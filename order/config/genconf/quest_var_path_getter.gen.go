// Code generated by tools/conf_gen/dynamic_conf_gen.go
package genconf

import (
	"fmt"
	"strings"

	"github.com/epifi/be-common/api/pkg/web"
	pkgweb "github.com/epifi/be-common/pkg/web"
)

// GetQuestVariableValue returns th value for a variable path relative to the Config object.
func (obj *Config) GetQuestVariableValue(questVariablePath []string) (val *web.DataValue, err error) {
	if len(questVariablePath) == 0 {
		return nil, fmt.Errorf("incomplete variable path")
	}
	switch questVariablePath[0] {
	case "IsEmailNotificationEnable":
		if len(questVariablePath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEmailNotificationEnable\"", strings.Join(questVariablePath[1:], "."))
		}
		return pkgweb.GetDataValue(obj.isEmailNotificationEnable())
	default:
		return nil, fmt.E<PERSON>rf("invalid quest variable path %q for Config", strings.Join(questVariablePath, "."))
	}
}
