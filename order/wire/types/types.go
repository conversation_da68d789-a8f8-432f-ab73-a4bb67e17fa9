package types

import (
	"github.com/redis/go-redis/v9"

	"github.com/epifi/gamma/api/comms"

	"github.com/epifi/be-common/pkg/queue"
)

// naming convention : cluster_name + db_number + "RedisStore"

type VendorMapping5RedisStore *redis.Client
type SavingsLedgerRedisStore *redis.Client
type DelayQueue1RedisStore *redis.Client
type B2CPaymentLockRedisStore *redis.Client
type SavingsLedgerReconPublisher queue.Publisher
type TxnDetailedStatusUpdateSnsPublisher queue.Publisher

type OrderCommsClientWithInterceptors comms.CommsClient

func CommsClientProvider(cl OrderCommsClientWithInterceptors) comms.CommsClient {
	return cl
}

// EnableResourceProvider is an alias for bool to signify switching on/off entity segregation
type EnableResourceProvider bool
