//go:build wireinject
// +build wireinject

//go:generate wire
package wire

import (
	"context"
	"errors"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"

	celestialPb "github.com/epifi/be-common/api/celestial"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	sqspkg "github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/pgp"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/faas"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	vendorapiGenConf "github.com/epifi/be-common/pkg/vendorapi/config/genconf"

	epifitemporalfaas "github.com/epifi/be-common/pkg/epifitemporal/faas"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/accounts/statement"
	"github.com/epifi/gamma/api/actor"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	locationPb "github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/bankcust"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	cardProvPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/categorizer"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	disputePb "github.com/epifi/gamma/api/cx/dispute"
	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/health_engine"
	hePb "github.com/epifi/gamma/api/health_engine"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/merchant"
	mPb "github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	aaPb "github.com/epifi/gamma/api/order/aa"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	parserPb "github.com/epifi/gamma/api/parser"
	"github.com/epifi/gamma/api/pay"
	beneficiaryManagementPb "github.com/epifi/gamma/api/pay/beneficiarymanagement"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	payIncidentManagerConsumer "github.com/epifi/gamma/api/pay/payincidentmanager"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	rpPb "github.com/epifi/gamma/api/recurringpayment"
	enachPb "github.com/epifi/gamma/api/recurringpayment/enach"
	savingsPb "github.com/epifi/gamma/api/savings"
	tieringPb "github.com/epifi/gamma/api/tiering"
	timelinePb "github.com/epifi/gamma/api/timeline"
	upiPb "github.com/epifi/gamma/api/upi"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/api/user"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	vgUpiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/order"
	"github.com/epifi/gamma/order/aa"
	aaConsumerPb "github.com/epifi/gamma/order/aa/consumer"
	orderActivity "github.com/epifi/gamma/order/activity"
	"github.com/epifi/gamma/order/actoractivity"
	payCampaign "github.com/epifi/gamma/order/campaign"
	staticconfig "github.com/epifi/gamma/order/config"
	config "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/consumer"
	"github.com/epifi/gamma/order/cx"
	"github.com/epifi/gamma/order/dao"
	"github.com/epifi/gamma/order/developer"
	"github.com/epifi/gamma/order/developer/processor"
	"github.com/epifi/gamma/order/internal"
	aaProcessor "github.com/epifi/gamma/order/internal/aa"
	connectedAccProcessor "github.com/epifi/gamma/order/internal/aa/connectedaccount"
	actorProcessor "github.com/epifi/gamma/order/internal/actor"
	heProcessor "github.com/epifi/gamma/order/internal/health_engine"
	inboundNotifProcessor "github.com/epifi/gamma/order/internal/inbound_notification"
	merchantProcessor "github.com/epifi/gamma/order/internal/merchant"
	parserProcessor "github.com/epifi/gamma/order/internal/parser"
	paymentProcessor "github.com/epifi/gamma/order/internal/payment"
	piProcessor "github.com/epifi/gamma/order/internal/pi"
	savingsProcessor "github.com/epifi/gamma/order/internal/savings"
	timelineProcessor "github.com/epifi/gamma/order/internal/timeline"
	upiProcessor "github.com/epifi/gamma/order/internal/upi"
	userProcessor "github.com/epifi/gamma/order/internal/user"
	"github.com/epifi/gamma/order/notification"
	"github.com/epifi/gamma/order/payment"
	"github.com/epifi/gamma/order/payment/business"
	paymentConsumer "github.com/epifi/gamma/order/payment/consumer"
	"github.com/epifi/gamma/order/payment/decision_engine"
	disputeConsumer "github.com/epifi/gamma/order/payment/disputes/consumer"
	"github.com/epifi/gamma/order/recon"
	reconConsumer "github.com/epifi/gamma/order/recon/consumer"
	orderTypes "github.com/epifi/gamma/order/types"
	types2 "github.com/epifi/gamma/order/wire/types"
	"github.com/epifi/gamma/order/workflow"
	payworkergenconfig "github.com/epifi/gamma/pay/config/worker/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	rewardsDeeplinkPkg "github.com/epifi/gamma/pkg/rewards/deeplink"
	awsSftp "github.com/epifi/gamma/pkg/sftp/aws/sftp"
	vendorapiPkgWire "github.com/epifi/gamma/pkg/vendorapi/inhouse/wire"
	vgtypes "github.com/epifi/gamma/vendorgateway/wire/types"
)

func envProvider(conf *config.Config) string {
	return conf.Application().Environment
}

func provideSavingLedgerRedisClient(store2 types2.SavingsLedgerRedisStore) *redis.Client {
	return store2
}

func provideDelayQueueDb0RedisClient(store types2.DelayQueue1RedisStore) *redis.Client {
	return store
}

func provideWorkflowProcessingDelayPublisher(publisher orderTypes.WorkflowProcessingDelayPublisher) queue.DelayPublisher {
	return publisher
}

func provideOrderEventPublisher2(publisher2 orderTypes.OrderEventPublisher2) queue.Publisher {
	return publisher2
}

func provideOrderOrchestrationPublisher(publisher orderTypes.OrderOrchestrationPublisher) orderTypes.OrderOrchestrationPublisher {
	return publisher
}

func provideAaTxnPublisher(publisher orderTypes.AATxnPublisher) queue.Publisher {
	return publisher
}

func providePaymentOrchestrationPublisher(publisher orderTypes.PaymentOrchestrationPublisher) queue.DelayPublisher {
	return publisher
}

func provideB2cPaymentLockRedisClient(client types2.B2CPaymentLockRedisStore) *redis.Client {
	return client
}

func provideInPaymentOrderPublisher(publisher orderTypes.InPaymentOrderUpdatePublisher) queue.Publisher {
	return publisher
}

func provideTxnDetailedStatusUpdateSnsPublisher(publisher types2.TxnDetailedStatusUpdateSnsPublisher) types2.TxnDetailedStatusUpdateSnsPublisher {
	return publisher
}

func convertPaymentProtocolPublishersToMap(intraBankEnquiryPublisher orderTypes.IntraBankEnquiryPublisher, impsEnquiryPublisher orderTypes.IMPSEnquiryPublisher, neftEnquiryPublisher orderTypes.NEFTEnquiryPublisher, rtgsEnquiryPublisher orderTypes.RTGSEnquiryPublisher,
	upiEnquiryPublisher orderTypes.UPIEnquiryPublisher) map[paymentPb.PaymentProtocol]queue.DelayPublisher {
	return map[paymentPb.PaymentProtocol]queue.DelayPublisher{
		paymentPb.PaymentProtocol_INTRA_BANK: intraBankEnquiryPublisher,
		paymentPb.PaymentProtocol_IMPS:       impsEnquiryPublisher,
		paymentPb.PaymentProtocol_NEFT:       neftEnquiryPublisher,
		paymentPb.PaymentProtocol_RTGS:       rtgsEnquiryPublisher,
		paymentPb.PaymentProtocol_UPI:        upiEnquiryPublisher,
	}
}

func featureFlagConfProvider(conf *config.Config) *config.FeatureFlags {
	return conf.FeatureFlags()
}

func orderCacheStorageProvider(orderRedisStore types.OrderRedisStore) orderTypes.OrderCacheStorage {
	return cache.NewRedisCacheStorage(orderRedisStore)
}

func orderCacheConfigProvider(config *config.Config) *config.OrderCacheConfig {
	return config.OrderCacheConfig()
}

func transactionCacheStorageProvider(orderRedisStore types.OrderRedisStore) orderTypes.TransactionCacheStorage {
	return cache.NewRedisCacheStorage(orderRedisStore)
}

func transactionCacheConfigProvider(config *config.Config) *config.TransactionCacheConfig {
	return config.TransactionCacheConfig()
}

func InitializeService(
	conf *config.Config,
	vendorapiGenConf *vendorapiGenConf.Config,
	db types.EpifiCRDB,
	wealthDb types.EpifiWealthCRDB,
	piClient piPb.PiClient,
	actorClient actorPb.ActorClient,
	authClient authPb.AuthClient,
	timelineClient timelinePb.TimelineServiceClient,
	depositClient depositPb.DepositClient,
	disputeClient disputePb.DisputeClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
	workflowProcessingPublisher orderTypes.WorkflowProcessingPublisher,
	workflowProcessingDelayPublisher orderTypes.WorkflowProcessingDelayPublisher,
	orderUpdateNotificationPublisher orderTypes.OrderUpdateEventPublisher,
	orderMerchantMergeEventPublisher orderTypes.OrderMerchantMergeEventPublisher,
	broker events.Broker,
	kycClient kycPb.KycClient,
	savingsClient savingsPb.SavingsClient,
	userClient userPb.UsersClient,
	userGrpClient userGroupPb.GroupClient,
	commsClient types2.OrderCommsClientWithInterceptors,
	nameCheckClient vgtypes.UNNameCheckClientWithInterceptors,
	merchantClient merchant.MerchantServiceClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	upiClient upiPb.UPIClient,
	tncPublisher orderTypes.EventsCompletedTnCPublisher,
	txnNotifPublisher orderTypes.TxnNotificationPublisher,
	orderOrchestrationPublisher orderTypes.OrderOrchestrationPublisher,
	orderNotificationPublisher orderTypes.OrderNotificationPublisher,
	orderSearchPublisher orderTypes.OrderSearchPublisher,
	p2pInvestmentClient p2pPb.P2PInvestmentClient,
	orderVpaVerificationPublisher orderTypes.OrderVpaVerificationPublisher,
	vgPaymentClient vgtypes.VgPaymentClientWithInterceptors,
	celestialClient celestialPb.CelestialClient,
	iftClient internationalfundtransfer.InternationalFundTransferClient,
	merchantResolutionVgClient vgtypes.MerchantResolutionClientWithInterceptors,
	bcClient bankCustPb.BankCustomerServiceClient,
	cardProvClient cardProvPb.CardProvisioningClient,
	txnCategorizerClient categorizer.TxnCategorizerClient,
	parserClient parserPb.ParserClient,
	recurringPaymentSvcClient rpPb.RecurringPaymentServiceClient,
	enachSvcClient enachPb.EnachServiceClient,
	txnDetailedStatusUpdateSnsPublisher types2.TxnDetailedStatusUpdateSnsPublisher,
	orderRedisStore types.OrderRedisStore,
	vgUpiClient vgUpiPb.UPIClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	payClient pay.PayClient,
) *order.Service {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		vgtypes.UNNameCheckClientProvider,
		vgtypes.VgPaymentClientProvider,
		vgtypes.MerchantResoulutionClientProvider,
		types2.CommsClientProvider,
		order.NewService,
		idgen.NewClock,
		idgen.WireSet,
		workflow.NewEngine,
		userProcessor.WireSet,
		timelineProcessor.WireSet,
		piProcessor.WireSet,
		featureFlagConfProvider,
		release.EvaluatorWireSet,
		dao.WireSet,
		internal.WireSet,
		upiProcessor.WireSet,
		inboundNotifProcessor.WireSet,
		savingsProcessor.WireSet,
		merchantProcessor.WireSet,
		wire.Bind(new(workflow.Engine), new(*workflow.OrderEngine)),
		provideWorkflowProcessingDelayPublisher,
		ReleaseConfigProvider,
		orderCacheStorageProvider,
		orderCacheConfigProvider,
		transactionCacheStorageProvider,
		transactionCacheConfigProvider,
		rewardsDeeplinkPkg.RewardsDeeplinkHelperWireSet,
		vendorapiPkgWire.MerchantNameCategoriserPkgWireSet,
		envProvider,
	)
	return &order.Service{}
}

func ReleaseConfigProvider(conf *config.Config) *releaseConfig.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func InitializeConsumerService(
	ctx context.Context,
	db types.EpifiCRDB,
	paymentClient paymentPb.PaymentClient,
	actorClient actorPb.ActorClient,
	timelineClient timelinePb.TimelineServiceClient,
	savingsClient savingsPb.SavingsClient,
	accountPiRelationClient accountPiPb.AccountPIRelationClient,
	piClient piPb.PiClient,
	depositClient depositPb.DepositClient,
	tncEventPublisher orderTypes.EventsCompletedTnCPublisher,
	upiClient upiPb.UPIClient,
	authClient authPb.AuthClient,
	eventBroker events.Broker,
	workflowProcessingPublisher orderTypes.WorkflowProcessingPublisher,
	workflowProcessingDelayPublisher orderTypes.WorkflowProcessingDelayPublisher,
	orderOrchestrationPublisher orderTypes.OrderOrchestrationPublisher,
	orderUpdateNotificationPublisher orderTypes.OrderUpdateEventPublisher,
	orderNotificationPublisher orderTypes.OrderNotificationPublisher,
	orderCollectNotificationPublisher orderTypes.OrderCollectNotificationPublisher,
	orderNotificationFallbackPublisher orderTypes.OrderNotificationFallbackPublisher,
	orderSearchPublisher orderTypes.OrderSearchPublisher,
	merchantClient merchant.MerchantServiceClient,
	p2pInvestmentClient p2pPb.P2PInvestmentClient,
	conf *config.Config,
	vgPaymentClient vgtypes.VgPaymentClientWithInterceptors,
	userClient userPb.UsersClient,
	userGrpClient userGroupPb.GroupClient,
	orderVpaVerificationPublisher orderTypes.OrderVpaVerificationPublisher,
	txnNotificationPublisher orderTypes.TxnNotificationPublisher,
	awsConf aws.Config,
	celestialClient celestialPb.CelestialClient,
	iftClient internationalfundtransfer.InternationalFundTransferClient,
	merchantResolutionVgClient vgtypes.MerchantResolutionClientWithInterceptors,
	paySavingsBalanceClient accountBalancePb.BalanceClient,
	parserClient parserPb.ParserClient,
	recurringPaymentSvcClient rpPb.RecurringPaymentServiceClient,
	enachSvcClient enachPb.EnachServiceClient,
	upiOnbClient upiOnboardingPb.UpiOnboardingClient,
	txnDetailedStatusUpdateSnsPublisher types2.TxnDetailedStatusUpdateSnsPublisher,
	orderRedisStore types.OrderRedisStore,
	payClient pay.PayClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	vgUPIClient vgUpiPb.UPIClient,
	vendorapiGenConf *vendorapiGenConf.Config,
) (*consumer.Service, error) {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		vgtypes.VgPaymentClientProvider,
		vgtypes.MerchantResoulutionClientProvider,
		consumer.NewService,
		idgen.NewClock,
		idgen.WireSet,
		workflow.NewEngine,
		dao.WireSet,
		upiProcessor.WireSet,
		piProcessor.WireSet,
		internal.WireSet,
		release.EvaluatorWireSet,
		timelineProcessor.WireSet,
		inboundNotifProcessor.WireSet,
		savingsProcessor.WireSet,
		awsSftp.AwsSftpWireSet,
		merchantProcessor.WireSet,
		wire.Bind(new(workflow.Engine), new(*workflow.OrderEngine)),
		provideWorkflowProcessingDelayPublisher,
		PreApprovedLoansS3ClientProvider,
		ReleaseConfigProvider,
		InitCryptors,
		PgpCryptoProvider,
		FaasExecutorProvider,
		orderCacheStorageProvider,
		orderCacheConfigProvider,
		transactionCacheStorageProvider,
		transactionCacheConfigProvider,
		vendorapiPkgWire.MerchantNameCategoriserPkgWireSet,
		envProvider,
	)
	return &consumer.Service{}, nil
}

func PreApprovedLoansS3ClientProvider(awsConf aws.Config, orderConf *config.Config) s3.S3Client {
	s3Client := s3.NewClient(awsConf, orderConf.DeclineDataAwsSftpBucket().AwsBucket)
	return s3Client
}

func PgpCryptoProvider(cryptorStoreMap *cryptormap.InMemoryCryptorStore) (crypto.Cryptor, error) {
	return cryptorStoreMap.GetCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_PGP)
}

// Method used for instantiating Cryptors for encrypted data from vendor.
// These cryptors and then used to create an in-memory vendor-cryptor mapping in a Singleton fashion.
func InitCryptors(conf *config.Config) (*cryptormap.InMemoryCryptorStore, error) {
	// create a pgp cryptor to be used for vendor communication encryption
	pgpCryptor := pgp.New(conf.Secrets().Ids[staticconfig.FederalPgpPublicKey],
		conf.Secrets().Ids[staticconfig.EpifiFederalPgpPrivateKey], conf.Secrets().Ids[staticconfig.EpifiFederalPgpPassphrase])
	if pgpCryptor == nil {
		return nil, errors.New("failed to create PGP cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(commonvgpb.Vendor_FEDERAL_BANK, commonvgpb.CryptorType_PGP, pgpCryptor)

	return cryptorStore, nil
}

func FaasExecutorProvider(ctx context.Context, awsConf aws.Config, conf *config.Config) (faas.FaaSExecutor, error) {
	return epifitemporalfaas.NewFaaSExecutor(ctx, sqspkg.InitSQSClient(awsConf), namespace.Pay, conf.ProcrastinatorWorkflowPublisher())
}

func InitializePaymentService(
	db types.EpifiCRDB,
	vgPaymentClient vgtypes.VgPaymentClientWithInterceptors,
	piClient piPb.PiClient,
	savingsClient savingsPb.SavingsClient,
	authClient authPb.AuthClient,
	actorClient actorPb.ActorClient,
	userClient userPb.UsersClient,
	vgUPIClient vgtypes.VgUpiClientWithInterceptors,
	intraBankEnquiryPublisher orderTypes.IntraBankEnquiryPublisher,
	impsEnquiryPublisher orderTypes.IMPSEnquiryPublisher,
	neftEnquiryPublisher orderTypes.NEFTEnquiryPublisher,
	rtgsEnquiryPublisher orderTypes.RTGSEnquiryPublisher,
	upiEnquiryPublisher orderTypes.UPIEnquiryPublisher,
	orderOrchestrationPublisher orderTypes.OrderOrchestrationPublisher,
	paymentOrchestrationPublisher orderTypes.PaymentOrchestrationPublisher,
	broker events.Broker,
	conf *config.Config,
	commsClient types2.OrderCommsClientWithInterceptors,
	redisClient types2.DelayQueue1RedisStore,
	client paymentPb.ConsumerClient,
	accountPiRelationClient accountPiPb.AccountPIRelationClient,
	upiClient upiPb.UPIClient,
	groupClient userGroupPb.GroupClient,
	inPaymentOrderPublisher orderTypes.InPaymentOrderUpdatePublisher,
	merchantClient merchant.MerchantServiceClient,
	merchantResolutionVgClient vgtypes.MerchantResolutionClientWithInterceptors,
	serviceClient bankcust.BankCustomerServiceClient,
	healthEngineClient health_engine.HealthEngineServiceClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	celestialClient celestialPb.CelestialClient,
	txnDetailedStatusUpdateSnsPublisher types2.TxnDetailedStatusUpdateSnsPublisher,
	orderRedisStore types.OrderRedisStore,
	locationClient locationPb.LocationClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	timelineClient timelinePb.TimelineServiceClient,
	payClient pay.PayClient,
) *payment.Service {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		vgtypes.VgPaymentClientProvider,
		vgtypes.MerchantResoulutionClientProvider,
		vgtypes.VgUpiClientProvider,
		types2.CommsClientProvider,
		release.EvaluatorWireSet,
		upiProcessor.WireSet,
		actorProcessor.WireSet,
		payment.NewService,
		lock.DefaultLockMangerWireSet,
		idgen.NewCryptoSeededSource,
		idgen.WireSet,
		dao.WireSet,
		piProcessor.WireSet,
		heProcessor.WireSet,
		paymentProcessor.WireSet,
		savingsProcessor.WireSet,
		provideDelayQueueDb0RedisClient,
		providePaymentOrchestrationPublisher,
		convertPaymentProtocolPublishersToMap,
		FeatureReleaseConfigProvider,
		orderCacheStorageProvider,
		orderCacheConfigProvider,
		transactionCacheStorageProvider,
		transactionCacheConfigProvider,
	)
	return &payment.Service{}
}

func FeatureReleaseConfigProvider(conf *config.Config) *releaseConfig.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func InitializePaymentDecisionService(
	actorClient actorPb.ActorClient,
	piClient piPb.PiClient,
	accountPiRelationClient accountPiPb.AccountPIRelationClient,
	upiClient upiPb.UPIClient, userClient userPb.UsersClient,
	userGrpClient userGroupPb.GroupClient,
	authClient authPb.AuthClient,
	orderClient orderPb.OrderServiceClient,
	paymentClient paymentPb.PaymentClient,
	orderConfig *config.Config,
	payClient pay.PayClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	savingClient savingsPb.SavingsClient,
	heClient health_engine.HealthEngineServiceClient,
	beneficiaryManagementClient beneficiaryManagementPb.BeneficiaryManagementClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	operationalStatusClient operationalStatusPb.OperationalStatusServiceClient,
) *decision_engine.Service {
	wire.Build(
		userProcessor.WireSet,
		decision_engine.NewService,
		PaymentProtocolDecisionsParamsProvider,
		RuleEngineParamsProvider,
		CustomRuleDEParamsProvider,
		CoolOffWindowProvider,
		release.EvaluatorWireSet,
		ReleaseConfigProvider,
	)
	return &decision_engine.Service{}
}

func PaymentProtocolDecisionsParamsProvider(config *config.Config) *config.PaymentProtocolDecisionParams {
	return config.PaymentProtocolDecisionParams()
}

func RuleEngineParamsProvider(config *config.Config) *config.RuleEngineParams {
	return config.RuleEngineParams()
}

func CustomRuleDEParamsProvider(config *config.Config) *config.CustomRuleDEParams {
	return config.CustomRuleDEParams()
}

func CoolOffWindowProvider(config *config.Config) *cfg.TimeDuration {
	return config.CoolOffWindow()
}

func InitializePaymentConsumerService(
	db types.EpifiCRDB,
	vgPaymentClient vgtypes.VgPaymentClientWithInterceptors,
	authClient authPb.AuthClient,
	orderOrchestrationPublisher orderTypes.OrderOrchestrationPublisher,
	vgUPIClient vgtypes.VgUpiClientWithInterceptors,
	broker events.Broker,
	orderConf *config.Config,
	piClient piPb.PiClient,
	orderClient orderPb.OrderServiceClient,
	upiClient upiPb.UPIClient,
	actorClient actorPb.ActorClient,
	usersClient userPb.UsersClient,
	groupClient userGroupPb.GroupClient,
	deemedTransactionUPIEnquiryPublisher orderTypes.DeemedTransactionUPIEnquiryPublisher,
	healthEngineClient health_engine.HealthEngineServiceClient,
	accountPiRelationClient accountPiPb.AccountPIRelationClient,
	merchantClient merchant.MerchantServiceClient,
	savingsClient savingsPb.SavingsClient,
	merchantResolutionVgClient vgtypes.MerchantResolutionClientWithInterceptors,
	orderUpdatePublisher orderTypes.OrderUpdateEventPublisher,
	payIncidentManagerClient payIncidentManagerConsumer.PayIncidentManagerClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	txnDetailedStatusUpdateSnsPublisher types2.TxnDetailedStatusUpdateSnsPublisher,
	orderRedisStore types.OrderRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
) *paymentConsumer.Service {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		vgtypes.VgPaymentClientProvider,
		vgtypes.MerchantResoulutionClientProvider,
		vgtypes.VgUpiClientProvider,
		release.EvaluatorWireSet,
		upiProcessor.WireSet,
		heProcessor.WireSet,
		savingsProcessor.WireSet,
		piProcessor.WireSet,
		paymentProcessor.WireSet,
		paymentConsumer.NewService,
		idgen.NewClock,
		idgen.WireSet,
		dao.WireSet,
		FeatureReleaseConfigProvider,
		transactionCacheStorageProvider,
		transactionCacheConfigProvider,
	)
	return &paymentConsumer.Service{}
}

func InitializeCallbackConsumer(
	db types.EpifiCRDB,
	orderOrchestrationPublisher orderTypes.OrderOrchestrationPublisher,
	piClient piPb.PiClient,
	broker events.Broker,
	conf *config.Config,
	celestialClient celestialPb.CelestialClient,
	txnDetailedStatusUpdateSnsPublisher types2.TxnDetailedStatusUpdateSnsPublisher,
	healthEngineClient health_engine.HealthEngineServiceClient,
	accountPiRelationClient accountPiPb.AccountPIRelationClient,
	merchantClient mPb.MerchantServiceClient,
	vgPaymentClient vgtypes.VgPaymentClientWithInterceptors,
	userGroupClient userGroupPb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	merchantResolutionClient vgtypes.MerchantResolutionClientWithInterceptors,
	savingsClient savingsPb.SavingsClient,
	upiClient upiPb.UPIClient,
	authClient authPb.AuthClient,
	upiOnbClient upiOnbPb.UpiOnboardingClient,
	orderRedisStore types.OrderRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	vgUPIClient vgUpiPb.UPIClient,
) *paymentConsumer.CallbackService {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		vgtypes.MerchantResoulutionClientProvider,
		vgtypes.VgPaymentClientProvider,
		paymentConsumer.NewCallbackService,
		idgen.NewClock,
		idgen.WireSet,
		internal.WireSet,
		heProcessor.WireSet,
		piProcessor.WireSet,
		upiProcessor.WireSet,
		savingsProcessor.WireSet,
		paymentProcessor.WireSet,
		release.EvaluatorWireSet,
		ReleaseConfigProvider,
		dao.WireSet,
		orderCacheStorageProvider,
		orderCacheConfigProvider,
		transactionCacheStorageProvider,
		transactionCacheConfigProvider,
	)
	return &paymentConsumer.CallbackService{}
}

func InitializeNotificationConsumer(
	conf *config.Config,
	db types.EpifiCRDB,
	commsClient types2.OrderCommsClientWithInterceptors,
	actorClient actorPb.ActorClient,
	piClient piPb.PiClient,
	timelineClient timelinePb.TimelineServiceClient,
	depositClient depositPb.DepositClient,
	savingsClient savingsPb.SavingsClient,
	accountPiRelationClient accountPiPb.AccountPIRelationClient,
	userClient userPb.UsersClient,
	userGrpClient userGroupPb.GroupClient,
	orderCollectNotificationPublisher orderTypes.OrderCollectNotificationPublisher,
	orderNotificationFallbackPublisher orderTypes.OrderNotificationFallbackPublisher,
	recurringPaymentClient rpPb.RecurringPaymentServiceClient,
	paySavingsBalanceClient accountBalancePb.BalanceClient,
	upiClient upiPb.UPIClient,
	authClient authPb.AuthClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	orderRedisStore types.OrderRedisStore,
	cpClient cardProvPb.CardProvisioningClient,
	tieringClient tieringPb.TieringClient,
	locationClient locationPb.LocationClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	vgUPIClient vgUpiPb.UPIClient,
) *notification.Service {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		types2.CommsClientProvider,
		idgen.NewClock,
		idgen.WireSet,
		dao.WireSet,
		userProcessor.WireSet,
		notification.NewService,
		actorProcessor.WireSet,
		upiProcessor.WireSet,
		release.EvaluatorWireSet,
		ReleaseConfigProvider,
		orderCacheStorageProvider,
		orderCacheConfigProvider,
	)
	return &notification.Service{}
}

func InitializeActorActivityService(
	orderClient orderPb.OrderServiceClient,
	actorClient actorPb.ActorClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	piClient piPb.PiClient,
	depositClient depositPb.DepositClient,
	conf *config.Config,
	timelineClient timelinePb.TimelineServiceClient,
	client aaPb.AccountAggregatorClient,
	userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	caClient connectedAccountPb.ConnectedAccountClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
) *actoractivity.Service {
	wire.Build(
		actoractivity.NewService,
		userProcessor.WireSet,
	)
	return &actoractivity.Service{}
}

func InitializeCxService(db types.EpifiCRDB,
	conf *config.Config,
	accPiClient accountPiPb.AccountPIRelationClient,
	rpClient rpPb.RecurringPaymentServiceClient,
	orderRedisStore types.OrderRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB]) *cx.Service {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		cx.NewService,
		dao.WireSet,
		idgen.NewClock,
		idgen.WireSet,
		orderCacheStorageProvider,
		orderCacheConfigProvider,
	)
	return &cx.Service{}
}

func InitializeDevService(
	db types.EpifiCRDB,
	wealthDB types.EpifiWealthCRDB,
	accPiClient accountPiPb.AccountPIRelationClient,
	savingsLedgerReconRedisClient types2.SavingsLedgerRedisStore,
	conf *config.Config,
	orderRedisStore types.OrderRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
) *developer.OrderDevService {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		dao.WireSet,
		idgen.NewClock,
		featureFlagConfProvider,
		idgen.WireSet,
		developer.NewOrderDevService,
		developer.NewDevFactory,
		processor.NewOrderDetailsProcessor,
		processor.NewTransactionProcessor,
		processor.NewSavingsLedgerReconProcessor,
		processor.NewOrderAttemptProcessor,
		processor.NewOrderWithTransactionsProcessor,
		processor.NewAaTransactionsProcessor,
		processor.NewTxnsForActorProcessor,
		processor.NewOrderMetadataProcessor,
		processor.NewAaDataPurgeRequestsProcessor,
		processor.NewOrderVendorOrderMapProcessor,
		processor.NewTransactionNotificationMapProcessor,
		provideSavingLedgerRedisClient,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		SavingsLedgerReconCacheConfigProvider,
		orderCacheStorageProvider,
		orderCacheConfigProvider,
		transactionCacheStorageProvider,
		transactionCacheConfigProvider,
	)
	return &developer.OrderDevService{}
}

func SavingsLedgerReconCacheConfigProvider(conf *config.Config) *config.SavingsLedgerReconCacheConfig {
	return conf.SavingsLedgerReconCacheConfig()
}

func InitializeReconService(
	conf *config.Config,
	db types.EpifiCRDB,
	reconPublisher types2.SavingsLedgerReconPublisher,
	userClient userPb.UsersClient,
	healthEngineClient hePb.HealthEngineServiceClient,
	userGrpClient userGroupPb.GroupClient,
	actorClient actorPb.ActorClient,
	broker events.Broker,
	savingsLedgerReconRedisClient types2.SavingsLedgerRedisStore,
	bcClient bankCustPb.BankCustomerServiceClient,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
) *recon.Service {
	wire.Build(
		dao.WireSet,
		userProcessor.WireSet,
		recon.NewService,
		provideSavingLedgerRedisClient,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		ReconPublisherProvider,
		CacheConfigProvider,
	)

	return &recon.Service{}
}

func ReconPublisherProvider(publisher types2.SavingsLedgerReconPublisher) queue.Publisher {
	return publisher
}

func CacheConfigProvider(conf *config.Config) *config.SavingsLedgerReconCacheConfig {
	return conf.SavingsLedgerReconCacheConfig()
}

func InitializeBusinessService(db types.EpifiCRDB, piClient piPb.PiClient, ovgB2CPaymentClient vgtypes.VgB2CPaymentClientWithInterceptors,
	savingsClient savingsPb.SavingsClient, orderConf *config.Config, b2cPaymentLockRedisStore types2.B2CPaymentLockRedisStore,
	txnDetailedStatusUpdateSnsPublisher types2.TxnDetailedStatusUpdateSnsPublisher, orderRedisStore types.OrderRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *business.Service {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		vgtypes.VgB2CPaymentClientProvider,
		dao.WireSet,
		idgen.WireSet,
		lock.DefaultLockMangerWireSet,
		business.NewService,
		ErrorRespCodesForPermanentFailureConfProvider,
		provideB2cPaymentLockRedisClient,
		transactionCacheStorageProvider,
		transactionCacheConfigProvider,
	)
	return &business.Service{}
}

func ErrorRespCodesForPermanentFailureConfProvider(conf *config.Config) *config.ErrorRespCodesForPermanentFailure {
	return conf.ErrorRespCodesForPermanentFailure()
}

func InitializeReconConsumerService(
	db types.EpifiCRDB,
	vgAccountClient vgtypes.VgAccountsClientWithInterceptors,
	actorClient actorPb.ActorClient,
	authClient authPb.AuthClient,
	timelineClient timelinePb.TimelineServiceClient,
	accountPiRelationClient accountPiPb.AccountPIRelationClient,
	piClient piPb.PiClient,
	depositClient depositPb.DepositClient,
	upiClient upiPb.UPIClient,
	eventBroker events.Broker,
	orderOrchestrationPublisher orderTypes.OrderOrchestrationPublisher,
	orderUpdateNotificationPublisher orderTypes.OrderUpdateEventPublisher,
	orderNotificationPublisher orderTypes.OrderNotificationPublisher,
	orderSearchPublisher orderTypes.OrderSearchPublisher,
	tncEventPublisher orderTypes.EventsCompletedTnCPublisher,
	merchantClient merchant.MerchantServiceClient,
	p2pInvestmentClient p2pPb.P2PInvestmentClient,
	conf *config.Config,
	savingsLedgerReconRedisClient types2.SavingsLedgerRedisStore,
	vgPaymentClient vgtypes.VgPaymentClientWithInterceptors,
	userClient userPb.UsersClient,
	userGrpClient userGroupPb.GroupClient,
	orderVpaVerificationPublisher orderTypes.OrderVpaVerificationPublisher,
	txnNotificationPublisher orderTypes.TxnNotificationPublisher,
	celestialClient celestialPb.CelestialClient,
	iftClient internationalfundtransfer.InternationalFundTransferClient,
	merchantResolutionVgClient vgtypes.MerchantResolutionClientWithInterceptors,
	accountStatementClient statement.AccountStatementClient,
	savingsClient savingsPb.SavingsClient,
	parserClient parserPb.ParserClient,
	recurringPaymentSvcClient rpPb.RecurringPaymentServiceClient,
	enachSvcClient enachPb.EnachServiceClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	txnDetailedStatusUpdateSnsPublisher types2.TxnDetailedStatusUpdateSnsPublisher,
	orderRedisStore types.OrderRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor],
	payClient pay.PayClient,
	vgUPIClient vgUpiPb.UPIClient,
	vendorapiGenConf *vendorapiGenConf.Config,
) *reconConsumer.Service {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		vgtypes.VgAccountsClientProvider,
		vgtypes.VgPaymentClientProvider,
		vgtypes.MerchantResoulutionClientProvider,
		dao.WireSet,
		idgen.NewClock,
		idgen.WireSet,
		upiProcessor.WireSet,
		savingsProcessor.WireSet,
		piProcessor.WireSet,
		internal.WireSet,
		timelineProcessor.WireSet,
		release.EvaluatorWireSet,
		inboundNotifProcessor.WireSet,
		merchantProcessor.WireSet,
		reconConsumer.NewService,
		provideSavingLedgerRedisClient,
		wire.NewSet(cache.NewRedisCacheStorage, wire.Bind(new(cache.CacheStorage), new(*cache.RedisCacheStorage))),
		SavingsLedgerReconCacheConfigProvider,
		ReleaseConfigProvider,
		orderCacheStorageProvider,
		orderCacheConfigProvider,
		transactionCacheStorageProvider,
		transactionCacheConfigProvider,
		vendorapiPkgWire.MerchantNameCategoriserPkgWireSet,
		envProvider,
	)
	return &reconConsumer.Service{}
}

func InitializeDisputedTxnConsumerService(
	db types.EpifiCRDB,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
) *disputeConsumer.Service {
	wire.Build(
		dao.WireSet,
		disputeConsumer.NewService,
	)
	return &disputeConsumer.Service{}
}

func InitializeCampaignService(
	conf *config.Config,
	db types.EpifiCRDB,
	onboardingClient onboarding.OnboardingClient,
	commsClient types2.OrderCommsClientWithInterceptors,
	orderRedisStore types.OrderRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
) *payCampaign.Service {
	wire.Build(
		OrderServerEntitySegregationFlagProvider,
		types2.CommsClientProvider,
		dao.WireSet,
		idgen.NewClock,
		idgen.WireSet,
		payCampaign.NewService,
		orderCacheStorageProvider,
		orderCacheConfigProvider,
	)
	return &payCampaign.Service{}
}

func InitializeAccountAggregatorService(
	conf *config.Config,
	db types.EpifiWealthCRDB,
	caClient connectedAccountPb.ConnectedAccountClient,
	client accountPiPb.AccountPIRelationClient,
) *aa.Service {
	wire.Build(
		dao.WireSet,
		idgen.NewClock,
		featureFlagConfProvider,
		idgen.WireSet,
		aa.NewService)
	return &aa.Service{}
}

func EpifiTxnExecutorProviderForAA(conf *config.Config, conn types.EpifiCRDB) types.EpifiTxnExecutor {
	if conf.FeatureFlags().EnablePGDBDaoForAA() {
		var gormDB *gorm.DB = conn
		return storagev2.NewGormTxnExecutor(gormDB.Clauses(dbresolver.Use(conf.AaParams().PgdbConnAlias())))
	}

	return storagev2.NewCRDBIdempotentTxnExecutor(conn)
}
func EpifiWealthTxnExecutorProviderForAA(conf *config.Config, conn types.EpifiWealthCRDB) types.EpifiWealthTxnExecutor {
	if conf.FeatureFlags().EnablePGDBDaoForAA() {
		var gormDB *gorm.DB = conn
		return storagev2.NewGormTxnExecutor(gormDB.Clauses(dbresolver.Use(conf.AaParams().PgdbConnAlias())))
	}

	return storagev2.NewCRDBIdempotentTxnExecutor(conn)
}

func InitializeAAConsumerService(
	conf *config.Config,
	epifiDb types.EpifiCRDB,
	epifiWealthDb types.EpifiWealthCRDB,
	aaTxnPublisher orderTypes.AATxnPublisher,
	authClient authPb.AuthClient,
	upiClient upiPb.UPIClient,
	piClient piPb.PiClient,
	merchantClient merchant.MerchantServiceClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	actorClient actorPb.ActorClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
	timelineClient timelinePb.TimelineServiceClient,
	parserClient vgtypes.VgParserClientWithInterceptors,
	aaAccountPiPurgePublisher orderTypes.AaAccountPiPurgePublisher,
	actorPiRelationPurgePublisher orderTypes.ActorPiRelationPurgePublisher,
	aaDataPurgingOrchestratorPublisher orderTypes.AaDataPurgeOrchestrationPublisher,
	redisClient types2.DelayQueue1RedisStore,
	vgPaymentClient vgtypes.VgPaymentClientWithInterceptors,
	userClient userPb.UsersClient,
	userGrpClient userGroupPb.GroupClient,
	savingsClient savingsPb.SavingsClient,
	merchantResolutionVgClient vgtypes.MerchantResolutionClientWithInterceptors,
	upiOnbClient upiOnboardingPb.UpiOnboardingClient,
	locationClient locationPb.LocationClient,
	vgUPIClient vgUpiPb.UPIClient,
	vendorapiGenConf *vendorapiGenConf.Config,
) *aaConsumerPb.Service {
	wire.Build(
		vgtypes.VgPaymentClientProvider,
		vgtypes.VgParserClientProvider,
		vgtypes.MerchantResoulutionClientProvider,
		EpifiWealthTxnExecutorProviderForAA,
		EpifiTxnExecutorProviderForAA,
		featureFlagConfProvider,
		dao.WireSet,
		idgen.WireSet,
		aaProcessor.WireSet,
		savingsProcessor.WireSet,
		piProcessor.WireSet,
		parserProcessor.WireSet,
		upiProcessor.WireSet,
		timelineProcessor.WireSet,
		connectedAccProcessor.WireSet,
		actorProcessor.WireSet,
		merchantProcessor.WireSet,
		lock.DefaultLockMangerWireSet,
		release.EvaluatorWireSet,
		provideDelayQueueDb0RedisClient,
		provideAaTxnPublisher,
		aaConsumerPb.NewService,
		ReleaseConfigProvider,
		vendorapiPkgWire.MerchantNameCategoriserPkgWireSet,
		envProvider,
	)
	return &aaConsumerPb.Service{}
}

func InitializeActivityProcessor(
	db types.EpifiCRDB,
	orderCacheConfig *config.OrderCacheConfig,
	orderEventPublisher orderTypes.OrderUpdateEventPublisher,
	piClient piPb.PiClient,
	orderRedisStore types.OrderRedisStore,
	dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB],
	enableResourceProvider types2.EnableResourceProvider,
) *orderActivity.Processor {
	wire.Build(
		idgen.NewClock,
		idgen.WireSet,
		dao.WireSet,
		orderActivity.NewProcessor,
		orderCacheStorageProvider,
	)

	return &orderActivity.Processor{}
}

func WorkerEntitySegregationFlagProvider(conf *payworkergenconfig.Config) types2.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in pay worker : %v", conf.EnableEntitySegregation()))
	return types2.EnableResourceProvider(conf.EnableEntitySegregation())
}

func OrderServerEntitySegregationFlagProvider(conf *config.Config) types2.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in order config : %v", conf.EnableEntitySegregation()))
	return types2.EnableResourceProvider(conf.EnableEntitySegregation())
}
