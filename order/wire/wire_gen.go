// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/crypto"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/crypto/pgp"
	faas2 "github.com/epifi/be-common/pkg/epifitemporal/faas"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/faas"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage/v2"
	genconf2 "github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/accounts/statement"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/cx/dispute"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/health_engine"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/merchant"
	order3 "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/aa"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/p2pinvestment"
	"github.com/epifi/gamma/api/parser"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/pay/beneficiarymanagement"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/pay/payincidentmanager"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/enach"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/timeline"
	"github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	onboarding2 "github.com/epifi/gamma/api/user/onboarding"
	upi2 "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/order"
	aa2 "github.com/epifi/gamma/order/aa"
	consumer5 "github.com/epifi/gamma/order/aa/consumer"
	"github.com/epifi/gamma/order/activity"
	"github.com/epifi/gamma/order/actoractivity"
	"github.com/epifi/gamma/order/campaign"
	"github.com/epifi/gamma/order/config"
	"github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/consumer"
	"github.com/epifi/gamma/order/cx"
	"github.com/epifi/gamma/order/dao"
	"github.com/epifi/gamma/order/developer"
	"github.com/epifi/gamma/order/developer/processor"
	aa3 "github.com/epifi/gamma/order/internal/aa"
	"github.com/epifi/gamma/order/internal/aa/connectedaccount"
	actor2 "github.com/epifi/gamma/order/internal/actor"
	celestial2 "github.com/epifi/gamma/order/internal/celestial"
	health_engine2 "github.com/epifi/gamma/order/internal/health_engine"
	"github.com/epifi/gamma/order/internal/inbound_notification"
	merchant2 "github.com/epifi/gamma/order/internal/merchant"
	order2 "github.com/epifi/gamma/order/internal/order"
	parser2 "github.com/epifi/gamma/order/internal/parser"
	payment3 "github.com/epifi/gamma/order/internal/payment"
	"github.com/epifi/gamma/order/internal/pi"
	savings2 "github.com/epifi/gamma/order/internal/savings"
	timeline2 "github.com/epifi/gamma/order/internal/timeline"
	upi3 "github.com/epifi/gamma/order/internal/upi"
	user2 "github.com/epifi/gamma/order/internal/user"
	"github.com/epifi/gamma/order/notification"
	payment2 "github.com/epifi/gamma/order/payment"
	"github.com/epifi/gamma/order/payment/business"
	consumer2 "github.com/epifi/gamma/order/payment/consumer"
	"github.com/epifi/gamma/order/payment/decision_engine"
	consumer4 "github.com/epifi/gamma/order/payment/disputes/consumer"
	"github.com/epifi/gamma/order/recon"
	consumer3 "github.com/epifi/gamma/order/recon/consumer"
	types2 "github.com/epifi/gamma/order/types"
	types3 "github.com/epifi/gamma/order/wire/types"
	"github.com/epifi/gamma/order/workflow"
	genconf4 "github.com/epifi/gamma/pay/config/worker/genconf"
	dao2 "github.com/epifi/gamma/pay/dao"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf3 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	"github.com/epifi/gamma/pkg/rewards/deeplink"
	"github.com/epifi/gamma/pkg/sftp/aws/sftp"
	"github.com/epifi/gamma/pkg/vendorapi/inhouse/wire"
	types4 "github.com/epifi/gamma/vendorgateway/wire/types"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

// Injectors from wire.go:

func InitializeService(conf *genconf.Config, vendorapiGenConf *genconf2.Config, db types.EpifiCRDB, wealthDb types.EpifiWealthCRDB, piClient paymentinstrument.PiClient, actorClient actor.ActorClient, authClient auth.AuthClient, timelineClient timeline.TimelineServiceClient, depositClient deposit.DepositClient, disputeClient dispute.DisputeClient, accountPiClient account_pi.AccountPIRelationClient, connectedAccountClient connected_account.ConnectedAccountClient, workflowProcessingPublisher types2.WorkflowProcessingPublisher, workflowProcessingDelayPublisher types2.WorkflowProcessingDelayPublisher, orderUpdateNotificationPublisher types2.OrderUpdateEventPublisher, orderMerchantMergeEventPublisher types2.OrderMerchantMergeEventPublisher, broker events.Broker, kycClient kyc.KycClient, savingsClient savings.SavingsClient, userClient user.UsersClient, userGrpClient group.GroupClient, commsClient types3.OrderCommsClientWithInterceptors, nameCheckClient types4.UNNameCheckClientWithInterceptors, merchantClient merchant.MerchantServiceClient, upiOnboardingClient onboarding.UpiOnboardingClient, upiClient upi.UPIClient, tncPublisher types2.EventsCompletedTnCPublisher, txnNotifPublisher types2.TxnNotificationPublisher, orderOrchestrationPublisher types2.OrderOrchestrationPublisher, orderNotificationPublisher types2.OrderNotificationPublisher, orderSearchPublisher types2.OrderSearchPublisher, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, orderVpaVerificationPublisher types2.OrderVpaVerificationPublisher, vgPaymentClient types4.VgPaymentClientWithInterceptors, celestialClient celestial.CelestialClient, iftClient internationalfundtransfer.InternationalFundTransferClient, merchantResolutionVgClient types4.MerchantResolutionClientWithInterceptors, bcClient bankcust.BankCustomerServiceClient, cardProvClient provisioning.CardProvisioningClient, txnCategorizerClient categorizer.TxnCategorizerClient, parserClient parser.ParserClient, recurringPaymentSvcClient recurringpayment.RecurringPaymentServiceClient, enachSvcClient enach.EnachServiceClient, txnDetailedStatusUpdateSnsPublisher types3.TxnDetailedStatusUpdateSnsPublisher, orderRedisStore types.OrderRedisStore, vgUpiClient upi2.UPIClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], payClient pay.PayClient) *order.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(conf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	orderCacheStorage := orderCacheStorageProvider(orderRedisStore)
	orderCacheConfig := orderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, orderCacheStorage, orderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, orderCacheConfig)
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	transactionDaoCRDB := dao.NewTransactionDao(db, crdbIdempotentTxnExecutor, domainIdGenerator, dbResourceProvider, txnExecutorResourceProvider, enableResourceProvider, attributedIdGen)
	transactionCacheStorage := transactionCacheStorageProvider(orderRedisStore)
	transactionCacheConfig := transactionCacheConfigProvider(conf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, transactionCacheStorage, transactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, transactionCacheConfig)
	featureFlags := featureFlagConfProvider(conf)
	aaTransactionDaoCRDB := dao.NewAATransactionDao(wealthDb, domainIdGenerator)
	aaTransactionDaoPgdb := dao.NewAATransactionDaoPgdb(conf, wealthDb, domainIdGenerator)
	aaTransactionDao := dao.AATransactionDaoProvider(featureFlags, aaTransactionDaoCRDB, aaTransactionDaoPgdb)
	disputedTransactionDaoCRDB := dao.NewDisputedTransactionDao(db)
	orderAttemptDaoCRDB := dao.NewOrderAttemptDao(db)
	delayPublisher := provideWorkflowProcessingDelayPublisher(workflowProcessingDelayPublisher)
	celestialProcessor := celestial2.NewCelestialProcessor(celestialClient, conf)
	orderEngine := workflow.NewEngine(orderDao, orderAttemptDaoCRDB, orderUpdateNotificationPublisher, workflowProcessingPublisher, delayPublisher, conf, db, celestialProcessor, piClient)
	processor := user2.NewProcessor(userClient, userGrpClient, actorClient)
	actorAddFundOptionsMapDaoCRDB := dao.NewAddFundOptionsActorMapDao(db)
	commsCommsClient := types3.CommsClientProvider(commsClient)
	transactionNotificationMapDaoCRDB := dao.NewTransactionNotificationMapDao(db)
	orderProcessor := order2.NewOrderProcessor(orderDao, orderUpdateNotificationPublisher, piClient)
	unNameCheckClient := types4.UNNameCheckClientProvider(nameCheckClient)
	orderMetadataDaoCrdb := dao.NewOrderMetadataDao(db)
	featureReleaseConfig := ReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	upiProcessor := upi3.NewProcessor(upiClient, authClient, actorClient, piClient, evaluator, upiOnboardingClient, accountPiClient, conf, userClient, vgUpiClient)
	timelineProcessor := timeline2.NewProcessor(actorClient, timelineClient)
	paymentClient := types4.VgPaymentClientProvider(vgPaymentClient)
	savingsProcessor := savings2.NewProcessor(savingsClient, accountPiClient)
	merchantResolutionClient := types4.MerchantResoulutionClientProvider(merchantResolutionVgClient)
	piProcessor := pi.NewProcessor(accountPiClient, piClient, merchantClient, upiProcessor, paymentClient, userGrpClient, userClient, actorClient, conf, savingsProcessor, merchantResolutionClient)
	string2 := envProvider(conf)
	service := wire.InitializeMerchantNameCategoriserService(vendorapiGenConf, string2)
	merchantProcessor := merchant2.NewProcessor(merchantClient, service)
	inbound_notificationProcessor := inbound_notification.NewProcessor(depositClient, transactionDao, orderDao, orderUpdateNotificationPublisher, tncPublisher, broker, timelineProcessor, piProcessor, upiProcessor, orderProcessor, orderOrchestrationPublisher, orderNotificationPublisher, orderSearchPublisher, merchantClient, p2pInvestmentClient, orderVpaVerificationPublisher, txnNotifPublisher, celestialProcessor, iftClient, merchantResolutionClient, parserClient, conf, evaluator, enachSvcClient, recurringPaymentSvcClient, txnDetailedStatusUpdateSnsPublisher, piClient, payClient, merchantProcessor)
	transactionAmountBreakupCRDB := dao.NewTransactionAmountBreakupDao(db)
	rewardsDeeplinkHelper := deeplink.NewRewardsDeeplinkHelper(cardProvClient)
	orderService := order.NewService(conf, orderDao, transactionDao, aaTransactionDao, disputedTransactionDaoCRDB, orderEngine, orderUpdateNotificationPublisher, orderMerchantMergeEventPublisher, piClient, actorClient, workflowProcessingPublisher, delayPublisher, authClient, timelineClient, depositClient, disputeClient, accountPiClient, connectedAccountClient, broker, kycClient, savingsClient, processor, userClient, actorAddFundOptionsMapDaoCRDB, commsCommsClient, transactionNotificationMapDaoCRDB, orderProcessor, unNameCheckClient, merchantClient, orderMetadataDaoCrdb, upiOnboardingClient, userGrpClient, upiProcessor, inbound_notificationProcessor, merchantResolutionClient, evaluator, transactionAmountBreakupCRDB, bcClient, cardProvClient, txnCategorizerClient, txnDetailedStatusUpdateSnsPublisher, upiClient, vgUpiClient, rewardsDeeplinkHelper)
	return orderService
}

func InitializeConsumerService(ctx context.Context, db types.EpifiCRDB, paymentClient payment.PaymentClient, actorClient actor.ActorClient, timelineClient timeline.TimelineServiceClient, savingsClient savings.SavingsClient, accountPiRelationClient account_pi.AccountPIRelationClient, piClient paymentinstrument.PiClient, depositClient deposit.DepositClient, tncEventPublisher types2.EventsCompletedTnCPublisher, upiClient upi.UPIClient, authClient auth.AuthClient, eventBroker events.Broker, workflowProcessingPublisher types2.WorkflowProcessingPublisher, workflowProcessingDelayPublisher types2.WorkflowProcessingDelayPublisher, orderOrchestrationPublisher types2.OrderOrchestrationPublisher, orderUpdateNotificationPublisher types2.OrderUpdateEventPublisher, orderNotificationPublisher types2.OrderNotificationPublisher, orderCollectNotificationPublisher types2.OrderCollectNotificationPublisher, orderNotificationFallbackPublisher types2.OrderNotificationFallbackPublisher, orderSearchPublisher types2.OrderSearchPublisher, merchantClient merchant.MerchantServiceClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, conf *genconf.Config, vgPaymentClient types4.VgPaymentClientWithInterceptors, userClient user.UsersClient, userGrpClient group.GroupClient, orderVpaVerificationPublisher types2.OrderVpaVerificationPublisher, txnNotificationPublisher types2.TxnNotificationPublisher, awsConf aws.Config, celestialClient celestial.CelestialClient, iftClient internationalfundtransfer.InternationalFundTransferClient, merchantResolutionVgClient types4.MerchantResolutionClientWithInterceptors, paySavingsBalanceClient balance.BalanceClient, parserClient parser.ParserClient, recurringPaymentSvcClient recurringpayment.RecurringPaymentServiceClient, enachSvcClient enach.EnachServiceClient, upiOnbClient onboarding.UpiOnboardingClient, txnDetailedStatusUpdateSnsPublisher types3.TxnDetailedStatusUpdateSnsPublisher, orderRedisStore types.OrderRedisStore, payClient pay.PayClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], vgUPIClient upi2.UPIClient, vendorapiGenConf *genconf2.Config) (*consumer.Service, error) {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(conf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	orderCacheStorage := orderCacheStorageProvider(orderRedisStore)
	orderCacheConfig := orderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, orderCacheStorage, orderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, orderCacheConfig)
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	transactionDaoCRDB := dao.NewTransactionDao(db, crdbIdempotentTxnExecutor, domainIdGenerator, dbResourceProvider, txnExecutorResourceProvider, enableResourceProvider, attributedIdGen)
	transactionCacheStorage := transactionCacheStorageProvider(orderRedisStore)
	transactionCacheConfig := transactionCacheConfigProvider(conf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, transactionCacheStorage, transactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, transactionCacheConfig)
	orderAttemptDaoCRDB := dao.NewOrderAttemptDao(db)
	delayPublisher := provideWorkflowProcessingDelayPublisher(workflowProcessingDelayPublisher)
	celestialProcessor := celestial2.NewCelestialProcessor(celestialClient, conf)
	orderEngine := workflow.NewEngine(orderDao, orderAttemptDaoCRDB, orderUpdateNotificationPublisher, workflowProcessingPublisher, delayPublisher, conf, db, celestialProcessor, piClient)
	processor := timeline2.NewProcessor(actorClient, timelineClient)
	featureReleaseConfig := ReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	upiProcessor := upi3.NewProcessor(upiClient, authClient, actorClient, piClient, evaluator, upiOnbClient, accountPiRelationClient, conf, userClient, vgUPIClient)
	paymentPaymentClient := types4.VgPaymentClientProvider(vgPaymentClient)
	savingsProcessor := savings2.NewProcessor(savingsClient, accountPiRelationClient)
	merchantResolutionClient := types4.MerchantResoulutionClientProvider(merchantResolutionVgClient)
	piProcessor := pi.NewProcessor(accountPiRelationClient, piClient, merchantClient, upiProcessor, paymentPaymentClient, userGrpClient, userClient, actorClient, conf, savingsProcessor, merchantResolutionClient)
	orderProcessor := order2.NewOrderProcessor(orderDao, orderUpdateNotificationPublisher, piClient)
	string2 := envProvider(conf)
	service := wire.InitializeMerchantNameCategoriserService(vendorapiGenConf, string2)
	merchantProcessor := merchant2.NewProcessor(merchantClient, service)
	inbound_notificationProcessor := inbound_notification.NewProcessor(depositClient, transactionDao, orderDao, orderUpdateNotificationPublisher, tncEventPublisher, eventBroker, processor, piProcessor, upiProcessor, orderProcessor, orderOrchestrationPublisher, orderNotificationPublisher, orderSearchPublisher, merchantClient, p2pInvestmentClient, orderVpaVerificationPublisher, txnNotificationPublisher, celestialProcessor, iftClient, merchantResolutionClient, parserClient, conf, evaluator, enachSvcClient, recurringPaymentSvcClient, txnDetailedStatusUpdateSnsPublisher, piClient, payClient, merchantProcessor)
	s3Client := PreApprovedLoansS3ClientProvider(awsConf, conf)
	inMemoryCryptorStore, err := InitCryptors(conf)
	if err != nil {
		return nil, err
	}
	cryptor, err := PgpCryptoProvider(inMemoryCryptorStore)
	if err != nil {
		return nil, err
	}
	awsSftp := sftp.NewAwsSftp(s3Client, cryptor)
	faaSExecutor, err := FaasExecutorProvider(ctx, awsConf, conf)
	if err != nil {
		return nil, err
	}
	consumerService := consumer.NewService(orderDao, transactionDao, orderEngine, savingsClient, accountPiRelationClient, piClient, orderAttemptDaoCRDB, inbound_notificationProcessor, conf, savingsProcessor, awsSftp, paymentPaymentClient, userGrpClient, userClient, actorClient, faaSExecutor, paySavingsBalanceClient, evaluator, txnDetailedStatusUpdateSnsPublisher, payClient, upiProcessor)
	return consumerService, nil
}

func InitializePaymentService(db types.EpifiCRDB, vgPaymentClient types4.VgPaymentClientWithInterceptors, piClient paymentinstrument.PiClient, savingsClient savings.SavingsClient, authClient auth.AuthClient, actorClient actor.ActorClient, userClient user.UsersClient, vgUPIClient types4.VgUpiClientWithInterceptors, intraBankEnquiryPublisher types2.IntraBankEnquiryPublisher, impsEnquiryPublisher types2.IMPSEnquiryPublisher, neftEnquiryPublisher types2.NEFTEnquiryPublisher, rtgsEnquiryPublisher types2.RTGSEnquiryPublisher, upiEnquiryPublisher types2.UPIEnquiryPublisher, orderOrchestrationPublisher types2.OrderOrchestrationPublisher, paymentOrchestrationPublisher types2.PaymentOrchestrationPublisher, broker events.Broker, conf *genconf.Config, commsClient types3.OrderCommsClientWithInterceptors, redisClient types3.DelayQueue1RedisStore, client payment.ConsumerClient, accountPiRelationClient account_pi.AccountPIRelationClient, upiClient upi.UPIClient, groupClient group.GroupClient, inPaymentOrderPublisher types2.InPaymentOrderUpdatePublisher, merchantClient merchant.MerchantServiceClient, merchantResolutionVgClient types4.MerchantResolutionClientWithInterceptors, serviceClient bankcust.BankCustomerServiceClient, healthEngineClient health_engine.HealthEngineServiceClient, upiOnboardingClient onboarding.UpiOnboardingClient, celestialClient celestial.CelestialClient, txnDetailedStatusUpdateSnsPublisher types3.TxnDetailedStatusUpdateSnsPublisher, orderRedisStore types.OrderRedisStore, locationClient location.LocationClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], timelineClient timeline.TimelineServiceClient, payClient pay.PayClient) *payment2.Service {
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(conf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	transactionDaoCRDB := dao.NewTransactionDao(db, crdbIdempotentTxnExecutor, domainIdGenerator, dbResourceProvider, txnExecutorResourceProvider, enableResourceProvider, attributedIdGen)
	transactionCacheStorage := transactionCacheStorageProvider(orderRedisStore)
	transactionCacheConfig := transactionCacheConfigProvider(conf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, transactionCacheStorage, transactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, transactionCacheConfig)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	orderCacheStorage := orderCacheStorageProvider(orderRedisStore)
	orderCacheConfig := orderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, orderCacheStorage, orderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, orderCacheConfig)
	paymentClient := types4.VgPaymentClientProvider(vgPaymentClient)
	source := idgen.NewCryptoSeededSource()
	runeNumberIdGenerator := idgen.NewNumberIdGenerator(source)
	upiUPIClient := types4.VgUpiClientProvider(vgUPIClient)
	commsCommsClient := types3.CommsClientProvider(commsClient)
	v := convertPaymentProtocolPublishersToMap(intraBankEnquiryPublisher, impsEnquiryPublisher, neftEnquiryPublisher, rtgsEnquiryPublisher, upiEnquiryPublisher)
	delayPublisher := providePaymentOrchestrationPublisher(paymentOrchestrationPublisher)
	client2 := provideDelayQueueDb0RedisClient(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client2, clock, uuidGenerator)
	featureReleaseConfig := FeatureReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, groupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	processor := upi3.NewProcessor(upiClient, authClient, actorClient, piClient, evaluator, upiOnboardingClient, accountPiRelationClient, conf, userClient, upiUPIClient)
	actorProcessor := actor2.NewProcessor(actorClient, userClient, locationClient, authClient)
	merchantResolutionClient := types4.MerchantResoulutionClientProvider(merchantResolutionVgClient)
	health_engineProcessor := health_engine2.NewProcessor(healthEngineClient)
	savingsProcessor := savings2.NewProcessor(savingsClient, accountPiRelationClient)
	piProcessor := pi.NewProcessor(accountPiRelationClient, piClient, merchantClient, processor, paymentClient, groupClient, userClient, actorClient, conf, savingsProcessor, merchantResolutionClient)
	paymentProcessor := payment3.NewProcessor(transactionDao, health_engineProcessor, piProcessor, conf, txnDetailedStatusUpdateSnsPublisher)
	transactionAmountBreakupCRDB := dao.NewTransactionAmountBreakupDao(db)
	service := payment2.NewService(transactionDao, orderDao, paymentClient, runeNumberIdGenerator, piClient, savingsClient, authClient, upiUPIClient, actorClient, userClient, commsCommsClient, orderOrchestrationPublisher, v, delayPublisher, broker, conf, redisLockManager, client, accountPiRelationClient, processor, actorProcessor, inPaymentOrderPublisher, merchantClient, merchantResolutionClient, serviceClient, paymentProcessor, upiClient, transactionAmountBreakupCRDB, celestialClient, txnDetailedStatusUpdateSnsPublisher, timelineClient, payClient)
	return service
}

func InitializePaymentDecisionService(actorClient actor.ActorClient, piClient paymentinstrument.PiClient, accountPiRelationClient account_pi.AccountPIRelationClient, upiClient upi.UPIClient, userClient user.UsersClient, userGrpClient group.GroupClient, authClient auth.AuthClient, orderClient order3.OrderServiceClient, paymentClient payment.PaymentClient, orderConfig *genconf.Config, payClient pay.PayClient, upiOnboardingClient onboarding.UpiOnboardingClient, savingClient savings.SavingsClient, heClient health_engine.HealthEngineServiceClient, beneficiaryManagementClient beneficiarymanagement.BeneficiaryManagementClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], operationalStatusClient operstatus.OperationalStatusServiceClient) *decision_engine.Service {
	paymentProtocolDecisionParams := PaymentProtocolDecisionsParamsProvider(orderConfig)
	ruleEngineParams := RuleEngineParamsProvider(orderConfig)
	customRuleDEParams := CustomRuleDEParamsProvider(orderConfig)
	processor := user2.NewProcessor(userClient, userGrpClient, actorClient)
	timeDuration := CoolOffWindowProvider(orderConfig)
	featureReleaseConfig := ReleaseConfigProvider(orderConfig)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := decision_engine.NewService(paymentProtocolDecisionParams, ruleEngineParams, actorClient, piClient, accountPiRelationClient, upiClient, customRuleDEParams, processor, authClient, orderClient, timeDuration, userGrpClient, userClient, paymentClient, orderConfig, payClient, upiOnboardingClient, savingClient, heClient, evaluator, beneficiaryManagementClient, operationalStatusClient)
	return service
}

func InitializePaymentConsumerService(db types.EpifiCRDB, vgPaymentClient types4.VgPaymentClientWithInterceptors, authClient auth.AuthClient, orderOrchestrationPublisher types2.OrderOrchestrationPublisher, vgUPIClient types4.VgUpiClientWithInterceptors, broker events.Broker, orderConf *genconf.Config, piClient paymentinstrument.PiClient, orderClient order3.OrderServiceClient, upiClient upi.UPIClient, actorClient actor.ActorClient, usersClient user.UsersClient, groupClient group.GroupClient, deemedTransactionUPIEnquiryPublisher types2.DeemedTransactionUPIEnquiryPublisher, healthEngineClient health_engine.HealthEngineServiceClient, accountPiRelationClient account_pi.AccountPIRelationClient, merchantClient merchant.MerchantServiceClient, savingsClient savings.SavingsClient, merchantResolutionVgClient types4.MerchantResolutionClientWithInterceptors, orderUpdatePublisher types2.OrderUpdateEventPublisher, payIncidentManagerClient payincidentmanager.PayIncidentManagerClient, upiOnboardingClient onboarding.UpiOnboardingClient, txnDetailedStatusUpdateSnsPublisher types3.TxnDetailedStatusUpdateSnsPublisher, orderRedisStore types.OrderRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *consumer2.Service {
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(orderConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	transactionDaoCRDB := dao.NewTransactionDao(db, crdbIdempotentTxnExecutor, domainIdGenerator, dbResourceProvider, txnExecutorResourceProvider, enableResourceProvider, attributedIdGen)
	transactionCacheStorage := transactionCacheStorageProvider(orderRedisStore)
	transactionCacheConfig := transactionCacheConfigProvider(orderConf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, transactionCacheStorage, transactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, transactionCacheConfig)
	paymentClient := types4.VgPaymentClientProvider(vgPaymentClient)
	upiUPIClient := types4.VgUpiClientProvider(vgUPIClient)
	featureReleaseConfig := FeatureReleaseConfigProvider(orderConf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, groupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	processor := upi3.NewProcessor(upiClient, authClient, actorClient, piClient, evaluator, upiOnboardingClient, accountPiRelationClient, orderConf, usersClient, upiUPIClient)
	health_engineProcessor := health_engine2.NewProcessor(healthEngineClient)
	savingsProcessor := savings2.NewProcessor(savingsClient, accountPiRelationClient)
	merchantResolutionClient := types4.MerchantResoulutionClientProvider(merchantResolutionVgClient)
	piProcessor := pi.NewProcessor(accountPiRelationClient, piClient, merchantClient, processor, paymentClient, groupClient, usersClient, actorClient, orderConf, savingsProcessor, merchantResolutionClient)
	paymentProcessor := payment3.NewProcessor(transactionDao, health_engineProcessor, piProcessor, orderConf, txnDetailedStatusUpdateSnsPublisher)
	service := consumer2.NewService(transactionDao, paymentClient, authClient, orderOrchestrationPublisher, upiUPIClient, broker, orderConf, piClient, orderClient, processor, deemedTransactionUPIEnquiryPublisher, paymentProcessor, orderUpdatePublisher, evaluator, payIncidentManagerClient, txnDetailedStatusUpdateSnsPublisher)
	return service
}

func InitializeCallbackConsumer(db types.EpifiCRDB, orderOrchestrationPublisher types2.OrderOrchestrationPublisher, piClient paymentinstrument.PiClient, broker events.Broker, conf *genconf.Config, celestialClient celestial.CelestialClient, txnDetailedStatusUpdateSnsPublisher types3.TxnDetailedStatusUpdateSnsPublisher, healthEngineClient health_engine.HealthEngineServiceClient, accountPiRelationClient account_pi.AccountPIRelationClient, merchantClient merchant.MerchantServiceClient, vgPaymentClient types4.VgPaymentClientWithInterceptors, userGroupClient group.GroupClient, usersClient user.UsersClient, actorClient actor.ActorClient, merchantResolutionClient types4.MerchantResolutionClientWithInterceptors, savingsClient savings.SavingsClient, upiClient upi.UPIClient, authClient auth.AuthClient, upiOnbClient onboarding.UpiOnboardingClient, orderRedisStore types.OrderRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], vgUPIClient upi2.UPIClient) *consumer2.CallbackService {
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(conf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	transactionDaoCRDB := dao.NewTransactionDao(db, crdbIdempotentTxnExecutor, domainIdGenerator, dbResourceProvider, txnExecutorResourceProvider, enableResourceProvider, attributedIdGen)
	transactionCacheStorage := transactionCacheStorageProvider(orderRedisStore)
	transactionCacheConfig := transactionCacheConfigProvider(conf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, transactionCacheStorage, transactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, transactionCacheConfig)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	orderCacheStorage := orderCacheStorageProvider(orderRedisStore)
	orderCacheConfig := orderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, orderCacheStorage, orderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, orderCacheConfig)
	celestialProcessor := celestial2.NewCelestialProcessor(celestialClient, conf)
	processor := health_engine2.NewProcessor(healthEngineClient)
	featureReleaseConfig := ReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, usersClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	upiProcessor := upi3.NewProcessor(upiClient, authClient, actorClient, piClient, evaluator, upiOnbClient, accountPiRelationClient, conf, usersClient, vgUPIClient)
	paymentClient := types4.VgPaymentClientProvider(vgPaymentClient)
	savingsProcessor := savings2.NewProcessor(savingsClient, accountPiRelationClient)
	merchantresolutionMerchantResolutionClient := types4.MerchantResoulutionClientProvider(merchantResolutionClient)
	piProcessor := pi.NewProcessor(accountPiRelationClient, piClient, merchantClient, upiProcessor, paymentClient, userGroupClient, usersClient, actorClient, conf, savingsProcessor, merchantresolutionMerchantResolutionClient)
	paymentProcessor := payment3.NewProcessor(transactionDao, processor, piProcessor, conf, txnDetailedStatusUpdateSnsPublisher)
	callbackService := consumer2.NewCallbackService(transactionDao, orderDao, orderOrchestrationPublisher, piClient, broker, conf, db, celestialProcessor, txnDetailedStatusUpdateSnsPublisher, paymentProcessor, attributedIdGen, txnExecutorResourceProvider, dbResourceProvider)
	return callbackService
}

func InitializeNotificationConsumer(conf *genconf.Config, db types.EpifiCRDB, commsClient types3.OrderCommsClientWithInterceptors, actorClient actor.ActorClient, piClient paymentinstrument.PiClient, timelineClient timeline.TimelineServiceClient, depositClient deposit.DepositClient, savingsClient savings.SavingsClient, accountPiRelationClient account_pi.AccountPIRelationClient, userClient user.UsersClient, userGrpClient group.GroupClient, orderCollectNotificationPublisher types2.OrderCollectNotificationPublisher, orderNotificationFallbackPublisher types2.OrderNotificationFallbackPublisher, recurringPaymentClient recurringpayment.RecurringPaymentServiceClient, paySavingsBalanceClient balance.BalanceClient, upiClient upi.UPIClient, authClient auth.AuthClient, upiOnboardingClient onboarding.UpiOnboardingClient, orderRedisStore types.OrderRedisStore, cpClient provisioning.CardProvisioningClient, tieringClient tiering.TieringClient, locationClient location.LocationClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], vgUPIClient upi2.UPIClient) *notification.Service {
	commsCommsClient := types3.CommsClientProvider(commsClient)
	transactionNotificationMapDaoCRDB := dao.NewTransactionNotificationMapDao(db)
	processor := user2.NewProcessor(userClient, userGrpClient, actorClient)
	actorProcessor := actor2.NewProcessor(actorClient, userClient, locationClient, authClient)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(conf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	orderCacheStorage := orderCacheStorageProvider(orderRedisStore)
	orderCacheConfig := orderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, orderCacheStorage, orderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, orderCacheConfig)
	featureReleaseConfig := ReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	upiProcessor := upi3.NewProcessor(upiClient, authClient, actorClient, piClient, evaluator, upiOnboardingClient, accountPiRelationClient, conf, userClient, vgUPIClient)
	service := notification.NewService(commsCommsClient, actorClient, piClient, timelineClient, conf, depositClient, savingsClient, accountPiRelationClient, transactionNotificationMapDaoCRDB, processor, orderCollectNotificationPublisher, orderNotificationFallbackPublisher, recurringPaymentClient, actorProcessor, orderDao, paySavingsBalanceClient, upiProcessor, cpClient, tieringClient)
	return service
}

func InitializeActorActivityService(orderClient order3.OrderServiceClient, actorClient actor.ActorClient, accountPiClient account_pi.AccountPIRelationClient, piClient paymentinstrument.PiClient, depositClient deposit.DepositClient, conf *genconf.Config, timelineClient timeline.TimelineServiceClient, client aa.AccountAggregatorClient, userClient user.UsersClient, userGroupClient group.GroupClient, caClient connected_account.ConnectedAccountClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB]) *actoractivity.Service {
	processor := user2.NewProcessor(userClient, userGroupClient, actorClient)
	service := actoractivity.NewService(orderClient, actorClient, accountPiClient, piClient, depositClient, timelineClient, client, caClient, processor, userGroupClient, userClient, conf)
	return service
}

func InitializeCxService(db types.EpifiCRDB, conf *genconf.Config, accPiClient account_pi.AccountPIRelationClient, rpClient recurringpayment.RecurringPaymentServiceClient, orderRedisStore types.OrderRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB]) *cx.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(conf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	orderCacheStorage := orderCacheStorageProvider(orderRedisStore)
	orderCacheConfig := orderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, orderCacheStorage, orderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, orderCacheConfig)
	service := cx.NewService(orderDao, accPiClient, rpClient)
	return service
}

func InitializeDevService(db types.EpifiCRDB, wealthDB types.EpifiWealthCRDB, accPiClient account_pi.AccountPIRelationClient, savingsLedgerReconRedisClient types3.SavingsLedgerRedisStore, conf *genconf.Config, orderRedisStore types.OrderRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *developer.OrderDevService {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(conf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	orderCacheStorage := orderCacheStorageProvider(orderRedisStore)
	orderCacheConfig := orderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, orderCacheStorage, orderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, orderCacheConfig)
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	transactionDaoCRDB := dao.NewTransactionDao(db, crdbIdempotentTxnExecutor, domainIdGenerator, dbResourceProvider, txnExecutorResourceProvider, enableResourceProvider, attributedIdGen)
	transactionCacheStorage := transactionCacheStorageProvider(orderRedisStore)
	transactionCacheConfig := transactionCacheConfigProvider(conf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, transactionCacheStorage, transactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, transactionCacheConfig)
	orderDetailsProcessor := processor.NewOrderDetailsProcessor(orderDao, transactionDao)
	orderAttemptDaoCRDB := dao.NewOrderAttemptDao(db)
	orderAttemptProcessor := processor.NewOrderAttemptProcessor(orderAttemptDaoCRDB)
	savingsLedgerReconDaoCRDB := dao.NewSavingsLedgerReconDaoCrdb(db)
	client := provideSavingLedgerRedisClient(savingsLedgerReconRedisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	savingsLedgerReconCacheConfig := SavingsLedgerReconCacheConfigProvider(conf)
	savingsLedgerReconCache := dao.NewSavingsLedgerReconCache(savingsLedgerReconDaoCRDB, redisCacheStorage, savingsLedgerReconCacheConfig)
	savingsLedgerReconDao := dao.ProvideSavingsLedgerReconDao(savingsLedgerReconCache, savingsLedgerReconDaoCRDB, savingsLedgerReconCacheConfig)
	savingsLedgerReconProcessor := processor.NewSavingsLedgerReconProcessor(savingsLedgerReconDao)
	transactionProcessor := processor.NewTransactionProcessor(transactionDao)
	orderWithTransactionsProcessor := processor.NewOrderWithTransactionsProcessor(orderDao, transactionDao)
	featureFlags := featureFlagConfProvider(conf)
	aaTransactionDaoCRDB := dao.NewAATransactionDao(wealthDB, domainIdGenerator)
	aaTransactionDaoPgdb := dao.NewAATransactionDaoPgdb(conf, wealthDB, domainIdGenerator)
	aaTransactionDao := dao.AATransactionDaoProvider(featureFlags, aaTransactionDaoCRDB, aaTransactionDaoPgdb)
	aaTransactionsProcessor := processor.NewAaTransactionsProcessor(aaTransactionDao)
	txnsForActorProcessor := processor.NewTxnsForActorProcessor(orderDao, accPiClient)
	orderMetadataDaoCrdb := dao.NewOrderMetadataDao(db)
	orderMetadataProcessor := processor.NewOrderMetadataProcessor(orderMetadataDaoCrdb)
	aaDataPurgeRequestDaoCRDB := dao.NewAADataPurgeRequestDao(db)
	aaDataPurgeRequestDaoPgdb := dao.NewAADataPurgeRequestDaoPgdb(conf, db)
	aaDataPurgeRequestDao := dao.AADataPurgeRequestTransactionDaoProvider(featureFlags, aaDataPurgeRequestDaoCRDB, aaDataPurgeRequestDaoPgdb)
	aaDataPurgeRequestsProcessor := processor.NewAaDataPurgeRequestsProcessor(aaDataPurgeRequestDao)
	orderVendorOrderMapProcessor := processor.NewOrderVendorOrderMapProcessor(orderVendorOrderMapDaoCRDB)
	transactionNotificationMapDaoCRDB := dao.NewTransactionNotificationMapDao(db)
	transactionNotificationMapProcessor := processor.NewTransactionNotificationMapProcessor(transactionNotificationMapDaoCRDB)
	devFactory := developer.NewDevFactory(orderDetailsProcessor, orderAttemptProcessor, savingsLedgerReconProcessor, transactionProcessor, orderWithTransactionsProcessor, aaTransactionsProcessor, txnsForActorProcessor, orderMetadataProcessor, aaDataPurgeRequestsProcessor, orderVendorOrderMapProcessor, transactionNotificationMapProcessor)
	orderDevService := developer.NewOrderDevService(devFactory)
	return orderDevService
}

func InitializeReconService(conf *genconf.Config, db types.EpifiCRDB, reconPublisher types3.SavingsLedgerReconPublisher, userClient user.UsersClient, healthEngineClient health_engine.HealthEngineServiceClient, userGrpClient group.GroupClient, actorClient actor.ActorClient, broker events.Broker, savingsLedgerReconRedisClient types3.SavingsLedgerRedisStore, bcClient bankcust.BankCustomerServiceClient, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB]) *recon.Service {
	savingsLedgerReconDaoCRDB := dao.NewSavingsLedgerReconDaoCrdb(db)
	client := provideSavingLedgerRedisClient(savingsLedgerReconRedisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	savingsLedgerReconCacheConfig := CacheConfigProvider(conf)
	savingsLedgerReconCache := dao.NewSavingsLedgerReconCache(savingsLedgerReconDaoCRDB, redisCacheStorage, savingsLedgerReconCacheConfig)
	savingsLedgerReconDao := dao.ProvideSavingsLedgerReconDao(savingsLedgerReconCache, savingsLedgerReconDaoCRDB, savingsLedgerReconCacheConfig)
	publisher := ReconPublisherProvider(reconPublisher)
	userProcessor := user2.NewProcessor(userClient, userGrpClient, actorClient)
	service := recon.NewService(conf, savingsLedgerReconDao, publisher, userClient, actorClient, userProcessor, broker, db, healthEngineClient, bcClient)
	return service
}

func InitializeBusinessService(db types.EpifiCRDB, piClient paymentinstrument.PiClient, ovgB2CPaymentClient types4.VgB2CPaymentClientWithInterceptors, savingsClient savings.SavingsClient, orderConf *genconf.Config, b2cPaymentLockRedisStore types3.B2CPaymentLockRedisStore, txnDetailedStatusUpdateSnsPublisher types3.TxnDetailedStatusUpdateSnsPublisher, orderRedisStore types.OrderRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor]) *business.Service {
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(orderConf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	transactionDaoCRDB := dao.NewTransactionDao(db, crdbIdempotentTxnExecutor, domainIdGenerator, dbResourceProvider, txnExecutorResourceProvider, enableResourceProvider, attributedIdGen)
	transactionCacheStorage := transactionCacheStorageProvider(orderRedisStore)
	transactionCacheConfig := transactionCacheConfigProvider(orderConf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, transactionCacheStorage, transactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, transactionCacheConfig)
	paymentClient := types4.VgB2CPaymentClientProvider(ovgB2CPaymentClient)
	errorRespCodesForPermanentFailure := ErrorRespCodesForPermanentFailureConfProvider(orderConf)
	client := provideB2cPaymentLockRedisClient(b2cPaymentLockRedisStore)
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	service := business.NewService(transactionDao, piClient, paymentClient, savingsClient, errorRespCodesForPermanentFailure, orderConf, redisLockManager, txnDetailedStatusUpdateSnsPublisher, attributedIdGen)
	return service
}

func InitializeReconConsumerService(db types.EpifiCRDB, vgAccountClient types4.VgAccountsClientWithInterceptors, actorClient actor.ActorClient, authClient auth.AuthClient, timelineClient timeline.TimelineServiceClient, accountPiRelationClient account_pi.AccountPIRelationClient, piClient paymentinstrument.PiClient, depositClient deposit.DepositClient, upiClient upi.UPIClient, eventBroker events.Broker, orderOrchestrationPublisher types2.OrderOrchestrationPublisher, orderUpdateNotificationPublisher types2.OrderUpdateEventPublisher, orderNotificationPublisher types2.OrderNotificationPublisher, orderSearchPublisher types2.OrderSearchPublisher, tncEventPublisher types2.EventsCompletedTnCPublisher, merchantClient merchant.MerchantServiceClient, p2pInvestmentClient p2pinvestment.P2PInvestmentClient, conf *genconf.Config, savingsLedgerReconRedisClient types3.SavingsLedgerRedisStore, vgPaymentClient types4.VgPaymentClientWithInterceptors, userClient user.UsersClient, userGrpClient group.GroupClient, orderVpaVerificationPublisher types2.OrderVpaVerificationPublisher, txnNotificationPublisher types2.TxnNotificationPublisher, celestialClient celestial.CelestialClient, iftClient internationalfundtransfer.InternationalFundTransferClient, merchantResolutionVgClient types4.MerchantResolutionClientWithInterceptors, accountStatementClient statement.AccountStatementClient, savingsClient savings.SavingsClient, parserClient parser.ParserClient, recurringPaymentSvcClient recurringpayment.RecurringPaymentServiceClient, enachSvcClient enach.EnachServiceClient, upiOnboardingClient onboarding.UpiOnboardingClient, txnDetailedStatusUpdateSnsPublisher types3.TxnDetailedStatusUpdateSnsPublisher, orderRedisStore types.OrderRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], txnExecutorResourceProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], payClient pay.PayClient, vgUPIClient upi2.UPIClient, vendorapiGenConf *genconf2.Config) *consumer3.Service {
	accountsClient := types4.VgAccountsClientProvider(vgAccountClient)
	savingsLedgerReconDaoCRDB := dao.NewSavingsLedgerReconDaoCrdb(db)
	client := provideSavingLedgerRedisClient(savingsLedgerReconRedisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	savingsLedgerReconCacheConfig := SavingsLedgerReconCacheConfigProvider(conf)
	savingsLedgerReconCache := dao.NewSavingsLedgerReconCache(savingsLedgerReconDaoCRDB, redisCacheStorage, savingsLedgerReconCacheConfig)
	savingsLedgerReconDao := dao.ProvideSavingsLedgerReconDao(savingsLedgerReconCache, savingsLedgerReconDaoCRDB, savingsLedgerReconCacheConfig)
	gormDB := dao.GormProvider(db)
	crdbIdempotentTxnExecutor := storagev2.NewCRDBIdempotentTxnExecutor(gormDB)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(conf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	transactionDaoCRDB := dao.NewTransactionDao(db, crdbIdempotentTxnExecutor, domainIdGenerator, dbResourceProvider, txnExecutorResourceProvider, enableResourceProvider, attributedIdGen)
	transactionCacheStorage := transactionCacheStorageProvider(orderRedisStore)
	transactionCacheConfig := transactionCacheConfigProvider(conf)
	transactionDaoCache := dao.NewTransactionDaoCache(transactionDaoCRDB, transactionCacheStorage, transactionCacheConfig)
	transactionDao := dao.ProvideTransactionDao(transactionDaoCRDB, transactionDaoCache, transactionCacheConfig)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	orderCacheStorage := orderCacheStorageProvider(orderRedisStore)
	orderCacheConfig := orderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, orderCacheStorage, orderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, orderCacheConfig)
	timelineProcessor := timeline2.NewProcessor(actorClient, timelineClient)
	featureReleaseConfig := ReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	upiProcessor := upi3.NewProcessor(upiClient, authClient, actorClient, piClient, evaluator, upiOnboardingClient, accountPiRelationClient, conf, userClient, vgUPIClient)
	paymentClient := types4.VgPaymentClientProvider(vgPaymentClient)
	savingsProcessor := savings2.NewProcessor(savingsClient, accountPiRelationClient)
	merchantResolutionClient := types4.MerchantResoulutionClientProvider(merchantResolutionVgClient)
	piProcessor := pi.NewProcessor(accountPiRelationClient, piClient, merchantClient, upiProcessor, paymentClient, userGrpClient, userClient, actorClient, conf, savingsProcessor, merchantResolutionClient)
	orderProcessor := order2.NewOrderProcessor(orderDao, orderUpdateNotificationPublisher, piClient)
	celestialProcessor := celestial2.NewCelestialProcessor(celestialClient, conf)
	string2 := envProvider(conf)
	service := wire.InitializeMerchantNameCategoriserService(vendorapiGenConf, string2)
	merchantProcessor := merchant2.NewProcessor(merchantClient, service)
	inbound_notificationProcessor := inbound_notification.NewProcessor(depositClient, transactionDao, orderDao, orderUpdateNotificationPublisher, tncEventPublisher, eventBroker, timelineProcessor, piProcessor, upiProcessor, orderProcessor, orderOrchestrationPublisher, orderNotificationPublisher, orderSearchPublisher, merchantClient, p2pInvestmentClient, orderVpaVerificationPublisher, txnNotificationPublisher, celestialProcessor, iftClient, merchantResolutionClient, parserClient, conf, evaluator, enachSvcClient, recurringPaymentSvcClient, txnDetailedStatusUpdateSnsPublisher, piClient, payClient, merchantProcessor)
	consumerService := consumer3.NewService(accountsClient, actorClient, authClient, savingsLedgerReconDao, transactionDao, orderDao, inbound_notificationProcessor, eventBroker, conf, db, accountStatementClient)
	return consumerService
}

func InitializeDisputedTxnConsumerService(db types.EpifiCRDB, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB]) *consumer4.Service {
	disputedTransactionDaoCRDB := dao.NewDisputedTransactionDao(db)
	service := consumer4.NewService(disputedTransactionDaoCRDB)
	return service
}

func InitializeCampaignService(conf *genconf.Config, db types.EpifiCRDB, onboardingClient onboarding2.OnboardingClient, commsClient types3.OrderCommsClientWithInterceptors, orderRedisStore types.OrderRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB]) *campaign.Service {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	enableResourceProvider := OrderServerEntitySegregationFlagProvider(conf)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	orderCacheStorage := orderCacheStorageProvider(orderRedisStore)
	orderCacheConfig := orderCacheConfigProvider(conf)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, orderCacheStorage, orderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, orderCacheConfig)
	payCampaignsDaoCRDB := dao.NewPayCampaignsDao(db)
	commsCommsClient := types3.CommsClientProvider(commsClient)
	service := campaign.NewService(conf, orderDao, payCampaignsDaoCRDB, onboardingClient, commsCommsClient)
	return service
}

func InitializeAccountAggregatorService(conf *genconf.Config, db types.EpifiWealthCRDB, caClient connected_account.ConnectedAccountClient, client account_pi.AccountPIRelationClient) *aa2.Service {
	featureFlags := featureFlagConfProvider(conf)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	aaTransactionDaoCRDB := dao.NewAATransactionDao(db, domainIdGenerator)
	aaTransactionDaoPgdb := dao.NewAATransactionDaoPgdb(conf, db, domainIdGenerator)
	aaTransactionDao := dao.AATransactionDaoProvider(featureFlags, aaTransactionDaoCRDB, aaTransactionDaoPgdb)
	service := aa2.NewService(conf, aaTransactionDao, caClient, client)
	return service
}

func InitializeAAConsumerService(conf *genconf.Config, epifiDb types.EpifiCRDB, epifiWealthDb types.EpifiWealthCRDB, aaTxnPublisher types2.AATxnPublisher, authClient auth.AuthClient, upiClient upi.UPIClient, piClient paymentinstrument.PiClient, merchantClient merchant.MerchantServiceClient, accountPiClient account_pi.AccountPIRelationClient, actorClient actor.ActorClient, connectedAccountClient connected_account.ConnectedAccountClient, timelineClient timeline.TimelineServiceClient, parserClient types4.VgParserClientWithInterceptors, aaAccountPiPurgePublisher types2.AaAccountPiPurgePublisher, actorPiRelationPurgePublisher types2.ActorPiRelationPurgePublisher, aaDataPurgingOrchestratorPublisher types2.AaDataPurgeOrchestrationPublisher, redisClient types3.DelayQueue1RedisStore, vgPaymentClient types4.VgPaymentClientWithInterceptors, userClient user.UsersClient, userGrpClient group.GroupClient, savingsClient savings.SavingsClient, merchantResolutionVgClient types4.MerchantResolutionClientWithInterceptors, upiOnbClient onboarding.UpiOnboardingClient, locationClient location.LocationClient, vgUPIClient upi2.UPIClient, vendorapiGenConf *genconf2.Config) *consumer5.Service {
	featureFlags := featureFlagConfProvider(conf)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	aaTransactionDaoCRDB := dao.NewAATransactionDao(epifiWealthDb, domainIdGenerator)
	aaTransactionDaoPgdb := dao.NewAATransactionDaoPgdb(conf, epifiWealthDb, domainIdGenerator)
	aaTransactionDao := dao.AATransactionDaoProvider(featureFlags, aaTransactionDaoCRDB, aaTransactionDaoPgdb)
	featureReleaseConfig := ReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	upiProcessor := upi3.NewProcessor(upiClient, authClient, actorClient, piClient, evaluator, upiOnbClient, accountPiClient, conf, userClient, vgUPIClient)
	paymentClient := types4.VgPaymentClientProvider(vgPaymentClient)
	savingsProcessor := savings2.NewProcessor(savingsClient, accountPiClient)
	merchantResolutionClient := types4.MerchantResoulutionClientProvider(merchantResolutionVgClient)
	piProcessor := pi.NewProcessor(accountPiClient, piClient, merchantClient, upiProcessor, paymentClient, userGrpClient, userClient, actorClient, conf, savingsProcessor, merchantResolutionClient)
	aaProcessor := aa3.NewProcessor(actorClient, piProcessor, userClient)
	connectedaccountProcessor := connectedaccount.NewProcessor(connectedAccountClient)
	parserParserClient := types4.VgParserClientProvider(parserClient)
	parserProcessor := parser2.NewProcessor(connectedaccountProcessor, parserParserClient)
	timelineProcessor := timeline2.NewProcessor(actorClient, timelineClient)
	publisher := provideAaTxnPublisher(aaTxnPublisher)
	aaDataPurgeRequestDaoCRDB := dao.NewAADataPurgeRequestDao(epifiDb)
	aaDataPurgeRequestDaoPgdb := dao.NewAADataPurgeRequestDaoPgdb(conf, epifiDb)
	aaDataPurgeRequestDao := dao.AADataPurgeRequestTransactionDaoProvider(featureFlags, aaDataPurgeRequestDaoCRDB, aaDataPurgeRequestDaoPgdb)
	actorProcessor := actor2.NewProcessor(actorClient, userClient, locationClient, authClient)
	string2 := envProvider(conf)
	service := wire.InitializeMerchantNameCategoriserService(vendorapiGenConf, string2)
	merchantProcessor := merchant2.NewProcessor(merchantClient, service)
	client := provideDelayQueueDb0RedisClient(redisClient)
	redisRwLock := lock.NewRedisRwLock(client, clock)
	epifiTxnExecutor := EpifiTxnExecutorProviderForAA(conf, epifiDb)
	epifiWealthTxnExecutor := EpifiWealthTxnExecutorProviderForAA(conf, epifiWealthDb)
	consumerService := consumer5.NewService(aaTransactionDao, aaProcessor, piProcessor, parserProcessor, timelineProcessor, publisher, actorPiRelationPurgePublisher, connectedaccountProcessor, aaDataPurgeRequestDao, actorProcessor, aaDataPurgingOrchestratorPublisher, merchantProcessor, timelineClient, conf, redisRwLock, epifiTxnExecutor, epifiWealthTxnExecutor)
	return consumerService
}

func InitializeActivityProcessor(db types.EpifiCRDB, orderCacheConfig *genconf.OrderCacheConfig, orderEventPublisher types2.OrderUpdateEventPublisher, piClient paymentinstrument.PiClient, orderRedisStore types.OrderRedisStore, dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB], enableResourceProvider types3.EnableResourceProvider) *activity.Processor {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	attributedIdGen := dao.ProvideAttributedIdGen(clock)
	orderVendorOrderMapDaoCRDB := dao2.NewOrderVendorOrderMapDao(db)
	orderDaoCRDB := dao.NewOrderDao(db, domainIdGenerator, dbResourceProvider, enableResourceProvider, attributedIdGen, orderVendorOrderMapDaoCRDB)
	orderCacheStorage := orderCacheStorageProvider(orderRedisStore)
	orderDaoCache := dao.NewOrderDaoCache(orderDaoCRDB, orderCacheStorage, orderCacheConfig)
	orderDao := dao.ProvideOrderDao(orderDaoCRDB, orderDaoCache, orderCacheConfig)
	activityProcessor := activity.NewProcessor(orderDao, orderEventPublisher, piClient)
	return activityProcessor
}

// wire.go:

func envProvider(conf *genconf.Config) string {
	return conf.Application().Environment
}

func provideSavingLedgerRedisClient(store2 types3.SavingsLedgerRedisStore) *redis.Client {
	return store2
}

func provideDelayQueueDb0RedisClient(store types3.DelayQueue1RedisStore) *redis.Client {
	return store
}

func provideWorkflowProcessingDelayPublisher(publisher types2.WorkflowProcessingDelayPublisher) queue.DelayPublisher {
	return publisher
}

func provideOrderEventPublisher2(publisher2 types2.OrderEventPublisher2) queue.Publisher {
	return publisher2
}

func provideOrderOrchestrationPublisher(publisher types2.OrderOrchestrationPublisher) types2.OrderOrchestrationPublisher {
	return publisher
}

func provideAaTxnPublisher(publisher types2.AATxnPublisher) queue.Publisher {
	return publisher
}

func providePaymentOrchestrationPublisher(publisher types2.PaymentOrchestrationPublisher) queue.DelayPublisher {
	return publisher
}

func provideB2cPaymentLockRedisClient(client types3.B2CPaymentLockRedisStore) *redis.Client {
	return client
}

func provideInPaymentOrderPublisher(publisher types2.InPaymentOrderUpdatePublisher) queue.Publisher {
	return publisher
}

func provideTxnDetailedStatusUpdateSnsPublisher(publisher types3.TxnDetailedStatusUpdateSnsPublisher) types3.TxnDetailedStatusUpdateSnsPublisher {
	return publisher
}

func convertPaymentProtocolPublishersToMap(intraBankEnquiryPublisher types2.IntraBankEnquiryPublisher, impsEnquiryPublisher types2.IMPSEnquiryPublisher, neftEnquiryPublisher types2.NEFTEnquiryPublisher, rtgsEnquiryPublisher types2.RTGSEnquiryPublisher,
	upiEnquiryPublisher types2.UPIEnquiryPublisher) map[payment.PaymentProtocol]queue.DelayPublisher {
	return map[payment.PaymentProtocol]queue.DelayPublisher{payment.PaymentProtocol_INTRA_BANK: intraBankEnquiryPublisher, payment.PaymentProtocol_IMPS: impsEnquiryPublisher, payment.PaymentProtocol_NEFT: neftEnquiryPublisher, payment.PaymentProtocol_RTGS: rtgsEnquiryPublisher, payment.PaymentProtocol_UPI: upiEnquiryPublisher}
}

func featureFlagConfProvider(conf *genconf.Config) *genconf.FeatureFlags {
	return conf.FeatureFlags()
}

func orderCacheStorageProvider(orderRedisStore types.OrderRedisStore) types2.OrderCacheStorage {
	return cache.NewRedisCacheStorage(orderRedisStore)
}

func orderCacheConfigProvider(config *genconf.Config) *genconf.OrderCacheConfig {
	return config.OrderCacheConfig()
}

func transactionCacheStorageProvider(orderRedisStore types.OrderRedisStore) types2.TransactionCacheStorage {
	return cache.NewRedisCacheStorage(orderRedisStore)
}

func transactionCacheConfigProvider(config *genconf.Config) *genconf.TransactionCacheConfig {
	return config.TransactionCacheConfig()
}

func ReleaseConfigProvider(conf *genconf.Config) *genconf3.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func PreApprovedLoansS3ClientProvider(awsConf aws.Config, orderConf *genconf.Config) s3.S3Client {
	s3Client := s3.NewClient(awsConf, orderConf.DeclineDataAwsSftpBucket().AwsBucket)
	return s3Client
}

func PgpCryptoProvider(cryptorStoreMap *cryptormap.InMemoryCryptorStore) (crypto.Cryptor, error) {
	return cryptorStoreMap.GetCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_PGP)
}

// Method used for instantiating Cryptors for encrypted data from vendor.
// These cryptors and then used to create an in-memory vendor-cryptor mapping in a Singleton fashion.
func InitCryptors(conf *genconf.Config) (*cryptormap.InMemoryCryptorStore, error) {

	pgpCryptor := pgp.New(conf.Secrets().Ids[config.FederalPgpPublicKey],
		conf.Secrets().Ids[config.EpifiFederalPgpPrivateKey], conf.Secrets().Ids[config.EpifiFederalPgpPassphrase])
	if pgpCryptor == nil {
		return nil, errors.New("failed to create PGP cryptor")
	}
	cryptorStore := &cryptormap.InMemoryCryptorStore{}
	cryptorStore.AddCryptor(vendorgateway.Vendor_FEDERAL_BANK, vendorgateway.CryptorType_PGP, pgpCryptor)

	return cryptorStore, nil
}

func FaasExecutorProvider(ctx context.Context, awsConf aws.Config, conf *genconf.Config) (faas.FaaSExecutor, error) {
	return faas2.NewFaaSExecutor(ctx, sqs.InitSQSClient(awsConf), namespace.Pay, conf.ProcrastinatorWorkflowPublisher())
}

func FeatureReleaseConfigProvider(conf *genconf.Config) *genconf3.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func PaymentProtocolDecisionsParamsProvider(config2 *genconf.Config) *genconf.PaymentProtocolDecisionParams {
	return config2.PaymentProtocolDecisionParams()
}

func RuleEngineParamsProvider(config2 *genconf.Config) *genconf.RuleEngineParams {
	return config2.RuleEngineParams()
}

func CustomRuleDEParamsProvider(config2 *genconf.Config) *genconf.CustomRuleDEParams {
	return config2.CustomRuleDEParams()
}

func CoolOffWindowProvider(config2 *genconf.Config) *cfg.TimeDuration {
	return config2.CoolOffWindow()
}

func SavingsLedgerReconCacheConfigProvider(conf *genconf.Config) *genconf.SavingsLedgerReconCacheConfig {
	return conf.SavingsLedgerReconCacheConfig()
}

func ReconPublisherProvider(publisher types3.SavingsLedgerReconPublisher) queue.Publisher {
	return publisher
}

func CacheConfigProvider(conf *genconf.Config) *genconf.SavingsLedgerReconCacheConfig {
	return conf.SavingsLedgerReconCacheConfig()
}

func ErrorRespCodesForPermanentFailureConfProvider(conf *genconf.Config) *genconf.ErrorRespCodesForPermanentFailure {
	return conf.ErrorRespCodesForPermanentFailure()
}

func EpifiTxnExecutorProviderForAA(conf *genconf.Config, conn types.EpifiCRDB) types.EpifiTxnExecutor {
	if conf.FeatureFlags().EnablePGDBDaoForAA() {
		var gormDB *gorm.DB = conn
		return storagev2.NewGormTxnExecutor(gormDB.Clauses(dbresolver.Use(conf.AaParams().PgdbConnAlias())))
	}

	return storagev2.NewCRDBIdempotentTxnExecutor(conn)
}

func EpifiWealthTxnExecutorProviderForAA(conf *genconf.Config, conn types.EpifiWealthCRDB) types.EpifiWealthTxnExecutor {
	if conf.FeatureFlags().EnablePGDBDaoForAA() {
		var gormDB *gorm.DB = conn
		return storagev2.NewGormTxnExecutor(gormDB.Clauses(dbresolver.Use(conf.AaParams().PgdbConnAlias())))
	}

	return storagev2.NewCRDBIdempotentTxnExecutor(conn)
}

func WorkerEntitySegregationFlagProvider(conf *genconf4.Config) types3.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in pay worker : %v", conf.EnableEntitySegregation()))
	return types3.EnableResourceProvider(conf.EnableEntitySegregation())
}

func OrderServerEntitySegregationFlagProvider(conf *genconf.Config) types3.EnableResourceProvider {
	logger.InfoNoCtx(fmt.Sprintf("Entity segregation flag in order config : %v", conf.EnableEntitySegregation()))
	return types3.EnableResourceProvider(conf.EnableEntitySegregation())
}
