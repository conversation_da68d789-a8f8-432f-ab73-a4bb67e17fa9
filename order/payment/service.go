package payment

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	timelinePb "github.com/epifi/gamma/api/timeline"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	"github.com/imdario/mergo"
	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	gormv2 "gorm.io/gorm"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	celestialPb "github.com/epifi/be-common/api/celestial"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/comms"
	merchantPb "github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	upiPb "github.com/epifi/gamma/api/upi"
	usersPb "github.com/epifi/gamma/api/user"
	merchantResolutionPb "github.com/epifi/gamma/api/vendorgateway/merchantresolution"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgUPIPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	config "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/dao"
	actorProcessor "github.com/epifi/gamma/order/internal/actor"
	"github.com/epifi/gamma/order/internal/health_engine"
	paymentProcessor "github.com/epifi/gamma/order/internal/payment"
	upiProcessor "github.com/epifi/gamma/order/internal/upi"
	"github.com/epifi/gamma/order/metrics"
	orderTypes "github.com/epifi/gamma/order/types"
	types2 "github.com/epifi/gamma/order/wire/types"
	payPkgTxns "github.com/epifi/gamma/pay/pkg/transactions"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

type Service struct {
	// UnimplementedPaymentServer is embedded to have forward compatible implementations
	paymentPb.UnimplementedPaymentServer

	txnDao                              dao.TransactionDao
	orderDao                            dao.OrderDao
	ovgPaymentClient                    vgPaymentPb.PaymentClient
	vgUPIClient                         vgUPIPb.UPIClient
	numberIDGen                         idgen.NumberIDGenerator
	piClient                            piPb.PiClient
	savingsClient                       savingsPb.SavingsClient
	authClient                          authPb.AuthClient
	actorClient                         actorPb.ActorClient
	userClient                          usersPb.UsersClient
	commsClient                         comms.CommsClient
	paymentEnquiryPublisherMap          map[paymentPb.PaymentProtocol]queue.DelayPublisher
	paymentOrchestrationPublisher       queue.DelayPublisher
	orderOrchestrationPublisher         orderTypes.OrderOrchestrationPublisher
	inPaymentOrderPublisher             orderTypes.InPaymentOrderUpdatePublisher
	eventBroker                         events.Broker
	conf                                *config.Config
	lockManager                         lock.ILockManager
	paymentConsumerClient               paymentPb.ConsumerClient
	accountPiClient                     accountPiPb.AccountPIRelationClient
	upiProcessor                        upiProcessor.UpiProcessor
	actorProcessor                      actorProcessor.ActorProcessor
	merchantClient                      merchantPb.MerchantServiceClient
	merchantResolutionVgClient          merchantResolutionPb.MerchantResolutionClient
	bankCustClient                      bankcust.BankCustomerServiceClient
	healthEngineProcessor               health_engine.HealthEngineProcessor
	paymentHealthProcessor              paymentProcessor.IPaymentHealthProcessor
	upiClient                           upiPb.UPIClient
	txnAmountBreakupDao                 dao.TransactionAmountBreakupDao
	celestialClient                     celestialPb.CelestialClient
	txnDetailedStatusUpdateSnsPublisher types2.TxnDetailedStatusUpdateSnsPublisher
	timelineClient                      timelinePb.TimelineServiceClient
	payV1Client                         payPb.PayClient
}

// Factory method for creating an instance of savings service. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewService(
	txnDao dao.TransactionDao,
	orderDao dao.OrderDao,
	ovgPaymentClient vgPaymentPb.PaymentClient,
	numberIDGen idgen.NumberIDGenerator,
	piClient piPb.PiClient,
	savingsClient savingsPb.SavingsClient,
	authClient authPb.AuthClient,
	vgUPIClient vgUPIPb.UPIClient,
	actorClient actorPb.ActorClient,
	userClient usersPb.UsersClient,
	commsClient comms.CommsClient,
	orderOrchestrationPublisher orderTypes.OrderOrchestrationPublisher,
	paymentEnquiryPublisherMap map[paymentPb.PaymentProtocol]queue.DelayPublisher,
	paymentOrchestrationPublisher queue.DelayPublisher,
	broker events.Broker,
	conf *config.Config,
	lockManager lock.ILockManager,
	paymentConsumerClient paymentPb.ConsumerClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	upiProcessor upiProcessor.UpiProcessor,
	actorProcessor actorProcessor.ActorProcessor,
	inPaymentOrderPublisher orderTypes.InPaymentOrderUpdatePublisher,
	merchantClient merchantPb.MerchantServiceClient,
	merchantResolutionVgClient merchantResolutionPb.MerchantResolutionClient,
	bcClient bankcust.BankCustomerServiceClient,
	paymentHealthProcessor paymentProcessor.IPaymentHealthProcessor,
	upiClient upiPb.UPIClient,
	txnAmountBreakupDao dao.TransactionAmountBreakupDao,
	celestialClient celestialPb.CelestialClient,
	txnDetailedStatusUpdateSnsPublisher types2.TxnDetailedStatusUpdateSnsPublisher,
	timelineClient timelinePb.TimelineServiceClient,
	payV1Client payPb.PayClient,
) *Service {
	return &Service{
		txnDao:                              txnDao,
		orderDao:                            orderDao,
		ovgPaymentClient:                    ovgPaymentClient,
		vgUPIClient:                         vgUPIClient,
		numberIDGen:                         numberIDGen,
		piClient:                            piClient,
		savingsClient:                       savingsClient,
		authClient:                          authClient,
		actorClient:                         actorClient,
		userClient:                          userClient,
		commsClient:                         commsClient,
		paymentEnquiryPublisherMap:          paymentEnquiryPublisherMap,
		paymentOrchestrationPublisher:       paymentOrchestrationPublisher,
		orderOrchestrationPublisher:         orderOrchestrationPublisher,
		eventBroker:                         broker,
		conf:                                conf,
		lockManager:                         lockManager,
		paymentConsumerClient:               paymentConsumerClient,
		accountPiClient:                     accountPiClient,
		upiProcessor:                        upiProcessor,
		actorProcessor:                      actorProcessor,
		inPaymentOrderPublisher:             inPaymentOrderPublisher,
		merchantClient:                      merchantClient,
		merchantResolutionVgClient:          merchantResolutionVgClient,
		bankCustClient:                      bcClient,
		paymentHealthProcessor:              paymentHealthProcessor,
		upiClient:                           upiClient,
		txnAmountBreakupDao:                 txnAmountBreakupDao,
		celestialClient:                     celestialClient,
		txnDetailedStatusUpdateSnsPublisher: txnDetailedStatusUpdateSnsPublisher,
		timelineClient:                      timelineClient,
		payV1Client:                         payV1Client,
	}
}

var amountBreakupTypeMap = map[upiPb.UpiInternationalPaymentChargeType]paymentPb.AmountBreakupType{
	upiPb.UpiInternationalPaymentChargeType_UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_UNSPECIFIED: paymentPb.AmountBreakupType_AMOUNT_BREAKUP_TYPE_UNSPECIFIED,
	upiPb.UpiInternationalPaymentChargeType_UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_MARKUP:      paymentPb.AmountBreakupType_AMOUNT_BREAKUP_TYPE_MARKUP,
	upiPb.UpiInternationalPaymentChargeType_UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_GST:         paymentPb.AmountBreakupType_AMOUNT_BREAKUP_TYPE_GST,
	upiPb.UpiInternationalPaymentChargeType_UPI_INTERNATIONAL_PAYMENT_CHARGE_TYPE_AMOUNT:      paymentPb.AmountBreakupType_AMOUNT_BREAKUP_TYPE_BASE_AMOUNT,
}

// CreateTransaction creates a transaction entry in the database with CREATED status.
func (s *Service) CreateTransaction(ctx context.Context, req *paymentPb.CreateTransactionRequest) (*paymentPb.CreateTransactionResponse, error) {
	// TODO(nitesh): figure out where to get the partner bank and protocol field from when we have more clarity on the
	// decision engine.
	var (
		res      = &paymentPb.CreateTransactionResponse{}
		err      error
		savedTxn *paymentPb.Transaction
		dedupeId *paymentPb.DedupeId
	)

	// validateFederalOwnershipForTxn is a safety check to ensure we don't assign any
	// other ownership, if data belongs to federal
	// we can live with some extra data being sent to Federal
	// but keeping FEDERAL_BANK data with us would be a compliance issue
	if err = s.validateFederalOwnershipForTxn(ctx, req); err != nil {
		logger.Error(ctx, "non success code while validating ownership for the txn", zap.String(logger.ORDER_ID, req.GetOrderId()),
			zap.Error(err))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	validErr := validateCreateTransactionRequest(req)
	if validErr != nil {
		logger.Error(ctx, "invalid transaction creation request: %v",
			zap.Error(validErr))
		res.Status = rpc.StatusInvalidArgument()
		res.Status.SetDebugMessage(validErr.Error())
		return res, nil
	}

	// For on-app transaction, we must have request-id, so dedupe id will be populated with this
	if req.GetReqInfo().GetReqId() != "" {
		dedupeId = &paymentPb.DedupeId{
			RequestId: req.GetReqInfo().GetReqId(),
		}
	}

	transaction := &paymentPb.Transaction{
		PiFrom:            req.GetPiFrom(),
		PiTo:              req.GetPiTo(),
		PartnerBank:       commonvgpb.Vendor_FEDERAL_BANK,
		Amount:            req.GetAmount(),
		Status:            req.GetStatus(),
		PaymentProtocol:   req.GetPaymentProtocol(),
		ProtocolStatus:    req.GetProtocolStatus(),
		Remarks:           req.GetRemarks(),
		PartnerExecutedAt: req.GetExecutedAt(),
		DetailedStatus:    req.DetailedStatus,
		Utr:               req.GetUtr(),
		DedupeId:          dedupeId,
		Ownership:         req.GetOwnership(),
	}

	savedTxn, err = s.createTransactionHelper(ctx, req.GetBaseAmountQuoteCurrency(), req.GetAmount(), req.GetCurrentActorId(), transaction, req.ReqInfo, req.OrderId)
	if err != nil {
		logger.Error(ctx, "failed to create transaction entry",
			zap.String("order-id", req.OrderId),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	bankName, _ := payPkgTxns.GetBankNameForPiId(ctx, savedTxn.GetPiFrom(), s.piClient)
	metrics.IncrementOnAppTxnStatusCount(savedTxn.GetStatus(), savedTxn.GetPaymentProtocol(), bankName)

	if savedTxn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
		s.upiProcessor.AddUpiMetricsForPspAsync(ctx, savedTxn.GetStatus(), savedTxn.GetPiFrom(), savedTxn.GetPiTo())
	}

	res.Status = rpc.StatusOk()
	res.Transaction = savedTxn
	return res, nil
}

// validateOrderCreationRequest performs application level validation checks e.g.
// checks if pi from and to are similar
func validateCreateTransactionRequest(req *paymentPb.CreateTransactionRequest) error {
	if req.PiFrom == req.PiTo {
		return fmt.Errorf("pi from and pi to can't be same")
	}

	if !money.IsValid(req.Amount) {
		return fmt.Errorf("invalid transaction amount: %s", req.Amount.String())
	}

	if req.ExecutedAt != nil && req.Status != paymentPb.TransactionStatus_FAILED && req.Status != paymentPb.TransactionStatus_SUCCESS {
		return fmt.Errorf("executed_at must be populated for only for terminal states")
	}

	return nil
}

// GetTransaction fetches transaction details from the database
func (s *Service) GetTransaction(ctx context.Context, req *paymentPb.GetTransactionRequest) (*paymentPb.GetTransactionResponse, error) {
	var (
		res         = &paymentPb.GetTransactionResponse{}
		identifier  string
		transaction *paymentPb.Transaction
		reqInfo     *paymentPb.PaymentRequestInformation
		err         error
	)

	switch req.GetIdentifier().(type) {
	case *paymentPb.GetTransactionRequest_TransactionId:
		identifier = req.GetTransactionId()
		transaction, err = s.txnDao.GetById(ctx, identifier)
	case *paymentPb.GetTransactionRequest_PartnerRefId:
		identifier = req.GetPartnerRefId()
		transaction, err = s.txnDao.GetByPartnerTransactionId(ctx, identifier)
	case *paymentPb.GetTransactionRequest_Utr:
		identifier = req.GetUtr()
		transaction, err = s.txnDao.GetByUTR(ctx, identifier)
	case *paymentPb.GetTransactionRequest_ReqId:
		identifier = req.GetReqId()
		transaction, err = s.txnDao.GetByReqId(ctx, identifier, false)
	default:
		logger.Error(ctx, "invalid transaction identifier")
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	if err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
		} else {
			logger.Error(ctx, "unable to fetch transaction",
				zap.String("identifier", identifier),
				zap.Any("type", req.GetIdentifier()),
				zap.Error(err))
			res.Status = rpc.StatusInternal()
		}
		return res, nil
	}

	if req.GetReqInfo {
		_, reqInfo, err = s.txnDao.GetByIdWithPaymentReqInfo(ctx, transaction.Id)
		if err != nil {
			logger.Error(ctx, "error while fetching payment req info",
				zap.String(logger.TXN_ID, transaction.Id),
				zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	res.Transaction = transaction
	res.ReqInfo = reqInfo
	res.Status = rpc.StatusOk()
	return res, nil
}

// UpdateTransaction updates transaction fields. Only fields in the specified field masks are updated
func (s *Service) UpdateTransaction(ctx context.Context, req *paymentPb.UpdateTransactionRequest) (*paymentPb.UpdateTransactionResponse, error) {
	var (
		res               = &paymentPb.UpdateTransactionResponse{}
		isTxnStateUpdated bool
		savedTxn          *paymentPb.Transaction
		err               error
	)
	if isTxnStateUpdated = paymentProcessor.SearchInFieldMasks(req.GetFieldMasks(), paymentPb.TransactionFieldMask_STATUS); isTxnStateUpdated {
		savedTxn, err = s.txnDao.GetById(ctx, req.GetTransaction().GetId())
		switch {
		case err != nil && (errors.Is(err, gormv2.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound)):
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		case err != nil:
			logger.Error(ctx, "failed to get txn by id", zap.String(logger.TXN_ID, req.GetTransaction().GetId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}

		err = mergo.Merge(savedTxn, req.GetTransaction(), mergo.WithOverride)
		if err != nil {
			logger.Error(ctx, "failed to merge requested txn with txn in DB", zap.String(logger.TXN_ID, req.GetTransaction().GetId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	txnErr := storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		err = s.txnDao.Update(txnCtx, req.Transaction, req.ReqInfo, req.FieldMasks)
		if err != nil {
			return err
		}

		if isTxnStateUpdated {
			err = s.publishToOrderQueue(txnCtx, req.GetTransaction())
			if err != nil {
				return fmt.Errorf("failed to publish event to order queue: %w", err)
			}
		}

		publishErr := payPkg.PublishTxnDetailedStatusUpdateEventIfRequired(txnCtx, req.GetTransaction(), s.txnDetailedStatusUpdateSnsPublisher, req.GetFieldMasks(), false)
		if publishErr != nil {
			logger.Error(txnCtx, "failed to publish event to txn detailed status update topic", zap.Error(publishErr))
		}

		return nil
	})
	if txnErr != nil {
		if errors.Is(txnErr, epifierrors.ErrRecordNotFound) {
			res.Status = rpc.StatusRecordNotFound()
			return res, nil
		}

		logger.Error(ctx, "failed to update the txn",
			zap.String(logger.TXN_ID, req.GetTransaction().GetId()), zap.Error(txnErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if isTxnStateUpdated {
		bankName, _ := payPkgTxns.GetBankNameForPiId(ctx, savedTxn.GetPiFrom(), s.piClient)
		metrics.RecordOnAppTxnStateChangeMetrics(savedTxn, bankName)

		if savedTxn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
			s.upiProcessor.AddUpiMetricsForPspAsync(ctx, savedTxn.GetStatus(), savedTxn.GetPiFrom(), savedTxn.GetPiTo())
		}
		if req.GetTransaction().GetStatus() == paymentPb.TransactionStatus_FAILED {
			metrics.IncrementOnAppTxnFailureCount(req.GetTransaction().GetLatestTxnDetailedStatus().GetErrorCategory(), req.GetTransaction().GetPaymentProtocol())
		}
	}

	res.Status = rpc.StatusOk()
	if s.conf.IsPushingPaymentsMetricsToHealthEngineEnabled() {
		// push the metrics to the health engine
		pushHealthMetricsErr := s.paymentHealthProcessor.PushMetrics(ctx, req.GetTransaction(), req.GetTransaction().GetStatus(), req.GetFieldMasks())
		// any error encountered in pushing the health metrics is only logged and not propagated since it should not block the payments flow
		if pushHealthMetricsErr != nil {
			logger.Error(ctx, "failed to push health metrics for the transaction", zap.String(logger.TXN_ID, req.GetTransaction().GetId()), zap.Error(pushHealthMetricsErr))
		}
	}
	return res, nil
}

// GetOrderId returns an order id associated with a given transaction
func (s *Service) GetOrderId(ctx context.Context, req *paymentPb.GetOrderIdRequest) (*paymentPb.GetOrderIdResponse, error) {
	res := &paymentPb.GetOrderIdResponse{}

	orderId, err := s.txnDao.GetOrderId(ctx, req.TransactionId)
	switch {
	case errors.Is(err, sql.ErrNoRows):
		logger.Error(ctx, "record not found", zap.String("txn-id", req.TransactionId), zap.Error(err))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "not able to fetch order id associated with transaction",
			zap.String("txn-id", req.TransactionId), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.OrderId = orderId
	res.Status = rpc.StatusOk()
	return res, nil
}

// GetTxnsByPi fetches a list of all successful transactions filtered by piFrom and piTo and ordered by the
// sortBy filed in the request message. The sorting order is defined by the boolean flag sortDesc which if set to true
// sorts the transactions in descending order.
//
// As the number of transactions could grow huge, the RPC returns the list of transactions in pages.
// The number of transactions returned will be bounded by the page_size specified, whereas offset can be used to
// fetch the next set of entries to the already returned ones.
// If the statuses is empty or nil, the RPC fetches transactions for all possible states
func (s *Service) GetTxnsByPi(ctx context.Context, req *paymentPb.GetTxnsByPiRequest) (*paymentPb.GetTxnsByPiResponse, error) {
	var (
		res              = &paymentPb.GetTxnsByPiResponse{}
		fromTime, toTime *time.Time
	)

	if req.FromTimestamp != nil {
		startTime := req.GetFromTimestamp().AsTime()
		fromTime = &startTime
	}
	if req.ToTimestamp != nil {
		endTime := req.GetToTimestamp().AsTime()
		toTime = &endTime
	}

	transactions, err := s.txnDao.GetTxnsByPi(ctx, req.GetPiFrom(), req.GetPiTo(), fromTime, toTime, req.GetSortBy(),
		req.GetSortDesc(), req.GetPageSize(), req.GetOffset(), req.GetStatuses())
	if err != nil {
		logger.Error(ctx, "failed to fetch transactions from DB",
			zap.Any("request", req),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if len(transactions) == 0 {
		logger.Info(ctx, "No transactions found for PIs",
			zap.Any("request", req))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}

	res.Transactions = transactions
	res.Status = rpc.StatusOk()
	return res, nil
}

// ReInitiateTransactionEnquiry is an async API, which initiates a transaction enquiry in the system.
// It publishes an event to payment enquiry queue for the respective protocol
//
// Positive acknowledgment from the API DOESN'T MEAN successful commencement of the payment.
// A client must fetch the status from the server.
func (s *Service) ReInitiateTransactionEnquiry(ctx context.Context, req *paymentPb.ReInitiateTransactionEnquiryRequest) (*paymentPb.ReInitiateTransactionEnquiryResponse, error) {
	res := &paymentPb.ReInitiateTransactionEnquiryResponse{}

	txn, err := s.txnDao.GetById(ctx, req.GetTransactionId())
	if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, gormv2.ErrRecordNotFound) {
		logger.Error(ctx, "txn not found", zap.Error(err))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	}

	if err != nil {
		logger.Error(ctx, "failed to fetch txn from DB", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	consumerRes, err := s.paymentConsumerClient.ProcessPayment(ctx, &paymentPb.ProcessPaymentRequest{
		RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
		TransactionId: txn.Id,
		PhoneNumber:   req.GetPhoneNumber().ToString(),
		ActorId:       req.GetActorId(),
		CustomerId:    req.GetCustomerId(),
	})
	if err != nil {
		logger.Error(ctx, "failed to publish event for payment enquiry", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Status = rpc.StatusOkWithDebugMsg(consumerRes.GetResponseHeader().GetStatus().String())
	return res, nil
}

func (s *Service) GetOrdersInfoForTransactions(ctx context.Context, req *paymentPb.GetOrdersInfoForTransactionsRequest) (*paymentPb.GetOrdersInfoForTransactionsResponse, error) {
	var (
		res  = &paymentPb.GetOrdersInfoForTransactionsResponse{}
		utrs []string
	)

	for _, reqObj := range req.GetTransactionIdentifiers() {
		switch reqObj.GetIdentifier().(type) {
		case *paymentPb.TransactionIdentifier_Utr:
			utrs = append(utrs, reqObj.GetUtr())
		default:
			logger.Error(ctx, "invalid transaction identifier")
			res.Status = rpc.StatusInvalidArgument()
			return res, nil
		}
	}

	orderWithTxns, err := s.txnDao.GetOrderWithTransactionIdsForUTRs(ctx, utrs)
	switch {
	case errors.Is(err, gormv2.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound):
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "failed to fetch orders id and transaction id", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	default:
		res.OrderTransactionId = orderWithTxns
		res.Status = rpc.StatusOk()
		return res, nil
	}
}

// GetTransactionsCount returns the count of transactions for given actor. If pi_ids of the actor is passed instead
// of the actor ID, then the transactions corresponding to that PI are fetched instead.
// The startTime & endTime fields are mandatory and the interval between the start & end times must be less than or
// equal to 2 days otherwise InvalidArgument error is returned.
// The transaction count returned may be less than the true value within the given window for certain edge
// cases (documented in GetTransactionsCount DAO method implementation in gamma/order/dao/transaction.go). This was a
// conscious call taken to keep the RPC performant enough for older time-windows.
// Deprecated: In favour of pay.GetTransactionAggregates. Query for this RPC will impact CRDB cluster.
func (s *Service) GetTransactionsCount(ctx context.Context, req *paymentPb.GetTransactionsCountRequest) (*paymentPb.GetTransactionsCountResponse, error) {
	var (
		res = &paymentPb.GetTransactionsCountResponse{}
	)
	piIds := req.GetPiIds()
	if len(req.GetPiIds()) == 0 {
		accountPiRes, err := s.accountPiClient.GetByActorId(ctx, &accountPiPb.GetByActorIdRequest{ActorId: req.GetActorId()})
		if grpcError := epifigrpc.RPCError(accountPiRes, err); grpcError != nil {
			logger.Error(ctx, "failed to fetch account pis", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		for _, accountPi := range accountPiRes.GetAccountPis() {
			if len(req.GetAccountTypes()) == 0 || funk.Contains(req.GetAccountTypes(), accountPi.GetAccountType()) {
				piIds = append(piIds, accountPi.GetPiId())
			}
		}
	}

	txnCount, err := s.txnDao.GetTransactionsCount(ctx, piIds, req.GetOtherPiIds(), req.GetTransactionType(),
		datetime.TimestampToTime(req.GetStartTime()), datetime.TimestampToTime(req.GetEndTime()), req.GetPaymentProtocols(), req.GetTransactionStatus())
	if err != nil {
		logger.Error(ctx, "error fetching txn count for piIds", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.Count = txnCount
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) getDsMerchantIdFromMerchantResolutionApi(ctx context.Context, name string) (string, error) {
	res, err := s.merchantResolutionVgClient.MerchantResolution(ctx, &merchantResolutionPb.MerchantResolutionRequest{
		Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
		RawMerchantName: name,
		IsMerchantTxn:   true,
		IsFiTxn:         true,
	})

	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return "", rpcErr
	}

	return res.GetDsMerchantId(), nil
}

// validateFederalOwnershipForTxn is a safety check to ensure that ownership is FEDERAL if any of the following conditions is true
// - txn is non upi
// - workflows is NO_OP
func (s *Service) validateFederalOwnershipForTxn(ctx context.Context, req *paymentPb.CreateTransactionRequest) error {
	order, err := s.orderDao.GetById(ctx, req.GetOrderId())
	if err != nil {
		return fmt.Errorf("error fetching order for the order-id = %s, error = %w", req.GetOrderId(), err)
	}

	if (req.GetPaymentProtocol() != paymentPb.PaymentProtocol_UPI || order.GetWorkflow() == orderPb.OrderWorkflow_NO_OP || order.GetWorkflow() == orderPb.OrderWorkflow_OFF_APP_UPI) && req.GetOwnership() != commontypes.Ownership_FEDERAL_BANK {
		return fmt.Errorf("invalid ownerhip found for the txn: ownership = %s, err = %w", req.GetOwnership().String(), epifierrors.ErrInvalidArgument)
	}
	return nil
}

// createTransactionAmountBreakup - validates the amount conversions b/w INR and foreign currency
// and creates entries for amount breakups in transaction_amount_breakups table.
func (s *Service) createTransactionAmountBreakup(ctx context.Context, baseAmountQuoteCurrency, inrAmount *gmoney.Money, txnId string, actorId string) ([]*paymentPb.TransactionAmountBreakup, error) {
	var (
		internationalPaymentForexDetailResponse *upiPb.ValidateInternationalPaymentResponse
		err                                     error
	)

	internationalPaymentForexDetailResponse, err = s.upiClient.ValidateInternationalPayment(ctx, &upiPb.ValidateInternationalPaymentRequest{
		ActorId:                 actorId,
		BaseAmountQuoteCurrency: baseAmountQuoteCurrency,
		TotalAmountInr:          inrAmount,
	})
	if err = epifigrpc.RPCError(internationalPaymentForexDetailResponse, err); err != nil {
		return nil, fmt.Errorf("unable to validate forex detail: %w", err)
	}

	return getTxnAmountBreakupsForIntlUpiPayment(txnId, internationalPaymentForexDetailResponse.GetUpiInternationalPaymentCharges()), nil
}

// getTxnAmountBreakupsForIntlUpiPayment -
// converts the upiInternationalPaymentsCharges to transaction amount breakups
// in order to persist all the different different charges applied on the international
// transaction. E.g. Markup, Gst etc.
func getTxnAmountBreakupsForIntlUpiPayment(txnId string, upiInternationalPaymentsCharges []*upiPb.UpiInternationalPaymentCharge) []*paymentPb.TransactionAmountBreakup {
	transactionAmountBreakups := make([]*paymentPb.TransactionAmountBreakup, 0)

	for _, upiInternationalPaymentCharge := range upiInternationalPaymentsCharges {
		transactionAmountBreakups = append(transactionAmountBreakups, &paymentPb.TransactionAmountBreakup{
			Amount:                   types.GetFromBeMoney(upiInternationalPaymentCharge.GetAmount()),
			TransactionId:            txnId,
			BreakupType:              amountBreakupTypeMap[upiInternationalPaymentCharge.GetUpiInternationalPaymentChargeType()],
			ConversionRate:           upiInternationalPaymentCharge.GetConversionRate(),
			OverheadChargePercentage: upiInternationalPaymentCharge.GetOverheadChargePercentage(),
			ConversionRateSource:     paymentPb.ConversionRateSource_Conversion_Rate_Source_NPCI_QR,
		})
	}
	return transactionAmountBreakups
}

// createTransactionHelper will create entry in txn dao and txn amount breakup dao using transactional block
func (s *Service) createTransactionHelper(ctx context.Context, baseAmountQuoteCurrency, inrAmount *gmoney.Money, actorId string, transaction *paymentPb.Transaction, reqInfo *paymentPb.PaymentRequestInformation,
	orderId string) (*paymentPb.Transaction, error) {
	var (
		savedTxn                           *paymentPb.Transaction
		transactionAmountBreakupDaoRequest []*paymentPb.TransactionAmountBreakup
		err                                error
	)

	txnErr := storagev2.RunCRDBTxn(ctx, func(txnCtx context.Context) error {
		savedTxn, err = s.txnDao.Create(txnCtx, transaction, reqInfo, orderId, commontypes.Ownership_EPIFI_TECH)
		if err != nil {
			logger.Error(ctx, "failed to create transaction",
				zap.String(logger.ORDER_ID, transaction.OrderId),
				zap.Error(err))
			return err
		}

		if baseAmountQuoteCurrency != nil {
			transactionAmountBreakupDaoRequest, err = s.createTransactionAmountBreakup(txnCtx, baseAmountQuoteCurrency, inrAmount, savedTxn.GetId(), actorId)
			if err != nil {
				logger.Error(ctx, "failed to create transaction amount breakup dao request",
					zap.String(logger.TXN_ID, savedTxn.Id),
					zap.Error(err))
				return err
			}
			if _, err = s.txnAmountBreakupDao.BatchCreate(txnCtx, transactionAmountBreakupDaoRequest); err != nil {
				logger.Error(ctx, "failed to create transaction amount breakup",
					zap.String(logger.TXN_ID, savedTxn.Id),
					zap.Error(err))
				return err
			}
		}
		return nil
	})
	if txnErr != nil {
		return nil, fmt.Errorf("unable to create transaction : txn-id = %v, err = %w", transaction.GetId(), txnErr)
	}
	return savedTxn, nil
}
