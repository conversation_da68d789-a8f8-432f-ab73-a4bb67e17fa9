package payment_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"flag"
	"os"
	"sync"
	"testing"
	"time"

	"github.com/golang/protobuf/ptypes"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/order/dao"
	"github.com/epifi/gamma/order/payment"
	"github.com/epifi/gamma/order/test"
)

const (
	fixturesOrder1Id          = "order-1"
	fixturesOrder3Id          = "order-3"
	fixturesOrder11Id         = "order-11"
	fixturesOrder12Id         = "order-12"
	fixturesOrder14Id         = "order-14"
	fixtureOrder52Id          = "order-52"
	fixturesTransaction3ReqId = "req-3"
)

var (
	initialiseSameDbOnce sync.Once

	fixturesTransaction1 = &paymentPb.Transaction{
		Id:          "transaction-1",
		PiFrom:      "pi-1",
		PiTo:        "pi-2",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_CREATED,
		PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
		Remarks:         "XYZ",
		ReqId:           "req-1",
		OrderId:         "order-1",
	}

	fixturesTransaction6 = &paymentPb.Transaction{
		Id:           "transaction-6",
		PiFrom:       "pi-1",
		PiTo:         "pi-2",
		PartnerRefId: "FED-6",
		Utr:          "FED-6",
		PartnerBank:  commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        2000,
		},
		Status:          paymentPb.TransactionStatus_SUCCESS,
		PaymentProtocol: paymentPb.PaymentProtocol_IMPS,
		Remarks:         "XYZ",
		ReqId:           "req-6",
		OrderId:         "order-6",
	}

	fixturesTransaction11 = &paymentPb.Transaction{
		Id:          "transaction-11",
		PiFrom:      "pi-3",
		PiTo:        "pi-2",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_CREATED,
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		Remarks:         "upi-txn",
		ReqId:           "req-11",
		OrderId:         "order-11",
	}

	fixturesTransaction52 = &paymentPb.Transaction{
		Id:          "transaction-52",
		PiFrom:      "pi-3",
		PiTo:        "pi-2",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_CREATED,
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		Remarks:         "upi-txn",
		ReqId:           "req-52",
		OrderId:         "order-52",
	}

	fixturesTransaction12 = &paymentPb.Transaction{
		Id:          "transaction-12",
		PiFrom:      "pi-ex-3",
		PiTo:        "pi-3",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_CREATED,
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		Remarks:         "upi-collect-txn",
		ReqId:           "req-12",
		OrderId:         "order-12",
	}

	_ = &paymentPb.Transaction{
		Id:          "transaction-13",
		PiFrom:      "pi-2",
		PiTo:        "pi-3",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_CREATED,
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		Remarks:         "upi-collect-short-circuit-txn",
		ReqId:           "req-13",
		OrderId:         "order-13",
	}

	fixturesTransaction14 = &paymentPb.Transaction{
		Id:          "transaction-14",
		PiFrom:      "pi-3",
		PiTo:        "pi-ex-3",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        1000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_CREATED,
		ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		Remarks:         "incoming-upi-collect-from-ex-payee-psp",
		ReqId:           "req-14",
		OrderId:         "order-14",
	}

	fixturesTransaction14ReqInfo = &paymentPb.PaymentRequestInformation{
		ReqId: "req-14",
		UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
			MerchantRefId: "ref-14",
		},
	}

	fixturesTransaction8 = &paymentPb.Transaction{
		Id:           "transaction-8",
		PiFrom:       "pi-1",
		PiTo:         "pi-2",
		Utr:          "FED-8",
		PartnerRefId: "FED-8",
		PartnerBank:  commonvgpb.Vendor_FEDERAL_BANK,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        2000,
			Nanos:        0,
		},
		Status:          paymentPb.TransactionStatus_SUCCESS,
		PaymentProtocol: paymentPb.PaymentProtocol_NEFT,
		Remarks:         "XYZ",
		ReqId:           "req-8",
		OrderId:         "order-8",
	}

	affectedTestTables = []string{"transactions", "orders"}
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()

	dbName, conf, db, teardown := test.InitTestServer()

	idg := idgen.NewDomainIdGenerator(idgen.NewClock())
	txnDao := dao.NewTransactionDao(db, nil, idg, nil, nil, false, nil)
	orderDao := dao.NewOrderDao(db, idg, nil, false, nil, nil)
	txnAmountBreakupDao := dao.NewTransactionAmountBreakupDao(db)
	paymentServer := payment.NewService(txnDao, orderDao, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, txnAmountBreakupDao, nil, nil, nil, nil)
	pts = NewPaymentServiceTestSuite(db, conf, txnDao, orderDao, paymentServer, dbName)

	numberIdGen := idgen.NewNumberIdGenerator(idgen.NewCryptoSeededSource())
	ts = NewInitiateTransactionTestSuite(db, conf, txnDao, orderDao, numberIdGen, nil, dbName)

	// parses the partner executed set in fixtures to proto.timeStamp
	tim, err := time.Parse("2006-01-02 15:04:05.999999", "2020-03-29 07:30:52.940000")
	if err != nil {
		logger.Fatal("failed to parse to time")
	}

	protoTime, err := ptypes.TimestampProto(tim)
	if err != nil {
		logger.Fatal("failed to parse to proto time")
	}

	fixturesTransaction6.PartnerExecutedAt = protoTime

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
