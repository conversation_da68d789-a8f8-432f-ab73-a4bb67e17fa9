// nolint: goimports
package payment_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	timelineMocks "github.com/epifi/gamma/api/timeline/mocks"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	eventsMock "github.com/epifi/be-common/pkg/events/mocks"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"

	actorPb "github.com/epifi/gamma/api/actor"
	actorMock "github.com/epifi/gamma/api/actor/mocks"
	authPb "github.com/epifi/gamma/api/auth"
	authMock "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/bankcust"
	bankCustMocks "github.com/epifi/gamma/api/bankcust/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	domainPb "github.com/epifi/gamma/api/order/domain"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	upiPb "github.com/epifi/gamma/api/upi"
	userMock "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/mocks"
	"github.com/epifi/gamma/order/dao/mocks"
	mocksUpiProcessor "github.com/epifi/gamma/order/internal/upi/mocks"
	"github.com/epifi/gamma/order/payment"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

func TestService_ProcessAddFundTransaction(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	payloadReq := &upiPb.UrnTransferInfo{
		PiTo:          "pi-to",
		ActorTo:       "actor-to",
		TxnId:         "upi-req-id",
		NextOrderInfo: nil,
	}
	collectPayloadReq := &paymentPb.AddFundsCollect{
		PaymentDetails: &paymentPb.PaymentDetails{
			PiTo:  "pi-to",
			ReqId: "upi-req-id",
		},
	}
	payload, err := protojson.Marshal(payloadReq)
	if err != nil {
		t.Error("error in building req")
		return
	}
	collectPayload, err := protojson.Marshal(collectPayloadReq)
	if err != nil {
		t.Error("error in building req")
		return
	}

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockTxnDao := mocks.NewMockTransactionDao(ctr)
	mockOrderDao := mocks.NewMockOrderDao(ctr)
	mockAuthClient := authMock.NewMockAuthClient(ctr)
	mockActorClient := actorMock.NewMockActorClient(ctr)
	mockUserClient := userMock.NewMockUsersClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockBCClient := bankCustMocks.NewMockBankCustomerServiceClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockEventsBroker := eventsMock.NewMockBroker(ctr)
	mockEventsBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	svc := payment.NewService(
		mockTxnDao,
		mockOrderDao,
		mockVgPaymentClient,
		ts.idg,
		mockPiClient,
		nil,
		mockAuthClient,
		nil,
		mockActorClient,
		mockUserClient,
		nil,
		nil,
		nil,
		nil,
		mockEventsBroker,
		ts.conf,
		nil,
		nil,
		nil,
		mockUpiProcessor,
		nil,
		mockInPaymentPublisher,
		nil,
		nil,
		mockBCClient,
		nil,
		nil,
		nil,
		nil,
		nil,
		mockTimelineClient,
		nil,
	)

	type mockGetOrderWithTxns struct {
		enable     bool
		externalId string
		want       *orderPb.OrderWithTransactions
		err        error
	}

	type mockCreateTxn struct {
		enable        bool
		txn           *paymentPb.Transaction
		paymentRqInfo *paymentPb.PaymentRequestInformation
		orderId       string
		want          *paymentPb.Transaction
		err           error
	}

	type mockVgAddFundStatus struct {
		enable bool
		req    *vgPaymentPb.GetAddFundStatusRequest
		want   *vgPaymentPb.GetAddFundStatusResponse
		err    error
	}

	type mockUpdateAndChangeStatus struct {
		txn           *paymentPb.Transaction
		payReqInfo    *paymentPb.PaymentRequestInformation
		updateMask    []paymentPb.TransactionFieldMask
		currentStatus paymentPb.TransactionStatus
		nextStatus    paymentPb.TransactionStatus
		err           error
	}

	type mockUpdateTxnStatus struct {
		enable     bool
		req        *paymentPb.Transaction
		payReqInfo *paymentPb.PaymentRequestInformation
		updateMask []paymentPb.TransactionFieldMask
		err        error
	}

	type mockGetDeviceAuth struct {
		enable bool
		req    *authPb.GetDeviceAuthRequest
		res    *authPb.GetDeviceAuthResponse
		err    error
	}

	type mockGetEntityDetailByActorId struct {
		enable bool
		req    *actorPb.GetEntityDetailsByActorIdRequest
		res    *actorPb.GetEntityDetailsByActorIdResponse
		err    error
	}

	type mockGetByReqId struct {
		enable bool
		reqId  string
		want   *paymentPb.Transaction
		err    error
	}

	type mockGetBankCustomerDetails struct {
		enable bool
		req    *bankcust.GetBankCustomerRequest
		res    *bankcust.GetBankCustomerResponse
		err    error
	}

	type mockIsNewAddFundsVpaEnabledForActor struct {
		enable  bool
		actorId string
		want    bool
		err     error
	}

	type mockGetPiById struct {
		enable bool
		req    *piPb.GetPiByIdRequest
		res    *piPb.GetPiByIdResponse
		err    error
	}

	tests := []struct {
		name                                string
		req                                 *domainPb.ProcessSettlementRequest
		want                                *domainPb.ProcessSettlementResponse
		mockGetOrderWithTxns                mockGetOrderWithTxns
		mockCreateTxn                       mockCreateTxn
		mockGetDeviceAuth                   mockGetDeviceAuth
		mockGetEntityDetailByActorId        mockGetEntityDetailByActorId
		mockGetBankCustomerDetails          mockGetBankCustomerDetails
		mockVgAddFundStatus                 mockVgAddFundStatus
		mockUpdateAndChangeStatuses         []*mockUpdateAndChangeStatus
		mockUpdateTxnStatus                 mockUpdateTxnStatus
		mockGetByReqId                      mockGetByReqId
		mockIsNewAddFundsVpaEnabledForActor mockIsNewAddFundsVpaEnabledForActor
		mockGetPiById                       mockGetPiById
		wantErr                             bool
	}{
		{
			name: "successfully process add fund transaction with vg success enquiry",
			req: &domainPb.ProcessSettlementRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "external-order-id",
					IsLastAttempt:   false,
				},
				Payload: payload,
			},
			mockIsNewAddFundsVpaEnabledForActor: mockIsNewAddFundsVpaEnabledForActor{
				enable:  true,
				actorId: "actor-id",
				want:    false,
			},
			want: &domainPb.ProcessSettlementResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			mockGetOrderWithTxns: mockGetOrderWithTxns{
				enable:     true,
				externalId: "external-order-id",
				want: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:           "order-id",
						ToActorId:    "actor-id",
						Status:       orderPb.OrderStatus_IN_SETTLEMENT,
						Workflow:     orderPb.OrderWorkflow_ADD_FUNDS,
						OrderPayload: payload,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
					},
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: true,
				txn: &paymentPb.Transaction{
					PiFrom:      "paymentinstrument-pool-account-federal",
					PiTo:        "pi-to",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
						Nanos:        0,
					},
					Status:          paymentPb.TransactionStatus_CREATED,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					Remarks:         payPkg.AddFundRemarks,
					Utr:             "upi-req-id",
					DedupeId: &paymentPb.DedupeId{
						RequestId: payPkg.AddFundSecondLegReqIdPrefix + "upi-req-id",
					},
					Ownership: commontypes.Ownership_FEDERAL_BANK,
				},
				paymentRqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "ADD_FUND:upi-req-id",
				},
				orderId: "order-id",
				want: &paymentPb.Transaction{
					Id:          "txn-id",
					PiFrom:      "paymentinstrument-pool-account-federal",
					PiTo:        "pi-to",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
						Nanos:        0,
					},
					Status:          paymentPb.TransactionStatus_CREATED,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					Utr:             "upi-req-id",
					DedupeId: &paymentPb.DedupeId{
						RequestId: payPkg.AddFundSecondLegReqIdPrefix + "upi-req-id",
					},
				},
				err: nil,
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-id",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status:      rpc.StatusOk(),
					ActorId:     "actor-id",
					DeviceToken: "device-token",
					Device: &commontypes.Device{
						DeviceId: "device-id",
					},
					UserProfileId: "user-profile-1",
				},
				err: nil,
			},
			mockGetEntityDetailByActorId: mockGetEntityDetailByActorId{
				enable: true,
				req: &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id",
					MobileNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
				err: nil,
			},
			mockGetBankCustomerDetails: mockGetBankCustomerDetails{
				enable: true,
				req: &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_UserId{
						UserId: "entity-id",
					},
				},
				res: &bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Status:           bankcust.Status_STATUS_ACTIVE,
						VendorCustomerId: "customer-id",
					},
				},
				err: nil,
			},
			mockVgAddFundStatus: mockVgAddFundStatus{
				enable: true,
				req: &vgPaymentPb.GetAddFundStatusRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Auth: &header.Auth{
						DeviceId:      "device-id",
						DeviceToken:   "device-token",
						CustomerId:    "customer-id",
						UserProfileId: "user-profile-1",
					},
					OriginalRequestId: "upi-req-id",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
				want: &vgPaymentPb.GetAddFundStatusResponse{
					Status:          rpc.StatusOk(),
					RawResponseCode: "000",
					UpiCustRefId:    "upi-cuts-ref",
				},
				err: nil,
			},
			mockGetByReqId: mockGetByReqId{
				enable: true,
				reqId:  "upi-req-id",
				want: &paymentPb.Transaction{
					Id: "txn-first-leg",
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				req: &piPb.GetPiByIdRequest{
					Id: "paymentinstrument-pool-account-federal",
				},
				res: &piPb.GetPiByIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstrument: &piPb.PaymentInstrument{
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								IfscCode: "FDRL10001",
							},
						},
					},
				},
			},
			mockUpdateAndChangeStatuses: []*mockUpdateAndChangeStatus{
				{
					txn: &paymentPb.Transaction{
						Id:          "txn-id",
						PiFrom:      "paymentinstrument-pool-account-federal",
						PiTo:        "pi-to",
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
						Utr:             "upi-req-id",
						DetailedStatus: &paymentPb.TransactionDetailedStatus{
							DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
								{
									State:         paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
									RawStatusCode: "000",
									Api:           paymentPb.TransactionDetailedStatus_DetailedStatus_ADD_FUNDS_ENQUIRY,
								},
							},
						},
						DedupeId: &paymentPb.DedupeId{
							RequestId: payPkg.AddFundSecondLegReqIdPrefix + "upi-req-id",
						},
					},
					payReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "ADD_FUND:upi-req-id",
					},
					updateMask: []paymentPb.TransactionFieldMask{
						paymentPb.TransactionFieldMask_DETAILED_STATUS,
					},
					currentStatus: paymentPb.TransactionStatus_CREATED,
					nextStatus:    paymentPb.TransactionStatus_SUCCESS,
					err:           nil,
				},
			},
			mockUpdateTxnStatus: mockUpdateTxnStatus{
				enable: true,
				req: &paymentPb.Transaction{
					Id:  "txn-first-leg",
					Utr: "upi-cuts-ref",
				},
				updateMask: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_UTR},
			},
			wantErr: false,
		},
		{
			name: "move in progress txn status to success but don't update utr in the first leg",
			req: &domainPb.ProcessSettlementRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "external-order-id",
					IsLastAttempt:   false,
				},
				Payload: payload,
			},
			want: &domainPb.ProcessSettlementResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			mockGetOrderWithTxns: mockGetOrderWithTxns{
				enable:     true,
				externalId: "external-order-id",
				want: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:           "order-id",
						Status:       orderPb.OrderStatus_IN_SETTLEMENT,
						Workflow:     orderPb.OrderWorkflow_ADD_FUNDS_COLLECT,
						OrderPayload: collectPayload,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						ToActorId: "actor-id",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:          "txn-id",
							PiFrom:      "paymentinstrument-pool-account-federal",
							PiTo:        "pi-to",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
								Nanos:        0,
							},
							Status:          paymentPb.TransactionStatus_IN_PROGRESS,
							PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
							Utr:             "upi-req-id",
							ReqId:           "ADD_FUND:upi-req-id",
						},
					},
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: false,
				txn: &paymentPb.Transaction{
					PiFrom:      "paymentinstrument-pool-account-federal",
					PiTo:        "pi-to",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
						Nanos:        0,
					},
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					Remarks:         payPkg.AddFundRemarks,
					Utr:             "upi-req-id",
				},
				paymentRqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "ADD_FUND:upi-req-id",
				},
				orderId: "order-id",
				want: &paymentPb.Transaction{
					Id:          "txn-id",
					PiFrom:      "paymentinstrument-pool-account-federal",
					PiTo:        "pi-to",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
						Nanos:        0,
					},
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
				},
				err: nil,
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-id",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status:      rpc.StatusOk(),
					ActorId:     "actor-id",
					DeviceToken: "device-token",
					Device: &commontypes.Device{
						DeviceId: "device-id",
					},
					UserProfileId: "user-profile-1",
				},
				err: nil,
			},
			mockGetEntityDetailByActorId: mockGetEntityDetailByActorId{
				enable: true,
				req: &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id",
					MobileNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
				err: nil,
			},
			mockGetBankCustomerDetails: mockGetBankCustomerDetails{
				enable: true,
				req: &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_UserId{
						UserId: "entity-id",
					},
				},
				res: &bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Status:           bankcust.Status_STATUS_ACTIVE,
						VendorCustomerId: "customer-id",
					},
				},
				err: nil,
			},
			mockVgAddFundStatus: mockVgAddFundStatus{
				enable: true,
				req: &vgPaymentPb.GetAddFundStatusRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Auth: &header.Auth{
						DeviceId:      "device-id",
						DeviceToken:   "device-token",
						CustomerId:    "customer-id",
						UserProfileId: "user-profile-1",
					},
					OriginalRequestId: "upi-req-id",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
				want: &vgPaymentPb.GetAddFundStatusResponse{
					Status:          rpc.StatusOk(),
					RawResponseCode: "000",
				},
				err: nil,
			},
			mockUpdateAndChangeStatuses: []*mockUpdateAndChangeStatus{
				{
					txn: &paymentPb.Transaction{
						Id:          "txn-id",
						PiFrom:      "paymentinstrument-pool-account-federal",
						PiTo:        "pi-to",
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						DetailedStatus: &paymentPb.TransactionDetailedStatus{
							DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
								{
									State:         paymentPb.TransactionDetailedStatus_DetailedStatus_SUCCESS,
									RawStatusCode: "000",
									Api:           paymentPb.TransactionDetailedStatus_DetailedStatus_ADD_FUNDS_ENQUIRY,
								},
							},
						},
						Status:          paymentPb.TransactionStatus_IN_PROGRESS,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
						Utr:             "upi-req-id",
						ReqId:           "ADD_FUND:upi-req-id",
					},
					payReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "ADD_FUND:upi-req-id",
					},
					updateMask: []paymentPb.TransactionFieldMask{
						paymentPb.TransactionFieldMask_DETAILED_STATUS,
					},
					currentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
					nextStatus:    paymentPb.TransactionStatus_SUCCESS,
					err:           nil,
				},
			},
			wantErr: false,
		},
		{
			name: "move in progress txn status to failure",
			req: &domainPb.ProcessSettlementRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "external-order-id",
					IsLastAttempt:   false,
				},
				Payload: payload,
			},
			want: &domainPb.ProcessSettlementResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE,
				},
			},
			mockGetOrderWithTxns: mockGetOrderWithTxns{
				enable:     true,
				externalId: "external-order-id",
				want: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:       "order-id",
						Status:   orderPb.OrderStatus_IN_SETTLEMENT,
						Workflow: orderPb.OrderWorkflow_ADD_FUNDS,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						OrderPayload: payload,
						ToActorId:    "actor-id",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:          "txn-id",
							PiFrom:      "paymentinstrument-pool-account-federal",
							PiTo:        "pi-to",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
								Nanos:        0,
							},
							Status:          paymentPb.TransactionStatus_IN_PROGRESS,
							PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
							Utr:             "upi-req-id",
							ReqId:           "ADD_FUND:upi-req-id",
						},
					},
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: false,
				txn: &paymentPb.Transaction{
					PiFrom:      "paymentinstrument-pool-account-federal",
					PiTo:        "pi-to",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
						Nanos:        0,
					},
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					Remarks:         payPkg.AddFundRemarks,
					Utr:             "upi-req-id",
				},
				paymentRqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "ADD_FUND:upi-req-id",
				},
				orderId: "order-id",
				want: &paymentPb.Transaction{
					Id:          "txn-id",
					PiFrom:      "paymentinstrument-pool-account-federal",
					PiTo:        "pi-to",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
						Nanos:        0,
					},
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					Utr:             "upi-req-id",
				},
				err: nil,
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-id",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status:      rpc.StatusOk(),
					ActorId:     "actor-id",
					DeviceToken: "device-token",
					Device: &commontypes.Device{
						DeviceId: "device-id",
					},
					UserProfileId: "user-profile-1",
				},
				err: nil,
			},
			mockGetEntityDetailByActorId: mockGetEntityDetailByActorId{
				enable: true,
				req: &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id",
					MobileNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
				err: nil,
			},
			mockGetBankCustomerDetails: mockGetBankCustomerDetails{
				enable: true,
				req: &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_UserId{
						UserId: "entity-id",
					},
				},
				res: &bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Status:           bankcust.Status_STATUS_ACTIVE,
						VendorCustomerId: "customer-id",
					},
				},
				err: nil,
			},
			mockVgAddFundStatus: mockVgAddFundStatus{
				enable: true,
				req: &vgPaymentPb.GetAddFundStatusRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Auth: &header.Auth{
						DeviceId:      "device-id",
						DeviceToken:   "device-token",
						CustomerId:    "customer-id",
						UserProfileId: "user-profile-1",
					},
					OriginalRequestId: "upi-req-id",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
				want: &vgPaymentPb.GetAddFundStatusResponse{
					Status:          rpc.NewStatus(uint32(vgPaymentPb.PayResponse_FAILED), "", ""),
					RawResponseCode: "000",
				},
				err: nil,
			},
			mockUpdateAndChangeStatuses: []*mockUpdateAndChangeStatus{
				{
					txn: &paymentPb.Transaction{
						Id:          "txn-id",
						PiFrom:      "paymentinstrument-pool-account-federal",
						PiTo:        "pi-to",
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						Status:          paymentPb.TransactionStatus_IN_PROGRESS,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
						Utr:             "upi-req-id",
						DetailedStatus: &paymentPb.TransactionDetailedStatus{
							DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
								{
									State:         paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE,
									RawStatusCode: "000",
									Api:           paymentPb.TransactionDetailedStatus_DetailedStatus_ADD_FUNDS_ENQUIRY,
								},
							},
						},
						ReqId: "ADD_FUND:upi-req-id",
					},
					payReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "ADD_FUND:upi-req-id",
					},
					updateMask: []paymentPb.TransactionFieldMask{
						paymentPb.TransactionFieldMask_DETAILED_STATUS,
					},
					currentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
					nextStatus:    paymentPb.TransactionStatus_FAILED,
					err:           nil,
				},
			},
			wantErr: false,
		},
		{
			name: "move in progress txn status to Manual intervention",
			req: &domainPb.ProcessSettlementRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "external-order-id",
					IsLastAttempt:   true,
				},
				Payload: payload,
			},
			want: &domainPb.ProcessSettlementResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			mockGetOrderWithTxns: mockGetOrderWithTxns{
				enable:     true,
				externalId: "external-order-id",
				want: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:       "order-id",
						Status:   orderPb.OrderStatus_IN_SETTLEMENT,
						Workflow: orderPb.OrderWorkflow_ADD_FUNDS_COLLECT,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						ToActorId:    "actor-id",
						OrderPayload: collectPayload,
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:          "txn-id",
							PiFrom:      "paymentinstrument-pool-account-federal",
							PiTo:        "pi-to",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
								Nanos:        0,
							},
							Status:          paymentPb.TransactionStatus_IN_PROGRESS,
							PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
							Utr:             "upi-req-id",
							ReqId:           "ADD_FUND:upi-req-id",
						},
					},
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: false,
				txn: &paymentPb.Transaction{
					PiFrom:      "paymentinstrument-pool-account-federal",
					PiTo:        "pi-to",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
						Nanos:        0,
					},
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					Remarks:         payPkg.AddFundRemarks,
					Utr:             "upi-req-id",
				},
				paymentRqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "ADD_FUND:upi-req-id",
				},
				orderId: "order-id",
				want: &paymentPb.Transaction{
					Id:          "txn-id",
					PiFrom:      "paymentinstrument-pool-account-federal",
					PiTo:        "pi-to",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
						Nanos:        0,
					},
					Status:          paymentPb.TransactionStatus_IN_PROGRESS,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					Utr:             "upi-req-id",
				},
				err: nil,
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-id",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status:      rpc.StatusOk(),
					ActorId:     "actor-id",
					DeviceToken: "device-token",
					Device: &commontypes.Device{
						DeviceId: "device-id",
					},
					UserProfileId: "user-profile-1",
				},
				err: nil,
			},
			mockGetEntityDetailByActorId: mockGetEntityDetailByActorId{
				enable: true,
				req: &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id",
					MobileNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
				err: nil,
			},
			mockGetBankCustomerDetails: mockGetBankCustomerDetails{
				enable: true,
				req: &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_UserId{
						UserId: "entity-id",
					},
				},
				res: &bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Status:           bankcust.Status_STATUS_ACTIVE,
						VendorCustomerId: "customer-id",
					},
				},
				err: nil,
			},
			mockVgAddFundStatus: mockVgAddFundStatus{
				enable: true,
				req: &vgPaymentPb.GetAddFundStatusRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Auth: &header.Auth{
						DeviceId:      "device-id",
						DeviceToken:   "device-token",
						CustomerId:    "customer-id",
						UserProfileId: "user-profile-1",
					},
					OriginalRequestId: "upi-req-id",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
				want: &vgPaymentPb.GetAddFundStatusResponse{
					Status:          rpc.NewStatus(uint32(vgPaymentPb.GetAddFundStatusResponse_IN_PROGRESS), "", ""),
					RawResponseCode: "F123",
				},
				err: nil,
			},
			mockUpdateAndChangeStatuses: []*mockUpdateAndChangeStatus{
				{
					txn: &paymentPb.Transaction{
						Id:          "txn-id",
						PiFrom:      "paymentinstrument-pool-account-federal",
						PiTo:        "pi-to",
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						Status:          paymentPb.TransactionStatus_IN_PROGRESS,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
						Utr:             "upi-req-id",
						DetailedStatus: &paymentPb.TransactionDetailedStatus{
							DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
								{
									State:         paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
									RawStatusCode: "F123",
									Api:           paymentPb.TransactionDetailedStatus_DetailedStatus_ADD_FUNDS_ENQUIRY,
								},
							},
						},
						ReqId: "ADD_FUND:upi-req-id",
					},
					payReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "ADD_FUND:upi-req-id",
					},
					updateMask: []paymentPb.TransactionFieldMask{
						paymentPb.TransactionFieldMask_DETAILED_STATUS,
					},
					currentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
					nextStatus:    paymentPb.TransactionStatus_IN_PROGRESS,
					err:           nil,
				},
				{
					txn: &paymentPb.Transaction{
						Id:          "txn-id",
						PiFrom:      "paymentinstrument-pool-account-federal",
						PiTo:        "pi-to",
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						Status:          paymentPb.TransactionStatus_IN_PROGRESS,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
						Utr:             "upi-req-id",
						DetailedStatus: &paymentPb.TransactionDetailedStatus{
							DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
								{
									State:         paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
									RawStatusCode: "F123",
									Api:           paymentPb.TransactionDetailedStatus_DetailedStatus_ADD_FUNDS_ENQUIRY,
								},
							},
						},
						ReqId: "ADD_FUND:upi-req-id",
					},
					currentStatus: paymentPb.TransactionStatus_IN_PROGRESS,
					nextStatus:    paymentPb.TransactionStatus_MANUAL_INTERVENTION,
					err:           nil,
				},
			},
			wantErr: false,
		},
		{
			name: "got record not found from enquiry service",
			req: &domainPb.ProcessSettlementRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "external-order-id",
					IsLastAttempt:   false,
				},
				Payload: payload,
			},
			want: &domainPb.ProcessSettlementResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{
					Status: domainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			mockGetOrderWithTxns: mockGetOrderWithTxns{
				enable:     true,
				externalId: "external-order-id",
				want: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:           "order-id",
						Status:       orderPb.OrderStatus_IN_SETTLEMENT,
						Workflow:     orderPb.OrderWorkflow_ADD_FUNDS_COLLECT,
						OrderPayload: collectPayload,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						ToActorId: "actor-id",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:          "txn-id",
							PiFrom:      "paymentinstrument-pool-account-federal",
							PiTo:        "pi-to",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
								Nanos:        0,
							},
							Status:          paymentPb.TransactionStatus_CREATED,
							PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
							Utr:             "upi-req-id",
							ReqId:           "ADD_FUND:upi-req-id",
						},
					},
				},
				err: nil,
			},
			mockCreateTxn: mockCreateTxn{
				enable: false,
				txn: &paymentPb.Transaction{
					PiFrom:      "paymentinstrument-pool-account-federal",
					PiTo:        "pi-to",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
						Nanos:        0,
					},
					Status:          paymentPb.TransactionStatus_CREATED,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					Remarks:         payPkg.AddFundRemarks,
					Utr:             "upi-req-id",
				},
				paymentRqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "ADD_FUND:upi-req-id",
				},
				orderId: "order-id",
				want: &paymentPb.Transaction{
					Id:          "txn-id",
					PiFrom:      "paymentinstrument-pool-account-federal",
					PiTo:        "pi-to",
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &money.Money{
						CurrencyCode: "INR",
						Units:        100,
						Nanos:        0,
					},
					Status:          paymentPb.TransactionStatus_CREATED,
					PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
					Utr:             "upi-req-id",
				},
				err: nil,
			},
			mockGetDeviceAuth: mockGetDeviceAuth{
				enable: true,
				req: &authPb.GetDeviceAuthRequest{
					ActorId: "actor-id",
				},
				res: &authPb.GetDeviceAuthResponse{
					Status:      rpc.StatusOk(),
					ActorId:     "actor-id",
					DeviceToken: "device-token",
					Device: &commontypes.Device{
						DeviceId: "device-id",
					},
					UserProfileId: "user-profile-1",
				},
				err: nil,
			},
			mockGetEntityDetailByActorId: mockGetEntityDetailByActorId{
				enable: true,
				req: &actorPb.GetEntityDetailsByActorIdRequest{
					ActorId: "actor-id",
				},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status:   rpc.StatusOk(),
					EntityId: "entity-id",
					MobileNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
				err: nil,
			},
			mockGetBankCustomerDetails: mockGetBankCustomerDetails{
				enable: true,
				req: &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_UserId{
						UserId: "entity-id",
					},
				},
				res: &bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Status:           bankcust.Status_STATUS_ACTIVE,
						VendorCustomerId: "customer-id",
					},
				},
				err: nil,
			},
			mockVgAddFundStatus: mockVgAddFundStatus{
				enable: true,
				req: &vgPaymentPb.GetAddFundStatusRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Auth: &header.Auth{
						DeviceId:      "device-id",
						DeviceToken:   "device-token",
						CustomerId:    "customer-id",
						UserProfileId: "user-profile-1",
					},
					OriginalRequestId: "upi-req-id",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
				want: &vgPaymentPb.GetAddFundStatusResponse{
					Status:          rpc.StatusRecordNotFound(),
					RawResponseCode: "OBE0067",
				},
				err: nil,
			},
			mockUpdateAndChangeStatuses: []*mockUpdateAndChangeStatus{
				{
					txn: &paymentPb.Transaction{
						Id:          "txn-id",
						PiFrom:      "paymentinstrument-pool-account-federal",
						PiTo:        "pi-to",
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Amount: &money.Money{
							CurrencyCode: "INR",
							Units:        100,
							Nanos:        0,
						},
						Status:          paymentPb.TransactionStatus_CREATED,
						PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
						Utr:             "upi-req-id",
						DetailedStatus: &paymentPb.TransactionDetailedStatus{
							DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
								{
									State:         paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN,
									RawStatusCode: "OBE0067",
									Api:           paymentPb.TransactionDetailedStatus_DetailedStatus_ADD_FUNDS_ENQUIRY,
								},
							},
						},
						ReqId: "ADD_FUND:upi-req-id",
					},
					payReqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "ADD_FUND:upi-req-id",
					},
					updateMask: []paymentPb.TransactionFieldMask{
						paymentPb.TransactionFieldMask_DETAILED_STATUS,
					},
					currentStatus: paymentPb.TransactionStatus_CREATED,
					nextStatus:    paymentPb.TransactionStatus_CREATED,
					err:           nil,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetOrderWithTxns.enable {
				mockOrderDao.EXPECT().GetByExternalIdWithTransactions(context.Background(), tt.mockGetOrderWithTxns.externalId).
					Return(tt.mockGetOrderWithTxns.want, tt.mockGetOrderWithTxns.err)
			}

			if tt.mockCreateTxn.enable {
				mockTxnDao.EXPECT().Create(context.Background(), tt.mockCreateTxn.txn, tt.mockCreateTxn.paymentRqInfo, tt.mockCreateTxn.orderId, commontypes.Ownership_EPIFI_TECH).
					Return(tt.mockCreateTxn.want, tt.mockCreateTxn.err)
			}

			if tt.mockGetDeviceAuth.enable {
				mockAuthClient.EXPECT().GetDeviceAuth(context.Background(), tt.mockGetDeviceAuth.req).
					Return(tt.mockGetDeviceAuth.res, tt.mockGetDeviceAuth.err)
			}

			if tt.mockGetEntityDetailByActorId.enable {
				mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), tt.mockGetEntityDetailByActorId.req).
					Return(tt.mockGetEntityDetailByActorId.res, tt.mockGetEntityDetailByActorId.err)
			}

			if tt.mockGetBankCustomerDetails.enable {
				mockBCClient.EXPECT().GetBankCustomer(context.Background(), tt.mockGetBankCustomerDetails.req).
					Return(tt.mockGetBankCustomerDetails.res, tt.mockGetBankCustomerDetails.err)
			}

			if tt.mockVgAddFundStatus.enable {
				mockVgPaymentClient.EXPECT().GetAddFundStatus(context.Background(), newAddFundArgMatcher(tt.mockVgAddFundStatus.req)).
					Return(tt.mockVgAddFundStatus.want, tt.mockVgAddFundStatus.err)
			}

			if tt.mockGetByReqId.enable {
				mockTxnDao.EXPECT().GetByReqId(gomock.Any(), tt.mockGetByReqId.reqId, false).Return(tt.mockGetByReqId.want, tt.mockGetByReqId.err)
			}

			if tt.mockIsNewAddFundsVpaEnabledForActor.enable {
				mockUpiProcessor.EXPECT().IsNewAddFundsVpaEnabledForActor(gomock.Any(), tt.mockIsNewAddFundsVpaEnabledForActor.actorId).
					Return(tt.mockIsNewAddFundsVpaEnabledForActor.want, tt.mockIsNewAddFundsVpaEnabledForActor.err)
			}

			for _, mockUpdateAndChangeStatus := range tt.mockUpdateAndChangeStatuses {
				mockTxnDao.EXPECT().UpdateAndChangeStatus(
					gomock.Any(),
					payPkg.NewTxnUpdateArgMatcher(mockUpdateAndChangeStatus.txn),
					mockUpdateAndChangeStatus.payReqInfo,
					mockUpdateAndChangeStatus.updateMask,
					mockUpdateAndChangeStatus.currentStatus,
					mockUpdateAndChangeStatus.nextStatus,
				).Return(mockUpdateAndChangeStatus.err).Do(func(
					ctx context.Context,
					transaction *paymentPb.Transaction,
					reqInfo *paymentPb.PaymentRequestInformation,
					updateMask []paymentPb.TransactionFieldMask,
					currentStatus, nextStatus paymentPb.TransactionStatus,
				) {
					transaction.Status = nextStatus
				})
			}

			if tt.mockUpdateTxnStatus.enable {
				mockTxnDao.EXPECT().Update(
					gomock.Any(),
					payPkg.NewTxnUpdateArgMatcher(tt.mockUpdateTxnStatus.req),
					tt.mockUpdateTxnStatus.payReqInfo,
					tt.mockUpdateTxnStatus.updateMask,
				).
					Return(tt.mockUpdateTxnStatus.err)
			}

			if tt.mockGetPiById.enable {
				mockPiClient.EXPECT().GetPiById(gomock.Any(), tt.mockGetPiById.req).Return(
					tt.mockGetPiById.res, tt.mockGetPiById.err)
			}

			got, err := svc.ProcessAddFundTransaction(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddFundTransaction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("AddFundTransaction() got = %v, want %v", got, tt.want)
			}
		})
	}
}

type AddFundVgArgMatcher struct {
	want *vgPaymentPb.GetAddFundStatusRequest
}

func newAddFundArgMatcher(want *vgPaymentPb.GetAddFundStatusRequest) *AddFundVgArgMatcher {
	return &AddFundVgArgMatcher{want: want}
}

func (a *AddFundVgArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*vgPaymentPb.GetAddFundStatusRequest)
	if !ok {
		return false
	}

	return reflect.DeepEqual(a.want, got)
}

func (a *AddFundVgArgMatcher) String() string {
	return fmt.Sprintf("want: %v", a.want)
}
