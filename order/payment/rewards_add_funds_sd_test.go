package payment_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	brokerMocks "github.com/epifi/be-common/pkg/events/mocks"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMock "github.com/epifi/gamma/api/actor/mocks"
	authPb "github.com/epifi/gamma/api/auth"
	authMocks "github.com/epifi/gamma/api/auth/mocks"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/bankcust/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	orderDomainPb "github.com/epifi/gamma/api/order/domain"
	orderPayloadPb "github.com/epifi/gamma/api/order/payload"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	userMock "github.com/epifi/gamma/api/user/mocks"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/mocks"
	mocksUpiProcessor "github.com/epifi/gamma/order/internal/upi/mocks"
	"github.com/epifi/gamma/order/payment"
)

var (
	fixtureEpifiBusinessActorId     = "actor-epifi-business-account"
	fixtureActorId1                 = "actor-user-1"
	fixtureEpifiBusinessAccountPiId = "paymentinstrument-epifi-business-account"
	fixtureSavingsPiId              = "pi-savings-1"
	fixtureSavingsPiName            = "Test-Savings-1"
	fixtureDepositPiId              = "pi-deposit-1"
	fixtureDepositPiName            = "Bike"
	fixtureSavingsAccount           = &piPb.Account{
		ActualAccountNumber: "**********",
		IfscCode:            "IFSC0001",
		AccountType:         accounts.Type_SAVINGS,
		Name:                "Test-Savings-1",
	}
	fixtureDepositAccount = &piPb.Account{
		ActualAccountNumber: "**********",
		IfscCode:            "FDRL0001001",
		AccountType:         accounts.Type_SMART_DEPOSIT,
		Name:                "Bike",
	}
	fixtureOrderExternalId      = "order-ext-21"
	fixtureOrderRewardsAddFunds = &orderPb.Order{
		Id:           "order-21",
		FromActorId:  fixtureEpifiBusinessActorId,
		ToActorId:    fixtureActorId1,
		Workflow:     orderPb.OrderWorkflow_REWARDS_ADD_FUNDS_SD,
		Status:       orderPb.OrderStatus_CREATED,
		OrderPayload: nil,
		Provenance:   orderPb.OrderProvenance_INTERNAL,
		Amount: &moneyPb.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		ClientReqId: "deposit-order-client-request-id",
	}
)

func TestService_RewardsSdAddFundsMakeDepositOwnFundTransferPayment(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	rewardsSDAddFundsPayload := &orderPayloadPb.RewardsAddFundsSD{
		B2CPayload: &paymentPb.B2CFundTransfer{
			PiFrom: fixtureEpifiBusinessAccountPiId,
			PiTo:   fixtureSavingsPiId,
			Amount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100,
			},
			Remarks:           "Rewards Smart Deposit fund transfer",
			PreferredProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
			Partner:           commonvgpb.Vendor_FEDERAL_BANK,
		},
		P2PFundTransferPayload: &orderPb.P2PFundTransfer{
			PaymentDetails: &orderPb.PaymentDetails{
				PiFrom: fixtureSavingsPiId,
				PiTo:   fixtureDepositPiId,
				Amount: &moneyPb.Money{
					CurrencyCode: "INR",
					Units:        100,
				},
				Remarks:         "Saved rewards into Smart Deposit",
				PaymentProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
			},
			PayerAccountId: "Test-Savings-1",
		},
	}
	marshalledPayload, _ := protojson.Marshal(rewardsSDAddFundsPayload)
	fixtureOrderRewardsAddFunds.OrderPayload = marshalledPayload

	ctr := gomock.NewController(t)
	defer ctr.Finish()

	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockAuthClient := authMocks.NewMockAuthClient(ctr)
	mockActorClient := actorMock.NewMockActorClient(ctr)
	mockUserClient := userMock.NewMockUsersClient(ctr)
	mockBCClient := mocks.NewMockBankCustomerServiceClient(ctr)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockTxnDetailedStatusUpdateSnsPublisher := queueMocks.NewMockPublisher(ctr)

	svc := payment.NewService(pts.txnDao, pts.orderDao, mockVgPaymentClient, nil, mockPiClient, mockSavingsClient, mockAuthClient, nil, mockActorClient, mockUserClient, nil, nil, nil, nil, brokerMock, pts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, mockBCClient, nil, nil, nil, nil, mockTxnDetailedStatusUpdateSnsPublisher, nil, nil)

	mockAuthClient.EXPECT().GetDeviceAuth(context.Background(), gomock.Any()).
		Return(&authPb.GetDeviceAuthResponse{
			Status:        rpc.StatusOk(),
			UserProfileId: "profile-id",
			DeviceToken:   "device-token",
			Device: &commontypes.Device{
				DeviceId: "device-id",
			},
		}, nil).AnyTimes()

	mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), gomock.Any()).
		Return(&actorPb.GetEntityDetailsByActorIdResponse{
			Status:       rpc.StatusOk(),
			EntityId:     "entity-id",
			MobileNumber: &commontypes.PhoneNumber{NationalNumber: 1234567, CountryCode: 91},
		}, nil).AnyTimes()

	mockBCClient.EXPECT().GetBankCustomer(context.Background(), gomock.Any()).
		Return(&bankcust.GetBankCustomerResponse{
			Status: rpc.StatusOk(),
			BankCustomer: &bankcust.BankCustomer{
				Status:           bankcust.Status_STATUS_ACTIVE,
				VendorCustomerId: "customer-id",
			},
		}, nil).AnyTimes()

	mockSavingsClient.EXPECT().GetAccount(context.Background(), gomock.Any()).Return(
		&savingsPb.GetAccountResponse{
			Account: &savingsPb.Account{
				PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: 123343},
				State:       savingsPb.State_CREATED,
				EmailId:     "<EMAIL>",
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		}, nil).AnyTimes()

	mockTxnDetailedStatusUpdateSnsPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(
		"random-msg-123", nil).AnyTimes()

	type mockGetPiById struct {
		enable bool
		req    *piPb.GetPiByIdRequest
		res    *piPb.GetPiByIdResponse
		err    error
	}

	type mockVgPay struct {
		enable bool
		res    *vgPaymentPb.PayResponse
		err    error
	}

	type mockVgGetStatus struct {
		enable bool
		res    *vgPaymentPb.GetStatusResponse
		err    error
	}

	type args struct {
		ctx context.Context
		req *orderDomainPb.ProcessFulfilmentRequest
	}

	tests := []struct {
		name            string
		args            args
		want            *orderDomainPb.ProcessFulfilmentResponse
		mockGetPiById   []*mockGetPiById
		mockVgPay       mockVgPay
		mockVgGetStatus mockVgGetStatus
		prepareDb       bool
		wantErr         bool
	}{
		{
			name: "#1.1 Should do a successful add funds in the first run, and return transient failure",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{
				ResponseHeader: &orderDomainPb.DomainResponseHeader{
					Status: orderDomainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
				},
			},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureDepositPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureDepositPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							ActualAccountNumber: fixtureDepositAccount.ActualAccountNumber,
							IfscCode:            fixtureDepositAccount.IfscCode,
							AccountType:         fixtureDepositAccount.AccountType,
							Name:                fixtureDepositAccount.Name,
						}},
						VerifiedName: fixtureDepositPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
			},
			mockVgPay: mockVgPay{
				enable: true,
				res: &vgPaymentPb.PayResponse{
					Status: &rpc.Status{Code: uint32(vgPaymentPb.PayResponse_PAYMENT_SUCCESS)},
					Utr:    "utr-1234",
				},
				err: nil,
			},
			prepareDb: true,
			wantErr:   false,
		},
		{
			name: "#1.2 Should return success as txn already successful",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{
				ResponseHeader: &orderDomainPb.DomainResponseHeader{
					Status: orderDomainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			prepareDb: false,
			wantErr:   false,
		},
		{
			name: "#1.3 Should return success without status check if dev action is triggered without overrideTerminalState flag set as txn already successful",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{
						ClientRequestId:             fixtureOrderExternalId,
						IsLastAttempt:               true,
						ShouldForceProcessOrder:     true,
						ShouldOverrideTerminalState: false,
					},
					Payload: marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{
				ResponseHeader: &orderDomainPb.DomainResponseHeader{
					Status: orderDomainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			prepareDb: false,
			wantErr:   false,
		},
		// Enable this test after txn can be updated even in terminal state.
		//  Currently we are getting error: "txn snot eligible for status update"
		// {
		// 	name: "#1.4 Should return success if dev action is triggered with override after status check",
		// 	args: args{
		// 		ctx: context.Background(),
		// 		req: &orderDomainPb.ProcessFulfilmentRequest{
		// 			RequestHeader: &orderDomainPb.DomainRequestHeader{
		// 				ClientRequestId:             fixtureOrderExternalId,
		// 				IsLastAttempt:               true,
		// 				ShouldForceProcessOrder:     true,
		// 				ShouldOverrideTerminalState: true,
		// 			},
		// 			Payload: marshalledPayload,
		// 		},
		// 	},
		// 	mockVgGetStatus: mockVgGetStatus{
		// 		enable: true,
		// 		res: &vgPaymentPb.GetStatusResponse{
		// 			Status:               rpc.StatusOk(),
		// 			Utr:                  "utr-1232",
		// 			TransactionTimestamp: ptypes.TimestampNow(),
		// 		},
		// 		err: nil,
		// 	},
		// 	want: &orderDomainPb.ProcessFulfilmentResponse{
		// 		ResponseHeader: &orderDomainPb.DomainResponseHeader{
		// 			Status: orderDomainPb.DomainProcessingStatus_SUCCESS,
		// 		},
		// 	},
		// 	prepareDb: false,
		// 	wantErr:   false,
		// },
		{
			name: "#2.1 Should successfully create a new transaction at our end but fail to send request to vendor",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{ResponseHeader: &orderDomainPb.DomainResponseHeader{
				Status: orderDomainPb.DomainProcessingStatus_TRANSIENT_FAILURE},
			},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res:    &piPb.GetPiByIdResponse{Status: rpc.StatusInternal(), PaymentInstrument: nil},
					err:    nil,
				},
				{
					enable: false,
					req:    &piPb.GetPiByIdRequest{Id: fixtureDepositPiId},
					res:    &piPb.GetPiByIdResponse{Status: rpc.StatusInternal(), PaymentInstrument: nil},
					err:    nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res:    &piPb.GetPiByIdResponse{Status: rpc.StatusInternal(), PaymentInstrument: nil},
					err:    nil,
				},
			},
			prepareDb: true,
			wantErr:   false,
		},
		{
			name: "#2.2 Should do a status check at vendor for txn created at 2.1 and return permanent failure",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{ResponseHeader: &orderDomainPb.DomainResponseHeader{
				Status: orderDomainPb.DomainProcessingStatus_PERMANENT_FAILURE,
			}},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: false,
					req:    &piPb.GetPiByIdRequest{Id: fixtureDepositPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureDepositPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							ActualAccountNumber: fixtureDepositAccount.ActualAccountNumber,
							IfscCode:            fixtureDepositAccount.IfscCode,
							AccountType:         fixtureDepositAccount.AccountType,
							Name:                fixtureDepositAccount.Name,
						}},
						VerifiedName: fixtureDepositPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
			},
			mockVgGetStatus: mockVgGetStatus{
				enable: true,
				res: &vgPaymentPb.GetStatusResponse{
					Status: &rpc.Status{Code: uint32(vgPaymentPb.GetStatusResponse_FAILED)},
				},
				err: nil,
			},
			prepareDb: false,
			wantErr:   false,
		},
		{
			name: "#3.1 Should initiate a new request to vendor and vendor return UNKNOWN status",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{ResponseHeader: &orderDomainPb.DomainResponseHeader{
				Status: orderDomainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
			}},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureDepositPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureDepositPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							ActualAccountNumber: fixtureDepositAccount.ActualAccountNumber,
							IfscCode:            fixtureDepositAccount.IfscCode,
							AccountType:         fixtureDepositAccount.AccountType,
							Name:                fixtureDepositAccount.Name,
						}},
						VerifiedName: fixtureDepositPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
			},
			mockVgPay: mockVgPay{
				enable: true,
				res:    &vgPaymentPb.PayResponse{Status: rpc.StatusUnknown()},
				err:    errors.New("unknown error"),
			},
			prepareDb: true,
			wantErr:   false,
		},
		{
			name: "#3.2 Should do a successful status check for txn created in 3.1",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{
				ResponseHeader: &orderDomainPb.DomainResponseHeader{
					Status: orderDomainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			mockVgGetStatus: mockVgGetStatus{
				enable: true,
				res: &vgPaymentPb.GetStatusResponse{
					Status:               rpc.StatusOk(),
					Utr:                  "utr-1232",
					TransactionTimestamp: ptypes.TimestampNow(),
				},
				err: nil,
			},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
			},
			prepareDb: false,
			wantErr:   false,
		},
		{
			name: "#4.1 Should successfully create a new transaction at our end but fail to send request to vendor",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{ResponseHeader: &orderDomainPb.DomainResponseHeader{
				Status: orderDomainPb.DomainProcessingStatus_TRANSIENT_FAILURE},
			},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res:    &piPb.GetPiByIdResponse{Status: rpc.StatusInternal(), PaymentInstrument: nil},
					err:    nil,
				},
				{
					enable: false,
					req:    &piPb.GetPiByIdRequest{Id: fixtureDepositPiId},
					res:    &piPb.GetPiByIdResponse{Status: rpc.StatusInternal(), PaymentInstrument: nil},
					err:    nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res:    &piPb.GetPiByIdResponse{Status: rpc.StatusInternal(), PaymentInstrument: nil},
					err:    nil,
				},
			},
			prepareDb: true,
			wantErr:   false,
		},
		{
			name: "#4.2 Should do a status check at vendor for txn created at 4.1 and return FAILED status along with transient failure",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{ResponseHeader: &orderDomainPb.DomainResponseHeader{
				Status: orderDomainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
			}},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: false,
					req:    &piPb.GetPiByIdRequest{Id: fixtureDepositPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureDepositPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							ActualAccountNumber: fixtureDepositAccount.ActualAccountNumber,
							IfscCode:            fixtureDepositAccount.IfscCode,
							AccountType:         fixtureDepositAccount.AccountType,
							Name:                fixtureDepositAccount.Name,
						}},
						VerifiedName: fixtureDepositPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
			},
			mockVgGetStatus: mockVgGetStatus{
				enable: true,
				res: &vgPaymentPb.GetStatusResponse{
					Status: &rpc.Status{Code: uint32(vgPaymentPb.GetStatusResponse_TRANSIENT_FAILURE)},
				},
				err: nil,
			},
			prepareDb: false,
			wantErr:   false,
		},
		{
			name: "#4.3 Should initiate a new request to vendor and return UNKNOWN status",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{ResponseHeader: &orderDomainPb.DomainResponseHeader{
				Status: orderDomainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
			}},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureDepositPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureDepositPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							ActualAccountNumber: fixtureDepositAccount.ActualAccountNumber,
							IfscCode:            fixtureDepositAccount.IfscCode,
							AccountType:         fixtureDepositAccount.AccountType,
							Name:                fixtureDepositAccount.Name,
						}},
						VerifiedName: fixtureDepositPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
			},
			mockVgPay: mockVgPay{
				enable: true,
				res:    &vgPaymentPb.PayResponse{Status: rpc.StatusUnknown()},
				err:    errors.New("unknown error"),
			},
			prepareDb: false,
			wantErr:   false,
		},
		{
			name: "#4.4 Should do a successful status check for txn created in 4.1",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{
				ResponseHeader: &orderDomainPb.DomainResponseHeader{
					Status: orderDomainPb.DomainProcessingStatus_SUCCESS,
				},
			},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
			},
			mockVgGetStatus: mockVgGetStatus{
				enable: true,
				res: &vgPaymentPb.GetStatusResponse{
					Status:               rpc.StatusOk(),
					Utr:                  "utr-1232",
					TransactionTimestamp: ptypes.TimestampNow(),
				},
				err: nil,
			},
			prepareDb: false,
			wantErr:   false,
		},
		{
			name: "#5.1 Should initiate a new request to vendor and vendor return UNKNOWN status",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{ClientRequestId: fixtureOrderExternalId},
					Payload:       marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{ResponseHeader: &orderDomainPb.DomainResponseHeader{
				Status: orderDomainPb.DomainProcessingStatus_TRANSIENT_FAILURE,
			}},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureDepositPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureDepositPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{Account: &piPb.Account{
							ActualAccountNumber: fixtureDepositAccount.ActualAccountNumber,
							IfscCode:            fixtureDepositAccount.IfscCode,
							AccountType:         fixtureDepositAccount.AccountType,
							Name:                fixtureDepositAccount.Name,
						}},
						VerifiedName: fixtureDepositPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
			},
			mockVgPay: mockVgPay{
				enable: true,
				res:    &vgPaymentPb.PayResponse{Status: rpc.StatusOk()},
			},
			prepareDb: true,
			wantErr:   false,
		},
		{
			name: "#5.2 Should return UNKNOWN status check at vendor in the last attempt of txn created in 5.1",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{
						ClientRequestId: fixtureOrderExternalId,
						IsLastAttempt:   true,
					},
					Payload: marshalledPayload,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{
				ResponseHeader: &orderDomainPb.DomainResponseHeader{
					Status: orderDomainPb.DomainProcessingStatus_IN_PROGRESS,
				},
			},
			mockVgGetStatus: mockVgGetStatus{
				enable: true,
				res: &vgPaymentPb.GetStatusResponse{
					Status:               rpc.StatusUnknown(),
					TransactionTimestamp: ptypes.TimestampNow(),
				},
				err: nil,
			},
			prepareDb: false,
			wantErr:   false,
		},
		{
			name: "#5.3 For forceful processing of manual_intervention txn do a status check and return SUCCESS",
			args: args{
				ctx: context.Background(),
				req: &orderDomainPb.ProcessFulfilmentRequest{
					RequestHeader: &orderDomainPb.DomainRequestHeader{
						ClientRequestId: fixtureOrderExternalId,
						// Last attempt will be true for forceful order processing in case of MANUAL_INTERVENTION state
						IsLastAttempt:               true,
						ShouldForceProcessOrder:     true,
						ShouldOverrideTerminalState: false,
					},
					Payload: marshalledPayload,
				},
			},
			mockGetPiById: []*mockGetPiById{
				{
					enable: true,
					req:    &piPb.GetPiByIdRequest{Id: fixtureSavingsPiId},
					res: &piPb.GetPiByIdResponse{Status: rpc.StatusOk(), PaymentInstrument: &piPb.PaymentInstrument{
						Id:   fixtureSavingsPiId,
						Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{
								ActualAccountNumber: fixtureSavingsAccount.ActualAccountNumber,
								IfscCode:            fixtureSavingsAccount.IfscCode,
								AccountType:         fixtureSavingsAccount.AccountType,
								Name:                fixtureSavingsAccount.Name,
							},
						},
						VerifiedName: fixtureSavingsPiName,
						State:        piPb.PaymentInstrumentState_CREATED,
					}},
					err: nil,
				},
			},
			want: &orderDomainPb.ProcessFulfilmentResponse{
				ResponseHeader: &orderDomainPb.DomainResponseHeader{
					Status: orderDomainPb.DomainProcessingStatus_SUCCESS,
				},
			},

			mockVgGetStatus: mockVgGetStatus{
				enable: true,
				res: &vgPaymentPb.GetStatusResponse{
					Status:               rpc.StatusOk(),
					TransactionTimestamp: ptypes.TimestampNow(),
				},
				err: nil,
			},
			prepareDb: false,
			wantErr:   false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.prepareDb {

				// Clean database, run migrations and load fixtures
				pkgTest.PrepareRandomScopedCrdbDatabase(t, pts.conf.EpifiDb(), pts.dbName, pts.db, affectedTestTables, &initialiseSameDbOnce)

			}
			for i := range tt.mockGetPiById {
				if tt.mockGetPiById[i].enable {
					mockPiClient.EXPECT().GetPiById(tt.args.ctx, tt.mockGetPiById[i].req).
						Return(tt.mockGetPiById[i].res, tt.mockGetPiById[i].err)
				}

			}

			if tt.mockVgGetStatus.enable {
				mockVgPaymentClient.EXPECT().GetStatus(tt.args.ctx, gomock.Any()).
					Return(tt.mockVgGetStatus.res, tt.mockVgGetStatus.err)
			}
			if tt.mockVgPay.enable {
				mockVgPaymentClient.EXPECT().Pay(tt.args.ctx, gomock.Any()).
					Return(tt.mockVgPay.res, tt.mockVgPay.err)
			}
			got, err := svc.RewardsSdAddFundsMakeDepositOwnFundTransferPayment(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RewardsSdAddFundsMakeDepositOwnFundTransferPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.ResponseHeader.Status != tt.want.ResponseHeader.Status {
				t.Errorf("RewardsSdAddFundsMakeDepositOwnFundTransferPayment() got status= %v, want status%v", got.ResponseHeader.Status, tt.want.ResponseHeader.Status)
			}
		})
	}
}
