package payment_test

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"reflect"
	"testing"
	"time"

	"github.com/epifi/be-common/pkg/datetime"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/order/payment"
)

func TestGetExpectedProcessingTime(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	svc := payment.NewService(nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, pts.conf, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	IST, err := time.LoadLocation(datetime.IST_TIME_ZONE)
	if err != nil {
		t.Error(err)
	}

	type args struct {
		protocol paymentPb.PaymentProtocol
		vendor   commonvgpb.Vendor
		timeFrom time.Time
	}
	tests := []struct {
		name    string
		args    args
		want    time.Duration
		wantErr bool
	}{
		{
			name: "NEFT",
			args: args{
				protocol: paymentPb.PaymentProtocol_NEFT,
				vendor:   commonvgpb.Vendor_FEDERAL_BANK,
				timeFrom: time.Date(2020, time.May, 9, 12, 00, 00, 00, IST),
			},
			want:    5 * time.Second,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := svc.GetExpectedProcessingTime(tt.args.protocol, tt.args.vendor, tt.args.timeFrom)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetExpectedProcessingTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetExpectedProcessingTime() got = %v, want %v", got, tt.want)
			}
		})
	}
}
