// nolint: goimports
package payment_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	timelineMocks "github.com/epifi/gamma/api/timeline/mocks"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/proto"
	"github.com/stretchr/testify/assert"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	eventsMock "github.com/epifi/be-common/pkg/events/mocks"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"

	accountsPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMock "github.com/epifi/gamma/api/actor/mocks"
	authMock "github.com/epifi/gamma/api/auth/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	domainPb "github.com/epifi/gamma/api/order/domain"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	mocks3 "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	mockPi "github.com/epifi/gamma/api/paymentinstrument/mocks"
	upiPb "github.com/epifi/gamma/api/upi"
	vgMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/mocks"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	mocks2 "github.com/epifi/gamma/api/vendorgateway/openbanking/upi/mocks"
	"github.com/epifi/gamma/order/dao/mocks"
	mocks4 "github.com/epifi/gamma/order/internal/actor/mocks"
	mocksUpiProcessor "github.com/epifi/gamma/order/internal/upi/mocks"
	"github.com/epifi/gamma/order/payment"
	"github.com/epifi/gamma/pkg/pay"
)

type txnUpdateArgMatcher struct {
	want *paymentPb.Transaction
}

// newTxnUpdateArgMatcher initializes custom argument matcher for transaction detailed status update
// this is required as creation timestamp in detailed status will be different per test run.
// The matcher helps in by-passing creation timestamp matching
func newTxnUpdateArgMatcher(txn *paymentPb.Transaction) *txnUpdateArgMatcher {
	return &txnUpdateArgMatcher{want: txn}
}

func (t *txnUpdateArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*paymentPb.Transaction)
	if !ok {
		return false
	}

	if len(got.DetailedStatus.GetDetailedStatusList()) != len(t.want.DetailedStatus.GetDetailedStatusList()) {
		return false
	}
	updateDetailedStatus(got, t.want)
	t.want.Utr = got.Utr
	return proto.Equal(got, t.want)
}

func (t *txnUpdateArgMatcher) String() string {
	return t.want.String()
}

type txnCreateArgMatcher struct {
	want *paymentPb.Transaction
}

// newTxnCreateArgMatcher initializes custom argument matcher for transaction creation dao method
func newTxnCreateArgMatcher(txn *paymentPb.Transaction) *txnCreateArgMatcher {
	return &txnCreateArgMatcher{
		want: txn,
	}
}

func (t *txnCreateArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*paymentPb.Transaction)
	if !ok {
		return false
	}

	if len(got.DetailedStatus.GetDetailedStatusList()) != len(t.want.DetailedStatus.GetDetailedStatusList()) {
		return false
	}
	updateDetailedStatus(got, t.want)
	return proto.Equal(got, t.want)
}

func (t *txnCreateArgMatcher) String() string {
	return t.want.String()
}

func updateDetailedStatus(got, want *paymentPb.Transaction) {
	if ln := len(want.DetailedStatus.GetDetailedStatusList()); ln > 0 {
		want.DetailedStatus.DetailedStatusList[ln-1].CreatedAt = got.DetailedStatus.DetailedStatusList[ln-1].CreatedAt
		want.DetailedStatus.DetailedStatusList[ln-1].UpdatedAt = got.DetailedStatus.DetailedStatusList[ln-1].UpdatedAt
	}
}

func TestService_InitiateAddFundsCollect(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockVgUpiClient := mocks2.NewMockUPIClient(ctr)
	mockTxnDao := mocks.NewMockTransactionDao(ctr)
	mockOrderDao := mocks.NewMockOrderDao(ctr)
	mockAuthClient := authMock.NewMockAuthClient(ctr)
	mockActorClient := actorMock.NewMockActorClient(ctr)
	mockPiClient := mockPi.NewMockPiClient(ctr)
	mockEventsBroker := eventsMock.NewMockBroker(ctr)
	mockAccountPiClinet := mocks3.NewMockAccountPIRelationClient(ctr)
	mockEventsBroker.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockActorProcessor := mocks4.NewMockActorProcessor(ctr)
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	svc := payment.NewService(mockTxnDao, mockOrderDao, mockVgPaymentClient, ts.idg, mockPiClient, nil, mockAuthClient, mockVgUpiClient, mockActorClient, nil, nil, nil, nil, nil, mockEventsBroker, ts.conf, nil, nil, mockAccountPiClinet, mockUpiProcessor, mockActorProcessor, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)
	type mockGetByIdWithTransactions struct {
		enable        bool
		orderId       string
		orderWithTxns *orderPb.OrderWithTransactions
		err           error
	}
	type mockCreateTxn struct {
		enable   bool
		txn      *paymentPb.Transaction
		reqInfo  *paymentPb.PaymentRequestInformation
		savedTxn *paymentPb.Transaction
		orderId  string
		err      error
	}
	type mockGetByIdWithPaymentReqInfo struct {
		enable  bool
		txn     *paymentPb.Transaction
		reqInfo *paymentPb.PaymentRequestInformation
		txnId   string
		err     error
	}
	type mockGetInternalDevice struct {
		enable bool
		req    string
		res    *upiPb.Device
		err    error
	}
	type mockGetEntityDetailsByActorId struct {
		enable bool
		req    *actorPb.GetEntityDetailsByActorIdRequest
		res    *actorPb.GetEntityDetailsByActorIdResponse
		err    error
	}
	type mockGetPiById struct {
		req *piPb.GetPiByIdRequest
		res *piPb.GetPiByIdResponse
		err error
	}
	type mockReqPay struct {
		enable bool
		req    *upi.ReqPayRequest
		res    *upi.ReqPayResponse
		err    error
	}
	type mockUpdateTxn struct {
		txn        *paymentPb.Transaction
		reqInfo    *paymentPb.PaymentRequestInformation
		updateMask []paymentPb.TransactionFieldMask
		err        error
	}
	type mockGetPiByActorId struct {
		enable bool
		req    *accountPiPb.GetPiByActorIdRequest
		res    *accountPiPb.GetPiByActorIdResponse
		err    error
	}
	type mockIsNewAddFundsVpaEnabledForActor struct {
		enable  bool
		actorId string
		want    bool
		err     error
	}
	payload := &orderPb.P2PCollect{
		PaymentDetails: &orderPb.PaymentDetails{
			PiFrom: "pi-1",
			PiTo:   "pi-2",
			Amount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100,
			},
			Remarks:         "remarks",
			ReqId:           "req-1",
			PaymentProtocol: paymentPb.PaymentProtocol_UPI,
			MerchantRefId:   "ref-1",
		},
	}
	paylaodBytes, err := protojson.Marshal(payload)
	assert.Nil(t, err)

	currentTime := time.Now()

	tests := []struct {
		name                                string
		req                                 *domainPb.ProcessCollectRequest
		res                                 *domainPb.ProcessCollectResponse
		mockGetByIdWithTransactions         mockGetByIdWithTransactions
		mockCreateTxn                       mockCreateTxn
		mockGetByIdWithPaymentReqInfo       mockGetByIdWithPaymentReqInfo
		mockGetInternalDevice               mockGetInternalDevice
		mockGetEntityDetailsByActorId       mockGetEntityDetailsByActorId
		mockGetPiByIds                      []mockGetPiById
		mockReqPay                          mockReqPay
		mockUpdateTxns                      []mockUpdateTxn
		mockGetPiByActorId                  mockGetPiByActorId
		mockIsNewAddFundsVpaEnabledForActor []mockIsNewAddFundsVpaEnabledForActor
		wantErr                             bool
	}{
		{
			name: "no order present for the given order id",
			req: &domainPb.ProcessCollectRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-random",
					IsLastAttempt:   false,
				},
			},
			mockGetByIdWithTransactions: mockGetByIdWithTransactions{
				enable:  true,
				orderId: "order-random",
				err:     epifierrors.ErrRecordNotFound,
			},
			res: &domainPb.ProcessCollectResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE},
			},
		},
		{
			name: "txn already in terminal state",
			req: &domainPb.ProcessCollectRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-1",
					IsLastAttempt:   false,
				},
				Payload: paylaodBytes,
			},
			mockGetByIdWithTransactions: mockGetByIdWithTransactions{
				enable:  true,
				orderId: "order-1",
				orderWithTxns: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: "order-1",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:     "txn-1",
							PiTo:   pay.FederalPoolAccountPiId,
							Status: paymentPb.TransactionStatus_FAILED,
						},
					},
				},
			},
			res: &domainPb.ProcessCollectResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_PERMANENT_FAILURE},
			},
		},
		{
			name: "create a new txn and process successfully",
			req: &domainPb.ProcessCollectRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-1",
					IsLastAttempt:   false,
				},
				Payload: paylaodBytes,
			},
			mockGetByIdWithTransactions: mockGetByIdWithTransactions{
				enable:  true,
				orderId: "order-1",
				orderWithTxns: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:           "order-1",
						ToActorId:    "actor-1",
						Workflow:     orderPb.OrderWorkflow_ADD_FUNDS_COLLECT,
						OrderPayload: paylaodBytes,
					},
				},
			},
			mockIsNewAddFundsVpaEnabledForActor: []mockIsNewAddFundsVpaEnabledForActor{
				{
					enable:  true,
					actorId: "actor-1",
					want:    false,
				},
				{
					enable:  true,
					actorId: "actor-1",
					want:    false,
				},
			},
			mockCreateTxn: mockCreateTxn{
				enable:  true,
				orderId: "order-1",
				txn: &paymentPb.Transaction{
					PiFrom:      "pi-1",
					PiTo:        pay.FederalPoolAccountPiId,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
					DedupeId: &paymentPb.DedupeId{
						RequestId: "req-1",
					},
					Remarks:         "remarks",
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					Status:          paymentPb.TransactionStatus_CREATED,
					Ownership:       commontypes.Ownership_FEDERAL_BANK,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "req-1",
				},
				savedTxn: &paymentPb.Transaction{
					Id:          "txn-1",
					PiFrom:      "pi-1",
					PiTo:        pay.FederalPoolAccountPiId,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
					DedupeId: &paymentPb.DedupeId{
						RequestId: "req-1",
					},
					Remarks:         "remarks",
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					Status:          paymentPb.TransactionStatus_CREATED,
					CreatedAt:       timestampPb.New(currentTime),
				},
			},
			mockGetByIdWithPaymentReqInfo: mockGetByIdWithPaymentReqInfo{
				enable: true,
				txnId:  "txn-1",
				txn: &paymentPb.Transaction{
					PiFrom:      "pi-1",
					PiTo:        pay.FederalPoolAccountPiId,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
					Remarks:         "remarks",
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					Status:          paymentPb.TransactionStatus_CREATED,
					CreatedAt:       timestampPb.New(currentTime),
					Ownership:       commontypes.Ownership_FEDERAL_BANK,
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "req-1",
				},
			},
			mockGetInternalDevice: mockGetInternalDevice{
				enable: true,
				req:    "actor-1",
				res:    &upiPb.Device{},
			},
			mockGetEntityDetailsByActorId: mockGetEntityDetailsByActorId{
				enable: true,
				req:    &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-1"},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status: rpc.StatusOk(),
					MobileNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
			},
			mockGetPiByIds: []mockGetPiById{
				{
					req: &piPb.GetPiByIdRequest{Id: "pi-1"},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:                    "random@icici",
									AccountReferenceNumber: "random-account-ref",
									MaskedAccountNumber:    "xxxxxxx7890",
									IfscCode:               "FED00001",
									AccountType:            accountsPb.Type_SAVINGS,
								},
							},
							VerifiedName: "random-name",
						},
					},
				},
			},
			mockReqPay: mockReqPay{
				enable: true,
				res: &upi.ReqPayResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockUpdateTxns: []mockUpdateTxn{
				{
					txn: &paymentPb.Transaction{
						PiFrom:      "pi-1",
						PiTo:        pay.FederalPoolAccountPiId,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						Remarks:         "remarks",
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_INITIATED,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_COLLECT_INITIATED,
						LocationToken:   "",
						CreatedAt:       timestampPb.New(currentTime),
						Ownership:       commontypes.Ownership_FEDERAL_BANK,
					},
					reqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "req-1",
					},
					updateMask: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
						paymentPb.TransactionFieldMask_PROTOCOL_STATUS, paymentPb.TransactionFieldMask_UTR,
						paymentPb.TransactionFieldMask_LOCATION_TOKEN},
				}, {
					txn: &paymentPb.Transaction{
						PiFrom:      "pi-1",
						PiTo:        pay.FederalPoolAccountPiId,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						Remarks:         "remarks",
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_IN_PROGRESS,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_COLLECT_REQ_PAY_SENT,
						LocationToken:   "",
						DetailedStatus: &paymentPb.TransactionDetailedStatus{
							DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
								{
									State: paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
									Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_PAY,
								},
							},
						},
						CreatedAt: timestampPb.New(currentTime),
						Ownership: commontypes.Ownership_FEDERAL_BANK,
					},
					updateMask: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
						paymentPb.TransactionFieldMask_PROTOCOL_STATUS, paymentPb.TransactionFieldMask_DETAILED_STATUS},
				},
			},
			mockGetPiByActorId: mockGetPiByActorId{
				enable: true,
				req: &accountPiPb.GetPiByActorIdRequest{
					ActorId: "actor-1",
				},
				res: &accountPiPb.GetPiByActorIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									ActualAccountNumber: "**********",
									AccountType:         accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
			},
			res: &domainPb.ProcessCollectResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_SUCCESS},
			},
		},
		{
			name: "successfully process already exisitng txn",
			req: &domainPb.ProcessCollectRequest{
				RequestHeader: &domainPb.DomainRequestHeader{
					ClientRequestId: "order-1",
					IsLastAttempt:   false,
				},
				Payload: paylaodBytes,
			},
			mockGetByIdWithTransactions: mockGetByIdWithTransactions{
				enable:  true,
				orderId: "order-1",
				orderWithTxns: &orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id:           "order-1",
						ToActorId:    "actor-1",
						Workflow:     orderPb.OrderWorkflow_ADD_FUNDS_COLLECT,
						OrderPayload: paylaodBytes,
					},
					Transactions: []*paymentPb.Transaction{
						{Id: "txn-1",
							PiFrom:      "pi-1",
							PiTo:        pay.FederalPoolAccountPiId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
							Remarks:         "remarks",
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							Status:          paymentPb.TransactionStatus_CREATED,
						},
					},
				},
			},
			mockIsNewAddFundsVpaEnabledForActor: []mockIsNewAddFundsVpaEnabledForActor{
				{
					enable:  true,
					actorId: "actor-1",
					want:    false,
				},
			},
			mockGetByIdWithPaymentReqInfo: mockGetByIdWithPaymentReqInfo{
				enable: true,
				txnId:  "txn-1",
				txn: &paymentPb.Transaction{
					PiFrom:      "pi-1",
					PiTo:        pay.FederalPoolAccountPiId,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        100,
					},
					Remarks:         "remarks",
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					Status:          paymentPb.TransactionStatus_CREATED,
					CreatedAt:       timestampPb.New(currentTime),
				},
				reqInfo: &paymentPb.PaymentRequestInformation{
					ReqId: "req-1",
				},
			},
			mockGetInternalDevice: mockGetInternalDevice{
				enable: true,
				req:    "actor-1",
				res:    &upiPb.Device{},
			},
			mockGetEntityDetailsByActorId: mockGetEntityDetailsByActorId{
				enable: true,
				req:    &actorPb.GetEntityDetailsByActorIdRequest{ActorId: "actor-1"},
				res: &actorPb.GetEntityDetailsByActorIdResponse{
					Status: rpc.StatusOk(),
					MobileNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
				},
			},
			mockGetPiByIds: []mockGetPiById{
				{
					req: &piPb.GetPiByIdRequest{Id: "pi-1"},
					res: &piPb.GetPiByIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstrument: &piPb.PaymentInstrument{
							Id:   "pi-1",
							Type: piPb.PaymentInstrumentType_UPI,
							Identifier: &piPb.PaymentInstrument_Upi{
								Upi: &piPb.Upi{
									Vpa:                    "random@icici",
									AccountReferenceNumber: "random-account-ref",
									MaskedAccountNumber:    "xxxxxxx7890",
									IfscCode:               "FED00001",
									AccountType:            accountsPb.Type_SAVINGS,
								},
							},
							VerifiedName: "random-name",
						},
					},
				},
			},
			mockGetPiByActorId: mockGetPiByActorId{
				enable: true,
				req: &accountPiPb.GetPiByActorIdRequest{
					ActorId: "actor-1",
				},
				res: &accountPiPb.GetPiByActorIdResponse{
					Status: rpc.StatusOk(),
					PaymentInstruments: []*piPb.PaymentInstrument{
						{
							Id:   "pi-3",
							Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
							Identifier: &piPb.PaymentInstrument_Account{
								Account: &piPb.Account{
									ActualAccountNumber: "**********",
									AccountType:         accountsPb.Type_SAVINGS,
								},
							},
						},
					},
				},
			},
			mockReqPay: mockReqPay{
				enable: true,
				res: &upi.ReqPayResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockUpdateTxns: []mockUpdateTxn{
				{
					txn: &paymentPb.Transaction{
						PiFrom:      "pi-1",
						PiTo:        pay.FederalPoolAccountPiId,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						Remarks:         "remarks",
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_INITIATED,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_COLLECT_INITIATED,
						LocationToken:   "",
						CreatedAt:       timestampPb.New(currentTime),
					},
					reqInfo: &paymentPb.PaymentRequestInformation{
						ReqId: "req-1",
					},
					updateMask: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
						paymentPb.TransactionFieldMask_PROTOCOL_STATUS, paymentPb.TransactionFieldMask_UTR,
						paymentPb.TransactionFieldMask_LOCATION_TOKEN},
				}, {
					txn: &paymentPb.Transaction{
						PiFrom:      "pi-1",
						PiTo:        pay.FederalPoolAccountPiId,
						PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        100,
						},
						Remarks:         "remarks",
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_IN_PROGRESS,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_COLLECT_REQ_PAY_SENT,
						LocationToken:   "",
						DetailedStatus: &paymentPb.TransactionDetailedStatus{
							DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
								{
									State: paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
									Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_PAY,
								},
							},
						},
						CreatedAt: timestampPb.New(currentTime),
					},
					updateMask: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
						paymentPb.TransactionFieldMask_PROTOCOL_STATUS, paymentPb.TransactionFieldMask_DETAILED_STATUS},
				},
			},
			res: &domainPb.ProcessCollectResponse{
				ResponseHeader: &domainPb.DomainResponseHeader{Status: domainPb.DomainProcessingStatus_SUCCESS},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockGetByIdWithTransactions.enable {
				mockOrderDao.EXPECT().GetByIdWithTransactions(context.Background(), tt.mockGetByIdWithTransactions.orderId).
					Return(tt.mockGetByIdWithTransactions.orderWithTxns, tt.mockGetByIdWithTransactions.err)
			}
			if tt.mockCreateTxn.enable {
				mockTxnDao.EXPECT().Create(context.Background(), newTxnCreateArgMatcher(tt.mockCreateTxn.txn), gomock.Any(), tt.mockCreateTxn.orderId, commontypes.Ownership_EPIFI_TECH).
					Return(tt.mockCreateTxn.savedTxn, tt.mockCreateTxn.err)

			}
			if tt.mockGetByIdWithPaymentReqInfo.enable {
				mockTxnDao.EXPECT().GetByIdWithPaymentReqInfo(context.Background(), tt.mockGetByIdWithPaymentReqInfo.txnId).
					Return(tt.mockGetByIdWithPaymentReqInfo.txn, tt.mockGetByIdWithPaymentReqInfo.reqInfo, tt.mockGetByIdWithPaymentReqInfo.err)
			}
			if tt.mockGetInternalDevice.enable {
				mockActorProcessor.EXPECT().GetInternalDeviceFromRegisteredDevice(context.Background(), tt.mockGetInternalDevice.req).
					Return(tt.mockGetInternalDevice.res, tt.mockGetInternalDevice.err)
			}
			if tt.mockGetEntityDetailsByActorId.enable {
				mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), tt.mockGetEntityDetailsByActorId.req).
					Return(tt.mockGetEntityDetailsByActorId.res, tt.mockGetEntityDetailsByActorId.err)
			}
			for _, mockGetPi := range tt.mockGetPiByIds {
				mockPiClient.EXPECT().GetPiById(context.Background(), mockGetPi.req).
					Return(mockGetPi.res, mockGetPi.err)
			}
			for _, mockUpdate := range tt.mockUpdateTxns {
				mockTxnDao.EXPECT().Update(context.Background(), newTxnUpdateArgMatcher(mockUpdate.txn), mockUpdate.reqInfo, mockUpdate.updateMask).
					Return(mockUpdate.err)
			}
			if tt.mockReqPay.enable {
				mockVgUpiClient.EXPECT().ReqPay(context.Background(), gomock.AssignableToTypeOf(tt.mockReqPay.req)).
					Return(tt.mockReqPay.res, tt.mockReqPay.err)
			}
			if tt.mockGetPiByActorId.enable {
				mockAccountPiClinet.EXPECT().GetPiByActorId(context.Background(), tt.mockGetPiByActorId.req).
					Return(tt.mockGetPiByActorId.res, tt.mockGetPiByActorId.err)
			}
			for _, mockIsNewAddFundsVpaEnabled := range tt.mockIsNewAddFundsVpaEnabledForActor {
				if mockIsNewAddFundsVpaEnabled.enable {
					mockUpiProcessor.EXPECT().IsNewAddFundsVpaEnabledForActor(gomock.Any(), mockIsNewAddFundsVpaEnabled.actorId).
						Return(mockIsNewAddFundsVpaEnabled.want, mockIsNewAddFundsVpaEnabled.err)
				}
			}
			got, err := svc.InitiateAddFundsCollect(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateAddFundsCollect() got err :%v, wantErr :%v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.res) {
				t.Errorf("InitiateAddFundsCollect() got :%v, want :%v", got, tt.res)
				return
			}
		})
	}
}
