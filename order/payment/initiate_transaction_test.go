package payment_test

import (
	"context"
	"errors"
	"log"
	"sync"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/jinzhu/copier"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	gormv2 "gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	mockCelestial "github.com/epifi/be-common/api/celestial/mocks"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	brokerMocks "github.com/epifi/be-common/pkg/events/mocks"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/queue"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	"google.golang.org/protobuf/encoding/protojson"

	accountPb "github.com/epifi/gamma/api/accounts"
	accountsPb "github.com/epifi/gamma/api/accounts"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	payMocks "github.com/epifi/gamma/api/pay/mocks"
	signalPb "github.com/epifi/gamma/api/pay/signal"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	timelineMocks "github.com/epifi/gamma/api/timeline/mocks"
	upiPb "github.com/epifi/gamma/api/upi"
	upiMocks "github.com/epifi/gamma/api/upi/mocks"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/mocks"
	vgUPIPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	mockVgUpi "github.com/epifi/gamma/api/vendorgateway/openbanking/upi/mocks"
	config "github.com/epifi/gamma/order/config/genconf"
	"github.com/epifi/gamma/order/dao"
	daoMocks "github.com/epifi/gamma/order/dao/mocks"
	mock_payment "github.com/epifi/gamma/order/internal/payment/mocks"
	mocksUpiProcessor "github.com/epifi/gamma/order/internal/upi/mocks"
	"github.com/epifi/gamma/order/payment"
	"github.com/epifi/gamma/order/test/mocks"
)

const (
	upiGlobalQr = "upiGlobal://pay?ver=01&mode=02&purpose=11&orgId=159991&tr=MerRef123&tn=Global%20Merchant&pa=globalmerchant@npci&pn=Global%merchant&mc=5411&mid=123412&msid=321231&mtid=123123&cc=SGP&qrMedium=04&invoiceNo=BillRef123&invoiceDate=2019-06-11T13:21:50+05:30&invoiceName=Global%20Merchant&sign=MEYCIQDaaEAL06Vsn9aNIarP7dai8/h9cMrVvuYe+uly0rYIMwIhAPRjz6Cj1ZodDLf/NZIGYnW4gypE84DNDzRETQTY1IpM"
)

type InitiateTransactionTestSuite struct {
	db            *gormv2.DB
	conf          *config.Config
	txnDao        dao.TransactionDao
	orderDao      dao.OrderDao
	idg           idgen.NumberIDGenerator
	paymentServer paymentPb.PaymentServer
	dbName        string
}

func NewInitiateTransactionTestSuite(db *gormv2.DB, conf *config.Config, txnDao dao.TransactionDao, orderDao dao.OrderDao, idg idgen.NumberIDGenerator,
	paymentServer paymentPb.PaymentServer, dbName string) InitiateTransactionTestSuite {
	return InitiateTransactionTestSuite{db: db, conf: conf, txnDao: txnDao, orderDao: orderDao, idg: idg, paymentServer: paymentServer, dbName: dbName}
}

// customer payment request matcher, enables test to match arguments based on defined logic
// e.g. there are 2 fields which are auto generated in a payment request which cant be predicted before hand.
// thus, this customer argument matcher enables us to match the  pay request struct ignoring the autogenerated fields.
type VgPayRequestMatcher struct {
	want *vgPaymentPb.PayRequest
}

func vgPayRequestMatcher(req *vgPaymentPb.PayRequest) *VgPayRequestMatcher {
	return &VgPayRequestMatcher{want: req}
}
func (p *VgPayRequestMatcher) Matches(x interface{}) bool {
	got := &vgPaymentPb.PayRequest{}
	if err := copier.Copy(got, x); err != nil {
		return false
	}
	p.want.TransTime = got.TransTime
	p.want.Auth = got.Auth
	return proto.Equal(p.want, got)
}

func (p *VgPayRequestMatcher) String() string {
	return p.want.String()
}

var (
	ts InitiateTransactionTestSuite

	accountPi1 = &piPb.PaymentInstrument{
		Id:   "pi-1",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "*********",
				SecureAccountNumber: "1234XXXXX",
				IfscCode:            "FED0001",
				AccountType:         accountPb.Type_SAVINGS,
			},
		},
		VerifiedName: "User-1",
	}

	upiPi = &piPb.PaymentInstrument{
		Id:   "pi-3",
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{
			Upi: &piPb.Upi{
				Vpa:                    "nitesh@fede",
				AccountReferenceNumber: "random-account-ref",
				MaskedAccountNumber:    "xxxxxxx7890",
				IfscCode:               "FED00001",
				AccountType:            accountsPb.Type_SAVINGS,
			},
		},
		VerifiedName: "nitesh",
	}

	accountPi2 = &piPb.PaymentInstrument{
		Id:   "pi-2",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "*********",
				SecureAccountNumber: "9876XXXXX",
				IfscCode:            "FED0001",
				AccountType:         accountPb.Type_SAVINGS,
			},
		},
		VerifiedName: "User-2",
	}

	externalAccountPi = &piPb.PaymentInstrument{
		Id:   "pi-ex-3",
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.PaymentInstrument_Account{
			Account: &piPb.Account{
				ActualAccountNumber: "***************",
				SecureAccountNumber: "xxxxxxxxxxx2941",
				IfscCode:            "ICIC0001",
				AccountType:         accountPb.Type_SAVINGS,
			}},
		VerifiedName: "user-xyz",
	}

	savingsPiFrom = &savingsPb.Account{
		Id:        "savings-1",
		AccountNo: "*********",
		IfscCode:  "FED0001",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: ***********,
		},
		EmailId:     "<EMAIL>",
		PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
	}

	paymentAuth = &paymentPb.InitiateTransactionRequest_AuthHeader{
		Device: &upiPb.Device{Id: "device-1", LocationToken: "location-token"},
	}

	remitterAccount = &vgPaymentPb.Account{
		AccountNumber:     "*********",
		IfscCode:          "FED0001",
		AccountType:       accountPb.Type_SAVINGS,
		AccountHolderName: "User-1",
		PhoneNumber: &commontypes.PhoneNumber{
			CountryCode:    91,
			NationalNumber: ***********,
		},
		Email: "<EMAIL>",
	}

	beneficiaryAccount = &vgPaymentPb.Account{
		AccountNumber:     "*********",
		IfscCode:          "FED0001",
		AccountType:       accountPb.Type_SAVINGS,
		AccountHolderName: "User-2",
	}

	vgPaymentReq = &vgPaymentPb.PayRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		RequestId:   fixturesTransaction1.ReqId,
		Auth:        nil, // TODO(nitesh): fix this when auth header is set across
		Remitter:    remitterAccount,
		Beneficiary: beneficiaryAccount,
		Amount:      fixturesTransaction1.Amount,
		UserRemarks: fixturesTransaction1.Remarks,
		Protocol:    paymentPb.PaymentProtocol_IMPS,
	}
)

func TestService_InitiateTransaction_SuccessfulPayment(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(

		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockPaymentsHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, mockPaymentsHealthProcessor, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiCall := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi1,
		}, nil)

	mockPiCall2 := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi2.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi2,
		}, nil).After(mockPiCall)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi1,
		}, nil).After(mockPiCall2)

	mockSavingsClient.
		EXPECT().
		GetAccount(context.Background(), &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ExternalId{
				ExternalId: &savingsPb.BankAccountIdentifier{
					AccountNo: "*********",
					IfscCode:  "FED0001",
				},
			},
		}).
		Return(&savingsPb.GetAccountResponse{
			Account: savingsPiFrom,
		}, nil)

	mockVgPaymentClient.EXPECT().
		Pay(context.Background(), vgPayRequestMatcher(vgPaymentReq)).
		Return(&vgPaymentPb.PayResponse{
			Status: rpc.StatusOk(),
		}, nil)

	mockOrderOrchPublisher.
		EXPECT().
		Publish(context.Background(), gomock.AssignableToTypeOf(&orderPb.ProcessOrderRequest{})).
		Return("random-msg", nil)

	mockPayOrchPublisher.
		EXPECT().
		PublishWithDelay(context.Background(), gomock.AssignableToTypeOf(&paymentPb.ProcessPaymentRequest{}), gomock.AssignableToTypeOf(time.Duration(0))).
		Return("random-msg-id", nil)

	mockPaymentsHealthProcessor.EXPECT().
		UpdateTransactionAndPushMetrics(gomock.Any(), gomock.Any(), nil, gomock.Any()).
		Return(nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction1.ReqId,
		OrderId:          fixturesOrder1Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusOk(), res.Status)
	assert.NotNil(t, res.Transaction)

	fetchedTransaction, payReqInfo, err := ts.txnDao.GetByIdWithPaymentReqInfo(context.Background(), res.Transaction.Id)
	assert.Nil(t, err)
	assert.NotNil(t, fetchedTransaction)
	expectedTxn := fixturesTransaction1
	expectedTxn.LocationToken = req.PaymentAuth.Device.LocationToken
	expectedTxn.Status = paymentPb.TransactionStatus_INITIATED
	expectedTxn.DetailedStatus = fetchedTransaction.DetailedStatus
	expectedPayReqId := &paymentPb.PaymentRequestInformation{
		DeviceId: "device-1",
		ReqId:    fixturesTransaction1.ReqId,
	}

	log.Println("Got\n\n", fetchedTransaction)
	log.Println("Expected \n\n", expectedTxn)

	assertTransaction(t, expectedTxn, fetchedTransaction)
	assert.True(t, proto.Equal(expectedPayReqId, payReqInfo))
}

func TestService_InitiateTransaction_SuccessfulUPIPayment(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgUPIClient := mockVgUpi.NewMockUPIClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockPaymentsHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, nil, ts.idg, mockPiClient, nil, nil, mockVgUPIClient, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, mockPaymentsHealthProcessor, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiCall := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: upiPi,
		}, nil)

	mockPiCall2 := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi2.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi2,
		}, nil).After(mockPiCall)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: upiPi,
		}, nil).After(mockPiCall2)

	mockVgUPIClient.EXPECT().
		ReqPay(context.Background(), gomock.AssignableToTypeOf(&vgUPIPb.ReqPayRequest{})).
		Return(&vgUPIPb.ReqPayResponse{
			Status: rpc.StatusOk(),
		}, nil)

	mockPaymentsHealthProcessor.EXPECT().UpdateAndChangeStatusAndPushMetrics(context.Background(), gomock.Any(), gomock.Any(), []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_PROTOCOL_STATUS,
		paymentPb.TransactionFieldMask_LOCATION_TOKEN, paymentPb.TransactionFieldMask_UTR}, paymentPb.TransactionStatus_CREATED, paymentPb.TransactionStatus_INITIATED).Return(nil)

	mockPaymentsHealthProcessor.EXPECT().
		UpdateTransactionAndPushMetrics(gomock.Any(), gomock.Any(), nil, gomock.Any()).
		Return(nil)

	mockOrderOrchPublisher.
		EXPECT().
		Publish(context.Background(), gomock.AssignableToTypeOf(&orderPb.ProcessOrderRequest{})).
		Return("random-msg", nil)

	mockPayOrchPublisher.
		EXPECT().
		PublishWithDelay(context.Background(), gomock.AssignableToTypeOf(&paymentPb.ProcessPaymentRequest{}), gomock.AssignableToTypeOf(time.Duration(0))).
		Return("random-msg-id", nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction11.ReqId,
		OrderId:          fixturesOrder11Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusOk(), res.Status)
	assert.NotNil(t, res.Transaction)

	fetchedTransaction, err := ts.txnDao.GetById(context.Background(), res.Transaction.Id)
	assert.Nil(t, err)
	assert.NotNil(t, fetchedTransaction)
	expectedTxn := fixturesTransaction11
	expectedTxn.LocationToken = req.PaymentAuth.Device.LocationToken
	expectedTxn.Status = paymentPb.TransactionStatus_IN_PROGRESS
	expectedTxn.ProtocolStatus = paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT
	expectedTxn.DetailedStatus = fetchedTransaction.DetailedStatus
	expectedTxn.Utr = fetchedTransaction.Utr
}

func TestService_InitiateTransaction_SuccessfulUPILitePaymentForWorkflow(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgUPIClient := mockVgUpi.NewMockUPIClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockPaymentsHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)
	mockCelestialClient := mockCelestial.NewMockCelestialClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, nil, ts.idg, mockPiClient, nil, nil, mockVgUPIClient, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, mockPaymentsHealthProcessor, nil, nil, mockCelestialClient, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiCall := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: upiPi,
		}, nil)

	mockPiCall2 := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi2.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi2,
		}, nil).After(mockPiCall)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: upiPi,
		}, nil).After(mockPiCall2)

	mockVgUPIClient.EXPECT().
		ReqPay(context.Background(), gomock.AssignableToTypeOf(&vgUPIPb.ReqPayRequest{})).
		Return(&vgUPIPb.ReqPayResponse{
			Status: rpc.StatusOk(),
		}, nil)

	mockPaymentsHealthProcessor.EXPECT().UpdateAndChangeStatusAndPushMetrics(context.Background(), gomock.Any(), gomock.Any(), []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_PROTOCOL_STATUS,
		paymentPb.TransactionFieldMask_LOCATION_TOKEN, paymentPb.TransactionFieldMask_UTR}, paymentPb.TransactionStatus_CREATED, paymentPb.TransactionStatus_INITIATED).Return(nil)

	mockPaymentsHealthProcessor.EXPECT().
		UpdateTransactionAndPushMetrics(gomock.Any(), gomock.Any(), nil, gomock.Any()).
		Return(nil)

	mockOrderOrchPublisher.
		EXPECT().
		Publish(context.Background(), gomock.AssignableToTypeOf(&orderPb.ProcessOrderRequest{})).
		Return("random-msg", nil)

	mockPayOrchPublisher.
		EXPECT().
		PublishWithDelay(context.Background(), gomock.AssignableToTypeOf(&paymentPb.ProcessPaymentRequest{}), gomock.AssignableToTypeOf(time.Duration(0))).
		Return("random-msg-id", nil)

	mockCelestialClient.EXPECT().SignalWorkflow(context.Background(), gomock.AssignableToTypeOf(&celestialPb.SignalWorkflowRequest{}), gomock.Any()).
		Return(&celestialPb.SignalWorkflowResponse{Status: rpc.StatusOk()}, nil).AnyTimes()

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction52.ReqId,
		OrderId:          fixtureOrder52Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusOk(), res.Status)
	assert.NotNil(t, res.Transaction)

	fetchedTransaction, err := ts.txnDao.GetById(context.Background(), res.Transaction.Id)
	assert.Nil(t, err)
	assert.NotNil(t, fetchedTransaction)
	expectedTxn := fixturesTransaction52
	expectedTxn.LocationToken = req.PaymentAuth.Device.LocationToken
	expectedTxn.Status = paymentPb.TransactionStatus_IN_PROGRESS
	expectedTxn.ProtocolStatus = paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT
	expectedTxn.DetailedStatus = fetchedTransaction.DetailedStatus
	expectedTxn.Utr = fetchedTransaction.Utr
}

func TestService_InitiateTransaction_SuccessfulInternationalUPIPayment(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgUPIClient := mockVgUpi.NewMockUPIClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockPaymentsHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)
	mockUpiClient := upiMocks.NewMockUPIClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, nil, ts.idg, mockPiClient, nil, nil, mockVgUPIClient, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, mockPaymentsHealthProcessor, mockUpiClient, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	upiInternationalQrInfo := &upiPb.UpiInternationalQrInfo{
		QrDetails: &upiPb.QRDetails{QrMedium: upiPb.QrMedium_QR_MEDIUM_APP},
		ForexDetailList: []*upiPb.FxDetail{
			{
				BaseAmount:     1.00,
				BaseCurrency:   "USD",
				ConversionRate: 100.00,
				Markup:         2.5,
			},
		},
		Institution: &upiPb.Institution{
			QrPayload: upiGlobalQr,
		},
	}

	mockPiCall := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: upiPi,
		}, nil)

	mockPiCall2 := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi2.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi2,
		}, nil).After(mockPiCall)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: upiPi,
		}, nil).After(mockPiCall2)

	mockVgUPIClient.EXPECT().
		ReqPay(context.Background(), gomock.AssignableToTypeOf(&vgUPIPb.ReqPayRequest{})).
		Return(&vgUPIPb.ReqPayResponse{
			Status: rpc.StatusOk(),
		}, nil)

	mockPaymentsHealthProcessor.EXPECT().UpdateAndChangeStatusAndPushMetrics(context.Background(), gomock.Any(), gomock.Any(), []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_PROTOCOL_STATUS,
		paymentPb.TransactionFieldMask_LOCATION_TOKEN, paymentPb.TransactionFieldMask_UTR}, paymentPb.TransactionStatus_CREATED, paymentPb.TransactionStatus_INITIATED).Return(nil)

	mockPaymentsHealthProcessor.EXPECT().
		UpdateTransactionAndPushMetrics(gomock.Any(), gomock.Any(), nil, gomock.Any()).
		Return(nil)

	mockOrderOrchPublisher.
		EXPECT().
		Publish(context.Background(), gomock.AssignableToTypeOf(&orderPb.ProcessOrderRequest{})).
		Return("random-msg", nil)

	mockPayOrchPublisher.
		EXPECT().
		PublishWithDelay(context.Background(), gomock.AssignableToTypeOf(&paymentPb.ProcessPaymentRequest{}), gomock.AssignableToTypeOf(time.Duration(0))).
		Return("random-msg-id", nil)

	mockUpiClient.EXPECT().GetUpiInternationalQrInfoFromCache(context.Background(), &upiPb.GetUpiInternationalQrInfoFromCacheRequest{
		ActorId: "actor-id",
	}).Return(&upiPb.GetUpiInternationalQrInfoFromCacheResponse{
		Status:                 rpc.StatusOk(),
		UpiInternationalQrInfo: upiInternationalQrInfo,
	}, nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction11.ReqId,
		OrderId:          fixturesOrder11Id,
		Urn:              upiGlobalQr,
		BaseAmountQuoteCurrency: &gmoney.Money{
			CurrencyCode: "USD",
			Units:        50,
			Nanos:        0,
		},
		ActorId: "actor-id",
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusOk(), res.Status)
	assert.NotNil(t, res.Transaction)

	fetchedTransaction, err := ts.txnDao.GetById(context.Background(), res.Transaction.Id)
	assert.Nil(t, err)
	assert.NotNil(t, fetchedTransaction)
	expectedTxn := fixturesTransaction11
	expectedTxn.LocationToken = req.PaymentAuth.Device.LocationToken
	expectedTxn.Status = paymentPb.TransactionStatus_IN_PROGRESS
	expectedTxn.ProtocolStatus = paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT
	expectedTxn.DetailedStatus = fetchedTransaction.DetailedStatus
	expectedTxn.Utr = fetchedTransaction.Utr
}

func TestService_InitiateTransaction_Account_RPC_Failure(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi1,
		}, nil)

	mockSavingsClient.
		EXPECT().
		GetAccount(context.Background(), &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ExternalId{
				ExternalId: &savingsPb.BankAccountIdentifier{
					AccountNo: "*********",
					IfscCode:  "FED0001",
				},
			},
		}).
		Return(nil, errors.New("savings RPC error"))

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction1.ReqId,
		OrderId:          fixturesOrder1Id,
	}
	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusInternal(), res.Status)
	assert.Nil(t, res.Transaction)

}

func TestService_InitiateTransaction_PI_RPC_Failure(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi1,
		}, nil)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi2.Id}).
		Return(nil, errors.New("PI rpc failure"))

	mockSavingsClient.
		EXPECT().
		GetAccount(context.Background(), &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ExternalId{
				ExternalId: &savingsPb.BankAccountIdentifier{
					AccountNo: "*********",
					IfscCode:  "FED0001",
				},
			},
		}).
		Return(&savingsPb.GetAccountResponse{
			Account: savingsPiFrom,
		}, nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction1.ReqId,
		OrderId:          fixturesOrder1Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusInternal(), res.Status)
	assert.Nil(t, res.Transaction)
}

func TestService_InitiateTransaction_PI_Type_NotAccount(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Type: piPb.PaymentInstrumentType_CREDIT_CARD,
			},
		}, nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction1.ReqId,
		OrderId:          fixturesOrder1Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusInvalidArgument(), res.Status)
	assert.Nil(t, res.Transaction)
}

func TestService_InitiateTransaction_UPI_PI_From_BankAccount(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgUPIClient := mockVgUpi.NewMockUPIClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, nil, ts.idg, mockPiClient, nil, nil, mockVgUPIClient, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
			},
		}, nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction11.ReqId,
		OrderId:          fixturesOrder11Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusInvalidArgument(), res.Status)
	assert.Nil(t, res.Transaction)
}

func TestService_InitiateTransaction_UPI_PI_TO_NONUPI(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgUPIClient := mockVgUpi.NewMockUPIClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, nil, ts.idg, mockPiClient, nil, nil, mockVgUPIClient, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: upiPi,
		}, nil)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi2.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Type: piPb.PaymentInstrumentType_CREDIT_CARD,
			},
		}, nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction11.ReqId,
		OrderId:          fixturesOrder11Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusInvalidArgument(), res.Status)
	assert.Nil(t, res.Transaction)
}

func TestService_InitiateTransaction_VG_RPC_Failure(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockPaymentsHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, mockPaymentsHealthProcessor, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiCall := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi1,
		}, nil)

	mockPiCall2 := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi2.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi2,
		}, nil).After(mockPiCall)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi1,
		}, nil).After(mockPiCall2)

	mockSavingsClient.
		EXPECT().
		GetAccount(context.Background(), &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ExternalId{
				ExternalId: &savingsPb.BankAccountIdentifier{
					AccountNo: "*********",
					IfscCode:  "FED0001",
				},
			},
		}).
		Return(&savingsPb.GetAccountResponse{
			Account: savingsPiFrom,
		}, nil)

	mockVgPaymentClient.EXPECT().
		Pay(context.Background(), vgPayRequestMatcher(vgPaymentReq)).
		Return(nil, errors.New("VG rpc failure"))

	mockOrderOrchPublisher.
		EXPECT().
		Publish(context.Background(), gomock.AssignableToTypeOf(&orderPb.ProcessOrderRequest{})).
		Return("random-msg-id", nil)

	mockPayOrchPublisher.
		EXPECT().
		PublishWithDelay(context.Background(), gomock.AssignableToTypeOf(&paymentPb.ProcessPaymentRequest{}), gomock.AssignableToTypeOf(time.Duration(0))).
		Return("random-msg-id", nil)

	mockPaymentsHealthProcessor.EXPECT().
		UpdateTransactionAndPushMetrics(gomock.Any(), gomock.Any(), nil, gomock.Any()).
		Return(nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction1.ReqId,
		OrderId:          fixturesOrder1Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusOk(), res.Status)
	assert.NotNil(t, res.Transaction)
	assert.True(t, res.GetTransaction().GetStatus() == paymentPb.TransactionStatus_UNKNOWN)

	fetchedTransaction, payReqInfo, err := ts.txnDao.GetByIdWithPaymentReqInfo(context.Background(), res.Transaction.Id)
	assert.Nil(t, err)
	assert.NotNil(t, fetchedTransaction)
	expectedTxn := fixturesTransaction1
	expectedTxn.Status = paymentPb.TransactionStatus_UNKNOWN
	expectedTxn.LocationToken = req.PaymentAuth.Device.LocationToken
	expectedTxn.DetailedStatus = fetchedTransaction.DetailedStatus
	expectedPayReqId := &paymentPb.PaymentRequestInformation{
		DeviceId: "device-1",
		ReqId:    fixturesTransaction1.ReqId,
	}
	// we need to update it because we are not updated in dao
	fetchedTransaction.Status = paymentPb.TransactionStatus_UNKNOWN
	assertTransaction(t, expectedTxn, fetchedTransaction)
	assert.True(t, proto.Equal(expectedPayReqId, payReqInfo))
}

func TestService_InitiateTransaction_VG_RPC_Payment_Failure(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockPaymentsHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, mockPaymentsHealthProcessor, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiCall := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi1,
		}, nil)

	mockPiCall2 := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi2.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi2,
		}, nil).After(mockPiCall)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi1,
		}, nil).After(mockPiCall2)

	mockSavingsClient.
		EXPECT().
		GetAccount(context.Background(), &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ExternalId{
				ExternalId: &savingsPb.BankAccountIdentifier{
					AccountNo: "*********",
					IfscCode:  "FED0001",
				},
			},
		}).
		Return(&savingsPb.GetAccountResponse{
			Account: savingsPiFrom,
		}, nil)

	mockVgPaymentClient.EXPECT().
		Pay(context.Background(), vgPayRequestMatcher(vgPaymentReq)).
		Return(&vgPaymentPb.PayResponse{
			Status: rpc.NewStatus(uint32(vgPaymentPb.PayResponse_FAILED), "failed", ""),
		}, nil)

	mockOrderOrchPublisher.
		EXPECT().
		Publish(context.Background(), gomock.AssignableToTypeOf(&orderPb.ProcessOrderRequest{})).
		Return("random-msg-id", nil)

	mockPaymentsHealthProcessor.EXPECT().
		UpdateTransactionAndPushMetrics(gomock.Any(), gomock.Any(), nil, gomock.Any()).
		Return(nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction1.ReqId,
		OrderId:          fixturesOrder1Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.EqualValues(t, paymentPb.InitiateTransactionResponse_TRANSACTION_FAILED, res.Status.Code)
	assert.EqualValues(t, paymentPb.TransactionStatus_FAILED, res.GetTransaction().GetStatus())

	assert.NotNil(t, res.Transaction)

	fetchedTransaction, payReqInfo, err := ts.txnDao.GetByIdWithPaymentReqInfo(context.Background(), res.Transaction.Id)
	assert.Nil(t, err)
	assert.NotNil(t, fetchedTransaction)
	expectedTxn := fixturesTransaction1
	expectedTxn.Status = paymentPb.TransactionStatus_FAILED
	expectedTxn.DetailedStatus = fetchedTransaction.DetailedStatus
	expectedTxn.LocationToken = req.PaymentAuth.Device.LocationToken
	expectedPayReqId := &paymentPb.PaymentRequestInformation{
		DeviceId: "device-1",
		ReqId:    fixturesTransaction1.ReqId,
	}

	// we need to update it because we are not updated in dao
	fetchedTransaction.Status = paymentPb.TransactionStatus_FAILED

	assertTransaction(t, expectedTxn, fetchedTransaction)
	assert.True(t, proto.Equal(expectedPayReqId, payReqInfo))
}

func TestService_InitiateTransaction_Not_Savings_Account(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
				Identifier: &piPb.PaymentInstrument_Account{
					Account: &piPb.Account{
						ActualAccountNumber: "*********",
						SecureAccountNumber: "1234XXXXX",
						IfscCode:            "FED0001",
						AccountType:         accountPb.Type_CURRENT,
					},
				},
				VerifiedName: "User-1",
			},
		}, nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction1.ReqId,
		OrderId:          fixturesOrder1Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusInvalidArgument(), res.Status)
	assert.Nil(t, res.Transaction)
}

func TestService_InitiateTransaction_FailedPublish(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockPaymentsHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, mockPaymentsHealthProcessor, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiCall1 := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi1,
		}, nil)

	mockPiCall2 := mockPiClient.EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi2.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi2,
		}, nil).After(mockPiCall1)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: accountPi1.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: accountPi1,
		}, nil).Times(2).After(mockPiCall2)

	mockSavingsClient.
		EXPECT().
		GetAccount(context.Background(), &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ExternalId{
				ExternalId: &savingsPb.BankAccountIdentifier{
					AccountNo: "*********",
					IfscCode:  "FED0001",
				},
			},
		}).
		Return(&savingsPb.GetAccountResponse{
			Account: savingsPiFrom,
		}, nil)

	mockVgPaymentClient.EXPECT().
		Pay(context.Background(), vgPayRequestMatcher(vgPaymentReq)).
		Return(&vgPaymentPb.PayResponse{
			Status: rpc.StatusOk(),
		}, nil)

	mockOrderOrchPublisher.
		EXPECT().
		Publish(context.Background(), gomock.AssignableToTypeOf(&orderPb.ProcessOrderRequest{})).
		Return("random-msg-id", nil)

	mockPayOrchPublisher.
		EXPECT().
		PublishWithDelay(context.Background(), gomock.AssignableToTypeOf(&paymentPb.ProcessPaymentRequest{}), gomock.AssignableToTypeOf(time.Duration(0))).
		Return("", errors.New("publish failure"))

	mockPaymentsHealthProcessor.EXPECT().
		UpdateTransactionAndPushMetrics(gomock.Any(), gomock.Any(), nil, gomock.Any()).
		Return(nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction1.ReqId,
		OrderId:          fixturesOrder1Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusOk(), res.Status)
	assert.NotNil(t, res.Transaction)

	fetchedTransaction, payReqInfo, err := ts.txnDao.GetByIdWithPaymentReqInfo(context.Background(), res.Transaction.Id)
	assert.Nil(t, err)
	assert.NotNil(t, fetchedTransaction)
	expectedTxn := fixturesTransaction1
	expectedTxn.Status = paymentPb.TransactionStatus_MANUAL_INTERVENTION
	expectedTxn.DetailedStatus = fetchedTransaction.DetailedStatus
	expectedTxn.LocationToken = req.PaymentAuth.Device.LocationToken
	expectedPayReqId := &paymentPb.PaymentRequestInformation{
		DeviceId: "device-1",
		ReqId:    fixturesTransaction1.ReqId,
	}
	assertTransaction(t, expectedTxn, fetchedTransaction)
	assert.True(t, proto.Equal(expectedPayReqId, payReqInfo))
}

func TestService_InitiateTransaction_Record_Not_found(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: "random-req-id",
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusRecordNotFound(), res.Status)
	assert.Nil(t, res.Transaction)
}

func TestService_InitiateTransaction_Transaction_Already_Initiated(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, nil, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction3ReqId,
		OrderId:          fixturesOrder3Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, paymentPb.InitiateTransactionResponse_INVALID_TRANSACTION_STATE, paymentPb.InitiateTransactionResponse_Status(res.Status.Code))
	assert.Nil(t, res.Transaction)
}

func TestService_InitiateTransaction_Invalid_Order_Id(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	t.Skip(
		// Clean database, run migrations and load fixtures
		"Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")

	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgPaymentClient := vgMocks.NewMockPaymentClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, mockVgPaymentClient, ts.idg, mockPiClient, mockSavingsClient, nil, nil, nil, nil, nil, nil, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction1.ReqId,
		OrderId:          fixturesOrder3Id,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusInvalidArgument(), res.Status)
	assert.Nil(t, res.Transaction)
}

func TestService_InitiateTransaction_UPI_Collect(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgUPIClient := mockVgUpi.NewMockUPIClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, nil, ts.idg, mockPiClient, nil, nil, mockVgUPIClient, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiCall := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: externalAccountPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Id:   "pi-ex-3",
				Type: piPb.PaymentInstrumentType_UPI,
				Identifier: &piPb.PaymentInstrument_Upi{
					Upi: &piPb.Upi{
						Vpa:                    "random@icici",
						AccountReferenceNumber: "random-account-ref",
						MaskedAccountNumber:    "xxxxxxx7890",
						IfscCode:               "FED00001",
						AccountType:            accountsPb.Type_SAVINGS,
					},
				},
				VerifiedName: "random-name",
			},
		}, nil)

	mockPiCall2 := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: upiPi,
		}, nil).After(mockPiCall)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: externalAccountPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Id:   "pi-ex-3",
				Type: piPb.PaymentInstrumentType_UPI,
				Identifier: &piPb.PaymentInstrument_Upi{
					Upi: &piPb.Upi{
						Vpa:                    "random@icici",
						AccountReferenceNumber: "random-account-ref",
						MaskedAccountNumber:    "xxxxxxx7890",
						IfscCode:               "FED00001",
						AccountType:            accountsPb.Type_SAVINGS,
					},
				},
				VerifiedName: "random-name",
			},
		}, nil).After(mockPiCall2)

	mockVgUPIClient.EXPECT().
		ReqPay(context.Background(), gomock.AssignableToTypeOf(&vgUPIPb.ReqPayRequest{})).
		Return(&vgUPIPb.ReqPayResponse{
			Status: rpc.StatusOk(),
		}, nil)

	mockOrderOrchPublisher.
		EXPECT().
		Publish(context.Background(), gomock.AssignableToTypeOf(&orderPb.ProcessOrderRequest{})).
		Return("random-msg", nil)

	mockPayOrchPublisher.
		EXPECT().
		PublishWithDelay(context.Background(), gomock.AssignableToTypeOf(&paymentPb.ProcessPaymentRequest{}),
			gomock.AssignableToTypeOf(time.Duration(0))).
		Return("random-msg-id", nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction12.ReqId,
		OrderId:          fixturesOrder12Id,
		IsCollectRequest: true,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusOk(), res.Status)
	assert.NotNil(t, res.Transaction)

	fetchedTransaction, err := ts.txnDao.GetById(context.Background(), res.Transaction.Id)
	assert.Nil(t, err)
	assert.NotNil(t, fetchedTransaction)
	expectedTxn := fixturesTransaction12
	expectedTxn.Status = paymentPb.TransactionStatus_IN_PROGRESS
	expectedTxn.ProtocolStatus = paymentPb.TransactionProtocolStatus_COLLECT_REQ_PAY_SENT
	expectedTxn.DetailedStatus = fetchedTransaction.DetailedStatus
	expectedTxn.Utr = fetchedTransaction.Utr
	expectedTxn.LocationToken = req.PaymentAuth.Device.LocationToken
	assertTransaction(t, expectedTxn, res.Transaction)
	assert.NotEmpty(t, fetchedTransaction.Utr)
}

func TestService_InitiateTransaction_UPI_Collect_Payment(t *testing.T) {
	t.Skip("Skipping test as it is failing in CI. This will help to atleast run other tests in Github as no tests are running now.")
	// Clean database, run migrations and load fixtures
	pkgTest.PrepareRandomScopedCrdbDatabase(t, ts.conf.EpifiDb(), ts.dbName, ts.db, affectedTestTables, &initialiseSameDbOnce)

	ctr := gomock.NewController(t)
	mockVgUPIClient := mockVgUpi.NewMockUPIClient(ctr)
	mockPiClient := piMocks.NewMockPiClient(ctr)
	mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
	mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
	brokerMock := brokerMocks.NewMockBroker(ctr)
	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
	mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
	mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("123", nil).AnyTimes()
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)

	ts.paymentServer = payment.NewService(ts.txnDao, ts.orderDao, nil, ts.idg, mockPiClient, nil, nil, mockVgUPIClient, nil, nil, nil, mockOrderOrchPublisher, getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, nil, nil, nil, nil, nil, mockTimelineClient, nil)

	defer func() {
		ctr.Finish()
		ts.paymentServer = nil
	}()

	mockPiCall := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: upiPi,
		}, nil)

	mockPiCall2 := mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: externalAccountPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status: rpc.StatusOk(),
			PaymentInstrument: &piPb.PaymentInstrument{
				Id:           externalAccountPi.Id,
				Type:         piPb.PaymentInstrumentType_UPI,
				Identifier:   &piPb.PaymentInstrument_Upi{Upi: &piPb.Upi{Vpa: "example@sbi"}},
				VerifiedName: "user-xyz",
			},
		}, nil).After(mockPiCall)

	mockPiClient.
		EXPECT().
		GetPiById(context.Background(), &piPb.GetPiByIdRequest{Id: upiPi.Id}).
		Return(&piPb.GetPiByIdResponse{
			Status:            rpc.StatusOk(),
			PaymentInstrument: upiPi,
		}, nil).After(mockPiCall2)

	mockVgUPIClient.EXPECT().
		RespAuthDetails(context.Background(), gomock.AssignableToTypeOf(&vgUPIPb.RespAuthDetailsRequest{})).
		Return(&vgUPIPb.RespAuthDetailsResponse{
			Status: rpc.StatusOk(),
		}, nil)

	mockOrderOrchPublisher.
		EXPECT().
		Publish(context.Background(), gomock.AssignableToTypeOf(&orderPb.ProcessOrderRequest{})).
		Return("random-msg", nil)

	mockPayOrchPublisher.
		EXPECT().
		PublishWithDelay(context.Background(), gomock.AssignableToTypeOf(&paymentPb.ProcessPaymentRequest{}),
			gomock.AssignableToTypeOf(time.Duration(0))).
		Return("random-msg-id", nil)

	req := &paymentPb.InitiateTransactionRequest{
		PaymentAuth:      paymentAuth,
		TransactionReqId: fixturesTransaction14.ReqId,
		OrderId:          fixturesOrder14Id,
		IsCollectRequest: true,
	}

	res, err := ts.paymentServer.InitiateTransaction(context.Background(), req)
	assert.Nil(t, err)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusOk(), res.Status)
	assert.NotNil(t, res.Transaction)

	fetchedTransaction, payReqInfo, err := ts.txnDao.GetByIdWithPaymentReqInfo(context.Background(), res.Transaction.Id)
	assert.Nil(t, err)
	assert.NotNil(t, fetchedTransaction)
	expectedTxn := fixturesTransaction14
	expectedTxn.Status = paymentPb.TransactionStatus_IN_PROGRESS
	expectedTxn.ProtocolStatus = paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT
	expectedTxn.DetailedStatus = fetchedTransaction.DetailedStatus
	expectedTxn.LocationToken = req.PaymentAuth.Device.LocationToken
	expectedPayReqId := fixturesTransaction14ReqInfo

	assertTransaction(t, expectedTxn, res.Transaction)
	assert.True(t, proto.Equal(expectedPayReqId, payReqInfo))
}

func getMockEnquiryPublisherMap(mockPublisher queue.DelayPublisher) map[paymentPb.PaymentProtocol]queue.DelayPublisher {
	return map[paymentPb.PaymentProtocol]queue.DelayPublisher{
		paymentPb.PaymentProtocol_INTRA_BANK: mockPublisher,
		paymentPb.PaymentProtocol_UPI:        mockPublisher,
		paymentPb.PaymentProtocol_IMPS:       mockPublisher,
		paymentPb.PaymentProtocol_NEFT:       mockPublisher,
		paymentPb.PaymentProtocol_RTGS:       mockPublisher,
	}
}

func TestService_InitiateTransaction(t *testing.T) {
	testCases := []struct {
		name       string
		req        *paymentPb.InitiateTransactionRequest
		setupMocks func(
			mockCelestialClient *mockCelestial.MockCelestialClient,
			mockPiClient *piMocks.MockPiClient,
			mockOrderOrchPublisher *mocks.MockPublisher,
			mockPayOrchPublisher *mocks.MockDelayPublisher,
			brokerMock *brokerMocks.MockBroker,
			mockUpiProcessor *mocksUpiProcessor.MockUpiProcessor,
			mockInPaymentPublisher *queueMocks.MockPublisher,
			mockTimelineClient *timelineMocks.MockTimelineServiceClient,
			mockPaymentsHealthProcessor *mock_payment.MockIPaymentHealthProcessor,
			mockPayV1Client *payMocks.MockPayClient,
			mockVgUPIClient *mockVgUpi.MockUPIClient,
			mockTxnDao *daoMocks.MockTransactionDao,
			mockOrderDao *daoMocks.MockOrderDao,
			wg *sync.WaitGroup,
		)
		goroutineCount int
		expectedStatus *rpc.Status
	}{
		{
			name:           "SuccessfulAuthAndNotifyDomain",
			expectedStatus: rpc.StatusOk(),
			setupMocks: func(
				mockCelestialClient *mockCelestial.MockCelestialClient,
				mockPiClient *piMocks.MockPiClient,
				mockOrderOrchPublisher *mocks.MockPublisher,
				mockPayOrchPublisher *mocks.MockDelayPublisher,
				brokerMock *brokerMocks.MockBroker,
				mockUpiProcessor *mocksUpiProcessor.MockUpiProcessor,
				mockInPaymentPublisher *queueMocks.MockPublisher,
				mockTimelineClient *timelineMocks.MockTimelineServiceClient,
				mockPaymentsHealthProcessor *mock_payment.MockIPaymentHealthProcessor,
				mockPayV1Client *payMocks.MockPayClient,
				mockVgUPIClient *mockVgUpi.MockUPIClient,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockOrderDao *daoMocks.MockOrderDao,
				wg *sync.WaitGroup,
			) {
				// Expect AddToBatch to be called on the broker mock
				brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()

				// Expect Publish to be called on the order orchestration publisher mock
				mockOrderOrchPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("mock-msg-id", nil).AnyTimes()

				mockPayOrchPublisher.
					EXPECT().
					PublishWithDelay(context.Background(), gomock.AssignableToTypeOf(&paymentPb.ProcessPaymentRequest{}), gomock.AssignableToTypeOf(time.Duration(0))).
					Return("random-msg-id", nil)

				// Set up txnDao expectations
				mockTxnDao.EXPECT().GetByReqIdWithPaymentReqInfo(gomock.Any(), "txn-req-id-123").Return(&paymentPb.Transaction{
					Id:              "txn-123",
					Status:          paymentPb.TransactionStatus_CREATED,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
				}, &paymentPb.PaymentRequestInformation{}, nil).AnyTimes()
				mockTxnDao.EXPECT().GetOrderId(gomock.Any(), "txn-123").Return("order-id-123", nil).AnyTimes()

				// Set up orderDao expectations (if needed)
				mockOrderDao.EXPECT().GetById(gomock.Any(), "order-id-123").Return(&orderPb.Order{Id: "order-id-123"}, nil).AnyTimes()
				// Add expectation for GetByIdWithTransactions to fix the unexpected call error
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), gomock.Any()).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: "order-id-123",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:              "txn-123",
							Status:          paymentPb.TransactionStatus_CREATED,
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}, nil).AnyTimes()
				mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("mock-msg-id", nil).AnyTimes()
				// Expect UpdateAndChangeStatus to be called for order location token update
				mockOrderDao.EXPECT().UpdateAndChangeStatus(
					gomock.Any(), // ctx
					gomock.Any(), // order
					gomock.Any(), // orderUpdateFieldMasks
					gomock.Any(), // fromStatus
					gomock.Any(), // toStatus
				).Return(nil).AnyTimes()

				// Expect Update to be called on the transaction DAO (for MANUAL_INTERVENTION or other status updates)
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

				// Add expectation for AddUpiMetricsForPspAsync to fix the unexpected call error
				mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

				// Prepare workflow identification info in the metadata
				workflowInfo := &payPb.WorkflowIdentificationInfo{
					SignalId:    "test-signal-id",
					ClientReqId: &workflowPb.ClientReqId{Id: "client-req-id", Client: workflowPb.Client_PAY},
				}
				clientMeta := &payPb.ClientIdentificationTxnMetaData{
					DomainOrderData: &payPb.DomainOrderData{
						WorkflowIdentificationInfo: workflowInfo,
					},
				}
				// Simulate encrypted orchestration metadata (just use proto for test)
				orchestrationMetadata, _ := proto.Marshal(clientMeta)

				// Mock decryption and unmarshalling
				mockPayV1Client.EXPECT().GetPlainData(gomock.Any(), gomock.Any()).Return(&payPb.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: orchestrationMetadata}, nil)

				// Mock PiClient and other dependencies as needed for UPI
				mockPiClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&piPb.GetPiByIdResponse{
					Status:            rpc.StatusOk(),
					PaymentInstrument: upiPi,
				}, nil).AnyTimes()
				mockPiClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&piPb.GetPiByIdResponse{
					Status:            rpc.StatusOk(),
					PaymentInstrument: accountPi2,
				}, nil).AnyTimes()

				// Mock UPI client to return IN_PROGRESS
				mockVgUPIClient.EXPECT().ReqPay(gomock.Any(), gomock.Any()).Return(&vgUPIPb.ReqPayResponse{
					Status: rpc.StatusOk(),
				}, nil)

				// Expect UpdateAndChangeStatusAndPushMetrics to be called for status transition CREATED -> IN_PROGRESS (txn)
				mockPaymentsHealthProcessor.EXPECT().UpdateAndChangeStatusAndPushMetrics(
					gomock.Any(), // ctx
					gomock.Any(), // txn
					gomock.Any(), // reqInfo
					gomock.Any(), // updateMask
					paymentPb.TransactionStatus_CREATED,
					paymentPb.TransactionStatus_INITIATED,
				).Return(nil).AnyTimes()

				// Expect UpdateTransactionAndPushMetrics to be called for transaction updates
				mockPaymentsHealthProcessor.EXPECT().UpdateTransactionAndPushMetrics(
					gomock.Any(), // ctx
					gomock.Any(), // txn
					gomock.Any(), // reqInfo
					gomock.Any(), // updateMask
				).Return(nil).AnyTimes()

				// Expect the celestial client to receive the correct FundTransferSignal
				mockCelestialClient.EXPECT().SignalWorkflow(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *celestialPb.SignalWorkflowRequest, opts ...interface{}) (*celestialPb.SignalWorkflowResponse, error) {
						defer func() {
							wg.Done()
						}()
						assert.Equal(t, "test-signal-id", req.SignalId)
						assert.Equal(t, "client-req-id", req.GetClientReqId().GetId())
						// Unmarshal payload and check FundTransferSignal
						fundSignal := &signalPb.FundTransferStatusSignal{
							TransactionStatus: paymentPb.TransactionStatus_IN_PROGRESS,
						}
						err := protojson.Unmarshal(req.Payload, fundSignal)
						require.NoError(t, err)
						return &celestialPb.SignalWorkflowResponse{Status: rpc.StatusOk()}, nil
					},
				)

			},
			goroutineCount: 1,
			req: &paymentPb.InitiateTransactionRequest{
				PaymentAuth:      paymentAuth,
				TransactionReqId: "txn-req-id-123",
				OrderId:          "order-id-123",
				OrchestrationMetadata: func() []byte {
					workflowInfo := &payPb.WorkflowIdentificationInfo{
						SignalId:    "test-signal-id",
						ClientReqId: &workflowPb.ClientReqId{Id: "client-req-id", Client: workflowPb.Client_PAY},
					}
					clientMeta := &payPb.ClientIdentificationTxnMetaData{
						DomainOrderData: &payPb.DomainOrderData{
							WorkflowIdentificationInfo: workflowInfo,
						},
					}
					orchestrationMetadata, _ := proto.Marshal(clientMeta)
					return orchestrationMetadata
				}(),
			},
		},
		{
			name:           "SignalWorkflowReturnsInternal",
			expectedStatus: rpc.StatusOk(),
			setupMocks: func(
				mockCelestialClient *mockCelestial.MockCelestialClient,
				mockPiClient *piMocks.MockPiClient,
				mockOrderOrchPublisher *mocks.MockPublisher,
				mockPayOrchPublisher *mocks.MockDelayPublisher,
				brokerMock *brokerMocks.MockBroker,
				mockUpiProcessor *mocksUpiProcessor.MockUpiProcessor,
				mockInPaymentPublisher *queueMocks.MockPublisher,
				mockTimelineClient *timelineMocks.MockTimelineServiceClient,
				mockPaymentsHealthProcessor *mock_payment.MockIPaymentHealthProcessor,
				mockPayV1Client *payMocks.MockPayClient,
				mockVgUPIClient *mockVgUpi.MockUPIClient,
				mockTxnDao *daoMocks.MockTransactionDao,
				mockOrderDao *daoMocks.MockOrderDao,
				wg *sync.WaitGroup,
			) {
				// Same setup as the successful case
				brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
				mockOrderOrchPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("mock-msg-id", nil).AnyTimes()
				mockPayOrchPublisher.
					EXPECT().
					PublishWithDelay(context.Background(), gomock.AssignableToTypeOf(&paymentPb.ProcessPaymentRequest{}), gomock.AssignableToTypeOf(time.Duration(0))).
					Return("random-msg-id", nil)
				mockTxnDao.EXPECT().GetByReqIdWithPaymentReqInfo(gomock.Any(), "txn-req-id-123").Return(&paymentPb.Transaction{
					Id:              "txn-123",
					Status:          paymentPb.TransactionStatus_CREATED,
					PaymentProtocol: paymentPb.PaymentProtocol_UPI,
					PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
				}, &paymentPb.PaymentRequestInformation{}, nil).AnyTimes()
				mockTxnDao.EXPECT().GetOrderId(gomock.Any(), "txn-123").Return("order-id-123", nil).AnyTimes()
				mockOrderDao.EXPECT().GetById(gomock.Any(), "order-id-123").Return(&orderPb.Order{Id: "order-id-123"}, nil).AnyTimes()
				mockOrderDao.EXPECT().GetByIdWithTransactions(gomock.Any(), gomock.Any()).Return(&orderPb.OrderWithTransactions{
					Order: &orderPb.Order{
						Id: "order-id-123",
					},
					Transactions: []*paymentPb.Transaction{
						{
							Id:              "txn-123",
							Status:          paymentPb.TransactionStatus_CREATED,
							PaymentProtocol: paymentPb.PaymentProtocol_UPI,
							PartnerBank:     commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}, nil).AnyTimes()
				mockInPaymentPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("mock-msg-id", nil).AnyTimes()
				mockOrderDao.EXPECT().UpdateAndChangeStatus(
					gomock.Any(), // ctx
					gomock.Any(), // order
					gomock.Any(), // orderUpdateFieldMasks
					gomock.Any(), // fromStatus
					gomock.Any(), // toStatus
				).Return(nil).AnyTimes()
				mockTxnDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mockUpiProcessor.EXPECT().AddUpiMetricsForPspAsync(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
				// Prepare workflow identification info in the metadata
				workflowInfo := &payPb.WorkflowIdentificationInfo{
					SignalId:    "test-signal-id",
					ClientReqId: &workflowPb.ClientReqId{Id: "client-req-id", Client: workflowPb.Client_PAY},
				}
				clientMeta := &payPb.ClientIdentificationTxnMetaData{
					DomainOrderData: &payPb.DomainOrderData{
						WorkflowIdentificationInfo: workflowInfo,
					},
				}
				orchestrationMetadata, _ := proto.Marshal(clientMeta)
				mockPayV1Client.EXPECT().GetPlainData(gomock.Any(), gomock.Any()).Return(&payPb.GetPlainDataResponse{Status: rpc.StatusOk(), PlainData: orchestrationMetadata}, nil)
				mockPiClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&piPb.GetPiByIdResponse{
					Status:            rpc.StatusOk(),
					PaymentInstrument: upiPi,
				}, nil).AnyTimes()
				mockPiClient.EXPECT().GetPiById(gomock.Any(), gomock.Any()).Return(&piPb.GetPiByIdResponse{
					Status:            rpc.StatusOk(),
					PaymentInstrument: accountPi2,
				}, nil).AnyTimes()
				mockVgUPIClient.EXPECT().ReqPay(gomock.Any(), gomock.Any()).Return(&vgUPIPb.ReqPayResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockPaymentsHealthProcessor.EXPECT().UpdateAndChangeStatusAndPushMetrics(
					gomock.Any(), // ctx
					gomock.Any(), // txn
					gomock.Any(), // reqInfo
					gomock.Any(), // updateMask
					paymentPb.TransactionStatus_CREATED,
					paymentPb.TransactionStatus_INITIATED,
				).Return(nil).AnyTimes()
				mockPaymentsHealthProcessor.EXPECT().UpdateTransactionAndPushMetrics(
					gomock.Any(), // ctx
					gomock.Any(), // txn
					gomock.Any(), // reqInfo
					gomock.Any(), // updateMask
				).Return(nil).AnyTimes()
				mockCelestialClient.EXPECT().SignalWorkflow(gomock.Any(), gomock.Any()).DoAndReturn(
					func(ctx context.Context, req *celestialPb.SignalWorkflowRequest, opts ...interface{}) (*celestialPb.SignalWorkflowResponse, error) {
						defer func() {
							wg.Done()
						}()
						assert.Equal(t, "test-signal-id", req.SignalId)
						assert.Equal(t, "client-req-id", req.GetClientReqId().GetId())
						fundSignal := &signalPb.FundTransferStatusSignal{
							TransactionStatus: paymentPb.TransactionStatus_IN_PROGRESS,
						}
						err := protojson.Unmarshal(req.Payload, fundSignal)
						require.NoError(t, err)
						return &celestialPb.SignalWorkflowResponse{Status: rpc.StatusInternal()}, nil
					},
				)
			},
			req: &paymentPb.InitiateTransactionRequest{
				PaymentAuth:      paymentAuth,
				TransactionReqId: "txn-req-id-123",
				OrderId:          "order-id-123",
				OrchestrationMetadata: func() []byte {
					workflowInfo := &payPb.WorkflowIdentificationInfo{
						SignalId:    "test-signal-id",
						ClientReqId: &workflowPb.ClientReqId{Id: "client-req-id", Client: workflowPb.Client_PAY},
					}
					clientMeta := &payPb.ClientIdentificationTxnMetaData{
						DomainOrderData: &payPb.DomainOrderData{
							WorkflowIdentificationInfo: workflowInfo,
						},
					}
					orchestrationMetadata, _ := proto.Marshal(clientMeta)
					return orchestrationMetadata
				}(),
			},
			goroutineCount: 1,
		},
		// Add more test cases here as needed
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			wg := &sync.WaitGroup{}
			if tc.goroutineCount > 0 {
				wg.Add(tc.goroutineCount)
			}

			mockCelestialClient := mockCelestial.NewMockCelestialClient(ctr)
			mockPiClient := piMocks.NewMockPiClient(ctr)
			mockOrderOrchPublisher := mocks.NewMockPublisher(ctr)
			mockPayOrchPublisher := mocks.NewMockDelayPublisher(ctr)
			brokerMock := brokerMocks.NewMockBroker(ctr)
			mockUpiProcessor := mocksUpiProcessor.NewMockUpiProcessor(ctr)
			mockInPaymentPublisher := queueMocks.NewMockPublisher(ctr)
			mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
			mockPaymentsHealthProcessor := mock_payment.NewMockIPaymentHealthProcessor(ctr)
			mockPayV1Client := payMocks.NewMockPayClient(ctr)
			mockVgUPIClient := mockVgUpi.NewMockUPIClient(ctr)
			mockTxnDao := daoMocks.NewMockTransactionDao(ctr)
			mockOrderDao := daoMocks.NewMockOrderDao(ctr)

			// Call the test case's setupMocks
			if tc.setupMocks != nil {
				tc.setupMocks(
					mockCelestialClient,
					mockPiClient,
					mockOrderOrchPublisher,
					mockPayOrchPublisher,
					brokerMock,
					mockUpiProcessor,
					mockInPaymentPublisher,
					mockTimelineClient,
					mockPaymentsHealthProcessor,
					mockPayV1Client,
					mockVgUPIClient,
					mockTxnDao,
					mockOrderDao,
					wg,
				)
			}

			ts.paymentServer = payment.NewService(
				mockTxnDao,
				mockOrderDao,
				nil, ts.idg, mockPiClient, nil, nil, mockVgUPIClient, nil, nil, nil, mockOrderOrchPublisher,
				getMockEnquiryPublisherMap(mockPayOrchPublisher), mockPayOrchPublisher, brokerMock, ts.conf, nil, nil, nil, mockUpiProcessor, nil, mockInPaymentPublisher, nil, nil, nil, mockPaymentsHealthProcessor, nil, nil, mockCelestialClient, nil, mockTimelineClient, mockPayV1Client,
			)
			service := ts.paymentServer

			// Call InitiateTransaction with the test case's request
			resp, err := service.InitiateTransaction(context.Background(), tc.req)
			require.NoError(t, err)
			assert.Equal(t, tc.expectedStatus.Code, resp.Status.Code)
			waitgroup.SafeWait(wg, 2*time.Second)
		})
	}
}
