// following file contains logic to initiate a payment with the partner bank.
// supports only IMPS payment for now.
package payment

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/golang/protobuf/ptypes"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	gormv2 "gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	upiNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/upi"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/accounts"
	beOrderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	signalPb "github.com/epifi/gamma/api/pay/signal"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	upiPb "github.com/epifi/gamma/api/upi"
	upiPayloadPb "github.com/epifi/gamma/api/upi/payload"
	userpb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/header"
	vgPaymentPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment"
	vgUPIPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/api/vendors"
	"github.com/epifi/gamma/order/events"
	orderEvent "github.com/epifi/gamma/order/events"
	"github.com/epifi/gamma/order/metrics"
	payPkgTxns "github.com/epifi/gamma/pay/pkg/transactions"
	payPkg "github.com/epifi/gamma/pkg/pay"
	upiPkg "github.com/epifi/gamma/pkg/upi"
	upi2 "github.com/epifi/gamma/upi"
)

const (
	DefaultUPIRemarks = "UPI"
	upiGlobalQR       = "upiGlobal://pay"
)

var (
	// errInvalidPI is returned in case PI related details can't be fetched.
	errInvalidPI = fmt.Errorf("invalid upi %w", epifierrors.ErrPermanent)

	statusFailedTransaction rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(
			uint32(paymentPb.InitiateTransactionResponse_TRANSACTION_FAILED),
			"transaction failed",
		)
	}

	statusInvalidTransactionState rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(
			uint32(paymentPb.InitiateTransactionResponse_INVALID_TRANSACTION_STATE),
			"transaction is already initiated",
		)
	}
)

// InitiateTransaction starts payment with the bank and publishes packet in the data pipeline.
// Following validations are performed before initiating payment with the vendor
// 1. Check if transaction-req-id maps to any transaction in the DB
// 2. Check if transaction mapped is in CREATED state.
// 3. Check if the correct order id is mapped against the transaction.
// nolint: funlen
func (s *Service) InitiateTransaction(ctx context.Context, req *paymentPb.InitiateTransactionRequest) (*paymentPb.InitiateTransactionResponse, error) {
	var (
		res     = &paymentPb.InitiateTransactionResponse{}
		txn     *paymentPb.Transaction
		reqInfo *paymentPb.PaymentRequestInformation
		err     error
	)

	defer func() {
		if txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
			upiTransactionEvent, er := getNewUpiTransactionEvent(req, txn, reqInfo)
			if er == nil {
				s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), upiTransactionEvent)
			}
		}
	}()

	txn, reqInfo, err = s.txnDao.GetByReqIdWithPaymentReqInfo(ctx, req.TransactionReqId)
	if err != nil {
		if errors.Is(err, gormv2.ErrRecordNotFound) {
			logger.Error(ctx, "transaction record not found",
				zap.String("txn-req-id", req.TransactionReqId),
				zap.Error(err))
			res.Status = rpc.StatusRecordNotFound()

		} else {
			logger.Error(ctx, "failed to fetch transaction details from DB",
				zap.String("txn-req-id", req.TransactionReqId),
				zap.Error(err))
			res.Status = rpc.StatusInternal()
		}
		return res, nil
	}

	if txn.Status != paymentPb.TransactionStatus_CREATED {
		logger.Info(ctx, "can't initiate transaction, invalid transaction state",
			zap.String("txn-req-id", req.TransactionReqId),
			zap.String("status", txn.Status.String()))
		res.Status = statusInvalidTransactionState()
		return res, nil
	}

	// validate of order belongs to the transaction
	// FIXME: only valid for 1 txn - 1 order mapping
	fetchedOrderId, err := s.txnDao.GetOrderId(ctx, txn.Id)
	if err != nil {
		logger.Error(ctx, "not able to fetch order-id",
			zap.String("txn-id", txn.Id),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if fetchedOrderId != req.OrderId {
		logger.Info(ctx, "req order id didn't match with fetched order id",
			zap.String("req-order-id", req.OrderId),
			zap.String("fetched-order-id", fetchedOrderId))
		res.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	err = s.setLocationTokenForActorInOrder(
		ctx,
		fetchedOrderId,
		req.GetActorId(),
		req.GetPaymentAuth().GetDevice().GetLocationToken(),
	)
	if err != nil {
		logger.Error(ctx, "error setting location token  actor in order", zap.String(logger.ORDER_ID, fetchedOrderId),
			zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Error(err))
	}

	var remitterPhoneNumber *commontypes.PhoneNumber
	switch txn.PaymentProtocol {
	case paymentPb.PaymentProtocol_IMPS, paymentPb.PaymentProtocol_INTRA_BANK, paymentPb.PaymentProtocol_NEFT, paymentPb.PaymentProtocol_RTGS:
		remitterPhoneNumber, err = s.makeAccountPayment(ctx, txn, req.PaymentAuth)
		if err != nil {
			logger.Error(ctx, "error while initiating bank payment",
				zap.String("txn-req-id", req.TransactionReqId),
				zap.Error(err))

			switch {
			case errors.Is(err, errInvalidPI):
				res.Status = rpc.StatusInvalidArgument()
			default:
				res.Status = rpc.StatusInternal()
			}
			return res, nil
		}

	case paymentPb.PaymentProtocol_UPI:
		// TODO(nitesh): To fetch and set the remitter's phone number
		err = s.makeUPIPayment(ctx, txn, reqInfo, req.IsCollectRequest, req.PaymentAuth, req.GetActorId(), req.GetUrn(), req.GetBaseAmountQuoteCurrency())
		if err != nil {
			logger.Error(ctx, "error while initiating UPI payment",
				zap.String("txn-req-id", req.TransactionReqId),
				zap.Error(err))

			switch {
			case errors.Is(err, errInvalidPI):
				res.Status = rpc.StatusInvalidArgument()
			default:
				res.Status = rpc.StatusInternal()
			}
			return res, nil
		}

	default:
		logger.Error(ctx, "payment protocol not supported",
			zap.String("txn-req-id", req.TransactionReqId),
			zap.String("protocol", txn.PaymentProtocol.String()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// publish packet to the order queue to notify that transaction is initiated.
	// the order consumer is responsible for updating based on txn status.
	err = s.publishToOrderQueue(ctx, txn)
	if err != nil {
		logger.Error(ctx, "can't publish event to the order queue",
			zap.String("tnx-id", txn.Id),
			zap.Error(err))
	}

	// Sending notification to domain service inside a goroutine to avoid blocking user
	goroutine.RunWithDefaultTimeout(ctx, func(gctx context.Context) {
		// notifying domain service that txn has been initiated
		if notifErr := s.notifyDomainService(gctx, req.GetOrchestrationMetadata(), txn); notifErr != nil {
			logger.Error(gctx, "failed to publish signal to domain", zap.Error(notifErr), zap.String(logger.TXN_ID, txn.GetId()))
		}
	})

	res.Transaction = txn
	if txn.Status == paymentPb.TransactionStatus_FAILED {
		metrics.IncrementOnAppTxnFailureCount(txn.GetLatestTxnDetailedStatus().GetErrorCategory(), txn.GetPaymentProtocol())
		res.Status = statusFailedTransaction()
		return res, nil
	}

	// Since we won't be depending entirely on vendor callbacks blindly. The idea here is to schedule the SQS event
	// using delaySeconds param. Now, if the vendor notifications don't come after a certain threshold.
	// We should start polling their servers for the status.

	err = s.publishToTxnStatusOrchestrationQueue(ctx, txn, remitterPhoneNumber, req.GetActorId(), req.GetPaymentAuth().GetCustomerId())
	if err != nil {
		logger.Error(ctx, "can't publish packet for transaction",
			zap.String("transaction-id", txn.Id),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	// publish an event for in payment orders, so that we can listen to these orders and update the timeline
	// for actors involved in order, updated timeline can be used to show ongoing orders on the UI
	err = s.publishToInPaymentOrderQueue(ctx, txn)
	if err != nil {
		logger.Error(ctx, "can't publish event to the in payment order update queue",
			zap.String("order-id", txn.GetOrderId()),
			zap.Error(err))
	}

	res.Status = rpc.StatusOk()
	return res, nil
}

// makeAccountPayment initiates payment for non-upi payment protocols including NEFT, RTGS, IMPS, INTRA_BANK,
// and update transaction status.
// Following are the step by step method followed to initiate a payment
//  1. Fetch account details for the PI IDs
//  2. update transaction status to INITIATED state
//  3. make the vendor gateway payment call
//  4. update the transaction status based on the rpc response
//     a) move to UNKNOWN status in case of suspected case at bank's end & rpc failure
//     Suspected case is similar to rpc failure at bank's end where bank does not know the status of the transaction.
//     Treatment of the suspected case and rpc failure has to be similar at our end.
//     b) move to IN_PROGRESS in case of success acknowledgement from the bank
//     c) move to FAILED in case of terminal failure response from the bank
//     d) move to SUCCESS in case of terminal success status from the bank
//
// nolint: funlen
func (s *Service) makeAccountPayment(ctx context.Context, txn *paymentPb.Transaction, payAuth *paymentPb.InitiateTransactionRequest_AuthHeader) (*commontypes.PhoneNumber, error) {
	// TODO(nitesh): add payment control related validation.
	var (
		// transaction status to be updated in the database
		statusToBeUpdated = paymentPb.TransactionStatus_INITIATED
		// detailed status to be updated in the database
		updateTxnMask     = []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS}
		txnDetailedStatus *paymentPb.TransactionDetailedStatus_DetailedStatus
		detailedState     paymentPb.TransactionDetailedStatus_DetailedStatus_State
		errorReason       = ""
		errorCategory     paymentPb.TransactionDetailedStatus_DetailedStatus_ErrorCategory
	)

	remitterAccount, err := s.getRemitterDetailFromPi(ctx, txn.PiFrom)
	if err != nil {
		return nil, fmt.Errorf("account details couldn't be fetched for PI: %s : %w", txn.PiFrom, err)
	}

	beneficiaryAccount, err := s.getAccountDetailsFromPi(ctx, txn.PiTo)
	if err != nil {
		return nil, fmt.Errorf("account details couldn't be fetched for PI: %s : %w", txn.PiFrom, err)
	}

	// Entry in db is created first with transaction in INITIATED state to mark at least one call to vendor has been
	// made
	txn.Status = paymentPb.TransactionStatus_INITIATED
	txn.LocationToken = payAuth.GetDevice().GetLocationToken()
	reqInfo := &paymentPb.PaymentRequestInformation{DeviceId: payAuth.Device.Id}
	updateFieldMask := []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
		paymentPb.TransactionFieldMask_PAYMENT_REQ_INFO, paymentPb.TransactionFieldMask_LOCATION_TOKEN}
	if err = s.paymentHealthProcessor.UpdateTransactionAndPushMetrics(ctx, txn, reqInfo, updateFieldMask); err != nil {
		return nil, fmt.Errorf("failed to update transaction to %s: %w", txn.Status.String(), err)
	}

	publishErr := payPkg.PublishTxnDetailedStatusUpdateEventIfRequired(ctx, txn, s.txnDetailedStatusUpdateSnsPublisher, updateFieldMask, false)
	if publishErr != nil {
		logger.Error(ctx, "failed to publish event to txn detailed status update topic", zap.Error(publishErr))
	}

	bankName, _ := payPkgTxns.GetBankNameForPiId(ctx, txn.GetPiFrom(), s.piClient)
	metrics.RecordOnAppTxnStateChangeMetrics(txn, bankName)

	vgPayReq := &vgPaymentPb.PayRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: txn.PartnerBank,
		},
		Auth: &header.Auth{
			DeviceId:      payAuth.GetDevice().GetId(),
			DeviceToken:   payAuth.GetDeviceToken(),
			EncryptedPin:  payAuth.GetPartnerSdkCredBlock(),
			UserProfileId: payAuth.GetUserProfileId(),
			CustomerId:    payAuth.GetCustomerId(),
		},
		RequestId:   txn.ReqId,
		TransTime:   ptypes.TimestampNow(),
		Protocol:    txn.PaymentProtocol,
		Remitter:    remitterAccount,
		Beneficiary: beneficiaryAccount,
		Amount:      txn.Amount,
		UserRemarks: txn.Remarks,
	}

	logger.Info(ctx, "Initiating request to vendor gateway")
	vgPayRes, err := s.ovgPaymentClient.Pay(ctx, vgPayReq)

	switch {
	// move to transaction to UNKNOWN status code post rpc call failure.
	case err != nil:
		logger.Error(ctx, "vendor gateway payment rpc failed moving to unknown status", zap.String(logger.TXN_ID, txn.Id),
			zap.Error(err))
		statusToBeUpdated = paymentPb.TransactionStatus_UNKNOWN
		txnDetailedStatus = payPkg.GetTxnDetailedStatusForSystemErr(err, paymentPb.TransactionDetailedStatus_DetailedStatus_FUND_TRANSFER_PAY)
		txn.AddDetailedStatus(txnDetailedStatus)
		updateTxnMask = append(updateTxnMask, paymentPb.TransactionFieldMask_DETAILED_STATUS)
		errorReason = err.Error()
	// in case of beneficiary account type is of FD/RD/SD then we immediately receive transaction response
	case vgPayRes.GetStatus().GetCode() == uint32(vgPaymentPb.PayResponse_PAYMENT_SUCCESS):
		statusToBeUpdated = paymentPb.TransactionStatus_SUCCESS
		updateTxnMask = append(updateTxnMask, paymentPb.TransactionFieldMask_STATUS)
	// mark the transaction as failed in case of terminating status code from the payment rpc.
	case vgPayRes.GetStatus().GetCode() == uint32(vgPaymentPb.PayResponse_BUSINESS_FAILURE):
		statusToBeUpdated = paymentPb.TransactionStatus_FAILED
		detailedState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
		errorCategory = paymentPb.TransactionDetailedStatus_DetailedStatus_USER
		errorReason = vgPayRes.Status.ShortMessage
	case vgPayRes.GetStatus().GetCode() == uint32(vgPaymentPb.PayResponse_FAILED) ||
		vgPayRes.GetStatus().GetCode() == uint32(vgPaymentPb.PayResponse_TECHNICAL_FAILURE) ||
		vgPayRes.GetStatus().GetCode() == uint32(vgPaymentPb.PayResponse_INVALID_CRED_BLOCK) ||
		vgPayRes.GetStatus().GetCode() == uint32(vgPaymentPb.PayResponse_DEVICE_KEY_NOT_REGISTER):
		statusToBeUpdated = paymentPb.TransactionStatus_FAILED
		detailedState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
		errorCategory = paymentPb.TransactionDetailedStatus_DetailedStatus_SERVER
		errorReason = vgPayRes.Status.ShortMessage
	case vgPayRes.GetStatus().IsSuccess():
		statusToBeUpdated = paymentPb.TransactionStatus_IN_PROGRESS
		detailedState = paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS
	default:
		statusToBeUpdated = paymentPb.TransactionStatus_UNKNOWN
		detailedState = paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN
	}

	if err == nil {
		txnDetailedStatus = payPkg.GetTxnDetailedStatusFundTransferForAck(
			detailedState,
			vgPayRes.GetRawResponseCode(),
			vgPayRes.GetRawResponseDescription(),
			vgPayRes.GetStatusCode(),
			vgPayRes.GetStatusDescriptionPayer(),
			vgPayRes.GetStatusDescriptionPayee(),
			paymentPb.TransactionDetailedStatus_DetailedStatus_FUND_TRANSFER_PAY,
			errorCategory,
		)
		txn.AddDetailedStatus(txnDetailedStatus)
		updateTxnMask = append(updateTxnMask, paymentPb.TransactionFieldMask_DETAILED_STATUS)
	}
	// TODO (ismail) add work_flow and entry_point
	if errorReason != "" {
		//nocustomlint:goroutine
		go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			events.NewServerPayInitiated(epificontext.ActorIdFromContext(ctx), events.Payer, "", "", "", payAuth.GetDevice().GetId(),
				time.Now(), txn.GetId(), txn.GetReqId(), txn.GetPaymentProtocol(), events.Failure, errorReason, "", "", s.conf.Secrets().Ids[cfg.EventSalt], "", "", txn.GetAmount(), s.conf.OrderEventAmountCategories()))
	} else {
		//nocustomlint:goroutine
		go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			events.NewServerPayInitiated(epificontext.ActorIdFromContext(ctx), events.Payer, "", "", "", payAuth.GetDevice().GetId(),
				time.Now(), txn.GetId(), txn.GetReqId(), txn.GetPaymentProtocol(), events.Success, "", "", "", s.conf.Secrets().Ids[cfg.EventSalt], "", "", txn.GetAmount(), s.conf.OrderEventAmountCategories()))
	}
	txn.Status = statusToBeUpdated

	err = s.paymentHealthProcessor.UpdateTransactionAndPushMetrics(ctx, txn, nil, updateTxnMask)
	if err != nil {
		logger.Error(ctx, "transaction status update failed",
			zap.String("id", txn.Id),
			zap.String("status", statusToBeUpdated.String()),
			zap.Error(err))
	} else {
		logger.Debug(ctx, "transaction initialization successful!",
			zap.String("id", txn.Id),
			zap.String("status", txn.Status.String()))

		metrics.RecordOnAppTxnStateChangeMetrics(txn, bankName)

		publishErr = payPkg.PublishTxnDetailedStatusUpdateEventIfRequired(ctx, txn, s.txnDetailedStatusUpdateSnsPublisher, updateTxnMask, false)
		if publishErr != nil {
			logger.Error(ctx, "failed to publish event to txn detailed status update topic", zap.Error(publishErr))
		}
	}

	return remitterAccount.GetPhoneNumber(), nil
}

// makeUPIPayment processes routes request to respective payment method and returns error accordingly
func (s *Service) makeUPIPayment(ctx context.Context, txn *paymentPb.Transaction, reqInfo *paymentPb.PaymentRequestInformation,
	isCollectReq bool, payAuth *paymentPb.InitiateTransactionRequest_AuthHeader, currentActorId, urn string, baseAmountQuoteCurrency *moneyPb.Money) error {
	switch {
	// if request is of type collect and txn is in `REQ_AUTH_RECEIVED` state that means
	// collect request has been received from external PSP and thus payment needs to be processed.
	// we send back the response to NPCI with `RespAuthDetails` api.
	case isCollectReq && txn.ProtocolStatus == paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED:
		logger.Debug(ctx, "Processing payment for UPI collect", zap.String("txn-id", txn.Id))
		return s.initiateRespAuth(ctx, txn, reqInfo, payAuth)

	// if request is of type collect and txn is in CREATED state then it means collect
	// request was initiated by actor in our system. Hence, we need to queue the message for retries
	// It also short circuits the collect request if both payer and payee belongs to epiFi
	// Note- cred block is not mandatory for this case.
	case isCollectReq:
		logger.Debug(ctx, "Sending UPI collect request", zap.String("txn-id", txn.Id))
		_, err := s.initiateUPICollect(ctx, txn, reqInfo, payAuth.GetDevice(), payAuth.GetNpciCredBlock(), currentActorId)
		return err
	// the default two cases that left are-
	// 1) request is not of type collect. In this case user is trying to make a simple UPI payment
	// 2) request is of type collect but it was short-circuited hence we send a `ReqPay` in this case.
	default:
		logger.Debug(ctx, "Processing UPI pay request", zap.String("txn-id", txn.Id))
		return s.initiateUPIPayment(ctx, txn, reqInfo, payAuth, urn, currentActorId, baseAmountQuoteCurrency)
	}
}

// initiateUPIPayment initiates UPI payment and update transaction status.
// Following are the step by step method followed to initiate a payment
//  1. Fetch account details for the payer and payee PI IDs
//  2. update transaction status to INITIATED state
//  3. make the vendor gateway payment call
//  4. update the transaction status based on the rpc response
//     a) move to UNKNOWN status in case of suspected case at bank's end & rpc failure
//     Suspected case is similar to rpc failure at bank's end where bank does not know the status of the transaction.
//     Treatment of the suspected case and rpc failure has to be similar at our end.
//     b) move to IN_PROGRESS in case of success acknowledgement from the bank
//     c) move to FAILED in case of terminal failure response from the bank
//     d) move to SUCCESS in case of terminal success status from the bank
//
// nolint: funlen
func (s *Service) initiateUPIPayment(ctx context.Context, txn *paymentPb.Transaction, reqInfo *paymentPb.PaymentRequestInformation,
	payAuth *paymentPb.InitiateTransactionRequest_AuthHeader, urn, actorId string, baseAmountQuoteCurrency *moneyPb.Money) error {
	var (
		// transaction status to be updated in the database
		statusToBeUpdated = paymentPb.TransactionStatus_INITIATED
		// protocol status to be updated in the database
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_PAY_INITIATED
		// detailed status to be updated in the database
		txnDetailedStatus *paymentPb.TransactionDetailedStatus_DetailedStatus

		detailedState paymentPb.TransactionDetailedStatus_DetailedStatus_State
		errorReason   = ""
	)

	payerPi, err := s.validateAndGetPI(ctx, txn.PiFrom, upiPiCheckFunc)
	if err != nil {
		return fmt.Errorf("unable to fetch payer PI info: %w", err)
	}

	payeePi, err := s.validateAndGetPI(ctx, txn.PiTo, upiAndBankPiCheckFunc)
	if err != nil {
		return fmt.Errorf("unable to fetch payee PI info: %w", err)
	}

	// Entry in db is created first with transaction in INITIATED state to mark at least one call to vendor has been
	// made
	txn.ProtocolStatus = paymentPb.TransactionProtocolStatus_PAY_INITIATED
	txn.LocationToken = payAuth.GetDevice().GetLocationToken()

	err = s.genUtrAndUpdateUpiTransactionWithRetry(
		ctx,
		txn,
		reqInfo,
		[]paymentPb.TransactionFieldMask{
			paymentPb.TransactionFieldMask_PROTOCOL_STATUS,
			paymentPb.TransactionFieldMask_LOCATION_TOKEN},
		txn.GetStatus(),
		paymentPb.TransactionStatus_INITIATED)

	if err != nil {
		return fmt.Errorf("failed to update transaction to %s :%w", txn.GetStatus().String(), err)
	}

	bankName, _ := payPkgTxns.GetBankNameForPiId(ctx, txn.GetPiFrom(), s.piClient)
	metrics.RecordOnAppTxnStateChangeMetrics(txn, bankName)

	if txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
		s.upiProcessor.AddUpiMetricsForPspAsync(ctx, txn.GetStatus(), txn.GetPiFrom(), txn.GetPiTo())
	}
	vgPayReq, err := s.createVgReqPayRequest(ctx, txn, reqInfo, payerPi, payeePi, vgUPIPb.ReqPayRequest_PAY, payAuth.GetDevice(),
		payAuth.GetNpciCredBlock(), urn, actorId, baseAmountQuoteCurrency, payAuth.GetNpciCredBlocks())
	if err != nil {
		return fmt.Errorf("error while creating req pay vg request: err = %w", err)
	}

	logger.Info(ctx, "Initiating UPI payment to vendor gateway")
	vgPayRes, err := s.vgUPIClient.ReqPay(ctx, vgPayReq)

	// nolint: dupl
	switch {
	// move to transaction to UNKNOWN status code post rpc call failure.
	case err != nil:
		logger.Error(ctx, "vendor gateway payment rpc failed moving to unknown status",
			zap.String("id", txn.Id),
			zap.Error(err))
		statusToBeUpdated = paymentPb.TransactionStatus_UNKNOWN
		txnDetailedStatus = payPkg.GetTxnDetailedStatusForSystemErr(err, paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_PAY)
		txn.AddDetailedStatus(txnDetailedStatus)
		errorReason = err.Error()
	// mark the transaction as failed in case of terminating status code from the payment rpc.
	case vgPayRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqPayResponse_FAILED):
		statusToBeUpdated = paymentPb.TransactionStatus_FAILED
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT
		detailedState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
		errorReason = vgPayRes.Status.ShortMessage
	// if non of the above failure cases match, then it means vendorgateway returned a positive acknowledgment. Thus, move to
	// IN_PROGRESS state.
	case vgPayRes.GetStatus().IsSuccess():
		statusToBeUpdated = paymentPb.TransactionStatus_IN_PROGRESS
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT
		detailedState = paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS
	default:
		statusToBeUpdated = paymentPb.TransactionStatus_UNKNOWN
		detailedState = paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT
	}
	if err == nil {
		txnDetailedStatus = upi2.GetTxnDetailedStatusUpiForAck(detailedState, vgPayRes.GetRawStatusCode(), vgPayRes.GetRawStatusDescription(),
			vgPayRes.GetStatusCode(), vgPayRes.GetStatusDescriptionPayer(), vgPayRes.GetStatusDescriptionPayee(), paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_PAY)
		txn.AddDetailedStatus(txnDetailedStatus)
	}
	if errorReason != "" {
		//nocustomlint:goroutine
		go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			events.NewServerPayInitiated(epificontext.ActorIdFromContext(ctx), events.Payer, "", "", "", payAuth.GetDevice().GetId(),
				time.Now(), txn.GetId(), txn.GetReqId(), txn.GetPaymentProtocol(), events.Failure, errorReason, "", "", s.conf.Secrets().Ids[cfg.EventSalt], "", "", txn.GetAmount(), s.conf.OrderEventAmountCategories()))
	} else {
		//nocustomlint:goroutine
		go s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx),
			events.NewServerPayInitiated(epificontext.ActorIdFromContext(ctx), events.Payer, "", "", "", payAuth.GetDevice().GetId(),
				time.Now(), txn.GetId(), txn.GetReqId(), txn.GetPaymentProtocol(), events.Success, "", "", "", s.conf.Secrets().Ids[cfg.EventSalt], "", "", txn.GetAmount(), s.conf.OrderEventAmountCategories()))
	}
	txn.Status = statusToBeUpdated
	txn.ProtocolStatus = protocolStatusToBeUpdated

	err = s.paymentHealthProcessor.UpdateTransactionAndPushMetrics(ctx, txn, nil, []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
		paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS})
	if err != nil {
		logger.Error(ctx, "transaction status update failed",
			zap.String("id", txn.Id),
			zap.String("status", statusToBeUpdated.String()),
			zap.Error(err))
	} else {
		logger.Debug(ctx, "transaction initialization successful!",
			zap.String("id", txn.Id),
			zap.String("status", txn.Status.String()))

		metrics.RecordOnAppTxnStateChangeMetrics(txn, bankName)

		if txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
			s.upiProcessor.AddUpiMetricsForPspAsync(ctx, txn.GetStatus(), txn.GetPiFrom(), txn.GetPiTo())
		}
	}

	if reqInfo.GetUpiInfo().GetPurpose() == vendors.DefaultPurposeUpiLiteEnablement ||
		reqInfo.GetUpiInfo().GetPurpose() == vendors.DefaultPurposeUpiLiteDisablement {
		signalErr := s.sendSignalToUpiLiteActionWorkflow(ctx, txn.GetOrderId())
		if err != nil {
			logger.Error(ctx, "error while sending signal to upi lite action workflow",
				zap.String(logger.TXN_ID, txn.Id), zap.String(logger.ORDER_ID, txn.GetOrderId()),
				zap.Error(signalErr))
		}
	}

	return nil
}

// sendSignalToUpiLiteActionWorkflow signal upi lite action workflow to
// authorise payment during upi lite activation / deactivation
func (s *Service) sendSignalToUpiLiteActionWorkflow(ctx context.Context, orderId string) error {
	order, dbErr := s.orderDao.GetById(ctx, orderId)
	if dbErr != nil {
		return fmt.Errorf("error while fetching order for the given order-id = %v : err = %w", orderId, dbErr)
	}

	payload, _ := protojson.Marshal(&upiPayloadPb.UpiLitePaymentAuthSignal{})

	signalWorkflowRes, err := s.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     order.GetClientReqId(),
				Client: workflowPb.Client_USER_APP,
			},
		},
		SignalId:  string(upiNs.UpiLitePaymentAuthSignal),
		Payload:   payload,
		Ownership: commontypes.Ownership_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(signalWorkflowRes, err); err != nil {
		return fmt.Errorf("error while signaling upi lite action workflow : err = %w", err)
	}
	return nil
}

// notifyDomainWorkflow sends a fund transfer signal to a domain workflow based on the orchestration metadata
// This should be called when the transaction reaches a relevant state, such as:
//   - SUCCESS: Payment fully completed
//   - IN_PROGRESS: User has successfully authorized (e.g., UPI PIN entered, payment in progress at bank/NPCI)
//   - FAILURE: Payment failed or was rejected/expired
//
// The method sends a FundTransferSignal with the transaction status to notify domain workflows of the payment state.
func (s *Service) notifyDomainWorkflow(ctx context.Context, metadata *payPb.ClientIdentificationTxnMetaData, txn *paymentPb.Transaction) error {
	// Determine signal ID from metadata
	signalId := metadata.GetDomainOrderData().GetWorkflowIdentificationInfo().GetSignalId()
	if signalId == "" {
		return fmt.Errorf("signalId is empty: %w", epifierrors.ErrInvalidArgument)
	}

	// Prepare fund transfer signal payload with transaction status
	fundSignal := &signalPb.FundTransferStatusSignal{
		TransactionStatus: txn.GetStatus(),
	}
	payload, err := protojson.Marshal(fundSignal)
	if err != nil {
		return fmt.Errorf("error marshalling fund transfer signal: %w", err)
	}

	// Send signal to workflow
	signalWorkflowRes, err := s.celestialClient.SignalWorkflow(ctx, &celestialPb.SignalWorkflowRequest{
		Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     metadata.GetDomainOrderData().GetWorkflowIdentificationInfo().GetClientReqId().GetId(),
				Client: metadata.GetDomainOrderData().GetWorkflowIdentificationInfo().GetClientReqId().GetClient(),
			},
		},
		SignalId:  signalId,
		Payload:   payload,
		Ownership: commontypes.Ownership_EPIFI_TECH,
	})
	if err = epifigrpc.RPCError(signalWorkflowRes, err); err != nil {
		return fmt.Errorf("error while signaling domain workflow: %w, signal: %s, client_req_id: %s", err, signalId, metadata.GetDomainOrderData().GetWorkflowIdentificationInfo().GetClientReqId().GetId())
	}

	logger.Info(ctx, "successfully sent fund transfer signal to domain workflow",
		zap.String(logger.TXN_ID, txn.GetId()),
		zap.String(logger.TXN_STATUS, txn.GetStatus().String()),
		zap.String(logger.SIGNAL_NAME, signalId),
		zap.String(logger.CLIENT_REQUEST_ID, metadata.GetDomainOrderData().GetWorkflowIdentificationInfo().GetClientReqId().GetId()))

	return nil
}

// notifyDomainService notifies domain service of txn authorisation
func (s *Service) notifyDomainService(ctx context.Context, orchestrationMetadata []byte, txn *paymentPb.Transaction) error {
	if len(orchestrationMetadata) == 0 {
		return nil
	}

	clientIdentificationTxnMetaData := &payPb.ClientIdentificationTxnMetaData{}

	// Decrypt the orchestration metadata (moved from frontend to backend)
	// The frontend now passes the encrypted blob directly to this method
	decryptRes, decryptErr := s.payV1Client.GetPlainData(ctx, &payPb.GetPlainDataRequest{
		SignedData: orchestrationMetadata,
	})
	if te := epifigrpc.RPCError(decryptRes, decryptErr); te != nil {
		return te
	}

	// Unmarshal the decrypted data
	err := proto.Unmarshal(decryptRes.GetPlainData(), clientIdentificationTxnMetaData)
	if err != nil {
		logger.Error(ctx, "error unmarshalling orchestration metadata",
			zap.String(logger.TXN_ID, txn.Id),
			zap.Error(err))
		return err
	}

	// Send signal to domain workflow if workflow details are available
	if clientIdentificationTxnMetaData.GetDomainOrderData().GetWorkflowIdentificationInfo() != nil {
		signalErr := s.notifyDomainWorkflow(ctx, clientIdentificationTxnMetaData, txn)
		if signalErr != nil {
			return signalErr
		}
	}

	return nil
}

// initiateUPICollect initiates a UPI collect request if payer pi doesn't belongs to epiFi,
// and short circuits collect request in-case payer PI belongs to epiFi.
//
// What does short circuiting a collect mean ?
// In normal collect flow we send `ReqPay` to NPCI which then forwards `ReqAuthDetails` to payer PSP. After payer authenticates
// the payment with `RespAuthDetails`. NPCI does attempt payment with remitter and beneficiary banks.
// In case both payer and payee belongs to epiFi we dont have to go through the whole collect flow. We can by pass
// NPCI and send collect request directly to the payer. In this case whenever payer authenticates a transaction
// by entering a pin. We send a pay request to NPCI instead of collect.
//
// This method takes following actions in sequence-
//  1. Fetch account detail and PI details for the both payer and payee PI
//  2. Check if both PIs are internal.
//     a) yes -> mark the transaction as `COLLECT_SHORT_CIRCUITED` and return
//     b) no -> mark the transaction as `COLLECT_INITIATED`
//  3. make the vendor gateway `ReqPay` call with type set as `COLLECT`
//  4. update the transaction status based on the rpc response
//     a) move to `UNKNOWN` status in case of VG rpc time out, as we dont know if the request reached the banks server or not.
//     In case of `UNKNOWN` state txn status is updated via txn status check call or via callback.
//     b) move to `COLLECT_SENT` in case of success acknowledgement from the bank
//     c) move to `FAILED` state in case of permanent failure response from the bank or if there is a transient failure
//     to keep the logic simpler we dont retry in case of transient failure here.
//
// nolint: funlen
func (s *Service) initiateUPICollect(ctx context.Context, txn *paymentPb.Transaction, reqInfo *paymentPb.PaymentRequestInformation,
	device *upiPb.Device, credBlock *upiPb.CredBlock, currentActorId string) (paymentPb.TransactionStatus, error) {
	var (
		// transaction status to be updated in the database
		statusToBeUpdated = paymentPb.TransactionStatus_INITIATED

		// protocol status to be updated in the database
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_COLLECT_INITIATED

		// detailed status to be updated in the database
		txnDetailedStatus *paymentPb.TransactionDetailedStatus_DetailedStatus

		detailState paymentPb.TransactionDetailedStatus_DetailedStatus_State

		payeePi *piPb.PaymentInstrument
		err     error
	)

	payerPi, err := s.validateAndGetPI(ctx, txn.PiFrom, upiPiCheckFunc)
	if err != nil {
		return txn.GetStatus(), fmt.Errorf("unable to fetch payer PI info: %w", err)
	}

	if upiPkg.IsAddFundsPi(txn.GetPiTo()) {
		payeePi, err = s.getPiForAddFunds(ctx, currentActorId, txn.GetPartnerBank())
	} else {
		payeePi, err = s.validateAndGetPI(ctx, txn.PiTo, upiPiCheckFunc)
	}

	if err != nil {
		return paymentPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, fmt.Errorf("unable to fetch payee PI info: %w", err)
	}

	// Entry in db is created first with transaction in INITIATED state to mark at least one call to vendor has been
	// made
	txn.Status = paymentPb.TransactionStatus_INITIATED
	txn.ProtocolStatus = paymentPb.TransactionProtocolStatus_COLLECT_INITIATED
	txn.LocationToken = device.GetLocationToken()
	txn.Utr = s.numberIDGen.GenRRN(txn.GetCreatedAt(), datetime.IST)
	updateFieldMask := []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
		paymentPb.TransactionFieldMask_PROTOCOL_STATUS, paymentPb.TransactionFieldMask_UTR,
		paymentPb.TransactionFieldMask_LOCATION_TOKEN}
	if err = s.txnDao.Update(ctx, txn, reqInfo, updateFieldMask); err != nil {
		return paymentPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, fmt.Errorf("failed to update transaction to %s: %v %w", txn.Status.String(), err, epifierrors.ErrTransient)
	}

	publishErr := payPkg.PublishTxnDetailedStatusUpdateEventIfRequired(ctx, txn, s.txnDetailedStatusUpdateSnsPublisher, updateFieldMask, false)
	if publishErr != nil {
		logger.Error(ctx, "failed to publish event to txn detailed status update topic", zap.Error(publishErr))
	}

	bankName, _ := payPkgTxns.GetBankNameForPiId(ctx, txn.GetPiFrom(), s.piClient)
	metrics.RecordOnAppTxnStateChangeMetrics(txn, bankName)

	if txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
		s.upiProcessor.AddUpiMetricsForPspAsync(ctx, txn.GetStatus(), txn.GetPiFrom(), txn.GetPiTo())
	}

	vgPayReq, err := s.createVgReqPayRequest(ctx, txn, reqInfo, payerPi, payeePi, vgUPIPb.ReqPayRequest_COLLECT, device, credBlock, "", currentActorId, nil, nil)
	if err != nil {
		return paymentPb.TransactionStatus_TRANSACTION_STATUS_UNSPECIFIED, fmt.Errorf("error while creating req pay vg request: err = %w", err)
	}

	logger.Info(ctx, "Initiating UPI collect to vendor gateway")
	vgPayRes, err := s.vgUPIClient.ReqPay(ctx, vgPayReq)

	// nolint: dupl
	switch {
	// move to transaction to UNKNOWN status code post rpc call failure.
	case err != nil:
		logger.Error(ctx, "vendor gateway payment rpc failed moving to unknown status",
			zap.String("id", txn.Id),
			zap.Error(err))
		// All transactions in unknown state are updated post status check call or callbacks.
		statusToBeUpdated = paymentPb.TransactionStatus_UNKNOWN
		txnDetailedStatus = payPkg.GetTxnDetailedStatusForSystemErr(err, paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_PAY)
		txn.AddDetailedStatus(txnDetailedStatus)
	// mark the transaction as failed in case of terminating status code from the payment rpc.
	case vgPayRes.GetStatus().GetCode() == uint32(vgUPIPb.ReqPayResponse_FAILED):
		statusToBeUpdated = paymentPb.TransactionStatus_FAILED
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_COLLECT_REQ_PAY_SENT
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
	// if non of the above failure cases match, then it means vendorgateway returned a positive acknowledgment. Thus, move to
	// COLLECT_SENT state.
	case vgPayRes.GetStatus().IsSuccess():
		statusToBeUpdated = paymentPb.TransactionStatus_IN_PROGRESS
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_COLLECT_REQ_PAY_SENT
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS
	default:
		statusToBeUpdated = paymentPb.TransactionStatus_UNKNOWN
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_COLLECT_REQ_PAY_SENT
	}
	if err == nil {
		txnDetailedStatus = upi2.GetTxnDetailedStatusUpiForAck(detailState, vgPayRes.GetRawStatusCode(), vgPayRes.GetRawStatusDescription(),
			vgPayRes.GetStatusCode(), vgPayRes.GetStatusDescriptionPayer(), vgPayRes.GetStatusDescriptionPayee(), paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_PAY)
		txn.AddDetailedStatus(txnDetailedStatus)
	}

	txn.Status = statusToBeUpdated
	txn.ProtocolStatus = protocolStatusToBeUpdated
	updateFieldMask = []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
		paymentPb.TransactionFieldMask_PROTOCOL_STATUS, paymentPb.TransactionFieldMask_DETAILED_STATUS}
	err = s.txnDao.Update(ctx, txn, nil, updateFieldMask)
	if err != nil {
		logger.Error(ctx, "transaction status update failed",
			zap.String("id", txn.Id),
			zap.String("status", statusToBeUpdated.String()),
			zap.Error(err))
	} else {
		logger.Debug(ctx, "collect transaction initialization successful!",
			zap.String("id", txn.Id),
			zap.String("status", txn.Status.String()))

		metrics.RecordOnAppTxnStateChangeMetrics(txn, bankName)
		if txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
			s.upiProcessor.AddUpiMetricsForPspAsync(ctx, txn.GetStatus(), txn.GetPiFrom(), txn.GetPiTo())
		}
		publishErr = payPkg.PublishTxnDetailedStatusUpdateEventIfRequired(ctx, txn, s.txnDetailedStatusUpdateSnsPublisher, updateFieldMask, false)
		if publishErr != nil {
			logger.Error(ctx, "failed to publish event to txn detailed status update topic", zap.Error(publishErr))
		}
	}

	return statusToBeUpdated, nil
}

// initiateRespAuth sends back resp auth to NPCI along with payeer auth details like account identifier and
// cred block. This method is only used in case of a `COLLECT` request came from a non-epiFi payee.
//
// This method takes following actions in sequence-
//  1. Fetch account detail and VPA details for the both payer and payee PI
//  2. Mark the transaction as `RESP_AUTH_INITIATED` so as to identify if payment was initialized once by payer.
//  3. make the vendor gateway `RespAuthDetails` call.
//  4. update the transaction status based on the rpc response
//     a) move to `UNKNOWN` status in case of rpc failure
//     all the transactions in unknown status are updated with responses from status call or callbacks.
//     b) move to `RESP_AUTH_SENT` in case of success acknowledgement from the bank
//     c) move to FAILED in case of terminal failure response from the bank
//
// nolint: funlen
func (s *Service) initiateRespAuth(ctx context.Context, txn *paymentPb.Transaction, reqInfo *paymentPb.PaymentRequestInformation,
	payAuth *paymentPb.InitiateTransactionRequest_AuthHeader) error {
	var (
		// transaction status to be updated in the database
		statusToBeUpdated = paymentPb.TransactionStatus_INITIATED

		// protocol status to be updated in the database
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED

		// detailed status to be updated in the database
		txnDetailedStatus *paymentPb.TransactionDetailedStatus_DetailedStatus

		detailState paymentPb.TransactionDetailedStatus_DetailedStatus_State
	)

	payerPi, err := s.validateAndGetPI(ctx, txn.PiFrom, upiPiCheckFunc)
	if err != nil {
		return fmt.Errorf("unable to fetch payer PI info: %w", err)
	}

	payeePi, err := s.validateAndGetPI(ctx, txn.PiTo, upiPiCheckFunc)
	if err != nil {
		return fmt.Errorf("unable to fetch payee PI info: %w", err)
	}

	// Entry in db is updated first with transaction in RESP_AUTH_INITIATED state to mark at least one call to vendor has been
	// made
	txn.Status = paymentPb.TransactionStatus_INITIATED
	txn.ProtocolStatus = paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED
	txn.LocationToken = payAuth.GetDevice().GetLocationToken()
	updateFieldMask := []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
		paymentPb.TransactionFieldMask_PROTOCOL_STATUS, paymentPb.TransactionFieldMask_LOCATION_TOKEN}
	if err = s.txnDao.Update(ctx, txn, nil, updateFieldMask); err != nil {
		return fmt.Errorf("failed to update transaction to %s: %w", txn.Status.String(), err)
	}
	publishErr := payPkg.PublishTxnDetailedStatusUpdateEventIfRequired(ctx, txn, s.txnDetailedStatusUpdateSnsPublisher, updateFieldMask, false)
	if publishErr != nil {
		logger.Error(ctx, "failed to publish event to txn detailed status update topic", zap.Error(publishErr))
	}

	bankName, _ := payPkgTxns.GetBankNameForPiId(ctx, txn.GetPiFrom(), s.piClient)
	metrics.RecordOnAppTxnStateChangeMetrics(txn, bankName)

	if txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
		s.upiProcessor.AddUpiMetricsForPspAsync(ctx, txn.GetStatus(), txn.GetPiFrom(), txn.GetPiTo())
	}
	vgRespAuthReq := createVgRespAuthRequest(txn, reqInfo, payerPi, payeePi, payAuth)

	logger.Info(ctx, "Initiating UPI payment to vendor gateway")
	vgRespAuthRes, err := s.vgUPIClient.RespAuthDetails(ctx, vgRespAuthReq)

	// nolint: dupl
	switch {
	// move to transaction to UNKNOWN status code post rpc call failure.
	case err != nil:
		logger.Error(ctx, "vendor gateway respAuth rpc failed moving to unknown status",
			zap.String("id", txn.Id),
			zap.Error(err))
		// For unknown status code transaction status check will be triggered after expire_at + 90 seconds
		// as per the standard NPCI norms
		statusToBeUpdated = paymentPb.TransactionStatus_UNKNOWN
		txnDetailedStatus = payPkg.GetTxnDetailedStatusForSystemErr(err, paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH)
		txn.AddDetailedStatus(txnDetailedStatus)
	// mark the transaction as failed in case of terminating status code from the respAuth rpc.
	// typically returned in case of txn expire_at crossed.
	case vgRespAuthRes.GetStatus().GetCode() == uint32(vgUPIPb.RespAuthDetailsResponse_FAILED):
		statusToBeUpdated = paymentPb.TransactionStatus_FAILED
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_FAILURE
	// if non of the above failure cases match, then it means vendor-gateway returned a positive acknowledgment. Thus, move to
	// RESP_AUTH_SENT state.
	case vgRespAuthRes.GetStatus().IsSuccess():
		statusToBeUpdated = paymentPb.TransactionStatus_IN_PROGRESS
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS
	default:
		statusToBeUpdated = paymentPb.TransactionStatus_UNKNOWN
		detailState = paymentPb.TransactionDetailedStatus_DetailedStatus_UNKNOWN
		protocolStatusToBeUpdated = paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT
	}

	if err == nil {
		txnDetailedStatus = upi2.GetTxnDetailedStatusUpiForAck(detailState, vgRespAuthRes.GetRawStatusCode(), vgRespAuthRes.GetRawStatusDescription(),
			vgRespAuthRes.GetStatusCode(), vgRespAuthRes.GetStatusDescriptionPayer(), vgRespAuthRes.GetStatusDescriptionPayee(), paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH)
		txn.AddDetailedStatus(txnDetailedStatus)
	}

	txn.Status = statusToBeUpdated
	txn.ProtocolStatus = protocolStatusToBeUpdated
	updateFieldMask = []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
		paymentPb.TransactionFieldMask_PROTOCOL_STATUS, paymentPb.TransactionFieldMask_DETAILED_STATUS}
	err = s.txnDao.Update(ctx, txn, nil, updateFieldMask)
	// In case of failure status check / callback shall update txn status
	if err != nil {
		logger.Error(ctx, "transaction status update failed",
			zap.String("id", txn.Id),
			zap.String("status", statusToBeUpdated.String()),
			zap.Error(err))
	} else {
		logger.Debug(ctx, "transaction resp auth sent successfully!",
			zap.String("id", txn.Id),
			zap.String("status", txn.Status.String()))
		metrics.RecordOnAppTxnStateChangeMetrics(txn, bankName)
		if txn.GetPaymentProtocol() == paymentPb.PaymentProtocol_UPI {
			s.upiProcessor.AddUpiMetricsForPspAsync(ctx, txn.GetStatus(), txn.GetPiFrom(), txn.GetPiTo())
		}
		publishErr = payPkg.PublishTxnDetailedStatusUpdateEventIfRequired(ctx, txn, s.txnDetailedStatusUpdateSnsPublisher, updateFieldMask, false)
		if publishErr != nil {
			logger.Error(ctx, "failed to publish event to txn detailed status update topic", zap.Error(publishErr))
		}
	}

	return nil
}

// createVgReqPayRequest creates and returns `ReqPayRequest` struct to be used to call VG api
// It populates payer and payee details based on type of request
// nolint: funlen
func (s *Service) createVgReqPayRequest(ctx context.Context, txn *paymentPb.Transaction, reqInfo *paymentPb.PaymentRequestInformation,
	payerPi, payeePi *piPb.PaymentInstrument, reqType vgUPIPb.ReqPayRequest_Type, device *upiPb.Device,
	credBlock *upiPb.CredBlock, urn, actorId string, baseAmountQuoteCurrency *moneyPb.Money, npciCredBlocks *paymentPb.NpciCredBlocks) (*vgUPIPb.ReqPayRequest, error) {
	var (
		rules                  *vgUPIPb.ReqPayRequest_Rules
		upiCreds, upiLiteCreds []*upiPb.CredBlock
	)

	// ignoring error here as we already have validations on PI type
	payerVPA, _ := payerPi.GetVPA()
	payeeVPA, _ := payeePi.GetVPA()
	txnNote := txn.GetRemarks()

	if txnNote == "" {
		txnNote = DefaultUPIRemarks
	}

	if npciCredBlocks != nil && len(npciCredBlocks.GetCredBlocks()) > 0 {
		for _, cred := range npciCredBlocks.GetCredBlocks() {
			if cred.GetType() == upiPb.CredBlock_ARQC || cred.GetType() == upiPb.CredBlock_ARPC {
				upiLiteCreds = []*upiPb.CredBlock{cred}
			} else {
				upiCreds = []*upiPb.CredBlock{cred}
			}
		}
	} else {
		// for backward compatibility
		upiCreds = []*upiPb.CredBlock{credBlock}
	}

	// by default payee and payer entity type is initialised as PERSON
	// then based on reqType and upiInfo in payment_request_info it is updated
	// to entity
	payer := &upiPb.Customer{
		PaymentAddress: payerVPA,
		Name:           payerPi.GetName(),
		Amount:         txn.Amount,
		Type:           upiPb.CustomerType_PERSON,
		MCC:            vendors.PersonMccCode,
		CmId:           reqInfo.GetUpiInfo().GetPayerUpiNumber(),
	}

	payee := &upiPb.Customer{
		PaymentAddress: payeeVPA,
		Name:           payeePi.GetName(),
		Amount:         txn.GetAmount(),
		Type:           upiPb.CustomerType_PERSON,
		MCC:            vendors.PersonMccCode,
		CmId:           reqInfo.GetUpiInfo().GetPayeeUpiNumber(),
	}

	if reqType == vgUPIPb.ReqPayRequest_PAY {
		payer.Device = device
		payer.Creds = upiCreds
		// ignoring error here as we already have validations on PI type
		payer.AccountDetails, _ = upiPb.GetUPIAccountDetails(payerPi)
		payer.Info, _ = upiPb.GetUPICustomerInfo(payerPi, device.GetPhoneNumber())
		payer.Info.Identity.Id = device.PhoneNumber.ToString()

		if payerPi.GetType() == piPb.PaymentInstrumentType_UPI_LITE {
			payer.Creds = upiLiteCreds
			payerAccDetails, err := upiPb.GetUpiLiteAccountDetails(ctx, payerPi, s.piClient)
			if err != nil {
				return nil, fmt.Errorf("error while fetching accoumt details for upi lite pi %s %w", payerPi.GetId(), err)
			}
			payer.AccountDetails = payerAccDetails
		}

		if payeePi.GetUpi().GetMerchantDetails().GetMcc() != "" && payeePi.GetUpi().GetMerchantDetails().GetMcc() != vendors.PersonMccCode {
			payee.Type = upiPb.CustomerType_ENTITY
			payee.MCC = payeePi.GetUpi().GetMerchantDetails().GetMcc()
			payee.Merchant = upiPb.ParsePIMerchantDetails(payeePi.GetUpi().GetMerchantDetails())
			mergeMerchantDetailsWithPayReqInfo(payee.Merchant, reqInfo.GetUpiInfo().GetMerchantDetails())
		}

		if payeePi.GetType() == piPb.PaymentInstrumentType_UPI_LITE {
			payee.Creds = upiLiteCreds
			payeeAccDetails, err := upiPb.GetUpiLiteAccountDetails(ctx, payeePi, s.piClient)
			if err != nil {
				return nil, fmt.Errorf("error while fetching accoumt details for upi lite pi %s %w", payeePi.GetId(), err)
			}
			payee.AccountDetails = payeeAccDetails
		}
	} else {
		payee.Device = device
		// ignoring error here as we already have validations on PI type
		payee.AccountDetails, _ = upiPb.GetUPIAccountDetails(payeePi)
		payee.Info, _ = upiPb.GetUPICustomerInfo(payeePi, device.GetPhoneNumber())

		if payerPi.GetUpi().GetMerchantDetails().GetMcc() != "" && payerPi.GetUpi().GetMerchantDetails().GetMcc() != vendors.PersonMccCode {
			payer.Type = upiPb.CustomerType_ENTITY
			payer.MCC = payerPi.GetUpi().GetMerchantDetails().GetMcc()
			payer.Merchant = upiPb.ParsePIMerchantDetails(payerPi.GetUpi().GetMerchantDetails())
			mergeMerchantDetailsWithPayReqInfo(payer.Merchant, reqInfo.GetUpiInfo().GetMerchantDetails())
		}

		if payeePi.GetUpi().GetMerchantDetails().GetMcc() != "" && payeePi.GetUpi().GetMerchantDetails().GetMcc() != vendors.PersonMccCode {
			payee.Type = upiPb.CustomerType_ENTITY
			payee.MCC = payeePi.GetUpi().GetMerchantDetails().GetMcc()
			payee.Merchant = upiPb.ParsePIMerchantDetails(payeePi.GetUpi().GetMerchantDetails())
			mergeMerchantDetailsWithPayReqInfo(payee.Merchant, reqInfo.GetUpiInfo().GetMerchantDetails())
		}

		// to begin with populating rules only for collect as for PAY we are not sure
		// what is behaviour of setting the rules
		rules = &vgUPIPb.ReqPayRequest_Rules{
			ExpireAfter: reqInfo.GetUpiInfo().GetRules().GetExpireAfter(),
			MinAmount:   reqInfo.GetUpiInfo().GetRules().GetMinAmount(),
		}
	}

	reqPayReq := &vgUPIPb.ReqPayRequest{
		Header: &commonvgpb.RequestHeader{Vendor: txn.PartnerBank},
		TxnHeader: &vgUPIPb.TransactionHeader{
			TransactionId:  reqInfo.GetReqId(),
			Note:           txnNote,
			CustRefId:      txn.GetUtr(),
			RefUrl:         reqInfo.GetUpiInfo().GetRefUrl(),
			TransTimestamp: ptypes.TimestampNow(), // Transaction origination time by the creator of the message
			InitiationMode: reqInfo.GetUpiInfo().GetInitiationMode(),
			RefId:          reqInfo.GetUpiInfo().GetMerchantRefId(),
			Purpose:        reqInfo.GetUpiInfo().GetPurpose(),
		},
		ReqType: reqType,
		Payer:   payer,
		Payees:  []*upiPb.Customer{payee},
		Rules:   rules,
	}

	isUpiInternationalPayment := strings.HasPrefix(urn, upiGlobalQR)
	if (baseAmountQuoteCurrency != nil) != isUpiInternationalPayment {
		return nil, fmt.Errorf("invalid arguments received for international upi payments")
	}

	if isUpiInternationalPayment {
		// NOTE - Original Txn Id is not expected in the normal payment flow
		// if sent in normal payments req payload, txns will fail !
		reqPayReq.TxnHeader.OriginalTxnId = reqPayReq.TxnHeader.GetTransactionId()
		err := s.mergeUpiIntlQrDetailsWithVendorReq(ctx, txn, reqPayReq, urn, actorId, baseAmountQuoteCurrency)
		if err != nil {
			return nil, fmt.Errorf("error while adding qrDetails in txn header: err = %w", err)
		}
	}

	return reqPayReq, nil
}

// mergeUpiIntlQrDetailsWithVendorReq - add cached qr details in the req pay request in some cases
// like upi international payments
func (s *Service) mergeUpiIntlQrDetailsWithVendorReq(ctx context.Context, txn *paymentPb.Transaction,
	req *vgUPIPb.ReqPayRequest, urn, actorId string, baseAmountQuoteCurrency *moneyPb.Money) error {
	var (
		err error
	)

	internationalQrInfoRes, err := s.upiClient.GetUpiInternationalQrInfoFromCache(ctx, &upiPb.GetUpiInternationalQrInfoFromCacheRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(internationalQrInfoRes, err); err != nil {
		return fmt.Errorf("error while fetching international qr info from cache: err = %w", err)
	}

	var fxDetail *upiPb.FxDetail
	for _, fx := range internationalQrInfoRes.GetUpiInternationalQrInfo().GetForexDetailList() {
		if fx.GetBaseCurrency() == baseAmountQuoteCurrency.GetCurrencyCode() {
			fxDetail = fx
			break
		}
	}

	// Ideally this shouldn't happen as fxDetail usign which payment is being initiated must be in cache
	// otherwise flow shouldn't even reach this step
	if fxDetail == nil {
		return fmt.Errorf("currency code for the payment not found in cache: err = %w", epifierrors.ErrInvalidArgument)
	}

	// qrPayload is not a part of cached data, so it is propagated from frontend to backend
	// and needs to be populated separately
	internationalQrInfoRes.UpiInternationalQrInfo.Institution.QrPayload = urn

	req.TxnHeader.Purpose = vendors.PurposeForUpiGlobal
	req.TxnHeader.QrDetails = internationalQrInfoRes.GetUpiInternationalQrInfo().GetQrDetails()
	req.Payer.Institution = internationalQrInfoRes.GetUpiInternationalQrInfo().GetInstitution()
	req.Payer.FxList = []*upiPb.FxDetail{fxDetail}

	return nil
}

// createVgRespAuthRequest creates and returns `RespAuthDetailsRequest` struct to be used to call VG api
func createVgRespAuthRequest(txn *paymentPb.Transaction, reqInfo *paymentPb.PaymentRequestInformation,
	payerPi, payeePi *piPb.PaymentInstrument, payAuth *paymentPb.InitiateTransactionRequest_AuthHeader) *vgUPIPb.RespAuthDetailsRequest {
	// ignoring error here as we already have validations on PI type
	payerVPA, _ := payerPi.GetVPA()
	payeeVPA, _ := payeePi.GetVPA()

	payerVPA = reqInfo.SafeFetchPayerVpa(payerVPA)
	payeeVPA = reqInfo.SafeFetchPayeeVpa(payeeVPA)

	creds := []*upiPb.CredBlock{payAuth.GetNpciCredBlock()}
	device := payAuth.Device

	payer := &upiPb.Customer{
		PaymentAddress: payerVPA,
		Name:           payerPi.GetName(),
		Amount:         txn.Amount,
		Device:         device,
		Creds:          creds,
		Type:           upiPb.CustomerType_PERSON, // Initially, since we dont have merchants all the customer type will PERSON
		MCC:            vendors.PersonMccCode,
		CmId:           reqInfo.GetUpiInfo().GetPayerUpiNumber(),
		SeqNum:         reqInfo.GetUpiInfo().GetSeqNum(),
	}

	// ignoring error here as we already have validations on PI type
	payer.AccountDetails, _ = upiPb.GetUPIAccountDetails(payerPi)
	payer.Info, _ = upiPb.GetUPICustomerInfo(payerPi, device.GetPhoneNumber())

	payee := &upiPb.Customer{
		PaymentAddress: payeeVPA,
		Name:           payeePi.GetName(),
		Amount:         txn.Amount,
		Type:           upiPb.CustomerType_PERSON,
		MCC:            vendors.PersonMccCode,
		Info:           reqInfo.GetUpiInfo().GetCustomerInfo(),
		AccountDetails: reqInfo.GetUpiInfo().GetCustomerAccountInfo(),
		CmId:           reqInfo.GetUpiInfo().GetPayeeUpiNumber(),
		SeqNum:         reqInfo.GetUpiInfo().GetSeqNum(),
	}

	// in case of resp auth (approval against collect) payee can be merchant
	if payeePi.GetUpi().GetMerchantDetails().GetMcc() != "" && payeePi.GetUpi().GetMerchantDetails().GetMcc() != vendors.PersonMccCode {
		payee.Type = upiPb.CustomerType_ENTITY
		payee.MCC = payeePi.GetUpi().GetMerchantDetails().GetMcc()
		payee.Merchant = upiPb.ParsePIMerchantDetails(payeePi.GetUpi().GetMerchantDetails())
		mergeMerchantDetailsWithPayReqInfo(payee.Merchant, reqInfo.GetUpiInfo().GetMerchantDetails())
	}

	return &vgUPIPb.RespAuthDetailsRequest{
		Header: &commonvgpb.RequestHeader{Vendor: txn.PartnerBank},
		TxnHeader: &vgUPIPb.TransactionHeader{
			TransactionId:  reqInfo.GetReqId(),
			Note:           txn.Remarks,
			CustRefId:      txn.GetUtr(),
			RefUrl:         reqInfo.GetUpiInfo().GetRefUrl(),
			TransTimestamp: reqInfo.GetUpiInfo().GetTxnOriginTimestamp(),
			InitiationMode: reqInfo.GetUpiInfo().GetInitiationMode(),
			RefId:          reqInfo.GetUpiInfo().GetMerchantRefId(),
			Purpose:        reqInfo.GetUpiInfo().GetPurpose(),
			RefCategory:    reqInfo.GetUpiInfo().GetRefCategory(),
		},
		Rules: &vgUPIPb.RespAuthDetailsRequest_Rules{
			ExpireAfter: reqInfo.GetUpiInfo().GetRules().GetExpireAfter(),
			MinAmount:   reqInfo.GetUpiInfo().GetRules().GetMinAmount(),
		},
		// Type is set to collect as resp auth for pay is sent immediately on receiving `ReqAuth`
		// only for type collect we send `RespAuth` when user authorizes the payment
		ReqType: vgUPIPb.RespAuthDetailsRequest_COLLECT,
		// for success scenarios, error code is ""
		Resp: &vgUPIPb.ResponseHeader{
			ReqMsgId: reqInfo.GetUpiInfo().GetMsgId(),
			Result:   vgUPIPb.ResponseHeader_SUCCESS,
		},
		Payer:  payer,
		Payees: []*upiPb.Customer{payee},
	}
}

// mergeMerchantDetailsWithPayReqInfo overwrites merchantId, storeId and terminalId for a given merchant details
func mergeMerchantDetailsWithPayReqInfo(toMd *upiPb.MerchantDetails, fromMd *upiPb.MerchantDetails) {
	if fromMd.GetMerchantTerminalId() != "" {
		toMd.MerchantTerminalId = fromMd.GetMerchantTerminalId()
	}

	if fromMd.GetMerchantStoreId() != "" {
		toMd.MerchantStoreId = fromMd.GetMerchantStoreId()
	}

	if fromMd.GetMerchantId() != "" {
		toMd.MerchantId = fromMd.GetMerchantId()
	}

	if fromMd.GetRegId() != "" {
		toMd.RegId = fromMd.GetRegId()
	}

	if fromMd.GetPinCode() != "" {
		toMd.PinCode = fromMd.GetPinCode()
	}

	if fromMd.GetTier() != "" {
		toMd.Tier = fromMd.GetTier()
	}

	if fromMd.GetMerchantLocation() != "" {
		toMd.MerchantLocation = fromMd.GetMerchantLocation()
	}

	if fromMd.GetMerchantInstituteCode() != "" {
		toMd.MerchantInstituteCode = fromMd.GetMerchantInstituteCode()
	}

	if fromMd.GetOnboardingType() != upiPb.MerchantOnboardingType_MERCHANT_ONBOARDING_TYPE_UNSPECIFIED {
		toMd.OnboardingType = fromMd.GetOnboardingType()
	}

	if fromMd.GetSubCode() != "" {
		toMd.Mcc = fromMd.GetSubCode()
	}
}

// getPiForAddFunds returns a custom pi to be used for making add funds request
func (s *Service) getPiForAddFunds(ctx context.Context, actorId string, partnerBank commonvgpb.Vendor) (*piPb.PaymentInstrument, error) {
	var (
		accountPi *piPb.PaymentInstrument
	)

	res, err := s.accountPiClient.GetPiByActorId(ctx, &accountPiPb.GetPiByActorIdRequest{ActorId: actorId})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, fmt.Errorf("error fetchng pi for actor: %s %w", actorId, epifierrors.ErrTransient)
	}

	for _, pi := range res.GetPaymentInstruments() {
		if pi.GetType() == piPb.PaymentInstrumentType_BANK_ACCOUNT &&
			pi.GetAccount().GetAccountType() == accounts.Type_SAVINGS {
			accountPi = pi
		}
	}

	if accountPi == nil {
		return nil, fmt.Errorf("no account pi present for actor: %s %w", actorId, epifierrors.ErrPermanent)
	}

	isEnabled, err := s.upiProcessor.IsNewAddFundsVpaEnabledForActor(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("error checking new add funds vpa is enabled for actor %s %w", actorId, err)
	}
	if isEnabled {
		dynamicVpaConfig, ok := s.conf.DynamicVPAV1ParamsMap()[partnerBank.String()]
		if !ok {
			return nil, fmt.Errorf("dynamic vpa config not present for vendor: %s %w", partnerBank.String(), epifierrors.ErrPermanent)
		}

		return &piPb.PaymentInstrument{
			Id:   payPkg.FederalPoolAccountV1PiId,
			Type: piPb.PaymentInstrumentType_UPI,
			Identifier: &piPb.PaymentInstrument_Upi{
				Upi: &piPb.Upi{
					// todo(79916): this vpa is not actually dynamic, so static vpa makes more sense.
					// This will require config structure refactoring.
					Vpa:                    dynamicVpaConfig.DynamicVPAString,
					AccountReferenceNumber: dynamicVpaConfig.AccountReferenceNumber,
					MaskedAccountNumber:    accountPi.GetAccount().GetSecureAccountNumber(),
					IfscCode:               accountPi.GetAccount().GetIfscCode(),
					AccountType:            accountPi.GetAccount().GetAccountType(),
					MerchantDetails:        upiPb.ConvertToPIMerchantDetails(dynamicVpaConfig.MerchantDetails, dynamicVpaConfig.MerchantDetails.GetMcc()),
					Name:                   dynamicVpaConfig.Name,
				}},
			VerifiedName: dynamicVpaConfig.Name,
		}, nil
	}

	dynamicVpaConfig, ok := s.conf.DynamicVPAParamsMap()[partnerBank.String()]
	if !ok {
		return nil, fmt.Errorf("dynamic vpa config not present for vendor: %s %w", partnerBank.String(), epifierrors.ErrPermanent)
	}
	// generate the dynamic vpa
	dynamicVpaParams := []string{accountPi.GetAccount().GetActualAccountNumber()}
	return &piPb.PaymentInstrument{
		Id:   payPkg.FederalPoolAccountPiId,
		Type: piPb.PaymentInstrumentType_UPI,
		Identifier: &piPb.PaymentInstrument_Upi{
			Upi: &piPb.Upi{
				Vpa:                    dynamicVpaConfig.GetDynamicVpa(dynamicVpaParams...),
				AccountReferenceNumber: dynamicVpaConfig.AccountReferenceNumber,
				MaskedAccountNumber:    accountPi.GetAccount().GetSecureAccountNumber(),
				IfscCode:               accountPi.GetAccount().GetIfscCode(),
				AccountType:            accountPi.GetAccount().GetAccountType(),
				MerchantDetails:        upiPb.ConvertToPIMerchantDetails(dynamicVpaConfig.MerchantDetails, dynamicVpaConfig.MerchantDetails.GetMcc()),
				Name:                   dynamicVpaConfig.Name,
			}},
		VerifiedName: dynamicVpaConfig.Name,
	}, nil

}

// genUtrAndUpdateUpiTransactionWithRetry generates the utr for upi transaction and update the transaction
// in case of utr collision it retries the entire process
func (s *Service) genUtrAndUpdateUpiTransactionWithRetry(
	ctx context.Context,
	txn *paymentPb.Transaction,
	reqInfo *paymentPb.PaymentRequestInformation,
	updateMask []paymentPb.TransactionFieldMask,
	currentStatus paymentPb.TransactionStatus,
	nextStatus paymentPb.TransactionStatus,
) error {
	retryCount := integer.Max(int(s.conf.UpiUtrCollisionRetryCount()), 1)
	for retryCount > 0 {
		txn.Utr = s.numberIDGen.GenRRN(txn.GetCreatedAt(), datetime.IST)

		updateMask = append(updateMask, paymentPb.TransactionFieldMask_UTR)
		err := s.paymentHealthProcessor.UpdateAndChangeStatusAndPushMetrics(ctx, txn, reqInfo, updateMask, currentStatus, nextStatus)
		switch {
		case errors.Is(err, epifierrors.ErrDuplicateEntry):
			logger.Error(ctx, "got duplicate entry error, retrying again", zap.String(logger.TXN_ID, txn.GetId()))
			retryCount--
		case err != nil:
			return fmt.Errorf("error updating transaction :%w", err)
		default:
			return nil
		}
	}
	return fmt.Errorf("number of retries exhausted for utr collison")
}

// setLocationTokenForActorInOrder populates location token for the current actor in order
// if the location token is already present we will not update the location
func (s *Service) setLocationTokenForActorInOrder(ctx context.Context, orderId string, currentActorId string, locationToken string) error {
	var (
		orderUpdateFieldMasks []beOrderPb.OrderFieldMask
	)
	if locationToken == "" {
		logger.Info(ctx, "empty location token received", zap.String(logger.ORDER_ID, orderId),
			zap.String(logger.ACTOR_ID, currentActorId))
		return nil
	}
	order, err := s.orderDao.GetById(ctx, orderId)
	if err != nil {
		return fmt.Errorf("error fetching order for the given id: %s", orderId)
	}

	if order.GetFromActorId() == currentActorId && order.GetFromActorLocationToken() == "" {
		order.FromActorLocationToken = locationToken
		orderUpdateFieldMasks = append(orderUpdateFieldMasks, beOrderPb.OrderFieldMask_FROM_ACTOR_LOCATION_TOKEN)
	} else if order.GetToActorId() == currentActorId && order.GetToActorLocationToken() == "" {
		order.ToActorLocationToken = locationToken
		orderUpdateFieldMasks = append(orderUpdateFieldMasks, beOrderPb.OrderFieldMask_TO_ACTOR_LOCATION_TOKEN)
	}

	if len(orderUpdateFieldMasks) != 0 {
		err = s.orderDao.UpdateAndChangeStatus(ctx, order, orderUpdateFieldMasks, order.GetStatus(), order.GetStatus())
		if err != nil {
			return fmt.Errorf("error updating locaiton token in order: %s : %w", orderId, err)
		}
	}
	return nil
}

func getNewUpiTransactionEvent(req *paymentPb.InitiateTransactionRequest, txn *paymentPb.Transaction, reqInfo *paymentPb.PaymentRequestInformation) (*orderEvent.UpiTransactionEvent, error) {
	var (
		amountInPaisa int64
		err           error
	)
	if amountInPaisa, err = money.ToPaise(txn.GetAmount()); err != nil {
		return nil, fmt.Errorf("error converting money to paise: err = %w", err)
	}
	isDeemedTxn := false
	detailedStatusList := txn.GetLatestTxnDetailedStatus()
	if detailedStatusList.GetState() == paymentPb.TransactionDetailedStatus_DetailedStatus_DEEMED {
		isDeemedTxn = true
	}
	errCode := detailedStatusList.GetRawStatusCode()
	geocode := req.GetPaymentAuth().GetDevice().GetGeocode()
	// This time should be considered only when transaction is in terminal state, else it should not be considered
	// It stores the time elapsed from time of transaction creation to time at which transaction reaches in terminal state
	elaspedTime := txn.GetUpdatedAt().AsTime().Sub(txn.GetCreatedAt().AsTime())
	return orderEvent.NewUpiTransactionEvent(req.GetActorId(), txn.GetStatus().String(),
		reqInfo.GetUpiInfo().GetPayerVpa(), reqInfo.GetUpiInfo().GetPayeeVpa(), strconv.FormatInt(amountInPaisa, 10), reqInfo.GetUpiInfo().GetMerchantDetails().GetBrandName(),
		reqInfo.GetUpiInfo().GetMcc(), txn.GetRemarks(), errCode, strconv.FormatFloat(geocode.GetLatitude(), 'f', 2, 64), strconv.FormatFloat(geocode.GetLongitude(), 'f', 2, 64), isDeemedTxn, elaspedTime), nil
}

func (s *Service) getPhoneNumberByActorId(ctx context.Context, actorId string) (*commontypes.PhoneNumber, error) {
	userResp, err := s.userClient.GetUser(ctx, &userpb.GetUserRequest{
		Identifier: &userpb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err = epifigrpc.RPCError(userResp, err); err != nil {
		return nil, err
	}
	return userResp.GetUser().GetProfile().GetPhoneNumber(), nil
}
