package interceptor

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc/interceptors/ratelimit/keygen"
	"github.com/epifi/be-common/pkg/logger"
)

const (
	rewardsClient                          = "rewards_generator_consumer_processdatacollectorevent"
	txnCategorizerBatchGetCategoriesClient = "categorizer_txncategorizer_batchgetcategories"
	riskTxnReviewClient                    = "cx_risk_ops_riskops_gettransactionreviewdetails"
)

type OrderKeyGenerator struct{}

func NewOrderKeyGenerator() keygen.IKeyGenerator {
	return &OrderKeyGenerator{}
}

var _ keygen.IKeyGenerator = &OrderKeyGenerator{}

func (r *OrderKeyGenerator) GenerateKey(ctx context.Context, req interface{}, fullGrpcMethodName string) (string, error) {
	// some specific client which needs different resource allocation/ specific rate limiting

	fullGrpcMethodNameTransformed := keygen.TransformGrpcMethodName(fullGrpcMethodName)

	clInfo := epificontext.ClientInfoFromContext(ctx)

	logger.Debug(ctx, "order rate-limiter debug logs", zap.String("client", clInfo.Identifier))

	switch clInfo.Identifier {
	// different resource allocation for rewards client.
	// separate ratelimiting of rewards as seen some unwanted spikes.
	case rewardsClient:
		fullGrpcMethodNameTransformed = fullGrpcMethodNameTransformed + "_" + "rewards_client"
	// separate ratelimiting of txn categorizer batch categories client as seen unwanted spikes.
	case txnCategorizerBatchGetCategoriesClient:
		fullGrpcMethodNameTransformed = fullGrpcMethodNameTransformed + "_" + "batchgetcategories"
	case riskTxnReviewClient:
		fullGrpcMethodNameTransformed = fullGrpcMethodNameTransformed + "_" + "risk_txn_review_client"
	default:
		// do nothing
	}

	logger.Debug(ctx, "order rate-limiter debug logs", zap.String("fullGrpcMethodNameTransformed", fullGrpcMethodNameTransformed))

	return fullGrpcMethodNameTransformed, nil
}
