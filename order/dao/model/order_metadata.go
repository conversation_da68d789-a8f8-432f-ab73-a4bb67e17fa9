package model

import (
	"time"

	orderPb "github.com/epifi/gamma/api/order"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
)

type OrderMetadata struct {
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`
	// OrderId for which Metadata is stored
	OrderId string
	// Metadata stored for given OrderId. E.g. qrData
	Metadata *orderPb.Metadata
	// type of metadata stored
	MetadataType orderPb.MetadataType
	// time of creation
	CreatedAt time.Time
	// last updated time
	UpdatedAt time.Time
	// Deletion time
	DeletedAt gorm.DeletedAt
}

func (OrderMetadata) TableName() string {
	return "order_metadata"
}

func (u *OrderMetadata) ConvertToOrderMetadataProto() *orderPb.OrderMetadata {
	if u == nil {
		return nil
	}
	return &orderPb.OrderMetadata{
		Id:           u.Id,
		OrderId:      u.OrderId,
		Metadata:     u.Metadata,
		MetadataType: u.MetadataType,
		CreatedAt:    timestamppb.New(u.CreatedAt),
		UpdatedAt:    timestamppb.New(u.UpdatedAt),
	}
}

func ConvertToOrderMetadataModel(orderMetadataProtoRequest *orderPb.OrderMetadata) *OrderMetadata {
	return &OrderMetadata{
		OrderId:      orderMetadataProtoRequest.GetOrderId(),
		Metadata:     orderMetadataProtoRequest.GetMetadata(),
		MetadataType: orderMetadataProtoRequest.GetMetadataType(),
	}
}
