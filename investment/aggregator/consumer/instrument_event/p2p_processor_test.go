package instrument_event

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/gamma/api/investment"
	consumerPb "github.com/epifi/gamma/api/investment/aggregator/consumer"
	"github.com/epifi/gamma/api/investment/aggregator/events"
	p2pPb "github.com/epifi/gamma/api/p2pinvestment"
	mock_queue "github.com/epifi/be-common/pkg/queue/mocks"
)

func TestP2P_Publisher(t *testing.T) {
	type args struct {
		ctx context.Context
		req *consumerPb.ProcessInvestmentEventRequest
	}
	ctx := context.Background()

	tests := []struct {
		name           string
		args           args
		setupMockCalls func(mockPublisher *mock_queue.MockPublisher)
		wantErr        bool
	}{
		{
			name: "test p2p investment done event",
			args: args{
				ctx: ctx,
				req: &consumerPb.ProcessInvestmentEventRequest{
					InstrumentType: investment.InvestmentInstrumentType_P2P_JUMP,
					InstrumentData: &consumerPb.ProcessInvestmentEventRequest_P2PInvestmentData{P2PInvestmentData: &consumerPb.P2PInvestmentData{ActorId: "A1", InvestmentTransactionId: "T1", Status: p2pPb.InvestmentTransactionStatus_SUCCESS}},
				},
			},
			setupMockCalls: func(mockPublisher *mock_queue.MockPublisher) {
				mockPublisher.EXPECT().Publish(gomock.Any(), &events.InvestmentEvent{
					ActorId:   "A1",
					EventType: events.EventType_EventType_ANY_INVESTMENT_DONE,
					EventInfo: &events.InvestmentEvent_P2PInvestmentInfo{
						P2PInvestmentInfo: &events.P2PInvestmentInfo{Id: "T1", Status: p2pPb.InvestmentTransactionStatus_SUCCESS},
					},
					InstrumentType: investment.InvestmentInstrumentType_P2P_JUMP,
				}).Return("1", nil)
			},
		},
		{
			name: "test p2p not supported event",
			args: args{
				ctx: ctx,
				req: &consumerPb.ProcessInvestmentEventRequest{
					InstrumentType: investment.InvestmentInstrumentType_US_STOCKS,
					InstrumentData: &consumerPb.ProcessInvestmentEventRequest_SdInvestmentData{},
				},
			},
			setupMockCalls: func(mockPublisher *mock_queue.MockPublisher) {},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockPublisher := mock_queue.NewMockPublisher(ctrl)
			p2pEventPublisher := NewP2PEventProcessor(mockPublisher)
			tt.setupMockCalls(mockPublisher)
			err := p2pEventPublisher.PublishEvent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PublishEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
