package orderETAProcessor

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/rpc/code"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	etaHandlerPb "github.com/epifi/gamma/api/investment/mutualfund/eta_handler"
	mfOrderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/investment/config/genconf"
	mfDao "github.com/epifi/gamma/investment/mutualfund/dao"
	wtypes "github.com/epifi/gamma/investment/wire/types"
	"github.com/epifi/gamma/pkg/investment"
)

type MutualFundsOrderEtaProcessor struct {
	cfg                    *genconf.Config
	orderDao               mfDao.OrderDao
	mfDao                  mfDao.MutualFundDao
	watsonClient           watsonPb.WatsonClient
	paymentHandler         phPb.PaymentHandlerClient
	orderETADelayPublisher wtypes.OrderETADelayPublisher
}

func NewMutualFundsOrderEtaProcessor(
	cfg *genconf.Config,
	orderDao mfDao.OrderDao,
	mfDao mfDao.MutualFundDao,
	watsonClient watsonPb.WatsonClient,
	paymentHandler phPb.PaymentHandlerClient,
	orderETADelayPublisher wtypes.OrderETADelayPublisher,
) *MutualFundsOrderEtaProcessor {
	return &MutualFundsOrderEtaProcessor{
		cfg:                    cfg,
		orderDao:               orderDao,
		mfDao:                  mfDao,
		watsonClient:           watsonClient,
		paymentHandler:         paymentHandler,
		orderETADelayPublisher: orderETADelayPublisher,
	}
}

func (s *MutualFundsOrderEtaProcessor) ProcessETA(ctx context.Context, req *etaHandlerPb.ProcessOrderETARequest) (*etaHandlerPb.ProcessOrderETAResponse, error) {
	mfOrder := req.GetMutualFundOrder()
	actorId := mfOrder.GetActorId()
	newMfOrder, err := s.orderDao.GetById(ctx, mfOrder.GetId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return permanentFailureResp()
		}
		logger.Error(ctx, "error in GetById", zap.Error(err), zap.String(logger.ORDER_ID, newMfOrder.GetId()), zap.String(logger.ACTOR_ID_V2, actorId))
		return transientFailureResp()
	}
	etaDate, etaErr := s.getEta(ctx, mfOrder)
	if etaErr != nil {
		logger.Error(ctx, "error in getEta", zap.Error(etaErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, newMfOrder.GetId()))
		return transientFailureResp()
	}

	isIncidentPresent, incidentErr := s.isIncidentPresent(ctx, mfOrder)
	if incidentErr != nil {
		logger.Error(ctx, "error in getIncident", zap.Error(incidentErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, newMfOrder.GetId()))
		return transientFailureResp()
	}

	switch newMfOrder.GetOrderStatus() {
	case mfOrderPb.OrderStatus_CONFIRMED_BY_RTA, mfOrderPb.OrderStatus_FAILURE, mfOrderPb.OrderStatus_IN_SETTLEMENT, mfOrderPb.OrderStatus_CREATION_ON_HOLD:
		return s.handleTerminalOrderStates(ctx, mfOrder, isIncidentPresent)
	default:
		return s.handleNonTerminalOrderStates(ctx, mfOrder, isIncidentPresent, etaDate)
	}

}

// isIncidentPresent returns true if the incident is present in watson.
func (p *MutualFundsOrderEtaProcessor) isIncidentPresent(ctx context.Context, order *mfOrderPb.Order) (bool, error) {
	resp, err := p.watsonClient.GetIncidentsForClient(ctx, &watsonPb.GetIncidentsForClientRequest{
		IncidentFilter: &watsonPb.IncidentFiltersForClient{
			ActorId:         order.GetActorId(),
			Client:          types.ServiceName_INVESTMENT_SERVICE,
			ResponseLimit:   1,
			ClientRequestId: getClientRequestId(order),
			IncidentCategory: &watsonPb.IncidentCategory{
				ProductCategory:        types.ProductCategory_PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS,
				ProductCategoryDetails: types.ProductCategoryDetails_PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL,
				SubCategory:            types.SubCategory_SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED,
			},
			IncidentStates: []watsonPb.IncidentState{
				watsonPb.IncidentState_INCIDENT_STATE_DROPPED,
				watsonPb.IncidentState_INCIDENT_STATE_LOGGED_IN_DB,
				watsonPb.IncidentState_INCIDENT_STATE_TICKET_CREATED,
			},
		},
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return false, nil
		}
		logger.Error(ctx, "error while getting watson incidents for actor", zap.Error(grpcErr), zap.String(logger.ACTOR_ID_V2, order.GetActorId()))
		return false, grpcErr
	}

	return true, nil
}

// getEta returns the ETA for the mf order
func (s *MutualFundsOrderEtaProcessor) getEta(ctx context.Context, mfOrder *mfOrderPb.Order) (time.Time, error) {
	if s.cfg.MFOrderEtaHandlerTestConfig().IsTestEnabled() && lo.Contains(s.cfg.MFOrderEtaHandlerTestConfig().MFAutoIdEnabledActors().ToStringArray(), mfOrder.GetActorId()) {
		return time.Now().Add(-1 * s.cfg.MFOrderEtaHandlerTestConfig().CustomPublishDelay()), nil
	}
	actorId := mfOrder.GetActorId()
	mf, err := s.mfDao.GetById(ctx, mfOrder.GetMutualFundId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return time.Time{}, err
		}
		logger.Error(ctx, "error in GetById", zap.Error(err), zap.String(logger.ORDER_ID, mfOrder.GetId()), zap.String(logger.ACTOR_ID_V2, actorId))
		return time.Time{}, err
	}
	paymentRes, paymentErr := s.paymentHandler.GetPaymentDetails(ctx, &phPb.GetPaymentDetailsRequest{
		OrderId:     mfOrder.GetId(),
		PaymentMode: phPb.PaymentMode(mfOrder.GetPaymentMode()),
	})
	if grpcErr := epifigrpc.RPCError(paymentRes, paymentErr); grpcErr != nil {
		if paymentRes.GetStatus().GetCode() == uint32(code.Code_NOT_FOUND) {
			etaDate, etaErr := investment.GetETADate(investment.ETAParams{
				PendingOrder:  mfOrder,
				AssetClass:    mf.GetAssetClass(),
				PaymentStatus: phPb.PaymentStatus_PAYMENT_STATUS_UNSPECIFIED,
				PaymentTime:   nil,
				CategoryName:  mf.GetCategoryName(),
			})
			if err != nil {
				logger.Error(ctx, "Error in GetETADate", zap.Error(etaErr), zap.String(logger.ACTOR_ID_V2, actorId))
				return time.Time{}, err
			}
			return etaDate, nil
		}
		logger.Error(ctx, "Error in GetPaymentDetails", zap.Error(grpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return time.Time{}, grpcErr
	}
	etaDate, err := investment.GetETADate(investment.ETAParams{
		PendingOrder:  mfOrder,
		AssetClass:    mf.GetAssetClass(),
		PaymentStatus: paymentRes.GetPaymentStatus(),
		PaymentTime:   paymentRes.GetTransactionTime(),
		CategoryName:  mf.GetCategoryName(),
	})
	if err != nil {
		logger.Error(ctx, "Error in GetETADate", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return time.Time{}, err
	}
	return etaDate, nil
}

// createIncident creates the incident on watson
func (s *MutualFundsOrderEtaProcessor) createIncident(ctx context.Context, order *mfOrderPb.Order) error {
	resp, err := s.watsonClient.IngestEvent(ctx, &watsonPb.IngestEventRequest{
		EventType:       watsonPb.EventType_EVENT_TYPE_INCIDENT_REPORT,
		Client:          types.ServiceName_INVESTMENT_SERVICE,
		ActorId:         order.GetActorId(),
		ClientRequestId: getClientRequestId(order),
		IncidentCategory: &watsonPb.IncidentCategory{
			ProductCategory:        types.ProductCategory_PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS,
			ProductCategoryDetails: types.ProductCategoryDetails_PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL,
			SubCategory:            types.SubCategory_SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED,
		},
		IdentifiedAt: timestamp.Now(),
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		return fmt.Errorf("error in IngestEvent: %w", grpcErr)
	}
	return nil
}

// resolveIncident resolves the incident on watson
func (s *MutualFundsOrderEtaProcessor) resolveIncident(ctx context.Context, order *mfOrderPb.Order) error {
	resp, err := s.watsonClient.IngestEvent(ctx, &watsonPb.IngestEventRequest{
		EventType:       watsonPb.EventType_EVENT_TYPE_INCIDENT_RESOLUTION,
		Client:          types.ServiceName_INVESTMENT_SERVICE,
		ActorId:         order.GetActorId(),
		ClientRequestId: getClientRequestId(order),
		IncidentCategory: &watsonPb.IncidentCategory{
			ProductCategory:        types.ProductCategory_PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS,
			ProductCategoryDetails: types.ProductCategoryDetails_PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL,
			SubCategory:            types.SubCategory_SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED,
		},
		IdentifiedAt: timestamp.Now(),
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		return fmt.Errorf("error in IngestEvent: %w", grpcErr)
	}
	return nil
}

// handleTerminalOrderStates determines the action to be taken for the mf order states that are terminal,
// for eg: resolving the incidents on watson for failure, confirmed_by_rta
func (s *MutualFundsOrderEtaProcessor) handleTerminalOrderStates(ctx context.Context, mfOrder *mfOrderPb.Order, isIncidentPresent bool) (*etaHandlerPb.ProcessOrderETAResponse, error) {
	actorId := mfOrder.GetActorId()
	logger.Info(ctx, "Order status is in terminal state", zap.String(logger.ORDER_ID, mfOrder.GetId()), zap.String(logger.ACTOR_ID_V2, actorId))
	if isIncidentPresent {
		resolveErr := s.resolveIncident(ctx, mfOrder)
		if resolveErr != nil {
			logger.Error(ctx, "error in resolveIncident", zap.Error(resolveErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, mfOrder.GetId()))
			return transientFailureResp()
		}
		return successResp()
	} else {
		logger.Info(ctx, "incident not present in watson", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, mfOrder.GetId()))
		return successResp()
	}

}

// handleNonTerminalOrderStates determines the action to be taken for the mf order states that are non-terminal,
// for eg: creating the incidents on watson.
func (s *MutualFundsOrderEtaProcessor) handleNonTerminalOrderStates(ctx context.Context, mfOrder *mfOrderPb.Order, isIncidentPresent bool, etaDate time.Time) (*etaHandlerPb.ProcessOrderETAResponse, error) {
	actorId := mfOrder.GetActorId()

	switch {
	case shouldNewTicketBeCreated(etaDate) && !isIncidentPresent:
		createIncidentErr := s.createIncident(ctx, mfOrder)
		if createIncidentErr != nil {
			logger.Error(ctx, "error in createIncident", zap.Error(createIncidentErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, mfOrder.GetId()))
			return transientFailureResp()
		}
		return successResp()
	case shouldNewTicketBeCreated(etaDate) && isIncidentPresent:
		return s.republishEvent(ctx, mfOrder, etaDate)
	}

	if shouldEscalateTicket(etaDate) {
		logger.Info(ctx, "ticket needs to be escalated", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, mfOrder.GetId()))
		// TODO (Rishab) escalate ticket logic is currently not supported by watson
		return successResp()
	}
	return successResp()
}

// considering the MF order id as the unique client request ID
func getClientRequestId(order *mfOrderPb.Order) string {
	return order.GetId()
}

// shouldNewTicketBeCreated verifies if the ticket should be created or not.
// Currently, we are creating a ticket if the ETA has been delayed by 2 days
func shouldNewTicketBeCreated(etaDate time.Time) bool {
	return time.Now().After(etaDate) && time.Now().Before(etaDate.Add(2*24*time.Hour))
}

// delayForRepublishingEvent calculates the delay for republishing the event
func delayForRepublishingEvent(etaDate time.Time) time.Duration {
	return time.Until(etaDate.Add(2 * 24 * time.Hour))
}

// shouldEscalateTicket returns true if the ticket has to be escalated.
func shouldEscalateTicket(etaDate time.Time) bool {
	return time.Now().After(etaDate.Add(2 * 24 * time.Hour))
}

// republishEvent republishes the mf order ETA event.
func (s *MutualFundsOrderEtaProcessor) republishEvent(ctx context.Context, mfOrder *mfOrderPb.Order, etaDate time.Time) (*etaHandlerPb.ProcessOrderETAResponse, error) {
	actorId := mfOrder.GetActorId()
	_, publishErr := s.orderETADelayPublisher.PublishWithDelay(ctx, &etaHandlerPb.ProcessOrderETARequest{
		ETARequestType: &etaHandlerPb.ProcessOrderETARequest_MutualFundOrder{
			MutualFundOrder: mfOrder,
		},
	}, delayForRepublishingEvent(etaDate))
	if publishErr != nil {
		logger.Error(ctx, "error in republishing ProcessOrderETARequest", zap.Error(publishErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.ORDER_ID, mfOrder.GetId()))
		return transientFailureResp()
	}
	return successResp()
}
