package notifications

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/queue"
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	oncev2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	pkgQueue "github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/comms"
	commsPb "github.com/epifi/gamma/api/comms"
	dePb "github.com/epifi/gamma/api/dynamic_elements"
	"github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	"github.com/epifi/gamma/api/frontend/investment/mutualfund/clientstates"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	"github.com/epifi/gamma/api/investment/mutualfund/notifications"
	notificationsPb "github.com/epifi/gamma/api/investment/mutualfund/notifications"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	omsPb "github.com/epifi/gamma/api/order"
	userPb "github.com/epifi/gamma/api/user"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	catDao "github.com/epifi/gamma/investment/mutualfund/catalog/dao"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	notiDao "github.com/epifi/gamma/investment/mutualfund/notifications/dao"
	omsFact "github.com/epifi/gamma/investment/mutualfund/notifications/oms_order_update_processor"
	"github.com/epifi/gamma/investment/mutualfund/notifier"
	"github.com/epifi/gamma/pkg/investment"
	invPkg "github.com/epifi/gamma/pkg/investment"
)

// type aliases for proper wiring

type OrderDelayedNotificationDelayPublisher pkgQueue.DelayPublisher
type DeferredNotificationDelayPublisher pkgQueue.DelayPublisher

type Service struct {
	notificationsPb.UnimplementedNotificationsServer
	cfg                               *genConf.Config
	doOnce                            oncev2.DoOnce
	commsClient                       commsPb.CommsClient
	phClient                          phPb.PaymentHandlerClient
	userClient                        userPb.UsersClient
	actorClient                       actorPb.ActorClient
	orderDao                          dao.OrderDao
	mfDao                             dao.MutualFundDao
	orderDelayedNotificationPublisher OrderDelayedNotificationDelayPublisher
	deferredNotificationPublisher     DeferredNotificationDelayPublisher
	dynamicElementGetterFactory       IDynamicElementsGetterFactory
	watchListFundMappingDao           catDao.WatchListFundMappingDao
	catalogClient                     catalogPb.CatalogManagerClient
	idempotentTxnExecutor             storagev2.IdempotentTxnExecutor
	actorNotificationDao              notiDao.ActorNotificationDao
	processOmsOrderUpdateFactory      *omsFact.ProcessOmsOrderUpdateFactory
}

func NewService(
	cfg *genConf.Config,
	doOnce oncev2.DoOnce,
	commsClient commsPb.CommsClient,
	phClient phPb.PaymentHandlerClient,
	actorClient actorPb.ActorClient,
	orderDao dao.OrderDao,
	mfDao dao.MutualFundDao,
	orderDelayedNotificationPublisher OrderDelayedNotificationDelayPublisher,
	deferredNotificationPublisher DeferredNotificationDelayPublisher,
	dynamicElementGetterFactory *DynamicElementsGetterFactory,
	watchListFundMappingDao catDao.WatchListFundMappingDao,
	catalogClient catalogPb.CatalogManagerClient,
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor,
	actorNotificationDao notiDao.ActorNotificationDao,
	processOmsOrderUpdateFactory *omsFact.ProcessOmsOrderUpdateFactory,
	userClient userPb.UsersClient,
) *Service {
	return &Service{
		cfg:                               cfg,
		doOnce:                            doOnce,
		commsClient:                       commsClient,
		phClient:                          phClient,
		actorClient:                       actorClient,
		orderDao:                          orderDao,
		userClient:                        userClient,
		mfDao:                             mfDao,
		orderDelayedNotificationPublisher: orderDelayedNotificationPublisher,
		deferredNotificationPublisher:     deferredNotificationPublisher,
		dynamicElementGetterFactory:       dynamicElementGetterFactory,
		watchListFundMappingDao:           watchListFundMappingDao,
		catalogClient:                     catalogClient,
		idempotentTxnExecutor:             idempotentTxnExecutor,
		actorNotificationDao:              actorNotificationDao,
		processOmsOrderUpdateFactory:      processOmsOrderUpdateFactory,
	}
}

const (
	DelayedOrderNotificationScheduledAtHour = 7
	MFWatchlistPNTaskString                 = "MF_WATCHLIST_"
	MFDropoffPNTaskString                   = "MF_DROPOFF_"
	MFOTIReminderTaskString                 = "MF_OTI_REMINDER_"
	DB_TRANSACTION_RETRIES                  = 3
)

//		SendDeferredNotification sends a delayed push notification with request type as
//		*SendDeferredNotificationRequest_CommsReq or
//		*SendDeferredNotificationRequest_WatchlistPnMsg
//	 	*SendDeferredNotificationRequest_InvestOneTimePnMsg
func (s *Service) SendDeferredNotification(ctx context.Context, req *notificationsPb.SendDeferredNotificationRequest) (*notificationsPb.SendDeferredNotificationResponse, error) {
	switch req.SendDeferredNotificationRequestType.(type) {
	case *notificationsPb.SendDeferredNotificationRequest_CommsReq:
		{
			res, err := s.commsClient.SendMessage(ctx, req.GetCommsReq())
			if err = epifigrpc.RPCError(res, err); err != nil {
				if res.GetStatus().IsRecordNotFound() || res.GetStatus().IsFailedPrecondition() {
					return s.sendDeferredNotificationSuccessResponse(), nil
				}
				logger.Error(ctx, "unable to send deferred notification", zap.Error(err))
				return s.sendDeferredNotificationTransientFailureResponse(), nil
			}
			logger.Debug(ctx, "deferred notification triggered successfully", zap.String(logger.QUEUE_MESSAGE_ID, res.GetMessageId()))
			return s.sendDeferredNotificationSuccessResponse(), nil
		}
	case *notificationsPb.SendDeferredNotificationRequest_WatchlistPnMsg:
		return s.sendPushNotificationForWatchlistedMFConsumer(ctx, req)

	case *notificationsPb.SendDeferredNotificationRequest_DropoffPnMsg:
		return s.sendPushNotificationForMFDropoffConsumer(ctx, req)

	case *notificationsPb.SendDeferredNotificationRequest_InvestOneTimeReminderPnMsg:
		return s.sendPushNotificationForMFOTIReminder(ctx, req)
	default:
		// TODO: (Rishab) To be removed after one release
		res, err := s.commsClient.SendMessage(ctx, req.GetMsg())
		if err = epifigrpc.RPCError(res, err); err != nil {
			if res.GetStatus().IsRecordNotFound() {
				logger.Info(ctx, "no device found for sending message")
				return s.sendDeferredNotificationSuccessResponse(), nil
			}
			logger.Error(ctx, "unable to send deferred notification", zap.Error(err))
			return &notificationsPb.SendDeferredNotificationResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
		}
	}
	return s.sendDeferredNotificationSuccessResponse(), nil
}

func (s *Service) SendDelayedOrderNotification(ctx context.Context, req *notificationsPb.SendDelayedOrderNotificationRequest) (*notificationsPb.SendDelayedOrderNotificationResponse, error) {
	logger.Debug(ctx, "received SendDelayedOrderNotification msg", zap.String(logger.ORDER_ID, req.GetOrderId()))

	order, orderDbErr := s.orderDao.GetById(ctx, req.GetOrderId())
	if orderDbErr != nil {
		logger.Error(ctx, "unable to get order by id", zap.Error(orderDbErr), zap.String(logger.ORDER_ID, req.GetOrderId()))
		return &notificationsPb.SendDelayedOrderNotificationResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}

	// get eta
	paymentRes, paymentErr := s.phClient.GetPaymentDetails(ctx, &phPb.GetPaymentDetailsRequest{
		OrderId:     order.GetId(),
		PaymentMode: phPb.PaymentMode(order.GetPaymentMode()),
	})
	if err := epifigrpc.RPCError(paymentRes, paymentErr); err != nil {
		if !rpc.StatusFromError(err).IsRecordNotFound() {
			logger.Error(ctx, "unable to get payment details for order", zap.Error(err), zap.String(logger.ORDER_ID, order.GetId()))
			return &notificationsPb.SendDelayedOrderNotificationResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
		}
	}
	mf, mfDbErr := s.mfDao.GetById(ctx, order.GetMutualFundId())
	if mfDbErr != nil {
		logger.Error(ctx, "unable to get fund by id", zap.Error(mfDbErr), zap.String(logger.MF_ID, order.GetMutualFundId()))
		return &notificationsPb.SendDelayedOrderNotificationResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	eta, etaErr := investment.GetETADate(investment.ETAParams{
		PendingOrder:  order,
		AssetClass:    mf.GetAssetClass(),
		PaymentStatus: paymentRes.GetPaymentStatus(),
		PaymentTime:   paymentRes.GetTransactionTime(),
		CategoryName:  mf.GetCategoryName(),
	})
	if etaErr != nil {
		logger.Error(ctx, "unable to get eta", zap.Error(etaErr))
		return &notificationsPb.SendDelayedOrderNotificationResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}

	processErr := s.sendSkipOrDelayNotification(ctx, order, eta, mf)
	if processErr != nil {
		return &notificationsPb.SendDelayedOrderNotificationResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}, nil
	}
	return &notificationsPb.SendDelayedOrderNotificationResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}, nil
}

func (s *Service) sendSkipOrDelayNotification(ctx context.Context, order *orderPb.Order, eta time.Time, mf *mfPb.MutualFund) error {
	// order stays in creation on hold for long time period only in case of some user input error
	// these orders are not shown to users, hence no need to notify user
	if order.GetOrderStatus() == orderPb.OrderStatus_CREATION_ON_HOLD {
		if time.Since(order.GetCreatedAt().AsTime()).Hours() < 1 {
			notificationErr := s.delayNotificationFurther(ctx, eta, order.GetId())
			if notificationErr != nil {
				logger.Error(ctx, "error delaying notification further", zap.Error(notificationErr))
				return notificationErr
			}
		} else {
			logger.Info(ctx, "order in creation on hold for long time, skipping sending notification", zap.String(logger.ORDER_STATUS, order.GetOrderStatus().String()))
			return nil
		}
	}

	// terminal state processing
	if order.GetOrderStatus() == orderPb.OrderStatus_SETTLED ||
		order.GetOrderStatus() == orderPb.OrderStatus_CONFIRMED_BY_RTA ||
		order.GetOrderStatus() == orderPb.OrderStatus_FAILURE ||
		order.GetOrderStatus() == orderPb.OrderStatus_EXPIRED ||
		order.GetOrderStatus() == orderPb.OrderStatus_MANUAL_INTERVENTION {
		logger.Info(ctx, "order has reached terminal state, skipping sending notification", zap.String(logger.ORDER_STATUS, order.GetOrderStatus().String()))
		return nil
	}

	// if eta is today or already expired, schedule notification for next morning of eta; else push to delay queue
	if (eta.Year() == time.Now().Year() && eta.Month() == time.Now().Month() && eta.Day() == time.Now().Day()) ||
		time.Until(eta) < 0 {
		notificationErr := s.scheduleDeferredNotificationForDelayedOrder(ctx, order, mf, eta)
		if notificationErr != nil {
			logger.Error(ctx, "error scheduling deferred notification for delayed order", zap.Error(notificationErr))
		}
	} else {
		notificationErr := s.delayNotificationFurther(ctx, eta, order.GetId())
		if notificationErr != nil {
			logger.Error(ctx, "error delaying notification further", zap.Error(notificationErr))
		}
	}
	return nil
}

func (s *Service) scheduleDeferredNotificationForDelayedOrder(ctx context.Context, order *orderPb.Order, mf *mfPb.MutualFund, eta time.Time) error {
	actorRes, actorErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: order.GetActorId()})
	if err := epifigrpc.RPCError(actorRes, actorErr); err != nil {
		logger.Error(ctx, "unable to get actor for mf order by id", zap.String(logger.ACTOR_ID_V2, order.GetActorId()), zap.Error(err))
		return err
	}
	userId := actorRes.GetActor().GetEntityId()

	notificationBody := fmt.Sprintf(s.cfg.MutualFundsNotificationParams().OrderDelayed().Body(), mf.GetNameData().GetShortName())
	year, month, day := eta.Date()
	notificationScheduledAt := time.Date(year, month, day+1, DelayedOrderNotificationScheduledAtHour, 0, 0, 0, eta.Location())
	notifierErr := notifier.SendOrderDelayedNotification(ctx, s.cfg, s.deferredNotificationPublisher,
		s.doOnce, userId, notificationBody, mf.GetId(), time.Until(notificationScheduledAt))
	if notifierErr != nil {
		logger.Error(ctx, "error notifying user", zap.Error(notifierErr))
		return notifierErr
	}
	return nil
}

// calculates duration to delay the notification for, and delays the notification for that long
func (s *Service) delayNotificationFurther(ctx context.Context, eta time.Time, orderId string) error {
	var durationUntilNextOrderDelayCheck time.Duration
	if time.Until(eta) < invPkg.DurationUntilNextEtaCheck {
		durationUntilNextOrderDelayCheck = time.Until(eta)
	} else {
		durationUntilNextOrderDelayCheck = invPkg.DurationUntilNextEtaCheck
	}
	_, publishErr := s.orderDelayedNotificationPublisher.PublishWithDelay(ctx, &notificationsPb.SendDelayedOrderNotificationRequest{OrderId: orderId}, durationUntilNextOrderDelayCheck)
	if publishErr != nil {
		logger.Error(ctx, "error republishing notification packet for further delay", zap.Error(publishErr), zap.String(logger.ORDER_ID, orderId))
		return publishErr
	}
	return nil
}

// nolint:funlen
func (s *Service) ProcessOmsOrderUpdate(ctx context.Context, request *omsPb.OrderUpdate) (*notifications.ProcessOmsOrderUpdateResponse, error) {
	omsOrderUpdateNotificationProcessor, fErr := s.processOmsOrderUpdateFactory.GetOMSNotificationProcessor(request.GetOrderWithTransactions())
	if fErr != nil {
		if fErr == omsFact.ERR_PROCESSOR_NOT_DEFINED {
			return s.returnSuccess()
		}
		logger.Error(ctx, "error in GetOMSNotificationProcessor", zap.Error(fErr))
		return s.returnPermanentFailure()
	}
	actorId := request.GetOrderWithTransactions().GetOrder().GetToActorId()
	omsOrderUpdateNotificationExecutor, notifyErr := omsOrderUpdateNotificationProcessor.NotifyUser(ctx, request)
	if fErr != nil {
		logger.Error(ctx, "error in NotifyUser", zap.Error(notifyErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return s.returnPermanentFailure()
	}
	return omsOrderUpdateNotificationExecutor, nil
}

// validates the request
// checks if the workflow is OrderWorkflow_ADD_FUNDS || OrderWorkflow_ADD_FUNDS_COLLECT
// checks if the order status is OrderStatus_PAID
func (s *Service) validateRequest(transactions *omsPb.OrderWithTransactions) (bool, error) {
	if transactions == nil {
		return false, fmt.Errorf("OrderWithTransactions is nil")
	}
	if transactions.Order == nil {
		return false, fmt.Errorf("orderWithTransactions.Order is nil")
	}
	if !transactions.GetOrder().IsAddFundsOrder() {
		return false, nil
	}
	if transactions.GetOrder().GetStatus() != omsPb.OrderStatus_PAID {
		return false, nil
	}
	return true, nil
}

func (s *Service) returnPermanentFailure() (*notificationsPb.ProcessOmsOrderUpdateResponse, error) {
	return &notificationsPb.ProcessOmsOrderUpdateResponse{ResponseHeader: &queue.ConsumerResponseHeader{
		Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
	}}, nil
}

func (s *Service) returnSuccess() (*notificationsPb.ProcessOmsOrderUpdateResponse, error) {
	return &notificationsPb.ProcessOmsOrderUpdateResponse{ResponseHeader: &queue.ConsumerResponseHeader{
		Status: queue.MessageConsumptionStatus_SUCCESS,
	}}, nil
}

func (s *Service) returnTransientFailure() (*notificationsPb.ProcessOmsOrderUpdateResponse, error) {
	return &notificationsPb.ProcessOmsOrderUpdateResponse{ResponseHeader: &queue.ConsumerResponseHeader{
		Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
	}}, nil
}

func (s *Service) FetchDynamicElements(ctx context.Context, req *dePb.FetchDynamicElementsRequest) (*dePb.FetchDynamicElementsResponse, error) {
	dynamicElementGetter, err := s.dynamicElementGetterFactory.GetDynamicElementsGetter(req.GetClientContext())
	if err != nil {
		logger.Error(ctx, "unable to get dynamic element getter",
			zap.String(logger.SCREEN, req.GetClientContext().GetScreenName().String()),
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.Error(err))
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	dynamicElements, err := dynamicElementGetter.GetDynamicElements(ctx, req.GetActorId(), req.GetClientContext().GetAppPlatform(), req.GetClientContext().GetAppVersion())
	if err != nil {
		logger.Error(ctx, "unable to get dynamic elements",
			zap.String(logger.SCREEN, req.GetClientContext().GetScreenName().String()),
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.Error(err))
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &dePb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: dynamicElements,
	}, nil
}

func (s *Service) DynamicElementCallback(context.Context, *dePb.DynamicElementCallbackRequest) (*dePb.DynamicElementCallbackResponse, error) {
	return &dePb.DynamicElementCallbackResponse{Status: rpc.StatusOk()}, nil
}

// prepareSendMessageReq prepares the message request to send the Push notification
func (s *Service) prepareSendMessageReq(ctx context.Context, actorId string, notificationType catalog.WatchlistMFPushNotificationType, isPriceDropped bool, mf *mfPb.MutualFund) (*comms.SendMessageRequest, error) {
	actorRes, actorErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if err := epifigrpc.RPCError(actorRes, actorErr); err != nil {
		logger.Error(ctx, "unable to get actor	 by id", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}
	userId := actorRes.GetActor().GetEntityId()
	var (
		notificationTemplate *genConf.NotificationParamsTemplate
		campaignName         comms.CampaignName
		deeplink             *deeplink.Deeplink
		body                 string
	)
	switch {
	case notificationType == catalog.WatchlistMFPushNotificationType_ONE_DAY:
		notificationTemplate = s.cfg.MutualFundsNotificationParams().WatchlistedNotInvested24hrs()
		campaignName = comms.CampaignName_CAMPAIGN_NAME_MF_WATCHLIST_BUY_24HRS
	case notificationType == catalog.WatchlistMFPushNotificationType_THREE_DAY:
		if isPriceDropped {
			notificationTemplate = s.cfg.MutualFundsNotificationParams().WatchlistBuyPriceDrop3Days()
			campaignName = comms.CampaignName_CAMPAIGN_NAME_MF_WATCHLIST_BUYPRICEDROP_3DAYS
		} else {
			notificationTemplate = s.cfg.MutualFundsNotificationParams().WatchlistBuyGeneral3Days()
			campaignName = comms.CampaignName_CAMPAIGN_NAME_MF_WATCHLIST_BUYGENERAL_3DAYS
		}
	}
	body = fmt.Sprintf(notificationTemplate.Body(), mf.GetNameData().GetShortName())
	deeplink = getDeeplinkForWatchlistPN(mf.GetId())
	notificationReq := notifier.CreateNotificationRequest(userId, notificationTemplate, body, campaignName, deeplink)
	return notificationReq, nil
}

func isPriceDropped(currentNav *moneyPb.Money, oldNav *moneyPb.Money) bool {
	if money.Compare(currentNav, oldNav) == -1 {
		return true
	} else {
		return false
	}
}

// getDeeplinkForWatchlistPN returns deeplink to mf details screen with given mfId
func getDeeplinkForWatchlistPN(mfId string) *deeplink.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_MUTUAL_FUND_DETAILS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_MutualFundDetailsScreenOptions{
			MutualFundDetailsScreenOptions: &deeplinkPb.MutualFundDetailsScreenOptions{
				MutualFundId: mfId,
				EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_DIGEST,
			},
		},
	}
}

// getWatchlistPNTaskString returns taskString to do the task once in a day
func getWatchlistPNTaskString(actorId string) string {
	curTime := time.Now()
	return MFWatchlistPNTaskString + actorId + "_" + datetime.DateToDDMMYYYY(datetime.TimeToDateInLoc(curTime, nil))
}

func (s *Service) sendDeferredNotificationTransientFailureResponse() *notificationsPb.SendDeferredNotificationResponse {
	return &notificationsPb.SendDeferredNotificationResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}
}

func (s *Service) sendDeferredNotificationSuccessResponse() *notificationsPb.SendDeferredNotificationResponse {
	return &notificationsPb.SendDeferredNotificationResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}
}

func (s *Service) sendDeferredNotificationPermanentFailureResponse() *notificationsPb.SendDeferredNotificationResponse {
	return &notificationsPb.SendDeferredNotificationResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE}}
}

// nolint:funlen
func (s *Service) sendPushNotificationForWatchlistedMFConsumer(ctx context.Context, req *notificationsPb.SendDeferredNotificationRequest) (*notificationsPb.SendDeferredNotificationResponse, error) {

	enableSendWatchlistPNConfig := s.cfg.SendWatchlistPNConfig().EnableSendWatchlistPNConfig()
	if !enableSendWatchlistPNConfig {
		return s.sendDeferredNotificationSuccessResponse(), nil
	}

	watchlistPN := req.GetWatchlistPnMsg()
	actorId := watchlistPN.GetActorId()
	mfId := watchlistPN.GetMfId()
	mfIds := []string{mfId}
	oldNav := watchlistPN.GetNav()
	// check if fund exist in watchlist
	_, err := s.watchListFundMappingDao.GetFundsInWatchListsForActor(ctx, actorId, mfIds)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "no funds in watchlist for this actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.MF_ID, mfId))
			return s.sendDeferredNotificationSuccessResponse(), nil
		}
		logger.Error(ctx, "unable to fetch watchlisted funds for actor", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.MF_ID, mfId))
		return s.sendDeferredNotificationTransientFailureResponse(), nil
	}
	// check if actor has invested
	res, getInvErr := s.catalogClient.GetInvestmentSummaryForMfs(ctx, &catalogPb.GetInvestmentSummaryForMfsRequest{ActorId: actorId, MfIds: []string{mfId}})
	if getInvErr != nil {
		logger.Error(ctx, "unable to check if actor has invested in fund", zap.Error(getInvErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.MF_ID, mfId))
		return s.sendDeferredNotificationTransientFailureResponse(), nil
	}
	// actor has already invested in this fund
	if investmentSummary, mfIdExists := res.GetInvestmentSummaries()[mfId]; mfIdExists {
		if investmentSummary.GetInvested() {
			logger.Info(ctx, "actor has already invested in this fund", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.MF_ID, mfId))
			return s.sendDeferredNotificationSuccessResponse(), nil
		}
	}

	mf, mfErr := s.mfDao.GetById(ctx, mfId)
	if mfErr != nil {
		logger.Error(ctx, "error in GetById", zap.Error(mfErr), zap.String(logger.MF_ID, mfId))
		return s.sendDeferredNotificationTransientFailureResponse(), nil
	}
	currentNav := mf.GetNav()
	isMFPriceDropped := isPriceDropped(currentNav, oldNav)
	taskString := getWatchlistPNTaskString(actorId)

	isDone, isDoneErr := s.doOnce.IsDone(ctx, taskString)
	if isDoneErr != nil {
		logger.Error(ctx, "error in isDone", zap.Error(isDoneErr), zap.String("taskString", taskString))
		return s.sendDeferredNotificationTransientFailureResponse(), nil
	}
	if isDone {
		logger.Info(ctx, "task is already done", zap.String(logger.MF_ID, mfId), zap.String(logger.ACTOR_ID_V2, actorId), zap.String("taskString", taskString))
		return s.sendDeferredNotificationSuccessResponse(), nil
	}

	txnErr := s.idempotentTxnExecutor.RunIdempotentTxn(ctx, DB_TRANSACTION_RETRIES, func(txnCtx context.Context) error {
		doErr := s.doOnce.Do(txnCtx, taskString)
		if doErr != nil {
			logger.Error(txnCtx, "error in Do", zap.Error(doErr), zap.String("taskString", taskString))
			return doErr
		}
		return s.sendNotificationForWatchlistedFunds(txnCtx, watchlistPN, mf, actorId, isMFPriceDropped)
	})
	if txnErr != nil {
		logger.Error(ctx, "error sending notification for watchlisted funds in txn", zap.Error(txnErr))
		return s.sendDeferredNotificationTransientFailureResponse(), txnErr
	}
	return s.sendDeferredNotificationSuccessResponse(), nil
}

func (s *Service) sendNotificationForWatchlistedFunds(ctx context.Context, watchlistPN *catalog.WatchlistMFPushNotification, mf *mfPb.MutualFund, actorId string, isMFPriceDropped bool) error {
	sendPNFunc := func() error {
		switch {
		case watchlistPN.GetWatchlistMfPnType() == catalog.WatchlistMFPushNotificationType_ONE_DAY:
			sendMessageReq, err := s.prepareSendMessageReq(ctx, actorId, watchlistPN.GetWatchlistMfPnType(), false, mf)
			if err != nil {
				logger.Error(ctx, "error in prepareSendMessageReq", zap.Error(err))
				return errors.Wrap(err, "error in prepareSendMessageReq")
			}
			sendMessageRes, err := s.commsClient.SendMessage(ctx, sendMessageReq)
			if err = epifigrpc.RPCError(sendMessageRes, err); err != nil {
				if sendMessageRes.GetStatus().IsRecordNotFound() {
					logger.Info(ctx, "no device found for sending message")
					return nil
				}
				logger.Error(ctx, "unable to send deferred notification", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
				return errors.Wrap(err, "unable to send deferred notification")
			}
			logger.Debug(ctx, "deferred notification triggered successfully", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.QUEUE_MESSAGE_ID, sendMessageRes.GetMessageId()))
			return nil
		case watchlistPN.GetWatchlistMfPnType() == catalog.WatchlistMFPushNotificationType_THREE_DAY:
			sendMessageReq, err := s.prepareSendMessageReq(ctx, actorId, watchlistPN.GetWatchlistMfPnType(), isMFPriceDropped, mf)
			if err != nil {
				logger.Error(ctx, "error in prepareSendMessageReq", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
				return errors.Wrap(err, "error in prepareSendMessageReq")

			}
			sendMessageRes, err := s.commsClient.SendMessage(ctx, sendMessageReq)
			if err = epifigrpc.RPCError(sendMessageRes, err); err != nil {
				if sendMessageRes.GetStatus().IsRecordNotFound() {
					logger.Info(ctx, "no device found for sending message")
					return nil
				}
				logger.Error(ctx, "unable to send deferred notification", zap.Error(err))
				return errors.Wrap(err, "unable to send deferred notification")
			}
			logger.Debug(ctx, "deferred notification triggered successfully", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.QUEUE_MESSAGE_ID, sendMessageRes.GetMessageId()))
			return nil
		default:
		}
		return nil
	}
	return sendPNFunc()
}

// nolint:funlen
func (s *Service) ProcessInvestmentClientEvent(ctx context.Context, req *notificationsPb.ProcessInvestmentClientEventRequest) (*notificationsPb.ProcessInvestmentClientEventResponse, error) {
	eventName := req.GetEventName()
	actorId := req.GetActorId()
	eventProperties := req.GetEventProperties()
	actorState := s.clientEventToActorState(eventName)

	if actorState == notificationsPb.ActorState_ACTOR_STATE_UNSPECIFIED {
		logger.Error(ctx, "client event name not found", zap.String("clientEventName", eventName), zap.Any("clientEventToCurrentActorStateMap", clientEventToCurrentActorStateMap))
		return s.processInvestmentClientEventSuccessResponse(), nil
	}

	// check if user has already invested
	res, err := s.catalogClient.GetInvestments(ctx, &catalogPb.GetInvestmentsRequest{ActorId: actorId, PageContext: &rpc.PageContextRequest{PageSize: 1}})
	if grpcError := epifigrpc.RPCError(res, err); grpcError != nil {
		logger.Error(ctx, "error in GetInvestments", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(grpcError))
		return s.processInvestmentClientEventFailureResponse(), nil
	}
	if len(res.GetMutualFundsInvestmentInfos()) > 0 {
		logger.Debug(ctx, "user has already invested", zap.String(logger.ACTOR_ID_V2, actorId))
		return s.processInvestmentClientEventSuccessResponse(), nil
	}

	txnErr := s.idempotentTxnExecutor.RunIdempotentTxn(ctx, DB_TRANSACTION_RETRIES, func(txnCtx context.Context) error {
		actorNotiStatus, getErr := s.actorNotificationDao.GetActorNotiStatusFromActorAndUsecase(txnCtx, actorId, notificationsPb.NotificationUsecase_MF_DROPOFF)
		if getErr != nil {
			if errors.Is(getErr, epifierrors.ErrRecordNotFound) {
				err2 := s.handleRecordNotFoundInActorNotificationStatus(txnCtx, actorId, actorState, eventProperties)
				if err2 != nil {
					logger.Error(txnCtx, "error in GetActorNotiStatusFromActorAndUsecase", zap.Error(err2), zap.String(logger.ACTOR_ID_V2, actorId))
					return err2
				}
				return nil
			} else {
				logger.Error(txnCtx, "error in GetActorNotiStatusFromActorAndUsecase", zap.Error(getErr), zap.String(logger.ACTOR_ID_V2, actorId))
				return getErr
			}

		}
		// calculate the priority of incoming event
		eventActorStatePriority := actorStatePriorityMap[actorState]
		// calculate the priority of actor state in table
		dbActorStatePriority := actorStatePriorityMap[actorNotiStatus.GetActorState()]
		switch {
		case eventActorStatePriority > dbActorStatePriority:
			actorSubState := currentActorStateToActorSubstates[actorState][0]
			actorNotiStatus.ActorState = actorState
			actorNotiStatus.ActorSubstate = actorSubState
			actorNotiStatus.InStateSince = timestampPb.New(time.Now())
			updateMasks := []notificationsPb.ActorNotificationStatusFieldMask{
				notificationsPb.ActorNotificationStatusFieldMask_IN_STATE_SINCE,
				notificationsPb.ActorNotificationStatusFieldMask_ACTOR_STATE,
				notificationsPb.ActorNotificationStatusFieldMask_ACTOR_SUBSTATE,
			}
			_, updateErr := s.actorNotificationDao.Update(txnCtx, actorNotiStatus, updateMasks)
			if updateErr != nil {
				logger.Error(txnCtx, "error in update dao", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(updateErr), zap.Any("actorNotiStatus", actorNotiStatus))
				return fmt.Errorf("error in update actorNotificationDao")
			}
			publishErr := s.publishActorNotificationStatusEvent(txnCtx, actorId, actorState, actorSubState, eventProperties)
			if publishErr != nil {
				logger.Error(txnCtx, "error in publishActorNotificationStatusEvent", zap.Error(publishErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("actorNotiStatus", actorNotiStatus))
				return nil
			}
		case eventActorStatePriority == dbActorStatePriority:
			actorNotiStatus.InStateSince = timestampPb.New(time.Now())
			updateMasks := []notificationsPb.ActorNotificationStatusFieldMask{
				notificationsPb.ActorNotificationStatusFieldMask_IN_STATE_SINCE,
			}
			_, updateErr := s.actorNotificationDao.Update(txnCtx, actorNotiStatus, updateMasks)
			if updateErr != nil {
				logger.Error(txnCtx, "error in update dao", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(updateErr), zap.Any("actorNotiStatus", actorNotiStatus))
				return fmt.Errorf("error in update dao")
			}
			return nil
		default:
			return nil
		}
		return nil
	})
	if txnErr != nil {
		return s.processInvestmentClientEventFailureResponse(), nil
	}
	return s.processInvestmentClientEventSuccessResponse(), nil
}

func (s *Service) processInvestmentClientEventSuccessResponse() *notificationsPb.ProcessInvestmentClientEventResponse {
	return &notificationsPb.ProcessInvestmentClientEventResponse{Status: rpc.StatusOk()}
}

func (s *Service) processInvestmentClientEventFailureResponse() *notificationsPb.ProcessInvestmentClientEventResponse {
	return &notificationsPb.ProcessInvestmentClientEventResponse{Status: rpc.StatusInternal()}
}

// handleRecordNotFoundInActorNotificationStatus creates actorNotificationStatus object in the actor_notification_status table and publishes the event.
func (s *Service) handleRecordNotFoundInActorNotificationStatus(ctx context.Context, actorId string, actorState notificationsPb.ActorState, eventProperties map[string]string) error {
	logger.Debug(ctx, "record not found in actor notification status", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("actorState", actorState))

	var initialActorSubstate notificationsPb.ActorSubState
	if val, ok := currentActorStateToActorSubstates[actorState]; ok {
		if len(val) > 0 {
			initialActorSubstate = val[0]
		}
	} else {
		initialActorSubstate = notificationsPb.ActorSubState_ACTOR_SUBSTATE_UNSPECIFIED
	}

	actorNotiStatus := &notificationsPb.ActorNotificationStatus{
		ActorId:             actorId,
		ActorState:          actorState,
		ActorSubstate:       initialActorSubstate,
		InStateSince:        timestampPb.New(time.Now()),
		NotificationUsecase: notificationsPb.NotificationUsecase_MF_DROPOFF,
	}
	_, err := s.actorNotificationDao.Create(ctx, actorNotiStatus)
	if err != nil {
		logger.Error(ctx, "error in Create actorNotificationStatus", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("actorNotificationStatus", actorNotiStatus))
		return err
	}

	publishErr := s.publishActorNotificationStatusEvent(ctx, actorId, actorState, initialActorSubstate, eventProperties)
	if publishErr != nil {
		logger.Error(ctx, "error in publishActorNotificationStatusEvent", zap.Error(publishErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("actorNotiStatus", actorNotiStatus))
		return publishErr
	}

	return nil
}

// publishActorNotificationStatusEvent publishes the event to the delay queue.
func (s *Service) publishActorNotificationStatusEvent(ctx context.Context, actorId string, actorState notificationsPb.ActorState, actorSubstate notificationsPb.ActorSubState, eventProperties map[string]string) error {
	req := &notificationsPb.SendDeferredNotificationRequest{
		RequestHeader: nil,
		SendDeferredNotificationRequestType: &notificationsPb.SendDeferredNotificationRequest_DropoffPnMsg{DropoffPnMsg: &notificationsPb.DropoffMFPushNotification{
			ActorId:         actorId,
			ActorState:      actorState,
			ActorSubstate:   actorSubstate,
			EventProperties: eventProperties,
		}},
	}
	// fetching delay from config
	delay, err := s.getDelayFromActorSubstate(actorSubstate)
	if err != nil {
		logger.Error(ctx, "error getting delay from actor substate", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("actorSubstateConfig", s.getDropOffPNTemplateDetails(actorSubstate)), zap.Error(err))
		return err
	}
	_, sqsErr := s.deferredNotificationPublisher.PublishWithDelay(ctx, req, delay)
	if sqsErr != nil {
		logger.Error(ctx, "error while publishing mf dropoff PN message", zap.Error(sqsErr), zap.Any(logger.ACTOR_ID_V2, actorId))
	}
	return nil
}

func createPushNotificationRequest(userId string, body string, title string, campaignName comms.CampaignName, iconUrl string, deeplink *deeplinkPb.Deeplink) *commsPb.SendMessageRequest {
	return &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_BEST_EFFORT,
		Medium:         commsPb.Medium_NOTIFICATION,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{UserId: userId},
		CampaignName:   campaignName,
		Message: &commsPb.SendMessageRequest_Notification{Notification: &comms.NotificationMessage{
			Notification: &fcm.Notification{
				NotificationType: fcm.NotificationType_SYSTEM_TRAY,
				NotificationTemplates: &fcm.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcm.SystemTrayTemplate{
					CommonTemplateFields: &fcm.CommonTemplateFields{
						Title:          title,
						Body:           body,
						IconAttributes: &fcm.IconAttributes{IconUrl: iconUrl},
						Deeplink:       deeplink,
					},
				}},
			},
		}},
	}
}

func (s *Service) PublishOTIReminderEvent(ctx context.Context, request *notificationsPb.PublishOTIReminderEventRequest) (*notificationsPb.PublishOTIReminderEventResponse, error) {

	amount := request.GetAmount()
	actorId := request.GetActorId()
	mfId := request.GetMutualFundId()
	delay := time.Now().Add(7 * 24 * time.Hour).Sub(timestampPb.Now().AsTime())
	deferredNotiReq := &notificationsPb.SendDeferredNotificationRequest{
		RequestHeader: nil,
		SendDeferredNotificationRequestType: &notificationsPb.SendDeferredNotificationRequest_InvestOneTimeReminderPnMsg{InvestOneTimeReminderPnMsg: &notificationsPb.InvestOneTimeReminder{
			ActorId:      actorId,
			MutualFundId: mfId,
			Amount:       amount,
		}},
	}
	_, sqsErr := s.deferredNotificationPublisher.PublishWithDelay(ctx, deferredNotiReq, delay)
	if sqsErr != nil {
		logger.Error(ctx, "error while publishing mf OTI reminder PN message", zap.Error(sqsErr), zap.Any(logger.ACTOR_ID_V2, actorId))
	}
	return &notificationsPb.PublishOTIReminderEventResponse{Status: rpc.StatusOk()}, nil
}
