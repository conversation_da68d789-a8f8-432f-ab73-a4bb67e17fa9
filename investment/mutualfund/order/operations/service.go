package operations

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/paymentinstrument"
	prereqDao "github.com/epifi/gamma/investment/mutualfund/order/prerequisite_handler/dao"

	"github.com/epifi/gamma/api/cx/ticket"
	prereqPb "github.com/epifi/gamma/api/investment/mutualfund/order/prerequisite_handler"

	"github.com/epifi/be-common/pkg/epifigrpc"

	efmpb "github.com/epifi/gamma/investment/mutualfund/order/filegenerator/dao"
	wtypes "github.com/epifi/gamma/investment/wire/types"
	invPkg "github.com/epifi/gamma/pkg/investment"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/investment/mutualfund"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	fgPb "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	pb "github.com/epifi/gamma/api/investment/mutualfund/order/operations"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	wob "github.com/epifi/gamma/api/wealthonboarding"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	"github.com/epifi/gamma/investment/mutualfund/dao"

	pkgPagination "github.com/epifi/be-common/pkg/pagination"
)

var (
	utcTimeZoneLocation = "UTC"
	istTimeZoneLocation = "Asia/Kolkata"
	orderSummaryFields  = []string{"Id", "VendorOrderId", "ExternalOrderId", "ActorId", "MutualFundId", "PaymentId", "Amc", "Rta", "OrderType", "OrderSubType",
		"OrderStatus", "FailureReason", "FailureDebugReason", "FolioId", "CreatedAt", "UpdatedAt", "PaymentMode", "PaymentStatus", "SchemeCode", "FullName", "Amount",
		"UTRNumber", "AccountNumber", "VendorGeneratedRefNumber", "SipInstallmentNumber", "SipRegistrationNumber", "PriorityScore", "TicketID", "IFSC",
	}
	orderCancellationFields = []string{"VendorOrderId", "Amc", "FolioId", "CreatedAt", "SchemeCode", "FullName", "Amount", "VendorGeneratedRefNumber", "SipInstallmentNumber", "SipRegistrationNumber", "PriorityScore", "TicketID"}
	postingStatusFields     = []string{"VendorOrderId", "Amc", "Rta", "OrderType", "CreatedAt", "SchemeCode", "FullName", "Amount", "UTRNumber", "OrderStatus", "VendorGeneratedRefNumber", "FolioId", "SipInstallmentNumber", "SipRegistrationNumber", "PriorityScore", "TicketID"}
	refundStatusFields      = []string{"UserCode", "VendorOrderId", "UTRNumber", "SchemeCode", "Amount", "FullName", "FolioId", "CreatedAt", "Amc", "IFSC", "AccountNumber", "AccountType", "BankName", "VendorGeneratedRefNumber",
		"SipInstallmentNumber", "SipRegistrationNumber", "PriorityScore", "TicketID"}
	preRequisiteFailureFields = []string{"ActorId", "FileType", "FileName", "FileStatus", "FileState"}
	mfOrderFields             = map[string]bool{"Id": true, "VendorOrderId": true, "ExternalOrderId": true, "ActorId": true, "MutualFundId": true,
		"Amc": true, "Rta": true, "OrderType": true, "OrderSubType": true,
		"OrderStatus": true, "FailureReason": true, "FailureDebugReason": true, "CreatedAt": true, "UpdatedAt": true, "PaymentMode": true, "Amount": true, "FolioId": true, "VendorGeneratedRefNumber": true,
		"SipInstallmentNumber": true, "SipRegistrationNumber": true}
	transactionFileFields = []string{"Id", "Amount", "Utr", "CreditedAt"}

	fullName           = "FullName"
	schemeCode         = "SchemeCode"
	utrNumber          = "UTRNumber"
	paymentStatus      = "PaymentStatus"
	opsFileNameFormat  = "orders_file_%s_%s.csv"
	accountNumber      = "AccountNumber"
	panNumber          = "PAN"
	brokerCode         = "BrokerDealerCode"
	userCode           = "UserCode"
	bankName           = "BankName"
	ifscCode           = "IFSC"
	accountType        = "AccountType"
	failureDebugReason = "FailureDebugReason"
	paymentId          = "PaymentId"
	priotityScore      = "PriorityScore"
	ticketID           = "TicketID"
	timeLayout         = "2006-01-02 15:04:05"
	pageSize           = uint32(100)
	camsBucketName     = "epifi-prod-mutualfund"
	karvyBucketName    = "epifi-prod-mutualfund-karvy"
)

type Service struct {
	pb.UnimplementedOrderOperationsServer
	orderDao                 dao.OrderDao
	s3Client                 wtypes.MFOpsS3Client
	catalogueManagerClient   catalog.CatalogManagerClient
	wobClient                wob.WealthOnboardingClient
	paymentHandlerClient     phPb.PaymentHandlerClient
	savingsClient            savingsPb.SavingsClient
	actorClient              actorPb.ActorClient
	userClient               userPb.UsersClient
	fileGeneratorClient      fgPb.FileGeneratorClient
	orderManagerClient       orderPb.OrderManagerClient
	config                   *genConf.Config
	prerequisiteStatusDao    prereqDao.PrerequisiteStatusDao
	paymentClient            paymentPb.PaymentClient
	piClient                 paymentinstrument.PiClient
	entityFileMapperDao      efmpb.EntityFileMapperDao
	fileGenerationAttemptDao efmpb.FileGenerationAttemptDao
	fileStateDao             dao.FileStateDao
	cxTicketClient           ticket.TicketClient
	camsS3Client             s3.S3Client
	karvyS3Client            s3.S3Client
}

func NewService(orderDao dao.OrderDao,
	s3Client wtypes.MFOpsS3Client,
	catalogueManagerClient catalog.CatalogManagerClient,
	wobClient wob.WealthOnboardingClient,
	paymentHandlerClient phPb.PaymentHandlerClient,
	savingsClient savingsPb.SavingsClient,
	actorClient actorPb.ActorClient,
	userClient userPb.UsersClient,
	fileGeneratorClient fgPb.FileGeneratorClient,
	orderManagerClient orderPb.OrderManagerClient,
	config *genConf.Config,
	prerequisiteStatusDao prereqDao.PrerequisiteStatusDao,
	entityFileMapperDao efmpb.EntityFileMapperDao,
	fileGenerationAttemptDao efmpb.FileGenerationAttemptDao,
	fileStateDao dao.FileStateDao,
	cxTicketClient ticket.TicketClient,
	camsS3Client wtypes.CamsS3Client,
	karvyS3Client wtypes.KarvyS3Client,
	paymentClient paymentPb.PaymentClient,
	piClient paymentinstrument.PiClient,
) *Service {
	return &Service{
		orderDao:                 orderDao,
		s3Client:                 s3Client,
		catalogueManagerClient:   catalogueManagerClient,
		wobClient:                wobClient,
		paymentHandlerClient:     paymentHandlerClient,
		savingsClient:            savingsClient,
		actorClient:              actorClient,
		userClient:               userClient,
		fileGeneratorClient:      fileGeneratorClient,
		orderManagerClient:       orderManagerClient,
		config:                   config,
		prerequisiteStatusDao:    prerequisiteStatusDao,
		entityFileMapperDao:      entityFileMapperDao,
		fileGenerationAttemptDao: fileGenerationAttemptDao,
		fileStateDao:             fileStateDao,
		cxTicketClient:           cxTicketClient,
		camsS3Client:             camsS3Client,
		karvyS3Client:            karvyS3Client,
		paymentClient:            paymentClient,
		piClient:                 piClient,
	}
}

func (s *Service) GetFileFromBucket(ctx context.Context, req *pb.GetFileFromBucketRequest) (*pb.GetFileFromBucketResponse, error) {
	if len(req.GetBucketName()) == 0 {
		return &pb.GetFileFromBucketResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty input bucket name"),
		}, nil
	}

	s3Client, err := s.getS3ClientForBucket(req.GetBucketName())
	if err != nil {
		return &pb.GetFileFromBucketResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid bucket name"),
		}, nil
	}

	s3Paths := strings.Split(req.GetS3Paths(), ",")
	if len(s3Paths) == 0 {
		return &pb.GetFileFromBucketResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty input s3 paths"),
		}, nil
	}
	var signedUrls []string
	for _, path := range s3Paths {
		signedUrl, err := s3Client.GetPreSignedUrl(ctx, path, 1800*time.Second)
		if err != nil {
			logger.Error(ctx, "error in getting signed url", zap.Error(err))
		}
		signedUrls = append(signedUrls, signedUrl)
	}

	return &pb.GetFileFromBucketResponse{
		Status:     rpcPb.StatusOk(),
		SignedUrls: signedUrls,
	}, nil
}

func (s *Service) GenerateMFSummaryFile(ctx context.Context, request *pb.GenerateMFSummaryFileRequest) (*pb.GenerateMFSummaryFileResponse, error) {

	logger.Info(ctx, "received GenerateMFSummaryFile", zap.String(logger.MF_AMC, request.Amc.String()),
		zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.END_TIME, request.EndTime.String()),
		zap.Strings(logger.ORDER_IDS, request.VendorOrderIds))
	utcTimeZone, err := time.LoadLocation(utcTimeZoneLocation)
	if err != nil {
		logger.Error(ctx, "error while loading timezone location",
			zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
		return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternal()}, nil
	}

	istTimeZone, err := time.LoadLocation(istTimeZoneLocation)
	if err != nil {
		logger.Error(ctx, "error while loading timezone location",
			zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
		return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternal()}, nil
	}

	startTime := request.StartTime.AsTime().In(utcTimeZone)
	endTime := request.EndTime.AsTime().In(utcTimeZone)

	switch request.Usecase {
	case pb.UseCase_USE_CASE_PREREQUISITE_FAILURE:
		return s.getPrerequisiteFailuresFile(ctx, startTime, endTime, request)
	case pb.UseCase_USE_CASE_AMC_TRANSACTIONS:
		return s.getAmcDebitAndCreditTracker(ctx, startTime, endTime, request, istTimeZone)
	}

	var orders []*orderPb.Order
	if len(request.VendorOrderIds) == 0 {
		orders, err = s.getOrdersByCreatedAt(ctx, startTime, endTime, request.Amc)
		if err != nil {
			logger.Error(ctx, "error while fetching orders",
				zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
			return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternal()}, nil
		}

		// ToDo(Junaid): Do these filtering from the db using complex queries. Filtering in memory should be fine for now as this is an ops use case.
		orders, err = s.filterOrdersOnUseCase(ctx, orders, request.Usecase)
		if err != nil {
			logger.Error(ctx, "error in filterOrdersOnUseCase",
				zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
			return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternal()}, nil
		}
	} else {
		orders, err = s.orderDao.GetByVendorOrderIds(ctx, request.VendorOrderIds)
		if err != nil {
			logger.Error(ctx, "error in GetByVendorOrderIds",
				zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
			return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternal()}, nil
		}
	}

	fileName := fmt.Sprintf(opsFileNameFormat, startTime.Format(timeLayout), endTime.Format(timeLayout))

	fileUrl, err := s.generateOpsFileForOrders(ctx, orders, fileName, istTimeZone, request.Usecase)
	if err != nil {
		logger.Error(ctx, "error while invoking generateOpsFileForOrders",
			zap.String(logger.START_TIME, request.StartTime.String()), zap.String(logger.END_TIME, request.EndTime.String()), zap.Error(err))
		return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusInternal()}, nil
	}

	return &pb.GenerateMFSummaryFileResponse{Status: rpcPb.StatusOk(), FileUrl: fileUrl}, nil
}

func (s *Service) generateOpsFileForOrders(ctx context.Context, orders []*orderPb.Order, fileName string, timeZoneLocation *time.Location, useCase pb.UseCase) (string, error) {

	var mfDetails map[string]*mutualfund.MutualFund

	var onboardingDetails map[string]*wob.PreInvestmentDetail

	var orderIdToPaymentDetailsMap map[string]*phPb.PaymentDetails

	var utrNumbersMap map[string]string

	var entityIDToTicketIDMap map[string]string

	var err error
	grp, grpCtx := errgroup.WithContext(ctx)

	grp.Go(func() error {
		mfDetails = s.getMutualFundDetails(ctx, orders)
		return nil
	})

	grp.Go(func() error {
		onboardingDetails, err = s.getPreInvestmentDetail(grpCtx, orders)
		return err
	})

	grp.Go(func() error {
		orderIdToPaymentDetailsMap = s.getPaymentDetails(grpCtx, orders)
		return nil
	})
	grp.Go(func() error {
		entityIDToTicketIDMap, err = s.fetchSupportTickets(grpCtx)
		if err != nil {
			logger.Error(grpCtx, "error in fetchSupportTickets", zap.Error(err))
		}
		return nil
	})

	if err = grp.Wait(); err != nil {
		return "", err
	}

	utrNumbersMap = s.getUtrNumber(ctx, orders, orderIdToPaymentDetailsMap)

	var sb strings.Builder

	/*
		ToDo(Junaid): Currently we are writing the fileContent into memory. If we get too many orders in a day that
					  equals few MBs size, then we might need to write to file in disk, upload to s3 and delete the file.
	*/
	opsFileFields := s.getOpsFileFields(useCase)
	sb.WriteString(strings.Join(opsFileFields, ","))
	sb.WriteString("\n")

	for _, order := range orders {
		value := reflect.ValueOf(order).Elem()
		var row []string

		for _, fieldName := range opsFileFields {
			if _, ok := mfOrderFields[fieldName]; ok {
				row = append(row, s.getRowValue(fieldName, value.FieldByName(fieldName), timeZoneLocation))
			} else {
				row = append(row, s.getDerivedRowValue(ctx, order, mfDetails, onboardingDetails,
					utrNumbersMap, fieldName, orderIdToPaymentDetailsMap, entityIDToTicketIDMap))
			}
		}
		sb.WriteString(strings.Join(row, ","))
		sb.WriteString("\n")
	}

	fileContent := sb.String()

	logger.Info(ctx, "file generated successfully, trying to upload")
	url, err := s.s3Client.WriteAndGetPreSignedUrl(ctx, fileName, []byte(fileContent), 1800)
	if err != nil {
		logger.Error(ctx, "error in WriteAndGetPreSignedUrl", zap.Error(err))
		return "", err
	}
	logger.Info(ctx, fmt.Sprintf("size of pre-signed url: %d", len(url)))
	return url, err
}

// Derived rows values are values that are not extracted from mf_orders table
func (s *Service) getDerivedRowValue(ctx context.Context, order *orderPb.Order, mfDetails map[string]*mutualfund.MutualFund,
	preInvestmentDetails map[string]*wob.PreInvestmentDetail, utrNumbersMap map[string]string, fieldName string,
	orderIdToPaymentDetailsMap map[string]*phPb.PaymentDetails, entityIDToTicketIDMap map[string]string) string {

	switch fieldName {
	case schemeCode:
		_, ok := mfDetails[order.MutualFundId]
		if ok && mfDetails[order.MutualFundId] != nil {
			return mfDetails[order.MutualFundId].SchemeCode
		} else {
			logger.Error(ctx, "schemeCode not found for mutual fund", zap.String(logger.ORDER_ID, order.Id), zap.String(logger.MUTUAL_FUND, order.MutualFundId))
			return ""
		}
	case fullName:
		_, ok := preInvestmentDetails[order.ActorId]
		if ok && preInvestmentDetails[order.ActorId] != nil && preInvestmentDetails[order.ActorId].CustomerName != nil {
			return preInvestmentDetails[order.ActorId].CustomerName.ToSentenceCaseString()
		} else {
			logger.Error(ctx, "customer name is not present", zap.String(logger.ORDER_ID, order.Id), zap.String(logger.ACTOR_ID, order.ActorId))
			return ""
		}
	case utrNumber:
		_, ok := utrNumbersMap[order.Id]
		if ok {
			return utrNumbersMap[order.Id]
		} else {
			logger.Error(ctx, "utr number is not present", zap.String(logger.ORDER_ID, order.Id), zap.String(logger.ACTOR_ID, order.ActorId))
			return ""
		}
	case accountNumber:
		// https://monorail.pointz.in/p/fi-app/issues/detail?id=21008 remove bank account from ops file and create a dev action for the same.
		_, ok := preInvestmentDetails[order.ActorId]
		if ok && preInvestmentDetails[order.ActorId] != nil && preInvestmentDetails[order.ActorId].BankDetails != nil {
			return preInvestmentDetails[order.ActorId].BankDetails.AccountNumber
		} else {
			logger.Info(ctx, "bank account details is not present", zap.String(logger.ORDER_ID, order.Id), zap.String(logger.ACTOR_ID, order.ActorId))
			return ""
		}
	case paymentStatus:
		_, ok := orderIdToPaymentDetailsMap[order.Id]
		if ok && orderIdToPaymentDetailsMap[order.Id] != nil && orderIdToPaymentDetailsMap[order.Id].Payment != nil {
			return orderIdToPaymentDetailsMap[order.Id].Payment.PaymentStatus.String()
		} else {
			logger.Info(ctx, "bank account details is not present", zap.String(logger.ORDER_ID, order.Id), zap.String(logger.ACTOR_ID, order.ActorId))
			return ""
		}
	case panNumber:
		_, ok := preInvestmentDetails[order.ActorId]
		if ok && preInvestmentDetails[order.ActorId] != nil && preInvestmentDetails[order.ActorId].Pan != nil {
			return preInvestmentDetails[order.ActorId].Pan.Id
		} else {
			logger.Info(ctx, "pan number is not present", zap.String(logger.ORDER_ID, order.Id), zap.String(logger.ACTOR_ID, order.ActorId))
			return ""
		}
	case brokerCode:
		if order.Rta == commonvgpb.Vendor_CAMS {
			return s.config.Cams().BrokerCode()
		} else {
			return s.config.Karvy().BrokerCode()
		}
	case userCode:
		if order.Rta == commonvgpb.Vendor_CAMS {
			return s.config.Cams().UserCode()
		} else {
			return s.config.Karvy().UserCode()
		}
	case bankName:
		_, ok := preInvestmentDetails[order.ActorId]
		if ok && preInvestmentDetails[order.ActorId] != nil && preInvestmentDetails[order.ActorId].BankDetails != nil {
			return preInvestmentDetails[order.ActorId].BankDetails.BankName
		} else {
			logger.Info(ctx, "bank account details is not present, falling back to Federal Bank", zap.String(logger.ORDER_ID, order.Id), zap.String(logger.ACTOR_ID, order.ActorId))
			return "Federal Bank"
		}
	case accountType:
		_, ok := preInvestmentDetails[order.ActorId]
		if ok && preInvestmentDetails[order.ActorId] != nil && preInvestmentDetails[order.ActorId].BankDetails != nil {
			return preInvestmentDetails[order.ActorId].BankDetails.AccountType
		} else {
			logger.Info(ctx, "bank account details is not present, falling back to Savings Account", zap.String(logger.ORDER_ID, order.Id), zap.String(logger.ACTOR_ID, order.ActorId))
			return "Savings Account"
		}
	case ifscCode:
		_, ok := preInvestmentDetails[order.ActorId]
		if ok && preInvestmentDetails[order.ActorId] != nil && preInvestmentDetails[order.ActorId].BankDetails != nil {
			return preInvestmentDetails[order.ActorId].BankDetails.IfscCode
		} else {
			logger.Info(ctx, "bank account details is not present", zap.String(logger.ORDER_ID, order.Id), zap.String(logger.ACTOR_ID, order.ActorId))
			return ""
		}
	case paymentId:
		_, ok := orderIdToPaymentDetailsMap[order.Id]
		if ok && orderIdToPaymentDetailsMap[order.Id] != nil && orderIdToPaymentDetailsMap[order.Id].Payment != nil {
			return orderIdToPaymentDetailsMap[order.Id].Payment.PaymentId
		} else {
			return ""
		}
	case priotityScore:
		if mfDetails[order.MutualFundId] != nil {
			return s.getPriorityScore(ctx, order, entityIDToTicketIDMap[order.VendorOrderId], mfDetails[order.MutualFundId], orderIdToPaymentDetailsMap[order.Id])
		} else {
			logger.Error(ctx, "unsupported mutual fund", zap.String(logger.MF_ID, order.MutualFundId))
			return ""
		}
	case ticketID:
		return entityIDToTicketIDMap[order.VendorOrderId]
	default:
		logger.Error(ctx, "unsupported field name", zap.String("FieldName", fieldName))
		return ""
	}
}

func (s *Service) getRowValue(fieldName string, fieldValue reflect.Value, timeZoneLocation *time.Location) string {
	if fieldValue.IsValid() {
		if fieldName == "CreatedAt" || fieldName == "UpdatedAt" || fieldName == "CreditedAt" {
			return fieldValue.Interface().(*timestampPb.Timestamp).AsTime().In(timeZoneLocation).String()
		}
		if fieldName == "Amount" {
			return strings.ReplaceAll(money.ToDisplayString(fieldValue.Interface().(*moneyPb.Money)), ",", "")
		}
		if fieldName == failureDebugReason {
			commaReplaced := strings.ReplaceAll(fmt.Sprintf("%v", fieldValue.Interface()), ",", " ")
			return strings.ReplaceAll(commaReplaced, "\n", " ")
		}
		return fmt.Sprintf("%v", fieldValue.Interface())

	} else {
		return ""
	}
}

func (s *Service) getOrdersByCreatedAt(ctx context.Context, startTime time.Time, endTime time.Time, amc mutualfund.Amc) ([]*orderPb.Order, error) {
	var orders []*orderPb.Order
	var pageContext *rpcPb.PageContextResponse
	var err error

	if amc != mutualfund.Amc_AMC_UNSPECIFIED {
		orders, pageContext, err = s.orderDao.GetOrdersByCreatedAt(ctx, startTime, endTime, nil, pageSize, dao.WithAmc(amc))
		if err != nil {
			return nil, err
		}
	} else {
		orders, pageContext, err = s.orderDao.GetOrdersByCreatedAt(ctx, startTime, endTime, nil, pageSize)
		if err != nil {
			return nil, err
		}
	}

	for pageContext.HasAfter {
		var newToken *pkgPagination.PageToken
		var page []*orderPb.Order
		newToken, err = pkgPagination.GetPageToken(&rpcPb.PageContextRequest{
			Token:    &rpcPb.PageContextRequest_AfterToken{AfterToken: pageContext.GetAfterToken()},
			PageSize: 100})

		if err != nil {
			return nil, err
		}

		if amc != mutualfund.Amc_AMC_UNSPECIFIED {
			page, pageContext, err = s.orderDao.GetOrdersByCreatedAt(ctx, startTime, endTime, newToken, pageSize, dao.WithAmc(amc))
		} else {
			page, pageContext, err = s.orderDao.GetOrdersByCreatedAt(ctx, startTime, endTime, newToken, pageSize)
		}
		if err != nil {
			return nil, err
		}
		orders = append(orders, page...)
	}
	return orders, nil
}

func (s *Service) filterOrdersOnUseCase(ctx context.Context, orders []*orderPb.Order, useCase pb.UseCase) ([]*orderPb.Order, error) {
	switch useCase {
	case pb.UseCase_USE_CASE_ORDERS_TO_CANCEL:
		return s.filterOrdersToCancel(ctx, orders)
	case pb.UseCase_USE_CASE_POSTING_STATUS:
		return s.filterOrdersForPostingStatus(orders), nil
	case pb.UseCase_USE_CASE_TO_DEBUG:
		return s.filterOrdersForDebugging(orders), nil
	case pb.UseCase_USE_CASE_OPS_TEAM_DEBUG:
		return s.filterOrdersForOpsDebugging(orders), nil
	case pb.UseCase_USE_CASE_ORDERS_TO_REFUND:
		return s.filterOrdersToRefund(ctx, orders), nil
	default:
		return orders, nil
	}
}

func (s *Service) filterOrdersToRefund(ctx context.Context, orders []*orderPb.Order) []*orderPb.Order {

	var failedOrders []*orderPb.Order
	for _, order := range orders {
		if order.OrderStatus == orderPb.OrderStatus_FAILURE {
			failedOrders = append(failedOrders, order)
			continue

		}
	}

	paymentDetails := s.getPaymentDetails(ctx, failedOrders)

	var ordersForRefund []*orderPb.Order
	for _, order := range failedOrders {
		_, ok := paymentDetails[order.Id]
		if !ok {
			continue
		}

		if paymentDetails[order.Id].Payment.PaymentStatus == phPb.PaymentStatus_PAYMENT_STATUS_SUCCESSFUL {
			ordersForRefund = append(ordersForRefund, order)
		}
	}
	return ordersForRefund

}

func (s *Service) filterOrdersToCancel(ctx context.Context, orders []*orderPb.Order) ([]*orderPb.Order, error) {

	var failedOrders []*orderPb.Order
	for _, order := range orders {
		if order.OrderStatus == orderPb.OrderStatus_FAILURE {
			failedOrders = append(failedOrders, order)
			continue
		}
	}

	orderIDToFileNameMap, fileStateMap, err := s.getFileStateDetails(ctx, failedOrders)
	if err != nil {
		return nil, err
	}

	paymentDetails := s.getPaymentDetails(ctx, failedOrders)

	var ordersToCancel []*orderPb.Order
	var fileState *orderPb.FileState
	for _, failedOrder := range failedOrders {

		// The failed orders for which payment is successful, will show up in refund file, so we don't need to show it in
		// cancellation file
		_, ok := paymentDetails[failedOrder.Id]
		if ok && paymentDetails[failedOrder.Id].Payment.PaymentStatus == phPb.PaymentStatus_PAYMENT_STATUS_SUCCESSFUL {
			continue
		}

		fileName, ok := orderIDToFileNameMap[failedOrder.Id]
		if ok {
			fileState, ok = fileStateMap[fileName]
			if ok && (fileState.State == orderPb.FileState_FILE_SENT_TO_VENDOR || fileState.State == orderPb.FileState_FILE_PROCESS_BY_VENDOR_SUCCESSFUL) {
				ordersToCancel = append(ordersToCancel, failedOrder)
			}
		}
	}

	return ordersToCancel, nil
}

func (s *Service) filterOrdersForOpsDebugging(orders []*orderPb.Order) []*orderPb.Order {
	var filteredOrders []*orderPb.Order
	for _, order := range orders {
		// debug all orders that are stuck in manual intervention with the failure reasons that needs to be debugged by ops team,
		if order.OrderStatus == orderPb.OrderStatus_MANUAL_INTERVENTION && s.isOpsDebugFailureReason(order.FailureDebugReason) {
			filteredOrders = append(filteredOrders, order)
			continue
		}
	}
	return filteredOrders
}

func (s *Service) filterOrdersForDebugging(orders []*orderPb.Order) []*orderPb.Order {

	var filteredOrders []*orderPb.Order
	for _, order := range orders {
		// for these order status, nothing needs to be done for debugging.
		if order.OrderStatus == orderPb.OrderStatus_IN_FULFILLMENT ||
			order.OrderStatus == orderPb.OrderStatus_IN_SETTLEMENT ||
			order.OrderStatus == orderPb.OrderStatus_CONFIRMED_BY_RTA ||
			order.OrderStatus == orderPb.OrderStatus_SETTLED ||
			(order.OrderStatus == orderPb.OrderStatus_FAILURE && order.FailureReason != orderPb.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR_MOBILE_NUMBER_CHANGE) ||
			(order.OrderStatus == orderPb.OrderStatus_FAILURE && order.FailureReason != orderPb.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR_REJECTED_DUE_TO_NO_UNITS_WITH_AMC) ||
			order.OrderStatus == orderPb.OrderStatus_CREATION_ON_HOLD {
			continue
		}
		// If an order is stuck in paid status after 24 hours, then it needs to be dubugged
		if order.OrderStatus == orderPb.OrderStatus_PAID &&
			time.Now().After(order.CreatedAt.AsTime().Add(24*time.Hour)) {
			filteredOrders = append(filteredOrders, order)
			continue
		}

		// debug all orders that are stuck in some state other than paid, MANUAL_INTERVENTION and failure for more than 3 hours
		if order.OrderStatus != orderPb.OrderStatus_MANUAL_INTERVENTION &&
			order.OrderStatus != orderPb.OrderStatus_FAILURE &&
			time.Now().After(order.CreatedAt.AsTime().Add(3*time.Hour)) {
			filteredOrders = append(filteredOrders, order)
			continue
		}

		if (order.OrderStatus == orderPb.OrderStatus_MANUAL_INTERVENTION && !s.isOpsDebugFailureReason(order.FailureDebugReason)) ||
			order.FailureReason == orderPb.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR_MOBILE_NUMBER_CHANGE ||
			order.FailureReason == orderPb.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR_REJECTED_DUE_TO_NO_UNITS_WITH_AMC {
			filteredOrders = append(filteredOrders, order)
			continue
		}
	}

	return filteredOrders
}

func (s *Service) filterOrdersForPostingStatus(orders []*orderPb.Order) []*orderPb.Order {
	var filteredOrders []*orderPb.Order
	for _, order := range orders {
		if order.GetOrderStatus() == orderPb.OrderStatus_IN_FULFILLMENT || order.GetOrderStatus() == orderPb.OrderStatus_IN_SETTLEMENT ||
			order.GetOrderStatus() == orderPb.OrderStatus_NOTIFYING_PAYMENT_CREDIT {
			filteredOrders = append(filteredOrders, order)
		}
	}
	return filteredOrders
}

func (s *Service) getOpsFileFields(useCase pb.UseCase) []string {
	switch useCase {
	case pb.UseCase_USE_CASE_ORDERS_TO_CANCEL:
		return orderCancellationFields
	case pb.UseCase_USE_CASE_POSTING_STATUS:
		return postingStatusFields
	case pb.UseCase_USE_CASE_ORDERS_TO_REFUND:
		return refundStatusFields
	case pb.UseCase_USE_CASE_PREREQUISITE_FAILURE:
		return preRequisiteFailureFields
	default:
		return orderSummaryFields
	}
}

func (s *Service) getFileType(entityType fgPb.EntityType) orderPb.FileType {
	switch entityType {
	case fgPb.EntityType_ENTITY_TYPE_FATCA:
		return orderPb.FileType_FILE_TYPE_FATCA_FILE
	case fgPb.EntityType_ENTITY_TYPE_ELOG:
		return orderPb.FileType_FILE_TYPE_ELOG_FILE
	case fgPb.EntityType_ENTITY_TYPE_ORDER_FEED:
		return orderPb.FileType_FILE_TYPE_ORDER_FEED_FILE
	case fgPb.EntityType_ENTITY_TYPE_CREDIT_MIS:
		return orderPb.FileType_FILE_TYPE_CREDIT_MIS_REPORT_FILE
	default:
		return orderPb.FileType_FILE_TYPE_UNSPECIFIED
	}
}

type PrerequisiteFileStructure struct {
	ActorId      string
	FileName     string
	FileType     string
	EntityStatus string
	FileState    string
}

// generateOpsFileForPreRequisiteFailure generates file url for prerequisite failure
func (s *Service) generateOpsFileForPreRequisiteFailure(ctx context.Context, fileInfos []*PrerequisiteFileStructure, useCase pb.UseCase, startTime time.Time, endTime time.Time) (string, error) {
	opsFileFields := s.getOpsFileFields(useCase)
	var sb strings.Builder
	sb.WriteString(strings.Join(opsFileFields, ","))
	sb.WriteString("\n")
	fileName := fmt.Sprintf(opsFileNameFormat, startTime, endTime.Format(timeLayout))

	for _, fileInfo := range fileInfos {
		value := reflect.ValueOf(fileInfo).Elem()
		var row []string
		for _, fieldName := range opsFileFields {
			val := s.getRowValuePreReqFailure(fieldName, value.FieldByName(fieldName))
			row = append(row, val)
		}
	}
	fileContent := sb.String()
	logger.Info(ctx, "file generated successfully, trying to upload")
	url, err := s.s3Client.WriteAndGetPreSignedUrl(ctx, fileName, []byte(fileContent), 1800)
	if err != nil {
		logger.Error(ctx, "error in WriteAndGetPreSignedUrl", zap.Error(err))
		return "", err
	}
	logger.Info(ctx, fmt.Sprintf("size of pre-signed url: %d", len(url)))
	return url, err
}

func (s *Service) getRowValuePreReqFailure(fieldName string, fieldValue reflect.Value) string {
	if fieldValue.IsValid() {
		switch {
		case fieldName == "ActorId":
			return fmt.Sprintf("%v", fieldValue.Interface())
		case fieldName == "FileType":
			return fmt.Sprintf("%v", fieldValue.Interface())
		case fieldName == "FileName":
			return fmt.Sprintf("%v", fieldValue.Interface())
		case fieldName == "FileStatus":
			return fmt.Sprintf("%v", fieldValue.Interface())
		default:
			return ""
		}
	} else {
		return ""
	}
}

func (s *Service) getPreRequisiteFileInfos(ctx context.Context, actorIdStateMapping map[string]prereqPb.State, entityType fgPb.EntityType, vendor commonvgpb.Vendor) ([]*PrerequisiteFileStructure, error) {
	var fileInfos []*PrerequisiteFileStructure
	for actorId, _ := range actorIdStateMapping {
		if actorIdStateMapping[actorId] == prereqPb.State_STATUS_SUCCESS {
			continue
		}
		entityId := actorId
		res, err := s.getPrerequisiteFileDetails(ctx, entityId, vendor, entityType)
		if err != nil {
			logger.Error(ctx, "error in getPrerequisiteFileDetails", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			return nil, err
		}
		fileName := res.FileState.GetFileName()
		fgaFileType := res.FileGenerationAttempt.GetFileType()
		fileStatus, err1 := s.fileStateDao.GetActiveFileByName(ctx, vendor, s.getFileType(entityType), fileName)
		if err != nil {
			logger.Error(ctx, "error in GetActiveFileByName", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("entityType", entityType.String()), zap.Any("fileName", fileName), zap.Error(err))
			return nil, err1
		}
		fileInfos = append(fileInfos, &PrerequisiteFileStructure{
			ActorId:      actorId,
			FileName:     fileName,
			FileType:     fgaFileType.String(),
			EntityStatus: res.EntityFileMap.GetStatus().String(),
			FileState:    fileStatus.GetState().String(),
		})
	}
	return fileInfos, nil
}

type EntityFileDetails struct {
	EntityFileMap         *fgPb.EntityFileMap
	FileGenerationAttempt *fgPb.FileGenerationAttempt
	FileState             *orderPb.FileState
}

func (s *Service) fetchSupportTickets(ctx context.Context) (map[string]string, error) {

	hasAfter := true
	var token *rpcPb.PageContextRequest_AfterToken
	token = nil

	var tickets []*ticket.Ticket

	for hasAfter {
		res, err := s.cxTicketClient.GetSupportTickets(ctx, &ticket.GetSupportTicketsRequest{TicketFilters: &ticket.TicketFilters{
			StatusList: []ticket.Status{ticket.Status_STATUS_PENDING, ticket.Status_STATUS_OPEN, ticket.Status_STATUS_WAITING_ON_THIRD_PARTY,
				ticket.Status_STATUS_ESCALATED_TO_FI_ENG, ticket.Status_STATUS_ESCALATED_TO_L2, ticket.Status_STATUS_ESCALATED_TO_FEDERAL, ticket.Status_STATUS_ESCALATED_TO_FI_OM},
			ProductCategoryList: []ticket.ProductCategory{ticket.ProductCategory_PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS},
		}, PageContextRequest: &rpcPb.PageContextRequest{PageSize: 50, Token: token}})
		if grpcError := epifigrpc.RPCError(res, err); grpcError != nil {
			return nil, grpcError
		}
		hasAfter = res.PageContextResponse.HasAfter
		token = &rpcPb.PageContextRequest_AfterToken{AfterToken: res.PageContextResponse.AfterToken}
		tickets = append(tickets, res.Tickets...)
	}

	entityIDToTicketIDMap := make(map[string]string)
	for _, t := range tickets {
		// ToDo(Junaid): remove this log after prod testing
		logger.Info(ctx, "unresolved mutual-fund cx ticket", zap.Int64("ticket_details", t.GetId()))
		if t.CustomFieldWithValue != nil {
			entityIDToTicketIDMap[t.CustomFieldWithValue.EntityId] = strconv.Itoa(int(t.Id))
		}
	}
	return entityIDToTicketIDMap, nil
}

func (s *Service) getPriorityScore(ctx context.Context, order *orderPb.Order, cxTicketID string,
	mf *mutualfund.MutualFund, paymentDetails *phPb.PaymentDetails) string {
	score := 0
	if !(order.OrderStatus == orderPb.OrderStatus_CONFIRMED_BY_RTA || order.OrderStatus == orderPb.OrderStatus_IN_SETTLEMENT || order.OrderStatus == orderPb.OrderStatus_SETTLED) {
		assetClass := mf.AssetClass
		var etaTime time.Time
		var err error
		if paymentDetails != nil && paymentDetails.Payment != nil {
			etaTime, err = invPkg.GetETADate(invPkg.ETAParams{
				PendingOrder:  order,
				AssetClass:    assetClass,
				PaymentStatus: paymentDetails.Payment.PaymentStatus,
				PaymentTime:   paymentDetails.Payment.CreatedAt,
				CategoryName:  mf.GetCategoryName(),
			})
		} else {
			etaTime, err = invPkg.GetETADate(invPkg.ETAParams{
				PendingOrder:  order,
				AssetClass:    assetClass,
				PaymentStatus: phPb.PaymentStatus_PAYMENT_STATUS_UNSPECIFIED,
				PaymentTime:   nil,
				CategoryName:  mf.GetCategoryName(),
			})
		}
		if err != nil {
			logger.Error(ctx, "error in calculating etadate", zap.String(logger.ORDER_ID, order.Id), zap.Error(err))
		} else {
			timeZoneLocation, err2 := time.LoadLocation(istTimeZoneLocation)
			if err2 != nil {
				logger.Error(ctx, "error in time.LoadLocation", zap.String(logger.ORDER_ID, order.Id), zap.Error(err))
			} else {
				daysAfterEta := time.Now().In(timeZoneLocation).Sub(etaTime).Hours() / 24
				if daysAfterEta >= 0 {
					score += int(daysAfterEta) * 5
				}
			}
		}

	}
	if order.OrderType == orderPb.OrderType_SELL {
		score += 2
	}

	if strings.Compare(cxTicketID, "") != 0 {
		score += 10
	}

	orderAmount := order.Amount

	switch {
	case money.Compare(orderAmount, money.FromPaisa(50000)) == -1:
		score += 1
	case money.Compare(orderAmount, money.FromPaisa(100000)) == -1:
		score += 2
	case money.Compare(orderAmount, money.FromPaisa(500000)) == -1:
		score += 4
	case money.Compare(orderAmount, money.FromPaisa(1000000)) == -1:
		score += 8
	default:
		score += 10
	}
	return strconv.Itoa(score)
}

func (s *Service) getS3ClientForBucket(bucketName string) (s3.S3Client, error) {
	switch bucketName {
	case s.config.Application().FileGenerator().CamsS3Bucket:
		return s.camsS3Client, nil
	case s.config.Application().FileGenerator().KarvyS3Bucket:
		return s.karvyS3Client, nil
	default:
		return nil, epifierrors.ErrInvalidArgument
	}
}

func (s *Service) isOpsDebugFailureReason(failureReason string) bool {

	switch {
	case strings.Contains(failureReason, "transaction you are trying to enter is not permitted under the current setup rules"):
		return true
	case strings.Contains(failureReason, "not available in SYSTEMATIC_FREQ_DATES_SETUP"):
		return true
	default:
		return false
	}
}

func (s *Service) GetEntityFilePathsByVendorOrderIds(ctx context.Context, req *pb.GetEntityFilePathsByVendorOrderIdsRequest) (*pb.GetEntityFilePathsByVendorOrderIdsResponse, error) {
	if len(req.GetVendorOrderIds()) == 0 {
		return &pb.GetEntityFilePathsByVendorOrderIdsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty input vendor order ids"),
		}, nil
	}

	if req.GetFileType() != fgPb.FileType_FILE_TYPE_ORDER_FEED {
		return &pb.GetEntityFilePathsByVendorOrderIdsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("unsupported file type"),
		}, nil
	}

	orderDetails, err := s.orderDao.GetByVendorOrderIds(ctx, req.GetVendorOrderIds())
	if err != nil {
		logger.Error(ctx, "error while fetching order details using list of vendor order Ids", zap.Error(err))
		return nil, err
	}

	fileNameToFile := make(map[string]*pb.FileMetaData)
	fileNameToVendorOrderIds := make(map[string][]string)
	vendorOrderIdToFile := make(map[string]*pb.FileMetaData)
	for _, orderDetail := range orderDetails {
		orderId := orderDetail.GetId()
		vendorOrderId := orderDetail.GetVendorOrderId()

		var entityFileMap *fgPb.EntityFileMap
		entityFileMap, err = s.entityFileMapperDao.GetActiveEntityDetails(ctx, orderId, orderDetail.GetRta(), fgPb.EntityType(req.GetFileType()))

		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error while fetching %s entity file mapper details", fgPb.EntityType_ENTITY_TYPE_ORDER_FEED), zap.Error(err), zap.String(logger.ORDER_ID, orderId))
			continue
		}

		var fileGenerationAttempt *fgPb.FileGenerationAttempt
		fileGenerationAttempt, err = s.fileGenerationAttemptDao.GetFileGenerationAttemptById(ctx, entityFileMap.GetFileId())
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error while fetching %s file generation attempt", fgPb.EntityType_ENTITY_TYPE_ORDER_FEED), zap.Error(err),
				zap.String(logger.ORDER_ID, orderId))
			continue
		}

		fileState, err1 := s.fileStateDao.GetActiveFileByName(ctx, orderDetail.GetRta(), orderPb.FileType_FILE_TYPE_ORDER_FEED_FILE, fileGenerationAttempt.GetFileName())
		if err1 != nil {
			logger.Error(ctx, fmt.Sprintf("error while fetching %s file state", orderPb.FileType_FILE_TYPE_ORDER_FEED_FILE), zap.Error(err),
				zap.String(logger.ORDER_ID, orderId))
			continue
		}

		vendorName := fileGenerationAttempt.GetVendorName()
		fileName := fileGenerationAttempt.GetFileName()
		parsedOrderCreatedAtDate := strings.ReplaceAll(fileState.GetCreatedAt().AsTime().Format(time.DateOnly), "-", "")

		fileNameToVendorOrderIds[fileName] = append(fileNameToVendorOrderIds[fileName], vendorOrderId)

		if fileNameToFile[fileName] == nil {
			fileNameToFile[fileName] = &pb.FileMetaData{
				FilePath:   fmt.Sprintf("%s/%s/%s/%s", vendorName, parsedOrderCreatedAtDate, req.GetFileType(), fileName),
				VendorName: vendorName,
			}
		}
	}

	for fileName, vendorOrderIds := range fileNameToVendorOrderIds {
		for _, vendorOrderId := range vendorOrderIds {
			vendorOrderIdToFile[vendorOrderId] = &pb.FileMetaData{
				FilePath:   fileNameToFile[fileName].GetFilePath(),
				VendorName: fileNameToFile[fileName].GetVendorName(),
			}
		}
	}

	return &pb.GetEntityFilePathsByVendorOrderIdsResponse{
		Status:              rpcPb.StatusOk(),
		VendorOrderIdToFile: vendorOrderIdToFile,
	}, nil
}
