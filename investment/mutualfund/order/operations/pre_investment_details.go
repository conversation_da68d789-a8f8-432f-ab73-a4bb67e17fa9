package operations

import (
	"context"
	"fmt"
	"sync"

	"github.com/thoas/go-funk"
	"go.uber.org/zap"

	"github.com/epifi/gamma/api/actor"
	pb "github.com/epifi/gamma/api/investment/mutualfund/order"
	savingsPb "github.com/epifi/gamma/api/savings"
	userPb "github.com/epifi/gamma/api/user"
	wob "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

func (s *Service) getPreInvestmentDetail(ctx context.Context, orders []*pb.Order) (map[string]*wob.PreInvestmentDetail, error) {
	uniqueActorIDs := make(map[string]bool)

	for _, val := range orders {
		uniqueActorIDs[val.ActorId] = true
	}

	var uniqueActorIDsArray []string
	for actorId := range uniqueActorIDs {
		uniqueActorIDsArray = append(uniqueActorIDsArray, actorId)
	}

	chunkList := funk.Chunk(uniqueActorIDsArray, 1000)

	resp := &wob.GetInvestmentDataResponse{InvestmentDetailInfo: make(map[string]*wob.PreInvestmentDetail)}

	index := 0
	for {
		var wg sync.WaitGroup
		ch := make(chan *wob.GetInvestmentDataResponse)

		for i := 0; i < Threads; i++ {

			if index >= len(chunkList.([][]string)) {
				break
			}
			wg.Add(1)
			go s.getInvestmentData(ctx, chunkList.([][]string)[index], ch, &wg)
			index++
		}

		go func() {
			wg.Wait()
			close(ch)
		}()
		for val := range ch {
			resp.InvestmentDetailInfo = s.mergeInvestmentDetailInfoMaps(resp.InvestmentDetailInfo, val.GetInvestmentDetailInfo())
		}
		if index >= len(chunkList.([][]string)) {
			break
		}
	}

	/*
		ToDo(Junaid): Since we saw some issues in prod where wealth on-boarding either didn't have required data or were not
			able to return partial data, we were getting empty bank account number and customer name for some users. As a fallback,
			we are fetching these data from epifi tech systems (savings and user service). Remove this once wealth on-boarding has the
			capability to return partial data.
	*/
	for _, val := range orders {
		if !s.hasInvestmentData(resp.InvestmentDetailInfo[val.ActorId]) {
			logger.Info(ctx, "investment details not found for actor. Fetching bank account and name details from savings and user service ", zap.String(logger.ACTOR_ID, val.ActorId))
			userId, err2 := s.getUserIdFromActorId(ctx, val.ActorId)
			if err2 != nil {
				logger.Error(ctx, "couldn't get user id from actor id", zap.Error(err2), zap.String(logger.ACTOR_ID, val.ActorId),
					zap.String(logger.USER_ID, userId))
				continue
			}

			accountDetails, err2 := s.getBankAccountDetails(ctx, val.ActorId, userId)
			if err2 != nil {
				logger.Error(ctx, "error in getBankAccountDetails fallback call", zap.String(logger.ACTOR_ID, val.ActorId), zap.Error(err2),
					zap.String(logger.USER_ID, userId))
				continue
			}

			userDetails, err2 := s.getUserDetails(ctx, userId)
			if err2 != nil {
				logger.Error(ctx, "error in getUserDetails fallback call", zap.String(logger.ACTOR_ID, val.ActorId), zap.Error(err2),
					zap.String(logger.USER_ID, userId))
				continue
			}

			if resp.InvestmentDetailInfo[val.ActorId] == nil {
				resp.InvestmentDetailInfo[val.ActorId] = &wob.PreInvestmentDetail{}
			}
			resp.InvestmentDetailInfo[val.ActorId].BankDetails = &wob.BankDetails{IfscCode: accountDetails.IfscCode, AccountNumber: accountDetails.AccountNo}
			resp.InvestmentDetailInfo[val.ActorId].CustomerName = userDetails.Profile.PanName
		}
	}
	logger.Info(ctx, fmt.Sprintf("GetInvestmentData successfully fetched for %d actors", len(uniqueActorIDs)))
	return resp.InvestmentDetailInfo, nil
}

func (s *Service) mergeInvestmentDetailInfoMaps(m1 map[string]*wob.PreInvestmentDetail, m2 map[string]*wob.PreInvestmentDetail) map[string]*wob.PreInvestmentDetail {
	if len(m1) == 0 {
		return m2
	}
	if len(m2) == 0 {
		return m1
	}

	for key, val := range m2 {
		m1[key] = val
	}
	return m1
}

func (s *Service) getInvestmentData(ctx context.Context, actorIDs []string, ch chan *wob.GetInvestmentDataResponse, wg *sync.WaitGroup) {
	resp, err := s.wobClient.GetInvestmentData(ctx, &wob.GetInvestmentDataRequest{ActorIds: actorIDs})
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		logger.Error(ctx, "error in GetInvestmentData for ops file", zap.Strings(logger.ACTOR_ID_V2, actorIDs), zap.Error(err2))
		wg.Done()
		return
	}
	ch <- resp
	wg.Done()
}

func (s *Service) hasInvestmentData(investmentDetail *wob.PreInvestmentDetail) bool {
	if investmentDetail == nil ||
		investmentDetail.BankDetails == nil ||
		len(investmentDetail.BankDetails.IfscCode) == 0 ||
		len(investmentDetail.BankDetails.AccountNumber) == 0 || investmentDetail.CustomerName == nil ||
		len(investmentDetail.CustomerName.FirstName) == 0 {
		return false
	}
	return true
}

func (s *Service) getBankAccountDetails(ctx context.Context, _ string, userId string) (*savingsPb.Account, error) {

	accResp, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{PrimaryUserId: userId}})
	if err != nil {
		logger.Error(ctx, "error in GetAccount", zap.String(logger.USER_ID, userId), zap.Error(err))
		return nil, err
	}

	if accResp == nil || accResp.GetAccount() == nil {
		logger.Error(ctx, "nil response received from savings client for GetAccount rpc", zap.String(logger.USER_ID, userId))
		return nil, fmt.Errorf("nil response recieved from savings client for GetAccount rpc")
	}
	return accResp.GetAccount(), nil
}

func (s *Service) getUserDetails(ctx context.Context, userId string) (*userPb.User, error) {
	userRes, err := s.userClient.GetUser(ctx,
		&userPb.GetUserRequest{Identifier: &userPb.GetUserRequest_Id{Id: userId}})
	if te := epifigrpc.RPCError(userRes, err); te != nil {
		logger.Error(ctx, "error in GetUser", zap.String(logger.USER_ID, userId), zap.Error(te))
		return nil, te
	}

	if userRes == nil || userRes.User == nil || userRes.User.Profile == nil || userRes.User.Profile.PanName == nil {
		return nil, fmt.Errorf("nil response recieved from user client for GetUser rpc")
	}

	return userRes.User, nil
}

func (s *Service) getUserIdFromActorId(ctx context.Context, actorID string) (string, error) {
	resp, err := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{
		Id: actorID,
	})
	if err2 := epifigrpc.RPCError(resp, err); err2 != nil {
		logger.Error(ctx, "Couldn't fetch actor info", zap.Error(err2), zap.String(logger.ACTOR_ID, actorID))
		return "", err
	}
	return resp.Actor.GetEntityId(), nil
}
