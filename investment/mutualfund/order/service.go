// nolint
package order

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	errors2 "github.com/pkg/errors"

	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/be-common/pkg/queue"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/fittt"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	pb "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	kycPb "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/order/domain"
	rms "github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/savings"
	camsPb "github.com/epifi/gamma/api/simulator/wealth/mutualfund/cams"
	userPb "github.com/epifi/gamma/api/user"
	wealthOB "github.com/epifi/gamma/api/wealthonboarding"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	invErrors "github.com/epifi/gamma/investment/errors"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/gamma/investment/mutualfund/notifier"
	ltp "github.com/epifi/gamma/investment/mutualfund/order/lockin_type_processor"
	"github.com/epifi/gamma/investment/mutualfund/order/order_type_processor"
	sipLedgerUtil "github.com/epifi/gamma/investment/mutualfund/order/sip_ledger_util"
	updOrder "github.com/epifi/gamma/investment/mutualfund/order/update_order"
	vos "github.com/epifi/gamma/investment/mutualfund/order/vendor_order_sender"
	wtypes "github.com/epifi/gamma/investment/wire/types"
	invPkg "github.com/epifi/gamma/pkg/investment"
)

type UploadCreditMISToVendorPublisher queue.Publisher

type Service struct {
	pb.UnimplementedOrderManagerServer
	orderDao                          dao.OrderDao
	folioDao                          dao.FolioLedgerDao
	fileStateDao                      dao.FileStateDao
	mutualFundDao                     dao.MutualFundDao
	amcInfoDao                        dao.AmcInfoDao
	orderStatusDao                    dao.OrderStatusUpdateDao
	orderConfirmationInfoDao          dao.OrderConfirmationInfoDao
	orderRejectionInfoDao             dao.OrderRejectionInfoDao
	sipLedgerDao                      dao.SIPLedgerDao
	vendorOrderSenderFactory          *vos.VendorOrderSenderFactory
	fileGeneratorClient               filegenerator.FileGeneratorClient
	camss3Client                      s3.S3Client
	karvys3Client                     s3.S3Client
	paymentHandlerClient              phPb.PaymentHandlerClient
	catalogClient                     catalogPb.CatalogManagerClient
	cfg                               *genConf.Config
	savingsClient                     savings.SavingsClient
	actorClient                       actorPb.ActorClient
	commsClient                       comms.CommsClient
	uploadCreditMISToVendorPublisher  UploadCreditMISToVendorPublisher
	kycClient                         kycPb.KycClient
	lockinTypeProcessorFactory        *ltp.LockinProcessorFactory
	idempotentTxnExecutor             storagev2.IdempotentTxnExecutor
	userClient                        userPb.UsersClient
	doOnce                            once.DoOnce
	wealthOnboardingClient            wealthOB.WealthOnboardingClient
	fitttClient                       fittt.FitttClient
	deferredNotificationPublisher     wtypes.DeferredNotificationDelayPublisher
	orderDelayedNotificationPublisher wtypes.OrderDelayedNotificationDelayPublisher
	orderStatusNotifier               updOrder.OrderStatusNotifier
	buyOrderProcessor                 *order_type_processor.BuyOrderProcessor
	sellOrderProcessor                *order_type_processor.SellOrderProcessor
	bankCustClient                    bankcust.BankCustomerServiceClient
	orderETADelayPublisher            wtypes.OrderETADelayPublisher
	sipLedgerHelper                   sipLedgerUtil.ISIPLedgerHelper
}

// ToDo(Junaid): Use Cams and Karvy S3 clients separately. (https://monorail.pointz.in/p/fi-app/issues/detail?id=19788)

func NewService(orderDao dao.OrderDao, folioDao dao.FolioLedgerDao, fileStateDao dao.FileStateDao,
	mutualFundDao dao.MutualFundDao,
	amcInfoDao dao.AmcInfoDao,
	orderStatusDao dao.OrderStatusUpdateDao,
	orderRejectionInfoDao dao.OrderRejectionInfoDao,
	orderConfirmationInfoDao dao.OrderConfirmationInfoDao,
	sipLedgerDao dao.SIPLedgerDao,
	vendorOrderSenderFactory *vos.VendorOrderSenderFactory,
	camsS3Client wtypes.CamsS3Client,
	karvyS3Client wtypes.KarvyS3Client,
	paymentHandlerClient phPb.PaymentHandlerClient,
	catalogClient catalogPb.CatalogManagerClient,
	cfg *genConf.Config,
	fileGeneratorClient filegenerator.FileGeneratorClient,
	savingsClient savings.SavingsClient,
	actorClient actorPb.ActorClient,
	commsClient comms.CommsClient,
	uploadCreditMISToVendorPublisher UploadCreditMISToVendorPublisher,
	kycClient kycPb.KycClient,
	lockinTypeProcessorFactory *ltp.LockinProcessorFactory,
	idempotentTxnExecutor storagev2.IdempotentTxnExecutor,
	userClient userPb.UsersClient,
	doOnce once.DoOnce,
	wealthOnboardingClient wealthOB.WealthOnboardingClient,
	fitttClient fittt.FitttClient,
	deferredNotificationPublisher wtypes.DeferredNotificationDelayPublisher,
	orderDelayedNotificationPublisher wtypes.OrderDelayedNotificationDelayPublisher,
	orderStatusNotifier updOrder.OrderStatusNotifier,
	buyOrderProcessor *order_type_processor.BuyOrderProcessor,
	sellOrderProcessor *order_type_processor.SellOrderProcessor,
	bankCustClient bankcust.BankCustomerServiceClient,
	orderETADelayPublisher wtypes.OrderETADelayPublisher,
	sipLedgerHelper sipLedgerUtil.ISIPLedgerHelper,
) *Service {
	return &Service{
		orderDao:                          orderDao,
		folioDao:                          folioDao,
		fileStateDao:                      fileStateDao,
		mutualFundDao:                     mutualFundDao,
		orderStatusDao:                    orderStatusDao,
		amcInfoDao:                        amcInfoDao,
		orderConfirmationInfoDao:          orderConfirmationInfoDao,
		orderRejectionInfoDao:             orderRejectionInfoDao,
		sipLedgerDao:                      sipLedgerDao,
		vendorOrderSenderFactory:          vendorOrderSenderFactory,
		camss3Client:                      camsS3Client,
		karvys3Client:                     karvyS3Client,
		paymentHandlerClient:              paymentHandlerClient,
		catalogClient:                     catalogClient,
		cfg:                               cfg,
		fileGeneratorClient:               fileGeneratorClient,
		savingsClient:                     savingsClient,
		actorClient:                       actorClient,
		commsClient:                       commsClient,
		uploadCreditMISToVendorPublisher:  uploadCreditMISToVendorPublisher,
		kycClient:                         kycClient,
		lockinTypeProcessorFactory:        lockinTypeProcessorFactory,
		idempotentTxnExecutor:             idempotentTxnExecutor,
		userClient:                        userClient,
		doOnce:                            doOnce,
		wealthOnboardingClient:            wealthOnboardingClient,
		fitttClient:                       fitttClient,
		deferredNotificationPublisher:     deferredNotificationPublisher,
		orderDelayedNotificationPublisher: orderDelayedNotificationPublisher,
		orderStatusNotifier:               orderStatusNotifier,
		buyOrderProcessor:                 buyOrderProcessor,
		sellOrderProcessor:                sellOrderProcessor,
		bankCustClient:                    bankCustClient,
		orderETADelayPublisher:            orderETADelayPublisher,
		sipLedgerHelper:                   sipLedgerHelper,
	}
}

const (
	DB_TRANSACTION_COUNT                     = uint(3)
	ORDERS_BY_ACTOR_ID_AND_FUND_ID_PAGE_SIZE = 10
)

// GetOrders returns the orders given a list of either order IDs or a client order id
func (s *Service) GetOrders(ctx context.Context, req *pb.GetOrdersRequest) (*pb.GetOrdersResponse, error) {
	logger.Debug(ctx, "getOrders request for request", zap.Strings(logger.ORDER_IDS, req.GetOrderIds()))

	orders, err := s.getOrdersByIds(ctx, req.OrderIds, req.OrderIdType)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error fetching mutual fund orders ", zap.String(logger.REQUEST, req.String()), zap.Error(err))
		return &pb.GetOrdersResponse{Status: rpc.NewStatus(uint32(pb.GetOrderResponse_INTERNAL), "error fetching mutual fund orders", "")}, nil
	}

	ordersMap := make(map[string]*pb.OrderData)
	for _, mfOrder := range orders {
		ordersMap[mfOrder.Id] = &pb.OrderData{
			Order: mfOrder,
		}
	}
	return &pb.GetOrdersResponse{
		Status: rpc.StatusOk(),
		Orders: ordersMap,
	}, nil
}

func (s *Service) getOrdersByIds(ctx context.Context, orderIds []string, orderIdType pb.GetOrdersRequest_OrderIdType) ([]*pb.Order, error) {
	switch orderIdType {
	case pb.GetOrdersRequest_ORDER_ID_TYPE_ID:
		return s.orderDao.GetByOrderIds(ctx, orderIds)
	case pb.GetOrdersRequest_ORDER_ID_TYPE_CLIENT_ID:
		return s.orderDao.GetByClientOrderIds(ctx, orderIds)
	case pb.GetOrdersRequest_ORDER_ID_TYPE_EXTERNAL_ID:
		return s.orderDao.GetByExternalOrderIds(ctx, orderIds)
	case pb.GetOrdersRequest_ORDER_ID_TYPE_VENDOR_ID:
		return s.orderDao.GetByVendorOrderIds(ctx, orderIds)
	default:
		return nil, fmt.Errorf("invalid type of order id")
	}
}

func (s *Service) TriggerOrderProcessing(context.Context, *pb.TriggerOrderProcessingRequest) (*pb.TriggerOrderProcessingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TriggerOrderProcessing not implemented")
}

// nolint: funlen
func (s *Service) SendOrderToVendor(ctx context.Context, request *domain.ProcessFulfilmentRequest) (*domain.ProcessFulfilmentResponse, error) {

	req := &mfPb.MutualFundsInvestmentInfo{}
	var (
		res    = &domain.ProcessFulfilmentResponse{}
		header = &domain.DomainResponseHeader{}
	)
	res.ResponseHeader = header

	err := protojson.Unmarshal(request.GetPayload(), req)
	if err != nil {
		logger.Error(ctx, "Unable to unmarshal the request payload for 'SendOrderToVendor",
			zap.String(logger.REQUEST, request.String()),
			zap.Error(err))
		header.Status = domain.DomainProcessingStatus_PERMANENT_FAILURE
		return res, nil
	}
	logger.Info(ctx, "Received SendOrderToVendor request for request", zap.String(logger.ORDER_ID, req.GetOrderId()))

	order, err := s.orderDao.GetById(ctx, req.OrderId)
	if err != nil {
		logger.Error(ctx, "error while fetching order details by id ", zap.String(logger.ORDER_ID, req.OrderId), zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			header.Status = domain.DomainProcessingStatus_PERMANENT_FAILURE
		} else {
			header.Status = domain.GetStatusFrom(err)
		}
		return res, nil
	}

	vendorOrderSender, err := s.vendorOrderSenderFactory.GetVendorOrderSender(order.Rta)
	if err != nil {
		logger.Error(ctx, "error while finding vendor order sender", zap.String(logger.ORDER_ID, req.OrderId), zap.String(logger.RTA, order.Rta.String()), zap.Error(err))
		header.Status = domain.GetStatusFrom(err)
		return res, nil
	}

	orderStatus, err := vendorOrderSender.SendOrderToVendor(ctx, req.OrderId, request.GetRequestHeader().GetIsLastAttempt())
	if err != nil {
		logger.Error(ctx, "error while sending order to vendor", zap.String(logger.ORDER_ID, req.OrderId), zap.String(logger.RTA, order.Rta.String()), zap.Error(err))
		// If this is the last attempt then update order to failed
		if request.GetRequestHeader().IsLastAttempt {
			// If the file was not sent to the vendor then fail the order with forward feed upload failure
			if errors.Is(err, invErrors.ErrFileNotSendToVendor) || errors.Is(err, invErrors.ErrFileNotSendToVendorInternalError) {
				order.FailureReason = pb.FailureReason_FWD_ORDER_FEED_FILE_UPLOAD_ERROR
			} else {
				order.FailureReason = pb.FailureReason_INTERNAL_SERVER_ERROR
			}
			order.OrderStatus = pb.OrderStatus_MANUAL_INTERVENTION
			updErr := updOrder.UpdateOrder(ctx, s.orderDao, s.orderStatusDao, s.folioDao, order, []pb.OrderFieldMask{
				pb.OrderFieldMask_ORDER_STATUS,
				pb.OrderFieldMask_FAILURE_REASON,
			}, false, s.idempotentTxnExecutor, s.orderStatusNotifier)
			if updErr != nil {
				logger.Error(ctx, fmt.Sprintf("Failed to update orderId: %s", order.GetId()),
					zap.String(logger.ORDER_ID, order.GetId()), zap.Error(updErr))
				header.Status = domain.GetStatusFrom(err)
			}
		}
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			header.Status = domain.DomainProcessingStatus_PERMANENT_FAILURE
		} else {
			header.Status = domain.GetStatusFrom(err)
		}
		return res, nil
	}
	s.updateResponseHeaderFromOrderStatus(header, *orderStatus)
	return res, nil
}

// GetOrder returns the order given either an order id or a client order id
func (s *Service) GetOrder(ctx context.Context, req *pb.GetOrderRequest) (*pb.GetOrderResponse, error) {
	var orderResp *pb.Order
	switch req.GetId().(type) {
	case *pb.GetOrderRequest_OrderId:
		order, err := s.orderDao.GetById(ctx, req.GetOrderId())
		if err != nil {
			logger.Error(ctx, "failed to get order by id", zap.String(logger.ORDER_ID, req.GetOrderId()),
				zap.Error(err))
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &pb.GetOrderResponse{
					Status: rpc.StatusRecordNotFoundWithDebugMsg(err.Error()),
					Order:  nil,
				}, nil
			}
			return &pb.GetOrderResponse{
				Status: rpc.StatusInternal(),
				Order:  nil,
			}, nil
		}
		orderResp = order
	case *pb.GetOrderRequest_ClientOrderId:
		order, err := s.orderDao.GetByClientOrderId(ctx, req.GetClientOrderId())
		if err != nil {
			logger.Error(ctx, "failed to get order by client order id", zap.String(logger.ORDER_ID,
				req.GetClientOrderId()), zap.Error(err))
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &pb.GetOrderResponse{
					Status: rpc.StatusRecordNotFoundWithDebugMsg(err.Error()),
					Order:  nil,
				}, nil
			}
			return &pb.GetOrderResponse{
				Status: rpc.StatusInternal(),
				Order:  nil,
			}, nil
		}
		orderResp = order
	case *pb.GetOrderRequest_ExternalOrderId:
		order, err := s.orderDao.GetByExternalOrderId(ctx, req.GetExternalOrderId())
		if err != nil {
			logger.Error(ctx, "failed to get order by external order id", zap.String(logger.ORDER_ID,
				req.GetExternalOrderId()), zap.Error(err))
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &pb.GetOrderResponse{
					Status: rpc.StatusRecordNotFoundWithDebugMsg(err.Error()),
					Order:  nil,
				}, nil
			}
			return &pb.GetOrderResponse{
				Status: rpc.StatusInternal(),
				Order:  nil,
			}, nil
		}
		orderResp = order
	case *pb.GetOrderRequest_VendorOrderId:
		order, err := s.orderDao.GetByVendorOrderId(ctx, req.GetVendorOrderId())
		if err != nil {
			logger.Error(ctx, "failed to get order by vendor order id", zap.String(logger.ORDER_ID,
				req.GetVendorOrderId()), zap.Error(err))
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &pb.GetOrderResponse{
					Status: rpc.StatusRecordNotFoundWithDebugMsg(err.Error()),
					Order:  nil,
				}, nil
			}
			return &pb.GetOrderResponse{
				Status: rpc.StatusInternal(),
				Order:  nil,
			}, nil
		}
		orderResp = order
	}
	return &pb.GetOrderResponse{
		Status: rpc.StatusOk(),
		Order:  orderResp,
	}, nil
}

// TODO(Ayush) Add tests
//
//nolint:funlen
func (s *Service) GetOrderDetails(ctx context.Context, req *pb.GetOrderDetailsRequest) (*pb.GetOrderDetailsResponse, error) {
	// Get Order
	order, err := s.getOrderForOrderDetailsRequest(ctx, req)
	if err != nil {
		return &pb.GetOrderDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// Get Mutual fund info
	mfInfo, err := s.fetchMutualFundInfo(ctx, order.GetMutualFundId())
	if err != nil {
		logger.Error(ctx, "failed to get mutual fund", zap.String(logger.ORDER_ID,
			order.GetId()), zap.String(logger.MF_ID, order.GetMutualFundId()),
			zap.Error(err))
		return &pb.GetOrderDetailsResponse{
			Status: &rpc.Status{
				Code:         uint32(pb.GetOrderDetailsResponse_INTERNAL),
				DebugMessage: err.Error(),
			},
		}, nil
	}

	// Get Amc info
	amcResp, err := s.catalogClient.GetAmcInfoByAMC(ctx, &catalogPb.GetAmcInfoByAMCRequest{Amc: order.GetAmc()})
	if err != nil {
		logger.Error(ctx, "failed to get amc info for order", zap.String(logger.ORDER_ID,
			order.GetId()), zap.String(logger.MF_AMC, order.GetAmc().String()), zap.Error(err))
		return &pb.GetOrderDetailsResponse{
			Status: &rpc.Status{
				Code:         uint32(pb.GetOrderDetailsResponse_INTERNAL),
				DebugMessage: err.Error(),
			},
		}, nil
	}
	if !amcResp.GetStatus().IsSuccess() {
		logger.Error(ctx, "failed to get amc info for order", zap.String(logger.ORDER_ID,
			order.GetId()), zap.String(logger.MF_AMC, order.GetAmc().String()), zap.Error(err))
		return &pb.GetOrderDetailsResponse{
			Status: &rpc.Status{
				Code:         uint32(pb.GetOrderDetailsResponse_INTERNAL),
				DebugMessage: amcResp.GetStatus().GetDebugMessage(),
				ShortMessage: amcResp.GetStatus().GetShortMessage(),
			},
		}, nil
	}

	// Get payment info
	payMode := convertToPaymentHandlerPaymentMode(order.PaymentMode)
	payInfo, err := s.paymentHandlerClient.GetPaymentDetails(ctx, &phPb.GetPaymentDetailsRequest{
		OrderId:     order.GetId(),
		PaymentMode: payMode,
	})
	if err != nil {
		logger.Error(ctx, "failed to get payment info for order", zap.String(logger.ORDER_ID,
			order.GetId()), zap.Error(err))
		return &pb.GetOrderDetailsResponse{
			Status: &rpc.Status{
				Code:         uint32(pb.GetOrderDetailsResponse_INTERNAL),
				DebugMessage: err.Error(),
			},
		}, nil
	}
	if !payInfo.GetStatus().IsSuccess() && !payInfo.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "failed to get payment info for order", zap.String(logger.ORDER_ID,
			order.GetId()), zap.Error(err))
		return &pb.GetOrderDetailsResponse{
			Status: &rpc.Status{
				Code:         uint32(pb.GetOrderDetailsResponse_INTERNAL),
				DebugMessage: payInfo.GetStatus().DebugMessage,
				ShortMessage: payInfo.GetStatus().GetShortMessage(),
			},
		}, nil
	}

	// Get status Timeline
	statusUpd, err := s.orderStatusDao.GetStatusUpdatesByOrderId(ctx, order.GetId())
	if err != nil {
		logger.Error(ctx, "failed to get order status timeline", zap.String(logger.ORDER_ID, order.GetId()),
			zap.Error(err))
		return &pb.GetOrderDetailsResponse{
			Status: &rpc.Status{
				Code:         uint32(pb.GetOrderDetailsResponse_INTERNAL),
				DebugMessage: err.Error(),
			},
		}, nil
	}
	statusTimeline := getTimelineFromStatusUpdates(statusUpd)

	orderConfirmationInfo, err := s.orderConfirmationInfoDao.GetByOrderId(ctx, order.GetId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return &pb.GetOrderDetailsResponse{
			Status: &rpc.Status{
				Code:         uint32(pb.GetOrderDetailsResponse_INTERNAL),
				DebugMessage: err.Error(),
			},
		}, nil
	}

	return &pb.GetOrderDetailsResponse{
		Status:                rpc.StatusOk(),
		Order:                 order,
		MutualFund:            mfInfo,
		AmcName:               amcResp.GetAmcInfo().GetAmcName(),
		OrderStatusTimeline:   statusTimeline,
		PaymentStatus:         payInfo.GetPaymentStatus(),
		UtrRefNumber:          payInfo.GetUtrRefNumber(),
		TransactionTime:       payInfo.GetTransactionTime(),
		OrderConfirmationInfo: orderConfirmationInfo,
	}, nil
}

func convertToPaymentHandlerPaymentMode(mode pb.PaymentMode) phPb.PaymentMode {
	switch mode {
	case pb.PaymentMode_SI:
		return phPb.PaymentMode_PAYMENT_MODE_SI
	case pb.PaymentMode_P2P_TRANSFER:
		return phPb.PaymentMode_P2P_TRANSFER
	default:
		return phPb.PaymentMode_PAYMENT_MODE_UNSPECIFIED
	}
}

func (s *Service) getOrderForOrderDetailsRequest(ctx context.Context, req *pb.GetOrderDetailsRequest) (*pb.Order, error) {
	var orderResp *pb.Order
	switch req.GetId().(type) {
	case *pb.GetOrderDetailsRequest_OrderId:
		order, err := s.orderDao.GetById(ctx, req.GetOrderId())
		if err != nil {
			logger.Error(ctx, "failed to get order by id", zap.String(logger.ORDER_ID, req.GetOrderId()),
				zap.Error(err))
			return nil, err
		}
		orderResp = order
	case *pb.GetOrderDetailsRequest_ClientOrderId:
		order, err := s.orderDao.GetByClientOrderId(ctx, req.GetClientOrderId())
		if err != nil {
			logger.Error(ctx, "failed to get order by client order id", zap.String(logger.ORDER_ID, req.GetClientOrderId()),
				zap.Error(err))
			return nil, err
		}
		orderResp = order
	case *pb.GetOrderDetailsRequest_ExternalOrderId:
		order, err := s.orderDao.GetByExternalOrderId(ctx, req.GetExternalOrderId())
		if err != nil {
			logger.Error(ctx, "failed to get order by external order id", zap.String(logger.ORDER_ID, req.GetExternalOrderId()),
				zap.Error(err))
			return nil, err
		}
		orderResp = order
	case *pb.GetOrderDetailsRequest_VendorOrderId:
		order, err := s.orderDao.GetByVendorOrderId(ctx, req.GetVendorOrderId())
		if err != nil {
			logger.Error(ctx, "failed to get order by vendor order id", zap.String(logger.ORDER_ID, req.GetVendorOrderId()),
				zap.Error(err))
			return nil, err
		}
		orderResp = order
	}
	return orderResp, nil
}

func getTimelineFromStatusUpdates(records []*pb.OrderStatusUpdateRecord) []*pb.OrderStatusUpdate {
	timeline := make([]*pb.OrderStatusUpdate, 0)
	for _, rec := range records {
		timeline = append(timeline, &pb.OrderStatusUpdate{
			OrderStatus: rec.GetOrderStatus(),
			UpdatedAt:   rec.GetUpdatedAt(),
		})
	}
	return timeline
}

func (s *Service) fetchMutualFundInfo(ctx context.Context, mutualFundId string) (*mfPb.MutualFund, error) {
	fundInfo, err := s.mutualFundDao.GetById(ctx, mutualFundId)
	if err != nil {
		logger.Error(ctx, "Error in fetching mutual fund", zap.String(logger.MF_ID, mutualFundId))
		return nil, err
	}
	return fundInfo, err
}

// nolint: funlen
func (s *Service) UpdateOrderStatusWithCheck(ctx context.Context, request *pb.UpdateOrderStatusWithCheckRequest) (*pb.UpdateOrderStatusWithCheckResponse, error) {
	logger.Info(ctx, fmt.Sprintf("received UpdateOrderStatusWithCheck request :%s", request.String()))

	if request.GetCurrentOrderStatus() == request.GetNewOrderStatus() {
		logger.Info(ctx, fmt.Sprintf("same current and new status. skipping update. Order status %s ", request.NewOrderStatus.String()))
		return &pb.UpdateOrderStatusWithCheckResponse{Status: &rpc.Status{
			Code: uint32(pb.UpdateOrderStatusWithCheckResponse_OK),
		}}, nil
	}

	var updateMasks []pb.OrderFieldMask
	updateMasks = append(updateMasks, pb.OrderFieldMask_ORDER_STATUS)

	if request.NewOrderStatus == pb.OrderStatus_FAILURE || request.NewOrderStatus == pb.OrderStatus_MANUAL_INTERVENTION {
		updateMasks = append(updateMasks, pb.OrderFieldMask_FAILURE_REASON)
	}

	orderId := request.OrderId
	var (
		order *pb.Order
		err   error
	)
	if request.GetOrderIdentifier() != nil {
		switch request.GetOrderIdentifier().(type) {
		case *pb.UpdateOrderStatusWithCheckRequest_Id:
			orderId = request.OrderIdentifier.(*pb.UpdateOrderStatusWithCheckRequest_Id).Id
			order, err = s.orderDao.GetById(ctx, orderId)
			if err != nil {
				logger.Error(ctx, "error in fetching order by  order id", zap.String(logger.ORDER_ID, orderId), zap.Error(err))
				return &pb.UpdateOrderStatusWithCheckResponse{Status: &rpc.Status{
					Code:         uint32(pb.UpdateOrderStatusWithCheckResponse_INTERNAL),
					ShortMessage: "error in fetching order by order id",
					DebugMessage: err.Error(),
				}}, nil
			}
		case *pb.UpdateOrderStatusWithCheckRequest_VendorOrderId:
			vendorOrderId := request.OrderIdentifier.(*pb.UpdateOrderStatusWithCheckRequest_VendorOrderId).VendorOrderId
			order, err = s.orderDao.GetByVendorOrderId(ctx, vendorOrderId)
			if err != nil {
				logger.Error(ctx, "error in fetching order by vendor order id", zap.String(logger.VENDOR_ORDER_ID, vendorOrderId), zap.Error(err))
				return &pb.UpdateOrderStatusWithCheckResponse{Status: &rpc.Status{
					Code:         uint32(pb.UpdateOrderStatusWithCheckResponse_INTERNAL),
					ShortMessage: "error in fetching order by vendor order id",
					DebugMessage: err.Error(),
				}}, nil
			}
			orderId = order.Id
		}
	}

	txnErr := s.idempotentTxnExecutor.RunIdempotentTxn(ctx, DB_TRANSACTION_COUNT, func(txnCtx context.Context) error {
		// ToDO(Junaid): Migrate this to UpdateOrder wrapper function
		terr := s.orderDao.UpdateWithStatusCheck(txnCtx, &pb.Order{
			Id:            orderId,
			OrderStatus:   request.NewOrderStatus,
			PayFailReason: request.PaymentFailureReason,
			FailureReason: request.FailureReason,
		}, request.CurrentOrderStatus, updateMasks)
		if terr != nil {
			return errors2.Wrap(terr, "failed to update order status")
		}
		_, terr = s.orderStatusDao.Create(txnCtx, &pb.OrderStatusUpdateRecord{
			OrderId:     orderId,
			OrderStatus: request.NewOrderStatus,
		})
		if terr != nil {
			return errors2.Wrap(terr, "error creating order status update")
		}
		// ToDo(Junaid): Migrate this to observer pattern
		uErr := s.sipLedgerHelper.UpdateSipLedgerIfNeeded(txnCtx, order)
		if uErr != nil {
			return errors2.Wrap(uErr, "error updating sip ledger")
		}
		folioErr := s.updateFolioStatusIfNeeded(txnCtx, order, request)
		if folioErr != nil {
			return errors2.Wrap(folioErr, "error updating folio status")
		}
		goroutine.Run(ctx, 5*time.Minute, func(ctx context.Context) {
			s.orderStatusNotifier.NotifyOrderUpdate(ctx, []string{orderId}, request.NewOrderStatus, request.FailureReason, "")
		})
		return nil
	})

	shortMessage := ""
	if txnErr != nil {
		logger.Error(ctx, "error in UpdateOrderStatusWithCheck dao call", zap.String(logger.ORDER_ID, orderId), zap.Error(txnErr))
		if errors.Is(txnErr, epifierrors.ErrRowNotUpdated) {
			orderDetails, err2 := s.orderDao.GetById(ctx, orderId)
			if err2 != nil {
				logger.Error(ctx, "order update failed because order was not found in db", zap.String(logger.ORDER_ID, orderId), zap.Error(err2))
			} else if orderDetails.GetOrderStatus() == request.GetNewOrderStatus() {
				return &pb.UpdateOrderStatusWithCheckResponse{Status: rpc.StatusAlreadyExistsWithDebugMsg("order already in requested state")}, nil
			} else {
				shortMessage = fmt.Sprintf("current order status in request is %s and actual current orders status in db is %s",
					request.CurrentOrderStatus, orderDetails.OrderStatus)
				logger.Error(ctx, shortMessage, zap.String(logger.ORDER_ID, orderId), zap.Error(txnErr))
			}
		}

		return &pb.UpdateOrderStatusWithCheckResponse{Status: &rpc.Status{
			Code:         uint32(pb.UpdateOrderStatusWithCheckResponse_INTERNAL),
			ShortMessage: shortMessage,
			DebugMessage: txnErr.Error(),
		}}, nil
	}

	return &pb.UpdateOrderStatusWithCheckResponse{Status: &rpc.Status{
		Code: uint32(pb.UpdateOrderStatusWithCheckResponse_OK),
	}}, nil
}

// updateFolioStatusIfNeeded updates the folio status back to active if an order that has OrderSubType_SELL_FULL_REDEMPTION_WITH_FOLIO_CLOSURE
// moves to failure so that new buy/sell orders can be placed with the same folio
func (s *Service) updateFolioStatusIfNeeded(ctx context.Context, order *pb.Order, request *pb.UpdateOrderStatusWithCheckRequest) error {
	if order.GetOrderSubType() == pb.OrderSubType_SELL_FULL_REDEMPTION_WITH_FOLIO_CLOSURE && request.NewOrderStatus == pb.OrderStatus_FAILURE {
		folios, err := s.folioDao.GetByFilterOptions(ctx, mfPb.FolioLedgerMask_CREATED_AT, true,
			dao.WithActorId(order.GetActorId()),
			dao.WithFolioId(order.GetFolioId()),
			dao.WithMutualFundId(order.GetMutualFundId()),
		)
		if err != nil {
			return errors2.Wrap(err, "failed to get folio by filter")
		}
		if len(folios) != 1 {
			return fmt.Errorf("invalid number of folios present: %d", len(folios))
		}
		folios[0].Status = mfPb.FolioStatus_FolioStatus_ACTIVE
		_, err = s.folioDao.Update(ctx, folios[0], []mfPb.FolioLedgerMask{mfPb.FolioLedgerMask_FOLIO_STATUS})
		if err != nil {
			return errors2.Wrap(err, "failed to update folio")
		}
		return nil
	}
	return nil
}

//nolint:funlen
func (s *Service) GetOrdersByFundIDAndActorID(ctx context.Context, request *pb.GetOrdersByFundIDAndActorIDRequest) (*pb.GetOrdersByFundIDAndActorIDResponse, error) {

	logger.Debug(ctx, "received GetOrdersByFundIDAndActorID", zap.String(logger.ACTOR_ID, request.ActorId))

	mutualFund, err := s.mutualFundDao.GetById(ctx, request.MutualFundId)
	if err != nil {
		logger.Error(ctx, "failed to get mutual fund", zap.String(logger.ACTOR_ID, request.ActorId),
			zap.String(logger.MUTUAL_FUND, request.GetMutualFundId()), zap.Error(err))
		return &pb.GetOrdersByFundIDAndActorIDResponse{Status: &rpc.Status{Code: uint32(pb.GetOrdersByFundIDAndActorIDResponse_INTERNAL)}}, nil

	}

	token, err := pagination.GetPageToken(request.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.String(logger.ACTOR_ID, request.ActorId),
			zap.String(logger.MUTUAL_FUND, request.GetMutualFundId()), zap.Error(err))
		return &pb.GetOrdersByFundIDAndActorIDResponse{Status: &rpc.Status{Code: uint32(pb.GetOrdersByFundIDAndActorIDResponse_INTERNAL)}}, nil
	}

	var totalOrders int32 = 0
	if token == nil {
		totalOrders, err = s.orderDao.GetOrderCountByMutualFundAndActorId(ctx, request.ActorId, request.MutualFundId)
		if err != nil {
			logger.Error(ctx, "failed to fetch order count", zap.String(logger.ACTOR_ID, request.ActorId),
				zap.String(logger.MUTUAL_FUND, request.GetMutualFundId()),
				zap.Error(err))
			return &pb.GetOrdersByFundIDAndActorIDResponse{Status: &rpc.Status{Code: uint32(pb.GetOrdersByFundIDAndActorIDResponse_INTERNAL)}}, nil
		}
		if totalOrders == 0 {
			logger.Info(ctx, "total orders is zero for the given actorId and mutualFundId", zap.String(logger.ACTOR_ID, request.ActorId),
				zap.String(logger.MUTUAL_FUND, request.GetMutualFundId()))
			return &pb.GetOrdersByFundIDAndActorIDResponse{
				Status:      rpc.StatusOk(),
				PageContext: nil,
				Orders:      []*pb.Order{},
				TotalOrders: 0,
				MutualFund:  mutualFund,
			}, nil
		}
	}

	var pageSize uint32
	if request.PageContext != nil {
		pageSize = request.PageContext.PageSize
	} else {
		pageSize = ORDERS_BY_ACTOR_ID_AND_FUND_ID_PAGE_SIZE
	}
	response := &pb.GetOrdersByFundIDAndActorIDResponse{}
	filterOptions := make([]storagev2.FilterOption, 0)
	filterOptions = append(filterOptions, dao.WithActorId(request.ActorId), dao.WithMutualFundId(request.MutualFundId))
	if request.Filters != nil && len(request.Filters) > 0 {
		options, filterErr := s.getFilterOptionsFromFilterField(request.GetFilters())
		if filterErr != nil {
			logger.Error(ctx, "invalid filter error", zap.Error(filterErr))
			return &pb.GetOrdersByFundIDAndActorIDResponse{Status: &rpc.Status{Code: uint32(pb.GetOrdersByFundIDAndActorIDResponse_INTERNAL)}}, nil
		}
		filterOptions = append(filterOptions, options...)
	}
	orders, pageContext, err := s.orderDao.GetOrdersByFilters(ctx, token, pageSize, filterOptions...)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "no orders found", zap.String(logger.ACTOR_ID, request.ActorId),
				zap.String(logger.MUTUAL_FUND, request.GetMutualFundId()), zap.Error(err))
			return &pb.GetOrdersByFundIDAndActorIDResponse{
				Status:      rpc.StatusOk(),
				TotalOrders: 0,
				PageContext: pageContext,
				MutualFund:  mutualFund,
				Orders:      []*pb.Order{},
			}, nil
		}
		logger.Error(ctx, "error in GetOrderIdsByMutualFundIdAndActorId", zap.String(logger.ACTOR_ID, request.ActorId),
			zap.String(logger.MUTUAL_FUND, request.GetMutualFundId()), zap.Error(err))
		return &pb.GetOrdersByFundIDAndActorIDResponse{Status: &rpc.Status{Code: uint32(pb.GetOrdersByFundIDAndActorIDResponse_INTERNAL)}}, nil
	}

	response.TotalOrders = totalOrders
	response.Orders = orders
	response.PageContext = pageContext
	response.MutualFund = mutualFund
	response.Status = &rpc.Status{Code: uint32(pb.GetOrdersByFundIDAndActorIDResponse_OK)}
	return response, nil
}

func (s *Service) findAvgPurchaseNav(balanceUnits float64, newUnits float64,
	latestNav *moneyPb.Money, avgPurchaseNav *moneyPb.Money) (*moneyPb.Money, error) {
	var err error
	var newAvgPrice *moneyPb.Money
	if avgPurchaseNav == nil || money.IsZero(avgPurchaseNav) {
		newAvgPrice = latestNav
	} else {
		/*
			Average Price is defined as
			newAvgPrice = (balanceUnits * currentAvgPrice + newAllocatedUnits * newAllocatedNav)/(balanceUnits + newAllocatedUnits)
		*/

		newAvgPrice, err = money.Sum(money.Multiply(latestNav, decimal.NewFromFloat(newUnits)), money.Multiply(avgPurchaseNav, decimal.NewFromFloat(balanceUnits)))
		if err != nil {
			return nil, err
		}
		newAvgPriceInDecimal := money.ToDecimal(newAvgPrice)
		totalUnits := decimal.Sum(decimal.NewFromFloat(newUnits), decimal.NewFromFloat(balanceUnits))
		newAvgPriceInDecimal = newAvgPriceInDecimal.Div(totalUnits)
		newAvgPrice = money.ParseDecimal(newAvgPriceInDecimal, money.RupeeCurrencyCode)
	}

	return newAvgPrice, nil
}

func (s *Service) updateResponseHeaderFromOrderStatus(header *domain.DomainResponseHeader, orderStatus pb.OrderStatus) {
	switch orderStatus {
	case pb.OrderStatus_SENT_TO_RTA:
		header.Status = domain.DomainProcessingStatus_IN_PROGRESS

	case pb.OrderStatus_ACCEPTED_BY_RTA:
		header.Status = domain.DomainProcessingStatus_SUCCESS

	case pb.OrderStatus_FAILURE:
		header.Status = domain.DomainProcessingStatus_TRANSIENT_FAILURE

	default:
		header.Status = domain.DomainProcessingStatus_TRANSIENT_FAILURE
	}
}

func (s *Service) getFilterOptionsFromFilterField(filters []*pb.Filter) ([]storagev2.FilterOption, error) {
	filterOptions := make([]storagev2.FilterOption, 0)
	for _, filter := range filters {
		switch filter.FilterField {
		case pb.FilterFieldMask_ORDER_CLIENT:
			switch filter.Comparator {
			case pb.Filter_EQUAL:
				filterOptions = append(filterOptions, dao.WithOrderClient(filter.GetOrderClientVal()))
			default:
				return nil, fmt.Errorf("unsupported filter for OrderClient %v", filter.Comparator)
			}
		case pb.FilterFieldMask_CREATED_AT:
			switch filter.Comparator {
			case pb.Filter_GREATER_OR_EQUAL:
				filterOptions = append(filterOptions, dao.WithMfOrdersCreatedAtGreaterOrEqual(filter.GetTimeVal()))
			case pb.Filter_SMALLER_OR_EQUAL:
				filterOptions = append(filterOptions, dao.WithMfOrdersCreatedAtLesserOrEqual(filter.GetTimeVal()))
			default:
				return nil, fmt.Errorf("unsupported filter for created_dt %v", filter.Comparator)
			}
		case pb.FilterFieldMask_EXTERNAL_ORDER_ID:
			switch filter.Comparator {
			case pb.Filter_EQUAL:
				filterOptions = append(filterOptions, dao.WithExternalOrderId(filter.GetStringVal()))
			default:
				return nil, fmt.Errorf("unsupported filter for external_order_id %v", filter.Comparator)
			}
		case pb.FilterFieldMask_ORDER_TYPE:
			switch filter.Comparator {
			case pb.Filter_EQUAL:
				filterOptions = append(filterOptions, dao.WithOrderType(filter.GetOrderTypeVal()))
			default:
				return nil, fmt.Errorf("unsupported filter for OrderClient %v", filter.Comparator)
			}
		case pb.FilterFieldMask_STATUS:
			switch filter.Comparator {
			case pb.Filter_EQUAL:
				filterOptions = append(filterOptions, dao.WithOrderStatus(filter.GetOrderStatusVal().String()))
			default:
				return nil, fmt.Errorf("unsupported filter for OrderClient %v", filter.Comparator)
			}

		default:
			return nil, fmt.Errorf("unsupported filter %v", filter.FilterField)
		}
	}
	return filterOptions, nil
}

func (s *Service) GetOrdersInCreditMISFile(ctx context.Context, request *pb.GetOrdersInCreditMISFileRequest) (*pb.GetOrdersInCreditMISFileResponse, error) {
	logger.Debug(ctx, "received GetOrdersInCreditMISFile", zap.String(logger.FILE_NAME, request.FileName))

	fileName := strings.TrimSpace(request.FileName)
	if fileName == "" {
		logger.Error(ctx, "empty file name")
		return &pb.GetOrdersInCreditMISFileResponse{Status: &rpc.Status{
			Code:         uint32(pb.GetOrdersInCreditMISFileResponse_INTERNAL),
			ShortMessage: "Empty file name",
		}}, nil
	}

	orders, err := s.getOrdersFromFileName(ctx, request.Rta, fileName)
	if err != nil {
		logger.Error(ctx, "failed to get order details from file name", zap.String(logger.FILE_NAME, request.FileName), zap.Error(err))
		return &pb.GetOrdersInCreditMISFileResponse{Status: &rpc.Status{
			Code:         uint32(pb.GetOrdersInCreditMISFileResponse_INTERNAL),
			ShortMessage: "Failed to get order details from file name",
		}}, nil
	}

	return &pb.GetOrdersInCreditMISFileResponse{Status: rpc.StatusOk(), Orders: orders}, nil
}

func (s *Service) getOrdersFromFileName(ctx context.Context, rta commonvgpb.Vendor, fileName string) ([]*pb.Order, error) {
	// Fetch file ID from file name
	creditMISFileState, err := s.fileStateDao.GetActiveFileByName(
		ctx, rta, pb.FileType_FILE_TYPE_CREDIT_MIS_REPORT_FILE, fileName)
	if err != nil {
		logger.Error(ctx, "failed to get file ID", zap.String(logger.FILE_NAME, fileName), zap.Error(err))
		return nil, err
	}
	fileId := creditMISFileState.GetFileDetailsMetaData().GetFileGeneratorId()

	// Fetch order IDs from file ID
	entityDetailsResponse, err := s.fileGeneratorClient.GetEntityDetailsForFile(
		ctx, &filegenerator.GetEntityDetailsForFileRequest{FileId: fileId})
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("Unable to fetch entity details for file ID: %s", fileId), zap.Error(err))
		return nil, err
	}
	if !entityDetailsResponse.Status.IsSuccess() {
		logger.Error(ctx, fmt.Sprintf(
			"Unable to fetch entity details for file ID: %s with debugMessage: %s and shortMessage:%s",
			fileId, entityDetailsResponse.Status.DebugMessage, entityDetailsResponse.Status.ShortMessage), zap.Error(err))
		return nil, err
	}
	var orderIds []string
	for entityId, entityFileGenStatus := range entityDetailsResponse.EntityIdFileGenStatusMap {
		if entityFileGenStatus == filegenerator.EntityFileGenStatus_ENTITY_FILE_GEN_STATUS_UPLOADED {
			orderIds = append(orderIds, entityId)
		}
	}
	orders, err := s.orderDao.GetByOrderIds(ctx, orderIds)
	if err != nil {
		logger.Error(ctx, "failed to get order details", zap.Strings(logger.ORDER_IDS, orderIds), zap.Error(err))
		return nil, err
	}

	return orders, nil
}

func (s *Service) GetMFInvestmentsForActor(ctx context.Context, request *pb.GetMFInvestmentsForActorRequest) (*pb.GetMFInvestmentsForActorResponse, error) {
	logger.Debug(ctx, "received GetMFInvestmentsForActor", zap.String(logger.ACTOR_ID, request.ActorId))

	mfIds, dbErr := s.orderDao.GetInvestedMfIdsByActorIdAndMfIds(ctx, request.GetActorId(), request.GetMfIds())
	if dbErr != nil {
		logger.Error(ctx, "failed to get MF IDs by actor ID", zap.String(logger.ACTOR_ID, request.ActorId), zap.Error(dbErr))
		return &pb.GetMFInvestmentsForActorResponse{Status: &rpc.Status{
			Code:         uint32(pb.GetMFInvestmentsForActorResponse_INTERNAL),
			ShortMessage: "Failed to get MFs user has invested in",
		}}, nil
	}

	investments := map[string]*pb.InvestmentsInfo{}
	for _, mfId := range mfIds {
		investments[mfId] = &pb.InvestmentsInfo{Invested: true}
	}

	return &pb.GetMFInvestmentsForActorResponse{Status: rpc.StatusOk(), Investments: investments}, nil
}

func (s *Service) notifyBuyOrdersProcessedSuccessfully(ctx context.Context, orders []*pb.Order) error {
	for _, order := range orders {
		actorRes, actorErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: order.ActorId})
		if err := epifigrpc.RPCError(actorRes, actorErr); err != nil {
			logger.Error(ctx, "unable to get actor by ID", zap.String(logger.ACTOR_ID_V2, order.ActorId), zap.Error(err))
			return err
		}
		userId := actorRes.GetActor().GetEntityId()

		// check if order is a FIT daily auto-invest rule based order
		var orderFitttRuleType rms.RuleTypeForSpecialHandling
		if order.GetClientOrderId() != "" {
			ruleDetailsRes, ruleDetailsErr := s.fitttClient.GetRuleDetails(ctx, &fittt.GetRuleDetailsRequest{ActionExecId: order.ClientOrderId})
			if err := epifigrpc.RPCError(ruleDetailsRes, ruleDetailsErr); err != nil {
				logger.Error(ctx, "unable to get rule details by client order id", zap.String(logger.CLIENT_ORDER_ID, order.ClientOrderId), zap.Error(err))
				return err
			}
			orderFitttRuleType = ruleDetailsRes.GetRule().GetRuleTypeForSpecialHandling()
		}

		var notifierErr error
		if orderFitttRuleType == rms.RuleTypeForSpecialHandling_RULE_TYPE_AUTO_INVEST_DAILY {
			notificationBody := s.cfg.MutualFundsNotificationParams().PortfolioUpdatedDailyDeeds().Body()
			notifierErr = notifier.SendWeeklyPortfolioUpdatedNotificationForDailyAutoInvestRules(ctx, s.cfg, s.deferredNotificationPublisher, s.doOnce, userId, notificationBody)
		} else {
			notificationBody := s.cfg.MutualFundsNotificationParams().PortfolioUpdated().Body()
			notifierErr = notifier.SendPortfolioUpdatedNotification(ctx, s.cfg, s.commsClient, s.doOnce, userId, notificationBody)
		}
		if notifierErr != nil {
			logger.Error(ctx, "error notifying user", zap.Error(notifierErr))
			return notifierErr
		}
	}
	return nil
}

func (s *Service) notifySellOrdersProcessedSuccessfully(ctx context.Context, orders []*pb.Order) error {
	for _, order := range orders {
		actorRes, actorErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: order.ActorId})
		if err := epifigrpc.RPCError(actorRes, actorErr); err != nil {
			logger.Error(ctx, "unable to get actor by ID", zap.String(logger.ACTOR_ID, order.ActorId), zap.Error(err))
			return err
		}
		userId := actorRes.GetActor().GetEntityId()
		amount, _ := money.ToDecimal(order.Amount).Float64()
		mfRes, mfErr := s.catalogClient.GetMutualFund(ctx, &catalogPb.GetMutualFundRequest{Id: order.MutualFundId})
		if err := epifigrpc.RPCError(mfRes, mfErr); err != nil {
			logger.Error(ctx, "unable to get MF by ID", zap.String(logger.MF_ID, order.MutualFundId), zap.Error(err))
			return err
		}
		paymentRes, paymentErr := s.paymentHandlerClient.GetPaymentDetails(ctx, &phPb.GetPaymentDetailsRequest{
			OrderId:     order.GetId(),
			PaymentMode: phPb.PaymentMode(order.GetPaymentMode()),
		})
		if err := epifigrpc.RPCError(paymentRes, paymentErr); err != nil {
			logger.Error(ctx, "unable to get payment for order", zap.String(logger.ORDER_ID, order.GetId()), zap.Error(err))
			if !rpc.StatusFromError(err).IsRecordNotFound() {
				return err
			}
		}
		eta, etaErr := invPkg.GetETADate(invPkg.ETAParams{
			PendingOrder:  order,
			AssetClass:    mfRes.MutualFund.AssetClass,
			PaymentStatus: paymentRes.GetPaymentStatus(),
			PaymentTime:   paymentRes.GetTransactionTime(),
			CategoryName:  mfRes.MutualFund.CategoryName,
		})
		if etaErr != nil {
			logger.Error(ctx, "unable to get ETA", zap.Error(etaErr))
			return etaErr
		}
		etaInDays := int(math.Ceil(time.Until(eta).Hours() / 24.0))
		notificationBody := fmt.Sprintf(s.cfg.MutualFundsNotificationParams().WithdrawalRequestProcessed().Body(),
			amount, mfRes.MutualFund.NameData.ShortName, etaInDays)
		notifierErr := notifier.SendWithdrawalRequestProcessedNotification(ctx, s.cfg, s.commsClient, s.doOnce, userId, notificationBody, mfRes.MutualFund.Id)
		if notifierErr != nil {
			logger.Error(ctx, "error notifying user", zap.Error(notifierErr))
			return notifierErr
		}
	}
	return nil
}

func (s *Service) MockCreditMISReportProcessing(ctx context.Context, req *pb.MockCreditMISReportProcessingRequest) (*pb.MockCreditMISReportProcessingResponse, error) {

	orders, err := s.getOrderDetails(ctx, req.OrderType, req.Rta, req.FileName, req.OrderIds)
	if err != nil {
		return &pb.MockCreditMISReportProcessingResponse{Status: rpc.StatusInternal()}, err
	}

	if req.OrderType == pb.OrderType_BUY {
		// If update is through credit MIS file then update ref. no., else manually update order status
		if req.FileName != "" {

			orderRes, orderErr := s.ProcessCreditMISReportConfirmation(ctx, &pb.ProcessCreditMISReportConfirmationRequest{
				FileName:        req.FileName,
				Rta:             req.Rta,
				ReferenceNumber: idgen.RandSeqDigits(7),
			})
			if err2 := epifigrpc.RPCError(orderRes, orderErr); err2 != nil {
				return &pb.MockCreditMISReportProcessingResponse{Status: rpc.StatusInternal()}, err2
			}
		} else {
			err = s.updateOrdersToInFulfillment(ctx, orders)
			if err != nil {
				return &pb.MockCreditMISReportProcessingResponse{Status: rpc.StatusInternal()}, err
			}
		}
	}
	logger.Info(ctx, "Sending credit MIS file order data to SQS")
	_, err = s.uploadCreditMISToVendorPublisher.Publish(ctx, &camsPb.GenerateReverseFeedFileAfterPaymentNotificationRequest{Orders: orders})
	if err != nil {
		logger.Error(ctx, "Unable to send message with order IDs to SQS", zap.Error(err))
		return &pb.MockCreditMISReportProcessingResponse{Status: rpc.StatusInternal()}, status.Error(codes.Internal, "Unable to send message to SQS")
	}

	return &pb.MockCreditMISReportProcessingResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) getOrderDetails(ctx context.Context, orderType pb.OrderType, vendor commonvgpb.Vendor, fileName string, orderIds []string) ([]*pb.Order, error) {
	switch orderType {
	case pb.OrderType_BUY:
		if len(orderIds) > 0 {
			return s.getOrdersByOrderIds(ctx, orderIds)
		}

		// Fallback to credit MIS file-name if no order IDs are provided
		res, err := s.GetOrdersInCreditMISFile(ctx, &pb.GetOrdersInCreditMISFileRequest{Rta: vendor, FileName: fileName})
		if te := epifigrpc.RPCError(res, err); te != nil {
			logger.Error(ctx, "error getting order details", zap.Error(te))
			return nil, fmt.Errorf("error getting order details")
		}
		return res.Orders, nil
	case pb.OrderType_SELL:
		if len(orderIds) > 0 {
			return s.getOrdersByOrderIds(ctx, orderIds)
		} else {
			return nil, fmt.Errorf("order IDs are a must for SELL order types")
		}
	default:
		return nil, fmt.Errorf("invalid order type")
	}
}

func (s *Service) updateOrdersToInFulfillment(ctx context.Context, orders []*pb.Order) error {
	for _, order := range orders {
		res, err := s.UpdateOrderStatusWithCheck(ctx, &pb.UpdateOrderStatusWithCheckRequest{
			NewOrderStatus:     pb.OrderStatus_IN_FULFILLMENT,
			CurrentOrderStatus: pb.OrderStatus_NOTIFYING_PAYMENT_CREDIT,
			FailureReason:      order.FailureReason,
			OrderIdentifier:    &pb.UpdateOrderStatusWithCheckRequest_VendorOrderId{VendorOrderId: order.VendorOrderId},
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			logger.Error(ctx, "UpdateOrderStatusWithCheck failed with error", zap.String(logger.VENDOR_ORDER_ID, order.VendorOrderId), zap.Error(te))
			return te
		}
	}
	return nil
}

func (s *Service) getOrdersByOrderIds(ctx context.Context, orderIds []string) ([]*pb.Order, error) {
	var orders []*pb.Order
	// TODO(Brijesh): Extend GetOrders RPC, add oneof for other identifiers, and use that here
	for _, id := range orderIds {
		res, err := s.GetOrder(ctx, &pb.GetOrderRequest{
			Id: &pb.GetOrderRequest_ExternalOrderId{ExternalOrderId: id},
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			logger.Error(ctx, "error getting order details", zap.Error(te))
			return nil, fmt.Errorf("error getting order details")
		}
		orders = append(orders, res.Order)
	}
	return orders, nil
}

func (s *Service) validateRtaConfirmedAmount(ctx context.Context, order *pb.Order, rtaConfirmedAmount *moneyPb.Money) error {
	if rtaConfirmedAmount == nil || !money.IsPositive(rtaConfirmedAmount) {
		return fmt.Errorf("rtaConfirmedAmount cannot be negative or nil")
	}

	if order.GetOrderType() != pb.OrderType_BUY {
		return nil
	}

	perDiff, err := money.CalculateDiffPercentage(rtaConfirmedAmount, order.GetAmount())
	if err != nil {
		return fmt.Errorf("error calculating percentage difference between order amount and rta confirmed amount, err: %w", err)
	}

	absoluteDiff, err := money.Subtract(rtaConfirmedAmount, order.GetAmount())
	if err != nil {
		return err
	}
	absoluteDiffAmount, err := money.ToPaise(absoluteDiff)
	if err != nil {
		return err
	}

	thresholdPercentErr := int64(s.cfg.RTAConfirmedFieldsValidationConfig().RTAConfirmedAmountThresholdErrorPercent())
	thresholdAbsoluteErr := int64(s.cfg.RTAConfirmedFieldsValidationConfig().RTAConfirmedAmountThresholdError())
	if perDiff.Abs().GreaterThanOrEqual(decimal.NewFromInt(thresholdPercentErr)) && (math.Abs(float64(absoluteDiffAmount/100)) >= math.Abs(float64(thresholdAbsoluteErr))) {
		logger.Error(ctx, fmt.Sprintf("RTAConfirmedAmount differs from order amount by greater than allowed percentage error. thresholdErr: %v%%, thresholdAbsoulteErr: %v, actualErr: %s%%",
			thresholdPercentErr, thresholdAbsoluteErr, perDiff.String()), zap.String(logger.ORDER_ID, order.GetVendorOrderId()),
			zap.String(logger.ID, order.GetId()))
	}

	return nil
}

func (s *Service) validateRtaConfirmedUnits(ctx context.Context, order *pb.Order, unitsAllocated float64) error {
	if unitsAllocated <= 0 {
		return fmt.Errorf("rtaConfirmedUnits cannot be negative")
	}

	orderAmountInPaise, err := money.ToPaise(order.GetAmount())
	if err != nil {
		return err
	}
	if order.GetOrderType() == pb.OrderType_SELL && order.GetUnits() != 0 && float64(orderAmountInPaise/100) >= float64(s.cfg.RTAConfirmedFieldsValidationConfig().RTAConfirmedAmountThresholdAmount()) {
		orderUnits := decimal.NewFromFloat(order.GetUnits())
		confirmedUnits := decimal.NewFromFloat(unitsAllocated)
		// perDiff = (ordered units - rta confirmed units)*100/ordered units
		perDiff := orderUnits.Sub(confirmedUnits).Div(orderUnits).Mul(decimal.NewFromInt(100))
		thresholdErr := int64(s.cfg.RTAConfirmedFieldsValidationConfig().RTAConfirmedUnitsThresholdErrorPercent())
		if perDiff.Abs().GreaterThanOrEqual(decimal.NewFromInt(thresholdErr)) {
			logger.Error(ctx, fmt.Sprintf("RTAConfirmedUnits differs from ordered units by greater than allowed percentage error, thresholdErr: %v%%, actualErr: %s%%",
				thresholdErr, perDiff.String()), zap.String(logger.ORDER_ID, order.GetVendorOrderId()),
				zap.String(logger.ID, order.GetId()))
		}
	}

	return nil
}

func (s *Service) HandleFitttSubscriptionUpdate(ctx context.Context, request *pb.HandleFitttSubscriptionUpdateRequest) (*pb.HandleFitttSubscriptionUpdateResponse, error) {
	if request.OrderSubType != pb.OrderSubType_BUY_SIP {
		return &pb.HandleFitttSubscriptionUpdateResponse{Status: rpc.StatusInvalidArgument()}, nil
	}

	txnErr := s.idempotentTxnExecutor.RunIdempotentTxn(ctx, DB_TRANSACTION_COUNT, func(txnCtx context.Context) error {
		sipLedgers, err := s.sipLedgerDao.GetByFilterOptions(txnCtx, mfPb.SIPLedgerMask_SIP_LEDGER_MASK_UNSPECIFIED, false,
			dao.WithFitttSubscriptionId(request.GetFitttSubscriptionId()), dao.WithSipStatus(mfPb.SIPStatus_SIP_STATUS_ACTIVE))
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(txnCtx, "No active SIP for fitt subsrciption", zap.String(logger.SUBSCRIPTION_ID, request.GetFitttSubscriptionId()))
				return nil
			}
			logger.Error(txnCtx, "error in fetching sip for fittt subscription", zap.String(logger.SUBSCRIPTION_ID, request.GetFitttSubscriptionId()),
				zap.Error(err))
			return err
		}
		if len(sipLedgers) > 1 {
			logger.Error(txnCtx, "cannot be more than 1 active SIPs for a fittt subscription",
				zap.String(logger.SUBSCRIPTION_ID, request.GetFitttSubscriptionId()), zap.Error(err))
			return fmt.Errorf("cannot be more than 1 active SIPs for a fittt subscription")
		}
		updatedSip := sipLedgers[0]
		updatedSip.SipStatus = mfPb.SIPStatus_SIP_STATUS_INACTIVE
		_, updErr := s.sipLedgerDao.Update(txnCtx, updatedSip, []mfPb.SIPLedgerMask{mfPb.SIPLedgerMask_SIP_STATUS})
		if updErr != nil {
			logger.Error(txnCtx, "error in updating SIP ledger", zap.String(logger.SUBSCRIPTION_ID, request.GetFitttSubscriptionId()), zap.Error(updErr))
			return updErr
		}
		return nil
	})
	if txnErr != nil {
		logger.Error(ctx, "error in transaction when updating sip ledger", zap.Error(txnErr))
		return &pb.HandleFitttSubscriptionUpdateResponse{Status: rpc.StatusInternal()}, nil
	}

	return &pb.HandleFitttSubscriptionUpdateResponse{Status: rpc.StatusOk()}, nil
}
