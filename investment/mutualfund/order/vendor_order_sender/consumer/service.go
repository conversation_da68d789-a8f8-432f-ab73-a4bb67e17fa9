package consumer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"math"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/actor"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	"github.com/epifi/gamma/api/investment/mutualfund/order/vendor_order_sender/consumer"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	userPb "github.com/epifi/gamma/api/user"
	mf "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	genConf "github.com/epifi/gamma/investment/config/genconf"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/gamma/investment/mutualfund/notifier"
	invPkg "github.com/epifi/gamma/pkg/investment"
)

type BuyOrdersNotificationFunction func(ctx context.Context, orders []*orderPb.Order) error
type SuccessfullySentSellOrdersNotificationFunction func(ctx context.Context, orders []*orderPb.Order) error

type Service struct {
	mfClient                                       mf.MutualFundClient
	orderDao                                       dao.OrderDao
	orderStatusDao                                 dao.OrderStatusUpdateDao
	fileStateDao                                   dao.FileStateDao
	cfg                                            *genConf.Config
	commsClient                                    comms.CommsClient
	actorClient                                    actor.ActorClient
	catalogClient                                  catalog.CatalogManagerClient
	phClient                                       phPb.PaymentHandlerClient
	userClient                                     userPb.UsersClient
	idempotentTxnExecutor                          storagev2.IdempotentTxnExecutor
	doOnce                                         once.DoOnce
	orderUpdaterFactory                            *OrderUpdaterFactory
	buyOrdersNotificationFunction                  BuyOrdersNotificationFunction
	successfullySentSellOrdersNotificationFunction SuccessfullySentSellOrdersNotificationFunction
}

const transactionMaximumRetries = uint(2)

func NewService(mfClient mf.MutualFundClient, orderDao dao.OrderDao, orderStatusDao dao.OrderStatusUpdateDao,
	fileStateDao dao.FileStateDao, cfg *genConf.Config, commsClient comms.CommsClient, actorClient actor.ActorClient,
	catalogClient catalog.CatalogManagerClient, phClient phPb.PaymentHandlerClient,
	userClient userPb.UsersClient, idempotentTxnExecutor storagev2.IdempotentTxnExecutor, orderUpdaterFactory *OrderUpdaterFactory,
	doOnce once.DoOnce) *Service {
	service := &Service{
		mfClient:              mfClient,
		orderDao:              orderDao,
		orderStatusDao:        orderStatusDao,
		fileStateDao:          fileStateDao,
		cfg:                   cfg,
		commsClient:           commsClient,
		actorClient:           actorClient,
		catalogClient:         catalogClient,
		phClient:              phClient,
		userClient:            userClient,
		idempotentTxnExecutor: idempotentTxnExecutor,
		doOnce:                doOnce,
		orderUpdaterFactory:   orderUpdaterFactory,
	}
	service.successfullySentSellOrdersNotificationFunction = service.notifySellOrdersSentSuccessfully
	service.buyOrdersNotificationFunction = service.notifyBuyOrdersPlaced
	return service
}

//nolint:funlen
func (s *Service) ProcessOrderFeedFileStatus(ctx context.Context, request *consumer.ProcessOrderFeedFileStatusRequest) (*consumer.ProcessOrderFeedFileStatusResponse, error) {
	logger.Info(ctx, fmt.Sprintf("received ProcessOrderFeedFileStatus for fileName: %s and vendor: %s and isAsyncProcessor: %t",
		request.FileName, request.Vendor, request.IsAsyncProcessor))

	allOrdersProcessedByOMS := true
	allOrdersProcessedByVendor := true
	var successfulVendorOrderIDs, failedVendorOrderIDs []string
	var err error
	orderIdToFailureReasonMap := request.OrderIdToFailureReasonMap
	orderIdToFailureStringMap := request.OrderIdToDebugFailureReasonMap
	if request.IsAsyncProcessor {
		successfulVendorOrderIDs, failedVendorOrderIDs, allOrdersProcessedByVendor, err = s.getOrderFeedFileStatus(ctx, request)
	} else {
		successfulVendorOrderIDs, failedVendorOrderIDs, allOrdersProcessedByVendor, err = s.processOrders(ctx, request)
	}

	var successfulBuyOrdersInSentToRTAStatus, successfulSellOrdersInSentToRTAStatus, failedOrdersInSentToRTAStatus []*orderPb.Order
	txnErr := s.idempotentTxnExecutor.RunIdempotentTxn(ctx, transactionMaximumRetries, func(txnCtx context.Context) error {
		if len(successfulVendorOrderIDs) > 0 {
			successfulOrderDetails, err2 := s.orderDao.GetByVendorOrderIds(txnCtx, successfulVendorOrderIDs)
			if err2 != nil {
				return err2
			}
			if len(successfulOrderDetails) != len(successfulVendorOrderIDs) {
				logger.Info(txnCtx,
					fmt.Sprintf("some successful vendor_order_ids are not present in db: "+
						"len(successfulVendorOrderIDs)!=len(successfulOrderDetails from DB) as %v != %v",
						len(successfulVendorOrderIDs), len(successfulOrderDetails)),
					zap.String(logger.FILE_NAME, request.FileName))
			}

			/*
				To ensure consistency, we should update the orders that are only in OrderStatus_SENT_TO_RTA.
			*/
			for _, orderDetail := range successfulOrderDetails {
				if orderDetail.OrderStatus == orderPb.OrderStatus_SENT_TO_RTA {
					if orderDetail.OrderType == orderPb.OrderType_BUY {
						successfulBuyOrdersInSentToRTAStatus = append(successfulBuyOrdersInSentToRTAStatus, orderDetail)
					} else {
						successfulSellOrdersInSentToRTAStatus = append(successfulSellOrdersInSentToRTAStatus, orderDetail)
					}
				}
				/*
					log and error if order status is not OrderStatus_SENT_TO_RTA or OrderStatus_ACCEPTED_BY_RTA
				*/
				if (orderDetail.OrderStatus != orderPb.OrderStatus_ACCEPTED_BY_RTA) && (orderDetail.OrderStatus != orderPb.OrderStatus_SENT_TO_RTA) {
					// In-case the order is not yet processed by vendor_order_file_sender yet, we need to do a retry
					if orderDetail.OrderStatus == orderPb.OrderStatus_INITIATED && !request.RequestHeader.IsLastAttempt {
						allOrdersProcessedByOMS = false
					}

					logger.Error(txnCtx, fmt.Sprintf("status of orderId is not OrderStatus_ACCEPTED_BY_RTA or  OrderStatus_SENT_TO_RTA and is %s", orderDetail.OrderStatus),
						zap.String("orderId", orderDetail.Id))
				}
			}

			if len(successfulBuyOrdersInSentToRTAStatus) > 0 {
				err2 = s.updateSuccessfulOrders(txnCtx, request.Vendor, successfulBuyOrdersInSentToRTAStatus, orderPb.OrderStatus_ACCEPTED_BY_RTA, request.OrderIdToVendorReferenceNumberMap)

				if err2 != nil {
					return err2
				}
			}
			if len(successfulSellOrdersInSentToRTAStatus) > 0 {
				err2 = s.updateSuccessfulOrders(txnCtx, request.Vendor, successfulSellOrdersInSentToRTAStatus, orderPb.OrderStatus_IN_FULFILLMENT, request.OrderIdToVendorReferenceNumberMap)
				if err2 != nil {
					return err2
				}
			}
		}

		if len(failedVendorOrderIDs) > 0 {
			failedOrderDetails, err2 := s.orderDao.GetByVendorOrderIds(txnCtx, failedVendorOrderIDs)
			if err2 != nil {
				return err2
			}
			if len(failedOrderDetails) != len(failedVendorOrderIDs) {
				logger.Info(txnCtx,
					fmt.Sprintf("some failed vendor_order_ids are not present in db: "+
						"len(failedVendorOrderIDs)!=len(failedOrderDetails from DB) as %v != %v",
						len(failedVendorOrderIDs), len(failedOrderDetails)),
					zap.String(logger.FILE_NAME, request.FileName))
			}

			/*
				To ensure consistency, we should update the orders that are only in OrderStatus_SENT_TO_RTA.
			*/
			for _, orderDetail := range failedOrderDetails {
				if orderDetail.OrderStatus == orderPb.OrderStatus_SENT_TO_RTA {
					failedOrdersInSentToRTAStatus = append(failedOrdersInSentToRTAStatus, orderDetail)
				}

				if (orderDetail.OrderStatus != orderPb.OrderStatus_FAILURE) && (orderDetail.OrderStatus != orderPb.OrderStatus_SENT_TO_RTA) {
					// In-case the order is not yet processed by vendor_order_file_sender yet, we need to do a retry
					if orderDetail.OrderStatus == orderPb.OrderStatus_INITIATED && !request.RequestHeader.IsLastAttempt {
						allOrdersProcessedByOMS = false
					}

					logger.Error(txnCtx, fmt.Sprintf("status of orderId is not OrderStatus_FAILURE or OrderStatus_SENT_TO_RTA and is %s", orderDetail.OrderStatus),
						zap.String("orderId", orderDetail.Id))
				}
			}

			if len(failedOrdersInSentToRTAStatus) > 0 {
				err2 = s.updateFailedOrders(txnCtx, request.Vendor, failedOrdersInSentToRTAStatus, orderIdToFailureReasonMap, orderIdToFailureStringMap, request.OrderIdToVendorReferenceNumberMap)
				if err2 != nil {
					return err2
				}
			}
		}

		if allOrdersProcessedByVendor && allOrdersProcessedByOMS {
			/*
				Since all orders in the file are processed and have reached terminal state, update the state of the file.
			*/
			activeFile, err2 := s.fileStateDao.GetActiveFileById(txnCtx, request.FileStateId)
			if err2 != nil {
				return err2
			}

			activeFile.State = orderPb.FileState_FILE_PROCESS_BY_VENDOR_SUCCESSFUL
			_, err = s.fileStateDao.Update(txnCtx, activeFile, []orderPb.FileStateFieldMask{orderPb.FileStateFieldMask_FILE_STATE_FIELD_MASK_STATE})
			if err != nil {
				return err
			}
		}

		return nil
	})

	if txnErr != nil {
		if storagev2.IsErrorAmbiguous(txnErr) {
			logger.Error(ctx, logger.TXN_AMBIGUOUS_ERR+" error in ProcessOrderFeedFileStatus txn", zap.String("fileName", request.FileName))
		}
		logger.Error(ctx, "error in transaction", zap.String("fileName", request.FileName), zap.Error(txnErr))
		return &consumer.ProcessOrderFeedFileStatusResponse{ResponseHeader: &queue.ConsumerResponseHeader{
			Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
		}}, nil
	}

	clonedCtxWithoutDeadline := epificontext.CloneCtx(ctx)

	// Send notifications for "BUY" orders placed successfully
	goroutine.RunWithCtx(clonedCtxWithoutDeadline, func(clonedCtxWithoutDeadline context.Context) {
		notifierErr := s.buyOrdersNotificationFunction(clonedCtxWithoutDeadline, successfulBuyOrdersInSentToRTAStatus)
		if notifierErr != nil {
			logger.Error(clonedCtxWithoutDeadline, "error sending notification", zap.Error(notifierErr))
		} else {
			logger.Debug(clonedCtxWithoutDeadline, "buyOrdersNotificationFunction sent successfully")
		}
	})

	// Send notifications for "SELL" orders successfully sent successfully (i.e., accepted by RTA)
	goroutine.RunWithCtx(clonedCtxWithoutDeadline, func(clonedCtxWithoutDeadline context.Context) {
		notifierErr := s.successfullySentSellOrdersNotificationFunction(clonedCtxWithoutDeadline, successfulSellOrdersInSentToRTAStatus)
		if notifierErr != nil {
			logger.Error(clonedCtxWithoutDeadline, "error sending notification", zap.Error(notifierErr))
		} else {
			logger.Debug(clonedCtxWithoutDeadline, "successfullySentSellOrdersNotificationFunction sent successfully")
		}
	})

	// Note: Notifications are not sent currently for failed orders in SENT_TO_RTA status,
	// as they might later be manually fixed to a successful state

	/*
		It is possible that for a single status check call, only partial orders are in the processed state
		(Success/Failure). So, we will need to keep on invoking the status check until all orders reach the
		processed/Terminal state.

		Todo(Junaid): The above comment was added after noticing this in UAT. Check if the same applies to prod as well.
	*/
	if !allOrdersProcessedByVendor || !allOrdersProcessedByOMS {
		return &consumer.ProcessOrderFeedFileStatusResponse{ResponseHeader: &queue.ConsumerResponseHeader{
			Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
		}}, nil
	}

	return &consumer.ProcessOrderFeedFileStatusResponse{ResponseHeader: &queue.ConsumerResponseHeader{
		Status: queue.MessageConsumptionStatus_SUCCESS,
	}}, nil

}

func (s *Service) getOrderFeedFileStatus(ctx context.Context, request *consumer.ProcessOrderFeedFileStatusRequest) ([]string, []string, bool, error) {
	response, err := s.mfClient.GetOrderFeedFileStatus(ctx, &mf.GetOrderFeedFileStatusRequest{
		Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_CAMS},
		FileName:   request.FileName,
		ReportType: mf.OrderFeedFileStatusReportType_ALL,
	})
	if err != nil {
		logger.Error(ctx, "error in fetching order feed file status", zap.String("fileName", request.FileName), zap.Error(err))

		return nil, nil, false, err
	}

	if !response.Status.IsSuccess() {
		logger.Error(ctx, fmt.Sprintf("GetOrderFeedFileStatus was not successful with debugMessage: %s and shortMessage: %s",
			response.Status.DebugMessage, response.Status.ShortMessage))

		return nil, nil, false, fmt.Errorf("GetOrderFeedFileStatus was not successful with debugMessage: %s and shortMessage: %s",
			response.Status.DebugMessage, response.Status.ShortMessage)
	}

	allOrdersProcessedByVendor := true
	var successfulVendorOrderIDs []string
	var failedVendorOrderIDs []string
	for _, val := range response.GetOrderFeedFileStatusResult {
		if val.Status == mf.GetOrderFeedFileStatusResult_PENDING {
			logger.Info(ctx, fmt.Sprintf("orderId:%s is in pending fileName: %s and vendor: %s", val.UserTransactionNumber, request.FileName, request.Vendor))
			allOrdersProcessedByVendor = false
			continue
		}

		if val.Status == mf.GetOrderFeedFileStatusResult_SUCCESSFUL {
			successfulVendorOrderIDs = append(successfulVendorOrderIDs, val.UserTransactionNumber)
		} else {
			failedVendorOrderIDs = append(failedVendorOrderIDs, val.UserTransactionNumber)
		}
	}
	return successfulVendorOrderIDs, failedVendorOrderIDs, allOrdersProcessedByVendor, nil
}

func (s *Service) processOrders(ctx context.Context, request *consumer.ProcessOrderFeedFileStatusRequest) ([]string, []string, bool, error) {
	var err error
	successfulVendorOrderIDs, err := s.getVendorOrderIDs(ctx, request.SuccessfulOrderIds)
	if err != nil {
		return nil, nil, false, err
	}

	failedVendorOrderIDs, err := s.getFailedOrders(ctx, request)
	if err != nil {
		return nil, nil, false, err
	}

	return successfulVendorOrderIDs, failedVendorOrderIDs, true, nil
}

func (s *Service) getFailedOrders(ctx context.Context, request *consumer.ProcessOrderFeedFileStatusRequest) ([]string, error) {
	var err error
	var failedVendorOrderIDs []string
	if len(request.FailedOrderIds) > 0 {
		failedVendorOrderIDs, err = s.getVendorOrderIDs(ctx, request.FailedOrderIds)
		if err != nil {
			return nil, err
		}
	} else {
		var failedOrderIDs []string
		for key := range request.OrderIdToFailureReasonMap {
			failedOrderIDs = append(failedOrderIDs, key)
		}
		failedVendorOrderIDs, err = s.getVendorOrderIDs(ctx, failedOrderIDs)
		if err != nil {
			return nil, err
		}
	}
	return failedVendorOrderIDs, nil
}

func (s *Service) getVendorOrderIDs(ctx context.Context, successfulOrderIDs []string) ([]string, error) {
	var vendorOrderIDs []string
	orders, err := s.orderDao.GetByOrderIds(ctx, successfulOrderIDs)
	if err != nil {
		return nil, err
	}
	for _, order := range orders {
		vendorOrderIDs = append(vendorOrderIDs, order.VendorOrderId)
	}

	return vendorOrderIDs, nil
}

func (s *Service) notifyBuyOrdersPlaced(ctx context.Context, orders []*orderPb.Order) error {

	for _, order := range orders {
		actorRes, actorErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: order.ActorId})
		if err := epifigrpc.RPCError(actorRes, actorErr); err != nil {
			logger.Error(ctx, "unable to get actor by ID", zap.String(logger.ACTOR_ID, order.ActorId), zap.Error(err))
			return actorErr
		}
		userId := actorRes.GetActor().GetEntityId()
		mfRes, mfErr := s.catalogClient.GetMutualFund(ctx, &catalog.GetMutualFundRequest{Id: order.MutualFundId})
		if err := epifigrpc.RPCError(mfRes, mfErr); err != nil {
			logger.Error(ctx, "unable to get MF by ID", zap.String(logger.MF_ID, order.MutualFundId), zap.Error(err))
			return mfErr
		}
		if order.OrderSubType == orderPb.OrderSubType_BUY_ONE_TIME_INVEST {
			notificationBody := fmt.Sprintf(s.cfg.MutualFundsNotificationParams().OrderPlacedSuccessfullyOTI().Body(), mfRes.GetMutualFund().GetNameData().GetShortName())
			notifierErr := notifier.SendOrderPlacedNotification(ctx, s.cfg, s.commsClient, s.doOnce, userId, notificationBody, mfRes.GetMutualFund().GetId())
			if notifierErr != nil {
				logger.Error(ctx, "error notifying user", zap.Error(notifierErr))
				return notifierErr
			}
		}
	}
	return nil
}

func (s *Service) notifySellOrdersSentSuccessfully(ctx context.Context, orders []*orderPb.Order) error {
	for _, order := range orders {
		actorRes, actorErr := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: order.ActorId})
		if err := epifigrpc.RPCError(actorRes, actorErr); err != nil {
			logger.Error(ctx, "unable to get actor by ID", zap.String(logger.ACTOR_ID, order.ActorId), zap.Error(err))
			return actorErr
		}
		userId := actorRes.GetActor().GetEntityId()
		amount, _ := money.ToDecimal(order.Amount).Float64()
		mfRes, mfErr := s.catalogClient.GetMutualFund(ctx, &catalog.GetMutualFundRequest{Id: order.MutualFundId})
		if err := epifigrpc.RPCError(mfRes, mfErr); err != nil {
			logger.Error(ctx, "unable to get MF by ID", zap.String(logger.MF_ID, order.MutualFundId), zap.Error(err))
			return mfErr
		}
		paymentRes, paymentErr := s.phClient.GetPaymentDetails(ctx, &phPb.GetPaymentDetailsRequest{
			OrderId:     order.GetId(),
			PaymentMode: phPb.PaymentMode(order.GetPaymentMode()),
		})
		if err := epifigrpc.RPCError(paymentRes, paymentErr); err != nil {
			logger.Error(ctx, "unable to get payment for order", zap.String(logger.ORDER_ID, order.GetId()), zap.Error(err))
			if !rpc.StatusFromError(err).IsRecordNotFound() {
				return err
			}
		}

		eta, etaErr := invPkg.GetETADate(invPkg.ETAParams{
			PendingOrder:  order,
			AssetClass:    mfRes.MutualFund.AssetClass,
			PaymentStatus: paymentRes.GetPaymentStatus(),
			PaymentTime:   paymentRes.GetTransactionTime(),
			CategoryName:  mfRes.MutualFund.CategoryName,
		})
		if etaErr != nil {
			logger.Error(ctx, "unable to get ETA", zap.Error(etaErr))
			return etaErr
		}
		etaInDays := int(math.Ceil(time.Until(eta).Hours() / 24.0))
		notificationBody := fmt.Sprintf(s.cfg.MutualFundsNotificationParams().WithdrawalRequestSent().Body(),
			amount, mfRes.MutualFund.NameData.ShortName, etaInDays)
		notifierErr := notifier.SendWithdrawalRequestSentNotification(ctx, s.cfg, s.commsClient, s.doOnce, userId, notificationBody, mfRes.GetMutualFund().GetId())
		if notifierErr != nil {
			logger.Error(ctx, "error notifying user", zap.Error(notifierErr))
			return notifierErr
		}
	}
	return nil
}

func (s *Service) updateSuccessfulOrders(ctx context.Context, vendor commonvgpb.Vendor, orders []*orderPb.Order, status orderPb.OrderStatus, orderIdToVendorReferenceNumberMap map[string]string) error {

	updater, err := s.orderUpdaterFactory.GetVendorOrderSender(vendor)
	if err != nil {
		return err
	}
	return updater.UpdateSuccessfulOrders(ctx, orders, status, orderIdToVendorReferenceNumberMap)
}

func (s *Service) updateFailedOrders(ctx context.Context, vendor commonvgpb.Vendor, orders []*orderPb.Order, orderIdToFailureReason map[string]orderPb.FailureReason,
	orderIdToFailureString map[string]string, orderIdToVendorReferenceNumberMap map[string]string) error {

	updater, err := s.orderUpdaterFactory.GetVendorOrderSender(vendor)
	if err != nil {
		return err
	}
	return updater.UpdateFailedOrders(ctx, orders, orderIdToFailureReason, orderIdToFailureString, orderIdToVendorReferenceNumberMap)
}

func getFailureReason(orderId string, orderIdToFailureReasonMap map[string]orderPb.FailureReason, orderIdToFailureString map[string]string) (orderPb.FailureReason, string) {
	debugFailureReason := ""
	if orderIdToFailureString != nil {
		// If failure string present in map use that
		if failString, ok2 := orderIdToFailureString[orderId]; ok2 {
			debugFailureReason = failString
		}
	} else {
		logger.ErrorNoCtx("orderIdToFailureString should not be empty please check", zap.String(logger.ORDER_ID, orderId))
		debugFailureReason = "orderIdToDebugFailureReasonMap should not be empty. Please check"
	}
	if orderIdToFailureReasonMap == nil {
		logger.ErrorNoCtx("orderIdToFailureReasonMap should not be empty please check", zap.String(logger.ORDER_ID, orderId))
		return orderPb.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR, debugFailureReason
	}

	// If failure reason present in map use that, otherwise fall back to default failure reason
	if failureReason, ok := orderIdToFailureReasonMap[orderId]; ok {
		return failureReason, debugFailureReason
	}
	return orderPb.FailureReason_FWD_ORDER_FEED_FILE_RTA_VALIDATION_ERROR, debugFailureReason

}
