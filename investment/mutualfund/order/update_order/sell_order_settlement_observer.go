package update_order

import (
	"context"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	consumerPb "github.com/epifi/gamma/api/investment/mutualfund/order/consumer"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	investPkg "github.com/epifi/gamma/pkg/investment"
)

var (
	sleepTimeInMinute = 3
)

type SettlementOrderUpdatesDelayPublisher queue.DelayPublisher

// SellOrderSettlementObserver observes for any change in order update and is order update is to IN_SETTLEMENT state, then
// a message is pushed to a delay queue which moves it to settled state after settlement period.
type SellOrderSettlementObserver struct {
	sellOrderUpdateQueuePublisher SettlementOrderUpdatesDelayPublisher
	orderDao                      dao.OrderDao
	mfDao                         dao.MutualFundDao
}

func NewSellOrderSettlementObserver(sellOrderUpdateQueuePublisher SettlementOrderUpdatesDelayPublisher, orderDao dao.OrderDao, mfDao dao.MutualFundDao) *SellOrderSettlementObserver {
	return &SellOrderSettlementObserver{sellOrderUpdateQueuePublisher: sellOrderUpdateQueuePublisher, orderDao: orderDao, mfDao: mfDao}
}

func (s *SellOrderSettlementObserver) processStatusUpdate(_ctx context.Context, orderId string, orderStatus orderPb.OrderStatus, _ orderPb.FailureReason,
	_ string) {
	ctx := epificontext.CtxWithTraceId(context.Background(), epificontext.TraceIdFromContext(_ctx))
	if orderStatus != orderPb.OrderStatus_IN_SETTLEMENT {
		return
	}

	// Record Failure reason as well if present otherwise pass empty string
	// we are using backGroundContext as processStatusUpdate sometimes can get called from a transaction and these DB operations
	// doesn't need to be part of a transaction.
	order, err := s.orderDao.GetById(ctx, orderId)
	if err != nil {
		logger.Error(ctx, "error in orderDao GetById", zap.Error(err), zap.String(logger.ORDER_ID, orderId))
		// This update is triggered inside a transaction in certain cases, eg: reverse feed processing
		// In that case the get query fails with the error
		// 	'current transaction is committed, commands ignored until end of transaction block'
		// Sleeping for some time, and then trying again
		logger.Info(ctx, "sleeping and then trying again", zap.Error(err), zap.String(logger.ORDER_ID, orderId))
		time.Sleep(time.Minute * time.Duration(sleepTimeInMinute))
		order, err = s.orderDao.GetById(ctx, orderId)
		if err != nil {
			logger.Error(ctx, "error in orderDao GetById", zap.Error(err), zap.String(logger.ORDER_ID, orderId))
			return
		}
	}

	mf, err := s.mfDao.GetById(ctx, order.MutualFundId)
	if err != nil {
		logger.Error(ctx, "error in mfDao GetById", zap.Error(err), zap.String(logger.ORDER_ID, orderId))
		return
	}

	delayNeeded, err := s.getDelayNeededInSeconds(order, mf)
	if err != nil {
		logger.Error(ctx, "error in getDelayNeededInSeconds", zap.Error(err), zap.String(logger.ORDER_ID, orderId))
		return
	}
	_, pubErr := s.sellOrderUpdateQueuePublisher.PublishWithDelay(ctx, &consumerPb.UpdateSellOrderAfterSettlementPeriodRequest{OrderId: orderId}, time.Second*time.Duration(delayNeeded))
	if pubErr != nil {
		logger.Error(ctx, "error in publishing for sellOrderUpdateQueuePublisher", zap.Error(pubErr), zap.String(logger.ORDER_ID, orderId))
	}
}

func (s *SellOrderSettlementObserver) getDelayNeededInSeconds(order *orderPb.Order, mf *mfPb.MutualFund) (int64, error) {
	// etaTime = orderCreationTime + expectedOrderProcessing days
	// delayNeeded = etaTime - currentTime

	etaDateTime, err := investPkg.GetETADate(investPkg.ETAParams{
		PendingOrder:  order,
		AssetClass:    mf.AssetClass,
		PaymentStatus: phPb.PaymentStatus_PAYMENT_STATUS_UNSPECIFIED,
		PaymentTime:   nil,
		CategoryName:  mf.CategoryName,
	})
	if err != nil {
		return 0, err
	}

	etaTime := timestampPb.New(etaDateTime).Seconds
	delayNeeded := etaTime - timestampPb.New(time.Now()).Seconds
	if delayNeeded <= 0 {
		delayNeeded = 1
	}
	return delayNeeded, nil
}
