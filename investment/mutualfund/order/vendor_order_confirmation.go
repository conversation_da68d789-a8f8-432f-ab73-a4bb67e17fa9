// nolint
package order

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	errors2 "github.com/pkg/errors"
	"github.com/shopspring/decimal"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage"
	txn "github.com/epifi/be-common/pkg/storage/v2"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	pb "github.com/epifi/gamma/api/investment/mutualfund/order"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	"github.com/epifi/gamma/investment/mutualfund/dao"
	"github.com/epifi/gamma/investment/mutualfund/order/order_confirmation_info"
	updOrder "github.com/epifi/gamma/investment/mutualfund/order/update_order"
	"github.com/epifi/gamma/pkg/investment"
)

var (
	PaymentFailures = []string{"Payment not received", "Cheque Dishonoured ", "Collection Dishonoured", "Credit Status pending more than", "Payment not received", "Credit Not Recd", "Credit not received", "Payment Failed", "CREDIT NOT RECEIVED", "Cheque Dishonoured / Col", "funds were not got transferred to AMC/MF"}

	KYC_NOT_PROVIDED = "kyc not provided"
	KYC_NOT_VERIFIED = "kyc not Verified"

	CANCELLED_VIA_REQUEST = "cancelled as per investor request"

	TPV_FAILURE = "third party validation failed as the account details is not provided"

	BALANCE_UNAVAILABLE_1 = "balance not available."
	BALANCE_UNAVAILABLE_2 = "zero balance"

	DOCUMENTS_UN_VERIFIED_1 = "documents not received/verified"
	DOCUMENTS_UN_VERIFIED_2 = "the fatca declaration is not provided or incomplete"

	PARTIAL_UNITS_IN_LOCK_IN_PERIOD = "as per rta partial units under lock in period"
	UNITS_IN_LOCK_IN_PERIOD         = "units under lock-in period"

	TRANSACTION_LIMIT_BELOW = "transaction amount falls below value limits"

	ZERO_BALANCE_FAILURE_1 = "Scheme having zero balance"
	ZERO_BALANCE_FAILURE_2 = "As per RTA Zero balance for the investor - Balance not available."
	ZERO_BALANCE_FAILURE_3 = "Scheme having zero balance"
)

/*
Update orders and folio ledger after reverse feed:
we can have multiple combinations of units allocated, nav, folio-id available so updating individually
do we also need to update OrderStatus here? If yes, with what status?
we can have a separate RPC to update order status altogether that will batch update orders

TODO(Ayush): Validate support for sell orders and if possible abstract out the same
*/
//nolint:funlen
func (s *Service) ProcessOrderConfirmationFromVendor(ctx context.Context, req *pb.ProcessOrderConfirmationFromVendorRequest) (*pb.ProcessOrderConfirmationFromVendorResponse, error) {
	logger.Info(ctx, fmt.Sprintf("received request for clientRequestId: %s for updating the orders", req))
	orderConfirmationUpdates := req.GetOrderConfirmationDetails()
	resMap := make(map[string]*pb.OrderUpdateResult)

	for _, val := range req.GetOrderConfirmationDetails() {
		logger.Info(ctx, fmt.Sprintf("received ProcessOrderConfirmationFromVendor for vendorOrderID: %s", val.VendorOrderId))
	}

	var newlyProcessedBuyOrders, newlyProcessedSellOrders []*pb.Order
	for _, update := range orderConfirmationUpdates {
		vendorOrderId := update.GetVendorOrderId()
		rtaConfirmedAmount := money.Multiply(update.GetNav(), decimal.NewFromFloat(update.GetUnitsAllocated()))
		// TODO (Junaid/Ayush): Assign 'units' only for buy orders. For sell orders we are skipping the update for this using masks. Better to clarify intention.
		// ToDo(Junaid): Remove OrderConfirmationMetaData from updatedOrder as it is deprecated.
		updatedOrder := &pb.Order{
			VendorOrderId:             vendorOrderId,
			Units:                     update.GetUnitsAllocated(),
			FolioId:                   update.GetFolioId(),
			Nav:                       update.GetNav(),
			OrderConfirmationMetaData: update.OrderConfirmationMetaData,
			RtaConfirmedAmount:        rtaConfirmedAmount,
			RtaConfirmedUnits:         update.GetUnitsAllocated(),
		}

		/*
			We are performing the following operations in the transaction for each order
			- Fetch Order information
			- If order is not in terminal state, update the folio information from the update otherwise skip folio update
			- Update the order information from the update and update its status to a terminal state
		*/
		orderUpdate := update
		var skippedTerminalOrderIds []string
		// Used for determining if we are processing an already confirmed order or not
		var isOrderAlreadyConfirmed bool
		txnErr := s.idempotentTxnExecutor.RunIdempotentTxn(ctx, DB_TRANSACTION_COUNT, func(txnCtx context.Context) error {
			// Fetch orders details. If order not present log and return error.
			order, getErr := s.orderDao.GetByVendorOrderId(txnCtx, vendorOrderId)
			if getErr != nil {
				if errors.Is(epifierrors.ErrRecordNotFound, getErr) {
					logger.Error(txnCtx, "order confirmed by RTA is unknown", zap.String(logger.VENDOR_ORDER_ID, vendorOrderId),
						zap.Error(getErr))
					return getErr
				}
				logger.Error(txnCtx, "failed to fetch order details", zap.String(logger.VENDOR_ORDER_ID, vendorOrderId))
				return getErr
			}
			orderId := order.Id
			isOrderAlreadyConfirmed = order.GetOrderStatus() == getConfirmedStateOfOrder(order)
			updatedOrder.Id = orderId
			updatedOrder.OrderSubType = order.GetOrderSubType()
			updatedOrder.ActorId = order.GetActorId()
			updatedOrder.MutualFundId = order.GetMutualFundId()
			/*
				If the order is in terminal state we are not doing any further processing
			*/
			if order.GetOrderStatus() == pb.OrderStatus_SETTLED || order.GetOrderStatus() == pb.OrderStatus_FAILURE {
				logger.Info(txnCtx, fmt.Sprintf("Skipping order as order is in terminal state for vendor order update: %s", vendorOrderId),
					zap.String(logger.ORDER_ID, orderId), zap.String(logger.ORDER_STATUS, order.GetOrderStatus().String()))
				skippedTerminalOrderIds = append(skippedTerminalOrderIds, orderId)
				return nil
			}

			if len(order.GetFolioId()) > 0 && strings.Compare(order.GetFolioId(), updatedOrder.GetFolioId()) != 0 {
				logger.Info(txnCtx, fmt.Sprintf("mismatch between placed folio : %s and recieved folio: %s , replacing folio", order.GetFolioId(), updatedOrder.GetFolioId()),
					zap.String(logger.ORDER_ID, orderId), zap.String(logger.ORDER_STATUS, order.GetOrderStatus().String()), zap.String(logger.VENDOR_ORDER_ID, order.GetVendorOrderId()))
				order.FolioId = updatedOrder.GetFolioId()
			}

			/*
				If the order is not in 'IN_FULFILLMENT' 'NOTIFYING_PAYMENT_CREDIT', 'CONFIRMED_BY_RTA' 'IN_SETTLEMENT' or state.
				Then we should not have received any confirmation from the vendor, erroring in that case.
			*/
			if !(order.GetOrderStatus() == pb.OrderStatus_IN_FULFILLMENT || order.GetOrderStatus() == pb.OrderStatus_NOTIFYING_PAYMENT_CREDIT ||
				order.GetOrderStatus() == pb.OrderStatus_CONFIRMED_BY_RTA || order.GetOrderStatus() == pb.OrderStatus_IN_SETTLEMENT) {
				logger.Error(ctx, fmt.Sprintf("invalid order state %s. Cannot process confirmation by vendor", order.GetOrderStatus().String()),
					zap.String(logger.ORDER_ID, orderId))
				return fmt.Errorf("invalid order state %s. Cannot process confirmation by vendor", order.GetOrderStatus().String())
			}

			if amtValidationErr := s.validateRtaConfirmedAmount(ctx, order, rtaConfirmedAmount); amtValidationErr != nil {
				return amtValidationErr
			}

			if unitValidationErr := s.validateRtaConfirmedUnits(ctx, order, orderUpdate.GetUnitsAllocated()); unitValidationErr != nil {
				return unitValidationErr
			}

			folioInfos, err := s.folioDao.GetByFilterOptions(txnCtx, mfPb.FolioLedgerMask_CREATED_AT, true, dao.WithFolioId(orderUpdate.GetFolioId()),
				dao.WithActorId(order.GetActorId()), dao.WithMutualFundId(order.GetMutualFundId()))
			if err != nil {
				/*
					Create new folio if no folio exists for this order
				*/
				if errors.Is(epifierrors.ErrRecordNotFound, err) {
					logger.Info(txnCtx, "No existing folio found. Creating new", zap.String(logger.FOLIO_ID, orderUpdate.GetFolioId()),
						zap.String(logger.VENDOR_ORDER_ID, orderUpdate.GetVendorOrderId()), zap.String(logger.ORDER_ID, orderId))

					folioInfo := &mfPb.FolioLedger{
						FolioId:                 orderUpdate.GetFolioId(),
						ActorId:                 order.GetActorId(),
						MutualFundId:            order.GetMutualFundId(),
						BalanceUnits:            orderUpdate.GetUnitsAllocated(),
						InvestedValue:           orderUpdate.GetAmount(),
						AvgPurchaseNav:          orderUpdate.GetNav(),
						LastTransactionPostedAt: datetime.DateToTimestamp(orderUpdate.GetPostDate(), datetime.IST),
						FolioUpdateProvenance:   mfPb.FolioUpdationProvenance_FUP_ORDER_CONFIRMATION_REVERSE_FEED,
					}

					_, folErr := s.folioDao.Create(txnCtx, folioInfo)
					if folErr != nil {
						logger.Error(txnCtx, "error creating new folio",
							zap.String(logger.FOLIO_ID, orderUpdate.GetFolioId()),
							zap.String(logger.VENDOR_ORDER_ID, orderUpdate.GetVendorOrderId()),
							zap.String(logger.ORDER_ID, orderId),
							zap.Error(folErr))
						return folErr
					}
				} else {
					logger.Error(txnCtx, "error fetching folio details",
						zap.String(logger.FOLIO_ID, orderUpdate.GetFolioId()),
						zap.String(logger.VENDOR_ORDER_ID, orderUpdate.GetVendorOrderId()),
						zap.String(logger.ORDER_ID, orderId),
						zap.Error(err))
					return err
				}
			} else {
				if len(folioInfos) > 1 {
					logger.Error(ctx, "Multiple folios present with same actor, folio id and mutual fund id",
						zap.String(logger.ACTOR_ID, order.GetActorId()), zap.String(logger.FOLIO_ID, orderUpdate.GetFolioId()),
						zap.String(logger.MUTUAL_FUND, order.GetMutualFundId()), zap.String(logger.ORDER_ID, order.GetId()))
					return fmt.Errorf("Multiple folios present with same actor %s and folio id %s. Cannot process confirmation by vendor",
						order.GetActorId(), orderUpdate.GetFolioId())
				}
			}
			orderUpdateMaskArray := []pb.OrderFieldMask{
				pb.OrderFieldMask_NAV,
				pb.OrderFieldMask_FOLIO_ID,
				pb.OrderFieldMask_Order_CONFIRMATION_META_DATA,
				pb.OrderFieldMask_ORDER_STATUS,
				pb.OrderFieldMask_RTA_CONFIRMED_AMOUNT,
				pb.OrderFieldMask_RTA_CONFIRMED_UNITS,
			}

			// We need to update units only for buy orders. For sell orders, they are calculated during order creation
			if order.OrderType == pb.OrderType_BUY {
				orderUpdateMaskArray = append(orderUpdateMaskArray, pb.OrderFieldMask_UNITS_ALLOCATED)
			}

			logger.Info(txnCtx, fmt.Sprintf("creating orderConfirmationInfo for vendorOrderId: %s, orderID: %s", vendorOrderId, orderId))

			err = order_confirmation_info.CreateOrUpdateOrderConfirmationInfo(txnCtx, &pb.OrderConfirmationInfo{
				OrderId:                   orderId,
				RtaTransactionNumber:      orderUpdate.GetRtaTransactionNumber(),
				Rta:                       order.GetRta(),
				OrderConfirmationMetadata: orderUpdate.GetOrderConfirmationMetaData(),
			}, s.orderConfirmationInfoDao)
			if err != nil {
				logger.Error(txnCtx, "failed to create orderConfirmationInfo",
					zap.String(logger.CLIENT_REQUEST_ID, req.ClientRequestId), zap.Error(err),
					zap.String(logger.ORDER_ID, orderId))
				return err
			}
			logger.Info(txnCtx, fmt.Sprintf("successfully created orderConfirmationInfo for vendorOrderId: %s, orderID: %s", vendorOrderId, orderId))

			/*
				Updating individual orders with the details from the reverse feed.
				Also updating the order to terminal state.
			*/
			updatedOrder.OrderStatus = getTerminalStateOfOrder(order)
			updErr := updOrder.UpdateOrder(txnCtx, s.orderDao, s.orderStatusDao, s.folioDao, updatedOrder, orderUpdateMaskArray, true, s.idempotentTxnExecutor, s.orderStatusNotifier)
			if updErr != nil {
				logger.Error(txnCtx, fmt.Sprintf("Failed to update orderId: %s", orderId),
					zap.String(logger.CLIENT_REQUEST_ID, req.ClientRequestId), zap.Error(updErr),
					zap.String(logger.ORDER_ID, orderId))
				return updErr
			}

			if !isOrderAlreadyConfirmed {
				switch order.OrderType {
				case pb.OrderType_BUY:
					newlyProcessedBuyOrders = append(newlyProcessedBuyOrders, order)
				case pb.OrderType_SELL:
					newlyProcessedSellOrders = append(newlyProcessedSellOrders, order)
				default:
				}
			}
			return nil
		})

		if len(skippedTerminalOrderIds) > 0 {
			logger.Info(ctx, "orders skipped due to already in terminal state", zap.Strings(logger.ORDER_IDS, skippedTerminalOrderIds))
		}
		// Update result map for each order
		if txnErr != nil {
			// ToDO(Junaid): remove this log
			logger.Error(ctx, fmt.Sprintf("error in updating order status in process order confiration for vendorOrderID: %s ", vendorOrderId), zap.Error(txnErr))
			switch {
			case storagev2.IsErrorAmbiguous(txnErr):
				logger.Error(ctx, logger.TXN_AMBIGUOUS_ERR+" ambiguous error in ProcessOrderConfirmationFromVendor",
					zap.String(logger.VENDOR_ORDER_ID, vendorOrderId), zap.String(logger.CLIENT_REQUEST_ID, req.ClientRequestId), zap.Error(txnErr))
				resMap[vendorOrderId] = &pb.OrderUpdateResult{
					VendorOrderId: vendorOrderId,
					UpdateStatus:  pb.OrderUpdateResult_FAILURE,
				}
			case errors.Is(txnErr, epifierrors.ErrRecordNotFound):
				logger.Error(ctx, "got record not found error in ProcessOrderConfirmationFromVendor",
					zap.String(logger.ORDER_ID, vendorOrderId), zap.Error(txnErr))
				resMap[vendorOrderId] = &pb.OrderUpdateResult{
					VendorOrderId: vendorOrderId,
					UpdateStatus:  pb.OrderUpdateResult_INVALID_ORDER_ID,
				}
			default:
				logger.Error(ctx, "error in ProcessOrderConfirmationFromVendor", zap.String(logger.CLIENT_REQUEST_ID, req.ClientRequestId),
					zap.String(logger.ORDER_ID, vendorOrderId), zap.Error(txnErr))
				resMap[vendorOrderId] = &pb.OrderUpdateResult{
					VendorOrderId: vendorOrderId,
					UpdateStatus:  pb.OrderUpdateResult_FAILURE,
				}
			}
		} else {
			// ToDO(Junaid): Remove this log
			logger.Info(ctx, fmt.Sprintf("vendor order id updated or skipped successfully: %s", vendorOrderId))
			resMap[vendorOrderId] = &pb.OrderUpdateResult{
				VendorOrderId: vendorOrderId,
				UpdateStatus:  pb.OrderUpdateResult_SUCCESS,
			}
		}
	}

	clonedCtxWithoutDeadline := epificontext.CloneCtx(ctx)

	// Send notification for "BUY" orders which were newly processed successfully by fund house and units were allocated
	goroutine.Run(ctx, 5*time.Minute, func(ctx context.Context) {
		if notifierErr := s.notifyBuyOrdersProcessedSuccessfully(clonedCtxWithoutDeadline, newlyProcessedBuyOrders); notifierErr != nil {
			logger.Error(clonedCtxWithoutDeadline, "error sending notification", zap.Error(notifierErr))
		}
	})

	// Send notifications for "SELL" orders which were newly processed successfully by fund house
	goroutine.Run(ctx, 5*time.Minute, func(ctx context.Context) {
		if notifierErr := s.notifySellOrdersProcessedSuccessfully(clonedCtxWithoutDeadline, newlyProcessedSellOrders); notifierErr != nil {
			logger.Error(clonedCtxWithoutDeadline, "error sending notification", zap.Error(notifierErr))
		}
	})

	return &pb.ProcessOrderConfirmationFromVendorResponse{
		Status:        &rpc.Status{Code: uint32(pb.ProcessOrderConfirmationFromVendorResponse_OK)},
		UpdateResults: resMap,
	}, nil
}

func getTerminalStateOfOrder(order *pb.Order) pb.OrderStatus {
	if order.GetOrderType() == pb.OrderType_BUY {
		return pb.OrderStatus_CONFIRMED_BY_RTA
	} else {
		return pb.OrderStatus_IN_SETTLEMENT
	}
}

// Confirmed state is different from terminal state only for Sell orders, where we mark the order as IN_SETTLEMENT when vendor confirms and SETTLED when we get the credit
func getConfirmedStateOfOrder(order *pb.Order) pb.OrderStatus {
	if order.GetOrderType() == pb.OrderType_BUY {
		return pb.OrderStatus_CONFIRMED_BY_RTA
	} else {
		return pb.OrderStatus_SETTLED
	}
}

/*
	ToDO(Junaid):
		1) ProcessPaymentCreditFromVendor currently works only for Redemption use cases. Need to add support to dividend
		   schemes as well before we open up dividend schemes.
*/
// nolint: funlen
func (s *Service) ProcessPaymentCreditFromVendor(ctx context.Context, req *pb.ProcessPaymentCreditFromVendorRequest) (*pb.ProcessPaymentCreditFromVendorResponse, error) {

	var updateResultMap = make(map[string]*pb.PaymentConfirmationUpdateResult)
	var orderId, transactionNumber string
	for id := range req.GetPaymentInfoMap() {
		identifier := id
		paymentInfo := req.PaymentInfoMap[identifier]
		orderId = ""

		txnErr := s.idempotentTxnExecutor.RunIdempotentTxn(ctx, DB_TRANSACTION_COUNT, func(txnCtx context.Context) error {
			orderConfirmationInfo, err := s.fetchOrderConfirmationInfo(txnCtx, identifier, req.OrderIdentifier, req.Rta)
			if err != nil {
				return err
			}
			orderId = orderConfirmationInfo.OrderId
			transactionNumber = orderConfirmationInfo.RtaTransactionNumber
			existingPaymentConfirmationMetadata := orderConfirmationInfo.PaymentConfirmationMetadata
			orderConfirmationInfo.PaymentConfirmationMetadata = paymentInfo
			err = s.orderConfirmationInfoDao.UpdateByOrderId(txnCtx, orderConfirmationInfo, []pb.OrderConfirmationInfoFieldMASK{pb.OrderConfirmationInfoFieldMASK_PAYMENT_CONFIRMATION_METADATA})
			if err != nil {
				return err
			}
			order, err := s.orderDao.GetById(txnCtx, orderConfirmationInfo.OrderId)
			if err != nil {
				return err
			}

			fund, err := s.mutualFundDao.GetById(ctx, order.MutualFundId)
			if err != nil {
				return err
			}
			etaDate, err := investment.GetETADate(investment.ETAParams{
				PendingOrder:  order,
				AssetClass:    fund.GetAssetClass(),
				PaymentStatus: phPb.PaymentStatus_PAYMENT_STATUS_UNSPECIFIED,
				PaymentTime:   nil,
				CategoryName:  fund.GetCategoryName(),
			})
			if err != nil {
				return err
			}

			// if both utr number is empty and the time of update is before the eta of the order, then we will not allow
			// update of status to settled. These orders will needed to be processed again after the eta for them to
			// move to settled state. If we don't have this check, we will be showing money transfer for the withdrawal
			// as success even before actual credit happens.
			if len(paymentInfo.InstrumentNumber) == 0 && !time.Now().After(etaDate) {
				logger.Error(txnCtx, "utr number is empty", zap.String(logger.ORDER_ID, orderId), zap.String(logger.ID, identifier),
					zap.String("IdentifierType", req.OrderIdentifier.String()))
				updateResultMap[identifier] = s.getPaymentUpdateResult(fmt.Errorf("utr number is empty"), orderId, transactionNumber, req.OrderIdentifier)
				return fmt.Errorf("utr number is empty and eta has not passed")
			}

			if order.OrderStatus == pb.OrderStatus_SETTLED && isPaymentDataUpdateAllowedForSettledOrder(existingPaymentConfirmationMetadata,
				orderConfirmationInfo.PaymentConfirmationMetadata) {
				return nil
			}

			if order.OrderStatus != pb.OrderStatus_IN_SETTLEMENT {
				return fmt.Errorf("order status is %s and not IN_SETTLEMENT", order.OrderStatus)
			}
			order.OrderStatus = pb.OrderStatus_SETTLED

			err = updOrder.UpdateOrder(txnCtx, s.orderDao, s.orderStatusDao, s.folioDao, order, []pb.OrderFieldMask{pb.OrderFieldMask_ORDER_STATUS}, true, s.idempotentTxnExecutor, s.orderStatusNotifier)
			if err != nil {
				return err
			}
			return nil
		})
		if txnErr != nil {
			if txn.IsErrorAmbiguous(txnErr) {
				logger.Error(ctx, logger.TXN_AMBIGUOUS_ERR+" error in ProcessPaymentCreditFromVendor transaction",
					zap.String(logger.ORDER_ID, orderId), zap.String(logger.ID, id),
					zap.String("IdentifierType", req.OrderIdentifier.String()), zap.Error(txnErr))
			} else {
				logger.Error(ctx, " error in ProcessPaymentCreditFromVendor transaction",
					zap.String(logger.ORDER_ID, orderId), zap.String(logger.ID, id),
					zap.String("IdentifierType", req.OrderIdentifier.String()), zap.Error(txnErr))
			}
			updateResultMap[id] = s.getPaymentUpdateResult(txnErr, orderId, transactionNumber, req.OrderIdentifier)
		} else {
			updateResultMap[id] = s.getPaymentUpdateResult(nil, orderId, transactionNumber, req.OrderIdentifier)
		}
	}

	return &pb.ProcessPaymentCreditFromVendorResponse{
		Status:         rpc.StatusOk(),
		UpdatedResults: updateResultMap,
	}, nil
}

func (s *Service) fetchOrderConfirmationInfo(ctx context.Context, id string, identifier pb.OrderIdentifier, rta commonvgpb.Vendor) (*pb.OrderConfirmationInfo, error) {
	switch identifier {
	case pb.OrderIdentifier_Order_IDENTIFIER_TRANSACTION_NUMBER:
		return s.orderConfirmationInfoDao.GetByTransactionNumberAndRta(ctx, id, rta)
	case pb.OrderIdentifier_ORDER_IDENTIFIER_ORDER_ID:
		return s.orderConfirmationInfoDao.GetByOrderId(ctx, id)
	default:
		return nil, fmt.Errorf("orderIdentifier :%s is not supported", identifier)
	}
}

func (s *Service) getPaymentUpdateResult(err error, orderId string, transactionNumber string, orderIdentifier pb.OrderIdentifier) *pb.PaymentConfirmationUpdateResult {
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			var updateStatus pb.PaymentConfirmationUpdateResult_Status
			if orderIdentifier == pb.OrderIdentifier_Order_IDENTIFIER_TRANSACTION_NUMBER {
				updateStatus = pb.PaymentConfirmationUpdateResult_INVALID_TRANSACTION_NUMBER
			} else {
				updateStatus = pb.PaymentConfirmationUpdateResult_INVALID_ORDER_ID
			}
			return &pb.PaymentConfirmationUpdateResult{
				UpdatedStatus:     updateStatus,
				OrderId:           orderId,
				TransactionNumber: transactionNumber,
			}
		} else {
			return &pb.PaymentConfirmationUpdateResult{
				UpdatedStatus:     pb.PaymentConfirmationUpdateResult_FAILURE,
				OrderId:           orderId,
				TransactionNumber: transactionNumber,
			}
		}
	}

	return &pb.PaymentConfirmationUpdateResult{
		UpdatedStatus:     pb.PaymentConfirmationUpdateResult_SUCCESS,
		OrderId:           orderId,
		TransactionNumber: transactionNumber,
	}
}

func (s *Service) ProcessOrderRejectionFromVendor(ctx context.Context, req *pb.ProcessOrderRejectionFromVendorRequest) (*pb.ProcessOrderRejectionFromVendorResponse, error) {
	var updateResultMap = make(map[string]*pb.OrderRejectionUpdateResult)

	var rejectedOrders []*pb.Order
	for id, rejectionInfo := range req.RejectedOrdersInfo {
		// ToDO(Junaid): Store the RejectedAt and RejectionReason in mf_order_rejection_infos table instead of logging.
		logger.Info(ctx, fmt.Sprintf("order rejected by vendor at %s with reason %s", rejectionInfo.RejectedAt, rejectionInfo.RejectionReason),
			zap.String(logger.VENDOR_ORDER_ID, id))
		vendorOrderId := id
		orderId := ""
		rejectionReason := rejectionInfo.RejectionReason
		rejectedAt := rejectionInfo.RejectedAt
		txnErr := s.idempotentTxnExecutor.RunIdempotentTxn(ctx, DB_TRANSACTION_COUNT, func(txnCtx context.Context) error {
			orderDetails, err := s.orderDao.GetByVendorOrderId(txnCtx, vendorOrderId)
			rejectedOrders = append(rejectedOrders, orderDetails)
			if err != nil {
				return err
			}
			isUpdateNeeded := s.isUpdateNeededForOrderRejection(orderDetails)
			if !isUpdateNeeded {
				return nil
			}
			orderId = orderDetails.Id
			orderDetails.OrderStatus = pb.OrderStatus_FAILURE
			orderDetails.FailureReason = s.getOrderFailureReasonFromRejectionReason(rejectionReason)
			orderDetails.FailureDebugReason = rejectionReason
			err = updOrder.UpdateOrder(txnCtx, s.orderDao, s.orderStatusDao, s.folioDao, orderDetails, []pb.OrderFieldMask{pb.OrderFieldMask_ORDER_STATUS, pb.OrderFieldMask_FAILURE_REASON, pb.OrderFieldMask_FAILURE_DEBUG_REASON}, true, s.idempotentTxnExecutor, s.orderStatusNotifier)
			if err != nil {
				return err
			}

			_, err = s.orderRejectionInfoDao.Create(txnCtx, &pb.OrderRejectionInfo{
				OrderId:         orderDetails.Id,
				RejectionReason: rejectionReason,
				RejectedAt:      rejectedAt,
			})

			if errors.Is(err, epifierrors.ErrDuplicateEntry) {
				return nil
			}

			return err
		})
		if txnErr != nil {
			updateResultMap[vendorOrderId] = s.getOrderRejectionUpdateErrResult(txnErr, orderId)
		} else {
			updateResultMap[vendorOrderId] = &pb.OrderRejectionUpdateResult{UpdatedStatus: pb.OrderRejectionUpdateResult_SUCCESS}
		}
	}

	return &pb.ProcessOrderRejectionFromVendorResponse{
		Status:         rpc.StatusOk(),
		UpdatedResults: updateResultMap,
	}, nil
}

func (s *Service) isUpdateNeededForOrderRejection(orderDetails *pb.Order) bool {
	if orderDetails.OrderStatus == pb.OrderStatus_FAILURE {
		// If the failure reason is not recorded, then update is needed so that we can record the failure reason.
		if orderDetails.FailureReason == pb.FailureReason_FAILURE_REASON_UNSPECIFIED || orderDetails.FailureReason == pb.FailureReason_RTA_REJECTION_UNKNOWN_FAILURE {
			return true
		}
		return false
	}
	// We don't want to update status of already confirmed orders even if vendor has updated it at their end. This will be handled using the reconciliation script.
	if orderDetails.OrderStatus == pb.OrderStatus_CONFIRMED_BY_RTA || orderDetails.OrderStatus == pb.OrderStatus_IN_SETTLEMENT || orderDetails.OrderStatus == pb.OrderStatus_SETTLED {
		return false
	}
	return true
}

func (s *Service) getOrderFailureReasonFromRejectionReason(rejectionReason string) pb.FailureReason {

	rejectionReason = strings.ToLower(rejectionReason)
	for _, val := range PaymentFailures {
		if strings.Contains(rejectionReason, strings.ToLower(val)) {
			return pb.FailureReason_RTA_REJECTION_PAYMENT_FAILURE
		}
	}

	if strings.Contains(rejectionReason, KYC_NOT_PROVIDED) {
		return pb.FailureReason_RTA_REJECTION_KYC_NOT_PROVIDED
	}

	if strings.Contains(rejectionReason, KYC_NOT_VERIFIED) {
		return pb.FailureReason_RTA_REJECTION_KYC_NOT_VERIFIED
	}

	if strings.Contains(rejectionReason, CANCELLED_VIA_REQUEST) {
		return pb.FailureReason_RTA_REJECTION_KYC_NOT_VERIFIED
	}

	if strings.Contains(rejectionReason, TPV_FAILURE) {
		return pb.FailureReason_RTA_REJECTION_THIRD_PART_VALIDATION_FAILURE
	}

	if strings.Contains(rejectionReason, BALANCE_UNAVAILABLE_1) || strings.Contains(rejectionReason, BALANCE_UNAVAILABLE_2) {
		return pb.FailureReason_RTA_REJECTION_THIRD_PART_VALIDATION_FAILURE
	}

	if strings.Contains(rejectionReason, DOCUMENTS_UN_VERIFIED_1) || strings.Contains(rejectionReason, DOCUMENTS_UN_VERIFIED_2) {
		return pb.FailureReason_RTA_REJECTION_DOCUMENTS_NOT_RECEIVED
	}

	if strings.Contains(rejectionReason, PARTIAL_UNITS_IN_LOCK_IN_PERIOD) {
		return pb.FailureReason_RTA_REJECTION_PARTIAL_UNITS_UNDER_LOCK_IN_PERIOD
	}

	if strings.Contains(rejectionReason, UNITS_IN_LOCK_IN_PERIOD) {
		return pb.FailureReason_RTA_REJECTION_UNITS_UNDER_LOCK_IN_PERIOD
	}

	if strings.Contains(rejectionReason, TRANSACTION_LIMIT_BELOW) {
		return pb.FailureReason_RTA_REJECTION_TRANSACTION_AMOUNT_BELOW_LIMITS
	}

	if strings.Contains(rejectionReason, ZERO_BALANCE_FAILURE_1) || strings.Contains(rejectionReason, ZERO_BALANCE_FAILURE_2) ||
		strings.Contains(rejectionReason, ZERO_BALANCE_FAILURE_3) {
		return pb.FailureReason_RTA_REJECTION_THIRD_PART_VALIDATION_FAILURE
	}
	return pb.FailureReason_RTA_REJECTION_UNKNOWN_FAILURE
}

func (s *Service) getOrderRejectionUpdateErrResult(err error, orderId string) *pb.OrderRejectionUpdateResult {
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return &pb.OrderRejectionUpdateResult{
			UpdatedStatus: pb.OrderRejectionUpdateResult_INVALID_VENDOR_ORDER_ID,
		}
	} else {
		return &pb.OrderRejectionUpdateResult{
			UpdatedStatus: pb.OrderRejectionUpdateResult_FAILURE,
			OrderId:       orderId,
		}
	}
}

func isPaymentDataUpdateAllowedForSettledOrder(existingPaymentConfirmationMetadata *pb.PaymentConfirmationMetadata, newPaymentConfirmationMetadata *pb.PaymentConfirmationMetadata) bool {

	/*
		In many cases, vendor doesn't put utr number (Same as instrument number) in  the redemption payout file and leaves
		the field as empty because of some delay at their end. We need to manually re-trigger a new redemption payout file and
		then re-upload that to update utr number in our system for orders in SETTLED state.
	*/
	if existingPaymentConfirmationMetadata == nil {
		return true
	}
	if len(existingPaymentConfirmationMetadata.InstrumentNumber) == 0 && len(newPaymentConfirmationMetadata.InstrumentNumber) > 0 {
		return true
	}
	return false
}

func (s *Service) ProcessExternalOrderConfirmationFromVendor(ctx context.Context, req *pb.ProcessExternalOrderConfirmationFromVendorRequest) (*pb.ProcessExternalOrderConfirmationFromVendorResponse, error) {

	logger.Debug(ctx, fmt.Sprintf("received request for clientRequestId: %s for updating the orders", req))

	updateResults := make(map[string]*pb.OrderUpdateResult)
	schemeCodeToFundIdMap, err := s.mutualFundDao.GetBySchemeCodesAndAMC(ctx, s.getUniqueSchemeCodes(req.OrderConfirmationDetails), req.Amc)
	if err != nil {
		logger.Error(ctx, "error in GetBySchemeCodesAndAMC for external order", zap.String(logger.ORDER_TYPE, req.OrderType.String()),
			zap.String(logger.MF_AMC, req.Amc.String()), zap.Error(err))
		return &pb.ProcessExternalOrderConfirmationFromVendorResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	var orderConfirmationDetails []*pb.OrderConfirmationDetail
	for _, val := range req.OrderConfirmationDetails {
		fund, folio, err2 := s.fetchFundAndFolioDetails(ctx, schemeCodeToFundIdMap, val)
		if err2 != nil {
			logger.Error(ctx, "error in fetchFundAndFolioDetails for external order", zap.String(logger.ORDER_TYPE, req.OrderType.String()),
				zap.String(logger.VENDOR_ORDER_ID, val.VendorOrderId), zap.String(logger.SCHEME_CODE, val.SchemeCode),
				zap.String(logger.MF_AMC, req.Amc.String()), zap.Error(err2))
			continue
		}

		// We are creating a new order at our db as orders created on external platforms will not be present in fi systems
		order, err2 := s.createExternalOrder(ctx, val, fund, folio, req.OrderType, req.Rta, req.Amc, pb.OrderStatus_IN_FULFILLMENT, req.OrderSubType)
		if err2 != nil {
			logger.Error(ctx, "error in createExternalOrder",
				zap.String(logger.ORDER_TYPE, req.OrderType.String()), zap.String(logger.VENDOR_ORDER_ID, val.VendorOrderId),
				zap.String(logger.SCHEME_CODE, val.SchemeCode), zap.String(logger.MF_AMC, req.Amc.String()), zap.Error(err2))
			continue
		}
		val.VendorOrderId = order.VendorOrderId
		orderConfirmationDetails = append(orderConfirmationDetails, val)
	}

	res, err := s.ProcessOrderConfirmationFromVendor(ctx, &pb.ProcessOrderConfirmationFromVendorRequest{
		ClientRequestId:          req.ClientRequestId,
		OrderConfirmationDetails: orderConfirmationDetails,
	})
	if grpcErr := epifigrpc.RPCError(res, err); grpcErr != nil {
		logger.Error(ctx, "error in ProcessOrderConfirmationFromVendor",
			zap.String(logger.ORDER_TYPE, req.OrderType.String()), zap.String(logger.MF_AMC, req.Amc.String()), zap.Error(grpcErr))
		return &pb.ProcessExternalOrderConfirmationFromVendorResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	for vendorOrderId, result := range res.GetUpdateResults() {
		updateResults[vendorOrderId] = result
	}

	return &pb.ProcessExternalOrderConfirmationFromVendorResponse{
		Status:        rpc.StatusOk(),
		UpdateResults: updateResults,
	}, nil
}

func (s *Service) ProcessInternalSwitchOrders(ctx context.Context, req *pb.ProcessInternalSwitchOrdersRequest) (*pb.ProcessInternalSwitchOrdersResponse, error) {
	logger.Debug(ctx, fmt.Sprintf("received request: %s for processing internal switch orders", req))
	updateResults := make(map[string]*pb.OrderUpdateResult)
	mutualFund, err := s.mutualFundDao.GetBySchemeCodeAndAmc(ctx, req.GetSwitchInOrder().GetOrderConfirmationDetail().GetSchemeCode(), req.GetSwitchInOrder().GetOrderConfirmationDetail().GetAmc())
	if err != nil {
		logger.Error(ctx, "error in GetBySchemeCode for internal switch order", zap.Error(err))
		return &pb.ProcessInternalSwitchOrdersResponse{
			Status: rpc.StatusInternal(),
		}, errors2.Wrap(err, "error in GetBySchemeCodesAndAMC for internal switch order")
	}

	confirmationDetail := req.GetSwitchInOrder().GetOrderConfirmationDetail()
	fund, folio, err2 := s.fetchFundAndFolioDetails(ctx, map[string]*mfPb.MutualFund{
		confirmationDetail.GetSchemeCode(): mutualFund,
	}, confirmationDetail)
	if err2 != nil {
		logger.Error(ctx, "error in fetchFundAndFolioDetails for internal switch order", zap.String(logger.ORDER_TYPE, req.GetSwitchInOrder().GetOrderType().String()),
			zap.String(logger.VENDOR_ORDER_ID, confirmationDetail.VendorOrderId), zap.String(logger.SCHEME_CODE, confirmationDetail.SchemeCode), zap.Error(err2))
		return &pb.ProcessInternalSwitchOrdersResponse{
			Status: rpc.StatusInternal(),
		}, errors2.Wrap(err2, "error in fetchFundAndFolioDetails for internal switch order")
	}

	err = s.createSwitchOrderAndUpdateVendorIdInConfirmationDetail(ctx, req.GetSwitchInOrder().GetOrderConfirmationDetail(), fund, folio, req.GetSwitchInOrder())
	if err != nil {
		logger.Error(ctx, "unable to create switch in order", zap.Error(err))
		return &pb.ProcessInternalSwitchOrdersResponse{
			Status: rpc.StatusInternal(),
		}, errors2.Wrap(err, "unable to create switch in order")
	}
	err = s.createSwitchOrderAndUpdateVendorIdInConfirmationDetail(ctx, req.GetSwitchOutOrder().GetOrderConfirmationDetail(), fund, folio, req.GetSwitchOutOrder())
	if err != nil {
		logger.Error(ctx, "unable to create switch out order", zap.Error(err))
		return &pb.ProcessInternalSwitchOrdersResponse{
			Status: rpc.StatusInternal(),
		}, errors2.Wrap(err, "unable to create switch in order")
	}

	orderConfirmationDetails := []*pb.OrderConfirmationDetail{
		req.GetSwitchInOrder().GetOrderConfirmationDetail(), req.GetSwitchOutOrder().GetOrderConfirmationDetail(),
	}

	res, err := s.ProcessOrderConfirmationFromVendor(ctx, &pb.ProcessOrderConfirmationFromVendorRequest{
		ClientRequestId:          req.ClientRequestId,
		OrderConfirmationDetails: orderConfirmationDetails,
	})
	if grpcErr := epifigrpc.RPCError(res, err); grpcErr != nil {
		logger.Error(ctx, "error in ProcessOrderConfirmationFromVendor", zap.Error(grpcErr))
		return &pb.ProcessInternalSwitchOrdersResponse{
			Status: rpc.StatusInternal(),
		}, errors2.Wrap(grpcErr, "error in ProcessOrderConfirmationFromVendor")
	}

	for vendorOrderId, result := range res.GetUpdateResults() {
		updateResults[vendorOrderId] = result
	}

	return &pb.ProcessInternalSwitchOrdersResponse{
		Status:        rpc.StatusOk(),
		UpdateResults: updateResults,
	}, nil
}

func (s *Service) ProcessExternalSwitchOrders(ctx context.Context, req *pb.ProcessExternalSwitchOrdersRequest) (*pb.ProcessExternalSwitchOrdersResponse, error) {
	logger.Debug(ctx, fmt.Sprintf("received request: %s for processing external switch orders", req))
	updateResults := make(map[string]*pb.OrderUpdateResult)

	siMutualFund, soMutualFund, err := s.getFundsForSwitchOrders(ctx, req)
	if err != nil {
		logger.Error(ctx, "error in getFundsForSwitchOrders for external switch orders", zap.Error(err))
		return nil, err
	}

	siFolio, soFolio, err := s.updateSwitchInFolioIfSwitchOutFolioIsAbsent(ctx, req, siMutualFund, soMutualFund)
	if err != nil {
		logger.Error(ctx, "error in GetBySchemeCode for external switch order", zap.Error(err))
		return &pb.ProcessExternalSwitchOrdersResponse{
			Status: rpc.StatusInternal(),
		}, errors2.Wrap(err, "error in GetBySchemeCodesAndAMC for external switch order")
	}

	err = s.createExternalSwitchOrderAndUpdateVendorIdInConfirmationDetail(ctx, req.GetSwitchInOrder().GetOrderConfirmationDetail(), siMutualFund, siFolio, req.GetSwitchInOrder())
	if err != nil {
		logger.Error(ctx, "unable to create switch in order", zap.Error(err))
		return &pb.ProcessExternalSwitchOrdersResponse{
			Status: rpc.StatusInternal(),
		}, errors2.Wrap(err, "unable to create switch in order")
	}
	err = s.createExternalSwitchOrderAndUpdateVendorIdInConfirmationDetail(ctx, req.GetSwitchOutOrder().GetOrderConfirmationDetail(), soMutualFund, soFolio, req.GetSwitchOutOrder())
	if err != nil {
		logger.Error(ctx, "unable to create switch out order", zap.Error(err))
		return &pb.ProcessExternalSwitchOrdersResponse{
			Status: rpc.StatusInternal(),
		}, errors2.Wrap(err, "unable to create switch in order")
	}

	orderConfirmationDetails := []*pb.OrderConfirmationDetail{
		req.GetSwitchInOrder().GetOrderConfirmationDetail(), req.GetSwitchOutOrder().GetOrderConfirmationDetail(),
	}

	res, err := s.ProcessOrderConfirmationFromVendor(ctx, &pb.ProcessOrderConfirmationFromVendorRequest{
		ClientRequestId:          req.ClientRequestId,
		OrderConfirmationDetails: orderConfirmationDetails,
	})
	if grpcErr := epifigrpc.RPCError(res, err); grpcErr != nil {
		logger.Error(ctx, "error in ProcessOrderConfirmationFromVendor", zap.Error(grpcErr))
		return &pb.ProcessExternalSwitchOrdersResponse{
			Status: rpc.StatusInternal(),
		}, errors2.Wrap(grpcErr, "error in ProcessOrderConfirmationFromVendor")
	}

	for vendorOrderId, result := range res.GetUpdateResults() {
		updateResults[vendorOrderId] = result
	}

	return &pb.ProcessExternalSwitchOrdersResponse{
		Status:        rpc.StatusOk(),
		UpdateResults: updateResults,
	}, nil
}

func (s *Service) getFundsForSwitchOrders(ctx context.Context, req *pb.ProcessExternalSwitchOrdersRequest) (*mfPb.MutualFund, *mfPb.MutualFund, error) {
	siMutualFund, err := s.mutualFundDao.GetBySchemeCodeAndAmc(ctx, req.GetSwitchInOrder().GetOrderConfirmationDetail().GetSchemeCode(), req.GetSwitchInOrder().GetOrderConfirmationDetail().GetAmc())
	if err != nil {
		logger.Error(ctx, "error in GetBySchemeCode for external switch in order", zap.Error(err))
		return nil, nil, errors2.Wrap(err, "error in GetBySchemeCodesAndAMC for external switch in order")
	}
	soMutualFund, err := s.mutualFundDao.GetBySchemeCodeAndAmc(ctx, req.GetSwitchOutOrder().GetOrderConfirmationDetail().GetSchemeCode(), req.GetSwitchOutOrder().GetOrderConfirmationDetail().GetAmc())
	if err != nil {
		logger.Error(ctx, "error in GetBySchemeCode for external switch out order", zap.Error(err))
		return nil, nil, errors2.Wrap(err, "error in GetBySchemeCodesAndAMC for external switch out order")
	}
	return siMutualFund, soMutualFund, nil
}

func (s *Service) updateSwitchInFolioIfSwitchOutFolioIsAbsent(ctx context.Context, req *pb.ProcessExternalSwitchOrdersRequest, siMutualFund, soMutualFund *mfPb.MutualFund) (*mfPb.FolioLedger, *mfPb.FolioLedger, error) {
	soConfirmationDetail := req.GetSwitchOutOrder().GetOrderConfirmationDetail()
	soFolio, err2 := s.folioDao.GetByFilterOptions(ctx, mfPb.FolioLedgerMask_CREATED_AT, true, dao.WithFolioId(soConfirmationDetail.GetFolioId()), dao.WithMutualFundId(soMutualFund.GetId()))
	if err2 != nil || len(soFolio) == 0 {
		logger.Error(ctx, "error in fetchFundAndFolioDetails for external switch order", zap.String(logger.ORDER_TYPE, req.GetSwitchOutOrder().GetOrderType().String()),
			zap.String(logger.VENDOR_ORDER_ID, soConfirmationDetail.VendorOrderId), zap.String(logger.SCHEME_CODE, soConfirmationDetail.SchemeCode), zap.Error(err2))
		return nil, nil, errors2.Wrap(err2, "error while fetching folio id for external order")
	}
	siConfirmationDetail := req.GetSwitchInOrder().GetOrderConfirmationDetail()
	siFolio, err2 := s.folioDao.GetByFilterOptions(ctx, mfPb.FolioLedgerMask_CREATED_AT, true, dao.WithFolioId(siConfirmationDetail.GetFolioId()), dao.WithMutualFundId(siMutualFund.GetId()))
	if err2 != nil || len(siFolio) == 0 {
		if !errors.Is(err2, epifierrors.ErrRecordNotFound) {
			return nil, nil, errors2.Wrap(err2, "error while fetching folio id for external order")
		}
		folio := soFolio[0]
		folio.MutualFundId = siMutualFund.GetId()
		folio, err2 = s.folioDao.Update(ctx, folio, []mfPb.FolioLedgerMask{mfPb.FolioLedgerMask_MUTUAL_FUND_ID})
		if err2 != nil {
			return nil, nil, errors2.Wrap(err2, "error while updating folio id for external switch order")
		}
		return folio, folio, nil
	}
	return siFolio[0], soFolio[0], nil
}

func (s *Service) createSwitchOrderAndUpdateVendorIdInConfirmationDetail(ctx context.Context, confirmationDetail *pb.OrderConfirmationDetail, fund *mfPb.MutualFund,
	folio *mfPb.FolioLedger, order *pb.ProcessInternalSwitchOrdersRequest_CreateSwitchOrderPayload) error {
	// We are creating a new order at our db as orders created on external platforms will not be present in fi systems
	createdOrder, err2 := s.createSwitchOrder(ctx, confirmationDetail, fund, folio, order.OrderType, order.Rta, fund.GetAmc(), pb.OrderStatus_IN_FULFILLMENT, order.GetOrderSubType())
	if err2 != nil {
		logger.Error(ctx, "error in create internal switch order",
			zap.String(logger.ORDER_TYPE, order.OrderType.String()), zap.String(logger.VENDOR_ORDER_ID, confirmationDetail.VendorOrderId),
			zap.String(logger.SCHEME_CODE, confirmationDetail.SchemeCode), zap.String(logger.MF_AMC, fund.Amc.String()), zap.Error(err2))
		return fmt.Errorf("error in creating internal switch order")
	}
	logger.Info(ctx, "created internal switch order", zap.String(logger.ACTOR_ID_V2, createdOrder.ActorId),
		zap.String(logger.ORDER_ID, createdOrder.Id), zap.String(logger.VENDOR_ORDER_ID, createdOrder.VendorOrderId))
	confirmationDetail.VendorOrderId = createdOrder.VendorOrderId
	return nil
}

func (s *Service) createExternalSwitchOrderAndUpdateVendorIdInConfirmationDetail(ctx context.Context, confirmationDetail *pb.OrderConfirmationDetail, fund *mfPb.MutualFund,
	folio *mfPb.FolioLedger, order *pb.ProcessExternalSwitchOrdersRequest_CreateSwitchOrderPayload) error {
	// We are creating a new order at our db as orders created on external platforms will not be present in fi systems
	createdOrder, err2 := s.createSwitchOrder(ctx, confirmationDetail, fund, folio, order.OrderType, order.Rta, fund.GetAmc(), pb.OrderStatus_IN_FULFILLMENT, order.GetOrderSubType())
	if err2 != nil {
		logger.Error(ctx, "error in create external switch order",
			zap.String(logger.ORDER_TYPE, order.OrderType.String()), zap.String(logger.VENDOR_ORDER_ID, confirmationDetail.VendorOrderId),
			zap.String(logger.SCHEME_CODE, confirmationDetail.SchemeCode), zap.String(logger.MF_AMC, fund.Amc.String()), zap.Error(err2))
		return fmt.Errorf("error in creating external switch order")
	}
	logger.Info(ctx, "created external switch order", zap.String(logger.ACTOR_ID_V2, createdOrder.ActorId),
		zap.String(logger.ORDER_ID, createdOrder.Id), zap.String(logger.VENDOR_ORDER_ID, createdOrder.VendorOrderId))
	confirmationDetail.VendorOrderId = createdOrder.VendorOrderId
	return nil
}

func (s *Service) UpdateOrderConfirmationInfo(ctx context.Context, req *pb.UpdateOrderConfirmationInfoRequest) (*pb.UpdateOrderConfirmationInfoResponse, error) {

	resultMap := make(map[string]*pb.UpdateOrderConfirmationInfoResult)
	for _, val := range req.OrderConfirmationInfos {
		err := s.orderConfirmationInfoDao.UpdateByOrderId(ctx, val, req.Masks)
		if err != nil {
			logger.Error(ctx, "UpdateOrderConfirmationInfo failed", zap.Error(err), zap.String(logger.ORDER_ID, val.OrderId))
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				resultMap[val.OrderId] = &pb.UpdateOrderConfirmationInfoResult{OrderId: val.OrderId, UpdateStatus: pb.UpdateOrderConfirmationInfoResult_INVALID_ORDER_ID}
			} else {
				resultMap[val.OrderId] = &pb.UpdateOrderConfirmationInfoResult{OrderId: val.OrderId, UpdateStatus: pb.UpdateOrderConfirmationInfoResult_FAILURE}
			}
			continue
		}
		resultMap[val.OrderId] = &pb.UpdateOrderConfirmationInfoResult{OrderId: val.OrderId, UpdateStatus: pb.UpdateOrderConfirmationInfoResult_SUCCESS}
	}

	return &pb.UpdateOrderConfirmationInfoResponse{
		Status:        rpc.StatusOk(),
		UpdateResults: resultMap,
	}, nil
}

//nolint:dupl
func (s *Service) createExternalOrder(ctx context.Context, val *pb.OrderConfirmationDetail, fund *mfPb.MutualFund, folio *mfPb.FolioLedger,
	orderType pb.OrderType, rta commonvgpb.Vendor, amc mfPb.Amc, status pb.OrderStatus, orderSubType pb.OrderSubType) (*pb.Order, error) {
	// We are appending user code to vendor order id as there is a possibility of de-duplication of vendor order ids created
	// by fi order and those created by external platforms
	vendorOrderId := val.VendorOrderId + "_" + val.UserCode

	order, err := s.orderDao.GetByVendorOrderId(ctx, vendorOrderId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			order, err = s.orderDao.Create(ctx, &pb.Order{
				ActorId:            folio.ActorId,
				MutualFundId:       fund.Id,
				Units:              val.UnitsAllocated,
				Amount:             val.Amount,
				OrderType:          orderType,
				FolioId:            val.FolioId,
				Nav:                val.Nav,
				OrderStatus:        status,
				Client:             pb.OrderClient_EXTERNAL_PLATFORM,
				ClientOrderId:      uuid.New().String(),
				Rta:                rta,
				Amc:                amc,
				VendorOrderId:      vendorOrderId,
				RtaConfirmedUnits:  val.UnitsAllocated,
				RtaConfirmedAmount: val.Amount,
				OrderSubType:       orderSubType,
			})
			if err != nil {
				return nil, err
			}
			return order, nil
		}
		return nil, err
	}

	return order, err
}

//nolint:dupl
func (s *Service) createSwitchOrder(ctx context.Context, val *pb.OrderConfirmationDetail, fund *mfPb.MutualFund, folio *mfPb.FolioLedger,
	orderType pb.OrderType, rta commonvgpb.Vendor, amc mfPb.Amc, status pb.OrderStatus, orderSubType pb.OrderSubType) (*pb.Order, error) {
	// We are appending user code and order sub_type to vendor order id as there is a possibility of de-duplication of vendor order ids created
	// by fi order and those created by external platforms
	vendorOrderId := fmt.Sprintf("%s_%s_%s", val.VendorOrderId, val.UserCode, orderSubType)

	order, err := s.orderDao.GetByVendorOrderId(ctx, vendorOrderId)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			order, err = s.orderDao.Create(ctx, &pb.Order{
				ActorId:            folio.ActorId,
				MutualFundId:       fund.Id,
				Units:              val.UnitsAllocated,
				Amount:             val.Amount,
				OrderType:          orderType,
				FolioId:            val.FolioId,
				Nav:                val.Nav,
				OrderStatus:        status,
				Client:             pb.OrderClient_EXTERNAL_PLATFORM,
				ClientOrderId:      uuid.New().String(),
				Rta:                rta,
				Amc:                amc,
				VendorOrderId:      vendorOrderId,
				RtaConfirmedUnits:  val.UnitsAllocated,
				RtaConfirmedAmount: val.Amount,
				OrderSubType:       orderSubType,
			})
			if err != nil {
				return nil, err
			}
			return order, nil
		}
		return nil, err
	}
	return order, err
}

func (s *Service) fetchFundAndFolioDetails(ctx context.Context, schemeCodeToFundIdMap map[string]*mfPb.MutualFund, val *pb.OrderConfirmationDetail) (*mfPb.MutualFund, *mfPb.FolioLedger, error) {

	fund, ok := schemeCodeToFundIdMap[val.SchemeCode]
	if !ok {
		return nil, nil, fmt.Errorf("fund with scheme code not found for amc for external order")
	}

	folio, err2 := s.folioDao.GetByFilterOptions(ctx,
		mfPb.FolioLedgerMask_CREATED_AT, true, dao.WithFolioId(val.FolioId), dao.WithMutualFundId(fund.Id))
	if err2 != nil || len(folio) == 0 {
		return nil, nil, fmt.Errorf("error while fetching folio id for external order")
	}
	return fund, folio[0], nil

}

func (s *Service) getUniqueSchemeCodes(orderConfirmationDetails []*pb.OrderConfirmationDetail) []string {

	schemeCodeSeen := make(map[string]bool)
	var uniqueSchemeCodes []string

	for _, val := range orderConfirmationDetails {
		if schemeCodeSeen[val.SchemeCode] {
			continue
		} else {
			uniqueSchemeCodes = append(uniqueSchemeCodes, val.SchemeCode)
		}
	}
	return uniqueSchemeCodes
}
