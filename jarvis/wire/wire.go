//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
//go:generate go run -mod=mod github.com/google/wire/cmd/wire

package wire

import (
	"context"
	"fmt"
	"path/filepath"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/pkg/cfg"

	"github.com/epifi/gamma/jarvis"
	jarvisaws "github.com/epifi/gamma/jarvis/aws"
	"github.com/epifi/gamma/jarvis/config"
	prdeployment "github.com/epifi/gamma/jarvis/prdeployment"
	"github.com/epifi/gamma/jarvis/webhook/github/eventhandler"
	types2 "github.com/epifi/gamma/jarvis/wire/types"

	"go.temporal.io/sdk/client"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cmd/types"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/google/wire"

	servergenconf "github.com/epifi/be-common/tools/servergen/config"
	"github.com/slack-go/slack"

	githubpkg "github.com/epifi/be-common/pkg/github"

	"github.com/epifi/gamma/jarvis/activity"
	jarvisWorkerConfig "github.com/epifi/gamma/jarvis/config/worker"
	"github.com/epifi/gamma/jarvis/consul"
	approverlist "github.com/epifi/gamma/jarvis/dao/approver_list"
	"github.com/epifi/gamma/jarvis/dao/form"
	"github.com/epifi/gamma/jarvis/dao/ticket"
	"github.com/epifi/gamma/jarvis/edith"
	"github.com/epifi/gamma/jarvis/frontend"
	"github.com/epifi/gamma/jarvis/jenkins"
	"github.com/epifi/gamma/jarvis/k8s"
	jarviss3 "github.com/epifi/gamma/jarvis/s3"
	jarvissecret "github.com/epifi/gamma/jarvis/secret"
	jarvissso "github.com/epifi/gamma/jarvis/sso"
	github "github.com/epifi/gamma/jarvis/webhook/github"
)

func InitialiseJarvisService(
	conf *config.Config, envServergenConfig map[string]*servergenconf.Config,
) *jarvis.JarvisService {
	wire.Build(
		jarvis.NewJarvisService,
	)
	return &jarvis.JarvisService{}
}

func InitialiseAwsService(
	awsConfig aws.Config,
	conf *config.Config,
	envServergenConfig map[string]*servergenconf.Config,
) *jarvisaws.AwsService {
	wire.Build(
		jarvisaws.NewAwsService,
	)
	return &jarvisaws.AwsService{}
}

func InitialiseK8sService(
	awsConfig aws.Config,
	conf *config.Config,
	envServergenConfig map[string]*servergenconf.Config,
) *k8s.K8sService {
	wire.Build(
		k8s.NewK8sService,
	)
	return &k8s.K8sService{}
}

func InitialiseJenkinsService(
	conf *config.Config,
	envServergenConfig map[string]*servergenconf.Config,
) *jenkins.JenkinsService {
	wire.Build(
		jenkins.NewJenkinsService,
	)
	return &jenkins.JenkinsService{}
}

func InitialiseJarvisS3Service(
	awsConfig aws.Config,
	conf *config.Config,
) (*jarviss3.JarvisS3Service, error) {
	wire.Build(
		jarviss3.NewJarvisS3Service,
	)
	return &jarviss3.JarvisS3Service{}, nil
}

func InitialiseJarvisSecretService(
	conf *config.Config,
) *jarvissecret.JarvisSecretService {
	wire.Build(
		jarvissecret.NewJarvisSecretService,
	)
	return &jarvissecret.JarvisSecretService{}
}

func InitialiseJarvisSsoService(
	conf *config.Config,
) *jarvissso.JarvisSsoService {
	wire.Build(
		jarvissso.NewJarvisSsoService,
	)
	return &jarvissso.JarvisSsoService{}
}

func InitialiseConsulService(
	conf *config.Config,
) *consul.ConsulService {
	wire.Build(
		consul.NewConsulService,
	)
	return &consul.ConsulService{}
}

func InitialiseEdithService(
	conf *config.Config,
	db types.JarvisPGDB,
) (*edith.Service, error) {
	wire.Build(
		types.JarvisPGDBGormProvider,
		MaxPagSizeProvider,
		form.FormDaoWireSet,
		ticket.TicketDaoWireSet,
		approverlist.ApproverListDaoWireSet,
		storagev2.TxnExecutorWireSet,
		edith.NewService,
	)
	return &edith.Service{}, nil
}

func InitialiseFrontendService(
	conf *config.Config,
	edithService *edith.Service,
	temporalClient *client.Client,
) (*frontend.Service, error) {
	wire.Build(
		frontend.NewService,
	)
	return &frontend.Service{}, nil
}

func MaxPagSizeProvider(conf *config.Config) uint32 {
	return conf.MaxPageSize
}

func InitialisePrDeploymentService(
	conf *config.Config,
	temporalClient client.Client,
	awsConfig aws.Config,
	slackToken types2.SlackToken,
) *prdeployment.PrDeploymentService {
	wire.Build(
		prdeployment.NewPrDeploymentService,
		s3ClientProvider,
		slackClientProvider,
	)
	return &prdeployment.PrDeploymentService{}
}

func InitialiseActivityProcessor(
	jarvisService *jarvis.JarvisService,
	edithService *edith.Service,
	frontendService *frontend.Service,
	workerConf *jarvisWorkerConfig.Config,
	slackToken types2.SlackToken,
	awsConfig aws.Config,
) (*activity.Processor, error) {
	wire.Build(
		activity.NewProcessor,
		jenkinsClientProvider,
		awsServiceProvider,
		contextProvider,
		slackClientProvider,
	)
	return &activity.Processor{}, nil
}

func InitialiseGithubWebhookService(ctx context.Context, conf *config.Config, slackToken types2.SlackToken) (*github.GithubWebhookService, error) {
	wire.Build(
		github.NewGithubWebhookService,
		slackClientProvider,
		eventhandler.NewIssuesEventHandler,
		eventHandlerProvider,
		initialiseGithubApiWrapper,
	)
	return &github.GithubWebhookService{}, nil
}

func jenkinsClientProvider(ctx context.Context, conf *jarvisWorkerConfig.Config) (*jenkins.Client, error) {
	var password string
	if conf.Application.Environment == cfg.ProductionEnv {
		password = conf.Secrets.Ids[config.JenkinsToken]
	} else {
		password = conf.Secrets.Ids[config.JenkinsTokenNonProd]
	}
	return jenkins.NewClient(ctx, conf.JenkinsUrlBase, conf.JenkinsUsername, password)
}

func awsServiceProvider(awsConfig aws.Config, conf *jarvisWorkerConfig.Config) (*jarvisaws.AwsService, error) {
	serverConf, err := jarvisWorkerConfig.LoadServerConfig(conf)
	if err != nil {
		return nil, fmt.Errorf("failed to load server config: %w", err)
	}
	envServergenConfig := make(map[string]*servergenconf.Config)
	for _, env := range conf.Envs {
		confDir, err := cfg.GetConfigDir()
		if err != nil {
			return nil, fmt.Errorf("failed to get config dir: %w", err)
		}
		serverDefFilePath := filepath.Join(confDir, fmt.Sprintf(servergenconf.ServerDefConfigFile, env))
		defconf, err := servergenconf.Load(serverDefFilePath)
		if err != nil {
			return nil, fmt.Errorf("failed to load servergen config for env %s: %w", env, err)
		}
		envServergenConfig[env] = defconf
	}
	return jarvisaws.NewAwsService(awsConfig, serverConf, envServergenConfig), nil
}

func contextProvider() context.Context {
	return context.Background()
}

func s3ClientProvider(awsConfig aws.Config, conf *config.Config) *s3.Client {
	return s3.NewClient(awsConfig, conf.PlatformS3Bucket)
}

func slackClientProvider(slackToken types2.SlackToken) *slack.Client {
	return slack.New(string(slackToken), slack.OptionDebug(false))
}

func eventHandlerProvider(issueHandler *eventhandler.IssuesEventHandler) map[string]eventhandler.EventHandler {
	return eventhandler.InitEventHandlerMap(issueHandler)
}

func initialiseGithubApiWrapper(ctx context.Context, conf *config.Config) (githubpkg.IGithubApiWrapper, error) {
	clientManager, err := githubpkg.NewGitHubClientManager(ctx, conf.GithubApp.AppId, conf.GithubApp.PrivateKey, conf.GithubApp.InstallationId)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize GitHub client manager: %w", err)
	}
	return githubpkg.NewGithubApiWrapper(clientManager), nil
}
