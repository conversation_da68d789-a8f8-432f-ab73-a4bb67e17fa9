package github

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"crypto/subtle"
	"encoding/hex"
	"io"
	"io/ioutil"
	"net/http"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	jarvisconf "github.com/epifi/gamma/jarvis/config"
	"github.com/epifi/gamma/jarvis/webhook/github/eventhandler"
)

// GithubWebhookService handles incoming GitHub webhook events and dispatches them to the appropriate event handler.
type GithubWebhookService struct {
	eventHandlerMap map[string]eventhandler.EventHandler
	conf            *jarvisconf.Config
}

func NewGithubWebhookService(conf *jarvisconf.Config, eventHandlerMap map[string]eventhandler.EventHandler) *GithubWebhookService {
	return &GithubWebhookService{
		conf:            conf,
		eventHandlerMap: eventHandlerMap,
	}
}

// GitHubWebhookHandler dispatches the event to the correct handler based on event type.
func (g *GithubWebhookService) GitHubWebhookHandler(w http.ResponseWriter, r *http.Request) {
	eventType := r.Header.Get("X-GitHub-Event")
	if eventType == "" {
		http.Error(w, "Missing X-GitHub-Event header", http.StatusBadRequest)
		return
	}

	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		logger.ErrorNoCtx("Failed to read webhook body", zap.Error(err))
		http.Error(w, "Failed to read body", http.StatusInternalServerError)
		return
	}
	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			logger.ErrorNoCtx("Failed to close request body", zap.Error(err))
		}
	}(r.Body)

	secret := g.conf.GithubApp.WebhookSecret
	if secret == "" {
		http.Error(w, "Webhook secret not configured", http.StatusUnauthorized)
		return
	}
	signature := r.Header.Get("X-Hub-Signature-256")
	if signature == "" {
		http.Error(w, "Missing X-Hub-Signature-256 header", http.StatusUnauthorized)
		return
	}
	computedSig := computeHMACSHA256(body, []byte(secret))
	expectedSig := "sha256=" + computedSig
	if !hmacEqual(signature, expectedSig) {
		logger.ErrorNoCtx("Webhook signature mismatch", zap.String("expected", expectedSig), zap.String("got", signature))
		http.Error(w, "Invalid signature", http.StatusUnauthorized)
		return
	}

	handler, ok := g.eventHandlerMap[eventType]
	if !ok {
		logger.DebugNoCtx("custom logic is not implemented for this event", zap.String("event", eventType))
		// Unknown event type, ignore gracefully
		w.WriteHeader(http.StatusOK)
		_, writeErr := w.Write([]byte("ok"))
		if writeErr != nil {
			logger.ErrorNoCtx("Failed to write response", zap.Error(writeErr), zap.String("event", eventType))
			return
		}
		return
	}

	if err := handler.Handle(context.Background(), body); err != nil {
		logger.ErrorNoCtx("Event handler error", zap.Error(err), zap.String("event", eventType))
		w.WriteHeader(http.StatusInternalServerError)
		_, writeErr := w.Write([]byte("error"))
		if writeErr != nil {
			logger.ErrorNoCtx("Failed to write error response", zap.Error(writeErr), zap.String("event", eventType))
			return
		}
		return
	}

	w.WriteHeader(http.StatusOK)
	_, writeErr := w.Write([]byte("ok"))
	if writeErr != nil {
		logger.ErrorNoCtx("Failed to write response", zap.Error(writeErr), zap.String("event", eventType))
		return
	}
}

// computeHMACSHA256 computes the HMAC SHA256 hex digest for the given data and secret
func computeHMACSHA256(data, secret []byte) string {
	h := hmac.New(sha256.New, secret)
	h.Write(data)
	return hex.EncodeToString(h.Sum(nil))
}

// hmacEqual safely compares two HMAC signatures
func hmacEqual(a, b string) bool {
	return subtle.ConstantTimeCompare([]byte(a), []byte(b)) == 1
}
