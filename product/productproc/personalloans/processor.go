package personalloans

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/product/productproc"
)

type PLProductProc struct {
	palClient palBePb.PreApprovedLoanClient
}

func NewPLProductProc(palClient palBePb.PreApprovedLoanClient) *PLProductProc {
	return &PLProductProc{
		palClient: palClient,
	}
}

var (
	_ productproc.ProductProc = (*PLProductProc)(nil)
)

func (p *PLProductProc) GetUserPriority(ctx context.Context, req *productproc.GetUserPriorityRequest) (*productproc.GetUserPriorityResponse, error) {
	return nil, epifierrors.ErrMethodUnimplemented
}

func (p *PLProductProc) GetProductStatus(ctx context.Context, req *productproc.GetProductStatusRequest) (*productproc.GetProductStatusResponse, error) {
	if req.ActorId == "" {
		return nil, epifierrors.ErrInvalidArgument
	}

	resp, err := p.palClient.GetLoanUserStatusV2(ctx, &palBePb.GetLoanUserStatusV2Request{
		ActorId: req.ActorId,
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		logger.Error(ctx, "error faced while getting loans user status", zap.Error(grpcErr))
		return nil, grpcErr
	}

	return &productproc.GetProductStatusResponse{
		ProductInfo: &product.ProductInfo{
			ProductStatus: getProductStatusFromLoansUserStatus(resp.GetUserStatus()),
			ActivatedAt:   resp.LoanEventTimestamps.GetLatestApplicationReqStartTimestamp(),
		},
	}, nil
}

func getProductStatusFromLoansUserStatus(status palBePb.UserStatus) product.ProductStatus {
	switch status {
	case palBePb.UserStatus_USER_STATUS_ACTIVE_LOAN, palBePb.UserStatus_USER_STATUS_ACTIVE_APPLICATION:
		return product.ProductStatus_PRODUCT_STATUS_ACTIVE
	case palBePb.UserStatus_USER_STATUS_REJECTED:
		return product.ProductStatus_PRODUCT_STATUS_REJECTED
	default:
		return product.ProductStatus_PRODUCT_STATUS_UNSPECIFIED
	}
}
