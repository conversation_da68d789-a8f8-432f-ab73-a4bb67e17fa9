Development:
  TableName: rewards_non_terminal_development
  TokenBasedAuthForRealtimeIngestion: true
Test:
  TableName: rewards_non_terminal_test
  TokenBasedAuthForRealtimeIngestion: true
Staging:
  TableName: rewards_non_terminal_staging
  TopicName: "staging.service.rewards.non_terminal_state_topic"
  KafkaBrokerList: "kafka-1.data-dev.pointz.in:9094,kafka-2.data-dev.pointz.in:9094,kafka-3.data-dev.pointz.in:9094"
  SslTruststoreLocation: "/tmp/dst-s3/kafka2.jks"
Qa:
  TableName: rewards_non_terminal_qa
  RealtimePartitions: 3
  RealtimeReplication: 2
  OfflinePartitions: 3
  OfflineReplication: 2
  TopicName: "qa.service.rewards.non_terminal_state_topic"
  KafkaBrokerList: "kafka-1.data-dev.pointz.in:9094,kafka-2.data-dev.pointz.in:9094,kafka-3.data-dev.pointz.in:9094"
  SslTruststoreLocation: "/tmp/dst-s3/kafka2.jks"
Uat:
  TableName: rewards_non_terminal_uat
  TopicName: "uat.service.rewards.non_terminal_state_topic"
  KafkaBrokerList: "kafka-1.data-dev.pointz.in:9094,kafka-2.data-dev.pointz.in:9094,kafka-3.data-dev.pointz.in:9094"
  SslTruststoreLocation: "/tmp/dst-s3/kafka2.jks"
Demo:
  TableName: rewards_non_terminal_demo
  TopicName: "demo.service.rewards.non_terminal_state_topic"
  KafkaBrokerList: "kafka-1.data-dev.pointz.in:9094,kafka-2.data-dev.pointz.in:9094,kafka-3.data-dev.pointz.in:9094"
  SslTruststoreLocation: "/tmp/dst-s3/kafka2.jks"
Prod:
  TableName: rewards_non_terminal_prod
  RealtimePartitions: 3
  RealtimeReplication: 2
  OfflinePartitions: 3
  OfflineReplication: 2
  FlushThresholdTime: "96h"
  TopicName: "prod.service.rewards.non_terminal_state_topic"
  KafkaBrokerList: "kafka-4.data-prod.epifi.in:9094,kafka-5.data-prod.epifi.in:9094,kafka-6.data-prod.epifi.in:9094"
  SslTruststoreLocation: "/etc/kafka-certs/kafka.jks"
