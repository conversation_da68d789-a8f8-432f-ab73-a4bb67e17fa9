package celestial

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"

	logHelper "github.com/epifi/gamma/celestial/internal/log"
)

// QueryWorkflow fetches the current state variables of a running workflow
// It uses Temporal's QueryWorkflow API to retrieve the state without affecting the workflow execution
func (s *Service) QueryWorkflow(ctx context.Context, req *celestialPb.QueryWorkflowRequest) (*celestialPb.QueryWorkflowResponse, error) {
	var (
		res         = &celestialPb.QueryWorkflowResponse{}
		workflowReq *celestialPb.WorkflowRequest
		err         error
	)

	// First, get the workflow request ID
	switch req.GetIdentifier().(type) {
	case *celestialPb.QueryWorkflowRequest_ClientReqId:
		// Fetching workflow requests using client request ids
		workflowReqs, wfErr := s.workflowRequestDao.BatchGetByClientReqId(ctx, []*workflowPb.ClientReqId{req.GetClientReqId()}, req.GetOwnership(), req.GetUseCase())
		switch {
		case errors.Is(wfErr, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "no workflow request found to query", logHelper.WorkflowClientReqID(req.GetClientReqId())...)
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		case wfErr != nil:
			logger.Error(ctx, "error in fetching workflow request", logHelper.WorkflowClientReqID(req.GetClientReqId(), zap.Error(wfErr))...)
			res.Status = rpcPb.StatusInternal()
			return res, nil
		default:
			logger.Debug(ctx, "workflow request fetched successfully", logHelper.WorkflowClientReqID(req.GetClientReqId())...)
		}
		if len(workflowReqs) == 0 {
			logger.Error(ctx, "no workflow request found to query", logHelper.WorkflowClientReqID(req.GetClientReqId())...)
			res.Status = rpcPb.StatusRecordNotFound()
			return res, nil
		}
		workflowReq = workflowReqs[0]
	case *celestialPb.QueryWorkflowRequest_WorkflowReqId:
		wfRequestId := req.GetWorkflowReqId()
		// Get the workflow request details
		workflowReq, err = s.workflowRequestDao.GetByID(ctx, wfRequestId, req.GetOwnership(), req.GetUseCase())
		if err != nil {
			switch {
			case errors.Is(err, epifierrors.ErrRecordNotFound):
				logger.Error(ctx, "workflow request not found", zap.String(logger.WORKFLOW_REQ_ID, wfRequestId))
				res.Status = rpcPb.StatusRecordNotFound()
				return res, nil
			default:
				logger.Error(ctx, "error in fetching workflow request", zap.String(logger.WORKFLOW_REQ_ID, wfRequestId), zap.Error(err))
				res.Status = rpcPb.StatusInternal()
				return res, nil
			}
		}
	default:
		logger.Error(ctx, "unhandled identifier type")
		res.Status = rpcPb.StatusInvalidArgument()
		return res, nil
	}

	// Use temporalProc to query the workflow
	queryResult, err := s.temporalProc.QueryWorkflow(ctx, workflowReq, req.GetQueryType(), req.GetQueryArgs())
	if err != nil {
		// Handle different error types based on the error type returned by temporalProc
		switch {
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			logger.Error(ctx, "invalid query parameters",
				zap.String(logger.WORKFLOW_REQ_ID, workflowReq.GetId()),
				zap.String(logger.QUERY_NAME, req.GetQueryType()),
				zap.Error(err))
			res.Status = rpcPb.StatusInvalidArgument()
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			logger.Error(ctx, "workflow execution not found",
				zap.String(logger.WORKFLOW_REQ_ID, workflowReq.GetId()),
				zap.Error(err))
			res.Status = rpcPb.StatusRecordNotFound()
		default:
			logger.Error(ctx, "error in querying workflow",
				zap.String(logger.WORKFLOW_REQ_ID, workflowReq.GetId()),
				zap.String(logger.QUERY_NAME, req.GetQueryType()),
				zap.Error(err))
			res.Status = rpcPb.StatusInternal()
		}
		return res, nil
	}

	// Return the query result
	res.Status = rpcPb.StatusOk()
	res.QueryResult = queryResult
	return res, nil
}
