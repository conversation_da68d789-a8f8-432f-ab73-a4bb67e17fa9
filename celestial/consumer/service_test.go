package consumer_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/epifi/be-common/pkg/epifitemporal"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/epifierrors"

	celestialPb "github.com/epifi/be-common/api/celestial"
	consumerPb "github.com/epifi/be-common/api/celestial/consumer"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"

	"github.com/epifi/gamma/celestial/internal"
)

func TestService_SignalWorkflow(t *testing.T) {
	type args struct {
		ctx context.Context
		req *consumerPb.SignalWorkflowRequest
	}

	type mockGetWorkflowRequestByClient struct {
		enable       bool
		clientReqIds []*workflowPb.ClientReqId
		want         []*celestialPb.WorkflowRequest
		err          error
		ownership    commontypes.Ownership
	}

	type mockGetWorkflowRequestByID struct {
		enable        bool
		workflowReqID string
		want          *celestialPb.WorkflowRequest
		err           error
		ownership     commontypes.Ownership
	}

	type mockSignalWorkflow struct {
		enable          bool
		workflowRequest *celestialPb.WorkflowRequest
		signalId        epifitemporal.Signal
		err             error
	}

	tests := []struct {
		name                           string
		args                           args
		mockGetWorkflowRequestByClient mockGetWorkflowRequestByClient
		mockGetWorkflowRequestByID     mockGetWorkflowRequestByID
		mockSignalWorkflow             mockSignalWorkflow
		want                           *consumerPb.SignalWorkflowResponse
		wantErr                        bool
	}{
		{
			name: "failed - wrong client req id - workflowReq not found in DB",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Identifier: &consumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "some_random_id",
							Client: workflowPb.Client_CX,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "some_random_id",
						Client: workflowPb.Client_CX,
					},
				},
				want: nil,
				err:  epifierrors.ErrRecordNotFound,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed - client req id - invalid argument",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Identifier: &consumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable:       true,
				clientReqIds: []*workflowPb.ClientReqId{{}},
				want:         nil,
				err:          epifierrors.ErrInvalidArgument,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed - failed to fetch workflow req due to DB issue - clientReqID was given",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Identifier: &consumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "some_random_id",
							Client: workflowPb.Client_CX,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "some_random_id",
						Client: workflowPb.Client_CX,
					},
				},
				want: nil,
				err:  errors.New("DB issue"),
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed - wrong workflow req id - workflowReq not found in DB",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Identifier: &consumerPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "workflow_req_id",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want:          nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed - wrong workflow req id - invalid argument",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Identifier: &consumerPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "",
				want:          nil,
				err:           epifierrors.ErrInvalidArgument,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed - failed to fetch workflowReq due to internal DB issue",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Identifier: &consumerPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "workflow_req_id",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want:          nil,
				err:           errors.New("DB issue"),
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to signal workflow - unknown identifier passed",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Identifier: nil,
				},
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to signal workflow due to some internal error from sdk",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &consumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CX,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Status:    stagePb.Status_SUCCESSFUL,
						TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
						StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
					},
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Status:    stagePb.Status_SUCCESSFUL,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      epifierrors.ErrPermissionDenied,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "signalled the workflow successfully when client req id is passed",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &consumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CX,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Status:    stagePb.Status_SUCCESSFUL,
						TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
						StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
					},
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Status:    stagePb.Status_SUCCESSFUL,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      nil,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "signaled the workflow successfully when client req id is passed for v1 workflow",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &consumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CX,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Status:    stagePb.Status_SUCCESSFUL,
						Version:   workflowPb.Version_V1,
						TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
						StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
					},
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Status:    stagePb.Status_SUCCESSFUL,
					Version:   workflowPb.Version_V1,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      nil,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "signalled the workflow successfully, when workflow req id is passed",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &consumerPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want: &celestialPb.WorkflowRequest{
					Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Status:    stagePb.Status_SUCCESSFUL,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Status:    stagePb.Status_SUCCESSFUL,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      nil,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "failed - wrong client req id - workflowReq not found in DB",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					Identifier: &consumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "some_random_id",
							Client: workflowPb.Client_CX,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				enable:    true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "some_random_id",
						Client: workflowPb.Client_CX,
					},
				},
				want: nil,
				err:  epifierrors.ErrRecordNotFound,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed - failed to fetch workflow req due to DB issue - clientReqID was given",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					Identifier: &consumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "some_random_id",
							Client: workflowPb.Client_CX,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				enable:    true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "some_random_id",
						Client: workflowPb.Client_CX,
					},
				},
				want: nil,
				err:  errors.New("DB issue"),
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed - wrong workflow req id - workflowReq not found in DB",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					Identifier: &consumerPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "workflow_req_id",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
				enable:        true,
				workflowReqID: "workflow_req_id",
				want:          nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed - failed to fetch workflowReq due to internal DB issue",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					Identifier: &consumerPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "workflow_req_id",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
				enable:        true,
				workflowReqID: "workflow_req_id",
				want:          nil,
				err:           errors.New("DB issue"),
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to signal workflow - unknown identifier passed",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Ownership:  commontypes.Ownership_US_STOCKS_ALPACA,
					Identifier: nil,
				},
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed to signal workflow due to some internal error from sdk",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					SignalId:  "my-signal",
					Payload:   nil,
					Identifier: &consumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CX,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				enable:    true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Status:    stagePb.Status_SUCCESSFUL,
						TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
						StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
					},
				},
				err: nil,
			},

			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Status:    stagePb.Status_SUCCESSFUL,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      epifierrors.ErrPermissionDenied,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "signalled the workflow successfully when client req id is passed",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					SignalId:  "my-signal",
					Payload:   nil,
					Identifier: &consumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CX,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				enable:    true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Status:    stagePb.Status_SUCCESSFUL,
						TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
						StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
					},
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Status:    stagePb.Status_SUCCESSFUL,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      nil,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "signalled the workflow successfully, when workflow req id is passed",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.SignalWorkflowRequest{
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					SignalId:  "my-signal",
					Payload:   nil,
					Identifier: &consumerPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
				enable:        true,
				workflowReqID: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want: &celestialPb.WorkflowRequest{
					Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Status:    stagePb.Status_SUCCESSFUL,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:        "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Status:    stagePb.Status_SUCCESSFUL,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      nil,
			},
			want: &consumerPb.SignalWorkflowResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newServiceWithMocks(t)
			internal.ClearNamespaceClient()
			if tt.mockGetWorkflowRequestByClient.enable {
				md.workflowRequestDao.EXPECT().BatchGetByClientReqId(gomock.Any(), tt.mockGetWorkflowRequestByClient.clientReqIds, tt.mockGetWorkflowRequestByClient.ownership, gomock.Any()).Return(
					tt.mockGetWorkflowRequestByClient.want, tt.mockGetWorkflowRequestByClient.err)
			}

			if tt.mockGetWorkflowRequestByID.enable {
				md.workflowRequestDao.EXPECT().GetByID(gomock.Any(), tt.mockGetWorkflowRequestByID.workflowReqID, tt.mockGetWorkflowRequestByID.ownership, gomock.Any()).Return(
					tt.mockGetWorkflowRequestByID.want, tt.mockGetWorkflowRequestByID.err)
			}

			if tt.mockSignalWorkflow.enable {
				md.temporalProcessor.EXPECT().SignalWorkflow(gomock.Any(), tt.mockSignalWorkflow.workflowRequest,
					tt.mockSignalWorkflow.signalId, gomock.Any()).Return(tt.mockSignalWorkflow.err)
			}
			got, err := svc.SignalWorkflow(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("SignalWorkflowV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got.ResponseHeader.Status != tt.want.ResponseHeader.Status {
				t.Errorf("SignalWorkflowV2() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestService_InitiateWorkflow(t *testing.T) {
	type args struct {
		ctx context.Context
		req *consumerPb.InitiateWorkflowRequest
	}
	type mockInitiateWorkflow struct {
		enable      bool
		workflowReq *celestialPb.WorkflowRequest
		err         error
	}
	type mockGetWorkflowRequestDao struct {
		enable    bool
		id        string
		res       *celestialPb.WorkflowRequest
		err       error
		ownership commontypes.Ownership
	}
	tests := []struct {
		name                      string
		args                      args
		mockInitiateWorkflow      mockInitiateWorkflow
		mockGetWorkflowRequestDao mockGetWorkflowRequestDao
		want                      *consumerPb.InitiateWorkflowResponse
		wantErr                   bool
	}{
		{
			name: "error in initiating workflow due to invalid workflow request id",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.InitiateWorkflowRequest{
					WorkflowReqId: "workflow_req_id",
				},
			},
			mockGetWorkflowRequestDao: mockGetWorkflowRequestDao{
				enable: true,
				id:     "workflow_req_id",
				res:    nil,
				err:    epifierrors.ErrRecordNotFound,
			},
			want: &consumerPb.InitiateWorkflowResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
			},
			wantErr: false,
		},
		{
			name: "error in initiating workflow due to connection issues",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.InitiateWorkflowRequest{
					WorkflowReqId: "workflow_req_id",
				},
			},
			mockGetWorkflowRequestDao: mockGetWorkflowRequestDao{
				enable: true,
				id:     "workflow_req_id",
				res:    nil,
				err:    errors.New("error"),
			},
			want: &consumerPb.InitiateWorkflowResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
			},
			wantErr: false,
		},
		{
			name: "initiated workflow successfully",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.InitiateWorkflowRequest{
					WorkflowReqId: "workflow_req_id",
				},
			},
			mockGetWorkflowRequestDao: mockGetWorkflowRequestDao{
				enable: true,
				id:     "workflow_req_id",
				res: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
				err: nil,
			},
			mockInitiateWorkflow: mockInitiateWorkflow{
				enable: true,
				workflowReq: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
				err: nil,
			},
			want: &consumerPb.InitiateWorkflowResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_SUCCESS,
			},
			},
			wantErr: false,
		},
		{
			name: "error in initiating workflow due to invalid workflow request id",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.InitiateWorkflowRequest{
					Ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
					WorkflowReqId: "workflow_req_id",
				},
			},
			mockGetWorkflowRequestDao: mockGetWorkflowRequestDao{
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				enable:    true,
				id:        "workflow_req_id",
				res:       nil,
				err:       epifierrors.ErrRecordNotFound,
			},
			want: &consumerPb.InitiateWorkflowResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
			},
			wantErr: false,
		},
		{
			name: "error in initiating workflow due to connection issues in fetching workflow request",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.InitiateWorkflowRequest{
					Ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
					WorkflowReqId: "workflow_req_id",
				},
			},
			mockGetWorkflowRequestDao: mockGetWorkflowRequestDao{
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				enable:    true,
				id:        "workflow_req_id",
				res:       nil,
				err:       errors.New("error"),
			},
			want: &consumerPb.InitiateWorkflowResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
			},
			wantErr: false,
		},
		{
			name: "initiated workflow successfully",
			args: args{
				ctx: context.Background(),
				req: &consumerPb.InitiateWorkflowRequest{
					Ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
					WorkflowReqId: "workflow_req_id",
				},
			},
			mockGetWorkflowRequestDao: mockGetWorkflowRequestDao{
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				enable:    true,
				id:        "workflow_req_id",
				res: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_US_STOCKS,
					},
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
				err: nil,
			},
			mockInitiateWorkflow: mockInitiateWorkflow{
				enable: true,
				workflowReq: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_US_STOCKS,
					},
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
				err: nil,
			},
			want: &consumerPb.InitiateWorkflowResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_SUCCESS,
			},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newServiceWithMocks(t)

			if tt.mockGetWorkflowRequestDao.enable {
				md.workflowRequestDao.EXPECT().GetByID(gomock.Any(), tt.mockGetWorkflowRequestDao.id, tt.mockGetWorkflowRequestDao.ownership, gomock.Any()).Return(
					tt.mockGetWorkflowRequestDao.res, tt.mockGetWorkflowRequestDao.err)
			}
			if tt.mockInitiateWorkflow.enable {
				md.temporalProcessor.EXPECT().InitiateWorkflow(gomock.Any(), tt.mockInitiateWorkflow.workflowReq, commontypes.UseCase_USE_CASE_UNSPECIFIED).Return(
					tt.mockInitiateWorkflow.err)
			}
			got, err := svc.InitiateWorkflow(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateWorkflowV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("InitiateWorkflowV2() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}
