// nolint: goimports
package celestial_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/mock"
	"go.temporal.io/api/common/v1"
	temporalEnums "go.temporal.io/api/enums/v1"
	"go.temporal.io/api/history/v1"
	"go.temporal.io/api/workflow/v1"
	"go.temporal.io/api/workflowservice/v1"
	temporalMocks "go.temporal.io/sdk/mocks"

	"github.com/epifi/be-common/pkg/epifitemporal"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	testPkg "github.com/epifi/be-common/pkg/test"

	celestialPb "github.com/epifi/be-common/api/celestial"
	celestialConsumerPb "github.com/epifi/be-common/api/celestial/consumer"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"

	"github.com/epifi/gamma/celestial/internal"
)

func TestService_GetWorkflowHistory(t *testing.T) {
	type args struct {
		ctx context.Context
		req *celestialPb.GetWorkflowHistoryRequest
	}

	type mockWorkflowHistoryDao struct {
		enable        bool
		workflowReqId string
		want          []*celestialPb.WorkflowHistory
		err           error
		ownership     commontypes.Ownership
		useCase       commontypes.UseCase
	}

	type mockWorkflowRequestDao struct {
		enable       bool
		clientReqIds []*workflowPb.ClientReqId
		want         []*celestialPb.WorkflowRequest
		err          error
		ownership    commontypes.Ownership
		useCase      commontypes.UseCase
	}

	tests := []struct {
		name                   string
		args                   args
		mockWorkflowHistoryDao mockWorkflowHistoryDao
		mockWorkflowRequestDao mockWorkflowRequestDao
		want                   *celestialPb.GetWorkflowHistoryResponse
		wantErr                bool
	}{
		{
			name: "successfully fetched workflow history list using client request id",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.GetWorkflowHistoryRequest{
					Identifier: &celestialPb.GetWorkflowHistoryRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockWorkflowHistoryDao: mockWorkflowHistoryDao{
				enable:        true,
				workflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want: []*celestialPb.WorkflowHistory{
					{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:         workflowPb.Stage_PAYMENT,
						Status:        stagePb.Status_SUCCESSFUL,
					},
					{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:         workflowPb.Stage_CREATION,
						Status:        stagePb.Status_INITIATED,
					},
				},
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				err:       nil,
			},
			mockWorkflowRequestDao: mockWorkflowRequestDao{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:     "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:  workflowPb.Stage_PAYMENT,
						Status: stagePb.Status_SUCCESSFUL,
					},
				},
				err:       nil,
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
			},
			want: &celestialPb.GetWorkflowHistoryResponse{
				Status: rpc.StatusOk(),
				WorkflowHistoryList: []*celestialPb.WorkflowHistory{
					{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:         workflowPb.Stage_PAYMENT,
						Status:        stagePb.Status_SUCCESSFUL,
					},
					{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:         workflowPb.Stage_CREATION,
						Status:        stagePb.Status_INITIATED,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "successfully fetched workflow history list using workflow request id",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.GetWorkflowHistoryRequest{
					Identifier: &celestialPb.GetWorkflowHistoryRequest_WorkflowRequestId{
						WorkflowRequestId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockWorkflowHistoryDao: mockWorkflowHistoryDao{
				enable:        true,
				workflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want: []*celestialPb.WorkflowHistory{
					{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:         workflowPb.Stage_PAYMENT,
						Status:        stagePb.Status_SUCCESSFUL,
					},
					{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:         workflowPb.Stage_CREATION,
						Status:        stagePb.Status_INITIATED,
					},
				},
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				err:       nil,
			},
			want: &celestialPb.GetWorkflowHistoryResponse{
				Status: rpc.StatusOk(),
				WorkflowHistoryList: []*celestialPb.WorkflowHistory{
					{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:         workflowPb.Stage_PAYMENT,
						Status:        stagePb.Status_SUCCESSFUL,
					},
					{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:         workflowPb.Stage_CREATION,
						Status:        stagePb.Status_INITIATED,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "failed to fetch workflow history list as workflow request id is not found",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.GetWorkflowHistoryRequest{
					Identifier: &celestialPb.GetWorkflowHistoryRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockWorkflowRequestDao: mockWorkflowRequestDao{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:     "some_random_workflow_request_id",
						Stage:  workflowPb.Stage_PAYMENT,
						Status: stagePb.Status_SUCCESSFUL,
					},
				},
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				err:       nil,
			},
			mockWorkflowHistoryDao: mockWorkflowHistoryDao{
				enable:        true,
				workflowReqId: "some_random_workflow_request_id",
				want:          nil,
				err:           epifierrors.ErrRecordNotFound,
				ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
			},
			want: &celestialPb.GetWorkflowHistoryResponse{
				Status:              rpc.StatusRecordNotFound(),
				WorkflowHistoryList: nil,
			},
			wantErr: false,
		},
		{
			name: "failed to fetch workflow req list as client request id is not found",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.GetWorkflowHistoryRequest{
					Identifier: &celestialPb.GetWorkflowHistoryRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockWorkflowRequestDao: mockWorkflowRequestDao{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want:      nil,
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				err:       epifierrors.ErrRecordNotFound,
			},
			want: &celestialPb.GetWorkflowHistoryResponse{
				Status:              rpc.StatusRecordNotFound(),
				WorkflowHistoryList: nil,
			},
			wantErr: false,
		},
		{
			name: "failed to fetch workflow req list due to some internal db issue",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.GetWorkflowHistoryRequest{
					Identifier: &celestialPb.GetWorkflowHistoryRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
				},
			},
			mockWorkflowRequestDao: mockWorkflowRequestDao{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: nil,
				err:  epifierrors.ErrPermissionDenied,
			},
			want: &celestialPb.GetWorkflowHistoryResponse{
				Status:              rpc.StatusInternal(),
				WorkflowHistoryList: nil,
			},
			wantErr: false,
		},
		{
			name: "failed to fetch workflow history list due to some internal db issue",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.GetWorkflowHistoryRequest{
					Identifier: &celestialPb.GetWorkflowHistoryRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
				},
			},
			mockWorkflowRequestDao: mockWorkflowRequestDao{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:     "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:  workflowPb.Stage_PAYMENT,
						Status: stagePb.Status_SUCCESSFUL,
					},
				},
				err: nil,
			},
			mockWorkflowHistoryDao: mockWorkflowHistoryDao{
				enable:        true,
				workflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want:          nil,
				err:           epifierrors.ErrPermissionDenied,
			},
			want: &celestialPb.GetWorkflowHistoryResponse{
				Status:              rpc.StatusInternal(),
				WorkflowHistoryList: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newServerWithMocks(t)
			if tt.mockWorkflowRequestDao.enable {
				md.workflowRequestDao.EXPECT().BatchGetByClientReqId(gomock.Any(), tt.mockWorkflowRequestDao.clientReqIds,
					tt.mockWorkflowRequestDao.ownership, tt.mockWorkflowRequestDao.useCase).Return(
					tt.mockWorkflowRequestDao.want, tt.mockWorkflowRequestDao.err)
			}
			if tt.mockWorkflowHistoryDao.enable {
				md.workflowHistoryDao.EXPECT().GetByWorkflowReqID(gomock.Any(), tt.mockWorkflowHistoryDao.workflowReqId,
					tt.mockWorkflowHistoryDao.ownership, tt.mockWorkflowHistoryDao.useCase).Return(
					tt.mockWorkflowHistoryDao.want, tt.mockWorkflowHistoryDao.err)
			}
			got, err := svc.GetWorkflowHistory(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWorkflowHistory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetWorkflowHistory() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestService_InitiateWorkflow(t *testing.T) {
	type args struct {
		ctx context.Context
		req *celestialPb.InitiateWorkflowRequest
	}
	type mockCreateWorkflowRequest struct {
		enable          bool
		req             *celestialPb.WorkflowCreationRequestParams
		want            *celestialPb.WorkflowCreationResponseParams
		wantWorkflowReq *celestialPb.WorkflowRequest
		err             error
	}
	type mockInitiateWorkflowPublisher struct {
		enable bool
		req    *celestialConsumerPb.InitiateWorkflowRequest
		res    string
		err    error
	}
	type mockValidateWorkflow struct {
		enable bool
		err    error
	}
	type mockInitiateWorkflow struct {
		enable      bool
		workflowReq *celestialPb.WorkflowRequest
		err         error
	}
	tests := []struct {
		name                          string
		mockCreateWorkflowRequest     mockCreateWorkflowRequest
		mockInitiateWorkflowPublisher mockInitiateWorkflowPublisher
		mockInitiateWorkflow          mockInitiateWorkflow
		mockValidateWorkflow          mockValidateWorkflow
		args                          args
		want                          *celestialPb.InitiateWorkflowResponse
		wantErr                       bool
	}{
		{
			name: "workflow for the given client req id already exists",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.InitiateWorkflowRequest{
					Version: workflowPb.Version_V0,
					Type:    workflowPb.Type_B2C_FUND_TRANSFER,
					ClientReqId: &celestialPb.ClientReqId{
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						Id:     "client_req_id",
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &celestialPb.WorkflowCreationRequestParams{
					Version: workflowPb.Version_V0,
					Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					Payload: nil,
					ClientReqId: &workflowPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
				want: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				wantWorkflowReq: nil,
				err:             epifierrors.ErrAlreadyExists,
			},
			want: &celestialPb.InitiateWorkflowResponse{
				Params: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				WorkflowRequestId: "new_workflow_request",
				Status:            rpc.StatusAlreadyExists(),
			},
			wantErr: false,
		},
		{
			name: "error in creating new workflow due to connection issues",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.InitiateWorkflowRequest{
					Version: workflowPb.Version_V0,
					Type:    workflowPb.Type_B2C_FUND_TRANSFER,
					ClientReqId: &celestialPb.ClientReqId{
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						Id:     "client_req_id",
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &celestialPb.WorkflowCreationRequestParams{
					Version: workflowPb.Version_V0,
					Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					Payload: nil,
					ClientReqId: &workflowPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
				want:            nil,
				wantWorkflowReq: nil,
				err:             epifierrors.ErrContextCanceled,
			},
			want: &celestialPb.InitiateWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "error in Validating workkflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.InitiateWorkflowRequest{
					Version: workflowPb.Version_V0,
					Type:    workflowPb.Type_B2C_FUND_TRANSFER,
					ClientReqId: &celestialPb.ClientReqId{
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						Id:     "client_req_id",
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &celestialPb.WorkflowCreationRequestParams{
					Version: workflowPb.Version_V0,
					Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					Payload: nil,
					ClientReqId: &workflowPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
				want: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				wantWorkflowReq: &celestialPb.WorkflowRequest{
					Id:      "new_workflow_request",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    errors.New("error"),
			},
			want: &celestialPb.InitiateWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "error in publishing the saved workflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.InitiateWorkflowRequest{
					Version: workflowPb.Version_V0,
					Type:    workflowPb.Type_B2C_FUND_TRANSFER,
					ClientReqId: &celestialPb.ClientReqId{
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						Id:     "client_req_id",
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &celestialPb.WorkflowCreationRequestParams{
					Version: workflowPb.Version_V0,
					Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					Payload: nil,
					ClientReqId: &workflowPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
				want: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				wantWorkflowReq: &celestialPb.WorkflowRequest{
					Id:      "new_workflow_request",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    nil,
			},
			mockInitiateWorkflowPublisher: mockInitiateWorkflowPublisher{
				enable: true,
				req: &celestialConsumerPb.InitiateWorkflowRequest{
					WorkflowReqId: "new_workflow_request",
					Ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
				},
				res: "",
				err: errors.New("error"),
			},
			want: &celestialPb.InitiateWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "initiated workflow successfully",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.InitiateWorkflowRequest{
					Version: workflowPb.Version_V0,
					Type:    workflowPb.Type_B2C_FUND_TRANSFER,
					ClientReqId: &celestialPb.ClientReqId{
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						Id:     "client_req_id",
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &celestialPb.WorkflowCreationRequestParams{
					Version: workflowPb.Version_V0,
					Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					Payload: nil,
					ClientReqId: &workflowPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
				want: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				wantWorkflowReq: &celestialPb.WorkflowRequest{
					Id:      "new_workflow_request",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    nil,
			},
			mockInitiateWorkflowPublisher: mockInitiateWorkflowPublisher{
				enable: true,
				req: &celestialConsumerPb.InitiateWorkflowRequest{
					WorkflowReqId: "new_workflow_request",
					Ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
				},
				res: "",
				err: nil,
			},
			want: &celestialPb.InitiateWorkflowResponse{
				Status: rpc.StatusOk(),
				Params: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				WorkflowRequestId: "new_workflow_request",
			},
			wantErr: false,
		},
		{
			name: "forward compatibility : initiated workflow successfully using new parameters",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.InitiateWorkflowRequest{
					Params: &celestialPb.WorkflowCreationRequestParams{
						Version: workflowPb.Version_V0,
						Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
						ClientReqId: &workflowPb.ClientReqId{
							Client: workflowPb.Client_US_STOCKS,
							Id:     "client_req_id",
						},
						Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					},
				},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &celestialPb.WorkflowCreationRequestParams{
					Version: workflowPb.Version_V0,
					Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					Payload: nil,
					ClientReqId: &workflowPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_US_STOCKS,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
				want: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				wantWorkflowReq: &celestialPb.WorkflowRequest{
					Id:      "new_workflow_request",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_US_STOCKS,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    nil,
			},
			mockInitiateWorkflowPublisher: mockInitiateWorkflowPublisher{
				enable: true,
				req: &celestialConsumerPb.InitiateWorkflowRequest{
					WorkflowReqId: "new_workflow_request",
					Ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
				},
				res: "",
				err: nil,
			},
			want: &celestialPb.InitiateWorkflowResponse{
				Status: rpc.StatusOk(),
				Params: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				WorkflowRequestId: "new_workflow_request",
			},
			wantErr: false,
		},
		{
			name: "failed validations due to missing workflow type",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.InitiateWorkflowRequest{
					Params: &celestialPb.WorkflowCreationRequestParams{
						Version: workflowPb.Version_V0,
						ClientReqId: &workflowPb.ClientReqId{
							Client: workflowPb.Client_US_STOCKS,
							Id:     "client_req_id",
						},
						Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					},
				},
			},
			want: &celestialPb.InitiateWorkflowResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "failed validations due to missing client req id",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.InitiateWorkflowRequest{
					Params: &celestialPb.WorkflowCreationRequestParams{
						Version:   workflowPb.Version_V0,
						Type:      celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
						Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					},
				},
			},
			want: &celestialPb.InitiateWorkflowResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "forward compatibility : initiated workflow successfully using new parameters in Best Effort Mode",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.InitiateWorkflowRequest{
					Params: &celestialPb.WorkflowCreationRequestParams{
						Version: workflowPb.Version_V0,
						Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
						ClientReqId: &workflowPb.ClientReqId{
							Client: workflowPb.Client_US_STOCKS,
							Id:     "client_req_id",
						},
						Ownership:        commontypes.Ownership_US_STOCKS_ALPACA,
						QualityOfService: celestialPb.QoS_BEST_EFFORT,
					},
				},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &celestialPb.WorkflowCreationRequestParams{
					Version: workflowPb.Version_V0,
					Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					Payload: nil,
					ClientReqId: &workflowPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_US_STOCKS,
					},
					QualityOfService: celestialPb.QoS_BEST_EFFORT,
					Ownership:        commontypes.Ownership_US_STOCKS_ALPACA,
				},
				want: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				wantWorkflowReq: &celestialPb.WorkflowRequest{
					Id:      "new_workflow_request",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_US_STOCKS,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockInitiateWorkflow: mockInitiateWorkflow{
				enable: true,
				workflowReq: &celestialPb.WorkflowRequest{
					Id:      "new_workflow_request",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_US_STOCKS,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
				err: nil,
			},
			want: &celestialPb.InitiateWorkflowResponse{
				Status: rpc.StatusOk(),
				Params: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				WorkflowRequestId: "new_workflow_request",
			},
			wantErr: false,
		},
		{
			name: "forward compatibility : initiated workflow successfully using new parameters in Guaranteed Mode",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.InitiateWorkflowRequest{
					Params: &celestialPb.WorkflowCreationRequestParams{
						Version: workflowPb.Version_V0,
						Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
						ClientReqId: &workflowPb.ClientReqId{
							Client: workflowPb.Client_US_STOCKS,
							Id:     "client_req_id",
						},
						Ownership:        commontypes.Ownership_US_STOCKS_ALPACA,
						QualityOfService: celestialPb.QoS_GUARANTEED,
					},
				},
			},
			mockCreateWorkflowRequest: mockCreateWorkflowRequest{
				enable: true,
				req: &celestialPb.WorkflowCreationRequestParams{
					Version: workflowPb.Version_V0,
					Type:    celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					Payload: nil,
					ClientReqId: &workflowPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_US_STOCKS,
					},
					Ownership:        commontypes.Ownership_US_STOCKS_ALPACA,
					QualityOfService: celestialPb.QoS_GUARANTEED,
				},
				want: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				wantWorkflowReq: &celestialPb.WorkflowRequest{
					Id:      "new_workflow_request",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_US_STOCKS,
					},
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    nil,
			},
			mockInitiateWorkflowPublisher: mockInitiateWorkflowPublisher{
				enable: true,
				req: &celestialConsumerPb.InitiateWorkflowRequest{
					WorkflowReqId: "new_workflow_request",
					Ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
				},
				res: "",
				err: nil,
			},
			want: &celestialPb.InitiateWorkflowResponse{
				Status: rpc.StatusOk(),
				Params: &celestialPb.WorkflowCreationResponseParams{
					WorkflowRequestId: "new_workflow_request",
				},
				WorkflowRequestId: "new_workflow_request",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			internal.ClearNamespaceClient()
			svc, md, assertMocks := newServerWithMocks(t)
			if tt.mockCreateWorkflowRequest.enable {
				md.celestialProcessor.EXPECT().CreateWorkflowRequest(gomock.Any(), tt.mockCreateWorkflowRequest.req).Return(
					tt.mockCreateWorkflowRequest.want, tt.mockCreateWorkflowRequest.wantWorkflowReq, tt.mockCreateWorkflowRequest.err)
			}
			if tt.mockInitiateWorkflow.enable {
				md.temporalProcessor.EXPECT().InitiateWorkflow(gomock.Any(), tt.mockInitiateWorkflow.workflowReq, commontypes.UseCase_USE_CASE_UNSPECIFIED).Return(
					tt.mockInitiateWorkflow.err)
			}
			if tt.mockValidateWorkflow.enable {
				md.temporalProcessor.EXPECT().ValidateWorkflow(gomock.Any(), gomock.Any()).Return(
					tt.mockValidateWorkflow.err)
			}
			if tt.mockInitiateWorkflowPublisher.enable {
				md.initiateWorkflowPublisher.EXPECT().PublishExtendedMessage(gomock.Any(), tt.mockInitiateWorkflowPublisher.req).Return(
					tt.mockInitiateWorkflowPublisher.res, tt.mockInitiateWorkflowPublisher.err)
			}

			got, err := svc.InitiateWorkflow(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("InitiateWorkflow() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestService_GetWorkflowStatus(t *testing.T) {
	type args struct {
		ctx context.Context
		req *celestialPb.GetWorkflowStatusRequest
	}

	type mockGetBatchWorkflowRequestDao struct {
		enable       bool
		clientReqIds []*workflowPb.ClientReqId
		want         []*celestialPb.WorkflowRequest
		err          error
		ownership    commontypes.Ownership
		useCase      commontypes.UseCase
	}
	tests := []struct {
		name                           string
		mockGetBatchWorkflowRequestDao mockGetBatchWorkflowRequestDao
		args                           args
		want                           *celestialPb.GetWorkflowStatusResponse
		wantErr                        bool
	}{
		{
			name: "successfully fetched workflow request status",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
				},
			},
			mockGetBatchWorkflowRequestDao: mockGetBatchWorkflowRequestDao{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:      "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Version: workflowPb.Version_V0,
						Type:    workflowPb.Type_B2C_FUND_TRANSFER,
						Stage:   workflowPb.Stage_PAYMENT,
						Status:  stagePb.Status_SUCCESSFUL,
					},
				},
				err: nil,
			},
			want: &celestialPb.GetWorkflowStatusResponse{
				Status: rpc.StatusOk(),
				WorkflowRequest: &celestialPb.WorkflowRequest{
					Id:      "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Version: workflowPb.Version_V0,
					Type:    workflowPb.Type_B2C_FUND_TRANSFER,
					Stage:   workflowPb.Stage_PAYMENT,
					Status:  stagePb.Status_SUCCESSFUL,
				},
			},
			wantErr: false,
		},
		{
			name: "workflow request not found for the given client req Id",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
				},
			},
			mockGetBatchWorkflowRequestDao: mockGetBatchWorkflowRequestDao{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: nil,
				err:  epifierrors.ErrRecordNotFound,
			},
			want: &celestialPb.GetWorkflowStatusResponse{
				Status:          rpc.StatusRecordNotFound(),
				WorkflowRequest: nil,
			},
			wantErr: false,
		},
		{
			name: "connection error while fetching record for client req Id",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.GetWorkflowStatusRequest{
					Identifier: &celestialPb.GetWorkflowStatusRequest_ClientRequestId{
						ClientRequestId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
				},
			},
			mockGetBatchWorkflowRequestDao: mockGetBatchWorkflowRequestDao{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: nil,
				err:  errors.New("error"),
			},
			want: &celestialPb.GetWorkflowStatusResponse{
				Status:          rpc.StatusInternal(),
				WorkflowRequest: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newServerWithMocks(t)
			if tt.mockGetBatchWorkflowRequestDao.enable {
				md.workflowRequestDao.EXPECT().BatchGetByClientReqId(gomock.Any(), tt.mockGetBatchWorkflowRequestDao.clientReqIds,
					tt.mockGetBatchWorkflowRequestDao.ownership, tt.mockGetBatchWorkflowRequestDao.useCase).Return(
					tt.mockGetBatchWorkflowRequestDao.want, tt.mockGetBatchWorkflowRequestDao.err)
			}
			got, err := svc.GetWorkflowStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWorkflowStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetWorkflowStatus() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestService_SignalWorkflow(t *testing.T) {
	type args struct {
		ctx context.Context
		req *celestialPb.SignalWorkflowRequest
	}

	type mockGetWorkflowRequestByClient struct {
		enable       bool
		clientReqIds []*workflowPb.ClientReqId
		want         []*celestialPb.WorkflowRequest
		err          error
		ownership    commontypes.Ownership
		useCase      commontypes.UseCase
	}
	type mockValidateWorkflow struct {
		enable bool
		err    error
	}
	type mockSignalWorkflowPublisher struct {
		enable bool
		req    *celestialConsumerPb.SignalWorkflowRequest
		res    string
		err    error
	}
	type mockGetWorkflowRequestByID struct {
		enable        bool
		workflowReqID string
		want          *celestialPb.WorkflowRequest
		err           error
		ownership     commontypes.Ownership
		useCase       commontypes.UseCase
	}

	type mockSignalWorkflow struct {
		enable          bool
		workflowRequest *celestialPb.WorkflowRequest
		signalId        epifitemporal.Signal
		err             error
	}

	tests := []struct {
		name                           string
		args                           args
		mockGetWorkflowRequestByClient mockGetWorkflowRequestByClient
		mockGetWorkflowRequestByID     mockGetWorkflowRequestByID
		mockValidateWorkflow           mockValidateWorkflow
		mockSignalWorkflowPublisher    mockSignalWorkflowPublisher
		mockSignalWorkflow             mockSignalWorkflow
		want                           *celestialPb.SignalWorkflowResponse
		wantErr                        bool
	}{
		{
			name: "failed - wrong client req id - workflowReq not found in DB",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "some_random_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "some_random_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: nil,
				err:  epifierrors.ErrRecordNotFound,
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failed - failed to fetch workflow req due to DB issue - clientReqID was given",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "some_random_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "some_random_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: nil,
				err:  errors.New("DB issue"),
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed - wrong workflow req id - workflowReq not found in DB",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "workflow_req_id",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want:          nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failed - failed to fetch workflowReq due to internal DB issue",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "workflow_req_id",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want:          nil,
				err:           errors.New("DB issue"),
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed to signal workflow - unknown identifier passed",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: nil,
				},
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "client req id - guaranteed mode - error in Validating workkflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					QualityOfService: celestialPb.QoS_GUARANTEED,
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:     "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:  workflowPb.Stage_PAYMENT,
						Status: stagePb.Status_SUCCESSFUL,
						Type:   workflowPb.Type_TYPE_UNSPECIFIED,
					},
				},
				err: nil,
			},
			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    errors.New("error"),
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "client req id - guaranteed mode - failed to publish signal workflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					SignalId:         "my-signal",
					QualityOfService: celestialPb.QoS_GUARANTEED,
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:     "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:  workflowPb.Stage_PAYMENT,
						Status: stagePb.Status_SUCCESSFUL,
						Type:   workflowPb.Type_TYPE_UNSPECIFIED,
					},
				},
				err: nil,
			},
			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    nil,
			},
			mockSignalWorkflowPublisher: mockSignalWorkflowPublisher{
				enable: true,
				req: &celestialConsumerPb.SignalWorkflowRequest{
					Identifier: &celestialConsumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					SignalId: "my-signal",
				},
				res: "",
				err: errors.New("error"),
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "client req id - guaranteed mode - successfully publish signal workflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					QualityOfService: celestialPb.QoS_GUARANTEED,
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:    workflowPb.Stage_PAYMENT,
						Status:   stagePb.Status_SUCCESSFUL,
						Type:     workflowPb.Type_B2C_FUND_TRANSFER,
						TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					},
				},
				err: nil,
			},
			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    nil,
			},
			mockSignalWorkflowPublisher: mockSignalWorkflowPublisher{
				enable: true,
				req: &celestialConsumerPb.SignalWorkflowRequest{
					Identifier: &celestialConsumerPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					SignalId: "my-signal",
				},
				res: "",
				err: nil,
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "client req id - best effort mode - failed to signal workflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:    workflowPb.Stage_PAYMENT,
						Status:   stagePb.Status_SUCCESSFUL,
						Type:     workflowPb.Type_B2C_FUND_TRANSFER,
						TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					},
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      epifierrors.ErrPermissionDenied,
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "client req id - best effort mode - signalled the workflow successfully",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:    workflowPb.Stage_PAYMENT,
						Status:   stagePb.Status_SUCCESSFUL,
						Type:     workflowPb.Type_B2C_FUND_TRANSFER,
						TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					},
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      nil,
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "client req id - best effort mode - signaled the workflow successfully for V1 workflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &celestialPb.SignalWorkflowRequest_ClientReqId{
						ClientReqId: &celestialPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
				},
			},
			mockGetWorkflowRequestByClient: mockGetWorkflowRequestByClient{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
						Stage:    workflowPb.Stage_PAYMENT,
						Status:   stagePb.Status_SUCCESSFUL,
						Type:     workflowPb.Type_B2C_FUND_TRANSFER,
						Version:  workflowPb.Version_V1,
						TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
					},
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					Version:  workflowPb.Version_V1,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      nil,
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "workflow req id - guaranteed mode - error in Validating workkflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
					QualityOfService: celestialPb.QoS_GUARANTEED,
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				err: nil,
			},
			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    errors.New("error"),
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "workflow req id - guaranteed mode - failed to publish signal workflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
					QualityOfService: celestialPb.QoS_GUARANTEED,
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				err: nil,
			},
			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    nil,
			},
			mockSignalWorkflowPublisher: mockSignalWorkflowPublisher{
				enable: true,
				req: &celestialConsumerPb.SignalWorkflowRequest{
					Identifier: &celestialConsumerPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
					SignalId: "my-signal",
				},
				res: "",
				err: errors.New("error"),
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "workflow req id - guaranteed mode - successfully publish signal workflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
					QualityOfService: celestialPb.QoS_GUARANTEED,
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				err: nil,
			},

			mockValidateWorkflow: mockValidateWorkflow{
				enable: true,
				err:    nil,
			},
			mockSignalWorkflowPublisher: mockSignalWorkflowPublisher{
				enable: true,
				req: &celestialConsumerPb.SignalWorkflowRequest{
					Identifier: &celestialConsumerPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
					SignalId: "my-signal",
				},
				res: "",
				err: nil,
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "workflow req id - best effort mode - failed to signal workflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      epifierrors.ErrPermissionDenied,
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "workflow req id - best effort mode - signalled the workflow successfully",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      nil,
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "workflow req id - best effort mode - signaled the workflow successfully for V1 workflow",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.SignalWorkflowRequest{
					SignalId: "my-signal",
					Payload:  nil,
					Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				want: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					Version:  workflowPb.Version_V1,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				err: nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					Version:  workflowPb.Version_V1,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				signalId: epifitemporal.Signal("my-signal"),
				err:      nil,
			},
			want: &celestialPb.SignalWorkflowResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newServerWithMocks(t)
			internal.ClearNamespaceClient()
			if tt.mockGetWorkflowRequestByClient.enable {
				md.workflowRequestDao.EXPECT().BatchGetByClientReqId(gomock.Any(), tt.mockGetWorkflowRequestByClient.clientReqIds,
					tt.mockGetWorkflowRequestByClient.ownership, tt.mockGetWorkflowRequestByClient.useCase).Return(
					tt.mockGetWorkflowRequestByClient.want, tt.mockGetWorkflowRequestByClient.err)
			}

			if tt.mockGetWorkflowRequestByID.enable {
				md.workflowRequestDao.EXPECT().GetByID(gomock.Any(), tt.mockGetWorkflowRequestByID.workflowReqID,
					tt.mockGetWorkflowRequestByID.ownership, tt.mockGetWorkflowRequestByID.useCase).Return(
					tt.mockGetWorkflowRequestByID.want, tt.mockGetWorkflowRequestByID.err)
			}

			if tt.mockValidateWorkflow.enable {
				md.temporalProcessor.EXPECT().ValidateWorkflow(gomock.Any(), gomock.Any()).Return(tt.mockValidateWorkflow.err)
			}

			if tt.mockSignalWorkflowPublisher.enable {
				md.signalWorkflowPublisher.EXPECT().PublishExtendedMessage(gomock.Any(), tt.mockSignalWorkflowPublisher.req).Return(
					tt.mockSignalWorkflowPublisher.res, tt.mockSignalWorkflowPublisher.err)
			}

			if tt.mockSignalWorkflow.enable {
				md.temporalProcessor.EXPECT().SignalWorkflow(gomock.Any(), tt.mockSignalWorkflow.workflowRequest,
					tt.mockSignalWorkflow.signalId, gomock.Any()).Return(tt.mockSignalWorkflow.err)
			}

			got, err := svc.SignalWorkflow(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("SignalWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("SignalWorkflow() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestService_GetWorkflowRequestsByFilters(t *testing.T) {
	type mockWorkflowRequestDao struct {
		enable bool
		want   []*celestialPb.WorkflowRequest
		err    error
	}

	workflowReq := &celestialPb.WorkflowRequest{
		Id:      "workflow_req_id",
		Status:  stagePb.Status_BLOCKED,
		Stage:   workflowPb.Stage_PAYMENT,
		Version: workflowPb.Version_V0,
		Type:    workflowPb.Type_BUY_US_STOCKS,
		ClientReqId: &celestialPb.ClientReqId{
			Client: workflowPb.Client_CLIENT_UNSPECIFIED,
			Id:     "client_req_id",
		},
	}
	type mockGetByWorkflowType struct {
		enable       bool
		workflowType *workflowPb.TypeEnum
		ownership    commontypes.Ownership
		filterOption []storagev2.FilterOption
		want         []*celestialPb.WorkflowRequest
		err          error
	}
	tests := []struct {
		name                  string
		mockGetByWorkflowType mockGetByWorkflowType
		req                   *celestialPb.GetWorkflowRequestsByFiltersRequest
		want                  *celestialPb.GetWorkflowRequestsByFiltersResponse
	}{
		{
			name: "success - getting by Filters",
			req: &celestialPb.GetWorkflowRequestsByFiltersRequest{
				Type:      workflowPb.Type_BUY_US_STOCKS,
				Stage:     workflowPb.Stage_PAYMENT,
				Status:    stagePb.Status_BLOCKED,
				ActorId:   "random-actor-id",
				Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
			},
			mockGetByWorkflowType: mockGetByWorkflowType{
				enable:       true,
				workflowType: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_BUY_US_STOCKS),
				ownership:    commontypes.Ownership_US_STOCKS_ALPACA,
				want:         []*celestialPb.WorkflowRequest{workflowReq},
				err:          nil,
			},
			want: &celestialPb.GetWorkflowRequestsByFiltersResponse{
				Status:    rpc.StatusOk(),
				WfReqList: []*celestialPb.WorkflowRequest{workflowReq},
			},
		},
		{
			name: "success - getting by Filters using type and stage enums",
			req: &celestialPb.GetWorkflowRequestsByFiltersRequest{
				Status:    stagePb.Status_BLOCKED,
				ActorId:   "random-actor-id",
				Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
				TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_BUY_US_STOCKS),
			},
			mockGetByWorkflowType: mockGetByWorkflowType{
				enable:       true,
				workflowType: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_BUY_US_STOCKS),
				ownership:    commontypes.Ownership_US_STOCKS_ALPACA,
				want:         []*celestialPb.WorkflowRequest{workflowReq},
				err:          nil,
			},
			want: &celestialPb.GetWorkflowRequestsByFiltersResponse{
				Status:    rpc.StatusOk(),
				WfReqList: []*celestialPb.WorkflowRequest{workflowReq},
			},
		},
		{
			name: "failure - due to invalid argument",
			req: &celestialPb.GetWorkflowRequestsByFiltersRequest{
				Type:      workflowPb.Type_BUY_US_STOCKS,
				Stage:     workflowPb.Stage_PAYMENT,
				Status:    stagePb.Status_BLOCKED,
				ActorId:   "random-actor-id",
				Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
			},
			mockGetByWorkflowType: mockGetByWorkflowType{
				enable:       true,
				workflowType: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_BUY_US_STOCKS),
				ownership:    commontypes.Ownership_US_STOCKS_ALPACA,
				err:          epifierrors.ErrInvalidArgument,
			},
			want: &celestialPb.GetWorkflowRequestsByFiltersResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "failure - getting by id due to connection issues",
			req: &celestialPb.GetWorkflowRequestsByFiltersRequest{
				Type:      workflowPb.Type_BUY_US_STOCKS,
				Stage:     workflowPb.Stage_PAYMENT,
				Status:    stagePb.Status_BLOCKED,
				ActorId:   "random-actor-id",
				Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
			},
			mockGetByWorkflowType: mockGetByWorkflowType{
				enable:       true,
				workflowType: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_BUY_US_STOCKS),
				ownership:    commontypes.Ownership_US_STOCKS_ALPACA,
				err:          errors.New("connection issues"),
			},
			want: &celestialPb.GetWorkflowRequestsByFiltersResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "success - getting by filters handle if deprecated fields are not populated",
			req: &celestialPb.GetWorkflowRequestsByFiltersRequest{
				Status:    stagePb.Status_BLOCKED,
				ActorId:   "random-actor-id",
				Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_BUY_US_STOCKS),
				StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
			},
			mockGetByWorkflowType: mockGetByWorkflowType{
				enable:       true,
				workflowType: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_BUY_US_STOCKS),
				ownership:    commontypes.Ownership_US_STOCKS_ALPACA,
				want:         []*celestialPb.WorkflowRequest{workflowReq},
				err:          nil,
			},
			want: &celestialPb.GetWorkflowRequestsByFiltersResponse{
				Status:    rpc.StatusOk(),
				WfReqList: []*celestialPb.WorkflowRequest{workflowReq},
			},
		},
		{
			name: "should return invalid argument if stage is not specified",
			req: &celestialPb.GetWorkflowRequestsByFiltersRequest{
				Status:    stagePb.Status_BLOCKED,
				ActorId:   "random-actor-id",
				Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_BUY_US_STOCKS),
			},
			want: &celestialPb.GetWorkflowRequestsByFiltersResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "should return invalid argument if type is not specified",
			req: &celestialPb.GetWorkflowRequestsByFiltersRequest{
				Status:    stagePb.Status_BLOCKED,
				ActorId:   "random-actor-id",
				Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_PAYMENT),
			},
			want: &celestialPb.GetWorkflowRequestsByFiltersResponse{
				Status: rpc.StatusInvalidArgument(),
			},
		},
		{
			name: "success - getting by filters + wf req id",
			req: &celestialPb.GetWorkflowRequestsByFiltersRequest{
				Status:    stagePb.Status_BLOCKED,
				ActorId:   "random-actor-id",
				Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				TypeEnum:  &workflowPb.TypeEnum{TypeValue: workflowPb.Type_BUY_US_STOCKS.String()},
				StageEnum: &workflowPb.StageEnum{StageValue: workflowPb.Stage_PAYMENT.String()},
				WfIds:     []string{"workflow_req_id"},
			},
			mockGetByWorkflowType: mockGetByWorkflowType{
				enable:       true,
				workflowType: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_BUY_US_STOCKS),
				ownership:    commontypes.Ownership_US_STOCKS_ALPACA,
				want:         []*celestialPb.WorkflowRequest{workflowReq},
				err:          nil,
			},
			want: &celestialPb.GetWorkflowRequestsByFiltersResponse{
				Status:    rpc.StatusOk(),
				WfReqList: []*celestialPb.WorkflowRequest{workflowReq},
			},
		},
		{
			name: "no record - getting by filters + incorrect wf req id",
			req: &celestialPb.GetWorkflowRequestsByFiltersRequest{
				Status:    stagePb.Status_BLOCKED,
				ActorId:   "random-actor-id",
				Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				WfIds:     []string{"workflow_req_id-incorrect"},
				TypeEnum:  &workflowPb.TypeEnum{TypeValue: workflowPb.Type_BUY_US_STOCKS.String()},
				StageEnum: &workflowPb.StageEnum{StageValue: workflowPb.Stage_PAYMENT.String()},
			},
			mockGetByWorkflowType: mockGetByWorkflowType{
				enable:       true,
				workflowType: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_BUY_US_STOCKS),
				ownership:    commontypes.Ownership_US_STOCKS_ALPACA,
				want:         nil,
				err:          nil,
			},
			want: &celestialPb.GetWorkflowRequestsByFiltersResponse{
				Status:    rpc.StatusOk(),
				WfReqList: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newServerWithMocks(t)
			if tt.mockGetByWorkflowType.enable {
				md.workflowRequestDao.EXPECT().
					GetByWorkflowType(gomock.Any(), testPkg.NewProtoArgMatcher(tt.mockGetByWorkflowType.workflowType), tt.mockGetByWorkflowType.ownership, nil, gomock.Any(), gomock.Any()).
					Return(tt.mockGetByWorkflowType.want, tt.mockGetByWorkflowType.err)
			}
			got, err := svc.GetWorkflowRequestsByFilters(context.Background(), tt.req)
			if err != nil {
				t.Errorf("GetWorkflowRequests() error = %v", err)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("GetWorkflowRequests() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestService_ResetWorkflowExecution(t *testing.T) {

	var (
		iterator = 0
	)

	type args struct {
		ctx context.Context
		req *celestialPb.ResetWorkflowExecutionRequest
	}

	type mockGetWorkflowRequestByID struct {
		enable        bool
		workflowReqID string
		want          *celestialPb.WorkflowRequest
		err           error
	}

	type mockGetClient struct {
		enable    bool
		namespace string
		want      *temporalMocks.Client
		err       error
	}

	type mockResetWorkflowExecution struct {
		enable        bool
		namespace     string
		workflowReqID string
		runId         string
		eventId       int64
		want          string
		err           error
	}

	type mockGetWorkflowHistory struct {
		enable bool
		want   *temporalMocks.HistoryEventIterator
		err    error
	}

	type mockHasNextEvent struct {
		enable bool
		err    error
	}

	type mockGetNextEvent struct {
		enable bool
		want   []*history.HistoryEvent
		err    error
	}

	tests := []struct {
		name                       string
		args                       args
		mockGetWorkflowRequestByID mockGetWorkflowRequestByID
		mockGetClient              mockGetClient
		mockResetWorkflowExecution mockResetWorkflowExecution
		mockGetWorkflowHistory     mockGetWorkflowHistory
		mockGetNextEvents          mockGetNextEvent
		mockHasNextEvent           mockHasNextEvent
		want                       *celestialPb.ResetWorkflowExecutionResponse
		wantErr                    bool
	}{
		{
			name: "workflow execution reset successfully",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.ResetWorkflowExecutionRequest{
					WorkflowReqId: "workflow_req_id",
					RunId:         "run_id",
					Identifier: &celestialPb.ResetWorkflowExecutionRequest_ActivityName{
						ActivityName: "activity-1",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				err: nil,
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockResetWorkflowExecution: mockResetWorkflowExecution{
				enable:        true,
				namespace:     "pay",
				workflowReqID: "workflow_req_id",
				runId:         "run_id",
				eventId:       1,
				want:          "new_run_id",
				err:           nil,
			},
			mockGetWorkflowHistory: mockGetWorkflowHistory{
				enable: true,
				want:   &temporalMocks.HistoryEventIterator{},
				err:    nil,
			},
			mockGetNextEvents: mockGetNextEvent{
				enable: true,
				want: []*history.HistoryEvent{
					{
						EventId:   1,
						EventType: temporalEnums.EVENT_TYPE_WORKFLOW_TASK_COMPLETED,
					},
					{
						EventId:   2,
						EventType: temporalEnums.EVENT_TYPE_ACTIVITY_TASK_SCHEDULED,
						Attributes: &history.HistoryEvent_ActivityTaskScheduledEventAttributes{
							ActivityTaskScheduledEventAttributes: &history.ActivityTaskScheduledEventAttributes{
								ActivityType: &common.ActivityType{
									Name: "activity-1",
								},
							},
						},
					},
				},
				err: nil,
			},
			mockHasNextEvent: mockHasNextEvent{
				enable: true,
				err:    nil,
			},
			want: &celestialPb.ResetWorkflowExecutionResponse{
				RunId:  "new_run_id",
				Status: rpc.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failed to reset workflow, workflow req not found",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.ResetWorkflowExecutionRequest{
					WorkflowReqId: "workflow_req_id",
					RunId:         "run_id",
					Identifier: &celestialPb.ResetWorkflowExecutionRequest_ActivityName{
						ActivityName: "activity-1",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want:          nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			want: &celestialPb.ResetWorkflowExecutionResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failed to reset workflow because of some db issue",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.ResetWorkflowExecutionRequest{
					WorkflowReqId: "workflow_req_id",
					RunId:         "run_id",
					Identifier: &celestialPb.ResetWorkflowExecutionRequest_ActivityName{
						ActivityName: "activity-1",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want:          nil,
				err:           errors.New("db issue"),
			},
			want: &celestialPb.ResetWorkflowExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed to reset workflow - wrong workflow type - namespace fetch failed",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.ResetWorkflowExecutionRequest{
					WorkflowReqId: "workflow_req_id",
					RunId:         "run_id",
					Identifier: &celestialPb.ResetWorkflowExecutionRequest_ActivityName{
						ActivityName: "activity-1",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want: &celestialPb.WorkflowRequest{
					Id:     "workflow_req_id",
					Stage:  workflowPb.Stage_PAYMENT,
					Status: stagePb.Status_SUCCESSFUL,
					Type:   workflowPb.Type_TYPE_UNSPECIFIED,
				},
				err: nil,
			},
			want: &celestialPb.ResetWorkflowExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed to reset workflow - wrong namespace - temporal client fetch failed",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.ResetWorkflowExecutionRequest{
					WorkflowReqId: "workflow_req_id",
					RunId:         "run_id",
					Identifier: &celestialPb.ResetWorkflowExecutionRequest_ActivityName{
						ActivityName: "activity-1",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				err: nil,
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      nil,
				err:       epifierrors.ErrInvalidArgument,
			},
			want: &celestialPb.ResetWorkflowExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed to reset workflow due to some sdk issue",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.ResetWorkflowExecutionRequest{
					WorkflowReqId: "workflow_req_id",
					RunId:         "run_id",
					Identifier: &celestialPb.ResetWorkflowExecutionRequest_ActivityName{
						ActivityName: "activity-1",
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				err: nil,
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockGetWorkflowHistory: mockGetWorkflowHistory{
				enable: true,
				want:   &temporalMocks.HistoryEventIterator{},
				err:    nil,
			},
			mockHasNextEvent: mockHasNextEvent{
				enable: true,
				err:    nil,
			},
			mockGetNextEvents: mockGetNextEvent{
				enable: true,
				want: []*history.HistoryEvent{
					{
						EventId:   1,
						EventType: temporalEnums.EVENT_TYPE_WORKFLOW_TASK_COMPLETED,
					},
					{
						EventId:   2,
						EventType: temporalEnums.EVENT_TYPE_ACTIVITY_TASK_SCHEDULED,
						Attributes: &history.HistoryEvent_ActivityTaskScheduledEventAttributes{
							ActivityTaskScheduledEventAttributes: &history.ActivityTaskScheduledEventAttributes{
								ActivityType: &common.ActivityType{
									Name: "activity-1",
								},
							},
						},
					},
				},
				err: errors.New("sdk issue"),
			},
			want: &celestialPb.ResetWorkflowExecutionResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "failed to reset workflow due to invalid event ID",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.ResetWorkflowExecutionRequest{
					WorkflowReqId: "workflow_req_id",
					RunId:         "run_id",
					Identifier: &celestialPb.ResetWorkflowExecutionRequest_EventId{
						EventId: 0,
					},
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				err: nil,
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockGetWorkflowHistory: mockGetWorkflowHistory{
				enable: true,
				want:   &temporalMocks.HistoryEventIterator{},
				err:    nil,
			},
			mockHasNextEvent: mockHasNextEvent{
				enable: true,
				err:    nil,
			},
			mockGetNextEvents: mockGetNextEvent{
				enable: true,
				want: []*history.HistoryEvent{
					{
						EventId:   1,
						EventType: temporalEnums.EVENT_TYPE_WORKFLOW_TASK_COMPLETED,
					},
					{
						EventId:   2,
						EventType: temporalEnums.EVENT_TYPE_ACTIVITY_TASK_SCHEDULED,
						Attributes: &history.HistoryEvent_ActivityTaskScheduledEventAttributes{
							ActivityTaskScheduledEventAttributes: &history.ActivityTaskScheduledEventAttributes{
								ActivityType: &common.ActivityType{
									Name: "activity-1",
								},
							},
						},
					},
				},
				err: nil,
			},
			want: &celestialPb.ResetWorkflowExecutionResponse{
				Status: rpc.StatusInvalidArgument(),
				EventDetails: []*celestialPb.EventDetail{
					{
						ActivityName: "activity-1",
						EventId:      2,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newServerWithMocks(t)
			internal.ClearNamespaceClient()

			if tt.mockGetWorkflowRequestByID.enable {
				md.workflowRequestDao.EXPECT().GetByID(gomock.Any(), tt.mockGetWorkflowRequestByID.workflowReqID, gomock.Any(), gomock.Any()).Return(
					tt.mockGetWorkflowRequestByID.want, tt.mockGetWorkflowRequestByID.err)
			}

			if tt.mockGetClient.enable {
				md.clientFactory.EXPECT().
					NewWorkflowClient(tt.mockGetClient.namespace, false, gomock.Any()).
					Return(tt.mockGetClient.want, tt.mockGetClient.err)

				if tt.mockGetClient.err == nil && tt.mockGetWorkflowHistory.enable {
					tt.mockGetClient.want.On("GetWorkflowHistory", mock.Anything, mock.Anything,
						mock.Anything, mock.Anything, mock.Anything).Return(tt.mockGetWorkflowHistory.want)

					if tt.mockHasNextEvent.enable {
						tt.mockGetWorkflowHistory.want.On("HasNext").Return(func() bool {
							if iterator == 2 {
								iterator = 0
								return false
							}
							if tt.mockGetNextEvents.enable {
								tt.mockGetWorkflowHistory.want.On("Next").Return(tt.mockGetNextEvents.want[iterator], tt.mockGetNextEvents.err).Times(1)
							}
							iterator += 1
							return true
						})
					}
				}

				if tt.mockGetClient.err == nil && tt.mockResetWorkflowExecution.enable {
					tt.mockGetClient.want.On("ResetWorkflowExecution", tt.args.ctx, &workflowservice.ResetWorkflowExecutionRequest{
						Namespace: tt.mockResetWorkflowExecution.namespace,
						WorkflowExecution: &common.WorkflowExecution{
							WorkflowId: tt.mockResetWorkflowExecution.workflowReqID,
							RunId:      tt.mockResetWorkflowExecution.runId,
						},
						WorkflowTaskFinishEventId: tt.mockResetWorkflowExecution.eventId,
					}).Return(&workflowservice.ResetWorkflowExecutionResponse{RunId: tt.mockResetWorkflowExecution.want}, tt.mockResetWorkflowExecution.err)
				}
			}

			got, err := svc.ResetWorkflowExecution(tt.args.ctx, tt.args.req)

			if tt.mockGetClient.enable && tt.mockGetClient.err == nil {
				tt.mockGetClient.want.AssertExpectations(t)
			}

			if (err != nil) != tt.wantErr {
				t.Errorf("ResetWorkflowExecution() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("ResetWorkflowExecution() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestService_ReinitiateWorkflow(t *testing.T) {
	type args struct {
		ctx context.Context
		req *celestialPb.ReinitiateWorkflowRequest
	}

	type mockGetWorkflowRequestByID struct {
		enable        bool
		workflowReqID string
		want          *celestialPb.WorkflowRequest
		err           error
	}

	type mockGetWorkflowRequestBatchByClientReqId struct {
		enable      bool
		clientReqID *workflowPb.ClientReqId
		want        []*celestialPb.WorkflowRequest
		err         error
	}

	type mockGetClient struct {
		enable    bool
		namespace string
		want      *temporalMocks.Client
		err       error
	}

	type mockListWorkflowExecution struct {
		enable bool
		want   *workflowservice.ListWorkflowExecutionsResponse
		err    error
	}

	type mockTerminalWorkflow struct {
		enable bool
		err    error
	}

	type mockWorkflowServiceClient struct {
		enable                    bool
		mockListWorkflowExecution mockListWorkflowExecution
		mockTerminalWorkflow      mockTerminalWorkflow
	}

	type mockWorkflowRequestUpdate struct {
		enable bool
		err    error
	}

	type mockArchiveWorkflowHistory struct {
		enable bool
		err    error
	}

	type mockTemporalProcReinitiateWorkflow struct {
		enable bool
		err    error
	}

	var rwr_wf_id = &celestialPb.ReinitiateWorkflowRequest{
		Identifier: &celestialPb.ReinitiateWorkflowRequest_WorkflowId{WorkflowId: "workflow_id"},
		Ownership:  commontypes.Ownership_EPIFI_TECH,
		Reason:     "some-reason",
	}

	var rwr_client_req_id = &celestialPb.ReinitiateWorkflowRequest{
		Identifier: &celestialPb.ReinitiateWorkflowRequest_ClientReqId{
			ClientReqId: &workflowPb.ClientReqId{
				Client: workflowPb.Client_PAY,
				Id:     "random-id",
			},
		},
		Ownership: commontypes.Ownership_EPIFI_TECH,
		Reason:    "some-reason",
	}

	var wfr_1 = &celestialPb.WorkflowRequest{
		Id:       "workflow_id",
		Stage:    workflowPb.Stage_PAYMENT,
		Status:   stagePb.Status_SUCCESSFUL,
		TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
	}

	tests := []struct {
		name                                     string
		args                                     args
		mockGetWorkflowRequestByID               mockGetWorkflowRequestByID
		mockGetWorkflowRequestBatchByClientReqId mockGetWorkflowRequestBatchByClientReqId
		mockGetClient                            mockGetClient
		mockWorkflowServiceClient                mockWorkflowServiceClient
		mockWorkflowRequestUpdate                mockWorkflowRequestUpdate
		mockArchiveWorkflowHistory               mockArchiveWorkflowHistory
		mockTemporalProcReinitiateWorkflow       mockTemporalProcReinitiateWorkflow
		want                                     *celestialPb.ReinitiateWorkflowResponse
		wantErr                                  bool
	}{
		{
			name: "successfully reinitiate workflow using workflow_id",
			args: args{
				ctx: context.Background(),
				req: rwr_wf_id,
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				err:           nil,
				want:          wfr_1,
				workflowReqID: "workflow_id",
			},
			mockGetClient: mockGetClient{
				enable:    true,
				want:      &temporalMocks.Client{},
				err:       nil,
				namespace: "pay",
			},
			mockWorkflowServiceClient: mockWorkflowServiceClient{
				enable: true,
				mockListWorkflowExecution: mockListWorkflowExecution{
					enable: true,
					err:    nil,
					want:   &workflowservice.ListWorkflowExecutionsResponse{},
				},
			},
			mockWorkflowRequestUpdate: mockWorkflowRequestUpdate{
				err:    nil,
				enable: true,
			},
			mockArchiveWorkflowHistory: mockArchiveWorkflowHistory{
				enable: true,
				err:    nil,
			},
			mockTemporalProcReinitiateWorkflow: mockTemporalProcReinitiateWorkflow{
				enable: true,
				err:    nil,
			},
			wantErr: false,
			want: &celestialPb.ReinitiateWorkflowResponse{
				Status: rpcPb.StatusOk(),
			},
		},
		{
			name: "successfully reinitiate workflow using client_req_id",
			args: args{
				ctx: context.Background(),
				req: rwr_client_req_id,
			},
			mockGetWorkflowRequestBatchByClientReqId: mockGetWorkflowRequestBatchByClientReqId{
				enable: true,
				err:    nil,
				want:   []*celestialPb.WorkflowRequest{wfr_1},
				clientReqID: &workflowPb.ClientReqId{
					Client: workflowPb.Client_PAY,
					Id:     "random-id",
				},
			},
			mockGetClient: mockGetClient{
				enable:    true,
				want:      &temporalMocks.Client{},
				err:       nil,
				namespace: "pay",
			},
			mockWorkflowServiceClient: mockWorkflowServiceClient{
				enable: true,
				mockListWorkflowExecution: mockListWorkflowExecution{
					enable: true,
					err:    nil,
					want:   &workflowservice.ListWorkflowExecutionsResponse{},
				},
			},
			mockWorkflowRequestUpdate: mockWorkflowRequestUpdate{
				err:    nil,
				enable: true,
			},
			mockArchiveWorkflowHistory: mockArchiveWorkflowHistory{
				enable: true,
				err:    nil,
			},
			mockTemporalProcReinitiateWorkflow: mockTemporalProcReinitiateWorkflow{
				enable: true,
				err:    nil,
			},
			wantErr: false,
			want: &celestialPb.ReinitiateWorkflowResponse{
				Status: rpcPb.StatusOk(),
			},
		},
		{
			name: "close existing running workflow and reinitiate",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.ReinitiateWorkflowRequest{
					Identifier:                      &celestialPb.ReinitiateWorkflowRequest_WorkflowId{WorkflowId: "workflow_id"},
					Ownership:                       commontypes.Ownership_EPIFI_TECH,
					Reason:                          "some-reason",
					ShouldForceCloseRunningWorkflow: true,
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				err:           nil,
				want:          wfr_1,
				workflowReqID: "workflow_id",
			},
			mockGetClient: mockGetClient{
				enable:    true,
				want:      &temporalMocks.Client{},
				err:       nil,
				namespace: "pay",
			},
			mockWorkflowServiceClient: mockWorkflowServiceClient{
				enable: true,
				mockListWorkflowExecution: mockListWorkflowExecution{
					enable: true,
					err:    nil,
					want: &workflowservice.ListWorkflowExecutionsResponse{
						Executions: []*workflow.WorkflowExecutionInfo{
							{
								Execution: &common.WorkflowExecution{
									WorkflowId: "workflow_id",
									RunId:      "run_id",
								},
							},
						},
					},
				},
				mockTerminalWorkflow: mockTerminalWorkflow{
					enable: true,
					err:    nil,
				},
			},
			mockWorkflowRequestUpdate: mockWorkflowRequestUpdate{
				err:    nil,
				enable: true,
			},
			mockArchiveWorkflowHistory: mockArchiveWorkflowHistory{
				enable: true,
				err:    nil,
			},
			mockTemporalProcReinitiateWorkflow: mockTemporalProcReinitiateWorkflow{
				enable: true,
				err:    nil,
			},
			want: &celestialPb.ReinitiateWorkflowResponse{
				Status: rpcPb.StatusOk(),
			},
			wantErr: false,
		},
		{
			name: "failed due to missing flag to close existing running workflow",
			args: args{
				ctx: context.Background(),
				req: rwr_wf_id,
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				err:           nil,
				want:          wfr_1,
				workflowReqID: "workflow_id",
			},
			mockGetClient: mockGetClient{
				enable:    true,
				want:      &temporalMocks.Client{},
				err:       nil,
				namespace: "pay",
			},
			mockWorkflowServiceClient: mockWorkflowServiceClient{
				enable: true,
				mockListWorkflowExecution: mockListWorkflowExecution{
					enable: true,
					err:    nil,
					want: &workflowservice.ListWorkflowExecutionsResponse{
						Executions: []*workflow.WorkflowExecutionInfo{
							{
								Execution: &common.WorkflowExecution{
									WorkflowId: "workflow_id",
									RunId:      "run_id",
								},
							},
						},
					},
				},
			},
			wantErr: false,
			want: &celestialPb.ReinitiateWorkflowResponse{
				Status: rpcPb.StatusFailedPrecondition(),
			},
		},
		{
			name: "fail on non-existing wf id",
			args: args{
				ctx: context.Background(),
				req: rwr_wf_id,
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				err:           epifierrors.ErrRecordNotFound,
				want:          nil,
				workflowReqID: rwr_wf_id.GetWorkflowId(),
			},
			want: &celestialPb.ReinitiateWorkflowResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name: "fail on non-existing client req id",
			args: args{
				ctx: context.Background(),
				req: rwr_client_req_id,
			},
			mockGetWorkflowRequestBatchByClientReqId: mockGetWorkflowRequestBatchByClientReqId{
				enable:      true,
				err:         epifierrors.ErrRecordNotFound,
				want:        nil,
				clientReqID: rwr_client_req_id.GetClientReqId(),
			},
			want: &celestialPb.ReinitiateWorkflowResponse{
				Status: rpcPb.StatusRecordNotFound(),
			},
		},
		{
			name: "fail due to some internal db error",
			args: args{
				ctx: context.Background(),
				req: rwr_client_req_id,
			},
			mockGetWorkflowRequestBatchByClientReqId: mockGetWorkflowRequestBatchByClientReqId{
				enable:      true,
				err:         epifierrors.ErrPermissionDenied,
				want:        nil,
				clientReqID: rwr_client_req_id.GetClientReqId(),
			},
			want: &celestialPb.ReinitiateWorkflowResponse{
				Status: rpcPb.StatusInternal(),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newServerWithMocks(t)
			internal.ClearNamespaceClient()

			if tt.mockGetWorkflowRequestByID.enable {
				md.workflowRequestDao.EXPECT().GetByID(gomock.Any(), tt.mockGetWorkflowRequestByID.workflowReqID, gomock.Any(), gomock.Any()).Return(
					tt.mockGetWorkflowRequestByID.want, tt.mockGetWorkflowRequestByID.err)
			}
			if tt.mockGetWorkflowRequestBatchByClientReqId.enable {
				md.workflowRequestDao.EXPECT().BatchGetByClientReqId(gomock.Any(), []*workflowPb.ClientReqId{tt.mockGetWorkflowRequestBatchByClientReqId.clientReqID},
					gomock.Any(), gomock.Any()).Return(tt.mockGetWorkflowRequestBatchByClientReqId.want, tt.mockGetWorkflowRequestBatchByClientReqId.err)
			}
			if tt.mockGetClient.enable {
				md.clientFactory.EXPECT().NewWorkflowClient(tt.mockGetClient.namespace, false, gomock.Any()).
					Return(tt.mockGetClient.want, tt.mockGetClient.err)

				if tt.mockGetClient.err == nil && tt.mockWorkflowServiceClient.enable {
					tt.mockGetClient.want.On("WorkflowService").Return(md.workflowServiceClient)

					if tt.mockWorkflowServiceClient.mockListWorkflowExecution.enable {
						md.workflowServiceClient.EXPECT().ListWorkflowExecutions(gomock.Any(), gomock.Any()).
							Return(tt.mockWorkflowServiceClient.mockListWorkflowExecution.want, tt.mockWorkflowServiceClient.mockListWorkflowExecution.err)
					}

					if tt.mockWorkflowServiceClient.mockTerminalWorkflow.enable {
						md.workflowServiceClient.EXPECT().TerminateWorkflowExecution(gomock.Any(), gomock.Any()).
							Return(&workflowservice.TerminateWorkflowExecutionResponse{}, tt.mockWorkflowServiceClient.mockTerminalWorkflow.err)
					}
				}
			}

			if tt.mockWorkflowRequestUpdate.enable {
				md.workflowRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), []celestialPb.WorkflowRequestFieldMask{
					celestialPb.WorkflowRequestFieldMask_WORKFLOW_REQUEST_FIELD_MASK_STAGE,
					celestialPb.WorkflowRequestFieldMask_WORKFLOW_REQUEST_FIELD_MASK_STATUS,
				}, tt.args.req.GetOwnership(), tt.args.req.GetUseCase()).Return(tt.mockWorkflowRequestUpdate.err)
			}

			if tt.mockArchiveWorkflowHistory.enable {
				md.workflowHistoryDao.EXPECT().ArchiveWorkflowReqIdHistory(gomock.Any(), gomock.Any(), tt.args.req.GetOwnership(), tt.args.req.GetUseCase()).
					Return(tt.mockArchiveWorkflowHistory.err)
			}

			if tt.mockTemporalProcReinitiateWorkflow.enable {
				md.temporalProcessor.EXPECT().ReinitiateWorkflow(gomock.Any(), gomock.Any(), commontypes.UseCase_USE_CASE_UNSPECIFIED).
					Return(tt.mockTemporalProcReinitiateWorkflow.err)
			}

			got, err := svc.ReinitiateWorkflow(tt.args.ctx, tt.args.req)

			if (err != nil) != tt.wantErr {
				t.Errorf("ReinitiateWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !proto.Equal(got, tt.want) {
				t.Errorf("ReinitiateWorkflow() got = %v, want %v", got, tt.want)
			}

			if tt.mockGetClient.enable && tt.mockGetClient.err == nil {
				tt.mockGetClient.want.AssertExpectations(t)
			}

			assertMocks()

		})
	}
}

func TestService_QueryWorkflow(t *testing.T) {
	type args struct {
		ctx context.Context
		req *celestialPb.QueryWorkflowRequest
	}

	type mockGetWorkflowRequestBatchByClientReqId struct {
		enable       bool
		clientReqIds []*workflowPb.ClientReqId
		want         []*celestialPb.WorkflowRequest
		err          error
		ownership    commontypes.Ownership
		useCase      commontypes.UseCase
	}

	type mockGetWorkflowRequestByID struct {
		enable        bool
		workflowReqID string
		want          *celestialPb.WorkflowRequest
		err           error
		ownership     commontypes.Ownership
		useCase       commontypes.UseCase
	}

	type mockQueryWorkflow struct {
		enable      bool
		workflowReq *celestialPb.WorkflowRequest
		queryType   string
		queryArgs   []byte
		want        []byte
		err         error
	}

	tests := []struct {
		name                                     string
		args                                     args
		mockGetWorkflowRequestBatchByClientReqId mockGetWorkflowRequestBatchByClientReqId
		mockGetWorkflowRequestByID               mockGetWorkflowRequestByID
		mockQueryWorkflow                        mockQueryWorkflow
		want                                     *celestialPb.QueryWorkflowResponse
		wantErr                                  bool
	}{
		{
			name: "successfully queried workflow using client request id",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.QueryWorkflowRequest{
					Identifier: &celestialPb.QueryWorkflowRequest_ClientReqId{
						ClientReqId: &workflowPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					QueryType: "query_type",
					QueryArgs: []byte("query_args"),
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockGetWorkflowRequestBatchByClientReqId: mockGetWorkflowRequestBatchByClientReqId{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:       "workflow_req_id",
						Stage:    workflowPb.Stage_PAYMENT,
						Status:   stagePb.Status_SUCCESSFUL,
						TypeEnum: &workflowPb.TypeEnum{},
					},
				},
				err:       nil,
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				useCase:   commontypes.UseCase(0),
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable: true,
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					TypeEnum: &workflowPb.TypeEnum{},
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
				want:      []byte("query_result"),
				err:       nil,
			},
			want: &celestialPb.QueryWorkflowResponse{
				Status:      rpc.StatusOk(),
				QueryResult: []byte("query_result"),
			},
			wantErr: false,
		},
		{
			name: "successfully queried workflow using workflow request id",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.QueryWorkflowRequest{
					Identifier: &celestialPb.QueryWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "workflow_req_id",
					},
					QueryType: "query_type",
					QueryArgs: []byte("query_args"),
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					TypeEnum: &workflowPb.TypeEnum{},
				},
				err:       nil,
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				useCase:   commontypes.UseCase(0),
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable: true,
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					TypeEnum: &workflowPb.TypeEnum{},
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
				want:      []byte("query_result"),
				err:       nil,
			},
			want: &celestialPb.QueryWorkflowResponse{
				Status:      rpc.StatusOk(),
				QueryResult: []byte("query_result"),
			},
			wantErr: false,
		},
		{
			name: "failed to query workflow as client request id is not found",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.QueryWorkflowRequest{
					Identifier: &celestialPb.QueryWorkflowRequest_ClientReqId{
						ClientReqId: &workflowPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					QueryType: "query_type",
					QueryArgs: []byte("query_args"),
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockGetWorkflowRequestBatchByClientReqId: mockGetWorkflowRequestBatchByClientReqId{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want:      nil,
				err:       epifierrors.ErrRecordNotFound,
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				useCase:   commontypes.UseCase(0),
			},
			want: &celestialPb.QueryWorkflowResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failed to query workflow as workflow request id is not found",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.QueryWorkflowRequest{
					Identifier: &celestialPb.QueryWorkflowRequest_WorkflowReqId{
						WorkflowReqId: "workflow_req_id",
					},
					QueryType: "query_type",
					QueryArgs: []byte("query_args"),
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockGetWorkflowRequestByID: mockGetWorkflowRequestByID{
				enable:        true,
				workflowReqID: "workflow_req_id",
				want:          nil,
				err:           epifierrors.ErrRecordNotFound,
				ownership:     commontypes.Ownership_US_STOCKS_ALPACA,
				useCase:       commontypes.UseCase(0),
			},
			want: &celestialPb.QueryWorkflowResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failed to query workflow due to invalid query type",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.QueryWorkflowRequest{
					Identifier: &celestialPb.QueryWorkflowRequest_ClientReqId{
						ClientReqId: &workflowPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					QueryType: "invalid_query_type",
					QueryArgs: []byte("query_args"),
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockGetWorkflowRequestBatchByClientReqId: mockGetWorkflowRequestBatchByClientReqId{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:       "workflow_req_id",
						Stage:    workflowPb.Stage_PAYMENT,
						Status:   stagePb.Status_SUCCESSFUL,
						TypeEnum: &workflowPb.TypeEnum{},
					},
				},
				err:       nil,
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				useCase:   commontypes.UseCase(0),
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable: true,
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					TypeEnum: &workflowPb.TypeEnum{},
				},
				queryType: "invalid_query_type",
				queryArgs: []byte("query_args"),
				want:      nil,
				err:       epifierrors.ErrInvalidArgument,
			},
			want: &celestialPb.QueryWorkflowResponse{
				Status: rpc.StatusInvalidArgument(),
			},
			wantErr: false,
		},
		{
			name: "failed to query workflow due to workflow not found",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.QueryWorkflowRequest{
					Identifier: &celestialPb.QueryWorkflowRequest_ClientReqId{
						ClientReqId: &workflowPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					QueryType: "query_type",
					QueryArgs: []byte("query_args"),
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockGetWorkflowRequestBatchByClientReqId: mockGetWorkflowRequestBatchByClientReqId{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:       "workflow_req_id",
						Stage:    workflowPb.Stage_PAYMENT,
						Status:   stagePb.Status_SUCCESSFUL,
						TypeEnum: &workflowPb.TypeEnum{},
					},
				},
				err:       nil,
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				useCase:   commontypes.UseCase(0),
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable: true,
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					TypeEnum: &workflowPb.TypeEnum{},
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
				want:      nil,
				err:       epifierrors.ErrRecordNotFound,
			},
			want: &celestialPb.QueryWorkflowResponse{
				Status: rpc.StatusRecordNotFound(),
			},
			wantErr: false,
		},
		{
			name: "failed to query workflow due to internal error",
			args: args{
				ctx: context.Background(),
				req: &celestialPb.QueryWorkflowRequest{
					Identifier: &celestialPb.QueryWorkflowRequest_ClientReqId{
						ClientReqId: &workflowPb.ClientReqId{
							Id:     "client_req_id",
							Client: workflowPb.Client_CLIENT_UNSPECIFIED,
						},
					},
					QueryType: "query_type",
					QueryArgs: []byte("query_args"),
					Ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				},
			},
			mockGetWorkflowRequestBatchByClientReqId: mockGetWorkflowRequestBatchByClientReqId{
				enable: true,
				clientReqIds: []*workflowPb.ClientReqId{
					{
						Id:     "client_req_id",
						Client: workflowPb.Client_CLIENT_UNSPECIFIED,
					},
				},
				want: []*celestialPb.WorkflowRequest{
					{
						Id:       "workflow_req_id",
						Stage:    workflowPb.Stage_PAYMENT,
						Status:   stagePb.Status_SUCCESSFUL,
						TypeEnum: &workflowPb.TypeEnum{},
					},
				},
				err:       nil,
				ownership: commontypes.Ownership_US_STOCKS_ALPACA,
				useCase:   commontypes.UseCase(0),
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable: true,
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					TypeEnum: &workflowPb.TypeEnum{},
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
				want:      nil,
				err:       errors.New("internal error"),
			},
			want: &celestialPb.QueryWorkflowResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md, teardown := newServerWithMocks(t)
			defer teardown()

			if tt.mockGetWorkflowRequestBatchByClientReqId.enable {
				md.workflowRequestDao.EXPECT().
					BatchGetByClientReqId(gomock.Any(), tt.mockGetWorkflowRequestBatchByClientReqId.clientReqIds, tt.mockGetWorkflowRequestBatchByClientReqId.ownership, tt.mockGetWorkflowRequestBatchByClientReqId.useCase).
					Return(tt.mockGetWorkflowRequestBatchByClientReqId.want, tt.mockGetWorkflowRequestBatchByClientReqId.err)
			}

			if tt.mockGetWorkflowRequestByID.enable {
				md.workflowRequestDao.EXPECT().
					GetByID(gomock.Any(), tt.mockGetWorkflowRequestByID.workflowReqID, tt.mockGetWorkflowRequestByID.ownership, tt.mockGetWorkflowRequestByID.useCase).
					Return(tt.mockGetWorkflowRequestByID.want, tt.mockGetWorkflowRequestByID.err)
			}

			if tt.mockQueryWorkflow.enable {
				md.temporalProcessor.EXPECT().
					QueryWorkflow(gomock.Any(), tt.mockQueryWorkflow.workflowReq, tt.mockQueryWorkflow.queryType, tt.mockQueryWorkflow.queryArgs).
					Return(tt.mockQueryWorkflow.want, tt.mockQueryWorkflow.err)
			}

			got, err := svc.QueryWorkflow(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("Service.QueryWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !proto.Equal(got, tt.want) {
				t.Errorf("Service.QueryWorkflow() = %v, want %v", got, tt.want)
			}
		})
	}
}
