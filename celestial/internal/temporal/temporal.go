package temporal

import (
	"context"
	"fmt"

	"github.com/google/wire"
	"github.com/pkg/errors"
	temporalEnumsPb "go.temporal.io/api/enums/v1"
	"go.temporal.io/api/serviceerror"
	"go.temporal.io/sdk/client"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	pkgcmdtypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	"github.com/epifi/be-common/pkg/epifitemporal/router"
	"github.com/epifi/gamma/celestial/dao"
	"github.com/epifi/gamma/celestial/internal"
	logHelper "github.com/epifi/gamma/celestial/internal/log"
)

// build time check to ensure processor implements the temporal processor interface
var _ internal.TemporalProcessor = &Processor{}

var WireSet = wire.NewSet(
	NewTemporalProcessor, wire.Bind(new(internal.TemporalProcessor), new(*Processor)),
)

type Processor struct {
	workflowRequestDao dao.WorkflowRequestDao
	clientFactory      epifitemporal.ClientFactory
	codecAesKey        pkgcmdtypes.CelestialCodecAesKey
}

func NewTemporalProcessor(workflowRequestDao dao.WorkflowRequestDao, clientFactory epifitemporal.ClientFactory, codecAesKey pkgcmdtypes.CelestialCodecAesKey) *Processor {
	return &Processor{
		workflowRequestDao: workflowRequestDao,
		clientFactory:      clientFactory,
		codecAesKey:        codecAesKey,
	}
}

func (p *Processor) InitiateWorkflow(ctx context.Context, workflowReq *celestialPb.WorkflowRequest, useCase commontypes.UseCase) error {
	if err := p.executeWorkflowReq(ctx, workflowReq, temporalEnumsPb.WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE, useCase); err != nil {
		return err
	}

	logger.Debug(ctx, "started workflow", logHelper.ClientReqID(workflowReq.GetClientReqId(), zap.String(logger.WORKFLOW_REQ_ID, workflowReq.GetId()))...)
	return nil
}

func (p *Processor) ReinitiateWorkflow(ctx context.Context, workflowReq *celestialPb.WorkflowRequest, useCase commontypes.UseCase) error {
	if err := p.executeWorkflowReq(ctx, workflowReq, temporalEnumsPb.WORKFLOW_ID_REUSE_POLICY_ALLOW_DUPLICATE, useCase); err != nil {
		return err
	}

	logger.Debug(ctx, "reinitiated workflow", logHelper.ClientReqID(workflowReq.GetClientReqId(), zap.String(logger.WORKFLOW_REQ_ID, workflowReq.GetId()))...)
	return nil
}

func (p *Processor) ValidateWorkflow(ctx context.Context, workflowType *workflowPb.TypeEnum) error {
	workflowRouter := router.GetRouter().GetWorkflowRouter()
	workflow := celestialPkg.GetWorkflowTypeFromTypeEnum(workflowType)
	_, err := workflowRouter.GetNamespace(workflow)
	if err != nil {
		return errors.Wrap(epifierrors.ErrPermanent,
			fmt.Sprintf("error in fetching namespace for workflow: %s, err: %s ", string(workflow), err))
	}
	return nil
}

func (p *Processor) SignalWorkflow(ctx context.Context, workflowRequest *celestialPb.WorkflowRequest, signalId epifitemporal.Signal, payload []byte) error {
	workflowRouter := router.GetRouter().GetWorkflowRouter()
	namespace, err := workflowRouter.GetNamespace(celestialPkg.GetWorkflowTypeFromTypeEnum(workflowRequest.GetTypeEnum()))
	if err != nil {
		return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error in fetching namespace for workflow, err: %s", err))
	}

	envNamespace, err := namespace.GetName()
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in fetching name for namespace, err: %s", err))
	}

	temporalClient, err := internal.GetTemporalClient(envNamespace, string(p.codecAesKey), p.clientFactory)
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in fetching temporal client for given namespace, err: %s", err))
	}

	//nolint:gocritic
	err = temporalClient.SignalWorkflow(ctx, workflowRequest.GetId(), "", string(signalId), payload)
	if err != nil {
		wfNotFoundErr := &serviceerror.NotFound{}
		if errors.As(err, &wfNotFoundErr) {
			return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error in sending signal to workflow err: %s", err))
		}

		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in signaling workflow, err: %s", err))
	}
	return nil
}

// QueryWorkflow queries a workflow for its current state variables
func (p *Processor) QueryWorkflow(ctx context.Context, workflowRequest *celestialPb.WorkflowRequest, queryType string, queryArgs []byte) ([]byte, error) {
	workflowRouter := router.GetRouter().GetWorkflowRouter()
	namespace, err := workflowRouter.GetNamespace(celestialPkg.GetWorkflowTypeFromTypeEnum(workflowRequest.GetTypeEnum()))
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error in fetching namespace for workflow, err: %s", err))
	}

	envNamespace, err := namespace.GetName()
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in fetching name for namespace, err: %s", err))
	}

	temporalClient, err := internal.GetTemporalClient(envNamespace, string(p.codecAesKey), p.clientFactory)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in fetching temporal client for given namespace, err: %s", err))
	}

	// Query the workflow
	queryResult, err := temporalClient.QueryWorkflow(ctx, workflowRequest.GetId(), "", queryType, queryArgs)
	if err != nil {
		wfNotFoundErr := &serviceerror.NotFound{}
		invalidArgumentErr := &serviceerror.InvalidArgument{}
		queryFailedErr := &serviceerror.QueryFailed{}

		switch {
		case errors.As(err, &wfNotFoundErr):
			return nil, errors.Wrap(epifierrors.ErrRecordNotFound, fmt.Sprintf("workflow execution not found, err: %s", err))
		case errors.As(err, &invalidArgumentErr) || errors.As(err, &queryFailedErr):
			return nil, errors.Wrap(epifierrors.ErrInvalidArgument, fmt.Sprintf("invalid query parameters or query failed, err: %s", err))
		default:
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in querying workflow, err: %s", err))
		}
	}

	// Extract the query result
	var resultBytes []byte
	if err := queryResult.Get(&resultBytes); err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in extracting query result, err: %s", err))
	}

	return resultBytes, nil
}

//nolint:funlen
func (p *Processor) executeWorkflowReq(ctx context.Context, workflowReq *celestialPb.WorkflowRequest, workflowIdReusePolicy temporalEnumsPb.WorkflowIdReusePolicy, useCase commontypes.UseCase) error {
	workflowRouter := router.GetRouter().GetWorkflowRouter()
	workflow := celestialPkg.GetWorkflowTypeFromTypeEnum(workflowReq.GetTypeEnum())
	namespace, err := workflowRouter.GetNamespace(workflow)
	if err != nil {
		return errors.Wrap(epifierrors.ErrPermanent,
			fmt.Sprintf("error in fetching namespace for workflow: %s, err: %s ", string(workflow), err))
	}

	envNamespace, err := namespace.GetName()
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient,
			fmt.Sprintf("error in fetching name for namespace: %s, err: %s", string(namespace), err))
	}

	temporalClient, err := internal.GetTemporalClient(envNamespace, string(p.codecAesKey), p.clientFactory)
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient,
			fmt.Sprintf("error in fetching temporal client for given namespace: %s, err: %s", string(namespace), err))
	}

	taskQueue, err := workflowRouter.GetTaskQueue(workflow)
	if err != nil {
		return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error in fetching task queue from workflow: %s, err: %s",
			workflow, err))
	}

	taskQueueName, err := taskQueue.GetName()
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in fetching name for task queue: %s err: %s", taskQueue, err))
	}

	if workflowReq.GetStatus() == stagePb.Status_CREATED {
		workflowRequestUpdate := &celestialPb.WorkflowRequest{
			Id:     workflowReq.GetId(),
			Status: stagePb.Status_INITIATED,
		}
		updateMask := []celestialPb.WorkflowRequestFieldMask{celestialPb.WorkflowRequestFieldMask_WORKFLOW_REQUEST_FIELD_MASK_STATUS}
		updErr := p.workflowRequestDao.Update(ctx, workflowRequestUpdate, updateMask, workflowReq.GetOwnership(), useCase)
		if updErr != nil {
			return errors.Wrap(epifierrors.ErrTransient,
				fmt.Sprintf("failed to update the workflow status to initiated, workflowReqId err: %s", updErr))
		}
	}

	workflowOptions := client.StartWorkflowOptions{
		ID:                    workflowReq.GetId(),
		TaskQueue:             taskQueueName,
		WorkflowIDReusePolicy: workflowIdReusePolicy,
	}

	_, err = temporalClient.ExecuteWorkflow(ctx, workflowOptions, string(celestialPkg.GetWorkflow(workflowReq.GetTypeEnum(), workflowReq.GetVersion())))
	if err == nil {
		return nil
	}

	nsNotFoundErr := &serviceerror.NamespaceNotFound{}
	invalidArgumentErr := &serviceerror.InvalidArgument{}
	if errors.As(err, &nsNotFoundErr) || errors.As(err, &invalidArgumentErr) {
		return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("error in executing workflow err: %s", err))
	}

	return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in executing workflow err: %s", err))
}
