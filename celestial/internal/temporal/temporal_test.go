package temporal_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/epifi/be-common/pkg/epifierrors"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	stagePb "github.com/epifi/be-common/api/celestial/workflow/stage"
	"github.com/epifi/be-common/pkg/epifitemporal"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace/recurringpayment"

	"github.com/epifi/gamma/celestial/internal"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	temporalEnumsPb "go.temporal.io/api/enums/v1"
	"go.temporal.io/api/serviceerror"
	"go.temporal.io/sdk/client"
	temporalMocks "go.temporal.io/sdk/mocks"
)

func TestProcessor_InitiateWorkflow(t *testing.T) {
	type args struct {
		ctx         context.Context
		workflowReq *celestialPb.WorkflowRequest
	}
	type mockUpdateWorkflowRequestDao struct {
		enable          bool
		workflowRequest *celestialPb.WorkflowRequest
		updateMask      []celestialPb.WorkflowRequestFieldMask
		ownership       commontypes.Ownership
		useCase         commontypes.UseCase
		err             error
	}
	type mockGetClient struct {
		enable    bool
		namespace string
		want      *temporalMocks.Client
		err       error
	}
	type mockExecuteWorkflow struct {
		enable   bool
		wo       client.StartWorkflowOptions
		workflow string
		want     *temporalMocks.WorkflowRun
		err      error
	}
	type mockGet struct {
		enable bool
		err    error
	}
	tests := []struct {
		name                         string
		args                         args
		mockUpdateWorkflowRequestDao mockUpdateWorkflowRequestDao
		mockGetClient                mockGetClient
		mockExecuteWorkflow          mockExecuteWorkflow
		mockGet                      mockGet
		wantErr                      bool
		wantErrType                  error
	}{
		{
			name: "error in updating workflow status to initiated due to connection issues",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
					Ownership: commontypes.Ownership_EPIFI_TECH,
				},
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "recurring-payment",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockUpdateWorkflowRequestDao: mockUpdateWorkflowRequestDao{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:     "workflow_req_id",
					Status: stagePb.Status_INITIATED,
				},
				updateMask: []celestialPb.WorkflowRequestFieldMask{celestialPb.WorkflowRequestFieldMask_WORKFLOW_REQUEST_FIELD_MASK_STATUS},
				ownership:  commontypes.Ownership_EPIFI_TECH,
				err:        errors.New("error"),
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrTransient,
		},
		{
			name: "error in fetching temporal client for given namespace",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "recurring-payment",
				want:      nil,
				err:       errors.New("error"),
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrTransient,
		},
		{
			name: "error in executing workflow on temporal",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockUpdateWorkflowRequestDao: mockUpdateWorkflowRequestDao{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:     "workflow_req_id",
					Status: stagePb.Status_INITIATED,
				},
				updateMask: []celestialPb.WorkflowRequestFieldMask{celestialPb.WorkflowRequestFieldMask_WORKFLOW_REQUEST_FIELD_MASK_STATUS},
				ownership:  commontypes.Ownership_EPIFI_TECH,
				err:        nil,
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "recurring-payment",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockExecuteWorkflow: mockExecuteWorkflow{
				enable: true,
				wo: client.StartWorkflowOptions{
					ID:                    "workflow_req_id",
					TaskQueue:             "recurring-payment-task-queue",
					WorkflowIDReusePolicy: temporalEnumsPb.WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE,
				},
				workflow: string(recurringpayment.CreateRecurringPaymentViaPayer),
				want:     nil,
				err:      errors.New("error"),
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrTransient,
		},
		{
			name: "should return permanent failure due for namespace not found error",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockUpdateWorkflowRequestDao: mockUpdateWorkflowRequestDao{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:     "workflow_req_id",
					Status: stagePb.Status_INITIATED,
				},
				updateMask: []celestialPb.WorkflowRequestFieldMask{celestialPb.WorkflowRequestFieldMask_WORKFLOW_REQUEST_FIELD_MASK_STATUS},
				ownership:  commontypes.Ownership_EPIFI_TECH,
				err:        nil,
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "recurring-payment",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockExecuteWorkflow: mockExecuteWorkflow{
				enable: true,
				wo: client.StartWorkflowOptions{
					ID:                    "workflow_req_id",
					TaskQueue:             "recurring-payment-task-queue",
					WorkflowIDReusePolicy: temporalEnumsPb.WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE,
				},
				workflow: string(recurringpayment.CreateRecurringPaymentViaPayer),
				want:     nil,
				err:      fmt.Errorf("test error: %w", &serviceerror.NamespaceNotFound{}),
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
		{
			name: "failed to get namespace for the workflow",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_TYPE_UNSPECIFIED),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
		{
			name: "initiated workflow successfully",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V0,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockUpdateWorkflowRequestDao: mockUpdateWorkflowRequestDao{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:     "workflow_req_id",
					Status: stagePb.Status_INITIATED,
				},
				updateMask: []celestialPb.WorkflowRequestFieldMask{celestialPb.WorkflowRequestFieldMask_WORKFLOW_REQUEST_FIELD_MASK_STATUS},
				ownership:  commontypes.Ownership_EPIFI_TECH,
				err:        nil,
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "recurring-payment",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockExecuteWorkflow: mockExecuteWorkflow{
				enable: true,
				wo: client.StartWorkflowOptions{
					ID:                    "workflow_req_id",
					TaskQueue:             "recurring-payment-task-queue",
					WorkflowIDReusePolicy: temporalEnumsPb.WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE,
				},
				workflow: string(recurringpayment.CreateRecurringPaymentViaPayer),
				want:     &temporalMocks.WorkflowRun{},
				err:      nil,
			},
			wantErr: false,
		},
		{
			name: "initiated workflow successfully for bumped up versions",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:      "workflow_req_id",
					Status:  stagePb.Status_CREATED,
					Version: workflowPb.Version_V1,
					Payload: nil,
					ClientReqId: &celestialPb.ClientReqId{
						Id:     "client_req_id",
						Client: workflowPb.Client_CX,
					},
					Ownership: commontypes.Ownership_EPIFI_TECH,
					TypeEnum:  celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
					StageEnum: celestialPkg.GetStageEnumFromProtoEnum(workflowPb.Stage_CREATION),
				},
			},
			mockUpdateWorkflowRequestDao: mockUpdateWorkflowRequestDao{
				enable: true,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:     "workflow_req_id",
					Status: stagePb.Status_INITIATED,
				},
				updateMask: []celestialPb.WorkflowRequestFieldMask{celestialPb.WorkflowRequestFieldMask_WORKFLOW_REQUEST_FIELD_MASK_STATUS},
				ownership:  commontypes.Ownership_EPIFI_TECH,
				err:        nil,
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "recurring-payment",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockExecuteWorkflow: mockExecuteWorkflow{
				enable: true,
				wo: client.StartWorkflowOptions{
					ID:                    "workflow_req_id",
					TaskQueue:             "recurring-payment-task-queue",
					WorkflowIDReusePolicy: temporalEnumsPb.WORKFLOW_ID_REUSE_POLICY_REJECT_DUPLICATE,
				},
				workflow: "CreateRecurringPaymentViaPayerV1",
				want:     &temporalMocks.WorkflowRun{},
				err:      nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		internal.ClearNamespaceClient()
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newProcessorWithMocks(t)
			if tt.mockUpdateWorkflowRequestDao.enable {
				md.WorkflowRequestDao.EXPECT().Update(gomock.Any(), tt.mockUpdateWorkflowRequestDao.workflowRequest,
					tt.mockUpdateWorkflowRequestDao.updateMask, tt.mockUpdateWorkflowRequestDao.ownership, tt.mockUpdateWorkflowRequestDao.useCase).Return(tt.mockUpdateWorkflowRequestDao.err)
			}
			if tt.mockGetClient.enable {
				defer func() {
					if tt.mockGetClient.want != nil {
						require.True(t, tt.mockGetClient.want.AssertExpectations(t))
					}
				}()

				md.ClientFactory.EXPECT().
					NewWorkflowClient(tt.mockGetClient.namespace, false, gomock.Any()).
					Return(tt.mockGetClient.want, tt.mockGetClient.err)

				if tt.mockGetClient.err == nil && tt.mockExecuteWorkflow.enable {
					tt.mockGetClient.want.
						On("ExecuteWorkflow", mock.Anything, tt.mockExecuteWorkflow.wo, tt.mockExecuteWorkflow.workflow).
						Return(tt.mockExecuteWorkflow.want, tt.mockExecuteWorkflow.err)
				}
			}

			err := svc.InitiateWorkflow(tt.args.ctx, tt.args.workflowReq, 0)
			if (err != nil) != tt.wantErr {
				t.Errorf("InitiateWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && tt.wantErrType != nil && !errors.Is(err, tt.wantErrType) {
				t.Errorf("want error type: %s: got err: %s", tt.wantErrType, err)
				return
			}

			assertMocks()
		})
	}
}

func TestProcessor_ValidateWorkflow(t *testing.T) {
	type args struct {
		ctx          context.Context
		workflowType *workflowPb.TypeEnum
	}
	tests := []struct {
		name        string
		args        args
		wantErr     bool
		wantErrType error
	}{
		{
			name: "Validation unsuccessful - failed to fetch namespace due to Workflow Namespace Not Found ",
			args: args{
				ctx:          context.Background(),
				workflowType: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_TYPE_UNSPECIFIED),
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
		{
			name: "Validation Successful - successfully get namespace",
			args: args{
				ctx:          context.Background(),
				workflowType: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_CREATE_RECURRING_PAYMENT_VIA_PAYER),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, _, assertMocks := newProcessorWithMocks(t)

			err := svc.ValidateWorkflow(tt.args.ctx, tt.args.workflowType)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidateWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && tt.wantErrType != nil && !errors.Is(err, tt.wantErrType) {
				t.Errorf("want error type: %s: got err: %s", tt.wantErrType, err)
				return
			}

			assertMocks()
		})
	}
}

func TestProcessor_SignalWorkflow(t *testing.T) {

	type mockGetClient struct {
		enable    bool
		namespace string
		want      *temporalMocks.Client
		err       error
	}
	type mockSignalWorkflow struct {
		enable        bool
		workflowReqId string
		signalId      string
		err           error
	}
	type args struct {
		ctx             context.Context
		workflowRequest *celestialPb.WorkflowRequest
		ownership       commontypes.Ownership
		signalId        epifitemporal.Signal
		payload         []byte
	}
	tests := []struct {
		name               string
		args               args
		mockGetClient      mockGetClient
		mockSignalWorkflow mockSignalWorkflow
		wantErr            bool
		wantErrType        error
	}{

		{
			name: "failed - wrong workflow type - namespace fetch failed",
			args: args{
				ctx: context.Background(),
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:     "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:  workflowPb.Stage_PAYMENT,
					Status: stagePb.Status_SUCCESSFUL,
					Type:   workflowPb.Type_TYPE_UNSPECIFIED,
				},
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
		{
			name: "failed to signal workflow due to some internal error from sdk",
			args: args{
				ctx:      context.Background(),
				signalId: payNs.B2CFundTransferCallbackSignal,
				payload:  nil,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:        true,
				workflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				signalId:      string(payNs.B2CFundTransferCallbackSignal),
				err:           epifierrors.ErrPermissionDenied,
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrTransient,
		},
		{
			name: "should return permanent failure in case signal workflow failed due to workflow not found error",
			args: args{
				ctx:      context.Background(),
				signalId: payNs.B2CFundTransferCallbackSignal,
				payload:  nil,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:        true,
				workflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				signalId:      string(payNs.B2CFundTransferCallbackSignal),
				err:           fmt.Errorf("test error: %w", &serviceerror.NotFound{}),
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
		{
			name: "signalled the workflow successfully",
			args: args{
				ctx:      context.Background(),
				signalId: payNs.B2CFundTransferCallbackSignal,
				payload:  nil,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:        true,
				workflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				signalId:      string(payNs.B2CFundTransferCallbackSignal),
				err:           nil,
			},
			wantErr: false,
		},
		{
			name: "signaled the workflow successfully for V1 workflow",
			args: args{
				ctx:      context.Background(),
				signalId: payNs.B2CFundTransferCallbackSignal,
				payload:  nil,
				workflowRequest: &celestialPb.WorkflowRequest{
					Id:       "6535e977-9107-47f3-9eb7-6aa5e072da30",
					Stage:    workflowPb.Stage_PAYMENT,
					Status:   stagePb.Status_SUCCESSFUL,
					Type:     workflowPb.Type_B2C_FUND_TRANSFER,
					Version:  workflowPb.Version_V1,
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockSignalWorkflow: mockSignalWorkflow{
				enable:        true,
				workflowReqId: "6535e977-9107-47f3-9eb7-6aa5e072da30",
				signalId:      string(payNs.B2CFundTransferCallbackSignal),
				err:           nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		internal.ClearNamespaceClient()
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newProcessorWithMocks(t)

			if tt.mockGetClient.enable {
				md.ClientFactory.EXPECT().
					NewWorkflowClient(tt.mockGetClient.namespace, false, gomock.Any()).
					Return(tt.mockGetClient.want, tt.mockGetClient.err)

				if tt.mockGetClient.err == nil && tt.mockSignalWorkflow.enable {
					tt.mockGetClient.want.
						On("SignalWorkflow", mock.Anything, tt.mockSignalWorkflow.workflowReqId, mock.Anything, tt.mockSignalWorkflow.signalId, mock.Anything).
						Return(tt.mockSignalWorkflow.err)
				}
			}
			err := svc.SignalWorkflow(tt.args.ctx, tt.args.workflowRequest, tt.args.signalId, tt.args.payload)
			if (err != nil) != tt.wantErr {
				t.Errorf("SignalWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && tt.wantErrType != nil && !errors.Is(err, tt.wantErrType) {
				t.Errorf("want error type: %s: got err: %s", tt.wantErrType, err)
				return
			}

			if tt.mockGetClient.enable && tt.mockGetClient.err == nil {
				tt.mockGetClient.want.AssertExpectations(t)
			}

			assertMocks()
		})
	}
}

func TestProcessor_QueryWorkflow(t *testing.T) {
	type args struct {
		ctx         context.Context
		workflowReq *celestialPb.WorkflowRequest
		queryType   string
		queryArgs   []byte
	}

	type mockGetClient struct {
		enable    bool
		namespace string
		want      *temporalMocks.Client
		err       error
	}

	type mockQueryWorkflow struct {
		enable     bool
		workflowID string
		runID      string
		queryType  string
		args       interface{}
		want       *temporalMocks.Value
		err        error
	}

	type mockGet struct {
		enable   bool
		valuePtr interface{}
		err      error
	}

	tests := []struct {
		name              string
		args              args
		mockGetClient     mockGetClient
		mockQueryWorkflow mockQueryWorkflow
		mockGet           mockGet
		want              []byte
		wantErr           bool
		wantErrType       error
	}{
		{
			name: "successfully query workflow",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable:     true,
				workflowID: "workflow_req_id",
				runID:      "",
				queryType:  "query_type",
				args:       []byte("query_args"),
				want:       &temporalMocks.Value{},
				err:        nil,
			},
			mockGet: mockGet{
				enable:   true,
				valuePtr: &[]byte{},
				err:      nil,
			},
			want:        []byte("query_result"),
			wantErr:     false,
			wantErrType: nil,
		},
		{
			name: "failed to get namespace for the workflow",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_TYPE_UNSPECIFIED),
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrPermanent,
		},
		{
			name: "failed to get temporal client",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      nil,
				err:       errors.New("temporal client error"),
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrTransient,
		},
		{
			name: "failed to query workflow due to not found error",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable:     true,
				workflowID: "workflow_req_id",
				runID:      "",
				queryType:  "query_type",
				args:       []byte("query_args"),
				want:       nil,
				err:        &serviceerror.NotFound{},
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrRecordNotFound,
		},
		{
			name: "failed to query workflow due to invalid argument error",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				queryType: "invalid_query_type",
				queryArgs: []byte("query_args"),
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable:     true,
				workflowID: "workflow_req_id",
				runID:      "",
				queryType:  "invalid_query_type",
				args:       []byte("query_args"),
				want:       nil,
				err:        &serviceerror.InvalidArgument{},
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrInvalidArgument,
		},
		{
			name: "failed to query workflow due to query failed error",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable:     true,
				workflowID: "workflow_req_id",
				runID:      "",
				queryType:  "query_type",
				args:       []byte("query_args"),
				want:       nil,
				err:        &serviceerror.QueryFailed{},
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrInvalidArgument,
		},
		{
			name: "failed to query workflow due to other error",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable:     true,
				workflowID: "workflow_req_id",
				runID:      "",
				queryType:  "query_type",
				args:       []byte("query_args"),
				want:       nil,
				err:        errors.New("other error"),
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrTransient,
		},
		{
			name: "failed to extract query result",
			args: args{
				ctx: context.Background(),
				workflowReq: &celestialPb.WorkflowRequest{
					Id:       "workflow_req_id",
					TypeEnum: celestialPkg.GetTypeEnumFromProtoEnum(workflowPb.Type_B2C_FUND_TRANSFER),
				},
				queryType: "query_type",
				queryArgs: []byte("query_args"),
			},
			mockGetClient: mockGetClient{
				enable:    true,
				namespace: "pay",
				want:      &temporalMocks.Client{},
				err:       nil,
			},
			mockQueryWorkflow: mockQueryWorkflow{
				enable:     true,
				workflowID: "workflow_req_id",
				runID:      "",
				queryType:  "query_type",
				args:       []byte("query_args"),
				want:       &temporalMocks.Value{},
				err:        nil,
			},
			mockGet: mockGet{
				enable:   true,
				valuePtr: &[]byte{},
				err:      errors.New("get error"),
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrTransient,
		},
	}

	for _, tt := range tests {
		internal.ClearNamespaceClient()
		t.Run(tt.name, func(t *testing.T) {
			svc, md, assertMocks := newProcessorWithMocks(t)

			if tt.mockGetClient.enable {
				md.ClientFactory.EXPECT().
					NewWorkflowClient(tt.mockGetClient.namespace, false, gomock.Any()).
					Return(tt.mockGetClient.want, tt.mockGetClient.err)

				if tt.mockGetClient.err == nil && tt.mockQueryWorkflow.enable {
					mockEncodedValue := &temporalMocks.Value{}
					tt.mockGetClient.want.
						On("QueryWorkflow", mock.Anything, tt.mockQueryWorkflow.workflowID, tt.mockQueryWorkflow.runID, tt.mockQueryWorkflow.queryType, tt.mockQueryWorkflow.args).
						Return(mockEncodedValue, tt.mockQueryWorkflow.err)

					if tt.mockQueryWorkflow.err == nil && tt.mockGet.enable {
						mockEncodedValue.On("Get", mock.AnythingOfType("*[]uint8")).
							Run(func(args mock.Arguments) {
								result := args.Get(0).(*[]byte)
								*result = []byte("query_result")
							}).
							Return(tt.mockGet.err)
					}
				}
			}

			got, err := svc.QueryWorkflow(tt.args.ctx, tt.args.workflowReq, tt.args.queryType, tt.args.queryArgs)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryWorkflow() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr && tt.wantErrType != nil && !errors.Is(err, tt.wantErrType) {
				t.Errorf("want error type: %s: got err: %s", tt.wantErrType, err)
				return
			}

			require.Equal(t, tt.want, got)

			if tt.mockGetClient.enable && tt.mockGetClient.err == nil {
				tt.mockGetClient.want.AssertExpectations(t)
			}

			assertMocks()
		})
	}
}
