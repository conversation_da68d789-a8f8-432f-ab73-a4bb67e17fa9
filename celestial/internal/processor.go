//go:generate mockgen -source=processor.go -destination=./mocks/mock_processor.go -package=mocks
package internal

import (
	"context"

	"github.com/google/wire"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifitemporal"

	"github.com/epifi/gamma/celestial/internal/celestial"
)

var WireSet = wire.NewSet(
	celestial.NewCelestialProcessor, wire.Bind(new(CelestialProcessor), new(*celestial.Processor)),
)

type CelestialProcessor interface {
	// CreateWorkflowRequest -  creates workflow request entry in DB
	// use cases include but not limited to adding workflow entry before child workflow creation
	CreateWorkflowRequest(ctx context.Context, req *celestialPb.WorkflowCreationRequestParams) (*celestialPb.WorkflowCreationResponseParams, *celestialPb.WorkflowRequest, error)
}

type TemporalProcessor interface {
	// InitiateWorkflow initialises workflow execution with temporal for the given workflowRequest
	InitiateWorkflow(ctx context.Context, workflowReq *celestialPb.WorkflowRequest, useCase commontypes.UseCase) error
	// ReinitiateWorkflow reinitialises workflow execution with temporal for the given workflow request
	ReinitiateWorkflow(ctx context.Context, workflowReq *celestialPb.WorkflowRequest, useCase commontypes.UseCase) error
	// ValidateWorkflow does basic workflow validation.
	// e.g. for initiating a workflow with temporal, we checks whether Namespace, is registered for the workflow or not, etc.
	ValidateWorkflow(ctx context.Context, workflowType *workflowPb.TypeEnum) error
	// SignalWorkflow sends a signal to the workflow along with some data(payload)
	SignalWorkflow(ctx context.Context, workflowReq *celestialPb.WorkflowRequest, signalId epifitemporal.Signal, payload []byte) error
	// QueryWorkflow queries a workflow for its current state variables
	QueryWorkflow(ctx context.Context, workflowReq *celestialPb.WorkflowRequest, queryType string, queryArgs []byte) ([]byte, error)
}
