// Code generated by MockGen. DO NOT EDIT.
// Source: processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	celestial "github.com/epifi/be-common/api/celestial"
	workflow "github.com/epifi/be-common/api/celestial/workflow"
	common "github.com/epifi/be-common/api/typesv2/common"
	epifitemporal "github.com/epifi/be-common/pkg/epifitemporal"
	gomock "github.com/golang/mock/gomock"
)

// MockCelestialProcessor is a mock of CelestialProcessor interface.
type MockCelestialProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockCelestialProcessorMockRecorder
}

// MockCelestialProcessorMockRecorder is the mock recorder for MockCelestialProcessor.
type MockCelestialProcessorMockRecorder struct {
	mock *MockCelestialProcessor
}

// NewMockCelestialProcessor creates a new mock instance.
func NewMockCelestialProcessor(ctrl *gomock.Controller) *MockCelestialProcessor {
	mock := &MockCelestialProcessor{ctrl: ctrl}
	mock.recorder = &MockCelestialProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCelestialProcessor) EXPECT() *MockCelestialProcessorMockRecorder {
	return m.recorder
}

// CreateWorkflowRequest mocks base method.
func (m *MockCelestialProcessor) CreateWorkflowRequest(ctx context.Context, req *celestial.WorkflowCreationRequestParams) (*celestial.WorkflowCreationResponseParams, *celestial.WorkflowRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateWorkflowRequest", ctx, req)
	ret0, _ := ret[0].(*celestial.WorkflowCreationResponseParams)
	ret1, _ := ret[1].(*celestial.WorkflowRequest)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// CreateWorkflowRequest indicates an expected call of CreateWorkflowRequest.
func (mr *MockCelestialProcessorMockRecorder) CreateWorkflowRequest(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateWorkflowRequest", reflect.TypeOf((*MockCelestialProcessor)(nil).CreateWorkflowRequest), ctx, req)
}

// MockTemporalProcessor is a mock of TemporalProcessor interface.
type MockTemporalProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockTemporalProcessorMockRecorder
}

// MockTemporalProcessorMockRecorder is the mock recorder for MockTemporalProcessor.
type MockTemporalProcessorMockRecorder struct {
	mock *MockTemporalProcessor
}

// NewMockTemporalProcessor creates a new mock instance.
func NewMockTemporalProcessor(ctrl *gomock.Controller) *MockTemporalProcessor {
	mock := &MockTemporalProcessor{ctrl: ctrl}
	mock.recorder = &MockTemporalProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTemporalProcessor) EXPECT() *MockTemporalProcessorMockRecorder {
	return m.recorder
}

// InitiateWorkflow mocks base method.
func (m *MockTemporalProcessor) InitiateWorkflow(ctx context.Context, workflowReq *celestial.WorkflowRequest, useCase common.UseCase) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateWorkflow", ctx, workflowReq, useCase)
	ret0, _ := ret[0].(error)
	return ret0
}

// InitiateWorkflow indicates an expected call of InitiateWorkflow.
func (mr *MockTemporalProcessorMockRecorder) InitiateWorkflow(ctx, workflowReq, useCase interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateWorkflow", reflect.TypeOf((*MockTemporalProcessor)(nil).InitiateWorkflow), ctx, workflowReq, useCase)
}

// QueryWorkflow mocks base method.
func (m *MockTemporalProcessor) QueryWorkflow(ctx context.Context, workflowReq *celestial.WorkflowRequest, queryType string, queryArgs []byte) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryWorkflow", ctx, workflowReq, queryType, queryArgs)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryWorkflow indicates an expected call of QueryWorkflow.
func (mr *MockTemporalProcessorMockRecorder) QueryWorkflow(ctx, workflowReq, queryType, queryArgs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryWorkflow", reflect.TypeOf((*MockTemporalProcessor)(nil).QueryWorkflow), ctx, workflowReq, queryType, queryArgs)
}

// ReinitiateWorkflow mocks base method.
func (m *MockTemporalProcessor) ReinitiateWorkflow(ctx context.Context, workflowReq *celestial.WorkflowRequest, useCase common.UseCase) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReinitiateWorkflow", ctx, workflowReq, useCase)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReinitiateWorkflow indicates an expected call of ReinitiateWorkflow.
func (mr *MockTemporalProcessorMockRecorder) ReinitiateWorkflow(ctx, workflowReq, useCase interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReinitiateWorkflow", reflect.TypeOf((*MockTemporalProcessor)(nil).ReinitiateWorkflow), ctx, workflowReq, useCase)
}

// SignalWorkflow mocks base method.
func (m *MockTemporalProcessor) SignalWorkflow(ctx context.Context, workflowReq *celestial.WorkflowRequest, signalId epifitemporal.Signal, payload []byte) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SignalWorkflow", ctx, workflowReq, signalId, payload)
	ret0, _ := ret[0].(error)
	return ret0
}

// SignalWorkflow indicates an expected call of SignalWorkflow.
func (mr *MockTemporalProcessorMockRecorder) SignalWorkflow(ctx, workflowReq, signalId, payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SignalWorkflow", reflect.TypeOf((*MockTemporalProcessor)(nil).SignalWorkflow), ctx, workflowReq, signalId, payload)
}

// ValidateWorkflow mocks base method.
func (m *MockTemporalProcessor) ValidateWorkflow(ctx context.Context, workflowType *workflow.TypeEnum) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateWorkflow", ctx, workflowType)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateWorkflow indicates an expected call of ValidateWorkflow.
func (mr *MockTemporalProcessorMockRecorder) ValidateWorkflow(ctx, workflowType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateWorkflow", reflect.TypeOf((*MockTemporalProcessor)(nil).ValidateWorkflow), ctx, workflowType)
}
