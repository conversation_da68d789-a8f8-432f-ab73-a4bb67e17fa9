package salaryprogram

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	durationPb "google.golang.org/protobuf/types/known/durationpb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"

	"github.com/epifi/gamma/featurestore"
	gammanames "github.com/epifi/gamma/pkg/names"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"golang.org/x/exp/maps"

	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/be-common/pkg/aws/v2/s3"

	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	"github.com/epifi/gamma/api/salaryprogram/dynamic_ui_element"
	dynamicUIElementPb "github.com/epifi/gamma/api/salaryprogram/dynamic_ui_element"
	salaryEnumsPb "github.com/epifi/gamma/api/salaryprogram/enums"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/frontend/tiering/earned_benefits"
	"github.com/epifi/gamma/frontend/user"

	accountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/salaryprogram/aa"
	"github.com/epifi/gamma/salaryprogram/aasalary"

	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"

	orderPaymentPb "github.com/epifi/gamma/api/order/payment"
	vgParserPb "github.com/epifi/gamma/api/vendorgateway/parser"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"

	caPb "github.com/epifi/gamma/api/connected_account"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	pkgErr "github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	eventsPkg "github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	accountsPb "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	dynamicElementsPb "github.com/epifi/gamma/api/dynamic_elements"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/deeplink"
	homeFePb "github.com/epifi/gamma/api/frontend/home"
	nudgePb "github.com/epifi/gamma/api/nudge"
	orderPb "github.com/epifi/gamma/api/order"
	paymentInstrumentPb "github.com/epifi/gamma/api/paymentinstrument"
	recurringpaymentPb "github.com/epifi/gamma/api/recurringpayment"
	rpPayloadPb "github.com/epifi/gamma/api/recurringpayment/payload"
	pb "github.com/epifi/gamma/api/salaryprogram"
	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	salaryprogramEventsPb "github.com/epifi/gamma/api/salaryprogram/events"
	"github.com/epifi/gamma/api/salaryprogram/notification"
	savingsPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/timeline"
	types "github.com/epifi/gamma/api/typesv2"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	deeplinkPkg "github.com/epifi/gamma/pkg/deeplink"
	"github.com/epifi/gamma/salaryprogram/concurenttaskmanager"
	"github.com/epifi/gamma/salaryprogram/concurenttaskmanager/taskexecutor"
	"github.com/epifi/gamma/salaryprogram/config"
	"github.com/epifi/gamma/salaryprogram/config/genconf"
	"github.com/epifi/gamma/salaryprogram/dao"
	"github.com/epifi/gamma/salaryprogram/dao/model"
	"github.com/epifi/gamma/salaryprogram/employerverifier"
	"github.com/epifi/gamma/salaryprogram/events"
	"github.com/epifi/gamma/salaryprogram/helper"
	"github.com/epifi/gamma/salaryprogram/metrics"
	"github.com/epifi/gamma/salaryprogram/minreqsalaryamount"
	"github.com/epifi/gamma/salaryprogram/salarytxnverifier"
	salaryTypes "github.com/epifi/gamma/salaryprogram/wire/types"
)

const (
	// placeholder in the salaryProgramActiveUserCntForEmpRedisKey should be replaced by empId for which the count is to be fetched
	salaryProgramActiveUsersCntForEmpRedisKey               = "salaryprogram:active_users_count_for_emp:%s"
	salaryProgramActiveUsersCntForEmpLastComputedAtRedisKey = "salaryprogram:active_users_count_for_emp_last_computed_at"
	// key used for acquiring a distributed lock for handling race conditions in salary lite mandate req initialisation flow
	salaryLiteMandateReqInitFlowLockKeyTemplate = "salaryprogram:salarylite_mandate_req_init_flow:%s"
	// key used for acquiring a distributed lock for handling race conditions in salary lite mandate execution req initialisation flow
	salaryLiteMandateExecutionReqInitFlowLockKeyTemplate = "salaryprogram:salarylite_mandate_execution_req_init_flow:%s"
	isSendingCommsToB2BWhitelistedUsersEnabled           = false
	// we show gtm popup only if activation done in last 24 hrs
	aaSalaryOffAppActivationGtmPopupDurationWindow = 24 * time.Hour
	// number of months of transactions to search salary transaction in AA transactions
	monthsForEvaluationForAaData = 6
	// number of months to search for income data from SMS parser
	monthsForEvaluationForSmsData = 4
	// min ratio of months to contain salary transaction
	ratioThreshold = 0.5
	// tolerance level for variance in salary amount
	tolerance = 0.30
	// min salary value
	minSalaryValue = 20000
	// start and end dates for salary cycle ( example 25th of a month to 10th of next month )
	startSalaryCycleDate             = 20
	endSalaryCycleDate               = 10
	aaSalaryProgram                  = "AA Salary"
	RegNotCompleted                  = "Registration not completed"
	RegCompletedBenefitsInActive     = "Registration completed, benefits inactive"
	BenefitsActive                   = "Benefits active"
	IncomeEstimatedActivationPending = "Income estimated, activation pending"
	IncomeEstimationPending          = "Income estimation pending"
)

var NoEligibleVariantErr = errors.New("no eligible variant found")

type Service struct {
	salaryprogramPb.UnimplementedSalaryProgramServer
	registrationDao                      dao.IRegistrationDao
	regStageDetailsDao                   dao.IRegistrationStageDetailsDao
	salaryTxnVerReqDao                   dao.ISalaryTxnVerificationRequestDao
	actHistoryDao                        dao.ISalaryProgramActivationHistoryDao
	whitelistedB2bUserDao                dao.IWhitelistedB2bUserDao
	nextRegStageGetter                   INextRegistrationStageGetter
	salaryTxnVerifier                    salarytxnverifier.ISalaryTxnVerifier
	minReqSalaryTxnAmtGetter             minreqsalaryamount.IMinReqSalaryAmountGetter
	txnExecutor                          storagev2.TxnExecutor
	orderClient                          orderPb.OrderServiceClient
	userHelperSvc                        helper.IUserHelperService
	salaryTxnDetectionPublisher          salaryTypes.SalaryTxnDetectionEventPublisher
	salaryProgramStatusUpdatePublisher   salaryTypes.SalaryProgramStatusUpdateEventPublisher
	nudgeClient                          nudgePb.NudgeServiceClient
	notificationHelper                   helper.INotificationHelper
	redisClient                          *redis.Client
	config                               *config.Config
	salaryEmployerVerifier               employerverifier.ISalaryEmployerVerifier
	employmentClient                     employmentPb.EmploymentClient
	eventsBroker                         eventsPkg.Broker
	salaryLiteMandateRequestDao          dao.ISalaryLiteMandateRequestDao
	salaryLiteMandateExecutionRequestDao dao.ISalaryLiteMandateExecutionRequestDao
	recurringPaymentServiceClient        recurringpaymentPb.RecurringPaymentServiceClient
	savingsClient                        savingsPb.SavingsClient
	piClient                             paymentInstrumentPb.PiClient
	actorClient                          actorPb.ActorClient
	distributedLockManager               lock.ILockManager
	timelineClient                       timeline.TimelineServiceClient
	dyconf                               *genconf.Config
	segmentationServiceClient            segmentPb.SegmentationServiceClient
	onboardingClient                     onboardingPb.OnboardingClient
	concurrentTaskExecutor               taskexecutor.IConcurrentTaskExecutorSvc
	opStatusClient                       operationalStatusPb.OperationalStatusServiceClient
	caClient                             caPb.ConnectedAccountClient
	vgParserClient                       vgParserPb.ParserClient
	aaSalaryProcessor                    aasalary.Processor
	salaryEstimationsDao                 dao.ISalaryEstimationsDao
	aaSalaryTxnVerificationDao           dao.IAASalaryTxnVerificationRequestsDao
	aaSalaryCriteriaDao                  dao.IAaSalaryCriteriaDao
	httpClient                           *http.Client
	salaryProgramBucketS3Client          s3.S3Client
	inAppTargetedCommsClient             tcPb.InAppTargetedCommsClient
	tieringClient                        tieringPb.TieringClient
	featurestoreFactory                  featurestore.IFactory
	dynamicUIElementDao                  dao.IDynamicUIElementDao
	dynamicUIElementEvaluatorConfigDao   dao.IDynamicUIElementEvaluatorConfigDao
	expressionEvaluator                  IExpressionEvaluator
}

func NewService(
	registrationDao dao.IRegistrationDao,
	regStageDetailsDao dao.IRegistrationStageDetailsDao,
	salaryTxnVerReqDao dao.ISalaryTxnVerificationRequestDao,
	actHistoryDao dao.ISalaryProgramActivationHistoryDao,
	nextRegStageGetter INextRegistrationStageGetter,
	salaryTxnVerifier salarytxnverifier.ISalaryTxnVerifier,
	minReqSalaryTxnAmountGetter minreqsalaryamount.IMinReqSalaryAmountGetter,
	txnExecutor storagev2.TxnExecutor,
	orderClient orderPb.OrderServiceClient,
	userHelperSvc helper.IUserHelperService,
	salaryTxnDetectionPublisher salaryTypes.SalaryTxnDetectionEventPublisher,
	salaryProgramStatusUpdatePublisher salaryTypes.SalaryProgramStatusUpdateEventPublisher,
	nudgeClient nudgePb.NudgeServiceClient,
	notificationHelper helper.INotificationHelper,
	redisClient salaryTypes.SalaryProgramRedisStore,
	config *config.Config,
	salaryEmployerVerifier employerverifier.ISalaryEmployerVerifier,
	employmentClient employmentPb.EmploymentClient,
	eventsBroker eventsPkg.Broker,
	salaryLiteMandateRequestDao dao.ISalaryLiteMandateRequestDao,
	salaryLiteMandateExecutionRequestDao dao.ISalaryLiteMandateExecutionRequestDao,
	recurringPaymentServiceClient recurringpaymentPb.RecurringPaymentServiceClient,
	savingsClient savingsPb.SavingsClient,
	piClient paymentInstrumentPb.PiClient,
	actorClient actorPb.ActorClient,
	distributedLockManager lock.ILockManager,
	timelineClient timeline.TimelineServiceClient,
	dyconf *genconf.Config,
	whitelistedB2bUserDao dao.IWhitelistedB2bUserDao,
	segmentationServiceClient segmentPb.SegmentationServiceClient,
	onboardingClient onboardingPb.OnboardingClient,
	concurrentTaskExecutor taskexecutor.IConcurrentTaskExecutorSvc,
	opStatusClient operationalStatusPb.OperationalStatusServiceClient,
	caClient caPb.ConnectedAccountClient,
	vgParserClient vgParserPb.ParserClient,
	aaSalaryProcessor aasalary.Processor,
	salaryEstimationsDao dao.ISalaryEstimationsDao,
	aaSalaryTxnVerificationDao dao.IAASalaryTxnVerificationRequestsDao,
	aaSalaryCriteriaDao dao.IAaSalaryCriteriaDao,
	httpClient *http.Client,
	salaryProgramBucketS3Client salaryTypes.SalaryProgramS3Client,
	inAppTargetedCommsClient tcPb.InAppTargetedCommsClient,
	tieringClient tieringPb.TieringClient,
	featurestoreFactory featurestore.IFactory,
	dynamicUIElementDao dao.IDynamicUIElementDao,
	dynamicUIElementEvaluatorConfigDao dao.IDynamicUIElementEvaluatorConfigDao,
	expressionEvaluator IExpressionEvaluator,
) *Service {
	return &Service{
		registrationDao:                      registrationDao,
		regStageDetailsDao:                   regStageDetailsDao,
		salaryTxnVerReqDao:                   salaryTxnVerReqDao,
		actHistoryDao:                        actHistoryDao,
		nextRegStageGetter:                   nextRegStageGetter,
		salaryTxnVerifier:                    salaryTxnVerifier,
		minReqSalaryTxnAmtGetter:             minReqSalaryTxnAmountGetter,
		txnExecutor:                          txnExecutor,
		orderClient:                          orderClient,
		userHelperSvc:                        userHelperSvc,
		salaryTxnDetectionPublisher:          salaryTxnDetectionPublisher,
		salaryProgramStatusUpdatePublisher:   salaryProgramStatusUpdatePublisher,
		nudgeClient:                          nudgeClient,
		notificationHelper:                   notificationHelper,
		config:                               config,
		redisClient:                          redisClient,
		salaryEmployerVerifier:               salaryEmployerVerifier,
		employmentClient:                     employmentClient,
		eventsBroker:                         eventsBroker,
		salaryLiteMandateRequestDao:          salaryLiteMandateRequestDao,
		salaryLiteMandateExecutionRequestDao: salaryLiteMandateExecutionRequestDao,
		recurringPaymentServiceClient:        recurringPaymentServiceClient,
		savingsClient:                        savingsClient,
		piClient:                             piClient,
		actorClient:                          actorClient,
		distributedLockManager:               distributedLockManager,
		timelineClient:                       timelineClient,
		dyconf:                               dyconf,
		whitelistedB2bUserDao:                whitelistedB2bUserDao,
		segmentationServiceClient:            segmentationServiceClient,
		onboardingClient:                     onboardingClient,
		concurrentTaskExecutor:               concurrentTaskExecutor,
		opStatusClient:                       opStatusClient,
		caClient:                             caClient,
		vgParserClient:                       vgParserClient,
		aaSalaryProcessor:                    aaSalaryProcessor,
		salaryEstimationsDao:                 salaryEstimationsDao,
		aaSalaryTxnVerificationDao:           aaSalaryTxnVerificationDao,
		aaSalaryCriteriaDao:                  aaSalaryCriteriaDao,
		httpClient:                           httpClient,
		salaryProgramBucketS3Client:          salaryProgramBucketS3Client,
		inAppTargetedCommsClient:             inAppTargetedCommsClient,
		tieringClient:                        tieringClient,
		featurestoreFactory:                  featurestoreFactory,
		dynamicUIElementDao:                  dynamicUIElementDao,
		dynamicUIElementEvaluatorConfigDao:   dynamicUIElementEvaluatorConfigDao,
		expressionEvaluator:                  expressionEvaluator,
	}
}

// compile time check to ensure Service implements salaryprogramPb.SalaryProgramServer
var _ salaryprogramPb.SalaryProgramServer = &Service{}

// CreateRegistration is useful for creating a new registration.
func (s *Service) CreateRegistration(ctx context.Context, req *salaryprogramPb.CreateRegistrationRequest) (*salaryprogramPb.CreateRegistrationResponse, error) {
	accountId, accountType, regFlowType, err := s.validateAndExtractParams(ctx, req)
	if err != nil {
		logger.Error(ctx, "error validating and extracting params", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &salaryprogramPb.CreateRegistrationResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	registration, err := s.registrationDao.Create(ctx, &salaryprogramPb.SalaryProgramRegistration{
		ActorId:                 req.GetActorId(),
		AccountId:               accountId,
		AccountType:             accountType,
		RegistrationFlowType:    regFlowType,
		RegistrationFlowVersion: s.config.CurrentRegistrationFlowVersion,
	})
	if err != nil {
		logger.Error(ctx, "error creating salary program registration entry in db", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &salaryprogramPb.CreateRegistrationResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	switch req.GetRegistrationFlowType() {
	case salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE:
		// publish salary program status update event
		_, publishErr := s.salaryProgramStatusUpdatePublisher.Publish(ctx, &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent{
			ActorId:             req.GetActorId(),
			SalaryProgramStatus: salaryprogramEventsPb.SalaryProgramStatus_SALARY_PROGRAM_STATUS_REGISTRATION_INITIATED,
			UpdatedAt:           registration.GetUpdatedAt(),
		})
		if publishErr != nil {
			logger.Error(ctx, "error publishing salary program status update event", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(publishErr))
			return &salaryprogramPb.CreateRegistrationResponse{
				Status: rpc.StatusInternalWithDebugMsg("error publishing program status update event"),
			}, nil
		}

		nudgeEntryRes, err := s.nudgeClient.PerformNudgeEntryForActor(ctx, &nudgePb.PerformNudgeEntryForActorRequest{
			ActorId:        req.GetActorId(),
			NudgeId:        s.config.SalaryProgramNudgeConfigs.RegistrationStartedButNotCompleted.NudgeId,
			EntryEventId:   req.GetActorId(),
			DelayInSeconds: uint32(s.config.SalaryProgramNudgeConfigs.RegistrationStartedButNotCompleted.NudgeDelay.Seconds()),
		})
		if rpcErr := epifigrpc.RPCError(nudgeEntryRes, err); rpcErr != nil {
			// silently ignoring the error since this is a non-critical step
			logger.Error(ctx, "unable to activate nudge for actor", zap.String(logger.NUDGE_ID, s.config.SalaryProgramNudgeConfigs.RegistrationStartedButNotCompleted.NudgeId))
		}

		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			// Version V0 notification when registration is initiated but employment verification is not done
			_ = s.notificationHelper.PublishSalaryNotificationDataEvents(
				epificontext.CloneCtx(ctx), registration.GetActorId(), notification.NotificationMedium_MEDIUM_PUSH_NOTIFICATION,
				notification.NotificationTrigger_REGISTRATION_INITIATED_BUT_EMP_VERIFICATION_NOT_DONE,
				&notification.TriggerMetadata{
					NotificationVersion: notification.NotificationVersion_VERSION_V0,
					Metadata: &notification.TriggerMetadata_RegInitiatedButEmpVerificationNotDoneTriggerMetadata{
						RegInitiatedButEmpVerificationNotDoneTriggerMetadata: &notification.RegInitiatedButEmpVerificationNotDoneTriggerMetadata{
							SalaryProgramRegistrationId: registration.GetId(),
						},
					},
				},
				s.config.SalaryProgramNotifications.RegInitiatedEmpVerificationNotDone.DelayDurations,
			)
		})

		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			// Version V1 notification when registration is initiated but employment verification is not done
			_ = s.notificationHelper.PublishSalaryNotificationDataEvents(
				epificontext.CloneCtx(ctx), registration.GetActorId(), notification.NotificationMedium_MEDIUM_PUSH_NOTIFICATION,
				notification.NotificationTrigger_REGISTRATION_INITIATED_BUT_EMP_VERIFICATION_NOT_DONE,
				&notification.TriggerMetadata{
					NotificationVersion: notification.NotificationVersion_VERSION_V1,
					Metadata: &notification.TriggerMetadata_RegInitiatedButEmpVerificationNotDoneTriggerMetadata{
						RegInitiatedButEmpVerificationNotDoneTriggerMetadata: &notification.RegInitiatedButEmpVerificationNotDoneTriggerMetadata{
							SalaryProgramRegistrationId: registration.GetId(),
						},
					},
				},
				s.config.SalaryProgramNotifications.RegInitiatedEmpVerificationNotDoneV1.DelayDurations,
			)
		})
	default:

	}

	return &salaryprogramPb.CreateRegistrationResponse{
		Status:       rpc.StatusOk(),
		Registration: registration,
	}, nil
}

func (s *Service) GetSalaryTxnVerificationRequestsCount(ctx context.Context,
	req *salaryprogramPb.GetSalaryTxnVerificationRequestsCountRequest) (*salaryprogramPb.GetSalaryTxnVerificationRequestsCountResponse, error) {
	daoFilters := &model.GetSalaryTxnVerificationRequestsCountFilter{
		RequestSourceIn:       req.GetRequestSourceIn(),
		VerificationStatus:    req.GetVerificationStatus(),
		VerificationSubStatus: req.GetVerificationSubStatus(),
	}
	count, err := s.salaryTxnVerReqDao.GetCount(ctx, daoFilters)
	if err != nil {
		logger.Error(ctx, "error getting count of salary verification txn requests", zap.Error(err))
		return &salaryprogramPb.GetSalaryTxnVerificationRequestsCountResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting count of salary verification txn requests"),
		}, nil
	}
	return &salaryprogramPb.GetSalaryTxnVerificationRequestsCountResponse{
		Status:                                rpc.StatusOk(),
		NumberOfSalaryTxnVerificationRequests: count,
	}, nil
}

// GetCurrentRegStatusAndNextRegStage is useful for fetching the current registration status and the next stage of registration that needs to completed.
func (s *Service) GetCurrentRegStatusAndNextRegStage(ctx context.Context, req *salaryprogramPb.CurrentRegStatusAndNextRegStageRequest) (*salaryprogramPb.CurrentRegStatusAndNextRegStageResponse, error) {
	registration, err := s.registrationDao.GetByActorId(ctx, req.GetActorId(), req.GetFlowType())
	switch {
	// if entry does not exist in registrations table then it implies registration was not initiated.
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return &salaryprogramPb.CurrentRegStatusAndNextRegStageResponse{
			Status:             rpc.StatusOk(),
			RegistrationStatus: salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED,
		}, nil

	case err != nil:
		logger.Error(ctx, "error fetching registration entry for the actor from db", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &salaryprogramPb.CurrentRegStatusAndNextRegStageResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	aaSalIncomeNotFoundStageDetails, getStageDetailErr := s.regStageDetailsDao.GetByRegIdAndStageName(ctx, registration.GetId(), salaryprogramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_AA_ACCOUNT_INCOME_NOT_FOUND)

	if getStageDetailErr != nil && !(errors.Is(getStageDetailErr, epifierrors.ErrRecordNotFound)) {
		logger.Error(ctx, "error fetching registration stage details for aa salary not found stage", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Error(getStageDetailErr))
		return &salaryprogramPb.CurrentRegStatusAndNextRegStageResponse{
			Status: rpc.StatusInternalWithDebugMsg(getStageDetailErr.Error()),
		}, nil
	}
	if aaSalIncomeNotFoundStageDetails != nil && aaSalIncomeNotFoundStageDetails.GetStageStatus() == salaryprogramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_COMPLETED {
		return &salaryprogramPb.CurrentRegStatusAndNextRegStageResponse{
			Status:             rpc.StatusOk(),
			RegistrationId:     registration.GetId(),
			RegistrationStatus: salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_FAILED,
			NextStage:          salaryprogramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_AA_ACCOUNT_INCOME_NOT_FOUND,
		}, nil
	}
	// get next stage to completed in the registration flow
	nextToBeCompletedStage, err := s.nextRegStageGetter.GetNextRegistrationStage(ctx, registration)
	if err != nil {
		logger.Error(ctx, "error fetching next to be completed stage", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Error(err))
		return &salaryprogramPb.CurrentRegStatusAndNextRegStageResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	successRes := &salaryprogramPb.CurrentRegStatusAndNextRegStageResponse{
		Status:         rpc.StatusOk(),
		RegistrationId: registration.GetId(),
	}

	// if nextToBeCompletedStage is UNSPECIFIED it implies all the stages are already COMPLETED
	if nextToBeCompletedStage == salaryprogramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_UNSPECIFIED {
		successRes.RegistrationStatus = salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED
	} else {
		successRes.RegistrationStatus = salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_INITIATED
		successRes.NextStage = nextToBeCompletedStage
	}

	return successRes, nil
}

// GetRegistrationStageDetails is useful to fetch the details of a registration stage for a given registration.
func (s *Service) GetRegistrationStageDetails(ctx context.Context, req *salaryprogramPb.GetRegistrationStageDetailsRequest) (*salaryprogramPb.GetRegistrationStageDetailsResponse, error) {
	regStageDetails, err := s.regStageDetailsDao.GetByRegIdAndStageName(ctx, req.GetRegistrationId(), req.GetStageName())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return &salaryprogramPb.GetRegistrationStageDetailsResponse{
			Status:      rpc.StatusOk(),
			StageStatus: salaryprogramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_NOT_INITIATED,
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching reg stage details from db", zap.String(logger.REGISTRATION_ID, req.GetRegistrationId()), zap.String("stageName", req.GetStageName().String()), zap.Error(err))
		return &salaryprogramPb.GetRegistrationStageDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &salaryprogramPb.GetRegistrationStageDetailsResponse{
		Status:      rpc.StatusOk(),
		StageStatus: regStageDetails.GetStageStatus(),
	}, nil
}

// UpdateEmploymentConfirmationStage is useful to update the employment confirmation stage of salary program registration.
// nolint: funlen
func (s *Service) UpdateEmploymentConfirmationStage(ctx context.Context, req *salaryprogramPb.UpdateEmploymentConfirmationStageRequest) (*salaryprogramPb.UpdateEmploymentConfirmationStageResponse, error) {
	// validate if a registration with given id
	registration, err := s.registrationDao.GetById(ctx, req.GetRegistrationId())
	if err != nil {
		logger.Error(ctx, "error fetching salary program registration entry from db", zap.String(logger.REGISTRATION_ID, req.GetRegistrationId()), zap.Error(err))
		return &salaryprogramPb.UpdateEmploymentConfirmationStageResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// employment confirmation stage update is allowed only if nextStageToBeCompleted is 'REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION'
	nextStageToBeCompleted, err := s.nextRegStageGetter.GetNextRegistrationStage(ctx, registration)
	if err != nil {
		logger.Error(ctx, "error fetching next to be completed stage", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Error(err))
		return &salaryprogramPb.UpdateEmploymentConfirmationStageResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if nextStageToBeCompleted != salaryprogramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION {
		logger.Error(ctx, "update employment confirmation stage is not allowed as nextStageToBeCompleted is not employment confirmation", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.String("nextStageToCompleted", nextStageToBeCompleted.String()), zap.Error(err))
		return &salaryprogramPb.UpdateEmploymentConfirmationStageResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("update employment confirmation stage is not allowed as nextStageToBeCompleted is not employment confirmation"),
		}, nil
	}

	// get the emp confirmation stage entry if it exists.
	empConfStageDetails, err := s.regStageDetailsDao.GetByRegIdAndStageName(ctx, registration.GetId(), salaryprogramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION)
	switch {
	// if employment confirmation stage details entry does not exist in db, create the stage with INITIATED state.
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		empConfStageDetails, err = s.regStageDetailsDao.Create(ctx, &salaryprogramPb.SalaryProgramRegistrationStageDetails{
			SalaryProgramRegistrationId: registration.GetId(),
			StageName:                   salaryprogramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION,
			StageStatus:                 salaryprogramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_INITIATED,
		})
		if err != nil {
			logger.Error(ctx, "error creating employment confirmation reg stage details", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Error(err))
			return &salaryprogramPb.UpdateEmploymentConfirmationStageResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	case err != nil:
		logger.Error(ctx, "error fetching employment confirmation stage details", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Error(err))
		return &salaryprogramPb.UpdateEmploymentConfirmationStageResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	switch req.GetUpdateStageStatusTo() {
	case salaryprogramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_INITIATED:
		// emp confirmation stage status cannot be updated to INITIATED after it has moved to some other status.
		if empConfStageDetails.GetStageStatus() != salaryprogramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_INITIATED {
			logger.Error(ctx, "cannot update emp confirmation stage status to INITIATED state", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.String("currentStageStatus", empConfStageDetails.GetStageStatus().String()))
			return &salaryprogramPb.UpdateEmploymentConfirmationStageResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("cannot update emp confirmation stage status to initiated state"),
			}, nil
		}

	// if stage status is not already in COMPLETED state, then try updating it to COMPLETED state.
	case salaryprogramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_COMPLETED:
		empConfStageDetails.StageStatus = salaryprogramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_COMPLETED
		// stageStatus can be updated to COMPLETED state only if its in INITIATED state currently.
		if err := s.regStageDetailsDao.UpdateWithCurrentStageStatusCheck(ctx, empConfStageDetails, []salaryprogramPb.SalaryProgramRegistrationStageDetailsFieldMask{salaryprogramPb.SalaryProgramRegistrationStageDetailsFieldMask_STAGE_STATUS},
			salaryprogramPb.SalaryProgramRegistrationStageStatus_REGISTRATION_STAGE_STATUS_INITIATED); err != nil {
			logger.Error(ctx, "error updating employment confirmation stage status to COMPLETED state", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Error(err))
			return &salaryprogramPb.UpdateEmploymentConfirmationStageResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
		logger.Info(ctx, "employment confirmation stage completed", zap.String(logger.ACTOR_ID_V2, registration.GetActorId()))

		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			// salary program notification when employment confirmation is complete but KYC is yet to be initiated
			_ = s.notificationHelper.PublishSalaryNotificationDataEvents(
				epificontext.CloneCtx(ctx), registration.GetActorId(), notification.NotificationMedium_MEDIUM_PUSH_NOTIFICATION,
				notification.NotificationTrigger_EMP_VERIFICATION_DONE_BUT_KYC_NOT_INITIATED_TRIGGER,
				&notification.TriggerMetadata{
					NotificationVersion: notification.NotificationVersion_VERSION_V0,
					Metadata: &notification.TriggerMetadata_EmpVerificationDoneButKycNotInitTriggerMetadata{
						EmpVerificationDoneButKycNotInitTriggerMetadata: &notification.EmpVerificationDoneButKycNotInitTriggerMetadata{
							SalaryProgramRegistrationId: registration.GetId(),
						},
					},
				},
				s.config.SalaryProgramNotifications.EmpVerificationDoneKycNotInitNotification.DelayDurations,
			)
		})

	default:
		logger.Error(ctx, "invalid emp confirmation stage details request, invalid UpdateStageStatusTo value", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.String("updateStageStatusTo", req.GetUpdateStageStatusTo().String()))
		return &salaryprogramPb.UpdateEmploymentConfirmationStageResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid emp confirmation stage details request, invalid UpdateStageStatusTo value"),
		}, nil
	}

	return &salaryprogramPb.UpdateEmploymentConfirmationStageResponse{
		Status:                      rpc.StatusOk(),
		EmpConfirmationStageDetails: empConfStageDetails,
	}, nil
}

func (s *Service) IsValidSalaryTxn(ctx context.Context, req *salaryprogramPb.IsValidSalaryTxnRequest) (*salaryprogramPb.IsValidSalaryTxnResponse, error) {
	// todo: check if multiple orders can be handled in one call
	// get the details of order for which salary verification request is raised
	orderWithTxnsRes, err := s.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{OrderId: req.GetOrderId()})
	if err != nil || !orderWithTxnsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "orderClient.GetOrderWithTransactions rpc call failed", zap.Any(logger.RPC_STATUS, orderWithTxnsRes.GetStatus()), zap.Error(err))
		return &salaryprogramPb.IsValidSalaryTxnResponse{Status: rpc.StatusInternalWithDebugMsg("orderClient.GetOrderWithTransactions rpc call failed")}, nil
	}
	orderWithTxns := orderWithTxnsRes.GetOrderWithTransactions()
	// verify if the given txn is a salary txn or not
	// Calling the auto verifier with timeout to avoid failing sync flows due to timeout/latency in auto verifier RPCs
	// verify if the given txn is a salary txn or not
	isSalaryTxn, verificationMetadata, err := s.salaryTxnVerifier.IsSalaryTxn(ctx, salarytxnverifier.SalaryTxnVerifierParams{
		OrderWithTxns:                            orderWithTxns,
		DiscountOnMinimumSalaryThreshold:         0,
		MinRequiredSalaryAmountForReverification: s.config.MinRequiredSalaryAmountForReverification,
		ValidationsToRun:                         req.GetSalaryTxnValidations(),
	})
	if err != nil {
		logger.Error(ctx, "auto verifier failed to respond", zap.Error(err))
		return &salaryprogramPb.IsValidSalaryTxnResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	validateSalaryTxnVerificationRequestResponse := &salaryprogramPb.IsValidSalaryTxnResponse{
		Status:           rpc.StatusOk(),
		IsValidSalaryTxn: isSalaryTxn,
	}

	if verificationMetadata != nil {
		validateSalaryTxnVerificationRequestResponse.FailedSalaryTxnValidation = verificationMetadata.FailedSalaryTxnValidation
	}

	return validateSalaryTxnVerificationRequestResponse, nil
}

// RaiseManualSalaryVerification is useful to raise a manual (user-initiated) salary verification request for verifying a given txn as salary.
// this rpc will validate if the given txn is a salary txn or not, if the verification (using realtime checks) fails, it will escalate the
// request to the ops team.
// nolint: funlen
func (s *Service) RaiseManualSalaryVerification(ctx context.Context, req *salaryprogramPb.RaiseManualSalaryVerificationRequest) (*salaryprogramPb.RaiseManualSalaryVerificationResponse, error) {
	if !lo.Contains([]salaryprogramPb.SalaryTxnVerificationRequestSource{salaryprogramPb.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST, salaryprogramPb.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_SALARY_OPS_REQUEST}, req.GetReqSource()) {
		logger.Info(ctx, "invalid request, request source should be IN_APP_USER_REQUEST or SALARY_OPS_REQUEST", zap.String("requestSource", req.GetReqSource().String()))
		return &salaryprogramPb.RaiseManualSalaryVerificationResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("invalid request, request source should be IN_APP_USER_REQUEST or SALARY_OPS_REQUEST")}, nil
	}

	// check if the salary program registration is completed by the user, salary identification is done only for users who have completed the registration.
	regStatusResp, err := s.GetCurrentRegStatusAndNextRegStage(ctx, &salaryprogramPb.CurrentRegStatusAndNextRegStageRequest{ActorId: req.GetActorId(),
		FlowType: salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE})
	if err != nil || !regStatusResp.GetStatus().IsSuccess() {
		logger.Error(ctx, "GetCurrentRegStatusAndNextRegStage call failed", zap.Any(logger.RPC_STATUS, regStatusResp.GetStatus()), zap.Error(err))
		return &salaryprogramPb.RaiseManualSalaryVerificationResponse{Status: rpc.StatusInternalWithDebugMsg("orderClient.GetOrderWithTransactions rpc call failed")}, nil
	}
	if regStatusResp.GetRegistrationStatus() != salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		logger.Error(ctx, "cannot raise manual salary verification as salary program registration is not yet completed by the user", zap.String(logger.ACTOR_ID, req.GetActorId()))
		return &salaryprogramPb.RaiseManualSalaryVerificationResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("cannot raise manual salary verification as salary program registration is not yet completed BY the user")}, nil
	}
	salaryProgramRegId := regStatusResp.GetRegistrationId()

	// get the details of order for which salary verification request is raised
	orderWithTxnsRes, err := s.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{OrderId: req.GetOrderId()})
	if err != nil || !orderWithTxnsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "orderClient.GetOrderWithTransactions rpc call failed", zap.Any(logger.RPC_STATUS, orderWithTxnsRes.GetStatus()), zap.Error(err))
		return &salaryprogramPb.RaiseManualSalaryVerificationResponse{Status: rpc.StatusInternalWithDebugMsg("orderClient.GetOrderWithTransactions rpc call failed")}, nil
	}
	orderWithTxns := orderWithTxnsRes.GetOrderWithTransactions()
	order := orderWithTxns.GetOrder()

	// actorId in request should be the beneficiary actor of the txn for which salary verification request is raised
	if req.GetActorId() != orderWithTxns.GetOrder().GetToActorId() {
		logger.Error(ctx, "actor id in request is not the beneficiary actor of txn", zap.Any(logger.RPC_STATUS, orderWithTxnsRes.GetStatus()), zap.Error(err))
		return &salaryprogramPb.RaiseManualSalaryVerificationResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id in request is not the beneficiary actor of txn")}, nil
	}

	// check if verification request already exists for given txn, if yes return that request
	existingSalaryVerRequest, err := s.salaryTxnVerReqDao.GetByTxnId(ctx, order.GetExternalId())
	switch {
	// if request does not already exist, we'll create one later in the flow.
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Info(ctx, "no existing verification request for txnId", zap.String(logger.ORDER_ID, req.GetOrderId()))
	// if err occurred while fetching the existing request, return ISE.
	case err != nil:
		logger.Error(ctx, "error fetching salary verification request using txnId", zap.Any(logger.ORDER_ID, req.GetOrderId()), zap.Error(err))
		return &salaryprogramPb.RaiseManualSalaryVerificationResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	// if no error is returned, then it implies that a verification request already exists, just return that.
	default:
		logger.Info(ctx, "salary verification request already exists for txnId", zap.String(logger.ORDER_ID, req.GetOrderId()))
		return &salaryprogramPb.RaiseManualSalaryVerificationResponse{
			Status:                rpc.StatusOk(),
			SalaryVerificationReq: existingSalaryVerRequest,
		}, nil
	}

	var salaryTxnVerReq *salaryprogramPb.SalaryTxnVerificationRequest

	ctxWithTimeout, cancel := context.WithTimeout(ctx, 2*time.Second)
	defer cancel()
	// verify if the given txn is a salary txn or not
	// Calling the auto verifier with timeout to avoid failing sync flows due to timeout/latency in auto verifier RPCs
	// verify if the given txn is a salary txn or not
	isSalaryTxn, verificationMetadata, err := s.salaryTxnVerifier.IsSalaryTxn(ctxWithTimeout, salarytxnverifier.SalaryTxnVerifierParams{
		OrderWithTxns:                            orderWithTxns,
		DiscountOnMinimumSalaryThreshold:         0,
		MinRequiredSalaryAmountForReverification: s.config.MinRequiredSalaryAmountForReverification,
	})
	switch {
	// if the txn is not verified as salary by the system checks or remitter info not found for the txn or txn category is not found or uan lookup api failed
	// then escalate the verification request to ops team.
	case err == nil && !isSalaryTxn,
		errors.Is(err, salarytxnverifier.ErrRemitterInfoNotFound),
		errors.Is(err, salarytxnverifier.ErrTxnCategoryNotFound),
		errors.Is(err, salarytxnverifier.ErrUanLookupApiFailed),
		errors.Is(ctxWithTimeout.Err(), context.DeadlineExceeded):

		salaryTxnVerReq, err = s.salaryTxnVerReqDao.Create(ctx, &salaryprogramPb.SalaryTxnVerificationRequest{
			ActorId:               req.GetActorId(),
			TxnId:                 order.GetExternalId(),
			RequestSource:         req.GetReqSource(),
			VerificationStatus:    salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS,
			VerificationSubStatus: salaryprogramPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_OPS_VERIFICATION,
			VerificationVersion:   salaryprogramPb.SalaryTxnVerificationVersion_VERIFICATION_VERSION_V1,
			TxnTimestamp:          order.GetCreatedAt(),
		})
		if err != nil {
			// todo (utkarsh) : check if we log this in secure logs
			logger.Error(ctx, "error while creating verification req entry in db", zap.Error(err))
			return &salaryprogramPb.RaiseManualSalaryVerificationResponse{Status: rpc.StatusInternalWithDebugMsg("error while creating verification req entry in db")}, nil
		}
	case err != nil:
		logger.Error(ctx, "error while checking if a txn is a salary txn", zap.String(logger.ACTOR_ID, req.GetActorId()), zap.String(logger.ORDER_ID, orderWithTxns.GetOrder().GetId()), zap.Error(err))
		return &salaryprogramPb.RaiseManualSalaryVerificationResponse{Status: rpc.StatusInternalWithDebugMsg("error while checking if a txn is a salary txn")}, nil
	// since the given order is successfully verified as a salary txn, create a salary verification request and a salary program activation entry.
	default:
		var (
			activationEntry *salaryprogramPb.SalaryProgramActivationHistory
			activationErr   error
		)
		if txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
			salaryTxnTimeInIST := order.GetCreatedAt().AsTime().In(datetime.IST)

			salaryTxnVerificationRequest := &salaryprogramPb.SalaryTxnVerificationRequest{
				ActorId:             req.GetActorId(),
				TxnId:               order.GetExternalId(),
				RequestSource:       req.GetReqSource(),
				VerificationStatus:  salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED,
				VerifiedBy:          salaryprogramPb.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_SYSTEM,
				VerificationVersion: salaryprogramPb.SalaryTxnVerificationVersion_VERIFICATION_VERSION_V1,
				TxnEmployerId:       verificationMetadata.SalaryTxnEmployerId,
				TxnTimestamp:        timestampPb.New(salaryTxnTimeInIST),
				AutoVerifierMeta:    verificationMetadata.AdditionalMeta,
			}

			// if the remitter name matches with any other employer in db, then we will ask the user to confirm the new employer
			if verificationMetadata.RemitterMatchesWithOtherEmployerInDb {
				salaryTxnVerificationRequest.VerificationSubStatus = salaryprogramPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE
			}

			salaryTxnVerReq, err = s.salaryTxnVerReqDao.Create(txnCtx, salaryTxnVerificationRequest)
			if err != nil {
				return fmt.Errorf("error creating salary txn verification req entry in db, err : %w", err)
			}

			activeTillTime := salaryTxnTimeInIST.Add(s.config.SalaryProgramNewActivationDuration)
			// making roundoff configurable to help testing some cases on non-prod
			if s.config.ShouldRoundOffActivationTillTimeToEndOfDay {
				activeTillTime = datetime.EndOfDay(activeTillTime)
			}
			activationEntry, activationErr = s.actHistoryDao.Create(txnCtx, &salaryprogramPb.SalaryProgramActivationHistory{
				SalaryProgramRegistrationId:    salaryProgramRegId,
				ActiveFrom:                     timestampPb.Now(),
				ActiveTill:                     timestampPb.New(activeTillTime),
				SalaryTxnVerificationRequestId: salaryTxnVerReq.GetId(),
				ActivationAction:               salaryprogramPb.SalaryActivationAction_SALARY_TXN,
				ActivationActionRefId:          salaryTxnVerReq.GetId(),
			})
			if activationErr != nil {
				return fmt.Errorf("error creating activation history entry in db, err : %w", activationErr)
			}

			return nil
		}); txnErr != nil {
			logger.Error(ctx, "error while creating salary verification req and activation entry in a db txn", zap.Error(txnErr))
			return &salaryprogramPb.RaiseManualSalaryVerificationResponse{Status: rpc.StatusInternalWithDebugMsg("error while creating salary verification req and activation entry in a db txn")}, nil
		}

		// todo (utkarsh) : remove this log few weeks after launch, added just for quick analytics
		logger.Info(ctx, "activation entry created for registration", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))

		// publish salary txn detection event after salary verification is successful,
		// todo (utkarsh) : consider moving salary txn verification request state update and event publish in an atomic block using transactional outbox.
		_, publishErr := s.salaryTxnDetectionPublisher.Publish(ctx, &salaryprogramEventsPb.SalaryDetectionEvent{
			ExternalOrderId:     salaryTxnVerReq.GetTxnId(),
			ActorId:             salaryTxnVerReq.GetActorId(),
			SalaryTxnTime:       salaryTxnVerReq.GetTxnTimestamp(),
			SalaryDetectionTime: salaryTxnVerReq.GetUpdatedAt(),
		})
		if publishErr != nil {
			logger.Error(ctx, "error publishing salary txn detection event", zap.String(logger.REQUEST_ID, salaryTxnVerReq.GetId()), zap.Error(publishErr))
			return &salaryprogramPb.RaiseManualSalaryVerificationResponse{
				Status: rpc.StatusInternalWithDebugMsg("error publishing salary txn detection event"),
			}, nil
		}

		// publish salary program status update event
		_, publishErr = s.salaryProgramStatusUpdatePublisher.Publish(ctx, &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent{
			ActorId:             salaryTxnVerReq.GetActorId(),
			SalaryProgramStatus: salaryprogramEventsPb.SalaryProgramStatus_SALARY_PROGRAM_STATUS_ACTIVE,
			UpdatedAt:           salaryTxnVerReq.GetUpdatedAt(),
			ActivationHistoryId: activationEntry.GetId(),
			StatusMetadata: &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent_ActiveStatusMetadata{
				ActiveStatusMetadata: &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent_ActiveStatusMeta{
					ActivationHistoryId: activationEntry.GetId(),
					ActivationType:      helper.GetSalaryActivationTypeFromActivationAction(activationEntry.GetActivationAction()),
				},
			},
		})
		if publishErr != nil {
			logger.Error(ctx, "error publishing salary program status update event", zap.String(logger.REQUEST_ID, salaryTxnVerReq.GetId()), zap.Error(publishErr))
			return &salaryprogramPb.RaiseManualSalaryVerificationResponse{
				Status: rpc.StatusInternalWithDebugMsg("error publishing program status update event"),
			}, nil
		}

		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			// trigger salary program activation notification
			_ = s.notificationHelper.PublishSalaryNotificationDataEvents(
				epificontext.CloneCtx(ctx), salaryTxnVerReq.GetActorId(), notification.NotificationMedium_MEDIUM_PUSH_NOTIFICATION,
				notification.NotificationTrigger_SALARY_BENEFITS_ACTIVATED_TRIGGER,
				&notification.TriggerMetadata{
					NotificationVersion: notification.NotificationVersion_VERSION_V0,
					Metadata: &notification.TriggerMetadata_BenefitsActivatedTriggerMetadata{
						BenefitsActivatedTriggerMetadata: &notification.SalaryBenefitsActivatedTriggerMetadata{
							SalaryProgramActivationId: activationEntry.GetId(),
						},
					},
				},
				s.config.SalaryProgramNotifications.SalaryBenefitsActivatedNotification.DelayDurations,
			)
		})

		// nolint:dupl
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			benefitsActiveTill := activationEntry.GetActiveTill().AsTime()
			eventDelayDurations := make([]time.Duration, 0, len(s.config.SalaryProgramNotifications.SalaryBenefitsInactivatedNotification.DelayDurations))

			durationTillBenefitsAreActive := time.Until(benefitsActiveTill)
			if durationTillBenefitsAreActive < -time.Nanosecond {
				logger.Error(epificontext.CloneCtx(ctx), "durationTillBenefitsAreActive is invalid", zap.Duration("durationTillBenefitsAreActive", durationTillBenefitsAreActive),
					zap.Time("benefitsActiveTill", benefitsActiveTill), zap.String("activationId", activationEntry.GetId()),
				)
				return
			}

			// adding configured delays to the expected delay of the msg, i.e. duration after which the benefits will get inactivated
			for _, delayDuration := range s.config.SalaryProgramNotifications.SalaryBenefitsInactivatedNotification.DelayDurations {
				eventDelayDurations = append(eventDelayDurations, durationTillBenefitsAreActive+delayDuration)
			}

			// trigger salary program inactivation notification
			_ = s.notificationHelper.PublishSalaryNotificationDataEvents(
				epificontext.CloneCtx(ctx), salaryTxnVerReq.GetActorId(), notification.NotificationMedium_MEDIUM_PUSH_NOTIFICATION,
				notification.NotificationTrigger_SALARY_BENEFITS_INACTIVATED_TRIGGER,
				&notification.TriggerMetadata{
					NotificationVersion: notification.NotificationVersion_VERSION_V0,
					Metadata: &notification.TriggerMetadata_BenefitsInactivatedTriggerMetadata{
						BenefitsInactivatedTriggerMetadata: &notification.SalaryBenefitsInactivatedTriggerMetadata{
							SalaryProgramActivationId: activationEntry.GetId(),
						},
					},
				},
				eventDelayDurations,
			)
		})

		goroutine.Run(ctx, time.Second*5, func(gctx context.Context) {
			// trigger salary benefits grace period started notification
			gracePeriodStartFrom := salaryTxnVerReq.GetTxnTimestamp().AsTime().Add(s.config.MinReqDurationSinceLastActivationForShowingGracePeriod)
			durationTillGracePeriodStart := time.Until(gracePeriodStartFrom)

			if durationTillGracePeriodStart < 0 {
				logger.Error(gctx, "durationTillGracePeriodStart is invalid", zap.Duration("durationTillGracePeriodStart", durationTillGracePeriodStart),
					zap.String("activationId", activationEntry.GetId()),
				)
				return
			}
			eventDelayDurations := make([]time.Duration, 0, len(s.config.SalaryProgramNotifications.SalaryBenefitsGracePeriodStartedNotification.DelayDurations))

			// adding configured delays to the expected delay of the msg, i.e. duration after which the benefits grace period start
			for _, delayDuration := range s.config.SalaryProgramNotifications.SalaryBenefitsGracePeriodStartedNotification.DelayDurations {
				eventDelayDurations = append(eventDelayDurations, durationTillGracePeriodStart+delayDuration)
			}

			_ = s.notificationHelper.PublishSalaryNotificationDataEvents(
				gctx, salaryTxnVerReq.GetActorId(), notification.NotificationMedium_MEDIUM_PUSH_NOTIFICATION,
				notification.NotificationTrigger_SALARY_BENEFITS_GRACE_PERIOD_STARTED_TRIGGER,
				&notification.TriggerMetadata{
					NotificationVersion: notification.NotificationVersion_VERSION_V0,
					Metadata: &notification.TriggerMetadata_SalaryBenefitsGracePeriodStartedTriggerMetadata{
						SalaryBenefitsGracePeriodStartedTriggerMetadata: &notification.SalaryBenefitsGracePeriodStartedTriggerMetadata{
							SalaryProgramActivationId: activationEntry.GetId(),
						},
					},
				},
				eventDelayDurations,
			)
		})

		// publish activation event to rudder for analytics/marketing campaigns
		goroutine.Run(epificontext.CloneCtx(ctx), 5*time.Second, func(gctx context.Context) {
			s.publishSalaryProgramActivationEvent(gctx, salaryTxnVerReq.GetActorId(), activationEntry)
		})

		metrics.MetricsRecorder.RecordSalaryVerificationReqStatusUpdate(salaryTxnVerReq.GetRequestSource().String(), salaryTxnVerReq.GetVerificationStatus().String(), salaryTxnVerReq.GetVerificationSubStatus().String(), salaryTxnVerReq.GetVerifiedBy().String())
	}

	return &salaryprogramPb.RaiseManualSalaryVerificationResponse{
		Status:                rpc.StatusOk(),
		SalaryVerificationReq: salaryTxnVerReq,
	}, nil
}

// nolint: funlen
// UpdateManualSalaryTxnVerificationStatus is useful to update the status of a manual salary txn verification request.
func (s *Service) UpdateManualSalaryTxnVerificationStatus(ctx context.Context, req *salaryprogramPb.UpdateManualSalaryTxnVerificationStatusRequest) (*salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse, error) {
	verificationReq, err := s.salaryTxnVerReqDao.GetById(ctx, req.GetSalaryTxnVerRequestId())
	if err != nil {
		logger.Error(ctx, "error fetching verification request from db", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()), zap.Error(err))
		return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	existingVerificationStatus := verificationReq.GetVerificationStatus()

	// check if verification request was raised by user or salary-ops, only user or salary-ops raised requests can be updated using this rpc
	if !lo.Contains([]salaryprogramPb.SalaryTxnVerificationRequestSource{salaryprogramPb.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_IN_APP_USER_REQUEST,
		salaryprogramPb.SalaryTxnVerificationRequestSource_VERIFICATION_REQUEST_SOURCE_SALARY_OPS_REQUEST}, verificationReq.GetRequestSource()) {
		logger.Info(ctx, "status update not allowed, salary verification request was not raised manually", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()))
		return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("status update not allowed, salary verification request was not raised manually"),
		}, nil
	}

	updateMask := []salaryprogramPb.SalaryTxnVerificationRequestFieldMask{salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_STATUS, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_REMARK}
	verificationReq.VerificationStatus = req.GetUpdateStatusTo()
	verificationReq.VerificationRemark = req.GetVerificationRemark()

	// get salary program registration id for actor
	registrationEntry, getRegErr := s.registrationDao.GetByActorId(ctx, verificationReq.GetActorId(), salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE)
	if getRegErr != nil {
		logger.Error(ctx, "error fetching registration entry by actorId", zap.String(logger.ACTOR_ID, verificationReq.GetActorId()))
		return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg(getRegErr.Error()),
		}, nil
	}

	switch req.GetUpdateStatusTo() {
	case salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS:
		if existingVerificationStatus != salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS && existingVerificationStatus != salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED {
			logger.Info(ctx, "status update not allowed, salary verification request cannot be updated to IN_PROGRESS state from it's current state", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()), zap.String("currentState", existingVerificationStatus.String()))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("status update not allowed, salary verification request cannot be updated to IN_PROGRESS state from it's current state"),
			}, nil
		}
		// sub-status is mandatory for updating the request status to IN_PROGRESS state
		if req.GetUpdateSubStatusTo() == salaryprogramPb.SalaryTxnVerificationRequestSubStatus_SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED {
			logger.Info(ctx, "sub-status is not present in update request", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("sub-status is not present in update request"),
			}, nil
		}
		updateMask = append(updateMask, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_SUB_STATUS)
		verificationReq.VerificationSubStatus = req.GetUpdateSubStatusTo()

		// If the status is to be updated from VERIFICATION_FAILED state to IN_PROGRESS state, then verified_by ,user_ack_status, verification_failure_reason_category and verification_failure_reason_sub_category columns needs to be reset to unspecified state as
		// verified_by/user_ack_status columns should be set only for requests in terminal state and failure reason category/sub-category are set only in case of VERIFICATION_FAILED state.
		if existingVerificationStatus == salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED {
			updateMask = append(updateMask, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFIED_BY, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_USER_ACK_STATUS,
				salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_FAILURE_REASON_CATEGORY, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_FAILURE_REASON_SUB_CATEGORY)
			verificationReq.VerifiedBy = salaryprogramPb.SalaryTxnVerificationRequestVerifiedBy_SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED
			verificationReq.UserAckStatus = salaryprogramPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED
			verificationReq.VerificationFailureReasonCategory = salaryprogramPb.SalaryTxnVerificationFailureReasonCategory_FAILURE_REASON_CATEGORY_UNSPECIFIED
			verificationReq.VerificationFailureReasonSubCategory = salaryprogramPb.SalaryTxnVerificationFailureReasonSubCategory_FAILURE_REASON_SUB_CATEGORY_UNSPECIFIED
		}

		// for updating the sub-status to AWAITING_EMPLOYMENT_UPDATE, txn_emp_id is mandatory
		if req.GetUpdateSubStatusTo() == salaryprogramPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE {
			if req.GetSalaryTxnEmployerId() == "" {
				logger.Error(ctx, "mandatory field txn_emp_id is missing in update request", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()), zap.String(logger.VERIFICATION_STATUS, req.GetUpdateStatusTo().String()), zap.String("verificationSubStatus", req.GetUpdateSubStatusTo().String()))
				return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
					Status: rpc.StatusInvalidArgumentWithDebugMsg("mandatory field txn_emp_id is missing in update request"),
				}, nil
			}
			updateMask = append(updateMask, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_TXN_EMPLOYER_ID)
			verificationReq.TxnEmployerId = req.GetSalaryTxnEmployerId()
		}

		// updating verification request with current status check to the make status check assertions thread safe.
		expectedCurrentVerificationStatus := existingVerificationStatus
		if updateErr := s.salaryTxnVerReqDao.UpdateWithCurrentVerificationStatusCheck(ctx, verificationReq, updateMask, expectedCurrentVerificationStatus); updateErr != nil {
			logger.Error(ctx, "error updating manual verification request db entry", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()), zap.Error(updateErr))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg(updateErr.Error()),
			}, nil
		}

	case salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED:
		if existingVerificationStatus != salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS && existingVerificationStatus != salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED {
			logger.Info(ctx, "status update not allowed, salary verification request cannot be updated to VERIFIED state from it's current state", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()), zap.String("currentState", existingVerificationStatus.String()))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("status update not allowed, salary verification request cannot be updated to VERIFIED state from it's current state"),
			}, nil
		}

		// If the status is to be updated from VERIFICATION_FAILED state to VERIFIED state, then user_ack_status, verification_failure_reason_category and verification_failure_reason_sub_category needs to be reset to UNSPECIFIED state
		// as earlier ack was for a different status which is being updated now and failure reason category/sub-category are set only in case of VERIFICATION_FAILED state.
		if existingVerificationStatus == salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED {
			updateMask = append(updateMask, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_USER_ACK_STATUS, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_FAILURE_REASON_CATEGORY, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_FAILURE_REASON_SUB_CATEGORY)
			verificationReq.UserAckStatus = salaryprogramPb.AcknowledgmentStatus_ACKNOWLEDGEMENT_STATUS_UNSPECIFIED
			verificationReq.VerificationFailureReasonCategory = salaryprogramPb.SalaryTxnVerificationFailureReasonCategory_FAILURE_REASON_CATEGORY_UNSPECIFIED
			verificationReq.VerificationFailureReasonSubCategory = salaryprogramPb.SalaryTxnVerificationFailureReasonSubCategory_FAILURE_REASON_SUB_CATEGORY_UNSPECIFIED
		}

		// salary txn employer id and verified by are mandatory for updating the request status to VERIFIED
		if req.GetSalaryTxnEmployerId() == "" || req.GetVerifiedBy() == salaryprogramPb.SalaryTxnVerificationRequestVerifiedBy_SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED {
			logger.Error(ctx, "can't update status to verified, mandatory params txn_emp_id/verified_by are missing", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("can't update status to verified, mandatory params txn_emp_id/verified_by are missing"),
			}, nil
		}
		updateMask = append(updateMask, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_TXN_EMPLOYER_ID, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_SUB_STATUS, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFIED_BY)
		verificationReq.TxnEmployerId = req.GetSalaryTxnEmployerId()
		verificationReq.VerifiedBy = req.GetVerifiedBy()
		// reset the sub-status when request status is updated to VERIFIED except in case of AWAITING EMPLOYMENT UPDATE so that we can nudge the user to confirm the employer
		if req.GetUpdateSubStatusTo() != salaryprogramPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE {
			verificationReq.VerificationSubStatus = salaryprogramPb.SalaryTxnVerificationRequestSubStatus_SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED
		} else {
			verificationReq.VerificationSubStatus = salaryprogramPb.SalaryTxnVerificationRequestSubStatus_REQUEST_SUB_STATUS_AWAITING_EMPLOYMENT_UPDATE
		}

		var activationEntry *salaryprogramPb.SalaryProgramActivationHistory

		// update verification status to VERIFIED and create activation entry in a db txn
		txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
			salaryTxnTimeInIST := verificationReq.GetTxnTimestamp().AsTime().In(datetime.IST)
			// updating verification request with current status check to the make status check assertions thread safe.
			expectedCurrentVerificationStatus := existingVerificationStatus
			if updateErr := s.salaryTxnVerReqDao.UpdateWithCurrentVerificationStatusCheck(txnCtx, verificationReq, updateMask, expectedCurrentVerificationStatus); updateErr != nil {
				return fmt.Errorf("error updating verification request status to VERIFIED, err : %w", updateErr)
			}

			activeTillTime := salaryTxnTimeInIST.Add(s.config.SalaryProgramNewActivationDuration)
			// making roundoff configurable to help testing some cases on non-prod
			if s.config.ShouldRoundOffActivationTillTimeToEndOfDay {
				activeTillTime = datetime.EndOfDay(activeTillTime)
			}
			var createErr error
			activationEntry, createErr = s.actHistoryDao.Create(txnCtx, &salaryprogramPb.SalaryProgramActivationHistory{
				SalaryProgramRegistrationId:    registrationEntry.GetId(),
				ActiveFrom:                     timestampPb.Now(),
				ActiveTill:                     timestampPb.New(activeTillTime),
				SalaryTxnVerificationRequestId: verificationReq.GetId(),
				ActivationAction:               salaryprogramPb.SalaryActivationAction_SALARY_TXN,
				ActivationActionRefId:          verificationReq.GetId(),
			})
			if createErr != nil {
				return fmt.Errorf("error creating activation history entry in db, err : %w", createErr)
			}
			return nil
		})
		if txnErr != nil {
			logger.Error(ctx, "error while updating the status of verification request and creating activation entry", zap.String(logger.REQUEST_ID, verificationReq.GetId()), zap.Error(txnErr))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg(txnErr.Error()),
			}, nil
		}

		// todo (utkarsh) : remove this log few weeks after launch, added just for quick analytics
		logger.Info(ctx, "activation entry created for registration", zap.String(logger.ACTOR_ID_V2, verificationReq.GetActorId()))

		// publish salary txn detection event after salary verification is successful,
		// todo (utkarsh) : consider moving salary txn verification request state update and event publish in an atomic block using transactional outbox.
		_, publishErr := s.salaryTxnDetectionPublisher.Publish(ctx, &salaryprogramEventsPb.SalaryDetectionEvent{
			ExternalOrderId:     verificationReq.GetTxnId(),
			ActorId:             verificationReq.GetActorId(),
			SalaryTxnTime:       verificationReq.GetTxnTimestamp(),
			SalaryDetectionTime: timestampPb.Now(),
		})
		if publishErr != nil {
			logger.Error(ctx, "error publishing salary txn detection event", zap.String(logger.REQUEST_ID, verificationReq.GetId()), zap.Error(publishErr))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg("error publishing salary txn detection event"),
			}, nil
		}

		// publish salary program status update event
		_, publishErr = s.salaryProgramStatusUpdatePublisher.Publish(ctx, &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent{
			ActorId:             verificationReq.GetActorId(),
			SalaryProgramStatus: salaryprogramEventsPb.SalaryProgramStatus_SALARY_PROGRAM_STATUS_ACTIVE,
			UpdatedAt:           verificationReq.GetUpdatedAt(),
			ActivationHistoryId: activationEntry.GetId(),
			StatusMetadata: &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent_ActiveStatusMetadata{
				ActiveStatusMetadata: &salaryprogramEventsPb.SalaryProgramStatusUpdateEvent_ActiveStatusMeta{
					ActivationHistoryId: activationEntry.GetId(),
					ActivationType:      helper.GetSalaryActivationTypeFromActivationAction(activationEntry.GetActivationAction()),
				},
			},
		})
		if publishErr != nil {
			logger.Error(ctx, "error publishing salary program status update event", zap.String(logger.REQUEST_ID, verificationReq.GetId()), zap.Error(publishErr))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg("error publishing program status update event"),
			}, nil
		}

		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			// trigger salary program activation notification
			_ = s.notificationHelper.PublishSalaryNotificationDataEvents(
				epificontext.CloneCtx(ctx), verificationReq.GetActorId(), notification.NotificationMedium_MEDIUM_PUSH_NOTIFICATION,
				notification.NotificationTrigger_SALARY_BENEFITS_ACTIVATED_TRIGGER,
				&notification.TriggerMetadata{
					NotificationVersion: notification.NotificationVersion_VERSION_V0,
					Metadata: &notification.TriggerMetadata_BenefitsActivatedTriggerMetadata{
						BenefitsActivatedTriggerMetadata: &notification.SalaryBenefitsActivatedTriggerMetadata{
							SalaryProgramActivationId: activationEntry.GetId(),
						},
					},
				},
				s.config.SalaryProgramNotifications.SalaryBenefitsActivatedNotification.DelayDurations,
			)

		})
		// nolint:dupl
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			benefitsActiveTill := activationEntry.GetActiveTill().AsTime()
			eventDelayDurations := make([]time.Duration, 0, len(s.config.SalaryProgramNotifications.SalaryBenefitsInactivatedNotification.DelayDurations))

			durationTillBenefitsAreActive := time.Until(benefitsActiveTill)
			if durationTillBenefitsAreActive < -time.Nanosecond {
				logger.Error(epificontext.CloneCtx(ctx), "durationTillBenefitsAreActive is invalid", zap.Duration("durationTillBenefitsAreActive", durationTillBenefitsAreActive),
					zap.Time("benefitsActiveTill", benefitsActiveTill), zap.String("activationId", activationEntry.GetId()),
				)
				return
			}

			// adding configured delays to the expected delay of the msg, i.e. duration after which the benefits will get inactivated
			for _, delayDuration := range s.config.SalaryProgramNotifications.SalaryBenefitsInactivatedNotification.DelayDurations {
				eventDelayDurations = append(eventDelayDurations, durationTillBenefitsAreActive+delayDuration)
			}

			// trigger salary program inactivation notification
			_ = s.notificationHelper.PublishSalaryNotificationDataEvents(
				epificontext.CloneCtx(ctx), verificationReq.GetActorId(), notification.NotificationMedium_MEDIUM_PUSH_NOTIFICATION,
				notification.NotificationTrigger_SALARY_BENEFITS_INACTIVATED_TRIGGER,
				&notification.TriggerMetadata{
					NotificationVersion: notification.NotificationVersion_VERSION_V0,
					Metadata: &notification.TriggerMetadata_BenefitsInactivatedTriggerMetadata{
						BenefitsInactivatedTriggerMetadata: &notification.SalaryBenefitsInactivatedTriggerMetadata{
							SalaryProgramActivationId: activationEntry.GetId(),
						},
					},
				},
				eventDelayDurations,
			)

		})

		goroutine.Run(ctx, time.Second*5, func(gctx context.Context) {
			// trigger salary benefits grace period started notification
			gracePeriodStartFrom := verificationReq.GetTxnTimestamp().AsTime().Add(s.config.MinReqDurationSinceLastActivationForShowingGracePeriod)
			durationTillGracePeriodStart := time.Until(gracePeriodStartFrom)

			if durationTillGracePeriodStart < 0 {
				logger.Error(gctx, "durationTillGracePeriodStart is invalid", zap.Duration("durationTillGracePeriodStart", durationTillGracePeriodStart),
					zap.String("activationId", activationEntry.GetId()),
				)
				return
			}
			eventDelayDurations := make([]time.Duration, 0, len(s.config.SalaryProgramNotifications.SalaryBenefitsGracePeriodStartedNotification.DelayDurations))

			// adding configured delays to the expected delay of the msg, i.e. duration after which the benefits grace period start
			for _, delayDuration := range s.config.SalaryProgramNotifications.SalaryBenefitsGracePeriodStartedNotification.DelayDurations {
				eventDelayDurations = append(eventDelayDurations, durationTillGracePeriodStart+delayDuration)
			}

			_ = s.notificationHelper.PublishSalaryNotificationDataEvents(
				gctx, verificationReq.GetActorId(), notification.NotificationMedium_MEDIUM_PUSH_NOTIFICATION,
				notification.NotificationTrigger_SALARY_BENEFITS_GRACE_PERIOD_STARTED_TRIGGER,
				&notification.TriggerMetadata{
					NotificationVersion: notification.NotificationVersion_VERSION_V0,
					Metadata: &notification.TriggerMetadata_SalaryBenefitsGracePeriodStartedTriggerMetadata{
						SalaryBenefitsGracePeriodStartedTriggerMetadata: &notification.SalaryBenefitsGracePeriodStartedTriggerMetadata{
							SalaryProgramActivationId: activationEntry.GetId(),
						},
					},
				},
				eventDelayDurations,
			)
		})

		// publish activation event to rudder for analytics/marketing campaigns
		goroutine.Run(epificontext.CloneCtx(ctx), 5*time.Second, func(gctx context.Context) {
			s.publishSalaryProgramActivationEvent(gctx, verificationReq.GetActorId(), activationEntry)
		})

		metrics.MetricsRecorder.RecordSalaryVerificationReqStatusUpdate(verificationReq.GetRequestSource().String(), verificationReq.GetVerificationStatus().String(), verificationReq.GetVerificationSubStatus().String(), verificationReq.GetVerifiedBy().String())

	case salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED:
		if existingVerificationStatus != salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS {
			logger.Info(ctx, "status update not allowed, salary verification request cannot be updated to VERIFICATION_FAILED state from it's current state", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()), zap.String("currentState", existingVerificationStatus.String()))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusFailedPreconditionWithDebugMsg("status update not allowed, salary verification request cannot be updated to VERIFICATION_FAILED state from it's current state"),
			}, nil
		}

		// verified by is mandatory for updating the status to VERIFICATION_FAILED
		if req.GetVerifiedBy() == salaryprogramPb.SalaryTxnVerificationRequestVerifiedBy_SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED {
			logger.Error(ctx, "can't update status to verification failed, mandatory param verified_by missing", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("can't update status to verification failed, mandatory param verified_by missing"),
			}, nil
		}

		// UI support for adding a failure reason for failed verification is in DEV, so commenting this check for compatibility with current UI.
		// TODO(yuvraj): uncomment this checks once UI support for this check goes live
		// // verified by, verification failure reason category and verification failure reason sub-category are mandatory for updating the status to VERIFICATION_FAILED
		// if req.GetVerifiedBy() == salaryprogramPb.SalaryTxnVerificationRequestVerifiedBy_SALARY_TXN_VERIFICATION_VERIFIED_BY_UNSPECIFIED || req.GetVerificationFailureReasonCategory() == salaryprogramPb.SalaryTxnVerificationFailureReasonCategory_FAILURE_REASON_CATEGORY_UNSPECIFIED ||
		//	req.GetVerificationFailureReasonSubCategory() == salaryprogramPb.SalaryTxnVerificationFailureReasonSubCategory_FAILURE_REASON_SUB_CATEGORY_UNSPECIFIED {
		//	logger.Error(ctx, "can't update status to verification failed, mandatory param verified_by/verification_failure_reason_category/verification_failure_reason_sub_category missing", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()))
		//	return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
		//		Status: rpc.StatusInvalidArgumentWithDebugMsg("can't update status to verification failed, mandatory param verified_by/verification_failure_reason_category/verification_failure_reason_sub_category missing"),
		//	}, nil
		// }

		// check to verify that mapping of verification failure reason category to sub-category exist.
		// if !lo.Contains(s.config.VerificationFailureReasonsCategoryToSubCategoriesMap[req.GetVerificationFailureReasonCategory()], req.GetVerificationFailureReasonSubCategory()) {
		//	logger.Error(ctx, "can't update status to verification failed, passed failure reason category to failure reason sub-category mapping not supported", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()))
		//	return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
		//		Status: rpc.StatusInvalidArgumentWithDebugMsg("can't update status to verification failed, passed failure reason category to failure reason sub-category mapping not supported"),
		//	}, nil
		// }

		// TODO (yuvraj): remove this check once UI support for this check goes live
		// check to verify that mapping of verification failure reason category to sub-category exist.
		if !lo.Contains(s.config.VerificationFailureReasonsCategoryToSubCategoriesMap[req.GetVerificationFailureReasonCategory()], req.GetVerificationFailureReasonSubCategory()) &&
			(req.GetVerificationFailureReasonCategory() != salaryprogramPb.SalaryTxnVerificationFailureReasonCategory_FAILURE_REASON_CATEGORY_UNSPECIFIED ||
				req.GetVerificationFailureReasonSubCategory() != salaryprogramPb.SalaryTxnVerificationFailureReasonSubCategory_FAILURE_REASON_SUB_CATEGORY_UNSPECIFIED) {
			logger.WarnWithCtx(ctx, "failure reason category to failure reason sub-category mapping not supported", zap.String("category", req.GetVerificationFailureReasonCategory().String()), zap.String("sub_category", req.GetVerificationFailureReasonSubCategory().String()), zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()))
		}

		updateMask = append(updateMask, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_SUB_STATUS, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFIED_BY,
			salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_FAILURE_REASON_CATEGORY, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_FAILURE_REASON_SUB_CATEGORY)
		verificationReq.VerifiedBy = req.GetVerifiedBy()
		verificationReq.VerificationFailureReasonCategory = req.GetVerificationFailureReasonCategory()
		verificationReq.VerificationFailureReasonSubCategory = req.GetVerificationFailureReasonSubCategory()
		// reset the sub-status when request status is updated to VERIFICATION_FAILED
		verificationReq.VerificationSubStatus = salaryprogramPb.SalaryTxnVerificationRequestSubStatus_SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED

		// updating verification request with current status check to the make status check assertions thread safe.
		expectedCurrentVerificationStatus := existingVerificationStatus
		if updateErr := s.salaryTxnVerReqDao.UpdateWithCurrentVerificationStatusCheck(ctx, verificationReq, updateMask, expectedCurrentVerificationStatus); updateErr != nil {
			logger.Error(ctx, "error updating manual verification request db entry", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()), zap.Error(updateErr))
			return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg(updateErr.Error()),
			}, nil
		}

		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			// trigger salary txn verification request rejection notification
			_ = s.notificationHelper.PublishSalaryNotificationDataEvents(
				epificontext.CloneCtx(ctx), verificationReq.GetActorId(), notification.NotificationMedium_MEDIUM_PUSH_NOTIFICATION,
				notification.NotificationTrigger_SALARY_TXN_VERIFICATION_REQUEST_REJECTED_TRIGGER,
				&notification.TriggerMetadata{
					NotificationVersion: notification.NotificationVersion_VERSION_V0,
					Metadata: &notification.TriggerMetadata_TxnVerificationReqRejectedTriggerMetadata{
						TxnVerificationReqRejectedTriggerMetadata: &notification.SalaryTxnVerificationRequestRejectedTriggerMetadata{
							SalaryProgramRegistrationId: registrationEntry.GetId(),
						},
					},
				},
				s.config.SalaryProgramNotifications.TxnVerificationReqRejectedNotification.DelayDurations,
			)
		})

		// publish event to rudder for analytics/marketing campaigns
		goroutine.Run(epificontext.CloneCtx(ctx), 5*time.Second, func(gctx context.Context) {
			s.eventsBroker.AddToBatch(gctx, events.NewSalaryTransactionVerificationRequestStatusEvent(verificationReq.GetActorId(), verificationReq.GetVerificationStatus().String(), verificationReq.GetVerificationSubStatus().String(), verificationReq.GetVerifiedBy().String(), verificationReq.GetRequestSource().String()))
		})

		metrics.MetricsRecorder.RecordSalaryVerificationReqStatusUpdate(verificationReq.GetRequestSource().String(), verificationReq.GetVerificationStatus().String(), verificationReq.GetVerificationSubStatus().String(), verificationReq.GetVerifiedBy().String())

	default:
		logger.Error(ctx, "unhandled verification status in update manual salary txn status update req", zap.String(logger.VERIFICATION_STATUS, req.GetUpdateStatusTo().String()))
		return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg("unhandled verification status in update manual salary txn status update req"),
		}, nil
	}

	return &salaryprogramPb.UpdateManualSalaryTxnVerificationStatusResponse{
		Status:                 rpc.StatusOk(),
		UpdatedVerificationReq: verificationReq,
	}, nil
}

// AckSalaryVerificationRequestStatus is useful for marking that a given verification request's status was acknowledged by the user,
// mostly useful for salary verification requests where ack is needed for dismissing the request from the app.
// status acknowledgement is allowed only if the verification request has reached a terminal state (VERIFIED or FAILED).
func (s *Service) AckSalaryVerificationRequestStatus(ctx context.Context, req *salaryprogramPb.AckSalaryVerificationRequestStatusRequest) (*salaryprogramPb.AckSalaryVerificationRequestStatusResponse, error) {
	salaryTxnVerReq, err := s.salaryTxnVerReqDao.GetById(ctx, req.GetSalaryTxnVerRequestId())
	if err != nil {
		logger.Error(ctx, "error fetching salary txn verification request from db", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()), zap.Error(err))
		return &salaryprogramPb.AckSalaryVerificationRequestStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// only verification requests in terminal state can be acknowledged.
	verificationStatus := salaryTxnVerReq.GetVerificationStatus()
	if verificationStatus != salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED && verificationStatus != salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFICATION_FAILED {
		logger.Info(ctx, "ack now allowed, verification request is not in terminal state", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()), zap.String("verificationStatus", salaryTxnVerReq.GetVerificationStatus().String()))
		return &salaryprogramPb.AckSalaryVerificationRequestStatusResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("ack now allowed, verification request is not in terminal state"),
		}, nil
	}

	// update status of verification request to ACKNOWLEDGED
	salaryTxnVerReq.UserAckStatus = salaryprogramPb.AcknowledgmentStatus_ACKNOWLEDGED
	salaryTxnVerificationRequestFieldMask := []salaryprogramPb.SalaryTxnVerificationRequestFieldMask{salaryprogramPb.SalaryTxnVerificationRequestFieldMask_USER_ACK_STATUS}

	if salaryTxnVerReq.GetVerificationStatus() == salaryprogramPb.SalaryTxnVerificationRequestStatus_REQUEST_STATUS_VERIFIED {
		salaryTxnVerReq.VerificationSubStatus = salaryprogramPb.SalaryTxnVerificationRequestSubStatus_SALARY_TXN_VERIFICATION_REQUEST_SUB_STATUS_UNSPECIFIED
		salaryTxnVerificationRequestFieldMask = append(salaryTxnVerificationRequestFieldMask, salaryprogramPb.SalaryTxnVerificationRequestFieldMask_VERIFICATION_SUB_STATUS)
	}

	if updateErr := s.salaryTxnVerReqDao.Update(ctx, salaryTxnVerReq, salaryTxnVerificationRequestFieldMask); updateErr != nil {
		logger.Error(ctx, "error updating salary verification request acknowledgement status", zap.String(logger.REQUEST_ID, req.GetSalaryTxnVerRequestId()), zap.Error(err))
		return &salaryprogramPb.AckSalaryVerificationRequestStatusResponse{
			Status: rpc.StatusInternalWithDebugMsg(updateErr.Error()),
		}, nil
	}

	return &salaryprogramPb.AckSalaryVerificationRequestStatusResponse{Status: rpc.StatusOk()}, nil
}

// GetSalaryTxnVerificationRequests is useful to fetch salary txn verification requests with filters, returns the requests in desc order of their creation time.
func (s *Service) GetSalaryTxnVerificationRequests(ctx context.Context, req *salaryprogramPb.GetSalaryTxnVerificationRequestsRequest) (*salaryprogramPb.GetSalaryTxnVerificationRequestsResponse, error) {
	var (
		actorId     = req.GetFilters().GetActorId()
		actorIdList []string
	)
	pageToken, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.Any("filters", req.GetFilters()), zap.Error(err))
		return &salaryprogramPb.GetSalaryTxnVerificationRequestsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// todo (utkarsh) : remove this check after requestSources filter is migrated to reqSources filter.
	if req.GetFilters().GetReqSource() != salaryprogramPb.SalaryTxnVerificationRequestSource_SALARY_TXN_VERIFICATION_REQUEST_SOURCE_UNSPECIFIED {
		req.GetFilters().ReqSources = append(req.GetFilters().GetReqSources(), req.GetFilters().GetReqSource())
	}

	if actorId != "" {
		actorIdList = append(actorIdList, actorId)
	}
	// get paginated salary txn verification requests from dao
	salaryTxnVerReqList, pageRes, err := s.salaryTxnVerReqDao.GetSalaryTxnVerificationRequestsPaginated(ctx, req.GetFilters().GetId(), actorIdList, req.GetFilters().GetReqSources(), req.GetFilters().GetStatus(), req.GetFilters().GetSubStatuses(), req.GetFilters().GetUserAckStatus(), pageToken, req.GetPageContext().GetPageSize(), req.GetSortOrder(), req.GetFilters().GetFromDate(), req.GetFilters().GetUptoDate(), nil)
	if err != nil {
		logger.Error(ctx, "error fetching salary txn verification requests from dao", zap.Any("filters", req.GetFilters()), zap.Error(err))
		return &salaryprogramPb.GetSalaryTxnVerificationRequestsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &salaryprogramPb.GetSalaryTxnVerificationRequestsResponse{
		Status:                        rpc.StatusOk(),
		SalaryTxnVerificationRequests: salaryTxnVerReqList,
		PageContext:                   pageRes,
	}, nil
}

// GetLatestActivationDetailsActiveAtTime is useful to fetch the activation details due to which salary program was active at a given time.
// If activation_kind is passed as SALARY_PROGRAM_ACTIVATION_KIND_BEST it will fetch the activation with the highest band. If multiple activations are present with the same highest band, the most recent one will be taken.
// If activation_kind is not passed, by default it will fetch the latest activation
//
//	e.g. can pass current time as active_at time to fetch the activation details due to which salary program is currently active.
//	return RecordNotFound status if no activation details are found.
//	NOTE: There are two types of salary activations (FULL_SALARY_ACTIVATION and SALARY_LITE_ACTIVATION) and some fields in the response are specific to either of the activation type
func (s *Service) GetLatestActivationDetailsActiveAtTime(ctx context.Context, req *salaryprogramPb.LatestActivationDetailsActiveAtTimeRequest) (*salaryprogramPb.LatestActivationDetailsActiveAtTimeResponse, error) {
	actHistories, err := s.actHistoryDao.GetByRegIdAndActiveFromTillTime(ctx, req.GetRegistrationId(), req.GetActiveAtTime().AsTime(), req.GetActiveAtTime().AsTime(), 100) // Arbitary high value
	if err != nil {
		logger.Error(ctx, "error fetching activation history entry from db", zap.String(logger.REGISTRATION_ID, req.GetRegistrationId()), zap.Time("active_at_time", req.GetActiveAtTime().AsTime()), zap.Error(err))
		return &salaryprogramPb.LatestActivationDetailsActiveAtTimeResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if len(actHistories) == 0 {
		return &salaryprogramPb.LatestActivationDetailsActiveAtTimeResponse{Status: rpc.StatusRecordNotFound()}, nil
	}

	var actHistory *salaryprogramPb.SalaryProgramActivationHistory
	if req.GetActivationKind() == salaryprogramPb.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_BEST {
		var actErr error
		actHistory, actErr = s.findBestActivation(ctx, actHistories)
		switch {
		case actErr != nil:
			logger.Error(ctx, "error finding best activation (the highest band recent activation)", zap.Error(actErr))
			return &salaryprogramPb.LatestActivationDetailsActiveAtTimeResponse{Status: rpc.StatusInternal()}, nil
		case actHistory == nil:
			logger.Error(ctx, "no best activation (the highest band recent activation) found")
			return &salaryprogramPb.LatestActivationDetailsActiveAtTimeResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
	} else {
		actHistory = actHistories[0]
	}

	latestActivationDetailsRes := &salaryprogramPb.LatestActivationDetailsActiveAtTimeResponse{
		Status:                rpc.StatusOk(),
		ActivationHistoryId:   actHistory.GetId(),
		ActiveFrom:            actHistory.GetActiveFrom(),
		ActiveTill:            actHistory.GetActiveTill(),
		ActivatedAt:           actHistory.GetCreatedAt(),
		ActivationType:        helper.GetSalaryActivationTypeFromActivationAction(actHistory.GetActivationAction()),
		ActivationActionRefId: actHistory.GetActivationActionRefId(),
	}

	// if the salary was activated due SALARY_TXN detection then populate salary txn specific details
	if actHistory.GetActivationAction() == salaryprogramPb.SalaryActivationAction_SALARY_TXN {
		// get details of salary txn which lead to the above activation
		salaryTxnVerReq, getErr := s.salaryTxnVerReqDao.GetById(ctx, actHistory.GetSalaryTxnVerificationRequestId())
		if getErr != nil {
			logger.Error(ctx, "error fetching salary_txn_verification_req for activation entry", zap.String("activationHistoryId", actHistory.GetId()), zap.Error(getErr))
			return &salaryprogramPb.LatestActivationDetailsActiveAtTimeResponse{
				Status: rpc.StatusInternalWithDebugMsg(getErr.Error()),
			}, nil
		}

		order, orderErr := s.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_ExternalId{ExternalId: salaryTxnVerReq.GetTxnId()}})
		if rpcErr := epifigrpc.RPCError(order, orderErr); rpcErr != nil {
			logger.Error(ctx, "error fetching order details for salary txn verification request", zap.String("orderId", salaryTxnVerReq.GetTxnId()), zap.Error(rpcErr))
			return &salaryprogramPb.LatestActivationDetailsActiveAtTimeResponse{
				Status: rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
			}, nil
		}

		latestActivationDetailsRes.SalaryTxnId = salaryTxnVerReq.GetTxnId()
		latestActivationDetailsRes.SalaryTxnTimestamp = salaryTxnVerReq.GetTxnTimestamp()
		latestActivationDetailsRes.SalaryTxnEmployerId = salaryTxnVerReq.GetTxnEmployerId()
		latestActivationDetailsRes.SalaryBand = s.getSalaryBandForAmount(order.GetOrder().GetAmount())
		latestActivationDetailsRes.B2BSalaryBand = s.getB2BSalaryBandForAmount(order.GetOrder().GetAmount())
	}

	return latestActivationDetailsRes, nil
}

// findBestActivation finds the activation with highest band (best activation) from the given activation histories.
// If multiple activations are present with the same highest band, the most recent one will be taken.
func (s *Service) findBestActivation(ctx context.Context, actHistories []*salaryprogramPb.SalaryProgramActivationHistory) (*salaryprogramPb.SalaryProgramActivationHistory, error) {
	salaryTxnVerReqIds := make([]string, 0)
	// create a list of salary txn verification request ids from activation histories which are activated by salary txn
	for _, aH := range actHistories {
		if aH.GetActivationAction() != salaryprogramPb.SalaryActivationAction_SALARY_TXN {
			continue
		}
		if id := aH.GetSalaryTxnVerificationRequestId(); id != "" {
			salaryTxnVerReqIds = append(salaryTxnVerReqIds, id)
		}
	}

	salaryTxnVerReqMap := make(map[string]*salaryprogramPb.SalaryTxnVerificationRequest)
	orderMap := make(map[string]*orderPb.Order)

	if len(salaryTxnVerReqIds) == 0 {
		return nil, fmt.Errorf("no salary_txn_verification_request_id found")
	}

	// get the salary txn verification requests with the collected ids
	salaryTxnVerReqs, idErr := s.salaryTxnVerReqDao.GetByIds(ctx, salaryTxnVerReqIds)
	if idErr != nil {
		logger.Error(ctx, "error fetching salary_txn_verification_reqs for activation entries", zap.Strings("ids", salaryTxnVerReqIds), zap.Error(idErr))
		return nil, idErr
	}
	orderIdentifiers := make([]*orderPb.OrderIdentifier, 0, len(salaryTxnVerReqs))
	// populate salaryTxnVerReqMap with the ids and corresponding salary txn verification requests
	for _, verReq := range salaryTxnVerReqs {
		salaryTxnVerReqMap[verReq.GetId()] = verReq
		orderIdentifiers = append(orderIdentifiers, &orderPb.OrderIdentifier{
			Identifier: &orderPb.OrderIdentifier_ExternalId{ExternalId: verReq.GetTxnId()},
		})
	}
	if len(orderIdentifiers) == 0 {
		return nil, fmt.Errorf("no order_identifiers found")
	}

	// get the orders with the txn id fetched from the salary txn verification requests
	ordersResp, err := s.orderClient.GetOrders(ctx, &orderPb.GetOrdersRequest{
		GetOrderBy: orderIdentifiers,
	})
	if rpcErr := epifigrpc.RPCError(ordersResp, err); rpcErr != nil {
		logger.Error(ctx, "error in GetOrdersWithTransactions rpc while finding the best activation in GetLatestActivationDetailsActiveAtTime", zap.Error(rpcErr), zap.Any("order_identifiers", orderIdentifiers))
		return nil, rpcErr
	}
	// populate orderMap with the external txn id and corresponding order
	for _, order := range ordersResp.GetOrders() {
		orderMap[order.GetExternalId()] = order
	}

	// structure to store activation histiry with its band
	type activationBandPair struct {
		act  *salaryprogramPb.SalaryProgramActivationHistory
		band salaryEnumsPb.B2BSalaryBand
	}
	var activationBandPairs []activationBandPair
	for _, aH := range actHistories {
		if aH.GetActivationAction() != salaryprogramPb.SalaryActivationAction_SALARY_TXN {
			continue
		}
		var band salaryEnumsPb.B2BSalaryBand
		// fetch the salary txn verification request with the id from the map
		salaryTxnVerReq := salaryTxnVerReqMap[aH.GetSalaryTxnVerificationRequestId()]

		// fetch teh order with the txn id from salary txn verification request
		order := orderMap[salaryTxnVerReq.GetTxnId()]

		// get the band of the activation with the amount in the order
		band = s.getB2BSalaryBandForAmount(order.GetAmount())
		activationBandPairs = append(activationBandPairs, activationBandPair{act: aH, band: band})
	}
	if len(activationBandPairs) == 0 {
		return nil, nil
	}
	// find the most recent highest band activation
	best := activationBandPairs[0]
	for _, ba := range activationBandPairs[1:] {
		if ba.band > best.band || (ba.band == best.band && ba.act.GetActiveTill().AsTime().After(best.act.GetActiveTill().AsTime())) {
			best = ba
		}
	}
	return best.act, nil
}

// todo: wrap this in a interface if needed in other places
func (s *Service) getSalaryBandForAmount(amount *money.Money) salaryEnumsPb.SalaryBand {
	for _, amountRange := range s.config.AmountRangeForSalaryBands {
		// min amount is inclusive and max amount is exclusive
		if amountRange.MinAmount <= amount.GetUnits() && amount.GetUnits() < amountRange.MaxAmount {
			return salaryEnumsPb.SalaryBand(salaryEnumsPb.SalaryBand_value[amountRange.SalaryBand])
		}
	}

	return salaryEnumsPb.SalaryBand_SALARY_BAND_UNSPECIFIED
}

func (s *Service) getB2BSalaryBandForAmount(amount *money.Money) salaryEnumsPb.B2BSalaryBand {
	for _, amountRange := range s.config.B2BAmountRangeForSalaryBands {
		// min amount is inclusive and max amount is exclusive
		if amountRange.MinAmount <= amount.GetUnits() && amount.GetUnits() < amountRange.MaxAmount {
			return salaryEnumsPb.B2BSalaryBand(salaryEnumsPb.B2BSalaryBand_value[amountRange.B2BSalaryBand])
		}
	}

	return salaryEnumsPb.B2BSalaryBand_B2B_SALARY_BAND_UNSPECIFIED
}

// GetSalaryProgramActivationHistories is useful to fetch the activation history for a given salaryprogram registration.
func (s *Service) GetSalaryProgramActivationHistories(ctx context.Context, req *salaryprogramPb.GetSalaryProgramActivationHistoriesRequest) (*salaryprogramPb.GetSalaryProgramActivationHistoriesResponse, error) {
	pageToken, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.Any("filters", req.GetPageContext()), zap.Error(err))
		return &salaryprogramPb.GetSalaryProgramActivationHistoriesResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// get paginated salary txn verification requests from dao
	actHistoryList, pageRes, err := s.actHistoryDao.GetActivationHistoriesPaginated(ctx, model.SalaryProgramActivationHistoryQueryFilters{
		SalaryProgramRegId:     req.GetRegistrationId(),
		SalaryActivationAction: helper.GetSalaryActivationActionFromActivationType(req.GetActivationType()),
	}, req.GetSortOrder(), pageToken, req.GetPageContext().GetPageSize())
	if err != nil {
		logger.Error(ctx, "error fetching salaryprogram activation histories from dao", zap.String(logger.REGISTRATION_ID, req.GetRegistrationId()), zap.Error(err))
		return &salaryprogramPb.GetSalaryProgramActivationHistoriesResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// populate activation type from activation action
	for _, actHistory := range actHistoryList {
		actHistory.ActivationType = helper.GetSalaryActivationTypeFromActivationAction(actHistory.GetActivationAction())
	}

	return &salaryprogramPb.GetSalaryProgramActivationHistoriesResponse{
		Status:              rpc.StatusOk(),
		ActivationHistories: actHistoryList,
		PageContext:         pageRes,
	}, nil
}

// GetMinRequiredAmountForSalaryTxnDetection is useful to fetch the min amount required for detecting a txn as salary for an actor.
// This is useful for salary programme B2B partnerships where min salary amount would be governed by the b2b contract.
func (s *Service) GetMinRequiredAmountForSalaryTxnDetection(ctx context.Context, req *salaryprogramPb.MinRequiredAmountForSalaryTxnDetectionRequest) (*salaryprogramPb.MinRequiredAmountForSalaryTxnDetectionResponse, error) {
	minReqSalaryAmount, err := s.minReqSalaryTxnAmtGetter.GetMinReqAmount(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error fetching min required salary txn amount", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &salaryprogramPb.MinRequiredAmountForSalaryTxnDetectionResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &salaryprogramPb.MinRequiredAmountForSalaryTxnDetectionResponse{
		Status: rpc.StatusOk(),
		Amount: minReqSalaryAmount,
	}, nil
}

// GetSalaryProgramActiveUsersCountForEmployer is useful to get the count of salary program active users who received their last salary credit from the given employer.
// Note : data returned by this rpc can be a bit stale as this data is periodically calculated and persisted by a cron job and is not fetched in realtime.
func (s *Service) GetSalaryProgramActiveUsersCountForEmployer(ctx context.Context, req *salaryprogramPb.GetSalaryProgramActiveUsersCountForEmployerRequest) (*salaryprogramPb.GetSalaryProgramActiveUsersCountForEmployerResponse, error) {
	// get time at which the count was last computed
	lastComputedAt, err := s.redisClient.Get(ctx, salaryProgramActiveUsersCntForEmpLastComputedAtRedisKey).Time()
	if err != nil {
		logger.Error(ctx, "error fetching salaryprogram active users info last_computed_at time from redis", zap.Error(err))
		return &salaryprogramPb.GetSalaryProgramActiveUsersCountForEmployerResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching salaryprogram active users info last_computed_at time from redis"),
		}, nil
	}

	activeUsersCountForEmpRedisKey := fmt.Sprintf(salaryProgramActiveUsersCntForEmpRedisKey, req.GetEmployerId())
	activeUsersCount, err := s.redisClient.Get(ctx, activeUsersCountForEmpRedisKey).Int64()
	switch {
	// It implies record is not found in redis, return 0 in such a case as for employers with zro salaryprogram active
	// users, no record would be present in redis.
	case errors.Is(err, redis.Nil):
		return &salaryprogramPb.GetSalaryProgramActiveUsersCountForEmployerResponse{
			Status:         rpc.StatusOk(),
			Count:          0,
			LastComputedAt: timestampPb.New(lastComputedAt),
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching salaryprogram active users info from redis", zap.String("employerId", req.GetEmployerId()), zap.Error(err))
		return &salaryprogramPb.GetSalaryProgramActiveUsersCountForEmployerResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching salaryprogram active users info from redis"),
		}, nil
	}

	return &salaryprogramPb.GetSalaryProgramActiveUsersCountForEmployerResponse{
		Status:         rpc.StatusOk(),
		Count:          activeUsersCount,
		LastComputedAt: timestampPb.New(lastComputedAt),
	}, nil
}

func (s *Service) GetSupportedSalaryVerificationFailureReasons(ctx context.Context, req *salaryprogramPb.GetSupportedSalaryVerificationFailureReasonsRequest) (*salaryprogramPb.GetSupportedSalaryVerificationFailureReasonsResponse, error) {
	var (
		failureReasons    = s.config.VerificationFailureReasonsCategoryToSubCategoriesMap
		failureReasonsRes = make(map[string]*salaryprogramPb.VerificationFailureReasonSubCategories)
	)

	for category, subCategories := range failureReasons {
		failureReasonSubCategoriesNames := make([]string, 0, len(subCategories))
		for _, subCategory := range subCategories {
			failureReasonSubCategoriesNames = append(failureReasonSubCategoriesNames, subCategory.String())
		}
		failureReasonsRes[category.String()] = &salaryprogramPb.VerificationFailureReasonSubCategories{
			SubCategories: failureReasonSubCategoriesNames,
		}
	}
	return &salaryprogramPb.GetSupportedSalaryVerificationFailureReasonsResponse{
		Status:                                   rpc.StatusOk(),
		CategoryToSubcategoriesFailureReasonsMap: failureReasonsRes,
	}, nil
}

func (s *Service) CheckEmployerEligibilityForSalaryProgram(ctx context.Context, req *salaryprogramPb.CheckEmployerEligibilityForSalaryProgramRequest) (*salaryprogramPb.CheckEmployerEligibilityForSalaryProgramResponse, error) {
	empEligibilityStatus, err := s.salaryEmployerVerifier.CheckEmployerEligibilityForSalaryProgram(ctx, req.GetEmployerId())
	if err != nil {
		logger.Error(ctx, "error checking employer eligibility for salary program", zap.String("employerId", req.GetEmployerId()), zap.Error(err))
		return &salaryprogramPb.CheckEmployerEligibilityForSalaryProgramResponse{
			Status: rpc.StatusInternalWithDebugMsg("error checking employer eligibility for salary program"),
		}, nil
	}

	return &salaryprogramPb.CheckEmployerEligibilityForSalaryProgramResponse{
		EligibilityStatus: empEligibilityStatus,
		Status:            rpc.StatusOk(),
	}, nil
}

func (s *Service) publishSalaryProgramActivationEvent(ctx context.Context, actorId string, activationEntry *salaryprogramPb.SalaryProgramActivationHistory) {
	// get current employer info for pushing in the event
	currentEmpRes, err := s.employmentClient.GetEmployerOfUser(ctx, &employmentPb.GetEmployerOfUserRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(currentEmpRes, err); rpcErr != nil {
		logger.WarnWithCtx(ctx, "error fetching current employer of the user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return
	}
	employerInfo := currentEmpRes.GetEmployerInfo()

	activationType := helper.GetSalaryActivationTypeFromActivationAction(activationEntry.GetActivationAction())

	s.eventsBroker.AddToBatch(ctx, events.NewCompletedSalaryActivationEvent(actorId, activationEntry.GetCreatedAt().AsTime(), employerInfo.GetEmployerId(), employerInfo.GetNameBySource(), activationType.String(), activationEntry.GetActivationAction().String(), events.FirstActivationUnknown))
}

func (s *Service) GetRegistrationDetails(ctx context.Context, req *salaryprogramPb.GetRegistrationDetailsRequest) (*salaryprogramPb.GetRegistrationDetailsResponse, error) {
	registration, err := s.registrationDao.GetByActorId(ctx, req.GetActorId(), req.GetFlowType())
	switch {
	// if entry does not exist in registrations table then it implies registration was not initiated.
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return &salaryprogramPb.GetRegistrationDetailsResponse{
			Status:             rpc.StatusOk(),
			RegistrationStatus: salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED,
		}, nil

	case err != nil:
		logger.Error(ctx, "error fetching registration entry for the actor from db", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &salaryprogramPb.GetRegistrationDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// get next stage to completed in the registration flow
	nextToBeCompletedStage, err := s.nextRegStageGetter.GetNextRegistrationStage(ctx, registration)
	if err != nil {
		logger.Error(ctx, "error fetching next to be completed stage", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Error(err))
		return &salaryprogramPb.GetRegistrationDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	if nextToBeCompletedStage != salaryprogramPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_UNSPECIFIED {
		return &salaryprogramPb.GetRegistrationDetailsResponse{
			Status:                     rpc.StatusOk(),
			RegistrationStatus:         salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_INITIATED,
			RegistrationCompletionTime: nil,
			RegistrationId:             registration.GetId(),
		}, nil
	}

	// if nextToBeCompletedStage is UNSPECIFIED it implies all the stages are already COMPLETED
	// we'll fetch the last stage completion time
	currentRegistrationFlowType := registration.GetRegistrationFlowType()
	numStagesInCurrentRegistrationFlow := len(s.config.RegFlowTypeToMandatoryStagesMap[currentRegistrationFlowType])
	lastRegistrationStage := s.config.RegFlowTypeToMandatoryStagesMap[currentRegistrationFlowType][numStagesInCurrentRegistrationFlow-1]

	lastStageDetails, err := s.regStageDetailsDao.GetByRegIdAndStageName(ctx, registration.GetId(), lastRegistrationStage)
	if err != nil {
		logger.Error(ctx, "error fetching last stage details", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Error(err))
		return &salaryprogramPb.GetRegistrationDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &salaryprogramPb.GetRegistrationDetailsResponse{
		Status:                     rpc.StatusOk(),
		RegistrationStatus:         salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
		RegistrationCompletionTime: lastStageDetails.GetUpdatedAt(),
		RegistrationId:             registration.GetId(),
	}, nil
}

// GetSalaryProgramEmployerChannel deprecated, use Employment Service to get salary program channel
func (s *Service) GetSalaryProgramEmployerChannel(ctx context.Context, req *salaryprogramPb.GetSalaryProgramEmployerChannelRequest) (*salaryprogramPb.GetSalaryProgramEmployerChannelResponse, error) {

	employer, getEmployerErr := s.employmentClient.GetEmployer(ctx, &employmentPb.GetEmployerRequest{
		Identifier: &employmentPb.GetEmployerRequest_EmployerId{
			EmployerId: req.GetEmployerId(),
		},
	})
	if employer.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "record not found for given employer", zap.String(logger.EMPLOYER_ID, req.GetEmployerId()))
		return &salaryprogramPb.GetSalaryProgramEmployerChannelResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	if rpcErr := epifigrpc.RPCError(employer, getEmployerErr); rpcErr != nil {
		logger.Error(ctx, "error in GetEmployer rpc", zap.Error(rpcErr))
		return &salaryprogramPb.GetSalaryProgramEmployerChannelResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in GetEmployer rpc"),
		}, nil
	}

	if employer.GetEmployerInfo() == nil {
		logger.Error(ctx, "employer info is nil for this employer")
		return &salaryprogramPb.GetSalaryProgramEmployerChannelResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg("employer info is nil for this employer"),
		}, nil
	}
	employerChannel := salaryprogramPb.SalaryProgramEmployerChannel_EMPLOYER_CHANNEL_B2C
	if employer.GetEmployerInfo().GetSalaryProgramChannel() == employmentPb.EmployerSalaryProgramChannel_B2B {
		employerChannel = salaryprogramPb.SalaryProgramEmployerChannel_EMPLOYER_CHANNEL_B2B
	}

	return &salaryprogramPb.GetSalaryProgramEmployerChannelResponse{
		Status:          rpc.StatusOk(),
		EmployerChannel: employerChannel,
	}, nil
}

func (s *Service) GetSalaryLiteMandateRequests(ctx context.Context, req *salaryprogramPb.GetSalaryLiteMandateRequestsRequest) (*salaryprogramPb.GetSalaryLiteMandateRequestsResponse, error) {
	pageToken, err := pagination.GetPageToken(req.GetPageContextRequest())
	if err != nil {
		logger.Error(ctx, "failed to get page token from page context req", zap.Any("pageContextReq", req.GetPageContextRequest()), zap.Error(err))
		return &salaryprogramPb.GetSalaryLiteMandateRequestsResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to get page token from page context req"),
		}, nil
	}

	// get paginated salaryLiteMandateRequests from dao
	salaryLiteMandateRequests, pageRes, err := s.salaryLiteMandateRequestDao.GetPaginated(ctx, model.SalaryLiteMandateRequestQueryFilters{
		Id:                               req.GetFilters().GetId(),
		ActorId:                          req.GetFilters().ActorId,
		RecurringPaymentId:               req.GetFilters().GetRecurringPaymentId(),
		RequestStatusesIn:                req.GetFilters().GetRequestStatuses(),
		PreferredExecutionDayOfMonthFrom: req.GetFilters().GetPreferredExecutionDayOfMonthFrom(),
		PreferredExecutionDayOfMonthTill: req.GetFilters().GetPreferredExecutionDayOfMonthTill(),
		CreatedFrom:                      req.GetFilters().GetCreatedFrom(),
		CreatedTill:                      req.GetFilters().GetCreateTill(),
	}, req.GetSortOrder(), pageToken, req.GetPageContextRequest().GetPageSize())
	if err != nil {
		logger.Error(ctx, "error in salary lite mandate request GePaginated dao call", zap.String(logger.FILTERS, req.GetFilters().String()), zap.Error(err))
		return &salaryprogramPb.GetSalaryLiteMandateRequestsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in salary lite mandate request GetPaginated dao call"),
		}, nil
	}
	if len(salaryLiteMandateRequests) == 0 {
		return &salaryprogramPb.GetSalaryLiteMandateRequestsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	return &salaryprogramPb.GetSalaryLiteMandateRequestsResponse{
		Status:                    rpc.StatusOk(),
		SalaryLiteMandateRequests: salaryLiteMandateRequests,
		PageContextResponse:       pageRes,
	}, nil
}

func (s *Service) GetSalaryLiteMandateExecutionRequests(ctx context.Context, req *salaryprogramPb.GetSalaryLiteMandateExecutionRequestsRequest) (*salaryprogramPb.GetSalaryLiteMandateExecutionRequestsResponse, error) {
	pageToken, err := pagination.GetPageToken(req.GetPageContextRequest())
	if err != nil {
		logger.Error(ctx, "failed to get page token from page context req", zap.Any("pageContextReq", req.GetPageContextRequest()), zap.Error(err))
		return &salaryprogramPb.GetSalaryLiteMandateExecutionRequestsResponse{
			Status: rpc.StatusInternalWithDebugMsg("failed to get page token from page context req"),
		}, nil
	}

	salaryLiteMandateExecutionRequests, pageCtxRes, err := s.salaryLiteMandateExecutionRequestDao.GetPaginated(ctx, model.SalaryLiteMandateExecutionRequestDaoFilters{
		Id:                 req.GetFilters().GetId(),
		RecurringPaymentId: req.GetFilters().GetRecurringPaymentId(),
		CreatedAtFromTime:  req.GetFilters().GetCreatedAtFromTime(),
		CreatedAtTillTime:  req.GetFilters().GetCreatedAtTillTime(),
		ActorId:            req.GetFilters().GetActorId(),
		MandateRequestId:   req.GetFilters().GetMandateRequestId(),
		RequestStatusIn:    req.GetFilters().GetRequestStatusIn(),
	}, req.GetFilters().GetSortOrder(), pageToken, req.GetPageContextRequest().GetPageSize())
	if err != nil {
		logger.Error(ctx, "error in GetPaginated dao call", zap.Error(err))
		return &salaryprogramPb.GetSalaryLiteMandateExecutionRequestsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in GetPaginated dao call"),
		}, nil
	}
	if len(salaryLiteMandateExecutionRequests) == 0 {
		return &salaryprogramPb.GetSalaryLiteMandateExecutionRequestsResponse{
			Status: rpc.StatusRecordNotFoundWithDebugMsg("record not found in GetPaginated dao"),
		}, nil
	}

	return &salaryprogramPb.GetSalaryLiteMandateExecutionRequestsResponse{
		Status:                             rpc.StatusOk(),
		SalaryLiteMandateExecutionRequests: salaryLiteMandateExecutionRequests,
		PageContextResponse:                pageCtxRes,
	}, nil
}

// nolint:funlen
func (s *Service) InitiateSalaryLiteMandate(ctx context.Context, req *salaryprogramPb.InitiateSalaryLiteMandateRequest) (*salaryprogramPb.InitiateSalaryLiteMandateResponse, error) {
	var (
		toPi        *paymentInstrumentPb.PaymentInstrument
		fromPi      *paymentInstrumentPb.PaymentInstrument
		fromActorId string
		toActorName string
		// Fi user is to actor here
		toActorId             = req.GetActorId()
		secureAccountNumber   = mask.GetMaskedAccountNumber(req.GetAccountNumber(), "x")
		remitterSimulatedIfsc = s.config.SalaryLiteConfig.SalaryLiteMandateNonProdSimulatedRemitterIfscCode
	)

	accountOperationalStatus, accountFreezeStatus, err := s.fetchAccountOperationalStatusForActor(ctx, toActorId)
	if err != nil {
		logger.Error(ctx, "error while checking the account operational status", zap.Any(logger.ACTOR_ID_V2, toActorId), zap.Error(err))
		return &salaryprogramPb.InitiateSalaryLiteMandateResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while checking the account operational status"),
		}, nil
	}

	if accountOperationalStatus != accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_ACTIVE || accountFreezeStatus != accountEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED {
		return &salaryprogramPb.InitiateSalaryLiteMandateResponse{
			Status: helper.BeMandateCreationFailedAsFiFedAccIsNotActiveStatusError(),
		}, nil
	}

	isNewReqAllowed, checkErr := s.isNewSalaryLiteMandateRequestAllowed(ctx, toActorId)
	if checkErr != nil {
		logger.Error(ctx, "error while checking if new salary lite mandate req allowed for user", zap.Error(checkErr))
		return &salaryprogramPb.InitiateSalaryLiteMandateResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while checking if new salary lite mandate req allowed for user"),
		}, nil
	}
	if !isNewReqAllowed {
		logger.WarnWithCtx(ctx, "new salary lite mandate request is not allowed for the user")
		return &salaryprogramPb.InitiateSalaryLiteMandateResponse{
			Status: rpc.StatusFailedPrecondition(),
		}, nil
	}

	errGroup, gctx := errgroup.WithContext(ctx)
	errGroup.Go(func() error {
		savingsAccountEssentialsRes, err := s.savingsClient.GetSavingsAccountEssentials(gctx, &savingsPb.GetSavingsAccountEssentialsRequest{
			Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
				ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
					ActorId:     toActorId,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		})
		if e := epifigrpc.RPCError(savingsAccountEssentialsRes, err); e != nil {
			logger.Error(gctx, "error in GetSavingsAccountEssentials rpc", zap.Error(e))
			return fmt.Errorf("error in GetSavingsAccountEssentials rpc")
		}

		toPiRes, err := s.piClient.GetPi(gctx, &paymentInstrumentPb.GetPiRequest{
			Type: paymentInstrumentPb.PaymentInstrumentType_BANK_ACCOUNT,
			Identifier: &paymentInstrumentPb.GetPiRequest_AccountRequestParams_{
				AccountRequestParams: &paymentInstrumentPb.GetPiRequest_AccountRequestParams{
					ActualAccountNumber: savingsAccountEssentialsRes.GetAccount().GetAccountNo(),
					IfscCode:            savingsAccountEssentialsRes.GetAccount().GetIfscCode(),
				},
			},
		})
		if e := epifigrpc.RPCError(toPiRes, err); e != nil {
			logger.Error(gctx, "error in GetPi rpc", zap.Error(e))
			return fmt.Errorf("error in GetPi rpc")
		}
		toPi = toPiRes.GetPaymentInstrument()
		return nil
	})
	errGroup.Go(func() error {
		remitterIfsc := req.GetIfscCode()
		if cfg.IsSimulatedEnv(s.config.Application.Environment) || cfg.IsUatEnv(s.config.Application.Environment) {
			remitterIfsc = remitterSimulatedIfsc
		}
		// create from/remitter PI
		createPiRes, err := s.piClient.CreatePi(gctx, &paymentInstrumentPb.CreatePiRequest{
			Type: paymentInstrumentPb.PaymentInstrumentType_BANK_ACCOUNT,
			Identifier: &paymentInstrumentPb.CreatePiRequest_Account{
				Account: &paymentInstrumentPb.Account{
					ActualAccountNumber: req.GetAccountNumber(),
					SecureAccountNumber: secureAccountNumber,
					IfscCode:            remitterIfsc,
					AccountType:         accountsPb.Type_SAVINGS,
					Name:                req.GetAccountHolderName(),
				},
			},
		})
		if e := epifigrpc.RPCError(createPiRes, err); e != nil {
			logger.Error(gctx, "error in CreatePi rpc", zap.Error(e))
			return fmt.Errorf("error in CreatePi rpc")
		}
		fromPi = createPiRes.GetPaymentInstrument()

		actorFromRes, err := s.actorClient.ResolveActorFrom(gctx, &actorPb.ResolveActorFromRequest{
			ActorTo:       toActorId,
			PiFrom:        createPiRes.GetPaymentInstrument().GetId(),
			ActorFromName: req.GetAccountHolderName(),
			Ownership:     commontypes.Ownership_EPIFI_TECH,
		})
		if e := epifigrpc.RPCError(actorFromRes, err); e != nil {
			logger.Error(gctx, "error in ResolveActorFrom rpc", zap.Error(e))
			return fmt.Errorf("error in ResolveActorFrom rpc")
		}
		fromActorId = actorFromRes.GetActorFrom()
		return nil
	})
	errGroup.Go(func() error {
		toUser, err := s.userHelperSvc.GetUserByActorId(gctx, toActorId)
		if err != nil {
			logger.Error(gctx, "error getting user by actor id", zap.Error(err), zap.String(logger.ACTOR_ID_V2, toActorId))
			return fmt.Errorf("error getting user by actor id")
		}
		toActorName = gammanames.BestNameFromProfile(gctx, toUser.GetProfile()).ToString()
		return nil
	})
	if errGrpErr := errGroup.Wait(); errGrpErr != nil {
		return &salaryprogramPb.InitiateSalaryLiteMandateResponse{
			Status: rpc.StatusInternalWithDebugMsg(errGrpErr.Error()),
		}, nil
	}

	if fromPi.GetId() == toPi.GetId() {
		return &salaryprogramPb.InitiateSalaryLiteMandateResponse{
			Status: rpc.NewStatus(
				uint32(pb.InitiateSalaryLiteMandateResponse_MANDATE_SETUP_FAILED_REMITTER_ACCOUNT_IS_FI_FED_SAVING_ACC),
				"mandate setup is failed as remitter account is Fi fed savings account",
				"mandate setup is failed as remitter account is Fi fed savings account",
			),
		}, nil
	}

	createTimelineRes, err := s.timelineClient.Create(ctx, &timeline.CreateRequest{
		PrimaryActorId:   toActorId,
		SecondaryActorId: fromActorId,
		PrimaryActorName: toActorName,
		// from account holder name
		SecondaryActorName: req.GetAccountHolderName(),
		PrimaryActorSource: timeline.TimelineSource_ACCOUNT_NUMBER,
		Ownership:          timeline.Ownership_EPIFI_TECH,
	})
	if rpcErr := epifigrpc.RPCError(createTimelineRes, err); rpcErr != nil {
		logger.Error(ctx, "timelineClient.Create rpc call failed", zap.Error(rpcErr))
		return &salaryprogramPb.InitiateSalaryLiteMandateResponse{
			Status: rpc.StatusInternalWithDebugMsg("timelineClient.Create rpc call failed"),
		}, nil
	}

	// checkAndInitiateSalaryLiteMandateReqWithLock checks if a salaryLiteMandateRequest can be created for the actor after acquiring a distributed lock
	// and creates a request
	nextActionDeeplink, err := s.checkAndInitiateSalaryLiteMandateReqWithLock(ctx, fromPi.GetId(), toPi.GetId(), fromActorId, req)
	if err != nil {
		logger.Error(ctx, "error in initiating salaryLiteMandateRequest with lock", zap.Error(err))
		return &salaryprogramPb.InitiateSalaryLiteMandateResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in initiating salaryLiteMandateRequest with lock"),
		}, nil
	}

	return &salaryprogramPb.InitiateSalaryLiteMandateResponse{
		Status:             rpc.StatusOk(),
		NextActionDeeplink: nextActionDeeplink,
	}, nil
}

// isNewSalaryLiteMandateRequestAllowed checks
// 1. if salary lite mandate already exist for user in IN_PROGRESS/SUCCESS state then we don't allow creation of any new req for the user
func (s *Service) isNewSalaryLiteMandateRequestAllowed(ctx context.Context, actorId string) (bool, error) {
	// todo(yuvraj) check if the existing req is expired
	activeReqStatuses := []salaryprogramPb.SalaryLiteMandateRequestStatus{salaryprogramPb.SalaryLiteMandateRequestStatus_IN_PROGRESS, salaryprogramPb.SalaryLiteMandateRequestStatus_SUCCESS}
	salaryLiteMandateReqs, _, err := s.salaryLiteMandateRequestDao.GetPaginated(ctx, model.SalaryLiteMandateRequestQueryFilters{
		ActorId:           actorId,
		RequestStatusesIn: activeReqStatuses,
	}, salaryprogramPb.SortOrder_DESC, nil, 1)
	if err != nil {
		return false, fmt.Errorf("error fetching salary lite mandate req, err: %w", err)
	}
	if len(salaryLiteMandateReqs) != 0 {
		logger.Info(ctx, "salary lite mandate already exist for user in IN_PROGRESS/SUCCESS state")
		return false, nil
	}
	return true, nil
}

// checkAndInitiateSalaryLiteMandateReqWithLock checks if a salaryLiteMandateRequest can be created for the actor after acquiring a distributed lock
// and creates a request
// nolint:funlen
func (s *Service) checkAndInitiateSalaryLiteMandateReqWithLock(ctx context.Context, fromPiId, toPiId, fromActorId string, req *salaryprogramPb.InitiateSalaryLiteMandateRequest) (*deeplink.Deeplink, error) {
	var (
		toActorId = req.GetActorId()
	)
	// acquire actor level distributed lock to prevent race conditions in this flow.
	lockKey := fmt.Sprintf(salaryLiteMandateReqInitFlowLockKeyTemplate, toActorId)
	lock, err := s.distributedLockManager.GetLock(ctx, lockKey, 5*time.Minute)
	if err != nil {
		return nil, fmt.Errorf("error aquiring distributed lock, err : %w", err)
	}
	defer func() { _ = lock.Release(epificontext.CloneCtx(ctx)) }()

	isNewReqAllowed, err := s.isNewSalaryLiteMandateRequestAllowed(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error while checking if new salary lite mandate req allowed for user", zap.Error(err))
		return nil, fmt.Errorf("error while checking if new salary lite mandate req allowed for user")
	}
	if !isNewReqAllowed {
		logger.WarnWithCtx(ctx, "new salary lite mandate request is not allowed for the user")
		return nil, fmt.Errorf("new salary lite mandate request is not allowed for the user")
	}

	var (
		currTime               = time.Now()
		mandateStartTime       = currTime.Add(s.config.SalaryLiteConfig.MinRequiredDelayForMandateStartTimeSinceCreation)
		mandateEndTime         = *datetime.AddNMonths(&currTime, s.config.SalaryLiteConfig.MandateActiveTimeInMonths)
		mandateCreateReqExpiry = currTime.Add(time.Hour)
		// taking 2X of MandateActiveTimeInMonths for retrying case of failures
		maximumAllowedTxns = 2 * s.config.SalaryLiteConfig.MandateActiveTimeInMonths
		clientRequestId    = uuid.NewString()
	)

	createRecurringPaymentRes, err := s.recurringPaymentServiceClient.CreateRecurringPaymentV1(ctx, &recurringpaymentPb.CreateRecurringPaymentV1Request{
		ClientRequestId: clientRequestId,
		CurrentActorId:  req.GetActorId(),
		Expiry:          timestampPb.New(mandateCreateReqExpiry),
		RecurringPaymentTypeSpecificPayload: &rpPayloadPb.RecurringPaymentTypeSpecificCreationPayload{
			Payload: &rpPayloadPb.RecurringPaymentTypeSpecificCreationPayload_EnachCreationPayload{
				EnachCreationPayload: &rpPayloadPb.EnachCreationPayload{
					AuthorisationMode: req.GetAuthMode(),
				},
			},
		},
		RecurringPaymentDetails: &recurringpaymentPb.RecurringPaymentCreationDetails{
			FromActorId: fromActorId,
			ToActorId:   req.GetActorId(),
			Type:        recurringpaymentPb.RecurringPaymentType_ENACH_MANDATES,
			PiFrom:      fromPiId,
			PiTo:        toPiId,
			Amount:      req.GetAmount(),
			Interval: &types.Interval{
				StartTime: timestampPb.New(mandateStartTime),
				EndTime:   timestampPb.New(mandateEndTime),
			},
			RecurrenceRule: &recurringpaymentPb.RecurrenceRule{
				AllowedFrequency: recurringpaymentPb.AllowedFrequency_AS_PRESENTED,
			},
			MaximumAllowedTxns: int32(maximumAllowedTxns),
			PartnerBank:        commonvgpb.Vendor_FEDERAL_BANK,
			Ownership:          recurringpaymentPb.RecurringPaymentOwnership_EPIFI_TECH,
			Provenance:         recurringpaymentPb.RecurrencePaymentProvenance_USER_APP,
			UiEntryPoint:       recurringpaymentPb.UIEntryPoint_SALARY_LITE,
			AmountType:         recurringpaymentPb.AmountType_MAXIMUM,
		},
	})
	if rpcErr := epifigrpc.RPCError(createRecurringPaymentRes, err); rpcErr != nil {
		return nil, fmt.Errorf("error in CreateRecurringPaymentV1 rpc, clientRequestId: %s err: %w", clientRequestId, rpcErr)
	}

	// creating a record in salaryLiteMandateRequest table
	_, err = s.salaryLiteMandateRequestDao.Create(ctx, &salaryprogramPb.SalaryLiteMandateRequest{
		Id:                           clientRequestId,
		ActorId:                      toActorId,
		RecurringPaymentId:           createRecurringPaymentRes.GetRecurringPaymentId(),
		PreferredExecutionDayOfMonth: uint32(req.GetPreferredExecutionDate().AsTime().In(datetime.IST).Day()),
		Amount:                       req.GetAmount(),
		RequestStatus:                salaryprogramPb.SalaryLiteMandateRequestStatus_IN_PROGRESS,
		Bank:                         req.GetBank(),
		StartDate:                    req.GetPreferredExecutionDate(),
	})
	if err != nil {
		return nil, fmt.Errorf("error in creating salaryLiteMandateRequest db entry, clientRequestId: %s err: %w", clientRequestId, err)
	}
	return createRecurringPaymentRes.GetNextActionDeeplink(), nil
}

func (s *Service) UpdateSalaryLiteMandateRequest(ctx context.Context, req *salaryprogramPb.UpdateSalaryLiteMandateRequestRequest) (*salaryprogramPb.UpdateSalaryLiteMandateRequestResponse, error) {
	updateErr := s.salaryLiteMandateRequestDao.Update(ctx, req.GetSalaryLiteMandateRequest(), req.GetUpdateFieldMasks())
	if updateErr != nil {
		logger.Error(ctx, "error while updating salary lite mandate request db entry", zap.Error(updateErr), zap.String(logger.ID, req.GetSalaryLiteMandateRequest().GetId()))
		return &salaryprogramPb.UpdateSalaryLiteMandateRequestResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while updating salary lite mandate request db entry"),
		}, nil
	}
	return &salaryprogramPb.UpdateSalaryLiteMandateRequestResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) InitiateSalaryLiteMandateExecution(ctx context.Context, req *salaryprogramPb.InitiateSalaryLiteMandateExecutionRequest) (*salaryprogramPb.InitiateSalaryLiteMandateExecutionResponse, error) {
	salaryLiteMandateRequests, _, err := s.salaryLiteMandateRequestDao.GetPaginated(ctx, model.SalaryLiteMandateRequestQueryFilters{
		ActorId: req.GetActorId(),
	}, salaryprogramPb.SortOrder_DESC, nil, 1)
	if err != nil {
		logger.Error(ctx, "error in salary lite mandate request GePaginated dao call", zap.Error(err))
		return &salaryprogramPb.InitiateSalaryLiteMandateExecutionResponse{
			Status: rpc.StatusInternalWithDebugMsg(pkgErr.Wrap(err, "error in salary lite mandate request GetPaginated dao call").Error()),
		}, nil
	}
	if len(salaryLiteMandateRequests) == 0 {
		logger.WarnWithCtx(ctx, "salary lite mandate request not present for the user")
		return &salaryprogramPb.InitiateSalaryLiteMandateExecutionResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("salary lite mandate request not present for the user"),
		}, nil
	}

	mandateReq := salaryLiteMandateRequests[0]
	if mandateReq.GetRequestStatus() != salaryprogramPb.SalaryLiteMandateRequestStatus_SUCCESS {
		logger.WarnWithCtx(ctx, "latest salary lite mandate request is not in SUCCESS state")
		return &salaryprogramPb.InitiateSalaryLiteMandateExecutionResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("latest salary lite mandate request is not in SUCCESS state"),
		}, nil
	}

	// checkAndInitiateSalaryLiteMandateExecutionReqWithLock checks if a salaryLiteMandateRequest can be created for the actor after acquiring a distributed lock
	// and creates a request in db and initiate execution request with recurring payment service
	err = s.checkAndInitiateSalaryLiteMandateExecutionReqWithLock(ctx, req.GetActorId(), mandateReq.GetRecurringPaymentId(), mandateReq.GetAmount(), req.GetProvenance(), mandateReq.GetPreferredExecutionDayOfMonth(), mandateReq.GetStartDate().AsTime(), mandateReq.GetId())
	if err != nil {
		logger.Error(ctx, "error in initiating salaryLiteMandateExecutionRequest with lock", zap.Error(err), zap.String(logger.RECURRING_PAYMENT_ID, mandateReq.GetRecurringPaymentId()), zap.String(logger.PROVENANCE, req.GetProvenance().String()))
		return &salaryprogramPb.InitiateSalaryLiteMandateExecutionResponse{
			Status: rpc.StatusInternalWithDebugMsg(pkgErr.Wrap(err, "error in initiating salaryLiteMandateExecutionRequest with lock").Error()),
		}, nil
	}

	return &salaryprogramPb.InitiateSalaryLiteMandateExecutionResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// checkAndInitiateSalaryLiteMandateExecutionReqWithLock checks if a salaryLiteMandateRequest can be created for the actor after acquiring a distributed lock
// and creates a request in db and initiate execution request with recurring payment service
// nolint:funlen
func (s *Service) checkAndInitiateSalaryLiteMandateExecutionReqWithLock(ctx context.Context, actorId, activeMandateRecurringPaymentId string, amount *money.Money, provenance salaryprogramPb.SalaryLiteMandateExecutionProvenance, preferredExecutionDay uint32, mandateStartDate time.Time,
	mandateRequestId string) error {
	// acquire actor level distributed lock to prevent race conditions in this flow.
	lockKey := fmt.Sprintf(salaryLiteMandateExecutionReqInitFlowLockKeyTemplate, actorId)
	lock, err := s.distributedLockManager.GetLock(ctx, lockKey, 5*time.Minute)
	if err != nil {
		return fmt.Errorf("error aquiring distributed lock, err : %w", err)
	}
	defer func() { _ = lock.Release(epificontext.CloneCtx(ctx)) }()

	isNewReqAllowed, err := s.isNewSalaryLiteMandateExecutionRequestAllowed(ctx, activeMandateRecurringPaymentId, provenance, preferredExecutionDay, mandateStartDate)
	if err != nil {
		return fmt.Errorf("error while checking if new salary lite mandate execution req allowed for user, err: %w", err)
	}
	if !isNewReqAllowed {
		return fmt.Errorf("new salary lite mandate execution request is not allowed for the user")
	}

	clientRequestId := uuid.NewString()
	// cloning context with new timeout since client context cancellation can lead failure off any one of the below dao/rpc calls,
	// which may lead to data inconsistency between recurring payment and salary program service
	newCtx, cancel := context.WithTimeout(epificontext.CloneCtx(ctx), 1*time.Minute)
	defer cancel()

	logger.Info(newCtx, "initiating salary lite mandate execution request", zap.String(logger.CLIENT_REQUEST_ID, clientRequestId), zap.String(logger.PROVENANCE, provenance.String()))

	executeRecurringPaymentRes, execErr := s.recurringPaymentServiceClient.ExecuteRecurringPaymentV1(newCtx, &recurringpaymentPb.ExecuteRecurringPaymentV1Request{
		RecurringPaymentId: activeMandateRecurringPaymentId,
		ClientRequestId:    clientRequestId,
		Amount:             amount,
	})
	if rpcErr := epifigrpc.RPCError(executeRecurringPaymentRes, execErr); rpcErr != nil {
		return fmt.Errorf("recurringPaymentsClient.ExecuteRecurringPaymentV1 call failed, clientRequestId: %s err: %w", clientRequestId, rpcErr)
	}
	_, createErr := s.salaryLiteMandateExecutionRequestDao.Create(newCtx, &salaryprogramPb.SalaryLiteMandateExecutionRequest{
		Id:                 clientRequestId,
		RecurringPaymentId: activeMandateRecurringPaymentId,
		RequestStatus:      salaryprogramPb.SalaryLiteMandateExecutionRequestStatus_EXECUTION_IN_PROGRESS,
		Provenance:         provenance,
		ActorId:            actorId,
		MandateRequestId:   mandateRequestId,
	})
	if createErr != nil {
		return fmt.Errorf("salaryLiteMandateExecutionDao.Create dao call failed, clientRequestId: %s err: %w", clientRequestId, createErr)
	}

	return nil
}

// isNewSalaryLiteMandateExecutionRequestAllowed checks
// 1. if the current time is before the mandate first execution start time, if yes return false, else perform other checks.
// 2. if salary lite mandate execution req already exist for user in IN_PROGRESS state then we don't allow creation of any new req for the user
// 3. provenance specific checks
func (s *Service) isNewSalaryLiteMandateExecutionRequestAllowed(ctx context.Context, activeMandateRecurringPaymentId string, provenance salaryprogramPb.SalaryLiteMandateExecutionProvenance, preferredExecutionDay uint32, mandateStartDate time.Time) (bool, error) {
	if provenance == salaryprogramPb.SalaryLiteMandateExecutionProvenance_PROVENANCE_SCHEDULED_JOB {
		if time.Now().Add(datetime.HoursInADay).Before(mandateStartDate) {
			logger.WarnWithCtx(ctx, "mandate not executing as mandate start date has not reached yet", zap.Any("mandate_start_date", mandateStartDate), zap.String(logger.PROVENANCE, provenance.String()))
			return false, nil
		}
	} else if time.Now().Before(mandateStartDate) {
		logger.WarnWithCtx(ctx, "mandate not executing as mandate start date has not reached yet", zap.Any("mandate_start_date", mandateStartDate), zap.String(logger.PROVENANCE, provenance.String()))
		return false, nil
	}

	salaryLiteMandateExecutionReqs, _, err := s.salaryLiteMandateExecutionRequestDao.GetPaginated(ctx, model.SalaryLiteMandateExecutionRequestDaoFilters{
		RecurringPaymentId: activeMandateRecurringPaymentId,
	}, salaryprogramPb.SortOrder_DESC, nil, 1)
	if err != nil {
		return false, fmt.Errorf("error in salary lite mandate execution dao GetPaginated method, err: %w", err)
	}

	// there should not be any existing in progress mandate execution req for the user
	if len(salaryLiteMandateExecutionReqs) != 0 && salaryLiteMandateExecutionReqs[0].GetRequestStatus() == salaryprogramPb.SalaryLiteMandateExecutionRequestStatus_EXECUTION_IN_PROGRESS {
		return false, fmt.Errorf("mandate execution request already present in IN_PROGRESS state for the user, id: %s", salaryLiteMandateExecutionReqs[0].GetId())
	}

	if provenance == salaryprogramPb.SalaryLiteMandateExecutionProvenance_PROVENANCE_SCHEDULED_JOB {
		isNewReqAllowed, checkErr := s.isNewSalaryLiteMandateExecutionAllowedForReqInitiateFromScheduledJob(ctx, activeMandateRecurringPaymentId, preferredExecutionDay)
		if checkErr != nil {
			return false, checkErr
		}
		if !isNewReqAllowed {
			return false, nil
		}
	}

	return true, nil
}

func (s *Service) isNewSalaryLiteMandateExecutionAllowedForReqInitiateFromScheduledJob(ctx context.Context, activeMandateRecurringPaymentId string, preferredExecutionDay uint32) (bool, error) {
	var (
		currTimeInIst     = time.Now().In(datetime.IST)
		lastExecutionDate = time.Date(currTimeInIst.Year(), currTimeInIst.Month(), int(preferredExecutionDay), 0, 0, 0, 0, datetime.IST)
	)
	if lastExecutionDate.After(currTimeInIst) {
		lastExecutionDate = lastExecutionDate.AddDate(0, -1, 0)
	}

	salaryLiteMandateExecutionReqs, _, err := s.salaryLiteMandateExecutionRequestDao.GetPaginated(ctx, model.SalaryLiteMandateExecutionRequestDaoFilters{
		RecurringPaymentId: activeMandateRecurringPaymentId,
		CreatedAtFromTime:  timestampPb.New(lastExecutionDate),
		Provenance:         salaryprogramPb.SalaryLiteMandateExecutionProvenance_PROVENANCE_SCHEDULED_JOB,
	}, salaryprogramPb.SortOrder_DESC, nil, 1)
	if err != nil {
		return false, fmt.Errorf("error in salary lite mandate execution dao GetPaginated method, err: %w", err)
	}
	if len(salaryLiteMandateExecutionReqs) != 0 {
		return false, fmt.Errorf("mandate execution request already present for the cycle")
	}
	return true, nil
}

type userSalaryStates struct {
	isUserFullSalaryProgramActive bool
	isUserSalaryLiteProgramActive bool
	hasUserCompletedSalaryLiteReg bool
	userSalaryActiveFrom          *timestampPb.Timestamp
}

// isEligibleForSalaryPromoWidget checks whether the user is eligible for displaying salary widget or not
// User is eligible iff
//  1. User is not a Fi lite user
//  2. User is part of some selected segments
//
// Return arguments:
//
//	bool -> isEligible
//	string -> reason if not eligible
//	error -> error, if any
//
//nolint:dupl
func (s *Service) isEligibleForSalaryPromoWidget(ctx context.Context, actorId string) (bool, string, error, tieringExtPb.Tier, string) {

	// Fi lite user check
	getFeatureDetailsRes, getFeatureDetailsResErr := s.onboardingClient.GetFeatureDetails(ctx, &onboardingPb.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onboardingPb.Feature_FEATURE_FI_LITE,
	})
	if rpcErr := epifigrpc.RPCError(getFeatureDetailsRes, getFeatureDetailsResErr); rpcErr != nil {
		return false, "", fmt.Errorf("error in GetFeatureDetails rpc: %w", rpcErr), tieringExtPb.Tier_TIER_UNSPECIFIED, ""
	}
	if getFeatureDetailsRes.GetIsFiLiteUser() {
		return false, "widget is not for Fi lite users", nil, tieringExtPb.Tier_TIER_UNSPECIFIED, ""
	}

	promoWidgetUseCaseType, toDisplayPromoWidget, promoWidgetUseCaseErr := s.getPromoWidgetUseCaseType(ctx, actorId)
	if promoWidgetUseCaseErr != nil {
		logger.Error(ctx, "error in getting promo widget use case type", zap.Error(promoWidgetUseCaseErr))
		return false, "error in getting promo widget use case type", promoWidgetUseCaseErr, tieringExtPb.Tier_TIER_UNSPECIFIED, ""
	}
	if !toDisplayPromoWidget && promoWidgetUseCaseErr == nil {
		// returning success status with no promo widgets as we don't want to show promo widget
		return false, "", nil, tieringExtPb.Tier_TIER_UNSPECIFIED, ""
	}

	getTieringPitchResp, getTieringPitchErr := s.tieringClient.GetTieringPitchV2(ctx, &tieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})

	if rpcErr := epifigrpc.RPCError(getTieringPitchResp, getTieringPitchErr); rpcErr != nil {
		return false, "defaulting to false in case of error", fmt.Errorf("error in GetTieringPitchV2 rpc: %w", rpcErr), tieringExtPb.Tier_TIER_UNSPECIFIED, ""
	}

	currentTier := getTieringPitchResp.GetCurrentTier()
	if currentTier.IsSalaryTier() || currentTier == tieringExtPb.Tier_TIER_FI_SALARY_LITE {
		return false, "user is in salary or salary lite tier", nil, currentTier, ""
	}

	// Disabling this since we are not promoting infinite temporarily
	// enabling salary promo widget for 50% of the users
	// hashBucket := pkgHash.Hash(actorId, 2)
	// if hashBucket == 1 {
	//	return false, "actorId hash does not belong to salary bucket", nil
	// }

	return true, "", nil, currentTier, promoWidgetUseCaseType
}

func (s *Service) FetchDynamicElements(ctx context.Context, fetchDynamicElementsRequest *dynamicElementsPb.FetchDynamicElementsRequest) (*dynamicElementsPb.FetchDynamicElementsResponse, error) {
	actorId := fetchDynamicElementsRequest.GetActorId()

	// only show promo widget for PRIMARY and SECONDARY sections
	if fetchDynamicElementsRequest.GetClientContext().GetHomeInfo().GetSection() != dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY &&
		fetchDynamicElementsRequest.GetClientContext().GetHomeInfo().GetSection() != dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY &&
		fetchDynamicElementsRequest.GetClientContext().GetHomeInfo().GetSection() != dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_GTM_POPUP {
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	var dynamicElementList = make([]*dynamicElementsPb.DynamicElement, 0)

	if segmentPromoWidget := s.getPromoWidgetDisplayConfigForSegment(ctx, actorId); segmentPromoWidget != nil {
		isEligible, _, eligibilityCheckErr, _ := s.isEligibleForSegmentPromoWidget(ctx, actorId, segmentPromoWidget)
		if eligibilityCheckErr != nil {
			logger.Error(ctx, "error in checking eligibility for aa salary promo widget", zap.Error(eligibilityCheckErr))
		}
		if isEligible {
			aaSalaryPromoWidget, aaSalaryPromoWidgetErr := s.getDynamicElementForAASalaryProgramPromoWidget(ctx, fetchDynamicElementsRequest)
			if aaSalaryPromoWidgetErr != nil {
				logger.Error(ctx, "error in getting DynamicElementListForSalaryProgramPromoWidget", zap.Error(aaSalaryPromoWidgetErr))
				return &dynamicElementsPb.FetchDynamicElementsResponse{
					Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in getting DynamicElementListForAASalaryProgramPromoWidget : %v", aaSalaryPromoWidgetErr)),
				}, nil
			}
			if aaSalaryPromoWidget != nil {
				dynamicElementList = append(dynamicElementList, aaSalaryPromoWidget)
			}
		}
	}

	if !s.config.PromoWidgetDisplayFlag {
		return nil, nil
	}

	isEligible, _, eligibilityCheckErr, currentTier, promoWidgetUseCaseType := s.isEligibleForSalaryPromoWidget(ctx, actorId)
	if eligibilityCheckErr != nil {
		logger.Error(ctx, "error in checking eligibility for salary promo widget", zap.Error(eligibilityCheckErr))
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in checking eligibility for salary promo widget"),
		}, nil
	}

	if isEligible && len(dynamicElementList) == 0 {
		switch {
		case currentTier == tieringExtPb.Tier_TIER_FI_INFINITE || currentTier.IsAaSalaryTier():
			fullSalOrSalLitePromoWidgetList, getDynamicElementsErr := s.getDynamicElementListForSalaryProgramPromoWidget(ctx, fetchDynamicElementsRequest.GetActorId(), fetchDynamicElementsRequest.GetClientContext().GetHomeInfo().GetSection(), promoWidgetUseCaseType)
			if getDynamicElementsErr != nil && errors.Is(getDynamicElementsErr, epifierrors.ErrRecordNotFound) {
				return &dynamicElementsPb.FetchDynamicElementsResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil
			}

			if getDynamicElementsErr != nil {
				logger.Error(ctx, "error in getting DynamicElementListForSalaryProgramPromoWidget", zap.Error(getDynamicElementsErr))
				return &dynamicElementsPb.FetchDynamicElementsResponse{
					Status: rpc.StatusInternalWithDebugMsg("error in getting DynamicElementListForSalaryProgramPromoWidget"),
				}, nil
			}

			dynamicElementList = append(dynamicElementList, fullSalOrSalLitePromoWidgetList)
		case currentTier.IsSalaryTier() || currentTier == tieringExtPb.Tier_TIER_FI_SALARY_LITE:
			// no dynamic elements for salary or salary lite tier
		default: // show aa salary promo widget if user is not on infinite, aa salary or salary tiers
			aaSalaryPromoWidget, aaSalaryPromoWidgetErr := s.getDynamicElementForAASalaryProgramPromoWidget(ctx, fetchDynamicElementsRequest)
			if aaSalaryPromoWidgetErr != nil {
				logger.Error(ctx, "error in getting DynamicElementListForSalaryProgramPromoWidget", zap.Error(aaSalaryPromoWidgetErr))
				return &dynamicElementsPb.FetchDynamicElementsResponse{
					Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error in getting DynamicElementListForAASalaryProgramPromoWidget : %v", aaSalaryPromoWidgetErr)),
				}, nil
			}
			if aaSalaryPromoWidget != nil {
				dynamicElementList = append(dynamicElementList, aaSalaryPromoWidget)
			}
		}
	}

	aaSalaryGtmPopup, aaSalaryGtmPopupErrr := s.getDynamicElementForAaSalaryOffAppActivation(ctx, fetchDynamicElementsRequest)
	if aaSalaryGtmPopupErrr != nil {
		logger.Error(ctx, "error in getDynamicElementForAaSalaryOffAppActivation", zap.Error(aaSalaryGtmPopupErrr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in getting getDynamicElementForAaSalaryOffAppActivation"),
		}, nil
	}

	if aaSalaryGtmPopup != nil {
		dynamicElementList = append(dynamicElementList, aaSalaryGtmPopup)
	}

	return &dynamicElementsPb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: dynamicElementList,
	}, nil
}

func (s *Service) getPromoWidgetUseCaseType(ctx context.Context, actorId string) (string, bool, error) {

	employerInfo, err := s.userHelperSvc.GetCurrentEmployerInfoOfActor(ctx, actorId)
	// if employer / employer info does not exists for user then we consider them as B2C
	if err != nil && err != epifierrors.ErrRecordNotFound {
		logger.Error(ctx, "error in getting current employer of user", zap.Error(err))
		return "", false, fmt.Errorf("error in getting current employer of user")
	}
	if employerInfo.GetSalaryProgramChannel() != employmentPb.EmployerSalaryProgramChannel_B2B {
		logger.Info(ctx, "promo widget not enabled for non B2B users")
		return "", false, nil
	}
	// get salary program registration status for the actor
	registrationStatusRes, err := s.GetCurrentRegStatusAndNextRegStage(ctx, &salaryprogramPb.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if err != nil || !registrationStatusRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error while fetching salary registration status for actor", zap.Any(logger.RPC_STATUS, registrationStatusRes.GetStatus()), zap.Error(err))
		return "", false, fmt.Errorf("error while fetching salary registration status for actor")
	}
	registrationStatus := registrationStatusRes.GetRegistrationStatus()
	var salaryStates *userSalaryStates
	// get activation status if user has completed registration.
	if registrationStatus == salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		salaryStates, err = s.getUserSalaryStates(ctx, actorId, registrationStatusRes.GetRegistrationId())
		if err != nil {
			logger.Error(ctx, "error while fetch user salary states", zap.Error(err), zap.String(logger.REGISTRATION_ID, registrationStatusRes.GetRegistrationId()))
			return "", false, fmt.Errorf("error while fetch user salary states")
		}
	}
	switch {
	case registrationStatus != salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED:
		return RegNotCompleted, true, nil
	case salaryStates.isUserFullSalaryProgramActive:
		return BenefitsActive, false, nil
	// if user has completed reg but is not active.
	default:
		return RegCompletedBenefitsInActive, true, nil
	}
}

func (s *Service) getDynamicElementForAASalaryProgramPromoWidget(ctx context.Context, fetchDynamicElementsRequest *dynamicElementsPb.FetchDynamicElementsRequest) (*dynamicElementsPb.DynamicElement, error) {

	actorId := fetchDynamicElementsRequest.GetActorId()
	homeScreenAdditionalInfoSection := fetchDynamicElementsRequest.GetClientContext().GetHomeInfo().GetSection()

	promoWidgetDisplayUseCaseForAaSalary, currentAASalaryStage, promoWidgetDisplayUseCaseForAaSalaryErr := s.getPromoWidgetDisplayUseCaseForAASalary(ctx, actorId)
	if promoWidgetDisplayUseCaseForAaSalaryErr != nil {
		logger.Error(ctx, "error in getting AA Salary promo widget display config", zap.Error(promoWidgetDisplayUseCaseForAaSalaryErr))
		return nil, fmt.Errorf("failed to get latest aa txn verificatin req, %w", promoWidgetDisplayUseCaseForAaSalaryErr)
	}

	if promoWidgetDisplayUseCaseForAaSalary == "" {
		return nil, nil
	}

	PromoWidgetForAASalary, err := s.getFeatureWidgetFromUseCaseType(ctx, actorId, homeScreenAdditionalInfoSection, promoWidgetDisplayUseCaseForAaSalary)
	if err != nil {
		return nil, fmt.Errorf("error in getting featureWidgetWithThreePoints element, err: %w", err)
	}

	PromoWidgetForAASalary.BizAnalyticsData["current_aa_salarystage"] = currentAASalaryStage.String()

	return PromoWidgetForAASalary, nil
}

func (s *Service) getDynamicElementForAaSalaryOffAppActivation(ctx context.Context, fetchDynamicElementsRequest *dynamicElementsPb.FetchDynamicElementsRequest) (*dynamicElementsPb.DynamicElement, error) {
	if fetchDynamicElementsRequest.GetClientContext().GetScreenName() != deeplink.Screen_HOME || fetchDynamicElementsRequest.GetClientContext().GetHomeInfo().GetSection() != dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_GTM_POPUP {
		return nil, nil
	}

	actorId := fetchDynamicElementsRequest.GetActorId()
	aaTxnVerification, getErr := s.aaSalaryTxnVerificationDao.GetLatestByActorIdAndStatusList(ctx, actorId,
		[]aa.AASalaryTxnVerificationRequestStatus{aa.AASalaryTxnVerificationRequestStatus_REQUEST_STATUS_COMPLETED},
		aa.AASalaryTxnVerificationRequestFieldMask_ID,
		aa.AASalaryTxnVerificationRequestFieldMask_UPDATED_AT,
		aa.AASalaryTxnVerificationRequestFieldMask_SALARY_PROGRAM_REGISTRATIONS_REF_ID,
		aa.AASalaryTxnVerificationRequestFieldMask_TRANSACTION_DETAILS,
		aa.AASalaryTxnVerificationRequestFieldMask_SALARY_BAND,
	)
	if getErr != nil {
		if errors.Is(getErr, epifierrors.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get latest aa txn verificatin req, %w", getErr)
	}

	// show gtm popup only if verification is completed in last 24 hrs
	if time.Since(aaTxnVerification.GetUpdatedAt().AsTime()) > aaSalaryOffAppActivationGtmPopupDurationWindow {
		return nil, nil
	}

	activations, activationErr := s.actHistoryDao.GetByRegIdAndActiveFromTillTime(ctx, aaTxnVerification.GetSalaryProgramRegistrationsRefId(), time.Now(), time.Now(), 1)
	if activationErr != nil {
		if errors.Is(activationErr, epifierrors.ErrRecordNotFound) || len(activations) == 0 {
			logger.Info(ctx, "activation not found for completed aa txn verification", zap.String("aa_txn_verification_id", aaTxnVerification.GetId()))
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get activation, %w", activationErr)
	}

	// show gtm popup only if activation done in last 24 hrs
	if time.Since(activations[0].GetActiveFrom().AsTime()) > aaSalaryOffAppActivationGtmPopupDurationWindow {
		return nil, nil
	}

	if activations[0].GetActivationAction() == salaryprogramPb.SalaryActivationAction_ADD_FUNDS {
		txnId := aaTxnVerification.GetTransactionDetails().GetTransactionDetail()[0].GetTxnId()
		order, orderErr := s.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: txnId}})
		if rpcErr := epifigrpc.RPCError(order, orderErr); rpcErr != nil {
			return nil, fmt.Errorf("failed to get order for txn Id %s, %w", txnId, rpcErr)
		}

		// show gtm pop only for activation via off-app transfer
		if order.GetOrder().GetProvenance() != orderPb.OrderProvenance_EXTERNAL {
			return nil, nil
		}
	}

	getTargetedCommsMappingsResp, getTargetedCommsMappingsErr := s.inAppTargetedCommsClient.GetTargetedCommsMappings(ctx, &tcPb.GetTargetedCommsMappingsRequest{
		MappingDetails: &tcPb.MappingDetails{
			MappingType:     tcPb.MappingType_MAPPING_TYPE_USER,
			MappedValue:     actorId,
			MappedValueMeta: aaSalaryOffAppActivationGtmPopupIdPrefix + activations[0].GetId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getTargetedCommsMappingsResp, getTargetedCommsMappingsErr); rpcErr != nil {
		if getTargetedCommsMappingsResp.GetStatus().IsRecordNotFound() {
			return constructAaSalaryGtmPopup(activations[0].GetId(), aaTxnVerification.GetSalaryBand(), activations[0].GetActivationAction())
		}
		return nil, fmt.Errorf("GetTargetedCommsMappings rpc failed, %w", rpcErr)
	}

	return nil, nil
}

var aaSalaryBandToCashbackPercentIcon = map[salaryEnumsPb.SalaryBand]string{
	salaryEnumsPb.SalaryBand_SALARY_BAND_1: "https://epifi-icons.pointz.in/aa-salary/1-percent-3d-silver.png",
	salaryEnumsPb.SalaryBand_SALARY_BAND_2: "https://epifi-icons.pointz.in/aa-salary/2-percent-3d-silver-2.png",
	salaryEnumsPb.SalaryBand_SALARY_BAND_3: "https://epifi-icons.pointz.in/aa-salary/3-percent-3d-silver-2.png",
}

var aaSalaryBandToCashbackPercent = map[salaryEnumsPb.SalaryBand]string{
	salaryEnumsPb.SalaryBand_SALARY_BAND_1: "1%",
	salaryEnumsPb.SalaryBand_SALARY_BAND_2: "2%",
	salaryEnumsPb.SalaryBand_SALARY_BAND_3: "3%",
}

const aaSalaryOffAppActivationGtmPopupIdPrefix = "aa_salary_off_app_activation_"

func constructAaSalaryGtmPopup(activationId string, band salaryEnumsPb.SalaryBand, activationAction salaryprogramPb.SalaryActivationAction) (*dynamicElementsPb.DynamicElement, error) {
	earnedBenefitsDl, dlErr := earned_benefits.GetEarnedBenefitsDeeplink(tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1)
	if dlErr != nil {
		return nil, fmt.Errorf("failed to get earned benefits deeplink, %w", dlErr)
	}

	title := fmt.Sprintf("%s back activated!", aaSalaryBandToCashbackPercent[band])
	body := "Applicable on all UPI or debit card spends via Fi"
	if activationAction == salaryprogramPb.SalaryActivationAction_ACTIVATION_SCRIPT {
		title = fmt.Sprintf("You’ve been upgraded to %s back", aaSalaryBandToCashbackPercent[band])
		body = fmt.Sprintf("Your monthly transfer amount makes you auto-eligible for %s", user.GetTierDisplayName(tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1))
	}

	return &dynamicElementsPb.DynamicElement{
		OwnerService:  typesPb.ServiceName_SALARY_PROGRAM_SERVICE,
		Id:            aaSalaryOffAppActivationGtmPopupIdPrefix + activationId,
		UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_DEFAULT,
		StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_GTM_POP_UP,
		Content: &dynamicElementsPb.ElementContent{
			Content: &dynamicElementsPb.ElementContent_GtmPopUpBanner{
				GtmPopUpBanner: &dynamicElementsPb.GTMPopUpBanner{
					Body: &dynamicElementsPb.GTMPopUpBanner_BodyLayoutParagraph_{
						BodyLayoutParagraph: &dynamicElementsPb.GTMPopUpBanner_BodyLayoutParagraph{
							Title:              commontypes.GetTextFromStringFontColourFontStyle(title, "#FFFFFF", commontypes.FontStyle_HEADLINE_XL),
							BodyContent:        commontypes.GetTextFromStringFontColourFontStyle(body, "#FFFFFF", commontypes.FontStyle_SUBTITLE_M),
							PopUpVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(aaSalaryBandToCashbackPercentIcon[band], 160, 160),
						},
					},
					BgColour: widgetPb.GetLinearGradientBackgroundColour(45, []*widgetPb.ColorStop{
						{
							Color:          "#0054BE",
							StopPercentage: 0,
						},
						{
							Color:          "#A6D9E9",
							StopPercentage: 80,
						},
					}),
					Ctas: []*dynamicElementsPb.DynamicElementCta{
						{
							Text:            "View rewards",
							BackgroundColor: "#262728",
							Deeplink:        earnedBenefitsDl,
							TextColor:       "#00B899",
						},
					},
					Deeplink:                   earnedBenefitsDl,
					DismissOnClickOutsidePopUp: true,
				},
			},
		},
		BizAnalyticsData: map[string]string{
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "AA_Salary_" + band.String(),
		},
		EndTime: timestampPb.New(datetime.EndOfDay(time.Now())),
	}, nil
}

func (s *Service) getDynamicElementListForSalaryProgramPromoWidget(ctx context.Context, actorId string, homeScreenAdditionalInfoSection dynamicElementsPb.HomeScreenAdditionalInfo_Section, promoWidgetUseCaseType string) (*dynamicElementsPb.DynamicElement, error) {

	featureWidget, err := s.getFeatureWidgetFromUseCaseType(ctx, actorId, homeScreenAdditionalInfoSection, promoWidgetUseCaseType)
	if err != nil {
		return nil, fmt.Errorf("error in getting featureWidgetWithThreePoints element, err: %w", err)
	}

	return featureWidget, nil
}

var userStatusToUseCaseMapThreePoints = map[string]dynamic_ui_element.DynamicUIUsecase{
	BenefitsActive:                   dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_SALARY_B2B_DEFAULT_BENEFITS_ACTIVE_THREE_POINTS,
	RegNotCompleted:                  dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_SALARY_B2B_DEFAULT_REG_NOT_COMPLETED_THREE_POINTS,
	RegCompletedBenefitsInActive:     dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_SALARY_B2B_DEFAULT_REG_COMPLETED_BENEFITS_INACTIVE_THREE_POINTS,
	IncomeEstimationPending:          dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_AA_SALARY_B2C_DEFAULT_INCOME_ESTIMATION_PENDING_THREE_POINTS,
	IncomeEstimatedActivationPending: dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_AA_SALARY_B2C_DEFAULT_INCOME_ESTIMATED_ACTIVATION_PENDING_THREE_POINTS,
}

var userStatusToUseCaseMapFourPoints = map[string]dynamic_ui_element.DynamicUIUsecase{
	BenefitsActive:                   dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_SALARY_B2B_DEFAULT_BENEFITS_ACTIVE_FOUR_POINTS,
	RegNotCompleted:                  dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_SALARY_B2B_DEFAULT_REG_NOT_COMPLETED_FOUR_POINTS,
	RegCompletedBenefitsInActive:     dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_SALARY_B2B_DEFAULT_REG_COMPLETED_BENEFITS_INACTIVE_FOUR_POINTS,
	IncomeEstimationPending:          dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_AA_SALARY_B2C_DEFAULT_INCOME_ESTIMATION_PENDING_FOUR_POINTS,
	IncomeEstimatedActivationPending: dynamic_ui_element.DynamicUIUsecase_DYNAMIC_UI_USECASE_AA_SALARY_B2C_DEFAULT_INCOME_ESTIMATED_ACTIVATION_PENDING_FOUR_POINTS,
}

var screenSectionToPromoWidgetTypeMap = map[dynamicElementsPb.HomeScreenAdditionalInfo_Section]map[string]dynamic_ui_element.DynamicUIUsecase{
	dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY:   userStatusToUseCaseMapThreePoints,
	dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY: userStatusToUseCaseMapFourPoints,
}

func (s *Service) getFeatureWidgetFromUseCaseType(ctx context.Context, actorId string, homeScreenAdditionalInfoSection dynamicElementsPb.HomeScreenAdditionalInfo_Section, promoWidgetUseCaseType string) (*dynamicElementsPb.DynamicElement, error) {

	useCaseMap := screenSectionToPromoWidgetTypeMap[homeScreenAdditionalInfoSection]
	useCase, ok := useCaseMap[promoWidgetUseCaseType]
	if !ok {
		return nil, fmt.Errorf("invalid promoWidgetUseCaseType")
	}
	dynamicUIElementEvaluatorConfig, err := s.dynamicUIElementEvaluatorConfigDao.GetByScreenAndUsecase(ctx, dynamic_ui_element.DynamicUIScreen_DYNAMIC_UI_SCREEN_HOME, useCase)
	if err != nil {
		return nil, fmt.Errorf("error in getting dynamicUIElementEvaluatorConfig, err: %w", err)
	}
	if dynamicUIElementEvaluatorConfig.GetEvaluatorExpressionConfig().GetIsVisible() == false {
		return nil, fmt.Errorf("promo widget not active for user")
	}

	variantName, err := s.evaluateExpressions(ctx, actorId, dynamicUIElementEvaluatorConfig)
	if err != nil && errors.Is(err, NoEligibleVariantErr) {
		return nil, fmt.Errorf("promo widget variant not found for user")
	}
	if err != nil {
		logger.Error(ctx, "error in evaluateExpression", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err),
			zap.Any("evaluator_expressions", dynamicUIElementEvaluatorConfig.GetEvaluatorExpressionConfig().GetEvaluatorExpressions()))
		return nil, fmt.Errorf("error in evaluateExpression")
	}

	dynamicElement, err := s.dynamicUIElementDao.GetByVariantName(ctx, variantName)
	if err != nil {
		return nil, fmt.Errorf("error in getting dynamicUIElement by variant name , err: %w", err)
	}
	return dynamicElement, nil
}

func (s *Service) evaluateExpressions(ctx context.Context, actorId string, dynamicUIElementEvaluatorConfig *dynamicUIElementPb.DynamicUIElementEvaluatorConfig) (string, error) {
	if len(dynamicUIElementEvaluatorConfig.GetEvaluatorExpressionConfig().GetEvaluatorExpressions()) == 0 {
		return "", fmt.Errorf("no expression found")
	}
	for _, evaluatorExpression := range dynamicUIElementEvaluatorConfig.GetEvaluatorExpressionConfig().GetEvaluatorExpressions() {
		result, err := s.expressionEvaluator.EvaluateExpressionUsingGoEvaluate(ctx, &EvaluateExpressionUsingGoEvaluateRequest{
			ActorId:             actorId,
			EvaluatorExpression: evaluatorExpression.GetExpression(),
			Screen:              dynamicUIElementEvaluatorConfig.GetDynamicUiScreen(),
			UseCase:             dynamicUIElementEvaluatorConfig.GetDynamicUiUsecase(),
		})
		if err != nil {
			return "", pkgErr.Wrap(err, "error while evaluating constraint")
		}
		if result {
			return evaluatorExpression.GetVariantName(), nil
		}
	}
	return "", NoEligibleVariantErr
}

// nolint: funlen
func (s *Service) getPromoWidgetDisplayConfig(ctx context.Context, actorId string) (*genconf.PromoWidgetDisplayConfig, *userSalaryStates, error) {

	salaryUserSegmentedPromoWidget := s.dyconf.DynamicElementConfig().PromoWidgetConfig().SalaryB2CUserSegmentedPromoWidget()
	salaryUserDefaultPromoWidget := s.dyconf.DynamicElementConfig().PromoWidgetConfig().SalaryB2CUserDefaultPromoWidget()

	employerInfo, err := s.userHelperSvc.GetCurrentEmployerInfoOfActor(ctx, actorId)
	// if employer / employer info does not exists for user then we consider them as B2C
	if err != nil && err != epifierrors.ErrRecordNotFound {
		logger.Error(ctx, "error in getting current employer of user", zap.Error(err))
		return nil, nil, fmt.Errorf("error in getting current employer of user")
	}

	// if employer exists then checking if it is from B2B channel
	if employerInfo.GetSalaryProgramChannel() == employmentPb.EmployerSalaryProgramChannel_B2B {
		salaryUserSegmentedPromoWidget = s.dyconf.DynamicElementConfig().PromoWidgetConfig().SalaryB2BUserSegmentedPromoWidget()
		salaryUserDefaultPromoWidget = s.dyconf.DynamicElementConfig().PromoWidgetConfig().SalaryB2BUserDefaultPromoWidget()
	}

	// getting the segment wise PromoWidgetSalaryStatesConfig
	type SegmentWithRank struct {
		SegmentId string
		Rank      int
	}
	segmentWithRankList := []*SegmentWithRank{}
	salaryUserSegmentedPromoWidget.Range(func(segmentId string, value *genconf.PromoWidgetSalaryStatesConfig) bool {
		segmentWithRankList = append(segmentWithRankList, &SegmentWithRank{
			SegmentId: segmentId,
			Rank:      value.Rank(),
		})
		return true
	})
	sort.Slice(segmentWithRankList, func(i, j int) bool {
		return segmentWithRankList[i].Rank < segmentWithRankList[j].Rank
	})

	segmentIds := []string{}
	for _, segmentWithRank := range segmentWithRankList {
		segmentIds = append(segmentIds, segmentWithRank.SegmentId)
	}

	var promoWidgetSalaryStatesConfig *genconf.PromoWidgetSalaryStatesConfig
	if len(segmentIds) > 0 {
		isMember, isMemberErr := s.segmentationServiceClient.IsMember(ctx, &segmentPb.IsMemberRequest{
			ActorId:    actorId,
			SegmentIds: segmentIds,
			LatestBy:   timestampPb.Now(),
		})
		if rpcErr := epifigrpc.RPCError(isMember, isMemberErr); rpcErr != nil {
			logger.Error(ctx, "error in IsMember rpc", zap.Error(rpcErr))
			return nil, nil, fmt.Errorf("error in IsMember rpc")
		}

		for _, segmentId := range segmentIds {
			if segmentMembership, ok := isMember.GetSegmentMembershipMap()[segmentId]; ok {
				if segmentMembership.GetIsActorMember() {
					promoWidgetSalaryStatesConfig = salaryUserSegmentedPromoWidget.Get(segmentId)
					break
				}
			}
		}
	}
	if promoWidgetSalaryStatesConfig == nil {
		promoWidgetSalaryStatesConfig = salaryUserDefaultPromoWidget
	}

	// get salary program registration status for the actor
	registrationStatusRes, err := s.GetCurrentRegStatusAndNextRegStage(ctx, &salaryprogramPb.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if err != nil || !registrationStatusRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error while fetching salary registration status for actor", zap.Any(logger.RPC_STATUS, registrationStatusRes.GetStatus()), zap.Error(err))
		return nil, nil, fmt.Errorf("error while fetching salary registration status for actor")
	}
	registrationStatus := registrationStatusRes.GetRegistrationStatus()

	var salaryStates *userSalaryStates
	// get activation status if user has completed registration.
	if registrationStatus == salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		salaryStates, err = s.getUserSalaryStates(ctx, actorId, registrationStatusRes.GetRegistrationId())
		if err != nil {
			logger.Error(ctx, "error while fetch user salary states", zap.Error(err), zap.String(logger.REGISTRATION_ID, registrationStatusRes.GetRegistrationId()))
			return nil, nil, fmt.Errorf("error while fetch user salary states")
		}
	}

	switch {

	case registrationStatus == salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED:
		return nil, salaryStates, nil

	case registrationStatus != salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED:
		return promoWidgetSalaryStatesConfig.RegNotCompleted(), salaryStates, nil

	case salaryStates.isUserFullSalaryProgramActive:
		return promoWidgetSalaryStatesConfig.BenefitsActive(), salaryStates, nil

	case s.isUserInSalaryLiteGracePeriod(salaryStates.isUserSalaryLiteProgramActive, salaryStates.userSalaryActiveFrom):
		return promoWidgetSalaryStatesConfig.SalaryLiteBenefitsActiveInGracePeriod(), salaryStates, nil

	case salaryStates.isUserSalaryLiteProgramActive:
		return promoWidgetSalaryStatesConfig.SalaryLiteBenefitsActive(), salaryStates, nil

	case salaryStates.hasUserCompletedSalaryLiteReg:
		return promoWidgetSalaryStatesConfig.SalaryLiteRegCompleted(), salaryStates, nil

	// if user has completed reg but is not active.
	default:
		return promoWidgetSalaryStatesConfig.RegCompletedBenefitsInActive(), salaryStates, nil
	}

}

func (s *Service) getPromoWidgetDisplayUseCaseForAASalary(ctx context.Context, actorId string) (string, salaryEnumsPb.AaSalaryStage, error) {
	aaSalarydetailsResp, aaSalarydetailsRespErr := s.GetAaSalaryDetails(ctx, &salaryprogramPb.GetAaSalaryDetailsRequest{ActorId: actorId})
	if aaSalarydetailsRespErr != nil {
		return "", salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_UNSPECIFIED, fmt.Errorf("error while fetching AA salary details : %v for actorId : %v", aaSalarydetailsRespErr, actorId)
	}
	switch aaSalarydetailsResp.GetCurrentStage() {

	// Case when user has not completed registration
	case salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_UNSPECIFIED, salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_INITIATED, salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_ACCOUNT_CONNECTED:
		return IncomeEstimationPending, aaSalarydetailsResp.GetCurrentStage(), nil

	// Case when user has registered with all benefits active
	case salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_INCOME_ESTIMATED, salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_SALARY_COMMITTED:
		return IncomeEstimatedActivationPending, aaSalarydetailsResp.GetCurrentStage(), nil

	// If user is Active or any other unexpected stage
	default:
		return "", salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_ACTIVATED, nil
	}

}

func (s *Service) getPromoWidgetDisplayConfigForAASalary(ctx context.Context, actorId string) (*genconf.PromoWidgetDisplayConfig, salaryEnumsPb.AaSalaryStage, error) {

	aaSalaryUserDefaultPromoWidget := s.dyconf.DynamicElementConfig().PromoWidgetConfig().AASalaryB2CUserDefaultPromoWidget()
	aaSalarydetailsResp, aaSalarydetailsRespErr := s.GetAaSalaryDetails(ctx, &salaryprogramPb.GetAaSalaryDetailsRequest{ActorId: actorId})
	segmentPromoWidgetConfig := s.dyconf.DynamicElementConfig().PromoWidgetConfig().SegmentUserDefaultPromoWidget()
	activeSegments := s.dyconf.DynamicElementConfig().PromoWidgetConfig().ActiveSegmentsForPromoWidgets().ToStringArray()

	if aaSalarydetailsRespErr != nil {
		return nil, salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_UNSPECIFIED, fmt.Errorf("error while fetching AA promo widget config : %v for actorId : %v", aaSalarydetailsRespErr, actorId)
	}

	errGroup, errGroupCtx := errgroup.WithContext(ctx)
	var segmentPromoWidget *genconf.PromoWidgetDisplayConfig

	for _, segment := range activeSegments {
		errGroup.Go(func() error {
			currentSegmentPromoWidgetConfig := segmentPromoWidgetConfig.Get(segment)
			if !currentSegmentPromoWidgetConfig.UserSegmentPromoWidget().IsVisible() {
				return nil
			}
			segmentIds := currentSegmentPromoWidgetConfig.SegmentIdsForUserSegmentPromoWidget().ToStringArray()
			isMemberResp, err := s.segmentationServiceClient.IsMember(errGroupCtx, &segmentPb.IsMemberRequest{
				ActorId:    actorId,
				SegmentIds: segmentIds,
				LatestBy:   timestampPb.Now(),
			})
			if te := epifigrpc.RPCError(isMemberResp, err); te != nil {
				logger.Error(ctx, "error in fetching segment membership details", zap.Error(te))
				return nil
			}
			for _, segmentId := range segmentIds {
				if isMemberResp.GetSegmentMembershipMap()[segmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
					isMemberResp.GetSegmentMembershipMap()[segmentId].GetIsActorMember() {
					segmentPromoWidget = currentSegmentPromoWidgetConfig.UserSegmentPromoWidget()
					return nil
				}
			}
			return nil
		})
	}

	_ = errGroup.Wait()
	if segmentPromoWidget != nil {
		return segmentPromoWidget, aaSalarydetailsResp.GetCurrentStage(), nil
	}

	switch aaSalarydetailsResp.GetCurrentStage() {

	// Case when user has not completed registration
	case salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_UNSPECIFIED, salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_INITIATED, salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_ACCOUNT_CONNECTED:
		return aaSalaryUserDefaultPromoWidget.IncomeEstimationPending(), aaSalarydetailsResp.GetCurrentStage(), nil

	// Case when user has registered with all benefits active
	case salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_INCOME_ESTIMATED, salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_SALARY_COMMITTED:
		return aaSalaryUserDefaultPromoWidget.IncomeEstimatedActivationPending(), aaSalarydetailsResp.GetCurrentStage(), nil

	// If user is Active or any other unexpected stage
	default:
		return nil, salaryEnumsPb.AaSalaryStage_AA_SALARY_STAGE_ACTIVATED, nil
	}
}

func (s *Service) isUserInSalaryLiteGracePeriod(isUserSalaryLiteProgramActive bool, userSalaryActiveFrom *timestampPb.Timestamp) bool {
	return isUserSalaryLiteProgramActive && time.Since(userSalaryActiveFrom.AsTime()) > s.config.MinReqDurationSinceLastActivationForShowingGracePeriod
}

func (s *Service) getUserSalaryStates(ctx context.Context, actorId, salaryRegId string) (*userSalaryStates, error) {
	salaryStates := &userSalaryStates{}
	errGroup, gctx := errgroup.WithContext(ctx)
	errGroup.Go(func() error {
		salaryPgActivationRes, activationErr := s.GetLatestActivationDetailsActiveAtTime(gctx, &salaryprogramPb.LatestActivationDetailsActiveAtTimeRequest{
			RegistrationId: salaryRegId,
			ActiveAtTime:   timestampPb.Now(),
		})
		// activation record not found is not an error scenario. It means that actor is yet to verify the salary txn to activate the benefits
		if activationErr != nil || (!salaryPgActivationRes.GetStatus().IsSuccess() && !salaryPgActivationRes.GetStatus().IsRecordNotFound()) {
			logger.Error(gctx, "error while fetching salary benefits activation details for actor", zap.Any(logger.RPC_STATUS, salaryPgActivationRes.GetStatus()), zap.String(logger.REGISTRATION_ID, salaryRegId), zap.Error(activationErr))
			return fmt.Errorf("error fetching salary benefits activation details")
		}
		if !salaryPgActivationRes.GetStatus().IsRecordNotFound() {
			salaryStates.userSalaryActiveFrom = salaryPgActivationRes.GetActiveFrom()
			if salaryPgActivationRes.GetActivationType() == salaryprogramPb.SalaryActivationType_FULL_SALARY_ACTIVATION {
				salaryStates.isUserFullSalaryProgramActive = true
			} else {
				salaryStates.isUserSalaryLiteProgramActive = true
			}
		}
		return nil
	})
	errGroup.Go(func() error {
		getMandateReqsRes, getMandateReqsErr := s.GetSalaryLiteMandateRequests(gctx, &salaryprogramPb.GetSalaryLiteMandateRequestsRequest{
			Filters: &salaryprogramPb.GetSalaryLiteMandateRequestsRequest_Filters{
				ActorId: actorId,
			},
			PageContextRequest: &rpc.PageContextRequest{
				PageSize: 1,
			},
		})
		if getMandateReqsErr != nil || (!getMandateReqsRes.GetStatus().IsSuccess() && !getMandateReqsRes.GetStatus().IsRecordNotFound()) {
			logger.Error(gctx, "salaryProgramClient.GetSalaryLiteMandateRequests call failed", zap.Any(logger.RPC_STATUS, getMandateReqsRes.GetStatus()), zap.String(logger.REGISTRATION_ID, salaryRegId), zap.Error(getMandateReqsErr))
			return fmt.Errorf("salaryProgramClient.GetSalaryLiteMandateRequests call failed")
		}
		if !getMandateReqsRes.GetStatus().IsRecordNotFound() && getMandateReqsRes.GetSalaryLiteMandateRequests()[0].GetRequestStatus() == salaryprogramPb.SalaryLiteMandateRequestStatus_SUCCESS {
			salaryStates.hasUserCompletedSalaryLiteReg = true
		}
		return nil
	})
	if errGrpErr := errGroup.Wait(); errGrpErr != nil {
		return nil, errGrpErr
	}
	return salaryStates, nil
}

// nolint: funlen
// not used as of now, if used check the icon and text as we are changing from fi-coins to fi-points (from August 1, 2025)
func (s *Service) getFeatureWidgetWithFourPoints(isCarouselVariant bool, promoWidgetDisplayConfig *genconf.PromoWidgetDisplayConfig, programName string) (*dynamicElementsPb.DynamicElement, error) {
	var featureWidgetWithFourPointsCtaDeeplink *deeplink.Deeplink
	if programName == aaSalaryProgram {
		fourPointCtaDeeplink := promoWidgetDisplayConfig.FourPointsWidgetTextVisualElementCardTopSectionConfig().CtaDeeplink()
		if fourPointCtaDeeplink.RewardOfferDetailsScreenOptions().RewardOfferId() != "" {
			var deeplinkErr error
			featureWidgetWithFourPointsCtaDeeplink, deeplinkErr = deeplinkPkg.NewDeeplinkFromV2Config(fourPointCtaDeeplink)
			if deeplinkErr != nil {
				return nil, fmt.Errorf("error in getting deeplink from deeplink cinfig, err: %w", deeplinkErr)
			}
		} else {
			featureWidgetWithFourPointsCtaDeeplink = tiering.AllPlansDeeplink(beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1, true)
		}
	} else {
		var deeplinkErr error
		featureWidgetWithFourPointsCtaDeeplink, deeplinkErr = deeplinkPkg.NewDeeplinkFromV2Config(promoWidgetDisplayConfig.FourPointsWidgetTextVisualElementCardTopSectionConfig().CtaDeeplink())
		if deeplinkErr != nil {
			return nil, fmt.Errorf("error in getting deeplink from deeplink cinfig, err: %w", deeplinkErr)
		}
	}

	featureWidgetWithFourPoints := &dynamicElementsPb.FeatureWidgetWithFourPoints{
		IsCarouselVariant: isCarouselVariant,
		BorderColor:       homeFePb.GetHomeWidgetBorderColor(),
		Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_{
			TextVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard{
				TopSection: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection{
					VisualElement: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: promoWidgetDisplayConfig.FourPointsWidgetTextVisualElementCardTopSectionConfig().Icon(),
								},
							},
						},
					},
				},
				Cta: &ui.IconTextComponent{
					Deeplink: featureWidgetWithFourPointsCtaDeeplink,
					Texts: []*commontypes.Text{
						{
							FontColor: promoWidgetDisplayConfig.FourPointsWidgetTextVisualElementCardTopSectionConfig().CtaFontColor(),
							BgColor:   promoWidgetDisplayConfig.FourPointsWidgetTextVisualElementCardTopSectionConfig().CtaBgColor(),
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: promoWidgetDisplayConfig.FourPointsWidgetTextVisualElementCardTopSectionConfig().CtaText(),
							},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BUTTON_S,
							},
						},
					},
					ContainerProperties: &ui.IconTextComponent_ContainerProperties{
						BgColor: promoWidgetDisplayConfig.FourPointsWidgetTextVisualElementCardTopSectionConfig().ContainerBgColor(),
					},
				},
				BgColour: &ui.BackgroundColour{
					Colour: &ui.BackgroundColour_BlockColour{
						BlockColour: "#FFFFFF",
					},
				},
			},
		},
	}

	indxForFourPointsWidgetTextVisualElementCardMiddleSectionConfigs := [4]string{"Point-1", "Point-2", "Point-3", "Point-4"}
	middleSectionHighlightedPoints := []*dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint{}
	fourPointsWidgetTextVisualElementCardMiddleSectionConfigs := promoWidgetDisplayConfig.FourPointsWidgetTextVisualElementCardMiddleSectionConfigs()
	for indx := 0; indx < 4; indx++ {
		highlightedPoint := &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint{
			LeftIcon: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: fourPointsWidgetTextVisualElementCardMiddleSectionConfigs.Get(indxForFourPointsWidgetTextVisualElementCardMiddleSectionConfigs[indx]).Icon(),
						},
					},
				},
			},
			PreText: &commontypes.Text{
				FontColor: "#929599",
				BgColor:   "#FFFFFF",
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: fourPointsWidgetTextVisualElementCardMiddleSectionConfigs.Get(indxForFourPointsWidgetTextVisualElementCardMiddleSectionConfigs[indx]).PreText(),
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
				},
			},
			Text: &commontypes.Text{
				FontColor: "#313234",
				BgColor:   "#FFFFFF",
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: fourPointsWidgetTextVisualElementCardMiddleSectionConfigs.Get(indxForFourPointsWidgetTextVisualElementCardMiddleSectionConfigs[indx]).Text(),
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
				},
			},
		}
		middleSectionHighlightedPoints = append(middleSectionHighlightedPoints, highlightedPoint)
	}
	featureWidgetWithFourPoints.GetTextVisualElementCard().MiddleSection = &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection{
		HighlightedPoints: middleSectionHighlightedPoints,
	}

	featureWidgetWithFourPoints.Title = &commontypes.Text{
		FontColor: "#313234",
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: promoWidgetDisplayConfig.FourPointsWidgetTitle(),
		},
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
		},
	}
	featureWidgetWithFourPoints.BorderColor = homeFePb.GetHomeWidgetBorderColor()

	dynamicElement := &dynamicElementsPb.DynamicElement{
		OwnerService:  types.ServiceName_SALARY_PROGRAM_SERVICE,
		UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
		BizAnalyticsData: map[string]string{
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): programName,
		},
		Content: &dynamicElementsPb.ElementContent{
			Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
				FeatureWidgetWithFourPoints: featureWidgetWithFourPoints,
			},
		},
	}

	return dynamicElement, nil

}

// nolint: funlen
// not used as of now, if used check the icon and text as we are changing from fi-coins to fi-points (from August 1, 2025)
func (s *Service) getFeatureWidgetWithThreePoints(promoWidgetDisplayConfig *genconf.PromoWidgetDisplayConfig, programName string) (*dynamicElementsPb.DynamicElement, error) {
	var featureWidgetWithThreePointsCtaDeeplink *deeplink.Deeplink
	if programName == aaSalaryProgram {
		threePointLeftVerticalDeeplink := promoWidgetDisplayConfig.ThreePointsWidgetLeftVerticalFlyerConfig().CtaDeeplink()
		if threePointLeftVerticalDeeplink.RewardOfferDetailsScreenOptions().RewardOfferId() != "" {
			var deeplinkErr error
			featureWidgetWithThreePointsCtaDeeplink, deeplinkErr = deeplinkPkg.NewDeeplinkFromV2Config(threePointLeftVerticalDeeplink)
			if deeplinkErr != nil {
				return nil, fmt.Errorf("error in getting deeplink from deeplink cinfig, err: %w", deeplinkErr)
			}
		} else {
			featureWidgetWithThreePointsCtaDeeplink = tiering.AllPlansDeeplink(beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, true)
		}
	} else {
		var deeplinkErr error
		featureWidgetWithThreePointsCtaDeeplink, deeplinkErr = deeplinkPkg.NewDeeplinkFromV2Config(promoWidgetDisplayConfig.ThreePointsWidgetLeftVerticalFlyerConfig().CtaDeeplink())
		if deeplinkErr != nil {
			return nil, fmt.Errorf("error in getting deeplink from deeplink cinfig, err: %w", deeplinkErr)
		}
	}

	var visualElement *commontypes.VisualElement
	var containerProperties *ui.IconTextComponent_ContainerProperties
	var texts = make([]*commontypes.Text, 0)

	if promoWidgetDisplayConfig.ThreePointsWidgetLeftVerticalFlyerConfig().LottieUrl() != "" {
		visualElement = commontypes.GetVisualElementLottieFromUrl(promoWidgetDisplayConfig.ThreePointsWidgetLeftVerticalFlyerConfig().LottieUrl())
		containerProperties = &ui.IconTextComponent_ContainerProperties{
			BgColor: promoWidgetDisplayConfig.ThreePointsWidgetLeftVerticalFlyerConfig().LottieContainerBgColor(),
		}
		texts = append(texts, &commontypes.Text{})
	} else {
		visualElement = commontypes.GetVisualElementImageFromUrl(promoWidgetDisplayConfig.ThreePointsWidgetLeftVerticalFlyerConfig().Icon())
		containerProperties = &ui.IconTextComponent_ContainerProperties{
			BgColor: promoWidgetDisplayConfig.ThreePointsWidgetLeftVerticalFlyerConfig().ContainerBgColor(),
		}
		texts = append(texts, &commontypes.Text{
			FontColor: promoWidgetDisplayConfig.ThreePointsWidgetLeftVerticalFlyerConfig().IconFontColor(),
			BgColor:   promoWidgetDisplayConfig.ThreePointsWidgetLeftVerticalFlyerConfig().IconBgColor(),
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: promoWidgetDisplayConfig.ThreePointsWidgetLeftVerticalFlyerConfig().CtaText(),
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BUTTON_S,
			},
		})
	}

	featureWidgetWithThreePoints := &dynamicElementsPb.FeatureWidgetWithThreePoints{
		BorderColor: homeFePb.GetHomeWidgetBorderColor(),
		LeftVerticalFlyer: &dynamicElementsPb.FeatureWidgetWithThreePoints_LeftVerticalFlyer{
			VisualElement: visualElement,
			Cta: &ui.IconTextComponent{
				Deeplink:            featureWidgetWithThreePointsCtaDeeplink,
				Texts:               texts,
				ContainerProperties: containerProperties,
			},
		},
	}

	indexForThreePointsWidgetRightHorizontalFlyerConfigs := [3]string{"Point-1", "Point-2", "Point-3"}
	rightHorizontalFlyers := []*dynamicElementsPb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{}
	threePointsWidgetRightHorizontalFlyerConfigs := promoWidgetDisplayConfig.ThreePointsWidgetRightHorizontalFlyerConfigs()
	for indx := 0; indx < 3; indx++ {

		var featureWidgetWithThreePointsRightHorizontalFlyerDeeplink *deeplink.Deeplink
		if programName == aaSalaryProgram {
			threePointRightHorizontalDeeplink := threePointsWidgetRightHorizontalFlyerConfigs.Get(indexForThreePointsWidgetRightHorizontalFlyerConfigs[indx]).Deeplink()
			if threePointRightHorizontalDeeplink.RewardOfferDetailsScreenOptions().RewardOfferId() != "" {
				var deeplinkErr error
				featureWidgetWithThreePointsRightHorizontalFlyerDeeplink, deeplinkErr = deeplinkPkg.NewDeeplinkFromV2Config(threePointRightHorizontalDeeplink)
				if deeplinkErr != nil {
					return nil, fmt.Errorf("error in getting deeplink from deeplink cinfig, err: %w", deeplinkErr)
				}
			} else {
				featureWidgetWithThreePointsRightHorizontalFlyerDeeplink = tiering.AllPlansDeeplink(beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, true)
			}
		} else {
			var rightHorizontalFlyerDeeplinkErr error
			featureWidgetWithThreePointsRightHorizontalFlyerDeeplink, rightHorizontalFlyerDeeplinkErr = deeplinkPkg.NewDeeplinkFromV2Config(threePointsWidgetRightHorizontalFlyerConfigs.Get(indexForThreePointsWidgetRightHorizontalFlyerConfigs[indx]).Deeplink())
			if rightHorizontalFlyerDeeplinkErr != nil {
				return nil, fmt.Errorf("error in getting deeplink from deeplink cinfig, err: %w", rightHorizontalFlyerDeeplinkErr)
			}
		}

		rightHorizontalFlyer := &dynamicElementsPb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
			PreText: &commontypes.Text{
				FontColor: "#929599",
				BgColor:   "#FFFFFF",
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: threePointsWidgetRightHorizontalFlyerConfigs.Get(indexForThreePointsWidgetRightHorizontalFlyerConfigs[indx]).PreText(),
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
				},
			},
			Text: &commontypes.Text{
				FontColor: "#313234",
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: threePointsWidgetRightHorizontalFlyerConfigs.Get(indexForThreePointsWidgetRightHorizontalFlyerConfigs[indx]).Text(),
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
				},
			},
			RightIcon: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: threePointsWidgetRightHorizontalFlyerConfigs.Get(indexForThreePointsWidgetRightHorizontalFlyerConfigs[indx]).Icon(),
						},
					},
				},
			},
			Deeplink: featureWidgetWithThreePointsRightHorizontalFlyerDeeplink,
			BgColour: &ui.BackgroundColour{
				Colour: &ui.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
		}
		rightHorizontalFlyers = append(rightHorizontalFlyers, rightHorizontalFlyer)
	}

	featureWidgetWithThreePoints.RightHorizontalFlyers = rightHorizontalFlyers
	featureWidgetWithThreePoints.Title = &commontypes.Text{
		FontColor: "#313234",
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: promoWidgetDisplayConfig.ThreePointsWidgetTitle(),
		},
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
		},
	}
	featureWidgetWithThreePoints.BorderColor = homeFePb.GetHomeWidgetBorderColor()
	dynamicElement := &dynamicElementsPb.DynamicElement{
		OwnerService:  types.ServiceName_SALARY_PROGRAM_SERVICE,
		UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS,
		BizAnalyticsData: map[string]string{
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): programName,
		},
		Content: &dynamicElementsPb.ElementContent{
			Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithThreePoints{
				FeatureWidgetWithThreePoints: featureWidgetWithThreePoints,
			},
		},
	}

	return dynamicElement, nil

}

func (s *Service) DynamicElementCallback(ctx context.Context, dynamicElementCallbackRequest *dynamicElementsPb.DynamicElementCallbackRequest) (*dynamicElementsPb.DynamicElementCallbackResponse, error) {
	if strings.HasPrefix(dynamicElementCallbackRequest.GetElementId(), aaSalaryOffAppActivationGtmPopupIdPrefix) {
		updateTargetedCommsResp, updateTargetedCommsErr := s.inAppTargetedCommsClient.AddTargetedCommsMapping(ctx, &tcPb.AddTargetedCommsMappingRequest{
			TargetedCommsElementId: strings.TrimPrefix(dynamicElementCallbackRequest.GetElementId(), aaSalaryOffAppActivationGtmPopupIdPrefix),
			MappingDetailsList: []*tcPb.MappingDetails{
				{
					MappingType:     tcPb.MappingType_MAPPING_TYPE_USER,
					MappedValue:     dynamicElementCallbackRequest.GetActorId(),
					MappedValueMeta: dynamicElementCallbackRequest.GetElementId(),
				},
			},
		})
		if rpcErr := epifigrpc.RPCError(updateTargetedCommsResp, updateTargetedCommsErr); rpcErr != nil {
			logger.Error(ctx, "failed to add targetted comms mapping", zap.Error(rpcErr))
			return &dynamicElementsPb.DynamicElementCallbackResponse{Status: rpc.StatusInternal()}, nil
		}
	}

	return &dynamicElementsPb.DynamicElementCallbackResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) CreateWhitelistedB2BUsersInBulk(ctx context.Context, req *salaryprogramPb.CreateWhitelistedB2BUsersInBulkRequest) (*salaryprogramPb.CreateWhitelistedB2BUsersInBulkResponse, error) {
	const (
		batchSize = 100
	)

	err := s.whitelistedB2bUserDao.CreateInBatches(ctx, req.GetWhitelistedB2BUsers(), batchSize)
	if err != nil {
		logger.Error(ctx, "whitelistedB2bUserDao.CreateInBatches dao call failed", zap.Error(err), zap.Int("batchSize", batchSize))
		return &salaryprogramPb.CreateWhitelistedB2BUsersInBulkResponse{
			Status: rpc.StatusInternalWithDebugMsg("whitelistedB2bUserDao.CreateInBatches dao call failed"),
		}, nil
	}
	if isSendingCommsToB2BWhitelistedUsersEnabled { // remove this check once vendor for sending comms is evaluated
		sendBulkSMSCommsToWhitelistedB2BUsersErr := s.sendBulkSMSCommsToWhitelistedB2BUsers(ctx, req.GetWhitelistedB2BUsers())
		if sendBulkSMSCommsToWhitelistedB2BUsersErr != nil {
			// sending the bulk sms to users on best effort basis, so only logging the errors but not returning error
			logger.Error(ctx, "error sending bulk sms to whitelisted users", zap.Error(sendBulkSMSCommsToWhitelistedB2BUsersErr))
		}
	}
	return &salaryprogramPb.CreateWhitelistedB2BUsersInBulkResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) IsEligibleForSalaryPromoWidget(ctx context.Context, req *salaryprogramPb.IsEligibleForSalaryPromoWidgetRequest) (*salaryprogramPb.IsEligibleForSalaryPromoWidgetResponse, error) {

	actorId := req.GetActorId()
	isEligible, inEligibilityReason, eligibilityCheckErr, _, _ := s.isEligibleForSalaryPromoWidget(ctx, actorId)
	if eligibilityCheckErr != nil {
		logger.Error(ctx, "error in checking eligibility for salary promo widget", zap.Error(eligibilityCheckErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &salaryprogramPb.IsEligibleForSalaryPromoWidgetResponse{
			Status: rpc.StatusInternalWithDebugMsg("error in checking eligibility for salary promo widget"),
		}, nil
	}
	return &salaryprogramPb.IsEligibleForSalaryPromoWidgetResponse{
		Status:              rpc.StatusOk(),
		IsEligible:          isEligible,
		IneligibilityReason: inEligibilityReason,
	}, nil
}

func (s *Service) sendBulkSMSCommsToWhitelistedB2BUsers(ctx context.Context, whitelistedB2BUsers []*salaryprogramPb.WhitelistedB2BUser) error {
	workerPool, wpErr := concurenttaskmanager.NewWorkerPool(20, len(whitelistedB2BUsers))
	if wpErr != nil {
		return errors.New("error creating workerPool for executing concurrent tasks")
	}
	taskExecutorErr := s.concurrentTaskExecutor.TaskExecutor(ctx, workerPool, &taskexecutor.SendBulkSMSCommsWhiteListedB2BUsers{WhitelistedB2BUser: whitelistedB2BUsers})
	if taskExecutorErr != nil {
		return pkgErr.Wrap(taskExecutorErr, "Error executing task for sending bulk SMS to whitelisted B2B users")
	}
	return nil
}

func (s *Service) fetchAccountOperationalStatusForActor(ctx context.Context, actorId string) (accountEnumsPb.OperationalStatus, accountEnumsPb.FreezeStatus, error) {
	accountId, err := s.getAccountIdForActor(ctx, actorId)
	if err != nil {
		return accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_UNSPECIFIED, accountEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED, fmt.Errorf("error while getting saving accountId for actor")
	}
	return s.getOperationalStatusForAccount(ctx, accountId)
}

// getOperationalStatusForAccount returns operational status of account
func (s *Service) getOperationalStatusForAccount(ctx context.Context, accountId string) (accountEnumsPb.OperationalStatus, accountEnumsPb.FreezeStatus, error) {
	if accountId == "" {
		return accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_UNSPECIFIED, accountEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED, fmt.Errorf("invalid account id %w", epifierrors.ErrInvalidArgument)
	}
	statusRes, statusErr := s.opStatusClient.GetOperationalStatus(ctx, &operationalStatusPb.GetOperationalStatusRequest{
		DataFreshness: operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
		AccountIdentifier: &operationalStatusPb.GetOperationalStatusRequest_SavingsAccountId{
			SavingsAccountId: accountId,
		},
	})
	if err := epifigrpc.RPCError(statusRes, statusErr); err != nil {
		switch {
		case rpc.StatusFromError(err).IsRecordNotFound():
			return accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_UNSPECIFIED, accountEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED, fmt.Errorf("%s, %w", err.Error(), epifierrors.ErrRecordNotFound)
		// this can be extended to have more cases later if needed
		default:
			return accountEnumsPb.OperationalStatus_OPERATIONAL_STATUS_UNSPECIFIED, accountEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED, fmt.Errorf("error while fetching account status from operational status service, %w", err)
		}
	}
	return statusRes.GetOperationalStatusInfo().GetOperationalStatus(), statusRes.GetOperationalStatusInfo().GetFreezeStatus(), nil
}

func (s *Service) getAccountIdForActor(ctx context.Context, actorId string) (string, error) {
	resp, err := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if grpcErr := epifigrpc.RPCError(resp, err); grpcErr != nil {
		return "", fmt.Errorf("error while fetching account details, %w", grpcErr)
	}
	return resp.GetAccount().GetId(), nil
}

func (s *Service) validateAndExtractParams(ctx context.Context, req *salaryprogramPb.CreateRegistrationRequest) (string, salaryprogramPb.SalaryProgramRegistrationAccountType, salaryprogramPb.SalaryProgramRegistrationFlowType, error) {
	if req.GetActorId() == "" || req.GetAccountType() == salaryprogramPb.SalaryProgramRegistrationAccountType_SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_UNSPECIFIED ||
		req.GetRegistrationFlowType() == salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_UNSPECIFIED {
		return "", 0, 0, errors.New("invalid create registration request")
	}
	switch req.GetRegistrationFlowType() {
	case salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_AA_SALARY:
		return req.GetAccountId(), salaryprogramPb.SalaryProgramRegistrationAccountType_SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_AA_SAVINGS_ACC,
			salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_AA_SALARY, nil
	case salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE:
		savingsAccountId, err := s.userHelperSvc.GetSavingsAccountIdOfActor(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "error while fetching savings account id of actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
			return "", 0, 0, fmt.Errorf("invalid create registration request: %v", zap.Error(err))
		}
		return savingsAccountId, salaryprogramPb.SalaryProgramRegistrationAccountType_SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_FI_FED_SAVINGS_ACC,
			salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE, nil
	default:
		return "", 0, 0, errors.New("invalid flow type")
	}
}

// nolint: ineffassign
func (s *Service) CreateRegistrationStageDetails(ctx context.Context, req *salaryprogramPb.CreateRegistrationStageDetailsRequest) (*salaryprogramPb.CreateRegistrationStageDetailsResponse, error) {
	// validate if a registration with given id
	registration, err := s.registrationDao.GetById(ctx, req.GetRegistrationId())
	if err != nil {
		logger.Error(ctx, "error fetching salary program registration entry from db", zap.String(logger.REGISTRATION_ID, req.GetRegistrationId()), zap.Error(err))
		return &salaryprogramPb.CreateRegistrationStageDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	nextStageToBeCompleted, err := s.nextRegStageGetter.GetNextRegistrationStage(ctx, registration)
	if err != nil {
		logger.Error(ctx, "error fetching next to be completed stage", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Any("RegStageToBeCreated", req.GetRegistrationStage()), zap.Error(err))
		return &salaryprogramPb.CreateRegistrationStageDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	if nextStageToBeCompleted != req.GetRegistrationStage() {
		logger.Error(ctx, "next stage to be completed in flow is not stage given in request",
			zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.String("nextStageToCompleted", nextStageToBeCompleted.String()), zap.Any("RegStageToBeCreated", req.GetRegistrationStage()), zap.Error(err))
		return &salaryprogramPb.CreateRegistrationStageDetailsResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("next stage to be completed in flow is not stage given in request"),
		}, nil
	}

	registrationStageDetails, err := s.regStageDetailsDao.GetByRegIdAndStageName(ctx, registration.GetId(), req.GetRegistrationStage())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		registrationStageDetails, err = s.regStageDetailsDao.Create(ctx, &salaryprogramPb.SalaryProgramRegistrationStageDetails{
			SalaryProgramRegistrationId: registration.GetId(),
			StageName:                   req.GetRegistrationStage(),
			StageStatus:                 req.GetTargetStageStatus(),
		})
		if err != nil {
			logger.Error(ctx, "error creating stage details", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Any("RegStageToBeCreated", req.GetRegistrationStage()), zap.Error(err))
			return &salaryprogramPb.CreateRegistrationStageDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	case err != nil:
		logger.Error(ctx, "error fetching stage details by reg id", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Any("RegStageToBeCreated", req.GetRegistrationStage()), zap.Error(err))
		return &salaryprogramPb.CreateRegistrationStageDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil

	case registrationStageDetails != nil && err == nil:
		logger.Error(ctx, "registration stage already exists, update is not implemented", zap.String(logger.REGISTRATION_ID, registration.GetId()), zap.Any("RegStageToBeCreated", req.GetRegistrationStage()), zap.Error(err))
		return &salaryprogramPb.CreateRegistrationStageDetailsResponse{
			Status: rpc.StatusPermissionDeniedWithDebugMsg("registration stage already exists, update is not implemented"),
		}, nil
	}

	return &salaryprogramPb.CreateRegistrationStageDetailsResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// nolint: funlen
func (s *Service) GetAASalaryEstimate(ctx context.Context, req *salaryprogramPb.GetAASalaryEstimateRequest) (*salaryprogramPb.GetAASalaryEstimateResponse, error) {

	aaAccountId := req.GetAccountId()
	if aaAccountId == "" {
		return &salaryprogramPb.GetAASalaryEstimateResponse{
			Status: rpc.StatusInternalWithDebugMsg("accountId cannot be empty in request"),
		}, nil
	}

	aaTransactionList, bank, _, getAaTxnErr := s.fetchAaTxnFromCaService(ctx, req.GetAccountId())
	if getAaTxnErr != nil {
		logger.Error(ctx, "error getting aa transactions from ca service", zap.String(logger.ACCOUNT_ID, aaAccountId), zap.Error(getAaTxnErr))
		return &salaryprogramPb.GetAASalaryEstimateResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error getting aa transactions from ca service for actor, err: %v", getAaTxnErr)),
		}, nil
	}

	salaryEstimate, salaryEstimateErr := s.getAASalaryEstimateFromTransactions(ctx, aaTransactionList, bank)
	if salaryEstimateErr != nil {
		logger.Error(ctx, "error getting salary estimate from transactions", zap.Error(salaryEstimateErr))
		if errors.Is(salaryEstimateErr, aasalary.ErrSalaryNotFound) {
			return &salaryprogramPb.GetAASalaryEstimateResponse{
				Status: rpc.NewStatus(uint32(salaryprogramPb.EstimateAndStoreSalaryResponse_SALARY_NOT_FOUND), "transactions satisfying salary criteria not found", ""),
			}, nil
		}
		return &salaryprogramPb.GetAASalaryEstimateResponse{
			Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error getting salary estimate from transactions for actor, err: %v", salaryEstimateErr)),
		}, nil
	}

	return &salaryprogramPb.GetAASalaryEstimateResponse{
		Status:          rpc.StatusOk(),
		EstimatedSalary: salaryEstimate,
	}, nil
}

func (s *Service) GetAaSalaryTxnVerificationRequest(ctx context.Context, req *salaryprogramPb.GetAaSalaryTxnVerificationRequestRequest) (*salaryprogramPb.GetAaSalaryTxnVerificationRequestResponse, error) {
	aaSalaryVerificationRequest, getErr := s.aaSalaryTxnVerificationDao.GetById(ctx, req.GetId(), req.GetFieldMasks()...)
	if getErr != nil {
		logger.Error(ctx, "failed to get salary txn verification request from db", zap.Error(getErr), zap.String(logger.ID, req.GetId()))
		return &salaryprogramPb.GetAaSalaryTxnVerificationRequestResponse{
			Status: rpc.StatusOk(),
		}, nil
	}

	return &salaryprogramPb.GetAaSalaryTxnVerificationRequestResponse{
		Status:                       rpc.StatusOk(),
		SalaryTxnVerificationRequest: aaSalaryVerificationRequest,
	}, nil
}

func getMaxFrequencyAmount(salaryTxnCountMap map[int32]int32) (int32, int32) {
	var maxFrequency int32 = -1
	var maxFrequencyTxnAmount int32
	for amount, frequency := range salaryTxnCountMap {
		if frequency > maxFrequency {
			maxFrequency = frequency
			maxFrequencyTxnAmount = amount
		} else if frequency == maxFrequency {
			if amount > maxFrequencyTxnAmount {
				maxFrequency = frequency
				maxFrequencyTxnAmount = amount
			}
		} else {
			continue
		}
	}
	return maxFrequencyTxnAmount, maxFrequency
}

func updateSalaryTxnFrequencyMap(filteredAaTxnList []*aasalary.AaTransaction, salaryTxnCountMap map[int32]int32, tolerance float64) map[int32]int32 {
	// list of "distinct" non overlapping amounts. for example, for 2 transactions 35000 and 36000 and tolerance level 10% we will add only 35000 since 35000*1.1 > 36000
	curMonthDistinctAmountTxnList := make([]int32, 0)
	for _, filteredTxn := range filteredAaTxnList {
		txnAmount := filteredTxn.GetAmount().GetUnits()

		// flag to indicate if an amount within tolerance level already exists in the list
		var existsInCurMonthList bool
		for _, amount := range curMonthDistinctAmountTxnList {
			if (float64(txnAmount) >= float64(amount)*(1-tolerance)) && (float64(txnAmount) <= float64(amount)*(1+tolerance)) {
				existsInCurMonthList = true
				break
			}
		}

		// if an amount within tolerance level does not already exist in the list, add it to the list
		if !(existsInCurMonthList) {
			curMonthDistinctAmountTxnList = append(curMonthDistinctAmountTxnList, int32(txnAmount))
		}
	}

	// add current month distinct amount list to global frequency map
	for _, curMonthAmount := range curMonthDistinctAmountTxnList {
		var existsInMap bool
		for amount, _ := range salaryTxnCountMap {
			if (float64(curMonthAmount) >= float64(amount)*(1-tolerance)) && (float64(curMonthAmount) <= float64(amount)*(1+tolerance)) {
				existsInMap = true
				salaryTxnCountMap[amount]++
				if amount < curMonthAmount {
					salaryTxnCountMap[curMonthAmount] = salaryTxnCountMap[amount]
				}
			}
		}
		if !(existsInMap) {
			salaryTxnCountMap[curMonthAmount] = 1
		}
	}

	return salaryTxnCountMap
}

func (s *Service) getAATransactionsForTimeRange(ctx context.Context, aaAccountId string, dateStart time.Time, dateEnd time.Time) []*aasalary.AaTransaction {
	var aaTxnList []*aasalary.AaTransaction
	pageContext := &rpc.PageContextRequest{
		PageSize: 100,
	}
	hasAfter := true
	for hasAfter {
		resp, err := s.caClient.GetRawTxnsForAccount(ctx, &caPb.GetRawTxnsForAccountRequest{
			AccountId: aaAccountId,
			Filters: &caPb.RawTransactionFilters{
				TransactionDateAfter:  timestampPb.New(dateStart),
				TransactionDateBefore: timestampPb.New(dateEnd),
			},
			PageContext: pageContext,
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			if resp.GetStatus().GetCode() == rpc.StatusRecordNotFound().GetCode() {
				logger.Info(ctx, fmt.Sprintf("no transactions found for time range %v - %v", dateStart, dateEnd), zap.Any(logger.ACCOUNT_ID, aaAccountId))
				break
			}
		}
		hasAfter = resp.GetPageContext().GetHasAfter()
		pageContext = &rpc.PageContextRequest{
			PageSize: 100,
			Token: &rpc.PageContextRequest_AfterToken{
				AfterToken: resp.GetPageContext().GetAfterToken(),
			},
		}
		nativeAaTxnList, convertErr := aasalary.ConvertToNativeAaTransactionList(resp.GetRawTxnList())
		if convertErr != nil {

		}
		aaTxnList = append(aaTxnList, nativeAaTxnList...)
	}
	return aaTxnList
}

func isPerfectInteger(num float32) bool {
	intPart := int32(num)
	return num == float32(intPart)
}

func (s *Service) getBankForAccountId(ctx context.Context, aaAccountId string) (typesPb.Bank, error) {
	accDetailsResp, accDetailsErr := s.caClient.GetAccountDetails(ctx, &caPb.GetAccountDetailsRequest{
		AccountId: aaAccountId,
	})

	if rpcErr := epifigrpc.RPCError(accDetailsResp, accDetailsErr); rpcErr != nil {
		return typesPb.Bank_BANK_UNSPECIFIED, fmt.Errorf("error getting account details. accountId : %v", aaAccountId)
	}

	fipId := accDetailsResp.GetAccountDetails().GetFipId()
	fipMeta, err := caPkg.GetFipMetaById(fipId)
	if err != nil {
		return typesPb.Bank_BANK_UNSPECIFIED, fmt.Errorf("error getting fip meta by id for fipId %v", fipId)
	}

	return fipMeta.Bank, nil
}

func getStartAndEndDateForMonth(year int, month time.Month, startSalaryCycleDate int, endSalaryCycleDate int) (time.Time, time.Time) {
	dateStart := time.Date(year, month, startSalaryCycleDate, 0, 0, 0, 0, datetime.IST)
	dateEnd := time.Date(year, month+1, endSalaryCycleDate, 0, 0, 0, 0, datetime.IST)
	return dateStart, dateEnd
}

func filterTransactionsOnTypeAndAmountValue(aaTxnList []*aasalary.AaTransaction, minSalaryValue int64) map[string]*aasalary.AaTransaction {
	amountFilteredTxnMap := make(map[string]*aasalary.AaTransaction)
	for _, txn := range aaTxnList {
		amount := txn.GetAmount().GetUnits()
		decimal := txn.GetAmount().GetNanos()
		if (txn.GetTxnType() == caEnumPb.TransactionType_TRANSACTION_TYPE_CREDIT) && (amount >= minSalaryValue) && ((decimal != 0) || (amount%1000 != 0)) {
			amountFilteredTxnMap[txn.GetId()] = txn
		}
	}
	return amountFilteredTxnMap
}

func filterTransactionsOnTxnDate(aaTxnMap map[string]*aasalary.AaTransaction, salCycleStartDate, salCycleEndDate int) map[string]*aasalary.AaTransaction {
	filteredTxnMap := make(map[string]*aasalary.AaTransaction)
	for txnId, txn := range aaTxnMap {
		txnDay := txn.GetTxnTimestamp().AsTime().Day()
		if (txnDay >= salCycleStartDate) || (txnDay <= salCycleEndDate) {
			filteredTxnMap[txnId] = txn
		}
	}
	return filteredTxnMap
}

func (s *Service) filterTransactionsUsingSmartParser(ctx context.Context, aaTxnMap map[string]*aasalary.AaTransaction, bank types.Bank) (map[string]*aasalary.AaTransaction, error) {
	smartParserInputList := getSmartParserInputList(maps.Values(aaTxnMap), bank)

	// parse via smart parser
	parserResp, parserRespErr := s.vgParserClient.ParseAATxnBulk(ctx, &vgParserPb.ParseAATxnBulkRequest{
		Header:              &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_IN_HOUSE},
		ParseAaTxnInputList: smartParserInputList,
	})
	if rpcErr := epifigrpc.RPCError(parserResp, parserRespErr); rpcErr != nil {
		return nil, fmt.Errorf("error bulk parsing raw transactions : %w", rpcErr)
	}

	// filter out UPI and IMPS transactions using smart parser output
	filteredTxnMap := make(map[string]*aasalary.AaTransaction)
	for _, parsedTxn := range parserResp.GetParseAaTxnOutputList() {
		paymentProtocol := parsedTxn.GetPaymentProtocol()
		if paymentProtocol != orderPaymentPb.PaymentProtocol_UPI {
			filteredTxnMap[parsedTxn.GetTxnId()] = aaTxnMap[parsedTxn.GetTxnId()]
		}
	}
	return filteredTxnMap, nil
}

func getSmartParserInputList(aaTxnList []*aasalary.AaTransaction, bank typesPb.Bank) []*vgParserPb.ParseAATxnInput {
	var smartParserInputList []*vgParserPb.ParseAATxnInput
	for _, txn := range aaTxnList {
		smartParserInputList = append(smartParserInputList, &vgParserPb.ParseAATxnInput{
			TxnId:     txn.GetId(),
			Narration: txn.GetNarration(),
			Bank:      bank,
			TxnType:   caPkg.ConvertAccountType(txn.GetTxnType()),
		})
	}
	return smartParserInputList
}

// RecordAaSalaryCommittedByUser stores the committed amount in aa salary verification table.
//
//nolint:funlen
func (s *Service) RecordAaSalaryCommittedByUser(ctx context.Context, request *salaryprogramPb.RecordAaSalaryCommittedByUserRequest) (*salaryprogramPb.RecordAaSalaryCommittedByUserResponse, error) {
	// check if the salary program registration is completed by the user, aa salary verification is done only for users who have completed the registration.
	regStatusResp, err := s.GetCurrentRegStatusAndNextRegStage(ctx, &salaryprogramPb.CurrentRegStatusAndNextRegStageRequest{ActorId: request.GetActorId(),
		FlowType: salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_AA_SALARY})
	if err != nil || !regStatusResp.GetStatus().IsSuccess() {
		logger.Error(ctx, "GetCurrentRegStatusAndNextRegStage call failed", zap.Any(logger.RPC_STATUS, regStatusResp.GetStatus()), zap.Error(err))
		return &salaryprogramPb.RecordAaSalaryCommittedByUserResponse{Status: rpc.StatusInternalWithDebugMsg("GetCurrentRegStatusAndNextRegStage rpc call failed")}, nil
	}
	if regStatusResp.GetRegistrationStatus() != salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		logger.Error(ctx, "can not record aa salary committed as aa salary program registration is not yet completed by the user", zap.String(logger.ACTOR_ID_V2, request.GetActorId()))
		return &salaryprogramPb.RecordAaSalaryCommittedByUserResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("can not record aa salary committed as aa salary program registration is not yet completed by the user")}, nil
	}

	latestSalaryEstimation, latestSalaryEstimationErr := s.salaryEstimationsDao.GetLatestForActor(ctx, request.GetActorId())
	if latestSalaryEstimationErr != nil {
		logger.Error(ctx, fmt.Sprintf("error finding salary estimation for actorId : %v. error : %v", request.GetActorId(), latestSalaryEstimationErr))
		return &salaryprogramPb.RecordAaSalaryCommittedByUserResponse{Status: rpc.StatusInternalWithDebugMsg("error finding salary estimation for actorId")}, nil
	}

	salaryBand, criteriaId, minUnitsInSalaryBand, calSalaryBandErr := s.aaSalaryProcessor.CalculateSalaryBand(ctx, request.GetActorId(), latestSalaryEstimation.GetLastEstimatedAmount(), request.GetCommittedSalaryAmount())
	if calSalaryBandErr != nil {
		logger.Error(ctx, "error calculating salary band using salary estimated, salary committed and criterion", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(calSalaryBandErr))
		return &salaryprogramPb.RecordAaSalaryCommittedByUserResponse{Status: rpc.StatusInternalWithDebugMsg("error calculating salary band using salary estimated, salary committed and criterion")}, nil
	}

	if txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {

		aaSalaryInvalidateVerificationReqErr := s.aaSalaryTxnVerificationDao.InvalidateAllInProgressRequestsForActor(txnCtx, request.GetActorId())
		if aaSalaryInvalidateVerificationReqErr != nil {
			return pkgErr.Wrap(aaSalaryInvalidateVerificationReqErr, "error invalidating existing aa salary verification in progress request")
		}
		if request.GetCommittedSalaryAmount() != nil {
			request.CommittedSalaryAmount.Units = minUnitsInSalaryBand
		}
		_, aaSalaryVerificationRequestErr := s.aaSalaryTxnVerificationDao.Create(txnCtx, &aa.AASalaryTxnVerificationRequest{
			ActorId:                         request.GetActorId(),
			SalaryProgramRegistrationsRefId: regStatusResp.GetRegistrationId(),
			RequestSource:                   request.GetRequestSource(),
			VerificationStatus:              aa.AASalaryTxnVerificationRequestStatus_REQUEST_STATUS_IN_PROGRESS,
			AaSalaryCriteriaRefId:           criteriaId,
			SalaryEstimationsRefId:          latestSalaryEstimation.GetId(),
			SalaryAmountCommitted:           request.GetCommittedSalaryAmount(),
			SalaryBand:                      salaryBand,
		})
		if aaSalaryVerificationRequestErr != nil {
			return pkgErr.Wrap(aaSalaryVerificationRequestErr, "error creating aa salary verification request")
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "error invalidating inactive and creating new verification request in a db txn", zap.Error(txnErr))
		return &salaryprogramPb.RecordAaSalaryCommittedByUserResponse{Status: rpc.StatusInternalWithDebugMsg("error invalidating inactive and creating new verification request in a db txn")}, nil
	}
	logger.Debug(ctx, "recorded committed salary for the user", zap.Any(logger.ACTOR_ID_V2, request.GetActorId()))
	return &salaryprogramPb.RecordAaSalaryCommittedByUserResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) GetSalaryEstimation(ctx context.Context, req *salaryprogramPb.GetSalaryEstimationRequest) (*salaryprogramPb.GetSalaryEstimationResponse, error) {
	var salaryEstimationErr error
	var salaryEstimation *salaryprogramPb.SalaryEstimation
	switch req.GetFilter().(type) {
	case *salaryprogramPb.GetSalaryEstimationRequest_Id_:
		salaryEstimation, salaryEstimationErr = s.salaryEstimationsDao.GetById(ctx, req.GetId().GetId())
		if salaryEstimationErr != nil {
			if errors.Is(salaryEstimationErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "salary estimation not found for id", zap.String(logger.ID, req.GetId().GetId()))
				return &salaryprogramPb.GetSalaryEstimationResponse{Status: rpc.StatusRecordNotFoundWithDebugMsg("salary estimation not found for id")}, nil
			}
			logger.Error(ctx, "error finding salary estimation for id", zap.String(logger.ID, req.GetId().GetId()), zap.Error(salaryEstimationErr))
			return &salaryprogramPb.GetSalaryEstimationResponse{Status: rpc.StatusInternalWithDebugMsg("error finding salary estimation for id")}, nil
		}
	case *salaryprogramPb.GetSalaryEstimationRequest_ActorIdSrcTypeStatus_:
		salaryEstimation, salaryEstimationErr = s.salaryEstimationsDao.GetByActorIdAndSourceTypeAndStatus(ctx,
			req.GetActorIdSrcTypeStatus().GetActorId(), req.GetActorIdSrcTypeStatus().GetSourceType(), req.GetActorIdSrcTypeStatus().GetStatus())
		if salaryEstimationErr != nil {
			if errors.Is(salaryEstimationErr, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "salary estimation not found for actorId, sourceType and status",
					zap.String(logger.ACTOR_ID_V2, req.GetActorIdSrcTypeStatus().GetActorId()), zap.Any("sourceType", req.GetActorIdSrcTypeStatus().GetSourceType()), zap.Any("status", req.GetActorIdSrcTypeStatus().GetStatus()))
				return &salaryprogramPb.GetSalaryEstimationResponse{Status: rpc.StatusRecordNotFoundWithDebugMsg("salary estimation not found for actorId, sourceType and status")}, nil
			}
			logger.Error(ctx, "error finding salary estimation for actorId, sourceType and status", zap.String(logger.ACTOR_ID_V2, req.GetActorIdSrcTypeStatus().GetActorId()), zap.Error(salaryEstimationErr))
			return &salaryprogramPb.GetSalaryEstimationResponse{Status: rpc.StatusInternalWithDebugMsg("error finding salary estimation for actorId, sourceType and status")}, nil
		}
	default:
		return &salaryprogramPb.GetSalaryEstimationResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("invalid filter type")}, nil
	}
	return &salaryprogramPb.GetSalaryEstimationResponse{
		Status:           rpc.StatusOk(),
		SalaryEstimation: salaryEstimation,
	}, nil
}

func (s *Service) GetConfigParams(context.Context, *salaryprogramPb.GetConfigParamsRequest) (*salaryprogramPb.GetConfigParamsResponse, error) {
	return &salaryprogramPb.GetConfigParamsResponse{
		Status: rpc.StatusOk(),
		AaSalaryMinDurationForReactivationEligibility: durationPb.New(s.dyconf.AaSalaryMinDurationForReactivationEligibility()),
		AaSalaryNewActivationDuration:                 durationPb.New(s.dyconf.AaSalaryNewActivationDuration()),
		AaSalaryMonthlyRewardEvaluationDate:           int32(s.dyconf.AaSalaryMonthlyRewardEvaluationDate()),
		AaSalaryMaxRewardActivationAdvanceMonths:      int32(s.dyconf.AaSalaryMaxRewardActivationAdvanceMonths()),
	}, nil
}

// TODO(Amitkumar) : Refractor getPromoWidgetDisplayConfigForSegment,isEligibleForSegmentPromoWidget to make it more readable
//
//nolint:dupl
func (s *Service) getPromoWidgetDisplayConfigForSegment(ctx context.Context, actorId string) *genconf.PromoWidgetDisplayConfig {

	segmentPromoWidgetConfig := s.dyconf.DynamicElementConfig().PromoWidgetConfig().SegmentUserDefaultPromoWidget()
	activeSegments := s.dyconf.DynamicElementConfig().PromoWidgetConfig().ActiveSegmentsForPromoWidgets().ToStringArray()

	errGroup, errGroupCtx := errgroup.WithContext(ctx)
	var segmentPromoWidget *genconf.PromoWidgetDisplayConfig

	for _, segment := range activeSegments {
		errGroup.Go(func() error {
			currentSegmentPromoWidgetConfig := segmentPromoWidgetConfig.Get(segment)
			if !currentSegmentPromoWidgetConfig.UserSegmentPromoWidget().IsVisible() {
				return nil
			}
			segmentIds := currentSegmentPromoWidgetConfig.SegmentIdsForUserSegmentPromoWidget().ToStringArray()
			isMemberResp, err := s.segmentationServiceClient.IsMember(errGroupCtx, &segmentPb.IsMemberRequest{
				ActorId:    actorId,
				SegmentIds: segmentIds,
				LatestBy:   timestampPb.Now(),
			})
			if te := epifigrpc.RPCError(isMemberResp, err); te != nil {
				logger.Error(ctx, "error in fetching segment membership details", zap.Error(te))
				return nil
			}
			for _, segmentId := range segmentIds {
				if isMemberResp.GetSegmentMembershipMap()[segmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
					isMemberResp.GetSegmentMembershipMap()[segmentId].GetIsActorMember() {
					segmentPromoWidget = currentSegmentPromoWidgetConfig.UserSegmentPromoWidget()
					return nil
				}
			}
			return nil
		})
	}

	_ = errGroup.Wait()
	return segmentPromoWidget
}

//nolint:dupl
func (s *Service) isEligibleForSegmentPromoWidget(ctx context.Context, actorId string, promoWidgetDisplayConfig *genconf.PromoWidgetDisplayConfig) (bool, string, error, tieringExtPb.Tier) {

	if promoWidgetDisplayConfig == nil {
		return false, "promoWidgetDisplayConfig is nil", nil, tieringExtPb.Tier_TIER_UNSPECIFIED
	}

	// segment check
	if !promoWidgetDisplayConfig.IsVisible() {
		return false, "user is not part of eligible segments", nil, tieringExtPb.Tier_TIER_UNSPECIFIED
	}

	getTieringPitchResp, getTieringPitchErr := s.tieringClient.GetTieringPitchV2(ctx, &tieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})

	if rpcErr := epifigrpc.RPCError(getTieringPitchResp, getTieringPitchErr); rpcErr != nil {
		return false, "defaulting to false in case of error", fmt.Errorf("error in GetTieringPitchV2 rpc: %w", rpcErr), tieringExtPb.Tier_TIER_UNSPECIFIED
	}

	currentTier := getTieringPitchResp.GetCurrentTier()

	if currentTier == tieringExtPb.Tier_TIER_FI_PLUS || currentTier.IsBaseTier() {
		return true, "", nil, currentTier
	}

	return false, "user is not in plus or base tier", nil, currentTier
}
