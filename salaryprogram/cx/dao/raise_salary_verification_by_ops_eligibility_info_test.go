package dao

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/protobuf/proto"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"
	gormV2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/salaryprogram"
	salaryprogramCxPb "github.com/epifi/gamma/api/salaryprogram/cx"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/pagination"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	"github.com/epifi/gamma/salaryprogram/config"
	"github.com/epifi/gamma/salaryprogram/cx/dao/model"
	"github.com/epifi/gamma/salaryprogram/test"
)

type RaiseSalaryVerificationByOpsEligibilityInfoDaoTestSuite struct {
	db     *gormV2.DB
	dao    IRaiseSalaryVerificationByOpsEligibilityInfoDao
	config *config.Config
}

func newRaiseSalaryVerificationByOpsEligibilityInfoDaoTestSuite(db *gormV2.DB, dao IRaiseSalaryVerificationByOpsEligibilityInfoDao, config *config.Config) *RaiseSalaryVerificationByOpsEligibilityInfoDaoTestSuite {
	return &RaiseSalaryVerificationByOpsEligibilityInfoDaoTestSuite{
		dao:    dao,
		db:     db,
		config: config,
	}
}

var rsvTs *RaiseSalaryVerificationByOpsEligibilityInfoDaoTestSuite

func TestPGDBRaiseSalaryVerificationByOpsEligibilityInfoDao_Create(t *testing.T) {
	type args struct {
		ctx             context.Context
		eligibilityInfo *salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo
	}
	tests := []struct {
		name    string
		args    args
		want    *salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo
		wantErr bool
	}{
		{
			name: "should return error if actor id is passed empty",
			args: args{
				ctx: context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{
					EligibilityStatus: salaryprogramCxPb.UserSalaryVerificationEligibilityStatus_NEW,
					LastActivationFromTime: &timestamp.Timestamp{
						Seconds: 1671533909,
					},
					LastActivationTillTime: &timestamp.Timestamp{
						Seconds: 1734692309,
					},
					LastVerifiedSalaryTxnTimestamp: &timestamp.Timestamp{
						Seconds: 1676890709,
					},
					LastVerifiedSalaryTxnVerifiedBy: salaryprogram.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS,
				},
			},
			wantErr: true,
		},
		{
			name: "should return error if eligibility status is passed unspecified",
			args: args{
				ctx: context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{
					ActorId: "actor-3",
					LastActivationFromTime: &timestamp.Timestamp{
						Seconds: 1671533909,
					},
					LastActivationTillTime: &timestamp.Timestamp{
						Seconds: 1734692309,
					},
					LastVerifiedSalaryTxnTimestamp: &timestamp.Timestamp{
						Seconds: 1676890709,
					},
					LastVerifiedSalaryTxnVerifiedBy: salaryprogram.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS,
				},
			},
			wantErr: true,
		},
		{
			name: "should return error if last activation from time is passed nil",
			args: args{
				ctx: context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{
					ActorId:           "actor-3",
					EligibilityStatus: salaryprogramCxPb.UserSalaryVerificationEligibilityStatus_NEW,
					LastActivationTillTime: &timestamp.Timestamp{
						Seconds: 1734692309,
					},
					LastVerifiedSalaryTxnTimestamp: &timestamp.Timestamp{
						Seconds: 1676890709,
					},
					LastVerifiedSalaryTxnVerifiedBy: salaryprogram.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS,
				},
			},
			wantErr: true,
		},
		{
			name: "should return error if actor id last activation till time is passed nil",
			args: args{
				ctx: context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{
					ActorId:           "actor-3",
					EligibilityStatus: salaryprogramCxPb.UserSalaryVerificationEligibilityStatus_NEW,
					LastActivationFromTime: &timestamp.Timestamp{
						Seconds: 1671533909,
					},
					LastVerifiedSalaryTxnTimestamp: &timestamp.Timestamp{
						Seconds: 1676890709,
					},
					LastVerifiedSalaryTxnVerifiedBy: salaryprogram.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS,
				},
			},
			wantErr: true,
		},
		{
			name: "should return error if last verified salary txn timestamp is passed nil",
			args: args{
				ctx: context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{
					ActorId:           "actor-3",
					EligibilityStatus: salaryprogramCxPb.UserSalaryVerificationEligibilityStatus_NEW,
					LastActivationFromTime: &timestamp.Timestamp{
						Seconds: 1671533909,
					},
					LastActivationTillTime: &timestamp.Timestamp{
						Seconds: 1734692309,
					},
					LastVerifiedSalaryTxnVerifiedBy: salaryprogram.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS,
				},
			},
			wantErr: true,
		},
		{
			name: "should successfully create entry in db if LastActivationTillTime, LastActivationFromTime and LastVerifiedSalaryTxnTimestamp are not passed all together",
			args: args{
				ctx: context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{
					ActorId:           "actor-3",
					EligibilityStatus: salaryprogramCxPb.UserSalaryVerificationEligibilityStatus_NEW,
					RegistrationCompletionTime: &timestamp.Timestamp{
						Seconds: 1671533909,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should successfully create entry in db",
			args: args{
				ctx: context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{
					ActorId:           "actor-5",
					EligibilityStatus: salaryprogramCxPb.UserSalaryVerificationEligibilityStatus_NEW,
					LastActivationFromTime: &timestamp.Timestamp{
						Seconds: 1671533909,
					},
					LastActivationTillTime: &timestamp.Timestamp{
						Seconds: 1734692309,
					},
					LastVerifiedSalaryTxnTimestamp: &timestamp.Timestamp{
						Seconds: 1676890709,
					},
					LastVerifiedSalaryTxnVerifiedBy: salaryprogram.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := rsvTs.dao.Create(tt.args.ctx, tt.args.eligibilityInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr == false && got.Id == "" {
				t.Errorf("Create() success but generated id nil")
				return
			}
		})
	}
}

func TestPGDBRaiseSalaryVerificationByOpsEligibilityInfoDao_UpdateByActorId(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, rsvTs.db, rsvTs.config.SalaryProgramDb.GetName(), test.AllTables)
	type args struct {
		ctx             context.Context
		eligibilityInfo *salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo
		updateMask      []salaryprogramCxPb.RaiseSalaryVerByOpsEligibilityInfoFieldMask
	}
	tests := []struct {
		name        string
		args        args
		wantErr     bool
		wantErrType error
	}{
		{
			name: "should return error if actor id is empty",
			args: args{
				ctx:             context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{},
			},
			wantErr: true,
		},
		{
			name: "should return error if field mask is empty",
			args: args{
				ctx: context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{
					ActorId: "actor-1",
				},
			},
			wantErr: true,
		},
		{
			name: "should return ErrRowNotUpdated error if no record exist for passed actor id",
			args: args{
				ctx: context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{
					ActorId:           "actor-5",
					EligibilityStatus: salaryprogramCxPb.UserSalaryVerificationEligibilityStatus_PENDING,
				},
				updateMask: []salaryprogramCxPb.RaiseSalaryVerByOpsEligibilityInfoFieldMask{salaryprogramCxPb.RaiseSalaryVerByOpsEligibilityInfoFieldMask_ELIGIBILITY_STATUS},
			},
			wantErr:     true,
			wantErrType: epifierrors.ErrRowNotUpdated,
		},
		{
			name: "should successfully update entry in db",
			args: args{
				ctx: context.Background(),
				eligibilityInfo: &salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{
					ActorId:           "actor-1",
					EligibilityStatus: salaryprogramCxPb.UserSalaryVerificationEligibilityStatus_PENDING,
					LastActivationFromTime: &timestamp.Timestamp{
						Seconds: 1671533910,
					},
					LastActivationTillTime: &timestamp.Timestamp{
						Seconds: 1734692310,
					},
					LastVerifiedSalaryTxnTimestamp: &timestamp.Timestamp{
						Seconds: 1676890710,
					},
					LastVerifiedSalaryTxnVerifiedBy: salaryprogram.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_SYSTEM,
				},
				updateMask: []salaryprogramCxPb.RaiseSalaryVerByOpsEligibilityInfoFieldMask{
					salaryprogramCxPb.RaiseSalaryVerByOpsEligibilityInfoFieldMask_ELIGIBILITY_STATUS, salaryprogramCxPb.RaiseSalaryVerByOpsEligibilityInfoFieldMask_LAST_ACTIVATION_FROM_TIME, salaryprogramCxPb.RaiseSalaryVerByOpsEligibilityInfoFieldMask_LAST_ACTIVATION_TILL_TIME,
					salaryprogramCxPb.RaiseSalaryVerByOpsEligibilityInfoFieldMask_LAST_VERIFIED_SALARY_TXN_TIMESTAMP, salaryprogramCxPb.RaiseSalaryVerByOpsEligibilityInfoFieldMask_LAST_VERIFIED_SALARY_TXN_VERIFIED_BY,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := rsvTs.dao.UpdateByActorId(tt.args.ctx, tt.args.eligibilityInfo, tt.args.updateMask)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErrType != nil && !errors.Is(err, tt.wantErrType) {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPGDBRaiseSalaryVerificationByOpsEligibilityInfoDao_GetPaginated(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, rsvTs.db, rsvTs.config.SalaryProgramDb.GetName(), test.AllTables)
	type args struct {
		ctx                context.Context
		filters            *model.RaiseSalaryVerificationByOpsEligibilityInfoDaoFilters
		sortOrder          salaryprogramCxPb.SortOrder
		salaryProgramStage salaryprogramCxPb.SalaryProgramStage
		pageSize           uint32
		pageToken          *pagination.PageToken
	}
	tests := []struct {
		name           string
		args           args
		want           []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo
		wantPageCtxRes *rpc.PageContextResponse
		wantErr        bool
	}{
		{
			name: "should return error if no field with index present in passed",
			args: args{
				ctx:                context.Background(),
				filters:            &model.RaiseSalaryVerificationByOpsEligibilityInfoDaoFilters{},
				salaryProgramStage: salaryprogramCxPb.SalaryProgramStage_SALARY_ACTIVE,
			},
			want:    []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{},
			wantErr: true,
		},
		{
			name: "should return a entry matching the passed filters",
			args: args{
				ctx: context.Background(),
				filters: &model.RaiseSalaryVerificationByOpsEligibilityInfoDaoFilters{
					ActorIds:                      []string{"actor-1"},
					LastActivationTillTimeFrom:    raiseSalaryVerEligibilityInfoFixture1.GetLastActivationFromTime().AsTime(),
					LastSalaryTxnVerifiedByIn:     []salaryprogram.SalaryTxnVerificationRequestVerifiedBy{salaryprogram.SalaryTxnVerificationRequestVerifiedBy_VERIFIED_BY_OPS},
					LastVerifiedSalaryTxnTimeTill: raiseSalaryVerEligibilityInfoFixture1.GetLastVerifiedSalaryTxnTimestamp().AsTime().Add(time.Second * 1),
					EligibilityStatus:             salaryprogramCxPb.UserSalaryVerificationEligibilityStatus_NEW,
				},
				salaryProgramStage: salaryprogramCxPb.SalaryProgramStage_SALARY_ACTIVE,
				pageSize:           10,
			},
			want:           []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{raiseSalaryVerEligibilityInfoFixture1},
			wantPageCtxRes: &rpc.PageContextResponse{},
			wantErr:        false,
		},
		{
			name: "should return a entry with page context having hasAfter true",
			args: args{
				ctx: context.Background(),
				filters: &model.RaiseSalaryVerificationByOpsEligibilityInfoDaoFilters{
					LastActivationTillTimeFrom: raiseSalaryVerEligibilityInfoFixture1.GetLastActivationFromTime().AsTime(),
				},
				// verified pagination
				pageSize: 1,
				// verifies sort order
				sortOrder:          salaryprogramCxPb.SortOrder_ASC,
				salaryProgramStage: salaryprogramCxPb.SalaryProgramStage_SALARY_ACTIVE,
			},
			want: []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{raiseSalaryVerEligibilityInfoFixture2},
			wantPageCtxRes: &rpc.PageContextResponse{
				HasAfter: true,
			},
			wantErr: false,
		},
		{
			name: "should return a entry matching the page token condition",
			args: args{
				ctx: context.Background(),
				filters: &model.RaiseSalaryVerificationByOpsEligibilityInfoDaoFilters{
					LastActivationTillTimeFrom: raiseSalaryVerEligibilityInfoFixture1.GetLastActivationFromTime().AsTime(),
				},
				pageSize:           10,
				sortOrder:          salaryprogramCxPb.SortOrder_DESC,
				salaryProgramStage: salaryprogramCxPb.SalaryProgramStage_SALARY_ACTIVE,
				pageToken: &pagination.PageToken{
					Timestamp: raiseSalaryVerEligibilityInfoFixture2.LastVerifiedSalaryTxnTimestamp,
				},
			},
			want:           []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{raiseSalaryVerEligibilityInfoFixture2},
			wantPageCtxRes: &rpc.PageContextResponse{},
			wantErr:        false,
		},
		{
			name: "should return entries matching the last_activation_till_time_from filter condition",
			args: args{
				ctx: context.Background(),
				filters: &model.RaiseSalaryVerificationByOpsEligibilityInfoDaoFilters{
					LastActivationTillTimeFrom: raiseSalaryVerEligibilityInfoFixture1.GetLastActivationFromTime().AsTime(),
				},
				pageSize:           10,
				salaryProgramStage: salaryprogramCxPb.SalaryProgramStage_SALARY_ACTIVE,
				sortOrder:          salaryprogramCxPb.SortOrder_ASC,
			},
			// verifies order
			want:           []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{raiseSalaryVerEligibilityInfoFixture2, raiseSalaryVerEligibilityInfoFixture1},
			wantPageCtxRes: &rpc.PageContextResponse{},
			wantErr:        false,
		},
		{
			name: "should return empty list if no users are salary active for passed activation_till time",
			args: args{
				ctx: context.Background(),
				filters: &model.RaiseSalaryVerificationByOpsEligibilityInfoDaoFilters{
					LastActivationTillTimeFrom: raiseSalaryVerEligibilityInfoFixture2.GetLastActivationTillTime().AsTime().Add(time.Second * 1),
				},
				pageSize:           10,
				salaryProgramStage: salaryprogramCxPb.SalaryProgramStage_SALARY_ACTIVE,
				sortOrder:          salaryprogramCxPb.SortOrder_ASC,
			},
			want:           []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{},
			wantPageCtxRes: &rpc.PageContextResponse{},
			wantErr:        false,
		},
		{
			name: "should return entries matching the filter condition",
			args: args{
				ctx: context.Background(),
				filters: &model.RaiseSalaryVerificationByOpsEligibilityInfoDaoFilters{
					ActorIds:                       []string{raiseSalaryVerEligibilityInfoFixture3.ActorId},
					IsLastVerifiedTxnTimeNull:      true,
					RegistrationCompletionTimeFrom: raiseSalaryVerEligibilityInfoFixture3.GetRegistrationCompletionTime().AsTime(),
				},
				pageSize:           10,
				salaryProgramStage: salaryprogramCxPb.SalaryProgramStage_REGISTERED_AND_ACTIVATION_PENDING,
				sortOrder:          salaryprogramCxPb.SortOrder_ASC,
			},
			want:           []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{raiseSalaryVerEligibilityInfoFixture3},
			wantPageCtxRes: &rpc.PageContextResponse{},
			wantErr:        false,
		},
		{
			name: "should return entries matching the filter condition in asc sorted order",
			args: args{
				ctx: context.Background(),
				filters: &model.RaiseSalaryVerificationByOpsEligibilityInfoDaoFilters{
					IsLastVerifiedTxnTimeNull:      true,
					RegistrationCompletionTimeFrom: raiseSalaryVerEligibilityInfoFixture3.GetRegistrationCompletionTime().AsTime(),
				},
				pageSize:           10,
				salaryProgramStage: salaryprogramCxPb.SalaryProgramStage_REGISTERED_AND_ACTIVATION_PENDING,
				sortOrder:          salaryprogramCxPb.SortOrder_ASC,
			},
			want:           []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{raiseSalaryVerEligibilityInfoFixture3, raiseSalaryVerEligibilityInfoFixture4},
			wantPageCtxRes: &rpc.PageContextResponse{},
			wantErr:        false,
		},
		{
			name: "should return entries matching the filter condition in desc sorted order",
			args: args{
				ctx: context.Background(),
				filters: &model.RaiseSalaryVerificationByOpsEligibilityInfoDaoFilters{
					IsLastVerifiedTxnTimeNull:      true,
					RegistrationCompletionTimeFrom: raiseSalaryVerEligibilityInfoFixture3.GetRegistrationCompletionTime().AsTime(),
				},
				pageSize:           10,
				salaryProgramStage: salaryprogramCxPb.SalaryProgramStage_REGISTERED_AND_ACTIVATION_PENDING,
				sortOrder:          salaryprogramCxPb.SortOrder_DESC,
			},
			want:           []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo{raiseSalaryVerEligibilityInfoFixture4, raiseSalaryVerEligibilityInfoFixture3},
			wantPageCtxRes: &rpc.PageContextResponse{},
			wantErr:        false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, gotPageCtxRes, err := rsvTs.dao.GetPaginated(tt.args.ctx, tt.args.filters, tt.args.sortOrder, tt.args.salaryProgramStage, tt.args.pageSize, tt.args.pageToken)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPaginated() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareRaiseSalaryVerEligibilityInfoList(got, tt.want) {
				t.Errorf("GetPaginated() got = %v\nwant %v", got, tt.want)
				return
			}
			if tt.wantPageCtxRes != nil && tt.wantPageCtxRes.HasAfter != gotPageCtxRes.HasAfter {
				t.Errorf("GetPaginated() gotPageCtxRes = %v\nwantPageCtxRes %v", gotPageCtxRes, tt.wantPageCtxRes)
			}
		})
	}
}

func compareRaiseSalaryVerEligibilityInfoList(actual, expected []*salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo) bool {
	if len(actual) != len(expected) {
		return false
	}
	for idx := range actual {
		if !compareRaiseSalaryVerEligibilityInfo(actual[idx], expected[idx]) {
			return false
		}
	}
	return true
}

func compareRaiseSalaryVerEligibilityInfo(actual, expected *salaryprogramCxPb.RaiseSalaryVerificationByOpsEligibilityInfo) bool {
	if actual != nil && expected != nil {
		expected.CreatedAt = actual.CreatedAt
		expected.UpdatedAt = actual.UpdatedAt
	}
	return proto.Equal(actual, expected)
}
