package dao

import (
	"fmt"
	"sync"
	"testing"

	salaryEnumsPb "github.com/epifi/gamma/api/salaryprogram/enums"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	spAaPb "github.com/epifi/gamma/api/salaryprogram/aa"

	"golang.org/x/net/context"
	gormV2 "gorm.io/gorm"

	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/salaryprogram/config"
	"github.com/epifi/gamma/salaryprogram/test"
)

var (
	aaSalaryCriteriaTs *AaSalaryCriteriaTestSuite
	criteria1          = &spAaPb.AaSalaryCriteria{
		Id:           "57f83510-407f-4a9a-ada3-998a032a54dd",
		CriteriaName: spAaPb.CriteriaName_CRITERIA_NAME_C_ONE,
		Details: &spAaPb.Criterion{
			CriteriaDetailList: []*spAaPb.CriteriaDetail{
				{
					SalaryBand: salaryEnumsPb.SalaryBand_SALARY_BAND_1,
					Actions: []*spAaPb.Action{
						{
							ActionDetails: &spAaPb.QualifyingCriteria{
								Criteria: &spAaPb.QualifyingCriteria_Salary{
									Salary: &spAaPb.Salary{
										MinSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        20000,
										},
										MaxSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        30000,
										},
										MinRatio: 0.6,
										MaxRatio: 0.8,
									},
								},
							},
						},
					},
				},
				{
					SalaryBand: salaryEnumsPb.SalaryBand_SALARY_BAND_2,
					Actions: []*spAaPb.Action{
						{
							ActionDetails: &spAaPb.QualifyingCriteria{
								Criteria: &spAaPb.QualifyingCriteria_Salary{
									Salary: &spAaPb.Salary{
										MinSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        20000,
										},
										MaxSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        30000,
										},
										MinRatio: 0.8,
										MaxRatio: 1.0,
									},
								},
							},
						},
					},
				},
				{
					SalaryBand: salaryEnumsPb.SalaryBand_SALARY_BAND_1,
					Actions: []*spAaPb.Action{
						{
							ActionDetails: &spAaPb.QualifyingCriteria{
								Criteria: &spAaPb.QualifyingCriteria_Salary{
									Salary: &spAaPb.Salary{
										MinSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        30000,
										},
										MaxSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        40000,
										},
										MinRatio: 0.6,
										MaxRatio: 0.8,
									},
								},
							},
						},
					},
				},
				{
					SalaryBand: salaryEnumsPb.SalaryBand_SALARY_BAND_2,
					Actions: []*spAaPb.Action{
						{
							ActionDetails: &spAaPb.QualifyingCriteria{
								Criteria: &spAaPb.QualifyingCriteria_Salary{
									Salary: &spAaPb.Salary{
										MinSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        30000,
										},
										MaxSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        40000,
										},
										MinRatio: 0.8,
										MaxRatio: 1.0,
									},
								},
							},
						},
					},
				},
			},
		},
	}
	criteria2 = &spAaPb.AaSalaryCriteria{
		Id:           "27f22e78-0512-4009-9620-ae6f5a4e0faa",
		CriteriaName: spAaPb.CriteriaName_CRITERIA_NAME_C_ONE,
		Details: &spAaPb.Criterion{
			CriteriaDetailList: []*spAaPb.CriteriaDetail{
				{
					SalaryBand: salaryEnumsPb.SalaryBand_SALARY_BAND_1,
					Actions: []*spAaPb.Action{
						{
							ActionDetails: &spAaPb.QualifyingCriteria{
								Criteria: &spAaPb.QualifyingCriteria_Salary{
									Salary: &spAaPb.Salary{
										MinSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        20000,
										},
										MaxSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        30000,
										},
										MinRatio: 0.6,
										MaxRatio: 0.8,
									},
								},
							},
						},
					},
				},
				{
					SalaryBand: salaryEnumsPb.SalaryBand_SALARY_BAND_2,
					Actions: []*spAaPb.Action{
						{
							ActionDetails: &spAaPb.QualifyingCriteria{
								Criteria: &spAaPb.QualifyingCriteria_Salary{
									Salary: &spAaPb.Salary{
										MinSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        20000,
										},
										MaxSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        30000,
										},
										MinRatio: 0.8,
										MaxRatio: 1.0,
									},
								},
							},
						},
					},
				},
				{
					SalaryBand: salaryEnumsPb.SalaryBand_SALARY_BAND_1,
					Actions: []*spAaPb.Action{
						{
							ActionDetails: &spAaPb.QualifyingCriteria{
								Criteria: &spAaPb.QualifyingCriteria_Salary{
									Salary: &spAaPb.Salary{
										MinSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        30000,
										},
										MaxSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        40000,
										},
										MinRatio: 0.6,
										MaxRatio: 0.8,
									},
								},
							},
						},
					},
				},
				{
					SalaryBand: salaryEnumsPb.SalaryBand_SALARY_BAND_2,
					Actions: []*spAaPb.Action{
						{
							ActionDetails: &spAaPb.QualifyingCriteria{
								Criteria: &spAaPb.QualifyingCriteria_Salary{
									Salary: &spAaPb.Salary{
										MinSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        30000,
										},
										MaxSalary: &money.Money{
											CurrencyCode: "INR",
											Units:        40000,
										},
										MinRatio: 0.8,
										MaxRatio: 1.0,
									},
								},
							},
						},
					},
				},
			},
		},
	}
	initialiseSameDbOnce sync.Once
)

type AaSalaryCriteriaTestSuite struct {
	db     *gormV2.DB
	dao    IAaSalaryCriteriaDao
	config *config.Config
}

func newAaSalaryCriteriaTestSuite(db *gormV2.DB, dao IAaSalaryCriteriaDao, config *config.Config) *AaSalaryCriteriaTestSuite {
	return &AaSalaryCriteriaTestSuite{db: db, dao: dao, config: config}
}

func TestPGDBAaSalaryCriteriaDao_Create(t *testing.T) {
	type args struct {
		ctx      context.Context
		criteria *spAaPb.AaSalaryCriteria
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    *spAaPb.AaSalaryCriteria
	}{
		{
			name: "criteria creation should be successful",
			args: args{
				ctx:      context.Background(),
				criteria: criteria1,
			},
			wantErr: false,
			want:    criteria1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, aaSalaryCriteriaTs.db, aaSalaryCriteriaTs.config.SalaryProgramDb.GetName(), test.AllTables)
			got, err := aaSalaryCriteriaTs.dao.Create(tt.args.ctx, tt.args.criteria)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == false {
				assertAaSalaryCriteria(t, got, tt.want)
			}
		})
	}
}

func TestPGDBAaSalaryCriteriaDao_GetCriteriaById(t *testing.T) {
	type args struct {
		ctx context.Context
		id  string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    *spAaPb.AaSalaryCriteria
	}{
		{
			name: "get by id should be successful",
			args: args{
				ctx: context.Background(),
				id:  "27f22e78-0512-4009-9620-ae6f5a4e0faa",
			},
			wantErr: false,
			want:    criteria2,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, aaSalaryCriteriaTs.db, aaSalaryCriteriaTs.config.SalaryProgramDb.GetName(), test.AllTables)
			got, err := aaSalaryCriteriaTs.dao.GetCriteriaById(tt.args.ctx, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == false {
				assertAaSalaryCriteria(t, got, tt.want)
			}
		})
	}
}

func TestPGDBAaSalaryCriteriaDao_GetCriteriaByName(t *testing.T) {
	type args struct {
		ctx          context.Context
		criteriaName string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    *spAaPb.AaSalaryCriteria
	}{
		{
			name: "get by criteria name should be successful",
			args: args{
				ctx:          context.Background(),
				criteriaName: "CRITERIA_NAME_C_ONE",
			},
			wantErr: false,
			want:    criteria2,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pkgTest.TruncateAndPopulateRdsFixtures(t, aaSalaryCriteriaTs.db, aaSalaryCriteriaTs.config.SalaryProgramDb.GetName(), test.AllTables)
			got, err := aaSalaryCriteriaTs.dao.GetCriteriaByName(tt.args.ctx, tt.args.criteriaName)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == false {
				assertAaSalaryCriteria(t, got, tt.want)
			}
		})
	}
}

func assertAaSalaryCriteria(t *testing.T, got, want *spAaPb.AaSalaryCriteria) {
	got.ExecutedAt = want.GetExecutedAt()
	got.CreatedAt = want.GetCreatedAt()
	got.UpdatedAt = want.GetUpdatedAt()
	assert.True(t, proto.Equal(got, want), fmt.Sprintf("got:%s, want:%s", got.String(), want.String()))
}
