package accrual

import (
	"context"

	sm "github.com/epifi/gamma/accrual/model"
	accrualPb "github.com/epifi/gamma/api/accrual"
)

// Temporary migration helper functions for the accrual service.
// TODO(sahil): Remove these functions once the migration scripts are completed.

// PerformTransaction is an exported wrapper for performTransaction. For use in migration scripts/internal tools.
func (s *Service) PerformTransaction(ctx context.Context, req *sm.TransactRequest) (*sm.TransactResponse, error) {
	return s.performTransaction(ctx, req)
}

// ResolveBalanceExported is an exported wrapper for resolveBalance. For use in migration scripts/internal tools.
func (s *Service) ResolveBalanceExported(ctx context.Context, accountId string) (*sm.Account, error) {
	return s.resolveBalance(ctx, accountId)
}

// PerformAccountClosure is an exported wrapper for performAccountClosure. For use in migration scripts/internal tools.
func (s *Service) PerformAccountClosure(ctx context.Context, accountId string) error {
	return s.performAccountClosure(ctx, accountId)
}

// CalculateAndUpdateAccountBalance is an exported wrapper for calculateAndUpdateAccountBalance. For use in migration scripts/internal tools.
func (s *Service) CalculateAndUpdateAccountBalance(ctx context.Context, accountId string) (*sm.Account, error) {
	return s.calculateAndUpdateAccountBalance(ctx, accountId)
}

// GetOrCreateAccount is an exported wrapper for getOrCreateAccount. For use in migration scripts/internal tools.
func (s *Service) GetOrCreateAccount(ctx context.Context, req *sm.AccountIdentifier) (*sm.Account, error) {
	return s.getOrCreateAccount(ctx, req)
}

// PerformReverseTransaction is an exported wrapper for performReverseTransaction. For use in migration scripts/internal tools.
func (s *Service) PerformReverseTransaction(ctx context.Context, refID string, parentTxn *sm.Transaction) (*accrualPb.ReverseTransactionResponse, error) {
	return s.performReverseTransaction(ctx, refID, parentTxn)
}

// ResolvePendingDebitTxns is an exported wrapper for resolvePendingDebitTxns. For use in migration scripts/internal tools.
func (s *Service) ResolvePendingDebitTxns(ctx context.Context, accountId string) bool {
	return s.resolvePendingDebitTxns(ctx, accountId)
}
