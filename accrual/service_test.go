package accrual

import (
	"context"
	"math"
	"math/rand"
	"os"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	gormV2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTestV2 "github.com/epifi/be-common/pkg/test/v2"

	accrualPb "github.com/epifi/gamma/api/accrual"
	rewardsPb "github.com/epifi/gamma/api/rewards"

	"github.com/epifi/gamma/accrual/config"
	"github.com/epifi/gamma/accrual/dao"
	svcModel "github.com/epifi/gamma/accrual/model"
	"github.com/epifi/gamma/accrual/test"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

type accrualServiceTestSuite struct {
	db             *gormV2.DB
	accountDao     dao.AccountDao
	transactionDao dao.TransactionDao
	conf           *config.Config
	service        *Service
}

var (
	largeTime      = time.Date(2050, time.January, 1, 1, 1, 1, 0, time.UTC)
	smallTime      = time.Date(2010, time.January, 1, 1, 1, 1, 0, time.UTC)
	largeTimestamp = timestampPb.New(largeTime)
	smallTimestamp = timestampPb.New(smallTime)

	affectedTables = []string{"accrual_accounts", "accrual_transactions", "point_buckets", "transaction_points_splits"}
	accrualSvcTs   *accrualServiceTestSuite
)

func newAccrualServiceTestSuite(
	db *gormV2.DB,
	accountDao dao.AccountDao,
	transactionDao dao.TransactionDao,
	conf *config.Config,
	service *Service) *accrualServiceTestSuite {
	return &accrualServiceTestSuite{
		db:             db,
		accountDao:     accountDao,
		transactionDao: transactionDao,
		conf:           conf,
		service:        service}
}

func TestMain(m *testing.M) {
	var teardown func()
	conf, db, teardown := test.InitTestServer()

	gormTxnExecutor := storageV2.NewGormTxnExecutor(db)
	idgenerator := idgen.NewDomainIdGenerator(idgen.NewClock())

	// init daos
	accountDao := dao.NewAccountDaoImpl(db, idgenerator)
	transactionDao := dao.NewTransactionDaoImpl(db)
	transactionSplitDao := dao.NewTransactionSplitDaoImpl(db)
	pointBucketDao := dao.NewPointBucketDaoImpl(db)

	service := NewService(gormTxnExecutor, idgenerator, accountDao, transactionDao, pointBucketDao, transactionSplitDao, false)
	accrualSvcTs = newAccrualServiceTestSuite(db, accountDao, transactionDao, conf, service)

	exitCode := m.Run()
	teardown()

	os.Exit(exitCode)
}

// nolint: ignore
func TestService_Transact(t *testing.T) {

	type Balance struct {
		CurrentBalance   int32
		AvailableBalance int32
	}

	testCases := []struct {
		name               string
		request            *accrualPb.TransactRequest
		wantRpcStatus      *rpc.Status
		wantUpdatedBalance *Balance
	}{
		{
			name: "Should successfully perform credit transaction",
			request: &accrualPb.TransactRequest{
				RequestRefId:     "req-1",
				ActorId:          "new-actor-1",
				Amount:           10,
				AmountExpiryTime: largeTimestamp,
				AccountType:      accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
				TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
			},
			wantRpcStatus: rpc.StatusOk(),
			wantUpdatedBalance: &Balance{
				CurrentBalance:   10,
				AvailableBalance: 10,
			},
		},
		{
			name: "Credit Txn with expiry less than current time should fail",
			request: &accrualPb.TransactRequest{
				RequestRefId:     "req-2",
				ActorId:          "new-actor-1",
				Amount:           10,
				AmountExpiryTime: smallTimestamp,
				AccountType:      accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
				TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
			},
			wantRpcStatus: rpc.StatusInvalidArgumentWithDebugMsg("expiry time cannot be before current time"),
		},
		{
			name: "Credit transaction with nil expiry should fail",
			request: &accrualPb.TransactRequest{
				RequestRefId:    "req-3",
				ActorId:         "new-actor-1",
				Amount:          10,
				AccountType:     accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
				TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
			},
			wantRpcStatus: rpc.StatusInvalidArgumentWithDebugMsg("nil expiry time"),
		},
		{
			name: "Debit account without any existing credit should fail",
			request: &accrualPb.TransactRequest{
				RequestRefId:    "req-1",
				ActorId:         "new-actor-1",
				Amount:          10,
				AccountType:     accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
				TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
			},
			wantRpcStatus: rpc.StatusFailedPreconditionWithDebugMsg("insufficient balance"),
		},
		{
			name: "Debit txn (with pending debit allowed) without any existing credit should be successful",
			request: &accrualPb.TransactRequest{
				RequestRefId:    "req-1",
				ActorId:         "new-actor-1",
				Amount:          10,
				AccountType:     accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
				TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
				Options:         &accrualPb.TransactRequest_Options{IsPendingDebitAllowed: true},
			},
			wantRpcStatus: rpc.StatusOk(),
			wantUpdatedBalance: &Balance{
				CurrentBalance:   0,
				AvailableBalance: -10,
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
			response, err := accrualSvcTs.service.Transact(context.Background(), tc.request)
			assert.Nil(t, err)
			assert.NotNil(t, response)
			assert.Equal(t, tc.wantRpcStatus.GetCode(), response.Status.GetCode())

			if tc.wantRpcStatus.GetCode() == rpc.StatusOk().GetCode() {
				accIdentifier := svcModel.AccountIdentifier{ActorId: tc.request.ActorId, AccountType: tc.request.AccountType}
				assertAccountBalance(t, accIdentifier, tc.wantUpdatedBalance.CurrentBalance, tc.wantUpdatedBalance.AvailableBalance)
			}
		})
	}

	// This checks validates that debit transaction (without pending debit allowed) should be successful when sufficient balance is present in the account.
	// For testing this it first credits the account with some balance and then debit the account with some lesser balance.
	t.Run("Debit with amount lesser than credit amount (pending debit not allowed case)", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId = "actor-1"
		)
		accountType := accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())
		accIdentifier := svcModel.AccountIdentifier{ActorId: actorId, AccountType: accountType}
		var creditAmt1, creditAmt2, debitAmt int32 = 55, 35, 90

		// CREDIT transaction  should be successful
		creditRes1, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-1",
			ActorId:          actorId,
			Amount:           creditAmt1,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditRes1, err)
		assertAccountBalance(t, accIdentifier, creditAmt1, creditAmt1)

		// credit transaction  should be successful
		creditRes2, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-2",
			ActorId:          actorId,
			Amount:           creditAmt2,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditRes2, err)
		assertAccountBalance(t, accIdentifier, creditAmt1+creditAmt2, creditAmt1+creditAmt2)

		// debit transaction should be successful as debit amount is less than credit amount
		debitTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:    "req-3",
			ActorId:         actorId,
			Amount:          debitAmt,
			AccountType:     accountType,
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		})
		assertForSuccessfulTransactRpcResponse(t, debitTxnRes, err)
		assertAccountBalance(t, accIdentifier, creditAmt1+creditAmt2-debitAmt, creditAmt1+creditAmt2-debitAmt)
	})

	// This checks validates that debit transaction (with pending debit allowed) should be successful when sufficient balance is present in the account.
	// For testing this it first credits the account with some balance and then debit the account with some lesser balance.
	t.Run("Debit with amount lesser than credit amount (pending debit allowed case)", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId = "actor-1"
		)
		accountType := accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())
		accIdentifier := svcModel.AccountIdentifier{ActorId: actorId, AccountType: accountType}
		var creditAmt1, creditAmt2, debitAmt int32 = 55, 35, 90

		// CREDIT transaction  should be successful
		creditRes1, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-1",
			ActorId:          actorId,
			Amount:           creditAmt1,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditRes1, err)
		assertAccountBalance(t, accIdentifier, creditAmt1, creditAmt1)

		// credit transaction  should be successful
		creditRes2, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-2",
			ActorId:          actorId,
			Amount:           creditAmt2,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditRes2, err)
		assertAccountBalance(t, accIdentifier, creditAmt1+creditAmt2, creditAmt1+creditAmt2)

		// debit transaction should be successful as debit amount is less than credit amount
		debitTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:    "req-3",
			ActorId:         actorId,
			Amount:          debitAmt,
			AccountType:     accountType,
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
			Options:         &accrualPb.TransactRequest_Options{IsPendingDebitAllowed: true},
		})
		assertForSuccessfulTransactRpcResponse(t, debitTxnRes, err)
		// adding sleep before check the account balance as txn is processed async
		// for pending debit allowed case, so waiting for the txn to get processed. and balance to get updated.
		time.Sleep(400 * time.Millisecond)
		assertAccountBalance(t, accIdentifier, creditAmt1+creditAmt2-debitAmt, creditAmt1+creditAmt2-debitAmt)
	})

	// This test validates that debit transaction (without pending debit allowed) should fail when sufficient balance is not present in the account.
	// For testing this it first credits the account with some balance and then debit the account with a greater balance.
	t.Run("Debit with amount greater than credit amount (pending debit not allowed case)", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId = "actor-2"
		)
		accountType := accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())
		accIdentifier := svcModel.AccountIdentifier{ActorId: actorId, AccountType: accountType}

		var creditAmt, debitAmt int32 = 100, 101

		// credit transaction
		creditTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-1",
			ActorId:          actorId,
			Amount:           creditAmt,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes, err)
		assertAccountBalance(t, accIdentifier, creditAmt, creditAmt)

		// debit transaction should fail, debit amount is greater than account balance
		debitTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:    "req-2",
			ActorId:         actorId,
			Amount:          debitAmt,
			AccountType:     accountType,
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		})
		assert.Nil(t, err)
		assert.NotNil(t, debitTxnRes)
		assert.Equal(t, rpc.StatusFailedPrecondition().GetCode(), debitTxnRes.Status.GetCode())
		assert.Zero(t, debitTxnRes.TransactionId)
	})

	// This test validates that debit transaction (with pending debit allowed) should make the available balance negative when sufficient balance is not present in the account.
	// For testing this it first credits the account with some balance and then debit the account (with pending debit allowed) with a greater balance.
	t.Run("Debit with amount greater than credit amount (pending debit allowed case)", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId = "actor-2"
		)
		accountType := accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())
		accIdentifier := svcModel.AccountIdentifier{ActorId: actorId, AccountType: accountType}

		var creditAmt, debitAmt int32 = 100, 101

		// credit transaction
		creditTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-1",
			ActorId:          actorId,
			Amount:           creditAmt,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes, err)
		assertAccountBalance(t, accIdentifier, creditAmt, creditAmt)

		// debit transaction should be successful
		debitTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:    "req-2",
			ActorId:         actorId,
			Amount:          debitAmt,
			AccountType:     accountType,
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
			Options:         &accrualPb.TransactRequest_Options{IsPendingDebitAllowed: true},
		})
		assertForSuccessfulTransactRpcResponse(t, debitTxnRes, err)
		// adding sleep before check the account balance as txn is processed async
		// for pending debit allowed case, so waiting for the txn to get processed. and balance to get updated.
		time.Sleep(400 * time.Millisecond)
		assertAccountBalance(t, accIdentifier, 0, creditAmt-debitAmt)
	})

	// This test validates that credits are getting expired timely and not being used for debit transactions post their expiry time.
	// For testing this it first credits the account with some balance having some expiry time and then waits (sleeps) till those credits
	// get expired and then try debit the account with a lesser balance that was credited earlier. Debit should fail as those credits
	// must have got expired.
	t.Run("Debit after credited amount has expired", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId = "actor-2"
		)
		accountType := accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())
		deltaDuration := 1 * time.Second
		// time post which credit will expire.
		currentPlusDeltaTime := timestampPb.New(time.Now().Add(deltaDuration))
		var creditAmt, debitAmt int32 = 100, 90

		// Crediting the account with creditAmt.
		creditTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-1",
			ActorId:          actorId,
			Amount:           creditAmt,
			AmountExpiryTime: currentPlusDeltaTime,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes, err)

		// Putting the thread to sleep so that the above credited balance gets expired.
		time.Sleep(deltaDuration)

		// Performing debit with amount lesser than credited earlier.
		// Is should failed as those previously credit amount must have got expired.
		debitTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:    "req-2",
			ActorId:         actorId,
			Amount:          debitAmt,
			AccountType:     accountType,
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		})
		assert.Nil(t, err)
		assert.NotNil(t, debitTxnRes)
		assert.Equal(t, rpc.StatusFailedPrecondition().GetCode(), debitTxnRes.Status.GetCode())
		assert.Zero(t, debitTxnRes.TransactionId)
	})

	// This test validates that credits are getting used for debit transactions if they haven't expired yet.
	// For testing this it first credits the account with some balance having some expiry time.
	// Then trying to debit the account with a lesser balance that was credited earlier. Debit should be successful
	// as those credits must not have have expired.
	t.Run("Debit just before credited amount was about to expire", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId = "actor-2"
		)
		accountType := accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())
		deltaDuration := 400 * time.Millisecond

		// time after which credit will expire
		currentPlusDeltaTime := timestampPb.New(time.Now().Add(deltaDuration))
		var creditAmt, debitAmt int32 = 100, 90

		// Transaction credits some amount into account.
		// Should be successful.
		creditTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-1",
			ActorId:          actorId,
			Amount:           creditAmt,
			AmountExpiryTime: currentPlusDeltaTime,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes, err)

		// This amount is lesser than credited earlier and those credits shouldn't have expired.
		// debit transaction should be successful.
		debitTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:    "req-2",
			ActorId:         actorId,
			Amount:          debitAmt,
			AccountType:     accountType,
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		})
		assertForSuccessfulTransactRpcResponse(t, debitTxnRes, err)
	})

	// This test validated that we don't allow same request reference ids for different transactions.
	// For this it first performs a credit transaction with some request refId and then tries to perform
	// another credit transaction with same request refId. The second transaction should fail.
	t.Run("Perform Transactions with same request refId", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		requestId := "req-11"

		// credit transaction
		creditTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     requestId,
			ActorId:          "actor-1",
			Amount:           100,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes, err)

		// credit transaction with duplicate request id
		txnRes2, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     requestId,
			ActorId:          "actor-2",
			Amount:           50,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assert.Nil(t, err)
		assert.NotNil(t, txnRes2)
		assert.Equal(t, rpc.StatusInternal().GetCode(), txnRes2.Status.GetCode())
		assert.Zero(t, txnRes2.TransactionId)
	})
}

func TestService_ReverseTransaction(t *testing.T) {

	// This test validates whether debit reversal is performed currently ans the account balance gets credited
	// by the amount of reversed debit transaction
	t.Run("Perform debit reversal ", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		accountIdentifier := svcModel.AccountIdentifier{
			ActorId:     "actor-1",
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		}
		deltaDuration1 := 5 * time.Minute
		// time after which credit will expire
		currentPlusDeltaTime1 := timestampPb.New(time.Now().Add(deltaDuration1))
		var creditAmt1, creditAmt2, debitAmount int32 = 50, 100, 70

		// Crediting the account with creditAmt1.
		creditTxnRes1, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-1",
			ActorId:          accountIdentifier.ActorId,
			Amount:           creditAmt1,
			AmountExpiryTime: currentPlusDeltaTime1,
			AccountType:      accountIdentifier.AccountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes1, err)
		assertAccountBalance(t, accountIdentifier, creditAmt1, creditAmt1)

		deltaDuration2 := 10 * time.Minute
		// time after which credit will expire
		currentPlusDeltaTime2 := timestampPb.New(time.Now().Add(deltaDuration2))

		// Crediting the account with creditAmt2.
		creditTxnRes2, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-2",
			ActorId:          accountIdentifier.ActorId,
			Amount:           creditAmt2,
			AmountExpiryTime: currentPlusDeltaTime2,
			AccountType:      accountIdentifier.AccountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes2, err)
		assertAccountBalance(t, accountIdentifier, creditAmt1+creditAmt2, creditAmt1+creditAmt2)

		// Debiting the account with debitAmt.
		debitTxnRefId := "req-3"
		debitTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:    debitTxnRefId,
			ActorId:         accountIdentifier.ActorId,
			Amount:          debitAmount,
			AccountType:     accountIdentifier.AccountType,
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		})
		assertForSuccessfulTransactRpcResponse(t, debitTxnRes, err)
		assertAccountBalance(t, accountIdentifier, creditAmt1+creditAmt2-debitAmount, creditAmt1+creditAmt2-debitAmount)

		// Reversing the above debit transaction
		reverseTxnRes, err := accrualSvcTs.service.ReverseTransaction(context.Background(), &accrualPb.ReverseTransactionRequest{
			RequestRefId: "req-4",
			TxnRefId:     debitTxnRefId,
		})
		assertSuccessfulReverseTxn(t, reverseTxnRes, err)
		assertAccountBalance(t, accountIdentifier, creditAmt1+creditAmt2, creditAmt1+creditAmt2)
	})

	// This test validates whether debit reversal is performed currently ans the account balance gets credited
	// by the amount of reversed debit transaction
	t.Run("Perform credit reversal ", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		accountIdentifier := svcModel.AccountIdentifier{
			ActorId:     "actor-1",
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		}
		var creditAmt int32 = 100

		// Crediting the account.
		creditTxnRefId := "req-1" // nolint goconst
		creditTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     creditTxnRefId,
			ActorId:          accountIdentifier.ActorId,
			Amount:           creditAmt,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountIdentifier.AccountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes, err)
		assertAccountBalance(t, accountIdentifier, creditAmt, creditAmt)

		// reversing the credit txn
		reverseTxnRes, err := accrualSvcTs.service.ReverseTransaction(context.Background(), &accrualPb.ReverseTransactionRequest{
			RequestRefId: "req-2",
			TxnRefId:     creditTxnRefId,
		})
		assertSuccessfulReverseTxn(t, reverseTxnRes, err)
		// reverse credit txns are resolved asynchronously, waiting for txn to get completed
		time.Sleep(time.Second)
		assertAccountBalance(t, accountIdentifier, 0, 0)
	})

	t.Run("reversal for not performed txn", func(t *testing.T) {
		reverseTxnRes, err := accrualSvcTs.service.ReverseTransaction(context.Background(), &accrualPb.ReverseTransactionRequest{
			RequestRefId: "req-1",                                // nolint goconst
			TxnRefId:     "352d7fcf-3a31-484c-96e4-a252f41c0f98", // random parent ref id, should be uuid
		})
		assert.Nil(t, err)
		assert.NotNil(t, reverseTxnRes)
		assert.NotEqual(t, rpc.StatusOk(), reverseTxnRes.Status)
		assert.Zero(t, reverseTxnRes.ReverseTransactionId)
	})

	t.Run("reversal for a txn which is a reverse of some txn", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		accountIdentifier := svcModel.AccountIdentifier{
			ActorId:     "actor-1",
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		}
		var creditAmt int32 = 100

		// Crediting the account.
		creditTxnRefId := "req-1" // nolint goconst
		creditTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     creditTxnRefId,
			ActorId:          accountIdentifier.ActorId,
			Amount:           creditAmt,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountIdentifier.AccountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes, err)
		assertAccountBalance(t, accountIdentifier, creditAmt, creditAmt)

		// reversing the credit txn
		reverseTxnRefId := "req-2"
		reverseTxnRes, err := accrualSvcTs.service.ReverseTransaction(context.Background(), &accrualPb.ReverseTransactionRequest{
			RequestRefId: reverseTxnRefId,
			TxnRefId:     creditTxnRefId,
		})
		assertSuccessfulReverseTxn(t, reverseTxnRes, err)
		// reverse credit txns are resolved asynchronously, waiting for txn to get completed
		time.Sleep(time.Second)
		assertAccountBalance(t, accountIdentifier, 0, 0)

		// should fail as we are trying to reverse a reverse txn performed above
		reverseTxnRes2, err := accrualSvcTs.service.ReverseTransaction(context.Background(), &accrualPb.ReverseTransactionRequest{
			RequestRefId: "req-3",
			TxnRefId:     reverseTxnRefId,
		})
		assert.Nil(t, err)
		assert.NotNil(t, reverseTxnRes2)
		assert.NotEqual(t, rpc.StatusOk(), reverseTxnRes2.Status)
		assert.Zero(t, reverseTxnRes2.ReverseTransactionId)
	})

	t.Run("multiple reversal for same credit txn", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		accountIdentifier := svcModel.AccountIdentifier{
			ActorId:     "actor-1",
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		}
		var creditAmt int32 = 100

		// Crediting the account.
		creditTxnRefId := "req-1" // nolint goconst
		creditTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     creditTxnRefId,
			ActorId:          accountIdentifier.ActorId,
			Amount:           creditAmt,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountIdentifier.AccountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes, err)

		// reversing the credit txn
		reverseTxnRes1, err := accrualSvcTs.service.ReverseTransaction(context.Background(), &accrualPb.ReverseTransactionRequest{
			RequestRefId: "req-2",
			TxnRefId:     creditTxnRefId,
		})
		assertSuccessfulReverseTxn(t, reverseTxnRes1, err)

		// reversing the same credit txn again
		reverseTxnRes2, err := accrualSvcTs.service.ReverseTransaction(context.Background(), &accrualPb.ReverseTransactionRequest{
			RequestRefId: "req-3",
			TxnRefId:     creditTxnRefId,
		})
		assert.Nil(t, err)
		assert.NotNil(t, reverseTxnRes2)
		assert.NotEqual(t, rpc.StatusOk(), reverseTxnRes2.Status)
		assert.Zero(t, reverseTxnRes2.ReverseTransactionId)
	})
}

func TestService_CheckConcurrentTransactions(t *testing.T) {

	// This test validates that account balance is updated properly in case of concurrent credit transactions.
	// For testing this we creating concurrent credit transactions and then validating that whether the final
	// account balance equals sum of the amount of these credit transactions.
	t.Run("Concurrent credit transactions on same account", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)

		accountIdentifier := svcModel.AccountIdentifier{
			ActorId:     "actor-1",
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		}
		// passing minimum expiry time such that those credits do not expire before this test completes
		totalCreditedAmount, _ := performConcurrentCreditTxns(t, accountIdentifier, 10, time.Minute)
		assertAccountBalance(t, accountIdentifier, totalCreditedAmount, totalCreditedAmount)
	})

	// This test validates that account balance is updated properly in case of concurrent credit and debit transactions.
	// For testing this we creating concurrent credit and debit transactions and then validating that whether the final
	// account balance equals sum of the amount of all credit transactions - sum of all debit txns.
	t.Run("Concurrent credit then concurrent debit transactions on same account", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		accountIdentifier := svcModel.AccountIdentifier{
			ActorId:     "actor-1",
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		}

		// passing minimum expiry time such that those credits do not expire before this test completes
		totalCreditedAmount, _ := performConcurrentCreditTxns(t, accountIdentifier, 2, 2*time.Minute)
		assertAccountBalance(t, accountIdentifier, totalCreditedAmount, totalCreditedAmount)

		// perform concurrent debit txns (with pending debit not allowed)
		maxTotalDebitAllowed1 := totalCreditedAmount - totalCreditedAmount/10
		totalDebitedAmount1, _ := performConcurrentDebitTxns(t, accountIdentifier, 4, 1, maxTotalDebitAllowed1, false)
		assertAccountBalance(t, accountIdentifier, totalCreditedAmount-totalDebitedAmount1, totalCreditedAmount-totalDebitedAmount1)

		// perform concurrent debit txns (with pending debit allowed)
		maxTotalDebitAllowed2 := totalCreditedAmount
		totalDebitedAmount2, _ := performConcurrentDebitTxns(t, accountIdentifier, 10, 100, maxTotalDebitAllowed2, true)
		// adding sleep before check the account balance as txn is processed async
		// for pending debit allowed case, so waiting for the txn to get processed. and balance to get updated.
		time.Sleep(400 * time.Millisecond)
		assertAccountBalance(t, accountIdentifier, int32(math.Max(float64(totalCreditedAmount-totalDebitedAmount1-totalDebitedAmount2), 0)), totalCreditedAmount-totalDebitedAmount1-totalDebitedAmount2)
	})

	t.Run("concurrent debit reversal", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		accountIdentifier := svcModel.AccountIdentifier{
			ActorId:     "actor-1",
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		}

		// passing minimum expiry time such that those credits do not expire before this test completes
		totalCreditedAmount, _ := performConcurrentCreditTxns(t, accountIdentifier, 2, 2*time.Minute)
		assertAccountBalance(t, accountIdentifier, totalCreditedAmount, totalCreditedAmount)

		maxTotalDebitAllowed := totalCreditedAmount - totalCreditedAmount/10
		totalDebitedAmount, debitTxnIds := performConcurrentDebitTxns(t, accountIdentifier, 4, 1, maxTotalDebitAllowed, false)
		assertAccountBalance(t, accountIdentifier, totalCreditedAmount-totalDebitedAmount, totalCreditedAmount-totalDebitedAmount)

		performTxnReversalConcurrently(t, debitTxnIds)
		assertAccountBalance(t, accountIdentifier, totalCreditedAmount, totalCreditedAmount)
	})

	t.Run("reverse all credit txns concurrently", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)

		accountIdentifier := svcModel.AccountIdentifier{
			ActorId:     "actor-1",
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		}
		// passing minimum expiry time such that those credits do not expire before this test completes
		totalCreditedAmount, creditTxnIds := performConcurrentCreditTxns(t, accountIdentifier, 10, time.Minute)
		assertAccountBalance(t, accountIdentifier, totalCreditedAmount, totalCreditedAmount)

		performTxnReversalConcurrently(t, creditTxnIds)

		// reverse credit txns are resolved asynchronously, waiting for all txn to get completed
		time.Sleep(time.Second)
		assertAccountBalance(t, accountIdentifier, 0, 0)
	})

	// one credit txn is not reversed
	t.Run("reverse credit txns concurrently leaving one credit txn", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)

		accountIdentifier := svcModel.AccountIdentifier{
			ActorId:     "actor-1",
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		}

		var creditTxn1Amt int32 = 100
		// Crediting the account.
		creditTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-1", // nolint goconst
			ActorId:          accountIdentifier.ActorId,
			Amount:           creditTxn1Amt,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountIdentifier.AccountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes, err)

		// passing minimum expiry time such that those credits do not expire before this test completes
		totalAmountCreditedConcurrently, creditTxnIds := performConcurrentCreditTxns(t, accountIdentifier, 10, time.Minute)
		assertAccountBalance(t, accountIdentifier, totalAmountCreditedConcurrently+creditTxn1Amt, totalAmountCreditedConcurrently+creditTxn1Amt)

		performTxnReversalConcurrently(t, creditTxnIds)

		// reverse credit txns are resolved asynchronously, waiting for all txn to get completed
		time.Sleep(time.Second)
		assertAccountBalance(t, accountIdentifier, creditTxn1Amt, creditTxn1Amt)
	})
}

// nolint: ignore
func TestService_CheckTransactionStatus(t *testing.T) {

	testCases := []struct {
		name                  string
		request               *accrualPb.CheckStatusRequest
		wantTransactionStatus accrualPb.TransactionStatus
	}{
		{
			name: "Txn Status of Not Performed Txn",
			request: &accrualPb.CheckStatusRequest{
				RequestRefId: "req-1", // nolint goconst
			},
			wantTransactionStatus: accrualPb.TransactionStatus_TRANSACTION_STATUS_NOT_PERFORMED,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
			response, err := accrualSvcTs.service.CheckTransactionStatus(context.Background(), tc.request)
			assert.Nil(t, err)
			assert.NotNil(t, response)
			assert.Equal(t, rpc.StatusOk(), response.Status)
			assert.Equal(t, tc.wantTransactionStatus, response.TransactionStatus)
		})
	}

	// This test validates checking the the status of a transaction that was successfully performed should be completed.
	t.Run("Txn Status of Completed transaction", func(t *testing.T) {
		requestRefId := "req-1" // nolint goconst
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)

		// Credit Transaction
		// Should be successful
		txnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     requestRefId,
			ActorId:          "actor-1",
			Amount:           100,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, txnRes, err)

		// checking status of above credit transaction
		// Should be completed  as transaction was successful.
		response, err := accrualSvcTs.service.CheckTransactionStatus(context.Background(), &accrualPb.CheckStatusRequest{RequestRefId: requestRefId})
		assert.Nil(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, rpc.StatusOk(), response.Status)
		assert.Equal(t, accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED, response.TransactionStatus)
	})

	// This test validates checking the the status of a transaction that was not successfully performed should be NOT_PERFORMED.
	t.Run("Txn Status of failed Debit transaction", func(t *testing.T) {
		requestRefId := "req-1" // nolint goconst

		// Debit transaction
		// Should fail as there wasn't sfficient balance in the account.
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		txnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:    requestRefId,
			ActorId:         "actor-1",
			Amount:          100,
			AccountType:     accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		})
		assert.Nil(t, err)
		assert.NotNil(t, txnRes)
		assert.NotEqual(t, rpc.StatusOk(), txnRes.Status)

		// checking status of above  debit transaction
		// should be NOT_PERFORMED as the debit transaction failed.
		response, err := accrualSvcTs.service.CheckTransactionStatus(context.Background(), &accrualPb.CheckStatusRequest{RequestRefId: requestRefId})
		assert.Nil(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, response.Status, rpc.StatusOk())
		assert.Equal(t, accrualPb.TransactionStatus_TRANSACTION_STATUS_NOT_PERFORMED, response.TransactionStatus)
	})

	// This test validates checking the the status of a transaction that was not successfully performed should be NOT_PERFORMED.
	t.Run("Txn Status of Debit transaction (with pending debit allowed)", func(t *testing.T) {
		requestRefId := "req-1" // nolint goconst

		// Debit transaction should move to pending state
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		txnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:    requestRefId,
			ActorId:         "actor-1",
			Amount:          100,
			AccountType:     accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
			Options:         &accrualPb.TransactRequest_Options{IsPendingDebitAllowed: true},
		})
		assertForSuccessfulTransactRpcResponse(t, txnRes, err)

		// checking status of above  debit transaction, it should be in 'PENDING' state
		response, err := accrualSvcTs.service.CheckTransactionStatus(context.Background(), &accrualPb.CheckStatusRequest{RequestRefId: requestRefId})
		assert.Nil(t, err)
		assert.NotNil(t, response)
		assert.Equal(t, response.Status, rpc.StatusOk())
		assert.Equal(t, accrualPb.TransactionStatus_TRANSACTION_STATUS_PENDING, response.TransactionStatus)
	})
}

func TestService_GetAccountDetails(t *testing.T) {
	t.Run("fetch new account", func(t *testing.T) {
		accountDetailsRes, err := accrualSvcTs.service.GetAccountDetails(context.Background(), &accrualPb.GetAccountDetailsRequest{
			ActorId:     "new-actor-1",
			AccountType: accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), accountDetailsRes.GetStatus().GetCode())
		assert.NotNil(t, accountDetailsRes.GetAccountId())
		assert.Zero(t, accountDetailsRes.GetAccountBalance())
	})

	t.Run("credit account and then account detail check", func(t *testing.T) {
		creditReq := &accrualPb.TransactRequest{
			RequestRefId:     "new-req-1",
			ActorId:          "new-actor-1",
			Amount:           100,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		}
		// Credit Transaction
		txnRes, err := accrualSvcTs.service.Transact(context.Background(), creditReq)
		assertForSuccessfulTransactRpcResponse(t, txnRes, err)

		// account balance and expiry check after credit
		accountDetailsRes, err := accrualSvcTs.service.GetAccountDetails(context.Background(), &accrualPb.GetAccountDetailsRequest{
			ActorId:     creditReq.GetActorId(),
			AccountType: creditReq.GetAccountType(),
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), accountDetailsRes.GetStatus().GetCode())
		assert.Equal(t, creditReq.GetAmount(), accountDetailsRes.GetAccountBalance())
		assert.Equal(t, creditReq.GetAmount(), accountDetailsRes.GetCurrentBalance())
		assert.Equal(t, creditReq.GetAmount(), accountDetailsRes.GetAvailableBalance())
		assert.Equal(t, creditReq.GetAmountExpiryTime().GetSeconds(), accountDetailsRes.GetNextExpiryTime().GetSeconds())
	})
}

func assertForSuccessfulTransactRpcResponse(t *testing.T, rpcRes *accrualPb.TransactResponse, rpcErr error) {
	assert.Nil(t, rpcErr)
	assert.NotNil(t, rpcRes)
	assert.Equal(t, rpc.StatusOk(), rpcRes.Status)
	assert.NotEmpty(t, rpcRes.TransactionId)
}

// assertAccountBalance fetches account balance and checks
// whether the account balance equals to some expected amount
func assertAccountBalance(t *testing.T, accountIdentifier svcModel.AccountIdentifier, expectedCurrentBalance, expectedAvailableBalance int32) {
	account, err := accrualSvcTs.accountDao.GetByActorIdAndAccountType(context.Background(), accountIdentifier.ActorId, accountIdentifier.AccountType)
	assert.Nil(t, err)
	assert.NotNil(t, account)

	if account.IsBalanceDirty() {
		account, err = accrualSvcTs.service.resolveBalance(context.Background(), account.AccountId)
		assert.Nil(t, err)
		assert.NotNil(t, account)
	}

	assert.Equal(t, expectedCurrentBalance, account.CurrentBalance, "current balance is not as expected")
	assert.Equal(t, expectedAvailableBalance, account.AvailableBalance, "available balance is not as expected")
}

// assertForReverseTxn asserts whether the reverse txn was successful
// @param res : reverse txn response
// @param errResponse : err repsonse
func assertSuccessfulReverseTxn(t *testing.T, res *accrualPb.ReverseTransactionResponse, errResponse error) {
	assert.Nil(t, errResponse)
	assert.NotNil(t, res)
	assert.Equal(t, rpc.StatusOk(), res.Status)
	assert.NotEmpty(t, res.ReverseTransactionId)
}

// Performs concurrent credit txns on an account with random amount (of at least 100) and random expiry time.
// fails if one of the transaction fails
// @param noOfCredits : no of txns
// @param accIdentifier : account involved in txn
// @param minExpiryDuration : min expiry duration of credits from current time like a duration
// of 5 minutes represnets all credits would expire at least 5 minutes after current time.
// returns the total credited amount and all the request ref ids of successful txns
func performConcurrentCreditTxns(t *testing.T, accIdentifier svcModel.AccountIdentifier, noOfCredits int, minExpiryDuration time.Duration) (creditedAmount int32, reqReferenceIds []string) {
	// asserts no of txns is greater than zero.
	assert.Greater(t, noOfCredits, 0)
	var wg1 sync.WaitGroup
	totalCreditedAmount := int32(0)
	failedTxnCount := int32(0)
	txnRequestRefIdChannel := make(chan string, noOfCredits)

	// sending concurrent credit transactions requests
	for i := 0; i < noOfCredits; i++ {
		wg1.Add(1)
		goroutine.RunWithCtx(context.Background(), func(ctx context.Context) {
			defer wg1.Done()
			// UNIQUE request referenceId for transaction
			requestRefId := uuid.New().String()
			// generating a random txn amount
			// keeping it at least 100 so that during debit (if performed) sufficient amount is available.
			randomAmount := rand.Int31n(1000) + 100
			// random amount expiry time,
			// such that the expiry time is at least minExpiryDuration ahead of current time
			randomDuration := time.Duration(rand.Intn(20))*time.Minute + minExpiryDuration
			amountExpiryTime := timestampPb.New(time.Now().Add(randomDuration))

			// sending a credit transaction request
			if res, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
				RequestRefId:     requestRefId,
				ActorId:          accIdentifier.ActorId,
				Amount:           randomAmount,
				AmountExpiryTime: amountExpiryTime,
				AccountType:      accIdentifier.AccountType,
				TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
			}); err != nil || !res.Status.IsSuccess() {
				logger.ErrorNoCtx("concurrent credit transaction failed", zap.Any("response", res))
				atomic.AddInt32(&failedTxnCount, 1)
				return
			} else {
				txnRequestRefIdChannel <- requestRefId
				// calculating the total credit amount till now
				atomic.AddInt32(&totalCreditedAmount, randomAmount)
			}
		})
	}
	// waiting for all credit transactions to finish
	waitgroup.SafeWait(&wg1, time.Minute)

	reqReferenceIdsCount := len(txnRequestRefIdChannel)
	for i := 0; i < reqReferenceIdsCount; i++ {
		reqReferenceIds = append(reqReferenceIds, <-txnRequestRefIdChannel)
	}

	t.Logf("failed concurrent credit txn count %v, total credited amount %v", failedTxnCount, totalCreditedAmount)
	assert.Equal(t, int32(0), failedTxnCount)
	assert.Greater(t, totalCreditedAmount, int32(0))
	return totalCreditedAmount, reqReferenceIds
}

// Performs concurrent debit txns on an account with random amount (of at least 1)
// fails if one of the transaction fails
// @param maxNoOfDebits : maximum no of txns allowed
// @param maxTotalDebitAllowed : maximum total debit allowed summing all txns
// @param accIdentifier : account involved in txn
// returns the total debited amount and all the request ref ids of successful txns
func performConcurrentDebitTxns(t *testing.T, accIdentifier svcModel.AccountIdentifier, maxNoOfDebits int, minTxnAmount int32, maxTotalDebitAllowed int32, isPendingDebitAllowed bool) (debitedAmount int32, reqReferenceIds []string) {
	// asserts no of txns is greater than zero.
	assert.Greater(t, maxNoOfDebits, 0)
	// asserts max total debit amout allowed is greater than zero.
	assert.Greater(t, maxTotalDebitAllowed, int32(0))

	var wg1 sync.WaitGroup

	// generating random debit Amounts such thst
	// total debit amount <= maxTotalDebitAllowed passed in argument
	// ans total no of txns <= maxNoOfDebits
	var randomDebitAmounts []int32
	var debitAmountTillNow int32 = 0
	for i := 0; i < maxNoOfDebits; i++ {
		// keeping random amount small enough so that can have atleast one txn
		// obeying debitAmountTillNow + randomAmount <= maxTotalDebitAllowed
		randomAmount := rand.Int31n(100) + minTxnAmount
		if debitAmountTillNow+randomAmount <= maxTotalDebitAllowed {
			randomDebitAmounts = append(randomDebitAmounts, randomAmount)
			debitAmountTillNow += randomAmount
		} else {
			break
		}
	}

	// asserting that at least one txn could be performed
	t.Logf("total concurrent debit txns %v total debit amount %v", len(randomDebitAmounts), debitAmountTillNow)
	totalDebitTxns := len(randomDebitAmounts)
	assert.Greater(t, totalDebitTxns, 0)

	// total amount debited by all the successful debit txns
	totalDebitedAmount := int32(0)
	failedTxnCount := int32(0)
	txnReqReferenceIdChannel := make(chan string, totalDebitTxns)

	// sending concurrent debit transactions requests using random amount generated
	for i := 0; i < totalDebitTxns; i++ {
		// passed i  as argument as is it not safe to use i directly inside goroutine
		// as it is a shared variable that can be concurrent updated.
		wg1.Add(1)
		// nocustomlint:goroutine
		go func(index int) {
			defer wg1.Done()
			// UNIQUE request referenceId for transaction
			requestRefId := uuid.New().String()
			// fetching a random txn amount
			randomAmount := randomDebitAmounts[index]

			req := &accrualPb.TransactRequest{
				RequestRefId:    requestRefId,
				ActorId:         accIdentifier.ActorId,
				Amount:          randomAmount,
				AccountType:     accIdentifier.AccountType,
				TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
				Options:         &accrualPb.TransactRequest_Options{IsPendingDebitAllowed: isPendingDebitAllowed},
			}
			// sending a debit transaction request
			if res, err := accrualSvcTs.service.Transact(context.Background(), req); err != nil || !res.Status.IsSuccess() {
				logger.ErrorNoCtx("concurrent debit transaction failed", zap.Any("response", res))
				atomic.AddInt32(&failedTxnCount, 1)
				return
			} else {
				txnReqReferenceIdChannel <- requestRefId
				// calculating the total debit amount till now
				atomic.AddInt32(&totalDebitedAmount, randomAmount)
			}
		}(i)
	}
	// waiting for all debit transactions to finish
	waitgroup.SafeWait(&wg1, time.Minute)

	// appending all txn ids to a slice
	reqReferenceIdsCount := len(txnReqReferenceIdChannel)
	for i := 0; i < reqReferenceIdsCount; i++ {
		reqReferenceIds = append(reqReferenceIds, <-txnReqReferenceIdChannel)
	}

	t.Logf("failed concurrent debit txn count %v, total debited amount %v", failedTxnCount, totalDebitedAmount)
	assert.Equal(t, int32(0), failedTxnCount)
	assert.Greater(t, totalDebitedAmount, int32(0))

	return totalDebitedAmount, reqReferenceIds
}

// Performs txn reversal for multiple txns concurrently
// @param reversalParentTxnRefIds : ref ids of txns that are to be reversed
func performTxnReversalConcurrently(t *testing.T, reversalParentTxnRefIds []string) {
	totalReversalReqs := len(reversalParentTxnRefIds)
	t.Logf("total concurrent reversal reqs %v", len(reversalParentTxnRefIds))

	assert.Greater(t, totalReversalReqs, 0)

	failedTxnCount := int32(0)
	var wg1 sync.WaitGroup

	for i := 0; i < totalReversalReqs; i++ {
		// passed i  as argument as is it not safe to use i directly inside goroutine
		// as it is a shared variable that can be concurrent updated.
		wg1.Add(1)
		// nocustomlint:goroutine
		go func(index int) {
			defer wg1.Done()
			// unique request referenceId for transaction
			requestRefId := uuid.New().String()
			parentTxnRefId := reversalParentTxnRefIds[index]

			if res, err := accrualSvcTs.service.ReverseTransaction(context.Background(), &accrualPb.ReverseTransactionRequest{
				TxnRefId:     parentTxnRefId,
				RequestRefId: requestRefId,
			}); err != nil || !res.Status.IsSuccess() {
				logger.ErrorNoCtx("concurrent reverse transaction failed", zap.Any("response", res))
				atomic.AddInt32(&failedTxnCount, 1)
				return
			}
		}(i)
	}

	waitgroup.SafeWait(&wg1, time.Minute)
	t.Logf("failed concurrent reverse txn count %v", failedTxnCount)
	assert.Equal(t, int32(0), failedTxnCount)
}

func TestService_ResolveBalance(t *testing.T) {
	// For testing this it first credits the account with some balance having some expiry time and then waits (sleeps) till those credits
	// get expired and then call resolve balance to expire the expired credits
	t.Run("Resolve balance after credited amount has expired", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId = "actor-2"
		)
		accountType := accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())
		deltaDuration := 1 * time.Second
		// time post which credit will expire.
		currentPlusDeltaTime := timestampPb.New(time.Now().Add(deltaDuration))
		var creditAmt int32 = 100

		// Crediting the account with creditAmt.
		creditTxnRes, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-1",
			ActorId:          actorId,
			Amount:           creditAmt,
			AmountExpiryTime: currentPlusDeltaTime,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditTxnRes, err)

		// Putting the thread to sleep so that the above credited balance gets expired.
		time.Sleep(deltaDuration)

		// get the account id
		account, err := accrualSvcTs.service.GetAccountDetails(context.Background(), &accrualPb.GetAccountDetailsRequest{
			ActorId:     actorId,
			AccountType: accountType,
		})
		assert.Nil(t, err)
		// Call resolve balance to resolve the expired credits
		debitTxnRes, err := accrualSvcTs.service.ResolveBalance(context.Background(), &accrualPb.ResolveBalanceRequest{
			AccountId: account.GetAccountId(),
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), debitTxnRes.Status.GetCode())
	})
}

// nolint: testifylint
func TestService_CloseAccount(t *testing.T) {
	t.Run("Successfully close an account with a positive balance", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)

		const (
			actorId = "actor-1"
		)
		var accountType = accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())
		var (
			accIdentifier                = svcModel.AccountIdentifier{ActorId: actorId, AccountType: accountType}
			creditAmt1, creditAmt2 int32 = 55, 45
		)

		// perform 1st credit transaction
		creditRes1, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-1",
			ActorId:          actorId,
			Amount:           creditAmt1,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditRes1, err)
		assertAccountBalance(t, accIdentifier, creditAmt1, creditAmt1)

		// perform 2nd credit transaction
		creditRes2, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:     "req-2",
			ActorId:          actorId,
			Amount:           creditAmt2,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assertForSuccessfulTransactRpcResponse(t, creditRes2, err)
		assertAccountBalance(t, accIdentifier, creditAmt1+creditAmt2, creditAmt1+creditAmt2)

		// close the accrual account
		closeAccountRes, err := accrualSvcTs.service.CloseAccount(context.Background(), &accrualPb.CloseAccountRequest{
			ActorId:     actorId,
			AccountType: accountType,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), closeAccountRes.GetStatus().GetCode())

		// Verify account details post account closure
		finalAccDetails, err := accrualSvcTs.accountDao.GetByActorIdAndAccountType(context.Background(), actorId, accountType)
		assert.Nil(t, err)
		assert.NotNil(t, finalAccDetails)
		assert.Zero(t, finalAccDetails.AvailableBalance)
		assert.Zero(t, finalAccDetails.CurrentBalance)
		assert.Equal(t, accrualPb.AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED, finalAccDetails.AccountOperationalStatus)

		// Verify that a debit transaction for account closure was created
		expiryTxnRefId := getAccountBalanceExpiryRefId(finalAccDetails.AccountId)
		expiryTxn, err := accrualSvcTs.transactionDao.GetByRefId(context.Background(), expiryTxnRefId)
		assert.Nil(t, err)
		if assert.NotNil(t, expiryTxn) {
			assert.Equal(t, int32(creditAmt1+creditAmt2), expiryTxn.Amount)
			assert.Equal(t, accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT, expiryTxn.TransactionType)
			assert.Equal(t, accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_ACCOUNT_CLOSURE_EXPIRY, expiryTxn.TransactionSubType)
		}
	})

	t.Run("Close an already closed account", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId = "actor-to-close-2"
		)
		var accountType = accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())

		// Close the account
		closeRes1, err := accrualSvcTs.service.CloseAccount(context.Background(), &accrualPb.CloseAccountRequest{
			ActorId:     actorId,
			AccountType: accountType,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), closeRes1.GetStatus().GetCode())

		// Close again
		closeRes2, err := accrualSvcTs.service.CloseAccount(context.Background(), &accrualPb.CloseAccountRequest{
			ActorId:     actorId,
			AccountType: accountType,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), closeRes2.GetStatus().GetCode())

		// Verify account details
		finalAccDetails, err := accrualSvcTs.service.GetAccountDetails(context.Background(), &accrualPb.GetAccountDetailsRequest{ActorId: actorId, AccountType: accountType})
		assert.Nil(t, err)
		assert.Equal(t, accrualPb.AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED, finalAccDetails.GetAccountOperationalStatus())
	})

	t.Run("Attempt to transact on a closed account", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId = "actor-to-close-3"
		)
		var accountType = accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())

		// Close the account
		closeRes, err := accrualSvcTs.service.CloseAccount(context.Background(), &accrualPb.CloseAccountRequest{
			ActorId:     actorId,
			AccountType: accountType,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), closeRes.GetStatus().GetCode())

		// Attempt to credit
		creditReq := &accrualPb.TransactRequest{
			RequestRefId:     "credit-after-close",
			ActorId:          actorId,
			Amount:           50,
			AmountExpiryTime: largeTimestamp,
			AccountType:      accountType,
			TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		}
		txnRes, err := accrualSvcTs.service.Transact(context.Background(), creditReq)
		assert.Nil(t, err)
		assert.NotEqual(t, rpc.StatusOk().GetCode(), txnRes.GetStatus().GetCode())
	})

	t.Run("Successfully close an account with zero balance", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId = "actor-zero-balance"
		)
		var accountType = accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now())
		// Create an account but don't credit it
		_, err := accrualSvcTs.service.getOrCreateAccount(context.Background(), &svcModel.AccountIdentifier{ActorId: actorId, AccountType: accountType})
		assert.Nil(t, err)

		// Close the account
		closeRes, err := accrualSvcTs.service.CloseAccount(context.Background(), &accrualPb.CloseAccountRequest{
			ActorId:     actorId,
			AccountType: accountType,
		})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), closeRes.GetStatus().GetCode())

		// Verify account is closed and balances are zero
		finalAccDetails, err := accrualSvcTs.accountDao.GetByActorIdAndAccountType(context.Background(), actorId, accountType)
		assert.Nil(t, err)
		assert.Equal(t, accrualPb.AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED, finalAccDetails.AccountOperationalStatus)
		assert.Zero(t, finalAccDetails.CurrentBalance)
		assert.Zero(t, finalAccDetails.AvailableBalance)

		// Verify that no expiry transaction was created
		expiryTxn, err := accrualSvcTs.transactionDao.GetByRefId(context.Background(), getAccountBalanceExpiryRefId(finalAccDetails.AccountId))
		assert.Error(t, err) // Expect a "record not found" error
		assert.Nil(t, expiryTxn)
	})

	t.Run("Successfully close an account with small pending debits", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId               = "actor-pending-debit"
			accountType           = accrualPb.AccountType_ACCOUNT_TYPE_FICOINS
			creditAmt       int32 = 250
			pendingDebitAmt int32 = 50
		)
		accIdentifier := svcModel.AccountIdentifier{ActorId: actorId, AccountType: accountType}

		// 1. Credit the account
		_, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId: "credit-for-pending-debit-test", ActorId: actorId, Amount: creditAmt,
			AmountExpiryTime: largeTimestamp, AccountType: accountType, TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assert.Nil(t, err)
		assertAccountBalance(t, accIdentifier, creditAmt, creditAmt)

		// 2. Create a pending debit
		_, err = accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId:    "pending-debit-for-close-test",
			ActorId:         actorId,
			Amount:          pendingDebitAmt,
			AccountType:     accountType,
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
			Options: &accrualPb.TransactRequest_Options{
				IsPendingDebitAllowed: true,
			},
		})
		assert.Nil(t, err)
		// Available balance is reduced, current balance is not
		assertAccountBalance(t, accIdentifier, creditAmt, creditAmt-pendingDebitAmt)

		// 3. Close the account
		closeRes, err := accrualSvcTs.service.CloseAccount(context.Background(), &accrualPb.CloseAccountRequest{ActorId: actorId, AccountType: accountType})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), closeRes.GetStatus().GetCode())

		// 4. Verify the final state
		finalAccDetails, err := accrualSvcTs.accountDao.GetByActorIdAndAccountType(context.Background(), actorId, accountType)
		assert.Nil(t, err)
		assert.Equal(t, accrualPb.AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED, finalAccDetails.AccountOperationalStatus)
		assert.Zero(t, finalAccDetails.CurrentBalance)
		assert.Zero(t, finalAccDetails.AvailableBalance)

		// 5. Verify the closure debit was for the remaining amount
		expiryTxn, err := accrualSvcTs.transactionDao.GetByRefId(context.Background(), getAccountBalanceExpiryRefId(finalAccDetails.AccountId))
		assert.Nil(t, err)
		if assert.NotNil(t, expiryTxn) {
			assert.Equal(t, creditAmt-pendingDebitAmt, expiryTxn.Amount)
		}
	})

	t.Run("Successfully close an account with a large pending debit", func(t *testing.T) {
		pkgTestV2.TruncateTestDatabaseTables(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
		const (
			actorId               = "actor-large-pending-debit"
			accountType           = accrualPb.AccountType_ACCOUNT_TYPE_FICOINS
			creditAmt       int32 = 250
			pendingDebitAmt int32 = 300
		)
		accIdentifier := svcModel.AccountIdentifier{ActorId: actorId, AccountType: accountType}

		// 1. Credit the account
		_, err := accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId: "credit-for-large-debit-test", ActorId: actorId, Amount: creditAmt,
			AmountExpiryTime: largeTimestamp, AccountType: accountType, TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		})
		assert.Nil(t, err)
		assertAccountBalance(t, accIdentifier, creditAmt, creditAmt)

		// 2. Create a pending debit larger than the credit
		_, err = accrualSvcTs.service.Transact(context.Background(), &accrualPb.TransactRequest{
			RequestRefId: "large-pending-debit-for-close-test", ActorId: actorId, Amount: pendingDebitAmt,
			AccountType: accountType, TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
			Options: &accrualPb.TransactRequest_Options{IsPendingDebitAllowed: true},
		})
		assert.Nil(t, err)
		// Available balance becomes negative
		assertAccountBalance(t, accIdentifier, creditAmt, creditAmt-pendingDebitAmt)

		// 3. Wait for async debit resolution to partially process the debit
		time.Sleep(100 * time.Millisecond)
		// After async processing, current balance is now 0, but a pending amount of 50 remains.
		assertAccountBalance(t, accIdentifier, 0, -50)

		// 4. Close the account
		closeRes, err := accrualSvcTs.service.CloseAccount(context.Background(), &accrualPb.CloseAccountRequest{ActorId: actorId, AccountType: accountType})
		assert.Nil(t, err)
		assert.Equal(t, rpc.StatusOk().GetCode(), closeRes.GetStatus().GetCode())

		// 5. Verify the final state
		finalAccDetails, err := accrualSvcTs.accountDao.GetByActorIdAndAccountType(context.Background(), actorId, accountType)
		assert.Nil(t, err)
		assert.Equal(t, accrualPb.AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED, finalAccDetails.AccountOperationalStatus)
		assert.Equal(t, int32(0), finalAccDetails.CurrentBalance)                           // Current balance is 0 as it was debited by async job
		assert.Equal(t, int32(creditAmt-pendingDebitAmt), finalAccDetails.AvailableBalance) // Available reflects the remaining pending debit

		// 6. Verify that NO new closure debit transaction was created
		expiryTxn, err := accrualSvcTs.transactionDao.GetByRefId(context.Background(), getAccountBalanceExpiryRefId(finalAccDetails.AccountId))
		assert.Error(t, err) // Expect a "record not found" error
		assert.Nil(t, expiryTxn)
	})
}

func TestService_GetFiCoinsToPointsMigrationDetails(t *testing.T) {
	pkgTestV2.TruncateAndPopulateRdsFixtures(t, accrualSvcTs.db, accrualSvcTs.conf.EpifiDb.GetName(), affectedTables)
	actorId := "Actor4"
	a := require.New(t)
	resp, err := accrualSvcTs.service.GetFiCoinsToPointsMigrationDetails(context.Background(), &accrualPb.GetFiCoinsToPointsMigrationDetailsRequest{
		ActorId: actorId,
	})
	finalErr := epifigrpc.RPCError(resp, err)
	a.NoError(finalErr, "error in getting fi coins to points migration details")
	a.Equal(int32(600), resp.GetTotalFiCoinsMigrated())
	a.Equal(int32(72), resp.GetTotalFiPointsMigrated())

	// for new actor, migration details should be empty and should not error
	newActorId := "newActor123"
	resp, err = accrualSvcTs.service.GetFiCoinsToPointsMigrationDetails(context.Background(), &accrualPb.GetFiCoinsToPointsMigrationDetailsRequest{
		ActorId: newActorId,
	})
	finalErr = epifigrpc.RPCError(resp, err)
	a.NoError(finalErr, "error in getting fi coins to points migration details")
	a.Equal(int32(0), resp.GetTotalFiCoinsMigrated())
	a.Equal(int32(0), resp.GetTotalFiPointsMigrated())
}
