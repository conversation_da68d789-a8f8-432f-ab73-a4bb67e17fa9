package accrual

import (
	"context"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	accrualPb "github.com/epifi/gamma/api/accrual"

	"github.com/epifi/gamma/accrual/dao"
	accrualError "github.com/epifi/gamma/accrual/error"
	svcModel "github.com/epifi/gamma/accrual/model"
	"github.com/epifi/gamma/accrual/wire/types"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

type Service struct {
	txnExecutor                         storageV2.TxnExecutor
	domainIdGenerator                   idgen.IdGenerator
	accountDao                          dao.AccountDao
	transactionDao                      dao.TransactionDao
	pointBucketDao                      dao.PointBucketDao
	txnSplitDao                         dao.TransactionSplitDao
	isFiCoinsToFiPointsMigrationEnabled types.IsFiCoinsToFiPointsMigrationEnabled
}

func NewService(txnExecutor storageV2.TxnExecutor, idGen idgen.IdGenerator, accountDao dao.AccountDao, transactionDao dao.TransactionDao, pointBucketDao dao.PointBucketDao, txnSplitDao dao.TransactionSplitDao, isFiCoinsToFiPointsMigrationEnabled types.IsFiCoinsToFiPointsMigrationEnabled) *Service {
	return &Service{
		txnExecutor:                         txnExecutor,
		domainIdGenerator:                   idGen,
		accountDao:                          accountDao,
		transactionDao:                      transactionDao,
		pointBucketDao:                      pointBucketDao,
		txnSplitDao:                         txnSplitDao,
		isFiCoinsToFiPointsMigrationEnabled: isFiCoinsToFiPointsMigrationEnabled,
	}
}

type deltaUpdateWithSplitReq struct {
	txnId               string
	bucketId            string
	deltaAmountWithSign int32
}

type retryPendingDebitTxnResponse struct {
	// whether all the pending debit txns are complete
	allPendingTxnsCompleted bool

	// whether we should try to resolve the next  pending txn
	shouldTryFuther bool
}

func (d deltaUpdateWithSplitReq) Validate() error {
	if d.txnId == "" {
		return errors.New("deltaUpdateWithSplitReq : txnId is nil")
	}
	if d.bucketId == "" {
		return errors.New("deltaUpdateWithSplitReq : bucketId is nil")
	}
	if d.deltaAmountWithSign == 0 {
		return errors.New("deltaUpdateWithSplitReq : deltaAmountWithSign is 0")
	}
	return nil
}

// Transact performs transaction of some given amount on a given actor's account.
// nolint: funlen;
func (s *Service) Transact(ctx context.Context, req *accrualPb.TransactRequest) (*accrualPb.TransactResponse, error) {
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return &accrualPb.TransactResponse{Status: rpc.StatusUnavailableWithDebugMsg(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error())}, nil
	}

	// fetch account details
	account, err := s.getOrCreateAccount(ctx, &svcModel.AccountIdentifier{ActorId: req.GetActorId(), AccountType: req.GetAccountType()})
	if err != nil {
		logger.Error(ctx, "error fetching or creating account", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &accrualPb.TransactResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching or creating account : " + err.Error())}, nil
	}
	if account.AccountOperationalStatus == accrualPb.AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED {
		logger.Error(ctx, "account is already closed", zap.Any(logger.REQUEST, req))
		return &accrualPb.TransactResponse{Status: rpc.StatusInternalWithDebugMsg("account is already closed")}, nil
	}
	if account.IsBalanceDirty() {
		if account, err = s.resolveBalance(ctx, account.AccountId); err != nil {
			logger.Error(ctx, "error resolving account balance", zap.Any(logger.REQUEST, req), zap.Error(err))
			return &accrualPb.TransactResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
	}

	txnRes, err := s.performTransaction(ctx, &svcModel.TransactRequest{
		RefId:                   req.GetRequestRefId(),
		AccountId:               account.AccountId,
		AccountAvailableBalance: account.AvailableBalance,
		TxnAmount:               req.GetAmount(),
		TransactionType:         req.GetTransactionType(),
		BalanceExpiryTime:       aws.Time(datetime.TimestampToTime(req.GetAmountExpiryTime())),
		IsPendingDebitAllowed:   req.GetOptions().GetIsPendingDebitAllowed(),
		MetaData:                req.GetTxnMetaData(),
	})
	if err != nil {
		logger.Error(ctx, "error performing transaction", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &accrualPb.TransactResponse{Status: accrualError.GetRpcStatusFromError(err)}, nil
	}

	return &accrualPb.TransactResponse{
		Status:            rpc.StatusOk(),
		TransactionId:     txnRes.TxnId,
		TransactionStatus: txnRes.TxnStatus,
	}, nil
}

func (s *Service) performTransaction(ctx context.Context, req *svcModel.TransactRequest) (*svcModel.TransactResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, err
	}
	switch req.TransactionType {
	case accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT:
		return s.performCreditTransaction(ctx, req)
	case accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT:
		return s.performDebitTransaction(ctx, req)
	default:
		// this can be taken care of in proto validation of request
		return nil, errors.New("invalid transaction type")
	}
}

// performCreditTransaction performs a credit txn on a given account, it credits an account with given amount,
// the amount would have some expiry attached to it.
// nolint:funlen
func (s *Service) performCreditTransaction(ctx context.Context, req *svcModel.TransactRequest) (*svcModel.TransactResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, err
	}

	var (
		txnId string
		err   error
	)

	if checkIfOngoingDbTxn(ctx) {
		// if there is an ongoing db transaction, perform credit txn in that transaction block.
		txnId, err = s.performCreditTransactionInOngoingDbTxn(ctx, req)
		if err != nil {
			return nil, err
		}
	} else {
		// perform credit txn in a db transaction block.
		// **Important** : use only "txnCtx" for propagation context inside this transaction block.
		txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
			txnId, err = s.performCreditTransactionInOngoingDbTxn(txnCtx, req)
			return err
		})
		if txnErr != nil {
			return nil, txnErr
		}
	}

	s.resolvePendingDebitTxnsIfApplicable(ctx, req.AccountId)

	return &svcModel.TransactResponse{
		TxnId:     txnId,
		TxnStatus: accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED,
	}, nil
}

// calculateAndUpdateAccountBalance computes and updates the latest account balance (current and available both) in the account db entry.
func (s *Service) calculateAndUpdateAccountBalance(ctx context.Context, accountId string) (*svcModel.Account, error) {
	// get total point balance
	pointBalanceInfo, err := s.pointBucketDao.GetTotalPointBalanceWithNextExpiry(ctx, accountId)
	if err != nil {
		return nil, err
	}
	currentBalance := pointBalanceInfo.TotalPointBalance

	// get total pending debit amount on the account for computing the available balance.
	totalPendingDebitAmount, err := s.transactionDao.GetTotalPendingDebitAmount(ctx, accountId)
	if err != nil {
		return nil, err
	}

	account, err := s.accountDao.UpdateBalance(ctx, &svcModel.UpdateAccBalanceRequest{
		AccountId:         accountId,
		NewCurrentBalance: currentBalance,
		// available balance = currentBalance - totalPendingDebitAmount
		NewAvailableBalance: currentBalance - totalPendingDebitAmount,
		NewExpiryTime:       pointBalanceInfo.NextExpiryTime,
	})
	if err != nil {
		return nil, err
	}

	return account, nil
}

// performDebitTransaction performs a debit txn on a given account, it first tries to resolve all the pending debit txns on
// the given account, once all of them are resolved only then this debit could be performed, otherwise an error would be returned.
// nolint: funlen
func (s *Service) performDebitTransaction(ctx context.Context, req *svcModel.TransactRequest) (*svcModel.TransactResponse, error) {
	if err := req.Validate(); err != nil {
		return nil, err
	}

	var (
		accountId             = req.AccountId
		isPendingDebitAllowed = req.IsPendingDebitAllowed
		isOngoingDbTxn        = checkIfOngoingDbTxn(ctx)

		txnId string
		err   error
	)

	// if pending debit is NOT allowed, check if sufficient balance is available in the account for debit and then only create a txn.
	if !isPendingDebitAllowed {
		// resolve all the pending debits on the given account
		allPendingDebitsCompleted := s.resolvePendingDebitTxns(ctx, accountId)
		if !allPendingDebitsCompleted {
			return nil, errors.New("could not perform debit due to pending debit txns")
		}

		if isOngoingDbTxn {
			txnId, err = s.performDebitTransactionWithPendingDebitNotAllowedInOngoingDbTxn(ctx, req)
			if err != nil {
				return nil, err
			}
		} else {
			// **Important** : use only 'txnCtx' for propagation context inside this transaction block.
			txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
				txnId, err = s.performDebitTransactionWithPendingDebitNotAllowedInOngoingDbTxn(txnCtx, req)
				if err != nil {
					return err
				}
				return nil
			})
			if txnErr != nil {
				return nil, txnErr
			}
		}

		return &svcModel.TransactResponse{
			TxnId:     txnId,
			TxnStatus: accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED,
		}, nil
	} else {

		if isOngoingDbTxn {
			txnId, err = s.performDebitTransactionWithPendingDebitAllowedInOngoingDbTxn(ctx, req)
			if err != nil {
				return nil, err
			}
		} else {
			// pending debit is allowed, so create a debit txn in PENDING state and let it process async.
			txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
				txnId, err = s.performDebitTransactionWithPendingDebitAllowedInOngoingDbTxn(txnCtx, req)
				if err != nil {
					return err
				}
				return nil
			})
			if txnErr != nil {
				return nil, txnErr
			}
		}

		s.resolvePendingDebitTxnsIfApplicable(ctx, accountId)

		return &svcModel.TransactResponse{
			TxnId:     txnId,
			TxnStatus: accrualPb.TransactionStatus_TRANSACTION_STATUS_PENDING,
		}, nil
	}
}

// resolveBalance resolves balance of the account after expiring all the expired credits.
func (s *Service) resolveBalance(ctx context.Context, accountId string) (*svcModel.Account, error) {
	var (
		account *svcModel.Account
		err     error
	)

	// check if there is an ongoing db transaction, if yes then resolve balance in that transaction.
	if checkIfOngoingDbTxn(ctx) {
		return s.resolveBalanceInOngoingDbTxn(ctx, accountId)
	}

	if txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		if account, err = s.resolveBalanceInOngoingDbTxn(txnCtx, accountId); err != nil {
			return err
		}
		return nil
	}); txnErr != nil {
		return nil, txnErr
	}

	return account, nil
}

// resolveBalanceInOngoingDbTxn resolves balance of the account after expiring all the expired credits in an ongoing db txn.
// If no ongoing db txn is found, it returns an error.
// nolint: funlen
func (s *Service) resolveBalanceInOngoingDbTxn(txnCtx context.Context, accountId string) (*svcModel.Account, error) {
	// acquiring lock to prevent concurrent updates on the same account.
	if _, err := s.accountDao.GetByIdWithLock(txnCtx, accountId); err != nil {
		return nil, errors.Wrap(err, "error fetching account by id with lock")
	}

	currentTime := time.Now()

	// fetch all the available buckets having expiry time less than current time, all these buckets should be marked expired now.
	expiredPointBuckets, err := s.pointBucketDao.GetAvailableBucketsWithExpiryTimeLessThan(txnCtx, accountId, currentTime)
	if err != nil {
		return nil, err
	}

	for _, bucket := range expiredPointBuckets {
		// create debit txn for expired buckets that had some balance
		if bucket.CurrentBalance > 0 {
			expiredTxnRefId, idGenErr := s.domainIdGenerator.Get(idgen.SystemGenerated)
			if idGenErr != nil {
				return nil, errors.Wrap(idGenErr, "error in generating id while resolving balance")
			}

			txnId, createErr := s.transactionDao.Create(txnCtx, &svcModel.CreateTxnReq{
				AccountId:          accountId,
				TransactionType:    accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
				TransactionSubType: accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_EXPIRY,
				Amount:             bucket.CurrentBalance,
				Status:             accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED,
				RequestRef:         expiredTxnRefId,
				EffectiveTxnDate:   *bucket.ExpiryTime,
			})
			if createErr != nil {
				return nil, errors.Wrap(createErr, "error while creating expired point bucket's transaction")
			}

			// update balance in point bucket to zero
			if err = s.deltaUpdatePointsAndCreateSplitInOngoingDbTxn(txnCtx, deltaUpdateWithSplitReq{
				txnId:               txnId,
				bucketId:            bucket.ID,
				deltaAmountWithSign: -bucket.CurrentBalance,
			}); err != nil {
				return nil, err
			}
		}
	}

	var expiredBucketIds []string
	for _, bucket := range expiredPointBuckets {
		expiredBucketIds = append(expiredBucketIds, bucket.ID)
	}
	if len(expiredBucketIds) > 0 {
		if err = s.pointBucketDao.UpdateStatusTo(txnCtx, expiredBucketIds, svcModel.EXPIRED); err != nil {
			return nil, err
		}
	}

	// compute and update the account balance after debiting balances of all the expired buckets
	account, err := s.calculateAndUpdateAccountBalance(txnCtx, accountId)
	if err != nil {
		return nil, err
	}

	return account, nil
}

// getOrCreateAccount fetches account details if account is present otherwise creates an account with zero balance.
func (s *Service) getOrCreateAccount(ctx context.Context, req *svcModel.AccountIdentifier) (*svcModel.Account, error) {
	account, err := s.accountDao.GetByActorIdAndAccountType(ctx, req.ActorId, req.AccountType)
	switch {
	// if account does not already exist, create it now
	case err != nil && errors.Is(err, epifierrors.ErrRecordNotFound):
		if account, err = s.createAccount(ctx, req); err != nil {
			return nil, err
		}
	case err != nil:
		return nil, err
	}

	return account, nil
}

func (s *Service) createAccount(ctx context.Context, req *svcModel.AccountIdentifier) (*svcModel.Account, error) {
	// creating account entry in db
	account, err := s.accountDao.Create(ctx, &svcModel.CreateAccountRequest{
		AccIdentifier:            req,
		AccountOperationalStatus: accrualPb.AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_ACTIVE,
		OpeningBalance:           int32(0),
		BalanceNextExpiryTime:    time.Now(),
	})
	if err != nil {
		if errors.Is(err, epifierrors.ErrDuplicateEntry) {
			return s.accountDao.GetByActorIdAndAccountType(ctx, req.ActorId, req.AccountType)
		}
		return nil, err
	}
	return account, nil
}

// CheckTransactionStatus rpc is useful to get the status of an already initiated transaction.
func (s *Service) CheckTransactionStatus(ctx context.Context, req *accrualPb.CheckStatusRequest) (*accrualPb.CheckStatusResponse, error) {
	txn, err := s.transactionDao.GetByRefId(ctx, req.RequestRefId)
	switch {
	// if txn is not found in db, return 'NOT_PERFORMED' status
	case err != nil && errors.Is(err, epifierrors.ErrRecordNotFound):
		return &accrualPb.CheckStatusResponse{
			Status:            rpc.StatusOk(),
			TransactionStatus: accrualPb.TransactionStatus_TRANSACTION_STATUS_NOT_PERFORMED,
		}, nil
	case err != nil:
		logger.Error(ctx, "error fetching txn by refId", zap.String(logger.REFERENCE_ID, req.GetRequestRefId()), zap.Error(err))
		return &accrualPb.CheckStatusResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &accrualPb.CheckStatusResponse{
		Status:            rpc.StatusOk(),
		TransactionStatus: txn.Status,
		TxnId:             txn.ID,
	}, nil
}

// ReverseTransaction rpc is useful to perform a reversal of an already initiated txn.
func (s *Service) ReverseTransaction(ctx context.Context, req *accrualPb.ReverseTransactionRequest) (*accrualPb.ReverseTransactionResponse, error) {
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return &accrualPb.ReverseTransactionResponse{Status: rpc.StatusUnavailableWithDebugMsg(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error())}, nil
	}

	// fetch the parent txn (that is to be reversed)
	parentTxn, err := s.transactionDao.GetByRefId(ctx, req.TxnRefId)
	switch {
	// txn to be reversed does not exists
	case err != nil && errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "txn to be reversed does not exists", zap.Any(logger.REQUEST, req))
		return &accrualPb.ReverseTransactionResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("txn to be reversed does not exists")}, nil

	case err != nil:
		logger.Error(ctx, "error fetching parent txn", zap.Any("rev_txn_request", req), zap.Error(err))
		return &accrualPb.ReverseTransactionResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	// in following cases a txn cannot be reversed
	// 1. if txn to be reversed is an expiry type of transaction (created as a results of points expiry)
	// 2. if txn to be reversed is itself a reverse of some txn.
	// 3. if the txn to be reversed isn't completed yet.
	case parentTxn.TransactionSubType == accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_EXPIRY || parentTxn.TransactionSubType == accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_REVERSE || parentTxn.Status != accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED:
		logger.Error(ctx, "parent txn cannot be reversed", zap.Any(logger.REQUEST, req), zap.Any("parent_txn", parentTxn))
		return &accrualPb.ReverseTransactionResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("parent txn cannot be reversed")}, nil
	}

	// check if reverse is already initiated for the requested txn
	isReverseAlreadyInitiated, err := s.isReversalAlreadyInitiatedForTxn(ctx, parentTxn.ID)
	switch {
	case err != nil:
		logger.Error(ctx, "error checking if txn was already reversed", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &accrualPb.ReverseTransactionResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil

	// reverse was already initiated for the requested txn.
	case isReverseAlreadyInitiated:
		logger.Error(ctx, "reversed already initiated ", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &accrualPb.ReverseTransactionResponse{Status: rpc.ExtendedStatusAlreadyProcessed()}, nil
	}

	// perform reverse txn
	reverseTxnResponse, err := s.performReverseTransaction(ctx, req.RequestRefId, parentTxn)
	if err != nil {
		logger.Error(ctx, "error during reversal", zap.Any(logger.REQUEST, req), zap.Error(err))
		return &accrualPb.ReverseTransactionResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	reverseTxnResponse.Status = rpc.StatusOk()
	return reverseTxnResponse, nil
}

// performReverseTransaction calls credit or debit reversal based on parent transaction type
// if parent txn is of credit type then perform credit reversal
// otherwise if parent txn is of debit type perform debit reversal
func (s *Service) performReverseTransaction(ctx context.Context, refID string, parentTxn *svcModel.Transaction) (*accrualPb.ReverseTransactionResponse, error) {
	switch parentTxn.TransactionType {
	case accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT:
		return s.performCreditReversal(ctx, refID, parentTxn)
	case accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT:
		return s.performDebitReversal(ctx, refID, parentTxn)
	default:
		return nil, errors.New("invalid parent transaction type")
	}
}

// performCreditReversal performs reversal of a credit txn i.e. it performs the debit of amount equal to the amount of credit txn.
// nolint: funlen
func (s *Service) performCreditReversal(ctx context.Context, refId string, parentTxn *svcModel.Transaction) (*accrualPb.ReverseTransactionResponse, error) {
	var (
		txnId       string
		err         error
		accountId   = parentTxn.AccountId
		currentTime = time.Now()
	)

	// **IMPORTANT** : use only "txnCtx" for context inside following scope.
	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// acquire lock to prevent concurrent updates on the same account.
		if _, err = s.accountDao.GetByIdWithLock(txnCtx, accountId); err != nil {
			return err
		}

		isAlreadyReversed, err := s.isReversalAlreadyInitiatedForTxn(txnCtx, parentTxn.ID)
		switch {
		case err != nil:
			logger.Error(ctx, "error checking for already initiated reverse", zap.String("parent_txn_id", parentTxn.ID))
			return errors.Wrap(err, "error checking for already initiated reverse")
		case isAlreadyReversed:
			logger.Error(ctx, "reverse already initiated for this transaction", zap.String("parent_txn_id", parentTxn.ID))
			return errors.Wrap(err, "reverse already initiated for this transaction")
		}

		var parentTxnId = parentTxn.ID
		var parentTxnAmt = parentTxn.Amount

		// just creating a transaction entry in pending state not debiting any amount now
		// **Note** : this is intentional as pending txns queue should ideally be consumed in earlier txn first order so just queueing the txn
		// as PENDING here and not trying to process it.
		if txnId, err = s.transactionDao.Create(txnCtx, &svcModel.CreateTxnReq{
			AccountId:            accountId,
			TransactionType:      accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
			TransactionSubType:   accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_REVERSE,
			Amount:               parentTxnAmt,
			PendingAmount:        parentTxnAmt,
			Status:               accrualPb.TransactionStatus_TRANSACTION_STATUS_PENDING,
			ParentTransactionRef: &parentTxnId,
			RequestRef:           refId,
			EffectiveTxnDate:     currentTime,
		}); err != nil {
			return errors.Wrap(err, "error creating reverse transaction")
		}

		// **Note** : balance update call should be made **ONLY** after txn is created and point buckets balances are updated.
		if _, err = s.calculateAndUpdateAccountBalance(txnCtx, accountId); err != nil {
			return errors.Wrap(err, "error updating account balance")
		}

		return nil
	})
	if txnErr != nil {
		return nil, errors.Wrap(txnErr, "error creating credit reversal txn")
	}

	s.resolvePendingDebitTxnsIfApplicable(ctx, accountId)

	return &accrualPb.ReverseTransactionResponse{ReverseTransactionId: txnId, TransactionStatus: accrualPb.TransactionStatus_TRANSACTION_STATUS_PENDING}, nil
}

// resolvePendingDebitTxns tries to resolve all the pending debit transactions of the given account
// returns true if all the pending debits are resolved, false otherwise
func (s *Service) resolvePendingDebitTxns(ctx context.Context, accountId string) bool {
	var (
		retryCount, maxRetries = 0, 1000
		resp                   *retryPendingDebitTxnResponse
		err                    error
	)

	for {
		if checkIfOngoingDbTxn(ctx) {
			resp, err = s.retryPendingDebitTxnInOngoingDbTxn(ctx, accountId)
			if err != nil {
				logger.Error(ctx, "error retrying pending debit txn", zap.Error(err))
				return false
			}
		} else {
			if txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
				if resp, err = s.retryPendingDebitTxnInOngoingDbTxn(txnCtx, accountId); err != nil {
					return err
				}
				return nil
			}); txnErr != nil {
				logger.Error(ctx, "error retrying pending debit txn", zap.Error(txnErr))
				return false
			}
		}

		if resp.allPendingTxnsCompleted || !resp.shouldTryFuther {
			break
		}

		if retryCount >= maxRetries {
			logger.Error(ctx, "max retries reached in resolvePendingDebitTxns", zap.Int("maxRetries", maxRetries))
			break
		}
		retryCount++
	}

	return resp.allPendingTxnsCompleted
}

// nolint: funlen
// This method tries to complete the first pending debit txn of the account passed to it in argument.
// It uses the existing db transaction block of the callee method.
// Return whether all the pending txns are complete or not and whether next retry should be tried out or not.
func (s *Service) retryPendingDebitTxnInOngoingDbTxn(txnCtx context.Context, accountId string) (*retryPendingDebitTxnResponse, error) {
	// Loading the existing db transaction block, as this method needs to execute in an existing txn block
	// If txn block not present return error.
	if !checkIfOngoingDbTxn(txnCtx) {
		return nil, errors.New("error retrying pending debit txn, no ongoing txn")
	}

	// Acquiring lock to prevent any inconsistency
	account, err := s.accountDao.GetByIdWithLock(txnCtx, accountId)
	if err != nil {
		return nil, err
	}
	if account.IsBalanceDirty() {
		if account, err = s.resolveBalance(txnCtx, accountId); err != nil {
			return nil, err
		}
	}

	// todo (utkarsh) : evaluate if we should process pending txns in order of effective txn timestamp instead
	pendingTxns, err := s.transactionDao.GetPendingDebitTxnsOrderByCreationTime(txnCtx, accountId)
	if err != nil {
		return nil, err
	}

	noOfPendingTxns := len(pendingTxns)
	if noOfPendingTxns == 0 {
		return &retryPendingDebitTxnResponse{allPendingTxnsCompleted: true, shouldTryFuther: false}, nil
	}

	// resolving only the first pending txn in current db txn block as if we loop to try for  all of them in s single txn block
	// then even if one of them fails we would have to evert all of previous successful ones.
	pendingTxn := pendingTxns[0]

	// todo : update this to deduct from corresponding split

	// can only retry for a pending txn if account has some balance
	if account.CurrentBalance > 0 {

		// calculating how much amount can be debited currently
		var amountToBeDebited int32
		if pendingTxn.PendingAmount <= account.CurrentBalance {
			amountToBeDebited = pendingTxn.PendingAmount
		} else {
			amountToBeDebited = account.CurrentBalance
		}

		// if amountToBeDebited equal pending amount then update the txn status to COMPLETED and pending amount to zero
		if amountToBeDebited == pendingTxn.PendingAmount {
			if err = s.transactionDao.UpdatePendingTxn(txnCtx, &svcModel.UpdatePendingTxnReq{
				TxnId:          pendingTxn.ID,
				FinalTxnStatus: accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED,
				PendingAmount:  0,
			}); err != nil {
				return nil, errors.Wrap(err, "error update txn status and amount of pending debit txns")
			}
			noOfPendingTxns--
		} else {
			// if amountToBeDebited **DOES NOT** equal pending amount then txn status remains PENDING and pending amount
			// should get reduced by amountToBeDebited
			err = s.transactionDao.UpdatePendingTxn(txnCtx, &svcModel.UpdatePendingTxnReq{
				TxnId:          pendingTxn.ID,
				FinalTxnStatus: accrualPb.TransactionStatus_TRANSACTION_STATUS_PENDING,
				PendingAmount:  pendingTxn.PendingAmount - amountToBeDebited,
			})
			if err != nil {
				return nil, errors.Wrap(err, "error update txn status and amount of pending debit txns")
			}
		}

		// debit the amount from point buckets
		if err = s.debitFromPointBucketsInEarliestExpiringOrder(txnCtx, pendingTxn.AccountId, amountToBeDebited, pendingTxn.ID); err != nil {
			logger.Error(txnCtx, "error retrying for pending debit txn", zap.String("account_id", pendingTxn.AccountId), zap.Error(err))
			return nil, errors.Wrap(err, "error retrying for pending debit txn")
		}

		// **Note** : balance update call should be made **ONLY** after txn is created and point buckets balances are updated.
		if _, err = s.calculateAndUpdateAccountBalance(txnCtx, accountId); err != nil {
			return nil, errors.Wrap(err, "error updating account balance")
		}

		account.CurrentBalance -= amountToBeDebited
	} else {
		return &retryPendingDebitTxnResponse{
			allPendingTxnsCompleted: false,
			shouldTryFuther:         false,
		}, nil
	}
	return &retryPendingDebitTxnResponse{
		allPendingTxnsCompleted: noOfPendingTxns == 0,
		shouldTryFuther:         noOfPendingTxns > 0 && account.CurrentBalance > 0,
	}, nil
}

// nolint: funlen
func (s *Service) performDebitReversal(ctx context.Context, refID string, parentTxn *svcModel.Transaction) (*accrualPb.ReverseTransactionResponse, error) {
	if refID == "" {
		return nil, errors.New("cannot perform debit reversal, reference id is nil")
	}

	var (
		txnId       string
		currentTime = time.Now()
	)

	// **IMPORTANT** : use only "txnCtx" for context inside following scope.
	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// acquire lock on account to avoid concurrent updates for same account.
		account, err := s.accountDao.GetByIdWithLock(txnCtx, parentTxn.AccountId)
		if err != nil {
			return err
		}
		if account.IsBalanceDirty() {
			if account, err = s.resolveBalance(txnCtx, account.AccountId); err != nil {
				return err
			}
		}

		isAlreadyReversed, err := s.isReversalAlreadyInitiatedForTxn(txnCtx, parentTxn.ID)
		switch {
		case err != nil:
			logger.Error(ctx, "error checking for already initiated reverse", zap.String("parent_txn_id", parentTxn.ID))
			return errors.Wrap(err, "error checking for already initiated reverse")
		case isAlreadyReversed:
			logger.Error(ctx, "reverse already initiated for this transaction", zap.String("parent_txn_id", parentTxn.ID))
			return errors.Wrap(err, "reverse already initiated for this transaction")
		}

		parentTxnId := parentTxn.ID
		if txnId, err = s.transactionDao.Create(txnCtx, &svcModel.CreateTxnReq{
			AccountId:            account.AccountId,
			TransactionType:      accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
			TransactionSubType:   accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_REVERSE,
			Amount:               parentTxn.Amount,
			Status:               accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED,
			ParentTransactionRef: &parentTxnId,
			RequestRef:           refID,
			EffectiveTxnDate:     currentTime,
		}); err != nil {
			return errors.Wrap(err, "error creating reverse transaction")
		}

		parentTxnSplits, err := s.txnSplitDao.GetByTxnId(txnCtx, parentTxn.ID)
		if err != nil {
			return err
		}

		var bucketIds []string
		for _, tSplit := range parentTxnSplits {
			bucketIds = append(bucketIds, tSplit.PointBucketId)
		}

		bucketMap, err := s.pointBucketDao.GetIdToBucketMap(txnCtx, bucketIds)
		if err != nil {
			return err
		}

		type extendedExpiryCredit struct {
			ExpiryTime time.Time
			Credits    int32
		}

		var extendedExpiryCredits []extendedExpiryCredit
		currentTime := time.Now()
		for _, tSplit := range parentTxnSplits {
			pointBucket := bucketMap[tSplit.PointBucketId]
			if pointBucket == nil {
				logger.Error(ctx, "invalid split entries for transaction", zap.String("txn_id", parentTxn.ID))
				return errors.New("invalid split entries for transaction")
			}
			switch pointBucket.Status {
			case svcModel.AVAILABLE:
				if err = s.deltaUpdatePointsAndCreateSplitInOngoingDbTxn(txnCtx, deltaUpdateWithSplitReq{
					txnId:               txnId,
					bucketId:            pointBucket.ID,
					deltaAmountWithSign: tSplit.SplitAmount,
				}); err != nil {
					return err
				}

			case svcModel.EXPIRED:
				approxDebitTime, expiryTime := tSplit.CreatedAt, pointBucket.ExpiryTime
				// added 1 second tolerance due to processing latency when the debit transaction utilized points
				// of that were just about to get expired
				if expiryTime.Add(1 * time.Second).Before(approxDebitTime) {
					logger.Error(ctx, "bucket expiry time is before debit time", zap.String("txn_split_id", tSplit.ID))
					return errors.New("expiry time before debit time")
				}
				extendedExpiryCredits = append(extendedExpiryCredits, extendedExpiryCredit{
					ExpiryTime: currentTime.Add(expiryTime.Sub(approxDebitTime)),
					Credits:    tSplit.SplitAmount,
				})

			default:
				return errors.New("invalid point bucket status")
			}
		}

		for _, credit := range extendedExpiryCredits {
			var bucket *svcModel.PointBucket
			bucket, err = s.pointBucketDao.GetOrCreate(txnCtx, &svcModel.PointBucketIdentifier{
				AccountId:  account.AccountId,
				ExpiryTime: &credit.ExpiryTime,
			})
			if err != nil {
				return err
			}

			if err = s.deltaUpdatePointsAndCreateSplitInOngoingDbTxn(txnCtx, deltaUpdateWithSplitReq{
				txnId:               txnId,
				bucketId:            bucket.ID,
				deltaAmountWithSign: credit.Credits,
			}); err != nil {
				return err
			}
		}

		// **Note** : balance update call should be made **ONLY** after txn is created and point buckets balances are updated
		if _, err = s.calculateAndUpdateAccountBalance(txnCtx, account.AccountId); err != nil {
			return errors.Wrap(err, "error updating account balance")
		}

		return nil
	})
	if txnErr != nil {
		return nil, txnErr
	}

	return &accrualPb.ReverseTransactionResponse{
		ReverseTransactionId: txnId,
		TransactionStatus:    accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED,
	}, nil
}

// deltaUpdatePointsAndCreateSplitInOngoingDbTxn changes the balance in a bucket by some amount and also creates/updates
// a transaction split entry represented by (txn_id, bucket_id).
// **Note** : It **DOES NOT** updates the account balance after performing delta update, it should be done by the caller.
func (s *Service) deltaUpdatePointsAndCreateSplitInOngoingDbTxn(txnCtx context.Context, req deltaUpdateWithSplitReq) error {
	if err := req.Validate(); err != nil {
		return errors.Wrap(err, "error in delta update points with split")
	}

	// Loading the existing db transaction block, as this method needs to execute in an existing txn block
	// If txn block not present return error.
	if !checkIfOngoingDbTxn(txnCtx) {
		return errors.New("error in delta update, no ongoing db txn")
	}

	if err := s.pointBucketDao.DeltaUpdatePoints(txnCtx, req.bucketId, req.deltaAmountWithSign); err != nil {
		return err
	}
	deltaAmountMagnitude := req.deltaAmountWithSign
	if req.deltaAmountWithSign < 0 {
		deltaAmountMagnitude = -req.deltaAmountWithSign
	}

	var (
		existingSplit *svcModel.TxnSplit
		err           error
	)

	if existingSplit, err = s.txnSplitDao.GetByTxnIdAndBucketId(txnCtx, req.txnId, req.bucketId); err != nil {
		return err
	}

	if existingSplit == nil {
		if _, err = s.txnSplitDao.Create(txnCtx, &svcModel.CreateTxnSplitReq{
			TxnId:         req.txnId,
			PointBucketId: req.bucketId,
			SplitAmount:   deltaAmountMagnitude,
		}); err != nil {
			return err
		}
	} else {
		if err = s.txnSplitDao.AddBalanceToTxnSplit(txnCtx, existingSplit.ID, deltaAmountMagnitude); err != nil {
			return err
		}
	}
	return nil
}

// debitFromPointBucketsInEarliestExpiringOrder debits the given debitAmount from point buckets of the given account in an ongoing db transaction block.
// The debit logic is that it first uses the point balance from buckets expiring first.
// **Note** : This does not updates the account balance after performing debits from point buckets.
// nolint: funlen
func (s *Service) debitFromPointBucketsInEarliestExpiringOrder(txnCtx context.Context, accountId string, debitAmount int32, accrualTxnId string) error {
	if accountId == "" || accrualTxnId == "" || debitAmount <= 0 {
		return errors.New("error in debiting from buckets, invalid request params")
	}

	// Loading the existing db transaction block, as this method needs to execute in an existing txn block
	// If txn block not present return error.
	if !checkIfOngoingDbTxn(txnCtx) {
		return errors.New("error in debiting from buckets, no ongoing db txn")
	}

	// get available buckets for account ordered by expiry in asc order
	availableBuckets, err := s.pointBucketDao.GetAvailableBucketsOrderedByExpiryTime(txnCtx, accountId)
	if err != nil {
		return err
	}

	// debit from requiredAmount buckets in expiry first order
	requiredAmount, collectedAmount := debitAmount, int32(0)
	var completelyUsedBucketIds []string
	var completelyUsedBucket []*svcModel.PointBucket
	var partiallyUsedBucket *svcModel.PointBucket
	var partiallyUsedAmount int32 = 0
	for _, bucket := range availableBuckets {
		// can only debit from buckets that have some balance
		if bucket.CurrentBalance > 0 {
			// if bucket balance is less than or equal to remaining required amount then it needs to be fully utilized
			if bucket.CurrentBalance <= requiredAmount-collectedAmount {
				collectedAmount += bucket.CurrentBalance
				completelyUsedBucketIds = append(completelyUsedBucketIds, bucket.ID)
				completelyUsedBucket = append(completelyUsedBucket, bucket)
			} else {
				// if bucket balance is greater than remaining  required amount then it needs to be partially utilized
				partiallyUsedAmount = requiredAmount - collectedAmount
				collectedAmount = requiredAmount
				partiallyUsedBucket = bucket
			}
		}

		// if amount collected equals required no need to iterate further
		if collectedAmount == requiredAmount {
			break
		}
	}

	if collectedAmount < requiredAmount {
		logger.Error(txnCtx, "error in debit, not sufficient balance",
			zap.String("txn_id", accrualTxnId),
			zap.Int32("required_amt", requiredAmount),
			zap.Int32("collected_amt", requiredAmount))
		return errors.New("error in debit, not sufficient balance")
	}

	// for completely utilized buckets the deltaAmountWithSign should be bucket balance
	if len(completelyUsedBucketIds) > 0 {
		for _, bucket := range completelyUsedBucket {
			if err = s.deltaUpdatePointsAndCreateSplitInOngoingDbTxn(txnCtx, deltaUpdateWithSplitReq{
				txnId:               accrualTxnId,
				bucketId:            bucket.ID,
				deltaAmountWithSign: -bucket.CurrentBalance,
			}); err != nil {
				return err
			}
		}
	}

	// update balance of partially utilized buckets and create its split entry
	if partiallyUsedAmount > 0 {
		if err = s.deltaUpdatePointsAndCreateSplitInOngoingDbTxn(txnCtx, deltaUpdateWithSplitReq{
			txnId:               accrualTxnId,
			bucketId:            partiallyUsedBucket.ID,
			deltaAmountWithSign: -partiallyUsedAmount,
		}); err != nil {
			return err
		}
	}

	return nil
}

// isReversalAlreadyInitiatedForTxn returns true if  reversal was already initiated for the given txn
func (s *Service) isReversalAlreadyInitiatedForTxn(ctx context.Context, txnId string) (bool, error) {
	// fetch already initiated reverse txn
	alreadyInitiatedRevTxn, err := s.transactionDao.GetByParentTxnRefId(ctx, txnId)
	if err != nil {
		return false, errors.Wrap(err, "error check for already initiated")
	}
	return alreadyInitiatedRevTxn != nil, nil
}

// GetAccountDetails rpc accepts actor_id and account type and returns account details.
func (s *Service) GetAccountDetails(ctx context.Context, req *accrualPb.GetAccountDetailsRequest) (*accrualPb.GetAccountDetailsResponse, error) {
	account, err := s.getOrCreateAccount(ctx, &svcModel.AccountIdentifier{ActorId: req.GetActorId(), AccountType: req.GetAccountType()})
	if err != nil {
		logger.Error(ctx, "error fetching or create account", zap.Error(err))
		return &accrualPb.GetAccountDetailsResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching or create account " + err.Error())}, nil
	}
	if account.IsBalanceDirty() {
		if account, err = s.resolveBalance(ctx, account.AccountId); err != nil {
			logger.Error(ctx, "error resolving account balance", zap.Any("txn_request", req), zap.Error(err))
			return &accrualPb.GetAccountDetailsResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
	}

	return &accrualPb.GetAccountDetailsResponse{
		Status:                   rpc.StatusOk(),
		AccountId:                account.AccountId,
		AccountOperationalStatus: account.AccountOperationalStatus,
		AccountBalance:           account.CurrentBalance,
		CurrentBalance:           account.CurrentBalance,
		AvailableBalance:         account.AvailableBalance,
		NextExpiryTime:           timestampPb.New(account.BalanceNextExpiryTime),
	}, nil
}

// ResolveBalance rpc accepts account_id of accrual accounts & resolves the balance.
func (s *Service) ResolveBalance(ctx context.Context, req *accrualPb.ResolveBalanceRequest) (*accrualPb.ResolveBalanceResponse, error) {
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return &accrualPb.ResolveBalanceResponse{Status: rpc.StatusUnavailableWithDebugMsg(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error())}, nil
	}

	accountId := req.GetAccountId()
	if _, err := s.resolveBalance(ctx, accountId); err != nil {
		logger.Error(ctx, "error resolving account balance", zap.Any("request", req), zap.Error(err))
		return &accrualPb.ResolveBalanceResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	return &accrualPb.ResolveBalanceResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// DebitFiCoinsForAccountClosure debits all the fi coins from actor's account and mark ACCOUNT_CLOSURE_EXPIRY as subtype for debit txn.
// Deprecated: do not use
func (s *Service) DebitFiCoinsForAccountClosure(_ context.Context, _ *accrualPb.DebitFiCoinsForAccountClosureRequest) (*accrualPb.DebitFiCoinsForAccountClosureResponse, error) {
	return &accrualPb.DebitFiCoinsForAccountClosureResponse{
		Status: rpc.StatusUnimplementedWithDebugMsg("this rpc is deprecated, use CloseAccount"),
	}, nil
}

// CloseAccount is used to:
// 1. Expire the balance of an account by performing a debit transaction of the available balance and marking the transaction subtype as ACCOUNT_CLOSURE_EXPIRY.
// 2. Update the account operational status to CLOSED.
// All the operations are performed in a single db transaction block to ensure atomicity.
func (s *Service) CloseAccount(ctx context.Context, req *accrualPb.CloseAccountRequest) (*accrualPb.CloseAccountResponse, error) {
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return &accrualPb.CloseAccountResponse{Status: rpc.StatusUnavailableWithDebugMsg(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error())}, nil
	}

	accountDetailsResp, accountDetailsErr := s.GetAccountDetails(ctx, &accrualPb.GetAccountDetailsRequest{
		ActorId:     req.GetActorId(),
		AccountType: req.GetAccountType(),
	})
	if rpcErr := epifigrpc.RPCError(accountDetailsResp, accountDetailsErr); rpcErr != nil {
		logger.Error(ctx, "error while fetching account details", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(rpcErr))
		return &accrualPb.CloseAccountResponse{Status: rpc.StatusInternalWithDebugMsg("error while fetching account details")}, nil
	}

	if accountDetailsResp.GetAccountOperationalStatus() == accrualPb.AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED {
		logger.Info(ctx, "account is already closed", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String("accountType", req.GetAccountType().String()))
		return &accrualPb.CloseAccountResponse{
			Status: rpc.StatusOk(),
		}, nil
	}

	if err := s.performAccountClosure(ctx, accountDetailsResp.GetAccountId()); err != nil {
		logger.Error(ctx, "error while performing accrual account closure", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &accrualPb.CloseAccountResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &accrualPb.CloseAccountResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) performAccountClosure(ctx context.Context, accountId string) error {

	// **Important** : use only 'txnCtx' for propagation context inside this transaction block.
	txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {

		// 1. acquire lock on account to prevent concurrent updates like debit or credit on the same account.
		account, err := s.accountDao.GetByIdWithLock(txnCtx, accountId)
		if err != nil {
			return err
		}
		if account.IsBalanceDirty() {
			if account, err = s.resolveBalance(txnCtx, accountId); err != nil {
				return err
			}
		}

		// 2. debit any valid available balance of the account
		availableBalance := account.AvailableBalance
		if availableBalance > 0 {
			if _, err = s.performTransaction(txnCtx, &svcModel.TransactRequest{
				AccountId:               accountId,
				AccountAvailableBalance: availableBalance,
				TxnAmount:               availableBalance,
				TransactionType:         accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
				TransactionSubType:      accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_ACCOUNT_CLOSURE_EXPIRY,
				RefId:                   getAccountBalanceExpiryRefId(accountId), // marking this ref_id as ACCOUNT_EXPIRY-accountId as this should be unique
				IsPendingDebitAllowed:   false,
			}); err != nil {
				return fmt.Errorf("error while performing debit transaction: %w", err)
			}
		}

		// 3. update the account operational status to CLOSED
		_, err = s.accountDao.UpdateAccountDetails(txnCtx, &svcModel.UpdateAccountDetailsRequest{
			AccountId:                accountId,
			AccountOperationalStatus: accrualPb.AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED,
		})
		if err != nil {
			return fmt.Errorf("error while updating account operational status: %w", err)
		}

		return nil
	})
	if txnErr != nil {
		return txnErr
	}
	return nil
}

func (s *Service) performCreditTransactionInOngoingDbTxn(txnCtx context.Context, req *svcModel.TransactRequest) (string, error) {
	var (
		currentTime = time.Now()
		accountId   = req.AccountId
		txnAmount   = req.TxnAmount

		txnId string
	)

	// acquire lock to prevent concurrent updates on the same account
	account, err := s.accountDao.GetByIdWithLock(txnCtx, req.AccountId)
	if err != nil {
		return "", err
	}
	if account.IsBalanceDirty() {
		// nolint: staticcheck
		if account, err = s.resolveBalance(txnCtx, accountId); err != nil {
			return "", err
		}
	}

	// checking if expiry time of credit has already passed
	if req.BalanceExpiryTime.Before(currentTime) {
		return "", errors.New("amount expiry time cannot be before current time")
	}

	// creating new credit transaction with all the required details
	if txnId, err = s.transactionDao.Create(txnCtx, &svcModel.CreateTxnReq{
		AccountId:          accountId,
		TransactionType:    accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		TransactionSubType: req.TransactionSubType,
		Amount:             txnAmount,
		Status:             accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED,
		RequestRef:         req.RefId,
		EffectiveTxnDate:   currentTime,
		MetaData:           req.MetaData,
	}); err != nil {
		return "", err
	}

	// fetching or creating a new  point bucket having given expiry time and account id.
	bucket, err := s.pointBucketDao.GetOrCreate(txnCtx, &svcModel.PointBucketIdentifier{
		AccountId:  accountId,
		ExpiryTime: req.BalanceExpiryTime,
	})
	if err != nil {
		return "", err
	}

	// crediting above point bucket with txnAmount credits
	if err = s.deltaUpdatePointsAndCreateSplitInOngoingDbTxn(txnCtx, deltaUpdateWithSplitReq{
		txnId:               txnId,
		bucketId:            bucket.ID,
		deltaAmountWithSign: txnAmount,
	}); err != nil {
		return "", err
	}

	// **Note** : balance update call should be made **ONLY** after txn is created and point buckets balances are updated.
	if _, err = s.calculateAndUpdateAccountBalance(txnCtx, accountId); err != nil {
		return "", err
	}

	return txnId, nil
}

func (s *Service) performDebitTransactionWithPendingDebitNotAllowedInOngoingDbTxn(txnCtx context.Context, req *svcModel.TransactRequest) (string, error) {
	var (
		accountId      = req.AccountId
		debitTxnAmount = req.TxnAmount
		currentTime    = time.Now()

		txnId string
		err   error
	)

	// acquire lock on account to prevent concurrent updates on the same account.
	account, err := s.accountDao.GetByIdWithLock(txnCtx, req.AccountId)
	if err != nil {
		return "", err
	}
	if account.IsBalanceDirty() {
		if account, err = s.resolveBalance(txnCtx, accountId); err != nil {
			return "", err
		}
	}

	if account.AvailableBalance < req.TxnAmount {
		logger.Debug(txnCtx, "insufficient balance for debit transaction", zap.String(logger.ACCOUNT_ID, accountId), zap.Int32("availableBalance", account.AvailableBalance), zap.Int32("debitTxnAmount", debitTxnAmount))
		return "", fmt.Errorf("unable to perform debit, err: %w", accrualError.ErrInsufficientBalance)
	}

	// create debit txn with required details
	txnId, err = s.transactionDao.Create(txnCtx, &svcModel.CreateTxnReq{
		AccountId:          accountId,
		TransactionType:    accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		TransactionSubType: req.TransactionSubType,
		Amount:             debitTxnAmount,
		Status:             accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED,
		RequestRef:         req.RefId,
		EffectiveTxnDate:   currentTime,
		MetaData:           req.MetaData,
	})
	if err != nil {
		return "", err
	}

	// debit the given amount from point buckets
	if err = s.debitFromPointBucketsInEarliestExpiringOrder(txnCtx, accountId, debitTxnAmount, txnId); err != nil {
		return "", errors.Wrap(err, "error in debiting from buckets")
	}

	// **Note** : balance update call should be made **ONLY** after txn is created and point buckets balances are updated.
	if _, err = s.calculateAndUpdateAccountBalance(txnCtx, accountId); err != nil {
		return "", errors.Wrap(err, "error updating account balance")
	}

	return txnId, nil
}

func (s *Service) performDebitTransactionWithPendingDebitAllowedInOngoingDbTxn(txnCtx context.Context, req *svcModel.TransactRequest) (string, error) {
	var (
		accountId      = req.AccountId
		debitTxnAmount = req.TxnAmount
		currentTime    = time.Now()

		txnId string
		err   error
	)

	// acquire lock to prevent concurrent updates on the same account
	if _, err = s.accountDao.GetByIdWithLock(txnCtx, accountId); err != nil {
		return "", err
	}

	// just creating a transaction entry in pending state not debiting any amount now
	// **Note** : this is intentional as pending txns queue should ideally be consumed in earlier txn first order so just queueing the txn
	// as PENDING here and intentionally not trying to process it.
	if txnId, err = s.transactionDao.Create(txnCtx, &svcModel.CreateTxnReq{
		AccountId:          accountId,
		TransactionType:    accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		TransactionSubType: req.TransactionSubType,
		Amount:             debitTxnAmount,
		PendingAmount:      debitTxnAmount,
		Status:             accrualPb.TransactionStatus_TRANSACTION_STATUS_PENDING,
		RequestRef:         req.RefId,
		EffectiveTxnDate:   currentTime,
		MetaData:           req.MetaData,
	}); err != nil {
		return "", errors.Wrap(err, "error creating reverse transaction")
	}

	// **Note** : balance update call should be made **ONLY** after txn is created and point buckets balances are updated.
	if _, err = s.calculateAndUpdateAccountBalance(txnCtx, accountId); err != nil {
		return "", errors.Wrap(err, "error updating account balance")
	}

	return txnId, nil
}

// resolvePendingDebitTxnsIfApplicable resolves the pending debit txns if applicable in the following order:
// 1. If the fiCoinsToFiPointsMigration feature flag is enabled, then we will not resolve the pending debit txns to avoid any race conditions.
// 2. If the ongoing db transaction is present, then we will resolve the pending debit txns in the ongoing db transaction.
// 3. Otherwise, we will resolve the pending debit txns in a new goroutine.
func (s *Service) resolvePendingDebitTxnsIfApplicable(ctx context.Context, accountId string) {
	switch {
	case bool(s.isFiCoinsToFiPointsMigrationEnabled):
		return
	case checkIfOngoingDbTxn(ctx):
		s.resolvePendingDebitTxns(ctx, accountId)
	default:
		// async resolve pending debit txns
		// todo (sahil) : add some retry mechanism if this go routine gets killed
		goroutine.RunWithCtx(epificontext.CloneCtx(ctx), func(gctx context.Context) { //nolint:contextcheck
			s.resolvePendingDebitTxns(gctx, accountId)
		})
	}
}

// getAccountBalanceExpiryRefId returns a unique reference id for account balance expiry transaction
func getAccountBalanceExpiryRefId(accountId string) string {
	return "ACCOUNT_EXPIRY-" + accountId
}

// checkIfOngoingDbTxn checks if there is an ongoing db transaction block in the context.
func checkIfOngoingDbTxn(ctx context.Context) bool {
	_, ok := gormctxv2.FromContext(ctx, nil)
	return ok
}

func (s *Service) GetFiCoinsToPointsMigrationDetails(ctx context.Context, req *accrualPb.GetFiCoinsToPointsMigrationDetailsRequest) (*accrualPb.GetFiCoinsToPointsMigrationDetailsResponse, error) {
	fcAccount, err := s.accountDao.GetByActorIdAndAccountType(ctx, req.GetActorId(), accrualPb.AccountType_ACCOUNT_TYPE_FICOINS)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			// if FC account is not found, then return 0 for both fi coins and fi points migrated
			return &accrualPb.GetFiCoinsToPointsMigrationDetailsResponse{
				Status: rpc.StatusOk(),
			}, nil
		}
		return &accrualPb.GetFiCoinsToPointsMigrationDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	fpAccount, err := s.accountDao.GetByActorIdAndAccountType(ctx, req.GetActorId(), accrualPb.AccountType_ACCOUNT_TYPE_FI_POINTS)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			// if FP account is not found, then return 0 for both fi coins and fi points migrated
			return &accrualPb.GetFiCoinsToPointsMigrationDetailsResponse{
				Status: rpc.StatusOk(),
			}, nil
		}
		return &accrualPb.GetFiCoinsToPointsMigrationDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	migrationTime := accrualPkg.GetFiCoinsToFiPointsMigrationTime()
	// Add a buffer of 1 day to the migration time to fetch all the txns that happened during the migration
	fromDate := migrationTime.Add(-1 * time.Hour * 24)
	toDate := migrationTime.Add(1 * time.Hour * 24)

	// total fi coins migrated to fi points
	fiCoinsMigrated, err := s.transactionDao.GetTotalAmountByAccountIdAndType(ctx, fcAccount.AccountId,
		accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
		accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_POINT_MIGRATION,
		fromDate, toDate)
	if err != nil {
		return &accrualPb.GetFiCoinsToPointsMigrationDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// total fi points migrated from fi coins
	fiPointsMigrated, err := s.transactionDao.GetTotalAmountByAccountIdAndType(ctx, fpAccount.AccountId,
		accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_POINT_MIGRATION,
		fromDate, toDate)
	if err != nil {
		return &accrualPb.GetFiCoinsToPointsMigrationDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &accrualPb.GetFiCoinsToPointsMigrationDetailsResponse{
		Status:                rpc.StatusOk(),
		TotalFiCoinsMigrated:  fiCoinsMigrated,
		TotalFiPointsMigrated: fiPointsMigrated,
	}, nil
}
