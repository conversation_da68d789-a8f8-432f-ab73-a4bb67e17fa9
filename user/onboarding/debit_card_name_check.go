package onboarding

import (
	"context"
	"fmt"

	gammanames "github.com/epifi/gamma/pkg/names"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	pb "github.com/epifi/gamma/api/user/onboarding"
)

func (s *Service) DebitCardNameCheck(ctx context.Context, req *pb.DebitCardNameCheckRequest) (*pb.DebitCardNameCheckResponse, error) {
	user, err := s.userProcessor.GetUserByActorId(ctx, req.GetActorId())
	if err != nil {
		return nil, err
	}

	if req.GetSkipNameCheck() {
		logger.Info(ctx, "skipping name check as debit card name is same as legal name")
		err = s.updateDebitCardNameCheck(ctx, req.GetActorId(), true, 0)
		if err != nil {
			logger.Error(ctx, "failed to update debit card name check metadata", zap.String(logger.USER_ID, user.GetId()), zap.Error(err))
			return &pb.DebitCardNameCheckResponse{Status: rpc.StatusInternal()}, nil
		}
		if err := s.updateUserDebitCardName(ctx, user, req.GetDebitCardName()); err != nil {
			return &pb.DebitCardNameCheckResponse{Status: rpc.StatusInternal()}, nil
		}
		return &pb.DebitCardNameCheckResponse{Status: rpc.StatusOk()}, nil
	}

	// Check debit card name match with KYC name
	kycNameCheckInfo, _ := gammanames.NameMatchAllCriteria(ctx, user.GetProfile().GetKycName(), req.GetDebitCardName())
	var kycNameCheckScore float32 = -1
	if kycNameCheckInfo != nil {
		kycNameCheckScore = float32(kycNameCheckInfo.WeightedSumScore)
	}
	kycNameCheckPassed := kycNameCheckScore >= 1

	if kycNameCheckPassed {
		err = s.updateDebitCardNameCheck(ctx, req.GetActorId(), kycNameCheckPassed, kycNameCheckScore)
		if err != nil {
			logger.Error(ctx, "failed to update debit card name check metadata ", zap.String(logger.USER_ID, user.GetId()), zap.Error(err))
			return &pb.DebitCardNameCheckResponse{Status: rpc.StatusInternal()}, nil
		}
		if err := s.updateUserDebitCardName(ctx, user, req.GetDebitCardName()); err != nil {
			return &pb.DebitCardNameCheckResponse{Status: rpc.StatusInternal()}, nil
		}
		return &pb.DebitCardNameCheckResponse{Status: rpc.StatusOk()}, nil
	}

	// Check debit card name match with PAN name
	panNameCheckInfo, _ := gammanames.NameMatchAllCriteria(ctx, user.GetProfile().GetPanName(), req.GetDebitCardName())
	var panNameCheckScore float32 = -1
	if panNameCheckInfo != nil {
		panNameCheckScore = float32(panNameCheckInfo.WeightedSumScore)
	}
	panNameCheckPassed := panNameCheckScore >= 1

	err = s.updateDebitCardNameCheck(ctx, req.GetActorId(), panNameCheckPassed, panNameCheckScore)
	if err != nil {
		logger.Error(ctx, "failed to update debit card name check metadata ", zap.String(logger.USER_ID, user.GetId()), zap.Error(err))
		return &pb.DebitCardNameCheckResponse{Status: rpc.StatusInternal()}, nil
	}

	if panNameCheckPassed {
		if err := s.updateUserDebitCardName(ctx, user, req.GetDebitCardName()); err != nil {
			return &pb.DebitCardNameCheckResponse{Status: rpc.StatusInternal()}, nil
		}
		return &pb.DebitCardNameCheckResponse{Status: rpc.StatusOk()}, nil
	}

	logger.Info(ctx, "name match failed for user with kyc and pan name", zap.String(logger.USER_ID, user.GetId()))
	return &pb.DebitCardNameCheckResponse{
		Status: rpc.NewStatusWithoutDebug(uint32(pb.DebitCardNameCheckResponse_NAME_CHECK_FAILED), "name match failed"),
	}, nil
}

// updateDebitCardNameCheck updates if the debit card name check passed or failed.
// If name check fails we will increase name check retry count
func (s *Service) updateDebitCardNameCheck(ctx context.Context, actorId string, oldNameMatchPassed bool, oldNameMatchScore float32) error {
	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, actorId)
	if err != nil {
		return fmt.Errorf("failed to fetch onboarding details %w", err)
	}
	switch {
	case onb.GetStageMetadata() == nil:
		onb.StageMetadata = &onbPb.StageMetadata{
			DebitCardNameCheck: &onbPb.DebitCardNameCheck{
				OldNameMatchPassed: oldNameMatchPassed,
				OldNameMatchScore:  oldNameMatchScore,
			},
		}
	case onb.GetStageMetadata().GetDebitCardNameCheck() == nil:
		onb.StageMetadata.DebitCardNameCheck = &onbPb.DebitCardNameCheck{
			OldNameMatchPassed: oldNameMatchPassed,
			OldNameMatchScore:  oldNameMatchScore,
		}
	default:
		onb.StageMetadata.DebitCardNameCheck.OldNameMatchPassed = oldNameMatchPassed
		onb.StageMetadata.DebitCardNameCheck.OldNameMatchScore = oldNameMatchScore
	}
	if !oldNameMatchPassed {
		onb.StageMetadata.DebitCardNameCheck.NameCheckRetryCount += 1
	}
	err = s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
	}, onb)
	if err != nil {
		return fmt.Errorf("error while updating debit card name check metadata %w", err)
	}
	logger.Info(ctx, "updated debit card name check metadata successfully", logOnb(onb.GetOnboardingId()))
	return nil
}

func (s *Service) updateUserDebitCardName(ctx context.Context, user *userPb.User, debitCardName *common.Name) error {
	user.GetProfile().DebitCardName = debitCardName
	resp, err := s.userClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
		User:       user,
		UpdateMask: []userPb.UserFieldMask{userPb.UserFieldMask_DEBIT_CARD_NAME},
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "failed to update debit card name for user", zap.String(logger.USER_ID, user.GetId()), zap.Error(err))
		return err
	}
	return nil
}

func (s *Service) ResetDebitCardNameRetries(ctx context.Context, req *pb.ResetDebitCardNameRetriesRequest) (*pb.ResetDebitCardNameRetriesResponse, error) {
	logger.Info(ctx, "received request to reset debit card name retries for user")
	onb, err := s.onboardingDao.GetOnboardingDetailsByActor(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error while fetching onboarding details by actor id", zap.Error(err))
		return &pb.ResetDebitCardNameRetriesResponse{Status: rpc.StatusInternal()}, nil
	}
	if onb.GetCurrentOnboardingStage() != onbPb.OnboardingStage_CONFIRM_CARD_MAILING_ADDRESS ||
		isStageSuccessOrSkipped(onb, onbPb.OnboardingStage_CONFIRM_CARD_MAILING_ADDRESS) {
		logger.Error(ctx, "stage already success or current stage not equal to confirm card mailing address "+
			"skipping retries update", zap.String(logger.ONBOARDING_STAGE, onb.GetCurrentOnboardingStage().String()))
		return &pb.ResetDebitCardNameRetriesResponse{Status: rpc.StatusFailedPrecondition()}, nil
	}
	if onb.GetStageMetadata().GetDebitCardNameCheck() == nil {
		onb.GetStageMetadata().DebitCardNameCheck = &pb.DebitCardNameCheck{}
	}
	onb.GetStageMetadata().GetDebitCardNameCheck().NameCheckRetryCount = 0
	err = s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{
		onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
	}, onb)
	if err != nil {
		logger.Error(ctx, "error while updating debit card name retries", zap.Error(err))
		return &pb.ResetDebitCardNameRetriesResponse{Status: rpc.StatusInternal()}, nil
	}
	logger.Info(ctx, "updated debit card name check retries successfully", logOnb(onb.GetOnboardingId()))
	return &pb.ResetDebitCardNameRetriesResponse{Status: rpc.StatusOk()}, nil
}
