//go:build wireinject

//go:generate wire

package wire

import (
	"github.com/google/wire"

	questSdkGenConf "github.com/epifi/be-common/quest/sdk/config/genconf"

	leadsPb "github.com/epifi/gamma/api/leads"

	"github.com/epifi/be-common/pkg/cmd/types"
	oncev2 "github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/liveness"
	authPb "github.com/epifi/gamma/api/auth/location"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/comms"
	upPb "github.com/epifi/gamma/api/comms/user_preference"
	beConnectedAccPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/creditreportv2"
	derivedAttributesPb "github.com/epifi/gamma/api/creditreportv2/derivedattributes"
	"github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/employment"
	fireflypb "github.com/epifi/gamma/api/firefly"
	ffV2Pb "github.com/epifi/gamma/api/firefly/v2"
	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	mfPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/kyc"
	kycdocspb "github.com/epifi/gamma/api/kyc/docs"
	vkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/omegle"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/pan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/product"
	questManagerPb "github.com/epifi/gamma/api/quest/manager"
	"github.com/epifi/gamma/api/risk"
	savingsPb "github.com/epifi/gamma/api/savings"
	screenerPb "github.com/epifi/gamma/api/screener"
	segmentPb "github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/tiering"
	userPb "github.com/epifi/gamma/api/user"
	groupPb "github.com/epifi/gamma/api/user/group"
	userLocation "github.com/epifi/gamma/api/user/location"
	obfuscatorPb "github.com/epifi/gamma/api/user/obfuscator"
	onboardingpb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	vgDocPb "github.com/epifi/gamma/api/vendorgateway/docs"
	ekycPb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	vgLivenessPb "github.com/epifi/gamma/api/vendorgateway/liveness"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	vgPan "github.com/epifi/gamma/api/vendorgateway/pan"
	vkyccalltroubleshootpb "github.com/epifi/gamma/api/vkyccall/troubleshoot"
	insightsPkg "github.com/epifi/gamma/insights/pkg"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/pkg/vendorstore"
	questSdkInit "github.com/epifi/gamma/quest/sdk/init"
	"github.com/epifi/gamma/user/config"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding"
	"github.com/epifi/gamma/user/onboarding/consumer"
	onbDao "github.com/epifi/gamma/user/onboarding/dao"
	onbHelper "github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/pkg/screener"
	"github.com/epifi/gamma/user/onboarding/resetter"
	"github.com/epifi/gamma/user/onboarding/stageproc"
	"github.com/epifi/gamma/user/onboarding/stageproc/datavalidator"
	watsonOnbClient "github.com/epifi/gamma/user/onboarding/watson"
	wireTypes "github.com/epifi/gamma/user/wire/types"
)

// config: {"syncOnbEventPublisher": "Onboarding().SyncOnboardingSqsPublisher()", "s3Client": "AWS().S3.UsersBucketName", "nrS3Client": "Onboarding().NrBucketName()"}
func InitializeOnboardingService(config3 *genconf.Config, db types.EpifiCRDB, userClient userPb.UsersClient,
	savClient savingsPb.SavingsClient, actorClient actor.ActorClient, cardClient cardPb.CardProvisioningClient,
	consentClient consent.ConsentClient, authClient auth.AuthClient, kycClient kyc.KycClient,
	broker events.Broker, vkycClient vkycpb.VKYCClient, onboardingStagePublisher wireTypes.OnboardingStageEventPublisher, orderClientClient orderPb.OrderServiceClient,
	userGrpClient groupPb.GroupClient, syncOnbEventPublisher wireTypes.SyncOnboardingSqsPublisher, commsClient comms.CommsClient,
	s3Client wireTypes.UsersS3Client, employmentClient employment.EmploymentClient, inAppReferralClient inAppReferralPb.InAppReferralClient, ekycClient ekycPb.EKYCClient,
	userPrefClient upPb.UserPreferenceClient, userConf *config.Config, ncClient namecheck.UNNameCheckClient,
	livenessClient liveness.LivenessClient, userLocationClient userLocation.LocationClient, obfuscatorClient obfuscatorPb.ObfuscatorClient,
	authLocClient authPb.LocationClient, riskClient risk.RiskClient,
	screenerClient screenerPb.ScreenerClient, bcClient bankCustPb.BankCustomerServiceClient,
	redisClient types.UserRedisStore, feEmpClient employment.EmploymentFeClient, derAttSvc derivedAttributesPb.DerivedAttributesManagerClient,
	userIntelClient userintel.UserIntelServiceClient, pgdb types.NudgePGDB, watsonClient watson.WatsonClient, panClient pan.PanClient,
	operationalStatusClient operationalStatusPb.OperationalStatusServiceClient, accountBalanceClient accountBalancePb.BalanceClient,
	fireflyClient fireflypb.FireflyClient, loanClient palPb.PreApprovedLoanClient, productClient product.ProductClient,
	vgPanClient vgPan.PANClient, creditReportV2Client creditreportv2.CreditReportManagerClient,
	userOnbCacheStorage types.OnboardingRueidisCacheStorage, kycDocExtractionClient kycdocspb.DocExtractionClient,
	omegleClient omegle.OmegleClient, vgDocClient vgDocPb.DocsClient, nrS3Client wireTypes.NrS3Client, onbCriticalCacheStorage types.OnboardingMinRueidisCacheStorage,
	vkycCallTroubleshootingClient vkyccalltroubleshootpb.TroubleshootClient, tieringClient tiering.TieringClient, vgLivClient vgLivenessPb.LivenessClient,
	questManagerCl questManagerPb.ManagerClient, questCacheStorage types.QuestCacheStorage, segmentClient segmentPb.SegmentationServiceClient, mfExternalOrdersClient mfPb.MFExternalOrdersClient,
	connectedAccountClient beConnectedAccPb.ConnectedAccountClient, leadsClient leadsPb.UserLeadSvcClient, ffV2Client ffV2Pb.FireflyV2Client) *onboarding.Service {
	wire.Build(
		types.UserRedisStoreRedisClientProvider,
		VkycConfigProvider,
		CSISConfigProvider,
		types.EpifiCRDBGormDBProvider,
		wireTypes.UsersS3ClientProvider,
		onboarding.NewService,
		OnboardingConfigProvider,
		OnboardingGenConfigProvider,
		oncev2.NewDoOnce,
		datetime.WireDefaultTimeSet,
		wireTypes.NextActionDecisionCacheConfigProvider,
		onbDao.NextActionDecisionCacheWireset,
		onbDao.OnboardingDetailsDaoWireSet,
		OnboardingDetailsCacheStorage,
		OnboardingDetailsCriticalCacheStorage,
		ProvideOnbDetailsMinCacheConfig,
		ProvideOnbDetailsCacheConfig,
		wire.Bind(new(onbDao.PinCodeDetailsDao), new(*onbDao.PinCodeDetailsCrdb)),
		onbDao.NewPinCodeDetailsCrdb,
		onboarding.NewStuckUserHandler,
		vendorstore.NewVendorStore,
		vendorstore.VendorStoreDAOWireSet,
		wire.Bind(new(userEvents.EventLogger), new(*userEvents.FiEventLogger)),
		userEvents.NewFiEventLogger,
		onboarding.NewInitiateCkycProcessor,
		onboarding.NewKycLivenessProcessor,
		onboarding.NewCKYCProcessor,
		onboarding.NewEKYCProcessor,
		onboarding.NewBKYCProcessor,
		onboarding.NewLivenessProcessor,
		onboarding.NewRiskScreenProc,
		onboarding.NewPanNameCheckProcessor,
		onboarding.NewKycDedupeCheckProcessor,
		onboarding.NewDedupeCheckProcessor,
		onboarding.NewShippingAddressProcessor,
		onboarding.NewPreCustomerCreationDedupeCheckProcessor,
		onboarding.NewEkycNameDobValidationProcessor,
		onboarding.NewKycNameDobValidationProcessor,
		onboarding.NewDeviceRegistrationProcessor,
		onboarding.NewCustomerCreationProcessor,
		onboarding.NewAccountCreationProcessor,
		onboarding.NewStageTroubleshooter,
		onboarding.NewCardAdressProcessor,
		onboarding.NewDOBandPANProcessor,
		onboarding.NewCardCreationProcessor,
		onboarding.NewUNNameCheckProc,
		onboarding.NewDebitCardPinSetupProcessor,
		onboarding.NewScreenerProc,
		onboarding.NewUpiConsentStage,
		onboarding.NewCreditReportCheckStage,
		onboarding.NewReferralFiniteCodeStage,
		onboarding.NewTncConsentStage,
		onboarding.NewIntentSelectionStage,
		onboarding.NewSavingIntroConsentStage,
		onboarding.NewMotherFatherNameStage,
		onboarding.NewVkycStage,
		onboarding.NewPreCustomerCreationCheckStage,
		onboarding.NewOptionalVkycStage,
		onboarding.NewAddmoneyStage,
		onboarding.NewAadharMobileValidationStage,
		onboarding.NewUpdateProfileDetailsStage,
		persistentqueue.NewPersistentQueue,
		stageproc.NewLocationCheckStage,
		stageproc.NewPermissionStage,
		stageproc.NewReferralFiniteCodeStage,
		stageproc.NewTncConsentStage,
		stageproc.NewCreditReportVerificationStage,
		stageproc.NewEmploymentVerificationStage,
		stageproc.NewWorkEmailOtpStage,
		stageproc.NewGmailVerificationStage,
		stageproc.NewCreditReportCheckStage,
		stageproc.NewITRIntimationVerificationStage,
		stageproc.NewAppScreeningStage,
		stageproc.NewMotherFatherNameStage,
		stageproc.NewDobPanStage,
		stageproc.NewDedupeCheckStage,
		stageproc.NewInitCkycStage,
		stageproc.NewEnsureKycAvailabilityStage,
		stageproc.NewNREnsureKycAvailabilityStage,
		stageproc.NewUpdateCustomerDetailsStage,
		stageproc.NewNRUpdateCustomerDetailsStage,
		stageproc.NewPanNameCheckStage,
		stageproc.NewKycDedupeCheckStage,
		stageproc.NewConfirmCardMailingAddressStage,
		stageproc.NewDebitCardPinSetStage,
		stageproc.OnbAddMoneyUtilsWireSet,
		stageproc.NewAddMoneyStage,
		stageproc.NewAccountCreationAddMoneyStage,
		stageproc.NewOnboardingCompleteStage,
		stageproc.NewPanUniquenessCheckStage,
		stageproc.NewHeuristicBasedScreening,
		stageproc.NewDeviceRegistrationStage,
		stageproc.NewVKYCOnboardingStage,
		stageproc.NewRiskScreeningStage,
		stageproc.NewUANPresenceCheckStage,
		stageproc.NewCKYCStage,
		stageproc.NewEKYCStage,
		stageproc.NewLivenessStage,
		stageproc.NewCustomerCreationStage,
		stageproc.NewAccountCreationStage,
		stageproc.NewCardCreationStage,
		stageproc.NewEPFOCompanySearch,
		stageproc.NewCAScreenerStage,
		stageproc.NewPreCustomerCreationCheckStage,
		stageproc.NewNRPreCustomerCreationCheckStage,
		stageproc.NewBKYCStage,
		stageproc.NewFiLiteRiskScreeningStage,
		stageproc.NewKYCNameDOBValidationStage,
		stageproc.NewUNNameCheckStage,
		stageproc.NewIncomeEstimateCheckStage,
		stageproc.NewSoftIntentSelectionStage,
		stageproc.NewIntentSelectionStage,
		stageproc.NewCreditCardOnboardingStatusCheck,
		stageproc.NewPLOnboardingStatusCheckStage,
		stageproc.NewSaIntroConsentStage,
		stageproc.NewSMSParserConsentStage,
		stageproc.NewAadharMobileValidationStage,
		stageproc.NewUpdateProfileDetailsStage,
		stageproc.NewLendabilityCheckStage,
		stageproc.NewPassportVerificationStage,
		stageproc.NewVisaVerificationStage,
		stageproc.NewCountryIDVerificationStage,
		stageproc.NewNROnboardingCrossDataValidationStage,
		stageproc.NewNRKycDedupeCheckStage,
		stageproc.NewNRLivenessStage,
		stageproc.NewNRUNNameCheckStage,
		stageproc.NewWealthAnalyserOnboardingStatusCheckStage,
		stageproc.NewNRCommunicationAddress,
		stageproc.NewNRVKYCStage,
		stageproc.NewNRQatarVKYCStage,
		stageproc.NewNRCardCreationStage,
		stageproc.NewNRLocationCheckStage,
		stageproc.NewNROAccountCreationStage,
		stageproc.NewOrderPhysicalCardStage,
		stageproc.NewOpenMinBalanceAccountStage,
		stageproc.NewInitiateCreditReportFetchStage,
		stageproc.NewQatarIdVerificationStage,
		stageproc.NewSMSParserDataVerificationStage,
		screener.NewScreenerCheckProcessor,
		stageproc.NewInstalledAppsCheckStage,
		stageproc.NewCollectAdditionalProfileDetailsForFederalLoansStage,
		stageproc.NewWaitForAutoPan,
		stageproc.NewEnsureCreditReportAvailabilityStage,
		idgen.WireSet,
		onbHelper.OnboardingProcWireSet,
		lock.RedisV9LockManagerWireSet,
		release.EvaluatorWireSet,
		featureReleaseConfigProvider,
		onbDao.NewGoodUserAssociatesDaoPostgres,
		resetter.ResetUserFactoryProviderSet,
		wire.Bind(new(onbDao.GoodUserAssociateDao), new(*onbDao.GoodUserAssociatesDaoPostgres)),
		datavalidator.WireSet,
		CrossValidationConfigProvider,
		ProvideQuestSDKClientConf,
		types.QuestCacheStorageProvider,
		questSdkInit.GetQuestSDKClient,
		insightsPkg.InsightsPanProcessorWireSet,
		stageproc.NewWBConnectedAccounts,
		stageproc.NewWBOnboardingCompleteStage,
		stageproc.NewSADeclarationStage,
	)
	return &onboarding.Service{}
}

func CSISConfigProvider(conf *genconf.Config) *config.CSIS { return conf.CSIS() }

func ProvideOnbDetailsCacheConfig(gconf *genconf.Config) *genconf.OnbDetailsCacheConfig {
	return gconf.Onboarding().OnbDetailsCacheConfig()
}

func ProvideOnbDetailsMinCacheConfig(gconf *genconf.Config) *genconf.OnbDetailsMinCacheConfig {
	return gconf.Onboarding().OnbDetailsCacheConfig().OnbDetailsMinCacheConfig()
}

func ProvideQuestSDKClientConf(conf *genconf.Config) *questSdkGenConf.Config {
	return conf.QuestSdk()
}

func CrossValidationConfigProvider(conf *genconf.Config) *genconf.CrossValidationConfig {
	return conf.Onboarding().NonResidentCrossValidationConfig()
}

func featureReleaseConfigProvider(conf *genconf.Config) *releaseGenConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func OnboardingConfigProvider(conf *config.Config) *config.OnboardingConfig { return conf.Onboarding }
func OnboardingGenConfigProvider(conf *genconf.Config) *genconf.OnboardingConfig {
	return conf.Onboarding()
}

func OnboardingDetailsCacheStorage(cs types.OnboardingRueidisCacheStorage) wireTypes.OnbDetailsCacheStorage {
	return cs
}

func OnboardingDetailsCriticalCacheStorage(cs types.OnboardingMinRueidisCacheStorage) wireTypes.OnbDetailsMinCacheStorage {
	return cs
}

func VkycConfigProvider(conf *genconf.Config) *genconf.VKYC { return conf.VKYC() }

func InitializeOnboardingUserUpdateVKYCConsumerService(userClient userPb.UsersClient, actorClient actor.ActorClient,
	onbClient onboardingpb.OnboardingClient) *consumer.OnboardingUserUpdateVKYCConsumerService {
	wire.Build(
		consumer.NewOnboardingUserUpdateVKYCConsumerService,
	)
	return &consumer.OnboardingUserUpdateVKYCConsumerService{}
}

func InitialiseWatsonClientService(userClient userPb.UsersClient, onboardingClient onboardingpb.OnboardingClient) *watsonOnbClient.Service {
	wire.Build(watsonOnbClient.NewService)
	return &watsonOnbClient.Service{}
}
