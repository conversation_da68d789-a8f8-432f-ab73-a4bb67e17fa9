// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	genconf2 "github.com/epifi/be-common/quest/sdk/config/genconf"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/liveness"
	location2 "github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/comms/user_preference"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/creditreportv2/derivedattributes"
	"github.com/epifi/gamma/api/cx/watson"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/v2"
	"github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/docs"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/leads"
	"github.com/epifi/gamma/api/omegle"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/product"
	"github.com/epifi/gamma/api/quest/manager"
	"github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	onboarding2 "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	docs2 "github.com/epifi/gamma/api/vendorgateway/docs"
	"github.com/epifi/gamma/api/vendorgateway/ekyc"
	liveness2 "github.com/epifi/gamma/api/vendorgateway/liveness"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	pan2 "github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/api/vkyccall/troubleshoot"
	"github.com/epifi/gamma/insights/pkg"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf3 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/pkg/vendorstore"
	"github.com/epifi/gamma/quest/sdk/init"
	"github.com/epifi/gamma/user/config"
	"github.com/epifi/gamma/user/config/genconf"
	events2 "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding"
	"github.com/epifi/gamma/user/onboarding/consumer"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper/dedupe"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	kyc2 "github.com/epifi/gamma/user/onboarding/helper/kyc"
	omegle2 "github.com/epifi/gamma/user/onboarding/helper/omegle"
	"github.com/epifi/gamma/user/onboarding/helper/onboarding_helper"
	"github.com/epifi/gamma/user/onboarding/helper/passport"
	user2 "github.com/epifi/gamma/user/onboarding/helper/user"
	vkyc2 "github.com/epifi/gamma/user/onboarding/helper/vkyc"
	screener2 "github.com/epifi/gamma/user/onboarding/pkg/screener"
	"github.com/epifi/gamma/user/onboarding/resetter"
	"github.com/epifi/gamma/user/onboarding/stageproc"
	"github.com/epifi/gamma/user/onboarding/stageproc/datavalidator"
	"github.com/epifi/gamma/user/onboarding/stageproc/datavalidator/datafetcher"
	"github.com/epifi/gamma/user/onboarding/watson"
	types2 "github.com/epifi/gamma/user/wire/types"
)

// Injectors from wire.go:

// config: {"syncOnbEventPublisher": "Onboarding().SyncOnboardingSqsPublisher()", "s3Client": "AWS().S3.UsersBucketName", "nrS3Client": "Onboarding().NrBucketName()"}
func InitializeOnboardingService(config3 *genconf.Config, db types.EpifiCRDB, userClient user.UsersClient, savClient savings.SavingsClient, actorClient actor.ActorClient, cardClient provisioning.CardProvisioningClient, consentClient consent.ConsentClient, authClient auth.AuthClient, kycClient kyc.KycClient, broker events.Broker, vkycClient vkyc.VKYCClient, onboardingStagePublisher types2.OnboardingStageEventPublisher, orderClientClient order.OrderServiceClient, userGrpClient group.GroupClient, syncOnbEventPublisher types2.SyncOnboardingSqsPublisher, commsClient comms.CommsClient, s3Client types2.UsersS3Client, employmentClient employment.EmploymentClient, inAppReferralClient inappreferral.InAppReferralClient, ekycClient ekyc.EKYCClient, userPrefClient user_preference.UserPreferenceClient, userConf *config.Config, ncClient namecheck.UNNameCheckClient, livenessClient liveness.LivenessClient, userLocationClient location.LocationClient, obfuscatorClient obfuscator.ObfuscatorClient, authLocClient location2.LocationClient, riskClient risk.RiskClient, screenerClient screener.ScreenerClient, bcClient bankcust.BankCustomerServiceClient, redisClient types.UserRedisStore, feEmpClient employment.EmploymentFeClient, derAttSvc derivedattributes.DerivedAttributesManagerClient, userIntelClient userintel.UserIntelServiceClient, pgdb types.NudgePGDB, watsonClient watson.WatsonClient, panClient pan.PanClient, operationalStatusClient operstatus.OperationalStatusServiceClient, accountBalanceClient balance.BalanceClient, fireflyClient firefly.FireflyClient, loanClient preapprovedloan.PreApprovedLoanClient, productClient product.ProductClient, vgPanClient pan2.PANClient, creditReportV2Client creditreportv2.CreditReportManagerClient, userOnbCacheStorage types.OnboardingRueidisCacheStorage, kycDocExtractionClient docs.DocExtractionClient, omegleClient omegle.OmegleClient, vgDocClient docs2.DocsClient, nrS3Client types2.NrS3Client, onbCriticalCacheStorage types.OnboardingMinRueidisCacheStorage, vkycCallTroubleshootingClient troubleshootpb.TroubleshootClient, tieringClient tiering.TieringClient, vgLivClient liveness2.LivenessClient, questManagerCl manager.ManagerClient, questCacheStorage types.QuestCacheStorage, segmentClient segment.SegmentationServiceClient, mfExternalOrdersClient external.MFExternalOrdersClient, connectedAccountClient connected_account.ConnectedAccountClient, leadsClient leads.UserLeadSvcClient, ffV2Client v2.FireflyV2Client) *onboarding.Service {
	defaultTime := datetime.NewDefaultTime()
	onboardingDaoCrdb := dao.NewOnboardingCrdb(db, defaultTime)
	onbDetailsMinCacheStorage := OnboardingDetailsCriticalCacheStorage(onbCriticalCacheStorage)
	onbDetailsCacheConfig := ProvideOnbDetailsCacheConfig(config3)
	onbDetailsCacheStorage := OnboardingDetailsCacheStorage(userOnbCacheStorage)
	onbDetailsMinCacheConfig := ProvideOnbDetailsMinCacheConfig(config3)
	onbDetailsCacheHelper := dao.NewOnbDetailsCacheHelper(onbDetailsCacheStorage, onbDetailsMinCacheConfig)
	onbDetailsCache := dao.NewOnbDetailsCache(onboardingDaoCrdb, onbDetailsMinCacheStorage, onbDetailsCacheConfig, onbDetailsCacheHelper)
	pinCodeDetailsCrdb := dao.NewPinCodeDetailsCrdb(db)
	onboardingConfig := OnboardingGenConfigProvider(config3)
	gormDB := types.EpifiCRDBGormDBProvider(db)
	doOnce := once.NewDoOnce(gormDB)
	configOnboardingConfig := OnboardingConfigProvider(userConf)
	vendorResponseCRDB := vendorstore.NewVendorResponseDAO(gormDB)
	vendorStore := vendorstore.NewVendorStore(vendorResponseCRDB)
	stuckUserHandler := onboarding.NewStuckUserHandler(doOnce, commsClient, configOnboardingConfig, vendorStore, defaultTime, userClient, vkycClient, watsonClient, panClient, fireflyClient)
	initiateCkycProcessor := onboarding.NewInitiateCkycProcessor()
	kycLivenessProcessor := onboarding.NewKycLivenessProcessor(kycClient, livenessClient)
	kycDedupeCheckProcessor := onboarding.NewKycDedupeCheckProcessor(configOnboardingConfig)
	ekycNameDobValidationProcessor := onboarding.NewEkycNameDobValidationProcessor(configOnboardingConfig)
	deviceRegistrationProcessor := onboarding.NewDeviceRegistrationProcessor(configOnboardingConfig, vendorStore)
	customerCreationProcessor := onboarding.NewCustomerCreationProcessor(configOnboardingConfig, vendorStore)
	accountCreationProcessor := onboarding.NewAccountCreationProcessor(configOnboardingConfig, vendorStore)
	cardAddressProcessor := onboarding.NewCardAdressProcessor()
	dedupeCheckProcessor := onboarding.NewDedupeCheckProcessor(configOnboardingConfig)
	preCustomerCreationDedupeCheckProcessor := onboarding.NewPreCustomerCreationDedupeCheckProcessor(configOnboardingConfig)
	processor := user2.NewUserProcessor(userClient, bcClient, userLocationClient, userIntelClient, userGrpClient, config3)
	panNameCheckProcessor := onboarding.NewPanNameCheckProcessor(userClient, processor)
	shippingAddressProcessor := onboarding.NewShippingAddressProcessor(vendorStore)
	doBandPAN := onboarding.NewDOBandPANProcessor(userIntelClient, userClient)
	ckycProcessor := onboarding.NewCKYCProcessor(kycClient)
	ekycProcessor := onboarding.NewEKYCProcessor(kycClient)
	livenessProcessor := onboarding.NewLivenessProcessor(kycClient, livenessClient)
	riskScreenProc := onboarding.NewRiskScreenProc()
	screenerProc := onboarding.NewScreenerProc(screenerClient)
	upiConsent := onboarding.NewUpiConsentStage(consentClient)
	cardCreationProcessor := onboarding.NewCardCreationProcessor(vendorStore)
	debitCardPinSetupProcessor := onboarding.NewDebitCardPinSetupProcessor()
	unNameCheckProc := onboarding.NewUNNameCheckProc()
	kycNameDobValidationProcessor := onboarding.NewKycNameDobValidationProcessor(configOnboardingConfig)
	bkycProcessor := onboarding.NewBKYCProcessor(kycClient)
	creditReportCheckProcessor := onboarding.NewCreditReportCheckStage(configOnboardingConfig)
	referralFiniteCodeProcessor := onboarding.NewReferralFiniteCodeStage(configOnboardingConfig)
	tncConsentProcessor := onboarding.NewTncConsentStage(configOnboardingConfig)
	intentSelectionProcessor := onboarding.NewIntentSelectionStage(configOnboardingConfig)
	savingIntroConsentProcessor := onboarding.NewSavingIntroConsentStage(configOnboardingConfig)
	motherFatherNameProcessor := onboarding.NewMotherFatherNameStage(configOnboardingConfig)
	vkycProcessor := onboarding.NewVkycStage(configOnboardingConfig)
	preCustomerCreationCheckProcessor := onboarding.NewPreCustomerCreationCheckStage(configOnboardingConfig)
	optionalVkycProcessor := onboarding.NewOptionalVkycStage(configOnboardingConfig)
	addmoneyProcessor := onboarding.NewAddmoneyStage(configOnboardingConfig)
	aadharMobileValidationProcessor := onboarding.NewAadharMobileValidationStage(configOnboardingConfig)
	updateProfileDetailsProcessor := onboarding.NewUpdateProfileDetailsStage(configOnboardingConfig)
	stageTroubleshooter := onboarding.NewStageTroubleshooter(configOnboardingConfig, onbDetailsCache, initiateCkycProcessor, kycLivenessProcessor, kycDedupeCheckProcessor, ekycNameDobValidationProcessor, deviceRegistrationProcessor, customerCreationProcessor, accountCreationProcessor, cardAddressProcessor, dedupeCheckProcessor, preCustomerCreationDedupeCheckProcessor, panNameCheckProcessor, shippingAddressProcessor, doBandPAN, ckycProcessor, ekycProcessor, livenessProcessor, riskScreenProc, screenerProc, upiConsent, cardCreationProcessor, debitCardPinSetupProcessor, processor, unNameCheckProc, kycNameDobValidationProcessor, bkycProcessor, creditReportCheckProcessor, referralFiniteCodeProcessor, tncConsentProcessor, intentSelectionProcessor, savingIntroConsentProcessor, motherFatherNameProcessor, vkycProcessor, preCustomerCreationCheckProcessor, optionalVkycProcessor, addmoneyProcessor, aadharMobileValidationProcessor, updateProfileDetailsProcessor)
	persistentQueue := persistentqueue.NewPersistentQueue(gormDB)
	proc := passport.NewPassportProcImpl(onbDetailsCache, kycDocExtractionClient)
	motherFatherNameStage := stageproc.NewMotherFatherNameStage(processor, onboardingConfig, proc, kycClient)
	locationCheckStage := stageproc.NewLocationCheckStage(userLocationClient, obfuscatorClient, processor, userClient)
	referralFiniteCodeStage := stageproc.NewReferralFiniteCodeStage(actorClient, userClient, userGrpClient, inAppReferralClient, onboardingConfig, broker, userLocationClient, obfuscatorClient, processor)
	tncConsentStage := stageproc.NewTncConsentStage(consentClient, userPrefClient, actorClient, userClient, userGrpClient, onboardingConfig)
	creditReportVerificationStage := stageproc.NewCreditReportVerificationStage(screenerClient, onbDetailsCache, broker, processor)
	incomeEstimateCheckStage := stageproc.NewIncomeEstimateCheckStage(config3, screenerClient)
	lendabilityCheckStage := stageproc.NewLendabilityCheckStage(screenerClient)
	creditReportCheckStage := stageproc.NewCreditReportCheckStage(onbDetailsCache, userClient, creditReportV2Client)
	employmentVerificationStage := stageproc.NewEmploymentVerificationStage(employmentClient, screenerClient, feEmpClient)
	itrIntimationVerificationStage := stageproc.NewITRIntimationVerificationStage(screenerClient, broker)
	goodUserAssociatesDaoPostgres := dao.NewGoodUserAssociatesDaoPostgres(pgdb)
	fiEventLogger := events2.NewFiEventLogger(broker, userClient, employmentClient, derAttSvc, userIntelClient, authLocClient, goodUserAssociatesDaoPostgres, config3, creditReportV2Client)
	onboardingHelper := onboarding_helper.NewOnboardingHelper(onbDetailsCache, fiEventLogger, commsClient, onboardingConfig)
	appScreeningStage := stageproc.NewAppScreeningStage(onboardingConfig, broker, screenerClient, employmentClient, fiEventLogger, onbDetailsCache, feEmpClient, userClient, userIntelClient, inAppReferralClient, actorClient, userGrpClient, processor, creditReportV2Client, onboardingHelper, tieringClient)
	dobPanStage := stageproc.NewDobPanStage(defaultTime, onboardingConfig, processor, inAppReferralClient, consentClient, userGrpClient, userClient, actorClient, creditReportV2Client, ncClient, onbDetailsCache, fiEventLogger, vgPanClient)
	dedupeProc := dedupe.NewDedupeProcImpl(userClient, broker, onboardingConfig, onbDetailsCache, defaultTime, bcClient)
	dedupeCheckStage := stageproc.NewDedupeCheckStage(onbDetailsCache, processor, dedupeProc, fiEventLogger)
	initCkycStage := stageproc.NewInitCkycStage(kycClient, onbDetailsCache, onboardingConfig, defaultTime, processor)
	genconfVKYC := VkycConfigProvider(config3)
	kycProc := kyc2.NewKYCProcImpl(kycClient, bcClient, genconfVKYC, kycDocExtractionClient, omegleClient, nrS3Client)
	ensureKycAvailabilityStage := stageproc.NewEnsureKycAvailabilityStage(onbDetailsCache, kycProc, processor)
	s3S3Client := types2.UsersS3ClientProvider(s3Client)
	updateCustomerDetailsStage := stageproc.NewUpdateCustomerDetailsStage(userClient, kycClient, onbDetailsCache, config3, s3S3Client, livenessClient, employmentClient, bcClient, processor, vkycClient)
	panNameCheckStage := stageproc.NewPanNameCheckStage(onbDetailsCache, persistentQueue, onboardingConfig, ncClient, processor, fiEventLogger, panClient)
	kycDedupeCheckStage := stageproc.NewKycDedupeCheckStage(userClient, onbDetailsCache, kycClient, dedupeProc, fiEventLogger)
	plOnboardingStatusCheck := stageproc.NewPLOnboardingStatusCheckStage(loanClient, onbDetailsCache, defaultTime)
	confirmCardMailingAddressStage := stageproc.NewConfirmCardMailingAddressStage(userClient, kycClient, bcClient, onboardingConfig)
	workEmailOtpStage := stageproc.NewWorkEmailOtpStage(screenerClient, userClient)
	gmailVerificationStage := stageproc.NewGmailVerificationStage(screenerClient)
	debitCardPinSetStage := stageproc.NewDebitCardPinSetStage(cardClient, savClient, onbDetailsCache, defaultTime)
	csis := CSISConfigProvider(config3)
	featureReleaseConfig := featureReleaseConfigProvider(config3)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	onbAddMoneyUtilImpl := stageproc.NewOnbAddMoneyUtils(onboardingConfig, evaluator, processor, orderClientClient, actorClient, userClient, userGrpClient, tieringClient, csis, bcClient, kycClient)
	addMoneyStage := stageproc.NewAddMoneyStage(savClient, orderClientClient, userClient, userGrpClient, csis, kycClient, onboardingConfig, bcClient, evaluator, processor, actorClient, broker, accountBalanceClient, onbAddMoneyUtilImpl, onbDetailsCache)
	onboardingCompleteStage := stageproc.NewOnboardingCompleteStage(onboardingConfig, onbDetailsCache, onboardingStagePublisher, defaultTime, bcClient)
	panUniquenessCheckStage := stageproc.NewPanUniquenessCheckStage(config3, userClient, onbDetailsCache, processor, fiEventLogger)
	heuristicBasedScreening := stageproc.NewHeuristicBasedScreening(screenerClient)
	deviceRegistration := stageproc.NewDeviceRegistrationStage(authClient, userLocationClient, userClient, defaultTime, onbDetailsCache)
	vkycOnboardingStage := stageproc.NewVKYCOnboardingStage(genconfVKYC, onbDetailsCache, vkycClient, onboardingConfig, bcClient, fiEventLogger, actorClient, userClient, userGrpClient, savClient)
	riskScreeningStage := stageproc.NewRiskScreeningStage(userClient, onboardingConfig, onbDetailsCache, riskClient, fiEventLogger, processor)
	uanPresenceCheckStage := stageproc.NewUANPresenceCheckStage(onboardingConfig, screenerClient, employmentClient, creditReportV2Client, userIntelClient)
	permissionStage := stageproc.NewPermissionStage(onboardingConfig, actorClient, userClient, userGrpClient)
	ckycStage := stageproc.NewCKYCStage(kycClient, onbDetailsCache, config3, processor)
	ekycStage := stageproc.NewEKYCStage(kycClient, onbDetailsCache, onboardingConfig, kycProc, fiEventLogger, processor, inAppReferralClient)
	livenessStage := stageproc.NewLivenessStage(kycClient, onbDetailsCache, onboardingConfig, livenessClient, userClient, broker, fiEventLogger, s3S3Client, config3)
	customerCreationStage := stageproc.NewCustomerCreationStage(onboardingConfig, onbDetailsCache, userClient, bcClient, panClient, vgPanClient, kycProc)
	accountCreationStage := stageproc.NewAccountCreationStage(onboardingConfig, onbDetailsCache, savClient, authClient, fiEventLogger, bcClient)
	cardCreationStage := stageproc.NewCardCreationStage(configOnboardingConfig, onbDetailsCache, actorClient, cardClient, watsonClient, savClient)
	unNameCheckStage := stageproc.NewUNNameCheckStage(processor, onbDetailsCache, kycClient)
	nextActionDecisionCacheConfig := types2.NextActionDecisionCacheConfigProvider(config3)
	nextActionDecisionCacheStorage := dao.NewNextActionDecisionCacheStorage(onbDetailsCacheStorage, nextActionDecisionCacheConfig)
	epfoCompanySearchStage := stageproc.NewEPFOCompanySearch(screenerClient)
	caScreenerStage := stageproc.NewCAScreenerStage(screenerClient, onbDetailsCache)
	preCustomerCreationCheckStage := stageproc.NewPreCustomerCreationCheckStage(onbDetailsCache, userClient, kycClient, defaultTime, dedupeProc, fiEventLogger, processor, config3, vkycClient, bcClient, onboardingConfig, panClient, kycProc, vgPanClient)
	bkyc := stageproc.NewBKYCStage(userGrpClient, kycClient, userClient, bcClient, vkycClient)
	kycNameDOBValidation := stageproc.NewKYCNameDOBValidationStage(kycClient, ekycClient, onbDetailsCache, onboardingConfig, processor, userGrpClient, fiEventLogger)
	client := types.UserRedisStoreRedisClientProvider(redisClient)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client, clock, uuidGenerator)
	fiLiteRiskScreeningStage := stageproc.NewFiLiteRiskScreeningStage(riskClient, onbDetailsCache, onboardingConfig, fiEventLogger, userClient)
	preOnboardingResetHandler := resetter.NewPreOnboardingResetHandler(onbDetailsCache, vkycClient, livenessClient, processor, savClient, kycProc, defaultTime, userClient, actorClient, bcClient, authClient, watsonClient, productClient, employmentClient, omegleClient)
	closedSavingsAccountResetHandler := resetter.NewClosedSavingsAccountResetHandler(onbDetailsCache, consentClient, userClient, savClient, bcClient, actorClient, authClient, processor, operationalStatusClient, employmentClient, productClient, omegleClient)
	resetUserFactory := resetter.NewResetUserFactory(preOnboardingResetHandler, closedSavingsAccountResetHandler)
	deeplinkProc := deeplink.NewProc(userClient, userGrpClient, onboardingConfig, productClient)
	intentSelectionStage := stageproc.NewIntentSelectionStage(onboardingConfig, userClient, onbDetailsCache, deeplinkProc, defaultTime, fiEventLogger, inAppReferralClient, userGrpClient, leadsClient)
	creditCardOnboardingStatusCheck := stageproc.NewCreditCardOnboardingStatusCheck(ffV2Client, onbDetailsCache, defaultTime, deeplinkProc, userClient)
	saIntroConsentStage := stageproc.NewSaIntroConsentStage(onbDetailsCache, consentClient, config3, fiEventLogger, deeplinkProc, evaluator)
	aadharMobileValidationStage := stageproc.NewAadharMobileValidationStage(ekycClient, kycClient, processor, onbDetailsCache)
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	updateProfileDetails := stageproc.NewUpdateProfileDetailsStage(bcClient, employmentClient, processor, onbDetailsCache, onboardingConfig, domainIdGenerator, userClient, savClient)
	passportVerificationStage := stageproc.NewPassportVerificationStage(onboardingConfig, config3, kycDocExtractionClient, nrS3Client, onbDetailsCache, vgDocClient, proc, defaultTime, fiEventLogger)
	visaVerificationStage := stageproc.NewVisaVerificationStage()
	countryIDVerificationStage := stageproc.NewCountryIDVerificationStage(onbDetailsCache, kycDocExtractionClient, onboardingConfig, defaultTime)
	defaultDataFetcher := datafetcher.NewDefaultDataFetcher()
	userInputDataFetcher := datafetcher.NewUserInputDataFetcher(config3, defaultDataFetcher, userClient)
	panDataFetcher := datafetcher.NewPanDataFetcher(defaultDataFetcher, userClient)
	passportDataFetcher := datafetcher.NewPassportDataFetcher(config3, defaultDataFetcher, proc, nrS3Client)
	emiratesIdDataFetcher := datafetcher.NewEmiratesIdDataFetcher(defaultDataFetcher, config3, kycDocExtractionClient)
	qatarIdDataFetcher := datafetcher.NewQatarIdDataFetcher(kycDocExtractionClient)
	crossValidationConfig := CrossValidationConfigProvider(config3)
	userNameChecker := datavalidator.NewUserNameChecker(ncClient, crossValidationConfig)
	dobChecker := datavalidator.NewDOBChecker()
	genderChecker := datavalidator.NewGenderChecker()
	nationalityChecker := datavalidator.NewNationalityChecker()
	motherNameChecker := datavalidator.NewMotherNameChecker(ncClient, crossValidationConfig)
	fatherNameChecker := datavalidator.NewFatherNameChecker(ncClient, crossValidationConfig)
	faceChecker := datavalidator.NewFaceChecker(livenessClient, onboardingConfig)
	passportNumberChecker := datavalidator.NewPassportNumberChecker()
	passportDateOfExpiryChecker := datavalidator.NewPassportDateOfExpiryChecker()
	kycDataValidator := datavalidator.NewKYCDataValidator(onboardingConfig, userInputDataFetcher, panDataFetcher, passportDataFetcher, emiratesIdDataFetcher, qatarIdDataFetcher, userNameChecker, dobChecker, genderChecker, nationalityChecker, motherNameChecker, fatherNameChecker, faceChecker, passportNumberChecker, passportDateOfExpiryChecker)
	nrOnboardingCrossDataValidationStage := stageproc.NewNROnboardingCrossDataValidationStage(config3, kycDataValidator, onbDetailsCache)
	nrLivenessStage := stageproc.NewNRLivenessStage(livenessClient, fiEventLogger, userClient, onboardingConfig, config3, s3S3Client, broker, onbDetailsCache, kycDocExtractionClient)
	nrKycDedupeCheckStage := stageproc.NewNRKycDedupeCheckStage(config3, dedupeProc, userClient, proc, onbDetailsCache)
	nrunNameCheckStage := stageproc.NewNRUNNameCheckStage(onbDetailsCache, kycClient, processor)
	softIntentSelectionStage := stageproc.NewSoftIntentSelectionStage(fiEventLogger, onboardingConfig, userClient, creditReportV2Client)
	nrUpdateCustomerDetailsStage := stageproc.NewNRUpdateCustomerDetailsStage(userClient, bcClient, onbDetailsCache, employmentClient, processor, vkycClient, proc)
	nrCommunicationAddress := stageproc.NewNRCommunicationAddress(userClient, processor)
	applicationIdGen := omegle2.NewApplicationIdGen()
	nrvkycStage := stageproc.NewNRVKYCStage(omegleClient, onbDetailsCache, kycDocExtractionClient, employmentClient, userClient, onboardingConfig, bcClient, proc, vkycCallTroubleshootingClient, applicationIdGen)
	nrCardCreation := stageproc.NewNRCardCreationStage(onbDetailsCache, onboardingConfig, actorClient, cardClient, savClient)
	nrPreCustomerCreationCheckStage := stageproc.NewNRPreCustomerCreationCheckStage(onbDetailsCache, userClient, kycClient, defaultTime, dedupeProc, fiEventLogger, processor, config3, vkycClient, bcClient, proc)
	nrLocationCheckStage := stageproc.NewNRLocationCheckStage(userLocationClient, obfuscatorClient, userClient, onboardingConfig)
	nrEnsureKycAvailabilityStage := stageproc.NewNREnsureKycAvailabilityStage(kycClient, onbDetailsCache, kycProc, processor)
	nroAccountCreationStage := stageproc.NewNROAccountCreationStage(onbDetailsCache, onboardingConfig, savClient, authClient, fiEventLogger, bcClient, consentClient)
	genconfConfig := ProvideQuestSDKClientConf(config3)
	cacheStorage := types.QuestCacheStorageProvider(questCacheStorage)
	questsdkClient := questsdkinit.GetQuestSDKClient(genconfConfig, questManagerCl, userGrpClient, userClient, actorClient, segmentClient, cacheStorage, broker)
	orderPhysicalCardStage := stageproc.NewOrderPhysicalCardStage(cardClient, config3, evaluator, vkycClient, inAppReferralClient, userClient, userGrpClient, questsdkClient)
	openMinBalanceAccountStage := stageproc.NewOpenMinBalanceAccountStage(consentClient, tieringClient, userClient, userGrpClient, evaluator)
	smsParserConsentStage := stageproc.NewSMSParserConsentStage(config3, consentClient)
	initiateCreditReportFetchStage := stageproc.NewInitiateCreditReportFetchStage(onboardingConfig, creditReportV2Client, consentClient, onbDetailsCache, fiEventLogger)
	panProcessor := pkg.NewUnverifiedPanProcessor(userClient, creditReportV2Client, connectedAccountClient)
	wealthAnalyserOnboardingStatusCheck := stageproc.NewWealthAnalyserOnboardingStatusCheckStage(onbDetailsCache, userClient, defaultTime, config3, fiEventLogger, mfExternalOrdersClient, evaluator, panProcessor, connectedAccountClient)
	qatarIdVerificationStage := stageproc.NewQatarIdVerificationStage(onbDetailsCache, kycDocExtractionClient, onboardingConfig, defaultTime, vgLivClient)
	nrQatarVKYCStage := stageproc.NewNRQatarVKYCStage(omegleClient, onbDetailsCache, kycDocExtractionClient, employmentClient, userClient, onboardingConfig, bcClient, proc, vkycCallTroubleshootingClient, applicationIdGen)
	checkProcessor := screener2.NewScreenerCheckProcessor(screenerClient, broker)
	smsParserDataVerificationStage := stageproc.NewSMSParserDataVerificationStage(onboardingConfig, screenerClient, onbDetailsCache, broker, processor, checkProcessor)
	installedAppsCheckStage := stageproc.NewInstalledAppsCheckStage(onboardingConfig, screenerClient, onbDetailsCache, broker, processor, checkProcessor)
	vkycProc := vkyc2.NewProc(vkycClient)
	preAccountCreationAddMoneyStage := stageproc.NewAccountCreationAddMoneyStage(onbAddMoneyUtilImpl, bcClient, evaluator, onboardingConfig, onbDetailsCache, userClient)
	collectAdditionalProfileDetailsForFederalLoansStage := stageproc.NewCollectAdditionalProfileDetailsForFederalLoansStage(userClient)
	waitForAutoPan := stageproc.NewWaitForAutoPan(onbDetailsCache, userClient, evaluator)
	wbConnectedAccounts := stageproc.NewWBConnectedAccounts(connectedAccountClient, onbDetailsCache, defaultTime, evaluator, mfExternalOrdersClient)
	wbOnboardingCompleteStage := stageproc.NewWBOnboardingCompleteStage(onbDetailsCache, defaultTime)
	saDeclarationStage := stageproc.NewSADeclarationStage(consentClient, onboardingConfig)
	ensureCreditReportAvailabilityStage := stageproc.NewEnsureCreditReportAvailabilityStage(onboardingConfig, creditReportV2Client, consentClient, onbDetailsCache, processor)
	service := onboarding.NewService(onbDetailsCache, pinCodeDetailsCrdb, onboardingConfig, userClient, savClient, actorClient, consentClient, authClient, broker, defaultTime, vkycClient, syncOnbEventPublisher, stuckUserHandler, commsClient, stageTroubleshooter, persistentQueue, motherFatherNameStage, locationCheckStage, referralFiniteCodeStage, tncConsentStage, creditReportVerificationStage, incomeEstimateCheckStage, lendabilityCheckStage, creditReportCheckStage, employmentVerificationStage, itrIntimationVerificationStage, appScreeningStage, dobPanStage, dedupeCheckStage, initCkycStage, ensureKycAvailabilityStage, updateCustomerDetailsStage, panNameCheckStage, kycDedupeCheckStage, plOnboardingStatusCheck, confirmCardMailingAddressStage, workEmailOtpStage, gmailVerificationStage, debitCardPinSetStage, addMoneyStage, onboardingCompleteStage, panUniquenessCheckStage, heuristicBasedScreening, deviceRegistration, vkycOnboardingStage, livenessClient, riskScreeningStage, uanPresenceCheckStage, permissionStage, ckycStage, ekycStage, livenessStage, customerCreationStage, accountCreationStage, cardCreationStage, unNameCheckStage, processor, nextActionDecisionCacheStorage, epfoCompanySearchStage, caScreenerStage, preCustomerCreationCheckStage, bkyc, kycNameDOBValidation, fiEventLogger, redisV9LockManager, kycProc, bcClient, fiLiteRiskScreeningStage, resetUserFactory, intentSelectionStage, creditCardOnboardingStatusCheck, saIntroConsentStage, watsonClient, deeplinkProc, productClient, aadharMobileValidationStage, updateProfileDetails, screenerClient, creditReportV2Client, passportVerificationStage, visaVerificationStage, countryIDVerificationStage, nrOnboardingCrossDataValidationStage, nrLivenessStage, nrKycDedupeCheckStage, nrunNameCheckStage, softIntentSelectionStage, nrUpdateCustomerDetailsStage, nrCommunicationAddress, nrvkycStage, s3S3Client, nrCardCreation, kycDocExtractionClient, nrPreCustomerCreationCheckStage, nrLocationCheckStage, proc, onboardingHelper, nrEnsureKycAvailabilityStage, nrS3Client, nroAccountCreationStage, orderPhysicalCardStage, applicationIdGen, openMinBalanceAccountStage, smsParserConsentStage, initiateCreditReportFetchStage, wealthAnalyserOnboardingStatusCheck, qatarIdVerificationStage, nrQatarVKYCStage, smsParserDataVerificationStage, installedAppsCheckStage, vkycProc, preAccountCreationAddMoneyStage, vgDocClient, collectAdditionalProfileDetailsForFederalLoansStage, kycDataValidator, waitForAutoPan, wbConnectedAccounts, wbOnboardingCompleteStage, saDeclarationStage, ensureCreditReportAvailabilityStage)
	return service
}

func InitializeOnboardingUserUpdateVKYCConsumerService(userClient user.UsersClient, actorClient actor.ActorClient, onbClient onboarding2.OnboardingClient) *consumer.OnboardingUserUpdateVKYCConsumerService {
	onboardingUserUpdateVKYCConsumerService := consumer.NewOnboardingUserUpdateVKYCConsumerService(userClient, actorClient, onbClient)
	return onboardingUserUpdateVKYCConsumerService
}

func InitialiseWatsonClientService(userClient user.UsersClient, onboardingClient onboarding2.OnboardingClient) *watson_client.Service {
	service := watson_client.NewService(userClient, onboardingClient)
	return service
}

// wire.go:

func CSISConfigProvider(conf *genconf.Config) *config.CSIS { return conf.CSIS() }

func ProvideOnbDetailsCacheConfig(gconf *genconf.Config) *genconf.OnbDetailsCacheConfig {
	return gconf.Onboarding().OnbDetailsCacheConfig()
}

func ProvideOnbDetailsMinCacheConfig(gconf *genconf.Config) *genconf.OnbDetailsMinCacheConfig {
	return gconf.Onboarding().OnbDetailsCacheConfig().OnbDetailsMinCacheConfig()
}

func ProvideQuestSDKClientConf(conf *genconf.Config) *genconf2.Config {
	return conf.QuestSdk()
}

func CrossValidationConfigProvider(conf *genconf.Config) *genconf.CrossValidationConfig {
	return conf.Onboarding().NonResidentCrossValidationConfig()
}

func featureReleaseConfigProvider(conf *genconf.Config) *genconf3.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}

func OnboardingConfigProvider(conf *config.Config) *config.OnboardingConfig { return conf.Onboarding }

func OnboardingGenConfigProvider(conf *genconf.Config) *genconf.OnboardingConfig {
	return conf.Onboarding()
}

func OnboardingDetailsCacheStorage(cs types.OnboardingRueidisCacheStorage) types2.OnbDetailsCacheStorage {
	return cs
}

func OnboardingDetailsCriticalCacheStorage(cs types.OnboardingMinRueidisCacheStorage) types2.OnbDetailsMinCacheStorage {
	return cs
}

func VkycConfigProvider(conf *genconf.Config) *genconf.VKYC { return conf.VKYC() }
