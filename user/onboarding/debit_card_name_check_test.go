package onboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/user/onboarding/helper/mocks"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"

	userPb "github.com/epifi/gamma/api/user"
	usersMocks "github.com/epifi/gamma/api/user/mocks"
	pb "github.com/epifi/gamma/api/user/onboarding"
	daoMocks "github.com/epifi/gamma/user/onboarding/dao/mocks"
)

func TestService_DebitCardNameCheck(t *testing.T) {
	t.Parallel()
	dcNameFixture := &commontypes.Name{
		FirstName: "Alpha",
		LastName:  "Bravo",
	}
	nonDCNameFixture := &commontypes.Name{
		FirstName: "Charlie",
		LastName:  "Delta",
	}
	getUserFixture := func(kycName, panName *commontypes.Name) *userPb.User {
		return &userPb.User{
			Id: "user-id",
			Profile: &userPb.Profile{
				KycName: kycName,
				PanName: panName,
			},
		}
	}

	type mockStruct struct {
		userProc   *mocks.MockUserProcessor
		userClient *usersMocks.MockUsersClient
		onbDao     *daoMocks.MockOnboardingDao
	}
	type args struct {
		req *pb.DebitCardNameCheckRequest
	}
	tests := map[string]struct {
		args     args
		want     *pb.DebitCardNameCheckResponse
		wantErr  error
		mockFunc func(*mockStruct)
	}{
		"KYC Name Check Passed": {
			args: args{
				req: &pb.DebitCardNameCheckRequest{
					ActorId:       "actor-id",
					DebitCardName: dcNameFixture,
				},
			},
			want: &pb.DebitCardNameCheckResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: nil,
			mockFunc: func(m *mockStruct) {
				m.userProc.EXPECT().GetUserByActorId(context.Background(), "actor-id").Return(getUserFixture(dcNameFixture, nonDCNameFixture), nil)
				m.userClient.EXPECT().UpdateUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(&userPb.UpdateUserResponse{Status: rpc.StatusOk()}, nil)
				m.onbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), gomock.Any()).Return(&pb.OnboardingDetails{}, nil)
				m.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
		"PAN Name Check Passed": {
			args: args{
				req: &pb.DebitCardNameCheckRequest{
					ActorId:       "actor-id",
					DebitCardName: dcNameFixture,
				},
			},
			want: &pb.DebitCardNameCheckResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: nil,
			mockFunc: func(m *mockStruct) {
				m.userProc.EXPECT().GetUserByActorId(context.Background(), "actor-id").Return(getUserFixture(nonDCNameFixture, dcNameFixture), nil)
				m.userClient.EXPECT().UpdateUser(gomock.Any(), gomock.Any(), gomock.Any()).Return(&userPb.UpdateUserResponse{Status: rpc.StatusOk()}, nil)
				m.onbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), gomock.Any()).Return(&pb.OnboardingDetails{}, nil)
				m.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
		"No Name Check Passed": {
			args: args{
				req: &pb.DebitCardNameCheckRequest{
					ActorId:       "actor-id",
					DebitCardName: dcNameFixture,
				},
			},
			want: &pb.DebitCardNameCheckResponse{
				Status: rpc.NewStatusWithoutDebug(uint32(pb.DebitCardNameCheckResponse_NAME_CHECK_FAILED), "name match failed"),
			},
			wantErr: nil,
			mockFunc: func(m *mockStruct) {
				m.userProc.EXPECT().GetUserByActorId(context.Background(), "actor-id").Return(getUserFixture(nonDCNameFixture, nonDCNameFixture), nil)
				m.onbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), gomock.Any()).Return(&pb.OnboardingDetails{}, nil)
				m.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}
	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			userProc := mocks.NewMockUserProcessor(ctr)
			userClient := usersMocks.NewMockUsersClient(ctr)
			onbDao := daoMocks.NewMockOnboardingDao(ctr)
			s := &Service{
				userProcessor: userProc,
				userClient:    userClient,
				onboardingDao: onbDao,
			}
			if tt.mockFunc != nil {
				tt.mockFunc(&mockStruct{
					userProc:   userProc,
					userClient: userClient,
					onbDao:     onbDao,
				})
			}
			got, err := s.DebitCardNameCheck(context.Background(), tt.args.req)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Unexpected error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if tt.wantErr.Error() != err.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("DebitCardNameCheck value is diff: %v", diff)
			}
		})
	}
}
