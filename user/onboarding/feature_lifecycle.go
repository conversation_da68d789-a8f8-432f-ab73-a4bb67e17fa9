package onboarding

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	productPb "github.com/epifi/gamma/api/product"
	screenerPb "github.com/epifi/gamma/api/screener"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

func (s *Service) GetFeatureLifecycle(ctx context.Context, req *onbPb.GetFeatureLifecycleRequest) (*onbPb.GetFeatureLifecycleResponse, error) {
	var (
		onboardingDetails     *onbPb.OnboardingDetails
		screenerDetails       []*screenerPb.CheckDetails
		productsStatusInfoMap map[string]*productPb.ProductInfo
	)

	grp, grpCtx := errgroup.WithContext(ctx)
	// 1. get onboarding details of the user
	grp.Go(func() error {
		getDetailsRes, err := s.GetDetails(grpCtx, &onbPb.GetDetailsRequest{
			ActorId:    req.GetActorId(),
			CachedData: req.GetWantCachedData(),
		})
		if rpcErr := epifigrpc.RPCError(getDetailsRes, err); rpcErr != nil {
			logger.Error(grpCtx, "error in GetDetails", zap.Error(rpcErr))
			return fmt.Errorf("error in GetDetails, err: %w", rpcErr)
		}
		onboardingDetails = getDetailsRes.GetDetails()
		return nil
	})
	// 2. get screener details of the user
	grp.Go(func() error {
		screenerAttemptsRes, err := s.screenerClient.GetScreenerAttemptsByActorId(grpCtx, &screenerPb.GetScreenerAttemptsByActorIdRequest{
			ActorId:    req.GetActorId(),
			CachedData: req.GetWantCachedData(),
		})
		if rpcErr := epifigrpc.RPCError(screenerAttemptsRes, err); rpcErr != nil {
			if screenerAttemptsRes.GetStatus().IsRecordNotFound() {
				return nil
			}
			logger.Error(grpCtx, "error in GetScreenerAttemptsByActorId", zap.Error(rpcErr))
			return fmt.Errorf("error in GetScreenerAttemptsByActorId, err: %w", rpcErr)
		}
		screenerDetails = screenerAttemptsRes.GetChecksMap()
		return nil
	})
	// 3. get products status of the user
	grp.Go(func() error {
		productsStatusRes, err := s.productClient.GetProductsStatus(grpCtx, &productPb.GetProductsStatusRequest{
			ActorId: req.GetActorId(),
			ProductTypes: []productPb.ProductType{
				//	productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
				//	productPb.ProductType_PRODUCT_TYPE_CREDIT_CARD,
				productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
				productPb.ProductType_PRODUCT_TYPE_TPAP,
				productPb.ProductType_PRODUCT_TYPE_WEALTH_ANALYSER,
			},
		})
		if rpcErr := epifigrpc.RPCError(productsStatusRes, err); rpcErr != nil {
			logger.Error(grpCtx, "error in GetProductsStatus for actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(rpcErr))
			return fmt.Errorf("error in GetProductsStatus, err: %w", rpcErr)
		}
		productsStatusInfoMap = productsStatusRes.GetProductInfoMap()
		return nil
	})
	if err := grp.Wait(); err != nil {
		logger.Error(grpCtx, "error while getting feature lifecycle", zap.Error(err))
		return &onbPb.GetFeatureLifecycleResponse{Status: rpc.StatusInternal()}, nil
	}

	featureLifecycleMap := make(map[string]*onbPb.FeatureLifecycle)
	for _, feature := range req.GetFeatures() {
		featureLifecycle := &onbPb.FeatureLifecycle{Feature: feature}
		// 1. set intent selection info
		featureLifecycle.IntentSelectionInfo = getFeatureIntentSelectionInfo(ctx, feature, onboardingDetails)
		// 3. set eligibility status from screener details
		featureLifecycle.EligibilityStatus = getFeatureLifecycleEligibility(ctx, feature, screenerDetails)
		// 4. set activation status
		featureLifecycle.ActivationStatus = getFeatureLifecycleActivationStatus(ctx, feature, onboardingDetails, productsStatusInfoMap)
		featureLifecycleMap[feature.String()] = featureLifecycle
	}

	return &onbPb.GetFeatureLifecycleResponse{
		Status:              rpc.StatusOk(),
		FeatureLifecycleMap: featureLifecycleMap,
		OnboardingDetails:   onboardingDetails,
	}, nil
}

func getFeatureIntentSelectionInfo(_ context.Context, feature onbPb.Feature, onboardingDetails *onbPb.OnboardingDetails) *onbPb.FeatureIntentSelectionInfo {
	selectedAsHardIntent := onbPb.IntentToFeatureMap[onboardingDetails.GetStageMetadata().GetIntentSelectionMetadata().GetSelection()] == feature
	selectedSoftIntents := lo.Filter(onboardingDetails.GetStageMetadata().GetSoftIntentSelectionMetadata().GetSelection(), func(softIntent onbPb.OnboardingSoftIntent, _ int) bool {
		return onbPb.SoftIntentCategoryToFeatureMap[onbPb.SoftIntentToCategoryMap[softIntent]] == feature
	})

	return &onbPb.FeatureIntentSelectionInfo{
		SelectedAsHardIntent: selectedAsHardIntent,
		SelectedSoftIntents:  selectedSoftIntents,
	}
}

// TODO: check with team, if we need to handle other features as well using products status
func getFeatureLifecycleActivationStatus(_ context.Context, feature onbPb.Feature, onboardingDetails *onbPb.OnboardingDetails, productsStatusInfoMap map[string]*productPb.ProductInfo) onbPb.FeatureStatus {
	switch feature {
	case onbPb.Feature_FEATURE_UPI_TPAP:
		featureStatus := productsStatusInfoMap[productPb.ProductType_PRODUCT_TYPE_TPAP.String()].GetProductStatus()
		return getOnboardingStatusBasedOnProductStatus(featureStatus)
	case onbPb.Feature_FEATURE_WEALTH_ANALYSER:
		featureStatus := productsStatusInfoMap[productPb.ProductType_PRODUCT_TYPE_WEALTH_ANALYSER.String()].GetProductStatus()
		return getOnboardingStatusBasedOnProductStatus(featureStatus)
	case onbPb.Feature_FEATURE_PL:
		featureStatus := productsStatusInfoMap[productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS.String()].GetProductStatus()
		return getOnboardingStatusBasedOnProductStatus(featureStatus)
	default:
		return onboardingDetails.GetFeatureDetails().GetFeatureInfo()[feature.String()].GetFeatureStatus()
	}
}

func getOnboardingStatusBasedOnProductStatus(productStatus productPb.ProductStatus) onbPb.FeatureStatus {
	switch productStatus {
	case productPb.ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS:
		return onbPb.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS
	case productPb.ProductStatus_PRODUCT_STATUS_ONBOARDING_FAILURE:
		return onbPb.FeatureStatus_FEATURE_STATUS_ONBOARDING_FAILURE
	case productPb.ProductStatus_PRODUCT_STATUS_ACTIVE:
		return onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE
	case productPb.ProductStatus_PRODUCT_STATUS_INACTIVE:
		return onbPb.FeatureStatus_FEATURE_STATUS_INACTIVE
	case productPb.ProductStatus_PRODUCT_STATUS_REJECTED:
		return onbPb.FeatureStatus_FEATURE_STATUS_REJECTED
	default:
		return onbPb.FeatureStatus_FEATURE_STATUS_UNSPECIFIED
	}
}

func getFeatureLifecycleEligibility(ctx context.Context, feature onbPb.Feature, screenerDetails []*screenerPb.CheckDetails) *onbPb.FeatureEligibility {
	var relevantCheckDetails []*screenerPb.CheckDetails

	switch feature {
	case onbPb.Feature_FEATURE_SA:
		relevantCheckDetails = lo.Filter(screenerDetails, func(check *screenerPb.CheckDetails, _ int) bool {
			return check.GetCheckType() == screenerPb.CheckType_CHECK_TYPE_INCOME_ESTIMATE ||
				check.GetCheckType() == screenerPb.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS
		})
	case onbPb.Feature_FEATURE_PL:
		relevantCheckDetails = lo.Filter(screenerDetails, func(check *screenerPb.CheckDetails, _ int) bool {
			return check.GetCheckType() == screenerPb.CheckType_CHECK_TYPE_LENDABILITY
		})
	}
	if len(relevantCheckDetails) == 0 {
		logger.Debug(ctx, "no relevant checks run for feature", zap.String(logger.FEATURE, feature.String()))
		return &onbPb.FeatureEligibility{}
	}

	featureEligibilityStatus := onbPb.FeatureEligibility_STATUS_UNKNOWN
	for _, checkDetails := range relevantCheckDetails {
		// break as soon as any of the relevant check is passed
		if checkDetails.GetCheckResult() == screenerPb.CheckResult_CHECK_RESULT_PASSED {
			featureEligibilityStatus = onbPb.FeatureEligibility_STATUS_PASSED
			break
		}
		// update status to failed if one of the checks is failed but none is passed
		if checkDetails.GetCheckResult() == screenerPb.CheckResult_CHECK_RESULT_FAILED {
			featureEligibilityStatus = onbPb.FeatureEligibility_STATUS_FAILED
		}
	}

	return &onbPb.FeatureEligibility{
		Status: featureEligibilityStatus,
	}
}
