package stageproc

import (
	"context"

	"go.uber.org/zap"

	cfg2 "github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/monitoring"

	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	"github.com/epifi/be-common/pkg/logger"
	txn "github.com/epifi/be-common/pkg/storage/v2"

	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/stageproc/datavalidator"
)

type NROnboardingCrossDataValidationStage struct {
	conf          *genconf.Config
	dataValidator datavalidator.DataValidator
	onbDao        dao.OnboardingDao
}

func NewNROnboardingCrossDataValidationStage(conf *genconf.Config, dataValidator datavalidator.DataValidator, onbDao dao.OnboardingDao) *NROnboardingCrossDataValidationStage {
	return &NROnboardingCrossDataValidationStage{
		conf:          conf,
		dataValidator: dataValidator,
		onbDao:        onbDao,
	}
}

func (s *NROnboardingCrossDataValidationStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	if isNonProdEnv() {
		return s.mockProcessor(ctx)
	}

	var (
		onbDetails = req.GetOnb()
		stage      = onbPb.OnboardingStage_NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION
		err        error
	)
	if getStageStatus(onbDetails, stage) == onbPb.OnboardingState_RESET {
		onbDetails, err = s.updateCrossValidationResult(ctx, nil, onbDetails.GetOnboardingId())
		if err != nil {
			return nil, err
		}
	}
	if getStageStatus(onbDetails, stage) == onbPb.OnboardingState_FAILURE {
		return &StageProcessorResponse{
			NextAction: deeplink.NewErrorFullScreen(ctx, error2.DataCrossValidationFailed),
		}, nil
	}
	// handle cases of manual review
	if onbDetails.GetStageMetadata().GetNonResidentCrossValidationResult().GetCrossValidationManualReviewInfo() != nil &&
		onbDetails.GetStageMetadata().GetNonResidentCrossValidationResult().GetVerdict() == onbPb.CrossValidationVerdict_CROSS_VALIDATION_VERDICT_SUCCESS {
		logger.Info(ctx, "cross validation manual review successful")
		return nil, NoActionError
	}
	if onbDetails.GetStageMetadata().GetNonResidentCrossValidationResult().GetCrossValidationManualReviewInfo() != nil &&
		onbDetails.GetStageMetadata().GetNonResidentCrossValidationResult().GetVerdict() == onbPb.CrossValidationVerdict_CROSS_VALIDATION_VERDICT_FAIL {
		logger.Info(ctx, "cross validation manual review failed")
		_, _ = updateStatus(ctx, s.onbDao, onbDetails.GetOnboardingId(), stage, onbPb.OnboardingState_FAILURE)
		return &StageProcessorResponse{
			NextAction: deeplink.NewErrorFullScreen(ctx, error2.CrossValidationManualReviewScreen),
		}, nil
	}
	if getStageStatus(onbDetails, stage) == onbPb.OnboardingState_MANUAL_INTERVENTION {
		return &StageProcessorResponse{
			NextAction: deeplink.NewErrorFullScreen(ctx, error2.CrossValidationManualReviewScreen),
		}, nil
	}

	res, err := s.dataValidator.ValidateData(ctx, &datavalidator.ValidateDataRequest{
		OnbDetails: onbDetails,
	})
	if err != nil {
		return nil, err
	}
	onbDetails, err = s.updateCrossValidationResult(ctx, res.GetCrossValidationResult(), onbDetails.GetOnboardingId())
	if err != nil {
		return nil, err
	}
	if res.GetCrossValidationResult().GetVerdict() == onbPb.CrossValidationVerdict_CROSS_VALIDATION_VERDICT_FAIL {
		monitoring.KibanaInfoServiceMonitor(ctx, cfg2.ONBOARDING_SERVICE, "Cross Validation in manual review")
		_, _ = updateStatus(ctx, s.onbDao, onbDetails.GetOnboardingId(), stage, onbPb.OnboardingState_MANUAL_INTERVENTION)
		return &StageProcessorResponse{
			// todo (NRI) : deeplink based on failure reason
			NextAction: deeplink.NewErrorFullScreen(ctx, error2.DataCrossValidationFailed),
			AnalyticsProps: map[string]string{
				"failure_reason":     res.GetCrossValidationResult().GetFailureReason().String(),
				"check":              res.GetCrossValidationResult().GetCheck().String(),
				"first_data_source":  res.GetCrossValidationResult().GetDataSource_1().String(),
				"second_data_source": res.GetCrossValidationResult().GetDataSource_2().String(),
			},
		}, nil
	}

	return nil, NoActionError
}

func (s *NROnboardingCrossDataValidationStage) updateCrossValidationResult(ctx context.Context, crossValidationResult *onbPb.CrossValidationResult, onbId string) (*onbPb.OnboardingDetails, error) {
	var (
		onbDetails *onbPb.OnboardingDetails
		err        error
	)
	if txnErr := txn.RunCRDBIdempotentTxn(ctx, transactionMaxRetry, func(ctx context.Context) error {
		onbDetails, err = s.onbDao.GetOnboardingDetailsById(ctx, onbId)
		if err != nil {
			return err
		}

		if onbDetails.GetStageMetadata() == nil {
			onbDetails.StageMetadata = &onbPb.StageMetadata{}
		}
		onbDetails.GetStageMetadata().NonResidentCrossValidationResult = crossValidationResult
		if err = s.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, onbDetails); err != nil {
			return err
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "unable to update onboarding details metadata", zap.Error(txnErr))
		return nil, txnErr
	}
	return onbDetails, nil
}

func (s *NROnboardingCrossDataValidationStage) mockProcessor(ctx context.Context) (*StageProcessorResponse, error) {
	logger.Info(ctx, "skipping nr onboarding cross data validation stage in non-prod environment")
	return nil, NoActionError
}
