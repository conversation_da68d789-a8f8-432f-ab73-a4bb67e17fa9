package stageproc

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	pkgMocks "github.com/epifi/be-common/pkg/mock"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/ekyc"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	userHelper "github.com/epifi/gamma/user/onboarding/helper/user"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

func TestKycNameDobStage_StageProcessor(t *testing.T) {
	onbFixture := &onbPb.OnboardingDetails{
		ActorId:       "actor-id",
		OnboardingId:  "onb-id",
		UserId:        "user-id",
		Vendor:        constant.PartnerBank,
		StageMetadata: &onbPb.StageMetadata{},
	}
	abortedStatus := rpc.NewStatus(uint32(codes.Aborted), "suspended customer id", "")
	newOnbFixture := &onbPb.OnboardingDetails{
		ActorId:      "actor-id",
		OnboardingId: "onb-id",
		UserId:       "user-id",
		Vendor:       constant.PartnerBank,
		StageMetadata: &onbPb.StageMetadata{
			EkycNameDobValidation: &onbPb.EKYCNameDOBValidationData{
				FailureDesc: fmt.Sprintf("%v: %v", abortedStatus.Code, abortedStatus.ShortMessage),
				RawResponse: "raw-response",
			},
		},
	}

	kycReq := &kyc.GetKYCRecordRequest{
		ActorId: onbFixture.ActorId,
	}

	kycRes := &kyc.GetKYCRecordResponse{
		Status:  rpc.StatusOk(),
		KycType: kyc.KycType_EKYC,
		KycRecord: &kyc.KYCRecord{
			UidReferenceKey: "uid-ref-key",
			Name: &commontypes.Name{
				FirstName: "Harry",
				LastName:  "Potter",
			},
			Dob: &date.Date{
				Year:  1996,
				Month: 2,
				Day:   21,
			},
		},
	}
	kycResBKYC := &kyc.GetKYCRecordResponse{
		Status:  rpc.StatusOk(),
		KycType: kyc.KycType_BKYC,
		KycRecord: &kyc.KYCRecord{
			UidReferenceKey: "uid-ref-key",
			Name: &commontypes.Name{
				FirstName: "Harry",
				LastName:  "Potter",
			},
			Dob: &date.Date{
				Year:  1996,
				Month: 2,
				Day:   21,
			},
		},
	}

	vgReq := &ekyc.NameDobValidationForEkycRequest{
		Header:          &commonvgpb.RequestHeader{Vendor: constant.PartnerBank},
		UidReferenceKey: kycRes.KycRecord.UidReferenceKey,
		CustomerName:    kycRes.KycRecord.Name,
		Dob:             kycRes.KycRecord.Dob,
	}

	userFix := &user.User{
		Profile: &user.Profile{
			DateOfBirth: &date.Date{
				Year:  1996,
				Month: 2,
				Day:   21,
			},
		},
	}

	type args struct {
		req *StageProcessorRequest
	}

	tests := []struct {
		name     string
		args     args
		want     *StageProcessorResponse
		wantErr  error
		mockFunc func(mocks *Clients)
	}{
		{
			name: "happy case",
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want:    &StageProcessorResponse{},
			wantErr: NoActionError,
			mockFunc: func(mocks *Clients) {
				mocks.KycClient.EXPECT().GetKYCRecord(gomock.Any(), kycReq).Return(kycRes, nil)
				mocks.userProc.EXPECT().GetUserByActorId(gomock.Any(), onbFixture.GetActorId()).Return(userFix, nil)
				mocks.vgEkycClient.EXPECT().NameDobValidationForEkyc(gomock.Any(), vgReq).Return(&ekyc.NameDobValidationForEkycResponse{
					Status:      rpc.StatusOk(),
					RawResponse: "ok-raw-response",
				}, nil)
				mocks.OnbDao.EXPECT().GetOnboardingDetailsById(gomock.Any(), onbFixture.GetOnboardingId()).Return(onbFixture, nil).Times(1)
				mocks.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, &onbPb.OnboardingDetails{
					OnboardingId: newOnbFixture.GetOnboardingId(),
					StageMetadata: &onbPb.StageMetadata{
						EkycNameDobValidation: &onbPb.EKYCNameDOBValidationData{
							RawResponse: "ok-raw-response",
						},
					},
				}).Return(nil).Times(1)
			},
		},
		{
			name: "happy case bkyc",
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want:    &StageProcessorResponse{},
			wantErr: NoActionError,
			mockFunc: func(mocks *Clients) {
				mocks.KycClient.EXPECT().GetKYCRecord(gomock.Any(), kycReq).Return(kycResBKYC, nil)
				mocks.userProc.EXPECT().GetUserByActorId(gomock.Any(), onbFixture.GetActorId()).Return(userFix, nil)
				mocks.vgEkycClient.EXPECT().NameDobValidationForEkyc(gomock.Any(), vgReq).Return(&ekyc.NameDobValidationForEkycResponse{
					Status:      rpc.StatusOk(),
					RawResponse: "ok-raw-response",
				}, nil)
				mocks.OnbDao.EXPECT().GetOnboardingDetailsById(gomock.Any(), onbFixture.GetOnboardingId()).Return(onbFixture, nil).Times(1)
				mocks.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, &onbPb.OnboardingDetails{
					OnboardingId: newOnbFixture.GetOnboardingId(),
					StageMetadata: &onbPb.StageMetadata{
						EkycNameDobValidation: &onbPb.EKYCNameDOBValidationData{
							RawResponse: "ok-raw-response",
						},
					},
				}).Return(nil).Times(1)
			},
		},
		{
			name: "KYC type is not EKYC",
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want:    &StageProcessorResponse{},
			wantErr: SkipStageError,
			mockFunc: func(mocks *Clients) {
				mocks.KycClient.EXPECT().GetKYCRecord(gomock.Any(), kycReq).Return(&kyc.GetKYCRecordResponse{
					Status:  rpc.StatusOk(),
					KycType: kyc.KycType_CKYC,
				}, nil)
			},
		},
		{
			name: "customer creation success",
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:      "actor-id",
						OnboardingId: "onb-id",
						UserId:       "user-id",
						StageDetails: &onbPb.StageDetails{
							StageMapping: map[string]*onbPb.StageInfo{
								onbPb.OnboardingStage_CUSTOMER_CREATION.String(): {
									State: onbPb.OnboardingState_SUCCESS,
								},
							},
						},
					},
				},
			},
			want:    &StageProcessorResponse{},
			wantErr: SkipStageError,
		},
		{
			name: "manual intervention state",
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:      "actor-id",
						OnboardingId: "onb-id",
						UserId:       "user-id",
						StageDetails: &onbPb.StageDetails{
							StageMapping: map[string]*onbPb.StageInfo{
								onbPb.OnboardingStage_KYC_NAME_DOB_VALIDATION.String(): {
									State: onbPb.OnboardingState_MANUAL_INTERVENTION,
								},
							},
						},
					},
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewErrorFullScreen(context.Background(), error2.EKYCNameDobValidationError),
			},
			wantErr: nil,
		},
		{
			name: "aborted status",
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want: &StageProcessorResponse{
				NextAction: &dlPb.Deeplink{
					Screen: dlPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
					ScreenOptions: &dlPb.Deeplink_GetNextOnboardingActionScreenOptions{
						GetNextOnboardingActionScreenOptions: &dlPb.GetNextOnboardingActionScreenOptions{
							ImageUrl: "https://epifi-icons.pointz.in/onboarding/lite_dino.png",
							Title:    "Taking you to home!",
							Feature:  onbPb.Feature_FEATURE_FI_LITE.String(),
						},
					},
				},
			},
			wantErr: nil,
			mockFunc: func(mocks *Clients) {
				mocks.KycClient.EXPECT().GetKYCRecord(gomock.Any(), kycReq).Return(kycRes, nil)
				mocks.userProc.EXPECT().GetUserByActorId(gomock.Any(), onbFixture.GetActorId()).Return(userFix, nil)
				mocks.vgEkycClient.EXPECT().NameDobValidationForEkyc(gomock.Any(), vgReq).Return(&ekyc.NameDobValidationForEkycResponse{
					Status:      abortedStatus,
					RawResponse: "raw-response",
				}, nil)
				mocks.OnbDao.EXPECT().GetOnboardingDetailsById(gomock.Any(), onbFixture.GetOnboardingId()).Return(onbFixture, nil).Times(1)
				c1 := mocks.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, &onbPb.OnboardingDetails{
					OnboardingId:  newOnbFixture.OnboardingId,
					StageMetadata: newOnbFixture.StageMetadata,
				}).Return(nil).Times(1)
				mocks.OnbDao.EXPECT().UpdateStatus(gomock.Any(), onbFixture.GetOnboardingId(), onbPb.OnboardingStage_KYC_NAME_DOB_VALIDATION, onbPb.OnboardingState_MANUAL_INTERVENTION).Return(newOnbFixture, nil).Times(1)
				mocks.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{
					onbPb.OnboardingDetailsFieldMask_FEATURE,
					onbPb.OnboardingDetailsFieldMask_FI_LITE_DETAILS,
				}, pkgMocks.NewProtoMatcher(&onbPb.OnboardingDetails{
					ActorId:       newOnbFixture.ActorId,
					UserId:        newOnbFixture.UserId,
					OnboardingId:  newOnbFixture.OnboardingId,
					StageMetadata: newOnbFixture.StageMetadata,
					Feature:       onbPb.Feature_FEATURE_FI_LITE,
					Vendor:        newOnbFixture.GetVendor(),
					FiLiteDetails: &onbPb.FiLiteDetails{
						IsEnabled:    commontypes.BooleanEnum_TRUE,
						FiLiteSource: onbPb.FiLiteSource_FI_LITE_SOURCE_EKYC_NAME_DOB_VALIDATION,
					},
				}, protocmp.IgnoreFields(&onbPb.FiLiteDetails{}, "accessibility_enabled_at"))).Return(nil).After(c1)
				// mocks.userProc.EXPECT().GetUserByActorId(gomock.Any(), gomock.Any()).Return(&user.User{
				//	Profile: &user.Profile{},
				// }, nil)
				// mocks.ugClient.EXPECT().GetGroupsMappedToEmail(gomock.Any(), gomock.Any()).Return(&group.GetGroupsMappedToEmailResponse{
				//	Status: rpc.StatusOk(),
				//	Groups: []group.UserGroup{
				//		group.UserGroup_INTERNAL,
				//	},
				// }, nil)
				mocks.eventLogger.EXPECT().LogFiLiteUserConverted(gomock.Any(), onbFixture.GetActorId(), gomock.Any())
			},
		},
		{
			name: "VG rpc error case",
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want:    &StageProcessorResponse{},
			wantErr: fmt.Errorf("error in kyc name dob validation: %w", fmt.Errorf("random error")),
			mockFunc: func(mocks *Clients) {
				mocks.KycClient.EXPECT().GetKYCRecord(gomock.Any(), kycReq).Return(kycRes, nil)
				mocks.userProc.EXPECT().GetUserByActorId(gomock.Any(), onbFixture.GetActorId()).Return(userFix, nil)
				mocks.vgEkycClient.EXPECT().NameDobValidationForEkyc(gomock.Any(), vgReq).Return(nil, fmt.Errorf("random error"))
			},
		},
		{
			name: "VG rpc default case",
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want:    &StageProcessorResponse{},
			wantErr: fmt.Errorf("invalid status in kyc name dob validation: %v", rpc.StatusInternal().String()),
			mockFunc: func(mocks *Clients) {
				mocks.KycClient.EXPECT().GetKYCRecord(gomock.Any(), kycReq).Return(kycRes, nil)
				mocks.userProc.EXPECT().GetUserByActorId(gomock.Any(), onbFixture.GetActorId()).Return(userFix, nil)
				mocks.vgEkycClient.EXPECT().NameDobValidationForEkyc(gomock.Any(), vgReq).Return(&ekyc.NameDobValidationForEkycResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			clients := setupServiceWithMocks(t)
			if tt.mockFunc != nil {
				tt.mockFunc(clients)
				// to handle for fi lite
				clients.userProc.EXPECT().IsNonResidentUser(gomock.Any(), gomock.Any()).Return(&userHelper.IsNonResidentUserResponse{
					IsNonResidentUser: commontypes.BooleanEnum_FALSE,
				}, nil).AnyTimes()
			}
			s := &KYCNameDOBValidation{
				kycClient:       clients.KycClient,
				vgEkycClient:    clients.vgEkycClient,
				onboardingDao:   clients.OnbDao,
				conf:            ts.GenConf.Onboarding(),
				userProc:        clients.userProc,
				userGroupClient: clients.ugClient,
				eventLogger:     clients.eventLogger,
			}
			got, err := s.StageProcessor(context.Background(), tt.args.req)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Unexpected error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			if diff := cmp.Diff(got.GetNextAction(), tt.want.GetNextAction(), protocmp.Transform()); diff != "" {
				t.Errorf("StageProcessor value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
