package stageproc

import (
	"context"
	"fmt"
	"strconv"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/feature/release"

	consentPb "github.com/epifi/gamma/api/consent"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"

	"go.uber.org/zap"
)

type SaIntroConsentStage struct {
	onbDao           dao.OnboardingDao
	consentClient    consentPb.ConsentClient
	conf             *genconf.OnboardingConfig
	eventLogger      userEvents.EventLogger
	dlHelper         helper.DeeplinkHelper
	releaseEvaluator release.IEvaluator
}

func NewSaIntroConsentStage(onbDao dao.OnboardingDao, consentClient consentPb.ConsentClient, conf *genconf.Config,
	eventLogger userEvents.EventLogger, dlHelper helper.DeeplinkHelper, releaseEvaluator release.IEvaluator) *SaIntroConsentStage {
	return &SaIntroConsentStage{
		onbDao:           onbDao,
		consentClient:    consentClient,
		conf:             conf.Onboarding(),
		eventLogger:      eventLogger,
		dlHelper:         dlHelper,
		releaseEvaluator: releaseEvaluator,
	}
}

func (s *SaIntroConsentStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	if !apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.conf.Flags().EnableSavingsIntroScreen()) {
		return s.handleSaConsentWhenFlagOff(ctx, req)
	}
	consentRes, err := s.consentClient.CheckConsentRequirement(ctx, &consentPb.CheckConsentRequirementRequest{
		ActorId: req.GetOnb().GetActorId(),
		ConsentTypes: []consentPb.ConsentType{
			consentPb.ConsentType_FED_TNC,
		},
		Owner: common.Owner_OWNER_EPIFI_TECH,
	})
	if err != nil {
		logger.Error(ctx, "failed to fetch consent requirement from consent service", zap.Error(err))
		return nil, err
	}
	if consentRes.GetIsConsentRequired() {

		isNROConsentEnabled := apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.conf.EnableTriggerNROAccountCreation())
		if req.GetOnb().GetFeature().IsNonResidentUserOnboarding() {
			s.eventLogger.LogLoadedSAIntroScreenV1Server(ctx, req.GetOnb().GetActorId())
			return &StageProcessorResponse{
				NextAction: getSaIntroScreenForNr(isNROConsentEnabled),
				AnalyticsProps: map[string]string{
					"nro_consent_enabled": strconv.FormatBool(isNROConsentEnabled),
				},
			}, nil
		}

		onbSelectedIntent := req.GetOnb().GetStageMetadata().GetIntentSelectionMetadata().GetSelection()
		switch onbSelectedIntent {
		case onbPb.OnboardingIntent_ONBOARDING_INTENT_DEBIT_CARD:
			s.eventLogger.LogLoadedDCIntroScreenServer(ctx, req.GetOnb().GetActorId())
			return &StageProcessorResponse{NextAction: &dlPb.Deeplink{Screen: dlPb.Screen_DC_ONBOARDING_INTRO}}, nil

		default:
			intentSelectionDL, errDL := s.dlHelper.GetIntentSelectionDL(ctx, req.GetOnb().GetActorId(), onbPb.IntentSelectionEntryPoint_INTENT_SELECTION_ENTRY_POINT_SA_INTRO_SCREEN.String())
			if errDL != nil {
				logger.Error(ctx, "failure while calling GetIntentSelectionDL", zap.Error(errDL))
				return nil, errDL
			}
			s.eventLogger.LogLoadedSAIntroScreenV1Server(ctx, req.GetOnb().GetActorId())
			return &StageProcessorResponse{
				NextAction: getSaIntroScreen(ctx, intentSelectionDL, s.releaseEvaluator, req.GetOnb().GetActorId()),
			}, nil
		}
	}
	return nil, NoActionError
}

// if feature is not enabled, check if all required consents have been collected from TNC stage
func (s *SaIntroConsentStage) handleSaConsentWhenFlagOff(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	consentRes, err := s.consentClient.CheckConsentRequirement(ctx, &consentPb.CheckConsentRequirementRequest{
		ActorId: req.GetOnb().GetActorId(),
		ConsentTypes: []consentPb.ConsentType{
			consentPb.ConsentType_FED_TNC,
			consentPb.ConsentType_FI_PRIVACY_POLICY,
			consentPb.ConsentType_FI_TNC,
			consentPb.ConsentType_FI_WEALTH_TNC,
		},
		Owner: common.Owner_OWNER_EPIFI_TECH,
	})
	if err != nil {
		logger.Error(ctx, "failed to fetch consent requirement from consent service", zap.Error(err))
		return nil, err
	}
	if consentRes.GetIsConsentRequired() {
		// all consents not present, hence reset TNC stage to collect them
		initialStage := getStageStatus(req.GetOnb(), onbPb.OnboardingStage_TNC_CONSENT)
		daoResp, daoErr := updateStatus(ctx, s.onbDao, req.GetOnb().GetOnboardingId(), onbPb.OnboardingStage_TNC_CONSENT, onbPb.OnboardingState_RESET)
		if daoErr != nil {
			return nil, err
		}
		finalStage := getStageStatus(daoResp, onbPb.OnboardingStage_TNC_CONSENT)
		logger.Info(ctx, fmt.Sprintf("force updated onboarding stage from %v to %v", initialStage, finalStage),
			zap.String(logger.ACTOR_ID_V2, req.GetOnb().GetActorId()), logOnb(req.GetOnb().GetOnboardingId()), zap.String(logger.ONBOARDING_STAGE, onbPb.OnboardingStage_SAVINGS_INTRO_CONSENT.String()),
		)
		return &StageProcessorResponse{
			NextAction: deeplink.NewActionToGetNextAction(),
		}, nil
	}
	return nil, NoActionError
}
