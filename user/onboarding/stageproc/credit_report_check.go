package stageproc

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	creditReportPb2 "github.com/epifi/gamma/api/creditreportv2"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

const (
	creditScoreCutoff = 600
)

type CreditReportCheckStage struct {
	onbDao               dao.OnboardingDao
	userClient           userPb.UsersClient
	creditReportV2Client creditReportPb2.CreditReportManagerClient
}

func NewCreditReportCheckStage(onbDao dao.OnboardingDao,
	userClient userPb.UsersClient, creditReportV2Client creditReportPb2.CreditReportManagerClient) *CreditReportCheckStage {
	return &CreditReportCheckStage{
		onbDao:               onbDao,
		userClient:           userClient,
		creditReportV2Client: creditReportV2Client,
	}
}

func (c *CreditReportCheckStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb     = req.GetOnb()
		actorId = req.GetOnb().GetActorId()
		stage   = onbPb.OnboardingStage_CREDIT_REPORT_CHECK
	)

	resp, errResp := c.userClient.GetB2BSalaryProgramVerificationStatus(ctx, &userPb.GetB2BSalaryProgramVerificationStatusRequest{
		Identifier: &userPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil {
		logger.Error(ctx, "error in fetching B2B verification status", zap.Error(err))
		return nil, err
	}

	if resp.GetIsVerified() {
		logger.Info(ctx, "user is b2b salary program verified, skipping credit score check", logOnb(req.GetOnb().GetOnboardingId()))
		return nil, SkipStageError
	}

	res, err := c.creditReportV2Client.GetCreditReport(ctx, &creditReportPb2.GetCreditReportRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(res, err); grpcErr != nil && !res.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in GetCreditReport", zap.Error(err), logOnb(onb.GetOnboardingId()))
		return nil, grpcErr
	}

	if res.GetStatus().IsRecordNotFound() || res.GetVerificationStatus() == creditReportPb2.VerificationStatus_VERIFICATION_STATUS_FAILED_REPORT_NOT_FOUND {
		logger.Info(ctx, "users credit report v2 is not found, skipping credit score check", logOnb(onb.GetOnboardingId()))
		return nil, SkipStageError
	}

	if res.GetCreditReportData().GetCreditScore() > 0 && res.GetCreditReportData().GetCreditScore() < creditScoreCutoff {
		logger.WarnWithCtx(ctx, fmt.Sprintf("credit score less than %v, failing check", creditScoreCutoff))
		_, _ = updateStatus(ctx, c.onbDao, onb.GetOnboardingId(), stage, onbPb.OnboardingState_FAILURE)
		return &StageProcessorResponse{
			NextAction: deeplink.NewErrorFullScreen(ctx, error2.ScreenerBlockOpts),
		}, nil
	}

	return nil, NoActionError
}
