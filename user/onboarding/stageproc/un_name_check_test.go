package stageproc

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	kycPb "github.com/epifi/gamma/api/kyc"
	kycMock "github.com/epifi/gamma/api/kyc/mocks"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	daoMock "github.com/epifi/gamma/user/onboarding/dao/mocks"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/helper/mocks"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

func TestUNNameCheckStage_StageProcessor(t *testing.T) {
	t.<PERSON>()
	var (
		onbDet = &onbPb.OnboardingDetails{
			ActorId:      "actor-id",
			OnboardingId: "onb-id",
			UserId:       "user-id",
		}
		userFixture = &userPb.User{
			Profile: &userPb.Profile{
				PanName: &commontypes.Name{
					FirstName:  "Alpha",
					MiddleName: "Bravo",
					LastName:   "Charlie",
				},
			},
		}
	)

	type mockStruct struct {
		userProc  *mocks.MockUserProcessor
		kycClient *kycMock.MockKycClient
		onbDao    *daoMock.MockOnboardingDao
	}
	tests := map[string]struct {
		req      *StageProcessorRequest
		want     *StageProcessorResponse
		wantErr  error
		mockFunc func(*mockStruct)
	}{
		"skipping since customer creation is successful": {
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-id",
					OnboardingId: "onb-id",
					UserId:       "user-id",
					StageDetails: &onbPb.StageDetails{
						StageMapping: map[string]*onbPb.StageInfo{onbPb.OnboardingStage_CUSTOMER_CREATION.String(): {
							State: onbPb.OnboardingState_SUCCESS,
						}},
					},
				},
			},
			wantErr: SkipStageError,
		},
		"happy case": {
			req: &StageProcessorRequest{
				Onb: onbDet,
			},
			wantErr: NoActionError,
			mockFunc: func(m *mockStruct) {
				m.userProc.EXPECT().GetUserByUserId(gomock.Any(), onbDet.UserId).Return(userFixture, nil)
				m.kycClient.EXPECT().ValidateCustomerName(gomock.Any(), &kycPb.ValidateCustomerNameRequest{
					Name: userFixture.Profile.PanName,
				}).Return(&kycPb.ValidateCustomerNameResponse{
					Status:            rpc.StatusOk(),
					IsValid:           true,
					UNNameCheckStatus: kycPb.UNNameCheckStatus_UN_NAME_CHECK_STATUS_PASSED,
				}, nil)
				m.onbDao.EXPECT().GetOnboardingDetailsById(gomock.Any(), onbDet.OnboardingId).Return(onbDet, nil)
				m.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{
					onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
				}, &onbPb.OnboardingDetails{
					OnboardingId: onbDet.OnboardingId,
					StageMetadata: &onbPb.StageMetadata{
						UNNameCheckStatusV2: kycPb.UNNameCheckStatus_UN_NAME_CHECK_STATUS_PASSED,
					},
				}).Return(nil)
			},
		},
		"failure case": {
			req: &StageProcessorRequest{
				Onb: onbDet,
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewErrorFullScreen(context.Background(), error2.OnbErrUNNameCheckBlocked),
			},
			mockFunc: func(m *mockStruct) {
				m.userProc.EXPECT().GetUserByUserId(gomock.Any(), onbDet.UserId).Return(userFixture, nil)
				m.kycClient.EXPECT().ValidateCustomerName(gomock.Any(), &kycPb.ValidateCustomerNameRequest{
					Name: userFixture.Profile.PanName,
				}).Return(&kycPb.ValidateCustomerNameResponse{
					Status:            rpc.StatusOk(),
					IsValid:           false,
					UNNameCheckStatus: kycPb.UNNameCheckStatus_UN_NAME_CHECK_STATUS_FAILED,
				}, nil)
				m.onbDao.EXPECT().GetOnboardingDetailsById(gomock.Any(), onbDet.OnboardingId).Return(onbDet, nil)
				m.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{
					onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
				}, &onbPb.OnboardingDetails{
					OnboardingId: onbDet.OnboardingId,
					StageMetadata: &onbPb.StageMetadata{
						UNNameCheckStatusV2: kycPb.UNNameCheckStatus_UN_NAME_CHECK_STATUS_FAILED,
					},
				}).Return(nil)
				m.onbDao.EXPECT().UpdateStatus(gomock.Any(), onbDet.OnboardingId, onbPb.OnboardingStage_UN_NAME_CHECK, onbPb.OnboardingState_FAILURE).Return(onbDet, nil)
			},
		},
		"early failure case": {
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId:      "actor-id",
					OnboardingId: "onb-id",
					UserId:       "user-id",
					StageDetails: &onbPb.StageDetails{
						StageMapping: map[string]*onbPb.StageInfo{
							onbPb.OnboardingStage_UN_NAME_CHECK.String(): {
								State: onbPb.OnboardingState_FAILURE,
							},
						},
					},
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewErrorFullScreen(context.Background(), error2.OnbErrUNNameCheckBlocked),
			},
		},
	}
	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			t.Parallel()
			ctr := gomock.NewController(t)
			kycClient := kycMock.NewMockKycClient(ctr)
			onbDao := daoMock.NewMockOnboardingDao(ctr)
			userProc := mocks.NewMockUserProcessor(ctr)

			if tt.mockFunc != nil {
				tt.mockFunc(&mockStruct{
					userProc:  userProc,
					kycClient: kycClient,
					onbDao:    onbDao,
				})
			}

			s := &UNNameCheckStage{
				userProc:  userProc,
				onbDao:    onbDao,
				kycClient: kycClient,
			}

			got, err := s.StageProcessor(context.Background(), tt.req)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Unexpected error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			if diff := cmp.Diff(got.GetNextAction(), tt.want.GetNextAction(), protocmp.Transform()); diff != "" {
				t.Errorf("StageProcessor value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
