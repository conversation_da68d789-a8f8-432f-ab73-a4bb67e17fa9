package stageproc

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	riskPb "github.com/epifi/gamma/api/risk"
	riskEnumPb "github.com/epifi/gamma/api/risk/enums"
	riskScrnrPb "github.com/epifi/gamma/api/risk/screener"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

type RiskScreeningStage struct {
	userClient  userPb.UsersClient
	conf        *genconf.OnboardingConfig
	onbDao      dao.OnboardingDao
	riskClient  riskPb.RiskClient
	eventLogger events.EventLogger
	userProc    helper.UserProcessor
}

func NewRiskScreeningStage(userClient userPb.UsersClient, conf *genconf.OnboardingConfig, onbDao dao.OnboardingDao,
	riskClient riskPb.RiskClient, eventLogger events.EventLogger, userProc helper.UserProcessor) *RiskScreeningStage {
	return &RiskScreeningStage{
		userClient:  userClient,
		conf:        conf,
		onbDao:      onbDao,
		riskClient:  riskClient,
		eventLogger: eventLogger,
		userProc:    userProc,
	}
}

func (r *RiskScreeningStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb              = req.GetOnb()
		actorId          = onb.GetActorId()
		stage            = onbPb.OnboardingStage_RISK_SCREENING
		screenerCriteria = riskEnumPb.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING
		err              error
	)

	isCustomerCreationInProgress, errCheck := r.userProc.IsCustomerCreationInProgress(ctx, actorId)
	if errCheck != nil {
		return nil, errCheck
	}
	if isCustomerCreationInProgress {
		logger.Info(ctx, "skipping risk screening as customer created with the bank")
		return nil, SkipStageError
	}

	if getStageStatus(onb, stage) == onbPb.OnboardingState_RESET {
		if onb.GetStageMetadata().GetRiskScreeningMetadata() != nil {
			onb.GetStageMetadata().GetRiskScreeningMetadata().ClientRequestId = ""
			if err = r.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, onb); err != nil {
				logger.Error(ctx, "failed to stage metadata", zap.Error(err))
				return nil, err
			}
		}
		onb, err = updateStatus(ctx, r.onbDao, onb.GetOnboardingId(), stage, onbPb.OnboardingState_INPROGRESS)
		if err != nil {
			logger.Error(ctx, "error in updating onboarding stage status", zap.Error(err))
			return nil, err
		}
	}

	clientReqId, err := r.createOrGetClientRequestId(ctx, onb)
	if err != nil {
		logger.Error(ctx, "failed to create or get client request id", zap.Error(err))
		return nil, fmt.Errorf("failed to create or get client request id: %w", err)
	}

	switch {
	case onb.GetFeature() == onbPb.Feature_FEATURE_CC:
		screenerCriteria = riskEnumPb.ScreenerCriteria_SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING
	case onb.GetFeature().IsNonResidentUserOnboarding():
		if !r.conf.Flags().EnableRiskCheckForNRUser() {
			logger.Info(ctx, "risk check for nr user is disabled")
			return nil, SkipStageError
		}
		screenerCriteria = riskEnumPb.ScreenerCriteria_SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING
	default:
		screenerCriteria = riskEnumPb.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING
		resp, errResp := r.userClient.GetB2BSalaryProgramVerificationStatus(ctx, &userPb.GetB2BSalaryProgramVerificationStatusRequest{
			Identifier: &userPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{
				ActorId: actorId,
			},
		})
		if err := epifigrpc.RPCError(resp, errResp); err != nil {
			logger.Error(ctx, "error in fetching B2B verification status", zap.Error(err))
			return nil, err
		}
		if resp.GetIsVerified() {
			screenerCriteria = riskEnumPb.ScreenerCriteria_SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING
		}
	}
	attemptStatusResp, errStatus := r.riskClient.GetScreenerAttemptStatus(ctx, &riskPb.GetScreenerAttemptStatusRequest{
		AttemptIdentifier: &riskScrnrPb.AttemptIdentifier{
			Identifier: &riskScrnrPb.AttemptIdentifier_CriteriaClientReqId{
				CriteriaClientReqId: &riskScrnrPb.CriteriaAndClientReqIDIdentifier{
					ScreenerCriteria: screenerCriteria,
					ClientRequestId:  clientReqId,
				},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(attemptStatusResp, errStatus); rpcErr != nil && !rpc.StatusFromError(rpcErr).IsRecordNotFound() {
		logger.Error(ctx, "failure while calling GetScreenerAttemptStatus", zap.Error(rpcErr))
		// If we face error in GetScreenerAttemptStatus, we let the user continue and circle back here later.
		// We would have stuck user alerts triggered if the user is stuck here for a significant period of time.
		// https://monorail.pointz.in/p/fi-app/issues/detail?id=79406
		return nil, NoActionSkipStatusUpdateError
	}

	riskScreenerStatus := attemptStatusResp.GetScreenerStatus()
	riskVerdict := attemptStatusResp.GetVerdict()

	if attemptStatusResp.GetStatus().IsRecordNotFound() {
		scrnrStatus, scrnrVerdict, errScreen := r.screenActor(ctx, actorId, clientReqId, screenerCriteria)
		if errScreen != nil {
			// If we face error in ScreenActor, we let the user continue and circle back here later.
			// We would have stuck user alerts triggered if the user is stuck here for a significant period of time.
			// https://monorail.pointz.in/p/fi-app/issues/detail?id=79406
			return nil, NoActionSkipStatusUpdateError
		}

		riskScreenerStatus = scrnrStatus
		riskVerdict = scrnrVerdict
	}

	return r.getActionFromScreenerStatusAndVerdict(ctx, onb, riskScreenerStatus, riskVerdict)
}

func (r *RiskScreeningStage) createOrGetClientRequestId(ctx context.Context, onb *onbPb.OnboardingDetails) (string, error) {
	if onb.GetStageMetadata().GetRiskScreeningMetadata() == nil {
		onb.StageMetadata.RiskScreeningMetadata = &onbPb.RiskScreeningMetadata{}
	}
	if onb.GetStageMetadata().GetRiskScreeningMetadata().GetClientRequestId() != "" {
		return onb.GetStageMetadata().GetRiskScreeningMetadata().GetClientRequestId(), nil
	}
	newClientReqId := uuid.NewString()
	onb.GetStageMetadata().GetRiskScreeningMetadata().ClientRequestId = newClientReqId
	if err := r.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, onb); err != nil {
		logger.Error(ctx, "failed to update risk screening meta data", zap.Error(err))
		return "", fmt.Errorf("failed to update risk screening meta data: %w", err)
	}
	return newClientReqId, nil
}

func (r *RiskScreeningStage) screenActor(ctx context.Context, actorId, clientReqId string, screenerCriteria riskEnumPb.ScreenerCriteria) (riskScrnrPb.ScreenerStatus, riskScrnrPb.Verdict, error) {
	screenActorResp, errScreen := r.riskClient.ScreenActor(ctx, &riskPb.ScreenActorRequest{
		ActorId:          actorId,
		ScreenerCriteria: screenerCriteria,
		ClientRequestId:  clientReqId,
	})
	if rpcErr := epifigrpc.RPCError(screenActorResp, errScreen); rpcErr != nil {
		logger.Error(ctx, "failure while calling ScreenActor", zap.Error(rpcErr))
		return riskScrnrPb.ScreenerStatus_SCREENER_STATUS_UNSPECIFIED, riskScrnrPb.Verdict_VERDICT_UNSPECIFIED, rpcErr
	}

	return screenActorResp.GetScreenerStatus(), screenActorResp.GetVerdict(), nil
}

// getActionFromScreenerStatusAndVerdict returns the next action based on risk screener status and verdict
func (r *RiskScreeningStage) getActionFromScreenerStatusAndVerdict(ctx context.Context, onb *onbPb.OnboardingDetails,
	riskScreenerStatus riskScrnrPb.ScreenerStatus, riskVerdict riskScrnrPb.Verdict) (*StageProcessorResponse, error) {
	switch riskScreenerStatus {
	case riskScrnrPb.ScreenerStatus_SCREENER_STATUS_IN_PROGRESS:
		return &StageProcessorResponse{
			NextAction: deeplink.NewActionToGetNextAction(),
		}, nil
	case riskScrnrPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW:
		if r.conf.Flags().AllowManualReviewUsers() && getStageStatus(onb, onbPb.OnboardingStage_DEVICE_REGISTRATION) != onbPb.OnboardingState_SUCCESS {
			return nil, NoActionSkipStatusUpdateError
		}
		_, _ = updateStatus(ctx, r.onbDao, onb.GetOnboardingId(), onbPb.OnboardingStage_RISK_SCREENING, onbPb.OnboardingState_MANUAL_INTERVENTION)
		r.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), true, events.BlockReasonRisk)
		return &StageProcessorResponse{
			NextAction: deeplink.NewErrorFullScreen(ctx, error2.OnbErrForcedManualReview),
		}, nil
	case riskScrnrPb.ScreenerStatus_SCREENER_STATUS_PROCESSING_FAILED:
		return &StageProcessorResponse{
			NextAction: deeplink.NewActionToGetNextAction(),
		}, nil
	case riskScrnrPb.ScreenerStatus_SCREENER_STATUS_DONE:
		switch riskVerdict {
		case riskScrnrPb.Verdict_VERDICT_PASS:
			r.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), false, "")
			return nil, NoActionError
		case riskScrnrPb.Verdict_VERDICT_FAIL:
			r.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), true, events.BlockReasonRisk)
			_, _ = updateStatus(ctx, r.onbDao, onb.GetOnboardingId(), onbPb.OnboardingStage_RISK_SCREENING, onbPb.OnboardingState_FAILURE)
			return &StageProcessorResponse{
				NextAction: getRiskBlockedScreen(ctx, onb.GetFeature()),
			}, nil
		default:
			logger.Error(ctx, fmt.Sprintf("unexpected risk screener verdict received: %v", riskVerdict))
			return nil, fmt.Errorf("unexpected risk screener verdict received: %v", riskVerdict)
		}
	default:
		logger.Error(ctx, fmt.Sprintf("unexpected risk screener status received: %v", riskScreenerStatus))
		return nil, fmt.Errorf("unexpected risk screener status received: %v", riskScreenerStatus)
	}
}

func getRiskBlockedScreen(ctx context.Context, feat onbPb.Feature) *deeplinkPb.Deeplink {
	switch feat {
	case onbPb.Feature_FEATURE_CC:
		return deeplink.NewErrorFullScreen(ctx, error2.CcOnbErrRiskScreeningBlocked)
	default:
		return deeplink.NewErrorFullScreen(ctx, error2.OnbErrRiskScreeningBlocked)
	}
}
