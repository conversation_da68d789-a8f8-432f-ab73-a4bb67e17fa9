package stageproc

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app"
	"github.com/epifi/be-common/pkg/logger"

	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	onboardingPkg "github.com/epifi/gamma/pkg/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	onbErr "github.com/epifi/gamma/user/onboarding/pkg/error"
)

// kycStatus fetches KYC Status from KYC Client
func kycStatus(ctx context.Context, kycClient kyc.KycClient, actorId string) (*kyc.CheckKYCStatusResponse, error) {
	// CheckKYCStatus API call also generates new liveness attempt in case of expiry.
	// As this API is called at regular intervals through, there would infinite attempts for stuck users.
	// optimising by not generating new liveness attempt if the call is through consumer
	// Delete after long term fix: https://monorail.pointz.in/p/fi-app/issues/detail?id=7942
	kycStatusRes, err := kycClient.CheckKYCStatus(ctx, &kyc.CheckKYCStatusRequest{
		ActorId:        actorId,
		IgnoreLiveness: IsConsumerFlow(ctx),
	})
	if err = epifigrpc.RPCError(kycStatusRes, err); err != nil {
		if rpc.StatusFromError(err).IsRecordNotFound() {
			logger.Debug(ctx, "Onb kyc record not found")
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "Onb kyc error in check kyc status", zap.Error(err))
		return nil, err
	}
	return kycStatusRes, nil
}

func tryInitCKYC(ctx context.Context, kycClient kyc.KycClient, actorId string, userProc helper.UserProcessor) error {
	aUser, err := userProc.GetUserByActorId(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in init ckyc from kyc stage", zap.Error(err))
		return err
	}
	if !DobPanExists(aUser.GetProfile().GetPAN(), aUser.GetProfile().GetDateOfBirth()) {
		return ErrDOBPANNotFound
	}

	resInitCKYC, err := kycClient.InitiateCKYC(ctx, &kyc.InitiateCKYCRequest{
		ActorId: actorId,
		IdProof: &kyc.IdProof{
			Type:    kyc.IdProofType_PAN,
			IdValue: aUser.GetProfile().GetPAN(),
		},
		Dob:         aUser.GetProfile().GetDateOfBirth(),
		PhoneNumber: aUser.GetProfile().GetPhoneNumber(),
		NameOnPan:   aUser.GetProfile().GetPanName(),
	})
	if err = epifigrpc.RPCError(resInitCKYC, err); err != nil {
		logger.Error(ctx, "error in initiate ckyc", zap.Error(err))
		return err
	}
	return nil
}

func isPerformVKYCForUser(ctx context.Context, vkycConf *genconf.VKYC, vkycOption onbPb.VKYCOption) bool {
	if vkycConf == nil {
		return false
	}
	vkycConfOption := vkycConf.Option().Get(vkycOption.String())
	return app.IsFeatureEnabledFromCtx(ctx, &app.FeatureConfig{
		UnsupportedPlatforms:    []commontypes.Platform{commontypes.Platform_WEB},
		MinAndroidVersion:       uint32(vkycConfOption.MinAndroidVersion()),
		MinIOSVersion:           uint32(vkycConfOption.MinIOSVersion()),
		FallbackToEnableFeature: true,
	})
}

// updateKYCStageState updates the state for a given stage in a go routine
func updateKYCStageState(ctx context.Context, onb *onbPb.OnboardingDetails, stage onbPb.OnboardingStage,
	stateToUpdate onbPb.OnboardingState, onbDao dao.OnboardingDao) error {
	// TODO(aditya): do in a goroutine
	onbId := onb.GetOnboardingId()
	currentState := getStageStatus(onb, stage)
	if currentState == stateToUpdate {
		return nil
	}
	if _, err := onbDao.UpdateStatus(ctx, onbId, stage, stateToUpdate); err != nil {
		logger.Error(ctx, fmt.Sprintf("error while updating onboarding stage %s state from %s to %s",
			stage.String(), currentState.String(), stateToUpdate.String()), logOnb(onbId), zap.Error(err))
		return err
	}
	logger.Info(ctx, fmt.Sprintf("updated onboarding stage %s status successfully from %s to %s state",
		stage.String(), currentState.String(), stateToUpdate.String()), logOnb(onbId))
	return nil
}

func processDOBMismatchFailures(ctx context.Context, kycDetails *kyc.CheckKYCStatusResponse) *dlPb.Deeplink {
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=32099
	if kycDetails.GetKycType() == kyc.KycType_CKYC {
		return nil
	}

	isDOBMismatch := kycDetails.GetFailureReason() == kyc.FailureType_DOB_MISMATCH
	if !isDOBMismatch {
		return nil
	}

	return deeplink.NewErrorFullScreen(ctx, &onbErr.ErrorScreenOpts{
		Title:          constant.KYCDOBMismatchTitle,
		Subtitle:       constant.KYCDOBMismatchSubtitle,
		HeaderImageURL: constant.GenericBlockingIcon,
	})
}

// updateKYCFailureReason updates the error reason due to which KYC failed. We will skip error handling here as this is not a blocking step during onboarding.
func updateKYCFailureReason(ctx context.Context, onb *onbPb.OnboardingDetails, failureType kyc.FailureType, onbDao dao.OnboardingDao, kycDetails *kyc.CheckKYCStatusResponse) {
	goroutine.Run(ctx, 30*time.Second, func(ctx context.Context) {
		switch {
		case onb.GetStageMetadata() == nil:
			onb.StageMetadata = &onbPb.StageMetadata{
				KycMetadata: &onbPb.KYCMetadata{
					FailureType: failureType,
				},
			}
		case onb.GetStageMetadata().GetKycMetadata() == nil:
			onb.StageMetadata.KycMetadata = &onbPb.KYCMetadata{
				FailureType: failureType,
			}
		default:
			onb.StageMetadata.KycMetadata.FailureType = failureType
		}
		if kycDetails.GetKycType() == kyc.KycType_CKYC {
			onb.StageMetadata.KycMetadata.CkycFailureType = failureType
		}
		err := onbDao.UpdateOnboardingDetailsByColumns(context.Background(), []onbPb.OnboardingDetailsFieldMask{
			onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
		}, onb)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("error while updating kyc stage metadata failure type %s",
				failureType.String()), logOnb(onb.GetOnboardingId()), zap.Error(err))
		}
		logger.Info(ctx, "updated kyc failure type successfully",
			zap.String("failureType", failureType.String()), logOnb(onb.GetOnboardingId()))
	})
}

func mapKYCCheckStatusErrorToAction(_ context.Context, err error) *dlPb.Deeplink {
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return onboardingPkg.GetPanDobCollectionScreen()

	default:
		return nil
	}
}

func validateParentNamesAsync(ctx context.Context, userProc helper.UserProcessor, kycClient kyc.KycClient, actorId string) {
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		user, _ := userProc.GetUserByActorId(ctx, actorId)
		if user.GetProfile().GetFatherName() == nil || user.GetProfile().GetMotherName() == nil {
			return
		}

		_, _ = kycClient.ValidateUserDetails(ctx, &kyc.ValidateUserDetailsRequest{
			ActorId:    actorId,
			FatherName: user.GetProfile().GetFatherName(),
			MotherName: user.GetProfile().GetMotherName(),
		})
	})
}
