package stageproc

import (
	"context"
	"testing"

	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/mohae/deepcopy"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	riskPb "github.com/epifi/gamma/api/risk"
	riskEnumPb "github.com/epifi/gamma/api/risk/enums"
	riskScrnrPb "github.com/epifi/gamma/api/risk/screener"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

func TestRiskScreeningStage_StageProcessor(t *testing.T) {
	t.Skip() // TODO(sai): fix this test https://epifi.slack.com/archives/C0101A42ZFW/p1706080432768549?thread_ts=**********.123129&cid=C0101A42ZFW
	t.Parallel()
	var (
		screenerStatusReq = func(clientReqId string) *riskPb.GetScreenerAttemptStatusRequest {
			return &riskPb.GetScreenerAttemptStatusRequest{
				AttemptIdentifier: &riskScrnrPb.AttemptIdentifier{
					Identifier: &riskScrnrPb.AttemptIdentifier_CriteriaClientReqId{
						CriteriaClientReqId: &riskScrnrPb.CriteriaAndClientReqIDIdentifier{
							ScreenerCriteria: riskEnumPb.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
							ClientRequestId:  clientReqId,
						},
					},
				},
			}
		}
	)

	// nolint: unparam
	B2BVerificationCall := func(isB2BUser bool, m *Clients) *gomock.Call {
		return m.userClient.EXPECT().GetB2BSalaryProgramVerificationStatus(gomock.Any(), &userPb.GetB2BSalaryProgramVerificationStatusRequest{
			Identifier: &userPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{
				ActorId: "actor-id",
			},
		}).Return(&userPb.GetB2BSalaryProgramVerificationStatusResponse{
			Status:     rpc.StatusOk(),
			IsVerified: isB2BUser,
		}, nil)
	}

	type args struct {
		req *StageProcessorRequest
	}
	tests := map[string]struct {
		args     args
		want     *StageProcessorResponse
		wantErr  error
		mockFunc func(*Clients)
	}{
		"GetScreenerAttemptStatus: Passed": {
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:      "actor-id",
						OnboardingId: "onb-id",
					},
				},
			},
			wantErr: NoActionError,
			mockFunc: func(m *Clients) {
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), "actor-id").Return(false, nil)
				B2BVerificationCall(false, m)
				m.riskClient.EXPECT().GetScreenerAttemptStatus(gomock.Any(), screenerStatusReq("onb-id")).Return(&riskPb.GetScreenerAttemptStatusResponse{
					Status:         rpc.StatusOk(),
					ScreenerStatus: riskScrnrPb.ScreenerStatus_SCREENER_STATUS_DONE,
					Verdict:        riskScrnrPb.Verdict_VERDICT_PASS,
				}, nil)
				m.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
			},
		},
		"GetScreenerAttemptStatus: Passed, B2B: True": {
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:      "actor-id",
						OnboardingId: "onb-id",
					},
				},
			},
			wantErr: NoActionError,
			mockFunc: func(m *Clients) {
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), "actor-id").Return(false, nil)
				B2BVerificationCall(true, m)
				m.riskClient.EXPECT().GetScreenerAttemptStatus(gomock.Any(), &riskPb.GetScreenerAttemptStatusRequest{
					AttemptIdentifier: &riskScrnrPb.AttemptIdentifier{
						Identifier: &riskScrnrPb.AttemptIdentifier_CriteriaClientReqId{
							CriteriaClientReqId: &riskScrnrPb.CriteriaAndClientReqIDIdentifier{
								ScreenerCriteria: riskEnumPb.ScreenerCriteria_SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING,
								ClientRequestId:  "onb-id",
							},
						},
					},
				}).Return(&riskPb.GetScreenerAttemptStatusResponse{
					Status:         rpc.StatusOk(),
					ScreenerStatus: riskScrnrPb.ScreenerStatus_SCREENER_STATUS_DONE,
					Verdict:        riskScrnrPb.Verdict_VERDICT_PASS,
				}, nil)
				m.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
			},
		},
		"GetScreenerAttemptStatus: RNF, ScreenActor: Passed": {
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:      "actor-id",
						OnboardingId: "onb-id",
					},
				},
			},
			wantErr: NoActionError,
			mockFunc: func(m *Clients) {
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), "actor-id").Return(false, nil)
				B2BVerificationCall(false, m)
				m.riskClient.EXPECT().GetScreenerAttemptStatus(gomock.Any(), screenerStatusReq("onb-id")).Return(&riskPb.GetScreenerAttemptStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				m.riskClient.EXPECT().ScreenActor(gomock.Any(), &riskPb.ScreenActorRequest{
					ActorId:          "actor-id",
					ScreenerCriteria: riskEnumPb.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
					ClientRequestId:  "onb-id",
				}).Return(&riskPb.ScreenActorResponse{
					Status:         rpc.StatusOk(),
					ScreenerStatus: riskScrnrPb.ScreenerStatus_SCREENER_STATUS_DONE,
					Verdict:        riskScrnrPb.Verdict_VERDICT_PASS,
				}, nil)
				m.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
			},
		},
		"GetScreenerAttemptStatus: Manual Review": {
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:      "actor-id",
						OnboardingId: "onb-id",
					},
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewErrorFullScreen(context.Background(), error2.OnbErrForcedManualReview),
			},
			mockFunc: func(m *Clients) {
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), "actor-id").Return(false, nil)
				B2BVerificationCall(false, m)
				m.riskClient.EXPECT().GetScreenerAttemptStatus(gomock.Any(), screenerStatusReq("onb-id")).Return(&riskPb.GetScreenerAttemptStatusResponse{
					Status:         rpc.StatusOk(),
					ScreenerStatus: riskScrnrPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
				}, nil)
				m.OnbDao.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				m.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
			},
		},
		"GetScreenerAttemptStatus: In Progress": {
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:      "actor-id",
						OnboardingId: "onb-id",
					},
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewActionToGetNextAction(),
			},
			mockFunc: func(m *Clients) {
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), "actor-id").Return(false, nil)
				B2BVerificationCall(false, m)
				m.riskClient.EXPECT().GetScreenerAttemptStatus(gomock.Any(), screenerStatusReq("onb-id")).Return(&riskPb.GetScreenerAttemptStatusResponse{
					Status:         rpc.StatusOk(),
					ScreenerStatus: riskScrnrPb.ScreenerStatus_SCREENER_STATUS_IN_PROGRESS,
				}, nil)
			},
		},
		"GetScreenerAttemptStatus: In Progress, Criteria: CC": {
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:      "actor-id",
						OnboardingId: "onb-id",
						Feature:      onbPb.Feature_FEATURE_CC,
					},
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewActionToGetNextAction(),
			},
			mockFunc: func(m *Clients) {
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), "actor-id").Return(false, nil)
				m.riskClient.EXPECT().GetScreenerAttemptStatus(gomock.Any(), &riskPb.GetScreenerAttemptStatusRequest{
					AttemptIdentifier: &riskScrnrPb.AttemptIdentifier{
						Identifier: &riskScrnrPb.AttemptIdentifier_CriteriaClientReqId{
							CriteriaClientReqId: &riskScrnrPb.CriteriaAndClientReqIDIdentifier{
								ScreenerCriteria: riskEnumPb.ScreenerCriteria_SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING,
								ClientRequestId:  "onb-id",
							},
						},
					},
				}).Return(&riskPb.GetScreenerAttemptStatusResponse{
					Status:         rpc.StatusOk(),
					ScreenerStatus: riskScrnrPb.ScreenerStatus_SCREENER_STATUS_IN_PROGRESS,
				}, nil)
			},
		},
		"GetScreenerAttemptStatus: Failed": {
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId:      "actor-id",
						OnboardingId: "onb-id",
					},
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewErrorFullScreen(context.Background(), error2.OnbErrRiskScreeningBlocked),
			},
			mockFunc: func(m *Clients) {
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), "actor-id").Return(false, nil)
				B2BVerificationCall(false, m)
				m.riskClient.EXPECT().GetScreenerAttemptStatus(gomock.Any(), screenerStatusReq("onb-id")).Return(&riskPb.GetScreenerAttemptStatusResponse{
					Status:         rpc.StatusOk(),
					ScreenerStatus: riskScrnrPb.ScreenerStatus_SCREENER_STATUS_DONE,
					Verdict:        riskScrnrPb.Verdict_VERDICT_FAIL,
				}, nil)
				m.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
				m.OnbDao.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			},
		},
	}
	for name, tt := range tests {
		tt := tt
		t.Run(name, func(t *testing.T) {
			t.Parallel()
			clients := setupServiceWithMocks(t)

			r := &RiskScreeningStage{
				riskClient:  clients.riskClient,
				eventLogger: clients.eventLogger,
				onbDao:      clients.OnbDao,
				userClient:  clients.userClient,
				conf:        clients.genConf.Onboarding(),
				userProc:    clients.userProc,
			}

			if tt.mockFunc != nil {
				tt.mockFunc(clients)
			}

			clients.statConf.Onboarding.Flags.AllowManualReviewUsers = false
			if err := clients.genConf.Onboarding().Set(clients.statConf.Onboarding, true, nil); err != nil {
				return
			}

			got, err := r.StageProcessor(context.Background(), deepcopy.Copy(tt.args.req).(*StageProcessorRequest))
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Unexpected error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			if diff := cmp.Diff(got.GetNextAction(), tt.want.GetNextAction(), protocmp.Transform()); diff != "" {
				t.Errorf("StageProcessor value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
