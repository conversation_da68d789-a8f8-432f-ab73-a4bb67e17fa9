package stageproc

import (
	"context"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/kyc"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

type ConfirmCardMailingAddressStage struct {
	userClient     userPb.UsersClient
	kycClient      kyc.KycClient
	dynConf        *genconf.OnboardingConfig
	bankCustClient bankcust.BankCustomerServiceClient
}

func NewConfirmCardMailingAddressStage(userClient userPb.UsersClient, kycClient kyc.KycClient, bcClient bankcust.BankCustomerServiceClient,
	dynConf *genconf.OnboardingConfig) *ConfirmCardMailingAddressStage {
	return &ConfirmCardMailingAddressStage{
		userClient:     userClient,
		kycClient:      kycClient,
		bankCustClient: bcClient,
		dynConf:        dynConf,
	}
}

// StageProcessor checks if user's debit card name match has passed or not and if user has updated the card shipping preference.
// If name match is not passed we will check the retry attempts and give option to update the name till retry attempts does not reach a threshold.
// If name match is success we will check if user has updated his card shipping preference.
// If card delivery address is not set, deeplink for address screen is returned, else nil.
func (s *ConfirmCardMailingAddressStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	if s.dynConf.ConfirmCardMailingAddress().EnableConfirmCardMailingAddressV2() {
		return s.stageProcV2(ctx, req)
	}
	logger.Info(ctx, "confirm card mailing address v1")
	return s.stageProcV1(ctx, req)
}

func (s *ConfirmCardMailingAddressStage) stageProcV1(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	onb := req.GetOnb()
	var (
		err      error
		isNrUser = onb.GetFeature().IsNonResidentUserOnboarding()
	)
	response := &StageProcessorResponse{}
	debitCardNameCheckMetaData := onb.GetStageMetadata().GetDebitCardNameCheck()
	switch {
	case debitCardNameCheckMetaData == nil:
		response.NextAction, err = ConfirmCardMailingAddressDLGen(ctx, s.bankCustClient, s.kycClient, onb.GetActorId(), onb.GetUserId(), isNrUser, s.dynConf)()
		return response, err
	case !debitCardNameCheckMetaData.GetOldNameMatchPassed():
		if debitCardNameCheckMetaData.GetNameCheckRetryCount() >= constant.DebitCardNameMaxRetries {
			logger.Info(ctx, "debit card retries exhausted for user")
			response.NextAction = deeplink.NewErrorFullScreen(ctx, error2.OnbErrCardNameMaxRetries)
			return response, nil
		}
		response.NextAction, err = ConfirmCardMailingAddressDLGen(ctx, s.bankCustClient, s.kycClient, onb.GetActorId(), onb.GetUserId(), isNrUser, s.dynConf)()
		return response, err
	default:
		logger.Info(ctx, "debit card name match passed for the user")
		return response, NoActionError
	}
}
