package stageproc

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	commonTypesPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/location"
	mockLocation "github.com/epifi/gamma/api/user/location/mocks"
	mockUsers "github.com/epifi/gamma/api/user/mocks"
	obfuscatorPb "github.com/epifi/gamma/api/user/obfuscator"
	mockObfuscator "github.com/epifi/gamma/api/user/obfuscator/mocks"
	"github.com/epifi/gamma/user/config/genconf"
)

type nrlocationStageMockStruct struct {
	conf           *genconf.OnboardingConfig
	mockLocClient  *mockLocation.MockLocationClient
	mockObfClient  *mockObfuscator.MockObfuscatorClient
	mockUserClient *mockUsers.MockUsersClient
}

func TestNRLocationCheckStage_StageProcessor(t *testing.T) {
	var (
		qatarAddress = &typesv2.PostalAddress{
			RegionCode: "Qatar",
		}
	)
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type args struct {
		req *StageProcessorRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *StageProcessorResponse
		wantErr error
		mocks   func(args, *nrlocationStageMockStruct)
	}{
		{
			name: "Country not found by IP",
			args: args{
				req: &StageProcessorRequest{},
			},
			want:    nil,
			wantErr: SkipStageError,
			mocks: func(args args, m *nrlocationStageMockStruct) {
				m.mockObfClient.EXPECT().ObfuscatePII(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.mockLocClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), gomock.Any()).Return(&location.FetchAndStoreAddressForIdentifierResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
		{
			name: "Error in IsNonResidentUser",
			args: args{
				req: &StageProcessorRequest{},
			},
			want:    nil,
			wantErr: errors.New("some error"),
			mocks: func(args args, m *nrlocationStageMockStruct) {
				m.mockObfClient.EXPECT().ObfuscatePII(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.mockLocClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), gomock.Any()).Return(&location.FetchAndStoreAddressForIdentifierResponse{
					Status:  rpc.StatusOk(),
					Address: qatarAddress,
				}, nil)
				m.mockUserClient.EXPECT().IsNonResidentUser(gomock.Any(), gomock.Any()).Return(nil, errors.New("some error")).AnyTimes()
			},
		},
		{
			name: "Resident user entered location check for NR flow",
			args: args{
				req: &StageProcessorRequest{},
			},
			want:    nil,
			wantErr: errors.New("resident user entered location check for NR flow"),
			mocks: func(args args, m *nrlocationStageMockStruct) {
				m.mockObfClient.EXPECT().ObfuscatePII(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.mockLocClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), gomock.Any()).Return(&location.FetchAndStoreAddressForIdentifierResponse{
					Status:  rpc.StatusOk(),
					Address: qatarAddress,
				}, nil)
				m.mockUserClient.EXPECT().IsNonResidentUser(gomock.Any(), gomock.Any()).Return(&usersPb.IsNonResidentUserResponse{
					IsNonResidentUser: commonTypesPb.BooleanEnum_FALSE,
					Status:            rpc.StatusOk(),
				}, nil)
			},
		},
		{
			name: "Location check passed",
			args: args{
				req: &StageProcessorRequest{},
			},
			want:    nil,
			wantErr: NoActionError,
			mocks: func(args args, m *nrlocationStageMockStruct) {
				m.mockObfClient.EXPECT().ObfuscatePII(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.mockLocClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), gomock.Any()).Return(&location.FetchAndStoreAddressForIdentifierResponse{
					Status:  rpc.StatusOk(),
					Address: qatarAddress,
				}, nil)
				m.mockUserClient.EXPECT().IsNonResidentUser(gomock.Any(), gomock.Any()).Return(&usersPb.IsNonResidentUserResponse{
					IsNonResidentUser:   commonTypesPb.BooleanEnum_TRUE,
					ResidentCountryCode: typesv2.CountryCode_COUNTRY_CODE_QAT,
					Status:              rpc.StatusOk(),
				}, nil)
			},
		},
		{
			name: "Location blocked",
			args: args{
				req: &StageProcessorRequest{},
			},
			want: &StageProcessorResponse{
				NextAction: getLocationCheckDeeplink(context.Background(), typesv2.CountryCode_COUNTRY_CODE_ARE),
				AnalyticsProps: map[string]string{
					"block_reason":     "NR user not in resident country",
					"current_country":  "Qatar",
					"resident_country": "United Arab Emirates",
				},
			},
			wantErr: nil,
			mocks: func(args args, m *nrlocationStageMockStruct) {
				m.mockObfClient.EXPECT().ObfuscatePII(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.mockLocClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), gomock.Any()).Return(&location.FetchAndStoreAddressForIdentifierResponse{
					Status:  rpc.StatusOk(),
					Address: qatarAddress,
				}, nil)
				m.mockUserClient.EXPECT().IsNonResidentUser(gomock.Any(), gomock.Any()).Return(&usersPb.IsNonResidentUserResponse{
					IsNonResidentUser:   commonTypesPb.BooleanEnum_TRUE,
					ResidentCountryCode: typesv2.CountryCode_COUNTRY_CODE_ARE,
					Status:              rpc.StatusOk(),
				}, nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mocks := &nrlocationStageMockStruct{
				conf:           dynConf.Onboarding(),
				mockLocClient:  mockLocation.NewMockLocationClient(ctrl),
				mockObfClient:  mockObfuscator.NewMockObfuscatorClient(ctrl),
				mockUserClient: mockUsers.NewMockUsersClient(ctrl),
			}
			tt.mocks(tt.args, mocks)
			stage := NewNRLocationCheckStage(mocks.mockLocClient, mocks.mockObfClient, mocks.mockUserClient, mocks.conf)
			got, err := stage.StageProcessor(epificontext.CtxWithXFwdForAddr(context.Background(), "***********"), tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			assert.Equal(t, tt.want, got)
		})
	}
}
