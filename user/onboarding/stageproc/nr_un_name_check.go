package stageproc

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/kyc"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

type NRUNNameCheckStage struct {
	onbDao    dao.OnboardingDao
	kycClient kyc.KycClient
	userProc  helper.UserProcessor
}

func NewNRUNNameCheckStage(onbDao dao.OnboardingDao, kycClient kyc.KycClient, userProc helper.UserProcessor) *NRUNNameCheckStage {
	return &NRUNNameCheckStage{onbDao: onbDao, kycClient: kycClient, userProc: userProc}
}

var _ StageProcessorType = (*NRUNNameCheckStage)(nil)

func (s *NRUNNameCheckStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb = req.GetOnb()
	)
	// skip the stage if customer creation is successful or skipped
	if getStageStatus(onb, onbPb.OnboardingStage_CUSTOMER_CREATION).IsSuccessOrSkipped() {
		logger.Info(ctx, fmt.Sprintf("skipping the %v stage since %v is successful or skip", onbPb.OnboardingStage_UN_NAME_CHECK, onbPb.OnboardingStage_CUSTOMER_CREATION))
		return nil, SkipStageError
	}
	if getStageStatus(onb, onbPb.OnboardingStage_UN_NAME_CHECK) == onbPb.OnboardingState_FAILURE {
		return &StageProcessorResponse{
			NextAction: deeplink.NewErrorFullScreen(ctx, error2.OnbErrUNNameCheckBlocked),
		}, nil
	}

	user, err := s.userProc.GetUserByUserId(ctx, onb.GetUserId())
	if err != nil {
		logger.Error(ctx, "failed to get user by user id", zap.Error(err))
		return nil, err
	}

	passportName := user.GetProfile().GetKycName()
	if passportName == nil {
		logger.Error(ctx, "Passport Name is empty for the user")
		return nil, fmt.Errorf("passport name is empty for the user")
	}

	res, errValidate := s.kycClient.ValidateCustomerName(ctx, &kyc.ValidateCustomerNameRequest{
		Name: passportName,
	})
	if grpcErr := epifigrpc.RPCError(res, errValidate); grpcErr != nil {
		logger.Error(ctx, "error in kyc validate customer name", zap.Error(grpcErr))
		return nil, grpcErr
	}
	logger.Info(ctx, "UN Name Check status received", zap.String(logger.STATUS, res.GetUNNameCheckStatus().String()))
	if err = s.updateUNNameCheckStatus(ctx, onb.GetOnboardingId(), res.GetUNNameCheckStatus()); err != nil {
		return nil, err
	}

	if res.GetUNNameCheckStatus() == kyc.UNNameCheckStatus_UN_NAME_CHECK_STATUS_FAILED {
		logger.Info(ctx, "user failed UN Name Check", logOnb(onb.GetOnboardingId()))
		_, _ = updateStatus(ctx, s.onbDao, onb.GetOnboardingId(), onbPb.OnboardingStage_UN_NAME_CHECK, onbPb.OnboardingState_FAILURE)
		return &StageProcessorResponse{
			NextAction: deeplink.NewErrorFullScreen(ctx, error2.OnbErrUNNameCheckBlocked),
		}, nil
	}

	return nil, NoActionError
}

func (s *NRUNNameCheckStage) updateUNNameCheckStatus(ctx context.Context, onbId string, unnc kyc.UNNameCheckStatus) error {
	if unnc == kyc.UNNameCheckStatus_UN_NAME_CHECK_STATUS_UNSPECIFIED {
		logger.Error(ctx, "unexpected unnc status", logOnb(onbId))
		return epifierrors.ErrInvalidArgument
	}
	if err := storage.RunCRDBIdempotentTxn(ctx, TxnRetries, func(ctx context.Context) error {
		// get latest Onb details
		onb, err := getOnboardingDetails(ctx, s.onbDao, onbId)
		if err != nil {
			return err
		}

		// update metadata
		if onb.GetStageMetadata() == nil {
			onb.StageMetadata = &onbPb.StageMetadata{}
		}
		onb.GetStageMetadata().UNNameCheckStatusV2 = unnc

		// update in DB
		return updateStageMetadata(ctx, s.onbDao, onbId, onb.GetStageMetadata())
	}); err != nil {
		logger.Error(ctx, "error in running txn to save un name check metadata", zap.Error(err))
		return err
	}
	return nil
}
