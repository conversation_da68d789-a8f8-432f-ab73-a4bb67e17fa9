package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"

	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/durationpb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	txn "github.com/epifi/be-common/pkg/storage/v2"

	livPb "github.com/epifi/gamma/api/auth/liveness"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	kycPb "github.com/epifi/gamma/api/kyc"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/auth"
	img "github.com/epifi/gamma/pkg/image"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
)

type LivenessStage struct {
	kycClient     kycPb.KycClient
	onboardingDao dao.OnboardingDao
	livClient     livPb.LivenessClient
	conf          *genconf.OnboardingConfig
	eventBroker   events.Broker
	userClient    userPb.UsersClient
	eventLogger   userEvents.EventLogger
	s3Client      s3.S3Client
	userConf      *genconf.Config
}

func NewLivenessStage(kycClient kycPb.KycClient, onboardingDao dao.OnboardingDao, conf *genconf.OnboardingConfig,
	livClient livPb.LivenessClient, userClient userPb.UsersClient, eventBroker events.Broker, eventLogger userEvents.EventLogger,
	s3Client s3.S3Client, userConf *genconf.Config) *LivenessStage {
	return &LivenessStage{
		kycClient:     kycClient,
		onboardingDao: onboardingDao,
		conf:          conf,
		livClient:     livClient,
		userClient:    userClient,
		eventBroker:   eventBroker,
		eventLogger:   eventLogger,
		s3Client:      s3Client,
		userConf:      userConf,
	}
}

var (
	transactionMaxRetry = uint(3)
)

func (s *LivenessStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb                = req.GetOnb()
		stage              = onbPb.OnboardingStage_LIVENESS
		actorId            = req.GetOnb().GetActorId()
		dl                 *dlPb.Deeplink
		errProcessLiveness error
		summaryReqId       = auth.GetLivenessSummaryReqId(onb)
		currentState       = getStageStatus(onb, stage)
	)

	kycStatusRes, err := kycStatus(ctx, s.kycClient, onb.GetActorId())
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("No kyc attempt found for %v", onb.GetActorId())
	}

	dl, errProcessLiveness = s.processLiveness(ctx, actorId, summaryReqId, kycStatusRes, onb, currentState)

	return &StageProcessorResponse{
		NextAction: dl,
	}, errProcessLiveness
}

func (s *LivenessStage) processLiveness(ctx context.Context, actorId, summaryReqId string,
	kycStatusRes *kycPb.CheckKYCStatusResponse, onb *onbPb.OnboardingDetails, currentState onbPb.OnboardingState) (*dlPb.Deeplink, error) {
	if currentState == onbPb.OnboardingState_RESET {
		getLSResp, getLSErr := s.livClient.GetLivenessSummary(ctx, &livPb.GetLivenessSummaryRequest{
			ActorId:      actorId,
			RequestId:    summaryReqId,
			LivenessFlow: livPb.LivenessFlow_ONBOARDING, // Using onb flow since it will only be used while onboarding
		})
		if grpcErr := epifigrpc.RPCError(getLSResp, getLSErr); rpc.StatusFromError(grpcErr).IsRecordNotFound() ||
			getLSResp.GetSummary().GetStatus() != livPb.SummaryStatus_SUMMARY_PASSED {
			// To handle users whose LIVENESS was in RESET state, but they went through V1 flow. This will make the users go through V2 flow of liveness
			_ = updateKYCStageState(ctx, onb, onbPb.OnboardingStage_LIVENESS, onbPb.OnboardingState_INITIATED, s.onboardingDao)
			return deeplink.NewActionToGetNextAction(), nil
		}
		logger.Debug(ctx, "Liveness in RESET state, redoing facematch")
		return s.redoFacematch(ctx, kycStatusRes, onb, actorId, summaryReqId)
	}

	summaryStatusRes, errSummaryStatus := s.livClient.GetLivenessSummaryStatus(ctx, &livPb.GetLivenessSummaryStatusRequest{
		ActorId:      actorId,
		RequestId:    summaryReqId,
		LivenessFlow: livPb.LivenessFlow_ONBOARDING,
	})
	if grpcErr := epifigrpc.RPCError(summaryStatusRes, errSummaryStatus); grpcErr != nil && !rpc.StatusFromError(grpcErr).IsRecordNotFound() {
		logger.Error(ctx, "Failed to get liveness summary status", zap.Error(grpcErr))
		return nil, grpcErr
	}

	if summaryStatusRes.GetStatus().IsRecordNotFound() {
		if s.isLivFMManuallyFailed(ctx, actorId) {
			logger.Info(ctx, "Previous attempt of the user was manually failed, not starting liveness summary")
			return deeplink.NewErrorFullScreen(ctx, error2.OnbErrLivenessManuallyFailed), nil
		}
		return s.createNewLivenessSummary(ctx, actorId, onb.GetOnboardingId(), kycStatusRes)
	}

	logger.Debug(ctx, "OnboardingSummary Received Summary Status",
		zap.String(logger.ACTOR_ID_V2, actorId),
		zap.String("SummaryStatus", summaryStatusRes.GetSummaryStatus().String()),
		zap.String("SummaryLivenessStatus", summaryStatusRes.GetSummaryLivenessStatus().String()),
		zap.String("SummaryFacematchStatus", summaryStatusRes.GetSummaryFacematchStatus().String()))
	switch summaryStatusRes.GetSummaryStatus() {
	case livPb.SummaryStatus_SUMMARY_UNSPECIFIED, livPb.SummaryStatus_SUMMARY_IN_PROGRESS:
		return s.createLivenessAttempt(ctx, actorId, summaryReqId)
	case livPb.SummaryStatus_SUMMARY_PASSED:
		s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), false, "")
		// set liveness photo in user profile
		if errImageUpdate := s.updateLivenessImageForProfile(ctx, onb.GetUserId(), onb.GetActorId(), summaryReqId); errImageUpdate != nil {
			logger.Error(ctx, "error in updating liveness photo in user profile", zap.Error(errImageUpdate))
			return nil, errImageUpdate
		}

		return nil, NoActionError
	case livPb.SummaryStatus_SUMMARY_RETRIES_EXHAUSTED:
		dl, done := s.isCKYCFacematchFailure(ctx, kycStatusRes, onb, summaryStatusRes)
		if done {
			return dl, nil
		}

		if s.conf.Flags().AllowManualReviewUsers() && !getStageStatus(onb, onbPb.OnboardingStage_DEVICE_REGISTRATION).IsSuccessOrSkipped() {
			logger.Info(ctx, "Device registration not done yet, skipping showing manual review screen")
			return nil, NoActionSkipStatusUpdateError
		}

		_ = updateKYCStageState(ctx, onb, onbPb.OnboardingStage_LIVENESS, onbPb.OnboardingState_MANUAL_INTERVENTION, s.onboardingDao)
		s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), true, userEvents.BlockReasonRisk)
		return s.livenessTerminalScreenV2(ctx, summaryStatusRes, actorId)
	case livPb.SummaryStatus_SUMMARY_EXPIRED:
		return s.createNewLivenessSummary(ctx, actorId, onb.GetOnboardingId(), kycStatusRes)
	default:
		logger.Error(ctx, fmt.Sprintf("unexpected livenesss summary status received : %v", summaryStatusRes.GetSummaryStatus()))
		return nil, fmt.Errorf("unexpected livenesss summary status received : %v", summaryStatusRes.GetSummaryStatus())
	}
}

func (s *LivenessStage) isCKYCFacematchFailure(ctx context.Context, kycStatusRes *kycPb.CheckKYCStatusResponse, onb *onbPb.OnboardingDetails, summaryStatusRes *livPb.GetLivenessSummaryStatusResponse) (*dlPb.Deeplink, bool) {
	if kycStatusRes.GetKycType() == kycPb.KycType_CKYC && getStageStatus(onb, onbPb.OnboardingStage_EKYC) == onbPb.OnboardingState_SKIPPED &&
		summaryStatusRes.GetSummaryFacematchStatus() == livPb.SummaryFacematchStatus_SUMMARY_FACEMATCH_RETRY {
		logger.Info(ctx, fmt.Sprintf("CKYC facematch failed, resetting EKYC. SummaryStatus : %v, SummaryLivenessStatus : %v, SummaryFacematchStatus : %v",
			summaryStatusRes.GetSummaryStatus(), summaryStatusRes.GetSummaryLivenessStatus(), summaryStatusRes.GetSummaryFacematchStatus()))
		_ = updateKYCStageState(ctx, onb, onbPb.OnboardingStage_EKYC, onbPb.OnboardingState_RESET, s.onboardingDao)
		_ = updateKYCStageState(ctx, onb, onbPb.OnboardingStage_LIVENESS, onbPb.OnboardingState_RESET, s.onboardingDao)
		return deeplink.NewActionToGetNextAction(), true
	}
	return nil, false
}

func (s *LivenessStage) createNewLivenessSummary(ctx context.Context, actorId string, onbId string, kycStatusRes *kycPb.CheckKYCStatusResponse) (*dlPb.Deeplink, error) {
	fmImages, errImages := getImagesFromKYCPayload(ctx, s.kycClient, actorId, kycStatusRes.GetKycType())
	if errImages != nil {
		return nil, errImages
	}
	summaryReqId := idgen.FederalRandomSequence("ONB", 10)
	_, err := s.updateLivenessStageMetadata(ctx, summaryReqId, onbId)
	if err != nil {
		logger.Error(ctx, "unable to update onboarding details liveness metadata")
		return nil, err
	}
	logger.Debug(ctx, fmt.Sprintf("OnboardingSummary Creating LivenessSummary. ActorID : %v, ReqId : %v", actorId, summaryReqId))
	createRes, errSummary := s.livClient.CreateLivenessSummary(ctx, &livPb.CreateLivenessSummaryRequest{
		ActorId:            actorId,
		RequestId:          summaryReqId,
		LivenessFlow:       livPb.LivenessFlow_ONBOARDING,
		MaxRetries:         3,
		RefFacematchPhotos: fmImages,
		ForceManualReview:  commontypes.BooleanEnum_FALSE,
		ExpiryDuration:     durationpb.New(s.conf.LivenessSummaryExpiryDuration()),
	})
	if grpcErr := epifigrpc.RPCError(createRes, errSummary); grpcErr != nil {
		logger.Error(ctx, "Failed to create livenessSummary", zap.Error(grpcErr))
		return nil, grpcErr
	}

	return s.createLivenessAttempt(ctx, actorId, summaryReqId)
}

func (s *LivenessStage) createLivenessAttempt(ctx context.Context, actorId, summaryReqId string) (*dlPb.Deeplink, error) {
	createAttemptRes, errCreateAttempt := s.livClient.CreateLivenessAttempt(ctx, &livPb.CreateLivenessAttemptRequest{
		ActorId:          actorId,
		SummaryRequestId: summaryReqId,
		LivenessFlow:     livPb.LivenessFlow_ONBOARDING,
	})
	if grpcErr := epifigrpc.RPCError(createAttemptRes, errCreateAttempt); grpcErr != nil {
		logger.Error(ctx, "Failed to create liveness attempt", zap.Error(grpcErr))
		return nil, grpcErr
	}

	// Send GET_NEXT_ONBOARDING_ACTION if video is received for the current attempt
	if createAttemptRes.GetLivenessAttempt().GetStatus() > livPb.LivenessStatus_LIVENESS_VIDEO_RECEIVED {
		return deeplink.NewActionToGetNextAction(), nil
	}
	useNewLivenessFlow := apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.conf.Flags().UseNewLivenessFlow())
	if useNewLivenessFlow {
		logger.Info(ctx, "using new liveness flow")
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_CHECK_LIVENESS,
		ScreenOptions: &dlPb.Deeplink_CheckLivenessScreenOptions{
			CheckLivenessScreenOptions: &dlPb.CheckLivenessScreenOptions{
				LivenessFlow:             dlPb.LivenessFlow_ONBOARDING,
				AttemptId:                createAttemptRes.GetLivenessAttempt().GetRequestId(),
				Otp:                      createAttemptRes.GetLivenessAttempt().GetOtp(),
				ErrorLastLivenessFailure: livStatusToDLStatus(createAttemptRes.GetOldAttemptStatus()),
				NextAction: &dlPb.Deeplink{
					Screen: dlPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
				},
				DisableFaceTrackingAndroid: true,
				Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/liveness_selfie_video.png").WithProperties(&commontypes.VisualElementProperties{
					Width:  240,
					Height: 240,
				}),
				Title:    commontypes.GetTextFromStringFontColourFontStyle("Take a selfie video in \njust 10 seconds", "#333333", commontypes.FontStyle_SUBTITLE_1),
				Subtitle: commontypes.GetTextFromStringFontColourFontStyle("This is to ensure no one is\npretending to be you.", "#878A8D", commontypes.FontStyle_BODY_S),
				ListItems: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle("Take the video in a well-lit place 💡", "#333333", commontypes.FontStyle_BODY_S),
					commontypes.GetTextFromStringFontColourFontStyle("Make sure you read out the 4 digits that appear on-screen 📢", "#333333", commontypes.FontStyle_BODY_S),
				},
				EnableVpnCheck:     true,
				UseNewLivenessFlow: useNewLivenessFlow,
			},
		},
	}, nil
}

func livStatusToDLStatus(livStatus livPb.LivenessStatus) dlPb.ErrorLivenessFailure {
	livenessFailureMap := map[livPb.LivenessStatus]dlPb.ErrorLivenessFailure{
		livPb.LivenessStatus_LIVENESS_FAILED_RETRY:            dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_OTP,
		livPb.LivenessStatus_LIVENESS_FACE_NOT_DETECTED:       dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_NOT_DETECTED,
		livPb.LivenessStatus_LIVENESS_MULTIPLE_FACES_DETECTED: dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_MULTIPLE_FACES_DETECTED,
		livPb.LivenessStatus_LIVENESS_MANUAL_RETRY:            dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_OTP,
		livPb.LivenessStatus_LIVENESS_INVALID_VIDEO:           dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_OTP,
		livPb.LivenessStatus_LIVENESS_FAILED:                  dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_OTP,
		livPb.LivenessStatus_LIVENESS_FACE_POORLY_DETECTED:    dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_POORLY_DETECTED,
		livPb.LivenessStatus_LIVENESS_FACE_TOO_FAR:            dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_TOO_FAR,
		livPb.LivenessStatus_LIVENESS_FACE_TOO_CLOSE:          dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_TOO_CLOSE,
		livPb.LivenessStatus_LIVENESS_FACE_TOO_DARK:           dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_TOO_DARK,
		livPb.LivenessStatus_LIVENESS_FACE_TOO_BRIGHT:         dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_TOO_BRIGHT,
		livPb.LivenessStatus_LIVENESS_NO_FACE_DETECTED:        dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_FACE_NOT_DETECTED,
		livPb.LivenessStatus_LIVENESS_MANUALLY_FAILED:         dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_OTP,
		livPb.LivenessStatus_LIVENESS_QUEUE_MAX_RETRIES:       dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_OTP,
	}
	dlEnum, ok := livenessFailureMap[livStatus]
	if ok {
		return dlEnum
	}
	return dlPb.ErrorLivenessFailure_ERROR_LIVENESS_FAILURE_UNSPECIFIED
}

func getImagesFromKYCPayload(ctx context.Context, kycClient kycPb.KycClient, actorId string, kycType kycPb.KycType) ([]*commontypes.Image, error) {
	kycDataRes, errKycData := kycClient.GetKYCVendorData(ctx, &kycPb.GetKYCVendorDataRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(kycDataRes, errKycData); grpcErr != nil {
		logger.Error(ctx, "failed to get kyc vendor data", zap.Error(grpcErr))
		return nil, errKycData
	}

	payload := kycDataRes.GetVendorData().GetPayload()
	switch kycType {
	case kycPb.KycType_CKYC:
		var ckycImages []*commontypes.Image
		for _, record := range payload.GetCkycDownloads().GetPayloads() {
			ckycImages = append(ckycImages, record.GetImagesData()...)
		}
		return ckycImages, nil
	case kycPb.KycType_EKYC:
		return []*commontypes.Image{payload.GetEkycRecord().GetPhoto()}, nil
	case kycPb.KycType_BKYC:
		return []*commontypes.Image{payload.GetBkycRecord().GetPhoto()}, nil
	default:
		logger.Error(ctx, fmt.Sprintf("unknown kyc type received : %v", kycType))
		return nil, fmt.Errorf("unknown kyc type received")
	}
}

// redoFacematch handles redoing the facematch in case of KYC expiry
func (s *LivenessStage) redoFacematch(ctx context.Context, kycStatusRes *kycPb.CheckKYCStatusResponse, onb *onbPb.OnboardingDetails, actorId, summaryReqId string) (*dlPb.Deeplink, error) {
	fmImages, errImages := getImagesFromKYCPayload(ctx, s.kycClient, actorId, kycStatusRes.GetKycType())
	if errImages != nil {
		return nil, errImages
	}
	redoFMResp, errRedoFM := s.livClient.RedoFaceMatch(ctx, &livPb.RedoFaceMatchRequest{
		ActorId:          actorId,
		SummaryRequestId: summaryReqId,
		LivenessFlow:     livPb.LivenessFlow_ONBOARDING,
		ReferencePhotos:  fmImages,
	})
	if grpcErr := epifigrpc.RPCError(redoFMResp, errRedoFM); grpcErr != nil {
		logger.Error(ctx, "Failed to do redoFM", zap.Error(grpcErr))
		return nil, grpcErr
	}
	// Send GetNextOnboardingAction or Let the below summary status do it's work?
	_ = updateKYCStageState(ctx, onb, onbPb.OnboardingStage_LIVENESS, onbPb.OnboardingState_INPROGRESS, s.onboardingDao)
	return deeplink.NewActionToGetNextAction(), nil
}

func (s *LivenessStage) livenessTerminalScreenV2(ctx context.Context, resp *livPb.GetLivenessSummaryStatusResponse, actorId string) (*dlPb.Deeplink, error) {
	if resp.GetSummaryLivenessStatus() == livPb.SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_FAILED || resp.GetSummaryFacematchStatus() == livPb.SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_FAILED {
		return deeplink.NewErrorFullScreen(ctx, error2.OnbErrLivenessManuallyFailed), nil
	}

	if s.conf.Flags().AllowManualReviewUsers() {
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), userEvents.NewManualReviewEvent(actorId))
		})
		return deeplink.NewLivenessManualReviewDL(), nil
	}
	return deeplink.NewActionForLivenessMaxRetries(), nil
}

// isLivFMManuallyFailed checks if any of the liveness or facematch attempt is manually failed for a user
func (s *LivenessStage) isLivFMManuallyFailed(ctx context.Context, actorId string) bool {
	getLAResp, getLAErr := s.livClient.GetLivenessAttempts(ctx, &livPb.GetLivenessAttemptsRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(getLAResp, getLAErr); grpcErr != nil {
		logger.Error(ctx, "faced error while fetching all liveness attempts for user", zap.Error(grpcErr))
	}

	for _, la := range getLAResp.GetLivenessAttempts() {
		if la.GetStatus() == livPb.LivenessStatus_LIVENESS_MANUALLY_FAILED {
			return true
		}
	}

	getFMResp, getFMErr := s.livClient.GetFaceMatchAttempts(ctx, &livPb.GetFaceMatchAttemptsRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(getFMResp, getFMErr); grpcErr != nil {
		logger.Error(ctx, "faced error while fetching all facematch attempts for user", zap.Error(grpcErr))
	}

	for _, fm := range getFMResp.GetFaceMatchAttempts() {
		if fm.GetStatus() == livPb.FaceMatchStatus_FACE_MATCH_MANUALLY_FAILED {
			return true
		}
	}

	return false
}

func (s *LivenessStage) updateLivenessStageMetadata(ctx context.Context, summaryReqId string, onbId string) (*onbPb.OnboardingDetails, error) {
	var onbDetails *onbPb.OnboardingDetails
	var err error
	if trErr := txn.RunCRDBIdempotentTxn(ctx, transactionMaxRetry, func(ctx context.Context) error {
		onbDetails, err = s.onboardingDao.GetOnboardingDetailsById(ctx, onbId)
		if err != nil {
			return err
		}

		if onbDetails.GetStageMetadata() == nil {
			onbDetails.StageMetadata = &onbPb.StageMetadata{}
		}
		if onbDetails.GetStageMetadata().GetLivenessMetadata() == nil {
			onbDetails.GetStageMetadata().LivenessMetadata = &onbPb.LivenessMetadata{}
		}
		onbDetails.GetStageMetadata().GetLivenessMetadata().RequestId = summaryReqId
		if err = s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, onbDetails); err != nil {
			return err
		}
		return nil
	}); err != nil {
		logger.Error(ctx, "unable to update onboarding details metadata", zap.Error(trErr))
		return nil, trErr
	}
	return onbDetails, nil
}

// nolint: dupl
func (s *LivenessStage) updateLivenessImageForProfile(ctx context.Context, userId, actorId, summaryReqId string) error {
	imageData, err := s.getUserImageFromLiveness(ctx, actorId, summaryReqId)
	if err != nil {
		return err
	}

	// upload photo to S3
	filePath, httpUrl, err := s.uploadBase64ImageToS3(ctx, imageData, userId)
	if err != nil {
		return err
	}

	// updating user db with the profile imageData url
	resp, err := s.userClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
		User: &userPb.User{
			Id: userId,
			Profile: &userPb.Profile{
				ProfileImageS3FilePath: filePath,
				Photo: &commontypes.Image{
					ImageUrl: httpUrl,
				},
			},
		},
		UpdateMask: []userPb.UserFieldMask{userPb.UserFieldMask_PROFILE_IMAGE_S3_FILE_PATH,
			userPb.UserFieldMask_PHOTO},
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in update user images in db", zap.Error(err), zap.String(logger.ENTITY_ID, userId))
		return err
	}

	return nil
}

// nolint: dupl
func (s *LivenessStage) getUserImageFromLiveness(ctx context.Context, actorId, summaryReqId string) (string, error) {
	getLivSummaryRes, errGetLivSummary := s.livClient.GetLivenessSummary(ctx, &livPb.GetLivenessSummaryRequest{
		ActorId:      actorId,
		RequestId:    summaryReqId,
		LivenessFlow: livPb.LivenessFlow_ONBOARDING,
	})
	if grpcErr := epifigrpc.RPCError(getLivSummaryRes, errGetLivSummary); grpcErr != nil {
		logger.Error(ctx, "failed to get liveness summary to get liveness frame", zap.Error(grpcErr))
		return "", grpcErr
	}

	getLivAttResp, errGetLivAtt := s.livClient.GetLivenessAttempt(ctx, &livPb.GetLivenessAttemptRequest{
		LivenessReqId: getLivSummaryRes.GetSummary().GetLivenessAttemptId(),
	})
	if grpcErr := epifigrpc.RPCError(getLivAttResp, errGetLivAtt); grpcErr != nil {
		logger.Error(ctx, "failed to get liveness attempt by req id", zap.Error(grpcErr))
		return "", grpcErr
	}

	return s.getLivenessImageDataForAttempt(ctx, getLivAttResp.GetLivenessAttempt())
}

// nolint: dupl
// getLivenessImageDataForAttempt returns liveness image data in base64 format for the given liveness attempt.
func (s *LivenessStage) getLivenessImageDataForAttempt(ctx context.Context, la *livPb.LivenessAttempt) (string, error) {
	imgData := la.GetImageFrame().GetImageDataBase64()

	// return early if image data already present
	if imgData != "" {
		return imgData, nil
	}

	// return error if both image url & data not present
	if la.GetImageFrame().GetImageUrl() == "" {
		logger.Error(ctx, "both liveness image url & data not found")
		return "", epifierrors.ErrRecordNotFound
	}

	// liveness image url is found but image data not found
	// fetch image data from image url
	imageRes, err := s.livClient.GetS3Image(ctx, &livPb.GetS3ImageRequest{
		LocationKey: la.GetImageFrame().GetImageUrl(),
	})
	if err = epifigrpc.RPCError(imageRes, err); err != nil {
		logger.Error(ctx, "error in get s3 image data", zap.Error(err))
		return "", err
	}
	if len(imageRes.GetImage()) == 0 {
		logger.Error(ctx, "received empty image in get s3 image")
		return "", fmt.Errorf("invalid get s3 image response")
	}

	// image fetch successful, return image data in base64
	b64data := base64.StdEncoding.EncodeToString(imageRes.GetImage())
	return b64data, nil
}

// nolint: dupl
func (s *LivenessStage) uploadBase64ImageToS3(ctx context.Context, imageBase64 string, userId string) (string, string, error) {
	if strings.TrimSpace(imageBase64) == "" {
		return "", "", fmt.Errorf("empty image b64 data received")
	}
	randSuffix := userId + "/" + uuid.New().String()
	filePath := filepath.Join(imagePathPrefix, randSuffix+imageFileExt)
	logger.Debug(ctx, fmt.Sprintf("writing to bucket... %v", s.userConf.AWS().S3.UsersBucketName))

	// decoding image before uploading
	image, err := base64.StdEncoding.DecodeString(imageBase64)
	if err != nil {
		logger.Error(ctx, "failed to decode image base64 string, ", zap.Error(err))
		return "", "", err
	}

	if err = s.s3Client.Write(ctx, filePath, image, "bucket-owner-full-control"); err != nil {
		logger.Error(ctx, "failed to store image in S3", zap.Error(err), zap.String(logger.ENTITY_ID, userId))
		return "", "", err
	}

	httpUrl := img.GetHttpUrl(s.userConf.AWS().S3.UsersBucketName, s.userConf.AWS().Region, filePath)
	logger.Debug(ctx, fmt.Sprintf("BucketName: %v and Region name: %v", s.userConf.AWS().S3.UsersBucketName, s.userConf.AWS().Region))
	return filePath, httpUrl, err
}
