package stageproc

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	userLocationPb "github.com/epifi/gamma/api/user/location"
	locMock "github.com/epifi/gamma/api/user/location/mocks"
	"github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/user/obfuscator"
	obfMock "github.com/epifi/gamma/api/user/obfuscator/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	deeplink2 "github.com/epifi/gamma/user/onboarding/helper/deeplink"
	mocks2 "github.com/epifi/gamma/user/onboarding/helper/mocks"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

func TestLocationCheckStage_StageProcessor(t *testing.T) {
	actor := "actorId"
	userId := "userId"
	ip := "***********"
	ipToken := "ipToken"
	address := &types.PostalAddress{
		RegionCode: "INDIA",
		PostalCode: "560066",
	}
	address2 := &types.PostalAddress{
		RegionCode: "bikiniBottom",
		PostalCode: "42069",
	}
	randomError := fmt.Errorf("random error")
	locationToken := "location-token"

	// add ip address of user to context
	ctx := epificontext.CtxWithXFwdForAddr(context.Background(), ip)

	type mockStruct struct {
		locClient   *locMock.MockLocationClient
		obfClient   *obfMock.MockObfuscatorClient
		usersClient *mocks.MockUsersClient
		userProc    *mocks2.MockUserProcessor
	}
	tests := []struct {
		name    string
		req     *StageProcessorRequest
		mocks   func(*mockStruct)
		want    *StageProcessorResponse
		wantErr error
	}{
		{
			name: "user country is india and error in checking whitelisting",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId: actor,
					UserId:  userId,
				},
			},
			mocks: func(m *mockStruct) {
				m.obfClient.EXPECT().ObfuscatePII(ctx, &obfuscator.ObfuscatePIIRequest{
					Pii: &types.Identifier{
						IdType: types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{
							PropValue: &types.IdentifierValue_IpAddress{
								IpAddress: &types.IpAddress{
									IpAddress: ip,
								},
							},
						},
					},
				}).Return(&obfuscator.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
					Token: &types.IdentifierToken{
						Token: ipToken,
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
					IdentifierValue: ipToken,
				}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
					Status:  rpc.StatusOk(),
					Address: address,
				}, nil)
				m.userProc.EXPECT().IsB2BSalaryProgramVerifiedUser(ctx, actor).Return(false, randomError)
			},
			want:    nil,
			wantErr: randomError,
		},
		{
			name: "user country is india and b2b whitelisted",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId: actor,
					UserId:  userId,
				},
			},
			mocks: func(m *mockStruct) {
				m.obfClient.EXPECT().ObfuscatePII(ctx, &obfuscator.ObfuscatePIIRequest{
					Pii: &types.Identifier{
						IdType: types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{
							PropValue: &types.IdentifierValue_IpAddress{
								IpAddress: &types.IpAddress{
									IpAddress: ip,
								},
							},
						},
					},
				}).Return(&obfuscator.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
					Token: &types.IdentifierToken{
						Token: ipToken,
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
					IdentifierValue: ipToken,
				}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
					Status:  rpc.StatusOk(),
					Address: address,
				}, nil)
				m.userProc.EXPECT().IsB2BSalaryProgramVerifiedUser(ctx, actor).Return(true, nil)
			},
			want:    nil,
			wantErr: SkipStageError,
		},
		{
			name: "user country is india and serviceable pincode",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId: actor,
					UserId:  userId,
				},
			},
			mocks: func(m *mockStruct) {
				m.obfClient.EXPECT().ObfuscatePII(ctx, &obfuscator.ObfuscatePIIRequest{
					Pii: &types.Identifier{
						IdType: types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{
							PropValue: &types.IdentifierValue_IpAddress{
								IpAddress: &types.IpAddress{
									IpAddress: ip,
								},
							},
						},
					},
				}).Return(&obfuscator.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
					Token: &types.IdentifierToken{
						Token: ipToken,
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
					IdentifierValue: ipToken,
				}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
					Status:  rpc.StatusOk(),
					Address: address,
				}, nil)
				m.userProc.EXPECT().IsB2BSalaryProgramVerifiedUser(ctx, actor).Return(false, nil)
				m.usersClient.EXPECT().GetUserDeviceProperties(gomock.Any(), &user.GetUserDevicePropertiesRequest{
					ActorId: actor,
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
					},
				}).Return(&user.GetUserDevicePropertiesResponse{
					Status: rpc.StatusOk(),
					UserDevicePropertyList: []*user.UserDeviceProperty{
						{
							DeviceProperty: types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
							PropertyValue: &types.PropertyValue{
								PropValue: &types.PropertyValue_LocationToken{
									LocationToken: locationToken,
								},
							},
						},
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
					IdentifierValue: locationToken,
				}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
					Status: rpc.StatusOk(),
					Address: &types.PostalAddress{
						PostalCode: "610001",
						RegionCode: "India",
					},
				}, nil)
			},
			want:    nil,
			wantErr: NoActionError,
		},
		{
			name: "user country is india and non serviceable pincode",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId: actor,
					UserId:  userId,
				},
			},
			mocks: func(m *mockStruct) {
				m.obfClient.EXPECT().ObfuscatePII(ctx, &obfuscator.ObfuscatePIIRequest{
					Pii: &types.Identifier{
						IdType: types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{
							PropValue: &types.IdentifierValue_IpAddress{
								IpAddress: &types.IpAddress{
									IpAddress: ip,
								},
							},
						},
					},
				}).Return(&obfuscator.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
					Token: &types.IdentifierToken{
						Token: ipToken,
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
					IdentifierValue: ipToken,
				}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
					Status:  rpc.StatusOk(),
					Address: address,
				}, nil)
				m.userProc.EXPECT().IsB2BSalaryProgramVerifiedUser(ctx, actor).Return(false, nil)
				m.usersClient.EXPECT().GetUserDeviceProperties(gomock.Any(), &user.GetUserDevicePropertiesRequest{
					ActorId: actor,
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
					},
				}).Return(&user.GetUserDevicePropertiesResponse{
					Status: rpc.StatusOk(),
					UserDevicePropertyList: []*user.UserDeviceProperty{
						{
							DeviceProperty: types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
							PropertyValue: &types.PropertyValue{
								PropValue: &types.PropertyValue_LocationToken{
									LocationToken: locationToken,
								},
							},
						},
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
					IdentifierValue: locationToken,
				}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
					Status: rpc.StatusOk(),
					Address: &types.PostalAddress{
						PostalCode: "713204",
					},
				}, nil)
			},
			want: &StageProcessorResponse{
				NextAction:     getLocationBlockErrorView(),
				AnalyticsProps: map[string]string{"block_reason": "resident user is outside india", "country_name": ""},
			},
			wantErr: nil,
		},
		{
			name: "user country not india",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId: actor,
				},
			},
			mocks: func(m *mockStruct) {
				m.obfClient.EXPECT().ObfuscatePII(ctx, &obfuscator.ObfuscatePIIRequest{
					Pii: &types.Identifier{
						IdType: types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{
							PropValue: &types.IdentifierValue_IpAddress{
								IpAddress: &types.IpAddress{
									IpAddress: ip,
								},
							},
						},
					},
				}).Return(&obfuscator.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
					Token: &types.IdentifierToken{
						Token: ipToken,
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
					IdentifierValue: ipToken,
				}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
					Status:  rpc.StatusOk(),
					Address: address2,
				}, nil)
			},
			want: &StageProcessorResponse{
				NextAction:     deeplink2.NewErrorFullScreen(context.Background(), error2.OnbErrCountryCheck),
				AnalyticsProps: map[string]string{"block_reason": "resident user is outside india", "country_name": address2.GetRegionCode()},
			},
			wantErr: nil,
		},
		{
			name: "private ip",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId: actor,
				},
			},
			mocks: func(m *mockStruct) {
				m.obfClient.EXPECT().ObfuscatePII(ctx, &obfuscator.ObfuscatePIIRequest{
					Pii: &types.Identifier{
						IdType: types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{
							PropValue: &types.IdentifierValue_IpAddress{
								IpAddress: &types.IpAddress{
									IpAddress: ip,
								},
							},
						},
					},
				}).Return(&obfuscator.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
					Token: &types.IdentifierToken{
						Token: ipToken,
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
					IdentifierValue: ipToken,
				}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
					Status: rpc.NewStatus(uint32(userLocationPb.FetchAndStoreAddressForIdentifierResponse_PRIVATE_IP), "", ""),
				}, nil)
			},
			want: &StageProcessorResponse{
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
				},
			},
			wantErr: nil,
		},
		{
			name: "error encountered while getting ip, not blocking user",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId: actor,
				},
			},
			mocks: func(m *mockStruct) {
				m.obfClient.EXPECT().ObfuscatePII(ctx, &obfuscator.ObfuscatePIIRequest{
					Pii: &types.Identifier{
						IdType: types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{
							PropValue: &types.IdentifierValue_IpAddress{
								IpAddress: &types.IpAddress{
									IpAddress: ip,
								},
							},
						},
					},
				}).Return(&obfuscator.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
					Token: &types.IdentifierToken{
						Token: ipToken,
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
					IdentifierValue: ipToken,
				}).Return(nil, epifierrors.ErrInvalidSQL)
			},
			want:    nil,
			wantErr: NoActionSkipStatusUpdateError,
		},
		{
			name: "could not find address for ip, not blocking user",
			req: &StageProcessorRequest{
				Onb: &onbPb.OnboardingDetails{
					ActorId: actor,
				},
			},
			mocks: func(m *mockStruct) {
				m.obfClient.EXPECT().ObfuscatePII(ctx, &obfuscator.ObfuscatePIIRequest{
					Pii: &types.Identifier{
						IdType: types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{
							PropValue: &types.IdentifierValue_IpAddress{
								IpAddress: &types.IpAddress{
									IpAddress: ip,
								},
							},
						},
					},
				}).Return(&obfuscator.ObfuscatePIIResponse{
					Status: rpc.StatusOk(),
					Token: &types.IdentifierToken{
						Token: ipToken,
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
					IdentifierValue: ipToken,
				}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				m.userProc.EXPECT().IsB2BSalaryProgramVerifiedUser(ctx, actor).Return(false, nil)
				m.usersClient.EXPECT().GetUserDeviceProperties(gomock.Any(), &user.GetUserDevicePropertiesRequest{
					ActorId: actor,
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
					},
				}).Return(&user.GetUserDevicePropertiesResponse{
					Status: rpc.StatusOk(),
					UserDevicePropertyList: []*user.UserDeviceProperty{
						{
							DeviceProperty: types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
							PropertyValue: &types.PropertyValue{
								PropValue: &types.PropertyValue_LocationToken{
									LocationToken: locationToken,
								},
							},
						},
					},
				}, nil)
				m.locClient.EXPECT().FetchAndStoreAddressForIdentifier(gomock.Any(), &userLocationPb.FetchAndStoreAddressForIdentifierRequest{
					IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
					IdentifierValue: locationToken,
				}).Return(&userLocationPb.FetchAndStoreAddressForIdentifierResponse{
					Status: rpc.StatusOk(),
					Address: &types.PostalAddress{
						PostalCode: "610001",
						RegionCode: "India",
					},
				}, nil)
			},
			want:    nil,
			wantErr: SkipStageError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			locClient := locMock.NewMockLocationClient(ctr)
			obfClient := obfMock.NewMockObfuscatorClient(ctr)
			usersClient := mocks.NewMockUsersClient(ctr)
			userProc := mocks2.NewMockUserProcessor(ctr)

			s := NewLocationCheckStage(locClient, obfClient, userProc, usersClient)
			if tt.mocks != nil {
				tt.mocks(&mockStruct{
					locClient:   locClient,
					obfClient:   obfClient,
					usersClient: usersClient,
					userProc:    userProc,
				})
			}

			got, err := s.StageProcessor(ctx, tt.req)
			assert.Equal(t, tt.wantErr, err)
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("StageProcessor - LocationCheck value mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
