package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	onbErr "github.com/epifi/gamma/user/onboarding/pkg/error"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/bankcust"
	mockBankCust "github.com/epifi/gamma/api/bankcust/mocks"
	"github.com/epifi/gamma/api/kyc"
	mockKyc "github.com/epifi/gamma/api/kyc/mocks"
	"github.com/epifi/gamma/api/kyc/vkyc"
	mockVkyc "github.com/epifi/gamma/api/kyc/vkyc/mocks"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	mockUserGroup "github.com/epifi/gamma/api/user/group/mocks"
	"github.com/epifi/gamma/api/user/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/pkg/onboarding"
)

func TestBKYC_StageProcessor(t *testing.T) {
	t.Parallel()
	type mockStruct struct {
		groupClient *mockUserGroup.MockGroupClient
		kycClient   *mockKyc.MockKycClient
		userClient  *mocks.MockUsersClient
		bcClient    *mockBankCust.MockBankCustomerServiceClient
		vkycClient  *mockVkyc.MockVKYCClient
	}
	type args struct {
		ctx context.Context
		req *StageProcessorRequest
	}
	var (
		minUser = &userPb.MinimalUser{
			Id: "user-id",
			ProfileEssentials: &userPb.ProfileEssentials{
				PhoneNumber: &commontypes.PhoneNumber{
					CountryCode:    91,
					NationalNumber: **********,
				},
				Email: "<EMAIL>",
			},
		}
		onbDetailsBkycFix = &onbPb.OnboardingDetails{
			ActorId: "actor-id",
			StageDetails: &onbPb.StageDetails{
				StageMapping: map[string]*onbPb.StageInfo{},
			},
			UserId: "user-id",
		}
		onbDetailsBkycFix1 = &onbPb.OnboardingDetails{
			ActorId: "actor-id",
			StageDetails: &onbPb.StageDetails{
				StageMapping: map[string]*onbPb.StageInfo{},
			},
			UserId: "user-id",
			StageMetadata: &onbPb.StageMetadata{
				LatestDedupeStatus: customer.DedupeStatus_CUSTOMER_EXISTS_PARTIAL_KYC,
			},
		}
		onbDetailsCCPassed = &onbPb.OnboardingDetails{
			ActorId: "actor-id",
			StageDetails: &onbPb.StageDetails{
				StageMapping: map[string]*onbPb.StageInfo{
					onbPb.OnboardingStage_CUSTOMER_CREATION.String(): {
						State: onbPb.OnboardingState_SUCCESS,
					},
				},
			},
			UserId: "user-id",
		}
		signatureImage = "signatureImage"
	)
	tests := map[string]struct {
		args    args
		want    *StageProcessorResponse
		mocks   func(args args, mocks *mockStruct)
		wantErr error
	}{
		"bkyc is errored with dob mismatch": {
			args: args{
				ctx: context.TODO(),
				req: &StageProcessorRequest{
					Onb: onbDetailsBkycFix,
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewErrorFullScreen(context.TODO(), &onbErr.ErrorScreenOpts{
					Title:          constant.KYCDOBMismatchTitle,
					Subtitle:       constant.KYCDOBMismatchSubtitle,
					HeaderImageURL: constant.GenericBlockingIcon,
				}),
			},
			mocks: func(args args, mocks *mockStruct) {
				mocks.vkycClient.EXPECT().GetVKYCSummary(gomock.Any(), &vkyc.GetVKYCSummaryRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&vkyc.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkyc.VKYCRecord{
						VkycSummary: &vkyc.VKYCSummary{
							Status: vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REGISTERED,
						},
					},
				}, nil)
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.req.GetOnb().GetVendor(),
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED), "")(),
				}, nil)
				mocks.userClient.EXPECT().GetMinimalUser(gomock.Any(), &userPb.GetMinimalUserRequest{
					Identifier: &userPb.GetMinimalUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(&userPb.GetMinimalUserResponse{
					MinimalUser: minUser,
					Status:      rpc.StatusOk(),
				}, nil)
				mocks.groupClient.EXPECT().CheckMapping(gomock.Any(), &group.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_BKYC,
					IdentifierValue: &group.IdentifierValue{
						Identifier: &group.IdentifierValue_PhoneNumber{
							PhoneNumber: minUser.GetProfileEssentials().GetPhoneNumber(),
						},
					},
				}).Return(&group.CheckMappingResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mocks.groupClient.EXPECT().CheckMapping(gomock.Any(), &group.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_BKYC,
					IdentifierValue: &group.IdentifierValue{
						Identifier: &group.IdentifierValue_Email{
							Email: minUser.GetProfileEssentials().GetEmail(),
						},
					},
				}).Return(&group.CheckMappingResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mocks.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&kyc.CheckKYCStatusResponse{
					Status:                            rpc.StatusOk(),
					KycType:                           kyc.KycType_BKYC,
					KycStatus:                         kyc.KycStatus_ERRORED,
					KycState:                          kyc.KYCState_BKYC_FAILED,
					FailureReason:                     kyc.FailureType_DOB_MISMATCH,
					DataValidationMaxRetries:          false,
					DataValidationMaxRetriesCount:     3,
					DataValidationCurrentRetriesCount: 0,
				}, nil)
			},
			wantErr: nil,
		},
		"kyc is expired for whitelisted user": {
			args: args{
				ctx: context.Background(),
				req: &StageProcessorRequest{
					Onb: onbDetailsBkycFix,
				},
			},
			want: &StageProcessorResponse{
				NextAction: getKYCSelectionDeeplink(),
			},
			mocks: func(args args, mocks *mockStruct) {
				mocks.vkycClient.EXPECT().GetVKYCSummary(gomock.Any(), &vkyc.GetVKYCSummaryRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&vkyc.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkyc.VKYCRecord{
						VkycSummary: &vkyc.VKYCSummary{
							Status: vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REGISTERED,
						},
					},
				}, nil)
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.req.GetOnb().GetVendor(),
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED), "")(),
				}, nil)
				mocks.userClient.EXPECT().GetMinimalUser(gomock.Any(), &userPb.GetMinimalUserRequest{
					Identifier: &userPb.GetMinimalUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(&userPb.GetMinimalUserResponse{
					MinimalUser: minUser,
					Status:      rpc.StatusOk(),
				}, nil)
				mocks.groupClient.EXPECT().CheckMapping(gomock.Any(), &group.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_BKYC,
					IdentifierValue: &group.IdentifierValue{
						Identifier: &group.IdentifierValue_PhoneNumber{
							PhoneNumber: minUser.GetProfileEssentials().GetPhoneNumber(),
						},
					},
				}).Return(&group.CheckMappingResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mocks.groupClient.EXPECT().CheckMapping(gomock.Any(), &group.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_BKYC,
					IdentifierValue: &group.IdentifierValue{
						Identifier: &group.IdentifierValue_Email{
							Email: minUser.GetProfileEssentials().GetEmail(),
						},
					},
				}).Return(&group.CheckMappingResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mocks.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&kyc.CheckKYCStatusResponse{
					Status:    rpc.StatusOk(),
					KycType:   kyc.KycType_BKYC,
					KycStatus: kyc.KycStatus_EXPIRED,
					KycState:  kyc.KYCState_BKYC_SUCCESS,
				}, nil)
			},
			wantErr: nil,
		},
		"skip bkyc if user is partial kyc": {
			args: args{
				ctx: context.Background(),
				req: &StageProcessorRequest{
					Onb: onbDetailsBkycFix1,
				},
			},
			mocks:   func(args args, mocks *mockStruct) {},
			want:    nil,
			wantErr: SkipStageError,
		},
		"user not found in any mapping": {
			args: args{
				ctx: context.Background(),
				req: &StageProcessorRequest{
					Onb: onbDetailsBkycFix,
				},
			},
			want: nil,
			mocks: func(args args, mocks *mockStruct) {
				mocks.vkycClient.EXPECT().GetVKYCSummary(gomock.Any(), &vkyc.GetVKYCSummaryRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&vkyc.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkyc.VKYCRecord{
						VkycSummary: &vkyc.VKYCSummary{
							Status: vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REGISTERED,
						},
					},
				}, nil)
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.req.GetOnb().GetVendor(),
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED), "")(),
				}, nil)
				mocks.userClient.EXPECT().GetMinimalUser(gomock.Any(), &userPb.GetMinimalUserRequest{
					Identifier: &userPb.GetMinimalUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(&userPb.GetMinimalUserResponse{
					MinimalUser: minUser,
					Status:      rpc.StatusOk(),
				}, nil)
				mocks.groupClient.EXPECT().CheckMapping(gomock.Any(), &group.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_BKYC,
					IdentifierValue: &group.IdentifierValue{
						Identifier: &group.IdentifierValue_PhoneNumber{
							PhoneNumber: minUser.GetProfileEssentials().GetPhoneNumber(),
						},
					},
				}).Return(&group.CheckMappingResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mocks.groupClient.EXPECT().CheckMapping(gomock.Any(), &group.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_BKYC,
					IdentifierValue: &group.IdentifierValue{
						Identifier: &group.IdentifierValue_Email{
							Email: minUser.GetProfileEssentials().GetEmail(),
						},
					},
				}).Return(&group.CheckMappingResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
			wantErr: SkipStageError,
		},
		"BKYC in review": {
			args: args{
				ctx: context.Background(),
				req: &StageProcessorRequest{
					Onb: onbDetailsBkycFix,
				},
			},
			want: &StageProcessorResponse{
				NextAction: onboarding.GetKYCRecordDeeplink([]byte(signatureImage)),
			},
			mocks: func(args args, mocks *mockStruct) {
				mocks.vkycClient.EXPECT().GetVKYCSummary(gomock.Any(), &vkyc.GetVKYCSummaryRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&vkyc.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkyc.VKYCRecord{
						VkycSummary: &vkyc.VKYCSummary{
							Status: vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REGISTERED,
						},
					},
				}, nil)
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.req.GetOnb().GetVendor(),
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED), "")(),
				}, nil)
				mocks.userClient.EXPECT().GetMinimalUser(gomock.Any(), &userPb.GetMinimalUserRequest{
					Identifier: &userPb.GetMinimalUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(&userPb.GetMinimalUserResponse{
					MinimalUser: minUser,
					Status:      rpc.StatusOk(),
				}, nil)
				mocks.groupClient.EXPECT().CheckMapping(gomock.Any(), &group.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_BKYC,
					IdentifierValue: &group.IdentifierValue{
						Identifier: &group.IdentifierValue_PhoneNumber{
							PhoneNumber: minUser.GetProfileEssentials().GetPhoneNumber(),
						},
					},
				}).Return(&group.CheckMappingResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mocks.groupClient.EXPECT().CheckMapping(gomock.Any(), &group.CheckMappingRequest{
					UserGroup: commontypes.UserGroup_BKYC,
					IdentifierValue: &group.IdentifierValue{
						Identifier: &group.IdentifierValue_Email{
							Email: minUser.GetProfileEssentials().GetEmail(),
						},
					},
				}).Return(&group.CheckMappingResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mocks.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&kyc.CheckKYCStatusResponse{
					Status:    rpc.StatusOk(),
					KycType:   kyc.KycType_BKYC,
					KycStatus: kyc.KycStatus_IN_PROGRESS,
					KycState:  kyc.KYCState_BKYC_IN_REVIEW,
				}, nil)
				mocks.kycClient.EXPECT().GetKYCRecord(gomock.Any(), &kyc.GetKYCRecordRequest{
					ActorId:                 args.req.GetOnb().GetActorId(),
					SignImage:               true,
					ForceBkycInReviewRecord: true,
				}).Return(&kyc.GetKYCRecordResponse{
					Status: rpc.StatusOk(),
					KycRecord: &kyc.KYCRecord{
						SignImage: signatureImage,
					},
				}, nil)
			},
			wantErr: nil,
		},
		"customer creation is success": {
			args: args{
				ctx: context.Background(),
				req: &StageProcessorRequest{
					Onb: onbDetailsCCPassed,
				},
			},
			want: nil,
			mocks: func(args args, mocks *mockStruct) {
				mocks.vkycClient.EXPECT().GetVKYCSummary(gomock.Any(), &vkyc.GetVKYCSummaryRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&vkyc.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkyc.VKYCRecord{
						VkycSummary: &vkyc.VKYCSummary{
							Status: vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REGISTERED,
						},
					},
				}, nil)
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.req.GetOnb().GetVendor(),
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: SkipStageError,
		},
		"vkyc is approved": {
			args: args{
				ctx: context.TODO(),
				req: &StageProcessorRequest{
					Onb: onbDetailsCCPassed,
				},
			},
			want: nil,
			mocks: func(args args, mocks *mockStruct) {
				mocks.vkycClient.EXPECT().GetVKYCSummary(gomock.Any(), &vkyc.GetVKYCSummaryRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&vkyc.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkyc.VKYCRecord{
						VkycSummary: &vkyc.VKYCSummary{
							Status: vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED,
						},
					},
				}, nil)
			},
			wantErr: SkipStageError,
		},
	}
	for name, tt := range tests {
		name := name
		tt := tt
		t.Run(name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			mockKycClient := mockKyc.NewMockKycClient(ctrl)
			mockUsersClient := mocks.NewMockUsersClient(ctrl)
			mockGroupClient := mockUserGroup.NewMockGroupClient(ctrl)
			mockBcClient := mockBankCust.NewMockBankCustomerServiceClient(ctrl)
			mockVkycClient := mockVkyc.NewMockVKYCClient(ctrl)
			b := &BKYC{
				groupClient: mockGroupClient,
				kycClient:   mockKycClient,
				userClient:  mockUsersClient,
				bcClient:    mockBcClient,
				vkycClient:  mockVkycClient,
			}
			tt.mocks(tt.args, &mockStruct{
				groupClient: mockGroupClient,
				kycClient:   mockKycClient,
				userClient:  mockUsersClient,
				bcClient:    mockBcClient,
				vkycClient:  mockVkycClient,
			})
			got, err := b.StageProcessor(tt.args.ctx, tt.args.req)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Unexpected error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			assert.Equalf(t, tt.want, got, "StageProcessor(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}
