package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	nriPkg "github.com/epifi/gamma/pkg/onboarding/nri"
	"github.com/epifi/gamma/user/onboarding/helper"
	deeplink2 "github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	"github.com/epifi/gamma/api/user/onboarding"
)

const (
	IndiaLowercase = "india"
)

type LocationCheckStage struct {
	locClient  location.LocationClient
	obfClient  obfuscator.ObfuscatorClient
	userProc   helper.UserProcessor
	userClient user.UsersClient
}

func NewLocationCheckStage(locClient location.LocationClient, obfClient obfuscator.ObfuscatorClient,
	userProc helper.UserProcessor, userClient user.UsersClient) *LocationCheckStage {
	return &LocationCheckStage{
		locClient:  locClient,
		obfClient:  obfClient,
		userProc:   userProc,
		userClient: userClient,
	}
}

var privateIpErr = errors.New("given ip is private")

func (s *LocationCheckStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	country, err := getUserCountryFromIp(ctx, s.locClient, s.obfClient)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return handleGetUserCountryFromIpError(ctx, err)
	}

	if !isCountryIndia(country) && country != "" {
		logger.Info(ctx, "User Blocked from on-boarding because they're not in India.", zap.String(logger.STATUS, country))
		if nriPkg.IsAllowedCountryByName(country) {
			return &StageProcessorResponse{
				NextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_ACCOUNT_DELETION_ACKNOWLEDGEMENT,
					ScreenOptions: &deeplinkPb.Deeplink_AccountDeletionAcknowledgementScreenOptions{
						AccountDeletionAcknowledgementScreenOptions: &deeplinkPb.AccountDeletionAcknowledgementScreenOptions{
							ImageUrl: error2.NonIndianIPAddressNROnbPrompt.HeaderImageURL,
							Title:    error2.NonIndianIPAddressNROnbPrompt.Title,
							Subtitle: error2.NonIndianIPAddressNROnbPrompt.Subtitle,
							Cta: &deeplinkPb.Cta{
								Text: "Restart with new number",
							},
							DeletionReason: user.DeletionDetails_DELETION_REASON_NRI_WITH_INDIAN_PH_NUM.String(),
						},
					},
				},
				AnalyticsProps: map[string]string{
					"country_name": country,
					"block_reason": "resident user is outside india, in NRI allowed country",
				},
			}, nil
		}
		return &StageProcessorResponse{
			NextAction: deeplink2.NewErrorFullScreen(ctx, error2.OnbErrCountryCheck),
			AnalyticsProps: map[string]string{
				"country_name": country,
				"block_reason": "resident user is outside india",
			},
		}, nil
	}
	isB2B, err := s.isB2BUser(ctx, req.GetOnb().GetActorId())
	if err != nil {
		return nil, err
	}
	if isB2B {
		logger.Info(ctx, "skipping further location checks as B2B salary user")
		return nil, SkipStageError
	}
	isUserInServiceablePinCodes, analyticsProps, err := s.isUserInServiceablePinCode(ctx, req.GetOnb())
	if err != nil {
		return nil, err
	}
	if !isUserInServiceablePinCodes {
		return &StageProcessorResponse{
			NextAction:     getLocationBlockErrorView(),
			AnalyticsProps: analyticsProps,
		}, nil
	}
	// If country is not found by IP, we do not penalise and allow user through.
	if country == "" {
		return nil, SkipStageError
	}
	return nil, NoActionError
}

func (s *LocationCheckStage) isB2BUser(ctx context.Context, actorId string) (bool, error) {
	return s.userProc.IsB2BSalaryProgramVerifiedUser(ctx, actorId)
}

func (s *LocationCheckStage) isUserInServiceablePinCode(ctx context.Context, onb *onboarding.OnboardingDetails) (bool, map[string]string, error) {
	var locToken string
	udpResp, errResp := s.userClient.GetUserDeviceProperties(ctx, &user.GetUserDevicePropertiesRequest{
		ActorId: onb.GetActorId(),
		PropertyTypes: []types.DeviceProperty{
			types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN,
		},
	})
	if err := epifigrpc.RPCError(udpResp, errResp); err != nil && !udpResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in fetching location token", zap.Error(err))
		return false, nil, err
	}
	for _, userDeviceProperty := range udpResp.GetUserDevicePropertyList() {
		if userDeviceProperty.GetDeviceProperty() == types.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN {
			locToken = userDeviceProperty.GetPropertyValue().GetLocationToken()
		}
	}
	// If location token not present in user device properties, we fetch from onboarding details.
	if locToken == "" || locToken == epificontext.UnknownToken {
		logger.Info(ctx, "fetching location token from onboarding details")
		locToken = onb.GetStageDetails().GetStageMapping()[onboarding.OnboardingStage_TNC_CONSENT.String()].GetLocationToken()
	}
	if locToken == "" || locToken == epificontext.UnknownToken {
		logger.Info(ctx, "fetching location token from context")
		locToken = epificontext.DeviceLocationTokenFromContext(ctx)
	}
	if locToken == "" || locToken == epificontext.UnknownToken {
		logger.Info(ctx, "location token empty")
		return false, map[string]string{
			"block_reason": "location token not found",
		}, nil
	}
	addrResp, errResp := s.locClient.FetchAndStoreAddressForIdentifier(ctx, &location.FetchAndStoreAddressForIdentifierRequest{
		IdentifierType:  types.IdentifierType_IDENTIFIER_TYPE_LOCATION,
		IdentifierValue: locToken,
	})
	if err := epifigrpc.RPCError(addrResp, errResp); err != nil {
		if rpc.StatusFromError(err).IsRecordNotFound() {
			return false, map[string]string{
				"block_reason":   "address not found for location token",
				"location_token": locToken,
			}, nil
		}
		logger.Error(ctx, "error in fetching address by location token", zap.Error(err))
		return false, nil, err
	}

	if !isCountryIndia(addrResp.GetAddress().GetRegionCode()) {
		logger.Info(ctx, "users country is not india in location check stage", zap.String(logger.COUNTRY, addrResp.GetAddress().GetRegionCode()))
		return false, map[string]string{
			"block_reason": "resident user is outside india",
			"country_name": addrResp.GetAddress().GetRegionCode(),
		}, nil
	}

	if lo.Contains(serviceablePincodes, addrResp.GetAddress().GetPostalCode()) {
		logger.Info(ctx, fmt.Sprintf("user from serviceable pin code. Not blocking. Pincode: %v", addrResp.GetAddress().GetPostalCode()))
		return true, nil, nil
	}
	logger.Info(ctx, fmt.Sprintf("user not from serviceable pin code. Blocking. Pincode: %v", addrResp.GetAddress().GetPostalCode()))
	return false, map[string]string{
		"block_reason": "unserviceable postal code",
		"postal_code":  addrResp.GetAddress().GetPostalCode(),
	}, nil
}

func isCountryIndia(country string) bool {
	return strings.EqualFold(country, IndiaLowercase)
}

func getLocationBlockErrorView() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &deeplink.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &deeplink.DetailedErrorViewScreenOptions{
				Title:            error2.OnbErrNonServiceablePincodes.Title,
				Subtitle:         error2.OnbErrNonServiceablePincodes.Subtitle,
				ImageUrl:         error2.OnbErrNonServiceablePincodes.HeaderImageURL,
				HasFeedback:      error2.OnbErrNonServiceablePincodes.HasFeedback,
				ScreenIdentifier: error2.OnbErrNonServiceablePincodes.Category.String(),
				TitleText:        commontypes.GetTextFromHtmlStringFontColourFontStyle(error2.OnbErrNonServiceablePincodes.Title, "#313234", commontypes.FontStyle_HEADLINE_XL),
				SubtitleText:     commontypes.GetTextFromHtmlStringFontColourFontStyle(error2.OnbErrNonServiceablePincodes.Subtitle, "#929599", commontypes.FontStyle_BODY_S),
				Image: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: error2.OnbErrNonServiceablePincodes.HeaderImageURL,
							},
							Properties: &commontypes.VisualElementProperties{
								Height: 80,
								Width:  80,
							},
							ImageType: commontypes.ImageType_PNG,
						},
					},
				},
				EventProperties: map[string]string{
					"LOCATION_CHECK": "BLOCKED",
				},
				FaqInfo: &deeplink.FaqInfo{
					FaqIcon: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: constant.CTAFAQIcon,
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  32,
									Height: 32,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
					FaqAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
					},
				},
			},
		},
	}
}
