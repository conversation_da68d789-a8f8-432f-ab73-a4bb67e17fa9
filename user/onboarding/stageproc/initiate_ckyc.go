package stageproc

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	config "github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	"go.uber.org/zap"
)

type InitCkycStage struct {
	kycClient     kyc.KycClient
	onboardingDao dao.OnboardingDao
	conf          *config.OnboardingConfig
	time          datetime.Time
	userProc      helper.UserProcessor
}

func NewInitCkycStage(kycClient kyc.KycClient, onboardingDao dao.OnboardingDao, conf *config.OnboardingConfig,
	time datetime.Time, userProc helper.UserProcessor) *InitCkycStage {
	return &InitCkycStage{
		kycClient:     kycClient,
		onboardingDao: onboardingDao,
		conf:          conf,
		time:          time,
		userProc:      userProc,
	}
}

func (s *InitCkycStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	onb := req.Onb
	response := &StageProcessorResponse{}

	if skipCKYCForDedupeUsers(onb) {
		return nil, SkipStageError
	}

	// Get KYC Status
	kycStatusRes, err := kycStatus(ctx, s.kycClient, onb.GetActorId())
	if errors.Is(err, epifierrors.ErrRecordNotFound) || kycStatusRes.GetKycStatus() == kyc.KycStatus_EXPIRED {
		err2 := tryInitCKYC(ctx, s.kycClient, onb.GetActorId(), s.userProc)
		switch {
		// we should have been able to initiate ckyc; fetch again to get latest details
		case err2 == nil:
			_, err = kycStatus(ctx, s.kycClient, onb.GetActorId())

		// continue with existing flow. return screen to get pan and dob
		case errors.Is(err2, ErrDOBPANNotFound):
			logger.Info(ctx, "dob pan not found for init ckyc")

		default:
			return response, err2
		}
	}
	if dl := mapKYCCheckStatusErrorToAction(ctx, err); dl != nil {
		response.NextAction = dl
		return response, nil
	}
	if err != nil {
		return response, err
	}

	return response, NoActionError
}

// blockUserFromOnboarding blocks all the users from onboarding based on time range
// by returning an error screen. e.g. usecase - planned federal bank downtime.
func blockUserFromOnboarding(ctx context.Context, timeClient datetime.Time, conf *config.OnboardingConfig) (*dlPb.Deeplink, error) {
	now := timeClient.Now().In(datetime.IST)
	from, err := datetime.ParseStringTimestampProtoInLocation(constant.StandardTimeLayout, conf.BlockOnboardingFromTime(), datetime.IST)
	if err != nil {
		logger.Error(ctx, "error in parsing time", zap.String("timeString", conf.BlockOnboardingFromTime()), zap.Error(err))
		return nil, err
	}
	to, err := datetime.ParseStringTimestampProtoInLocation(constant.StandardTimeLayout, conf.BlockOnboardingTillTime(), datetime.IST)
	if err != nil {
		logger.Error(ctx, "error in parsing time", zap.String("timeString", conf.BlockOnboardingTillTime()), zap.Error(err))
		return nil, err
	}

	if now.After(from.AsTime()) && now.Before(to.AsTime()) {
		logger.Info(ctx, fmt.Sprintf("blocking user from onboarding from %v till %v", from.AsTime().In(datetime.IST), to.AsTime().In(datetime.IST)))
		return deeplink.NewErrorFullScreen(ctx, &error2.ErrorScreenOpts{
			Title:       conf.BlockOnboardingMsg(),
			HasFeedback: true,
		}), nil
	}

	return nil, nil
}
