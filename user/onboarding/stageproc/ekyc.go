package stageproc

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/api/kyc"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

type EKYCStage struct {
	kycClient           kyc.KycClient
	onboardingDao       dao.OnboardingDao
	conf                *genconf.OnboardingConfig
	inAppReferralClient inAppReferralPb.InAppReferralClient
	eventLogger         userEvents.EventLogger
	userProc            helper.UserProcessor
	kycProc             helper.IKYCHelper
}

func NewEKYCStage(kycClient kyc.KycClient, onboardingDao dao.OnboardingDao, conf *genconf.OnboardingConfig, kycProc helper.IKYCHelper,
	eventLogger userEvents.EventLogger, userProc helper.UserProcessor, inAppReferralClient inAppReferralPb.InAppReferralClient) *EKYCStage {
	return &EKYCStage{
		kycClient:           kycClient,
		onboardingDao:       onboardingDao,
		conf:                conf,
		inAppReferralClient: inAppReferralClient,
		eventLogger:         eventLogger,
		userProc:            userProc,
		kycProc:             kycProc,
	}
}

func (s *EKYCStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	onb := req.Onb
	// Get KYC Status
	res, err := s.kycProc.CheckKYCStatus(ctx, onb.GetActorId())

	// Skip if BKYC is completed
	if res.GetKycType() == kyc.KycType_BKYC && res.GetKycStatus() == kyc.KycStatus_COMPLETED {
		return nil, SkipStageError
	}
	// Checking no kyc attempt or if kyc attempt is of type CKYC
	if errors.Is(err, epifierrors.ErrRecordNotFound) || res.GetKycType() != kyc.KycType_EKYC {
		dl, errEKYC := StartEkycDLGen(ctx, s.inAppReferralClient, onb.GetActorId(), s.conf, s.kycProc, onb.GetFeature())()
		return &StageProcessorResponse{
			NextAction: dl,
		}, errEKYC
	}
	if err != nil {
		return nil, err
	}
	logger.Debug(ctx, fmt.Sprintf("EKYC CheckKYCStatus response : %v", res))

	dl, errProcessEKYC := s.processEKYC(ctx, res, StartEkycDLGen(ctx, s.inAppReferralClient, onb.GetActorId(), s.conf, s.kycProc, onb.GetFeature()),
		onb, s.onboardingDao)
	if IsOrchestratorAction(errProcessEKYC) {
		validateParentNamesAsync(ctx, s.userProc, s.kycClient, onb.GetActorId())
	}
	return &StageProcessorResponse{
		NextAction: dl,
	}, errProcessEKYC
}

func (s *EKYCStage) processEKYC(ctx context.Context, kycDetails *kyc.CheckKYCStatusResponse, ekycGen DeeplinkGenerator, onb *onbPb.OnboardingDetails,
	onbDao dao.OnboardingDao) (dl *dlPb.Deeplink, err error) {

	var (
		failureType kyc.FailureType
	)

	switch kycDetails.KycStatus {
	// success terminal state
	case kyc.KycStatus_COMPLETED:
		s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), false, "")
		return nil, NoActionError
	// failure terminal state
	case kyc.KycStatus_ERRORED:
		dl = processDOBMismatchFailures(ctx, kycDetails)
		if dl == nil {
			switch kycDetails.GetFailureReason() {
			case kyc.FailureType_EMPTY_ADDRESS:
				dl = deeplink.NewErrorFullScreen(ctx, error2.OnbErrKYCEmptyAddress)
			case kyc.FailureType_MINOR_AGE:
				dl = deeplink.NewErrorFullScreen(ctx, error2.OnbErrKYCMinorAge)
			case kyc.FailureType_MINIMUM_AGE_VALIDATION_FAILURE:
				dl = deeplink.NewErrorFullScreen(ctx, error2.MinimumAgeForKYC)
			case kyc.FailureType_NAME_DOB_VALIDATION_FAILURE:
				dl = deeplink.NewErrorFullScreen(ctx, error2.EKYCNameDobValidationError)
			case kyc.FailureType_AADHAAR_MOBILE_MISMATCH:
				logger.Info(ctx, "KYC failed with reason: AADHAAR_MOBILE_MISMATCH in EKYC stage")
				dl = deeplink.AadharNumberMismatchAccountDeletionScreen()
			default:
				logger.Error(ctx, fmt.Sprintf("KYC failed with reason: %v", kycDetails.GetFailureReason()))
				dl = deeplink.NewErrorFullScreen(ctx, error2.OnbErrKYCGeneric)
			}
		}
		if !kycDetails.GetDataValidationMaxRetries() {
			failureType = kycDetails.GetFailureReason()
		} else {
			// Retries exhausted for the failure
			// We will update failure reason to unspecified in case of retries exhausted as it is a permanent failure
			// and user does not need to be nudged in that case.
			failureType = kyc.FailureType_REASON_UNSPECIFIED
		}
		_ = updateKYCStageState(ctx, onb, onbPb.OnboardingStage_EKYC, onbPb.OnboardingState_FAILURE, onbDao)
		updateKYCFailureReason(ctx, onb, failureType, onbDao, kycDetails)
		s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), true, userEvents.BlockReasonTerminal)
	case kyc.KycStatus_EXPIRED:
		dl, err = ekycGen()

	// non terminal state
	case kyc.KycStatus_IN_PROGRESS:
		dl, err = ekycGen()

	default:
		logger.Error(ctx, "Onb kyc invalid status in process ekyc",
			zap.String("status", kycDetails.KycStatus.String()),
		)
		err = fmt.Errorf("invalid kyc status: %v", kycDetails.KycStatus)
	}

	return dl, err
}
