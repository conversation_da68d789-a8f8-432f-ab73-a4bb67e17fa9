package stageproc

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/api/bankcust"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	panPb "github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	vgPbCustomer "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	vgPanPb "github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/pkg/obfuscator"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	onbDao "github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

type PreCustomerCreationCheckStage struct {
	onbDao      onbDao.OnboardingDao
	userClient  user.UsersClient
	kycClient   kycPb.KycClient
	time        datetime.Time
	dedupeProc  helper.IDedupeHelper
	eventLogger userEvents.EventLogger
	userProc    helper.UserProcessor
	userConf    *genconf.Config
	vkycClient  vkycPb.VKYCClient
	bcClient    bankcust.BankCustomerServiceClient
	conf        *genconf.OnboardingConfig
	panClient   panPb.PanClient
	kycProc     helper.IKYCHelper
	vgPanClient vgPanPb.PANClient
}

func NewPreCustomerCreationCheckStage(onbDao onbDao.OnboardingDao, userClient user.UsersClient,
	kycClient kycPb.KycClient, time datetime.Time, dedupeProc helper.IDedupeHelper, eventLogger userEvents.EventLogger,
	userProc helper.UserProcessor, userConf *genconf.Config, vkycClient vkycPb.VKYCClient, bcClient bankcust.BankCustomerServiceClient,
	conf *genconf.OnboardingConfig, panClient panPb.PanClient, kycProc helper.IKYCHelper, vgPanClient vgPanPb.PANClient) *PreCustomerCreationCheckStage {
	return &PreCustomerCreationCheckStage{
		onbDao:      onbDao,
		userClient:  userClient,
		kycClient:   kycClient,
		time:        time,
		dedupeProc:  dedupeProc,
		eventLogger: eventLogger,
		userProc:    userProc,
		userConf:    userConf,
		vkycClient:  vkycClient,
		bcClient:    bcClient,
		conf:        conf,
		panClient:   panClient,
		kycProc:     kycProc,
		vgPanClient: vgPanClient,
	}
}

var (
	errNoAction = errors.New("current check does not need any action")
)

func (s *PreCustomerCreationCheckStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb   = req.GetOnb()
		stage = onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK
	)

	// check if another user with same pan has device registered with federal
	isDedupePan, maskedPhoneNumber, err := s.isOnbRestrictedOnPan(ctx, onb)
	if err != nil {
		return nil, err
	}

	if isDedupePan {
		_, _ = updateStatus(ctx, s.onbDao, onb.GetOnboardingId(), stage, onbPb.OnboardingState_FAILURE)
		s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), true, userEvents.BlockReasonTerminal)
		_ = updateStageMetadata(ctx, s.onbDao, onb.GetOnboardingId(), getStageMetadataWithPanUniquenessFailure(onb))
		return &StageProcessorResponse{
			NextAction: &dlPb.Deeplink{
				Screen: dlPb.Screen_ACCOUNT_DELETION_ACKNOWLEDGEMENT,
				ScreenOptions: &dlPb.Deeplink_AccountDeletionAcknowledgementScreenOptions{
					AccountDeletionAcknowledgementScreenOptions: &dlPb.AccountDeletionAcknowledgementScreenOptions{
						ImageUrl: "https://epifi-icons.pointz.in/onboarding/caveman.png",
						Title:    constant.DeviceAlreadyRegisteredWithPan,
						Subtitle: fmt.Sprintf("The existing account is already linked to mobile number <b>%v</b> for signup. Please resume your journey again with the same phone number.", maskedPhoneNumber),
						Cta: &dlPb.Cta{
							Text: "Retry with registered mobile number",
						},
						DeletionReason: user.DeletionDetails_DELETION_REASON_DEDUPE_MISMATCH.String(),
					},
				},
			},
		}, nil
	}

	if shouldPollAgain := s.shouldPollAgain(ctx, onb); shouldPollAgain {
		return &StageProcessorResponse{
			NextAction: deeplink.NewActionToGetNextAction(),
		}, nil
	}

	if !onb.GetFeature().IsNonResidentUserOnboarding() && s.conf.Flags().EnablePanAadharCheckInPreCustomerCreationCheckStage() {
		dl, err := s.validatePanAadhaar(ctx, onb)
		if err != nil {
			logger.Error(ctx, "error in validating pan aadhaar", zap.Error(err))
			return nil, err
		}
		if dl != nil {
			return &StageProcessorResponse{
				NextAction: dl,
			}, nil
		}
	}

	inProgress, errCustomer := s.userProc.IsCustomerCreationInProgress(ctx, onb.GetActorId())
	if errCustomer != nil {
		return nil, errCustomer
	}

	if inProgress {
		return nil, SkipStageError
	}

	if onb.GetFeature() != onbPb.Feature_FEATURE_CC && s.time.Since(stageInfo(onb, onbPb.OnboardingStage_RISK_SCREENING).GetLastUpdatedAt().AsTime()) > s.userConf.Onboarding().RiskScreeningExpiry() {
		if err := s.onbDao.UpdateStageStatuses(ctx, onb.GetOnboardingId(), map[onbPb.OnboardingStage]onbPb.OnboardingState{
			onbPb.OnboardingStage_RISK_SCREENING: onbPb.OnboardingState_RESET,
		}); err != nil {
			logger.Error(ctx, "error while updating stage status for risk screening", zap.Error(err))
			return nil, err
		}
		return &StageProcessorResponse{
			NextAction: deeplink.NewActionToGetNextAction(),
		}, nil
	}
	// Pre customer creation dedupe check
	dl, err := s.dedupeCheck(ctx, onb, stage)
	if !errors.Is(err, errNoAction) {
		return &StageProcessorResponse{
			NextAction: dl,
		}, err
	}

	s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), false, "")
	return nil, NoActionError
}

// nolint: dupl
func (s *PreCustomerCreationCheckStage) validatePanAadhaar(ctx context.Context, onb *onbPb.OnboardingDetails) (*dlPb.Deeplink, error) {
	if err := s.checkPanAadhaarLinked(ctx, onb); err != nil {
		if errors.Is(err, panAadhaarNotLinkedError) {
			return deeplink.NewErrorFullScreen(ctx, error2.PanAadhaarNotLinked), nil
		}
		return nil, err
	}

	if s.conf.Flags().BlockCCUserForPANLinkage() {
		hashMismatch, checkInProgress, errHashCheck := s.checkHashedAadharValues(ctx, onb)
		if errHashCheck != nil {
			logger.Error(ctx, "failure while checking hashed aadhar values", zap.Error(errHashCheck))
			return nil, errHashCheck
		}

		if checkInProgress {
			return deeplink.NewActionToAccountCreationProgress(), nil
		}

		if hashMismatch == commontypes.BooleanEnum_TRUE {
			logger.Info(ctx, "aadhar last 4 digits hash mismatch found for user")
			return deeplink.NewErrorFullScreen(ctx, error2.OnbErrPANAadharMismatch), nil
		}
	}
	return nil, nil
}

func (s *PreCustomerCreationCheckStage) checkPanAadhaarLinked(ctx context.Context, onb *onbPb.OnboardingDetails) error {
	if s.conf.BlockOnboardingDueToUnlinkedPANAndAadhaar() {
		panResp, panErr := s.panClient.GetPANAadharLinkStatus(ctx, &panPb.GetPANAadharLinkStatusRequest{
			ActorId: onb.GetActorId(),
		})
		if rpcErr := epifigrpc.RPCError(panResp, panErr); rpcErr != nil {
			logger.Error(ctx, "got unexpected response from be", zap.Error(rpcErr))
			return rpcErr
		}
		if panResp.GetPanAadharLinkStatus() != panPb.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED {
			logger.Info(ctx, fmt.Sprintf("user's pan aadhaar is not linked %v", panResp.GetPanAadharLinkStatus()))
			return panAadhaarNotLinkedError
		}
		return nil
	}
	return nil
}

// nolint: funlen
func (s *PreCustomerCreationCheckStage) checkHashedAadharValues(ctx context.Context, onb *onbPb.OnboardingDetails) (commontypes.BooleanEnum, bool, error) {
	panAadharLinkageDetails := onb.GetPanAadharLinkageDetails()
	onbDetailsNMDOBHash := panAadharLinkageDetails.GetEkycNameDobValidationAadharDigitsHash()
	ekycNMDOBHash := onbDetailsNMDOBHash

	kycStatusResp, _ := s.kycProc.CheckKYCStatus(ctx, onb.GetActorId())
	kycNMDOBHash := kycStatusResp.GetRequestParams().GetEkycNameDobValidationAadhaarDigitsHash()
	kycNMDOBLast2DigitsHash := kycStatusResp.GetRequestParams().GetEkycNameDobValidationAadhaarLast2DigitsHash()

	if len(kycNMDOBHash) != 0 {
		if !strings.EqualFold(kycNMDOBHash, onbDetailsNMDOBHash) && onb.GetPanAadharLinkageDetails() != nil { // Update the new values in onb details
			onb.PanAadharLinkageDetails.EkycNameDobValidationAadharDigitsHash = kycNMDOBHash
			onb.PanAadharLinkageDetails.IsAadharDigitsHashMismatch = commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED
			if errUpdate := s.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, onb); errUpdate != nil {
				logger.Error(ctx, "failed while update onboarding details", zap.Error(errUpdate))
				return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, errUpdate
			}
		}
		ekycNMDOBHash = kycNMDOBHash
	}

	if panAadharLinkageDetails == nil || ekycNMDOBHash == "" {
		// TODO (Ankit): Make it blocker
		logger.Info(ctx, "pan aadhar linkage is nil or ekyc hash is empty")
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, nil
	}

	if panAadharLinkageDetails.GetIsAadharDigitsHashMismatch() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		return panAadharLinkageDetails.GetIsAadharDigitsHashMismatch(), false, nil
	}

	getUserResp, errGetUser := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: onb.GetUserId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getUserResp, errGetUser); rpcErr != nil {
		logger.Error(ctx, "failed while getting user", zap.Error(rpcErr))
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, rpcErr
	}

	panAadharValidationResp, errPanAadharValidation := s.vgPanClient.PANAadhaarValidation(ctx, &vgPanPb.PANAadhaarValidationRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		Pan: getUserResp.GetUser().GetProfile().GetPAN(),
	})
	switch {
	case panAadharValidationResp.GetStatus().IsInProgress():
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, true, nil
	case panAadharValidationResp.GetStatus().GetCode() == uint32(vgPanPb.PANAadhaarValidationResponse_PAN_AADHAAR_NOT_LINKED):
		panAadharLinkageDetails.PanAadhaarLinked = commontypes.BooleanEnum_FALSE
		// keeping the error non-blocking as it will result in another API call but will not block the user
		_ = s.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, onb)
		return commontypes.BooleanEnum_TRUE, false, nil
	default:
		if rpcErr := epifigrpc.RPCError(panAadharValidationResp, errPanAadharValidation); rpcErr != nil {
			logger.Error(ctx, "failed while getting pan validation response", zap.Error(rpcErr))
			return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, rpcErr
		}
	}

	aadharLast4Digits := strings.TrimSpace(panAadharValidationResp.GetPanAadhaarValidationResult().GetAadhaarLast4Digits())
	if len(aadharLast4Digits) != 4 {
		logger.Info(ctx, "aadhar last 4 digits are not of length 4", zap.Any("LENGTH", len(aadharLast4Digits)))
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, errors.New("aadhar last 4 digits are not numeric or not of length 4")
	}
	maskedAadhaar := panAadharValidationResp.GetPanAadhaarValidationResult().GetMaskedAadhaar()
	aadhaarLast2Digit := strings.TrimSpace(maskedAadhaar[len(maskedAadhaar)-2:])

	panValidationHashedDigits := obfuscator.Hashed(aadharLast4Digits)
	panValidationLast2DigitsHash := obfuscator.Hashed(aadhaarLast2Digit)
	hashMismatch := commontypes.BooleanEnum_TRUE
	panAadharLinkageDetails.PanAadhaarLinked = commontypes.BooleanEnum_FALSE
	if strings.EqualFold(ekycNMDOBHash, panValidationHashedDigits) || strings.EqualFold(kycNMDOBLast2DigitsHash, panValidationLast2DigitsHash) {
		hashMismatch = commontypes.BooleanEnum_FALSE
		panAadharLinkageDetails.PanAadhaarLinked = commontypes.BooleanEnum_TRUE
	}
	panAadharLinkageDetails.PanLinkedAadharDigitsHash = panValidationHashedDigits
	panAadharLinkageDetails.PanLinkedAadhaarLast2DigitsHash = panValidationLast2DigitsHash
	panAadharLinkageDetails.IsAadharDigitsHashMismatch = hashMismatch
	logger.Info(ctx, fmt.Sprintf("logging the aadhar mismatch result %v", hashMismatch),
		zap.Any("PAN_VALIDATION_AADHAR_DIGITS_HASH", panValidationHashedDigits),
		zap.Any("EKYC_NAME_DOB_VALIDATION_AADHAR_DIGITS_HASH", panAadharLinkageDetails.GetPanLinkedAadharDigitsHash()),
		zap.Any("PAN_VALIDATION_AADHAR_LAST2_DIGITS_HASH", panAadharLinkageDetails.GetPanLinkedAadhaarLast2DigitsHash()))

	if errUpdate := s.onbDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, onb); errUpdate != nil {
		logger.Error(ctx, "failed while update onboarding details", zap.Error(errUpdate))
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, errUpdate
	}

	return hashMismatch, false, nil
}

func (s *PreCustomerCreationCheckStage) shouldPollAgain(ctx context.Context, onb *onbPb.OnboardingDetails) bool {
	switch onb.GetFeature() {
	case onbPb.Feature_FEATURE_PL:
		if s.userConf.Onboarding().Flags().AllowManualReviewUsers() &&
			(!getStageStatus(onb, onbPb.OnboardingStage_VKYC).IsSuccessOrSkipped() ||
				!getStageStatus(onb, onbPb.OnboardingStage_AADHAR_MOBILE_VALIDATION).IsSuccessOrSkipped()) {
			logger.Info(ctx, "vkyc or AMV is not successful yet, asking client to poll again")
			return true
		}
	default:
		if s.userConf.Onboarding().Flags().AllowManualReviewUsers() &&
			(!getStageStatus(onb, onbPb.OnboardingStage_LIVENESS).IsSuccessOrSkipped() ||
				!getStageStatus(onb, onbPb.OnboardingStage_RISK_SCREENING).IsSuccessOrSkipped() ||
				!getStageStatus(onb, onbPb.OnboardingStage_VKYC).IsSuccessOrSkipped() ||
				!getStageStatus(onb, onbPb.OnboardingStage_AADHAR_MOBILE_VALIDATION).IsSuccessOrSkipped()) {
			logger.Info(ctx, "LIVENESS or RISK_SCREENING or vkyc or AMV is not successful yet, asking client to poll again")
			return true
		}
	}

	return false
}

func getStageMetadataWithPanUniquenessFailure(onb *onbPb.OnboardingDetails) *onbPb.StageMetadata {
	md := onb.GetStageMetadata()
	if md == nil {
		md = &onbPb.StageMetadata{}
	}
	md.PreCustomerCreationMetadata = &onbPb.PreCustomerCreationMetadata{
		FailureReason: onbPb.PreCustomerCreationMetadata_FAILURE_REASON_PAN_UNIQUENESS_CHECK,
	}
	return md
}
func (s *PreCustomerCreationCheckStage) dedupeCheck(ctx context.Context, onb *onbPb.OnboardingDetails, stage onbPb.OnboardingStage) (*dlPb.Deeplink, error) {
	// validate manual intervention status
	allow, err := s.dedupeProc.AllowDedupeCheck(ctx, onb, stage)
	if err != nil {
		logger.Error(ctx, "error in AllowDedupeCheck", zap.Error(err))
		return nil, err
	}
	if !allow {
		dl := s.dedupeProc.DedupeDeeplinkHandler(ctx, onb, onb.GetStageMetadata().GetPreCustomerCreationDedupeStatus())
		if getStageStatus(onb, stage) == onbPb.OnboardingState_MANUAL_INTERVENTION {
			if dl == nil {
				return nil, errNoAction
			}
			return dl, nil
		}
		s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), false, "")
		return nil, errNoAction
	}

	userResp, kycResp, err := getUserAndKycRecordFromOnbDetails(ctx, onb, s.userClient, s.kycClient)
	if errors.Is(err, ErrKYCRecordNotFound) && getStageStatus(onb, onbPb.OnboardingStage_ENSURE_KYC_AVAILABILITY).IsSuccessOrSkipped() {
		logger.Info(ctx, "skipping dedupe check bc kyc is not found & ensure kyc availability stage is success")
		return nil, errNoAction
	}
	if err != nil {
		logger.Error(ctx, "error in fetching user or KYC records from onboarding details", zap.Error(err))
		return nil, err
	}

	idProofs := kycResp.GetKycRecord().GetIdentityProofs()
	idProofs = append(idProofs, &kycPb.IdProof{
		Type:    kycPb.IdProofType_UID,
		IdValue: kycResp.GetKycRecord().GetUidReferenceKey(),
	})

	allowed, dedupeStatus, err := s.dedupeProc.DedupeCheckCore(ctx, userResp.GetUser(), onb.GetVendor(), idProofs, stage)
	if err != nil {
		logger.Error(ctx, "error in dedupe check", zap.Error(err))
		return nil, err
	}

	// Update dedupe status in onboarding metadata
	if err = s.dedupeProc.UpdateDedupeFlagInOnboardingDetails(ctx, onb, dedupeStatus, onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK); err != nil {
		logger.Error(ctx, "Failed to update dedupe flag in onboarding details", zap.Error(err))
		return nil, err
	}

	if dedupeStatus == vgPbCustomer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS {
		bcRes, bcErr := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
			Identifier: &bankcust.GetBankCustomerRequest_ActorId{
				ActorId: onb.GetActorId(),
			},
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		})
		if rpcErr := epifigrpc.RPCError(bcRes, bcErr); rpcErr != nil {
			logger.Error(ctx, "error in getting bank customer", zap.Error(rpcErr))
			return nil, rpcErr
		}
		if bcRes.GetBankCustomer().GetKycLevelUpdateFlow() == bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC {
			logger.Info(ctx, "precustomercreationcheck dedupe status CUSTOMER_DOEST_NOT_EXISTS, so updating kyc level flow in bankCustomer")
			vkycSummaryStatus, errVKYC := s.getVKYCSummaryStatus(ctx, onb.GetActorId())
			if errVKYC != nil {
				return nil, errVKYC
			}

			kycLevel := bcRes.GetBankCustomer().GetKycInfo().GetKycLevel()
			kycLevelUpdateFlow := bcRes.GetBankCustomer().GetKycLevelUpdateFlow()
			switch {
			case vkycSummaryStatus == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED:
				logger.Info(ctx, "updating bank customer for vkyc approved", zap.String("old kycLevelUpdateFlow", kycLevelUpdateFlow.String()), zap.String("old kyclevel", kycLevel.String()))
				kycLevelUpdateFlow = bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC_LATEST_NO_DEDUPE
				kycLevel = kycPb.KYCLevel_FULL_KYC
			case vkycSummaryStatus == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED:
				logger.Info(ctx, "updating bank customer for vkyc rejected", zap.String("old kycLevelUpdateFlow", kycLevelUpdateFlow.String()), zap.String("old kyclevel", kycLevel.String()))
				kycLevelUpdateFlow = bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_UNSPECIFIED
				kycLevel = kycPb.KYCLevel_MIN_KYC
			}
			updateResp, updateErr := s.bcClient.UpdateKycLevel(ctx, &bankcust.UpdateKycLevelRequest{
				ActorId:            onb.GetActorId(),
				Vendor:             commonvgpb.Vendor_FEDERAL_BANK,
				KycLevel:           kycLevel,
				KycLevelUpdateFlow: kycLevelUpdateFlow,
			})
			if err = epifigrpc.RPCError(updateResp, updateErr); err != nil {
				logger.Error(ctx, "error in updating kyc level", zap.Error(err))
				return nil, err

			}
		}
	}

	if dedupeStatus == vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PARTIAL_KYC {
		logger.Info(ctx, "partial etb users who have crossed vkyc stage")
		vkycSummaryStatus, errVKYC := s.getVKYCSummaryStatus(ctx, onb.GetActorId())
		if errVKYC != nil {
			return nil, errVKYC
		}
		if vkycSummaryStatus == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED {
			logger.Info(ctx, "partial kyc dedupe customer post vkyc approved")
			s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), true, userEvents.BlockReasonTerminal)
			return ActionForUserStuckOnAcctCreation(ctx, onb), nil
		}
		// We will reset relevant stages for these users and allow them to perform VKYC.
		if err = s.onbDao.UpdateStageStatuses(ctx, onb.GetOnboardingId(), map[onbPb.OnboardingStage]onbPb.OnboardingState{
			onbPb.OnboardingStage_UPDATE_CUSTOMER_DETAILS: onbPb.OnboardingState_RESET,
			onbPb.OnboardingStage_VKYC:                    onbPb.OnboardingState_RESET,
		}); err != nil {
			logger.Error(ctx, "error in updating onboarding stages", zap.Error(err))
			return nil, err
		}
		return deeplink.NewActionToGetNextAction(), nil
	}

	// dedupe check validation passed
	if allowed {
		return nil, errNoAction
	}

	dl := s.dedupeProc.DedupeDeeplinkHandler(ctx, onb, dedupeStatus)
	if dl == nil {
		return nil, NoActionError
	}

	// dedupe check validation failed
	if _, err = updateStatus(ctx, s.onbDao, onb.GetOnboardingId(), stage, onbPb.OnboardingState_MANUAL_INTERVENTION); err != nil {
		return nil, err
	}

	s.eventLogger.LogOnboardingRiskBlocking(ctx, onb.GetActorId(), true, userEvents.BlockReasonTerminal)
	return dl, nil
}

// this function checks if there is another user with a given pan whose customer is created with federal
//
//nolint:funlen
func (s *PreCustomerCreationCheckStage) isOnbRestrictedOnPan(ctx context.Context, onb *onbPb.OnboardingDetails) (bool, string, error) {
	phoneNumber := ""
	if getStageStatus(onb, onbPb.OnboardingStage_PAN_UNIQUENESS_CHECK).IsSuccessOrSkipped() {
		// check already performed for user
		return false, phoneNumber, nil
	}
	userResp, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: onb.GetUserId(),
		},
	})
	if grpcErr := epifigrpc.RPCError(userResp, err); grpcErr != nil {
		logger.Error(ctx, "error in get user", zap.Error(grpcErr))
		return false, phoneNumber, grpcErr
	}
	pan := userResp.GetUser().GetProfile().GetPAN()
	if isSimulatedEnv(s.userConf) &&
		!strings.HasPrefix(strings.ToUpper(pan), "UNQPP") {
		logger.Info(ctx, fmt.Sprintf("bypassing unique pan check for %v", pan))
		return false, phoneNumber, nil
	}
	// get all users with pan provided by User
	userIdentifiers := []*user.GetUsersRequest_GetUsersIdentifier{
		{
			Identifier: &user.GetUsersRequest_GetUsersIdentifier_Pan{
				Pan: pan,
			},
		},
	}
	getUsersResp, err := s.userClient.GetUsers(ctx, &user.GetUsersRequest{
		Identifier: userIdentifiers,
	})
	if err = epifigrpc.RPCError(getUsersResp, err); err != nil {
		logger.Error(ctx, "error in getting users by pan", zap.Error(err))
		return false, phoneNumber, fmt.Errorf("error in get user: %w", err)
	}
	if len(getUsersResp.GetUsers()) == 1 {
		phoneNumber = getMaskedNumber(getUsersResp.GetUsers()[0].GetProfile().GetPhoneNumber().GetNationalNumber())
	}
	for _, aUser := range getUsersResp.GetUsers() {
		// skip for current user
		if aUser.GetId() == onb.GetUserId() {
			continue
		}
		// User cannot continue if bank customer creation is in progress or created for some other user with provided pan
		bcRes, bcErr := s.bcClient.CheckBankCustomerCreationStatus(ctx, &bankcust.CheckBankCustomerCreationStatusRequest{
			ActorId: aUser.GetActorId(),
			Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
		})
		switch {
		case bcRes.GetStatus().IsSuccess(), bcRes.GetStatus().IsInProgress(),
			bcRes.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_NON_RETRYABLE_FAILURE),
			bcRes.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_RETRYABLE_FAILURE):
			logger.Info(ctx, "user is blocked, pan already used", zap.String("panUsedByUserId", aUser.GetId()))
			return true, phoneNumber, nil
		case bcRes.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED):
			continue
		default:
			if rpcErr := epifigrpc.RPCError(bcRes, bcErr); rpcErr != nil {
				logger.Error(ctx, "error while fetching bank customer creation status", zap.Error(rpcErr))
				return false, phoneNumber, rpcErr
			}
		}
	}
	// no user found with same pan and customer creation in progress
	return false, phoneNumber, nil
}

func (s *PreCustomerCreationCheckStage) getVKYCSummaryStatus(ctx context.Context, actorId string) (vkycPb.VKYCSummaryStatus, error) {
	resp, errResp := s.vkycClient.GetVKYCSummary(ctx, &vkycPb.GetVKYCSummaryRequest{
		ActorId: actorId,
	})
	if err := epifigrpc.RPCError(resp, errResp); err != nil && !resp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in fetching vkyc summary", zap.Error(err))
		return vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNSPECIFIED, err
	}
	return resp.GetVkycRecord().GetVkycSummary().GetStatus(), nil
}

func getMaskedNumber(phone uint64) string {
	phoneStr := fmt.Sprintf("%v", phone)
	return strings.Join([]string{strings.Repeat("x", 6), phoneStr[6:]}, "")
}
