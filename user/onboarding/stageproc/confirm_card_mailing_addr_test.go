package stageproc

import (
	"context"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	colorPkg "github.com/epifi/be-common/pkg/colors"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	onbDlPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	pkgDl "github.com/epifi/gamma/pkg/deeplink"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/gamma/api/typesv2/ui"

	"github.com/epifi/gamma/api/bankcust"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	kycPkg "github.com/epifi/gamma/pkg/kyc"
)

var (
	kycLevelFull = kyc.KYCLevel_FULL_KYC

	shippingPref1 = &user.ShippingPreference{
		Id:      "1234",
		ActorId: "123",
	}
	shippingPreferenceReq = &user.GetShippingPreferenceRequest{
		ActorId:      "123",
		ShippingItem: types.ShippingItem_DEBIT_CARD,
	}
	shippingRespSuccess = &user.GetShippingPreferenceResponse{
		Preference: shippingPref1,
		Status:     rpc.StatusOk(),
	}
	shippingRespRecordNotFound = &user.GetShippingPreferenceResponse{
		Preference: shippingPref1,
		Status:     rpc.StatusRecordNotFound(),
	}
	longNameUserFix = &user.User{
		Profile: &user.Profile{
			KycName: &commontypes.Name{
				FirstName: "Long Name",
				LastName:  "Very Long Name",
			},
		},
	}
	emptyNameUserFix = &user.User{
		Profile: &user.Profile{
			KycName: &commontypes.Name{
				FirstName: "1 &^%",
				LastName:  "& * *",
			},
		},
	}
	shortNameUserFix = &user.User{
		Profile: &user.Profile{
			KycName: &commontypes.Name{
				FirstName: "Short",
				LastName:  "Name",
			},
		},
	}
	cleanedNameUserFix = &user.User{
		Profile: &user.Profile{
			KycName: &commontypes.Name{
				FirstName: "Sh@89ort",
				LastName:  "Na#me",
			},
		},
	}
	cleanedNameUserFix2 = &user.User{
		Profile: &user.Profile{
			KycName: &commontypes.Name{
				FirstName: "Sh@89ort abc",
				LastName:  "Na#me",
			},
		},
	}
)

func TestService_ConfirmCardMailingAddress(t *testing.T) {

	config, _ := genconf.LoadOnlyStaticConf()
	featureConfig := config.Onboarding().Flags().EnableSaDeclarationstage()

	screenParams := ts.Conf.Onboarding.PhysicalCardChargesMailingAddressScreenParams
	type getShippingPreferenceParams struct {
		req  *user.GetShippingPreferenceRequest
		resp *user.GetShippingPreferenceResponse
		err  error
	}

	type args struct {
		onb                         *onbPb.OnboardingDetails
		getShippingPreferenceParams *getShippingPreferenceParams
	}
	type test struct {
		name    string
		args    args
		mocks   func(mock *Clients)
		wantErr bool
		wantDL  *dlPb.Deeplink
	}
	ctx := context.Background()
	tests := []test{
		{
			name: "StageMetadata missing",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
				},
			},
			mocks: func(mock *Clients) {
				_ = featureConfig.SetDisableFeature(false, true, nil)
				_ = featureConfig.SetMinAndroidVersion(1, true, nil)
				_ = featureConfig.SetMinIOSVersion(1, true, nil)
				_ = featureConfig.SetFallbackToEnableFeature(true, true, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   longNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
				mock.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Id: "bc-1",
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycLevelFull,
						},
					},
				}, nil)
				mock.KycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId: "123",
				}).Return(&kyc.CheckKYCStatusResponse{
					Status:  rpc.StatusOk(),
					KycType: kyc.KycType_EKYC,
				}, nil)
				mock.KycClient.EXPECT().GetKYCRecord(gomock.Any(), &kyc.GetKYCRecordRequest{
					ActorId:   "123",
					SignImage: true,
				}).Return(&kyc.GetKYCRecordResponse{
					Status:  rpc.StatusOk(),
					KycType: kyc.KycType_EKYC,
					KycRecord: &kyc.KYCRecord{
						Gender: types.Gender_MALE,
					},
				}, nil)
			},
			wantErr: false,
			wantDL: deeplinkv3.GetDeeplinkV3WithoutError(dlPb.Screen_CONFIRM_CARD_MAILING_ADDRESS_SCREEN_V2, &onbDlPb.ConfirmCardMailingAddressOptions{
				KycLevel:           kycPkg.KycLevelMap[kycLevelFull],
				PlaceHolderForName: "YOUR FULL NAME",
				Flow:               onbDlPb.ConfirmCardMailingAddressOptions_FLOW_ONBOARDING,
				Image: &commontypes.Image{
					ImageType: commontypes.ImageType_PNG,
					ImageUrl:  constant.ConfirmCardMailingAddressIcon,
					Height:    89,
					Width:     136,
				},
				CheckboxTextColor: screenParams.CheckboxTextColor,
				PlaceHolderColor:  screenParams.PlaceholderColor,
				ContentColor:      screenParams.ContentColor,
				DividerColor:      screenParams.DividerColor,
				EditIconColor:     screenParams.EditIconColor,
				CardColor:         screenParams.CardColor,
				BackgroundColor:   screenParams.BackgroundColor,
				ScreenTitle:       commontypes.GetTextFromStringFontColourFontStyle(constant.ConfirmCardMailingTitle, colorPkg.ColorDarkLayer2, commontypes.FontStyle_HEADLINE_L),
				ScreenSubtitle:    commontypes.GetTextFromStringFontColourFontStyle(constant.ConfirmCardMailingSubTitle, colorPkg.ColorOnlightLowEmphasis, commontypes.FontStyle_BODY_S),
				Cta: &dlPb.Cta{
					Type:         dlPb.Cta_CONTINUE,
					Text:         screenParams.CtaText,
					DisplayTheme: dlPb.Cta_PRIMARY,
					Status:       dlPb.Cta_CTA_STATUS_ENABLED,
				},
				AddressConfirmationMessage: "This name will be used in your debit card.",
				HideAddressField:           true,
				HeaderBar:                  pkgDl.HeaderBarForFederalOwnedScreen(),
				DebitCardNameDescription:   commontypes.GetTextFromStringFontColourFontStyle(constant.ConfirmCardMailingNameDescription, colorPkg.ColorOnlightLowEmphasis, commontypes.FontStyle_BODY_S),
				NamePlaceholder: &onbDlPb.ConfirmCardMailingAddressOptions_PlaceHolder{
					BgColor:         widget.GetBlockBackgroundColour("#FFFFFF"),
					BorderColor:     widget.GetBlockBackgroundColour("#FFFFFF"),
					Label:           commontypes.GetTextFromStringFontColourFontStyle("YOUR FULL NAME", colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_SUBTITLE_L),
					ExplanatoryText: commontypes.GetTextFromStringFontColourFontStyle("This name will appear on your Debit Card. Ensure it matches with your KYC records.", colorPkg.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_S),
				},
				Gender: &onbDlPb.ConfirmCardMailingAddressOptions_PlaceHolder{
					BgColor:         widget.GetBlockBackgroundColour("#FFFFFF"),
					BorderColor:     widget.GetBlockBackgroundColour("#FFFFFF"),
					Label:           commontypes.GetTextFromStringFontColourFontStyle("GENDER", colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_SUBTITLE_L),
					Value:           commontypes.GetTextFromStringFontColourFontStyle("MALE", colorPkg.ColorOnLightHighEmphasis, commontypes.FontStyle_HEADLINE_L),
					ExplanatoryText: commontypes.GetTextFromStringFontColourFontStyle("As per your KYC records", colorPkg.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_S),
				},
				PurposeOfSavingsAccount: &onbDlPb.ConfirmCardMailingAddressOptions_PurposeOfSavingsAccount{
					Title: commontypes.GetTextFromStringFontColourFontStyle(constant.ConfirmCardMailingRadioTitle, colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_OVERLINE_S_CAPS),
					RadioOptions: []*onbDlPb.ConfirmCardMailingAddressOptions_RadioOption{
						{
							Options:                      commontypes.GetTextFromStringFontColourFontStyle("Savings", colorPkg.ColorOnlightLowEmphasis, commontypes.FontStyle_HEADLINE_S),
							PurposeOfSavingsAccountValue: types.PurposeOfSavingsAccount_PURPOSE_OF_SAVINGS_ACCOUNT_SAVINGS.String(),
						},
						{
							Options:                      commontypes.GetTextFromStringFontColourFontStyle("Loans", colorPkg.ColorOnlightLowEmphasis, commontypes.FontStyle_HEADLINE_S),
							PurposeOfSavingsAccountValue: types.PurposeOfSavingsAccount_PURPOSE_OF_SAVINGS_ACCOUNT_LOAN.String(),
						},
						{
							Options:                      commontypes.GetTextFromStringFontColourFontStyle("Investment", colorPkg.ColorOnlightLowEmphasis, commontypes.FontStyle_HEADLINE_S),
							PurposeOfSavingsAccountValue: types.PurposeOfSavingsAccount_PURPOSE_OF_SAVINGS_ACCOUNT_INVESTMENT.String(),
						},
					},
				},
				ConfirmBottomSheetHeader: &ui.IconTextComponent{
					LeftIcon: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  "https://epifi-icons.pointz.in/onboarding/usercheck",
						Width:     24,
						Height:    24,
					},
					Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Save and proceed?", colorPkg.ColorNight, commontypes.FontStyle_BODY_XS)},
					RightIcon:           nil,
					LeftImgTxtPadding:   0,
					RightImgTxtPadding:  0,
					Deeplink:            nil,
					ContainerProperties: nil,
					LeftVisualElement:   nil,
					RightVisualElement:  nil,
				},
			}),
		},
		{
			name: "Debit card retry not exhausted",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  false,
							NameCheckRetryCount: 1,
						},
					},
				},
			},
			mocks: func(mock *Clients) {
				_ = featureConfig.SetDisableFeature(true, true, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   longNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
				mock.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Id: "bc-1",
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycLevelFull,
						},
					},
				}, nil)
				mock.KycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId: "123",
				}).Return(&kyc.CheckKYCStatusResponse{
					Status:  rpc.StatusOk(),
					KycType: kyc.KycType_EKYC,
				}, nil)
			},
			wantErr: false,
			wantDL: &dlPb.Deeplink{
				Screen: dlPb.Screen_CONFIRM_CARD_MAILING_ADDRESS,
				ScreenOptions: &dlPb.Deeplink_ConfirmCardMailingAddressOptions{
					ConfirmCardMailingAddressOptions: &dlPb.ConfirmCardMailingAddressOptions{
						HideAddressField:      true,
						KycLevel:              kycPkg.KycLevelMap[kycLevelFull],
						PlaceHolderForName:    screenParams.PlaceholderForName,
						PlaceHolderForAddress: screenParams.PlaceholderForAddress,
						CheckBoxTexts: []*dlPb.ConfirmCardMailingAddressOptions_CheckBoxText{
							{
								Type: types.AddressType_MAILING,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_PERMANENT,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_SHIPPING,
								Text: screenParams.NonAadharAddrCheckBoxText,
							},
							{
								Type: types.AddressType_ADDRESS_TYPE_UNSPECIFIED,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
						},
						Flow: dlPb.ConfirmCardMailingAddressOptions_FLOW_ONBOARDING,
						Image: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  screenParams.ImageUrl,
						},
						CheckboxTextColor: screenParams.CheckboxTextColor,
						PlaceHolderColor:  screenParams.PlaceholderColor,
						ContentColor:      screenParams.ContentColor,
						DividerColor:      screenParams.DividerColor,
						EditIconColor:     screenParams.EditIconColor,
						CardColor:         screenParams.CardColor,
						BackgroundColor:   screenParams.BackgroundColor,
						ScreenTitle: &commontypes.Text{
							FontColor:    screenParams.TitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Title},
						},
						ScreenSubtitle: &commontypes.Text{
							FontColor:    screenParams.SubtitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Subtitle},
						},
						Cta: &dlPb.Cta{
							Type:         dlPb.Cta_CONTINUE,
							Text:         screenParams.CtaText,
							DisplayTheme: dlPb.Cta_PRIMARY,
							Status:       dlPb.Cta_CTA_STATUS_ENABLED,
						},
					},
				},
			},
		},
		{
			name: "Debit card retries exhausted",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  false,
							NameCheckRetryCount: constant.DebitCardNameMaxRetries,
						},
					},
				},
			},
			mocks: func(mock *Clients) {
				_ = featureConfig.SetDisableFeature(true, true, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   longNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: false,
			wantDL:  deeplink.NewErrorFullScreen(context.Background(), error2.OnbErrCardNameMaxRetries),
		},
		{
			name: "confirm card mailing automatic",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  true,
							NameCheckRetryCount: 1,
						},
					},
				},
			},
			mocks: func(mock *Clients) {
				_ = featureConfig.SetDisableFeature(true, true, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   shortNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
				mock.userClient.EXPECT().UpdateUser(gomock.Any(), &user.UpdateUserRequest{
					User: &user.User{
						Id: "1",
						Profile: &user.Profile{
							DebitCardName: shortNameUserFix.GetProfile().GetKycName(),
						},
					},
					UpdateMask: []user.UserFieldMask{
						user.UserFieldMask_DEBIT_CARD_NAME,
					},
				}).Return(&user.UpdateUserResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: true,
			wantDL:  getDeviceRegistrationTransitionScreen(onbPb.Feature_FEATURE_UNSPECIFIED),
		},
		{
			name: "confirm card mailing, with Sanitize KYC Name",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  true,
							NameCheckRetryCount: 1,
						},
					},
				},
			},
			mocks: func(mock *Clients) {
				_ = featureConfig.SetDisableFeature(true, true, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   cleanedNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
				mock.userClient.EXPECT().UpdateUser(gomock.Any(), &user.UpdateUserRequest{
					User: &user.User{
						Id: "1",
						Profile: &user.Profile{
							DebitCardName: shortNameUserFix.GetProfile().GetKycName(),
						},
					},
					UpdateMask: []user.UserFieldMask{
						user.UserFieldMask_DEBIT_CARD_NAME,
					},
				}).Return(&user.UpdateUserResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: true,
			wantDL:  getDeviceRegistrationTransitionScreen(onbPb.Feature_FEATURE_UNSPECIFIED),
		},
		{
			name: "confirm card mailing, with Sanitize KYC Name white space",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  true,
							NameCheckRetryCount: 1,
						},
					},
				},
			},
			mocks: func(mock *Clients) {
				_ = featureConfig.SetDisableFeature(true, true, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   cleanedNameUserFix2,
					Status: rpc.StatusOk(),
				}, nil)
				mock.userClient.EXPECT().UpdateUser(gomock.Any(), &user.UpdateUserRequest{
					User: &user.User{
						Id: "1",
						Profile: &user.Profile{
							DebitCardName: &commontypes.Name{
								FirstName:  "Short",
								MiddleName: "abc",
								LastName:   "Name",
							},
						},
					},
					UpdateMask: []user.UserFieldMask{
						user.UserFieldMask_DEBIT_CARD_NAME,
					},
				}).Return(&user.UpdateUserResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			wantErr: true,
			wantDL:  getDeviceRegistrationTransitionScreen(onbPb.Feature_FEATURE_UNSPECIFIED),
		},
		{
			name: "empty name fix",
			args: args{
				onb: &onbPb.OnboardingDetails{
					UserId:  "1",
					ActorId: "123",
					StageMetadata: &onbPb.StageMetadata{
						DebitCardNameCheck: &onbPb.DebitCardNameCheck{
							OldNameMatchPassed:  false,
							NameCheckRetryCount: 1,
						},
					},
				},
				getShippingPreferenceParams: &getShippingPreferenceParams{
					req:  shippingPreferenceReq,
					resp: shippingRespRecordNotFound,
					err:  nil,
				},
			},
			mocks: func(mock *Clients) {
				_ = featureConfig.SetDisableFeature(true, true, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&user.GetUserResponse{
					User:   emptyNameUserFix,
					Status: rpc.StatusOk(),
				}, nil)
				mock.bankCustClient.EXPECT().GetBankCustomer(gomock.Any(), &bankcust.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankcust.GetBankCustomerRequest_ActorId{
						ActorId: "123",
					},
				}).Return(&bankcust.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankcust.BankCustomer{
						Id: "bc-1",
						KycInfo: &bankcust.KYCInfo{
							KycLevel: kycLevelFull,
						},
					},
				}, nil)
				mock.KycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId: "123",
				}).Return(&kyc.CheckKYCStatusResponse{
					Status:  rpc.StatusOk(),
					KycType: kyc.KycType_EKYC,
				}, nil)
			},
			wantErr: false,
			wantDL: &dlPb.Deeplink{
				Screen: dlPb.Screen_CONFIRM_CARD_MAILING_ADDRESS,
				ScreenOptions: &dlPb.Deeplink_ConfirmCardMailingAddressOptions{
					ConfirmCardMailingAddressOptions: &dlPb.ConfirmCardMailingAddressOptions{
						HideAddressField:      true,
						KycLevel:              kycPkg.KycLevelMap[kycLevelFull],
						PlaceHolderForName:    screenParams.PlaceholderForName,
						PlaceHolderForAddress: screenParams.PlaceholderForAddress,
						CheckBoxTexts: []*dlPb.ConfirmCardMailingAddressOptions_CheckBoxText{
							{
								Type: types.AddressType_MAILING,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_PERMANENT,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
							{
								Type: types.AddressType_SHIPPING,
								Text: screenParams.NonAadharAddrCheckBoxText,
							},
							{
								Type: types.AddressType_ADDRESS_TYPE_UNSPECIFIED,
								Text: screenParams.CurrentAddrCheckBoxText,
							},
						},
						Flow: dlPb.ConfirmCardMailingAddressOptions_FLOW_ONBOARDING,
						Image: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  screenParams.ImageUrl,
						},
						CheckboxTextColor: screenParams.CheckboxTextColor,
						PlaceHolderColor:  screenParams.PlaceholderColor,
						ContentColor:      screenParams.ContentColor,
						DividerColor:      screenParams.DividerColor,
						EditIconColor:     screenParams.EditIconColor,
						CardColor:         screenParams.CardColor,
						BackgroundColor:   screenParams.BackgroundColor,
						ScreenTitle: &commontypes.Text{
							FontColor:    screenParams.TitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Title},
						},
						ScreenSubtitle: &commontypes.Text{
							FontColor:    screenParams.SubtitleFontColor,
							DisplayValue: &commontypes.Text_PlainString{PlainString: screenParams.Subtitle},
						},
						Cta: &dlPb.Cta{
							Type:         dlPb.Cta_CONTINUE,
							Text:         screenParams.CtaText,
							DisplayTheme: dlPb.Cta_PRIMARY,
							Status:       dlPb.Cta_CTA_STATUS_ENABLED,
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			clients := setupServiceWithMocks(t)
			s := NewConfirmCardMailingAddressStage(clients.userClient, clients.KycClient, clients.bankCustClient, ts.GenConf.Onboarding())
			s.dynConf = config.Onboarding()
			tt.mocks(clients)
			res, err := s.StageProcessor(ctx, &StageProcessorRequest{Onb: tt.args.onb})
			dl := res.GetNextAction()
			assert.Equal(t, tt.wantErr, err != nil)
			assert.Equal(t, tt.wantDL, dl)
		})
	}
}
