package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	deeplink2 "github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	onbPkg "github.com/epifi/gamma/pkg/onboarding"
)

type BKYC struct {
	groupClient group.GroupClient
	kycClient   kyc.KycClient
	userClient  user.UsersClient
	bcClient    bankcust.BankCustomerServiceClient
	vkycClient  vkyc.VKYCClient
}

func NewBKYCStage(grpClient group.GroupClient, kycClient kyc.KycClient, userClient user.UsersClient, bcClient bankcust.BankCustomerServiceClient, vkycClient vkyc.VKYCClient) *BKYC {
	return &BKYC{
		groupClient: grpClient,
		kycClient:   kycClient,
		userClient:  userClient,
		bcClient:    bcClient,
		vkycClient:  vkycClient,
	}
}

func (b *BKYC) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		actorId = req.GetOnb().GetActorId()
	)
	canSkip, err := b.checkIfSkipBKYC(ctx, req.GetOnb())
	if err != nil {
		return nil, err
	}
	if canSkip {
		return nil, SkipStageError
	}
	minUserRes, errResp := b.userClient.GetMinimalUser(ctx, &user.GetMinimalUserRequest{
		Identifier: &user.GetMinimalUserRequest_Id{
			Id: req.GetOnb().GetUserId(),
		},
	})
	if err := epifigrpc.RPCError(minUserRes, errResp); err != nil {
		logger.Error(ctx, "error in fetching minimal user", zap.Error(err))
		return nil, err
	}
	isWhitelisted, err := b.checkWhitelistingForBKYC(ctx, minUserRes.GetMinimalUser().GetProfileEssentials())
	if err != nil {
		return nil, err
	}
	if !isWhitelisted {
		return nil, SkipStageError
	}
	kycRes, err := kycStatus(ctx, b.kycClient, actorId)
	if err != nil && !storageV2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "error in fetching kyc record", zap.Error(err))
		return nil, err
	}
	if kycRes.GetKycType() == kyc.KycType_BKYC && kycRes.GetKycStatus() == kyc.KycStatus_COMPLETED {
		return nil, NoActionError
	}
	if kycRes.GetKycType() == kyc.KycType_BKYC && kycRes.GetKycStatus() == kyc.KycStatus_ERRORED {
		return handleBKYCErrored(ctx, kycRes)
	}
	if b.showKycOptionSelectionScreen(kycRes) {
		logger.Info(ctx, "returning kyc option selection screen")
		return &StageProcessorResponse{
			NextAction: getKYCSelectionDeeplink(),
		}, nil
	}

	if kycRes.GetKycType() == kyc.KycType_BKYC && kycRes.GetKycState() == kyc.KYCState_BKYC_IN_REVIEW {
		logger.Info(ctx, "returning bkyc in review screen")
		signatureImage, errSign := b.getSignature(ctx, actorId)
		if errSign != nil {
			return nil, errSign
		}
		return &StageProcessorResponse{
			NextAction: onbPkg.GetKYCRecordDeeplink(signatureImage),
		}, nil
	}
	return nil, SkipStageError
}

func (b *BKYC) getSignature(ctx context.Context, actorId string) ([]byte, error) {
	kycRes, err := b.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId:                 actorId,
		SignImage:               true,
		ForceBkycInReviewRecord: true,
	})
	if rpcErr := epifigrpc.RPCError(kycRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting KYC record", zap.Error(rpcErr))
		return nil, rpcErr
	}
	return []byte(kycRes.GetKycRecord().GetSignImage()), nil
}

func (b *BKYC) showKycOptionSelectionScreen(kycRes *kyc.CheckKYCStatusResponse) bool {
	if kycRes.GetStatus().IsRecordNotFound() {
		return true
	}
	if kycRes.GetKycStatus() == kyc.KycStatus_EXPIRED {
		return true
	}
	if kycRes.GetKycType() == kyc.KycType_CKYC && kycRes.GetKycStatus() == kyc.KycStatus_COMPLETED {
		return true
	}
	if kycRes.GetKycType() != kyc.KycType_BKYC && kycRes.GetKycStatus() != kyc.KycStatus_COMPLETED {
		return true
	}
	if kycRes.GetKycType() == kyc.KycType_BKYC && kycRes.GetKycState() != kyc.KYCState_BKYC_IN_REVIEW {
		return true
	}
	return false
}

// nolint: funlen,dupl
func getKYCSelectionDeeplink() *deeplink.Deeplink {
	const (
		screenTitleFontStyle         = commontypes.FontStyle_HEADLINE_L
		screenTitleFontColor         = "#313234"
		biometricTagFontColor        = "#2D5E6E"
		recommendedTagFontColor      = "#FFF8CE"
		ekycTagFontColor             = "#6F62A4"
		tagFontStyle                 = commontypes.FontStyle_SUBTITLE_XS
		deeplinkOptionTitleFontColor = "#313234"
		deeplinkOptionTitleFontStyle = commontypes.FontStyle_HEADLINE_M
	)
	selectionScreen := &deeplink.Deeplink{
		Screen: deeplink.Screen_ONBOARDING_KYC_OPTION_SELECTION_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&onboarding.OnboardingKYCOptionSelectionScreenOption{
			Title: commontypes.GetTextFromStringFontColourFontStyle("Pick a eKYC method to continue", screenTitleFontColor, screenTitleFontStyle),
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: "#FFF",
				},
			},
			DeeplinkOption: []*onboarding.OnboardingKYCOptionSelectionScreenOption_DeeplinkOption{
				{
					Title: commontypes.GetTextFromStringFontColourFontStyle("eKYC: Aadhaar + Biometric", deeplinkOptionTitleFontColor, deeplinkOptionTitleFontStyle),
					Icon: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/onboarding/bkyc_selection.png",
								},
								Properties: &commontypes.VisualElementProperties{
									Height: 80,
									Width:  80,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
					Tags: []*onboarding.OnboardingKYCOptionSelectionScreenOption_DeeplinkOption_Tag{
						{
							Text: commontypes.GetTextFromStringFontColourFontStyle("Suggested", recommendedTagFontColor, tagFontStyle),
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#D2AC3D",
								},
							},
							PositionAboveTitle: true,
						},
						{
							Text: commontypes.GetTextFromStringFontColourFontStyle("Full access", biometricTagFontColor, tagFontStyle),
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#EFF2F6",
								},
							},
						},
						{
							Text: commontypes.GetTextFromStringFontColourFontStyle("No balance limits", biometricTagFontColor, tagFontStyle),
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#EFF2F6",
								},
							},
						},
						{
							Text: commontypes.GetTextFromStringFontColourFontStyle("Lifetime account", biometricTagFontColor, tagFontStyle),
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#EFF2F6",
								},
							},
						},
						{
							Text: commontypes.GetTextFromStringFontColourFontStyle("No video verification required", biometricTagFontColor, tagFontStyle),
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#EFF2F6",
								},
							},
						},
					},
					Deeplink: onbPkg.GetBKYCConsentScreenDeeplink(),
				},
				{
					Title: commontypes.GetTextFromStringFontColourFontStyle("eKYC: Aadhaar + OTP", deeplinkOptionTitleFontColor, deeplinkOptionTitleFontStyle),
					Icon: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/onboarding/ekyc_selection.png",
								},
								Properties: &commontypes.VisualElementProperties{
									Height: 80,
									Width:  80,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
					Tags: []*onboarding.OnboardingKYCOptionSelectionScreenOption_DeeplinkOption_Tag{
						{
							Text: commontypes.GetTextFromStringFontColourFontStyle("Limited features", ekycTagFontColor, tagFontStyle),
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#EFF2F6",
								},
							},
						},
						{
							Text: commontypes.GetTextFromStringFontColourFontStyle("50K balance limit", ekycTagFontColor, tagFontStyle),
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#EFF2F6",
								},
							},
						},
						{
							Text: commontypes.GetTextFromStringFontColourFontStyle("Expires in 1y", ekycTagFontColor, tagFontStyle),
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#EFF2F6",
								},
							},
						},
						{
							Text: commontypes.GetTextFromStringFontColourFontStyle("Video verification required", ekycTagFontColor, tagFontStyle),
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: "#EFF2F6",
								},
							},
						},
					},
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_SKIP_ONBOARDING_STAGE_API,
						ScreenOptions: &deeplink.Deeplink_SkipOnboardingStageApiOption{
							SkipOnboardingStageApiOption: &deeplink.SkipOnboardingStageApiOption{
								Stage: onbPb.OnboardingStage_BKYC.String(),
							},
						},
					},
				},
			},
			Ctas: []*deeplink.Cta{
				{
					Type:         deeplink.Cta_DONE,
					Text:         "Continue",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		}),
	}
	return selectionScreen
}

func (b *BKYC) checkWhitelistingForBKYC(ctx context.Context, profile *user.ProfileEssentials) (bool, error) {
	// We do not show the selection screen to users not in BKYC user group
	groupRes, errResp := b.groupClient.CheckMapping(ctx, &group.CheckMappingRequest{
		UserGroup: commontypes.UserGroup_BKYC,
		IdentifierValue: &group.IdentifierValue{
			Identifier: &group.IdentifierValue_PhoneNumber{
				PhoneNumber: profile.GetPhoneNumber(),
			},
		},
	})
	if err := epifigrpc.RPCError(groupRes, errResp); err != nil && !groupRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in fetching user group mapping", zap.Error(err))
		return false, err
	}
	if groupRes.GetStatus().IsSuccess() {
		return true, nil
	}
	groupRes, errResp = b.groupClient.CheckMapping(ctx, &group.CheckMappingRequest{
		UserGroup: commontypes.UserGroup_BKYC,
		IdentifierValue: &group.IdentifierValue{
			Identifier: &group.IdentifierValue_Email{
				Email: profile.GetEmail(),
			},
		},
	})
	if err := epifigrpc.RPCError(groupRes, errResp); err != nil {
		if groupRes.GetStatus().IsRecordNotFound() {
			return false, nil
		}
		logger.Error(ctx, "error in fetching user group mapping", zap.Error(err))
		return false, err
	}
	return true, nil
}

func (b *BKYC) checkIfSkipBKYC(ctx context.Context, onbDetails *onbPb.OnboardingDetails) (bool, error) {
	if isUserDedupeWithPartialKYC(onbDetails) {
		return true, nil
	}
	vkycResp, errResp := b.vkycClient.GetVKYCSummary(ctx, &vkyc.GetVKYCSummaryRequest{
		ActorId: onbDetails.GetActorId(),
	})
	if err := epifigrpc.RPCError(vkycResp, errResp); err != nil && !vkycResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in fetching vkyc summary", zap.Error(err))
		return false, err
	}
	if vkycResp.GetVkycRecord().GetVkycSummary().GetStatus() == vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED ||
		vkycResp.GetVkycRecord().GetVkycSummary().GetStatus() == vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW {
		return true, nil
	}
	bcResp, errResp := b.bcClient.CheckBankCustomerCreationStatus(ctx, &bankcust.CheckBankCustomerCreationStatusRequest{
		ActorId: onbDetails.GetActorId(),
		Vendor:  onbDetails.GetVendor(),
	})
	switch {
	case bcResp.GetStatus().IsSuccess(),
		bcResp.GetStatus().IsInProgress():
		return true, nil
	case bcResp.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED),
		bcResp.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_RETRYABLE_FAILURE),
		bcResp.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_NON_RETRYABLE_FAILURE),
		bcResp.GetStatus().IsRecordNotFound():
		return false, nil
	default:
		if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
			logger.Error(ctx, "error in fetching bank customer status", zap.Error(err))
			return false, err
		}
		return false, nil
	}
}

func handleBKYCErrored(ctx context.Context, kycRes *kyc.CheckKYCStatusResponse) (*StageProcessorResponse, error) {
	dl := processDOBMismatchFailures(ctx, kycRes)
	if dl == nil {
		switch kycRes.GetFailureReason() {
		case kyc.FailureType_EMPTY_ADDRESS:
			dl = deeplink2.NewErrorFullScreen(ctx, error2.OnbErrKYCEmptyAddress)
		case kyc.FailureType_MINOR_AGE:
			dl = deeplink2.NewErrorFullScreen(ctx, error2.OnbErrKYCMinorAge)
		default:
			logger.Info(ctx, "received errored BKYC status", zap.String(logger.FAILURES, kycRes.GetFailureReason().String()))
			// If we have received ERRORED state from BKYC that is non-terminal, we skip this stage and let the user try other methods.
			return nil, SkipStageError
		}
	}
	return &StageProcessorResponse{
		NextAction: dl,
	}, nil
}
