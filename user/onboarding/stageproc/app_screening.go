package stageproc

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/constants"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/creditreportv2"
	empPb "github.com/epifi/gamma/api/employment"
	feScreeningPb "github.com/epifi/gamma/api/frontend/account/screening"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	feUserOnbPb "github.com/epifi/gamma/api/frontend/user/onboarding"
	"github.com/epifi/gamma/api/inapphelp/app_feedback"
	inAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	scrnrPb "github.com/epifi/gamma/api/screener"
	tieringPb "github.com/epifi/gamma/api/tiering"
	types "github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	userintelPb "github.com/epifi/gamma/api/userintel"
	pkgEmp "github.com/epifi/gamma/pkg/employment"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
	"github.com/epifi/gamma/user/onboarding/pkg/screener"
)

type AppScreeningStage struct {
	conf                                                   *genconf.OnboardingConfig
	eventBroker                                            events.Broker
	creditReportV2Client                                   creditreportv2.CreditReportManagerClient
	scrnrClient                                            scrnrPb.ScreenerClient
	empClient                                              empPb.EmploymentClient
	eventLogger                                            userEvents.EventLogger
	onbDao                                                 dao.OnboardingDao
	feEmpClient                                            empPb.EmploymentFeClient
	userClient                                             usersPb.UsersClient
	userIntelClient                                        userintelPb.UserIntelServiceClient
	inAppReferralClient                                    inAppReferralPb.InAppReferralClient
	affluentUserBonusTransitionScreenABEvaluator           *release.ABEvaluator[string]
	affluentUserBonusTransitionScreenNonRefereeABEvaluator *release.ABEvaluator[string]
	userProc                                               helper.UserProcessor
	userGroupClient                                        userGroupPb.GroupClient
	screenerChoicePageABEvaluator                          *release.ABEvaluator[string]
	helper                                                 helper.OnboardingHelper
	tieringClient                                          tieringPb.TieringClient
}

func NewAppScreeningStage(conf *genconf.OnboardingConfig, eventBroker events.Broker,
	scrnrClient scrnrPb.ScreenerClient, empClient empPb.EmploymentClient, eventLogger userEvents.EventLogger,
	onbDao dao.OnboardingDao, feEmpClient empPb.EmploymentFeClient, userClient usersPb.UsersClient, userIntelClient userintelPb.UserIntelServiceClient,
	inAppReferralClient inAppReferralPb.InAppReferralClient, actorClient actorPb.ActorClient, userGroupClient userGroupPb.GroupClient,
	userProc helper.UserProcessor, creditReportV2Client creditreportv2.CreditReportManagerClient, helper helper.OnboardingHelper,
	tieringClient tieringPb.TieringClient) *AppScreeningStage {

	// generic string to experiment function to map generic ab experiment types
	genericStringExprFn := func(str string) string { return str }
	// initializing ab evaluator for AFFLUENT_USER_BONUS_TRANSITION_SCREEN
	affluentUserBonusTransitionScreenABEvaluator := getABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, conf.ABFeatureReleaseConfig(), genericStringExprFn)
	// initializing ab evaluator for AFFLUENT_USER_BONUS_TRANSITION_SCREEN_NON_REFEREE
	affluentUserBonusTransitionScreenNonRefereeABEvaluator := getABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, conf.ABFeatureReleaseConfig(), genericStringExprFn)
	// initializing ab evaluator for SCREENER_CHOICE_PAGE
	screenerChoicePageABEvaluator := getABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, conf.ABFeatureReleaseConfig(), genericStringExprFn)

	return &AppScreeningStage{
		conf:                conf,
		eventBroker:         eventBroker,
		scrnrClient:         scrnrClient,
		empClient:           empClient,
		eventLogger:         eventLogger,
		onbDao:              onbDao,
		feEmpClient:         feEmpClient,
		userClient:          userClient,
		userIntelClient:     userIntelClient,
		inAppReferralClient: inAppReferralClient,
		userProc:            userProc,
		userGroupClient:     userGroupClient,
		affluentUserBonusTransitionScreenABEvaluator:           affluentUserBonusTransitionScreenABEvaluator,
		affluentUserBonusTransitionScreenNonRefereeABEvaluator: affluentUserBonusTransitionScreenNonRefereeABEvaluator,
		screenerChoicePageABEvaluator:                          screenerChoicePageABEvaluator,
		creditReportV2Client:                                   creditReportV2Client,
		helper:                                                 helper,
		tieringClient:                                          tieringClient,
	}
}

const facingTroubleText = "Facing trouble?"

func (s *AppScreeningStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb     = req.GetOnb()
		actorId = req.GetOnb().GetActorId()
	)

	s.handleMarketingEvents(ctx, onb, actorId)

	if onb.GetStageMetadata().GetUserAllowedToOpenAmbAccount() {
		_, err := screener.SkipScreening(ctx, s.onbDao, onb, s.userProc)
		if err != nil {
			logger.Error(ctx, "error while skipping screening", zap.Error(err))
			return nil, err
		}
	}

	if screener.IsScreenerCompletedOrSkipped(onb) {
		s.findAndLogScreenerSegmentOfUserV2(ctx, onb)
		return nil, NoActionError
	}

	userResp, userErr := s.userClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier:        &usersPb.GetUserRequest_Id{Id: req.GetOnb().GetUserId()},
		WantPhotoInBase64: false,
	})
	if err := epifigrpc.RPCError(userResp, userErr); err != nil {
		return nil, err
	}
	forceStudentEmail := screener.ForceScreenerForStudentProgramConstraints(userResp.GetUser())

	scrnrAttResp, scrnrAttErr := s.scrnrClient.GetScreenerAttemptsByActorId(ctx, &scrnrPb.GetScreenerAttemptsByActorIdRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(scrnrAttResp, scrnrAttErr); grpcErr != nil {
		logger.Error(ctx, "error faced while calling GetScreenerAttemptsByActorId", zap.Error(grpcErr))
		return nil, grpcErr
	}

	scrnrAtt := scrnrAttResp.GetScreenerAttempt()
	checkDetails := scrnrAttResp.GetChecksMap()

	if scrnrAtt.GetResultInfo().GetResult() == scrnrPb.ScreenerAttemptResult_SCREENER_ATTEMPT_RESULT_PASSED ||
		scrnrAtt.GetResultInfo().GetResult() == scrnrPb.ScreenerAttemptResult_SCREENER_ATTEMPT_RESULT_MANUALLY_PASSED {
		if !forceStudentEmail || lo.Contains[scrnrPb.CheckType](scrnrAtt.GetResultInfo().GetPassingStages(), scrnrPb.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION) {
			logger.Info(ctx, "user passed through screener v2")
			return nil, NoActionError
		}
	}

	isScreenerChoicePageEnabled := s.isScreenerChoicePageEnabled(ctx, actorId)
	logger.Info(ctx, "user failed screener in new income estimation flow")
	// if screener choice page is not enabled, show block screen
	if !isScreenerChoicePageEnabled {
		return &StageProcessorResponse{
			NextAction: deeplink.NewErrorFullScreen(ctx, &error2.ErrorScreenOpts{
				Title:          constants.ScreenerBlockTitle,
				Subtitle:       constants.ScreenerBlockSubtitle,
				HeaderImageURL: constants.ScreenerBlockImage,
				HasFeedback:    true,
			}),
		}, nil
	}

	if !isScreenerChoicePageEnabled {
		logger.Info(ctx, "user failed screener with old screen")
		return &StageProcessorResponse{
			NextAction: deeplink.NewErrorFullScreen(ctx, &error2.ErrorScreenOpts{
				Title:          constants.EKYCCertErrorTitle,
				Subtitle:       constants.EKYCCertErrorMessage,
				HeaderImageURL: constants.EKYCCertErrorImage,
			}),
		}, nil
	}

	getEmpInfoResp, errGetEmpInfo := s.empClient.GetEmploymentInfo(ctx, &empPb.GetEmploymentInfoRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(getEmpInfoResp, errGetEmpInfo); grpcErr != nil {
		if getEmpInfoResp.GetStatus().IsRecordNotFound() {
			return s.handleEmploymentInfoNotFound(ctx, onb)
		}
		logger.Error(ctx, "failed to get employment info for actor", zap.Error(grpcErr))
		return nil, grpcErr
	}

	// Build the CTA text based on employment type and salary (if applicable)
	empType := getEmpInfoResp.GetEmploymentData().GetEmploymentType()
	ctaText := fmt.Sprintf("You are %v ", strings.ReplaceAll(empType.String(), "_", " "))
	ctaText += getSalaryCtaString(getEmpInfoResp.GetEmploymentData().GetEmploymentInfo().GetAnnualSalary())

	dlResp, dlErr := s.feEmpClient.GetEmploymentDeclarationDL(ctx, &empPb.GetEmploymentDeclarationDLRequest{
		UpdateSource: empPb.UpdateSource_UPDATE_SOURCE_ONBOARDING,
		ScreenView:   dlPb.EmploymentDeclarationOptions_BOTTOM_SHEET,
		ActorId:      actorId,
	})
	if err := epifigrpc.RPCError(dlResp, dlErr); err != nil {
		logger.Error(ctx, "error while generating employment declaration DL", zap.Error(err))
		return nil, err
	}
	dl := dlResp.GetDeeplink()

	daysToExpiry := math.Ceil(time.Until(scrnrAtt.GetExpiry().AsTime()).Hours() / 24)
	if daysToExpiry < 0 {
		daysToExpiry = 0
	}

	logger.Info(ctx, "user failed screener with screener choice screen")
	if forceStudentEmail {
		return &StageProcessorResponse{
			NextAction: s.getStudentProgramDL(checkDetails, daysToExpiry, ctaText),
		}, nil
	}

	_, dl = s.getScreenerChoiceDL(ctx, scrnrAtt, checkDetails, daysToExpiry, dl, ctaText, empType, onb.GetFiLiteDetails().GetIsEnabled(), req.GetOnb())

	return &StageProcessorResponse{
		NextAction: dl,
	}, nil

}

func (s *AppScreeningStage) handleEmploymentInfoNotFound(ctx context.Context, onb *onbPb.OnboardingDetails) (*StageProcessorResponse, error) {
	logger.Info(ctx, "employment info not found in app screening stage", zap.String(logger.ACTOR_ID_V2, onb.GetActorId()))
	if _, err := s.helper.UpdateStageStatus(ctx, onb.GetActorId(), onbPb.OnboardingStage_EMPLOYMENT_VERIFICATION, onbPb.OnboardingState_RESET, onb); err != nil {
		logger.Error(ctx, "error while resetting employment verification stage", zap.Error(err))
		return nil, err
	}
	return &StageProcessorResponse{
		NextAction: deeplink.NewActionToGetNextAction(),
	}, nil
}

func getAffluentUserBonusTransitionScreenDl(screenType feUserOnbPb.BonusTransitionScreenType) (*dlPb.Deeplink, error) {
	screenTitleFontColour := "#000000"
	screenTitleFontStyle := commontypes.FontStyle_HEADLINE_L

	screenTitle, screenIcon, getScreenDetailsErr := getScreenDetailsForScreenType(screenType)
	if getScreenDetailsErr != nil {
		return nil, fmt.Errorf("error in getting screen details for screen type : %w", getScreenDetailsErr)
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_AFFLUENT_USER_BONUS_TRANSITION_SCREEN,
		ScreenOptions: &dlPb.Deeplink_AffluentUserTransitionScreenOptions{
			AffluentUserTransitionScreenOptions: &dlPb.AffluentUserBonusTransitionScreenOptions{
				ScreenTitle: commontypes.GetTextFromStringFontColourFontStyle(screenTitle, screenTitleFontColour, screenTitleFontStyle),
				ScreenIcon:  commontypes.GetImageFromUrl(screenIcon),
				ScreenType:  screenType.String(),
			},
		},
	}, nil
}

// gets the details of the screen based on the screen type to be returned
func getScreenDetailsForScreenType(screenType feUserOnbPb.BonusTransitionScreenType) (screenTitle string, screenIcon string, err error) {
	switch screenType {
	case feUserOnbPb.BonusTransitionScreenType_BONUS_TRANSITION_SCREEN_TYPE_AFFLUENT_REFEREE:
		screenTitle = "Surprise! We've doubled your joining bonus"
		screenIcon = "https://epifi-icons.pointz.in/referrals/affluent_user_bonus_screen_icon.png"
		err = nil
	case feUserOnbPb.BonusTransitionScreenType_BONUS_TRANSITION_SCREEN_TYPE_AFFLUENT_NON_REFEREE:
		screenTitle = "Surprise! We've increased your joining bonus"
		screenIcon = "https://epifi-icons.pointz.in/referrals/affluent_user_bonus_screen_icon.png"
		err = nil
	default:
		err = fmt.Errorf("screen details not assigned for screen type : %s", screenType.String())
	}

	return screenTitle, screenIcon, err
}

func (s *AppScreeningStage) getStudentProgramDL(checkDetails []*scrnrPb.CheckDetails, expiry float64, ctaText string) *dlPb.Deeplink {
	scrnrCheckChoices := getStudentProgramChoices(checkDetails, expiry)
	isAnyCheckAvailable := false
	for _, choice := range scrnrCheckChoices {
		if !choice.IsDisabled {
			isAnyCheckAvailable = true
			break
		}
	}

	screenTitle := "<b>Help us check your Fi eligibility</b>"
	screensSubTitle := "Tap any of the options below"
	dlOptions := &dlPb.ScreenerChoiceScreenOptions{
		Title:               screenTitle,
		Subtitle:            screensSubTitle,
		ScreenerCheckChoice: scrnrCheckChoices,
		Ctas: []*dlPb.Cta{
			{
				Type:         dlPb.Cta_CUSTOM,
				Text:         "Get help",
				DisplayTheme: dlPb.Cta_TERTIARY,
				Status:       dlPb.Cta_CTA_STATUS_ENABLED,
				Deeplink: &dlPb.Deeplink{
					Screen: dlPb.Screen_HELP_MAIN,
				},
			},
		},
		ShareFeedbackBlock: &dlPb.ScreenerChoiceScreenOptions_ShareFeedbackBlock{
			ImageUrl:               "https://epifi-icons.pointz.in/onboarding/message/message.png",
			Text:                   "Help us serve you better <font color=#00B899>Share feedback</font>",
			FeedbackPageIdentifier: app_feedback.AppScreen_APP_SCREEN_SCREENER_TERMINAL.String(),
		},
		CtaHeader:   facingTroubleText,
		CtaHeaderV2: commontypes.GetTextFromStringFontColourFontStyle(facingTroubleText, "#6A6D70", commontypes.FontStyle_SUBTITLE_XS),
	}

	if !isAnyCheckAvailable {
		dlOptions.WarningIcon = "https://epifi-icons.pointz.in/onboarding/warning_triangle.png"
		warningMessage := fmt.Sprintf("You've used up all attempts. Please try again after %v days", expiry)
		if expiry <= 1 {
			warningMessage = strings.TrimSuffix(warningMessage, "s")
		}
		dlOptions.WarningMessage = warningMessage
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_SCREENER_CHOICE,
		ScreenOptions: &dlPb.Deeplink_ScreenerChoiceOptions{
			ScreenerChoiceOptions: dlOptions,
		},
	}
}

func getStudentProgramChoices(checkDetails []*scrnrPb.CheckDetails, expiry float64) []*dlPb.ScreenerCheckChoice {
	var choices []*dlPb.ScreenerCheckChoice
	checkExpiryTitleString := fmt.Sprintf("You can retry verifying with this option in %v days", expiry)
	if expiry <= 1 {
		checkExpiryTitleString = strings.TrimSuffix(checkExpiryTitleString, "s")
	}

	check, ok := lo.Find[*scrnrPb.CheckDetails](checkDetails, func(check *scrnrPb.CheckDetails) bool {
		return check.GetCheckType() == scrnrPb.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION
	})
	if !ok || check.GetCheckResult() == scrnrPb.CheckResult_CHECK_RESULT_DISABLED {
		return choices
	}

	choice := &dlPb.ScreenerCheckChoice{
		Title:           "<b>Use official student email ID</b>",
		ImageUrl:        "https://epifi-icons.pointz.in/onboarding/email.png",
		CheckIdentifier: onbPb.OnboardingStage_WORK_EMAIL_VERIFICATION.String(),
	}
	attemptsLeftString := fmt.Sprintf("%v attempts left", check.GetRetriesLeft())
	if check.GetRetriesLeft() <= 1 {
		attemptsLeftString = strings.Replace(attemptsLeftString, "s left", " left", 1)
	}
	if check.GetRetriesLeft() <= 0 {
		choice.IsDisabled = true
		choice.Description = "No attempts left"
		choice.BottomSheet = &dlPb.GenericBottomSheet{
			Title:       checkExpiryTitleString,
			Description: "Or, you can tap any of the available options in the list and verify today",
		}
	} else {
		choice.Description = attemptsLeftString
	}
	choices = append(choices, choice)
	return choices
}

// nolint:funlen
func (s *AppScreeningStage) getScreenerChoiceDL(ctx context.Context, scrnrAtt *scrnrPb.ScreenerAttempt, checkDetails []*scrnrPb.CheckDetails, expiry float64,
	dl *dlPb.Deeplink, ctaText string, empType empPb.EmploymentType, isFiLite commontypes.BooleanEnum, onb *onbPb.OnboardingDetails) (hasFailedScreener bool, resDl *dlPb.Deeplink) {
	scrnrCheckChoices := s.getScreenerCheckChoices(checkDetails, expiry, empType)

	isAnyCheckAvailable := false
	for _, choice := range scrnrCheckChoices {
		if !choice.IsDisabled {
			isAnyCheckAvailable = true
			break
		}
	}

	screenTitle := "<b>Help us verify your work status</b>"
	screensSubTitle := "Fi is for working professionals! Tap on any of the options below to verify you are one"
	if empType == empPb.EmploymentType_FREELANCER ||
		empType == empPb.EmploymentType_STUDENT ||
		empType == empPb.EmploymentType_HOMEMAKER ||
		empType == empPb.EmploymentType_RETIRED ||
		empType == empPb.EmploymentType_OTHERS {
		screenTitle = "Help us check your Fi eligibility"
		screensSubTitle = "Tap any of the options below"
	}

	var ctas []*dlPb.Cta

	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.conf.EditEmploymentInScreener()) {
		ctas = []*dlPb.Cta{
			{
				Type:         dlPb.Cta_CUSTOM,
				Text:         ctaText,
				DisplayTheme: dlPb.Cta_TERTIARY,
				Status:       dlPb.Cta_CTA_STATUS_ENABLED,
				Deeplink:     dl,
			},
		}
	} else if s.conf.AppScreeningConfig().EnableChatbotInChoiceScreen() {
		ctas = []*dlPb.Cta{
			{
				Type:         dlPb.Cta_CUSTOM,
				Text:         "Get help",
				DisplayTheme: dlPb.Cta_TERTIARY,
				Status:       dlPb.Cta_CTA_STATUS_ENABLED,
				Deeplink: &dlPb.Deeplink{
					Screen: dlPb.Screen_HELP_MAIN,
				},
			},
		}
	}

	dlOptions := &dlPb.ScreenerChoiceScreenOptions{
		Title:               screenTitle,
		Subtitle:            screensSubTitle,
		ScreenerCheckChoice: scrnrCheckChoices,
		Ctas:                ctas,
		ShareFeedbackBlock: &dlPb.ScreenerChoiceScreenOptions_ShareFeedbackBlock{
			ImageUrl:               "https://epifi-icons.pointz.in/onboarding/message/message.png",
			Text:                   "Help us serve you better <font color=#00B899>Share feedback</font>",
			FeedbackPageIdentifier: app_feedback.AppScreen_APP_SCREEN_SCREENER_TERMINAL.String(),
		},
		CtaHeader:   facingTroubleText,
		CtaHeaderV2: commontypes.GetTextFromStringFontColourFontStyle(facingTroubleText, "#6A6D70", commontypes.FontStyle_SUBTITLE_XS),
	}

	if !isAnyCheckAvailable {
		dlOptions.WarningIcon = "https://epifi-icons.pointz.in/onboarding/warning_triangle.png"
		warningMessage := "Uh oh...you cannot open a Savings Account via Fi. Still want to open a bank account? Visit your nearest Federal Bank branch or check out their website."
		dlOptions.WarningMessage = warningMessage
	}

	if isFiLiteEnabled(ctx, s.userGroupClient, s.userProc, onb, s.conf, false) && s.conf.AppScreeningConfig().EnableFiLiteEntryPointInChoiceScreen() {
		dlOptions.ShareFeedbackBlock = nil
		// for the screener choices screen, both primary cta and back action has the same action, which is either fi lite drop off screen or home screen
		if isFiLite == commontypes.BooleanEnum_TRUE {
			backActionDl := actionAfterOnboardingComplete
			ctas = append(ctas, &dlPb.Cta{
				Type:         dlPb.Cta_CUSTOM,
				Text:         "I'll do this later",
				Deeplink:     backActionDl,
				DisplayTheme: dlPb.Cta_PRIMARY,
				Status:       dlPb.Cta_CTA_STATUS_ENABLED,
			})
			dlOptions.BackAction = &dlPb.BackAction{
				Deeplink: backActionDl,
			}
		}
	}

	// marking as screener failed if no checks are remaining
	hasFailedScreener = !isAnyCheckAvailable

	return hasFailedScreener, &dlPb.Deeplink{
		Screen: dlPb.Screen_SCREENER_CHOICE,
		ScreenOptions: &dlPb.Deeplink_ScreenerChoiceOptions{
			ScreenerChoiceOptions: dlOptions,
		},
	}
}

// nolint: funlen,dupl
func (s *AppScreeningStage) getScreenerCheckChoices(checkDetails []*scrnrPb.CheckDetails, expiry float64, empType empPb.EmploymentType) []*dlPb.ScreenerCheckChoice {
	type UIElements struct {
		Title           string
		ImageUrl        string
		CheckIdentifier string
	}

	uiMap := map[scrnrPb.CheckType]*UIElements{
		scrnrPb.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION: {
			Title:           "<b>Use official work email ID</b>",
			ImageUrl:        "https://epifi-icons.pointz.in/onboarding/email.png",
			CheckIdentifier: onbPb.OnboardingStage_WORK_EMAIL_VERIFICATION.String(),
		},
		scrnrPb.CheckType_CHECK_TYPE_GMAIL_INSIGHTS: {
			Title:           "<b>Link Gmail Account</b>",
			ImageUrl:        "https://epifi-icons.pointz.in/onboarding/gmail.png",
			CheckIdentifier: onbPb.OnboardingStage_GMAIL_VERIFICATION.String(),
		},
		scrnrPb.CheckType_CHECK_TYPE_EPFO: {
			Title:           "<b>Search employer name</b>",
			ImageUrl:        "https://epifi-icons.pointz.in/onboarding/employer.png",
			CheckIdentifier: onbPb.OnboardingStage_EPFO_COMPANY_SEARCH.String(),
		},
		scrnrPb.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS: {
			Title:           "<b>Connect another bank account</b>",
			ImageUrl:        "https://epifi-icons.pointz.in/onboarding/generic_bank.png",
			CheckIdentifier: onbPb.OnboardingStage_CONNECTED_ACCOUNTS.String(),
		},
		scrnrPb.CheckType_CHECK_TYPE_CREDIT_REPORT: {
			Title:           "<b>Use credit report</b>",
			ImageUrl:        "https://epifi-icons.pointz.in/onboarding/credit_meter/credit_meter.png",
			CheckIdentifier: onbPb.OnboardingStage_CREDIT_REPORT_VERIFICATION.String(),
		},
		scrnrPb.CheckType_CHECK_TYPE_ITR_INTIMATION: {
			Title:           "<b>Upload ITR intimation",
			ImageUrl:        "https://epifi-icons.pointz.in/onboarding/itr-int-page.png",
			CheckIdentifier: onbPb.OnboardingStage_ITR_INTIMATION_VERIFICATION.String(),
		},
		scrnrPb.CheckType_CHECK_TYPE_INCOME_ESTIMATE: {
			Title:           "<b>Use credit report</b>",
			ImageUrl:        "https://epifi-icons.pointz.in/onboarding/credit_meter/credit_meter.png",
			CheckIdentifier: onbPb.OnboardingStage_INCOME_ESTIMATE_CHECK.String(),
		},
	}

	var choices []*dlPb.ScreenerCheckChoice
	checkExpiryTitleString := fmt.Sprintf("You can retry verifying with this option in %v days", expiry)
	if expiry <= 1 {
		checkExpiryTitleString = strings.TrimSuffix(checkExpiryTitleString, "s")
	}

	for _, check := range checkDetails {
		uiElement, ok := uiMap[check.GetCheckType()]
		if !ok {
			continue
		}

		if check.GetCheckResult() == scrnrPb.CheckResult_CHECK_RESULT_DISABLED {
			continue
		}

		if !s.isCheckValid(check.GetCheckType(), empType) {
			continue
		}

		if check.GetCheckType() == scrnrPb.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION &&
			empType == empPb.EmploymentType_STUDENT {
			uiElement.Title = "<b>Use official student email ID</b>"
		}

		choice := &dlPb.ScreenerCheckChoice{
			Title:           uiElement.Title,
			ImageUrl:        uiElement.ImageUrl,
			CheckIdentifier: uiElement.CheckIdentifier,
		}
		attemptsLeftString := fmt.Sprintf("%v attempts left", check.GetRetriesLeft())
		if check.GetRetriesLeft() <= 1 {
			attemptsLeftString = strings.Replace(attemptsLeftString, "s left", " left", 1)
		}
		if check.GetRetriesLeft() <= 0 {
			choice.IsDisabled = true
			choice.Description = "No attempts left"
			choice.BottomSheet = &dlPb.GenericBottomSheet{
				Title:       checkExpiryTitleString,
				Description: "Or, you can tap any of the available options in the list and verify today",
			}
		} else {
			choice.Description = attemptsLeftString
		}
		if check.GetCheckType() == scrnrPb.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS {
			switch check.GetCheckResult() {
			case scrnrPb.CheckResult_CHECK_RESULT_IN_PROGRESS:
				choice.ShowInfoBottomSheet = true
				choice.BottomSheetFooterImageUrl = constant.PoweredByEpifiWealthIconURL
				choice.BottomSheet = &dlPb.GenericBottomSheet{
					Title:       constant.CheckInProgressTitle,
					Description: constant.CheckInProgressDescription,
				}
				choice.Status = &dlPb.IconTextWidget{
					LeftImg: &commontypes.Image{
						ImageUrl: constant.ClockIconURL,
						Width:    16,
						Height:   16,
					},
					Text:    commontypes.GetTextFromHtmlStringFontColourFontStyle("Verification in progress", "#525355", commontypes.FontStyle_BODY_XS),
					BgColor: constant.BgColorInProgress,
				}
				choice.Description = ""
			case scrnrPb.CheckResult_CHECK_RESULT_FAILED:
				if check.GetRetriesLeft() > 0 {
					break
				}
				choice.Status = &dlPb.IconTextWidget{
					LeftImg: &commontypes.Image{
						ImageUrl: constant.WarningIconURL,
						Width:    16,
						Height:   16,
					},
					Text:    commontypes.GetTextFromHtmlStringFontColourFontStyle("Verification failed", "#525355", commontypes.FontStyle_BODY_XS),
					BgColor: constant.BgColorFailed,
				}
				choice.Description = ""
			default:
				break
			}
		}
		if check.GetCheckType() == scrnrPb.CheckType_CHECK_TYPE_INCOME_ESTIMATE {
			switch check.GetCheckResult() {
			case scrnrPb.CheckResult_CHECK_RESULT_IN_PROGRESS:
				choice.ShowInfoBottomSheet = true
				choice.BottomSheet = &dlPb.GenericBottomSheet{
					Title:       constant.CreditReportCheckInProgressTitle,
					Description: constant.CreditReportCheckInProgressDescription,
				}
				choice.Status = &dlPb.IconTextWidget{
					LeftImg: &commontypes.Image{
						ImageUrl: constant.ClockIconURL,
						Width:    16,
						Height:   16,
					},
					Text:    commontypes.GetTextFromHtmlStringFontColourFontStyle("Verification in progress", "#525355", commontypes.FontStyle_BODY_XS),
					BgColor: constant.BgColorInProgress,
				}
				choice.Description = ""
			case scrnrPb.CheckResult_CHECK_RESULT_FAILED:
				choice.Status = &dlPb.IconTextWidget{
					LeftImg: &commontypes.Image{
						ImageUrl: constant.WarningIconURL,
						Width:    16,
						Height:   16,
					},
					Text:    commontypes.GetTextFromHtmlStringFontColourFontStyle("Verification failed", "#525355", commontypes.FontStyle_BODY_XS),
					BgColor: constant.BgColorFailed,
				}
				choice.Description = ""
			default:
				break
			}
		}
		choices = append(choices, choice)
	}
	return choices
}

func (s *AppScreeningStage) isCheckValid(checkType scrnrPb.CheckType, empType empPb.EmploymentType) bool {
	switch checkType {
	case scrnrPb.CheckType_CHECK_TYPE_EPFO:
		return empType == empPb.EmploymentType_SALARIED
	case scrnrPb.CheckType_CHECK_TYPE_WORK_EMAIL_VERIFICATION:
		return empType == empPb.EmploymentType_SALARIED ||
			empType == empPb.EmploymentType_BUSINESS_OWNER ||
			empType == empPb.EmploymentType_FREELANCER ||
			empType == empPb.EmploymentType_WORKING_PROFESSIONAL ||
			empType == empPb.EmploymentType_STUDENT
	case scrnrPb.CheckType_CHECK_TYPE_CREDIT_REPORT:
		return false
	default:
		return true
	}
}

func (s *AppScreeningStage) findAndLogScreenerSegmentOfUserV2(ctx context.Context, onb *onbPb.OnboardingDetails) {

	var (
		actorId          = onb.GetActorId()
		appScreeningData = onb.GetStageMetadata().GetAppScreeningData()
		creditResV2      = &creditreportv2.GetCreditReportResponse{}
		err              error
	)
	isTrue := func(b commontypes.BooleanEnum) bool {
		return b == commontypes.BooleanEnum_TRUE
	}

	// fetching user's credit report
	if isTrue(appScreeningData.GetCreditReportVerificationPassed()) {
		creditResV2, err = s.creditReportV2Client.GetCreditReport(ctx, &creditreportv2.GetCreditReportRequest{ActorId: actorId})
		if err = epifigrpc.RPCError(creditResV2, err); err != nil {
			logger.Error(ctx, "failed to get credit report of user", zap.Error(err))
		}
	}

	// fetch user's device id
	devRes, err := s.userClient.GetUserDeviceProperties(ctx, &usersPb.GetUserDevicePropertiesRequest{
		ActorId: actorId,
		PropertyTypes: []types.DeviceProperty{
			types.DeviceProperty_DEVICE_PROP_DEVICE_ID,
		},
	})
	if err = epifigrpc.RPCError(devRes, err); err != nil {
		logger.Error(ctx, "failed to get device details", zap.Error(err))
	}
	deviceId := devRes.GetPropValue(types.DeviceProperty_DEVICE_PROP_DEVICE_ID).GetDeviceId()

	switch {
	// Check if credit report is found and secured loan and score > 750 and PAN = onboarding PAN
	case isTrue(appScreeningData.GetCreditReportVerificationPassed()) &&
		creditResV2.GetCreditReportData().GetCreditScore() > 750:
		logger.Info(ctx, "User in segment1")
		LogScreenerSegmentServerEvent(ctx, s.eventBroker, actorId, userEvents.ScreenerSegment1, deviceId)
	case isTrue(appScreeningData.GetScreeningPassed()):
		logger.Info(ctx, "User in segment3")
		LogScreenerSegmentServerEvent(ctx, s.eventBroker, actorId, userEvents.ScreenerSegment3, deviceId)
	default:
		logger.Info(ctx, "User in segment4")
		LogScreenerSegmentServerEvent(ctx, s.eventBroker, actorId, userEvents.ScreenerSegment4, deviceId)
	}
}

func (s *AppScreeningStage) handleMarketingEvents(ctx context.Context, onb *onbPb.OnboardingDetails, actorId string) {
	if s.conf.Flags().EnableAffluenceV2() {
		s.eventLogger.LogAffluenceHeuristicsV3(ctx, actorId)
		s.eventLogger.LogLendability(ctx, actorId)
	}
}

func getSalaryCtaString(salary *feScreeningPb.AnnualSalary) string {
	switch {
	case salary.GetRange() != nil:
		rangeString := pkgEmp.GetIncomeStringFromRange(&usersPb.SalaryRange{
			MinValue: salary.GetRange().GetMinValue(),
			MaxValue: salary.GetRange().GetMaxValue(),
		})
		return fmt.Sprintf("earning %s/year", rangeString)
	case salary.GetAbsolute() != 0:
		return fmt.Sprintf("earning ₹%.0f/year", salary.GetAbsolute())
	default:
		return ""
	}
}

// isScreenerChoicePageEnabled checks if screener choice page is enabled or not
func (s *AppScreeningStage) isScreenerChoicePageEnabled(ctx context.Context, actorId string) bool {
	screenEnabled, variant, err := s.screenerChoicePageABEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_SCREENER_CHOICE_PAGE).WithActorId(actorId))
	if err != nil {
		logger.WarnWithCtx(ctx, "error evaluating AB for screener choice page, ignoring silently", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return screenEnabled && variant == "ONE"
}
