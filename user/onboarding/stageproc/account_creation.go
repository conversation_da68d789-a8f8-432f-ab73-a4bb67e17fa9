package stageproc

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/gamma/user/onboarding/helper/deeplink"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/savings"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
)

type AccountCreationStage struct {
	onboardingDao  dao.OnboardingDao
	dynConf        *genconf.OnboardingConfig
	savingsClient  savings.SavingsClient
	authClient     auth.AuthClient
	eventLogger    userEvents.EventLogger
	bankCustClient bankcust.BankCustomerServiceClient
}

func NewAccountCreationStage(dynConf *genconf.OnboardingConfig, onboardingDao dao.OnboardingDao, savingsClient savings.SavingsClient,
	authClient auth.AuthClient, eventLogger userEvents.EventLogger, bankCustClient bankcust.BankCustomerServiceClient) *AccountCreationStage {
	return &AccountCreationStage{
		dynConf:        dynConf,
		onboardingDao:  onboardingDao,
		savingsClient:  savingsClient,
		authClient:     authClient,
		eventLogger:    eventLogger,
		bankCustClient: bankCustClient,
	}
}

var (
	accountStatusToOnboardingStatusMapping = map[savings.State]onbPb.OnboardingState{
		savings.State_STATE_UNSPECIFIED:          onbPb.OnboardingState_UNSPECIFIED,
		savings.State_INITIATED:                  onbPb.OnboardingState_INITIATED,
		savings.State_IN_PROGRESS:                onbPb.OnboardingState_INPROGRESS,
		savings.State_CREATED:                    onbPb.OnboardingState_SUCCESS,
		savings.State_MAX_RETRIES_CHECK_STATUS:   onbPb.OnboardingState_MANUAL_INTERVENTION,
		savings.State_MAX_RETRIES_CREATE_ACCOUNT: onbPb.OnboardingState_MANUAL_INTERVENTION,
		savings.State_FAILED:                     onbPb.OnboardingState_FAILURE,
		savings.State_SUSPENDED:                  onbPb.OnboardingState_FAILURE,
	}
	errInvalidDedupeInAcctCreation = errors.New("invalid dedupe status received in CreateAccount")
)

func (a *AccountCreationStage) StageProcessor(ctx context.Context, request *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		err      error
		response = &StageProcessorResponse{}
		onb      = request.GetOnb()
		actorId  = onb.GetActorId()
	)
	if errStatus := updateFeatureOnbStatus(ctx, a.onboardingDao, onb, onbPb.Feature_FEATURE_SA, onbPb.FeatureStatus_FEATURE_STATUS_ONBOARDING_IN_PROGRESS, nil); errStatus != nil {
		logger.Error(ctx, "failed to update feature onb status", zap.Error(errStatus))
	}
	// Account creation
	onb, err = a.processAccountCreation(ctx, onb)
	// we have changed definition of access tokens: REGISTERED status of token meaning device registered + account is created
	// so, we expire access tokens upon account creation success, deleting unregistered tokens
	// client will create new tokens with registered status as the account is created
	_ = a.expireAccessTokens(ctx, actorId, err)
	if IsOrchestratorAction(err) {
		return nil, err
	}
	if !getStageStatus(onb, onbPb.OnboardingStage_ACCOUNT_CREATION).IsSuccessOrSkipped() || err != nil {
		if errors.Is(err, errInvalidDedupeInAcctCreation) {
			return &StageProcessorResponse{
				// Return generic dedupe error screen.
				// TODO(Shivam): Return screen based on dedupe status
				NextAction: getDedupeErrorDL(ctx, 0),
			}, nil
		}
		if IsUserStuck(ctx, onb, a.dynConf.AccountSetupMaxStuckDuration()) {
			return &StageProcessorResponse{
				NextAction: ActionForUserStuckOnAcctCreation(ctx, onb),
			}, err
		}
		response.NextAction = deeplink.NewActionToAccountCreationProgress()
		return response, err
	}

	a.triggerAffluentAccountEventV2(ctx, actorId)
	a.eventLogger.LogSAConverted(ctx, onb)
	a.eventLogger.LogLendableAccountCreationEvent(ctx, actorId)
	return response, NoActionError
}

func (a *AccountCreationStage) processAccountCreation(ctx context.Context, onb *onbPb.OnboardingDetails) (*onbPb.OnboardingDetails, error) {
	if getStageStatus(onb, onbPb.OnboardingStage_ACCOUNT_CREATION).IsSuccessOrSkipped() {
		return onb, nil
	}
	bcResp, errResp := a.bankCustClient.CheckBankCustomerCreationStatus(ctx, &bankcust.CheckBankCustomerCreationStatusRequest{
		ActorId: onb.GetActorId(),
		Vendor:  onb.GetVendor(),
	})
	if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
		// If customer is not yet created, ideally a user should not reach here. So we return an error.
		logger.Error(ctx, "error in checking for bank customer status", zap.Error(err))
		return nil, err
	}

	savingsResponse, errResp := a.savingsClient.GetSavingsAccountEssentials(ctx, &savings.GetSavingsAccountEssentialsRequest{
		Filter: &savings.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savings.ActorIdBankFilter{
				ActorId:     onb.GetActorId(),
				PartnerBank: onb.GetVendor(),
			},
		},
	})
	if err := epifigrpc.RPCError(savingsResponse, errResp); err != nil {
		// If account does not exist in DB yet, we call CreateAccount to start account creation process.
		if savingsResponse.GetStatus().IsRecordNotFound() {
			acct, errAc := a.createAccount(ctx, onb)
			if errAc != nil {
				return nil, errAc
			}
			return a.updateStageStatus(ctx, onb, acct.GetState(), acct.GetAccountNo(), acct.GetId())
		}
		logger.Error(ctx, "failed to get account savings account details", zap.Error(err))
		return nil, err
	}
	return a.updateStageStatus(ctx, onb, savingsResponse.GetAccount().GetState(), savingsResponse.GetAccount().GetAccountNo(), savingsResponse.GetAccount().GetId())
}

func (a *AccountCreationStage) createAccount(ctx context.Context, onb *onbPb.OnboardingDetails) (*savings.Account, error) {
	userId, actorId := onb.GetUserId(), onb.GetActorId()

	var sku savings.SKU
	if onb.GetFeature().IsNonResidentUserOnboarding() {
		sku = savings.SKU_NRE_ORIGINAL
	}
	createResp, errResp := a.savingsClient.CreateAccount(ctx, &savings.CreateAccountRequest{
		PrimaryAccountHolderId: userId,
		ActorId:                actorId,
		Sku:                    sku,
		Options:                &savings.CreateAccountRequest_Options{ShouldOpenAmbAccount: onb.GetStageMetadata().GetUserAllowedToOpenAmbAccount()},
	})
	if err := epifigrpc.RPCError(createResp, errResp); err != nil {
		if createResp.GetStatus().GetCode() == uint32(savings.CreateAccountResponse_INVALID_DEDUPE_STATUS) {
			return nil, errInvalidDedupeInAcctCreation
		}
		logger.Error(ctx, "error in fetching savings account", zap.Error(err))
		return nil, err
	}
	return createResp.GetAccount(), nil
}

func (a *AccountCreationStage) updateStageStatus(ctx context.Context, onb *onbPb.OnboardingDetails, state savings.State, accountNo, accountId string) (*onbPb.OnboardingDetails, error) {
	var err error
	currentState := getStageStatus(onb, onbPb.OnboardingStage_ACCOUNT_CREATION)
	newState := accountStatusToOnboardingStatusMapping[state]
	logger.Debug(ctx, fmt.Sprintf("ACCOUNT_CREATION -> currentState: %v; newState: %v", currentState.String(), newState.String()))
	if currentState != newState {
		if newState == onbPb.OnboardingState_SUCCESS {
			onb.AccountInfo = &onbPb.AccountInformationInternal{
				AccountNumber: accountNo,
				AccountId:     accountId,
				IsPrimary:     true, // TODO (keerthana): update logic for if primary account
			}
			if err = a.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_ACCOUNT_INFO}, onb); err != nil {
				logger.Error(ctx, "failed to update account info of user", zap.Error(err))
			}
			a.triggerAffluentAccountEventV2(ctx, onb.GetActorId())
			a.eventLogger.LogSAConverted(ctx, onb)
			a.eventLogger.LogLendableAccountCreationEvent(ctx, onb.GetActorId())
			return nil, NoActionError
		}

		onb, err = a.onboardingDao.UpdateStatus(ctx, onb.GetOnboardingId(), onbPb.OnboardingStage_ACCOUNT_CREATION, newState)
		if err != nil {
			logger.Error(ctx, "failed to update onboarding status", zap.Error(err), logOnb(onb.GetOnboardingId()))
			return nil, err
		}
	}
	return onb, nil
}

func (a *AccountCreationStage) expireAccessTokens(ctx context.Context, actorId string, orchErr error) error {
	if !isExpiryValid(orchErr) {
		return nil
	}

	updRes, err := a.authClient.UpdateToken(ctx, &auth.UpdateTokenRequest{
		Status: auth.UpdateTokenRequest_DELETE,
		Identifier: &auth.UpdateTokenRequest_ActorId{
			ActorId: actorId,
		},
		TokenTypes: []auth.TokenType{
			auth.TokenType_ACCESS_TOKEN,
		},
		TokenUpdationReason: auth.TokenDeletionReason_TOKEN_DELETION_REASON_ONBOARDING_TOKEN_UPGRADE,
	})
	if grpcErr := epifigrpc.RPCError(updRes, err); grpcErr != nil {
		logger.Error(ctx, "unable to delete access tokens", zap.Error(grpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return grpcErr
	}

	return nil
}

func isExpiryValid(err error) bool {
	if errors.Is(err, NoActionError) || errors.Is(err, SkipStageError) {
		return true
	}
	return false
}

func (a *AccountCreationStage) triggerAffluentAccountEventV2(ctx context.Context, actorId string) {
	a.eventLogger.LogAffluenceAccountCreationEventV2(ctx, actorId)
}
