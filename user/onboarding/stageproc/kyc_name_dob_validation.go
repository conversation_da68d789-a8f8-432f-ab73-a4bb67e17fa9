package stageproc

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/kyc"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	ekycPb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	"github.com/epifi/gamma/pkg/obfuscator"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

type KYCNameDOBValidation struct {
	kycClient       kyc.KycClient
	vgEkycClient    ekycPb.EKYCClient
	onboardingDao   dao.OnboardingDao
	conf            *genconf.OnboardingConfig
	userProc        helper.UserProcessor
	userGroupClient userGroupPb.GroupClient
	eventLogger     userEvents.EventLogger
}

func NewKYCNameDOBValidationStage(kycClient kyc.KycClient, vgEkycClient ekycPb.EKYCClient, onboardingDao dao.OnboardingDao,
	conf *genconf.OnboardingConfig, userProc helper.UserProcessor, userGroupClient userGroupPb.GroupClient,
	eventLogger userEvents.EventLogger) *KYCNameDOBValidation {
	return &KYCNameDOBValidation{
		kycClient:       kycClient,
		vgEkycClient:    vgEkycClient,
		onboardingDao:   onboardingDao,
		conf:            conf,
		userProc:        userProc,
		userGroupClient: userGroupClient,
		eventLogger:     eventLogger,
	}
}

func (s *KYCNameDOBValidation) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb      = req.Onb
		stage    = onbPb.OnboardingStage_KYC_NAME_DOB_VALIDATION
		response = &StageProcessorResponse{}
	)
	// we do not have to do EKYC Name DOB Validation if the user was a dedupe existing federal user
	if getStageStatus(onb, onbPb.OnboardingStage_CUSTOMER_CREATION).IsSuccessOrSkipped() {
		logger.Info(ctx, "skipping kyc name dob validation because customer creation is skipped or success")
		return response, SkipStageError
	}

	if getStageStatus(onb, stage) == onbPb.OnboardingState_MANUAL_INTERVENTION {
		response.NextAction = deeplink.NewErrorFullScreen(ctx, error2.EKYCNameDobValidationError)
		return response, nil
	}

	// Get kyc record to collect data for vendor call
	kycRes, err := s.getKYCRecord(ctx, onb.GetActorId())
	if err != nil {
		return response, err
	}

	// ensure kyc type is ekyc
	if kycRes.GetKycType() != kyc.KycType_EKYC && kycRes.GetKycType() != kyc.KycType_BKYC {
		logger.Info(ctx, fmt.Sprintf("skipping kyc name dob validation because type: %v", kycRes.GetKycType()))
		return response, SkipStageError
	}

	dob, err := s.getUserDOB(ctx, onb.GetActorId())
	if err != nil {
		return nil, err
	}

	// make vendor call for name dob validation
	res, err := s.vgEkycClient.NameDobValidationForEkyc(ctx, &ekycPb.NameDobValidationForEkycRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: onb.GetVendor(),
		},
		UidReferenceKey: kycRes.GetKycRecord().GetUidReferenceKey(),
		CustomerName:    kycRes.GetKycRecord().GetName(),
		Dob:             dob,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in kyc name dob validation", zap.Error(err))
		return response, fmt.Errorf("error in kyc name dob validation: %w", err)

	case res.GetStatus().IsSuccess():
		if errUpdate := s.storePanAadharLinkageDetails(ctx, onb, res.GetAadharLast4Digits()); errUpdate != nil {
			logger.Error(ctx, "faced error while storing pan aadhar linkage details", zap.Error(errUpdate))
			return nil, errUpdate
		}
		if len(strings.TrimSpace(res.GetRawResponse())) == 0 {
			return nil, fmt.Errorf("empty raw response for ekyc name dob validation")
		}
		if errUpdate := updateEkycNameDobValidationDataInTx(ctx, s.onboardingDao, onb.GetOnboardingId(), &onbPb.EKYCNameDOBValidationData{
			RawResponse: res.GetRawResponse(),
		}); errUpdate != nil {
			logger.Error(ctx, "faced error while storing ekyc namedob validation details", zap.Error(errUpdate))
			return nil, errUpdate
		}
		return response, NoActionError

	case res.GetStatus().IsAborted():
		return s.handleAbortedStatus(ctx, onb, res)
	default:
		logger.Error(ctx, "invalid res status in kyc name dob validation", zap.Error(rpc.StatusAsError(res.GetStatus())))
		return response, fmt.Errorf("invalid status in kyc name dob validation: %v", res.GetStatus().String())
	}
}

func (s *KYCNameDOBValidation) getKYCRecord(ctx context.Context, actorId string) (*kyc.GetKYCRecordResponse, error) {

	// Get kyc record to collect data for vendor call
	kycRes, err := s.kycClient.GetKYCRecord(ctx, &kyc.GetKYCRecordRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(kycRes, err); err != nil {
		if kycRes.GetStatus().IsRecordNotFound() {
			return nil, NoActionSkipStatusUpdateError
		}
		logger.Error(ctx, "error in get kyc record", zap.Error(err))
		return nil, err
	}
	return kycRes, nil
}

func (s *KYCNameDOBValidation) handleAbortedStatus(ctx context.Context, onb *onbPb.OnboardingDetails, res *ekycPb.NameDobValidationForEkycResponse) (*StageProcessorResponse, error) {
	logger.Info(ctx, "kyc name dob validation failed", zap.String(logger.RESPONSE_REASON, res.GetStatus().GetShortMessage()))
	_ = updateEkycNameDobValidationDataInTx(ctx, s.onboardingDao, onb.GetOnboardingId(), &onbPb.EKYCNameDOBValidationData{
		FailureDesc: fmt.Sprintf("%v: %v", res.GetStatus().GetCode(), res.GetStatus().GetShortMessage()),
		RawResponse: res.GetRawResponse(),
	})
	_, _ = updateStatus(ctx, s.onboardingDao, onb.OnboardingId, onbPb.OnboardingStage_KYC_NAME_DOB_VALIDATION, onbPb.OnboardingState_MANUAL_INTERVENTION)

	if isFiLiteEnabled(ctx, s.userGroupClient, s.userProc, onb, s.conf, false) {
		return transitionToFiLite(ctx, s.onboardingDao, s.eventLogger, onb, onbPb.FiLiteSource_FI_LITE_SOURCE_EKYC_NAME_DOB_VALIDATION) // Reusing this for now. TODO(Ankit): Update if needed
	}
	return &StageProcessorResponse{
		NextAction: deeplink.NewErrorFullScreen(ctx, error2.EKYCNameDobValidationError),
	}, nil
}

func (s *KYCNameDOBValidation) getUserDOB(ctx context.Context, actorId string) (*date.Date, error) {
	user, err := s.userProc.GetUserByActorId(ctx, actorId)
	if err != nil {
		return nil, err
	}
	return user.GetProfile().GetDateOfBirth(), nil
}

func (s *KYCNameDOBValidation) storePanAadharLinkageDetails(ctx context.Context, onb *onbPb.OnboardingDetails, aadhar4digits string) error {
	if len(strings.TrimSpace(aadhar4digits)) == 0 {
		logger.Info(ctx, "received empty last 4 digits of aadhar")
		return nil
	}

	panAadharLinkageDetails := onb.GetPanAadharLinkageDetails()
	hashedDigits := obfuscator.Hashed(aadhar4digits)
	last2Digits := obfuscator.Hashed(aadhar4digits[2:])

	if panAadharLinkageDetails == nil {
		panAadharLinkageDetails = &onbPb.PanAadharLinkageDetails{
			EkycNameDobValidationAadharDigitsHash:      hashedDigits,
			EkycNameDobValidationAadharLast2DigitsHash: last2Digits,
		}
	} else {
		// Setting default values for cases where we need to store fresh details after downloading new KYC data
		panAadharLinkageDetails.IsAadharDigitsHashMismatch = commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED
		panAadharLinkageDetails.PanAadhaarLinked = commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED
		panAadharLinkageDetails.PanLinkedAadharDigitsHash = ""
		panAadharLinkageDetails.EkycNameDobValidationAadharDigitsHash = hashedDigits
		panAadharLinkageDetails.EkycNameDobValidationAadharLast2DigitsHash = last2Digits
	}

	onb.PanAadharLinkageDetails = panAadharLinkageDetails

	if errUpdate := s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, onb); errUpdate != nil {
		logger.Error(ctx, "failed while update onboarding details", zap.Error(errUpdate))
		return errUpdate
	}

	return nil
}
