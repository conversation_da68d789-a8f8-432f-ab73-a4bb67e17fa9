package stageproc

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/monitoring"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	appFbPb "github.com/epifi/gamma/api/inapphelp/app_feedback"
	"github.com/epifi/gamma/api/typesv2"
	usersPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	"github.com/epifi/gamma/pkg/countrystdinfo"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
	"github.com/epifi/gamma/user/onboarding/pkg/nri"
)

type NRLocationCheckStage struct {
	onbConf    *genconf.OnboardingConfig
	locClient  location.LocationClient
	obfClient  obfuscator.ObfuscatorClient
	userClient usersPb.UsersClient
}

func NewNRLocationCheckStage(locClient location.LocationClient, obfClient obfuscator.ObfuscatorClient, userClient usersPb.UsersClient,
	onbConf *genconf.OnboardingConfig) *NRLocationCheckStage {
	return &NRLocationCheckStage{
		locClient:  locClient,
		obfClient:  obfClient,
		userClient: userClient,
		onbConf:    onbConf,
	}
}

var _ StageProcessorType = (*NRLocationCheckStage)(nil)

func (s *NRLocationCheckStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	if isNonProdEnv() && s.onbConf.Flags().SkipLocationCheckForNROnboarding() {
		return s.mockProcessor(ctx)
	}

	country, err := getUserCountryFromIp(ctx, s.locClient, s.obfClient)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return handleGetUserCountryFromIpError(ctx, err)
	}

	// If country is not found by IP, we do not penalise and allow user through.
	if country == "" {
		return nil, SkipStageError
	}

	isNrUserRes, err := s.userClient.IsNonResidentUser(ctx, &usersPb.IsNonResidentUserRequest{
		Identifier: &usersPb.IsNonResidentUserRequest_ActorId{
			ActorId: req.GetOnb().GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(isNrUserRes, err); rpcErr != nil {
		logger.Error(ctx, "error in IsNonResidentUser", zap.Error(rpcErr))
		return nil, rpcErr
	}
	if !isNrUserRes.GetIsNonResidentUser().ToBool() {
		monitoring.KibanaErrorServiceMonitor(ctx, cfg.ONBOARDING_SERVICE, "resident user entered location check for NR flow")
		return nil, fmt.Errorf("resident user entered location check for NR flow")
	}

	// Checking for both UAE & United Arab Emirates
	if !nri.IsAllowedCountryNameForCountryCode(ctx, country, isNrUserRes.GetResidentCountryCode()) {
		logger.Info(ctx, "User Blocked from on-boarding")
		return &StageProcessorResponse{
			NextAction: getLocationCheckDeeplink(ctx, isNrUserRes.GetResidentCountryCode()),
			AnalyticsProps: map[string]string{
				"block_reason":     "NR user not in resident country",
				"current_country":  country,
				"resident_country": countrystdinfo.GetCLDRCountryName(isNrUserRes.GetResidentCountryCode()),
			},
		}, nil
	}

	return nil, NoActionError
}

func getLocationCheckDeeplink(ctx context.Context, countryCode typesv2.CountryCode) *dlPb.Deeplink {
	countryCodeToLocationBlock := map[typesv2.CountryCode]*dlPb.Deeplink{
		typesv2.CountryCode_COUNTRY_CODE_QAT: deeplink.NewErrorFullScreen(ctx, error2.OnbErrCountryQatarCheck),
		typesv2.CountryCode_COUNTRY_CODE_ARE: deeplink.NewErrorFullScreen(ctx, error2.OnbErrCountryUAECheck),
	}
	dl, ok := countryCodeToLocationBlock[countryCode]
	if !ok {
		return deeplink.NewErrorFullScreen(ctx, &error2.ErrorScreenOpts{
			Title:    fmt.Sprintf("Uh oh! Looks like you're not in %v", countrystdinfo.GetCLDRCountryName(countryCode)),
			Subtitle: "If you are on VPN, please turn it off and try again.",
			Category: appFbPb.AppScreen_APP_SCREEN_ONBOARDING_TERMINAL,
		})
	}
	return dl
}

func isNonProdEnv() bool {
	env, err := cfg.GetEnvironment()
	if err != nil {
		// return non prod as false in case of error
		return false
	}
	return cfg.IsQaEnv(env) || cfg.IsStagingEnv(env) || cfg.IsUatEnv(env)
}

func (s *NRLocationCheckStage) mockProcessor(ctx context.Context) (*StageProcessorResponse, error) {
	logger.Info(ctx, "skipping nr location check stage in non-prod environment")
	return nil, NoActionError
}
