package stageproc

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/mohae/deepcopy"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/user/onboarding/helper/deeplink"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/savings"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

func TestAccountCreationStage_StageProcessor(t *testing.T) {
	t.<PERSON>l()
	var (
		onbFixture = &onbPb.OnboardingDetails{
			OnboardingId: "onb-id",
			ActorId:      "actor-id",
			StageDetails: &onbPb.StageDetails{
				StageMapping: map[string]*onbPb.StageInfo{
					onbPb.OnboardingStage_ACCOUNT_CREATION.String(): {State: onbPb.OnboardingState_UNSPECIFIED, LastUpdatedAt: timestamppb.Now()},
				},
			},
		}
		nriOnbFixture = &onbPb.OnboardingDetails{
			OnboardingId: "onb-id-nri",
			ActorId:      "actor-id-nri",
			Feature:      onbPb.Feature_FEATURE_NON_RESIDENT_SA,
			StageDetails: &onbPb.StageDetails{
				StageMapping: map[string]*onbPb.StageInfo{
					onbPb.OnboardingStage_ACCOUNT_CREATION.String(): {State: onbPb.OnboardingState_UNSPECIFIED, LastUpdatedAt: timestamppb.Now()},
				},
			},
		}
	)

	type args struct {
		request *StageProcessorRequest
	}
	tests := []struct {
		name     string
		args     args
		want     *StageProcessorResponse
		wantErr  error
		mockFunc func(*Clients)
	}{
		{
			name: "initiate resident account creation",
			args: args{
				request: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewActionToAccountCreationProgress(),
			},
			wantErr: nil,
			mockFunc: func(m *Clients) {
				m.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				m.bankCustClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					ActorId: onbFixture.GetActorId(),
					Vendor:  onbFixture.GetVendor(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).Return(&savings.GetSavingsAccountEssentialsResponse{Status: rpc.StatusRecordNotFound()}, nil)
				m.savingsClient.EXPECT().CreateAccount(gomock.Any(), &savings.CreateAccountRequest{
					PrimaryAccountHolderId: onbFixture.GetUserId(),
					ActorId:                onbFixture.GetActorId(),
					Options:                &savings.CreateAccountRequest_Options{ShouldOpenAmbAccount: false},
				}).Return(&savings.CreateAccountResponse{
					Status: rpc.StatusOk(),
					Account: &savings.Account{
						Id:        "sav-id",
						AccountNo: "acct-no",
						State:     savings.State_INITIATED,
					},
				}, nil)
				m.OnbDao.EXPECT().UpdateStatus(gomock.Any(), onbFixture.GetOnboardingId(), onbPb.OnboardingStage_ACCOUNT_CREATION, onbPb.OnboardingState_INITIATED).Return(&onbPb.OnboardingDetails{
					OnboardingId: "onb-id",
					ActorId:      "actor-id",
					StageDetails: &onbPb.StageDetails{
						StageMapping: map[string]*onbPb.StageInfo{
							onbPb.OnboardingStage_ACCOUNT_CREATION.String(): {State: onbPb.OnboardingState_INITIATED, LastUpdatedAt: timestamppb.Now()},
						},
					},
				}, nil)
			},
		},
		{
			name: "initiate NRE account creation",
			args: args{
				request: &StageProcessorRequest{
					Onb: nriOnbFixture,
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewActionToAccountCreationProgress(),
			},
			wantErr: nil,
			mockFunc: func(m *Clients) {
				m.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				m.bankCustClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					ActorId: nriOnbFixture.GetActorId(),
					Vendor:  nriOnbFixture.GetVendor(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).Return(&savings.GetSavingsAccountEssentialsResponse{Status: rpc.StatusRecordNotFound()}, nil)
				m.savingsClient.EXPECT().CreateAccount(gomock.Any(), &savings.CreateAccountRequest{
					PrimaryAccountHolderId: nriOnbFixture.GetUserId(),
					ActorId:                nriOnbFixture.GetActorId(),
					Sku:                    savings.SKU_NRE_ORIGINAL,
					Options:                &savings.CreateAccountRequest_Options{ShouldOpenAmbAccount: false},
				}).Return(&savings.CreateAccountResponse{
					Status: rpc.StatusOk(),
					Account: &savings.Account{
						Id:        "sav-id",
						AccountNo: "acct-no",
						State:     savings.State_INITIATED,
					},
				}, nil)
				m.OnbDao.EXPECT().UpdateStatus(gomock.Any(), nriOnbFixture.GetOnboardingId(), onbPb.OnboardingStage_ACCOUNT_CREATION, onbPb.OnboardingState_INITIATED).Return(&onbPb.OnboardingDetails{
					OnboardingId: nriOnbFixture.GetOnboardingId(),
					ActorId:      nriOnbFixture.GetActorId(),
					StageDetails: &onbPb.StageDetails{
						StageMapping: map[string]*onbPb.StageInfo{
							onbPb.OnboardingStage_ACCOUNT_CREATION.String(): {State: onbPb.OnboardingState_INITIATED, LastUpdatedAt: timestamppb.Now()},
						},
					},
				}, nil)
			},
		},
		{
			name: "successful",
			args: args{
				request: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want:    &StageProcessorResponse{},
			wantErr: NoActionError,
			mockFunc: func(m *Clients) {
				m.bankCustClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					ActorId: onbFixture.GetActorId(),
					Vendor:  onbFixture.GetVendor(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).Return(
					&savings.GetSavingsAccountEssentialsResponse{
						Status: rpc.StatusOk(),
						Account: &savings.SavingsAccountEssentials{
							Id:          "acct-id",
							AccountNo:   "account-no",
							ActorId:     onbFixture.GetActorId(),
							State:       savings.State_CREATED,
							PartnerBank: onbFixture.GetVendor(),
						},
					}, nil)
				m.AuthClient.EXPECT().UpdateToken(gomock.Any(), &auth.UpdateTokenRequest{
					Identifier: &auth.UpdateTokenRequest_ActorId{
						ActorId: onbFixture.GetActorId(),
					},
					Status: auth.UpdateTokenRequest_DELETE,
					TokenTypes: []auth.TokenType{
						auth.TokenType_ACCESS_TOKEN,
					},
					TokenUpdationReason: auth.TokenDeletionReason_TOKEN_DELETION_REASON_ONBOARDING_TOKEN_UPGRADE,
				}).Return(&auth.UpdateTokenResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.eventLogger.EXPECT().LogAffluenceAccountCreationEventV2(gomock.Any(), onbFixture.ActorId)
				m.eventLogger.EXPECT().LogSAConverted(gomock.Any(), gomock.Any())
				m.eventLogger.EXPECT().LogLendableAccountCreationEvent(gomock.Any(), onbFixture.ActorId)
				m.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(2)
			},
		},
		{
			name: "stage already success",
			args: args{
				request: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						OnboardingId: onbFixture.OnboardingId,
						ActorId:      onbFixture.ActorId,
						StageDetails: &onbPb.StageDetails{
							StageMapping: map[string]*onbPb.StageInfo{
								onbPb.OnboardingStage_ACCOUNT_CREATION.String(): {State: onbPb.OnboardingState_SUCCESS, LastUpdatedAt: timestamppb.Now()},
							},
						},
					},
				},
			},
			want:    &StageProcessorResponse{},
			wantErr: NoActionError,
			mockFunc: func(m *Clients) {
				m.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				m.eventLogger.EXPECT().LogAffluenceAccountCreationEventV2(gomock.Any(), onbFixture.ActorId)
				m.eventLogger.EXPECT().LogSAConverted(gomock.Any(), gomock.Any())
				m.eventLogger.EXPECT().LogLendableAccountCreationEvent(gomock.Any(), onbFixture.ActorId)
			},
		},
		{
			name: "dedupe error in account creation",
			args: args{
				request: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want: &StageProcessorResponse{
				NextAction: getDedupeErrorDL(context.Background(), 0),
			},
			wantErr: nil,
			mockFunc: func(m *Clients) {
				m.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				m.bankCustClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					ActorId: onbFixture.GetActorId(),
					Vendor:  onbFixture.GetVendor(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), gomock.Any()).Return(&savings.GetSavingsAccountEssentialsResponse{Status: rpc.StatusRecordNotFound()}, nil)
				m.savingsClient.EXPECT().CreateAccount(gomock.Any(), &savings.CreateAccountRequest{
					PrimaryAccountHolderId: onbFixture.GetUserId(),
					ActorId:                onbFixture.GetActorId(),
					Options:                &savings.CreateAccountRequest_Options{ShouldOpenAmbAccount: false},
				}).Return(&savings.CreateAccountResponse{
					Status: rpc.NewStatusFactory(uint32(savings.CreateAccountResponse_INVALID_DEDUPE_STATUS), "")(),
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			mockClient := setupServiceWithMocks(t)
			if tt.mockFunc != nil {
				tt.mockFunc(mockClient)
			}
			a := NewAccountCreationStage(ts.GenConf.Onboarding(), mockClient.OnbDao, mockClient.savingsClient, mockClient.AuthClient, mockClient.eventLogger, mockClient.bankCustClient)
			got, err := a.StageProcessor(context.Background(), deepcopy.Copy(tt.args.request).(*StageProcessorRequest))
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Unexpected error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			if diff := cmp.Diff(got.GetNextAction(), tt.want.GetNextAction(), protocmp.Transform()); diff != "" {
				t.Errorf("StageProcessor value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
