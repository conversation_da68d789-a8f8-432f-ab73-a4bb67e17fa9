// nolint
package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	"context"
	"encoding/base64"
	"fmt"
	"path/filepath"
	"strings"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	txn "github.com/epifi/be-common/pkg/storage/v2"

	livPb "github.com/epifi/gamma/api/auth/liveness"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	kycdocspb "github.com/epifi/gamma/api/kyc/docs"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	img "github.com/epifi/gamma/pkg/image"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
)

type NRLivenessStage struct {
	livClient              livPb.LivenessClient
	eventLogger            userEvents.EventLogger
	userClient             userPb.UsersClient
	conf                   *genconf.OnboardingConfig
	userConf               *genconf.Config
	s3Client               s3.S3Client
	eventBroker            events.Broker
	onboardingDao          dao.OnboardingDao
	kycDocExtractionClient kycdocspb.DocExtractionClient
}

func NewNRLivenessStage(livClient livPb.LivenessClient, eventLogger userEvents.EventLogger, userClient userPb.UsersClient, conf *genconf.OnboardingConfig,
	userConf *genconf.Config, s3Client s3.S3Client, eventBroker events.Broker, onboardingDao dao.OnboardingDao, kycDocExtractionClient kycdocspb.DocExtractionClient) *NRLivenessStage {
	return &NRLivenessStage{
		livClient:              livClient,
		eventLogger:            eventLogger,
		userClient:             userClient,
		conf:                   conf,
		userConf:               userConf,
		s3Client:               s3Client,
		onboardingDao:          onboardingDao,
		eventBroker:            eventBroker,
		kycDocExtractionClient: kycDocExtractionClient,
	}
}

func (s *NRLivenessStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb                = req.GetOnb()
		stage              = onbPb.OnboardingStage_LIVENESS
		actorId            = req.GetOnb().GetActorId()
		errProcessLiveness error
		summaryReqId       string
		currentState       = getStageStatus(onb, stage)
		err                error
	)

	if onb.GetStageMetadata().GetLivenessMetadata().GetRequestId() == "" {
		onb, err = s.updateMetadataWithNewRequestId(ctx, onb)
		if err != nil {
			return nil, err
		}
	}
	if currentState == onbPb.OnboardingState_RESET {
		onb, err = s.updateMetadataWithNewRequestId(ctx, onb)
		if err != nil {
			return nil, err
		}
		onb, err = updateStatus(ctx, s.onboardingDao, onb.GetOnboardingId(), stage, onbPb.OnboardingState_INPROGRESS)
		if err != nil {
			logger.Error(ctx, "unable to update liveness stage status", zap.Error(err))
			return nil, err
		}
		logger.Debug(ctx, fmt.Sprintf("OnboardingSummary Creating LivenessSummary. ActorID : %v, ReqId : %v", actorId, summaryReqId))
	}

	summaryReqId = onb.GetStageMetadata().GetLivenessMetadata().GetRequestId()
	dl, analyticsProps, errProcessLiveness := s.processLiveness(ctx, actorId, summaryReqId, onb)
	return &StageProcessorResponse{
		NextAction:     dl,
		AnalyticsProps: analyticsProps,
	}, errProcessLiveness
}

func (s *NRLivenessStage) updateMetadataWithNewRequestId(ctx context.Context, onb *onbPb.OnboardingDetails) (*onbPb.OnboardingDetails, error) {
	var err error
	summaryReqId := idgen.FederalRandomSequence("ONB", 10)
	onb, err = s.updateLivenessStageMetadata(ctx, summaryReqId, onb.GetOnboardingId())
	if err != nil {
		logger.Error(ctx, "unable to update onboarding details liveness metadata", zap.Error(err))
		return nil, err
	}
	return onb, nil
}

func (s *NRLivenessStage) processLiveness(ctx context.Context, actorId string, summaryReqId string, onb *onbPb.OnboardingDetails) (*dlPb.Deeplink, map[string]string, error) {
	summaryStatusRes, errSummaryStatus := s.livClient.GetLivenessSummaryStatus(ctx, &livPb.GetLivenessSummaryStatusRequest{
		ActorId:      actorId,
		RequestId:    summaryReqId,
		LivenessFlow: livPb.LivenessFlow_NON_RESIDENT_ONBOARDING,
	})
	if grpcErr := epifigrpc.RPCError(summaryStatusRes, errSummaryStatus); grpcErr != nil && !rpc.StatusFromError(grpcErr).IsRecordNotFound() {
		logger.Error(ctx, "Failed to get liveness summary status", zap.Error(grpcErr))
		return nil, nil, grpcErr
	}

	if summaryStatusRes.GetStatus().IsRecordNotFound() {
		dl, err := s.createNewLivenessSummary(ctx, actorId, onb)
		return dl, nil, err
	}

	analyticsProps := map[string]string{
		"overall_status":   summaryStatusRes.GetSummaryStatus().String(),
		"liveness_status":  summaryStatusRes.GetSummaryLivenessStatus().String(),
		"facematch_status": summaryStatusRes.GetSummaryFacematchStatus().String(),
	}

	logger.Debug(ctx, "OnboardingSummary Received Summary Status",
		zap.String(logger.ACTOR_ID_V2, actorId),
		zap.String("SummaryStatus", summaryStatusRes.GetSummaryStatus().String()),
		zap.String("SummaryLivenessStatus", summaryStatusRes.GetSummaryLivenessStatus().String()),
		zap.String("SummaryFacematchStatus", summaryStatusRes.GetSummaryFacematchStatus().String()))
	switch summaryStatusRes.GetSummaryStatus() {
	case livPb.SummaryStatus_SUMMARY_UNSPECIFIED, livPb.SummaryStatus_SUMMARY_IN_PROGRESS:
		dl, err := s.createLivenessAttempt(ctx, actorId, summaryReqId)
		return dl, analyticsProps, err
	case livPb.SummaryStatus_SUMMARY_PASSED:
		// set liveness photo in user profile
		if errImageUpdate := s.updateLivenessImageForProfile(ctx, onb.GetUserId(), onb.GetActorId(), summaryReqId); errImageUpdate != nil {
			logger.Error(ctx, "error in updating liveness photo in user profile", zap.Error(errImageUpdate))
			return nil, nil, errImageUpdate
		}

		return nil, nil, NoActionError
	case livPb.SummaryStatus_SUMMARY_RETRIES_EXHAUSTED:
		_ = updateKYCStageState(ctx, onb, onbPb.OnboardingStage_LIVENESS, onbPb.OnboardingState_MANUAL_INTERVENTION, s.onboardingDao)
		dl, err := s.livenessTerminalScreenV2(ctx, summaryStatusRes, actorId)
		return dl, analyticsProps, err
	case livPb.SummaryStatus_SUMMARY_EXPIRED:
		dl, err := s.createNewLivenessSummary(ctx, actorId, onb)
		return dl, analyticsProps, err
	default:
		logger.Error(ctx, fmt.Sprintf("unexpected livenesss summary status received : %v", summaryStatusRes.GetSummaryStatus()))
		return nil, nil, fmt.Errorf("unexpected livenesss summary status received : %v", summaryStatusRes.GetSummaryStatus())
	}
}

func (s *NRLivenessStage) createNewLivenessSummary(ctx context.Context, actorId string, onbDetails *onbPb.OnboardingDetails) (*dlPb.Deeplink, error) {
	fmImages, err := s.getImagesForFacematch(ctx, onbDetails)
	if err != nil {
		return nil, err
	}

	createRes, errSummary := s.livClient.CreateLivenessSummary(ctx, &livPb.CreateLivenessSummaryRequest{
		ActorId:            actorId,
		RequestId:          onbDetails.GetStageMetadata().GetLivenessMetadata().GetRequestId(),
		LivenessFlow:       livPb.LivenessFlow_NON_RESIDENT_ONBOARDING,
		MaxRetries:         3,
		RefFacematchPhotos: fmImages,
		ForceManualReview:  commontypes.BooleanEnum_FALSE,
		// Liveness expiry is explicitly not set for NR onboarding as we don't handle liveness expiry as of now
	})
	if grpcErr := epifigrpc.RPCError(createRes, errSummary); grpcErr != nil {
		logger.Error(ctx, "Failed to create livenessSummary", zap.Error(grpcErr))
		return nil, grpcErr
	}

	return s.createLivenessAttempt(ctx, actorId, onbDetails.GetStageMetadata().GetLivenessMetadata().GetRequestId())
}

func (s *NRLivenessStage) getImagesForFacematch(ctx context.Context, onbDetails *onbPb.OnboardingDetails) ([]*commontypes.Image, error) {
	fmImages := make([]*commontypes.Image, 0)
	s3Uri, err := img.ConvertHttpURLToS3URI(onbDetails.GetStageMetadata().GetPassportVerificationMetadata().GetFaceImageUrl())
	if err != nil {
		return nil, fmt.Errorf("failed to convert http url to s3 uri: %w", err)
	}
	filePath, err := img.ExtractBucketAndKeyFromS3Uri(s3Uri)
	if err != nil {
		return nil, fmt.Errorf("failed to extract bucket and key from s3 uri: %w", err)
	}
	fmImages = append(fmImages, &commontypes.Image{
		ImageUrl: filePath,
	})
	// todo(saiteja) : fill image from uqudo emirates ID
	return fmImages, nil
}

func (s *NRLivenessStage) updateLivenessStageMetadata(ctx context.Context, summaryReqId string, onbId string) (*onbPb.OnboardingDetails, error) {
	var onbDetails *onbPb.OnboardingDetails
	var err error
	if trErr := txn.RunCRDBIdempotentTxn(ctx, transactionMaxRetry, func(ctx context.Context) error {
		onbDetails, err = s.onboardingDao.GetOnboardingDetailsById(ctx, onbId)
		if err != nil {
			return err
		}

		if onbDetails.GetStageMetadata() == nil {
			onbDetails.StageMetadata = &onbPb.StageMetadata{}
		}
		if onbDetails.GetStageMetadata().GetLivenessMetadata() == nil {
			onbDetails.GetStageMetadata().LivenessMetadata = &onbPb.LivenessMetadata{}
		}
		onbDetails.GetStageMetadata().GetLivenessMetadata().RequestId = summaryReqId
		if err = s.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, onbDetails); err != nil {
			return err
		}
		return nil
	}); err != nil {
		logger.Error(ctx, "unable to update onboarding details metadata", zap.Error(trErr))
		return nil, trErr
	}
	return onbDetails, nil
}

func (s *NRLivenessStage) createLivenessAttempt(ctx context.Context, actorId, summaryReqId string) (*dlPb.Deeplink, error) {
	createAttemptRes, errCreateAttempt := s.livClient.CreateLivenessAttempt(ctx, &livPb.CreateLivenessAttemptRequest{
		ActorId:          actorId,
		SummaryRequestId: summaryReqId,
		LivenessFlow:     livPb.LivenessFlow_NON_RESIDENT_ONBOARDING,
	})
	if grpcErr := epifigrpc.RPCError(createAttemptRes, errCreateAttempt); grpcErr != nil {
		logger.Error(ctx, "Failed to create liveness attempt", zap.Error(grpcErr))
		return nil, grpcErr
	}

	// Send GET_NEXT_ONBOARDING_ACTION if video is received for the current attempt
	if createAttemptRes.GetLivenessAttempt().GetStatus() > livPb.LivenessStatus_LIVENESS_VIDEO_RECEIVED {
		return deeplink.NewActionToGetNextAction(), nil
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_CHECK_LIVENESS,
		ScreenOptions: &dlPb.Deeplink_CheckLivenessScreenOptions{
			CheckLivenessScreenOptions: &dlPb.CheckLivenessScreenOptions{
				LivenessFlow:             dlPb.LivenessFlow_NON_RESIDENT_ONBOARDING,
				AttemptId:                createAttemptRes.GetLivenessAttempt().GetRequestId(),
				Otp:                      createAttemptRes.GetLivenessAttempt().GetOtp(),
				ErrorLastLivenessFailure: livStatusToDLStatus(createAttemptRes.GetOldAttemptStatus()),
				NextAction: &dlPb.Deeplink{
					Screen: dlPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
				},
				DisableFaceTrackingAndroid: true,
				Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/liveness_selfie_video.png").WithProperties(&commontypes.VisualElementProperties{
					Width:  240,
					Height: 240,
				}),
				Title:    commontypes.GetTextFromStringFontColourFontStyle("Take a selfie video in \njust 10 seconds", "#333333", commontypes.FontStyle_SUBTITLE_1),
				Subtitle: commontypes.GetTextFromStringFontColourFontStyle("This is to ensure no one is\npretending to be you.", "#878A8D", commontypes.FontStyle_BODY_S),
				ListItems: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle("Take the video in a well-lit place 💡", "#333333", commontypes.FontStyle_BODY_S),
					commontypes.GetTextFromStringFontColourFontStyle("Make sure you read out the 4 digits that appear on-screen 📢", "#333333", commontypes.FontStyle_BODY_S),
				},
				EnableVpnCheck: true,
			},
		},
	}, nil
}

func (s *NRLivenessStage) updateLivenessImageForProfile(ctx context.Context, userId, actorId, summaryReqId string) error {
	imageData, err := s.getUserImageFromLiveness(ctx, actorId, summaryReqId)
	if err != nil {
		return err
	}

	// upload photo to S3
	filePath, httpUrl, err := s.uploadBase64ImageToS3(ctx, imageData, userId)
	if err != nil {
		return err
	}

	// updating user db with the profile imageData url
	resp, err := s.userClient.UpdateUser(ctx, &userPb.UpdateUserRequest{
		User: &userPb.User{
			Id: userId,
			Profile: &userPb.Profile{
				ProfileImageS3FilePath: filePath,
				Photo: &commontypes.Image{
					ImageUrl: httpUrl,
				},
			},
		},
		UpdateMask: []userPb.UserFieldMask{userPb.UserFieldMask_PROFILE_IMAGE_S3_FILE_PATH,
			userPb.UserFieldMask_PHOTO},
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error in update user images in db", zap.Error(err), zap.String(logger.ENTITY_ID, userId))
		return err
	}

	return nil
}

func (s *NRLivenessStage) getUserImageFromLiveness(ctx context.Context, actorId, summaryReqId string) (string, error) {
	getLivSummaryRes, errGetLivSummary := s.livClient.GetLivenessSummary(ctx, &livPb.GetLivenessSummaryRequest{
		ActorId:      actorId,
		RequestId:    summaryReqId,
		LivenessFlow: livPb.LivenessFlow_NON_RESIDENT_ONBOARDING,
	})
	if grpcErr := epifigrpc.RPCError(getLivSummaryRes, errGetLivSummary); grpcErr != nil {
		logger.Error(ctx, "failed to get liveness summary to get liveness frame", zap.Error(grpcErr))
		return "", grpcErr
	}

	getLivAttResp, errGetLivAtt := s.livClient.GetLivenessAttempt(ctx, &livPb.GetLivenessAttemptRequest{
		LivenessReqId: getLivSummaryRes.GetSummary().GetLivenessAttemptId(),
	})
	if grpcErr := epifigrpc.RPCError(getLivAttResp, errGetLivAtt); grpcErr != nil {
		logger.Error(ctx, "failed to get liveness attempt by req id", zap.Error(grpcErr))
		return "", grpcErr
	}

	return s.getLivenessImageDataForAttempt(ctx, getLivAttResp.GetLivenessAttempt())
}

func (s *NRLivenessStage) uploadBase64ImageToS3(ctx context.Context, imageBase64 string, userId string) (string, string, error) {
	if strings.TrimSpace(imageBase64) == "" {
		return "", "", fmt.Errorf("empty image b64 data received")
	}
	randSuffix := userId + "/" + uuid.New().String()
	filePath := filepath.Join(imagePathPrefix, randSuffix+imageFileExt)
	logger.Debug(ctx, fmt.Sprintf("writing to bucket... %v", s.userConf.AWS().S3.UsersBucketName))

	// decoding image before uploading
	image, err := base64.StdEncoding.DecodeString(imageBase64)
	if err != nil {
		logger.Error(ctx, "failed to decode image base64 string, ", zap.Error(err))
		return "", "", err
	}

	if err = s.s3Client.Write(ctx, filePath, image, "bucket-owner-full-control"); err != nil {
		logger.Error(ctx, "failed to store image in S3", zap.Error(err), zap.String(logger.ENTITY_ID, userId))
		return "", "", err
	}

	httpUrl := img.GetHttpUrl(s.userConf.AWS().S3.UsersBucketName, s.userConf.AWS().Region, filePath)
	logger.Debug(ctx, fmt.Sprintf("BucketName: %v and Region name: %v", s.userConf.AWS().S3.UsersBucketName, s.userConf.AWS().Region))
	return filePath, httpUrl, err
}

// nolint: dupl
// getLivenessImageDataForAttempt returns liveness image data in base64 format for the given liveness attempt.
func (s *NRLivenessStage) getLivenessImageDataForAttempt(ctx context.Context, la *livPb.LivenessAttempt) (string, error) {
	imgData := la.GetImageFrame().GetImageDataBase64()

	// return early if image data already present
	if imgData != "" {
		return imgData, nil
	}

	// return error if both image url & data not present
	if la.GetImageFrame().GetImageUrl() == "" {
		logger.Error(ctx, "both liveness image url & data not found")
		return "", epifierrors.ErrRecordNotFound
	}

	// liveness image url is found but image data not found
	// fetch image data from image url
	imageRes, err := s.livClient.GetS3Image(ctx, &livPb.GetS3ImageRequest{
		LocationKey: la.GetImageFrame().GetImageUrl(),
	})
	if err = epifigrpc.RPCError(imageRes, err); err != nil {
		logger.Error(ctx, "error in get s3 image data", zap.Error(err))
		return "", err
	}
	if len(imageRes.GetImage()) == 0 {
		logger.Error(ctx, "received empty image in get s3 image")
		return "", fmt.Errorf("invalid get s3 image response")
	}

	// image fetch successful, return image data in base64
	b64data := base64.StdEncoding.EncodeToString(imageRes.GetImage())
	return b64data, nil
}

func (s *NRLivenessStage) livenessTerminalScreenV2(ctx context.Context, resp *livPb.GetLivenessSummaryStatusResponse, actorId string) (*dlPb.Deeplink, error) {
	if resp.GetSummaryLivenessStatus() == livPb.SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_FAILED || resp.GetSummaryFacematchStatus() == livPb.SummaryFacematchStatus_SUMMARY_FACEMATCH_MANUALLY_FAILED {
		return deeplink.NewErrorFullScreen(ctx, error2.OnbErrLivenessManuallyFailed), nil
	}

	if s.conf.Flags().AllowManualReviewUsers() {
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), userEvents.NewManualReviewEvent(actorId))
		})
		return deeplink.NewLivenessManualReviewDL(), nil
	}
	return deeplink.NewActionForLivenessMaxRetries(), nil
}
