package stageproc

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/bankcust"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	onbDao "github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

type NRPreCustomerCreationCheckStage struct {
	onbDao         onbDao.OnboardingDao
	userClient     user.UsersClient
	kycClient      kycPb.KycClient
	time           datetime.Time
	dedupeProc     helper.IDedupeHelper
	eventLogger    userEvents.EventLogger
	userProc       helper.UserProcessor
	userConf       *genconf.Config
	vkycClient     vkycPb.VKYCClient
	bcClient       bankcust.BankCustomerServiceClient
	passportHelper helper.PassportHelper
}

func NewNRPreCustomerCreationCheckStage(onbDao onbDao.OnboardingDao, userClient user.UsersClient,
	kycClient kycPb.KycClient, time datetime.Time, dedupeProc helper.IDedupeHelper, eventLogger userEvents.EventLogger,
	userProc helper.UserProcessor, userConf *genconf.Config, vkycClient vkycPb.VKYCClient, bcClient bankcust.BankCustomerServiceClient,
	passportHelper helper.PassportHelper) *NRPreCustomerCreationCheckStage {
	return &NRPreCustomerCreationCheckStage{
		onbDao:         onbDao,
		userClient:     userClient,
		kycClient:      kycClient,
		time:           time,
		dedupeProc:     dedupeProc,
		eventLogger:    eventLogger,
		userProc:       userProc,
		userConf:       userConf,
		vkycClient:     vkycClient,
		bcClient:       bcClient,
		passportHelper: passportHelper,
	}
}

func (s *NRPreCustomerCreationCheckStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb   = req.GetOnb()
		stage = onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK
	)

	// check if another user with same pan has device registered with federal
	isDedupePan, maskedPhoneNumber, err := s.isOnbRestrictedOnPan(ctx, onb)
	if err != nil {
		return nil, err
	}

	if isDedupePan {
		_, _ = updateStatus(ctx, s.onbDao, onb.GetOnboardingId(), stage, onbPb.OnboardingState_FAILURE)
		_ = updateStageMetadata(ctx, s.onbDao, onb.GetOnboardingId(), getStageMetadataWithPanUniquenessFailure(onb))
		return &StageProcessorResponse{
			NextAction: deeplink.NewErrorFullScreen(ctx, &error2.ErrorScreenOpts{
				Title:    constant.DeviceAlreadyRegisteredWithPan,
				Subtitle: fmt.Sprintf("The existing account is already linked to mobile number <b>%v</b> for signup. You can resume your journey again with the same phone number.", maskedPhoneNumber),
			}),
		}, nil
	}

	if !getStageStatus(onb, onbPb.OnboardingStage_LIVENESS).IsSuccessOrSkipped() ||
		!getStageStatus(onb, onbPb.OnboardingStage_NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION).IsSuccessOrSkipped() ||
		!getStageStatus(onb, onbPb.OnboardingStage_VKYC).IsSuccessOrSkipped() {
		logger.Info(ctx, "LIVENESS or CROSS_DATA_VALIDATION or VKYC is not successful yet, asking client to poll again")
		return &StageProcessorResponse{
			NextAction: deeplink.NewActionToGetNextAction(),
		}, nil
	}

	inProgress, errCustomer := s.userProc.IsCustomerCreationInProgress(ctx, onb.GetActorId())
	if errCustomer != nil {
		return nil, errCustomer
	}
	if inProgress {
		return nil, SkipStageError
	}

	if err = s.checkFullKycBankCustomer(ctx, onb); err != nil {
		return nil, err
	}

	// Pre customer creation dedupe check
	dl, err := s.dedupeCheck(ctx, onb, stage)
	if !errors.Is(err, errNoAction) {
		return &StageProcessorResponse{
			NextAction: dl,
		}, err
	}

	return nil, NoActionError

}

func (s *NRPreCustomerCreationCheckStage) checkFullKycBankCustomer(ctx context.Context, onb *onbPb.OnboardingDetails) error {
	bankCustomerRes, err := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: onb.GetActorId(),
		},
		Vendor: onb.GetVendor(),
	})
	if rpcErr := epifigrpc.RPCError(bankCustomerRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting bank customer", zap.Error(rpcErr))
		return rpcErr
	}
	if bankCustomerRes.GetBankCustomer().GetKycInfo().GetKycLevel() != kycPb.KYCLevel_FULL_KYC {
		// todo (NRI) add alerts
		logger.Info(ctx, "user reached pre customer creation without full KYC")
		return epifierrors.ErrPermissionDenied
	}
	return nil
}

func (s *NRPreCustomerCreationCheckStage) dedupeCheck(ctx context.Context, onb *onbPb.OnboardingDetails, stage onbPb.OnboardingStage) (*dlPb.Deeplink, error) {
	// validate manual intervention status
	allow, err := s.dedupeProc.AllowDedupeCheck(ctx, onb, stage)
	if err != nil {
		logger.Error(ctx, "error in AllowDedupeCheck", zap.Error(err))
		return nil, err
	}
	if !allow {
		if getStageStatus(onb, stage) == onbPb.OnboardingState_MANUAL_INTERVENTION {
			dl := s.dedupeProc.DedupeDeeplinkHandler(ctx, onb, onb.GetStageMetadata().GetPreCustomerCreationDedupeStatus())
			if dl == nil {
				return nil, errNoAction
			}
			return dl, nil
		}
		return nil, errNoAction
	}
	userRes, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: onb.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting user", zap.Error(rpcErr))
		return nil, rpcErr
	}
	passportData, err := s.passportHelper.FetchPassport(ctx, onb.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to get passport data", zap.Error(err))
		return nil, err
	}
	idProofs := []*kycPb.IdProof{
		{
			Type:    kycPb.IdProofType_PASSPORT,
			IdValue: passportData.GetPassportNumber(),
		},
	}
	allowed, dedupeStatus, err := s.dedupeProc.DedupeCheckCore(ctx, userRes.GetUser(), commonvgpb.Vendor_FEDERAL_BANK, idProofs, stage)
	if err != nil {
		return nil, err
	}
	// Update dedupe status in onboarding meta data
	if err = s.dedupeProc.UpdateDedupeFlagInOnboardingDetails(ctx, onb, dedupeStatus, stage); err != nil {
		logger.Error(ctx, "Failed to update dedupe flag in onboarding details", zap.Error(err))
		return nil, err
	}

	// dedupe check validation passed
	if allowed {
		return nil, NoActionError
	}

	// dedupe check validation failed
	if _, err = updateStatus(ctx, s.onbDao, onb.GetOnboardingId(), stage, onbPb.OnboardingState_MANUAL_INTERVENTION); err != nil {
		return nil, err
	}

	return s.dedupeProc.DedupeDeeplinkHandler(ctx, onb, onb.GetStageMetadata().GetKycDedupeStatus()), nil
}

// nolint: dupl, funlen
func (s *NRPreCustomerCreationCheckStage) isOnbRestrictedOnPan(ctx context.Context, onb *onbPb.OnboardingDetails) (bool, string, error) {
	phoneNumber := ""
	if getStageStatus(onb, onbPb.OnboardingStage_PAN_UNIQUENESS_CHECK).IsSuccessOrSkipped() {
		// check already performed for user
		return false, phoneNumber, nil
	}
	userResp, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: onb.GetUserId(),
		},
	})
	if grpcErr := epifigrpc.RPCError(userResp, err); grpcErr != nil {
		logger.Error(ctx, "error in get user", zap.Error(grpcErr))
		return false, phoneNumber, grpcErr
	}
	pan := userResp.GetUser().GetProfile().GetPAN()
	if isSimulatedEnv(s.userConf) &&
		!strings.HasPrefix(strings.ToUpper(pan), "UNQPP") {
		logger.Info(ctx, fmt.Sprintf("bypassing unique pan check for %v", pan))
		return false, phoneNumber, nil
	}
	// get all users with pan provided by User
	userIdentifiers := []*user.GetUsersRequest_GetUsersIdentifier{
		{
			Identifier: &user.GetUsersRequest_GetUsersIdentifier_Pan{
				Pan: pan,
			},
		},
	}
	getUsersResp, err := s.userClient.GetUsers(ctx, &user.GetUsersRequest{
		Identifier: userIdentifiers,
	})
	if err = epifigrpc.RPCError(getUsersResp, err); err != nil {
		logger.Error(ctx, "error in getting users by pan", zap.Error(err))
		return false, phoneNumber, fmt.Errorf("error in get user: %w", err)
	}
	if len(getUsersResp.GetUsers()) == 1 {
		phoneNumber = getMaskedNumber(getUsersResp.GetUsers()[0].GetProfile().GetPhoneNumber().GetNationalNumber())
	}
	for _, aUser := range getUsersResp.GetUsers() {
		// skip for current user
		if aUser.GetId() == onb.GetUserId() {
			continue
		}
		// User cannot continue if bank customer creation is in progress or created for some other user with provided pan
		bcRes, bcErr := s.bcClient.CheckBankCustomerCreationStatus(ctx, &bankcust.CheckBankCustomerCreationStatusRequest{
			ActorId: aUser.GetActorId(),
			Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
		})
		switch {
		case bcRes.GetStatus().IsSuccess(), bcRes.GetStatus().IsInProgress(),
			bcRes.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_NON_RETRYABLE_FAILURE),
			bcRes.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_RETRYABLE_FAILURE):
			logger.Info(ctx, "user is blocked, pan already used", zap.String("panUsedByUserId", aUser.GetId()))
			return true, phoneNumber, nil
		case bcRes.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED):
			continue
		default:
			if rpcErr := epifigrpc.RPCError(bcRes, bcErr); rpcErr != nil {
				logger.Error(ctx, "error while fetching bank customer creation status", zap.Error(rpcErr))
				return false, phoneNumber, rpcErr
			}
		}
	}
	// no user found with same pan and customer creation in progress
	return false, phoneNumber, nil
}
