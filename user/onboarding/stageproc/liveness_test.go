package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	"context"
	"encoding/base64"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	mockS3 "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/mock"

	livPb "github.com/epifi/gamma/api/auth/liveness"
	livMocks "github.com/epifi/gamma/api/auth/liveness/mocks"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/kyc"
	kycMocks "github.com/epifi/gamma/api/kyc/mocks"
	userPb "github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/testing/mocks"
	userEvents "github.com/epifi/gamma/user/events"
	eventMocks "github.com/epifi/gamma/user/events/mocks"
	daoMocks "github.com/epifi/gamma/user/onboarding/dao/mocks"
)

var (
	nextActionDL = &dlPb.Deeplink{
		Screen: dlPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
	}

	failureDL = deeplink.NewErrorFullScreen(context.Background(), error2.OnbErrLivenessManuallyFailed)

	checkLivenessDL = func(la *livPb.LivenessAttempt) *dlPb.Deeplink {
		return &dlPb.Deeplink{
			Screen: dlPb.Screen_CHECK_LIVENESS,
			ScreenOptions: &dlPb.Deeplink_CheckLivenessScreenOptions{
				CheckLivenessScreenOptions: &dlPb.CheckLivenessScreenOptions{
					LivenessFlow: dlPb.LivenessFlow_ONBOARDING,
					AttemptId:    la.GetRequestId(),
					Otp:          la.GetOtp(),
					NextAction: &dlPb.Deeplink{
						Screen: dlPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
					},
					DisableFaceTrackingAndroid: true,
					Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/onboarding/liveness_selfie_video.png").WithProperties(&commontypes.VisualElementProperties{
						Width:  240,
						Height: 240,
					}),
					Title:    commontypes.GetTextFromStringFontColourFontStyle("Take a selfie video in \njust 10 seconds", "#333333", commontypes.FontStyle_SUBTITLE_1),
					Subtitle: commontypes.GetTextFromStringFontColourFontStyle("This is to ensure no one is\npretending to be you.", "#878A8D", commontypes.FontStyle_BODY_S),
					ListItems: []*commontypes.Text{
						commontypes.GetTextFromStringFontColourFontStyle("Take the video in a well-lit place 💡", "#333333", commontypes.FontStyle_BODY_S),
						commontypes.GetTextFromStringFontColourFontStyle("Make sure you read out the 4 digits that appear on-screen 📢", "#333333", commontypes.FontStyle_BODY_S),
					},
					EnableVpnCheck: true,
				},
			},
		}
	}
)

func TestLivenessStage_StageProcessor(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	ctx = context.WithValue(ctx, epificontext.CtxAppPlatformKey, feHeaderPb.Platform_ANDROID.String())
	ctx = context.WithValue(ctx, epificontext.CtxAppVersionCodeKey, "20")

	onbDetailsFixture := &onbPb.OnboardingDetails{
		ActorId: "actor-id",
		StageDetails: &onbPb.StageDetails{
			StageMapping: map[string]*onbPb.StageInfo{},
		},
		OnboardingId: "onboarding-id",
		UserId:       "user-id",
	}

	onbDetailsFixtureWithDevRegComplete := &onbPb.OnboardingDetails{
		ActorId: "actor-id",
		StageDetails: &onbPb.StageDetails{
			StageMapping: map[string]*onbPb.StageInfo{
				onbPb.OnboardingStage_DEVICE_REGISTRATION.String(): {
					State: onbPb.OnboardingState_SUCCESS,
				},
			},
		},
		OnboardingId: "onboarding-id",
		UserId:       "user-id",
	}

	onbDetailsWithEKYCSkipped := &onbPb.OnboardingDetails{
		ActorId: "actor-id",
		StageDetails: &onbPb.StageDetails{
			StageMapping: map[string]*onbPb.StageInfo{
				onbPb.OnboardingStage_EKYC.String(): {
					State: onbPb.OnboardingState_SKIPPED,
				},
			},
		},
		OnboardingId: "onboarding-id",
		UserId:       "user-id",
	}

	onbDetailsWithLivenessReset := &onbPb.OnboardingDetails{
		ActorId: "actor-id",
		StageDetails: &onbPb.StageDetails{
			StageMapping: map[string]*onbPb.StageInfo{
				onbPb.OnboardingStage_LIVENESS.String(): {
					State: onbPb.OnboardingState_RESET,
				},
			},
		},
		OnboardingId: "onboarding-id",
		UserId:       "user-id",
	}

	type mockStruct struct {
		kycClient   *kycMocks.MockKycClient
		onbDao      *daoMocks.MockOnboardingDao
		livClient   *livMocks.MockLivenessClient
		userClient  *userMocks.MockUsersClient
		eventLogger *eventMocks.MockEventLogger
		s3Client    *mockS3.MockS3Client
	}

	mockCheckKYCStatusReq := &kyc.CheckKYCStatusRequest{
		ActorId: onbDetailsFixture.ActorId,
	}

	mockSuccessCheckKYCStatusRes := &kyc.CheckKYCStatusResponse{
		Status:    rpc.StatusOk(),
		KycStatus: kyc.KycStatus_COMPLETED,
		KycType:   kyc.KycType_CKYC,
	}

	mockGetLivenessSummaryStatusReq := &livPb.GetLivenessSummaryStatusRequest{
		ActorId:      onbDetailsFixture.ActorId,
		RequestId:    onbDetailsFixture.OnboardingId,
		LivenessFlow: livPb.LivenessFlow_ONBOARDING,
	}

	mockLA := &livPb.LivenessAttempt{
		ActorId:   onbDetailsFixture.ActorId,
		RequestId: "request-id",
		Otp:       "1234",
		ImageFrame: &commontypes.Image{
			ImageDataBase64: base64.StdEncoding.EncodeToString([]byte{1, 1, 1}),
		},
	}

	mockVendorDataRes := &kyc.GetKYCVendorDataResponse{
		Status: rpc.StatusOk(),
		VendorData: &kyc.KYCVendorData{
			Payload: &kyc.KYCVendorDataPayload{
				CkycDownloads: &kyc.CKYCDownloadVendorData{
					Payloads: []*kyc.CKYCDownloadPayload{
						{
							ImagesData: []*commontypes.Image{
								{
									ImageType:       commontypes.ImageType_JPEG,
									ImageDataBase64: "base64-data",
									ImageUrl:        "image-url",
								},
							},
						},
					},
				},
			},
		},
	}

	type args struct {
		req *StageProcessorRequest
	}

	tests := []struct {
		name    string
		args    args
		want    *StageProcessorResponse
		wantErr error
		mocks   func(args, *mockStruct)
	}{
		{
			name: "liveness success",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsFixture),
				},
			},
			want: &StageProcessorResponse{
				NextAction: nil,
			},
			wantErr: NoActionError,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), mockGetLivenessSummaryStatusReq).Return(&livPb.GetLivenessSummaryStatusResponse{
					Status:                 rpc.StatusOk(),
					SummaryStatus:          livPb.SummaryStatus_SUMMARY_PASSED,
					MaxAttempts:            3,
					AttemptCount:           1,
					SummaryLivenessStatus:  livPb.SummaryLivenessStatus_SUMMARY_LIVENESS_COMPLETED,
					SummaryFacematchStatus: livPb.SummaryFacematchStatus_SUMMARY_FACEMATCH_COMPLETED,
				}, nil)
				mockStruct.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), args.req.GetOnb().GetActorId(), false, "")
				mockStruct.livClient.EXPECT().GetLivenessSummary(gomock.Any(), &livPb.GetLivenessSummaryRequest{
					ActorId:      mockGetLivenessSummaryStatusReq.GetActorId(),
					RequestId:    mockGetLivenessSummaryStatusReq.GetRequestId(),
					LivenessFlow: mockGetLivenessSummaryStatusReq.GetLivenessFlow(),
				}).Return(&livPb.GetLivenessSummaryResponse{
					Status: rpc.StatusOk(),
					Summary: &livPb.LivenessSummary{
						LivenessAttemptId: mockLA.GetAttemptId(),
					},
				}, nil)
				mockStruct.livClient.EXPECT().GetLivenessAttempt(gomock.Any(), &livPb.GetLivenessAttemptRequest{
					LivenessReqId: mockLA.GetAttemptId(),
				}).Return(&livPb.GetLivenessAttemptResponse{
					LivenessAttempt: mockLA,
					Status:          rpc.StatusOk(),
				}, nil)
				mockStruct.s3Client.EXPECT().Write(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockStruct.userClient.EXPECT().UpdateUser(gomock.Any(), mock.NewProtoMatcher(&userPb.UpdateUserRequest{
					UpdateMask: []userPb.UserFieldMask{
						userPb.UserFieldMask_PROFILE_IMAGE_S3_FILE_PATH,
						userPb.UserFieldMask_PHOTO,
					},
				}, protocmp.IgnoreFields(&userPb.UpdateUserRequest{}, "user"))).Return(&userPb.UpdateUserResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
		{
			name: "liveness in-progress",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsFixture),
				},
			},
			want: &StageProcessorResponse{
				NextAction: checkLivenessDL(mockLA),
			},
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), mockGetLivenessSummaryStatusReq).Return(&livPb.GetLivenessSummaryStatusResponse{
					Status:        rpc.StatusOk(),
					SummaryStatus: livPb.SummaryStatus_SUMMARY_IN_PROGRESS,
					MaxAttempts:   2,
					AttemptCount:  1,
				}, nil)
				mockStruct.livClient.EXPECT().CreateLivenessAttempt(gomock.Any(), &livPb.CreateLivenessAttemptRequest{
					ActorId:          args.req.Onb.ActorId,
					SummaryRequestId: args.req.Onb.OnboardingId,
					LivenessFlow:     livPb.LivenessFlow_ONBOARDING,
				}).Return(&livPb.CreateLivenessAttemptResponse{
					Status:          rpc.StatusOk(),
					LivenessAttempt: mockLA,
				}, nil)
			},
			wantErr: nil,
		},
		{
			name: "liveness failed and DEVICE_REGISTRATION complete",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsFixtureWithDevRegComplete),
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink.NewLivenessManualReviewDL(),
			},
			wantErr: nil,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), mockGetLivenessSummaryStatusReq).Return(&livPb.GetLivenessSummaryStatusResponse{
					Status:        rpc.StatusOk(),
					SummaryStatus: livPb.SummaryStatus_SUMMARY_RETRIES_EXHAUSTED,
					MaxAttempts:   1,
					AttemptCount:  1,
				}, nil)
				mockStruct.onbDao.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onbDetailsFixtureWithDevRegComplete, nil).AnyTimes()
				mockStruct.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), args.req.Onb.GetActorId(), true, userEvents.BlockReasonRisk)
			},
		},
		{
			name: "liveness failed but DEVICE_REGISTRATION not complete",
			args: args{
				req: &StageProcessorRequest{
					Onb: onbDetailsFixture,
				},
			},
			wantErr: NoActionSkipStatusUpdateError,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), mockGetLivenessSummaryStatusReq).Return(&livPb.GetLivenessSummaryStatusResponse{
					Status:        rpc.StatusOk(),
					SummaryStatus: livPb.SummaryStatus_SUMMARY_RETRIES_EXHAUSTED,
					MaxAttempts:   1,
					AttemptCount:  1,
				}, nil)
				mockStruct.onbDao.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(args.req.GetOnb(), nil).AnyTimes()
			},
		},
		{
			name: "facematch failed for CKYC, resetting EKYC",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsWithEKYCSkipped),
				},
			},
			want: &StageProcessorResponse{
				NextAction: nextActionDL,
			},
			wantErr: nil,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), mockGetLivenessSummaryStatusReq).Return(&livPb.GetLivenessSummaryStatusResponse{
					Status:                 rpc.StatusOk(),
					SummaryStatus:          livPb.SummaryStatus_SUMMARY_RETRIES_EXHAUSTED,
					MaxAttempts:            1,
					AttemptCount:           1,
					SummaryFacematchStatus: livPb.SummaryFacematchStatus_SUMMARY_FACEMATCH_RETRY,
				}, nil)
				mockStruct.onbDao.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(args.req.GetOnb(), nil).AnyTimes()
			},
		},
		{
			name: "No KYC record",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsFixture),
				},
			},
			want: &StageProcessorResponse{
				NextAction: nil,
			},
			wantErr: fmt.Errorf("No kyc attempt found for %v", onbDetailsFixture.ActorId),
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(&kyc.CheckKYCStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
			},
		},
		{
			name: "No liveness summary, creating a new one",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsFixture),
				},
			},
			want: &StageProcessorResponse{
				NextAction: checkLivenessDL(mockLA),
			},
			wantErr: nil,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), mockGetLivenessSummaryStatusReq).Return(&livPb.GetLivenessSummaryStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockStruct.kycClient.EXPECT().GetKYCVendorData(gomock.Any(), &kyc.GetKYCVendorDataRequest{
					ActorId: args.req.GetOnb().ActorId,
				}).Return(mockVendorDataRes, nil)
				mockStruct.onbDao.EXPECT().GetOnboardingDetailsById(gomock.Any(), gomock.Any()).Return(getNewOnbDetailsFixture(onbDetailsFixture), nil)
				mockStruct.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockStruct.livClient.EXPECT().CreateLivenessSummary(gomock.Any(), mock.NewProtoMatcher(&livPb.CreateLivenessSummaryRequest{
					ActorId:            args.req.GetOnb().ActorId,
					LivenessFlow:       livPb.LivenessFlow_ONBOARDING,
					MaxRetries:         3,
					RefFacematchPhotos: mockVendorDataRes.VendorData.Payload.CkycDownloads.Payloads[0].ImagesData,
					ForceManualReview:  commontypes.BooleanEnum_FALSE,
				}, protocmp.IgnoreFields(&livPb.CreateLivenessSummaryRequest{}, "request_id", "expiry_duration"))).Return(&livPb.CreateLivenessSummaryResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockStruct.livClient.EXPECT().CreateLivenessAttempt(gomock.Any(), mock.NewProtoMatcher(&livPb.CreateLivenessAttemptRequest{
					ActorId:      args.req.GetOnb().ActorId,
					LivenessFlow: livPb.LivenessFlow_ONBOARDING,
				}, protocmp.IgnoreFields(&livPb.CreateLivenessAttemptRequest{}, "summary_request_id"))).Return(&livPb.CreateLivenessAttemptResponse{
					Status:          rpc.StatusOk(),
					LivenessAttempt: mockLA,
				}, nil)
				mockStruct.livClient.EXPECT().GetLivenessAttempts(gomock.Any(), gomock.Any()).Return(nil, nil)
				mockStruct.livClient.EXPECT().GetFaceMatchAttempts(gomock.Any(), gomock.Any()).Return(nil, nil)
			},
		},
		{
			name: "No liveness summary but past attempt is manually failed",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsFixture),
				},
			},
			want: &StageProcessorResponse{
				NextAction: failureDL,
			},
			wantErr: nil,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), mockGetLivenessSummaryStatusReq).Return(&livPb.GetLivenessSummaryStatusResponse{
					Status: rpc.StatusRecordNotFound(),
				}, nil)
				mockStruct.livClient.EXPECT().GetLivenessAttempts(gomock.Any(), gomock.Any()).Return(&livPb.GetLivenessAttemptsResponse{
					Status: rpc.StatusOk(),
					LivenessAttempts: []*livPb.LivenessAttempt{
						{
							Status: livPb.LivenessStatus_LIVENESS_PASSED,
						},
						{
							Status: livPb.LivenessStatus_LIVENESS_MANUALLY_FAILED,
						},
					},
				}, nil)
			},
		},
		{
			name: "liveness manually failed and DEVICE_REGISTRATION complete",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsFixtureWithDevRegComplete),
				},
			},
			want: &StageProcessorResponse{
				NextAction: failureDL,
			},
			wantErr: nil,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), mockGetLivenessSummaryStatusReq).Return(&livPb.GetLivenessSummaryStatusResponse{
					Status:                rpc.StatusOk(),
					SummaryStatus:         livPb.SummaryStatus_SUMMARY_RETRIES_EXHAUSTED,
					SummaryLivenessStatus: livPb.SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_FAILED,
					MaxAttempts:           1,
					AttemptCount:          1,
				}, nil)
				mockStruct.onbDao.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onbDetailsFixture, nil).AnyTimes()
				mockStruct.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), onbDetailsFixture.GetActorId(), true, userEvents.BlockReasonRisk)
			},
		},
		{
			name: "liveness manually failed but DEVICE_REGISTRATION not complete",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsFixture),
				},
			},
			wantErr: NoActionSkipStatusUpdateError,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), mockGetLivenessSummaryStatusReq).Return(&livPb.GetLivenessSummaryStatusResponse{
					Status:                rpc.StatusOk(),
					SummaryStatus:         livPb.SummaryStatus_SUMMARY_RETRIES_EXHAUSTED,
					SummaryLivenessStatus: livPb.SummaryLivenessStatus_SUMMARY_LIVENESS_MANUALLY_FAILED,
					MaxAttempts:           1,
					AttemptCount:          1,
				}, nil)
				mockStruct.onbDao.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onbDetailsFixture, nil).AnyTimes()
			},
		},
		{
			name: "Liveness in RESET state",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsWithLivenessReset),
				},
			},
			want: &StageProcessorResponse{
				NextAction: nextActionDL,
			},
			wantErr: nil,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.kycClient.EXPECT().GetKYCVendorData(gomock.Any(), &kyc.GetKYCVendorDataRequest{
					ActorId: onbDetailsFixture.ActorId,
				}).Return(mockVendorDataRes, nil)
				mockStruct.livClient.EXPECT().RedoFaceMatch(gomock.Any(), &livPb.RedoFaceMatchRequest{
					ActorId:          args.req.GetOnb().ActorId,
					SummaryRequestId: args.req.GetOnb().OnboardingId,
					LivenessFlow:     livPb.LivenessFlow_ONBOARDING,
					ReferencePhotos:  mockVendorDataRes.VendorData.Payload.CkycDownloads.Payloads[0].ImagesData,
				}).Return(&livPb.RedoFaceMatchResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockStruct.onbDao.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(getNewOnbDetailsFixture(onbDetailsWithEKYCSkipped), nil).AnyTimes()
				mockStruct.livClient.EXPECT().GetLivenessSummary(gomock.Any(), gomock.Any()).Return(&livPb.GetLivenessSummaryResponse{
					Status: rpc.StatusOk(),
					Summary: &livPb.LivenessSummary{
						Status: livPb.SummaryStatus_SUMMARY_PASSED,
					},
				}, nil)
			},
		},
		{
			name: "Liveness in RESET state, liveness summary expired",
			args: args{
				req: &StageProcessorRequest{
					Onb: onbDetailsWithLivenessReset,
				},
			},
			want: &StageProcessorResponse{
				NextAction: nextActionDL,
			},
			wantErr: nil,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummary(gomock.Any(), gomock.Any()).Return(&livPb.GetLivenessSummaryResponse{
					Summary: &livPb.LivenessSummary{
						Status: livPb.SummaryStatus_SUMMARY_EXPIRED,
					},
					Status: rpc.StatusOk(),
				}, nil)
				mockStruct.onbDao.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(onbDetailsWithEKYCSkipped, nil).AnyTimes()
			},
		},
		{
			name: "Liveness summary expired, creating new liveness summary",
			args: args{
				req: &StageProcessorRequest{
					Onb: getNewOnbDetailsFixture(onbDetailsFixture),
				},
			},
			want: &StageProcessorResponse{
				NextAction: checkLivenessDL(mockLA),
			},
			wantErr: nil,
			mocks: func(args args, mockStruct *mockStruct) {
				mockStruct.kycClient.EXPECT().CheckKYCStatus(gomock.Any(), mockCheckKYCStatusReq).Return(mockSuccessCheckKYCStatusRes, nil)
				mockStruct.livClient.EXPECT().GetLivenessSummaryStatus(gomock.Any(), mockGetLivenessSummaryStatusReq).Return(&livPb.GetLivenessSummaryStatusResponse{
					Status:        rpc.StatusOk(),
					SummaryStatus: livPb.SummaryStatus_SUMMARY_EXPIRED,
				}, nil)
				mockStruct.kycClient.EXPECT().GetKYCVendorData(gomock.Any(), &kyc.GetKYCVendorDataRequest{
					ActorId: args.req.GetOnb().ActorId,
				}).Return(mockVendorDataRes, nil)
				mockStruct.onbDao.EXPECT().GetOnboardingDetailsById(gomock.Any(), args.req.GetOnb().GetOnboardingId()).Return(args.req.GetOnb(), nil)
				mockStruct.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mockStruct.livClient.EXPECT().CreateLivenessSummary(gomock.Any(), mock.NewProtoMatcher(&livPb.CreateLivenessSummaryRequest{
					ActorId:            args.req.GetOnb().ActorId,
					LivenessFlow:       livPb.LivenessFlow_ONBOARDING,
					MaxRetries:         3,
					RefFacematchPhotos: mockVendorDataRes.VendorData.Payload.CkycDownloads.Payloads[0].ImagesData,
					ForceManualReview:  commontypes.BooleanEnum_FALSE,
				}, protocmp.IgnoreFields(&livPb.CreateLivenessSummaryRequest{}, "request_id", "expiry_duration"))).Return(&livPb.CreateLivenessSummaryResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockStruct.livClient.EXPECT().CreateLivenessAttempt(gomock.Any(), mock.NewProtoMatcher(&livPb.CreateLivenessAttemptRequest{
					ActorId:      args.req.GetOnb().ActorId,
					LivenessFlow: livPb.LivenessFlow_ONBOARDING,
				}, protocmp.IgnoreFields(&livPb.CreateLivenessAttemptRequest{}, "summary_request_id"))).Return(&livPb.CreateLivenessAttemptResponse{
					Status:          rpc.StatusOk(),
					LivenessAttempt: mockLA,
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			kycClient := kycMocks.NewMockKycClient(ctrl)
			onbDao := daoMocks.NewMockOnboardingDao(ctrl)
			livClient := livMocks.NewMockLivenessClient(ctrl)
			userClient := userMocks.NewMockUsersClient(ctrl)
			eventLogger := eventMocks.NewMockEventLogger(ctrl)
			s3Client := mockS3.NewMockS3Client(ctrl)

			if tt.mocks != nil {
				tt.mocks(tt.args, &mockStruct{
					kycClient:   kycClient,
					onbDao:      onbDao,
					livClient:   livClient,
					userClient:  userClient,
					eventLogger: eventLogger,
					s3Client:    s3Client,
				})
			}

			s := &LivenessStage{
				kycClient:     kycClient,
				onboardingDao: onbDao,
				livClient:     livClient,
				conf:          ts.GenConf.Onboarding(),
				userClient:    userClient,
				eventBroker:   &mocks.NoTestMockBroker{},
				eventLogger:   eventLogger,
				userConf:      ts.GenConf,
				s3Client:      s3Client,
			}
			// Setting this as true for unit tests
			ts.Conf.Onboarding.Flags.AllowManualReviewUsers = true
			err := ts.GenConf.Onboarding().Set(ts.Conf.Onboarding, false, nil)
			if err != nil {
				return
			}
			got, err := s.StageProcessor(ctx, tt.args.req)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Unexpected error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			if diff := cmp.Diff(got.GetNextAction(), tt.want.GetNextAction(), protocmp.Transform()); diff != "" {
				t.Errorf("StageProcessor value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}

func getNewOnbDetailsFixture(onbDetails *onbPb.OnboardingDetails) *onbPb.OnboardingDetails {
	newOnbDetails := &onbPb.OnboardingDetails{}
	*newOnbDetails = *onbDetails
	return newOnbDetails
}
