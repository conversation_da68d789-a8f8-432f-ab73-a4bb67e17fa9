package stageproc

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/pkg/errors"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	timeMocks "github.com/epifi/be-common/pkg/datetime/mocks"

	bcPb "github.com/epifi/gamma/api/bankcust"
	bankCustMock "github.com/epifi/gamma/api/bankcust/mocks"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/kyc"
	kycMocks "github.com/epifi/gamma/api/kyc/mocks"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/kyc/vkyc/mocks"
	"github.com/epifi/gamma/api/pan"
	panMocks "github.com/epifi/gamma/api/pan/mocks"
	userPb "github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	vgPbCustomer "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	panPb "github.com/epifi/gamma/api/vendorgateway/pan"
	vgMockPan "github.com/epifi/gamma/api/vendorgateway/pan/mocks"
	"github.com/epifi/gamma/pkg/obfuscator"
	userEvents "github.com/epifi/gamma/user/events"
	eventMocks "github.com/epifi/gamma/user/events/mocks"
	daoMocks "github.com/epifi/gamma/user/onboarding/dao/mocks"
	deeplink2 "github.com/epifi/gamma/user/onboarding/helper/deeplink"
	dedupeMock "github.com/epifi/gamma/user/onboarding/helper/mocks"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

func TestPreCustomerCreationCheckStage_StageProcessor(t *testing.T) {
	var (
		someDL = &deeplink.Deeplink{
			Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
			ScreenOptions: &deeplink.Deeplink_DetailedErrorViewScreenOptions{
				DetailedErrorViewScreenOptions: &deeplink.DetailedErrorViewScreenOptions{},
			},
		}
		timeNow    = time.Now()
		onbFixture = &onbPb.OnboardingDetails{
			ActorId:      "actor-id",
			OnboardingId: "onb-id",
			UserId:       "user-id",
			StageDetails: &onbPb.StageDetails{
				StageMapping: map[string]*onbPb.StageInfo{
					onbPb.OnboardingStage_LIVENESS.String(): {
						State: onbPb.OnboardingState_SUCCESS,
					},
					onbPb.OnboardingStage_RISK_SCREENING.String(): {
						State:         onbPb.OnboardingState_SUCCESS,
						LastUpdatedAt: timestampPb.New(timeNow),
					},
					onbPb.OnboardingStage_VKYC.String(): {
						State:         onbPb.OnboardingState_SKIPPED,
						LastUpdatedAt: timestampPb.New(timeNow),
					},
					onbPb.OnboardingStage_AADHAR_MOBILE_VALIDATION.String(): {
						State:         onbPb.OnboardingState_SKIPPED,
						LastUpdatedAt: timestampPb.New(timeNow),
					},
				},
			},
			Feature: onbPb.Feature_FEATURE_SA,
		}
		kycRecord = &kyc.KYCRecord{
			IdentityProofs:  []*kyc.IdProof{{Type: kyc.IdProofType_DRIVING_LICENSE, IdValue: "driving-license"}},
			KycLevel:        kyc.KYCLevel_FULL_KYC,
			UidReferenceKey: "uid-ref-key",
		}
		getUserReq = &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_Id{
				Id: onbFixture.UserId,
			},
		}
		getUserResp = &userPb.GetUserResponse{
			Status: rpc.StatusOk(),
			User: &userPb.User{
				Id: onbFixture.UserId,
				Profile: &userPb.Profile{
					PAN: "UNQPP",
					PhoneNumber: &commontypes.PhoneNumber{
						CountryCode:    91,
						NationalNumber: **********,
					},
					DateOfBirth: &date.Date{
						Year:  1994,
						Month: 10,
						Day:   10,
					},
				},
			},
		}
		getKYCRecordReq = &kyc.GetKYCRecordRequest{
			ActorId: onbFixture.GetActorId(),
		}
		getKYCRecordResp = &kyc.GetKYCRecordResponse{
			Status:    rpc.StatusOk(),
			KycType:   kyc.KycType_CKYC,
			KycRecord: kycRecord,
		}
		gNow = datetime.DateToTimestamp(&date.Date{
			Year:  2024,
			Month: 03,
			Day:   01,
		}, datetime.IST)
		bankCustomerFixture = &bcPb.BankCustomer{
			Id:               "id-1",
			VendorCustomerId: "bank-customer-1",
			ActorId:          "1234",
			UserId:           "user-id-1",
			Name: &commontypes.Name{
				FirstName:  "Neil",
				MiddleName: "Nitin",
				LastName:   "Mukesh",
				Honorific:  "Dr.",
			},
			Vendor:                    commonvgpb.Vendor_FEDERAL_BANK,
			CreationStartedAt:         gNow,
			VendorCreationSucceededAt: gNow,
			FiCreationSucceededAt:     gNow,
			KycLevelUpdateFlow:        bcPb.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_POST_ONBOARDING,
			VendorMetadata: &bcPb.VendorMetadata{
				Vendor: &bcPb.VendorMetadata_FederalMetadata{
					FederalMetadata: &bcPb.FederalMetadata{
						CifRequestId: "reqId",
						EkycRrnNo:    "ekyc_1234",
					},
				},
			},
			DedupeInfo: &bcPb.DedupeInfo{
				KycLevel: kyc.KYCLevel_FULL_KYC,
			},
		}
	)
	type mockStruct struct {
		onbDao      *daoMocks.MockOnboardingDao
		userClient  *userMocks.MockUsersClient
		kycClient   *kycMocks.MockKycClient
		timeClient  *timeMocks.MockTime
		dedupeProc  *dedupeMock.MockIDedupeHelper
		eventLogger *eventMocks.MockEventLogger
		userProc    *dedupeMock.MockUserProcessor
		vkycClient  *mocks.MockVKYCClient
		bcClient    *bankCustMock.MockBankCustomerServiceClient
		panClient   *panMocks.MockPanClient
		kycProc     *dedupeMock.MockIKYCHelper
		vgPanClient *vgMockPan.MockPANClient
	}
	type args struct {
		req *StageProcessorRequest
	}
	tests := map[string]struct {
		args     args
		want     *StageProcessorResponse
		wantErr  error
		mockFunc func(*mockStruct, args)
	}{
		"LIVENESS is active": {
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						StageDetails: &onbPb.StageDetails{
							StageMapping: map[string]*onbPb.StageInfo{
								onbPb.OnboardingStage_LIVENESS.String(): {
									State: onbPb.OnboardingState_INPROGRESS,
								},
							},
						},
					},
				},
			},
			mockFunc: func(mockStruct *mockStruct, args args) {
				mockStruct.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(getUserResp, nil)
				mockStruct.userClient.EXPECT().GetUsers(gomock.Any(), &userPb.GetUsersRequest{
					Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
								Pan: getUserResp.GetUser().GetProfile().GetPAN(),
							},
						},
					},
				}).Return(&userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want: &StageProcessorResponse{
				NextAction: deeplink2.NewActionToGetNextAction(),
			},
		},
		"User blocked due to pan uniqueness check": {
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId: onbFixture.GetActorId(),
						StageDetails: &onbPb.StageDetails{
							StageMapping: map[string]*onbPb.StageInfo{
								onbPb.OnboardingStage_LIVENESS.String(): {
									State: onbPb.OnboardingState_INPROGRESS,
								},
							},
						},
					},
				},
			},
			mockFunc: func(mockStruct *mockStruct, args args) {
				mockStruct.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(getUserResp, nil)
				mockStruct.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{
					onbPb.OnboardingDetailsFieldMask_STAGE_METADATA,
				}, &onbPb.OnboardingDetails{
					OnboardingId: args.req.GetOnb().GetOnboardingId(),
					StageMetadata: &onbPb.StageMetadata{
						PreCustomerCreationMetadata: &onbPb.PreCustomerCreationMetadata{
							FailureReason: onbPb.PreCustomerCreationMetadata_FAILURE_REASON_PAN_UNIQUENESS_CHECK,
						},
					},
				})
				mockStruct.userClient.EXPECT().GetUsers(gomock.Any(), &userPb.GetUsersRequest{
					Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
								Pan: getUserResp.GetUser().GetProfile().GetPAN(),
							},
						},
					},
				}).Return(&userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
					Users: []*userPb.User{
						{
							Id:      "user-id-2",
							ActorId: "actor-id-2",
							Profile: &userPb.Profile{
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
							},
						},
					},
				}, nil)
				mockStruct.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bcPb.CheckBankCustomerCreationStatusRequest{
					ActorId: "actor-id-2",
					Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
				}).Return(&bcPb.CheckBankCustomerCreationStatusResponse{
					Status: rpc.NewStatusFactory(uint32(bcPb.CheckBankCustomerCreationStatusResponse_OK), "customer already created.")(),
				}, nil)
				mockStruct.onbDao.EXPECT().UpdateStatus(gomock.Any(), args.req.GetOnb().GetOnboardingId(), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK, onbPb.OnboardingState_FAILURE).Return(nil, nil)
				mockStruct.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), args.req.GetOnb().GetActorId(), true, userEvents.BlockReasonTerminal)
			},
			want: &StageProcessorResponse{
				NextAction: &deeplink.Deeplink{
					Screen: deeplink.Screen_ACCOUNT_DELETION_ACKNOWLEDGEMENT,
					ScreenOptions: &deeplink.Deeplink_AccountDeletionAcknowledgementScreenOptions{
						AccountDeletionAcknowledgementScreenOptions: &deeplink.AccountDeletionAcknowledgementScreenOptions{
							ImageUrl: "https://epifi-icons.pointz.in/onboarding/caveman.png",
							Title:    constant.DeviceAlreadyRegisteredWithPan,
							Subtitle: fmt.Sprintf("The existing account is already linked to mobile number <b>xxxxxx1231</b> for signup. Please resume your journey again with the same phone number."),
							Cta: &deeplink.Cta{
								Text: "Retry with registered mobile number",
							},
							DeletionReason: userPb.DeletionDetails_DELETION_REASON_DEDUPE_MISMATCH.String(),
						},
					},
				},
			},
		},
		"User not blocked due to as customer not created for other PAN": {
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			mockFunc: func(mockStruct *mockStruct, args args) {
				mockStruct.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(getUserResp, nil)
				mockStruct.userClient.EXPECT().GetUsers(gomock.Any(), &userPb.GetUsersRequest{
					Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
								Pan: getUserResp.GetUser().GetProfile().GetPAN(),
							},
						},
					},
				}).Return(&userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
					Users: []*userPb.User{
						{
							Id:      "user-id-2",
							ActorId: "actor-id-2",
							Profile: &userPb.Profile{
								PhoneNumber: &commontypes.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
							},
						},
					},
				}, nil)
				mockStruct.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bcPb.CheckBankCustomerCreationStatusRequest{
					ActorId: "actor-id-2",
					Vendor:  commonvgpb.Vendor_FEDERAL_BANK,
				}).Return(&bcPb.CheckBankCustomerCreationStatusResponse{
					Status: rpc.NewStatusFactory(uint32(bcPb.CheckBankCustomerCreationStatusResponse_NOT_STARTED), "customer already created.")(),
				}, nil)
				mockStruct.dedupeProc.EXPECT().AllowDedupeCheck(gomock.Any(), args.req.GetOnb(), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(true, nil)
				mockStruct.userClient.EXPECT().GetUser(gomock.Any(), getUserReq).Return(getUserResp, nil)
				mockStruct.kycClient.EXPECT().GetKYCRecord(gomock.Any(), getKYCRecordReq).Return(getKYCRecordResp, nil)
				mockStruct.dedupeProc.EXPECT().DedupeCheckCore(gomock.Any(), getUserResp.GetUser(), onbFixture.GetVendor(),
					append(getKYCRecordResp.GetKycRecord().GetIdentityProofs(), &kyc.IdProof{
						Type:    kyc.IdProofType_UID,
						IdValue: getKYCRecordResp.GetKycRecord().GetUidReferenceKey(),
					}), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(true, vgPbCustomer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS, nil)
				mockStruct.dedupeProc.EXPECT().UpdateDedupeFlagInOnboardingDetails(gomock.Any(), onbFixture,
					vgPbCustomer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS, onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(nil)
				mockStruct.bcClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bcPb.GetBankCustomerRequest_ActorId{
						ActorId: args.req.GetOnb().GetActorId(),
					},
				}).Return(&bcPb.GetBankCustomerResponse{
					Status:       rpc.StatusOk(),
					BankCustomer: bankCustomerFixture}, nil)
				mockStruct.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), onbFixture.GetActorId()).Return(false, nil)
				mockStruct.timeClient.EXPECT().Since(args.req.GetOnb().GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_RISK_SCREENING.String()].GetLastUpdatedAt().AsTime()).Return(12 * 24 * time.Hour)
				mockStruct.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), onbFixture.GetActorId(), false, "")
				mockStruct.panClient.EXPECT().GetPANAadharLinkStatus(gomock.Any(), &pan.GetPANAadharLinkStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&pan.GetPANAadharLinkStatusResponse{
					Status:              rpc.StatusOk(),
					PanAadharLinkStatus: pan.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED,
				}, nil)
			},
			want: &StageProcessorResponse{
				NextAction: nil,
			},
			wantErr: NoActionError,
		},
		"dedupe customer exist, phone linked to existing account": {
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want: &StageProcessorResponse{
				NextAction: someDL,
			},
			mockFunc: func(m *mockStruct, args args) {
				m.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(getUserResp, nil)
				m.userClient.EXPECT().GetUsers(gomock.Any(), &userPb.GetUsersRequest{
					Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
								Pan: getUserResp.GetUser().GetProfile().GetPAN(),
							},
						},
					},
				}).Return(&userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.dedupeProc.EXPECT().AllowDedupeCheck(gomock.Any(), args.req.GetOnb(), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(true, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), getUserReq).Return(getUserResp, nil)
				m.kycClient.EXPECT().GetKYCRecord(gomock.Any(), getKYCRecordReq).Return(getKYCRecordResp, nil)
				m.dedupeProc.EXPECT().DedupeCheckCore(gomock.Any(), getUserResp.GetUser(), onbFixture.GetVendor(),
					append(getKYCRecordResp.GetKycRecord().GetIdentityProofs(), &kyc.IdProof{
						Type:    kyc.IdProofType_UID,
						IdValue: getKYCRecordResp.GetKycRecord().GetUidReferenceKey(),
					}), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(false, vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT, nil)
				m.dedupeProc.EXPECT().UpdateDedupeFlagInOnboardingDetails(gomock.Any(), onbFixture,
					vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT, onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(nil)
				m.onbDao.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), onbFixture.GetActorId()).Return(false, nil)
				m.timeClient.EXPECT().Since(args.req.GetOnb().GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_RISK_SCREENING.String()].GetLastUpdatedAt().AsTime()).Return(12 * 24 * time.Hour)
				m.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), onbFixture.GetActorId(), true, userEvents.BlockReasonTerminal)
				m.dedupeProc.EXPECT().DedupeDeeplinkHandler(gomock.Any(), gomock.Any(), vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT).Return(someDL)
				m.panClient.EXPECT().GetPANAadharLinkStatus(gomock.Any(), &pan.GetPANAadharLinkStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&pan.GetPANAadharLinkStatusResponse{
					Status:              rpc.StatusOk(),
					PanAadharLinkStatus: pan.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED,
				}, nil)
			},
		},
		"customer does not exist": {
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want: &StageProcessorResponse{
				NextAction: nil,
			},
			wantErr: NoActionError,
			mockFunc: func(m *mockStruct, args args) {
				m.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(getUserResp, nil)
				m.userClient.EXPECT().GetUsers(gomock.Any(), &userPb.GetUsersRequest{
					Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
								Pan: getUserResp.GetUser().GetProfile().GetPAN(),
							},
						},
					},
				}).Return(&userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.dedupeProc.EXPECT().AllowDedupeCheck(gomock.Any(), args.req.GetOnb(), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(true, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), getUserReq).Return(getUserResp, nil)
				m.kycClient.EXPECT().GetKYCRecord(gomock.Any(), getKYCRecordReq).Return(getKYCRecordResp, nil)
				m.dedupeProc.EXPECT().DedupeCheckCore(gomock.Any(), getUserResp.GetUser(), onbFixture.GetVendor(),
					append(getKYCRecordResp.GetKycRecord().GetIdentityProofs(), &kyc.IdProof{
						Type:    kyc.IdProofType_UID,
						IdValue: getKYCRecordResp.GetKycRecord().GetUidReferenceKey(),
					}), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(true, vgPbCustomer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS, nil)
				m.dedupeProc.EXPECT().UpdateDedupeFlagInOnboardingDetails(gomock.Any(), onbFixture,
					vgPbCustomer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS, onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(nil)
				m.bcClient.EXPECT().GetBankCustomer(context.Background(), &bcPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bcPb.GetBankCustomerRequest_ActorId{
						ActorId: args.req.GetOnb().GetActorId(),
					},
				}).Return(&bcPb.GetBankCustomerResponse{
					Status:       rpc.StatusOk(),
					BankCustomer: bankCustomerFixture}, nil)
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), onbFixture.GetActorId()).Return(false, nil)
				m.timeClient.EXPECT().Since(args.req.GetOnb().GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_RISK_SCREENING.String()].GetLastUpdatedAt().AsTime()).Return(12 * 24 * time.Hour)
				m.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), onbFixture.GetActorId(), false, "")
				m.panClient.EXPECT().GetPANAadharLinkStatus(gomock.Any(), &pan.GetPANAadharLinkStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&pan.GetPANAadharLinkStatusResponse{
					Status:              rpc.StatusOk(),
					PanAadharLinkStatus: pan.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED,
				}, nil)
			},
		},
		"Partial Dedupe Customer": {
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink2.NewActionToGetNextAction(),
			},
			wantErr: nil,
			mockFunc: func(m *mockStruct, args args) {
				m.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(getUserResp, nil)
				m.userClient.EXPECT().GetUsers(gomock.Any(), &userPb.GetUsersRequest{
					Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
								Pan: getUserResp.GetUser().GetProfile().GetPAN(),
							},
						},
					},
				}).Return(&userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.dedupeProc.EXPECT().AllowDedupeCheck(gomock.Any(), args.req.GetOnb(), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(true, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), getUserReq).Return(getUserResp, nil)
				m.kycClient.EXPECT().GetKYCRecord(gomock.Any(), getKYCRecordReq).Return(getKYCRecordResp, nil)
				m.dedupeProc.EXPECT().DedupeCheckCore(gomock.Any(), getUserResp.GetUser(), onbFixture.GetVendor(),
					append(getKYCRecordResp.GetKycRecord().GetIdentityProofs(), &kyc.IdProof{
						Type:    kyc.IdProofType_UID,
						IdValue: getKYCRecordResp.GetKycRecord().GetUidReferenceKey(),
					}), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(true, vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PARTIAL_KYC, nil)
				m.dedupeProc.EXPECT().UpdateDedupeFlagInOnboardingDetails(gomock.Any(), onbFixture,
					vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PARTIAL_KYC, onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(nil)
				m.vkycClient.EXPECT().GetVKYCSummary(gomock.Any(), &vkycPb.GetVKYCSummaryRequest{
					ActorId: onbFixture.GetActorId(),
				}).Return(&vkycPb.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkycPb.VKYCRecord{
						VkycSummary: &vkycPb.VKYCSummary{
							Status: vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_UNSPECIFIED,
						},
					},
				}, nil)
				m.onbDao.EXPECT().UpdateStageStatuses(gomock.Any(), onbFixture.GetOnboardingId(), map[onbPb.OnboardingStage]onbPb.OnboardingState{
					onbPb.OnboardingStage_UPDATE_CUSTOMER_DETAILS: onbPb.OnboardingState_RESET,
					onbPb.OnboardingStage_VKYC:                    onbPb.OnboardingState_RESET,
				}).Return(nil)
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), onbFixture.GetActorId()).Return(false, nil)
				m.timeClient.EXPECT().Since(args.req.GetOnb().GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_RISK_SCREENING.String()].GetLastUpdatedAt().AsTime()).Return(12 * 24 * time.Hour)
				m.panClient.EXPECT().GetPANAadharLinkStatus(gomock.Any(), &pan.GetPANAadharLinkStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&pan.GetPANAadharLinkStatusResponse{
					Status:              rpc.StatusOk(),
					PanAadharLinkStatus: pan.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED,
				}, nil)
			},
		},
		"Partial Dedupe Customer with VKYC approved": {
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want: &StageProcessorResponse{
				NextAction: ActionForUserStuckOnAcctCreation(context.Background(), onbFixture),
			},
			wantErr: nil,
			mockFunc: func(m *mockStruct, args args) {
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), onbFixture.GetActorId()).Return(false, nil)
				m.timeClient.EXPECT().Since(args.req.GetOnb().GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_RISK_SCREENING.String()].GetLastUpdatedAt().AsTime()).Return(12 * 24 * time.Hour)
				m.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(getUserResp, nil)
				m.userClient.EXPECT().GetUsers(gomock.Any(), &userPb.GetUsersRequest{
					Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
								Pan: getUserResp.GetUser().GetProfile().GetPAN(),
							},
						},
					},
				}).Return(&userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.dedupeProc.EXPECT().AllowDedupeCheck(gomock.Any(), args.req.GetOnb(), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(true, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), getUserReq).Return(getUserResp, nil)
				m.kycClient.EXPECT().GetKYCRecord(gomock.Any(), getKYCRecordReq).Return(getKYCRecordResp, nil)
				m.dedupeProc.EXPECT().DedupeCheckCore(gomock.Any(), getUserResp.GetUser(), onbFixture.GetVendor(),
					append(getKYCRecordResp.GetKycRecord().GetIdentityProofs(), &kyc.IdProof{
						Type:    kyc.IdProofType_UID,
						IdValue: getKYCRecordResp.GetKycRecord().GetUidReferenceKey(),
					}), onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(true, vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PARTIAL_KYC, nil)
				m.dedupeProc.EXPECT().UpdateDedupeFlagInOnboardingDetails(gomock.Any(), onbFixture,
					vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PARTIAL_KYC, onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK).Return(nil)
				m.vkycClient.EXPECT().GetVKYCSummary(gomock.Any(), &vkycPb.GetVKYCSummaryRequest{
					ActorId: onbFixture.GetActorId(),
				}).Return(&vkycPb.GetVKYCSummaryResponse{
					Status: rpc.StatusOk(),
					VkycRecord: &vkycPb.VKYCRecord{
						VkycSummary: &vkycPb.VKYCSummary{
							Status: vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED,
						},
					},
				}, nil)
				m.eventLogger.EXPECT().LogOnboardingRiskBlocking(gomock.Any(), args.req.GetOnb().GetActorId(), true, userEvents.BlockReasonTerminal)
				m.panClient.EXPECT().GetPANAadharLinkStatus(gomock.Any(), &pan.GetPANAadharLinkStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&pan.GetPANAadharLinkStatusResponse{
					Status:              rpc.StatusOk(),
					PanAadharLinkStatus: pan.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED,
				}, nil)
			},
		},
		"customer creation in-progress, skip stage": {
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			wantErr: SkipStageError,
			mockFunc: func(m *mockStruct, args args) {
				m.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(getUserResp, nil)
				m.userClient.EXPECT().GetUsers(gomock.Any(), &userPb.GetUsersRequest{
					Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
								Pan: getUserResp.GetUser().GetProfile().GetPAN(),
							},
						},
					},
				}).Return(&userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), onbFixture.GetActorId()).Return(true, nil)
				m.panClient.EXPECT().GetPANAadharLinkStatus(gomock.Any(), &pan.GetPANAadharLinkStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&pan.GetPANAadharLinkStatusResponse{
					Status:              rpc.StatusOk(),
					PanAadharLinkStatus: pan.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED,
				}, nil)
			},
		},
		"reset risk screening for old users": {
			args: args{
				req: &StageProcessorRequest{
					Onb: &onbPb.OnboardingDetails{
						ActorId: "actor_id",
						StageDetails: &onbPb.StageDetails{
							StageMapping: map[string]*onbPb.StageInfo{
								onbPb.OnboardingStage_RISK_SCREENING.String(): {
									State:         onbPb.OnboardingState_SUCCESS,
									LastUpdatedAt: timestampPb.New(timeNow.Add(-20 * 24 * time.Hour)),
								},
								onbPb.OnboardingStage_LIVENESS.String(): {
									State:         onbPb.OnboardingState_SUCCESS,
									LastUpdatedAt: timestampPb.New(timeNow),
								},
								onbPb.OnboardingStage_VKYC.String(): {
									State:         onbPb.OnboardingState_SKIPPED,
									LastUpdatedAt: timestampPb.New(timeNow),
								},
								onbPb.OnboardingStage_AADHAR_MOBILE_VALIDATION.String(): {
									State:         onbPb.OnboardingState_SKIPPED,
									LastUpdatedAt: timestampPb.New(timeNow),
								},
							},
						},
						Vendor:                 commonvgpb.Vendor_FEDERAL_BANK,
						OnboardingId:           "onb_id",
						UserId:                 "user_id",
						CreatedAt:              timestampPb.New(timeNow),
						CurrentOnboardingStage: onbPb.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK,
					},
				},
			},
			mockFunc: func(mock *mockStruct, args args) {
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(getUserResp, nil)
				mock.userClient.EXPECT().GetUsers(gomock.Any(), &userPb.GetUsersRequest{
					Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
								Pan: getUserResp.GetUser().GetProfile().GetPAN(),
							},
						},
					},
				}).Return(&userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mock.userProc.EXPECT().IsCustomerCreationInProgress(gomock.Any(), "actor_id").Return(false, nil)
				mock.timeClient.EXPECT().Since(args.req.GetOnb().GetStageDetails().GetStageMapping()[onbPb.OnboardingStage_RISK_SCREENING.String()].GetLastUpdatedAt().AsTime()).Return(16 * 24 * time.Hour)
				mock.onbDao.EXPECT().UpdateStageStatuses(gomock.Any(), args.req.GetOnb().GetOnboardingId(), map[onbPb.OnboardingStage]onbPb.OnboardingState{
					onbPb.OnboardingStage_RISK_SCREENING: onbPb.OnboardingState_RESET,
				}).Return(nil)
				mock.panClient.EXPECT().GetPANAadharLinkStatus(gomock.Any(), &pan.GetPANAadharLinkStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&pan.GetPANAadharLinkStatusResponse{
					Status:              rpc.StatusOk(),
					PanAadharLinkStatus: pan.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED,
				}, nil)
			},
			want: &StageProcessorResponse{
				NextAction: deeplink2.NewActionToGetNextAction(),
			},
			wantErr: nil,
		},
		"pan and aadhaar not linked": {
			args: args{
				req: &StageProcessorRequest{
					Onb: onbFixture,
				},
			},
			want: &StageProcessorResponse{
				NextAction: deeplink2.NewErrorFullScreen(context.Background(), error2.PanAadhaarNotLinked),
			},
			wantErr: nil,
			mockFunc: func(m *mockStruct, args args) {
				m.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: args.req.GetOnb().GetUserId(),
					},
				}).Return(getUserResp, nil)
				m.userClient.EXPECT().GetUsers(gomock.Any(), &userPb.GetUsersRequest{
					Identifier: []*userPb.GetUsersRequest_GetUsersIdentifier{
						{
							Identifier: &userPb.GetUsersRequest_GetUsersIdentifier_Pan{
								Pan: getUserResp.GetUser().GetProfile().GetPAN(),
							},
						},
					},
				}).Return(&userPb.GetUsersResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.panClient.EXPECT().GetPANAadharLinkStatus(gomock.Any(), &pan.GetPANAadharLinkStatusRequest{
					ActorId: args.req.GetOnb().GetActorId(),
				}).Return(&pan.GetPANAadharLinkStatusResponse{
					Status:              rpc.StatusOk(),
					PanAadharLinkStatus: pan.PANAadharLinkStatus_PAN_AADHAR_STATUS_NOT_LINKED,
				}, nil)
			},
		},
	}
	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			onbDao := daoMocks.NewMockOnboardingDao(ctr)
			userClient := userMocks.NewMockUsersClient(ctr)
			kycClient := kycMocks.NewMockKycClient(ctr)
			timeClient := timeMocks.NewMockTime(ctr)
			dedupeProc := dedupeMock.NewMockIDedupeHelper(ctr)
			mockEventLogger := eventMocks.NewMockEventLogger(ctr)
			userProc := dedupeMock.NewMockUserProcessor(ctr)
			mockVKYCClient := mocks.NewMockVKYCClient(ctr)
			mockBcClient := bankCustMock.NewMockBankCustomerServiceClient(ctr)
			mockPanClient := panMocks.NewMockPanClient(ctr)
			mockKycProc := dedupeMock.NewMockIKYCHelper(ctr)
			mockVgPanClient := vgMockPan.NewMockPANClient(ctr)

			if tt.mockFunc != nil {
				tt.mockFunc(&mockStruct{
					onbDao:      onbDao,
					userClient:  userClient,
					kycClient:   kycClient,
					timeClient:  timeClient,
					dedupeProc:  dedupeProc,
					eventLogger: mockEventLogger,
					userProc:    userProc,
					vkycClient:  mockVKYCClient,
					bcClient:    mockBcClient,
					panClient:   mockPanClient,
					kycProc:     mockKycProc,
					vgPanClient: mockVgPanClient,
				}, tt.args)
			}
			s := NewPreCustomerCreationCheckStage(onbDao, userClient, kycClient, timeClient, dedupeProc, mockEventLogger, userProc, ts.GenConf, mockVKYCClient, mockBcClient, ts.GenConf.Onboarding(), mockPanClient, mockKycProc, mockVgPanClient)
			ts.Conf.Onboarding.Flags.AllowManualReviewUsers = true
			ts.Conf.Onboarding.Flags.EnablePanAadharCheckInPreCustomerCreationCheckStage = true
			err := ts.GenConf.Onboarding().Set(ts.Conf.Onboarding, false, nil)
			if err != nil {
				return
			}
			got, err := s.StageProcessor(context.Background(), tt.args.req)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Unexpected error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			if diff := cmp.Diff(got.GetNextAction(), tt.want.GetNextAction(), protocmp.Transform()); diff != "" {
				t.Errorf("StageProcessor value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}

func TestPreCustomerCreationCheckStage_checkHashedAadharValues(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		onb *onbPb.OnboardingDetails
	}
	tests := []struct {
		name     string
		args     args
		mockFunc func(mocks *Clients)
		want     commontypes.BooleanEnum
		wantErr  error
	}{
		{
			name: "success with no values from kyc",
			args: args{
				ctx: context.Background(),
				onb: &onbPb.OnboardingDetails{
					ActorId: "actor-id",
					UserId:  "user-id",
					PanAadharLinkageDetails: &onbPb.PanAadharLinkageDetails{
						EkycNameDobValidationAadharDigitsHash:      obfuscator.Hashed("1234"),
						EkycNameDobValidationAadharLast2DigitsHash: obfuscator.Hashed("34"),
					},
				},
			},
			mockFunc: func(mocks *Clients) {
				mocks.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: "user-id",
					},
				}).Return(&userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							PAN: "pan",
						},
					},
					Status: rpc.StatusOk(),
				}, nil)
				mocks.vgPanClient.EXPECT().PANAadhaarValidation(gomock.Any(), &panPb.PANAadhaarValidationRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Pan: "pan",
				}).Return(&panPb.PANAadhaarValidationResponse{
					Status: rpc.StatusOk(),
					PanAadhaarValidationResult: &panPb.PANAadhaarValidationResult{
						AadhaarLast4Digits: "1234",
						MaskedAadhaar:      "XXXXXXXX1234",
					},
				}, nil)
				mocks.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, gomock.Any()).Return(nil)
				mocks.kycProc.EXPECT().CheckKYCStatus(gomock.Any(), "actor-id").Return(&kyc.CheckKYCStatusResponse{}, nil)
			},
			want:    commontypes.BooleanEnum_FALSE,
			wantErr: nil,
		},
		{
			name: "success with values from kyc",
			args: args{
				ctx: context.Background(),
				onb: &onbPb.OnboardingDetails{
					ActorId: "actor-id",
					UserId:  "user-id",
					PanAadharLinkageDetails: &onbPb.PanAadharLinkageDetails{
						EkycNameDobValidationAadharDigitsHash:      obfuscator.Hashed("1234"),
						EkycNameDobValidationAadharLast2DigitsHash: obfuscator.Hashed("34"),
					},
				},
			},
			mockFunc: func(mocks *Clients) {
				mocks.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: "user-id",
					},
				}).Return(&userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							PAN: "pan",
						},
					},
					Status: rpc.StatusOk(),
				}, nil)
				mocks.vgPanClient.EXPECT().PANAadhaarValidation(gomock.Any(), &panPb.PANAadhaarValidationRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Pan: "pan",
				}).Return(&panPb.PANAadhaarValidationResponse{
					Status: rpc.StatusOk(),
					PanAadhaarValidationResult: &panPb.PANAadhaarValidationResult{
						AadhaarLast4Digits: "5678",
						MaskedAadhaar:      "XXXXXXXX5678",
					},
				}, nil)
				mocks.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, gomock.Any()).Return(nil).Times(2)
				mocks.kycProc.EXPECT().CheckKYCStatus(gomock.Any(), "actor-id").Return(&kyc.CheckKYCStatusResponse{
					RequestParams: &kyc.KYCAttemptRequestParams{
						EkycNameDobValidationAadhaarDigitsHash: obfuscator.Hashed("5678"),
					},
				}, nil)
			},
			want:    commontypes.BooleanEnum_FALSE,
			wantErr: nil,
		},
		{
			name: "empty EkycNameDobValidationAadharDigitsHash",
			args: args{
				ctx: context.Background(),
				onb: &onbPb.OnboardingDetails{
					UserId:  "user-id",
					ActorId: "actor-id",
					PanAadharLinkageDetails: &onbPb.PanAadharLinkageDetails{
						EkycNameDobValidationAadharDigitsHash: "",
					},
				},
			},
			want:    commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr: nil,
			mockFunc: func(mocks *Clients) {
				mocks.kycProc.EXPECT().CheckKYCStatus(gomock.Any(), "actor-id").Return(&kyc.CheckKYCStatusResponse{}, nil)
			},
		},
		{
			name: "invalid aadhar digits from vendor",
			args: args{
				ctx: context.Background(),
				onb: &onbPb.OnboardingDetails{
					UserId:  "user-id",
					ActorId: "actor-id",
					PanAadharLinkageDetails: &onbPb.PanAadharLinkageDetails{
						EkycNameDobValidationAadharDigitsHash: obfuscator.Hashed("1234"),
					},
				},
			},
			mockFunc: func(mocks *Clients) {
				mocks.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: "user-id",
					},
				}).Return(&userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							PAN: "pan",
						},
					},
					Status: rpc.StatusOk(),
				}, nil)
				mocks.vgPanClient.EXPECT().PANAadhaarValidation(gomock.Any(), &panPb.PANAadhaarValidationRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Pan: "pan",
				}).Return(&panPb.PANAadhaarValidationResponse{
					Status: rpc.StatusOk(),
					PanAadhaarValidationResult: &panPb.PANAadhaarValidationResult{
						AadhaarLast4Digits: "AA234",
					},
				}, nil)
				mocks.kycProc.EXPECT().CheckKYCStatus(gomock.Any(), "actor-id").Return(&kyc.CheckKYCStatusResponse{}, nil)
			},
			want:    commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED,
			wantErr: errors.New("aadhar last 4 digits are not numeric or not of length 4"),
		},
		{
			name: "aadhaar digits have different hash",
			args: args{
				ctx: context.Background(),
				onb: &onbPb.OnboardingDetails{
					UserId:  "user-id",
					ActorId: "actor-id",
					PanAadharLinkageDetails: &onbPb.PanAadharLinkageDetails{
						EkycNameDobValidationAadharDigitsHash:      obfuscator.Hashed("1234"),
						EkycNameDobValidationAadharLast2DigitsHash: obfuscator.Hashed("34"),
					},
				},
			},
			mockFunc: func(mocks *Clients) {
				mocks.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: "user-id",
					},
				}).Return(&userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							PAN: "pan",
						},
					},
					Status: rpc.StatusOk(),
				}, nil)
				mocks.vgPanClient.EXPECT().PANAadhaarValidation(gomock.Any(), &panPb.PANAadhaarValidationRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Pan: "pan",
				}).Return(&panPb.PANAadhaarValidationResponse{
					Status: rpc.StatusOk(),
					PanAadhaarValidationResult: &panPb.PANAadhaarValidationResult{
						AadhaarLast4Digits: "1212",
						MaskedAadhaar:      "XXXXXXXXX1212",
					},
				}, nil)
				mocks.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, gomock.Any()).Return(nil)
				mocks.kycProc.EXPECT().CheckKYCStatus(gomock.Any(), "actor-id").Return(&kyc.CheckKYCStatusResponse{}, nil)
			},
			want:    commontypes.BooleanEnum_TRUE,
			wantErr: nil,
		},
		{
			name: "vendor return first 2 and last 2 hashed values",
			args: args{
				ctx: context.Background(),
				onb: &onbPb.OnboardingDetails{
					UserId:  "user-id",
					ActorId: "actor-id",
					PanAadharLinkageDetails: &onbPb.PanAadharLinkageDetails{
						EkycNameDobValidationAadharDigitsHash:      obfuscator.Hashed("1234"),
						EkycNameDobValidationAadharLast2DigitsHash: obfuscator.Hashed("34"),
					},
				},
			},
			mockFunc: func(mocks *Clients) {
				mocks.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{
						Id: "user-id",
					},
				}).Return(&userPb.GetUserResponse{
					User: &userPb.User{
						Profile: &userPb.Profile{
							PAN: "pan",
						},
					},
					Status: rpc.StatusOk(),
				}, nil)
				mocks.vgPanClient.EXPECT().PANAadhaarValidation(gomock.Any(), &panPb.PANAadhaarValidationRequest{
					Header: &commonvgpb.RequestHeader{
						Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					},
					Pan: "pan",
				}).Return(&panPb.PANAadhaarValidationResponse{
					Status: rpc.StatusOk(),
					PanAadhaarValidationResult: &panPb.PANAadhaarValidationResult{
						AadhaarLast4Digits: "1234",
						MaskedAadhaar:      "44XXXXXXXXX34",
					},
				}, nil)
				mocks.OnbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, gomock.Any()).Return(nil)
				mocks.kycProc.EXPECT().CheckKYCStatus(gomock.Any(), "actor-id").Return(&kyc.CheckKYCStatusResponse{}, nil)
			},
			want:    commontypes.BooleanEnum_FALSE,
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			mockClients := setupServiceWithMocks(t)
			if tt.mockFunc != nil {
				tt.mockFunc(mockClients)
			}

			c := NewPreCustomerCreationCheckStage(mockClients.OnbDao, mockClients.userClient, mockClients.KycClient, mockClients.timeClient, mockClients.dedupeProc, mockClients.eventLogger, mockClients.userProc, ts.GenConf, mockClients.beVKYCClient, mockClients.bankCustClient, ts.GenConf.Onboarding(), mockClients.panClient, mockClients.kycProc, mockClients.vgPanClient)

			got, _, err := c.checkHashedAadharValues(tt.args.ctx, tt.args.onb)
			if err != nil || tt.wantErr != nil {
				if err.Error() != tt.wantErr.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
					return
				}
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("checkHashedAadharValues value is mismatch got = %v, \n want = %v", got, tt.want)
			}
		})
	}
}
