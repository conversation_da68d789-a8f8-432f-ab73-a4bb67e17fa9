package stageproc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/kyc"
	beVkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	savingsClientPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	userGroup "github.com/epifi/gamma/api/user/group"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	release "github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/user/config/genconf"
	userEvents "github.com/epifi/gamma/user/events"
	"github.com/epifi/gamma/user/onboarding/dao"
)

type VKYCOnboardingStage struct {
	onboardingDao           dao.OnboardingDao
	vkycConfig              *genconf.VKYC
	vkycClient              beVkycPb.VKYCClient
	onbConf                 *genconf.OnboardingConfig
	bcClient                bankcust.BankCustomerServiceClient
	eventLogger             userEvents.EventLogger
	vkycPriorityABEvaluator *release.ABEvaluator[string]
	savingsClient           savingsClientPb.SavingsClient
}

func NewVKYCOnboardingStage(vkycConfig *genconf.VKYC, onboardingDao dao.OnboardingDao, vkycClient beVkycPb.VKYCClient,
	onbConf *genconf.OnboardingConfig, bcClient bankcust.BankCustomerServiceClient,
	eventLogger userEvents.EventLogger, actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient userGroup.GroupClient,
	savingsClient savingsClientPb.SavingsClient) *VKYCOnboardingStage {
	genericStringExprFn := func(str string) string { return str }
	vkycPriorityABEvaluator := getABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, onbConf.ABFeatureReleaseConfig(), genericStringExprFn)
	return &VKYCOnboardingStage{
		onboardingDao:           onboardingDao,
		vkycConfig:              vkycConfig,
		vkycClient:              vkycClient,
		onbConf:                 onbConf,
		bcClient:                bcClient,
		eventLogger:             eventLogger,
		vkycPriorityABEvaluator: vkycPriorityABEvaluator,
		savingsClient:           savingsClient,
	}
}

var (
	vkycNotEnabledInApprovalBlockingFlow = fmt.Errorf("platform/version in vkyc approval blocking flow")
	updateAppError                       = fmt.Errorf("update app to continue with vkyc")
)

// vkycOptionToStageMap maps vkyc option to stage option applicable
var vkycOptionToStageMap = map[onbPb.VKYCOption]onbPb.OnboardingStage{
	onbPb.VKYCOption_VKYC_OPTION_CKYC_O:                   onbPb.OnboardingStage_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_LSO:                      onbPb.OnboardingStage_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_ONBOARDING:               onbPb.OnboardingStage_OPTIONAL_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_STUDENT:                  onbPb.OnboardingStage_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_PARTIAL_KYC_DEDUPE:       onbPb.OnboardingStage_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_EKYC_NUMBER_MISMATCH:     onbPb.OnboardingStage_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_LOW_QUALITY_USERS:        onbPb.OnboardingStage_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_FI_LITE_USERS:            onbPb.OnboardingStage_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_FI_LITE_CC_USERS:         onbPb.OnboardingStage_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_CLOSED_ACCOUNT_REOPENING: onbPb.OnboardingStage_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_NON_RESIDENT_ONBOARDING:  onbPb.OnboardingStage_VKYC,
	onbPb.VKYCOption_VKYC_OPTION_FEDERAL_LOANS:            onbPb.OnboardingStage_VKYC,
}

// NOTE - this does NOT consider schedule call flow enabled

func (v *VKYCOnboardingStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	isVKYCPrioritised := v.IsVkycPrioritisedOverAddFunds(ctx, req.GetOnb().GetActorId())
	if !getStageStatus(req.GetOnb(), onbPb.OnboardingStage_ADD_MONEY).IsSuccessOrSkipped() && !isVKYCPrioritised && req.GetOnb().GetCurrentOnboardingStage() == onbPb.OnboardingStage_OPTIONAL_VKYC {
		return nil, NoActionSkipStatusUpdateError
	} else if getStageStatus(req.GetOnb(), onbPb.OnboardingStage_ADD_MONEY).IsSuccessOrSkipped() && isVKYCPrioritised && req.GetOnb().GetCurrentOnboardingStage() == onbPb.OnboardingStage_OPTIONAL_VKYC {
		return nil, SkipStageError
	}
	var (
		response = &StageProcessorResponse{}
		onb      = req.GetOnb()
		actorId  = onb.GetActorId()
	)
	getBCResp, errBC := v.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: onb.GetActorId(),
		},
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
	})
	if grpcErr := epifigrpc.RPCError(getBCResp, errBC); grpcErr != nil {
		logger.Error(ctx, "failed to get bank customer", zap.Error(grpcErr))
		return nil, grpcErr
	}
	vkycConfOption, err := v.getVKYCConfOption(ctx, onb, getBCResp.GetBankCustomer().GetKycLevelUpdateFlow())
	if err != nil {
		if errors.Is(err, updateAppError) {
			return &StageProcessorResponse{
				NextAction: deeplink.NewErrorFullScreen(ctx, error2.EKYCCertUpgradeError),
			}, nil
		}
		logger.Error(ctx, "error getting vkyc conf option", zap.Error(err))
		return nil, err
	}

	// check if vkyc option is applicable for current stage
	if vkycOptionToStageMap[onb.GetStageMetadata().GetVkycMetadata().GetVkycOption()] != onb.GetCurrentOnboardingStage() {
		return response, SkipStageError
	}

	vkycInfo, err := v.vkycClient.GetVKYCInfo(ctx, &beVkycPb.GetVKYCInfoRequest{
		ActorId: actorId,
	})

	if te := epifigrpc.RPCError(vkycInfo, err); te != nil {
		logger.Error(ctx, "unable to fetch vkyc status", zap.Error(err))
		return nil, err
	}

	isFullKycUser := getBCResp.GetBankCustomer().GetKycInfo().GetKycLevel() == kyc.KYCLevel_FULL_KYC

	if vkycInfo.GetVkycRecord().GetVkycSummary().GetStatus() == beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED {
		return v.handleVKYCApproved(ctx, vkycConfOption, onb, isFullKycUser)
	}

	// check whether user is full kyc and as per option do we have to ignore full kyc user
	if isFullKycUser && vkycConfOption.IgnoreFullKYCUser() {
		logger.Info(ctx, fmt.Sprintf("vkyc ignored for full kyc user: %s", onb.GetStageMetadata().GetVkycMetadata().GetVkycOption().String()))
		if vkycInfo.GetVkycRecord().GetVkycSummary().GetStatus() == beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW {
			reRegisterInfo, reRegisterErr := v.vkycClient.ReRegisterVkycInfo(ctx, &beVkycPb.ReRegisterVkycInfoRequest{
				Identifier: &beVkycPb.ReRegisterVkycInfoRequest_ActorId{
					ActorId: actorId,
				},
				CallInfoSubStatus: beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_ALREADY_FULL_KYC_USER,
				AttemptSubStatus:  beVkycPb.VKYCAttemptSubStatus_VKYC_ATTEMPT_SUB_STATUS_CALL_FAILED_USER_ALREADY_FULL_KYC,
			})
			if te := epifigrpc.RPCError(reRegisterInfo, reRegisterErr); te != nil {
				logger.Error(ctx, "Error in failing vkyc attempt", zap.Error(te))
				return nil, te
			}
		}
		return response, SkipStageError
	}

	if !v.isVkycEnabledForPlatformAppVersion(ctx, vkycConfOption) {
		// case when vkyc is not enabled but the vkyc option has vkyc approval as a blocking step, this can happen if feature is release in phased out manner like 5%, 10% .. etc or orchestrator run via non client facing rpc thus no platform and app version code available
		if vkycConfOption.IsVKYCApprovalBlocking() {
			logger.Error(ctx, "cannot skip vkyc due to blocking step", zap.Error(vkycNotEnabledInApprovalBlockingFlow))
			// show O type of failure to user
			response.NextAction = NewCKYCFlagErrorDL(ctx, kyc.FailureType_CKYC_O_FLAG)
			return response, nil
		}
		// defensive check before skipping the stage(assuming vkyc is blocking for O type user)
		if isUserCkycWithOFlag(onb) {
			logger.Info(ctx, "cannot skip vkyc for CKYC type O user")
			response.NextAction = NewCKYCFlagErrorDL(ctx, kyc.FailureType_CKYC_O_FLAG)
			return response, nil
		}
		logger.Info(ctx, "platform and version not enabled for VKYC", zap.String("vkycOption", onb.GetStageMetadata().GetVkycMetadata().GetVkycOption().String()))
		return response, SkipStageError
	}
	// skip based on probability and user not CKYC type O and PerformVkycCheck = false indicates VKYC wasn't enabled for user even once
	if !onb.GetStageMetadata().GetVkycMetadata().GetPerformVkycCheck() && !v.enableVkycBasedOnProbabilityPercentage(ctx, vkycConfOption.VkycEnablePercentage()) {
		// case when vkyc is not enabled but the vkyc option has vkyc approval as a blocking step, this can happen if feature is release in phased out manner like 5%, 10% .. etc or orchestrator run via non client facing rpc thus no platform and app version code available where we don't want the stage to be skipped
		if vkycConfOption.IsVKYCApprovalBlocking() {
			logger.Error(ctx, "skipVKYCBasedOnProbability but cannot skip vkyc due to blocking step", zap.Error(vkycNotEnabledInApprovalBlockingFlow))
			// show O type of failure to user
			response.NextAction = NewCKYCFlagErrorDL(ctx, kyc.FailureType_CKYC_O_FLAG)
			return response, nil
		}
		// defensive check before skipping the stage (assuming vkyc is blocking for O type user)
		if isUserCkycWithOFlag(onb) {
			logger.Info(ctx, "cannot skip vkyc for CKYC type O user")
			response.NextAction = NewCKYCFlagErrorDL(ctx, kyc.FailureType_CKYC_O_FLAG)
			return response, nil
		}
		logger.Info(ctx, "skipping vkyc due to probability", zap.String(logger.ACTOR_ID_V2, actorId))
		return response, SkipStageError
	}
	if !onb.GetStageMetadata().GetVkycMetadata().GetPerformVkycCheck() {
		// set PerformVkycCheck
		onb.GetStageMetadata().GetVkycMetadata().PerformVkycCheck = true
		if err = v.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, onb); err != nil {
			return nil, fmt.Errorf("error updating stage metadata")
		}
		// set stage to INPROGRESS since user subjected to VKYC now
		// User can still mark this skipped if option given in deeplink
		_, err = v.onboardingDao.UpdateStatus(ctx, onb.GetOnboardingId(), onb.GetCurrentOnboardingStage(), onbPb.OnboardingState_INPROGRESS)
		if err != nil {
			return response, err
		}
		logger.Info(ctx, fmt.Sprintf("started vkyc in onboarding for %v", onb.GetStageMetadata().GetVkycMetadata().GetVkycOption().String()), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	// return deeplink based on summary status
	return v.getVKYCDeeplinkFromStatus(ctx, vkycConfOption, onb, vkycInfo, isFullKycUser)
}

func (v *VKYCOnboardingStage) enableVkycBasedOnProbabilityPercentage(ctx context.Context, vkycEnablePercentage int) bool {
	// #nosec
	rand.Seed(time.Now().UnixNano()) // #nosec
	// get one random number between 1 -100
	// #nosec
	randP := uint32(rand.Intn(99)) + 1
	if int(randP) <= vkycEnablePercentage { // #nosec
		logger.Info(ctx, "enabling vkyc through onboarding based on vkyc enable percentage")
		return true
	}
	logger.Info(ctx, "disabling vkyc through onboarding based on vkyc enable percentage")
	return false
}

// isVkycEnabledForPlatformAppVersion returns true if vkyc is enabled for platform and app version combination
func (v *VKYCOnboardingStage) isVkycEnabledForPlatformAppVersion(ctx context.Context, vkycOption *genconf.VKYCOption) bool {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	logger.Info(ctx, "check if vkyc enabled", logger.ZapPlatformAndAppVersion(platform, version)...)
	return (platform == commontypes.Platform_ANDROID && version >= vkycOption.MinAndroidVersion()) ||
		(platform == commontypes.Platform_IOS && version >= vkycOption.MinIOSVersion())
}

// nolint:funlen
func (v *VKYCOnboardingStage) getVKYCConfOption(ctx context.Context, onb *onbPb.OnboardingDetails, kycLevelUpdateFlow bankcust.KycLevelUpdateFlow) (*genconf.VKYCOption, error) {
	var (
		platform, version = epificontext.AppPlatformAndVersion(ctx)
	)
	// vkycConfOption nil denotes user visiting stage first time
	if onb.GetStageMetadata().GetVkycMetadata() == nil {
		onb.GetStageMetadata().VkycMetadata = &onbPb.VKYCMetadata{}
	}
	switch kycLevelUpdateFlow {
	case bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_O:
		onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_CKYC_O
	case bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC:
		onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_PARTIAL_KYC_DEDUPE
		if !v.isVkycEnabledForPlatformAppVersion(ctx, v.vkycConfig.Option().Get(onbPb.VKYCOption_VKYC_OPTION_PARTIAL_KYC_DEDUPE.String())) {
			logger.Info(ctx, "vkyc not enabled for this version/platform for partial etb", logger.ZapPlatformAndAppVersion(platform, version)...)
			return nil, updateAppError
		}
	case bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_EKYC_NUMBER_MISMATCH:
		onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_EKYC_NUMBER_MISMATCH
		if !v.isVkycEnabledForPlatformAppVersion(ctx, v.vkycConfig.Option().Get(onbPb.VKYCOption_VKYC_OPTION_EKYC_NUMBER_MISMATCH.String())) {
			logger.Info(ctx, "vkyc not enabled for this version/platform for ekyc number mismatch", logger.ZapPlatformAndAppVersion(platform, version)...)
			return nil, updateAppError
		}
	case bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_STUDENT:
		onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_STUDENT
		if !v.isVkycEnabledForPlatformAppVersion(ctx, v.vkycConfig.Option().Get(onbPb.VKYCOption_VKYC_OPTION_STUDENT.String())) {
			onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_ONBOARDING
		}
	case bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_LOW_QUALITY_USERS:
		onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_LOW_QUALITY_USERS
		// If VKYC is not enabled for this group, we can consider them VKYC Option onboarding
		if !v.isVkycEnabledForPlatformAppVersion(ctx, v.vkycConfig.Option().Get(onbPb.VKYCOption_VKYC_OPTION_LOW_QUALITY_USERS.String())) {
			onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_ONBOARDING
		}
	case bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_FI_LITE_USERS:
		if onb.GetFeature() == onbPb.Feature_FEATURE_CC {
			onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_FI_LITE_CC_USERS
		} else {
			onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_FI_LITE_USERS
			if !v.isVkycEnabledForPlatformAppVersion(ctx, v.vkycConfig.Option().Get(onbPb.VKYCOption_VKYC_OPTION_FI_LITE_USERS.String())) {
				onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_ONBOARDING
			}
		}
	case bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_CLOSED_ACCOUNT_REOPENING:
		onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_CLOSED_ACCOUNT_REOPENING
		if !v.isVkycEnabledForPlatformAppVersion(ctx, v.vkycConfig.Option().Get(onbPb.VKYCOption_VKYC_OPTION_CLOSED_ACCOUNT_REOPENING.String())) {
			onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_CLOSED_ACCOUNT_REOPENING
		}
	// todo: remove if this is not required as NR onboarding has a new CIF creation flow
	case bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_NON_RESIDENT_ONBOARDING:
		onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_NON_RESIDENT_ONBOARDING
		if !v.isVkycEnabledForPlatformAppVersion(ctx, v.vkycConfig.Option().Get(onbPb.VKYCOption_VKYC_OPTION_NON_RESIDENT_ONBOARDING.String())) {
			onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_NON_RESIDENT_ONBOARDING
		}
	case bankcust.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_FEDERAL_LOANS:
		onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_FEDERAL_LOANS
	default:
		switch {
		case isUserCkycWithOFlag(onb):
			onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_CKYC_O
		default:
			onb.GetStageMetadata().GetVkycMetadata().VkycOption = onbPb.VKYCOption_VKYC_OPTION_ONBOARDING
		}
	}
	// update onboarding stage vkyc metadata
	if err := v.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_STAGE_METADATA}, onb); err != nil {
		return nil, err
	}
	logger.Info(ctx, fmt.Sprintf("vkyc option for user: %s", onb.GetStageMetadata().GetVkycMetadata().GetVkycOption().String()), logger.ZapPlatformAndAppVersion(platform, version)...)
	return v.vkycConfig.Option().Get(onb.GetStageMetadata().GetVkycMetadata().GetVkycOption().String()), nil
}

func (v *VKYCOnboardingStage) IsVkycPrioritisedOverAddFunds(ctx context.Context, actorId string) bool {
	commonConstraintsData := release.NewCommonConstraintData(types.Feature_PRIORITIES_VKYC_OVER_ADD_FUNDS).WithActorId(actorId)
	isPriorities, variant, err := v.vkycPriorityABEvaluator.Evaluate(ctx, commonConstraintsData)
	if err != nil {
		logger.WarnWithCtx(ctx, "error evaluating AB variant for affluent user bonus transition screen, returning", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return isPriorities && variant == "ONE"
}
