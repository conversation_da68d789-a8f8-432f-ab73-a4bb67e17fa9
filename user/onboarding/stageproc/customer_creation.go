package stageproc

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	dlPb "github.com/epifi/gamma/api/frontend/deeplink"

	"github.com/epifi/gamma/api/bankcust"
	panPb "github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	vgPanPb "github.com/epifi/gamma/api/vendorgateway/pan"
	"github.com/epifi/gamma/pkg/obfuscator"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/helper"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

type CustomerCreationStage struct {
	onboardingDao  dao.OnboardingDao
	conf           *genconf.OnboardingConfig
	usersClient    user.UsersClient
	bankCustClient bankcust.BankCustomerServiceClient
	panClient      panPb.PanClient
	vgPanClient    vgPanPb.PANClient
	kycProc        helper.IKYCHelper
}

func NewCustomerCreationStage(conf *genconf.OnboardingConfig, onboardingDao dao.OnboardingDao, usersClient user.UsersClient,
	bcClient bankcust.BankCustomerServiceClient, panClient panPb.PanClient, vgPanClient vgPanPb.PANClient, kycProc helper.IKYCHelper) *CustomerCreationStage {
	return &CustomerCreationStage{
		conf:           conf,
		onboardingDao:  onboardingDao,
		usersClient:    usersClient,
		bankCustClient: bcClient,
		panClient:      panClient,
		vgPanClient:    vgPanClient,
		kycProc:        kycProc,
	}
}

var (
	customerStatusToOnboardingStatusMapping = map[bankcust.Status]onbPb.OnboardingState{
		bankcust.Status_STATUS_UNSPECIFIED:       onbPb.OnboardingState_UNSPECIFIED,
		bankcust.Status_STATUS_IN_PROGRESS:       onbPb.OnboardingState_INPROGRESS,
		bankcust.Status_STATUS_ACTIVE:            onbPb.OnboardingState_SUCCESS,
		bankcust.Status_STATUS_FAILED:            onbPb.OnboardingState_MANUAL_INTERVENTION,
		bankcust.Status_CUSTOMER_STATUS_INACTIVE: onbPb.OnboardingState_MANUAL_INTERVENTION,
	}
	panAadhaarNotLinkedError = fmt.Errorf("pan and aadhaar are not linked")

	customerCreationSourceMap = map[onbPb.Feature]bankcust.Source{
		onbPb.Feature_FEATURE_SA:                    bankcust.Source_SOURCE_SAVINGS_ACCOUNT_ONBOARDING,
		onbPb.Feature_FEATURE_CC:                    bankcust.Source_SOURCE_CREDIT_CARD_ONBOARDING,
		onbPb.Feature_FEATURE_NON_RESIDENT_SA:       bankcust.Source_SOURCE_NON_RESIDENT_SAVINGS_ACCOUNT_ONBOARDING,
		onbPb.Feature_FEATURE_NON_RESIDENT_SA_QATAR: bankcust.Source_SOURCE_NON_RESIDENT_SAVINGS_ACCOUNT_ONBOARDING_QATAR,
		onbPb.Feature_FEATURE_PL:                    bankcust.Source_SOURCE_PERSONAL_LOAN_ONBOARDING,
	}
)

func (c *CustomerCreationStage) StageProcessor(ctx context.Context, request *StageProcessorRequest) (*StageProcessorResponse, error) {
	onb := request.GetOnb()

	if !onb.GetFeature().IsNonResidentUserOnboarding() && !c.conf.Flags().EnablePanAadharCheckInPreCustomerCreationCheckStage() {
		dl, err := c.validatePanAadhaar(ctx, onb)
		if err != nil {
			logger.Error(ctx, "error in validating pan aadhaar", zap.Error(err))
			return nil, err
		}
		if dl != nil {
			return &StageProcessorResponse{
				NextAction: dl,
			}, nil
		}
	}

	// First we check the status of customer creation. If we are allowed to trigger customer creation,
	// we call InitBankCustomer and then check the status and update the stage status accordingly.
	allow, err := c.allowInitBankCustomer(ctx, onb.GetActorId(), onb.GetVendor())
	if err != nil {
		return nil, err
	}
	if allow {
		if err = c.initCustomerCreation(ctx, onb); err != nil {
			logger.Error(ctx, "error initiating customer creation", zap.Error(err))
			return nil, err
		}
	}
	if onb, err = c.processCustomerCreation(ctx, onb); err != nil {
		if IsOrchestratorAction(err) {
			return nil, err
		}
		logger.Error(ctx, "error in processing customer creation", zap.Error(err))
	}
	if !getStageStatus(onb, onbPb.OnboardingStage_CUSTOMER_CREATION).IsSuccessOrSkipped() || err != nil {
		if IsUserStuck(ctx, onb, c.conf.AccountSetupMaxStuckDuration()) {
			return &StageProcessorResponse{
				NextAction: ActionForUserStuckOnAcctCreation(ctx, onb),
			}, err
		}
		return &StageProcessorResponse{
			NextAction: deeplink.NewActionToAccountCreationProgress(),
		}, err
	}
	return nil, NoActionError
}

func (c *CustomerCreationStage) allowInitBankCustomer(ctx context.Context, actorId string, vendor commonvgpb.Vendor) (bool, error) {
	bcResp, errResp := c.bankCustClient.CheckBankCustomerCreationStatus(ctx, &bankcust.CheckBankCustomerCreationStatusRequest{
		ActorId: actorId,
		Vendor:  vendor,
	})
	switch {
	case bcResp.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED) ||
		bcResp.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_RETRYABLE_FAILURE):
		logger.Info(ctx, "customer creation can be initiated")
		return true, nil
	case bcResp.GetStatus().GetCode() == uint32(bankcust.CheckBankCustomerCreationStatusResponse_NON_RETRYABLE_FAILURE) ||
		bcResp.GetStatus().IsSuccess() ||
		bcResp.GetStatus().IsInProgress():
		logger.Info(ctx, "initiating customer creation not allowed", zap.Uint32(logger.STATUS, bcResp.GetStatus().GetCode()))
		return false, nil
	default:
		if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
			logger.Error(ctx, "error in checking for bank customer status", zap.Error(err))
			return false, err
		}
		logger.Error(ctx, "unhandled customer creation status")
		return false, fmt.Errorf("unhandled customer creation state")
	}
}

func (c *CustomerCreationStage) initCustomerCreation(ctx context.Context, onb *onbPb.OnboardingDetails) error {
	logger.Info(ctx, "Initiating customer creation from onboarding service")
	source, ok := customerCreationSourceMap[onb.GetFeature()]
	if !ok {
		logger.Error(ctx, "not a valid feature for customer creation", zap.String(logger.STATE, onb.GetFeature().String()))
		source = bankcust.Source_SOURCE_SAVINGS_ACCOUNT_ONBOARDING
	}
	resp, errResp := c.bankCustClient.InitBankCustomer(ctx, &bankcust.InitBankCustomerRequest{
		ActorId: onb.GetActorId(),
		Vendor:  onb.GetVendor(),
		Source:  source,
	})
	switch {
	case resp.GetStatus().IsSuccess():
		return nil
	case resp.GetStatus().IsInProgress() ||
		resp.GetStatus().GetCode() == uint32(bankcust.InitBankCustomerResponse_CUSTOMER_ALREADY_CREATED) ||
		resp.GetStatus().IsInvalidArgument():
		// We do not return error in this case, as this could have happened due to a race condition
		logger.Error(ctx, "initiating customer creation not allowed")
		return nil
	default:
		if err := epifigrpc.RPCError(resp, errResp); err != nil {
			logger.Error(ctx, "error in init bank customer response", zap.Error(err))
			return err
		}
	}
	return nil
}

func (c *CustomerCreationStage) processCustomerCreation(ctx context.Context, onb *onbPb.OnboardingDetails) (*onbPb.OnboardingDetails, error) {
	// TODO(Shivam): Migrate to CheckBankCustomerCreationStatus
	bcRes, bcErr := c.bankCustClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: onb.GetVendor(),
		Identifier: &bankcust.GetBankCustomerRequest_UserId{
			UserId: onb.GetUserId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(bcRes, bcErr); rpcErr != nil {
		if !bcRes.GetStatus().IsRecordNotFound() {
			logger.Error(ctx, "error while fetching bank customer", zap.Error(rpcErr))
			return nil, rpcErr
		}
	}

	currentState := getStageStatus(onb, onbPb.OnboardingStage_CUSTOMER_CREATION)
	newState := customerStatusToOnboardingStatusMapping[bcRes.GetBankCustomer().GetStatus()]
	if newState == onbPb.OnboardingState_SUCCESS {
		return nil, NoActionError
	}
	if currentState != newState && newState != onbPb.OnboardingState_UNSPECIFIED {
		logger.Info(ctx, fmt.Sprintf("updating customer creation onboarding stage state from %v to %v", currentState.String(), newState.String()))
		return updateStatus(ctx, c.onboardingDao, onb.GetOnboardingId(), onbPb.OnboardingStage_CUSTOMER_CREATION, newState)
	}
	return onb, nil
}

func (c *CustomerCreationStage) validatePanAadhaar(ctx context.Context, onb *onbPb.OnboardingDetails) (*dlPb.Deeplink, error) {
	if err := c.checkPanAadhaarLinked(ctx, onb); err != nil {
		if errors.Is(err, panAadhaarNotLinkedError) {
			return deeplink.NewErrorFullScreen(ctx, error2.PanAadhaarNotLinked), nil
		}
		return nil, err
	}

	if c.conf.Flags().BlockCCUserForPANLinkage() {
		hashMismatch, checkInProgress, errHashCheck := c.checkHashedAadharValues(ctx, onb)
		if errHashCheck != nil {
			logger.Error(ctx, "failure while checking hashed aadhar values", zap.Error(errHashCheck))
			return nil, errHashCheck
		}

		if checkInProgress {
			return deeplink.NewActionToAccountCreationProgress(), nil
		}

		if hashMismatch == commontypes.BooleanEnum_TRUE {
			logger.Info(ctx, "aadhar last 4 digits hash mismatch found for user")
			return deeplink.NewErrorFullScreen(ctx, error2.OnbErrPANAadharMismatch), nil
		}
	}
	return nil, nil
}

func (c *CustomerCreationStage) checkPanAadhaarLinked(ctx context.Context, onb *onbPb.OnboardingDetails) error {
	if c.conf.BlockOnboardingDueToUnlinkedPANAndAadhaar() {
		panResp, panErr := c.panClient.GetPANAadharLinkStatus(ctx, &panPb.GetPANAadharLinkStatusRequest{
			ActorId: onb.GetActorId(),
		})
		if rpcErr := epifigrpc.RPCError(panResp, panErr); rpcErr != nil {
			logger.Error(ctx, "got unexpected response from be", zap.Error(rpcErr))
			return rpcErr
		}
		if panResp.GetPanAadharLinkStatus() != panPb.PANAadharLinkStatus_PAN_AADHAR_STATUS_LINKED {
			logger.Info(ctx, fmt.Sprintf("user's pan aadhaar is not linked %v", panResp.GetPanAadharLinkStatus()))
			return panAadhaarNotLinkedError
		}
		return nil
	}
	return nil
}

// nolint: funlen
func (c *CustomerCreationStage) checkHashedAadharValues(ctx context.Context, onb *onbPb.OnboardingDetails) (commontypes.BooleanEnum, bool, error) {
	panAadharLinkageDetails := onb.GetPanAadharLinkageDetails()
	onbDetailsNMDOBHash := panAadharLinkageDetails.GetEkycNameDobValidationAadharDigitsHash()
	ekycNMDOBHash := onbDetailsNMDOBHash

	kycStatusResp, _ := c.kycProc.CheckKYCStatus(ctx, onb.GetActorId())
	kycNMDOBHash := kycStatusResp.GetRequestParams().GetEkycNameDobValidationAadhaarDigitsHash()
	kycNMDOBLast2DigitsHash := kycStatusResp.GetRequestParams().GetEkycNameDobValidationAadhaarLast2DigitsHash()

	if len(kycNMDOBHash) != 0 {
		if !strings.EqualFold(kycNMDOBHash, onbDetailsNMDOBHash) && onb.GetPanAadharLinkageDetails() != nil { // Update the new values in onb details
			onb.PanAadharLinkageDetails.EkycNameDobValidationAadharDigitsHash = kycNMDOBHash
			onb.PanAadharLinkageDetails.IsAadharDigitsHashMismatch = commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED
			if errUpdate := c.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, onb); errUpdate != nil {
				logger.Error(ctx, "failed while update onboarding details", zap.Error(errUpdate))
				return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, errUpdate
			}
		}
		ekycNMDOBHash = kycNMDOBHash
	}

	if panAadharLinkageDetails == nil || ekycNMDOBHash == "" {
		// TODO (Ankit): Make it blocker
		logger.Info(ctx, "pan aadhar linkage is nil or ekyc hash is empty")
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, nil
	}

	if panAadharLinkageDetails.GetIsAadharDigitsHashMismatch() != commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED {
		return panAadharLinkageDetails.GetIsAadharDigitsHashMismatch(), false, nil
	}

	getUserResp, errGetUser := c.usersClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: onb.GetUserId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getUserResp, errGetUser); rpcErr != nil {
		logger.Error(ctx, "failed while getting user", zap.Error(rpcErr))
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, rpcErr
	}

	panAadharValidationResp, errPanAadharValidation := c.vgPanClient.PANAadhaarValidation(ctx, &vgPanPb.PANAadhaarValidationRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		Pan: getUserResp.GetUser().GetProfile().GetPAN(),
	})
	switch {
	case panAadharValidationResp.GetStatus().IsInProgress():
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, true, nil
	case panAadharValidationResp.GetStatus().GetCode() == uint32(vgPanPb.PANAadhaarValidationResponse_PAN_AADHAAR_NOT_LINKED):
		panAadharLinkageDetails.PanAadhaarLinked = commontypes.BooleanEnum_FALSE
		// keeping the error non-blocking as it will result in another API call but will not block the user
		_ = c.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, onb)
		return commontypes.BooleanEnum_TRUE, false, nil
	default:
		if rpcErr := epifigrpc.RPCError(panAadharValidationResp, errPanAadharValidation); rpcErr != nil {
			logger.Error(ctx, "failed while getting pan validation response", zap.Error(rpcErr))
			return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, rpcErr
		}
	}

	aadharLast4Digits := strings.TrimSpace(panAadharValidationResp.GetPanAadhaarValidationResult().GetAadhaarLast4Digits())
	if len(aadharLast4Digits) != 4 {
		logger.Info(ctx, "aadhar last 4 digits are not of length 4", zap.Any("LENGTH", len(aadharLast4Digits)))
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, errors.New("aadhar last 4 digits are not numeric or not of length 4")
	}
	maskedAadhaar := panAadharValidationResp.GetPanAadhaarValidationResult().GetMaskedAadhaar()
	aadhaarLast2Digit := strings.TrimSpace(maskedAadhaar[len(maskedAadhaar)-2:])

	panValidationHashedDigits := obfuscator.Hashed(aadharLast4Digits)
	panValidationLast2DigitsHash := obfuscator.Hashed(aadhaarLast2Digit)
	hashMismatch := commontypes.BooleanEnum_TRUE
	if strings.EqualFold(ekycNMDOBHash, panValidationHashedDigits) || strings.EqualFold(kycNMDOBLast2DigitsHash, panValidationLast2DigitsHash) {
		hashMismatch = commontypes.BooleanEnum_FALSE
	}
	panAadharLinkageDetails.PanLinkedAadharDigitsHash = panValidationHashedDigits
	panAadharLinkageDetails.PanLinkedAadhaarLast2DigitsHash = panValidationLast2DigitsHash
	panAadharLinkageDetails.IsAadharDigitsHashMismatch = hashMismatch
	logger.Info(ctx, fmt.Sprintf("logging the aadhar mismatch result %v", hashMismatch),
		zap.Any("PAN_VALIDATION_AADHAR_DIGITS_HASH", panValidationHashedDigits),
		zap.Any("EKYC_NAME_DOB_VALIDATION_AADHAR_DIGITS_HASH", panAadharLinkageDetails.GetPanLinkedAadharDigitsHash()),
		zap.Any("PAN_VALIDATION_AADHAR_LAST2_DIGITS_HASH", panAadharLinkageDetails.GetPanLinkedAadhaarLast2DigitsHash()))

	if errUpdate := c.onboardingDao.UpdateOnboardingDetailsByColumns(ctx, []onbPb.OnboardingDetailsFieldMask{onbPb.OnboardingDetailsFieldMask_PAN_AADHAR_LINKAGE_DETAILS}, onb); errUpdate != nil {
		logger.Error(ctx, "failed while update onboarding details", zap.Error(errUpdate))
		return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, false, errUpdate
	}

	return hashMismatch, false, nil
}
