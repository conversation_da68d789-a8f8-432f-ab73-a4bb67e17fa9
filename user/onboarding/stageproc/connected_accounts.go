package stageproc

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	scrnrPb "github.com/epifi/gamma/api/screener"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/onboarding/dao"
	"github.com/epifi/gamma/user/onboarding/pkg/screener"
)

// CAScreenerStage is the connected accounts screener check stage processor
type CAScreenerStage struct {
	screenerClient scrnrPb.ScreenerClient
	onboardingDao  dao.OnboardingDao
}

func NewCAScreenerStage(screenerClient scrnrPb.ScreenerClient, onboardingDao dao.OnboardingDao) *CAScreenerStage {
	return &CAScreenerStage{
		screenerClient: screenerClient,
		onboardingDao:  onboardingDao,
	}
}

func (s *CAScreenerStage) StageProcessor(ctx context.Context, req *StageProcessorRequest) (*StageProcessorResponse, error) {
	var (
		onb          = req.GetOnb()
		actorId      = req.GetOnb().GetActorId()
		currentCheck = scrnrPb.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS
		stage        = onbPb.OnboardingStage_CONNECTED_ACCOUNTS
	)

	platform, version := epificontext.AppPlatformAndVersion(ctx)
	logger.Info(ctx, fmt.Sprintf("app version and platform in ca stage proc: %v, %v", version, platform),
		zap.String(logger.ACTOR_ID_V2, actorId))
	if screener.IsScreenerCompletedOrSkipped(onb) {
		return nil, SkipStageError
	}

	getAttemptRes, errGet := s.screenerClient.GetScreenerAttemptsByActorId(ctx, &scrnrPb.GetScreenerAttemptsByActorIdRequest{
		ActorId: onb.GetActorId(),
	})
	if grpcErr := epifigrpc.RPCError(getAttemptRes, errGet); grpcErr != nil && !rpc.StatusFromError(grpcErr).IsRecordNotFound() {
		logger.Error(ctx, "failed to get screener attempts by actor id", zap.Error(grpcErr))
		return nil, grpcErr
	}

	// If no screener attempt is present, start the check to create an attempt
	if getAttemptRes.GetStatus().IsRecordNotFound() {
		return runScreenerV2Check(ctx, s.screenerClient, actorId, currentCheck)
	}

	scrAtt := getAttemptRes.GetScreenerAttempt()
	caCheckDtls, ok := lo.Find[*scrnrPb.CheckDetails](getAttemptRes.GetChecksMap(), func(check *scrnrPb.CheckDetails) bool {
		return check.GetCheckType() == currentCheck
	})
	if !ok {
		logger.Error(ctx, fmt.Sprintf("did not find current check in screener attempt : %v", currentCheck))
		return nil, fmt.Errorf("did not find current check in screener attempt")
	}

	if scrAtt.GetResultInfo().GetResult() == scrnrPb.ScreenerAttemptResult_SCREENER_ATTEMPT_RESULT_PASSED ||
		scrAtt.GetResultInfo().GetResult() == scrnrPb.ScreenerAttemptResult_SCREENER_ATTEMPT_RESULT_MANUALLY_PASSED {
		// 1. If it got passed due to some other check -> SKIPPED
		// 2. If got passed due to current check -> COMPLETED
		if caCheckDtls.GetCheckResult() == scrnrPb.CheckResult_CHECK_RESULT_PASSED {
			return nil, NoActionError
		}
		return nil, SkipStageError
	}

	if getStageStatus(onb, stage) == onbPb.OnboardingState_RESET {
		if _, err := s.onboardingDao.UpdateStatus(ctx, onb.GetOnboardingId(), onb.GetCurrentOnboardingStage(), onbPb.OnboardingState_INPROGRESS); err != nil {
			logger.Error(ctx, "failed to update the stage status", zap.Error(err))
			return nil, err
		}
		return runScreenerV2Check(ctx, s.screenerClient, actorId, currentCheck)
	}

	switch caCheckDtls.GetCheckResult() {
	case scrnrPb.CheckResult_CHECK_RESULT_PASSED:
		return nil, NoActionError
	case scrnrPb.CheckResult_CHECK_RESULT_SKIPPED,
		scrnrPb.CheckResult_CHECK_RESULT_FAILED,
		scrnrPb.CheckResult_CHECK_RESULT_DISABLED:
		return nil, SkipStageError
	case scrnrPb.CheckResult_CHECK_RESULT_UNSCPECIFIED, scrnrPb.CheckResult_CHECK_RESULT_IN_PROGRESS, scrnrPb.CheckResult_CHECK_RESULT_INITIATED:
		// Need to start a new check or check current check status
		return runScreenerV2Check(ctx, s.screenerClient, actorId, currentCheck)
	}

	logger.Error(ctx, "Flow shouldn't have gone here as all cases are handled above")
	return nil, fmt.Errorf("unexpected code flow")
}
