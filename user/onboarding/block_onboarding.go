package onboarding

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/user/config/genconf"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error2 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

func (s *Service) BlockOnboardingByStage(ctx context.Context, conf *genconf.OnboardingConfig, stage onbPb.OnboardingStage) (*deeplinkPb.Deeplink, error) {

	onbStage := stage.String()
	healthConf, present := conf.HealthConfig().Load(onbStage)
	if !present {
		return nil, nil
	}
	now := s.Time.Now().In(datetime.IST)
	from, err := datetime.ParseStringTimestampProtoInLocation(constant.StandardTimeLayout, healthConf.From(), datetime.IST)
	if err != nil {
		logger.Error(ctx, "error in parsing time", zap.String("timeString", healthConf.From()), zap.Error(err))
		return nil, err
	}

	to, err := datetime.ParseStringTimestampProtoInLocation(constant.StandardTimeLayout, healthConf.To(), datetime.IST)
	if err != nil {
		logger.Error(ctx, "error in parsing time", zap.String("timeString", healthConf.To()), zap.Error(err))
		return nil, err
	}

	if now.After(from.AsTime()) && now.Before(to.AsTime()) {
		logger.Info(ctx, fmt.Sprintf("blocking user from onboarding from %v till %v at stage %v",
			from.AsTime().In(datetime.IST), to.AsTime().In(datetime.IST), stage.String()))
		return deeplink.NewErrorFullScreen(ctx, &error2.ErrorScreenOpts{
			Title:       healthConf.Message(),
			HasFeedback: true,
		}), nil
	}

	return nil, nil
}

func (s *Service) shouldBlockNrOnboarding(ctx context.Context, onb *onbPb.OnboardingDetails) (bool, error) {
	if !onb.GetFeature().IsNonResidentUserOnboarding() || !s.dynConf.Flags().BlockNrOnboarding() {
		return false, nil
	}
	if isStageSuccessOrSkipped(onb, onbPb.OnboardingStage_CUSTOMER_CREATION) {
		return false, nil
	}
	isCCInProgress, errCC := s.userProcessor.IsCustomerCreationInProgress(ctx, onb.GetActorId())
	if errCC != nil {
		logger.Error(ctx, "failed while check for custom creation in progress", zap.Error(errCC))
		return false, errCC
	}
	if isCCInProgress {
		return false, nil
	}
	return true, nil
}
