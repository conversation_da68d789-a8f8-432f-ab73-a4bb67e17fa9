package deeplink

import (
	"context"

	"github.com/epifi/be-common/api/typesv2/common"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	walkthroughScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/walkthrough"

	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	onPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/frontend/cx"
	"github.com/epifi/gamma/user/onboarding/pkg/constant"
	error3 "github.com/epifi/gamma/user/onboarding/pkg/error"
)

var (
	UpdateAppDeeplink = &dlPb.Deeplink{
		Screen: dlPb.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &dlPb.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &dlPb.DetailedErrorViewScreenOptions{
				Title:    constant.UpdateAppTitle,
				Subtitle: constant.UpdateAppSubtitle,
				ImageUrl: constant.UpdateAppImageUrl,
				Ctas: []*dlPb.Cta{
					{
						Type: dlPb.Cta_CUSTOM,
						Text: "Update App",
						Deeplink: &dlPb.Deeplink{
							Screen: dlPb.Screen_UPDATE_APP_SCREEN,
						},
						Status: dlPb.Cta_CTA_STATUS_ENABLED,
					},
				},
			},
		},
	}
)

// NewErrorFullScreen uses DETAILED_ERROR_VIEW_SCREEN
func NewErrorFullScreen(ctx context.Context, opts *error3.ErrorScreenOpts) *dlPb.Deeplink {
	ctas, ctaHeader := getCTAInfoFromErrOpts(ctx, opts)

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &dlPb.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &dlPb.DetailedErrorViewScreenOptions{
				Title:            opts.Title,
				Subtitle:         opts.Subtitle,
				FailureReason:    opts.FailureReason,
				ImageUrl:         opts.HeaderImageURL,
				HasFeedback:      opts.HasFeedback,
				ScreenIdentifier: opts.Category.String(),
				CtaHeader:        ctaHeader,
				Ctas:             ctas,
			},
		},
	}
}

type ScreenOptionParams struct {
	webViewUrl string
}

func NewActionToGetNextAction() *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
	}
}

func NewActionToGetNextActionForFiLite() *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
		ScreenOptions: &dlPb.Deeplink_GetNextOnboardingActionScreenOptions{
			GetNextOnboardingActionScreenOptions: &dlPb.GetNextOnboardingActionScreenOptions{
				Feature: onPb.Feature_FEATURE_FI_LITE.String(),
			},
		},
	}
}

func NewActionToAccountCreationProgress() *dlPb.Deeplink {

	widget := func(text, iconUrl, bgColor, textColor string) *dlPb.IconTextWidget {
		return &dlPb.IconTextWidget{
			RightImg:            ui.GetImage(common.ImageType_PNG, iconUrl, 72, 46),
			Text:                ui.GetText(text, textColor, common.FontStyle_HEADLINE_S),
			RightImgTextPadding: 24,
			BgColor:             bgColor,
		}
	}

	bottomInfoCard := func(text, iconUrl, bgColor, textColor string) *typesUi.IconTextComponent {
		return &typesUi.IconTextComponent{
			Texts: []*commontypes.Text{
				ui.GetText(text, textColor, common.FontStyle_HEADLINE_S),
			},
			RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, 46, 72).WithImageType(commontypes.ImageType_PNG),
			ContainerProperties: &typesUi.IconTextComponent_ContainerProperties{
				BgColor:       bgColor,
				CornerRadius:  28,
				TopPadding:    22,
				LeftPadding:   20,
				BottomPadding: 22,
				RightPadding:  20,
			},
			RightImgTxtPadding: 24,
		}
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_SAVINGS_ACCOUNT_SETUP_PROGRESS,
		ScreenOptions: &dlPb.Deeplink_SavingsAccountSetupProgress{
			SavingsAccountSetupProgress: &dlPb.SavingsAccountSetupProgress{
				Step: dlPb.SavingsAccountSetupProgress_ACCOUNT_SETUP,
				BottomInfoWidget: []*dlPb.IconTextWidget{
					widget("Over 40Cr+ saved in Forex charges using Zero Forex Debit Card", "https://epifi-icons.pointz.in/onboarding/debit_card_backside_tilted.png", "#DCF3EE", "#004E2D"),
					widget("Over ₹1000Cr invested by Fi users in 0 Commission Mutual Funds", "https://epifi-icons.pointz.in/onboarding/golden_egg_bowl.png", "#FBF3E6", "#C0723D"),
					widget("Folks from Amazon, Google, Cognizant, TCS, Zomato & more have joined Fi's Salary Program", "https://epifi-icons.pointz.in/onboarding/golden_star.png", "#FFFCEB", "#98712F"),
					widget("3.5 million users trust Fi to save, pay, track & invest their money", "https://epifi-icons.pointz.in/onboarding/users.png", "#EAE8F1", "#2C2B6E"),
				},
			},
		},
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(
			&onboarding.AccountCreationProgressScreenOptions{
				BottomInfoCards: []*typesUi.IconTextComponent{
					bottomInfoCard("Over 40Cr+ saved in Forex charges using Zero Forex Debit Card", "https://epifi-icons.pointz.in/onboarding/debit_card_backside_tilted.png", "#DCF3EE", "#004E2D"),
					bottomInfoCard("Over ₹1000Cr invested by Fi users in 0 Commission Mutual Funds", "https://epifi-icons.pointz.in/onboarding/golden_egg_bowl.png", "#FBF3E6", "#C0723D"),
					bottomInfoCard("Folks from Amazon, Google, Cognizant, TCS, Zomato & more have joined Fi's Salary Program", "https://epifi-icons.pointz.in/onboarding/golden_star.png", "#FFFCEB", "#98712F"),
					bottomInfoCard("3.5 million users trust Fi to save, pay, track & invest their money", "https://epifi-icons.pointz.in/onboarding/users.png", "#EAE8F1", "#2C2B6E"),
				},
			}),
	}
}

func NewActionForLivenessMaxRetries() *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_LIVENESS_MANUAL_REVIEW,
		ScreenOptions: &dlPb.Deeplink_LivenessManualReviewOptions{
			LivenessManualReviewOptions: &dlPb.LivenessManualReviewOptions{
				Title:          constant.LivenessManualReviewTitle,
				Subtitle:       constant.LivenessManualReviewSubTitle,
				FailureReasons: []string{constant.LivenessManualReviewReason1, constant.LivenessManualReviewReason2, constant.LivenessManualReviewReason3, constant.LivenessManualReviewReason4},
				Footer:         constant.LivenessManualReviewFooter,
			},
		},
	}
}

func NewLivenessManualReviewDL() *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_INSTRUCTIONS_SCREEN,
		ScreenOptions: &dlPb.Deeplink_InstructionsScreenOptions{
			InstructionsScreenOptions: &dlPb.InstructionsScreenOptions{
				Ctas: []*dlPb.Cta{
					{
						Type: dlPb.Cta_CUSTOM,
						Text: "Get Help",
						Deeplink: &dlPb.Deeplink{
							Screen: dlPb.Screen_HELP_MAIN,
						},
						DisplayTheme: dlPb.Cta_TERTIARY,
						Status:       dlPb.Cta_CTA_STATUS_ENABLED,
					},
				},
				Title: "Your account is close to the finish line",
				Instructions: []*dlPb.Instruction{
					{
						Title:       "Your details are under review",
						Description: "It should take upto 4 hours. Once done, we'll resume your account creation",
						IconUrl:     "https://epifi-icons.pointz.in/onboarding/clock.png",
					},
				},
				ImageUrl: "https://epifi-icons.pointz.in/onboarding/man_with_flag.png",
			},
		},
	}
}

func getCTAInfoFromErrOpts(ctx context.Context, opts *error3.ErrorScreenOpts) ([]*dlPb.Cta, string) {
	var (
		ctas                  []*dlPb.Cta
		ctaHeader             string
		isTertiaryCTAReserved bool
	)

	// cx chat and call both are now handled via contact us flow, hence re-directing there
	if opts.HasChat || opts.HasCall {
		isTertiaryCTAReserved = true
		ctaHeader = constant.CTAHeaderCX
		ctas = append(ctas, cx.GetContactUsCta(ctx))
	}

	if !isTertiaryCTAReserved && opts.HasFeedback {
		ctaHeader = constant.CTAHeaderFeedback
		ctas = append(ctas, &dlPb.Cta{
			Text:         constant.CTAFeedback,
			Type:         dlPb.Cta_FEEDBACK,
			DisplayTheme: dlPb.Cta_TERTIARY,
		})
	}

	return ctas, ctaHeader
}

func NewVKYCNotificationAPIDeeplink(notificationType dlPb.VKYCNotificationScreenOptions_NotificationType) *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_VKYC_NOTIFICATION_API,
		ScreenOptions: &dlPb.Deeplink_VkycNotificationScreenOptions{
			VkycNotificationScreenOptions: &dlPb.VKYCNotificationScreenOptions{
				NotificationType: notificationType,
			},
		},
	}
}

func AadharNumberMismatchAccountDeletionScreen() *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_ACCOUNT_DELETION_ACKNOWLEDGEMENT,
		ScreenOptions: &dlPb.Deeplink_AccountDeletionAcknowledgementScreenOptions{
			AccountDeletionAcknowledgementScreenOptions: &dlPb.AccountDeletionAcknowledgementScreenOptions{
				ImageUrl: "https://epifi-icons.pointz.in/onboarding/caveman.png",
				Title:    "We cannot open an account for you",
				Subtitle: "The mobile number used for onboarding on Fi app does not match with the mobile number linked to aadhaar. " +
					"You can start your journey again by using the mobile number that is linked to your aadhaar",
				Cta: &dlPb.Cta{
					Text: "Retry with different mobile number",
				},
				DeletionReason: userPb.DeletionDetails_DELETION_REASON_AADHAR_MOBILE_MISMATCH.String(),
			},
		},
	}
}

func NextActionToSkipOnbStageNextAction(stage onPb.OnboardingStage) *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_SKIP_ONBOARDING_STAGE_API,
		ScreenOptions: &dlPb.Deeplink_SkipOnboardingStageApiOption{
			SkipOnboardingStageApiOption: &dlPb.SkipOnboardingStageApiOption{
				Stage: stage.String(),
			},
		},
	}
}

func GetNrOnboardingBlockDl() *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_INFO_ACKNOWLEDGEMENT_SCREEN,
		ScreenOptions: &dlPb.Deeplink_InfoAcknowledgementScreenOptions{
			InfoAcknowledgementScreenOptions: &dlPb.InfoAcknowledgementScreenOptions{
				ScreenContent: &dlPb.InfoAcknowledgementScreenOptions_ScreenContentTheme1_{
					ScreenContentTheme1: &dlPb.InfoAcknowledgementScreenOptions_ScreenContentTheme1{
						TitleText:    commontypes.GetTextFromHtmlStringFontColourFontStyle("NRE Savings Accounts are temporarily paused", "#313234", commontypes.FontStyle_HEADLINE_L),
						SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("NRE digital onboarding, which was a pilot project authorised by the regulator, has come to an end. Our banking partner will not be able to accept applications anymore.<br><br>Thank you for your interest and cooperation. We'll notify you once this becomes available again.", "#6A6D70", commontypes.FontStyle_BODY_S),
						ScreenImage:  commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/onboarding/reminder.png", 98, 90),
					},
				},
				ScreenTheme: dlPb.InfoAcknowledgementScreenOptions_SCREEN_THEME_1,
			},
		},
	}
}

func GetWalkthroughScreenDeeplink() *dlPb.Deeplink {
	return &dlPb.Deeplink{
		Screen: dlPb.Screen_WALKTHROUGH_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&walkthroughScreenOptions.WalkthroughScreenOptions{
			Skip: &dlPb.Cta{
				Text:         "SKIP",
				DisplayTheme: dlPb.Cta_PRIMARY,
				Status:       dlPb.Cta_CTA_STATUS_ENABLED,
				Deeplink: &dlPb.Deeplink{
					Screen: dlPb.Screen_HOME,
				},
			},
			WalkthroughLottie: commontypes.GetVisualElementLottieFromUrl("https://epifi-icons.pointz.in/home-v2/activation/onboarding-walkthrough-2.json").WithRepeatCount(0),
			Ctas: []*dlPb.Cta{
				{
					Text:         "Yes! Let's go",
					DisplayTheme: dlPb.Cta_PRIMARY,
					Status:       dlPb.Cta_CTA_STATUS_ENABLED,
					Deeplink: &dlPb.Deeplink{
						Screen: dlPb.Screen_HOME,
					},
				},
			},
			Loader: &walkthroughScreenOptions.WalkthroughLoader{
				LoaderTitle: commontypes.GetPlainStringText("Personalising"),
			},
			SkipCtaDelayDuration: 3,
		}),
	}
}
