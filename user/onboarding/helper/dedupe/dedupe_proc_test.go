package dedupe

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	dateTimeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/names"

	"github.com/epifi/gamma/api/bankcust"
	bankCustMock "github.com/epifi/gamma/api/bankcust/mocks"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	kycPb "github.com/epifi/gamma/api/kyc"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	eventMock "github.com/epifi/gamma/testing/mocks"
	"github.com/epifi/gamma/user/config/genconf"
	daoMocks "github.com/epifi/gamma/user/onboarding/dao/mocks"
	"github.com/epifi/gamma/user/onboarding/dao/model"
	"github.com/epifi/gamma/user/onboarding/helper/deeplink"
	errorOnb "github.com/epifi/gamma/user/onboarding/pkg/error"
)

var (
	genConf *genconf.Config
)

func TestMain(m *testing.M) {
	var err error
	logger.Init(cfg.TestEnv)
	if genConf, err = genconf.Load(); err != nil {
		logger.ErrorNoCtx("failed to load dynamic config", zap.Error(err))
		return
	}
	exitCode := m.Run()
	os.Exit(exitCode)
}

func TestProc_DedupeCheckCore(t *testing.T) {
	t.Parallel()
	var (
		actor1      = "actor-1"
		userFixture = &userPb.User{
			ActorId: actor1,
			Id:      "user-id",
			Profile: &userPb.Profile{
				PAN: "pan_number",
				PhoneNumber: &commontypes.PhoneNumber{
					CountryCode:    91,
					NationalNumber: **********,
				},
				Email: "<EMAIL>",
			},
		}
		_        = fmt.Errorf("random error")
		idProofs = []*kycPb.IdProof{
			{
				Type:    kycPb.IdProofType_VOTER_ID,
				IdValue: "voted-id-1",
			},
			{
				Type:    kycPb.IdProofType_UID,
				IdValue: "aadhar-id-1",
			},
			{
				Type:    kycPb.IdProofType_PASSPORT,
				IdValue: "passport-number-1",
			},
			{
				Type:    kycPb.IdProofType_DRIVING_LICENSE,
				IdValue: "driving-license-1",
			},
		}
	)
	// Setup logger
	ctx := context.Background()
	type mockStruct struct {
		usersClient    *mocks.MockUsersClient
		onbDao         *daoMocks.MockOnboardingDao
		timeClient     *dateTimeMocks.MockTime
		bankCustClient *bankCustMock.MockBankCustomerServiceClient
	}
	type args struct {
		user     *userPb.User
		vendor   commonvgpb.Vendor
		idProofs []*kycPb.IdProof
		stage    onboarding.OnboardingStage
	}
	tests := map[string]struct {
		args       args
		allow      bool
		mocks      func(args args, mocks *mockStruct)
		wantStatus customer.DedupeStatus
		wantErr    error
	}{
		"error in dedupe check": {
			args: args{
				user:     userFixture,
				vendor:   commonvgpb.Vendor_FEDERAL_BANK,
				idProofs: idProofs,
				stage:    onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			allow: false,
			mocks: func(args args, mocks *mockStruct) {
				p, d, v, a := getIdProofs(args.idProofs)
				mocks.usersClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:         args.user.GetActorId(),
					Vendor:          args.vendor,
					PanNumber:       args.user.GetProfile().GetPAN(),
					PhoneNumber:     args.user.GetProfile().GetPhoneNumber(),
					PassportNumber:  p,
					DrivingLicense:  d,
					VoterId:         v,
					UserId:          args.user.GetId(),
					UidReferenceKey: a,
					DateOfBirth:     args.user.GetProfile().GetDateOfBirth(),
					EmailId:         args.user.GetProfile().GetEmail(),
					DedupeFlow:      getDedupeFlowFromOnboardingStage(args.stage),
				}).Return(&userPb.DedupeCheckResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				mocks.onbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.user.GetActorId(), &model.GetOnbDetailsByActorParams{
					UseCache: true,
				}).Return(&onboarding.OnboardingDetails{
					Feature: onboarding.Feature_FEATURE_SA,
				}, nil)
			},
			wantStatus: 0,
			wantErr:    rpc.StatusAsError(rpc.StatusInternal()),
		},
		"customer does not exists": {
			args: args{
				user:     userFixture,
				vendor:   commonvgpb.Vendor_FEDERAL_BANK,
				idProofs: idProofs,
				stage:    onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			allow: true,
			mocks: func(args args, mocks *mockStruct) {
				p, d, v, a := getIdProofs(args.idProofs)
				mocks.usersClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:         args.user.GetActorId(),
					Vendor:          args.vendor,
					PanNumber:       args.user.GetProfile().GetPAN(),
					PhoneNumber:     args.user.GetProfile().GetPhoneNumber(),
					PassportNumber:  p,
					DrivingLicense:  d,
					VoterId:         v,
					UserId:          args.user.GetId(),
					UidReferenceKey: a,
					DateOfBirth:     args.user.GetProfile().GetDateOfBirth(),
					EmailId:         args.user.GetProfile().GetEmail(),
					DedupeFlow:      getDedupeFlowFromOnboardingStage(args.stage),
				}).Return(&userPb.DedupeCheckResponse{
					Status:       rpc.StatusOk(),
					DedupeStatus: customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
				}, nil)
				mocks.onbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.user.GetActorId(), &model.GetOnbDetailsByActorParams{
					UseCache: true,
				}).Return(&onboarding.OnboardingDetails{
					Feature: onboarding.Feature_FEATURE_SA,
				}, nil)

			},
			wantStatus: customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
			wantErr:    nil,
		},
		"customer exists": {
			args: args{
				user:     userFixture,
				vendor:   commonvgpb.Vendor_FEDERAL_BANK,
				idProofs: idProofs,
				stage:    onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			allow: true,
			mocks: func(args args, mocks *mockStruct) {
				p, d, v, a := getIdProofs(args.idProofs)
				mocks.usersClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:         args.user.GetActorId(),
					Vendor:          args.vendor,
					PanNumber:       args.user.GetProfile().GetPAN(),
					PhoneNumber:     args.user.GetProfile().GetPhoneNumber(),
					PassportNumber:  p,
					DrivingLicense:  d,
					VoterId:         v,
					UserId:          args.user.GetId(),
					UidReferenceKey: a,
					DateOfBirth:     args.user.GetProfile().GetDateOfBirth(),
					EmailId:         args.user.GetProfile().GetEmail(),
					DedupeFlow:      getDedupeFlowFromOnboardingStage(args.stage),
				}).Return(&userPb.DedupeCheckResponse{
					CustomerId:   "1234",
					Status:       rpc.StatusOk(),
					CustomerName: "Shivam Chaturvedi",
					DedupeStatus: customer.DedupeStatus_CUSTOMER_EXISTS,
				}, nil)
				mocks.bankCustClient.EXPECT().UpdateDedupeInfo(gomock.Any(), &bankcust.UpdateDedupeInfoRequest{
					ActorId:                    args.user.GetActorId(),
					Vendor:                     args.vendor,
					VendorCustomerId:           "1234",
					OriginalKycLevelWithVendor: kycPb.KYCLevel_FULL_KYC,
					DedupeName:                 names.ParseString("Shivam Chaturvedi"),
				}).Return(&bankcust.UpdateDedupeInfoResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mocks.onbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.user.GetActorId(), &model.GetOnbDetailsByActorParams{
					UseCache: true,
				}).Return(&onboarding.OnboardingDetails{
					Feature: onboarding.Feature_FEATURE_SA,
				}, nil)
			},
			wantStatus: customer.DedupeStatus_CUSTOMER_EXISTS,
			wantErr:    nil,
		},
		"dedupe users not allowed": {
			args: args{
				user:     userFixture,
				vendor:   commonvgpb.Vendor_FEDERAL_BANK,
				idProofs: idProofs,
				stage:    onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			allow: false,
			mocks: func(args args, mocks *mockStruct) {
				p, d, v, a := getIdProofs(args.idProofs)
				mocks.usersClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:         args.user.GetActorId(),
					Vendor:          args.vendor,
					PanNumber:       args.user.GetProfile().GetPAN(),
					PhoneNumber:     args.user.GetProfile().GetPhoneNumber(),
					PassportNumber:  p,
					DrivingLicense:  d,
					VoterId:         v,
					UserId:          args.user.GetId(),
					UidReferenceKey: a,
					DateOfBirth:     args.user.GetProfile().GetDateOfBirth(),
					EmailId:         args.user.GetProfile().GetEmail(),
					DedupeFlow:      getDedupeFlowFromOnboardingStage(args.stage),
				}).Return(&userPb.DedupeCheckResponse{
					CustomerId:   "1234",
					Status:       rpc.StatusOk(),
					CustomerName: "Shivam Chaturvedi",
					DedupeStatus: customer.DedupeStatus_CUSTOMER_EXISTS_MINOR,
				}, nil)
				mocks.onbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.user.GetActorId(), &model.GetOnbDetailsByActorParams{
					UseCache: true,
				}).Return(&onboarding.OnboardingDetails{
					Feature: onboarding.Feature_FEATURE_SA,
				}, nil)
			},
			wantStatus: customer.DedupeStatus_CUSTOMER_EXISTS_MINOR,
			wantErr:    nil,
		},
		"dedupe user not allowed as days past dues": {
			args: args{
				user:     userFixture,
				vendor:   commonvgpb.Vendor_FEDERAL_BANK,
				idProofs: idProofs,
				stage:    onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			allow: false,
			mocks: func(args args, mocks *mockStruct) {
				p, d, v, a := getIdProofs(args.idProofs)
				mocks.usersClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:         args.user.GetActorId(),
					Vendor:          args.vendor,
					PanNumber:       args.user.GetProfile().GetPAN(),
					PhoneNumber:     args.user.GetProfile().GetPhoneNumber(),
					PassportNumber:  p,
					DrivingLicense:  d,
					VoterId:         v,
					UserId:          args.user.GetId(),
					UidReferenceKey: a,
					DateOfBirth:     args.user.GetProfile().GetDateOfBirth(),
					EmailId:         args.user.GetProfile().GetEmail(),
					DedupeFlow:      getDedupeFlowFromOnboardingStage(args.stage),
				}).Return(&userPb.DedupeCheckResponse{
					Status:       rpc.StatusOk(),
					CustomerName: "Nirav Modi",
					DedupeStatus: customer.DedupeStatus_BLOCKED_AS_DAYS_PAST_DUES,
				}, nil)
				mocks.onbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.user.GetActorId(), &model.GetOnbDetailsByActorParams{
					UseCache: true,
				}).Return(&onboarding.OnboardingDetails{
					Feature: onboarding.Feature_FEATURE_SA,
				}, nil)
			},
			wantStatus: customer.DedupeStatus_BLOCKED_AS_DAYS_PAST_DUES,
			wantErr:    nil,
		},
		"non resident user: dedupe blocked": {
			args: args{
				user:     userFixture,
				vendor:   commonvgpb.Vendor_FEDERAL_BANK,
				idProofs: idProofs,
				stage:    onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			allow: true,
			mocks: func(args args, mocks *mockStruct) {
				p, d, v, a := getIdProofs(args.idProofs)
				mocks.usersClient.EXPECT().DedupeCheck(gomock.Any(), &userPb.DedupeCheckRequest{
					ActorId:         args.user.GetActorId(),
					Vendor:          args.vendor,
					PanNumber:       args.user.GetProfile().GetPAN(),
					PhoneNumber:     args.user.GetProfile().GetPhoneNumber(),
					PassportNumber:  p,
					DrivingLicense:  d,
					VoterId:         v,
					UserId:          args.user.GetId(),
					UidReferenceKey: a,
					DateOfBirth:     args.user.GetProfile().GetDateOfBirth(),
					EmailId:         args.user.GetProfile().GetEmail(),
					DedupeFlow:      getDedupeFlowFromOnboardingStage(args.stage),
				}).Return(&userPb.DedupeCheckResponse{
					Status:       rpc.StatusOk(),
					CustomerId:   "1234",
					CustomerName: "Nirav Modi",
					DedupeStatus: customer.DedupeStatus_CUSTOMER_EXISTS_NRI,
				}, nil)
				mocks.bankCustClient.EXPECT().UpdateDedupeInfo(gomock.Any(), &bankcust.UpdateDedupeInfoRequest{
					ActorId:                    args.user.GetActorId(),
					Vendor:                     args.vendor,
					VendorCustomerId:           "1234",
					OriginalKycLevelWithVendor: kycPb.KYCLevel_MIN_KYC,
					DedupeName:                 names.ParseString("Nirav Modi"),
				}).Return(&bankcust.UpdateDedupeInfoResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mocks.onbDao.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), args.user.GetActorId(), &model.GetOnbDetailsByActorParams{
					UseCache: true,
				}).Return(&onboarding.OnboardingDetails{
					Feature: onboarding.Feature_FEATURE_NON_RESIDENT_SA,
				}, nil)
			},
			wantStatus: customer.DedupeStatus_CUSTOMER_EXISTS_NRI,
			wantErr:    nil,
		},
	}
	for name, tt := range tests {
		name := name
		tt := tt
		t.Run(name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			usersClient := mocks.NewMockUsersClient(ctrl)
			broker := &eventMock.NoTestMockBroker{}
			onbDao := daoMocks.NewMockOnboardingDao(ctrl)
			timeClient := dateTimeMocks.NewMockTime(ctrl)
			bankCustClient := bankCustMock.NewMockBankCustomerServiceClient(ctrl)
			o := &Proc{
				usersClient: usersClient,
				broker:      broker,
				dynConf:     genConf.Onboarding(),
				onbDao:      onbDao,
				timeClient:  timeClient,
				bcClient:    bankCustClient,
			}
			tt.mocks(tt.args, &mockStruct{
				usersClient:    usersClient,
				onbDao:         onbDao,
				timeClient:     timeClient,
				bankCustClient: bankCustClient,
			})
			got, gotStatus, err := o.DedupeCheckCore(ctx, tt.args.user, tt.args.vendor, tt.args.idProofs, tt.args.stage)
			if tt.wantErr != nil && !assert.EqualError(t, err, tt.wantErr.Error()) {
				t.Errorf("DedupeCheckCore() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.allow {
				t.Errorf("DedupeCheckCore() got = %v, allow %v", got, tt.allow)
			}
			if gotStatus != tt.wantStatus {
				t.Errorf("DedupeCheckCore() gotStatus = %v, allow %v", gotStatus, tt.wantStatus)
			}
		})
	}
}

func TestProc_UpdateDedupeFlagInOnboardingDetails(t *testing.T) {
	t.Parallel()
	var (
		inputOnb = &onboarding.OnboardingDetails{
			OnboardingId:  "onb-id",
			ActorId:       "actor-id",
			UserId:        "user-id",
			StageMetadata: &onboarding.StageMetadata{},
		}
		ctx = context.Background()
	)

	type mockStruct struct {
		onbDao *daoMocks.MockOnboardingDao
	}
	type args struct {
		onb        *onboarding.OnboardingDetails
		dedupeFlag customer.DedupeStatus
		stage      onboarding.OnboardingStage
	}
	tests := map[string]struct {
		args    args
		mocks   func(args args, mocks *mockStruct)
		wantErr string
	}{
		"invalid stage": {
			args: args{
				onb:        inputOnb,
				dedupeFlag: customer.DedupeStatus_CUSTOMER_EXISTS,
				stage:      onboarding.OnboardingStage_INITIATE_CKYC,
			},
			mocks: func(args args, mocks *mockStruct) {

			},
			wantErr: fmt.Sprintf("unexpected onboarding stage %v for dedupe status update", onboarding.OnboardingStage_INITIATE_CKYC),
		},
		"customer exists in dedupe check stage": {
			args: args{
				onb:        inputOnb,
				dedupeFlag: customer.DedupeStatus_CUSTOMER_EXISTS,
				stage:      onboarding.OnboardingStage_DEDUPE_CHECK,
			},
			mocks: func(args args, mocks *mockStruct) {
				args.onb.GetStageMetadata().DedupeStatus = args.dedupeFlag
				mocks.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onboarding.OnboardingDetailsFieldMask{
					onboarding.OnboardingDetailsFieldMask_STAGE_METADATA,
				}, args.onb).Return(nil)
			},
			wantErr: "",
		},
		"customer exists in kyc dedupe check stage": {
			args: args{
				onb:        inputOnb,
				dedupeFlag: customer.DedupeStatus_CUSTOMER_EXISTS,
				stage:      onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			mocks: func(args args, mocks *mockStruct) {
				args.onb.GetStageMetadata().KycDedupeStatus = args.dedupeFlag
				mocks.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onboarding.OnboardingDetailsFieldMask{
					onboarding.OnboardingDetailsFieldMask_STAGE_METADATA,
				}, args.onb).Return(nil)
			},
			wantErr: "",
		},
		"customer exists in pre customer creation check stage": {
			args: args{
				onb:        inputOnb,
				dedupeFlag: customer.DedupeStatus_CUSTOMER_EXISTS,
				stage:      onboarding.OnboardingStage_PRE_CUSTOMER_CREATION_CHECK,
			},
			mocks: func(args args, mocks *mockStruct) {
				args.onb.GetStageMetadata().PreCustomerCreationDedupeStatus = args.dedupeFlag
				mocks.onbDao.EXPECT().UpdateOnboardingDetailsByColumns(gomock.Any(), []onboarding.OnboardingDetailsFieldMask{
					onboarding.OnboardingDetailsFieldMask_STAGE_METADATA,
				}, args.onb).Return(nil)
			},
			wantErr: "",
		},
	}
	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			onbDao := daoMocks.NewMockOnboardingDao(ctrl)
			o := &Proc{
				onbDao: onbDao,
			}
			tt.mocks(tt.args, &mockStruct{
				onbDao: onbDao,
			})
			err := o.UpdateDedupeFlagInOnboardingDetails(ctx, tt.args.onb, tt.args.dedupeFlag, tt.args.stage)
			if tt.wantErr != "" && !assert.EqualError(t, err, tt.wantErr) {
				t.Errorf("UpdateDedupeFlagInOnboardingDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestProc_AllowDedupeCheck(t *testing.T) {
	t.Parallel()
	var (
		ctx      = context.Background()
		inputOnb = &onboarding.OnboardingDetails{
			OnboardingId:  "onb-id",
			ActorId:       "actor-id",
			UserId:        "user-id",
			StageMetadata: &onboarding.StageMetadata{},
			StageDetails: &onboarding.StageDetails{
				StageMapping: map[string]*onboarding.StageInfo{
					onboarding.OnboardingStage_KYC_DEDUPE_CHECK.String(): {
						State:         onboarding.OnboardingState_RESET,
						LastUpdatedAt: timestamppb.Now(),
					},
				},
			},
		}
		manualIntOnb = &onboarding.OnboardingDetails{
			OnboardingId: "onb-id",
			ActorId:      "actor-id",
			UserId:       "user-id",
			StageDetails: &onboarding.StageDetails{
				StageMapping: map[string]*onboarding.StageInfo{
					onboarding.OnboardingStage_DEDUPE_CHECK.String(): {
						State:         onboarding.OnboardingState_MANUAL_INTERVENTION,
						LastUpdatedAt: timestamppb.Now(),
					},
				},
			},
			StageMetadata: &onboarding.StageMetadata{
				DedupeStatus: customer.DedupeStatus_CUSTOMER_EXISTS_MULTIPLE_CUSTOMER_ID,
			},
		}
	)
	type mockStruct struct {
		onbDao     *daoMocks.MockOnboardingDao
		timeClient *dateTimeMocks.MockTime
		bcClient   *bankCustMock.MockBankCustomerServiceClient
	}
	type args struct {
		onb   *onboarding.OnboardingDetails
		stage onboarding.OnboardingStage
	}
	tests := map[string]struct {
		args    args
		mocks   func(args args, mocks *mockStruct)
		want    bool
		wantErr error
	}{
		"internal error in bank customer status check": {
			args: args{
				onb:   inputOnb,
				stage: onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			mocks: func(args args, mocks *mockStruct) {
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.onb.GetVendor(),
					ActorId: args.onb.GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:    false,
			wantErr: rpc.StatusAsError(rpc.StatusInternal()),
		},
		"customer creation in progress": {
			args: args{
				onb:   inputOnb,
				stage: onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			mocks: func(args args, mocks *mockStruct) {
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.onb.GetVendor(),
					ActorId: args.onb.GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.ExtendedStatusInProgress(),
				}, nil)
			},
			want:    false,
			wantErr: nil,
		},
		"customer creation successful": {
			args: args{
				onb:   inputOnb,
				stage: onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			mocks: func(args args, mocks *mockStruct) {
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.onb.GetVendor(),
					ActorId: args.onb.GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
			want:    false,
			wantErr: nil,
		},
		"customer creation not started": {
			args: args{
				onb:   inputOnb,
				stage: onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			mocks: func(args args, mocks *mockStruct) {
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.onb.GetVendor(),
					ActorId: args.onb.GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED), "customer creation not started")(),
				}, nil)
			},
			want:    true,
			wantErr: nil,
		},
		"dedupe stage not in manual intervention": {
			args: args{
				onb:   inputOnb,
				stage: onboarding.OnboardingStage_KYC_DEDUPE_CHECK,
			},
			mocks: func(args args, mocks *mockStruct) {
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.onb.GetVendor(),
					ActorId: args.onb.GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_RETRYABLE_FAILURE), "customer creation not started")(),
				}, nil)
			},
			want:    true,
			wantErr: nil,
		},
		"dedupe stage in manual intervention and time more than cache time": {
			args: args{
				onb:   manualIntOnb,
				stage: onboarding.OnboardingStage_DEDUPE_CHECK,
			},
			mocks: func(args args, mocks *mockStruct) {
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.onb.GetVendor(),
					ActorId: args.onb.GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED), "customer creation not started")(),
				}, nil)
				mocks.timeClient.EXPECT().Since(args.onb.GetStageDetails().GetStageMapping()[args.stage.String()].GetLastUpdatedAt().AsTime()).
					Return(5 * time.Minute)
				mocks.onbDao.EXPECT().UpdateStatus(gomock.Any(), args.onb.GetOnboardingId(), args.stage, onboarding.OnboardingState_RESET).
					Return(nil, nil)
			},
			want:    true,
			wantErr: nil,
		},
		"dedupe stage in manual intervention and time less than cache time": {
			args: args{
				onb:   manualIntOnb,
				stage: onboarding.OnboardingStage_DEDUPE_CHECK,
			},
			mocks: func(args args, mocks *mockStruct) {
				mocks.bcClient.EXPECT().CheckBankCustomerCreationStatus(gomock.Any(), &bankcust.CheckBankCustomerCreationStatusRequest{
					Vendor:  args.onb.GetVendor(),
					ActorId: args.onb.GetActorId(),
				}).Return(&bankcust.CheckBankCustomerCreationStatusResponse{
					Status: rpc.NewStatusFactory(uint32(bankcust.CheckBankCustomerCreationStatusResponse_NOT_STARTED), "customer creation not started")(),
				}, nil)
				mocks.timeClient.EXPECT().Since(args.onb.GetStageDetails().GetStageMapping()[args.stage.String()].GetLastUpdatedAt().AsTime()).
					Return(1 * time.Minute)
			},
			want:    false,
			wantErr: nil,
		},
	}
	for name, tt := range tests {
		name := name
		tt := tt
		t.Run(name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			onbDao := daoMocks.NewMockOnboardingDao(ctrl)
			bcClient := bankCustMock.NewMockBankCustomerServiceClient(ctrl)
			timeClient := dateTimeMocks.NewMockTime(ctrl)
			o := &Proc{
				onbDao:     onbDao,
				timeClient: timeClient,
				bcClient:   bcClient,
				dynConf:    genConf.Onboarding(),
			}
			tt.mocks(tt.args, &mockStruct{
				onbDao:     onbDao,
				timeClient: timeClient,
				bcClient:   bcClient,
			})
			got, err := o.AllowDedupeCheck(ctx, tt.args.onb, tt.args.stage)
			if tt.wantErr != nil && !assert.EqualError(t, err, tt.wantErr.Error()) {
				t.Errorf("AllowDedupeCheck() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equalf(t, tt.want, got, "AllowDedupeCheck(%v, %v, %v)", ctx, tt.args.onb, tt.args.stage)
		})
	}
}

func TestProc_DedupeDeeplinkHandler(t *testing.T) {
	t.Parallel()
	var (
		ctx = context.Background()
	)

	type mockStruct struct {
		onbDao *daoMocks.MockOnboardingDao
	}
	type args struct {
		onb        *onboarding.OnboardingDetails
		dedupeFlag customer.DedupeStatus
	}
	tests := map[string]struct {
		args  args
		mocks func(args args, mocks *mockStruct)
		want  *dlPb.Deeplink
	}{
		"new-to-bank nri user": {
			args: args{
				onb: &onboarding.OnboardingDetails{
					OnboardingId: "onb-id",
					ActorId:      "actor-id",
					UserId:       "user-id",
					Feature:      onboarding.Feature_FEATURE_NON_RESIDENT_SA,
				},
				dedupeFlag: customer.DedupeStatus_CUSTOMER_DOEST_NOT_EXISTS,
			},
			mocks: func(args args, mocks *mockStruct) {},
			want:  nil,
		},
		"existing-to-bank nri user": {
			args: args{
				onb: &onboarding.OnboardingDetails{
					OnboardingId: "onb-id",
					ActorId:      "actor-id",
					UserId:       "user-id",
					Feature:      onboarding.Feature_FEATURE_NON_RESIDENT_SA,
				},
				dedupeFlag: customer.DedupeStatus_CUSTOMER_EXISTS,
			},
			mocks: func(args args, mocks *mockStruct) {},
			want:  deeplink.NewErrorFullScreen(ctx, errorOnb.NRDedupeError),
		},
		"dob mismatch": {
			args: args{
				onb: &onboarding.OnboardingDetails{
					OnboardingId: "onb-id",
					ActorId:      "actor-id",
					UserId:       "user-id",
				},
				dedupeFlag: customer.DedupeStatus_CUSTOMER_EXISTS_DOB_MISMATCH,
			},
			mocks: func(args args, mocks *mockStruct) {},
			want:  NewDedupeError(ctx, customer.DedupeStatus_CUSTOMER_EXISTS_DOB_MISMATCH),
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			onbDao := daoMocks.NewMockOnboardingDao(ctrl)
			o := &Proc{
				dynConf: genConf.Onboarding(),
				onbDao:  onbDao,
			}
			tt.mocks(tt.args, &mockStruct{
				onbDao: onbDao,
			})
			got := o.DedupeDeeplinkHandler(ctx, tt.args.onb, tt.args.dedupeFlag)
			assert.Equal(t, tt.want, got)
		})
	}
}
