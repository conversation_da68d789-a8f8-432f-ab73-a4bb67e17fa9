package dedupe

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	appFbPb "github.com/epifi/gamma/api/inapphelp/app_feedback"
	userPb "github.com/epifi/gamma/api/user"
	vgPbCustomer "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/pkg/frontend/cx"
)

type ErrorScreenOpts struct {
	// content
	Title    string
	Subtitle string

	// only for Screen_DETAILED_ERROR_VIEW_SCREEN
	FailureReason  string
	HeaderImageURL string

	// used in user feedback flow
	Category appFbPb.AppScreen

	// CTAs
	HasChat     bool
	HasCall     bool
	HasFeedback bool
	HasRetry    bool
}

const (
	CTACall     = "Call us"
	CTAChat     = "Chat with us"
	CTAFeedback = "Give feedback"

	CTAHeaderFeedback = "How was your experience?"
	CTAHeaderCX       = "Need further assistance?"

	NRICheckFailureTitle                = "Looks like you are an NRI"
	NRICheckFailureMessage              = "Any Non-Resident Indian (NRI) with an existing Federal a/c currently does not have access to the services provided by Federal Bank through the Fi app"
	MinorCheckFailureTitle              = "Are you under the age of 18 years?"
	MinorCheckFailureMessage            = "Minors with Federal Bank accounts do not currently have access to the services provided by Federal Bank through the Fi app"
	PartialKycFlow                      = "You already have min-KYC account with Federal Bank or its partners"
	PartialKycFlowSubtitle              = "As soon as you complete your full-KYC procedure at Federal Bank — or one of the partners, like Jupiter — you can continue with Fi✅"
	MultipleCustomerIDsExistTitle       = "You have multiple customer IDs at Federal Bank"
	MultipleCustomerIDsExistMessage     = "You can only have one customer ID. So, you currently don't have access to the services provided by Federal Bank through the Fi app. Please contact Federal Bank customer support for further help."
	PhoneNumberMismatch                 = "You already have an account with Federal Bank or its partners"
	PhoneNumberMismatchSub              = "While signing up on Fi, please use the same phone number you've registered with on Federal or one of the partners, like Jupiter📱"
	DOBMismatchTitle                    = "Uh-oh! There's a Date of Birth mismatch"
	DOBMismatchSubtitle                 = "Your Federal Bank a/c has a different Date of Birth (DOB). Please update it at Federal Bank."
	PhoneNumberLinkedToMultipleAccounts = "Looks like your phone number is linked to multiple existing Federal Bank accounts — or one of the partners, like Jupiter. " +
		"Please contact the Federal Bank on 1800-425-1199 or 1800-420-1199 for assistance before you continue."
	PhoneNumberLinkedToExistingAccount = "Looks like the phone number you are using is linked to another user's Federal Bank account — or one of the partners, like Jupiter. " +
		"Please contact the Federal Bank on 1800-425-1199 or 1800-420-1199 for assistance before you continue."
	GenericDedupeCheckFailure = "Looks like you already have a Federal Bank Account. " +
		"Please contact the Federal Bank on 1800-425-1199 or 1800-420-1199 for assistance."
	PhoneNumberMismatchTitle     = "Phone number already in use with Federal bank"
	PartialProfileTitle          = "You already have a Savings Account with Federal Bank or its partners"
	PartialProfileMsg            = "As soon as you update all your profile details at Federal Bank — or one of the partners, like Jupiter — you can continue with Fi ✅"
	BlockedUserWithDuesTitle     = "Have existing dues with Federal Bank?"
	BlockedUserWithDuesMsg       = "Clear your dues to get access to the services provided by Federal Bank through the Fi app"
	FailureImageURL              = "https://epifi-icons.pointz.in/onboarding/caveman.png"
	EmailMismatchTitle           = "Email already in use with Federal bank"
	EmailMismatchSubtitle        = "While signing up on Fi, please use the same email you've registered with on Federal or one of the partners, like Jupiter📱"
	EmailLinkedToExistingAccount = "Looks like the email address you are using is linked to another user's Federal Bank account — or one of the partners, like Jupiter. "
	KycNotValidTitle             = "You have an existing account with Federal for which KYC expired or due."
	KycNotValidSubTitle          = "If your existing KYC info is correct, SMS \"KYC Y\" to ********** from your registered mobile number. " +
		"<br><br>If your details like income, occupation, address have changed, please check the mail from Federal Bank with the subject \"Periodic Know Your Customer (KYC) Updation\" for more info." +
		"<br><br>Note: KYC updation takes 5-6 business hours! So, after you update KYC — consider waiting for a few hours before trying to log in."
	GenericBlockingTitle    = "We cannot open an account for you"
	GenericBlockingSubtitle = "Unfortunately, we won't be able to open a Federal savings account as we couldn't verify your details"
	GenericBlockingIcon     = "https://epifi-icons.pointz.in/onboarding/door_with_nails.png"
)

var StatusToDeeplinkDataMapping = map[vgPbCustomer.DedupeStatus]*ErrorScreenOpts{
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PARTIAL_KYC: {
		Title:       PartialKycFlow,
		Subtitle:    PartialKycFlowSubtitle,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PHONE_MISMATCH: {
		Title:       PhoneNumberMismatch,
		Subtitle:    PhoneNumberMismatchSub,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT: {
		Title:       PhoneNumberMismatchTitle,
		Subtitle:    PhoneNumberLinkedToExistingAccount,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_EXISTING_ACCOUNT: {
		Title:       EmailMismatchTitle,
		Subtitle:    EmailLinkedToExistingAccount,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_NRI: {
		Title:       NRICheckFailureTitle,
		Subtitle:    NRICheckFailureMessage,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_NRI_MISSING_CUSTOMER_ID: {
		Title:       NRICheckFailureTitle,
		Subtitle:    NRICheckFailureMessage,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_MINOR: {
		Title:       MinorCheckFailureTitle,
		Subtitle:    MinorCheckFailureMessage,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_MULTIPLE_CUSTOMER_ID: {
		Title:       MultipleCustomerIDsExistTitle,
		Subtitle:    MultipleCustomerIDsExistMessage,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS: {
		Title:       PhoneNumberMismatchTitle,
		Subtitle:    PhoneNumberLinkedToMultipleAccounts,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT: {
		Title:       PhoneNumberMismatchTitle,
		Subtitle:    PhoneNumberLinkedToExistingAccount,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_DOB_MISMATCH: {
		Title:       DOBMismatchTitle,
		Subtitle:    DOBMismatchSubtitle,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PARTIAL_PROFILE: {
		Title:       PartialProfileTitle,
		Subtitle:    PartialProfileMsg,
		HasChat:     false,
		HasCall:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_BLOCKED: {
		Title:          GenericBlockingTitle,
		Subtitle:       GenericBlockingSubtitle,
		HeaderImageURL: GenericBlockingIcon,
		Category:       appFbPb.AppScreen_APP_SCREEN_ONBOARDING_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_BLOCKED_AS_DAYS_PAST_DUES: {
		Title:       BlockedUserWithDuesTitle,
		Subtitle:    BlockedUserWithDuesMsg,
		HasCall:     false,
		HasChat:     false,
		HasFeedback: true,
		Category:    appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_EMAIL_MISMATCH: {
		Title:          EmailMismatchTitle,
		Subtitle:       EmailMismatchSubtitle,
		HeaderImageURL: GenericBlockingIcon,
		Category:       appFbPb.AppScreen_APP_SCREEN_ONBOARDING_TERMINAL,
	},
	vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_KYC_NOT_VALID: {
		Title:          KycNotValidTitle,
		Subtitle:       KycNotValidSubTitle,
		HeaderImageURL: GenericBlockingIcon,
		Category:       appFbPb.AppScreen_APP_SCREEN_ONBOARDING_TERMINAL,
	},
}

func NewDedupeError(ctx context.Context, dedupeStatus vgPbCustomer.DedupeStatus) *dlPb.Deeplink {
	deeplinkData, dataExists := StatusToDeeplinkDataMapping[dedupeStatus]
	if !dataExists { // Handling in case of error
		deeplinkData = &ErrorScreenOpts{
			Title:          GenericDedupeCheckFailure,
			HasChat:        false,
			HasCall:        false,
			HasFeedback:    true,
			Category:       appFbPb.AppScreen_APP_SCREEN_ONBOARDING_DEDUPE_CHECK_TERMINAL,
			HeaderImageURL: FailureImageURL,
		}
	}
	dedupeFailureImage := FailureImageURL
	if deeplinkData.HeaderImageURL != "" {
		dedupeFailureImage = deeplinkData.HeaderImageURL
	}
	opts := &ErrorScreenOpts{
		Title:          deeplinkData.Title,
		HasChat:        deeplinkData.HasChat,
		HasCall:        deeplinkData.HasCall,
		Subtitle:       deeplinkData.Subtitle,
		HasFeedback:    deeplinkData.HasFeedback,
		Category:       deeplinkData.Category,
		HeaderImageURL: dedupeFailureImage,
	}
	ctas, ctaHeader := getCTAInfoFromErrOpts(ctx, opts)

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_DETAILED_ERROR_VIEW_SCREEN,
		ScreenOptions: &dlPb.Deeplink_DetailedErrorViewScreenOptions{
			DetailedErrorViewScreenOptions: &dlPb.DetailedErrorViewScreenOptions{
				Title:            opts.Title,
				Subtitle:         opts.Subtitle,
				FailureReason:    opts.FailureReason,
				ImageUrl:         opts.HeaderImageURL,
				HasFeedback:      opts.HasFeedback,
				ScreenIdentifier: opts.Category.String(),
				CtaHeader:        ctaHeader,
				Ctas:             ctas,
			},
		},
	}
}

func NewAccountDeletionScreen(ctx context.Context, dedupeStatus vgPbCustomer.DedupeStatus, showHelper bool, ph *commontypes.PhoneNumber) *dlPb.Deeplink {
	if (dedupeStatus == vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT ||
		dedupeStatus == vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS ||
		dedupeStatus == vgPbCustomer.DedupeStatus_CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT ||
		dedupeStatus == vgPbCustomer.DedupeStatus_CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS) && showHelper {
		phNumString := strconv.FormatUint(ph.GetNationalNumber(), 10)
		phStr := phNumString[len(phNumString)-4:]
		return &dlPb.Deeplink{
			Screen: dlPb.Screen_ACCOUNT_DELETION_ACKNOWLEDGEMENT,
			ScreenOptions: &dlPb.Deeplink_AccountDeletionAcknowledgementScreenOptions{
				AccountDeletionAcknowledgementScreenOptions: &dlPb.AccountDeletionAcknowledgementScreenOptions{
					ImageUrl: "https://epifi-icons.pointz.in/onboarding/caveman.png",
					Title:    fmt.Sprintf("Your mobile number ending in ••%v is linked to an existing Federal Account for different user.", phStr),
					Subtitle: "You can either start the process again using the same mobile number you registered with Federal Bank.\n\n" +
						"Or call Federal bank at  <font color='#00B899'>1800-425-1199</font> or  <font color='#00B899'>1800-420-1199</font> and they’ll help you get your number delinked.",
					Cta: &dlPb.Cta{
						Text: "Retry with different mobile number",
					},
					DeletionReason: userPb.DeletionDetails_DELETION_REASON_DEDUPE_MISMATCH.String(),
				},
			},
		}
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_ACCOUNT_DELETION_ACKNOWLEDGEMENT,
		ScreenOptions: &dlPb.Deeplink_AccountDeletionAcknowledgementScreenOptions{
			AccountDeletionAcknowledgementScreenOptions: &dlPb.AccountDeletionAcknowledgementScreenOptions{
				ImageUrl: "https://epifi-icons.pointz.in/onboarding/caveman.png",
				Title:    "You have an existing Federal Account with a different mobile number",
				Subtitle: "No worries! Just start the process again using the same mobile number you registered with Federal Bank (or one of its other partners like Jupiter). It'll be quick, promise 👌",
				Cta: &dlPb.Cta{
					Text: "Retry with different mobile number",
				},
				DeletionReason: userPb.DeletionDetails_DELETION_REASON_DEDUPE_MISMATCH.String(),
			},
		},
	}
}

func EmailDedupeValidationFailedAccountDeletionScreen(ctx context.Context, dedupeStatus vgPbCustomer.DedupeStatus, showHelper bool, email string) *dlPb.Deeplink {
	if (dedupeStatus == vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_EMAIL_LINKED_TO_EXISTING_ACCOUNT ||
		dedupeStatus == vgPbCustomer.DedupeStatus_CUSTOMER_EXISTS_EMAIL_LINKED_TO_MULTIPLE_ACCOUNTS ||
		dedupeStatus == vgPbCustomer.DedupeStatus_CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_EXISTING_ACCOUNT ||
		dedupeStatus == vgPbCustomer.DedupeStatus_CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_MULTIPLE_ACCOUNTS) && showHelper {
		maskedEmail := getMaskedEmail(email)
		return &dlPb.Deeplink{
			Screen: dlPb.Screen_ACCOUNT_DELETION_ACKNOWLEDGEMENT,
			ScreenOptions: &dlPb.Deeplink_AccountDeletionAcknowledgementScreenOptions{
				AccountDeletionAcknowledgementScreenOptions: &dlPb.AccountDeletionAcknowledgementScreenOptions{
					ImageUrl: "https://epifi-icons.pointz.in/onboarding/caveman.png",
					Title:    fmt.Sprintf("Your email ending in ••%v is linked to an existing Federal Account for different user.", maskedEmail),
					Subtitle: "You can either start the process again using the same email you registered with Federal Bank.\n\n" +
						"Or call Federal bank at  <font color='#00B899'>1800-425-1199</font> or  <font color='#00B899'>1800-420-1199</font> and they’ll help you get your email delinked.",
					Cta: &dlPb.Cta{
						Text: "Retry with different email",
					},
					DeletionReason: userPb.DeletionDetails_DELETION_REASON_DEDUPE_MISMATCH.String(),
				},
			},
		}
	}

	return &dlPb.Deeplink{
		Screen: dlPb.Screen_ACCOUNT_DELETION_ACKNOWLEDGEMENT,
		ScreenOptions: &dlPb.Deeplink_AccountDeletionAcknowledgementScreenOptions{
			AccountDeletionAcknowledgementScreenOptions: &dlPb.AccountDeletionAcknowledgementScreenOptions{
				ImageUrl: "https://epifi-icons.pointz.in/onboarding/caveman.png",
				Title:    "You have an existing Federal Account with a different email",
				Subtitle: "No worries! Just start the process again using the same email you registered with Federal Bank (or one of its other partners like Jupiter). It'll be quick, promise 👌",
				Cta: &dlPb.Cta{
					Text: "Retry with different email",
				},
				DeletionReason: userPb.DeletionDetails_DELETION_REASON_AADHAR_MOBILE_MISMATCH.String(),
			},
		},
	}
}

func getMaskedEmail(email string) string {
	parts := strings.Split(email, "@")
	firstPart := parts[0]
	masked := firstPart[0:1] + strings.Repeat("x", len(firstPart)-2) + firstPart[len(firstPart)-1:]
	return strings.Join([]string{masked, "@", parts[1]}, "")
}

func getCTAInfoFromErrOpts(ctx context.Context, opts *ErrorScreenOpts) ([]*dlPb.Cta, string) {
	var (
		ctas                  []*dlPb.Cta
		ctaHeader             string
		isTertiaryCTAReserved bool
	)

	// cx chat and call both are now handled via contact us flow, hence re-directing there
	if opts.HasChat || opts.HasCall {
		isTertiaryCTAReserved = true
		ctaHeader = CTAHeaderCX
		ctas = append(ctas, cx.GetContactUsCta(ctx))
	}

	if !isTertiaryCTAReserved && opts.HasFeedback {
		ctaHeader = CTAHeaderFeedback
		ctas = append(ctas, &dlPb.Cta{
			Text:         CTAFeedback,
			Type:         dlPb.Cta_FEEDBACK,
			DisplayTheme: dlPb.Cta_TERTIARY,
		})
	}

	return ctas, ctaHeader
}
