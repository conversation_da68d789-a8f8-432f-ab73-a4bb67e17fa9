package onboarding

import (
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/vendorstore"
)

var (
	OnbStageToVendorResponseApiMap = map[string][]vendorstore.Api{
		"CUSTOMER_CREATION":       {vendorstore.API_CUSTOMER_CREATION_INIT, vendorstore.API_CUSTOMER_CREATION_ENQUIRY, vendorstore.API_CUSTOMER_CREATION_CALLBACK},
		"ACCOUNT_CREATION":        {vendorstore.API_ACCOUNT_CREATION_INIT, vendorstore.API_ACCOUNT_CREATION_ENQUIRY, vendorstore.API_ACCOUNT_CREATION_CALLBACK},
		"DEVICE_REGISTRATION":     {vendorstore.API_DEVICE_REGISTRATION, vendorstore.API_DEVICE_REGISTRATION_ENQUIRY},
		"SHIPPING_ADDRESS_UPDATE": {vendorstore.API_SHIPPING_ADDRESS_UPDATE_INIT, vendorstore.API_SHIPPING_ADDRESS_UPDATE_ENQUIRY, vendorstore.API_SHIPPING_ADDRESS_UPDATE_CALLBACK},
		"CREDIT_REPORT":           {vendorstore.API_CREDIT_REPORT_PRESENCE, vendorstore.API_CREDIT_REPORT_FETCH},
		"CARD_CREATION":           {vendorstore.API_CREATE_CARD_INIT, vendorstore.API_CREATE_CARD_ENQUIRY, vendorstore.API_CREATE_CARD_CALLBACK},
		"DEDUPE_CHECK":            {vendorstore.FEDERAL_API_DEDUPE_CHECK},
	}
)

func stageInfo(onb *onbPb.OnboardingDetails, stage onbPb.OnboardingStage) *onbPb.StageInfo {
	return onb.GetStageDetails().GetStageMapping()[stage.String()]
}
