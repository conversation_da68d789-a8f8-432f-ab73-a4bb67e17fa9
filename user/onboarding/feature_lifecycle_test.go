package onboarding

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"github.com/epifi/be-common/api/rpc"

	productPb "github.com/epifi/gamma/api/product"
	productMock "github.com/epifi/gamma/api/product/mocks"
	screenerPb "github.com/epifi/gamma/api/screener"
	mockScreener "github.com/epifi/gamma/api/screener/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	mockDao "github.com/epifi/gamma/user/onboarding/dao/mocks"
	"github.com/epifi/gamma/user/onboarding/dao/model"
)

func TestService_GetFeatureLifecycle(t *testing.T) {
	type args struct {
		ctx context.Context
		req *onbPb.GetFeatureLifecycleRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(onboardingDaoMock *mockDao.MockOnboardingDao, screenerClientMock *mockScreener.MockScreenerClient, productClientMock *productMock.MockProductClient)
		want       *onbPb.GetFeatureLifecycleResponse
		wantErr    assert.ErrorAssertionFunc
	}{
		{
			name: "should return error if GetDetails fails",
			args: args{
				ctx: context.Background(),
				req: &onbPb.GetFeatureLifecycleRequest{
					ActorId:        "actor-1",
					Features:       []onbPb.Feature{onbPb.Feature_FEATURE_SA},
					WantCachedData: true,
				},
			},
			setupMocks: func(onboardingDaoMock *mockDao.MockOnboardingDao, screenerClientMock *mockScreener.MockScreenerClient, productClientMock *productMock.MockProductClient) {
				onboardingDaoMock.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), "actor-1", &model.GetOnbDetailsByActorParams{UseCache: true}).
					Return(nil, fmt.Errorf("error in GetOnboardingDetailsByActor"))
				screenerClientMock.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screenerPb.GetScreenerAttemptsByActorIdRequest{ActorId: "actor-1", CachedData: true}).
					Return(&screenerPb.GetScreenerAttemptsByActorIdResponse{Status: rpc.StatusOk()}, nil)
				productClientMock.EXPECT().GetProductsStatus(gomock.Any(), &productPb.GetProductsStatusRequest{ActorId: "actor-1", ProductTypes: []productPb.ProductType{productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS, productPb.ProductType_PRODUCT_TYPE_TPAP, productPb.ProductType_PRODUCT_TYPE_WEALTH_ANALYSER}}).
					Return(&productPb.GetProductsStatusResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &onbPb.GetFeatureLifecycleResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return error if GetScreenerAttemptsByActorId fails",
			args: args{
				ctx: context.Background(),
				req: &onbPb.GetFeatureLifecycleRequest{
					ActorId:        "actor-1",
					Features:       []onbPb.Feature{onbPb.Feature_FEATURE_SA},
					WantCachedData: true,
				},
			},
			setupMocks: func(onboardingDaoMock *mockDao.MockOnboardingDao, screenerClientMock *mockScreener.MockScreenerClient, productClientMock *productMock.MockProductClient) {
				onboardingDaoMock.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), "actor-1", &model.GetOnbDetailsByActorParams{UseCache: true}).
					Return(&onbPb.OnboardingDetails{}, nil)
				screenerClientMock.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screenerPb.GetScreenerAttemptsByActorIdRequest{ActorId: "actor-1", CachedData: true}).
					Return(&screenerPb.GetScreenerAttemptsByActorIdResponse{Status: rpc.StatusInternal()}, nil)
				productClientMock.EXPECT().GetProductsStatus(gomock.Any(), &productPb.GetProductsStatusRequest{ActorId: "actor-1", ProductTypes: []productPb.ProductType{productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS, productPb.ProductType_PRODUCT_TYPE_TPAP, productPb.ProductType_PRODUCT_TYPE_WEALTH_ANALYSER}}).
					Return(&productPb.GetProductsStatusResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &onbPb.GetFeatureLifecycleResponse{
				Status: rpc.StatusInternal(),
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return feature lifecycle details - 1",
			args: args{
				ctx: context.Background(),
				req: &onbPb.GetFeatureLifecycleRequest{
					ActorId:        "actor-1",
					Features:       []onbPb.Feature{onbPb.Feature_FEATURE_SA},
					WantCachedData: true,
				},
			},
			setupMocks: func(onboardingDaoMock *mockDao.MockOnboardingDao, screenerClientMock *mockScreener.MockScreenerClient, productClientMock *productMock.MockProductClient) {
				onboardingDaoMock.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), "actor-1", &model.GetOnbDetailsByActorParams{UseCache: true}).
					Return(&onbPb.OnboardingDetails{
						StageMetadata: &onbPb.StageMetadata{
							SoftIntentSelectionMetadata: &onbPb.SoftIntentSelectionMetadata{
								Selection: []onbPb.OnboardingSoftIntent{
									onbPb.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI,
									onbPb.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INSTANT_SALARY,
								},
							},
						},
					}, nil)
				screenerClientMock.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screenerPb.GetScreenerAttemptsByActorIdRequest{ActorId: "actor-1", CachedData: true}).
					Return(&screenerPb.GetScreenerAttemptsByActorIdResponse{
						Status: rpc.StatusOk(),
						ChecksMap: []*screenerPb.CheckDetails{
							{
								CheckType:   screenerPb.CheckType_CHECK_TYPE_INCOME_ESTIMATE,
								CheckResult: screenerPb.CheckResult_CHECK_RESULT_FAILED,
							},
							{
								CheckType:   screenerPb.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS,
								CheckResult: screenerPb.CheckResult_CHECK_RESULT_PASSED,
							},
						},
					}, nil)
				productClientMock.EXPECT().GetProductsStatus(gomock.Any(), &productPb.GetProductsStatusRequest{ActorId: "actor-1", ProductTypes: []productPb.ProductType{productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS, productPb.ProductType_PRODUCT_TYPE_TPAP, productPb.ProductType_PRODUCT_TYPE_WEALTH_ANALYSER}}).
					Return(&productPb.GetProductsStatusResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &onbPb.GetFeatureLifecycleResponse{
				Status: rpc.StatusOk(),
				FeatureLifecycleMap: map[string]*onbPb.FeatureLifecycle{
					onbPb.Feature_FEATURE_SA.String(): {
						Feature: onbPb.Feature_FEATURE_SA,
						IntentSelectionInfo: &onbPb.FeatureIntentSelectionInfo{
							SelectedAsHardIntent: false,
							SelectedSoftIntents:  []onbPb.OnboardingSoftIntent{onbPb.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI},
						},
						EligibilityStatus: &onbPb.FeatureEligibility{
							Status: onbPb.FeatureEligibility_STATUS_PASSED,
						},
						ActivationStatus: onbPb.FeatureStatus_FEATURE_STATUS_UNSPECIFIED,
					},
				},
				OnboardingDetails: &onbPb.OnboardingDetails{
					StageMetadata: &onbPb.StageMetadata{
						SoftIntentSelectionMetadata: &onbPb.SoftIntentSelectionMetadata{
							Selection: []onbPb.OnboardingSoftIntent{
								onbPb.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI,
								onbPb.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INSTANT_SALARY,
							},
						},
					},
				},
			},
			wantErr: assert.NoError,
		},
		{
			name: "should return feature lifecycle details - 2",
			args: args{
				ctx: context.Background(),
				req: &onbPb.GetFeatureLifecycleRequest{
					ActorId:        "actor-1",
					Features:       []onbPb.Feature{onbPb.Feature_FEATURE_SA},
					WantCachedData: true,
				},
			},
			setupMocks: func(onboardingDaoMock *mockDao.MockOnboardingDao, screenerClientMock *mockScreener.MockScreenerClient, productClientMock *productMock.MockProductClient) {
				onboardingDaoMock.EXPECT().GetOnboardingDetailsByActor(gomock.Any(), "actor-1", &model.GetOnbDetailsByActorParams{UseCache: true}).
					Return(&onbPb.OnboardingDetails{
						StageMetadata: &onbPb.StageMetadata{
							SoftIntentSelectionMetadata: &onbPb.SoftIntentSelectionMetadata{
								Selection: []onbPb.OnboardingSoftIntent{
									onbPb.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI,
									onbPb.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INSTANT_SALARY,
								},
							},
							IntentSelectionMetadata: &onbPb.IntentSelectionMetadata{
								Selection: onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT,
							},
						},
						FeatureDetails: &onbPb.FeatureDetails{
							FeatureInfo: map[string]*onbPb.FeatureInfo{
								onbPb.Feature_FEATURE_SA.String(): {FeatureStatus: onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE},
							},
						},
					}, nil)
				screenerClientMock.EXPECT().GetScreenerAttemptsByActorId(gomock.Any(), &screenerPb.GetScreenerAttemptsByActorIdRequest{ActorId: "actor-1", CachedData: true}).
					Return(&screenerPb.GetScreenerAttemptsByActorIdResponse{
						Status: rpc.StatusOk(),
						ChecksMap: []*screenerPb.CheckDetails{
							{
								CheckType:   screenerPb.CheckType_CHECK_TYPE_INCOME_ESTIMATE,
								CheckResult: screenerPb.CheckResult_CHECK_RESULT_FAILED,
							},
							{
								CheckType:   screenerPb.CheckType_CHECK_TYPE_CONNECTED_ACCOUNTS,
								CheckResult: screenerPb.CheckResult_CHECK_RESULT_PASSED,
							},
						},
					}, nil)
				productClientMock.EXPECT().GetProductsStatus(gomock.Any(), &productPb.GetProductsStatusRequest{ActorId: "actor-1", ProductTypes: []productPb.ProductType{productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS, productPb.ProductType_PRODUCT_TYPE_TPAP, productPb.ProductType_PRODUCT_TYPE_WEALTH_ANALYSER}}).
					Return(&productPb.GetProductsStatusResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &onbPb.GetFeatureLifecycleResponse{
				Status: rpc.StatusOk(),
				FeatureLifecycleMap: map[string]*onbPb.FeatureLifecycle{
					onbPb.Feature_FEATURE_SA.String(): {
						Feature: onbPb.Feature_FEATURE_SA,
						IntentSelectionInfo: &onbPb.FeatureIntentSelectionInfo{
							SelectedAsHardIntent: true,
							SelectedSoftIntents:  []onbPb.OnboardingSoftIntent{onbPb.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI},
						},
						EligibilityStatus: &onbPb.FeatureEligibility{
							Status: onbPb.FeatureEligibility_STATUS_PASSED,
						},
						ActivationStatus: onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE,
					},
				},
				OnboardingDetails: &onbPb.OnboardingDetails{
					StageMetadata: &onbPb.StageMetadata{
						SoftIntentSelectionMetadata: &onbPb.SoftIntentSelectionMetadata{
							Selection: []onbPb.OnboardingSoftIntent{
								onbPb.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI,
								onbPb.OnboardingSoftIntent_ONBOARDING_SOFT_INTENT_INSTANT_SALARY,
							},
						},
						IntentSelectionMetadata: &onbPb.IntentSelectionMetadata{
							Selection: onbPb.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT,
						},
					},
					FeatureDetails: &onbPb.FeatureDetails{
						FeatureInfo: map[string]*onbPb.FeatureInfo{
							onbPb.Feature_FEATURE_SA.String(): {FeatureStatus: onbPb.FeatureStatus_FEATURE_STATUS_ACTIVE},
						},
					},
				},
			},
			wantErr: assert.NoError,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			onboardingDaoMock := mockDao.NewMockOnboardingDao(ctrl)
			screenerClientMock := mockScreener.NewMockScreenerClient(ctrl)
			productClientMock := productMock.NewMockProductClient(ctrl)

			tt.setupMocks(onboardingDaoMock, screenerClientMock, productClientMock)

			s := &Service{
				onboardingDao:  onboardingDaoMock,
				screenerClient: screenerClientMock,
				productClient:  productClientMock,
			}

			got, err := s.GetFeatureLifecycle(tt.args.ctx, tt.args.req)
			if !tt.wantErr(t, err, fmt.Sprintf("GetFeatureLifecycle(%v, %v)", tt.args.ctx, tt.args.req)) {
				return
			}
			assert.Equalf(t, tt.want, got, "GetFeatureLifecycle(%v, %v)", tt.args.ctx, tt.args.req)
		})
	}
}
