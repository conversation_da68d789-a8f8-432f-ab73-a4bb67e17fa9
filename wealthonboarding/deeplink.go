package wealthonboarding

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"golang.org/x/net/context"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feClientStatePb "github.com/epifi/gamma/api/frontend/wealthonboarding/clientstate"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/investment/wealthonboarding"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/pkg/frontend/cx"
	"github.com/epifi/gamma/wealthonboarding/helper"
)

func getDeeplinkForOnboardingCompleted(ctx context.Context, wealthFlow feClientStatePb.WealthFlow, od *woPb.OnboardingDetails) (*deeplinkPb.Deeplink, error) {
	switch wealthFlow {
	case feClientStatePb.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT:
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_CONNECTED_ACCOUNTS_SDK,
			ScreenOptions: &deeplinkPb.Deeplink_ConnectedAccountsOptions{
				ConnectedAccountsOptions: &deeplinkPb.ConnectedAccountsOptions{
					MobileNumber: od.GetMetadata().GetPersonalDetails().GetPhoneNumber(),
					Name:         od.GetMetadata().GetPersonalDetails().GetName().ToString(),
				},
			},
		}, nil
	case feClientStatePb.WealthFlow_WEALTH_FLOW_INVESTMENT:
		logger.Info(ctx, "no deeplink as this is handled by investment wrapper API")
	default:
		logger.Error(ctx, "unsupported wealth flow")
		return nil, errors.New("unsupported wealth flow")
	}
	return nil, nil
}

func (s *Service) enrichStatusScreenOptions(ctx context.Context, so *deeplinkPb.WealthOnboardingStatusScreenOptions, wealthFlow feClientStatePb.WealthFlow, actorId string) error {
	if so.GetCta() != nil {
		logger.Info(ctx, "cta already populated")
		return nil
	}
	so.Cta = &deeplinkPb.Cta{
		Text: "Okay",
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HOME,
		},
	}
	so.Flow = wealthFlow

	switch so.OnboardingAction {
	default:
		so.Title = WaitTitle
		so.Description = WaitDesc
	case deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_FAILED:
		so.Title = FailedTitle
		so.Description = FailedDesc
	case deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_RETRY:
		so.Title = RetryTitle
		so.Description = RetryDesc
	case deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_CONTACT_CUSTOMER_SUPPORT:
		so.Title = ContactCustomerSupportTitle
		so.Description = ContactCustomerSupportDesc
	case deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_FUTURE_SCOPE:
		err := s.enrichStatusScreenOptionsForFutureScope(ctx, so, wealthFlow, actorId)
		if err != nil {
			logger.Error(ctx, "error enriching status screen options for future scope", zap.Error(err))
			return err
		}
	case deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_UPDATE_APP:
		err := populateUpdateAppDataInSo(ctx, wealthFlow, so)
		if err != nil {
			logger.Error(ctx, "error while getting title and description", zap.Error(err))
			return err
		}
	}
	return nil
}

func (s *Service) enrichStatusScreenOptionsForFutureScope(ctx context.Context, so *deeplinkPb.WealthOnboardingStatusScreenOptions, wealthFlow feClientStatePb.WealthFlow, actorId string) error {
	platform, version := epificontext.AppPlatformAndVersion(ctx)
	isAppVersionSupportedForNotifyMeDeeplink := s.versionCheckForNotifyMeDeeplink(version, platform)
	if isAppVersionSupportedForNotifyMeDeeplink {
		so.GetCta().Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HOME,
		}
	}

	so.GetCta().Text = "Notify Me"
	switch wealthFlow {
	case feClientStatePb.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT:
		so.Title = ConnectedAccountFutureScopeTitle
		so.Description = ConnectedAccountFutureScopeDesc
	case feClientStatePb.WealthFlow_WEALTH_FLOW_INVESTMENT:
		so.Title = InvestmentFutureScopeTitle
		so.Description = InvestmentFutureScopeDescV2
		so.IllustrationUrl = helper.FutureScopeIllustrationURL
	default:
		logger.Error(ctx, "unsupported wealth flow")
		return errors.New("unsupported wealth flow")
	}
	return nil
}

func enrichErrorScreenOptions(ctx context.Context, so *deeplinkPb.WealthOnboardingErrorScreenOptions, wealthFlow feClientStatePb.WealthFlow) {
	so.Flow = wealthFlow
	if so.GetCta() != nil {
		logger.Info(ctx, "cta already populated for error screen")
		return
	}
	so.Cta = &deeplinkPb.Cta{
		Text: "Okay",
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HOME,
		},
	}
	switch so.OnboardingAction {
	case deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_CONTACT_CUSTOMER_SUPPORT:
		so.IllustrationUrl = helper.ManualInterventionIllustrationURL
		so.GetCta().Deeplink = cx.GetContactUsDeeplink(ctx)
		so.GetCta().Text = "Contact us"
	case deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_RETRY:
		so.IllustrationUrl = helper.TransientErrorIllustrationURL
		so.Title = TransientErrorTitle
		so.Description = TransientErrorDescription
		so.GetCta().Deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_WEALTH_ONBOARDING_LANDING_SCREEN,
		}
		so.GetCta().Text = "Retry"
	case deeplinkPb.WealthOnboardingAction_WEALTH_ONBOARDING_ACTION_FAILED:
		so.IllustrationUrl = helper.TerminalStateIllustrationURL
		so.Title = TerminalStateTitle
		so.Description = TerminalStateDescription
	default:
		logger.Info(ctx, "assigning default screen options", zap.String("onbaording_action", so.OnboardingAction.String()))
		so.IllustrationUrl = helper.TransientErrorIllustrationURL
		so.Title = TransientErrorTitle
		so.Description = TransientErrorDescription
	}
}

// TODO(Brijesh): Move to using release evaluator
func (s *Service) versionCheckForNotifyMeDeeplink(versionCode int, platform commontypes.Platform) bool {
	switch platform {
	case commontypes.Platform_ANDROID:
		return versionCode <= s.conf.NotifyMeFix.MaxAndroidVersion
	case commontypes.Platform_IOS:
		return versionCode <= s.conf.NotifyMeFix.MaxIOSVersion
	default:
		return false
	}
}

// Updates the deeplink returned by orchestrator to add appropriate info based on wealth flow and other params
// nolint:funlen
func (s *Service) updateNextStepDeeplink(ctx context.Context, dl *deeplinkPb.Deeplink, wealthFlow feClientStatePb.WealthFlow, actorId string) error {
	switch dl.GetScreen() {
	case deeplinkPb.Screen_WEALTH_ONBOARDING_STATUS_SCREEN:
		so := dl.GetWealthOnboardingStatusScreenOptions()
		err := s.enrichStatusScreenOptions(ctx, so, wealthFlow, actorId)
		if err != nil {
			return err
		}
	case deeplinkPb.Screen_WEALTH_ONBOARDING_AGREEMENT_CONFIRMATION_SCREEN:
		so := dl.GetWealthOnboardingSignAgreementScreenOptions()
		err := s.enrichTnCScreenOptions(ctx, so, wealthFlow, actorId)
		if err != nil {
			return err
		}
	case deeplinkPb.Screen_WEALTH_ONBOARDING_ERROR_SCREEN:
		so := dl.GetWealthOnboardingErrorScreenOptions()
		enrichErrorScreenOptions(ctx, so, wealthFlow)
	case deeplinkPb.Screen_WEALTH_ONBOARDING_CAPTURE_MISSING_DATA_SCREEN:
		// populating flow data
		so := dl.GetWealthOnboardingCaptureMissingDataScreenOptions()
		so.Title = "You're almost there!"
		so.Description = "Finish this step to create an account for all your investments"
		for _, md := range so.GetMissingData() {
			updateDataCollectionWithFlow(ctx, md, wealthFlow)
		}
		so.Flow = wealthFlow
		if so.GetSignDocketDeeplink().GetWealthOnboardingDocumentAadhaarEsignScreenOptions() != nil {
			so.GetSignDocketDeeplink().GetWealthOnboardingDocumentAadhaarEsignScreenOptions().Flow = wealthFlow
		}
		so.IllustrationUrl = helper.FinishLineFlagIllustrationURL
	case deeplinkPb.Screen_DIGILOCKER_DOWNLOAD:
		so := dl.GetDigilockerDownloadScreenOptions()
		// populating flow data
		so.FlowData = &deeplinkPb.DigilockerFlowData{
			Data: &deeplinkPb.DigilockerFlowData_WealthFlow{
				WealthFlow: wealthFlow,
			},
		}
	case deeplinkPb.Screen_WEALTH_ONBOARDING_LANDING_SCREEN:
		if dl.GetWealthOnboardingLandingScreenOptions() == nil {
			dl.ScreenOptions = &deeplinkPb.Deeplink_WealthOnboardingLandingScreenOptions{
				WealthOnboardingLandingScreenOptions: &deeplinkPb.WealthOnboardingLandingScreenOptions{},
			}
		}
		so := dl.GetWealthOnboardingLandingScreenOptions()
		so.Flow = wealthFlow
	case deeplinkPb.Screen_CHECK_LIVENESS:
		return nil
	case deeplinkPb.Screen_DEEP_LINK_URI_UNSPECIFIED:
		return nil
	case deeplinkPb.Screen_WEALTH_ONBOARDING_PAN_DOB_SCREEN:
		screenOptionsV2 := &wealthonboarding.WealthOnboardingPanDobScreenOptions{}
		if err := dl.GetScreenOptionsV2().UnmarshalTo(screenOptionsV2); err != nil {
			return fmt.Errorf("error while unmarshalling screen options for wo pan-dob to v2 : %w", err)
		}
		screenOptionsV2.Flow = wealthFlow
	case deeplinkPb.Screen_WEALTH_ONBOARDING_INVESTMENT_RISK_SURVEY_SCREEN:
		screenOptionsV2 := &wealthonboarding.InvestmentRiskSurveyScreenOptions{}
		if err := dl.GetScreenOptionsV2().UnmarshalTo(screenOptionsV2); err != nil {
			return fmt.Errorf("error while unmarshalling screen options for wo risk survey screen to v2 : %w", err)
		}
		screenOptionsV2.Flow = wealthFlow
	case deeplinkPb.Screen_WEALTH_ONBOARDING_INVESTMENT_RISK_PROFILE_SCREEN:
		screenOptionsV2 := &wealthonboarding.InvestmentRiskProfileScreenOptions{}
		if err := dl.GetScreenOptionsV2().UnmarshalTo(screenOptionsV2); err != nil {
			return fmt.Errorf("error while unmarshalling screen options for wo risk profile screen to v2 : %w", err)
		}
		screenOptionsV2.Flow = wealthFlow
	default:
		return errors.New(fmt.Sprintf("unhandled wealth onboarding screen: %v", dl.GetScreen().String()))
	}
	return nil
}

// sets flow param + overrides deeplink title and description for all connected-account clients and older investment clients
// other fields are set within orchestrator, ref: getTnCDeeplink
func (s *Service) enrichTnCScreenOptions(ctx context.Context, so *deeplinkPb.WealthOnboardingSignAgreementScreenOptions, wealthFlow feClientStatePb.WealthFlow, actorId string) error {
	so.Flow = wealthFlow
	switch wealthFlow {
	case feClientStatePb.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT:
		so.Title = ConnectedAccountSignAgreementTitle
		so.Description = ConnectedAccountSignAgreementDesc
	case feClientStatePb.WealthFlow_WEALTH_FLOW_INVESTMENT:

	default:
		logger.Error(ctx, "unsupported wealth flow")
		return errors.New("unsupported wealth flow")
	}
	return nil
}

func populateUpdateAppDataInSo(ctx context.Context, flow feClientStatePb.WealthFlow, so *deeplinkPb.WealthOnboardingStatusScreenOptions) error {
	var (
		appStoreLink string
		appStoreName string
	)
	platform := epificontext.AppPlatformFromContext(ctx)
	switch platform {
	case commontypes.Platform_IOS:
		appStoreName = IosStore
		appStoreLink = "https://apps.apple.com/in/app/fi-money-save-invest-smart/id1531564767"
	case commontypes.Platform_ANDROID:
		appStoreName = AndroidStore
		appStoreLink = "https://play.google.com/store/apps/details?id=com.epifi.paisa"
	default:
		return errors.New(fmt.Sprintf("unknown platform : %s", platform.String()))
	}
	so.Cta = &deeplinkPb.Cta{
		Text: "Update App",
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_EXTERNAL_REDIRECTION,
			ScreenOptions: &deeplinkPb.Deeplink_ExternalRedirectionScreenOptions{
				ExternalRedirectionScreenOptions: &deeplinkPb.ExternalRedirectionScreenOptions{
					ExternalUrl: appStoreLink,
				},
			},
		},
	}
	if so.GetTitle() == "" {
		switch flow {
		case feClientStatePb.WealthFlow_WEALTH_FLOW_INVESTMENT:
			so.Title = UpdateFiAppToStartInvestingTitle
		case feClientStatePb.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT:
			so.Title = ConnectedAccountNotSupportedTitle
		default:
			return errors.New("unsupported wealth flow")
		}
	}

	if so.GetDescription() == "" {
		switch flow {
		case feClientStatePb.WealthFlow_WEALTH_FLOW_INVESTMENT:
			so.Description = UpdateFiAppToStartInvestingDescription
		case feClientStatePb.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT:
			so.Description = fmt.Sprintf(ConnectedAccountNotSupportedDesc, appStoreName)
		default:
			return errors.New("unsupported wealth flow")
		}
	}
	return nil
}

// assigns the appropriate flow data to the missing data deeplink
func updateDataCollectionWithFlow(ctx context.Context, md *deeplinkPb.WealthOnboardingCaptureMissingDataScreenOptions_MissingDataWithStatus, flow feClientStatePb.WealthFlow) {
	mdl := md.GetMissingDataDeeplink()
	fd := &deeplinkPb.DataCollectionFlowData{
		Data: &deeplinkPb.DataCollectionFlowData_WealthFlow{
			WealthFlow: flow,
		},
	}
	switch mdl.GetScreen() {
	default:
		logger.Error(ctx, "unhandled user input collection deeplink", zap.String(logger.SCREEN, mdl.GetScreen().String()))
	case deeplinkPb.Screen_COLLECT_GENDER_SCREEN:
		if mdl.GetCollectGenderScreenOptions() != nil {
			mdl.GetCollectGenderScreenOptions().FlowData = fd
			mdl.GetCollectGenderScreenOptions().Flow = deeplinkPb.DataCollectionFlow_DATA_COLLECTION_FLOW_WEALTH_ONBOARDING
		}
	case deeplinkPb.Screen_COLLECT_MARITAL_STATUS_SCREEN:
		if mdl.GetCollectMaritalStatusScreenOptions() != nil {
			mdl.GetCollectMaritalStatusScreenOptions().FlowData = fd
			mdl.GetCollectMaritalStatusScreenOptions().Flow = deeplinkPb.DataCollectionFlow_DATA_COLLECTION_FLOW_WEALTH_ONBOARDING
		}
	case deeplinkPb.Screen_COLLECT_INCOME_SLAB_SCREEN:
		if mdl.GetCollectIncomeSlabScreenOptions() != nil {
			mdl.GetCollectIncomeSlabScreenOptions().FlowData = fd
			mdl.GetCollectIncomeSlabScreenOptions().Flow = deeplinkPb.DataCollectionFlow_DATA_COLLECTION_FLOW_WEALTH_ONBOARDING
		}
	case deeplinkPb.Screen_COLLECT_SIGNATURE_SCREEN:
		if mdl.GetCollectSignatureScreenOptions() != nil {
			mdl.GetCollectSignatureScreenOptions().FlowData = fd
			mdl.GetCollectSignatureScreenOptions().Flow = deeplinkPb.DataCollectionFlow_DATA_COLLECTION_FLOW_WEALTH_ONBOARDING
		}
	case deeplinkPb.Screen_COLLECT_PAN_SCREEN:
		if mdl.GetCollectPanScreenOptions() != nil {
			mdl.GetCollectPanScreenOptions().FlowData = fd
			mdl.GetCollectPanScreenOptions().Flow = deeplinkPb.DataCollectionFlow_DATA_COLLECTION_FLOW_WEALTH_ONBOARDING
		}
	case deeplinkPb.Screen_COLLECT_NOMINEE_DETAILS_SCREEN:
		if mdl.GetCollectPanScreenOptions() != nil {
			mdl.GetCollectPanScreenOptions().Flow = deeplinkPb.DataCollectionFlow_DATA_COLLECTION_FLOW_WEALTH_ONBOARDING
			mdl.GetCollectPanScreenOptions().FlowData = fd
		}
	case deeplinkPb.Screen_CHECK_LIVENESS:
	// do nothing as this is not a wealth onboarding owned deeplink
	case deeplinkPb.Screen_WEALTH_ONBOARDING_IA_AGREEMENT_SCREEN:
		if mdl.GetWealthOnboardingIaAgreementScreenOptions() != nil {
			mdl.GetWealthOnboardingIaAgreementScreenOptions().Flow = deeplinkPb.DataCollectionFlow_DATA_COLLECTION_FLOW_WEALTH_ONBOARDING
			mdl.GetWealthOnboardingIaAgreementScreenOptions().FlowData = fd
		}
	}
}

func getDataCollectionIntroTitleAndDesc(flow feClientStatePb.WealthFlow) (string, string) {
	title, desc := "", ""
	if flow == feClientStatePb.WealthFlow_WEALTH_FLOW_CONNECTED_ACCOUNT {
		title = "Getting you ready for \nConnected Accounts"
		desc = "To use Connected Accounts we may need some more details from you. We promise this will be quick 🤞"
	} else {
		title = "Getting you ready for \nMutual Funds"
		desc = "To allow you to invest in Mutual Funds we may need some more details from you. We promise this will be quick 🤞"
	}
	return title, desc
}
