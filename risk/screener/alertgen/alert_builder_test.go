package alertgen

import (
	"context"
	"reflect"
	"testing"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	"github.com/epifi/gamma/api/risk/enums"
	screenerPb "github.com/epifi/gamma/api/risk/screener"
	"github.com/epifi/gamma/risk/config/genconf"
)

var (
	testConf                          *genconf.Config
	testId                            = "test-id"
	testActor1                        = "test-actor-1"
	screenerAttemptWithScreenerPassed = &screenerPb.ScreenerAttempt{
		Id:       testId,
		ActorId:  testActor1,
		Criteria: enums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
		Status:   screenerPb.ScreenerStatus_SCREENER_STATUS_DONE,
		Verdict:  screenerPb.Verdict_VERDICT_PASS,
	}
	screenerAttemptWithManualReview = &screenerPb.ScreenerAttempt{
		Id:       testId,
		ActorId:  testActor1,
		Criteria: enums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING,
		Status:   screenerPb.ScreenerStatus_SCREENER_STATUS_IN_MANUAL_REVIEW,
		Verdict:  screenerPb.Verdict_VERDICT_UNSPECIFIED,
	}
	screenerAttemptWithFailed = &screenerPb.ScreenerAttempt{
		Id:       testId,
		ActorId:  testActor1,
		Criteria: enums.ScreenerCriteria_SCREENER_CRITERIA_FI_LITE_ONBOARDING,
		Status:   screenerPb.ScreenerStatus_SCREENER_STATUS_DONE,
		Verdict:  screenerPb.Verdict_VERDICT_FAIL,
	}

	rawAlertWithManualReview = &caseManagementPb.RawAlert{
		Identifier: &caseManagementPb.RuleIdentifier{
			Identifier: &caseManagementPb.RuleIdentifier_ExternalId{
				ExternalId: "ext-rule-id-2",
			},
		},
		ActorId:    screenerAttemptWithManualReview.GetActorId(),
		EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_SCREENER,
		EntityId:   screenerAttemptWithManualReview.GetId(),
	}

	rawAlertWithAutoBlock = &caseManagementPb.RawAlert{
		Identifier: &caseManagementPb.RuleIdentifier{
			Identifier: &caseManagementPb.RuleIdentifier_ExternalId{
				ExternalId: "ext-rule-id-1",
			},
		},
		ActorId:    screenerAttemptWithManualReview.GetActorId(),
		EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_SCREENER,
		EntityId:   screenerAttemptWithManualReview.GetId(),
	}
)

func TestCMAlertBuilderImpl_BuildAlertsForScreenerAttempt(t *testing.T) {
	type args struct {
		ctx     context.Context
		attempt *screenerPb.ScreenerAttempt
	}
	tests := []struct {
		name    string
		args    args
		want    []*caseManagementPb.RawAlert
		wantErr bool
	}{
		{
			name: "failed because alert creation is not required for attempt",
			args: args{
				ctx:     context.Background(),
				attempt: screenerAttemptWithScreenerPassed,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failed because rule id config not found",
			args: args{
				ctx: context.Background(),
				attempt: &screenerPb.ScreenerAttempt{
					Criteria: enums.ScreenerCriteria_SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING,
					Status:   screenerPb.ScreenerStatus_SCREENER_STATUS_DONE,
					Verdict:  screenerPb.Verdict_VERDICT_PASS,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "failed with alert creation not required err for failed screener attempt",
			args: args{
				ctx:     context.Background(),
				attempt: screenerAttemptWithFailed,
			},
			wantErr: true,
		},
		{
			name: "success with manual review case",
			args: args{
				ctx:     context.Background(),
				attempt: screenerAttemptWithManualReview,
			},
			want:    []*caseManagementPb.RawAlert{rawAlertWithManualReview},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ScreenerAttemptAlertBuilderImpl{
				conf: testConf,
			}
			got, err := c.Build(tt.args.ctx, tt.args.attempt)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildAlertsForScreenerAttempt() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !areAlertsEqual(got, tt.want) {
				t.Errorf("BuildAlertsForScreenerAttempt() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func areAlertsEqual(got []*caseManagementPb.RawAlert, want []*caseManagementPb.RawAlert) bool {
	if len(got) != len(want) {
		return false
	}
	for i := range got {
		want[i].InitiatedAt = got[i].InitiatedAt
	}
	return reflect.DeepEqual(got, want)
}
