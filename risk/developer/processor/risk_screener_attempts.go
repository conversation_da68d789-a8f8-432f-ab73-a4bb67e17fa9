package processor

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/risk/developer"
	screenerPb "github.com/epifi/gamma/api/risk/screener"
	"github.com/epifi/gamma/risk/dao"

	"google.golang.org/protobuf/encoding/protojson"
)

const (
	actorIdParam                 = "actor_id"
	limitParam                   = "limit"
	screenerAttemptMaxFetchLimit = 100
	screenerAttemptMinFetchLimit = 1
	screenerAttemptDefaultCount  = 10
)

type ScreenerAttemptProcessor struct {
	screenerAttemptsDao dao.ScreenerAttemptDao
}

func NewScreenerAttemptsProcessor(screenerAttemptsDao dao.ScreenerAttemptDao) *ScreenerAttemptProcessor {
	return &ScreenerAttemptProcessor{
		screenerAttemptsDao: screenerAttemptsDao,
	}
}

func (r *ScreenerAttemptProcessor) FetchParamList(ctx context.Context, entity developer.Entity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            actorIdParam,
			Label:           "Actor Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            limitParam,
			Label:           "Limit (max 100)",
			Type:            db_state.ParameterDataType_INTEGER,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (r *ScreenerAttemptProcessor) FetchData(ctx context.Context, entity developer.Entity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", fmt.Errorf("filters cannot be empty")
	}

	var (
		screenerAttempts []*screenerPb.ScreenerAttempt
		actorId          string
		limit            int
	)

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case actorIdParam:
			actorId = filter.GetStringValue()
		case limitParam:
			limit = int(filter.GetIntegerValue())
		default:
			return "", fmt.Errorf("unknown parameter name %s", filter.GetParameterName())
		}
	}

	if actorId == "" {
		return "", fmt.Errorf("actor id cannot be empty")
	}
	switch {
	case limit < screenerAttemptMinFetchLimit:
		limit = screenerAttemptDefaultCount
	case limit > screenerAttemptMaxFetchLimit:
		return "", fmt.Errorf("screener attempts for give actor id must be in between %v to %v", screenerAttemptMinFetchLimit, screenerAttemptMaxFetchLimit)
	}

	screenerAttempts, err := r.screenerAttemptsDao.GetByActorId(ctx, actorId, []screenerPb.ScreenerAttemptFieldMask{screenerPb.ScreenerAttemptFieldMask_SCREENER_ATTEMPT_FIELD_MASK_ALL}, limit)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return "", epifierrors.ErrRecordNotFound
		}
		return "", fmt.Errorf("failed to fetch screener attempts for actor id: %s : %w", actorId, err)
	}

	var attemptString []string

	for _, screenerAttempt := range screenerAttempts {
		marshallRes, marshallErr := protojson.Marshal(screenerAttempt)
		if marshallErr != nil {
			return "", fmt.Errorf("cannot marshall screener attempts request %w", marshallErr)
		}
		attemptString = append(attemptString, string(marshallRes))
	}
	return "[" + strings.Join(attemptString, ", ") + "]", nil
}
