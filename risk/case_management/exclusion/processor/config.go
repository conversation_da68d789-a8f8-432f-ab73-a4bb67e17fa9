package processor

import (
	"context"

	"github.com/epifi/be-common/pkg/epifierrors"
	workerConfig "github.com/epifi/gamma/risk/config/worker"
)

type ConfigProvider interface {
	GetSegmentExclusionMap(ctx context.Context) (map[string]string, error)
}

type ConfigProviderFromWorkerConfig struct {
	conf *workerConfig.Config
}

var _ ConfigProvider = &ConfigProviderFromWorkerConfig{}

func NewConfigProviderFromWorkerConfig(conf *workerConfig.Config) *ConfigProviderFromWorkerConfig {
	return &ConfigProviderFromWorkerConfig{
		conf: conf,
	}
}

func (c *ConfigProviderFromWorkerConfig) GetSegmentExclusionMap(ctx context.Context) (map[string]string, error) {
	if c.conf == nil || c.conf.ExclusionSegmentMap == nil {
		return nil, epifierrors.ErrRecordNotFound
	}
	return c.conf.ExclusionSegmentMap, nil
}
