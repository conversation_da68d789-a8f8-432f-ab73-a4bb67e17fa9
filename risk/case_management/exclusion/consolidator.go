//go:generate mockgen -source=consolidator.go -destination=mocks/mock_consolidator.go -package=mock_exclusion
package exclusion

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	cmPb "github.com/epifi/gamma/api/risk/case_management"
	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	exclusionPb "github.com/epifi/gamma/api/risk/case_management/exclusion"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/risk/case_management/essential"
)

type Consolidator interface {
	// GetConsolidatedExclusion gives the consolidated results of all the exclusions enabled for the actor.
	// It returns true if any of the exclusion returns true
	// It also provides the commentary about exclusion reasons and corresponding verdict
	// It returns epifierrors.ErrInvalidArgument when it gets invalid caseId
	GetConsolidatedExclusion(ctx context.Context, caseId string) (shouldExclude bool, excludeCommentary string,
		err error)

	// GetConsolidatedCaseReviewExclusions takes alert onto consideration and determines the case creation
	// eligibility/ review level for a case. It also gives the certain commentary on why such action was taken for same.
	GetConsolidatedCaseReviewExclusions(ctx context.Context, alert *cmPb.AlertWithRuleDetails) (reviewLevel cmEnumsPb.CaseReviewLevel,
		excludeCommentary string, err error)
}

type ConsolidatorImpl struct {
	exclusionProcessorFactory IProcessorFactory
	actorManager              essential.ActorManager
}

var _ Consolidator = &ConsolidatorImpl{}

func NewConsolidatorImpl(actorManager essential.ActorManager, exclusionProcessorFactory IProcessorFactory) *ConsolidatorImpl {
	return &ConsolidatorImpl{
		actorManager:              actorManager,
		exclusionProcessorFactory: exclusionProcessorFactory,
	}
}

func (f *ConsolidatorImpl) GetConsolidatedExclusion(ctx context.Context, caseId string) (bool, string, error) {
	if len(caseId) == 0 {
		return false, "", epifierrors.ErrInvalidArgument
	}
	actor, actorErr := f.actorManager.GetActorByCaseId(ctx, caseId)
	if actorErr != nil {
		return false, "", fmt.Errorf("error fetching the actor: %w", actorErr)
	}
	isConsolidatedExcluded := false
	finalExclusionCommentary := strings.Builder{}
	exclusionProcessorList, err := f.exclusionProcessorFactory.GetProcessor(ctx, exclusionPb.ExclusionType_EXCLUSION_TYPE_SEGMENT_EXCLUSION)
	if err != nil {
		logger.Error(ctx, "Unable to fetch processor for given exclusion type", zap.Error(err))
		return false, "", errors.Wrap(err, "unable to fetch processor for given exclusion type")
	}
	for _, exclusion := range exclusionProcessorList {
		isExcluded, excludeCommentary, err := exclusion.ShouldExclude(ctx, &exclusionPb.ExclusionRequestElement{
			Type: &exclusionPb.ExclusionRequestElement_ActorTypeParams{
				ActorTypeParams: &exclusionPb.ActorTypeParams{
					ActorId: actor.GetId(),
				},
			},
		})
		if err != nil {
			if isConsolidatedExcluded { // if actor is already excluded return true with the nil error and logging of
				// error for debugging
				logger.Error(ctx, "error fetching the exclusion for the actor", zap.String(logger.ACTOR_ID_V2,
					actor.GetId()), zap.Error(err))
				err = nil
			}
			return isConsolidatedExcluded, finalExclusionCommentary.String(), err
		}
		if isExcluded {
			isConsolidatedExcluded = true
		}
		finalExclusionCommentary.WriteString(excludeCommentary)
		finalExclusionCommentary.WriteString("<br>")
	}
	return isConsolidatedExcluded, finalExclusionCommentary.String(), nil
}

func (f *ConsolidatorImpl) GetConsolidatedCaseReviewExclusions(ctx context.Context, alert *cmPb.AlertWithRuleDetails) (cmEnumsPb.CaseReviewLevel, string, error) {
	if alert == nil {
		return cmEnumsPb.CaseReviewLevel_CASE_ACTION_LEVEL_UNSPECIFIED, "", epifierrors.ErrInvalidArgument
	}

	isConsolidatedExcluded := false
	finalExclusionCommentary := strings.Builder{}

	// TODO: https://monorail.pointz.in/p/fi-app/issues/detail?id=60271
	// Return early if not required to check for exclusion
	shouldCheckForExclusion, err := f.shouldCheckForExclusion(alert)
	switch {
	case err != nil:
		return cmEnumsPb.CaseReviewLevel_CASE_ACTION_LEVEL_UNSPECIFIED, "",
			fmt.Errorf("failed to find whether exclusion is required %w", err)
	case !shouldCheckForExclusion:
		return cmEnumsPb.CaseReviewLevel_CASE_ACTION_LEVEL_UNSPECIFIED, "", nil
	}

	exclusionProcessorList, err := f.exclusionProcessorFactory.GetProcessor(ctx, exclusionPb.ExclusionType_EXCLUSION_TYPE_ACCOUNT_STATUS_REVIEW_EXCLUSION)
	if err != nil {
		logger.Error(ctx, "Unable to fetch processor for given exclusion type", zap.Error(err))
		return cmEnumsPb.CaseReviewLevel_CASE_ACTION_LEVEL_UNSPECIFIED, "", err
	}
	for _, exclusion := range exclusionProcessorList {
		isExcluded, excludeCommentary, err := exclusion.ShouldExclude(ctx, &exclusionPb.ExclusionRequestElement{
			Type: &exclusionPb.ExclusionRequestElement_AlertTypeParams{
				AlertTypeParams: &exclusionPb.AlertTypeParams{
					Alerts: alert,
				},
			},
		})
		if err != nil {
			if isConsolidatedExcluded {
				// if actor is already excluded, log error and continue
				logger.Error(ctx, "error fetching the exclusion for the actor", zap.String(logger.ACTOR_ID_V2,
					alert.GetAlert().GetActorId()), zap.Error(err))
				// nolint:ineffassign
				err = nil
			}
			continue
		}
		if isExcluded {
			isConsolidatedExcluded = true
		}
		finalExclusionCommentary.WriteString(excludeCommentary)
		finalExclusionCommentary.WriteString("<br>")
	}
	// currently all the exclusions will result in for won't review case
	if isConsolidatedExcluded {
		return cmEnumsPb.CaseReviewLevel_CASE_ACTION_LEVEL_CREATE_WONT_REVIEW_CASE, finalExclusionCommentary.String(), nil
	}
	return cmEnumsPb.CaseReviewLevel_CASE_ACTION_LEVEL_UNSPECIFIED, finalExclusionCommentary.String(), nil
}

func (f *ConsolidatorImpl) shouldCheckForExclusion(alert *cmPb.AlertWithRuleDetails) (bool, error) {
	// Under current impl, exclusion will apply only for transaction review type cases
	reviewType, err := alert.GetReviewType()
	if err != nil {
		return false, fmt.Errorf("failed to fetch review type for alert %w", err)
	}

	if reviewType == reviewPb.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW {
		return true, nil
	}

	return false, nil
}
