// Code generated by MockGen. DO NOT EDIT.
// Source: alert_with_rule.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	case_management "github.com/epifi/gamma/api/risk/case_management"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	gomock "github.com/golang/mock/gomock"
)

// MockAlertWithRuleManager is a mock of AlertWithRuleManager interface.
type MockAlertWithRuleManager struct {
	ctrl     *gomock.Controller
	recorder *MockAlertWithRuleManagerMockRecorder
}

// MockAlertWithRuleManagerMockRecorder is the mock recorder for MockAlertWithRuleManager.
type MockAlertWithRuleManagerMockRecorder struct {
	mock *MockAlertWithRuleManager
}

// NewMockAlertWithRuleManager creates a new mock instance.
func NewMockAlertWithRuleManager(ctrl *gomock.Controller) *MockAlertWithRuleManager {
	mock := &MockAlertWithRuleManager{ctrl: ctrl}
	mock.recorder = &MockAlertWithRuleManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAlertWithRuleManager) EXPECT() *MockAlertWithRuleManagerMockRecorder {
	return m.recorder
}

// GetByActorId mocks base method.
func (m *MockAlertWithRuleManager) GetByActorId(ctx context.Context, actorId string, selectMasks []case_management.AlertFieldMask, limit int, options ...storagev2.FilterOption) ([]*case_management.AlertWithRuleDetails, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, actorId, selectMasks, limit}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorId", varargs...)
	ret0, _ := ret[0].([]*case_management.AlertWithRuleDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockAlertWithRuleManagerMockRecorder) GetByActorId(ctx, actorId, selectMasks, limit interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, actorId, selectMasks, limit}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockAlertWithRuleManager)(nil).GetByActorId), varargs...)
}
