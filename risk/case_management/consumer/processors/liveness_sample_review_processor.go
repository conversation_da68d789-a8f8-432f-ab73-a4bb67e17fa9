package processors

import (
	"context"

	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	persistentQueuePb "github.com/epifi/gamma/api/persistentqueue"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/risk/case_management/helper"

	"github.com/pkg/errors"
)

type LivenessSampleReviewProcessor struct {
	livenessClient livenessPb.LivenessClient
	pqHelper       helper.IPersistentQueueHelper
}

func NewLivenessSampleReviewProcessor(livenessClient livenessPb.LivenessClient, pqHelper helper.IPersistentQueueHelper) *LivenessSampleReviewProcessor {
	return &LivenessSampleReviewProcessor{
		livenessClient: livenessClient,
		pqHelper:       pqHelper,
	}
}

func (l *LivenessSampleReviewProcessor) ProcessPayload(ctx context.Context, payloadType caseManagementPb.PayloadType, payload interface{}) error {
	// validate if payload is of liveness type
	lsReviewPayload, ok := payload.(*caseManagementPb.RiskCase_LivenessSampleReview)
	if !ok || lsReviewPayload == nil {
		return errors.New("invalid payload received for processing liveness review")
	}

	latestAttempt, err := l.getLivenessAttemptDetails(ctx, lsReviewPayload.LivenessSampleReview)
	if err != nil {
		return errors.Wrap(err, "error while getting latest onboarding liveness attempt for user")
	}

	pqPayloadType, err := l.pqHelper.ConvertToPQPayloadType(ctx, payloadType)
	if err != nil {
		return errors.Wrap(err, "error while converting to persistent queue payload type")
	}
	pqLivenessReviewPayload := buildPersistentQueueLivenessSampleReviewPayload(lsReviewPayload.LivenessSampleReview, latestAttempt)

	err = l.pqHelper.PushToQueue(ctx, lsReviewPayload.LivenessSampleReview.GetActorId(), pqPayloadType,
		&persistentQueuePb.Payload{LivenessSamplePayload: pqLivenessReviewPayload})
	if err != nil {
		return errors.Wrap(err, "error while pushing review payload to persistent queue")
	}
	return nil
}

func buildPersistentQueueLivenessSampleReviewPayload(payload *caseManagementPb.LivenessSampleReview, attempt *livenessPb.LivenessAttempt) *persistentQueuePb.LivenessSampleReview {
	return &persistentQueuePb.LivenessSampleReview{
		ActorId:       payload.GetActorId(),
		RequestId:     attempt.GetRequestId(),
		VideoLocation: attempt.GetVideoLocation(),
		CreatedAt:     attempt.GetCreatedAt(),
		SubSample:     payload.GetSubSample(),
	}
}

// getLivenessAttemptDetails will call the liveness service to fetch liveness attempt details for given request id
func (l *LivenessSampleReviewProcessor) getLivenessAttemptDetails(ctx context.Context, livenessSampleReviewPayload *caseManagementPb.LivenessSampleReview) (*livenessPb.LivenessAttempt, error) {
	// if request id is not present we will return the latest liveness attempt
	if livenessSampleReviewPayload.GetRequestId() == "" {
		return getLatestOnboardingLivenessAttempt(ctx, l.livenessClient, livenessSampleReviewPayload.GetActorId())
	}
	// get liveness attempt data for given request id
	attemptResp, attemptErr := l.livenessClient.GetLivenessAttempt(ctx, &livenessPb.GetLivenessAttemptRequest{
		LivenessReqId: livenessSampleReviewPayload.GetRequestId(),
	})
	if attemptErr = epifigrpc.RPCError(attemptResp, attemptErr); attemptErr != nil {
		return nil, errors.Wrap(attemptErr, "error while fetching liveness attempts data for the user")
	}
	return attemptResp.GetLivenessAttempt(), nil
}
