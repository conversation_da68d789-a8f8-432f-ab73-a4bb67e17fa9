package processors

import (
	"context"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
)

type Failure struct {
	Payload interface{}
	BatchId string
	Err     error
}

type Input struct {
	Payload        interface{}
	BatchId        string
	RuleIdentifier *caseManagementPb.RuleIdentifier
	InitiatedAt    *timestamp.Timestamp
}

func (i *Input) GetPayload() interface{} {
	if i == nil {
		return nil
	}
	return i.Payload
}

func (i *Input) GetBatchId() string {
	if i == nil {
		return ""
	}
	return i.BatchId
}

func (i *Input) GetRuleIdentifier() *caseManagementPb.RuleIdentifier {
	if i == nil {
		return nil
	}
	return i.RuleIdentifier
}

func (i *Input) GetInitiatedAt() *timestamp.Timestamp {
	if i == nil {
		return nil
	}
	return i.InitiatedAt
}

func (f *Failure) GetPayload() interface{} {
	if f == nil {
		return nil
	}
	return f.Payload
}

func (f *Failure) GetErr() error {
	if f == nil {
		return nil
	}
	return f.Err
}

func (f *Failure) GetBatchId() string {
	if f == nil {
		return ""
	}
	return f.BatchId
}

type PayloadProcessor interface {
	// TODO https://monorail.pointz.in/p/fi-app/issues/detail?id=41982
	ProcessPayload(ctx context.Context, payloadType caseManagementPb.PayloadType, payload interface{}) error
}

type PayloadBatchProcessor interface {
	ProcessPayload(context.Context, caseManagementPb.PayloadType, []*Input) []*Failure
}
