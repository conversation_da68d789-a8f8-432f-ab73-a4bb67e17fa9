package activity

import (
	"context"

	"github.com/pkg/errors"

	activityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func (p *Processor) GetCase(ctx context.Context, req *activityPb.GetCaseRequest) (
	*activityPb.GetCaseResponse, error) {
	caseObj, err := p.caseStore.GetCaseById(ctx, req.GetCaseId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound) ||
		errors.Is(err, epifierrors.ErrInvalidArgument):
		return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
	case err != nil:
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	return &activityPb.GetCaseResponse{
		Case: caseObj,
	}, nil
}
