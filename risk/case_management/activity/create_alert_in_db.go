package activity

import (
	"context"

	"github.com/pkg/errors"

	caseManagementActivityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func (p *Processor) CreateAlertInDB(ctx context.Context, req *caseManagementActivityPb.CreateAlertInDBRequest) (*caseManagementActivityPb.CreateAlertInDBResponse, error) {
	alert, err := p.alertDao.Create(ctx, req.GetAlert())
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
	case err != nil:
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	return &caseManagementActivityPb.CreateAlertInDBResponse{
		Alert: alert,
	}, nil
}
