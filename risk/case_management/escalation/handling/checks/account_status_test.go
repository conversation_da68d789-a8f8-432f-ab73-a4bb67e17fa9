package checks

import (
	"context"
	"errors"
	"testing"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	accountEnumsPb "github.com/epifi/gamma/api/accounts/enums"
	escalationPb "github.com/epifi/gamma/api/risk/case_management/escalation"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"
	enumsPb "github.com/epifi/gamma/api/risk/enums"
	accountTypesPb "github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/risk/accountstatus"
	"github.com/epifi/gamma/risk/case_management/dao/mocks"
	accountStatusMocks "github.com/epifi/gamma/risk/test/mocks/accountstatus"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
)

func TestAccountStatusChecker_Check(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAccountStatusFetcher := accountStatusMocks.NewMockFetcher(ctrl)
	mockReviewActionDao := mocks.NewMockReviewAction(ctrl)
	checker := NewAccountStatusChecker(mockAccountStatusFetcher, mockReviewActionDao)

	ctx := context.Background()
	event := &escalationPb.EscalationEvent{ActorId: "actor1"}
	logger.Init("test1")

	tests := []struct {
		name       string
		setupMocks func()
		want       *escalationPb.HandlingParams
		wantErr    bool
	}{
		{
			name: "account closed",
			setupMocks: func() {
				mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(ctx, event.GetActorId(), enumsPb.DataFreshness_DATA_FRESHNESS_LAST_KNOWN, accountTypesPb.AccountProductOffering_APO_REGULAR).
					Return(&accountstatus.OperationalDetails{IsAccountClosed: true}, nil)
			},
			want:    buildDropCaseHandling(escalationPb.DropEscalationOptions_REASON_ACCOUNT_CLOSED),
			wantErr: false,
		},
		{
			name: "fetch account status error",
			setupMocks: func() {
				mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(ctx, event.GetActorId(), enumsPb.DataFreshness_DATA_FRESHNESS_LAST_KNOWN, accountTypesPb.AccountProductOffering_APO_REGULAR).
					Return(nil, errors.New("fetch error"))
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "account frozen successful case creation",
			setupMocks: func() {
				mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(ctx, event.GetActorId(), enumsPb.DataFreshness_DATA_FRESHNESS_LAST_KNOWN, accountTypesPb.AccountProductOffering_APO_REGULAR).
					Return(&accountstatus.OperationalDetails{FreezeStatus: accountEnumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE}, nil)
				mockReviewActionDao.EXPECT().GetByActorId(ctx, event.GetActorId(), gomock.Any(), 1).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    buildCreateCaseHandling(reviewPb.Status_STATUS_CREATED),
			wantErr: false,
		},
		{
			name: "unfreeze action in progress",
			setupMocks: func() {
				mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(ctx, event.GetActorId(), enumsPb.DataFreshness_DATA_FRESHNESS_LAST_KNOWN, accountTypesPb.AccountProductOffering_APO_REGULAR).
					Return(&accountstatus.OperationalDetails{FreezeStatus: accountEnumsPb.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE}, nil)
				mockReviewActionDao.EXPECT().GetByActorId(ctx, event.GetActorId(), gomock.Any(), 1).
					Return([]*reviewPb.Action{{ActionType: reviewPb.ActionType_ACTION_TYPE_UNFREEZE_ACCOUNT, Status: reviewPb.ActionStatus_ACTION_STATUS_UNSPECIFIED}}, nil)
			},
			want:    buildDropCaseHandling(escalationPb.DropEscalationOptions_REASON_UNFREEZE_ACTION_IN_PROGRESS),
			wantErr: false,
		},
		{
			name: "previous escalation rejected",
			setupMocks: func() {
				mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(ctx, event.GetActorId(), enumsPb.DataFreshness_DATA_FRESHNESS_LAST_KNOWN, accountTypesPb.AccountProductOffering_APO_REGULAR).
					Return(&accountstatus.OperationalDetails{FreezeStatus: accountEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED}, nil)
				mockReviewActionDao.EXPECT().GetByActorId(ctx, event.GetActorId(), gomock.Any(), 1).
					Return([]*reviewPb.Action{{ActionType: reviewPb.ActionType_ACTION_TYPE_REJECT_ESCALATION, Status: reviewPb.ActionStatus_ACTION_STATUS_SUCCESS}}, nil)
			},
			want:    buildDropCaseHandling(escalationPb.DropEscalationOptions_REASON_PREVIOUS_ESCALATION_REJECTED),
			wantErr: false,
		},
		{
			name: "account unfrozen",
			setupMocks: func() {
				mockAccountStatusFetcher.EXPECT().FetchAccountStatusForActor(ctx, event.GetActorId(), enumsPb.DataFreshness_DATA_FRESHNESS_LAST_KNOWN, accountTypesPb.AccountProductOffering_APO_REGULAR).
					Return(&accountstatus.OperationalDetails{FreezeStatus: accountEnumsPb.FreezeStatus_FREEZE_STATUS_UNSPECIFIED}, nil)
				mockReviewActionDao.EXPECT().GetByActorId(ctx, event.GetActorId(), gomock.Any(), 1).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:    buildDropCaseHandling(escalationPb.DropEscalationOptions_REASON_ACCOUNT_UNFROZEN),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()
			got, err := checker.Check(ctx, event)
			if (err != nil) != tt.wantErr {
				t.Errorf("Check() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.Equal(t, tt.want, got)
		})
	}
}
