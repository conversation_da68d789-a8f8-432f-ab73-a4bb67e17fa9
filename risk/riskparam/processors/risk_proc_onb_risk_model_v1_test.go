package processors

import (
	"context"
	"testing"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"
)

func Test_validateInputForV1(t *testing.T) {
	logger.Init(cfg.TestEnv)
	type args struct {
		metadata map[string]string
	}
	tests := []struct {
		name                  string
		args                  args
		wantGmailPanNameScore float64
		wantErr               string
	}{
		{
			name:                  "info present & missing info & invalid value",
			wantGmailPanNameScore: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotGmailPanNameScore, err := validateInputForV1(context.Background(), tt.args.metadata)
			if (err != nil) != (tt.wantErr != "") {
				t.Errorf("validateInput() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if err.Error() != tt.wantErr {
					t.Errorf("validateInput() error = %v, wantErr %v", err, tt.wantErr)
					return
				}
			}

			if gotGmailPanNameScore != tt.wantGmailPanNameScore {
				t.Errorf("validateInput() gotGmailPanNameScore = %v, want %v", gotGmailPanNameScore, tt.wantGmailPanNameScore)
			}
		})
	}
}
