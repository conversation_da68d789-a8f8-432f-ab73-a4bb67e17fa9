package processors

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/mocks"
	"github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/risk/riskparam"
)

func TestOnboardingEKYCNumberMismatch_ProcessRisk(t *testing.T) {
	type args struct {
		req *riskparam.ProcessorRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *riskparam.ProcessorResponse
		mock    func(mock *mockStruct, args args)
		wantErr bool
	}{
		{
			name: "Number Mismatch",
			args: args{
				req: &riskparam.ProcessorRequest{
					ActorId: "actor-1",
				},
			},
			want: &riskparam.ProcessorResponse{
				RiskData: &risk.RiskData{
					ActorId:   "actor-1",
					RiskParam: risk.RiskParam_RISK_PARAM_EKYC_ONBOARDING_NUMBER_MISMATCH,
					Result:    risk.Result_RESULT_FAIL,
					Score:     float32(0.5),
				},
			},
			mock: func(mock *mockStruct, args args) {
				mock.mockKycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId:        args.req.ActorId,
					IgnoreLiveness: true,
				}).Return(&kyc.CheckKYCStatusResponse{
					Status: rpc.StatusOk(),
					RequestParams: &kyc.KYCAttemptRequestParams{
						EkycNumberMismatch: &kyc.EKYCNumberMismatch{
							IsNumberMismatch: commontypes.BooleanEnum_TRUE,
						},
					},
				}, nil)
			},
			wantErr: false,
		},
		{
			name: "Number matched",
			args: args{
				req: &riskparam.ProcessorRequest{
					ActorId: "actor-1",
				},
			},
			want: &riskparam.ProcessorResponse{
				RiskData: &risk.RiskData{
					ActorId:   "actor-1",
					RiskParam: risk.RiskParam_RISK_PARAM_EKYC_ONBOARDING_NUMBER_MISMATCH,
					Result:    risk.Result_RESULT_PASS,
					Score:     0,
				},
			},
			mock: func(mock *mockStruct, args args) {
				mock.mockKycClient.EXPECT().CheckKYCStatus(gomock.Any(), &kyc.CheckKYCStatusRequest{
					ActorId:        args.req.ActorId,
					IgnoreLiveness: true,
				}).Return(&kyc.CheckKYCStatusResponse{
					Status: rpc.StatusOk(),
					RequestParams: &kyc.KYCAttemptRequestParams{
						EkycNumberMismatch: &kyc.EKYCNumberMismatch{
							IsNumberMismatch: commontypes.BooleanEnum_FALSE,
						},
					},
				}, nil)
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			mockKycClient := mocks.NewMockKycClient(ctrl)
			r := &OnboardingEKYCNumberMismatch{
				kycClient: mockKycClient,
			}
			tt.mock(&mockStruct{
				mockKycClient: mockKycClient,
			}, tt.args)
			got, err := r.ProcessRisk(context.Background(), tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRisk() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessRisk() got = %v, want %v", got, tt.want)
			}
		})
	}
}
