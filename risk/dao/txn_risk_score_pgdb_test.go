package dao

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/gamma/api/risk"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"
)

var (
	trskdtsPGDB TxnRiskScoreDaoTestSuite
)

func TestTxnRiskScoreDaoPGDB_Create(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, trskdtsPGDB.db, trskdtsPGDB.conf.FRMPgdb.GetName(), riskEvaluatorEntityTestTables)
	type args struct {
		ctx          context.Context
		txnRiskScore *risk.TxnRiskScore
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    *risk.TxnRiskScore
	}{
		{
			name: "Successfully insert value",
			args: args{
				ctx:          context.Background(),
				txnRiskScore: trs1,
			},
			want:    trs1,
			wantErr: false,
		},
		{
			name: "give error for blank txn id",
			args: args{
				ctx:          context.Background(),
				txnRiskScore: trs2,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "give error for blank vendor",
			args: args{
				ctx:          context.Background(),
				txnRiskScore: trs3,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := trskdtsPGDB.txnRiskScore.Create(tt.args.ctx, tt.args.txnRiskScore)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&risk.TxnRiskScore{}, "id", "created_at", "updated_at", "deleted_at"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("Create() value is mismatch got: %v\nwant: %v", got, tt.want)
			}
		})
	}
}
