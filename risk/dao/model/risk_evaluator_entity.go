package model

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/risk/enums"
)

type RiskEvaluatorEntity struct {
	Id         string `gorm:"type:uuid;default:gen_random_uuid();primaryKey"`
	EntityType string `gorm:"primaryKey"`
	EntityId   string `gorm:"primaryKey"`
	Vendor     string
	CreatedAt  time.Time
	UpdatedAt  time.Time
	DeletedAt  *time.Time
}

func NewRiskEvaluatorEntity(riskEvaluatorEntity *risk.RiskEvaluatorEntity) *RiskEvaluatorEntity {
	return &RiskEvaluatorEntity{
		Id:         riskEvaluatorEntity.GetId(),
		EntityType: riskEvaluatorEntity.GetEntity().GetType().String(),
		EntityId:   riskEvaluatorEntity.GetEntity().GetId(),
		Vendor:     riskEvaluatorEntity.GetVendor().String(),
	}
}

func (ree *RiskEvaluatorEntity) ToProto() *risk.RiskEvaluatorEntity {
	if ree == nil {
		return nil
	}
	var deletedAt *timestamppb.Timestamp
	if ree.DeletedAt != nil {
		deletedAt = timestamppb.New(*ree.DeletedAt)
	}
	proto := &risk.RiskEvaluatorEntity{
		Id: ree.Id,
		Entity: &risk.RiskEntity{
			Type: enums.EntityType(enums.EntityType_value[ree.EntityType]),
			Id:   ree.EntityId,
		},
		Vendor:    commonvgpb.Vendor(commonvgpb.Vendor_value[ree.Vendor]),
		CreatedAt: timestamppb.New(ree.CreatedAt),
		UpdatedAt: timestamppb.New(ree.UpdatedAt),
		DeletedAt: deletedAt,
	}
	return proto
}
