package workflow_test

// nolint:testifylint
import (
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"go.temporal.io/sdk/testsuite"

	activityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	riskEnums "github.com/epifi/gamma/api/risk/enums"
	workflowPb "github.com/epifi/gamma/api/risk/workflow"
	riskActivity "github.com/epifi/gamma/risk/activity"
	riskWorkflow "github.com/epifi/gamma/risk/workflow"
)

// setupTestEnvironment sets up the test environment with all necessary registrations
func setupTestEnvironment() *testsuite.TestWorkflowEnvironment {
	testSuite := &testsuite.WorkflowTestSuite{}
	env := testSuite.NewTestWorkflowEnvironment()

	// Register activities and workflows
	env.RegisterActivity(&riskActivity.Processor{})
	env.RegisterWorkflow(riskWorkflow.CaseReprioritisation)
	env.RegisterWorkflow(riskWorkflow.GetScoresForCases)
	env.RegisterWorkflow(riskWorkflow.BulkUpdateWorkflow)

	return env
}

const (
	DS_MODEL_1_NAME = "DS_PRECISION_v1"
	DS_MODEL_2_NAME = "DS_PRECISION_v2"
	DEFAULT_MODEL2  = "default_model_name_2"
)

func TestCaseReprioritisation_SuccessWithBothModels(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
		Filters: &workflowPb.CaseFetchFilters{
			UnassignedOnly: true,
			Status:         []int32{int32(activityPb.Status_STATUS_CREATED)},
		},
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/tickets.json",
		}, nil)

	// Mock GetScoresForCases child workflow with mixed model data
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: []*workflowPb.TicketWithScore{
				{
					TicketId:    "ticket1",
					Model1Score: 85,
					Model1Name:  DS_MODEL_1_NAME,
					Model2Score: 90,
					Model2Name:  DS_MODEL_2_NAME,
				},
				{
					TicketId:    "ticket2",
					Model1Score: 85,
					Model1Name:  DS_MODEL_1_NAME,
					Model2Score: 75,
					Model2Name:  DS_MODEL_2_NAME,
				},
				{
					TicketId:    "ticket3",
					Model1Score: 70,
					Model1Name:  DS_MODEL_1_NAME,
					Model2Score: 90,
					Model2Name:  DS_MODEL_2_NAME,
				},
			},
			ErroredTicketIds: []string{},
		}, nil)

	// Mock BulkUpdateWorkflow for Model1 score groups with WF prefix verification
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model1Score == 85 && req.Model1Name == DS_MODEL_1_NAME && len(req.TicketIds) == 2
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job1",
		SuccessfulTicketIds: []string{"ticket1", "ticket2"},
		ErroredTicketIds:    []string{},
	}, nil)

	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model1Score == 70 && req.Model1Name == DS_MODEL_1_NAME && len(req.TicketIds) == 1
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job2",
		SuccessfulTicketIds: []string{"ticket3"},
		ErroredTicketIds:    []string{},
	}, nil)

	// Mock BulkUpdateWorkflow for Model2 score groups
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Score == 90 && req.Model2Name == DS_MODEL_2_NAME && len(req.TicketIds) == 2
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job3",
		SuccessfulTicketIds: []string{"ticket1", "ticket3"},
		ErroredTicketIds:    []string{},
	}, nil)

	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Score == 75 && req.Model2Name == DS_MODEL_2_NAME && len(req.TicketIds) == 1
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job4",
		SuccessfulTicketIds: []string{"ticket2"},
		ErroredTicketIds:    []string{},
	}, nil)

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_BatchingLargeDataset(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/large_tickets.json",
		}, nil)

	// Create 150 tickets with same model1 score (should create 2 batches: 100 + 50)
	tickets := make([]*workflowPb.TicketWithScore, 150)
	for i := 0; i < 150; i++ {
		tickets[i] = &workflowPb.TicketWithScore{
			TicketId:    fmt.Sprintf("ticket%d", i+1),
			Model1Score: 85,
			Model1Name:  DS_MODEL_1_NAME,
			Model2Score: 90,
			Model2Name:  DS_MODEL_2_NAME,
		}
	}

	// Mock GetScoresForCases child workflow with large dataset
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: tickets,
			ErroredTicketIds:  []string{},
		}, nil)

	// Mock first batch (100 tickets) for Model1 with WF prefix workflow ID
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model1Score == 85 && req.Model1Name == DS_MODEL_1_NAME && len(req.TicketIds) == 100 && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_1_SCORE) && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_1_NAME)
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job1",
		SuccessfulTicketIds: make([]string, 100),
		ErroredTicketIds:    []string{},
	}, nil)

	// Mock second batch (50 tickets) for Model1
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model1Score == 85 && req.Model1Name == DS_MODEL_1_NAME && len(req.TicketIds) == 50 && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_1_SCORE) && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_1_NAME)
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job2",
		SuccessfulTicketIds: make([]string, 50),
		ErroredTicketIds:    []string{},
	}, nil)

	// Mock first batch (100 tickets) for Model2
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Score == 90 && req.Model2Name == DS_MODEL_2_NAME && len(req.TicketIds) == 100 && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_SCORE) && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_NAME)
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job3",
		SuccessfulTicketIds: make([]string, 100),
		ErroredTicketIds:    []string{},
	}, nil)

	// Mock second batch (50 tickets) for Model2
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Score == 90 && req.Model2Name == DS_MODEL_2_NAME && len(req.TicketIds) == 50 && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_SCORE) && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_NAME)
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job4",
		SuccessfulTicketIds: make([]string, 50),
		ErroredTicketIds:    []string{},
	}, nil)

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_BatchingEdgeCases(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/edge_case_tickets.json",
		}, nil)

	// Create exactly 201 tickets (should create 3 batches: 100 + 100 + 1)
	tickets := make([]*workflowPb.TicketWithScore, 201)
	for i := 0; i < 201; i++ {
		tickets[i] = &workflowPb.TicketWithScore{
			TicketId:    fmt.Sprintf("ticket%d", i+1),
			Model1Score: 75,
			Model1Name:  DS_MODEL_1_NAME,
		}
	}

	// Mock GetScoresForCases child workflow
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: tickets,
			ErroredTicketIds:  []string{},
		}, nil)

	// Mock first batch (100 tickets)
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model1Score == 75 && len(req.TicketIds) == 100
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job1",
		SuccessfulTicketIds: make([]string, 100),
		ErroredTicketIds:    []string{},
	}, nil)

	// Mock second batch (100 tickets)
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model1Score == 75 && len(req.TicketIds) == 100
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job2",
		SuccessfulTicketIds: make([]string, 100),
		ErroredTicketIds:    []string{},
	}, nil)

	// Mock third batch (1 ticket)
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model1Score == 75 && len(req.TicketIds) == 1
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job3",
		SuccessfulTicketIds: []string{"ticket201"},
		ErroredTicketIds:    []string{},
	}, nil)

	// Mock Model2 batches (all tickets get default Model2 score 0 and model name DEFAULT_MODEL2)
	// Mock first batch (100 tickets) for Model2
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Name == DEFAULT_MODEL2 && req.Model2Score == 0 && len(req.TicketIds) == 100
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job4",
		SuccessfulTicketIds: make([]string, 100),
		ErroredTicketIds:    []string{},
	}, nil)

	// Mock second batch (100 tickets) for Model2
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Name == DEFAULT_MODEL2 && req.Model2Score == 0 && len(req.TicketIds) == 100
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job5",
		SuccessfulTicketIds: make([]string, 100),
		ErroredTicketIds:    []string{},
	}, nil)

	// Mock third batch (1 ticket) for Model2
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Name == DEFAULT_MODEL2 && req.Model2Score == 0 && len(req.TicketIds) == 1
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job6",
		SuccessfulTicketIds: []string{"ticket201"},
		ErroredTicketIds:    []string{},
	}, nil)

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_DryRunMode(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         true,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/tickets.json",
		}, nil)

	// Mock GetScoresForCases child workflow
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: []*workflowPb.TicketWithScore{
				{
					TicketId:    "ticket1",
					Model1Score: 85,
					Model1Name:  DS_MODEL_1_NAME,
				},
			},
			ErroredTicketIds: []string{},
		}, nil)

	// No BulkUpdateWorkflow calls should be made in dry run mode

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_FreshdeskError(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity with error
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(nil, errors.New("CRM service unavailable"))

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify error
	assert.True(t, env.IsWorkflowCompleted())
	require.Error(t, env.GetWorkflowError())
	assert.Contains(t, env.GetWorkflowError().Error(), "GetAllFreshdeskTickets activity failed")

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_GetScoresError(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/tickets.json",
		}, nil)

	// Mock GetScoresForCases child workflow with error
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(nil, errors.New("ML service timeout"))

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify error
	assert.True(t, env.IsWorkflowCompleted())
	require.Error(t, env.GetWorkflowError())
	assert.Contains(t, env.GetWorkflowError().Error(), "GetScoresForCases workflow failed")

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_PartialBulkUpdateFailure(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/tickets.json",
		}, nil)

	// Mock GetScoresForCases child workflow
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: []*workflowPb.TicketWithScore{
				{
					TicketId:    "ticket1",
					Model1Score: 85,
					Model1Name:  DS_MODEL_1_NAME,
				},
			},
			ErroredTicketIds: []string{"failed_ticket"}, // Some tickets failed scoring
		}, nil)

	// Mock BulkUpdateWorkflow with partial success
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.Anything).
		Return(&workflowPb.BulkUpdateWorkflowResponse{
			JobId:               "job1",
			SuccessfulTicketIds: []string{},
			ErroredTicketIds:    []string{"ticket1"}, // Bulk update failed
		}, nil)

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion (workflow should complete despite partial failures)
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_EmptyTicketList(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/empty_tickets.json",
		}, nil)

	// Mock GetScoresForCases child workflow with empty response
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: []*workflowPb.TicketWithScore{},
			ErroredTicketIds:  []string{},
		}, nil)

	// No BulkUpdateWorkflow calls should be made for empty data

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_BulkUpdateWorkflowFailure(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/tickets.json",
		}, nil)

	// Mock GetScoresForCases child workflow
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: []*workflowPb.TicketWithScore{
				{
					TicketId:    "ticket1",
					Model1Score: 85,
					Model1Name:  DS_MODEL_1_NAME,
				},
			},
			ErroredTicketIds: []string{},
		}, nil)

	// Mock BulkUpdateWorkflow with failure
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.Anything).
		Return(nil, errors.New("bulk update service unavailable"))

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion (workflow continues despite bulk update failures)
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_MixedScoreGrouping(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/tickets.json",
		}, nil)

	// Mock GetScoresForCases child workflow with varied scores
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: []*workflowPb.TicketWithScore{
				{
					TicketId:    "ticket1",
					Model1Score: 85,
					Model1Name:  DS_MODEL_1_NAME,
					Model2Score: 80,
					Model2Name:  DS_MODEL_2_NAME,
				},
				{
					TicketId:    "ticket2",
					Model1Score: 85,
					Model1Name:  DS_MODEL_1_NAME,
					Model2Score: 90,
					Model2Name:  DS_MODEL_2_NAME,
				},
				{
					TicketId:    "ticket3",
					Model1Score: 70,
					Model1Name:  DS_MODEL_1_NAME,
					Model2Score: 80,
					Model2Name:  DS_MODEL_2_NAME,
				},
				{
					TicketId:    "ticket4",
					Model1Score: 70,
					Model1Name:  DS_MODEL_1_NAME,
					Model2Score: 60,
					Model2Name:  DS_MODEL_2_NAME,
				},
			},
			ErroredTicketIds: []string{},
		}, nil)

	// Mock BulkUpdateWorkflow calls with specific field masks for Model1 and Model2
	// Model1 score 85 group (2 tickets)
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model1Score == 85 && req.Model1Name == DS_MODEL_1_NAME && len(req.TicketIds) == 2 && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_1_SCORE) && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_1_NAME)
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job1",
		SuccessfulTicketIds: []string{"ticket1", "ticket2"},
		ErroredTicketIds:    []string{},
	}, nil)

	// Model1 score 70 group (2 tickets)
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model1Score == 70 && req.Model1Name == DS_MODEL_1_NAME && len(req.TicketIds) == 2 && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_1_SCORE) && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_1_NAME)
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job2",
		SuccessfulTicketIds: []string{"ticket3", "ticket4"},
		ErroredTicketIds:    []string{},
	}, nil)

	// Model2 score 80 group (2 tickets)
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Score == 80 && req.Model2Name == DS_MODEL_2_NAME && len(req.TicketIds) == 2 && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_SCORE) && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_NAME)
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job3",
		SuccessfulTicketIds: []string{"ticket1", "ticket3"},
		ErroredTicketIds:    []string{},
	}, nil)

	// Model2 score 90 group (1 ticket)
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Score == 90 && req.Model2Name == DS_MODEL_2_NAME && len(req.TicketIds) == 1 && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_SCORE) && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_NAME)
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job4",
		SuccessfulTicketIds: []string{"ticket2"},
		ErroredTicketIds:    []string{},
	}, nil)

	// Model2 score 60 group (1 ticket)
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Score == 60 && req.Model2Name == DS_MODEL_2_NAME && len(req.TicketIds) == 1 && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_SCORE) && containsFieldMask(req.UpdateFieldMask, riskEnums.TicketBulkUpdateFieldMask_MODEL_2_NAME)
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job5",
		SuccessfulTicketIds: []string{"ticket4"},
		ErroredTicketIds:    []string{},
	}, nil)

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_ModelNamesDefaulting(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/tickets.json",
		}, nil)

	// Mock GetScoresForCases child workflow with empty model names
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: []*workflowPb.TicketWithScore{
				{
					TicketId:    "ticket1",
					Model1Score: 85,
					Model1Name:  "", // Empty model name should be defaulted
					Model2Score: 90,
					Model2Name:  "", // Empty model name should be defaulted
				},
			},
			ErroredTicketIds: []string{},
		}, nil)

	// Mock BulkUpdateWorkflow with defaulted model names
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model1Score == 85 && req.Model1Name == "default_model_name_1" && len(req.TicketIds) == 1
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job1",
		SuccessfulTicketIds: []string{"ticket1"},
		ErroredTicketIds:    []string{},
	}, nil)

	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return req.Model2Score == 90 && req.Model2Name == DEFAULT_MODEL2 && len(req.TicketIds) == 1
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job2",
		SuccessfulTicketIds: []string{"ticket1"},
		ErroredTicketIds:    []string{},
	}, nil)

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_MultiplePartialFailures(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/tickets.json",
		}, nil)

	// Create 150 tickets with the same score to test partial failures across batches
	tickets := make([]*workflowPb.TicketWithScore, 150)
	for i := 0; i < 150; i++ {
		tickets[i] = &workflowPb.TicketWithScore{
			TicketId:    fmt.Sprintf("ticket%d", i+1),
			Model1Score: 85,
			Model1Name:  DS_MODEL_1_NAME,
		}
	}

	// Mock GetScoresForCases child workflow
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: tickets,
			ErroredTicketIds:  []string{"error_ticket_1", "error_ticket_2"}, // Some scoring failures
		}, nil)

	// Mock first batch (100 tickets) - some failures
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return len(req.TicketIds) == 100
	})).Return(&workflowPb.BulkUpdateWorkflowResponse{
		JobId:               "job1",
		SuccessfulTicketIds: make([]string, 95),                                              // 95 successful
		ErroredTicketIds:    []string{"ticket1", "ticket2", "ticket3", "ticket4", "ticket5"}, // 5 failed
	}, nil)

	// Mock second batch (50 tickets) - workflow error
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.MatchedBy(func(req *workflowPb.BulkUpdateWorkflowRequest) bool {
		return len(req.TicketIds) == 50
	})).Return(nil, errors.New("batch update failed"))

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion (workflow should complete despite failures)
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

func TestCaseReprioritisation_GroupingError(t *testing.T) {
	env := setupTestEnvironment()

	req := &workflowPb.CaseReprioritisationRequest{
		IsManualUpload: false,
		DryRun:         false,
	}

	// Mock GetAllFreshdeskTickets activity
	env.OnActivity("GetAllFreshdeskTickets", mock.Anything, mock.Anything).
		Return(&activityPb.GetCasesFromFreshdeskResponse{
			S3Path: "s3://bucket/path/to/tickets.json",
		}, nil)

	// Mock GetScoresForCases child workflow with nil tickets to test grouping edge case
	env.OnWorkflow("GetScoresForCases", mock.Anything, mock.Anything).
		Return(&workflowPb.GetScoresForCasesResponse{
			TicketsWithScores: []*workflowPb.TicketWithScore{
				nil, // This nil ticket should be handled gracefully
				{
					TicketId:    "ticket1",
					Model1Score: 85,
					Model1Name:  DS_MODEL_1_NAME,
				},
			},
			ErroredTicketIds: []string{},
		}, nil)

	// Mock BulkUpdateWorkflow for the valid ticket
	env.OnWorkflow("BulkUpdateWorkflow", mock.Anything, mock.Anything).
		Return(&workflowPb.BulkUpdateWorkflowResponse{
			JobId:               "job1",
			SuccessfulTicketIds: []string{"ticket1"},
			ErroredTicketIds:    []string{},
		}, nil)

	// Execute workflow
	env.ExecuteWorkflow(riskWorkflow.CaseReprioritisation, req)

	// Verify completion (workflow should handle nil tickets gracefully)
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	var result workflowPb.CaseReprioritisationResponse
	err := env.GetWorkflowResult(&result)
	require.NoError(t, err)

	env.AssertExpectations(t)
}

// Helper function to check if a slice contains a specific field mask
func containsFieldMask(masks []riskEnums.TicketBulkUpdateFieldMask, target riskEnums.TicketBulkUpdateFieldMask) bool {
	for _, mask := range masks {
		if mask == target {
			return true
		}
	}
	return false
}
