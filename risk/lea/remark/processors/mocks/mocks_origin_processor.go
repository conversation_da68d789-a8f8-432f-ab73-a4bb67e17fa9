// Code generated by MockGen. DO NOT EDIT.
// Source: origin_processor.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	risk "github.com/epifi/gamma/api/risk"
	enums "github.com/epifi/gamma/api/risk/enums"
	gomock "github.com/golang/mock/gomock"
)

// MockOriginProcessor is a mock of OriginProcessor interface.
type MockOriginProcessor struct {
	ctrl     *gomock.Controller
	recorder *MockOriginProcessorMockRecorder
}

// MockOriginProcessorMockRecorder is the mock recorder for MockOriginProcessor.
type MockOriginProcessorMockRecorder struct {
	mock *MockOriginProcessor
}

// NewMockOriginProcessor creates a new mock instance.
func NewMockOriginProcessor(ctrl *gomock.Controller) *MockOriginProcessor {
	mock := &MockOriginProcessor{ctrl: ctrl}
	mock.recorder = &MockOriginProcessorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOriginProcessor) EXPECT() *MockOriginProcessorMockRecorder {
	return m.recorder
}

// ProcessRemark mocks base method.
func (m *MockOriginProcessor) ProcessRemark(ctx context.Context, origin enums.LEAReportOrigin, remarks string) (*risk.LEADescription, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessRemark", ctx, origin, remarks)
	ret0, _ := ret[0].(*risk.LEADescription)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProcessRemark indicates an expected call of ProcessRemark.
func (mr *MockOriginProcessorMockRecorder) ProcessRemark(ctx, origin, remarks interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessRemark", reflect.TypeOf((*MockOriginProcessor)(nil).ProcessRemark), ctx, origin, remarks)
}
