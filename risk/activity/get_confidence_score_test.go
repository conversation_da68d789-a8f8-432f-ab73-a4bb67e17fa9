package activity_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"

	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"

	activityPb "github.com/epifi/gamma/api/risk/case_management/activity"
	prioritizationPb "github.com/epifi/gamma/api/risk/case_management/prioritization"
	reviewPb "github.com/epifi/gamma/api/risk/case_management/review"

	"github.com/epifi/gamma/api/vendorgateway/risk"

	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	protizerMocks "github.com/epifi/gamma/risk/case_management/prioritisation/priotizer/mocks"
)

func TestProcessor_GetConfidenceScore(t *testing.T) {
	type args struct {
		ctx context.Context
		req *activityPb.GetConfidenceScoreRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(*mockedDependencies, *gomock.Controller)
		want       *activityPb.GetConfidenceScoreResponse
		wantErr    bool
		assertErr  func(err error) bool
	}{
		{
			name: "success - two models",
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetConfidenceScoreRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
					CaseId: "case-123",
				},
			},
			setupMocks: func(md *mockedDependencies, ctrl *gomock.Controller) {
				// Mock GetCaseById
				md.caseStore.EXPECT().
					GetCaseById(gomock.Any(), "case-123").
					Return(&reviewPb.Case{
						Id:      "case-123",
						ActorId: "actor-456",
					}, nil)

				// Mock GetByActorId for alerts
				alerts := []*caseManagementPb.AlertWithRuleDetails{
					{
						Alert: &caseManagementPb.Alert{
							Id:      "alert-1",
							ActorId: "actor-456",
						},
						Rule: &caseManagementPb.Rule{
							Id:   "rule-1",
							Name: "High Risk Transaction",
						},
					},
				}
				md.alertWithRuleManager.EXPECT().
					GetByActorId(gomock.Any(), "actor-456", []caseManagementPb.AlertFieldMask{caseManagementPb.AlertFieldMask_ALERT_FIELD_MASK_ALL}, 0).
					Return(alerts, nil)

				// Mock prioritization factory
				mockModel := protizerMocks.NewMockPrioritization(ctrl)
				inputParams := &prioritizationPb.InputParameter{
					Alerts:  alerts,
					ActorId: "actor-456",
				}
				md.prioritizationFactory.EXPECT().
					GetPrioritizationModel(
						gomock.Any(),
						prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION,
						"actor-456",
						alerts,
					).
					Return(mockModel, inputParams, nil)

				dsModelScores := []*risk.ModelResponseInfo{
					{Name: "DS_PRECISION_v1", Score: 85},
					{Name: "DS_PRECISION_v2", Score: 72},
				}
				mockModel.EXPECT().
					GetConfidenceScore(gomock.Any(), inputParams).
					Return(dsModelScores, float32(85), nil)
			},
			want: &activityPb.GetConfidenceScoreResponse{
				Success:     true,
				Model1Name:  "DS_PRECISION_v1",
				Model1Score: 85,
				Model2Name:  "DS_PRECISION_v2",
				Model2Score: 72,
			},
			wantErr: false,
		},
		{
			name: "success - single model",
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetConfidenceScoreRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
					CaseId: "case-123",
				},
			},
			setupMocks: func(md *mockedDependencies, ctrl *gomock.Controller) {
				md.caseStore.EXPECT().
					GetCaseById(gomock.Any(), "case-123").
					Return(&reviewPb.Case{
						Id:      "case-123",
						ActorId: "actor-456",
					}, nil)

				alerts := []*caseManagementPb.AlertWithRuleDetails{
					{
						Alert: &caseManagementPb.Alert{
							Id:      "alert-1",
							ActorId: "actor-456",
						},
					},
				}
				md.alertWithRuleManager.EXPECT().
					GetByActorId(gomock.Any(), "actor-456", []caseManagementPb.AlertFieldMask{caseManagementPb.AlertFieldMask_ALERT_FIELD_MASK_ALL}, 0).
					Return(alerts, nil)

				mockModel := protizerMocks.NewMockPrioritization(ctrl)
				inputParams := &prioritizationPb.InputParameter{
					Alerts:  alerts,
					ActorId: "actor-456",
				}
				md.prioritizationFactory.EXPECT().
					GetPrioritizationModel(
						gomock.Any(),
						prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION,
						"actor-456",
						alerts,
					).
					Return(mockModel, inputParams, nil)

				dsModelScores := []*risk.ModelResponseInfo{
					{Name: "DS_PRECISION_v1", Score: 95},
				}
				mockModel.EXPECT().
					GetConfidenceScore(gomock.Any(), inputParams).
					Return(dsModelScores, float32(95), nil)
			},
			want: &activityPb.GetConfidenceScoreResponse{
				Success:     true,
				Model1Name:  "DS_PRECISION_v1",
				Model1Score: 95,
				Model2Name:  "",
				Model2Score: 0,
			},
			wantErr: false,
		},
		{
			name: "error - case not found",
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetConfidenceScoreRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
					CaseId: "non-existent-case",
				},
			},
			setupMocks: func(md *mockedDependencies, ctrl *gomock.Controller) {
				md.caseStore.EXPECT().
					GetCaseById(gomock.Any(), "non-existent-case").
					Return(nil, errors.New("case not found"))
			},
			want: &activityPb.GetConfidenceScoreResponse{
				Success:      false,
				ErrorMessage: "failed to get actor ID from ticket ID: failed to get case by ID: case not found",
			},
			wantErr: false,
		},
		{
			name: "error - alerts not found",
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetConfidenceScoreRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
					CaseId: "case-123",
				},
			},
			setupMocks: func(md *mockedDependencies, ctrl *gomock.Controller) {
				md.caseStore.EXPECT().
					GetCaseById(gomock.Any(), "case-123").
					Return(&reviewPb.Case{
						Id:      "case-123",
						ActorId: "actor-456",
					}, nil)

				md.alertWithRuleManager.EXPECT().
					GetByActorId(gomock.Any(), "actor-456", []caseManagementPb.AlertFieldMask{caseManagementPb.AlertFieldMask_ALERT_FIELD_MASK_ALL}, 0).
					Return(nil, errors.New("alerts not found"))
			},
			want: &activityPb.GetConfidenceScoreResponse{
				Success:      false,
				ErrorMessage: "failed to get alerts with rules: alerts not found",
			},
			wantErr: false,
		},
		{
			name: "error - prioritization factory error",
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetConfidenceScoreRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
					CaseId: "case-123",
				},
			},
			setupMocks: func(md *mockedDependencies, ctrl *gomock.Controller) {
				md.caseStore.EXPECT().
					GetCaseById(gomock.Any(), "case-123").
					Return(&reviewPb.Case{
						Id:      "case-123",
						ActorId: "actor-456",
					}, nil)

				alerts := []*caseManagementPb.AlertWithRuleDetails{
					{
						Alert: &caseManagementPb.Alert{
							Id:      "alert-1",
							ActorId: "actor-456",
						},
					},
				}
				md.alertWithRuleManager.EXPECT().
					GetByActorId(gomock.Any(), "actor-456", []caseManagementPb.AlertFieldMask{caseManagementPb.AlertFieldMask_ALERT_FIELD_MASK_ALL}, 0).
					Return(alerts, nil)

				md.prioritizationFactory.EXPECT().
					GetPrioritizationModel(
						gomock.Any(),
						prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION,
						"actor-456",
						alerts,
					).
					Return(nil, nil, errors.New("prioritization model error"))
			},
			want: &activityPb.GetConfidenceScoreResponse{
				Success:      false,
				ErrorMessage: "failed to get DS model scores: failed to get DS prioritization model: prioritization model error",
			},
			wantErr: false,
		},
		{
			name: "error - model calculation error",
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetConfidenceScoreRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
					CaseId: "case-123",
				},
			},
			setupMocks: func(md *mockedDependencies, ctrl *gomock.Controller) {
				md.caseStore.EXPECT().
					GetCaseById(gomock.Any(), "case-123").
					Return(&reviewPb.Case{
						Id:      "case-123",
						ActorId: "actor-456",
					}, nil)

				alerts := []*caseManagementPb.AlertWithRuleDetails{
					{
						Alert: &caseManagementPb.Alert{
							Id:      "alert-1",
							ActorId: "actor-456",
						},
					},
				}
				md.alertWithRuleManager.EXPECT().
					GetByActorId(gomock.Any(), "actor-456", []caseManagementPb.AlertFieldMask{caseManagementPb.AlertFieldMask_ALERT_FIELD_MASK_ALL}, 0).
					Return(alerts, nil)

				mockModel := protizerMocks.NewMockPrioritization(ctrl)
				inputParams := &prioritizationPb.InputParameter{
					Alerts:  alerts,
					ActorId: "actor-456",
				}
				md.prioritizationFactory.EXPECT().
					GetPrioritizationModel(
						gomock.Any(),
						prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION,
						"actor-456",
						alerts,
					).
					Return(mockModel, inputParams, nil)

				mockModel.EXPECT().
					GetConfidenceScore(gomock.Any(), inputParams).
					Return(nil, float32(0), errors.New("model calculation failed"))
			},
			want: &activityPb.GetConfidenceScoreResponse{
				Success:      false,
				ErrorMessage: "failed to calculate DS model scores: model calculation failed",
			},
			wantErr: false,
		},
		{
			name: "success - empty model scores",
			args: args{
				ctx: context.Background(),
				req: &activityPb.GetConfidenceScoreRequest{
					RequestHeader: &celestialActivityPb.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
					CaseId: "case-123",
				},
			},
			setupMocks: func(md *mockedDependencies, ctrl *gomock.Controller) {
				md.caseStore.EXPECT().
					GetCaseById(gomock.Any(), "case-123").
					Return(&reviewPb.Case{
						Id:      "case-123",
						ActorId: "actor-456",
					}, nil)

				alerts := []*caseManagementPb.AlertWithRuleDetails{
					{
						Alert: &caseManagementPb.Alert{
							Id:      "alert-1",
							ActorId: "actor-456",
						},
					},
				}
				md.alertWithRuleManager.EXPECT().
					GetByActorId(gomock.Any(), "actor-456", []caseManagementPb.AlertFieldMask{caseManagementPb.AlertFieldMask_ALERT_FIELD_MASK_ALL}, 0).
					Return(alerts, nil)

				mockModel := protizerMocks.NewMockPrioritization(ctrl)
				inputParams := &prioritizationPb.InputParameter{
					Alerts:  alerts,
					ActorId: "actor-456",
				}
				md.prioritizationFactory.EXPECT().
					GetPrioritizationModel(
						gomock.Any(),
						prioritizationPb.PrioritizationModelType_PRIORITIZATION_MODEL_TYPE_DS_PRECISION,
						"actor-456",
						alerts,
					).
					Return(mockModel, inputParams, nil)

				mockModel.EXPECT().
					GetConfidenceScore(gomock.Any(), inputParams).
					Return([]*risk.ModelResponseInfo{}, float32(0), nil)
			},
			want: &activityPb.GetConfidenceScoreResponse{
				Success:     true,
				Model1Name:  "",
				Model1Score: 0,
				Model2Name:  "",
				Model2Score: 0,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			act, md, assertTest := newProcessorWithMocks(t)
			defer assertTest()
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(act)

			// Create a gomock controller for this specific test
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			tt.setupMocks(md, ctrl)

			var result *activityPb.GetConfidenceScoreResponse
			got, err := env.ExecuteActivity(riskNs.GetConfidenceScore, tt.args.req)

			if got != nil {
				getErr := got.Get(&result)
				if result != nil && getErr != nil {
					t.Errorf("GetConfidenceScore() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetConfidenceScore() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && tt.assertErr != nil && !tt.assertErr(err):
				t.Errorf("GetConfidenceScore() error = %v assertion failed", err)
				return
			case tt.want != nil && result != nil:
				assert.Equal(t, tt.want.Success, result.Success)
				assert.Equal(t, tt.want.Model1Name, result.Model1Name)
				assert.InDelta(t, tt.want.Model1Score, result.Model1Score, 0.001)
				assert.Equal(t, tt.want.Model2Name, result.Model2Name)
				assert.InDelta(t, tt.want.Model2Score, result.Model2Score, 0.001)
				if tt.want.ErrorMessage != "" {
					assert.Contains(t, result.ErrorMessage, tt.want.ErrorMessage)
				}
			}
		})
	}
}
