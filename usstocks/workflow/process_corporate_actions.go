package workflow

import (
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/datetime"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	usStocksNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"
	usStocksActivityPb "github.com/epifi/gamma/api/usstocks/activity"
	ussWfPb "github.com/epifi/gamma/api/usstocks/workflow"
)

// nolint:funlen
func ProcessCorporateActions(ctx workflow.Context, _ *emptyPb.Empty) error {
	lg := workflow.GetLogger(ctx)
	lg.Info("initiating corporate action workflow ")
	wfReq := &ussWfPb.ProcessCorporateActionsReq{
		StartDate: nil,
		EndDate:   nil,
	}
	resp, err := getCorporateActions(ctx, wfReq)
	if err != nil {
		lg.Error("error getting corporate actions", zap.Error(err))
		return errors.Wrapf(err, "error getting corporate actions")
	}
	if resp.GetCorporateActions() == nil {
		lg.Info("no stocks to update/create from corporate actions")
		return nil
	}
	err = updateOrCreateNewSymbols(ctx, resp)
	if err != nil {
		lg.Error("error updating or creating new symbols", zap.Error(err))
		return errors.Wrap(err, "error updating or creating new symbols")
	}
	return nil
}

func getCorporateActions(ctx workflow.Context, request *ussWfPb.ProcessCorporateActionsReq) (*usStocksActivityPb.GetCorporateActionsFromVendorResponse, error) {
	if request.GetStartDate() == nil {
		request.StartDate = datetime.TimeToDateInLoc(time.Now().Add(-21*24*time.Hour), time.Local)
	}
	if request.GetEndDate() == nil {
		request.EndDate = datetime.TimeToDateInLoc(time.Now(), time.Local)
	}
	resp := &usStocksActivityPb.GetCorporateActionsFromVendorResponse{}
	req := &usStocksActivityPb.GetCorporateActionsFromVendorRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: USSAlpacaOwnership,
		},
		StartDate: request.GetStartDate(),
		EndDate:   request.GetEndDate(),
	}
	err := activityPkg.Execute(ctx, usStocksNs.GetCorporateActionsFromVendor, resp, req)
	if err != nil {
		return nil, errors.Wrapf(err, "error executing activity: %s", string(usStocksNs.GetCorpAction))
	}
	return resp, nil
}

func updateOrCreateNewSymbols(ctx workflow.Context, request *usStocksActivityPb.GetCorporateActionsFromVendorResponse) error {
	resp := &usStocksActivityPb.UpdateOrCreateSymbolsResponse{}
	req := &usStocksActivityPb.UpdateOrCreateSymbolsRequest{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: USSAlpacaOwnership,
		},
		CorporateActions: request.GetCorporateActions(),
	}
	err := activityPkg.Execute(ctx, usStocksNs.UpdateOrCreateSymbols, resp, req)
	if err != nil {
		return errors.Wrapf(err, "error executing activity: %s", string(usStocksNs.UpdateOrCreateSymbols))
	}
	return nil
}
