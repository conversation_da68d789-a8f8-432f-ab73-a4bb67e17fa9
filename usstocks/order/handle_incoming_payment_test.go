package order_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	celestialPb "github.com/epifi/be-common/api/celestial"
	usstocksPb "github.com/epifi/gamma/api/usstocks"
	orderPb "github.com/epifi/gamma/api/usstocks/order"
	usstocksOrderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/api/usstocks/payload"
	"github.com/epifi/be-common/pkg/epifierrors"
	usstocks2 "github.com/epifi/be-common/pkg/epifitemporal/namespace/usstocks"
	"github.com/epifi/gamma/usstocks/order/inward_remittance_ops_processor/inward_transaction_processor"
)

func TestService_HandleIncomingPayment(t *testing.T) {
	orderSvc, md := getServiceWithMocks(t)

	type args struct {
		ctx context.Context
		req *usstocksOrderPb.HandleIncomingPaymentRequest
	}
	tests := []struct {
		name           string
		args           args
		setupMockCalls func(*args)
		want           *usstocksOrderPb.HandleIncomingPaymentResponse
		wantErr        bool
	}{
		{
			name: "aggregated transaction being received",
			args: args{
				ctx: context.Background(),
				req: &usstocksOrderPb.HandleIncomingPaymentRequest{
					ExternalOrderId: "external-order-id-1",
					ActorId:         "actor-id-1",
					Reason:          usstocksOrderPb.IncomingPaymentReason_AGGREGATED_INWARD_REMITTANCE_TRANSACTION,
					TxnId:           "txn-id-1",
					Utr:             "utr-id-1",
				},
			},
			setupMockCalls: func(args *args) {
				md.aggregatedRemTxnDao.EXPECT().GetByExternalId(gomock.Any(), args.req.GetExternalOrderId()).Return(&orderPb.AggregatedRemittanceTransaction{Id: "agg-txn-id-1"}, nil)
				md.aggregatedRemTxnDao.EXPECT().Update(gomock.Any(), &usstocksOrderPb.AggregatedRemittanceTransaction{
					Id:               "agg-txn-id-1",
					TransactionState: usstocksPb.AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_SUCCESSFUL,
				}, []orderPb.AggregatedRemittanceTransactionFieldMask{orderPb.AggregatedRemittanceTransactionFieldMask_AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_STATE}).Return(nil)
				md.transactionProcessor.EXPECT().UpdateTransaction(gomock.Any(), &inward_transaction_processor.UpdateTransactionRequest{
					UpdateType:    inward_transaction_processor.UpdateTransactionAmountReceived,
					TransactionId: "agg-txn-id-1",
					UtrNumber:     args.req.GetUtr(),
				}).Return(nil)
			},
			want: &usstocksOrderPb.HandleIncomingPaymentResponse{Status: rpc.StatusOk()},
		},
		{
			name: "aggregated transaction error while getting external id",
			args: args{
				ctx: context.Background(),
				req: &usstocksOrderPb.HandleIncomingPaymentRequest{
					ExternalOrderId: "external-order-id-1",
					ActorId:         "actor-id-1",
					Reason:          usstocksOrderPb.IncomingPaymentReason_AGGREGATED_INWARD_REMITTANCE_TRANSACTION,
					TxnId:           "txn-id-1",
					Utr:             "utr-id-1",
				},
			},
			setupMockCalls: func(args *args) {
				md.aggregatedRemTxnDao.EXPECT().GetByExternalId(gomock.Any(), args.req.GetExternalOrderId()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &usstocksOrderPb.HandleIncomingPaymentResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "aggregated transaction while updating",
			args: args{
				ctx: context.Background(),
				req: &usstocksOrderPb.HandleIncomingPaymentRequest{
					ExternalOrderId: "external-order-id-1",
					ActorId:         "actor-id-1",
					Reason:          usstocksOrderPb.IncomingPaymentReason_AGGREGATED_INWARD_REMITTANCE_TRANSACTION,
					TxnId:           "txn-id-1",
					Utr:             "utr-id-1",
				},
			},
			setupMockCalls: func(args *args) {
				md.aggregatedRemTxnDao.EXPECT().GetByExternalId(gomock.Any(), args.req.GetExternalOrderId()).Return(&orderPb.AggregatedRemittanceTransaction{Id: "agg-txn-id-1"}, nil)
				md.aggregatedRemTxnDao.EXPECT().Update(gomock.Any(), &usstocksOrderPb.AggregatedRemittanceTransaction{
					Id:               "agg-txn-id-1",
					TransactionState: usstocksPb.AggregatedRemittanceTransactionState_AGGREGATED_REMITTANCE_TRANSACTION_STATE_PAYMENT_SUCCESSFUL,
				}, []orderPb.AggregatedRemittanceTransactionFieldMask{orderPb.AggregatedRemittanceTransactionFieldMask_AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_STATE}).Return(nil)
				md.transactionProcessor.EXPECT().UpdateTransaction(gomock.Any(), &inward_transaction_processor.UpdateTransactionRequest{
					UpdateType:    inward_transaction_processor.UpdateTransactionAmountReceived,
					TransactionId: "agg-txn-id-1",
					UtrNumber:     args.req.GetUtr(),
				}).Return(epifierrors.ErrRecordNotFound)
			},
			want: &usstocksOrderPb.HandleIncomingPaymentResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "sell order being received",
			args: args{
				ctx: context.Background(),
				req: &usstocksOrderPb.HandleIncomingPaymentRequest{
					ExternalOrderId: "sell-order-id-1",
					ActorId:         "actor-id-1",
					Reason:          usstocksOrderPb.IncomingPaymentReason_SELL_ORDER,
					TxnId:           "txn-id-1",
					Utr:             "utr-id-1",
				},
			},
			setupMockCalls: func(args *args) {
				sampleOrder := &orderPb.Order{WfReqId: "workflow-id-1"}
				md.orderDao.EXPECT().GetByExternalOrderId(gomock.Any(), args.req.GetExternalOrderId()).Return(sampleOrder, nil)
				sellPaymentStatusSignal := &payload.SellPaymentStatusSignal{Status: payload.SellPaymentStatusType_SELL_PAYMENT_STATUS_TYPE_RECEIVED}
				sellPaymentStatusSignalPayload, err := protojson.Marshal(sellPaymentStatusSignal)
				if err != nil {
					return
				}

				md.celestialClient.EXPECT().SignalWorkflow(gomock.Any(), &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{WorkflowReqId: sampleOrder.GetWfReqId()},
					SignalId:   string(usstocks2.SellPaymentStatusSignal),
					Payload:    sellPaymentStatusSignalPayload,
					Ownership:  commontypes.Ownership_US_STOCKS_ALPACA,
				}).Return(&celestialPb.SignalWorkflowResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &usstocksOrderPb.HandleIncomingPaymentResponse{Status: rpc.StatusOk()},
		},
		{
			name: "sell order error getting by external id",
			args: args{
				ctx: context.Background(),
				req: &usstocksOrderPb.HandleIncomingPaymentRequest{
					ExternalOrderId: "sell-order-id-1",
					ActorId:         "actor-id-1",
					Reason:          usstocksOrderPb.IncomingPaymentReason_SELL_ORDER,
					TxnId:           "txn-id-1",
					Utr:             "utr-id-1",
				},
			},
			setupMockCalls: func(args *args) {
				md.orderDao.EXPECT().GetByExternalOrderId(gomock.Any(), args.req.GetExternalOrderId()).Return(nil, epifierrors.ErrRecordNotFound)
			},
			want: &usstocksOrderPb.HandleIncomingPaymentResponse{Status: rpc.StatusInternal()},
		},
		{
			name: "sell order error while signaling",
			args: args{
				ctx: context.Background(),
				req: &usstocksOrderPb.HandleIncomingPaymentRequest{
					ExternalOrderId: "sell-order-id-1",
					ActorId:         "actor-id-1",
					Reason:          usstocksOrderPb.IncomingPaymentReason_SELL_ORDER,
					TxnId:           "txn-id-1",
					Utr:             "utr-id-1",
				},
			},
			setupMockCalls: func(args *args) {
				sampleOrder := &orderPb.Order{WfReqId: "workflow-id-1"}
				md.orderDao.EXPECT().GetByExternalOrderId(gomock.Any(), args.req.GetExternalOrderId()).Return(sampleOrder, nil)
				sellPaymentStatusSignal := &payload.SellPaymentStatusSignal{Status: payload.SellPaymentStatusType_SELL_PAYMENT_STATUS_TYPE_RECEIVED}
				sellPaymentStatusSignalPayload, err := protojson.Marshal(sellPaymentStatusSignal)
				if err != nil {
					return
				}

				md.celestialClient.EXPECT().SignalWorkflow(gomock.Any(), &celestialPb.SignalWorkflowRequest{
					Identifier: &celestialPb.SignalWorkflowRequest_WorkflowReqId{WorkflowReqId: sampleOrder.GetWfReqId()},
					SignalId:   string(usstocks2.SellPaymentStatusSignal),
					Payload:    sellPaymentStatusSignalPayload,
					Ownership:  commontypes.Ownership_US_STOCKS_ALPACA,
				}).Return(&celestialPb.SignalWorkflowResponse{Status: rpc.StatusInternal()}, nil)

			},
			want: &usstocksOrderPb.HandleIncomingPaymentResponse{Status: rpc.StatusInternal()},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMockCalls(&tt.args)
			got, err := orderSvc.HandleIncomingPayment(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("HandleIncomingPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&rpc.Status{}, "debug_message"),
			}

			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("HandleIncomingPayment() got = %v,\n want %v diff: %v", got, tt.want, diff)
			}
		})
	}
}
