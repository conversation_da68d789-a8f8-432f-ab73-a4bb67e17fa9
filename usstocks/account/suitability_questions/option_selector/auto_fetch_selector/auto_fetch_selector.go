package auto_fetch_selector

import (
	"context"
	"fmt"

	"github.com/Knetic/govaluate"
	"github.com/pkg/errors"

	accountPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/usstocks/account/suitability_questions/option_selector"
	"github.com/epifi/gamma/usstocks/account/suitability_questions/option_selector/auto_fetch_selector/auto_fetch_data_collector"
	config "github.com/epifi/gamma/usstocks/config/genconf"
)

type AutoFetchOptionSelector struct {
	optionType                         accountPb.OptionSelectionType
	dataCollectorMap                   map[accountPb.AutoFetchOptionType]auto_fetch_data_collector.IAutoFetchDataCollector
	questionTypeToAutoFetchQuestionMap map[accountPb.SuitabilityQuestionType]*accountPb.SuitabilityQuestions
}

func NewAutoFetchOptionSelector(config *config.Config, dataCollectors ...auto_fetch_data_collector.IAutoFetchDataCollector) *AutoFetchOptionSelector {
	questionTypeToAutoFetchQuestionMap := make(map[accountPb.SuitabilityQuestionType]*accountPb.SuitabilityQuestions, 0)
	for _, question := range config.SuitabilityQuestionsMap() {
		if question.OptionSelectionType == accountPb.OptionSelectionType_OPTION_SELECTION_TYPE_AUTO_FETCH {
			questionTypeToAutoFetchQuestionMap[question.Type] = question
		}
	}
	dataCollectorMap := make(map[accountPb.AutoFetchOptionType]auto_fetch_data_collector.IAutoFetchDataCollector, 0)
	for _, dataCollector := range dataCollectors {
		dataCollectorMap[dataCollector.GetType()] = dataCollector
	}
	return &AutoFetchOptionSelector{optionType: accountPb.OptionSelectionType_OPTION_SELECTION_TYPE_AUTO_FETCH, questionTypeToAutoFetchQuestionMap: questionTypeToAutoFetchQuestionMap, dataCollectorMap: dataCollectorMap}
}

func (a *AutoFetchOptionSelector) GetType() accountPb.OptionSelectionType {
	return a.optionType
}

func (a *AutoFetchOptionSelector) GetAllOptions(ctx context.Context, req *option_selector.GetAllOptionsReq) (*option_selector.GetAllOptionsResp, error) {
	dataCollector, err := a.getDataCollector(req.QuestionType)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting getDataCollector")
	}
	optionsResp, err := dataCollector.GetOptions(ctx, req.ActorId)
	if err != nil {
		return nil, errors.Wrap(err, "error while GetAllOptions")
	}
	return &option_selector.GetAllOptionsResp{
		Options: optionsResp.Options,
		Answer:  optionsResp.Answer,
	}, nil
}

func (a *AutoFetchOptionSelector) CalculateScore(questionType accountPb.SuitabilityQuestionType, profileInfoForGivenQuestion *accountPb.SuitabilityAnswer) (int32, error) {
	dataCollector, err := a.getDataCollector(questionType)
	if err != nil {
		return 0, errors.Wrap(err, "error while getting getDataCollector")
	}
	err = dataCollector.ValidateAnswer(profileInfoForGivenQuestion)
	if err != nil {
		return 0, errors.Wrap(err, "error while getting ValidateAnswer")
	}
	expressionMap, err := dataCollector.GoExpressionParamMap(profileInfoForGivenQuestion)
	if err != nil {
		return 0, errors.Wrap(err, "error while GoExpressionParamMap")
	}
	question := a.questionTypeToAutoFetchQuestionMap[questionType]
	for expression, expectedScore := range question.GetOptions().GetAutoFetchOption().GetExpressionToScoreMap() {
		res, err2 := a.evaluateExpressionUsingGoEvaluate(expressionMap, expression)
		if err2 != nil {
			return 0, errors.Wrap(err2, "error while evaluateExpressionUsingGoEvaluate")
		}
		if res {
			return expectedScore, nil
		}
	}
	return 0, fmt.Errorf("no expression was evaluate to true")
}

func (a *AutoFetchOptionSelector) ValidateAnswer(questionType accountPb.SuitabilityQuestionType, answer *accountPb.SuitabilityAnswer) error {
	dataCollector, err := a.getDataCollector(questionType)
	if err != nil {
		return errors.Wrap(err, "error while getting getDataCollector")
	}
	return dataCollector.ValidateAnswer(answer)
}

func (a *AutoFetchOptionSelector) getDataCollector(questionType accountPb.SuitabilityQuestionType) (auto_fetch_data_collector.IAutoFetchDataCollector, error) {
	autoFetchQuestion, ok := a.questionTypeToAutoFetchQuestionMap[questionType]
	if !ok {
		return nil, fmt.Errorf("giving question is not auto fetch")
	}
	dataCollector, ok := a.dataCollectorMap[autoFetchQuestion.GetOptions().GetAutoFetchOption().GetOptionType()]
	if !ok {
		return nil, fmt.Errorf("giving autofetch types is not implemmented")
	}
	return dataCollector, nil
}

// nolint
func (a *AutoFetchOptionSelector) evaluateExpressionUsingGoEvaluate(expressionMap map[string]interface{}, goExpression string) (bool, error) {
	expression, err := govaluate.NewEvaluableExpression(goExpression)
	if err != nil {
		return false, fmt.Errorf("error in NewEvaluableExpression %w", err)
	}
	result, evalErr := expression.Evaluate(expressionMap)
	if evalErr != nil {
		return false, errors.Wrap(evalErr, "error while evaluting")
	}

	switch result.(type) {
	case bool:
		return result.(bool), nil
	default:
		return false, fmt.Errorf("defined condition should evaluate to bool")
	}
}
