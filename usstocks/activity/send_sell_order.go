package activity

import (
	"context"
	"fmt"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/usstocks"
	"github.com/epifi/gamma/api/usstocks/activity"
	usStocksActivityPb "github.com/epifi/gamma/api/usstocks/activity"
	orderMgPb "github.com/epifi/gamma/api/usstocks/order"
	vgUSStocks "github.com/epifi/gamma/api/vendorgateway/stocks"

	"github.com/pkg/errors"
	temporalActivity "go.temporal.io/sdk/activity"
	"go.uber.org/zap"
)

// This activity place the order status with the vendor
func (p *Processor) SendSellOrder(ctx context.Context, req *usStocksActivityPb.SendSellOrderRequest) (*activity.SendSellOrderResponse, error) {
	lg := temporalActivity.GetLogger(ctx)

	orderId := req.GetRequestHeader().GetClientReqId()

	order, err := p.orderDao.GetById(ctx, orderId)
	if err != nil {
		lg.Error("error in getting US stocks order", zap.Error(err), zap.String(logger.ORDER_ID, orderId))
	}
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
	case err != nil:
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	err = validateSellOrder(order)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("invalid arguments for sell order : %s", err.Error()))
	}

	createOrderRequest := getVgCreateSellOrderRequest(order)
	resp, err := p.vgUSStocksClient.CreateOrder(ctx, createOrderRequest)
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().GetCode() == uint32(vgUSStocks.CreateOrderResponse_DUPLICATED_CLIENT_ORDER_ID) {
			existingOrder, getErr := p.getOrderDetails(ctx, order.GetVendorAccountId(), orderId)
			if getErr != nil {
				return nil, getErr
			}
			return &activity.SendSellOrderResponse{VgOrder: existingOrder}, nil
		}
		if failureReason, ok := vgCreateOrderStatusToOrderFailureReasonMap[resp.GetStatus().GetCode()]; ok {
			err = p.orderDao.Update(ctx, orderId, &orderMgPb.Order{FailureReason: failureReason, State: usstocks.OrderState_ORDER_FAILED},
				[]orderMgPb.OrderFieldMask{orderMgPb.OrderFieldMask_ORDER_FIELD_MASK_ORDER_STATE, orderMgPb.OrderFieldMask_ORDER_FIELD_MASK_FAILURE_REASON})
			if err != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
			}
			return nil, epifierrors.ErrPermanent
		}
		lg.Error("error in creating sell order with vendor", zap.Error(err), zap.String(logger.ORDER_ID, orderId))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	return &activity.SendSellOrderResponse{VgOrder: resp.GetOrder()}, nil
}

// TODO: (aniket), add this validation in sell sync flow as well
func validateSellOrder(order *orderMgPb.Order) error {
	switch order.GetTradeInfo().GetOrderType() {
	case usstocks.OrderType_ORDER_TYPE_NOTIONAL_VALUE, usstocks.OrderType_ORDER_TYPE_REWARDS_NOTIONAL_VALUE, usstocks.OrderType_ORDER_TYPE_SIP_NOTIONAL_VALUE:
		if !money.IsCurrencyCodeUSD(order.GetAmountRequested()) {
			return errors.New("invalid currency code in amount requested for sell order. Expected currency: USD")
		}
	case usstocks.OrderType_ORDER_TYPE_SELL_ALL:
		if order.GetQtyConfirmed() <= 0 {
			return errors.New("quantity confirmed should be greater than 0 for sell all order")
		}
	case usstocks.OrderType_ORDER_TYPE_QUANTITY_VALUE:
		if order.GetQtyRequested() <= 0 {
			return errors.New("quantity requested should be greater than 0 for sell quantity order")
		}
	case usstocks.OrderType_ORDER_TYPE_LIMIT_ORDER, usstocks.OrderType_ORDER_TYPE_SELL_ALL_LIMIT_ORDER:
		if !money.IsCurrencyCodeUSD(order.GetLimitPrice()) {
			return errors.New("invalid currency code in limit price for sell order. Expected currency: USD")
		}
		if order.GetQtyRequested() <= 0 {
			return errors.New("quantity requested should be greater than 0 for sell limit order")
		}
	}
	return nil
}

func getVgCreateSellOrderRequest(order *orderMgPb.Order) *vgUSStocks.CreateOrderRequest {
	createOrderReq := &vgUSStocks.CreateOrderRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		AccountId:     order.GetVendorAccountId(),
		ClientOrderId: order.GetId(),
		Symbol:        order.GetSymbol(),
		Side:          orderSideMapping[order.GetSide()],
		TimeInForce:   getVgTimeInForceFromOrder(order.GetTimeInForce()),
		Brokerage:     getVgBrokerage(order.GetInvoiceDetails().GetBrokerage()),
	}

	switch order.GetTradeInfo().GetOrderType() {
	case usstocks.OrderType_ORDER_TYPE_NOTIONAL_VALUE, usstocks.OrderType_ORDER_TYPE_REWARDS_NOTIONAL_VALUE, usstocks.OrderType_ORDER_TYPE_SIP_NOTIONAL_VALUE:
		createOrderReq.Notional = order.GetAmountRequested()
		createOrderReq.OrderType = vgUSStocks.OrderType_ORDER_TYPE_MARKET
	case usstocks.OrderType_ORDER_TYPE_SELL_ALL, usstocks.OrderType_ORDER_TYPE_SELL_ALL_QUANTITY:
		if order.GetQtyConfirmed() > 0 {
			createOrderReq.Qty = order.GetQtyConfirmed()
		} else {
			createOrderReq.Notional = order.GetAmountRequested()
		}
		createOrderReq.OrderType = vgUSStocks.OrderType_ORDER_TYPE_MARKET
	case usstocks.OrderType_ORDER_TYPE_QUANTITY_VALUE:
		createOrderReq.Qty = order.GetQtyRequested()
		createOrderReq.OrderType = vgUSStocks.OrderType_ORDER_TYPE_MARKET
	case usstocks.OrderType_ORDER_TYPE_LIMIT_ORDER, usstocks.OrderType_ORDER_TYPE_SELL_ALL_LIMIT_ORDER:
		createOrderReq.Qty = order.GetQtyRequested()
		createOrderReq.LimitPrice = order.GetLimitPrice()
		createOrderReq.OrderType = vgUSStocks.OrderType_ORDER_TYPE_LIMIT
	}
	return createOrderReq
}
