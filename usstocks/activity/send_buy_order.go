package activity

import (
	"context"
	"fmt"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/pkg/errors"
	temporalActivity "go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/usstocks"
	"github.com/epifi/gamma/api/usstocks/activity"
	ussOrderPb "github.com/epifi/gamma/api/usstocks/order"
	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
)

var (
	orderSideMapping = map[usstocks.OrderSide]vgStocksPb.Side{
		usstocks.OrderSide_BUY:                    vgStocksPb.Side_SIDE_BUY,
		usstocks.OrderSide_SELL:                   vgStocksPb.Side_SIDE_SELL,
		usstocks.OrderSide_ORDER_SIDE_UNSPECIFIED: vgStocksPb.Side_SIDE_UNSPECIFIED,
	}
	vgCreateOrderStatusToOrderFailureReasonMap = map[uint32]ussOrderPb.FailureReason{
		uint32(vgStocksPb.CreateOrderResponse_SYMBOL_IS_PTP_AND_ACCOUNT_IS_NOT_CONFIGURED):         ussOrderPb.FailureReason_FAILURE_REASON_SYMBOL_IS_PTP_AND_ACCOUNT_IS_NOT_CONFIGURED,
		uint32(vgStocksPb.CreateOrderResponse_FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL): ussOrderPb.FailureReason_FAILURE_REASON_FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL,
		uint32(vgStocksPb.CreateOrderResponse_INSUFFICIENT_SELL_QTY_REQUESTED):                     ussOrderPb.FailureReason_FAILURE_REASON_INSUFFICIENT_SELL_QTY_REQUESTED,
		uint32(vgStocksPb.CreateOrderResponse_SYMBOL_INACTIVE_FOR_TRADE):                           ussOrderPb.FailureReason_FAILURE_REASON_SYMBOL_INACTIVE_FOR_TRADE,
		uint32(vgStocksPb.CreateOrderResponse_SYMBOL_NOT_FOUND_AT_VENDOR):                          ussOrderPb.FailureReason_FAILURE_REASON_SYMBOL_NOT_FOUND_AT_VENDOR,
		uint32(vgStocksPb.CreateOrderResponse_SHORT_SELLING_IS_NOT_ALLOWED):                        ussOrderPb.FailureReason_FAILURE_REASON_SHORT_SELLING_IS_NOT_ALLOWED,
		uint32(vgStocksPb.CreateOrderResponse_SYMBOL_NON_TRADABLE_AT_VENDOR):                       ussOrderPb.FailureReason_FAILURE_REASON_SYMBOL_NON_TRADABLE_AT_VENDOR,
		uint32(vgStocksPb.CreateOrderResponse_INSUFFICIENT_BUYING_POWER):                           ussOrderPb.FailureReason_FAILURE_REASON_INSUFFICIENT_BUYING_POWER,
		uint32(vgStocksPb.CreateOrderResponse_OPPOSITE_SIDE_ORDER_EXISTS):                          ussOrderPb.FailureReason_FAILURE_REASON_OPPOSITE_SIDE_ORDER_EXISTS,
		uint32(vgStocksPb.CreateOrderResponse_DAY_TRADING_PROTECTION):                              ussOrderPb.FailureReason_FAILURE_REASON_DAY_TRADING_PROTECTION,
	}
)

func (p *Processor) SendBuyOrder(ctx context.Context, req *activity.SendBuyOrderRequest) (*activity.SendBuyOrderResponse, error) {
	lg := temporalActivity.GetLogger(ctx)

	orderId := req.GetRequestHeader().GetClientReqId()

	order, err := p.orderDao.GetById(ctx, orderId)
	if err != nil {
		lg.Error("error in getting US stocks order", zap.Error(err), zap.String(logger.ORDER_ID, orderId))
	}
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
	case err != nil:
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	err = validateBuyOrder(order)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("invalid arguments for buy order : %s", err.Error()))
	}

	createOrderRequest := getVgCreateBuyOrderRequest(order)
	res, err := p.vgUSStocksClient.CreateOrder(ctx, createOrderRequest)
	if err = epifigrpc.RPCError(res, err); err != nil {
		lg.Error("error creating buy order", zap.Error(err), zap.String(logger.ORDER_ID, orderId))
		if res.GetStatus().GetCode() == uint32(vgStocksPb.CreateOrderResponse_DUPLICATED_CLIENT_ORDER_ID) {
			existingOrder, getErr := p.getOrderDetails(ctx, order.GetVendorAccountId(), orderId)
			if getErr != nil {
				return nil, getErr
			}
			return &activity.SendBuyOrderResponse{VgOrder: existingOrder}, nil
		}
		failureReason, ok := vgCreateOrderStatusToOrderFailureReasonMap[res.GetStatus().GetCode()]
		if !ok {
			return nil, errors.Errorf("error getting failure reason for status code: %d, order id: %s", res.GetStatus().GetCode(), orderId)
		}
		updateErr := p.orderDao.Update(ctx, orderId, &ussOrderPb.Order{FailureReason: failureReason, State: usstocks.OrderState_ORDER_FAILED},
			[]ussOrderPb.OrderFieldMask{ussOrderPb.OrderFieldMask_ORDER_FIELD_MASK_ORDER_STATE, ussOrderPb.OrderFieldMask_ORDER_FIELD_MASK_FAILURE_REASON},
		)
		if updateErr != nil {
			return nil, errors.Wrapf(updateErr, "error updating order, id: %s", orderId)
		}
		lg.Info("marked order as failed with failure reason", zap.String(logger.ORDER_ID, orderId))
		return nil, epifitemporal.NewPermanentError(errors.Wrapf(err, "error creating buy order, id: %s", orderId))
	}
	return &activity.SendBuyOrderResponse{VgOrder: res.GetOrder()}, nil
}

// TODO: (aniket), add this validation in buy sync flow as well
func validateBuyOrder(order *ussOrderPb.Order) error {
	switch order.GetTradeInfo().GetOrderType() {
	case usstocks.OrderType_ORDER_TYPE_NOTIONAL_VALUE, usstocks.OrderType_ORDER_TYPE_REWARDS_NOTIONAL_VALUE, usstocks.OrderType_ORDER_TYPE_SIP_NOTIONAL_VALUE:
		if !money.IsCurrencyCodeUSD(order.GetAmountRequested()) {
			return errors.New("invalid currency code in amount requested for buy order. Expected currency: USD")
		}
	case usstocks.OrderType_ORDER_TYPE_LIMIT_ORDER:
		if !money.IsCurrencyCodeUSD(order.GetLimitPrice()) {
			return errors.New("invalid currency code in limit price for buy order. Expected currency: USD")
		}
		if order.GetQtyRequested() <= 0 {
			return errors.New("quantity requested should be greater than 0 for buy limit order")
		}
	case usstocks.OrderType_ORDER_TYPE_QUANTITY_VALUE:
		if order.GetQtyRequested() <= 0 {
			return errors.New("quantity requested should be greater than 0 for buy quantity order")
		}
	}
	return nil
}

func getVgCreateBuyOrderRequest(order *ussOrderPb.Order) *vgStocksPb.CreateOrderRequest {
	createOrderReq := &vgStocksPb.CreateOrderRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_ALPACA,
		},
		AccountId:     order.GetVendorAccountId(),
		ClientOrderId: order.GetId(),
		Symbol:        order.GetSymbol(),
		Side:          orderSideMapping[order.GetSide()],
		TimeInForce:   getVgTimeInForceFromOrder(order.GetTimeInForce()),
		Brokerage:     getVgBrokerage(order.GetInvoiceDetails().GetBrokerage()),
	}
	// sending both quantity and amount is not permitted by vendor,
	// hence checking for order type to fill quantity or notional value and limit price
	switch order.GetTradeInfo().GetOrderType() {
	case usstocks.OrderType_ORDER_TYPE_NOTIONAL_VALUE, usstocks.OrderType_ORDER_TYPE_SIP_NOTIONAL_VALUE, usstocks.OrderType_ORDER_TYPE_REWARDS_NOTIONAL_VALUE:
		createOrderReq.Notional = order.GetAmountRequested()
		createOrderReq.OrderType = vgStocksPb.OrderType_ORDER_TYPE_MARKET
	case usstocks.OrderType_ORDER_TYPE_QUANTITY_VALUE:
		createOrderReq.Qty = order.GetQtyRequested()
		createOrderReq.OrderType = vgStocksPb.OrderType_ORDER_TYPE_MARKET
	case usstocks.OrderType_ORDER_TYPE_LIMIT_ORDER:
		createOrderReq.Qty = order.GetQtyRequested()
		createOrderReq.LimitPrice = order.GetLimitPrice()
		createOrderReq.OrderType = vgStocksPb.OrderType_ORDER_TYPE_LIMIT
	}
	return createOrderReq
}

func getVgTimeInForceFromOrder(timeInForce usstocks.TimeInForce) vgStocksPb.TimeInForce {
	switch timeInForce {
	case usstocks.TimeInForce_TIME_IN_FORCE_GTC:
		return vgStocksPb.TimeInForce_TIME_IN_FORCE_GTC
	default:
		return vgStocksPb.TimeInForce_TIME_IN_FORCE_DAY
	}
}

func getVgBrokerage(brokerage *ussOrderPb.Brokerage) *vgStocksPb.Brokerage {
	if brokerage.GetBrokerageAmount() != nil {
		return &vgStocksPb.Brokerage{
			Brokerage: &vgStocksPb.Brokerage_BrokerageAmount{
				BrokerageAmount: brokerage.GetBrokerageAmount(),
			},
		}
	}
	if brokerage.GetBrokerageInPercentage() != 0 {
		return &vgStocksPb.Brokerage{
			Brokerage: &vgStocksPb.Brokerage_BrokerageInPercentage{
				BrokerageInPercentage: brokerage.GetBrokerageInPercentage(),
			},
		}
	}
	return nil
}

func (p *Processor) getOrderDetails(ctx context.Context, vendorAccountId, orderId string) (*vgStocksPb.Order, error) {
	res, err := p.vgUSStocksClient.GetOrderDetails(ctx, &vgStocksPb.GetOrderDetailsRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_ALPACA},
		AccountId: vendorAccountId,
		OrderId:   orderId,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, errors.Wrapf(err, "error getting order details for order id: %s", orderId)
	}
	return res.GetOrderDetails(), nil
}
