-- temporary script to fix the icon url of a teaser insight in prod
-- We are going ahead as the number of entries in table is single digit and a full table scan is not a problem now

UPDATE CONTENT_TEMPLATES SET CONTENT_DETAILS='{"title": {"text": "We''re sensing a pattern 🔮"}, "ctaText": {"text": "KNOW MORE"}, "iconUrl": "https://epifi-icons.pointz.in/insights/chart-increasing_1f4c8-min.png", "cardType": "INSIGHT_CARD_TYPE_ONLY_TEXT", "richText": {"text": "Ka-ching! The {#INSIGHT_VARIABLE_IDENTIFIER#} week of {#INSIGHT_VARIABLE_MONTH#} was when your spends 🚀 at {#INSIGHT_VARIABLE_PERCENT#}% of your spends"}, "plainText": {"text": "Ka-ching! The {#INSIGHT_VARIABLE_IDENTIFIER#} week of {#INSIGHT_VARIABLE_MONTH#} was when your spends 🚀 at {#INSIGHT_VARIABLE_PERCENT#}% of your spends"}}'
where FRAMEWORK_ID='2691e6ab-0434-4d85-b095-35196338ea22';
