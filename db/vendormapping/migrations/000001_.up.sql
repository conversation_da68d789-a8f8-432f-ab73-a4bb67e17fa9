CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

/*mapping for any id requested by the client*/
CREATE TABLE IF NOT EXISTS be_vendor_mappings
(
    input_id            VARCHAR NOT NULL,
    freshdesk_id        VARCHAR NOT NULL UNIQUE,
    ozonetel_id         VARCHAR NOT NULL UNIQUE,
    loylty_id           VARCHAR NOT NULL UNIQUE,
    created_at          TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at          TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (input_id)
);
/*index on input id as it always be used in where condition*/
CREATE INDEX be_vm_input_id_index ON be_vendor_mappings (input_id);

/*specific to Data Platform - Rudder Stack in specific use cases.*/
CREATE TABLE IF NOT EXISTS dp_vendor_mappings
(
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    prospect_id         VARCHAR,
    actor_id            VARCHAR,
    firehose_id         VARCHAR NOT NULL UNIQUE,
    amplitude_id        VARCHAR NOT NULL UNIQUE,
    moengage_id         VARCHAR NOT NULL UNIQUE,
    created_at          TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at          TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

/*index on prospect id as it be used in where condition by events*/
CREATE INDEX dp_vm_prospect_id_index ON dp_vendor_mappings (prospect_id);
/*index on actor id as it always be used in where condition by events*/
CREATE INDEX dp_vm_actor_id_index ON dp_vendor_mappings (actor_id);
