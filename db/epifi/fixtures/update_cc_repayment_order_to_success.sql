-- https://monorail.pointz.in/p/fi-app/issues/detail?id=65271
-- Fixtures to update CC orders to PAID when txn mapped is successful
-- Discussion https://epifi.slack.com/archives/C0101Q3TXFF/p1698654328220029

UPDATE orders SET status = 'PAID', updated_at = NOW() where id = 'ODVxF9vRZbTe2FvsQCmnNFUg231028==' AND status = 'PAYMENT_FAILED' ;

-- Updating the workflow request and workflow stage history associated with order to correct status
UPDATE workflow_requests set status = 'SUCCESSFUL', updated_at = NOW() where id = 'WFRf33Gvlk+RG2v9qKwZ5q5qg231028==';
UPDATE workflow_histories set status = 'SUCCESSFUL', updated_at = NOW() where wf_req_id = 'WFRf33Gvlk+RG2v9qKwZ5q5qg231028==' and stage = 'PAYMENT';


