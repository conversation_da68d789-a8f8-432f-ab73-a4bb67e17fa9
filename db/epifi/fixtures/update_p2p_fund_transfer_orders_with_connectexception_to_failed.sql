-- https://monorail.pointz.in/p/fi-app/issues/detail?id=37223
-- https://portal.federalbank.co.in/redmine/issues/5762
-- Federal has confirmed these orders are failed

UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230217h0IdTmiVTsWTJI2y3H5SHw==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230105JQUSWd5pTXGby4pTHMwTrw==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230206rtqrTYUFT1etdtPO5uIm3Q==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230206XUnXbI2qTUGx4PcILNVFWQ==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230206ryUABN1uSOmypaftbsjsKA==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230206f+3B8WYlQTa1+wBCUFE0CQ==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230206f5uNSaViTo+eqMjJqjOh8Q==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230206OIM9iegATsOb+0vCSh68dg==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230206/jX/03tCS6CE4s3fZRu5IQ==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN2302067UAE0xslTT6ujTWlBh8oOg==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230206nKF2llLFSPmkt/UleeCwIw==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230206GuAsinAjTjSwThVW7Nl0tQ==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230206ZzOdA8CXT/W3KO0o5poprQ==' AND status = 'MANUAL_INTERVENTION' ;
UPDATE transactions SET status = 'FAILED', updated_at = NOW() WHERE id = 'TXN230317Lc9cZe5yQUC+6tSY4OtkXA==' AND status = 'UNKNOWN' ;

UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230217w3iTNUaWRzauDq5PqHLfiA==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230105EHg830awSEi3TNJbcCFcyQ==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD2302060oPPbyjRQ32fBTcxuPZw0Q==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230206Ak4lMP9ySfG3gvIoqJ7pwA==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230206W6hxD6lQQRO4zuPLyUXcTA==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD2302061zRM/OLvQLG0MWfhJCfPAA==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230206WzTgRIbqTH27+jEU3VS16g==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD2302062frbzJHSQSmGjGSYnev3ow==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230206g1T/PNYfTVipSoItLBzdPw==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230206AhhmgDSET3yLXMrBeOwTlA==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230206LRK3eZTbRFyxWZwAN3B29Q==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230206RFX48yehT9iNvKdGHtO1WA==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230206I5KK5lKsSjGKQU8K7xXlFQ==' AND status = 'IN_PAYMENT' ;
UPDATE orders SET status = 'PAYMENT_FAILED', updated_at = NOW() where id = 'OD230317ue6o58WjRhqq//gSmLPJbQ==' AND status = 'IN_PAYMENT' ;


