CREATE TABLE IF NOT EXISTS kyc_vendor_data
(
    id             UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    kyc_attempt_id UUID,
    payload_type    STRING,
    ttl_in_sec     INT64,
    payload        JSONB,
    created_at     TIMESTAMPTZ      NOT NULL DEFAULT NOW(),
    updated_at     TIMESTAMPTZ      NOT NULL DEFAULT NOW(),
    deleted_at     TIMESTAMPTZ,
    CONSTRAINT fk_kyc_attempt_id FOREIGN KEY (kyc_attempt_id) REFERENCES kyc_attempts (attempt_id)
);
