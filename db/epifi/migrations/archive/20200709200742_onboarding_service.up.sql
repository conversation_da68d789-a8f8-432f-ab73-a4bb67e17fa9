CREATE TABLE IF NOT EXISTS Onboarding_Details (
    id                       UUID PRIMARY KEY NOT NULL DEFAULT gen_random_uuid(),
    actor_id                 STRING NOT NULL,
    stage_details            JSONB,
    account_info             JSONB,
    card_info                JSONB,
    vendor                   STRING NOT NULL,
    current_onboarding_stage STRING,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMPTZ
)
