CREATE TABLE IF NOT EXISTS paused_events (
	id STRING NOT NULL UNIQUE,
	event_name STRING NOT NULL,  -- name of the event/stage/service that needs to be paused/blocked
	start_time TIMESTAMPTZ NOT NULL,
	end_time TIMESTAMPTZ NOT NULL CHECK (end_time > NOW() AND end_time >= start_time),
	display_message JSONB,  -- any message that we may want to display to the client

	created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
	deleted_at TIMESTAMPTZ,

	PRIMARY KEY (event_name, end_time, start_time)
)
