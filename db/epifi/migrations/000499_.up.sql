CREATE TABLE IF NOT EXISTS upi_request_logs (
	id               UUID               NOT NULL DEFAULT gen_random_uuid(),
	actor_id         STRING             NOT NULL,
	account_id       STRING             NOT NULL,
	vendor 			 STRING             NOT NULL,
	status			 STRING   			NOT NULL,
	api_type		 STRING				NOT NULL,
	detailed_status  JSONB				NOT NULL,
	vendor_req_id 	 STRING				NOT NULL,
	-- standard time stamp fields
	created_at       TIMESTAMPTZ    	NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at       TIMESTAMPTZ    	NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at       TIMESTAMPTZ    	NULL,
	PRIMARY KEY(id),
	UNIQUE(vendor_req_id),
	INDEX upi_request_logs_updated_at_idx (updated_at DESC),
	INDEX upi_request_logs_actor_id_account_id_idx (actor_id ASC, account_id ASC),
	INDEX upi_request_logs_account_id_api_type_idx (account_id ASC, api_type ASC),
	INDEX upi_request_logs_vendor_req_id_idx (vendor_req_id ASC),
	FAMILY "frequently_updated" (updated_at, status, detailed_status),
	FAMILY "primary"(id, actor_id, account_id, vendor, api_type, created_at, deleted_at, vendor_req_id)
	);
COMMENT ON TABLE upi_request_logs IS 'stores the response codes from the vendor for all the upi requests for an actor';
COMMENT ON COLUMN upi_request_logs.id IS 'stores the unique random id generated by default and acts as a primary key';
COMMENT ON COLUMN upi_request_logs.actor_id IS 'actor id corresponding to the user';
COMMENT ON COLUMN upi_request_logs.account_id IS 'account id of the upi account corresponding to which the request/response are logged';
COMMENT ON COLUMN upi_request_logs.vendor IS '{"proto_type":"vendorgateway.Vendor", "comment":"stores the Tpap partner bank that epifi integrates with", "ref":"api.vendorgateway.vendor.proto"';
COMMENT ON COLUMN upi_request_logs.status IS '{"proto_type":"upi.onboarding.enums.UpiRequestLogApiStatus", "comment":"stores the status of the request initiated with vendor", "ref":"api.upi.onboarding.enums.upi_request_logs_enums.proto"}';
COMMENT ON COLUMN upi_request_logs.api_type IS '{"proto_type":"upi.onboarding.enums.UpiRequestLogApiType", "comment":"stores the api corresponding to the request initiated with vendor", "ref":"api.upi.onboarding.enums.upi_request_logs_enums.proto"}';
COMMENT ON COLUMN upi_request_logs.detailed_status IS '{"proto_type":"upi.onboarding.DetailedStatus", "comment":"stores the request codes and descriptions", "ref":"api.upi.onboarding.upi_request_logs.proto"}';
COMMENT ON COLUMN upi_request_logs.vendor_req_id IS 'stores the request id for the request initiated with vendor';
