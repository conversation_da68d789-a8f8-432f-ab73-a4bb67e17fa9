CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;
COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';
CREATE TABLE public.schema_migrations (
    version bigint NOT NULL,
    dirty boolean NOT NULL
);
CREATE TABLE public.segments (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    provider_segment_id character varying NOT NULL,
    provider_name character varying NOT NULL,
    segment_type character varying NOT NULL,
    export_till timestamp with time zone NOT NULL,
    provider_segment_metadata jsonb NOT NULL,
    segment_details jsonb NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone
);
COMMENT ON TABLE public.segments IS 'stores mapping of provider segment id and internal segment id';
COMMENT ON COLUMN public.segments.provider_segment_id IS 'stores provider generated segment id';
COMMENT ON COLUMN public.segments.provider_name IS 'stores provider name like <PERSON>WS or G<PERSON>';
COMMENT ON COLUMN public.segments.segment_type IS 'stores segment type enum, which can be either static or dynamic';
COMMENT ON COLUMN public.segments.export_till IS 'stores timestamp upto which auto export should be supported';
COMMENT ON COLUMN public.segments.provider_segment_metadata IS 'stores provider specific segment metadata in raw form';
COMMENT ON COLUMN public.segments.segment_details IS 'stores segment specific details in raw form';
CREATE TABLE public.segments_metadata (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    segment_name character varying NOT NULL,
    query_filter character varying,
    segment_type character varying NOT NULL,
    status character varying NOT NULL,
    owner character varying NOT NULL,
    approval_status boolean,
    reviewed_by character varying,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    deleted_at timestamp with time zone,
    segment_path character varying
);
COMMENT ON TABLE public.segments_metadata IS 'stores segments metadata';
COMMENT ON COLUMN public.segments_metadata.segment_name IS 'stores segment name provided by owner';
COMMENT ON COLUMN public.segments_metadata.query_filter IS 'stores query filter for getting segment';
COMMENT ON COLUMN public.segments_metadata.segment_type IS 'stores segment type which can be either static or dynamic';
COMMENT ON COLUMN public.segments_metadata.status IS 'stores segment status which can be either active or inactive, only active segment will be used';
COMMENT ON COLUMN public.segments_metadata.owner IS 'stores segment owner email';
COMMENT ON COLUMN public.segments_metadata.approval_status IS 'stores approval status, either true or false, only true segment will be process';
COMMENT ON COLUMN public.segments_metadata.reviewed_by IS 'stores segment reviewer email';
COMMENT ON COLUMN public.segments_metadata.expires_at IS 'stores segment expiry date';
ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);
ALTER TABLE ONLY public.segments_metadata
    ADD CONSTRAINT segments_metadata_pkey PRIMARY KEY (id);
ALTER TABLE ONLY public.segments_metadata
    ADD CONSTRAINT segments_metadata_segment_type_query_filter_segment_path_key UNIQUE (segment_type, query_filter, segment_path);
ALTER TABLE ONLY public.segments
    ADD CONSTRAINT segments_pkey PRIMARY KEY (id);
CREATE INDEX segments_metadata_updated_at_idx ON public.segments_metadata USING btree (updated_at DESC);
CREATE UNIQUE INDEX segments_provider_segment_id_provider_name_key ON public.segments USING btree (provider_segment_id, provider_name);
CREATE INDEX segments_updated_at_key ON public.segments USING btree (updated_at);
