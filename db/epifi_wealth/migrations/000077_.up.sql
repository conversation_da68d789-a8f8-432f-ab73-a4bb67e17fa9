CREATE INDEX IF NOT EXISTS idx_account_ref_id_txn_date_deleted_at_unix ON aa_transactions(account_reference_id, transaction_date, deleted_at_unix ASC);

CREATE INDEX IF NOT EXISTS idx_account_ref_id_txn_id_deleted_at_unix ON aa_transactions(account_reference_id, txn_id, deleted_at_unix ASC);

CREATE INDEX IF NOT EXISTS idx_actor_id_txn_date_deleted_at_unix ON aa_transactions(actor_id, transaction_date, deleted_at_unix ASC);

CREATE INDEX IF NOT EXISTS idx_actor_id_deleted_at_unix ON aa_transactions(actor_id, deleted_at_unix ASC);

DROP INDEX IF EXISTS aa_transactions@account_reference_id_idx;
CREATE INDEX IF NOT EXISTS idx_account_ref_id_deleted_at_unix ON aa_transactions(account_reference_id, deleted_at_unix ASC);

CREATE INDEX IF NOT EXISTS idx_id_deleted_at_unix ON aa_transactions(id, deleted_at_unix ASC);
