UPSERT
INTO mf_filter_groups (id, name, type, level, display_info, weight, enabled)
VALUES ('MFFG22060955kYGNBPQgOM8p39UPI12A==', 'FUND_TYPES', 'NESTED_HIERARCHY', 'LEVEL_1', jsonb'{"name": "Fund types", "info": "Mutual funds are divided into several kinds of categories, representing the kinds of securities they have targeted for their portfolios and the type of returns they seek. There is a fund for nearly every type of investor or investment approach."}', 10, true),
       ('MFFG220609G6gu/ULvQGSqQNkrEFmUIw==', 'EQUITY_FUND_TYPES', 'FLAT', 'LEVEL_2', jsonb'{"name": "Equity fund types", "info": "Such schemes select & manage stock portfolios on your behalf. That is, your money is placed in stocks — certificates that claim you own a microscopic slice of a company.\nThere''s an element of risk in such investments, as it depends on how well a company fares."}', 20, true),
	   ('MFFG220609v5/b1OodQgW33eIZGycg1w==', 'DEBT_FUND_TYPES', 'FLAT', 'LEVEL_2', jsonb'{"name": "Debt fund types", "info": "Debt is a way for companies/governments to raise money; your investment is a loan to them. Borrowers typically return the cash in monthly/yearly interest payments & a principal payout.\nDebt funds invest in safe bonds where payback is consistent — it''s less riskier than equity."}', 30, true),
	   ('MFFG220609LnIzm3V+RTuJ0zK1ZYyJ+w==', 'CASH_FUND_TYPES', 'FLAT', 'LEVEL_2', jsonb'{"name": "Cash fund types", "info": "These mutual funds are the closest equivalent to owning cash.\nSub-categories within these funds are Overnight Fund and Liquid Fund."}', 40, true),
	   ('MFFG220609FxY8DmclSXSWvimQ3hFAJw==', 'HYBRID_FUNDS_BY_ALLOCATION', 'FLAT', 'LEVEL_2', jsonb'{"name": "Hybrid funds by allocation", "info": ""}', 50, true),
	   ('MFFG220609AHhzglBKTWKmu8gAfwYuUw==', 'HYBRID_FUNDS_BY_SOLUTION', 'FLAT', 'LEVEL_2', jsonb'{"name": "Hybrid funds by solution", "info": ""}', 60, false),
	   ('MFFG220609vxyOkGGfTMeYsNQvwJaIuw==', 'INDEX_FUND_TYPES', 'FLAT', 'LEVEL_2', jsonb'{"name": "Index fund types", "info": ""}', 70, true),
	   ('MFFG220609vxyOkGGfTMeYsNQvwPaUam==', 'COMMODITIES_FUND_TYPES', 'FLAT', 'LEVEL_2', jsonb'{"name": "Commodity fund types", "info": ""}', 75, true),

	   ('MFFG220609MxJFL1LxTYqOaMKf+43FaA==', 'RETURNS', 'INLINE_HIERARCHY', 'LEVEL_1', jsonb'{"name": "Returns", "info": "One of the critical parameters while choosing an investment. This number shows how much your invested money has grown on average every year.\nFor equity funds, it is generally better to look at longer-term returns like 5-7 years."}', 80, true),
	   ('MFFG220609R8NDsDs1TrOllBw9b56VbQ==', 'FUND_EXPENSE_RATIO', 'FLAT', 'LEVEL_1', jsonb'{"name": "Fund house expense (Expense ratio)", "info": "All fund houses charge an annual fee to research & manage your assets. Usually, it''s a tiny percentage that gets automatically deducted from your overall returns."}', 90, true),
	   ('MFFG220609sc40LXiPQMmnvOcTYCsQXg==', 'RISK_PROFILE', 'FLAT', 'LEVEL_1', jsonb'{"name": "Risk Profile", "info": "Funds have specific periods when they do well. But it''s crucial to consider if a fund will replicate its past returns.\n''Risk'' hints at the probability of deviating from past performance trends. The norm is: Expect high returns only if you''re ready to deal with high risk."}', 100, true),
	   ('MFFG220609b7r60P9MTrSXZofg6IQzEw==', 'FUND_SIZE', 'FLAT', 'LEVEL_1', jsonb'{"name": "Fund size (AUM)", "info": "It denotes the total amount of collective money managed by a fund. A significantly large AuM (Assets Under Management) means many investors have faith in the fund."}', 110, true),
	   ('MFFG2206099CMrdBDyRN6nn6Yo8vVnTw==', 'FUND_HOUSES', 'DROPDOWN', 'LEVEL_1', jsonb'{"name": "Fund houses", "next_screen_intro_text": "Select one or more fund houses", "info": "Fund Houses are SEBI-regulated companies that manage your investments. They assign a legion of expert money managers who research the market & remain responsible for your funds."}', 120, true),
	   ('MFFG220609eRGA3vE2Q9ibbtMOuTIOXw==', 'GEOGRAPHY_FOCUS', 'FLAT', 'LEVEL_1', jsonb'{"name": "Geography focus", "info": ""}', 130, false),
	   ('MFFG220609CQo7vbGhTOSsmDp1bUP0Ug==', 'SECTOR_CONCENTRATIONS', 'INLINE_HIERARCHY', 'LEVEL_1', jsonb'{"name": "Sector concentrations", "info": "Sectoral funds invest in companies from a particular sector (IT, Banking, Infrastructure etc.).\nSuch pointed investments are a good idea — especially when you know a specific sector is poised to take off."}', 140, true),
	   ('MFFG220609SxwlrUPHSaeVGA6LJZ6XRg==', 'INV_STYLE', 'FLAT', 'LEVEL_1', jsonb'{"name": "Investment style", "info": ""}', 150, false),
	   ('MFFG220609ugmMjwNpTdaVS4rQaY2hng==', 'MIN_AUTO_INV_AMT', 'FLAT', 'LEVEL_1', jsonb'{"name": "Minimum SIP amount", "info": ""}', 160, true),
	   ('MFFG220609FE3Mgeb3TViXsGJzZOAgaA==', 'MIN_OT_INV_AMT', 'FLAT', 'LEVEL_1', jsonb'{"name": "Minimum one-time amount", "info": "The bare minimum one-time investment amount which a Fund House accepts for a particular fund."}', 170, true),
	   ('MFFG2206092Ciy/Lf3R3Su0BdL3dCl9w==', 'CREDIT_QUALITY', 'FLAT', 'LEVEL_1', jsonb'{"name": "Credit quality", "info": "Debt funds invest in bonds of various companies. But, just like any other loan, there''s a risk of a company defaulting —  i.e. you lose cash.\nCredit Quality shows a company''s creditworthiness based on an analysis done by recognised agencies like Crisil. Low quality=High risk."}', 180, true),
	   ('MFFG220609StEVeQsqT1Ca4y91BubDMA==', 'FUND_AGE', 'FLAT', 'LEVEL_1', jsonb'{"name": "Fund age", "info": "The number of years a Mutual Fund has been active and open to investments. The older a fund, the better — especially if it has proven itself with steady performances over the years."}', 190, true),
	   ('MFFG220609U8o5x7lTQKiTrRy/WIwF3Q==', 'FUND_MANAGERS', 'DROPDOWN', 'LEVEL_1', jsonb'{"name": "Fund managers", "next_screen_intro_text": "Select one or more managers", "info": "This is the person your Fund House delegates all the investment responsibilities to. The fund manager is responsible for the investment strategy that generates returns for your funds."}', 200, true),
	   ('MFFG220609vj6K1dATSGeGzr2f4FhYTQ==', 'SUGGESTED_INV_DURATION', 'FLAT', 'LEVEL_1', jsonb'{"name": "Suggested investment duration", "info": "The suggested number of years to enjoy the full potential of a fund. It may vary based on the fund''s risk profile and asset class."}', 210, true),
       ('MFFG221020gyv1BgnUSGqeXmWf4Orvsg==', 'YIELD_TO_MATURITY', 'FLAT', 'LEVEL_1', jsonb'{"name": "Expected interest rate", "info": "It is the potential interest that an investor is likely to earn per annum if the investment is held long term, also known as Yield to Maturity"}', 220, true);

WITH filter_data (name, type, query_info, display_info, filter_group_id, child_group_ids, weight, enabled) AS (
	VALUES
	       ('EQUITY_FUND_TYPE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Equity", "selectedText": "Equity"}', 'FUND_TYPES', ARRAY ['EQUITY_FUND_TYPES'], 10, true),
		   ('DEBT_FUND_TYPE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Debt", "selectedText": "Debt"}', 'FUND_TYPES', ARRAY ['DEBT_FUND_TYPES'], 20, true),
		   ('CASH_FUND_TYPE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Cash", "selectedText": "Cash"}', 'FUND_TYPES', ARRAY ['CASH_FUND_TYPES'], 30, true),
		   ('HYBRID_FUND_TYPE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Hybrid", "selectedText": "Hybrid"}', 'FUND_TYPES', ARRAY ['HYBRID_FUNDS_BY_ALLOCATION', 'HYBRID_FUNDS_BY_SOLUTION'], 40, true),
		   ('INDEX_FUND_TYPE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Index", "selectedText": "Index"}', 'FUND_TYPES', ARRAY ['INDEX_FUND_TYPES'], 50, true),
		   ('COMMODITIES_FUND_TYPE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Commodity", "selectedText": "Commodity"}', 'FUND_TYPES', ARRAY ['COMMODITIES_FUND_TYPES'], 50, true),

		   -- Equity filters
		   ('MULTI_CAP_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Multi cap", "selectedText": "Multi cap"}', 'EQUITY_FUND_TYPES', null, 10, true),
		   ('LARGE_CAP_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Large cap", "selectedText": "Large cap"}', 'EQUITY_FUND_TYPES', null, 20, true),
		   ('LARGE_AND_MID_CAP_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Large & Mid cap", "selectedText": "Large & Mid cap"}', 'EQUITY_FUND_TYPES', null, 30, true),
		   ('MID_CAP_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mid cap", "selectedText": "Mid cap"}', 'EQUITY_FUND_TYPES', null, 40, true),
		   ('SMALL_CAP_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Small cap", "selectedText": "Small cap"}', 'EQUITY_FUND_TYPES', null, 50, true),
		   ('DIVIDEND_YIELD_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dividend yield", "selectedText": "Dividend yield"}', 'EQUITY_FUND_TYPES', null, 60, true),
		   ('VALUE_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Value", "selectedText": "Value"}', 'EQUITY_FUND_TYPES', null, 70, true),
		   ('CONTRA_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Contra", "selectedText": "Contra"}', 'EQUITY_FUND_TYPES', null, 80, true),
		   ('FOCUSED_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Focused", "selectedText": "Focused"}', 'EQUITY_FUND_TYPES', null, 90, true),
		   ('SECTORAL_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sectoral", "selectedText": "Sectoral"}', 'EQUITY_FUND_TYPES', null, 100, true),
		   ('TAX_SAVING_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Tax saving", "selectedText": "Tax saving"}', 'EQUITY_FUND_TYPES', null, 110, true),
		   ('FLEXI_CAP_EQUITY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Flexi cap", "selectedText": "Flexi cap"}', 'EQUITY_FUND_TYPES', null, 120, true),
		   ('INTERNATIONAL_EQUITY_SOL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Global", "selectedText": "Global"}', 'EQUITY_FUND_TYPES', null, 130, true),

		   -- Debt filters
		   ('ULTRA_SHORT_DURATION_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ultra short duration", "selectedText": "Ultra short duration", "display_sub_text": "3 to 6 months"}', 'DEBT_FUND_TYPES', null, 20, true),
		   ('SHORT_DURATION_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Short duration", "selectedText": "Short duration", "display_sub_text": "1 to 3 years"}', 'DEBT_FUND_TYPES', null, 30, true),
		   ('LOW_DURATION_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Low duration", "selectedText": "Low duration", "display_sub_text": ""}', 'DEBT_FUND_TYPES', null, 35, true),
		   ('MEDIUM_DURATION_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Medium duration", "selectedText": "Medium duration", "display_sub_text": "3 to 4 years"}', 'DEBT_FUND_TYPES', null, 40, true),
		   ('MEDIUM_LONG_DURATION_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Medium to long duration", "selectedText": "Medium to long duration", "display_sub_text": "4 to 7 years"}', 'DEBT_FUND_TYPES', null, 50, true),
		   ('LONG_DURATION_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Long duration", "selectedText": "Long duration", "display_sub_text": "more than 7 years"}', 'DEBT_FUND_TYPES', null, 60, true),
		   ('DYNAMIC_BOND_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dynamic bond", "selectedText": "Dynamic bond", "display_sub_text": "mixed durations"}', 'DEBT_FUND_TYPES', null, 70, true),
		   ('CREDIT_RISK_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Credit risk", "selectedText": "Credit risk"}', 'DEBT_FUND_TYPES', null, 80, true),
		   ('BANKING_PSU_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Banking & PSU", "selectedText": "Banking & PSU"}', 'DEBT_FUND_TYPES', null, 90, true),
		   ('FLOATER_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Floater", "selectedText": "Floater"}', 'DEBT_FUND_TYPES', null, 100, true),
		   ('CORPORATE_BOND_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Corporate bond", "selectedText": "Corporate bond"}', 'DEBT_FUND_TYPES', null, 110, true),
		   ('GILT_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gilt", "selectedText": "Gilt"}', 'DEBT_FUND_TYPES', null, 120, true),
		   ('GILT_10Y_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gilt fund with 10y", "selectedText": "Gilt fund with 10y"}', 'DEBT_FUND_TYPES', null, 130, true),
		   ('MONEY_MARKET_CASH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Money market", "selectedText": "Money market"}', 'DEBT_FUND_TYPES', null, 140, true),

		   -- Cash filters
		   ('LIQUID_DEBT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Liquid", "selectedText": "Liquid", "display_sub_text": "upto 90 days"}', 'CASH_FUND_TYPES', null, 5, true),
		   ('OVERNIGHT_CASH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Overnight", "selectedText": "Overnight"}', 'CASH_FUND_TYPES', null, 10, true),

		   -- Hybrid funds by allocation
		   ('CONSERVATIVE_HYBRID_ALLOC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Conservative", "selectedText": "Conservative"}', 'HYBRID_FUNDS_BY_ALLOCATION', null, 10, true),
		   ('BALANCED_HYBRID_ALLOC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Balanced", "selectedText": "Balanced"}', 'HYBRID_FUNDS_BY_ALLOCATION', null, 20, true),
		   ('AGGRESSIVE_HYBRID_ALLOC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Aggressive", "selectedText": "Aggressive"}', 'HYBRID_FUNDS_BY_ALLOCATION', null, 30, true),
		   ('DYNAMIC_ASSET_HYBRID_ALLOC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dynamic asset", "selectedText": "Dynamic asset"}', 'HYBRID_FUNDS_BY_ALLOCATION', null, 40, true),
		   ('MULTI_ASSET_HYBRID_ALLOC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Multi asset", "selectedText": "Multi asset"}', 'HYBRID_FUNDS_BY_ALLOCATION', null, 50, true),
		   ('ARBITRAGE_HYBRID_ALLOC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Arbitrage", "selectedText": "Arbitrage"}', 'HYBRID_FUNDS_BY_ALLOCATION', null, 60, true),
		   ('EQUITY_SAVINGS_HYBRID_ALLOC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Equity Savings", "selectedText": "Equity Savings"}', 'HYBRID_FUNDS_BY_ALLOCATION', null, 70, true),
		   ('FOF_HYBRID_ALLOC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Fund of funds", "selectedText": "Fund of funds"}', 'HYBRID_FUNDS_BY_ALLOCATION', null, 70, true),

		   -- Index fund types
		   ('LARGE_CAP_INDEX', 'TERMED', jsonb'{}', jsonb'{"displayName": "Large cap", "selectedText": "Large cap"}', 'INDEX_FUND_TYPES', null, 10, true),
		   ('MID_CAP_INDEX', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mid cap", "selectedText": "Mid cap"}', 'INDEX_FUND_TYPES', null, 20, true),
		   ('SMALL_CAP_INDEX', 'TERMED', jsonb'{}', jsonb'{"displayName": "Small cap", "selectedText": "Small cap"}', 'INDEX_FUND_TYPES', null, 30, true),
		   ('US_FOCUSED_INDEX', 'TERMED', jsonb'{}', jsonb'{"displayName": "US focused", "selectedText": "US focused"}', 'INDEX_FUND_TYPES', null, 40, false),
		   ('OTHERS_INDEX', 'TERMED', jsonb'{}', jsonb'{"displayName": "Others", "selectedText": "Others"}', 'INDEX_FUND_TYPES', null, 50, true),

	       -- COMMODITIES_FUND_TYPE
		   ('GOLD_COMMODITIES', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gold", "selectedText": "Gold"}', 'COMMODITIES_FUND_TYPES', null, 10, true),
		   ('SILVER_COMMODITIES', 'TERMED', jsonb'{}', jsonb'{"displayName": "Silver", "selectedText": "Silver"}', 'COMMODITIES_FUND_TYPES', null, 10, true),

		   -- Returns
		   -- Note: 6 months is not supported
           ('UPTO_5_PER_1_MON_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "upto 5%,1 month", "selectedText": "upto 5% returns • 1m"}', 'RETURNS', null, 10, true),
           ('UPTO_5_PER_6_MON_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "upto 5%,6 months", "selectedText": "upto 5% returns • 6m"}', 'RETURNS', null, 20, true),
		   ('UPTO_5_PER_5_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "upto 5%,5 year", "selectedText": "upto 5% returns • 5y"}', 'RETURNS', null, 50, true),
		   ('UPTO_5_PER_3_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "upto 5%,3 year", "selectedText": "upto 5% returns • 3y"}', 'RETURNS', null, 40, true),
		   ('UPTO_5_PER_1_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "upto 5%,1 year", "selectedText": "upto 5% returns • 1y"}', 'RETURNS', null, 30, true),

           ('6_TO_10_PER_1_MON_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "6 to 10%,1 month", "selectedText": "6 to 10% returns • 1m"}', 'RETURNS', null, 60, true),
           ('6_TO_10_PER_6_MON_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "6 to 10%,6 months", "selectedText": "6 to 10% returns • 6m"}', 'RETURNS', null, 70, true),
		   ('6_TO_10_PER_5_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "6 to 10%,5 year", "selectedText": "6 to 10% returns • 5y"}', 'RETURNS', null, 100, true),
		   ('6_TO_10_PER_3_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "6 to 10%,3 year", "selectedText": "6 to 10% returns • 3y"}', 'RETURNS', null, 90, true),
		   ('6_TO_10_PER_1_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "6 to 10%,1 year", "selectedText": "6 to 10% returns • 1y"}', 'RETURNS', null, 80, true),

           ('11_TO_15_PER_1_MON_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "11 to 15%,1 month", "selectedText": "11 to 15% returns • 1m"}', 'RETURNS', null, 110, true),
           ('11_TO_15_PER_6_MON_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "11 to 15%,6 months", "selectedText": "11 to 15% returns • 6m"}', 'RETURNS', null, 120, true),
		   ('11_TO_15_PER_5_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "11 to 15%,5 year", "selectedText": "11 to 15% returns • 5y"}', 'RETURNS', null, 150, true),
		   ('11_TO_15_PER_3_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "11 to 15%,3 year", "selectedText": "11 to 15% returns • 3y"}', 'RETURNS', null, 140, true),
		   ('11_TO_15_PER_1_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "11 to 15%,1 year", "selectedText": "11 to 15% returns • 1y"}', 'RETURNS', null, 130, true),

           ('16_TO_20_PER_1_MON_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "16 to 20%,1 month", "selectedText": "16 to 20% returns • 1m"}', 'RETURNS', null, 160, true),
           ('16_TO_20_PER_6_MON_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "16 to 20%,6 months", "selectedText": "16 to 20% returns • 6m"}', 'RETURNS', null, 170, true),
		   ('16_TO_20_PER_5_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "16 to 20%,5 year", "selectedText": "16 to 20% returns • 5y"}', 'RETURNS', null, 200, true),
		   ('16_TO_20_PER_3_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "16 to 20%,3 year", "selectedText": "16 to 20% returns • 3y"}', 'RETURNS', null, 190, true),
		   ('16_TO_20_PER_1_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "16 to 20%,1 year", "selectedText": "16 to 20% returns • 1y"}', 'RETURNS', null, 180, true),

           ('GREATER_THAN_20_PER_1_MON_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "greater than 20%,1 month", "selectedText": "greater than 20% returns • 1m"}', 'RETURNS', null, 210, true),
           ('GREATER_THAN_20_PER_6_MON_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "greater than 20%,6 months", "selectedText": "greater than 20% returns • 6m"}', 'RETURNS', null, 220, true),
		   ('GREATER_THAN_20_PER_5_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "greater than 20%,5 year", "selectedText": "greater than 20% returns • 5y"}', 'RETURNS', null, 250, true),
		   ('GREATER_THAN_20_PER_3_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "greater than 20%,3 year", "selectedText": "greater than 20% returns • 3y"}', 'RETURNS', null, 240, true),
		   ('GREATER_THAN_20_PER_1_YR_RETURNS', 'TERMED', jsonb'{}', jsonb'{"displayName": "greater than 20%,1 year", "selectedText": "greater than 20% returns • 1y"}', 'RETURNS', null, 230, true),

           -- Yield To Maturity
           ('GREATER_THAN_1_PER_YIELD_TO_MATURITY','TERMED',jsonb'{}', jsonb'{"displayName": "greater than 1%", "selectedText": "greater than 1%"}', 'YIELD_TO_MATURITY', null, 260, true),
           ('GREATER_THAN_3_PER_YIELD_TO_MATURITY','TERMED',jsonb'{}', jsonb'{"displayName": "greater than 3%", "selectedText": "greater than 3%"}', 'YIELD_TO_MATURITY', null, 270, true),
           ('GREATER_THAN_5_PER_YIELD_TO_MATURITY','TERMED',jsonb'{}', jsonb'{"displayName": "greater than 5%", "selectedText": "greater than 5%"}', 'YIELD_TO_MATURITY', null, 280, true),
           ('GREATER_THAN_7_PER_YIELD_TO_MATURITY','TERMED',jsonb'{}', jsonb'{"displayName": "greater than 7%", "selectedText": "greater than 7%"}', 'YIELD_TO_MATURITY', null, 290, true),

           -- Fund house expense (Expense ratio)
		   ('UPTO_POINT5_PER_EXPENSE', 'TERMED', jsonb'{}', jsonb'{"displayName": "upto 0.5%", "selectedText": "upto 0.5% expense ratio"}', 'FUND_EXPENSE_RATIO', null, 10, true),
		   ('POINT51_TO_1_PER_EXPENSE', 'TERMED', jsonb'{}', jsonb'{"displayName": "0.51% to 1%", "selectedText": "0.51% to 1% expense ratio"}', 'FUND_EXPENSE_RATIO', null, 20, true),
		   ('1POINT1_TO_1POINT5PER_PER_EXPENSE', 'TERMED', jsonb'{}', jsonb'{"displayName": "1.1% to 1.5%", "selectedText": "1.1% to 1.5% expense ratio"}', 'FUND_EXPENSE_RATIO', null, 30, true),
		   ('GREATER_THAN_1POINT5PER_EXPENSE', 'TERMED', jsonb'{}', jsonb'{"displayName": "greater than 1.5%", "selectedText": "greater than 1.5% expense ratio"}', 'FUND_EXPENSE_RATIO', null, 40, true),

		   -- Risk Profile
		   ('VERY_HIGH_RISK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Very high", "selectedText": "Very high risk profile"}', 'RISK_PROFILE', null, 10, true),
		   ('HIGH_RISK', 'TERMED', jsonb'{}', jsonb'{"displayName": "High", "selectedText": "High risk profile"}', 'RISK_PROFILE', null, 20, true),
		   ('MODERATELY_HIGH_RISK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Moderately high", "selectedText": "Moderately high risk profile"}', 'RISK_PROFILE', null, 30, true),
		   ('MODERATE_RISK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Moderate", "selectedText": "Moderate risk profile"}', 'RISK_PROFILE', null, 40, true),
		   ('MODERATELY_LOW_RISK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Moderately low", "selectedText": "Moderately low risk profile"}', 'RISK_PROFILE', null, 50, true),
		   ('LOW_RISK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Low", "selectedText": "Low risk profile"}', 'RISK_PROFILE', null, 60, true),

		   -- Fund size (AUM)
		   ('UPTO_100CR_FUND_SIZE', 'TERMED', jsonb'{}', jsonb'{"displayName": "upto 100Cr", "selectedText": "upto 100Cr fund size"}', 'FUND_SIZE', null, 10, true),
		   ('101_TO_1000CR_FUND_SIZE', 'TERMED', jsonb'{}', jsonb'{"displayName": "101 to 1000Cr", "selectedText": "101 to 1000Cr fund size"}', 'FUND_SIZE', null, 20, true),
		   ('1001_TO_5000CR_FUND_SIZE', 'TERMED', jsonb'{}', jsonb'{"displayName": "1001 to 5000Cr", "selectedText": "1001 to 5000Cr fund size"}', 'FUND_SIZE', null, 30, true),
		   ('5001_TO_10KCR_FUND_SIZE', 'TERMED', jsonb'{}', jsonb'{"displayName": "5001 to 10k Cr", "selectedText": "5001 to 10k Cr fund size"}', 'FUND_SIZE', null, 40, true),
		   ('MORE_THAN_10KCR_FUND_SIZE', 'TERMED', jsonb'{}', jsonb'{"displayName": "more than 10k Cr", "selectedText": "more than 10k Cr fund size"}', 'FUND_SIZE', null, 50, true),

		   -- Fund houses
		   ('ADITYA_BIRLA_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Aditya Birla", "selectedText": "Aditya Birla"}', 'FUND_HOUSES', null, 10, true),
		   ('AXIS_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Axis", "selectedText": "Axis"}', 'FUND_HOUSES', null, 20, true),
		   ('BARODA_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Baroda", "selectedText": "Baroda"}', 'FUND_HOUSES', null, 30, false),
		   ('BNP_PARIBAS_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "BNP Paribas", "selectedText": "BNP Paribas"}', 'FUND_HOUSES', null, 40, true),
		   ('BOI_AXA_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "BOI AXA", "selectedText": "BOI AXA"}', 'FUND_HOUSES', null, 50, false),
		   ('CANARA_ROBECO_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Canara Robeco", "selectedText": "Canara Robeco"}', 'FUND_HOUSES', null, 60, true),
		   ('DSP_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "DSP", "selectedText": "DSP"}', 'FUND_HOUSES', null, 70, true),
		   ('EDELWEISS_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Edelweiss", "selectedText": "Edelweiss"}', 'FUND_HOUSES', null, 80, true),
		   ('FRANKLIN_TEMPLETON_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Franklin Templeton", "selectedText": "Franklin Templeton"}', 'FUND_HOUSES', null, 90, true),
		   ('HDFC_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "HDFC", "selectedText": "HDFC"}', 'FUND_HOUSES', null, 100, true),
		   ('HSBC_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "HSBC", "selectedText": "HSBC"}', 'FUND_HOUSES', null, 110, false),
		   ('ICICI_PRUDENTIAL_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "ICICI Prudential", "selectedText": "ICICI Prudential"}', 'FUND_HOUSES', null, 120, true),
		   ('IDBI_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "IDBI", "selectedText": "IDBI"}', 'FUND_HOUSES', null, 130, false),
		   ('IDFC_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Bandhan", "selectedText": "Bandhan"}', 'FUND_HOUSES', null, 140, true),
		   ('IIFL_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "360 ONE", "selectedText": "360 ONE"}', 'FUND_HOUSES', null, 150, true),
		   ('INDIABULLS_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Indiabulls", "selectedText": "Indiabulls"}', 'FUND_HOUSES', null, 160, false),
		   ('INVESCO_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Invesco", "selectedText": "Invesco"}', 'FUND_HOUSES', null, 170, true),
		   ('ITI_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "ITI", "selectedText": "ITI"}', 'FUND_HOUSES', null, 180, false),
		   ('JM_FINANCIAL_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "JM Financial", "selectedText": "JM Financial"}', 'FUND_HOUSES', null, 190, true),
		   ('KOTAK_MAHINDRA_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kotak Mahindra", "selectedText": "Kotak Mahindra"}', 'FUND_HOUSES', null, 200, true),
		   ('LIC_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "LIC", "selectedText": "LIC"}', 'FUND_HOUSES', null, 210, true),
		   ('LNT_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "L&T", "selectedText": "L&T"}', 'FUND_HOUSES', null, 220, true),
		   ('MAHINDRA_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mahindra Manulife", "selectedText": "Mahindra Manulife"}', 'FUND_HOUSES', null, 230, true),
		   ('MIRAE_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mirae Asset", "selectedText": "Mirae Asset"}', 'FUND_HOUSES', null, 240, true),
		   ('MOTILAL_OSWAL_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Motilal Oswal", "selectedText": "Motilal Oswal"}', 'FUND_HOUSES', null, 250, true),
		   ('NAVI_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Navi", "selectedText": "Navi"}', 'FUND_HOUSES', null, 260, true),
		   ('NIPPON_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nippon", "selectedText": "Nippon"}', 'FUND_HOUSES', null, 270, true),
		   ('NJ_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "NJ", "selectedText": "NJ"}', 'FUND_HOUSES', null, 280, false),
		   ('PGIM_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "PGIM", "selectedText": "PGIM"}', 'FUND_HOUSES', null, 290, true),
		   ('PPFAS_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "PPFAS", "selectedText": "PPFAS"}', 'FUND_HOUSES', null, 300, true),
		   ('PRINCIPAL_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Principal", "selectedText": "Principal"}', 'FUND_HOUSES', null, 310, false),
		   ('QUANT_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Quant", "selectedText": "Quant"}', 'FUND_HOUSES', null, 320, true),
		   ('QUANTUM_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Quantum", "selectedText": "Quantum"}', 'FUND_HOUSES', null, 330, false),
		   ('SAMCO_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Samco", "selectedText": "Samco"}', 'FUND_HOUSES', null, 340, false),
		   ('SBI_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "SBI", "selectedText": "SBI"}', 'FUND_HOUSES', null, 350, true),
		   ('SHRIRAM_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Shriram", "selectedText": "Shriram"}', 'FUND_HOUSES', null, 360, false),
		   ('SUNDARAM_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sundaram", "selectedText": "Sundaram"}', 'FUND_HOUSES', null, 370, true),
		   ('TATA_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Tata", "selectedText": "Tata"}', 'FUND_HOUSES', null, 380, true),
		   ('TAURUS_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Taurus", "selectedText": "Taurus"}', 'FUND_HOUSES', null, 390, true),
		   ('TRUST_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Trust", "selectedText": "Trust"}', 'FUND_HOUSES', null, 400, false),
		   ('UNION_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Union", "selectedText": "Union"}', 'FUND_HOUSES', null, 410, true),
		   ('UTI_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "UTI", "selectedText": "UTI"}', 'FUND_HOUSES', null, 420, true),
		   ('WHITEOAK_CAPITAL_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "WhiteOak Capital", "selectedText": "WhiteOak Capital"}', 'FUND_HOUSES', null, 430, false),
		   ('BAJAJ_FINSERV_AMC', 'TERMED', jsonb'{}', jsonb'{"displayName": "Bajaj Finserv", "selectedText": "Bajaj Finserv"}', 'FUND_HOUSES', null, 440, true),

		   -- Geography focus
		   -- Note: not supported
-- 		   ('INDIA_FOCUSSED_GEO', 'TERMED', jsonb'{}', jsonb'{"displayName": "India focussed", "selectedText": "India focussed"}', 'GEOGRAPHY_FOCUS', null, 10, true),
-- 		   ('US_FOCUSSED_GEO', 'TERMED', jsonb'{}', jsonb'{"displayName": "US focussed", "selectedText": "US focussed"}', 'GEOGRAPHY_FOCUS', null, 20, true),
-- 		   ('REST_WORLD_FOCUSSED_GEO', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rest of the world", "selectedText": "Rest of the world"}', 'GEOGRAPHY_FOCUS', null, 30, true),

		   -- Sector concentrations
		   ('PHARMA_LESS_THAN_10_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pharma,less than 10%", "selectedText": "Pharma • less than 10%"}', 'SECTOR_CONCENTRATIONS', null, 10, true),
		   ('PHARMA_11_TO_25_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pharma,11 to 25%", "selectedText": "Pharma • 11 to 25%"}', 'SECTOR_CONCENTRATIONS', null, 20, true),
		   ('PHARMA_26_TO_50_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pharma,26 to 50%", "selectedText": "Pharma • 26 to 50%"}', 'SECTOR_CONCENTRATIONS', null, 30, true),
		   ('PHARMA_51_TO_80_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pharma,51 to 80%", "selectedText": "Pharma • 51 to 80%"}', 'SECTOR_CONCENTRATIONS', null, 40, true),
		   ('PHARMA_GREATER_THAN_80_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pharma,greater than 80%", "selectedText": "Pharma • greater than 80%"}', 'SECTOR_CONCENTRATIONS', null, 50, true),

		   ('IT_LESS_THAN_10_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "IT & Tech,less than 10%", "selectedText": "IT & Tech • less than 10%"}', 'SECTOR_CONCENTRATIONS', null, 60, true),
		   ('IT_11_TO_25_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "IT & Tech,11 to 25%", "selectedText": "IT & Tech • 11 to 25%"}', 'SECTOR_CONCENTRATIONS', null, 70, true),
		   ('IT_26_TO_50_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "IT & Tech,26 to 50%", "selectedText": "IT & Tech • 26 to 50%"}', 'SECTOR_CONCENTRATIONS', null, 80, true),
		   ('IT_51_TO_80_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "IT & Tech,51 to 80%", "selectedText": "IT & Tech • 51 to 80%"}', 'SECTOR_CONCENTRATIONS', null, 90, true),
		   ('IT_GREATER_THAN_80_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "IT & Tech,greater than 80%", "selectedText": "IT & Tech • greater than 80%"}', 'SECTOR_CONCENTRATIONS', null, 100, true),

		   ('INFRA_LESS_THAN_10_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Infrastructure,less than 10%", "selectedText": "Infrastructure • less than 10%"}', 'SECTOR_CONCENTRATIONS', null, 110, true),
		   ('INFRA_11_TO_25_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Infrastructure,11 to 25%", "selectedText": "Infrastructure • 11 to 25%"}', 'SECTOR_CONCENTRATIONS', null, 120, true),
		   ('INFRA_26_TO_50_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Infrastructure,26 to 50%", "selectedText": "Infrastructure • 26 to 50%"}', 'SECTOR_CONCENTRATIONS', null, 130, true),
		   ('INFRA_51_TO_80_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Infrastructure,51 to 80%", "selectedText": "Infrastructure • 51 to 80%"}', 'SECTOR_CONCENTRATIONS', null, 140, true),
		   ('INFRA_GREATER_THAN_80_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Infrastructure,greater than 80%", "selectedText": "Infrastructure • greater than 80%"}', 'SECTOR_CONCENTRATIONS', null, 150, true),

		   ('BFS_LESS_THAN_10_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Banking & Financial services,less than 10%", "selectedText": "Banking & Financial services • less than 10%"}', 'SECTOR_CONCENTRATIONS', null, 160, true),
		   ('BFS_11_TO_25_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Banking & Financial services,11 to 25%", "selectedText": "Banking & Financial services • 11 to 25%"}', 'SECTOR_CONCENTRATIONS', null, 170, true),
		   ('BFS_26_TO_50_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Banking & Financial services,26 to 50%", "selectedText": "Banking & Financial services • 26 to 50%"}', 'SECTOR_CONCENTRATIONS', null, 180, true),
		   ('BFS_51_TO_80_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Banking & Financial services,51 to 80%", "selectedText": "Banking & Financial services • 51 to 80%"}', 'SECTOR_CONCENTRATIONS', null, 190, true),
		   ('BFS_GREATER_THAN_80_PER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Banking & Financial services,greater than 80%", "selectedText": "Banking & Financial services • greater than 80%"}', 'SECTOR_CONCENTRATIONS', null, 200, true),

		   -- Minimum AutoInvest amount (SIP)
		   ('UPTO_100_AUTO_INV_AMT', 'TERMED', jsonb'{}', jsonb'{"displayName": "upto 100", "selectedText": "upto 100"}', 'MIN_AUTO_INV_AMT', null, 10, true),
		   ('101_TO_500_AUTO_INV_AMT', 'TERMED', jsonb'{}', jsonb'{"displayName": "101 to 500", "selectedText": "101 to 500"}', 'MIN_AUTO_INV_AMT', null, 20, true),
		   ('501_TO_1000_AUTO_INV_AMT', 'TERMED', jsonb'{}', jsonb'{"displayName": "501 to 1000", "selectedText": "501 to 1000"}', 'MIN_AUTO_INV_AMT', null, 30, true),
		   ('1001_TO_5000_AUTO_INV_AMT', 'TERMED', jsonb'{}', jsonb'{"displayName": "1001 to 5000", "selectedText": "1001 to 5000"}', 'MIN_AUTO_INV_AMT', null, 40, true),
		   ('MORE_THAN_5000_AUTO_INV_AMT', 'TERMED', jsonb'{}', jsonb'{"displayName": "more than 5000", "selectedText": "more than 5000"}', 'MIN_AUTO_INV_AMT', null, 50, true),

		   -- Minimum one-time amount
		   ('UPTO_100_OT_INV_AMT', 'TERMED', jsonb'{}', jsonb'{"displayName": "upto 100", "selectedText": "upto 100 one-time amount"}', 'MIN_OT_INV_AMT', null, 10, true),
		   ('101_TO_500_OT_INV_AMT', 'TERMED', jsonb'{}', jsonb'{"displayName": "101 to 500", "selectedText": "101 to 500 one-time amount"}', 'MIN_OT_INV_AMT', null, 20, true),
		   ('501_TO_1000_OT_INV_AMT', 'TERMED', jsonb'{}', jsonb'{"displayName": "501 to 1000", "selectedText": "501 to 1000 one-time amount"}', 'MIN_OT_INV_AMT', null, 30, true),
		   ('1001_TO_5000_OT_INV_AMT', 'TERMED', jsonb'{}', jsonb'{"displayName": "1001 to 5000", "selectedText": "1001 to 5000 one-time amount"}', 'MIN_OT_INV_AMT', null, 40, true),
		   ('MORE_THAN_5000_OT_INV_AMT', 'TERMED', jsonb'{}', jsonb'{"displayName": "more than 5000", "selectedText": "more than 5000 one-time amount"}', 'MIN_OT_INV_AMT', null, 50, true),

		   -- Fund age
		   ('ZERO_TO_3YR_FUND_AGE', 'TERMED', jsonb'{}', jsonb'{"displayName": "0 to 3y", "selectedText": "0 to 3y fund age"}', 'FUND_AGE', null, 10, true),
		   ('3_TO_5YR_FUND_AGE', 'TERMED', jsonb'{}', jsonb'{"displayName": "3 to 5y", "selectedText": "3 to 5y fund age"}', 'FUND_AGE', null, 20, true),
		   ('5_TO_10YR_FUND_AGE', 'TERMED', jsonb'{}', jsonb'{"displayName": "5 to 10y", "selectedText": "5 to 10y fund age"}', 'FUND_AGE', null, 30, true),
		   ('MORE_THAN_10YR_FUND_AGE', 'TERMED', jsonb'{}', jsonb'{"displayName": "more than 10y", "selectedText": "more than 10y fund age"}', 'FUND_AGE', null, 40, true),

		   -- Fund managers
		   ('MANAGER_AAKASH_MANGHANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Aakash Manghani", "selectedText": "Aakash M"}', 'FUND_MANAGERS', null, 10, true),
		   ('MANAGER_ABHINAV_KHANDELWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Abhinav Khandelwal", "selectedText": "Abhinav K"}', 'FUND_MANAGERS', null, 20, true),
		   ('MANAGER_ABHINAV_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Abhinav Sharma", "selectedText": "Abhinav S"}', 'FUND_MANAGERS', null, 30, true),
		   ('MANAGER_ABHIROOP_MUKHERJEE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Abhiroop Mukherjee", "selectedText": "Abhiroop M"}', 'FUND_MANAGERS', null, 40, true),
		   ('MANAGER_ABHISHEK_BISEN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Abhishek Bisen", "selectedText": "Abhishek B"}', 'FUND_MANAGERS', null, 50, true),
		   ('MANAGER_ABHISHEK_GHOSH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Abhishek Ghosh", "selectedText": "Abhishek G"}', 'FUND_MANAGERS', null, 60, true),
		   ('MANAGER_ABHISHEK_GUPTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Abhishek Gupta", "selectedText": "Abhishek G"}', 'FUND_MANAGERS', null, 70, true),
		   ('MANAGER_ABHISHEK_IYER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Abhishek Iyer", "selectedText": "Abhishek I"}', 'FUND_MANAGERS', null, 80, true),
		   ('MANAGER_ABHISHEK_SINGH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Abhishek Singh", "selectedText": "Abhishek S"}', 'FUND_MANAGERS', null, 90, true),
		   ('MANAGER_ABHISHEK_SONTHALIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Abhishek Sonthalia", "selectedText": "Abhishek S"}', 'FUND_MANAGERS', null, 100, true),
		   ('MANAGER_ABUL_FATEH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Abul Fateh", "selectedText": "Abul F"}', 'FUND_MANAGERS', null, 110, true),
		   ('MANAGER_ADITYA_KHEMANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Aditya Khemani", "selectedText": "Aditya K"}', 'FUND_MANAGERS', null, 120, true),
		   ('MANAGER_ADITYA_MULKI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Aditya Mulki", "selectedText": "Aditya M"}', 'FUND_MANAGERS', null, 130, true),
		   ('MANAGER_ADITYA_PAGARIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Aditya Pagaria", "selectedText": "Aditya P"}', 'FUND_MANAGERS', null, 140, true),
		   ('MANAGER_AISHWARYA_AGARWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Aishwarya Agarwal", "selectedText": "Aishwarya A"}', 'FUND_MANAGERS', null, 150, true),
		   ('MANAGER_AJAY_ARGAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ajay Argal", "selectedText": "Ajay A"}', 'FUND_MANAGERS', null, 160, true),
		   ('MANAGER_AJAY_GARG', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ajay Garg", "selectedText": "Ajay G"}', 'FUND_MANAGERS', null, 170, true),
		   ('MANAGER_AJAY_KHANDELWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ajay Khandelwal", "selectedText": "Ajay K"}', 'FUND_MANAGERS', null, 180, true),
		   ('MANAGER_AJAY_TYAGI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ajay Tyagi", "selectedText": "Ajay T"}', 'FUND_MANAGERS', null, 190, true),
		   ('MANAGER_AKHIL_KAKKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Akhil Kakkar", "selectedText": "Akhil K"}', 'FUND_MANAGERS', null, 200, true),
		   ('MANAGER_AKHIL_KALLURI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Akhil Kalluri", "selectedText": "Akhil K"}', 'FUND_MANAGERS', null, 210, true),
		   ('MANAGER_AKHIL_MITTAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Akhil Mittal", "selectedText": "Akhil M"}', 'FUND_MANAGERS', null, 220, true),
		   ('MANAGER_AKHIL_THAKKER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Akhil Thakker", "selectedText": "Akhil T"}', 'FUND_MANAGERS', null, 230, true),
		   ('MANAGER_ALOK_RANJAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Alok Ranjan", "selectedText": "Alok R"}', 'FUND_MANAGERS', null, 240, true),
		   ('MANAGER_ALOK_SAHOO', 'TERMED', jsonb'{}', jsonb'{"displayName": "Alok Sahoo", "selectedText": "Alok S"}', 'FUND_MANAGERS', null, 250, true),
		   ('MANAGER_ALOK_SINGH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Alok Singh", "selectedText": "Alok S"}', 'FUND_MANAGERS', null, 260, true),
		   ('MANAGER_AMANDEEP_CHOPRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amandeep Chopra", "selectedText": "Amandeep C"}', 'FUND_MANAGERS', null, 270, true),
		   ('MANAGER_AMAR_KALKUNDRIKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amar Kalkundrikar", "selectedText": "Amar K"}', 'FUND_MANAGERS', null, 280, true),
		   ('MANAGER_AMEY_SATHE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amey Sathe", "selectedText": "Amey S"}', 'FUND_MANAGERS', null, 290, true),
		   ('MANAGER_AMIT_GANATRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Ganatra", "selectedText": "Amit G"}', 'FUND_MANAGERS', null, 300, true),
		   ('MANAGER_AMIT_GARG', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Garg", "selectedText": "Amit G"}', 'FUND_MANAGERS', null, 310, true),
		   ('MANAGER_AMIT_HIREMATH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Hiremath", "selectedText": "Amit H"}', 'FUND_MANAGERS', null, 320, true),
		   ('MANAGER_AMIT_KADAM', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Kadam", "selectedText": "Amit K"}', 'FUND_MANAGERS', null, 330, true),
		   ('MANAGER_AMIT_MODANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Modani", "selectedText": "Amit M"}', 'FUND_MANAGERS', null, 340, true),
		   ('MANAGER_AMIT_NADEKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Nadekar", "selectedText": "Amit N"}', 'FUND_MANAGERS', null, 350, true),
		   ('MANAGER_AMIT_NIGAM', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Nigam", "selectedText": "Amit N"}', 'FUND_MANAGERS', null, 360, true),
		   ('MANAGER_AMIT_PREMCHANDANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Premchandani", "selectedText": "Amit P"}', 'FUND_MANAGERS', null, 370, true),
		   ('MANAGER_AMIT_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Sharma", "selectedText": "Amit S"}', 'FUND_MANAGERS', null, 380, true),
		   ('MANAGER_AMIT_SOMANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Somani", "selectedText": "Amit S"}', 'FUND_MANAGERS', null, 390, true),
		   ('MANAGER_AMIT_VORA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Amit Vora", "selectedText": "Amit V"}', 'FUND_MANAGERS', null, 400, true),
		   ('MANAGER_ANAND_GUPTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anand Gupta", "selectedText": "Anand G"}', 'FUND_MANAGERS', null, 410, true),
		   ('MANAGER_ANAND_LADDHA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anand Laddha", "selectedText": "Anand L"}', 'FUND_MANAGERS', null, 420, true),
		   ('MANAGER_ANAND_NEVATIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anand Nevatia", "selectedText": "Anand N"}', 'FUND_MANAGERS', null, 430, true),
		   ('MANAGER_ANAND_RADHAKRISHNAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anand Radhakrishnan", "selectedText": "Anand R"}', 'FUND_MANAGERS', null, 440, true),
		   ('MANAGER_ANAND_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anand Sharma", "selectedText": "Anand S"}', 'FUND_MANAGERS', null, 450, true),
		   ('MANAGER_ANANDHA_PADMANABHAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anandha Padmanabhan", "selectedText": "Anandha P"}', 'FUND_MANAGERS', null, 460, true),
		   ('MANAGER_ANIL_BAMBOLI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anil Bamboli", "selectedText": "Anil B"}', 'FUND_MANAGERS', null, 470, true),
		   ('MANAGER_ANIL_GHELANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anil Ghelani", "selectedText": "Anil G"}', 'FUND_MANAGERS', null, 480, true),
		   ('MANAGER_ANIL_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anil Shah", "selectedText": "Anil S"}', 'FUND_MANAGERS', null, 490, true),
		   ('MANAGER_ANINDYA_SARKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anindya Sarkar", "selectedText": "Anindya S"}', 'FUND_MANAGERS', null, 500, true),
		   ('MANAGER_ANIRUDDHA_NAHA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Aniruddha Naha", "selectedText": "Aniruddha N"}', 'FUND_MANAGERS', null, 510, true),
		   ('MANAGER_ANISH_TAWAKLEY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anish Tawakley", "selectedText": "Anish T"}', 'FUND_MANAGERS', null, 520, true),
		   ('MANAGER_ANJU_CHHAJER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anju Chhajer", "selectedText": "Anju C"}', 'FUND_MANAGERS', null, 530, true),
		   ('MANAGER_ANKIT_AGARWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ankit Agarwal", "selectedText": "Ankit A"}', 'FUND_MANAGERS', null, 540, true),
		   ('MANAGER_ANKIT_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ankit Jain", "selectedText": "Ankit J"}', 'FUND_MANAGERS', null, 550, true),
		   ('MANAGER_ANKIT_PANDE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ankit Pande", "selectedText": "Ankit P"}', 'FUND_MANAGERS', null, 560, true),
		   ('MANAGER_ANKIT_TIKMANY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ankit Tikmany", "selectedText": "Ankit T"}', 'FUND_MANAGERS', null, 570, true),
		   ('MANAGER_ANKUR_ARORA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ankur Arora", "selectedText": "Ankur A"}', 'FUND_MANAGERS', null, 580, true),
		   ('MANAGER_ANKUSH_SOOD', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ankush Sood", "selectedText": "Ankush S"}', 'FUND_MANAGERS', null, 590, true),
		   ('MANAGER_ANOOP_BHASKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anoop Bhaskar", "selectedText": "Anoop B"}', 'FUND_MANAGERS', null, 600, true),
		   ('MANAGER_ANUJ_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anuj Jain", "selectedText": "Anuj J"}', 'FUND_MANAGERS', null, 610, true),
		   ('MANAGER_ANUJ_TAGRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anuj Tagra", "selectedText": "Anuj T"}', 'FUND_MANAGERS', null, 620, true),
		   ('MANAGER_ANUPAM_JOSHI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anupam Joshi", "selectedText": "Anupam J"}', 'FUND_MANAGERS', null, 630, true),
		   ('MANAGER_ANUPAM_TIWARI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anupam Tiwari", "selectedText": "Anupam T"}', 'FUND_MANAGERS', null, 640, true),
		   ('MANAGER_ANURAG_MITTAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Anurag Mittal", "selectedText": "Anurag M"}', 'FUND_MANAGERS', null, 650, true),
		   ('MANAGER_APARNA_KARNIK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Aparna Karnik", "selectedText": "Aparna K"}', 'FUND_MANAGERS', null, 660, true),
		   ('MANAGER_ARDHENDU_BHATTACHARYA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ardhendu Bhattacharya", "selectedText": "Ardhendu B"}', 'FUND_MANAGERS', null, 670, true),
		   ('MANAGER_ARJUN_KHANNA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Arjun Khanna", "selectedText": "Arjun K"}', 'FUND_MANAGERS', null, 680, true),
		   ('MANAGER_ARUN_AGARWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Arun Agarwal", "selectedText": "Arun A"}', 'FUND_MANAGERS', null, 690, true),
		   ('MANAGER_ARUN_R.', 'TERMED', jsonb'{}', jsonb'{"displayName": "Arun R.", "selectedText": "Arun R"}', 'FUND_MANAGERS', null, 700, true),
		   ('MANAGER_ARUN_SUNDARESAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Arun Sundaresan", "selectedText": "Arun S"}', 'FUND_MANAGERS', null, 710, true),
		   ('MANAGER_ARVIND_SUBRAMANIAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Arvind Subramanian", "selectedText": "Arvind S"}', 'FUND_MANAGERS', null, 720, true),
		   ('MANAGER_ASHISH_AGARWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ashish Agarwal", "selectedText": "Ashish A"}', 'FUND_MANAGERS', null, 730, true),
		   ('MANAGER_ASHISH_AGGARWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ashish Aggarwal", "selectedText": "Ashish A"}', 'FUND_MANAGERS', null, 740, true),
		   ('MANAGER_ASHISH_NAIK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ashish Naik", "selectedText": "Ashish N"}', 'FUND_MANAGERS', null, 750, true),
		   ('MANAGER_ASHUTOSH_BHARGAVA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ashutosh Bhargava", "selectedText": "Ashutosh B"}', 'FUND_MANAGERS', null, 760, true),
		   ('MANAGER_ASHUTOSH_DESAI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ashutosh Desai", "selectedText": "Ashutosh D"}', 'FUND_MANAGERS', null, 770, true),
		   ('MANAGER_ASHWANI_AGARWALLA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ashwani Agarwalla", "selectedText": "Ashwani A"}', 'FUND_MANAGERS', null, 780, true),
		   ('MANAGER_ASIT_BHANDARKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Asit Bhandarkar", "selectedText": "Asit B"}', 'FUND_MANAGERS', null, 790, true),
		   ('MANAGER_ATUL_BHOLE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Atul Bhole", "selectedText": "Atul B"}', 'FUND_MANAGERS', null, 800, true),
		   ('MANAGER_ATUL_PENKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Atul Penkar", "selectedText": "Atul P"}', 'FUND_MANAGERS', null, 810, true),
		   ('MANAGER_AUROBINDO_GAYAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Aurobindo Gayan", "selectedText": "Aurobindo G"}', 'FUND_MANAGERS', null, 820, true),
		   ('MANAGER_AVNISH_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Avnish Jain", "selectedText": "Avnish J"}', 'FUND_MANAGERS', null, 830, true),
		   ('MANAGER_AYUSH_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ayush Jain", "selectedText": "Ayush J"}', 'FUND_MANAGERS', null, 840, true),
		   ('MANAGER_B_KUMAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "B Kumar", "selectedText": "B Kumar"}', 'FUND_MANAGERS', null, 850, true),
		   ('MANAGER_BHAGYESH_KAGALKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Bhagyesh Kagalkar", "selectedText": "Bhagyesh K"}', 'FUND_MANAGERS', null, 860, true),
		   ('MANAGER_BHARAT_LAHOTI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Bharat Lahoti", "selectedText": "Bharat L"}', 'FUND_MANAGERS', null, 870, true),
		   ('MANAGER_BHARTI_SAWANT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Bharti Sawant", "selectedText": "Bharti S"}', 'FUND_MANAGERS', null, 880, true),
		   ('MANAGER_BHAVESH_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Bhavesh Jain", "selectedText": "Bhavesh J"}', 'FUND_MANAGERS', null, 890, true),
		   ('MANAGER_BHAVIK_DAVE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Bhavik Dave", "selectedText": "Bhavik D"}', 'FUND_MANAGERS', null, 900, true),
		   ('MANAGER_BHAVIN_VITHLANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Bhavin Vithlani", "selectedText": "Bhavin V"}', 'FUND_MANAGERS', null, 910, true),
		   ('MANAGER_BHUPESH_BAMETA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Bhupesh Bameta", "selectedText": "Bhupesh B"}', 'FUND_MANAGERS', null, 920, true),
		   ('MANAGER_BHUPESH_KALYANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Bhupesh Kalyani", "selectedText": "Bhupesh K"}', 'FUND_MANAGERS', null, 930, true),
		   ('MANAGER_BRIJESH_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Brijesh Shah", "selectedText": "Brijesh S"}', 'FUND_MANAGERS', null, 940, true),
		   ('MANAGER_CHAITANYA_CHOKSI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Chaitanya Choksi", "selectedText": "Chaitanya C"}', 'FUND_MANAGERS', null, 950, true),
		   ('MANAGER_CHANCHAL_KHANDELWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Chanchal Khandelwal", "selectedText": "Chanchal K"}', 'FUND_MANAGERS', null, 960, true),
		   ('MANAGER_CHANDNI_GUPTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Chandni Gupta", "selectedText": "Chandni G"}', 'FUND_MANAGERS', null, 970, true),
		   ('MANAGER_CHANDRAPRAKASH_PADIYAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Chandraprakash Padiyar", "selectedText": "Chandraprakash P"}', 'FUND_MANAGERS', null, 980, true),
		   ('MANAGER_CHARANJIT_SINGH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Charanjit Singh", "selectedText": "Charanjit S"}', 'FUND_MANAGERS', null, 990, true),
		   ('MANAGER_CHEENU_GUPTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Cheenu Gupta", "selectedText": "Cheenu G"}', 'FUND_MANAGERS', null, 1000, true),
		   ('MANAGER_CHIRAG_DAGLI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Chirag Dagli", "selectedText": "Chirag D"}', 'FUND_MANAGERS', null, 1010, true),
		   ('MANAGER_CHIRAG_MEHTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Chirag Mehta", "selectedText": "Chirag M"}', 'FUND_MANAGERS', null, 1020, true),
		   ('MANAGER_CHIRAG_SETALVAD', 'TERMED', jsonb'{}', jsonb'{"displayName": "Chirag Setalvad", "selectedText": "Chirag S"}', 'FUND_MANAGERS', null, 1030, true),
		   ('MANAGER_CHOCKALINGAM_NARAYANAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Chockalingam Narayanan", "selectedText": "Chockalingam N"}', 'FUND_MANAGERS', null, 1040, true),
		   ('MANAGER_DAYLYNN_PINTO', 'TERMED', jsonb'{}', jsonb'{"displayName": "Daylynn Pinto", "selectedText": "Daylynn P"}', 'FUND_MANAGERS', null, 1050, true),
		   ('MANAGER_DEEPAK_AGRAWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Deepak Agrawal", "selectedText": "Deepak A"}', 'FUND_MANAGERS', null, 1060, true),
		   ('MANAGER_DEEPAK_GUPTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Deepak Gupta", "selectedText": "Deepak G"}', 'FUND_MANAGERS', null, 1070, true),
		   ('MANAGER_DEVANG_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Devang Shah", "selectedText": "Devang S"}', 'FUND_MANAGERS', null, 1080, true),
		   ('MANAGER_DEVENDER_SINGHAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Devender Singhal", "selectedText": "Devender S"}', 'FUND_MANAGERS', null, 1090, true),
		   ('MANAGER_DEVESH_THACKER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Devesh Thacker", "selectedText": "Devesh T"}', 'FUND_MANAGERS', null, 1100, true),
		   ('MANAGER_DHARMESH_KAKKAD', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dharmesh Kakkad", "selectedText": "Dharmesh K"}', 'FUND_MANAGERS', null, 1110, true),
		   ('MANAGER_DHAVAL_GALA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dhaval Gala", "selectedText": "Dhaval G"}', 'FUND_MANAGERS', null, 1120, true),
		   ('MANAGER_DHAVAL_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dhaval Shah", "selectedText": "Dhaval S"}', 'FUND_MANAGERS', null, 1130, true),
		   ('MANAGER_DHAWAL_DALAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dhawal Dalal", "selectedText": "Dhawal D"}', 'FUND_MANAGERS', null, 1140, true),
		   ('MANAGER_DHAWAL_DHANANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dhawal Dhanani", "selectedText": "Dhawal D"}', 'FUND_MANAGERS', null, 1150, true),
		   ('MANAGER_DHIMANT_KOTHARI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dhimant Kothari", "selectedText": "Dhimant K"}', 'FUND_MANAGERS', null, 1160, true),
		   ('MANAGER_DHRUMIL_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dhrumil Shah", "selectedText": "Dhrumil S"}', 'FUND_MANAGERS', null, 1170, true),
		   ('MANAGER_DHRUV_BHATIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dhruv Bhatia", "selectedText": "Dhruv B"}', 'FUND_MANAGERS', null, 1180, true),
		   ('MANAGER_DIIPESH_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Diipesh Shah", "selectedText": "Diipesh S"}', 'FUND_MANAGERS', null, 1190, true),
		   ('MANAGER_DINESH_AHUJA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dinesh Ahuja", "selectedText": "Dinesh A"}', 'FUND_MANAGERS', null, 1200, true),
		   ('MANAGER_DINESH_BALACHANDRAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dinesh Balachandran", "selectedText": "Dinesh B"}', 'FUND_MANAGERS', null, 1210, true),
		   ('MANAGER_DWIJENDRA_SRIVASTAVA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Dwijendra Srivastava", "selectedText": "Dwijendra S"}', 'FUND_MANAGERS', null, 1220, true),
		   ('MANAGER_EKTA_GALA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ekta Gala", "selectedText": "Ekta G"}', 'FUND_MANAGERS', null, 1230, true),
		   ('MANAGER_ENNETTE_FERNANDES', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ennette Fernandes", "selectedText": "Ennette F"}', 'FUND_MANAGERS', null, 1240, true),
		   ('MANAGER_FATEMA_PACHA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Fatema Pacha", "selectedText": "Fatema P"}', 'FUND_MANAGERS', null, 1250, true),
		   ('MANAGER_GARGI_BANERJEE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gargi Banerjee", "selectedText": "Gargi B"}', 'FUND_MANAGERS', null, 1260, true),
		   ('MANAGER_GAURAV_KHANDELWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gaurav Khandelwal", "selectedText": "Gaurav K"}', 'FUND_MANAGERS', null, 1270, true),
		   ('MANAGER_GAURAV_KOCHAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gaurav Kochar", "selectedText": "Gaurav K"}', 'FUND_MANAGERS', null, 1280, true),
		   ('MANAGER_GAURAV_MISRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gaurav Misra", "selectedText": "Gaurav M"}', 'FUND_MANAGERS', null, 1290, true),
		   ('MANAGER_GAUTAM_BHUPAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gautam Bhupal", "selectedText": "Gautam B"}', 'FUND_MANAGERS', null, 1300, true),
		   ('MANAGER_GAUTAM_KAUL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gautam Kaul", "selectedText": "Gautam K"}', 'FUND_MANAGERS', null, 1310, true),
		   ('MANAGER_GHAZAL_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ghazal Jain", "selectedText": "Ghazal J"}', 'FUND_MANAGERS', null, 1320, true),
		   ('MANAGER_GOPAL_AGRAWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gopal Agrawal", "selectedText": "Gopal A"}', 'FUND_MANAGERS', null, 1330, true),
		   ('MANAGER_GURVINDER_WASAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Gurvinder Wasan", "selectedText": "Gurvinder W"}', 'FUND_MANAGERS', null, 1340, true),
		   ('MANAGER_HARDICK_BORA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Hardick Bora", "selectedText": "Hardick B"}', 'FUND_MANAGERS', null, 1350, true),
		   ('MANAGER_HARDIK_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Hardik Shah", "selectedText": "Hardik S"}', 'FUND_MANAGERS', null, 1360, true),
		   ('MANAGER_HARISH_BIHANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Harish Bihani", "selectedText": "Harish B"}', 'FUND_MANAGERS', null, 1370, true),
		   ('MANAGER_HARISH_KRISHNAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Harish Krishnan", "selectedText": "Harish K"}', 'FUND_MANAGERS', null, 1380, true),
		   ('MANAGER_HARSHA_UPADHYAYA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Harsha Upadhyaya", "selectedText": "Harsha U"}', 'FUND_MANAGERS', null, 1390, true),
		   ('MANAGER_HARSHAD_BORAWAKE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Harshad Borawake", "selectedText": "Harshad B"}', 'FUND_MANAGERS', null, 1400, true),
		   ('MANAGER_HARSHAL_JOSHI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Harshal Joshi", "selectedText": "Harshal J"}', 'FUND_MANAGERS', null, 1410, true),
		   ('MANAGER_HARSHIL_JOSHI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Harshil Joshi", "selectedText": "Harshil J"}', 'FUND_MANAGERS', null, 1420, true),
		   ('MANAGER_HARSHIL_SUVARNKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Harshil Suvarnkar", "selectedText": "Harshil S"}', 'FUND_MANAGERS', null, 1430, true),
		   ('MANAGER_HETAL_GADA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Hetal Gada", "selectedText": "Hetal G"}', 'FUND_MANAGERS', null, 1440, true),
		   ('MANAGER_HITASH_DANG', 'TERMED', jsonb'{}', jsonb'{"displayName": "Hitash Dang", "selectedText": "Hitash D"}', 'FUND_MANAGERS', null, 1450, true),
		   ('MANAGER_HITEN_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Hiten Jain", "selectedText": "Hiten J"}', 'FUND_MANAGERS', null, 1460, true),
		   ('MANAGER_HITEN_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Hiten Shah", "selectedText": "Hiten S"}', 'FUND_MANAGERS', null, 1470, true),
		   ('MANAGER_HITESH_DAS', 'TERMED', jsonb'{}', jsonb'{"displayName": "Hitesh Das", "selectedText": "Hitesh D"}', 'FUND_MANAGERS', null, 1480, true),
		   ('MANAGER_IHAB_DALWAI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ihab Dalwai", "selectedText": "Ihab D"}', 'FUND_MANAGERS', null, 1490, true),
		   ('MANAGER_JAIPRAKASH_TOSHNIWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Jaiprakash Toshniwal", "selectedText": "Jaiprakash T"}', 'FUND_MANAGERS', null, 1500, true),
		   ('MANAGER_JALPAN_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Jalpan Shah", "selectedText": "Jalpan S"}', 'FUND_MANAGERS', null, 1510, true),
		   ('MANAGER_JAY_KOTHARI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Jay Kothari", "selectedText": "Jay K"}', 'FUND_MANAGERS', null, 1520, true),
		   ('MANAGER_JIGAR_SHETHIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Jigar Shethia", "selectedText": "Jigar S"}', 'FUND_MANAGERS', null, 1530, true),
		   ('MANAGER_JIGNESH_RAO', 'TERMED', jsonb'{}', jsonb'{"displayName": "Jignesh Rao", "selectedText": "Jignesh R"}', 'FUND_MANAGERS', null, 1540, true),
		   ('MANAGER_JINESH_GOPANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Jinesh Gopani", "selectedText": "Jinesh G"}', 'FUND_MANAGERS', null, 1550, true),
		   ('MANAGER_JITENDRA_SRIRAM', 'TERMED', jsonb'{}', jsonb'{"displayName": "Jitendra Sriram", "selectedText": "Jitendra S"}', 'FUND_MANAGERS', null, 1560, true),
		   ('MANAGER_KAMAL_GADA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kamal Gada", "selectedText": "Kamal G"}', 'FUND_MANAGERS', null, 1570, true),
		   ('MANAGER_KAPIL_PUNJABI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kapil Punjabi", "selectedText": "Kapil P"}', 'FUND_MANAGERS', null, 1580, true),
		   ('MANAGER_KARAN_DOSHI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Karan Doshi", "selectedText": "Karan D"}', 'FUND_MANAGERS', null, 1590, true),
		   ('MANAGER_KARAN_MUNDHRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Karan Mundhra", "selectedText": "Karan M"}', 'FUND_MANAGERS', null, 1600, true),
		   ('MANAGER_KARAN_MUNDRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Karan Mundra", "selectedText": "Karan M"}', 'FUND_MANAGERS', null, 1610, true),
		   ('MANAGER_KARAN_SINGH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Karan Singh", "selectedText": "Karan S"}', 'FUND_MANAGERS', null, 1620, true),
		   ('MANAGER_KARN_KUMAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Karn Kumar", "selectedText": "Karn K"}', 'FUND_MANAGERS', null, 1630, true),
		   ('MANAGER_KARTIK_SORAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kartik Soral", "selectedText": "Kartik S"}', 'FUND_MANAGERS', null, 1640, true),
		   ('MANAGER_KAUSTUBH_GUPTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kaustubh Gupta", "selectedText": "Kaustubh G"}', 'FUND_MANAGERS', null, 1650, true),
		   ('MANAGER_KAUSTUBH_SULE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kaustubh Sule", "selectedText": "Kaustubh S"}', 'FUND_MANAGERS', null, 1660, true),
		   ('MANAGER_KAYZAD_EGHLIM', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kayzad Eghlim", "selectedText": "Kayzad E"}', 'FUND_MANAGERS', null, 1670, true),
		   ('MANAGER_KEDAR_KARNIK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kedar Karnik", "selectedText": "Kedar K"}', 'FUND_MANAGERS', null, 1680, true),
		   ('MANAGER_KHOZEM_JABALPURWALA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Khozem Jabalpurwala", "selectedText": "Khozem J"}', 'FUND_MANAGERS', null, 1690, true),
		   ('MANAGER_KINJAL_DESAI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kinjal Desai", "selectedText": "Kinjal D"}', 'FUND_MANAGERS', null, 1700, true),
		   ('MANAGER_KIRAN_SEBASTIAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kiran Sebastian", "selectedText": "Kiran S"}', 'FUND_MANAGERS', null, 1710, true),
		   ('MANAGER_KRISHAN_DAGA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Krishan Daga", "selectedText": "Krishan D"}', 'FUND_MANAGERS', null, 1720, true),
		   ('MANAGER_KRISHNA_CHEEMALAPATI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Krishna Cheemalapati", "selectedText": "Krishna C"}', 'FUND_MANAGERS', null, 1730, true),
		   ('MANAGER_KRISHNA_SANGHAVI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Krishna Sanghavi", "selectedText": "Krishna S"}', 'FUND_MANAGERS', null, 1740, true),
		   ('MANAGER_KUNAL_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kunal Jain", "selectedText": "Kunal J"}', 'FUND_MANAGERS', null, 1750, true),
		   ('MANAGER_KUNAL_SANGOI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kunal Sangoi", "selectedText": "Kunal S"}', 'FUND_MANAGERS', null, 1760, true),
		   ('MANAGER_KUSH_SONIGARA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Kush Sonigara", "selectedText": "Kush S"}', 'FUND_MANAGERS', null, 1770, true),
		   ('MANAGER_LAKSHMIKANTH_REDDY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Lakshmikanth Reddy", "selectedText": "Lakshmikanth R"}', 'FUND_MANAGERS', null, 1780, true),
		   ('MANAGER_LALIT_KUMAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Lalit Kumar", "selectedText": "Lalit K"}', 'FUND_MANAGERS', null, 1790, true),
		   ('MANAGER_LAUKIK_BAGWE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Laukik Bagwe", "selectedText": "Laukik B"}', 'FUND_MANAGERS', null, 1800, true),
		   ('MANAGER_LOKESH_MALLYA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Lokesh Mallya", "selectedText": "Lokesh M"}', 'FUND_MANAGERS', null, 1810, true),
		   ('MANAGER_LOVELISH_SOLANKI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Lovelish Solanki", "selectedText": "Lovelish S"}', 'FUND_MANAGERS', null, 1820, true),
		   ('MANAGER_MAHENDRA_JAJOO', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mahendra Jajoo", "selectedText": "Mahendra J"}', 'FUND_MANAGERS', null, 1830, true),
		   ('MANAGER_MAHESH_CHHABRIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mahesh Chhabria", "selectedText": "Mahesh C"}', 'FUND_MANAGERS', null, 1840, true),
		   ('MANAGER_MAHESH_PATIL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mahesh Patil", "selectedText": "Mahesh P"}', 'FUND_MANAGERS', null, 1850, true),
		   ('MANAGER_MANISH_BANTHIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Manish Banthia", "selectedText": "Manish B"}', 'FUND_MANAGERS', null, 1860, true),
		   ('MANAGER_MANISH_GUNWANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Manish Gunwani", "selectedText": "Manish G"}', 'FUND_MANAGERS', null, 1870, true),
		   ('MANAGER_MANISH_LODHA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Manish Lodha", "selectedText": "Manish L"}', 'FUND_MANAGERS', null, 1880, true),
		   ('MANAGER_MANSI_SAJEJA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mansi Sajeja", "selectedText": "Mansi S"}', 'FUND_MANAGERS', null, 1890, true),
		   ('MANAGER_MARZBAN_IRANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Marzban Irani", "selectedText": "Marzban I"}', 'FUND_MANAGERS', null, 1900, true),
		   ('MANAGER_MAYANK_BUKREDIWALA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mayank Bukrediwala", "selectedText": "Mayank B"}', 'FUND_MANAGERS', null, 1910, true),
		   ('MANAGER_MAYANK_PRAKASH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mayank Prakash", "selectedText": "Mayank P"}', 'FUND_MANAGERS', null, 1920, true),
		   ('MANAGER_MAYUR_PATEL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mayur Patel", "selectedText": "Mayur P"}', 'FUND_MANAGERS', null, 1930, true),
		   ('MANAGER_MEENAKSHI_DAWAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Meenakshi Dawar", "selectedText": "Meenakshi D"}', 'FUND_MANAGERS', null, 1940, true),
		   ('MANAGER_MEETA_SHETTY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Meeta Shetty", "selectedText": "Meeta S"}', 'FUND_MANAGERS', null, 1950, true),
		   ('MANAGER_MEHUL_DAMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mehul Dama", "selectedText": "Mehul D"}', 'FUND_MANAGERS', null, 1960, true),
		   ('MANAGER_MEHUL_SONI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mehul Soni", "selectedText": "Mehul S"}', 'FUND_MANAGERS', null, 1970, true),
		   ('MANAGER_MILAN_MODY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Milan Mody", "selectedText": "Milan M"}', 'FUND_MANAGERS', null, 1980, true),
		   ('MANAGER_MILIND_AGRAWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Milind Agrawal", "selectedText": "Milind A"}', 'FUND_MANAGERS', null, 1990, true),
		   ('MANAGER_MILIND_BAFNA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Milind Bafna", "selectedText": "Milind B"}', 'FUND_MANAGERS', null, 2000, true),
		   ('MANAGER_MITHRAEM_BHARUCHA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mithraem Bharucha", "selectedText": "Mithraem B"}', 'FUND_MANAGERS', null, 2010, true),
		   ('MANAGER_MITTUL_KALAWADIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mittul Kalawadia", "selectedText": "Mittul K"}', 'FUND_MANAGERS', null, 2020, true),
		   ('MANAGER_MOHIT_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mohit Jain", "selectedText": "Mohit J"}', 'FUND_MANAGERS', null, 2030, true),
		   ('MANAGER_MOHIT_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Mohit Sharma", "selectedText": "Mohit S"}', 'FUND_MANAGERS', null, 2040, true),
		   ('MANAGER_MONIKA_GANDHI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Monika Gandhi", "selectedText": "Monika G"}', 'FUND_MANAGERS', null, 2050, true),
		   ('MANAGER_MURTHY_NAGARAJAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Murthy Nagarajan", "selectedText": "Murthy N"}', 'FUND_MANAGERS', null, 2060, true),
		   ('MANAGER_NAGHMA_KHOJA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Naghma Khoja", "selectedText": "Naghma K"}', 'FUND_MANAGERS', null, 2070, true),
		   ('MANAGER_NEELESH_DHAMNASKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Neelesh Dhamnaskar", "selectedText": "Neelesh D"}', 'FUND_MANAGERS', null, 2080, true),
		   ('MANAGER_NEELESH_SURANA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Neelesh Surana", "selectedText": "Neelesh S"}', 'FUND_MANAGERS', null, 2090, true),
		   ('MANAGER_NEELOTPAL_SAHAI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Neelotpal Sahai", "selectedText": "Neelotpal S"}', 'FUND_MANAGERS', null, 2100, true),
		   ('MANAGER_NEERAJ_KUMAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Neeraj Kumar", "selectedText": "Neeraj K"}', 'FUND_MANAGERS', null, 2110, true),
		   ('MANAGER_NEERAJ_SAXENA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Neeraj Saxena", "selectedText": "Neeraj S"}', 'FUND_MANAGERS', null, 2120, true),
		   ('MANAGER_NEMISH_SHETH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nemish Sheth", "selectedText": "Nemish S"}', 'FUND_MANAGERS', null, 2130, true),
		   ('MANAGER_NIDHI_CHAWLA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nidhi Chawla", "selectedText": "Nidhi C"}', 'FUND_MANAGERS', null, 2140, true),
		   ('MANAGER_NIKET_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Niket Shah", "selectedText": "Niket S"}', 'FUND_MANAGERS', null, 2150, true),
		   ('MANAGER_NIKHIL_KABRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nikhil Kabra", "selectedText": "Nikhil K"}', 'FUND_MANAGERS', null, 2160, true),
		   ('MANAGER_NIKHIL_RUNGTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nikhil Rungta", "selectedText": "Nikhil R"}', 'FUND_MANAGERS', null, 2170, true),
		   ('MANAGER_NILESH_SHETTY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nilesh Shetty", "selectedText": "Nilesh S"}', 'FUND_MANAGERS', null, 2180, true),
		   ('MANAGER_NIRALI_BHANSALI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nirali Bhansali", "selectedText": "Nirali B"}', 'FUND_MANAGERS', null, 2190, true),
		   ('MANAGER_NISHIT_PATEL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nishit Patel", "selectedText": "Nishit P"}', 'FUND_MANAGERS', null, 2200, true),
		   ('MANAGER_NISHITA_DOSHI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nishita Doshi", "selectedText": "Nishita D"}', 'FUND_MANAGERS', null, 2210, true),
		   ('MANAGER_NITESH_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nitesh Jain", "selectedText": "Nitesh J"}', 'FUND_MANAGERS', null, 2220, true),
		   ('MANAGER_NITIN_GOSAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Nitin Gosar", "selectedText": "Nitin G"}', 'FUND_MANAGERS', null, 2230, true),
		   ('MANAGER_PALLAB_ROY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pallab Roy", "selectedText": "Pallab R"}', 'FUND_MANAGERS', null, 2240, true),
		   ('MANAGER_PANKAJ_PATHAK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pankaj Pathak", "selectedText": "Pankaj P"}', 'FUND_MANAGERS', null, 2250, true),
		   ('MANAGER_PANKAJ_TIBREWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pankaj Tibrewal", "selectedText": "Pankaj T"}', 'FUND_MANAGERS', null, 2260, true),
		   ('MANAGER_PARIJAT_AGRAWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Parijat Agrawal", "selectedText": "Parijat A"}', 'FUND_MANAGERS', null, 2270, true),
		   ('MANAGER_PARIJAT_GARG', 'TERMED', jsonb'{}', jsonb'{"displayName": "Parijat Garg", "selectedText": "Parijat G"}', 'FUND_MANAGERS', null, 2280, true),
		   ('MANAGER_PAUL_PARAMPREET', 'TERMED', jsonb'{}', jsonb'{"displayName": "Paul Parampreet", "selectedText": "Paul P"}', 'FUND_MANAGERS', null, 2290, true),
		   ('MANAGER_PIYUSH_BARANWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Piyush Baranwal", "selectedText": "Piyush B"}', 'FUND_MANAGERS', null, 2300, true),
		   ('MANAGER_PRADEEP_GOKHALE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pradeep Gokhale", "selectedText": "Pradeep G"}', 'FUND_MANAGERS', null, 2310, true),
		   ('MANAGER_PRAKASH_GOEL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Prakash Goel", "selectedText": "Prakash G"}', 'FUND_MANAGERS', null, 2320, true),
		   ('MANAGER_PRANAV_GOKHALE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pranav Gokhale", "selectedText": "Pranav G"}', 'FUND_MANAGERS', null, 2330, true),
		   ('MANAGER_PRANAV_GUPTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pranav Gupta", "selectedText": "Pranav G"}', 'FUND_MANAGERS', null, 2340, true),
		   ('MANAGER_PRANAVI_KULKARNI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pranavi Kulkarni", "selectedText": "Pranavi K"}', 'FUND_MANAGERS', null, 2350, true),
		   ('MANAGER_PRANAY_SINHA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pranay Sinha", "selectedText": "Pranay S"}', 'FUND_MANAGERS', null, 2360, true),
		   ('MANAGER_PRASANNA_PATHAK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Prasanna Pathak", "selectedText": "Prasanna P"}', 'FUND_MANAGERS', null, 2370, true),
		   ('MANAGER_PRASHANT_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Prashant Jain", "selectedText": "Prashant J"}', 'FUND_MANAGERS', null, 2380, true),
		   ('MANAGER_PRASHANT_PIMPLE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Prashant Pimple", "selectedText": "Prashant P"}', 'FUND_MANAGERS', null, 2390, true),
		   ('MANAGER_PRATEEK_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Prateek Jain", "selectedText": "Prateek J"}', 'FUND_MANAGERS', null, 2400, true),
		   ('MANAGER_PRATEEK_NIGUDKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Prateek Nigudkar", "selectedText": "Prateek N"}', 'FUND_MANAGERS', null, 2410, true),
		   ('MANAGER_PRATEEK_PODDAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Prateek Poddar", "selectedText": "Prateek P"}', 'FUND_MANAGERS', null, 2420, true),
		   ('MANAGER_PRATIBH_AGARWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pratibh Agarwal", "selectedText": "Pratibh A"}', 'FUND_MANAGERS', null, 2430, true),
		   ('MANAGER_PRATISH_KRISHNAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Pratish Krishnan", "selectedText": "Pratish K"}', 'FUND_MANAGERS', null, 2440, true),
		   ('MANAGER_PRAVEEN_AYATHAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Praveen Ayathan", "selectedText": "Praveen A"}', 'FUND_MANAGERS', null, 2450, true),
		   ('MANAGER_PRAVEEN_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Praveen Jain", "selectedText": "Praveen J"}', 'FUND_MANAGERS', null, 2460, true),
		   ('MANAGER_PREETHI_S', 'TERMED', jsonb'{}', jsonb'{"displayName": "Preethi S", "selectedText": "Preethi S"}', 'FUND_MANAGERS', null, 2470, true),
		   ('MANAGER_PRIYA_RANJAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Priya Ranjan", "selectedText": "Priya R"}', 'FUND_MANAGERS', null, 2480, true),
		   ('MANAGER_PRIYANKA_KHANDELWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Priyanka Khandelwal", "selectedText": "Priyanka K"}', 'FUND_MANAGERS', null, 2490, true),
		   ('MANAGER_PUNEET_PAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Puneet Pal", "selectedText": "Puneet P"}', 'FUND_MANAGERS', null, 2500, true),
		   ('MANAGER_R_ARUN', 'TERMED', jsonb'{}', jsonb'{"displayName": "R Arun", "selectedText": "R Arun"}', 'FUND_MANAGERS', null, 2510, true),
		   ('MANAGER_R_JANAKIRAMAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "R Janakiraman", "selectedText": "R Janakiraman"}', 'FUND_MANAGERS', null, 2520, true),
		   ('MANAGER_R_SIVAKUMAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "R Sivakumar", "selectedText": "R Sivakumar"}', 'FUND_MANAGERS', null, 2530, true),
		   ('MANAGER_R_SRINIVASAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "R Srinivasan", "selectedText": "R Srinivasan"}', 'FUND_MANAGERS', null, 2540, true),
		   ('MANAGER_RAHUL_DEDHIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rahul Dedhia", "selectedText": "Rahul D"}', 'FUND_MANAGERS', null, 2550, true),
		   ('MANAGER_RAHUL_GOSWAMI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rahul Goswami", "selectedText": "Rahul G"}', 'FUND_MANAGERS', null, 2560, true),
		   ('MANAGER_RAHUL_JAGWANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rahul Jagwani", "selectedText": "Rahul J"}', 'FUND_MANAGERS', null, 2570, true),
		   ('MANAGER_RAHUL_PAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rahul Pal", "selectedText": "Rahul P"}', 'FUND_MANAGERS', null, 2580, true),
		   ('MANAGER_RAHUL_SINGH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rahul Singh", "selectedText": "Rahul S"}', 'FUND_MANAGERS', null, 2590, true),
		   ('MANAGER_RAJ_GANDHI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Raj gandhi", "selectedText": "Raj g"}', 'FUND_MANAGERS', null, 2600, true),
		   ('MANAGER_RAJ_MEHTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Raj Mehta", "selectedText": "Raj M"}', 'FUND_MANAGERS', null, 2610, true),
		   ('MANAGER_RAJASA_KAKULAVARAPU', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rajasa Kakulavarapu", "selectedText": "Rajasa K"}', 'FUND_MANAGERS', null, 2620, true),
		   ('MANAGER_RAJAT_CHANDAK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rajat Chandak", "selectedText": "Rajat C"}', 'FUND_MANAGERS', null, 2630, true),
		   ('MANAGER_RAJEEV_RADHAKRISHNAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rajeev Radhakrishnan", "selectedText": "Rajeev R"}', 'FUND_MANAGERS', null, 2640, true),
		   ('MANAGER_RAJEEV_THAKKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rajeev Thakkar", "selectedText": "Rajeev T"}', 'FUND_MANAGERS', null, 2650, true),
		   ('MANAGER_RAJU_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Raju Sharma", "selectedText": "Raju S"}', 'FUND_MANAGERS', null, 2660, true),
		   ('MANAGER_RAKESH_VYAS', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rakesh Vyas", "selectedText": "Rakesh V"}', 'FUND_MANAGERS', null, 2670, true),
		   ('MANAGER_RAMA_SRINIVASAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rama Srinivasan", "selectedText": "Rama S"}', 'FUND_MANAGERS', null, 2680, true),
		   ('MANAGER_RAMNEEK_KUNDRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ramneek Kundra", "selectedText": "Ramneek K"}', 'FUND_MANAGERS', null, 2690, true),
		   ('MANAGER_RANJITHGOPAL_A.', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ranjithgopal A.", "selectedText": "Ranjithgopal A"}', 'FUND_MANAGERS', null, 2700, true),
		   ('MANAGER_RATISH_VARIER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ratish Varier", "selectedText": "Ratish V"}', 'FUND_MANAGERS', null, 2710, true),
		   ('MANAGER_RAUNAK_ONKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Raunak Onkar", "selectedText": "Raunak O"}', 'FUND_MANAGERS', null, 2720, true),
		   ('MANAGER_RAVI_ADUKIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ravi Adukia", "selectedText": "Ravi A"}', 'FUND_MANAGERS', null, 2730, true),
		   ('MANAGER_RAVI_GOPALAKRISHNAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ravi Gopalakrishnan", "selectedText": "Ravi G"}', 'FUND_MANAGERS', null, 2740, true),
		   ('MANAGER_RAVIPRAKASH_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Raviprakash Sharma", "selectedText": "Raviprakash S"}', 'FUND_MANAGERS', null, 2750, true),
		   ('MANAGER_RESHAM_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Resham Jain", "selectedText": "Resham J"}', 'FUND_MANAGERS', null, 2760, true),
		   ('MANAGER_RICHARD_DSOUZA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Richard DSouza", "selectedText": "Richard D"}', 'FUND_MANAGERS', null, 2770, true),
		   ('MANAGER_RISHI_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rishi Sharma", "selectedText": "Rishi S"}', 'FUND_MANAGERS', null, 2780, true),
		   ('MANAGER_RITESH_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ritesh Jain", "selectedText": "Ritesh J"}', 'FUND_MANAGERS', null, 2790, true),
		   ('MANAGER_RITESH_LUNAWAT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ritesh Lunawat", "selectedText": "Ritesh L"}', 'FUND_MANAGERS', null, 2800, true),
		   ('MANAGER_RITESH_NAMBIAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Ritesh Nambiar", "selectedText": "Ritesh N"}', 'FUND_MANAGERS', null, 2810, true),
		   ('MANAGER_ROHAN_KORDE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rohan Korde", "selectedText": "Rohan K"}', 'FUND_MANAGERS', null, 2820, true),
		   ('MANAGER_ROHAN_MARU', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rohan Maru", "selectedText": "Rohan M"}', 'FUND_MANAGERS', null, 2830, true),
		   ('MANAGER_ROHIT_SEKSARIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rohit Seksaria", "selectedText": "Rohit S"}', 'FUND_MANAGERS', null, 2840, true),
		   ('MANAGER_ROHIT_SHIMPI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rohit Shimpi", "selectedText": "Rohit S"}', 'FUND_MANAGERS', null, 2850, true),
		   ('MANAGER_ROHIT_SINGHANIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rohit Singhania", "selectedText": "Rohit S"}', 'FUND_MANAGERS', null, 2860, true),
		   ('MANAGER_ROSHAN_CHUTKEY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Roshan Chutkey", "selectedText": "Roshan C"}', 'FUND_MANAGERS', null, 2870, true),
		   ('MANAGER_ROSHI_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Roshi Jain", "selectedText": "Roshi J"}', 'FUND_MANAGERS', null, 2880, true),
		   ('MANAGER_RUKUN_TARACHANDANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rukun Tarachandani", "selectedText": "Rukun T"}', 'FUND_MANAGERS', null, 2890, true),
		   ('MANAGER_RUPESH_PATEL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Rupesh Patel", "selectedText": "Rupesh P"}', 'FUND_MANAGERS', null, 2900, true),
		   ('MANAGER_S_BHARATH', 'TERMED', jsonb'{}', jsonb'{"displayName": "S Bharath", "selectedText": "S Bharath"}', 'FUND_MANAGERS', null, 2910, true),
		   ('MANAGER_SACHIN_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sachin Jain", "selectedText": "Sachin J"}', 'FUND_MANAGERS', null, 2920, true),
		   ('MANAGER_SACHIN_PADWAL-DESAI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sachin Padwal-Desai", "selectedText": "Sachin P"}', 'FUND_MANAGERS', null, 2930, true),
		   ('MANAGER_SACHIN_RELEKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sachin Relekar", "selectedText": "Sachin R"}', 'FUND_MANAGERS', null, 2940, true),
		   ('MANAGER_SACHIN_TRIVEDI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sachin Trivedi", "selectedText": "Sachin T"}', 'FUND_MANAGERS', null, 2950, true),
		   ('MANAGER_SAHIL_SHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sahil Shah", "selectedText": "Sahil S"}', 'FUND_MANAGERS', null, 2960, true),
		   ('MANAGER_SAILESH_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sailesh Jain", "selectedText": "Sailesh J"}', 'FUND_MANAGERS', null, 2970, true),
		   ('MANAGER_SAILESH_RAJ BHAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sailesh Raj Bhan", "selectedText": "Sailesh R"}', 'FUND_MANAGERS', null, 2980, true),
		   ('MANAGER_SAMIR_RACHH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Samir Rachh", "selectedText": "Samir R"}', 'FUND_MANAGERS', null, 2990, true),
		   ('MANAGER_SANDEEP_AGARWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sandeep Agarwal", "selectedText": "Sandeep A"}', 'FUND_MANAGERS', null, 3000, true),
		   ('MANAGER_SANDEEP_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sandeep Jain", "selectedText": "Sandeep J"}', 'FUND_MANAGERS', null, 3010, true),
		   ('MANAGER_SANDEEP_MANAM', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sandeep Manam", "selectedText": "Sandeep M"}', 'FUND_MANAGERS', null, 3020, true),
		   ('MANAGER_SANDEEP_TANDON', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sandeep Tandon", "selectedText": "Sandeep T"}', 'FUND_MANAGERS', null, 3030, true),
		   ('MANAGER_SANDEEP_YADAV', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sandeep Yadav", "selectedText": "Sandeep Y"}', 'FUND_MANAGERS', null, 3040, true),
		   ('MANAGER_SANJAY_BEMBALKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sanjay Bembalkar", "selectedText": "Sanjay B"}', 'FUND_MANAGERS', null, 3050, true),
		   ('MANAGER_SANJAY_CHAWLA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sanjay Chawla", "selectedText": "Sanjay C"}', 'FUND_MANAGERS', null, 3060, true),
		   ('MANAGER_SANJAY_DOSHI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sanjay Doshi", "selectedText": "Sanjay D"}', 'FUND_MANAGERS', null, 3070, true),
		   ('MANAGER_SANJAY_GODAMBE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sanjay Godambe", "selectedText": "Sanjay G"}', 'FUND_MANAGERS', null, 3080, true),
		   ('MANAGER_SANJAY_PAWAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sanjay Pawar", "selectedText": "Sanjay P"}', 'FUND_MANAGERS', null, 3090, true),
		   ('MANAGER_SANJEEV_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sanjeev Sharma", "selectedText": "Sanjeev S"}', 'FUND_MANAGERS', null, 3100, true),
		   ('MANAGER_SANKALP_BAID', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sankalp Baid", "selectedText": "Sankalp B"}', 'FUND_MANAGERS', null, 3110, true),
		   ('MANAGER_SANKARAN_NAREN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sankaran Naren", "selectedText": "Sankaran N"}', 'FUND_MANAGERS', null, 3120, true),
		   ('MANAGER_SANTOSH_KAMATH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Santosh Kamath", "selectedText": "Santosh K"}', 'FUND_MANAGERS', null, 3130, true),
		   ('MANAGER_SANTOSH_SINGH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Santosh Singh", "selectedText": "Santosh S"}', 'FUND_MANAGERS', null, 3140, true),
		   ('MANAGER_SATISH_DONDAPATI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Satish Dondapati", "selectedText": "Satish D"}', 'FUND_MANAGERS', null, 3150, true),
		   ('MANAGER_SATISH_MISHRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Satish Mishra", "selectedText": "Satish M"}', 'FUND_MANAGERS', null, 3160, true),
		   ('MANAGER_SATISH_RAMANATHAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Satish Ramanathan", "selectedText": "Satish R"}', 'FUND_MANAGERS', null, 3170, true),
		   ('MANAGER_SATYABRATA_MOHANTY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Satyabrata Mohanty", "selectedText": "Satyabrata M"}', 'FUND_MANAGERS', null, 3180, true),
		   ('MANAGER_SAURABH_PANT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Saurabh Pant", "selectedText": "Saurabh P"}', 'FUND_MANAGERS', null, 3190, true),
		   ('MANAGER_SHADAB_RIZVI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Shadab Rizvi", "selectedText": "Shadab R"}', 'FUND_MANAGERS', null, 3200, true),
		   ('MANAGER_SHALINI_TIBREWALA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Shalini Tibrewala", "selectedText": "Shalini T"}', 'FUND_MANAGERS', null, 3210, true),
		   ('MANAGER_SHARMILA_DMELLO', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sharmila DMello", "selectedText": "Sharmila D"}', 'FUND_MANAGERS', null, 3220, true),
		   ('MANAGER_SHARWAN_GOYAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sharwan Goyal", "selectedText": "Sharwan G"}', 'FUND_MANAGERS', null, 3230, true),
		   ('MANAGER_SHASHANK_VERMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Shashank Verma", "selectedText": "Shashank V"}', 'FUND_MANAGERS', null, 3240, true),
		   ('MANAGER_SHIBANI_KURIAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Shibani Kurian", "selectedText": "Shibani K"}', 'FUND_MANAGERS', null, 3250, true),
		   ('MANAGER_SHOBHIT_MEHROTRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Shobhit Mehrotra", "selectedText": "Shobhit M"}', 'FUND_MANAGERS', null, 3260, true),
		   ('MANAGER_SHREYASH_DEVALKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Shreyash Devalkar", "selectedText": "Shreyash D"}', 'FUND_MANAGERS', null, 3270, true),
		   ('MANAGER_SHRIDATTA_BHANDWALDAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Shridatta Bhandwaldar", "selectedText": "Shridatta B"}', 'FUND_MANAGERS', null, 3280, true),
		   ('MANAGER_SHRIRAM_RAMANATHAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Shriram Ramanathan", "selectedText": "Shriram R"}', 'FUND_MANAGERS', null, 3290, true),
		   ('MANAGER_SIDDHANT_CHHABRIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Siddhant Chhabria", "selectedText": "Siddhant C"}', 'FUND_MANAGERS', null, 3300, true),
		   ('MANAGER_SIDDHARTH_BOTHRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Siddharth Bothra", "selectedText": "Siddharth B"}', 'FUND_MANAGERS', null, 3310, true),
		   ('MANAGER_SIDDHARTH_DEB', 'TERMED', jsonb'{}', jsonb'{"displayName": "Siddharth Deb", "selectedText": "Siddharth D"}', 'FUND_MANAGERS', null, 3320, true),
		   ('MANAGER_SILKY_JAIN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Silky Jain", "selectedText": "Silky J"}', 'FUND_MANAGERS', null, 3330, true),
		   ('MANAGER_SNEHA_JOSHI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sneha Joshi", "selectedText": "Sneha J"}', 'FUND_MANAGERS', null, 3340, true),
		   ('MANAGER_SOHINI_ANDANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sohini Andani", "selectedText": "Sohini A"}', 'FUND_MANAGERS', null, 3350, true),
		   ('MANAGER_SONAL_GUPTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sonal Gupta", "selectedText": "Sonal G"}', 'FUND_MANAGERS', null, 3360, true),
		   ('MANAGER_SONAM_UDASI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sonam Udasi", "selectedText": "Sonam U"}', 'FUND_MANAGERS', null, 3370, true),
		   ('MANAGER_SORBH_GUPTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sorbh Gupta", "selectedText": "Sorbh G"}', 'FUND_MANAGERS', null, 3380, true),
		   ('MANAGER_SRI_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sri Sharma", "selectedText": "Sri S"}', 'FUND_MANAGERS', null, 3390, true),
		   ('MANAGER_SRINIVAS_RAVURI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Srinivas Ravuri", "selectedText": "Srinivas R"}', 'FUND_MANAGERS', null, 3400, true),
		   ('MANAGER_SRINIVASAN_RAMAMURTHY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Srinivasan Ramamurthy", "selectedText": "Srinivasan R"}', 'FUND_MANAGERS', null, 3410, true),
		   ('MANAGER_SUDHIR_AGARWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sudhir Agarwal", "selectedText": "Sudhir A"}', 'FUND_MANAGERS', null, 3420, true),
		   ('MANAGER_SUDHIR_KEDIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sudhir Kedia", "selectedText": "Sudhir K"}', 'FUND_MANAGERS', null, 3430, true),
		   ('MANAGER_SUMAN_PRASAD', 'TERMED', jsonb'{}', jsonb'{"displayName": "Suman Prasad", "selectedText": "Suman P"}', 'FUND_MANAGERS', null, 3440, true),
		   ('MANAGER_SUMIT_AGRAWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sumit Agrawal", "selectedText": "Sumit A"}', 'FUND_MANAGERS', null, 3450, true),
		   ('MANAGER_SUMIT_BHATNAGAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sumit Bhatnagar", "selectedText": "Sumit B"}', 'FUND_MANAGERS', null, 3460, true),
		   ('MANAGER_SUNAINA_CUNHA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sunaina Cunha", "selectedText": "Sunaina C"}', 'FUND_MANAGERS', null, 3470, true),
		   ('MANAGER_SUNIL_PATIL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sunil Patil", "selectedText": "Sunil P"}', 'FUND_MANAGERS', null, 3480, true),
		   ('MANAGER_SURBHI_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Surbhi Sharma", "selectedText": "Surbhi S"}', 'FUND_MANAGERS', null, 3490, true),
		   ('MANAGER_SUSHIL_BUDHIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Sushil Budhia", "selectedText": "Sushil B"}', 'FUND_MANAGERS', null, 3500, true),
		   ('MANAGER_SUYASH_CHOUDHARY', 'TERMED', jsonb'{}', jsonb'{"displayName": "Suyash Choudhary", "selectedText": "Suyash C"}', 'FUND_MANAGERS', null, 3510, true),
		   ('MANAGER_SWAPNIL_MAYEKAR', 'TERMED', jsonb'{}', jsonb'{"displayName": "Swapnil Mayekar", "selectedText": "Swapnil M"}', 'FUND_MANAGERS', null, 3520, true),
		   ('MANAGER_SWATI_KULKARNI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Swati Kulkarni", "selectedText": "Swati K"}', 'FUND_MANAGERS', null, 3530, true),
		   ('MANAGER_TAHER_BADSHAH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Taher Badshah", "selectedText": "Taher B"}', 'FUND_MANAGERS', null, 3540, true),
		   ('MANAGER_TANMAYA_DESAI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Tanmaya Desai", "selectedText": "Tanmaya D"}', 'FUND_MANAGERS', null, 3550, true),
		   ('MANAGER_TARUN_SINGH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Tarun Singh", "selectedText": "Tarun S"}', 'FUND_MANAGERS', null, 3560, true),
		   ('MANAGER_TEJAS_GUTKA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Tejas Gutka", "selectedText": "Tejas G"}', 'FUND_MANAGERS', null, 3570, true),
		   ('MANAGER_TEJAS_SHETH', 'TERMED', jsonb'{}', jsonb'{"displayName": "Tejas Sheth", "selectedText": "Tejas S"}', 'FUND_MANAGERS', null, 3580, true),
		   ('MANAGER_TRIDEEP_BHATTACHARYA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Trideep Bhattacharya", "selectedText": "Trideep B"}', 'FUND_MANAGERS', null, 3590, true),
		   ('MANAGER_UMESH_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Umesh Sharma", "selectedText": "Umesh S"}', 'FUND_MANAGERS', null, 3600, true),
		   ('MANAGER_UTKARSH_KATKORIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Utkarsh Katkoria", "selectedText": "Utkarsh K"}', 'FUND_MANAGERS', null, 3610, true),
		   ('MANAGER_V_BALASUBRAMANIAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "V Balasubramanian", "selectedText": "V Balasubramanian"}', 'FUND_MANAGERS', null, 3620, true),
		   ('MANAGER_V_SRIVATSA', 'TERMED', jsonb'{}', jsonb'{"displayName": "V Srivatsa", "selectedText": "V Srivatsa"}', 'FUND_MANAGERS', null, 3630, true),
		   ('MANAGER_VAIBHAV_DUSAD', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vaibhav Dusad", "selectedText": "Vaibhav D"}', 'FUND_MANAGERS', null, 3640, true),
		   ('MANAGER_VARUN_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Varun Sharma", "selectedText": "Varun S"}', 'FUND_MANAGERS', null, 3650, true),
		   ('MANAGER_VASAV_SAHGAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vasav Sahgal", "selectedText": "Vasav S"}', 'FUND_MANAGERS', null, 3660, true),
		   ('MANAGER_VENKAT_SAMALA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Venkat Samala", "selectedText": "Venkat S"}', 'FUND_MANAGERS', null, 3670, true),
		   ('MANAGER_VENKATESH_SANJEEVI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Venkatesh Sanjeevi", "selectedText": "Venkatesh S"}', 'FUND_MANAGERS', null, 3680, true),
		   ('MANAGER_VENUGOPAL_MANGHAT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Venugopal Manghat", "selectedText": "Venugopal M"}', 'FUND_MANAGERS', null, 3690, true),
		   ('MANAGER_VIHAG_MISHRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vihag Mishra", "selectedText": "Vihag M"}', 'FUND_MANAGERS', null, 3700, true),
		   ('MANAGER_VIHANG_NAIK', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vihang Naik", "selectedText": "Vihang N"}', 'FUND_MANAGERS', null, 3710, true),
		   ('MANAGER_VIKAS_AGRAWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vikas Agrawal", "selectedText": "Vikas A"}', 'FUND_MANAGERS', null, 3720, true),
		   ('MANAGER_VIKAS_GARG', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vikas Garg", "selectedText": "Vikas G"}', 'FUND_MANAGERS', null, 3730, true),
		   ('MANAGER_VIKASH_AGARWAL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vikash Agarwal", "selectedText": "Vikash A"}', 'FUND_MANAGERS', null, 3740, true),
		   ('MANAGER_VIKRAM_CHOPRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vikram Chopra", "selectedText": "Vikram C"}', 'FUND_MANAGERS', null, 3750, true),
		   ('MANAGER_VIKRAM_PAMNANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vikram Pamnani", "selectedText": "Vikram P"}', 'FUND_MANAGERS', null, 3760, true),
		   ('MANAGER_VIKRANT_MEHTA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vikrant Mehta", "selectedText": "Vikrant M"}', 'FUND_MANAGERS', null, 3770, true),
		   ('MANAGER_VINAY_PAHARIA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vinay Paharia", "selectedText": "Vinay P"}', 'FUND_MANAGERS', null, 3780, true),
		   ('MANAGER_VINAY_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vinay Sharma", "selectedText": "Vinay S"}', 'FUND_MANAGERS', null, 3790, true),
		   ('MANAGER_VINEET_MALOO', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vineet Maloo", "selectedText": "Vineet M"}', 'FUND_MANAGERS', null, 3800, true),
		   ('MANAGER_VINIT_SAMBRE', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vinit Sambre", "selectedText": "Vinit S"}', 'FUND_MANAGERS', null, 3810, true),
		   ('MANAGER_VINOD_BHAT', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vinod Bhat", "selectedText": "Vinod B"}', 'FUND_MANAGERS', null, 3820, true),
		   ('MANAGER_VIRAJ_KULKARNI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Viraj Kulkarni", "selectedText": "Viraj K"}', 'FUND_MANAGERS', null, 3830, true),
		   ('MANAGER_VISHAL_CHOPDA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vishal Chopda", "selectedText": "Vishal C"}', 'FUND_MANAGERS', null, 3840, true),
		   ('MANAGER_VISHAL_GAJWANI', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vishal Gajwani", "selectedText": "Vishal G"}', 'FUND_MANAGERS', null, 3850, true),
		   ('MANAGER_VISHAL_MISHRA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vishal Mishra", "selectedText": "Vishal M"}', 'FUND_MANAGERS', null, 3860, true),
		   ('MANAGER_VISHAL_THAKKER', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vishal Thakker", "selectedText": "Vishal T"}', 'FUND_MANAGERS', null, 3870, true),
		   ('MANAGER_VIVEK_RAMAKRISHNAN', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vivek Ramakrishnan", "selectedText": "Vivek R"}', 'FUND_MANAGERS', null, 3880, true),
		   ('MANAGER_VIVEK_SHARMA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vivek Sharma", "selectedText": "Vivek S"}', 'FUND_MANAGERS', null, 3890, true),
		   ('MANAGER_VRIJESH_KASERA', 'TERMED', jsonb'{}', jsonb'{"displayName": "Vrijesh Kasera", "selectedText": "Vrijesh K"}', 'FUND_MANAGERS', null, 3900, true),
		   ('MANAGER_YOGESH_PATIL', 'TERMED', jsonb'{}', jsonb'{"displayName": "Yogesh Patil", "selectedText": "Yogesh P"}', 'FUND_MANAGERS', null, 3910, true),

			-- Suggested investment duration
		   ('LESS_THAN_1MON_INV_DUR', 'TERMED', jsonb'{}', jsonb'{"displayName": "less than 1m", "selectedText": "less than 1m investment duration"}', 'SUGGESTED_INV_DURATION', null, 10, true),
		   ('6MON_TO_1YR_INV_DUR', 'TERMED', jsonb'{}', jsonb'{"displayName": "6m to 1y", "selectedText": "6m to 1y investment duration"}', 'SUGGESTED_INV_DURATION', null, 20, true),
		   ('1YR_TO_3YR_INV_DUR', 'TERMED', jsonb'{}', jsonb'{"displayName": "1y to 3y", "selectedText": "1y to 3y investment duration"}', 'SUGGESTED_INV_DURATION', null, 30, true),
		   ('3YR_TO_5YR_INV_DUR', 'TERMED', jsonb'{}', jsonb'{"displayName": "3y to 5y", "selectedText": "3y to 5y investment duration"}', 'SUGGESTED_INV_DURATION', null, 40, true),
		   ('5YR_TO_7YR_INV_DUR', 'TERMED', jsonb'{}', jsonb'{"displayName": "5y to 7y", "selectedText": "5y to 7y investment duration"}', 'SUGGESTED_INV_DURATION', null, 50, true),
		   ('MORE_THAN_7YR_INV_DUR', 'TERMED', jsonb'{}', jsonb'{"displayName": "more than 7y", "selectedText": "more than 7y investment duration"}', 'SUGGESTED_INV_DURATION', null, 60, true)

) INSERT INTO mf_filters (id, name, type, query_info, display_info, filter_group_id, child_group_ids, weight, enabled)
SELECT gen_random_uuid(), name, type, query_info, display_info, filter_group_id, child_group_ids, weight, enabled
FROM filter_data
-- update the record if it already exists, instead of inserting
ON CONFLICT (name)
	DO UPDATE
	SET (type, query_info, display_info, filter_group_id, child_group_ids, weight, enabled) =
	    (excluded.type, excluded.query_info, excluded.display_info, excluded.filter_group_id, excluded.child_group_ids, excluded.weight, excluded.enabled);
