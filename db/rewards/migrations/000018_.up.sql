-- create reward_offer_groups table
CREATE TABLE if not exists reward_offer_groups (
   id uuid default uuid_generate_v4 () not null constraint reward_offer_groups_pkey primary key,
   user_reward_aggregate  int not null,
   created_at timestamp with time zone default now() not null,
   updated_at timestamp with time zone default now() not null
);
comment on table reward_offer_groups is 'entity created for grouping multiple reward offers under same group for creating across reward offers inventory';

-- create reward_offer_group_actor_aggregates table
CREATE TABLE if not exists reward_offer_group_actor_aggregates (
    id uuid default uuid_generate_v4 () not null constraint reward_offer_group_actor_aggregates_pkey primary key,
    offer_group_id   varchar not null,
    actor_id   varchar not null,
    count      int not null,
    created_at timestamp with time zone default now() not null,
    updated_at timestamp with time zone default now() not null,
    -- unique constraint on actor_id and offer_group_id combination
    constraint reward_offer_group_actor_aggr_unique_actor_id_offer_group_id unique (actor_id, offer_group_id)
);
comment on table reward_offer_group_actor_aggregates is 'stores information of reward offer group inventory for an actor';

-- add group_id column in reward_offers table
alter table if exists reward_offers add column if not exists group_id varchar default null;
comment on column reward_offers.group_id is 'denotes the id of group to which this offer belongs';
