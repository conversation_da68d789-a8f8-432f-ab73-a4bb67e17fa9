-- add offer_type column in reward_offers table
alter table if exists reward_offers add column if not exists offer_type varchar default 'UNSPECIFIED_REWARD_OFFER_TYPE';
comment on column reward_offers.offer_type is 'denotes the type of offer e.g REFERRAL_REFEREE_OFFER denotes referral reward offer for referee';

-- add offer_type column in rewards table
alter table if exists rewards add column if not exists offer_type varchar default 'UNSPECIFIED_REWARD_OFFER_TYPE';
comment on column rewards.offer_type is 'denotes the type of offer responsible for this reward e.g REFERRAL_REFEREE_OFFER denotes referral reward offer for referee';

-- add external_ref column in rewards table
alter table if exists rewards add column if not exists external_ref varchar default null;
comment on column rewards.external_ref is 'used to link reward with an external ref/entity e.g. linking finiteCode to a referral rewards';

-- add global_dedupe_id column in rewards table
alter table if exists rewards add column if not exists global_dedupe_id varchar default null;
comment on column rewards.global_dedupe_id is 'used for special deduping cases for deduping rewards for an actor';

-- add composite unique index on (actor_id, reward_global_dedupe_id) in rewards table
create unique index if not exists rewards_actor_id_global_dedupe_id_unique_idx on rewards (actor_id, global_dedupe_id);
