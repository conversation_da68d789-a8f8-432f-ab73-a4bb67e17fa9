ALTER TABLE ro_group_reward_units_actor_utilisation_in_time_period  ADD COLUMN IF NOT EXISTS cc_bill_eraser_units int;
ALTER TABLE reward_offer_reward_units_actor_utilisation_in_time_period ADD COLUMN IF NOT EXISTS  cc_bill_eraser_units int;
ALTER TABLE reward_offer_reward_units_actor_utilisation ADD COLUMN IF NOT EXISTS  cc_bill_eraser_units int;
ALTER TABLE reward_offer_group_reward_units_actor_utilisation ADD COLUMN IF NOT EXISTS cc_bill_eraser_units int;
