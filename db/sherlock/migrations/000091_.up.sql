CREATE TABLE IF NOT EXISTS escalations (
	id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
	ticket_id INT NOT NULL,
	external_reference_id VARCHAR,
    escalation_type VARCHAR NOT NULL,
	status VARCHAR NOT NULL,
    actor_id VARCHAR NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (id),
    UNIQUE (ticket_id, escalation_type)
);

COMMENT ON COLUMN escalations.ticket_id IS 'Id of support ticket for which escalation is done';
COMMENT ON COLUMN escalations.actor_id IS 'User identifier for whom escalation is created';
COMMENT ON COLUMN escalations.external_reference_id IS 'External reference ID related to the escalation provided by system where escalation is done';
COMMENT ON COLUMN escalations.status IS 'Current status of the escalation (e.g., Open, Closed, In Progress)';
COMMENT ON COLUMN escalations.escalation_type IS 'Type of system where escalation is done (e.g., Federal, Product team)';

CREATE INDEX IF NOT EXISTS idx_escalations_external_reference_id ON escalations(external_reference_id);
CREATE INDEX IF NOT EXISTS idx_escalations_updated_at ON escalations(updated_at);

CREATE TABLE IF NOT EXISTS escalation_updates (
	id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
	escalation_id uuid NOT NULL,
	payload_type VARCHAR NOT NULL,
	payload JSONB NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (id)
);

COMMENT ON COLUMN escalation_updates.escalation_id IS 'ID of the escalation being updated';
COMMENT ON COLUMN escalation_updates.payload_type IS 'The type of update payload (e.g., Comment, Status Change)';
COMMENT ON COLUMN escalation_updates.payload IS 'The raw JSON payload containing update details';

CREATE INDEX IF NOT EXISTS idx_escalation_updates_escalation_id ON escalation_updates(escalation_id);
CREATE INDEX IF NOT EXISTS idx_escalation_updates_updated_at ON escalation_updates(updated_at);

CREATE TABLE IF NOT EXISTS escalation_attachments (
	id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
	escalation_id uuid NOT NULL,
	attachment_id VARCHAR,
	s3_path VARCHAR NOT NULL,
	uploaded_by VARCHAR NOT NULL,
	status VARCHAR NOT NULL,
	created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
	PRIMARY KEY (id)
);

COMMENT ON COLUMN escalation_attachments.escalation_id IS 'ID of the escalation associated with this attachment';
COMMENT ON COLUMN escalation_attachments.attachment_id IS 'Unique identifier for the attachment file provided by escalation system';
COMMENT ON COLUMN escalation_attachments.s3_path IS 'S3 storage path where the file is located';
COMMENT ON COLUMN escalation_attachments.uploaded_by IS 'email id of the user who uploaded the attachment';
COMMENT ON COLUMN escalation_attachments.status IS 'Status of the attachment (e.g., Active, Deleted)';

CREATE INDEX IF NOT EXISTS idx_escalation_attachments_escalation_id ON escalation_attachments(escalation_id);
CREATE INDEX IF NOT EXISTS idx_escalation_attachments_updated_at ON escalation_attachments(updated_at);
