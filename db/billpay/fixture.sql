INSERT INTO recharge_orders (id, client_request_id, actor_id, account_type, account_identifier, account_operator, plan_details, status, sub_status, created_at, updated_at) VALUES
 ('order-1', '6683df89-97cd-4f34-bd0a-4a6aba0f6bf7', 'test-actor', 'RECHARGE_ACCOUNT_TYPE_MOBILE', '**********', 'OPERATOR_AIRTEL', '{"mobileRechargePlanDetails": {"planName": "Test Plan", "amount": {"currencyCode": "INR", "units": "100", "nanos": *********}, "validity": "28 days", "planDescription": "Test plan description"}}', 'RECHARGE_ORDER_STATUS_SUCCESS', 'RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED', NOW() - INTERVAL '2 HOUR', NOW() - INTERVAL '2 HOUR'),
 ('order-2', '6683df89-97cd-4f34-bd0a-4a6aba0f6bf8', 'test-actor', 'RECHARGE_ACCOUNT_TYPE_MOBILE', '**********', 'OPERATOR_AIRTEL', '{"mobileRechargePlanDetails": {"planName": "Test Plan", "amount": {"currencyCode": "INR", "units": "100", "nanos": *********}, "validity": "28 days", "planDescription": "Test plan description"}}', 'RECHARGE_ORDER_STATUS_INITIATED', 'RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED', NOW() - INTERVAL '1 HOUR', NOW() - INTERVAL '1 HOUR'),
 ('order-3', '6683df89-97cd-4f34-bd0a-4a6aba0f6bf9', 'test-actor', 'RECHARGE_ACCOUNT_TYPE_MOBILE', '**********', 'OPERATOR_AIRTEL', '{"mobileRechargePlanDetails": {"planName": "Test Plan", "amount": {"currencyCode": "INR", "units": "100", "nanos": *********}, "validity": "28 days", "planDescription": "Test plan description"}}', 'RECHARGE_ORDER_STATUS_INITIATED', 'RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED', NOW(), NOW()),
 ('order-4', '6683df89-97cd-4f34-bd0a-4a6aba0f6bf0', 'another-actor', 'RECHARGE_ACCOUNT_TYPE_MOBILE', '**********', 'OPERATOR_AIRTEL', '{"mobileRechargePlanDetails": {"planName": "Test Plan", "amount": {"currencyCode": "INR", "units": "100", "nanos": *********}, "validity": "28 days", "planDescription": "Test plan description"}}', 'RECHARGE_ORDER_STATUS_INITIATED', 'RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED', NOW(), NOW());
