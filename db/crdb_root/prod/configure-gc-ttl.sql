ALTER TABLE epifi.public.timelines CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.actor_pi_resolutions CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.actors CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.blocked_actors_map CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.payment_instruments CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.account_pis CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.aa_account_pis CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.pi_state_logs CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.account_merchant_infos CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.card_merchant_infos CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.merchant_pis CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.merchant_pi_gplace_data CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.merchants CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.probable_known_merchants CONFIGURE ZONE USING gc.ttlseconds=60;
ALTER TABLE epifi.public.payment_instrument_purge_audits CONFIGURE ZONE USING gc.ttlseconds=60;
