package rewardoffers

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"errors"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"

	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	besalarypgmock "github.com/epifi/gamma/api/salaryprogram/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	segmentMocks "github.com/epifi/gamma/api/segment/mocks"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	typesAccountPb "github.com/epifi/gamma/api/typesv2/account"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	mocks3 "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/helper"
	mock_dao "github.com/epifi/gamma/rewards/test/mocks/generator/dao"
	mocks "github.com/epifi/gamma/rewards/test/mocks/helper"
)

var (
	currentTime      = time.Now()
	time20MinsBefore = currentTime.Add(-20 * time.Minute)
)

type mockService struct {
	mockUserHelperSvc  *mocks.MockIUserHelperService
	mockRewardOfferDao *mock_dao.MockRewardOfferDao
	mockSalaryPgCl     *besalarypgmock.MockSalaryProgramClient
	mockSegmentClient  *segmentMocks.MockSegmentationServiceClient
	mockOnboardingCl   *mocks3.MockOnboardingClient
}

func TestRewardOfferService_GetRewardOffersForActor(t *testing.T) {
	t.Skip("Need to handle mock client and responses. More details in https://github.com/epiFi/gamma/pull/130738")
	// reward offer without any tags
	nonTaggedRewardOffer := &rewardOffersPb.RewardOffer{
		Id: "reward-offer-1",
	}
	// reward offer with inapp referral tag
	inappReferralSalaryTierRewardOffer := &rewardOffersPb.RewardOffer{
		Id:   "reward-offer-2",
		Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_IN_APP_REFERRAL, rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY},
	}
	// reward offer with influencer referral tag
	influencerReferralRewardOffer := &rewardOffersPb.RewardOffer{
		Id:   "reward-offer-4",
		Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_INFLUENCER_REFERRAL, rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_INFINITE},
	}
	// reward offer with non referral tag.
	nonReferralUserRewardOffer1 := &rewardOffersPb.RewardOffer{
		Id:   "reward-offer-5",
		Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_NON_REFERRAL, rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY, rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_INFINITE},
	}
	// reward offer with non referral tag and display constraint configured
	nonReferralUserRewardOffer2 := &rewardOffersPb.RewardOffer{
		Id:   "reward-offer-5",
		Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_NON_REFERRAL, rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY, rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_INFINITE},
		DisplayMeta: &rewardOffersPb.DisplayMeta{
			DisplayConstraintExpression: "DURATION_SINCE_ONBOARDING_IN_MINS < 10",
		},
	}

	rewardOffersList1 := []*rewardOffersPb.RewardOffer{nonTaggedRewardOffer, inappReferralSalaryTierRewardOffer, influencerReferralRewardOffer, nonReferralUserRewardOffer1, nonReferralUserRewardOffer2}

	type args struct {
		ctx     context.Context
		request *rewardOffersPb.GetRewardOffersForActorRequest
	}
	tests := []struct {
		name         string
		args         args
		setUpMocks   func(m *mockService)
		wantResponse *rewardOffersPb.GetRewardOffersForActorResponse
		wantErr      bool
	}{
		{
			name: "Fetch reward offers for non referral user (offer with display constraint not passing should get filtered out)",
			args: args{
				ctx:     context.Background(),
				request: &rewardOffersPb.GetRewardOffersForActorRequest{ActorId: "actor-1"},
			},
			setUpMocks: func(m *mockService) {
				m.mockRewardOfferDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				m.mockUserHelperSvc.EXPECT().GetUserSavingsAccountState(gomock.Any(), "actor-1", typesAccountPb.AccountProductOffering_APO_REGULAR).Return(savingsPb.State_CREATED, nil)
				m.mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is a non referral user.
					IsOnboardedThroughReferral: false,
				}, nil)
				m.mockUserHelperSvc.EXPECT().GetAccountTierForActorAtTime(gomock.Any(), "actor-1", gomock.Any()).Return(tieringExtPb.Tier_TIER_FI_SALARY, nil, nil)
				m.mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)
				m.mockSalaryPgCl.EXPECT().GetCurrentRegStatusAndNextRegStage(gomock.Any(), gomock.Any()).Return(&beSalaryPb.CurrentRegStatusAndNextRegStageResponse{
					Status:             rpc.StatusOk(),
					RegistrationId:     "",
					RegistrationStatus: beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					NextStage:          0,
				}, nil)
				m.mockSalaryPgCl.EXPECT().GetLatestActivationDetailsActiveAtTime(gomock.Any(), gomock.Any()).Return(&beSalaryPb.LatestActivationDetailsActiveAtTimeResponse{
					Status: rpc.StatusOk(),
				}, nil)
				m.mockOnboardingCl.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Eq(&onbPb.GetFeatureDetailsRequest{ActorId: "actor-1"})).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				// Mock segment client calls that return empty results (no segment-based filtering)
				m.mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberResponse{
					Status:               rpc.StatusOk(),
					SegmentMembershipMap: make(map[string]*segmentPb.SegmentMembership),
				}, nil).AnyTimes()
				m.mockSegmentClient.EXPECT().IsMemberOfExpressions(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status:                         rpc.StatusOk(),
					SegmentExpressionMembershipMap: make(map[string]*segmentPb.SegmentExpressionMembership),
				}, nil).AnyTimes()
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForActorResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{nonTaggedRewardOffer, nonReferralUserRewardOffer1},
			},
		},
		{
			name: "Fetch reward offers for non referral user, (offer with display constraint passing should get filtered in)",
			args: args{
				ctx:     context.Background(),
				request: &rewardOffersPb.GetRewardOffersForActorRequest{ActorId: "actor-1"},
			},
			setUpMocks: func(m *mockService) {
				m.mockRewardOfferDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				m.mockUserHelperSvc.EXPECT().GetUserSavingsAccountState(gomock.Any(), "actor-1", typesAccountPb.AccountProductOffering_APO_REGULAR).Return(savingsPb.State_CREATED, nil)
				m.mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is a non referral user.
					IsOnboardedThroughReferral: false,
				}, nil)
				m.mockUserHelperSvc.EXPECT().GetAccountTierForActorAtTime(gomock.Any(), "actor-1", gomock.Any()).Return(tieringExtPb.Tier_TIER_FI_SALARY, nil, nil)
				// returning currentTime as onb completion time so that the nonReferralUserRewardOffer2 display constraint passes and it should get filtered in.
				m.mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(currentTime), nil)
				m.mockSalaryPgCl.EXPECT().GetCurrentRegStatusAndNextRegStage(gomock.Any(), gomock.Any()).Return(&beSalaryPb.CurrentRegStatusAndNextRegStageResponse{
					Status:             rpc.StatusOk(),
					RegistrationId:     "",
					RegistrationStatus: beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED,
					NextStage:          0,
				}, nil)
				m.mockOnboardingCl.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Eq(&onbPb.GetFeatureDetailsRequest{ActorId: "actor-1"})).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				// Mock segment client calls that return empty results (no segment-based filtering)
				m.mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberResponse{
					Status:               rpc.StatusOk(),
					SegmentMembershipMap: make(map[string]*segmentPb.SegmentMembership),
				}, nil).AnyTimes()
				m.mockSegmentClient.EXPECT().IsMemberOfExpressions(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status:                         rpc.StatusOk(),
					SegmentExpressionMembershipMap: make(map[string]*segmentPb.SegmentExpressionMembership),
				}, nil).AnyTimes()
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForActorResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{nonTaggedRewardOffer, nonReferralUserRewardOffer1, nonReferralUserRewardOffer2},
			},
		},
		{
			name: "Fetch reward offers for inapp referral user",
			args: args{
				ctx:     context.Background(),
				request: &rewardOffersPb.GetRewardOffersForActorRequest{ActorId: "actor-1"},
			},
			setUpMocks: func(m *mockService) {
				m.mockRewardOfferDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				m.mockUserHelperSvc.EXPECT().GetUserSavingsAccountState(gomock.Any(), "actor-1", typesAccountPb.AccountProductOffering_APO_REGULAR).Return(savingsPb.State_CREATED, nil)
				m.mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is an inapp referral user with regular finite code.
					IsOnboardedThroughReferral: true,
					FiniteCodeChannel:          inAppReferralEnumPb.FiniteCodeChannel_IN_APP_REFERRAL,
					FiniteCodeType:             inAppReferralEnumPb.FiniteCodeType_REGULAR,
				}, nil)
				m.mockUserHelperSvc.EXPECT().GetAccountTierForActorAtTime(gomock.Any(), "actor-1", gomock.Any()).Return(tieringExtPb.Tier_TIER_FI_SALARY, nil, nil)
				m.mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)
				m.mockSalaryPgCl.EXPECT().GetCurrentRegStatusAndNextRegStage(gomock.Any(), gomock.Any()).Return(&beSalaryPb.CurrentRegStatusAndNextRegStageResponse{
					Status:             rpc.StatusOk(),
					RegistrationId:     "",
					RegistrationStatus: beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED,
					NextStage:          0,
				}, nil)
				m.mockOnboardingCl.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Eq(&onbPb.GetFeatureDetailsRequest{ActorId: "actor-1"})).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				// Mock segment client calls that return empty results (no segment-based filtering)
				m.mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberResponse{
					Status:               rpc.StatusOk(),
					SegmentMembershipMap: make(map[string]*segmentPb.SegmentMembership),
				}, nil).AnyTimes()
				m.mockSegmentClient.EXPECT().IsMemberOfExpressions(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status:                         rpc.StatusOk(),
					SegmentExpressionMembershipMap: make(map[string]*segmentPb.SegmentExpressionMembership),
				}, nil).AnyTimes()
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForActorResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{nonTaggedRewardOffer, inappReferralSalaryTierRewardOffer},
			},
		},
		{
			name: "Fetch reward offers for influencer referral user",
			args: args{
				ctx:     context.Background(),
				request: &rewardOffersPb.GetRewardOffersForActorRequest{ActorId: "actor-1"},
			},
			setUpMocks: func(m *mockService) {
				m.mockRewardOfferDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				m.mockUserHelperSvc.EXPECT().GetUserSavingsAccountState(gomock.Any(), "actor-1", typesAccountPb.AccountProductOffering_APO_REGULAR).Return(savingsPb.State_CREATED, nil)
				m.mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is an inapp referral user with golden ticket finite code.
					IsOnboardedThroughReferral: true,
					FiniteCodeChannel:          inAppReferralEnumPb.FiniteCodeChannel_INFLUENCER_REFERRAL,
					FiniteCodeType:             inAppReferralEnumPb.FiniteCodeType_REGULAR,
				}, nil)
				m.mockUserHelperSvc.EXPECT().GetAccountTierForActorAtTime(gomock.Any(), "actor-1", gomock.Any()).Return(tieringExtPb.Tier_TIER_FI_INFINITE, nil, nil)
				m.mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)
				m.mockSalaryPgCl.EXPECT().GetCurrentRegStatusAndNextRegStage(gomock.Any(), gomock.Any()).Return(&beSalaryPb.CurrentRegStatusAndNextRegStageResponse{
					Status:             rpc.StatusOk(),
					RegistrationId:     "",
					RegistrationStatus: beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED,
					NextStage:          0,
				}, nil)
				m.mockOnboardingCl.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Eq(&onbPb.GetFeatureDetailsRequest{ActorId: "actor-1"})).Return(&onbPb.GetFeatureDetailsResponse{
					Status:       rpc.StatusOk(),
					IsFiLiteUser: false,
				}, nil)
				// Mock segment client calls that return empty results (no segment-based filtering)
				m.mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberResponse{
					Status:               rpc.StatusOk(),
					SegmentMembershipMap: make(map[string]*segmentPb.SegmentMembership),
				}, nil).AnyTimes()
				m.mockSegmentClient.EXPECT().IsMemberOfExpressions(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status:                         rpc.StatusOk(),
					SegmentExpressionMembershipMap: make(map[string]*segmentPb.SegmentExpressionMembership),
				}, nil).AnyTimes()
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForActorResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{nonTaggedRewardOffer, influencerReferralRewardOffer},
			},
		},
		{
			name: "Error fetching actor referral info, only non tagged offers should be returned",
			args: args{
				ctx:     context.Background(),
				request: &rewardOffersPb.GetRewardOffersForActorRequest{ActorId: "actor-1"},
			},
			setUpMocks: func(m *mockService) {
				m.mockRewardOfferDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				m.mockUserHelperSvc.EXPECT().GetUserSavingsAccountState(gomock.Any(), "actor-1", typesAccountPb.AccountProductOffering_APO_REGULAR).Return(savingsPb.State_CREATED, nil)
				m.mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(nil, errors.New("error fetching referral info"))
				m.mockUserHelperSvc.EXPECT().GetAccountTierForActorAtTime(gomock.Any(), "actor-1", gomock.Any()).Return(tieringExtPb.Tier_TIER_FI_SALARY, nil, nil)
				m.mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)
				// Mock segment client calls that return empty results (no segment-based filtering)
				m.mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberResponse{
					Status:               rpc.StatusOk(),
					SegmentMembershipMap: make(map[string]*segmentPb.SegmentMembership),
				}, nil).AnyTimes()
				m.mockSegmentClient.EXPECT().IsMemberOfExpressions(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status:                         rpc.StatusOk(),
					SegmentExpressionMembershipMap: make(map[string]*segmentPb.SegmentExpressionMembership),
				}, nil).AnyTimes()
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForActorResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{nonTaggedRewardOffer},
			},
		},
		{
			name: "Unspecified finite code channel in actor referral info, only non tagged offers should be returned",
			args: args{
				ctx:     context.Background(),
				request: &rewardOffersPb.GetRewardOffersForActorRequest{ActorId: "actor-1"},
			},
			setUpMocks: func(m *mockService) {
				m.mockRewardOfferDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				m.mockUserHelperSvc.EXPECT().GetUserSavingsAccountState(gomock.Any(), "actor-1", typesAccountPb.AccountProductOffering_APO_REGULAR).Return(savingsPb.State_CREATED, nil)
				m.mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is an inapp referral user with golden ticket finite code.
					IsOnboardedThroughReferral: true,
					FiniteCodeChannel:          inAppReferralEnumPb.FiniteCodeChannel_FINITE_CODE_CHANNEL_UNSPECIFIED,
					FiniteCodeType:             inAppReferralEnumPb.FiniteCodeType_REGULAR,
				}, nil)
				m.mockUserHelperSvc.EXPECT().GetAccountTierForActorAtTime(gomock.Any(), "actor-1", gomock.Any()).Return(tieringExtPb.Tier_TIER_FI_SALARY, nil, nil)
				m.mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)
				// Mock segment client calls that return empty results (no segment-based filtering)
				m.mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberResponse{
					Status:               rpc.StatusOk(),
					SegmentMembershipMap: make(map[string]*segmentPb.SegmentMembership),
				}, nil).AnyTimes()
				m.mockSegmentClient.EXPECT().IsMemberOfExpressions(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status:                         rpc.StatusOk(),
					SegmentExpressionMembershipMap: make(map[string]*segmentPb.SegmentExpressionMembership),
				}, nil).AnyTimes()
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForActorResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{nonTaggedRewardOffer},
			},
		},
		{
			name: "Unspecified finite code channel in actor referral info, even non tagged offers should not be returned since user is fi lite",
			args: args{
				ctx:     context.Background(),
				request: &rewardOffersPb.GetRewardOffersForActorRequest{ActorId: "actor-1"},
			},
			setUpMocks: func(m *mockService) {
				m.mockRewardOfferDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				m.mockUserHelperSvc.EXPECT().GetUserSavingsAccountState(gomock.Any(), "actor-1", typesAccountPb.AccountProductOffering_APO_REGULAR).Return(savingsPb.State_CREATED, epifierrors.ErrRecordNotFound)
				m.mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is an inapp referral user with golden ticket finite code.
					IsOnboardedThroughReferral: true,
					FiniteCodeChannel:          inAppReferralEnumPb.FiniteCodeChannel_FINITE_CODE_CHANNEL_UNSPECIFIED,
					FiniteCodeType:             inAppReferralEnumPb.FiniteCodeType_REGULAR,
				}, nil)
				m.mockUserHelperSvc.EXPECT().GetAccountTierForActorAtTime(gomock.Any(), "actor-1", gomock.Any()).Return(tieringExtPb.Tier_TIER_FI_SALARY, nil, nil)
				m.mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)
				// Mock segment client calls that return empty results (no segment-based filtering)
				m.mockSegmentClient.EXPECT().IsMember(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberResponse{
					Status:               rpc.StatusOk(),
					SegmentMembershipMap: make(map[string]*segmentPb.SegmentMembership),
				}, nil).AnyTimes()
				m.mockSegmentClient.EXPECT().IsMemberOfExpressions(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status:                         rpc.StatusOk(),
					SegmentExpressionMembershipMap: make(map[string]*segmentPb.SegmentExpressionMembership),
				}, nil).AnyTimes()
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForActorResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{},
			},
		},
	}
	// nolint: scopelint
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// Create all necessary mocks
			mockUserHelperSvc := mocks.NewMockIUserHelperService(ctr)
			mockRewardOffersDao := mock_dao.NewMockRewardOfferDao(ctr)
			mockRewardOfferGroupDao := mock_dao.NewMockRewardOfferGroupDao(ctr)
			mockSalaryprogramClient := besalarypgmock.NewMockSalaryProgramClient(ctr)
			mockSegmentClient := segmentMocks.NewMockSegmentationServiceClient(ctr)
			mockOnboardingClient := mocks3.NewMockOnboardingClient(ctr)

			// Set up common mock expectations that are used across all test cases
			mockUserHelperSvc.EXPECT().IsActorUserPartOfGroup(gomock.Any(), gomock.Any(), commontypes.UserGroup_INTERNAL).Return(true, nil).AnyTimes()
			mockRewardOffersDao.EXPECT().GetOffersRewardUnitsUtilized(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			mockRewardOffersDao.EXPECT().GetActorLevelRewardOfferInventory(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			mockRewardOffersDao.EXPECT().GetActionLevelRewardOfferInventory(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			mockRewardOfferGroupDao.EXPECT().GetOfferGroupsByIds(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			mockRewardOfferGroupDao.EXPECT().GetOfferGroupsRewardUnitsUtilized(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			mockRewardOfferGroupDao.EXPECT().GetActorLevelOfferGroupInventory(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

			// Create the service using the proper constructor
			service := NewRewardOfferService(
				mockRewardOffersDao,
				mockRewardOfferGroupDao,
				mockSalaryprogramClient,
				mockOnboardingClient,
				mockSegmentClient,
				mockUserHelperSvc,
				&config.Flags{EnableRewardsForInternalUsersOnly: false},
				nil, // dyconf
				nil, // slackHelper
				nil, // merchantClient
				nil, // questSdkClient
				nil, // bedrockClient
			)

			// Create mock service struct for the test case
			m := &mockService{
				mockUserHelperSvc:  mockUserHelperSvc,
				mockRewardOfferDao: mockRewardOffersDao,
				mockSalaryPgCl:     mockSalaryprogramClient,
				mockSegmentClient:  mockSegmentClient,
				mockOnboardingCl:   mockOnboardingClient,
			}

			// Setup test-specific mock expectations
			tc.setUpMocks(m)

			// Execute the test
			response, err := service.GetRewardOffersForActor(tc.args.ctx, tc.args.request)
			if (err != nil) != tc.wantErr {
				t.Errorf("GetRewardOffersForActor() error = %v, wantErr %v", err, tc.wantErr)
			}
			if diff := cmp.Diff(tc.wantResponse, response, protocmp.Transform()); diff != "" {
				t.Errorf("GetRewardOffersForActor() got %v, wantReponse = %v, diff: %s", response, tc.wantResponse, diff)
				return
			}
		})
	}
}

func TestRewardOfferService_GetRewardOffersForScreen(t *testing.T) {
	// todo: refactor the unit tests or the RPC. Why? Adding another tag check will lead to bloated tests.

	// reward offer without any tags
	nonTaggedRewardOffer := &rewardOffersPb.RewardOffer{
		Id: "reward-offer-1",
	}
	// reward offer for non referral user.
	nonReferralUserOfferAndSalaryProgramInactive := &rewardOffersPb.RewardOffer{
		Id:   "reward-offer-2",
		Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_NON_REFERRAL, rewardOffersPb.RewardOfferTag_SALARY_PROGRAM_INACTIVE, rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY},
	}
	// first fund addition reward offer for non referral user.
	nonReferralUserRewardOfferFirstAddFundsOfferAndSalaryProgramInactive := &rewardOffersPb.RewardOffer{
		Id:   "reward-offer-3",
		Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_FIRST_FUND_ADDITION, rewardOffersPb.RewardOfferTag_NON_REFERRAL, rewardOffersPb.RewardOfferTag_SALARY_PROGRAM_INACTIVE, rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY},
	}
	// reward offer for user who onboarded through inapp regular finite code.
	inappRegularReferralUserOfferAndSalaryProgramInactive := &rewardOffersPb.RewardOffer{
		Id:   "reward-offer-4",
		Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_IN_APP_REFERRAL, rewardOffersPb.RewardOfferTag_REGULAR_FINITE_CODE, rewardOffersPb.RewardOfferTag_SALARY_PROGRAM_INACTIVE, rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY},
	}
	// first fund addition reward offer for user who onboarded through in app regular finite code referral and is salary program inactive.
	inappRegularReferralUserFirstFundAdditionOfferAndSalaryProgramInactive := &rewardOffersPb.RewardOffer{
		Id: "reward-offer-5",
		Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_FIRST_FUND_ADDITION, rewardOffersPb.RewardOfferTag_IN_APP_REFERRAL, rewardOffersPb.RewardOfferTag_REGULAR_FINITE_CODE, rewardOffersPb.RewardOfferTag_SALARY_PROGRAM_INACTIVE,
			rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY},
	}
	// first fund addition reward offer for user who onboarded through in app regular finite code referral and is salary program active.
	inappRegularReferralUserFirstFundAdditionOfferAndSalaryProgramActive := &rewardOffersPb.RewardOffer{
		Id: "reward-offer-6",
		Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_FIRST_FUND_ADDITION, rewardOffersPb.RewardOfferTag_IN_APP_REFERRAL, rewardOffersPb.RewardOfferTag_REGULAR_FINITE_CODE, rewardOffersPb.RewardOfferTag_SALARY_PROGRAM_ACTIVE,
			rewardOffersPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY},
	}

	rewardOffersList1 := []*rewardOffersPb.RewardOffer{nonTaggedRewardOffer, nonReferralUserOfferAndSalaryProgramInactive, nonReferralUserRewardOfferFirstAddFundsOfferAndSalaryProgramInactive, inappRegularReferralUserOfferAndSalaryProgramInactive,
		inappRegularReferralUserFirstFundAdditionOfferAndSalaryProgramInactive, inappRegularReferralUserFirstFundAdditionOfferAndSalaryProgramActive}

	regWithNotInitiatedStausRes := &beSalaryPb.CurrentRegStatusAndNextRegStageResponse{
		Status:             rpc.StatusOk(),
		RegistrationStatus: beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED,
	}

	type args struct {
		ctx     context.Context
		request *rewardOffersPb.GetRewardOffersForScreenRequest
	}
	tests := []struct {
		name         string
		args         args
		mockFunc     func(mockUserHelperSvc *mocks.MockIUserHelperService, mockSalaryProgramClient *besalarypgmock.MockSalaryProgramClient, mockOnboardingClient *mocks3.MockOnboardingClient, mockRewardOfferGroupDao *mock_dao.MockRewardOfferGroupDao, mockRewardOffersDao *mock_dao.MockRewardOfferDao)
		wantResponse *rewardOffersPb.GetRewardOffersForScreenResponse
		wantErr      bool
	}{

		{
			name: "Get all offers for a non referral user",
			args: args{
				ctx:     context.Background(),
				request: &rewardOffersPb.GetRewardOffersForScreenRequest{ActorId: "actor-1"},
			},
			mockFunc: func(mockUserHelperSvc *mocks.MockIUserHelperService, mockSalaryProgramClient *besalarypgmock.MockSalaryProgramClient, mockOnboardingClient *mocks3.MockOnboardingClient, mockRewardOfferGroupDao *mock_dao.MockRewardOfferGroupDao,
				mockRewardOffersDao *mock_dao.MockRewardOfferDao) {
				mockRewardOffersDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is a non referral user.
					IsOnboardedThroughReferral: false,
				}, nil)
				mockSalaryProgramClient.EXPECT().
					GetCurrentRegStatusAndNextRegStage(gomock.Any(), &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
						ActorId: "actor-1", FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
					}).
					Return(regWithNotInitiatedStausRes, nil)
				mockOnboardingClient.EXPECT().
					GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{ActorId: "actor-1"}).
					Return(&onbPb.GetFeatureDetailsResponse{Status: rpc.StatusOk(), IsFiLiteUser: false}, nil)
				mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)

			},
			wantResponse: &rewardOffersPb.GetRewardOffersForScreenResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{nonReferralUserOfferAndSalaryProgramInactive, nonReferralUserRewardOfferFirstAddFundsOfferAndSalaryProgramInactive},
			},
		},
		{
			name: "Get First Fund addition related offers for non referral onb user",
			args: args{
				ctx: context.Background(),
				request: &rewardOffersPb.GetRewardOffersForScreenRequest{ActorId: "actor-1",
					Filter: &rewardOffersPb.GetRewardOffersForScreenRequest_Filter{Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_FIRST_FUND_ADDITION}},
				},
			},
			mockFunc: func(mockUserHelperSvc *mocks.MockIUserHelperService, mockSalaryProgramClient *besalarypgmock.MockSalaryProgramClient, mockOnboardingClient *mocks3.MockOnboardingClient, mockRewardOfferGroupDao *mock_dao.MockRewardOfferGroupDao,
				mockRewardOffersDao *mock_dao.MockRewardOfferDao) {
				mockRewardOffersDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is a non referral user.
					IsOnboardedThroughReferral: false,
				}, nil)
				mockSalaryProgramClient.EXPECT().
					GetCurrentRegStatusAndNextRegStage(gomock.Any(), &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
						ActorId: "actor-1", FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
					}).
					Return(regWithNotInitiatedStausRes, nil)
				mockOnboardingClient.EXPECT().
					GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{ActorId: "actor-1"}).
					Return(&onbPb.GetFeatureDetailsResponse{Status: rpc.StatusOk(), IsFiLiteUser: false}, nil)
				mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForScreenResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{nonReferralUserRewardOfferFirstAddFundsOfferAndSalaryProgramInactive},
			},
		},
		{
			name: "Get all offers for inapp regular referral onb user",
			args: args{
				ctx:     context.Background(),
				request: &rewardOffersPb.GetRewardOffersForScreenRequest{ActorId: "actor-1"},
			},
			mockFunc: func(mockUserHelperSvc *mocks.MockIUserHelperService, mockSalaryProgramClient *besalarypgmock.MockSalaryProgramClient, mockOnboardingClient *mocks3.MockOnboardingClient, mockRewardOfferGroupDao *mock_dao.MockRewardOfferGroupDao,
				mockRewardOffersDao *mock_dao.MockRewardOfferDao) {
				mockRewardOffersDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is an inapp referral user with golden ticket finite code.
					IsOnboardedThroughReferral: true,
					FiniteCodeChannel:          inAppReferralEnumPb.FiniteCodeChannel_IN_APP_REFERRAL,
					FiniteCodeType:             inAppReferralEnumPb.FiniteCodeType_REGULAR,
				}, nil)
				mockSalaryProgramClient.EXPECT().
					GetCurrentRegStatusAndNextRegStage(gomock.Any(), &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
						ActorId: "actor-1", FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
					}).
					Return(regWithNotInitiatedStausRes, nil)
				mockOnboardingClient.EXPECT().
					GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{ActorId: "actor-1"}).
					Return(&onbPb.GetFeatureDetailsResponse{Status: rpc.StatusOk(), IsFiLiteUser: false}, nil)
				mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForScreenResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{inappRegularReferralUserOfferAndSalaryProgramInactive, inappRegularReferralUserFirstFundAdditionOfferAndSalaryProgramInactive},
			},
		},
		{
			name: "Get First Fund addition related offers for inapp regular referral onb, salary program inactive user",
			args: args{
				ctx: context.Background(),
				request: &rewardOffersPb.GetRewardOffersForScreenRequest{ActorId: "actor-1",
					Filter: &rewardOffersPb.GetRewardOffersForScreenRequest_Filter{Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_FIRST_FUND_ADDITION}},
				},
			},
			mockFunc: func(mockUserHelperSvc *mocks.MockIUserHelperService, mockSalaryProgramClient *besalarypgmock.MockSalaryProgramClient, mockOnboardingClient *mocks3.MockOnboardingClient, mockRewardOfferGroupDao *mock_dao.MockRewardOfferGroupDao,
				mockRewardOffersDao *mock_dao.MockRewardOfferDao) {
				mockRewardOffersDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is an inapp referral user with golden ticket finite code.
					IsOnboardedThroughReferral: true,
					FiniteCodeChannel:          inAppReferralEnumPb.FiniteCodeChannel_IN_APP_REFERRAL,
					FiniteCodeType:             inAppReferralEnumPb.FiniteCodeType_REGULAR,
				}, nil)
				mockSalaryProgramClient.EXPECT().
					GetCurrentRegStatusAndNextRegStage(gomock.Any(), &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
						ActorId: "actor-1", FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
					}).
					Return(regWithNotInitiatedStausRes, nil)
				mockOnboardingClient.EXPECT().
					GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{ActorId: "actor-1"}).
					Return(&onbPb.GetFeatureDetailsResponse{Status: rpc.StatusOk(), IsFiLiteUser: false}, nil)
				mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForScreenResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{inappRegularReferralUserFirstFundAdditionOfferAndSalaryProgramInactive},
			},
		},
		{
			name: "Get First Fund addition related offers for inapp regular referral onb ,salary program active user",
			args: args{
				ctx: context.Background(),
				request: &rewardOffersPb.GetRewardOffersForScreenRequest{ActorId: "actor-1",
					Filter: &rewardOffersPb.GetRewardOffersForScreenRequest_Filter{Tags: []rewardOffersPb.RewardOfferTag{rewardOffersPb.RewardOfferTag_FIRST_FUND_ADDITION}},
				},
			},
			mockFunc: func(mockUserHelperSvc *mocks.MockIUserHelperService, mockSalaryProgramClient *besalarypgmock.MockSalaryProgramClient, mockOnboardingClient *mocks3.MockOnboardingClient, mockRewardOfferGroupDao *mock_dao.MockRewardOfferGroupDao,
				mockRewardOffersDao *mock_dao.MockRewardOfferDao) {
				mockRewardOffersDao.EXPECT().FetchRewardOffers(gomock.Any(), gomock.Any()).Return(rewardOffersList1, nil)
				mockUserHelperSvc.EXPECT().GetActorReferralInfo(gomock.Any(), "actor-1").Return(&helper.ActorReferralInfo{
					// actor is an inapp referral user with golden ticket finite code.
					IsOnboardedThroughReferral: true,
					FiniteCodeChannel:          inAppReferralEnumPb.FiniteCodeChannel_IN_APP_REFERRAL,
					FiniteCodeType:             inAppReferralEnumPb.FiniteCodeType_REGULAR,
				}, nil)
				mockSalaryProgramClient.EXPECT().
					GetCurrentRegStatusAndNextRegStage(gomock.Any(), &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
						ActorId: "actor-1", FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
					}).
					Return(&beSalaryPb.CurrentRegStatusAndNextRegStageResponse{
						Status:             rpc.StatusOk(),
						RegistrationId:     "reg-id-1",
						RegistrationStatus: beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED,
					}, nil)
				mockSalaryProgramClient.EXPECT().GetLatestActivationDetailsActiveAtTime(gomock.Any(), gomock.Any()).Return(&beSalaryPb.LatestActivationDetailsActiveAtTimeResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mockOnboardingClient.EXPECT().
					GetFeatureDetails(gomock.Any(), &onbPb.GetFeatureDetailsRequest{ActorId: "actor-1"}).
					Return(&onbPb.GetFeatureDetailsResponse{Status: rpc.StatusOk(), IsFiLiteUser: false}, nil)
				mockUserHelperSvc.EXPECT().GetEventTimeForActor(gomock.Any(), "actor-1", "ONB_COMPLETED").Return(aws.Time(time20MinsBefore), nil)
			},
			wantResponse: &rewardOffersPb.GetRewardOffersForScreenResponse{
				Status:       rpc.StatusOk(),
				RewardOffers: []*rewardOffersPb.RewardOffer{inappRegularReferralUserFirstFundAdditionOfferAndSalaryProgramActive},
			},
		},
	}
	// nolint: scopelint
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// mock user helper service
			mockUserHelperSvc := mocks.NewMockIUserHelperService(ctr)

			mockUserHelperSvc.EXPECT().GetAccountTierForActorAtTime(gomock.Any(), gomock.Any(), gomock.Any()).Return(tieringExtPb.Tier_TIER_FI_SALARY, nil, nil).AnyTimes()

			// mock salary program client
			mockSalaryProgramClient := besalarypgmock.NewMockSalaryProgramClient(ctr)
			// mock onboarding client
			mockOnboardingClient := mocks3.NewMockOnboardingClient(ctr)
			// mock rewardOffers dao
			mockRewardOffersDao := mock_dao.NewMockRewardOfferDao(ctr)
			// mock reward offer group dao
			mockRewardOfferGroupDao := mock_dao.NewMockRewardOfferGroupDao(ctr)
			// setup mock calls.
			tc.mockFunc(mockUserHelperSvc, mockSalaryProgramClient, mockOnboardingClient, mockRewardOfferGroupDao, mockRewardOffersDao)

			rewardOfferService := NewRewardOfferService(mockRewardOffersDao, mockRewardOfferGroupDao, mockSalaryProgramClient, mockOnboardingClient, nil, mockUserHelperSvc, &config.Flags{EnableRewardsForInternalUsersOnly: false}, nil, nil, nil, nil, nil)

			response, err := rewardOfferService.GetRewardOffersForScreen(tc.args.ctx, tc.args.request)
			if (err != nil) != tc.wantErr {
				t.Errorf("GetRewardOffersForScreen() error = %v, wantErr %v", err, tc.wantErr)
			}
			if !proto.Equal(tc.wantResponse, response) {
				t.Errorf("GetRewardOffersForScreen() got %v, wantReponse = %v", response, tc.wantResponse)
				return
			}
		})
	}
}
