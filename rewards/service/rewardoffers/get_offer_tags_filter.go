package rewardoffers

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
)

// todo (utkarsh) : should we abstract the tag filter logic adding with processors for multiple impls like ONb route etc ?

var (
	accountTierToRewardOfferTagMap = map[tieringExtPb.Tier]rewardOfferPb.RewardOfferTag{
		tieringExtPb.Tier_TIER_FI_REGULAR:          rewardOfferPb.RewardOfferTag_ACCOUNT_TIER_FI_REGULAR,
		tieringExtPb.Tier_TIER_FI_BASIC:            rewardOfferPb.RewardOfferTag_ACCOUNT_TIER_FI_BASIC,
		tieringExtPb.Tier_TIER_FI_PLUS:             rewardOfferPb.RewardOfferTag_ACCOUNT_TIER_FI_PLUS,
		tieringExtPb.Tier_TIER_FI_INFINITE:         rewardOfferPb.RewardOfferTag_ACCOUNT_TIER_FI_INFINITE,
		tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: rewardOfferPb.RewardOfferTag_ACCOUNT_TIER_FI_AA_SALARY_BAND_1,
		tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: rewardOfferPb.RewardOfferTag_ACCOUNT_TIER_FI_AA_SALARY_BAND_2,
		tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: rewardOfferPb.RewardOfferTag_ACCOUNT_TIER_FI_AA_SALARY_BAND_3,
		tieringExtPb.Tier_TIER_FI_SALARY_LITE:      rewardOfferPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY_LITE,
		tieringExtPb.Tier_TIER_FI_SALARY_BASIC:     rewardOfferPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY_BASIC,
		tieringExtPb.Tier_TIER_FI_SALARY:           rewardOfferPb.RewardOfferTag_ACCOUNT_TIER_FI_SALARY,
	}
)

// getOfferTagsFilterForUser returns a list of tags used for filtering rewardOffers for displaying to the user.
func (rgs *RewardOfferService) getOfferTagsFilterForUser(ctx context.Context, actorId string) ([]rewardOfferPb.RewardOfferTag, error) {
	var (
		onbRouteTags      []rewardOfferPb.RewardOfferTag
		salaryProgramTags []rewardOfferPb.RewardOfferTag
		accountTierTags   []rewardOfferPb.RewardOfferTag
		accountTypeTags   []rewardOfferPb.RewardOfferTag
		relevantTags      []rewardOfferPb.RewardOfferTag
	)

	eg, egCtx := errgroup.WithContext(ctx)

	eg.Go(func() error {
		tags, err := rgs.getOfferTagsFilterForUserBasedOnOnbRoute(egCtx, actorId)
		if err != nil {
			logger.Error(egCtx, "error fetching offer tags based on user onb route", zap.Error(err))
			return err
		}
		onbRouteTags = append(onbRouteTags, tags...)
		return nil
	})

	eg.Go(func() error {

		tags, err := rgs.getTagsFilterForUserBasedOnSalaryProgramStatus(egCtx, actorId)
		if err != nil {
			logger.Error(egCtx, "error fetching offer tags for salary program", zap.Error(err))
			return err
		}
		salaryProgramTags = append(salaryProgramTags, tags...)
		return nil
	})

	eg.Go(func() error {
		tags, err := rgs.getTagsFilterForUserBasedOnAccountTier(egCtx, actorId)
		if err != nil {
			logger.Error(egCtx, "error fetching offer tags for account tier", zap.Error(err))
			return err
		}
		accountTierTags = append(accountTierTags, tags...)
		return nil
	})

	eg.Go(func() error {
		tags, err := rgs.getTagsFilterForUserBasedOnAccountTypeDetails(egCtx, actorId)
		if err != nil {
			logger.Error(egCtx, "error fetching offer tags for account type", zap.Error(err))
			return err
		}
		accountTypeTags = append(accountTypeTags, tags...)
		return nil
	})

	if err := eg.Wait(); err != nil {
		logger.Error(ctx, "error fetching offer tags filter for user", zap.Error(err))
		return nil, fmt.Errorf("error fetching offer tags filter for user, err: %w", err)
	}

	relevantTags = append(relevantTags, onbRouteTags...)
	relevantTags = append(relevantTags, salaryProgramTags...)
	relevantTags = append(relevantTags, accountTierTags...)
	relevantTags = append(relevantTags, accountTypeTags...)

	return relevantTags, nil
}

// getOfferTagsFilterForUserBasedOnOnbRoute returns a list of offer tags relevant for the user based on his onboarding route (referral or non-referral)
func (rgs *RewardOfferService) getOfferTagsFilterForUserBasedOnOnbRoute(ctx context.Context, actorId string) ([]rewardOfferPb.RewardOfferTag, error) {
	var relevantTags []rewardOfferPb.RewardOfferTag

	// get referral info of user and map it to offer tags.
	actorReferralInfo, err := rgs.userHelperService.GetActorReferralInfo(ctx, actorId)
	if err != nil {
		return nil, errors.Wrap(err, "couldn't create tag list for offer filtering, error fetching actor referral info")
	}
	if !actorReferralInfo.IsOnboardedThroughReferral {
		relevantTags = append(relevantTags, rewardOfferPb.RewardOfferTag_NON_REFERRAL)
		return relevantTags, nil
	}

	finiteCodeChannelToRewardOfferTagMap := map[inAppReferralEnumPb.FiniteCodeChannel]rewardOfferPb.RewardOfferTag{
		inAppReferralEnumPb.FiniteCodeChannel_IN_APP_REFERRAL:     rewardOfferPb.RewardOfferTag_IN_APP_REFERRAL,
		inAppReferralEnumPb.FiniteCodeChannel_INFLUENCER_REFERRAL: rewardOfferPb.RewardOfferTag_INFLUENCER_REFERRAL,
		inAppReferralEnumPb.FiniteCodeChannel_CUSTOMER_SERVICE:    rewardOfferPb.RewardOfferTag_CUSTOMER_SUPPORT_REFERRAL,
		inAppReferralEnumPb.FiniteCodeChannel_GPAY:                rewardOfferPb.RewardOfferTag_GPAY_REFERRAL,
		inAppReferralEnumPb.FiniteCodeChannel_PHONEPE:             rewardOfferPb.RewardOfferTag_PHONEPE_REFERRAL,
		inAppReferralEnumPb.FiniteCodeChannel_VANTAGE_CIRCLE:      rewardOfferPb.RewardOfferTag_VANTAGE_CIRCLE_REFERRAL,
		inAppReferralEnumPb.FiniteCodeChannel_ACQUISITION:         rewardOfferPb.RewardOfferTag_ACQUISITION_REFERRAL,
		inAppReferralEnumPb.FiniteCodeChannel_TIMESPRIME:          rewardOfferPb.RewardOfferTag_TIMESPRIME_REFERRAL,
		inAppReferralEnumPb.FiniteCodeChannel_TIMESPOINT:          rewardOfferPb.RewardOfferTag_TIMESPOINT_REFERRAL,
	}
	offerTag, ok := finiteCodeChannelToRewardOfferTagMap[actorReferralInfo.FiniteCodeChannel]
	if !ok {
		logger.Error(ctx, "unhandled finite code channel for reward offer filtering", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("finite_code_channel", actorReferralInfo.FiniteCodeChannel.String()))
		return nil, fmt.Errorf("unhandled finite code channel for reward offer filtering")

	}
	relevantTags = append(relevantTags, offerTag)

	return relevantTags, nil
}

func (rgs *RewardOfferService) getTagsFilterForUserBasedOnSalaryProgramStatus(ctx context.Context, actorId string) ([]rewardOfferPb.RewardOfferTag, error) {
	registrationStatusRes, err := rgs.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if err != nil || !registrationStatusRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching salary program registration status for the actor", zap.Error(err),
			zap.Any(logger.RPC_STATUS, registrationStatusRes.GetStatus()),
		)
		return nil, fmt.Errorf("error fetching salary program registration status for the actor")
	}

	if registrationStatusRes.GetRegistrationStatus() == beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED {
		return []rewardOfferPb.RewardOfferTag{rewardOfferPb.RewardOfferTag_SALARY_PROGRAM_INACTIVE}, nil
	}

	latestActivationDetails, err := rgs.salaryProgramClient.GetLatestActivationDetailsActiveAtTime(ctx, &beSalaryPb.LatestActivationDetailsActiveAtTimeRequest{
		ActiveAtTime:   timestampPb.New(time.Now()),
		RegistrationId: registrationStatusRes.GetRegistrationId(),
		// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user.
		ActivationKind: beSalaryPb.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
	})
	if err != nil || (!latestActivationDetails.GetStatus().IsSuccess() && !latestActivationDetails.GetStatus().IsRecordNotFound()) {
		logger.Error(ctx, "error fetching latest activation details of salary program for the actor", zap.Error(err),
			zap.String(logger.REGISTRATION_ID, registrationStatusRes.GetRegistrationId()),
			zap.Any(logger.RPC_STATUS, latestActivationDetails.GetStatus()),
		)
		return nil, fmt.Errorf("error fetching latest activation details of salary program for the actor")
	}

	if latestActivationDetails.GetStatus().IsRecordNotFound() {
		return []rewardOfferPb.RewardOfferTag{rewardOfferPb.RewardOfferTag_SALARY_PROGRAM_INACTIVE}, nil
	}

	return []rewardOfferPb.RewardOfferTag{rewardOfferPb.RewardOfferTag_SALARY_PROGRAM_ACTIVE}, nil
}

// getTagsFilterForUserBasedOnAccountTier returns a list of offer tags relevant for the user as per his/her savings account tier at current time
func (rgs *RewardOfferService) getTagsFilterForUserBasedOnAccountTier(ctx context.Context, actorId string) ([]rewardOfferPb.RewardOfferTag, error) {
	currentTime := time.Now()
	accountTier, _, err := rgs.userHelperService.GetAccountTierForActorAtTime(ctx, actorId, &currentTime)
	if err != nil {
		return nil, fmt.Errorf("error fetching account tier for actor, err: %w", err)
	}

	tierRewardOfferTag, ok := accountTierToRewardOfferTagMap[accountTier]
	if !ok {
		logger.Error(ctx, "unhandled account tier for reward offer tag mapping", zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.TIER, accountTier.String()))
		return nil, fmt.Errorf("unhandled account tier for reward offer tag mapping")
	}

	return []rewardOfferPb.RewardOfferTag{tierRewardOfferTag}, nil
}

// getTagsFilterForUserBasedOnAccountTypeDetails returns a list of offer tags relevant for the user as per his/her currently activated account type
func (rgs *RewardOfferService) getTagsFilterForUserBasedOnAccountTypeDetails(ctx context.Context, actorId string) ([]rewardOfferPb.RewardOfferTag, error) {
	featureDetailResp, err := rgs.onboardingClient.GetFeatureDetails(ctx, &onboardingPb.GetFeatureDetailsRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(featureDetailResp, err); rpcErr != nil {
		return nil, fmt.Errorf("error in calling GetFeatureDetails RPC, err: %w", rpcErr)
	}

	if featureDetailResp.GetIsFiLiteUser() {
		return []rewardOfferPb.RewardOfferTag{rewardOfferPb.RewardOfferTag_ACCOUNT_TYPE_FI_LITE}, nil
	}
	return []rewardOfferPb.RewardOfferTag{}, nil
}
