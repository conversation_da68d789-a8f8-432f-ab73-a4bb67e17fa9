package triggerhandler

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/accrual"

	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsNotificationPb "github.com/epifi/gamma/api/rewards/notification"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"

	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/generator/dao"
	"github.com/epifi/gamma/rewards/notification/helper"
	"github.com/epifi/gamma/rewards/notification/internalerrors"
)

type RewardProcessingStatusUpdateHandler struct {
	notificationParamsMap *config.RewardsNotificationParams
	rewardsDao            dao.RewardsDao
	helper                *helper.HelperService
}

func NewRewardProcessingStatusUpdateHandler(
	notificationParamsMap *config.RewardsNotificationParams,
	rewardsDao dao.RewardsDao,
	helper *helper.HelperService,
) *RewardProcessingStatusUpdateHandler {
	return &RewardProcessingStatusUpdateHandler{
		notificationParamsMap: notificationParamsMap,
		rewardsDao:            rewardsDao,
		helper:                helper,
	}
}

var _ ITriggerHandler = &RewardProcessingStatusUpdateHandler{}

func (r *RewardProcessingStatusUpdateHandler) IsTriggerValid(ctx context.Context, actorId string, triggerMetadata *rewardsNotificationPb.TriggerMetadata, notificationTypeMetadata *rewardsNotificationPb.NotificationTypeMetadata) (bool, error) {
	rewardProcessingStatusUpdateTriggerMetadata := triggerMetadata.GetRewardProcessingStatusUpdateTriggerMetadata()
	if rewardProcessingStatusUpdateTriggerMetadata == nil {
		return false, fmt.Errorf("rewardProcessingStatusUpdateTriggerMetadata is nil")
	}

	rewardModel, err := r.rewardsDao.FetchRewardById(ctx, rewardProcessingStatusUpdateTriggerMetadata.GetRewardId())
	if err != nil {
		return false, fmt.Errorf("error in FetchByActorAndOfferId call inside GetNotificationMessage for actorId %s and rewardId %s: %w", actorId, rewardProcessingStatusUpdateTriggerMetadata.GetRewardId(), err)
	}
	rewardProto, err := rewardModel.GetProtoReward()
	if err != nil {
		return false, fmt.Errorf("error in GetProtoReward() call inside GetNotificationMessage for actorId %s and rewardId %s: %w", actorId, rewardProcessingStatusUpdateTriggerMetadata.GetRewardId(), err)
	}

	// if the current status of the reward is not the same as that when it's notification payload was created, then don't send the notification
	if rewardProto.GetStatus() != rewardProcessingStatusUpdateTriggerMetadata.GetRewardStatus() {
		return false, nil
	}

	return true, nil
}

// nolint: funlen
func (r *RewardProcessingStatusUpdateHandler) GetNotificationMessage(ctx context.Context, actorId string, triggerMetadata *rewardsNotificationPb.TriggerMetadata, rewardNotificationType rewardOffersPb.RewardNotificationType, content *rewardsNotificationPb.Content, notificationTypeMetadata *rewardsNotificationPb.NotificationTypeMetadata) (*NotificationTypeMessage, error) {
	var (
		rewardProcessingNotificationParamsMap = r.notificationParamsMap.RewardProcessingNotificationParams
		rewardTypeSpecificNotificationParams  *config.NotificationTemplateParams
	)

	rewardProcessingStatusUpdateTriggerMetadata := triggerMetadata.GetRewardProcessingStatusUpdateTriggerMetadata()
	if rewardProcessingStatusUpdateTriggerMetadata == nil {
		return nil, fmt.Errorf("rewardProcessingStatusUpdateTriggerMetadata is nil")
	}

	rewardModel, err := r.rewardsDao.FetchRewardById(ctx, rewardProcessingStatusUpdateTriggerMetadata.GetRewardId())
	if err != nil {
		return nil, fmt.Errorf("error in FetchByActorAndOfferId call inside GetNotificationMessage for actorId %s and rewardId %s: %w", actorId, rewardProcessingStatusUpdateTriggerMetadata.GetRewardId(), err)
	}
	rewardProto, err := rewardModel.GetProtoReward()
	if err != nil {
		return nil, fmt.Errorf("error in GetProtoReward() call inside GetNotificationMessage for actorId %s and rewardId %s: %w", actorId, rewardProcessingStatusUpdateTriggerMetadata.GetRewardId(), err)
	}

	if rewardProto.GetStatus() != rewardsPb.RewardStatus_PROCESSED {
		logger.Info(ctx, "not sending notification for any processing status update other than success", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.String("processingStatus", rewardProto.GetRewardOptions().GetActionDetails()))
		return nil, nil
	}

	rewardTypeToNotificationParamsMap, isPresent := rewardProcessingNotificationParamsMap[rewardProcessingStatusUpdateTriggerMetadata.GetRewardStatus().String()]
	if !isPresent {
		logger.Info(ctx, "rewardTypeToNotificationParamsMap not present for reward status, not sending notification ", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.String(logger.REWARD_STATUS, rewardProcessingStatusUpdateTriggerMetadata.GetRewardStatus().String()))
		return nil, fmt.Errorf("rewardTypeToNotificationParamsMap not present for reward status, err : %w", internalerrors.ErrMandatoryParamsMissing)
	} else {
		rewardTypeSpecificNotificationParams, isPresent = rewardTypeToNotificationParamsMap[rewardProto.GetChosenReward().GetRewardType().String()]
		if !isPresent {
			logger.Error(ctx, "notification params not present for reward type in reward status, not sending notification", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.String(logger.REWARD_TYPE, rewardProto.GetChosenReward().GetRewardType().String()), zap.String(logger.REWARD_STATUS, rewardProcessingStatusUpdateTriggerMetadata.GetRewardStatus().String()))
			return nil, fmt.Errorf("notification params not present for reward type for reward status, err : %w", internalerrors.ErrMandatoryParamsMissing)
		}
	}

	var (
		notificationTitle       = content.GetTitle()
		notificationBody        = content.GetBody()
		notificationDeeplink    = content.GetDeeplink()
		imageUrl                = content.GetImageUrl()
		notificationParamsTitle string
		notificationParamsBody  string
		rewardUnits             uint32
	)

	rewardUnitsAsFloat, err := rewardsPb.GetRewardUnitsFromRewardOption(rewardProto.GetChosenReward())
	if err != nil {
		return nil, fmt.Errorf("error while getting reward units: %w", err)
	}
	rewardUnits = uint32(rewardUnitsAsFloat)

	if rewardTypeSpecificNotificationParams != nil {
		notificationParamsTitle = rewardTypeSpecificNotificationParams.Title
		notificationParamsBody = rewardTypeSpecificNotificationParams.Body
		if imageUrl == "" {
			imageUrl = rewardTypeSpecificNotificationParams.ImageUrl
		}
	}

	switch rewardProto.GetChosenReward().GetRewardType() {
	case rewardsPb.RewardType_FI_COINS:
		if notificationTitle == "" {
			notificationTitle = accrual.ReplaceCoinWithPointIfApplicable(fmt.Sprintf(notificationParamsTitle, rewardUnits))
		}
		if notificationBody == "" {
			if rewardProto.GetStatus() == rewardsPb.RewardStatus_PROCESSED {
				notificationBody = fmt.Sprintf(notificationParamsBody, rewardProto.GetRewardOptions().GetActionDetails())
			} else {
				logger.Info(ctx, "not sending notification for any processing status update other than success", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.String("processingStatus", rewardProto.GetRewardOptions().GetActionDetails()))
				return nil, nil
			}
		}
		if notificationDeeplink == nil {
			notificationDeeplink = &deeplink.Deeplink{
				Screen: deeplink.Screen_MY_REWARDS_SCREEN,
			}
		}
	case rewardsPb.RewardType_EGV_BASKET, rewardsPb.RewardType_THRIWE_BENEFITS_PACKAGE:
		if notificationTitle == "" {
			notificationTitle = notificationParamsTitle
		}
		if notificationBody == "" {
			if rewardProto.GetStatus() == rewardsPb.RewardStatus_PROCESSED {
				notificationBody = fmt.Sprintf(notificationParamsBody, rewardProto.GetRewardOptions().GetActionDetails())
			} else {
				logger.Info(ctx, "not sending notification for any processing status update other than success", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.String("processingStatus", rewardProto.GetRewardOptions().GetActionDetails()))
				return nil, nil
			}
		}
		if notificationDeeplink == nil {
			notificationDeeplink = &deeplink.Deeplink{
				Screen: deeplink.Screen_REDEEMED_OFFERS_SCREEN,
			}
		}
	case rewardsPb.RewardType_CASH:
		if notificationTitle == "" {
			notificationTitle = fmt.Sprintf(notificationParamsTitle, rewardUnits)
		}
		if notificationBody == "" {
			if rewardProto.GetStatus() == rewardsPb.RewardStatus_PROCESSED {
				notificationBody = fmt.Sprintf(notificationParamsBody, rewardProto.GetRewardOptions().GetActionDetails())
			} else {
				logger.Info(ctx, "not sending notification for any processing status update other than success", zap.String(logger.REWARD_ID, rewardProto.GetId()), zap.String("processingStatus", rewardProto.GetRewardOptions().GetActionDetails()))
				return nil, nil
			}
		}
		if notificationDeeplink == nil {
			notificationDeeplink = &deeplink.Deeplink{
				Screen: deeplink.Screen_MY_REWARDS_SCREEN,
			}
		}
	default:
		logger.Error(ctx, "unsupported reward type encountered while trying to send reward processed notification", zap.String(logger.REWARD_TYPE, rewardProto.GetChosenReward().GetRewardType().String()))
		return nil, fmt.Errorf("unsupported reward type encountered while trying to send reward processed notification")
	}

	var message NotificationTypeMessage
	// setting message.NotificationMessage
	switch rewardNotificationType {
	case rewardOffersPb.RewardNotificationType_SYSTEM_TRAY:
		commonTemplateFields := &fcm.CommonTemplateFields{
			Title:    notificationTitle,
			Body:     notificationBody,
			Deeplink: notificationDeeplink,
		}
		if imageUrl != "" {
			commonTemplateFields.IconAttributes = &fcm.IconAttributes{IconUrl: imageUrl}
		}

		message.NotificationMessage = &fcm.Notification{
			NotificationType: fcm.NotificationType_SYSTEM_TRAY,
			NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
				SystemTrayTemplate: &fcm.SystemTrayTemplate{
					CommonTemplateFields: commonTemplateFields,
				},
			},
		}
	case rewardOffersPb.RewardNotificationType_IN_APP:
		commonTemplateFields := &fcm.CommonTemplateFields{
			Title:    notificationTitle,
			Body:     notificationBody,
			Deeplink: notificationDeeplink,
		}
		if imageUrl != "" {
			commonTemplateFields.IconAttributes = &fcm.IconAttributes{IconUrl: imageUrl}
		}

		message.NotificationMessage = &fcm.Notification{
			NotificationType: fcm.NotificationType_IN_APP,
			NotificationTemplates: &fcm.Notification_InAppTemplate{
				InAppTemplate: &fcm.InAppTemplate{
					CommonTemplateFields: commonTemplateFields,
					AfterClickAction:     fcm.AfterClickAction_DISMISS,
				},
			},
		}
	case rewardOffersPb.RewardNotificationType_IN_APP_CRITICAL:
		commonTemplateFields := &fcm.CommonTemplateFields{
			Title:    notificationTitle,
			Body:     notificationBody,
			Deeplink: notificationDeeplink,
		}
		if imageUrl != "" {
			commonTemplateFields.IconAttributes = &fcm.IconAttributes{IconUrl: imageUrl}
		}

		message.NotificationMessage = &fcm.Notification{
			NotificationType: fcm.NotificationType_IN_APP,
			NotificationTemplates: &fcm.Notification_InAppTemplate{
				InAppTemplate: &fcm.InAppTemplate{
					CommonTemplateFields: commonTemplateFields,
					AfterClickAction:     fcm.AfterClickAction_DISMISS,
					NotificationPriority: fcm.InAppNotificationPriority_NOTIFICATION_PRIORITY_CRITICAL,
				},
			},
		}
	case rewardOffersPb.RewardNotificationType_EMAIL:
		emailMessage, err := r.helper.GetEmailMessage(ctx, notificationTypeMetadata.GetEmail(), rewardProto)
		if err != nil {
			return nil, fmt.Errorf("error while getting email message: %w", err)
		}
		message.EmailMessage = emailMessage
	default:
		return nil, fmt.Errorf("unsupported rewardNotificationType : %s while getting notification message, err : %w", rewardNotificationType, internalerrors.ErrPermanentFailure)
	}

	switch rewardProto.GetStatus() {
	case rewardsPb.RewardStatus_PROCESSED:
		message.CampaignName = commsPb.CampaignName_CAMPAIGN_NAME_REWARD_PROCESSED
	}

	return &message, nil
}
