package helper

import (
	"context"
	"fmt"
	"strconv"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	commsPb "github.com/epifi/gamma/api/comms"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsNotificationPb "github.com/epifi/gamma/api/rewards/notification"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	usersPb "github.com/epifi/gamma/api/user"

	"github.com/epifi/gamma/rewards/notification/internalerrors"
)

func (h *HelperService) GetEmailMessage(ctx context.Context, emailNotificationTypeMetadata *rewardsNotificationPb.EmailNotificationTypeMetadata, reward *rewardsPb.Reward) (*commsPb.EmailMessage, error) {
	var (
		emailTemplate  = emailNotificationTypeMetadata.GetEmailTemplate()
		pdfAttachments []*commsPb.EmailMessage_Attachment
	)
	// return if email notifications are disabled
	if h.dyconf.Flags().DisableEmailBasedNotifications() {
		return nil, fmt.Errorf("email notifications are disabled, err : %w", internalerrors.ErrPermanentFailure)
	}

	// fetch user details
	userResp, err := h.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_ActorId{
			ActorId: reward.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		if userResp.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("user details not found: %w", epifierrors.ErrRecordNotFound)
		}
		logger.Error(ctx, "failed to get user details", zap.Error(rpcErr))
		return nil, fmt.Errorf("failed to get user details: %w", rpcErr)
	}

	// create email message
	emailMessage := &commsPb.EmailMessage{
		FromEmailId:   "<EMAIL>",
		FromEmailName: "Fi Money",
		ToEmailId:     userResp.GetUser().GetProfile().GetEmail(),
		ToEmailName:   userResp.GetUser().GetProfile().GetKycName().ToString(),
	}

	switch emailTemplate {
	// currently this only contains tiering cashback summary
	case commsPb.EmailType_TIERING_REWARDS_SUMMARY_EMAIL:
		var tieringRewardsSummaryOption *commsPb.TieringRewardsSummaryEmailOption_TieringCashbackSummary

		tieringRewardsSummaryOption, pdfAttachments, err = h.getTieringRewardsSummaryEmailOption(ctx, reward, userResp, emailTemplate)
		if err != nil {
			logger.Error(ctx, "failed to get tiering rewards summary email option", zap.Error(err))
			return nil, fmt.Errorf("failed to get tiering rewards summary email option: %w", err)
		}

		// set email option
		emailMessage.EmailOption = &commsPb.EmailOption{
			Option: &commsPb.EmailOption_TieringRewardsSummaryEmailOption{
				TieringRewardsSummaryEmailOption: &commsPb.TieringRewardsSummaryEmailOption{
					EmailType: commsPb.EmailType_TIERING_REWARDS_SUMMARY_EMAIL,
					Option: &commsPb.TieringRewardsSummaryEmailOption_TieringRewardsSummaryOptionV1{
						TieringRewardsSummaryOptionV1: tieringRewardsSummaryOption,
					},
				},
			},
		}

	default:
		return nil, fmt.Errorf("invalid email template: %v", emailTemplate)
	}

	// add pdf attachment if any
	if len(pdfAttachments) > 0 {
		emailMessage.Attachment = pdfAttachments
	}

	return emailMessage, nil
}

func (h *HelperService) getTieringRewardsSummaryEmailOption(ctx context.Context, reward *rewardsPb.Reward, userResp *usersPb.GetUserResponse, emailTemplate commsPb.EmailType) (*commsPb.TieringRewardsSummaryEmailOption_TieringCashbackSummary, []*commsPb.EmailMessage_Attachment, error) {
	var (
		rewardCashAmount            float32
		rewardFiCoinsAmount         float32
		rewardCashAmountCap         float32
		rewardFiCoinsAmountCap      float32
		prioritizedRewardAmount     float32
		prioritizedRewardCap        float32
		prioritizedRewardType       rewardsPb.RewardType
		moneyPlantText              string
		cashDisplayType             string
		fiCoinsDisplayType          string
		cashRewardOption            *rewardsPb.RewardOption
		fiCoinsRewardOption         *rewardsPb.RewardOption
		tierText                    string
		valueBackPercentageText     string
		rewardOffer                 *rewardOffersPb.RewardOffer
		fromStatementDateInIst      = datetime.PreviousMonth(reward.GetCreatedAt().AsTime().In(datetime.IST))
		toStatementDateInIst        = datetime.EndOfMonth(fromStatementDateInIst)
		descriptions                []*commsPb.TieringRewardsSummaryEmailOption_IconText
		tieringRewardsSummaryOption *commsPb.TieringRewardsSummaryEmailOption_TieringCashbackSummary
		err                         error
	)

	// check if all projections are updated with the reward id
	if !reward.GetRewardMetadata().GetProjectionMetadata().GetAreAllProjectionsUpdated() {
		logger.Error(ctx, "all projections against the reward are not updated.", zap.String(logger.REWARD_ID, reward.GetId()))
		return nil, nil, fmt.Errorf("all projections against the reward are not updated")
	}

	// fetch reward options
	cashRewardOption = rewardsPb.GetRewardOptionOfRewardType(reward.GetRewardOptions().GetOptions(), rewardsPb.RewardType_CASH)
	fiCoinsRewardOption = rewardsPb.GetRewardOptionOfRewardType(reward.GetRewardOptions().GetOptions(), rewardsPb.RewardType_FI_COINS)

	// check if any valid reward option exists
	if cashRewardOption == nil && fiCoinsRewardOption == nil {
		return nil, nil, fmt.Errorf("no valid reward option found")
	}

	rewardOffers, err := h.rewardOffersClient.GetRewardOffersByIds(ctx, &rewardOffersPb.GetRewardOffersByIdsRequest{
		Ids: []string{reward.GetOfferId()},
	})
	if rpcErr := epifigrpc.RPCError(rewardOffers, err); rpcErr != nil {
		logger.Error(ctx, "failed to get reward offers", zap.Error(rpcErr))
		return nil, nil, fmt.Errorf("failed to get reward offers: %w", rpcErr)
	}
	if len(rewardOffers.GetRewardOffers()) == 0 {
		return nil, nil, fmt.Errorf("reward offer not found")
	}
	rewardOffer = rewardOffers.GetRewardOffers()[0]

	// Get cash reward amount if option exists
	if cashRewardOption == nil {
		cashDisplayType = "none"
	} else {
		rewardCashAmount, err = rewardsPb.GetRewardUnitsFromRewardOption(cashRewardOption)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to get reward units from cash reward option: %w", err)
		}

		// fetch max cash reward cap
		rewardCashAmountCap, err = GetRewardUnitsCap(rewardOffer, rewardsPb.RewardType_CASH)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to cash reward units cap: %w", err)
		}
	}

	// Get fi coins reward amount if option exists
	if fiCoinsRewardOption == nil {
		fiCoinsDisplayType = "none"
	} else {
		rewardFiCoinsAmount, err = rewardsPb.GetRewardUnitsFromRewardOption(fiCoinsRewardOption)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to get reward units from fi coins reward option: %w", err)
		}

		// fetch max fi coins reward cap
		rewardFiCoinsAmountCap, err = GetRewardUnitsCap(rewardOffer, rewardsPb.RewardType_FI_COINS)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to fi-coins reward units cap: %w", err)
		}
	}

	// set the tier and value back percentage text
	tierText = offerTypeToTierText[rewardOffer.GetOfferType()]
	valueBackPercentageText = offerTypeToValueBackPercentageText[rewardOffer.GetOfferType()]

	// Set prioritized reward amount and cap based on available options
	// Prioritize cash rewards if available, otherwise use fi coins
	switch {
	case cashRewardOption != nil:
		prioritizedRewardAmount = rewardCashAmount
		prioritizedRewardCap = rewardCashAmountCap
		prioritizedRewardType = rewardsPb.RewardType_CASH
	case fiCoinsRewardOption != nil:
		prioritizedRewardAmount = rewardFiCoinsAmount
		prioritizedRewardCap = rewardFiCoinsAmountCap
		prioritizedRewardType = rewardsPb.RewardType_FI_COINS
	default:
		return nil, nil, fmt.Errorf("no valid reward option found")
	}

	tieringRewardsSummaryOption = &commsPb.TieringRewardsSummaryEmailOption_TieringCashbackSummary{
		TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
		HeaderDetails: &commsPb.TieringRewardsSummaryEmailOption_HeaderDetails{
			Title: "Fi Plans Rewards Summary",
			Date:  fmt.Sprintf("%s - %s", GetFormattedDate(fromStatementDateInIst), GetFormattedDate(toStatementDateInIst)),
		},
		BodyDetails: &commsPb.TieringRewardsSummaryEmailOption_BodyDetails{
			ProgressRingDetails: &commsPb.TieringRewardsSummaryEmailOption_ProgressRingDetails{
				Title: &commsPb.TieringRewardsSummaryEmailOption_IconText{
					IconUrlIdentifier: partialRewardsEarnedIconUrl,
					Text:              "Reward Earned",
				},
				Progress:   strconv.FormatFloat(float64((prioritizedRewardAmount/prioritizedRewardCap)*100), 'f', 0, 32) + "%",
				RingColour: progressRingColorGreen,
			},
			Title: &commsPb.TieringRewardsSummaryEmailOption_IconText{
				IconUrlIdentifier: "",
				Text:              fmt.Sprintf("Congratulations on earning %s for being on the %s Plan.", valueBackPercentageText, tierText),
			},
			RewardDetails: &commsPb.TieringRewardsSummaryEmailOption_RewardDetails{
				Cash: &commsPb.TieringRewardsSummaryEmailOption_RewardDetail{
					DisplayType: cashDisplayType,
					RewardType: &commsPb.TieringRewardsSummaryEmailOption_IconText{
						IconUrlIdentifier: cashWithBgIcon,
						Text:              "CASHBACK EARNED",
					},
					RewardAmount: &commsPb.TieringRewardsSummaryEmailOption_IconText{
						IconUrlIdentifier: cashSymbolIcon,
						Text:              money.ToDisplayStringInIndianFormatFromFloatValue(float64(rewardCashAmount), 0),
					},
					RewardCap: &commsPb.TieringRewardsSummaryEmailOption_IconText{
						IconUrlIdentifier: "",
						Text:              strconv.FormatFloat(float64(rewardCashAmountCap), 'f', 0, 32),
					},
				},
				FiCoins: &commsPb.TieringRewardsSummaryEmailOption_RewardDetail{
					DisplayType: fiCoinsDisplayType,
					RewardType: &commsPb.TieringRewardsSummaryEmailOption_IconText{
						IconUrlIdentifier: fiCoinsWithBgIcon,
						Text:              "FI-COINS EARNED",
					},
					RewardAmount: &commsPb.TieringRewardsSummaryEmailOption_IconText{
						IconUrlIdentifier: fiCoinsSymbolIcon,
						Text:              money.ToDisplayStringInIndianFormatFromFloatValue(float64(rewardFiCoinsAmount), 0),
					},
					RewardCap: &commsPb.TieringRewardsSummaryEmailOption_IconText{
						IconUrlIdentifier: "",
						Text:              strconv.FormatFloat(float64(rewardFiCoinsAmountCap), 'f', 0, 32),
					},
				},
			},
			CtaText: "Want to earn more rewards?",
		},
	}

	// get money plant text based on reward options
	switch {
	case cashRewardOption != nil && fiCoinsRewardOption != nil:
		moneyPlantText = "Reward would be added to your Fi-Coins balance or Federal Bank Savings Account on Fi after you open the money plant."
	case cashRewardOption != nil:
		moneyPlantText = "Reward would be added to your <b>Federal Bank Savings Account on Fi</b> after you open the money plant."
	case fiCoinsRewardOption != nil:
		moneyPlantText = "Reward would be added to your Fi-Coins balance after you open the money plant."
	default:
		return nil, nil, fmt.Errorf("no valid reward option found")
	}
	// update descriptions with :
	descriptions = append(descriptions,
		// 1. add money plant text to the descriptions
		&commsPb.TieringRewardsSummaryEmailOption_IconText{
			IconUrlIdentifier: tickSymbolIconUrl,
			Text:              moneyPlantText,
		},
		// 2. always adding password protection description at the end
		&commsPb.TieringRewardsSummaryEmailOption_IconText{
			IconUrlIdentifier: tickSymbolIconUrl,
			Text:              "The attached statement only contains transactions eligible for rewards based on your account plan that corresponds with your <b>Federal Bank Savings Account on Fi</b>.",
		},
		&commsPb.TieringRewardsSummaryEmailOption_IconText{
			IconUrlIdentifier: tickSymbolIconUrl,
			Text: "Your Fi Plan reward statement is password protected. To get your custom 8-digit password, " +
				"club the first <b>4 letters of your name (as per KYC records) in capital</b> and <b>your birthdate</b> (DDMM)." +
				"<br>" +
				"For example, if your name is <b>Om Dev</b> & you were born on <b>Jan 01</b>, your passcode is <b>OMDE0101</b>.",
		},
	)

	tieringRewardsSummaryOption.GetBodyDetails().Descriptions = descriptions

	// update the progress ring title icon and text if the reward cap is reached
	if prioritizedRewardAmount == prioritizedRewardCap {
		tieringRewardsSummaryOption.GetBodyDetails().GetProgressRingDetails().GetTitle().IconUrlIdentifier = fullRewardsEarnedIconUrl
		tieringRewardsSummaryOption.GetBodyDetails().GetTitle().Text = fmt.Sprintf("Congratulations on earning the complete %s for being in the %s Plan.", valueBackPercentageText, tierText)
	}

	pdfAttachment, err := h.GetPdfAsAttachment(ctx, userResp.GetUser(), reward, rewardOffer, emailTemplateToPdfTemplateMap[emailTemplate], prioritizedRewardType, prioritizedRewardAmount)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get pdf as attachment: %w", err)
	}

	return tieringRewardsSummaryOption, []*commsPb.EmailMessage_Attachment{pdfAttachment}, nil
}
