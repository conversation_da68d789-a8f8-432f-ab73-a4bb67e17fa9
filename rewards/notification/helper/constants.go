package helper

import (
	commsPb "github.com/epifi/gamma/api/comms"
	docsPb "github.com/epifi/gamma/api/docs"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	vgAccountPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
)

const (
	StatementPreSignedURLExpiryTime = 60 * 60 // 1 hour

	// icon text holders
	fiCoinsSymbolIcon  = "fi_coins_symbol"
	fiCoinsWithBgIcon  = "fi_coins_with_bg"
	fiCoinsSummaryIcon = "fi_coins_summary"
	cashSymbolIcon     = "cash_symbol"
	cashWithBgIcon     = "cash_with_bg"
	cashSummaryIcon    = "cash_summary"

	// icon urls
	partialRewardsEarnedIconUrl = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/coin-rain.png"
	fullRewardsEarnedIconUrl    = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/fire.png"
	tickSymbolIconUrl           = "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/check-circle.png"

	progressRingColorGreen = "#005244"
)

var (
	emailTemplateToPdfTemplateMap = map[commsPb.EmailType]docsPb.PDFTemplate{
		commsPb.EmailType_TIERING_REWARDS_SUMMARY_EMAIL: docsPb.PDFTemplate_SAVINGS_STATEMENT_WITH_TIERING_REWARDS_SUMMARY,
	}

	rewardTypeWithBgIconMap = map[rewardsPb.RewardType]string{
		rewardsPb.RewardType_FI_COINS: fiCoinsWithBgIcon,
		rewardsPb.RewardType_CASH:     cashWithBgIcon,
	}

	rewardTypeSummaryIconMap = map[rewardsPb.RewardType]string{
		rewardsPb.RewardType_FI_COINS: fiCoinsSummaryIcon,
		rewardsPb.RewardType_CASH:     cashSummaryIcon,
	}

	rewardTypeSymbolIconMap = map[rewardsPb.RewardType]string{
		rewardsPb.RewardType_CASH:     cashSymbolIcon,
		rewardsPb.RewardType_FI_COINS: fiCoinsSymbolIcon,
	}

	txnTypeMapToAmountBadge = map[vgAccountPb.TransactionType]string{
		vgAccountPb.TransactionType_DEBIT:  "DEBIT",
		vgAccountPb.TransactionType_CREDIT: "CREDIT",
	}

	offerTypeToValueBackPercentageText = map[rewardsPb.RewardOfferType]string{
		rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER:               "1% Back",
		rewardsPb.RewardOfferType_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER:           "2% Back",
		rewardsPb.RewardOfferType_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER:        "1% Back",
		rewardsPb.RewardOfferType_SALARY_BASIC_TIER_1_PERCENT_CASHBACK_OFFER:       "1% Back",
		rewardsPb.RewardOfferType_SALARY_TIER_2_PERCENT_CASHBACK_OFFER:             "2% Back",
		rewardsPb.RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER: "2% Back",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER:   "1% Back",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER:   "2% Back",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER:   "3% Back",
	}

	offerTypeToTierText = map[rewardsPb.RewardOfferType]string{
		rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER:               "Plus",
		rewardsPb.RewardOfferType_INFINITE_TIER_2_PERCENT_CASHBACK_OFFER:           "Infinite",
		rewardsPb.RewardOfferType_SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER:        "Salary Lite",
		rewardsPb.RewardOfferType_SALARY_BASIC_TIER_1_PERCENT_CASHBACK_OFFER:       "Salary Basic",
		rewardsPb.RewardOfferType_SALARY_TIER_2_PERCENT_CASHBACK_OFFER:             "Salary",
		rewardsPb.RewardOfferType_INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER: "Infinite or Salary",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER:   "Prime",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER:   "Prime",
		rewardsPb.RewardOfferType_AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER:   "Prime",
	}
)
