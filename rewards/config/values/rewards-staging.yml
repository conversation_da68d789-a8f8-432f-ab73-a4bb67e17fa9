Application:
  Environment: "staging"
  Name: "rewards"

Server:
  Ports:
    GrpcPort: 9091
    GrpcSecurePort: 9515
    HttpPort: 9999
    HttpPProfPort: 9990

RewardsDb:
  AppName: "rewards"
  StatementTimeout: 5s
  Name: "rewards"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"

RedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "master.staging-common-cache-redis.wyivwq.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
    DB: 10
  ClientName: rewards-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

Secrets:
  Ids:
    DbCredentials: "staging/rds/postgres14"
    RudderWriteKey: "staging/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "staging/gcloud/profiling-service-account-key"
    SlackOauthToken: "staging/rewards/slack-oauth-token"
    KafkaCredentials: "staging/kafka/rewards"
    KafkaCaCertificate: "staging/kafka/cert"
    StartreePinotAuthToken: "staging/pinot/startree"

ServerEndpoints:
  CommsEndpoint: "comms-nlb.pointz.in:8085"
  PaymentinstrumentEndpoint: "paymentinstrument-nlb.pointz.in:8087"

Aws:
  Region: "ap-south-1"

OrderUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-order-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

UserSearchSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-user-search-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

UserSearchV1SqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-user-search-v1-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

ManualGiveawayEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-manual-giveaway-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

FitttExecutionUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-fittt-execution-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

ExtraInterestSdBonusPayoutSqsPublisher:
  QueueName: "staging-rewards-extra-interest-sd-bonus-payout-event"

ExtraInterestSdBonusPayoutSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-extra-interest-sd-bonus-payout-event"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 10
      TimeUnit: "Minute"

MinBalanceSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-min-balance-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

CAAccountUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-ca-account-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

KYCSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-kyc-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

FitttSportsEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-fittt-sports-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

SavingsAccountStateUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-savings-account-state-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

SalaryDetectionSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-salaryprogram-salary-txn-detection-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

DebitCardSwitchNotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-dc-card-switch-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

SalaryProgramStatusUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-salaryprogram-salary-status-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

MerchantPiUpdateAffectedEntitiesEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-rewards-merchant-pi-update-affected-entities-event-queue"
  # to access cross account queue in data-dev
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

OnboardingStageUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-onboarding-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

DataCollectorEventSqsPublisher:
  QueueName: "staging-rewards-data-collector-queue"

DataCollectorEventsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-data-collector-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 7
      TimeUnit: "Minute"

RewardClawbackEventCollectedDataPublisher:
  QueueName: "staging-rewards-reward-clawback-event-collector-queue"

RewardClawbackEventCollectorSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-reward-clawback-event-collector-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 6
      TimeUnit: "Minute"

RewardsClawbackEventCollectedDataCustomDelayPublisher:
  DestQueueName: "staging-rewards-reward-clawback-event-collector-queue"
  OrchestratorSqsPublisher:
    QueueName: "staging-custom-delay-orchestrator-queue"

ClaimRewardEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-claim-reward-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"

RewardsManualGiveawayEventSqsPublisher:
  QueueName: "staging-rewards-manual-giveaway-event-queue"

RewardsClaimRewardEventSqsCustomDelayPublisher:
  DestQueueName: "staging-rewards-claim-reward-event-queue"
  OrchestratorSqsPublisher:
    QueueName: "staging-custom-delay-orchestrator-queue"

DataCollectorEventSqsCustomDelayPublisher:
  DestQueueName: "staging-rewards-data-collector-queue"
  OrchestratorSqsPublisher:
    QueueName: "staging-custom-delay-orchestrator-queue"

Sentry:
  DSN: "http://c6171cb4513f4c15a98cadea428bc863@13.233.212.81:9000/4"
  FlushTimeoutInSecs: 5
  Tags:
    app-name: "rewards"

RewardsSqsPublisher:
  QueueName: "staging-rewards-data-queue"

RewardsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-data-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

RewardsProcessingSqsCustomDelayPublisher:
  DestQueueName: "staging-rewards-processing-delay-queue"
  OrchestratorSqsPublisher:
    QueueName: "staging-custom-delay-orchestrator-queue"

RewardsProcessingDelaySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-processing-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

LuckyDrawWinningProcessingSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-lucky-draw-winning-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

RewardClawbackProcessingSqsPublisher:
  QueueName: "staging-rewards-reward-clawback-processing-queue"

RewardClawbackProcessingSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-reward-clawback-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Second"

CreditCardTransactionsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-credit-card-txn-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

CreditCardRequestStageUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-credit-card-request-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

CreditCardBillGenerationEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-cc-bill-generation-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

InvestmentEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-investment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

RewardsOfferRedemptionStatusUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-offer-redemption-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

CaAccountDataSyncEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-reward-ca-account-data-sync-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

RewardsActorNudgeStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-actor-nudge-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

EpfPassbookImportEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-reward-epf-passbook-import-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

LuckyDrawWinningProcessingSqsPublisher:
  QueueName: "staging-lucky-draw-winning-processing-queue"

RewardStatusUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-reward-status-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Second"

RewardGenerationEventSnsPublisher:
  TopicName: "staging-reward-generation-event-topic"

RewardStatusUpdateEventSnsPublisher:
  TopicName: "staging-rewards-reward-status-update-event-topic"

RewardsNotificationEventSqsCustomDelayPublisher:
  DestQueueName: "staging-rewards-notification-queue"
  OrchestratorSqsPublisher:
    QueueName: "staging-custom-delay-orchestrator-queue"

RewardsNotificationEventsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

CreditReportDownloadEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-credit-report-download-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

VendorRewardFulfillmentEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-vendor-reward-fulfillment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

Jobs:
  ClaimedGiftHampersReportJob :
    S3BucketName : "epifi-staging-rewards"
    DirPath      : "/jobs/claimed-gift-hampers-report"

RewardsBucketName: "epifi-staging-rewards"

RudderStack:
  Key: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Flags:
  DisableRewardsProcessedNotification: false
  TrimDebugMessageFromStatus: false
  EnableGenerationOfRewardsInLockedState: true
  EnableTPAPOrdersForRewardsProcessing: true


RewardsCampaignCommConfig:
  SalaryDepositV1RewardOfferId: ""

ReferralRewardsCappingAndPeriod:
  RewardsCap: 2
  CappingPeriod: 24h
  Active: true

RewardsPeriodicCappingsPerOfferType:
  - 1:
      OfferType: "REFERRAL_REFERRER_OFFER"
      RewardsCap: 1
      CappingPeriod: 1
      CappingDuration: "CALENDAR_MONTH"
      Active: true

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CreditCardRewardsConfig:
  MinReqDelayForRewardEvalRelativeToBillWindowEndDate: 5m # 5 minutes
  ShouldProcessBillingEventImmediatelyForRewardEvaluation: true
  CuratedMerchantNameToKnownMerchantMap:
    "swiggy":
      - 3 #SWIGGY
    "dominos":
      - 27 #DOMINOS
    "zomato":
      - 1 #ZOMATO
      - 593 #ZOMATO_HYPERPURE
    "bookmyshow":
      - 105 #BOOKMYSHOW
    "big_basket":
      - 6 #BIG_BASKET
    "decathlon":
      - 145 #DECATHLON
    "myntra":
      - 7 #MYNTRA
    "ola":
      - 9 #OLA
      - 26 #OLA_MONEY
      - 384 #OLA_CARS
      - 385 #OLA_ELECTRIC
      - 386 #OLA_FOODS
    "uber":
      - 10 #UBER
      - 541 #UBER_EATS
    "dmart":
      - 155 #DMART
    "apollo_pharmacy":
      - 67 #APOLLO_PHARMACY
    "makemytrip":
      - 330 #MAKEMYTRIP
    "croma":
      - 139 #CROMA
    "amazon":
      - 2   #AMAZON
      - 12 #AMAZON_PAY
      - 13 #AMAZON_INTL
      - 51  #AMAZON_PRIME
      - 52 #AMAZON_SELLER_SERVICES
    "flipkart":
      - 4 #FLIPKART
      - 168 #EKART
      - 193 #FLIPKART_WHOLESALE
    "lifestyle":
      - 315 #LIFESTYLE_INTERNATIONAL
    "urban_company":
      - 549 #URBAN_COMPANY
    "zepto":
      - 595 #ZEPTO
    "shoppers_stop":
      - 596 #SHOPPERS_STOP
    "cleartrip":
      - 597 #CLEARTRIP
    "vistara":
      - 886 #VISTARA
  CreditCardSpends1xRewardCalculationConfig:
    TxnAmountToRewardUnitsFactor: 5
    TxnLevelMaxRewardUnitsCap: 500
  CreditCardTopMerchantSpendsRewardConfig:
    MinReqCCSpentAmountInRsForRewardEligibility: 3000
    CountOfMerchantsToBeRewarded : 3
    MultiplierValueOn1xRewardAmount: 3
    MaxRewardUnitsCap: 3000
    OntologyIdsToExcludeWhileCalculatingAggregateSpends:
      - "9c4d98b7-c7b2-452e-a38f-1de36bf64fe9" # Fees and Service taxes
      - "36e073c8-e43f-4ba4-b766-4c7624cfeda4" # Interest
      - "62ec4b2a-fe6b-4415-8ffd-a128e5591dd5" # Wallet

RewardsRewardExpirySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-reward-expiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsRewardExpirySqsPublisher:
  QueueName: "staging-rewards-reward-expiry-queue"

RewardsRewardExpirySqsCustomDelayPublisher:
  DestQueueName: "staging-rewards-reward-expiry-queue"
  OrchestratorSqsPublisher:
    QueueName: "staging-custom-delay-orchestrator-queue"

RewardsRewardUnlockerSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-reward-unlocker-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsRewardUnlockerSqsPublisher:
  QueueName: "staging-rewards-reward-unlocker-queue"

RewardsRewardUnlockerSqsCustomDelayPublisher:
  DestQueueName: "staging-rewards-reward-unlocker-queue"
  OrchestratorSqsPublisher:
    QueueName: "staging-custom-delay-orchestrator-queue"

RewardsProjectionUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-projection-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsProjectionsGenerationEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-projections-generation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

TieringPeriodicRewardEventSqsSubscriber:
  StartOnServerStart: false
  Disable: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  # to access cross account queue in data-dev
  QueueName: "dev-tiering-periodic-reward-event-queue"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

TieringTierUpdateEventSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-tiering-tier-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsProjectionUpdateEventSqsPublisher:
  QueueName: "staging-rewards-projection-update-event-queue"

RewardsProjectionsGenerationEventSqsPublisher:
  QueueName: "staging-rewards-projections-generation-event-queue"

RewardsOrderUpdateEventQueueSqsPublisher:
  QueueName : "staging-rewards-order-update-queue"

RewardsCreditCardTxnEventQueueSqsPublisher:
  QueueName : "staging-rewards-credit-card-txn-event-queue"

RewardsCreditCardBillingEventQueueSqsPublisher:
  QueueName : "staging-rewards-cc-bill-generation-event-consumer-queue"

BulkClaimRewardsEventSqsPublisher:
  QueueName: "staging-bulk-claim-rewards-event-queue"

BulkClaimRewardsEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-bulk-claim-rewards-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsAccountStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-account-operational-status-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

TerminalPinotRewardProducer:
  TopicName: "staging.service.rewards.terminal_state_topic"
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024

RewardsTerminalStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-reward-terminal-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

PinotProjectionProducer:
  TopicName: "staging.service.rewards.reward_projections_topic"
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024

ProjectionEventSnsPublisher:
  TopicName: "staging-rewards-projection-event-topic"

ProjectionEventForPinotSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-rewards-projection-event-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

NonTerminalPinotRewardProducer:
  TopicName: "staging.service.rewards.non_terminal_state_topic"
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024
