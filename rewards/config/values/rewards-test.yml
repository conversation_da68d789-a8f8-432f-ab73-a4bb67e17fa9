Application:
  Environment: "test"
  Name: "rewards"

Server:
  Ports:
    GrpcPort: 9091
    GrpcSecurePort: 9515
    HttpPort: 9999
  EnablePoller: true

RewardsDb:
  AppName: "rewards"
  StatementTimeout: 5m
  Name: "rewards_test"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

RedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0
  ClientName: rewards-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 500
      ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

Secrets:
  Ids:
    DbCredentials: "{\"username\": \"root\", \"password\": \"\"}"
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    SlackOauthToken: "dummy-token"
    KafkaCredentials: ""
    KafkaCaCertificate: ""
    StartreePinotAuthToken: "{\"AuthToken\": \"\"}"

OrderUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-order-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

UserSearchSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-user-search-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

InvestmentEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-investment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

UserSearchV1SqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-user-search-v1-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

ManualGiveawayEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-manual-giveaway-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

FitttExecutionUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "fittt-execution-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

ExtraInterestSdBonusPayoutSqsPublisher:
  QueueName: "rewards-extra-interest-sd-bonus-payout-event"

ExtraInterestSdBonusPayoutSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-extra-interest-sd-bonus-payout-event"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 10
      TimeUnit: "Minute"

MinBalanceSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-min-balance-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

CAAccountUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-ca-account-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

KYCSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-kyc-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

FitttSportsEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-fittt-sports-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

SavingsAccountStateUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-savings-account-state-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

SalaryDetectionSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-salaryprogram-salary-txn-detection-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

DebitCardSwitchNotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-dc-card-switch-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

SalaryProgramStatusUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-salaryprogram-salary-status-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

MerchantPiUpdateAffectedEntitiesEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-merchant-pi-update-affected-entities-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

OnboardingStageUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-onboarding-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

DataCollectorEventSqsPublisher:
  QueueName: "rewards-data-collector-queue"

DataCollectorEventsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-data-collector-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

RewardClawbackEventCollectedDataPublisher:
  QueueName: "rewards-reward-clawback-event-collector-queue"

RewardClawbackEventCollectorSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-reward-clawback-event-collector-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 6
      TimeUnit: "Minute"

RewardsClawbackEventCollectedDataCustomDelayPublisher:
  DestQueueName: "rewards-reward-clawback-event-collector-queue"
  OrchestratorSqsPublisher:
    QueueName: "custom-delay-orchestrator-queue"

ClaimRewardEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-claim-reward-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"

RewardsManualGiveawayEventSqsPublisher:
  QueueName: "rewards-manual-giveaway-event-queue"

RewardsClaimRewardEventSqsCustomDelayPublisher:
  DestQueueName: "rewards-claim-reward-event-queue"
  OrchestratorSqsPublisher:
    QueueName: "custom-delay-orchestrator-queue"

DataCollectorEventSqsCustomDelayPublisher:
  DestQueueName: "rewards-data-collector-queue"
  OrchestratorSqsPublisher:
    QueueName: "custom-delay-orchestrator-queue"

RewardsSqsPublisher:
  QueueName: "rewards-data-queue"

RewardsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-data-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

Aws:
  Region: "ap-south-1"


RewardsProcessingSqsCustomDelayPublisher:
  DestQueueName: "rewards-processing-delay-queue"
  OrchestratorSqsPublisher:
    QueueName: "custom-delay-orchestrator-queue"

RewardsProcessingDelaySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-processing-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

LuckyDrawWinningProcessingSqsPublisher:
  QueueName: "lucky-draw-winning-processing-queue"

LuckyDrawWinningProcessingSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "lucky-draw-winning-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Second"

RewardClawbackProcessingSqsPublisher:
  QueueName: "rewards-reward-clawback-processing-queue"

RewardClawbackProcessingSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-reward-clawback-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Second"

CreditCardTransactionsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-credit-card-txn-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

CreditCardRequestStageUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-credit-card-request-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

CreditCardBillGenerationEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-cc-bill-generation-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsOfferRedemptionStatusUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-offer-redemption-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardStatusUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-reward-status-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Second"

RewardGenerationEventSnsPublisher:
  TopicName: "reward-generation-event-topic"

RewardStatusUpdateEventSnsPublisher:
  TopicName: "rewards-reward-status-update-event-topic"

RewardsNotificationEventSqsCustomDelayPublisher:
  DestQueueName: "rewards-notification-queue"
  OrchestratorSqsPublisher:
    QueueName: "custom-delay-orchestrator-queue"

RewardsNotificationEventsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"

CreditReportDownloadEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-credit-report-download-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

Jobs:
  ClaimedGiftHampersReportJob :
    S3BucketName : "epifi-rewards"
    DirPath      : "/jobs/claimed-gift-hampers-report"

RewardsBucketName: "epifi-rewards"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Flags:
  TrimDebugMessageFromStatus: false
  EnableRewardsForInternalUsersOnly: false
  EnableGenerationOfRewardsInLockedState: true
#  Setting this true here to avoid test case failures
  EnableRewardClawback: true

RewardsPayoutConfig:
  TimeToWaitBeforeCheckingOrderStatus: 0

RewardsCampaignCommConfig:
  SalaryDepositV1RewardOfferId: ""

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

CreditCardRewardsConfig:
  MinReqDelayForRewardEvalRelativeToBillWindowEndDate: 240h # 10 Days
  CuratedMerchantNameToKnownMerchantMap:
    "amazon":
      - 2   #AMAZON
      - 51  #AMAZON_PRIME
    "flipkart":
      - 4 #FLIPKART
    "swiggy":
      - 3 #SWIGGY
    "zomato":
      - 1 #ZOMATO
  CreditCardSpends1xRewardCalculationConfig:
    TxnAmountToRewardUnitsFactor: 5
    TxnLevelMaxRewardUnitsCap: 1500
  CreditCardTopMerchantSpendsRewardConfig:
    MinReqCCSpentAmountInRsForRewardEligibility: 2000
    CountOfMerchantsToBeRewarded : 3
    MultiplierValueOn1xRewardAmount: 3
    MaxRewardUnitsCap: 3000
    OntologyIdsToExcludeWhileCalculatingAggregateSpends:
      - "9c4d98b7-c7b2-452e-a38f-1de36bf64fe9" # Fees and Service taxes
      - "36e073c8-e43f-4ba4-b766-4c7624cfeda4" # Interest
      - "62ec4b2a-fe6b-4415-8ffd-a128e5591dd5" # Wallet

RewardsRewardUnlockerSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-reward-unlocker-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsRewardUnlockerSqsPublisher:
  QueueName: "rewards-reward-unlocker-queue"

RewardsRewardUnlockerSqsCustomDelayPublisher:
  DestQueueName: "rewards-reward-unlocker-queue"
  OrchestratorSqsPublisher:
    QueueName: "custom-delay-orchestrator-queue"

RewardsRewardExpirySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-reward-expiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsRewardExpirySqsPublisher:
  QueueName: "rewards-reward-expiry-queue"

RewardsRewardExpirySqsCustomDelayPublisher:
  DestQueueName: "rewards-reward-expiry-queue"
  OrchestratorSqsPublisher:
    QueueName: "custom-delay-orchestrator-queue"

RewardsProjectionUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-projection-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsProjectionsGenerationEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-projections-generation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

TieringPeriodicRewardEventSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "tiering-periodic-reward-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

TieringTierUpdateEventSqsSubscriber:
  StartOnServerStart: false
  Disable: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-tiering-tier-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsProjectionUpdateEventSqsPublisher:
  QueueName: "rewards-projection-update-event-queue"

RewardsProjectionsGenerationEventSqsPublisher:
  QueueName: "rewards-projections-generation-event-queue"

RewardsOrderUpdateEventQueueSqsPublisher:
  QueueName : "rewards-order-update-queue"

RewardsCreditCardTxnEventQueueSqsPublisher:
  QueueName : "rewards-credit-card-txn-event-queue"

RewardsCreditCardBillingEventQueueSqsPublisher:
  QueueName : "rewards-cc-bill-generation-event-consumer-queue"

BulkClaimRewardsEventSqsPublisher:
  QueueName: "bulk-claim-rewards-event-queue"

BulkClaimRewardsEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "bulk-claim-rewards-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

RewardsAccountStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-account-operational-status-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

TerminalPinotRewardProducer:
  TopicName: "service.rewards.terminal_state_topic"
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  SASLConfig:
    Enabled: false
  TLSConfig:
    Enabled: false
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024

RewardsTerminalStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-reward-terminal-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

PinotProjectionProducer:
  TopicName: "service.rewards.reward_projections_topic"
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  SASLConfig:
    Enabled: false
  TLSConfig:
    Enabled: false
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024

PinotConfig:
  Endpoint:
    Host: "http://localhost"
    Port: 9000
    IsSecure: false

ProjectionEventSnsPublisher:
  TopicName: "rewards-projection-event-topic"

ProjectionEventForPinotSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-projection-event-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

CaAccountDataSyncEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "reward-ca-account-data-sync-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

RewardsActorNudgeStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "rewards-actor-nudge-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

EpfPassbookImportEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "reward-epf-passbook-import-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

VendorRewardFulfillmentEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "vendor-reward-fulfillment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

NonTerminalPinotRewardProducer:
  TopicName: "service.rewards.non_terminal_state_topic"
  Brokers:
    - "kafka-1.data-dev.pointz.in:9094"
    - "kafka-2.data-dev.pointz.in:9094"
    - "kafka-3.data-dev.pointz.in:9094"
  SASLConfig:
    Enabled: false
  TLSConfig:
    Enabled: false
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024
