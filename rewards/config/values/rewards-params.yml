Flags:
  TrimDebugMessageFromStatus: true
  EnableRewardsForInternalUsersOnly: false
  EnableGenerationOfRewardsInLockedState: false
  ValidateRewardOfferConstraintsExpressionOnCreation: true
  EnableTPAPOrdersForRewardsProcessing: false

RewardsNotificationParams:
  RewardProcessingNotificationParams:
    PROCESSED:
      FI_COINS:
        Title: "%d Fi Coins credited 💸"
        Body: "%s"
        ImageUrl: "https://epifi-icons.pointz.in/rewards/in_app_notifications/double-fi-coins.png"
      EGV_BASKET:
        Title: "Congrats! Your reward has been added to your Collected Offers!"
        Body: "%s"
        ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CollectedOffers.png"
      THRIWE_BENEFITS_PACKAGE:
        Title: "Congrats! Your reward has been added to your Collected Offers!"
        Body: "%s"
        ImageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CollectedOffers.png"
      CASH:
        Title: "Congrats! ₹%d cash reward credited!💸"
        Body: "%s"
        ImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/rewards/in_app_notifications/cash.png"

  RewardEarnedNotificationParamsMap:
    SYSTEM_TRAY:
      Title: "Congrats! You just earned a Money-Plant!"
      Body:  "%s"
    IN_APP:
      Title: "You just earned a money-plant"
      Body: "%s"
      ImageUrl: "https://epifi-icons.pointz.in/rewards/in_app_notifications/double-fi-coins.png"
      ExpiresAfterDuration: 6h
    IN_APP_CRITICAL:
      Title: "You just earned a money-plant"
      Body: "%s"
      ImageUrl: "https://epifi-icons.pointz.in/rewards/in_app_notifications/double-fi-coins.png"
      ExpiresAfterDuration: 6h
    IN_APP_FULL_SCREEN:
      Title: "Congrats! You just earned a Money-Plant!"
      Body: "%s"
      ImageUrl: "https://epifi-icons.pointz.in/rewards/plant-4.svg"
      AutoDismissAfterSeconds: 5
#    Always add a default params map because we check for the presence of the key in the map while sending notifications
    EMAIL:

RewardsDb:
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/rewards/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

SlackAlertingConfig:
  EntityUpdatesChannelId: "C02VCMA4EU9" # rewards-engg-discussion

RetryDelayForProcessingInProcessRewards : 10m
RetryDelayForProcessingInProgressRewardClawbacks: 10m
MinTimeDelayBetweenNowAndUnlockDateForLockingReward: 2m

Tracing:
  Enable: false

PinotConfig:
  AppName: "rewards-aggregates"

CacheConfig:
  UserReferralInfoLRUCacheConfig:
    Size: 50
    Ttl: 5s

ReferralRewardsCappingAndPeriod:
  RewardsCap: 200
  CappingPeriod: 720h # 30 days
  Active: false

RewardsPeriodicCappingsPerOfferType:
  - 1:
      OfferType: "REFERRAL_REFERRER_OFFER"
      RewardsCap: 10
      CappingPeriod: 1
      CappingDuration: "CALENDAR_MONTH"
      Active: false

RewardsCountCheckForConsistencyWithGeneration:
  Active: true

RewardsPayoutConfig:
  EpifiBusinessAccountActorId: "actor-epifi-business-account"
  EpifiBusinessAccountPiIds: ["paymentinstrument-epifi-business-account", "paymentinstrument-epifi-business-account-2"]
  EnableRewardsPayoutViaCelestial: true
  TimeToWaitBeforeCheckingOrderStatus: 3

ChequebookChargeTxnBeneficiaryActorId: "actor-federal-generic-account"

EnableTxnAggregatesWithPinot:
  IsFeatureRestricted: false

DataCollectorConfig:
  WaitTimeForFetchingCreditCardTxnCategories: 1s

BulkClaimRewardsRedisLockDuration: 24h

TieringPeriodicRewardConfig:
  RewardTypeToDailyUpperCapMap:
    - 'CASH': 100
    - 'FI_COINS': 4500

RewardsFactGeneratorConfig:
  MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn: 4320h

FeedbackEngineCustomEvaluatorRules:
  - FEEDBACK_APP_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS:
      LastClaimThresholdDuration: 72h
      MinClaimCount: 2
      MinCashbackEarned: 10
      MinFiCoinsEarned: 1000

RewardOfferGenAIReviewConfig:
  BedrockModelId: "anthropic.claude-3-sonnet-********-v1:0"
  SystemPrompt: "You are an expert in reviewing reward offers and suggest changes if required, ops team take your review before making a reward offer live."
  Prompt: "<reward_offer>\\nINPUT_REWARD_OFFER_JSON_PLACEHOLDER\\n</reward_offer>\\n\\n<fields_explanation>\\n- contraint_meta: it's a logical expression, reward will be generated only if this expression evaluates to true.\\n- reward_meta: it contains config to calculate reward, user reward aggregates/capping config and notification config.\\n- display_meta : it contains details to display offer details on the app like- steps to claim reward, terms and conditions etc.\\n</fields_explanation>\\n\\n<instructions>\\n1. display_meta steps should be aligned with contraint_meta.\\n2. display_meta tncs should contain details about reward aggregate present in reward_meta.\\n3. display_meta tncs should contain details offer period based on active_since and active_till.\\n4. action_desc should describe the reason for which reward is earned, example- Earned for doing merchant transaction.\\n5. if is_visible flag is true in the reward offer then display window and active window should be same\\n6. if reward_meta autoprocess is true then reward_meta should have processing success notification config else if autoprocess is not true then earned reward notification config should be present.\\n</instructions>\\n\\nBefore generating the feedback, understand about reward offer fields in <fields_explanation> tags. \\nUnderstand instructions in <instructions> tag step by step, Then, provide the feedback.\\nConsider the following points for feedback output format:\\n- feedback should be in slack message format without using code block.\\n- feedback should be concise and avoid additional suggestions.\\n- don't mention the <instructions> in the output\\n"
  SampleOfferPath: "./mappingJson/sample_reward_offer.json"

OrdersRewardConfig:
  SelectedVpaS3Path: "vpa/razorpay_vpa.csv"
