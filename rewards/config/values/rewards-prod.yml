Application:
  Environment: "prod"
  Name: "rewards"

Server:
  Ports:
    GrpcPort: 9091
    GrpcSecurePort: 9515
    HttpPort: 9999
    HttpPProfPort: 9990

RewardsDb:
  AppName: "rewards"
  StatementTimeout: 10s
  Name: "rewards"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 70
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

QuestSdk:
  Disable: false

QuestRedisOptions:
  IsSecureRedis: true
  Options:
    Addr: "redis-12231.internal.c30655.ap-south-1-mz.ec2.cloud.rlrcp.com:12231"
    Password: ""
  AuthDetails:
    SecretPath: "prod/redis/growth-infra/prefixaccess"
    Environment: "prod"
    Region: "ap-south-1"
  ClientName: rewards-quest-sdk
  HystrixCommand:
    CommandName: "quest_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 10000
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 50
      SleepWindow: 15s
      FallbackMaxConcurrency: 10000

Secrets:
  Ids:
    DbCredentials: "prod/rds/epifimetis/rewards"
    RudderWriteKey: "prod/rudder/internal-writekey"
    GoogleCloudProfilingServiceAccountKey: "prod/gcloud/profiling-service-account-key"
    SlackOauthToken: "prod/rewards/slack-oauth-token"
    KafkaCredentials: "prod/kafka/rewards"
    KafkaCaCertificate: "prod/kafka/cert"
    StartreePinotAuthToken: "prod/pinot/startree"

SlackAlertingConfig:
  EntityUpdatesChannelId: "C042R4M9Q2J" # rewards-catalog-alerts
  IsEnabled: true

Aws:
  Region: "ap-south-1"

CacheConfig:
  UserReferralInfoLRUCacheConfig: # size & ttl values are added keeping in mind the processing of a single data-collector event
    Size: 50
    Ttl: 5s # short ttl to avoid stale data read

OrderUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-order-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1s
    Namespace: "rewards"

UserSearchSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-user-search-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

UserSearchV1SqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-user-search-v1-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

ManualGiveawayEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-manual-giveaway-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "rewards"

FitttExecutionUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-fittt-execution-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

InvestmentEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-investment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

ExtraInterestSdBonusPayoutSqsPublisher:
  QueueName: "prod-rewards-extra-interest-sd-bonus-payout-event"

ExtraInterestSdBonusPayoutSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-extra-interest-sd-bonus-payout-event"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 10
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

MinBalanceSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-min-balance-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

CAAccountUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-ca-account-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

KYCSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-kyc-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

FitttSportsEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-fittt-sports-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

SavingsAccountStateUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-savings-account-state-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

SalaryDetectionSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-salaryprogram-salary-txn-detection-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

DebitCardSwitchNotificationSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-dc-card-switch-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

SalaryProgramStatusUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-salaryprogram-salary-status-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

MerchantPiUpdateAffectedEntitiesEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-merchant-pi-update-affected-entities-event-queue"
  # to access cross account queue in data-prod
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "rewards"

OnboardingStageUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-onboarding-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

CreditCardTransactionsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-credit-card-txn-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "rewards"

DataCollectorEventSqsPublisher:
  QueueName: "prod-rewards-data-collector-queue"

DataCollectorEventsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 3
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-data-collector-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 5
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "rewards"

RewardClawbackEventCollectedDataPublisher:
  QueueName: "prod-rewards-reward-clawback-event-collector-queue"

RewardClawbackEventCollectorSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-reward-clawback-event-collector-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "rewards"

RewardsClawbackEventCollectedDataCustomDelayPublisher:
  DestQueueName: "prod-rewards-reward-clawback-event-collector-queue"
  OrchestratorSqsPublisher:
    QueueName: "prod-custom-delay-orchestrator-queue"

ClaimRewardEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-claim-reward-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

VendorRewardFulfillmentEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-vendor-reward-fulfillment-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 1s
    Namespace: "rewards"

RewardsManualGiveawayEventSqsPublisher:
  QueueName: "prod-rewards-manual-giveaway-event-queue"

RewardsClaimRewardEventSqsCustomDelayPublisher:
  DestQueueName: "prod-rewards-claim-reward-event-queue"
  OrchestratorSqsPublisher:
    QueueName: "prod-custom-delay-orchestrator-queue"

DataCollectorEventSqsCustomDelayPublisher:
  DestQueueName: "prod-rewards-data-collector-queue"
  OrchestratorSqsPublisher:
    QueueName: "prod-custom-delay-orchestrator-queue"

#Sentry:
#  DSN: "http://c6171cb4513f4c15a98cadea428bc863@13.233.212.81:9000/4"
#  FlushTimeoutInSecs: 5
#  Tags:
#    app-name: "rewards"

RewardsSqsPublisher:
  QueueName: "prod-rewards-data-queue"

RewardsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-data-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 2s
    Namespace: "rewards"

RewardsProcessingSqsCustomDelayPublisher:
  DestQueueName: "prod-rewards-processing-delay-queue"
  OrchestratorSqsPublisher:
    QueueName: "prod-custom-delay-orchestrator-queue"

RewardsProcessingDelaySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-processing-delay-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 20
        Period: 2s
    Namespace: "rewards"

LuckyDrawWinningProcessingSqsPublisher:
  QueueName: "prod-lucky-draw-winning-processing-queue"

LuckyDrawWinningProcessingSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-lucky-draw-winning-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

RewardClawbackProcessingSqsPublisher:
  QueueName: "prod-rewards-reward-clawback-processing-queue"

RewardClawbackProcessingSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-reward-clawback-processing-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "rewards"

RewardStatusUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-reward-status-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

RewardGenerationEventSnsPublisher:
  TopicName: "prod-reward-generation-event-topic"

RewardStatusUpdateEventSnsPublisher:
  TopicName: "prod-rewards-reward-status-update-event-topic"

CreditCardRequestStageUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-credit-card-request-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

CreditCardBillGenerationEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-cc-bill-generation-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1s
    Namespace: "rewards"

RewardsOfferRedemptionStatusUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-offer-redemption-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

RewardsNotificationEventSqsCustomDelayPublisher:
  DestQueueName: "prod-rewards-notification-queue"
  OrchestratorSqsPublisher:
    QueueName: "prod-custom-delay-orchestrator-queue"

RewardsNotificationEventsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Second"

CreditReportDownloadEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-credit-report-download-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

Jobs:
  ClaimedGiftHampersReportJob :
    S3BucketName : "prod-epifi-rewards"
    DirPath      : "/jobs/claimed-gift-hampers-report"

RewardsBucketName: "prod-epifi-rewards"

RudderStack:
  IntervalInSec: 10
  BatchSize: 20
  Verbose: false

Flags:
  DisableRewardsProcessedNotification: false
  TrimDebugMessageFromStatus: false
  EnableRewardsForInternalUsersOnly: false
  EnableGenerationOfRewardsInLockedState: true
  DisableEmailBasedNotifications: false
  EnableTPAPOrdersForRewardsProcessing: false


RewardsPayoutConfig:
  EpifiBusinessAccountActorId: "actor-epifi-business-account"
  EpifiBusinessAccountPiIds: ["paymentinstrument-epifi-business-account", "paymentinstrument-epifi-business-account-2"]
  EnableRewardsPayoutViaCelestial: true

RewardsCampaignCommConfig:
  SalaryDepositV1RewardOfferId: "f4468c50-a564-49d9-8b10-5b79f9e04358"

Tracing:
  Enable: true

ReferralRewardsCappingAndPeriod:
  RewardsCap: 40
  CappingPeriod: 8760h # 365 days
  Active: true

RewardsPeriodicCappingsPerOfferType:
  - 1:
      OfferType: "REFERRAL_REFERRER_OFFER"
      RewardsCap: 10
      CappingPeriod: 1
      CappingDuration: "CALENDAR_MONTH"
      Active: false

RewardsCountCheckForConsistencyWithGeneration:
  Active: true

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: true
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

EnableTxnAggregatesWithPinot:
  IsFeatureRestricted: false
  AllowedUserGroups:
    - 1 # INTERNAL

CreditCardRewardsConfig:
  MinReqDelayForRewardEvalRelativeToBillWindowEndDate: 240h # 10 days
  ShouldProcessBillingEventImmediatelyForRewardEvaluation: true
  CuratedMerchantNameToKnownMerchantMap:
    "swiggy":
      - 3 #SWIGGY
    "dominos":
      - 27 #DOMINOS
    "zomato":
      - 1 #ZOMATO
      - 593 #ZOMATO_HYPERPURE
    "bookmyshow":
      - 105 #BOOKMYSHOW
    "big_basket":
      - 6 #BIG_BASKET
    "decathlon":
      - 145 #DECATHLON
    "myntra":
      - 7 #MYNTRA
    "ola":
      - 9 #OLA
      - 26 #OLA_MONEY
      - 384 #OLA_CARS
      - 385 #OLA_ELECTRIC
      - 386 #OLA_FOODS
    "uber":
      - 10 #UBER
      - 541 #UBER_EATS
    "dmart":
      - 155 #DMART
    "apollo_pharmacy":
      - 67 #APOLLO_PHARMACY
    "yatra":
      - 878 #YATRA
    "croma":
      - 139 #CROMA
    "amazon":
      - 2   #AMAZON
      - 12 #AMAZON_PAY
      - 13 #AMAZON_INTL
      - 51  #AMAZON_PRIME
      - 52 #AMAZON_SELLER_SERVICES
    "flipkart":
      - 4 #FLIPKART
      - 168 #EKART
      - 193 #FLIPKART_WHOLESALE
    "lifestyle":
      - 315 #LIFESTYLE_INTERNATIONAL
    "urban_company":
      - 549 #URBAN_COMPANY
    "zepto":
      - 595 #ZEPTO
    "shoppers_stop":
      - 596 #SHOPPERS_STOP
    "cleartrip":
      - 597 #CLEARTRIP
    "vistara":
      - 886 #VISTARA
  CreditCardSpends1xRewardCalculationConfig:
    TxnAmountToRewardUnitsFactor: 5
    TxnLevelMaxRewardUnitsCap: 100000
  CreditCardTopMerchantSpendsRewardConfig:
    MinReqCCSpentAmountInRsForRewardEligibility: 15000
    CountOfMerchantsToBeRewarded : 3
    MultiplierValueOn1xRewardAmount: 3
    MaxRewardUnitsCap: 50000
    OntologyIdsToExcludeWhileCalculatingAggregateSpends:
      - "9c4d98b7-c7b2-452e-a38f-1de36bf64fe9" # Fees and Service taxes
      - "36e073c8-e43f-4ba4-b766-4c7624cfeda4" # Interest
      - "62ec4b2a-fe6b-4415-8ffd-a128e5591dd5" # Wallet

RewardsRewardExpirySqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-reward-expiry-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "rewards"

RewardsRewardExpirySqsPublisher:
  QueueName: "prod-rewards-reward-expiry-queue"

RewardsRewardExpirySqsCustomDelayPublisher:
  DestQueueName: "prod-rewards-reward-expiry-queue"
  OrchestratorSqsPublisher:
    QueueName: "prod-custom-delay-orchestrator-queue"

RewardsRewardUnlockerSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-reward-unlocker-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 40
        Period: 1s
    Namespace: "rewards"

RewardsRewardUnlockerSqsPublisher:
  QueueName: "prod-rewards-reward-unlocker-queue"

RewardsRewardUnlockerSqsCustomDelayPublisher:
  DestQueueName: "prod-rewards-reward-unlocker-queue"
  OrchestratorSqsPublisher:
    QueueName: "prod-custom-delay-orchestrator-queue"

RewardsProjectionUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-projection-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

RewardsProjectionsGenerationEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 3
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-projections-generation-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "rewards"

TieringPeriodicRewardEventSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  # to access cross account queue in data-prod
  QueueName: "prod-tiering-periodic-reward-event-queue"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 15
        Period: 1s
    Namespace: "rewards"

TieringTierUpdateEventSqsSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-tiering-tier-update-event-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

RewardsProjectionUpdateEventSqsPublisher:
  QueueName: "prod-rewards-projection-update-event-queue"

RewardsProjectionsGenerationEventSqsPublisher:
  QueueName: "prod-rewards-projections-generation-event-queue"

RewardsOrderUpdateEventQueueSqsPublisher:
  QueueName : "prod-rewards-order-update-queue"

RewardsCreditCardTxnEventQueueSqsPublisher:
  QueueName : "prod-rewards-credit-card-txn-event-queue"

RewardsCreditCardBillingEventQueueSqsPublisher:
  QueueName : "prod-rewards-cc-bill-generation-event-consumer-queue"

BulkClaimRewardsEventSqsPublisher:
  QueueName: "prod-bulk-claim-rewards-event-queue"

BulkClaimRewardsEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-bulk-claim-rewards-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

RewardsAccountStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-account-operational-status-update-consumer-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"

TerminalPinotRewardProducer:
  TopicName: "prod.service.rewards.terminal_state_topic"
  Brokers:
    - "kafka-4.data-prod.epifi.in:9094"
    - "kafka-5.data-prod.epifi.in:9094"
    - "kafka-6.data-prod.epifi.in:9094"
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024


RewardsTerminalStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-reward-terminal-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

PinotProjectionProducer:
  TopicName: "prod.service.rewards.reward_projections_topic"
  Brokers:
    - "kafka-4.data-prod.epifi.in:9094"
    - "kafka-5.data-prod.epifi.in:9094"
    - "kafka-6.data-prod.epifi.in:9094"
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024

ProjectionEventSnsPublisher:
  TopicName: "prod-rewards-projection-event-topic"

CaAccountDataSyncEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-reward-ca-account-data-sync-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

EpfPassbookImportEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-reward-epf-passbook-import-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

RewardsActorNudgeStatusUpdateEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-actor-nudge-status-update-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1s
    Namespace: "rewards"

ProjectionEventForPinotSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-rewards-projection-event-for-pinot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 9
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "rewards"

NonTerminalPinotRewardProducer:
  TopicName: "prod.service.rewards.non_terminal_state_topic"
  Brokers:
    - "kafka-4.data-prod.epifi.in:9094"
    - "kafka-5.data-prod.epifi.in:9094"
    - "kafka-6.data-prod.epifi.in:9094"
  SASLConfig:
    Enabled: true
  TLSConfig:
    Enabled: true
  FlushFrequency: 50ms
  FlushMessages: 5000
  FlushMaxMessages: 10000
  FlushMaxBytes: 1024

WatsonMeta:
  IsEnabled: true
  IsErrorActivityEnabled: true
  IsUserActivityEnabled: true
  DefaultIssueCategoryIdForRewards: "4272d1a9-9be5-5f16-8784-d47db2e865a2"
  DefaultIssueCategoryIdForProjections: "88e4f959-f65b-5c17-aafa-82d2021892f2"

MerchantIdsOfRewardsInterest:
  - "0011bf9a-6311-4231-8234-781a9c113406" # swiggy
  - "f6934372-c0f6-4f0a-926f-b7cc24377959" # big_basket
  - "008b65b7-cd2e-464f-b694-afd84364bbd1" # myntra
  - "f8cfcd3e-7397-4c73-b768-986c2e6531ab" # vistara
  - "0b978b29-bdb4-4074-be5a-e757610c460d" # ola
  - "0043dbbe-07c9-40a7-b15c-d27e53ffa77d" # lifestyle_international
  - "4392da78-e557-4154-b91a-8ea5cd2822bf" # zepto
  - "0eb98595-3d46-4e1a-89a4-a8b7c775c926" # urban_company
  - "0568d0cc-d0f3-4ebe-9162-018fa9d48837" # flipkart
  - "00d77c27-c2b5-4d14-9864-6f893351f264" # dominos
  - "00210cdb-9752-448f-b24b-46cb2848cded" # uber
  - "0b46d822-9ef5-435b-bc40-9f5e60589bd0" # amazon
  - "044f4252-283e-40d3-b494-d3091292fa63" # zomato
  - "0d4d59de-ed86-4dd6-9a66-edbe5bcf551b" # zomato_hyperpure
  - "00144377-ab45-4269-89ed-042f254fb644" # dmart
  - "095c7b63-04fa-4f5d-a711-296e98b30fac" # croma
  - "7f92aee8-2efe-4b60-9053-177de9603895" # bookmyshow
  - "00a949f3-0cff-446c-91a9-623a450d5882" # apollo_pharmacy
  - "00de5f76-cb24-4cc3-aa7f-e741eaec28e4" # decathlon
  - "0fe81c23-1cd0-4d54-82b7-dce89bc81927" # yatra
  - "3430f9ac-6365-4996-922a-5fbb341104ad" # shoppers_stop
  - "25ba7872-0737-440b-be9e-0ac45ec86b79" # cleartrip
