// nolint: gosec
package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
	sdkConfig "github.com/epifi/be-common/quest/sdk/config"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	merchantPb "github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/pkg/pinot"
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

const (
	RudderWriteKey         = "RudderWriteKey"
	SlackOauthTokenKey     = "SlackOauthToken"
	dbCredentials          = "DbCredentials"
	kafkaCredentials       = "KafkaCredentials"
	kafkaCaCertificate     = "KafkaCaCertificate"
	StartreePinotAuthToken = "StartreePinotAuthToken"
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(false)
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(true)
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load static config")
	}

	return config, nil
}

func loadConfig(onlyStaticFiles bool) (*Config, error) {
	// will be used only for test environment
	configDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(*configDirPath, cfg.REWARD_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	if onlyStaticFiles {
		return conf, nil
	}
	keyToIdMap := cfg.AddPgdbSslCertSecretIds(conf.RewardsDb, conf.Secrets.Ids)
	keyToSecret, err := cfg.LoadSecrets(keyToIdMap, conf.Application.Environment, conf.Aws.Region)

	if err != nil {
		return nil, err
	}

	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}
	return conf, nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_RDS)
	cfg.UpdateDbEndpointInConfig(c.RewardsDb, dbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdatePGDBSecretValues(c.RewardsDb, c.Secrets, keyToSecret)
	// update db username and password in db config
	// if env is development keys are not fetched from SM hence update from yml file itself
	if c.Application.Environment == cfg.TestEnv || c.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c.RewardsDb, c.Secrets.Ids[dbCredentials])
		return nil
	}
	if _, ok := keyToSecret[dbCredentials]; !ok {
		return fmt.Errorf("db username password not fetched from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.RewardsDb, keyToSecret[dbCredentials])

	// update kafka username, password and ca certificate for TerminalPinotRewardProducer kafka stream producer
	if err := updateKafkaStreamProducerConfig(c.TerminalPinotRewardProducer, keyToSecret); err != nil {
		return err
	}

	// update kafka username, password and ca certificate for PinotProjectionProducer kafka stream producer
	if err := updateKafkaStreamProducerConfig(c.PinotProjectionProducer, keyToSecret); err != nil {
		return err
	}

	// update kafka username, password and ca certificate for NonTerminalPinotRewardProducer kafka stream producer
	if err := updateKafkaStreamProducerConfig(c.NonTerminalPinotRewardProducer, keyToSecret); err != nil {
		return err
	}

	// update pinot config
	c.PinotConfig.Endpoint = cfg.GetPinotEndpoint(cfg.PINOT)
	if _, ok := keyToSecret[StartreePinotAuthToken]; !ok {
		return fmt.Errorf("unable to fetch startree pinot auth token from secrets manager")
	}
	pinot.UpdateAuthTokenInConfig(c.PinotConfig, keyToSecret[StartreePinotAuthToken])

	return nil
}

func updateKafkaStreamProducerConfig(kafkaStreamProducerConfig *cfg.KafkaStreamProducer, keyToSecret map[string]string) error {
	if kafkaStreamProducerConfig.SASLConfig.Enabled {
		if _, ok := keyToSecret[kafkaCredentials]; !ok {
			return fmt.Errorf("unable to fetch kafka stream producer username password from secrets manager")
		}
		cfg.UpdateKafkaSPUsernamePasswordInConfig(kafkaStreamProducerConfig, keyToSecret[kafkaCredentials])
	}

	if kafkaStreamProducerConfig.TLSConfig.Enabled {
		if _, ok := keyToSecret[kafkaCaCertificate]; !ok {
			return fmt.Errorf("unable to fetch kakfa stream producer ca certificate from secrets manager")
		}
		kafkaStreamProducerConfig.TLSConfig.Certificate = keyToSecret[kafkaCaCertificate]
	}
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		c.RewardsDb.Host = val
	}

	if val, ok := os.LookupEnv("REDIS_HOST"); ok {
		c.RedisOptions.Addr = val
	}

	return nil
}

func testEnvConfigDir() *string {
	configPath := filepath.Join(b, "..", "values")
	return &configPath
}

// TODO(Vikas): Add validate tags later and verify the struct post unmarshalling
//
//go:generate conf_gen github.com/epifi/gamma/rewards/config Config
type Config struct {
	Application                                        *Application
	Server                                             *Server
	Logging                                            *cfg.Logging
	RewardsDb                                          *cfg.DB
	RedisOptions                                       *cfg.RedisOptions
	QuestSdk                                           *sdkConfig.Config `dynamic:"true"`
	QuestRedisOptions                                  *cfg.RedisOptions
	Secrets                                            *cfg.Secrets
	Aws                                                *Aws
	PinotConfig                                        *pinot.Config
	DataCollectorEventsSqsSubscriber                   *cfg.SqsSubscriber `dynamic:"true"`
	ClaimRewardEventSqsSubscriber                      *cfg.SqsSubscriber `dynamic:"true"`
	DataCollectorEventSqsPublisher                     *cfg.SqsPublisher
	RewardsSqsSubscriber                               *cfg.SqsSubscriber `dynamic:"true"`
	RewardsSqsPublisher                                *cfg.SqsPublisher
	OrderUpdateSqsSubscriber                           *cfg.SqsSubscriber `dynamic:"true"`
	UserSearchSqsSubscriber                            *cfg.SqsSubscriber `dynamic:"true"`
	UserSearchV1SqsSubscriber                          *cfg.SqsSubscriber `dynamic:"true"`
	ManualGiveawayEventSqsSubscriber                   *cfg.SqsSubscriber `dynamic:"true"`
	FitttExecutionUpdateSqsSubscriber                  *cfg.SqsSubscriber `dynamic:"true"`
	ExtraInterestSdBonusPayoutSqsSubscriber            *cfg.SqsSubscriber `dynamic:"true"`
	MinBalanceSqsSubscriber                            *cfg.SqsSubscriber `dynamic:"true"`
	CAAccountUpdateEventSqsSubscriber                  *cfg.SqsSubscriber `dynamic:"true"`
	KYCSqsSubscriber                                   *cfg.SqsSubscriber `dynamic:"true"`
	FitttSportsEventSqsSubscriber                      *cfg.SqsSubscriber `dynamic:"true"`
	SavingsAccountStateUpdateSqsSubscriber             *cfg.SqsSubscriber `dynamic:"true"`
	SalaryDetectionSqsSubscriber                       *cfg.SqsSubscriber `dynamic:"true"`
	SalaryProgramStatusUpdateSqsSubscriber             *cfg.SqsSubscriber `dynamic:"true"`
	DebitCardSwitchNotificationSqsSubscriber           *cfg.SqsSubscriber `dynamic:"true"`
	OnboardingStageUpdateSqsSubscriber                 *cfg.SqsSubscriber `dynamic:"true"`
	ExtraInterestSdBonusPayoutSqsPublisher             *cfg.SqsPublisher
	CreditCardTransactionsSqsSubscriber                *cfg.SqsSubscriber `dynamic:"true"`
	CreditCardRequestStageUpdateSqsSubscriber          *cfg.SqsSubscriber `dynamic:"true"`
	CreditCardBillGenerationEventSqsSubscriber         *cfg.SqsSubscriber `dynamic:"true"`
	RewardsRewardUnlockerSqsPublisher                  *cfg.SqsPublisher
	RewardsRewardUnlockerSqsSubscriber                 *cfg.SqsSubscriber `dynamic:"true"`
	RewardsRewardUnlockerSqsCustomDelayPublisher       *cfg.SqsCustomDelayQueuePublisher
	RewardsRewardExpirySqsPublisher                    *cfg.SqsPublisher
	RewardsRewardExpirySqsSubscriber                   *cfg.SqsSubscriber `dynamic:"true"`
	MerchantPiUpdateAffectedEntitiesEventSqsSubscriber *cfg.SqsSubscriber `dynamic:"true" ,iam:"external-consumer"`
	RewardsRewardExpirySqsCustomDelayPublisher         *cfg.SqsCustomDelayQueuePublisher
	// clawback generator queue pub/sub
	RewardClawbackEventCollectorSqsSubscriber             *cfg.SqsSubscriber `dynamic:"true"`
	RewardClawbackEventCollectedDataPublisher             *cfg.SqsPublisher
	RewardsClawbackEventCollectedDataCustomDelayPublisher *cfg.SqsCustomDelayQueuePublisher
	// clawback processor queue pub/sub
	RewardClawbackProcessingSqsSubscriber             *cfg.SqsSubscriber `dynamic:"true"`
	RewardClawbackProcessingSqsPublisher              *cfg.SqsPublisher
	RewardsProcessingSqsCustomDelayPublisher          *cfg.SqsCustomDelayQueuePublisher
	RewardsProcessingDelaySqsSubscriber               *cfg.SqsSubscriber `dynamic:"true"`
	LuckyDrawWinningProcessingSqsPublisher            *cfg.SqsPublisher
	LuckyDrawWinningProcessingSqsSubscriber           *cfg.SqsSubscriber `dynamic:"true"`
	RewardStatusUpdateSqsSubscriber                   *cfg.SqsSubscriber `dynamic:"true"`
	RewardGenerationEventSnsPublisher                 *cfg.SnsPublisher
	RewardStatusUpdateEventSnsPublisher               *cfg.SnsPublisher
	RewardsManualGiveawayEventSqsPublisher            *cfg.SqsPublisher
	ClawbackEventCollectorSqsSubscriber               *cfg.SqsSubscriber `dynamic:"true"`
	RewardsClaimRewardEventSqsCustomDelayPublisher    *cfg.SqsCustomDelayQueuePublisher
	DataCollectorEventSqsCustomDelayPublisher         *cfg.SqsCustomDelayQueuePublisher
	RewardsNotificationEventSqsCustomDelayPublisher   *cfg.SqsCustomDelayQueuePublisher
	RewardsNotificationEventsSqsSubscriber            *cfg.SqsSubscriber `dynamic:"true"`
	CreditReportDownloadEventSubscriber               *cfg.SqsSubscriber `dynamic:"true"`
	InvestmentEventSubscriber                         *cfg.SqsSubscriber `dynamic:"true"`
	RewardsOfferRedemptionStatusUpdateEventSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	RewardsBucketName                                 string             `dynamic:"true"`
	RewardsNotificationParams                         *RewardsNotificationParams
	Sentry                                            *cfg.Sentry
	RudderStack                                       *cfg.RudderStackBroker
	Jobs                                              *Jobs
	Flags                                             *Flags               `dynamic:"true"`
	RewardsPayoutConfig                               *RewardsPayoutConfig `dynamic:"true"`
	SlackAlertingConfig                               *SlackAlertingConfig `dynamic:"true"`
	RewardsCampaignCommConfig                         *RewardsCampaignCommConfig
	ReferralRewardsCappingAndPeriod                   *ReferralRewardsCappingAndPeriod       `dynamic:"true" ,quest:"component,area:Rewards"`
	RewardsPeriodicCappingsPerOfferType               map[string]*RewardsPeriodicCappingInfo `dynamic:"true"`
	RewardsCountCheckForConsistencyWithGeneration     *RewardsCountCheckForConsistencyWithGeneration
	// delay after which a reward in processing in progress state should be retried.
	RetryDelayForProcessingInProcessRewards time.Duration
	// delay after which a clawback in processing in progress state should be retried
	RetryDelayForProcessingInProgressRewardClawbacks time.Duration
	Tracing                                          *cfg.Tracing
	Profiling                                        *cfg.Profiling
	CacheConfig                                      *CacheConfig `dynamic:"true"`
	// feature flag to control whether to fetch txnAggregates from Hybrid model of Pinot and Crdb
	EnableTxnAggregatesWithPinot *cfg.FeatureReleaseConfig `dynamic:"true"`
	// denotes the actor to which the chequebook request charge related txns are credited,
	// this is useful in identifying the chequebook charges related txns.
	ChequebookChargeTxnBeneficiaryActorId               string               `dynamic:"true"`
	DataCollectorConfig                                 *DataCollectorConfig `dynamic:"true"`
	CreditCardRewardsConfig                             *CreditCardRewardsConfig
	MinTimeDelayBetweenNowAndUnlockDateForLockingReward time.Duration
	RewardsProjectionsGenerationEventSqsSubscriber      *cfg.SqsSubscriber `dynamic:"true"`
	RewardsProjectionUpdateEventSqsSubscriber           *cfg.SqsSubscriber `dynamic:"true"`
	RewardsProjectionUpdateEventSqsPublisher            *cfg.SqsPublisher
	RewardsProjectionsGenerationEventSqsPublisher       *cfg.SqsPublisher
	TieringPeriodicRewardEventSqsSubscriber             *cfg.SqsSubscriber `dynamic:"true" iam:"external-consumer"`
	TieringTierUpdateEventSqsSubscriber                 *cfg.SqsSubscriber `dynamic:"true"`
	EpfPassbookImportEventSqsSubscriber                 *cfg.SqsSubscriber `dynamic:"true"`
	CaAccountDataSyncEventSqsSubscriber                 *cfg.SqsSubscriber `dynamic:"true"`
	RewardsActorNudgeStatusUpdateEventSqsSubscriber     *cfg.SqsSubscriber `dynamic:"true"`
	BulkClaimRewardsEventSqsPublisher                   *cfg.SqsPublisher
	BulkClaimRewardsEventSqsSubscriber                  *cfg.SqsSubscriber `dynamic:"true"`
	BulkClaimRewardsRedisLockDuration                   time.Duration      `dynamic:"true"`
	RewardsOrderUpdateEventQueueSqsPublisher            *cfg.SqsPublisher
	RewardsCreditCardTxnEventQueueSqsPublisher          *cfg.SqsPublisher
	RewardsCreditCardBillingEventQueueSqsPublisher      *cfg.SqsPublisher
	TieringPeriodicRewardConfig                         *TieringPeriodicRewardConfig `dynamic:"true"`
	RewardsFactGeneratorConfig                          *RewardsFactGeneratorConfig  `dynamic:"true"`
	RewardsAccountStatusUpdateEventSqsSubscriber        *cfg.SqsSubscriber           `dynamic:"true"`
	TerminalPinotRewardProducer                         *cfg.KafkaStreamProducer
	RewardsTerminalStatusUpdateEventSqsSubscriber       *cfg.SqsSubscriber `dynamic:"true"`
	PinotProjectionProducer                             *cfg.KafkaStreamProducer
	ProjectionEventSnsPublisher                         *cfg.SnsPublisher
	ProjectionEventForPinotSqsSubscriber                *cfg.SqsSubscriber `dynamic:"true"`
	NonTerminalPinotRewardProducer                      *cfg.KafkaStreamProducer
	WatsonMeta                                          *WatsonMeta                   `dynamic:"true"`
	RewardOfferGenAIReviewConfig                        *RewardOfferGenAIReviewConfig `dynamic:"true"`
	// IDs of merchants which are considered for rewards/projection generation
	MerchantIdsOfRewardsInterest []string
	// FeedbackEngineCustomEvaluatorRules contains the custom evaluator rules for feedback engine.
	FeedbackEngineCustomEvaluatorRules map[string]*FeedbackEngineCustomEvaluatorRule `dynamic:"true"`
	// config for reward generated via Orders/txns
	OrdersRewardConfig                        *OrdersRewardConfig `dynamic:"true"`
	VendorRewardFulfillmentEventSqsSubscriber *cfg.SqsSubscriber  `dynamic:"true"`
}

type Application struct {
	Environment string
	Name        string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Aws struct {
	Region string
}

type Jobs struct {
	ClaimedGiftHampersReportJob *ClaimedGiftHampersReportJob
}

type FeedbackEngineCustomEvaluatorRule struct {
	LastClaimThresholdDuration time.Duration `dynamic:"true"`
	MinClaimCount              int           `dynamic:"true"`
	MinCashbackEarned          int           `dynamic:"true"`
	MinFiCoinsEarned           int           `dynamic:"true"`
}

type ClaimedGiftHampersReportJob struct {
	S3BucketName string
	DirPath      string
}

type CacheConfig struct {
	// config for user referral info in-memory LRU cache
	UserReferralInfoLRUCacheConfig *UserReferralInfoLRUCacheConfig `dynamic:"true"`
}

// config for reward generated via Orders/txns
type OrdersRewardConfig struct {
	// selected VPAs file s3 path, required for rewarding on txns done to selected vpas
	SelectedVpaS3Path string `dynamic:"true"`
}

type RewardOfferGenAIReviewConfig struct {
	BedrockModelId  string `dynamic:"true"`
	Prompt          string `dynamic:"true"`
	SystemPrompt    string `dynamic:"true"`
	SampleOfferPath string `dynamic:"true"`
}

type UserReferralInfoLRUCacheConfig struct {
	// size of the LRU cache, i.e. max number of keys can be present in the cache at a time
	// post reaching this limit, the least recently used key will be evicted
	Size int           `dynamic:"true"`
	Ttl  time.Duration `dynamic:"true"`
}

type SlackAlertingConfig struct {
	// slack messages/notifications allowed or not
	IsEnabled bool `dynamic:"true"`

	// channel-id for the updates related to entities need to be sent
	EntityUpdatesChannelId string `dynamic:"true"`
}

type NotificationTemplateParams struct {
	Title    string
	Body     string
	ImageUrl string
	// some notification templates support these params
	AutoDismissAfterSeconds int
	// time after which the notification should expire
	ExpiresAfterDuration time.Duration
	// deeplink to be used for the notification
	Deeplink *deeplinkPb.Deeplink
}

type RewardsNotificationParams struct {
	// reward processing notifications differ depending on processing status and reward
	RewardProcessingNotificationParams map[string]map[string]*NotificationTemplateParams
	// notification params map for earned reward notifications for supported notification types like SYSTEM_TRAY, IN_APP etc
	// key of map is rewardOfferPb.RewardNotificationType and value is the corresponding notification configuration
	RewardEarnedNotificationParamsMap map[string]*NotificationTemplateParams
	// data for notification to be sent when reward is unlocked
	// key of map is rewardOfferPb.RewardNotificationType and value is the corresponding notification configuration
	RewardUnlockedNotificationParamsMap map[string]*NotificationTemplateParams
}

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool `dynamic:"true"`
	// If true rewards feature would only be exposed to internal users.
	// If false it would be available for all the users.
	EnableRewardsForInternalUsersOnly bool `dynamic:"true"`
	// flag to decide whether notifications are supposed to be sent or not when rewards processing is successful.
	// note: notifications triggered via external domain services are not controlled, i.e. orders for cash
	DisableRewardsProcessedNotification bool `dynamic:"true"`
	// flag to decide whether we should generate new rewards in `LOCKED` state.
	// if set to false, rewards will be generated in `CREATED` state only
	EnableGenerationOfRewardsInLockedState bool `dynamic:"true"`
	// flag to decide whether to validate the reward offer constraints expression while creating/updating reward offers
	ValidateRewardOfferConstraintsExpressionOnCreation bool `dynamic:"true"`
	// flag to decide whether email based notifications are supposed to be disabled or not
	// putting this flag here instead of NotificationTemplateParams because map-of-map is not supported in confgen(RewardProcessingNotificationParams)
	DisableEmailBasedNotifications bool `dynamic:"true"`
	// flag to enable or disable reward clawback flows
	// note: not keeping this dynamic as fi coins to fi points clawbacks are not supported yet
	// and this flag should only be enabled after the handling of fi coins to fi points clawbacks is added
	EnableRewardClawback bool

	// Flag to control whether TPAP txns should be considered for rewards processing or not.
	// https://github.com/epiFi/tickets/issues/56397
	//
	// This flag, along with controlling the processing of TPAP txns, will also control whether Fi-lite users are allowed the evaluation of Orders for rewards processing or not.
	// This is done to have a single kill-switch while enabling/disabling the TPAP orders processing.
	EnableTPAPOrdersForRewardsProcessing bool `dynamic:"true"`
}

type RewardsPayoutConfig struct {
	EpifiBusinessAccountActorId     string
	EpifiBusinessAccountPiIds       []string `dynamic:"true"`
	EnableRewardsPayoutViaCelestial bool     `dynamic:"true"`
	// Time (in seconds) to wait before checking for updated status of order
	TimeToWaitBeforeCheckingOrderStatus int
}

type RewardsCampaignCommConfig struct {
	// reward offer id for salaryDepositV1 reward campaign
	SalaryDepositV1RewardOfferId string
}

type ReferralRewardsCappingAndPeriod struct {
	RewardsCap    uint32        `dynamic:"true" ,quest:"variable"`
	CappingPeriod time.Duration `dynamic:"true"`
	Active        bool          `dynamic:"true"`
}

// Struct to store capping info per offer type
// The information consists of whether the capping is currently active or not,
// the period of capping, the duration of the capping along with the cap or limit
type RewardsPeriodicCappingInfo struct {
	RewardsCap    uint32 `dynamic:"true"`
	OfferType     string
	CappingPeriod int `dynamic:"true"`
	// this denotes the duration of the capping period
	// example - if capping period is 1 and capping duration is CALENDAR_MONTH -
	// then capping will be calculated from the start of the month
	// supported values - CALENDAR_MONTH, CALENDAR_DAY, CALENDAR_WEEK
	// add handling in rewards/generator/ruleengine/fact/common/rewards_capping_info.go if any new Duration support is being added
	CappingDuration string
	Active          bool `dynamic:"true"`
}

type RewardsCountCheckForConsistencyWithGeneration struct {
	Active bool
}

type DataCollectorConfig struct {
	WaitTimeForFetchingCreditCardTxnCategories time.Duration `dynamic:"true"`
}

type CreditCardRewardsConfig struct {
	CreditCardSpends1xRewardCalculationConfig *CreditCardSpends1xRewardCalculationConfig
	CreditCardTopMerchantSpendsRewardConfig   *CreditCardTopMerchantSpendsRewardConfig
	// map of curated merchant names (for top merchant spends/curated merchant spends reward) to corresponding the known merchant present in merchant service.
	CuratedMerchantNameToKnownMerchantMap map[string][]merchantPb.KnownMerchant
	// min required delay only after which the credit card billing event should be evaluated for top merchant spends/curated merchant spends reward.
	MinReqDelayForRewardEvalRelativeToBillWindowEndDate time.Duration
	// flag to decide if we want to immediately process billing event for generation of billing related rewards. if set, MinReqDelayForRewardEvalRelativeToBillWindowEndDate will be ignored
	// ** NOTE **: it's currently only used to enable immediate processing of billing events for QA/staging testing
	ShouldProcessBillingEventImmediatelyForRewardEvaluation bool
}

type CreditCardSpends1xRewardCalculationConfig struct {
	// TxnAmountToRewardUnitsFactor is useful to calculate the units to be rewarded given a credit card txn amount.
	// Eg if this value is 5 then it implies that 1 unit of Cash/Fi-Coins (whichever is configured) should be rewarded for every 5 Rs spend on credit card.
	TxnAmountToRewardUnitsFactor int32
	// max 1x reward units that can be given as a single reward (transaction level capping)
	TxnLevelMaxRewardUnitsCap float64
}

type CreditCardTopMerchantSpendsRewardConfig struct {
	// minimum required cc spend amount in a billing cycle for being eligible for top merchant spends reward.
	MinReqCCSpentAmountInRsForRewardEligibility int32
	// count of top merchants which would be rewarded.
	CountOfMerchantsToBeRewarded int
	// reward multiplier used for calculating top merchant spends reward from 1x reward amount.
	MultiplierValueOn1xRewardAmount float64
	// max reward units that can be given as a single reward (top merchant reward capping)
	MaxRewardUnitsCap float64
	// list of ontology IDs that won't be considered while calculating net CC spends
	OntologyIdsToExcludeWhileCalculatingAggregateSpends []string
}

type TieringPeriodicRewardConfig struct {
	RewardTypeToDailyUpperCapMap map[string]float32 `dynamic:"true"`
}

type RewardsFactGeneratorConfig struct {
	MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn time.Duration `dynamic:"true"`
}

type WatsonMeta struct {
	IsEnabled                            bool   `dynamic:"true"`
	IsErrorActivityEnabled               bool   `dynamic:"true"`
	IsUserActivityEnabled                bool   `dynamic:"true"`
	DefaultIssueCategoryIdForRewards     string `dynamic:"true"`
	DefaultIssueCategoryIdForProjections string `dynamic:"true"`
}
