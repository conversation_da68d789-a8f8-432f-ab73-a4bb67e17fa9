// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	pkgweb "github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	genconfig "github.com/epifi/be-common/quest/sdk/config/genconf"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	pinot "github.com/epifi/gamma/pkg/pinot"
	config "github.com/epifi/gamma/rewards/config"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_BulkClaimRewardsRedisLockDuration   int64
	_RewardsPeriodicCappingsPerOfferType *syncmap.Map[string, *RewardsPeriodicCappingInfo]
	// FeedbackEngineCustomEvaluatorRules contains the custom evaluator rules for feedback engine.
	_FeedbackEngineCustomEvaluatorRules *syncmap.Map[string, *FeedbackEngineCustomEvaluatorRule]
	_RewardsBucketName                  string
	_RewardsBucketNameMutex             *sync.RWMutex
	// denotes the actor to which the chequebook request charge related txns are credited,
	// this is useful in identifying the chequebook charges related txns.
	_ChequebookChargeTxnBeneficiaryActorId                 string
	_ChequebookChargeTxnBeneficiaryActorIdMutex            *sync.RWMutex
	_QuestSdk                                              *genconfig.Config
	_DataCollectorEventsSqsSubscriber                      *gencfg.SqsSubscriber
	_ClaimRewardEventSqsSubscriber                         *gencfg.SqsSubscriber
	_RewardsSqsSubscriber                                  *gencfg.SqsSubscriber
	_OrderUpdateSqsSubscriber                              *gencfg.SqsSubscriber
	_UserSearchSqsSubscriber                               *gencfg.SqsSubscriber
	_UserSearchV1SqsSubscriber                             *gencfg.SqsSubscriber
	_ManualGiveawayEventSqsSubscriber                      *gencfg.SqsSubscriber
	_FitttExecutionUpdateSqsSubscriber                     *gencfg.SqsSubscriber
	_ExtraInterestSdBonusPayoutSqsSubscriber               *gencfg.SqsSubscriber
	_MinBalanceSqsSubscriber                               *gencfg.SqsSubscriber
	_CAAccountUpdateEventSqsSubscriber                     *gencfg.SqsSubscriber
	_KYCSqsSubscriber                                      *gencfg.SqsSubscriber
	_FitttSportsEventSqsSubscriber                         *gencfg.SqsSubscriber
	_SavingsAccountStateUpdateSqsSubscriber                *gencfg.SqsSubscriber
	_SalaryDetectionSqsSubscriber                          *gencfg.SqsSubscriber
	_SalaryProgramStatusUpdateSqsSubscriber                *gencfg.SqsSubscriber
	_DebitCardSwitchNotificationSqsSubscriber              *gencfg.SqsSubscriber
	_OnboardingStageUpdateSqsSubscriber                    *gencfg.SqsSubscriber
	_CreditCardTransactionsSqsSubscriber                   *gencfg.SqsSubscriber
	_CreditCardRequestStageUpdateSqsSubscriber             *gencfg.SqsSubscriber
	_CreditCardBillGenerationEventSqsSubscriber            *gencfg.SqsSubscriber
	_RewardsRewardUnlockerSqsSubscriber                    *gencfg.SqsSubscriber
	_RewardsRewardExpirySqsSubscriber                      *gencfg.SqsSubscriber
	_MerchantPiUpdateAffectedEntitiesEventSqsSubscriber    *gencfg.SqsSubscriber
	_RewardClawbackEventCollectorSqsSubscriber             *gencfg.SqsSubscriber
	_RewardClawbackProcessingSqsSubscriber                 *gencfg.SqsSubscriber
	_RewardsProcessingDelaySqsSubscriber                   *gencfg.SqsSubscriber
	_LuckyDrawWinningProcessingSqsSubscriber               *gencfg.SqsSubscriber
	_RewardStatusUpdateSqsSubscriber                       *gencfg.SqsSubscriber
	_ClawbackEventCollectorSqsSubscriber                   *gencfg.SqsSubscriber
	_RewardsNotificationEventsSqsSubscriber                *gencfg.SqsSubscriber
	_CreditReportDownloadEventSubscriber                   *gencfg.SqsSubscriber
	_InvestmentEventSubscriber                             *gencfg.SqsSubscriber
	_RewardsOfferRedemptionStatusUpdateEventSubscriber     *gencfg.SqsSubscriber
	_Flags                                                 *Flags
	_RewardsPayoutConfig                                   *RewardsPayoutConfig
	_SlackAlertingConfig                                   *SlackAlertingConfig
	_ReferralRewardsCappingAndPeriod                       *ReferralRewardsCappingAndPeriod
	_CacheConfig                                           *CacheConfig
	_EnableTxnAggregatesWithPinot                          *gencfg.FeatureReleaseConfig
	_DataCollectorConfig                                   *DataCollectorConfig
	_RewardsProjectionsGenerationEventSqsSubscriber        *gencfg.SqsSubscriber
	_RewardsProjectionUpdateEventSqsSubscriber             *gencfg.SqsSubscriber
	_TieringPeriodicRewardEventSqsSubscriber               *gencfg.SqsSubscriber
	_TieringTierUpdateEventSqsSubscriber                   *gencfg.SqsSubscriber
	_EpfPassbookImportEventSqsSubscriber                   *gencfg.SqsSubscriber
	_CaAccountDataSyncEventSqsSubscriber                   *gencfg.SqsSubscriber
	_RewardsActorNudgeStatusUpdateEventSqsSubscriber       *gencfg.SqsSubscriber
	_BulkClaimRewardsEventSqsSubscriber                    *gencfg.SqsSubscriber
	_TieringPeriodicRewardConfig                           *TieringPeriodicRewardConfig
	_RewardsFactGeneratorConfig                            *RewardsFactGeneratorConfig
	_RewardsAccountStatusUpdateEventSqsSubscriber          *gencfg.SqsSubscriber
	_RewardsTerminalStatusUpdateEventSqsSubscriber         *gencfg.SqsSubscriber
	_ProjectionEventForPinotSqsSubscriber                  *gencfg.SqsSubscriber
	_WatsonMeta                                            *WatsonMeta
	_RewardOfferGenAIReviewConfig                          *RewardOfferGenAIReviewConfig
	_OrdersRewardConfig                                    *OrdersRewardConfig
	_VendorRewardFulfillmentEventSqsSubscriber             *gencfg.SqsSubscriber
	_Application                                           *config.Application
	_Server                                                *config.Server
	_Logging                                               *cfg.Logging
	_RewardsDb                                             *cfg.DB
	_RedisOptions                                          *cfg.RedisOptions
	_QuestRedisOptions                                     *cfg.RedisOptions
	_Secrets                                               *cfg.Secrets
	_Aws                                                   *config.Aws
	_PinotConfig                                           *pinot.Config
	_DataCollectorEventSqsPublisher                        *cfg.SqsPublisher
	_RewardsSqsPublisher                                   *cfg.SqsPublisher
	_ExtraInterestSdBonusPayoutSqsPublisher                *cfg.SqsPublisher
	_RewardsRewardUnlockerSqsPublisher                     *cfg.SqsPublisher
	_RewardsRewardUnlockerSqsCustomDelayPublisher          *cfg.SqsCustomDelayQueuePublisher
	_RewardsRewardExpirySqsPublisher                       *cfg.SqsPublisher
	_RewardsRewardExpirySqsCustomDelayPublisher            *cfg.SqsCustomDelayQueuePublisher
	_RewardClawbackEventCollectedDataPublisher             *cfg.SqsPublisher
	_RewardsClawbackEventCollectedDataCustomDelayPublisher *cfg.SqsCustomDelayQueuePublisher
	_RewardClawbackProcessingSqsPublisher                  *cfg.SqsPublisher
	_RewardsProcessingSqsCustomDelayPublisher              *cfg.SqsCustomDelayQueuePublisher
	_LuckyDrawWinningProcessingSqsPublisher                *cfg.SqsPublisher
	_RewardGenerationEventSnsPublisher                     *cfg.SnsPublisher
	_RewardStatusUpdateEventSnsPublisher                   *cfg.SnsPublisher
	_RewardsManualGiveawayEventSqsPublisher                *cfg.SqsPublisher
	_RewardsClaimRewardEventSqsCustomDelayPublisher        *cfg.SqsCustomDelayQueuePublisher
	_DataCollectorEventSqsCustomDelayPublisher             *cfg.SqsCustomDelayQueuePublisher
	_RewardsNotificationEventSqsCustomDelayPublisher       *cfg.SqsCustomDelayQueuePublisher
	_RewardsNotificationParams                             *config.RewardsNotificationParams
	_Sentry                                                *cfg.Sentry
	_RudderStack                                           *cfg.RudderStackBroker
	_Jobs                                                  *config.Jobs
	_RewardsCampaignCommConfig                             *config.RewardsCampaignCommConfig
	_RewardsCountCheckForConsistencyWithGeneration         *config.RewardsCountCheckForConsistencyWithGeneration
	_RetryDelayForProcessingInProcessRewards               time.Duration
	_RetryDelayForProcessingInProgressRewardClawbacks      time.Duration
	_Tracing                                               *cfg.Tracing
	_Profiling                                             *cfg.Profiling
	_CreditCardRewardsConfig                               *config.CreditCardRewardsConfig
	_MinTimeDelayBetweenNowAndUnlockDateForLockingReward   time.Duration
	_RewardsProjectionUpdateEventSqsPublisher              *cfg.SqsPublisher
	_RewardsProjectionsGenerationEventSqsPublisher         *cfg.SqsPublisher
	_BulkClaimRewardsEventSqsPublisher                     *cfg.SqsPublisher
	_RewardsOrderUpdateEventQueueSqsPublisher              *cfg.SqsPublisher
	_RewardsCreditCardTxnEventQueueSqsPublisher            *cfg.SqsPublisher
	_RewardsCreditCardBillingEventQueueSqsPublisher        *cfg.SqsPublisher
	_TerminalPinotRewardProducer                           *cfg.KafkaStreamProducer
	_PinotProjectionProducer                               *cfg.KafkaStreamProducer
	_ProjectionEventSnsPublisher                           *cfg.SnsPublisher
	_NonTerminalPinotRewardProducer                        *cfg.KafkaStreamProducer
	_MerchantIdsOfRewardsInterest                          []string
}

func (obj *Config) BulkClaimRewardsRedisLockDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._BulkClaimRewardsRedisLockDuration))
}
func (obj *Config) RewardsPeriodicCappingsPerOfferType() *syncmap.Map[string, *RewardsPeriodicCappingInfo] {
	return obj._RewardsPeriodicCappingsPerOfferType
}

// FeedbackEngineCustomEvaluatorRules contains the custom evaluator rules for feedback engine.
func (obj *Config) FeedbackEngineCustomEvaluatorRules() *syncmap.Map[string, *FeedbackEngineCustomEvaluatorRule] {
	return obj._FeedbackEngineCustomEvaluatorRules
}
func (obj *Config) RewardsBucketName() string {
	obj._RewardsBucketNameMutex.RLock()
	defer obj._RewardsBucketNameMutex.RUnlock()
	return obj._RewardsBucketName
}

// denotes the actor to which the chequebook request charge related txns are credited,
// this is useful in identifying the chequebook charges related txns.
func (obj *Config) ChequebookChargeTxnBeneficiaryActorId() string {
	obj._ChequebookChargeTxnBeneficiaryActorIdMutex.RLock()
	defer obj._ChequebookChargeTxnBeneficiaryActorIdMutex.RUnlock()
	return obj._ChequebookChargeTxnBeneficiaryActorId
}
func (obj *Config) QuestSdk() *genconfig.Config {
	return obj._QuestSdk
}
func (obj *Config) DataCollectorEventsSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._DataCollectorEventsSqsSubscriber
}
func (obj *Config) ClaimRewardEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ClaimRewardEventSqsSubscriber
}
func (obj *Config) RewardsSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsSqsSubscriber
}
func (obj *Config) OrderUpdateSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._OrderUpdateSqsSubscriber
}
func (obj *Config) UserSearchSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._UserSearchSqsSubscriber
}
func (obj *Config) UserSearchV1SqsSubscriber() *gencfg.SqsSubscriber {
	return obj._UserSearchV1SqsSubscriber
}
func (obj *Config) ManualGiveawayEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ManualGiveawayEventSqsSubscriber
}
func (obj *Config) FitttExecutionUpdateSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._FitttExecutionUpdateSqsSubscriber
}
func (obj *Config) ExtraInterestSdBonusPayoutSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ExtraInterestSdBonusPayoutSqsSubscriber
}
func (obj *Config) MinBalanceSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._MinBalanceSqsSubscriber
}
func (obj *Config) CAAccountUpdateEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._CAAccountUpdateEventSqsSubscriber
}
func (obj *Config) KYCSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._KYCSqsSubscriber
}
func (obj *Config) FitttSportsEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._FitttSportsEventSqsSubscriber
}
func (obj *Config) SavingsAccountStateUpdateSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._SavingsAccountStateUpdateSqsSubscriber
}
func (obj *Config) SalaryDetectionSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._SalaryDetectionSqsSubscriber
}
func (obj *Config) SalaryProgramStatusUpdateSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._SalaryProgramStatusUpdateSqsSubscriber
}
func (obj *Config) DebitCardSwitchNotificationSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._DebitCardSwitchNotificationSqsSubscriber
}
func (obj *Config) OnboardingStageUpdateSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._OnboardingStageUpdateSqsSubscriber
}
func (obj *Config) CreditCardTransactionsSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._CreditCardTransactionsSqsSubscriber
}
func (obj *Config) CreditCardRequestStageUpdateSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._CreditCardRequestStageUpdateSqsSubscriber
}
func (obj *Config) CreditCardBillGenerationEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._CreditCardBillGenerationEventSqsSubscriber
}
func (obj *Config) RewardsRewardUnlockerSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsRewardUnlockerSqsSubscriber
}
func (obj *Config) RewardsRewardExpirySqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsRewardExpirySqsSubscriber
}
func (obj *Config) MerchantPiUpdateAffectedEntitiesEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._MerchantPiUpdateAffectedEntitiesEventSqsSubscriber
}
func (obj *Config) RewardClawbackEventCollectorSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardClawbackEventCollectorSqsSubscriber
}
func (obj *Config) RewardClawbackProcessingSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardClawbackProcessingSqsSubscriber
}
func (obj *Config) RewardsProcessingDelaySqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsProcessingDelaySqsSubscriber
}
func (obj *Config) LuckyDrawWinningProcessingSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._LuckyDrawWinningProcessingSqsSubscriber
}
func (obj *Config) RewardStatusUpdateSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardStatusUpdateSqsSubscriber
}
func (obj *Config) ClawbackEventCollectorSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ClawbackEventCollectorSqsSubscriber
}
func (obj *Config) RewardsNotificationEventsSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsNotificationEventsSqsSubscriber
}
func (obj *Config) CreditReportDownloadEventSubscriber() *gencfg.SqsSubscriber {
	return obj._CreditReportDownloadEventSubscriber
}
func (obj *Config) InvestmentEventSubscriber() *gencfg.SqsSubscriber {
	return obj._InvestmentEventSubscriber
}
func (obj *Config) RewardsOfferRedemptionStatusUpdateEventSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsOfferRedemptionStatusUpdateEventSubscriber
}
func (obj *Config) Flags() *Flags {
	return obj._Flags
}
func (obj *Config) RewardsPayoutConfig() *RewardsPayoutConfig {
	return obj._RewardsPayoutConfig
}
func (obj *Config) SlackAlertingConfig() *SlackAlertingConfig {
	return obj._SlackAlertingConfig
}
func (obj *Config) ReferralRewardsCappingAndPeriod() *ReferralRewardsCappingAndPeriod {
	return obj._ReferralRewardsCappingAndPeriod
}
func (obj *Config) CacheConfig() *CacheConfig {
	return obj._CacheConfig
}
func (obj *Config) EnableTxnAggregatesWithPinot() *gencfg.FeatureReleaseConfig {
	return obj._EnableTxnAggregatesWithPinot
}
func (obj *Config) DataCollectorConfig() *DataCollectorConfig {
	return obj._DataCollectorConfig
}
func (obj *Config) RewardsProjectionsGenerationEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsProjectionsGenerationEventSqsSubscriber
}
func (obj *Config) RewardsProjectionUpdateEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsProjectionUpdateEventSqsSubscriber
}
func (obj *Config) TieringPeriodicRewardEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._TieringPeriodicRewardEventSqsSubscriber
}
func (obj *Config) TieringTierUpdateEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._TieringTierUpdateEventSqsSubscriber
}
func (obj *Config) EpfPassbookImportEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._EpfPassbookImportEventSqsSubscriber
}
func (obj *Config) CaAccountDataSyncEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._CaAccountDataSyncEventSqsSubscriber
}
func (obj *Config) RewardsActorNudgeStatusUpdateEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsActorNudgeStatusUpdateEventSqsSubscriber
}
func (obj *Config) BulkClaimRewardsEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._BulkClaimRewardsEventSqsSubscriber
}
func (obj *Config) TieringPeriodicRewardConfig() *TieringPeriodicRewardConfig {
	return obj._TieringPeriodicRewardConfig
}
func (obj *Config) RewardsFactGeneratorConfig() *RewardsFactGeneratorConfig {
	return obj._RewardsFactGeneratorConfig
}
func (obj *Config) RewardsAccountStatusUpdateEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsAccountStatusUpdateEventSqsSubscriber
}
func (obj *Config) RewardsTerminalStatusUpdateEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._RewardsTerminalStatusUpdateEventSqsSubscriber
}
func (obj *Config) ProjectionEventForPinotSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ProjectionEventForPinotSqsSubscriber
}
func (obj *Config) WatsonMeta() *WatsonMeta {
	return obj._WatsonMeta
}
func (obj *Config) RewardOfferGenAIReviewConfig() *RewardOfferGenAIReviewConfig {
	return obj._RewardOfferGenAIReviewConfig
}
func (obj *Config) OrdersRewardConfig() *OrdersRewardConfig {
	return obj._OrdersRewardConfig
}
func (obj *Config) VendorRewardFulfillmentEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._VendorRewardFulfillmentEventSqsSubscriber
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) RewardsDb() *cfg.DB {
	return obj._RewardsDb
}
func (obj *Config) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}
func (obj *Config) QuestRedisOptions() *cfg.RedisOptions {
	return obj._QuestRedisOptions
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) Aws() *config.Aws {
	return obj._Aws
}
func (obj *Config) PinotConfig() *pinot.Config {
	return obj._PinotConfig
}
func (obj *Config) DataCollectorEventSqsPublisher() *cfg.SqsPublisher {
	return obj._DataCollectorEventSqsPublisher
}
func (obj *Config) RewardsSqsPublisher() *cfg.SqsPublisher {
	return obj._RewardsSqsPublisher
}
func (obj *Config) ExtraInterestSdBonusPayoutSqsPublisher() *cfg.SqsPublisher {
	return obj._ExtraInterestSdBonusPayoutSqsPublisher
}
func (obj *Config) RewardsRewardUnlockerSqsPublisher() *cfg.SqsPublisher {
	return obj._RewardsRewardUnlockerSqsPublisher
}
func (obj *Config) RewardsRewardUnlockerSqsCustomDelayPublisher() *cfg.SqsCustomDelayQueuePublisher {
	return obj._RewardsRewardUnlockerSqsCustomDelayPublisher
}
func (obj *Config) RewardsRewardExpirySqsPublisher() *cfg.SqsPublisher {
	return obj._RewardsRewardExpirySqsPublisher
}
func (obj *Config) RewardsRewardExpirySqsCustomDelayPublisher() *cfg.SqsCustomDelayQueuePublisher {
	return obj._RewardsRewardExpirySqsCustomDelayPublisher
}
func (obj *Config) RewardClawbackEventCollectedDataPublisher() *cfg.SqsPublisher {
	return obj._RewardClawbackEventCollectedDataPublisher
}
func (obj *Config) RewardsClawbackEventCollectedDataCustomDelayPublisher() *cfg.SqsCustomDelayQueuePublisher {
	return obj._RewardsClawbackEventCollectedDataCustomDelayPublisher
}
func (obj *Config) RewardClawbackProcessingSqsPublisher() *cfg.SqsPublisher {
	return obj._RewardClawbackProcessingSqsPublisher
}
func (obj *Config) RewardsProcessingSqsCustomDelayPublisher() *cfg.SqsCustomDelayQueuePublisher {
	return obj._RewardsProcessingSqsCustomDelayPublisher
}
func (obj *Config) LuckyDrawWinningProcessingSqsPublisher() *cfg.SqsPublisher {
	return obj._LuckyDrawWinningProcessingSqsPublisher
}
func (obj *Config) RewardGenerationEventSnsPublisher() *cfg.SnsPublisher {
	return obj._RewardGenerationEventSnsPublisher
}
func (obj *Config) RewardStatusUpdateEventSnsPublisher() *cfg.SnsPublisher {
	return obj._RewardStatusUpdateEventSnsPublisher
}
func (obj *Config) RewardsManualGiveawayEventSqsPublisher() *cfg.SqsPublisher {
	return obj._RewardsManualGiveawayEventSqsPublisher
}
func (obj *Config) RewardsClaimRewardEventSqsCustomDelayPublisher() *cfg.SqsCustomDelayQueuePublisher {
	return obj._RewardsClaimRewardEventSqsCustomDelayPublisher
}
func (obj *Config) DataCollectorEventSqsCustomDelayPublisher() *cfg.SqsCustomDelayQueuePublisher {
	return obj._DataCollectorEventSqsCustomDelayPublisher
}
func (obj *Config) RewardsNotificationEventSqsCustomDelayPublisher() *cfg.SqsCustomDelayQueuePublisher {
	return obj._RewardsNotificationEventSqsCustomDelayPublisher
}
func (obj *Config) RewardsNotificationParams() *config.RewardsNotificationParams {
	return obj._RewardsNotificationParams
}
func (obj *Config) Sentry() *cfg.Sentry {
	return obj._Sentry
}
func (obj *Config) RudderStack() *cfg.RudderStackBroker {
	return obj._RudderStack
}
func (obj *Config) Jobs() *config.Jobs {
	return obj._Jobs
}
func (obj *Config) RewardsCampaignCommConfig() *config.RewardsCampaignCommConfig {
	return obj._RewardsCampaignCommConfig
}
func (obj *Config) RewardsCountCheckForConsistencyWithGeneration() *config.RewardsCountCheckForConsistencyWithGeneration {
	return obj._RewardsCountCheckForConsistencyWithGeneration
}
func (obj *Config) RetryDelayForProcessingInProcessRewards() time.Duration {
	return obj._RetryDelayForProcessingInProcessRewards
}
func (obj *Config) RetryDelayForProcessingInProgressRewardClawbacks() time.Duration {
	return obj._RetryDelayForProcessingInProgressRewardClawbacks
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) CreditCardRewardsConfig() *config.CreditCardRewardsConfig {
	return obj._CreditCardRewardsConfig
}
func (obj *Config) MinTimeDelayBetweenNowAndUnlockDateForLockingReward() time.Duration {
	return obj._MinTimeDelayBetweenNowAndUnlockDateForLockingReward
}
func (obj *Config) RewardsProjectionUpdateEventSqsPublisher() *cfg.SqsPublisher {
	return obj._RewardsProjectionUpdateEventSqsPublisher
}
func (obj *Config) RewardsProjectionsGenerationEventSqsPublisher() *cfg.SqsPublisher {
	return obj._RewardsProjectionsGenerationEventSqsPublisher
}
func (obj *Config) BulkClaimRewardsEventSqsPublisher() *cfg.SqsPublisher {
	return obj._BulkClaimRewardsEventSqsPublisher
}
func (obj *Config) RewardsOrderUpdateEventQueueSqsPublisher() *cfg.SqsPublisher {
	return obj._RewardsOrderUpdateEventQueueSqsPublisher
}
func (obj *Config) RewardsCreditCardTxnEventQueueSqsPublisher() *cfg.SqsPublisher {
	return obj._RewardsCreditCardTxnEventQueueSqsPublisher
}
func (obj *Config) RewardsCreditCardBillingEventQueueSqsPublisher() *cfg.SqsPublisher {
	return obj._RewardsCreditCardBillingEventQueueSqsPublisher
}
func (obj *Config) TerminalPinotRewardProducer() *cfg.KafkaStreamProducer {
	return obj._TerminalPinotRewardProducer
}
func (obj *Config) PinotProjectionProducer() *cfg.KafkaStreamProducer {
	return obj._PinotProjectionProducer
}
func (obj *Config) ProjectionEventSnsPublisher() *cfg.SnsPublisher {
	return obj._ProjectionEventSnsPublisher
}
func (obj *Config) NonTerminalPinotRewardProducer() *cfg.KafkaStreamProducer {
	return obj._NonTerminalPinotRewardProducer
}
func (obj *Config) MerchantIdsOfRewardsInterest() []string {
	return obj._MerchantIdsOfRewardsInterest
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// A flag to determine if the debug message in Status is to be trimmed
	_TrimDebugMessageFromStatus uint32
	// If true rewards feature would only be exposed to internal users.
	// If false it would be available for all the users.
	_EnableRewardsForInternalUsersOnly uint32
	// flag to decide whether notifications are supposed to be sent or not when rewards processing is successful.
	// note: notifications triggered via external domain services are not controlled, i.e. orders for cash
	_DisableRewardsProcessedNotification uint32
	// flag to decide whether we should generate new rewards in `LOCKED` state.
	// if set to false, rewards will be generated in `CREATED` state only
	_EnableGenerationOfRewardsInLockedState uint32
	// flag to decide whether to validate the reward offer constraints expression while creating/updating reward offers
	_ValidateRewardOfferConstraintsExpressionOnCreation uint32
	// flag to decide whether email based notifications are supposed to be disabled or not
	// putting this flag here instead of NotificationTemplateParams because map-of-map is not supported in confgen(RewardProcessingNotificationParams)
	_DisableEmailBasedNotifications uint32
	// Flag to control whether TPAP txns should be considered for rewards processing or not.
	// https://github.com/epiFi/tickets/issues/56397
	// This flag, along with controlling the processing of TPAP txns, will also control whether Fi-lite users are allowed the evaluation of Orders for rewards processing or not.
	// This is done to have a single kill-switch while enabling/disabling the TPAP orders processing.
	_EnableTPAPOrdersForRewardsProcessing uint32
	_EnableRewardClawback                 bool
}

// A flag to determine if the debug message in Status is to be trimmed
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	if atomic.LoadUint32(&obj._TrimDebugMessageFromStatus) == 0 {
		return false
	} else {
		return true
	}
}

// If true rewards feature would only be exposed to internal users.
// If false it would be available for all the users.
func (obj *Flags) EnableRewardsForInternalUsersOnly() bool {
	if atomic.LoadUint32(&obj._EnableRewardsForInternalUsersOnly) == 0 {
		return false
	} else {
		return true
	}
}

// flag to decide whether notifications are supposed to be sent or not when rewards processing is successful.
// note: notifications triggered via external domain services are not controlled, i.e. orders for cash
func (obj *Flags) DisableRewardsProcessedNotification() bool {
	if atomic.LoadUint32(&obj._DisableRewardsProcessedNotification) == 0 {
		return false
	} else {
		return true
	}
}

// flag to decide whether we should generate new rewards in `LOCKED` state.
// if set to false, rewards will be generated in `CREATED` state only
func (obj *Flags) EnableGenerationOfRewardsInLockedState() bool {
	if atomic.LoadUint32(&obj._EnableGenerationOfRewardsInLockedState) == 0 {
		return false
	} else {
		return true
	}
}

// flag to decide whether to validate the reward offer constraints expression while creating/updating reward offers
func (obj *Flags) ValidateRewardOfferConstraintsExpressionOnCreation() bool {
	if atomic.LoadUint32(&obj._ValidateRewardOfferConstraintsExpressionOnCreation) == 0 {
		return false
	} else {
		return true
	}
}

// flag to decide whether email based notifications are supposed to be disabled or not
// putting this flag here instead of NotificationTemplateParams because map-of-map is not supported in confgen(RewardProcessingNotificationParams)
func (obj *Flags) DisableEmailBasedNotifications() bool {
	if atomic.LoadUint32(&obj._DisableEmailBasedNotifications) == 0 {
		return false
	} else {
		return true
	}
}

// Flag to control whether TPAP txns should be considered for rewards processing or not.
// https://github.com/epiFi/tickets/issues/56397
// This flag, along with controlling the processing of TPAP txns, will also control whether Fi-lite users are allowed the evaluation of Orders for rewards processing or not.
// This is done to have a single kill-switch while enabling/disabling the TPAP orders processing.
func (obj *Flags) EnableTPAPOrdersForRewardsProcessing() bool {
	if atomic.LoadUint32(&obj._EnableTPAPOrdersForRewardsProcessing) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) EnableRewardClawback() bool {
	return obj._EnableRewardClawback
}

type RewardsPayoutConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnableRewardsPayoutViaCelestial     uint32
	_EpifiBusinessAccountPiIds           roarray.ROArray[string]
	_EpifiBusinessAccountPiIdsMutex      *sync.RWMutex
	_EpifiBusinessAccountActorId         string
	_TimeToWaitBeforeCheckingOrderStatus int
}

func (obj *RewardsPayoutConfig) EnableRewardsPayoutViaCelestial() bool {
	if atomic.LoadUint32(&obj._EnableRewardsPayoutViaCelestial) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RewardsPayoutConfig) EpifiBusinessAccountPiIds() roarray.ROArray[string] {
	obj._EpifiBusinessAccountPiIdsMutex.RLock()
	defer obj._EpifiBusinessAccountPiIdsMutex.RUnlock()
	return obj._EpifiBusinessAccountPiIds
}
func (obj *RewardsPayoutConfig) EpifiBusinessAccountActorId() string {
	return obj._EpifiBusinessAccountActorId
}
func (obj *RewardsPayoutConfig) TimeToWaitBeforeCheckingOrderStatus() int {
	return obj._TimeToWaitBeforeCheckingOrderStatus
}

type SlackAlertingConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// slack messages/notifications allowed or not
	_IsEnabled uint32
	// channel-id for the updates related to entities need to be sent
	_EntityUpdatesChannelId      string
	_EntityUpdatesChannelIdMutex *sync.RWMutex
}

// slack messages/notifications allowed or not
func (obj *SlackAlertingConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// channel-id for the updates related to entities need to be sent
func (obj *SlackAlertingConfig) EntityUpdatesChannelId() string {
	obj._EntityUpdatesChannelIdMutex.RLock()
	defer obj._EntityUpdatesChannelIdMutex.RUnlock()
	return obj._EntityUpdatesChannelId
}

type ReferralRewardsCappingAndPeriod struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	_RewardsCap    uint32
	_Active        uint32
	_CappingPeriod int64
}

func (obj *ReferralRewardsCappingAndPeriod) rewardsCap() uint32 {
	return uint32(atomic.LoadUint32(&obj._RewardsCap))
}
func (obj *ReferralRewardsCappingAndPeriod) RewardsCap(ctx context.Context) uint32 {
	defVal := obj.rewardsCap()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "RewardsCap"}, defVal)
	val, ok := res.(int64)
	if ok {
		return uint32(val)
	}
	return defVal
}

func (obj *ReferralRewardsCappingAndPeriod) Active() bool {
	if atomic.LoadUint32(&obj._Active) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ReferralRewardsCappingAndPeriod) CappingPeriod() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CappingPeriod))
}

type RewardsPeriodicCappingInfo struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_RewardsCap      uint32
	_CappingPeriod   int64
	_Active          uint32
	_OfferType       string
	_CappingDuration string
}

func (obj *RewardsPeriodicCappingInfo) RewardsCap() uint32 {
	return uint32(atomic.LoadUint32(&obj._RewardsCap))
}
func (obj *RewardsPeriodicCappingInfo) CappingPeriod() int {
	return int(atomic.LoadInt64(&obj._CappingPeriod))
}
func (obj *RewardsPeriodicCappingInfo) Active() bool {
	if atomic.LoadUint32(&obj._Active) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RewardsPeriodicCappingInfo) OfferType() string {
	return obj._OfferType
}
func (obj *RewardsPeriodicCappingInfo) CappingDuration() string {
	return obj._CappingDuration
}

type CacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_UserReferralInfoLRUCacheConfig *UserReferralInfoLRUCacheConfig
}

func (obj *CacheConfig) UserReferralInfoLRUCacheConfig() *UserReferralInfoLRUCacheConfig {
	return obj._UserReferralInfoLRUCacheConfig
}

type UserReferralInfoLRUCacheConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// size of the LRU cache, i.e. max number of keys can be present in the cache at a time
	// post reaching this limit, the least recently used key will be evicted
	_Size int64
	_Ttl  int64
}

// size of the LRU cache, i.e. max number of keys can be present in the cache at a time
// post reaching this limit, the least recently used key will be evicted
func (obj *UserReferralInfoLRUCacheConfig) Size() int {
	return int(atomic.LoadInt64(&obj._Size))
}
func (obj *UserReferralInfoLRUCacheConfig) Ttl() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._Ttl))
}

type DataCollectorConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_WaitTimeForFetchingCreditCardTxnCategories int64
}

func (obj *DataCollectorConfig) WaitTimeForFetchingCreditCardTxnCategories() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._WaitTimeForFetchingCreditCardTxnCategories))
}

type TieringPeriodicRewardConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_RewardTypeToDailyUpperCapMap *syncmap.Map[string, float32]
}

func (obj *TieringPeriodicRewardConfig) RewardTypeToDailyUpperCapMap() *syncmap.Map[string, float32] {
	return obj._RewardTypeToDailyUpperCapMap
}

type RewardsFactGeneratorConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn int64
}

func (obj *RewardsFactGeneratorConfig) MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn))
}

type WatsonMeta struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled                                 uint32
	_IsErrorActivityEnabled                    uint32
	_IsUserActivityEnabled                     uint32
	_DefaultIssueCategoryIdForRewards          string
	_DefaultIssueCategoryIdForRewardsMutex     *sync.RWMutex
	_DefaultIssueCategoryIdForProjections      string
	_DefaultIssueCategoryIdForProjectionsMutex *sync.RWMutex
}

func (obj *WatsonMeta) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *WatsonMeta) IsErrorActivityEnabled() bool {
	if atomic.LoadUint32(&obj._IsErrorActivityEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *WatsonMeta) IsUserActivityEnabled() bool {
	if atomic.LoadUint32(&obj._IsUserActivityEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *WatsonMeta) DefaultIssueCategoryIdForRewards() string {
	obj._DefaultIssueCategoryIdForRewardsMutex.RLock()
	defer obj._DefaultIssueCategoryIdForRewardsMutex.RUnlock()
	return obj._DefaultIssueCategoryIdForRewards
}
func (obj *WatsonMeta) DefaultIssueCategoryIdForProjections() string {
	obj._DefaultIssueCategoryIdForProjectionsMutex.RLock()
	defer obj._DefaultIssueCategoryIdForProjectionsMutex.RUnlock()
	return obj._DefaultIssueCategoryIdForProjections
}

type RewardOfferGenAIReviewConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_BedrockModelId       string
	_BedrockModelIdMutex  *sync.RWMutex
	_Prompt               string
	_PromptMutex          *sync.RWMutex
	_SystemPrompt         string
	_SystemPromptMutex    *sync.RWMutex
	_SampleOfferPath      string
	_SampleOfferPathMutex *sync.RWMutex
}

func (obj *RewardOfferGenAIReviewConfig) BedrockModelId() string {
	obj._BedrockModelIdMutex.RLock()
	defer obj._BedrockModelIdMutex.RUnlock()
	return obj._BedrockModelId
}
func (obj *RewardOfferGenAIReviewConfig) Prompt() string {
	obj._PromptMutex.RLock()
	defer obj._PromptMutex.RUnlock()
	return obj._Prompt
}
func (obj *RewardOfferGenAIReviewConfig) SystemPrompt() string {
	obj._SystemPromptMutex.RLock()
	defer obj._SystemPromptMutex.RUnlock()
	return obj._SystemPrompt
}
func (obj *RewardOfferGenAIReviewConfig) SampleOfferPath() string {
	obj._SampleOfferPathMutex.RLock()
	defer obj._SampleOfferPathMutex.RUnlock()
	return obj._SampleOfferPath
}

type FeedbackEngineCustomEvaluatorRule struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MinClaimCount              int64
	_MinCashbackEarned          int64
	_MinFiCoinsEarned           int64
	_LastClaimThresholdDuration int64
}

func (obj *FeedbackEngineCustomEvaluatorRule) MinClaimCount() int {
	return int(atomic.LoadInt64(&obj._MinClaimCount))
}
func (obj *FeedbackEngineCustomEvaluatorRule) MinCashbackEarned() int {
	return int(atomic.LoadInt64(&obj._MinCashbackEarned))
}
func (obj *FeedbackEngineCustomEvaluatorRule) MinFiCoinsEarned() int {
	return int(atomic.LoadInt64(&obj._MinFiCoinsEarned))
}
func (obj *FeedbackEngineCustomEvaluatorRule) LastClaimThresholdDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._LastClaimThresholdDuration))
}

type OrdersRewardConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// selected VPAs file s3 path, required for rewarding on txns done to selected vpas
	_SelectedVpaS3Path      string
	_SelectedVpaS3PathMutex *sync.RWMutex
}

// selected VPAs file s3 path, required for rewarding on txns done to selected vpas
func (obj *OrdersRewardConfig) SelectedVpaS3Path() string {
	obj._SelectedVpaS3PathMutex.RLock()
	defer obj._SelectedVpaS3PathMutex.RUnlock()
	return obj._SelectedVpaS3Path
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["bulkclaimrewardsredislockduration"] = _obj.SetBulkClaimRewardsRedisLockDuration

	_obj._RewardsPeriodicCappingsPerOfferType = &syncmap.Map[string, *RewardsPeriodicCappingInfo]{}
	_setters["rewardsperiodiccappingsperoffertype"] = _obj.SetRewardsPeriodicCappingsPerOfferType

	_obj._FeedbackEngineCustomEvaluatorRules = &syncmap.Map[string, *FeedbackEngineCustomEvaluatorRule]{}
	_setters["feedbackenginecustomevaluatorrules"] = _obj.SetFeedbackEngineCustomEvaluatorRules
	_setters["rewardsbucketname"] = _obj.SetRewardsBucketName
	_obj._RewardsBucketNameMutex = &sync.RWMutex{}
	_setters["chequebookchargetxnbeneficiaryactorid"] = _obj.SetChequebookChargeTxnBeneficiaryActorId
	_obj._ChequebookChargeTxnBeneficiaryActorIdMutex = &sync.RWMutex{}
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_DataCollectorEventsSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DataCollectorEventsSqsSubscriber = _DataCollectorEventsSqsSubscriber
	helper.AddFieldSetters("datacollectoreventssqssubscriber", _fieldSetters, _setters)
	_ClaimRewardEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ClaimRewardEventSqsSubscriber = _ClaimRewardEventSqsSubscriber
	helper.AddFieldSetters("claimrewardeventsqssubscriber", _fieldSetters, _setters)
	_RewardsSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsSqsSubscriber = _RewardsSqsSubscriber
	helper.AddFieldSetters("rewardssqssubscriber", _fieldSetters, _setters)
	_OrderUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderUpdateSqsSubscriber = _OrderUpdateSqsSubscriber
	helper.AddFieldSetters("orderupdatesqssubscriber", _fieldSetters, _setters)
	_UserSearchSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UserSearchSqsSubscriber = _UserSearchSqsSubscriber
	helper.AddFieldSetters("usersearchsqssubscriber", _fieldSetters, _setters)
	_UserSearchV1SqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UserSearchV1SqsSubscriber = _UserSearchV1SqsSubscriber
	helper.AddFieldSetters("usersearchv1sqssubscriber", _fieldSetters, _setters)
	_ManualGiveawayEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ManualGiveawayEventSqsSubscriber = _ManualGiveawayEventSqsSubscriber
	helper.AddFieldSetters("manualgiveawayeventsqssubscriber", _fieldSetters, _setters)
	_FitttExecutionUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FitttExecutionUpdateSqsSubscriber = _FitttExecutionUpdateSqsSubscriber
	helper.AddFieldSetters("fitttexecutionupdatesqssubscriber", _fieldSetters, _setters)
	_ExtraInterestSdBonusPayoutSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ExtraInterestSdBonusPayoutSqsSubscriber = _ExtraInterestSdBonusPayoutSqsSubscriber
	helper.AddFieldSetters("extrainterestsdbonuspayoutsqssubscriber", _fieldSetters, _setters)
	_MinBalanceSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._MinBalanceSqsSubscriber = _MinBalanceSqsSubscriber
	helper.AddFieldSetters("minbalancesqssubscriber", _fieldSetters, _setters)
	_CAAccountUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CAAccountUpdateEventSqsSubscriber = _CAAccountUpdateEventSqsSubscriber
	helper.AddFieldSetters("caaccountupdateeventsqssubscriber", _fieldSetters, _setters)
	_KYCSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._KYCSqsSubscriber = _KYCSqsSubscriber
	helper.AddFieldSetters("kycsqssubscriber", _fieldSetters, _setters)
	_FitttSportsEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FitttSportsEventSqsSubscriber = _FitttSportsEventSqsSubscriber
	helper.AddFieldSetters("fitttsportseventsqssubscriber", _fieldSetters, _setters)
	_SavingsAccountStateUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SavingsAccountStateUpdateSqsSubscriber = _SavingsAccountStateUpdateSqsSubscriber
	helper.AddFieldSetters("savingsaccountstateupdatesqssubscriber", _fieldSetters, _setters)
	_SalaryDetectionSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SalaryDetectionSqsSubscriber = _SalaryDetectionSqsSubscriber
	helper.AddFieldSetters("salarydetectionsqssubscriber", _fieldSetters, _setters)
	_SalaryProgramStatusUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SalaryProgramStatusUpdateSqsSubscriber = _SalaryProgramStatusUpdateSqsSubscriber
	helper.AddFieldSetters("salaryprogramstatusupdatesqssubscriber", _fieldSetters, _setters)
	_DebitCardSwitchNotificationSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DebitCardSwitchNotificationSqsSubscriber = _DebitCardSwitchNotificationSqsSubscriber
	helper.AddFieldSetters("debitcardswitchnotificationsqssubscriber", _fieldSetters, _setters)
	_OnboardingStageUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OnboardingStageUpdateSqsSubscriber = _OnboardingStageUpdateSqsSubscriber
	helper.AddFieldSetters("onboardingstageupdatesqssubscriber", _fieldSetters, _setters)
	_CreditCardTransactionsSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditCardTransactionsSqsSubscriber = _CreditCardTransactionsSqsSubscriber
	helper.AddFieldSetters("creditcardtransactionssqssubscriber", _fieldSetters, _setters)
	_CreditCardRequestStageUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditCardRequestStageUpdateSqsSubscriber = _CreditCardRequestStageUpdateSqsSubscriber
	helper.AddFieldSetters("creditcardrequeststageupdatesqssubscriber", _fieldSetters, _setters)
	_CreditCardBillGenerationEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditCardBillGenerationEventSqsSubscriber = _CreditCardBillGenerationEventSqsSubscriber
	helper.AddFieldSetters("creditcardbillgenerationeventsqssubscriber", _fieldSetters, _setters)
	_RewardsRewardUnlockerSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsRewardUnlockerSqsSubscriber = _RewardsRewardUnlockerSqsSubscriber
	helper.AddFieldSetters("rewardsrewardunlockersqssubscriber", _fieldSetters, _setters)
	_RewardsRewardExpirySqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsRewardExpirySqsSubscriber = _RewardsRewardExpirySqsSubscriber
	helper.AddFieldSetters("rewardsrewardexpirysqssubscriber", _fieldSetters, _setters)
	_MerchantPiUpdateAffectedEntitiesEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._MerchantPiUpdateAffectedEntitiesEventSqsSubscriber = _MerchantPiUpdateAffectedEntitiesEventSqsSubscriber
	helper.AddFieldSetters("merchantpiupdateaffectedentitieseventsqssubscriber", _fieldSetters, _setters)
	_RewardClawbackEventCollectorSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardClawbackEventCollectorSqsSubscriber = _RewardClawbackEventCollectorSqsSubscriber
	helper.AddFieldSetters("rewardclawbackeventcollectorsqssubscriber", _fieldSetters, _setters)
	_RewardClawbackProcessingSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardClawbackProcessingSqsSubscriber = _RewardClawbackProcessingSqsSubscriber
	helper.AddFieldSetters("rewardclawbackprocessingsqssubscriber", _fieldSetters, _setters)
	_RewardsProcessingDelaySqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsProcessingDelaySqsSubscriber = _RewardsProcessingDelaySqsSubscriber
	helper.AddFieldSetters("rewardsprocessingdelaysqssubscriber", _fieldSetters, _setters)
	_LuckyDrawWinningProcessingSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._LuckyDrawWinningProcessingSqsSubscriber = _LuckyDrawWinningProcessingSqsSubscriber
	helper.AddFieldSetters("luckydrawwinningprocessingsqssubscriber", _fieldSetters, _setters)
	_RewardStatusUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardStatusUpdateSqsSubscriber = _RewardStatusUpdateSqsSubscriber
	helper.AddFieldSetters("rewardstatusupdatesqssubscriber", _fieldSetters, _setters)
	_ClawbackEventCollectorSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ClawbackEventCollectorSqsSubscriber = _ClawbackEventCollectorSqsSubscriber
	helper.AddFieldSetters("clawbackeventcollectorsqssubscriber", _fieldSetters, _setters)
	_RewardsNotificationEventsSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsNotificationEventsSqsSubscriber = _RewardsNotificationEventsSqsSubscriber
	helper.AddFieldSetters("rewardsnotificationeventssqssubscriber", _fieldSetters, _setters)
	_CreditReportDownloadEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditReportDownloadEventSubscriber = _CreditReportDownloadEventSubscriber
	helper.AddFieldSetters("creditreportdownloadeventsubscriber", _fieldSetters, _setters)
	_InvestmentEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._InvestmentEventSubscriber = _InvestmentEventSubscriber
	helper.AddFieldSetters("investmenteventsubscriber", _fieldSetters, _setters)
	_RewardsOfferRedemptionStatusUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsOfferRedemptionStatusUpdateEventSubscriber = _RewardsOfferRedemptionStatusUpdateEventSubscriber
	helper.AddFieldSetters("rewardsofferredemptionstatusupdateeventsubscriber", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_RewardsPayoutConfig, _fieldSetters := NewRewardsPayoutConfig()
	_obj._RewardsPayoutConfig = _RewardsPayoutConfig
	helper.AddFieldSetters("rewardspayoutconfig", _fieldSetters, _setters)
	_SlackAlertingConfig, _fieldSetters := NewSlackAlertingConfig()
	_obj._SlackAlertingConfig = _SlackAlertingConfig
	helper.AddFieldSetters("slackalertingconfig", _fieldSetters, _setters)
	_ReferralRewardsCappingAndPeriod, _fieldSetters := NewReferralRewardsCappingAndPeriod()
	_obj._ReferralRewardsCappingAndPeriod = _ReferralRewardsCappingAndPeriod
	helper.AddFieldSetters("referralrewardscappingandperiod", _fieldSetters, _setters)
	_CacheConfig, _fieldSetters := NewCacheConfig()
	_obj._CacheConfig = _CacheConfig
	helper.AddFieldSetters("cacheconfig", _fieldSetters, _setters)
	_EnableTxnAggregatesWithPinot, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._EnableTxnAggregatesWithPinot = _EnableTxnAggregatesWithPinot
	helper.AddFieldSetters("enabletxnaggregateswithpinot", _fieldSetters, _setters)
	_DataCollectorConfig, _fieldSetters := NewDataCollectorConfig()
	_obj._DataCollectorConfig = _DataCollectorConfig
	helper.AddFieldSetters("datacollectorconfig", _fieldSetters, _setters)
	_RewardsProjectionsGenerationEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsProjectionsGenerationEventSqsSubscriber = _RewardsProjectionsGenerationEventSqsSubscriber
	helper.AddFieldSetters("rewardsprojectionsgenerationeventsqssubscriber", _fieldSetters, _setters)
	_RewardsProjectionUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsProjectionUpdateEventSqsSubscriber = _RewardsProjectionUpdateEventSqsSubscriber
	helper.AddFieldSetters("rewardsprojectionupdateeventsqssubscriber", _fieldSetters, _setters)
	_TieringPeriodicRewardEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TieringPeriodicRewardEventSqsSubscriber = _TieringPeriodicRewardEventSqsSubscriber
	helper.AddFieldSetters("tieringperiodicrewardeventsqssubscriber", _fieldSetters, _setters)
	_TieringTierUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TieringTierUpdateEventSqsSubscriber = _TieringTierUpdateEventSqsSubscriber
	helper.AddFieldSetters("tieringtierupdateeventsqssubscriber", _fieldSetters, _setters)
	_EpfPassbookImportEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EpfPassbookImportEventSqsSubscriber = _EpfPassbookImportEventSqsSubscriber
	helper.AddFieldSetters("epfpassbookimporteventsqssubscriber", _fieldSetters, _setters)
	_CaAccountDataSyncEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CaAccountDataSyncEventSqsSubscriber = _CaAccountDataSyncEventSqsSubscriber
	helper.AddFieldSetters("caaccountdatasynceventsqssubscriber", _fieldSetters, _setters)
	_RewardsActorNudgeStatusUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsActorNudgeStatusUpdateEventSqsSubscriber = _RewardsActorNudgeStatusUpdateEventSqsSubscriber
	helper.AddFieldSetters("rewardsactornudgestatusupdateeventsqssubscriber", _fieldSetters, _setters)
	_BulkClaimRewardsEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._BulkClaimRewardsEventSqsSubscriber = _BulkClaimRewardsEventSqsSubscriber
	helper.AddFieldSetters("bulkclaimrewardseventsqssubscriber", _fieldSetters, _setters)
	_TieringPeriodicRewardConfig, _fieldSetters := NewTieringPeriodicRewardConfig()
	_obj._TieringPeriodicRewardConfig = _TieringPeriodicRewardConfig
	helper.AddFieldSetters("tieringperiodicrewardconfig", _fieldSetters, _setters)
	_RewardsFactGeneratorConfig, _fieldSetters := NewRewardsFactGeneratorConfig()
	_obj._RewardsFactGeneratorConfig = _RewardsFactGeneratorConfig
	helper.AddFieldSetters("rewardsfactgeneratorconfig", _fieldSetters, _setters)
	_RewardsAccountStatusUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsAccountStatusUpdateEventSqsSubscriber = _RewardsAccountStatusUpdateEventSqsSubscriber
	helper.AddFieldSetters("rewardsaccountstatusupdateeventsqssubscriber", _fieldSetters, _setters)
	_RewardsTerminalStatusUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsTerminalStatusUpdateEventSqsSubscriber = _RewardsTerminalStatusUpdateEventSqsSubscriber
	helper.AddFieldSetters("rewardsterminalstatusupdateeventsqssubscriber", _fieldSetters, _setters)
	_ProjectionEventForPinotSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProjectionEventForPinotSqsSubscriber = _ProjectionEventForPinotSqsSubscriber
	helper.AddFieldSetters("projectioneventforpinotsqssubscriber", _fieldSetters, _setters)
	_WatsonMeta, _fieldSetters := NewWatsonMeta()
	_obj._WatsonMeta = _WatsonMeta
	helper.AddFieldSetters("watsonmeta", _fieldSetters, _setters)
	_RewardOfferGenAIReviewConfig, _fieldSetters := NewRewardOfferGenAIReviewConfig()
	_obj._RewardOfferGenAIReviewConfig = _RewardOfferGenAIReviewConfig
	helper.AddFieldSetters("rewardoffergenaireviewconfig", _fieldSetters, _setters)
	_OrdersRewardConfig, _fieldSetters := NewOrdersRewardConfig()
	_obj._OrdersRewardConfig = _OrdersRewardConfig
	helper.AddFieldSetters("ordersrewardconfig", _fieldSetters, _setters)
	_VendorRewardFulfillmentEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._VendorRewardFulfillmentEventSqsSubscriber = _VendorRewardFulfillmentEventSqsSubscriber
	helper.AddFieldSetters("vendorrewardfulfillmenteventsqssubscriber", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["bulkclaimrewardsredislockduration"] = _obj.SetBulkClaimRewardsRedisLockDuration

	_obj._RewardsPeriodicCappingsPerOfferType = &syncmap.Map[string, *RewardsPeriodicCappingInfo]{}
	_setters["rewardsperiodiccappingsperoffertype"] = _obj.SetRewardsPeriodicCappingsPerOfferType

	_obj._FeedbackEngineCustomEvaluatorRules = &syncmap.Map[string, *FeedbackEngineCustomEvaluatorRule]{}
	_setters["feedbackenginecustomevaluatorrules"] = _obj.SetFeedbackEngineCustomEvaluatorRules
	_setters["rewardsbucketname"] = _obj.SetRewardsBucketName
	_obj._RewardsBucketNameMutex = &sync.RWMutex{}
	_setters["chequebookchargetxnbeneficiaryactorid"] = _obj.SetChequebookChargeTxnBeneficiaryActorId
	_obj._ChequebookChargeTxnBeneficiaryActorIdMutex = &sync.RWMutex{}
	_QuestSdk, _fieldSetters := genconfig.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_DataCollectorEventsSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DataCollectorEventsSqsSubscriber = _DataCollectorEventsSqsSubscriber
	helper.AddFieldSetters("datacollectoreventssqssubscriber", _fieldSetters, _setters)
	_ClaimRewardEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ClaimRewardEventSqsSubscriber = _ClaimRewardEventSqsSubscriber
	helper.AddFieldSetters("claimrewardeventsqssubscriber", _fieldSetters, _setters)
	_RewardsSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsSqsSubscriber = _RewardsSqsSubscriber
	helper.AddFieldSetters("rewardssqssubscriber", _fieldSetters, _setters)
	_OrderUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderUpdateSqsSubscriber = _OrderUpdateSqsSubscriber
	helper.AddFieldSetters("orderupdatesqssubscriber", _fieldSetters, _setters)
	_UserSearchSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UserSearchSqsSubscriber = _UserSearchSqsSubscriber
	helper.AddFieldSetters("usersearchsqssubscriber", _fieldSetters, _setters)
	_UserSearchV1SqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._UserSearchV1SqsSubscriber = _UserSearchV1SqsSubscriber
	helper.AddFieldSetters("usersearchv1sqssubscriber", _fieldSetters, _setters)
	_ManualGiveawayEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ManualGiveawayEventSqsSubscriber = _ManualGiveawayEventSqsSubscriber
	helper.AddFieldSetters("manualgiveawayeventsqssubscriber", _fieldSetters, _setters)
	_FitttExecutionUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FitttExecutionUpdateSqsSubscriber = _FitttExecutionUpdateSqsSubscriber
	helper.AddFieldSetters("fitttexecutionupdatesqssubscriber", _fieldSetters, _setters)
	_ExtraInterestSdBonusPayoutSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ExtraInterestSdBonusPayoutSqsSubscriber = _ExtraInterestSdBonusPayoutSqsSubscriber
	helper.AddFieldSetters("extrainterestsdbonuspayoutsqssubscriber", _fieldSetters, _setters)
	_MinBalanceSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._MinBalanceSqsSubscriber = _MinBalanceSqsSubscriber
	helper.AddFieldSetters("minbalancesqssubscriber", _fieldSetters, _setters)
	_CAAccountUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CAAccountUpdateEventSqsSubscriber = _CAAccountUpdateEventSqsSubscriber
	helper.AddFieldSetters("caaccountupdateeventsqssubscriber", _fieldSetters, _setters)
	_KYCSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._KYCSqsSubscriber = _KYCSqsSubscriber
	helper.AddFieldSetters("kycsqssubscriber", _fieldSetters, _setters)
	_FitttSportsEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FitttSportsEventSqsSubscriber = _FitttSportsEventSqsSubscriber
	helper.AddFieldSetters("fitttsportseventsqssubscriber", _fieldSetters, _setters)
	_SavingsAccountStateUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SavingsAccountStateUpdateSqsSubscriber = _SavingsAccountStateUpdateSqsSubscriber
	helper.AddFieldSetters("savingsaccountstateupdatesqssubscriber", _fieldSetters, _setters)
	_SalaryDetectionSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SalaryDetectionSqsSubscriber = _SalaryDetectionSqsSubscriber
	helper.AddFieldSetters("salarydetectionsqssubscriber", _fieldSetters, _setters)
	_SalaryProgramStatusUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._SalaryProgramStatusUpdateSqsSubscriber = _SalaryProgramStatusUpdateSqsSubscriber
	helper.AddFieldSetters("salaryprogramstatusupdatesqssubscriber", _fieldSetters, _setters)
	_DebitCardSwitchNotificationSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._DebitCardSwitchNotificationSqsSubscriber = _DebitCardSwitchNotificationSqsSubscriber
	helper.AddFieldSetters("debitcardswitchnotificationsqssubscriber", _fieldSetters, _setters)
	_OnboardingStageUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OnboardingStageUpdateSqsSubscriber = _OnboardingStageUpdateSqsSubscriber
	helper.AddFieldSetters("onboardingstageupdatesqssubscriber", _fieldSetters, _setters)
	_CreditCardTransactionsSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditCardTransactionsSqsSubscriber = _CreditCardTransactionsSqsSubscriber
	helper.AddFieldSetters("creditcardtransactionssqssubscriber", _fieldSetters, _setters)
	_CreditCardRequestStageUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditCardRequestStageUpdateSqsSubscriber = _CreditCardRequestStageUpdateSqsSubscriber
	helper.AddFieldSetters("creditcardrequeststageupdatesqssubscriber", _fieldSetters, _setters)
	_CreditCardBillGenerationEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditCardBillGenerationEventSqsSubscriber = _CreditCardBillGenerationEventSqsSubscriber
	helper.AddFieldSetters("creditcardbillgenerationeventsqssubscriber", _fieldSetters, _setters)
	_RewardsRewardUnlockerSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsRewardUnlockerSqsSubscriber = _RewardsRewardUnlockerSqsSubscriber
	helper.AddFieldSetters("rewardsrewardunlockersqssubscriber", _fieldSetters, _setters)
	_RewardsRewardExpirySqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsRewardExpirySqsSubscriber = _RewardsRewardExpirySqsSubscriber
	helper.AddFieldSetters("rewardsrewardexpirysqssubscriber", _fieldSetters, _setters)
	_MerchantPiUpdateAffectedEntitiesEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._MerchantPiUpdateAffectedEntitiesEventSqsSubscriber = _MerchantPiUpdateAffectedEntitiesEventSqsSubscriber
	helper.AddFieldSetters("merchantpiupdateaffectedentitieseventsqssubscriber", _fieldSetters, _setters)
	_RewardClawbackEventCollectorSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardClawbackEventCollectorSqsSubscriber = _RewardClawbackEventCollectorSqsSubscriber
	helper.AddFieldSetters("rewardclawbackeventcollectorsqssubscriber", _fieldSetters, _setters)
	_RewardClawbackProcessingSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardClawbackProcessingSqsSubscriber = _RewardClawbackProcessingSqsSubscriber
	helper.AddFieldSetters("rewardclawbackprocessingsqssubscriber", _fieldSetters, _setters)
	_RewardsProcessingDelaySqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsProcessingDelaySqsSubscriber = _RewardsProcessingDelaySqsSubscriber
	helper.AddFieldSetters("rewardsprocessingdelaysqssubscriber", _fieldSetters, _setters)
	_LuckyDrawWinningProcessingSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._LuckyDrawWinningProcessingSqsSubscriber = _LuckyDrawWinningProcessingSqsSubscriber
	helper.AddFieldSetters("luckydrawwinningprocessingsqssubscriber", _fieldSetters, _setters)
	_RewardStatusUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardStatusUpdateSqsSubscriber = _RewardStatusUpdateSqsSubscriber
	helper.AddFieldSetters("rewardstatusupdatesqssubscriber", _fieldSetters, _setters)
	_ClawbackEventCollectorSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ClawbackEventCollectorSqsSubscriber = _ClawbackEventCollectorSqsSubscriber
	helper.AddFieldSetters("clawbackeventcollectorsqssubscriber", _fieldSetters, _setters)
	_RewardsNotificationEventsSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsNotificationEventsSqsSubscriber = _RewardsNotificationEventsSqsSubscriber
	helper.AddFieldSetters("rewardsnotificationeventssqssubscriber", _fieldSetters, _setters)
	_CreditReportDownloadEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CreditReportDownloadEventSubscriber = _CreditReportDownloadEventSubscriber
	helper.AddFieldSetters("creditreportdownloadeventsubscriber", _fieldSetters, _setters)
	_InvestmentEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._InvestmentEventSubscriber = _InvestmentEventSubscriber
	helper.AddFieldSetters("investmenteventsubscriber", _fieldSetters, _setters)
	_RewardsOfferRedemptionStatusUpdateEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsOfferRedemptionStatusUpdateEventSubscriber = _RewardsOfferRedemptionStatusUpdateEventSubscriber
	helper.AddFieldSetters("rewardsofferredemptionstatusupdateeventsubscriber", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_RewardsPayoutConfig, _fieldSetters := NewRewardsPayoutConfig()
	_obj._RewardsPayoutConfig = _RewardsPayoutConfig
	helper.AddFieldSetters("rewardspayoutconfig", _fieldSetters, _setters)
	_SlackAlertingConfig, _fieldSetters := NewSlackAlertingConfig()
	_obj._SlackAlertingConfig = _SlackAlertingConfig
	helper.AddFieldSetters("slackalertingconfig", _fieldSetters, _setters)
	_ReferralRewardsCappingAndPeriod, _fieldSetters := NewReferralRewardsCappingAndPeriodWithQuest(questFieldPath + "/" + "ReferralRewardsCappingAndPeriod")
	_obj._ReferralRewardsCappingAndPeriod = _ReferralRewardsCappingAndPeriod
	helper.AddFieldSetters("referralrewardscappingandperiod", _fieldSetters, _setters)
	_CacheConfig, _fieldSetters := NewCacheConfig()
	_obj._CacheConfig = _CacheConfig
	helper.AddFieldSetters("cacheconfig", _fieldSetters, _setters)
	_EnableTxnAggregatesWithPinot, _fieldSetters := gencfg.NewFeatureReleaseConfig()
	_obj._EnableTxnAggregatesWithPinot = _EnableTxnAggregatesWithPinot
	helper.AddFieldSetters("enabletxnaggregateswithpinot", _fieldSetters, _setters)
	_DataCollectorConfig, _fieldSetters := NewDataCollectorConfig()
	_obj._DataCollectorConfig = _DataCollectorConfig
	helper.AddFieldSetters("datacollectorconfig", _fieldSetters, _setters)
	_RewardsProjectionsGenerationEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsProjectionsGenerationEventSqsSubscriber = _RewardsProjectionsGenerationEventSqsSubscriber
	helper.AddFieldSetters("rewardsprojectionsgenerationeventsqssubscriber", _fieldSetters, _setters)
	_RewardsProjectionUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsProjectionUpdateEventSqsSubscriber = _RewardsProjectionUpdateEventSqsSubscriber
	helper.AddFieldSetters("rewardsprojectionupdateeventsqssubscriber", _fieldSetters, _setters)
	_TieringPeriodicRewardEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TieringPeriodicRewardEventSqsSubscriber = _TieringPeriodicRewardEventSqsSubscriber
	helper.AddFieldSetters("tieringperiodicrewardeventsqssubscriber", _fieldSetters, _setters)
	_TieringTierUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._TieringTierUpdateEventSqsSubscriber = _TieringTierUpdateEventSqsSubscriber
	helper.AddFieldSetters("tieringtierupdateeventsqssubscriber", _fieldSetters, _setters)
	_EpfPassbookImportEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._EpfPassbookImportEventSqsSubscriber = _EpfPassbookImportEventSqsSubscriber
	helper.AddFieldSetters("epfpassbookimporteventsqssubscriber", _fieldSetters, _setters)
	_CaAccountDataSyncEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._CaAccountDataSyncEventSqsSubscriber = _CaAccountDataSyncEventSqsSubscriber
	helper.AddFieldSetters("caaccountdatasynceventsqssubscriber", _fieldSetters, _setters)
	_RewardsActorNudgeStatusUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsActorNudgeStatusUpdateEventSqsSubscriber = _RewardsActorNudgeStatusUpdateEventSqsSubscriber
	helper.AddFieldSetters("rewardsactornudgestatusupdateeventsqssubscriber", _fieldSetters, _setters)
	_BulkClaimRewardsEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._BulkClaimRewardsEventSqsSubscriber = _BulkClaimRewardsEventSqsSubscriber
	helper.AddFieldSetters("bulkclaimrewardseventsqssubscriber", _fieldSetters, _setters)
	_TieringPeriodicRewardConfig, _fieldSetters := NewTieringPeriodicRewardConfig()
	_obj._TieringPeriodicRewardConfig = _TieringPeriodicRewardConfig
	helper.AddFieldSetters("tieringperiodicrewardconfig", _fieldSetters, _setters)
	_RewardsFactGeneratorConfig, _fieldSetters := NewRewardsFactGeneratorConfig()
	_obj._RewardsFactGeneratorConfig = _RewardsFactGeneratorConfig
	helper.AddFieldSetters("rewardsfactgeneratorconfig", _fieldSetters, _setters)
	_RewardsAccountStatusUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsAccountStatusUpdateEventSqsSubscriber = _RewardsAccountStatusUpdateEventSqsSubscriber
	helper.AddFieldSetters("rewardsaccountstatusupdateeventsqssubscriber", _fieldSetters, _setters)
	_RewardsTerminalStatusUpdateEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RewardsTerminalStatusUpdateEventSqsSubscriber = _RewardsTerminalStatusUpdateEventSqsSubscriber
	helper.AddFieldSetters("rewardsterminalstatusupdateeventsqssubscriber", _fieldSetters, _setters)
	_ProjectionEventForPinotSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProjectionEventForPinotSqsSubscriber = _ProjectionEventForPinotSqsSubscriber
	helper.AddFieldSetters("projectioneventforpinotsqssubscriber", _fieldSetters, _setters)
	_WatsonMeta, _fieldSetters := NewWatsonMeta()
	_obj._WatsonMeta = _WatsonMeta
	helper.AddFieldSetters("watsonmeta", _fieldSetters, _setters)
	_RewardOfferGenAIReviewConfig, _fieldSetters := NewRewardOfferGenAIReviewConfig()
	_obj._RewardOfferGenAIReviewConfig = _RewardOfferGenAIReviewConfig
	helper.AddFieldSetters("rewardoffergenaireviewconfig", _fieldSetters, _setters)
	_OrdersRewardConfig, _fieldSetters := NewOrdersRewardConfig()
	_obj._OrdersRewardConfig = _OrdersRewardConfig
	helper.AddFieldSetters("ordersrewardconfig", _fieldSetters, _setters)
	_VendorRewardFulfillmentEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._VendorRewardFulfillmentEventSqsSubscriber = _VendorRewardFulfillmentEventSqsSubscriber
	helper.AddFieldSetters("vendorrewardfulfillmenteventsqssubscriber", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init(questFieldPath string) {
	newObj, _ := NewConfig()
	*obj = *newObj
}
func (obj *Config) InitWithQuest(questFieldPath string) {
	newObj, _ := NewConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
	obj._ReferralRewardsCappingAndPeriod.SetQuestSDK(questSdk)
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	childVars, childVarsErr = obj._ReferralRewardsCappingAndPeriod.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}

	for _, v := range childVars {
		v.Area = "Rewards" // from quest tag annotation for the component field
		vars = append(vars, v)
	}
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "bulkclaimrewardsredislockduration":
		return obj.SetBulkClaimRewardsRedisLockDuration(v.BulkClaimRewardsRedisLockDuration, true, nil)
	case "rewardsperiodiccappingsperoffertype":
		return obj.SetRewardsPeriodicCappingsPerOfferType(v.RewardsPeriodicCappingsPerOfferType, true, path)
	case "feedbackenginecustomevaluatorrules":
		return obj.SetFeedbackEngineCustomEvaluatorRules(v.FeedbackEngineCustomEvaluatorRules, true, path)
	case "rewardsbucketname":
		return obj.SetRewardsBucketName(v.RewardsBucketName, true, nil)
	case "chequebookchargetxnbeneficiaryactorid":
		return obj.SetChequebookChargeTxnBeneficiaryActorId(v.ChequebookChargeTxnBeneficiaryActorId, true, nil)
	case "questsdk":
		return obj._QuestSdk.Set(v.QuestSdk, true, path)
	case "datacollectoreventssqssubscriber":
		return obj._DataCollectorEventsSqsSubscriber.Set(v.DataCollectorEventsSqsSubscriber, true, path)
	case "claimrewardeventsqssubscriber":
		return obj._ClaimRewardEventSqsSubscriber.Set(v.ClaimRewardEventSqsSubscriber, true, path)
	case "rewardssqssubscriber":
		return obj._RewardsSqsSubscriber.Set(v.RewardsSqsSubscriber, true, path)
	case "orderupdatesqssubscriber":
		return obj._OrderUpdateSqsSubscriber.Set(v.OrderUpdateSqsSubscriber, true, path)
	case "usersearchsqssubscriber":
		return obj._UserSearchSqsSubscriber.Set(v.UserSearchSqsSubscriber, true, path)
	case "usersearchv1sqssubscriber":
		return obj._UserSearchV1SqsSubscriber.Set(v.UserSearchV1SqsSubscriber, true, path)
	case "manualgiveawayeventsqssubscriber":
		return obj._ManualGiveawayEventSqsSubscriber.Set(v.ManualGiveawayEventSqsSubscriber, true, path)
	case "fitttexecutionupdatesqssubscriber":
		return obj._FitttExecutionUpdateSqsSubscriber.Set(v.FitttExecutionUpdateSqsSubscriber, true, path)
	case "extrainterestsdbonuspayoutsqssubscriber":
		return obj._ExtraInterestSdBonusPayoutSqsSubscriber.Set(v.ExtraInterestSdBonusPayoutSqsSubscriber, true, path)
	case "minbalancesqssubscriber":
		return obj._MinBalanceSqsSubscriber.Set(v.MinBalanceSqsSubscriber, true, path)
	case "caaccountupdateeventsqssubscriber":
		return obj._CAAccountUpdateEventSqsSubscriber.Set(v.CAAccountUpdateEventSqsSubscriber, true, path)
	case "kycsqssubscriber":
		return obj._KYCSqsSubscriber.Set(v.KYCSqsSubscriber, true, path)
	case "fitttsportseventsqssubscriber":
		return obj._FitttSportsEventSqsSubscriber.Set(v.FitttSportsEventSqsSubscriber, true, path)
	case "savingsaccountstateupdatesqssubscriber":
		return obj._SavingsAccountStateUpdateSqsSubscriber.Set(v.SavingsAccountStateUpdateSqsSubscriber, true, path)
	case "salarydetectionsqssubscriber":
		return obj._SalaryDetectionSqsSubscriber.Set(v.SalaryDetectionSqsSubscriber, true, path)
	case "salaryprogramstatusupdatesqssubscriber":
		return obj._SalaryProgramStatusUpdateSqsSubscriber.Set(v.SalaryProgramStatusUpdateSqsSubscriber, true, path)
	case "debitcardswitchnotificationsqssubscriber":
		return obj._DebitCardSwitchNotificationSqsSubscriber.Set(v.DebitCardSwitchNotificationSqsSubscriber, true, path)
	case "onboardingstageupdatesqssubscriber":
		return obj._OnboardingStageUpdateSqsSubscriber.Set(v.OnboardingStageUpdateSqsSubscriber, true, path)
	case "creditcardtransactionssqssubscriber":
		return obj._CreditCardTransactionsSqsSubscriber.Set(v.CreditCardTransactionsSqsSubscriber, true, path)
	case "creditcardrequeststageupdatesqssubscriber":
		return obj._CreditCardRequestStageUpdateSqsSubscriber.Set(v.CreditCardRequestStageUpdateSqsSubscriber, true, path)
	case "creditcardbillgenerationeventsqssubscriber":
		return obj._CreditCardBillGenerationEventSqsSubscriber.Set(v.CreditCardBillGenerationEventSqsSubscriber, true, path)
	case "rewardsrewardunlockersqssubscriber":
		return obj._RewardsRewardUnlockerSqsSubscriber.Set(v.RewardsRewardUnlockerSqsSubscriber, true, path)
	case "rewardsrewardexpirysqssubscriber":
		return obj._RewardsRewardExpirySqsSubscriber.Set(v.RewardsRewardExpirySqsSubscriber, true, path)
	case "merchantpiupdateaffectedentitieseventsqssubscriber":
		return obj._MerchantPiUpdateAffectedEntitiesEventSqsSubscriber.Set(v.MerchantPiUpdateAffectedEntitiesEventSqsSubscriber, true, path)
	case "rewardclawbackeventcollectorsqssubscriber":
		return obj._RewardClawbackEventCollectorSqsSubscriber.Set(v.RewardClawbackEventCollectorSqsSubscriber, true, path)
	case "rewardclawbackprocessingsqssubscriber":
		return obj._RewardClawbackProcessingSqsSubscriber.Set(v.RewardClawbackProcessingSqsSubscriber, true, path)
	case "rewardsprocessingdelaysqssubscriber":
		return obj._RewardsProcessingDelaySqsSubscriber.Set(v.RewardsProcessingDelaySqsSubscriber, true, path)
	case "luckydrawwinningprocessingsqssubscriber":
		return obj._LuckyDrawWinningProcessingSqsSubscriber.Set(v.LuckyDrawWinningProcessingSqsSubscriber, true, path)
	case "rewardstatusupdatesqssubscriber":
		return obj._RewardStatusUpdateSqsSubscriber.Set(v.RewardStatusUpdateSqsSubscriber, true, path)
	case "clawbackeventcollectorsqssubscriber":
		return obj._ClawbackEventCollectorSqsSubscriber.Set(v.ClawbackEventCollectorSqsSubscriber, true, path)
	case "rewardsnotificationeventssqssubscriber":
		return obj._RewardsNotificationEventsSqsSubscriber.Set(v.RewardsNotificationEventsSqsSubscriber, true, path)
	case "creditreportdownloadeventsubscriber":
		return obj._CreditReportDownloadEventSubscriber.Set(v.CreditReportDownloadEventSubscriber, true, path)
	case "investmenteventsubscriber":
		return obj._InvestmentEventSubscriber.Set(v.InvestmentEventSubscriber, true, path)
	case "rewardsofferredemptionstatusupdateeventsubscriber":
		return obj._RewardsOfferRedemptionStatusUpdateEventSubscriber.Set(v.RewardsOfferRedemptionStatusUpdateEventSubscriber, true, path)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "rewardspayoutconfig":
		return obj._RewardsPayoutConfig.Set(v.RewardsPayoutConfig, true, path)
	case "slackalertingconfig":
		return obj._SlackAlertingConfig.Set(v.SlackAlertingConfig, true, path)
	case "referralrewardscappingandperiod":
		return obj._ReferralRewardsCappingAndPeriod.Set(v.ReferralRewardsCappingAndPeriod, true, path)
	case "cacheconfig":
		return obj._CacheConfig.Set(v.CacheConfig, true, path)
	case "enabletxnaggregateswithpinot":
		return obj._EnableTxnAggregatesWithPinot.Set(v.EnableTxnAggregatesWithPinot, true, path)
	case "datacollectorconfig":
		return obj._DataCollectorConfig.Set(v.DataCollectorConfig, true, path)
	case "rewardsprojectionsgenerationeventsqssubscriber":
		return obj._RewardsProjectionsGenerationEventSqsSubscriber.Set(v.RewardsProjectionsGenerationEventSqsSubscriber, true, path)
	case "rewardsprojectionupdateeventsqssubscriber":
		return obj._RewardsProjectionUpdateEventSqsSubscriber.Set(v.RewardsProjectionUpdateEventSqsSubscriber, true, path)
	case "tieringperiodicrewardeventsqssubscriber":
		return obj._TieringPeriodicRewardEventSqsSubscriber.Set(v.TieringPeriodicRewardEventSqsSubscriber, true, path)
	case "tieringtierupdateeventsqssubscriber":
		return obj._TieringTierUpdateEventSqsSubscriber.Set(v.TieringTierUpdateEventSqsSubscriber, true, path)
	case "epfpassbookimporteventsqssubscriber":
		return obj._EpfPassbookImportEventSqsSubscriber.Set(v.EpfPassbookImportEventSqsSubscriber, true, path)
	case "caaccountdatasynceventsqssubscriber":
		return obj._CaAccountDataSyncEventSqsSubscriber.Set(v.CaAccountDataSyncEventSqsSubscriber, true, path)
	case "rewardsactornudgestatusupdateeventsqssubscriber":
		return obj._RewardsActorNudgeStatusUpdateEventSqsSubscriber.Set(v.RewardsActorNudgeStatusUpdateEventSqsSubscriber, true, path)
	case "bulkclaimrewardseventsqssubscriber":
		return obj._BulkClaimRewardsEventSqsSubscriber.Set(v.BulkClaimRewardsEventSqsSubscriber, true, path)
	case "tieringperiodicrewardconfig":
		return obj._TieringPeriodicRewardConfig.Set(v.TieringPeriodicRewardConfig, true, path)
	case "rewardsfactgeneratorconfig":
		return obj._RewardsFactGeneratorConfig.Set(v.RewardsFactGeneratorConfig, true, path)
	case "rewardsaccountstatusupdateeventsqssubscriber":
		return obj._RewardsAccountStatusUpdateEventSqsSubscriber.Set(v.RewardsAccountStatusUpdateEventSqsSubscriber, true, path)
	case "rewardsterminalstatusupdateeventsqssubscriber":
		return obj._RewardsTerminalStatusUpdateEventSqsSubscriber.Set(v.RewardsTerminalStatusUpdateEventSqsSubscriber, true, path)
	case "projectioneventforpinotsqssubscriber":
		return obj._ProjectionEventForPinotSqsSubscriber.Set(v.ProjectionEventForPinotSqsSubscriber, true, path)
	case "watsonmeta":
		return obj._WatsonMeta.Set(v.WatsonMeta, true, path)
	case "rewardoffergenaireviewconfig":
		return obj._RewardOfferGenAIReviewConfig.Set(v.RewardOfferGenAIReviewConfig, true, path)
	case "ordersrewardconfig":
		return obj._OrdersRewardConfig.Set(v.OrdersRewardConfig, true, path)
	case "vendorrewardfulfillmenteventsqssubscriber":
		return obj._VendorRewardFulfillmentEventSqsSubscriber.Set(v.VendorRewardFulfillmentEventSqsSubscriber, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetBulkClaimRewardsRedisLockDuration(v.BulkClaimRewardsRedisLockDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRewardsPeriodicCappingsPerOfferType(v.RewardsPeriodicCappingsPerOfferType, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetFeedbackEngineCustomEvaluatorRules(v.FeedbackEngineCustomEvaluatorRules, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetRewardsBucketName(v.RewardsBucketName, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetChequebookChargeTxnBeneficiaryActorId(v.ChequebookChargeTxnBeneficiaryActorId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._QuestSdk.Set(v.QuestSdk, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DataCollectorEventsSqsSubscriber.Set(v.DataCollectorEventsSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ClaimRewardEventSqsSubscriber.Set(v.ClaimRewardEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsSqsSubscriber.Set(v.RewardsSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderUpdateSqsSubscriber.Set(v.OrderUpdateSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UserSearchSqsSubscriber.Set(v.UserSearchSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UserSearchV1SqsSubscriber.Set(v.UserSearchV1SqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ManualGiveawayEventSqsSubscriber.Set(v.ManualGiveawayEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FitttExecutionUpdateSqsSubscriber.Set(v.FitttExecutionUpdateSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ExtraInterestSdBonusPayoutSqsSubscriber.Set(v.ExtraInterestSdBonusPayoutSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MinBalanceSqsSubscriber.Set(v.MinBalanceSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CAAccountUpdateEventSqsSubscriber.Set(v.CAAccountUpdateEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._KYCSqsSubscriber.Set(v.KYCSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FitttSportsEventSqsSubscriber.Set(v.FitttSportsEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SavingsAccountStateUpdateSqsSubscriber.Set(v.SavingsAccountStateUpdateSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SalaryDetectionSqsSubscriber.Set(v.SalaryDetectionSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SalaryProgramStatusUpdateSqsSubscriber.Set(v.SalaryProgramStatusUpdateSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DebitCardSwitchNotificationSqsSubscriber.Set(v.DebitCardSwitchNotificationSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OnboardingStageUpdateSqsSubscriber.Set(v.OnboardingStageUpdateSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditCardTransactionsSqsSubscriber.Set(v.CreditCardTransactionsSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditCardRequestStageUpdateSqsSubscriber.Set(v.CreditCardRequestStageUpdateSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditCardBillGenerationEventSqsSubscriber.Set(v.CreditCardBillGenerationEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsRewardUnlockerSqsSubscriber.Set(v.RewardsRewardUnlockerSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsRewardExpirySqsSubscriber.Set(v.RewardsRewardExpirySqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._MerchantPiUpdateAffectedEntitiesEventSqsSubscriber.Set(v.MerchantPiUpdateAffectedEntitiesEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardClawbackEventCollectorSqsSubscriber.Set(v.RewardClawbackEventCollectorSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardClawbackProcessingSqsSubscriber.Set(v.RewardClawbackProcessingSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsProcessingDelaySqsSubscriber.Set(v.RewardsProcessingDelaySqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LuckyDrawWinningProcessingSqsSubscriber.Set(v.LuckyDrawWinningProcessingSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardStatusUpdateSqsSubscriber.Set(v.RewardStatusUpdateSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ClawbackEventCollectorSqsSubscriber.Set(v.ClawbackEventCollectorSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsNotificationEventsSqsSubscriber.Set(v.RewardsNotificationEventsSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditReportDownloadEventSubscriber.Set(v.CreditReportDownloadEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InvestmentEventSubscriber.Set(v.InvestmentEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsOfferRedemptionStatusUpdateEventSubscriber.Set(v.RewardsOfferRedemptionStatusUpdateEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsPayoutConfig.Set(v.RewardsPayoutConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SlackAlertingConfig.Set(v.SlackAlertingConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ReferralRewardsCappingAndPeriod.Set(v.ReferralRewardsCappingAndPeriod, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CacheConfig.Set(v.CacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableTxnAggregatesWithPinot.Set(v.EnableTxnAggregatesWithPinot, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DataCollectorConfig.Set(v.DataCollectorConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsProjectionsGenerationEventSqsSubscriber.Set(v.RewardsProjectionsGenerationEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsProjectionUpdateEventSqsSubscriber.Set(v.RewardsProjectionUpdateEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TieringPeriodicRewardEventSqsSubscriber.Set(v.TieringPeriodicRewardEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TieringTierUpdateEventSqsSubscriber.Set(v.TieringTierUpdateEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EpfPassbookImportEventSqsSubscriber.Set(v.EpfPassbookImportEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CaAccountDataSyncEventSqsSubscriber.Set(v.CaAccountDataSyncEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsActorNudgeStatusUpdateEventSqsSubscriber.Set(v.RewardsActorNudgeStatusUpdateEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._BulkClaimRewardsEventSqsSubscriber.Set(v.BulkClaimRewardsEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TieringPeriodicRewardConfig.Set(v.TieringPeriodicRewardConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsFactGeneratorConfig.Set(v.RewardsFactGeneratorConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsAccountStatusUpdateEventSqsSubscriber.Set(v.RewardsAccountStatusUpdateEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardsTerminalStatusUpdateEventSqsSubscriber.Set(v.RewardsTerminalStatusUpdateEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProjectionEventForPinotSqsSubscriber.Set(v.ProjectionEventForPinotSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._WatsonMeta.Set(v.WatsonMeta, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RewardOfferGenAIReviewConfig.Set(v.RewardOfferGenAIReviewConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrdersRewardConfig.Set(v.OrdersRewardConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VendorRewardFulfillmentEventSqsSubscriber.Set(v.VendorRewardFulfillmentEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._RewardsDb = v.RewardsDb
	obj._RedisOptions = v.RedisOptions
	obj._QuestRedisOptions = v.QuestRedisOptions
	obj._Secrets = v.Secrets
	obj._Aws = v.Aws
	obj._PinotConfig = v.PinotConfig
	obj._DataCollectorEventSqsPublisher = v.DataCollectorEventSqsPublisher
	obj._RewardsSqsPublisher = v.RewardsSqsPublisher
	obj._ExtraInterestSdBonusPayoutSqsPublisher = v.ExtraInterestSdBonusPayoutSqsPublisher
	obj._RewardsRewardUnlockerSqsPublisher = v.RewardsRewardUnlockerSqsPublisher
	obj._RewardsRewardUnlockerSqsCustomDelayPublisher = v.RewardsRewardUnlockerSqsCustomDelayPublisher
	obj._RewardsRewardExpirySqsPublisher = v.RewardsRewardExpirySqsPublisher
	obj._RewardsRewardExpirySqsCustomDelayPublisher = v.RewardsRewardExpirySqsCustomDelayPublisher
	obj._RewardClawbackEventCollectedDataPublisher = v.RewardClawbackEventCollectedDataPublisher
	obj._RewardsClawbackEventCollectedDataCustomDelayPublisher = v.RewardsClawbackEventCollectedDataCustomDelayPublisher
	obj._RewardClawbackProcessingSqsPublisher = v.RewardClawbackProcessingSqsPublisher
	obj._RewardsProcessingSqsCustomDelayPublisher = v.RewardsProcessingSqsCustomDelayPublisher
	obj._LuckyDrawWinningProcessingSqsPublisher = v.LuckyDrawWinningProcessingSqsPublisher
	obj._RewardGenerationEventSnsPublisher = v.RewardGenerationEventSnsPublisher
	obj._RewardStatusUpdateEventSnsPublisher = v.RewardStatusUpdateEventSnsPublisher
	obj._RewardsManualGiveawayEventSqsPublisher = v.RewardsManualGiveawayEventSqsPublisher
	obj._RewardsClaimRewardEventSqsCustomDelayPublisher = v.RewardsClaimRewardEventSqsCustomDelayPublisher
	obj._DataCollectorEventSqsCustomDelayPublisher = v.DataCollectorEventSqsCustomDelayPublisher
	obj._RewardsNotificationEventSqsCustomDelayPublisher = v.RewardsNotificationEventSqsCustomDelayPublisher
	obj._RewardsNotificationParams = v.RewardsNotificationParams
	obj._Sentry = v.Sentry
	obj._RudderStack = v.RudderStack
	obj._Jobs = v.Jobs
	obj._RewardsCampaignCommConfig = v.RewardsCampaignCommConfig
	obj._RewardsCountCheckForConsistencyWithGeneration = v.RewardsCountCheckForConsistencyWithGeneration
	obj._RetryDelayForProcessingInProcessRewards = v.RetryDelayForProcessingInProcessRewards
	obj._RetryDelayForProcessingInProgressRewardClawbacks = v.RetryDelayForProcessingInProgressRewardClawbacks
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._CreditCardRewardsConfig = v.CreditCardRewardsConfig
	obj._MinTimeDelayBetweenNowAndUnlockDateForLockingReward = v.MinTimeDelayBetweenNowAndUnlockDateForLockingReward
	obj._RewardsProjectionUpdateEventSqsPublisher = v.RewardsProjectionUpdateEventSqsPublisher
	obj._RewardsProjectionsGenerationEventSqsPublisher = v.RewardsProjectionsGenerationEventSqsPublisher
	obj._BulkClaimRewardsEventSqsPublisher = v.BulkClaimRewardsEventSqsPublisher
	obj._RewardsOrderUpdateEventQueueSqsPublisher = v.RewardsOrderUpdateEventQueueSqsPublisher
	obj._RewardsCreditCardTxnEventQueueSqsPublisher = v.RewardsCreditCardTxnEventQueueSqsPublisher
	obj._RewardsCreditCardBillingEventQueueSqsPublisher = v.RewardsCreditCardBillingEventQueueSqsPublisher
	obj._TerminalPinotRewardProducer = v.TerminalPinotRewardProducer
	obj._PinotProjectionProducer = v.PinotProjectionProducer
	obj._ProjectionEventSnsPublisher = v.ProjectionEventSnsPublisher
	obj._NonTerminalPinotRewardProducer = v.NonTerminalPinotRewardProducer
	obj._MerchantIdsOfRewardsInterest = v.MerchantIdsOfRewardsInterest
	return nil
}

func (obj *Config) SetBulkClaimRewardsRedisLockDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.BulkClaimRewardsRedisLockDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._BulkClaimRewardsRedisLockDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BulkClaimRewardsRedisLockDuration")
	}
	return nil
}
func (obj *Config) SetRewardsPeriodicCappingsPerOfferType(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.RewardsPeriodicCappingInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.RewardsPeriodicCappingsPerOfferType", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._RewardsPeriodicCappingsPerOfferType, v, dynamic, path)

}
func (obj *Config) SetFeedbackEngineCustomEvaluatorRules(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.FeedbackEngineCustomEvaluatorRule)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.FeedbackEngineCustomEvaluatorRules", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._FeedbackEngineCustomEvaluatorRules, v, dynamic, path)

}
func (obj *Config) SetRewardsBucketName(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.RewardsBucketName", reflect.TypeOf(val))
	}
	obj._RewardsBucketNameMutex.Lock()
	defer obj._RewardsBucketNameMutex.Unlock()
	obj._RewardsBucketName = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "RewardsBucketName")
	}
	return nil
}
func (obj *Config) SetChequebookChargeTxnBeneficiaryActorId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.ChequebookChargeTxnBeneficiaryActorId", reflect.TypeOf(val))
	}
	obj._ChequebookChargeTxnBeneficiaryActorIdMutex.Lock()
	defer obj._ChequebookChargeTxnBeneficiaryActorIdMutex.Unlock()
	obj._ChequebookChargeTxnBeneficiaryActorId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ChequebookChargeTxnBeneficiaryActorId")
	}
	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["trimdebugmessagefromstatus"] = _obj.SetTrimDebugMessageFromStatus
	_setters["enablerewardsforinternalusersonly"] = _obj.SetEnableRewardsForInternalUsersOnly
	_setters["disablerewardsprocessednotification"] = _obj.SetDisableRewardsProcessedNotification
	_setters["enablegenerationofrewardsinlockedstate"] = _obj.SetEnableGenerationOfRewardsInLockedState
	_setters["validaterewardofferconstraintsexpressiononcreation"] = _obj.SetValidateRewardOfferConstraintsExpressionOnCreation
	_setters["disableemailbasednotifications"] = _obj.SetDisableEmailBasedNotifications
	_setters["enabletpapordersforrewardsprocessing"] = _obj.SetEnableTPAPOrdersForRewardsProcessing
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *config.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "trimdebugmessagefromstatus":
		return obj.SetTrimDebugMessageFromStatus(v.TrimDebugMessageFromStatus, true, nil)
	case "enablerewardsforinternalusersonly":
		return obj.SetEnableRewardsForInternalUsersOnly(v.EnableRewardsForInternalUsersOnly, true, nil)
	case "disablerewardsprocessednotification":
		return obj.SetDisableRewardsProcessedNotification(v.DisableRewardsProcessedNotification, true, nil)
	case "enablegenerationofrewardsinlockedstate":
		return obj.SetEnableGenerationOfRewardsInLockedState(v.EnableGenerationOfRewardsInLockedState, true, nil)
	case "validaterewardofferconstraintsexpressiononcreation":
		return obj.SetValidateRewardOfferConstraintsExpressionOnCreation(v.ValidateRewardOfferConstraintsExpressionOnCreation, true, nil)
	case "disableemailbasednotifications":
		return obj.SetDisableEmailBasedNotifications(v.DisableEmailBasedNotifications, true, nil)
	case "enabletpapordersforrewardsprocessing":
		return obj.SetEnableTPAPOrdersForRewardsProcessing(v.EnableTPAPOrdersForRewardsProcessing, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *config.Flags, dynamic bool, path []string) (err error) {

	err = obj.SetTrimDebugMessageFromStatus(v.TrimDebugMessageFromStatus, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableRewardsForInternalUsersOnly(v.EnableRewardsForInternalUsersOnly, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableRewardsProcessedNotification(v.DisableRewardsProcessedNotification, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableGenerationOfRewardsInLockedState(v.EnableGenerationOfRewardsInLockedState, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetValidateRewardOfferConstraintsExpressionOnCreation(v.ValidateRewardOfferConstraintsExpressionOnCreation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableEmailBasedNotifications(v.DisableEmailBasedNotifications, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableTPAPOrdersForRewardsProcessing(v.EnableTPAPOrdersForRewardsProcessing, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *config.Flags) error {

	obj._EnableRewardClawback = v.EnableRewardClawback
	return nil
}

func (obj *Flags) SetTrimDebugMessageFromStatus(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.TrimDebugMessageFromStatus", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._TrimDebugMessageFromStatus, 1)
	} else {
		atomic.StoreUint32(&obj._TrimDebugMessageFromStatus, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "TrimDebugMessageFromStatus")
	}
	return nil
}
func (obj *Flags) SetEnableRewardsForInternalUsersOnly(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableRewardsForInternalUsersOnly", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRewardsForInternalUsersOnly, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRewardsForInternalUsersOnly, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRewardsForInternalUsersOnly")
	}
	return nil
}
func (obj *Flags) SetDisableRewardsProcessedNotification(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.DisableRewardsProcessedNotification", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableRewardsProcessedNotification, 1)
	} else {
		atomic.StoreUint32(&obj._DisableRewardsProcessedNotification, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableRewardsProcessedNotification")
	}
	return nil
}
func (obj *Flags) SetEnableGenerationOfRewardsInLockedState(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableGenerationOfRewardsInLockedState", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableGenerationOfRewardsInLockedState, 1)
	} else {
		atomic.StoreUint32(&obj._EnableGenerationOfRewardsInLockedState, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableGenerationOfRewardsInLockedState")
	}
	return nil
}
func (obj *Flags) SetValidateRewardOfferConstraintsExpressionOnCreation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.ValidateRewardOfferConstraintsExpressionOnCreation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ValidateRewardOfferConstraintsExpressionOnCreation, 1)
	} else {
		atomic.StoreUint32(&obj._ValidateRewardOfferConstraintsExpressionOnCreation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ValidateRewardOfferConstraintsExpressionOnCreation")
	}
	return nil
}
func (obj *Flags) SetDisableEmailBasedNotifications(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.DisableEmailBasedNotifications", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableEmailBasedNotifications, 1)
	} else {
		atomic.StoreUint32(&obj._DisableEmailBasedNotifications, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableEmailBasedNotifications")
	}
	return nil
}
func (obj *Flags) SetEnableTPAPOrdersForRewardsProcessing(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableTPAPOrdersForRewardsProcessing", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableTPAPOrdersForRewardsProcessing, 1)
	} else {
		atomic.StoreUint32(&obj._EnableTPAPOrdersForRewardsProcessing, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableTPAPOrdersForRewardsProcessing")
	}
	return nil
}

func NewRewardsPayoutConfig() (_obj *RewardsPayoutConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardsPayoutConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablerewardspayoutviacelestial"] = _obj.SetEnableRewardsPayoutViaCelestial
	_setters["epifibusinessaccountpiids"] = _obj.SetEpifiBusinessAccountPiIds
	_obj._EpifiBusinessAccountPiIdsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *RewardsPayoutConfig) Init() {
	newObj, _ := NewRewardsPayoutConfig()
	*obj = *newObj
}

func (obj *RewardsPayoutConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RewardsPayoutConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RewardsPayoutConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsPayoutConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RewardsPayoutConfig) setDynamicField(v *config.RewardsPayoutConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablerewardspayoutviacelestial":
		return obj.SetEnableRewardsPayoutViaCelestial(v.EnableRewardsPayoutViaCelestial, true, nil)
	case "epifibusinessaccountpiids":
		return obj.SetEpifiBusinessAccountPiIds(v.EpifiBusinessAccountPiIds, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RewardsPayoutConfig) setDynamicFields(v *config.RewardsPayoutConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEnableRewardsPayoutViaCelestial(v.EnableRewardsPayoutViaCelestial, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEpifiBusinessAccountPiIds(v.EpifiBusinessAccountPiIds, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RewardsPayoutConfig) setStaticFields(v *config.RewardsPayoutConfig) error {

	obj._EpifiBusinessAccountActorId = v.EpifiBusinessAccountActorId
	obj._TimeToWaitBeforeCheckingOrderStatus = v.TimeToWaitBeforeCheckingOrderStatus
	return nil
}

func (obj *RewardsPayoutConfig) SetEnableRewardsPayoutViaCelestial(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsPayoutConfig.EnableRewardsPayoutViaCelestial", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableRewardsPayoutViaCelestial, 1)
	} else {
		atomic.StoreUint32(&obj._EnableRewardsPayoutViaCelestial, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableRewardsPayoutViaCelestial")
	}
	return nil
}
func (obj *RewardsPayoutConfig) SetEpifiBusinessAccountPiIds(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsPayoutConfig.EpifiBusinessAccountPiIds", reflect.TypeOf(val))
	}
	obj._EpifiBusinessAccountPiIdsMutex.Lock()
	defer obj._EpifiBusinessAccountPiIdsMutex.Unlock()
	obj._EpifiBusinessAccountPiIds = roarray.New[string](v)
	return nil
}

func NewSlackAlertingConfig() (_obj *SlackAlertingConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SlackAlertingConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["entityupdateschannelid"] = _obj.SetEntityUpdatesChannelId
	_obj._EntityUpdatesChannelIdMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *SlackAlertingConfig) Init() {
	newObj, _ := NewSlackAlertingConfig()
	*obj = *newObj
}

func (obj *SlackAlertingConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SlackAlertingConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SlackAlertingConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *SlackAlertingConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SlackAlertingConfig) setDynamicField(v *config.SlackAlertingConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "entityupdateschannelid":
		return obj.SetEntityUpdatesChannelId(v.EntityUpdatesChannelId, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SlackAlertingConfig) setDynamicFields(v *config.SlackAlertingConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEntityUpdatesChannelId(v.EntityUpdatesChannelId, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SlackAlertingConfig) setStaticFields(v *config.SlackAlertingConfig) error {

	return nil
}

func (obj *SlackAlertingConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *SlackAlertingConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *SlackAlertingConfig) SetEntityUpdatesChannelId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *SlackAlertingConfig.EntityUpdatesChannelId", reflect.TypeOf(val))
	}
	obj._EntityUpdatesChannelIdMutex.Lock()
	defer obj._EntityUpdatesChannelIdMutex.Unlock()
	obj._EntityUpdatesChannelId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "EntityUpdatesChannelId")
	}
	return nil
}

func NewReferralRewardsCappingAndPeriod() (_obj *ReferralRewardsCappingAndPeriod, _setters map[string]dynconf.SetFunc) {
	_obj = &ReferralRewardsCappingAndPeriod{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["rewardscap"] = _obj.SetRewardsCap
	_setters["active"] = _obj.SetActive
	_setters["cappingperiod"] = _obj.SetCappingPeriod
	return _obj, _setters
}

func NewReferralRewardsCappingAndPeriodWithQuest(questFieldPath string) (_obj *ReferralRewardsCappingAndPeriod, _setters map[string]dynconf.SetFunc) {
	_obj = &ReferralRewardsCappingAndPeriod{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["rewardscap"] = _obj.SetRewardsCap
	_setters["active"] = _obj.SetActive
	_setters["cappingperiod"] = _obj.SetCappingPeriod
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *ReferralRewardsCappingAndPeriod) Init(questFieldPath string) {
	newObj, _ := NewReferralRewardsCappingAndPeriod()
	*obj = *newObj
}
func (obj *ReferralRewardsCappingAndPeriod) InitWithQuest(questFieldPath string) {
	newObj, _ := NewReferralRewardsCappingAndPeriodWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *ReferralRewardsCappingAndPeriod) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *ReferralRewardsCappingAndPeriod) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ReferralRewardsCappingAndPeriod) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "RewardsCap",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_INT},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *ReferralRewardsCappingAndPeriod) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ReferralRewardsCappingAndPeriod)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralRewardsCappingAndPeriod", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ReferralRewardsCappingAndPeriod) setDynamicField(v *config.ReferralRewardsCappingAndPeriod, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "rewardscap":
		return obj.SetRewardsCap(v.RewardsCap, true, nil)
	case "active":
		return obj.SetActive(v.Active, true, nil)
	case "cappingperiod":
		return obj.SetCappingPeriod(v.CappingPeriod, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ReferralRewardsCappingAndPeriod) setDynamicFields(v *config.ReferralRewardsCappingAndPeriod, dynamic bool, path []string) (err error) {

	err = obj.SetRewardsCap(v.RewardsCap, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetActive(v.Active, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCappingPeriod(v.CappingPeriod, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ReferralRewardsCappingAndPeriod) setStaticFields(v *config.ReferralRewardsCappingAndPeriod) error {

	return nil
}

func (obj *ReferralRewardsCappingAndPeriod) SetRewardsCap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralRewardsCappingAndPeriod.RewardsCap", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._RewardsCap, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RewardsCap")
	}
	return nil
}
func (obj *ReferralRewardsCappingAndPeriod) SetActive(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralRewardsCappingAndPeriod.Active", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Active, 1)
	} else {
		atomic.StoreUint32(&obj._Active, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Active")
	}
	return nil
}
func (obj *ReferralRewardsCappingAndPeriod) SetCappingPeriod(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ReferralRewardsCappingAndPeriod.CappingPeriod", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CappingPeriod, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CappingPeriod")
	}
	return nil
}

func NewRewardsPeriodicCappingInfo() (_obj *RewardsPeriodicCappingInfo, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardsPeriodicCappingInfo{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["rewardscap"] = _obj.SetRewardsCap
	_setters["cappingperiod"] = _obj.SetCappingPeriod
	_setters["active"] = _obj.SetActive
	return _obj, _setters
}

func (obj *RewardsPeriodicCappingInfo) Init() {
	newObj, _ := NewRewardsPeriodicCappingInfo()
	*obj = *newObj
}

func (obj *RewardsPeriodicCappingInfo) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RewardsPeriodicCappingInfo) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RewardsPeriodicCappingInfo)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsPeriodicCappingInfo", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RewardsPeriodicCappingInfo) setDynamicField(v *config.RewardsPeriodicCappingInfo, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "rewardscap":
		return obj.SetRewardsCap(v.RewardsCap, true, nil)
	case "cappingperiod":
		return obj.SetCappingPeriod(v.CappingPeriod, true, nil)
	case "active":
		return obj.SetActive(v.Active, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RewardsPeriodicCappingInfo) setDynamicFields(v *config.RewardsPeriodicCappingInfo, dynamic bool, path []string) (err error) {

	err = obj.SetRewardsCap(v.RewardsCap, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCappingPeriod(v.CappingPeriod, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetActive(v.Active, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RewardsPeriodicCappingInfo) setStaticFields(v *config.RewardsPeriodicCappingInfo) error {

	obj._OfferType = v.OfferType
	obj._CappingDuration = v.CappingDuration
	return nil
}

func (obj *RewardsPeriodicCappingInfo) SetRewardsCap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsPeriodicCappingInfo.RewardsCap", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._RewardsCap, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RewardsCap")
	}
	return nil
}
func (obj *RewardsPeriodicCappingInfo) SetCappingPeriod(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsPeriodicCappingInfo.CappingPeriod", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CappingPeriod, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CappingPeriod")
	}
	return nil
}
func (obj *RewardsPeriodicCappingInfo) SetActive(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsPeriodicCappingInfo.Active", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Active, 1)
	} else {
		atomic.StoreUint32(&obj._Active, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Active")
	}
	return nil
}

func NewCacheConfig() (_obj *CacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_UserReferralInfoLRUCacheConfig, _fieldSetters := NewUserReferralInfoLRUCacheConfig()
	_obj._UserReferralInfoLRUCacheConfig = _UserReferralInfoLRUCacheConfig
	helper.AddFieldSetters("userreferralinfolrucacheconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *CacheConfig) Init() {
	newObj, _ := NewCacheConfig()
	*obj = *newObj
}

func (obj *CacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CacheConfig) setDynamicField(v *config.CacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "userreferralinfolrucacheconfig":
		return obj._UserReferralInfoLRUCacheConfig.Set(v.UserReferralInfoLRUCacheConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CacheConfig) setDynamicFields(v *config.CacheConfig, dynamic bool, path []string) (err error) {

	err = obj._UserReferralInfoLRUCacheConfig.Set(v.UserReferralInfoLRUCacheConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CacheConfig) setStaticFields(v *config.CacheConfig) error {

	return nil
}

func NewUserReferralInfoLRUCacheConfig() (_obj *UserReferralInfoLRUCacheConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &UserReferralInfoLRUCacheConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["size"] = _obj.SetSize
	_setters["ttl"] = _obj.SetTtl
	return _obj, _setters
}

func (obj *UserReferralInfoLRUCacheConfig) Init() {
	newObj, _ := NewUserReferralInfoLRUCacheConfig()
	*obj = *newObj
}

func (obj *UserReferralInfoLRUCacheConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UserReferralInfoLRUCacheConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UserReferralInfoLRUCacheConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserReferralInfoLRUCacheConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UserReferralInfoLRUCacheConfig) setDynamicField(v *config.UserReferralInfoLRUCacheConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "size":
		return obj.SetSize(v.Size, true, nil)
	case "ttl":
		return obj.SetTtl(v.Ttl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UserReferralInfoLRUCacheConfig) setDynamicFields(v *config.UserReferralInfoLRUCacheConfig, dynamic bool, path []string) (err error) {

	err = obj.SetSize(v.Size, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTtl(v.Ttl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UserReferralInfoLRUCacheConfig) setStaticFields(v *config.UserReferralInfoLRUCacheConfig) error {

	return nil
}

func (obj *UserReferralInfoLRUCacheConfig) SetSize(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserReferralInfoLRUCacheConfig.Size", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._Size, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Size")
	}
	return nil
}
func (obj *UserReferralInfoLRUCacheConfig) SetTtl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *UserReferralInfoLRUCacheConfig.Ttl", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._Ttl, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Ttl")
	}
	return nil
}

func NewDataCollectorConfig() (_obj *DataCollectorConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DataCollectorConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["waittimeforfetchingcreditcardtxncategories"] = _obj.SetWaitTimeForFetchingCreditCardTxnCategories
	return _obj, _setters
}

func (obj *DataCollectorConfig) Init() {
	newObj, _ := NewDataCollectorConfig()
	*obj = *newObj
}

func (obj *DataCollectorConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DataCollectorConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DataCollectorConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DataCollectorConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DataCollectorConfig) setDynamicField(v *config.DataCollectorConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "waittimeforfetchingcreditcardtxncategories":
		return obj.SetWaitTimeForFetchingCreditCardTxnCategories(v.WaitTimeForFetchingCreditCardTxnCategories, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DataCollectorConfig) setDynamicFields(v *config.DataCollectorConfig, dynamic bool, path []string) (err error) {

	err = obj.SetWaitTimeForFetchingCreditCardTxnCategories(v.WaitTimeForFetchingCreditCardTxnCategories, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DataCollectorConfig) setStaticFields(v *config.DataCollectorConfig) error {

	return nil
}

func (obj *DataCollectorConfig) SetWaitTimeForFetchingCreditCardTxnCategories(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *DataCollectorConfig.WaitTimeForFetchingCreditCardTxnCategories", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._WaitTimeForFetchingCreditCardTxnCategories, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "WaitTimeForFetchingCreditCardTxnCategories")
	}
	return nil
}

func NewTieringPeriodicRewardConfig() (_obj *TieringPeriodicRewardConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &TieringPeriodicRewardConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._RewardTypeToDailyUpperCapMap = &syncmap.Map[string, float32]{}
	_setters["rewardtypetodailyuppercapmap"] = _obj.SetRewardTypeToDailyUpperCapMap
	return _obj, _setters
}

func (obj *TieringPeriodicRewardConfig) Init() {
	newObj, _ := NewTieringPeriodicRewardConfig()
	*obj = *newObj
}

func (obj *TieringPeriodicRewardConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *TieringPeriodicRewardConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.TieringPeriodicRewardConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringPeriodicRewardConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *TieringPeriodicRewardConfig) setDynamicField(v *config.TieringPeriodicRewardConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "rewardtypetodailyuppercapmap":
		return obj.SetRewardTypeToDailyUpperCapMap(v.RewardTypeToDailyUpperCapMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *TieringPeriodicRewardConfig) setDynamicFields(v *config.TieringPeriodicRewardConfig, dynamic bool, path []string) (err error) {

	err = obj.SetRewardTypeToDailyUpperCapMap(v.RewardTypeToDailyUpperCapMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *TieringPeriodicRewardConfig) setStaticFields(v *config.TieringPeriodicRewardConfig) error {

	return nil
}

func (obj *TieringPeriodicRewardConfig) SetRewardTypeToDailyUpperCapMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]float32)
	if !ok {
		return fmt.Errorf("invalid data type %v *TieringPeriodicRewardConfig.RewardTypeToDailyUpperCapMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._RewardTypeToDailyUpperCapMap, v, path)
}

func NewRewardsFactGeneratorConfig() (_obj *RewardsFactGeneratorConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardsFactGeneratorConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxtimedifferencebetweenchequebookrequestandchargesdebittxn"] = _obj.SetMaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn
	return _obj, _setters
}

func (obj *RewardsFactGeneratorConfig) Init() {
	newObj, _ := NewRewardsFactGeneratorConfig()
	*obj = *newObj
}

func (obj *RewardsFactGeneratorConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RewardsFactGeneratorConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RewardsFactGeneratorConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsFactGeneratorConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RewardsFactGeneratorConfig) setDynamicField(v *config.RewardsFactGeneratorConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxtimedifferencebetweenchequebookrequestandchargesdebittxn":
		return obj.SetMaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn(v.MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RewardsFactGeneratorConfig) setDynamicFields(v *config.RewardsFactGeneratorConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn(v.MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RewardsFactGeneratorConfig) setStaticFields(v *config.RewardsFactGeneratorConfig) error {

	return nil
}

func (obj *RewardsFactGeneratorConfig) SetMaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardsFactGeneratorConfig.MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn")
	}
	return nil
}

func NewWatsonMeta() (_obj *WatsonMeta, _setters map[string]dynconf.SetFunc) {
	_obj = &WatsonMeta{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["iserroractivityenabled"] = _obj.SetIsErrorActivityEnabled
	_setters["isuseractivityenabled"] = _obj.SetIsUserActivityEnabled
	_setters["defaultissuecategoryidforrewards"] = _obj.SetDefaultIssueCategoryIdForRewards
	_obj._DefaultIssueCategoryIdForRewardsMutex = &sync.RWMutex{}
	_setters["defaultissuecategoryidforprojections"] = _obj.SetDefaultIssueCategoryIdForProjections
	_obj._DefaultIssueCategoryIdForProjectionsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *WatsonMeta) Init() {
	newObj, _ := NewWatsonMeta()
	*obj = *newObj
}

func (obj *WatsonMeta) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *WatsonMeta) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.WatsonMeta)
	if !ok {
		return fmt.Errorf("invalid data type %v *WatsonMeta", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *WatsonMeta) setDynamicField(v *config.WatsonMeta, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "iserroractivityenabled":
		return obj.SetIsErrorActivityEnabled(v.IsErrorActivityEnabled, true, nil)
	case "isuseractivityenabled":
		return obj.SetIsUserActivityEnabled(v.IsUserActivityEnabled, true, nil)
	case "defaultissuecategoryidforrewards":
		return obj.SetDefaultIssueCategoryIdForRewards(v.DefaultIssueCategoryIdForRewards, true, nil)
	case "defaultissuecategoryidforprojections":
		return obj.SetDefaultIssueCategoryIdForProjections(v.DefaultIssueCategoryIdForProjections, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *WatsonMeta) setDynamicFields(v *config.WatsonMeta, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsErrorActivityEnabled(v.IsErrorActivityEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsUserActivityEnabled(v.IsUserActivityEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDefaultIssueCategoryIdForRewards(v.DefaultIssueCategoryIdForRewards, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDefaultIssueCategoryIdForProjections(v.DefaultIssueCategoryIdForProjections, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *WatsonMeta) setStaticFields(v *config.WatsonMeta) error {

	return nil
}

func (obj *WatsonMeta) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *WatsonMeta.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *WatsonMeta) SetIsErrorActivityEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *WatsonMeta.IsErrorActivityEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsErrorActivityEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsErrorActivityEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsErrorActivityEnabled")
	}
	return nil
}
func (obj *WatsonMeta) SetIsUserActivityEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *WatsonMeta.IsUserActivityEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsUserActivityEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsUserActivityEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsUserActivityEnabled")
	}
	return nil
}
func (obj *WatsonMeta) SetDefaultIssueCategoryIdForRewards(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *WatsonMeta.DefaultIssueCategoryIdForRewards", reflect.TypeOf(val))
	}
	obj._DefaultIssueCategoryIdForRewardsMutex.Lock()
	defer obj._DefaultIssueCategoryIdForRewardsMutex.Unlock()
	obj._DefaultIssueCategoryIdForRewards = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DefaultIssueCategoryIdForRewards")
	}
	return nil
}
func (obj *WatsonMeta) SetDefaultIssueCategoryIdForProjections(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *WatsonMeta.DefaultIssueCategoryIdForProjections", reflect.TypeOf(val))
	}
	obj._DefaultIssueCategoryIdForProjectionsMutex.Lock()
	defer obj._DefaultIssueCategoryIdForProjectionsMutex.Unlock()
	obj._DefaultIssueCategoryIdForProjections = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DefaultIssueCategoryIdForProjections")
	}
	return nil
}

func NewRewardOfferGenAIReviewConfig() (_obj *RewardOfferGenAIReviewConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RewardOfferGenAIReviewConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["bedrockmodelid"] = _obj.SetBedrockModelId
	_obj._BedrockModelIdMutex = &sync.RWMutex{}
	_setters["prompt"] = _obj.SetPrompt
	_obj._PromptMutex = &sync.RWMutex{}
	_setters["systemprompt"] = _obj.SetSystemPrompt
	_obj._SystemPromptMutex = &sync.RWMutex{}
	_setters["sampleofferpath"] = _obj.SetSampleOfferPath
	_obj._SampleOfferPathMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *RewardOfferGenAIReviewConfig) Init() {
	newObj, _ := NewRewardOfferGenAIReviewConfig()
	*obj = *newObj
}

func (obj *RewardOfferGenAIReviewConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RewardOfferGenAIReviewConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RewardOfferGenAIReviewConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardOfferGenAIReviewConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RewardOfferGenAIReviewConfig) setDynamicField(v *config.RewardOfferGenAIReviewConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "bedrockmodelid":
		return obj.SetBedrockModelId(v.BedrockModelId, true, nil)
	case "prompt":
		return obj.SetPrompt(v.Prompt, true, nil)
	case "systemprompt":
		return obj.SetSystemPrompt(v.SystemPrompt, true, nil)
	case "sampleofferpath":
		return obj.SetSampleOfferPath(v.SampleOfferPath, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RewardOfferGenAIReviewConfig) setDynamicFields(v *config.RewardOfferGenAIReviewConfig, dynamic bool, path []string) (err error) {

	err = obj.SetBedrockModelId(v.BedrockModelId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPrompt(v.Prompt, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSystemPrompt(v.SystemPrompt, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSampleOfferPath(v.SampleOfferPath, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RewardOfferGenAIReviewConfig) setStaticFields(v *config.RewardOfferGenAIReviewConfig) error {

	return nil
}

func (obj *RewardOfferGenAIReviewConfig) SetBedrockModelId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardOfferGenAIReviewConfig.BedrockModelId", reflect.TypeOf(val))
	}
	obj._BedrockModelIdMutex.Lock()
	defer obj._BedrockModelIdMutex.Unlock()
	obj._BedrockModelId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BedrockModelId")
	}
	return nil
}
func (obj *RewardOfferGenAIReviewConfig) SetPrompt(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardOfferGenAIReviewConfig.Prompt", reflect.TypeOf(val))
	}
	obj._PromptMutex.Lock()
	defer obj._PromptMutex.Unlock()
	obj._Prompt = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Prompt")
	}
	return nil
}
func (obj *RewardOfferGenAIReviewConfig) SetSystemPrompt(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardOfferGenAIReviewConfig.SystemPrompt", reflect.TypeOf(val))
	}
	obj._SystemPromptMutex.Lock()
	defer obj._SystemPromptMutex.Unlock()
	obj._SystemPrompt = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SystemPrompt")
	}
	return nil
}
func (obj *RewardOfferGenAIReviewConfig) SetSampleOfferPath(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *RewardOfferGenAIReviewConfig.SampleOfferPath", reflect.TypeOf(val))
	}
	obj._SampleOfferPathMutex.Lock()
	defer obj._SampleOfferPathMutex.Unlock()
	obj._SampleOfferPath = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SampleOfferPath")
	}
	return nil
}

func NewFeedbackEngineCustomEvaluatorRule() (_obj *FeedbackEngineCustomEvaluatorRule, _setters map[string]dynconf.SetFunc) {
	_obj = &FeedbackEngineCustomEvaluatorRule{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minclaimcount"] = _obj.SetMinClaimCount
	_setters["mincashbackearned"] = _obj.SetMinCashbackEarned
	_setters["minficoinsearned"] = _obj.SetMinFiCoinsEarned
	_setters["lastclaimthresholdduration"] = _obj.SetLastClaimThresholdDuration
	return _obj, _setters
}

func (obj *FeedbackEngineCustomEvaluatorRule) Init() {
	newObj, _ := NewFeedbackEngineCustomEvaluatorRule()
	*obj = *newObj
}

func (obj *FeedbackEngineCustomEvaluatorRule) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FeedbackEngineCustomEvaluatorRule) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FeedbackEngineCustomEvaluatorRule)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeedbackEngineCustomEvaluatorRule", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FeedbackEngineCustomEvaluatorRule) setDynamicField(v *config.FeedbackEngineCustomEvaluatorRule, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minclaimcount":
		return obj.SetMinClaimCount(v.MinClaimCount, true, nil)
	case "mincashbackearned":
		return obj.SetMinCashbackEarned(v.MinCashbackEarned, true, nil)
	case "minficoinsearned":
		return obj.SetMinFiCoinsEarned(v.MinFiCoinsEarned, true, nil)
	case "lastclaimthresholdduration":
		return obj.SetLastClaimThresholdDuration(v.LastClaimThresholdDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FeedbackEngineCustomEvaluatorRule) setDynamicFields(v *config.FeedbackEngineCustomEvaluatorRule, dynamic bool, path []string) (err error) {

	err = obj.SetMinClaimCount(v.MinClaimCount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinCashbackEarned(v.MinCashbackEarned, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinFiCoinsEarned(v.MinFiCoinsEarned, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLastClaimThresholdDuration(v.LastClaimThresholdDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FeedbackEngineCustomEvaluatorRule) setStaticFields(v *config.FeedbackEngineCustomEvaluatorRule) error {

	return nil
}

func (obj *FeedbackEngineCustomEvaluatorRule) SetMinClaimCount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeedbackEngineCustomEvaluatorRule.MinClaimCount", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinClaimCount, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinClaimCount")
	}
	return nil
}
func (obj *FeedbackEngineCustomEvaluatorRule) SetMinCashbackEarned(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeedbackEngineCustomEvaluatorRule.MinCashbackEarned", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinCashbackEarned, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinCashbackEarned")
	}
	return nil
}
func (obj *FeedbackEngineCustomEvaluatorRule) SetMinFiCoinsEarned(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeedbackEngineCustomEvaluatorRule.MinFiCoinsEarned", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MinFiCoinsEarned, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinFiCoinsEarned")
	}
	return nil
}
func (obj *FeedbackEngineCustomEvaluatorRule) SetLastClaimThresholdDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeedbackEngineCustomEvaluatorRule.LastClaimThresholdDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LastClaimThresholdDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LastClaimThresholdDuration")
	}
	return nil
}

func NewOrdersRewardConfig() (_obj *OrdersRewardConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OrdersRewardConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["selectedvpas3path"] = _obj.SetSelectedVpaS3Path
	_obj._SelectedVpaS3PathMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *OrdersRewardConfig) Init() {
	newObj, _ := NewOrdersRewardConfig()
	*obj = *newObj
}

func (obj *OrdersRewardConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OrdersRewardConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OrdersRewardConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OrdersRewardConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OrdersRewardConfig) setDynamicField(v *config.OrdersRewardConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "selectedvpas3path":
		return obj.SetSelectedVpaS3Path(v.SelectedVpaS3Path, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OrdersRewardConfig) setDynamicFields(v *config.OrdersRewardConfig, dynamic bool, path []string) (err error) {

	err = obj.SetSelectedVpaS3Path(v.SelectedVpaS3Path, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OrdersRewardConfig) setStaticFields(v *config.OrdersRewardConfig) error {

	return nil
}

func (obj *OrdersRewardConfig) SetSelectedVpaS3Path(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *OrdersRewardConfig.SelectedVpaS3Path", reflect.TypeOf(val))
	}
	obj._SelectedVpaS3PathMutex.Lock()
	defer obj._SelectedVpaS3PathMutex.Unlock()
	obj._SelectedVpaS3Path = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SelectedVpaS3Path")
	}
	return nil
}
