// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "bulkclaimrewardsredislockduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BulkClaimRewardsRedisLockDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BulkClaimRewardsRedisLockDuration, nil
	case "rewardsperiodiccappingsperoffertype":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.RewardsPeriodicCappingsPerOfferType, nil
		case len(dynamicFieldPath) > 1:

			return obj.RewardsPeriodicCappingsPerOfferType[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.RewardsPeriodicCappingsPerOfferType, nil
	case "feedbackenginecustomevaluatorrules":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.FeedbackEngineCustomEvaluatorRules, nil
		case len(dynamicFieldPath) > 1:

			return obj.FeedbackEngineCustomEvaluatorRules[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.FeedbackEngineCustomEvaluatorRules, nil
	case "rewardsbucketname":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RewardsBucketName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RewardsBucketName, nil
	case "chequebookchargetxnbeneficiaryactorid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ChequebookChargeTxnBeneficiaryActorId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ChequebookChargeTxnBeneficiaryActorId, nil
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "datacollectoreventssqssubscriber":
		return obj.DataCollectorEventsSqsSubscriber.Get(dynamicFieldPath[1:])
	case "claimrewardeventsqssubscriber":
		return obj.ClaimRewardEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardssqssubscriber":
		return obj.RewardsSqsSubscriber.Get(dynamicFieldPath[1:])
	case "orderupdatesqssubscriber":
		return obj.OrderUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "usersearchsqssubscriber":
		return obj.UserSearchSqsSubscriber.Get(dynamicFieldPath[1:])
	case "usersearchv1sqssubscriber":
		return obj.UserSearchV1SqsSubscriber.Get(dynamicFieldPath[1:])
	case "manualgiveawayeventsqssubscriber":
		return obj.ManualGiveawayEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "fitttexecutionupdatesqssubscriber":
		return obj.FitttExecutionUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "extrainterestsdbonuspayoutsqssubscriber":
		return obj.ExtraInterestSdBonusPayoutSqsSubscriber.Get(dynamicFieldPath[1:])
	case "minbalancesqssubscriber":
		return obj.MinBalanceSqsSubscriber.Get(dynamicFieldPath[1:])
	case "caaccountupdateeventsqssubscriber":
		return obj.CAAccountUpdateEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "kycsqssubscriber":
		return obj.KYCSqsSubscriber.Get(dynamicFieldPath[1:])
	case "fitttsportseventsqssubscriber":
		return obj.FitttSportsEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "savingsaccountstateupdatesqssubscriber":
		return obj.SavingsAccountStateUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "salarydetectionsqssubscriber":
		return obj.SalaryDetectionSqsSubscriber.Get(dynamicFieldPath[1:])
	case "salaryprogramstatusupdatesqssubscriber":
		return obj.SalaryProgramStatusUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "debitcardswitchnotificationsqssubscriber":
		return obj.DebitCardSwitchNotificationSqsSubscriber.Get(dynamicFieldPath[1:])
	case "onboardingstageupdatesqssubscriber":
		return obj.OnboardingStageUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "creditcardtransactionssqssubscriber":
		return obj.CreditCardTransactionsSqsSubscriber.Get(dynamicFieldPath[1:])
	case "creditcardrequeststageupdatesqssubscriber":
		return obj.CreditCardRequestStageUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "creditcardbillgenerationeventsqssubscriber":
		return obj.CreditCardBillGenerationEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardsrewardunlockersqssubscriber":
		return obj.RewardsRewardUnlockerSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardsrewardexpirysqssubscriber":
		return obj.RewardsRewardExpirySqsSubscriber.Get(dynamicFieldPath[1:])
	case "merchantpiupdateaffectedentitieseventsqssubscriber":
		return obj.MerchantPiUpdateAffectedEntitiesEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardclawbackeventcollectorsqssubscriber":
		return obj.RewardClawbackEventCollectorSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardclawbackprocessingsqssubscriber":
		return obj.RewardClawbackProcessingSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardsprocessingdelaysqssubscriber":
		return obj.RewardsProcessingDelaySqsSubscriber.Get(dynamicFieldPath[1:])
	case "luckydrawwinningprocessingsqssubscriber":
		return obj.LuckyDrawWinningProcessingSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardstatusupdatesqssubscriber":
		return obj.RewardStatusUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "clawbackeventcollectorsqssubscriber":
		return obj.ClawbackEventCollectorSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardsnotificationeventssqssubscriber":
		return obj.RewardsNotificationEventsSqsSubscriber.Get(dynamicFieldPath[1:])
	case "creditreportdownloadeventsubscriber":
		return obj.CreditReportDownloadEventSubscriber.Get(dynamicFieldPath[1:])
	case "investmenteventsubscriber":
		return obj.InvestmentEventSubscriber.Get(dynamicFieldPath[1:])
	case "rewardsofferredemptionstatusupdateeventsubscriber":
		return obj.RewardsOfferRedemptionStatusUpdateEventSubscriber.Get(dynamicFieldPath[1:])
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "rewardspayoutconfig":
		return obj.RewardsPayoutConfig.Get(dynamicFieldPath[1:])
	case "slackalertingconfig":
		return obj.SlackAlertingConfig.Get(dynamicFieldPath[1:])
	case "referralrewardscappingandperiod":
		return obj.ReferralRewardsCappingAndPeriod.Get(dynamicFieldPath[1:])
	case "cacheconfig":
		return obj.CacheConfig.Get(dynamicFieldPath[1:])
	case "enabletxnaggregateswithpinot":
		return obj.EnableTxnAggregatesWithPinot.Get(dynamicFieldPath[1:])
	case "datacollectorconfig":
		return obj.DataCollectorConfig.Get(dynamicFieldPath[1:])
	case "rewardsprojectionsgenerationeventsqssubscriber":
		return obj.RewardsProjectionsGenerationEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardsprojectionupdateeventsqssubscriber":
		return obj.RewardsProjectionUpdateEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "tieringperiodicrewardeventsqssubscriber":
		return obj.TieringPeriodicRewardEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "tieringtierupdateeventsqssubscriber":
		return obj.TieringTierUpdateEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "epfpassbookimporteventsqssubscriber":
		return obj.EpfPassbookImportEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "caaccountdatasynceventsqssubscriber":
		return obj.CaAccountDataSyncEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardsactornudgestatusupdateeventsqssubscriber":
		return obj.RewardsActorNudgeStatusUpdateEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "bulkclaimrewardseventsqssubscriber":
		return obj.BulkClaimRewardsEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "tieringperiodicrewardconfig":
		return obj.TieringPeriodicRewardConfig.Get(dynamicFieldPath[1:])
	case "rewardsfactgeneratorconfig":
		return obj.RewardsFactGeneratorConfig.Get(dynamicFieldPath[1:])
	case "rewardsaccountstatusupdateeventsqssubscriber":
		return obj.RewardsAccountStatusUpdateEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "rewardsterminalstatusupdateeventsqssubscriber":
		return obj.RewardsTerminalStatusUpdateEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "projectioneventforpinotsqssubscriber":
		return obj.ProjectionEventForPinotSqsSubscriber.Get(dynamicFieldPath[1:])
	case "watsonmeta":
		return obj.WatsonMeta.Get(dynamicFieldPath[1:])
	case "rewardoffergenaireviewconfig":
		return obj.RewardOfferGenAIReviewConfig.Get(dynamicFieldPath[1:])
	case "ordersrewardconfig":
		return obj.OrdersRewardConfig.Get(dynamicFieldPath[1:])
	case "vendorrewardfulfillmenteventsqssubscriber":
		return obj.VendorRewardFulfillmentEventSqsSubscriber.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "trimdebugmessagefromstatus":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TrimDebugMessageFromStatus\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TrimDebugMessageFromStatus, nil
	case "enablerewardsforinternalusersonly":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRewardsForInternalUsersOnly\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRewardsForInternalUsersOnly, nil
	case "disablerewardsprocessednotification":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableRewardsProcessedNotification\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableRewardsProcessedNotification, nil
	case "enablegenerationofrewardsinlockedstate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableGenerationOfRewardsInLockedState\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableGenerationOfRewardsInLockedState, nil
	case "validaterewardofferconstraintsexpressiononcreation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ValidateRewardOfferConstraintsExpressionOnCreation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ValidateRewardOfferConstraintsExpressionOnCreation, nil
	case "disableemailbasednotifications":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableEmailBasedNotifications\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableEmailBasedNotifications, nil
	case "enabletpapordersforrewardsprocessing":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableTPAPOrdersForRewardsProcessing\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableTPAPOrdersForRewardsProcessing, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RewardsPayoutConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablerewardspayoutviacelestial":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableRewardsPayoutViaCelestial\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableRewardsPayoutViaCelestial, nil
	case "epifibusinessaccountpiids":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EpifiBusinessAccountPiIds\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EpifiBusinessAccountPiIds, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RewardsPayoutConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SlackAlertingConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "entityupdateschannelid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EntityUpdatesChannelId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EntityUpdatesChannelId, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SlackAlertingConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ReferralRewardsCappingAndPeriod) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "rewardscap":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RewardsCap\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RewardsCap, nil
	case "active":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Active\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Active, nil
	case "cappingperiod":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CappingPeriod\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CappingPeriod, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ReferralRewardsCappingAndPeriod", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RewardsPeriodicCappingInfo) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "rewardscap":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RewardsCap\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RewardsCap, nil
	case "cappingperiod":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CappingPeriod\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CappingPeriod, nil
	case "active":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Active\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Active, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RewardsPeriodicCappingInfo", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "userreferralinfolrucacheconfig":
		return obj.UserReferralInfoLRUCacheConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UserReferralInfoLRUCacheConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "size":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Size\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Size, nil
	case "ttl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Ttl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Ttl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UserReferralInfoLRUCacheConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DataCollectorConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "waittimeforfetchingcreditcardtxncategories":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"WaitTimeForFetchingCreditCardTxnCategories\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.WaitTimeForFetchingCreditCardTxnCategories, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DataCollectorConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *TieringPeriodicRewardConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "rewardtypetodailyuppercapmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.RewardTypeToDailyUpperCapMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"RewardTypeToDailyUpperCapMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.RewardTypeToDailyUpperCapMap[dynamicFieldPath[1]], nil

		}
		return obj.RewardTypeToDailyUpperCapMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for TieringPeriodicRewardConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RewardsFactGeneratorConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxtimedifferencebetweenchequebookrequestandchargesdebittxn":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxTimeDifferenceBetweenChequebookRequestAndChargesDebitTxn, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RewardsFactGeneratorConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *WatsonMeta) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "iserroractivityenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsErrorActivityEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsErrorActivityEnabled, nil
	case "isuseractivityenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsUserActivityEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsUserActivityEnabled, nil
	case "defaultissuecategoryidforrewards":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultIssueCategoryIdForRewards\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultIssueCategoryIdForRewards, nil
	case "defaultissuecategoryidforprojections":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DefaultIssueCategoryIdForProjections\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DefaultIssueCategoryIdForProjections, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for WatsonMeta", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RewardOfferGenAIReviewConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "bedrockmodelid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BedrockModelId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BedrockModelId, nil
	case "prompt":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Prompt\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Prompt, nil
	case "systemprompt":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SystemPrompt\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SystemPrompt, nil
	case "sampleofferpath":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SampleOfferPath\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SampleOfferPath, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RewardOfferGenAIReviewConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FeedbackEngineCustomEvaluatorRule) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "minclaimcount":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinClaimCount\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinClaimCount, nil
	case "mincashbackearned":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinCashbackEarned\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinCashbackEarned, nil
	case "minficoinsearned":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinFiCoinsEarned\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinFiCoinsEarned, nil
	case "lastclaimthresholdduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LastClaimThresholdDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LastClaimThresholdDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FeedbackEngineCustomEvaluatorRule", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OrdersRewardConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "selectedvpas3path":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SelectedVpaS3Path\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SelectedVpaS3Path, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OrdersRewardConfig", strings.Join(dynamicFieldPath, "."))
	}
}
