package consumer

import (
	"context"
	"errors"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	eventMocks "github.com/epifi/be-common/pkg/events/mocks"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	dataCollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	generatorPb "github.com/epifi/gamma/api/rewards/generator"
	mockRewards "github.com/epifi/gamma/api/rewards/mocks"
	rewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	"github.com/epifi/gamma/rewards/config/genconf"
	"github.com/epifi/gamma/rewards/generator/internalerrors"
	"github.com/epifi/gamma/rewards/generator/model"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/common"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/order"
	"github.com/epifi/gamma/rewards/test"
	daoMock "github.com/epifi/gamma/rewards/test/mocks/generator/dao"
	ruleEngineMock "github.com/epifi/gamma/rewards/test/mocks/generator/ruleengine"
	mockGenerator "github.com/epifi/gamma/rewards/test/mocks/generator/ruleengine/fact/generator"
	helperMocks "github.com/epifi/gamma/rewards/test/mocks/helper"
	notificationMock "github.com/epifi/gamma/rewards/test/mocks/notification"
)

type GeneratorConsumerServiceTestSuit struct {
	service generatorPb.ConsumerServer
}

var (
	ts      GeneratorConsumerServiceTestSuit
	dynConf *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
// nolint: dogsled
func TestMain(m *testing.M) {

	_, genConf, _, teardown := test.InitTestServer(false)

	ts = GeneratorConsumerServiceTestSuit{}
	dynConf = genConf

	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestGeneratorConsumerService_ProcessDataCollectorEvent(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockRewardsDao := daoMock.NewMockRewardsDao(ctr)
	mockRewardOfferDao := daoMock.NewMockRewardOfferDao(ctr)
	notificationServiceMock := notificationMock.NewMockINotificationService(ctr)
	mockFactGenerator := mockGenerator.NewMockIFactGenerator(ctr)
	mockFactGenFactory := mockGenerator.NewMockIFactGeneratorFactory(ctr)
	mockRuleEngine := ruleEngineMock.NewMockRuleEngine(ctr)
	mockPublisher := queueMocks.NewMockPublisher(ctr)
	brokerMock := eventMocks.NewMockBroker(ctr)
	mockRewardsGenerationEventSnsPublisher := queueMocks.NewMockPublisher(ctr)
	userHelperMock := helperMocks.NewMockIUserHelperService(ctr)

	mockOrderFact := &order.OrderFact{CommonFact: &common.CommonFact{}}

	brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
	notificationServiceMock.EXPECT().SendEarnedRewardNotification(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	mockFactGenFactory.EXPECT().Get(gomock.Any(), gomock.Any()).Return(mockFactGenerator, nil).AnyTimes()

	factory := fact.NewFactEngine(mockFactGenFactory)
	ts.service = &GeneratorConsumerService{
		ruleEngine:                         mockRuleEngine,
		factEngine:                         factory,
		rewardDao:                          mockRewardsDao,
		rewardPublisher:                    mockPublisher,
		eventsBroker:                       brokerMock,
		notificationService:                notificationServiceMock,
		rewardsGenerationEventSnsPublisher: mockRewardsGenerationEventSnsPublisher,
		rewardOfferDao:                     mockRewardOfferDao,
		userHelper:                         userHelperMock,
		dynConf:                            dynConf,
	}

	reward := &model.Reward{
		RefId:   "order-1",
		ActorId: "act-1",
		OfferId: "offer-1",
		Status:  rewardsPb.RewardStatus_PROCESSING_PENDING,
		Aggregates: &rewardOffersPb.RewardAggregates{
			UserAggregate:   1,
			ActionAggregate: 1,
		},
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Option: &rewardsPb.RewardOption_Cash{
						Cash: &rewardsPb.Cash{
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        10,
							},
						},
					},
				}, {
					Option: &rewardsPb.RewardOption_FiCoins{
						FiCoins: &rewardsPb.FiCoins{
							Units: 0,
							ExpiresAt: &timestampPb.Timestamp{
								Seconds: 123123,
								Nanos:   0,
							},
						},
					},
				},
			},
		},
	}
	rewardOffer := &rewardOffersPb.RewardOffer{
		Id:          "offer-1",
		ActiveSince: "2020-05-10T10:04:05+05:30",
		ActiveTill:  "2025-05-10T10:04:05+05:30",
		CreatedBy:   "CreatedBy-1",
		ActionType:  "TXN",
		ConstraintMeta: &rewardOffersPb.ConstraintsMeta{
			Expression: "TXN_AMOUNT>100",
		},
		RewardMeta: &rewardOffersPb.RewardMeta{
			RewardAggregates: &rewardOffersPb.RewardAggregates{
				UserAggregate:   2,
				ActionAggregate: 100,
			},
			DefaultDecideTimeInSecs: 10,
			Probability:             1,
			RewardConfigOptions: []*rewardOffersPb.RewardConfigOption{{
				RewardType: rewardsPb.RewardType_FI_COINS,
				UnitsConfig: &rewardOffersPb.RewardConfigOption_ExpressionProbabilityConfig{
					ExpressionProbabilityConfig: &rewardOffersPb.ExpressionProbabilityConfig{
						Expression:  "TXN_AMOUNT*0.1",
						Probability: 0.5,
					},
				},
			}},
		},
		DisplayMeta: &rewardOffersPb.DisplayMeta{
			DisplaySince: "2020-05-10T10:04:05+05:30",
			DisplayTill:  "2025-05-10T10:04:05+05:30",
			DisplayType:  rewardOffersPb.DisplayType_FRINGE,
			Title:        "title",
			Steps:        []string{"steps"},
			Tncs:         []string{"tnc"},
			Icon:         "icon_url",
		},
	}
	rewardOffers := []*rewardOffersPb.RewardOffer{rewardOffer}

	t.Run("user is NR, event not eligible for reward generation", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil)
		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(true, nil)
		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		require.NoError(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.GetResponseHeader().GetStatus())
	})

	t.Run("user is FiLite, event not eligible for reward generation", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_KYC}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil)
		userHelperMock.EXPECT().IsFiLiteUser(gomock.Any(), "").Return(true, nil)
		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		require.NoError(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.GetResponseHeader().GetStatus())
	})

	t.Run("auto process reward: successful processing of data collector event", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(reward, nil).Times(2)
		mockRewardsDao.EXPECT().PersistReward(gomock.Any(), gomock.Any()).Return(nil).Times(2)
		mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("msg-1", nil).Times(2)
		mockRewardsGenerationEventSnsPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("msg-1", nil).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.ResponseHeader.Status)
	})

	t.Run("auto process reward: successful processing of data collector event: no rewards", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.ResponseHeader.Status)
	})

	t.Run("auto process reward: unsuccessful processing of data collector event: failed to create even a single fact", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{}}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil)
		userHelperMock.EXPECT().IsFiLiteUser(gomock.Any(), "").Return(false, nil)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))
		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, res.ResponseHeader.Status)
	})

	t.Run("auto process reward: failed to create some of the facts", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return([]*rewardOffersPb.RewardOffer{rewardOffer, rewardOffer, rewardOffer}, nil)
		// first 2 fact generation calls gave error
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error creating fact")).Times(2)
		// next fact generation call generated some facts.
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(reward, nil).Times(2)
		mockRewardsDao.EXPECT().PersistReward(gomock.Any(), gomock.Any()).Return(nil).Times(2)
		mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("msg-1", nil).Times(2)
		mockRewardsGenerationEventSnsPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("msg-1", nil).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, res.ResponseHeader.Status)
	})

	t.Run("auto process reward: unsuccessful processing of data collector event: rule engine error", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("error in rule engine while calculating rewards")).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, res.ResponseHeader.Status)
	})

	t.Run("auto process reward: unsuccessful processing of data collector event: persistence failed", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(reward, nil).Times(2)
		mockRewardsDao.EXPECT().PersistReward(gomock.Any(), gomock.Any()).Return(fmt.Errorf("error while persisting reward")).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, res.ResponseHeader.Status)
	})

	t.Run("auto process reward: successful processing of duplicate data collector event: persistence failed", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(reward, nil).Times(2)
		mockRewardsDao.EXPECT().PersistReward(gomock.Any(), gomock.Any()).Return(epifierrors.ErrDuplicateEntry).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.ResponseHeader.Status)
	})

	t.Run("auto process reward: successful processing of data collector event: persistence failed due to max reward aggregate already reached", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(reward, nil).Times(2)
		mockRewardsDao.EXPECT().PersistReward(gomock.Any(), gomock.Any()).Return(internalerrors.MaxRewardAggregateReached).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.ResponseHeader.Status)
	})

	reward = &model.Reward{
		RefId:   "txn-1",
		ActorId: "act-1",
		OfferId: "offer-1",
		Status:  0,
		Aggregates: &rewardOffersPb.RewardAggregates{
			UserAggregate:   1,
			ActionAggregate: 1,
		},
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Option: &rewardsPb.RewardOption_Cash{
						Cash: &rewardsPb.Cash{
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        10,
							},
						},
					},
				}, {
					Option: &rewardsPb.RewardOption_FiCoins{
						FiCoins: &rewardsPb.FiCoins{
							Units: 0,
							ExpiresAt: &timestampPb.Timestamp{
								Seconds: 123123,
								Nanos:   0,
							},
						},
					},
				},
			},
		},
	}

	t.Run("user select reward: successful processing of data collector event", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(reward, nil).Times(2)
		mockRewardsDao.EXPECT().PersistReward(gomock.Any(), gomock.Any()).Return(nil).Times(2)
		mockRewardsGenerationEventSnsPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("msg-1", nil).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.ResponseHeader.Status)
	})

	t.Run("user select reward: successful processing of data collector event: no rewards", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.ResponseHeader.Status)
	})

	t.Run("user select reward: unsuccessful processing of data collector event: failed to create fact", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{}}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil)
		userHelperMock.EXPECT().IsFiLiteUser(gomock.Any(), "").Return(false, nil)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))
		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, res.ResponseHeader.Status)
	})

	t.Run("user select reward: unsuccessful processing of data collector event: rule engine error", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("error in rule engine while calculating rewards")).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, res.ResponseHeader.Status)
	})

	t.Run("user select reward: unsuccessful processing of data collector event: persistence failed", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(reward, nil).Times(2)
		mockRewardsDao.EXPECT().PersistReward(gomock.Any(), gomock.Any()).Return(fmt.Errorf("error while persisting reward")).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE, res.ResponseHeader.Status)
	})

	t.Run("user select reward: successful processing of duplicate data collector event: persistence failed", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(reward, nil).Times(2)
		mockRewardsDao.EXPECT().PersistReward(gomock.Any(), gomock.Any()).Return(epifierrors.ErrDuplicateEntry).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.ResponseHeader.Status)
	})

	t.Run("user select reward: successful processing of data collector event: persistence failed due to max reward aggregate already reached", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(reward, nil).Times(2)
		mockRewardsDao.EXPECT().PersistReward(gomock.Any(), gomock.Any()).Return(internalerrors.MaxRewardAggregateReached).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		assert.Nil(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.ResponseHeader.Status)
	})

	rewardOffer1 := &rewardOffersPb.RewardOffer{
		Id: "offer-id-3",
		RewardMeta: &rewardOffersPb.RewardMeta{
			DefaultDecideTimeInSecs: 30,
			Probability:             1,
			RewardConfigOptions: []*rewardOffersPb.RewardConfigOption{
				{
					ConstraintExpression: "true",
					RewardType:           rewardsPb.RewardType_FI_COINS,
					DisplayConfig:        &rewardOffersPb.RewardConfigOption_Display{},
					UnitsConfig: &rewardOffersPb.RewardConfigOption_FixedProbabilityConfig{
						FixedProbabilityConfig: &rewardOffersPb.FixedProbabilityConfig{
							Value:       0,
							Probability: 1,
						},
					},
				},
			},
		},
		DisplayMeta:    &rewardOffersPb.DisplayMeta{ActionDesc: "Earned for doing a txn through Fi"},
		ConstraintMeta: &rewardOffersPb.ConstraintsMeta{Expression: "2>1"},
	}
	rewardOffers1 := []*rewardOffersPb.RewardOffer{rewardOffer1}

	reward1 := &model.Reward{
		RefId:           "ref-id-1",
		ActorId:         "actor-id-1",
		Status:          rewardsPb.RewardStatus_PROCESSING_PENDING,
		OfferId:         "offer-id-3",
		ActionTimestamp: time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC),
		RewardOptions: &rewardsPb.RewardOptions{
			DefaultDecideTimeInSecs: 30,
			ActionDetails:           "Earned for doing a txn through Fi",
			Options: []*rewardsPb.RewardOption{
				{
					Display: &rewardsPb.RewardOptionDisplay{
						BeforeClaimTitle: "No reward 😢",
						AfterClaimTitle:  "No reward 😢",
					},
					RewardType: rewardsPb.RewardType_NO_REWARD,
					RewardProcessingTimeConfig: &rewardsPb.RewardTimeConfig{
						Config: &rewardsPb.RewardTimeConfig_RelativeTimeInMinutes{RelativeTimeInMinutes: 0},
					},
					Option: &rewardsPb.RewardOption_NoReward{},
				},
			},
		},
		RewardType: rewardsPb.RewardType_NO_REWARD,
		ChosenReward: &rewardsPb.RewardOption{
			RewardType: rewardsPb.RewardType_NO_REWARD,
			Option:     &rewardsPb.RewardOption_NoReward{},
		},
		ClaimType:      rewardsPb.ClaimType_CLAIM_TYPE_MANUAL,
		RewardDisplay:  &rewardsPb.RewardDisplay{},
		RewardMetadata: &rewardsPb.RewardMetadata{},
	}

	t.Run("user select reward: NO_REWARD type reward to be auto processed", func(t *testing.T) {
		req := &dataCollectorPb.CollectedData{Data: &dataCollectorPb.CollectedData_OrderUpdateEvent{
			OrderUpdateEvent: &orderPb.OrderUpdate{},
		}, DataType: rewardsPb.CollectedDataType_ORDER}

		userHelperMock.EXPECT().IsNonResidentUser(gomock.Any(), "").Return(false, nil).Times(2)
		mockRewardOfferDao.EXPECT().FetchRewardOffersActiveAtTime(gomock.Any(), gomock.Any(), gomock.Any(), rewardOffersPb.GenerationType_GENERATION_TYPE_REWARD).Return(rewardOffers1, nil)
		mockFactGenerator.EXPECT().GenerateFacts(gomock.Any(), gomock.Any(), gomock.Any()).Return([]common.IFact{mockOrderFact, mockOrderFact}, nil)
		mockRuleEngine.EXPECT().CalculateRewards(gomock.Any(), gomock.Any()).Return(reward1, nil).Times(2)
		mockRewardsDao.EXPECT().PersistReward(gomock.Any(), gomock.Any()).Return(nil).Times(2)
		mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("msg-1", nil).Times(2)
		mockRewardsGenerationEventSnsPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("msg-1", nil).Times(2)

		res, err := ts.service.ProcessDataCollectorEvent(context.Background(), req)
		require.NoError(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, queuePb.MessageConsumptionStatus_SUCCESS, res.GetResponseHeader().GetStatus())
	})
}

func TestGeneratorConsumerService_ProcessClaimRewardEvent(t *testing.T) {
	rewardWithDefaultOptionCash := &model.Reward{
		Id:     "reward-id-1",
		Status: rewardsPb.RewardStatus_CREATED,
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Id:         "cash-option-id-1",
					RewardType: rewardsPb.RewardType_CASH,
				},
				{
					Id:         "sd-option-id-1",
					RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
				},
			},
		},
	}

	rewardWithDefaultOptionSd := &model.Reward{
		Id:     "reward-id-2",
		Status: rewardsPb.RewardStatus_CREATED,
		RewardOptions: &rewardsPb.RewardOptions{
			Options: []*rewardsPb.RewardOption{
				{
					Id:         "sd-option-id-1",
					RewardType: rewardsPb.RewardType_SMART_DEPOSIT,
				},
				{
					Id:         "cash-option-id-1",
					RewardType: rewardsPb.RewardType_CASH,
				},
			},
		},
	}

	type args struct {
		ctx context.Context
		req *generatorPb.ClaimRewardRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(rewardsDao *daoMock.MockRewardsDao, rewardsGeneratorClient *mockRewards.MockRewardsGeneratorClient)
		want       *generatorPb.ConsumerResponse
		wantErr    bool
	}{
		{
			name: "should return TransientFailure if dao call to fetch reward fails",
			args: args{
				ctx: context.Background(),
				req: &generatorPb.ClaimRewardRequest{RewardId: "reward-id-1"},
			},
			setupMocks: func(rewardsDao *daoMock.MockRewardsDao, rewardsGeneratorClient *mockRewards.MockRewardsGeneratorClient) {
				rewardsDao.EXPECT().FetchRewardById(context.Background(), "reward-id-1").Return(nil, errors.New("error fetching reward from db"))
			},
			want: &generatorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			}},
		},
		{
			name: "should return PermanentFailure if reward is not in CREATED state",
			args: args{
				ctx: context.Background(),
				req: &generatorPb.ClaimRewardRequest{RewardId: "reward-id-1"},
			},
			setupMocks: func(rewardsDao *daoMock.MockRewardsDao, rewardsGeneratorClient *mockRewards.MockRewardsGeneratorClient) {
				rewardsDao.EXPECT().FetchRewardById(context.Background(), "reward-id-1").Return(&model.Reward{Status: rewardsPb.RewardStatus_PROCESSING_PENDING}, nil)
			},
			want: &generatorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
			}},
		},
		{
			name: "should return PermanentFailure if auto-choose is not possible for default option",
			args: args{
				ctx: context.Background(),
				req: &generatorPb.ClaimRewardRequest{RewardId: "reward-id-2"},
			},
			setupMocks: func(rewardsDao *daoMock.MockRewardsDao, rewardsGeneratorClient *mockRewards.MockRewardsGeneratorClient) {
				rewardsDao.EXPECT().FetchRewardById(context.Background(), "reward-id-2").Return(rewardWithDefaultOptionSd, nil)
			},
			want: &generatorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
			}},
		},
		{
			name: "should return TransientFailure if choose-option rpc call fails",
			args: args{
				ctx: context.Background(),
				req: &generatorPb.ClaimRewardRequest{RewardId: "reward-id-1"},
			},
			setupMocks: func(rewardsDao *daoMock.MockRewardsDao, rewardsGeneratorClient *mockRewards.MockRewardsGeneratorClient) {
				rewardsDao.EXPECT().FetchRewardById(context.Background(), "reward-id-1").Return(rewardWithDefaultOptionCash, nil)

				rewardsGeneratorClient.EXPECT().ChooseReward(context.Background(), &rewardsPb.ChooseRewardRequest{
					RewardId:       rewardWithDefaultOptionCash.Id,
					RewardOptionId: rewardWithDefaultOptionCash.RewardOptions.Options[0].Id,
				}).Return(&rewardsPb.ChooseRewardResponse{Status: rpc.StatusInternalWithDebugMsg("error choosing the option")}, nil)
			},
			want: &generatorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			}},
		},
		{
			name: "should return Success response",
			args: args{
				ctx: context.Background(),
				req: &generatorPb.ClaimRewardRequest{RewardId: "reward-id-1"},
			},
			setupMocks: func(rewardsDao *daoMock.MockRewardsDao, rewardsGeneratorClient *mockRewards.MockRewardsGeneratorClient) {
				rewardsDao.EXPECT().FetchRewardById(context.Background(), "reward-id-1").Return(rewardWithDefaultOptionCash, nil)

				rewardsGeneratorClient.EXPECT().ChooseReward(context.Background(), &rewardsPb.ChooseRewardRequest{
					RewardId:       rewardWithDefaultOptionCash.Id,
					RewardOptionId: rewardWithDefaultOptionCash.RewardOptions.Options[0].Id,
				}).Return(&rewardsPb.ChooseRewardResponse{Status: rpc.StatusOk()}, nil)
			},
			want: &generatorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_SUCCESS,
			}},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			// init mocks
			mockRewardsDao := daoMock.NewMockRewardsDao(ctr)
			mockRewardsGeneratorClient := mockRewards.NewMockRewardsGeneratorClient(ctr)

			tt.setupMocks(mockRewardsDao, mockRewardsGeneratorClient)

			s := &GeneratorConsumerService{
				rewardDao:     mockRewardsDao,
				rewardsClient: mockRewardsGeneratorClient,
				dynConf:       dynConf,
			}
			got, err := s.ProcessClaimRewardEvent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessClaimRewardEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("ProcessClaimRewardEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}
