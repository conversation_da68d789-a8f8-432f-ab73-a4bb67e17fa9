package pinot

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	accrualPkg "github.com/epifi/gamma/pkg/accrual"

	fireflyPb "github.com/epifi/gamma/api/firefly"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"

	"github.com/epifi/gamma/rewards/pinot/dao"
	"github.com/epifi/gamma/rewards/pinot/dao/model"
)

type Service struct {
	rewardsPinotPb.UnimplementedRewardsAggregatesServer
	terminalRewardsDao   dao.TerminalRewardsDao
	rewardProjectionsDao dao.RewardProjectionsDao
	rewardsClient        rewardsPb.RewardsGeneratorClient
	fireflyClient        fireflyPb.FireflyClient
	timeClient           datetime.Time
}

func NewService(
	terminalRewardsDao dao.TerminalRewardsDao,
	rewardProjectionsDao dao.RewardProjectionsDao,
	rewardsClient rewardsPb.RewardsGeneratorClient,
	fireflyClient fireflyPb.FireflyClient,
	timeClient datetime.Time,
) *Service {
	return &Service{
		terminalRewardsDao:   terminalRewardsDao,
		rewardProjectionsDao: rewardProjectionsDao,
		rewardsClient:        rewardsClient,
		fireflyClient:        fireflyClient,
		timeClient:           timeClient,
	}
}

// GetRewardsAggregates generates aggregates for terminal rewards only for given actor
// grouped by reward type of the chosen reward option
func (s *Service) GetRewardsAggregates(ctx context.Context, req *rewardsPinotPb.GetRewardsAggregatesRequest) (*rewardsPinotPb.GetRewardsAggregatesResponse, error) {
	if req.GetActorId() == "" {
		return &rewardsPinotPb.GetRewardsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty actorId field")}, nil
	}

	// --- Validate and extract the flattened out Time Range ---
	timeRangeType, fromTime, toTime, err := req.GetTimeRange().ValidateAndFlatten()
	if err != nil {
		logger.Error(ctx, "invalid time range parameters", zap.Error(err))
		return &rewardsPinotPb.GetRewardsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg(err.Error())}, nil
	}

	baseFilter := &model.RewardAggregationFilter{
		RewardType:          req.GetFilters().GetRewardType(),
		ClaimType:           req.GetFilters().GetClaimType(),
		AccountTier:         req.GetFilters().GetAccountTier(),
		PaymentProtocol:     req.GetFilters().GetPaymentProtocol(),
		OrderWorkflow:       req.GetFilters().GetOrderWorkflow(),
		RewardStatuses:      req.GetFilters().GetRewardStatuses(),
		OfferTypes:          req.GetFilters().GetRewardOfferTypes(),
		OfferId:             req.GetFilters().GetOfferId(),
		MerchantIds:         req.GetFilters().GetMerchantIds(),
		MerchantNames:       req.GetFilters().GetMerchantNames(),
		IncludeOntologyIds:  req.GetFilters().GetIncludeOntologyIds(),
		ExcludeOntologyIds:  req.GetFilters().GetExcludeOntologyIds(),
		IncludeL0Ontologies: req.GetFilters().GetIncludeL0Ontologies(),
		ExcludeL0Ontologies: req.GetFilters().GetExcludeL0Ontologies(),
		IncludeActionTypes:  req.GetFilters().GetIncludeActionTypes(),
		ExcludeActionTypes:  req.GetFilters().GetExcludeActionTypes(),
	}

	// Populate time fields using helper
	if err := populateTimeFilter(baseFilter, timeRangeType, fromTime, toTime); err != nil {
		logger.Error(ctx, "error populating time filter", zap.Error(err), zap.Any("timeRangeType", timeRangeType))
		return &rewardsPinotPb.GetRewardsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
	}

	terminalRewardsAggregates, err := s.getTerminalRewardsAggregatesWithMiscellaneousFiltersHandling(ctx, req.GetActorId(), baseFilter, req.GetFilters().GetMiscellaneousFilters())
	if err != nil {
		logger.Error(ctx, "error while fetching terminal rewards aggregates from GetRewardAggregatesForActorId dao",
			zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &rewardsPinotPb.GetRewardsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
	}

	rewardsAggregates := make([]*rewardsPinotPb.RewardOptionAggregate, 0)
	for _, terminalRewardAggregates := range terminalRewardsAggregates {
		rewardsAggregates = append(rewardsAggregates, &rewardsPinotPb.RewardOptionAggregate{
			RewardType:  terminalRewardAggregates.RewardType,
			RewardUnits: terminalRewardAggregates.RewardUnits,
			RewardCount: terminalRewardAggregates.RewardCount,
		})
	}

	return &rewardsPinotPb.GetRewardsAggregatesResponse{
		Status:                 rpcPb.StatusOk(),
		RewardOptionAggregates: rewardsAggregates,
	}, nil
}

// getTerminalRewardsAggregatesWithMiscellaneousFiltersHandling is a helper function that fetches the terminal rewards aggregates for a given actor
// with handling for miscellaneousFilters (Check filter proto definition for more details)
func (s *Service) getTerminalRewardsAggregatesWithMiscellaneousFiltersHandling(ctx context.Context, actorID string, baseFilter *model.RewardAggregationFilter, miscellaneousFilters *rewardsPinotPb.MiscellaneousFilters) ([]*model.RewardOptionAggregate, error) {
	var (
		terminalRewardsAggregates []*model.RewardOptionAggregate
		migrationTime             = accrualPkg.GetFiCoinsToFiPointsMigrationTime()
	)

	switch {
	case miscellaneousFilters.GetMergeFiCoinsToFiPoints() && s.timeClient.Now().After(migrationTime):
		var (
			isCreditCardUser                                bool
			preMigrationAggregates, postMigrationAggregates []*model.RewardOptionAggregate
			rewardTypeToAggregateMap                        = make(map[rewardsPb.RewardType]*model.RewardOptionAggregate)
		)

		// Using error group to fetch pre-migration and post-migration aggregates concurrently
		eg, gCtx := errgroup.WithContext(ctx)

		eg.Go(func() error {
			preMigrationFilter := *baseFilter
			preMigrationFilter.ToActionTime = migrationTime

			var daoErr error
			preMigrationAggregates, daoErr = s.terminalRewardsDao.GetRewardAggregatesForActorId(gCtx, actorID, &preMigrationFilter)
			if daoErr != nil {
				return fmt.Errorf("error while fetching pre-migration aggregates: %w", daoErr)
			}
			return nil
		})

		eg.Go(func() error {
			postMigrationFilter := *baseFilter
			postMigrationFilter.FromActionTime = migrationTime
			var daoErr error
			postMigrationAggregates, daoErr = s.terminalRewardsDao.GetRewardAggregatesForActorId(gCtx, actorID, &postMigrationFilter)
			if daoErr != nil {
				return fmt.Errorf("error while fetching post-migration aggregates: %w", daoErr)
			}
			return nil
		})

		eg.Go(func() error {
			isCreditCardUserResp, err := s.fireflyClient.IsCreditCardUser(gCtx, &fireflyPb.IsCreditCardUserRequest{
				ActorId: actorID,
			})
			if rpcErr := epifigrpc.RPCError(isCreditCardUserResp, err); rpcErr != nil {
				return fmt.Errorf("error while checking if user is a credit card user: %w", rpcErr)
			}
			isCreditCardUser = isCreditCardUserResp.GetIsCreditCardUser()
			return nil
		})

		if err := eg.Wait(); err != nil {
			logger.Error(ctx, "error while fetching terminal rewards aggregates from GetRewardAggregatesForActorId dao",
				zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorID))
			return nil, err
		}

		for _, agg := range preMigrationAggregates {
			// For pre-migration aggregates, we need to convert FI_COINS to FI_POINTS valuation
			if agg.RewardType == rewardsPb.RewardType_FI_COINS {
				agg.RewardUnits = float64(accrualPkg.ConvertFiCoinsToFiPoints(int32(agg.RewardUnits), isCreditCardUser))
			}
			rewardTypeToAggregateMap[agg.RewardType] = agg
		}
		for _, agg := range postMigrationAggregates {
			if currentAgg, ok := rewardTypeToAggregateMap[agg.RewardType]; ok {
				currentAgg.RewardUnits += agg.RewardUnits
				currentAgg.RewardCount += agg.RewardCount
			} else {
				rewardTypeToAggregateMap[agg.RewardType] = agg
			}
		}
		for rewardType, agg := range rewardTypeToAggregateMap {
			terminalRewardsAggregates = append(terminalRewardsAggregates, &model.RewardOptionAggregate{
				RewardType:  rewardType,
				RewardUnits: agg.RewardUnits,
				RewardCount: agg.RewardCount,
			})
		}
	default:
		var err error
		terminalRewardsAggregates, err = s.terminalRewardsDao.GetRewardAggregatesForActorId(ctx, actorID, baseFilter)
		if err != nil {
			logger.Error(ctx, "error while fetching terminal rewards aggregates from GetRewardAggregatesForActorId dao",
				zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorID))
			return nil, err
		}
	}

	// Return the merged aggregates.
	return terminalRewardsAggregates, nil
}

// GetMerchantRewardsAggregates generates aggregates for terminal rewards only for given actor
// grouped by merchant id, merchant name; sorted in descending order of reward units
func (s *Service) GetMerchantRewardsAggregates(ctx context.Context, req *rewardsPinotPb.GetMerchantRewardsAggregatesRequest) (*rewardsPinotPb.GetMerchantRewardsAggregatesResponse, error) {
	if !req.GetFromCreatedAt().IsValid() || !req.GetToCreatedAt().IsValid() {
		logger.Error(ctx, "createdTime i.e, fromCreatedAt and toCreatedAt fields are missing",
			zap.String("from_created_at", req.GetFromCreatedAt().String()),
			zap.String("to_created_at", req.GetToCreatedAt().String()))
		return &rewardsPinotPb.GetMerchantRewardsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
	}

	if req.GetActorId() == "" {
		return &rewardsPinotPb.GetMerchantRewardsAggregatesResponse{Status: rpcPb.StatusInternalWithDebugMsg("empty actorId field")}, nil
	}

	terminalRewardsAggregates, err := s.terminalRewardsDao.GetMerchantRewardAggregatesForActorId(ctx,
		req.GetActorId(),
		&model.RewardAggregationFilter{
			IncludeActionTypes: []rewardsPb.CollectedDataType{req.GetActionType()},
			FromCreatedAt:      req.GetFromCreatedAt().AsTime(),
			ToCreatedAt:        req.GetToCreatedAt().AsTime(),
			RewardType:         req.GetRewardType(),
		},
	)
	if err != nil {
		logger.Error(ctx, "error while fetching terminal rewards aggregates from GetMerchantRewardsAggregatesResponse dao",
			zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &rewardsPinotPb.GetMerchantRewardsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
	}

	rewardsAggregates := make([]*rewardsPinotPb.MerchantRewardAggregate, 0)
	for _, terminalRewardAggregates := range terminalRewardsAggregates {
		rewardsAggregates = append(rewardsAggregates, &rewardsPinotPb.MerchantRewardAggregate{
			RewardAggregate: &rewardsPinotPb.RewardOptionAggregate{
				RewardType:  terminalRewardAggregates.RewardAggregate.RewardType,
				RewardUnits: terminalRewardAggregates.RewardAggregate.RewardUnits,
				RewardCount: terminalRewardAggregates.RewardAggregate.RewardCount,
			},
			MerchantId:   terminalRewardAggregates.MerchantId,
			MerchantName: terminalRewardAggregates.MerchantName,
		})
	}

	return &rewardsPinotPb.GetMerchantRewardsAggregatesResponse{
		Status:                   rpcPb.StatusOk(),
		MerchantRewardAggregates: rewardsAggregates,
	}, nil
}

// GetActualizedRewardProjectionsAggregates generates aggregates for actualized reward projections for given actor
// grouped by reward_id, option1_projection_reward_type, option2_projection_reward_type
// these are divided into claimed and unclaimed rewards (to determine which option was chosen by the user)
//  1. ClaimedRewardProjections : projections for which user has chosen the option for the actualized reward
//  2. UnclaimedRewardProjections : projections for which user has not chosen the option for the actualized reward
//     all options are added to aggregates and only one reward type should be chosen to be considered for aggregates
func (s *Service) GetActualizedRewardProjectionsAggregates(ctx context.Context, req *rewardsPinotPb.GetActualizedRewardProjectionsAggregatesRequest) (*rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse, error) {
	if req.GetActorId() == "" {
		return &rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty actorId field")}, nil
	}

	// --- Validate and extract the flattened out Time Range ---
	timeRangeType, fromTime, toTime, err := req.GetTimeRange().ValidateAndFlatten()
	if err != nil {
		logger.Error(ctx, "invalid time range parameters", zap.Error(err))
		return &rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg(err.Error())}, nil
	}

	// --- Create Filter ---
	filter := newRewardProjectionFilterFromRequest(req.GetFilters(), true) // isActualized = true

	// Populate time fields using helper
	if err := populateTimeFilter(filter, timeRangeType, fromTime, toTime); err != nil {
		logger.Error(ctx, "error populating time filter", zap.Error(err), zap.Any("timeRangeType", timeRangeType))
		return &rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
	}

	actualizedRewardProjectionsAggregates, err := s.rewardProjectionsDao.GetRewardProjectionsAggregatesForActorId(ctx, req.GetActorId(), filter)
	if err != nil {
		logger.Error(ctx, "error while fetching reward projections aggregates from GetRewardProjectionsAggregatesForActorId dao",
			zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
	}

	claimedProjectionRewards, unclaimedProjectionRewards, err := s.getClaimedAndUnclaimedProjectionRewards(ctx, actualizedRewardProjectionsAggregates)
	if err != nil {
		logger.Error(ctx, "error while fetching claimed and unclaimed projection rewards", zap.Error(err))
		return &rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
	}

	return &rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse{
		Status: rpcPb.StatusOk(),
		RewardProjectionsAggregates: &rewardsPinotPb.ActualizedRewardProjectionsAggregate{
			ClaimedRewardProjections:   claimedProjectionRewards,
			UnclaimedRewardProjections: unclaimedProjectionRewards,
		},
	}, nil
}

// nolint:funlen
func (s *Service) getClaimedAndUnclaimedProjectionRewards(ctx context.Context, actualizedRewardProjectionsAggregates []*model.RewardProjectionsAggregate) ([]*rewardsPinotPb.RewardProjectionOption, []*rewardsPinotPb.RewardProjectionOption, error) {
	rewardIds := make([]string, 0)
	for _, rewardProjectionAggregates := range actualizedRewardProjectionsAggregates {
		if rewardProjectionAggregates.GeneratedRewardId == "" {
			continue
		}
		rewardIds = append(rewardIds, rewardProjectionAggregates.GeneratedRewardId)
	}

	// we are returning directly if no reward ids are present
	if len(rewardIds) == 0 {
		return nil, nil, nil
	}

	if len(rewardIds) > 25 {
		logger.Error(ctx, "reward ids count is more than 25, not fetching rewards")
		return nil, nil, fmt.Errorf("reward ids count is more than 25, not fetching all the rewards for aggregation")
	}

	getRewardsResp, err := s.rewardsClient.GetRewards(ctx, &rewardsPb.GetRewardsRequest{Ids: rewardIds})
	if rpcErr := epifigrpc.RPCError(getRewardsResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching rewards from rewards generator",
			zap.Error(rpcErr), zap.Strings("reward_ids", rewardIds))
		return nil, nil, fmt.Errorf("error while fetching rewards from rewards generator, err : %w", rpcErr)
	}

	rewardIdsToRewardMap := make(map[string]*rewardsPb.Reward)
	for _, reward := range getRewardsResp.GetRewards() {
		rewardIdsToRewardMap[reward.GetId()] = reward
	}

	claimedProjectionRewardsMap := make(map[rewardsPb.RewardType]float64)
	unclaimedProjectionRewardsMap := make(map[rewardsPb.RewardType]float64)

	for _, actualizedRewardProjectionAggregates := range actualizedRewardProjectionsAggregates {
		reward, ok := rewardIdsToRewardMap[actualizedRewardProjectionAggregates.GeneratedRewardId]
		if !ok {
			// just logging the error here to not fail aggregation call if just a single reward is not found.
			logger.Error(ctx, "reward not found for reward id", zap.String(logger.REWARD_ID, actualizedRewardProjectionAggregates.GeneratedRewardId))
			continue
		}
		chosenReward := reward.GetChosenReward()
		if chosenReward != nil {
			for _, rewardContributionOptionAggregate := range actualizedRewardProjectionAggregates.RewardProjectionOptionsAggregate {
				if rewardContributionOptionAggregate.RewardType == chosenReward.GetRewardType() {
					claimedProjectionRewardsMap[rewardContributionOptionAggregate.RewardType] += rewardContributionOptionAggregate.RewardUnits
					break
				}
			}
		} else {
			for _, rewardContributionOptionAggregate := range actualizedRewardProjectionAggregates.RewardProjectionOptionsAggregate {
				unclaimedProjectionRewardsMap[rewardContributionOptionAggregate.RewardType] += rewardContributionOptionAggregate.RewardUnits
			}
		}
	}

	claimedProjectionRewards := make([]*rewardsPinotPb.RewardProjectionOption, 0)
	unclaimedProjectionRewards := make([]*rewardsPinotPb.RewardProjectionOption, 0)

	for rewardType, rewardUnits := range claimedProjectionRewardsMap {
		claimedProjectionRewards = append(claimedProjectionRewards, &rewardsPinotPb.RewardProjectionOption{
			RewardType:  rewardType,
			RewardUnits: rewardUnits,
		})
	}

	for rewardType, rewardUnits := range unclaimedProjectionRewardsMap {
		unclaimedProjectionRewards = append(unclaimedProjectionRewards, &rewardsPinotPb.RewardProjectionOption{
			RewardType:  rewardType,
			RewardUnits: rewardUnits,
		})
	}
	return claimedProjectionRewards, unclaimedProjectionRewards, nil
}

// GetUnActualizedRewardProjectionsAggregates generates aggregates for un-actualized reward projections for given actor
// i.e. this will only contain the aggregates for reward projections for which reward has not yet been generated.
func (s *Service) GetUnActualizedRewardProjectionsAggregates(ctx context.Context, req *rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesRequest) (*rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesResponse, error) {
	if req.GetActorId() == "" {
		return &rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty actorId field")}, nil
	}

	if req.GetRewardType() == rewardsPb.RewardType_REWARD_TYPE_UNSPECIFIED {
		return &rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty rewardType field")}, nil
	}

	// --- Validate and extract the flattened out Time Range ---
	timeRangeType, fromTime, toTime, err := req.GetTimeRange().ValidateAndFlatten()
	if err != nil {
		logger.Error(ctx, "invalid time range parameters", zap.Error(err))
		return &rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg(err.Error())}, nil
	}

	// --- Create Filter ---
	filter := newRewardProjectionFilterFromRequest(req.GetFilters(), false) // isActualized = false

	// Populate time fields using helper
	if err := populateTimeFilter(filter, timeRangeType, fromTime, toTime); err != nil {
		logger.Error(ctx, "error populating time filter", zap.Error(err), zap.Any("timeRangeType", timeRangeType))
		return &rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
	}

	unActualizedRewardProjectionsAggregates, err := s.rewardProjectionsDao.GetRewardProjectionsAggregatesForActorId(ctx, req.GetActorId(), filter)
	if err != nil {
		logger.Error(ctx, "error while fetching reward projections aggregates from GetRewardProjectionsAggregatesForActorId dao",
			zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
	}

	// Call the helper to aggregate and get the single result object
	rewardProjectionAggregateResult := getRewardProjectionsAggregateFromUnActualizedRewardProjections(unActualizedRewardProjectionsAggregates, req.GetRewardType())

	return &rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesResponse{
		Status:                      rpcPb.StatusOk(),
		RewardProjectionsAggregates: rewardProjectionAggregateResult, // Use the result from the helper
	}, nil
}

func getRewardProjectionsAggregateFromUnActualizedRewardProjections(unActualizedRewardProjectionsAggregates []*model.RewardProjectionsAggregate, rewardType rewardsPb.RewardType) *rewardsPinotPb.RewardProjectionOption {
	var rewardUnits float64

	// iterate over all the un-actualized reward projections and aggregate the reward units for each reward type
	for _, unActualizedRewardProjectionsAggregate := range unActualizedRewardProjectionsAggregates {
		for _, rewardContributionOption := range unActualizedRewardProjectionsAggregate.RewardProjectionOptionsAggregate {
			if rewardContributionOption.RewardType == rewardType {
				rewardUnits += rewardContributionOption.RewardUnits
			}
		}
	}

	// Return a pointer to a single result struct
	return &rewardsPinotPb.RewardProjectionOption{
		RewardType:  rewardType,
		RewardUnits: rewardUnits,
	}
}

// GetCustomUnActualizedRewardProjectionsAggregates handles requests for specialized projection aggregations.
func (s *Service) GetCustomUnActualizedRewardProjectionsAggregates(ctx context.Context, req *rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest) (*rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse, error) {
	// --- Basic Validations ---
	if req.GetActorId() == "" {
		return &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty actorId field")}, nil
	}
	if req.GetRewardType() == rewardsPb.RewardType_REWARD_TYPE_UNSPECIFIED {
		return &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty rewardType field")}, nil
	}

	// --- Validate and extract the flattened out Time Range ---
	timeRangeType, fromTime, toTime, err := req.GetTimeRange().ValidateAndFlatten()
	if err != nil {
		logger.Error(ctx, "invalid top-level time range parameters", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid time_range: " + err.Error())}, nil
	}

	// --- Extract Custom Query ---
	customQuery := req.GetProjectionCustomQuery()
	if customQuery == nil {
		return &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("projection_custom_query field is required")}, nil
	}

	// --- Validate Custom Query Metadata ---
	if err := validateProjectionCustomQuery(customQuery); err != nil {
		// Use the specific error returned by the validator
		st, _ := status.FromError(err) // Get gRPC status
		logger.Error(ctx, "Invalid projection custom query", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		// Use the NewStatusWithoutDebug helper assuming it exists and takes code and message
		return &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.NewStatusWithoutDebug(uint32(st.Code()), st.Message())}, nil
	}

	// --- Handle Different Custom Query Types ---
	var finalAggregate *rewardsPinotPb.RewardProjectionOption

	switch customQuery.GetType() {
	case rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE:
		metadata := customQuery.GetTieringMonthlyProjectionsAggregateMetadata()
		// Build filter model specifically for this dao call
		// Include fields needed by the filter builder to generate the full WHERE clause.
		filter := &model.RewardProjectionAggregationFilter{
			IsActualized: false, // Tiering applies to un-actualized
			ActionTypes:  []rewardsPb.CollectedDataType{metadata.GetActionType()},
			RewardType:   req.GetRewardType(),      // Use reward type from request
			OfferTypes:   metadata.GetOfferTypes(), // Use offer type from metadata
		}

		// Populate time fields using helper (from top-level request time_range)
		if err := populateTimeFilter(filter, timeRangeType, fromTime, toTime); err != nil {
			logger.Error(ctx, "error populating time filter for custom query", zap.Error(err), zap.Any("timeRangeType", timeRangeType), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
		}

		// Call the specific dao method for tiering, passing the populated filter
		finalAggregate, err = s.rewardProjectionsDao.GetTieringMonthlyProjectionAggregates(ctx, req.GetActorId(), req.GetRewardType(), filter, metadata)
		if err != nil {
			logger.Error(ctx, "error calling GetTieringMonthlyProjectionAggregates dao",
				zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			// Consider mapping specific dao errors to gRPC status codes if needed
			return &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInternal()}, nil
		}

	default:
		logger.Error(ctx, "unsupported custom query type received", zap.String("queryType", customQuery.GetType().String()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		// Return InvalidArgument as the provided custom query type is not valid/supported
		return &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("unsupported custom query type: %v", customQuery.GetType()))}, nil
	}

	// --- Return Response ---
	return &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
		Status:                      rpcPb.StatusOk(),
		RewardProjectionsAggregates: finalAggregate, // Assign the result from the switch case
	}, nil
}

// validateProjectionCustomQuery checks if the custom query object and its specific metadata are valid.
func validateProjectionCustomQuery(customQuery *rewardsPinotPb.ProjectionCustomQuery) error {
	if customQuery == nil {
		// This should ideally be caught earlier, but double-check
		return status.Error(codes.InvalidArgument, "projection_custom_query is required")
	}

	switch customQuery.GetType() {
	case rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE:
		metadata := customQuery.GetTieringMonthlyProjectionsAggregateMetadata()
		if metadata == nil {
			return status.Error(codes.InvalidArgument, "tiering_monthly_projections_aggregate_metadata is required for this query type")
		}
		if metadata.GetOfferTypes() == nil || len(metadata.GetOfferTypes()) == 0 || lo.Contains(metadata.GetOfferTypes(), rewardsPb.RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE) {
			return status.Error(codes.InvalidArgument, "valid offer_types in tiering metadata is required")
		}
		if metadata.GetDailyLimit() < 0 {
			return status.Error(codes.InvalidArgument, "daily_limit in tiering metadata cannot be negative")
		}
		if metadata.GetMonthlyLimit() < 0 {
			return status.Error(codes.InvalidArgument, "monthly_limit in tiering metadata cannot be negative")
		}
		if metadata.GetMonthlyLimit() < metadata.GetDailyLimit() {
			return status.Error(codes.InvalidArgument, "monthly_limit cannot be less than daily_limit in tiering metadata")
		}
		return nil // Valid tiering metadata
	default:
		// Treat any other type as unsupported/invalid for now
		return status.Error(codes.InvalidArgument, fmt.Sprintf("unsupported projection_custom_query type: %v", customQuery.GetType()))
	}
}

// newRewardProjectionFilterFromRequest creates and populates a RewardProjectionAggregationFilter
// from the provided protobuf filters and the actualization status.
func newRewardProjectionFilterFromRequest(protoFilters *rewardsPinotPb.ProjectionFilters, isActualized bool) *model.RewardProjectionAggregationFilter {
	if protoFilters == nil {
		// Return a filter with only the actualization status if protoFilters is nil
		return &model.RewardProjectionAggregationFilter{
			IsActualized: isActualized,
		}
	}
	return &model.RewardProjectionAggregationFilter{
		IsActualized:        isActualized,
		AccountId:           protoFilters.GetAccountId(),
		ActionTypes:         protoFilters.GetActionTypes(),
		AccountTier:         protoFilters.GetAccountTier(),
		PaymentProtocol:     protoFilters.GetPaymentProtocol(),
		OrderWorkflow:       protoFilters.GetOrderWorkflow(),
		OfferTypes:          protoFilters.GetRewardOfferTypes(),
		OfferId:             protoFilters.GetOfferId(),
		MerchantIds:         protoFilters.GetMerchantIds(),
		MerchantNames:       protoFilters.GetMerchantNames(),
		IncludeOntologyIds:  protoFilters.GetIncludeOntologyIds(),
		ExcludeOntologyIds:  protoFilters.GetExcludeOntologyIds(),
		IncludeL0Ontologies: protoFilters.GetIncludeL0Ontologies(),
		ExcludeL0Ontologies: protoFilters.GetExcludeL0Ontologies(),
	}
}
