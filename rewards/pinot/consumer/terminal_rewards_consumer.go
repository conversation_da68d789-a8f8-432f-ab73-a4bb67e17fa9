package consumer

import (
	"context"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/events"
	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"
	"github.com/epifi/gamma/rewards/pinot/eventspecificpinotrewardinfo"
)

// ProcessTerminalRewardEventsForPinot is used to consume the terminal state reward (PROCESSED, EXPIRED)
// which are pushed to RewardStatusUpdateEvent sns topic,stitches other information
// and publishes them to the kafka stream for uploading to realtime table for terminal rewards
// The consumer also publishes the terminal event to rewards_non_terminal table with delete_entry marked as true
// to remove to soft-delete the entry from that table and synchronize terminal and non-terminal tables
func (rps *RewardsPinotService) ProcessTerminalRewardEventsForPinot(ctx context.Context, req *events.RewardStatusUpdateEvent) (*rewardsPinotPb.ConsumerResponse, error) {
	var (
		reward       = req.GetReward()
		refId        = reward.GetRefId()
		rewardId     = reward.GetId()
		rewardStatus = reward.GetStatus()
		rewardPinot  *rewardsPinotPb.Reward
		err          error
	)

	// filtering out non-terminal rewards for any future changes to RewardStatusUpdateEvent
	if !checkIfTerminalReward(rewardStatus) {
		logger.Debug(ctx, "non terminal reward received in ProcessTerminalRewardEventsForPinot, returning permanent failure", zap.Any("reward", reward), zap.String("rewardStatus", rewardStatus.String()))
		return getPermanentFailureRes(), nil
	}

	// fill reward details which are already available
	rewardPinot, err = rps.getRewardPinotDetails(ctx, reward)
	if err != nil {
		logger.Error(ctx, "error fetching reward pinot details from reward proto using getRewardPinotDetails", zap.String(logger.REWARD_ID, rewardId), zap.Error(err))
		return getTransientFailureRes(), nil
	}

	// fill event specific details
	err = rps.populateEventSpecificRewardDetails(ctx, rewardPinot, refId)
	if err != nil {
		logger.Error(ctx, "error fetching additional reward pinot details using populateEventSpecificRewardDetails", zap.String(logger.REWARD_ID, rewardId), zap.Error(err))
		return getTransientFailureRes(), nil
	}

	// publish the enriched rewardPinot to kafka live stream
	_, publishErr := rps.terminalPinotRewardProducer.PublishMessage(ctx, rewardPinot)
	if publishErr != nil {
		logger.Error(ctx, "error while publishing message using terminalPinotRewardProducer", zap.String(logger.REWARD_ID, rewardId), zap.Error(publishErr))
		return getTransientFailureRes(), nil
	}

	// publish the terminal rewardPinot with deleteEntry marked as true
	// to the rewards_non_terminal realtime table to remove the record
	rewardPinot.DeleteEntry = true
	_, publishErr = rps.nonTerminalPinotRewardProducer.PublishMessage(ctx, rewardPinot)
	if publishErr != nil {
		logger.Error(ctx, "error while publishing message using nonTerminalPinotRewardProducer(deleted marked)", zap.String(logger.REWARD_ID, rewardId), zap.Error(publishErr))
		return getTransientFailureRes(), nil
	}

	return getSuccessRes(), nil
}

func (rps *RewardsPinotService) getRewardPinotDetails(ctx context.Context, reward *rewardsPb.Reward) (*rewardsPinotPb.Reward, error) {
	var (
		err      error
		rewardId = reward.GetId()
		offerId  = reward.GetOfferId()
		actorId  = reward.GetActorId()
	)
	// filling pinot reward with available data from any reward
	rewardPinot := &rewardsPinotPb.Reward{
		Id:               reward.GetId(),
		ActorId:          reward.GetActorId(),
		OfferId:          reward.GetOfferId(),
		OfferType:        reward.GetOfferType(),
		ChosenRewardType: reward.GetChosenReward().GetRewardType(),
		ClaimType:        reward.GetClaimType(),
		Status:           reward.GetStatus(),
		SubStatus:        reward.GetSubStatusV2(),
		ActionType:       reward.GetActionType(),
		CreatedAt:        reward.GetCreatedAt().AsTime().Truncate(time.Second).UnixMilli(),
		UnlockDate:       reward.GetRewardOptions().GetUnlockDate().AsTime().Truncate(time.Second).UnixMilli(),
		UpdatedAt:        reward.GetUpdatedAt().AsTime().Truncate(time.Second).UnixMilli(),
		ActionTime:       reward.GetActionTime().AsTime().Truncate(time.Second).UnixMilli(),
	}

	// filling DeleteEntry if deleted_at is not null
	if reward.GetDeletedAt() != nil {
		rewardPinot.DeleteEntry = true
	}

	// filling the flattened out reward option fields
	rewardOptions := reward.GetRewardOptions().GetOptions()
	rewardOptionsLength := len(rewardOptions)

	if rewardOptionsLength > 0 {
		rewardOption := rewardOptions[0]
		rewardPinot.Option1RewardType = rewardOption.GetRewardType()
		rewardPinot.Option1RewardUnits, err = rewardsPb.GetRewardUnitsFromRewardOption(rewardOption)
		if err != nil {
			logger.Error(ctx, "error fetching reward option 1 value using GetRewardUnitsFromRewardOption", zap.String(logger.REWARD_ID, rewardId), zap.Error(err))
			return nil, fmt.Errorf("error fetching reward option 1 value using GetRewardUnitsFromRewardOption, err: %w", err)
		}
	}
	if rewardOptionsLength > 1 {
		rewardOption := rewardOptions[1]
		rewardPinot.Option2RewardType = rewardOption.GetRewardType()
		rewardPinot.Option2RewardUnits, err = rewardsPb.GetRewardUnitsFromRewardOption(rewardOption)
		if err != nil {
			logger.Error(ctx, "error fetching reward option 2 value using GetRewardUnitsFromRewardOption", zap.String(logger.REWARD_ID, rewardId), zap.Error(err))
			return nil, fmt.Errorf("error fetching reward option 2 value using GetRewardUnitsFromRewardOption, err: %w", err)
		}
	}
	if rewardOptionsLength > 2 {
		logger.WarnWithCtx(ctx, "reward option entry beyond supported number of reward options", zap.String(logger.REWARD_ID, rewardId))
	}

	// filling chosen reward units
	rewardPinot.ChosenRewardUnits, err = rewardsPb.GetRewardUnitsFromRewardOption(reward.GetChosenReward())
	if err != nil {
		logger.Error(ctx, "error fetching chosen reward option value using GetRewardUnitsFromRewardOption", zap.String(logger.REWARD_ID, rewardId), zap.Error(err))
		// filling the chosen reward units value
		return nil, fmt.Errorf("error fetching chosen reward option value using GetRewardUnitsFromRewardOption, err: %w", err)
	}

	// filling the unlock event by fetching reward offer
	rewardOffer, err := rps.rewardOfferDao.FetchRewardOfferById(ctx, offerId)
	if err != nil {
		logger.Error(ctx, "error fetching reward offer using FetchRewardOfferById", zap.String(logger.OFFER_ID, offerId), zap.Error(err))
		return nil, fmt.Errorf("error fetching reward offer using FetchRewardOfferById, err: %w", err)
	}
	rewardPinot.UnlockEvent = rewardOffer.GetUnlockEvent()

	// fetching account tier at action time
	rewardPinot.AccountTier, _, err = rps.userHelperService.GetAccountTierForActorAtTime(ctx, actorId, aws.Time(reward.GetActionTime().AsTime()))
	if err != nil {
		logger.Error(ctx, "error fetching account tier for the actor using GetAccountTierForActorAtTime", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("actionTime", reward.GetActionTime().String()), zap.Error(err))
		return nil, fmt.Errorf("error fetching account tier info for actor at action time, err: %w", err)
	}

	return rewardPinot, nil
}

// nolint:dupl
func (rps *RewardsPinotService) populateEventSpecificRewardDetails(ctx context.Context, rewardPinot *rewardsPinotPb.Reward, refId string) error {
	var (
		actorId         = rewardPinot.GetActorId()
		actionType      = rewardPinot.GetActionType()
		rewardOfferType = rewardPinot.GetOfferType()
	)

	// checking if the actionType is valid to fetch additional details
	if !checkIfValidActionTypeForAdditionalDetails(actionType) || checkIfInvalidRewardOfferTypeForAdditionalDetails(rewardOfferType) {
		return nil
	}

	// fetching EventSpecificPinotRewardInfo implementation for the respective actionType
	fetchEventSpecificPinotReward, err := rps.iRewardsPinotEventSpecificInfoFactory.Get(ctx, actionType)
	if err != nil {
		logger.Error(ctx, "error fetching EventSpecificPinotRewardInfo implementation for rewards pinot consumer", zap.String(logger.REFERENCE_ID, refId), zap.String(logger.ACTION_TYPE, actionType.String()), zap.Error(err))
		return err
	}

	// fetching additional details for the pinot reward
	additionalDetails, err := fetchEventSpecificPinotReward.GetAdditionalDetails(ctx, eventspecificpinotrewardinfo.AdditionalDetailsReq{
		ExternalId: refId,
		ActorId:    actorId,
		ActionType: actionType,
	})
	if err != nil {
		logger.Error(ctx, "error fetching additional reward information for the event", zap.String(logger.REFERENCE_ID, refId), zap.String(logger.ACTION_TYPE, actionType.String()), zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return err
	}
	rewardPinot.MerchantName = additionalDetails.GetMerchantName()
	rewardPinot.MerchantId = additionalDetails.GetMerchantId()
	rewardPinot.PaymentProtocol = additionalDetails.GetPaymentProtocol()
	rewardPinot.DsOntologyIds = additionalDetails.GetOntologyDetails().GetDsOntologyIds()
	rewardPinot.DsL0Ontologies = additionalDetails.GetOntologyDetails().GetDsL0Ontologies()
	rewardPinot.OrderWorkflow = additionalDetails.GetOrderWorkflow()

	return nil
}
