package pinot

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"sort"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/datetime/mocks"

	fireflyPb "github.com/epifi/gamma/api/firefly"
	fireflyMocks "github.com/epifi/gamma/api/firefly/mocks"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	mockRewardsGenerator "github.com/epifi/gamma/api/rewards/mocks"
	rewardsPinotPb "github.com/epifi/gamma/api/rewards/pinot"

	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	mockRewardsPinotDao "github.com/epifi/gamma/rewards/pinot/dao/mocks"
	"github.com/epifi/gamma/rewards/pinot/dao/model"
	"github.com/epifi/gamma/rewards/test"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
// nolint: dogsled
func TestMain(m *testing.M) {
	_, _, _, teardown := test.InitTestServer(false)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestService_GetRewardsAggregates(t *testing.T) {
	var (
		migrationTime = accrualPkg.GetFiCoinsToFiPointsMigrationTime()
		actorID       = "actor-1"
	)

	type args struct {
		ctx context.Context
		req *rewardsPinotPb.GetRewardsAggregatesRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(*mockRewardsPinotDao.MockTerminalRewardsDao, *fireflyMocks.MockFireflyClient, *mocks.MockTime)
		want       *rewardsPinotPb.GetRewardsAggregatesResponse
		wantErr    bool
	}{
		{
			name: "pre-migration: directly returns aggregates",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(-1 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, _ *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(-24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, gomock.Any()).Return([]*model.RewardOptionAggregate{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100, RewardCount: 2}}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status: rpcPb.StatusOk(),
				RewardOptionAggregates: []*rewardsPinotPb.RewardOptionAggregate{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: 100,
						RewardCount: 2,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "post-migration: directly returns aggregates as MergeFiCoinsToFiPoints is false",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: false,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, gomock.Any()).Return([]*model.RewardOptionAggregate{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100, RewardCount: 2}}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status:                 rpcPb.StatusOk(),
				RewardOptionAggregates: []*rewardsPinotPb.RewardOptionAggregate{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100, RewardCount: 2}},
			},
			wantErr: false,
		},
		{
			name: "post-migration: merges pre and post aggregates while converting FI_COINS to FI_POINTS valuation",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime.Add(-7 * 24 * time.Hour).In(time.UTC),
					ToActionTime:   migrationTime,
				}).Return([]*model.RewardOptionAggregate{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100, RewardCount: 2}}, nil)
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime,
					ToActionTime:   migrationTime.Add(7 * 24 * time.Hour).In(time.UTC),
				}).Return([]*model.RewardOptionAggregate{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 50, RewardCount: 1}}, nil)
				mockFirefly.EXPECT().IsCreditCardUser(gomock.Any(), gomock.Any()).Return(&fireflyPb.IsCreditCardUserResponse{
					Status:           rpcPb.StatusOk(),
					IsCreditCardUser: true,
				}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status:                 rpcPb.StatusOk(),
				RewardOptionAggregates: []*rewardsPinotPb.RewardOptionAggregate{{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: float64(accrualPkg.ConvertFiCoinsToFiPoints(100, true)) + 50, RewardCount: 3}},
			},
			wantErr: false,
		},
		{
			name: "post-migration: merges pre and post aggregates without any FI_COINS aggregate present",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime.Add(-7 * 24 * time.Hour).In(time.UTC),
					ToActionTime:   migrationTime,
				}).Return([]*model.RewardOptionAggregate{{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 10, RewardCount: 1}}, nil)
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime,
					ToActionTime:   migrationTime.Add(7 * 24 * time.Hour).In(time.UTC),
				}).Return([]*model.RewardOptionAggregate{{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 5, RewardCount: 2}}, nil)
				mockFirefly.EXPECT().IsCreditCardUser(gomock.Any(), gomock.Any()).Return(&fireflyPb.IsCreditCardUserResponse{
					Status:           rpcPb.StatusOk(),
					IsCreditCardUser: false,
				}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status: rpcPb.StatusOk(),
				RewardOptionAggregates: []*rewardsPinotPb.RewardOptionAggregate{
					{
						RewardType:  rewardsPb.RewardType_CASH,
						RewardUnits: 15,
						RewardCount: 3,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "post-migration: pre-migration DAO error returns internal",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime.Add(-7 * 24 * time.Hour).In(time.UTC),
					ToActionTime:   migrationTime,
				}).Return(nil, fmt.Errorf("dao error"))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime,
					ToActionTime:   migrationTime.Add(7 * 24 * time.Hour).In(time.UTC),
				})
				mockFirefly.EXPECT().IsCreditCardUser(gomock.Any(), gomock.Any()).Return(&fireflyPb.IsCreditCardUserResponse{
					Status:           rpcPb.StatusOk(),
					IsCreditCardUser: false,
				}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "post-migration: post-migration DAO error returns internal",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime.Add(-7 * 24 * time.Hour).In(time.UTC),
					ToActionTime:   migrationTime,
				})
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime,
					ToActionTime:   migrationTime.Add(7 * 24 * time.Hour).In(time.UTC),
				}).Return(nil, fmt.Errorf("dao error"))
				mockFirefly.EXPECT().IsCreditCardUser(gomock.Any(), gomock.Any()).Return(&fireflyPb.IsCreditCardUserResponse{
					Status:           rpcPb.StatusOk(),
					IsCreditCardUser: false,
				}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "post-migration: firefly error returns internal",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime.Add(-7 * 24 * time.Hour).In(time.UTC),
					ToActionTime:   migrationTime,
				}).Return([]*model.RewardOptionAggregate{{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 10, RewardCount: 1}}, nil)
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime,
					ToActionTime:   migrationTime.Add(7 * 24 * time.Hour).In(time.UTC),
				}).Return([]*model.RewardOptionAggregate{{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 5, RewardCount: 2}}, nil)
				mockFirefly.EXPECT().IsCreditCardUser(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("rpc error"))
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
		{
			name: "post-migration: multiple reward types in pre and post aggregates",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime.Add(-7 * 24 * time.Hour).In(time.UTC),
					ToActionTime:   migrationTime,
				}).Return([]*model.RewardOptionAggregate{
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100, RewardCount: 2},
					{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 20, RewardCount: 1},
				}, nil)
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime,
					ToActionTime:   migrationTime.Add(7 * 24 * time.Hour).In(time.UTC),
				}).Return([]*model.RewardOptionAggregate{
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 50, RewardCount: 1},
					{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 10, RewardCount: 2},
				}, nil)
				mockFirefly.EXPECT().IsCreditCardUser(gomock.Any(), gomock.Any()).Return(&fireflyPb.IsCreditCardUserResponse{
					Status:           rpcPb.StatusOk(),
					IsCreditCardUser: true,
				}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status: rpcPb.StatusOk(),
				RewardOptionAggregates: []*rewardsPinotPb.RewardOptionAggregate{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: float64(accrualPkg.ConvertFiCoinsToFiPoints(100, true)) + 50,
						RewardCount: 3,
					},
					{
						RewardType:  rewardsPb.RewardType_CASH,
						RewardUnits: 30,
						RewardCount: 3,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "post-migration: no aggregates returned (empty slice)",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime.Add(-7 * 24 * time.Hour).In(time.UTC),
					ToActionTime:   migrationTime,
				}).Return([]*model.RewardOptionAggregate{}, nil)
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime,
					ToActionTime:   migrationTime.Add(7 * 24 * time.Hour).In(time.UTC),
				}).Return([]*model.RewardOptionAggregate{}, nil)
				mockFirefly.EXPECT().IsCreditCardUser(gomock.Any(), gomock.Any()).Return(&fireflyPb.IsCreditCardUserResponse{
					Status:           rpcPb.StatusOk(),
					IsCreditCardUser: false,
				}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status:                 rpcPb.StatusOk(),
				RewardOptionAggregates: []*rewardsPinotPb.RewardOptionAggregate{},
			},
			wantErr: false,
		},
		{
			name: "post-migration: only pre-migration aggregates",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime.Add(-7 * 24 * time.Hour).In(time.UTC),
					ToActionTime:   migrationTime,
				}).Return([]*model.RewardOptionAggregate{
					{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 10, RewardCount: 1},
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100, RewardCount: 1},
				}, nil)
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime,
					ToActionTime:   migrationTime.Add(7 * 24 * time.Hour).In(time.UTC),
				}).Return([]*model.RewardOptionAggregate{}, nil)
				mockFirefly.EXPECT().IsCreditCardUser(gomock.Any(), gomock.Any()).Return(&fireflyPb.IsCreditCardUserResponse{
					Status:           rpcPb.StatusOk(),
					IsCreditCardUser: false,
				}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status: rpcPb.StatusOk(),
				RewardOptionAggregates: []*rewardsPinotPb.RewardOptionAggregate{
					{
						RewardType:  rewardsPb.RewardType_CASH,
						RewardUnits: 10,
						RewardCount: 1,
					},
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: float64(accrualPkg.ConvertFiCoinsToFiPoints(100, false)),
						RewardCount: 1,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "post-migration: only post-migration aggregates",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime.Add(-7 * 24 * time.Hour).In(time.UTC),
					ToActionTime:   migrationTime,
				}).Return([]*model.RewardOptionAggregate{}, nil)
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime,
					ToActionTime:   migrationTime.Add(7 * 24 * time.Hour).In(time.UTC),
				}).Return([]*model.RewardOptionAggregate{
					{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 5, RewardCount: 2},
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100, RewardCount: 1},
				}, nil)
				mockFirefly.EXPECT().IsCreditCardUser(gomock.Any(), gomock.Any()).Return(&fireflyPb.IsCreditCardUserResponse{
					Status:           rpcPb.StatusOk(),
					IsCreditCardUser: false,
				}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status: rpcPb.StatusOk(),
				RewardOptionAggregates: []*rewardsPinotPb.RewardOptionAggregate{
					{RewardType: rewardsPb.RewardType_CASH, RewardUnits: 5, RewardCount: 2},
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100, RewardCount: 1},
				},
			},
			wantErr: false,
		},
		{
			name: "post-migration: filter for only FI_COINS",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetRewardsAggregatesRequest{
					ActorId: actorID,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type: rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME,
						Range: &rewardsPinotPb.TimeRange{
							From: timestampPb.New(migrationTime.Add(-7 * 24 * time.Hour)),
							To:   timestampPb.New(migrationTime.Add(7 * 24 * time.Hour)),
						},
					},
					Filters: &rewardsPinotPb.Filters{
						RewardType: rewardsPb.RewardType_FI_COINS,
						MiscellaneousFilters: &rewardsPinotPb.MiscellaneousFilters{
							MergeFiCoinsToFiPoints: true,
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockFirefly *fireflyMocks.MockFireflyClient, mockTime *mocks.MockTime) {
				mockTime.EXPECT().Now().Return(migrationTime.Add(24 * time.Hour))
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime.Add(-7 * 24 * time.Hour).In(time.UTC),
					ToActionTime:   migrationTime,
					RewardType:     rewardsPb.RewardType_FI_COINS,
				}).Return([]*model.RewardOptionAggregate{
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 100, RewardCount: 2},
				}, nil)
				mockTerminalRewardsDao.EXPECT().GetRewardAggregatesForActorId(gomock.Any(), actorID, &model.RewardAggregationFilter{
					FromActionTime: migrationTime,
					ToActionTime:   migrationTime.Add(7 * 24 * time.Hour).In(time.UTC),
					RewardType:     rewardsPb.RewardType_FI_COINS,
				}).Return([]*model.RewardOptionAggregate{
					{RewardType: rewardsPb.RewardType_FI_COINS, RewardUnits: 50, RewardCount: 1},
				}, nil)
				mockFirefly.EXPECT().IsCreditCardUser(gomock.Any(), gomock.Any()).Return(&fireflyPb.IsCreditCardUserResponse{
					Status:           rpcPb.StatusOk(),
					IsCreditCardUser: true,
				}, nil)
			},
			want: &rewardsPinotPb.GetRewardsAggregatesResponse{
				Status: rpcPb.StatusOk(),
				RewardOptionAggregates: []*rewardsPinotPb.RewardOptionAggregate{
					{
						RewardType:  rewardsPb.RewardType_FI_COINS,
						RewardUnits: float64(accrualPkg.ConvertFiCoinsToFiPoints(100, true)) + 50,
						RewardCount: 2 + 1,
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockTerminalRewardsDao := mockRewardsPinotDao.NewMockTerminalRewardsDao(ctr)
			mockFirefly := fireflyMocks.NewMockFireflyClient(ctr)
			mockTime := mocks.NewMockTime(ctr)

			tt.setupMocks(mockTerminalRewardsDao, mockFirefly, mockTime)

			pinotService := NewService(mockTerminalRewardsDao, nil, nil, mockFirefly, mockTime)
			got, err := pinotService.GetRewardsAggregates(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRewardsAggregates() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			sort.Slice(got.GetRewardOptionAggregates(), func(i, j int) bool {
				return got.GetRewardOptionAggregates()[i].GetRewardType() > got.GetRewardOptionAggregates()[j].GetRewardType()
			})

			sort.Slice(tt.want.GetRewardOptionAggregates(), func(i, j int) bool {
				return tt.want.GetRewardOptionAggregates()[i].GetRewardType() > tt.want.GetRewardOptionAggregates()[j].GetRewardType()
			})

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRewardsAggregates() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetActualizedRewardProjectionsAggregates(t *testing.T) {
	reward1 := &rewardsPb.Reward{
		Id: "reward-1",
		ChosenReward: &rewardsPb.RewardOption{
			RewardType: rewardsPb.RewardType_CASH,
		},
	}
	reward2 := &rewardsPb.Reward{
		Id: "reward-2",
		ChosenReward: &rewardsPb.RewardOption{
			RewardType: rewardsPb.RewardType_FI_COINS,
		},
	}
	reward3 := &rewardsPb.Reward{
		Id: "reward-3",
	}
	actor1AggregateDaoResponse := []*model.RewardProjectionsAggregate{
		{
			GeneratedRewardId: "reward-1",
			RewardProjectionOptionsAggregate: []*model.RewardProjectionOption{
				{
					RewardType:  rewardsPb.RewardType_CASH,
					RewardUnits: 10,
				},
				{
					RewardType:  rewardsPb.RewardType_FI_COINS,
					RewardUnits: 500,
				},
			},
		},
		{
			GeneratedRewardId: "reward-2",
			RewardProjectionOptionsAggregate: []*model.RewardProjectionOption{
				{
					RewardType:  rewardsPb.RewardType_CASH,
					RewardUnits: 5,
				},
				{
					RewardType:  rewardsPb.RewardType_FI_COINS,
					RewardUnits: 250,
				},
			},
		},
		{
			GeneratedRewardId: "reward-3",
			RewardProjectionOptionsAggregate: []*model.RewardProjectionOption{
				{
					RewardType:  rewardsPb.RewardType_CASH,
					RewardUnits: 50,
				},
				{
					RewardType:  rewardsPb.RewardType_FI_COINS,
					RewardUnits: 2500,
				},
			},
		},
	}

	type args struct {
		ctx context.Context
		req *rewardsPinotPb.GetActualizedRewardProjectionsAggregatesRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(*mockRewardsPinotDao.MockTerminalRewardsDao, *mockRewardsPinotDao.MockRewardProjectionsDao, *mockRewardsGenerator.MockRewardsGeneratorClient)
		want       *rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse
		wantErr    bool
	}{
		{
			name: "rpc returns correct aggregates for actor-1",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetActualizedRewardProjectionsAggregatesRequest{
					ActorId:   "actor-1",
					TimeRange: rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 29, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao, mockRewardsClient *mockRewardsGenerator.MockRewardsGeneratorClient) {
				mockRewardProjectionsDao.EXPECT().GetRewardProjectionsAggregatesForActorId(gomock.Any(), "actor-1", gomock.Any()).Return(
					actor1AggregateDaoResponse, nil)
				mockRewardsClient.EXPECT().GetRewards(gomock.Any(), &rewardsPb.GetRewardsRequest{
					Ids: []string{"reward-1", "reward-2", "reward-3"},
				}).Return(&rewardsPb.GetRewardsResponse{
					Status: rpcPb.StatusOk(),
					Rewards: []*rewardsPb.Reward{
						reward1, reward2, reward3,
					},
				}, nil)
			},
			want: &rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusOk(),
				RewardProjectionsAggregates: &rewardsPinotPb.ActualizedRewardProjectionsAggregate{
					ClaimedRewardProjections: []*rewardsPinotPb.RewardProjectionOption{
						{
							RewardType:  rewardsPb.RewardType_FI_COINS,
							RewardUnits: 250,
						},
						{
							RewardType:  rewardsPb.RewardType_CASH,
							RewardUnits: 10,
						},
					},
					UnclaimedRewardProjections: []*rewardsPinotPb.RewardProjectionOption{
						{
							RewardType:  rewardsPb.RewardType_CASH,
							RewardUnits: 50,
						},
						{
							RewardType:  rewardsPb.RewardType_FI_COINS,
							RewardUnits: 2500,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "rpc returns internal response when projection aggregate dao call fails",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetActualizedRewardProjectionsAggregatesRequest{
					ActorId:   "actor-1",
					TimeRange: rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 29, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao, mockRewardsClient *mockRewardsGenerator.MockRewardsGeneratorClient) {
				mockRewardProjectionsDao.EXPECT().GetRewardProjectionsAggregatesForActorId(gomock.Any(), "actor-1", gomock.Any()).Return(
					nil, fmt.Errorf("error in GetActualizedRewardProjectionsAggregatesForActorId dao call"))
			},
			want: &rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			var (
				mockTerminalRewardsDao   = mockRewardsPinotDao.NewMockTerminalRewardsDao(ctr)
				mockRewardProjectionsDao = mockRewardsPinotDao.NewMockRewardProjectionsDao(ctr)
				mockRewardsClient        = mockRewardsGenerator.NewMockRewardsGeneratorClient(ctr)
			)

			tt.setupMocks(mockTerminalRewardsDao, mockRewardProjectionsDao, mockRewardsClient)

			pinotService := NewService(mockTerminalRewardsDao, mockRewardProjectionsDao, mockRewardsClient, nil, nil)
			got, err := pinotService.GetActualizedRewardProjectionsAggregates(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetActualizedRewardProjectionsAggregates() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !compareActualizedRewardProjectionsAggregate(got, tt.want) {
				t.Errorf("GetActualizedRewardProjectionsAggregates() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func compareActualizedRewardProjectionsAggregate(gotRes, wantRes *rewardsPinotPb.GetActualizedRewardProjectionsAggregatesResponse) bool {
	if !reflect.DeepEqual(gotRes.GetStatus(), wantRes.GetStatus()) {
		return false
	}

	var (
		got  = gotRes.GetRewardProjectionsAggregates()
		want = wantRes.GetRewardProjectionsAggregates()
	)
	if len(got.GetClaimedRewardProjections()) != len(want.GetClaimedRewardProjections()) || len(got.GetUnclaimedRewardProjections()) != len(want.GetUnclaimedRewardProjections()) {
		return false
	}

	// Sort both slices before comparison
	sort.Slice(got.GetClaimedRewardProjections(), func(i, j int) bool {
		return got.GetClaimedRewardProjections()[i].GetRewardType() > got.GetClaimedRewardProjections()[j].GetRewardType()
	})
	sort.Slice(want.GetClaimedRewardProjections(), func(i, j int) bool {
		return want.GetClaimedRewardProjections()[i].GetRewardType() > want.GetClaimedRewardProjections()[j].GetRewardType()
	})
	sort.Slice(got.GetUnclaimedRewardProjections(), func(i, j int) bool {
		return got.GetUnclaimedRewardProjections()[i].GetRewardType() > got.GetUnclaimedRewardProjections()[j].GetRewardType()
	})
	sort.Slice(want.GetUnclaimedRewardProjections(), func(i, j int) bool {
		return want.GetUnclaimedRewardProjections()[i].GetRewardType() > want.GetUnclaimedRewardProjections()[j].GetRewardType()
	})

	for i := range got.GetClaimedRewardProjections() {
		if !reflect.DeepEqual(got.GetClaimedRewardProjections()[i], want.GetClaimedRewardProjections()[i]) {
			return false
		}
	}

	for i := range got.GetUnclaimedRewardProjections() {
		if !reflect.DeepEqual(got.GetUnclaimedRewardProjections()[i], want.GetUnclaimedRewardProjections()[i]) {
			return false
		}
	}

	return true
}

func TestService_GetUnActualizedRewardProjectionsAggregates(t *testing.T) {
	actor1AggregateDaoResponse := []*model.RewardProjectionsAggregate{
		{
			RewardProjectionOptionsAggregate: []*model.RewardProjectionOption{
				{
					RewardType:  rewardsPb.RewardType_CASH,
					RewardUnits: 10,
				},
				{
					RewardType:  rewardsPb.RewardType_FI_COINS,
					RewardUnits: 500,
				},
			},
		},
		{
			RewardProjectionOptionsAggregate: []*model.RewardProjectionOption{
				{
					RewardType:  rewardsPb.RewardType_CASH,
					RewardUnits: 5,
				},
				{
					RewardType:  rewardsPb.RewardType_FI_COINS,
					RewardUnits: 250,
				},
			},
		},
		{
			RewardProjectionOptionsAggregate: []*model.RewardProjectionOption{
				{
					RewardType:  rewardsPb.RewardType_CASH,
					RewardUnits: 50,
				},
				{
					RewardType:  rewardsPb.RewardType_FI_COINS,
					RewardUnits: 2500,
				},
			},
		},
	}

	type args struct {
		ctx context.Context
		req *rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(*mockRewardsPinotDao.MockTerminalRewardsDao, *mockRewardsPinotDao.MockRewardProjectionsDao)
		want       *rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesResponse
		wantErr    bool
	}{
		{
			name: "rpc returns correct aggregates for actor-1",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_FI_COINS,
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 29, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				mockRewardProjectionsDao.EXPECT().GetRewardProjectionsAggregatesForActorId(gomock.Any(), "actor-1", gomock.Any()).Return(
					actor1AggregateDaoResponse, nil)
			},
			want: &rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusOk(),
				RewardProjectionsAggregates: &rewardsPinotPb.RewardProjectionOption{
					RewardType:  rewardsPb.RewardType_FI_COINS,
					RewardUnits: 3250,
				},
			},
			wantErr: false,
		},
		{
			name: "rpc returns internal response when projection aggregate dao call fails",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_FI_COINS,
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 29, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				mockRewardProjectionsDao.EXPECT().GetRewardProjectionsAggregatesForActorId(gomock.Any(), "actor-1", gomock.Any()).Return(
					nil, fmt.Errorf("error in GetActualizedRewardProjectionsAggregatesForActorId dao call"))
			},
			want: &rewardsPinotPb.GetUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			var (
				mockTerminalRewardsDao   = mockRewardsPinotDao.NewMockTerminalRewardsDao(ctr)
				mockRewardProjectionsDao = mockRewardsPinotDao.NewMockRewardProjectionsDao(ctr)
			)

			tt.setupMocks(mockTerminalRewardsDao, mockRewardProjectionsDao)

			pinotService := NewService(mockTerminalRewardsDao, mockRewardProjectionsDao, nil, nil, nil)
			got, err := pinotService.GetUnActualizedRewardProjectionsAggregates(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetUnActualizedRewardProjectionsAggregates() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetUnActualizedRewardProjectionsAggregates() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetCustomUnActualizedRewardProjectionsAggregates(t *testing.T) {
	// Define expected reward projection option result for a successful tiering query
	tieringProjectionResult := &rewardsPinotPb.RewardProjectionOption{
		RewardType:  rewardsPb.RewardType_CASH,
		RewardUnits: 4500.0,
	}

	// Define a zero result for cases with no matching data
	zeroProjectionResult := &rewardsPinotPb.RewardProjectionOption{
		RewardType:  rewardsPb.RewardType_CASH,
		RewardUnits: 0.0,
	}

	type args struct {
		ctx context.Context
		req *rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(*mockRewardsPinotDao.MockTerminalRewardsDao, *mockRewardsPinotDao.MockRewardProjectionsDao)
		want       *rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse
		wantErr    bool
	}{
		{
			name: "rpc returns InvalidArgument for empty actor_id",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "", // Empty actor ID
					RewardType: rewardsPb.RewardType_CASH,
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 1, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   1000,
								MonthlyLimit: 5000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// No mocks needed as error checking is done before DAO calls
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty actorId field"),
			},
			wantErr: false,
		},
		{
			name: "rpc returns InvalidArgument for unspecified reward_type",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_REWARD_TYPE_UNSPECIFIED, // Unspecified reward type
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 1, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   1000,
								MonthlyLimit: 5000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// No mocks needed as error checking is done before DAO calls
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty rewardType field"),
			},
			wantErr: false,
		},
		{
			name: "rpc returns InvalidArgument for missing custom query",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:               "actor-1",
					RewardType:            rewardsPb.RewardType_CASH,
					TimeRange:             rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 1, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
					ProjectionCustomQuery: nil, // Missing custom query
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// No mocks needed as error checking is done before DAO calls
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("projection_custom_query field is required"),
			},
			wantErr: false,
		},
		{
			name: "rpc returns InvalidArgument for missing time range",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_CASH,
					TimeRange:  nil, // Missing time range
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   1000,
								MonthlyLimit: 5000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// No mocks needed as error checking is done before DAO calls
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid time_range: rpc error: code = InvalidArgument desc = time range filter cannot be nil"),
			},
			wantErr: false,
		},
		{
			name: "rpc returns InvalidArgument for time range with range field missing",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_CASH,
					TimeRange: &rewardsPinotPb.TimeRangeFilter{
						Type:  rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT,
						Range: nil, // Missing range field
					},
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   1000,
								MonthlyLimit: 5000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// No mocks needed as error checking is done before DAO calls
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid time_range: rpc error: code = InvalidArgument desc = a valid time range with 'from' and 'to' timestamps is required"),
			},
			wantErr: false,
		},
		{
			name: "rpc returns InvalidArgument for time range with 'from' timestamp after 'to' timestamp",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_CASH,
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 04, 1, 0, 0, 0, 0, time.UTC))),
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   1000,
								MonthlyLimit: 5000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// No mocks needed as error checking is done before DAO calls
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid time_range: rpc error: code = InvalidArgument desc = time range 'from' cannot be after 'to'"),
			},
			wantErr: false,
		},
		{
			name: "rpc returns InvalidArgument for unspecified time range type",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_CASH,
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_UNSPECIFIED, timestampPb.New(time.Date(2024, 04, 1, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   1000,
								MonthlyLimit: 5000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// No mocks needed as error checking is done before DAO calls
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid time_range: rpc error: code = InvalidArgument desc = time range type must not be UNSPECIFIED"),
			},
			wantErr: false,
		},
		{
			name: "rpc returns successful tiering projection aggregates",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_CASH,
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 1, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   1000,
								MonthlyLimit: 5000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// Expect GetTieringMonthlyProjectionAggregates to be called with correct parameters
				mockRewardProjectionsDao.EXPECT().GetTieringMonthlyProjectionAggregates(
					gomock.Any(),
					"actor-1",
					rewardsPb.RewardType_CASH,
					gomock.Any(), // We can't compare the filter struct directly as it has complex implementation details
					gomock.Any(), // Same for metadata - matching could be complex due to internal values
				).Return(tieringProjectionResult, nil)
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status:                      rpcPb.StatusOk(),
				RewardProjectionsAggregates: tieringProjectionResult,
			},
			wantErr: false,
		},
		{
			name: "rpc returns successful tiering projection aggregates with FI_COINS reward type",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_FI_COINS,
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 1, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   5000,
								MonthlyLimit: 25000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				fiCoinsResult := &rewardsPinotPb.RewardProjectionOption{
					RewardType:  rewardsPb.RewardType_FI_COINS,
					RewardUnits: 22500.0,
				}

				mockRewardProjectionsDao.EXPECT().GetTieringMonthlyProjectionAggregates(
					gomock.Any(),
					"actor-1",
					rewardsPb.RewardType_FI_COINS,
					gomock.Any(),
					gomock.Any(),
				).Return(fiCoinsResult, nil)
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusOk(),
				RewardProjectionsAggregates: &rewardsPinotPb.RewardProjectionOption{
					RewardType:  rewardsPb.RewardType_FI_COINS,
					RewardUnits: 22500.0,
				},
			},
			wantErr: false,
		},
		{
			name: "rpc returns successful tiering projection aggregates with ACTION_TIME range type",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_CASH,
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME, timestampPb.New(time.Date(2024, 04, 1, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   1000,
								MonthlyLimit: 5000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// Here we would expect the time filter to be populated with ActionTime instead of CreatedAt
				mockRewardProjectionsDao.EXPECT().GetTieringMonthlyProjectionAggregates(
					gomock.Any(),
					"actor-1",
					rewardsPb.RewardType_CASH,
					gomock.Any(),
					gomock.Any(),
				).Return(tieringProjectionResult, nil)
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status:                      rpcPb.StatusOk(),
				RewardProjectionsAggregates: tieringProjectionResult,
			},
			wantErr: false,
		},
		{
			name: "rpc returns zero rewards when no data matches the criteria",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_CASH,
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 1, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   1000,
								MonthlyLimit: 5000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// Return zero results
				mockRewardProjectionsDao.EXPECT().GetTieringMonthlyProjectionAggregates(
					gomock.Any(),
					"actor-1",
					rewardsPb.RewardType_CASH,
					gomock.Any(),
					gomock.Any(),
				).Return(zeroProjectionResult, nil)
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusOk(),
				RewardProjectionsAggregates: &rewardsPinotPb.RewardProjectionOption{
					RewardType:  rewardsPb.RewardType_CASH,
					RewardUnits: 0.0,
				},
			},
			wantErr: false,
		},
		{
			name: "rpc returns internal error when DAO call fails",
			args: args{
				ctx: context.Background(),
				req: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesRequest{
					ActorId:    "actor-1",
					RewardType: rewardsPb.RewardType_CASH,
					TimeRange:  rewardsPinotPb.NewTimeRangeFilter(rewardsPinotPb.TimeRangeType_TIME_RANGE_TYPE_CREATED_AT, timestampPb.New(time.Date(2024, 04, 1, 0, 0, 0, 0, time.UTC)), timestampPb.New(time.Date(2024, 05, 1, 0, 0, 0, 0, time.UTC))),
					ProjectionCustomQuery: &rewardsPinotPb.ProjectionCustomQuery{
						Type: rewardsPinotPb.ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE,
						Metadata: &rewardsPinotPb.ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata{
							TieringMonthlyProjectionsAggregateMetadata: &rewardsPinotPb.TieringMonthlyProjectionsAggregateMetadata{
								ActionType:   rewardsPb.CollectedDataType_ORDER,
								OfferTypes:   []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_PLUS_TIER_1_PERCENT_CASHBACK_OFFER},
								DailyLimit:   1000,
								MonthlyLimit: 5000,
							},
						},
					},
				},
			},
			setupMocks: func(mockTerminalRewardsDao *mockRewardsPinotDao.MockTerminalRewardsDao, mockRewardProjectionsDao *mockRewardsPinotDao.MockRewardProjectionsDao) {
				// Expect GetTieringMonthlyProjectionAggregates to return an error
				mockRewardProjectionsDao.EXPECT().GetTieringMonthlyProjectionAggregates(
					gomock.Any(),
					"actor-1",
					rewardsPb.RewardType_CASH,
					gomock.Any(),
					gomock.Any(),
				).Return(nil, fmt.Errorf("database error or query execution error"))
			},
			want: &rewardsPinotPb.GetCustomUnActualizedRewardProjectionsAggregatesResponse{
				Status: rpcPb.StatusInternal(),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			var (
				mockTerminalRewardsDao   = mockRewardsPinotDao.NewMockTerminalRewardsDao(ctr)
				mockRewardProjectionsDao = mockRewardsPinotDao.NewMockRewardProjectionsDao(ctr)
			)

			tt.setupMocks(mockTerminalRewardsDao, mockRewardProjectionsDao)

			pinotService := NewService(mockTerminalRewardsDao, mockRewardProjectionsDao, nil, nil, nil)
			got, err := pinotService.GetCustomUnActualizedRewardProjectionsAggregates(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetCustomUnActualizedRewardProjectionsAggregates() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCustomUnActualizedRewardProjectionsAggregates() got = %v, want %v", got, tt.want)
			}
		})
	}
}
