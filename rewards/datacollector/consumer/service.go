package consumer

import (
	"context"
	"errors"
	"fmt"
	"time"

	nudgePb "github.com/epifi/gamma/api/nudge"

	"github.com/samber/lo"
	"go.uber.org/zap"

	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	caPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/enums"
	caExternalPb "github.com/epifi/gamma/api/connected_account/external"
	depositpb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/firefly/accounting"
	creditCardConsumerPb "github.com/epifi/gamma/api/firefly/accounting/consumer"
	ffAccEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ffBillingEventsPb "github.com/epifi/gamma/api/firefly/billing/events"
	ffEvent "github.com/epifi/gamma/api/firefly/event"
	fitttpb "github.com/epifi/gamma/api/fittt"
	fitttActionpb "github.com/epifi/gamma/api/fittt/action"
	sportsEventPb "github.com/epifi/gamma/api/fittt/sports/events"
	orderPb "github.com/epifi/gamma/api/order"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	clawbackprocessorPb "github.com/epifi/gamma/api/rewards/clawbackprocessor"
	dataCollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	salaryprogramEventsPb "github.com/epifi/gamma/api/salaryprogram/events"
	savingsPb "github.com/epifi/gamma/api/savings"
	searchEventsPb "github.com/epifi/gamma/api/search/events"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	fireflyHelper "github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/config/genconf"
	"github.com/epifi/gamma/rewards/datacollector/datacollectionerrors"
	"github.com/epifi/gamma/rewards/datacollector/model"
	"github.com/epifi/gamma/rewards/helper"
	"github.com/epifi/gamma/rewards/metrics"
	wireTypes "github.com/epifi/gamma/rewards/wire/types"
)

var (
	// consumer response statuses
	getSuccessRes = func() *dataCollectorPb.ConsumerResponse {
		return &dataCollectorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS}}
	}
	getTransientFailureRes = func() *dataCollectorPb.ConsumerResponse {
		return &dataCollectorPb.ConsumerResponse{ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE}}
	}
)

type RewardsConsumerService struct {
	dataCollectorPb.UnimplementedConsumerServer

	dataCollectorEventPublisher            wireTypes.DataCollectorEventSqsPublisher
	dataCollectorEventDelayPublisher       wireTypes.DataCollectorEventSqsCustomDelayPublisher
	actorClient                            actorPb.ActorClient
	fitttClient                            fitttpb.FitttClient
	depositClient                          depositpb.DepositClient
	caClient                               caPb.ConnectedAccountClient
	clawbackPublisher                      wireTypes.RewardClawbackEventCollectedDataPublisher
	clawbackDelayPublisher                 wireTypes.RewardsClawbackEventCollectedDataCustomDelayPublisher
	ccAccountingClient                     accounting.AccountingClient
	txnCatClient                           categorizerPb.TxnCategorizerClient
	onboardingClient                       onbPb.OnboardingClient
	piClient                               piPb.PiClient
	rewardUnlockerEventPublisher           wireTypes.RewardsRewardUnlockerSqsPublisher
	projectionsGenerationEventSqsPublisher wireTypes.RewardsProjectionsGenerationEventSqsPublisher
	conf                                   *config.Config
	dynConf                                *genconf.Config
	cardClient                             cardPb.CardProvisioningClient
	savingsClient                          savingsPb.SavingsClient
	nudgeClient                            nudgePb.NudgeServiceClient
	userHelperClient                       helper.IUserHelperService
}

func NewRewardsConsumerService(
	dataCollectorEventPublisher wireTypes.DataCollectorEventSqsPublisher,
	dataCollectorEventDelayPublisher wireTypes.DataCollectorEventSqsCustomDelayPublisher,
	actorClient actorPb.ActorClient,
	fitttClient fitttpb.FitttClient,
	depositClient depositpb.DepositClient,
	caClient caPb.ConnectedAccountClient,
	clawbackPublisher wireTypes.RewardClawbackEventCollectedDataPublisher,
	clawbackDelayPublisher wireTypes.RewardsClawbackEventCollectedDataCustomDelayPublisher,
	ccAccountingClient accounting.AccountingClient,
	txnCatClient categorizerPb.TxnCategorizerClient,
	onboardingClient onbPb.OnboardingClient,
	piClient piPb.PiClient,
	rewardUnlockerEventPublisher wireTypes.RewardsRewardUnlockerSqsPublisher,
	projectionsGenerationEventSqsPublisher wireTypes.RewardsProjectionsGenerationEventSqsPublisher,
	conf *config.Config,
	dynConf *genconf.Config,
	cardClient cardPb.CardProvisioningClient,
	savingsClient savingsPb.SavingsClient,
	nudgeClient nudgePb.NudgeServiceClient,
	userHelperClient helper.IUserHelperService,
) *RewardsConsumerService {
	return &RewardsConsumerService{
		dataCollectorEventPublisher:            dataCollectorEventPublisher,
		dataCollectorEventDelayPublisher:       dataCollectorEventDelayPublisher,
		actorClient:                            actorClient,
		fitttClient:                            fitttClient,
		depositClient:                          depositClient,
		caClient:                               caClient,
		clawbackPublisher:                      clawbackPublisher,
		clawbackDelayPublisher:                 clawbackDelayPublisher,
		ccAccountingClient:                     ccAccountingClient,
		txnCatClient:                           txnCatClient,
		onboardingClient:                       onboardingClient,
		piClient:                               piClient,
		rewardUnlockerEventPublisher:           rewardUnlockerEventPublisher,
		projectionsGenerationEventSqsPublisher: projectionsGenerationEventSqsPublisher,
		conf:                                   conf,
		dynConf:                                dynConf,
		cardClient:                             cardClient,
		savingsClient:                          savingsClient,
		nudgeClient:                            nudgeClient,
		userHelperClient:                       userHelperClient,
	}
}

func (rcs *RewardsConsumerService) ProcessOrderUpdateEvents(ctx context.Context, req *orderPb.OrderUpdate) (*dataCollectorPb.ConsumerResponse, error) {
	orderId := req.GetOrderWithTransactions().GetOrder().GetId()
	// ignore order update event if there's no txn associated with it
	if len(req.GetOrderWithTransactions().GetTransactions()) == 0 {
		logger.Info(ctx, "ignored order update event as it includes no txn", zap.String(logger.ORDER_ID, orderId))
		return getSuccessRes(), nil
	}

	// check if reward generation is supported for given order workflow, otherwise ignore the event.
	orderWorkflow := req.GetOrderWithTransactions().GetOrder().GetWorkflow()
	if !IsOrderWorkflowPresentInList(orderWorkflow, helper.AllowedOrderWorkflowsForRewardGeneration) {
		logger.Info(ctx, "ignored order update event, order workflow not supported reward generation", zap.String(logger.ORDER_ID, orderId))
		return getSuccessRes(), nil
	}

	// check if order status is in one of the terminal states, otherwise ignore the event.
	orderStatus := req.GetOrderWithTransactions().GetOrder().GetStatus()
	if !IsOrderStatusPresentInList(orderStatus, helper.AllowedOrderStatusesForRewardGeneration) {
		logger.Info(ctx, "ignored order update event, order status not supported reward generation",
			zap.String(logger.ORDER_ID, orderId))
		return getSuccessRes(), nil
	}

	// if REWARD tag is present in order, it implies that order was initiated by rewards
	// and reward shouldn't be generated for such events. For now this tag is received only
	// for NO_OP create/close deposit order.
	if orderPb.IsTagExist(req.GetOrderWithTransactions().GetOrder().GetTags(), orderPb.OrderTag_REWARD) {
		logger.Info(ctx, "ignored order update event, order event contains REWARD tag",
			zap.String(logger.ORDER_ID, orderId))
		return getSuccessRes(), nil
	}

	// handle special cases
	// 1. For ADD_FUNDS workflow, order status SETTLED is the only terminal state, for any other order status event should be ignored.
	if req.GetOrderWithTransactions().GetOrder().GetWorkflow() == orderPb.OrderWorkflow_ADD_FUNDS && req.GetOrderWithTransactions().GetOrder().GetStatus() != orderPb.OrderStatus_SETTLED {
		logger.Info(ctx, "ignored order update event, ADD_FUNDS order event not in terminal state",
			zap.String(logger.ORDER_ID, orderId))
		return getSuccessRes(), nil
	}

	// By default, we want to prevent processing of TPAP txns for rewards.
	// Thus, adding a flag whose default value will preserve the existing behaviour.
	if !rcs.dynConf.Flags().EnableTPAPOrdersForRewardsProcessing() {
		// ignore TPAP order events
		// todo(growth-infra): remove this check if we want to consider TPAP txns for reward generation
		orderAccountRelation := req.GetOrderAccountRelation()
		var err error
		// fetch the account relation if it's unspecified in the event
		if orderAccountRelation == orderPb.OrderAccountRelation_ORDER_ACCOUNT_RELATION_UNSPECIFIED {
			orderAccountRelation, err = pay.IsOrderRelatedToInternalAccount(ctx, req.GetOrderWithTransactions(), rcs.piClient)
			if err != nil {
				logger.Error(ctx, "error while fetching order account relation for order event", zap.String(logger.ORDER_ID, orderId), zap.Error(err))
				if !errors.Is(err, epifierrors.ErrInvalidArgument) {
					return getTransientFailureRes(), nil
				}
			}
		}

		// NOTE: if removing this check, remove ORDER from getCollectedDataTypesEligibleForFiLite(), currently this check is blocking fi-lite users (since fi-lite users are can only do TPAP txns)
		if orderAccountRelation != orderPb.OrderAccountRelation_INTERNAL_ACCOUNT {
			logger.Info(ctx, "ignored TPAP order event", zap.String(logger.ORDER_ID, orderId), zap.String("orderAccountRelation", orderAccountRelation.String()))
			return getSuccessRes(), nil
		}
	}

	logger.Info(ctx, "Collected order update event", zap.String(logger.ORDER_ID, req.GetOrderWithTransactions().GetOrder().GetId()))
	orderCollectedData := &model.OrderCollectedData{
		OrderUpdate: req,
		Ctx:         ctx,
		FitttClient: rcs.fitttClient,
	}
	protoOrderCollectedData, err := orderCollectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting order collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(orderCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoOrderCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing order event collected data", zap.Error(err))
		recordDataCollectorCollectionError(orderCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err = rcs.projectionsGenerationEventSqsPublisher.Publish(ctx, protoOrderCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing order event collected data to projections generation queue", zap.Error(err))
		recordDataCollectorCollectionError(orderCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Debug(ctx, "Pushed order update event to projections generation queue", zap.String(logger.QUEUE_MESSAGE_ID, id))

	_, err = rcs.rewardUnlockerEventPublisher.Publish(ctx, protoOrderCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing order update event to unlocker", zap.Error(err))
		return getTransientFailureRes(), nil
	}

	metrics.MetricsRecorder.RecordDataCollectorCollectedData(orderCollectedData.GetDataType().String())

	return getSuccessRes(), nil
}

// TODO(rohanchougule): deprecate this once the new search event consumer is live
func (rcs *RewardsConsumerService) ProcessUserSearchEvents(ctx context.Context, req *searchEventsPb.UserSearchEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected search event", zap.String(logger.EVENT_ID, req.GetEventId()))

	collectedData := &model.UserSearchCollectedData{
		UserSearchEvent: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting user search collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing user search event collected data", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed search event", zap.String(logger.QUEUE_MESSAGE_ID, id))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(collectedData.GetDataType().String())

	return getSuccessRes(), nil
}

func (rcs *RewardsConsumerService) ProcessUserSearchEventV1(ctx context.Context, req *searchEventsPb.UserSearchEvent) (*dataCollectorPb.ConsumerResponse, error) {
	// TODO(rohanchougule): add biz logic
	return getSuccessRes(), nil
}

func (rcs *RewardsConsumerService) ProcessManualGiveawayEvents(ctx context.Context, req *dataCollectorPb.ManualGiveawayEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected manual giveaway event", zap.String(logger.EVENT_ID, req.GetEventId()), zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Any("event_time", req.GetEventTimestamp()))
	collectedData := &model.ManualGiveawayCollectedData{
		ManualGiveawayEvent: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting manual giveaway collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing manual giveaway event collected data", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed manual giveaway event", zap.String(logger.QUEUE_MESSAGE_ID, id))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(collectedData.GetDataType().String())

	return getSuccessRes(), nil
}

func (rcs *RewardsConsumerService) ProcessFitttActionExecutionUpdateEvents(ctx context.Context, req *fitttActionpb.ActionExecutionUpdate) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected fittt action execution update event", zap.String(logger.EVENT_ID, req.GetExecution().GetId()), zap.String(logger.ACTOR_ID, req.GetActorId()))

	// ignore action execution update event if it's not in terminal success state.
	if req.GetExecution().GetStatus() != fitttActionpb.ActionStatus_SUCCESS {
		logger.Info(ctx, "ignored fittt action execution update event, action status not in terminal success state", zap.String(logger.EVENT_ID, req.GetExecution().GetId()), zap.String("action_status", req.GetExecution().GetStatus().String()))
		return getSuccessRes(), nil
	}

	collectedData := &model.FitttCollectedData{
		ActionExecutionUpdate: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting fittt collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing fittt collected data", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed fittt collected data event", zap.String(logger.QUEUE_MESSAGE_ID, id))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(collectedData.GetDataType().String())

	return getSuccessRes(), nil
}

func (rcs *RewardsConsumerService) ProcessExtraInterestSdBonusPayoutEvents(ctx context.Context, req *dataCollectorPb.ExtraInterestSdBonusPayoutEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected extra interest sd bonus payout event", zap.String(logger.EVENT_ID, req.GetBonusPayoutId()), zap.String(logger.ACTOR_ID, req.GetActorId()))

	// fetch bonus payout details using bonus payout id
	bonusPayoutDetailsRes, err := rcs.depositClient.GetBonusPayoutDetails(ctx, &depositpb.GetBonusPayoutDetailsRequest{DepositAccountBonusPayoutId: req.GetBonusPayoutId()})
	if err != nil || !bonusPayoutDetailsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "depositClient.GetBonusPayoutDetails call failed", zap.String(logger.BONUS_PAYOUT_ID, req.GetBonusPayoutId()), zap.String(logger.ACTOR_ID, req.GetActorId()), zap.Any(logger.RPC_STATUS, bonusPayoutDetailsRes.GetStatus()), zap.Error(err))
		recordDataCollectorCollectionError(rewardsPb.CollectedDataType_EXTRA_INTEREST_SD_BONUS_PAYOUT, err)
		return getTransientFailureRes(), nil
	}
	bonusPayoutDetails := bonusPayoutDetailsRes.GetDepositAccountBonusPayout()

	// ignore bonus payout event if bonus payout is not valid, possible in scenarios where SD was pre-closed
	// and subsequent bonus payouts shouldn't happen as a result.
	if !bonusPayoutDetails.GetIsValid() {
		logger.Info(ctx, "ignored extra interest sd bonus payout event, bonus payout isValid flag is false", zap.String(logger.BONUS_PAYOUT_ID, req.GetBonusPayoutId()), zap.String(logger.ACTOR_ID, req.GetActorId()))
		return getSuccessRes(), nil
	}
	// todo (utkarsh) : instead of copying should we embed the bonus payout details proto inside event ?
	// enrich bonus payout event with additional details
	req.PayoutDate = bonusPayoutDetails.GetPayoutDate()
	req.BonusPayoutAmount = bonusPayoutDetails.GetBonusAmount()
	req.BonusSdAccountNumber = bonusPayoutDetails.GetDepositAccountNumber()
	req.BonusPayoutNumber = bonusPayoutDetails.GetPayoutNumber()

	collectedData := &model.ExtraInterestSdBonusPayoutCollectedData{
		ExtraInterestSdBonusPayoutEvent: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting extra interest bonus payout collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing extra interest bonus payout collected data", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed extra interest bonus payout collected data event", zap.String(logger.QUEUE_MESSAGE_ID, id))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(collectedData.GetDataType().String())

	return getSuccessRes(), nil
}

func (rcs *RewardsConsumerService) ProcessMinBalanceEvent(ctx context.Context, minBalanceEvent *dataCollectorPb.MinBalanceEvent) (*dataCollectorPb.ConsumerResponse, error) {
	// TODO(kunal): Filter out events as needed

	logger.Info(ctx, "Received min balance event",
		zap.String(logger.EVENT_ID, minBalanceEvent.GetRefId()),
		zap.String(logger.ACTOR_ID, minBalanceEvent.GetActorId()),
	)

	minBalanceCollectedData := &model.MinBalanceCollectedData{
		MinBalanceEvent: minBalanceEvent,
	}
	protoCollectedData, err := minBalanceCollectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting min balance collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(minBalanceCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	queueMessageID, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing min balance event collected data", zap.Error(err))
		recordDataCollectorCollectionError(minBalanceCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed min balance event", zap.String(logger.QUEUE_MESSAGE_ID, queueMessageID))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(minBalanceCollectedData.GetDataType().String())

	return getSuccessRes(), nil
}

func (rcs *RewardsConsumerService) ProcessCAAccountUpdateEvent(ctx context.Context, accountUpdateEvent *caExternalPb.AccountUpdateEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Received ca account update event",
		zap.String(logger.ACTOR_ID, accountUpdateEvent.GetActorId()),
		zap.String(logger.ACCOUNT_ID, accountUpdateEvent.GetAccountId()),
		zap.String("accountStatus", accountUpdateEvent.GetAccountStatus().String()),
		zap.String(logger.ACC_INSTRUMENT_TYPE, accountUpdateEvent.GetAccInstrumentType().String()),
	)

	if accountUpdateEvent.GetAccountStatus() == enums.AccountStatus_ACCOUNT_STATUS_DELETED {
		logger.Info(ctx, "account delete event received, no processing needed",
			zap.String(logger.ACTOR_ID, accountUpdateEvent.GetActorId()),
			zap.String(logger.ACCOUNT_ID, accountUpdateEvent.GetAccountId()),
		)
		return getSuccessRes(), nil
	}

	caAccountCollectedData := &model.CAAccountCollectedData{
		AccountUpdateEvent: accountUpdateEvent,
		Ctx:                ctx,
		CaClient:           rcs.caClient,
	}
	protoCollectedData, err := caAccountCollectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting ca account collected data model to proto",
			zap.Error(err), zap.Any("accountUpdateEvent", accountUpdateEvent),
		)
		recordDataCollectorCollectionError(caAccountCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	queueMessageId, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing ca account collected data", zap.Error(err),
			zap.String(logger.ACTOR_ID, accountUpdateEvent.GetActorId()), zap.String(logger.ACCOUNT_ID, accountUpdateEvent.GetAccountId()),
		)
		recordDataCollectorCollectionError(caAccountCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed ca account update event", zap.String(logger.QUEUE_MESSAGE_ID, queueMessageId))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(caAccountCollectedData.GetDataType().String())

	return getSuccessRes(), nil
}

//nolint:dupl
func (rcs *RewardsConsumerService) ProcessKYCEvent(ctx context.Context, req *userPb.KycEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected KYC event", zap.String(logger.ACTOR_ID, req.GetActorId()))
	collectedData := &model.KYCCollectedData{
		KycEvent: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting kyc collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	messageId, err := rcs.rewardUnlockerEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing kyc event collected data to unlocker", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed kyc event to unlocker layer", zap.String(logger.QUEUE_MESSAGE_ID, messageId))

	messageId, err = rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing kyc event collected data to generator", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed kyc event to generator layer", zap.String(logger.QUEUE_MESSAGE_ID, messageId))

	metrics.MetricsRecorder.RecordDataCollectorCollectedData(collectedData.GetDataType().String())
	return getSuccessRes(), nil
}

func (rcs *RewardsConsumerService) ProcessFitttSportsEvent(ctx context.Context, req *sportsEventPb.SportsEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected fittt sports event", zap.Any("request", req))
	collectedData := &model.FitttSportsLeaderboardCollectedData{
		SportsEvent: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting fittt sports leaderboard collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing fittt sports leaderboard event collected data", zap.String(logger.ACTOR_ID, protoCollectedData.GetActorId()), zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed fittt sports leaderboard event", zap.String(logger.QUEUE_MESSAGE_ID, id), zap.String(logger.ACTOR_ID, protoCollectedData.GetActorId()))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(collectedData.GetDataType().String())
	return getSuccessRes(), nil
}

func (rcs *RewardsConsumerService) ProcessSavingsAccountStateUpdateEvent(ctx context.Context, req *savingsPb.AccountStateUpdateEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected savings account state update event", zap.String(logger.EVENT_ID, req.GetAccount().GetId()), zap.String(logger.ACTOR_ID, req.GetActor().GetId()), zap.String(logger.ACCOUNT_STATE, req.GetAccount().GetState().String()))

	// for now we only need account creation events, so filtering out all the other events.
	// If the account state is not CREATED then ignore the event.
	if req.GetAccount().GetState() != savingsPb.State_CREATED {
		logger.Info(ctx, "ignoring savings account state update event, account is not in CREATED state", zap.String(logger.EVENT_ID, req.GetAccount().GetId()), zap.String(logger.ACTOR_ID, req.GetActor().GetId()))
		return getSuccessRes(), nil
	}

	collectedData := &model.SavingsAccountUpdateCollectedData{
		AccountStateUpdateEvent: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting savings account state update collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	// publish event for unlocking of account
	_, err = rcs.rewardUnlockerEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing kyc event collected data to unlocker", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing savings account state update collected data", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed savings account state update collected data event", zap.String(logger.QUEUE_MESSAGE_ID, id))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(collectedData.GetDataType().String())

	return getSuccessRes(), nil
}

// nolint: dupl
func (rcs *RewardsConsumerService) ProcessSalaryDetectionEvent(ctx context.Context, req *salaryprogramEventsPb.SalaryDetectionEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected salary detection event", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.ORDER_ID, req.GetExternalOrderId()))

	collectedData := &model.SalaryDetectionCollectedData{
		SalaryDetectionEvent: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting salary detection collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing salary detection collected data", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed salary detection collected data event", zap.String(logger.QUEUE_MESSAGE_ID, id))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(collectedData.GetDataType().String())

	return getSuccessRes(), nil
}

func (rcs *RewardsConsumerService) ProcessOnboardingStageUpdateEvent(ctx context.Context, req *onbPb.OnboardingStageUpdate) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected onboarding stage update event", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.ONBOARDING_STAGE, req.GetStage().String()), zap.String(logger.STATE, req.GetState().String()))

	// for now we only need onboarding complete events, so filtering out all the other events.
	// If the onboarding stage is not ONBOARDING_COMPLETE or onboarding state is not SUCCESS then ignore the event.
	if req.GetStage() != onbPb.OnboardingStage_ONBOARDING_COMPLETE || req.GetState() != onbPb.OnboardingStageUpdate_SUCCESS {
		logger.Info(ctx, "ignoring onboarding stage update event", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.ONBOARDING_STAGE, req.GetStage().String()), zap.String(logger.STATE, req.GetState().String()))
		return getSuccessRes(), nil
	}

	collectedData := &model.OnboardingStageUpdateCollectedData{
		OnboardingStageUpdate: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting onboarding stage update collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing onboarding stage update collected data", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed onboarding stage update collected data event", zap.String(logger.QUEUE_MESSAGE_ID, id))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(collectedData.GetDataType().String())

	return getSuccessRes(), nil
}

// todo(divyadep/utkarsh): write UT
func (rcs *RewardsConsumerService) ProcessCreditCardTransactionEvent(ctx context.Context, req *creditCardConsumerPb.CreditCardTransactionEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected Credit card transaction event", zap.String(logger.TXN_ID, req.GetCardTransaction().GetId()))

	// we only need to process successful credit card txn events
	if req.GetCardTransaction().GetTxnStatus() != ffAccEnumsPb.TransactionStatus_TRANSACTION_STATUS_SUCCESS {
		logger.Info(ctx, "ignoring credit card txn event as the txn is not in SUCCESS state", zap.String(logger.TXN_ID, req.GetCardTransaction().GetId()))
		return getSuccessRes(), nil
	}

	// categorizer listens to the same order events stream as rewards so waiting for waitDuration for the txn to get categorized.
	time.Sleep(time.Until(req.GetCardTransaction().GetCreatedAt().AsTime().Add(rcs.conf.DataCollectorConfig.WaitTimeForFetchingCreditCardTxnCategories)))

	actorIdToCategoryOntologiesMap, err := rcs.getTxnCategoriesForActors(ctx, req.GetTransactionAdditionalInfo().GetActorFrom(), req.GetTransactionAdditionalInfo().GetActorTo(), req.GetCardTransaction().GetId())
	if err != nil {
		logger.Error(ctx, "error while fetching category ontologies for CC txn", zap.String(logger.TXN_ID, req.GetCardTransaction().GetId()), zap.Error(err))
		recordDataCollectorCollectionError(rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION, err)
		return getTransientFailureRes(), nil
	}

	ccTxnCollectedData := &model.CreditCardTransactionCollectedData{
		Ctx:                                ctx,
		CreditCardTxnEvent:                 req,
		ActorIdToTxnCategoryOntologyIdsMap: actorIdToCategoryOntologiesMap,
	}
	protoCcTxnCollectedData, err := ccTxnCollectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting credit card txn collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(ccTxnCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCcTxnCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing credit card txn collected data", zap.Error(err))
		recordDataCollectorCollectionError(ccTxnCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed credit card txn collected data", zap.String(logger.QUEUE_MESSAGE_ID, id))

	id, err = rcs.projectionsGenerationEventSqsPublisher.Publish(ctx, protoCcTxnCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing credit card txn collected data to projections generation queue", zap.Error(err))
		recordDataCollectorCollectionError(ccTxnCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Debug(ctx, "Pushed credit card txn collected data to projections generation queue", zap.String(logger.QUEUE_MESSAGE_ID, id))

	// publish CC txn event with delay for correct evaluation of constraint
	// expressions that depend on pinot aggregates as there could be some delay in
	// actual transaction and the transaction reflecting in pinot
	id, publishErr := rcs.dataCollectorEventDelayPublisher.PublishWithDelay(ctx, protoCcTxnCollectedData, time.Hour)
	if publishErr != nil {
		logger.Error(ctx, "error while publishing credit card txn collected data with delay", zap.Error(publishErr))
		recordDataCollectorCollectionError(ccTxnCollectedData.GetDataType(), publishErr)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed credit card txn collected data with delay", zap.String(logger.QUEUE_MESSAGE_ID, id))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(ccTxnCollectedData.GetDataType().String())

	// All reward clawback event publishments are now controlled by the EnableRewardClawback flag.
	// Check the annotated PR for details.
	if rcs.dynConf.Flags().EnableRewardClawback() {
		rcs.handleCreditCardTxnClawbackPublishing(ctx, req)
	}

	return getSuccessRes(), nil
}

// handleCreditCardTxnClawbackPublishing: method for publishing eligible clawback events for credit card transactions.
func (rcs *RewardsConsumerService) handleCreditCardTxnClawbackPublishing(ctx context.Context, req *creditCardConsumerPb.CreditCardTransactionEvent) {
	// Only publish if the credit card txn was a reversal of some existing txn.
	if req.GetCardTransaction().GetParentTransactionId() == "" || !fireflyHelper.IsReversalOrRefundTransaction(req.GetCardTransaction().GetTxnCategory()) {
		return
	}
	// todo (divyadeep) : use some interface based contract to avoid doing the event to clawback event mapping here similar to collected data event.
	clawbackEvent := &clawbackprocessorPb.ClawbackEvent{
		Id:        req.GetCardTransaction().GetExternalTxnId(),
		EventType: rewardsPb.ClawbackEventType_CREDIT_CARD_TXN_REVERSAL,
		EventTime: req.GetCardTransaction().GetCreatedAt(),
		EventTypeSpecificDetails: &clawbackprocessorPb.ClawbackEvent_CreditCardReversalTxnEvent{
			CreditCardReversalTxnEvent: req,
		},
	}
	id, err := rcs.clawbackPublisher.Publish(ctx, clawbackEvent)
	if err != nil {
		logger.Error(ctx, "error pushing credit card reversal txn clawback event", zap.String(logger.TXN_ID, req.GetCardTransaction().GetId()), zap.Error(err))
		return
	}
	logger.Info(ctx, "Pushed credit card txn reversal clawback event", zap.String(logger.QUEUE_MESSAGE_ID, id))
	id, err = rcs.clawbackDelayPublisher.PublishWithDelay(ctx, clawbackEvent, rcs.dynConf.CreditCardRewardsConfig().MinReqDelayForRewardEvalRelativeToBillWindowEndDate+(time.Hour*12))
	if err != nil {
		logger.Error(ctx, "error pushing credit card reversal txn clawback delayed event", zap.String(logger.TXN_ID, req.GetCardTransaction().GetId()), zap.Error(err))
		return
	}
	logger.Info(ctx, "Pushed credit card txn reversal clawback delayed event", zap.String(logger.QUEUE_MESSAGE_ID, id))
}

func (rcs *RewardsConsumerService) ProcessCreditCardBillingEvent(ctx context.Context, req *ffBillingEventsPb.CreditCardBillGenerationEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected credit card billing event", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.BILL_ID, req.GetBillId()))

	ccBillingCollectedData := &model.CreditCardBillingCollectedData{
		CreditCardBillGenerationEvent: req,
	}
	protoCCBillingCollectedData, err := ccBillingCollectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting credit card billing collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(ccBillingCollectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	requiredDelayForRewardEvaluation := rcs.dynConf.CreditCardRewardsConfig().MinReqDelayForRewardEvalRelativeToBillWindowEndDate
	requiredRewardEvaluationTime := req.GetBillWindow().GetToTimestamp().AsTime().In(datetime.IST).Add(requiredDelayForRewardEvaluation)

	if rcs.conf.CreditCardRewardsConfig.ShouldProcessBillingEventImmediatelyForRewardEvaluation || time.Until(requiredRewardEvaluationTime) <= 0 {
		// publish message immediately if ShouldProcessBillingEventImmediatelyForRewardEvaluation is set or requiredRewardEvaluationTime is 0
		id, publishErr := rcs.dataCollectorEventPublisher.Publish(ctx, protoCCBillingCollectedData)
		if publishErr != nil {
			logger.Error(ctx, "error while publishing credit card billing collected data", zap.Error(publishErr))
			recordDataCollectorCollectionError(ccBillingCollectedData.GetDataType(), publishErr)
			return getTransientFailureRes(), nil
		}
		logger.Info(ctx, "Pushed credit card billing collected data", zap.String(logger.QUEUE_MESSAGE_ID, id))
	} else {
		// publish with delay
		id, publishErr := rcs.dataCollectorEventDelayPublisher.PublishWithDelay(ctx, protoCCBillingCollectedData, time.Until(requiredRewardEvaluationTime))
		if publishErr != nil {
			logger.Error(ctx, "error while publishing delayed credit card billing collected data", zap.Error(publishErr))
			recordDataCollectorCollectionError(ccBillingCollectedData.GetDataType(), publishErr)
			return getTransientFailureRes(), nil
		}
		logger.Info(ctx, "Pushed delayed credit card billing collected data", zap.String(logger.QUEUE_MESSAGE_ID, id))
	}

	// All reward clawback event publishments are now controlled by the EnableRewardClawback flag.
	// Check the annotated PR for details.
	if rcs.dynConf.Flags().EnableRewardClawback() {
		rcs.handleCreditCardBillingClawbackPublishing(ctx, req)
	}

	metrics.MetricsRecorder.RecordDataCollectorCollectedData(ccBillingCollectedData.GetDataType().String())
	return getSuccessRes(), nil
}

// handleCreditCardBillingClawbackPublishing: method for publishing clawback events for credit card billing
func (rcs *RewardsConsumerService) handleCreditCardBillingClawbackPublishing(ctx context.Context, req *ffBillingEventsPb.CreditCardBillGenerationEvent) {
	clawbackEvent := &clawbackprocessorPb.ClawbackEvent{
		Id:        req.GetBillId(),
		EventType: rewardsPb.ClawbackEventType_CLAWBACK_EVENT_TYPE_CREDIT_CARD_BILLING,
		EventTime: req.GetBillWindow().GetToTimestamp(),
		EventTypeSpecificDetails: &clawbackprocessorPb.ClawbackEvent_CreditCardBillGenerationEvent{
			CreditCardBillGenerationEvent: req,
		},
	}
	messageId, err := rcs.clawbackPublisher.Publish(ctx, clawbackEvent)
	if err != nil {
		logger.Error(ctx, "error pushing credit card bill generation clawback event", zap.String("billId", req.GetBillId()), zap.Error(err))
		recordDataCollectorCollectionError(rewardsPb.CollectedDataType_CREDIT_CARD_BILLING, err)
		return
	}
	logger.Info(ctx, "Pushed credit card bill generation clawback event", zap.String(logger.QUEUE_MESSAGE_ID, messageId))
}

func (rcs *RewardsConsumerService) getTxnCategoriesForActors(ctx context.Context, fromActor, toActor, txnId string) (map[string]*dataCollectorPb.CategoryOntologyIds, error) {
	actorsDetailsRes, err := rcs.actorClient.GetEntityDetails(ctx, &actorPb.GetEntityDetailsRequest{ActorIds: []string{fromActor, toActor}})
	if rpcErr := epifigrpc.RPCError(actorsDetailsRes, err); rpcErr != nil {
		return nil, fmt.Errorf("error while fetching actor details, err : %w", rpcErr)
	}
	actorIdToActorTypeMap := make(map[string]types.ActorType)
	for _, actorDetails := range actorsDetailsRes.GetEntityDetails() {
		actorIdToActorTypeMap[actorDetails.GetActorId()] = actorDetails.GetEntityType()
	}

	fromActorType, isPresent := actorIdToActorTypeMap[fromActor]
	if !isPresent {
		return nil, fmt.Errorf("fromActor info not present in actorIdToEntityTypeMap, fromActor: %s", fromActor)
	}
	toActorType, isPresent := actorIdToActorTypeMap[toActor]
	if !isPresent {
		return nil, fmt.Errorf("toActor info not present in actorIdToEntityTypeMap, toActor: %s", toActor)
	}

	actorIdToTxnCategoryOntologyIds := map[string]*dataCollectorPb.CategoryOntologyIds{}

	// fetch txn categories from fromActor's perspective if fromActor is a fi user
	// Note : txn categories are persisted only w.r.t to fi users, there this actorType check is required
	if fromActorType == types.ActorType_USER {
		txnCategoriesRes, err := rcs.txnCatClient.GetTxnCategoryDetails(ctx, &categorizerPb.GetTxnCategoryDetailsRequest{
			ActorId:      fromActor,
			Provenance:   categorizerPb.Provenance_DS,
			DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
			ActivityId:   &categorizerPb.ActivityId{Id: &categorizerPb.ActivityId_FiCardTxnId{FiCardTxnId: txnId}},
		})
		if rpcErr := epifigrpc.RPCError(txnCategoriesRes, err); rpcErr != nil {
			return nil, fmt.Errorf("txnCatClient.GetTxnCategoryDetails rpc call failed for fromActor for credit card txn. err: %s, priority: %w", rpcErr.Error(), datacollectionerrors.P1RewardDataCollectorError)
		}

		var ontologyIds []string
		lo.ForEach[*categorizerPb.OntologyDetails](txnCategoriesRes.GetTxnCategories().GetOntologies(), func(ontologyDetails *categorizerPb.OntologyDetails, _ int) {
			ontologyIds = append(ontologyIds, ontologyDetails.GetOntologyId())
		})
		actorIdToTxnCategoryOntologyIds[fromActor] = &dataCollectorPb.CategoryOntologyIds{OntologyIds: ontologyIds}
	}

	// fetch txn categories from toActor's perspective if fromActor is a fi user
	// Note : txn categories are persisted only w.r.t to fi users, there this actorType check is required
	if toActorType == types.ActorType_USER {
		txnCategoriesRes, err := rcs.txnCatClient.GetTxnCategoryDetails(ctx, &categorizerPb.GetTxnCategoryDetailsRequest{
			ActorId:      toActor,
			Provenance:   categorizerPb.Provenance_DS,
			DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ANY,
			ActivityId:   &categorizerPb.ActivityId{Id: &categorizerPb.ActivityId_FiCardTxnId{FiCardTxnId: txnId}},
		})
		if rpcErr := epifigrpc.RPCError(txnCategoriesRes, err); rpcErr != nil {
			return nil, fmt.Errorf("txnCatClient.GetTxnCategoryDetails rpc call failed for toActor for credit card txn. err: %s, priority: %w", rpcErr.Error(), datacollectionerrors.P1RewardDataCollectorError)
		}

		var ontologyIds []string
		lo.ForEach[*categorizerPb.OntologyDetails](txnCategoriesRes.GetTxnCategories().GetOntologies(), func(ontologyDetails *categorizerPb.OntologyDetails, _ int) {
			ontologyIds = append(ontologyIds, ontologyDetails.GetOntologyId())
		})
		actorIdToTxnCategoryOntologyIds[toActor] = &dataCollectorPb.CategoryOntologyIds{OntologyIds: ontologyIds}
	}

	return actorIdToTxnCategoryOntologyIds, nil
}

func (rcs *RewardsConsumerService) ProcessCreditCardRequestStageUpdateEvent(ctx context.Context, req *ffEvent.CreditCardRequestStageUpdateEvent) (*dataCollectorPb.ConsumerResponse, error) {
	logger.Info(ctx, "Collected credit card request stage update event", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.REQUEST_ID, req.GetCardRequestId()))

	collectedData := &model.CreditCardRequestStageUpdate{
		CreditCardRequestStageUpdateEvent: req,
	}
	protoCollectedData, err := collectedData.GetProtoCollectedData()
	if err != nil {
		logger.Error(ctx, "error converting credit card request stage update collected data model to proto", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}

	id, err := rcs.dataCollectorEventPublisher.Publish(ctx, protoCollectedData)
	if err != nil {
		logger.Error(ctx, "error while publishing credit card request stage update collected data", zap.Error(err))
		recordDataCollectorCollectionError(collectedData.GetDataType(), err)
		return getTransientFailureRes(), nil
	}
	logger.Info(ctx, "Pushed credit card request stage update collected data event", zap.String(logger.QUEUE_MESSAGE_ID, id))
	metrics.MetricsRecorder.RecordDataCollectorCollectedData(collectedData.GetDataType().String())

	return getSuccessRes(), nil
}
