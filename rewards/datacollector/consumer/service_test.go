package consumer

import (
	"context"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"
	ffEvent "github.com/epifi/gamma/api/firefly/event"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	dataCollectorPb "github.com/epifi/gamma/api/rewards/datacollector"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/config/genconf"
	"github.com/epifi/gamma/rewards/test"
)

var (
	conf    *config.Config
	dynConf *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
// nolint: dogsled
func TestMain(m *testing.M) {
	var teardown func()
	conf, dynConf, _, teardown = test.InitTestServer(false)
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

func TestRewardsConsumerService_ProcessOrderUpdateEvents(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()

	mockPublisher := queueMocks.NewMockPublisher(ctr)

	type args struct {
		ctx context.Context
		req *orderPb.OrderUpdate
	}
	tests := []struct {
		name    string
		args    args
		mocks   []interface{}
		want    *dataCollectorPb.ConsumerResponse
		wantErr bool
	}{
		{
			name: "unsuccessful processing of order update event: pipeline publish failed",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderAccountRelation: orderPb.OrderAccountRelation_INTERNAL_ACCOUNT,
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Workflow: orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
							Status:   orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{{Id: "txn -1"}},
					},
				},
			},
			mocks: []interface{}{
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("", errors.New("failed to push event to data collector queue")),
			},
			want:    getTransientFailureRes(),
			wantErr: false,
		},
		{
			name: "successful processing of order update event",
			args: args{
				ctx: context.Background(),
				req: &orderPb.OrderUpdate{
					OrderAccountRelation: orderPb.OrderAccountRelation_INTERNAL_ACCOUNT,
					OrderWithTransactions: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							// note: using one of the allowed/whitelisted order-workflows here. Might change in the future.
							Workflow: orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
							Status:   orderPb.OrderStatus_PAID,
						},
						Transactions: []*paymentPb.Transaction{{Id: "txn -1"}},
					},
				},
			},
			mocks: []interface{}{
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("id", nil).Times(3),
			},
			want:    getSuccessRes(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rcs := &RewardsConsumerService{
				dataCollectorEventPublisher:            mockPublisher,
				projectionsGenerationEventSqsPublisher: mockPublisher,
				rewardUnlockerEventPublisher:           mockPublisher,
				conf:                                   conf,
				dynConf:                                dynConf,
			}
			got, err := rcs.ProcessOrderUpdateEvents(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessOrderUpdateEvents() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessOrderUpdateEvents() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestRewardsConsumerService_ProcessCreditCardRequestStageUpdateEvent(t *testing.T) {
	type args struct {
		ctx context.Context
		req *ffEvent.CreditCardRequestStageUpdateEvent
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(mockPublisher *queueMocks.MockPublisher)
		want       *dataCollectorPb.ConsumerResponse
		wantErr    bool
	}{
		{
			name: "should publish successfully",
			args: args{
				ctx: context.Background(),
				req: &ffEvent.CreditCardRequestStageUpdateEvent{
					CardRequestId: "req-id-1",
				},
			},
			setupMocks: func(mockPublisher *queueMocks.MockPublisher) {
				mockPublisher.EXPECT().Publish(gomock.Any(), gomock.Any()).Return("id", nil)
			},
			want: getSuccessRes(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			defer ctr.Finish()

			mockPublisher := queueMocks.NewMockPublisher(ctr)

			tt.setupMocks(mockPublisher)

			rcs := &RewardsConsumerService{
				dataCollectorEventPublisher: mockPublisher,
			}
			got, err := rcs.ProcessCreditCardRequestStageUpdateEvent(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessCreditCardRequestStageUpdateEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessCreditCardRequestStageUpdateEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}
