package rewardfulfillment

import (
	"context"
	"time"

	"github.com/jonboulle/clockwork"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"

	accrualPb "github.com/epifi/gamma/api/accrual"
	fireflyPb "github.com/epifi/gamma/api/firefly"
	rewardsPb "github.com/epifi/gamma/api/rewards"

	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/rewards/processor/dao"
)

type fiCoinsRewardProcessor struct {
	accrualClient        accrualPb.AccrualClient
	fireflyClient        fireflyPb.FireflyClient
	processingRequestDao dao.ProcessingRequestDao
	clock                clockwork.Clock
}

type Opt func(lpp *fiCoinsRewardProcessor)

func WithClock(clock clockwork.Clock) Opt {
	return func(lpp *fiCoinsRewardProcessor) {
		lpp.clock = clock
	}
}

func NewFiCoinsRewardProcessor(
	accrualClient accrualPb.AccrualClient,
	fireflyClient fireflyPb.FireflyClient,
	processingRequestDao dao.ProcessingRequestDao,
	opts ...Opt,
) *fiCoinsRewardProcessor {
	lpp := &fiCoinsRewardProcessor{
		accrualClient:        accrualClient,
		fireflyClient:        fireflyClient,
		processingRequestDao: processingRequestDao,
		clock:                clockwork.NewRealClock(),
	}
	for _, opt := range opts {
		opt(lpp)
	}
	return lpp
}

func (lpp *fiCoinsRewardProcessor) Process(ctx context.Context, request IRequest) (*Response, error) {
	// check if fi coins to fi points migration is active
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return nil, errors.New(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error())
	}

	// fetch processing request entry for given reward processing request
	processingRequest, err := lpp.processingRequestDao.GetById(ctx, request.GetProcessingReqId())
	if err != nil {
		logger.Error(ctx, "error fetching processing request entry", zap.Any("reward_processing_request", request), zap.Error(err))
		return nil, errors.Wrap(err, "error fetching processing request entry")
	}
	// get ficoins reward from reward processing request
	ficoinsReward, ok := request.GetRewardDetails().(*rewardsPb.FiCoins)
	if !ok {
		return nil, errors.New("invalid ficoins reward details in request")
	}
	// if ficoins amount is zero, then no need to call accrual service for ficoins processing.
	// just return processed status without updating any processing ref in processingRequest table
	// as no downstream service has to be communicated.
	if ficoinsReward.GetUnits() == 0 {
		logger.Info(ctx, "processing zero value ficoins reward", zap.Any("request", request))
		return &Response{Status: ProcessingStatus_PROCESSED}, nil
	}
	// if the fi-coins have already expired then we mark the reward as expired
	if ficoinsReward.GetExpiresAt().AsTime().Before(time.Now()) {
		return &Response{Status: ProcessingStatus_EXPIRED, SubStatus: rewardsPb.SubStatus_SUB_STATUS_FI_COINS_EXPIRED}, nil
	}

	// if processing ref does not exits generate one, as it is needed for calling accrual service
	if processingRequest.ProcessingRef == "" {
		// setting processing_ref same as processing_request.id
		// as we want a unique ref for each unique processing request
		// and processing request id would provide such uniqueness
		processingRef := processingRequest.Id
		if err := lpp.processingRequestDao.UpdateProcessingRef(ctx, processingRequest.Id, processingRef); err != nil {
			logger.Error(ctx, "error updating processing ref in processing request", zap.Any("reward_processing_request", request), zap.Error(err))
			return nil, errors.Wrap(err, "error updating processing ref in processing request")
		}
		processingRequest.ProcessingRef = processingRef
	}

	// check if txn was already initiated
	checkStatusRequest := &accrualPb.CheckStatusRequest{RequestRefId: processingRequest.ProcessingRef}
	checkTransactionStatusRes, err := lpp.accrualClient.CheckTransactionStatus(ctx, checkStatusRequest)
	if err != nil || checkTransactionStatusRes == nil || !checkTransactionStatusRes.Status.IsSuccess() {
		logger.Error(ctx, "check credit txn status call failed", zap.Error(err), zap.Any("res", checkTransactionStatusRes))
		return nil, errors.New("check credit txn status call failed")
	}
	switch checkTransactionStatusRes.TransactionStatus {
	case accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED:
		return &Response{
			Status:    ProcessingStatus_PROCESSED,
			SubStatus: rewardsPb.SubStatus_SUB_STATUS_CREDITED_FI_COINS,
		}, nil
	case accrualPb.TransactionStatus_TRANSACTION_STATUS_NOT_PERFORMED:
		logger.Info(ctx, "txn not performed")
	default:
		return nil, errors.New("txn not successful")
	}
	actionTime, err := request.GetActionTime()
	if err != nil {
		logger.Error(ctx, "failed to get action time", zap.Error(err))
		return nil, errors.Wrap(err, "failed to get action time")
	}
	// units are expected to be within the int32 range.
	// nolint: gosec
	rewardCreditAmount, txnMetaData, err := accrualPkg.ConvertFiCoinsToFiPointsForCreditTxn(ctx, lpp.clock, int32(ficoinsReward.GetUnits()), actionTime, request.GetActorId(), lpp.fireflyClient)
	if err != nil {
		logger.Error(ctx, "error converting fi-coins to fi-points for credit transaction", zap.Error(err))
		return nil, errors.Wrap(err, "error converting fi-coins to fi-points for credit transaction")
	}

	req := &accrualPb.TransactRequest{
		RequestRefId:     processingRequest.ProcessingRef,
		ActorId:          request.GetActorId(),
		Amount:           rewardCreditAmount,
		AmountExpiryTime: ficoinsReward.GetExpiresAt(),
		AccountType:      accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()),
		TransactionType:  accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
		TxnMetaData:      txnMetaData,
	}
	transactRes, err := lpp.accrualClient.Transact(ctx, req)
	if err != nil || transactRes == nil || !transactRes.Status.IsSuccess() {
		logger.Error(ctx, "credit tnx call failed", zap.Error(err), zap.Any("res", transactRes))
		return nil, errors.New("credit txn call failed")
	}
	if transactRes.TransactionStatus == accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED {
		return &Response{
			Status:    ProcessingStatus_PROCESSED,
			SubStatus: rewardsPb.SubStatus_SUB_STATUS_CREDITED_FI_COINS,
		}, nil
	} else {
		return nil, errors.New("check credit txn call not successful")
	}
}
