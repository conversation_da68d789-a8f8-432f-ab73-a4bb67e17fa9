// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/IBM/sarama"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/bedrockruntime"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	types2 "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/storage/v2"
	genconf2 "github.com/epifi/be-common/quest/sdk/config/genconf"
	"github.com/epifi/gamma/api/accrual"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/casper/exchanger"
	"github.com/epifi/gamma/api/casper/redemption"
	"github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/gamma/api/firefly/pinot"
	"github.com/epifi/gamma/api/fittt"
	"github.com/epifi/gamma/api/health_engine"
	"github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/api/insights/epf"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/merchant"
	"github.com/epifi/gamma/api/nudge"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/cx"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/quest/manager"
	"github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/luckydraw"
	"github.com/epifi/gamma/api/rewards/projector"
	rewardoffers2 "github.com/epifi/gamma/api/rewards/rewardoffers"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/timeline"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	order2 "github.com/epifi/gamma/api/usstocks/order"
	rewards2 "github.com/epifi/gamma/api/usstocks/rewards"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	"github.com/epifi/gamma/pkg/downtime"
	"github.com/epifi/gamma/pkg/kafka/producer"
	"github.com/epifi/gamma/pkg/kafka/producer/partitioner"
	pinot3 "github.com/epifi/gamma/pkg/pinot"
	"github.com/epifi/gamma/quest/sdk/init"
	"github.com/epifi/gamma/rewards/aggregator"
	"github.com/epifi/gamma/rewards/campaigncomm"
	processor2 "github.com/epifi/gamma/rewards/campaigncomm/processor"
	consumer3 "github.com/epifi/gamma/rewards/clawback/consumer"
	dao2 "github.com/epifi/gamma/rewards/clawback/dao"
	"github.com/epifi/gamma/rewards/clawback/decider"
	generator2 "github.com/epifi/gamma/rewards/clawback/generator"
	"github.com/epifi/gamma/rewards/config"
	"github.com/epifi/gamma/rewards/config/genconf"
	"github.com/epifi/gamma/rewards/datacollector/consumer"
	"github.com/epifi/gamma/rewards/datacollector/service"
	"github.com/epifi/gamma/rewards/developer"
	"github.com/epifi/gamma/rewards/developer/processor"
	"github.com/epifi/gamma/rewards/expiry/consumer"
	consumer2 "github.com/epifi/gamma/rewards/generator/consumer"
	"github.com/epifi/gamma/rewards/generator/dao"
	"github.com/epifi/gamma/rewards/generator/rewardsimulator"
	"github.com/epifi/gamma/rewards/generator/ruleengine"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact"
	"github.com/epifi/gamma/rewards/generator/ruleengine/fact/generator"
	"github.com/epifi/gamma/rewards/helper"
	"github.com/epifi/gamma/rewards/helper/rewardamountcalculator"
	"github.com/epifi/gamma/rewards/helper/slack_helper"
	"github.com/epifi/gamma/rewards/helper/slack_helper/attachment_builder"
	"github.com/epifi/gamma/rewards/jobhelper"
	luckydraw2 "github.com/epifi/gamma/rewards/luckydraw"
	dao5 "github.com/epifi/gamma/rewards/luckydraw/dao"
	"github.com/epifi/gamma/rewards/luckydraw/distributionstrategy"
	"github.com/epifi/gamma/rewards/notification"
	helper2 "github.com/epifi/gamma/rewards/notification/helper"
	"github.com/epifi/gamma/rewards/notification/triggerhandler"
	pinot2 "github.com/epifi/gamma/rewards/pinot"
	consumer5 "github.com/epifi/gamma/rewards/pinot/consumer"
	dao7 "github.com/epifi/gamma/rewards/pinot/dao"
	"github.com/epifi/gamma/rewards/pinot/eventspecificpinotrewardinfo"
	"github.com/epifi/gamma/rewards/processor/clawbackfulfillment"
	dao4 "github.com/epifi/gamma/rewards/processor/dao"
	"github.com/epifi/gamma/rewards/processor/rewardfulfillment"
	service3 "github.com/epifi/gamma/rewards/processor/service"
	"github.com/epifi/gamma/rewards/processor/status/reward"
	"github.com/epifi/gamma/rewards/processor/status/rewardclawback"
	reward2 "github.com/epifi/gamma/rewards/processor/status/winning"
	"github.com/epifi/gamma/rewards/processor/subscriberService"
	projector2 "github.com/epifi/gamma/rewards/projector"
	consumer4 "github.com/epifi/gamma/rewards/projector/consumer"
	dao3 "github.com/epifi/gamma/rewards/projector/dao"
	consumer6 "github.com/epifi/gamma/rewards/requeueservice/consumer"
	service2 "github.com/epifi/gamma/rewards/service"
	"github.com/epifi/gamma/rewards/service/rewardoffers"
	"github.com/epifi/gamma/rewards/unlocker/consumer"
	dao6 "github.com/epifi/gamma/rewards/unlocker/dao"
	"github.com/epifi/gamma/rewards/wire/types"
	"github.com/redis/go-redis/v9"
	"github.com/slack-go/slack"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeService() *service.RewardsService {
	rewardsService := service.NewRewardsService()
	return rewardsService
}

func InitializeRewardsConsumerService(publisher types.DataCollectorEventSqsPublisher, delayPublisher types.DataCollectorEventSqsCustomDelayPublisher, actorClient actor.ActorClient, fitttClient fittt.FitttClient, depositClient deposit.DepositClient, caClient connected_account.ConnectedAccountClient, clawbackPublisher types.RewardClawbackEventCollectedDataPublisher, clawbackDelayPublisher types.RewardsClawbackEventCollectedDataCustomDelayPublisher, ccAccountingClient accounting.AccountingClient, txnCatClient categorizer.TxnCategorizerClient, onboardingClient onboarding.OnboardingClient, usersClient user.UsersClient, piClient paymentinstrument.PiClient, rewardsUnlockerPublisher types.RewardsRewardUnlockerSqsPublisher, projectionsGenerationEventSqsPublisher types.RewardsProjectionsGenerationEventSqsPublisher, conf *config.Config, dynConf *genconf.Config, cardClient provisioning.CardProvisioningClient, savingsClient savings.SavingsClient, nudgeClient nudge.NudgeServiceClient, redisClient types.RewardsRedisStore, userGroupClient group.GroupClient, accountTieringClient tiering.TieringClient, inAppReferralClient inappreferral.InAppReferralClient, orderClient order.OrderServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, vkycClient vkyc.VKYCClient, segmentClient segment.SegmentationServiceClient, userIntelClient userintel.UserIntelServiceClient, employmentClient employment.EmploymentClient, bankCustClient bankcust.BankCustomerServiceClient, db types2.RewardsPGDB) (*consumer.RewardsConsumerService, error) {
	client := types.RedisClientProvider(redisClient)
	flags := flagConfigProvider(conf)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisV9LockManager)
	userHelperService := helper.NewUserHelperService(client, actorClient, usersClient, userGroupClient, accountTieringClient, inAppReferralClient, orderClient, onboardingClient, cardClient, vkycClient, salaryProgramClient, segmentClient, userIntelClient, employmentClient, flags, bankCustClient, savingsClient, crdbRewardsDao, dynConf)
	rewardsConsumerService := consumer.NewRewardsConsumerService(publisher, delayPublisher, actorClient, fitttClient, depositClient, caClient, clawbackPublisher, clawbackDelayPublisher, ccAccountingClient, txnCatClient, onboardingClient, piClient, rewardsUnlockerPublisher, projectionsGenerationEventSqsPublisher, conf, dynConf, cardClient, savingsClient, nudgeClient, userHelperService)
	return rewardsConsumerService, nil
}

func InitializeRewardsGeneratorService(db types2.RewardsPGDB, redisClient types.RewardsRedisStore, rewardProcessingPublisher types.RewardsSqsPublisher, rewardProcessingDelayPublisher types.RewardsProcessingSqsCustomDelayPublisher, rewardsManualGiveawayEventPublisher types.RewardsManualGiveawayEventSqsPublisher, bulkClaimRewardsEventPublisher types.BulkClaimRewardsEventSqsPublisher, dynamicConf *genconf.Config, eventsBroker events.Broker, luckyDrawSvcClient luckydraw.LuckyDrawServiceClient, merchantClient merchant.MerchantServiceClient, ffAccClient accounting.AccountingClient, ffBillingClient billing.BillingClient, ffTxnAggregatesClient pinot.TxnAggregatesClient, payClient pay.PayClient, config2 *config.Config) *service2.RewardsService {
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	client := types.RedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisV9LockManager)
	pgdbCreditCardRewardDao := dao.NewPGDBCreditCardRewardDao(db)
	pgdbRewardsClawbackDao := dao2.NewPGDBRewardsClawbackDao(db)
	creditCardRewardAggregator := aggregator.NewCreditCardRewardAggregator(pgdbCreditCardRewardDao, merchantClient, dynamicConf)
	ccRewardAmountCalculator := rewardamountcalculator.NewCCRewardAmountCalculator(creditCardRewardAggregator, ffAccClient, ffBillingClient, ffTxnAggregatesClient, dynamicConf)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	crdbRewardOfferDao := dao.NewCrdbRewardOfferDao(db)
	pgdbRewardsProjectionDao := dao3.NewPGDBRewardsProjectionDao(db)
	rewardsService := service2.NewRewardsGeneratorService(crdbRewardsDao, pgdbCreditCardRewardDao, pgdbRewardsClawbackDao, creditCardRewardAggregator, ccRewardAmountCalculator, rewardProcessingPublisher, rewardProcessingDelayPublisher, rewardsManualGiveawayEventPublisher, bulkClaimRewardsEventPublisher, eventsBroker, luckyDrawSvcClient, redisCacheStorage, config2, dynamicConf, clock, crdbRewardOfferDao, pgdbRewardsProjectionDao, payClient)
	return rewardsService
}

func InitializeRewardOfferService(db types2.RewardsPGDB, redisClient types.RewardsRedisStore, questCacheStorage types.QuestCacheStorage, actorClient actor.ActorClient, usersClient user.UsersClient, vkycClient vkyc.VKYCClient, accountTieringClient tiering.TieringClient, debitCardProvClient provisioning.CardProvisioningClient, userGroupClient group.GroupClient, inAppReferralClient inappreferral.InAppReferralClient, onbClient onboarding.OnboardingClient, orderClient order.OrderServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, segmentClient segment.SegmentationServiceClient, userIntelClient userintel.UserIntelServiceClient, conf *config.Config, dyconf *genconf.Config, awsconfv2 aws.Config, merchantClient merchant.MerchantServiceClient, bcClient bankcust.BankCustomerServiceClient, employmentClient employment.EmploymentClient, questManagerCl manager.ManagerClient, broker events.Broker, savingsClient savings.SavingsClient) *rewardoffers.RewardOfferService {
	crdbRewardOfferDao := dao.NewCrdbRewardOfferDao(db)
	pgdbRewardOfferGroupDao := dao.NewPgdbRewardOfferGroupDao(db)
	client := types.RedisClientProvider(redisClient)
	flags := flagConfigProvider(conf)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisV9LockManager)
	userHelperService := helper.NewUserHelperService(client, actorClient, usersClient, userGroupClient, accountTieringClient, inAppReferralClient, orderClient, onbClient, debitCardProvClient, vkycClient, salaryProgramClient, segmentClient, userIntelClient, employmentClient, flags, bcClient, savingsClient, crdbRewardsDao, dyconf)
	slackClient := slackClientProvider(conf)
	rewardOfferAttachmentBuilder := attachment_builder.NewRewardOfferAttachmentBuilder()
	attachmentBuilderFactory := attachment_builder.NewAttachmentBuilderFactory(rewardOfferAttachmentBuilder)
	slackHelperSvc := slack_helper.NewSlackHelperSvc(slackClient, attachmentBuilderFactory, dyconf)
	genconfConfig := questSDKClientConfProvider(dyconf)
	cacheStorage := types.QuestCacheStorageProvider(questCacheStorage)
	questsdkClient := questsdkinit.GetQuestSDKClient(genconfConfig, questManagerCl, userGroupClient, usersClient, actorClient, segmentClient, cacheStorage, broker)
	bedrockruntimeClient := bedrockClientProvider(awsconfv2)
	rewardOfferService := rewardoffers.NewRewardOfferService(crdbRewardOfferDao, pgdbRewardOfferGroupDao, salaryProgramClient, onbClient, segmentClient, userHelperService, flags, dyconf, slackHelperSvc, merchantClient, questsdkClient, bedrockruntimeClient)
	return rewardOfferService
}

func InitializeGeneratorConsumerService(db types2.RewardsPGDB, redisClient types.RewardsRedisStore, rewardPublisher types.RewardsSqsPublisher, rewardsClient rewards.RewardsGeneratorClient, claimRewardEventDelayPublisher types.RewardsClaimRewardEventSqsCustomDelayPublisher, orderClient order.OrderServiceClient, orderCxClient cx.CXClient, merchantClient merchant.MerchantServiceClient, txnClient payment.PaymentClient, accountPiClient account_pi.AccountPIRelationClient, actorClient actor.ActorClient, usersClient user.UsersClient, userGroupClient group.GroupClient, accountTieringClient tiering.TieringClient, inAppReferralClient inappreferral.InAppReferralClient, client onboarding.OnboardingClient, debitCardProvClient provisioning.CardProvisioningClient, searchClient search.ActionBarClient, caClient connected_account.ConnectedAccountClient, vkycClient vkyc.VKYCClient, fitttClient fittt.FitttClient, luckyDrawSvcClient luckydraw.LuckyDrawServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, segmentClient segment.SegmentationServiceClient, commsClient types.RewardsCommsClientWithInterceptors, eventsBroker events.Broker, conf *config.Config, dynConf *genconf.Config, rewardsGenerationEventSnsPublisher types.RewardGenerationEventSnsPublisher, authClient auth.AuthClient, redemption2 redemption.OfferRedemptionServiceClient, txnCatClient categorizer.TxnCategorizerClient, payClient pay.PayClient, fireflyClient firefly.FireflyClient, ffAccClient accounting.AccountingClient, ffBillingClient billing.BillingClient, ffTxnAggregatesClient pinot.TxnAggregatesClient, rewardsNotificationEventSqsCustomDelayPublisher types.RewardsNotificationEventSqsCustomDelayPublisher, userIntelClient userintel.UserIntelServiceClient, bcClient bankcust.BankCustomerServiceClient, employmentClient employment.EmploymentClient, exchangerOfferClient exchanger.ExchangerOfferServiceClient, offerCatalogClient casper.OfferCatalogServiceClient, savingsClient savings.SavingsClient, piClient paymentinstrument.PiClient, rewardsProjectionClient projector.ProjectorServiceClient, unlockerDelayPublisher types.RewardsRewardUnlockerSqsCustomDelayPublisher, projectionUpdateEventPublisher types.RewardsProjectionUpdateEventSqsPublisher, alfredClient alfred.AlfredClient, ussOrderMgClient order2.OrderManagerClient, rewardsRewardExpirySqsCustomDelayPublisher types.RewardsRewardExpirySqsCustomDelayPublisher, epfClient epf.EpfClient, questManagerCl manager.ManagerClient, questCacheStorage types.QuestCacheStorage, rewardsBucketS3Client types.RewardsS3Client) (*consumer2.GeneratorConsumerService, error) {
	pgdbRewardOfferGroupDao := dao.NewPgdbRewardOfferGroupDao(db)
	crdbRewardOfferDao := dao.NewCrdbRewardOfferDao(db)
	pgdbRewardsProjectionDao := dao3.NewPGDBRewardsProjectionDao(db)
	client2 := types.RedisClientProvider(redisClient)
	flags := flagConfigProvider(conf)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client2, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisV9LockManager)
	userHelperService := helper.NewUserHelperService(client2, actorClient, usersClient, userGroupClient, accountTieringClient, inAppReferralClient, orderClient, client, debitCardProvClient, vkycClient, salaryProgramClient, segmentClient, userIntelClient, employmentClient, flags, bcClient, savingsClient, crdbRewardsDao, dynConf)
	customRuleEngine := ruleengine.NewCustomRuleEngine(pgdbRewardOfferGroupDao, crdbRewardOfferDao, pgdbRewardsProjectionDao, luckyDrawSvcClient, userHelperService, ffAccClient, crdbRewardsDao, conf, dynConf)
	genconfConfig := questSDKClientConfProvider(dynConf)
	cacheStorage := types.QuestCacheStorageProvider(questCacheStorage)
	questsdkClient := questsdkinit.GetQuestSDKClient(genconfConfig, questManagerCl, userGroupClient, usersClient, actorClient, segmentClient, cacheStorage, eventsBroker)
	orderFactGenerator := generator.NewOrderFactGenerator(crdbRewardsDao, orderClient, orderCxClient, merchantClient, txnClient, client, actorClient, accountPiClient, userHelperService, authClient, redemption2, salaryProgramClient, txnCatClient, payClient, userGroupClient, usersClient, rewardsProjectionClient, piClient, conf, alfredClient, dynConf, questsdkClient, rewardsBucketS3Client)
	searchFactGenerator := generator.NewSearchFactGenerator(userHelperService)
	referrerOrderFactGenerator := generator.NewReferrerOrderFactGenerator(crdbRewardsDao, orderClient, accountPiClient, txnClient, userHelperService, conf, dynConf, payClient, userGroupClient, usersClient, actorClient, questsdkClient)
	referrerOrderFactGeneratorV2 := generator.NewReferrerOrderFactGeneratorV2(crdbRewardsDao, orderClient, accountPiClient, txnClient, userHelperService, conf, payClient, userGroupClient, usersClient, actorClient, questsdkClient)
	salaryProgramInAppReferralRefereeFactGenerator := generator.NewSalaryProgramInAppReferralRefereeFactGenerator(userHelperService, salaryProgramClient)
	salaryProgramInAppReferralReferrerFactGenerator := generator.NewSalaryProgramInAppReferralReferrerFactGenerator(userHelperService, salaryProgramClient)
	manualGiveawayFactGenerator := generator.NewManualGiveawayFactGenerator(userHelperService)
	fitttFactGenerator := generator.NewFitttFactGenerator(userHelperService, searchClient, fitttClient)
	extraInterestSdBonusPayoutFactGenerator := generator.NewExtraInterestSdBonusPayoutFactGenerator(userHelperService)
	minBalanceFactGenerator := generator.NewMinBalanceFactGenerator(userHelperService)
	caAccountFactGenerator := generator.NewCAAccountFactGenerator(userHelperService, caClient, salaryProgramClient)
	kycFactGenerator := generator.NewKYCFactGenerator(userHelperService, client)
	fitttSportsLeaderboardFactGenerator := generator.NewFitttSportsLeaderboardFactGenerator(userHelperService)
	savingsAccountUpdateFactGenerator := generator.NewSavingsAccountUpdateFactGenerator(userHelperService)
	salaryDetectionFactGenerator := generator.NewSalaryDetectionFactGenerator(userHelperService, orderClient, dynConf)
	onboardingStageUpdateFactGenerator := generator.NewOnboardingStageUpdateFactGenerator(userHelperService)
	ccTxnFactGenerator := generator.NewCcTxnFactGenerator(userHelperService, fireflyClient, actorClient, merchantClient, ffAccClient, txnCatClient, redemption2, crdbRewardsDao, piClient)
	pgdbCreditCardRewardDao := dao.NewPGDBCreditCardRewardDao(db)
	creditCardRewardAggregator := aggregator.NewCreditCardRewardAggregator(pgdbCreditCardRewardDao, merchantClient, dynConf)
	ccRewardAmountCalculator := rewardamountcalculator.NewCCRewardAmountCalculator(creditCardRewardAggregator, ffAccClient, ffBillingClient, ffTxnAggregatesClient, dynConf)
	creditCard1xRewardFactGenerator := generator.NewCreditCard1xRewardFactGenerator(userHelperService, actorClient, merchantClient, fireflyClient, ccRewardAmountCalculator, dynConf, txnCatClient, ffAccClient)
	ccTopMerchantsSpendsRewardFactGenerator := generator.NewCcTopMerchantsSpendsRewardFactGenerator(userHelperService, fireflyClient, ffAccClient, ccRewardAmountCalculator, ffTxnAggregatesClient, dynConf)
	ccRequestStageUpdateFactGenerator := generator.NewCcRequestStageUpdateFactGenerator(userHelperService)
	ccCuratedMerchantsSpendsRewardFactGenerator := generator.NewCcCuratedMerchantsSpendsRewardFactGenerator(userHelperService, fireflyClient, ffAccClient, ccRewardAmountCalculator, ffTxnAggregatesClient, dynConf)
	ccQuarterlyAirportLoungeRewardFactGenerator := generator.NewCcQuarterlyAirportLoungeRewardFactGenerator(userHelperService, ffTxnAggregatesClient)
	creditReportDownloadFactGenerator := generator.NewCreditReportDownloadFactGenerator(userHelperService)
	salaryStatusUpdateFactGenerator := generator.NewSalaryStatusUpdateFactGenerator(userHelperService, salaryProgramClient)
	offerRedemptionStatusUpdateFactGenerator := generator.NewOfferRedemptionStatusUpdateFactGenerator(userHelperService, exchangerOfferClient, offerCatalogClient)
	refereeSignupFactGenerator := generator.NewRefereeSignupFactGenerator(userHelperService)
	ccBillingFactGenerator := generator.NewCcBillingFactGenerator(userHelperService, fireflyClient, ffAccClient, ffTxnAggregatesClient, dynConf)
	investmentRetentionFactGenerator := generator.NewInvestmentRetentionFactGenerator(userHelperService)
	investmentWithdrawalFactGenerator := generator.NewInvestmentWithdrawalFactGenerator(userHelperService, crdbRewardsDao)
	tieringPeriodicRewardFactGenerator := generator.NewTieringPeriodicRewardFactGenerator(accountTieringClient, userHelperService)
	tieringRewardFactGenerator := generator.NewTieringRewardFactGenerator()
	usStocksRewardUnlockFactGenerator := generator.NewUSStocksRewardUnlockFactGenerator(userHelperService, ussOrderMgClient)
	usStocksOrderFactGenerator := generator.NewUSStocksOrderFactGenerator(conf, dynConf, userHelperService, ussOrderMgClient, questsdkClient)
	referrerUSStocksFactGenerator := generator.NewReferrerUSStocksFactGenerator(crdbRewardsDao, userHelperService, conf, dynConf, ussOrderMgClient, questsdkClient)
	dcSwitchNotificationFactGenerator := generator.NewDcSwitchNotificationFactGenerator(dynConf, userHelperService, debitCardProvClient)
	epfPassbookImportFactGenerator := generator.NewEpfPassbookImportFactGenerator(dynConf, userHelperService, epfClient)
	referrerEpfPassbookImportFactGenerator := generator.NewReferrerEpfPassbookImportFactGenerator(crdbRewardsDao, userHelperService, conf, dynConf, epfClient)
	caAccountDataSyncDataGenerator := generator.NewCaAccountDataSyncDataGenerator(userHelperService, caClient, salaryProgramClient)
	mfExternalOrderUpdateFactGenerator := generator.NewMFExternalOrderUpdateFactGenerator(conf, dynConf, userHelperService, ussOrderMgClient)
	referrerMFExternalOrderUpdateFactGenerator := generator.NewReferrerMFExternalOrderUpdateFactGenerator(crdbRewardsDao, userHelperService, conf, dynConf)
	referrerCAAccountFactGenerator := generator.NewReferrerCAAccountFactGenerator(crdbRewardsDao, userHelperService, conf, dynConf)
	actorNudgeStatusUpdateFactGenerator := generator.NewActorNudgeStatusUpdateFactGenerator(userHelperService)
	vendorRewardFulfillmentFactGenerator := generator.NewVendorRewardFulfillmentFactGenerator(dynConf, userHelperService)
	factory := generator.NewFactory(orderFactGenerator, searchFactGenerator, referrerOrderFactGenerator, referrerOrderFactGeneratorV2, salaryProgramInAppReferralRefereeFactGenerator, salaryProgramInAppReferralReferrerFactGenerator, manualGiveawayFactGenerator, fitttFactGenerator, extraInterestSdBonusPayoutFactGenerator, minBalanceFactGenerator, caAccountFactGenerator, kycFactGenerator, fitttSportsLeaderboardFactGenerator, savingsAccountUpdateFactGenerator, salaryDetectionFactGenerator, onboardingStageUpdateFactGenerator, ccTxnFactGenerator, creditCard1xRewardFactGenerator, ccTopMerchantsSpendsRewardFactGenerator, ccRequestStageUpdateFactGenerator, ccCuratedMerchantsSpendsRewardFactGenerator, ccQuarterlyAirportLoungeRewardFactGenerator, creditReportDownloadFactGenerator, salaryStatusUpdateFactGenerator, offerRedemptionStatusUpdateFactGenerator, refereeSignupFactGenerator, ccBillingFactGenerator, investmentRetentionFactGenerator, investmentWithdrawalFactGenerator, tieringPeriodicRewardFactGenerator, tieringRewardFactGenerator, usStocksRewardUnlockFactGenerator, usStocksOrderFactGenerator, referrerUSStocksFactGenerator, dcSwitchNotificationFactGenerator, epfPassbookImportFactGenerator, referrerEpfPassbookImportFactGenerator, caAccountDataSyncDataGenerator, mfExternalOrderUpdateFactGenerator, referrerMFExternalOrderUpdateFactGenerator, referrerCAAccountFactGenerator, actorNudgeStatusUpdateFactGenerator, vendorRewardFulfillmentFactGenerator)
	factEngine := fact.NewFactEngine(factory)
	commsCommsClient := types.CommsClientProvider(commsClient)
	notificationService := InitializeRewardsNotificationService(db, actorClient, commsCommsClient, conf, dynConf, rewardsNotificationEventSqsCustomDelayPublisher, redisClient)
	iNotificationService := iNotificationServiceProvider(notificationService)
	rewardsCxActivityHelperService := helper.NewRewardsCxActivityHelperService(eventsBroker, dynConf)
	generatorConsumerService := consumer2.NewGeneratorConsumerService(customRuleEngine, factEngine, crdbRewardsDao, crdbRewardOfferDao, rewardPublisher, eventsBroker, iNotificationService, rewardsGenerationEventSnsPublisher, claimRewardEventDelayPublisher, unlockerDelayPublisher, rewardsRewardExpirySqsCustomDelayPublisher, projectionUpdateEventPublisher, rewardsClient, conf, dynConf, rewardsCxActivityHelperService, userHelperService)
	return generatorConsumerService, nil
}

func InitializeProcessorFactory(db types2.RewardsPGDB, healthEngineClient health_engine.HealthEngineServiceClient, redisClient *redis.Client, accrualClient accrual.AccrualClient, accountPiRelationClient account_pi.AccountPIRelationClient, orderServiceClient order.OrderServiceClient, payClient pay.PayClient, depositClient deposit.DepositClient, actorClient actor.ActorClient, usersClient user.UsersClient, vkycClient vkyc.VKYCClient, accountTieringClient tiering.TieringClient, userGroupClient group.GroupClient, inAppReferralClient inappreferral.InAppReferralClient, onbClient onboarding.OnboardingClient, debitCardProvClient provisioning.CardProvisioningClient, luckyDrawClient luckydraw.LuckyDrawServiceClient, conf *config.Config, timelineClient timeline.TimelineServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, segmentClient segment.SegmentationServiceClient, redemptionClient redemption.OfferRedemptionServiceClient, userIntelClient userintel.UserIntelServiceClient, employmentClient employment.EmploymentClient, dynamicConf *genconf.Config, bcClient bankcust.BankCustomerServiceClient, ffBillingClient billing.BillingClient, fireflyClient firefly.FireflyClient, ussRewardMgClient rewards2.UssRewardManagerClient, savingsClient savings.SavingsClient) *rewardfulfillment.Factory {
	clock := lock.NewRealClockProvider()
	csisDownTimeCheck := downtime.NewCsisDownTimeCheck(healthEngineClient, clock)
	processingRequestDaoImpl := dao4.NewProcessingRequestDaoImpl(db)
	rewardsPayoutConfig := rewardsPayoutConfigProvider(conf)
	cashRewardProcessor := rewardfulfillment.NewCashRewardProcessor(actorClient, timelineClient, orderServiceClient, payClient, accountPiRelationClient, csisDownTimeCheck, processingRequestDaoImpl, rewardsPayoutConfig, dynamicConf, savingsClient)
	v := RewardfulfillmentSvcOptProvider()
	fiCoinsRewardProcessor := rewardfulfillment.NewFiCoinsRewardProcessor(accrualClient, fireflyClient, processingRequestDaoImpl, v...)
	giftHamperRequestDaoImpl := dao4.NewGiftHamperRequestDaoImpl(db)
	giftHamperRewardProcessor := rewardfulfillment.NewGiftHamperRewardProcessor(processingRequestDaoImpl, giftHamperRequestDaoImpl)
	luckyDrawRewardProcessor := rewardfulfillment.NewLuckyDrawRewardProcessor(processingRequestDaoImpl, luckyDrawClient)
	flags := flagConfigProvider(conf)
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(redisClient, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisV9LockManager)
	userHelperService := helper.NewUserHelperService(redisClient, actorClient, usersClient, userGroupClient, accountTieringClient, inAppReferralClient, orderServiceClient, onbClient, debitCardProvClient, vkycClient, salaryProgramClient, segmentClient, userIntelClient, employmentClient, flags, bcClient, savingsClient, crdbRewardsDao, dynamicConf)
	sDRewardProcessor := rewardfulfillment.NewSDRewardProcessor(depositClient, orderServiceClient, processingRequestDaoImpl, userHelperService, timelineClient, actorClient, rewardsPayoutConfig, csisDownTimeCheck)
	noOpProcessor := rewardfulfillment.NewNoOpProcessor()
	gormDB := GormProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	egvBasketProcessor := rewardfulfillment.NewEgvBasketProcessor(redemptionClient, processingRequestDaoImpl, gormTxnExecutor)
	noRewardProcessor := rewardfulfillment.NewNoRewardProcessor(processingRequestDaoImpl)
	thriweBenefitsPackageRewardProcessor := rewardfulfillment.NewThriweBenefitsPackageRewardProcessor(processingRequestDaoImpl, redemptionClient)
	creditCardBillEraserRewardProcessor := rewardfulfillment.NewCreditCardBillEraserRewardProcessor(processingRequestDaoImpl, ffBillingClient, fireflyClient, dynamicConf)
	usstockRewardProcessor := rewardfulfillment.NewUsstockRewardProcessor(ussRewardMgClient, processingRequestDaoImpl)
	factory := rewardfulfillment.NewFactory(cashRewardProcessor, fiCoinsRewardProcessor, giftHamperRewardProcessor, luckyDrawRewardProcessor, sDRewardProcessor, noOpProcessor, egvBasketProcessor, noRewardProcessor, thriweBenefitsPackageRewardProcessor, creditCardBillEraserRewardProcessor, usstockRewardProcessor)
	return factory
}

func InitializeRewardProcessorSubscriberService(db types2.RewardsPGDB, healthEngineClient health_engine.HealthEngineServiceClient, redisClient types.RewardsRedisStore, rewardProcessingDelayPublisher types.RewardsProcessingSqsCustomDelayPublisher, clawbackProcessingEventPublisher types.RewardClawbackProcessingSqsPublisher, clawbackProcessingDelayPublisher types.RewardClawbackProcessingSqsDelayPublisher, rewardStatusUpdatePublisher types.RewardStatusUpdateEventSnsPublisher, winningProcessingDelayPublisher types.LuckyDrawWinningProcessingSqsDelayPublisher, rewardProcessingPublisher types.RewardsSqsPublisher, accrualClient accrual.AccrualClient, accountPiRelationClient account_pi.AccountPIRelationClient, orderServiceClient order.OrderServiceClient, payClient pay.PayClient, depositClient deposit.DepositClient, usersClient user.UsersClient, vkycClient vkyc.VKYCClient, accountTieringClient tiering.TieringClient, userGroupClient group.GroupClient, inAppReferralClient inappreferral.InAppReferralClient, onbClient onboarding.OnboardingClient, debitCardProvClient provisioning.CardProvisioningClient, luckyDrawClient luckydraw.LuckyDrawServiceClient, timelineClient timeline.TimelineServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, segmentClient segment.SegmentationServiceClient, eventsBroker events.Broker, actorClient actor.ActorClient, commsClient types.RewardsCommsClientWithInterceptors, redemptionClient redemption.OfferRedemptionServiceClient, userIntelClient userintel.UserIntelServiceClient, dynamicConf *genconf.Config, config2 *config.Config, rewardsNotificationEventSqsCustomDelayPublisher types.RewardsNotificationEventSqsCustomDelayPublisher, bcClient bankcust.BankCustomerServiceClient, employmentClient employment.EmploymentClient, savingsClient savings.SavingsClient, ffBillingClient billing.BillingClient, fireflyClient firefly.FireflyClient, ussRewardMgClient rewards2.UssRewardManagerClient) (*subscriberService.RewardProcessorSubscriberService, error) {
	client := types.RedisClientProvider(redisClient)
	factory := InitializeProcessorFactory(db, healthEngineClient, client, accrualClient, accountPiRelationClient, orderServiceClient, payClient, depositClient, actorClient, usersClient, vkycClient, accountTieringClient, userGroupClient, inAppReferralClient, onbClient, debitCardProvClient, luckyDrawClient, config2, timelineClient, salaryProgramClient, segmentClient, redemptionClient, userIntelClient, employmentClient, dynamicConf, bcClient, ffBillingClient, fireflyClient, ussRewardMgClient, savingsClient)
	iFactory := iFactoryProvider(factory)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisLockManager)
	commsCommsClient := types.CommsClientProvider(commsClient)
	notificationService := InitializeRewardsNotificationService(db, actorClient, commsCommsClient, config2, dynamicConf, rewardsNotificationEventSqsCustomDelayPublisher, redisClient)
	iNotificationService := iNotificationServiceProvider(notificationService)
	rewardsCxActivityHelperService := helper.NewRewardsCxActivityHelperService(eventsBroker, dynamicConf)
	successHandler := reward.NewSuccessHandler(crdbRewardsDao, eventsBroker, iNotificationService, rewardStatusUpdatePublisher, rewardsCxActivityHelperService)
	failureHandler := reward.NewFailureHandler(crdbRewardsDao, eventsBroker, iNotificationService, rewardsCxActivityHelperService)
	manualInterventionHandler := reward.NewManualInterventionHandler(crdbRewardsDao, eventsBroker, iNotificationService, rewardsCxActivityHelperService)
	inProgressHandler := reward.NewInProgressHandler(eventsBroker, rewardProcessingDelayPublisher, config2)
	expiryHandler := reward.NewExpiryHandler(crdbRewardsDao, eventsBroker, iNotificationService, rewardStatusUpdatePublisher)
	handlerFactory := reward.NewHandlerFactory(successHandler, failureHandler, manualInterventionHandler, inProgressHandler, expiryHandler)
	processingRequestDaoImpl := dao4.NewProcessingRequestDaoImpl(db)
	gormDB := GormProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	crdbRewardOfferDao := dao.NewCrdbRewardOfferDao(db)
	rewardProcessorService := service3.NewRewardProcessorService(iFactory, handlerFactory, processingRequestDaoImpl, crdbRewardsDao, gormTxnExecutor, eventsBroker, crdbRewardOfferDao, rewardsCxActivityHelperService)
	luckyDrawWinningDaoImpl := dao5.NewLuckyDrawWinningDaoImpl(db)
	rewardSuccessHandler := reward2.NewSuccessHandler(luckyDrawWinningDaoImpl)
	rewardFailureHandler := reward2.NewFailureHandler(luckyDrawWinningDaoImpl)
	rewardManualInterventionHandler := reward2.NewManualInterventionHandler(luckyDrawWinningDaoImpl)
	rewardInProgressHandler := reward2.NewInProgressHandler(luckyDrawWinningDaoImpl, winningProcessingDelayPublisher)
	rewardHandlerFactory := reward2.NewHandlerFactory(rewardSuccessHandler, rewardFailureHandler, rewardManualInterventionHandler, rewardInProgressHandler)
	luckyDrawRegistrationDaoImpl := dao5.NewLuckyDrawRegistrationDaoImpl(db)
	winningProcessorService := service3.NewWinningProcessorService(iFactory, rewardHandlerFactory, luckyDrawWinningDaoImpl, luckyDrawRegistrationDaoImpl, processingRequestDaoImpl, gormTxnExecutor)
	pgdbRewardsClawbackDao := dao2.NewPGDBRewardsClawbackDao(db)
	fiCoinsRewardClawbackFulfillmentProcessor := clawbackfulfillment.NewFiCoinsRewardClawbackFulfillmentProcessor(accrualClient, processingRequestDaoImpl)
	clawbackfulfillmentFactory := clawbackfulfillment.NewFactory(fiCoinsRewardClawbackFulfillmentProcessor)
	clawbackStatusSuccessHandler := clawbackStatus.NewSuccessHandler(pgdbRewardsClawbackDao)
	clawbackStatusFailureHandler := clawbackStatus.NewFailureHandler(pgdbRewardsClawbackDao)
	clawbackStatusInProgressHandler := clawbackStatus.NewInProgressHandler(clawbackProcessingDelayPublisher, config2)
	clawbackStatusHandlerFactory := clawbackStatus.NewHandlerFactory(clawbackStatusSuccessHandler, clawbackStatusFailureHandler, clawbackStatusInProgressHandler)
	clawbackProcessorService := service3.NewClawbackProcessorService(clawbackfulfillmentFactory, clawbackStatusHandlerFactory, processingRequestDaoImpl, pgdbRewardsClawbackDao, crdbRewardsDao, gormTxnExecutor)
	bulkClaimRewardsService := service3.NewBulkClaimRewardsService(crdbRewardsDao, rewardProcessingPublisher, rewardProcessingDelayPublisher, gormTxnExecutor)
	accountStatusUpdateService := service3.NewAccountStatusUpdateService(crdbRewardsDao, savingsClient, gormTxnExecutor)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	rewardProcessorSubscriberService := subscriberService.NewRewardProcessorSubscriberService(rewardProcessorService, winningProcessorService, pgdbRewardsClawbackDao, clawbackProcessorService, clawbackProcessingEventPublisher, bulkClaimRewardsService, accountStatusUpdateService, redisCacheStorage, dynamicConf)
	return rewardProcessorSubscriberService, nil
}

func InitializeLuckyDrawService(db types2.RewardsPGDB, winningProcessingPublisher types.LuckyDrawWinningProcessingSqsPublisher) *luckydraw2.LuckyDrawService {
	luckyDrawCampaignDaoImpl := dao5.NewLuckyDrawCampaignDaoImpl(db)
	luckyDrawDaoImpl := dao5.NewLuckyDrawDaoImpl(db)
	luckyDrawRegistrationDaoImpl := dao5.NewLuckyDrawRegistrationDaoImpl(db)
	luckyDrawWinningDaoImpl := dao5.NewLuckyDrawWinningDaoImpl(db)
	gormDB := GormProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	tieredRewardDistribution := distributionstrategy.NewTieredRewardDistribution(luckyDrawRegistrationDaoImpl)
	factory := distributionstrategy.NewFactory(tieredRewardDistribution)
	luckyDrawService := luckydraw2.NewLuckyDrawService(luckyDrawCampaignDaoImpl, luckyDrawDaoImpl, luckyDrawRegistrationDaoImpl, luckyDrawWinningDaoImpl, gormTxnExecutor, winningProcessingPublisher, factory)
	return luckyDrawService
}

func InitializeRewardsJobHelperService(db types2.RewardsPGDB) *jobhelper.RewardsJobHelperService {
	giftHamperRequestDaoImpl := dao4.NewGiftHamperRequestDaoImpl(db)
	rewardsJobHelperService := jobhelper.NewRewardsJobHelperService(giftHamperRequestDaoImpl)
	return rewardsJobHelperService
}

func InitializeDevService(db types2.RewardsPGDB, redisClient types.RewardsRedisStore) *developer.RewardsDevService {
	crdbRewardOfferDao := dao.NewCrdbRewardOfferDao(db)
	rewardOfferProcessor := processor.NewRewardOfferProcessor(crdbRewardOfferDao)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	client := types.RedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisLockManager)
	rewardProcessor := processor.NewRewardProcessor(crdbRewardsDao)
	pgdbRewardOfferGroupDao := dao.NewPgdbRewardOfferGroupDao(db)
	rewardOfferGroupProcessor := processor.NewRewardOfferGroupProcessor(pgdbRewardOfferGroupDao)
	luckyDrawCampaignDaoImpl := dao5.NewLuckyDrawCampaignDaoImpl(db)
	luckyDrawCampaignProcessor := processor.NewLuckyDrawCampaignProcessor(luckyDrawCampaignDaoImpl)
	luckyDrawDaoImpl := dao5.NewLuckyDrawDaoImpl(db)
	luckyDrawProcessor := processor.NewLuckyDrawProcessor(luckyDrawDaoImpl)
	luckyDrawRegistrationDaoImpl := dao5.NewLuckyDrawRegistrationDaoImpl(db)
	luckyDrawRegistrationProcessor := processor.NewLuckyDrawRegistrationProcessor(luckyDrawRegistrationDaoImpl)
	luckyDrawWinningDaoImpl := dao5.NewLuckyDrawWinningDaoImpl(db)
	luckyDrawWinningProcessor := processor.NewLuckyDrawWinningProcessor(luckyDrawWinningDaoImpl)
	rewardOfferDisplayRankProcessor := processor.NewRewardOfferDisplayRankProcessor(crdbRewardOfferDao)
	pgdbRewardsClawbackDao := dao2.NewPGDBRewardsClawbackDao(db)
	rewardClawbackProcessor := processor.NewRewardClawbackProcessor(pgdbRewardsClawbackDao)
	pgdbCreditCardRewardInfoDao := dao.NewPGDBCreditCardRewardInfoDao(db)
	creditCardRewardInfoProcessor := processor.NewCreditCardRewardInfoProcessor(pgdbCreditCardRewardInfoDao)
	rewardOfferActorOfferLevelMonthlyUtil := processor.NewRewardOfferActorOfferLevelMonthlyUtil(crdbRewardsDao)
	rewardOfferActorGroupLevelMonthlyUtil := processor.NewRewardOfferActorGroupLevelMonthlyUtil(crdbRewardsDao)
	pgdbRewardsProjectionDao := dao3.NewPGDBRewardsProjectionDao(db)
	rewardProjectionsProcessor := processor.NewRewardProjectionsProcessor(pgdbRewardsProjectionDao)
	processingRequestDaoImpl := dao4.NewProcessingRequestDaoImpl(db)
	processingRequestProcessor := processor.NewProcessingRequestProcessor(processingRequestDaoImpl)
	devFactory := developer.NewDevFactory(rewardOfferProcessor, rewardProcessor, rewardOfferGroupProcessor, luckyDrawCampaignProcessor, luckyDrawProcessor, luckyDrawRegistrationProcessor, luckyDrawWinningProcessor, rewardOfferDisplayRankProcessor, rewardClawbackProcessor, creditCardRewardInfoProcessor, rewardOfferActorOfferLevelMonthlyUtil, rewardOfferActorGroupLevelMonthlyUtil, rewardProjectionsProcessor, processingRequestProcessor)
	rewardsDevService := developer.NewRewardsDevService(devFactory)
	return rewardsDevService
}

func InitializeRewardsNotificationService(db types2.RewardsPGDB, actorClient actor.ActorClient, commsClient comms.CommsClient, conf *config.Config, dyconf *genconf.Config, rewardsNotificationEventSqsCustomDelayPublisher types.RewardsNotificationEventSqsCustomDelayPublisher, redisClient types.RewardsRedisStore) *notification.Service {
	crdbRewardOfferDao := dao.NewCrdbRewardOfferDao(db)
	rewardsNotificationParams := rewardsNotificationParms(conf)
	notificationHelper := notification.NewNotificationHelper(rewardsNotificationEventSqsCustomDelayPublisher)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	client := types.RedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisLockManager)
	notificationService := notification.NewService(commsClient, actorClient, crdbRewardOfferDao, rewardsNotificationParams, dyconf, notificationHelper, crdbRewardsDao)
	return notificationService
}

func InitializeRewardCampaignCommService(db types2.RewardsPGDB, redisClient types.RewardsRedisStore, actorClient actor.ActorClient, commsClient types.RewardsCommsClientWithInterceptors, orderClient order.OrderServiceClient, savingsClient savings.SavingsClient, conf *config.Config) *campaigncomm.Service {
	commsCommsClient := types.CommsClientProvider(commsClient)
	firstFundAddition := processor2.NewFirstFundAdditionComm(savingsClient, actorClient, orderClient, commsCommsClient)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	client := types.RedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisLockManager)
	rewardsCampaignCommConfig := rewardsCampaignCommConfigProvider(conf)
	salaryDepositV1 := processor2.NewSalaryDepositV1(savingsClient, commsCommsClient, actorClient, crdbRewardsDao, rewardsCampaignCommConfig)
	factory := processor2.NewFactory(firstFundAddition, salaryDepositV1)
	campaigncommService := campaigncomm.NewService(factory)
	return campaigncommService
}

func InitializeClawbackConsumerService(db types2.RewardsPGDB, redisClient types.RewardsRedisStore, ccAccountingClient accounting.AccountingClient, merchantClient merchant.MerchantServiceClient, ccBillingClient billing.BillingClient, ffTxnAggregatesClient pinot.TxnAggregatesClient, conf *config.Config, dynConf *genconf.Config, clawbackProcessingPublisher types.RewardClawbackProcessingSqsPublisher) *consumer3.ConsumerService {
	pgdbRewardsClawbackDao := dao2.NewPGDBRewardsClawbackDao(db)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	client := types.RedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisLockManager)
	pgdbCreditCardRewardInfoDao := dao.NewPGDBCreditCardRewardInfoDao(db)
	ccTxnReversalClawbackDecider := decider.NewCCTxnReversalClawbackDecider(crdbRewardsDao, pgdbCreditCardRewardInfoDao, ccAccountingClient, merchantClient, ccBillingClient, dynConf)
	ccBillGenClawbackDecider := decider.NewCCBillGenClawbackDecider(crdbRewardsDao, ccBillingClient)
	factory := decider.NewFactory(ccTxnReversalClawbackDecider, ccBillGenClawbackDecider)
	pgdbCreditCardRewardDao := dao.NewPGDBCreditCardRewardDao(db)
	creditCardRewardAggregator := aggregator.NewCreditCardRewardAggregator(pgdbCreditCardRewardDao, merchantClient, dynConf)
	ccRewardAmountCalculator := rewardamountcalculator.NewCCRewardAmountCalculator(creditCardRewardAggregator, ccAccountingClient, ccBillingClient, ffTxnAggregatesClient, dynConf)
	ccTxnReversal1xRewardClawbackGenerator := generator2.NewCCTxnReversal1xRewardClawbackGenerator(ccAccountingClient, ccRewardAmountCalculator)
	ccTxnReversalCuratedMerchantsSpendsOfferClawbackGenerator := generator2.NewCCTxnReversalCuratedMerchantsSpendsOfferClawbackGenerator(ccAccountingClient, crdbRewardsDao, ccRewardAmountCalculator)
	ccBillGenTopMerchantsSpendsOfferClawbackGenerator := generator2.NewCCBillGenTopMerchantsSpendsOfferClawbackGenerator(ccBillingClient, ccRewardAmountCalculator)
	generatorFactory := generator2.NewFactory(ccTxnReversal1xRewardClawbackGenerator, ccTxnReversalCuratedMerchantsSpendsOfferClawbackGenerator, ccBillGenTopMerchantsSpendsOfferClawbackGenerator)
	consumerService := consumer3.NewConsumerService(pgdbRewardsClawbackDao, factory, generatorFactory, clawbackProcessingPublisher, dynConf)
	return consumerService
}

func InitializeNotificationSvc(commsClient comms.CommsClient, nudgeClient nudge.NudgeServiceClient, actorClient actor.ActorClient, db types2.RewardsPGDB, redisClient types.RewardsRedisStore, conf *config.Config, vgAccountClient accounts.AccountsClient, docClient docs.DocsClient, savingsClient savings.SavingsClient, usersClient user.UsersClient, bcClient bankcust.BankCustomerServiceClient, authClient auth.AuthClient, rewardProjectionsClient projector.ProjectorServiceClient, ordersClient order.OrderServiceClient, rewardOffersClient rewardoffers2.RewardOffersClient, dyconf *genconf.Config) *notification.NotificationSvc {
	rewardsNotificationParams := rewardsNotificationParms(conf)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	client := types.RedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisLockManager)
	rewardUnlockedHandler := triggerhandler.NewRewardUnlockedHandler(rewardsNotificationParams, crdbRewardsDao)
	helperService := helper2.NewHelperService(commsClient, vgAccountClient, docClient, savingsClient, usersClient, bcClient, authClient, rewardProjectionsClient, ordersClient, rewardOffersClient, dyconf)
	rewardEarnedHandler := triggerhandler.NewRewardEarnedHandler(rewardsNotificationParams, crdbRewardsDao, helperService)
	rewardProcessingStatusUpdateHandler := triggerhandler.NewRewardProcessingStatusUpdateHandler(rewardsNotificationParams, crdbRewardsDao, helperService)
	triggerHandlerFactory := triggerhandler.NewTriggerHandlerFactory(rewardUnlockedHandler, rewardEarnedHandler, rewardProcessingStatusUpdateHandler)
	notificationSvc := notification.NewNotificationSvc(commsClient, nudgeClient, actorClient, triggerHandlerFactory)
	return notificationSvc
}

func InitializeNotificationConsumerService(commsClient types.RewardsCommsClientWithInterceptors, actorClient actor.ActorClient, nudgeClient nudge.NudgeServiceClient, db types2.RewardsPGDB, redisClient types.RewardsRedisStore, conf *config.Config, vgAccountClient accounts.AccountsClient, docClient docs.DocsClient, savingsClient savings.SavingsClient, usersClient user.UsersClient, bcClient bankcust.BankCustomerServiceClient, authClient auth.AuthClient, rewardProjectionsClient projector.ProjectorServiceClient, ordersClient order.OrderServiceClient, rewardOffersClient rewardoffers2.RewardOffersClient, dyconf *genconf.Config) *notification.NotificationConsumerService {
	commsCommsClient := types.CommsClientProvider(commsClient)
	notificationSvc := InitializeNotificationSvc(commsCommsClient, nudgeClient, actorClient, db, redisClient, conf, vgAccountClient, docClient, savingsClient, usersClient, bcClient, authClient, rewardProjectionsClient, ordersClient, rewardOffersClient, dyconf)
	notificationConsumerService := notification.NewConsumerService(notificationSvc)
	return notificationConsumerService
}

func InitializeUnlockerConsumerService(db types2.RewardsPGDB, orderClient order.OrderServiceClient, orderCxClient cx.CXClient, merchantClient merchant.MerchantServiceClient, txnClient payment.PaymentClient, accountPiClient account_pi.AccountPIRelationClient, actorClient actor.ActorClient, usersClient user.UsersClient, userGroupClient group.GroupClient, accountTieringClient tiering.TieringClient, inAppReferralClient inappreferral.InAppReferralClient, client onboarding.OnboardingClient, debitCardProvClient provisioning.CardProvisioningClient, searchClient search.ActionBarClient, caClient connected_account.ConnectedAccountClient, vkycClient vkyc.VKYCClient, fitttClient fittt.FitttClient, luckyDrawSvcClient luckydraw.LuckyDrawServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, segmentClient segment.SegmentationServiceClient, userIntelClient userintel.UserIntelServiceClient, employmentClient employment.EmploymentClient, redisClient types.RewardsRedisStore, publisher types.RewardsRewardUnlockerSqsPublisher, bankCustServiceClient bankcust.BankCustomerServiceClient, ccAccountingClient accounting.AccountingClient, savingsClient savings.SavingsClient, authClient auth.AuthClient, redemptionServiceClient redemption.OfferRedemptionServiceClient, categorizerClient categorizer.TxnCategorizerClient, payClient pay.PayClient, fireflyClient firefly.FireflyClient, ffBillingClient billing.BillingClient, ffTxnAggregatesClient pinot.TxnAggregatesClient, exchangerOfferClient exchanger.ExchangerOfferServiceClient, offerCatalogClient casper.OfferCatalogServiceClient, rewardsProjectionClient projector.ProjectorServiceClient, piClient paymentinstrument.PiClient, rewardProcessingPublisher types.RewardsSqsPublisher, config2 *config.Config, dynConf *genconf.Config, alfredClient alfred.AlfredClient, ussOrderMgClient order2.OrderManagerClient, epfClient epf.EpfClient, questManagerCl manager.ManagerClient, questCacheStorage types.QuestCacheStorage, eventsBroker events.Broker, rewardsBucketS3Client types.RewardsS3Client) *unlocker.Service {
	gormDB := GormProvider(db)
	pgdbUnlockerDao := dao6.NewPGDBUnlockerDao(gormDB)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	client2 := types.RedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client2, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisV9LockManager)
	flags := flagConfigProvider(config2)
	userHelperService := helper.NewUserHelperService(client2, actorClient, usersClient, userGroupClient, accountTieringClient, inAppReferralClient, orderClient, client, debitCardProvClient, vkycClient, salaryProgramClient, segmentClient, userIntelClient, employmentClient, flags, bankCustServiceClient, savingsClient, crdbRewardsDao, dynConf)
	genconfConfig := questSDKClientConfProvider(dynConf)
	cacheStorage := types.QuestCacheStorageProvider(questCacheStorage)
	questsdkClient := questsdkinit.GetQuestSDKClient(genconfConfig, questManagerCl, userGroupClient, usersClient, actorClient, segmentClient, cacheStorage, eventsBroker)
	orderFactGenerator := generator.NewOrderFactGenerator(crdbRewardsDao, orderClient, orderCxClient, merchantClient, txnClient, client, actorClient, accountPiClient, userHelperService, authClient, redemptionServiceClient, salaryProgramClient, categorizerClient, payClient, userGroupClient, usersClient, rewardsProjectionClient, piClient, config2, alfredClient, dynConf, questsdkClient, rewardsBucketS3Client)
	searchFactGenerator := generator.NewSearchFactGenerator(userHelperService)
	referrerOrderFactGenerator := generator.NewReferrerOrderFactGenerator(crdbRewardsDao, orderClient, accountPiClient, txnClient, userHelperService, config2, dynConf, payClient, userGroupClient, usersClient, actorClient, questsdkClient)
	referrerOrderFactGeneratorV2 := generator.NewReferrerOrderFactGeneratorV2(crdbRewardsDao, orderClient, accountPiClient, txnClient, userHelperService, config2, payClient, userGroupClient, usersClient, actorClient, questsdkClient)
	salaryProgramInAppReferralRefereeFactGenerator := generator.NewSalaryProgramInAppReferralRefereeFactGenerator(userHelperService, salaryProgramClient)
	salaryProgramInAppReferralReferrerFactGenerator := generator.NewSalaryProgramInAppReferralReferrerFactGenerator(userHelperService, salaryProgramClient)
	manualGiveawayFactGenerator := generator.NewManualGiveawayFactGenerator(userHelperService)
	fitttFactGenerator := generator.NewFitttFactGenerator(userHelperService, searchClient, fitttClient)
	extraInterestSdBonusPayoutFactGenerator := generator.NewExtraInterestSdBonusPayoutFactGenerator(userHelperService)
	minBalanceFactGenerator := generator.NewMinBalanceFactGenerator(userHelperService)
	caAccountFactGenerator := generator.NewCAAccountFactGenerator(userHelperService, caClient, salaryProgramClient)
	kycFactGenerator := generator.NewKYCFactGenerator(userHelperService, client)
	fitttSportsLeaderboardFactGenerator := generator.NewFitttSportsLeaderboardFactGenerator(userHelperService)
	savingsAccountUpdateFactGenerator := generator.NewSavingsAccountUpdateFactGenerator(userHelperService)
	salaryDetectionFactGenerator := generator.NewSalaryDetectionFactGenerator(userHelperService, orderClient, dynConf)
	onboardingStageUpdateFactGenerator := generator.NewOnboardingStageUpdateFactGenerator(userHelperService)
	ccTxnFactGenerator := generator.NewCcTxnFactGenerator(userHelperService, fireflyClient, actorClient, merchantClient, ccAccountingClient, categorizerClient, redemptionServiceClient, crdbRewardsDao, piClient)
	pgdbCreditCardRewardDao := dao.NewPGDBCreditCardRewardDao(db)
	creditCardRewardAggregator := aggregator.NewCreditCardRewardAggregator(pgdbCreditCardRewardDao, merchantClient, dynConf)
	ccRewardAmountCalculator := rewardamountcalculator.NewCCRewardAmountCalculator(creditCardRewardAggregator, ccAccountingClient, ffBillingClient, ffTxnAggregatesClient, dynConf)
	creditCard1xRewardFactGenerator := generator.NewCreditCard1xRewardFactGenerator(userHelperService, actorClient, merchantClient, fireflyClient, ccRewardAmountCalculator, dynConf, categorizerClient, ccAccountingClient)
	ccTopMerchantsSpendsRewardFactGenerator := generator.NewCcTopMerchantsSpendsRewardFactGenerator(userHelperService, fireflyClient, ccAccountingClient, ccRewardAmountCalculator, ffTxnAggregatesClient, dynConf)
	ccRequestStageUpdateFactGenerator := generator.NewCcRequestStageUpdateFactGenerator(userHelperService)
	ccCuratedMerchantsSpendsRewardFactGenerator := generator.NewCcCuratedMerchantsSpendsRewardFactGenerator(userHelperService, fireflyClient, ccAccountingClient, ccRewardAmountCalculator, ffTxnAggregatesClient, dynConf)
	ccQuarterlyAirportLoungeRewardFactGenerator := generator.NewCcQuarterlyAirportLoungeRewardFactGenerator(userHelperService, ffTxnAggregatesClient)
	creditReportDownloadFactGenerator := generator.NewCreditReportDownloadFactGenerator(userHelperService)
	salaryStatusUpdateFactGenerator := generator.NewSalaryStatusUpdateFactGenerator(userHelperService, salaryProgramClient)
	offerRedemptionStatusUpdateFactGenerator := generator.NewOfferRedemptionStatusUpdateFactGenerator(userHelperService, exchangerOfferClient, offerCatalogClient)
	refereeSignupFactGenerator := generator.NewRefereeSignupFactGenerator(userHelperService)
	ccBillingFactGenerator := generator.NewCcBillingFactGenerator(userHelperService, fireflyClient, ccAccountingClient, ffTxnAggregatesClient, dynConf)
	investmentRetentionFactGenerator := generator.NewInvestmentRetentionFactGenerator(userHelperService)
	investmentWithdrawalFactGenerator := generator.NewInvestmentWithdrawalFactGenerator(userHelperService, crdbRewardsDao)
	tieringPeriodicRewardFactGenerator := generator.NewTieringPeriodicRewardFactGenerator(accountTieringClient, userHelperService)
	tieringRewardFactGenerator := generator.NewTieringRewardFactGenerator()
	usStocksRewardUnlockFactGenerator := generator.NewUSStocksRewardUnlockFactGenerator(userHelperService, ussOrderMgClient)
	usStocksOrderFactGenerator := generator.NewUSStocksOrderFactGenerator(config2, dynConf, userHelperService, ussOrderMgClient, questsdkClient)
	referrerUSStocksFactGenerator := generator.NewReferrerUSStocksFactGenerator(crdbRewardsDao, userHelperService, config2, dynConf, ussOrderMgClient, questsdkClient)
	dcSwitchNotificationFactGenerator := generator.NewDcSwitchNotificationFactGenerator(dynConf, userHelperService, debitCardProvClient)
	epfPassbookImportFactGenerator := generator.NewEpfPassbookImportFactGenerator(dynConf, userHelperService, epfClient)
	referrerEpfPassbookImportFactGenerator := generator.NewReferrerEpfPassbookImportFactGenerator(crdbRewardsDao, userHelperService, config2, dynConf, epfClient)
	caAccountDataSyncDataGenerator := generator.NewCaAccountDataSyncDataGenerator(userHelperService, caClient, salaryProgramClient)
	mfExternalOrderUpdateFactGenerator := generator.NewMFExternalOrderUpdateFactGenerator(config2, dynConf, userHelperService, ussOrderMgClient)
	referrerMFExternalOrderUpdateFactGenerator := generator.NewReferrerMFExternalOrderUpdateFactGenerator(crdbRewardsDao, userHelperService, config2, dynConf)
	referrerCAAccountFactGenerator := generator.NewReferrerCAAccountFactGenerator(crdbRewardsDao, userHelperService, config2, dynConf)
	actorNudgeStatusUpdateFactGenerator := generator.NewActorNudgeStatusUpdateFactGenerator(userHelperService)
	vendorRewardFulfillmentFactGenerator := generator.NewVendorRewardFulfillmentFactGenerator(dynConf, userHelperService)
	factory := generator.NewFactory(orderFactGenerator, searchFactGenerator, referrerOrderFactGenerator, referrerOrderFactGeneratorV2, salaryProgramInAppReferralRefereeFactGenerator, salaryProgramInAppReferralReferrerFactGenerator, manualGiveawayFactGenerator, fitttFactGenerator, extraInterestSdBonusPayoutFactGenerator, minBalanceFactGenerator, caAccountFactGenerator, kycFactGenerator, fitttSportsLeaderboardFactGenerator, savingsAccountUpdateFactGenerator, salaryDetectionFactGenerator, onboardingStageUpdateFactGenerator, ccTxnFactGenerator, creditCard1xRewardFactGenerator, ccTopMerchantsSpendsRewardFactGenerator, ccRequestStageUpdateFactGenerator, ccCuratedMerchantsSpendsRewardFactGenerator, ccQuarterlyAirportLoungeRewardFactGenerator, creditReportDownloadFactGenerator, salaryStatusUpdateFactGenerator, offerRedemptionStatusUpdateFactGenerator, refereeSignupFactGenerator, ccBillingFactGenerator, investmentRetentionFactGenerator, investmentWithdrawalFactGenerator, tieringPeriodicRewardFactGenerator, tieringRewardFactGenerator, usStocksRewardUnlockFactGenerator, usStocksOrderFactGenerator, referrerUSStocksFactGenerator, dcSwitchNotificationFactGenerator, epfPassbookImportFactGenerator, referrerEpfPassbookImportFactGenerator, caAccountDataSyncDataGenerator, mfExternalOrderUpdateFactGenerator, referrerMFExternalOrderUpdateFactGenerator, referrerCAAccountFactGenerator, actorNudgeStatusUpdateFactGenerator, vendorRewardFulfillmentFactGenerator)
	factEngine := fact.NewFactEngine(factory)
	pgdbRewardOfferGroupDao := dao.NewPgdbRewardOfferGroupDao(db)
	crdbRewardOfferDao := dao.NewCrdbRewardOfferDao(db)
	pgdbRewardsProjectionDao := dao3.NewPGDBRewardsProjectionDao(db)
	customRuleEngine := ruleengine.NewCustomRuleEngine(pgdbRewardOfferGroupDao, crdbRewardOfferDao, pgdbRewardsProjectionDao, luckyDrawSvcClient, userHelperService, ccAccountingClient, crdbRewardsDao, config2, dynConf)
	unlockerService := unlocker.NewService(pgdbUnlockerDao, crdbRewardsDao, factEngine, customRuleEngine, userHelperService, rewardProcessingPublisher, crdbRewardOfferDao)
	return unlockerService
}

func InitializeExpiryConsumerService(db types2.RewardsPGDB, redisClient types.RewardsRedisStore) *expiry.Service {
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	client := types.RedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisV9LockManager)
	expiryService := expiry.NewService(crdbRewardsDao)
	return expiryService
}

func InitialiseRewardsProjectionService(db types2.RewardsPGDB) *projector2.RewardsProjectionService {
	pgdbRewardsProjectionDao := dao3.NewPGDBRewardsProjectionDao(db)
	rewardsProjectionService := projector2.NewRewardsProjectionService(pgdbRewardsProjectionDao)
	return rewardsProjectionService
}

func InitialiseRewardsProjectionConsumer(db types2.RewardsPGDB, orderClient order.OrderServiceClient, orderCxClient cx.CXClient, merchantClient merchant.MerchantServiceClient, txnClient payment.PaymentClient, accountPiClient account_pi.AccountPIRelationClient, actorClient actor.ActorClient, usersClient user.UsersClient, userGroupClient group.GroupClient, accountTieringClient tiering.TieringClient, inAppReferralClient inappreferral.InAppReferralClient, client onboarding.OnboardingClient, debitCardProvClient provisioning.CardProvisioningClient, searchClient search.ActionBarClient, caClient connected_account.ConnectedAccountClient, vkycClient vkyc.VKYCClient, fitttClient fittt.FitttClient, luckyDrawSvcClient luckydraw.LuckyDrawServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, segmentClient segment.SegmentationServiceClient, userIntelClient userintel.UserIntelServiceClient, employmentClient employment.EmploymentClient, redisClient types.RewardsRedisStore, publisher types.RewardsRewardUnlockerSqsPublisher, snsPublisher types.ProjectionEventSnsPublisher, bankCustServiceClient bankcust.BankCustomerServiceClient, ccAccountingClient accounting.AccountingClient, savingsClient savings.SavingsClient, authClient auth.AuthClient, redemptionServiceClient redemption.OfferRedemptionServiceClient, categorizerClient categorizer.TxnCategorizerClient, payClient pay.PayClient, fireflyClient firefly.FireflyClient, ffBillingClient billing.BillingClient, ffTxnAggregatesClient pinot.TxnAggregatesClient, exchangerOfferClient exchanger.ExchangerOfferServiceClient, offerCatalogClient casper.OfferCatalogServiceClient, rewardsProjectionClient projector.ProjectorServiceClient, piClient paymentinstrument.PiClient, rewardProcessingPublisher types.RewardsSqsPublisher, config2 *config.Config, dynConf *genconf.Config, alfredClient alfred.AlfredClient, ussOrderMgClient order2.OrderManagerClient, eventBroker events.Broker, epfClient epf.EpfClient, questManagerCl manager.ManagerClient, questCacheStorage types.QuestCacheStorage, rewardsBucketS3Client types.RewardsS3Client) *consumer4.ProjectionsConsumer {
	pgdbRewardsProjectionDao := dao3.NewPGDBRewardsProjectionDao(db)
	crdbRewardOfferDao := dao.NewCrdbRewardOfferDao(db)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	client2 := types.RedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client2, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisV9LockManager)
	flags := flagConfigProvider(config2)
	userHelperService := helper.NewUserHelperService(client2, actorClient, usersClient, userGroupClient, accountTieringClient, inAppReferralClient, orderClient, client, debitCardProvClient, vkycClient, salaryProgramClient, segmentClient, userIntelClient, employmentClient, flags, bankCustServiceClient, savingsClient, crdbRewardsDao, dynConf)
	genconfConfig := questSDKClientConfProvider(dynConf)
	cacheStorage := types.QuestCacheStorageProvider(questCacheStorage)
	questsdkClient := questsdkinit.GetQuestSDKClient(genconfConfig, questManagerCl, userGroupClient, usersClient, actorClient, segmentClient, cacheStorage, eventBroker)
	orderFactGenerator := generator.NewOrderFactGenerator(crdbRewardsDao, orderClient, orderCxClient, merchantClient, txnClient, client, actorClient, accountPiClient, userHelperService, authClient, redemptionServiceClient, salaryProgramClient, categorizerClient, payClient, userGroupClient, usersClient, rewardsProjectionClient, piClient, config2, alfredClient, dynConf, questsdkClient, rewardsBucketS3Client)
	searchFactGenerator := generator.NewSearchFactGenerator(userHelperService)
	referrerOrderFactGenerator := generator.NewReferrerOrderFactGenerator(crdbRewardsDao, orderClient, accountPiClient, txnClient, userHelperService, config2, dynConf, payClient, userGroupClient, usersClient, actorClient, questsdkClient)
	referrerOrderFactGeneratorV2 := generator.NewReferrerOrderFactGeneratorV2(crdbRewardsDao, orderClient, accountPiClient, txnClient, userHelperService, config2, payClient, userGroupClient, usersClient, actorClient, questsdkClient)
	salaryProgramInAppReferralRefereeFactGenerator := generator.NewSalaryProgramInAppReferralRefereeFactGenerator(userHelperService, salaryProgramClient)
	salaryProgramInAppReferralReferrerFactGenerator := generator.NewSalaryProgramInAppReferralReferrerFactGenerator(userHelperService, salaryProgramClient)
	manualGiveawayFactGenerator := generator.NewManualGiveawayFactGenerator(userHelperService)
	fitttFactGenerator := generator.NewFitttFactGenerator(userHelperService, searchClient, fitttClient)
	extraInterestSdBonusPayoutFactGenerator := generator.NewExtraInterestSdBonusPayoutFactGenerator(userHelperService)
	minBalanceFactGenerator := generator.NewMinBalanceFactGenerator(userHelperService)
	caAccountFactGenerator := generator.NewCAAccountFactGenerator(userHelperService, caClient, salaryProgramClient)
	kycFactGenerator := generator.NewKYCFactGenerator(userHelperService, client)
	fitttSportsLeaderboardFactGenerator := generator.NewFitttSportsLeaderboardFactGenerator(userHelperService)
	savingsAccountUpdateFactGenerator := generator.NewSavingsAccountUpdateFactGenerator(userHelperService)
	salaryDetectionFactGenerator := generator.NewSalaryDetectionFactGenerator(userHelperService, orderClient, dynConf)
	onboardingStageUpdateFactGenerator := generator.NewOnboardingStageUpdateFactGenerator(userHelperService)
	ccTxnFactGenerator := generator.NewCcTxnFactGenerator(userHelperService, fireflyClient, actorClient, merchantClient, ccAccountingClient, categorizerClient, redemptionServiceClient, crdbRewardsDao, piClient)
	pgdbCreditCardRewardDao := dao.NewPGDBCreditCardRewardDao(db)
	creditCardRewardAggregator := aggregator.NewCreditCardRewardAggregator(pgdbCreditCardRewardDao, merchantClient, dynConf)
	ccRewardAmountCalculator := rewardamountcalculator.NewCCRewardAmountCalculator(creditCardRewardAggregator, ccAccountingClient, ffBillingClient, ffTxnAggregatesClient, dynConf)
	creditCard1xRewardFactGenerator := generator.NewCreditCard1xRewardFactGenerator(userHelperService, actorClient, merchantClient, fireflyClient, ccRewardAmountCalculator, dynConf, categorizerClient, ccAccountingClient)
	ccTopMerchantsSpendsRewardFactGenerator := generator.NewCcTopMerchantsSpendsRewardFactGenerator(userHelperService, fireflyClient, ccAccountingClient, ccRewardAmountCalculator, ffTxnAggregatesClient, dynConf)
	ccRequestStageUpdateFactGenerator := generator.NewCcRequestStageUpdateFactGenerator(userHelperService)
	ccCuratedMerchantsSpendsRewardFactGenerator := generator.NewCcCuratedMerchantsSpendsRewardFactGenerator(userHelperService, fireflyClient, ccAccountingClient, ccRewardAmountCalculator, ffTxnAggregatesClient, dynConf)
	ccQuarterlyAirportLoungeRewardFactGenerator := generator.NewCcQuarterlyAirportLoungeRewardFactGenerator(userHelperService, ffTxnAggregatesClient)
	creditReportDownloadFactGenerator := generator.NewCreditReportDownloadFactGenerator(userHelperService)
	salaryStatusUpdateFactGenerator := generator.NewSalaryStatusUpdateFactGenerator(userHelperService, salaryProgramClient)
	offerRedemptionStatusUpdateFactGenerator := generator.NewOfferRedemptionStatusUpdateFactGenerator(userHelperService, exchangerOfferClient, offerCatalogClient)
	refereeSignupFactGenerator := generator.NewRefereeSignupFactGenerator(userHelperService)
	ccBillingFactGenerator := generator.NewCcBillingFactGenerator(userHelperService, fireflyClient, ccAccountingClient, ffTxnAggregatesClient, dynConf)
	investmentRetentionFactGenerator := generator.NewInvestmentRetentionFactGenerator(userHelperService)
	investmentWithdrawalFactGenerator := generator.NewInvestmentWithdrawalFactGenerator(userHelperService, crdbRewardsDao)
	tieringPeriodicRewardFactGenerator := generator.NewTieringPeriodicRewardFactGenerator(accountTieringClient, userHelperService)
	tieringRewardFactGenerator := generator.NewTieringRewardFactGenerator()
	usStocksRewardUnlockFactGenerator := generator.NewUSStocksRewardUnlockFactGenerator(userHelperService, ussOrderMgClient)
	usStocksOrderFactGenerator := generator.NewUSStocksOrderFactGenerator(config2, dynConf, userHelperService, ussOrderMgClient, questsdkClient)
	referrerUSStocksFactGenerator := generator.NewReferrerUSStocksFactGenerator(crdbRewardsDao, userHelperService, config2, dynConf, ussOrderMgClient, questsdkClient)
	dcSwitchNotificationFactGenerator := generator.NewDcSwitchNotificationFactGenerator(dynConf, userHelperService, debitCardProvClient)
	epfPassbookImportFactGenerator := generator.NewEpfPassbookImportFactGenerator(dynConf, userHelperService, epfClient)
	referrerEpfPassbookImportFactGenerator := generator.NewReferrerEpfPassbookImportFactGenerator(crdbRewardsDao, userHelperService, config2, dynConf, epfClient)
	caAccountDataSyncDataGenerator := generator.NewCaAccountDataSyncDataGenerator(userHelperService, caClient, salaryProgramClient)
	mfExternalOrderUpdateFactGenerator := generator.NewMFExternalOrderUpdateFactGenerator(config2, dynConf, userHelperService, ussOrderMgClient)
	referrerMFExternalOrderUpdateFactGenerator := generator.NewReferrerMFExternalOrderUpdateFactGenerator(crdbRewardsDao, userHelperService, config2, dynConf)
	referrerCAAccountFactGenerator := generator.NewReferrerCAAccountFactGenerator(crdbRewardsDao, userHelperService, config2, dynConf)
	actorNudgeStatusUpdateFactGenerator := generator.NewActorNudgeStatusUpdateFactGenerator(userHelperService)
	vendorRewardFulfillmentFactGenerator := generator.NewVendorRewardFulfillmentFactGenerator(dynConf, userHelperService)
	factory := generator.NewFactory(orderFactGenerator, searchFactGenerator, referrerOrderFactGenerator, referrerOrderFactGeneratorV2, salaryProgramInAppReferralRefereeFactGenerator, salaryProgramInAppReferralReferrerFactGenerator, manualGiveawayFactGenerator, fitttFactGenerator, extraInterestSdBonusPayoutFactGenerator, minBalanceFactGenerator, caAccountFactGenerator, kycFactGenerator, fitttSportsLeaderboardFactGenerator, savingsAccountUpdateFactGenerator, salaryDetectionFactGenerator, onboardingStageUpdateFactGenerator, ccTxnFactGenerator, creditCard1xRewardFactGenerator, ccTopMerchantsSpendsRewardFactGenerator, ccRequestStageUpdateFactGenerator, ccCuratedMerchantsSpendsRewardFactGenerator, ccQuarterlyAirportLoungeRewardFactGenerator, creditReportDownloadFactGenerator, salaryStatusUpdateFactGenerator, offerRedemptionStatusUpdateFactGenerator, refereeSignupFactGenerator, ccBillingFactGenerator, investmentRetentionFactGenerator, investmentWithdrawalFactGenerator, tieringPeriodicRewardFactGenerator, tieringRewardFactGenerator, usStocksRewardUnlockFactGenerator, usStocksOrderFactGenerator, referrerUSStocksFactGenerator, dcSwitchNotificationFactGenerator, epfPassbookImportFactGenerator, referrerEpfPassbookImportFactGenerator, caAccountDataSyncDataGenerator, mfExternalOrderUpdateFactGenerator, referrerMFExternalOrderUpdateFactGenerator, referrerCAAccountFactGenerator, actorNudgeStatusUpdateFactGenerator, vendorRewardFulfillmentFactGenerator)
	factEngine := fact.NewFactEngine(factory)
	pgdbRewardOfferGroupDao := dao.NewPgdbRewardOfferGroupDao(db)
	customRuleEngine := ruleengine.NewCustomRuleEngine(pgdbRewardOfferGroupDao, crdbRewardOfferDao, pgdbRewardsProjectionDao, luckyDrawSvcClient, userHelperService, ccAccountingClient, crdbRewardsDao, config2, dynConf)
	rewardsCxActivityHelperService := helper.NewRewardsCxActivityHelperService(eventBroker, dynConf)
	projectionsConsumer := consumer4.NewProjectionsConsumer(pgdbRewardsProjectionDao, crdbRewardOfferDao, factEngine, customRuleEngine, snsPublisher, rewardsCxActivityHelperService, crdbRewardsDao)
	return projectionsConsumer
}

func InitializeRewardsSimulator(merchantClient merchant.MerchantServiceClient, txnClient payment.PaymentClient, client onboarding.OnboardingClient, actorClient actor.ActorClient, accountPiClient account_pi.AccountPIRelationClient, authClient auth.AuthClient, redemption2 redemption.OfferRedemptionServiceClient, salaryProgramClient salaryprogram.SalaryProgramClient, txnCatClient categorizer.TxnCategorizerClient, payClient pay.PayClient, userGroupClient group.GroupClient, usersClient user.UsersClient, conf *config.Config, dynConf *genconf.Config, searchClient search.ActionBarClient, caClient connected_account.ConnectedAccountClient, fireflyClient firefly.FireflyClient, ffAccClient accounting.AccountingClient, ffTxnAggregatesClient pinot.TxnAggregatesClient, exchangerOfferClient exchanger.ExchangerOfferServiceClient, offerCatalogClient casper.OfferCatalogServiceClient, accountTieringClient tiering.TieringClient, inAppReferralClient inappreferral.InAppReferralClient, debitCardProvClient provisioning.CardProvisioningClient, vkycClient vkyc.VKYCClient, segmentClient segment.SegmentationServiceClient, userIntelClient userintel.UserIntelServiceClient, employmentClient employment.EmploymentClient, bcClient bankcust.BankCustomerServiceClient, savingsClient savings.SavingsClient, ffBillingClient billing.BillingClient, redisClient types.RewardsRedisStore, orderCxClient cx.CXClient, piClient paymentinstrument.PiClient, rewardsProjectionClient projector.ProjectorServiceClient, db types2.RewardsPGDB, fitttClient fittt.FitttClient, orderClient order.OrderServiceClient, rewardsPublisher types.RewardsOrderUpdateEventQueueSqsPublisher, alfredClient alfred.AlfredClient, ussOrderMgClient order2.OrderManagerClient, ccTxnPublisher types.RewardsCreditCardTxnEventQueueSqsPublisher, ccBillingEventPublisher types.RewardsCreditCardBillingEventQueueSqsPublisher, epfClient epf.EpfClient, questManagerCl manager.ManagerClient, questCacheStorage types.QuestCacheStorage, eventsBroker events.Broker, rewardsBucketS3Client types.RewardsS3Client) *rewardsimulator.GenerateRewardSimulatorService {
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	client2 := types.RedisClientProvider(redisClient)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client2, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisV9LockManager)
	flags := flagConfigProvider(conf)
	userHelperService := helper.NewUserHelperService(client2, actorClient, usersClient, userGroupClient, accountTieringClient, inAppReferralClient, orderClient, client, debitCardProvClient, vkycClient, salaryProgramClient, segmentClient, userIntelClient, employmentClient, flags, bcClient, savingsClient, crdbRewardsDao, dynConf)
	genconfConfig := questSDKClientConfProvider(dynConf)
	cacheStorage := types.QuestCacheStorageProvider(questCacheStorage)
	questsdkClient := questsdkinit.GetQuestSDKClient(genconfConfig, questManagerCl, userGroupClient, usersClient, actorClient, segmentClient, cacheStorage, eventsBroker)
	orderFactGenerator := generator.NewOrderFactGenerator(crdbRewardsDao, orderClient, orderCxClient, merchantClient, txnClient, client, actorClient, accountPiClient, userHelperService, authClient, redemption2, salaryProgramClient, txnCatClient, payClient, userGroupClient, usersClient, rewardsProjectionClient, piClient, conf, alfredClient, dynConf, questsdkClient, rewardsBucketS3Client)
	searchFactGenerator := generator.NewSearchFactGenerator(userHelperService)
	referrerOrderFactGenerator := generator.NewReferrerOrderFactGenerator(crdbRewardsDao, orderClient, accountPiClient, txnClient, userHelperService, conf, dynConf, payClient, userGroupClient, usersClient, actorClient, questsdkClient)
	referrerOrderFactGeneratorV2 := generator.NewReferrerOrderFactGeneratorV2(crdbRewardsDao, orderClient, accountPiClient, txnClient, userHelperService, conf, payClient, userGroupClient, usersClient, actorClient, questsdkClient)
	salaryProgramInAppReferralRefereeFactGenerator := generator.NewSalaryProgramInAppReferralRefereeFactGenerator(userHelperService, salaryProgramClient)
	salaryProgramInAppReferralReferrerFactGenerator := generator.NewSalaryProgramInAppReferralReferrerFactGenerator(userHelperService, salaryProgramClient)
	manualGiveawayFactGenerator := generator.NewManualGiveawayFactGenerator(userHelperService)
	fitttFactGenerator := generator.NewFitttFactGenerator(userHelperService, searchClient, fitttClient)
	extraInterestSdBonusPayoutFactGenerator := generator.NewExtraInterestSdBonusPayoutFactGenerator(userHelperService)
	minBalanceFactGenerator := generator.NewMinBalanceFactGenerator(userHelperService)
	caAccountFactGenerator := generator.NewCAAccountFactGenerator(userHelperService, caClient, salaryProgramClient)
	kycFactGenerator := generator.NewKYCFactGenerator(userHelperService, client)
	fitttSportsLeaderboardFactGenerator := generator.NewFitttSportsLeaderboardFactGenerator(userHelperService)
	savingsAccountUpdateFactGenerator := generator.NewSavingsAccountUpdateFactGenerator(userHelperService)
	salaryDetectionFactGenerator := generator.NewSalaryDetectionFactGenerator(userHelperService, orderClient, dynConf)
	onboardingStageUpdateFactGenerator := generator.NewOnboardingStageUpdateFactGenerator(userHelperService)
	ccTxnFactGenerator := generator.NewCcTxnFactGenerator(userHelperService, fireflyClient, actorClient, merchantClient, ffAccClient, txnCatClient, redemption2, crdbRewardsDao, piClient)
	pgdbCreditCardRewardDao := dao.NewPGDBCreditCardRewardDao(db)
	creditCardRewardAggregator := aggregator.NewCreditCardRewardAggregator(pgdbCreditCardRewardDao, merchantClient, dynConf)
	ccRewardAmountCalculator := rewardamountcalculator.NewCCRewardAmountCalculator(creditCardRewardAggregator, ffAccClient, ffBillingClient, ffTxnAggregatesClient, dynConf)
	creditCard1xRewardFactGenerator := generator.NewCreditCard1xRewardFactGenerator(userHelperService, actorClient, merchantClient, fireflyClient, ccRewardAmountCalculator, dynConf, txnCatClient, ffAccClient)
	ccTopMerchantsSpendsRewardFactGenerator := generator.NewCcTopMerchantsSpendsRewardFactGenerator(userHelperService, fireflyClient, ffAccClient, ccRewardAmountCalculator, ffTxnAggregatesClient, dynConf)
	ccRequestStageUpdateFactGenerator := generator.NewCcRequestStageUpdateFactGenerator(userHelperService)
	ccCuratedMerchantsSpendsRewardFactGenerator := generator.NewCcCuratedMerchantsSpendsRewardFactGenerator(userHelperService, fireflyClient, ffAccClient, ccRewardAmountCalculator, ffTxnAggregatesClient, dynConf)
	ccQuarterlyAirportLoungeRewardFactGenerator := generator.NewCcQuarterlyAirportLoungeRewardFactGenerator(userHelperService, ffTxnAggregatesClient)
	creditReportDownloadFactGenerator := generator.NewCreditReportDownloadFactGenerator(userHelperService)
	salaryStatusUpdateFactGenerator := generator.NewSalaryStatusUpdateFactGenerator(userHelperService, salaryProgramClient)
	offerRedemptionStatusUpdateFactGenerator := generator.NewOfferRedemptionStatusUpdateFactGenerator(userHelperService, exchangerOfferClient, offerCatalogClient)
	refereeSignupFactGenerator := generator.NewRefereeSignupFactGenerator(userHelperService)
	ccBillingFactGenerator := generator.NewCcBillingFactGenerator(userHelperService, fireflyClient, ffAccClient, ffTxnAggregatesClient, dynConf)
	investmentRetentionFactGenerator := generator.NewInvestmentRetentionFactGenerator(userHelperService)
	investmentWithdrawalFactGenerator := generator.NewInvestmentWithdrawalFactGenerator(userHelperService, crdbRewardsDao)
	tieringPeriodicRewardFactGenerator := generator.NewTieringPeriodicRewardFactGenerator(accountTieringClient, userHelperService)
	tieringRewardFactGenerator := generator.NewTieringRewardFactGenerator()
	usStocksRewardUnlockFactGenerator := generator.NewUSStocksRewardUnlockFactGenerator(userHelperService, ussOrderMgClient)
	usStocksOrderFactGenerator := generator.NewUSStocksOrderFactGenerator(conf, dynConf, userHelperService, ussOrderMgClient, questsdkClient)
	referrerUSStocksFactGenerator := generator.NewReferrerUSStocksFactGenerator(crdbRewardsDao, userHelperService, conf, dynConf, ussOrderMgClient, questsdkClient)
	dcSwitchNotificationFactGenerator := generator.NewDcSwitchNotificationFactGenerator(dynConf, userHelperService, debitCardProvClient)
	epfPassbookImportFactGenerator := generator.NewEpfPassbookImportFactGenerator(dynConf, userHelperService, epfClient)
	referrerEpfPassbookImportFactGenerator := generator.NewReferrerEpfPassbookImportFactGenerator(crdbRewardsDao, userHelperService, conf, dynConf, epfClient)
	caAccountDataSyncDataGenerator := generator.NewCaAccountDataSyncDataGenerator(userHelperService, caClient, salaryProgramClient)
	mfExternalOrderUpdateFactGenerator := generator.NewMFExternalOrderUpdateFactGenerator(conf, dynConf, userHelperService, ussOrderMgClient)
	referrerMFExternalOrderUpdateFactGenerator := generator.NewReferrerMFExternalOrderUpdateFactGenerator(crdbRewardsDao, userHelperService, conf, dynConf)
	referrerCAAccountFactGenerator := generator.NewReferrerCAAccountFactGenerator(crdbRewardsDao, userHelperService, conf, dynConf)
	actorNudgeStatusUpdateFactGenerator := generator.NewActorNudgeStatusUpdateFactGenerator(userHelperService)
	vendorRewardFulfillmentFactGenerator := generator.NewVendorRewardFulfillmentFactGenerator(dynConf, userHelperService)
	factory := generator.NewFactory(orderFactGenerator, searchFactGenerator, referrerOrderFactGenerator, referrerOrderFactGeneratorV2, salaryProgramInAppReferralRefereeFactGenerator, salaryProgramInAppReferralReferrerFactGenerator, manualGiveawayFactGenerator, fitttFactGenerator, extraInterestSdBonusPayoutFactGenerator, minBalanceFactGenerator, caAccountFactGenerator, kycFactGenerator, fitttSportsLeaderboardFactGenerator, savingsAccountUpdateFactGenerator, salaryDetectionFactGenerator, onboardingStageUpdateFactGenerator, ccTxnFactGenerator, creditCard1xRewardFactGenerator, ccTopMerchantsSpendsRewardFactGenerator, ccRequestStageUpdateFactGenerator, ccCuratedMerchantsSpendsRewardFactGenerator, ccQuarterlyAirportLoungeRewardFactGenerator, creditReportDownloadFactGenerator, salaryStatusUpdateFactGenerator, offerRedemptionStatusUpdateFactGenerator, refereeSignupFactGenerator, ccBillingFactGenerator, investmentRetentionFactGenerator, investmentWithdrawalFactGenerator, tieringPeriodicRewardFactGenerator, tieringRewardFactGenerator, usStocksRewardUnlockFactGenerator, usStocksOrderFactGenerator, referrerUSStocksFactGenerator, dcSwitchNotificationFactGenerator, epfPassbookImportFactGenerator, referrerEpfPassbookImportFactGenerator, caAccountDataSyncDataGenerator, mfExternalOrderUpdateFactGenerator, referrerMFExternalOrderUpdateFactGenerator, referrerCAAccountFactGenerator, actorNudgeStatusUpdateFactGenerator, vendorRewardFulfillmentFactGenerator)
	factEngine := fact.NewFactEngine(factory)
	crdbRewardOfferDao := dao.NewCrdbRewardOfferDao(db)
	generateRewardSimulatorService := rewardsimulator.NewGenerateRewardSimulatorService(factEngine, crdbRewardOfferDao, crdbRewardsDao, fitttClient, orderClient, orderCxClient, ffAccClient, actorClient, txnCatClient, ffBillingClient, rewardsPublisher, ccTxnPublisher, ccBillingEventPublisher)
	return generateRewardSimulatorService
}

func InitializeRewardsPinotAggregateService(conf *config.Config, rewardsClient rewards.RewardsGeneratorClient, fireflyClient firefly.FireflyClient) *pinot2.Service {
	client := pinotClientProvider(conf)
	pinotTerminalRewardsDao := dao7.NewPinotTerminalRewardsDao(client)
	pinotRewardProjectionsDao := dao7.NewPinotRewardProjectionsDao(client)
	defaultTime := datetime.NewDefaultTime()
	pinotService := pinot2.NewService(pinotTerminalRewardsDao, pinotRewardProjectionsDao, rewardsClient, fireflyClient, defaultTime)
	return pinotService
}

func InitializeRewardsPinotService(db types2.RewardsPGDB, ccAccountingClient accounting.AccountingClient, categorizerClient categorizer.TxnCategorizerClient, merchantClient merchant.MerchantServiceClient, actorClient actor.ActorClient, orderServiceClient order.OrderServiceClient, usersClient user.UsersClient, userGroupClient group.GroupClient, accountTieringClient tiering.TieringClient, inAppReferralClient inappreferral.InAppReferralClient, onboardingClient onboarding.OnboardingClient, debitCardProvClient provisioning.CardProvisioningClient, vkycClient vkyc.VKYCClient, salaryProgramClient salaryprogram.SalaryProgramClient, segmentClient segment.SegmentationServiceClient, userIntelClient userintel.UserIntelServiceClient, employmentClient employment.EmploymentClient, bcClient bankcust.BankCustomerServiceClient, savingsClient savings.SavingsClient, conf *config.Config, dynConf *genconf.Config, redisClient types.RewardsRedisStore) (*consumer5.RewardsPinotService, error) {
	crdbRewardOfferDao := dao.NewCrdbRewardOfferDao(db)
	orderEventSpecificPinotRewardInfo := eventspecificpinotrewardinfo.NewOrderEventSpecificPinotRewardInfo(orderServiceClient, actorClient, merchantClient, categorizerClient)
	creditCardTransactionEventSpecificPinotRewardInfo := eventspecificpinotrewardinfo.NewCreditCardTransactionEventSpecificPinotRewardInfo(actorClient, merchantClient, categorizerClient, ccAccountingClient)
	rewardsPinotEventSpecificInfoFactory := eventspecificpinotrewardinfo.NewRewardsPinotEventSpecificInfoFactory(orderEventSpecificPinotRewardInfo, creditCardTransactionEventSpecificPinotRewardInfo)
	client := types.RedisClientProvider(redisClient)
	flags := flagConfigProvider(conf)
	clock := lock.NewRealClockProvider()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	uuidGenerator := idgen.NewUuidGenerator()
	redisV9LockManager := lock.NewRedisV9LockManager(client, clock, uuidGenerator)
	crdbRewardsDao := dao.NewRewardsDao(db, domainIdGenerator, redisV9LockManager)
	userHelperService := helper.NewUserHelperService(client, actorClient, usersClient, userGroupClient, accountTieringClient, inAppReferralClient, orderServiceClient, onboardingClient, debitCardProvClient, vkycClient, salaryProgramClient, segmentClient, userIntelClient, employmentClient, flags, bcClient, savingsClient, crdbRewardsDao, dynConf)
	terminalPinotRewardProducer, err := terminalPinotRewardProducerProvider(conf)
	if err != nil {
		return nil, err
	}
	pinotProjectionProducer, err := pinotProjectionProducerProvider(conf)
	if err != nil {
		return nil, err
	}
	nonTerminalPinotRewardProducer, err := nonTerminalPinotRewardProducerProvider(conf)
	if err != nil {
		return nil, err
	}
	rewardsPinotService := consumer5.NewRewardsPinotService(crdbRewardOfferDao, rewardsPinotEventSpecificInfoFactory, userHelperService, terminalPinotRewardProducer, pinotProjectionProducer, nonTerminalPinotRewardProducer)
	return rewardsPinotService, nil
}

func InitializeRequeueServiceConsumerService(ccAccountingClient accounting.AccountingClient, orderClient order.OrderServiceClient, orderUpdateEventPublisher types.RewardsOrderUpdateEventQueueSqsPublisher, ccTxnEventPublisher types.RewardsCreditCardTxnEventQueueSqsPublisher, conf *config.Config) *consumer6.RequeueServiceConsumerService {
	requeueServiceConsumerService := consumer6.NewRequeueServiceConsumerService(ccAccountingClient, orderClient, orderUpdateEventPublisher, ccTxnEventPublisher, conf)
	return requeueServiceConsumerService
}

// wire.go:

func GormProvider(db types2.RewardsPGDB) *gorm.DB {
	return db
}

func terminalPinotRewardProducerProvider(rewardsConf *config.Config) (types.TerminalPinotRewardProducer, error) {
	partitionConstructor := func(_ string) sarama.Partitioner {
		return partitioner.NewMurMur2Partitioner()
	}

	kafkaConfig := &producer.Config{
		Brokers:          rewardsConf.TerminalPinotRewardProducer.Brokers,
		RequiredAcks:     sarama.WaitForAll,
		Partitioner:      partitionConstructor,
		TopicName:        rewardsConf.TerminalPinotRewardProducer.TopicName,
		SaslEnabled:      rewardsConf.TerminalPinotRewardProducer.SASLConfig.Enabled,
		Username:         rewardsConf.TerminalPinotRewardProducer.SASLConfig.Username,
		Password:         rewardsConf.TerminalPinotRewardProducer.SASLConfig.Password,
		TlsEnabled:       rewardsConf.TerminalPinotRewardProducer.TLSConfig.Enabled,
		Cert:             []byte(rewardsConf.TerminalPinotRewardProducer.TLSConfig.Certificate),
		FlushFrequency:   rewardsConf.TerminalPinotRewardProducer.FlushFrequency,
		FlushMessages:    rewardsConf.TerminalPinotRewardProducer.FlushMessages,
		FlushMaxMessages: rewardsConf.TerminalPinotRewardProducer.FlushMaxMessages,
		FlushMaxBytes:    rewardsConf.TerminalPinotRewardProducer.FlushMaxBytes,
	}

	return producer.NewKafkaStreamProducer(kafkaConfig, rewardsConf.Application.Environment)
}

func pinotProjectionProducerProvider(rewardsConf *config.Config) (types.PinotProjectionProducer, error) {
	partitionConstructor := func(_ string) sarama.Partitioner {
		return partitioner.NewMurMur2Partitioner()
	}

	kafkaConfig := &producer.Config{
		Brokers:          rewardsConf.PinotProjectionProducer.Brokers,
		RequiredAcks:     sarama.WaitForAll,
		Partitioner:      partitionConstructor,
		TopicName:        rewardsConf.PinotProjectionProducer.TopicName,
		SaslEnabled:      rewardsConf.PinotProjectionProducer.SASLConfig.Enabled,
		Username:         rewardsConf.PinotProjectionProducer.SASLConfig.Username,
		Password:         rewardsConf.PinotProjectionProducer.SASLConfig.Password,
		TlsEnabled:       rewardsConf.PinotProjectionProducer.TLSConfig.Enabled,
		Cert:             []byte(rewardsConf.PinotProjectionProducer.TLSConfig.Certificate),
		FlushFrequency:   rewardsConf.PinotProjectionProducer.FlushFrequency,
		FlushMessages:    rewardsConf.PinotProjectionProducer.FlushMessages,
		FlushMaxMessages: rewardsConf.PinotProjectionProducer.FlushMaxMessages,
		FlushMaxBytes:    rewardsConf.PinotProjectionProducer.FlushMaxBytes,
	}

	return producer.NewKafkaStreamProducer(kafkaConfig, rewardsConf.Application.Environment)
}

func nonTerminalPinotRewardProducerProvider(rewardsConf *config.Config) (types.NonTerminalPinotRewardProducer, error) {
	partitionConstructor := func(_ string) sarama.Partitioner {
		return partitioner.NewMurMur2Partitioner()
	}

	kafkaConfig := &producer.Config{
		Brokers:          rewardsConf.NonTerminalPinotRewardProducer.Brokers,
		RequiredAcks:     sarama.WaitForAll,
		Partitioner:      partitionConstructor,
		TopicName:        rewardsConf.NonTerminalPinotRewardProducer.TopicName,
		SaslEnabled:      rewardsConf.NonTerminalPinotRewardProducer.SASLConfig.Enabled,
		Username:         rewardsConf.NonTerminalPinotRewardProducer.SASLConfig.Username,
		Password:         rewardsConf.NonTerminalPinotRewardProducer.SASLConfig.Password,
		TlsEnabled:       rewardsConf.NonTerminalPinotRewardProducer.TLSConfig.Enabled,
		Cert:             []byte(rewardsConf.NonTerminalPinotRewardProducer.TLSConfig.Certificate),
		FlushFrequency:   rewardsConf.NonTerminalPinotRewardProducer.FlushFrequency,
		FlushMessages:    rewardsConf.NonTerminalPinotRewardProducer.FlushMessages,
		FlushMaxMessages: rewardsConf.NonTerminalPinotRewardProducer.FlushMaxMessages,
		FlushMaxBytes:    rewardsConf.NonTerminalPinotRewardProducer.FlushMaxBytes,
	}

	return producer.NewKafkaStreamProducer(kafkaConfig, rewardsConf.Application.Environment)
}

func iFactoryProvider(factory *rewardfulfillment.Factory) rewardfulfillment.IFactory {
	return factory
}

func flagConfigProvider(conf *config.Config) *config.Flags {
	return conf.Flags
}

func rewardsPayoutConfigProvider(conf *config.Config) *config.RewardsPayoutConfig {
	return conf.RewardsPayoutConfig
}

func iNotificationServiceProvider(notificationService *notification.Service) notification.INotificationService {
	return notificationService
}

func slackClientProvider(conf *config.Config) *slack.Client {
	var slackClient *slack.Client
	if cfg.IsProdEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config.SlackOauthTokenKey], slack.OptionDebug(false))
	} else if cfg.IsSimulatedEnv(conf.Application.Environment) {
		slackClient = slack.New(conf.Secrets.Ids[config.SlackOauthTokenKey], slack.OptionDebug(true))
	}
	return slackClient
}

func questSDKClientConfProvider(conf *genconf.Config) *genconf2.Config {
	return conf.QuestSdk()
}

func bedrockClientProvider(awsConf aws.Config) *bedrockruntime.Client {
	return bedrockruntime.NewFromConfig(awsConf)
}

func rewardsNotificationParms(conf *config.Config) *config.RewardsNotificationParams {
	return conf.RewardsNotificationParams
}

func rewardsCampaignCommConfigProvider(conf *config.Config) *config.RewardsCampaignCommConfig {
	return conf.RewardsCampaignCommConfig
}

func rewardsNotificationParamsProvider(conf *config.Config) *config.RewardsNotificationParams {
	return conf.RewardsNotificationParams
}

func pinotClientProvider(conf *config.Config) pinot3.Client {
	env, _ := cfg.GetEnvironment()
	client, _ := pinot3.NewPinotClient(conf.PinotConfig, env)
	return client
}

func RewardfulfillmentSvcOptProvider() []rewardfulfillment.Opt {
	return nil
}
