package helper

import (
	orderPb "github.com/epifi/gamma/api/order"
	rewardsPb "github.com/epifi/gamma/api/rewards"
)

var (
	// Reward generation is allowed only for following the order workflows.
	// For CREATE_DEPOSIT and PRE_CLOSE_DEPOSIT order workflows the corresponding NO_OP order would be used for triggering reward, so ignored those workflows in allowed list.
	AllowedOrderWorkflowsForRewardGeneration = []orderPb.OrderWorkflow{orderPb.OrderWorkflow_P2P_FUND_TRANSFER, orderPb.OrderWorkflow_P2P_COLLECT, orderPb.OrderWorkflow_P2P_COLLECT_SHORT_CIRCUIT,
		orderPb.OrderWorkflow_NO_OP, orderPb.OrderWorkflow_URN_TRANSFER, orderPb.OrderWorkflow_ADD_FUNDS, orderPb.OrderWorkflow_ADD_FUNDS_SD, orderPb.OrderWorkflow_ADD_FUNDS_COLLECT,
		orderPb.OrderWorkflow_EXECUTE_RECURRING_PAYMENT, orderPb.OrderWorkflow_COLLECT_ONE_TIME_RECURRING_PAYMENT, orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_WITH_AUTH,
		orderPb.OrderWorkflow_COLLECT_RECURRING_PAYMENT_NO_AUTH, orderPb.OrderWorkflow_MUTUAL_FUNDS_INVEST_POST_PAID, orderPb.OrderWorkflow_P2P_INVESTMENT, orderPb.OrderWorkflow_OFF_APP_UPI, orderPb.OrderWorkflow_INTERNATIONAL_FUND_TRANSFER}
	// Reward generation is ONLY supported for following order statuses.
	AllowedOrderStatusesForRewardGeneration = []orderPb.OrderStatus{orderPb.OrderStatus_FULFILLED, orderPb.OrderStatus_PAID, orderPb.OrderStatus_SETTLED}
)

// CheckIfNoRewardType checks if the given reward type is NO_REWARD.
func CheckIfNoRewardType(rewardType rewardsPb.RewardType) bool {
	return rewardType == rewardsPb.RewardType_NO_REWARD
}

// CheckIfAnyOptionOfRewardType checks if any of the reward options has the input reward type
func CheckIfAnyOptionOfRewardType(rewardOptions []*rewardsPb.RewardOption, rewardType rewardsPb.RewardType) bool {
	for _, option := range rewardOptions {
		if option.GetRewardType() == rewardType {
			return true
		}
	}
	return false
}
