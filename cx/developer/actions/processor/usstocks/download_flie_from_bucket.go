// nolint:dupl
package usstocks

import (
	"context"
	"encoding/json"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	dsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/usstocks/operations"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
)

type USSGetDocumentFromBucket struct {
	operationsClient operations.OperationsClient
}

func NewUSSGetDocumentFromBucket(operationsClient operations.OperationsClient) *USSGetDocumentFromBucket {
	return &USSGetDocumentFromBucket{operationsClient: operationsClient}
}

func (c *USSGetDocumentFromBucket) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*dsPb.ParameterMeta, error) {
	paramList := []*dsPb.ParameterMeta{
		{
			Name:            "s3-paths",
			Label:           "Comma separated s3 paths",
			Type:            dsPb.ParameterDataType_STRING,
			ParameterOption: dsPb.ParameterOption_MANDATORY,
		},
	}
	return paramList, nil
}

func (c *USSGetDocumentFromBucket) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*dsPb.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var s3PathsCommaSeperated string
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case "s3-paths":
			s3PathsCommaSeperated = filter.GetStringValue()
		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}
	var marshalledRes []byte
	s3Paths := strings.Split(s3PathsCommaSeperated, ",")
	// mark step stale
	resp, err := c.operationsClient.GetFileFromBucket(ctx, &operations.GetFileFromBucketRequest{
		S3Paths: s3Paths,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error in calling GetFileFromBucket", zap.Error(te))
		marshalledRes = []byte(te.Error())
	} else {
		marshalledRes, err = json.Marshal(resp)
		if err != nil {
			logger.Error(ctx, "error marshalling GetFileFromBucket response", zap.Error(err))
			marshalledRes = []byte(err.Error())
		}
	}
	return string(marshalledRes), nil
}
