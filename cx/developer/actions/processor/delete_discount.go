//nolint:all
package processor

import (
	"context"
	"errors"

	discountsPb "github.com/epifi/gamma/api/casper/discounts"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"

	actionPb "github.com/epifi/gamma/api/cx/developer/actions"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/be-common/pkg/logger"
)

const discountId = "discount_id"

type DeleteDiscount struct {
	discountClient discountsPb.DiscountServiceClient
}

func NewDeleteDiscount(discountClient discountsPb.DiscountServiceClient) *DeleteDiscount {
	return &DeleteDiscount{discountClient: discountClient}
}

func (c *DeleteDiscount) FetchParamList(ctx context.Context, action actionPb.DeveloperActions, devActionMeta *actionPb.DevActionMeta) ([]*db_state.ParameterMeta, error) {

	return []*db_state.ParameterMeta{
		{
			Name:            discountId,
			Label:           "Discount ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}, nil
}

func (c *DeleteDiscount) ExecuteAction(ctx context.Context, action actionPb.DeveloperActions, filters []*db_state.Filter, devActionMeta *actionPb.DevActionMeta) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	deleteDiscountRequest := &discountsPb.DeleteDiscountRequest{}
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case discountId:
			deleteDiscountRequest.Id = filter.GetStringValue()

		default:
			logger.Error(ctx, "invalid filter", zap.String("filter", filter.String()))
			return "", status.Error(codes.InvalidArgument, "invalid filter")
		}
	}

	// delete offer listing
	resp, err := c.discountClient.DeleteDiscount(ctx, deleteDiscountRequest)
	if err != nil {
		// if grpc error occurred return the error
		return "", err
	}

	// returned marshalled response
	marshalledRes, err := protojson.Marshal(resp)
	if err != nil {
		logger.Error(ctx, "error marshalling DeleteDiscount response", zap.Error(err))
		return "", nil
	}
	return string(marshalledRes), nil
}
