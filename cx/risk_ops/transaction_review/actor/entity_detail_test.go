package actor

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
)

func TestEntityDetailManagerImpl_GetEntityDetailMap(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	mockActorClient := mocks.NewMockActorClient(ctrl)
	var (
		actorId		= "actorId"
		actorIds	= []string{actorId}
		req		= &actor.GetEntityDetailsRequest{ActorIds: actorIds}
		actorEntity	= &actor.GetEntityDetailsResponse_EntityDetail{
			ActorId: actorId,
		}
		someRandomError	= errors.New("some random error")
	)
	type args struct {
		actorIds []string
	}
	type mockActorClientField struct {
		enable	bool
		req	*actor.GetEntityDetailsRequest
		want	*actor.GetEntityDetailsResponse
		err	error
	}
	tests := []struct {
		name			string
		args			args
		mockActorClientField	mockActorClientField
		want			map[string]*actor.GetEntityDetailsResponse_EntityDetail
		wantErr			bool
		assertErr		func(err error) bool
	}{
		{
			name:		"fails with invalid argument for blank actor id",
			args:		args{},
			want:		nil,
			wantErr:	true,
			assertErr: func(err error) bool {
				return errors.Is(err, epifierrors.ErrInvalidArgument)
			},
		},
		{
			name:	"fails when actor service fails",
			args: args{
				actorIds: actorIds,
			},
			mockActorClientField: mockActorClientField{
				enable:	true,
				req:	req,
				want:	nil,
				err:	someRandomError,
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"success",
			args: args{
				actorIds: actorIds,
			},
			mockActorClientField: mockActorClientField{
				enable:	true,
				req:	req,
				want: &actor.GetEntityDetailsResponse{
					Status:		rpc.StatusOk(),
					EntityDetails:	[]*actor.GetEntityDetailsResponse_EntityDetail{actorEntity},
				},
				err:	nil,
			},
			want: map[string]*actor.GetEntityDetailsResponse_EntityDetail{
				actorId: actorEntity,
			},
			wantErr:	false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			e := NewEntityDetailManagerImpl(mockActorClient)
			if tt.mockActorClientField.enable {
				mockActorClient.EXPECT().GetEntityDetails(ctx,
					tt.mockActorClientField.req).Return(tt.mockActorClientField.want, tt.mockActorClientField.err)
			}
			got, err := e.GetEntityDetailMap(ctx, tt.args.actorIds)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEntityDetailMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.wantErr && tt.assertErr != nil && !tt.assertErr(err) {
				t.Errorf("FetchLatestStatus() error = %v assertion failed", err)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetEntityDetailMap() got = %v, want %v", got, tt.want)
			}
		})
	}
}
