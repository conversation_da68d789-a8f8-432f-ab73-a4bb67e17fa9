package consumer

import (
	"context"
	"errors"
	"testing"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	watsonInternal "github.com/epifi/gamma/cx/internal/watson"
	mock_dao "github.com/epifi/gamma/cx/test/mocks/watson/dao"
	ticketEvent "github.com/epifi/gamma/cx/watson/consumer/ticket_event"

	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/golang/mock/gomock"
	"github.com/google/uuid"

	"github.com/epifi/be-common/api/rpc"
	watsonPb "github.com/epifi/gamma/api/cx/watson"
	watsonPbConsumer "github.com/epifi/gamma/api/cx/watson/consumer"
	types "github.com/epifi/gamma/api/typesv2"
)

const (
	validIncidentCategoryId = "PRODUCT_CATEGORY_TRANSACTIONS::PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP::SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT"
	incidentId2             = "incident-id-2"
	validIssueCategoryId    = "8a2d37b2-c4ab-5449-967a-87a4dd698404"
	incidentCategoryId1     = "PRODUCT_CATEGORY_TRANSACTIONS:PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT:SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED"
)

var (
	actorId1         = "actorId-1"
	actorId2         = "actorId-2"
	clientRequestId  = uuid.NewString()
	mockTransientErr = watsonInternal.ErrUnhandled
	incident1        = &watsonPb.Incident{
		ActorId:            actorId1,
		ClientRequestId:    clientRequestId,
		IncidentCategoryId: validIncidentCategoryId,
		IncidentData:       &watsonPb.IncidentData{},
		Client:             types.ServiceName_USER_SERVICE,
	}
	incident2 = &watsonPb.Incident{
		ActorId:            actorId2,
		ClientRequestId:    clientRequestId,
		IncidentCategoryId: validIncidentCategoryId,
		IncidentData:       &watsonPb.IncidentData{},
		Client:             types.ServiceName_USER_SERVICE,
		IncidentState:      watsonPb.IncidentState_INCIDENT_STATE_DROPPED,
	}
	incident3 = &watsonPb.Incident{
		IncidentCategoryId: validIssueCategoryId,
		Client:             types.ServiceName_ORDER_SERVICE,
		ActorId:            actorId2,
		ClientRequestId:    clientRequestId,
	}
	oldTicket1          = &ticketPb.Ticket{Id: 1, Status: ticketPb.Status_STATUS_OPEN}
	newTicket1          = &ticketPb.Ticket{Id: 1, Status: ticketPb.Status_STATUS_WAITING_ON_PRODUCT}
	ticketEventPayload1 = &watsonPb.TicketEventPayload{
		Payload: &watsonPb.TicketEventPayload_UpdateEventPayload{
			UpdateEventPayload: &watsonPb.TicketUpdateEventPayload{
				NewTicket: newTicket1,
				OldTicket: oldTicket1,
			},
		},
	}
)

func TestWatsonConsumer_ProcessReportIncidentEvent(t *testing.T) {
	t.Parallel()
	watsonConsumer, mockDeps, assertTest := newWatsonConsumerWithMocks(t)
	defer assertTest()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *watsonPbConsumer.ProcessReportIncidentEventRequest
	}
	tests := []struct {
		name string
		args args
		want *watsonPbConsumer.ProcessReportIncidentEventResponse
	}{
		{
			name: "error in fetching or creating incident in db",
			args: args{
				mocks: []interface{}{
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident1).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident1).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockConsumerHelper.EXPECT().GetOrCreateIncident(gomock.Any(), incident1).Return(nil, mockTransientErr),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessReportIncidentEventRequest{
					Incident: incident1,
				},
			},
			want: reportIncidentTransErr(rpc.StatusInternal()),
		},
		{
			name: "transient error while validating incident",
			args: args{
				mocks: []interface{}{
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident1).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident1).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockConsumerHelper.EXPECT().GetOrCreateIncident(gomock.Any(), incident1).Return(incident1, nil),
					mockDeps.mockConsumerHelper.EXPECT().IsIncidentValidToInitiateWorkflow(gomock.Any(), incident1).
						Return(false, watsonInternal.ErrFailedToCallClient),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessReportIncidentEventRequest{
					Incident: incident1,
				},
			},
			want: reportIncidentTransErr(rpc.StatusInternal()),
		},
		{
			name: "error: incident update failed while updating incident state to dropped",
			args: args{
				mocks: []interface{}{
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident1).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident1).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockConsumerHelper.EXPECT().GetOrCreateIncident(gomock.Any(), incident1).Return(incident1, nil),
					mockDeps.mockConsumerHelper.EXPECT().IsIncidentValidToInitiateWorkflow(gomock.Any(), incident1).
						Return(false, watsonInternal.ErrConfigNotFound),
					mockDeps.mockWatsonHelper.EXPECT().UpdateIncidentState(gomock.Any(), incident1.GetId(), watsonPb.IncidentState_INCIDENT_STATE_DROPPED).Return(mockTransientErr),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessReportIncidentEventRequest{
					Incident: incident1,
				},
			},
			want: reportIncidentTransErr(rpc.StatusInternal()),
		},
		{
			name: "error: dropped incident",
			args: args{
				mocks: []interface{}{
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident1).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident1).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockConsumerHelper.EXPECT().GetOrCreateIncident(gomock.Any(), incident1).Return(incident1, nil),
					mockDeps.mockConsumerHelper.EXPECT().IsIncidentValidToInitiateWorkflow(gomock.Any(), incident1).
						Return(false, watsonInternal.ErrClientNotRegistered),
					mockDeps.mockWatsonHelper.EXPECT().UpdateIncidentState(gomock.Any(), incident1.GetId(), watsonPb.IncidentState_INCIDENT_STATE_DROPPED).Return(nil),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessReportIncidentEventRequest{
					Incident: incident1,
				},
			},
			want: reportIncidentPermanentErr(rpc.StatusUnimplemented()),
		},
		{
			name: "error: cannot update incident state for invalid incident",
			args: args{
				mocks: []interface{}{
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident1).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident1).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockConsumerHelper.EXPECT().GetOrCreateIncident(gomock.Any(), incident1).Return(incident1, nil),
					mockDeps.mockConsumerHelper.EXPECT().IsIncidentValidToInitiateWorkflow(gomock.Any(), incident1).
						Return(false, nil),
					mockDeps.mockWatsonHelper.EXPECT().UpdateIncidentState(gomock.Any(), incident1.GetId(),
						watsonPb.IncidentState_INCIDENT_STATE_INVALID).Return(mockTransientErr),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessReportIncidentEventRequest{
					Incident: incident1,
				},
			},
			want: reportIncidentTransErr(rpc.StatusInternal()),
		},
		{
			name: "invalid incident",
			args: args{
				mocks: []interface{}{
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident1).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident1).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockConsumerHelper.EXPECT().GetOrCreateIncident(gomock.Any(), incident1).Return(incident1, nil),
					mockDeps.mockConsumerHelper.EXPECT().IsIncidentValidToInitiateWorkflow(gomock.Any(), incident1).
						Return(false, nil),
					mockDeps.mockWatsonHelper.EXPECT().UpdateIncidentState(gomock.Any(), incident1.GetId(),
						watsonPb.IncidentState_INCIDENT_STATE_INVALID).Return(nil),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessReportIncidentEventRequest{
					Incident: incident1,
				},
			},
			want: reportIncidentSuccessResp(),
		},
		{
			name: "workflow already exists for given incident",
			args: args{
				mocks: []interface{}{
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident1).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident1).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockConsumerHelper.EXPECT().GetOrCreateIncident(gomock.Any(), incident1).Return(incident1, nil),
					mockDeps.mockConsumerHelper.EXPECT().IsIncidentValidToInitiateWorkflow(gomock.Any(), incident1).
						Return(true, nil),
					mockDeps.mockConsumerHelper.EXPECT().InitiateWatsonIncidentLifecycleWorkflow(gomock.Any(), incident1).
						Return(epifierrors.ErrAlreadyExists),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessReportIncidentEventRequest{
					Incident: incident1,
				},
			},
			want: reportIncidentPermanentErr(rpc.StatusAlreadyExists()),
		},
		{
			name: "workflow successfully initiated",
			args: args{
				mocks: []interface{}{
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident1).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident1).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockConsumerHelper.EXPECT().GetOrCreateIncident(gomock.Any(), incident1).Return(incident1, nil),
					mockDeps.mockConsumerHelper.EXPECT().IsIncidentValidToInitiateWorkflow(gomock.Any(), incident1).
						Return(true, nil),
					mockDeps.mockConsumerHelper.EXPECT().InitiateWatsonIncidentLifecycleWorkflow(gomock.Any(), incident1).
						Return(nil),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessReportIncidentEventRequest{
					Incident: incident1,
				},
			},
			want: reportIncidentSuccessResp(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _ := watsonConsumer.ProcessReportIncidentEvent(tt.args.ctx, tt.args.req)
			if got.String() != tt.want.String() {
				t.Errorf("ProcessReportIncidentEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWatsonConsumer_ProcessResolveIncidentEvent(t *testing.T) {
	t.Parallel()
	watsonConsumer, mockDeps, assertTest := newWatsonConsumerWithMocks(t)
	defer assertTest()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *watsonPbConsumer.ProcessResolveIncidentEventRequest
	}
	tests := []struct {
		name string
		args args
		want *watsonPbConsumer.ProcessResolveIncidentEventResponse
	}{
		{
			name: "error - incident doesn't exist - permanent error",
			args: args{
				mocks: []interface{}{
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident2).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockWatsonHelper.EXPECT().GetIncidentFromDB(
						epificontext.CtxWithActorId(context.Background(), actorId2),
						incident2.GetActorId(), incident2.GetClientRequestId(),
						incident2.GetIncidentCategoryId(), incident2.GetClient()).
						Return(nil, epifierrors.ErrRecordNotFound),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessResolveIncidentEventRequest{
					Incident: incident2,
				},
			},
			want: resolveIncidentPermanentErr(rpc.StatusRecordNotFound()),
		},
		{
			name: "error - db error - transient error",
			args: args{
				mocks: []interface{}{
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident2).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockWatsonHelper.EXPECT().GetIncidentFromDB(
						epificontext.CtxWithActorId(context.Background(), actorId2),
						incident2.GetActorId(), incident2.GetClientRequestId(),
						incident2.GetIncidentCategoryId(), incident2.GetClient()).
						Return(nil, mockTransientErr),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessResolveIncidentEventRequest{
					Incident: incident2,
				},
			},
			want: resolveIncidentTransErr(rpc.StatusInternal()),
		},
		{
			name: "error - db error - transient error",
			args: args{
				mocks: []interface{}{
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident2).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockWatsonHelper.EXPECT().GetIncidentFromDB(
						epificontext.CtxWithActorId(context.Background(), actorId2),
						incident2.GetActorId(), incident2.GetClientRequestId(),
						incident2.GetIncidentCategoryId(), incident2.GetClient()).
						Return(nil, mockTransientErr),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessResolveIncidentEventRequest{
					Incident: incident2,
				},
			},
			want: resolveIncidentTransErr(rpc.StatusInternal()),
		},
		{
			name: "permanent error dropped incident",
			args: args{
				mocks: []interface{}{
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident2).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockWatsonHelper.EXPECT().GetIncidentFromDB(
						epificontext.CtxWithActorId(context.Background(), actorId2),
						incident2.GetActorId(), incident2.GetClientRequestId(),
						incident2.GetIncidentCategoryId(), incident2.GetClient()).
						Return(incident2, nil),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessResolveIncidentEventRequest{
					Incident: incident2,
				},
			},
			want: resolveIncidentPermanentErr(rpc.StatusInvalidArgument()),
		},
		{
			name: "error: not found even with older incident category",
			args: args{
				mocks: []interface{}{
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident3).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockWatsonHelper.EXPECT().GetIncidentFromDB(
						epificontext.CtxWithActorId(context.Background(), incident3.GetActorId()),
						incident3.GetActorId(), incident3.GetClientRequestId(),
						incident3.GetIncidentCategoryId(), incident3.GetClient()).
						Return(nil, epifierrors.ErrRecordNotFound),
					mockDeps.mockWatsonHelper.EXPECT().GetIncidentFromDB(
						epificontext.CtxWithActorId(context.Background(), incident3.GetActorId()),
						incident3.GetActorId(), incident3.GetClientRequestId(),
						incidentCategoryId1, incident3.GetClient()).
						Return(nil, epifierrors.ErrRecordNotFound),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessResolveIncidentEventRequest{
					Incident: incident3,
				},
			},
			want: resolveIncidentPermanentErr(rpc.StatusRecordNotFound()),
		},
		{
			name: "error while sending resolution signal to workflow",
			args: args{
				mocks: []interface{}{
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident1).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockWatsonHelper.EXPECT().GetIncidentFromDB(
						epificontext.CtxWithActorId(context.Background(), incident1.GetActorId()),
						incident1.GetActorId(), incident2.GetClientRequestId(),
						incident2.GetIncidentCategoryId(), incident1.GetClient()).
						Return(incident1, nil),
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident1).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockConsumerHelper.EXPECT().SignalIncidentResolutionToWorkflow(gomock.Any(), incident1).Return(mockTransientErr),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessResolveIncidentEventRequest{
					Incident: incident1,
				},
			},
			want: resolveIncidentTransErr(rpc.StatusInternal()),
		},
		{
			name: "successfully send resolution signal to workflow",
			args: args{
				mocks: []interface{}{
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident1).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockWatsonHelper.EXPECT().GetIncidentFromDB(
						epificontext.CtxWithActorId(context.Background(), incident1.GetActorId()),
						incident1.GetActorId(), incident2.GetClientRequestId(),
						incident2.GetIncidentCategoryId(), incident2.GetClient()).
						Return(incident1, nil),
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident1).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockConsumerHelper.EXPECT().SignalIncidentResolutionToWorkflow(gomock.Any(), incident1).Return(nil),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessResolveIncidentEventRequest{
					Incident: incident1,
				},
			},
			want: resolveIncidentSuccessResp(),
		},
		{
			name: "success: incident created via enum resolved via id",
			args: args{
				mocks: []interface{}{
					mockDeps.mockHelperFactory.EXPECT().GetWatsonHelper(incident3).Return(mockDeps.mockWatsonHelper),
					mockDeps.mockWatsonHelper.EXPECT().GetIncidentFromDB(
						epificontext.CtxWithActorId(context.Background(), incident3.GetActorId()),
						incident3.GetActorId(), incident3.GetClientRequestId(),
						incident3.GetIncidentCategoryId(), incident3.GetClient()).
						Return(nil, epifierrors.ErrRecordNotFound),
					mockDeps.mockWatsonHelper.EXPECT().GetIncidentFromDB(
						epificontext.CtxWithActorId(context.Background(), incident3.GetActorId()),
						incident3.GetActorId(), incident3.GetClientRequestId(),
						incidentCategoryId1, incident3.GetClient()).
						Return(incident3, nil),
					mockDeps.mockConsumerHelperFactory.EXPECT().GetConsumerHelper(incident3).Return(mockDeps.mockConsumerHelper),
					mockDeps.mockConsumerHelper.EXPECT().SignalIncidentResolutionToWorkflow(gomock.Any(), incident3).Return(nil),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessResolveIncidentEventRequest{
					Incident: incident3,
				},
			},
			want: resolveIncidentSuccessResp(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _ := watsonConsumer.ProcessResolveIncidentEvent(tt.args.ctx, tt.args.req)
			if got.String() != tt.want.String() {
				t.Errorf("ProcessResolveIncidentEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWatsonConsumer_ProcessTicketEventForWatson(t *testing.T) {
	t.Parallel()
	watsonConsumer, mockDeps, assertTest := newWatsonConsumerWithMocks(t)
	defer assertTest()

	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *watsonPbConsumer.ProcessTicketEventForWatsonRequest
	}
	tests := []struct {
		name string
		args args
		want *watsonPbConsumer.ProcessTicketEventForWatsonResponse
	}{
		{
			name: "failed to get ticket event processor",
			args: args{
				mocks: []interface{}{
					mockDeps.mockTicketEventProcessorFactory.EXPECT().GetTicketEventProcessor(watsonPb.TicketEventType_TICKET_EVENT_TYPE_STATUS_CHANGE).
						Return(mockDeps.mockTicketEventProcessor, errors.New("mock err")),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessTicketEventForWatsonRequest{
					TicketEventType:    watsonPb.TicketEventType_TICKET_EVENT_TYPE_STATUS_CHANGE,
					TicketEventPayload: ticketEventPayload1,
				},
			},
			want: ticketEvent.TicketEventTransErr(rpc.StatusInternal()),
		},
		{
			name: "success",
			args: args{
				mocks: []interface{}{
					mockDeps.mockTicketEventProcessorFactory.EXPECT().GetTicketEventProcessor(watsonPb.TicketEventType_TICKET_EVENT_TYPE_STATUS_CHANGE).
						Return(mockDeps.mockTicketEventProcessor, nil),
					mockDeps.mockTicketEventProcessor.EXPECT().ProcessTicketEvent(context.Background(), &watsonPbConsumer.ProcessTicketEventForWatsonRequest{
						TicketEventType:    watsonPb.TicketEventType_TICKET_EVENT_TYPE_STATUS_CHANGE,
						TicketEventPayload: ticketEventPayload1,
					}).Return(ticketEvent.TicketEventSuccessResp(), nil),
				},
				ctx: context.Background(),
				req: &watsonPbConsumer.ProcessTicketEventForWatsonRequest{
					TicketEventType:    watsonPb.TicketEventType_TICKET_EVENT_TYPE_STATUS_CHANGE,
					TicketEventPayload: ticketEventPayload1,
				},
			},
			want: ticketEvent.TicketEventSuccessResp(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, _ := watsonConsumer.ProcessTicketEventForWatson(tt.args.ctx, tt.args.req)
			if got.String() != tt.want.String() {
				t.Errorf("ProcessTicketEventForWatson() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWatsonConsumer_ProcessCreateTicketEvent(t *testing.T) {
	t.Parallel()
	var (
		req1 = &ticketPb.CreateTicketEvent{
			ClientRequestInfo: &ticketPb.ClientRequestInfo{
				Client: ticketPb.Client_CLIENT_WATSON,
				Id:     incidentId2,
			},
			Ticket: &ticketPb.Ticket{Id: 5},
		}
	)

	type args struct {
		mocks func(i *mock_dao.MockIIncidentDao, t *mock_dao.MockIIncidentTicketDetailDao)
		req   *ticketPb.CreateTicketEvent
	}
	tests := []struct {
		name string
		args args
		want *watsonPbConsumer.ProcessCreateTicketEventResponse
	}{
		{
			name: "invalid request - ticket id missing",
			args: args{
				req:   &ticketPb.CreateTicketEvent{},
				mocks: func(i *mock_dao.MockIIncidentDao, t *mock_dao.MockIIncidentTicketDetailDao) {},
			},
			want: processTicketEventPermanentErr(rpc.StatusInvalidArgument()),
		},
		{
			name: "invalid request - incident not found",
			args: args{
				req: req1,
				mocks: func(i *mock_dao.MockIIncidentDao, t *mock_dao.MockIIncidentTicketDetailDao) {
					i.EXPECT().Get(gomock.Any(), req1.GetClientRequestInfo().GetId()).Return(nil, epifierrors.ErrRecordNotFound)
				},
			},
			want: processTicketEventPermanentErr(rpc.StatusInvalidArgument()),
		},
		{
			name: "error failed to check whether incident exists",
			args: args{
				req: req1,
				mocks: func(i *mock_dao.MockIIncidentDao, t *mock_dao.MockIIncidentTicketDetailDao) {
					i.EXPECT().Get(gomock.Any(), req1.GetClientRequestInfo().GetId()).Return(nil, mockTransientErr)
				},
			},
			want: processTicketEventTransientErr(rpc.StatusInternal()),
		},
		{
			name: "failed to create incident ticket details record",
			args: args{
				req: req1,
				mocks: func(i *mock_dao.MockIIncidentDao, t *mock_dao.MockIIncidentTicketDetailDao) {
					i.EXPECT().Get(gomock.Any(), req1.GetClientRequestInfo().GetId()).Return(incident1, nil)
					t.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, mockTransientErr)
				},
			},
			want: processTicketEventTransientErr(rpc.StatusInternal()),
		},
		{
			name: "success: record already exists",
			args: args{
				req: req1,
				mocks: func(i *mock_dao.MockIIncidentDao, t *mock_dao.MockIIncidentTicketDetailDao) {
					i.EXPECT().Get(gomock.Any(), req1.GetClientRequestInfo().GetId()).Return(incident1, nil)
					t.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrAlreadyExists)
				},
			},
			want: processTicketEventSuccessResp(rpc.StatusAlreadyExists()),
		},
		{
			name: "success: incident ticket details record created",
			args: args{
				req: req1,
				mocks: func(i *mock_dao.MockIIncidentDao, t *mock_dao.MockIIncidentTicketDetailDao) {
					i.EXPECT().Get(gomock.Any(), req1.GetClientRequestInfo().GetId()).Return(incident1, nil)
					t.EXPECT().Create(gomock.Any(), gomock.Any()).Return(&watsonPb.IncidentTicketDetail{}, nil)
				},
			},
			want: processTicketEventSuccessResp(rpc.StatusOk()),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			watsonConsumer, mockDeps, assertTest := newWatsonConsumerWithMocks(t)
			defer assertTest()
			tt.args.mocks(mockDeps.mockIncidentDao, mockDeps.mockIncidentTicketDao)
			got, _ := watsonConsumer.ProcessCreateTicketEvent(context.Background(), tt.args.req)
			if got.String() != tt.want.String() {
				t.Errorf("ProcessTicketEventForWatson() got = %v, want %v", got, tt.want)
			}
		})
	}
}
