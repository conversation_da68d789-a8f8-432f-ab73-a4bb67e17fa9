package processor

import (
	"context"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/fcm"
	types "github.com/epifi/gamma/api/typesv2"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/helper"
	"github.com/epifi/gamma/cx/metrics"
	cxTypes "github.com/epifi/gamma/cx/wire/types"
	"github.com/epifi/gamma/pkg/frontend/cx"

	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	callPb "github.com/epifi/gamma/api/cx/call"
	ozonetelConsumerPb "github.com/epifi/gamma/api/cx/call/consumer"
	consumerPb "github.com/epifi/gamma/api/cx/consumer"
	"github.com/epifi/gamma/cx/call/consumer/common"
	callDao "github.com/epifi/gamma/cx/call/dao"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

type CallEndedProcessor struct {
	callDetailsDao     callDao.ICallDetailsDao
	updateTicketPub    cxTypes.UpdateTicketPublisher
	customerIdentifier helper.ICustomerIdentifier
	commsClient        commsPb.CommsClient
	callGenConf        *cxGenConf.CallConfig
}

func NewCallEndedProcessor(callDetailsDao callDao.ICallDetailsDao, updateTicketPub cxTypes.UpdateTicketPublisher,
	customerIdentifier helper.ICustomerIdentifier, commsClient commsPb.CommsClient, callGenConf *cxGenConf.CallConfig) *CallEndedProcessor {
	return &CallEndedProcessor{
		callDetailsDao:     callDetailsDao,
		updateTicketPub:    updateTicketPub,
		customerIdentifier: customerIdentifier,
		commsClient:        commsClient,
		callGenConf:        callGenConf,
	}
}

var _ ICallStageProcessor = &CallEndedProcessor{}

func (p *CallEndedProcessor) ProcessCallDetails(ctx context.Context, callDetails *callPb.CallDetails) (*ozonetelConsumerPb.AddOrUpdateOzonetelCallDetailsResponse, error) {
	// identify actor for given caller
	// this step is done at best effort basis
	// any errors which are encountered are not blocking or transient error
	// this is done because:
	// given caller might have never opened Fi account so there will be no actor id present
	// there might be some performance issue from actor service which can cause errors in the flow
	// updating end of the call details and updating ticket is more critical operation than identifying actor
	// returning transient error will only end up delaying db and ticket update for other call details like disposition, recording link, etc
	// if actor is identified then we populate actor id in call details
	// if error is encountered we log the error and process on next steps
	actor, actorErr := p.customerIdentifier.GetActor(ctx, "", callDetails.GetCallerId())
	if actorErr != nil {
		cxLogger.Error(ctx, "error while identifying actor in end of the call stage consumer", zap.Error(actorErr), zap.Any(logger.OZONETEL_MONITOR_UCID, callDetails.GetMonitorUcid()))
	}
	callDetails.ActorId = actor.GetId()

	// update record in db
	// if err encountered other than record not found than retry
	updateErr := p.callDetailsDao.UpdateByMonitorUCID(ctx, callDetails.GetMonitorUcid(), callDetails, getUpdateMaskForCallEndStage())
	if updateErr != nil && !errors.Is(updateErr, epifierrors.ErrRecordNotFound) {
		cxLogger.Error(ctx, "error while updating call record in db", zap.Error(updateErr), zap.Any(logger.OZONETEL_MONITOR_UCID, callDetails.GetMonitorUcid()),
			zap.Any("callStage", callDetails.GetCallStage()))
		// retry if update fails because of any other reasons other than record not found
		return common.CallTransErr(), nil
	}

	// if previous callbacks (initiated / agent assigned) are not received than store this callback in db
	// if any error than return transient and retry
	if updateErr != nil && errors.Is(updateErr, epifierrors.ErrRecordNotFound) {
		_, createErr := p.callDetailsDao.Create(ctx, callDetails)
		if createErr != nil {
			cxLogger.Error(ctx, "error while creating new record in call ended processor", zap.Error(createErr),
				zap.Any(logger.OZONETEL_MONITOR_UCID, callDetails.GetMonitorUcid()))
			return common.CallTransErr(), nil
		}
	}

	// get updated record from db
	updatedDetails, updatedErr := p.callDetailsDao.GetByMonitorUCID(ctx, callDetails.GetMonitorUcid())
	if updatedErr != nil {
		cxLogger.Error(ctx, "error while fetching call record from db", zap.Error(updatedErr), zap.Any(logger.OZONETEL_MONITOR_UCID, callDetails.GetMonitorUcid()))
		return common.CallTransErr(), nil
	}

	// if ticket id is present for that call record
	// publish call record to update ticket queue to update relevant ticket details from call record
	// description, status, etc is updated by ozonetel after end of each call
	if updatedDetails.GetFreshdeskTicketId() != 0 {

		updatedMsg := &consumerPb.UpdateTicketEventRequest{
			UpdateTicketPayload: &consumerPb.UpdateTicketPayload{
				UpdateTicketRequestType: consumerPb.UpdateTicketRequestType_UPDATE_TICKET_REQUEST_TYPE_OZONETEL_CALL,
				DetailsPayload: &consumerPb.UpdateTicketPayload_CallDetails{
					CallDetails: updatedDetails,
				},
			},
		}

		// if publish fails than retry
		_, pubErr := p.updateTicketPub.Publish(ctx, updatedMsg)
		if pubErr != nil {
			cxLogger.Error(ctx, "error while publishing call record msg to queue", zap.Error(pubErr), zap.Any(logger.OZONETEL_MONITOR_UCID, callDetails.GetMonitorUcid()))
			return common.CallTransErr(), nil
		}
	}
	if isCallAbandoned(ctx, callDetails) {
		p.handleAbandonedCall(ctx, callDetails, actor)
	}
	// push to custom metrics
	metrics.RecordCallType(callDetails.GetCallType().String())
	metrics.RecordCallHandleStatus(callDetails.GetCallHandleStatus().String())

	// return success if everything goes fine
	return common.CallSuccessResp(), nil
}

func isCallAbandoned(ctx context.Context, details *callPb.CallDetails) bool {
	if details.GetCallType() == callPb.CallType_CALL_TYPE_INBOUND &&
		details.GetCallHandleStatus() == callPb.CallHandleStatus_CALL_HANDLE_STATUS_NOT_ANSWERED {
		logger.Debug(ctx, "call is abandoned", zap.Any("callDetails", details))
	}
	// check if call was not picked by any agent
	return details.GetCallType() == callPb.CallType_CALL_TYPE_INBOUND &&
		details.GetCallHandleStatus() == callPb.CallHandleStatus_CALL_HANDLE_STATUS_NOT_ANSWERED
}

func (p *CallEndedProcessor) handleAbandonedCall(ctx context.Context, details *callPb.CallDetails, actor *types.Actor) {
	// we cannot send comms to unregistered users, for those cases we won't be able to find actor
	if actor == nil {
		metrics.RecordAbandonedCallCommsSent(metrics.AbandonedCallCommsNotSendForUnregisteredUser)
		return
	}

	if !p.callGenConf.AbandonedCallConfig().IsAbandonedCallCommsEnabled() {
		logger.Info(ctx, "comms for abandoned calls disabled", zap.Any(logger.OZONETEL_MONITOR_UCID, details.GetMonitorUcid()))
		return
	}
	// we will be sending comms on best effort basis, hence in case of failure we only log the error instead of returning it
	resp, err := p.commsClient.SendMessageBatch(ctx, &commsPb.SendMessageBatchRequest{
		Type:              commsPb.QoS_BEST_EFFORT,
		UserIdentifier:    &commsPb.SendMessageBatchRequest_UserId{UserId: actor.GetEntityId()},
		CommunicationList: p.getAbandonedCallComms(ctx),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "failed to send comms for abandoned call", zap.Error(te), zap.Any(logger.OZONETEL_MONITOR_UCID, details.GetMonitorUcid()))
		metrics.RecordAbandonedCallCommsSent(metrics.AbandonedCallCommsFailedToSent)
		return
	}
	metrics.RecordAbandonedCallCommsSent(metrics.AbandonedCallCommsSuccessfullySent)
}

func (p *CallEndedProcessor) getAbandonedCallComms(ctx context.Context) []*commsPb.Communication {
	return []*commsPb.Communication{
		{
			Medium: commsPb.Medium_NOTIFICATION,
			Message: &commsPb.Communication_Notification{
				Notification: &commsPb.NotificationMessage{
					Priority: commsPb.NotificationPriority_NORMAL,
					Notification: &fcm.Notification{
						NotificationType: fcm.NotificationType_SYSTEM_TRAY,
						NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
							SystemTrayTemplate: &fcm.SystemTrayTemplate{
								CommonTemplateFields: &fcm.CommonTemplateFields{
									Title:    p.callGenConf.AbandonedCallConfig().NotificationTemplate().Title(),
									Body:     p.callGenConf.AbandonedCallConfig().NotificationTemplate().Body(),
									Deeplink: cx.GetContactUsDeeplink(ctx),
								},
							},
						},
					},
				},
			},
		},
		// TODO: Add whatsapp comms once template is whitelisted
	}
}

// following fields are updated by push to url webhook
func getUpdateMaskForCallEndStage() []callPb.CallDetailsFieldMask {
	return []callPb.CallDetailsFieldMask{
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_RECORDING_LINK,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_DISPOSITION,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_FALLBACK_RULE,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_DIAL_STATUS,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_STAGE,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_START_TIME,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_END_TIME,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_DURATION,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CALL_STATUS,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_AGENT_STATUS,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_CUSTOMER_STATUS,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_HANG_UP_BY,
		callPb.CallDetailsFieldMask_CALL_DETAILS_FIELD_MASK_ACTOR_ID,
	}
}
