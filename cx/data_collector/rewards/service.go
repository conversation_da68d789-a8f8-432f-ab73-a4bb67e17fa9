package rewards

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"
	"time"

	"google.golang.org/protobuf/encoding/protojson"

	moneyPb "github.com/epifi/be-common/pkg/money"

	"github.com/samber/lo"

	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"

	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"

	"github.com/epifi/be-common/api/rpc"

	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/cx/data_collector/helper"

	"google.golang.org/genproto/googleapis/type/postaladdress"

	exchangerPb "github.com/epifi/gamma/api/casper/exchanger"

	cxLogger "github.com/epifi/gamma/cx/logger"

	"github.com/epifi/gamma/api/casper/redemption"

	rewardsPb "github.com/epifi/gamma/api/rewards"

	"github.com/epifi/gamma/api/cx"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	casperPb "github.com/epifi/gamma/api/casper"
	rePb "github.com/epifi/gamma/api/cx/data_collector/rewards"
	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/pkg/accrual"
)

type Service struct {
	authEngine                      auth_engine.IAuthEngine
	rewardOffersClient              rewardOfferPb.RewardOffersClient
	rewardClient                    rewardsPb.RewardsGeneratorClient
	offersClient                    casperPb.OfferListingServiceClient
	offerRedemptionClient           redemption.OfferRedemptionServiceClient
	exchangerOffersClient           exchangerPb.ExchangerOfferServiceClient
	userClient                      userPb.UsersClient
	rewardsProjectionClient         rewardsProjectionPb.ProjectorServiceClient
	externalVendorRedemptionsClient evrPb.ExternalVendorRedemptionServiceClient
}

func NewService(authEngine auth_engine.IAuthEngine, rewardOffersClient rewardOfferPb.RewardOffersClient,
	offersClient casperPb.OfferListingServiceClient, offerRedemptionClient redemption.OfferRedemptionServiceClient,
	rewardsClient rewardsPb.RewardsGeneratorClient, exchangerOffersClient exchangerPb.ExchangerOfferServiceClient,
	userClient userPb.UsersClient, rewardsProjectionClient rewardsProjectionPb.ProjectorServiceClient, externalVendorRedemptionsClient evrPb.ExternalVendorRedemptionServiceClient) *Service {
	return &Service{
		authEngine:                      authEngine,
		rewardOffersClient:              rewardOffersClient,
		offersClient:                    offersClient,
		offerRedemptionClient:           offerRedemptionClient,
		rewardClient:                    rewardsClient,
		exchangerOffersClient:           exchangerOffersClient,
		userClient:                      userClient,
		rewardsProjectionClient:         rewardsProjectionClient,
		externalVendorRedemptionsClient: externalVendorRedemptionsClient,
	}
}

var _ rePb.RewardsServer = &Service{}

func (s *Service) GetRewardOffers(ctx context.Context, req *rePb.GetRewardOffersRequest) (*rePb.GetRewardOffersResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &rePb.GetRewardOffersResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	var rewardOffers []*rewardOfferPb.RewardOffer

	if req.GetFetchOffersOnlyApplicableToUser() {
		rewardOffersOnlyApplicableToUser, err := s.getRewardOffersOnlyApplicableToUser(ctx, req.GetHeader().GetActor().GetId())
		if err != nil {
			cxLogger.Error(ctx, "error in GetRewardOffersOnlyApplicableToUser func", zap.Error(err))
			return &rePb.GetRewardOffersResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error in GetRewardOffersOnlyApplicableToUser func"),
			}, nil
		}
		rewardOffers = rewardOffersOnlyApplicableToUser

	} else {
		var rewardsRequest *rewardOfferPb.GetRewardOffersRequest

		activeTillDateString := time.Now().Format(time.RFC3339)
		if req.GetDate() != nil {
			activeTillDateString = req.GetDate().AsTime().Format(time.RFC3339)
		}

		// active from is optional
		activeFromDateString := ""
		if req.GetFromTime() != nil {
			activeFromDateString = req.GetFromTime().AsTime().Format(time.RFC3339)
		}

		// call rewards service to fetch all active rewards if time window is nil.
		rewardsRequest = &rewardOfferPb.GetRewardOffersRequest{
			ActiveSince:    activeFromDateString,
			ActiveTill:     activeTillDateString,
			DisplaySince:   activeFromDateString,
			DisplayTill:    activeTillDateString,
			IsVisible:      true,
			Status:         rewardOfferPb.RewardOfferStatus_ACTIVE,
			GenerationType: rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
		}

		resp, err := s.rewardOffersClient.GetRewardOffers(ctx, rewardsRequest)
		if te := epifigrpc.RPCError(resp, err); te != nil {
			cxLogger.Error(ctx, "failed to fetch rewards", zap.Error(te))
			return &rePb.GetRewardOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch rewards")}, nil
		}
		rewardOffers = append(rewardOffers, resp.GetRewardOffers()...)

		rewardsRequest.IsVisible = false
		getRewardOffersRes, err := s.rewardOffersClient.GetRewardOffers(ctx, rewardsRequest)
		if rpcErr := epifigrpc.RPCError(getRewardOffersRes, err); rpcErr != nil {
			cxLogger.Error(ctx, "error in GetRewardOffers rpc", zap.Error(rpcErr))
			return &rePb.GetRewardOffersResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in GetRewardOffers rpc"),
			}, nil
		}
		nonVisibleRewardOffers := getRewardOffersRes.GetRewardOffers()
		nonVisibleRewardOffers = lo.Filter(nonVisibleRewardOffers, func(rewardOffer *rewardOfferPb.RewardOffer, _ int) bool {
			if rewardOffer.GetActionType() == "MANUAL_GIVEAWAY" {
				return false
			}
			return true
		})

		rewardOffers = append(rewardOffers, nonVisibleRewardOffers...)

	}
	return formatRewardsResponse(rewardOffers)
}

func (s *Service) getRewardOffersOnlyApplicableToUser(ctx context.Context, actorId string) ([]*rewardOfferPb.RewardOffer, error) {

	userDeviceProperties, err := s.userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId: actorId,
		PropertyTypes: []types.DeviceProperty{
			types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO,
		},
	})
	if e := epifigrpc.RPCError(userDeviceProperties, err); e != nil {
		cxLogger.Error(ctx, "error in GetUserDeviceProperties rpc", zap.Error(e))
		return nil, fmt.Errorf("error in GetUserDeviceProperties rpc, err: %w", e)
	}

	property := userDeviceProperties.UserDevicePropertyList[0]
	if property == nil || property.GetDeviceProperty() != types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO {
		logger.Error(ctx, "no device property with app version found")
		return nil, fmt.Errorf("no device property with app version found")
	}
	platform := property.PropertyValue.GetAppVersionInfo().GetPlatform()

	timeNowString := time.Now().Format(time.RFC3339)
	GetRewardOffersForActorRequest := &rewardOfferPb.GetRewardOffersForActorRequest{
		DisplaySince:      timeNowString,
		DisplayTill:       timeNowString,
		Status:            rewardOfferPb.RewardOfferStatus_ACTIVE,
		IsVisible:         true,
		ActorId:           actorId,
		SupportedPlatform: platform,
		GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
	}
	visibleRewardOffersForActor, err := s.rewardOffersClient.GetRewardOffersForActor(ctx, GetRewardOffersForActorRequest)
	if e := epifigrpc.RPCError(visibleRewardOffersForActor, err); e != nil {
		cxLogger.Error(ctx, "error in GetRewardOffersForActor rpc", zap.Error(e))
		return nil, fmt.Errorf("error in GetRewardOffersForActor rpc, err: %w", e)
	}
	var rewardOffers []*rewardOfferPb.RewardOffer
	for _, visibleRewardOffer := range visibleRewardOffersForActor.GetRewardOffers() {
		rewardOffers = append(rewardOffers, visibleRewardOffer)
	}

	GetRewardOffersForActorRequest = &rewardOfferPb.GetRewardOffersForActorRequest{
		DisplaySince:      timeNowString,
		DisplayTill:       timeNowString,
		Status:            rewardOfferPb.RewardOfferStatus_ACTIVE,
		IsVisible:         false,
		ActorId:           actorId,
		SupportedPlatform: platform,
		GenerationType:    rewardOfferPb.GenerationType_GENERATION_TYPE_REWARD,
	}
	notVisibleRewardOffersForActor, err := s.rewardOffersClient.GetRewardOffersForActor(ctx, GetRewardOffersForActorRequest)
	if e := epifigrpc.RPCError(notVisibleRewardOffersForActor, err); e != nil {
		cxLogger.Error(ctx, "error in GetRewardOffersForActor rpc", zap.Error(e))
		return nil, fmt.Errorf("error in GetRewardOffersForActor rpc, err: %w", e)
	}
	for _, notVisibleRewardOffer := range notVisibleRewardOffersForActor.GetRewardOffers() {
		rewardOffers = append(rewardOffers, notVisibleRewardOffer)
	}

	return rewardOffers, nil

}

func formatRewardsResponse(beRewardOffers []*rewardOfferPb.RewardOffer) (*rePb.GetRewardOffersResponse, error) {
	var rewards []*rePb.RewardOffer
	for _, r := range beRewardOffers {
		rewards = append(rewards, getCxRewardOfferFromBeRewardOffer(r))
	}

	return &rePb.GetRewardOffersResponse{Status: rpcPb.StatusOk(), RewardOffers: rewards}, nil
}
func (s *Service) GetOffers(ctx context.Context, req *rePb.GetOffersRequest) (*rePb.GetOffersResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &rePb.GetOffersResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	getOffersRequest := &casperPb.GetOffersByFiltersRequest{
		ActorId: actorId,
		OfferFilters: &casperPb.OfferFilters{
			FromTime:       req.GetFromTime(),
			TillTime:       req.GetDate(),
			CardOfferType:  req.GetCardOffersType(),
			RedemptionMode: casperPb.OfferRedemptionMode(req.GetRedemptionMode()),
		},
		PageContext: convertToOffersPageContext(req.GetPageContext()),
	}
	resp, err := s.offersClient.GetOffersByFilters(ctx, getOffersRequest)
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "failed to fetch offers", zap.Error(te))
		return &rePb.GetOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch offers")}, nil
	}
	return formatOffersResponse(resp)
}

func convertToOffersPageContext(pageContext *cx.PageContextRequest) *casperPb.PageContextRequest {
	if pageContext == nil {
		return nil
	}
	targetPageContext := &casperPb.PageContextRequest{}
	targetPageContext.PageSize = pageContext.GetPageSize()
	switch pageContext.GetToken().(type) {
	case *cx.PageContextRequest_AfterToken:
		targetPageContext.Token = &casperPb.PageContextRequest_AfterToken{
			AfterToken: pageContext.GetAfterToken(),
		}
	case *cx.PageContextRequest_BeforeToken:
		targetPageContext.Token = &casperPb.PageContextRequest_BeforeToken{
			BeforeToken: pageContext.GetBeforeToken(),
		}
	}
	return targetPageContext
}

func formatOffersResponse(response *casperPb.GetOffersByFiltersResponse) (*rePb.GetOffersResponse, error) {
	var offers []*rePb.Offer
	for _, o := range response.GetOffers() {
		offers = append(offers, &rePb.Offer{
			Id:             o.GetId(),
			Name:           o.GetName(),
			Desc:           o.GetDesc(),
			Price:          o.GetPrice(),
			RedemptionMode: rePb.OfferRedemptionMode(o.GetRedemptionMode()),
			TncList:        o.GetTnc().GetTncList(),
			OfferDetails:   o.GetAdditionalDetails().GetOfferDetails(),
			HowToRedeem:    o.GetAdditionalDetails().GetHowToRedeem(),
			ActiveSince:    getTimestampFromTimeString(response.GetOfferIdToListingMap()[o.GetId()].GetActiveSince()),
			ActiveTill:     getTimestampFromTimeString(response.GetOfferIdToListingMap()[o.GetId()].GetActiveTill()),
			DisplaySince:   getTimestampFromTimeString(response.GetOfferIdToListingMap()[o.GetId()].GetDisplaySince()),
			DisplayTill:    getTimestampFromTimeString(response.GetOfferIdToListingMap()[o.GetId()].GetDisplayTill()),
			AttachEntityMeta: &ticketPb.AttachEntityMeta{
				Meta: &ticketPb.AttachEntityMeta_OfferMeta{
					OfferMeta: &ticketPb.OfferMeta{
						OfferId:          o.GetId(),
						OfferName:        o.GetName(),
						OfferDescription: o.GetDesc(),
					},
				},
			},
		})
	}
	return &rePb.GetOffersResponse{Status: rpcPb.StatusOk(),
		Offers: offers,
		PageContext: &cx.PageContextResponse{
			BeforeToken: response.GetPageContext().GetBeforeToken(),
			AfterToken:  response.GetPageContext().GetAfterToken(),
			HasAfter:    response.GetPageContext().GetHasAfter(),
			HasBefore:   response.GetPageContext().GetHasAfter(),
		},
	}, nil
}

func getTimestampFromTimeString(timeStr string) *timestamppb.Timestamp {
	t, _ := time.Parse(time.RFC3339, timeStr)

	return timestamppb.New(t)
}

func (s *Service) GetUserRewards(ctx context.Context, req *rePb.GetUserRewardsRequest) (*rePb.GetUserRewardsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &rePb.GetUserRewardsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	resp, err := s.rewardClient.GetRewardsByActorId(ctx, &rewardsPb.RewardsByActorIdRequest{
		ActorId:     actorId,
		PageContext: convertToRewardsPageContext(req.GetPageContext()),
		Filter:      getRewardFiltersFromRequest(req),
	})

	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching rewards for user", zap.Error(err))
		return &rePb.GetUserRewardsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching rewards"),
		}, nil
	}

	return formatUserRewardsResponse(resp), nil
}

func formatUserRewardsResponse(resp *rewardsPb.RewardsResponse) *rePb.GetUserRewardsResponse {

	var rewardCxWrapper []*rePb.RewardCxWrapper
	for _, reward := range resp.GetRewards() {
		rewardCxWrapper = append(rewardCxWrapper, &rePb.RewardCxWrapper{
			Reward: reward,
			AttachEntityMeta: &ticketPb.AttachEntityMeta{
				Meta: &ticketPb.AttachEntityMeta_RewardMeta{
					RewardMeta: &ticketPb.RewardMeta{
						RewardId: reward.GetId(),
					},
				},
			},
		})
	}

	return &rePb.GetUserRewardsResponse{
		Status: rpcPb.StatusOk(),
		PageContext: &cx.PageContextResponse{
			BeforeToken: resp.GetPageContext().GetBeforeToken(),
			AfterToken:  resp.GetPageContext().GetAfterToken(),
			HasBefore:   resp.GetPageContext().GetHasBefore(),
			HasAfter:    resp.GetPageContext().GetHasAfter(),
		},
		Rewards:             resp.GetRewards(),
		RewardCxWrapperList: rewardCxWrapper,
	}
}

func getRewardFiltersFromRequest(req *rePb.GetUserRewardsRequest) *rewardsPb.RewardsByActorIdRequest_Filter {
	return &rewardsPb.RewardsByActorIdRequest_Filter{
		RewardId:       req.GetRewardId(),
		RewardType:     req.GetRewardType(),
		StartDate:      req.GetStartDate(),
		EndDate:        req.GetEndDate(),
		VisibilityType: req.GetVisibilityType(),
		RewardOfferId:  req.GetRewardOfferId(),
	}
}

func convertToRewardsPageContext(pageContext *cx.PageContextRequest) *rpcPb.PageContextRequest {
	if pageContext == nil {
		return nil
	}
	targetPageContext := &rpcPb.PageContextRequest{}
	targetPageContext.PageSize = pageContext.GetPageSize()
	switch pageContext.GetToken().(type) {
	case *cx.PageContextRequest_AfterToken:
		targetPageContext.Token = &rpcPb.PageContextRequest_AfterToken{
			AfterToken: pageContext.GetAfterToken(),
		}
	case *cx.PageContextRequest_BeforeToken:
		targetPageContext.Token = &rpcPb.PageContextRequest_BeforeToken{
			BeforeToken: pageContext.GetBeforeToken(),
		}
	}
	return targetPageContext
}

func (s *Service) GetRedeemedOffers(ctx context.Context, req *rePb.GetRedeemedOffersRequest) (*rePb.GetRedeemedOffersResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &rePb.GetRedeemedOffersResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	resp, err := s.offerRedemptionClient.GetRedeemedOffersForActor(ctx, &redemption.GetRedeemedOffersForActorRequest{
		ActorId:     actorId,
		PageContext: helper.ConvertToRpcPageContext(req.GetPageContext()),
		Filters: &redemption.GetRedeemedOffersForActorRequest_Filters{
			FromDate:        req.GetFromDate(),
			UptoDate:        req.GetUptoDate(),
			RedeemedOfferId: req.GetRedeemedOfferId(),
			OfferType:       req.GetOfferType(),
			Vendor:          req.GetVendor(),
			RedemptionState: req.GetRedemptionState(),
			ExpiryStatus:    redemption.GetRedeemedOffersForActorRequest_Filters_ExpiryStatus(req.GetExpiryStatus()),
		},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error while fetching offer details from offer redemption service", zap.Error(err))
		return &rePb.GetRedeemedOffersResponse{
			Status:           rpcPb.StatusInternalWithDebugMsg("error while fetching offer details from offer redemption service"),
			SherlockDeepLink: sherlockDeepLink,
		}, nil
	}
	return formatRedeemedOfferResponse(resp), nil
}

func formatRedeemedOfferResponse(resp *redemption.GetRedeemedOffersForActorResponse) *rePb.GetRedeemedOffersResponse {
	// masking customer's address in case of Physical Merchandise
	maskUserInfoInRedeemedOffers(resp.GetRedeemedOffers())

	var redeemedOfferCxWrapper []*rePb.RedeemedOfferCxWrapper
	for _, redeemedOffer := range resp.GetRedeemedOffers() {
		redeemedOfferCxWrapper = append(redeemedOfferCxWrapper, &rePb.RedeemedOfferCxWrapper{
			RedeemedOffer: redeemedOffer,
			AttachEntityMeta: &ticketPb.AttachEntityMeta{
				Meta: &ticketPb.AttachEntityMeta_RedeemedOfferMeta{
					RedeemedOfferMeta: &ticketPb.RedeemedOfferMeta{
						RedeemedOfferId: redeemedOffer.GetId(),
					},
				},
			},
		})
	}

	return &rePb.GetRedeemedOffersResponse{
		Status: rpcPb.StatusOk(),
		PageContext: &cx.PageContextResponse{
			HasBefore:   resp.GetPageContext().GetHasBefore(),
			HasAfter:    resp.GetPageContext().GetHasAfter(),
			BeforeToken: resp.GetPageContext().GetBeforeToken(),
			AfterToken:  resp.GetPageContext().GetAfterToken(),
		},
		RedeemedOffers:             resp.GetRedeemedOffers(),
		RedeemedOfferCxWrapperList: redeemedOfferCxWrapper,
	}
}

func maskUserInfoInRedeemedOffers(redeemedOffers []*redemption.RedeemedOffer) {
	for _, offer := range redeemedOffers {
		if physicalMerchandiseDetails := offer.GetRedeemedOfferDetails().GetPhysicalMerchandiseDetails(); physicalMerchandiseDetails != nil {
			shippingAddress := physicalMerchandiseDetails.GetShippingAddress()

			if len(shippingAddress.GetAddressLines()) > 0 {
				// fixing the number of characters to show in address line (max 5)
				maxCharactersToExtract := int32(math.Min(float64(len(shippingAddress.GetAddressLines()[0])), 5))

				// extracting only `maxCharactersToExtract` characters and appending "***" after that to show masking
				addressLines := []string{fmt.Sprintf("%s***", shippingAddress.GetAddressLines()[0][:maxCharactersToExtract])}
				maskedShippingAddress := &postaladdress.PostalAddress{
					PostalCode:   shippingAddress.GetPostalCode(),
					AddressLines: addressLines,
				}
				// replacing existing postal address
				physicalMerchandiseDetails.ShippingAddress = maskedShippingAddress
			}
		}
	}
}

func (s *Service) GetRewardOfferById(ctx context.Context, req *rePb.GetRewardOfferByIdRequest) (*rePb.GetRewardOfferByIdResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &rePb.GetRewardOfferByIdResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	if req.GetRewardOfferId() == "" {
		cxLogger.Info(ctx, "reward offer id not passed in request")
		return &rePb.GetRewardOfferByIdResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("reward offer id not passed in request"),
		}, nil
	}

	// fetch reward offer details from rewards service
	resp, err := s.rewardOffersClient.GetRewardOffersByIds(ctx, &rewardOfferPb.GetRewardOffersByIdsRequest{
		Ids: []string{req.GetRewardOfferId()},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "failed to fetch reward offer", zap.Error(te))
		return &rePb.GetRewardOfferByIdResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch rewards")}, nil
	}
	return toCXRewardOfferResponse(ctx, req.GetRewardOfferId(), resp)
}

func toCXRewardOfferResponse(ctx context.Context, offerId string, resp *rewardOfferPb.GetRewardOffersByIdsResponse) (*rePb.GetRewardOfferByIdResponse, error) {
	var rewardOffer *rewardOfferPb.RewardOffer
	for _, r := range resp.GetRewardOffers() {
		if r.GetId() == offerId {
			rewardOffer = r
		}
	}
	// return error if reward offer was not found in list
	if rewardOffer == nil {
		cxLogger.Error(ctx, "reward offer not found in response list", zap.String("rewardOfferId", offerId))
		return &rePb.GetRewardOfferByIdResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &rePb.GetRewardOfferByIdResponse{
		Status:      rpcPb.StatusOk(),
		RewardOffer: getCxRewardOfferFromBeRewardOffer(rewardOffer),
	}, nil
}

func getCxRewardOfferFromBeRewardOffer(rewardOffer *rewardOfferPb.RewardOffer) *rePb.RewardOffer {
	stepsV1 := &webui.LabelValue{
		Label: "Steps to Redeem",
		Value: rewardOffer.GetDisplayMeta().GetSteps(),
	}

	tncsV1 := &webui.LabelValue{
		Label: "Terms and Conditions",
		Value: rewardOffer.GetDisplayMeta().GetTncs(),
	}
	if !rewardOffer.GetRewardMeta().GetIsImplicitLockingDisabled() {
		tncsV1.Value = append(tncsV1.GetValue(), "reward will be locked if user's account is Min KYC or Fi-lite")
	}

	// If unlock meta is present then:
	// show steps to redeem, tncs and unlock steps if implicit locking is enabled else show only unlock steps
	var unlockSteps *webui.LabelValue
	if rewardOffer.GetUnlockMeta().GetCta() != nil {
		unlockSteps = &webui.LabelValue{
			Label: "Steps to unlock",
			Value: []string{
				rewardOffer.GetUnlockMeta().GetCta().GetName(),
				rewardOffer.GetUnlockMeta().GetCta().GetDesc(),
			},
		}
		if rewardOffer.GetRewardMeta().GetIsImplicitLockingDisabled() {
			stepsV1 = nil
			tncsV1 = nil
		}
	}

	return &rePb.RewardOffer{
		Id:           rewardOffer.GetId(),
		Title:        rewardOffer.GetDisplayMeta().GetTitle(),
		Steps:        rewardOffer.GetDisplayMeta().GetSteps(),
		Tncs:         rewardOffer.GetDisplayMeta().GetTncs(),
		ActionDesc:   rewardOffer.GetDisplayMeta().GetActionDesc(),
		StepsV1:      stepsV1,
		TncsV1:       tncsV1,
		UnlockSteps:  unlockSteps,
		ActiveSince:  getTimestampFromTimeString(rewardOffer.GetActiveSince()),
		ActiveTill:   getTimestampFromTimeString(rewardOffer.GetActiveTill()),
		DisplaySince: getTimestampFromTimeString(rewardOffer.GetDisplayMeta().GetDisplaySince()),
		DisplayTill:  getTimestampFromTimeString(rewardOffer.GetDisplayMeta().GetDisplayTill()),
		AttachEntityMeta: &ticketPb.AttachEntityMeta{
			Meta: &ticketPb.AttachEntityMeta_RewardOfferMeta{
				RewardOfferMeta: &ticketPb.RewardOfferMeta{
					RewardOfferId:          rewardOffer.GetId(),
					RewardOfferName:        rewardOffer.GetDisplayMeta().GetTitle(),
					RewardOfferDescription: rewardOffer.GetDisplayMeta().GetActionDesc(),
				},
			},
		},
	}
}

func formatExchangerOffersResponse(response *exchangerPb.GetExchangerOffersByFiltersResponse) (*rePb.GetExchangerOffersResponse, error) {
	// returning StatusRecordNotFound in case there are no exchanger offers returned from GetExchangerOffer service
	if len(response.GetExchangerOffers()) == 0 {
		return &rePb.GetExchangerOffersResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	var exchangerOffers []*rePb.ExchangerOffer
	for _, o := range response.GetExchangerOffers() {
		exchangerOffers = append(exchangerOffers, &rePb.ExchangerOffer{
			Id:             o.GetId(),
			Name:           o.GetOfferDisplayDetails().GetTitle(),
			Desc:           o.GetOfferDisplayDetails().GetDesc(),
			Price:          o.GetRedemptionPrice(),
			RedemptionMode: rePb.OfferRedemptionMode(o.GetRedemptionCurrency()),
			TncList:        o.GetOfferDisplayDetails().GetTnc(),
			DisplaySince:   getTimestampFromTimeString(response.GetOfferIdToListingMap()[o.GetId()].GetDisplaySince()),
			DisplayTill:    getTimestampFromTimeString(response.GetOfferIdToListingMap()[o.GetId()].GetDisplayTill()),
			ActiveSince:    getTimestampFromTimeString(response.GetOfferIdToListingMap()[o.GetId()].GetActiveSince()),
			ActiveTill:     getTimestampFromTimeString(response.GetOfferIdToListingMap()[o.GetId()].GetActiveTill()),
			AttachEntityMeta: &ticketPb.AttachEntityMeta{
				Meta: &ticketPb.AttachEntityMeta_ExchangerOfferMeta{
					ExchangerOfferMeta: &ticketPb.ExchangerOfferMeta{
						ExchangerOfferId:          o.GetId(),
						ExchangerOfferTitle:       o.GetOfferDisplayDetails().GetTitle(),
						ExchangerOfferDescription: o.GetOfferDisplayDetails().GetDesc(),
					},
				},
			},
		})
	}
	return &rePb.GetExchangerOffersResponse{
		Status:          rpcPb.StatusOk(),
		ExchangerOffers: exchangerOffers,
	}, nil
}

func (s *Service) GetExchangerOffers(ctx context.Context, req *rePb.GetExchangerOffersRequest) (*rePb.GetExchangerOffersResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &rePb.GetExchangerOffersResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()
	getExchangerOffersRequest := &exchangerPb.GetExchangerOffersByFiltersRequest{
		ActorId: actorId,
		Filters: &exchangerPb.ExchangerOfferFilters{
			FromTime: req.GetFromTime(),
			TillTime: req.GetDate(),
		},
	}
	resp, err := s.exchangerOffersClient.GetExchangerOffersByFilters(ctx, getExchangerOffersRequest)
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "failed to fetch exchanger offers", zap.Error(te), zap.String(logger.ACTOR_ID, actorId))
		return &rePb.GetExchangerOffersResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to fetch exchanger offers")}, nil
	}
	return formatExchangerOffersResponse(resp)
}

func convertToExchangerOffersPageContext(pageContext *cx.PageContextRequest) *rpcPb.PageContextRequest {
	if pageContext == nil {
		return nil
	}
	targetPageContext := &rpcPb.PageContextRequest{}
	targetPageContext.PageSize = pageContext.GetPageSize()
	switch pageContext.GetToken().(type) {
	case *cx.PageContextRequest_AfterToken:
		targetPageContext.Token = &rpcPb.PageContextRequest_AfterToken{
			AfterToken: pageContext.GetAfterToken(),
		}
	case *cx.PageContextRequest_BeforeToken:
		targetPageContext.Token = &rpcPb.PageContextRequest_BeforeToken{
			BeforeToken: pageContext.GetBeforeToken(),
		}
	}
	return targetPageContext
}

func formatExchangerOfferOrderResponse(resp *exchangerPb.GetExchangerOfferOrdersForActorResponse) (*rePb.GetExchangerOfferOrdersResponse, error) {
	// mask any user info in exchanger-orders
	maskUserInfoInExchangerOrders(resp.GetExchangerOfferOrders())

	// returning StatusRecordNotFound in case there are no exchanger offers returned from GetExchangerOfferOrdersForActor service
	if len(resp.GetExchangerOfferOrders()) == 0 {
		return &rePb.GetExchangerOfferOrdersResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	var exchangerOfferOrderCxWrapper []*rePb.ExchangerOfferOrderCxWrapper
	for _, exchangerOfferOrder := range resp.GetExchangerOfferOrders() {
		exchangerOfferOrderCxWrapper = append(exchangerOfferOrderCxWrapper, &rePb.ExchangerOfferOrderCxWrapper{
			ExchangerOfferOrder: exchangerOfferOrder,
			AttachEntityMeta: &ticketPb.AttachEntityMeta{
				Meta: &ticketPb.AttachEntityMeta_ExchangerOfferOrderMeta{
					ExchangerOfferOrderMeta: &ticketPb.ExchangerOfferOrderMeta{
						ExchangerOrderId: exchangerOfferOrder.GetId(),
					},
				},
			},
		})
	}

	return &rePb.GetExchangerOfferOrdersResponse{
		Status: rpcPb.StatusOk(),
		PageContext: &cx.PageContextResponse{
			HasBefore:   resp.GetPageContext().GetHasBefore(),
			HasAfter:    resp.GetPageContext().GetHasAfter(),
			BeforeToken: resp.GetPageContext().GetBeforeToken(),
			AfterToken:  resp.GetPageContext().GetAfterToken(),
		},
		ExchangerOfferOrders:             resp.GetExchangerOfferOrders(),
		ExchangerOfferOrderCxWrapperList: exchangerOfferOrderCxWrapper,
	}, nil

}

func maskUserInfoInExchangerOrders(exchangerOrders []*exchangerPb.ExchangerOfferOrder) {
	for _, order := range exchangerOrders {
		// mask user info in reward specific data
		switch order.GetRewardType() {
		case exchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE:
			shippingAddress := order.GetChosenOption().GetPhysicalMerchandiseRewardMetadata().GetShippingAddress()
			if len(shippingAddress.GetAddressLines()) > 0 {
				maxCharsToShow := int32(math.Min(float64(len(shippingAddress.GetAddressLines()[0])), 5))
				newAddressLines := []string{fmt.Sprintf("%s***", shippingAddress.GetAddressLines()[0][:maxCharsToShow])}
				// replace the existing shipping address with the masked address-lines
				order.GetChosenOption().GetPhysicalMerchandiseRewardMetadata().ShippingAddress = &postaladdress.PostalAddress{
					PostalCode:   shippingAddress.GetPostalCode(),
					AddressLines: newAddressLines,
				}
			}
		default:
			// do nothing
		}
	}
}

func (s *Service) GetExchangerOfferOrders(ctx context.Context, req *rePb.GetExchangerOfferOrdersRequest) (*rePb.GetExchangerOfferOrdersResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())

	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &rePb.GetExchangerOfferOrdersResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	actorId := req.GetHeader().GetActor().GetId()

	// sending an empty slice in case the redemption state parameter of request is 0, i.e, ExchangerOfferOrderState_ORDER_STATE_UNSPECIFIED
	// as `States` filter expects a slice and a non-empty slice causes incorrect results to be fetched in this case.
	exchangerOfferOrderState := req.GetRedemptionState()
	states := []exchangerPb.ExchangerOfferOrderState{exchangerOfferOrderState}
	if exchangerOfferOrderState == exchangerPb.ExchangerOfferOrderState_ORDER_STATE_UNSPECIFIED {
		states = []exchangerPb.ExchangerOfferOrderState{}
	}

	resp, err := s.exchangerOffersClient.GetExchangerOfferOrdersForActor(ctx, &exchangerPb.GetExchangerOfferOrdersForActorRequest{
		ActorId:     actorId,
		PageContext: convertToExchangerOffersPageContext(req.GetPageContext()),
		Filters: &exchangerPb.GetExchangerOfferOrdersForActorRequest_Filters{
			FromDate: req.GetFromDate(),
			UptoDate: req.GetUptoDate(),
			States:   states,
		},
	})

	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching exchanger offer details from exchanger offer order service", zap.Error(te), zap.String(logger.ACTOR_ID, actorId))
		return &rePb.GetExchangerOfferOrdersResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching exchanger offer order details from exchanger offer order service"),
		}, nil
	}
	return formatExchangerOfferOrderResponse(resp)
}

// nolint: funlen
func (s *Service) GetRewardUnitsUtilisationForActorAndOfferInMonth(ctx context.Context, req *rePb.GetRewardUnitsUtilisationForActorAndOfferInMonthRequest) (*rePb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse, error) {

	monthlyRewardUnitsUtilisedAndRewardCountForActorAndOffer, err := s.rewardClient.GetRewardUnitsUtilisationForActorAndOfferInMonth(ctx, &rewardsPb.GetRewardUnitsUtilisationForActorAndOfferInMonthRequest{
		ActorId:       req.GetHeader().GetActor().GetId(),
		RewardOfferId: req.GetRewardOfferId(),
		Date:          req.GetDate(),
	})
	if e := epifigrpc.RPCError(monthlyRewardUnitsUtilisedAndRewardCountForActorAndOffer, err); e != nil {
		cxLogger.Error(ctx, "error in GetMonthlyRewardUnitsUtilisedAndRewardCountForActorAndOffer rpc", zap.Error(e))
		return &rePb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in GetMonthlyRewardUnitsUtilisedAndRewardCountForActorAndOffer rpc"),
		}, nil
	}

	rewardOffersByIds, err := s.rewardOffersClient.GetRewardOffersByIds(ctx, &rewardOfferPb.GetRewardOffersByIdsRequest{
		Ids: []string{req.GetRewardOfferId()},
	})
	if e := epifigrpc.RPCError(rewardOffersByIds, err); e != nil {
		cxLogger.Error(ctx, "error in GetRewardOffersByIds rpc", zap.Error(e))
		return &rePb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in GetRewardOffersByIds rpc"),
		}, nil
	}
	if len(rewardOffersByIds.GetRewardOffers()) == 0 {
		cxLogger.Error(ctx, "no reward offer found for given id", zap.String(logger.REWARD_OFFER_ID, req.GetRewardOfferId()))
		return &rePb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("no reward offer found for given id"),
		}, nil
	}

	rewardOffer := rewardOffersByIds.GetRewardOffers()[0]
	rewardTypeToMonthlyCappingMap := make(map[rewardsPb.RewardType]uint32)
	monthlyUnitsCaps := rewardOffer.GetRewardMeta().GetRewardAggregates().GetRewardUnitsCapMonthlyUserAggregate().GetUnitsCaps()
	for _, monthlyUnitsCap := range monthlyUnitsCaps {
		rewardTypeToMonthlyCappingMap[monthlyUnitsCap.GetRewardType()] = monthlyUnitsCap.GetUnits()
	}

	unitsUtilizedAndCappingList := []*rePb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping{
		{
			RewardType:    rewardsPb.RewardType_FI_COINS,
			UnitsUtilized: monthlyRewardUnitsUtilisedAndRewardCountForActorAndOffer.GetMonthlyRewardOfferRewardUnitsActorUtilisation().GetFiCoinUnits(),
			Capping:       rewardTypeToMonthlyCappingMap[rewardsPb.RewardType_FI_COINS],
		},
		{
			RewardType:    rewardsPb.RewardType_CASH,
			UnitsUtilized: monthlyRewardUnitsUtilisedAndRewardCountForActorAndOffer.GetMonthlyRewardOfferRewardUnitsActorUtilisation().GetCashUnits(),
			Capping:       rewardTypeToMonthlyCappingMap[rewardsPb.RewardType_CASH],
		},
		{
			RewardType:    rewardsPb.RewardType_SMART_DEPOSIT,
			UnitsUtilized: monthlyRewardUnitsUtilisedAndRewardCountForActorAndOffer.GetMonthlyRewardOfferRewardUnitsActorUtilisation().GetSdCashUnits(),
			Capping:       rewardTypeToMonthlyCappingMap[rewardsPb.RewardType_SMART_DEPOSIT],
		},
	}

	return &rePb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse{
		Status:                               rpcPb.StatusOk(),
		UnitsUtilizedAndCappingList:          unitsUtilizedAndCappingList,
		RewardCount:                          monthlyRewardUnitsUtilisedAndRewardCountForActorAndOffer.GetMonthlyRewardCount(),
		RewardTypeUtilizationAndCappingTable: s.GetRewardTypeUtilizationAndCappingTable(unitsUtilizedAndCappingList),
	}, nil

}

func (s *Service) GetRewardTypeUtilizationAndCappingTable(unitsUtilizedAndCappingList []*rePb.GetRewardUnitsUtilisationForActorAndOfferInMonthResponse_UnitsUtilizedAndCapping) *webui.Table {

	const (
		RewardTypeHeader           = "Reward Type"
		MonthlyUnitsUtilizedHeader = "Monthly Units Utilized"
		MonthlyCappingHeader       = "Monthly Capping"
	)

	rewardTypeUtilizationAndCappingTable := &webui.Table{}
	rewardTypeUtilizationAndCappingTable.TableName = "Reward type monthly utilization and capping table"
	rewardTypeUtilizationAndCappingTable.TableHeaders = []*webui.TableHeader{}
	rewardTypeUtilizationAndCappingTable.TableRows = []*webui.TableRow{}
	rewardTypeUtilizationAndCappingTable.TableHeaders = append(rewardTypeUtilizationAndCappingTable.TableHeaders,
		&webui.TableHeader{
			Label:     RewardTypeHeader,
			HeaderKey: RewardTypeHeader,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     MonthlyUnitsUtilizedHeader,
			HeaderKey: MonthlyUnitsUtilizedHeader,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     MonthlyCappingHeader,
			HeaderKey: MonthlyCappingHeader,
			IsVisible: true,
		},
	)
	for _, unitsUtilizedAndCapping := range unitsUtilizedAndCappingList {

		headerKeyCellMap := make(map[string]*webui.TableCell)
		headerKeyCellMap[RewardTypeHeader] = &webui.TableCell{
			Value: unitsUtilizedAndCapping.GetRewardType().String(),
		}
		headerKeyCellMap[MonthlyCappingHeader] = &webui.TableCell{
			Value: strconv.Itoa(int(unitsUtilizedAndCapping.GetCapping())),
		}
		headerKeyCellMap[MonthlyUnitsUtilizedHeader] = &webui.TableCell{
			Value: strconv.Itoa(int(unitsUtilizedAndCapping.GetUnitsUtilized())),
		}
		rewardTypeUtilizationAndCappingTable.TableRows = append(rewardTypeUtilizationAndCappingTable.TableRows, &webui.TableRow{
			HeaderKeyCellMap: headerKeyCellMap,
		})
	}

	return rewardTypeUtilizationAndCappingTable

}

func (s *Service) GetRewardOfferTypesOptions(ctx context.Context, req *rePb.GetRewardOfferTypesOptionsRequest) (*rePb.GetRewardOfferTypesOptionsResponse, error) {
	offerTypes := make(map[string]string, 0)
	for _, val := range rePb.RewardOfferTypesOptions_name {
		offerTypes[val] = cxRewardOfferTypeToTextDescription[rePb.RewardOfferTypesOptions(rePb.RewardOfferTypesOptions_value[val])]
	}
	return &rePb.GetRewardOfferTypesOptionsResponse{
		Status:     rpc.StatusOk(),
		OfferTypes: offerTypes,
	}, nil
}

func (s *Service) GetRewardDetails(ctx context.Context, req *rePb.GetRewardDetailsRequest) (*rePb.GetRewardDetailsResponse, error) {
	var (
		refIds             []string
		timeWindow         *rewardsPb.TimeWindow
		rewardDetailsTable *webui.Table
		err                error
	)

	switch {
	case req.GetExternalTxnId() != "":
		refIds = append(refIds, req.GetExternalTxnId())
	case req.GetFromDate() != nil && req.GetToDate() != nil:
		timeWindow = &rewardsPb.TimeWindow{
			FromTime: req.GetFromDate(),
			TillTime: req.GetToDate(),
		}
		timeDiff, timeErr := validateTimeDifference(req.GetFromDate(), req.GetToDate())
		if timeErr != nil {
			cxLogger.Error(ctx, "Date range must not exceed 31 days", zap.String("req", req.String()), zap.String("timeDiff", timeDiff.String()), zap.Error(timeErr))
			return &rePb.GetRewardDetailsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("Date range must not exceed 31 days"),
			}, nil
		}
	default:
		cxLogger.Error(ctx, "Either date range or external txn id is mandatory", zap.String("req", req.String()))
		return &rePb.GetRewardDetailsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("Either date range or external txn id is mandatory"),
		}, nil
	}
	val, ok := rePb.RewardOfferTypesOptions_value[req.GetRewardOfferType()]
	if !ok {
		cxLogger.Error(ctx, "error in fetching reward offer type", zap.String(logger.ACTOR_ID_V2, req.GetHeader().GetUser().GetActorId()), zap.String("rewardOfferType", req.GetRewardOfferType()))
		return &rePb.GetRewardDetailsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("Unsupported reward offer type"),
		}, nil
	}
	rewardOfferType := rePb.RewardOfferTypesOptions(val)
	if lo.Contains(lo.Keys(cxRewardOfferTypeToBeRewardOfferType), rewardOfferType) {
		rewardDetailsTable, err = s.GetRewardDetailsForTieringCashback(ctx, req.GetHeader().GetUser().GetActorId(), cxRewardOfferTypeToBeRewardOfferType[rewardOfferType], refIds, timeWindow)
		if err != nil {
			cxLogger.Error(ctx, "error while fetching reward details for tiering cashback", zap.String(logger.ACTOR_ID_V2, req.GetHeader().GetUser().GetActorId()), zap.Error(err))
			return &rePb.GetRewardDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching reward details"),
			}, nil
		}
	}
	return &rePb.GetRewardDetailsResponse{
		Status:             rpc.StatusOk(),
		RewardDetailsTable: rewardDetailsTable,
	}, nil
}

func (s *Service) GetRewardDetailsForTieringCashback(ctx context.Context, actorId string, offerType rewardsPb.RewardOfferType, refIds []string, timeWindow *rewardsPb.TimeWindow) (*webui.Table, error) {
	var (
		pageSize   = 30
		pageCtxReq = &rpcPb.PageContextRequest{
			PageSize: uint32(pageSize),
		}
		projections      []*rewardsProjectionPb.Projection
		filterOfferTypes []rewardsPb.RewardOfferType
	)

	if offerType != rewardsPb.RewardOfferType_UNSPECIFIED_REWARD_OFFER_TYPE {
		filterOfferTypes = []rewardsPb.RewardOfferType{offerType}
	}

	for {
		resp, err := s.rewardsProjectionClient.GetRewardsProjections(ctx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
			ActorId: actorId,
			Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
				ActionType: []rewardsPb.CollectedDataType{
					rewardsPb.CollectedDataType_ORDER,
				},
				RefIds:     refIds,
				TimeWindow: timeWindow,
				OfferType:  filterOfferTypes,
			},
			FetchAggregates: false,
			PageCtxRequest:  pageCtxReq,
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			return nil, rpcErr
		}
		projections = append(projections, resp.GetIndividualProjections().GetProjections()...)
		pageContext := resp.GetPageCtxResponse()
		if !pageContext.GetHasAfter() {
			break
		}
		pageCtxReq.Token = &rpc.PageContextRequest_AfterToken{AfterToken: pageContext.GetAfterToken()}
	}

	// Extract unique offer IDs from projections
	offerIDs := lo.Map(projections, func(projection *rewardsProjectionPb.Projection, _ int) string {
		return projection.GetOfferId()
	})
	uniqueOfferIDs := lo.Uniq(offerIDs)

	// Fetch reward offer details from rewards service
	rewardOffersByIds, err := s.rewardOffersClient.GetRewardOffersByIds(ctx, &rewardOfferPb.GetRewardOffersByIdsRequest{
		Ids: uniqueOfferIDs,
	})
	if rpcErr := epifigrpc.RPCError(rewardOffersByIds, err); rpcErr != nil {
		return nil, rpcErr
	}

	// Create a map of offer ID to reward offer
	offerIDToRewardOffer := lo.SliceToMap(rewardOffersByIds.GetRewardOffers(), func(rewardOffer *rewardOfferPb.RewardOffer) (string, *rewardOfferPb.RewardOffer) {
		return rewardOffer.GetId(), rewardOffer
	})

	return getRewardDetailsTable(projections, offerIDToRewardOffer), nil
}

// nolint: funlen, gocritic
func getRewardDetailsTable(projections []*rewardsProjectionPb.Projection, offerIdToRewardOfferMap map[string]*rewardOfferPb.RewardOffer) *webui.Table {
	const (
		RewardOfferIdHeader           = "Reward Offer Id"
		ShortDescHeader               = "Short Description"
		RefIdHeader                   = "Txn Id/ Ref Id"
		ProjectedRewardCashbackHeader = "Projected Reward Cashback"
		ActualRewardCashbackHeader    = "Actual Reward Cashback"
		ProjectedRewardFiCoinsHeader  = "Projected Reward Fi Coins"
		ActualRewardFiCoinsHeader     = "Actual Reward Fi Coins"
		TimestampHeader               = "Timestamp"
	)
	rewardDetailsTable := &webui.Table{}
	rewardDetailsTable.TableName = "Check for Reward transactions details"
	rewardDetailsTable.TableHeaders = []*webui.TableHeader{}
	rewardDetailsTable.TableRows = []*webui.TableRow{}
	rewardDetailsTable.TableHeaders = append(rewardDetailsTable.TableHeaders,
		&webui.TableHeader{
			Label:     RewardOfferIdHeader,
			HeaderKey: RewardOfferIdHeader,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     ShortDescHeader,
			HeaderKey: ShortDescHeader,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     RefIdHeader,
			HeaderKey: RefIdHeader,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     ProjectedRewardCashbackHeader,
			HeaderKey: ProjectedRewardCashbackHeader,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     ActualRewardCashbackHeader,
			HeaderKey: ActualRewardCashbackHeader,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     ProjectedRewardFiCoinsHeader,
			HeaderKey: ProjectedRewardFiCoinsHeader,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     ActualRewardFiCoinsHeader,
			HeaderKey: ActualRewardFiCoinsHeader,
			IsVisible: true,
		},
		&webui.TableHeader{
			Label:     TimestampHeader,
			HeaderKey: TimestampHeader,
			IsVisible: true,
		},
	)
	for _, projection := range projections {
		headerKeyCallMap := make(map[string]*webui.TableCell)
		headerKeyCallMap[RewardOfferIdHeader] = &webui.TableCell{
			Value: projection.GetOfferId(),
		}
		headerKeyCallMap[ShortDescHeader] = &webui.TableCell{
			Value: offerIdToRewardOfferMap[projection.GetOfferId()].GetDisplayMeta().GetShortDesc(),
		}
		headerKeyCallMap[RefIdHeader] = &webui.TableCell{
			Value: projection.GetRefId(),
		}
		for _, rewardUnitsWithTypes := range projection.GetProjectedOptions().GetRewardUnitsWithTypes() {
			if rewardUnitsWithTypes.GetRewardType() == rewardsPb.RewardType_CASH {
				headerKeyCallMap[ProjectedRewardCashbackHeader] = &webui.TableCell{
					Value: strconv.FormatFloat(float64(rewardUnitsWithTypes.GetRewardUnits()), 'f', 2, 64),
				}
			} else if rewardUnitsWithTypes.GetRewardType() == rewardsPb.RewardType_FI_COINS {
				headerKeyCallMap[ProjectedRewardFiCoinsHeader] = &webui.TableCell{
					Value: strconv.FormatFloat(float64(rewardUnitsWithTypes.GetRewardUnits()), 'f', 2, 64),
				}
			}
		}
		for _, rewardUnitsWithTypes := range projection.GetRewardContributions().GetRewardUnitsWithTypes() {
			if rewardUnitsWithTypes.GetRewardType() == rewardsPb.RewardType_CASH {
				headerKeyCallMap[ActualRewardCashbackHeader] = &webui.TableCell{
					Value: strconv.FormatFloat(float64(rewardUnitsWithTypes.GetRewardUnits()), 'f', 2, 64),
				}
			} else if rewardUnitsWithTypes.GetRewardType() == rewardsPb.RewardType_FI_COINS {
				headerKeyCallMap[ActualRewardFiCoinsHeader] = &webui.TableCell{
					Value: strconv.FormatFloat(float64(rewardUnitsWithTypes.GetRewardUnits()), 'f', 2, 64),
				}
			}
		}
		headerKeyCallMap[TimestampHeader] = &webui.TableCell{
			Value: projection.GetActionTime().AsTime().In(datetime.IST).Format(time.RFC822),
		}

		rewardDetailsTable.TableRows = append(rewardDetailsTable.TableRows, &webui.TableRow{
			HeaderKeyCellMap: headerKeyCallMap,
		})
	}

	var (
		totalProjectedCashPosAmount   = 0.0
		totalProjectedCashNegAmount   = 0.0
		totalProjectedFiCoinPosAmount = 0.0
		totalProjectedFiCoinNegAmount = 0.0
		totalActualCashPosAmount      = 0.0
		totalActualCashNegAmount      = 0.0
		totalActualFiCoinPosAmount    = 0.0
		totalActualFiCoinNegAmount    = 0.0
	)
	for _, projection := range projections {
		for _, rewardUnitsWithTypes := range projection.GetProjectedOptions().GetRewardUnitsWithTypes() {
			if rewardUnitsWithTypes.GetRewardType() == rewardsPb.RewardType_CASH {
				if rewardUnitsWithTypes.GetRewardUnits() > 0 {
					totalProjectedCashPosAmount += float64(rewardUnitsWithTypes.GetRewardUnits())
				} else {
					totalProjectedCashNegAmount += float64(rewardUnitsWithTypes.GetRewardUnits())
				}
			} else if rewardUnitsWithTypes.GetRewardType() == rewardsPb.RewardType_FI_COINS {
				if rewardUnitsWithTypes.GetRewardUnits() > 0 {
					totalProjectedFiCoinPosAmount += float64(rewardUnitsWithTypes.GetRewardUnits())
				} else {
					totalProjectedFiCoinNegAmount += float64(rewardUnitsWithTypes.GetRewardUnits())
				}
			}
		}
		for _, rewardUnitsWithTypes := range projection.GetRewardContributions().GetRewardUnitsWithTypes() {
			if rewardUnitsWithTypes.GetRewardType() == rewardsPb.RewardType_CASH {
				if rewardUnitsWithTypes.GetRewardUnits() > 0 {
					totalActualCashPosAmount += float64(rewardUnitsWithTypes.GetRewardUnits())
				} else {
					totalActualCashNegAmount += float64(rewardUnitsWithTypes.GetRewardUnits())
				}
			} else if rewardUnitsWithTypes.GetRewardType() == rewardsPb.RewardType_FI_COINS {
				if rewardUnitsWithTypes.GetRewardUnits() > 0 {
					totalActualFiCoinPosAmount += float64(rewardUnitsWithTypes.GetRewardUnits())
				} else {
					totalActualFiCoinNegAmount += float64(rewardUnitsWithTypes.GetRewardUnits())
				}
			}
		}
	}
	// total positive amount
	rewardDetailsTable.TableRows = append(rewardDetailsTable.TableRows, addRewardDetailsTableRow("Total Positive amount", "",
		strconv.FormatFloat(totalProjectedCashPosAmount, 'f', 2, 64),
		strconv.FormatFloat(totalProjectedFiCoinPosAmount, 'f', 2, 64),
		strconv.FormatFloat(totalActualCashPosAmount, 'f', 2, 64),
		strconv.FormatFloat(totalActualFiCoinPosAmount, 'f', 2, 64),
		""))
	// total negative amount
	rewardDetailsTable.TableRows = append(rewardDetailsTable.TableRows, addRewardDetailsTableRow("Total Negative amount", "",
		strconv.FormatFloat(totalProjectedCashNegAmount, 'f', 2, 64),
		strconv.FormatFloat(totalProjectedFiCoinNegAmount, 'f', 2, 64),
		strconv.FormatFloat(totalActualCashNegAmount, 'f', 2, 64),
		strconv.FormatFloat(totalActualFiCoinNegAmount, 'f', 2, 64),
		""))
	// total aggregate amount
	rewardDetailsTable.TableRows = append(rewardDetailsTable.TableRows, addRewardDetailsTableRow("Total net amount", "",
		strconv.FormatFloat(totalProjectedCashPosAmount+totalProjectedCashNegAmount, 'f', 2, 64),
		strconv.FormatFloat(totalProjectedFiCoinPosAmount+totalProjectedFiCoinNegAmount, 'f', 2, 64),
		strconv.FormatFloat(totalActualCashPosAmount+totalActualCashNegAmount, 'f', 2, 64),
		strconv.FormatFloat(totalActualFiCoinPosAmount+totalActualFiCoinNegAmount, 'f', 2, 64),
		""))
	return rewardDetailsTable
}

func addRewardDetailsTableRow(rewardOfferId, refId, projectedCashback, projectedFiCoins, actualCashback, actualFiCoins, timestamp string) *webui.TableRow {
	const (
		RewardOfferIdHeader           = "Reward Offer Id"
		RefIdHeader                   = "Txn Id/ Ref Id"
		ProjectedRewardCashbackHeader = "Projected Reward Cashback"
		ActualRewardCashbackHeader    = "Actual Reward Cashback"
		ProjectedRewardFiCoinsHeader  = "Projected Reward Fi Coins"
		ActualRewardFiCoinsHeader     = "Actual Reward Fi Coins"
		TimestampHeader               = "Timestamp"
	)
	headerKeyCallMap := make(map[string]*webui.TableCell)
	headerKeyCallMap[RewardOfferIdHeader] = &webui.TableCell{
		Value: rewardOfferId,
	}
	headerKeyCallMap[RefIdHeader] = &webui.TableCell{
		Value: refId,
	}
	headerKeyCallMap[TimestampHeader] = &webui.TableCell{
		Value: timestamp,
	}
	headerKeyCallMap[ProjectedRewardCashbackHeader] = &webui.TableCell{
		Value: projectedCashback,
	}
	headerKeyCallMap[ProjectedRewardFiCoinsHeader] = &webui.TableCell{
		Value: projectedFiCoins,
	}
	headerKeyCallMap[ActualRewardCashbackHeader] = &webui.TableCell{
		Value: actualCashback,
	}
	headerKeyCallMap[ActualRewardFiCoinsHeader] = &webui.TableCell{
		Value: actualFiCoins,
	}
	return &webui.TableRow{
		HeaderKeyCellMap: headerKeyCallMap,
	}
}

func validateTimeDifference(ts1, ts2 *timestamppb.Timestamp) (time.Duration, error) {
	// Convert timestamp to time.Time
	time1 := time.Unix(ts1.Seconds, int64(ts1.Nanos)).UTC()
	time2 := time.Unix(ts2.Seconds, int64(ts2.Nanos)).UTC()

	// Calculate the time difference
	timeDifference := time2.Sub(time1)

	// Check if the time difference is exactly 31 days
	requiredDuration := 31 * 24 * time.Hour
	if timeDifference > requiredDuration {
		return timeDifference, fmt.Errorf("the time difference must not exceed 31 days")
	}
	return timeDifference, nil
}

func (s *Service) GetFiStoreRedemptionsDetails(ctx context.Context, req *rePb.GetFiStoreRedemptionsDetailsRequest) (*rePb.GetFiStoreRedemptionsDetailsResponse, error) {
	var (
		pageSize   = 30
		pageCtxReq = &rpcPb.PageContextRequest{
			PageSize: uint32(pageSize),
		}
		timeWindow *evrPb.TimeWindow
		categories []evrPb.Category
	)

	if req.GetPageContext() != nil {
		pageCtxReq = helper.ConvertToRpcPageContext(req.GetPageContext())
	}

	if req.GetCategory() != evrPb.Category_CATEGORY_UNSPECIFIED {
		categories = append(categories, req.GetCategory())
	}

	if req.GetFromDate() != nil && req.GetToDate() != nil {
		timeWindow = &evrPb.TimeWindow{
			FromTime: req.GetFromDate(),
			TillTime: req.GetToDate(),
		}
	}

	resp, err := s.externalVendorRedemptionsClient.GetFiStoreRedemptions(ctx, &evrPb.GetFiStoreRedemptionsRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
		Filters: &evrPb.GetFiStoreRedemptionsRequest_Filters{
			VendorRefId: req.GetOrderId(),
			Categories:  categories,
			TimeWindow:  timeWindow,
		},
		PageCtxRequest: pageCtxReq,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching fi store redemptions", zap.String(logger.ACTOR_ID_V2, req.GetHeader().GetActor().GetId()), zap.Error(rpcErr))
		return &rePb.GetFiStoreRedemptionsDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching fi store redemptions"),
		}, nil
	}

	return &rePb.GetFiStoreRedemptionsDetailsResponse{
		Status:                    rpc.StatusOk(),
		FiStoreRewardDetailsTable: getFiStoreRedemptionsDetailsTable(resp.GetRedemptions()),
		PageContext:               helper.ConvertToCxPageContext(resp.GetPageCtxResponse()),
	}, nil
}

// nolint: funlen
func getFiStoreRedemptionsDetailsTable(redemptions []*evrPb.FiStoreRedemption) *webui.Table {
	const (
		OrderIdHeader      = "Order Id"
		OrderStateHeader   = "Order State"
		OrderDateHeader    = "Order Date"
		ProductNameHeader  = "Product Name"
		ProductPriceHeader = "Product Price"
		FiCoinsSpentHeader = "Fi coins spent"
	)
	rewardDetailsTable := &webui.Table{}
	rewardDetailsTable.TableName = "Fi Store Redemptions"
	rewardDetailsTable.TableHeaders = []*webui.TableHeader{}
	rewardDetailsTable.TableRows = []*webui.TableRow{}
	rewardDetailsTable.TableHeaders = append(rewardDetailsTable.GetTableHeaders(),
		&webui.TableHeader{
			Label:     OrderIdHeader,
			HeaderKey: OrderIdHeader,
			IsVisible: true,
		}, &webui.TableHeader{
			Label:     OrderStateHeader,
			HeaderKey: OrderStateHeader,
			IsVisible: true,
		}, &webui.TableHeader{
			Label:     OrderDateHeader,
			HeaderKey: OrderDateHeader,
			IsVisible: true,
		}, &webui.TableHeader{
			Label:     ProductNameHeader,
			HeaderKey: ProductNameHeader,
			IsVisible: true,
		}, &webui.TableHeader{
			Label:     ProductPriceHeader,
			HeaderKey: ProductPriceHeader,
			IsVisible: true,
		}, &webui.TableHeader{
			Label:     FiCoinsSpentHeader,
			HeaderKey: FiCoinsSpentHeader,
			IsVisible: true,
		},
	)

	for _, fiStoreRedemption := range redemptions {
		headerKeyCallMap := make(map[string]*webui.TableCell)
		headerKeyCallMap[OrderIdHeader] = &webui.TableCell{
			Value: fiStoreRedemption.GetVendorRefId(),
		}
		headerKeyCallMap[OrderStateHeader] = &webui.TableCell{
			Value: strings.TrimPrefix(fiStoreRedemption.GetOrderStatus().String(), "ORDER_STATUS_"),
		}
		headerKeyCallMap[OrderDateHeader] = &webui.TableCell{
			Value: fiStoreRedemption.GetOrderTimestamp().AsTime().Format(time.RFC822),
		}
		headerKeyCallMap[ProductNameHeader] = &webui.TableCell{
			Value: fiStoreRedemption.GetRedemptionMetaData().GetProductName(),
		}
		headerKeyCallMap[ProductPriceHeader] = &webui.TableCell{
			Value: moneyPb.ToDisplayString(fiStoreRedemption.GetProductPrice()),
		}
		headerKeyCallMap[FiCoinsSpentHeader] = &webui.TableCell{
			Value: strconv.Itoa(int(fiStoreRedemption.GetSpentFiCoinUnits())),
		}
		attachEntityMeta := &ticketPb.AttachEntityMeta{
			Meta: &ticketPb.AttachEntityMeta_VendorRedemptionMeta{
				VendorRedemptionMeta: &ticketPb.VendorRedemptionMeta{
					VendorRedemptionId: fiStoreRedemption.GetId(),
				},
			},
		}
		attachEntityMetaJson, _ := protojson.Marshal(attachEntityMeta)
		rewardDetailsTable.TableRows = append(rewardDetailsTable.GetTableRows(), &webui.TableRow{
			Meta:             string(attachEntityMetaJson),
			HeaderKeyCellMap: headerKeyCallMap,
		})
	}
	return rewardDetailsTable
}

func (s *Service) GetFiStoreRedemptionAdditionalDetails(ctx context.Context, req *rePb.GetFiStoreRedemptionAdditionalDetailsRequest) (*rePb.GetFiStoreRedemptionAdditionalDetailsResponse, error) {
	resp, err := s.externalVendorRedemptionsClient.GetFiStoreRedemptions(ctx, &evrPb.GetFiStoreRedemptionsRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
		Filters: &evrPb.GetFiStoreRedemptionsRequest_Filters{
			RedemptionId: req.GetRedemptionId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching fi store redemptions", zap.String(logger.ACTOR_ID_V2, req.GetHeader().GetActor().GetId()), zap.Error(rpcErr))
		return &rePb.GetFiStoreRedemptionAdditionalDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching fi store redemption"),
		}, nil
	}

	if len(resp.GetRedemptions()) == 0 {
		return &rePb.GetFiStoreRedemptionAdditionalDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("no redemption exists with given id"),
		}, nil
	}
	fiStoreRedemption := resp.GetRedemptions()[0]
	externalVendorRedemptionsResp, err := s.externalVendorRedemptionsClient.GetExternalVendorRedemptions(ctx, &evrPb.GetExternalVendorRedemptionsRequest{
		ActorId: req.GetHeader().GetActor().GetId(),
		Filters: &evrPb.GetExternalVendorRedemptionsRequest_Filters{
			VendorOrderId: fiStoreRedemption.GetVendorRefId(),
			Vendor:        fiStoreRedemption.GetVendor(),
		},
	})
	if rpcErr := epifigrpc.RPCError(externalVendorRedemptionsResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching external vendor redemptions", zap.String(logger.ACTOR_ID_V2, req.GetHeader().GetActor().GetId()), zap.Error(rpcErr))
		return &rePb.GetFiStoreRedemptionAdditionalDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching external vendor redemptions"),
		}, nil
	}

	var (
		externalVendorRedemption  *evrPb.ExternalVendorRedemption
		externalVendorRedemptions = externalVendorRedemptionsResp.GetExternalVendorRedemptions()
		isFiCoinsTransacted       bool
	)
	if len(externalVendorRedemptionsResp.GetExternalVendorRedemptions()) == 0 {
		isFiCoinsTransacted = false
	} else {
		// first redemption is the latest one (credit txn) for the given vendor order id
		sort.Slice(externalVendorRedemptions, func(i, j int) bool {
			if externalVendorRedemptions[i].GetTxnType() == evrPb.TxnType_CREDIT {
				return true
			}
			return false
		})
		externalVendorRedemption = externalVendorRedemptions[0]
		isFiCoinsTransacted = true
	}

	return &rePb.GetFiStoreRedemptionAdditionalDetailsResponse{
		Status:        rpc.StatusOk(),
		RewardDetails: getFiStoreRedemptionDetails(fiStoreRedemption, externalVendorRedemption, isFiCoinsTransacted),
	}, nil
}

// nolint: funlen
func getFiStoreRedemptionDetails(redemption *evrPb.FiStoreRedemption, externalVendorRedemption *evrPb.ExternalVendorRedemption, isFiCoinsTransacted bool) []*webui.LabelValueV2 {
	const (
		VendorNameHeader        = "Vendor Name"
		CategoryHeader          = "Category"
		QuantityHeader          = "Quantity"
		FinalCashPriceHeader    = "Final cash price"
		FiCoinsSpentHeader      = "Fi coins spent"
		BrandNameHeader         = "Brand Name"
		OrderDeliveryLinkHeader = "Order Delivery Link (if any)"
		FiCoinsStatusHeader     = "Fi coins status"
	)

	var list []*webui.LabelValueV2

	list = append(list, &webui.LabelValueV2{
		Label:    VendorNameHeader,
		DataType: webui.LabelValueV2_DATA_TYPE_STRING,
		Value:    &webui.LabelValueV2_StringValue{StringValue: redemption.GetVendor().String()},
	}, &webui.LabelValueV2{
		Label:    CategoryHeader,
		DataType: webui.LabelValueV2_DATA_TYPE_STRING,
		Value:    &webui.LabelValueV2_StringValue{StringValue: strings.TrimPrefix(redemption.GetCategory().String(), "CATEGORY_")},
	}, &webui.LabelValueV2{
		Label:    QuantityHeader,
		DataType: webui.LabelValueV2_DATA_TYPE_STRING,
		Value:    &webui.LabelValueV2_StringValue{StringValue: strconv.Itoa(int(redemption.GetRedemptionMetaData().GetQuantity()))},
	}, &webui.LabelValueV2{
		Label:    FinalCashPriceHeader,
		DataType: webui.LabelValueV2_DATA_TYPE_STRING,
		Value:    &webui.LabelValueV2_StringValue{StringValue: moneyPb.ToDisplayString(redemption.GetSpentCashUnits())},
	}, &webui.LabelValueV2{
		Label:    FiCoinsSpentHeader,
		DataType: webui.LabelValueV2_DATA_TYPE_STRING,
		Value:    &webui.LabelValueV2_StringValue{StringValue: strconv.Itoa(int(redemption.GetSpentFiCoinUnits()))},
	}, &webui.LabelValueV2{
		Label:    BrandNameHeader,
		DataType: webui.LabelValueV2_DATA_TYPE_STRING,
		Value:    &webui.LabelValueV2_StringValue{StringValue: redemption.GetRedemptionMetaData().GetBrandName()},
	}, &webui.LabelValueV2{
		Label:    OrderDeliveryLinkHeader,
		DataType: webui.LabelValueV2_DATA_TYPE_STRING,
		Value:    &webui.LabelValueV2_StringValue{StringValue: redemption.GetRedemptionMetaData().GetOrderTrackingLink()},
	}, &webui.LabelValueV2{
		Label:    FiCoinsStatusHeader,
		DataType: webui.LabelValueV2_DATA_TYPE_STRING,
		Value:    &webui.LabelValueV2_StringValue{StringValue: getFiCoinsStatus(isFiCoinsTransacted, externalVendorRedemption)},
	})

	return list
}

func getFiCoinsStatus(isFiCoinsTransacted bool, externalVendorRedemption *evrPb.ExternalVendorRedemption) string {
	switch {
	case !isFiCoinsTransacted:
		return accrual.ReplaceCoinWithPointIfApplicable("Fi coins not used/transacted for the redemption")
	case externalVendorRedemption.GetTxnType() == evrPb.TxnType_CREDIT:
		return accrual.ReplaceCoinWithPointIfApplicable("Fi coins credited/refunded for the redemption")
	case externalVendorRedemption.GetTxnType() == evrPb.TxnType_DEBIT:
		return accrual.ReplaceCoinWithPointIfApplicable("Fi coins debited/transacted for the redemption")
	default:
		return accrual.ReplaceCoinWithPointIfApplicable("Fi coins status unknown")
	}
}
