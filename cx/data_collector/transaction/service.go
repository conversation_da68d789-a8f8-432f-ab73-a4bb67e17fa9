package transaction

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/cx"
	tPb "github.com/epifi/gamma/api/cx/data_collector/transaction"
	orderPb "github.com/epifi/gamma/api/order"
	oPb "github.com/epifi/gamma/api/order/cx"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	recurringPay "github.com/epifi/gamma/api/recurringpayment"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/webui"
	"github.com/epifi/gamma/cx/config"
	"github.com/epifi/gamma/cx/customer_auth/auth_engine"
	"github.com/epifi/gamma/cx/data_collector/helper"
	cxLogger "github.com/epifi/gamma/cx/logger"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
)

type Service struct {
	orderTxnClient                 oPb.CXClient
	authEngine                     auth_engine.IAuthEngine
	txnConfig                      *config.Transaction
	actorClient                    actorPb.ActorClient
	dataCollectorHelper            helper.IDataCollectorHelper
	txnDataCollectorHelper         helper.ITransactionsDataCollectorHelper
	orderConfig                    *config.OrderConfig
	txnCategoryDataCollectorHelper helper.TxnCategoryDataCollectorHelper
	recurringPaymentsClient        recurringPay.RecurringPaymentServiceClient
}

func NewService(orderTxnClient oPb.CXClient, authEngine auth_engine.IAuthEngine,
	txn *config.Transaction, actorClient actorPb.ActorClient, dataCollectorHelper helper.IDataCollectorHelper,
	orderConfig *config.OrderConfig, txnDataCollectorHelper helper.ITransactionsDataCollectorHelper,
	txnCategoryHelper helper.TxnCategoryDataCollectorHelper, recurringPaymentsClient recurringPay.RecurringPaymentServiceClient,
) *Service {
	return &Service{
		orderTxnClient:                 orderTxnClient,
		authEngine:                     authEngine,
		txnConfig:                      txn,
		actorClient:                    actorClient,
		dataCollectorHelper:            dataCollectorHelper,
		orderConfig:                    orderConfig,
		txnDataCollectorHelper:         txnDataCollectorHelper,
		txnCategoryDataCollectorHelper: txnCategoryHelper,
		recurringPaymentsClient:        recurringPaymentsClient,
	}
}

var _ tPb.CustomerTransactionsServer = &Service{}

var cxPaymentProtocolToOrderPaymentProtocol = map[tPb.PaymentProtocol]paymentPb.PaymentProtocol{
	tPb.PaymentProtocol_INTRA_BANK:                   paymentPb.PaymentProtocol_INTRA_BANK,
	tPb.PaymentProtocol_NEFT:                         paymentPb.PaymentProtocol_NEFT,
	tPb.PaymentProtocol_IMPS:                         paymentPb.PaymentProtocol_IMPS,
	tPb.PaymentProtocol_RTGS:                         paymentPb.PaymentProtocol_RTGS,
	tPb.PaymentProtocol_UPI:                          paymentPb.PaymentProtocol_UPI,
	tPb.PaymentProtocol_CARD:                         paymentPb.PaymentProtocol_CARD,
	tPb.PaymentProtocol_SWIFT:                        paymentPb.PaymentProtocol_SWIFT,
	tPb.PaymentProtocol_ENACH:                        paymentPb.PaymentProtocol_ENACH,
	tPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED: paymentPb.PaymentProtocol_PAYMENT_PROTOCOL_UNSPECIFIED,
}

// NOTE : DEPRECATED
func (s *Service) GetCustomerTransactions(ctx context.Context, req *tPb.GetCustomerTransactionsRequest) (*tPb.GetCustomerTransactionsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here
	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &tPb.GetCustomerTransactionsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	ret := validateRequest(req)
	if ret != nil {
		cxLogger.Info(ctx, "request is not valid to fetch txn's")
		return ret, nil
	}
	orderReq := buildGetOrdersForActorRequest(req, s.txnConfig)
	resp, err := s.orderTxnClient.GetOrdersForActor(ctx, orderReq)
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "unable to get all orders for an actor", zap.Error(err))
		return &tPb.GetCustomerTransactionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("unable to get all orders for an actor"),
		}, nil
	}
	return s.buildGetAllTxnCxProto(ctx, resp, req), nil
}

func (s *Service) GetAllTransactionsDetails(ctx context.Context, req *tPb.GetAllTransactionsDetailsRequest) (*tPb.GetAllTransactionsDetailsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here

	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &tPb.GetAllTransactionsDetailsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}
	orderReq := buildGetOrdersRequest(req, s.txnConfig)
	resp, err := s.orderTxnClient.GetOrdersForActor(ctx, orderReq)
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "unable to get all orders for an actor", zap.Error(te))
		return &tPb.GetAllTransactionsDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("unable to get all orders for an actor"),
		}, nil
	}
	orderWithTxnReq := &orderPb.GetOrdersWithTransactionsRequest{}
	for _, order := range resp.GetResults() {
		orderWithTxnReq.OrderIdentifiers = append(orderWithTxnReq.OrderIdentifiers, &orderPb.OrderIdentifier{
			Identifier: &orderPb.OrderIdentifier_OrderId{OrderId: order.GetOrderId()},
		})
	}
	orderWithTxnList, err := s.txnDataCollectorHelper.FetchOrderWithTxnList(ctx, orderWithTxnReq, req.GetHeader().GetActor().GetId())
	if err != nil {
		return &tPb.GetAllTransactionsDetailsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("unable to get order with transactions for an actor by order id"),
		}, nil
	}

	return &tPb.GetAllTransactionsDetailsResponse{
		Status:               rpcPb.StatusOk(),
		OrderWithTransaction: orderWithTxnList,
		BeforePageToken: &cx.PageToken{
			LastEventTimestamp: resp.GetBeforePageToken().GetLastOrderTimestamp(),
			EventOffset:        resp.GetBeforePageToken().GetOrderOffset(),
		},
		AfterPageToken: &cx.PageToken{
			LastEventTimestamp: resp.GetAfterPageToken().GetLastOrderTimestamp(),
			EventOffset:        resp.GetAfterPageToken().GetOrderOffset(),
		},
		Limit: s.txnConfig.PageSize,
	}, nil
}

func (s *Service) buildGetAllTxnCxProto(ctx context.Context, resp *oPb.GetOrdersForActorResponse,
	req *tPb.GetCustomerTransactionsRequest) *tPb.GetCustomerTransactionsResponse {
	// if there was any error in building response, we return immediately with error
	var custTxnDataList []*tPb.CustomerTransactionData
	for _, result := range resp.GetResults() {
		customerTxnData := &tPb.CustomerTransactionData{
			Amount:  result.GetAmount(),
			PiFrom:  result.GetPiFrom(),
			PiTo:    result.GetPiTo(),
			OrderId: result.GetOrderId(),
			Status:  result.GetStatus(),
			DetailedStatus: &tPb.DetailedStatus{
				Code:        result.GetDetailedStatus().GetCode(),
				Description: result.GetDetailedStatus().GetDescription(),
			},
			CreatedAt:       result.GetCreatedAt(),
			OrderTags:       result.GetOrderTags(),
			Provenance:      result.GetProvenance(),
			Workflow:        result.GetWorkflow(),
			PaymentProtocol: result.GetPaymentProtocol(),
		}
		// fill derived fields using response
		txnType, err := s.deriveTxnType(ctx, result.FromActorId, result.ToActorId, req.GetHeader().GetActor())
		if err != nil {
			cxLogger.Error(ctx, "cannot derive txn type using from actor and to actor", zap.Error(err))
			return &tPb.GetCustomerTransactionsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("cannot derive txn type using from actor and to actor"),
			}
		}
		customerTxnData.TransactionType = txnType
		fromActor, toActor, err := s.getActorInformation(ctx, result.GetFromActorId(), result.GetToActorId())
		if err != nil {
			cxLogger.Error(ctx, "cannot get to actor information from actor service", zap.Error(err))
			return &tPb.GetCustomerTransactionsResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("cannot get to actor information from actor service"),
			}
		}
		customerTxnData.FromActor = fromActor
		customerTxnData.ToActor = toActor
		custTxnDataList = append(custTxnDataList, customerTxnData)
	}
	return &tPb.GetCustomerTransactionsResponse{
		Status: rpcPb.StatusOk(), CustomerTransactionDataList: custTxnDataList,
		AfterPageToken: &cx.PageToken{
			LastEventTimestamp: resp.GetAfterPageToken().GetLastOrderTimestamp(),
			EventOffset:        resp.GetAfterPageToken().GetOrderOffset(),
		},
		BeforePageToken: &cx.PageToken{
			LastEventTimestamp: resp.GetBeforePageToken().GetLastOrderTimestamp(),
			EventOffset:        resp.GetBeforePageToken().GetOrderOffset(),
		},
	}
}

func (s *Service) getActorInformation(ctx context.Context, fromActorId string, toActorId string) (*typesPb.Actor, *typesPb.Actor, error) {
	// Get actor information from actor id in response
	respFrom, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: fromActorId})
	if te := epifigrpc.RPCError(respFrom, err); te != nil {
		cxLogger.Error(ctx, "cannot get from actor information from actor service", zap.Error(err))
		return nil, nil, fmt.Errorf("cannot get from actor information from actor service")
	}
	respTo, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: toActorId})
	if te := epifigrpc.RPCError(respTo, err); te != nil {
		cxLogger.Error(ctx, "cannot get to actor information from actor service", zap.Error(err))
		return nil, nil, fmt.Errorf("cannot get to actor information from actor service")
	}
	return maskActor(respFrom.GetActor()), maskActor(respTo.GetActor()), nil
}

func maskActor(act *typesPb.Actor) *typesPb.Actor {
	rs := []rune(act.GetName())
	for i := 2; i < len(rs)-2; i++ {
		rs[i] = 'X'
	}
	actor := &typesPb.Actor{
		Type: act.GetType(),
		Name: string(rs),
	}
	return actor
}

func (s *Service) deriveTxnType(ctx context.Context, fromActorId string, toActorId string, actor *typesPb.Actor) (tPb.TransactionType, error) {
	// From actor id in order response is always the one who got debited hence this logic
	if fromActorId == actor.GetId() {
		return tPb.TransactionType_DEBIT, nil
	} else if toActorId == actor.GetId() {
		return tPb.TransactionType_CREDIT, nil
	}
	cxLogger.Error(ctx, "from actor and to actor received from order service do match with that in request header")
	return tPb.TransactionType_UNSPECIFIED, fmt.Errorf("from actor and to actor received from order service do match with that in request header")
}

func buildGetOrdersRequest(req *tPb.GetAllTransactionsDetailsRequest, txnConfig *config.Transaction) *oPb.GetOrdersForActorRequest {
	fromTime := req.GetFromDate().AsTime()
	fromDate := datetime.TimeToDate(&fromTime)
	// To fetch transactions of same date also, we pass next day date to order service
	// for example if toTime was 27 nov 2020 4 pm, converting to date makes it 27 Nov so any transaction on that day will not come
	toTime := req.GetToDate().AsTime().AddDate(0, 0, 1)
	toDate := datetime.TimeToDate(&toTime)
	request := &oPb.GetOrdersForActorRequest{
		ActorId:  req.GetHeader().GetActor().GetId(),
		FromDate: fromDate,
		ToDate:   toDate,
	}
	if req.GetAmount() != nil {
		tol := (req.GetAmount().GetUnits() * txnConfig.ToleranceValue) / 100.0
		fromAmount := &moneyPb.Money{
			CurrencyCode: req.GetAmount().CurrencyCode,
			Units:        req.GetAmount().Units - tol,
			Nanos:        req.GetAmount().GetNanos(),
		}
		toAmount := &moneyPb.Money{
			CurrencyCode: req.GetAmount().CurrencyCode,
			Units:        req.GetAmount().Units + tol,
			Nanos:        req.GetAmount().GetNanos(),
		}
		request.FromAmount = fromAmount
		request.ToAmount = toAmount
	}
	if req.GetTransactionId() != "" {
		request.TransactionId = req.GetTransactionId()
	}
	if req.GetStatus() != tPb.OrderStatus_ORDER_STATUS_UNSPECIFIED {
		num, ok := orderPb.OrderStatus_value[req.GetStatus().String()]
		if !ok {
			cxLogger.ErrorNoCtx("order status enum passed does not exist in order service, please sync")
		} else {
			request.Status = orderPb.OrderStatus(num)
		}
	}
	if req.GetAfterPageToken() != nil {
		request.Token = &oPb.GetOrdersForActorRequest_AfterPageToken{AfterPageToken: &oPb.PageToken{
			LastOrderTimestamp: req.GetAfterPageToken().GetLastEventTimestamp(),
			OrderOffset:        req.GetAfterPageToken().GetEventOffset(),
		}}
	}
	if req.GetBeforePageToken() != nil {
		request.Token = &oPb.GetOrdersForActorRequest_BeforePageToken{BeforePageToken: &oPb.PageToken{
			LastOrderTimestamp: req.GetAfterPageToken().GetLastEventTimestamp(),
			OrderOffset:        req.GetAfterPageToken().GetEventOffset(),
		}}
	}

	if len(req.GetPaymentProtocolFilters()) != 0 {
		request.PaymentProtocolFilters = convertToOrderPaymentProtocolProto(req.GetPaymentProtocolFilters())
	}

	switch req.GetUtrFilter().(type) {
	case *tPb.GetAllTransactionsDetailsRequest_FullUtr:
		request.UtrFilter = &oPb.GetOrdersForActorRequest_FullUtr{FullUtr: req.GetFullUtr()}
	case *tPb.GetAllTransactionsDetailsRequest_PartialUtr:
		request.UtrFilter = &oPb.GetOrdersForActorRequest_PartialUtr{PartialUtr: req.GetPartialUtr()}
	default:
		logger.ErrorNoCtx("no utr filter found")
	}

	request.PageSize = txnConfig.PageSize
	return request
}

// function to change type of PaymentProtocolFilter variables from cx to payment
func convertToOrderPaymentProtocolProto(protocols []tPb.PaymentProtocol) []paymentPb.PaymentProtocol {
	var paymentProtocols []paymentPb.PaymentProtocol
	for _, protocol := range protocols {
		paymentProtocols = append(paymentProtocols, cxPaymentProtocolToOrderPaymentProtocol[protocol])
	}
	return paymentProtocols
}

func buildGetOrdersForActorRequest(req *tPb.GetCustomerTransactionsRequest, txnConfig *config.Transaction) *oPb.GetOrdersForActorRequest {
	fromTime := req.GetFromDate().AsTime()
	fromDate := datetime.TimeToDate(&fromTime)
	// To fetch transactions of same date also, we pass next day date to order service
	// for example if toTime was 27 nov 2020 4 pm, converting to date makes it 27 Nov so any transaction on that day will not come
	toTime := req.GetToDate().AsTime().AddDate(0, 0, 1)
	toDate := datetime.TimeToDate(&toTime)
	request := &oPb.GetOrdersForActorRequest{
		ActorId:  req.GetHeader().GetActor().GetId(),
		FromDate: fromDate,
		ToDate:   toDate,
	}
	if req.GetAmount() != nil {
		fromAmount := &moneyPb.Money{
			CurrencyCode: req.GetAmount().CurrencyCode,
			Units:        req.GetAmount().Units - txnConfig.ToleranceValue,
			Nanos:        req.GetAmount().GetNanos(),
		}
		toAmount := &moneyPb.Money{
			CurrencyCode: req.GetAmount().CurrencyCode,
			Units:        req.GetAmount().Units + txnConfig.ToleranceValue,
			Nanos:        req.GetAmount().GetNanos(),
		}
		request.FromAmount = fromAmount
		request.ToAmount = toAmount
	}
	if req.GetTransactionId() != "" {
		request.TransactionId = req.GetTransactionId()
	}
	if req.GetStatus() != orderPb.OrderStatus_ORDER_STATUS_UNSPECIFIED {
		request.Status = req.GetStatus()
	}
	if req.GetAfterPageToken() != nil {
		request.Token = &oPb.GetOrdersForActorRequest_AfterPageToken{AfterPageToken: &oPb.PageToken{
			LastOrderTimestamp: req.GetAfterPageToken().GetLastEventTimestamp(),
			OrderOffset:        req.GetAfterPageToken().GetEventOffset(),
		}}
	}
	if req.GetBeforePageToken() != nil {
		request.Token = &oPb.GetOrdersForActorRequest_BeforePageToken{BeforePageToken: &oPb.PageToken{
			LastOrderTimestamp: req.GetAfterPageToken().GetLastEventTimestamp(),
			OrderOffset:        req.GetAfterPageToken().GetEventOffset(),
		}}
	}
	request.PageSize = txnConfig.PageSize
	return request
}

func validateRequest(req *tPb.GetCustomerTransactionsRequest) *tPb.GetCustomerTransactionsResponse {
	if req.GetHeader().GetActor() == nil {
		return &tPb.GetCustomerTransactionsResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("actor not present in header")}
	}
	if req.GetFromDate() == nil && req.GetToDate() == nil {
		return &tPb.GetCustomerTransactionsResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("date range is mandatory")}
	}
	return nil
}

func (s *Service) GetFirstOrLastNTransactions(ctx context.Context, req *tPb.GetFirstOrLastNTransactionsRequest) (*tPb.GetFirstOrLastNTransactionsResponse, error) {
	isActionRequired, sherlockDeepLink := s.authEngine.IsAuthActionRequiredForInformationLevel(ctx, req.GetHeader(), req.GetHeader().GetInformationLevel())
	// underlying method should always return a non nil deeplink in case of additional action required hence that check is not present here

	if isActionRequired {
		cxLogger.Info(ctx, "auth action required to show information")
		return &tPb.GetFirstOrLastNTransactionsResponse{Status: rpcPb.StatusOk(), SherlockDeepLink: sherlockDeepLink}, nil
	}

	orderWithTxnList, err := s.txnDataCollectorHelper.GetFirstOrLastNTransactions(ctx, req.GetHeader().GetActor().GetId(), req.GetType(), s.orderConfig.TxnCountForLastNTxns)
	if err != nil {
		cxLogger.Error(ctx, "error fetching first N or last N transactions", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &tPb.GetFirstOrLastNTransactionsResponse{
				Status: rpcPb.StatusRecordNotFoundWithDebugMsg(err.Error()),
			}, nil
		}
		return &tPb.GetFirstOrLastNTransactionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	var orderIds []string
	for _, order := range orderWithTxnList {
		orderIds = append(orderIds, order.GetOrderId())
	}
	txnDsCategories, err := s.txnCategoryDataCollectorHelper.GetCategoriesForOrders(ctx, req.GetHeader().GetActor().GetId(), orderIds)
	if err != nil {
		cxLogger.Error(ctx, "Unable to get ds categories for the orders", zap.Error(err))
		return &tPb.GetFirstOrLastNTransactionsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("Unable to get ds categories for the orders"),
		}, nil
	}

	txnDetailsTable, err := s.createTxnDetailsTable(orderWithTxnList, txnDsCategories)

	return &tPb.GetFirstOrLastNTransactionsResponse{
		Status:               rpcPb.StatusOk(),
		OrderWithTransaction: orderWithTxnList,
		TransactionDetails:   txnDetailsTable,
	}, nil
}

func (s *Service) createTxnDetailsTable(ordersWithTxns []*tPb.OrderWithTransaction,
	orderIdToTxnCategoriesMap map[string][]string) (*webui.Table, error) {
	var (
		txnDetailsTable = &webui.Table{
			TableName: FirstOrLastNTxnsTableName,
			TableHeaders: []*webui.TableHeader{
				{Label: FiTxnIdLabel, HeaderKey: FiTxnIdKey},
				{Label: UtrLabel, HeaderKey: UtrKey},
				{Label: AmountLabel, HeaderKey: AmountKey},
				{Label: IncomingOutgoingLabel, HeaderKey: IncomingOutgoingKey},
				{Label: TxnModeLabel, HeaderKey: TxnModeKey},
				{Label: StatusLabel, HeaderKey: StatusKey},
				{Label: FromInstrLabel, HeaderKey: FromInstrKey},
				{Label: ToInstrLabel, HeaderKey: ToInstrKey},
				{Label: CreatedAtLabel, HeaderKey: CreatedAtKey},
				{Label: OrderTagsLabel, HeaderKey: OrderTagsKey},
				{Label: MerchantLabel, HeaderKey: MerchantKey},
				{Label: DsCategoriesLabel, HeaderKey: DsCategoriesKey},
			},
			Actions: []*webui.CTA{
				{Label: ViewDetailsCtaLabel},
				{Label: ViewDisputeDetailsCtaLabel},
				{Label: RaiseDisputeCtaLabel},
			},
			TableRows: []*webui.TableRow{},
		}
	)

	for _, order := range ordersWithTxns {
		amount, err := s.convertMoneyToString(order.GetAmount())
		if err != nil {
			return nil, fmt.Errorf("failed to convert amount to string: %w", err)
		}
		dsCategories := orderIdToTxnCategoriesMap[order.GetOrderId()]
		tableRow := &webui.TableRow{
			HeaderKeyToCellValueMap: map[string]string{
				FiTxnIdKey:          order.GetOrderExternalId(),
				UtrKey:              order.GetUtr(),
				AmountKey:           amount,
				IncomingOutgoingKey: s.getIncomingOutgoing(order.GetTransactionType()),
				TxnModeKey:          order.GetPaymentProtocol(),
				StatusKey:           order.GetTxnStatus(),
				FromInstrKey:        order.GetFromPiDetails().GetPiType(),
				ToInstrKey:          order.GetToPiDetails().GetPiType(),
				CreatedAtKey:        order.GetTxnCreatedAt().AsTime().In(datetime.IST).String(),
				OrderTagsKey:        strings.Join(order.GetOrderTags(), ","),
				MerchantKey:         order.GetMerchantDetails().GetMerchantName().ToString(),
				DsCategoriesKey:     strings.Join(dsCategories, ","),
			},
		}
		txnDetailsTable.TableRows = append(txnDetailsTable.TableRows, tableRow)
	}

	return txnDetailsTable, nil
}

func (s *Service) convertMoneyToString(amount *moneyPb.Money) (string, error) {
	amountInPaisa, err := money.ToPaise(amount)
	if err != nil {
		return "", fmt.Errorf("failed to convert amount to paisa: %w", err)
	}
	amt := float64(amountInPaisa) / 100.0
	amtStr := strconv.FormatFloat(amt, 'f', 2, 64)
	return fmt.Sprintf("%s %s", amount.GetCurrencyCode(), amtStr), nil
}

func (s *Service) getIncomingOutgoing(transactionType tPb.TransactionType) string {
	var incomingOutgoing string
	switch transactionType {
	case tPb.TransactionType_DEBIT:
		incomingOutgoing = "Outgoing"
	case tPb.TransactionType_CREDIT:
		incomingOutgoing = "Incoming"
	}
	return incomingOutgoing
}
