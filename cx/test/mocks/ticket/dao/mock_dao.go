// Code generated by MockGen. DO NOT EDIT.
// Source: dao.go

// Package mock_dao is a generated GoMock package.
package mock_dao

import (
	context "context"
	reflect "reflect"

	rpc "github.com/epifi/be-common/api/rpc"
	pagination "github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	ticket "github.com/epifi/gamma/api/cx/ticket"
	dao "github.com/epifi/gamma/cx/ticket/dao"
	gomock "github.com/golang/mock/gomock"
)

// MockISupportTicketDao is a mock of ISupportTicketDao interface.
type MockISupportTicketDao struct {
	ctrl     *gomock.Controller
	recorder *MockISupportTicketDaoMockRecorder
}

// MockISupportTicketDaoMockRecorder is the mock recorder for MockISupportTicketDao.
type MockISupportTicketDaoMockRecorder struct {
	mock *MockISupportTicketDao
}

// NewMockISupportTicketDao creates a new mock instance.
func NewMockISupportTicketDao(ctrl *gomock.Controller) *MockISupportTicketDao {
	mock := &MockISupportTicketDao{ctrl: ctrl}
	mock.recorder = &MockISupportTicketDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockISupportTicketDao) EXPECT() *MockISupportTicketDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockISupportTicketDao) Create(ctx context.Context, req *ticket.TicketDetails) (*ticket.TicketDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, req)
	ret0, _ := ret[0].(*ticket.TicketDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockISupportTicketDaoMockRecorder) Create(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockISupportTicketDao)(nil).Create), ctx, req)
}

// GetAllTickets mocks base method.
func (m *MockISupportTicketDao) GetAllTickets(ctx context.Context, filters *ticket.TicketFilters, pageToken *dao.PageToken, limit int) ([]*ticket.TicketDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllTickets", ctx, filters, pageToken, limit)
	ret0, _ := ret[0].([]*ticket.TicketDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllTickets indicates an expected call of GetAllTickets.
func (mr *MockISupportTicketDaoMockRecorder) GetAllTickets(ctx, filters, pageToken, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllTickets", reflect.TypeOf((*MockISupportTicketDao)(nil).GetAllTickets), ctx, filters, pageToken, limit)
}

// GetById mocks base method.
func (m *MockISupportTicketDao) GetById(ctx context.Context, id int64) (*ticket.TicketDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, id)
	ret0, _ := ret[0].(*ticket.TicketDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockISupportTicketDaoMockRecorder) GetById(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockISupportTicketDao)(nil).GetById), ctx, id)
}

// GetByIdWithLock mocks base method.
func (m *MockISupportTicketDao) GetByIdWithLock(ctx context.Context, id int64) (*ticket.TicketDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByIdWithLock", ctx, id)
	ret0, _ := ret[0].(*ticket.TicketDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByIdWithLock indicates an expected call of GetByIdWithLock.
func (mr *MockISupportTicketDaoMockRecorder) GetByIdWithLock(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByIdWithLock", reflect.TypeOf((*MockISupportTicketDao)(nil).GetByIdWithLock), ctx, id)
}

// GetTicketCount mocks base method.
func (m *MockISupportTicketDao) GetTicketCount(ctx context.Context, filters *ticket.TicketFilters) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTicketCount", ctx, filters)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketCount indicates an expected call of GetTicketCount.
func (mr *MockISupportTicketDaoMockRecorder) GetTicketCount(ctx, filters interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketCount", reflect.TypeOf((*MockISupportTicketDao)(nil).GetTicketCount), ctx, filters)
}

// GetTicketsByUpdatedAt mocks base method.
func (m *MockISupportTicketDao) GetTicketsByUpdatedAt(ctx context.Context, filters *ticket.TicketFilters, pageToken *pagination.PageToken, limit uint32) ([]*ticket.TicketDetails, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTicketsByUpdatedAt", ctx, filters, pageToken, limit)
	ret0, _ := ret[0].([]*ticket.TicketDetails)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetTicketsByUpdatedAt indicates an expected call of GetTicketsByUpdatedAt.
func (mr *MockISupportTicketDaoMockRecorder) GetTicketsByUpdatedAt(ctx, filters, pageToken, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketsByUpdatedAt", reflect.TypeOf((*MockISupportTicketDao)(nil).GetTicketsByUpdatedAt), ctx, filters, pageToken, limit)
}

// Update mocks base method.
func (m *MockISupportTicketDao) Update(ctx context.Context, req *ticket.TicketDetails, updateMask []ticket.SupportTicketFieldMask) (*ticket.TicketDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, req, updateMask)
	ret0, _ := ret[0].(*ticket.TicketDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockISupportTicketDaoMockRecorder) Update(ctx, req, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockISupportTicketDao)(nil).Update), ctx, req, updateMask)
}

// MockIBulkTicketJobDao is a mock of IBulkTicketJobDao interface.
type MockIBulkTicketJobDao struct {
	ctrl     *gomock.Controller
	recorder *MockIBulkTicketJobDaoMockRecorder
}

// MockIBulkTicketJobDaoMockRecorder is the mock recorder for MockIBulkTicketJobDao.
type MockIBulkTicketJobDaoMockRecorder struct {
	mock *MockIBulkTicketJobDao
}

// NewMockIBulkTicketJobDao creates a new mock instance.
func NewMockIBulkTicketJobDao(ctrl *gomock.Controller) *MockIBulkTicketJobDao {
	mock := &MockIBulkTicketJobDao{ctrl: ctrl}
	mock.recorder = &MockIBulkTicketJobDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIBulkTicketJobDao) EXPECT() *MockIBulkTicketJobDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIBulkTicketJobDao) Create(ctx context.Context, job *ticket.BulkTicketJobDetails) (*ticket.BulkTicketJobDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, job)
	ret0, _ := ret[0].(*ticket.BulkTicketJobDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockIBulkTicketJobDaoMockRecorder) Create(ctx, job interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIBulkTicketJobDao)(nil).Create), ctx, job)
}

// Get mocks base method.
func (m *MockIBulkTicketJobDao) Get(ctx context.Context, jobId int64) (*ticket.BulkTicketJobDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, jobId)
	ret0, _ := ret[0].(*ticket.BulkTicketJobDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockIBulkTicketJobDaoMockRecorder) Get(ctx, jobId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockIBulkTicketJobDao)(nil).Get), ctx, jobId)
}

// GetAll mocks base method.
func (m *MockIBulkTicketJobDao) GetAll(ctx context.Context, filters *ticket.BulkTicketJobFilters, pageToken *dao.PageToken, limit int) ([]*ticket.BulkTicketJobDetails, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAll", ctx, filters, pageToken, limit)
	ret0, _ := ret[0].([]*ticket.BulkTicketJobDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAll indicates an expected call of GetAll.
func (mr *MockIBulkTicketJobDaoMockRecorder) GetAll(ctx, filters, pageToken, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAll", reflect.TypeOf((*MockIBulkTicketJobDao)(nil).GetAll), ctx, filters, pageToken, limit)
}

// KillJob mocks base method.
func (m *MockIBulkTicketJobDao) KillJob(ctx context.Context, jobId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KillJob", ctx, jobId)
	ret0, _ := ret[0].(error)
	return ret0
}

// KillJob indicates an expected call of KillJob.
func (mr *MockIBulkTicketJobDaoMockRecorder) KillJob(ctx, jobId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KillJob", reflect.TypeOf((*MockIBulkTicketJobDao)(nil).KillJob), ctx, jobId)
}

// UpdateTicketProcessingFailure mocks base method.
func (m *MockIBulkTicketJobDao) UpdateTicketProcessingFailure(ctx context.Context, jobId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTicketProcessingFailure", ctx, jobId)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTicketProcessingFailure indicates an expected call of UpdateTicketProcessingFailure.
func (mr *MockIBulkTicketJobDaoMockRecorder) UpdateTicketProcessingFailure(ctx, jobId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTicketProcessingFailure", reflect.TypeOf((*MockIBulkTicketJobDao)(nil).UpdateTicketProcessingFailure), ctx, jobId)
}

// UpdateTicketProcessingSuccess mocks base method.
func (m *MockIBulkTicketJobDao) UpdateTicketProcessingSuccess(ctx context.Context, jobId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateTicketProcessingSuccess", ctx, jobId)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateTicketProcessingSuccess indicates an expected call of UpdateTicketProcessingSuccess.
func (mr *MockIBulkTicketJobDaoMockRecorder) UpdateTicketProcessingSuccess(ctx, jobId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTicketProcessingSuccess", reflect.TypeOf((*MockIBulkTicketJobDao)(nil).UpdateTicketProcessingSuccess), ctx, jobId)
}

// MockITicketFailureLogDao is a mock of ITicketFailureLogDao interface.
type MockITicketFailureLogDao struct {
	ctrl     *gomock.Controller
	recorder *MockITicketFailureLogDaoMockRecorder
}

// MockITicketFailureLogDaoMockRecorder is the mock recorder for MockITicketFailureLogDao.
type MockITicketFailureLogDaoMockRecorder struct {
	mock *MockITicketFailureLogDao
}

// NewMockITicketFailureLogDao creates a new mock instance.
func NewMockITicketFailureLogDao(ctrl *gomock.Controller) *MockITicketFailureLogDao {
	mock := &MockITicketFailureLogDao{ctrl: ctrl}
	mock.recorder = &MockITicketFailureLogDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockITicketFailureLogDao) EXPECT() *MockITicketFailureLogDaoMockRecorder {
	return m.recorder
}

// AddTicketFailureLog mocks base method.
func (m *MockITicketFailureLogDao) AddTicketFailureLog(ctx context.Context, failureLog *ticket.TicketFailureLog) (*ticket.TicketFailureLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddTicketFailureLog", ctx, failureLog)
	ret0, _ := ret[0].(*ticket.TicketFailureLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTicketFailureLog indicates an expected call of AddTicketFailureLog.
func (mr *MockITicketFailureLogDaoMockRecorder) AddTicketFailureLog(ctx, failureLog interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTicketFailureLog", reflect.TypeOf((*MockITicketFailureLogDao)(nil).AddTicketFailureLog), ctx, failureLog)
}

// GetAllFailureLogsForJob mocks base method.
func (m *MockITicketFailureLogDao) GetAllFailureLogsForJob(ctx context.Context, jobId int64) ([]*ticket.TicketFailureLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllFailureLogsForJob", ctx, jobId)
	ret0, _ := ret[0].([]*ticket.TicketFailureLog)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllFailureLogsForJob indicates an expected call of GetAllFailureLogsForJob.
func (mr *MockITicketFailureLogDaoMockRecorder) GetAllFailureLogsForJob(ctx, jobId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllFailureLogsForJob", reflect.TypeOf((*MockITicketFailureLogDao)(nil).GetAllFailureLogsForJob), ctx, jobId)
}

// MockITicketDetailsTransformationDao is a mock of ITicketDetailsTransformationDao interface.
type MockITicketDetailsTransformationDao struct {
	ctrl     *gomock.Controller
	recorder *MockITicketDetailsTransformationDaoMockRecorder
}

// MockITicketDetailsTransformationDaoMockRecorder is the mock recorder for MockITicketDetailsTransformationDao.
type MockITicketDetailsTransformationDaoMockRecorder struct {
	mock *MockITicketDetailsTransformationDao
}

// NewMockITicketDetailsTransformationDao creates a new mock instance.
func NewMockITicketDetailsTransformationDao(ctrl *gomock.Controller) *MockITicketDetailsTransformationDao {
	mock := &MockITicketDetailsTransformationDao{ctrl: ctrl}
	mock.recorder = &MockITicketDetailsTransformationDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockITicketDetailsTransformationDao) EXPECT() *MockITicketDetailsTransformationDaoMockRecorder {
	return m.recorder
}

// CreateBatch mocks base method.
func (m *MockITicketDetailsTransformationDao) CreateBatch(ctx context.Context, transformations []*ticket.TicketDetailsTransformation) ([]*ticket.TicketDetailsTransformation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateBatch", ctx, transformations)
	ret0, _ := ret[0].([]*ticket.TicketDetailsTransformation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateBatch indicates an expected call of CreateBatch.
func (mr *MockITicketDetailsTransformationDaoMockRecorder) CreateBatch(ctx, transformations interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateBatch", reflect.TypeOf((*MockITicketDetailsTransformationDao)(nil).CreateBatch), ctx, transformations)
}

// DeleteBatch mocks base method.
func (m *MockITicketDetailsTransformationDao) DeleteBatch(ctx context.Context, filters ...storagev2.FilterOption) (int64, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range filters {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteBatch", varargs...)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteBatch indicates an expected call of DeleteBatch.
func (mr *MockITicketDetailsTransformationDaoMockRecorder) DeleteBatch(ctx interface{}, filters ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, filters...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBatch", reflect.TypeOf((*MockITicketDetailsTransformationDao)(nil).DeleteBatch), varargs...)
}

// GetAllByFilters mocks base method.
func (m *MockITicketDetailsTransformationDao) GetAllByFilters(ctx context.Context, filters ...storagev2.FilterOption) ([]*ticket.TicketDetailsTransformation, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range filters {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllByFilters", varargs...)
	ret0, _ := ret[0].([]*ticket.TicketDetailsTransformation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllByFilters indicates an expected call of GetAllByFilters.
func (mr *MockITicketDetailsTransformationDaoMockRecorder) GetAllByFilters(ctx interface{}, filters ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, filters...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllByFilters", reflect.TypeOf((*MockITicketDetailsTransformationDao)(nil).GetAllByFilters), varargs...)
}

// Update mocks base method.
func (m *MockITicketDetailsTransformationDao) Update(ctx context.Context, req *ticket.TicketDetailsTransformation, updateMask []ticket.TicketDetailsTransformationFieldMask) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, req, updateMask)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockITicketDetailsTransformationDaoMockRecorder) Update(ctx, req, updateMask interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockITicketDetailsTransformationDao)(nil).Update), ctx, req, updateMask)
}

// MockIInAppCsatResponseDao is a mock of IInAppCsatResponseDao interface.
type MockIInAppCsatResponseDao struct {
	ctrl     *gomock.Controller
	recorder *MockIInAppCsatResponseDaoMockRecorder
}

// MockIInAppCsatResponseDaoMockRecorder is the mock recorder for MockIInAppCsatResponseDao.
type MockIInAppCsatResponseDaoMockRecorder struct {
	mock *MockIInAppCsatResponseDao
}

// NewMockIInAppCsatResponseDao creates a new mock instance.
func NewMockIInAppCsatResponseDao(ctrl *gomock.Controller) *MockIInAppCsatResponseDao {
	mock := &MockIInAppCsatResponseDao{ctrl: ctrl}
	mock.recorder = &MockIInAppCsatResponseDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIInAppCsatResponseDao) EXPECT() *MockIInAppCsatResponseDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockIInAppCsatResponseDao) Create(ctx context.Context, response *ticket.InAppCsatResponse) (*ticket.InAppCsatResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, response)
	ret0, _ := ret[0].(*ticket.InAppCsatResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockIInAppCsatResponseDaoMockRecorder) Create(ctx, response interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIInAppCsatResponseDao)(nil).Create), ctx, response)
}

// GetInAppCsatResponses mocks base method.
func (m *MockIInAppCsatResponseDao) GetInAppCsatResponses(ctx context.Context, filterOptions []storagev2.FilterOption) ([]*ticket.InAppCsatResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInAppCsatResponses", ctx, filterOptions)
	ret0, _ := ret[0].([]*ticket.InAppCsatResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInAppCsatResponses indicates an expected call of GetInAppCsatResponses.
func (mr *MockIInAppCsatResponseDaoMockRecorder) GetInAppCsatResponses(ctx, filterOptions interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInAppCsatResponses", reflect.TypeOf((*MockIInAppCsatResponseDao)(nil).GetInAppCsatResponses), ctx, filterOptions)
}
