package dao

import (
	"context"
	"fmt"
	"sort"
	"time"

	rpc2 "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/storage"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/cx/ticket/dao/model"
	"github.com/epifi/gamma/cx/ticket/ticket_states"

	"github.com/lib/pq"
	"github.com/pkg/errors"
	gormV2 "gorm.io/gorm"

	"github.com/epifi/be-common/pkg/pagination"
)

var CustomFieldFilterKeyToColumnMap = map[ticketPb.CustomFieldFilter_FilterKey]string{
	ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_LOAN_VENDOR:         "ticket->'customFields'->'loanOutcallMetadata'->>'loanVendor'",
	ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_LOAN_PROGRAM:        "ticket->'customFields'->'loanOutcallMetadata'->>'loanProgram'",
	ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION1:        "ticket->'customFields'->'loanOutcallMetadata'->>'disposition1'",
	ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION2:        "ticket->'customFields'->'loanOutcallMetadata'->>'disposition2'",
	ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION3:        "ticket->'customFields'->'loanOutcallMetadata'->>'disposition3'",
	ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DROP_OFF_STAGE:      "ticket->'customFields'->'loanOutcallMetadata'->>'dropOffStage'",
	ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_ACQUISITION_CHANNEL: "ticket->'customFields'->'loanOutcallMetadata'->>'acquisitionChannel'",
}

type SupportTicketDao struct {
	DB *gormV2.DB
}

var _ ISupportTicketDao = &SupportTicketDao{}

// column names to update for support ticket
var ColumnNameMap = map[ticketPb.SupportTicketFieldMask]string{
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_STATUS:                   "status",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_SOURCE:                   "source",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_PRODUCT_CATEGORY:         "product_category",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_PRODUCT_CATEGORY_DETAILS: "product_category_details",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_TICKET:                   "ticket",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_REQUESTER:                "requester",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_USER_IDENTIFIER_TYPE:     "identifier_type",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_USER_IDENTIFIER_VALUE:    "identifier_value",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_TICKET_CREATED_AT:        "ticket_created_at",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_TICKET_UPDATED_AT:        "ticket_updated_at",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_TICKET_META:              "ticket_meta",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_RAW_TICKET:               "raw_ticket",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_AGENT_GROUP:              "agent_group",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_ACTOR_ID:                 "actor_id",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_EXPECTED_RESOLUTION_TIME: "expected_resolution_time",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_ISSUE_CATEGORY_ID:        "issue_category_id",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_CONTACT_SUMMARY_DETAILS:  "contact_summary_details",
	ticketPb.SupportTicketFieldMask_SUPPORT_TICKET_FIELD_MASK_RESPONDER_ID:             "responder_id",
}

func NewSupportTicketDao(db types.SherlockPGDB) *SupportTicketDao {
	return &SupportTicketDao{
		DB: db,
	}
}

func (s *SupportTicketDao) ValidateCreate(req *ticketPb.TicketDetails) error {
	switch {
	case req.GetId() == 0:
		return errors.New("ticket id cannot be empty")
	case req.GetIdentifierType() == ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_UNSPECIFIED:
		return errors.New("identifier type cannot be unspecified")
	case req.GetIdentifierValue() == "":
		return errors.New("identifier value cannot be empty")
	case req.GetTicket() == nil:
		return errors.New("ticket cannot be nil")
	case req.GetRequester() == nil:
		return errors.New("requester cannot be nil")
	default:
		return nil
	}
}

func (s *SupportTicketDao) Create(ctx context.Context, req *ticketPb.TicketDetails) (*ticketPb.TicketDetails, error) {
	defer metric_util.TrackDuration("cx/ticket/dao", "SupportTicketDao", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.DB)
	validationErr := s.ValidateCreate(req)
	if validationErr != nil {
		return nil, errors.Wrap(validationErr, "mandatory fields not passed for create")
	}
	dbModel := model.FromProtoMessage(req)
	if err := db.Create(dbModel).Error; err != nil {
		var pgErr *pq.Error
		ok := errors.As(err, &pgErr)
		if ok && pgErr.Code == storage.PostgresDuplicateKeyValueErrorCode {
			return nil, epifierrors.ErrDuplicateEntry
		}
		return nil, errors.Wrap(err, "error while creating ticket details record")
	}
	return dbModel.ToProtoMessage(), nil
}

func (s *SupportTicketDao) Update(ctx context.Context, req *ticketPb.TicketDetails, updateMask []ticketPb.SupportTicketFieldMask) (*ticketPb.TicketDetails, error) {
	defer metric_util.TrackDuration("cx/ticket/dao", "SupportTicketDao", "Update", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.DB)
	if req.GetId() == 0 {
		return nil, errors.New("id can't be empty for update")
	}
	if len(updateMask) == 0 {
		return nil, errors.New("update mask can't be empty")
	}
	dbModel := model.FromProtoMessage(req)
	query := db.Model(dbModel).Where("id = ?", req.GetId()).Select(getColumnsForUpdate(updateMask)).Updates(dbModel)
	if query.Error != nil {
		return nil, errors.Wrap(query.Error, "error while updating ticket details in db")
	}
	// no row was found for update
	if query.RowsAffected == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return dbModel.ToProtoMessage(), nil
}

func (s *SupportTicketDao) GetById(ctx context.Context, id int64) (*ticketPb.TicketDetails, error) {
	defer metric_util.TrackDuration("cx/ticket/dao", "SupportTicketDao", "GetById", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.DB)
	if id == 0 {
		return nil, fmt.Errorf("ticket id is mandatory for getting ticket record")
	}
	ticketModel := &model.SupportTicket{}
	if err := db.Where("id = ?", id).First(ticketModel).Error; err != nil {
		if errors.Is(err, gormV2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "failed to fetch ticket record for id")
	}
	return ticketModel.ToProtoMessage(), nil
}

func (s *SupportTicketDao) GetAllTickets(ctx context.Context, filters *ticketPb.TicketFilters, pageToken *PageToken,
	limit int) ([]*ticketPb.TicketDetails, error) {
	defer metric_util.TrackDuration("cx/ticket/dao", "SupportTicketDao", "GetAllTickets", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.DB)
	var ticketList []*model.SupportTicket
	query, err := s.getQueryForFilters(db, filters)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting query for filters")
	}

	if pageToken != nil {
		if pageToken.Timestamp != nil {
			if pageToken.IsReverse {
				//  using ascending order here to fetch immediately previous record instead of first record
				query = query.Where("ticket_created_at >= ?", pageToken.Timestamp.AsTime()).Order("ticket_created_at ASC")
			} else {
				query = query.Where("ticket_created_at <= ?", pageToken.Timestamp.AsTime()).Order("ticket_created_at DESC")
			}
			query = query.Offset(int(pageToken.Offset))
		} else {
			query = query.Order("ticket_created_at DESC")
		}
	}

	// if limit is passed as -1 i.e service doesn't want to add any limit on response
	// make the limit as -2 because we are adding +1 to limit below
	if limit == -1 {
		limit = -2
	}
	if err := query.Limit(limit + 1).Find(&ticketList).Error; err != nil {
		return nil, errors.Wrap(err, "cannot get ticket records")
	}

	// if the page token is of reverse type, then fix the order of entries as paginated query fetches entries in a reverse order for applying offset.
	if pageToken != nil && pageToken.IsReverse {
		sort.Slice(ticketList, func(i, j int) bool {
			return ticketList[i].TicketCreatedAt.Time.After(ticketList[j].TicketCreatedAt.Time)
		})
	}

	var ticketProtoList []*ticketPb.TicketDetails
	for _, ticketModel := range ticketList {
		ticketProtoList = append(ticketProtoList, ticketModel.ToProtoMessage())
	}
	return ticketProtoList, nil
}

func getColumnsForUpdate(updateMask []ticketPb.SupportTicketFieldMask) []string {
	var selectColumns []string
	for _, field := range updateMask {
		selectColumns = append(selectColumns, ColumnNameMap[field])
	}
	return selectColumns
}

func (s *SupportTicketDao) GetTicketCount(ctx context.Context, filters *ticketPb.TicketFilters) (int64, error) {
	defer metric_util.TrackDuration("cx/ticket/dao", "SupportTicketDao", "GetTicketCount", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, s.DB)
	query, err := s.getQueryForFilters(db, filters)
	if err != nil {
		return 0, errors.Wrap(err, "error while getting query for filters")
	}
	var count int64
	if err := query.Model(&model.SupportTicket{}).Count(&count).Error; err != nil {
		return 0, errors.Wrap(err, "cannot get ticket records")
	}
	return count, nil
}

func (s *SupportTicketDao) getQueryForFilters(db *gormV2.DB, filters *ticketPb.TicketFilters) (*gormV2.DB, error) {
	query := db
	if len(filters.GetStatusList()) != 0 {
		query = query.Where("status IN (?)", filters.GetStatusList())
	}
	if len(filters.GetSourceList()) != 0 {
		query = query.Where("source IN (?)", filters.GetSourceList())
	}
	if len(filters.GetProductCategoryList()) != 0 {
		query = query.Where("product_category IN (?)", filters.GetProductCategoryList())
	}
	if len(filters.GetProductCategoryDetailsList()) != 0 {
		query = query.Where("product_category_details IN (?)", filters.GetProductCategoryDetailsList())
	}
	// if identifier value list is populated than identifier_type and  identifier_value filters will not be honoured
	if filters.GetIdentifierType() != ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_UNSPECIFIED && len(filters.GetIdentifierValueList()) == 0 {
		query = query.Where("identifier_type = ?", filters.GetIdentifierType())
	}
	if filters.GetIdentifierValue() != "" && len(filters.GetIdentifierValueList()) == 0 {
		query = query.Where("identifier_value = ?", filters.GetIdentifierValue())
	}
	if filters.GetFromTime() != nil {
		query = query.Where("ticket_created_at >= ?", filters.GetFromTime().AsTime())
	}
	if filters.GetToTime() != nil {
		query = query.Where("ticket_created_at <= ?", filters.GetToTime().AsTime())
	}
	if len(filters.GetIdentifierValueList()) != 0 {
		query = query.Where("identifier_value IN (?)", filters.GetIdentifierValueList())
	}
	if len(filters.GetAgentGroupList()) != 0 {
		query = query.Where("agent_group IN (?)", filters.GetAgentGroupList())
	}
	if filters.GetActiveAfterTime() != nil {
		query = query.Where(db.Where("ticket_updated_at >= ?", filters.GetActiveAfterTime().AsTime()).Or(db.Not("status IN (?)", ticket_states.GetClosedTicketStates())))
	}
	if len(filters.GetActorIdList()) != 0 {
		query = query.Where("actor_id IN (?)", filters.GetActorIdList())
	}
	if filters.GetIssueCategoryId() != "" {
		query = query.Where("issue_category_id = ?", filters.GetIssueCategoryId())
	}
	if filters.GetResponderId() != 0 {
		query = query.Where("responder_id = ?", filters.GetResponderId())
	}
	if len(filters.GetCustomFieldDbFilters()) > 0 {
		if filters.GetResponderId() == 0 && len(filters.GetActorIdList()) == 0 {
			return nil, fmt.Errorf("can't query with Custom Field Filters without responderId or actorId")
		}
		filtersOnTicket := filters.GetCustomFieldDbFilters()
		for _, filter := range filtersOnTicket {
			switch filter.GetFilterKey() {
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_LOAN_VENDOR:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterLoanVendor())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_LOAN_PROGRAM:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterLoanProgram())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION1:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterDisposition1())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION2:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterDisposition2())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION3:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterDisposition3())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DROP_OFF_STAGE:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterDropoffStage())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_ACQUISITION_CHANNEL:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterAcquisitionChannel())
			}
		}
	}
	return query, nil
}

func (s *SupportTicketDao) GetByIdWithLock(ctx context.Context, id int64) (*ticketPb.TicketDetails, error) {
	defer metric_util.TrackDuration("cx/ticket/dao", "SupportTicketDao", "GetByIdWithLock", time.Now())
	if id == 0 {
		return nil, fmt.Errorf("ticket id is mandatory for getting ticket record")
	}

	db, ok := gormctxv2.FromContextWithLock(ctx)
	if !ok {
		return nil, fmt.Errorf("lock cannot be acquired, no ongoing txn")
	}

	ticketModel := &model.SupportTicket{}
	if err := db.Where("id = ?", id).First(ticketModel).Error; err != nil {
		if errors.Is(err, gormV2.ErrRecordNotFound) {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "failed to fetch ticket record for id")
	}
	return ticketModel.ToProtoMessage(), nil
}

func (s *SupportTicketDao) GetTicketsByUpdatedAt(ctx context.Context, filters *ticketPb.TicketFilters, pageToken *pagination.PageToken,
	limit uint32) ([]*ticketPb.TicketDetails, *rpc2.PageContextResponse, error) {
	defer metric_util.TrackDuration("cx/ticket/dao", "SupportTicketDao", "GetTicketsByUpdatedAt", time.Now())

	if limit < 1 {
		return nil, nil, errors.New("page size should be at least 1")
	}

	db := gormctxv2.FromContextOrDefault(ctx, s.DB)
	var ticketList []*model.SupportTicket
	query, err := s.getQueryForCSATFilters(db, filters)
	if err != nil {
		return nil, nil, errors.Wrap(err, "error while getting query for filters")
	}

	// Use pagination helper to add pagination on ticket_updated_at column
	query, err = pagination.AddPaginationOnGivenColumns(query, pageToken, limit, "support_tickets", "ticket_updated_at", "id")
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to add pagination")
	}

	if err := query.Find(&ticketList).Error; err != nil {
		return nil, nil, errors.Wrap(err, "cannot get ticket records")
	}

	if len(ticketList) == 0 {
		return nil, nil, epifierrors.ErrRecordNotFound
	}

	rows, pageCtxResp, err := pagination.NewPageCtxResp(pageToken, int(limit), model.TicketDetailsList(ticketList))
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to create page context response")
	}

	ticketList = rows.(model.TicketDetailsList)
	// Convert to proto messages
	var ticketProtoList []*ticketPb.TicketDetails
	for _, ticketModel := range ticketList {
		ticketProtoList = append(ticketProtoList, ticketModel.ToProtoMessage())
	}
	return ticketProtoList, pageCtxResp, nil
}

//nolint:dupl
func (s *SupportTicketDao) getQueryForCSATFilters(db *gormV2.DB, filters *ticketPb.TicketFilters) (*gormV2.DB, error) {
	query := db
	if len(filters.GetStatusList()) != 0 {
		query = query.Where("status IN (?)", filters.GetStatusList())
	}
	if len(filters.GetSourceList()) != 0 {
		query = query.Where("source IN (?)", filters.GetSourceList())
	}
	if len(filters.GetProductCategoryList()) != 0 {
		query = query.Where("product_category IN (?)", filters.GetProductCategoryList())
	}
	if len(filters.GetProductCategoryDetailsList()) != 0 {
		query = query.Where("product_category_details IN (?)", filters.GetProductCategoryDetailsList())
	}
	// if identifier value list is populated than identifier_type and  identifier_value filters will not be honoured
	if filters.GetIdentifierType() != ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_UNSPECIFIED && len(filters.GetIdentifierValueList()) == 0 {
		query = query.Where("identifier_type = ?", filters.GetIdentifierType())
	}
	if filters.GetIdentifierValue() != "" && len(filters.GetIdentifierValueList()) == 0 {
		query = query.Where("identifier_value = ?", filters.GetIdentifierValue())
	}
	// from time is updated_time
	if filters.GetFromTime() != nil {
		query = query.Where("ticket_updated_at >= ?", filters.GetFromTime().AsTime())
	}
	if filters.GetToTime() != nil {
		query = query.Where("ticket_updated_at <= ?", filters.GetToTime().AsTime())
	}
	if len(filters.GetIdentifierValueList()) != 0 {
		query = query.Where("identifier_value IN (?)", filters.GetIdentifierValueList())
	}
	if len(filters.GetAgentGroupList()) != 0 {
		query = query.Where("agent_group IN (?)", filters.GetAgentGroupList())
	}
	if filters.GetActiveAfterTime() != nil {
		query = query.Where(db.Where("ticket_updated_at >= ?", filters.GetActiveAfterTime().AsTime()).Or(db.Not("status IN (?)", ticket_states.GetClosedTicketStates())))
	}
	if len(filters.GetActorIdList()) != 0 {
		query = query.Where("actor_id IN (?)", filters.GetActorIdList())
	}
	if filters.GetIssueCategoryId() != "" {
		query = query.Where("issue_category_id = ?", filters.GetIssueCategoryId())
	}
	if filters.GetResponderId() != 0 {
		query = query.Where("responder_id = ?", filters.GetResponderId())
	}
	if len(filters.GetCustomFieldDbFilters()) > 0 {
		if filters.GetResponderId() == 0 && len(filters.GetActorIdList()) == 0 {
			return nil, fmt.Errorf("can't query with Custom Field Filters without responderId or actorId")
		}
		filtersOnTicket := filters.GetCustomFieldDbFilters()
		for _, filter := range filtersOnTicket {
			switch filter.GetFilterKey() {
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_LOAN_VENDOR:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterLoanVendor())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_LOAN_PROGRAM:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterLoanProgram())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION1:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterDisposition1())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION2:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterDisposition2())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION3:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterDisposition3())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DROP_OFF_STAGE:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterDropoffStage())
			case ticketPb.CustomFieldFilter_FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_ACQUISITION_CHANNEL:
				query = query.Where(CustomFieldFilterKeyToColumnMap[filter.GetFilterKey()]+" = ?", filter.GetTicketFiltersCustomFieldFilterAcquisitionChannel())
			}
		}
	}
	return query, nil
}
