//go:generate dao_metrics_gen .
//go:generate mockgen -source=dao.go -destination=../../test/mocks/ticket/dao/mock_dao.go

package dao

import (
	"context"

	rpc2 "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/pagination"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
)

type ISupportTicketDao interface {
	// Create will create a new record for ticket
	// will return epifierrors.ErrDuplicateEntry error if record already exists in db with given id
	// will return non-nil error for other unknown errors
	Create(ctx context.Context, req *ticketPb.TicketDetails) (*ticketPb.TicketDetails, error)
	// Update if record already exists with given ticket id will update all the columns passed in field mask
	// will return epifierrors.ErrRecordNotFound record doesn't exists
	// will return non-nil error for other unknown errors
	Update(ctx context.Context, req *ticketPb.TicketDetails, updateMask []ticketPb.SupportTicketFieldMask) (*ticketPb.TicketDetails, error)
	// GetById get ticket details by id
	// will return epifierrors.ErrRecordNotFound error if record doesn't exists
	// will return non-nil error for other unknown errors
	GetById(ctx context.Context, id int64) (*ticketPb.TicketDetails, error)
	// GetByIdWithLock will get ticket details by id with write lock on the row
	// will return epifierrors.ErrRecordNotFound error if record doesn't exists
	// will return non-nil error for other unknown errors
	GetByIdWithLock(ctx context.Context, id int64) (*ticketPb.TicketDetails, error)
	// GetAllTickets will get all tickets with given filters applied
	// will return empty list if no ticket are found with given filters
	// will return non-nil error for other unknown errors
	GetAllTickets(ctx context.Context, filters *ticketPb.TicketFilters, pageToken *PageToken, limit int) ([]*ticketPb.TicketDetails, error)
	// GetTicketCount will get count of all tickets with given filters applied
	// will return count as 0 if no ticket are found with given filters
	// will return non-nil error for other unknown errors
	GetTicketCount(ctx context.Context, filters *ticketPb.TicketFilters) (int64, error)
	// GetTicketsByUpdatedAt fetches tickets ordered by ticket_updated_at in descending order
	// will return epifierrors.ErrRecordNotFound if no tickets found
	// will return non-nil error for other unknown errors
	GetTicketsByUpdatedAt(ctx context.Context, filters *ticketPb.TicketFilters, pageToken *pagination.PageToken, limit uint32) ([]*ticketPb.TicketDetails, *rpc2.PageContextResponse, error)
}

type IBulkTicketJobDao interface {
	// Create will create the job entry in db
	Create(ctx context.Context, job *ticketPb.BulkTicketJobDetails) (*ticketPb.BulkTicketJobDetails, error)
	// Get will return the job entry from db for a given id
	// will return epifierrors.ErrRecordNotFound if no job is found with given id
	Get(ctx context.Context, jobId int64) (*ticketPb.BulkTicketJobDetails, error)
	// GetAll will return list of jobs satifsying the given filters and belonging to current page
	GetAll(ctx context.Context, filters *ticketPb.BulkTicketJobFilters, pageToken *PageToken, limit int) ([]*ticketPb.BulkTicketJobDetails, error)
	// UpdateTicketProcessingSuccess this will increment the processed and success count in the job
	// we will fetch the current count and increment it inside a transaction to avoid concurrent update issues
	UpdateTicketProcessingSuccess(ctx context.Context, jobId int64) error
	// UpdateTicketProcessingFailure this will increment the processed and failure count in the job
	// we will fetch the current count and increment it inside a transaction to avoid concurrent update issues
	// will also create a failure log entry in the db for the given ticket
	UpdateTicketProcessingFailure(ctx context.Context, jobId int64) error
	// KillJob this will update the is_killed flag in the job and
	// all the events for this job will be ignored by consumer if this job is killed
	KillJob(ctx context.Context, jobId int64) error
}

type ITicketFailureLogDao interface {
	// AddTicketFailureLog will add the failure log in the db
	AddTicketFailureLog(ctx context.Context, failureLog *ticketPb.TicketFailureLog) (*ticketPb.TicketFailureLog, error)
	// GetAllFailureLogsForJob will return list of all failure logs for the given job id
	GetAllFailureLogsForJob(ctx context.Context, jobId int64) ([]*ticketPb.TicketFailureLog, error)
}

type ITicketDetailsTransformationDao interface {
	// CreateBatch will create the transformation entry in db
	// In case of conflict we overwrite the concerned row. (this is to make it easy to update the required fields in csv and sync with the db)
	CreateBatch(ctx context.Context, transformations []*ticketPb.TicketDetailsTransformation) ([]*ticketPb.TicketDetailsTransformation, error)
	// GetAllByFilters will return list of transformation from db for given filters
	// will return epifierrors.ErrRecordNotFound if no transformation is found with given filters
	GetAllByFilters(ctx context.Context, filters ...storageV2.FilterOption) ([]*ticketPb.TicketDetailsTransformation, error)
	// Update updates a single record with given id. Will update all the columns passed in field mask
	// will return epifierrors.ErrRecordNotFound if record doesn't exist
	Update(ctx context.Context, req *ticketPb.TicketDetailsTransformation, updateMask []ticketPb.TicketDetailsTransformationFieldMask) error
	// DeleteBatch deletes records for the given filters.
	// will return epifierrors.ErrRecordNotFound if records do not exist
	DeleteBatch(ctx context.Context, filters ...storageV2.FilterOption) (int64, error)
}

type IInAppCsatResponseDao interface {
	// Create method will create the in-app csat response record in db
	Create(ctx context.Context, response *ticketPb.InAppCsatResponse) (*ticketPb.InAppCsatResponse, error)
	// GetInAppCsatResponses fetches in app csat responses based on the provided filter options
	// applicable filter options : in app cast id, actor id and ticket
	GetInAppCsatResponses(ctx context.Context, filterOptions []storageV2.FilterOption) ([]*ticketPb.InAppCsatResponse, error)
}
