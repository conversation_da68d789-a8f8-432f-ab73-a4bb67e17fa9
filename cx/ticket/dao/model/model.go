package model

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/pagination"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"time"

	"github.com/epifi/be-common/pkg/nulltypes"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
)

type SupportTicket struct {
	Id                     nulltypes.NullInt64
	Status                 ticketPb.Status
	Source                 ticketPb.Source
	ProductCategory        ticketPb.ProductCategory
	ProductCategoryDetails string
	IdentifierType         ticketPb.UserIdentifierType
	IdentifierValue        nulltypes.NullString
	Ticket                 *ticketPb.Ticket
	Requester              *ticketPb.Requester
	TicketCreatedAt        nulltypes.NullTime
	TicketUpdatedAt        nulltypes.NullTime
	TicketMeta             *ticketPb.TicketMeta
	Vendor                 commonvgpb.Vendor
	CreatedAt              time.Time
	UpdatedAt              time.Time
	RawTicket              nulltypes.NullString
	AgentGroup             ticketPb.Group
	ActorId                string
	ExpectedResolutionTime nulltypes.NullTime
	IssueCategoryId        nulltypes.NullString
	ContactSummaryDetails  *ticketPb.ContactSummaryDetails
	ResponderId            int64
}

type BulkTicketJob struct {
	// using null types for certain columns to avoid adding go default values for the type and enforce not null db constraint
	Id                    nulltypes.NullInt64
	Status                ticketPb.JobStatus
	InputTicketCount      nulltypes.NullInt64
	ProcessedTicketCount  int
	SuccessfulTicketCount int
	FailedTicketCount     int
	IsKilled              commontypes.BooleanEnum
	StartedByEmail        nulltypes.NullString
	CheckerEmail          nulltypes.NullString
	Description           nulltypes.NullString
	CreatedAt             time.Time
	UpdatedAt             time.Time
}

type TicketFailureLog struct {
	// using null types for certain columns to avoid adding go default values for the type and enforce not null db constraint
	JobId         nulltypes.NullInt64
	TicketId      nulltypes.NullInt64
	FailureReason string
	CreatedAt     time.Time
	UpdatedAt     time.Time
}

type TicketDetailsTransformation struct {
	Id              string `gorm:"primary_key;type:uuid;default:gen_random_uuid()"`
	ProductCategory ticketPb.ProductCategory
	// using string instead of nulltypes to allow comparison on empty string for enforcing unique constraint
	ProductCategoryDetails string
	Subcategory            string
	TransformationType     ticketPb.TicketTransformationType
	TransformationValue    *ticketPb.TicketDetailsTransformation_TransformationValue
	CreatedAt              time.Time
	UpdatedAt              time.Time
}

func (s *SupportTicket) ToProtoMessage() *ticketPb.TicketDetails {
	if s == nil {
		return nil
	}
	return &ticketPb.TicketDetails{
		Id:                     s.Id.GetValue(),
		Status:                 s.Status,
		Source:                 s.Source,
		ProductCategory:        s.ProductCategory,
		ProductCategoryDetails: s.ProductCategoryDetails,
		IdentifierType:         s.IdentifierType,
		IdentifierValue:        s.IdentifierValue.GetValue(),
		Ticket:                 s.Ticket,
		Requester:              s.Requester,
		TicketCreatedAt:        timestampPb.New(s.TicketCreatedAt.GetValue()),
		TicketUpdatedAt:        timestampPb.New(s.TicketUpdatedAt.GetValue()),
		CreatedAt:              timestampPb.New(s.CreatedAt),
		UpdatedAt:              timestampPb.New(s.UpdatedAt),
		TicketMeta:             s.TicketMeta,
		Vendor:                 s.Vendor,
		RawTicket:              s.RawTicket.GetValue(),
		AgentGroup:             s.AgentGroup,
		ActorId:                s.ActorId,
		ExpectedResolutionTime: timestampPb.New(s.ExpectedResolutionTime.GetValue()),
		IssueCategoryId:        s.IssueCategoryId.GetValue(),
		ContactSummaryDetails:  s.ContactSummaryDetails,
		ResponderId:            s.ResponderId,
	}
}

func FromProtoMessage(t *ticketPb.TicketDetails) *SupportTicket {
	if t == nil {
		return nil
	}
	st := &SupportTicket{
		Status:                 t.GetStatus(),
		Source:                 t.GetSource(),
		ProductCategory:        t.GetProductCategory(),
		ProductCategoryDetails: t.GetProductCategoryDetails(),
		IdentifierType:         t.GetIdentifierType(),
		IdentifierValue:        nulltypes.NewNullString(t.GetIdentifierValue()),
		Ticket:                 t.GetTicket(),
		Requester:              t.GetRequester(),
		TicketCreatedAt:        nulltypes.NewNullTime(t.GetTicketCreatedAt().AsTime()),
		TicketUpdatedAt:        nulltypes.NewNullTime(t.GetTicketUpdatedAt().AsTime()),
		TicketMeta:             t.GetTicketMeta(),
		Vendor:                 t.GetVendor(),
		RawTicket:              nulltypes.NewNullString(t.GetRawTicket()),
		AgentGroup:             t.GetAgentGroup(),
		ActorId:                t.GetActorId(),
		ExpectedResolutionTime: nulltypes.NewNullTime(t.GetExpectedResolutionTime().AsTime()),
		IssueCategoryId:        nulltypes.NewNullString(t.GetIssueCategoryId()),
		ContactSummaryDetails:  t.GetContactSummaryDetails(),
		ResponderId:            t.GetResponderId(),
	}
	if t.Id != 0 {
		st.Id = nulltypes.NewNullInt64(t.Id)
	}
	return st
}

func (b *BulkTicketJob) ToProtoMessage() *ticketPb.BulkTicketJobDetails {
	return &ticketPb.BulkTicketJobDetails{
		JobId:                 b.Id.GetValue(),
		JobStatus:             b.Status,
		InputTicketCount:      b.InputTicketCount.GetValue(),
		ProcessedTicketCount:  int64(b.ProcessedTicketCount),
		SuccessfulTicketCount: int64(b.SuccessfulTicketCount),
		FailedTicketCount:     int64(b.FailedTicketCount),
		IsKilled:              b.IsKilled,
		CreatedAt:             timestampPb.New(b.CreatedAt),
		UpdatedAt:             timestampPb.New(b.UpdatedAt),
		StartedByEmail:        b.StartedByEmail.GetValue(),
		CheckerEmail:          b.CheckerEmail.GetValue(),
		Description:           b.Description.GetValue(),
	}
}

func FromBulkTicketJobProtoMessage(b *ticketPb.BulkTicketJobDetails) *BulkTicketJob {
	details := &BulkTicketJob{
		Status:                b.GetJobStatus(),
		InputTicketCount:      nulltypes.NewNullInt64(b.GetInputTicketCount()),
		ProcessedTicketCount:  int(b.GetProcessedTicketCount()),
		SuccessfulTicketCount: int(b.GetSuccessfulTicketCount()),
		FailedTicketCount:     int(b.GetFailedTicketCount()),
		IsKilled:              b.IsKilled,
		StartedByEmail:        nulltypes.NewNullString(b.StartedByEmail),
		CheckerEmail:          nulltypes.NewNullString(b.CheckerEmail),
		Description:           nulltypes.NewNullString(b.Description),
	}
	if b.GetJobId() != 0 {
		details.Id = nulltypes.NewNullInt64(b.GetJobId())
	}
	return details
}

func (t *TicketFailureLog) ToProtoMessage() *ticketPb.TicketFailureLog {
	return &ticketPb.TicketFailureLog{
		JobId:         t.JobId.GetValue(),
		TicketId:      t.TicketId.GetValue(),
		FailureReason: t.FailureReason,
		CreatedAt:     timestampPb.New(t.CreatedAt),
		UpdatedAt:     timestampPb.New(t.UpdatedAt),
	}
}

func FromTicketFailureLogProtoMessage(t *ticketPb.TicketFailureLog) *TicketFailureLog {
	return &TicketFailureLog{
		JobId:         nulltypes.NewNullInt64(t.GetJobId()),
		TicketId:      nulltypes.NewNullInt64(t.GetTicketId()),
		FailureReason: t.FailureReason,
	}
}

func (t *TicketDetailsTransformation) ToProtoMessage() *ticketPb.TicketDetailsTransformation {
	return &ticketPb.TicketDetailsTransformation{
		Id:                     t.Id,
		ProductCategory:        t.ProductCategory,
		ProductCategoryDetails: t.ProductCategoryDetails,
		Subcategory:            t.Subcategory,
		TransformationType:     t.TransformationType,
		TransformationValue:    t.TransformationValue,
		CreatedAt:              timestampPb.New(t.CreatedAt),
		UpdatedAt:              timestampPb.New(t.UpdatedAt),
	}
}

func ToTicketDetailsTransformationProtoMessageList(modelsList []*TicketDetailsTransformation) []*ticketPb.TicketDetailsTransformation {
	var transformationMessagesList []*ticketPb.TicketDetailsTransformation
	for _, model := range modelsList {
		transformationMessagesList = append(transformationMessagesList, model.ToProtoMessage())
	}
	return transformationMessagesList
}

func FromTicketDetailsTransformationsMessage(t *ticketPb.TicketDetailsTransformation) *TicketDetailsTransformation {
	return &TicketDetailsTransformation{
		Id:                     t.GetId(),
		ProductCategory:        t.GetProductCategory(),
		ProductCategoryDetails: t.GetProductCategoryDetails(),
		Subcategory:            t.GetSubcategory(),
		TransformationType:     t.GetTransformationType(),
		TransformationValue:    t.GetTransformationValue(),
	}
}

func FromTicketDetailsTransformationsMessageList(messageList []*ticketPb.TicketDetailsTransformation) []*TicketDetailsTransformation {
	var transformationModelsList []*TicketDetailsTransformation
	for _, message := range messageList {
		transformationModelsList = append(transformationModelsList, FromTicketDetailsTransformationsMessage(message))
	}
	return transformationModelsList
}

type InAppCsatResponse struct {
	// record id
	Id string `gorm:"type:uuid;default:uuid_generate_v4();primaryKey"`
	// actor id of the user who responded to the CSAT question
	ActorId string
	// id of the ticket for which the CSAT response was given
	TicketId int64
	// id of the question which was responded from feedback_questions table in inapphelp db
	QuestionId string
	// id of the attempt of the user, which is also stored in feedback_question_responses table in inapphelp db
	AttemptId string
	// integer denoting the CSAT score responded by the user
	CsatScore        int64
	CreatedAt        time.Time
	UpdatedAt        time.Time
	FeedbackComments nulltypes.NullString
}

func (i *InAppCsatResponse) ToProtoMessage() *ticketPb.InAppCsatResponse {
	return &ticketPb.InAppCsatResponse{
		Id:               i.Id,
		ActorId:          i.ActorId,
		TicketId:         i.TicketId,
		QuestionId:       i.QuestionId,
		AttemptId:        i.AttemptId,
		CsatScore:        i.CsatScore,
		CreatedAt:        timestampPb.New(i.CreatedAt),
		UpdatedAt:        timestampPb.New(i.UpdatedAt),
		FeedbackComments: i.FeedbackComments.GetValue(),
	}
}

func FromInAppCsatResponseProtoMsg(msg *ticketPb.InAppCsatResponse) *InAppCsatResponse {
	return &InAppCsatResponse{
		ActorId:          msg.GetActorId(),
		TicketId:         msg.GetTicketId(),
		QuestionId:       msg.GetQuestionId(),
		AttemptId:        msg.GetAttemptId(),
		CsatScore:        msg.GetCsatScore(),
		FeedbackComments: nulltypes.NewNullString(msg.GetFeedbackComments()),
	}
}

// TicketDetailsList implements pagination.Rows to use pagination on support_tickets table
type TicketDetailsList []*SupportTicket

func (s TicketDetailsList) Slice(start, end int) pagination.Rows { return s[start:end] }
func (s TicketDetailsList) GetTimestamp(index int) time.Time     { return s[index].TicketUpdatedAt.Time }
func (s TicketDetailsList) Size() int                            { return len(s) }
