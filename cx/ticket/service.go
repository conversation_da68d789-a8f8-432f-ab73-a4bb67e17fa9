// nolint: goimports
package ticket

import (
	"context"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/pagination"

	commsPb "github.com/epifi/gamma/api/comms"
	irPb "github.com/epifi/gamma/api/inapphelp/issue_reporting"
	usersPb "github.com/epifi/gamma/api/user"
	dao3 "github.com/epifi/gamma/cx/issue_config/dao"
	"github.com/epifi/gamma/cx/ticket/csat"
	"github.com/epifi/gamma/cx/ticket/csat/token_manager"
	"github.com/epifi/gamma/inapphelp/issue_reporting/dao"
	"github.com/epifi/gamma/pkg/feature/release"

	"github.com/samber/lo"

	"google.golang.org/protobuf/encoding/protojson"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/be-common/pkg/epifigrpc"

	alPb "github.com/epifi/gamma/api/cx/audit_log"
	callPb "github.com/epifi/gamma/api/cx/call"
	"github.com/epifi/gamma/api/cx/consumer"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	freshdeskPb "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/cx/audit_log"
	"github.com/epifi/gamma/cx/config"
	cxConfig "github.com/epifi/gamma/cx/config"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	helper3 "github.com/epifi/gamma/cx/dispute/helper"
	events2 "github.com/epifi/gamma/cx/events"
	helper2 "github.com/epifi/gamma/cx/helper"
	"github.com/epifi/gamma/cx/interceptor"
	"github.com/epifi/gamma/cx/issue_category/manager"
	cxLogger "github.com/epifi/gamma/cx/logger"
	"github.com/epifi/gamma/cx/metrics"
	"github.com/epifi/gamma/cx/sherlock_auth"
	"github.com/epifi/gamma/cx/sherlock_user/sherlock_user_wrapper"
	dao2 "github.com/epifi/gamma/cx/ticket/dao"
	"github.com/epifi/gamma/cx/ticket/dao/validations"
	"github.com/epifi/gamma/cx/ticket/helper"
	"github.com/epifi/gamma/cx/ticket/processor"
	"github.com/epifi/gamma/cx/ticket/ticket_states"
	cxTypes "github.com/epifi/gamma/cx/wire/types"
	mapUtils "github.com/epifi/gamma/pkg/map"
)

type Service struct {
	ticketPb.UnimplementedTicketServer
	fdClient                       freshdeskPb.FreshdeskClient
	s3RecordingClient              S3RecordingClient
	s3TranscriptClient             S3TranscriptClient
	authFunc                       interceptor.AuthFunction
	accessControlFunc              interceptor.AccessControlFunction
	casbinAuthorizationSvc         *sherlock_auth.CasbinAuthorizationService
	cognitoSvc                     *sherlock_auth.CognitoAuthService
	auditLogSvc                    *audit_log.Service
	cxDesc                         interceptor.GetCxDescriptor
	supportTicketDao               dao2.ISupportTicketDao
	sherlockUserDao                sherlock_user_wrapper.ISherlockUser
	bulkTicketJobDao               dao2.IBulkTicketJobDao
	ticketFailureLogDao            dao2.ITicketFailureLogDao
	updateTicketPublisher          cxTypes.UpdateTicketPublisher
	createTicketPublisher          cxTypes.CreateTicketPublisher
	bulkTicketJobConfig            *config.BulkTicketJobConfig
	ticketConfig                   *cxGenConf.TicketConfig
	attachEntityFactory            processor.IAttachEntityFactory
	ticketHelper                   helper.ITicketHelper
	customerIdentifierHelper       helper2.ICustomerIdentifier
	ticketDetailsTransformationDao dao2.ITicketDetailsTransformationDao
	cxConf                         *cxConfig.Config
	cacheStorage                   cache.CacheStorage
	issueCategoryManager           manager.IssueCategoryManager
	disputeHelper                  helper3.IDisputeHelper
	eventBroker                    events.Broker
	userQueryLogDao                dao.UserQueryLogDao
	issueConfigDao                 dao3.IssueConfigDao
	commsClient                    commsPb.CommsClient
	userClient                     usersPb.UsersClient
	ticketClient                   ticketPb.TicketClient
	releaseEvaluator               release.IEvaluator
	csatEvaluator                  csat.Evaluator
	csatTokenManager               token_manager.TokenManager
	inAppCsatDao                   dao2.IInAppCsatResponseDao
}

// have defined separate interfaces here because we can't use same type for 2 providers in wire
type S3RecordingClient interface {
	Write(ctx context.Context, dst string, content []byte, aclString string) error
	Read(ctx context.Context, file string) ([]byte, error)
}

type S3TranscriptClient interface {
	Write(ctx context.Context, dst string, content []byte, aclString string) error
	Read(ctx context.Context, file string) ([]byte, error)
}

const (
	CreatedByKey                          = "CREATED BY"
	CreationModeKey                       = "CREATION MODE"
	AutoIdTag                             = "Auto ID"
	reportedViaAppTag                     = "reported from app"
	maxTicketListLengthToSupportCsatCheck = 5
)

var (
	ErrContactNotFound                            = errors.New("contact not found")
	ValidIdentifierTypeForTicketFiltersEnrichment = map[ticketPb.UserIdentifierType]bool{
		ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_PHONE_NUMBER: true,
	}
	productCategoryForTxnRelatedTickets = []ticketPb.ProductCategory{
		ticketPb.ProductCategory_PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS,
		ticketPb.ProductCategory_PRODUCT_CATEGORY_TRANSACTION,
		ticketPb.ProductCategory_PRODUCT_CATEGORY_DEBIT_CARD,
		ticketPb.ProductCategory_PRODUCT_CATEGORY_ON_APP_TRANSACTIONS,
	}
)

func NewTicketService(fdClient freshdeskPb.FreshdeskClient, s3RecordingClient S3RecordingClient,
	s3TranscriptionClient S3TranscriptClient, authFunc interceptor.AuthFunction,
	accessControlFunc interceptor.AccessControlFunction, cxDesc interceptor.GetCxDescriptor,
	casbinAuthorizationSvc *sherlock_auth.CasbinAuthorizationService, cognitoSvc *sherlock_auth.CognitoAuthService,
	auditLogSvc *audit_log.Service, supportTicketDao dao2.ISupportTicketDao,
	sherlockUserDao sherlock_user_wrapper.ISherlockUser, bulkTicketJobDao dao2.IBulkTicketJobDao,
	ticketFailureLogDao dao2.ITicketFailureLogDao, updateTicketPublisher cxTypes.UpdateTicketPublisher, createTicketPublisher cxTypes.CreateTicketPublisher,
	bulkTicketJobConfig *config.BulkTicketJobConfig, ticketConfig *cxGenConf.TicketConfig,
	attachEntityFactory processor.IAttachEntityFactory, ticketHelper helper.ITicketHelper, customerIdentifierHelper helper2.ICustomerIdentifier,
	ticketDetailsTransformationDao dao2.ITicketDetailsTransformationDao, cxConf *cxConfig.Config, cacheStorage cache.CacheStorage,
	issueCategoryManager manager.IssueCategoryManager, disputeHelper helper3.IDisputeHelper, eventBroker events.Broker,
	userQueryLogDao dao.UserQueryLogDao, issueConfigDao dao3.IssueConfigDao, commsClient commsPb.CommsClient,
	userClient usersPb.UsersClient, ticketClient ticketPb.TicketClient, releaseEvaluator release.IEvaluator,
	csatEvaluator csat.Evaluator, csatTokenManager token_manager.TokenManager, inAppCsatDao dao2.IInAppCsatResponseDao) *Service {
	return &Service{
		fdClient:                       fdClient,
		s3RecordingClient:              s3RecordingClient,
		s3TranscriptClient:             s3TranscriptionClient,
		auditLogSvc:                    auditLogSvc,
		accessControlFunc:              accessControlFunc,
		authFunc:                       authFunc,
		cxDesc:                         cxDesc,
		cognitoSvc:                     cognitoSvc,
		casbinAuthorizationSvc:         casbinAuthorizationSvc,
		supportTicketDao:               supportTicketDao,
		sherlockUserDao:                sherlockUserDao,
		bulkTicketJobDao:               bulkTicketJobDao,
		ticketFailureLogDao:            ticketFailureLogDao,
		updateTicketPublisher:          updateTicketPublisher,
		createTicketPublisher:          createTicketPublisher,
		bulkTicketJobConfig:            bulkTicketJobConfig,
		ticketConfig:                   ticketConfig,
		attachEntityFactory:            attachEntityFactory,
		ticketHelper:                   ticketHelper,
		customerIdentifierHelper:       customerIdentifierHelper,
		ticketDetailsTransformationDao: ticketDetailsTransformationDao,
		cxConf:                         cxConf,
		cacheStorage:                   cacheStorage,
		issueCategoryManager:           issueCategoryManager,
		disputeHelper:                  disputeHelper,
		eventBroker:                    eventBroker,
		userQueryLogDao:                userQueryLogDao,
		issueConfigDao:                 issueConfigDao,
		commsClient:                    commsClient,
		userClient:                     userClient,
		ticketClient:                   ticketClient,
		releaseEvaluator:               releaseEvaluator,
		csatEvaluator:                  csatEvaluator,
		csatTokenManager:               csatTokenManager,
		inAppCsatDao:                   inAppCsatDao,
	}
}

const (
	recordingMethodName  = "/cx.ticket.ticket/GetCallRecording"
	transcriptMethodName = "/cx.ticket.ticket/GetCallTranscript"
	// 90 days
	MaxAllowedDurationForFetchingTicket = time.Hour * 24 * 90
	DefaultPageSize                     = 30
	DefaultPageSizeForApp               = 10
)

var _ ticketPb.TicketServer = &Service{}

func (s *Service) AttachEntity(ctx context.Context, req *ticketPb.AttachEntityRequest) (*ticketPb.AttachEntityResponse, error) {
	if req.GetEntityType() == ticketPb.EntityType_ENTITY_TYPE_UNSPECIFIED || req.GetHeader().GetTicket() == nil {
		cxLogger.Error(ctx, "invalid request parameters for attach entity rpc",
			zap.String("entity id", req.GetEntityId()), zap.String("entity type", req.GetEntityType().String()))
		return &ticketPb.AttachEntityResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
	attachEntityMetaList, err := s.getAttachEntityMeta(req)
	if err != nil {
		cxLogger.Error(ctx, "invalid attach entity meta value for attach entity rpc", zap.Error(err), zap.String("entity type", req.GetEntityType().String()))
		return &ticketPb.AttachEntityResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
	// entity type is not passed by client for recent activity
	// hence, if parsed entity meta is of type sherlock actor activity manually update entity type
	if len(attachEntityMetaList) > 0 && attachEntityMetaList[0].GetSherlockActorActivityV2Meta() != nil {
		req.EntityType = ticketPb.EntityType_ACTOR_ACTIVITY
	}
	updatedTicket, err := s.AttachEntityViaMeta(ctx, req.GetEntityType(), req.GetHeader().GetTicket().GetId(), attachEntityMetaList)
	if err != nil {
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			return &ticketPb.AttachEntityResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("attach entity meta list cannot be empty or entity passed does not match with attach entity meta"),
			}, nil
		}
		cxLogger.Error(ctx, "error while fetching updated ticket for attach entity flow", zap.Error(err), zap.Any("entityType", req.GetEntityType()))
		return &ticketPb.AttachEntityResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	updateResp, updateErr := s.fdClient.UpdateTicket(ctx, &freshdeskPb.UpdateTicketRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FRESHDESK},
		Ticket: updatedTicket,
	})
	if te := epifigrpc.RPCError(updateResp, updateErr); te != nil {
		cxLogger.Error(ctx, "failed to update ticket in AttachEntity flow", zap.Error(te), zap.Any("entityType", req.GetEntityType()))
		return &ticketPb.AttachEntityResponse{Status: rpcPb.StatusInternalWithDebugMsg("failed to update ticket")}, err
	}

	return &ticketPb.AttachEntityResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *Service) getAttachEntityMetaV1(req *ticketPb.AttachEntityRequest) []*ticketPb.AttachEntityMeta {
	return req.GetAttachEntityMetaList()
}

func (s *Service) getAttachEntityMetaV2(req *ticketPb.AttachEntityRequest) ([]*ticketPb.AttachEntityMeta, error) {
	var attachEntityMetaList []*ticketPb.AttachEntityMeta
	for _, entityMetaString := range req.GetAttachEntityMetaListV2() {
		var entityMeta ticketPb.AttachEntityMeta
		err := protojson.Unmarshal([]byte(entityMetaString), &entityMeta)
		if err != nil {
			return nil, errors.Wrap(err, "failed to unmarshal attach entity values")
		}
		attachEntityMetaList = append(attachEntityMetaList, &entityMeta)
	}
	return attachEntityMetaList, nil
}

func (s *Service) getAttachEntityMeta(req *ticketPb.AttachEntityRequest) ([]*ticketPb.AttachEntityMeta, error) {
	if len(req.GetAttachEntityMetaListV2()) != 0 {
		attachEntityMetaList, attachEntityErr := s.getAttachEntityMetaV2(req)
		if attachEntityErr != nil {
			return nil, errors.Wrap(attachEntityErr, "failed to fetch attach entity meta")
		}
		return attachEntityMetaList, nil
	} else {
		return s.getAttachEntityMetaV1(req), nil
	}
}
func (s *Service) AttachEntityViaMeta(ctx context.Context, entityType ticketPb.EntityType, ticketId int64, attachEntityMetaList []*ticketPb.AttachEntityMeta) (*freshdeskPb.Ticket, error) {
	// get processor for given entity type
	attachEntityProcessor, err := s.attachEntityFactory.GetAttachEntityProcessor(ctx, entityType)
	if err != nil {
		return nil, errors.Wrap(err, "error while identifying processor for given entity type")
	}

	// validate attach entity meta list for given processor
	validationErr := attachEntityProcessor.ValidateAttachEntityMetaList(ctx, attachEntityMetaList)
	if validationErr != nil {
		if errors.Is(validationErr, processor.InvalidAttachEntityMetaTypeErr) || errors.Is(validationErr, processor.EmptyAttachEntityMetaListErr) {
			return nil, epifierrors.ErrInvalidArgument
		}
		return nil, errors.Wrap(validationErr, "error while validating attach entity meta list")
	}

	// if validation passes get the ticket from individual processor
	cxTicket, ticketErr := attachEntityProcessor.GetAttachEntityTicket(ctx, ticketId, attachEntityMetaList)
	if ticketErr != nil {
		if errors.Is(ticketErr, processor.InvalidAttachEntityMetaTypeErr) || errors.Is(ticketErr, processor.EmptyAttachEntityMetaListErr) {
			return nil, epifierrors.ErrInvalidArgument
		}
		return nil, errors.Wrap(ticketErr, "error while fetching updated ticket for attach entity")
	}

	// convert to vg freshdesk ticket
	return cxTicket.ToVgFreshdeskProto(), nil
}

func (s *Service) GetRelatedTickets(ctx context.Context, req *ticketPb.GetRelatedTicketsRequest) (*ticketPb.GetRelatedTicketsResponse, error) {
	if req.GetHeader().GetTicket() == nil {
		cxLogger.Info(ctx, "ticket details are missing in header")
		return &ticketPb.GetRelatedTicketsResponse{Status: rpcPb.StatusInternalWithDebugMsg("ticket details are missing in header")}, nil
	}
	// get requester id from ticket in header
	requesterId := req.GetHeader().GetTicket().GetRequesterId()

	// fetch all the tickets linked to same requester
	resp, err := s.fdClient.GetAllTickets(ctx, &freshdeskPb.GetAllTicketsRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FRESHDESK,
		},
		RequesterId: requesterId,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching tickets list from freshdesk", zap.Error(te))
		return &ticketPb.GetRelatedTicketsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while ticket list from freshdesk"),
		}, nil
	}
	return &ticketPb.GetRelatedTicketsResponse{
		Status:  rpcPb.StatusOk(),
		Tickets: filterClosedTickets(resp.GetTicketList()),
	}, nil
}

func filterClosedTickets(list []*freshdeskPb.Ticket) []*freshdeskPb.Ticket {
	var filteredList []*freshdeskPb.Ticket
	for _, ticket := range list {
		// filter out resolved or closed tickets
		if ticket.GetStatus() != freshdeskPb.Status_RESOLVED && ticket.GetStatus() != freshdeskPb.Status_CLOSED {
			filteredList = append(filteredList, ticket)
		}
	}
	return filteredList
}

func (s *Service) MergeTickets(ctx context.Context, req *ticketPb.MergeTicketsRequest) (*ticketPb.MergeTicketsResponse, error) {
	if req.GetPrimaryTicketId() == 0 || len(req.GetSecondaryTicketIds()) == 0 {
		cxLogger.Info(ctx, "primary ticket id or secondary ticket id missing in request")
		return &ticketPb.MergeTicketsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("primary ticket id and secondary ticket id both are mandatory"),
		}, nil
	}
	resp, err := s.fdClient.MergeTickets(ctx, &freshdeskPb.MergeTicketsRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FRESHDESK,
		},
		PrimaryTicketId:    req.GetPrimaryTicketId(),
		SecondaryTicketIds: req.GetSecondaryTicketIds(),
		PrimaryTicketNote: &freshdeskPb.MergeTicketsRequest_TicketNote{
			Body:    req.GetPrimaryTicketNote(),
			Private: true,
		},
		SecondaryTicketNote: &freshdeskPb.MergeTicketsRequest_TicketNote{
			Body:    req.GetSecondaryTicketNote(),
			Private: true,
		},
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while calling freshdesk to merge tickets", zap.Error(te))
		return &ticketPb.MergeTicketsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while calling freshdesk to merge tickets"),
		}, nil
	}
	return &ticketPb.MergeTicketsResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

//nolint:dupl,funlen
func (s *Service) GetCallRecording(req *ticketPb.GetCallRecordingRequest, respServer ticketPb.Ticket_GetCallRecordingServer) error {
	ctx := respServer.Context()
	if req.GetHeader().GetTicketId() == 0 || req.GetRecordingId() == "" {
		cxLogger.Info(ctx, "ticket id or recording id not passed in request")
		// return the video chunk received
		if err := respServer.Send(&ticketPb.GetCallRecordingResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ticket id and recording id are mandatory"),
		}); err != nil {
			return err
		}
		return nil
	}

	// check auth
	// TODO(sachin): move the auth logic to stream interceptor once we figure out how to get request params there
	err := s.CheckAuth(req, ctx, recordingMethodName)
	if err != nil {
		return err
	}

	ticketResp, err := s.fdClient.GetTicketByTicketId(ctx, &freshdeskPb.GetTicketByTicketIdRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FRESHDESK,
		},
		TicketId: req.GetHeader().GetTicketId(),
	})
	if te := epifigrpc.RPCError(ticketResp, err); te != nil {
		// return the video chunk received
		if streamErr := respServer.Send(&ticketPb.GetCallRecordingResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching ticket details"),
		}); streamErr != nil {
			return streamErr
		}
		return nil
	}

	location := getRecordingLocation(req, ticketResp.GetTicket())
	recording, err := s.s3RecordingClient.Read(ctx, location)
	if err != nil {
		cxLogger.Error(
			ctx, "Error in reading call recording", zap.Error(err), zap.String("file_location", location),
			zap.Int64(logger.TICKET_ID, req.GetHeader().GetTicketId()), zap.String("recording_id", req.GetRecordingId()),
		)
		if err := respServer.Send(
			&ticketPb.GetCallRecordingResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in reading call recording"),
			},
		); err != nil {
			return err
		}
		return nil
	}
	const packetSize = 50 * 1024 // 50KiB , Recommended size 16KiB - 64KiB
	cxLogger.Info(ctx, "Length of recording", zap.Int("len", len(recording)),
		zap.Int64(logger.TICKET_ID, req.GetHeader().GetTicketId()), zap.String("recording_id", req.GetRecordingId()))
	for i := 0; i < len(recording); i += packetSize {
		if err := respServer.Send(
			&ticketPb.GetCallRecordingResponse{
				Status: rpc.StatusOk(),
				Chunk:  recording[i:integer.Min(i+packetSize, len(recording))],
			},
		); err != nil {
			return err
		}
	}
	return nil
}

func getRecordingLocation(req *ticketPb.GetCallRecordingRequest, ticket *freshdeskPb.Ticket) string {
	return fmt.Sprintf("/Epifi/%s/%s.mp3", ticket.GetCreatedAt().AsTime().Format("2006/01/02"), req.GetRecordingId())
}

//nolint:dupl,funlen
func (s *Service) GetCallTranscript(req *ticketPb.GetCallTranscriptRequest, respServer ticketPb.Ticket_GetCallTranscriptServer) error {
	ctx := respServer.Context()
	if req.GetHeader().GetTicketId() == 0 || req.GetRecordingId() == "" {
		cxLogger.Info(ctx, "ticket id or recording id not passed in request")
		// return the video chunk received
		if err := respServer.Send(&ticketPb.GetCallTranscriptResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ticket id and recording id are mandatory"),
		}); err != nil {
			return err
		}
		return nil
	}

	// check auth
	// TODO(sachin): move the auth logic to stream interceptor once we figure out how to get request params there
	err := s.CheckAuth(req, ctx, transcriptMethodName)
	if err != nil {
		return err
	}

	ticketResp, err := s.fdClient.GetTicketByTicketId(ctx, &freshdeskPb.GetTicketByTicketIdRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FRESHDESK,
		},
		TicketId: req.GetHeader().GetTicketId(),
	})
	if te := epifigrpc.RPCError(ticketResp, err); te != nil {
		// return the video chunk received
		if streamErr := respServer.Send(&ticketPb.GetCallTranscriptResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while fetching ticket details"),
		}); streamErr != nil {
			return streamErr
		}
		return nil
	}

	location := getTranscriptLocation(req, ticketResp.GetTicket())
	recording, err := s.s3TranscriptClient.Read(ctx, location)
	if err != nil {
		cxLogger.Error(
			ctx, "error in reading call transcript", zap.Error(err), zap.String("file_location", location),
			zap.Int64(logger.TICKET_ID, req.GetHeader().GetTicketId()), zap.String("recording_id", req.GetRecordingId()),
		)
		if err := respServer.Send(
			&ticketPb.GetCallTranscriptResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in reading call transcript"),
			},
		); err != nil {
			return err
		}
		return nil
	}
	const packetSize = 50 * 1024 // 50KiB , Recommended size 16KiB - 64KiB
	cxLogger.Info(ctx, "Length of transcript", zap.Int("len", len(recording)),
		zap.Int64(logger.TICKET_ID, req.GetHeader().GetTicketId()), zap.String("recording_id", req.GetRecordingId()))
	for i := 0; i < len(recording); i += packetSize {
		if err := respServer.Send(
			&ticketPb.GetCallTranscriptResponse{
				Status: rpc.StatusOk(),
				Chunk:  recording[i:integer.Min(i+packetSize, len(recording))],
			},
		); err != nil {
			return err
		}
	}
	return nil
}

func getTranscriptLocation(req *ticketPb.GetCallTranscriptRequest, _ *freshdeskPb.Ticket) string {
	return fmt.Sprintf("%s.json", req.GetRecordingId())
}

func (s *Service) CheckAuth(req interceptor.RequestWithHeader, ctx context.Context, methodName string) error {
	header := req.GetHeader()
	ok, err := s.authFunc(ctx, header, s.cognitoSvc)
	if err != nil || !ok {
		return err
	}
	accessErr := sherlock_auth.SetOrValidateAccessLevel(ctx, header, s.sherlockUserDao)
	if accessErr != nil {
		return accessErr
	}

	// temp fix till we figure out auth using stream interceptor

	rpcOptions := s.cxDesc(methodName)
	ok, err = s.accessControlFunc(ctx, s.casbinAuthorizationSvc, methodName, header.GetAgentEmail(), header.GetAccessLevel())
	if err != nil || !ok {
		// log request with access status denied
		cxLogger.Error(ctx, "permission denied to user", zap.Error(err))
		// suppressing logging error in case of unauthorized
		logErr := interceptor.LogRequest(rpcOptions, s.auditLogSvc, ctx, req, header, alPb.AccessStatus_DENIED)
		if logErr != nil {
			cxLogger.Error(ctx, "error while logging request", zap.Error(logErr))
		}
		return err
	}
	// populate information level in header
	header.InformationLevel = rpcOptions.InformationLevel
	// log request with access status allowed
	logErr := interceptor.LogRequest(rpcOptions, s.auditLogSvc, ctx, req, header, alPb.AccessStatus_ALLOWED)

	// throw internal error if log request returns error
	if logErr != nil {
		cxLogger.Error(ctx, "error while logging request", zap.Error(logErr))
		return status.Errorf(codes.Internal, "Internal server error")
	}
	return nil
}

func (s *Service) GetSupportTickets(ctx context.Context, req *ticketPb.GetSupportTicketsRequest) (*ticketPb.GetSupportTicketsResponse, error) {
	// if ticket id is passed then just return the response
	if req.GetTicketId() != 0 {
		return s.GetSingleTicketResponse(ctx, req)
	}
	// set default values for optional fields if not passed
	s.setDefaultValues(req)
	if err := validateSupportTicketsRequest(req); err != nil {
		logger.Info(ctx, "Get Support ticket request is not valid", zap.Error(err))
		return &ticketPb.GetSupportTicketsResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	pageToken, err := s.getPageToken(req.GetPageContextRequest())
	if err != nil {
		cxLogger.Error(ctx, "unable to decode page tokens", zap.Error(err))
		return &ticketPb.GetSupportTicketsResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("unable to decode page tokens"),
		}, nil
	}

	ticketDetailList, err := s.supportTicketDao.GetAllTickets(ctx, req.GetTicketFilters(), pageToken, int(req.GetPageContextRequest().GetPageSize()))
	if err != nil {
		logger.Error(ctx, "failed to fetch ticket list from db", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &ticketPb.GetSupportTicketsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &ticketPb.GetSupportTicketsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	// apply filter on custom fields if applicable
	if isCustomFieldFilteringRequired(req.GetTicketFilters()) {
		ticketDetailList = filterTicketsByCustomField(ticketDetailList, req.GetTicketFilters().GetCustomFieldFilter())
	}

	ticketList, pageContextResponse, err := s.PaginateSupportTicketsResponse(ctx, int(req.GetPageContextRequest().GetPageSize()), ticketDetailList, pageToken)
	if err != nil {
		logger.Error(ctx, "error while paginating ticket details response", zap.Error(err))
		return &ticketPb.GetSupportTicketsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("failed to encode page token"),
		}, nil
	}

	if len(ticketList) == 0 {
		return &ticketPb.GetSupportTicketsResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	return &ticketPb.GetSupportTicketsResponse{
		Status:              rpcPb.StatusOk(),
		Tickets:             ticketList,
		PageContextResponse: pageContextResponse,
	}, nil
}

// filterTicketsByCustomField method applies custom field filter on each ticket
// returns list of tickets matching the given filter
func filterTicketsByCustomField(ticketList []*ticketPb.TicketDetails, filter *ticketPb.CustomFieldFilter) []*ticketPb.TicketDetails {
	var filteredTickets []*ticketPb.TicketDetails
	for _, ticket := range ticketList {
		if doesCustomFieldFilterMatch(filter, ticket.GetTicket().GetCustomFields()) {
			filteredTickets = append(filteredTickets, ticket)
		}
	}

	return filteredTickets
}

// doesCustomFieldFilterMatch method determines whether given custom field satisfies the required custom field filter
func doesCustomFieldFilterMatch(filter *ticketPb.CustomFieldFilter, fields *ticketPb.CustomFields) bool {
	switch filter.GetFilterKey() {
	case ticketPb.CustomFieldFilter_FILTER_KEY_TXN_ID:
		return filter.GetTxnId() == fields.GetEntityId()
	case ticketPb.CustomFieldFilter_FILTER_KEY_MANDATE_ID:
		return filter.GetMandateId() == fields.GetEntityId()
	default:
		return false
	}
}

// isCustomFieldFilteringRequired method determines whether custom field filter needs to be applied
// it checks the required precondition for applying filter and validate the custom field filter
func isCustomFieldFilteringRequired(filter *ticketPb.TicketFilters) bool {
	// check to confirm ticket is being fetched for single actor
	if len(filter.GetActorIdList()) != 1 {
		return false
	}

	// validation of custom field filter
	switch filter.GetCustomFieldFilter().GetFilterKey() {
	case ticketPb.CustomFieldFilter_FILTER_KEY_TXN_ID:
		return filter.GetCustomFieldFilter().GetTxnId() != ""
	case ticketPb.CustomFieldFilter_FILTER_KEY_MANDATE_ID:
		return filter.GetCustomFieldFilter().GetMandateId() != ""
	default:
		return false
	}
}

func (s *Service) PaginateSupportTicketsResponse(ctx context.Context, pageSize int, ticketDetailList []*ticketPb.TicketDetails, pageToken *dao2.PageToken) ([]*ticketPb.Ticket, *rpcPb.PageContextResponse, error) {
	var resultTicketList []*ticketPb.Ticket
	for i, ticketDetail := range ticketDetailList {
		if i == pageSize {
			break
		}
		s.setFieldsInTicketObject(ticketDetail)
		resultTicketList = append(resultTicketList, ticketDetail.GetTicket())
		if s.ticketConfig.IsTicketListLoggingEnabled() {
			cxLogger.Info(ctx, "actor id in PaginateSupportTicketsResponse while appending", zap.Int64(logger.TICKET_ID, ticketDetail.GetId()), zap.Any("actorId in ticketDetail", ticketDetail.GetActorId()), zap.Any("actorId in appended value", ticketDetail.GetTicket().GetActorId()))
		}
	}
	hasAfter, hasBefore := getHasBeforeAndAfter(pageToken, len(ticketDetailList), pageSize)

	var pageContextResponse *rpc.PageContextResponse

	// populate page context response
	if len(resultTicketList) > 0 {
		pageContext, err := getPageContextResponse(ctx, resultTicketList, hasBefore, hasAfter)
		if err != nil {
			return nil, nil, errors.Wrap(err, "failed to encode page token")
		}
		pageContextResponse = pageContext
	}

	if s.ticketConfig.IsTicketListLoggingEnabled() {
		for _, ticket := range resultTicketList {
			cxLogger.Info(ctx, "actor id in resultTicketList before returning", zap.Int64(logger.TICKET_ID, ticket.GetId()), zap.String(logger.ACTOR_ID, ticket.GetActorId()))
		}
	}
	return resultTicketList, pageContextResponse, nil
}

//nolint:dupl
func getPageContextResponse(ctx context.Context, tickets []*ticketPb.Ticket, hasBefore bool, hasAfter bool) (*rpcPb.PageContextResponse, error) {
	beforeToken := &dao2.PageToken{
		Timestamp: tickets[0].GetCreatedAt(),
		Offset:    0,
		IsReverse: true,
	}
	afterToken := &dao2.PageToken{
		Timestamp: tickets[len(tickets)-1].GetCreatedAt(),
		Offset:    0,
		IsReverse: false,
	}
	for _, ticket := range tickets {
		if reflect.DeepEqual(beforeToken.Timestamp, ticket.GetCreatedAt()) {
			beforeToken.Offset++
		}
		if reflect.DeepEqual(afterToken.Timestamp, ticket.GetCreatedAt()) {
			afterToken.Offset++
		}
	}
	encodedBeforeToken, err := beforeToken.MarshalToken()
	if err != nil {
		cxLogger.Error(ctx, "unable to encode before page token", zap.Error(err))
		return nil, err
	}
	encodedAfterToken, err := afterToken.MarshalToken()
	if err != nil {
		cxLogger.Error(ctx, "unable to encode after page token", zap.Error(err))
		return nil, err
	}
	PageContext := &rpcPb.PageContextResponse{
		BeforeToken: encodedBeforeToken,
		HasBefore:   hasBefore,
		AfterToken:  encodedAfterToken,
		HasAfter:    hasAfter,
	}
	return PageContext, nil
}

func getHasBeforeAndAfter(pageToken *dao2.PageToken, resCount int, pageSize int) (bool, bool) {
	var hasBefore, hasAfter bool
	if pageToken.IsReverse {
		if pageToken.Offset > 0 {
			hasAfter = true
		}
		if resCount > pageSize {
			hasBefore = true
		}
	} else {
		if pageToken.Offset > 0 {
			hasBefore = true
		}
		if resCount > pageSize {
			hasAfter = true
		}
	}
	return hasAfter, hasBefore
}

func (s *Service) getPageToken(pageContext *rpcPb.PageContextRequest) (*dao2.PageToken, error) {
	pageToken := &dao2.PageToken{}
	var encodedToken string
	if pageContext.GetAfterToken() != "" {
		encodedToken = pageContext.GetAfterToken()
	}
	if pageContext.GetBeforeToken() != "" {
		encodedToken = pageContext.GetBeforeToken()
	}
	if encodedToken != "" {
		err := pageToken.UnmarshalToken(encodedToken)
		if err != nil {
			return nil, errors.Wrap(err, "error while unmarshalling page token")
		}
	}
	return pageToken, nil
}

func (s *Service) setDefaultValues(req *ticketPb.GetSupportTicketsRequest) {
	if req.GetTicketFilters() == nil {
		req.TicketFilters = &ticketPb.TicketFilters{}
	}
	if req.GetTicketFilters().GetToTime() == nil {
		req.GetTicketFilters().ToTime = timestampPb.New(time.Now())
	}
	if req.GetPageContextRequest() == nil {
		req.PageContextRequest = &rpcPb.PageContextRequest{}
	}
	if req.GetPageContextRequest().GetPageSize() == 0 {
		req.GetPageContextRequest().PageSize = DefaultPageSize
	}
	// if list of ProductCategory (L1) is not passed populate it based on the type of tickets clients want to fetch
	// right now we are only doing this for transaction tickets but in future there will be many such cases
	if len(req.GetTicketFilters().GetProductCategoryList()) == 0 &&
		req.GetTicketFilters().GetCustomFieldFilter().GetFilterKey() == ticketPb.CustomFieldFilter_FILTER_KEY_TXN_ID {
		// clients are fetching ticket by TxnId so apply product category related to Txn
		req.GetTicketFilters().ProductCategoryList = productCategoryForTxnRelatedTickets
	}
}

func (s *Service) GetSingleTicketResponse(ctx context.Context, req *ticketPb.GetSupportTicketsRequest) (*ticketPb.GetSupportTicketsResponse, error) {
	ticketDetail, err := s.supportTicketDao.GetById(ctx, req.GetTicketId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &ticketPb.GetSupportTicketsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "failed to fetch ticket by using id", zap.Error(err))
		return &ticketPb.GetSupportTicketsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	s.setFieldsInTicketObject(ticketDetail)
	return &ticketPb.GetSupportTicketsResponse{
		Status:  rpcPb.StatusOk(),
		Tickets: []*ticketPb.Ticket{ticketDetail.GetTicket()},
		PageContextResponse: &rpcPb.PageContextResponse{
			HasBefore: false,
			HasAfter:  false,
		},
	}, nil
}

func (s *Service) setFieldsInTicketObject(ticketDetail *ticketPb.TicketDetails) {
	// set ticket url
	ticketDetail.GetTicket().Url = fmt.Sprintf(s.ticketConfig.URL(), ticketDetail.GetTicket().GetId())
	// set ticket expected resolution date which is calculated based on sla config
	ticketDetail.GetTicket().GetCustomFields().ExpectedResolutionDate = ticketDetail.GetExpectedResolutionTime()
	// if ticket id is not available in ticket, populate the actor id from db
	if ticketDetail.GetTicket().GetActorId() == "" && ticketDetail.GetActorId() != "" {
		ticketDetail.GetTicket().ActorId = ticketDetail.GetActorId()
	}
}

func validateSupportTicketsRequest(req *ticketPb.GetSupportTicketsRequest) error {
	if req.GetPageContextRequest().GetPageSize() > 50 {
		return errors.New("invalid page size: max page size allowed is 50")
	}
	if req.GetTicketFilters().GetActiveAfterTime() == nil && len(req.GetTicketFilters().GetActorIdList()) != 1 {
		// Bypass time constraints if the request is from GetSupportTicketsForApp or for a single actor Id
		if req.GetTicketFilters().GetFromTime() == nil {
			return errors.New("from time is mandatory parameter for fetching tickets")
		}
		if req.GetTicketFilters().GetFromTime().AsTime().Add(MaxAllowedDurationForFetchingTicket).
			Before(req.GetTicketFilters().GetToTime().AsTime()) {
			return errors.New("difference between from time and to time can't be more that 90 days")
		}
	}
	// valid request
	return nil
}

func ValidateSupportTicketsRequestForSherlock(req *ticketPb.GetSupportTicketsForSherlockRequest) error {
	if req.GetTicketFilters().GetFromTime() == nil || req.GetTicketFilters().GetToTime() == nil {
		return errors.New("from time and to time is mandatory parameter for fetching tickets")
	}
	if req.GetTicketFilters().GetFromTime().AsTime().Add(MaxAllowedDurationForFetchingTicket).
		Before(req.GetTicketFilters().GetToTime().AsTime()) {
		return errors.New("difference between from time and to time can't be more that 90 days")
	}
	if req.GetPageContextRequest().GetPageSize() > 50 {
		return errors.New("invalid page size")
	}
	// valid request
	return nil
}

func (s *Service) enrichIdentifierValueListByPhoneNumber(ctx context.Context, ticketFilters *ticketPb.TicketFilters) error {
	var identifierValueList []string
	// if parsing phone number fails we should return the error
	// only this step is blocking for enriching the identifier value list
	phoneNumber, err := commontypes.ParsePhoneNumber(ticketFilters.GetIdentifierValue())
	if err != nil {
		return errors.Wrap(err, "error while parsing phone number")
	}
	// explicitly adding country code to generate all possible combinations for phone number
	phoneNumber.CountryCode = 91

	// append all possible phone number combinations to query it on db
	identifierValueList = append(identifierValueList, phoneNumber.ToStringNationalNumber(), phoneNumber.ToString(), phoneNumber.ToSignedString())

	// if we face issue while fetching actor, we just log it and go to next step
	actor, actorErr := s.customerIdentifierHelper.GetActor(ctx, "", phoneNumber)
	if actorErr != nil {
		cxLogger.Error(ctx, "error while fetching actor for phone number", zap.Error(actorErr))
		ticketFilters.IdentifierValueList = identifierValueList
		return nil
	}
	// fetch user for actor
	// if we face issue, log error and go to next step
	user, userErr := s.customerIdentifierHelper.GetUserByActorId(ctx, actor.GetId())
	if userErr == nil {
		// append email to identifier value list
		identifierValueList = append(identifierValueList, user.GetProfile().GetEmail())
	} else {
		cxLogger.Error(ctx, "error while fetching user for actor", zap.Error(userErr))
	}

	// fetch freshdesk ref id for actor
	// if we face issue, log error and go to next step
	freshdeskId, freshdeskErr := s.customerIdentifierHelper.GetFreshdeskIdForActor(ctx, actor.GetId())
	if freshdeskErr == nil {
		// append freshdeskId to identifier value list
		identifierValueList = append(identifierValueList, freshdeskId)
	} else {
		cxLogger.Error(ctx, "error while fetching freshdesk id for actor", zap.Error(freshdeskErr))
	}
	ticketFilters.IdentifierValueList = identifierValueList
	return nil
}

func (s *Service) enrichTicketFiltersWithIdentifierValueList(ctx context.Context, ticketFilters *ticketPb.TicketFilters) error {
	// TODO (Diparth): extend this for all supported identifier type
	switch ticketFilters.GetIdentifierType() {
	case ticketPb.UserIdentifierType_USER_IDENTIFIER_TYPE_PHONE_NUMBER:
		return s.enrichIdentifierValueListByPhoneNumber(ctx, ticketFilters)
	default:
		return errors.New("invalid identifier type")
	}
}

// nolint:funlen
func (s *Service) GetSupportTicketsForSherlockFromDB(ctx context.Context, req *ticketPb.GetSupportTicketsForSherlockRequest) (*ticketPb.GetSupportTicketsForSherlockResponse, error) {
	err := ValidateSupportTicketsRequestForSherlock(req)
	if err != nil {
		logger.Error(ctx, "invalid input req for GetSupportTicketsForSherlock", zap.Error(err))
		return &ticketPb.GetSupportTicketsForSherlockResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(err.Error()),
		}, nil
	}

	pageToken, err := s.getPageToken(req.GetPageContextRequest())
	if err != nil {
		logger.Error(ctx, "unable to decode page tokens", zap.Error(err))
		return &ticketPb.GetSupportTicketsForSherlockResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("unable to decode page tokens"),
		}, nil
	}

	// enrich ticket filters if supported identifier type is passed
	// if error is encountered we log the error and query db
	// if no error is encountered identifier value list is enriched with necessary identifier
	if ValidIdentifierTypeForTicketFiltersEnrichment[req.GetTicketFilters().GetIdentifierType()] {
		enrichErr := s.enrichTicketFiltersWithIdentifierValueList(ctx, req.GetTicketFilters())
		if enrichErr != nil {
			cxLogger.Error(ctx, "error while enriching ticket filters", zap.Error(enrichErr))
		}
	}

	ticketDetailList, err := s.supportTicketDao.GetAllTickets(ctx, req.GetTicketFilters(), pageToken, int(req.GetPageContextRequest().GetPageSize()))
	if err != nil {
		logger.Error(ctx, "failed to fetch ticket list from db", zap.Error(err))
		return &ticketPb.GetSupportTicketsForSherlockResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if s.ticketConfig.IsTicketListLoggingEnabled() {
		for _, ticket := range ticketDetailList {
			cxLogger.Info(ctx, "actor id after fetching from DB", zap.Int64(logger.TICKET_ID, ticket.GetId()), zap.String(logger.ACTOR_ID, ticket.GetActorId()))
		}
	}

	ticketList, pageContextResponse, err := s.PaginateSupportTicketsResponse(ctx, int(req.GetPageContextRequest().GetPageSize()), ticketDetailList, pageToken)
	if err != nil {
		logger.Error(ctx, "error while paginating ticket details response")
		return &ticketPb.GetSupportTicketsForSherlockResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if len(ticketList) == 0 {
		logger.Error(ctx, "ticket details not found")
		return &ticketPb.GetSupportTicketsForSherlockResponse{
			Status: rpcPb.StatusRecordNotFound(),
		}, nil
	}

	// sort cx ticket on basis of status
	// OPEN status should come first, followed by PENDING, RESOLVED, CLOSED
	sortCxTicketListOnStatus(ticketList)

	// push metric to denote ticket was served from db
	metrics.RecordPastTicketsServingSourceForCall(callPb.PastTicketsServingSourceForCall_PAST_TICKETS_SERVING_SOURCE_FOR_CALL_SUPPORT_TICKETS_DB.String())

	if s.ticketConfig.IsTicketListLoggingEnabled() {
		for _, ticket := range ticketList {
			cxLogger.Info(ctx, "actor id just before sending response", zap.Int64(logger.TICKET_ID, ticket.GetId()), zap.String(logger.ACTOR_ID, ticket.GetActorId()))
		}
	}

	return &ticketPb.GetSupportTicketsForSherlockResponse{
		Status:              rpcPb.StatusOk(),
		Tickets:             ticketList,
		PageContextResponse: pageContextResponse,
	}, nil
}

func (s *Service) GetSupportTicketsForSherlock(ctx context.Context, req *ticketPb.GetSupportTicketsForSherlockRequest) (*ticketPb.GetSupportTicketsForSherlockResponse, error) {
	switch req.GetFetchFilter().(type) {
	case *ticketPb.GetSupportTicketsForSherlockRequest_TicketFilters:
		return s.GetSupportTicketsForSherlockFromDB(ctx, req)
	case *ticketPb.GetSupportTicketsForSherlockRequest_ContactDetails:
		return s.FetchTicketsFromSource(ctx, req)
	default:
		return &ticketPb.GetSupportTicketsForSherlockResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid fetch medium"),
		}, nil
	}
}

// nolint:funlen
func (s *Service) FetchTicketsFromSource(ctx context.Context, req *ticketPb.GetSupportTicketsForSherlockRequest) (*ticketPb.GetSupportTicketsForSherlockResponse, error) {
	if req.GetContactDetails() == nil {
		cxLogger.Info(ctx, "contact info field is empty")
		return &ticketPb.GetSupportTicketsForSherlockResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("contact info should be populated"),
		}, nil
	}

	var contact *freshdeskPb.TicketContact
	var err error

	switch req.GetContactDetails().GetContactInfo().(type) {
	case *ticketPb.ContactDetails_PhoneNumber:
		contact, err = s.FetchContactUsingPhoneNumber(ctx, req.GetContactDetails().GetPhoneNumber())
	case *ticketPb.ContactDetails_EmailId:
		contact, err = s.FetchContactUsingEmailId(ctx, req.GetContactDetails().GetEmailId())
	default:
		cxLogger.Error(ctx, "invalid contact info")
		return &ticketPb.GetSupportTicketsForSherlockResponse{
			Status: rpc.StatusInvalidArgument(),
		}, nil
	}

	if err != nil {
		cxLogger.Error(ctx, "error fetching contact", zap.Error(err), zap.Any("phoneNumber", req.GetContactDetails().GetPhoneNumber()),
			zap.Any("email", req.GetContactDetails().GetEmailId()))
		if errors.Is(err, ErrContactNotFound) {
			return &ticketPb.GetSupportTicketsForSherlockResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		return &ticketPb.GetSupportTicketsForSherlockResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// fetch all the tickets linked to same requester
	resp, err := s.fdClient.GetAllTickets(ctx, &freshdeskPb.GetAllTicketsRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FRESHDESK,
		},
		RequesterId: contact.GetId(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error fetching tickets for contact", zap.Error(err), zap.Any("contact", contact.String()))
		if resp.GetStatus().IsRecordNotFound() {
			return &ticketPb.GetSupportTicketsForSherlockResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		}
		return &ticketPb.GetSupportTicketsForSherlockResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	var cxTicketList []*ticketPb.Ticket
	for _, fdTicket := range resp.GetTicketList() {
		cxTicketList = append(cxTicketList, ticketPb.ToCXTicket(fdTicket, s.ticketConfig.URL(), ""))
	}

	// push metric to denote ticket was served from freshdesk
	metrics.RecordPastTicketsServingSourceForCall(callPb.PastTicketsServingSourceForCall_PAST_TICKETS_SERVING_SOURCE_FOR_CALL_FRESHDESK.String())

	// sort cx ticket on basis of status
	// OPEN status should come first, followed by PENDING, RESOLVED, CLOSED
	sortCxTicketListOnStatus(cxTicketList)

	return &ticketPb.GetSupportTicketsForSherlockResponse{
		Status:  rpc.StatusOk(),
		Tickets: cxTicketList,
		PageContextResponse: &rpcPb.PageContextResponse{
			HasBefore: false,
			HasAfter:  false,
		},
	}, nil
}

// nolint:gocritic
func sortCxTicketListOnStatus(cxTicketList []*ticketPb.Ticket) {
	sort.Slice(cxTicketList[:], func(i, j int) bool {
		return cxTicketList[i].GetStatus() < cxTicketList[j].GetStatus()
	})
}

func (s *Service) FetchContactUsingPhoneNumber(ctx context.Context, phoneNumber *commontypes.PhoneNumber) (*freshdeskPb.TicketContact, error) {
	phoneNumber.CountryCode = 91
	contactsResp, err := s.fdClient.GetContacts(ctx, &freshdeskPb.GetContactsRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FRESHDESK,
		},
		Filters: &freshdeskPb.GetContactsRequest_Filters{
			PhoneNumber: phoneNumber,
		},
		LimitPerPage: 1,
		PageNumber:   1,
	})
	if err = epifigrpc.RPCError(contactsResp, err); err != nil {
		return nil, errors.Wrap(err, "error while fetching contact from freshdesk using phone number")
	}
	if len(contactsResp.GetContacts()) == 0 {
		return nil, ErrContactNotFound
	}
	return contactsResp.GetContacts()[0], nil
}

func (s *Service) FetchContactUsingEmailId(ctx context.Context, emailId string) (*freshdeskPb.TicketContact, error) {
	contactsResp, err := s.fdClient.GetContacts(ctx, &freshdeskPb.GetContactsRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FRESHDESK,
		},
		Filters: &freshdeskPb.GetContactsRequest_Filters{
			EmailId: emailId,
		},
		LimitPerPage: 1,
		PageNumber:   1,
	})
	if err = epifigrpc.RPCError(contactsResp, err); err != nil {
		return nil, errors.Wrap(err, "error while fetching contact from freshdesk using emailId")
	}
	if len(contactsResp.GetContacts()) == 0 {
		return nil, ErrContactNotFound
	}
	return contactsResp.GetContacts()[0], nil
}

//nolint:funlen
func (s *Service) GetSupportTicketsForApp(ctx context.Context, req *ticketPb.GetSupportTicketsForAppRequest) (*ticketPb.GetSupportTicketsForAppResponse, error) {
	if err := validateTicketsForAppReqAndAddDefaults(req); err != nil {
		logger.Info(ctx, "invalid request", zap.Error(err))
		return &ticketPb.GetSupportTicketsForAppResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(err.Error()),
		}, nil
	}
	if req.GetShouldUseCache() {
		cachedTicket, err := s.fetchTicketFromCacheForApp(ctx, req)
		switch {
		// we found an empty ticket object in cache, it means user don't have any tickets
		case err == nil && cachedTicket.GetId() == 0:
			return &ticketPb.GetSupportTicketsForAppResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		case err == nil:
			return &ticketPb.GetSupportTicketsForAppResponse{
				Status:  rpcPb.StatusOk(),
				Tickets: []*ticketPb.TicketDetailsForUser{cachedTicket},
			}, nil
		default:
			if !errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "failed to fetch ticket from cache", zap.Error(err), zap.String(logger.KEY_ID, getCacheKeyForActorTicket(req.GetActorId())))
			}
		}
	}

	// error handling is not required as the method takes care of it
	resp := s.getSupportTicketsForAppResponse(ctx, req)
	// setting latest ticket in cache as tickets are being displayed of home page of app, there can be high traffic expected
	s.setLatestTicketInCache(ctx, req.GetActorId(), resp)
	return resp, nil
}

func (s *Service) fetchTicketFromCacheForApp(ctx context.Context, req *ticketPb.GetSupportTicketsForAppRequest) (*ticketPb.TicketDetailsForUser, error) {
	ticketJsonStr, err := s.cacheStorage.Get(ctx, getCacheKeyForActorTicket(req.GetActorId()))
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch ticket from cache")
	}

	ticket := &ticketPb.TicketDetailsForUser{}
	parseErr := protojson.Unmarshal([]byte(ticketJsonStr), ticket)
	if parseErr != nil {
		return nil, errors.Wrap(parseErr, "failed to parse the cached ticket details")
	}
	return ticket, nil
}

//nolint:funlen
func (s *Service) getSupportTicketsForAppResponse(ctx context.Context, req *ticketPb.GetSupportTicketsForAppRequest) *ticketPb.GetSupportTicketsForAppResponse {
	ticketResp, err := s.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			// Fetch tickets which are active after cutoff date
			ActiveAfterTime: timestampPb.New(s.ticketConfig.ShowTicketsInAppConfig().CutOffDateToShowTickets()),
			ToTime:          timestampPb.New(time.Now()),
			// Query for only whitelisted categories
			ProductCategoryList: getWhitelistedProductCategories(s.ticketConfig.ShowTicketsInAppConfig()),
			ActorIdList:         []string{req.GetActorId()},
			StatusList:          getStatusListFromInAppStatusList(req.GetTicketFilters().GetStatusList()),
		},
		PageContextRequest: req.GetPageContextRequest(),
	})
	if te := epifigrpc.RPCError(ticketResp, err); te != nil || len(ticketResp.GetTickets()) == 0 {
		logger.Error(ctx, "failed to fetch support ticket list", zap.Error(te))
		if ticketResp.GetStatus().IsRecordNotFound() {
			return &ticketPb.GetSupportTicketsForAppResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}
		}
		return &ticketPb.GetSupportTicketsForAppResponse{
			Status: rpcPb.StatusInternal(),
		}
	}
	ticketListForUser, err := s.formatTicketsForUser(ctx, ticketResp.GetTickets())
	if err != nil {
		logger.Error(ctx, "failed to format tickets for user", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &ticketPb.GetSupportTicketsForAppResponse{Status: rpcPb.StatusRecordNotFound()}
		}
		return &ticketPb.GetSupportTicketsForAppResponse{Status: rpcPb.StatusInternal()}
	}

	return &ticketPb.GetSupportTicketsForAppResponse{
		Status:              rpcPb.StatusOk(),
		Tickets:             ticketListForUser,
		PageContextResponse: ticketResp.GetPageContextResponse(),
	}
}

func (s *Service) formatTicketsForUser(ctx context.Context, ticketList []*ticketPb.Ticket) ([]*ticketPb.TicketDetailsForUser, error) {
	var ticketListForApp []*ticketPb.TicketDetailsForUser
	for _, ticket := range ticketList {
		// TODO: add metrics to catch errors in this loop
		// If some mandatory fields are missing in the tickets we skip showing those tickets
		cxLogger.Debug(ctx, "fetched ticket for app", zap.Any("ticket", ticket))

		ticketForApp, convertErr := s.convertToTicketForApp(ctx, ticket)
		// Since we are already querying for valid ticket category, convertErr is expected to be nil
		if convertErr != nil {
			logger.Error(ctx, "failed conversion to in app ticket", zap.Any(logger.TICKET_ID, ticket.GetId()), zap.Error(convertErr))
			continue
		}
		logger.Debug(ctx, "converted ticket for app", zap.Any("ticket", ticketForApp))
		ticketListForApp = append(ticketListForApp, ticketForApp)
	}
	if len(ticketListForApp) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return ticketListForApp, nil
}

func (s *Service) setLatestTicketInCache(ctx context.Context, actorId string, ticketForAppResp *ticketPb.GetSupportTicketsForAppResponse) {
	var ticketToCache *ticketPb.TicketDetailsForUser
	switch {
	// if not tickets are found for user we will cache empty objects
	// this is done to handle the case where user don't have any tickets, set of such users is expected to be much higher
	// we are setting empty object to signify user don't have any ticket, and we don't end up making DB calls for such cases
	case ticketForAppResp.GetStatus().IsRecordNotFound():
		ticketToCache = &ticketPb.TicketDetailsForUser{}
	// if there are tickets for user we pick the first one and cache it as that ticket is the latest one
	case ticketForAppResp.GetStatus().IsSuccess() && len(ticketForAppResp.GetTickets()) > 0:
		ticketToCache = ticketForAppResp.GetTickets()[0]
	default:
		// if there are other failure don't cache the result and return immediately
		return
	}

	ticketJsonStr, err := protojson.Marshal(ticketToCache)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("failed to marshal latest ticket: %v", ticketJsonStr), zap.Error(err))
		return
	}
	err = s.cacheStorage.Set(ctx, getCacheKeyForActorTicket(actorId), string(ticketJsonStr), s.ticketConfig.LatestTicketCacheValidityDuration())
	if err != nil {
		logger.Error(ctx, "failed to cache latest ticket", zap.Error(err), zap.Int64(logger.TICKET_ID, ticketToCache.GetId()),
			zap.String(logger.ACTOR_ID_V2, actorId))
	}
}

func isTicketStillUpdating(ticket *ticketPb.Ticket, mandatoryFields []string) (commontypes.BooleanEnum, error) {
	for _, fieldName := range mandatoryFields {
		field := reflect.ValueOf(ticket).Elem().FieldByName(fieldName)
		if field == (reflect.Value{}) {
			if ticket.GetCustomFields() != nil {
				field = reflect.ValueOf(ticket.GetCustomFields()).Elem().FieldByName(fieldName)
			}
			// this means there is no field with this name in the Ticket struct. So check Ticket.CustomFields struct
			if field == (reflect.Value{}) {
				return commontypes.BooleanEnum_BOOLEAN_ENUM_UNSPECIFIED, errors.New("invalid field name mentioned in config")
			}
		}
		if field.IsZero() {
			// if a mandatory field is its zero value, the ticket is still updating, so UI should show ticket-updating card
			logger.DebugNoCtx("mandatory field is empty", zap.Any("field", field), zap.Int64(logger.TICKET_ID, ticket.GetId()))
			return commontypes.BooleanEnum_TRUE, nil
		}
	}
	// if none of the mandatory fields are zero value then UI should not show ticket-updating card
	return commontypes.BooleanEnum_FALSE, nil
}

func validateTicketsForAppReqAndAddDefaults(req *ticketPb.GetSupportTicketsForAppRequest) error {
	if req.GetActorId() == "" {
		return errors.New("Actor Id cannot be empty")
	}
	if req.GetPageContextRequest().GetPageSize() == 0 {
		if req.GetPageContextRequest() == nil {
			req.PageContextRequest = &rpcPb.PageContextRequest{}
		}
		req.GetPageContextRequest().PageSize = DefaultPageSizeForApp
	}
	return nil
}

func getWhitelistedProductCategories(conf *cxGenConf.ShowTicketsInAppConfig) []ticketPb.ProductCategory {
	var whitelistedCategories []ticketPb.ProductCategory
	for _, catStr := range helper2.ConvertROArrToStringArr(conf.WhitelistedProductCategories()) {
		category, ok := ticketPb.ProductCategory_value[catStr]
		if ok {
			whitelistedCategories = append(whitelistedCategories, ticketPb.ProductCategory(category))
		}
	}
	return whitelistedCategories
}

func getStatusListFromInAppStatusList(inappStatusList []ticketPb.TicketStatusForUser) []ticketPb.Status {
	var statusList []ticketPb.Status
	for _, inappStatus := range inappStatusList {
		switch inappStatus {
		case ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_CLOSED:
			statusList = append(statusList, ticket_states.GetClosedTicketStates()...)
		case ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE:
			statusList = append(statusList, ticket_states.GetOpenTicketStates()...)
		case ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_WAITING_ON_CUSTOMER:
			statusList = append(statusList, ticket_states.GetWaitingOnCustomerTicketStates()...)
		default:
		}
	}
	return statusList
}

func (s *Service) convertToTicketForApp(ctx context.Context, ticket *ticketPb.Ticket, isCsatEvaluationRequired ...bool) (*ticketPb.TicketDetailsForUser, error) {
	title, description, err := s.getCategoryTransformation(ctx, ticket, ticketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_TITLE_DESCRIPTION)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get title and description for the ticket")
	}
	isInAppCSATSurveyRequired := false
	if len(isCsatEvaluationRequired) > 0 && isCsatEvaluationRequired[0] {
		isInAppCSATSurveyRequired, _, err = s.csatEvaluator.IsTicketEligibleForCsat(ctx, &ticketPb.TicketDetails{
			Id:              ticket.GetId(),
			Status:          ticket.GetStatus(),
			IssueCategoryId: ticket.GetIssueCategoryId(),
		}, uuid.NewString())
		if err != nil {
			logger.Error(ctx, "failed to determine if in app csat survey is required", zap.Error(err), zap.Int64(logger.TICKET_ID, ticket.GetId()))
		}
	}
	autoIdTagBoolVal := commontypes.BooleanEnum_FALSE
	if lo.Contains(ticket.GetTags(), AutoIdTag) {
		autoIdTagBoolVal = commontypes.BooleanEnum_TRUE
	}
	// we are not populating expected resolution by using due by field from freshdesk
	// as it may set wrong expectations of resolution time to customers
	// expected behaviour is that not sending expected resolution by field will cause it not be rendered on client
	ticketForApp := &ticketPb.TicketDetailsForUser{
		Id:                  ticket.GetId(),
		Title:               title,
		Description:         description,
		Status:              convertToStatusForApp(ticket),
		CreatedTime:         ticket.GetCreatedAt(),
		LastInteractionTime: ticket.GetUpdatedAt(),
		OtherTicketDetails: map[string]*ticketPb.TicketDetailsForUser_MapValue{
			CreatedByKey: {
				Val: &ticketPb.TicketDetailsForUser_MapValue_StrVal{
					StrVal: s.determineTicketCreator(ticket.GetTags()),
				},
			},
			CreationModeKey: {
				Val: &ticketPb.TicketDetailsForUser_MapValue_StrVal{
					StrVal: s.convertSourceToCreationMode(ticket.GetSource(), ticket.GetTags()),
				},
			},
			AutoIdTag: {
				Val: &ticketPb.TicketDetailsForUser_MapValue_BoolVal{
					BoolVal: autoIdTagBoolVal,
				},
			},
		},
		IsInAppCsatSurveyRequired: isInAppCSATSurveyRequired,
	}
	// if flag is enabled, expected resolution by is determined using sla config
	// otherwise it remains as it is, determined by dueBy field
	if s.ticketConfig.SLAConfig().IsExpectedResolutionByFieldDeterminedUsingSLA() {
		logger.Debug(ctx, "setting expected resolution date on in app ticket", zap.Any("SLA", ticket.GetCustomFields().GetExpectedResolutionDate()), zap.Int64(logger.TICKET_ID, ticket.GetId()))
		// The product decision taken here is to not populate nil SLA, or show 1 Jan 1970 to user in the app
		// If expected resolution date is nil, it will not be populated and present date would be kept as it is
		if ticket.GetCustomFields().GetExpectedResolutionDate().GetSeconds() != 0 {
			ticketForApp.ExpectedResolutionBy = ticket.GetCustomFields().GetExpectedResolutionDate()
		}
	}
	if lo.Contains(ticket_states.GetClosedTicketStates(), ticket.GetStatus()) {
		ticketForApp.ClosedTime = ticket.GetUpdatedAt()
	}
	return ticketForApp, nil
}

func (s *Service) determineTicketCreator(tags []string) string {
	createdBy := ticketPb.CreatedBy_CREATED_BY_CUSTOMER
	if lo.Contains(tags, AutoIdTag) {
		createdBy = ticketPb.CreatedBy_CREATED_BY_SYSTEM
	}
	val := s.cxConf.TicketConfig.ShowTicketsInAppConfig.CreatedByEnumToValueMapping[createdBy.String()]
	return val
}

func (s *Service) getCategoryTransformation(ctx context.Context, ticket *ticketPb.Ticket, transformationType ticketPb.TicketTransformationType) (title, description string, retErr error) {
	prodCat := ticket.GetCustomFields().GetProductCategory()
	if prodCat == ticketPb.ProductCategory_PRODUCT_CATEGORY_UNSPECIFIED {
		return s.ticketConfig.DefaultAppTicketTitle(), s.ticketConfig.DefaultAppTicketDescription(), nil
	}
	categoryTagsListFilter := []*ticketPb.TicketDetailsTransformation{{ProductCategory: prodCat}}
	catDetails := ticket.GetCustomFieldWithValue().GetProductCategoryDetails()
	if catDetails != "" {
		categoryTagsListFilter = append(categoryTagsListFilter, &ticketPb.TicketDetailsTransformation{ProductCategory: prodCat, ProductCategoryDetails: catDetails})
	}
	subCat := ticket.GetCustomFieldWithValue().GetSubCategory()
	if subCat != "" {
		categoryTagsListFilter = append(categoryTagsListFilter, &ticketPb.TicketDetailsTransformation{ProductCategory: prodCat, ProductCategoryDetails: catDetails, Subcategory: subCat})
	}

	transformations, err := s.ticketDetailsTransformationDao.GetAllByFilters(ctx,
		dao2.WithProdCatProdCatDetailsSubcatList(categoryTagsListFilter),
		dao2.WithTransformationTypeList([]ticketPb.TicketTransformationType{transformationType}),
	)
	if err != nil {
		// we don't want to surface errors to customers after they have tried to fetch a particular ticket
		// just because a specific title and description is not available, so we will populate a generic title and description
		if errors.Is(err, epifierrors.ErrRecordNotFound) && s.ticketConfig.IsDefaultTitleAndDescriptionEnabledForInAppTicket() {
			logger.Error(ctx, "title and description transformation not found", zap.Int64(logger.TICKET_ID, ticket.GetId()), zap.String(logger.PRODUCT_CATEGORY, ticket.GetCustomFields().GetProductCategory().String()),
				zap.String(logger.PRODUCT_CATEGORY_DETAILS, ticket.GetCustomFieldWithValue().GetProductCategoryDetails()), zap.String(logger.PRODUCT_SUB_CATEGORY, ticket.GetCustomFieldWithValue().GetSubCategory()))
			return s.ticketConfig.DefaultAppTicketTitle(), s.ticketConfig.DefaultAppTicketDescription(), nil
		}
		return "", "", errors.Wrap(err, fmt.Sprintf("failed to get transformation from db for (%s, %s, %s)", prodCat.String(), catDetails, subCat))
	}
	// Sort this resulting list so that the entry with maximum match (category details, subcategory) comes first
	// Explanation: Since we are querying explicitly for empty string as well, at max we get 3 entries in the result.
	// 				All the resultant transformations would have same product category and the empty strings would be
	// 				last in the list. So the first element in the list would be maximum match.
	sort.Slice(transformations, func(i, j int) bool {
		if transformations[i].GetProductCategoryDetails() == transformations[j].GetProductCategoryDetails() {
			return transformations[i].GetSubcategory() > transformations[j].GetSubcategory()
		}
		return transformations[i].GetProductCategoryDetails() > transformations[j].GetProductCategoryDetails()
	})
	return transformations[0].GetTransformationValue().GetTitle(), transformations[0].GetTransformationValue().GetDescription(), nil
}

func (s *Service) GetCategoryTransformation(ctx context.Context, req *ticketPb.GetCategoryTransformationsRequest) (*ticketPb.GetCategoryTransformationsResponse, error) {
	ticket := req.GetTicket()
	transformationType := ticketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_TITLE_DESCRIPTION
	if lo.Contains(ticket.GetTags(), "Auto ID") {
		transformationType = ticketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_TITLE_DESCRIPTION_AUTO_IDS
	}
	title, description, getTitleDescriptionErr := s.getCategoryTransformation(ctx, req.GetTicket(), transformationType)
	if getTitleDescriptionErr != nil {
		logger.Error(ctx, "failed to get title and description for the ticket", zap.Int64(logger.TICKET_ID, req.GetTicket().GetId()), zap.Error(getTitleDescriptionErr))
		return &ticketPb.GetCategoryTransformationsResponse{
			Status:      rpc.StatusInternalWithDebugMsg(getTitleDescriptionErr.Error()),
			Title:       s.ticketConfig.DefaultAppTicketTitle(),
			Description: s.ticketConfig.DefaultAppTicketDescription(),
		}, nil
	}

	return &ticketPb.GetCategoryTransformationsResponse{
		Status:      rpc.StatusOk(),
		Title:       title,
		Description: description,
	}, nil
}

func convertToStatusForApp(ticket *ticketPb.Ticket) ticketPb.TicketStatusForUser {
	// we don't want to display empty ticket status to users, hence by default we will show active status to user
	switch {
	case ticket.GetStatus() == ticketPb.Status_STATUS_RESOLVED || ticket.GetStatus() == ticketPb.Status_STATUS_CLOSED:
		return ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_CLOSED
	case ticket.GetStatus() == ticketPb.Status_STATUS_WAITING_ON_CUSTOMER:
		return ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_WAITING_ON_CUSTOMER
	case ticket.GetCustomFields().GetReopenStatus():
		return ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_RE_OPEN
	case ticket.GetCustomFields().GetExpectedResolutionDate() != nil && time.Now().After(ticket.GetCustomFields().GetExpectedResolutionDate().AsTime()):
		return ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_DELAY
	default:
		return ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE
	}
}

func (s *Service) convertSourceToCreationMode(source ticketPb.Source, tags []string) string {
	var creationMode ticketPb.CreationMode
	switch source {
	case ticketPb.Source_SOURCE_EMAIL:
		creationMode = ticketPb.CreationMode_CREATION_MODE_EMAIL
	case ticketPb.Source_SOURCE_CHAT:
		creationMode = ticketPb.CreationMode_CREATION_MODE_CHAT
	case ticketPb.Source_SOURCE_PHONE:
		creationMode = ticketPb.CreationMode_CREATION_MODE_CALL
	default:
		creationMode = ticketPb.CreationMode_CREATION_MODE_DEFAULT
	}
	// TODO (smit): Add source for Watson tickets after discussion with product
	if lo.Contains(tags, AutoIdTag) {
		creationMode = ticketPb.CreationMode_CREATION_MODE_AUTOMATIC
	}
	if lo.Contains(tags, reportedViaAppTag) {
		creationMode = ticketPb.CreationMode_CREATION_MODE_APP
	}
	val := s.cxConf.TicketConfig.ShowTicketsInAppConfig.CreationModeEnumToValueMapping[creationMode.String()]
	return val
}

func (s *Service) CreateTicketDetailsTransformations(ctx context.Context, req *ticketPb.CreateTicketDetailsTransformationsRequest) (*ticketPb.CreateTicketDetailsTransformationsResponse, error) {
	if len(req.GetTransformationsList()) == 0 {
		return &ticketPb.CreateTicketDetailsTransformationsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("transformations list cannot be empty"),
		}, nil
	}
	if err := validations.ValidateTransformationList(req.GetTransformationsList()); err != nil {
		logger.Error(ctx, "invalid arguments to create transformation", zap.Error(err))
		return &ticketPb.CreateTicketDetailsTransformationsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("product category and title are mandatory to create entry. " + err.Error()),
		}, nil
	}

	transformationList, dbErr := s.ticketDetailsTransformationDao.CreateBatch(ctx, req.GetTransformationsList())
	if dbErr != nil {
		logger.Error(ctx, "db error while creating transformation")
		if errors.Is(dbErr, epifierrors.ErrDuplicateEntry) {
			return &ticketPb.CreateTicketDetailsTransformationsResponse{
				Status: rpcPb.StatusAlreadyExists(),
			}, nil
		}
		return &ticketPb.CreateTicketDetailsTransformationsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(dbErr.Error()),
		}, nil
	}

	return &ticketPb.CreateTicketDetailsTransformationsResponse{
		Status:              rpcPb.StatusOk(),
		TransformationsList: transformationList,
	}, nil
}

func (s *Service) UpdateTicketDetailsTransformation(ctx context.Context, req *ticketPb.UpdateTicketDetailsTransformationRequest) (*ticketPb.UpdateTicketDetailsTransformationResponse, error) {
	if req.GetTransformation().GetId() == "" || req.GetTransformation().GetTransformationType() == ticketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_UNSPECIFIED {
		logger.Error(ctx, "invalid argument while updating transformation")
		return &ticketPb.UpdateTicketDetailsTransformationResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("row Id and transformation type are mandatory to update entry"),
		}, nil
	}

	dbErr := s.ticketDetailsTransformationDao.Update(ctx, req.GetTransformation(), req.GetUpdateMask())
	if dbErr != nil {
		logger.Error(ctx, "db error while creating transformation")
		if errors.Is(dbErr, epifierrors.ErrRecordNotFound) {
			return &ticketPb.UpdateTicketDetailsTransformationResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &ticketPb.UpdateTicketDetailsTransformationResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(dbErr.Error()),
		}, nil
	}

	return &ticketPb.UpdateTicketDetailsTransformationResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *Service) DeleteTicketDetailsTransformations(ctx context.Context, req *ticketPb.DeleteTicketDetailsTransformationsRequest) (*ticketPb.DeleteTicketDetailsTransformationsResponse, error) {
	if len(req.GetRowIdList()) == 0 && len(req.GetTransformationTypeList()) == 0 && len(req.GetProductCategoryList()) == 0 {
		logger.Error(ctx, "invalid argument while deleting transformation")
		return &ticketPb.DeleteTicketDetailsTransformationsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("at least one filter is mandatory to delete entry"),
		}, nil
	}

	delCount, dbErr := s.ticketDetailsTransformationDao.DeleteBatch(ctx, dao2.WithRowIdList(req.GetRowIdList()),
		dao2.WithTransformationTypeList(req.GetTransformationTypeList()), dao2.WithProductCategoryList(req.GetProductCategoryList()))
	if dbErr != nil {
		logger.Error(ctx, "db error while creating transformation")
		if errors.Is(dbErr, epifierrors.ErrRecordNotFound) {
			return &ticketPb.DeleteTicketDetailsTransformationsResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &ticketPb.DeleteTicketDetailsTransformationsResponse{
			Status: rpcPb.StatusInternalWithDebugMsg(dbErr.Error()),
		}, nil
	}

	return &ticketPb.DeleteTicketDetailsTransformationsResponse{
		Status:       rpcPb.StatusOk(),
		DeletedCount: delCount,
	}, nil
}

// FetchTicketFieldFromCache will return ticket field object if found in cache
// will return nil if ticket field is not found or some other error has occurred
func (s *Service) FetchTicketFieldFromCache(ctx context.Context, ticketFieldId int64) *freshdeskPb.FreshdeskTicketField {
	ticketFieldJsonStr, err := s.cacheStorage.Get(ctx, getCacheKeyForTicketField(ticketFieldId))
	if err != nil {
		if !errors.Is(err, epifierrors.ErrRecordNotFound) {
			cxLogger.Error(ctx, "failed to fetch ticket field from cache", zap.Error(err), zap.Any("ticketFieldId", ticketFieldId))
		}
		return nil
	}
	ticketField := &freshdeskPb.FreshdeskTicketField{}
	err = protojson.Unmarshal([]byte(ticketFieldJsonStr), ticketField)
	if err != nil {
		cxLogger.Error(ctx, "failed to unmarshal ticket field from cache", zap.Error(err), zap.Any("ticketFieldId", ticketFieldId))
		// Corrupted value. Delete this from cache (best-effort).
		goroutine.Run(ctx, 5*time.Second, func(ctx2 context.Context) {
			deleteErr := s.cacheStorage.Delete(ctx2, getCacheKeyForTicketField(ticketFieldId))
			if deleteErr != nil {
				cxLogger.Error(ctx, "failed to delete ticket field key from redis", zap.Error(deleteErr),
					zap.Any("ticketFieldId", ticketFieldId))
			}
		})
		return nil
	}
	return ticketField
}

func getCacheKeyForTicketField(ticketFieldId int64) string {
	return "cx_ticket_field_" + strconv.FormatInt(ticketFieldId, 10)
}

func getCacheKeyForActorTicket(actorId string) string {
	return "cx_actor_home_ticket_" + actorId
}

func (s *Service) SetTicketFieldInCache(ctx context.Context, ticketField *freshdeskPb.FreshdeskTicketField, ticketFieldId int64) {
	ticketFieldBytes, err := protojson.Marshal(ticketField)
	if err != nil {
		cxLogger.Error(ctx, "error while marshaling ticket field for caching", zap.Error(err))
		return
	}
	cacheValidityDuration := s.cxConf.TicketConfig.TicketFieldCacheValidityDuration
	if cacheValidityDuration == 0 {
		// use default duration of 24 hrs if duration is zero
		cacheValidityDuration = 24 * time.Hour
	}
	err = s.cacheStorage.Set(ctx, getCacheKeyForTicketField(ticketFieldId), string(ticketFieldBytes), cacheValidityDuration)
	if err != nil {
		cxLogger.Error(ctx, "error while setting ticket field in cache", zap.Error(err))
		return
	}
	cxLogger.Info(ctx, "set the ticket field in cache successfully")
}

func (s *Service) GetFreshdeskTicketCategories(ctx context.Context, req *ticketPb.GetFreshdeskTicketCategoriesRequest) (*ticketPb.GetFreshdeskTicketCategoriesResponse, error) {
	// get product category field id from config
	productCategoryFieldId := s.cxConf.TicketConfig.ProductCategoryFieldId
	// try fetching ticket field from cache
	freshdeskticketField := s.FetchTicketFieldFromCache(ctx, productCategoryFieldId)
	// if ticket field is still nil after fetching from cache, we try to fetch it from freshdesk
	// fetching the value corresponding to cf_product_category field id,
	// we also get product category details and subcategory in nested format in choices field
	if freshdeskticketField == nil {
		ticketCategoryFieldResp, ticketCategoryFieldErr := s.fdClient.GetTicketField(ctx, &freshdeskPb.GetTicketFieldRequest{
			Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FRESHDESK},
			TicketFieldId: productCategoryFieldId,
		})
		// if there is error while retrieving the field from freshdesk too, we return internal status
		if te := epifigrpc.RPCError(ticketCategoryFieldResp, ticketCategoryFieldErr); te != nil {
			cxLogger.Error(ctx, "error while retrieving product category field", zap.Error(te))
			return &ticketPb.GetFreshdeskTicketCategoriesResponse{
				Status: rpcPb.StatusInternalWithDebugMsg("error while retrieving ticket field from freshdesk"),
			}, nil
		}
		// we set the value retrieved from freshdesk, to be used in response
		freshdeskticketField = ticketCategoryFieldResp.GetFreshdeskTicketField()
		// we also update the value in cache, to be used for future retrievals
		goroutine.Run(ctx, 5*time.Second, func(ctx2 context.Context) {
			s.SetTicketFieldInCache(ctx2, freshdeskticketField, productCategoryFieldId)
		})
	}
	// ticketCategories will store the required details in nested format to be populated in response
	ticketCategories := ticketPb.TicketCategories{}
	// iterate through all the product category field
	for _, productCategoryField := range freshdeskticketField.GetChoices() {
		productCategoryChoice := ticketPb.ProductCategoryChoice{
			ProductCategory: productCategoryField.GetLabel(),
		}
		// iterate through all the product category details fields for respective product category
		for _, productCategoryDetailsField := range productCategoryField.GetChoices() {
			productCategoryDetailsChoice := ticketPb.ProductCategoryDetailsChoice{
				ProductCategoryDetails: productCategoryDetailsField.GetLabel(),
			}
			// iterate through all the subcategory fields for respective product category detail
			for _, subcategoryField := range productCategoryDetailsField.GetChoices() {
				subcategoryChoice := ticketPb.SubcategoryChoice{
					Subcategory: subcategoryField.GetLabel(),
				}
				// append the current subcategory to the product category detail's subcategory choices
				productCategoryDetailsChoice.SubcategoryChoices = append(productCategoryDetailsChoice.SubcategoryChoices, &subcategoryChoice)
			}
			// append the current product category detail to the product category's product category detail choices
			productCategoryChoice.ProductCategoryDetailsChoices = append(productCategoryChoice.ProductCategoryDetailsChoices, &productCategoryDetailsChoice)
		}
		// append the current product category to the ticket category detail's product category choices
		ticketCategories.ProductCategoryChoices = append(ticketCategories.ProductCategoryChoices, &productCategoryChoice)
	}

	return &ticketPb.GetFreshdeskTicketCategoriesResponse{
		Status:           rpcPb.StatusOk(),
		TicketCategories: &ticketCategories,
	}, nil
}

func (s *Service) getProductCategoryEnum(productCategoryStr string) ticketPb.ProductCategory {
	productCatValToEnumMap := mapUtils.ReverseMap(s.cxConf.SupportTicketFreshdeskConfig.ProductCategoryEnumToValueMapping)
	productCategoryEnumStr := strings.ToUpper(productCatValToEnumMap[productCategoryStr])
	productCategoryEnum := ticketPb.ProductCategory(ticketPb.ProductCategory_value[productCategoryEnumStr])
	return productCategoryEnum
}

func (s *Service) GetTicketInfo(ctx context.Context, req *ticketPb.GetTicketInfoRequest) (*ticketPb.GetTicketInfoResponse, error) {
	if req.GetHeader().GetTicket().GetId() == 0 {
		return &ticketPb.GetTicketInfoResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ticket id is mandatory to get ticket info")}, nil
	}

	fetchedTicketDetails, fetchedTicketDetailsErr := s.supportTicketDao.GetById(ctx, req.GetHeader().GetTicket().GetId())
	if fetchedTicketDetailsErr != nil {
		if errors.Is(fetchedTicketDetailsErr, epifierrors.ErrRecordNotFound) {
			return &ticketPb.GetTicketInfoResponse{Status: rpcPb.StatusRecordNotFoundWithDebugMsg("ticket details not found in DB")}, nil
		}
		return &ticketPb.GetTicketInfoResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while fetching ticket details from DB")}, nil
	}
	resp := ticketPb.GetTicketInfoResponse{}
	resp.ProductCategory = fetchedTicketDetails.GetTicket().GetCustomFieldWithValue().GetProductCategory()
	resp.ProductCategoryDetail = fetchedTicketDetails.GetTicket().GetCustomFieldWithValue().GetProductCategoryDetails()
	resp.Subcategory = fetchedTicketDetails.GetTicket().GetCustomFieldWithValue().GetSubCategory()
	resp.ExpectedResolutionTime = fetchedTicketDetails.GetExpectedResolutionTime()
	// convert product category present in customFieldsWithValue to enum for querying additional details like sla etc
	// we are not using GetProductCategory() function on fetchedTicketDetails object as it may contain outdated data
	// the latest updates to product category field are only present in custom fields with value field of ticket object
	productCategoryEnum := s.getProductCategoryEnum(fetchedTicketDetails.GetTicket().GetCustomFieldWithValue().GetProductCategory())
	// in case the enum is unavailable we are not blocking the request, logging the error and returning whatever info is available
	if productCategoryEnum == ticketPb.ProductCategory_PRODUCT_CATEGORY_UNSPECIFIED {
		cxLogger.Error(ctx, "invalid product category present in get request", zap.String("productCategory", fetchedTicketDetails.GetTicket().GetCustomFieldWithValue().GetProductCategory()), zap.Int64(logger.TICKET_ID, req.GetHeader().GetTicket().GetId()))
	}
	// Fetch additional details of ticket like sla, guru link, escalation teams etc mapped to (product category, product category details, subcategory) of ticket
	// These details are fetched from ticket_details_transformation table
	// The product decision taken here is that these additional details like SLA, Guru Link etc will be populated on best effort basis
	fetchedTicketDetailsTransformations, fetchedTicketDetailsTransformationsErr := s.ticketDetailsTransformationDao.GetAllByFilters(ctx,
		dao2.WithProdCatProdCatDetailsSubcatList([]*ticketPb.TicketDetailsTransformation{
			{ProductCategory: productCategoryEnum, ProductCategoryDetails: fetchedTicketDetails.GetTicket().GetCustomFieldWithValue().GetProductCategoryDetails(), Subcategory: fetchedTicketDetails.GetTicket().GetCustomFieldWithValue().GetSubCategory()},
		}),
	)
	if fetchedTicketDetailsTransformationsErr != nil {
		if errors.Is(fetchedTicketDetailsTransformationsErr, epifierrors.ErrRecordNotFound) {
			cxLogger.Error(ctx, "record not found while fetching ticket details transformations", zap.Error(fetchedTicketDetailsTransformationsErr), zap.Int64(logger.TICKET_ID, req.GetHeader().GetTicket().GetId()))
		}
		cxLogger.Error(ctx, "error while fetching ticket details transformations", zap.Error(fetchedTicketDetailsTransformationsErr), zap.Int64(logger.TICKET_ID, req.GetHeader().GetTicket().GetId()))
	} else {
		// Populate the corresponding fields in response based on transformation type
		// Adding nolint here as we are not populating all type of transformations in the below switch case
		// nolint:exhaustive
		for _, transformation := range fetchedTicketDetailsTransformations {
			switch transformation.GetTransformationType() {
			case ticketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_GURU_LINK:
				resp.GuruLink = transformation.GetTransformationValue().GetGuruLink()
			case ticketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_NOTE:
				resp.Note = transformation.GetTransformationValue().GetNote()
			case ticketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_ESCALATION_TEAM:
				resp.EscalationTeams = transformation.GetTransformationValue().GetEscalationTeams()
			case ticketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_SLA_DURATION_MAPPING:
				resp.Sla = transformation.GetTransformationValue().GetSlaDuration()
			case ticketPb.TicketTransformationType_TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_IS_FCR:
				resp.IsFcr = transformation.GetTransformationValue().GetIsFcr()
			}
		}
	}
	resp.Status = rpcPb.StatusOk()
	return &resp, nil
}

func getIndexForProductCategory(productCategory string, ticketCategories *ticketPb.TicketCategories) int {
	for index, productCategoryChoice := range ticketCategories.GetProductCategoryChoices() {
		if productCategoryChoice.GetProductCategory() == productCategory {
			return index
		}
	}
	return -1
}

func getIndexForProductCategoryDetails(productCategoryDetails string, productCategoryChoice *ticketPb.ProductCategoryChoice) int {
	for index, productCategoryDetailsChoice := range productCategoryChoice.GetProductCategoryDetailsChoices() {
		if productCategoryDetailsChoice.GetProductCategoryDetails() == productCategoryDetails {
			return index
		}
	}
	return -1
}

func getIndexForSubcategory(subcategory string, productCategoryDetailsChoice *ticketPb.ProductCategoryDetailsChoice) int {
	for index, subcategoryChoice := range productCategoryDetailsChoice.GetSubcategoryChoices() {
		if subcategoryChoice.GetSubcategory() == subcategory {
			return index
		}
	}
	return -1
}

// nolint:funlen,dupl,ineffassign
func (s *Service) UpdateTicketInfo(ctx context.Context, req *ticketPb.UpdateTicketInfoRequest) (*ticketPb.UpdateTicketInfoResponse, error) {
	// checking if the request contains a valid ticket id for update
	if req.GetHeader().GetTicket().GetId() == 0 {
		return &ticketPb.UpdateTicketInfoResponse{Status: rpcPb.StatusInvalidArgumentWithDebugMsg("ticket and ticket id are mandatory to update ticket")}, nil
	}
	// product category is mandatory for update
	if len(req.GetProductCategory()) == 0 {
		return &ticketPb.UpdateTicketInfoResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("product category is mandatory for update"),
		}, nil
	}
	// validate product category from mapping present in config
	productCatValToEnumMap := mapUtils.ReverseMap(s.cxConf.SupportTicketFreshdeskConfig.ProductCategoryEnumToValueMapping)
	_, isProductCategoryPresentInConfig := productCatValToEnumMap[req.GetProductCategory()]
	if !isProductCategoryPresentInConfig {
		cxLogger.Error(ctx, "invalid product category received in request", zap.String("productCategory", req.GetProductCategory()), zap.Int64(logger.TICKET_ID, req.GetHeader().GetTicket().GetId()))
		return &ticketPb.UpdateTicketInfoResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid product category received in request"),
		}, nil
	}
	freshdeskTicketCategoriesResp, freshdeskTicketCategoriesErr := s.GetFreshdeskTicketCategories(ctx, &ticketPb.GetFreshdeskTicketCategoriesRequest{Header: req.GetHeader()})
	// if there is error while retrieving the field from freshdesk too, we return internal status
	if te := epifigrpc.RPCError(freshdeskTicketCategoriesResp, freshdeskTicketCategoriesErr); te != nil {
		cxLogger.Error(ctx, "error while retrieving freshdesk ticket categories for validation", zap.Error(te))

		return &ticketPb.UpdateTicketInfoResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while retrieving ticket categories from freshdesk for validation"),
		}, nil
	}

	// we will use these variables to store the indexes of respective ticket categories
	// these variables will be then used for validation in the nested response of getFreshdeskTicketCategories
	productCategoryIndex := -1
	productCategoryDetailsIndex := -1
	subcategoryIndex := -1
	// get product category field id from config
	productCategoryFieldId := s.cxConf.TicketConfig.ProductCategoryFieldId
	// try to find the index at which given product category is present in the response of getFreshdeskTicketCategories
	productCategoryIndex = getIndexForProductCategory(req.GetProductCategory(), freshdeskTicketCategoriesResp.GetTicketCategories())
	// validate the index at which the product category is found
	// if the index where the product category is found is invalid, we return status invalid argument
	if productCategoryIndex < 0 || productCategoryIndex >= len(freshdeskTicketCategoriesResp.GetTicketCategories().GetProductCategoryChoices()) {
		// invalidate the cache on best effort basis
		// this is done as a new product category maybe updated on freshdesk which is not in cache
		goroutine.Run(ctx, 5*time.Second, func(ctx2 context.Context) {
			deleteErr := s.cacheStorage.Delete(ctx2, getCacheKeyForTicketField(productCategoryFieldId))
			if deleteErr != nil {
				cxLogger.Error(ctx, "failed to delete ticket field key from redis", zap.Error(deleteErr),
					zap.Any("ticketFieldId", productCategoryFieldId))
			}
		})
		return &ticketPb.UpdateTicketInfoResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid product category received in request"),
		}, nil
	}
	chosenProductCategory := freshdeskTicketCategoriesResp.GetTicketCategories().GetProductCategoryChoices()[productCategoryIndex]
	// if product category details is available in request, we will validate it
	if len(req.GetProductCategoryDetails()) > 0 {
		// try to find the index at which given product category details is present in the response of getFreshdeskTicketCategories
		productCategoryDetailsIndex = getIndexForProductCategoryDetails(req.GetProductCategoryDetails(), chosenProductCategory)
		// validate the index at which the product category details is found
		// if the index where the product category details is found is invalid, we return status invalid argument
		if productCategoryDetailsIndex < 0 || productCategoryDetailsIndex >= len(chosenProductCategory.GetProductCategoryDetailsChoices()) {
			// invalidate the cache on best effort basis
			// this is done as a new product category details maybe updated on freshdesk which is not in cache
			goroutine.Run(ctx, 5*time.Second, func(ctx2 context.Context) {
				deleteErr := s.cacheStorage.Delete(ctx2, getCacheKeyForTicketField(productCategoryFieldId))
				if deleteErr != nil {
					cxLogger.Error(ctx, "failed to delete ticket field key from redis", zap.Error(deleteErr),
						zap.Any("ticketFieldId", productCategoryFieldId))
				}
			})
			return &ticketPb.UpdateTicketInfoResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid product category details received in request"),
			}, nil
		}
	}
	// if subcategory is available in request, we will validate it
	if len(req.GetSubcategory()) > 0 {
		if len(req.GetProductCategoryDetails()) == 0 {
			return &ticketPb.UpdateTicketInfoResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("product category details is required for updating subcategory"),
			}, nil
		}
		chosenProductCategoryDetails := chosenProductCategory.GetProductCategoryDetailsChoices()[productCategoryDetailsIndex]
		// try to find the index at which given subcategory is present in the response of getFreshdeskTicketCategories
		subcategoryIndex = getIndexForSubcategory(req.GetSubcategory(), chosenProductCategoryDetails)
		// validate the index at which the subcategory is found
		// if the index where the subcategory is found is invalid, we return status invalid argument
		if subcategoryIndex < 0 || subcategoryIndex >= len(chosenProductCategoryDetails.GetSubcategoryChoices()) {
			// invalidate the cache on best effort basis
			// this is done as a new subcategory maybe updated on freshdesk which is not in cache
			goroutine.Run(ctx, 5*time.Second, func(ctx2 context.Context) {
				deleteErr := s.cacheStorage.Delete(ctx2, getCacheKeyForTicketField(productCategoryFieldId))
				if deleteErr != nil {
					cxLogger.Error(ctx, "failed to delete ticket field key from redis", zap.Error(deleteErr),
						zap.Any("ticketFieldId", productCategoryFieldId))
				}
			})
			return &ticketPb.UpdateTicketInfoResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("invalid subcategory received in request"),
			}, nil
		}
	}
	// publishing a message to update the received values on freshdesk
	_, err := s.updateTicketPublisher.Publish(ctx, &consumer.UpdateTicketEventRequest{
		UpdateTicketPayload: &consumer.UpdateTicketPayload{
			UpdateTicketRequestType: consumer.UpdateTicketRequestType_UPDATE_TICKET_REQUEST_TYPE_SHERLOCK_TICKET_INFO,
			DetailsPayload: &consumer.UpdateTicketPayload_SherlockTicketInfoUpdatePayload{SherlockTicketInfoUpdatePayload: &consumer.SherlockTicketInfoUpdatePayload{
				Ticket: &freshdeskPb.TicketRaw{
					Id: req.GetHeader().GetTicket().GetId(),
					CustomFields: &freshdeskPb.CustomFieldsWithValue{
						ProductCategory:        req.GetProductCategory(),
						ProductCategoryDetails: req.GetProductCategoryDetails(),
						SubCategory:            req.GetSubcategory(),
					},
				},
			}},
		},
	})
	// in case of facing error while publishing message, we return status internal
	if err != nil {
		cxLogger.Error(ctx, "error while publishing message to update ticket info on freshdesk", zap.Error(err), zap.Any(logger.TICKET_ID, req.GetHeader().GetTicket().GetId()))
		return &ticketPb.UpdateTicketInfoResponse{Status: rpcPb.StatusInternalWithDebugMsg("error while publishing message to update info")}, nil
	}
	// otherwise we return success
	return &ticketPb.UpdateTicketInfoResponse{Status: rpcPb.StatusOk()}, nil
}

// UpdateTicketAsync accepts cx ticket to be updated and publishes it to update ticket queue
func (s *Service) UpdateTicketAsync(ctx context.Context, req *ticketPb.UpdateTicketAsyncRequest) (*ticketPb.UpdateTicketAsyncResponse, error) {
	validationErr := s.validateUpdateTicketRequest(req.GetTicket())
	if validationErr != nil {
		logger.Error(ctx, "error validating update ticket request", zap.Error(validationErr), zap.Int64(logger.TICKET_ID, req.GetTicket().GetId()))
		return &ticketPb.UpdateTicketAsyncResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	_, err := s.updateTicketPublisher.Publish(ctx, &consumer.UpdateTicketEventRequest{
		UpdateTicketPayload: &consumer.UpdateTicketPayload{
			UpdateTicketRequestType: consumer.UpdateTicketRequestType_UPDATE_TICKET_REQUEST_TYPE_CX_TICKET,
			DetailsPayload: &consumer.UpdateTicketPayload_CxTicketUpdatePayload{
				CxTicketUpdatePayload: &consumer.CxTicketUpdatePayload{
					Ticket: req.GetTicket(),
				},
			},
		},
	})
	if err != nil {
		cxLogger.Error(ctx, "error while publishing message to update ticket queue", zap.Error(err), zap.Int64(logger.TICKET_ID, req.GetTicket().GetId()),
			zap.Any(logger.REQUEST_TYPE, consumer.UpdateTicketRequestType_UPDATE_TICKET_REQUEST_TYPE_CX_TICKET))
		return &ticketPb.UpdateTicketAsyncResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while publishing message to update ticket queue"),
		}, nil
	}
	return &ticketPb.UpdateTicketAsyncResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) AddPrivateNoteAsync(ctx context.Context, req *ticketPb.AddPrivateNoteAsyncRequest) (*ticketPb.AddPrivateNoteAsyncResponse, error) {
	if validateErr := validateAddPrivateNoteAsyncRequest(req); validateErr != nil {
		cxLogger.Error(ctx, "error validating Add private note async request", zap.Error(validateErr), zap.Int64(logger.TICKET_ID, req.GetTicketId()),
			zap.Any(logger.REQUEST_TYPE, consumer.CreateTicketRequestType_CREATE_TICKET_REQUEST_TYPE_ADD_PRIVATE_NOTE))
		return &ticketPb.AddPrivateNoteAsyncResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(validateErr.Error()),
		}, nil
	}
	_, err := s.createTicketPublisher.Publish(ctx, &consumer.CreateTicketEventRequest{
		CreateTicketPayload: &consumer.CreateTicketPayload{
			CreateTicketRequestType: consumer.CreateTicketRequestType_CREATE_TICKET_REQUEST_TYPE_ADD_PRIVATE_NOTE,
			DetailsPayload: &consumer.CreateTicketPayload_AddPrivateNotePayload{
				AddPrivateNotePayload: &consumer.AddPrivateNotePayload{
					TicketId:     req.GetTicketId(),
					Body:         req.GetBody(),
					NotifyEmails: req.GetNotifyEmails(),
					AgentId:      req.GetAgentId(),
				},
			},
		},
	})
	if err != nil {
		cxLogger.Error(ctx, "error while publishing private note event to create ticket queue", zap.Error(err), zap.Int64(logger.TICKET_ID, req.GetTicketId()),
			zap.Any(logger.REQUEST_TYPE, consumer.CreateTicketRequestType_CREATE_TICKET_REQUEST_TYPE_ADD_PRIVATE_NOTE))
		return &ticketPb.AddPrivateNoteAsyncResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error while publishing private note event to create ticket queue"),
		}, nil
	}
	return &ticketPb.AddPrivateNoteAsyncResponse{Status: rpc.StatusOk()}, nil
}

func validateAddPrivateNoteAsyncRequest(req *ticketPb.AddPrivateNoteAsyncRequest) error {
	if req.GetTicketId() == 0 {
		return errors.New("ticket id is missing in AddPrivateNoteAsyncRequest")
	}
	if req.GetBody() == "" {
		return errors.New("body text is missing in AddPrivateNoteAsyncRequest")
	}
	return nil
}

func (s *Service) CreateTicket(ctx context.Context, req *ticketPb.CreateTicketRequest) (*ticketPb.CreateTicketResponse, error) {
	if req.GetIsAsyncCreationRequired() {
		_, publishErr := s.createTicketPublisher.Publish(ctx, &consumer.CreateTicketEventRequest{
			CreateTicketPayload: &consumer.CreateTicketPayload{
				CreateTicketRequestType: consumer.CreateTicketRequestType_CREATE_TICKET_REQUEST_TYPE_CX_TICKET,
				DetailsPayload: &consumer.CreateTicketPayload_CxTicketPayload{
					CxTicketPayload: req.GetTicket(),
				},
			},
		})
		if publishErr != nil {
			logger.Error(ctx, "failed to publish ticket for creation", zap.Error(publishErr),
				zap.String(logger.ACTOR_ID_V2, req.GetTicket().GetActorId()))
			return &ticketPb.CreateTicketResponse{Status: rpcPb.StatusInternal()}, nil
		} else {
			return &ticketPb.CreateTicketResponse{Status: rpcPb.StatusOk(), Ticket: req.GetTicket()}, nil
		}
	}

	cxTicket := req.GetTicket()
	issueCategory, err := s.issueCategoryManager.GetValueById(ctx, cxTicket.GetIssueCategoryId())
	if err != nil {
		cxLogger.Error(ctx, "Failed to get issue category by Id", zap.Error(err), zap.String(logger.ISSUE_CATEGORY_ID, cxTicket.GetIssueCategoryId()))
	} else {
		cxLogger.Info(ctx, "Successfully fetched issue category from Id for creating ticket", zap.String(logger.ISSUE_CATEGORY_ID, issueCategory.GetId()),
			zap.String(logger.PRODUCT_CATEGORY, issueCategory.GetProductCategory()), zap.String(logger.PRODUCT_CATEGORY_DETAILS, issueCategory.GetProductCategoryDetails()),
			zap.String(logger.PRODUCT_SUB_CATEGORY, issueCategory.GetSubCategory()))
	}
	vgTicketRaw := cxTicket.ToVgTicketRaw(s.cxConf.SupportTicketFreshdeskConfig, s.cxConf.Application.Environment, issueCategory)

	vgResp, err := s.fdClient.CreateTicketRaw(ctx, &freshdeskPb.CreateTicketRawRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FRESHDESK},
		Ticket: vgTicketRaw,
	})
	if te := epifigrpc.RPCError(vgResp, err); te != nil {
		cxLogger.Error(ctx, "failed to invoke vg create ticket raw", zap.Error(te))
		if vgResp.GetStatus().IsInvalidArgument() {
			return &ticketPb.CreateTicketResponse{
				Status: vgResp.GetStatus(),
			}, nil
		}
		return &ticketPb.CreateTicketResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	cxTicket.Id = vgResp.GetTicket().GetId()
	// emit event to track ticket creation for specific entity id if the caller has explicitly mentioned an entity id
	if req.GetTicket().GetActorId() != "" && req.GetTicket().GetCustomFields().GetEntityId() != "" {
		goroutine.Run(ctx, 2*time.Second, func(gCtx context.Context) {
			s.eventBroker.AddToBatch(ctx, events2.NewTicketCreationEvent(req.GetTicket().GetActorId(),
				req.GetTicket().GetCustomFields().GetEntityId(), strconv.FormatInt(cxTicket.GetId(), 10)))

			// once a ticket is created for issue reporting flow,
			// we will update it in the user query log corresponding to that query
			// entity id here represents the query id for which the ticket is created
			updateErr := s.userQueryLogDao.Update(gCtx, &irPb.UserQueryLog{
				Id:       req.GetTicket().GetCustomFields().GetEntityId(),
				TicketId: cxTicket.GetId(),
			}, []irPb.UserQueryLogUpdateMask{irPb.UserQueryLogUpdateMask_USER_QUERY_LOG_UPDATE_MASK_TICKET_ID})
			// this update is done on the best effort basis, hence we are not returning any error
			if updateErr != nil && !errors.Is(updateErr, epifierrors.ErrNoRowsAffected) {
				cxLogger.Error(gCtx, "error while updating user query log with ticket id", zap.Error(updateErr))
			}
		})
	}

	return &ticketPb.CreateTicketResponse{
		Status: rpc.StatusOk(),
		Ticket: cxTicket,
	}, nil
}

func (s *Service) GetSupportTicketByIdForApp(ctx context.Context, req *ticketPb.GetSupportTicketByIdForAppRequest) (*ticketPb.GetSupportTicketByIdForAppResponse, error) {
	resp, err := s.GetSingleTicketResponse(ctx, &ticketPb.GetSupportTicketsRequest{TicketId: req.GetTicketId()})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		cxLogger.Error(ctx, "error while fetching support ticket with given id", zap.Error(te), zap.Int64(logger.TICKET_ID, req.GetTicketId()))
		return &ticketPb.GetSupportTicketByIdForAppResponse{
			Status: resp.GetStatus(),
		}, nil
	}
	if len(resp.GetTickets()) == 0 {
		cxLogger.Error(ctx, "ticket not found for given id", zap.Int64(logger.TICKET_ID, req.GetTicketId()))
		return &ticketPb.GetSupportTicketByIdForAppResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
	// we need to ensure if the ticket requested belongs to the actor id for requester
	ticket := resp.GetTickets()[0]
	if ticket.GetActorId() != req.GetActorId() {
		cxLogger.Info(ctx, "requested user's and ticket's actorId not matching",
			zap.String("TicketActorId", ticket.GetActorId()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()),
			zap.Int64(logger.TICKET_ID, req.GetTicketId()))
		return &ticketPb.GetSupportTicketByIdForAppResponse{
			Status: rpc.StatusPermissionDenied(),
		}, nil
	}
	ticketForUser, conversionErr := s.convertToTicketForApp(ctx, resp.GetTickets()[0], true)
	if conversionErr != nil {
		cxLogger.Error(ctx, "failed conversion to in app ticket", zap.Error(conversionErr), zap.Int64(logger.TICKET_ID, req.GetTicketId()))
		return &ticketPb.GetSupportTicketByIdForAppResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	return &ticketPb.GetSupportTicketByIdForAppResponse{
		Status: rpc.StatusOk(),
		Ticket: ticketForUser,
	}, nil
}

func (s *Service) validateUpdateTicketRequest(ticket *ticketPb.Ticket) error {
	if ticket.GetId() == 0 {
		return errors.New("ticket id is required to update ticket")
	}
	tempTicket := &ticketPb.Ticket{Id: ticket.GetId()}
	if proto.Equal(tempTicket, ticket) {
		return errors.New("no ticket fields found for update")
	}
	return nil
}

func (s *Service) FetchLatestResolvedTicketIdForCSAT(ctx context.Context, req *ticketPb.FetchLatestResolvedTicketIdForCSATRequest) (*ticketPb.FetchLatestResolvedTicketIdForCSATResponse, error) {
	csatConf := s.cxConf.TicketConfig.CsatConfig
	pageSize := csatConf.PageSize
	pageLimit := csatConf.PageLimit

	ticketFilters := &ticketPb.TicketFilters{
		ActorIdList: []string{req.GetActorId()},
		StatusList:  []ticketPb.Status{ticketPb.Status_STATUS_RESOLVED},
		FromTime:    timestampPb.New(time.Now().Add(-csatConf.CsatEligibilityWindow)),
		ToTime:      timestampPb.New(time.Now()),
	}

	var (
		pageToken      *pagination.PageToken
		eligibleTicket *ticketPb.TicketDetails
	)

	// Keep fetching tickets until we find eligible ones or run out of tickets
	for page := 1; page <= pageLimit; page++ {
		ticketDetailList, pageCtxResp, err := s.supportTicketDao.GetTicketsByUpdatedAt(ctx, ticketFilters, pageToken, pageSize)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				break
			}
			logger.Error(ctx, "error while fetching tickets", zap.Error(err))
			return &ticketPb.FetchLatestResolvedTicketIdForCSATResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching tickets"),
			}, nil
		}

		// Check each ticket for eligibility
		for _, ticket := range ticketDetailList {
			// Check if the ticket has any survey details
			if ticket.GetTicketMeta() != nil && len(ticket.GetTicketMeta().GetSurveyDetails()) > 0 {
				// Get the latest survey
				latestSurvey := ticket.GetTicketMeta().GetSurveyDetails()[len(ticket.GetTicketMeta().GetSurveyDetails())-1]
				// Check if the latest survey is waiting for user input
				if latestSurvey.GetStatus() == ticketPb.SurveyStatus_SURVEY_STATUS_WAITING_ON_USER {
					eligibleTicket = ticket
					break
				}
			}
		}

		if eligibleTicket != nil {
			break
		}

		// If no more pages, break
		if !pageCtxResp.GetHasAfter() {
			break
		}

		// Set up token for next page
		pageToken = &pagination.PageToken{}
		if err := pageToken.Unmarshal(pageCtxResp.GetAfterToken()); err != nil {
			logger.Error(ctx, "error unmarshalling page token", zap.Error(err))
			return &ticketPb.FetchLatestResolvedTicketIdForCSATResponse{
				Status: rpc.StatusInternalWithDebugMsg("error unmarshalling page token"),
			}, nil
		}
	}

	if eligibleTicket == nil {
		return &ticketPb.FetchLatestResolvedTicketIdForCSATResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	return &ticketPb.FetchLatestResolvedTicketIdForCSATResponse{
		Status:   rpc.StatusOk(),
		TicketId: eligibleTicket.GetId(),
	}, nil
}
