package cash

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	accountsPb "github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	actorMock "github.com/epifi/gamma/api/actor/mocks"
	cxPb "github.com/epifi/gamma/api/cx"
	payoutPb "github.com/epifi/gamma/api/cx/payout"
	orderMock "github.com/epifi/gamma/api/order/mocks"
	"github.com/epifi/gamma/api/pay"
	payMocksPb "github.com/epifi/gamma/api/pay/mocks"
	"github.com/epifi/gamma/api/paymentinstrument"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	accountPiMocks "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	"github.com/epifi/gamma/api/timeline"
	timelineMocks "github.com/epifi/gamma/api/timeline/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	mock_dao "github.com/epifi/gamma/cx/test/mocks/payout/dao"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"
)

func TestCashPayoutProcessor_ValidatePayoutRequest(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockPayoutDao := mock_dao.NewMockIPayoutRequestDAO(ctr)
	mockAccountPIClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockOrderClient := orderMock.NewMockOrderServiceClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockActorClient := actorMock.NewMockActorClient(ctr)
	mockPIClient := piMocks.NewMockPiClient(ctr)
	mockPayClient := payMocksPb.NewMockPayClient(ctr)
	mockDelayPublisher := queueMocks.NewMockDelayPublisher(ctr)
	genConf, err := cxGenConf.Load()
	if err != nil {
		t.Errorf("Error while loading config")
		return
	}

	logger.Init(genConf.Application().Environment)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		ctx	context.Context
		request	*payoutPb.RaisePayoutRequest
	}
	tests := []struct {
		name	string
		args	args
		want	bool
		wantErr	bool
	}{
		{
			name:	"no valid amount or payout type field",
			args: args{
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "123"},
						TicketId:	1234,
						AgentEmail:	"<EMAIL>",
					},
					Reason:	"test-reason",
				},
			},
			want:		false,
			wantErr:	true,
		},
		{
			name:	"no reason field",
			args: args{
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "123"},
						TicketId:	1234,
						AgentEmail:	"<EMAIL>",
					},
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(100).GetPb()}},
				},
			},
			want:		false,
			wantErr:	true,
		},
		{
			name:	"no ticketId field",
			args: args{
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "123"},
						AgentEmail:	"<EMAIL>",
					},
					Reason:		"test-reason",
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(100).GetPb()}},
				},
			},
			want:		false,
			wantErr:	true,
		},
		{
			name:	"no id field in actor",
			args: args{
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{},
						TicketId:	1234,
						AgentEmail:	"<EMAIL>",
					},
					Reason:		"test-reason",
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(100).GetPb()}},
				},
			},
			want:		false,
			wantErr:	true,
		},
		{
			name:	"no agent email",
			args: args{
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "3456"},
						TicketId:	1234,
					},
					Reason:		"test-reason",
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(100).GetPb()}},
				},
			},
			want:		false,
			wantErr:	true,
		},
		{
			name:	"amount less than min allowed value",
			args: args{
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "3456"},
						TicketId:	1234,
						AgentEmail:	"<EMAIL>",
					},
					Reason:		"test-reason",
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(0).GetPb()}},
				},
			},
			want:		false,
			wantErr:	true,
		},
		{
			name:	"amount greater than max allowed value",
			args: args{
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "3456"},
						TicketId:	1234,
						AgentEmail:	"<EMAIL>",
					},
					Reason:		"test-reason",
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(1001).GetPb()}},
				},
			},
			want:		false,
			wantErr:	true,
		},
		{
			name:	"success - valid request",
			args: args{
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "3456"},
						TicketId:	1234,
						AgentEmail:	"<EMAIL>",
					},
					Reason:		"test-reason",
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(50).GetPb()}},
				},
			},
			want:		true,
			wantErr:	false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewCashPayoutProcessor(mockPayoutDao, mockAccountPIClient, mockOrderClient, genConf.Payout(), mockTimelineClient, mockActorClient, mockPIClient, mockDelayPublisher, mockPayClient)
			got, err := p.ValidatePayoutRequest(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("ValidatePayoutRequest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ValidatePayoutRequest() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCashPayoutProcessor_RaisePayout(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockPayoutDao := mock_dao.NewMockIPayoutRequestDAO(ctr)
	mockAccountPIClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockOrderClient := orderMock.NewMockOrderServiceClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockActorClient := actorMock.NewMockActorClient(ctr)
	mockPIClient := piMocks.NewMockPiClient(ctr)
	mockPayClient := payMocksPb.NewMockPayClient(ctr)
	mockDelayPublisher := queueMocks.NewMockDelayPublisher(ctr)
	genConf, err := cxGenConf.Load()
	if err != nil {
		t.Errorf("Error while loading config")
		return
	}

	logger.Init(genConf.Application().Environment)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		request	*payoutPb.RaisePayoutRequest
	}
	tests := []struct {
		name	string
		args	args
		want	*payoutPb.PayoutDetail
		wantErr	bool
	}{
		{
			name:	"error while checking for auto approval",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().GetPayoutRequests(context.Background(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("failed to fetch payout list in given timeframe")),
				},
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "123"},
						TicketId:	1234,
						AgentEmail:	"<EMAIL>",
						AccessLevel:	2,
					},
					Reason:	"test-reason",
				},
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"error while adding payout request in db",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().GetPayoutRequests(context.Background(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*payoutPb.PayoutDetail{
						{
							PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
							PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(100).GetPb()}},
						},
					}, nil),
					mockPayoutDao.EXPECT().GetPayoutRequests(context.Background(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*payoutPb.PayoutDetail{
						{
							PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
							PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(100).GetPb()}},
						},
					}, nil),
					mockPayoutDao.EXPECT().GetPayoutRequests(context.Background(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*payoutPb.PayoutDetail{
						{
							PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
							PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(100).GetPb()}},
						},
					}, nil),
					mockPayoutDao.EXPECT().Create(context.Background(), gomock.Any()).Return(nil, errors.New("error while adding payout request in db")),
				},
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "3456"},
						TicketId:	1234,
						AgentEmail:	"<EMAIL>",
						AccessLevel:	2,
					},
					Reason:		"test-reason",
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(500).GetPb()}},
				},
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"payout auto approved - failed to process payout",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().Create(context.Background(), gomock.Any()).Return(&payoutPb.PayoutDetail{Id: "1234", ApprovalState: payoutPb.ApprovalState_AUTO_APPROVED}, nil),
					mockPayoutDao.EXPECT().MarkPayoutAsProcessing(context.Background(), gomock.Any()).Return(nil, errors.New("error while updating the payout status of payout request")),
					mockPayoutDao.EXPECT().UpdatePayout(context.Background(), gomock.Any(), gomock.Any()).Return(nil, nil),
				},
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "3456"},
						TicketId:	1234,
						AgentEmail:	"<EMAIL>",
						AccessLevel:	2,
					},
					Reason:		"test-reason",
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(500).GetPb()}},
				},
			},
			want:		nil,
			wantErr:	true,
		},
		{
			name:	"payout successfully raised - without auto approval",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().Create(context.Background(), gomock.Any()).Return(&payoutPb.PayoutDetail{Id: "1234", ApprovalState: payoutPb.ApprovalState_PENDING}, nil),
				},
				ctx:	context.Background(),
				request: &payoutPb.RaisePayoutRequest{
					Header: &cxPb.Header{
						Actor:		&types.Actor{Id: "3456"},
						TicketId:	1234,
						AgentEmail:	"<EMAIL>",
						AccessLevel:	2,
					},
					Reason:		"test-reason",
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(200).GetPb()}},
				},
			},
			want:		&payoutPb.PayoutDetail{Id: "1234", ApprovalState: payoutPb.ApprovalState_PENDING},
			wantErr:	false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewCashPayoutProcessor(mockPayoutDao, mockAccountPIClient, mockOrderClient, genConf.Payout(), mockTimelineClient, mockActorClient, mockPIClient, mockDelayPublisher, mockPayClient)
			got, err := p.RaisePayout(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("RaisePayout() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RaisePayout() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCashPayoutProcessor_CanAutoApprovePayout(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockPayoutDao := mock_dao.NewMockIPayoutRequestDAO(ctr)
	mockAccountPIClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockOrderClient := orderMock.NewMockOrderServiceClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockActorClient := actorMock.NewMockActorClient(ctr)
	mockPIClient := piMocks.NewMockPiClient(ctr)
	mockPayClient := payMocksPb.NewMockPayClient(ctr)
	mockDelayPublisher := queueMocks.NewMockDelayPublisher(ctr)
	genConf, err := cxGenConf.Load()
	if err != nil {
		t.Errorf("Error while loading config")
		return
	}

	logger.Init(genConf.Application().Environment)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		request	*payoutPb.PayoutDetail
	}
	tests := []struct {
		name	string
		args	args
		want	bool
		wantErr	bool
	}{
		{
			name:	"error while performing timeframe check",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().GetPayoutRequests(context.Background(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error while fetching payout")),
				},
				ctx:		context.Background(),
				request:	&payoutPb.PayoutDetail{},
			},
			want:		false,
			wantErr:	true,
		},
		{
			name:	"timeframe check failed",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().GetPayoutRequests(context.Background(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*payoutPb.PayoutDetail{
						{
							PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
							PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(5000).GetPb()}},
						},
					}, nil),
				},
				ctx:	context.Background(),
				request: &payoutPb.PayoutDetail{
					PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
					PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(50).GetPb()}},
				},
			},
			want:		false,
			wantErr:	false,
		},
		{
			name:	"payout auto approved - all checks passed",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().GetPayoutRequests(context.Background(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*payoutPb.PayoutDetail{
						{
							PayoutType:	payoutPb.PayoutType_PAYOUT_TYPE_CASH,
							PayoutValue:	&payoutPb.PayoutValue{PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(100).GetPb()}},
						},
					}, nil),
				},
				ctx:	context.Background(),
				request: &payoutPb.PayoutDetail{
					TicketId:	1234,
					PayoutValue: &payoutPb.PayoutValue{
						PayoutValue: &payoutPb.PayoutValue_Cash{Cash: money.AmountINR(200).GetPb()},
					},
				},
			},
			want:		true,
			wantErr:	false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewCashPayoutProcessor(mockPayoutDao, mockAccountPIClient, mockOrderClient, genConf.Payout(), mockTimelineClient, mockActorClient, mockPIClient, mockDelayPublisher, mockPayClient)
			got, err := p.CanAutoApprovePayout(tt.args.ctx, tt.args.request)
			if (err != nil) != tt.wantErr {
				t.Errorf("CanAutoApprovePayout() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CanAutoApprovePayout() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCashPayoutProcessor_ProcessPayout(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	mockPayoutDao := mock_dao.NewMockIPayoutRequestDAO(ctr)
	mockAccountPIClient := accountPiMocks.NewMockAccountPIRelationClient(ctr)
	mockOrderClient := orderMock.NewMockOrderServiceClient(ctr)
	mockTimelineClient := timelineMocks.NewMockTimelineServiceClient(ctr)
	mockActorClient := actorMock.NewMockActorClient(ctr)
	mockPIClient := piMocks.NewMockPiClient(ctr)
	mockPayClient := payMocksPb.NewMockPayClient(ctr)
	mockDelayPublisher := queueMocks.NewMockDelayPublisher(ctr)
	genConf, err := cxGenConf.Load()
	if err != nil {
		t.Errorf("Error while loading config")
		return
	}

	logger.Init(genConf.Application().Environment)

	defer func() {
		ctr.Finish()
	}()

	type args struct {
		mocks	[]interface{}
		ctx	context.Context
		payout	*payoutPb.PayoutDetail
	}
	tests := []struct {
		name	string
		args	args
		wantErr	bool
	}{
		{
			name:	"error while marking payout status as processing",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().MarkPayoutAsProcessing(context.Background(), gomock.Any()).Return(nil, errors.New("error while updating the payout status of request")),
					mockPayoutDao.EXPECT().UpdatePayout(context.Background(), gomock.Any(), gomock.Any()).Return(nil, nil),
				},
				ctx:	context.Background(),
				payout: &payoutPb.PayoutDetail{
					Id: "1234",
				},
			},
			wantErr:	true,
		},
		{
			name:	"error while creating timeline",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().MarkPayoutAsProcessing(context.Background(), gomock.Any()).Return(&payoutPb.PayoutDetail{Id: "1234", PayoutStatus: payoutPb.PayoutStatus_PROCESSING}, nil),
					mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), gomock.Any()).Return(nil, errors.New("error while fetching actor details")),
					mockPayoutDao.EXPECT().UpdatePayout(context.Background(), gomock.Any(), gomock.Any()).Return(nil, nil),
				},
				ctx:	context.Background(),
				payout: &payoutPb.PayoutDetail{
					Id:		"1234",
					ActorId:	"3456",
				},
			},
			wantErr:	true,
		},
		{
			name:	"error while publishing status check event",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().MarkPayoutAsProcessing(context.Background(), gomock.Any()).Return(&payoutPb.PayoutDetail{Id: "1234", PayoutStatus: payoutPb.PayoutStatus_PROCESSING}, nil),
					mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), gomock.Any()).Return(&actor.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "sample-actor-1"}, Status: rpc.StatusOk()}, nil),
					mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), gomock.Any()).Return(&actor.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "sample-actor-2"}, Status: rpc.StatusOk()}, nil),
					mockTimelineClient.EXPECT().Create(context.Background(), gomock.Any()).Return(&timeline.CreateResponse{Status: rpc.StatusOk()}, nil),
					mockDelayPublisher.EXPECT().PublishWithDelay(context.Background(), gomock.Any(), gomock.Any()).Return("", errors.New("error while publishing message")),
					mockPayoutDao.EXPECT().UpdatePayout(context.Background(), gomock.Any(), gomock.Any()).Return(nil, nil),
				},
				ctx:	context.Background(),
				payout: &payoutPb.PayoutDetail{
					Id:		"1234",
					ActorId:	"3456",
				},
			},
			wantErr:	true,
		},
		{
			name:	"error while processing payout",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().MarkPayoutAsProcessing(context.Background(), gomock.Any()).Return(&payoutPb.PayoutDetail{Id: "1234", PayoutStatus: payoutPb.PayoutStatus_PROCESSING}, nil),
					mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), gomock.Any()).Return(&actor.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "sample-actor-1"}, Status: rpc.StatusOk()}, nil),
					mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), gomock.Any()).Return(&actor.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "sample-actor-2"}, Status: rpc.StatusOk()}, nil),
					mockTimelineClient.EXPECT().Create(context.Background(), gomock.Any()).Return(&timeline.CreateResponse{Status: rpc.StatusOk()}, nil),
					mockDelayPublisher.EXPECT().PublishWithDelay(context.Background(), gomock.Any(), gomock.Any()).Return("sample-message-id", nil),
					mockAccountPIClient.EXPECT().GetByActorId(context.Background(), gomock.Any()).Return(&accountPiPb.GetByActorIdResponse{Status: rpc.StatusInternal()}, errors.New("get pi by actor call unsuccessful")),
					mockPayoutDao.EXPECT().UpdatePayout(context.Background(), gomock.Any(), gomock.Any()).Return(nil, nil),
				},
				ctx:	context.Background(),
				payout: &payoutPb.PayoutDetail{
					Id:		"1234",
					ActorId:	"3456",
				},
			},
			wantErr:	true,
		},
		{
			name:	"payout successfully processed",
			args: args{
				mocks: []interface{}{
					mockPayoutDao.EXPECT().MarkPayoutAsProcessing(context.Background(), gomock.Any()).Return(&payoutPb.PayoutDetail{Id: "1234", PayoutStatus: payoutPb.PayoutStatus_PROCESSING}, nil),
					mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), gomock.Any()).Return(&actor.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "sample-actor-1"}, Status: rpc.StatusOk()}, nil),
					mockActorClient.EXPECT().GetEntityDetailsByActorId(context.Background(), gomock.Any()).Return(&actor.GetEntityDetailsByActorIdResponse{Name: &commontypes.Name{FirstName: "sample-actor-2"}, Status: rpc.StatusOk()}, nil),
					mockTimelineClient.EXPECT().Create(context.Background(), gomock.Any()).Return(&timeline.CreateResponse{Status: rpc.StatusOk()}, nil),
					mockDelayPublisher.EXPECT().PublishWithDelay(context.Background(), gomock.Any(), gomock.Any()).Return("sample-message-id", nil),
					mockAccountPIClient.EXPECT().GetByActorId(context.Background(), gomock.Any()).Return(&accountPiPb.GetByActorIdResponse{Status: rpc.StatusOk(), AccountPis: []*accountPiPb.AccountPI{}}, nil),
					mockPIClient.EXPECT().GetPIsByIds(context.Background(), gomock.Any()).Return(&paymentinstrument.GetPIsByIdsResponse{Status: rpc.StatusOk(), Paymentinstruments: []*piPb.PaymentInstrument{{
						Id:	"sample-pi",
						Type:	piPb.PaymentInstrumentType_BANK_ACCOUNT,
						Identifier: &piPb.PaymentInstrument_Account{
							Account: &piPb.Account{AccountType: accountsPb.Type_SAVINGS},
						},
					},
					}}, nil),
					mockPayClient.EXPECT().MakeB2CFundTransfer(context.Background(), gomock.Any()).Return(&pay.MakeB2CFundTransferResponse{Status: rpc.StatusOk()}, nil),
					mockPayoutDao.EXPECT().UpdatePayout(context.Background(), gomock.Any(), gomock.Any()).Return(nil, nil),
				},
				ctx:	context.Background(),
				payout: &payoutPb.PayoutDetail{
					Id:		"1234",
					ActorId:	"3456",
				},
			},
			wantErr:	false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := NewCashPayoutProcessor(mockPayoutDao, mockAccountPIClient, mockOrderClient, genConf.Payout(), mockTimelineClient, mockActorClient, mockPIClient, mockDelayPublisher, mockPayClient)
			err := p.ProcessPayout(tt.args.ctx, tt.args.payout)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessPayout() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
