package workflow

import (
	"context"
	"errors"

	chatbotWorkflowPb "github.com/epifi/gamma/api/cx/chat/bot/workflow"
	"github.com/epifi/gamma/cx/chat/bot/workflow/execute_action_processor"
	"github.com/epifi/gamma/cx/chat/bot/workflow/fetch_data_processor"
)

type IFetchDataFactory interface {
	// GetFetchDataProcessor accepts mandatory workflowEntity
	// returns applicable processor for given entity
	GetFetchDataProcessor(ctx context.Context, workflowEntity chatbotWorkflowPb.WorkflowEntity) (fetch_data_processor.IFetchDataProcessor, error)
}

type IExecuteActionFactory interface {
	// GetExecuteActionProcessor accepts mandatory workflowAction
	// returns applicable processor for given action
	GetExecuteActionProcessor(ctx context.Context, workflowAction chatbotWorkflowPb.WorkflowAction) (execute_action_processor.IExecuteActionProcessor, error)
}

type FetchDataFactory struct {
	faqProcessor                                        *fetch_data_processor.FaqProcessor
	userDetailsProcessor                                *fetch_data_processor.UserDetailsProcessor
	txnListProcessor                                    *fetch_data_processor.TxnListProcessor
	txnDetailsProcessor                                 *fetch_data_processor.TxnDetailsProcessor
	creditCardStateProcessor                            *fetch_data_processor.CreditCardStateProcessor
	debitCardTrackingProcessor                          *fetch_data_processor.DebitCardTrackingProcessor
	creditCardTxnListProcessor                          *fetch_data_processor.CreditCardTxnListProcessor
	employmentDataProcessor                             *fetch_data_processor.EmploymentDataProcessor
	fetchDisputeProcessor                               *fetch_data_processor.FetchDisputeProcessor
	livenessDataProcessor                               *fetch_data_processor.LivenessDataProcessor
	fetchRewardOffersForUsersProcessor                  *fetch_data_processor.FetchRewardOffersForUsersProcessor
	checkSalaryProgramAmazonVoucherEligibilityProcessor *fetch_data_processor.CheckSalaryProgramAmazonVoucherEligibilityProcessor
	fetchChargesForActorProcessor                       *fetch_data_processor.FetchChargesForActorProcessor
	displayTxnReasonProcessor                           *fetch_data_processor.DisplayTxnReasonProcessor
	fetchFailedTxnsProcessor                            *fetch_data_processor.FetchFailedTxnsProcessor
	fetchRewardEventDetailsProcessor                    *fetch_data_processor.FetchRewardEventDetailsProcessor
	fetchSalaryProgramRegistrationDetailsProcessor      *fetch_data_processor.FetchSalaryProgramRegistrationDetailsProcessor
	fetchRewardForEventProcessor                        *fetch_data_processor.FetchRewardForEventProcessor
	balanceRefreshProcessor                             *fetch_data_processor.BalanceRefreshProcessor
	predefinedMessageTemplateProcessor                  *fetch_data_processor.PredefinedMessageTemplateProcessor
	fetchUserTransactionsProcessor                      *fetch_data_processor.FetchUserTransactionsProcessor
}

// will update this struct once requirement arises
type ExecuteActionFactory struct {
	createTicketProcessor *execute_action_processor.CreateTicketProcessor
}

func NewFetchDataFactory(faqProcessor *fetch_data_processor.FaqProcessor, userDetailsProcessor *fetch_data_processor.UserDetailsProcessor,
	txnListProcessor *fetch_data_processor.TxnListProcessor, txnDetailsProcessor *fetch_data_processor.TxnDetailsProcessor,
	debitCardTrackingProcessor *fetch_data_processor.DebitCardTrackingProcessor,
	creditCardStateProcessor *fetch_data_processor.CreditCardStateProcessor,
	creditCardTxnListProcessor *fetch_data_processor.CreditCardTxnListProcessor,
	employmentDataProcessor *fetch_data_processor.EmploymentDataProcessor,
	fetchDisputeProcessor *fetch_data_processor.FetchDisputeProcessor,
	livenessDataProcessor *fetch_data_processor.LivenessDataProcessor,
	fetchRewardOffersForUsersProcessor *fetch_data_processor.FetchRewardOffersForUsersProcessor,
	checkSalaryProgramAmazonVoucherEligibilityProcessor *fetch_data_processor.CheckSalaryProgramAmazonVoucherEligibilityProcessor,
	fetchChargesForActorProcessor *fetch_data_processor.FetchChargesForActorProcessor,
	displayTxnReasonProcessor *fetch_data_processor.DisplayTxnReasonProcessor,
	fetchFailedTxnsProcessor *fetch_data_processor.FetchFailedTxnsProcessor,
	fetchRewardEventDetailsProcessor *fetch_data_processor.FetchRewardEventDetailsProcessor,
	fetchSalaryProgramRegistrationDetailsProcessor *fetch_data_processor.FetchSalaryProgramRegistrationDetailsProcessor,
	fetchRewardForEventProcessor *fetch_data_processor.FetchRewardForEventProcessor,
	balanceRefreshProcessor *fetch_data_processor.BalanceRefreshProcessor,
	predefineMessageTemplateProcessor *fetch_data_processor.PredefinedMessageTemplateProcessor,
	fetchUserTransactionsProcessor *fetch_data_processor.FetchUserTransactionsProcessor) *FetchDataFactory {
	return &FetchDataFactory{
		faqProcessor:                                        faqProcessor,
		userDetailsProcessor:                                userDetailsProcessor,
		txnListProcessor:                                    txnListProcessor,
		txnDetailsProcessor:                                 txnDetailsProcessor,
		debitCardTrackingProcessor:                          debitCardTrackingProcessor,
		creditCardStateProcessor:                            creditCardStateProcessor,
		creditCardTxnListProcessor:                          creditCardTxnListProcessor,
		employmentDataProcessor:                             employmentDataProcessor,
		fetchDisputeProcessor:                               fetchDisputeProcessor,
		livenessDataProcessor:                               livenessDataProcessor,
		fetchRewardOffersForUsersProcessor:                  fetchRewardOffersForUsersProcessor,
		checkSalaryProgramAmazonVoucherEligibilityProcessor: checkSalaryProgramAmazonVoucherEligibilityProcessor,
		fetchChargesForActorProcessor:                       fetchChargesForActorProcessor,
		displayTxnReasonProcessor:                           displayTxnReasonProcessor,
		fetchFailedTxnsProcessor:                            fetchFailedTxnsProcessor,
		fetchRewardEventDetailsProcessor:                    fetchRewardEventDetailsProcessor,
		fetchSalaryProgramRegistrationDetailsProcessor:      fetchSalaryProgramRegistrationDetailsProcessor,
		fetchRewardForEventProcessor:                        fetchRewardForEventProcessor,
		balanceRefreshProcessor:                             balanceRefreshProcessor,
		predefinedMessageTemplateProcessor:                  predefineMessageTemplateProcessor,
		fetchUserTransactionsProcessor:                      fetchUserTransactionsProcessor,
	}
}

func NewExecuteActionFactory(createTicketProcessor *execute_action_processor.CreateTicketProcessor) *ExecuteActionFactory {
	return &ExecuteActionFactory{
		createTicketProcessor: createTicketProcessor,
	}
}

var _ IFetchDataFactory = &FetchDataFactory{}

func (f *FetchDataFactory) GetFetchDataProcessor(ctx context.Context, workflowEntity chatbotWorkflowPb.WorkflowEntity) (fetch_data_processor.IFetchDataProcessor, error) {
	switch workflowEntity {
	case chatbotWorkflowPb.WorkflowEntity_WORK_FLOW_ENTITY_FAQ:
		return f.faqProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_USER_DETAILS:
		return f.userDetailsProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_TXN_LIST:
		return f.txnListProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_TXN_DETAILS:
		return f.txnDetailsProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_DEBIT_CARD_TRACKING:
		return f.debitCardTrackingProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_CREDIT_CARD_STATE:
		return f.creditCardStateProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_CREDIT_CARD_TXN_LIST:
		return f.creditCardTxnListProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_EMPLOYMENT_DATA:
		return f.employmentDataProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_DISPUTE_DETAILS:
		return f.fetchDisputeProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_LIVENESS:
		return f.livenessDataProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_REWARD_OFFERS_FOR_USER:
		return f.fetchRewardOffersForUsersProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_CHECK_SALARY_PROGRAM_AMAZON_VOUCHER_ELIGIBILITY:
		return f.checkSalaryProgramAmazonVoucherEligibilityProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_CHARGES_FOR_ACTOR:
		return f.fetchChargesForActorProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_DISPLAY_TXN_REASON_FOR_ACTOR:
		return f.displayTxnReasonProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_FAILED_TRANSACTIONS_FOR_ACTOR:
		return f.fetchFailedTxnsProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_REWARDS_EVENT_DETAILS:
		return f.fetchRewardEventDetailsProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_SALARY_PROGRAM_REGISTRATION_DETAILS:
		return f.fetchSalaryProgramRegistrationDetailsProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_REWARD_FOR_EVENT:
		return f.fetchRewardForEventProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_REFRESH_BALANCE:
		return f.balanceRefreshProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_PREDEFINED_MESSAGE_TEMPLATE:
		return f.predefinedMessageTemplateProcessor, nil
	case chatbotWorkflowPb.WorkflowEntity_WORKFLOW_ENTITY_FETCH_USER_TRANSACTIONS:
		return f.fetchUserTransactionsProcessor, nil
	default:
		return nil, errors.New("invalid workflow entity")
	}
}

func (e *ExecuteActionFactory) GetExecuteActionProcessor(ctx context.Context, workflowAction chatbotWorkflowPb.WorkflowAction) (execute_action_processor.IExecuteActionProcessor, error) {
	switch workflowAction {
	case chatbotWorkflowPb.WorkflowAction_WORKFLOW_ACTION_CREATE_TICKET:
		return e.createTicketProcessor, nil
	default:
		return nil, errors.New("invalid workflow action")
	}
}
