// nolint: dupl
package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	idGen "github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"
	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayEnums "github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/config/genconf"
	"github.com/epifi/gamma/billpay/dao/model"
)

var RechargeOrderDaoWireSet = wire.NewSet(NewRechargeOrderImpl, wire.Bind(new(RechargeOrderDao), new(*RechargeOrderImpl)))

type RechargeOrderImpl struct {
	db     *gorm.DB
	config *genconf.Config
	idGen  idGen.IdGenerator
}

func NewRechargeOrderImpl(db *gorm.DB, config *genconf.Config, idGen idGen.IdGenerator) *RechargeOrderImpl {
	return &RechargeOrderImpl{db: db, config: config, idGen: idGen}
}

// verify interface compatibility
var _ RechargeOrderDao = &RechargeOrderImpl{}

func (r *RechargeOrderImpl) Create(ctx context.Context, order *billpaypb.RechargeOrder) (*billpaypb.RechargeOrder, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderImpl", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	orderModel := model.NewRechargeOrderFromProto(order)
	// Generate ID using idGen
	id, err := r.idGen.Get(idGen.RechargeOrderId)
	if err != nil {
		return nil, errors.Wrap(err, "error fetching id from idgen")
	}
	orderModel.Id = id
	res := db.Create(orderModel)
	if res.Error != nil {
		if storagev2.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		logger.Error(ctx, "failed to create recharge order", zap.Error(res.Error))
		return nil, errors.Wrap(res.Error, "error creating new entry for recharge order")
	}
	return orderModel.ToProto(), nil
}

func (r *RechargeOrderImpl) Update(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, order *billpaypb.RechargeOrder) error {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderImpl", "Update", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	if len(fieldMask.GetPaths()) == 0 {
		return fmt.Errorf("fieldMask cannot be empty")
	}
	orderModel := model.NewRechargeOrderFromProto(order)
	res := db.Model(&model.RechargeOrder{}).Where("id = ?", order.GetId()).Select(fieldMask.GetPaths()).Updates(orderModel)
	if res.Error != nil {
		logger.Error(ctx, "failed to update recharge order in db", zap.Error(res.Error))
		return res.Error
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRecordNotFound
	}
	return nil
}

func (r *RechargeOrderImpl) GetById(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, id string) (*billpaypb.RechargeOrder, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderImpl", "GetById", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	var order model.RechargeOrder
	res := db.Model(&model.RechargeOrder{}).Where("id = ?", id).Select(fieldMask.GetPaths()).First(&order)
	if res.Error != nil {
		logger.Error(ctx, "failed to fetch recharge order", zap.Error(res.Error))
		return nil, res.Error
	}
	return order.ToProto(), nil
}

func (r *RechargeOrderImpl) GetByClientRequestId(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, clientRequestId string) (*billpaypb.RechargeOrder, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderImpl", "GetByClientRequestId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	var order model.RechargeOrder
	res := db.Model(&model.RechargeOrder{}).Where("client_request_id = ?", clientRequestId).Select(fieldMask.GetPaths()).First(&order)
	if res.Error != nil {
		logger.Error(ctx, "failed to fetch recharge order by client_request_id", zap.Error(res.Error))
		return nil, res.Error
	}
	return order.ToProto(), nil
}

func (r *RechargeOrderImpl) GetByActorId(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, actorId string, pageToken *pagination.PageToken, pageSize uint32, options []storagev2.FilterOption) ([]*billpaypb.RechargeOrder, *rpc.PageContextResponse, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderImpl", "GetByActorId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	if pageSize > r.config.MaxDaoPageSize() || pageSize == 0 {
		pageSize = r.config.MaxDaoPageSize()
	}
	db = db.Model(&model.RechargeOrder{}).Where("actor_id = ?", actorId)
	// Apply filter options
	for _, opt := range options {
		db = opt.ApplyInGorm(db)
	}
	if pageToken != nil {
		if pageToken.IsReverse {
			db = db.Where("created_at >= ?", pageToken.GetTimestamp().AsTime()).Order("created_at")
		} else {
			db = db.Where("created_at <= ?", pageToken.GetTimestamp().AsTime()).Order("created_at desc")
		}
		db = db.Offset(int(pageToken.GetOffset()))
	} else {
		db = db.Order("created_at desc")
	}
	db = db.Limit(int(pageSize + 1))
	var orders []*model.RechargeOrder
	res := db.Select(fieldMask.GetPaths()).Find(&orders)
	if res.Error != nil {
		logger.Error(ctx, "Failed to get recharge orders by actor id", zap.Error(res.Error))
		return nil, nil, res.Error
	}
	var pageCtxResp *rpc.PageContextResponse
	rows, resp, err := pagination.NewPageCtxResp(pageToken, int(pageSize), RechargeOrders(orders))
	if err != nil {
		logger.Error(ctx, "Failed to create new page context", zap.Error(err))
		return nil, nil, err
	}
	orders = rows.(RechargeOrders)
	pageCtxResp = resp
	ordersProto := make([]*billpaypb.RechargeOrder, 0, len(orders))
	for _, order := range orders {
		orderProto := order.ToProto()
		ordersProto = append(ordersProto, orderProto)
	}
	return ordersProto, pageCtxResp, nil
}

func (r *RechargeOrderImpl) GetLatestOrdersForUniqueAccountsByActor(ctx context.Context, actorId string, accountType billpayEnums.RechargeAccountType) ([]*billpaypb.RechargeOrder, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderImpl", "GetLatestOrdersForUniqueAccountsByActor", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	var orders []*model.RechargeOrder

	sql := `
		SELECT DISTINCT ON (account_identifier) *
		FROM recharge_orders
		WHERE actor_id = ?
		  AND account_type = ?
		ORDER BY account_identifier, created_at DESC
	`
	res := db.Raw(sql, actorId, accountType).Scan(&orders)
	if res.Error != nil {
		logger.Error(ctx, "Failed to get latest orders for unique accounts by actor", zap.Error(res.Error))
		return nil, res.Error
	}

	ordersProto := make([]*billpaypb.RechargeOrder, 0, len(orders))
	for _, order := range orders {
		ordersProto = append(ordersProto, order.ToProto())
	}
	return ordersProto, nil
}

func (r *RechargeOrderImpl) GetByActorIdAndAccountDetails(ctx context.Context, actorId string, accountType billpayEnums.RechargeAccountType, accountIdentifier string, options ...storagev2.FilterOption) ([]*billpaypb.RechargeOrder, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderImpl", "GetByActorIdAndAccountDetails", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	var orders []*model.RechargeOrder

	db = db.Model(&model.RechargeOrder{}).
		Where("actor_id = ?", actorId).
		Where("account_type = ?", accountType).
		Where("account_identifier = ?", accountIdentifier)

	// Apply filter options
	for _, opt := range options {
		db = opt.ApplyInGorm(db)
	}

	res := db.Order("created_at desc").Find(&orders)

	if res.Error != nil {
		logger.Error(ctx, "Failed to get recharge orders by actor id and account details", zap.Error(res.Error))
		return nil, res.Error
	}

	ordersProto := make([]*billpaypb.RechargeOrder, 0, len(orders))
	for _, order := range orders {
		ordersProto = append(ordersProto, order.ToProto())
	}
	return ordersProto, nil
}

type RechargeOrders []*model.RechargeOrder

func (rs RechargeOrders) Slice(start, end int) pagination.Rows { return rs[start:end] }
func (rs RechargeOrders) GetTimestamp(index int) time.Time     { return rs[index].CreatedAt }
func (rs RechargeOrders) Size() int                            { return len(rs) }
