package dao_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	pkgTest "github.com/epifi/be-common/pkg/test/v2"
	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayEnums "github.com/epifi/gamma/api/billpay/enums"
)

func newRechargeOrderStage(orderId, clientRequestId string, stage billpayEnums.RechargeStage, status billpayEnums.RechargeStageStatus) *billpaypb.RechargeOrderStage {
	return &billpaypb.RechargeOrderStage{
		RechargeOrderId: orderId,
		ClientRequestId: clientRequestId,
		Stage:           stage,
		Status:          status,
		CreatedAt:       timestamppb.New(time.Now()),
		UpdatedAt:       timestamppb.New(time.Now()),
		CompletedAt:     nil,
	}
}

func TestRechargeOrderStageImpl_Create(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, dbConf.GetName(), []string{"recharge_orders", "recharge_order_stages"})
	a := require.New(t)
	order := newRechargeOrder("actor", uuid.New().String())
	createdOrder, err := rechargeOrderDao.Create(context.Background(), order)
	a.NoError(err)

	type args struct {
		stage *billpaypb.RechargeOrderStage
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "valid create",
			args: args{
				stage: newRechargeOrderStage(createdOrder.GetId(), uuid.New().String(), billpayEnums.RechargeStage_RECHARGE_STAGE_PAYMENT, billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS),
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := rechargeOrderStageDao.Create(context.Background(), tt.args.stage)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				diff := cmp.Diff(tt.args.stage.RechargeOrderId, got.RechargeOrderId)
				if diff != "" {
					t.Errorf("Create() mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}

func TestRechargeOrderStageImpl_Update(t *testing.T) {
	tx := db.Begin()
	defer tx.Rollback()
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, dbConf.GetName(), []string{"recharge_orders", "recharge_order_stages"})
	a := require.New(t)
	order := newRechargeOrder("actor", uuid.New().String())
	createdOrder, err := rechargeOrderDao.Create(context.Background(), order)
	a.NoError(err)
	stage := newRechargeOrderStage(createdOrder.GetId(), uuid.New().String(), billpayEnums.RechargeStage_RECHARGE_STAGE_PAYMENT, billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS)
	created, err := rechargeOrderStageDao.Create(context.Background(), stage)
	a.NoError(err)

	type args struct {
		stage     *billpaypb.RechargeOrderStage
		fieldMask *fieldmaskpb.FieldMask
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    billpayEnums.RechargeStageStatus
	}{
		{
			name: "valid update",
			args: args{
				stage: func() *billpaypb.RechargeOrderStage {
					created.Status = billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL
					return created
				}(),
				fieldMask: &fieldmaskpb.FieldMask{Paths: []string{"status"}},
			},
			wantErr: false,
			want:    billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
		},
		{
			name: "empty fieldMask",
			args: args{
				stage:     created,
				fieldMask: &fieldmaskpb.FieldMask{},
			},
			wantErr: true,
		},
		{
			name: "record not found",
			args: args{
				stage: func() *billpaypb.RechargeOrderStage {
					return &billpaypb.RechargeOrderStage{
						Id:     "non-existing-id",
						Status: billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					}
				}(),
				fieldMask: &fieldmaskpb.FieldMask{Paths: []string{"status"}},
			},
			wantErr: true,
			want:    billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_UNSPECIFIED,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			err := rechargeOrderStageDao.Update(context.Background(), tt.args.fieldMask, tt.args.stage)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !tt.wantErr {
				fetched, err := rechargeOrderStageDao.GetById(context.Background(), nil, tt.args.stage.GetId())
				a.NoError(err)
				if fetched.GetStatus() != tt.want {
					t.Errorf("expected status %v, got %v", tt.want, fetched.GetStatus())
				}
			}
		})
	}
}

func TestRechargeOrderStageImpl_GetById(t *testing.T) {
	tx := db.Begin()
	defer tx.Rollback()
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, dbConf.GetName(), []string{"recharge_orders", "recharge_order_stages"})
	a := require.New(t)
	order := newRechargeOrder("actor", uuid.New().String())
	createdOrder, err := rechargeOrderDao.Create(context.Background(), order)
	a.NoError(err)
	stage := newRechargeOrderStage(createdOrder.GetId(), uuid.New().String(), billpayEnums.RechargeStage_RECHARGE_STAGE_PAYMENT, billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS)
	created, err := rechargeOrderStageDao.Create(context.Background(), stage)
	a.NoError(err)

	type args struct {
		id string
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    *billpaypb.RechargeOrderStage
	}{
		{
			name:    "found",
			args:    args{id: created.GetId()},
			wantErr: false,
			want:    created,
		},
		{
			name:    "not found",
			args:    args{id: uuid.New().String()},
			wantErr: true,
			want:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := rechargeOrderStageDao.GetById(context.Background(), nil, tt.args.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !tt.wantErr {
				diff := cmp.Diff(tt.want, got, protocmp.Transform())
				if diff != "" {
					t.Errorf("GetById() mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}

func TestRechargeOrderStageImpl_GetByClientRequestId(t *testing.T) {
	tx := db.Begin()
	defer tx.Rollback()
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, dbConf.GetName(), []string{"recharge_orders", "recharge_order_stages"})
	a := require.New(t)
	order := newRechargeOrder("actor", uuid.New().String())
	createdOrder, err := rechargeOrderDao.Create(context.Background(), order)
	a.NoError(err)
	clientRequestId := uuid.New().String()
	stage := newRechargeOrderStage(createdOrder.GetId(), clientRequestId, billpayEnums.RechargeStage_RECHARGE_STAGE_PAYMENT, billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS)
	created, err := rechargeOrderStageDao.Create(context.Background(), stage)
	a.NoError(err)
	tests := []struct {
		name            string
		clientRequestId string
		wantErr         bool
		want            *billpaypb.RechargeOrderStage
	}{
		{
			name:            "found",
			clientRequestId: created.GetClientRequestId(),
			wantErr:         false,
			want:            created,
		},
		{
			name:            "not found",
			clientRequestId: uuid.New().String(),
			wantErr:         true,
			want:            nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := rechargeOrderStageDao.GetByClientRequestId(context.Background(), nil, tt.clientRequestId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientRequestId() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !tt.wantErr {
				diff := cmp.Diff(tt.want, got, protocmp.Transform())
				if diff != "" {
					t.Errorf("GetByClientRequestId() mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}

func TestRechargeOrderStageImpl_GetByRechargeOrderId(t *testing.T) {
	tx := db.Begin()
	defer tx.Rollback()
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, dbConf.GetName(), []string{"recharge_orders", "recharge_order_stages"})
	a := require.New(t)
	order := newRechargeOrder("actor", uuid.New().String())
	createdOrder, err := rechargeOrderDao.Create(context.Background(), order)
	a.NoError(err)
	stage1 := newRechargeOrderStage(createdOrder.GetId(), uuid.New().String(), billpayEnums.RechargeStage_RECHARGE_STAGE_PAYMENT, billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS)
	stage2 := newRechargeOrderStage(createdOrder.GetId(), uuid.New().String(), billpayEnums.RechargeStage_RECHARGE_STAGE_FULFILLMENT, billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL)
	_, err = rechargeOrderStageDao.Create(context.Background(), stage1)
	a.NoError(err)
	_, err = rechargeOrderStageDao.Create(context.Background(), stage2)
	a.NoError(err)

	type args struct {
		rechargeOrderId string
	}

	tests := []struct {
		name    string
		args    args
		want    int
		wantErr bool
	}{
		{
			name:    "two stages",
			args:    args{rechargeOrderId: createdOrder.GetId()},
			want:    2,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			stages, err := rechargeOrderStageDao.GetByRechargeOrderId(context.Background(), nil, tt.args.rechargeOrderId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByRechargeOrderId() error = %v, wantErr %v", err, tt.wantErr)
			}
			if len(stages) != tt.want {
				t.Errorf("expected %d stages, got %d", tt.want, len(stages))
			}
		})
	}
}

func TestRechargeOrderStageImpl_GetByRechargeOrderIdAndStage(t *testing.T) {
	tx := db.Begin()
	defer tx.Rollback()
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, dbConf.GetName(), []string{"recharge_orders", "recharge_order_stages"})
	a := require.New(t)
	order := newRechargeOrder("actor", uuid.New().String())
	createdOrder, err := rechargeOrderDao.Create(context.Background(), order)
	a.NoError(err)
	stage := newRechargeOrderStage(createdOrder.GetId(), uuid.New().String(), billpayEnums.RechargeStage_RECHARGE_STAGE_PAYMENT, billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS)
	created, err := rechargeOrderStageDao.Create(context.Background(), stage)
	a.NoError(err)
	tests := []struct {
		name string
		args struct {
			rechargeOrderId string
			stage           billpayEnums.RechargeStage
		}
		wantErr bool
		want    *billpaypb.RechargeOrderStage
	}{
		{
			name: "found",
			args: struct {
				rechargeOrderId string
				stage           billpayEnums.RechargeStage
			}{createdOrder.GetId(), billpayEnums.RechargeStage_RECHARGE_STAGE_PAYMENT},
			wantErr: false,
			want:    created,
		},
		{
			name: "not found",
			args: struct {
				rechargeOrderId string
				stage           billpayEnums.RechargeStage
			}{createdOrder.GetId(), billpayEnums.RechargeStage_RECHARGE_STAGE_REFUND},
			wantErr: true,
			want:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := rechargeOrderStageDao.GetByRechargeOrderIdAndStage(context.Background(), nil, tt.args.rechargeOrderId, tt.args.stage)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByRechargeOrderIdAndStage() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !tt.wantErr {
				diff := cmp.Diff(tt.want, got, protocmp.Transform())
				if diff != "" {
					t.Errorf("GetByRechargeOrderIdAndStage() mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}
