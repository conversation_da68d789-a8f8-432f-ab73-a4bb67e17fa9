package dao_test

import (
	"context"
	"testing"
	"time"

	"github.com/google/go-cmp/cmp"
	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	pkgTest "github.com/epifi/be-common/pkg/test/v2"

	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayEnums "github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/dao"
)

func newRechargeOrder(actorId, clientRequestId string) *billpaypb.RechargeOrder {
	return &billpaypb.RechargeOrder{
		// Id will be generated by the DAO using idGen.IdGenerator
		ClientRequestId:   clientRequestId,
		ActorId:           actorId,
		AccountType:       billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE,
		AccountIdentifier: "**********",
		AccountOperator:   billpayEnums.Operator_OPERATOR_AIRTEL,
		PlanDetails: &billpaypb.PlanDetails{
			MobileRechargePlanDetails: &billpaypb.MobileRechargePlan{
				PlanName:        "Test Plan",
				Amount:          &gmoney.Money{CurrencyCode: "INR", Units: 100, Nanos: *********},
				Validity:        "28 days",
				PlanDescription: "Test plan description",
			},
		},
		PlanIdentifier: "INR 100.75",
		Status:         billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED,
		SubStatus:      billpayEnums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
		CreatedAt:      timestamppb.New(time.Now()),
		UpdatedAt:      timestamppb.New(time.Now()),
		CompletedAt:    nil,
	}
}

func TestRechargeOrderImpl_Create(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, db, dbConf.GetName(), []string{"recharge_orders"})
	a := require.New(t)

	type args struct {
		order *billpaypb.RechargeOrder
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    *billpaypb.RechargeOrder
	}{
		{
			name: "valid recharge order",
			args: args{
				order: newRechargeOrder("actor1", uuid.New().String()),
			},
			wantErr: false,
			want:    nil, // will set after creation
		},
		{
			name: "duplicate client_request_id triggers unique index error",
			args: args{
				order: func() *billpaypb.RechargeOrder {
					id := uuid.New().String()
					order := newRechargeOrder("actor2", id)
					_, err := rechargeOrderDao.Create(context.Background(), order)
					a.NoError(err)
					return newRechargeOrder("actor3", id) // same client_request_id
				}(),
			},
			wantErr: true,
			want:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := rechargeOrderDao.Create(context.Background(), tt.args.order)
			if (err != nil) != tt.wantErr {
				t.Errorf("Create() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr {
				// Fetch from DB to verify computed columns and persisted data
				fetched, err := rechargeOrderDao.GetById(context.Background(), nil, got.GetId())
				a.NoError(err)
				// PlanIdentifier should be computed as per migration logic
				if fetched.GetPlanIdentifier() == "" {
					t.Errorf("expected computed PlanIdentifier, got empty string")
				}
				// Compare all fields except timestamps (allow some drift)
				if diff := cmp.Diff(got, fetched, protocmp.Transform()); diff != "" {
					t.Errorf("Create() mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}

func TestRechargeOrderImpl_GetLatestOrdersForUniqueAccountsByActor(t *testing.T) {

	pkgTest.TruncateTestDatabaseTables(t, db, dbConf.GetName(), []string{"recharge_orders"})
	a := require.New(t)

	type args struct {
		actorId     string
		accountType billpayEnums.RechargeAccountType
	}

	// Setup: create multiple orders for same and different accounts
	actorId := "actor1"
	accountType := billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE
	account1 := "**********"
	account2 := "**********"

	// Oldest order for account1
	order1 := newRechargeOrder(actorId, uuid.New().String())
	order1.AccountIdentifier = account1
	order1.CreatedAt = timestamppb.New(time.Now().Add(-2 * time.Hour))
	_, err := rechargeOrderDao.Create(context.Background(), order1)
	a.NoError(err)

	// Newest order for account1
	order2 := newRechargeOrder(actorId, uuid.New().String())
	order2.AccountIdentifier = account1
	order2.CreatedAt = timestamppb.New(time.Now().Add(-1 * time.Hour))
	createdOrder2, err := rechargeOrderDao.Create(context.Background(), order2)
	a.NoError(err)

	// Order for account2
	order3 := newRechargeOrder(actorId, uuid.New().String())
	order3.AccountIdentifier = account2
	order3.CreatedAt = timestamppb.New(time.Now().Add(-30 * time.Minute))
	createdOrder3, err := rechargeOrderDao.Create(context.Background(), order3)
	a.NoError(err)

	// Order for another actor (should not be returned)
	order4 := newRechargeOrder("actor2", uuid.New().String())
	order4.AccountIdentifier = account1
	order4.CreatedAt = timestamppb.New(time.Now().Add(-10 * time.Minute))
	_, err = rechargeOrderDao.Create(context.Background(), order4)
	a.NoError(err)

	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    []*billpaypb.RechargeOrder
	}{
		{
			name: "latest orders for unique accounts for actor1",
			args: args{
				actorId:     actorId,
				accountType: accountType,
			},
			wantErr: false,
			want:    []*billpaypb.RechargeOrder{createdOrder2, createdOrder3}, // latest for account1 and account2
		},
		{
			name: "no orders for unknown actor",
			args: args{
				actorId:     "unknown_actor",
				accountType: accountType,
			},
			wantErr: false,
			want:    []*billpaypb.RechargeOrder{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := rechargeOrderDao.GetLatestOrdersForUniqueAccountsByActor(context.Background(), tt.args.actorId, tt.args.accountType)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLatestOrdersForUniqueAccountsByActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// Compare as sets (order doesn't matter)
			if len(got) != len(tt.want) {
				t.Errorf("expected %d orders, got %d", len(tt.want), len(got))
			}
			// For each expected, find a match in got
			for _, wantOrder := range tt.want {
				found := false
				for _, gotOrder := range got {
					if wantOrder.GetId() == gotOrder.GetId() {
						if diff := cmp.Diff(wantOrder, gotOrder, protocmp.Transform()); diff != "" {
							t.Errorf("Order mismatch (-want +got):\n%s", diff)
						}
						found = true
						break
					}
				}
				if !found {
					t.Errorf("expected order with id %s not found in result", wantOrder.GetId())
				}
			}
		})
	}
}

func TestRechargeOrderImpl_Update(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, db, dbConf.GetName(), []string{"recharge_orders"})
	a := require.New(t)

	order := newRechargeOrder("actor1", uuid.New().String())
	created, err := rechargeOrderDao.Create(context.Background(), order)
	a.NoError(err)

	updatedStatus := billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS
	created.Status = updatedStatus
	fieldMask := &fieldmaskpb.FieldMask{Paths: []string{"status"}}

	tests := []struct {
		name string
		args struct {
			order     *billpaypb.RechargeOrder
			fieldMask *fieldmaskpb.FieldMask
		}
		wantErr bool
	}{
		{
			name: "valid update",
			args: struct {
				order     *billpaypb.RechargeOrder
				fieldMask *fieldmaskpb.FieldMask
			}{created, fieldMask},
			wantErr: false,
		},
		{
			name: "empty fieldMask",
			args: struct {
				order     *billpaypb.RechargeOrder
				fieldMask *fieldmaskpb.FieldMask
			}{created, &fieldmaskpb.FieldMask{}},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			err := rechargeOrderDao.Update(context.Background(), tt.args.fieldMask, tt.args.order)
			if (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !tt.wantErr {
				fetched, err := rechargeOrderDao.GetById(context.Background(), nil, tt.args.order.GetId())
				a.NoError(err)
				if fetched.GetStatus() != updatedStatus {
					t.Errorf("expected status %v, got %v", updatedStatus, fetched.GetStatus())
				}
			}
		})
	}
}

func TestRechargeOrderImpl_GetById(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, db, dbConf.GetName(), []string{"recharge_orders"})
	a := require.New(t)
	order := newRechargeOrder("actor1", uuid.New().String())
	created, err := rechargeOrderDao.Create(context.Background(), order)
	a.NoError(err)

	tests := []struct {
		name    string
		id      string
		wantErr bool
		want    *billpaypb.RechargeOrder
	}{
		{
			name:    "found",
			id:      created.GetId(),
			wantErr: false,
			want:    created,
		},
		{
			name:    "not found",
			id:      uuid.New().String(),
			wantErr: true,
			want:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := rechargeOrderDao.GetById(context.Background(), nil, tt.id)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetById() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !tt.wantErr {
				diff := cmp.Diff(tt.want, got, protocmp.Transform())
				if diff != "" {
					t.Errorf("GetById() mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}

func TestRechargeOrderImpl_GetByClientRequestId(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, db, dbConf.GetName(), []string{"recharge_orders"})
	a := require.New(t)
	order := newRechargeOrder("actor1", uuid.New().String())
	created, err := rechargeOrderDao.Create(context.Background(), order)
	a.NoError(err)

	tests := []struct {
		name            string
		clientRequestId string
		wantErr         bool
		want            *billpaypb.RechargeOrder
	}{
		{
			name:            "found",
			clientRequestId: created.GetClientRequestId(),
			wantErr:         false,
			want:            created,
		},
		{
			name:            "not found",
			clientRequestId: uuid.New().String(),
			wantErr:         true,
			want:            nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := rechargeOrderDao.GetByClientRequestId(context.Background(), nil, tt.clientRequestId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByClientRequestId() error = %v, wantErr %v", err, tt.wantErr)
			}
			if !tt.wantErr {
				diff := cmp.Diff(tt.want, got, protocmp.Transform())
				if diff != "" {
					t.Errorf("GetByClientRequestId() mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}

func TestRechargeOrderImpl_GetByActorId(t *testing.T) {
	pkgTest.TruncateTestDatabaseTables(t, db, dbConf.GetName(), []string{"recharge_orders"})
	a := require.New(t)
	actorId := "actor1"
	otherActorId := "actor2"
	// Setup: create orders with different fields for filter tests
	baseTime := time.Now().Add(-2 * time.Hour)
	order1 := newRechargeOrder(actorId, uuid.New().String())
	order1.Status = billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED
	order1.AccountType = billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE
	order1.AccountOperator = billpayEnums.Operator_OPERATOR_AIRTEL
	order1.CreatedAt = timestamppb.New(baseTime)
	_, err := rechargeOrderDao.Create(context.Background(), order1)
	a.NoError(err)

	order2 := newRechargeOrder(actorId, uuid.New().String())
	order2.Status = billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS
	order2.AccountType = billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE
	order2.AccountOperator = billpayEnums.Operator_OPERATOR_JIO
	order2.CreatedAt = timestamppb.New(baseTime.Add(30 * time.Minute))
	_, err = rechargeOrderDao.Create(context.Background(), order2)
	a.NoError(err)

	order3 := newRechargeOrder(actorId, uuid.New().String())
	order3.Status = billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED
	order3.AccountType = billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE
	order3.AccountOperator = billpayEnums.Operator_OPERATOR_VI
	order3.CreatedAt = timestamppb.New(baseTime.Add(1 * time.Hour))
	_, err = rechargeOrderDao.Create(context.Background(), order3)
	a.NoError(err)

	order4 := newRechargeOrder(otherActorId, uuid.New().String())
	order4.Status = billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS
	order4.AccountType = billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE
	order4.AccountOperator = billpayEnums.Operator_OPERATOR_AIRTEL
	order4.CreatedAt = timestamppb.New(baseTime.Add(90 * time.Minute))
	_, err = rechargeOrderDao.Create(context.Background(), order4)
	a.NoError(err)

	tests := []struct {
		name      string
		actorId   string
		options   []storagev2.FilterOption
		pageSize  uint32
		wantCount int
	}{
		{
			name:      "all orders for actor1",
			actorId:   actorId,
			options:   nil,
			pageSize:  10,
			wantCount: 3,
		},
		{
			name:      "filter by status=SUCCESS",
			actorId:   actorId,
			options:   []storagev2.FilterOption{dao.WithRechargeOrderStatus(billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS)},
			pageSize:  10,
			wantCount: 1,
		},
		{
			name:      "filter by operator=OPERATOR_AIRTEL",
			actorId:   actorId,
			options:   []storagev2.FilterOption{dao.WithOperator(billpayEnums.Operator_OPERATOR_AIRTEL)},
			pageSize:  10,
			wantCount: 1,
		},
		{
			name:      "pagination: pageSize=2 (should return 2)",
			actorId:   actorId,
			options:   nil,
			pageSize:  2,
			wantCount: 2,
		},
		{
			name:      "no orders for unknown actor",
			actorId:   "unknown_actor",
			options:   nil,
			pageSize:  10,
			wantCount: 0,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, _, err := rechargeOrderDao.GetByActorId(context.Background(), nil, tt.actorId, nil, tt.pageSize, tt.options)
			a.NoError(err)
			if len(got) != tt.wantCount {
				t.Errorf("expected %d orders, got %d", tt.wantCount, len(got))
			}
		})
	}
}

func TestRechargeOrderImpl_GetByActorIdAndAccountDetails(t *testing.T) {
	pkgTest.TruncateAndPopulateRdsFixtures(t, db, dbConf.GetName(), []string{"recharge_orders"})

	// Setup: create multiple orders for same and different accounts
	actorId := "test-actor"
	accountType := billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE
	accountIdentifier := "**********"

	// Order 1 for the target account (older)
	order1Proto := newRechargeOrder(actorId, "6683df89-97cd-4f34-bd0a-4a6aba0f6bf7")
	order1Proto.AccountIdentifier = accountIdentifier
	order1Proto.CreatedAt = timestamppb.New(time.Now().Add(-2 * time.Hour))
	order1Proto.Status = billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS // For filter test
	order1Proto.Id = "order-1"

	// Order 2 for the target account (newer)
	order2Proto := newRechargeOrder(actorId, "6683df89-97cd-4f34-bd0a-4a6aba0f6bf8")
	order2Proto.AccountIdentifier = accountIdentifier
	order2Proto.CreatedAt = timestamppb.New(time.Now().Add(-1 * time.Hour))
	order2Proto.Id = "order-2"

	// Order for a different account identifier
	order3Proto := newRechargeOrder(actorId, "6683df89-97cd-4f34-bd0a-4a6aba0f6bf9")
	order3Proto.AccountIdentifier = "**********"
	order3Proto.Id = "order-3"

	// Order for a different actor
	order4Proto := newRechargeOrder("another-actor", "6683df89-97cd-4f34-bd0a-4a6aba0f6bf0")
	order4Proto.AccountIdentifier = accountIdentifier
	order4Proto.Id = "order-4"

	type args struct {
		actorId           string
		accountType       billpayEnums.RechargeAccountType
		accountIdentifier string
		options           []storagev2.FilterOption
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    []*billpaypb.RechargeOrder
	}{
		{
			name: "fetch orders for specific actor and account",
			args: args{
				actorId:           actorId,
				accountType:       accountType,
				accountIdentifier: accountIdentifier,
			},
			wantErr: false,
			// Should return both orders for the account, newest first
			want: []*billpaypb.RechargeOrder{order2Proto, order1Proto},
		},
		{
			name: "fetch orders for specific actor and account with status filter",
			args: args{
				actorId:           actorId,
				accountType:       accountType,
				accountIdentifier: accountIdentifier,
				options: []storagev2.FilterOption{
					dao.WithStatusFilter(billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS),
				},
			},
			wantErr: false,
			// Should return only the completed order
			want: []*billpaypb.RechargeOrder{order1Proto},
		},
		{
			name: "fetch orders excluding a specific status",
			args: args{
				actorId:           actorId,
				accountType:       accountType,
				accountIdentifier: accountIdentifier,
				options: []storagev2.FilterOption{
					dao.WithStatusExcludeFilter([]billpayEnums.RechargeOrderStatus{billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS}),
				},
			},
			wantErr: false,
			// Should return only the initiated order
			want: []*billpaypb.RechargeOrder{order2Proto},
		},
		{
			name: "no orders for unknown account identifier",
			args: args{
				actorId:           actorId,
				accountType:       accountType,
				accountIdentifier: "**********",
			},
			wantErr: false,
			want:    []*billpaypb.RechargeOrder{},
		},
		{
			name: "no orders for unknown actor",
			args: args{
				actorId:           "unknown-actor",
				accountType:       accountType,
				accountIdentifier: accountIdentifier,
			},
			wantErr: false,
			want:    []*billpaypb.RechargeOrder{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := rechargeOrderDao.GetByActorIdAndAccountDetails(context.Background(), tt.args.actorId, tt.args.accountType, tt.args.accountIdentifier, tt.args.options...)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetByActorIdAndAccountDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			for ind, gotItem := range got {
				gotItem.CreatedAt = tt.want[ind].CreatedAt
				gotItem.UpdatedAt = tt.want[ind].UpdatedAt
				gotItem.CompletedAt = tt.want[ind].CompletedAt
			}

			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("GetByActorIdAndAccountDetails() mismatch (-want +got):\\n%s", diff)
			}
		})
	}
}
