// nolint: dupl
package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	idGen "github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"
	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayEnums "github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/config/genconf"
	"github.com/epifi/gamma/billpay/dao/model"
)

var RechargeOrderStageDaoWireSet = wire.NewSet(NewRechargeOrderStageImpl, wire.Bind(new(RechargeOrderStageDao), new(*RechargeOrderStageImpl)))

type RechargeOrderStageImpl struct {
	db     *gorm.DB
	config *genconf.Config
	idGen  idGen.IdGenerator
}

func NewRechargeOrderStageImpl(db *gorm.DB, config *genconf.Config, idGen idGen.IdGenerator) *RechargeOrderStageImpl {
	return &RechargeOrderStageImpl{db: db, config: config, idGen: idGen}
}

// verify interface compatibility
var _ RechargeOrderStageDao = &RechargeOrderStageImpl{}

func (r *RechargeOrderStageImpl) Create(ctx context.Context, stage *billpaypb.RechargeOrderStage) (*billpaypb.RechargeOrderStage, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderStageImpl", "Create", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	stageModel := model.NewRechargeOrderStageFromProto(stage)

	// Generate ID using idGen
	id, err := r.idGen.Get(idGen.RechargeOrderStageId)
	if err != nil {
		return nil, errors.Wrap(err, "error fetching id from idgen")
	}
	stageModel.Id = id

	res := db.Create(stageModel)
	if res.Error != nil {
		if storagev2.IsDuplicateRowError(res.Error) {
			return nil, epifierrors.ErrDuplicateEntry
		}
		logger.Error(ctx, "failed to create recharge order stage", zap.Error(res.Error))
		return nil, errors.Wrap(res.Error, "error creating new entry for recharge order stage")
	}
	return stageModel.ToProto(), nil
}

func (r *RechargeOrderStageImpl) Update(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, stage *billpaypb.RechargeOrderStage) error {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderStageImpl", "Update", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	if len(fieldMask.GetPaths()) == 0 {
		return fmt.Errorf("fieldMask cannot be empty")
	}
	stageModel := model.NewRechargeOrderStageFromProto(stage)
	res := db.Model(&model.RechargeOrderStage{}).Where("id = ?", stage.GetId()).Select(fieldMask.GetPaths()).Updates(stageModel)
	if res.Error != nil {
		logger.Error(ctx, "failed to update recharge order stage in db", zap.Error(res.Error))
		return res.Error
	}
	if res.RowsAffected == 0 {
		return epifierrors.ErrRecordNotFound
	}

	return nil
}

func (r *RechargeOrderStageImpl) GetById(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, id string) (*billpaypb.RechargeOrderStage, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderStageImpl", "GetById", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	var stage model.RechargeOrderStage
	res := db.Model(&model.RechargeOrderStage{}).Where("id = ?", id).Select(fieldMask.GetPaths()).First(&stage)
	if res.Error != nil {
		logger.Error(ctx, "failed to fetch recharge order stage", zap.Error(res.Error))
		return nil, res.Error
	}
	return stage.ToProto(), nil
}

func (r *RechargeOrderStageImpl) GetByClientRequestId(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, clientRequestId string) (*billpaypb.RechargeOrderStage, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderStageImpl", "GetByClientRequestId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	var stage model.RechargeOrderStage
	res := db.Model(&model.RechargeOrderStage{}).Where("client_request_id = ?", clientRequestId).Select(fieldMask.GetPaths()).First(&stage)
	if res.Error != nil {
		logger.Error(ctx, "failed to fetch recharge order stage by client_request_id", zap.Error(res.Error))
		return nil, res.Error
	}
	return stage.ToProto(), nil
}

func (r *RechargeOrderStageImpl) GetByRechargeOrderId(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, rechargeOrderId string) ([]*billpaypb.RechargeOrderStage, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderStageImpl", "GetByRechargeOrderId", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	var stages []*model.RechargeOrderStage
	res := db.Model(&model.RechargeOrderStage{}).
		Where("recharge_order_id = ?", rechargeOrderId).
		Order("created_at desc").
		Select(fieldMask.GetPaths()).
		Find(&stages)
	if res.Error != nil {
		logger.Error(ctx, "Failed to get recharge order stages by recharge_order_id", zap.Error(res.Error))
		return nil, res.Error
	}
	stagesProto := make([]*billpaypb.RechargeOrderStage, 0, len(stages))
	for _, stage := range stages {
		stagesProto = append(stagesProto, stage.ToProto())
	}
	return stagesProto, nil
}

func (r *RechargeOrderStageImpl) GetByRechargeOrderIdAndStage(ctx context.Context, fieldMask *fieldmaskpb.FieldMask, rechargeOrderId string, stage billpayEnums.RechargeStage) (*billpaypb.RechargeOrderStage, error) {
	defer metric_util.TrackDuration("billpay/dao", "RechargeOrderStageImpl", "GetByRechargeOrderIdAndStage", time.Now())
	db := gormctxv2.FromContextOrDefault(ctx, r.db)
	var stageModel model.RechargeOrderStage
	res := db.Model(&model.RechargeOrderStage{}).
		Where("recharge_order_id = ? AND stage = ?", rechargeOrderId, stage).
		Select(fieldMask.GetPaths()).
		First(&stageModel)
	if res.Error != nil {
		logger.Error(ctx, "failed to fetch recharge order stage by recharge_order_id and stage", zap.Error(res.Error))
		return nil, res.Error
	}
	return stageModel.ToProto(), nil
}

type RechargeOrderStages []*model.RechargeOrderStage

func (rs RechargeOrderStages) Slice(start, end int) pagination.Rows { return rs[start:end] }
func (rs RechargeOrderStages) GetTimestamp(index int) time.Time     { return rs[index].CreatedAt }
func (rs RechargeOrderStages) Size() int                            { return len(rs) }
