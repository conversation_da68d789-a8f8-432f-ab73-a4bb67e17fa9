package model

import (
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayEnums "github.com/epifi/gamma/api/billpay/enums"
)

type RechargeOrder struct {
	Id                string `gorm:"primary_key"`
	ClientRequestId   string
	ActorId           string
	AccountType       billpayEnums.RechargeAccountType
	AccountIdentifier string
	AccountOperator   billpayEnums.Operator
	PlanDetails       *billpaypb.PlanDetails
	PlanIdentifier    string `gorm:"->"`
	Status            billpayEnums.RechargeOrderStatus
	SubStatus         billpayEnums.RechargeOrderSubStatus
	CreatedAt         time.Time
	UpdatedAt         time.Time
	CompletedAt       *time.Time
	DeletedAt         gorm.DeletedAt
}

func (m *RechargeOrder) TableName() string {
	return "recharge_orders"
}

func NewRechargeOrderFromProto(order *billpaypb.RechargeOrder) *RechargeOrder {
	if order == nil {
		return nil
	}
	return &RechargeOrder{
		Id:                order.GetId(),
		ClientRequestId:   order.GetClientRequestId(),
		ActorId:           order.GetActorId(),
		AccountType:       order.GetAccountType(),
		AccountIdentifier: order.GetAccountIdentifier(),
		AccountOperator:   order.GetAccountOperator(),
		PlanDetails:       order.GetPlanDetails(),
		PlanIdentifier:    order.GetPlanIdentifier(),
		Status:            order.GetStatus(),
		SubStatus:         order.GetSubStatus(),
		CompletedAt:       protoTimeToPtr(order.GetCompletedAt()),
	}
}

func (m *RechargeOrder) ToProto() *billpaypb.RechargeOrder {
	if m == nil {
		return nil
	}
	return &billpaypb.RechargeOrder{
		Id:                m.Id,
		ClientRequestId:   m.ClientRequestId,
		ActorId:           m.ActorId,
		AccountType:       m.AccountType,
		AccountIdentifier: m.AccountIdentifier,
		AccountOperator:   m.AccountOperator,
		PlanDetails:       m.PlanDetails,
		PlanIdentifier:    m.PlanIdentifier,
		Status:            m.Status,
		SubStatus:         m.SubStatus,
		CreatedAt:         timestamppb.New(m.CreatedAt),
		UpdatedAt:         timestamppb.New(m.UpdatedAt),
		CompletedAt:       timePtrToProto(m.CompletedAt),
	}
}
