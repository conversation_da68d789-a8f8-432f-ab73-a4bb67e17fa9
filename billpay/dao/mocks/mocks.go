// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/epifi/gamma/billpay/dao (interfaces: Category<PERSON><PERSON>,Biller<PERSON><PERSON>,ActorBillerAccountDao,BillFetchRequestDao,BillDao,PaymentDao,DisputeDao,RechargeOrderDao,RechargeOrderStageDao)

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	rpc "github.com/epifi/be-common/api/rpc"
	pagination "github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	billpaypb "github.com/epifi/gamma/api/billpay"
	enums "github.com/epifi/gamma/api/billpay/enums"
	gomock "github.com/golang/mock/gomock"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

// MockCategoryDao is a mock of CategoryDao interface.
type MockCategoryDao struct {
	ctrl     *gomock.Controller
	recorder *MockCategoryDaoMockRecorder
}

// MockCategoryDaoMockRecorder is the mock recorder for MockCategoryDao.
type MockCategoryDaoMockRecorder struct {
	mock *MockCategoryDao
}

// NewMockCategoryDao creates a new mock instance.
func NewMockCategoryDao(ctrl *gomock.Controller) *MockCategoryDao {
	mock := &MockCategoryDao{ctrl: ctrl}
	mock.recorder = &MockCategoryDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCategoryDao) EXPECT() *MockCategoryDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockCategoryDao) Create(arg0 context.Context, arg1 *billpaypb.Category) (*billpaypb.Category, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(*billpaypb.Category)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockCategoryDaoMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockCategoryDao)(nil).Create), arg0, arg1)
}

// GetById mocks base method.
func (m *MockCategoryDao) GetById(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.Category, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.Category)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockCategoryDaoMockRecorder) GetById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockCategoryDao)(nil).GetById), arg0, arg1, arg2)
}

// List mocks base method.
func (m *MockCategoryDao) List(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *pagination.PageToken, arg3 uint32) ([]*billpaypb.Category, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*billpaypb.Category)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// List indicates an expected call of List.
func (mr *MockCategoryDaoMockRecorder) List(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockCategoryDao)(nil).List), arg0, arg1, arg2, arg3)
}

// Update mocks base method.
func (m *MockCategoryDao) Update(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *billpaypb.Category) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockCategoryDaoMockRecorder) Update(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockCategoryDao)(nil).Update), arg0, arg1, arg2)
}

// MockBillerDao is a mock of BillerDao interface.
type MockBillerDao struct {
	ctrl     *gomock.Controller
	recorder *MockBillerDaoMockRecorder
}

// MockBillerDaoMockRecorder is the mock recorder for MockBillerDao.
type MockBillerDaoMockRecorder struct {
	mock *MockBillerDao
}

// NewMockBillerDao creates a new mock instance.
func NewMockBillerDao(ctrl *gomock.Controller) *MockBillerDao {
	mock := &MockBillerDao{ctrl: ctrl}
	mock.recorder = &MockBillerDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBillerDao) EXPECT() *MockBillerDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockBillerDao) Create(arg0 context.Context, arg1 *billpaypb.Biller) (*billpaypb.Biller, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(*billpaypb.Biller)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockBillerDaoMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockBillerDao)(nil).Create), arg0, arg1)
}

// GetByCategoryId mocks base method.
func (m *MockBillerDao) GetByCategoryId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.Biller, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCategoryId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.Biller)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByCategoryId indicates an expected call of GetByCategoryId.
func (mr *MockBillerDaoMockRecorder) GetByCategoryId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCategoryId", reflect.TypeOf((*MockBillerDao)(nil).GetByCategoryId), arg0, arg1, arg2, arg3, arg4)
}

// GetById mocks base method.
func (m *MockBillerDao) GetById(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.Biller, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.Biller)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockBillerDaoMockRecorder) GetById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockBillerDao)(nil).GetById), arg0, arg1, arg2)
}

// List mocks base method.
func (m *MockBillerDao) List(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *pagination.PageToken, arg3 uint32) ([]*billpaypb.Biller, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].([]*billpaypb.Biller)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// List indicates an expected call of List.
func (mr *MockBillerDaoMockRecorder) List(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockBillerDao)(nil).List), arg0, arg1, arg2, arg3)
}

// Update mocks base method.
func (m *MockBillerDao) Update(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *billpaypb.Biller) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockBillerDaoMockRecorder) Update(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockBillerDao)(nil).Update), arg0, arg1, arg2)
}

// MockActorBillerAccountDao is a mock of ActorBillerAccountDao interface.
type MockActorBillerAccountDao struct {
	ctrl     *gomock.Controller
	recorder *MockActorBillerAccountDaoMockRecorder
}

// MockActorBillerAccountDaoMockRecorder is the mock recorder for MockActorBillerAccountDao.
type MockActorBillerAccountDaoMockRecorder struct {
	mock *MockActorBillerAccountDao
}

// NewMockActorBillerAccountDao creates a new mock instance.
func NewMockActorBillerAccountDao(ctrl *gomock.Controller) *MockActorBillerAccountDao {
	mock := &MockActorBillerAccountDao{ctrl: ctrl}
	mock.recorder = &MockActorBillerAccountDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockActorBillerAccountDao) EXPECT() *MockActorBillerAccountDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockActorBillerAccountDao) Create(arg0 context.Context, arg1 *billpaypb.ActorBillerAccount) (*billpaypb.ActorBillerAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(*billpaypb.ActorBillerAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockActorBillerAccountDaoMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockActorBillerAccountDao)(nil).Create), arg0, arg1)
}

// GetByActorId mocks base method.
func (m *MockActorBillerAccountDao) GetByActorId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.ActorBillerAccount, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.ActorBillerAccount)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockActorBillerAccountDaoMockRecorder) GetByActorId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockActorBillerAccountDao)(nil).GetByActorId), arg0, arg1, arg2, arg3, arg4)
}

// GetByBillerId mocks base method.
func (m *MockActorBillerAccountDao) GetByBillerId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.ActorBillerAccount, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByBillerId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.ActorBillerAccount)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByBillerId indicates an expected call of GetByBillerId.
func (mr *MockActorBillerAccountDaoMockRecorder) GetByBillerId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByBillerId", reflect.TypeOf((*MockActorBillerAccountDao)(nil).GetByBillerId), arg0, arg1, arg2, arg3, arg4)
}

// GetById mocks base method.
func (m *MockActorBillerAccountDao) GetById(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.ActorBillerAccount, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.ActorBillerAccount)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockActorBillerAccountDaoMockRecorder) GetById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockActorBillerAccountDao)(nil).GetById), arg0, arg1, arg2)
}

// Update mocks base method.
func (m *MockActorBillerAccountDao) Update(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *billpaypb.ActorBillerAccount) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockActorBillerAccountDaoMockRecorder) Update(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockActorBillerAccountDao)(nil).Update), arg0, arg1, arg2)
}

// MockBillFetchRequestDao is a mock of BillFetchRequestDao interface.
type MockBillFetchRequestDao struct {
	ctrl     *gomock.Controller
	recorder *MockBillFetchRequestDaoMockRecorder
}

// MockBillFetchRequestDaoMockRecorder is the mock recorder for MockBillFetchRequestDao.
type MockBillFetchRequestDaoMockRecorder struct {
	mock *MockBillFetchRequestDao
}

// NewMockBillFetchRequestDao creates a new mock instance.
func NewMockBillFetchRequestDao(ctrl *gomock.Controller) *MockBillFetchRequestDao {
	mock := &MockBillFetchRequestDao{ctrl: ctrl}
	mock.recorder = &MockBillFetchRequestDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBillFetchRequestDao) EXPECT() *MockBillFetchRequestDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockBillFetchRequestDao) Create(arg0 context.Context, arg1 *billpaypb.BillFetchRequest) (*billpaypb.BillFetchRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(*billpaypb.BillFetchRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockBillFetchRequestDaoMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockBillFetchRequestDao)(nil).Create), arg0, arg1)
}

// GetByActorBillerAccountId mocks base method.
func (m *MockBillFetchRequestDao) GetByActorBillerAccountId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.BillFetchRequest, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorBillerAccountId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.BillFetchRequest)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByActorBillerAccountId indicates an expected call of GetByActorBillerAccountId.
func (mr *MockBillFetchRequestDaoMockRecorder) GetByActorBillerAccountId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorBillerAccountId", reflect.TypeOf((*MockBillFetchRequestDao)(nil).GetByActorBillerAccountId), arg0, arg1, arg2, arg3, arg4)
}

// GetByActorId mocks base method.
func (m *MockBillFetchRequestDao) GetByActorId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.BillFetchRequest, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.BillFetchRequest)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockBillFetchRequestDaoMockRecorder) GetByActorId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockBillFetchRequestDao)(nil).GetByActorId), arg0, arg1, arg2, arg3, arg4)
}

// GetById mocks base method.
func (m *MockBillFetchRequestDao) GetById(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.BillFetchRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.BillFetchRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockBillFetchRequestDaoMockRecorder) GetById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockBillFetchRequestDao)(nil).GetById), arg0, arg1, arg2)
}

// Update mocks base method.
func (m *MockBillFetchRequestDao) Update(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *billpaypb.BillFetchRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockBillFetchRequestDaoMockRecorder) Update(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockBillFetchRequestDao)(nil).Update), arg0, arg1, arg2)
}

// MockBillDao is a mock of BillDao interface.
type MockBillDao struct {
	ctrl     *gomock.Controller
	recorder *MockBillDaoMockRecorder
}

// MockBillDaoMockRecorder is the mock recorder for MockBillDao.
type MockBillDaoMockRecorder struct {
	mock *MockBillDao
}

// NewMockBillDao creates a new mock instance.
func NewMockBillDao(ctrl *gomock.Controller) *MockBillDao {
	mock := &MockBillDao{ctrl: ctrl}
	mock.recorder = &MockBillDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBillDao) EXPECT() *MockBillDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockBillDao) Create(arg0 context.Context, arg1 *billpaypb.Bill) (*billpaypb.Bill, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(*billpaypb.Bill)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockBillDaoMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockBillDao)(nil).Create), arg0, arg1)
}

// GetByActorId mocks base method.
func (m *MockBillDao) GetByActorId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.Bill, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.Bill)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockBillDaoMockRecorder) GetByActorId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockBillDao)(nil).GetByActorId), arg0, arg1, arg2, arg3, arg4)
}

// GetByBillFetchRequestId mocks base method.
func (m *MockBillDao) GetByBillFetchRequestId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.Bill, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByBillFetchRequestId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.Bill)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByBillFetchRequestId indicates an expected call of GetByBillFetchRequestId.
func (mr *MockBillDaoMockRecorder) GetByBillFetchRequestId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByBillFetchRequestId", reflect.TypeOf((*MockBillDao)(nil).GetByBillFetchRequestId), arg0, arg1, arg2)
}

// GetByDueDate mocks base method.
func (m *MockBillDao) GetByDueDate(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2, arg3 *timestamppb.Timestamp, arg4 *pagination.PageToken, arg5 uint32) ([]*billpaypb.Bill, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByDueDate", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*billpaypb.Bill)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByDueDate indicates an expected call of GetByDueDate.
func (mr *MockBillDaoMockRecorder) GetByDueDate(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByDueDate", reflect.TypeOf((*MockBillDao)(nil).GetByDueDate), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetById mocks base method.
func (m *MockBillDao) GetById(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.Bill, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.Bill)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockBillDaoMockRecorder) GetById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockBillDao)(nil).GetById), arg0, arg1, arg2)
}

// GetByStatus mocks base method.
func (m *MockBillDao) GetByStatus(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 billpaypb.BillStatus, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.Bill, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.Bill)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByStatus indicates an expected call of GetByStatus.
func (mr *MockBillDaoMockRecorder) GetByStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByStatus", reflect.TypeOf((*MockBillDao)(nil).GetByStatus), arg0, arg1, arg2, arg3, arg4)
}

// Update mocks base method.
func (m *MockBillDao) Update(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *billpaypb.Bill) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockBillDaoMockRecorder) Update(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockBillDao)(nil).Update), arg0, arg1, arg2)
}

// MockPaymentDao is a mock of PaymentDao interface.
type MockPaymentDao struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentDaoMockRecorder
}

// MockPaymentDaoMockRecorder is the mock recorder for MockPaymentDao.
type MockPaymentDaoMockRecorder struct {
	mock *MockPaymentDao
}

// NewMockPaymentDao creates a new mock instance.
func NewMockPaymentDao(ctrl *gomock.Controller) *MockPaymentDao {
	mock := &MockPaymentDao{ctrl: ctrl}
	mock.recorder = &MockPaymentDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentDao) EXPECT() *MockPaymentDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockPaymentDao) Create(arg0 context.Context, arg1 *billpaypb.Payment) (*billpaypb.Payment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(*billpaypb.Payment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockPaymentDaoMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockPaymentDao)(nil).Create), arg0, arg1)
}

// GetByActorId mocks base method.
func (m *MockPaymentDao) GetByActorId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.Payment, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.Payment)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockPaymentDaoMockRecorder) GetByActorId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockPaymentDao)(nil).GetByActorId), arg0, arg1, arg2, arg3, arg4)
}

// GetByBillerId mocks base method.
func (m *MockPaymentDao) GetByBillerId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.Payment, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByBillerId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.Payment)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByBillerId indicates an expected call of GetByBillerId.
func (mr *MockPaymentDaoMockRecorder) GetByBillerId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByBillerId", reflect.TypeOf((*MockPaymentDao)(nil).GetByBillerId), arg0, arg1, arg2, arg3, arg4)
}

// GetByBillsRefId mocks base method.
func (m *MockPaymentDao) GetByBillsRefId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) ([]*billpaypb.Payment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByBillsRefId", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*billpaypb.Payment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByBillsRefId indicates an expected call of GetByBillsRefId.
func (mr *MockPaymentDaoMockRecorder) GetByBillsRefId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByBillsRefId", reflect.TypeOf((*MockPaymentDao)(nil).GetByBillsRefId), arg0, arg1, arg2)
}

// GetById mocks base method.
func (m *MockPaymentDao) GetById(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.Payment, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.Payment)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockPaymentDaoMockRecorder) GetById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockPaymentDao)(nil).GetById), arg0, arg1, arg2)
}

// Update mocks base method.
func (m *MockPaymentDao) Update(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *billpaypb.Payment) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockPaymentDaoMockRecorder) Update(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockPaymentDao)(nil).Update), arg0, arg1, arg2)
}

// MockDisputeDao is a mock of DisputeDao interface.
type MockDisputeDao struct {
	ctrl     *gomock.Controller
	recorder *MockDisputeDaoMockRecorder
}

// MockDisputeDaoMockRecorder is the mock recorder for MockDisputeDao.
type MockDisputeDaoMockRecorder struct {
	mock *MockDisputeDao
}

// NewMockDisputeDao creates a new mock instance.
func NewMockDisputeDao(ctrl *gomock.Controller) *MockDisputeDao {
	mock := &MockDisputeDao{ctrl: ctrl}
	mock.recorder = &MockDisputeDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDisputeDao) EXPECT() *MockDisputeDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockDisputeDao) Create(arg0 context.Context, arg1 *billpaypb.Dispute) (*billpaypb.Dispute, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(*billpaypb.Dispute)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockDisputeDaoMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockDisputeDao)(nil).Create), arg0, arg1)
}

// GetByActorId mocks base method.
func (m *MockDisputeDao) GetByActorId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.Dispute, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.Dispute)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockDisputeDaoMockRecorder) GetByActorId(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockDisputeDao)(nil).GetByActorId), arg0, arg1, arg2, arg3, arg4)
}

// GetById mocks base method.
func (m *MockDisputeDao) GetById(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.Dispute, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.Dispute)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockDisputeDaoMockRecorder) GetById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockDisputeDao)(nil).GetById), arg0, arg1, arg2)
}

// GetByPaymentId mocks base method.
func (m *MockDisputeDao) GetByPaymentId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) ([]*billpaypb.Dispute, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPaymentId", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*billpaypb.Dispute)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByPaymentId indicates an expected call of GetByPaymentId.
func (mr *MockDisputeDaoMockRecorder) GetByPaymentId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPaymentId", reflect.TypeOf((*MockDisputeDao)(nil).GetByPaymentId), arg0, arg1, arg2)
}

// GetByStatus mocks base method.
func (m *MockDisputeDao) GetByStatus(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 billpaypb.DisputeStatus, arg3 *pagination.PageToken, arg4 uint32) ([]*billpaypb.Dispute, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].([]*billpaypb.Dispute)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByStatus indicates an expected call of GetByStatus.
func (mr *MockDisputeDaoMockRecorder) GetByStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByStatus", reflect.TypeOf((*MockDisputeDao)(nil).GetByStatus), arg0, arg1, arg2, arg3, arg4)
}

// Update mocks base method.
func (m *MockDisputeDao) Update(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *billpaypb.Dispute) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockDisputeDaoMockRecorder) Update(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockDisputeDao)(nil).Update), arg0, arg1, arg2)
}

// MockRechargeOrderDao is a mock of RechargeOrderDao interface.
type MockRechargeOrderDao struct {
	ctrl     *gomock.Controller
	recorder *MockRechargeOrderDaoMockRecorder
}

// MockRechargeOrderDaoMockRecorder is the mock recorder for MockRechargeOrderDao.
type MockRechargeOrderDaoMockRecorder struct {
	mock *MockRechargeOrderDao
}

// NewMockRechargeOrderDao creates a new mock instance.
func NewMockRechargeOrderDao(ctrl *gomock.Controller) *MockRechargeOrderDao {
	mock := &MockRechargeOrderDao{ctrl: ctrl}
	mock.recorder = &MockRechargeOrderDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRechargeOrderDao) EXPECT() *MockRechargeOrderDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRechargeOrderDao) Create(arg0 context.Context, arg1 *billpaypb.RechargeOrder) (*billpaypb.RechargeOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(*billpaypb.RechargeOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRechargeOrderDaoMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRechargeOrderDao)(nil).Create), arg0, arg1)
}

// GetByActorId mocks base method.
func (m *MockRechargeOrderDao) GetByActorId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 *pagination.PageToken, arg4 uint32, arg5 []storagev2.FilterOption) ([]*billpaypb.RechargeOrder, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByActorId", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].([]*billpaypb.RechargeOrder)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetByActorId indicates an expected call of GetByActorId.
func (mr *MockRechargeOrderDaoMockRecorder) GetByActorId(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorId", reflect.TypeOf((*MockRechargeOrderDao)(nil).GetByActorId), arg0, arg1, arg2, arg3, arg4, arg5)
}

// GetByActorIdAndAccountDetails mocks base method.
func (m *MockRechargeOrderDao) GetByActorIdAndAccountDetails(arg0 context.Context, arg1 string, arg2 enums.RechargeAccountType, arg3 string, arg4 ...storagev2.FilterOption) ([]*billpaypb.RechargeOrder, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByActorIdAndAccountDetails", varargs...)
	ret0, _ := ret[0].([]*billpaypb.RechargeOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByActorIdAndAccountDetails indicates an expected call of GetByActorIdAndAccountDetails.
func (mr *MockRechargeOrderDaoMockRecorder) GetByActorIdAndAccountDetails(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByActorIdAndAccountDetails", reflect.TypeOf((*MockRechargeOrderDao)(nil).GetByActorIdAndAccountDetails), varargs...)
}

// GetByClientRequestId mocks base method.
func (m *MockRechargeOrderDao) GetByClientRequestId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.RechargeOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientRequestId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.RechargeOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientRequestId indicates an expected call of GetByClientRequestId.
func (mr *MockRechargeOrderDaoMockRecorder) GetByClientRequestId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientRequestId", reflect.TypeOf((*MockRechargeOrderDao)(nil).GetByClientRequestId), arg0, arg1, arg2)
}

// GetById mocks base method.
func (m *MockRechargeOrderDao) GetById(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.RechargeOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.RechargeOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockRechargeOrderDaoMockRecorder) GetById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockRechargeOrderDao)(nil).GetById), arg0, arg1, arg2)
}

// GetLatestOrdersForUniqueAccountsByActor mocks base method.
func (m *MockRechargeOrderDao) GetLatestOrdersForUniqueAccountsByActor(arg0 context.Context, arg1 string, arg2 enums.RechargeAccountType) ([]*billpaypb.RechargeOrder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLatestOrdersForUniqueAccountsByActor", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*billpaypb.RechargeOrder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLatestOrdersForUniqueAccountsByActor indicates an expected call of GetLatestOrdersForUniqueAccountsByActor.
func (mr *MockRechargeOrderDaoMockRecorder) GetLatestOrdersForUniqueAccountsByActor(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLatestOrdersForUniqueAccountsByActor", reflect.TypeOf((*MockRechargeOrderDao)(nil).GetLatestOrdersForUniqueAccountsByActor), arg0, arg1, arg2)
}

// Update mocks base method.
func (m *MockRechargeOrderDao) Update(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *billpaypb.RechargeOrder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRechargeOrderDaoMockRecorder) Update(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRechargeOrderDao)(nil).Update), arg0, arg1, arg2)
}

// MockRechargeOrderStageDao is a mock of RechargeOrderStageDao interface.
type MockRechargeOrderStageDao struct {
	ctrl     *gomock.Controller
	recorder *MockRechargeOrderStageDaoMockRecorder
}

// MockRechargeOrderStageDaoMockRecorder is the mock recorder for MockRechargeOrderStageDao.
type MockRechargeOrderStageDaoMockRecorder struct {
	mock *MockRechargeOrderStageDao
}

// NewMockRechargeOrderStageDao creates a new mock instance.
func NewMockRechargeOrderStageDao(ctrl *gomock.Controller) *MockRechargeOrderStageDao {
	mock := &MockRechargeOrderStageDao{ctrl: ctrl}
	mock.recorder = &MockRechargeOrderStageDaoMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRechargeOrderStageDao) EXPECT() *MockRechargeOrderStageDaoMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockRechargeOrderStageDao) Create(arg0 context.Context, arg1 *billpaypb.RechargeOrderStage) (*billpaypb.RechargeOrderStage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", arg0, arg1)
	ret0, _ := ret[0].(*billpaypb.RechargeOrderStage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockRechargeOrderStageDaoMockRecorder) Create(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockRechargeOrderStageDao)(nil).Create), arg0, arg1)
}

// GetByClientRequestId mocks base method.
func (m *MockRechargeOrderStageDao) GetByClientRequestId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.RechargeOrderStage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByClientRequestId", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.RechargeOrderStage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByClientRequestId indicates an expected call of GetByClientRequestId.
func (mr *MockRechargeOrderStageDaoMockRecorder) GetByClientRequestId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByClientRequestId", reflect.TypeOf((*MockRechargeOrderStageDao)(nil).GetByClientRequestId), arg0, arg1, arg2)
}

// GetById mocks base method.
func (m *MockRechargeOrderStageDao) GetById(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) (*billpaypb.RechargeOrderStage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", arg0, arg1, arg2)
	ret0, _ := ret[0].(*billpaypb.RechargeOrderStage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockRechargeOrderStageDaoMockRecorder) GetById(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockRechargeOrderStageDao)(nil).GetById), arg0, arg1, arg2)
}

// GetByRechargeOrderId mocks base method.
func (m *MockRechargeOrderStageDao) GetByRechargeOrderId(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string) ([]*billpaypb.RechargeOrderStage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRechargeOrderId", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*billpaypb.RechargeOrderStage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRechargeOrderId indicates an expected call of GetByRechargeOrderId.
func (mr *MockRechargeOrderStageDaoMockRecorder) GetByRechargeOrderId(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRechargeOrderId", reflect.TypeOf((*MockRechargeOrderStageDao)(nil).GetByRechargeOrderId), arg0, arg1, arg2)
}

// GetByRechargeOrderIdAndStage mocks base method.
func (m *MockRechargeOrderStageDao) GetByRechargeOrderIdAndStage(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 string, arg3 enums.RechargeStage) (*billpaypb.RechargeOrderStage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRechargeOrderIdAndStage", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*billpaypb.RechargeOrderStage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRechargeOrderIdAndStage indicates an expected call of GetByRechargeOrderIdAndStage.
func (mr *MockRechargeOrderStageDaoMockRecorder) GetByRechargeOrderIdAndStage(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRechargeOrderIdAndStage", reflect.TypeOf((*MockRechargeOrderStageDao)(nil).GetByRechargeOrderIdAndStage), arg0, arg1, arg2, arg3)
}

// Update mocks base method.
func (m *MockRechargeOrderStageDao) Update(arg0 context.Context, arg1 *fieldmaskpb.FieldMask, arg2 *billpaypb.RechargeOrderStage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockRechargeOrderStageDaoMockRecorder) Update(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockRechargeOrderStageDao)(nil).Update), arg0, arg1, arg2)
}
