package workflow

import (
	"fmt"

	"go.temporal.io/sdk/workflow"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
)

func getWorkflowProcessingParams(ctx workflow.Context) (*workflowPb.ProcessingParams, error) {
	wfReqID := workflow.GetInfo(ctx).WorkflowExecution.ID
	wfProcessingParams := &activityPb.GetWorkflowProcessingParamsV2Response{}
	if err := activityPkg.Execute(ctx, epifitemporal.GetWorkflowProcessingParamsV2, wfProcessingParams, &activityPb.GetWorkflowProcessingParamsV2Request{
		RequestHeader: &activityPb.RequestHeader{
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
		WfReqId: wfReqID,
	}); err != nil {
		return nil, fmt.Errorf("failed to get workflow processing params: %w", err)
	}

	return wfProcessingParams.GetWfReqParams(), nil
}
