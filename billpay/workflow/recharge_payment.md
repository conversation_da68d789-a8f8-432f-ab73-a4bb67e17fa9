# RechargePayment Workflow

## Overview

The RechargePayment workflow is a Temporal-based workflow that handles the complete lifecycle of mobile/DTH recharge transactions in the Fi Money platform. It implements a three-stage process: Payment, Fulfillment, and Refund (conditional).

## Workflow Stages

The workflow executes through three distinct stages as defined in `protos/api/billpay/enums/enums.proto:`:

1. **RECHARGE_STAGE_PAYMENT** - Handles payment authorization and verification
2. **RECHARGE_STAGE_FULFILLMENT** - Processes recharge with vendor and status verification
3. **RECHARGE_STAGE_REFUND** - Handles refund processing (conditional based on fulfillment outcome)

## Architecture

### Activities Used

The workflow utilizes the following activities defined in `be-common/pkg/epifitemporal/namespace/pay/pay.go:88`:

- `UpdateRechargeOrderStatus` - Updates the overall order status
- `CreateRechargeOrderStage` - Creates a new stage in the order lifecycle
- `UpdateRechargeOrderStage` - Updates the status of a specific stage
- `EnquireRechargePaymentStatus` - Checks payment transaction status
- `InitiateRechargeWithVendor` - Initiates recharge with vendor partner
- `EnquireRechargeStatusWithVendor` - Checks recharge status with vendor
- `ShouldInitiateRechargeRefundOrder` - Determines if refund is required
- `InitiateRechargeRefundOrder` - Initiates refund process
- `EnquireRechargeRefundOrderStatus` - Checks refund status

### Signal Handling

The workflow listens for `RechargeFundTransferAuthSignal` during the payment stage to confirm user authentication.

## Workflow Flow

```mermaid
graph TD
    A[Start Workflow] --> B[Update Order Status to IN_PROGRESS]
    B --> C[Create Payment Stage]
    C --> D[Listen for Auth Signal with 20min timeout]
    
    D --> E{Signal Received?}
    E -->|No| F[Mark Stage as EXPIRED]
    F --> G[Update Order Status to EXPIRED]
    G --> H[Terminate Workflow]
    
    E -->|Yes| I[Update Stage to IN_PROGRESS]
    I --> J[Enquire Payment Status]
    
    J --> K{Payment Status?}
    K -->|Transient Error| L[Mark Stage as MANUAL_INTERVENTION]
    L --> M[Update Order Status to MANUAL_INTERVENTION]
    M --> H
    
    K -->|Permanent Error| N[Mark Stage as FAILED]
    N --> O[Update Order Status to FAILED]
    O --> H
    
    K -->|Success| P[Mark Payment Stage as SUCCESS]
    P --> Q[Create Fulfillment Stage]
    
    Q --> R[Initiate Recharge with Vendor]
    R --> S{Initiate Status?}
    S -->|Transient Error| T[Mark Stage as MANUAL_INTERVENTION]
    T --> U[Update Order Status to MANUAL_INTERVENTION]
    U --> H
    
    S -->|Permanent Error| V[Mark Stage as FAILED]
    V --> W[Proceed to Refund Check]
    
    S -->|Success| X[Enquire Recharge Status with Vendor]
    X --> Y{Enquiry Status?}
    Y -->|Transient Error| T
    Y -->|Permanent Error| V
    Y -->|Success| Z[Mark Fulfillment Stage as SUCCESS]
    
    Z --> W
    W --> AA[Check if Refund Needed]
    AA --> BB{Refund Required?}
    
    BB -->|No| CC[Update Order Status to SUCCESS]
    CC --> H
    
    BB -->|Yes| DD[Create Refund Stage]
    DD --> EE[Initiate Refund Order]
    
    EE --> FF{Refund Initiate Status?}
    FF -->|Transient Error| GG[Mark Stage as MANUAL_INTERVENTION]
    GG --> HH[Update Order Status to MANUAL_INTERVENTION]
    HH --> H
    
    FF -->|Permanent Error| II[Mark Stage as FAILED]
    II --> HH
    
    FF -->|Success| JJ[Enquire Refund Status]
    JJ --> KK{Refund Enquiry Status?}
    KK -->|Transient Error| GG
    KK -->|Permanent Error| II
    KK -->|Success| LL[Mark Refund Stage as SUCCESS]
    LL --> MM[Update Order Status to REFUNDED]
    MM --> H
```

## Stage Details

### 1. RECHARGE_STAGE_PAYMENT

**Purpose**: Handles payment authorization and verification

**Flow**:
1. Updates order status to `RECHARGE_ORDER_STATUS_IN_PROGRESS`
2. Creates payment stage record
3. Listens for `RechargeFundTransferAuthSignal` with 20-minute timeout
4. If signal received:
   - Updates stage to `IN_PROGRESS`
   - Enquires payment status
   - Handles response based on success/failure
5. If signal not received within timeout:
   - Marks stage as `EXPIRED`
   - Updates order status to `EXPIRED`
   - Terminates workflow

**Error Handling**:
- **Transient errors**: Mark stage and order as `MANUAL_INTERVENTION`
- **Permanent errors**: Mark stage as `FAILED`, order as `FAILED`
- **Timeout**: Mark stage as `EXPIRED`, order as `EXPIRED`

### 2. RECHARGE_STAGE_FULFILLMENT

**Purpose**: Processes recharge with vendor and verifies completion

**Flow**:
1. Creates fulfillment stage record
2. Initiates recharge with vendor partner
3. Enquires recharge status with vendor
4. Updates stage status based on results

**Error Handling**:
- **Transient errors**: Mark stage as `MANUAL_INTERVENTION`, order as `MANUAL_INTERVENTION`
- **Permanent errors**: Mark stage as `FAILED`, proceed to refund check

### 3. RECHARGE_STAGE_REFUND (Conditional)

**Purpose**: Handles refund processing when required

**Trigger**: Executed only if `ShouldInitiateRechargeRefundOrder` returns `true`

**Flow**:
1. Creates refund stage record
2. Initiates refund order
3. Enquires refund status
4. Updates final order status to `FAILED` with `REFUNDED` sub-status

**Error Handling**:
- **Transient errors**: Mark stage as `MANUAL_INTERVENTION`, order as `MANUAL_INTERVENTION`
- **Permanent errors**: Mark stage as `FAILED`, order as `MANUAL_INTERVENTION`

## Order Status Flow

```mermaid
stateDiagram-v2
    [*] --> IN_PROGRESS: Workflow Start
    IN_PROGRESS --> EXPIRED: Payment Timeout
    IN_PROGRESS --> FAILED: Payment Failed (Permanent)
    IN_PROGRESS --> MANUAL_INTERVENTION: Payment/Fulfillment Error (Transient)
    IN_PROGRESS --> SUCCESS: Fulfillment Success + No Refund
    IN_PROGRESS --> REFUNDED: Refund Success
    IN_PROGRESS --> MANUAL_INTERVENTION: Refund Error
    
    EXPIRED --> [*]
    FAILED --> [*]
    MANUAL_INTERVENTION --> [*]
    SUCCESS --> [*]
    REFUNDED --> [*]
```

## Configuration

### Activity Registration

All activities must be registered in `gamma/cmd/worker/pay/config/pay-params.yml` under `WorkflowParamsList`.

Example:
```yaml
WorkflowParamsList:
  - UpdateRechargeOrderStatus
  - CreateRechargeOrderStage
  - UpdateRechargeOrderStage
  - EnquireRechargePaymentStatus
  - InitiateRechargeWithVendor
  - EnquireRechargeStatusWithVendor
  - ShouldInitiateRechargeRefundOrder
  - InitiateRechargeRefundOrder
  - EnquireRechargeRefundOrderStatus
```

### Input Parameters

The workflow expects a `billpayWfPb.RechargePaymentRequest` with:
- **ClientRequestId**: Unique identifier for tracking the recharge order

## Error Classification

The workflow uses `epifitemporal.IsRetryableError()` to classify errors:

- **Transient Errors**: Network issues, temporary service unavailability
  - Action: Mark as `MANUAL_INTERVENTION` for human review
- **Permanent Errors**: Invalid parameters, business rule violations
  - Action: Mark as `FAILED` or proceed to next stage based on context


## Related Files

- **Workflow Implementation**: `gamma/billpay/workflow/recharge_payment.go`
- **Activity Implementations**: `gamma/billpay/activity/`
- **Protocol Definitions**: `protos/api/billpay/`
- **Configuration**: `gamma/cmd/worker/pay/config/pay-params.yml`
- **Database Schema**: `gamma/db/billpay/`

### Recovery Procedures

- Orders in `MANUAL_INTERVENTION` status require developer review
