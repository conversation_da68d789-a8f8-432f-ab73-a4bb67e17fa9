package workflow_test

import (
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/encoding/protojson"

	celestialActivityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/celestial/workflow"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifitemporal"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	billpayWfPb "github.com/epifi/gamma/api/billpay/workflow"
	billpayActivity "github.com/epifi/gamma/billpay/activity"
	billpayUtils "github.com/epifi/gamma/billpay/utils"
	billpayWorkflow "github.com/epifi/gamma/billpay/workflow"
	celestialActivityV2 "github.com/epifi/gamma/celestial/activity/v2"
)

const (
	testClientRequestId            = "test-client-req-id"
	testPaymentClientRequestId     = "test-payment-client-req-id"
	testFulfillmentClientRequestId = "test-fulfillment-client-req-id"
	testRefundClientRequestId      = "test-refund-client-req-id"
	defaultWorkflowID              = "default-test-workflow-id"
)

type activityMock struct {
	enable   bool
	activity epifitemporal.Activity
	req      interface{}
	res      []interface{}
}

type signalMock struct {
	delay      time.Duration
	signal     interface{}
	signalName epifitemporal.Signal
}

var (
	payload, _ = protojson.Marshal(&billpayWfPb.RechargePaymentRequest{
		ClientRequestId: testClientRequestId,
	})
)

func TestRechargePaymentWorkflow(t *testing.T) {
	tests := []struct {
		name              string
		mockActivities    []activityMock
		mockSignals       []*signalMock
		want              interface{}
		wantErr           bool
		workflowCompletes bool
	}{
		{
			name: "happy_flow_no_refund_needed",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// CreateRechargeOrderStage for PAYMENT
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				// UpdateRechargeOrderStage for PAYMENT INITIATED
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// EnquireRechargePaymentStatus
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargePaymentStatusResponse{}, nil},
				},
				// UpdateRechargeOrderStage for PAYMENT SUCCESS
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeFulfilment
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeFulfilment,
					req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
						ShouldInitiateFulfilment: true, // Fulfillment should be initiated for happy flow
					}, nil},
				},
				// CreateRechargeOrderStage for FULFILLMENT
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testFulfillmentClientRequestId,
						},
					}, nil},
				},
				// InitiateRechargeWithVendor
				{
					enable:   true,
					activity: payNs.InitiateRechargeWithVendor,
					req: &billpayActPb.InitiateRechargeWithVendorRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
					},
					res: []interface{}{&billpayActPb.InitiateRechargeWithVendorResponse{}, nil},
				},
				// EnquireRechargeStatusWithVendor
				{
					enable:   true,
					activity: payNs.EnquireRechargeStatusWithVendor,
					req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargeStatusWithVendorResponse{}, nil},
				},
				// UpdateRechargeOrderStage for FULFILLMENT SUCCESS
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// UpdateRechargeOrderStatus to SUCCESS (moved after fulfillment success)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// ShouldInitiateRechargeRefundOrder
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeRefundOrder,
					req: &billpayActPb.ShouldInitiateRechargeRefundOrderRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeRefundOrderResponse{
						ShouldInitiateRefund: false, // No refund needed for happy flow
					}, nil},
				},
			},
			mockSignals: []*signalMock{
				{
					delay:      10 * time.Nanosecond,
					signal:     []byte("auth_signal"),
					signalName: payNs.RechargeFundTransferAuthSignal,
				},
			},
			want:              nil,
			wantErr:           false,
			workflowCompletes: true,
		},
		{
			name: "payment_enquiry_transient_error_retry",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// CreateRechargeOrderStage for PAYMENT
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				// UpdateRechargeOrderStage for PAYMENT INITIATED
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// EnquireRechargePaymentStatus fails with transient error
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewTransientError(errors.New("payment enquiry failed"))},
				},
				// UpdateRechargeOrderStage for PAYMENT MANUAL_INTERVENTION (for transient error)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// UpdateRechargeOrderStatus to MANUAL_INTERVENTION
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
			},
			mockSignals: []*signalMock{
				{
					delay:      10 * time.Nanosecond,
					signal:     []byte("auth_signal"),
					signalName: payNs.RechargeFundTransferAuthSignal,
				},
			},
			want:              nil,
			wantErr:           true,
			workflowCompletes: false,
		},
		{
			name: "payment_failed_permanent_error",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// CreateRechargeOrderStage for PAYMENT
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				// UpdateRechargeOrderStage for PAYMENT INITIATED
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// EnquireRechargePaymentStatus fails with permanent error
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewPermanentError(errors.New("payment failed permanently"))},
				},
				// UpdateRechargeOrderStage for PAYMENT FAILED (for permanent error)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// UpdateRechargeOrderStatus to FAILED
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeFulfilment,
					req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
						ShouldInitiateFulfilment: false,
					}, nil},
				},
			},
			mockSignals: []*signalMock{
				{
					delay:      10 * time.Nanosecond,
					signal:     []byte("auth_signal"),
					signalName: payNs.RechargeFundTransferAuthSignal,
				},
			},
			want:              nil,
			wantErr:           false,
			workflowCompletes: true,
		},
		{
			name: "payment_in_manual_intervention",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// CreateRechargeOrderStage for PAYMENT
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				// UpdateRechargeOrderStage for PAYMENT INITIATED
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// EnquireRechargePaymentStatus fails with transient error (max retries exhausted)
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewTransientError(errors.New("max retries exhausted"))},
				},
				// UpdateRechargeOrderStage for PAYMENT MANUAL_INTERVENTION
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// UpdateRechargeOrderStatus to MANUAL_INTERVENTION
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
			},
			mockSignals: []*signalMock{
				{
					delay:      10 * time.Nanosecond,
					signal:     []byte("auth_signal"),
					signalName: payNs.RechargeFundTransferAuthSignal,
				},
			},
			want:              nil,
			wantErr:           true,
			workflowCompletes: false,
		},
		{
			name: "fulfillment_initiate_permanent_failure",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// Payment stage - successful flow
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargePaymentStatusResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeFulfilment - check if fulfillment should be initiated
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeFulfilment,
					req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
						ShouldInitiateFulfilment: true,
					}, nil},
				},
				// Fulfillment stage starts
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testFulfillmentClientRequestId,
						},
					}, nil},
				},
				// InitiateRechargeWithVendor fails with permanent error
				{
					enable:   true,
					activity: payNs.InitiateRechargeWithVendor,
					req: &billpayActPb.InitiateRechargeWithVendorRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewPermanentError(errors.New("vendor initiate failed permanently"))},
				},
				// UpdateRechargeOrderStage for FULFILLMENT FAILED
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeRefundOrder - returns true since fulfillment failed
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeRefundOrder,
					req: &billpayActPb.ShouldInitiateRechargeRefundOrderRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeRefundOrderResponse{
						ShouldInitiateRefund: true,
					}, nil},
				},
				// Refund stage - successful flow
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_REFUND,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testRefundClientRequestId,
						},
					}, nil},
				},
				{
					enable:   true,
					activity: payNs.InitiateRechargeRefundOrder,
					req: &billpayActPb.InitiateRechargeRefundOrderRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						RefundClientRequestId: testRefundClientRequestId,
					},
					res: []interface{}{&billpayActPb.InitiateRechargeRefundOrderResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.EnquireRechargeRefundOrderStatus,
					req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						RefundClientRequestId: testRefundClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargeRefundOrderStatusResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_REFUND,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
			},
			mockSignals: []*signalMock{
				{
					delay:      10 * time.Nanosecond,
					signal:     []byte("auth_signal"),
					signalName: payNs.RechargeFundTransferAuthSignal,
				},
			},
			want:              nil,
			wantErr:           false,
			workflowCompletes: true,
		},
		{
			name: "fulfillment_initiate_transient_failure",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// Payment stage - successful flow
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargePaymentStatusResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeFulfilment - check if fulfillment should be initiated
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeFulfilment,
					req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
						ShouldInitiateFulfilment: true,
					}, nil},
				},
				// Fulfillment stage starts
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testFulfillmentClientRequestId,
						},
					}, nil},
				},
				// InitiateRechargeWithVendor fails with transient error (max retries exhausted)
				{
					enable:   true,
					activity: payNs.InitiateRechargeWithVendor,
					req: &billpayActPb.InitiateRechargeWithVendorRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewTransientError(errors.New("failure"))},
				},
				// UpdateRechargeOrderStage for FULFILLMENT MANUAL_INTERVENTION
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// UpdateRechargeOrderStatus to MANUAL_INTERVENTION
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
			},
			mockSignals: []*signalMock{
				{
					delay:      10 * time.Nanosecond,
					signal:     []byte("auth_signal"),
					signalName: payNs.RechargeFundTransferAuthSignal,
				},
			},
			want:              nil,
			wantErr:           true,
			workflowCompletes: false,
		},
		{
			name: "fulfillment_enquiry_permanent_failure",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// Payment stage - successful flow
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargePaymentStatusResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeFulfilment - check if fulfillment should be initiated
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeFulfilment,
					req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
						ShouldInitiateFulfilment: true,
					}, nil},
				},
				// Fulfillment stage starts
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testFulfillmentClientRequestId,
						},
					}, nil},
				},
				// InitiateRechargeWithVendor succeeds
				{
					enable:   true,
					activity: payNs.InitiateRechargeWithVendor,
					req: &billpayActPb.InitiateRechargeWithVendorRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
					},
					res: []interface{}{&billpayActPb.InitiateRechargeWithVendorResponse{}, nil},
				},
				// EnquireRechargeStatusWithVendor fails with permanent error
				{
					enable:   true,
					activity: payNs.EnquireRechargeStatusWithVendor,
					req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewPermanentError(errors.New("vendor enquiry failed permanently"))},
				},
				// UpdateRechargeOrderStage for FULFILLMENT FAILED
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeRefundOrder - returns true since fulfillment failed
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeRefundOrder,
					req: &billpayActPb.ShouldInitiateRechargeRefundOrderRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeRefundOrderResponse{
						ShouldInitiateRefund: true,
					}, nil},
				},
				// Refund stage - successful flow
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_REFUND,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testRefundClientRequestId,
						},
					}, nil},
				},
				{
					enable:   true,
					activity: payNs.InitiateRechargeRefundOrder,
					req: &billpayActPb.InitiateRechargeRefundOrderRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						RefundClientRequestId: testRefundClientRequestId,
					},
					res: []interface{}{&billpayActPb.InitiateRechargeRefundOrderResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.EnquireRechargeRefundOrderStatus,
					req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						RefundClientRequestId: testRefundClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargeRefundOrderStatusResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_REFUND,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_FAILED,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
			},
			mockSignals: []*signalMock{
				{
					delay:      10 * time.Nanosecond,
					signal:     []byte("auth_signal"),
					signalName: payNs.RechargeFundTransferAuthSignal,
				},
			},
			want:              nil,
			wantErr:           false,
			workflowCompletes: true,
		},
		{
			name: "fulfillment_enquiry_transient_failure",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// Payment stage - successful flow
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargePaymentStatusResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeFulfilment - check if fulfillment should be initiated
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeFulfilment,
					req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
						ShouldInitiateFulfilment: true,
					}, nil},
				},
				// Fulfillment stage starts
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testFulfillmentClientRequestId,
						},
					}, nil},
				},
				// InitiateRechargeWithVendor succeeds
				{
					enable:   true,
					activity: payNs.InitiateRechargeWithVendor,
					req: &billpayActPb.InitiateRechargeWithVendorRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
					},
					res: []interface{}{&billpayActPb.InitiateRechargeWithVendorResponse{}, nil},
				},
				// EnquireRechargeStatusWithVendor fails with transient error (max retries exhausted)
				{
					enable:   true,
					activity: payNs.EnquireRechargeStatusWithVendor,
					req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewTransientError(errors.New("vendor enquiry retries exhausted"))},
				},
				// UpdateRechargeOrderStage for FULFILLMENT MANUAL_INTERVENTION
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// UpdateRechargeOrderStatus to MANUAL_INTERVENTION
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
			},
			mockSignals: []*signalMock{
				{
					delay:      10 * time.Nanosecond,
					signal:     []byte("auth_signal"),
					signalName: payNs.RechargeFundTransferAuthSignal,
				},
			},
			want:              nil,
			wantErr:           true,
			workflowCompletes: false,
		},
		{
			name: "refund_initiate_failure",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// Payment stage - successful flow
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargePaymentStatusResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeFulfilment - check if fulfillment should be initiated
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeFulfilment,
					req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
						ShouldInitiateFulfilment: true,
					}, nil},
				},
				// Fulfillment stage - failed
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testFulfillmentClientRequestId,
						},
					}, nil},
				},
				{
					enable:   true,
					activity: payNs.InitiateRechargeWithVendor,
					req: &billpayActPb.InitiateRechargeWithVendorRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewPermanentError(errors.New("vendor initiate failed permanently"))},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeRefundOrder - returns true since fulfillment failed
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeRefundOrder,
					req: &billpayActPb.ShouldInitiateRechargeRefundOrderRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeRefundOrderResponse{
						ShouldInitiateRefund: true,
					}, nil},
				},
				// Refund stage - initiate fails
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_REFUND,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testRefundClientRequestId,
						},
					}, nil},
				},
				// InitiateRechargeRefundOrder fails with transient error (max retries exhausted)
				{
					enable:   true,
					activity: payNs.InitiateRechargeRefundOrder,
					req: &billpayActPb.InitiateRechargeRefundOrderRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						RefundClientRequestId: testRefundClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewTransientError(errors.New("refund initiate retries exhausted"))},
				},
				// UpdateRechargeOrderStage for REFUND MANUAL_INTERVENTION
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_REFUND,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_MANUAL_INTERVENTION,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// UpdateRechargeOrderStatus to MANUAL_INTERVENTION
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
			},
			mockSignals: []*signalMock{
				{
					delay:      10 * time.Nanosecond,
					signal:     []byte("auth_signal"),
					signalName: payNs.RechargeFundTransferAuthSignal,
				},
			},
			want:              nil,
			wantErr:           true,
			workflowCompletes: false,
		},
		{
			name: "refund_enquiry_failure",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// Payment stage - successful flow
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargePaymentStatusResponse{}, nil},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeFulfilment - check if fulfillment should be initiated
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeFulfilment,
					req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
						ShouldInitiateFulfilment: true,
					}, nil},
				},
				// Fulfillment stage - failed
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testFulfillmentClientRequestId,
						},
					}, nil},
				},
				{
					enable:   true,
					activity: payNs.InitiateRechargeWithVendor,
					req: &billpayActPb.InitiateRechargeWithVendorRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewPermanentError(errors.New("vendor initiate failed permanently"))},
				},
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeRefundOrder - returns true since fulfillment failed
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeRefundOrder,
					req: &billpayActPb.ShouldInitiateRechargeRefundOrderRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeRefundOrderResponse{
						ShouldInitiateRefund: true,
					}, nil},
				},
				// Refund stage - enquiry fails
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_REFUND,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testRefundClientRequestId,
						},
					}, nil},
				},
				// InitiateRechargeRefundOrder succeeds
				{
					enable:   true,
					activity: payNs.InitiateRechargeRefundOrder,
					req: &billpayActPb.InitiateRechargeRefundOrderRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						RefundClientRequestId: testRefundClientRequestId,
					},
					res: []interface{}{&billpayActPb.InitiateRechargeRefundOrderResponse{}, nil},
				},
				// EnquireRechargeRefundOrderStatus fails with permanent error
				{
					enable:   true,
					activity: payNs.EnquireRechargeRefundOrderStatus,
					req: &billpayActPb.EnquireRechargeRefundOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						RefundClientRequestId: testRefundClientRequestId,
					},
					res: []interface{}{nil, epifitemporal.NewPermanentError(errors.New("refund enquiry failed permanently"))},
				},
				// UpdateRechargeOrderStage for REFUND FAILED (permanent error in refund enquiry)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_REFUND,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// UpdateRechargeOrderStatus to MANUAL_INTERVENTION (even for permanent refund error)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
			},
			mockSignals: []*signalMock{
				{
					delay:      10 * time.Nanosecond,
					signal:     []byte("auth_signal"),
					signalName: payNs.RechargeFundTransferAuthSignal,
				},
			},
			want:              nil,
			wantErr:           true,
			workflowCompletes: false,
		},
		{
			name: "payment_timeout_but_enquiry_succeeds",
			mockActivities: []activityMock{
				{
					enable:   true,
					activity: epifitemporal.GetWorkflowProcessingParamsV2,
					req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
						RequestHeader: &celestialActivityPb.RequestHeader{
							Ownership: commontypes.Ownership_EPIFI_TECH,
						},
						WfReqId: defaultWorkflowID,
					},
					res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
						ResponseHeader: nil,
						WfReqParams: &workflow.ProcessingParams{
							Payload: payload,
						},
					}, nil},
				},
				// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStatus,
					req: &billpayActPb.UpdateRechargeOrderStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
						SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
				},
				// CreateRechargeOrderStage for PAYMENT
				{
					enable:   true,
					activity: payNs.CreateRechargeOrderStage,
					req: &billpayActPb.CreateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					},
					res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
						RechargeOrderStage: &billpayPb.RechargeOrderStage{
							ClientRequestId: testPaymentClientRequestId,
						},
					}, nil},
				},
				// EnquireRechargePaymentStatus (called without signal being received)
				{
					enable:   true,
					activity: payNs.EnquireRechargePaymentStatus,
					req: &billpayActPb.EnquireRechargePaymentStatusRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						PaymentStageClientRequestId: testPaymentClientRequestId,
					},
					res: []interface{}{&billpayActPb.EnquireRechargePaymentStatusResponse{}, nil},
				},
				// UpdateRechargeOrderStage for PAYMENT SUCCESS (even though signal timed out)
				{
					enable:   true,
					activity: payNs.UpdateRechargeOrderStage,
					req: &billpayActPb.UpdateRechargeOrderStageRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
						Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
					},
					res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
				},
				// ShouldInitiateRechargeFulfilment
				{
					enable:   true,
					activity: payNs.ShouldInitiateRechargeFulfilment,
					req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
						RequestHeader: &celestialActivityPb.RequestHeader{
							ClientReqId: testClientRequestId,
						},
					},
					res: []interface{}{&billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
						ShouldInitiateFulfilment: false, // Skip fulfillment for this test
					}, nil},
				},
			},
			mockSignals: []*signalMock{
				// No signal sent - this will cause timeout
			},
			want:              nil,
			wantErr:           false,
			workflowCompletes: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			env := wts.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(billpayWorkflow.RechargePayment)

			// Register billpay activity processor so activities can be found
			billpayProcessor := &billpayActivity.Processor{}
			env.RegisterActivity(billpayProcessor)
			env.RegisterActivity(&celestialActivityV2.Processor{})

			// Set up signal sending with delay
			if len(tt.mockSignals) > 0 {
				env.RegisterDelayedCallback(func() {
					for _, signal := range tt.mockSignals {
						env.SignalWorkflow(string(signal.signalName), signal.signal)
					}
				}, 10)
			}

			// Mock all activities - this overrides the registered activities with test behavior
			for _, activity := range tt.mockActivities {
				if activity.enable {
					// For activities that might be retried (error cases), use Maybe() to allow multiple calls
					// For activities that succeed, use Times(1) to ensure they're called exactly once
					if len(activity.res) >= 2 && activity.res[1] != nil {
						// This activity returns an error, so it might be retried - use Maybe()
						env.OnActivity(string(activity.activity), mock.Anything, activity.req).Return(activity.res...).Maybe()
					} else {
						// This activity succeeds, should be called exactly once
						env.OnActivity(string(activity.activity), mock.Anything, activity.req).Return(activity.res...).Times(1)
					}
				}
			}

			// Add optional mock for payment timeout scenario (in case signal doesn't arrive)
			env.OnActivity(string(payNs.UpdateRechargeOrderStage), mock.Anything, &billpayActPb.UpdateRechargeOrderStageRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
				StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_EXPIRED,
			}).Return(&billpayActPb.UpdateRechargeOrderStageResponse{}, nil).Maybe()

			// Execute workflow
			env.ExecuteWorkflow(billpayWorkflow.RechargePayment, &billpayWfPb.RechargePaymentRequest{
				ClientRequestId: testClientRequestId,
			})

			// Verify workflow completion
			if tt.workflowCompletes {
				assert.True(t, env.IsWorkflowCompleted(), "Expected workflow to complete")
			}

			// Verify error expectation
			if (env.GetWorkflowError() != nil) != tt.wantErr {
				t.Errorf("RechargePayment() error = %v, wantErr %v", env.GetWorkflowError(), tt.wantErr)
			}

			// Verify all mocked activities were called
			env.AssertExpectations(t)
		})
	}
}

func TestRechargePaymentQueryHandler(t *testing.T) {
	env := wts.NewTestWorkflowEnvironment()
	env.RegisterWorkflow(billpayWorkflow.RechargePayment)

	// Register billpay activity processor so activities can be found
	billpayProcessor := &billpayActivity.Processor{}
	env.RegisterActivity(billpayProcessor)
	env.RegisterActivity(&celestialActivityV2.Processor{})

	// Set up signal for happy flow
	env.RegisterDelayedCallback(func() {
		env.SignalWorkflow(string(payNs.RechargeFundTransferAuthSignal), []byte("auth_signal"))
	}, 10)

	payload, err := protojson.Marshal(&billpayWfPb.RechargePaymentRequest{
		ClientRequestId: testClientRequestId,
	})
	require.NoError(t, err)

	// Mock all activities for successful flow
	mockActivities := []activityMock{
		{
			enable:   true,
			activity: epifitemporal.GetWorkflowProcessingParamsV2,
			req: &celestialActivityPb.GetWorkflowProcessingParamsV2Request{
				RequestHeader: &celestialActivityPb.RequestHeader{
					Ownership: commontypes.Ownership_EPIFI_TECH,
				},
				WfReqId: defaultWorkflowID,
			},
			res: []interface{}{&celestialActivityPb.GetWorkflowProcessingParamsV2Response{
				ResponseHeader: nil,
				WfReqParams: &workflow.ProcessingParams{
					Payload: payload,
				},
			}, nil},
		},
		// UpdateRechargeOrderStatus to IN_PROGRESS (first call)
		{
			enable:   true,
			activity: payNs.UpdateRechargeOrderStatus,
			req: &billpayActPb.UpdateRechargeOrderStatusRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
				SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
			},
			res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
		},
		// CreateRechargeOrderStage for PAYMENT
		{
			enable:   true,
			activity: payNs.CreateRechargeOrderStage,
			req: &billpayActPb.CreateRechargeOrderStageRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				Stage: enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
			},
			res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
				RechargeOrderStage: &billpayPb.RechargeOrderStage{
					ClientRequestId: testPaymentClientRequestId,
				},
			}, nil},
		},
		// UpdateRechargeOrderStage for PAYMENT INITIATED
		{
			enable:   true,
			activity: payNs.UpdateRechargeOrderStage,
			req: &billpayActPb.UpdateRechargeOrderStageRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
				StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
			},
			res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
		},
		// EnquireRechargePaymentStatus
		{
			enable:   true,
			activity: payNs.EnquireRechargePaymentStatus,
			req: &billpayActPb.EnquireRechargePaymentStatusRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				PaymentStageClientRequestId: testPaymentClientRequestId,
			},
			res: []interface{}{&billpayActPb.EnquireRechargePaymentStatusResponse{}, nil},
		},
		// UpdateRechargeOrderStage for PAYMENT SUCCESS
		{
			enable:   true,
			activity: payNs.UpdateRechargeOrderStage,
			req: &billpayActPb.UpdateRechargeOrderStageRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
				StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
			},
			res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
		},
		// ShouldInitiateRechargeFulfilment
		{
			enable:   true,
			activity: payNs.ShouldInitiateRechargeFulfilment,
			req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
			},
			res: []interface{}{&billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
				ShouldInitiateFulfilment: true, // Skip fulfillment for this test
			}, nil},
		},
		// CreateRechargeOrderStage for FULFILLMENT
		{
			enable:   true,
			activity: payNs.CreateRechargeOrderStage,
			req: &billpayActPb.CreateRechargeOrderStageRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				Stage: enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
			},
			res: []interface{}{&billpayActPb.CreateRechargeOrderStageResponse{
				RechargeOrderStage: &billpayPb.RechargeOrderStage{
					ClientRequestId: testFulfillmentClientRequestId,
				},
			}, nil},
		},
		// InitiateRechargeWithVendor
		{
			enable:   true,
			activity: payNs.InitiateRechargeWithVendor,
			req: &billpayActPb.InitiateRechargeWithVendorRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
			},
			res: []interface{}{&billpayActPb.InitiateRechargeWithVendorResponse{}, nil},
		},
		// EnquireRechargeStatusWithVendor
		{
			enable:   true,
			activity: payNs.EnquireRechargeStatusWithVendor,
			req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				FulfilmentStageClientRequestId: testFulfillmentClientRequestId,
			},
			res: []interface{}{&billpayActPb.EnquireRechargeStatusWithVendorResponse{}, nil},
		},
		// UpdateRechargeOrderStage for FULFILLMENT SUCCESS
		{
			enable:   true,
			activity: payNs.UpdateRechargeOrderStage,
			req: &billpayActPb.UpdateRechargeOrderStageRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				Stage:               enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
				StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
			},
			res: []interface{}{&billpayActPb.UpdateRechargeOrderStageResponse{}, nil},
		},
		// ShouldInitiateRechargeRefundOrder
		{
			enable:   true,
			activity: payNs.ShouldInitiateRechargeRefundOrder,
			req: &billpayActPb.ShouldInitiateRechargeRefundOrderRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
			},
			res: []interface{}{&billpayActPb.ShouldInitiateRechargeRefundOrderResponse{
				ShouldInitiateRefund: false,
			}, nil},
		},
		// UpdateRechargeOrderStatus to SUCCESS
		{
			enable:   true,
			activity: payNs.UpdateRechargeOrderStatus,
			req: &billpayActPb.UpdateRechargeOrderStatusRequest{
				RequestHeader: &celestialActivityPb.RequestHeader{
					ClientReqId: testClientRequestId,
				},
				StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
				SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
			},
			res: []interface{}{&billpayActPb.UpdateRechargeOrderStatusResponse{}, nil},
		},
	}

	for _, activity := range mockActivities {
		env.OnActivity(string(activity.activity), mock.Anything, activity.req).Return(activity.res...).Maybe()
	}

	// Execute workflow
	env.ExecuteWorkflow(billpayWorkflow.RechargePayment, &billpayWfPb.RechargePaymentRequest{
		ClientRequestId: testClientRequestId,
	})

	// Wait for workflow to complete
	assert.True(t, env.IsWorkflowCompleted())
	require.NoError(t, env.GetWorkflowError())

	// Now test the query handler
	encoded, err := env.QueryWorkflow(billpayUtils.RechargePaymentStatusQueryType)
	require.NoError(t, err)

	var queryResult billpayUtils.RechargePaymentWorkflowData
	err = encoded.Get(&queryResult)
	require.NoError(t, err)

	// Verify the query result
	assert.Equal(t, testClientRequestId, queryResult.ClientRequestId)
	assert.Equal(t, enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS, queryResult.OrderStatus)
}
