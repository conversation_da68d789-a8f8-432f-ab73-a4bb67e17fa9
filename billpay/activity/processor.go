package activity

import (
	orderPb "github.com/epifi/gamma/api/order"
	payPb "github.com/epifi/gamma/api/pay"
	userPb "github.com/epifi/gamma/api/user"
	rechargeVgPb "github.com/epifi/gamma/api/vendorgateway/recharge"
	"github.com/epifi/gamma/billpay/dao"
)

type Processor struct {
	rechargeOrderDao      dao.RechargeOrderDao
	rechargeOrderStageDao dao.RechargeOrderStageDao
	orderClient           orderPb.OrderServiceClient
	rechargeClient        rechargeVgPb.MobileRechargeServiceClient
	userClient            userPb.UsersClient
	payClient             payPb.PayClient
}

func NewProcessor(
	rechargeOrderDao dao.RechargeOrderDao,
	rechargeOrderStageDao dao.RechargeOrderStageDao,
	orderClient orderPb.OrderServiceClient,
	rechargeClient rechargeVgPb.MobileRechargeServiceClient,
	userClient userPb.UsersClient,
	payClient payPb.PayClient,
) *Processor {
	return &Processor{
		rechargeOrderDao:      rechargeOrderDao,
		rechargeOrderStageDao: rechargeOrderStageDao,
		orderClient:           orderClient,
		rechargeClient:        rechargeClient,
		userClient:            userClient,
		payClient:             payClient,
	}
}
