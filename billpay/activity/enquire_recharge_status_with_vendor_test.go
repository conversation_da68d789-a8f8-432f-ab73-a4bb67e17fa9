package activity_test

import (
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	rechargeVgPb "github.com/epifi/gamma/api/vendorgateway/recharge"
	"github.com/epifi/gamma/billpay/dao"
)

func TestProcessor_EnquireRechargeStatusWithVendor(t *testing.T) {
	p, md, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	type args struct {
		req *billpayActPb.EnquireRechargeStatusWithVendorRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(mockDeps *MockDependencies)
		want       *billpayActPb.EnquireRechargeStatusWithVendorResponse
		wantErr    bool
		retryable  bool
	}{
		{
			name: "happy_flow_success",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
									InitiateRechargeRefId:   "test-ref-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(&rechargeVgPb.EnquireRechargeStatusResponse{
						Status:  rpc.StatusOk(),
						TraceId: "test-enquire-trace-id",
						Success: true,
						Data: &rechargeVgPb.RechargeDetails{
							Status: rechargeVgPb.RechargeStatus_RECHARGE_STATUS_SUCCESSFUL,
						},
					}, nil)

				// Mock stage update
				mockDeps.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Data(), gomock.Any()).
					Return(nil)
			},
			want:      &billpayActPb.EnquireRechargeStatusWithVendorResponse{},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "recharge_status_processing_transient_error",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
									InitiateRechargeRefId:   "test-ref-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(&rechargeVgPb.EnquireRechargeStatusResponse{
						Status:  rpc.StatusOk(),
						TraceId: "test-enquire-trace-id",
						Success: true,
						Data: &rechargeVgPb.RechargeDetails{
							Status: rechargeVgPb.RechargeStatus_RECHARGE_STATUS_PROCESSING,
						},
					}, nil)

				// Mock stage update
				mockDeps.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Data(), gomock.Any()).
					Return(nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "recharge_status_failure_permanent_error",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
									InitiateRechargeRefId:   "test-ref-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(&rechargeVgPb.EnquireRechargeStatusResponse{
						Status:  rpc.StatusOk(),
						TraceId: "test-enquire-trace-id",
						Success: true,
						Data: &rechargeVgPb.RechargeDetails{
							Status: rechargeVgPb.RechargeStatus_RECHARGE_STATUS_FAILURE,
						},
					}, nil)

				// Mock stage update
				mockDeps.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Data(), gomock.Any()).
					Return(nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "recharge_status_reversed_permanent_error",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
									InitiateRechargeRefId:   "test-ref-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(&rechargeVgPb.EnquireRechargeStatusResponse{
						Status:  rpc.StatusOk(),
						TraceId: "test-enquire-trace-id",
						Success: true,
						Data: &rechargeVgPb.RechargeDetails{
							Status: rechargeVgPb.RechargeStatus_RECHARGE_STATUS_REVERSED,
						},
					}, nil)

				// Mock stage update
				mockDeps.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Data(), gomock.Any()).
					Return(nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "missing_fulfilment_stage_client_request_id",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// No mocks needed as validation happens before any DAO calls
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "stage_not_found",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "non-existent-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock stage not found
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "non-existent-client-req-id").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "stage_not_fulfilment_stage",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-payment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting a payment stage instead of fulfillment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-payment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-payment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT, // Wrong stage type
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "recharge_order_not_found",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "non-existent-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
								},
							},
						},
					}, nil)

				// Mock recharge order not found
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "non-existent-order-id").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "missing_initiate_recharge_trace_id",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage without initiate recharge trace id
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "", // Missing trace id
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "vendor_gateway_rpc_error",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call failure
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(nil, errors.New("rpc error"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "vendor_gateway_invalid_argument_status",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call with invalid argument status
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(&rechargeVgPb.EnquireRechargeStatusResponse{
						Status:  rpc.StatusInvalidArgumentWithDebugMsg("Invalid payment ref id"),
						TraceId: "test-enquire-trace-id",
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "vendor_gateway_internal_error_status",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call with internal error status
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(&rechargeVgPb.EnquireRechargeStatusResponse{
						Status:  rpc.StatusInternalWithDebugMsg("Internal server error"),
						TraceId: "test-enquire-trace-id",
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "vendor_gateway_unknown_status",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call with unknown status
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(&rechargeVgPb.EnquireRechargeStatusResponse{
						Status:  rpc.StatusUnknownWithDebugMsg("Unknown error"),
						TraceId: "test-enquire-trace-id",
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "vendor_response_missing_data",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call with missing data
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(&rechargeVgPb.EnquireRechargeStatusResponse{
						Status:  rpc.StatusOk(),
						TraceId: "test-enquire-trace-id",
						Success: true,
						Data:    nil, // Missing data
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "unknown_recharge_status",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call with unknown recharge status
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(&rechargeVgPb.EnquireRechargeStatusResponse{
						Status:  rpc.StatusOk(),
						TraceId: "test-enquire-trace-id",
						Success: true,
						Data: &rechargeVgPb.RechargeDetails{
							Status: rechargeVgPb.RechargeStatus_RECHARGE_STATUS_UNSPECIFIED, // Unknown status
						},
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "stage_update_fails",
			args: args{
				req: &billpayActPb.EnquireRechargeStatusWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_FulfillmentDetails{
								FulfillmentDetails: &billpayPb.RechargeFulfillmentStageData{
									InitiateRechargeTraceId: "test-initiate-trace-id",
								},
							},
						},
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock vendor gateway call
				mockDeps.RechargeClient.EXPECT().
					EnquireRechargeStatus(gomock.Any(), &rechargeVgPb.EnquireRechargeStatusRequest{
						PaymentRefId: "test-fulfilment-client-req-id",
					}).
					Return(&rechargeVgPb.EnquireRechargeStatusResponse{
						Status:  rpc.StatusOk(),
						TraceId: "test-enquire-trace-id",
						Success: true,
						Data: &rechargeVgPb.RechargeDetails{
							Status: rechargeVgPb.RechargeStatus_RECHARGE_STATUS_SUCCESSFUL,
						},
					}, nil)

				// Mock stage update failure
				mockDeps.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Data(), gomock.Any()).
					Return(errors.New("database update failed"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			if tt.setupMocks != nil {
				tt.setupMocks(md)
			}

			// Execute the activity using temporal test environment
			encoded, err := env.ExecuteActivity(payNs.EnquireRechargeStatusWithVendor, tt.args.req)

			// Assert error expectations
			if tt.wantErr {
				if err == nil {
					t.Errorf("EnquireRechargeStatusWithVendor() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				// Check if error is retryable as expected
				if tt.retryable {
					if !epifitemporal.IsRetryableError(err) {
						t.Errorf("EnquireRechargeStatusWithVendor() error should be retryable but got non-retryable error: %v", err)
					}
				} else {
					if epifitemporal.IsRetryableError(err) {
						t.Errorf("EnquireRechargeStatusWithVendor() error should be non-retryable but got retryable error: %v", err)
					}
				}
				return
			}

			if err != nil {
				t.Errorf("EnquireRechargeStatusWithVendor() unexpected error = %v", err)
				return
			}

			// Decode the response
			var got billpayActPb.EnquireRechargeStatusWithVendorResponse
			err = encoded.Get(&got)
			if err != nil {
				t.Errorf("Failed to decode response: %v", err)
				return
			}

			// Assert response
			if diff := cmp.Diff(tt.want, &got, protocmp.Transform()); diff != "" {
				t.Errorf("EnquireRechargeStatusWithVendor() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
