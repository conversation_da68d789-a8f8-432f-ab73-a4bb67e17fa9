package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	orderPb "github.com/epifi/gamma/api/order"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/billpay/dao"
)

// EnquireRechargeRefundOrderStatus activity checks the status of a B2C fund transfer for recharge refund.
// This activity uses the GetFundTransferStatus API to check the current status of the refund transfer
// and determines if the refund has been completed successfully.
//
// Error handling:
// - Returns ErrPermanent for missing required parameters or missing recharge order records
// - Returns ErrTransient for database access errors or pay service failures
// - The workflow updates the refund stage status based on the fund transfer status
func (p *Processor) EnquireRechargeRefundOrderStatus(ctx context.Context, req *billpayActPb.EnquireRechargeRefundOrderStatusRequest) (*billpayActPb.EnquireRechargeRefundOrderStatusResponse, error) {
	lg := activity.GetLogger(ctx)
	refundClientRequestId := req.GetRefundClientRequestId()

	// Validate input parameters
	if refundClientRequestId == "" {
		lg.Error("refund client request id is required")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "refund client request id is required")
	}

	// Get recharge order by client request id from request header
	rechargeOrderClientRequestId := req.GetRequestHeader().GetClientReqId()
	if rechargeOrderClientRequestId == "" {
		lg.Error("recharge order client request id is required in request header")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "recharge order client request id is required in request header")
	}

	// Get recharge order to validate it exists
	rechargeOrder, err := p.rechargeOrderDao.GetByClientRequestId(ctx, dao.RechargeOrderFieldMasks.BasicInfo(), rechargeOrderClientRequestId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("no such recharge order exists",
			zap.String(logger.CLIENT_REQUEST_ID, rechargeOrderClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "recharge order not found")
	case err != nil:
		lg.Error("failed to fetch recharge order by client request id",
			zap.String(logger.CLIENT_REQUEST_ID, rechargeOrderClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to fetch recharge order")
	}

	// Call GetFundTransferStatus to check refund status
	getFundTransferStatusReq := &payPb.GetFundTransferStatusRequest{
		Identifier: &payPb.GetFundTransferStatusRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     refundClientRequestId,
				Client: workflowPb.Client_PAY,
			},
		},
	}

	getFundTransferStatusResp, err := p.payClient.GetFundTransferStatus(ctx, getFundTransferStatusReq)
	switch {
	case err != nil:
		lg.Error("error while calling GetFundTransferStatus for refund status",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("recharge_order_id", rechargeOrder.GetId()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error checking refund transfer status")

	case getFundTransferStatusResp.GetStatus().IsRecordNotFound():
		lg.Error("refund transfer not found in pay service",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("recharge_order_id", rechargeOrder.GetId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "refund transfer not found")

	case !getFundTransferStatusResp.GetStatus().IsSuccess() &&
		getFundTransferStatusResp.GetStatus().GetCode() != uint32(payPb.GetFundTransferStatusResponse_IN_PROGRESS) &&
		getFundTransferStatusResp.GetStatus().GetCode() != uint32(payPb.GetFundTransferStatusResponse_FAILED):
		lg.Error("unexpected response from GetFundTransferStatus",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("recharge_order_id", rechargeOrder.GetId()),
			zap.Any("status", getFundTransferStatusResp.GetStatus()))
		return nil, errors.Wrap(epifierrors.ErrTransient, "unexpected response from pay service")
	}

	// Handle different fund transfer status codes
	switch getFundTransferStatusResp.GetStatus().GetCode() {
	case uint32(payPb.GetFundTransferStatusResponse_OK):
		// Transfer completed successfully - get order details and store in stage data
		lg.Info("refund transfer completed successfully",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("recharge_order_id", rechargeOrder.GetId()))

		// Get order details using refund client request id
		getOrderResp, err := p.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
			Identifier: &orderPb.GetOrderRequest_ClientReqId{
				ClientReqId: refundClientRequestId,
			},
		})

		switch {
		case err != nil:
			lg.Error("error while calling GetOrder() for fetching refund order details",
				zap.String("refund_client_request_id", refundClientRequestId),
				zap.String("recharge_order_id", rechargeOrder.GetId()),
				zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, "error fetching refund order details")
		case !getOrderResp.GetStatus().IsSuccess():
			lg.Error("error response from GetOrder() for refund order",
				zap.String("refund_client_request_id", refundClientRequestId),
				zap.String("recharge_order_id", rechargeOrder.GetId()),
				zap.Any("status", getOrderResp.GetStatus()))
			return nil, errors.Wrap(epifierrors.ErrTransient, "error response from order service for refund order")
		}

		refundOrder := getOrderResp.GetOrder()

		// Update refund stage data with refund order details
		err = p.updateRefundStageData(ctx, rechargeOrder.GetId(), refundClientRequestId, refundOrder.GetId())
		if err != nil {
			lg.Error("failed to update refund stage data",
				zap.String("refund_client_request_id", refundClientRequestId),
				zap.String("recharge_order_id", rechargeOrder.GetId()),
				zap.String("refund_order_id", refundOrder.GetId()),
				zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, "failed to update refund stage data")
		}

	case uint32(payPb.GetFundTransferStatusResponse_IN_PROGRESS):
		// Transfer still in progress - return transient error
		lg.Info("refund transfer still in progress",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("recharge_order_id", rechargeOrder.GetId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, "refund transfer still in progress")

	case uint32(payPb.GetFundTransferStatusResponse_FAILED):
		// Transfer failed - return permanent error
		lg.Error("refund transfer failed",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("recharge_order_id", rechargeOrder.GetId()),
			zap.String("pay_error_code", getFundTransferStatusResp.GetPayErrorCode()),
			zap.Any("sub_status", getFundTransferStatusResp.GetSubStatus()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "refund transfer failed")

	default:
		lg.Error("unknown fund transfer status code",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("recharge_order_id", rechargeOrder.GetId()),
			zap.Uint32("status_code", getFundTransferStatusResp.GetStatus().GetCode()))
		return nil, errors.Wrap(epifierrors.ErrTransient, "unknown fund transfer status")
	}

	return &billpayActPb.EnquireRechargeRefundOrderStatusResponse{}, nil
}
