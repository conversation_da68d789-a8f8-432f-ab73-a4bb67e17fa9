package activity_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"

	"google.golang.org/protobuf/testing/protocmp"

	celestialPb "github.com/epifi/be-common/api/celestial"
	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	typesv2CommonPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/vendorgateway"

	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	orderPb "github.com/epifi/gamma/api/order"

	payPb "github.com/epifi/gamma/api/pay"
	vendorgatewayPb "github.com/epifi/gamma/api/vendorgateway"
	"github.com/epifi/gamma/billpay/dao"
)

func TestProcessor_InitiateRechargeRefundOrder(t *testing.T) {
	p, md, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	type args struct {
		req *billpayActPb.InitiateRechargeRefundOrderRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(mockDeps *MockDependencies)
		want       *billpayActPb.InitiateRechargeRefundOrderResponse
		wantErr    bool
		retryable  bool
	}{
		{
			name: "happy_flow_success",
			args: args{
				req: &billpayActPb.InitiateRechargeRefundOrderRequest{
					RefundClientRequestId: "test-refund-client-req-id",
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "test-recharge-order-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order by client request ID
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-payment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					}, nil)

				// Mock getting payment order details
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-payment-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:          "test-payment-order-id",
							ClientReqId: "test-payment-client-req-id",
							FromActorId: "test-actor-id",
							Amount:      &money.Money{Units: 10000, CurrencyCode: "INR"},
							ExternalId:  "test-payment-client-req-id",
						},
					}, nil)

				// Mock checking if refund order already exists (should not exist)
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-refund-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				// Mock MakeB2CFundTransfer call
				mockDeps.PayClient.EXPECT().
					MakeB2CFundTransfer(gomock.Any(), &payPb.MakeB2CFundTransferRequest{
						PayerActorId:    "dummy-payer-actor-id",
						PayeeActorId:    "test-actor-id",
						Amount:          &money.Money{Units: 10000, CurrencyCode: "INR"},
						ClientRequestId: &celestialPb.ClientReqId{Id: "test-refund-client-req-id"},
						PiFrom:          "dummy-pi-from",
						PiTo:            "dummy-pi-to",
						Remarks:         "Recharge refund for order: test-payment-client-req-id",
						Partner:         vendorgateway.Vendor_FEDERAL_BANK,
						EntityOwnership: typesv2CommonPb.Ownership_EPIFI_TECH,
						RequestSource:   vendorgatewayPb.RequestSource_REQUEST_SOURCE_BILLPAY_RECHARGE,
					}).
					Return(&payPb.MakeB2CFundTransferResponse{
						Status: rpc.StatusOk(),
					}, nil)

				// Mock getting refund stage for update
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_REFUND).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-refund-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_REFUND,
						Data:            &billpayPb.RechargeStageData{},
					}, nil)

				// Mock updating refund stage data
				mockDeps.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Data(), gomock.Any()).
					Return(nil)
			},
			want:      &billpayActPb.InitiateRechargeRefundOrderResponse{},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "missing_refund_client_request_id_permanent_error",
			args: args{
				req: &billpayActPb.InitiateRechargeRefundOrderRequest{
					RefundClientRequestId: "", // Missing refund client request ID
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "test-recharge-order-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// No mocks needed as validation should fail early
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
		{
			name: "missing_recharge_order_client_request_id_permanent_error",
			args: args{
				req: &billpayActPb.InitiateRechargeRefundOrderRequest{
					RefundClientRequestId: "test-refund-client-req-id",
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "", // Missing recharge order client request ID
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// No mocks needed as validation should fail early
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
		{
			name: "recharge_order_not_found_permanent_error",
			args: args{
				req: &billpayActPb.InitiateRechargeRefundOrderRequest{
					RefundClientRequestId: "test-refund-client-req-id",
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "test-recharge-order-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock recharge order not found
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-client-req-id").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
		{
			name: "payment_stage_not_found_permanent_error",
			args: args{
				req: &billpayActPb.InitiateRechargeRefundOrderRequest{
					RefundClientRequestId: "test-refund-client-req-id",
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "test-recharge-order-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock payment stage not found
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
		{
			name: "payment_order_not_found_transient_error",
			args: args{
				req: &billpayActPb.InitiateRechargeRefundOrderRequest{
					RefundClientRequestId: "test-refund-client-req-id",
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "test-recharge-order-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-payment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					}, nil)

				// Mock payment order not found
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-payment-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error
		},
		{
			name: "refund_order_already_exists_idempotent",
			args: args{
				req: &billpayActPb.InitiateRechargeRefundOrderRequest{
					RefundClientRequestId: "test-refund-client-req-id",
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "test-recharge-order-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-payment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					}, nil)

				// Mock getting payment order details
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-payment-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:          "test-payment-order-id",
							ClientReqId: "test-payment-client-req-id",
							FromActorId: "test-actor-id",
							Amount:      &money.Money{Units: 10000, CurrencyCode: "INR"},
							ExternalId:  "test-payment-client-req-id",
						},
					}, nil)

				// Mock refund order already exists
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-refund-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id: "existing-refund-order-id",
						},
					}, nil)
			},
			want:      &billpayActPb.InitiateRechargeRefundOrderResponse{},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "make_b2c_fund_transfer_already_exists_idempotent",
			args: args{
				req: &billpayActPb.InitiateRechargeRefundOrderRequest{
					RefundClientRequestId: "test-refund-client-req-id",
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "test-recharge-order-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-payment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					}, nil)

				// Mock getting payment order details
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-payment-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:          "test-payment-order-id",
							ClientReqId: "test-payment-client-req-id",
							FromActorId: "test-actor-id",
							Amount:      &money.Money{Units: 10000, CurrencyCode: "INR"},
							ExternalId:  "test-payment-client-req-id",
						},
					}, nil)

				// Mock refund order does not exist
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-refund-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				// Mock MakeB2CFundTransfer returns ALREADY_EXISTS
				mockDeps.PayClient.EXPECT().
					MakeB2CFundTransfer(gomock.Any(), gomock.Any()).
					Return(&payPb.MakeB2CFundTransferResponse{
						Status: rpc.StatusAlreadyExists(),
					}, nil)
			},
			want:      &billpayActPb.InitiateRechargeRefundOrderResponse{},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "make_b2c_fund_transfer_internal_error_transient",
			args: args{
				req: &billpayActPb.InitiateRechargeRefundOrderRequest{
					RefundClientRequestId: "test-refund-client-req-id",
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "test-recharge-order-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-payment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					}, nil)

				// Mock getting payment order details
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-payment-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:          "test-payment-order-id",
							ClientReqId: "test-payment-client-req-id",
							FromActorId: "test-actor-id",
							Amount:      &money.Money{Units: 10000, CurrencyCode: "INR"},
							ExternalId:  "test-payment-client-req-id",
						},
					}, nil)

				// Mock refund order does not exist
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-refund-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				// Mock MakeB2CFundTransfer returns INTERNAL error
				mockDeps.PayClient.EXPECT().
					MakeB2CFundTransfer(gomock.Any(), gomock.Any()).
					Return(&payPb.MakeB2CFundTransferResponse{
						Status: rpc.StatusInternal(),
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error
		},
		{
			name: "make_b2c_fund_transfer_unexpected_status_permanent_error",
			args: args{
				req: &billpayActPb.InitiateRechargeRefundOrderRequest{
					RefundClientRequestId: "test-refund-client-req-id",
					RequestHeader: &activityPb.RequestHeader{
						ClientReqId: "test-recharge-order-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-recharge-order-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-payment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					}, nil)

				// Mock getting payment order details
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-payment-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order: &orderPb.Order{
							Id:          "test-payment-order-id",
							ClientReqId: "test-payment-client-req-id",
							FromActorId: "test-actor-id",
							Amount:      &money.Money{Units: 10000, CurrencyCode: "INR"},
							ExternalId:  "test-payment-client-req-id",
						},
					}, nil)

				// Mock refund order does not exist
				mockDeps.OrderClient.EXPECT().
					GetOrder(gomock.Any(), &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ClientReqId{
							ClientReqId: "test-refund-client-req-id",
						},
					}).
					Return(&orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)

				// Mock MakeB2CFundTransfer returns unexpected status
				mockDeps.PayClient.EXPECT().
					MakeB2CFundTransfer(gomock.Any(), gomock.Any()).
					Return(&payPb.MakeB2CFundTransferResponse{
						Status: rpc.StatusInvalidArgument(),
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks
			tt.setupMocks(md)

			// Execute activity
			encoded, err := env.ExecuteActivity(p.InitiateRechargeRefundOrder, tt.args.req)

			// Check error expectation
			if tt.wantErr {
				if err == nil {
					t.Errorf("InitiateRechargeRefundOrder() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				// Check retryability
				if tt.retryable {
					if !epifitemporal.IsRetryableError(err) {
						t.Errorf("InitiateRechargeRefundOrder() expected retryable error, got %v", err)
					}
				} else {
					if epifitemporal.IsRetryableError(err) {
						t.Errorf("InitiateRechargeRefundOrder() expected non-retryable error, got %v", err)
					}
				}
				return
			}

			// Check success case
			if err != nil {
				t.Errorf("InitiateRechargeRefundOrder() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// Decode result
			var got billpayActPb.InitiateRechargeRefundOrderResponse
			if err := encoded.Get(&got); err != nil {
				t.Errorf("Failed to decode result: %v", err)
				return
			}

			// Compare result
			if diff := cmp.Diff(tt.want, &got, protocmp.Transform()); diff != "" {
				t.Errorf("InitiateRechargeRefundOrder() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
