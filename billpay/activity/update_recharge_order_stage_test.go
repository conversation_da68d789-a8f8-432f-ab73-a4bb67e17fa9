package activity_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/dao"
)

func TestProcessor_UpdateRechargeOrderStage(t *testing.T) {
	p, md, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	type args struct {
		ctx context.Context
		req *billpayActPb.UpdateRechargeOrderStageRequest
	}

	var (
		testClientRequestId = "test-client-req-id-123"
		testRechargeOrderId = "test-recharge-order-id-123"

		// Test recharge order
		existingRechargeOrder = &billpayPb.RechargeOrder{
			Id:              testRechargeOrderId,
			ClientRequestId: testClientRequestId,
			ActorId:         "test-actor-123",
			Status:          enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_IN_PROGRESS,
		}

		// Test recharge order stage with same status as update request (for idempotent test)
		existingRechargeOrderStageWithSameStatus = &billpayPb.RechargeOrderStage{
			Id:              "test-stage-id-123",
			RechargeOrderId: testRechargeOrderId,
			ClientRequestId: testClientRequestId,
			Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
			Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
		}
	)

	tests := []struct {
		name      string
		args      args
		want      *billpayActPb.UpdateRechargeOrderStageResponse
		wantErr   bool
		retryable bool
		setup     func()
	}{
		{
			name: "happy_flow_success",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				},
			},
			want: &billpayActPb.UpdateRechargeOrderStageResponse{
				RechargeOrderStage: &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id-123",
					RechargeOrderId: testRechargeOrderId,
					ClientRequestId: testClientRequestId,
					Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				},
			},
			wantErr: false,
			setup: func() {
				// Create fresh stage object for this test to avoid mutation issues
				freshStage := &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id-123",
					RechargeOrderId: testRechargeOrderId,
					ClientRequestId: testClientRequestId,
					Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
				}

				// Mock GetByClientRequestId - return existing recharge order
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(existingRechargeOrder, nil).
					Times(1)

				// Mock GetByRechargeOrderIdAndStage - return existing stage
				md.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.BasicInfo(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(freshStage, nil).
					Times(1)

				// Mock Update - successful update
				updatedStage := &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id-123",
					RechargeOrderId: testRechargeOrderId,
					ClientRequestId: testClientRequestId,
					Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				}
				md.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Status(), updatedStage).
					Return(nil).
					Times(1)
			},
		},
		{
			name: "recharge_order_not_found",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "non-existent-req-id",
					},
					Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
			setup: func() {
				// Mock GetByClientRequestId - return record not found error
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "non-existent-req-id").
					Return(nil, epifierrors.ErrRecordNotFound).
					Times(1)
			},
		},
		{
			name: "recharge_order_stage_not_found",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					Stage:               enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
			setup: func() {
				// Mock GetByClientRequestId - return existing recharge order
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(existingRechargeOrder, nil).
					Times(1)

				// Mock GetByRechargeOrderIdAndStage - return stage not found error
				md.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.BasicInfo(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT).
					Return(nil, epifierrors.ErrRecordNotFound).
					Times(1)
			},
		},
		{
			name: "stage_status_already_same_idempotent",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				},
			},
			want: &billpayActPb.UpdateRechargeOrderStageResponse{
				RechargeOrderStage: &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id-123",
					RechargeOrderId: testRechargeOrderId,
					ClientRequestId: testClientRequestId,
					Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				},
			},
			wantErr: false,
			setup: func() {
				// Mock GetByClientRequestId - return existing recharge order
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(existingRechargeOrder, nil).
					Times(1)

				// Mock GetByRechargeOrderIdAndStage - return stage with same status
				md.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.BasicInfo(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(existingRechargeOrderStageWithSameStatus, nil).
					Times(1)
				// No Update call should be made since status is already the same
			},
		},
		{
			name: "unspecified_stage_passed",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					Stage:               enums.RechargeStage_RECHARGE_STAGE_UNSPECIFIED,
					StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
			setup: func() {
				// No mocks needed as validation happens before DAO calls
			},
		},
		{
			name: "unspecified_stage_status_passed",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_UNSPECIFIED,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
			setup: func() {
				// No mocks needed as validation happens before DAO calls
			},
		},
		{
			name: "get_recharge_order_fails_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
			setup: func() {
				// Mock GetByClientRequestId - return database error
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(nil, errors.New("database connection error")).
					Times(1)
			},
		},
		{
			name: "get_recharge_order_stage_fails_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
			setup: func() {
				// Mock GetByClientRequestId - return existing recharge order
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(existingRechargeOrder, nil).
					Times(1)

				// Mock GetByRechargeOrderIdAndStage - return database error
				md.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.BasicInfo(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(nil, errors.New("database connection error")).
					Times(1)
			},
		},
		{
			name: "update_stage_fails_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					Stage:               enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					StageStatusToUpdate: enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
			setup: func() {
				// Create fresh stage object for this test to avoid mutation issues
				freshStage := &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id-123",
					RechargeOrderId: testRechargeOrderId,
					ClientRequestId: testClientRequestId,
					Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
				}

				// Mock GetByClientRequestId - return existing recharge order
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(existingRechargeOrder, nil).
					Times(1)

				// Mock GetByRechargeOrderIdAndStage - return existing stage (with different status than target)
				md.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.BasicInfo(), testRechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(freshStage, nil).
					Times(1)

				// Mock Update - return database error
				updatedStage := &billpayPb.RechargeOrderStage{
					Id:              "test-stage-id-123",
					RechargeOrderId: testRechargeOrderId,
					ClientRequestId: testClientRequestId,
					Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
				}
				md.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Status(), updatedStage).
					Return(errors.New("database connection error")).
					Times(1)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks for this test case
			tt.setup()

			// Execute the activity using temporal test environment
			encoded, err := env.ExecuteActivity(payNs.UpdateRechargeOrderStage, tt.args.req)

			// Check error expectation
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateRechargeOrderStage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// If we expect an error, validate it's the correct type
			if tt.wantErr {
				if tt.retryable && !epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected retryable error, got non-retryable error: %v", err)
				}

				if !tt.retryable && epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected non-retryable error, got retryable error: %v", err)
				}

				return
			}

			// Decode and compare response
			var got billpayActPb.UpdateRechargeOrderStageResponse
			if err := encoded.Get(&got); err != nil {
				t.Errorf("Failed to decode response: %v", err)
				return
			}

			// Compare responses using protocmp
			if diff := cmp.Diff(tt.want, &got, protocmp.Transform()); diff != "" {
				t.Errorf("UpdateRechargeOrderStage() response mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
