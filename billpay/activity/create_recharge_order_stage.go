package activity

import (
	"context"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/dao"
)

// CreateRechargeOrderStage activity creates a new recharge order stage for the given recharge order and stage
func (p *Processor) CreateRechargeOrderStage(ctx context.Context, req *billpayActPb.CreateRechargeOrderStageRequest) (*billpayActPb.CreateRechargeOrderStageResponse, error) {
	var (
		res = &billpayActPb.CreateRechargeOrderStageResponse{}
		err error
	)
	lg := activity.GetLogger(ctx)

	// Validate stage name
	if req.GetStage() == enums.RechargeStage_RECHARGE_STAGE_UNSPECIFIED {
		lg.Error("stage name is unspecified", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "stage name cannot be unspecified")
	}

	// Get recharge order by ID to validate it exists and get actor ID
	rechargeOrder, err := p.rechargeOrderDao.GetByClientRequestId(ctx, dao.RechargeOrderFieldMasks.BasicInfo(), req.GetRequestHeader().GetClientReqId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("no such recharge order exists", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
	case err != nil:
		lg.Error("failed to fetch recharge order by id", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	// append actor-id to ctx
	ctx = epificontext.CtxWithActorId(ctx, rechargeOrder.GetActorId())

	// Check if stage already exists for this recharge order and stage combination
	existingStage, err := p.rechargeOrderStageDao.GetByRechargeOrderIdAndStage(ctx, dao.RechargeOrderStageFieldMasks.All(), rechargeOrder.GetId(), req.GetStage())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Info("no such recharge order stage exists, will create a new recharge order stage",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String("stage", req.GetStage().String()),
			zap.Error(err))
	case err != nil:
		lg.Error("failed to fetch recharge order stage by recharge order id and stage",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.STAGE, req.GetStage().String()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	default:
		lg.Info("recharge order stage already exists, returning existing stage",
			zap.String("recharge_order_stage_id", existingStage.GetId()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.STAGE, req.GetStage().String()))
		res.RechargeOrderStage = existingStage
		return res, nil
	}

	// Generate client request ID for the stage
	clientRequestId := uuid.NewString()

	// Create new recharge order stage
	newRechargeOrderStage := &billpaypb.RechargeOrderStage{
		RechargeOrderId: rechargeOrder.GetId(),
		ClientRequestId: clientRequestId,
		Stage:           req.GetStage(),
		Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_INITIATED,
	}

	createdStage, err := p.rechargeOrderStageDao.Create(ctx, newRechargeOrderStage)
	if err != nil {
		lg.Error("failed to create recharge order stage",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String("stage", req.GetStage().String()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	res.RechargeOrderStage = createdStage

	return res, nil
}
