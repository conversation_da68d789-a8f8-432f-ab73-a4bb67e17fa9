package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	orderPb "github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/billpay/dao"
)

// EnquireRechargePaymentStatus activity polls for payment status using order service.
// This activity checks if payment is in terminal state by calling the GetOrder API
// with the payment stage client request ID and analyzing the order status.
//
// Error handling:
// - Returns ErrPermanent if order record not found (payment initiation may still be in progress)
// - Returns ErrTransient for order service errors or non-terminal order states
// - The workflow uses this activity's response to determine payment success/failure
func (p *Processor) EnquireRechargePaymentStatus(ctx context.Context, req *billpayActPb.EnquireRechargePaymentStatusRequest) (*billpayActPb.EnquireRechargePaymentStatusResponse, error) {
	lg := activity.GetLogger(ctx)
	paymentStageClientRequestId := req.GetPaymentStageClientRequestId()

	// Validate input parameters
	if paymentStageClientRequestId == "" {
		lg.Error("payment stage client request id is required")
		return nil, errors.Wrap(epifierrors.ErrTransient, "payment stage client request id is required")
	}

	// Get order details using the payment stage client request id
	getOrderResp, err := p.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ClientReqId{
			ClientReqId: paymentStageClientRequestId,
		},
	})

	switch {
	case err != nil:
		lg.Error("error while calling GetOrder() for fetching payment status",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error checking order status")

	case getOrderResp.GetStatus().IsRecordNotFound():
		lg.Info("order record not found, order creation not initiated",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "order record not found, payment initiation may still be in progress")

	case !getOrderResp.GetStatus().IsSuccess():
		lg.Error("error response from GetOrder()",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.Any("status", getOrderResp.GetStatus()))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error response from order service")
	}

	order := getOrderResp.GetOrder()
	orderStatus := order.GetStatus()

	lg.Debug("order status retrieved",
		zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
		zap.String("order_id", order.GetId()),
		zap.String("order_status", orderStatus.String()))

	err = p.updateRechargeOrderStageWithOrderId(ctx, paymentStageClientRequestId, order)
	if err != nil {
		lg.Error("failed to update recharge order stage with order id",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.String("order_id", order.GetId()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to update recharge order stage")
	}

	// Check if order is in terminal state
	switch orderStatus {
	case orderPb.OrderStatus_PAID:
		// Payment successful
		lg.Info("payment successful",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.String("order_id", order.GetId()),
			zap.String("order_status", orderStatus.String()))

		return &billpayActPb.EnquireRechargePaymentStatusResponse{}, nil

	case orderPb.OrderStatus_PAYMENT_FAILED:
		// Payment failed - return permanent error
		lg.Info("payment failed",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.String("order_id", order.GetId()),
			zap.String("order_status", orderStatus.String()))

		return nil, errors.Wrap(epifierrors.ErrPermanent, "payment failed")

	default:
		// Payment still in progress - return transient error to retry
		lg.Info("payment still in progress",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.String("order_id", order.GetId()),
			zap.String("order_status", orderStatus.String()))

		return nil, errors.Wrap(epifierrors.ErrTransient, "payment still in progress")
	}
}

// updateRechargeOrderStageWithOrderId updates the recharge order stage data column with order details
func (p *Processor) updateRechargeOrderStageWithOrderId(ctx context.Context, paymentStageClientRequestId string, order *orderPb.Order) error {
	lg := activity.GetLogger(ctx)

	// Get the recharge order stage by client request id
	stage, err := p.rechargeOrderStageDao.GetByClientRequestId(ctx, dao.RechargeOrderStageFieldMasks.BasicInfo(), paymentStageClientRequestId)
	if err != nil {
		lg.Error("failed to get recharge order stage by client request id",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.Error(err))
		return errors.Wrap(epifierrors.ErrTransient, "failed to get recharge order stage")
	}

	// Check if data is already updated (idempotent behavior)
	if stage.GetData() != nil {
		existingData := stage.GetData()
		if existingData.GetPoolAccountPaymentDetails() != nil &&
			existingData.GetPoolAccountPaymentDetails().GetOrderId() == order.GetId() {
			lg.Info("recharge order stage data already updated with order id",
				zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
				zap.String("order_id", order.GetId()))
			return nil
		}
	}

	// Create pool account payment details with order id
	poolAccountPaymentDetails := &billpayPb.RechargePoolAccountPaymentStageData{
		OrderId:          order.GetId(),
		PaymentTimestamp: order.GetCreatedAt(),
	}

	// Create the stage data with pool account payment details
	stageData := &billpayPb.RechargeStageData{
		Data: &billpayPb.RechargeStageData_PoolAccountPaymentDetails{
			PoolAccountPaymentDetails: poolAccountPaymentDetails,
		},
	}

	// Update the stage data
	stage.Data = stageData

	err = p.rechargeOrderStageDao.Update(ctx, dao.RechargeOrderStageFieldMasks.Data(), stage)
	if err != nil {
		lg.Error("failed to update recharge order stage data",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStageClientRequestId),
			zap.String("order_id", order.GetId()),
			zap.Error(err))
		return errors.Wrap(epifierrors.ErrTransient, "failed to update recharge order stage data")
	}

	return nil
}
