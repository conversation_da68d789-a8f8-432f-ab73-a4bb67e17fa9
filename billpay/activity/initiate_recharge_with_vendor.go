package activity

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	commonv2 "github.com/epifi/be-common/api/typesv2/common"
	vgCommon "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	userPb "github.com/epifi/gamma/api/user"
	rechargeVgPb "github.com/epifi/gamma/api/vendorgateway/recharge"
	"github.com/epifi/gamma/billpay/conversion"
	"github.com/epifi/gamma/billpay/dao"
)

// InitiateRechargeWithVendor activity initiates a recharge request with the vendor gateway.
// This activity takes the fulfilment stage client_request_id as input, validates the stage,
// fetches recharge order details, builds the vendor request, and calls the recharge API.
//
// Error handling:
// - Returns ErrPermanent for invalid input, wrong stage type, or missing records
// - Returns ErrTransient for database access errors or vendor API failures
// - For Permanent failures, the workflow marks the Fulfilment stage as FAILED and proceeds with refund
// - For Transient failures, the workflow marks the Fulfilment stage as MANUAL_INTERVENTION for debugging
func (p *Processor) InitiateRechargeWithVendor(ctx context.Context, req *billpayActPb.InitiateRechargeWithVendorRequest) (*billpayActPb.InitiateRechargeWithVendorResponse, error) {
	lg := activity.GetLogger(ctx)
	fulfilmentStageClientRequestId := req.GetFulfilmentStageClientRequestId()

	// Validate input parameters
	if fulfilmentStageClientRequestId == "" {
		lg.Error("fulfilment stage client request id is required")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "fulfilment stage client request id is required")
	}

	// Get the fulfilment stage by client request id
	stage, err := p.rechargeOrderStageDao.GetByClientRequestId(ctx, dao.RechargeOrderStageFieldMasks.All(), fulfilmentStageClientRequestId)
	if err != nil {
		lg.Error("failed to get recharge order stage by client request id",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to get recharge order stage")
	}

	// Verify this is a fulfilment stage
	if stage.GetStage() != enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT {
		lg.Error("stage is not a fulfilment stage",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("stage", stage.GetStage().String()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "stage is not a fulfilment stage")
	}

	// Get the recharge order to extract necessary details
	rechargeOrder, err := p.rechargeOrderDao.GetById(ctx, dao.RechargeOrderFieldMasks.All(), stage.GetRechargeOrderId())
	if err != nil {
		lg.Error("failed to get recharge order by id", zap.String("recharge_order_id", stage.GetRechargeOrderId()),
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to get recharge order")
	}

	// append actor-id to ctx
	ctx = epificontext.CtxWithActorId(ctx, rechargeOrder.GetActorId())

	// Build the InitiateRecharge request
	initiateRechargeReq, refId, err := p.buildInitiateRechargeRequest(ctx, rechargeOrder, fulfilmentStageClientRequestId)
	if err != nil {
		lg.Error("failed to build initiate recharge request",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to build initiate recharge request")
	}

	// Call the vendor gateway to initiate recharge
	resp, err := p.rechargeClient.InitiateRecharge(ctx, initiateRechargeReq)
	if err != nil {
		lg.Error("failed to call initiate recharge vendor API",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to call initiate recharge vendor API")
	}

	// Handle the response based on status
	err = p.handleInitiateRechargeResponse(ctx, resp, stage, fulfilmentStageClientRequestId, refId)
	if err != nil {
		return nil, err
	}

	return &billpayActPb.InitiateRechargeWithVendorResponse{}, nil
}

// buildInitiateRechargeRequest builds the InitiateRecharge request from recharge order data
func (p *Processor) buildInitiateRechargeRequest(ctx context.Context, rechargeOrder *billpayPb.RechargeOrder, fulfilmentStageClientRequestId string) (*rechargeVgPb.InitiateRechargeRequest, string, error) {
	lg := activity.GetLogger(ctx)

	// Parse the account identifier as mobile number
	mobileNumber, err := commonv2.ParsePhoneNumber(rechargeOrder.GetAccountIdentifier())
	if err != nil {
		return nil, "", errors.Wrap(err, "failed to parse mobile number")
	}

	// Get user details to fetch customer mobile number
	userResp, err := p.userClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{
			ActorId: rechargeOrder.GetActorId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		return nil, "", errors.Wrap(rpcErr, "failed to get user details")
	}

	// Extract customer mobile number from user profile
	customerMobileNumber := userResp.GetUser().GetProfile().GetPhoneNumber()

	// Extract plan details
	planDetails := rechargeOrder.GetPlanDetails()
	if planDetails.GetMobileRechargePlanDetails() == nil {
		lg.Error("plan details are missing or invalid")
		return nil, "", errors.New("plan details are missing or invalid")
	}

	plan := planDetails.GetMobileRechargePlanDetails()

	// Get payment stage to extract payment mode
	paymentStage, err := p.rechargeOrderStageDao.GetByRechargeOrderIdAndStage(ctx, dao.RechargeOrderStageFieldMasks.All(), rechargeOrder.GetId(), enums.RechargeStage_RECHARGE_STAGE_PAYMENT)
	if err != nil {
		lg.Error("failed to get payment stage for recharge order",
			zap.String("recharge_order_id", rechargeOrder.GetId()),
			zap.Error(err))
		return nil, "", errors.Wrap(err, "failed to get payment stage")
	}

	// Extract payment mode from payment stage data
	var paymentMode = paymentStage.GetData().GetPoolAccountPaymentDetails().GetPaymentMode()
	if paymentMode == enums.PaymentMode_PAYMENT_MODE_UNSPECIFIED {
		lg.Warn("payment mode not found in payment stage data, falling back to wallet mode",
			zap.String("recharge_order_id", rechargeOrder.GetId()))
	}

	// Build payment details
	paymentDetails := &rechargeVgPb.PaymentDetails{
		Amount:       plan.GetAmount(),
		Mode:         conversion.ConvertBillPayPaymentModeToVendorGatewayPaymentMode(paymentMode),
		PaymentRefId: fulfilmentStageClientRequestId, // Using fulfilment stage client request id as payment ref id
		Timestamp:    paymentStage.GetData().GetPoolAccountPaymentDetails().GetPaymentTimestamp(),
	}

	// Generate a unique reference ID
	refId := uuid.NewString()

	// Build the request - note: RequestHeader only has Vendor field
	req := &rechargeVgPb.InitiateRechargeRequest{
		Header: &vgCommon.RequestHeader{
			Vendor: vgCommon.Vendor_SETU, // Using SETU as default vendor
		},
		CustomerMobileNumber: customerMobileNumber,
		RechargeMobileNumber: mobileNumber,
		Operator:             conversion.ConvertRechargeOperatorToVendorGatewayOperator(rechargeOrder.GetAccountOperator()),
		Location:             "Maharashtra", // Default location - TODO: get from plan or order
		PaymentDetails:       paymentDetails,
		RefId:                refId,
	}

	return req, refId, nil
}

// handleInitiateRechargeResponse handles the vendor gateway response and updates stage data accordingly
func (p *Processor) handleInitiateRechargeResponse(ctx context.Context, resp *rechargeVgPb.InitiateRechargeResponse, stage *billpayPb.RechargeOrderStage, fulfilmentStageClientRequestId string, refId string) error {
	lg := activity.GetLogger(ctx)

	// Check the status and handle accordingly
	switch {
	case resp.GetStatus().IsSuccess():
		// Success case - update stage data with recharge details
		return p.updateFulfilmentStageData(ctx, stage, resp, fulfilmentStageClientRequestId, refId)

	case resp.GetStatus().IsAlreadyExists():
		// Already exists case - this is also success, just log and continue
		lg.Info("recharge already exists at vendor",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("trace_id", resp.GetTraceId()))
		return p.updateFulfilmentStageData(ctx, stage, resp, fulfilmentStageClientRequestId, refId)

	case resp.GetStatus().IsInvalidArgument():
		// Invalid argument - permanent failure
		lg.Error("invalid argument for initiate recharge",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("status_debug_msg", resp.GetStatus().GetDebugMessage()))
		return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("invalid argument: %s", resp.GetStatus().GetDebugMessage()))

	case resp.GetStatus().IsInternal() || resp.GetStatus().IsUnknown():
		// Internal server error or unknown status - transient failure
		lg.Error("internal server error or unknown status from vendor",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("status_debug_msg", resp.GetStatus().GetDebugMessage()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("vendor error: %s", resp.GetStatus().GetDebugMessage()))

	default:
		// Unknown status code - treat as transient failure
		lg.Error("unknown status code from vendor",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.Uint32("status_code", resp.GetStatus().GetCode()),
			zap.String("status_debug_msg", resp.GetStatus().GetDebugMessage()))
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unknown vendor status: %s", resp.GetStatus().GetDebugMessage()))
	}
}

// updateFulfilmentStageData updates the fulfilment stage data with successful recharge details
func (p *Processor) updateFulfilmentStageData(ctx context.Context, stage *billpayPb.RechargeOrderStage, resp *rechargeVgPb.InitiateRechargeResponse, fulfilmentStageClientRequestId string, refId string) error {
	lg := activity.GetLogger(ctx)

	// Create fulfilment stage data
	fulfillmentDetails := &billpayPb.RechargeFulfillmentStageData{
		RechargeDetails:         conversion.ConvertVendorGatewayRechargeDetailsToRechargeDetails(resp.GetData()),
		InitiateRechargeRefId:   refId,
		InitiateRechargeTraceId: resp.GetTraceId(),
	}

	// Create the stage data with fulfillment details
	stageData := &billpayPb.RechargeStageData{
		Data: &billpayPb.RechargeStageData_FulfillmentDetails{
			FulfillmentDetails: fulfillmentDetails,
		},
	}

	// Update the stage data
	stage.Data = stageData

	err := p.rechargeOrderStageDao.Update(ctx, dao.RechargeOrderStageFieldMasks.Data(), stage)
	if err != nil {
		lg.Error("failed to update recharge order stage data",
			zap.String(logger.CLIENT_REQUEST_ID, fulfilmentStageClientRequestId),
			zap.String("trace_id", resp.GetTraceId()),
			zap.Error(err))
		return errors.Wrap(epifierrors.ErrTransient, "failed to update recharge order stage data")
	}

	return nil
}
