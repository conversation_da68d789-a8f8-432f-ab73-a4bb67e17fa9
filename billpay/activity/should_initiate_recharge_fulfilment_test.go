package activity_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/dao"
)

func TestProcessor_ShouldInitiateRechargeFulfilment(t *testing.T) {
	p, md, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	type args struct {
		ctx context.Context
		req *billpayActPb.ShouldInitiateRechargeFulfilmentRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(mockDeps *MockDependencies)
		want       *billpayActPb.ShouldInitiateRechargeFulfilmentResponse
		wantErr    bool
		retryable  bool
	}{
		{
			name: "happy_flow_payment_stage_successful_should_initiate_fulfilment",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order by client request id
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:              "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						ActorId:         "test-actor-id",
					}, nil)

				// Mock getting payment stage in successful state
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
						ClientRequestId: "test-payment-client-req-id",
					}, nil)
			},
			want: &billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
				ShouldInitiateFulfilment: true,
			},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "happy_flow_payment_stage_failed_should_not_initiate_fulfilment",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order by client request id
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:              "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						ActorId:         "test-actor-id",
					}, nil)

				// Mock getting payment stage in failed state
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_FAILED,
						ClientRequestId: "test-payment-client-req-id",
					}, nil)
			},
			want: &billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
				ShouldInitiateFulfilment: false,
			},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "happy_flow_payment_stage_in_progress_should_not_initiate_fulfilment",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order by client request id
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:              "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						ActorId:         "test-actor-id",
					}, nil)

				// Mock getting payment stage in progress state
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
						ClientRequestId: "test-payment-client-req-id",
					}, nil)
			},
			want: &billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
				ShouldInitiateFulfilment: false,
			},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "happy_flow_payment_stage_expired_should_not_initiate_fulfilment",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order by client request id
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:              "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						ActorId:         "test-actor-id",
					}, nil)

				// Mock getting payment stage in expired state
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_EXPIRED,
						ClientRequestId: "test-payment-client-req-id",
					}, nil)
			},
			want: &billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
				ShouldInitiateFulfilment: false,
			},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "missing_client_request_id_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "", // Empty client request id
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// No mocks needed as validation happens before any DAO calls
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
		{
			name: "recharge_order_not_found_permanent_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "non-existent-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock recharge order not found
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "non-existent-client-req-id").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
		{
			name: "recharge_order_dao_error_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock database error when fetching recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-client-req-id").
					Return(nil, errors.New("database connection failed"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error
		},
		{
			name: "payment_stage_dao_error_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order by client request id
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:              "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						ActorId:         "test-actor-id",
					}, nil)

				// Mock database error when fetching payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(nil, errors.New("database connection failed"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error
		},
		{
			name: "idempotent_behavior_multiple_calls_same_result",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.ShouldInitiateRechargeFulfilmentRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "test-client-req-id",
					},
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting recharge order by client request id
				mockDeps.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "test-client-req-id").
					Return(&billpayPb.RechargeOrder{
						Id:              "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						ActorId:         "test-actor-id",
					}, nil)

				// Mock getting payment stage in successful state
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_SUCCESSFUL,
						ClientRequestId: "test-payment-client-req-id",
					}, nil)
			},
			want: &billpayActPb.ShouldInitiateRechargeFulfilmentResponse{
				ShouldInitiateFulfilment: true,
			},
			wantErr:   false,
			retryable: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks for this test case
			tt.setupMocks(md)

			// Execute the activity
			val, err := env.ExecuteActivity(payNs.ShouldInitiateRechargeFulfilment, tt.args.req)

			// Check error expectations
			if tt.wantErr {
				if err == nil {
					t.Errorf("ShouldInitiateRechargeFulfilment() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				// Check if error is retryable or not
				isRetryable := epifitemporal.IsRetryableError(err)
				if isRetryable != tt.retryable {
					t.Errorf("ShouldInitiateRechargeFulfilment() retryable = %v, want retryable %v, error = %v", isRetryable, tt.retryable, err)
				}
				return
			}

			// No error expected
			if err != nil {
				t.Errorf("ShouldInitiateRechargeFulfilment() unexpected error = %v", err)
				return
			}

			// Check the response
			var got billpayActPb.ShouldInitiateRechargeFulfilmentResponse
			if err := val.Get(&got); err != nil {
				t.Errorf("Failed to get activity result: %v", err)
				return
			}

			// Compare the response using protocmp
			if diff := cmp.Diff(tt.want, &got, protocmp.Transform()); diff != "" {
				t.Errorf("ShouldInitiateRechargeFulfilment() response mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
