package activity_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
)

func TestProcessor_CreateRechargeOrderStage(t *testing.T) {
	p, md, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	var (
		testClientRequestId = "test-recharge-order-client-req-id"
	)

	type args struct {
		ctx context.Context
		req *billpayActPb.CreateRechargeOrderStageRequest
	}
	tests := []struct {
		name      string
		args      args
		mockFunc  func()
		want      *billpayActPb.CreateRechargeOrderStageResponse
		wantErr   bool
		retryable bool
	}{
		{
			name: "happy_flow_success",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.CreateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: testClientRequestId},
					Stage:         enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), gomock.Any(), testClientRequestId).
					Return(&billpaypb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock stage doesn't exist yet
				md.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), gomock.Any(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock successful stage creation
				md.RechargeOrderStageDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, stage *billpaypb.RechargeOrderStage) (*billpaypb.RechargeOrderStage, error) {
						// Simulate database setting the ID
						stage.Id = "test-stage-id"
						return stage, nil
					})
			},
			want: &billpayActPb.CreateRechargeOrderStageResponse{
				RechargeOrderStage: &billpaypb.RechargeOrderStage{
					Id:              "test-stage-id",
					RechargeOrderId: "test-recharge-order-id",
					Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_INITIATED,
					ClientRequestId: "", // Will be generated, so we'll ignore this in comparison
				},
			},
			wantErr: false,
		},
		{
			name: "recharge_order_not_found",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.CreateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: "non-existent-client-request-id"},
					Stage:         enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
				},
			},
			mockFunc: func() {
				// Mock recharge order not found
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), gomock.Any(), "non-existent-client-request-id").
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "stage_already_exists",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.CreateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: testClientRequestId},
					Stage:         enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), gomock.Any(), testClientRequestId).
					Return(&billpaypb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock existing stage found
				existingStage := &billpaypb.RechargeOrderStage{
					Id:              "existing-stage-id",
					RechargeOrderId: "test-recharge-order-id",
					Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					ClientRequestId: "existing-client-req-id",
				}
				md.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), gomock.Any(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(existingStage, nil)
			},
			want: &billpayActPb.CreateRechargeOrderStageResponse{
				RechargeOrderStage: &billpaypb.RechargeOrderStage{
					Id:              "existing-stage-id",
					RechargeOrderId: "test-recharge-order-id",
					Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
					Status:          enums.RechargeStageStatus_RECHARGE_STAGE_STATUS_IN_PROGRESS,
					ClientRequestId: "existing-client-req-id",
				},
			},
			wantErr: false,
		},
		{
			name: "unspecified_stage_name",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.CreateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: testClientRequestId},
					Stage:         enums.RechargeStage_RECHARGE_STAGE_UNSPECIFIED,
				},
			},
			mockFunc: func() {
				// No mocks needed as validation happens before any DAO calls
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
		},
		{
			name: "get_recharge_order_fails_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.CreateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: testClientRequestId},
					Stage:         enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
				},
			},
			mockFunc: func() {
				// Mock database error when fetching recharge order
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), gomock.Any(), testClientRequestId).
					Return(nil, errors.New("database connection failed"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "get_existing_stage_fails_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.CreateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: testClientRequestId},
					Stage:         enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), gomock.Any(), testClientRequestId).
					Return(&billpaypb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock database error when checking for existing stage
				md.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), gomock.Any(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(nil, errors.New("database connection failed"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
		{
			name: "create_stage_fails_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.CreateRechargeOrderStageRequest{
					RequestHeader: &activity.RequestHeader{ClientReqId: testClientRequestId},
					Stage:         enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
				},
			},
			mockFunc: func() {
				// Mock successful recharge order fetch
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), gomock.Any(), testClientRequestId).
					Return(&billpaypb.RechargeOrder{
						Id:      "test-recharge-order-id",
						ActorId: "test-actor-id",
					}, nil)

				// Mock stage doesn't exist yet
				md.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), gomock.Any(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(nil, epifierrors.ErrRecordNotFound)

				// Mock failed stage creation
				md.RechargeOrderStageDao.EXPECT().
					Create(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("database insert failed"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.mockFunc()

			val, err := env.ExecuteActivity(payNs.CreateRechargeOrderStage, tt.args.req)

			// If we expect an error, validate it's the correct type
			if tt.wantErr {
				if tt.retryable && !epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected retryable error for %s, got non-retryable error: %v", tt.name, err)
				}

				if !tt.retryable && epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected non-retryable error, got retryable error: %v", err)
				}

				return
			}

			if err != nil {
				t.Errorf("CreateRechargeOrderStage() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			var got billpayActPb.CreateRechargeOrderStageResponse
			if err := val.Get(&got); err != nil {
				t.Errorf("Failed to get activity result: %v", err)
				return
			}

			// For successful creation, ignore the generated ClientRequestId in comparison
			if tt.name == "happy_flow_success" {
				// Verify that ClientRequestId was generated
				if got.RechargeOrderStage.ClientRequestId == "" {
					t.Error("Expected ClientRequestId to be generated, but it was empty")
				}
				// Clear it for comparison
				got.RechargeOrderStage.ClientRequestId = ""
			}

			if diff := cmp.Diff(tt.want, &got, protocmp.Transform()); diff != "" {
				t.Errorf("CreateRechargeOrderStage() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
