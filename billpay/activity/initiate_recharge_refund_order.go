package activity

import (
	"context"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	celestialPb "github.com/epifi/be-common/api/celestial"
	typesv2CommonPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	orderPb "github.com/epifi/gamma/api/order"
	payPb "github.com/epifi/gamma/api/pay"
	vendorgateway2 "github.com/epifi/gamma/api/vendorgateway"
	"github.com/epifi/gamma/billpay/dao"
)

// InitiateRechargeRefundOrder activity initiates a B2C fund transfer for refunding recharge amount.
// This activity gets the payment stage order details, extracts amount and actor information,
// then creates a refund transfer request through the pay service.
//
// Error handling:
// - Returns ErrPermanent for missing required parameters or missing order/stage records
// - Returns ErrTransient for database access errors or pay service failures
// - Any failure of this activity will result in MANUAL_INTERVENTION of the recharge order
func (p *Processor) InitiateRechargeRefundOrder(ctx context.Context, req *billpayActPb.InitiateRechargeRefundOrderRequest) (*billpayActPb.InitiateRechargeRefundOrderResponse, error) {
	lg := activity.GetLogger(ctx)
	refundClientRequestId := req.GetRefundClientRequestId()

	// Validate input parameters
	if refundClientRequestId == "" {
		lg.Error("refund client request id is required")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "refund client request id is required")
	}

	// Get recharge order by client request id from request header
	rechargeOrderClientRequestId := req.GetRequestHeader().GetClientReqId()
	if rechargeOrderClientRequestId == "" {
		lg.Error("recharge order client request id is required in request header")
		return nil, errors.Wrap(epifierrors.ErrPermanent, "recharge order client request id is required in request header")
	}

	// Get recharge order to validate it exists and get actor ID
	rechargeOrder, err := p.rechargeOrderDao.GetByClientRequestId(ctx, dao.RechargeOrderFieldMasks.BasicInfo(), rechargeOrderClientRequestId)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("no such recharge order exists",
			zap.String(logger.CLIENT_REQUEST_ID, rechargeOrderClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "recharge order not found")
	case err != nil:
		lg.Error("failed to fetch recharge order by client request id",
			zap.String(logger.CLIENT_REQUEST_ID, rechargeOrderClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to fetch recharge order")
	}

	// Get payment stage to get the payment order details
	paymentStage, err := p.rechargeOrderStageDao.GetByRechargeOrderIdAndStage(ctx, dao.RechargeOrderStageFieldMasks.All(), rechargeOrder.GetId(), enums.RechargeStage_RECHARGE_STAGE_PAYMENT)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("payment stage not found for recharge order",
			zap.String(logger.CLIENT_REQUEST_ID, rechargeOrderClientRequestId),
			zap.String("recharge_order_id", rechargeOrder.GetId()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "payment stage not found")
	case err != nil:
		lg.Error("failed to fetch payment stage",
			zap.String(logger.CLIENT_REQUEST_ID, rechargeOrderClientRequestId),
			zap.String("recharge_order_id", rechargeOrder.GetId()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to fetch payment stage")
	}

	// Get order details using payment stage client request id to extract amount and payee details
	getOrderResp, err := p.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ClientReqId{
			ClientReqId: paymentStage.GetClientRequestId(),
		},
	})

	switch {
	case err != nil:
		lg.Error("error while calling GetOrder() for fetching payment order details",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStage.GetClientRequestId()),
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error fetching payment order details")
	case !getOrderResp.GetStatus().IsSuccess():
		lg.Error("error response from GetOrder()",
			zap.String(logger.CLIENT_REQUEST_ID, paymentStage.GetClientRequestId()),
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.Any("status", getOrderResp.GetStatus()))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error response from order service")
	}

	order := getOrderResp.GetOrder()

	// Check for idempotency - if refund already initiated, return success
	existingRefundOrder, err := p.orderClient.GetOrder(ctx, &orderPb.GetOrderRequest{
		Identifier: &orderPb.GetOrderRequest_ClientReqId{
			ClientReqId: refundClientRequestId,
		},
	})

	switch {
	case err != nil:
		lg.Error("error while checking for existing refund order",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error checking existing refund order")

	case existingRefundOrder.GetStatus().IsSuccess():
		lg.Info("refund order already exists, returning success",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("existing_order_id", existingRefundOrder.GetOrder().GetId()))
		return &billpayActPb.InitiateRechargeRefundOrderResponse{}, nil

	case !existingRefundOrder.GetStatus().IsRecordNotFound():
		lg.Error("unexpected error response when checking existing refund order",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.Any("status", existingRefundOrder.GetStatus()))
		return nil, errors.Wrap(epifierrors.ErrTransient, "unexpected error checking existing refund order")
	}

	// Create B2C fund transfer request for refund
	makeB2CFundTransferReq := &payPb.MakeB2CFundTransferRequest{
		PayerActorId: "dummy-payer-actor-id", // Using dummy value as requested
		PayeeActorId: order.GetFromActorId(), // Refund to the original payer
		Amount:       order.GetAmount(),      // Refund the same amount
		ClientRequestId: &celestialPb.ClientReqId{
			Id: refundClientRequestId,
			// Client: workflowPb.Client_PAY, // todo[obed]: check what client needs to be passed here
		},
		PiFrom:          "dummy-pi-from", // Will be set by pay service based on vendor
		PiTo:            "dummy-pi-to",   // Will be set by pay service based on payee actor
		Remarks:         "Recharge refund for order: " + order.GetExternalId(),
		Partner:         vendorgateway.Vendor_FEDERAL_BANK,
		EntityOwnership: typesv2CommonPb.Ownership_EPIFI_TECH,
		RequestSource:   vendorgateway2.RequestSource_REQUEST_SOURCE_BILLPAY_RECHARGE,
	}

	// Call MakeB2CFundTransfer to initiate the refund
	makeB2CFundTransferResp, err := p.payClient.MakeB2CFundTransfer(ctx, makeB2CFundTransferReq)
	switch {
	case err != nil:
		lg.Error("error while calling MakeB2CFundTransfer for refund",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("payee_actor_id", order.GetFromActorId()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "error initiating refund transfer")

	case makeB2CFundTransferResp.GetStatus().IsAlreadyExists():
		lg.Info("refund transfer already exists",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("payee_actor_id", order.GetFromActorId()))
		return &billpayActPb.InitiateRechargeRefundOrderResponse{}, nil

	case makeB2CFundTransferResp.GetStatus().IsInternal():
		lg.Error("internal error from MakeB2CFundTransfer",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("payee_actor_id", order.GetFromActorId()),
			zap.Any("status", makeB2CFundTransferResp.GetStatus()))
		return nil, errors.Wrap(epifierrors.ErrTransient, "internal error from pay service")

	case !makeB2CFundTransferResp.GetStatus().IsSuccess():
		lg.Error("unexpected response from MakeB2CFundTransfer",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("payee_actor_id", order.GetFromActorId()),
			zap.Any("status", makeB2CFundTransferResp.GetStatus()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "unexpected response from pay service")
	}

	// Update refund stage data with refund details
	err = p.updateRefundStageData(ctx, rechargeOrder.GetId(), refundClientRequestId, order.GetId())
	if err != nil {
		lg.Error("failed to update refund stage data",
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.String("recharge_order_id", rechargeOrder.GetId()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, "failed to update refund stage data")
	}

	lg.Info("successfully initiated recharge refund order",
		zap.String("refund_client_request_id", refundClientRequestId),
		zap.String(logger.ORDER_ID, order.GetId()))

	return &billpayActPb.InitiateRechargeRefundOrderResponse{}, nil
}

// updateRefundStageData updates the refund stage with refund details
func (p *Processor) updateRefundStageData(ctx context.Context, rechargeOrderId, refundClientRequestId, refundOrderId string) error {
	lg := activity.GetLogger(ctx)

	// Get or create refund stage
	refundStage, err := p.rechargeOrderStageDao.GetByRechargeOrderIdAndStage(ctx, dao.RechargeOrderStageFieldMasks.All(), rechargeOrderId, enums.RechargeStage_RECHARGE_STAGE_REFUND)
	if err != nil {
		lg.Error("failed to fetch refund stage",
			zap.String("recharge_order_id", rechargeOrderId),
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.Error(err))
		return errors.Wrap(epifierrors.ErrTransient, "failed to fetch refund stage")
	}

	// Check if data is already updated (idempotent behavior)
	if refundStage.GetData().GetRefundDetails().GetOrderId() == refundOrderId {
		lg.Info("refund stage data already updated", zap.String("recharge_order_id", rechargeOrderId),
			zap.String("refund_client_request_id", refundClientRequestId))
		return nil
	}

	// Create refund details
	refundDetails := &billpayPb.RechargeRefundStageData{
		OrderId: refundOrderId,
	}

	// Create the stage data with refund details
	stageData := &billpayPb.RechargeStageData{
		Data: &billpayPb.RechargeStageData_RefundDetails{
			RefundDetails: refundDetails,
		},
	}

	// Update the stage data
	refundStage.Data = stageData

	err = p.rechargeOrderStageDao.Update(ctx, dao.RechargeOrderStageFieldMasks.Data(), refundStage)
	if err != nil {
		lg.Error("failed to update refund stage data",
			zap.String("recharge_order_id", rechargeOrderId),
			zap.String("refund_client_request_id", refundClientRequestId),
			zap.Error(err))
		return errors.Wrap(epifierrors.ErrTransient, "failed to update refund stage data")
	}

	return nil
}
