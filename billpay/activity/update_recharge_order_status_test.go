package activity_test

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/billpay/dao"
)

func TestProcessor_UpdateRechargeOrderStatus(t *testing.T) {
	p, md, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	type args struct {
		ctx context.Context
		req *billpayActPb.UpdateRechargeOrderStatusRequest
	}

	var (
		testClientRequestId = "test-client-req-id-123"
		testRechargeOrderId = "test-recharge-order-id-123"

		// Test recharge order with initial status
		existingRechargeOrder = &billpayPb.RechargeOrder{
			Id:              testRechargeOrderId,
			ClientRequestId: testClientRequestId,
			ActorId:         "test-actor-123",
			Status:          enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED,
			SubStatus:       enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
		}

		// Test recharge order with same status as update request (for duplicate update test)
		existingRechargeOrderWithSameStatus = &billpayPb.RechargeOrder{
			Id:              testRechargeOrderId,
			ClientRequestId: testClientRequestId,
			ActorId:         "test-actor-123",
			Status:          enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
			SubStatus:       enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
		}
	)

	tests := []struct {
		name      string
		args      args
		want      *billpayActPb.UpdateRechargeOrderStatusResponse
		wantErr   bool
		retryable bool
		setup     func()
	}{
		{
			name: "happy_flow_success",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStatusRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
				},
			},
			want:    &billpayActPb.UpdateRechargeOrderStatusResponse{},
			wantErr: false,
			setup: func() {
				// Mock GetByClientRequestId - return existing order
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(existingRechargeOrder, nil).
					Times(1)

				// Mock Update - successful update
				updatedOrder := &billpayPb.RechargeOrder{
					Id:              testRechargeOrderId,
					ClientRequestId: testClientRequestId,
					ActorId:         "test-actor-123",
					Status:          enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					SubStatus:       enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
				}
				md.RechargeOrderDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderFieldMasks.StatusSubStatus(), updatedOrder).
					Return(nil).
					Times(1)
			},
		},
		{
			name: "client_request_id_not_found",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStatusRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: "non-existent-req-id",
					},
					StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: false,
			setup: func() {
				// Mock GetByClientRequestId - return record not found error
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), "non-existent-req-id").
					Return(nil, epifierrors.ErrRecordNotFound).
					Times(1)
			},
		},
		{
			name: "duplicate_update_same_status_and_substatus",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStatusRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
				},
			},
			want:    &billpayActPb.UpdateRechargeOrderStatusResponse{},
			wantErr: false,
			setup: func() {
				// Mock GetByClientRequestId - return order with same status
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(existingRechargeOrderWithSameStatus, nil).
					Times(1)
				// No Update call should be made since status is already the same
			},
		},
		{
			name: "unspecified_status_passed",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStatusRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_UNSPECIFIED,
					SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
			setup: func() {
				// Mock GetByClientRequestId - return existing order
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(existingRechargeOrder, nil).
					Times(1)
				// No Update call should be made since status validation fails
			},
		},
		{
			name: "unspecified_substatus_passed",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStatusRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
				},
			},
			want:    &billpayActPb.UpdateRechargeOrderStatusResponse{},
			wantErr: false,
			setup: func() {
				// Mock GetByClientRequestId - return existing order
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(existingRechargeOrder, nil).
					Times(1)

				// Mock Update - successful update (substatus can be unspecified)
				updatedOrder := &billpayPb.RechargeOrder{
					Id:              testRechargeOrderId,
					ClientRequestId: testClientRequestId,
					ActorId:         "test-actor-123",
					Status:          enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					SubStatus:       enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
				}
				md.RechargeOrderDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderFieldMasks.StatusSubStatus(), updatedOrder).
					Return(nil).
					Times(1)
			},
		},
		{
			name: "update_fails_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStatusRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
			setup: func() {
				// Mock GetByClientRequestId - return existing order
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(existingRechargeOrder, nil).
					Times(1)

				// Mock Update - return database error
				updatedOrder := &billpayPb.RechargeOrder{
					Id:              testRechargeOrderId,
					ClientRequestId: testClientRequestId,
					ActorId:         "test-actor-123",
					Status:          enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					SubStatus:       enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
				}
				md.RechargeOrderDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderFieldMasks.StatusSubStatus(), updatedOrder).
					Return(errors.New("database connection failed")).
					Times(1)
			},
		},
		{
			name: "get_recharge_order_fails_transient_error",
			args: args{
				ctx: context.Background(),
				req: &billpayActPb.UpdateRechargeOrderStatusRequest{
					RequestHeader: &activity.RequestHeader{
						ClientReqId: testClientRequestId,
					},
					StatusToUpdate:    enums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_SUCCESS,
					SubStatusToUpdate: enums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_REFUNDED,
				},
			},
			want:      nil,
			wantErr:   true,
			retryable: true,
			setup: func() {
				// Mock GetByClientRequestId - return database error
				md.RechargeOrderDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderFieldMasks.BasicInfo(), testClientRequestId).
					Return(nil, errors.New("database connection timeout")).
					Times(1)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mocks for this test case
			tt.setup()

			// Execute the activity using temporal test environment
			encoded, err := env.ExecuteActivity(payNs.UpdateRechargeOrderStatus, tt.args.req)

			// Check error expectation
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateRechargeOrderStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// If we expect an error, validate it's the correct type
			if tt.wantErr {
				if tt.retryable && !epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected retryable error for %s, got non-retryable error: %v", tt.name, err)
				}

				if !tt.retryable && epifitemporal.IsRetryableError(err) {
					t.Errorf("Expected non-retryable error, got retryable error: %v", err)
				}

				return
			}

			// Decode and compare response
			var got billpayActPb.UpdateRechargeOrderStatusResponse
			if err := encoded.Get(&got); err != nil {
				t.Errorf("Failed to decode response: %v", err)
				return
			}

			// Compare responses using protocmp
			if diff := cmp.Diff(tt.want, &got, protocmp.Transform()); diff != "" {
				t.Errorf("UpdateRechargeOrderStatus() response mismatch (-want +got):\n%s", diff)
			}
		})
	}
}
