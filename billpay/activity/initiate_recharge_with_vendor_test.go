package activity_test

import (
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commonv2 "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifitemporal"
	payNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/pay"
	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayActPb "github.com/epifi/gamma/api/billpay/activity"
	"github.com/epifi/gamma/api/billpay/enums"
	userPb "github.com/epifi/gamma/api/user"
	recharge "github.com/epifi/gamma/api/vendorgateway/recharge"
	"github.com/epifi/gamma/billpay/conversion"
	"github.com/epifi/gamma/billpay/dao"
)

func TestProcessor_InitiateRechargeWithVendor(t *testing.T) {
	p, md, assertTest := getBillpayActivityProcessorWithMocks(t)
	defer assertTest()

	env := wts.NewTestActivityEnvironment()
	env.RegisterActivity(p)

	type args struct {
		req *billpayActPb.InitiateRechargeWithVendorRequest
	}

	tests := []struct {
		name       string
		args       args
		setupMocks func(mockDeps *MockDependencies)
		want       *billpayActPb.InitiateRechargeWithVendorResponse
		wantErr    bool
		retryable  bool
	}{
		{
			name: "happy_flow_success",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "test-fulfilment-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the fulfilment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-fulfilment-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-fulfilment-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.All(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:                "test-recharge-order-id",
						ActorId:           "test-actor-id",
						AccountIdentifier: "+91**********",
						AccountOperator:   conversion.ConvertVendorGatewayOperatorToRechargeOperator(recharge.Operator_OPERATOR_AIRTEL),
						PlanDetails: &billpayPb.PlanDetails{
							MobileRechargePlanDetails: conversion.ConvertVendorGatewayPlanToRechargePlan(&recharge.Plan{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
								Validity:        "30 days",
								PlanDescription: "Test plan",
								ServiceProvider: "AIRTEL",
								Operator:        recharge.Operator_OPERATOR_AIRTEL,
							}),
						},
					}, nil)

				// Mock getting user details
				mockDeps.UserClient.EXPECT().
					GetUser(gomock.Any(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "test-actor-id",
						},
					}).
					Return(&userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								PhoneNumber: &commonv2.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
							},
						},
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_PoolAccountPaymentDetails{
								PoolAccountPaymentDetails: &billpayPb.RechargePoolAccountPaymentStageData{
									PaymentMode:      conversion.ConvertVendorGatewayPaymentModeToBillPayPaymentMode(recharge.PaymentMode_PAYMENT_MODE_WALLET),
									PaymentTimestamp: timestamppb.Now(),
								},
							},
						},
					}, nil)

				// Mock vendor gateway call
				mockDeps.RechargeClient.EXPECT().
					InitiateRecharge(gomock.Any(), gomock.Any()).
					Return(&recharge.InitiateRechargeResponse{
						Status:  rpc.StatusOk(),
						TraceId: "test-trace-id",
						Data: &recharge.RechargeDetails{
							Status: recharge.RechargeStatus_RECHARGE_STATUS_PROCESSING,
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
							MobileNumber: &commonv2.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Provider: recharge.Operator_OPERATOR_AIRTEL,
						},
					}, nil)

				// Mock stage update
				mockDeps.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Data(), gomock.Any()).
					Return(nil)
			},
			want:      &billpayActPb.InitiateRechargeWithVendorResponse{},
			wantErr:   false,
			retryable: false,
		},
		{
			name: "missing_client_request_id_permanent_error",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// No mocks needed as validation happens early
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
		{
			name: "stage_not_found_transient_error",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "non-existent-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock stage not found
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "non-existent-client-req-id").
					Return(nil, errors.New("stage not found"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error (not last attempt)
		},
		{
			name: "wrong_stage_type_permanent_error",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "test-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting stage with wrong type
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT, // Wrong stage type
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
		{
			name: "recharge_order_not_found_transient_error",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "test-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the fulfilment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					}, nil)

				// Mock recharge order not found
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.All(), "test-recharge-order-id").
					Return(nil, errors.New("recharge order not found"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error (not last attempt)
		},
		{
			name: "user_not_found_transient_error",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "test-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the fulfilment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.All(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:                "test-recharge-order-id",
						ActorId:           "test-actor-id",
						AccountIdentifier: "+91**********",
						AccountOperator:   conversion.ConvertVendorGatewayOperatorToRechargeOperator(recharge.Operator_OPERATOR_AIRTEL),
						PlanDetails: &billpayPb.PlanDetails{
							MobileRechargePlanDetails: conversion.ConvertVendorGatewayPlanToRechargePlan(&recharge.Plan{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
								Validity:        "30 days",
								PlanDescription: "Test plan",
								ServiceProvider: "AIRTEL",
								Operator:        recharge.Operator_OPERATOR_AIRTEL,
							}),
						},
					}, nil)

				// Mock user not found
				mockDeps.UserClient.EXPECT().
					GetUser(gomock.Any(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "test-actor-id",
						},
					}).
					Return(nil, errors.New("user not found"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error (not last attempt)
		},
		{
			name: "payment_stage_not_found_transient_error",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "test-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the fulfilment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.All(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:                "test-recharge-order-id",
						ActorId:           "test-actor-id",
						AccountIdentifier: "+91**********",
						AccountOperator:   conversion.ConvertVendorGatewayOperatorToRechargeOperator(recharge.Operator_OPERATOR_AIRTEL),
						PlanDetails: &billpayPb.PlanDetails{
							MobileRechargePlanDetails: conversion.ConvertVendorGatewayPlanToRechargePlan(&recharge.Plan{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
								Validity:        "30 days",
								PlanDescription: "Test plan",
								ServiceProvider: "AIRTEL",
								Operator:        recharge.Operator_OPERATOR_AIRTEL,
							}),
						},
					}, nil)

				// Mock getting user details
				mockDeps.UserClient.EXPECT().
					GetUser(gomock.Any(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "test-actor-id",
						},
					}).
					Return(&userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								PhoneNumber: &commonv2.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
							},
						},
					}, nil)

				// Mock payment stage not found
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(nil, errors.New("payment stage not found"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error (not last attempt)
		},
		{
			name: "vendor_gateway_rpc_error",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "test-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the fulfilment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.All(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:                "test-recharge-order-id",
						ActorId:           "test-actor-id",
						AccountIdentifier: "+91**********",
						AccountOperator:   conversion.ConvertVendorGatewayOperatorToRechargeOperator(recharge.Operator_OPERATOR_AIRTEL),
						PlanDetails: &billpayPb.PlanDetails{
							MobileRechargePlanDetails: conversion.ConvertVendorGatewayPlanToRechargePlan(&recharge.Plan{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
								Validity:        "30 days",
								PlanDescription: "Test plan",
								ServiceProvider: "AIRTEL",
								Operator:        recharge.Operator_OPERATOR_AIRTEL,
							}),
						},
					}, nil)

				// Mock getting user details
				mockDeps.UserClient.EXPECT().
					GetUser(gomock.Any(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "test-actor-id",
						},
					}).
					Return(&userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								PhoneNumber: &commonv2.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
							},
						},
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_PoolAccountPaymentDetails{
								PoolAccountPaymentDetails: &billpayPb.RechargePoolAccountPaymentStageData{
									PaymentMode:      conversion.ConvertVendorGatewayPaymentModeToBillPayPaymentMode(recharge.PaymentMode_PAYMENT_MODE_WALLET),
									PaymentTimestamp: timestamppb.Now(),
								},
							},
						},
					}, nil)

				// Mock vendor gateway RPC error
				mockDeps.RechargeClient.EXPECT().
					InitiateRecharge(gomock.Any(), gomock.Any()).
					Return(nil, errors.New("vendor gateway connection error"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error (not last attempt)
		},
		{
			name: "vendor_response_invalid_argument",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "test-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the fulfilment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.All(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:                "test-recharge-order-id",
						ActorId:           "test-actor-id",
						AccountIdentifier: "+91**********",
						AccountOperator:   conversion.ConvertVendorGatewayOperatorToRechargeOperator(recharge.Operator_OPERATOR_AIRTEL),
						PlanDetails: &billpayPb.PlanDetails{
							MobileRechargePlanDetails: conversion.ConvertVendorGatewayPlanToRechargePlan(&recharge.Plan{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
								Validity:        "30 days",
								PlanDescription: "Test plan",
								ServiceProvider: "AIRTEL",
								Operator:        recharge.Operator_OPERATOR_AIRTEL,
							}),
						},
					}, nil)

				// Mock getting user details
				mockDeps.UserClient.EXPECT().
					GetUser(gomock.Any(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "test-actor-id",
						},
					}).
					Return(&userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								PhoneNumber: &commonv2.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
							},
						},
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_PoolAccountPaymentDetails{
								PoolAccountPaymentDetails: &billpayPb.RechargePoolAccountPaymentStageData{
									PaymentMode:      conversion.ConvertVendorGatewayPaymentModeToBillPayPaymentMode(recharge.PaymentMode_PAYMENT_MODE_WALLET),
									PaymentTimestamp: timestamppb.Now(),
								},
							},
						},
					}, nil)

				// Mock vendor gateway invalid argument response
				mockDeps.RechargeClient.EXPECT().
					InitiateRecharge(gomock.Any(), gomock.Any()).
					Return(&recharge.InitiateRechargeResponse{
						Status: rpc.StatusInvalidArgument(),
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: false, // Permanent error
		},
		{
			name: "vendor_response_internal_error",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "test-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the fulfilment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.All(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:                "test-recharge-order-id",
						ActorId:           "test-actor-id",
						AccountIdentifier: "+91**********",
						AccountOperator:   conversion.ConvertVendorGatewayOperatorToRechargeOperator(recharge.Operator_OPERATOR_AIRTEL),
						PlanDetails: &billpayPb.PlanDetails{
							MobileRechargePlanDetails: conversion.ConvertVendorGatewayPlanToRechargePlan(&recharge.Plan{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
								Validity:        "30 days",
								PlanDescription: "Test plan",
								ServiceProvider: "AIRTEL",
								Operator:        recharge.Operator_OPERATOR_AIRTEL,
							}),
						},
					}, nil)

				// Mock getting user details
				mockDeps.UserClient.EXPECT().
					GetUser(gomock.Any(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "test-actor-id",
						},
					}).
					Return(&userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								PhoneNumber: &commonv2.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
							},
						},
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_PoolAccountPaymentDetails{
								PoolAccountPaymentDetails: &billpayPb.RechargePoolAccountPaymentStageData{
									PaymentMode:      conversion.ConvertVendorGatewayPaymentModeToBillPayPaymentMode(recharge.PaymentMode_PAYMENT_MODE_WALLET),
									PaymentTimestamp: timestamppb.Now(),
								},
							},
						},
					}, nil)

				// Mock vendor gateway internal error response
				mockDeps.RechargeClient.EXPECT().
					InitiateRecharge(gomock.Any(), gomock.Any()).
					Return(&recharge.InitiateRechargeResponse{
						Status: rpc.StatusInternal(),
					}, nil)
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error (not last attempt)
		},
		{
			name: "stage_update_failure_transient_error",
			args: args{
				req: &billpayActPb.InitiateRechargeWithVendorRequest{
					FulfilmentStageClientRequestId: "test-client-req-id",
				},
			},
			setupMocks: func(mockDeps *MockDependencies) {
				// Mock getting the fulfilment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByClientRequestId(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-client-req-id").
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						ClientRequestId: "test-client-req-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_FULFILLMENT,
					}, nil)

				// Mock getting the recharge order
				mockDeps.RechargeOrderDao.EXPECT().
					GetById(gomock.Any(), dao.RechargeOrderFieldMasks.All(), "test-recharge-order-id").
					Return(&billpayPb.RechargeOrder{
						Id:                "test-recharge-order-id",
						ActorId:           "test-actor-id",
						AccountIdentifier: "+91**********",
						AccountOperator:   conversion.ConvertVendorGatewayOperatorToRechargeOperator(recharge.Operator_OPERATOR_AIRTEL),
						PlanDetails: &billpayPb.PlanDetails{
							MobileRechargePlanDetails: conversion.ConvertVendorGatewayPlanToRechargePlan(&recharge.Plan{
								Amount: &money.Money{
									CurrencyCode: "INR",
									Units:        100,
								},
								Validity:        "30 days",
								PlanDescription: "Test plan",
								ServiceProvider: "AIRTEL",
								Operator:        recharge.Operator_OPERATOR_AIRTEL,
							}),
						},
					}, nil)

				// Mock getting user details
				mockDeps.UserClient.EXPECT().
					GetUser(gomock.Any(), &userPb.GetUserRequest{
						Identifier: &userPb.GetUserRequest_ActorId{
							ActorId: "test-actor-id",
						},
					}).
					Return(&userPb.GetUserResponse{
						Status: rpc.StatusOk(),
						User: &userPb.User{
							Profile: &userPb.Profile{
								PhoneNumber: &commonv2.PhoneNumber{
									CountryCode:    91,
									NationalNumber: **********,
								},
							},
						},
					}, nil)

				// Mock getting payment stage
				mockDeps.RechargeOrderStageDao.EXPECT().
					GetByRechargeOrderIdAndStage(gomock.Any(), dao.RechargeOrderStageFieldMasks.All(), "test-recharge-order-id", enums.RechargeStage_RECHARGE_STAGE_PAYMENT).
					Return(&billpayPb.RechargeOrderStage{
						Id:              "test-payment-stage-id",
						RechargeOrderId: "test-recharge-order-id",
						Stage:           enums.RechargeStage_RECHARGE_STAGE_PAYMENT,
						Data: &billpayPb.RechargeStageData{
							Data: &billpayPb.RechargeStageData_PoolAccountPaymentDetails{
								PoolAccountPaymentDetails: &billpayPb.RechargePoolAccountPaymentStageData{
									PaymentMode:      conversion.ConvertVendorGatewayPaymentModeToBillPayPaymentMode(recharge.PaymentMode_PAYMENT_MODE_WALLET),
									PaymentTimestamp: timestamppb.Now(),
								},
							},
						},
					}, nil)

				// Mock vendor gateway call
				mockDeps.RechargeClient.EXPECT().
					InitiateRecharge(gomock.Any(), gomock.Any()).
					Return(&recharge.InitiateRechargeResponse{
						Status:  rpc.StatusOk(),
						TraceId: "test-trace-id",
						Data: &recharge.RechargeDetails{
							Status: recharge.RechargeStatus_RECHARGE_STATUS_PROCESSING,
							Amount: &money.Money{
								CurrencyCode: "INR",
								Units:        100,
							},
							MobileNumber: &commonv2.PhoneNumber{
								CountryCode:    91,
								NationalNumber: **********,
							},
							Provider: recharge.Operator_OPERATOR_AIRTEL,
						},
					}, nil)

				// Mock stage update failure
				mockDeps.RechargeOrderStageDao.EXPECT().
					Update(gomock.Any(), dao.RechargeOrderStageFieldMasks.Data(), gomock.Any()).
					Return(errors.New("database update failed"))
			},
			want:      nil,
			wantErr:   true,
			retryable: true, // Transient error (not last attempt)
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks(md)

			val, err := env.ExecuteActivity(payNs.InitiateRechargeWithVendor, tt.args.req)

			if tt.wantErr {
				if err == nil {
					t.Errorf("InitiateRechargeWithVendor() error = nil, wantErr %v", tt.wantErr)
					return
				}

				// Check if error is retryable as expected
				if tt.retryable {
					if !epifitemporal.IsRetryableError(err) {
						t.Errorf("InitiateRechargeWithVendor() error should be retryable, but got non-retryable error: %v", err)
					}
				} else {
					if epifitemporal.IsRetryableError(err) {
						t.Errorf("InitiateRechargeWithVendor() error should be non-retryable, but got retryable error: %v", err)
					}
				}
			} else {
				if err != nil {
					t.Errorf("InitiateRechargeWithVendor() error = %v, wantErr %v", err, tt.wantErr)
					return
				}

				var got billpayActPb.InitiateRechargeWithVendorResponse
				if err := val.Get(&got); err != nil {
					t.Errorf("Failed to get activity result: %v", err)
					return
				}

				if diff := cmp.Diff(tt.want, &got, protocmp.Transform()); diff != "" {
					t.Errorf("InitiateRechargeWithVendor() response mismatch (-want +got):\n%s", diff)
				}
			}
		})
	}
}
