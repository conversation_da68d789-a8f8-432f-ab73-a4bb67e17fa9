package utils

import (
	"fmt"

	billpayEnums "github.com/epifi/gamma/api/billpay/enums"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	billpayDeeplink "github.com/epifi/gamma/billpay/deeplink"
)

const (
	RechargePaymentStatusQueryType = "getRechargeWorkflowStatus"
)

// RechargePaymentWorkflowData represents the complete state of the recharge payment workflow
// This data is exposed via query handlers for external systems to track progress
type RechargePaymentWorkflowData struct {
	// Core identifiers
	ClientRequestId string `json:"client_request_id"`

	// Order-level status
	OrderStatus    billpayEnums.RechargeOrderStatus    `json:"order_status"`
	OrderSubStatus billpayEnums.RechargeOrderSubStatus `json:"order_sub_status"`

	// Stage-level status
	CurrStage       billpayEnums.RechargeStage       `json:"current_stage"`
	CurrStageStatus billpayEnums.RechargeStageStatus `json:"current_stage_status"`

	// UI navigation
	NextAction *deepLinkPb.Deeplink `json:"next_action"`
}

// NewRechargePaymentWorkflowData creates a new workflow data instance with the given client request ID
func NewRechargePaymentWorkflowData(clientRequestId string) *RechargePaymentWorkflowData {
	return &RechargePaymentWorkflowData{
		ClientRequestId: clientRequestId,
		OrderStatus:     billpayEnums.RechargeOrderStatus_RECHARGE_ORDER_STATUS_INITIATED,
		OrderSubStatus:  billpayEnums.RechargeOrderSubStatus_RECHARGE_ORDER_SUB_STATUS_UNSPECIFIED,
		CurrStage:       billpayEnums.RechargeStage_RECHARGE_STAGE_UNSPECIFIED,
		CurrStageStatus: billpayEnums.RechargeStageStatus_RECHARGE_STAGE_STATUS_UNSPECIFIED,
	}
}

// UpdateOrderStatus updates the order status and computes next action
func (w *RechargePaymentWorkflowData) UpdateOrderStatus(
	status billpayEnums.RechargeOrderStatus,
	subStatus billpayEnums.RechargeOrderSubStatus,
	nextActionProcessor *billpayDeeplink.NextActionProcessorImpl,
) error {
	w.OrderStatus = status
	w.OrderSubStatus = subStatus

	return w.ComputeNextAction(nextActionProcessor)
}

// UpdateStageStatus updates the stage status and computes next action
func (w *RechargePaymentWorkflowData) UpdateStageStatus(
	stage billpayEnums.RechargeStage,
	stageStatus billpayEnums.RechargeStageStatus,
	nextActionProcessor *billpayDeeplink.NextActionProcessorImpl,
) error {
	w.CurrStage = stage
	w.CurrStageStatus = stageStatus

	return w.ComputeNextAction(nextActionProcessor)
}

// ComputeNextAction computes and updates the next action
func (w *RechargePaymentWorkflowData) ComputeNextAction(nextActionProcessor *billpayDeeplink.NextActionProcessorImpl) error {
	if nextActionProcessor == nil {
		return fmt.Errorf("nextActionProcessor cannot be nil")
	}

	nextAction, err := nextActionProcessor.GetRechargeNextAction(&billpayDeeplink.RechargeNextActionParams{
		ClientRequestId: w.ClientRequestId,
		OrderStatus:     w.OrderStatus,
		OrderSubStatus:  w.OrderSubStatus,
		CurrStage:       w.CurrStage,
		CurrStageStatus: w.CurrStageStatus,
	})
	if err != nil {
		// Set a default error state for NextAction instead of leaving it inconsistent
		w.NextAction = nil
		return fmt.Errorf("failed to compute next action: %w", err)
	}

	w.NextAction = nextAction
	return nil
}
