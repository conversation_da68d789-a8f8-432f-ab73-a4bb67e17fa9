//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/pinpoint"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/google/wire"

	pkgaws "github.com/epifi/be-common/pkg/aws/v2"
	pinpointpkg "github.com/epifi/be-common/pkg/aws/v2/pinpoint"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	pkgTypes "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/events"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/be-common/pkg/storage/v2/analytics"
	"github.com/epifi/gamma/segment"
	"github.com/epifi/gamma/segment/config"
	"github.com/epifi/gamma/segment/config/genconf"
	segmentConsumer "github.com/epifi/gamma/segment/consumer"
	segmentDao "github.com/epifi/gamma/segment/dao"
	"github.com/epifi/gamma/segment/developer"
	"github.com/epifi/gamma/segment/developer/processor"
	"github.com/epifi/gamma/segment/wire/types"
)

func InitializeUserSegmentService(
	rdb types.SegmentRedisStore,
	cacheStorage types.SegmentCacheStorage,
	dbv2 pkgTypes.SegmentPGDB,
	dbResourceProviders storagev2.AnalyticsDBResourceProvider,
	bqDbResourceProvider analytics.BigqueryDBResourceProvider,
	conf *config.Config,
	tsePublisher types.TriggerSegmentExportPublisher,
	awsConf aws.Config,
	eventsBroker events.Broker,
) *segment.Service {
	wire.Build(
		PinpointRoleProvider,
		pkgaws.PinpointWireSet,
		wire.Bind(new(pinpointpkg.PinpointClientInterface), new(*pinpoint.Client)),
		segment.NewService,
		segmentDao.SegmentUserDaoWireSet,
		segmentDao.SegmentDaoWireSet,
		segmentDao.SegmentMetadataDaoWireSet,
		segmentDao.AnalyticsDaoWireSet,
		segmentDao.BigqueryDaoWireSet,
	)
	return &segment.Service{}
}

func PinpointRoleProvider(conf *config.Config) pkgaws.PinpointRoleArn {
	return pkgaws.PinpointRoleArn(conf.AWSPinpointOptions.AssumeRoleARN)
}
func InitializeUserSegmentConsumerService(
	rdb types.SegmentRedisStore,
	cacheStorage types.SegmentCacheStorage,
	dbv2 pkgTypes.SegmentPGDB,
	tsePublisher types.TriggerSegmentExportPublisher,
	psePublisher types.PollSegmentExportPublisher,
	uploadSegmentExportPartFileDelayPublisher types.UploadSegmentExportPartFilePublisher,
	compareSegmentInstancesDelayPublisher types.CompareSegmentInstancesDelayPublisher,
	awsConf aws.Config,
	conf *config.Config,
	gconf *genconf.Config,
	broker events.Broker,
) *segmentConsumer.Service {
	wire.Build(
		PinpointRoleProvider,
		pkgaws.PinpointWireSet,
		wire.Bind(new(pinpointpkg.PinpointClientInterface), new(*pinpoint.Client)),
		NewS3Downloader,
		NewS3Uploader,
		segmentDao.SegmentUserDaoWireSet,
		segmentDao.SegmentDaoWireSet,
		segmentDao.SegmentMetadataDaoWireSet,
		segmentConsumer.NewConsumerService,
		wire.Bind(new(s3pkg.S3DownloaderApiInterface), new(*manager.Downloader)),
		wire.Bind(new(s3pkg.S3UploaderApiInterface), new(*manager.Uploader)),
	)
	return &segmentConsumer.Service{}
}

func InitializeUserSegmentDbStateService(
	rdb types.SegmentRedisStore,
	cacheStorage types.SegmentCacheStorage,
	dbv2 pkgTypes.SegmentPGDB,
	conf *config.Config,
	tsePublisher types.TriggerSegmentExportPublisher,
	awsConf aws.Config,
	eventsBroker events.Broker,
	dbResourceProviders storagev2.AnalyticsDBResourceProvider,
	bqDbResourceProvider analytics.BigqueryDBResourceProvider,
) *developer.SegmentDbStates {
	wire.Build(
		PinpointRoleProvider,
		pkgaws.PinpointWireSet,
		wire.Bind(new(pinpointpkg.PinpointClientInterface), new(*pinpoint.Client)),
		segment.SegmentationServiceServerWireSet,
		segmentDao.SegmentDaoWireSet,
		segmentDao.SegmentUserDaoWireSet,
		segmentDao.SegmentMetadataDaoWireSet,
		segmentDao.AnalyticsDaoWireSet,
		processor.NewSegmentsProcessor,
		processor.NewSegmentInstancesProcessor,
		processor.NewSegmentInstanceDetailsProcessor,
		processor.NewSegmentMembershipProcessor,
		developer.NewDevFactory,
		developer.NewSegmentDbStates,
		segmentDao.BigqueryDaoWireSet,
	)
	return &developer.SegmentDbStates{}
}

func NewS3Downloader(config aws.Config) *manager.Downloader {
	return manager.NewDownloader(s3.NewFromConfig(config))
}

func NewS3Uploader(config aws.Config) *manager.Uploader {
	return manager.NewUploader(s3.NewFromConfig(config))
}
