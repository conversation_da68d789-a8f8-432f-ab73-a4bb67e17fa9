Application:
  Environment: "uat"
  Name: "webfe"

AWS:
  Region: "ap-south-1"

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

Accounts:
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 12
    HystrixCommand:
      CommandName: "webfe_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 200
        ExecutionTimeout: 100ms
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80

RiskS3Config:
  BucketName: "epifi-uat-risk"

DebitCardS3Buckets:
  DcDocsBucketName: "epifi-uat-debit-card-docs"

FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_NETWORTH_MCP:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
