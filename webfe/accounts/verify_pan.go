package accounts

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	operationalStatusEnums "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/accounts/operstatus"

	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/grpc/status"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	savingsPb "github.com/epifi/gamma/api/savings"
	usersPb "github.com/epifi/gamma/api/user"
	webAccountsPb "github.com/epifi/gamma/api/webfe/accounts"
	authPkg "github.com/epifi/gamma/pkg/auth"
	feErr "github.com/epifi/gamma/pkg/frontend/errors"
	pkgSavings "github.com/epifi/gamma/pkg/savings"
)

// nolint: funlen
func (s *Service) verifyPan(ctx context.Context, req *webAccountsPb.VerifyPanRequest) (*webAccountsPb.VerifyPanResponse, error) {
	var (
		errRes = func(status *rpc.Status, errorMessage string) *webAccountsPb.VerifyPanResponse {
			return &webAccountsPb.VerifyPanResponse{
				RespHeader: &headerPb.ResponseHeader{
					Status: status,
				},
				ErrorMessage: errorMessage,
			}
		}
	)
	validateRes, err := authPkg.ValidateToken(ctx, s.authClient, req.GetReq().GetAuth().GetRefreshToken(), req.GetReq().GetAuth().GetDevice(), authPb.TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN)
	if rpcErr := epifigrpc.RPCError(validateRes, err); rpcErr != nil {
		if st, ok := status.FromError(rpcErr); ok {
			return errRes(rpc.NewStatus(uint32(st.Code()), st.Message(), ""), ""), nil
		}
		logger.Error(ctx, "error in validating refresh token", zap.Error(rpcErr))
		return errRes(rpc.StatusInternalWithDebugMsg(rpcErr.Error()), ""), nil
	}
	// enrich the context with actorId for logging in the next steps
	ctx = epificontext.CtxWithActorId(ctx, validateRes.GetActorId())

	user, err := s.getUserByActorId(ctx, validateRes.GetActorId())
	if err != nil {
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()), ""), nil
	}

	accountNo, accountId, err := s.getAccountNumberAndId(ctx, validateRes.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK)
	if err != nil {
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()), ""), nil
	}
	lastFourDigitAcctNo := accountNo[len(accountNo)-4:]

	operStatus, getOperStatusErr := s.getOperationalStatus(ctx, accountId)
	if getOperStatusErr != nil {
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()), ""), nil
	}

	if !pkgSavings.ShouldShowAlternateAccountCollectionForm(user.GetAccessRevokeDetails().GetReason(), operStatus) {
		logger.Info(ctx, "permission denied form is not enabled", zap.String(logger.REASON, user.GetAccessRevokeDetails().GetReason().String()), zap.String(logger.STATUS, operStatus.String()))
		clientErr := feErr.NewClientError(feErr.ACCOUNT_ACTIVE_CABT)
		s.eventLogger.LogUserPANVerificationServer(ctx, validateRes.GetActorId(), user.GetAccessRevokeDetails(), clientErr.Title)
		return &webAccountsPb.VerifyPanResponse{
			RespHeader:              feErr.RespHeaderFromClientErr(ctx, clientErr.Code),
			NextAction:              pkgSavings.ClientErrorAsDeeplink(ctx, clientErr),
			LastFourDigitsAccountNo: lastFourDigitAcctNo,
		}, nil
	}

	verifyPanResp, verifyPanErr := s.savingsClient.VerifyPanForAccountClosure(ctx, &savingsPb.VerifyPanForAccountClosureRequest{
		ActorId: validateRes.GetActorId(),
		Pan:     req.GetPan(),
	})
	if err = epifigrpc.RPCError(verifyPanResp, verifyPanErr); err != nil {
		logger.Error(ctx, "got not OK response from pan verification API", zap.Error(err))
		return errRes(verifyPanResp.GetStatus(), getErrorMsgFromIncorrectAttempts(verifyPanResp.GetAttemptsLeft())), nil
	}

	accessToken, err := s.createAccessToken(ctx, validateRes.GetActorId(), req)
	if err != nil {
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()), ""), nil
	}

	accClosureNextAction, clientErr := pkgSavings.AcctClosureNextAction(ctx, validateRes.GetActorId(), user.GetAccessRevokeDetails().GetReason(), s.savingsClient, s.extAcctClient)
	if clientErr != nil {
		s.eventLogger.LogUserPANVerificationServer(ctx, validateRes.GetActorId(), user.GetAccessRevokeDetails(), clientErr.Title)
		logger.Info(ctx, "next action after verify pan", zap.String(logger.SCREEN, clientErr.CodeStr))
		return &webAccountsPb.VerifyPanResponse{
			RespHeader:              feErr.RespHeaderFromClientErr(ctx, clientErr.Code),
			NextAction:              pkgSavings.ClientErrorAsDeeplink(ctx, clientErr),
			LastFourDigitsAccountNo: lastFourDigitAcctNo,
		}, nil
	}
	s.eventLogger.LogUserPANVerificationServer(ctx, validateRes.GetActorId(), user.GetAccessRevokeDetails(), accClosureNextAction.GetScreen().String())
	logger.Info(ctx, "next action after verify pan", zap.String(logger.SCREEN, accClosureNextAction.GetScreen().String()))
	return &webAccountsPb.VerifyPanResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		AccessToken:             accessToken,
		LastFourDigitsAccountNo: lastFourDigitAcctNo,
		NextAction:              accClosureNextAction,
	}, nil
}

func (s *Service) getUserByActorId(ctx context.Context, actorId string) (*usersPb.User, error) {
	userRes, err := s.usersClient.GetUser(ctx, &usersPb.GetUserRequest{
		Identifier: &usersPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userRes, err); rpcErr != nil {
		logger.Error(ctx, "error in getting actor by ID", zap.Error(rpcErr))
		return nil, rpcErr
	}
	return userRes.GetUser(), nil
}

func (s *Service) createAccessToken(ctx context.Context, actorId string, req *webAccountsPb.VerifyPanRequest) (string, error) {
	res, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{
		Id: actorId,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error in getting actor by Id", zap.Error(rpcErr))
		return "", rpcErr
	}
	return authPkg.NewToken(ctx, s.authClient,
		req.GetReq().GetAuth().GetDevice(),
		res.GetActor(),
		nil,
		"",
		authPb.TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_ACCESS_TOKEN,
		"",
		nil)
}

func (s *Service) getOperationalStatus(ctx context.Context, savAccId string) (operationalStatusEnums.OperationalStatus, error) {
	operStatusResp, operStatusErr := s.operationalStatusClient.GetOperationalStatus(ctx, &operstatus.GetOperationalStatusRequest{
		DataFreshness:     operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_LAST_KNOWN,
		AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{SavingsAccountId: savAccId},
	})
	if rpcErr := epifigrpc.RPCError(operStatusResp, operStatusErr); rpcErr != nil {
		return operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_UNSPECIFIED, rpcErr
	}

	return operStatusResp.GetOperationalStatusInfo().GetOperationalStatus(), nil
}

func (s *Service) getAccountNumberAndId(ctx context.Context, actorId string, vendor commonvgpb.Vendor) (string, string, error) {
	res, err := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: vendor,
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error in getting savings account essentials", zap.Error(rpcErr))
		return "", "", rpcErr
	}
	return res.GetAccount().GetAccountNo(), res.GetAccount().GetId(), nil
}

func getErrorMsgFromIncorrectAttempts(attemptsLeft int32) string {
	switch attemptsLeft {
	case 0:
		return "You have reached the max retries for PAN entries. \nPlease try again in 24 hours"
	case 1:
		return "The PAN Details do not match with what you entered \nduring Account Creation. You have 1 attempt left!"
	default:
		return fmt.Sprintf("The PAN Details do not match with what you entered \nduring Account Creation. You have %v attempts left!", attemptsLeft)
	}
}
