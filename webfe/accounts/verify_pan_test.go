package accounts

import (
	"google.golang.org/genproto/googleapis/type/date"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	operationalStatusEnums "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/pkg/savings"

	"context"
	"testing"

	assert "github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/frontend/header"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	webAccountsPb "github.com/epifi/gamma/api/webfe/accounts"
	feErr "github.com/epifi/gamma/pkg/frontend/errors"
)

var (
	device = &commontypes.Device{
		Model:        "TestModel",
		Manufacturer: "TestManufacturer",
		HwVersion:    "TestHwVersion",
		SwVersion:    "TestSwVersion",
		OsApiVersion: "TestOsApiVersion",
		DeviceId:     "TestDeviceId",
	}
	refreshToken = "refreshToken"
	accessToken  = "accessToken"
	actorId      = "actorId"
	user         = &userPb.User{
		Profile: &userPb.Profile{
			PAN: "pan",
		},
		AccessRevokeDetails: &userPb.AccessRevokeDetails{
			Reason: userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY,
		},
	}
	ifsc          = "FDRL12345F"
	accountNumber = "1234"
	accountHolder = "name"
)

func TestService_VerifyPan(t *testing.T) {
	unAuthenticatedStatus, _ := status.FromError(status.Errorf(codes.Unauthenticated, ""))
	type args struct {
		req *webAccountsPb.VerifyPanRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *webAccountsPb.VerifyPanResponse
		wantErr   error
		wantMocks func(args, *mockedDependencies)
	}{
		{
			name: "validate refresh token: token expired",
			args: args{
				req: &webAccountsPb.VerifyPanRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
							AuthToken: &header.AuthHeader_RefreshToken{
								RefreshToken: refreshToken,
							},
						},
					},
				},
			},
			want: &webAccountsPb.VerifyPanResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.NewStatus(uint32(unAuthenticatedStatus.Code()), unAuthenticatedStatus.Message(), ""),
				},
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.authClient.EXPECT().ValidateToken(gomock.Any(), &authPb.ValidateTokenRequest{
					Token:     args.req.GetReq().GetAuth().GetRefreshToken(),
					TokenType: authPb.TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN,
					Device:    args.req.GetReq().GetAuth().GetDevice(),
				}).Return(&authPb.ValidateTokenResponse{
					Status: rpc.NewStatus(uint32(authPb.ValidateTokenResponse_TOKEN_INVALID), "", ""),
				}, nil)
			},
		},
		{
			name: "get user failed",
			args: args{
				req: &webAccountsPb.VerifyPanRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
							AuthToken: &header.AuthHeader_RefreshToken{
								RefreshToken: refreshToken,
							},
						},
					},
				},
			},
			want: &webAccountsPb.VerifyPanResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg(rpc.StatusAsError(rpc.StatusInternalWithDebugMsg("error in get user")).Error()),
				},
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.authClient.EXPECT().ValidateToken(gomock.Any(), &authPb.ValidateTokenRequest{
					Token:     args.req.GetReq().GetAuth().GetRefreshToken(),
					TokenType: authPb.TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN,
					Device:    args.req.GetReq().GetAuth().GetDevice(),
				}).Return(&authPb.ValidateTokenResponse{
					Status:  rpc.StatusOk(),
					ActorId: actorId,
				}, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusInternalWithDebugMsg("error in get user"),
				}, nil)
			},
		},
		{
			name: "access revoked not equal to MIN KYC closure",
			args: args{
				req: &webAccountsPb.VerifyPanRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
							AuthToken: &header.AuthHeader_RefreshToken{
								RefreshToken: refreshToken,
							},
						},
					},
				},
			},
			want: &webAccountsPb.VerifyPanResponse{
				RespHeader:              feErr.RespHeaderFromClientErr(context.Background(), feErr.ACCOUNT_ACTIVE_CABT),
				NextAction:              savings.ClientErrorAsDeeplink(context.Background(), feErr.NewClientError(feErr.ACCOUNT_ACTIVE_CABT)),
				LastFourDigitsAccountNo: "5678",
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.authClient.EXPECT().ValidateToken(gomock.Any(), &authPb.ValidateTokenRequest{
					Token:     args.req.GetReq().GetAuth().GetRefreshToken(),
					TokenType: authPb.TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN,
					Device:    args.req.GetReq().GetAuth().GetDevice(),
				}).Return(&authPb.ValidateTokenResponse{
					Status:  rpc.StatusOk(),
					ActorId: actorId,
				}, nil)
				mock.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						AccountNo: "********",
					},
				}, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &userPb.User{
						Profile: &userPb.Profile{
							PAN: "pan",
						},
						AccessRevokeDetails: &userPb.AccessRevokeDetails{
							Reason: userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_ACCOUNT_DELETION_REQUEST,
						},
					},
				}, nil)

				mock.eventLogger.EXPECT().LogUserPANVerificationServer(gomock.Any(), actorId, &userPb.AccessRevokeDetails{
					Reason: userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_ACCOUNT_DELETION_REQUEST,
				}, feErr.NewClientError(feErr.ACCOUNT_ACTIVE_CABT).Title)

				mock.operStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), gomock.Any()).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
					OperationalStatusInfo: &operstatus.OperationalStatusInfo{
						AccountClosedAt: &date.Date{
							Year:  2025,
							Month: 5,
							Day:   1,
						},
						OperationalStatus: operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
					},
				}, nil)
			},
		},
		{
			name: "max verification attempts",
			args: args{
				req: &webAccountsPb.VerifyPanRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
							AuthToken: &header.AuthHeader_RefreshToken{
								RefreshToken: refreshToken,
							},
						},
					},
					Pan: "pan",
				},
			},
			want: &webAccountsPb.VerifyPanResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.NewStatus(uint32(webAccountsPb.VerifyPanResponse_PAN_INCORRECT_LOCKED), "", ""),
				},
				ErrorMessage: "You have reached the max retries for PAN entries. \nPlease try again in 24 hours",
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.authClient.EXPECT().ValidateToken(gomock.Any(), &authPb.ValidateTokenRequest{
					Token:     args.req.GetReq().GetAuth().GetRefreshToken(),
					TokenType: authPb.TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN,
					Device:    args.req.GetReq().GetAuth().GetDevice(),
				}).Return(&authPb.ValidateTokenResponse{
					Status:  rpc.StatusOk(),
					ActorId: actorId,
				}, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User:   user,
				}, nil)
				mock.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						AccountNo: "********",
					},
				}, nil)
				mock.savingsClient.EXPECT().VerifyPanForAccountClosure(gomock.Any(), &savingsPb.VerifyPanForAccountClosureRequest{
					ActorId: actorId,
					Pan:     "pan",
				}).Return(&savingsPb.VerifyPanForAccountClosureResponse{
					Status:       rpc.NewStatus(uint32(savingsPb.VerifyPanForAccountClosureResponse_PAN_INCORRECT_LOCKED), "", ""),
					AttemptsLeft: 0,
				}, nil)
				mock.operStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), gomock.Any()).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
					OperationalStatusInfo: &operstatus.OperationalStatusInfo{
						AccountClosedAt: &date.Date{
							Year:  2025,
							Month: 5,
							Day:   1,
						},
						OperationalStatus: operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
					},
				}, nil)
			},
		},
		{
			name: "incorrect pan for first time",
			args: args{
				req: &webAccountsPb.VerifyPanRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
							AuthToken: &header.AuthHeader_RefreshToken{
								RefreshToken: refreshToken,
							},
						},
					},
					Pan: "pan2",
				},
			},
			want: &webAccountsPb.VerifyPanResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.NewStatus(uint32(webAccountsPb.VerifyPanResponse_PAN_INCORRECT), "", ""),
				},
				ErrorMessage: "The PAN Details do not match with what you entered \nduring Account Creation. You have 2 attempts left!",
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.authClient.EXPECT().ValidateToken(gomock.Any(), &authPb.ValidateTokenRequest{
					Token:     args.req.GetReq().GetAuth().GetRefreshToken(),
					TokenType: authPb.TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN,
					Device:    args.req.GetReq().GetAuth().GetDevice(),
				}).Return(&authPb.ValidateTokenResponse{
					Status:  rpc.StatusOk(),
					ActorId: actorId,
				}, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User:   user,
				}, nil)
				mock.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						AccountNo: "********",
					},
				}, nil)
				mock.savingsClient.EXPECT().VerifyPanForAccountClosure(gomock.Any(), &savingsPb.VerifyPanForAccountClosureRequest{
					ActorId: actorId,
					Pan:     "pan2",
				}).Return(&savingsPb.VerifyPanForAccountClosureResponse{
					Status:       rpc.NewStatus(uint32(savingsPb.VerifyPanForAccountClosureResponse_PAN_INCORRECT), "", ""),
					AttemptsLeft: 2,
				}, nil)
				mock.operStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), gomock.Any()).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
					OperationalStatusInfo: &operstatus.OperationalStatusInfo{
						AccountClosedAt: &date.Date{
							Year:  2025,
							Month: 5,
							Day:   1,
						},
						OperationalStatus: operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
					},
				}, nil)
			},
		},
		{
			name: "error from account closure next action",
			args: args{
				req: &webAccountsPb.VerifyPanRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							Device: device,
							AuthToken: &header.AuthHeader_RefreshToken{
								RefreshToken: refreshToken,
							},
						},
					},
					Pan: "pan",
				},
			},
			want: &webAccountsPb.VerifyPanResponse{
				RespHeader:              feErr.RespHeaderFromClientErr(context.Background(), feErr.ACCOUNT_CLOSED_REPORTED_LESS_THAN_ONE),
				NextAction:              savings.ClientErrorAsDeeplink(context.Background(), feErr.NewClientError(feErr.ACCOUNT_CLOSED_REPORTED_LESS_THAN_ONE)),
				LastFourDigitsAccountNo: "5678",
			},
			wantErr: nil,
			wantMocks: func(args args, mock *mockedDependencies) {
				mock.authClient.EXPECT().ValidateToken(gomock.Any(), &authPb.ValidateTokenRequest{
					Token:     args.req.GetReq().GetAuth().GetRefreshToken(),
					TokenType: authPb.TokenType_WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN,
					Device:    args.req.GetReq().GetAuth().GetDevice(),
				}).Return(&authPb.ValidateTokenResponse{
					Status:  rpc.StatusOk(),
					ActorId: actorId,
				}, nil)
				mock.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&userPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User:   user,
				}, nil)
				mock.savingsClient.EXPECT().VerifyPanForAccountClosure(gomock.Any(), &savingsPb.VerifyPanForAccountClosureRequest{
					ActorId: actorId,
					Pan:     "pan",
				}).Return(&savingsPb.VerifyPanForAccountClosureResponse{
					Status: rpc.StatusOk(),
				}, nil)
				mock.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						Id: actorId,
					},
				}, nil)
				mock.authClient.EXPECT().CreateToken(gomock.Any(), gomock.Any()).Return(&authPb.CreateTokenResponse{
					Status: rpc.StatusOk(),
					Token:  accessToken,
				}, nil)
				mock.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						AccountNo: "********",
					},
				}, nil)

				mock.savingsClient.EXPECT().GetAccount(gomock.Any(), &savingsPb.GetAccountRequest{
					Identifier: &savingsPb.GetAccountRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&savingsPb.GetAccountResponse{Account: &savingsPb.Account{Id: "savId"}}, nil)
				mock.savingsClient.EXPECT().GetClosedAccountBalTransferData(gomock.Any(), &savingsPb.GetClosedAccountBalTransferDataRequest{
					SavingsAccountId: "savId",
				}).Return(&savingsPb.GetClosedAccountBalTransferDataResponse{
					Status: rpc.StatusOk(),
					Entries: []*savingsPb.ClosedAccountBalanceTransfer{
						{
							Id:               "cbt-id",
							SavingsAccountId: "savId",
							ReportedClosureBalance: &gmoney.Money{
								CurrencyCode: "INR",
								Units:        0,
							},
							UpdatedAt: timestampPb.Now(),
						},
					},
				}, nil)

				mock.eventLogger.EXPECT().LogUserPANVerificationServer(gomock.Any(), actorId, &userPb.AccessRevokeDetails{
					Reason: userPb.AccessRevokeReason_ACCESS_REVOKE_REASON_MIN_KYC_ACCOUNT_EXPIRY,
				}, feErr.NewClientError(feErr.ACCOUNT_CLOSED_REPORTED_LESS_THAN_ONE).Title)

				mock.operStatusClient.EXPECT().GetOperationalStatus(gomock.Any(), gomock.Any()).Return(&operstatus.GetOperationalStatusResponse{
					Status: rpc.StatusOk(),
					OperationalStatusInfo: &operstatus.OperationalStatusInfo{
						AccountClosedAt: &date.Date{
							Year:  2025,
							Month: 5,
							Day:   1,
						},
						OperationalStatus: operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_ACTIVE,
					},
				}, nil)
				mock.extAcctClient.EXPECT().GetBankAccounts(gomock.Any(), gomock.Any()).Return(&extacct.GetBankAccountsResponse{
					Status: rpc.StatusOk(),
					BankAccounts: []*extacct.BankAccount{
						{
							Ifsc:          ifsc,
							AccountNumber: accountNumber,
							Name:          accountHolder,
						},
					},
				}, nil)
				mock.savingsClient.EXPECT().StoreClosedAccountBalTransferDataFromStatement(gomock.Any(), gomock.Any()).Return(&savingsPb.StoreClosedAccountBalTransferDataFromStatementResponse{
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md := newServiceWithMocks(t)
			tt.wantMocks(tt.args, md)
			got, err := s.VerifyPan(context.Background(), tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			if !proto.Equal(got, tt.want) {
				t.Errorf("VerifyPan() got = %v, want %v", got, tt.want)
			}
		})
	}
}
