package auto_suggestions

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	accountsPb "github.com/epifi/gamma/api/accounts"
	pb "github.com/epifi/gamma/api/frontend/analyser"
	deeplinkpb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type shortCutsRecord struct {
	id          int
	displayName string
	deeplink    *deeplinkpb.Deeplink
	imageUrl    string
	synonyms    []string
}

type shortCutId int

const (
	IdReminders shortCutId = iota
	IdCreditScore
	IdLoan
	IdNetWorth
	IdSalaryProgram
	IdInfinitePlan
	IdPlusPlan
	IdConnectedAccount
	IdEarlySalary
	IdCreditCard
	IdDebitCard
	IdCardOffers
	IdMf
	IdJump
	IdFd
	IdSd
	IdSpendAnalyser
	IdFiMinutes
	IdWaysToEarn
	IdOffersCatalog
	IdReferrals
	IdOffersOrders
)

var (
	shortCutsIndex = []shortCutsRecord{
		{
			id:          int(IdReminders),
			displayName: "Set Reminders",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_REMINDERS_ENTRY_POINT,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/SetReminders.png",
			synonyms: []string{"budgeting"},
		},
		{
			id:          int(IdCreditScore),
			displayName: "Credit Score",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_ANALYSER_SCREEN,
				ScreenOptions: &deeplinkpb.Deeplink_AnalyserScreenOptions{
					AnalyserScreenOptions: &deeplinkpb.AnalyserScreenOptions{
						AnalyserName: pb.AnalyserName_ANALYSER_NAME_CREDIT_SCORE.String(),
					},
				},
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CreditScore.png",
			synonyms: []string{"credit score", "experian", "bureau"},
		},
		{
			id:          int(IdLoan),
			displayName: "Instant Loan",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/InstantLoan.png",
			synonyms: []string{"loan", "pre approved loan"},
		},
		{
			id:          int(IdNetWorth),
			displayName: "Net Worth",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_NET_WORTH_HUB_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/NetWorth.png",
			synonyms: []string{"net worth", "networth", "epf", "employee provident fund", "my assets", "my liabilities"},
		},
		// {
		// 	id:          int(IdSalaryProgram),
		// 	displayName: "Salary Plan",
		// 	deeplink: &deeplinkpb.Deeplink{
		// 		Screen: deeplinkpb.Screen_SALARY_PROGRAM_INTRO_SCREEN,
		// 	},
		// 	imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/explore_salary_v3.png",
		// 	synonyms: []string{"salary plan"},
		// },
		{
			id:          int(IdInfinitePlan),
			displayName: "Infinite Plan",
			deeplink:    tiering.AllPlansDeeplink(tieringExtPb.Tier_TIER_FI_INFINITE, true),
			imageUrl:    "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/explore_infinite_v3.png",
			synonyms:    []string{"infinite plan"},
		},
		{
			id:          int(IdPlusPlan),
			displayName: "Plus Plan",
			deeplink:    tiering.AllPlansDeeplink(tieringExtPb.Tier_TIER_FI_PLUS, true),
			imageUrl:    "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/explore_plus_v3.png",
			synonyms:    []string{"plus plan"},
		},
		{
			id:          int(IdConnectedAccount),
			displayName: "All Bank Balances",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/ConnectYourBanks.png",
			synonyms: []string{"connected account"},
		},
		{
			id:          int(IdEarlySalary),
			displayName: "Instant Salary",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_EARLY_SALARY_LANDING_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/explore-early_salary.png",
			synonyms: []string{"early salary", "instant salary"},
		},
		{
			id:          int(IdCreditCard),
			displayName: "Credit Card",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CreditCard.png",
			synonyms: []string{"credit card"},
		},
		{
			id:          int(IdDebitCard),
			displayName: "Debit Card",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_CARD_HOME_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/DebitCard.png",
			synonyms: []string{"credit card"},
		},
		{
			id:          int(IdCardOffers),
			displayName: "Card Offers",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CardOffers.png",
			synonyms: []string{"debit card", "card offers"},
		},
		{
			id:          int(IdMf),
			displayName: "Mutual Funds",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_MUTUAL_FUND_COLLECTIONS_LANDING_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/MutualFunds.png",
			synonyms: []string{"mutual fund", "mf"},
		},
		{
			id:          int(IdJump),
			displayName: "Jump",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_P2P_INVESTMENT_DASHBOARD_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/FiJump.png",
			synonyms: []string{"jump", "p2p"},
		},
		{
			id:          int(IdFd),
			displayName: "Fixed Deposits",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_DEPOSIT_LANDING_SCREEN,
				ScreenOptions: &deeplinkpb.Deeplink_DepositAccountLandingScreenOption{
					DepositAccountLandingScreenOption: &deeplinkpb.DepositAccountLandingScreenOptions{
						DepositType: accountsPb.Type_FIXED_DEPOSIT,
					},
				},
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/FixedDeposits.png",
			synonyms: []string{"fd", "fixed deposits"},
		},
		{
			id:          int(IdSd),
			displayName: "Smart Deposits",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_DEPOSIT_LANDING_SCREEN,
				ScreenOptions: &deeplinkpb.Deeplink_DepositAccountLandingScreenOption{
					DepositAccountLandingScreenOption: &deeplinkpb.DepositAccountLandingScreenOptions{
						DepositType: accountsPb.Type_SMART_DEPOSIT,
					},
				},
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/SmartDeposits.png",
			synonyms: []string{"sd", "smart deposits"},
		},
		{
			id:          int(IdSpendAnalyser),
			displayName: "Spend Analyser",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_ANALYSER_SCREEN,
				ScreenOptions: &deeplinkpb.Deeplink_AnalyserScreenOptions{
					AnalyserScreenOptions: &deeplinkpb.AnalyserScreenOptions{
						AnalyserName: pb.AnalyserName_ANALYSER_NAME_SPEND_TOP_CATEGORIES.String(),
					},
				},
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/SpendAnalyser.png",
			synonyms: []string{"spend analyser", "category"},
		},
		{
			id:          int(IdFiMinutes),
			displayName: "Fi Minutes",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_INSIGHTS_STORIES_SCREEN,
				ScreenOptions: &deeplinkpb.Deeplink_InsightsStoriesScreenOptions{
					InsightsStoriesScreenOptions: &deeplinkpb.InsightsStoriesScreenOptions{
						StoryGroup: "STORY_GROUP_MONTHLY_RECAP",
					},
				},
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/YourFiMinutes.png",
			synonyms: []string{"spend analyser", "category"},
		},
		{
			id:          int(IdWaysToEarn),
			displayName: "Earn Rewards",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_REWARDS_WAYS_TO_EARN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/EarnRewards.png",
			synonyms: []string{"rewards", "earn"},
		},
		{
			id:          int(IdOffersCatalog),
			displayName: "Spend Fi Coins",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_OFFERS_LANDING_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/SpednFiCoins.png",
			synonyms: []string{"fi coins", "coins"},
		},
		{
			id:          int(IdReferrals),
			displayName: "Refer & Earn",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_REFERRALS_ELIGIBILITY_LANDING_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/ReferAndEarn.png",
			synonyms: []string{"referrals"},
		},
		{
			id:          int(IdOffersOrders),
			displayName: "Collected Offers",
			deeplink: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_REDEEMED_OFFERS_SCREEN,
			},
			imageUrl: "https://epifi-icons.pointz.in/home-v2/ExploreIconsV2/CollectedOffers.png",
			synonyms: []string{"collected offers", "redeemed offers"},
		},
	}
)

func getShortCutsIndex() []shortCutsRecord {
	var enrichedShortCuts []shortCutsRecord
	for _, eachShortCut := range shortCutsIndex {
		enrichedShortCut := eachShortCut
		if shortCutId(eachShortCut.id) == IdNetWorth {
			screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&networth.NetWorthHubScreenOptions{
				LoadingNessage: &commontypes.Text{
					FontColor: "#F6F9FD",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Taking you to Net Worth",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
					},
				},
			})
			if err != nil {
				logger.ErrorNoCtx("failed to convert NetWorthHubScreenOptions to screen option v2", zap.Error(err))
				continue
			}
			enrichedShortCut.deeplink.ScreenOptionsV2 = screenOptionsV2
		}
		enrichedShortCuts = append(enrichedShortCuts, enrichedShortCut)
	}
	return enrichedShortCuts
}
