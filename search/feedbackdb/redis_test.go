package feedbackdb

import (
	"context"
	"testing"

	"github.com/alicebob/miniredis/v2"
	"github.com/redis/go-redis/v9"
)

func TestRedisDB_GetCounterByKey(t *testing.T) {
	rDB := miniredis.RunT(t)
	redisClient := redis.NewClient(&redis.Options{
		Addr: rDB.Addr(),
	})

	// Pre-test setup
	ctx := context.Background()
	key := "key"
	redisClient.Incr(ctx, key)
	redisDB := NewRedisDB(redisClient)

	// Check if key exists
	val, err := redisDB.GetCounterByKey(ctx, key)
	if err != nil {
		t.<PERSON><PERSON>rf("Expected no error, found error: %#v", err)
	}
	if val != 1 {
		t.<PERSON><PERSON>rf("Expected value to be 1, found %d", val)
	}

	// Check for random key
	val, err = redisDB.GetCounterByKey(ctx, "random")
	if err == nil {
		t.<PERSON><PERSON><PERSON>("Expected error, found no error")
	}
	if val != 0 {
		t.<PERSON><PERSON><PERSON>("Expected value to be 0, found %d", val)
	}
}
