// nolint:dupl,funlen
package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/olivere/elastic/v7"
	"github.com/pkg/errors"

	"github.com/epifi/gamma/api/cx/issue_category"
	irPb "github.com/epifi/gamma/api/inapphelp/issue_reporting"
	searchPb "github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/search/config/genconf"
	"github.com/epifi/gamma/search/dao/index"
	"github.com/epifi/gamma/search/dao/query"
	"github.com/epifi/gamma/search/queryBuilder"
)

// type to distinguish between same ES type for wire initialization
type EsClientUserIssue ES

// type to distinguish between same index type for wire initialization
type UserIssueIndexName string

type UserIssueSearcherImpl struct {
	index    UserIssueIndexName
	esClient ES
	qBuilder queryBuilder.QueryBuilder
}

var _ UserIssueSearcherI = &UserIssueSearcherImpl{}

func NewUserIssueSearcherImpl(index UserIssueIndexName, esClient EsClientUserIssue) *UserIssueSearcherImpl {
	qBuilder := queryBuilder.NewQueryBuilder()
	return &UserIssueSearcherImpl{
		index:    index,
		esClient: esClient,
		qBuilder: qBuilder,
	}
}

func (c *UserIssueSearcherImpl) executeQuery(ctx context.Context, query string) (*elastic.SearchResult, error) {
	respBytes, _, err := c.esClient.Query(ctx, query, string(c.index))
	if err != nil {
		return nil, fmt.Errorf("error in querying: %w", err)
	}
	var esResp elastic.SearchResult
	err = json.Unmarshal(respBytes, &esResp)
	if err != nil {
		return nil, fmt.Errorf("error in resp json unmarshal: %w", err)
	}
	if esResp.Status == 400 || esResp.Error != nil {
		return nil, fmt.Errorf("error in post query to es: %s", esResp.Error.Reason)
	}
	return &esResp, nil
}

func (c *UserIssueSearcherImpl) GetQueryResults(ctx context.Context, params *query.UserIssueSearchParams, _ *genconf.UserIssueSearchConfig) (*searchPb.UserIssueResult, error) {
	elasticQuery, err := c.qBuilder.UserIssueSearchQuery(ctx, params)
	if err != nil {
		return nil, errors.Wrap(err, "failed to build search query")
	}
	resp, err := c.executeQuery(ctx, elasticQuery)
	if err != nil || resp.Error != nil {
		return nil, errors.Wrap(err, "error while searching user issue")
	}
	var searchResults []*index.UserIssue
	if resp != nil && resp.Hits != nil {
		for _, hit := range resp.Hits.Hits {
			result := &index.UserIssue{}
			unmarshalErr := json.Unmarshal(hit.Source, result)
			if unmarshalErr != nil {
				return nil, errors.Wrap(unmarshalErr, "error in unmarshalling issue result")
			}
			searchResults = append(searchResults, result)
		}
	}
	issueProto := convertUserIssueToProto(searchResults)
	return &searchPb.UserIssueResult{
		Issues: issueProto,
	}, nil
}

func convertUserIssueToProto(results []*index.UserIssue) []*irPb.KnownIssue {
	var protoList []*irPb.KnownIssue
	for _, issue := range results {
		protoList = append(protoList, &irPb.KnownIssue{
			QueryText: issue.QueryText,
			Keywords:  issue.Keywords,
			IssueCategory: &issue_category.IssueCategory{
				ProductCategory:        issue.ProductCategory,
				ProductCategoryDetails: issue.ProductCategoryDetails,
				SubCategory:            issue.SubCategory,
			},
		})
	}
	return protoList
}
