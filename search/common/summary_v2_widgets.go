package common

//nolint:dupl
import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/frontend/analyser"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	"github.com/epifi/gamma/api/frontend/investment/mutualfund/clientstates"
	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/frontend/search/widget"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	searchPb "github.com/epifi/gamma/api/search"
	"github.com/epifi/gamma/api/search/summary"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/investment"
	"github.com/epifi/gamma/search/constant"
	dao_query "github.com/epifi/gamma/search/dao/query"
	"github.com/epifi/gamma/search/dao/summary_v2"
	"github.com/epifi/gamma/search/preview_page/ui"
)

// US Stocks related constants
const (
	usStocksHeading             = "US Stocks"
	usStocksInvestedHeading     = "Invested Amount"
	usStocksCurrentValueHeading = "Current Value"
	usStocksReturnsHeading      = "Returns"
	usStocksDailyReturnsHeading = "Daily Returns"
	usStocksClosePriceHeading   = "Close Price"
	usStocksHeadingImgUrl       = "https://epifi-icons.pointz.in/quick-link-icons/US_Stocks.png"
)

const (
	CategoryImageTmpl = "https://epifi-icons.pointz.in/askfi-txn-categories/%s.svg"
	// TODO(Atul): Take from designer and add
	LoanEmiIconS3Url               = "https://epifi-icons.pointz.in/quick-link-icons/loan_processing.svg"
	SentIconS3Url                  = "https://epifi-icons.pointz.in/quick-link-icons/sent.svg"
	ReceivedIconS3Url              = "https://epifi-icons.pointz.in/quick-link-icons/received.svg"
	ReturnsUpIconS3Url             = "https://epifi-icons.pointz.in/quick-link-icons/Returns+Up.svg"
	ReturnsDownIconS3Url           = "https://epifi-icons.pointz.in/quick-link-icons/Returns+Down.svg"
	CopyCtaIconS3Url               = "https://epifi-icons.pointz.in/quick-link-icons/copy.svg"
	DefaultIconForSummaryV2        = "https://epifi-icons.pointz.in/quick-link-icons/ReceiptIcon.svg"
	NoTransactionsStateIconS3Url   = "https://epifi-icons.pointz.in/quick-link-icons/No+Txn+State+Icon.svg"
	RemarksDefaultIconForSummaryV2 = "https://epifi-icons.pointz.in/quick-link-icons/summary_remarks.png"
	JumpS3IconUrl                  = "https://epifi-icons.pointz.in/quick-link-icons/Jump.svg"
	MutualFundS3IconUrl            = "https://epifi-icons.pointz.in/quick-link-icons/Mutual+Funds.svg"
	SmartDepositS3IconUrl          = "https://epifi-icons.pointz.in/quick-link-icons/SD.svg"
	FixedDepositS3IconUrl          = "https://epifi-icons.pointz.in/quick-link-icons/FD.svg"
	SalaryProgramS3IconUrl         = "https://epifi-icons.pointz.in/quick-link-icons/Salary+Account.svg"
	DefaultIconForDebitCardOffers  = "https://epifi-icons.pointz.in/quick-link-icons/Debit+Card+Offers.svg"
	MaximumDebitCardOffersToShow   = 3
	// SummaryHeadingRowPadding is used for the heading row in the summary v2
	SummaryHeadingRowPadding = 16
	// SummaryNonHeadingRowPadding is used for non-heading rows in the summary v2
	SummaryNonHeadingRowPadding = 8
)

const (
	// Mutual fund related constants
	NavTitle    = "NAV"
	Invested    = "Invested"
	Current     = "Current"
	Returns     = "Returns"
	Avg1YReturn = "Avg 1Y Return"
)

const (
	// CTA display text constants
	CtaDisplayTextCopy = "copy"
	// CTA toast text constants
	CtaToastTextCopiedToClipboard = "Copied to clipboard"
)

// Debit Card offers skill constants
const (
	BgColorForOfferCodeContentElement = "#ECEEF0"
	OfferCodeContentElementHeading    = "CODE"
)

var (
	// Icons for category
	// https://docs.google.com/spreadsheets/d/1WORrG1RtEzYkMmAA9CVYnlgpCN-KWcUPM-jDtHLrWYY/edit#gid=*********
	DisplayCatToFileName = map[string]string{
		"bank fees":             "Bank%20Fees",
		"books & publications":  "Books%20%26%20Publications",
		"books publications":    "Books%20%26%20Publications",
		"business spends":       "Business%20Spends",
		"cash":                  "Cash",
		"credit card & loan":    "Credit%20Card%20%26%20Loan",
		"credit card loan":      "Credit%20Card%20%26%20Loan",
		"crypto":                "Crypto",
		"deposits":              "Deposits",
		"dividend":              "Dividend",
		"education":             "Education",
		"entertainment":         "Entertainment",
		"family care":           "Family%20Care",
		"fashion":               "Fashion",
		"food & drinks":         "Food%20%26%20Drinks",
		"food drinks":           "Food%20%26%20Drinks",
		"groceries":             "Groceries",
		"gadget":                "Gadget",
		"gift":                  "Gift",
		"housing & bills":       "Housing%20%26%20Bills",
		"housing bills":         "Housing%20%26%20Bills",
		"income":                "Income",
		"insurance":             "Insurance",
		"interest":              "Interest",
		"investment withdrawal": "Investment%20Withdrawal",
		"miscellaneous":         "Miscellaneous",
		"borrowed":              "Borrowed",
		"money transfer":        "Money%20Transfer",
		"money transfer debit":  "Money%20Transfer",
		"money transfer credit": "Money%20Transfer",
		"personal care":         "Personal%20Care",
		"pets":                  "Pets",
		"refund & reward":       "Refund%20%26%20Reward",
		"refund reward":         "Refund%20%26%20Reward",
		"self transfer":         "Self%20Transfer",
		"self transfer debit":   "Self%20Transfer",
		"self transfer credit":  "Self%20Transfer",
		"shopping":              "Shopping",
		"software":              "Software",
		"sports & games":        "Sports%20%26%20Games",
		"sports games":          "Sports%20%26%20Games",
		"stocks & mf":           "Stocks%20%26%20MF",
		"stocks mf":             "Stocks%20%26%20MF",
		"tax":                   "Tax",
		"travel & vacation":     "Travel%20%26%20Vacation",
		"travel vacation":       "Travel%20%26%20Vacation",
		"commute":               "Commute",
		"unknown":               "Unknown",
		"wallet & payment":      "Wallet%20%26%20Payment",
		"wallet payment":        "Wallet%20%26%20Payment",
	}
)

func BuildSummaryRowsForUsStocksPortfolio(portfolioSummary summary_v2.UsStocksInvestmentSummary) []*widget.SummaryRow {
	summaryHeadingRow := &widget.SummaryRow{
		Heading: usStocksHeading,
		ChatheadImageContent: &widget.SummaryRow_ImgUrl{
			ImgUrl: usStocksHeadingImgUrl,
		},
		SubHeading_1:      fmt.Sprintf("%d investments", portfolioSummary.TotalInvestments),
		SubHeading_2:      "xxxx",
		RowPadding:        SummaryHeadingRowPadding,
		ShouldShowDivider: true,
	}

	investedRow := &widget.SummaryRow{
		Heading:           usStocksInvestedHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: money.ToDisplayStringInIndianFormat(money.ParseFloat(portfolioSummary.InvestedAmount, money.USDCurrencyCode), 2, true), // TODO: Find USD
		},
	}

	currentValueRow := &widget.SummaryRow{
		Heading:           usStocksCurrentValueHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: money.ToDisplayStringInIndianFormat(money.ParseFloat(portfolioSummary.CurrentValue, money.USDCurrencyCode), 2, true), // TODO: Find USD
		},
	}

	returnsRow := &widget.SummaryRow{
		Heading:     usStocksReturnsHeading,
		ContentType: widget.ContentType_TEXT,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: fmt.Sprintf("%.2f %s", portfolioSummary.Returns, "%"),
		},
	}

	return []*widget.SummaryRow{
		summaryHeadingRow,
		investedRow,
		currentValueRow,
		returnsRow,
	}
}

func BuildSummaryRowsForNonInvestedUsStockWidget(stockDetails summary_v2.UsStockSearchSummaryRows) []*widget.SummaryRow {

	summaryHeadingRow := &widget.SummaryRow{
		Heading: CapitaliseFirstCharOfString(stockDetails.CompanyName),
		ChatheadImageContent: &widget.SummaryRow_ImgUrl{
			ImgUrl: stockDetails.CompanyLogoURL,
		},
		SubHeading_1:      stockDetails.GetExchange(),
		SubHeading_2:      stockDetails.GetTickerSymbol(),
		RowPadding:        SummaryHeadingRowPadding,
		ShouldShowDivider: true,
		ChatheadDeeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_LANDING_SCREEN,
		},
	}

	returnsRow := &widget.SummaryRow{
		Heading:           usStocksDailyReturnsHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: fmt.Sprintf("%.2f %s", stockDetails.Returns, "%"),
		},
	}

	closePriceRow := &widget.SummaryRow{
		Heading:           usStocksClosePriceHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: money.ToDisplayStringWithPrecision(stockDetails.ClosePrice, 2),
		},
	}

	return []*widget.SummaryRow{
		summaryHeadingRow,
		returnsRow,
		closePriceRow,
	}
}

func BuildSummaryRowsForUsSingleStockSearchWidget(stockSearchParams summary_v2.UsStockSearchSummaryRows) []*widget.SummaryRow {
	summaryHeadingRow := &widget.SummaryRow{
		Heading: CapitaliseFirstCharOfString(stockSearchParams.CompanyName),
		ChatheadImageContent: &widget.SummaryRow_ImgUrl{
			ImgUrl: stockSearchParams.CompanyLogoURL,
		},
		SubHeading_1:      stockSearchParams.Exchange + " : " + stockSearchParams.TickerSymbol,
		SubHeading_2:      fmt.Sprintf("You own %.2f units", stockSearchParams.TotalUnitsOwned),
		RowPadding:        SummaryHeadingRowPadding,
		ShouldShowDivider: true,
		ChatheadDeeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_USSTOCKS_SYMBOL_DETAILS_SCREEN,
			ScreenOptions: &deeplink.Deeplink_UsstocksSymbolDetailsScreenOptions{
				UsstocksSymbolDetailsScreenOptions: &deeplink.USStocksSymbolDetailsScreenOptions{
					StockId: stockSearchParams.StockID,
				},
			},
		},
	}

	investedAmountRow := &widget.SummaryRow{
		Heading:           usStocksInvestedHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: fmt.Sprintf("$%.2f", stockSearchParams.InvestedAmount),
		},
	}

	currentValueRow := &widget.SummaryRow{
		Heading:           usStocksCurrentValueHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: fmt.Sprintf("$%.2f", stockSearchParams.CurrentValue),
		},
	}

	returnsRow := &widget.SummaryRow{
		Heading:           usStocksReturnsHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: fmt.Sprintf("%.2f %s", stockSearchParams.Returns, "%"),
		},
	}

	return []*widget.SummaryRow{
		summaryHeadingRow,
		investedAmountRow,
		currentValueRow,
		returnsRow,
	}
}

// nolint:funlen,dupl
func BuildSummaryRowsForCatSummaryWidget(txnWidgetSummaryRowsParamsCat summary_v2.TxnWidgetSummaryRowsParams) []*widget.SummaryRow {
	var (
		debitHeading, creditHeading string
		summaryRowsCat              []*widget.SummaryRow
	)
	debitHeading, creditHeading = getDebitAndCreditHeading(txnWidgetSummaryRowsParamsCat.AggregationBucket, txnWidgetSummaryRowsParamsCat.IsMerchant)
	summaryRowCatHeadingRow := &widget.SummaryRow{
		Heading:           CapitaliseFirstCharOfString(txnWidgetSummaryRowsParamsCat.Heading),
		SubHeading_1:      GetTxnsCountStr(txnWidgetSummaryRowsParamsCat.TxnCount),
		SubHeading_2:      CapitaliseFirstCharOfString(txnWidgetSummaryRowsParamsCat.TimeToShow),
		RowPadding:        SummaryHeadingRowPadding,
		ShouldShowDivider: true,
	}
	switch {
	case summaryRowCatHeadingRow.Heading == "":
		summaryRowCatHeadingRow.Heading = GetDefaultHeadingForTab(txnWidgetSummaryRowsParamsCat.TabName)
		summaryRowCatHeadingRow.ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: DefaultIconForSummaryV2,
		}
	case txnWidgetSummaryRowsParamsCat.ImageUrl != "":
		summaryRowCatHeadingRow.ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: txnWidgetSummaryRowsParamsCat.ImageUrl,
		}
	case txnWidgetSummaryRowsParamsCat.DisplayCategory != nil:
		summaryRowCatHeadingRow.ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: fmt.Sprintf(CategoryImageTmpl, DisplayCatToFileName[strings.ToLower(txnWidgetSummaryRowsParamsCat.DisplayCategory[0])]),
		}
	}
	summaryRowsCat = append(summaryRowsCat, summaryRowCatHeadingRow)
	debitSummaryRow := &widget.SummaryRow{
		Heading:           debitHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text:     money.ToDisplayStringInIndianFormat(money.ParseFloat(txnWidgetSummaryRowsParamsCat.DebitValue, money.RupeeCurrencyCode), 2, true),
			TextIcon: SentIconS3Url,
		},
	}
	creditSummaryRow := &widget.SummaryRow{
		Heading:     creditHeading,
		ContentType: widget.ContentType_TEXT,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text:     money.ToDisplayStringInIndianFormat(money.ParseFloat(txnWidgetSummaryRowsParamsCat.CreditValue, money.RupeeCurrencyCode), 2, true),
			TextIcon: ReceivedIconS3Url,
		},
	}
	if txnWidgetSummaryRowsParamsCat.DebitValue > 0 {
		summaryRowsCat = append(summaryRowsCat, debitSummaryRow)
	}
	if txnWidgetSummaryRowsParamsCat.CreditValue > 0 {
		summaryRowsCat = append(summaryRowsCat, creditSummaryRow)
	}
	if len(summaryRowsCat) > 0 {
		summaryRowsCat[len(summaryRowsCat)-1].RowPadding = 0
		summaryRowsCat[len(summaryRowsCat)-1].ShouldShowDivider = false
	}
	return summaryRowsCat
}

// nolint:funlen,dupl
func BuildSummaryRowsForRemarksSummaryWidget(txnWidgetSummaryRowsParamsRemarks summary_v2.TxnWidgetSummaryRowsParams) []*widget.SummaryRow {
	var (
		debitHeading, creditHeading string
		summaryRowsRemarks          []*widget.SummaryRow
	)
	debitHeading, creditHeading = getDebitAndCreditHeading(txnWidgetSummaryRowsParamsRemarks.AggregationBucket, txnWidgetSummaryRowsParamsRemarks.IsMerchant)
	summaryRowsRemarksHeading := &widget.SummaryRow{
		Heading:           CapitaliseFirstCharOfString(txnWidgetSummaryRowsParamsRemarks.Heading),
		SubHeading_1:      GetTxnsCountStr(txnWidgetSummaryRowsParamsRemarks.TxnCount),
		SubHeading_2:      CapitaliseFirstCharOfString(txnWidgetSummaryRowsParamsRemarks.TimeToShow),
		RowPadding:        SummaryHeadingRowPadding,
		ShouldShowDivider: true,
	}
	if summaryRowsRemarksHeading.Heading == "" {
		summaryRowsRemarksHeading.Heading = GetDefaultHeadingForTab(txnWidgetSummaryRowsParamsRemarks.TabName)
		summaryRowsRemarksHeading.ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: DefaultIconForSummaryV2,
		}
	} else {
		summaryRowsRemarksHeading.ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: RemarksDefaultIconForSummaryV2,
		}
	}
	summaryRowsRemarks = append(summaryRowsRemarks, summaryRowsRemarksHeading)

	debitSummaryRow := &widget.SummaryRow{
		Heading:           debitHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text:     money.ToDisplayStringInIndianFormat(money.ParseFloat(txnWidgetSummaryRowsParamsRemarks.DebitValue, money.RupeeCurrencyCode), 2, true),
			TextIcon: SentIconS3Url,
		},
	}
	creditSummaryRow := &widget.SummaryRow{
		Heading:     creditHeading,
		ContentType: widget.ContentType_TEXT,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text:     money.ToDisplayStringInIndianFormat(money.ParseFloat(txnWidgetSummaryRowsParamsRemarks.CreditValue, money.RupeeCurrencyCode), 2, true),
			TextIcon: ReceivedIconS3Url,
		},
	}
	if txnWidgetSummaryRowsParamsRemarks.DebitValue > 0 {
		summaryRowsRemarks = append(summaryRowsRemarks, debitSummaryRow)
	}
	if txnWidgetSummaryRowsParamsRemarks.CreditValue > 0 {
		summaryRowsRemarks = append(summaryRowsRemarks, creditSummaryRow)
	}
	if len(summaryRowsRemarks) > 0 {
		summaryRowsRemarks[len(summaryRowsRemarks)-1].RowPadding = 0
		summaryRowsRemarks[len(summaryRowsRemarks)-1].ShouldShowDivider = false
	}
	return summaryRowsRemarks
}

func GetTxnsCountStr(txnCount int) string {
	if txnCount == 0 {
		return ""
	}
	if txnCount == 1 {
		return fmt.Sprintf("%d TRANSACTION", txnCount)
	}
	return fmt.Sprintf("%d TRANSACTIONS", txnCount)
}

func GetDefaultHeadingForTab(tabName string) string {
	switch tabName {
	case constant.FiCCTabName:
		return summary_v2.OnFiCC
	case constant.AllBanksTabName:
		return summary_v2.OnAllBanks
	default:
		return summary_v2.DefaultHeadingPrefixSummaryV2 + tabName
	}
}

//nolint:dupl,funlen
func BuildSummaryRowsForTxnWidget(txnWidgetSummaryRowsParams *summary_v2.TxnWidgetSummaryRowsParams, chatheadDeeplink *deeplink.Deeplink) []*widget.SummaryRow {
	var summaryRows []*widget.SummaryRow
	var direction string
	if len(txnWidgetSummaryRowsParams.TxnDirection) > 0 {
		direction = txnWidgetSummaryRowsParams.TxnDirection[0]
	}
	debitHeading, creditHeading := getDebitAndCreditHeading(txnWidgetSummaryRowsParams.AggregationBucket, txnWidgetSummaryRowsParams.IsMerchant)
	summaryRowHeading := &widget.SummaryRow{
		Heading:           CapitaliseFirstCharOfString(txnWidgetSummaryRowsParams.Heading),
		SubHeading_1:      GetTxnsCountStr(txnWidgetSummaryRowsParams.TxnCount),
		SubHeading_2:      CapitaliseFirstCharOfString(txnWidgetSummaryRowsParams.TimeToShow),
		RowPadding:        SummaryHeadingRowPadding,
		ShouldShowDivider: true,
	}
	switch {
	case summaryRowHeading.Heading == "":
		summaryRowHeading.Heading = GetDefaultHeadingForTab(txnWidgetSummaryRowsParams.TabName)
		summaryRowHeading.ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: DefaultIconForSummaryV2,
		}
	case txnWidgetSummaryRowsParams.ImageUrl != "":
		summaryRowHeading.ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: txnWidgetSummaryRowsParams.ImageUrl,
		}
	default:
		summaryRowHeading.ChatheadImageContent = &widget.SummaryRow_Chathead{
			Chathead: &widget.SummaryRowChathead{
				Title:   CapitaliseFirstCharOfString(txnWidgetSummaryRowsParams.Heading),
				BgColor: actor.GetColourCodeForActor(txnWidgetSummaryRowsParams.Heading),
			},
		}
	}

	if chatheadDeeplink != nil {
		summaryRowHeading.ChatheadDeeplink = chatheadDeeplink
	}
	summaryRows = append(summaryRows, summaryRowHeading)
	debitSummaryRow := &widget.SummaryRow{
		Heading:           debitHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text:     money.ToDisplayStringInIndianFormat(money.ParseFloat(txnWidgetSummaryRowsParams.DebitValue, money.RupeeCurrencyCode), 2, true),
			TextIcon: SentIconS3Url,
		},
	}
	creditSummaryRow := &widget.SummaryRow{
		Heading:     creditHeading,
		ContentType: widget.ContentType_TEXT,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text:     money.ToDisplayStringInIndianFormat(money.ParseFloat(txnWidgetSummaryRowsParams.CreditValue, money.RupeeCurrencyCode), 2, true),
			TextIcon: ReceivedIconS3Url,
		},
	}
	switch strings.ToLower(direction) {
	case strings.ToLower(constant.TxnDirectionDebit):
		summaryRows = append(summaryRows, debitSummaryRow)
	case strings.ToLower(constant.TxnDirectionCredit):
		summaryRows = append(summaryRows, creditSummaryRow)
	default:
		summaryRows = append(summaryRows, debitSummaryRow, creditSummaryRow)
	}
	if len(summaryRows) > 0 {
		summaryRows[len(summaryRows)-1].RowPadding = 0
		summaryRows[len(summaryRows)-1].ShouldShowDivider = false
	}
	return summaryRows
}

func GetSummaryV2FromText(summaryText, tabName string) *summary.SummaryV2 {
	return &summary.SummaryV2{
		SummaryRows: []*widget.SummaryRow{{
			ContentType: widget.ContentType_TEXT,
			Content:     &widget.SummaryRow_TextContent{TextContent: &widget.TextContent{Text: summaryText}},
		}},
		TabName: tabName,
	}
}

func GetSummaryV2FromTextAndDeeplink(summaryText, tabName string, deeplinkElements []*widget.DeepLinkElement) *summary.SummaryV2 {
	return &summary.SummaryV2{
		SummaryRows: []*widget.SummaryRow{{
			ContentType: widget.ContentType_TEXT,
			Content:     &widget.SummaryRow_TextContent{TextContent: &widget.TextContent{Text: summaryText}},
		}},
		DeepLinkElements: deeplinkElements,
		TabName:          tabName,
	}
}

func GetSummaryV2ForZeroState(summaryV2Params *summary_v2.TxnWidgetSummaryRowsParams) *summary.SummaryV2 {
	var heading, subHeading1, subHeading2, bodyText string
	if summaryV2Params.Heading != "" {
		heading = cases.Title(language.English).String(summaryV2Params.Heading)
		subHeading1 = summary_v2.NoTransactionsFoundHeading
		subHeading2 = CapitaliseFirstCharOfString(summaryV2Params.TimeToShow)
		bodyText = summary_v2.NoTxnWithActorSummaryV2BodyTextPrefix + heading + " " + summaryV2Params.TimeToShow + summary_v2.NoTxnStateSummaryV2BodyTextSuffix
	} else {
		heading = summary_v2.NoTransactionsFoundHeading
		switch summaryV2Params.TabName {
		case constant.FiTabName:
			subHeading1 = summary_v2.OnFi
		case constant.FiCCTabName:
			subHeading1 = summary_v2.OnFiCC
		default:
			subHeading1 = summary_v2.AcrossAllBanks
		}
		subHeading2 = CapitaliseFirstCharOfString(summaryV2Params.TimeToShow)
		bodyText = summary_v2.NoTxnStateSummaryV2BodyTextPrefix + summaryV2Params.TimeToShow + summary_v2.NoTxnStateSummaryV2BodyTextSuffix
	}
	return &summary.SummaryV2{
		CardTitle: summaryV2Params.CardTitle,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: NoTransactionsStateIconS3Url,
				},
				Heading:      heading,
				SubHeading_1: subHeading1,
				SubHeading_2: subHeading2,
				RowPadding:   SummaryHeadingRowPadding,
			},
			{
				ContentType: widget.ContentType_TEXT,
				Content: &widget.SummaryRow_TextContent{
					TextContent: &widget.TextContent{
						Text: bodyText,
					},
				},
			},
		},
		TabName:     summaryV2Params.TabName,
		SummaryType: summary.SummaryType_NO_TRANSACTIONS,
	}
}

func GetIfscCodeSummaryV2(ifscCodeSummaryV2Param *summary_v2.IfscCodeSummaryV2Params) *summary.SummaryV2 {
	var summaryRows []*widget.SummaryRow
	summaryRows = append(summaryRows, &widget.SummaryRow{
		ChatheadImageContent: &widget.SummaryRow_ImgUrl{
			ImgUrl: ifscCodeSummaryV2Param.GetBankLogoUrl(),
		},
		Heading:      ifscCodeSummaryV2Param.GetIfscCode(),
		SubHeading_1: ifscCodeSummaryV2Param.GetBranchName(),
		SubHeading_2: ifscCodeSummaryV2Param.GetBankName(),
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			CtaType: widget.CTAType_COPY,
			CtaData: &widget.SummaryRowRightElement_CopyCtaData{
				CopyCtaData: &widget.CopyCtaData{
					CopyContent: ifscCodeSummaryV2Param.GetIfscCode(),
					DisplayText: CtaDisplayTextCopy,
					ToastText:   CtaToastTextCopiedToClipboard,
				},
			},
			CtaIcon: CopyCtaIconS3Url,
		},
		RowPadding: SummaryHeadingRowPadding,
	})
	return &summary.SummaryV2{
		CardTitle:   summary_v2.IfscCodeCardTitle,
		SummaryRows: summaryRows,
		TabName:     ifscCodeSummaryV2Param.GetTabName(),
	}
}

func getDebitAndCreditHeading(aggregationBucket []dao_query.AggrBucket, isMerchant bool) (string, string) {
	var headingPrefix, debitHeadingSuffix, creditHeadingSuffix, finalDebitHeading, finalCreditHeading string
	if aggregationBucket != nil {
		switch aggregationBucket[0] {
		case dao_query.AggrBucketMax:
			headingPrefix = "Max"
		case dao_query.AggrBucketMin:
			headingPrefix = "Min"
		case dao_query.AggrBucketAvg:
			headingPrefix = "Average"
		default:
			headingPrefix = "Total"
		}
	}
	if isMerchant {
		debitHeadingSuffix = "spent"
		creditHeadingSuffix = "refunded"
	} else {
		debitHeadingSuffix = "paid"
		creditHeadingSuffix = "received"
	}
	if headingPrefix != "" {
		finalDebitHeading = CapitaliseFirstCharOfString(strings.Join([]string{headingPrefix, debitHeadingSuffix}, " "))
		finalCreditHeading = CapitaliseFirstCharOfString(strings.Join([]string{headingPrefix, creditHeadingSuffix}, " "))
	} else {
		finalDebitHeading = CapitaliseFirstCharOfString(debitHeadingSuffix)
		finalCreditHeading = CapitaliseFirstCharOfString(creditHeadingSuffix)
	}
	return finalDebitHeading, finalCreditHeading
}

//nolint:funlen
func BuildSummaryRowsForAllBanksTab(txnWidgetSummaryRowsParams *summary_v2.TxnWidgetSummaryRowsParams) []*widget.SummaryRow {
	var summaryRows []*widget.SummaryRow
	var direction string
	if len(txnWidgetSummaryRowsParams.TxnDirection) > 0 {
		direction = txnWidgetSummaryRowsParams.TxnDirection[0]
	}
	debitHeading, creditHeading := getDebitAndCreditHeading(txnWidgetSummaryRowsParams.AggregationBucket, txnWidgetSummaryRowsParams.IsMerchant)
	summaryHeadingRow := &widget.SummaryRow{
		Heading:           CapitaliseFirstCharOfString(txnWidgetSummaryRowsParams.Heading),
		SubHeading_1:      GetTxnsCountStr(txnWidgetSummaryRowsParams.TxnCount),
		SubHeading_2:      CapitaliseFirstCharOfString(txnWidgetSummaryRowsParams.TimeToShow),
		RowPadding:        SummaryHeadingRowPadding,
		ShouldShowDivider: true,
	}
	switch {
	case summaryHeadingRow.Heading == "":
		summaryHeadingRow.Heading = GetDefaultHeadingForTab(txnWidgetSummaryRowsParams.TabName)
		summaryHeadingRow.ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: DefaultIconForSummaryV2,
		}
	case txnWidgetSummaryRowsParams.ChatHead != nil:
		summaryHeadingRow.ChatheadImageContent = &widget.SummaryRow_Chathead{
			Chathead: &widget.SummaryRowChathead{
				Title:   txnWidgetSummaryRowsParams.ChatHead.GetTitle(),
				ImgUrl:  txnWidgetSummaryRowsParams.ChatHead.GetImgUrl(),
				BgColor: txnWidgetSummaryRowsParams.ChatHead.GetBgColor(),
			},
		}
	case txnWidgetSummaryRowsParams.ImageUrl != "":
		summaryHeadingRow.ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: txnWidgetSummaryRowsParams.ImageUrl,
		}
	}
	summaryRows = append(summaryRows, summaryHeadingRow)
	debitSummaryRow := &widget.SummaryRow{
		Heading:           debitHeading,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text:     money.ToDisplayStringInIndianFormat(money.ParseFloat(txnWidgetSummaryRowsParams.DebitValue, money.RupeeCurrencyCode), 2, true),
			TextIcon: SentIconS3Url,
		},
	}
	creditSummaryRow := &widget.SummaryRow{
		Heading:     creditHeading,
		ContentType: widget.ContentType_TEXT,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text:     money.ToDisplayStringInIndianFormat(money.ParseFloat(txnWidgetSummaryRowsParams.CreditValue, money.RupeeCurrencyCode), 2, true),
			TextIcon: ReceivedIconS3Url,
		},
	}
	switch strings.ToLower(direction) {
	case strings.ToLower(constant.TxnDirectionDebit):
		summaryRows = append(summaryRows, debitSummaryRow)
	case strings.ToLower(constant.TxnDirectionCredit):
		summaryRows = append(summaryRows, creditSummaryRow)
	default:
		summaryRows = append(summaryRows, debitSummaryRow, creditSummaryRow)
	}
	if len(summaryRows) > 0 {
		summaryRows[len(summaryRows)-1].RowPadding = 0
		summaryRows[len(summaryRows)-1].ShouldShowDivider = false
	}
	return summaryRows
}

func GetSummaryV2ForAccountInfo(accountNumber string, ifscCode string) *summary.SummaryV2 {
	return &summary.SummaryV2{
		CardTitle: summary_v2.AccountDetailsCardTitle,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: summary_v2.FiBankLogoURL,
				},
				Heading:      accountNumber,
				SubHeading_1: fmt.Sprintf(summary_v2.FiBankAndIfscCode, ifscCode),
				SubHeading_2: summary_v2.FederalNeobank,
				SummaryRowRightElement: &widget.SummaryRowRightElement{
					CtaType: widget.CTAType_COPY,
					CtaData: &widget.SummaryRowRightElement_CopyCtaData{
						CopyCtaData: &widget.CopyCtaData{
							CopyContent: accountNumber,
							DisplayText: CtaDisplayTextCopy,
							ToastText:   CtaToastTextCopiedToClipboard,
						},
					},
					CtaIcon: CopyCtaIconS3Url,
				},
				RowPadding: SummaryHeadingRowPadding,
			},
		},
		TabName: constant.FiTabName,
	}
}

//nolint:dupl
func GetMobileNumberPaymentWidget(paymentWidgetParams summary_v2.PaymentWidgetParams) *summary.SummaryV2 {
	summaryResp := &summary.SummaryV2{
		CardTitle: summary_v2.TransactCardTitle,
		TabName:   constant.FiTabName,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_Chathead{
					Chathead: &widget.SummaryRowChathead{
						Title:   CapitaliseFirstCharOfString(paymentWidgetParams.GetSecondaryActorName()),
						BgColor: actor.GetColourCodeForActor(paymentWidgetParams.GetSecondaryActorId()),
					},
				},
				Heading:      paymentWidgetParams.GetSecondaryActorName(),
				SubHeading_1: paymentWidgetParams.GetMobileNumber(),
				RowPadding:   SummaryHeadingRowPadding,
			},
		},
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
				Text:     constant.StartPaymentCtaText,
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_ENTER_AMOUNT_SCREEN,
					ScreenOptions: &deeplink.Deeplink_EnterAmountScreenOptions{
						EnterAmountScreenOptions: &timeline.EnterAmountScreenOptions{
							Timeline: &timeline.EnterAmountScreenOptions_Timeline{
								Id: paymentWidgetParams.GetTimelineId(),
								Chathead: &timeline.EnterAmountScreenOptions_Timeline_Chathead{
									SecondActorId:   paymentWidgetParams.GetSecondaryActorId(),
									SecondActorName: paymentWidgetParams.GetSecondaryActorName(),
									TimelineId:      paymentWidgetParams.GetTimelineId(),
									ColorCode:       ui.ColorSignatureInk,
								},
								IsPayButtonEnabled:     true,
								IsRequestButtonEnabled: false,
								TimelineState:          timeline.TransactionTimelineState_DEFAULT,
							},
							TransactionType: timeline.TransactionType_TRANSFER,
							EnterAmountParams: &timeline.EnterAmountScreenOptions_EnterAmountParams{
								IsAmountEditable: true,
								MinAmount: types.GetFromBeMoney(&moneyPb.Money{
									CurrencyCode: "INR",
									Units:        1,
								}),
							},
							UiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ASK_FI,
						},
					},
				},
			},
		},
		SummaryType: summary.SummaryType_TRANSACT,
	}
	if len(paymentWidgetParams.GetSecondaryImageUrl()) > 0 {
		summaryResp.GetSummaryRows()[0].ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: paymentWidgetParams.GetSecondaryImageUrl(),
		}
	}
	return summaryResp
}

func BuildRewardWidget(cashReward string, fiCoins int32) *summary.SummaryV2 {
	return &summary.SummaryV2{
		CardTitle: "REWARDS",
		TabName:   constant.FiTabName,
		SummaryRows: []*widget.SummaryRow{
			{
				Heading: "Fi-Coins",
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: "https://epifi-icons.pointz.in/quick-link-icons/reward_summary_rupee.png",
				},
				SummaryRowRightElement: &widget.SummaryRowRightElement{
					Text: money.ToDisplayStringInIndianFormatFromFloatValue(float64(fiCoins), 0),
				},
				RowPadding:        SummaryHeadingRowPadding,
				ShouldShowDivider: true,
			}, {
				Heading: "Cash & Deposits",
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: "https://epifi-icons.pointz.in/quick-link-icons/reward_coins_askfi.png",
				},
				SummaryRowRightElement: &widget.SummaryRowRightElement{
					Text: cashReward,
				},
				RowPadding:        SummaryNonHeadingRowPadding,
				ShouldShowDivider: false,
			},
		},
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_MY_REWARDS_SCREEN,
				},
				Text:     "My Rewards",
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
	}
}

//nolint:dupl
func GetNoTransactionsActorPaymentWidget(paymentWidgetParams summary_v2.PaymentWidgetParams) *summary.SummaryV2 {
	summaryResp := &summary.SummaryV2{
		CardTitle: summary_v2.TransactCardTitle,
		TabName:   constant.FiTabName,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_Chathead{
					Chathead: &widget.SummaryRowChathead{
						Title:   CapitaliseFirstCharOfString(paymentWidgetParams.GetSecondaryActorName()),
						BgColor: actor.GetColourCodeForActor(paymentWidgetParams.GetSecondaryActorId()),
					},
				},
				Heading:    paymentWidgetParams.GetSecondaryActorName(),
				RowPadding: SummaryHeadingRowPadding,
			},
		},
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				DeeplinkElementType: widget.DeeplinkElementType_DEEPLINK_ELEMENT_TYPE_PAY,
				CtaTheme:            widget.CtaDisplayTheme_PRIMARY,
				CtaType:             widget.CTAType_DEEPLINK,
				Text:                constant.StartPaymentCtaText,
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_TIMELINE,
					ScreenOptions: &deeplink.Deeplink_TimelineScreenOptions{
						TimelineScreenOptions: &deeplink.TimelineScreenOptions{
							TimelineId: paymentWidgetParams.GetTimelineId(),
						},
					},
				},
			},
		},
		SummaryType: summary.SummaryType_TRANSACT,
	}
	if len(paymentWidgetParams.GetSecondaryImageUrl()) > 0 {
		summaryResp.GetSummaryRows()[0].ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: paymentWidgetParams.GetSecondaryImageUrl(),
		}
	}
	return summaryResp
}

//nolint:dupl
func GetUPIPaymentWidget(paymentWidgetParams summary_v2.PaymentWidgetParams) *summary.SummaryV2 {
	summaryResp := &summary.SummaryV2{
		CardTitle: summary_v2.TransactCardTitle,
		TabName:   constant.FiTabName,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_Chathead{
					Chathead: &widget.SummaryRowChathead{
						Title:   CapitaliseFirstCharOfString(paymentWidgetParams.GetSecondaryActorName()),
						BgColor: actor.GetColourCodeForActor(paymentWidgetParams.GetSecondaryActorId()),
					},
				},
				Heading:      paymentWidgetParams.GetSecondaryActorName(),
				SubHeading_1: paymentWidgetParams.GetVpa(),
				RowPadding:   SummaryHeadingRowPadding,
			},
		},
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
				Text:     constant.StartPaymentCtaText,
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_ENTER_AMOUNT_SCREEN,
					ScreenOptions: &deeplink.Deeplink_EnterAmountScreenOptions{
						EnterAmountScreenOptions: &timeline.EnterAmountScreenOptions{
							Timeline: &timeline.EnterAmountScreenOptions_Timeline{
								Id: paymentWidgetParams.GetTimelineId(),
								Chathead: &timeline.EnterAmountScreenOptions_Timeline_Chathead{
									SecondActorId:   paymentWidgetParams.GetSecondaryActorId(),
									SecondActorName: paymentWidgetParams.GetSecondaryActorName(),
									TimelineId:      paymentWidgetParams.GetTimelineId(),
									ColorCode:       ui.ColorSignatureInk,
								},
								IsPayButtonEnabled:     true,
								IsRequestButtonEnabled: false,
								TimelineState:          timeline.TransactionTimelineState_DEFAULT,
							},
							TransactionType: timeline.TransactionType_TRANSFER,
							EnterAmountParams: &timeline.EnterAmountScreenOptions_EnterAmountParams{
								IsAmountEditable: true,
								MinAmount: types.GetFromBeMoney(&moneyPb.Money{
									CurrencyCode: "INR",
									Units:        1,
								}),
							},
							UiEntryPoint: timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ASK_FI,
						},
					},
				},
			},
		},
		SummaryType: summary.SummaryType_TRANSACT,
	}
	if len(paymentWidgetParams.GetSecondaryImageUrl()) > 0 {
		summaryResp.GetSummaryRows()[0].ChatheadImageContent = &widget.SummaryRow_ImgUrl{
			ImgUrl: paymentWidgetParams.GetSecondaryImageUrl(),
		}
	}
	return summaryResp
}

//nolint:dupl
func GetJumpSummaryV2ForZeroState() []*searchPb.SearchResultUnit {
	summaryRows := []*widget.SummaryRow{
		{
			ChatheadImageContent: &widget.SummaryRow_ImgUrl{
				ImgUrl: JumpS3IconUrl,
			},
			Heading:    summary_v2.JumpZeroStateHeading,
			RowPadding: SummaryHeadingRowPadding,
		},
		{
			ContentType: widget.ContentType_TEXT,
			Content: &widget.SummaryRow_TextContent{
				TextContent: &widget.TextContent{
					Text: summary_v2.JumpZeroStateSummaryV2BodyText,
				},
			},
			RowPadding: SummaryNonHeadingRowPadding,
		},
	}
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle:   summary_v2.JumpCardTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_P2P_INVESTMENT_DASHBOARD_SCREEN,
				},
				Text:     summary_v2.InvestInJump,
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

func BuildMutualFundWidget(ctx context.Context, fundInfo *catalog.FilteredMutualFundInfo, investmentDetails *catalog.GetInvestmentSummaryForMfsResponse) *summary.SummaryV2 {
	var summaryRows []*widget.SummaryRow
	if investmentDetails.GetInvestmentSummaries()[fundInfo.GetMutualFundId()].GetInvested() {
		summaryRows = BuildInvestedMutualFundSummaryRows(ctx, fundInfo, investmentDetails)
	} else {
		summaryRows = BuildNonInvestedMutualFundSummaryRows(ctx, fundInfo)
	}
	return &summary.SummaryV2{
		CardTitle:   summary_v2.MutualFundSearchTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_MUTUAL_FUND_DETAILS_SCREEN,
					ScreenOptions: &deeplink.Deeplink_MutualFundDetailsScreenOptions{
						MutualFundDetailsScreenOptions: &deeplink.MutualFundDetailsScreenOptions{
							MutualFundId: fundInfo.GetMutualFundId(),
							EntryPoint:   clientstates.MutualFundListEntryPoint_MF_LIST_ENTRY_POINT_UNSPECIFIED,
						},
					},
				},
				Text:     summary_v2.GoToMutualFunds,
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}
}

func BuildInvestedMutualFundSummaryRows(ctx context.Context, fundInfo *catalog.FilteredMutualFundInfo, investmentDetails *catalog.GetInvestmentSummaryForMfsResponse) []*widget.SummaryRow {
	var returnsS3IconUrl string
	var summaryRows []*widget.SummaryRow
	var imgURL string

	imgURL = investment.IconsForAmc[fundInfo.GetAmc()]
	if len(imgURL) == 0 {
		imgURL = DefaultIconForSummaryV2
	}
	mutualFundDisplayName := fundInfo.GetNameData().GetDisplayName()
	mutualFundOptionType := fundInfo.GetOptionType().String()
	mutualFundAssetClass := fundInfo.GetAssetClass().String()

	if investmentDetails.GetInvestmentSummaries()[fundInfo.GetMutualFundId()].GetGrowthPercentage() >= 0 {
		returnsS3IconUrl = ReturnsUpIconS3Url
	} else {
		returnsS3IconUrl = ReturnsDownIconS3Url
	}

	summaryRowHeading := &widget.SummaryRow{
		ChatheadImageContent: &widget.SummaryRow_ImgUrl{
			ImgUrl: imgURL,
		},
		Heading:           mutualFundDisplayName,
		SubHeading_1:      mutualFundOptionType,
		SubHeading_2:      mutualFundAssetClass,
		RowPadding:        SummaryHeadingRowPadding,
		ShouldShowDivider: true,
	}

	investedValueRow := &widget.SummaryRow{
		Heading:           Invested,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: money.ToDisplayStringInIndianFormat(investmentDetails.GetInvestmentSummaries()[fundInfo.GetMutualFundId()].GetTotalInvestedValue(), 2, true),
		},
	}

	currentValueRow := &widget.SummaryRow{
		Heading:           Current,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: money.ToDisplayStringInIndianFormat(investmentDetails.GetInvestmentSummaries()[fundInfo.GetMutualFundId()].GetCurrentValue(), 2, true),
		},
	}

	returnsValueRow := &widget.SummaryRow{
		Heading:     Returns,
		ContentType: widget.ContentType_TEXT,
		RowPadding:  SummaryNonHeadingRowPadding,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text:     fmt.Sprintf("%.2f %s", investmentDetails.GetInvestmentSummaries()[fundInfo.GetMutualFundId()].GetGrowthPercentage(), "%"),
			TextIcon: returnsS3IconUrl,
		},
	}
	summaryRows = append(summaryRows, summaryRowHeading, investedValueRow, currentValueRow, returnsValueRow)
	return summaryRows
}

func BuildNonInvestedMutualFundSummaryRows(ctx context.Context, fundInfo *catalog.FilteredMutualFundInfo) []*widget.SummaryRow {
	mutualFundDisplayName := fundInfo.GetNameData().GetDisplayName()
	mutualFundNav := money.ToDisplayStringInIndianFormat(fundInfo.GetNav(), 2, true)
	mutualFundOptionType := fundInfo.GetOptionType().String()
	mutualFundAssetClass := fundInfo.GetAssetClass().String()
	avg1yReturn := fundInfo.GetReturns().GetAvgFundReturnOneYear()
	var imgURL string

	imgURL = investment.IconsForAmc[fundInfo.GetAmc()]
	if len(imgURL) == 0 {
		imgURL = DefaultIconForSummaryV2
	}

	summaryRowHeading := &widget.SummaryRow{
		ChatheadImageContent: &widget.SummaryRow_ImgUrl{
			ImgUrl: imgURL,
		},
		Heading:           mutualFundDisplayName,
		SubHeading_1:      mutualFundOptionType,
		SubHeading_2:      mutualFundAssetClass,
		RowPadding:        SummaryHeadingRowPadding,
		ShouldShowDivider: true,
	}

	navRow := &widget.SummaryRow{
		Heading:           NavTitle,
		ContentType:       widget.ContentType_TEXT,
		RowPadding:        SummaryNonHeadingRowPadding,
		ShouldShowDivider: true,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: mutualFundNav,
		},
	}

	avg1YReturnRow := &widget.SummaryRow{
		Heading:     Avg1YReturn,
		ContentType: widget.ContentType_TEXT,
		RowPadding:  SummaryNonHeadingRowPadding,
		SummaryRowRightElement: &widget.SummaryRowRightElement{
			Text: fmt.Sprintf("%.2f %s", avg1yReturn, "%"),
		},
	}

	return []*widget.SummaryRow{summaryRowHeading, navRow, avg1YReturnRow}
}

func GetJumpSummaryV2(summaryParams *summary_v2.JumpSummaryV2Params) []*searchPb.SearchResultUnit {
	var subHeading1, returnsS3IconUrl string
	if summaryParams.GetCountOfInvestments() > 1 {
		subHeading1 = fmt.Sprintf("%d INVESTMENTS", summaryParams.GetCountOfInvestments())
	} else {
		subHeading1 = "1 INVESTMENT"
	}
	if summaryParams.GetTotalReturns() >= 0 {
		returnsS3IconUrl = ReturnsUpIconS3Url
	} else {
		returnsS3IconUrl = ReturnsDownIconS3Url
	}
	summaryRows := []*widget.SummaryRow{
		{
			ChatheadImageContent: &widget.SummaryRow_ImgUrl{
				ImgUrl: JumpS3IconUrl,
			},
			Heading:      summary_v2.JumpHeading,
			SubHeading_1: subHeading1,
			SummaryRowRightElement: &widget.SummaryRowRightElement{
				Text: money.ToDisplayStringInIndianFormat(money.ParseFloat(summaryParams.GetCurrentValue(), money.RupeeCurrencyCode), 2, true),
			},
			ShouldShowDivider: true,
			RowPadding:        SummaryHeadingRowPadding,
		},
		{
			Heading: summary_v2.JumpInvestedHeading,
			SummaryRowRightElement: &widget.SummaryRowRightElement{
				Text: money.ToDisplayStringInIndianFormat(money.ParseFloat(summaryParams.GetTotalInvested(), money.RupeeCurrencyCode), 2, true),
			},
			ShouldShowDivider: true,
			RowPadding:        SummaryNonHeadingRowPadding,
		},
		{
			Heading: summary_v2.JumpReturnsHeading,
			SummaryRowRightElement: &widget.SummaryRowRightElement{
				Text:     money.ToDisplayStringInIndianFormat(money.ParseFloat(summaryParams.GetTotalReturns(), money.RupeeCurrencyCode), 2, true),
				TextIcon: returnsS3IconUrl,
			},
			RowPadding: SummaryNonHeadingRowPadding,
		},
	}
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle:   summary_v2.JumpCardTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_P2P_INVESTMENT_DASHBOARD_SCREEN,
				},
				Text:     summary_v2.GoToJump,
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

//nolint:dupl
func GetMutualFundSummaryV2ForZeroState() []*searchPb.SearchResultUnit {
	summaryRows := []*widget.SummaryRow{
		{
			ChatheadImageContent: &widget.SummaryRow_ImgUrl{
				ImgUrl: MutualFundS3IconUrl,
			},
			Heading:    summary_v2.MutualFundZeroStateHeading,
			RowPadding: SummaryHeadingRowPadding,
		},
		{
			ContentType: widget.ContentType_TEXT,
			Content: &widget.SummaryRow_TextContent{
				TextContent: &widget.TextContent{
					Text: summary_v2.MutualFundZeroStateSummaryV2BodyText,
				},
			},
			RowPadding: SummaryNonHeadingRowPadding,
		},
	}
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle:   summary_v2.MutualFundCardTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_MUTUAL_FUND_INVESTMENT_DIGEST_SCREEN,
				},
				Text:     summary_v2.InvestInMutualFunds,
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

func GetMutualFundSummaryV2(summaryV2Params *summary_v2.MutualFundSummaryV2Params) []*searchPb.SearchResultUnit {
	var subHeading1, returnsS3IconUrl string
	if summaryV2Params.GetCountOfInvestments() > 1 {
		subHeading1 = fmt.Sprintf("%d FUNDS", summaryV2Params.GetCountOfInvestments())
	} else {
		subHeading1 = "1 FUND"
	}
	if summaryV2Params.GetGrowthPercentage() >= 0 {
		returnsS3IconUrl = ReturnsUpIconS3Url
	} else {
		returnsS3IconUrl = ReturnsDownIconS3Url
	}
	summaryRows := []*widget.SummaryRow{
		{
			ChatheadImageContent: &widget.SummaryRow_ImgUrl{
				ImgUrl: MutualFundS3IconUrl,
			},
			Heading:      summary_v2.MutualFundHeading,
			SubHeading_1: subHeading1,
			SummaryRowRightElement: &widget.SummaryRowRightElement{
				Text: money.ToDisplayStringInIndianFormat(money.ParseFloat(summaryV2Params.GetCurrentValue(), money.RupeeCurrencyCode), 2, true),
			},
			ShouldShowDivider: true,
			RowPadding:        SummaryHeadingRowPadding,
		},
		{
			Heading: summary_v2.MutualFundInvestedHeading,
			SummaryRowRightElement: &widget.SummaryRowRightElement{
				Text: money.ToDisplayStringInIndianFormat(money.ParseFloat(summaryV2Params.GetTotalInvested(), money.RupeeCurrencyCode), 2, true),
			},
			ShouldShowDivider: true,
			RowPadding:        SummaryNonHeadingRowPadding,
		},
		{
			Heading: summary_v2.MutualFundReturnsHeading,
			SummaryRowRightElement: &widget.SummaryRowRightElement{
				Text:     money.ToDisplayStringInIndianFormat(money.ParseFloat(summaryV2Params.GetGrowthPercentage(), money.RupeeCurrencyCode), 2, false) + "%",
				TextIcon: returnsS3IconUrl,
			},
			RowPadding: SummaryNonHeadingRowPadding,
		},
	}
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle:   summary_v2.MutualFundCardTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_MUTUAL_FUND_INVESTMENT_DIGEST_SCREEN,
				},
				Text:     summary_v2.GoToMutualFunds,
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

func GetSingleDepositSummaryV2(summaryV2Params []*summary_v2.DepositSummaryV2Params, cardTitle string, deepLink *deeplink.Deeplink) []*searchPb.SearchResultUnit {
	var iconUrl string
	if summaryV2Params == nil {
		return nil
	}
	if summaryV2Params[0].GetDepositIconUrl() == "" {
		if cardTitle == summary_v2.SmartDepositCardTitle {
			iconUrl = SmartDepositS3IconUrl
		} else {
			iconUrl = FixedDepositS3IconUrl
		}
	} else {
		iconUrl = summaryV2Params[0].GetDepositIconUrl()
	}
	summaryRows := []*widget.SummaryRow{
		{
			ChatheadImageContent: &widget.SummaryRow_ImgUrl{
				ImgUrl: iconUrl,
			},
			Heading:      summaryV2Params[0].GetDepositName(),
			SubHeading_1: summaryV2Params[0].GetAccountNumber(),
			SubHeading_2: "MATURES ON " + strings.ToUpper(summaryV2Params[0].GetMaturityDate()),
			SummaryRowRightElement: &widget.SummaryRowRightElement{
				Text: money.ToDisplayStringInIndianFormat(money.ParseFloat(summaryV2Params[0].GetDepositBalance(), money.RupeeCurrencyCode), 2, true),
			},
			RowPadding: SummaryNonHeadingRowPadding,
		},
	}
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle:   cardTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link:     deepLink,
				Text:     summary_v2.SeeDetails,
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

func GetMultipleDepositSummaryV2(summaryV2Params []*summary_v2.DepositSummaryV2Params, cardTitle, headingIconUrl string, deepLink *deeplink.Deeplink) []*searchPb.SearchResultUnit {
	var heading string
	if summaryV2Params == nil {
		return nil
	}
	if cardTitle == summary_v2.SmartDepositCardTitle {
		heading = fmt.Sprintf("%d Smart Deposits", len(summaryV2Params))
	} else {
		heading = fmt.Sprintf("%d Fixed Deposits", len(summaryV2Params))
	}
	summaryRows := []*widget.SummaryRow{
		{
			ChatheadImageContent: &widget.SummaryRow_ImgUrl{
				ImgUrl: headingIconUrl,
			},
			Heading:           heading,
			ShouldShowDivider: true,
			RowPadding:        SummaryHeadingRowPadding,
		},
	}
	for idx := range summaryV2Params {
		summaryRows = append(summaryRows, &widget.SummaryRow{
			Heading: summaryV2Params[idx].GetDepositName(),
			SummaryRowRightElement: &widget.SummaryRowRightElement{
				Text: money.ToDisplayStringInIndianFormat(money.ParseFloat(summaryV2Params[idx].GetDepositBalance(), money.RupeeCurrencyCode), 2, true),
			},
			ShouldShowDivider: true,
			RowPadding:        SummaryNonHeadingRowPadding,
		})
	}
	summaryRows[len(summaryRows)-1].ShouldShowDivider = false
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle:   cardTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link:     deepLink,
				Text:     summary_v2.SeeDetails,
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

func GetDepositSummaryV2ForZeroState(heading, headingIconUrl, cardTitle, ctaString, bodyText string, deepLink *deeplink.Deeplink) []*searchPb.SearchResultUnit {
	summaryRows := []*widget.SummaryRow{
		{
			ChatheadImageContent: &widget.SummaryRow_ImgUrl{
				ImgUrl: headingIconUrl,
			},
			Heading:    heading,
			RowPadding: SummaryHeadingRowPadding,
		},
		{
			ContentType: widget.ContentType_TEXT,
			Content: &widget.SummaryRow_TextContent{
				TextContent: &widget.TextContent{
					Text: bodyText,
				},
			},
			RowPadding: SummaryNonHeadingRowPadding,
		},
	}
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle:   cardTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link:     deepLink,
				Text:     ctaString,
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

//nolint:dupl
func GetSalaryProgramSummaryV2ForZeroState() []*searchPb.SearchResultUnit {
	summaryRows := []*widget.SummaryRow{
		{
			ChatheadImageContent: &widget.SummaryRow_ImgUrl{
				ImgUrl: SalaryProgramS3IconUrl,
			},
			Heading:    summary_v2.SalaryProgramZeroStateHeading,
			RowPadding: SummaryHeadingRowPadding,
		},
		{
			ContentType: widget.ContentType_TEXT,
			Content: &widget.SummaryRow_TextContent{
				TextContent: &widget.TextContent{
					Text: summary_v2.SalaryProgramZeroStateSummaryV2BodyText,
				},
			},
			RowPadding: SummaryNonHeadingRowPadding,
		},
	}
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle:   summary_v2.SalaryProgramCardTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_SALARY_PROGRAM_INTRO_SCREEN,
				},
				Text:     summary_v2.GetSalaryAccount,
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

func GetSalaryProgramSummaryV2(salaryProgramActiveTill, employerName string) []*searchPb.SearchResultUnit {
	summaryRows := []*widget.SummaryRow{
		{
			ChatheadImageContent: &widget.SummaryRow_ImgUrl{
				ImgUrl: SalaryProgramS3IconUrl,
			},
			Heading:      summary_v2.SalaryProgramActiveStateHeading,
			SubHeading_1: employerName,
			SubHeading_2: "ACTIVE TILL " + salaryProgramActiveTill,
			RowPadding:   SummaryNonHeadingRowPadding,
		},
	}
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle:   summary_v2.SalaryProgramCardTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
				},
				Text:     summary_v2.SeeSalaryBenefits,
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

func GetDebitCardOffersNotFoundSummaryV2(merchantName string) []*searchPb.SearchResultUnit {
	var heading, subHeading1 string
	if merchantName == "" {
		heading = summary_v2.DebitCardOffersNotFoundHeading
	} else {
		heading = merchantName
		subHeading1 = summary_v2.DebitCardOffersNotFoundHeading
	}
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle: summary_v2.DebitCardOffersCardTitle,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: DefaultIconForDebitCardOffers,
				},
				Heading:      heading,
				SubHeading_1: subHeading1,
				RowPadding:   SummaryNonHeadingRowPadding,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

//nolint:funlen
func GetDebitCardOffersSummaryV2(summaryV2Params *summary_v2.CardOffersSummaryV2Params) []*searchPb.SearchResultUnit {
	var cardTitle string
	summaryRows := []*widget.SummaryRow{
		{
			ChatheadImageContent: &widget.SummaryRow_ImgUrl{
				ImgUrl: summaryV2Params.GetIconUrl(),
			},
			Heading:    summaryV2Params.GetHeading(),
			RowPadding: SummaryHeadingRowPadding,
		},
	}
	for _, currentOffer := range summaryV2Params.GetOfferParams() {
		var (
			offerCodeContent *widget.SummaryRow_OfferCodeContent
			contentType      widget.ContentType
		)
		if currentOffer.GetOfferCode() != "" {
			contentType = widget.ContentType_OFFER_CODE
			offerCodeContent = &widget.SummaryRow_OfferCodeContent{
				OfferCodeContent: &widget.OfferCodeContent{
					Heading:         OfferCodeContentElementHeading,
					OfferCode:       currentOffer.GetOfferCode(),
					BackgroundColor: BgColorForOfferCodeContentElement,
					CtaType:         widget.CTAType_COPY,
					CopyCtaData: &widget.CopyCtaData{
						CopyContent: currentOffer.GetOfferCode(),
						DisplayText: CtaDisplayTextCopy,
						ToastText:   CtaToastTextCopiedToClipboard,
					},
				},
			}
		}
		summaryRows = append(summaryRows, &widget.SummaryRow{
			SubHeading_1:      currentOffer.GetOfferDescription(),
			ContentType:       contentType,
			Content:           offerCodeContent,
			ShouldShowDivider: true,
			RowPadding:        SummaryNonHeadingRowPadding,
		})
	}
	summaryRows[len(summaryRows)-1].ShouldShowDivider = false
	if summaryV2Params.GetCountOfOffers() > 1 {
		cardTitle = summary_v2.DebitCardOffersCardTitle + fmt.Sprintf(" • %d OFFERS", summaryV2Params.GetCountOfOffers())
	} else {
		cardTitle = summary_v2.DebitCardOffersCardTitle + fmt.Sprintf(" • %d OFFER", summaryV2Params.GetCountOfOffers())
	}
	offersDeeplink := &deeplink.Deeplink{
		Screen: deeplink.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN,
	}
	if (summaryV2Params.GetDeviceInfo().Platform == commontypes.Platform_ANDROID && summaryV2Params.GetDeviceInfo().AppVersion >= 230) ||
		(summaryV2Params.GetDeviceInfo().Platform == commontypes.Platform_IOS && summaryV2Params.GetDeviceInfo().AppVersion >= 332) {
		offersDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_CARD_OFFERS_CATALOG_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CardOffersCatalogScreenOptions{
				CardOffersCatalogScreenOptions: &deeplink.CardOffersCatalogScreenOptions{
					CardType: deeplink.CardOffersCatalogScreenOptions_DEBIT_CARD,
				},
			},
		}
	}
	return []*searchPb.SearchResultUnit{GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle:   cardTitle,
		SummaryRows: summaryRows,
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link:     offersDeeplink,
				Text:     summary_v2.AllOffers,
				CtaTheme: widget.CtaDisplayTheme_SECONDARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)}
}

func GetSummaryWidgetForProcessingLoan() *searchPb.SearchResultUnit {
	return GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle: summary_v2.PreApprovedLoanCardTitle,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: summary_v2.ProcessingLoanIcon,
				},
				Heading:    summary_v2.PreApprovedLoanProcessingHeading,
				RowPadding: SummaryHeadingRowPadding,
			},
			{
				ContentType: widget.ContentType_TEXT,
				Content: &widget.SummaryRow_TextContent{
					TextContent: &widget.TextContent{
						Text: summary_v2.PreApprovedLoanProcessingBodyText,
					},
				},
				RowPadding: SummaryNonHeadingRowPadding,
			},
		},
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link: &deeplink.Deeplink{
					Screen: deeplink.Screen_HELP_MAIN,
				},
				Text:     "Get help",
				CtaTheme: widget.CtaDisplayTheme_SECONDARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
			{
				Link:     getPlLandingScreenDeeplinkWithEntryPointData(),
				Text:     "View your loan",
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)
}

func GetSummaryWidgetForLoanOffer(amount string) *searchPb.SearchResultUnit {
	return GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle: summary_v2.PreApprovedLoanCardTitle,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: summary_v2.LoanSummaryIcon,
				},
				Heading:    summary_v2.PreApprovedLoanHeading,
				RowPadding: SummaryHeadingRowPadding,
			},
			{
				ContentType: widget.ContentType_IMAGE,
				Content: &widget.SummaryRow_ImageContent{
					ImageContent: &widget.ImageContent{
						ImgUrl: summary_v2.LoanOfferBodyImage,
					},
				},
				RowPadding:        SummaryNonHeadingRowPadding,
				ShouldShowDivider: true,
			},
			{
				Heading:    summary_v2.PreApprovedLoanEligibleAmountHeading,
				RowPadding: SummaryNonHeadingRowPadding,
				SummaryRowRightElement: &widget.SummaryRowRightElement{
					Text: amount,
				},
			},
		},
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link:     getPlLandingScreenDeeplinkWithEntryPointData(),
				Text:     "See Instant Loan",
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)
}

func GetSummaryWidgetForActiveLoan(summaryParams *summary_v2.LoanSummaryWidgetParams) *searchPb.SearchResultUnit {
	return GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle: summary_v2.PreApprovedLoanCardTitle,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: summary_v2.LoanSummaryIcon,
				},
				Heading:           summaryParams.GetLoanName(),
				SubHeading_1:      "NEXT DUE " + summaryParams.GetNextEmiDate(),
				SubHeading_2:      "ACTIVE TILL " + summaryParams.GetActiveTill(),
				RowPadding:        SummaryHeadingRowPadding,
				ShouldShowDivider: true,
			},
			{
				Heading: summary_v2.PreApprovedLoanAmountHeading,
				SummaryRowRightElement: &widget.SummaryRowRightElement{
					Text: summaryParams.GetLoanAmount(),
				},
				RowPadding:        SummaryNonHeadingRowPadding,
				ShouldShowDivider: true,
			},
			{
				Heading: summary_v2.PreApprovedLoanEmiAmountHeading,
				SummaryRowRightElement: &widget.SummaryRowRightElement{
					Text:     summaryParams.GetNextEmiAmount(),
					TextIcon: LoanEmiIconS3Url,
				},
				RowPadding: SummaryNonHeadingRowPadding,
			},
		},
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link:     getPlLandingScreenDeeplinkWithEntryPointData(),
				Text:     "View your loan",
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)
}

func GetSummaryWidgetForClosedLoan(summaryParams *summary_v2.LoanSummaryWidgetParams) *searchPb.SearchResultUnit {
	return GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle: summary_v2.PreApprovedLoanCardTitle,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: summary_v2.LoanSummaryIcon,
				},
				Heading:           summaryParams.GetLoanName(),
				SubHeading_1:      "CLOSED",
				SubHeading_2:      "WAS ACTIVE TILL " + summaryParams.GetActiveTill(),
				RowPadding:        SummaryHeadingRowPadding,
				ShouldShowDivider: true,
			},
			{
				Heading: summary_v2.PreApprovedLoanAmountHeading,
				SummaryRowRightElement: &widget.SummaryRowRightElement{
					Text: summaryParams.GetLoanAmount(),
				},
				RowPadding:        SummaryNonHeadingRowPadding,
				ShouldShowDivider: true,
			},
			{
				Heading: summary_v2.PreApprovedLoanTotalPaidHeading,
				SummaryRowRightElement: &widget.SummaryRowRightElement{
					Text:     summaryParams.GetTotalPayableAmount(),
					TextIcon: LoanEmiIconS3Url,
				},
				RowPadding: SummaryNonHeadingRowPadding,
			},
		},
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link:     getPlLandingScreenDeeplinkWithEntryPointData(),
				Text:     "View your loan",
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)
}

func GetSummaryWidgetForOutstandingLoanAmount(summaryParams *summary_v2.LoanSummaryWidgetParams) *searchPb.SearchResultUnit {
	return GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle: summary_v2.PreApprovedLoanCardTitle,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: summary_v2.LoanSummaryIcon,
				},
				Heading:           summaryParams.GetLoanName(),
				SubHeading_1:      "LAST PAID ON " + summaryParams.GetPrevPaidEmiDate(),
				SubHeading_2:      "ACTIVE TILL " + summaryParams.GetActiveTill(),
				RowPadding:        SummaryHeadingRowPadding,
				ShouldShowDivider: true,
			},
			{
				Heading: summary_v2.PreApprovedLoanOutstandingAmountHeading,
				SummaryRowRightElement: &widget.SummaryRowRightElement{
					Text: summaryParams.GetOutstandingLoanAmount(),
				},
				RowPadding: SummaryNonHeadingRowPadding,
			},
		},
		DeepLinkElements: []*widget.DeepLinkElement{
			{
				Link:     getPlLandingScreenDeeplinkWithEntryPointData(),
				Text:     "View your loan",
				CtaTheme: widget.CtaDisplayTheme_PRIMARY,
				CtaType:  widget.CTAType_DEEPLINK,
			},
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)
}

func GetLoanSummaryWidgetForInEligibleUsers() *searchPb.SearchResultUnit {
	return GetSearchResultUnitFromSummaryV2(&summary.SummaryV2{
		CardTitle: summary_v2.PreApprovedLoanCardTitle,
		SummaryRows: []*widget.SummaryRow{
			{
				ChatheadImageContent: &widget.SummaryRow_ImgUrl{
					ImgUrl: summary_v2.LoanSummaryIcon,
				},
				Heading:    summary_v2.PreApprovedLoanHeading,
				RowPadding: SummaryHeadingRowPadding,
			},
			{
				ContentType: widget.ContentType_TEXT,
				Content: &widget.SummaryRow_TextContent{
					TextContent: &widget.TextContent{
						Text: summary_v2.PreApprovedLoanNotAvailableSummaryV2BodyText,
					},
				},
				RowPadding: SummaryNonHeadingRowPadding,
			},
		},
		DeepLinkElements: []*widget.DeepLinkElement{
			GetCreditScoreWidgetDeeplink(),
		},
		TabName: constant.FiTabName,
	}, constant.FiTabName)
}

func GetCreditScoreWidgetDeeplink() *widget.DeepLinkElement {
	return &widget.DeepLinkElement{
		Link: &deeplink.Deeplink{
			Screen: deeplink.Screen_ANALYSER_SCREEN,
			ScreenOptions: &deeplink.Deeplink_AnalyserScreenOptions{
				AnalyserScreenOptions: &deeplink.AnalyserScreenOptions{
					AnalyserName: analyser.AnalyserName_ANALYSER_NAME_CREDIT_SCORE.String(),
				},
			},
		},
		Text:     "See credit score",
		CtaTheme: widget.CtaDisplayTheme_PRIMARY,
		CtaType:  widget.CTAType_DEEPLINK,
	}
}

func getPlLandingScreenDeeplinkWithEntryPointData() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
		ScreenOptions: &deeplink.Deeplink_PreApprovedLoanLandingScreenOptions{
			PreApprovedLoanLandingScreenOptions: &deeplink.PreApprovedLoanLandingScreenOptions{
				LoanHeader: &palEnumFePb.LoanHeader{
					EventData: &palEnumFePb.EventData{EntryPoint: palEnumFePb.EntryPoint_ENTRY_POINT_SEARCH_OR_ASK_FI},
				},
			},
		},
	}
}
