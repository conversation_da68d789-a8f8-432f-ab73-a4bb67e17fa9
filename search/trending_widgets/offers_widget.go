package trending_widgets

import (
	"context"

	"github.com/epifi/gamma/search/events"

	"github.com/epifi/gamma/api/frontend/deeplink"
	searchPb "github.com/epifi/gamma/api/search"
)

const (
	offersHeadingIMGUrl = "https://epifi-icons.pointz.in/quick-link-icons/LP_Offers.png"
	offersWidgetTitle   = "Fi Offers"
	offersWidgetKind    = "offers"
)

func (p *Processor) GetOffersWidget(ctx context.Context) *searchPb.TrendingWidget {
	return &searchPb.TrendingWidget{
		Title:         offersWidgetTitle,
		HeadingImgUrl: offersHeadingIMGUrl,
		BgColor:       TrendingTileBGColor,
		ShadowBgColor: TrendingTileShadowColor,
		Kind:          offersWidgetKind,
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_ASK_FI_LANDING_PAGE,
			ScreenOptions: &deeplink.Deeplink_AskFiLandingPageOptions{
				AskFiLandingPageOptions: &deeplink.AskFiLandingPageOptions{
					Query:  "fi offers",
					Source: events.Source_LandingPageRecommendedWidgets,
				},
			},
		},
	}
}
