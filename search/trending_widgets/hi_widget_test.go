package trending_widgets

import (
	"context"
	"testing"

	"github.com/epifi/gamma/search/events"

	"github.com/stretchr/testify/assert"

	"github.com/epifi/gamma/api/frontend/deeplink"
	searchPb "github.com/epifi/gamma/api/search"
)

func TestProcessor_GetHiWidget(t *testing.T) {
	p := NewProcessor(nil, nil, nil, nil, nil, nil, nil, nil)
	got := p.GetHiWidget(context.Background())
	assert.Equal(t, got, &searchPb.TrendingWidget{
		Title:         "Hi",
		HeadingImgUrl: "https://epifi-icons.pointz.in/quick-link-icons/LP_Hi.png",
		BgColor:       "#383838",
		ShadowBgColor: "#1A1A1A",
		Kind:          hiWidgetKind,
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_ASK_FI_LANDING_PAGE,
			ScreenOptions: &deeplink.Deeplink_AskFiLandingPageOptions{
				AskFiLandingPageOptions: &deeplink.AskFiLandingPageOptions{
					Query:  "Hi",
					Source: events.Source_LandingPageRecommendedWidgets,
				},
			},
		},
	})
}
