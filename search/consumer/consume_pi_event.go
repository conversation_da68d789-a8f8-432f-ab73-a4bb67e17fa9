package consumer

import (
	"context"
	"fmt"

	piPb "github.com/epifi/gamma/api/paymentinstrument"
	daoIndex "github.com/epifi/gamma/search/dao/index"

	"go.uber.org/zap"

	accountPIPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	indexerPb "github.com/epifi/gamma/api/search/indexer"
	"github.com/epifi/be-common/pkg/logger"
)

func (consumer *IndexConsumerService) ProcessPiEvent(ctx context.Context, event *accountPIPb.AccountPiCreateOrUpdateEvent) (*indexerPb.ProcessPiEventResponse, error) {
	switch event.GetEventType() {
	case accountPIPb.PiEventType_CREATE:
		logger.Debug(ctx, "pi-create event to index", zap.String(logger.PI_ID, event.GetAccountPi().GetPiId()))
		return consumer.processPiCreateEvent(ctx, event)
	case accountPIPb.PiEventType_UPDATE:
		logger.Debug(ctx, "pi-update event to index", zap.String(logger.PI_ID, event.GetPi().GetId()))
		return consumer.processPiUpdateEvent(ctx, event)
	default:
		logger.Error(ctx, "unrecognised event-type", zap.String("event-type", event.GetEventType().String()))
		return &indexerPb.ProcessPiEventResponse{ResponseHeader: genFinalFailure()}, fmt.Errorf("unrecognised event-type %s", event.GetEventType().String())
	}
}

func (consumer *IndexConsumerService) processPiCreateEvent(ctx context.Context, event *accountPIPb.AccountPiCreateOrUpdateEvent) (*indexerPb.ProcessPiEventResponse, error) {
	piRecord, err := consumer.buildPiRecordFromAccountPi(ctx, event)
	if err != nil {
		return &indexerPb.ProcessPiEventResponse{ResponseHeader: genFinalFailure()}, fmt.Errorf("error in building pi record: %w", err)
	}
	err = consumer.CurrIndexer.IndexPiRecord(ctx, piRecord)
	if err != nil {
		//TODO(shubhra): check if message go to dead-letter queue
		if event.RequestHeader.IsLastAttempt {
			logger.Error(ctx, "last attempt to index pi failed", zap.String("pi-id", event.GetAccountPi().GetPiId()))
			return &indexerPb.ProcessPiEventResponse{ResponseHeader: genFinalFailure()}, err
		}
		return &indexerPb.ProcessPiEventResponse{ResponseHeader: genTransientFailure()}, err
	}
	logger.Info(ctx, "successfully indexed pi record", zap.String(logger.PI_ID, event.GetAccountPi().GetPiId()))
	return &indexerPb.ProcessPiEventResponse{ResponseHeader: genSuccess()}, nil
}

func (consumer *IndexConsumerService) processPiUpdateEvent(ctx context.Context, event *accountPIPb.AccountPiCreateOrUpdateEvent) (*indexerPb.ProcessPiEventResponse, error) {
	piRecord, isStateUpdated, err := consumer.buildPiRecordFromPi(ctx, event)
	if !isStateUpdated {
		logger.Info(ctx, "pi-state is unspecified. Not updating pi-record in es", zap.String(logger.PI_ID, event.GetPi().GetId()))
		return &indexerPb.ProcessPiEventResponse{ResponseHeader: genSuccess()}, nil
	}
	if err != nil {
		return &indexerPb.ProcessPiEventResponse{ResponseHeader: genFinalFailure()}, fmt.Errorf("error in building pi-update record: %w", err)
	}
	err = consumer.CurrIndexer.UpdatePiRecord(ctx, piRecord)
	if err != nil {
		//TODO(shubhra): check if message go to dead-letter queue
		if event.RequestHeader.IsLastAttempt {
			logger.Error(ctx, "last attempt to index pi-update failed", zap.String("pi-id", event.GetPi().GetId()))
			return &indexerPb.ProcessPiEventResponse{ResponseHeader: genFinalFailure()}, err
		}
		return &indexerPb.ProcessPiEventResponse{ResponseHeader: genTransientFailure()}, err
	}
	logger.Info(ctx, "successfully indexed pi-update record", zap.String(logger.PI_ID, event.GetPi().GetId()))
	return &indexerPb.ProcessPiEventResponse{ResponseHeader: genSuccess()}, nil
}

func (consumer *IndexConsumerService) buildPiRecordFromAccountPi(ctx context.Context, event *accountPIPb.AccountPiCreateOrUpdateEvent) (*daoIndex.Instrument, error) {
	_, cancel := context.WithCancel(ctx)
	defer cancel()

	accountPi := event.GetAccountPi()
	piDetail, err := consumer.getPiDetail(ctx, accountPi.GetPiId())
	if err != nil {
		return nil, fmt.Errorf("error in getting pi-detail from piClient: %w", err)
	}
	return buildPiRecordFromPi(ctx, piDetail, accountPi.GetActorId(), consumer.depositClient)
}

// only update state for a pi in es-index
func (consumer *IndexConsumerService) buildPiRecordFromPi(ctx context.Context, event *accountPIPb.AccountPiCreateOrUpdateEvent) (piRecord *daoIndex.Instrument, isStateUpdated bool, err error) {
	_, cancel := context.WithCancel(ctx)
	defer cancel()
	var pi daoIndex.Instrument

	piDetail := event.GetPi()
	pi.PiId = piDetail.GetId()
	state := piDetail.GetState()
	// if state not updated return with isStateUpdated as false
	if state == piPb.PaymentInstrumentState_PAYMENT_INSTRUMENT_STATE_UNSPECIFIED {
		return nil, false, nil
	}
	if pi.PiId == "" {
		logger.Error(ctx, "no pi id found to update")
		return nil, true, fmt.Errorf("no pi id found to update")
	}
	logger.Debug(ctx, "successfully build pi-record to update", zap.String(logger.PI_ID, pi.PiId))
	return &pi, true, nil
}
