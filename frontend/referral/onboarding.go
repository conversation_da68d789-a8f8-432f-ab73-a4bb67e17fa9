package referral

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/gamma/api/frontend/header"
	feReferralPb "github.com/epifi/gamma/api/frontend/referral"
	beInAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

func (s *Service) GetEnterReferralCodePageV1(ctx context.Context, request *feReferralPb.EnterReferralCodePageV1Request) (*feReferralPb.EnterReferralCodePageV1Response, error) {
	var (
		actorID           = request.GetReq().GetAuth().GetActorId()
		defaultOfferCodes = s.getReferralDefaultOfferCodes()
		pageTitle         = "We’ve found offers just for you!"
		pageSubtitle      = "Enjoy joining benefits by applying any of the exclusive offers below"
	)
	if len(defaultOfferCodes) == 1 {
		pageTitle = "We’ve found an offer for you!"
		pageSubtitle = "Enjoy joining benefits by applying the exclusive offer below"
	}

	return &feReferralPb.EnterReferralCodePageV1Response{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: pageTitle},
			FontColor:    "#333333",
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
		},
		Desc: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: pageSubtitle},
			FontColor:    "#8D8D8D",
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
		},
		DefaultOfferCodes:     defaultOfferCodes,
		ReferralCodeEntryInfo: s.getReferralCodeEntryInfo(ctx, actorID),
		SkipCta: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Skip this step"},
					FontColor:    "#00B899",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      "#F7F9FC",
				CornerRadius: 19,
				TopPadding:   12, BottomPadding: 12, LeftPadding: 63, RightPadding: 63,
			},
		},
	}, nil
}

// getReferralDefaultOfferCodes returns the (default) offer codes to be rendered on referral screen during onboarding.
// It also caters for the following:
// 1. underlying finite code to be used for making the Claim API call
// 2. dialog box to be shown post successfully applying the code
func (s *Service) getReferralDefaultOfferCodes() []*feReferralPb.DefaultOfferCodeInfo {
	var (
		offerCodes []*feReferralPb.DefaultOfferCodeInfo
		popUpText  string
	)

	s.dyconf.Referrals().InfoDuringOnboarding().OfferCodes().Range(func(_ string, value *genconf.ReferralsOnbScreenOfferCode) (continueRange bool) {
		if !value.IsEnabled() {
			return true
		}

		offerText := value.Offer()
		codeAppliedPopup := s.getOfferAppliedDialogBoxInfo(value.CodeAppliedPopupDetails())
		// identifying offer code 2 and modifying fi-coins to fi-points as the text is hard coded in config with value.
		if value.Identifier() == "OnboardingOfferCode2" {
			offerText = accrualPkg.ReplaceCoinWithPointIfApplicable(fmt.Sprintf("Get flat %d Fi-Coins", accrualPkg.ConvertFiCoinsToFiPointsIfApplicable(2500, false, nil)))
			popUpText = accrualPkg.ReplaceCoinWithPointIfApplicable(fmt.Sprintf("You get flat %d Fi-Coins", accrualPkg.ConvertFiCoinsToFiPointsIfApplicable(2500, false, nil)))
			codeAppliedPopup.SubTitle = &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: popUpText},
						FontColor:    "#333333",
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
					},
				},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					BgColor:      "#C5E9B2",
					CornerRadius: 9,
					TopPadding:   12, BottomPadding: 12, LeftPadding: 12, RightPadding: 12,
				},
			}
		}
		offerCodes = append(offerCodes, &feReferralPb.DefaultOfferCodeInfo{
			Code: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						DisplayValue: &commontypes.Text_PlainString{PlainString: value.DisplayCode()},
						FontColor:    "#313234",
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					},
				},
				LeftIcon:          &commontypes.Image{ImageType: commontypes.ImageType_PNG, ImageUrl: value.Icon(), Height: 24, Width: 24},
				LeftImgTxtPadding: 8,
				// todo: check if we need container properties
			},
			Offer: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: offerText},
				FontColor:    "#5D7D4C",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Desc: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: value.Desc()},
				FontColor:    "#606265",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			ApplyNowCta: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: value.CtaText()},
				FontColor:    "#00B899",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
			},
			CodeAppliedPopup:     codeAppliedPopup,
			BgColor:              &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#F7F9FC"}},
			DisplayedCode:        value.DisplayCode(),
			UnderlyingFiniteCode: value.UnderlyingFiniteCode(),
		})

		return true
	})

	return offerCodes
}

// nolint:funlen
// getOfferAppliedDialogBoxInfo returns the dialog box details to be shown post a code is applied.
// It also decides whether the box will have `Accept` or `Accept` && `Dismiss` CTA depending on the config.
func (s *Service) getOfferAppliedDialogBoxInfo(popupConf *genconf.ReferralsOnbScreenCodeSubmittedDialogBox) *feReferralPb.DialogBoxV1Info {
	if popupConf == nil {
		return nil
	}
	ctaText := "Continue"
	if popupConf.CtaText() != "" {
		ctaText = popupConf.CtaText()
	}

	var subtitle *ui.IconTextComponent
	if popupConf.Subtitle() != "" {
		subtitle = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: popupConf.Subtitle()},
					FontColor:    "#333333",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_3},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      "#C5E9B2",
				CornerRadius: 9,
				TopPadding:   12, BottomPadding: 12, LeftPadding: 12, RightPadding: 12,
			},
		}
	}

	dialogBox := &feReferralPb.DialogBoxV1Info{
		Image: &commontypes.Image{ImageUrl: popupConf.Image(), ImageType: commontypes.ImageType_PNG, Height: 100, Width: 100},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: popupConf.Title()},
			FontColor:    "#000000",
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_L},
		},
		SubTitle: subtitle,
		Desc: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: popupConf.Desc()},
			FontColor:    "#646464",
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
		},
		BgColor: &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#D9F2CC"}},
		Accept: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: ctaText},
					FontColor:    "#00B899",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      "#FFFFFF",
				CornerRadius: 18,
				TopPadding:   12, BottomPadding: 12,
			},
		},
	}

	if popupConf.CanDismiss() {
		dialogBox.Dismiss = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Remove"},
					FontColor:    "#00B899",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      "#FFFFFF",
				CornerRadius: 18,
				TopPadding:   12, BottomPadding: 12,
			},
		}
	}

	return dialogBox
}

func (s *Service) getOfferAppliedDialogBoxInfoFromBEComponent(beComponent *beInAppReferralPb.CodeAppliedComponentDetails) *feReferralPb.DialogBoxV1Info {
	popupConf := beComponent.GetCodeAppliedPopup()
	if popupConf == nil {
		return nil
	}

	var subtitle *ui.IconTextComponent
	var desc *commontypes.Text
	if popupConf.GetSubtitle() != nil {
		subtitle = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				getTypesTextFromBETextWithFallback(popupConf.GetSubtitle(), commontypes.FontStyle_SUBTITLE_3, "", "#333333"),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      getStringWithFallback(popupConf.GetSubtitle().GetBgColor(), "#C5E9B2"),
				CornerRadius: 9,
				TopPadding:   12, BottomPadding: 12, LeftPadding: 12, RightPadding: 12,
			},
		}
	}
	if popupConf.GetDesc() != nil {
		desc = getTypesTextFromBETextWithFallback(popupConf.GetDesc(), commontypes.FontStyle_BODY_S, "", "#646464")
	}

	dialogBox := &feReferralPb.DialogBoxV1Info{
		Image:    &commontypes.Image{ImageUrl: popupConf.GetPrimaryVisualElement(), ImageType: commontypes.ImageType_PNG, Height: 100, Width: 100},
		Title:    getTypesTextFromBETextWithFallback(popupConf.GetTitle(), commontypes.FontStyle_SUBTITLE_L, "", "#000000"),
		SubTitle: subtitle,
		Desc:     desc,
		BgColor:  &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: getStringWithFallback(popupConf.GetBgColor(), "#D9F2CC")}},
		Accept: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: getStringWithFallback(popupConf.GetCta().GetText(), "Continue")},
					FontColor:    getStringWithFallback(popupConf.GetCta().GetFontColor(), "#00B899"),
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      getStringWithFallback(popupConf.GetCta().GetBgColor(), "#FFFFFF"),
				CornerRadius: 18,
				TopPadding:   12, BottomPadding: 12,
			},
		},
	}

	if popupConf.GetCanDismiss() {
		dialogBox.Dismiss = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Remove"},
					FontColor:    "#00B899",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:      "#FFFFFF",
				CornerRadius: 18,
				TopPadding:   12, BottomPadding: 12,
			},
		}
	}

	return dialogBox
}

// nolint: funlen
// getReferralCodeEntryInfo returns info associated with manual referral code entry. It caters for the following:
// 1. entry point for entering referral code manually
// 2. bottom-sheet for entering the code
// 3. loader when the API call is in progress
// 4. dialog box info post code is applied successfully
func (s *Service) getReferralCodeEntryInfo(ctx context.Context, actorID string) *feReferralPb.EnterReferralCodePageV1Response_ReferralCodeEntryInfo {
	return &feReferralPb.EnterReferralCodePageV1Response_ReferralCodeEntryInfo{
		Cta: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Did a friend refer you?"},
					FontColor:    "#606265",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
				},
			},
			RightIcon: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/manual-entry-cta.png",
				Height:    24, Width: 24,
			},
			RightImgTxtPadding: 16,
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				CornerRadius: 19,
				TopPadding:   16, RightPadding: 16, LeftPadding: 20, BottomPadding: 16,
				BgColor: "#F7F9FC",
			},
		},
		CodeInputBottomSheet: &feReferralPb.ReferralCodeInputBottomSheet{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Did a friend refer you?"},
				FontColor:    "#333333",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_2},
			},
			Desc: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "If you received an invite to Fi, enter the referral code below and enjoy the referral rewards"},
				FontColor:    "#8D8D8D",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
			},
			InputBoxPlaceholder: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Enter code"},
				FontColor:    "#B2B5B9",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
			},
			SubmitButtonDisabledIcon: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-submit-disabled.png",
				Height:    48, Width: 48,
			},
			SubmitButtonEnabledIcon: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-submit-enabled.png",
				Height:    48, Width: 48,
			},
			InputBoxSubtext: s.getInputBoxSubtext(ctx, actorID),
			LoaderTitle: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Verifying your referral code"},
				FontColor:    "#000000",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_L},
			},
			LoaderSubTitle: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "This takes a few seconds ⏱️"},
				FontColor:    "#333333",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
			},
		},
		CodeAppliedPopup: s.getOfferAppliedDialogBoxInfo(s.dyconf.Referrals().InfoDuringOnboarding().ReferralCodeAppliedPopupDetails()),
	}
}

func (s *Service) getInputBoxSubtext(ctx context.Context, actorID string) *commontypes.Text {
	if !s.isPhoneNumberAsReferralCodeEnabled(ctx, actorID) {
		return nil
	}

	subtext := commontypes.GetTextFromStringFontColourFontStyle("Forgot referral code? Enter your friend's number 📞", "#333333", commontypes.FontStyle_NUMBER_2XS)
	subtext.BgColor = "#E6E9ED"

	return subtext
}
