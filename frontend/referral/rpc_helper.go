package referral

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	feReferralPb "github.com/epifi/gamma/api/frontend/referral"
	beInAppReferralPb "github.com/epifi/gamma/api/inappreferral"
	inAppReferralEnumPb "github.com/epifi/gamma/api/inappreferral/enums"
	beRewardsPb "github.com/epifi/gamma/api/rewards"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	beOnbPb "github.com/epifi/gamma/api/user/onboarding"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
)

func (s *Service) hasClaimedFiniteCodeViaReferral(ctx context.Context, actorId string) (bool, string, error) {
	getReferralDetailsForActorResponse, err := s.inAppReferralClient.GetReferralDetailsForActor(ctx, &beInAppReferralPb.GetReferralDetailsForActorRequest{
		ActorId:                   actorId,
		ForceIncludeExceptionCase: true,
	})

	switch {
	case err != nil:
		return false, "", fmt.Errorf("inAppReferralClient.GetReferralDetailsForActor() erred out, %w", err)
	case getReferralDetailsForActorResponse == nil || getReferralDetailsForActorResponse.GetStatus() == nil:
		return false, "", fmt.Errorf("got nil in inAppReferralClient.GetReferralDetailsForActor response: %v", getReferralDetailsForActorResponse)
	case getReferralDetailsForActorResponse.GetStatus().IsInternal():
		return false, "", fmt.Errorf("got INTERNAL status in fetching whether user has already onboarded via referral")
	case getReferralDetailsForActorResponse.GetStatus().IsRecordNotFound():
		return false, "", nil
	case getReferralDetailsForActorResponse.GetStatus().IsSuccess():
		return true, getReferralDetailsForActorResponse.GetReferralDetails().GetCode(), nil
	default:
		return false, "", fmt.Errorf("got unexpected status in inAppReferralClient.GetReferralDetailsForActor: %s", getReferralDetailsForActorResponse.GetStatus())
	}
}

// getFiniteCode returns if a finite code exists in referral service,
// and if it does whether it's claim limit has been exhausted or not.
func (s *Service) getFiniteCodeViaReferral(ctx context.Context, code string) (doesFiniteCodeExist bool, hasClaimLimitExhausted bool, finiteCode *beInAppReferralPb.FiniteCode, err error) {
	getFiniteCodeByCodeResponse, err := s.inAppReferralClient.GetFiniteCodeByCode(ctx, &beInAppReferralPb.GetFiniteCodeByCodeRequest{
		Code: code,
	})

	switch {
	case err != nil:
		return false, false, nil, fmt.Errorf("inAppReferralClient.GetFiniteCodeByCode() erred out, %w", err)
	case getFiniteCodeByCodeResponse == nil || getFiniteCodeByCodeResponse.GetStatus() == nil:
		return false, false, nil, fmt.Errorf("got nil in inAppReferralClient.GetFiniteCodeByCode response: %v", getFiniteCodeByCodeResponse)
	case getFiniteCodeByCodeResponse.GetStatus().IsInternal():
		return false, false, nil, fmt.Errorf("got INTERNAL status in fetching whether user has already onboarded via referral")
	case getFiniteCodeByCodeResponse.GetStatus().IsRecordNotFound():
		return false, false, nil, nil
	case getFiniteCodeByCodeResponse.GetStatus().IsResourceExhausted():
		return true, true, getFiniteCodeByCodeResponse.GetFiniteCode(), nil
	case getFiniteCodeByCodeResponse.GetStatus().IsSuccess():
		return true, false, getFiniteCodeByCodeResponse.GetFiniteCode(), nil
	default:
		return false, false, nil, fmt.Errorf("got unexpected status in inAppReferralClient.GetFiniteCodeByCode: %s", getFiniteCodeByCodeResponse.GetStatus())
	}
}

func (s *Service) generateNewFiniteCodeFromBE(ctx context.Context, actorId string, finiteCodeType inAppReferralEnumPb.FiniteCodeType) error {
	res, err := s.inAppReferralClient.GenerateFiniteCodeForActor(ctx, &beInAppReferralPb.GenerateFiniteCodeForActorRequest{
		ActorId:        actorId,
		FiniteCodeType: finiteCodeType,
	})
	switch {
	case err != nil:
		return fmt.Errorf("inAppReferralClient.GenerateFiniteCodeForActor erred out %w", err)
	case res == nil || res.GetStatus() == nil:
		return fmt.Errorf("got nil in inAppReferralClient.GenerateFiniteCodeForActor response %v", res)
	case res.GetStatus().IsFailedPrecondition():
		return fmt.Errorf("user not eligible for referral program, %w", notEligibleForReferralError)
	case res.GetStatus().IsAlreadyExists():
		return nil
	case res.GetStatus().IsSuccess():
		return nil
	default:
		return fmt.Errorf("inAppReferralClient.GenerateFiniteCodeForActor returned unknown status response, %v", res.GetStatus())
	}
}

func (s *Service) getCustomerInfo(ctx context.Context, actorId string) (mobileNumber *commontypes.PhoneNumber, err error) {
	getEntityDetailsByActorIdResponse, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: actorId,
	})
	switch {
	case err != nil:
		wrappedErr := fmt.Errorf("actorClient.GetEntityDetailsByActorId() failed, %w", err)
		return nil, wrappedErr
	case getEntityDetailsByActorIdResponse == nil || getEntityDetailsByActorIdResponse.GetStatus() == nil:
		wrappedErr := fmt.Errorf("got nil in actorClient.GetEntityDetailsByActorId(), resp: %v", getEntityDetailsByActorIdResponse)
		return nil, wrappedErr
	case !getEntityDetailsByActorIdResponse.GetStatus().IsSuccess():
		wrappedErr := fmt.Errorf("actorClient.GetEntityDetailsByActorId() failed with status: %s", getEntityDetailsByActorIdResponse.GetStatus())
		return nil, wrappedErr
	default:
		return getEntityDetailsByActorIdResponse.GetMobileNumber(), nil
	}
}

// getTotalRewardsEarnedForReferral returns the total rewards earned via referral for an actor.
func (s *Service) getTotalRewardsEarnedForReferral(ctx context.Context, actorId string, fromTime *timestamppb.Timestamp) ([]*feReferralPb.Reward, error) {
	res, err := s.rewardsClient.GetRewardSummary(ctx, &beRewardsPb.GetRewardSummaryRequest{
		ActorId: actorId,
		Filter: &beRewardsPb.GetRewardSummaryRequest_Filter{
			RewardOfferType: beRewardsPb.RewardOfferType_REFERRAL_REFERRER_OFFER,
			FromTime:        fromTime,
		},
	})

	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, fmt.Errorf("failed to fetch referral rewards for actor, %w", te)
	}
	return []*feReferralPb.Reward{
		// populate fi-coins rewards
		{
			Icon: &commontypes.Image{
				ImageUrl: "https://epifi-icons.pointz.in/rewards/ficoin-2.png",
			},
			RewardType: feReferralPb.Reward_FI_COINS,
			Amount: &types.Money{
				Units: int64(res.GetTotalFiCoinsEarned()),
			},
			Title: "Fi-Coins",
		},
		// populate savings rewards
		{
			Icon: &commontypes.Image{
				ImageUrl: "https://epifi-icons.pointz.in/casper/exchanger/cashback.png",
			},
			RewardType: feReferralPb.Reward_SAVINGS,
			Amount: &types.Money{
				CurrencyCode: "INR",
				Units:        res.GetTotalCashRewardEarned().GetUnits() + res.GetTotalSidRewardEarned().GetUnits(),
				Decimals:     0,
			},
			Title: "Cash",
		},
	}, nil
}

func (s *Service) beIsActorEligibleForReferral(ctx context.Context, actorId string, finiteCodeType inAppReferralEnumPb.FiniteCodeType) (*feReferralPb.IsActorEligibleForReferralResponse_UnqualifiedDetails, error) {
	res, err := s.inAppReferralClient.IsActorEligibleForReferral(ctx, &beInAppReferralPb.IsActorEligibleForReferralRequest{ActorId: actorId, FiniteCodeType: finiteCodeType})

	switch {
	case err != nil:
		return nil, fmt.Errorf("inAppReferralClient.IsActorEligibleForReferral() failed, %w", err)
	case res == nil || res.GetStatus() == nil:
		return nil, fmt.Errorf("got nil in inAppReferralClient.IsActorEligibleForReferral(), resp: %v", res)
	case res.Status.GetCode() == uint32(beInAppReferralPb.IsActorEligibleForReferralResponse_FAILED_PRECONDITION_ONBOARDING_DURATION):
		// TODO(harish): make unqualifiedDetails config driven, and drive duration and amount from const
		unqualifiedDetailsDesc := fmt.Sprintf("It’s been %v days since you opened your Fi Account. Maintain an average balance of ₹1,000 across your Savings Account and Deposits for at least 10 days to start referring!\n\nNote: After you add funds, it may take 2 - 3 business days to reflect in your average balance.", res.GetDaysSinceOnboarding())
		unqualifiedDetails := &feReferralPb.IsActorEligibleForReferralResponse_UnqualifiedDetails{
			Title:                     "Unlock referrals & get your friends on Fi",
			Description:               unqualifiedDetailsDesc,
			GranularUnqualifiedStatus: feReferralPb.IsActorEligibleForReferralResponse_UnqualifiedDetails_INSUFFICIENT_ONB_DURATION,
		}
		return unqualifiedDetails, fmt.Errorf("failed precondition, %w", notEligibleForReferralError)
	case res.Status.GetCode() == uint32(beInAppReferralPb.IsActorEligibleForReferralResponse_FAILED_PRECONDITION_AVG_EOD_BALANCE):
		// TODO(harish): make unqualifiedDetails config driven, and drive duration and amount from const
		eodAvgBalance := ""
		if res.GetAmount() != nil {
			// since money.ToString with precision 0 rounds-up values with decimal value >= 5, we are explicitly setting the
			// nanos to 0 as we only want round-down
			res.Amount.Nanos = 0
			eodAvgBalance, err = money.ToString(res.GetAmount(), 0)
			if err != nil {
				return nil, fmt.Errorf("failed to convert amount to string, %w", err)
			}
		}
		unqualifiedDetailsDesc := fmt.Sprintf("Maintain an average balance of ₹1,000 or more across your Savings Account and Deposits for at least 10 days to start referring! Your current average balance is approximately ₹%s.\n\nNote: After you add funds, it may take 2 - 3 business days to reflect in your average balance.", eodAvgBalance)
		unqualifiedDetails := &feReferralPb.IsActorEligibleForReferralResponse_UnqualifiedDetails{
			Title:                     "Unlock referrals & get your friends on Fi",
			Description:               unqualifiedDetailsDesc,
			GranularUnqualifiedStatus: feReferralPb.IsActorEligibleForReferralResponse_UnqualifiedDetails_INSUFFICIENT_AVERAGE_EOD_BALANCE,
		}
		return unqualifiedDetails, fmt.Errorf("failed precondition, %w", notEligibleForReferralError)
	case res.Status.GetCode() == uint32(beInAppReferralPb.IsActorEligibleForReferralResponse_NO_AVG_EOD_BALANCE_FOUND):
		unqualifiedDetailsDesc := "Maintain an average balance of ₹1,000 or more across your Savings Account and Deposits for at least 10 days to start referring! \n\nNote: After you add funds, it may take 2 - 3 business days to reflect in your average balance."
		unqualifiedDetails := &feReferralPb.IsActorEligibleForReferralResponse_UnqualifiedDetails{
			Title:                     "Unlock referrals & get your friends on Fi",
			Description:               unqualifiedDetailsDesc,
			GranularUnqualifiedStatus: feReferralPb.IsActorEligibleForReferralResponse_UnqualifiedDetails_INSUFFICIENT_AVERAGE_EOD_BALANCE,
		}
		return unqualifiedDetails, fmt.Errorf("failed precondition, %w", notEligibleForReferralError)
	case !res.GetStatus().IsSuccess():
		return nil, fmt.Errorf("inAppReferralClient.IsActorEligibleForReferral() failed with status: %s", res.GetStatus())
	default:
		return nil, nil
	}
}

func (s *Service) checkReferralEligibilityForActor(ctx context.Context, actorId string) (*errors.ErrorView, error) {
	// check if actor is eligible for referral
	res, err := s.IsActorEligibleForReferral(ctx, &feReferralPb.IsActorEligibleForReferralRequest{Req: &header.RequestHeader{
		Auth: &header.AuthHeader{ActorId: actorId},
	}})
	switch {
	case err != nil:
		return nil, fmt.Errorf("failed to check actor referral eligibility, %w", err)
	case res == nil || res.GetRespHeader().GetStatus() == nil:
		return nil, fmt.Errorf("got nul response from FE's IsActorEligibleForReferral RPC, %v", res.GetRespHeader().GetStatus())
	case res.GetRespHeader().GetStatus().IsSuccess():
		return nil, nil
	case !res.GetRespHeader().GetStatus().IsSuccess() && res.GetUnqualifiedDetails() != nil:
		return &errors.ErrorView{
			Type: errors.ErrorViewType_FULL_SCREEN,
			// TODO(harish): confirm with @aniruddha if invite friends post add funds will only be called for successful cases
			Options: &errors.ErrorView_FullScreenErrorView{FullScreenErrorView: &errors.FullScreenErrorView{
				Title:    "Funds added successfully",
				Subtitle: "After you add funds, it may take up to 2 - 3 days to reflect in your average balance.\n\nTo start referring, maintain an average balance of ₹1,000 or more across your Savings Account and Deposits for at least 10 days.",
			}},
		}, nil
	default:
		return nil, fmt.Errorf("failed to check actor referral eligibility, %v", res.GetRespHeader().GetStatus())
	}
}

func (s *Service) getDebitCardPinSetTimeForActor(ctx context.Context, actorId string) (*time.Time, error) {
	onbDetailsRes, err := s.onboardingClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId:    actorId,
		CachedData: true,
	})
	if err != nil || !onbDetailsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching onboarding details for actor", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Error(err), zap.Any(logger.RPC_STATUS, onbDetailsRes.GetStatus()),
		)
		return nil, fmt.Errorf("error fetching onboarding details for actor: %w", err)
	}

	onbStageInfoMap := onbDetailsRes.GetDetails().GetStageDetails().GetStageMapping()
	if len(onbStageInfoMap) == 0 {
		logger.WarnWithCtx(ctx, "onb stage info map is empty in onb details", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, nil
	}

	debitCardPinSetStageInfo := onbStageInfoMap[onbPb.OnboardingStage_DEBIT_CARD_PIN_SETUP.String()]
	if debitCardPinSetStageInfo == nil {
		logger.WarnWithCtx(ctx, "DEBIT_CARD_PIN_SETUP stage not found in onb details", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, nil
	}

	if debitCardPinSetStageInfo.GetState() != onbPb.OnboardingState_SUCCESS {
		logger.WarnWithCtx(ctx, "DEBIT_CARD_PIN_SETUP stage not yet completed", zap.Any("current_status", debitCardPinSetStageInfo.GetState()),
			zap.String(logger.ACTOR_ID_V2, actorId),
		)
		return nil, nil
	}

	debitCardPinSetTime := debitCardPinSetStageInfo.GetLastUpdatedAt().AsTime()
	return &debitCardPinSetTime, nil
}

// TODO : this can be combined with getDebitCardPinSetTimeForActor method to be a generic method which takes in an onboarding stage as parameter and gives time as output
// nolint: dupl
func (s *Service) getReferralFiniteCodeStageTimeForActor(ctx context.Context, actorId string) (*time.Time, error) {
	onbDetailsRes, err := s.onboardingClient.GetDetails(ctx, &onbPb.GetDetailsRequest{
		ActorId:    actorId,
		CachedData: true,
	})
	if err != nil || !onbDetailsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching onboarding details for actor", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Error(err), zap.Any(logger.RPC_STATUS, onbDetailsRes.GetStatus()),
		)
		return nil, fmt.Errorf("error fetching onboarding details for actor: %w", err)
	}

	onbStageInfoMap := onbDetailsRes.GetDetails().GetStageDetails().GetStageMapping()
	if len(onbStageInfoMap) == 0 {
		logger.WarnWithCtx(ctx, "onb stage info map is empty in onb details", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, nil
	}

	referralFiniteCodeStageInfo := onbStageInfoMap[onbPb.OnboardingStage_REFERRAL_FINITE_CODE.String()]
	if referralFiniteCodeStageInfo == nil {
		logger.WarnWithCtx(ctx, "REFERRAL_FINITE_CODE stage not found in onb details", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, nil
	}

	if referralFiniteCodeStageInfo.GetState() != onbPb.OnboardingState_SUCCESS {
		logger.WarnWithCtx(ctx, "REFERRAL_FINITE_CODE stage not yet completed", zap.Any("current_status", referralFiniteCodeStageInfo.GetState()),
			zap.String(logger.ACTOR_ID_V2, actorId),
		)
		return nil, nil
	}

	referralClaimTime := referralFiniteCodeStageInfo.GetLastUpdatedAt().AsTime()
	return &referralClaimTime, nil
}

// This method should return false by default, and in case there are errors in determining restrict-ability
func (s *Service) isReferralsRestrictedForActor(ctx context.Context, actorID string) bool {

	isUserAgeRestricted := false

	errGrp, errGrpCtx := errgroup.WithContext(ctx)

	if s.dyconf.Referrals().FeatureRestrictionParams().AgeRestriction().Enable() {
		errGrp.Go(func() error {
			userResp, getUserErr := s.userClient.GetUser(errGrpCtx, &userPb.GetUserRequest{
				Identifier: &userPb.GetUserRequest_ActorId{
					ActorId: actorID,
				},
			})

			if rpcErr := epifigrpc.RPCError(userResp, getUserErr); rpcErr != nil {
				return fmt.Errorf("error fetching user for actor : %w", rpcErr)
			}

			age := datetime.AgeAt(datetime.DateToTimeV2(userResp.GetUser().GetProfile().GetDateOfBirth(), datetime.IST), time.Now())
			lowerBound := s.dyconf.Referrals().FeatureRestrictionParams().AgeRestriction().AgeLowerBound()
			if age < lowerBound {
				logger.Info(ctx, "user is age restricted for referrals", zap.Int("ageLowerBound", lowerBound))
				isUserAgeRestricted = true
				return nil
			}

			return nil
		})

	}

	checkReferralsRestrictedErr := errGrp.Wait()
	if checkReferralsRestrictedErr != nil {
		logger.Error(ctx, "error checking whether referrals is restricted for user", zap.Error(checkReferralsRestrictedErr))
		return false
	}

	return isUserAgeRestricted
}

func (s *Service) isFiLiteUser(ctx context.Context, actorId string) (bool, error) {
	featureDetailsRes, featureDetailsErr := s.onboardingClient.GetFeatureDetails(ctx, &beOnbPb.GetFeatureDetailsRequest{
		Feature: beOnbPb.Feature_FEATURE_SA,
		ActorId: actorId,
	})

	if rpcErr := epifigrpc.RPCError(featureDetailsRes, featureDetailsErr); rpcErr != nil {
		return false, fmt.Errorf("onboardingClient.GetFeatureDetails call failed : %w", rpcErr)
	}

	return featureDetailsRes.GetIsFiLiteUser(), nil
}

func (s *Service) getUserFeatureReferralEligibilityInfo(ctx context.Context, actorId string) (*userFeatureReferralEligibilityInfo, error) {
	featureDetailsRes, featureDetailsErr := s.onboardingClient.GetFeatureDetails(ctx, &beOnbPb.GetFeatureDetailsRequest{
		Feature: beOnbPb.Feature_FEATURE_WEALTH_ANALYSER,
		ActorId: actorId,
	})

	if rpcErr := epifigrpc.RPCError(featureDetailsRes, featureDetailsErr); rpcErr != nil {
		return nil, fmt.Errorf("onboardingClient.GetFeatureDetails call failed : %w", rpcErr)
	}

	switch {
	case !featureDetailsRes.GetIsFiLiteUser():
		return &userFeatureReferralEligibilityInfo{
			isEligible:     true,
			appFeature:     inAppReferralEnumPb.AppFeature_SAVINGS_ACCOUNT,
			finiteCodeType: inAppReferralEnumPb.FiniteCodeType_REGULAR,
		}, nil

	case s.dyconf.ReferralsV1().IsReferralForD2hUsersEnabled() &&
		(featureDetailsRes.GetFeatureInfo().GetFeatureStatus() == beOnbPb.FeatureStatus_FEATURE_STATUS_ACTIVE ||
			featureDetailsRes.GetFeatureInfo().GetFeatureStatus() == beOnbPb.FeatureStatus_FEATURE_STATUS_INACTIVE):
		return &userFeatureReferralEligibilityInfo{
			isEligible:     true,
			appFeature:     inAppReferralEnumPb.AppFeature_DIRECT_TO_HOME,
			finiteCodeType: inAppReferralEnumPb.FiniteCodeType_D2H_REGULAR,
		}, nil

	default:
		return &userFeatureReferralEligibilityInfo{
			isEligible: false,
		}, nil
	}
}
