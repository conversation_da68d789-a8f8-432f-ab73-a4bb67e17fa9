package fittt

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/google/uuid"
	errors2 "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/fieldmaskpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/fittt"
	actionpb "github.com/epifi/gamma/api/fittt/action"
	eventpb "github.com/epifi/gamma/api/fittt/event"
	"github.com/epifi/gamma/api/fittt/sports"
	deeplinkpb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/fittt"
	feHdr "github.com/epifi/gamma/api/frontend/header"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	luckyDrawPb "github.com/epifi/gamma/api/rewards/luckydraw"
	"github.com/epifi/gamma/api/rms/manager"
	"github.com/epifi/gamma/api/rms/orchestrator/event"
	"github.com/epifi/gamma/api/rms/ui"
	"github.com/epifi/gamma/api/search"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/fittt/errors"
	"github.com/epifi/gamma/pkg/accrual"
	fitttPkg "github.com/epifi/gamma/pkg/fittt"
	pkgOnboarding "github.com/epifi/gamma/pkg/onboarding"
)

type Matches []*sports.Match

func (a Matches) Len() int { return len(a) }
func (a Matches) Less(i, j int) bool {
	return a[i].GetExpectedStartTime().AsTime().Before(a[j].GetExpectedStartTime().AsTime())
}
func (a Matches) Swap(i, j int) { a[i], a[j] = a[j], a[i] }

type MatchesForSportsChallengeScreen struct {
	executedMatches       []*sports.Match
	ongoingOrEndedMatches []*sports.Match
	openToPlayMatch       *sports.Match
	lockedMatch           *sports.Match
}
type CricketMatchStateForChallenge int8

const (
	MATCH_YET_TO_START CricketMatchStateForChallenge = iota
	// FIT rule execution for the match is completed
	MATCH_EXECUTED
	MATCH_IN_PROGRESS
	MATCH_ENDED
)

type DailyDrawCardState int8

const (
	DAILY_DRAW_INVALID_STATE DailyDrawCardState = iota
	DAILY_DRAW_ZERO_STATE
	DAILY_DRAW_NOT_QUALIFIED
	DAILY_DRAW_WON_MONEY_PLANT
	DAILY_DRAW_WON_FI_COINS
	DAILY_DRAW_WON_CASH
)

// nolint: funlen
func (s *Service) GetSportsChallengeHomePageData(ctx context.Context, req *fePb.GetSportsChallengeHomePageDataRequest) (*fePb.GetSportsChallengeHomePageDataResponse, error) {
	matches, err := s.getMatchesForChallengeHomeScreen(ctx)
	if err != nil {
		logger.Error(ctx, "error in getting matches for tournament", zap.Error(err))
		return &fePb.GetSportsChallengeHomePageDataResponse{RespHeader: errors.GetFitFullScreenErrorResponseHeader(err, errors.FIT0006)}, nil
	}

	logger.Debug(ctx, "determined different matches",
		zap.Any("executed_next", matches.executedMatches),
		zap.Any("ongoing_ended_matches", matches.ongoingOrEndedMatches),
		zap.Any("open_to_play", matches.openToPlayMatch),
		zap.Any("locked", matches.lockedMatch))

	var matchCards []*fePb.MatchCard
	var banners []*fePb.BannerCard
	var claimPrizeScreenData *fePb.ClaimPrizePageData
	var rewardsDigest *fePb.RewardsDigest
	var leaderboard *fePb.Leaderboard
	g, _ := errgroup.WithContext(ctx)
	g.Go(func() error {
		matchCardsLocal, localErr := s.getMatchCards(ctx, matches, req.GetReq().GetAuth().GetActorId())
		if localErr != nil {
			logger.Error(ctx, "error in getting match cards", zap.Error(localErr))
			return localErr
		}
		matchCards = matchCardsLocal
		return nil
	})

	g.Go(func() error {
		bannersLocal, claimPrizeScreenDataLocal, localErr := s.getSportsChallengeHomePageBannersAndClaimPrizeScreenData(ctx,
			req.GetReq().GetAuth().GetActorId(), req.GetTournamentId(), matches)
		if localErr != nil {
			logger.Error(ctx, "error fetching banners for home page", zap.Error(localErr))
			return localErr
		}
		banners = bannersLocal
		claimPrizeScreenData = claimPrizeScreenDataLocal
		return nil
	})

	g.Go(func() error {
		rewardsDigestLocal, localErr := s.getSportsChallengeHomePageRewardsDigest(ctx, req.GetReq().GetAuth().GetActorId())
		if localErr != nil {
			logger.Error(ctx, "error fetching rewards data for home page", zap.Error(localErr))
			return localErr
		}
		rewardsDigest = rewardsDigestLocal
		return nil
	})

	g.Go(func() error {
		if !s.conf.Fittt().FiSavingsLeague().ShowLeaderboard() {
			logger.Debug(ctx, "not showing leaderboard")
			return nil
		}
		leaderboardLocal, localErr := s.GetSportsChallengeHomePageLeaderboard(ctx, matches, req.GetReq().GetAuth().GetActorId())
		if localErr != nil {
			logger.Error(ctx, "error fetching weekly leaderboard for home page", zap.Error(localErr))
			return localErr
		}
		leaderboard = leaderboardLocal
		return nil
	})
	err = g.Wait()
	if err != nil {
		logger.Error(ctx, "error fetching sports challenge home page data", zap.Error(err))
		return &fePb.GetSportsChallengeHomePageDataResponse{RespHeader: errors.GetFitFullScreenErrorResponseHeader(err, errors.FIT0006)}, nil
	}

	return &fePb.GetSportsChallengeHomePageDataResponse{
		RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusOk()},
		Title:      &fePb.Text{Text: "Fi Savings League"},
		Header: &fePb.SportsChallengeHomePageHeader{
			Banners:     banners,
			DisplayInfo: &fePb.CardDisplayInfo{BgColor: "#4F71AB"},
		},
		MatchCards:         matchCards,
		RewardsDigest:      rewardsDigest,
		Leaderboard:        leaderboard,
		ClaimPrizePageData: claimPrizeScreenData,
	}, nil
}

type WeeklyRewardsEarned struct {
	startTime      time.Time
	fiCoinsEarned  int32
	cashbackEarned int64
}

func (s *Service) getSportsChallengeHomePageRewardsDigest(ctx context.Context, actorId string) (*fePb.RewardsDigest, error) {
	if !s.conf.Fittt().FiSavingsLeague().ShowRewardsDigest() {
		return nil, nil
	}

	var thisWeekRewardCard *fePb.RewardsDigest_PointsEarnedCard
	var weeklyRewardsEarnedCards []*fePb.RewardsDigest_FiCoinsCard
	startOfThisWeek := datetime.StartOfWeek(time.Now(), time.Sunday).In(datetime.IST)
	startOfTheTournament := time.Unix(s.conf.Fittt().FiSavingsLeague().StartDateOfTournament(), 0).In(datetime.IST)

	g, _ := errgroup.WithContext(ctx)
	g.Go(func() error {
		pointsEarnedThisWeek, amtSavedThisWeek, err := s.getThisWeekPointsAndSavedAmount(ctx, actorId, startOfThisWeek)
		if err != nil {
			return err
		}
		thisWeekRewardCard = getThisWeekRewardCard(pointsEarnedThisWeek, amtSavedThisWeek)
		return nil
	})
	g.Go(func() error {
		weeklyRewardsEarned, err := s.getPastWeeksCoinsAndCashbackRewards(ctx, actorId, startOfThisWeek, startOfTheTournament)
		if err != nil {
			return err
		}
		logger.Debug(ctx, "Rewards Digest", zap.Any("weeklyRewardsEarned", weeklyRewardsEarned))
		weeklyRewardsEarnedCards = getPastWeekRewardsCards(weeklyRewardsEarned)
		if len(weeklyRewardsEarnedCards) == 0 {
			return nil
		}
		return nil
	})
	err := g.Wait()
	if err != nil {
		logger.Error(ctx, "error in fetching Rewards Digest", zap.Error(err))
		return nil, err
	}

	return &fePb.RewardsDigest{
		Id: "rewards_digest",
		Title: &fePb.Text{
			Text: "Points & Rewards",
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#333333",
			},
		},
		PointsEarnedCard: thisWeekRewardCard,
		FiCoinsCard:      weeklyRewardsEarnedCards,
	}, nil
}

func getThisWeekRewardCard(pointsEarnedThisWeek uint32, amtSavedThisWeek int64) *fePb.RewardsDigest_PointsEarnedCard {
	pointsAndRewardsEarnedThisWeekText := "Participate now to earn points and rewards"
	if pointsEarnedThisWeek != 0 || amtSavedThisWeek != 0 {
		pointsAndRewardsEarnedThisWeekText = fmt.Sprintf("You earned %d points & saved ₹%d this week 🙌", pointsEarnedThisWeek, amtSavedThisWeek)
	}
	return &fePb.RewardsDigest_PointsEarnedCard{
		Id:    "this_week_reward_card",
		Title: nil,
		Description: &fePb.Text{
			Text: pointsAndRewardsEarnedThisWeekText,
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#333333",
			},
		},
		Action: &fePb.CTA{
			Text: &fePb.Text{Text: "SEE ALL REWARDS", DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#00B899"}},
			Action: &deeplinkpb.Deeplink{
				Screen:        deeplinkpb.Screen_MY_REWARDS_SCREEN,
				ScreenOptions: nil,
			},
			Id:     "this_week_reward_card_cta",
			ImgUrl: "https://epifi-icons.pointz.in/fittt-images/sports-challenge/rewards-coins.png",
		},
	}
}

func getPastWeekRewardsCards(weeklyRewardsEarned []*WeeklyRewardsEarned) []*fePb.RewardsDigest_FiCoinsCard {
	var weeklyRewardsCards []*fePb.RewardsDigest_FiCoinsCard
	for _, weekData := range weeklyRewardsEarned {
		if weekData.fiCoinsEarned == 0 && weekData.cashbackEarned == 0 {
			// if no coins or cashback earned this week, skip
			continue
		}
		var cardDate, cardDesc string
		weekStartDate := weekData.startTime
		weekEndDate := weekData.startTime.Add(6 * 24 * time.Hour)
		if weekStartDate.Month() == weekEndDate.Month() {
			truncatedMonth := strings.ToUpper(weekStartDate.Month().String()[:3])
			cardDate = fmt.Sprintf("%d-%d %s", weekStartDate.Day(), weekEndDate.Day(), truncatedMonth)
		} else {
			weekStartMonth := strings.ToUpper(weekStartDate.Month().String()[:3])
			weekEndMonth := strings.ToUpper(weekEndDate.Month().String()[:3])

			cardDate = fmt.Sprintf("%d %s-%d %s", weekStartDate.Day(), weekStartMonth, weekEndDate.Day(), weekEndMonth)
		}
		cardDesc = "Earned \n"
		if weekData.cashbackEarned != 0 {
			cardDesc += fmt.Sprintf("₹%d cash ", weekData.cashbackEarned)
		}
		if weekData.fiCoinsEarned != 0 {
			if weekData.cashbackEarned != 0 {
				cardDesc += "& \n"
			}
			cardDesc += fmt.Sprintf("%d %s", weekData.fiCoinsEarned, accrual.ReplaceCoinWithPointIfApplicable("Fi coins"))
		}
		card := &fePb.RewardsDigest_FiCoinsCard{
			Id:          cardDate,
			DateTime:    &fePb.Text{Text: cardDate},
			Description: &fePb.Text{Text: cardDesc},
		}
		weeklyRewardsCards = append(weeklyRewardsCards, card)
	}
	return weeklyRewardsCards
}

func (s *Service) getThisWeekPointsAndSavedAmount(ctx context.Context, actorId string, startTime time.Time) (uint32, int64, error) {
	pointsEarnedThisWeek, err := s.fitttClient.GetSportsTournamentPoints(ctx, &fittt.GetSportsTournamentPointsRequest{
		ActorId:        actorId,
		TournamentTag:  s.conf.Fittt().FiSavingsLeague().TournamentTagId(),
		EventStartFrom: timestamppb.New(startTime),
		EventStartTo:   timestamppb.New(time.Now().In(datetime.IST)),
	})
	if err2 := epifigrpc.RPCError(pointsEarnedThisWeek, err); err2 != nil {
		logger.Error(ctx, "error in calculation of this week's points", zap.Error(err2))
		return 0, 0, err
	}
	thisWeekPoints := pointsEarnedThisWeek.GetTotalPoints()
	logger.Debug(ctx, "Rewards Digest", zap.Any("pointsEarnedThisWeek", pointsEarnedThisWeek))

	// fetches the rewards earned this week
	// since we get startTime as the sunday for the week, we need to query
	// from search, the saved amount from evening as the rules will be executed
	// for the afternoon matches and amount will be saved in evening
	ruleIds := getFiSavingsLeagueRuleIds(s.conf)
	thisWeekSavedResp, err := s.searchClient.GetTxnAggregateFitRuleIds(ctx, &search.GetTxnAggregateFitRuleIdsRequest{
		ActorId:  actorId,
		RuleIds:  ruleIds,
		FromTime: timestamppb.New(startTime.Add(18 * time.Hour)),
		ToTime:   timestamppb.New(time.Now().In(datetime.IST)),
	})
	if err2 := epifigrpc.RPCError(pointsEarnedThisWeek, err); err2 != nil {
		logger.Error(ctx, "error in calculation of this week's saved amt", zap.Error(err2))
		return 0, 0, err
	}
	logger.Debug(ctx, "Rewards Digest", zap.Any("GetTxnAggregateFitRuleIds", thisWeekSavedResp))

	var amtSavedThisWeek int64
	for _, ruleId := range ruleIds {
		amtSavedThisWeek += thisWeekSavedResp.SavedAmountMap[ruleId].GetUnits()
	}

	return thisWeekPoints, amtSavedThisWeek, nil
}

// nolint: funlen
func (s *Service) getPastWeeksCoinsAndCashbackRewards(ctx context.Context, actorId string, startOfThisWeek, startOfTournament time.Time) ([]*WeeklyRewardsEarned, error) {
	// fetching all reward client request ids from fittt service from the start of the tournament
	// till this week's start in reverse chronological order
	rewardClientIdsResp, err := s.fitttClient.GetSportsTournamentRewardsClientIds(ctx, &fittt.GetSportsTournamentRewardsClientIdsRequest{
		TournamentTagId:             s.conf.Fittt().FiSavingsLeague().TournamentTagId(),
		StartTime:                   timestamppb.New(startOfTournament),
		EndTime:                     timestamppb.New(startOfThisWeek),
		InReverseChronologicalOrder: true,
	})
	if err2 := epifigrpc.RPCError(rewardClientIdsResp, err); err2 != nil {
		logger.Error(ctx, "error in fetching this weeks rewards summary", zap.Error(err2))
		return nil, err2
	}
	rewardClientIdsMapEntries := rewardClientIdsResp.GetWeekNoRewardClientIdsMap()
	logger.Debug(ctx, "Rewards Digest", zap.Any("Rewards Client ids", rewardClientIdsMapEntries))

	weeklyRewardsEarned := make([]*WeeklyRewardsEarned, len(rewardClientIdsMapEntries))
	startOfPastWeek := startOfThisWeek
	g, _ := errgroup.WithContext(ctx)
	for w, mapEntry := range rewardClientIdsMapEntries {
		weekNo := w                                                   // local variable for use in goroutine
		weeklyRewardClientIds := mapEntry.GetRewardClientRequestIds() // local variable for use in goroutine
		startOfPastWeek = startOfPastWeek.Add(-7 * 24 * time.Hour)
		if startOfPastWeek.Before(time.Unix(s.conf.Fittt().FiSavingsLeague().StartDateOfTournament(), 0)) {
			startOfPastWeek = time.Unix(s.conf.Fittt().FiSavingsLeague().StartDateOfTournament(), 0)
		}
		weeklyRewardsEarned[weekNo] = &WeeklyRewardsEarned{}
		weeklyRewardsEarned[weekNo].startTime = startOfPastWeek
		g.Go(func() error {
			// fetching rewards earned each week
			thisWeekRewardsSummary, gerr := s.rewardsClient.GetRewardSummary(ctx, &rewardsPb.GetRewardSummaryRequest{
				ActorId: actorId,
				Filter:  &rewardsPb.GetRewardSummaryRequest_Filter{ExternalRefIds: weeklyRewardClientIds},
			})
			if err2 := epifigrpc.RPCError(thisWeekRewardsSummary, gerr); err2 != nil {
				logger.Error(ctx, "error in fetching this weeks rewards summary", zap.Error(err2))
				return err
			}
			logger.Debug(ctx, "Rewards Digest", zap.Any("weekly reward ids", weeklyRewardClientIds), zap.Any("summary", thisWeekRewardsSummary))
			weeklyRewardsEarned[weekNo].fiCoinsEarned = thisWeekRewardsSummary.GetTotalFiCoinsEarned()
			weeklyRewardsEarned[weekNo].cashbackEarned = thisWeekRewardsSummary.GetTotalCashRewardEarned().GetUnits()
			return nil
		})
	}
	err = g.Wait()
	if err != nil {
		logger.Error(ctx, "error calculating rewards for past weeks", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		return nil, err
	}
	return weeklyRewardsEarned, nil
}

func (s *Service) GetSportsChallengeHomePageLeaderboard(ctx context.Context, matches *MatchesForSportsChallengeScreen,
	actorId string) (*fePb.Leaderboard, error) {
	userRankings, err := s.getWeeklyLeaderboardUserRankings(ctx, matches, actorId)
	if err != nil {
		logger.Error(ctx, "failed to get user rankings for weekly leaderboard", zap.Error(err))
		return nil, err
	}

	leaderboardRefreshTimeStr := getTimeDisplayString(s.conf.Fittt().FiSavingsLeague().LeaderboardRefreshTime().Hours(),
		s.conf.Fittt().FiSavingsLeague().LeaderboardRefreshTime().Minutes())
	refreshText := fmt.Sprintf("Leaderboard refreshes daily at %s", leaderboardRefreshTimeStr)
	if len(userRankings) == 0 {
		refreshText = "Leaderboard will appear after the first match results are in"
	}

	return &fePb.Leaderboard{
		Id: "weekly_leaderboard",
		Title: &fePb.Text{
			Text: "Weekly Leaderboard",
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#333333",
			},
		},
		UserRankings: userRankings,
		Info: &fePb.Text{
			Text: refreshText,
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#A4A4A4",
			},
		},
	}, nil
}

func (s *Service) getCurrentWeekNoForFiSavingsLeague() uint32 {
	// maintaining second week start time to calculate weekNo
	// not using tournament start time, as first week of IPL2022 returned from vendor is of 8 days i.e. from Saturday to Saturday (both inclusive)
	// so using second week start timestamp to calculate further weeks
	// difference in seconds between start time and current time
	timeSinceStart := time.Now().In(datetime.IST).Unix() - s.conf.Fittt().FiSavingsLeague().TournamentSecondWeekStartTimestamp()
	return 1 /* first week */ + secondsToWeek(timeSinceStart) + 1 // +1 for the current week
}

// converts seconds to weeks
// does not consider ongoing week, since it has not been completed yet
func secondsToWeek(timeSinceStart int64) uint32 {
	return uint32(timeSinceStart / 604800) // 60(minute) * 60(hour) * 24(day) * 7(week)
}

func (s *Service) getWeeklyLeaderboardUserRankings(ctx context.Context, matches *MatchesForSportsChallengeScreen, actorId string) ([]*fePb.Leaderboard_UserRankings, error) {
	weekNo := s.getCurrentWeekNoForFiSavingsLeague()
	m := getOngoingOrPreviousMatch(matches.ongoingOrEndedMatches, matches.executedMatches)
	if matches.openToPlayMatch != nil &&
		m != nil &&
		// if previous or ongoing match is not on same week
		m.GetMatchData().GetWeekNo() != matches.openToPlayMatch.GetMatchData().GetWeekNo() &&
		// and next match is less than `LeaderboardResetDurationBeforeFirstMatchOfTheWeek`, show leaderboard for previous week
		time.Until(matches.openToPlayMatch.GetExpectedStartTime().AsTime()) >= s.conf.Fittt().FiSavingsLeague().LeaderboardResetDurationBeforeFirstMatchOfTheWeek() {
		logger.Debug(ctx, "showing previous week's leaderboard", zap.Time(logger.START_TIME, matches.openToPlayMatch.GetExpectedStartTime().AsTime()))
		weekNo = m.GetMatchData().GetWeekNo()
	}

	leaderboardResp, err := s.fitttClient.GetTournamentLeaderboard(ctx, &fittt.GetTournamentLeaderboardRequest{
		ActorId:         actorId,
		TournamentTagId: s.conf.Fittt().FiSavingsLeague().TournamentTagId(),
		RankLimit:       s.conf.Fittt().FiSavingsLeague().LeaderboardRankLimit(),
		WeekNo:          weekNo,
	})
	if err2 := epifigrpc.RPCError(leaderboardResp, err); err2 != nil {
		logger.Error(ctx, "error in getting leaderboard data from fittt", zap.Error(err))
		return nil, err2
	}

	userRankings, err := s.getUserRankings(ctx, leaderboardResp)
	if err != nil {
		return nil, err
	}

	return userRankings, nil
}

func getOngoingOrPreviousMatch(ongoingOrNotExecutedMatches, executedMatches []*sports.Match) *sports.Match {
	var m *sports.Match
	for _, match := range ongoingOrNotExecutedMatches {
		if m == nil || m.GetExpectedStartTime().AsTime().Before(match.GetExpectedStartTime().AsTime()) {
			m = match
		}
	}

	// if there are no ongoing or completed match whose execution did not happen
	// getting the latest executed match
	if m == nil {
		for _, match := range executedMatches {
			if m == nil || m.GetExpectedStartTime().AsTime().Before(match.GetExpectedStartTime().AsTime()) {
				m = match
			}
		}
	}
	return m
}

func (s *Service) getActorNameMap(ctx context.Context, actorIds []string) (map[string]string, error) {
	actorDetailsResp, err := s.actorClient.GetEntityDetails(ctx, &actor.GetEntityDetailsRequest{ActorIds: actorIds})
	if err2 := epifigrpc.RPCError(actorDetailsResp, err); err2 != nil {
		logger.Error(ctx, "error in getting actor details for leaderboard", zap.Error(err2), zap.Strings(logger.ACTOR_ID, actorIds))
		return nil, err2
	}

	actorNameMap := make(map[string]string)
	for _, a := range actorDetailsResp.GetEntityDetails() {
		actorNameMap[a.GetActorId()] = a.GetName().GetFirstName()
	}
	return actorNameMap, nil
}

// nolint: funlen
func (s *Service) getUserRankings(ctx context.Context, leaderboardResp *fittt.GetTournamentLeaderboardResponse) ([]*fePb.Leaderboard_UserRankings, error) {
	if len(leaderboardResp.GetContestantsByRank()) == 0 {
		logger.Debug(ctx, "no contestants present")
		return nil, nil
	}

	var actorIds []string
	for _, l := range leaderboardResp.GetContestantsByRank() {
		actorIds = append(actorIds, l.GetActorId())
	}

	actorNameMap, err := s.getActorNameMap(ctx, actorIds)
	if err != nil {
		logger.Error(ctx, "error in getting actor name map", zap.Error(err))
		return nil, err
	}

	var userRankings []*fePb.Leaderboard_UserRankings
	var userAlreadyAdded bool

	// adding top `n` users  to userRanking list
	for itr := 0; itr < len(leaderboardResp.GetContestantsByRank()) &&
		itr < int(s.conf.Fittt().FiSavingsLeague().LeaderboardRankLimit()); itr++ {
		l := leaderboardResp.GetContestantsByRank()[itr]

		actorName := actorNameMap[l.GetActorId()]
		if leaderboardResp.GetUserStats().GetActorId() == l.GetActorId() {
			userAlreadyAdded = true
			actorName = "You"
		}

		userRankings = append(userRankings, &fePb.Leaderboard_UserRankings{
			Id:              l.GetActorId(),
			UserName:        &fePb.Text{Text: actorName},
			UserRankingText: nil,
			PointsEarned:    &fePb.Text{Text: fmt.Sprintf("%d<br><font color=\"#8D8D8D\">points</font>", l.GetPoints())},
			DisplayInfo: &fePb.CardDisplayInfo{
				ImgUrl: fmt.Sprintf("https://epifi-icons.pointz.in/fittt-images/sports-challenge/weekly-leaderboard-%d.png", itr+1),
			},
		})
	}

	if userAlreadyAdded || isAlreadyAWinnerInPreviousWeeks(leaderboardResp.GetUserStats().GetRank(), leaderboardResp.GetUserStats().GetPoints()) {
		// user has non-zero points but not ranked. Happens when the user has already won in previous weeks.
		return userRankings, nil
	}

	rankText := fmt.Sprintf("#%d", leaderboardResp.GetUserStats().GetRank())

	if leaderboardResp.GetUserStats().GetRank() == 0 {
		rankText = "-"
	}
	// if user has participated but not among top n users
	userRankings = append(userRankings, &fePb.Leaderboard_UserRankings{
		Id:              leaderboardResp.GetUserStats().GetActorId(),
		UserName:        &fePb.Text{Text: "You"},
		UserRankingText: &fePb.Text{Text: rankText},
		PointsEarned:    &fePb.Text{Text: fmt.Sprintf("%d<br><font color=\"#8D8D8D\">points</font>", leaderboardResp.GetUserStats().GetPoints())},
		DisplayInfo: &fePb.CardDisplayInfo{
			ImgUrl: "https://epifi-icons.pointz.in/fittt-images/sports-challenge/weekly-leaderboard-4.png",
		},
	})
	return userRankings, nil
}

func isAlreadyAWinnerInPreviousWeeks(rank, points uint32) bool {
	return rank == 0 && points != 0
}

func (s *Service) getSportsChallengeHomePageBannersAndClaimPrizeScreenData(ctx context.Context, actorId, tournamentId string,
	matches *MatchesForSportsChallengeScreen) ([]*fePb.BannerCard, *fePb.ClaimPrizePageData, error) {

	g, _ := errgroup.WithContext(ctx)
	var dailyDrawCard, weeklyDrawCard *fePb.BannerCard
	var claimPrizeScreenData *fePb.ClaimPrizePageData
	var qualifiedForWeeklyReward bool
	g.Go(func() error {
		weeklyDrawCardLocal, claimPrizeScreenDataLocal, qualifiedForWeeklyRewardLocal, err := s.getWeeklyDrawCardBasedOnConditionsAndClaimPrizeScreenData(ctx, actorId, tournamentId)
		if err != nil {
			logger.Error(ctx, "Error fetching weekly draw card", zap.Error(err))
			return err
		}
		weeklyDrawCard = weeklyDrawCardLocal
		claimPrizeScreenData = claimPrizeScreenDataLocal
		qualifiedForWeeklyReward = qualifiedForWeeklyRewardLocal
		return nil
	})

	g.Go(func() error {
		dailyDrawCardLocal, err := s.getDailyDrawCardBasedOnConditions(ctx, actorId, matches)
		if err != nil {
			logger.Error(ctx, "Error fetching daily draw card", zap.Error(err))
			return err
		}
		dailyDrawCard = dailyDrawCardLocal
		return nil
	})
	err := g.Wait()
	if err != nil {
		logger.Error(ctx, "Error fetching banner cards", zap.Error(err))
		return nil, nil, err
	}

	var bannersList []*fePb.BannerCard

	if s.conf.Fittt().FiSavingsLeague().ShowDefaultBanner() {
		bannersList = append(bannersList, FSLDefaultBanner)
	}

	if qualifiedForWeeklyReward || s.conf.Fittt().FiSavingsLeague().ShouldShowWeeklyRewardsBannerFirst() {
		if dailyDrawCard != nil {
			bannersList = append(bannersList, dailyDrawCard)
		}
		if weeklyDrawCard != nil {
			bannersList = append(bannersList, weeklyDrawCard)
		}
		// show weekly banner before daily banner if user has won reward or configured in yml
		return bannersList, claimPrizeScreenData, nil
	}

	if weeklyDrawCard != nil {
		bannersList = append(bannersList, weeklyDrawCard)
	}

	if dailyDrawCard != nil {
		bannersList = append(bannersList, dailyDrawCard)
	}

	if s.conf.Fittt().FiSavingsLeague().ShowInvestInTheTeamBanner() {
		bannersList = append(bannersList, CricketRuleAutoInvestBanner)
	}

	return bannersList, claimPrizeScreenData, nil
}

// nolint: funlen
func (s *Service) getDailyDrawCardBasedOnConditions(ctx context.Context, actorId string, matches *MatchesForSportsChallengeScreen) (*fePb.BannerCard, error) {
	if !s.conf.Fittt().FiSavingsLeague().ShowDailyBanner() {
		return nil, nil
	}
	showZeroStateForDailyDraw, err := s.shouldShowDailyDrawZeroStateBanner(ctx, actorId, matches)
	if err != nil {
		return nil, err
	}
	if showZeroStateForDailyDraw {
		logger.Debug(ctx, "showing Daily draw zero state banner")
		return s.getDailyDrawCardFromState(DAILY_DRAW_ZERO_STATE), nil
	}

	// all conditions for checking daily draw are met
	luckyDrawBannerState, amtWon, err := s.getDailyLuckyDrawState(ctx, actorId, matches.executedMatches)
	if err != nil {
		return nil, err
	}
	if luckyDrawBannerState != DAILY_DRAW_INVALID_STATE {
		logger.Debug(ctx, "Daily Draw Banner", zap.Any("getDailyLuckyDrawState", luckyDrawBannerState))
		if luckyDrawBannerState == DAILY_DRAW_WON_CASH {
			return getDailyLuckyDrawWonCard(amtWon), nil
		}
		return s.getDailyDrawCardFromState(luckyDrawBannerState), nil
	}

	logger.Debug(ctx, "Daily Draw Banner - Default")
	return s.getDailyDrawCardFromState(DAILY_DRAW_ZERO_STATE), nil
}

func (s *Service) isLuckyDrawAlreadyRevealed() bool {
	luckyDrawRevealHours := s.conf.Fittt().FiSavingsLeague().LuckyDrawRevealCutOffTime().Hours()
	luckyDrawRevealMinutes := s.conf.Fittt().FiSavingsLeague().LuckyDrawRevealCutOffTime().Minutes()

	luckyDrawRevealTime := datetime.StartOfDay(time.Now()).
		Add(time.Duration(luckyDrawRevealHours) * time.Hour).
		Add(time.Duration(luckyDrawRevealMinutes) * time.Minute)

	return luckyDrawRevealTime.Before(time.Now())
}

func (s *Service) shouldShowDailyDrawZeroStateBanner(ctx context.Context, actorId string, matches *MatchesForSportsChallengeScreen) (bool, error) {

	// if there is an ongoing or ended match, show zero state card
	if len(matches.ongoingOrEndedMatches) > 0 {
		logger.Debug(ctx, "ongoing matches, showing daily draw zero state card")
		return true, nil
	}

	// if the openToPlayMatch starts in less than configured hrs, show zero state card
	if matches.openToPlayMatch != nil {
		if time.Until(matches.openToPlayMatch.GetExpectedStartTime().AsTime()) < s.conf.Fittt().FiSavingsLeague().LuckyDrawZerothCardDurationBeforeMatchStart() {
			logger.Debug(ctx, "next match starts in less than 2 hrs, showing daily draw zero state card")
			return true, nil
		}
	}

	// if the current time is before the lucky draw reveal time, show zero state card
	if !s.isLuckyDrawAlreadyRevealed() {
		logger.Debug(ctx, "today lucky draw hasn't been revealed yet, showing daily draw zero state card")
		return true, nil
	}

	// the code reached here means there is no ongoing match or
	// the time to openToPlay match is > set duration
	// e.g this might be the case when time b/w luckydraw reveal and next match
	// if the user has no subscriptions for executed matches, show not qualified card
	hasSubscription, err := s.hasSubscriptionForMatches(ctx, actorId, matches.executedMatches)
	if err != nil {
		return false, err
	}

	// if the user did not have any previous day match subscriptions, show zero state card
	if !hasSubscription {
		logger.Debug(ctx, "user did not have any subscriptions for previous day matches")
		return true, nil
	}

	return false, nil
}

func (s *Service) hasSubscriptionForMatches(ctx context.Context, actorId string, matches []*sports.Match) (bool, error) {
	for _, match := range matches {
		activeSubsResp, err := s.getActiveSubscriptionsForMatch(ctx, match, actorId)
		if err != nil {
			logger.Error(ctx, "failed to fetch active subscriptions for executed match", zap.Error(err),
				zap.String(logger.MATCH_ID, match.GetVendorMatchId()))
			return false, err
		}

		// knowing if the user has subscribed to atleast any one rule from yesterday's match
		for _, subsList := range activeSubsResp {
			if len(subsList.GetRuleSubscriptions()) > 0 {
				return true, nil
			}
		}
	}
	return false, nil
}

func (s *Service) getDailyLuckyDrawState(ctx context.Context, actorId string, prevDayMatches []*sports.Match) (DailyDrawCardState, int64, error) {
	// finding all unique reference ids
	rewardsReferenceMap := make(map[string]bool)
	for _, match := range prevDayMatches {
		rewardsReferenceMap[match.GetMatchData().GetRewardsClientRequestId()] = true
	}
	rewardsReferenceList := make([]string, 0)
	for key := range rewardsReferenceMap {
		rewardsReferenceList = append(rewardsReferenceList, key)
	}
	logger.Debug(ctx, "rewards reference list", zap.Strings("referenceIds", rewardsReferenceList))
	if len(rewardsReferenceList) == 0 {
		return DAILY_DRAW_INVALID_STATE, 0, nil
	}

	rewardForPrevMatchesResp, err := s.rewardsClient.GetRewardsByActorId(ctx, &rewardsPb.RewardsByActorIdRequest{
		ActorId: actorId,
		Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
			ExternalRefList: rewardsReferenceList,
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: uint32(len(rewardsReferenceList)),
		},
	})
	if err2 := epifigrpc.RPCError(rewardForPrevMatchesResp, err); err2 != nil {
		logger.Error(ctx, "Failed to fetch rewards for yesterday's matches", zap.Error(err2))
		return DAILY_DRAW_INVALID_STATE, 0, err
	}
	logger.Debug(ctx, "Rewards Response", zap.Any("Rewards for previous match", rewardForPrevMatchesResp))

	for _, reward := range rewardForPrevMatchesResp.GetRewards() {
		switch reward.GetStatus() {
		case rewardsPb.RewardStatus_PROCESSING_FAILED:
			logger.Info(ctx, "rewards processing failed, returning zero state card")
			return DAILY_DRAW_ZERO_STATE, 0, nil
		case rewardsPb.RewardStatus_PROCESSED:
			for _, rewardOption := range reward.GetRewardOptions().GetOptions() {
				if rewardOption.GetRewardType() == rewardsPb.RewardType_LUCKY_DRAW {
					// reward type is only lucky draw for Fi Savings league
					logger.Debug(ctx, "user has chance to win lucky draw")
					luckyDrawState, amountWon, err2 := s.getDailyLuckyDrawStateUtil(ctx, actorId, rewardOption.GetLuckyDraw().GetLuckyDrawId())
					if err2 != nil {
						return DAILY_DRAW_INVALID_STATE, 0, err2
					}
					return luckyDrawState, amountWon, nil
				}
			}
		default:
			logger.Debug(ctx, "rewards state not handled", zap.String(logger.STATE, reward.GetStatus().String()))
			return DAILY_DRAW_INVALID_STATE, 0, nil
		}
	}
	// if the rewards for the user is nil, he hasn't qualified for them
	return DAILY_DRAW_NOT_QUALIFIED, 0, nil
}

func (s *Service) getDailyLuckyDrawStateUtil(ctx context.Context, actorId, luckyDrawId string) (DailyDrawCardState, int64, error) {
	registrationStatusResp, err := s.luckyDrawClient.CheckRegistrationStatus(ctx, &luckyDrawPb.CheckRegistrationStatusRequest{
		LuckyDrawId: luckyDrawId,
		ActorId:     actorId,
	})
	if err2 := epifigrpc.RPCError(registrationStatusResp, err); err2 != nil {
		logger.Error(ctx, "failed to fetch client lucky draw reg resp", zap.String("Lucky draw id", luckyDrawId), zap.Error(err2))
		return DAILY_DRAW_INVALID_STATE, 0, err
	}
	logger.Debug(ctx, "lucky draw registration status resp", zap.Any("registrationStatusResp", registrationStatusResp))
	luckyDrawRegistrationId := registrationStatusResp.GetRegistrationId()
	luckyDrawWinningResp, err := s.luckyDrawClient.GetLuckyDrawWinning(ctx, &luckyDrawPb.GetLuckyDrawWinningRequest{LuckyDrawRegistrationId: luckyDrawRegistrationId})
	if err2 := epifigrpc.RPCError(luckyDrawWinningResp, err); err2 != nil {
		logger.Error(ctx, "failed to fetch lucky draw winning resp", zap.Error(err2), zap.String("Lucky draw registration id", luckyDrawRegistrationId))
		return DAILY_DRAW_INVALID_STATE, 0, err
	}
	logger.Debug(ctx, "lucky draw winning resp", zap.Any("luckyDrawWinningResp", luckyDrawWinningResp))
	luckyDrawWinning := luckyDrawWinningResp.GetLuckyDrawWinning()
	switch luckyDrawWinning.GetRewardStatus() {
	case luckyDrawPb.RewardStatus_CREATED:
		logger.Debug(ctx, "user has won Fi coins for lucky draw")
		return DAILY_DRAW_WON_MONEY_PLANT, 0, nil
	case luckyDrawPb.RewardStatus_PROCESSING_FAILED:
		logger.Info(ctx, "lucky draw processing failed, returning zero state card")
		return DAILY_DRAW_ZERO_STATE, 0, nil
	default:
		// lucky draw is in PROCESSED, PROCESSING_IN_PROGRESS or other states
		switch luckyDrawWinning.GetRewardType() {
		case rewardsPb.RewardType_FI_COINS:
			logger.Info(ctx, "user has won Fi coins for lucky draw")
			return DAILY_DRAW_WON_FI_COINS, int64(luckyDrawWinning.GetReward().GetFiCoins().GetUnits()), nil
		case rewardsPb.RewardType_CASH:
			logger.Info(ctx, "user has won 750 cashback in lucky draw!")
			return DAILY_DRAW_WON_CASH, luckyDrawWinning.GetReward().GetCash().GetAmount().GetUnits(), nil
		default:
			logger.Info(ctx, "reward type invalid")
		}
	}
	return DAILY_DRAW_INVALID_STATE, 0, nil
}

// nolint: dupl
func getDailyLuckyDrawWonCard(wonAmount int64) *fePb.BannerCard {
	return &fePb.BannerCard{
		Id: "lucky_draw_won",
		Title: &fePb.Text{
			Text: "You won ₹" + fmt.Sprint(wonAmount) + " cash!",
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#478295",
				BgColor:   "#DEEEF2",
			},
		},
		CardDisplayInfo: &fePb.CardDisplayInfo{
			BgColor: "#C0DAE0",
			ImgUrl:  "https://epifi-icons.pointz.in/fittt-images/sports-challenge/daily-lucky-draw-won.png",
		},
		CardCta: &fePb.CTA{
			Action: &deeplinkpb.Deeplink{
				Screen:        deeplinkpb.Screen_MY_REWARDS_SCREEN,
				ScreenOptions: nil,
			}},
		Header: &fePb.Text{
			Text: "DAILY DRAW RESULTS ARE IN",
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#478295",
			},
		},
		SubTitle: &fePb.Text{
			Text:        "You're one of the Lucky 10",
			DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#333333"},
		},
		Description: &fePb.Text{
			Text:        "Keep playing to earn points and win more rewards",
			DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#383838"},
		},
	}
}

func (s *Service) getDailyDrawCardFromState(state DailyDrawCardState) *fePb.BannerCard {
	myRewardsDeeplink := &deeplinkpb.Deeplink{
		Screen:        deeplinkpb.Screen_MY_REWARDS_SCREEN,
		ScreenOptions: nil,
	}
	waysToEarnRewardsDeeplink := &deeplinkpb.Deeplink{
		Screen:        deeplinkpb.Screen_REWARDS_WAYS_TO_EARN,
		ScreenOptions: nil,
	}
	allCollectionsDeeplink := &deeplinkpb.Deeplink{
		Screen:        deeplinkpb.Screen_FIT_ALL_COLLECTIONS_PAGE,
		ScreenOptions: &deeplinkpb.Deeplink_FitAllCollectionsPageScreenOptions{FitAllCollectionsPageScreenOptions: &deeplinkpb.FitAllCollectionsPageScreenOptions{CollectionType: fePb.CollectionType_COLLECTION_TYPE_AUTO_SAVE.String()}},
	}
	endOfTheTournament := time.Unix(s.conf.Fittt().FiSavingsLeague().EndDateOfTournament(), 0).In(datetime.IST)
	if time.Now().After(endOfTheTournament.Add(24 * time.Hour)) {
		// redirecting users to Fittt from daily rewards
		return getDailyRewardBannerCardTemplate("Explore FIT Rules", "WANT TO SAVE MORE?", "Save and Earn more rewards",
			"Keep up your savings streak by<br> setting up new FIT Rules", allCollectionsDeeplink, nil)
	}
	// TODO(scocoyash): won cash state is not handled as UI is different, kept in in different func for now
	switch state {
	case DAILY_DRAW_ZERO_STATE:
		luckyDrawRevealTimeString := getTimeDisplayString(s.conf.Fittt().FiSavingsLeague().LuckyDrawRevealCutOffTime().Hours(),
			s.conf.Fittt().FiSavingsLeague().LuckyDrawRevealCutOffTime().Minutes())
		nextDrawTimingString := fmt.Sprintf("NEXT DRAW TOMORROW AT %s", luckyDrawRevealTimeString)
		if time.Now().In(datetime.IST).Hour() < 12 {
			nextDrawTimingString = fmt.Sprintf("NEXT DRAW AT %s", luckyDrawRevealTimeString)
		}
		return getDailyRewardBannerCardTemplate("Daily lucky draw", nextDrawTimingString, "10 lucky users win ₹750 cashback",
			"Save twice daily to enter the draw & <br>win guaranteed Fi-Coins <b>Know more</b>", waysToEarnRewardsDeeplink, &fePb.CTA{Id: "know_more_cta"})
	case DAILY_DRAW_NOT_QUALIFIED:
		return getDailyRewardBannerCardTemplate("Not enough points", "DAILY DRAW RESULTS ARE IN", "You need more points to qualify",
			"Keep playing to earn point. Select <br>another team & player squad.", waysToEarnRewardsDeeplink, nil)
	case DAILY_DRAW_WON_MONEY_PLANT:
		return getDailyRewardBannerCardTemplate("You won a Money-Plant!", "DAILY DRAW RESULTS ARE IN", "Want to see what you’ve won?",
			"Tap to grow your Money-Plant & <br>claim your prize! ", myRewardsDeeplink, nil)
	case DAILY_DRAW_WON_FI_COINS:
		return getDailyRewardBannerCardTemplate("You won Fi-coins!", "DAILY DRAW RESULTS ARE IN", "Spend them on cool rewards",
			"Looking for cash? Play again and stand <br>a chance to win up to ₹750", myRewardsDeeplink, nil)
	default:
		logger.DebugNoCtx("invalid daily draw card state")
		return nil
	}
}

func getTimeDisplayString(hour, minute int) string {
	suffix := "AM"
	if hour >= 12 {
		suffix = "PM"
	}
	if hour > 12 {
		hour %= 12
	}

	luckyDrawRevealTimeString := fmt.Sprintf("%02d:%02d %s", hour, minute, suffix)
	return luckyDrawRevealTimeString
}

func getDailyRewardBannerCardTemplate(titleText, drawStatusText, subtitleText, descriptionText string,
	deeplink *deeplinkpb.Deeplink, knowMoreCTA *fePb.CTA) *fePb.BannerCard {
	return &fePb.BannerCard{
		Id:    "daily_rewards_banner",
		Title: &fePb.Text{Text: titleText, DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#4F71AB", BgColor: "#D1DAF1"}},
		CardDisplayInfo: &fePb.CardDisplayInfo{
			BgColor: "#BBC8E9",
			ImgUrl:  "https://epifi-icons.pointz.in/fittt-images/sports-challenge/plant-banner.png",
		},
		CardCta:     &fePb.CTA{Action: deeplink},
		Header:      &fePb.Text{Text: drawStatusText, DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#4F71AB"}},
		SubTitle:    &fePb.Text{Text: subtitleText, DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#333333"}},
		Description: &fePb.Text{Text: descriptionText, DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#383838"}},
		KnowMoreCta: knowMoreCTA,
	}
}

func getFiSavingsLeagueRuleIds(conf *genconf.Config) []string {
	return []string{
		conf.Fittt().FiSavingsLeague().Rule1().Id(),
		conf.Fittt().FiSavingsLeague().Rule2().Id(),
		conf.Fittt().FiSavingsLeague().Rule3().Id(),
	}
}

func (s *Service) getActiveSubscriptionsForMatch(ctx context.Context, match *sports.Match, actorId string) (map[string]*manager.Subscriptions, error) {
	if match == nil {
		logger.Error(ctx, "match cannot be nil to fetch active subscriptions")
		return nil, fmt.Errorf("match cannot be nil to fetch active subscriptions")
	}

	subsResp, err := s.rmClient.GetActiveSubscriptionsForTimeInterval(ctx, &manager.GetActiveSubscriptionsForTimeIntervalRequest{
		RuleIds:   getFiSavingsLeagueRuleIds(s.conf),
		ActorId:   actorId,
		StartTime: match.GetExpectedStartTime(),
		EndTime:   match.GetExpectedStartTime(),
		ParamFilters: []*manager.ParamFilter{
			{
				ParamType: manager.RuleParamType_CRICKET_TEAM,
				Key:       match.GetMatchData().GetOpponents().GetOpponents()[0].GetTeam().GetTeamId(),
			},
			{
				ParamType: manager.RuleParamType_CRICKET_TEAM,
				Key:       match.GetMatchData().GetOpponents().GetOpponents()[1].GetTeam().GetTeamId(),
			},
		},
	})
	if err2 := epifigrpc.RPCError(subsResp, err); err2 != nil {
		logger.Error(ctx, "error in getting active subscriptions", zap.String(logger.MATCH_ID, match.GetId()), zap.Error(err2))
		return nil, err2
	}

	return subsResp.GetSubscriptionsMap(), nil
}

// nolint:funlen
func (s *Service) GetSportsChallengeMatchDetailsPageData(ctx context.Context, req *fePb.GetSportsChallengeMatchDetailsPageDataRequest) (*fePb.GetSportsChallengeMatchDetailsPageDataResponse, error) {
	matchFieldMask, err := getMatchFieldMasks(ctx)
	if err != nil {
		logger.Error(ctx, "error in generating field mask for match", zap.Error(err))
		return &fePb.GetSportsChallengeMatchDetailsPageDataResponse{RespHeader: errors.GetFitFullScreenErrorResponseHeader(err, errors.FIT0006)}, nil
	}

	matchResp, err := s.fitttSportsClient.GetMatchesByIds(ctx, &sports.GetMatchesByIdsRequest{
		MatchIds:  []string{req.GetMatchId()},
		FieldMask: matchFieldMask,
	})
	if err2 := epifigrpc.RPCError(matchResp, err); err2 != nil {
		logger.Error(ctx, "error in getting matches from fittt", zap.String(logger.MATCH_ID, req.GetMatchId()), zap.Error(err2))
		return &fePb.GetSportsChallengeMatchDetailsPageDataResponse{
			RespHeader: errors.GetFitFullScreenErrorResponseHeader(err2, errors.FIT0006),
		}, nil
	}

	match := matchResp.GetMatches()[req.GetMatchId()]
	if match == nil {
		logger.Error(ctx, "no match with given id exists in backend", zap.String(logger.MATCH_ID, req.GetMatchId()))
		return &fePb.GetSportsChallengeMatchDetailsPageDataResponse{
			RespHeader: errors.GetFitFullScreenErrorResponseHeader(err, errors.FIT0006),
		}, nil
	}
	logger.Debug(ctx, "match received from fittt", zap.Any("match", match), zap.String(logger.MATCH_ID, req.GetMatchId()))

	subsMap, err := s.getActiveSubscriptionsForMatch(ctx, match, req.GetReq().GetAuth().GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting active subscriptions", zap.String(logger.MATCH_ID, req.GetMatchId()), zap.Error(err))
		return &fePb.GetSportsChallengeMatchDetailsPageDataResponse{
			RespHeader: errors.GetFitFullScreenErrorResponseHeader(err, errors.FIT0006),
		}, nil
	}
	logger.Debug(ctx, "all active subscriptions for match", zap.String(logger.MATCH_ID, req.GetMatchId()), zap.Any("subscription map", subsMap))

	ruleCards, err := s.getRuleCardsForMatchDetailsPage(ctx, subsMap, match, req.GetReq().GetAuth().GetActorId())
	if err != nil {
		logger.Error(ctx, "error in getting rule cards for match details page", zap.String(logger.MATCH_ID, req.GetMatchId()), zap.Error(err))
		return &fePb.GetSportsChallengeMatchDetailsPageDataResponse{
			RespHeader: errors.GetFitFullScreenErrorResponseHeader(err, errors.FIT0006),
		}, nil
	}

	subscriptionPreRequisiteAction := fePb.SubscriptionPreRequisiteActionType_SHOW_FYI_MESSAGE_BOX_FOR_PAUSING_EXISTING_SUBS
	var subscriptionPreRequisiteActionValue *fePb.SubscriptionPreRequisiteActionTypeValue
	if len(ruleCards[0].GetPossibleParams().GetPossibleSdValues()) == 0 {
		subscriptionPreRequisiteAction = fePb.SubscriptionPreRequisiteActionType_CREATE_SD_ACCOUNT
		// add create SD account value which contains the details to be shown in the bottom sheet
		subscriptionPreRequisiteActionValue = &fePb.SubscriptionPreRequisiteActionTypeValue{
			ActionType: &fePb.SubscriptionPreRequisiteActionTypeValue_CreateSdAccountValue{
				CreateSdAccountValue: getCreateSdPreReqActionValue(),
			},
		}
	}

	startsAt := match.GetExpectedStartTime().AsTime().In(datetime.IST)
	startsAtString := strings.ToUpper(startsAt.Format("02 Jan 03:04 PM"))
	if isSameDay(startsAt, time.Now().In(datetime.IST)) {
		// same day
		startsAtString = strings.ToUpper(startsAt.Format("03:04 PM"))
	}

	return &fePb.GetSportsChallengeMatchDetailsPageDataResponse{
		RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusOk()},
		// Title:      &fePb.Text{Text: fmt.Sprintf("Week %d • Match %d", match.GetMatchData().GetWeekNo(), match.GetMatchData().GetMatchNo())},
		Title: &fePb.Text{Text: fmt.Sprintf("Match %d", match.GetMatchData().GetMatchNo())},
		SubTitle: &fePb.Text{Text: fmt.Sprintf("%s VS %s • STARTS AT %s", strings.ToUpper(match.GetMatchData().GetOpponents().GetOpponents()[0].GetTeam().GetCode()),
			strings.ToUpper(match.GetMatchData().GetOpponents().GetOpponents()[1].GetTeam().GetCode()), startsAtString)},
		Cards:                              ruleCards,
		BgColor:                            "#4F71AB",
		MatchStartTime:                     match.GetExpectedStartTime(),
		PreRequisiteStepBeforeSubscription: subscriptionPreRequisiteAction,
		PreRequisiteStepsValue:             subscriptionPreRequisiteActionValue,
	}, nil
}

func getCricketSubscriptionUniqueParam(sub *manager.RuleSubscription) (*fePb.Value, string) {
	for _, v := range sub.GetRuleParamValues().GetRuleParamValues() {
		switch {
		case v.GetPlayerVal() != nil:
			return &fePb.Value{
				Value: &fePb.Value_PlayerValue{
					PlayerValue: &fePb.CricketPlayerValue{
						PlayerId:   v.GetPlayerVal().GetId(),
						PlayerName: v.GetPlayerVal().GetName(),
						PlayerPic:  v.GetPlayerVal().GetLogo(),
						Team: &fePb.CricketTeamValue{
							TeamId:          v.GetPlayerVal().GetTeam().GetId(),
							TeamName:        v.GetPlayerVal().GetTeam().GetName(),
							TeamLogo:        v.GetPlayerVal().GetTeam().GetLogo(),
							AbbreviatedName: v.GetPlayerVal().GetTeam().GetAbbrName(),
						},
						PlayerType:  fePb.CricketPlayerType(fePb.CricketPlayerType_value[v.GetPlayerVal().GetPlayerType().String()]),
						DisplayName: GetPlayerDisplayName(v.GetPlayerVal().GetName()),
					}},
				Id:         v.GetPlayerVal().GetId(),
				IsSelected: true,
			}, v.GetPlayerVal().GetId()
		case v.GetTeamVal() != nil:
			return &fePb.Value{
				Value: &fePb.Value_TeamValue{
					TeamValue: &fePb.CricketTeamValue{
						TeamId:          v.GetTeamVal().GetId(),
						TeamName:        v.GetTeamVal().GetName(),
						TeamLogo:        v.GetTeamVal().GetLogo(),
						AbbreviatedName: v.GetTeamVal().GetAbbrName(),
					}},
				Id:         v.GetTeamVal().GetId(),
				IsSelected: true,
			}, v.GetTeamVal().GetId()
		}
	}
	return nil, ""
}

func (s *Service) getCricketSubscribedParams(ctx context.Context, subsMap map[string]*manager.Subscriptions) ([]*fePb.Value,
	map[string]map[string]*fePb.SdParamValue, map[string][]*manager.Value, error) {
	if len(subsMap) == 0 {
		return nil, make(map[string]map[string]*fePb.SdParamValue), nil, nil
	}

	var subscribedParams []*fePb.Value
	// map[ruleId]map[uniqueParamName]*fePb.Value
	subscribedParamIdToSDMap := make(map[string]map[string]*fePb.SdParamValue)
	// map[ruleId][sdAccountId]*manager.Value
	sdParams := make(map[string][]*manager.Value)
	sdDedupeMap := make(map[string]map[string]bool)
	for _, ruleId := range getFiSavingsLeagueRuleIds(s.conf) {
		for _, sub := range subsMap[ruleId].GetRuleSubscriptions() {
			sdParam := getSdParam(sub)
			if sdDedupeMap[ruleId] == nil {
				sdDedupeMap[ruleId] = make(map[string]bool)
			}

			if !sdDedupeMap[ruleId][sdParam.GetSdValue().GetAccountId()] {
				sdParams[ruleId] = append(sdParams[ruleId], sdParam)
				sdDedupeMap[ruleId][sdParam.GetSdValue().GetAccountId()] = true
			}

			u, paramId := getCricketSubscriptionUniqueParam(sub)
			if u != nil {
				subscribedParams = append(subscribedParams, u)
				if _, present := subscribedParamIdToSDMap[ruleId]; !present {
					subscribedParamIdToSDMap[ruleId] = make(map[string]*fePb.SdParamValue)
				}
				subscribedParamIdToSDMap[ruleId][paramId] = getFeSDParam(getSdParam(sub).GetSdValue())
			} else {
				logger.Error(ctx, "did not received unique param for subscription", zap.String(logger.SUBSCRIPTION_ID, sub.GetId()))
				return nil, nil, nil, fmt.Errorf("did not received unique param for subscription, %s", sub.GetId())
			}
		}
	}
	return subscribedParams, subscribedParamIdToSDMap, sdParams, nil
}

func getFeSDParam(param *manager.SdParamValue) *fePb.SdParamValue {
	return &fePb.SdParamValue{
		Name:                param.GetName(),
		AccountId:           param.GetAccountId(),
		MaskedAccountNumber: param.GetMaskedAccountNumber(),
	}
}

func getSdParam(sub *manager.RuleSubscription) *manager.Value {
	for _, p := range sub.GetRuleParamValues().GetRuleParamValues() {
		if p.GetSdValue() != nil {
			return p
		}
	}
	return nil
}

func getMatchFieldMasks(ctx context.Context) (*fieldmaskpb.FieldMask, error) {
	matchFieldMask, err := fieldmaskpb.New(&sports.Match{}, "id", "expected_start_time", "start_time",
		"end_time", "match_data", "vendor_match_id", "vendor_tournament_id")
	if err != nil {
		logger.Error(ctx, "error in generating field mask for match", zap.Error(err))
		return nil, err
	}
	return matchFieldMask, nil
}

func splitMatchesByDay(matches []*sports.Match) (yesterdayMatches []*sports.Match, todayMatches []*sports.Match,
	futureMatches []*sports.Match) {
	today := datetime.StartOfDay(time.Now())
	yesterday := datetime.StartOfDay(today.Add(-1 * time.Hour))

	for _, m := range matches {
		matchDay := datetime.StartOfDay(m.ExpectedStartTime.AsTime())
		switch {
		case matchDay.Equal(yesterday):
			yesterdayMatches = append(yesterdayMatches, m)
		case matchDay.Equal(today):
			todayMatches = append(todayMatches, m)
		case matchDay.After(today):
			futureMatches = append(futureMatches, m)
		default: // discard - case won't occur since matches will be fetched for only three days
		}
	}
	return yesterdayMatches, todayMatches, futureMatches
}

func (s *Service) getMatchStateForLeague(m *sports.Match,
	executionBufferDuration time.Duration, events map[string]*eventpb.Event) CricketMatchStateForChallenge {
	now := time.Now().In(datetime.IST)
	expectedStart := m.ExpectedStartTime.AsTime()
	switch {
	case expectedStart.After(now):
		return MATCH_YET_TO_START
	case m.EndTime == nil || m.EndTime.AsTime().After(now):
		// match started but not ended yet
		return MATCH_IN_PROGRESS
	default:
		// match ended
		executionTriggerEvent, exist := events[m.VendorMatchId]
		if !exist {
			// execution not yet completed
			return MATCH_ENDED
		}
		expectedExecutionEnd := executionTriggerEvent.CreatedAt.AsTime().Add(executionBufferDuration)

		// if time has passed after the buffer time for execution after the execution trigger event
		if now.After(expectedExecutionEnd) {
			return MATCH_EXECUTED
		}
		// Ended but not execution is not completed yet
		return MATCH_ENDED
	}

}

func (s *Service) getMatches(ctx context.Context, from, to *timestamppb.Timestamp) ([]*sports.Match, error) {
	matchFieldMask, err := getMatchFieldMasks(ctx)
	if err != nil {
		logger.Error(ctx, "error in generating field mask for match", zap.Error(err))
		return nil, err
	}
	matchesResp, err := s.fitttSportsClient.GetMatches(ctx, &sports.GetMatchesRequest{
		TournamentTag: s.conf.Fittt().FiSavingsLeague().TournamentTagId(),
		From:          from,
		To:            to,
		FieldMask:     matchFieldMask,
	})
	if err2 := epifigrpc.RPCError(matchesResp, err); err2 != nil {
		logger.Error(ctx, "error in getting matchesResp for tournament", zap.Error(err2))
		return nil, err2
	}
	return matchesResp.GetMatches(), nil
}

func (s *Service) getMatchesForChallengeHomeScreen(ctx context.Context) (*MatchesForSportsChallengeScreen, error) {
	yesterdayStart := timestamppb.New(datetime.StartOfDay(time.Now().Add(-1 * 24 * time.Hour)))
	futureEnd := timestamppb.New(datetime.EndOfDay(time.Now().Add(time.Duration(s.conf.Fittt().FiSavingsLeague().FetchMatchWindowInDays()*24) * time.Hour)))

	matches, err := s.getMatches(ctx, yesterdayStart, futureEnd)
	if err != nil {
		logger.Error(ctx, "error in getting matchesResp for tournament", zap.Error(err))
		return nil, err
	}
	resultMatches := &MatchesForSportsChallengeScreen{}
	logger.Debug(ctx, "matchesResp received for tournament", zap.Any(logger.RESPONSE, matches))
	if len(matches) == 0 {
		return resultMatches, nil
	}
	sort.Sort(Matches(matches))

	matchIds := make([]string, 0)
	for _, m := range matches {
		matchIds = append(matchIds, m.GetVendorMatchId())
	}
	eventsResp, err := s.fitttClient.GetCricketMatchResultEvents(ctx, &fittt.GetCricketMatchResultEventsRequest{
		VendorMatchIds: matchIds,
		EventStartFrom: yesterdayStart,
		EventStartTo:   timestamppb.Now(),
	})
	if err2 := epifigrpc.RPCError(eventsResp, err); err2 != nil {
		logger.Error(ctx, "error in getting match result events", zap.Error(err2))
		return nil, err2
	}
	events := eventsResp.Events
	yesterdayMatches, todayMatches, futureMatches := splitMatchesByDay(matches)
	s.setMatchesFromToday(todayMatches, events, resultMatches)
	s.setMatchesFromFutureDays(resultMatches, futureMatches)
	s.setMatchesFromYesterday(ctx, resultMatches, yesterdayMatches, events)

	return resultMatches, nil
}

func (s *Service) setMatchesFromFutureDays(resultMatches *MatchesForSportsChallengeScreen, futureMatches []*sports.Match) {
	switch {
	case resultMatches.openToPlayMatch == nil:
		if len(futureMatches) > 0 {
			resultMatches.openToPlayMatch = futureMatches[0]
		}
		if len(futureMatches) > 1 {
			// lockedMatch will be nil if openToPlayMatch is nil in this switch case
			resultMatches.lockedMatch = futureMatches[1]
		}
	case resultMatches.lockedMatch == nil:
		if len(futureMatches) > 0 {
			resultMatches.lockedMatch = futureMatches[0]
		}
	}
}

func (s *Service) setMatchesFromYesterday(ctx context.Context, resultMatches *MatchesForSportsChallengeScreen, yesterdayMatches []*sports.Match, events map[string]*eventpb.Event) {
	now := time.Now().In(datetime.IST)
	matchExecutionBufferDuration := s.conf.Fittt().FiSavingsLeague().MatchExecutionBufferDuration()
	openToPlayMatch := resultMatches.openToPlayMatch
	matchEndedCardDurationBeforeNextMatch := s.conf.Fittt().FiSavingsLeague().MatchEndedCardDurationBeforeNextMatch()
	// add yesterday's match only if there is no executed, ongoing,
	// ended or openToPlay match or openToPlay match start after some configured duration from now.
	if len(resultMatches.executedMatches) == 0 &&
		len(resultMatches.ongoingOrEndedMatches) == 0 &&
		(openToPlayMatch == nil ||
			openToPlayMatch.ExpectedStartTime.AsTime().After(now.Add(matchEndedCardDurationBeforeNextMatch))) {
		for _, yesterdayMatch := range yesterdayMatches {
			switch s.getMatchStateForLeague(yesterdayMatch, matchExecutionBufferDuration, events) {
			case MATCH_EXECUTED:
				resultMatches.executedMatches = append(resultMatches.executedMatches, yesterdayMatch)
			case MATCH_ENDED: // if a match is played in the night of yesterday,
				// then the execution will happen only in the morning of next day ~7-8am and so the execution for the match can still not
				// finished
				resultMatches.ongoingOrEndedMatches = append(resultMatches.ongoingOrEndedMatches, yesterdayMatch)
			default: // not possible
				logger.Debug(ctx, "invalid state for yesterday's match", zap.String("match", yesterdayMatch.String()))
			}
		}
	}
}

func (s *Service) setMatchesFromToday(todayMatches []*sports.Match, events map[string]*eventpb.Event, resultMatches *MatchesForSportsChallengeScreen) {
	matchExecutionBufferDuration := s.conf.Fittt().FiSavingsLeague().MatchExecutionBufferDuration()
	for _, m := range todayMatches {
		switch s.getMatchStateForLeague(m, matchExecutionBufferDuration, events) {
		case MATCH_YET_TO_START:
			if resultMatches.openToPlayMatch != nil {
				// already a match is open to play. So add this match to locked matches
				if resultMatches.lockedMatch != m {
					// in-case there are more than matches in some future tournament, don't over-write previous match
					resultMatches.lockedMatch = m
				} // else skip
			} else {
				resultMatches.openToPlayMatch = m
			}
		case MATCH_IN_PROGRESS, MATCH_ENDED:
			resultMatches.ongoingOrEndedMatches = append(resultMatches.ongoingOrEndedMatches, m)
		case MATCH_EXECUTED:
			resultMatches.executedMatches = append(resultMatches.executedMatches, m)
		}
	}
}

// nolint: funlen
func (s *Service) getExecutedMatchCard(ctx context.Context, executedMatch *sports.Match, actorId string) (*fePb.MatchCard, error) {
	// cases:
	// 1. user has not participated in the match
	// 2. user has participated but has 0 match points
	// 3. user participated and has some match points

	// fetch points calculation for the match
	pointsResp, err := s.fitttClient.GetSportsMatchPoints(ctx, &fittt.GetSportsMatchPointsRequest{
		ActorId:       actorId,
		VendorMatchId: executedMatch.GetVendorMatchId(),
		Vendor:        sports.Vendor_VENDOR_ROUNAZ,
	})
	if err2 := epifigrpc.RPCError(pointsResp, err); err2 != nil {
		logger.Error(ctx, "could not fetch match points from fittt service", zap.Error(err2))
		return nil, err2
	}

	var subIds []string
	for subId := range pointsResp.EventFrequency {
		subIds = append(subIds, subId)
	}
	logger.Debug(ctx, "subs for which points have been calculated", zap.Any("sub ids", subIds))

	activeSubsResp, err := s.getActiveSubscriptionsForMatch(ctx, executedMatch, actorId)
	if err != nil {
		logger.Error(ctx, "failed to fetch active subscriptions for prev match", zap.String(logger.MATCH_ID, executedMatch.GetVendorMatchId()), zap.Error(err))
		return nil, err
	}
	logger.Debug(ctx, "active subs for match and actor from rms", zap.String(logger.ACTOR_ID, actorId),
		zap.String(logger.MATCH_ID, executedMatch.GetVendorMatchId()), zap.Any("active subs resp", activeSubsResp))

	var postMatchStatsTags []*fePb.Tag
	var noOfSixes, noOfWickets uint32
	var teamStatString, teamLogo string
	var hasSubscribedToPlayerRule, hasSubscribedToTeamRule bool
	var inProcessingOrFailed bool
	for _, subList := range activeSubsResp {
		for _, sub := range subList.GetRuleSubscriptions() {
			actionExecstatus := pointsResp.ExecutionStatus[sub.GetId()]
			// nolint: exhaustive
			switch actionExecstatus {
			case actionpb.ActionStatus_CREATED, actionpb.ActionStatus_INITIATED:
				teamStatString = "🕰️ Processing match results"
				inProcessingOrFailed = true
			case actionpb.ActionStatus_FAILED:
				teamStatString = "❗ execution failed for match"
				inProcessingOrFailed = true
			}
			if inProcessingOrFailed {
				break
			}

			ruleName := getFiSavingsLeagueRuleCardDisplayInfo(s.conf, sub.GetRuleId()).Name()
			switch strings.ToUpper(ruleName) {
			case OneTeamOneDream:
				teamStat, isPresent := pointsResp.GetEventFrequency()[sub.GetId()]
				if isPresent && teamStat > 0 {
					teamStatString = "Your team won"
				} else {
					teamStatString = "Uh oh, your team lost"
				}
				teamLogo = getFiSavingsTeamLogo(s.conf,
					sub.RuleParamValues.RuleParamValues["configuredCricketTeam"].GetTeamVal().GetId())
				hasSubscribedToTeamRule = true
			case SuperSixer:
				noOfSixes += pointsResp.GetEventFrequency()[sub.GetId()]
				hasSubscribedToPlayerRule = true
			case Howzaaattt:
				noOfWickets += pointsResp.GetEventFrequency()[sub.GetId()]
				hasSubscribedToPlayerRule = true
			}
		}
		if inProcessingOrFailed {
			break
		}
	}

	if inProcessingOrFailed {
		logger.Debug(ctx, "rules are in processing or failed state")
		postMatchStatsTags = append(postMatchStatsTags, &fePb.Tag{
			Id:    "in_processing_or_failed",
			Color: "#F7F9FA",
			Desc: &fePb.Text{
				Text: teamStatString,
				DisplayInfo: &fePb.TextDisplayInfo{
					FontColor: "#333333",
					BgColor:   "#F7F9FA",
				},
			},
		})
		return getPostMatchCard(executedMatch, postMatchStatsTags), nil
	}

	if !hasSubscribedToPlayerRule && !hasSubscribedToTeamRule {
		logger.Debug(ctx, "user hasn't subscribed to either player or team rule")
		return nil, nil
	}

	// team rule
	if hasSubscribedToTeamRule {
		postMatchStatsTags = append(postMatchStatsTags, &fePb.Tag{
			Id:    "team_won_or_lost",
			Color: "#F7F9FA",
			Desc: &fePb.Text{
				Text: teamStatString,
				DisplayInfo: &fePb.TextDisplayInfo{
					FontColor: "#333333",
					BgColor:   "#F7F9FA",
				},
			},
			PrimaryImg: teamLogo,
		})
	}

	// player rule details
	if hasSubscribedToPlayerRule {
		var noOfSixesString, noOfWicketsString string
		if noOfSixes == 1 {
			noOfSixesString = fmt.Sprintf("hit %d six", noOfSixes)
		} else {
			noOfSixesString = fmt.Sprintf("hit %d sixes", noOfSixes)
		}

		if noOfWickets == 1 {
			noOfWicketsString = fmt.Sprintf("took %d wicket", noOfWickets)
		} else {
			noOfWicketsString = fmt.Sprintf("took %d wickets", noOfWickets)
		}
		playerStatsString := "Your players "
		switch {
		case noOfSixesString != "" && noOfWicketsString != "":
			playerStatsString += noOfSixesString + " & " + noOfWicketsString
		case noOfSixesString != "":
			playerStatsString += noOfSixesString
		case noOfWicketsString != "":
			playerStatsString += noOfWicketsString
		}
		postMatchStatsTags = append(postMatchStatsTags, &fePb.Tag{
			Color: "#F7F9FA",
			Desc: &fePb.Text{
				Text: playerStatsString,
				DisplayInfo: &fePb.TextDisplayInfo{
					FontColor: "#333333",
					BgColor:   "#F7F9FA",
				},
			},
			Id: "player_stats",
		})
	}

	totalSavedResp, err := s.searchClient.GetTxnAggregateFit(ctx, &search.GetTxnAggregateFitRequest{
		ActorId:         actorId,
		SubscriptionIds: subIds,
		RuleId:          "",
		FromTime:        executedMatch.GetExpectedStartTime(),
		ToTime:          timestamppb.Now(),
		ExecutionTags:   []string{executedMatch.MatchEventTag},
	})
	if err2 := epifigrpc.RPCError(totalSavedResp, err); err2 != nil {
		logger.Error(ctx, "could not fetch total saved amount from search service", zap.Error(err2))
		return nil, err2
	}

	// points and amount saved
	var amtSaved int64
	for _, subId := range subIds {
		amtSaved += totalSavedResp.SavedAmountMap[subId].GetUnits()
	}
	pointsScoredString := "You didn't save anything this match!😞"
	if amtSaved > 0 {
		// pointsScoredString = fmt.Sprintf("You earned %d points & saved ₹%d this match 🙀", pointsResp.GetTotalPoints(), amtSaved)
		pointsScoredString = fmt.Sprintf("You saved ₹%d this match 🙀", amtSaved)
	}
	postMatchStatsTags = append(postMatchStatsTags, &fePb.Tag{
		Color: "#D9F2CC",
		Desc: &fePb.Text{
			Text: pointsScoredString,
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#3E8355",
				BgColor:   "#D9F2CC",
			},
		},
		Id: "points_scored",
	})

	return getPostMatchCard(executedMatch, postMatchStatsTags), nil
}

func getPostMatchCard(executedMatch *sports.Match, postMatchStatsTags []*fePb.Tag) *fePb.MatchCard {
	return &fePb.MatchCard{
		Card: &fePb.MatchCard_PostMatch{
			PostMatch: &fePb.PostMatchCard{
				Header: &fePb.Text{
					Text: fmt.Sprintf("%s VS %s • MATCH ENDED ON %s",
						strings.ToUpper(executedMatch.GetMatchData().GetOpponents().GetOpponents()[0].GetTeam().GetTeamId()),
						strings.ToUpper(executedMatch.GetMatchData().GetOpponents().GetOpponents()[1].GetTeam().GetTeamId()),
						strings.ToUpper(executedMatch.GetEndTime().AsTime().In(datetime.IST).Format("02 Jan 03:04 PM"))),
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#478295",
						BgColor:   "#FFFFFF",
					},
				},
				PostMatchStats: postMatchStatsTags,
			},
		}}
}

func (s *Service) getOngoingOrEndedMatchCard(ctx context.Context, ongoingOrEndedMatch *sports.Match, actorId string) (*fePb.MatchCard, error) {
	subsMap, err := s.getActiveSubscriptionsForMatch(ctx, ongoingOrEndedMatch, actorId)
	if err != nil {
		logger.Error(ctx, "error in getting active subscriptions for time interval", zap.Error(err))
		return nil, err
	}
	if len(subsMap) == 0 {
		logger.Debug(ctx, "user subscription for ongoingOrEndedMatch match is nil")
		return nil, nil
	}

	subscribedParams, _, _, err := s.getCricketSubscribedParams(ctx, subsMap)
	if err != nil {
		return nil, err
	}

	matchStatusString := "MATCH IN PROGRESS"
	if ongoingOrEndedMatch.GetEndTime() != nil && ongoingOrEndedMatch.GetEndTime().AsTime().Before(time.Now()) {
		matchStatusString = "MATCH HAS ENDED"
	}

	return &fePb.MatchCard{
		Card: &fePb.MatchCard_SelectionMade{
			SelectionMade: &fePb.SelectionMadeCard{
				Header: &fePb.Text{
					Text: matchStatusString,
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#4F71AB",
						BgColor:   "#FFFFFF",
					},
				},
				Title: &fePb.Text{
					Text: "Your picks for this match",
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#333333",
					},
				},
				UniqueParamValues: subscribedParams,
				EditAllowed:       false,
			},
		},
	}, nil
}

func isSameDay(date1, date2 time.Time) bool {
	y1, m1, d1 := date1.Date()
	y2, m2, d2 := date2.Date()
	return y1 == y2 && m1 == m2 && d1 == d2
}

// nolint:funlen
func (s *Service) getOpenToPlayMatchCard(ctx context.Context, nextMatch *sports.Match, actorId string) (*fePb.MatchCard, error) {
	subsResp, err := s.rmClient.GetActiveSubscriptionsForTimeInterval(ctx, &manager.GetActiveSubscriptionsForTimeIntervalRequest{
		RuleIds:   getFiSavingsLeagueRuleIds(s.conf),
		ActorId:   actorId,
		StartTime: timestamppb.New(time.Now()),
		EndTime:   timestamppb.New(time.Now()),
		ParamFilters: []*manager.ParamFilter{
			{
				ParamType: manager.RuleParamType_CRICKET_TEAM,
				Key:       nextMatch.GetMatchData().GetOpponents().GetOpponents()[0].GetTeam().GetTeamId(),
			},
			{
				ParamType: manager.RuleParamType_CRICKET_TEAM,
				Key:       nextMatch.GetMatchData().GetOpponents().GetOpponents()[1].GetTeam().GetTeamId(),
			},
		},
	})
	if err2 := epifigrpc.RPCError(subsResp, err); err2 != nil {
		logger.Error(ctx, "error in getting active subscriptions for time interval", zap.Error(err2))
		return nil, err2
	}
	if len(subsResp.GetSubscriptionsMap()) == 0 {
		return s.getZeroStateMatchCard(ctx, actorId, nextMatch)
	}
	subscribedParams, _, _, err := s.getCricketSubscribedParams(ctx, subsResp.GetSubscriptionsMap())
	if err != nil {
		return nil, err
	}

	startsAt := nextMatch.GetExpectedStartTime().AsTime().In(datetime.IST)
	startsAtString := strings.ToUpper(startsAt.Format("02 Jan 03:04 PM"))
	if isSameDay(startsAt, time.Now().In(datetime.IST)) {
		// same day
		startsAtString = strings.ToUpper(startsAt.Format("03:04 PM"))
	}
	return &fePb.MatchCard{
		Card: &fePb.MatchCard_SelectionMade{
			SelectionMade: &fePb.SelectionMadeCard{
				Header: &fePb.Text{
					// Text: fmt.Sprintf("WEEK %d • MATCH %d • STARTS AT %s", nextMatch.GetMatchData().GetWeekNo(), nextMatch.GetMatchData().GetMatchNo(), startsAtString),
					Text: fmt.Sprintf("MATCH %d • STARTS AT %s", nextMatch.GetMatchData().GetMatchNo(), startsAtString),
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#4F71AB",
						BgColor:   "#FFFFFF",
					},
				},
				Title: &fePb.Text{
					Text: "Your picks for this match",
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#333333",
					},
				},
				UniqueParamValues: subscribedParams,
				EditAllowed:       true,
				Cta: &fePb.CTA{
					Action: &deeplinkpb.Deeplink{
						Screen: deeplinkpb.Screen_FITTT_SPORTS_CHALLENGE_DETAILS_SCREEN,
						ScreenOptions: &deeplinkpb.Deeplink_SportsChallengeDetailsScreenOptions{
							SportsChallengeDetailsScreenOptions: &deeplinkpb.FitttSportsChallengeDetailsScreenOptions{MatchId: nextMatch.GetId()},
						},
					},
				},
			},
		},
	}, nil
}

func (s *Service) getZeroStateMatchCard(ctx context.Context, actorId string, match *sports.Match) (*fePb.MatchCard, error) {
	startsAt := match.GetExpectedStartTime().AsTime().In(datetime.IST)
	startsAtString := strings.ToUpper(startsAt.Format("02 Jan 03:04 PM"))
	titleText := "Pick your team to participate \nin upcoming challenge"
	if isSameDay(startsAt, time.Now().In(datetime.IST)) {
		// same day
		startsAtString = startsAt.Format("03:04 PM")
		titleText = "Pick your team to participate \nin today’s challenge"
	}

	deeplink, err := s.getChallengeDetailsDeeplink(ctx, actorId, match.GetId())
	if err != nil {
		return nil, err
	}
	return &fePb.MatchCard{
		Card: &fePb.MatchCard_ZeroStateMatch{
			ZeroStateMatch: &fePb.ZeroStateMatchCard{
				Header: &fePb.Text{
					// Text: fmt.Sprintf("WEEK %d • MATCH %d • STARTS AT %s", match.GetMatchData().GetWeekNo(), match.GetMatchData().GetMatchNo(), startsAtString),
					Text: fmt.Sprintf("MATCH %d • STARTS AT %s", match.GetMatchData().GetMatchNo(), startsAtString),
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#4F71AB",
						BgColor:   "#FFFFFF",
					},
				},
				Title: &fePb.Text{
					Text: titleText,
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#333333",
					},
				},
				Team1: &fePb.SportsTeam{
					Id:     match.GetMatchData().GetOpponents().GetOpponents()[0].GetTeam().GetTeamId(),
					ImgUrl: getFiSavingsTeamLogo(s.conf, match.GetMatchData().GetOpponents().GetOpponents()[0].GetTeam().GetTeamId()),
				},
				Team2: &fePb.SportsTeam{
					Id:     match.GetMatchData().GetOpponents().GetOpponents()[1].GetTeam().GetTeamId(),
					ImgUrl: getFiSavingsTeamLogo(s.conf, match.GetMatchData().GetOpponents().GetOpponents()[1].GetTeam().GetTeamId()),
				},
				Deeplink: &fePb.CTA{
					Text: &fePb.Text{
						Text: "Play Now!",
						DisplayInfo: &fePb.TextDisplayInfo{
							FontColor: "#00B899",
							BgColor:   "#F7F9FA",
						},
					},
					Action: deeplink,
					Id:     "sports-challenge-cta",
				},
			},
		},
	}, nil
}

func (s *Service) getChallengeDetailsDeeplink(ctx context.Context, actorId, matchId string) (*deeplinkpb.Deeplink, error) {
	featureDetailsRes, err := s.userOnboardingClient.GetFeatureDetails(ctx, &onboarding.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onboarding.Feature_FEATURE_SA,
	})
	if err = epifigrpc.RPCError(featureDetailsRes, err); err != nil {
		return nil, errors2.Wrap(err, "error getting feature details to check if Fi Lite user")
	}

	deeplink := &deeplinkpb.Deeplink{
		Screen: deeplinkpb.Screen_FITTT_SPORTS_CHALLENGE_DETAILS_SCREEN,
		ScreenOptions: &deeplinkpb.Deeplink_SportsChallengeDetailsScreenOptions{
			SportsChallengeDetailsScreenOptions: &deeplinkpb.FitttSportsChallengeDetailsScreenOptions{MatchId: matchId}},
	}

	if featureDetailsRes.GetIsFiLiteUser() {
		deeplink, err = pkgOnboarding.GetSmartDepositsBenefitsScreen("6.8")
		if err != nil {
			return nil, errors2.Wrap(err, "error getting SD benefits screen")
		}
	}

	return deeplink, nil
}

func getFiSavingsTeamLogo(conf *genconf.Config, teamId string) string {
	return conf.Fittt().FiSavingsLeague().TeamLogos().Get(teamId)
}

func (s *Service) getLockedMatchCard(match *sports.Match, unlockTime time.Time) *fePb.MatchCard {
	unlockTextString := fmt.Sprintf("Unlocks at \n%s", strings.ToUpper(unlockTime.In(datetime.IST).Format("02 Jan 03:04 PM")))
	if isSameDay(unlockTime.In(datetime.IST), time.Now().In(datetime.IST)) {
		// same day
		unlockTextString = fmt.Sprintf("Unlocks today\nat %s", unlockTime.In(datetime.IST).Format("03:04 PM"))
	}
	return &fePb.MatchCard{
		Card: &fePb.MatchCard_LockedCard{
			LockedCard: &fePb.LockedCard{
				Title: &fePb.Text{
					Text: "PICK YOUR TEAM",
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#4F71AB",
						BgColor:   "#99FFFFFF",
					},
				},
				Team1: &fePb.SportsTeam{
					Id:     match.GetMatchData().GetOpponents().GetOpponents()[0].GetTeam().GetTeamId(),
					ImgUrl: getFiSavingsTeamLogo(s.conf, match.GetMatchData().GetOpponents().GetOpponents()[0].GetTeam().GetTeamId()),
				},
				Team2: &fePb.SportsTeam{
					Id:     match.GetMatchData().GetOpponents().GetOpponents()[1].GetTeam().GetTeamId(),
					ImgUrl: getFiSavingsTeamLogo(s.conf, match.GetMatchData().GetOpponents().GetOpponents()[1].GetTeam().GetTeamId()),
				},
				UnlockText: &fePb.Text{
					Text: unlockTextString,
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#333333",
						BgColor:   "#99FFFFFF",
					},
				},
			},
		},
	}
}

// nolint: funlen
func (s *Service) getMatchCards(ctx context.Context, matches *MatchesForSportsChallengeScreen, actorId string) ([]*fePb.MatchCard, error) {
	var matchCards []*fePb.MatchCard
	var openToPlayMatchCard, lockedMatchCard *fePb.MatchCard

	g, _ := errgroup.WithContext(ctx)
	executedMatchCards := make([]*fePb.MatchCard, len(matches.executedMatches))
	for i, match := range matches.executedMatches {
		ind := i       // assignment to use in go routine
		match := match // assignment to use in go routine
		g.Go(func() error {
			card, err := s.getExecutedMatchCard(ctx, match, actorId)
			if err != nil {
				return err
			}
			executedMatchCards[ind] = card
			return nil
		})
	}

	ongoingOrEndedMatchCards := make([]*fePb.MatchCard, len(matches.ongoingOrEndedMatches))
	for i, match := range matches.ongoingOrEndedMatches {
		ind := i
		match := match // assignment to use in go routine
		g.Go(func() error {
			card, err := s.getOngoingOrEndedMatchCard(ctx, match, actorId)
			if err != nil {
				return err
			}
			ongoingOrEndedMatchCards[ind] = card
			return nil
		})
	}

	if matches.openToPlayMatch != nil {
		g.Go(func() error {
			card, err := s.getOpenToPlayMatchCard(ctx, matches.openToPlayMatch, actorId)
			if err != nil {
				return err
			}
			openToPlayMatchCard = card
			return nil
		})
	}

	if matches.lockedMatch != nil {
		g.Go(func() error {
			lockedMatchCard = s.getLockedMatchCard(matches.lockedMatch, matches.openToPlayMatch.GetExpectedStartTime().AsTime())
			return nil
		})
	}

	err := g.Wait()
	if err != nil {
		logger.Error(ctx, "error while forming match cards", zap.Error(err))
		return nil, err
	}
	for _, m := range executedMatchCards {
		if m != nil {
			matchCards = append(matchCards, m)
		}
	}
	for _, m := range ongoingOrEndedMatchCards {
		if m != nil {
			matchCards = append(matchCards, m)
		}
	}
	if openToPlayMatchCard != nil {
		matchCards = append(matchCards, openToPlayMatchCard)
	}
	if lockedMatchCard != nil {
		matchCards = append(matchCards, lockedMatchCard)
	}

	// appending thank you card after the end of the tournament
	endOfTheTournament := time.Unix(s.conf.Fittt().FiSavingsLeague().EndDateOfTournament(), 0).In(datetime.IST)
	if time.Now().After(endOfTheTournament) {
		// after the tournament ends, add a custom card to thank for participation
		thankYouCard := getThankYouForParticipatingCard()
		matchCards = append(matchCards, thankYouCard)
	}
	logger.Debug(ctx, "Match cards", zap.Any("match cards", matchCards))

	return matchCards, nil
}

func getThankYouForParticipatingCard() *fePb.MatchCard {
	titleText := "Thank you for participating\nand playing!"
	return &fePb.MatchCard{
		Card: &fePb.MatchCard_SelectionMade{
			SelectionMade: &fePb.SelectionMadeCard{
				Header: &fePb.Text{
					Text: "FI SAVINGS LEAGUE HAS ENDED",
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#4F71AB",
						BgColor:   "#FFFFFF",
					},
				},
				Title: &fePb.Text{
					Text: titleText,
					DisplayInfo: &fePb.TextDisplayInfo{
						FontColor: "#333333",
					},
				},
				Cta: &fePb.CTA{
					Text: &fePb.Text{
						Text: "Explore FIT Rules",
						DisplayInfo: &fePb.TextDisplayInfo{
							FontColor: "#00B899",
							BgColor:   "#F7F9FA",
						},
					},
					Action: &deeplinkpb.Deeplink{
						Screen: deeplinkpb.Screen_FIT_ALL_COLLECTIONS_PAGE,
						ScreenOptions: &deeplinkpb.Deeplink_FitAllCollectionsPageScreenOptions{
							FitAllCollectionsPageScreenOptions: &deeplinkpb.FitAllCollectionsPageScreenOptions{
								CollectionType: fePb.CollectionType_COLLECTION_TYPE_AUTO_INVEST.String(),
							},
						},
					},
					Id: "fi-savings-league-has-ended-cta",
				},
			},
		},
	}
}

func (s *Service) getRuleCardsForMatchDetailsPage(ctx context.Context, subsMap map[string]*manager.Subscriptions, match *sports.Match,
	actorId string) ([]*fePb.SportsChallengeRuleCard, error) {
	possibleParamsResp, err := s.rmUIClient.GetPossibleParamValuesByRuleIds(ctx, &ui.GetPossibleParamValuesByRuleIdsRequest{
		RuleIds: getFiSavingsLeagueRuleIds(s.conf),
		Filters: []*ui.ParamFilter{
			{
				ParamType: ui.RuleParamType_CRICKET_TEAM,
				Key:       match.GetMatchData().GetOpponents().GetOpponents()[0].GetTeam().GetTeamId(),
			},
			{
				ParamType: ui.RuleParamType_CRICKET_TEAM,
				Key:       match.GetMatchData().GetOpponents().GetOpponents()[1].GetTeam().GetTeamId(),
			},
		},
	})
	if err2 := epifigrpc.RPCError(possibleParamsResp, err); err2 != nil {
		logger.Error(ctx, "error in getting possible params", zap.Error(err))
		return nil, err2
	}

	rulesResp, err := s.rmClient.GetRulesByIds(ctx, &manager.GetRulesByIdsRequest{
		RuleIds: getFiSavingsLeagueRuleIds(s.conf),
	})
	if err2 := epifigrpc.RPCError(rulesResp, err); err2 != nil {
		logger.Error(ctx, "error in getting rules", zap.Error(err2), zap.Strings(logger.RULE_IDS, getFiSavingsLeagueRuleIds(s.conf)))
		return nil, err2
	}

	_, subscribedParamIdToSDMap, subscribedSds, err := s.getCricketSubscribedParams(ctx, subsMap)
	if err != nil {
		return nil, err
	}
	logger.Debug(ctx, "subscribed params map", zap.Any("subscribed params map", subscribedParamIdToSDMap), zap.Any("subscribedSds", subscribedSds))

	var ruleCards []*fePb.SportsChallengeRuleCard
	for _, r := range getFiSavingsLeagueRuleIds(s.conf) {
		ruleCard, err2 := s.getRuleCard(ctx, subscribedParamIdToSDMap[r], rulesResp.GetRules()[r], possibleParamsResp.GetParamValuesMap()[r], actorId, subscribedSds[r])
		if err2 != nil {
			logger.Error(ctx, "error in getting rule card", zap.Error(err2))
			return nil, err2
		}
		ruleCards = append(ruleCards, ruleCard)
	}

	return ruleCards, nil
}

func (s *Service) getParamsForSportsChallengeRuleCard(ctx context.Context, possibleValues *ui.ParamValues,
	subscribedParamIdToSDMap map[string]*fePb.SdParamValue, rule *manager.Rule, actorId string, subscribedSds []*manager.Value) (*fePb.PossibleParams, error) {
	sdPossibleValues, _, err := s.getSDAccounts(ctx, actorId, nil)
	if err != nil {
		logger.Error(ctx, "Error in getting sd accounts", zap.Error(err))
		return nil, err
	}

	uniqueParams, paramToSDMapping := getUniqueParamPossibleValuesForSportsChallenge(possibleValues, subscribedParamIdToSDMap, sdPossibleValues)

	var uniqueParamVarName, sdParamVarName, moneyParamVarName string
	for _, i := range rule.GetDescription().GetInputParams() {
		switch i.GetInputType() {
		case manager.RuleParamType_CRICKET_PLAYER, manager.RuleParamType_CRICKET_TEAM:
			uniqueParamVarName = i.GetName()
		case manager.RuleParamType_SMART_DEPOSIT:
			sdParamVarName = i.GetName()
		case manager.RuleParamType_MONEY:
			moneyParamVarName = i.GetName()
		default:
			// do nothing
		}
	}

	logger.Debug(ctx, "subscribed SDs", zap.Any("subscribedSDs", subscribedSds),
		zap.Any("possibleSDs", sdPossibleValues))

	var atLeastOneSDSelected bool
	// Setting isSelected flag as true for already subscribed Sds
	for _, sd := range subscribedSds {
		for _, p := range sdPossibleValues {
			if sd.GetSdValue().GetAccountId() == p.GetSdValue().GetAccountId() {
				p.IsSelected = true
				atLeastOneSDSelected = true
			}
		}
	}

	if !atLeastOneSDSelected && len(sdPossibleValues) > 0 {
		sdPossibleValues[0].IsSelected = true
	}

	logger.Debug(ctx, "default money values for sports challenge",
		zap.Int64("rule 1: ", s.conf.Fittt().FiSavingsLeague().Rule1().RuleCardDisplayTexts().DefaultMoneyVal()),
		zap.Int64("rule 2: ", s.conf.Fittt().FiSavingsLeague().Rule2().RuleCardDisplayTexts().DefaultMoneyVal()),
		zap.Int64("rule 3: ", s.conf.Fittt().FiSavingsLeague().Rule3().RuleCardDisplayTexts().DefaultMoneyVal()))

	possibleParams := &fePb.PossibleParams{
		UniqueParamName:      uniqueParamVarName,
		PossibleUniqueParams: uniqueParams,
		SdParamName:          sdParamVarName,
		PossibleSdValues:     sdPossibleValues,
		MoneyParamName:       moneyParamVarName,
		MoneyValues: []*fePb.Value{
			{
				Value: &fePb.Value_MoneyVal{
					MoneyVal: &types.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        getFiSavingsLeagueRuleCardDisplayInfo(s.conf, rule.GetId()).DefaultMoneyVal(),
					},
				},
				Id: uuid.New().String(),
			},
		},
		MaxUniqueParamSelectionAllowed:         int32(rule.GetMaxSubscriptionsPerActor()),
		SelectionLimitMsg:                      getFiSavingsLeagueRuleCardDisplayInfo(s.conf, rule.GetId()).SelectionLimitMsg(),
		ExistingSubscriptionUniqueParamToSdMap: paramToSDMapping,
	}
	return possibleParams, nil
}

func getFiSavingsLeagueRuleCardDisplayInfo(conf *genconf.Config, ruleId string) *genconf.RuleCardDisplayTexts {
	leagueConf := conf.Fittt().FiSavingsLeague()
	switch {
	case leagueConf.Rule1().Id() == ruleId:
		return leagueConf.Rule1().RuleCardDisplayTexts()
	case leagueConf.Rule2().Id() == ruleId:
		return leagueConf.Rule2().RuleCardDisplayTexts()
	case leagueConf.Rule3().Id() == ruleId:
		return leagueConf.Rule3().RuleCardDisplayTexts()
	default:
		logger.ErrorNoCtx("rule Id not identified for getting display info", zap.String(logger.RULE_ID, ruleId))
		return nil
	}
}

// nolint: funlen
func getUniqueParamPossibleValuesForSportsChallenge(possibleValues *ui.ParamValues, subscribedParamIdToSDMap map[string]*fePb.SdParamValue,
	sdPossibleValues []*fePb.Value) ([]*fePb.Value, []*fePb.UniqueParamToSDMap) {
	var uniqueParams []*fePb.Value
	var uniqueParamToSDMapping []*fePb.UniqueParamToSDMap

	if subscribedParamIdToSDMap == nil {
		subscribedParamIdToSDMap = make(map[string]*fePb.SdParamValue)
	}

	activeSDsMap := make(map[string]bool)
	for _, p := range sdPossibleValues {
		activeSDsMap[p.GetSdValue().GetAccountId()] = true
	}

	for _, paramMap := range possibleValues.GetParamValuesMap() {
		switch paramMap.GetRuleParamType() {
		case ui.RuleParamType_CRICKET_PLAYER:
			for _, p := range paramMap.GetParamValues() {
				var alreadySubscribed bool
				playerId := p.GetValue().GetCricketPlayerVal().GetPlayerId()
				if _, present := subscribedParamIdToSDMap[playerId]; present && activeSDsMap[subscribedParamIdToSDMap[playerId].GetAccountId()] {
					alreadySubscribed = true
				}

				param := &fePb.Value{
					Value: &fePb.Value_PlayerValue{
						PlayerValue: &fePb.CricketPlayerValue{
							PlayerId:   p.GetValue().GetCricketPlayerVal().GetPlayerId(),
							PlayerName: p.GetValue().GetCricketPlayerVal().GetPlayerName(),
							PlayerPic:  p.GetValue().GetCricketPlayerVal().GetPlayerPic(),
							Team: &fePb.CricketTeamValue{
								TeamId:          p.GetValue().GetCricketPlayerVal().GetTeam().GetTeamId(),
								TeamName:        p.GetValue().GetCricketPlayerVal().GetTeam().GetTeamName(),
								TeamLogo:        p.GetValue().GetCricketPlayerVal().GetTeam().GetTeamLogo(),
								AbbreviatedName: p.GetValue().GetCricketPlayerVal().GetTeam().GetAbbreviatedName(),
							},
							PlayerType:  fePb.CricketPlayerType(fePb.CricketPlayerType_value[p.GetValue().GetCricketPlayerVal().GetPlayerType().String()]),
							DisplayName: GetPlayerDisplayName(p.GetValue().GetCricketPlayerVal().GetPlayerName()),
						}},
					Id:         p.GetValue().GetParamId(),
					IsSelected: alreadySubscribed,
				}
				uniqueParams = append(uniqueParams, param)

				if alreadySubscribed {
					val := subscribedParamIdToSDMap[p.GetValue().GetCricketPlayerVal().GetPlayerId()]
					uniqueParamToSDMapping = append(uniqueParamToSDMapping, &fePb.UniqueParamToSDMap{
						Id:          uuid.New().String(),
						UniqueParam: param,
						SdParam:     &fePb.Value{Id: val.GetAccountId(), Value: &fePb.Value_SdValue{SdValue: val}, IsSelected: true},
					})
				}

			}
		case ui.RuleParamType_CRICKET_TEAM:
			for _, p := range paramMap.GetParamValues() {
				var alreadySubscribed bool
				teamId := p.GetValue().GetCricketTeamVal().GetTeamId()
				if _, present := subscribedParamIdToSDMap[teamId]; present && activeSDsMap[subscribedParamIdToSDMap[teamId].GetAccountId()] {
					alreadySubscribed = true
				}
				param := &fePb.Value{
					Value: &fePb.Value_TeamValue{
						TeamValue: &fePb.CricketTeamValue{
							TeamId:          p.GetValue().GetCricketTeamVal().GetTeamId(),
							TeamName:        p.GetValue().GetCricketTeamVal().GetTeamName(),
							TeamLogo:        p.GetValue().GetCricketTeamVal().GetTeamLogo(),
							AbbreviatedName: p.GetValue().GetCricketTeamVal().GetAbbreviatedName(),
						},
					},
					Id:         p.GetValue().GetParamId(),
					IsSelected: alreadySubscribed,
				}
				uniqueParams = append(uniqueParams, param)

				if alreadySubscribed {
					val := subscribedParamIdToSDMap[p.GetValue().GetCricketTeamVal().GetTeamId()]
					uniqueParamToSDMapping = append(uniqueParamToSDMapping, &fePb.UniqueParamToSDMap{
						Id:          uuid.New().String(),
						UniqueParam: param,
						SdParam:     &fePb.Value{Id: val.GetAccountId(), Value: &fePb.Value_SdValue{SdValue: val}, IsSelected: true},
					})
				}
			}
		default:
		}
	}
	return uniqueParams, uniqueParamToSDMapping
}

func (s *Service) getRuleCard(ctx context.Context, subscribedParamIdToSDMap map[string]*fePb.SdParamValue, rule *manager.Rule, valuesMap *ui.ParamValues,
	actorId string, subscribedSds []*manager.Value) (*fePb.SportsChallengeRuleCard, error) {
	params, err2 := s.getParamsForSportsChallengeRuleCard(ctx, valuesMap, subscribedParamIdToSDMap, rule, actorId, subscribedSds)
	if err2 != nil {
		return nil, err2
	}
	return &fePb.SportsChallengeRuleCard{
		Id:               rule.GetId(),
		NoSubTitle:       &fePb.Text{Text: getFiSavingsLeagueRuleCardDisplayInfo(s.conf, rule.GetId()).NoSubTitle()},
		NoSubDesc:        &fePb.Text{Text: getFiSavingsLeagueRuleCardDisplayInfo(s.conf, rule.GetId()).NoSubDesc()},
		ExistingSubTitle: &fePb.Text{Text: getFiSavingsLeagueRuleCardDisplayInfo(s.conf, rule.GetId()).ExistingSubTitle()},
		DisplayInfo: &fePb.CardDisplayInfo{
			BgColor: "#FFFFFF",
			ImgUrl:  getFiSavingsLeagueRuleCardDisplayInfo(s.conf, rule.GetId()).ImgUrl(),
		},
		PossibleParams: params,
	}, nil
}

func (s *Service) getSportsChallengeRuleSubscriptionRequestsParamValues(subRequests map[string]*fePb.RuleSubscriptionDataList) (map[string][]*fePb.RuleParamValues, map[string][]string, map[string][]int64, string) {
	var teamNameAbbreviation string
	ruleParamValuesMap := make(map[string][]*fePb.RuleParamValues)
	// ideally these maps should be uniqueParam->Value map
	// but since we require as per ruleId in sports challenge, we are using this
	ruleSdNamesMap := make(map[string][]string)
	ruleSdAmountValuesMap := make(map[string][]int64)
	// map[ruleId][sdAccountId]alreadyAddedFlag
	sdDedupeMap := make(map[string]map[string]bool)
	for ruleId, ruleSubscriptionDataList := range subRequests {
		ruleConf := getFiSavingsLeagueRuleCardDisplayInfo(s.conf, ruleId)
		for _, ruleSubscriptionData := range ruleSubscriptionDataList.GetRuleSubscriptionsList() {
			// money param value can be default if not provided
			if ruleSubscriptionData.GetSelectedMoneyVal() == nil {
				ruleSubscriptionData.SelectedMoneyVal = &types.Money{
					CurrencyCode: money.RupeeCurrencyCode,
					Units:        ruleConf.DefaultMoneyVal(),
				}
			}
			if ruleSubscriptionData.GetSelectedUniqueValue().GetTeamValue() != nil {
				teamNameAbbreviation = strings.ToUpper(ruleSubscriptionData.GetSelectedUniqueValue().GetTeamValue().GetTeamId())
			}
			// constructing fe rule param values
			rpv := &fePb.RuleParamValues{RuleParamValues: map[string]*fePb.Value{
				ruleSubscriptionData.UniqueParamName: ruleSubscriptionData.GetSelectedUniqueValue(),
				ruleSubscriptionData.SdParamName:     {Value: &fePb.Value_SdValue{SdValue: ruleSubscriptionData.GetSelectedSdValue()}},
				ruleSubscriptionData.MoneyParamName:  {Value: &fePb.Value_MoneyVal{MoneyVal: ruleSubscriptionData.GetSelectedMoneyVal()}},
			}}

			ruleParamValuesMap[ruleId] = append(ruleParamValuesMap[ruleId], rpv)

			if sdDedupeMap[ruleId] == nil {
				sdDedupeMap[ruleId] = make(map[string]bool)
			}
			if !sdDedupeMap[ruleId][ruleSubscriptionData.GetSelectedSdValue().GetAccountId()] {
				ruleSdNamesMap[ruleId] = append(ruleSdNamesMap[ruleId], ruleSubscriptionData.GetSelectedSdValue().GetName())
				sdDedupeMap[ruleId][ruleSubscriptionData.GetSelectedSdValue().GetAccountId()] = true
			}
			ruleSdAmountValuesMap[ruleId] = append(ruleSdAmountValuesMap[ruleId], ruleSubscriptionData.GetSelectedMoneyVal().GetUnits())
		}
	}
	return ruleParamValuesMap, ruleSdNamesMap, ruleSdAmountValuesMap, teamNameAbbreviation
}

func (s *Service) getSportsChallengeSubscribeReqParamValues(subReq map[string]*fePb.MultipleSubscriptionsForRuleData) (map[string][]*fePb.RuleParamValues, map[string][]string, map[string][]int64, string) {
	var teamNameAbbreviation string
	ruleParamValuesMap := make(map[string][]*fePb.RuleParamValues)
	ruleSdNamesMap := make(map[string][]string)
	ruleSdAmountValuesMap := make(map[string][]int64)
	for ruleId, subData := range subReq {
		// money param value can be default if not provided
		if subData.GetSelectedMoneyVal() == nil {
			subData.SelectedMoneyVal = &types.Money{
				CurrencyCode: money.RupeeCurrencyCode,
				Units:        getFiSavingsLeagueRuleCardDisplayInfo(s.conf, ruleId).DefaultMoneyVal(),
			}
		}

		for _, uniqueValue := range subData.SelectedUniqueValues {
			if uniqueValue.GetTeamValue() != nil {
				teamNameAbbreviation = strings.ToUpper(uniqueValue.GetTeamValue().GetTeamId())
			}
			// constructing rule param values for comparison
			rpv := &fePb.RuleParamValues{RuleParamValues: map[string]*fePb.Value{
				subData.UniqueParamName: uniqueValue,
				subData.SdParamName:     {Value: &fePb.Value_SdValue{SdValue: subData.GetSelectedSdValue()}},
				subData.MoneyParamName:  {Value: &fePb.Value_MoneyVal{MoneyVal: subData.GetSelectedMoneyVal()}},
			}}
			ruleParamValuesMap[ruleId] = append(ruleParamValuesMap[ruleId], rpv)
		}
		// only single value will be filled in these two maps as previous request had a common sd for all subs
		// for a rule
		ruleSdNamesMap[ruleId] = append(ruleSdNamesMap[ruleId], subData.GetSelectedSdValue().GetName())
		ruleSdAmountValuesMap[ruleId] = append(ruleSdAmountValuesMap[ruleId], subData.GetSelectedMoneyVal().GetUnits())
	}
	return ruleParamValuesMap, ruleSdNamesMap, ruleSdAmountValuesMap, teamNameAbbreviation
}

func areRuleSubscriptionParamsSimilar(rule *manager.Rule, currentValueMap, newValueMap map[string]*manager.Value) int {
	// nolint: dogsled
	currentUniqueParamValue, _, currentMoneyParam, _, _ := getUniqueAndMoneyParamForSubscription(rule.Name, currentValueMap)
	// nolint: dogsled
	newUniqueParamValue, _, newMoneyParam, _, _ := getUniqueAndMoneyParamForSubscription(rule.Name, newValueMap)

	var currentSdParamAccount, newSdParamAccount string
	for _, paramValue := range currentValueMap {
		if paramValue.GetSdValue().GetAccountId() != "" {
			currentSdParamAccount = paramValue.GetSdValue().GetAccountId()
			break
		}
	}
	for _, paramValue := range newValueMap {
		if paramValue.GetSdValue().GetAccountId() != "" {
			newSdParamAccount = paramValue.GetSdValue().GetAccountId()
			break
		}
	}

	if currentUniqueParamValue == newUniqueParamValue && currentSdParamAccount == newSdParamAccount &&
		currentMoneyParam.GetUnits() == newMoneyParam.GetUnits() {
		// do nothing
		return RULE_PARAMS_SIMILAR
	}

	if currentUniqueParamValue == newUniqueParamValue {
		// unique params are same, so update the existing sub
		return UNIQUE_PARAMS_SIMILAR
	}

	// completely different, create new subscription
	return RULE_PARAMS_DISSIMILAR
}

// nolint: funlen
func (s *Service) SubscribeToSportsChallenge(ctx context.Context, req *fePb.SubscribeToSportsChallengeRequest) (*fePb.SubscribeToSportsChallengeResponse, error) {
	resp := &fePb.SubscribeToSportsChallengeResponse{
		RespHeader: &feHdr.ResponseHeader{Status: rpc.StatusOk()},
		Title:      &fePb.Text{Text: "Rules for today’s match are active 🎉"},
		SubTitle:   &fePb.Text{Text: "You’ll start saving when your team wins or players score during the match"},
		ShareData: &fePb.ShareCardData{
			Title:        nil,
			ShareDisplay: &fePb.Text{Text: fiSavingsLeagueshareCardDisplayText},
			ImgUrl:       "https://epifi-icons.pointz.in/fittt-images/sports-challenge/fi-savings-league-share.png",
		},
	}

	actorId := req.GetReq().GetAuth().GetActorId()
	logger.Debug(ctx, "subscribe to sports challenge request received",
		zap.Any("Request", req.GetMultipleSubsRequest()),
		zap.Any("New Request Format", req.GetRuleSubscriptionsRequest()),
		zap.String(logger.MATCH_ID, req.GetMatchId()))

	// not taking rule ids from the incoming requests because if the user
	// has no selection for the rule, we wont be getting any subscriptions
	// so we consider user has opted out from the rule and we will pause
	// all the other subscription for the rule
	ruleIds := getFiSavingsLeagueRuleIds(s.conf)

	var feRuleParamValuesMap map[string][]*fePb.RuleParamValues
	var ruleSdNamesMap map[string][]string
	var ruleSdAmountValuesMap map[string][]int64
	var teamName string
	if req.GetRuleSubscriptionsRequest() != nil {
		feRuleParamValuesMap, ruleSdNamesMap, ruleSdAmountValuesMap, teamName = s.getSportsChallengeRuleSubscriptionRequestsParamValues(req.GetRuleSubscriptionsRequest())
	} else {
		feRuleParamValuesMap, ruleSdNamesMap, ruleSdAmountValuesMap, teamName = s.getSportsChallengeSubscribeReqParamValues(req.GetMultipleSubsRequest())
	}
	logger.Debug(ctx, "fe rule param values map", zap.Any("feRuleParamValuesMap", feRuleParamValuesMap))

	var ruleSubscriptionsResp *manager.GetSubscriptionsByActorForRulesResponse
	var rulesResp *manager.GetRulesByIdsResponse
	g, _ := errgroup.WithContext(ctx)
	g.Go(func() error {
		ruleSubscriptionsRespLocal, err := s.rmClient.GetSubscriptionsByActorForRules(ctx, &manager.GetSubscriptionsByActorForRulesRequest{
			ActorId:                actorId,
			RuleIds:                ruleIds,
			States:                 []manager.RuleSubscriptionState{manager.RuleSubscriptionState_ACTIVE, manager.RuleSubscriptionState_INACTIVE},
			ShouldNotUsePagination: true,
		})
		if err2 := epifigrpc.RPCError(ruleSubscriptionsRespLocal, err); err2 != nil {
			logger.Error(ctx, "Failed to get existing active subscriptions", zap.String("actorId", actorId), zap.Error(err2))
			return err2
		}
		ruleSubscriptionsResp = ruleSubscriptionsRespLocal
		return nil
	})

	g.Go(func() error {
		// fetching all the rules from their ids
		rulesRespLocal, err := s.rmClient.GetRulesByIds(ctx, &manager.GetRulesByIdsRequest{
			RuleIds:    ruleIds,
			FieldMasks: []manager.DisplayDataFieldMask{manager.DisplayDataFieldMask_RULE_DISPLAY_INFO, manager.DisplayDataFieldMask_POSSIBLE_PARAM_VALUES},
		})
		if err2 := epifigrpc.RPCError(rulesRespLocal, err); err2 != nil {
			logger.Error(ctx, "failed to get rules", zap.Strings(logger.RULE_IDS, ruleIds), zap.Error(err2))
			return err2
		}
		rulesResp = rulesRespLocal
		return nil
	})
	err := g.Wait()
	if err != nil {
		logger.Error(ctx, "failed in goroutine", zap.String("actorId", actorId), zap.Error(err))
		resp.RespHeader = errors.GetFitFullScreenErrorResponseHeader(err, errors.FIT0003)
		return resp, nil
	}
	logger.Debug(ctx, "subscriptions for actor", zap.Strings(logger.RULE_IDS, ruleIds), zap.Any("response", ruleSubscriptionsResp.GetRuleSubscriptions()))

	existingSubs := ruleSubscriptionsResp.GetRuleSubscriptions()
	err = s.updateOrSubscribeSportsRules(ctx, existingSubs, feRuleParamValuesMap, actorId, rulesResp.GetRules())
	if err != nil {
		logger.Error(ctx, "Error in update or subscribe sports rule", zap.Error(err))
		resp.RespHeader = errors.GetFitFullScreenErrorResponseHeader(err, errors.FIT0003)
		return resp, nil
	}

	resp.SuccessCard = getSportsChallengeSubscriptionSuccessCards(feRuleParamValuesMap, rulesResp.GetRules(), ruleSdNamesMap, ruleSdAmountValuesMap, teamName)
	return resp, nil
}

func getSportsChallengeSubscriptionSuccessCards(ruleParamValuesMap map[string][]*fePb.RuleParamValues, rulesMap map[string]*manager.Rule, ruleSdNamesMap map[string][]string, ruleSdAmountValuesMap map[string][]int64, teamName string) []*fePb.SubscriptionsSuccessCard {
	var successCards []*fePb.SubscriptionsSuccessCard
	for ruleId, paramsList := range ruleParamValuesMap {
		if len(paramsList) == 0 {
			// sending success cards only for params
			// for which request has been received
			continue
		}

		sdString := ruleSdNamesMap[ruleId][0]
		if len(ruleSdNamesMap[ruleId]) > 1 {
			sdString = fmt.Sprintf("%d Smart Deposits", len(ruleSdNamesMap[ruleId]))
		}

		var card *fePb.SubscriptionsSuccessCard
		ruleName := rulesMap[ruleId].GetName()
		switch strings.ToUpper(ruleName) {
		case SuperSixer, AboveAndBeyond:
			// using 1st values from ruleSdAmountValuesMap and ruleSdNamesMap for backward compatibility
			// as 1st value will be filled in getSportsChallengeSubscribeReqParamValues api
			cardText := fmt.Sprintf("If my selected Batsmen hit a 6, put aside ₹%d into %s", ruleSdAmountValuesMap[ruleId][0], sdString)
			card = SuperSixerSubscriptionsSuccessCard
			card.Desc = &fePb.Text{Text: cardText, DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#333333"}}
		case Howzaaattt, MaidenMaiden:
			cardText := fmt.Sprintf("If my selected Bowlers take a wicket, put aside ₹%d into %s", ruleSdAmountValuesMap[ruleId][0], sdString)
			card = HowzatSubscriptionsSuccessCard
			card.Desc = &fePb.Text{Text: cardText, DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#333333"}}
		case OneTeamOneDream:
			cardText := fmt.Sprintf("When %s wins a match, put aside ₹%d into %s", teamName, ruleSdAmountValuesMap[ruleId][0], sdString)
			card = OneTeamOneDreamSubscriptionsSuccessCard
			card.Desc = &fePb.Text{Text: cardText, DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#333333"}}
		}
		// default color mentioned in the constants file
		card.DisplayInfo.BgColor = rulesMap[ruleId].GetDisplayData().GetBackgroundColor()
		successCards = append(successCards, card)
	}
	return successCards
}

// 5 types of subs
// 1. new subscriptions for that rule
// 2. old subs which are completely same and Active for which nothing to do
// 3. old subs which are completely same and Inactive - update the state only
// 4. old subscriptions for that rule which needs to be paused
// 5. old subs which are partially same, need to update partial data
// nolint: funlen
func (s *Service) updateOrSubscribeSportsRules(ctx context.Context, existingSubs map[string]*manager.Subscriptions,
	newSubsParamsMap map[string][]*fePb.RuleParamValues, actorId string, rulesMap map[string]*manager.Rule) error {

	// map[ruleId][paramId]RuleSubscription
	existingSubsMap := make(map[string]map[string]*manager.RuleSubscription)
	for ruleId, subs := range existingSubs {
		existingSubsMap[ruleId] = make(map[string]*manager.RuleSubscription)
		for _, sub := range subs.GetRuleSubscriptions() {
			uniqueParamId, err := getUniqueParamIdForSportsChallenge(sub.GetRuleParamValues().GetRuleParamValues())
			if err != nil {
				logger.Error(ctx, "error in getting unique param Id for sports challenge", zap.Error(err))
				return err
			}
			existingSubsMap[ruleId][uniqueParamId] = sub
		}
	}
	logger.Debug(ctx, "existing subscription map", zap.Any("existingSubsMap", existingSubsMap), zap.Any("existingSubs", existingSubs))

	// map[ruleId][paramId]ParamValues
	newSubsParamFlatMap := make(map[string]map[string]*manager.RuleParamValues)
	for ruleId, newParams := range newSubsParamsMap {
		newSubsParamFlatMap[ruleId] = make(map[string]*manager.RuleParamValues)
		for _, n := range newParams {
			rmsParamValues, err2 := fitttPkg.GetRMSRuleParamValues(ctx, n, rulesMap[ruleId])
			if err2 != nil {
				logger.Error(ctx, "error in getting rms param values, subscribe sports rule flow ", zap.Error(err2))
				return err2
			}
			uniqueParamId, err2 := getUniqueParamIdForSportsChallenge(rmsParamValues.GetRuleParamValues())
			if err2 != nil {
				logger.Error(ctx, "error in getting unique param Id for sports challenge", zap.Error(err2))
				return err2
			}
			newSubsParamFlatMap[ruleId][uniqueParamId] = rmsParamValues
		}
	}
	logger.Debug(ctx, "new subscription param flat map", zap.Any("newSubsFlatMap", newSubsParamFlatMap), zap.Any("newSubs", newSubsParamsMap))

	subsIdsToBePaused := getSubscriptionsToBePaused(existingSubsMap, newSubsParamFlatMap)
	logger.Debug(ctx, "subscriptions to be paused", zap.Strings(logger.SUBSCRIPTION_ID, subsIdsToBePaused))

	if len(subsIdsToBePaused) > 0 {
		updateResp, err2 := s.rmClient.UpdateSubscriptionState(ctx, &manager.UpdateSubscriptionStateRequest{
			State:                 manager.RuleSubscriptionState_INACTIVE,
			ActorId:               actorId,
			StateChangeReason:     manager.SubscriptionStateChangeReason_USER_ACTION,
			StateChangeProvenance: manager.SubscriptionStateChangeProvenance_USER_APP,
			SubIds:                subsIdsToBePaused,
		})
		if err3 := epifigrpc.RPCError(updateResp, err2); err3 != nil {
			logger.Error(ctx, "error in updating subscription states for existing subs", zap.Strings(logger.SUBSCRIPTION_ID, subsIdsToBePaused))
			return err3
		}
	}

	for ruleId, newParams := range newSubsParamFlatMap {
		for paramId, n := range newParams {
			if _, present := existingSubsMap[ruleId][paramId]; present {
				err2 := s.handleExistingSubscriptionSubscribeRequest(ctx, existingSubsMap[ruleId][paramId], n, rulesMap[ruleId], actorId)
				if err2 != nil {
					logger.Error(ctx, "failed to handle existing subscription's subscribe request", zap.Error(err2),
						zap.Any("existing", existingSubsMap[ruleId][paramId]), zap.Any("new requested", n))
					return err2
				}
			} else {
				// create a new subscription
				subscriptionResp, e := s.rmClient.SubscribeRule(ctx, &manager.SubscribeRuleRequest{SubscriptionData: &manager.RuleSubscription{
					ActorId:               actorId,
					RuleParamValues:       n,
					RuleId:                ruleId,
					State:                 manager.RuleSubscriptionState_ACTIVE,
					VersionState:          manager.SubscriptionVersionState_CURRENT,
					StateChangeReason:     manager.SubscriptionStateChangeReason_NEW_SUBSCRIPTION,
					StateChangeProvenance: manager.SubscriptionStateChangeProvenance_USER_APP,
				}})
				if err2 := epifigrpc.RPCError(subscriptionResp, e); err2 != nil {
					logger.Error(ctx, "Failed to subscribe rule", zap.String(logger.RULE_ID, ruleId), zap.Error(err2))
					return err2
				}
			}
		}
	}
	return nil
}

func (s *Service) handleExistingSubscriptionSubscribeRequest(ctx context.Context, existingSub *manager.RuleSubscription,
	newParam *manager.RuleParamValues, rule *manager.Rule, actorId string) error {
	// 1. If nothing should be done on existing sub
	// 2. If existing sub should be updated
	// 3. If existing sub should be resumed
	switch areRuleSubscriptionParamsSimilar(rule, existingSub.GetRuleParamValues().GetRuleParamValues(), newParam.GetRuleParamValues()) {
	case RULE_PARAMS_SIMILAR:
		if existingSub.GetState() == manager.RuleSubscriptionState_INACTIVE {
			logger.Debug(ctx, "All rule params are similar, but subscription inactive, updating state", zap.Any("existingSub", existingSub.GetRuleParamValues().GetRuleParamValues()),
				zap.Any("newSub", newParam.GetRuleParamValues()))
			updateResp, err := s.rmClient.UpdateSubscriptionState(ctx, &manager.UpdateSubscriptionStateRequest{
				SubscriptionId:        existingSub.GetId(),
				ActorId:               actorId,
				State:                 manager.RuleSubscriptionState_ACTIVE,
				StateChangeReason:     manager.SubscriptionStateChangeReason_USER_ACTION,
				StateChangeProvenance: manager.SubscriptionStateChangeProvenance_USER_APP,
			})
			if err2 := epifigrpc.RPCError(updateResp, err); err2 != nil {
				logger.Error(ctx, "error in updating subscription state", zap.Error(err2), zap.String(logger.SUBSCRIPTION_ID, existingSub.GetId()))
				return err2
			}
		} else {
			logger.Debug(ctx, "All rule params are similar and subscription active", zap.Any("existingSub", existingSub.GetRuleParamValues().GetRuleParamValues()),
				zap.Any("newSub", newParam.GetRuleParamValues()))
		}
	case UNIQUE_PARAMS_SIMILAR:
		logger.Debug(ctx, "unique params similar, but rule params differ", zap.Any("existingSub", existingSub.GetRuleParamValues().GetRuleParamValues()),
			zap.Any("newSub", newParam.GetRuleParamValues()))

		updatedFields := []manager.RuleSubscriptionFieldMask{manager.RuleSubscriptionFieldMask_RULE_PARAM_VALUES}
		if existingSub.GetState() == manager.RuleSubscriptionState_INACTIVE {
			updatedFields = append(updatedFields, manager.RuleSubscriptionFieldMask_STATE)
		}

		updatedSubscriptionResp, err := s.rmClient.UpdateRuleSubscription(ctx, &manager.UpdateRuleSubscriptionRequest{
			RuleSubscriptionId:    existingSub.GetId(),
			UserDefinedValues:     newParam,
			State:                 manager.RuleSubscriptionState_ACTIVE,
			StateChangeReason:     manager.SubscriptionStateChangeReason_USER_ACTION,
			StateChangeProvenance: manager.SubscriptionStateChangeProvenance_USER_APP,
			UpdatedFields:         updatedFields,
		})
		if err2 := epifigrpc.RPCError(updatedSubscriptionResp, err); err2 != nil {
			logger.Info(ctx, "Failed to update subscription", zap.Error(err2), zap.String(logger.SUBSCRIPTION_ID, existingSub.GetId()),
				zap.Any("newParamValues", newParam))
			return err2
		}
	case RULE_PARAMS_DISSIMILAR:
		return fmt.Errorf("existing subscription's unique params not matching with new param")
	}
	return nil
}

func getSubscriptionsToBePaused(existingSubsMap map[string]map[string]*manager.RuleSubscription, newParamsMap map[string]map[string]*manager.RuleParamValues) []string {
	var subscriptionsToBePaused []string

	for ruleId, existingSubs := range existingSubsMap {
		for paramId, sub := range existingSubs {
			// for every existing subscription for a rule
			// Pause the subscription if it is not present in new request
			if _, present := newParamsMap[ruleId][paramId]; !present && sub.GetState() == manager.RuleSubscriptionState_ACTIVE {
				subscriptionsToBePaused = append(subscriptionsToBePaused, sub.GetId())
			}
		}
	}
	return subscriptionsToBePaused
}

func getUniqueParamIdForSportsChallenge(values map[string]*manager.Value) (string, error) {
	var uniqueParamId string
	for _, v := range values {
		switch {
		case v.GetTeamVal() != nil:
			uniqueParamId = v.GetTeamVal().GetId()
		case v.GetPlayerVal() != nil:
			uniqueParamId = v.GetPlayerVal().GetId()
		}
	}
	if uniqueParamId == "" {
		return "", fmt.Errorf("team or player value not found for subscription")
	}
	return uniqueParamId, nil
}

// nolint: dupl
func (s *Service) isSportsChallengeAllowed(ctx context.Context, actorId string) (bool, error) {
	assignedGroups, err := s.getAssignedUserGroups(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in getting assigned groups to user", zap.Error(err))
		return false, err
	}

	reqdUserGroups := s.conf.Fittt().AllowedUserGroupsForSportsChallenge()
	// no user group is required
	if len(reqdUserGroups) == 0 {
		return true, nil
	}

	for _, allowedUserGroup := range reqdUserGroups {
		if assignedGroups[commontypes.UserGroup(commontypes.UserGroup_value[allowedUserGroup])] {
			return true, nil
		}
	}
	logger.Debug(ctx, "user is not present in allowed group for sports challenge", zap.Any(logger.USER_GROUP, assignedGroups), zap.Any(logger.ALLOWED_GROUPS, reqdUserGroups))
	return false, nil
}

func (s *Service) getWeeklyDrawCardBasedOnConditionsAndClaimPrizeScreenData(ctx context.Context, actorId string,
	tournamentId string) (*fePb.BannerCard, *fePb.ClaimPrizePageData, bool, error) {
	if !s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().ShowWeeklyBanner() {
		return nil, nil, false, nil
	}

	rank, points, err := s.getUsersRankAndPointsForWeek(ctx, actorId)
	if err != nil {
		return nil, nil, false, err
	}

	showZeroStateCard, err := s.shouldShowWeeklyDrawZeroStateCard(ctx, actorId, rank, points)
	if err != nil {
		return nil, nil, false, err
	}
	if showZeroStateCard {
		logger.Debug(ctx, "showing Weekly draw zero state banner")
		return s.getWeeklyDrawZeroStateCard(), nil, false, nil
	}

	weeklyBonanzaCard, claimPrizeScreenData, qualifiedForReward, err := s.getWeeklyBonanzaCardAndClaimPrizeScreenData(ctx, actorId, tournamentId, rank, points)
	if err != nil {
		return nil, nil, false, err
	}

	if weeklyBonanzaCard != nil {
		return weeklyBonanzaCard, claimPrizeScreenData, qualifiedForReward, nil
	}

	logger.Debug(ctx, "Weekly Draw Banner - Default")
	return s.getWeeklyDrawZeroStateCard(), nil, false, nil
}

func (s *Service) getUsersRankAndPointsForWeek(ctx context.Context, actorId string) (uint32, uint32, error) {
	leaderboardResp, err := s.fitttClient.GetTournamentLeaderboard(ctx, &fittt.GetTournamentLeaderboardRequest{
		ActorId:         actorId,
		TournamentTagId: s.conf.Fittt().FiSavingsLeague().TournamentTagId(),
		RankLimit:       1,
		WeekNo:          s.getCurrentWeekNoForFiSavingsLeague() - 1,
	})
	if err2 := epifigrpc.RPCError(leaderboardResp, err); err2 != nil {
		logger.Error(ctx, "error in getting leaderboard stats from server", zap.Error(err2))
		return 0, 0, err2
	}
	return leaderboardResp.GetUserStats().GetRank(), leaderboardResp.GetUserStats().GetPoints(), nil
}

func (s *Service) getWeeklyBonanzaCardAndClaimPrizeScreenData(ctx context.Context, actorId, tournamentId string, userRank, points uint32) (*fePb.BannerCard,
	*fePb.ClaimPrizePageData, bool, error) {
	weekNo := s.getCurrentWeekNoForFiSavingsLeague() - 1 /*previous week*/
	weeklyReward, err := s.rewardsClient.GetRewardsByActorId(ctx, &rewardsPb.RewardsByActorIdRequest{
		ActorId: actorId,
		Filter: &rewardsPb.RewardsByActorIdRequest_Filter{
			ExternalRefList: []string{fmt.Sprintf("%s_week_%d", tournamentId, weekNo)},
		},
		PageContext: &rpc.PageContextRequest{
			PageSize: 1,
		},
	})
	if err2 := epifigrpc.RPCError(weeklyReward, err); err2 != nil {
		logger.Error(ctx, "Failed to fetch rewards for weekly bonanza", zap.Error(err2))
		return nil, nil, false, err
	}
	logger.Debug(ctx, "Rewards Response", zap.Any("Rewards for weekly bonanza", weeklyReward))

	reward, isPrizeAlreadyClaimed := getWeeklyBonanzaReward(ctx, weeklyReward)
	if reward == nil {
		if isAlreadyAWinnerInPreviousWeeks(userRank, points) {
			return s.getWeeklyDrawAlreadyWinnerStateCard(), nil, false, nil
		}
		logger.Debug(ctx, "user did not qualified for weekly bonanza")
		return getWeeklyBonanzaNotQualifiedCard(), nil, false, nil
	}

	logger.Info(ctx, "user has qualified for weekly bonanza", zap.Any("reward", reward))
	switch {
	case reward.GetGiftHamper() != nil:
		giftHamper, claimPrizeScreenData, err2 := s.getWeeklyBonanzaGiftHamperCardAndClaimPrizeScreenData(ctx, userRank, reward.GetGiftHamper(), isPrizeAlreadyClaimed, actorId)
		return giftHamper, claimPrizeScreenData, true, err2
	case reward.GetCash() != nil:
		cashPrizeCard, err2 := s.getWeeklyBonanzaCashPrizeCard(userRank)
		return cashPrizeCard, nil, true, err2
	default:
		logger.Error(ctx, "weekly bonanza reward not recognized", zap.Any("reward", reward))
		return nil, nil, false, fmt.Errorf("weekly bonanza reward not recognized")
	}
}

func getWeeklyBonanzaReward(ctx context.Context, weeklyReward *rewardsPb.RewardsResponse) (*rewardsPb.RewardOption, bool) {
	for _, reward := range weeklyReward.GetRewards() {
		switch reward.GetStatus() {
		case rewardsPb.RewardStatus_CREATED:
			for _, rewardOption := range reward.GetRewardOptions().GetOptions() {
				// Gift hamper eg: iPhone, iPad, Airpods etc
				if rewardOption.GetRewardType() == rewardsPb.RewardType_GIFT_HAMPER ||
					rewardOption.GetRewardType() == rewardsPb.RewardType_CASH {
					logger.Info(ctx, "user won a gift hamper or cash")
					return rewardOption, false /*user did not claim the reward yet*/
				}
			}
		case rewardsPb.RewardStatus_PROCESSED:
			for _, rewardOption := range reward.GetRewardOptions().GetOptions() {
				// Gift hamper eg: iPhone, iPad, Airpods etc
				if rewardOption.GetRewardType() == rewardsPb.RewardType_GIFT_HAMPER ||
					rewardOption.GetRewardType() == rewardsPb.RewardType_CASH {
					logger.Info(ctx, "user won a gift hamper or cash")
					return rewardOption, true /*user has already claimed the reward*/
				}
			}
		default:
			logger.Info(ctx, "rewards state not handled", zap.String(logger.STATE, reward.GetStatus().String()))
		}
	}
	return nil, false
}

func (s *Service) getWeeklyBonanzaGiftHamperCardAndClaimPrizeScreenData(ctx context.Context, rank uint32, hamper *rewardsPb.GiftHamper,
	isPrizeAlreadyClaimed bool, actorId string) (*fePb.BannerCard, *fePb.ClaimPrizePageData, error) {
	var title, subTitle, desc, imgUrl, claimPrizeTitle, claimPrizeDesc string
	switch rank {
	case 1:
		title = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank1().Title()
		subTitle = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank1().SubTitle()
		imgUrl = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank1().ImgUrl()
		desc = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank1().Description()
		claimPrizeTitle = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank1().ClaimPrizeScreenTitle()
		claimPrizeDesc = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank1().ClaimPrizeScreenDescription()
	case 2:
		title = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank2().Title()
		subTitle = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank2().SubTitle()
		imgUrl = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank2().ImgUrl()
		desc = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank2().Description()
		claimPrizeTitle = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank2().ClaimPrizeScreenTitle()
		claimPrizeDesc = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank2().ClaimPrizeScreenDescription()
	case 3:
		title = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank3().Title()
		subTitle = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank3().SubTitle()
		imgUrl = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank3().ImgUrl()
		desc = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank3().Description()
		claimPrizeTitle = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank3().ClaimPrizeScreenTitle()
		claimPrizeDesc = s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank3().ClaimPrizeScreenDescription()
	default:
		err := fmt.Errorf("user is not qualified for reward, received rewards object %d", rank)
		logger.Error(ctx, "error in showing weekly bonanza card", zap.Error(err), zap.Any("reward", hamper))
		return nil, nil, err
	}

	claimPrizeScreen, err := s.getClaimPrizeScreenData(ctx, actorId, imgUrl, claimPrizeTitle, claimPrizeDesc, isPrizeAlreadyClaimed)
	if err != nil {
		logger.Error(ctx, "error in getting claim prize screen data", zap.Error(err))
		return nil, nil, err
	}

	return getWeeklyBonanzaCard(title, subTitle, desc, imgUrl), claimPrizeScreen, nil
}

// nolint: funlen
func (s *Service) getClaimPrizeScreenData(ctx context.Context, actorId, imgUrl, claimPrizeTitle, claimPrizeDesc string,
	isPrizeAlreadyClaimed bool) (*fePb.ClaimPrizePageData, error) {

	if isPrizeAlreadyClaimed {
		logger.Debug(ctx, "not showing claim prize screen to user, as prize is already claimed")
		return nil, nil
	}

	profileResp, err := s.rmClient.GetProfile(ctx, &manager.GetProfileRequest{
		ActorId: actorId,
		Client:  event.RMSClient_FITTT,
	})
	if err2 := epifigrpc.RPCError(profileResp, err); err2 != nil {
		logger.Error(ctx, "error in getting fittt profile", zap.Error(err2))
		return nil, err2
	}

	// if user has already visited claim prize screen, do not show the screen again
	if profileResp.GetProfile().GetFitttProfile().GetVisitedSportsChallengeClaimPrizeScreen() {
		return nil, nil
	}

	// if user is visited claim prize screen for the first time, update `VisitedSportsChallengeClaimPrizeScreen` to true
	// Note: This should have been ideal solved by moving the UpdateProfile request from client, as failure in further processing of API will
	// not be able to rollback the updated state
	// but according to client team, adding this to client is of some effort and not seeing the Claim prize screen in case of above scenario is not business
	// critical case
	profileResp.GetProfile().GetFitttProfile().VisitedSportsChallengeClaimPrizeScreen = true
	updateProfileResp, e := s.rmClient.UpdateProfile(ctx, &manager.UpdateProfileRequest{
		ActorId: actorId,
		Client:  event.RMSClient_FITTT,
		Profile: profileResp.GetProfile(),
	})
	if err2 := epifigrpc.RPCError(updateProfileResp, e); err2 != nil {
		logger.Error(ctx, "error in updating fittt profile", zap.Error(err2))
		return nil, err2
	}

	return &fePb.ClaimPrizePageData{
		DisplayInfo: &fePb.CardDisplayInfo{
			ImgUrl:  imgUrl,
			BgColor: "#C0D9E0",
		},
		Title: &fePb.Text{
			Text: claimPrizeTitle,
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#5684AE",
			},
		},
		SubTitle: &fePb.Text{
			Text: claimPrizeDesc,
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#646464",
			},
		},
		ClaimPrizeCta: &fePb.CTA{
			Text: &fePb.Text{
				Text:        "Claim Prize",
				DisplayInfo: &fePb.TextDisplayInfo{FontColor: "#00B899"},
			},
			Action: &deeplinkpb.Deeplink{
				Screen: deeplinkpb.Screen_MY_REWARDS_SCREEN,
			},
			Id: "claim_prize",
		},
	}, nil

}

func (s *Service) getWeeklyBonanzaCashPrizeCard(rank uint32) (*fePb.BannerCard, error) {
	if rank <= 50 {
		title := s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank4To50().Title()
		subTitle := s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank4To50().SubTitle()
		imgUrl := s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank4To50().ImgUrl()
		desc := s.conf.Fittt().FiSavingsLeague().WeeklyRewardsCardDisplayData().Rank4To50().Description()
		return getWeeklyBonanzaCard(title, subTitle, desc, imgUrl), nil
	}
	return nil, fmt.Errorf("user did not qualify for weekly bonanza, but reward is present %d", rank)
}

func getWeeklyBonanzaCard(title, subTitle, desc, imgUrl string) *fePb.BannerCard {
	return &fePb.BannerCard{
		Id: "weekly_bonanza",
		Title: &fePb.Text{
			Text: title,
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#478295",
				BgColor:   "#DEEEF2",
			},
		},
		CardDisplayInfo: &fePb.CardDisplayInfo{
			BgColor: "#C0DAE0",
			ImgUrl:  imgUrl,
		},
		CardCta: &fePb.CTA{
			Action: &deeplinkpb.Deeplink{
				Screen:        deeplinkpb.Screen_MY_REWARDS_SCREEN,
				ScreenOptions: nil,
			}},
		Header: &fePb.Text{
			Text: "WEEKLY DRAW RESULTS ARE IN",
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#478295",
			},
		},
		SubTitle:    &fePb.Text{Text: subTitle},
		Description: &fePb.Text{Text: desc},
	}
}

func (s *Service) shouldShowWeeklyDrawZeroStateCard(ctx context.Context, actorId string, rank, points uint32) (bool, error) {
	if isAlreadyAWinnerInPreviousWeeks(rank, points) {
		return false, nil
	}

	// if it is the first week, show zero state throughout the week
	if s.conf.Fittt().FiSavingsLeague().WeeklyRewardsFirstWeekNo() == s.getCurrentWeekNoForFiSavingsLeague() {
		logger.Debug(ctx, "First week of challenge, not expecting any other state for banner")
		return true, nil
	}

	sow := datetime.StartOfWeek(time.Now().In(datetime.IST), time.Sunday)
	// we want to show zero state after midweek (after wednesday 12 noon)
	if time.Since(sow) > s.conf.Fittt().FiSavingsLeague().WeeklyCardStateResetDuration() {
		logger.Debug(ctx, "resetting card to zero state, configured time to reset from winning state reached")
		return true, nil
	}

	if !s.isWeeklyDrawDeclaredForCurrentWeek() {
		logger.Debug(ctx, "weekly draw has not been declared for current week, sending zero state")
		return true, nil
	}

	// exactly one week back
	oneWeekBefore := time.Now().In(datetime.IST).Add(time.Duration(-7*24) * time.Hour)
	previousWeekSOW := timestamppb.New(datetime.StartOfWeek(oneWeekBefore, time.Sunday))
	previousWeekEOW := timestamppb.New(datetime.EndOfWeek(oneWeekBefore, time.Saturday))
	matches, err := s.getMatches(ctx, previousWeekSOW, previousWeekEOW)
	if err != nil {
		return false, err
	}

	hasSubscription, err := s.hasSubscriptionForMatches(ctx, actorId, matches)
	if err != nil {
		return false, err
	}

	if !hasSubscription {
		logger.Debug(ctx, "User does not had any subscription for previous week, sending zero state")
		return true, nil
	}

	return false, nil
}

func (s *Service) isWeeklyDrawDeclaredForCurrentWeek() bool {
	currentTime := time.Now().In(datetime.IST)
	// if weekly rewards have not been declared yet, show zero state
	if currentTime.Weekday() > time.Weekday(s.conf.Fittt().FiSavingsLeague().WeeklyRewardsDeclarationDay()) {
		return true
	}

	if currentTime.Weekday() < time.Weekday(s.conf.Fittt().FiSavingsLeague().WeeklyRewardsDeclarationDay()) {
		return false
	}

	// current day is same as weekly draw reveal day
	hours := s.conf.Fittt().FiSavingsLeague().LeaderboardRefreshTime().Hours()
	minutes := s.conf.Fittt().FiSavingsLeague().LeaderboardRefreshTime().Minutes()

	weeklyDrawRevealTime := datetime.StartOfDay(time.Now()).
		Add(time.Duration(hours) * time.Hour).
		Add(time.Duration(minutes) * time.Minute)

	return weeklyDrawRevealTime.Before(time.Now())
}

// nolint: dupl
func (s *Service) getWeeklyDrawAlreadyWinnerStateCard() *fePb.BannerCard {
	return &fePb.BannerCard{
		Id: "weekly_bonanza",
		Title: &fePb.Text{
			Text: "Weekly bonanza",
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#6F62A4",
				BgColor:   "#CDC6E8",
			},
		},
		CardDisplayInfo: &fePb.CardDisplayInfo{
			BgColor: "#C0B7E1",
			ImgUrl:  "https://epifi-icons.pointz.in/fittt-images/sports-challenge/championship-cup-tilted-banner.png",
		},
		CardCta: &fePb.CTA{
			Action: &deeplinkpb.Deeplink{
				Screen:        deeplinkpb.Screen_REWARDS_WAYS_TO_EARN,
				ScreenOptions: nil,
			}},
		Header: &fePb.Text{
			Text: s.getWeeklyDrawRevealTimeString(),
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#6F62A4",
			},
		},
		SubTitle:    &fePb.Text{Text: "You’ve already won the big prize!"},
		Description: &fePb.Text{Text: "You’re eligible to win only one large prize, but you still play in daily draw <b>Know more</b>"},
		KnowMoreCta: &fePb.CTA{Id: "know_more_cta"},
	}
}

func getWeeklyBonanzaNotQualifiedCard() *fePb.BannerCard {
	return &fePb.BannerCard{
		Id: "weekly_bonanza",
		Title: &fePb.Text{
			Text: "You did not qualify!",
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#6F62A4",
				BgColor:   "#CDC6E8",
			},
		},
		CardDisplayInfo: &fePb.CardDisplayInfo{
			BgColor: "#C0B7E1",
			ImgUrl:  "https://epifi-icons.pointz.in/fittt-images/sports-challenge/championship-cup-tilted-banner.png",
		},
		CardCta: &fePb.CTA{
			Action: &deeplinkpb.Deeplink{
				Screen:        deeplinkpb.Screen_REWARDS_WAYS_TO_EARN,
				ScreenOptions: nil,
			}},
		Header: &fePb.Text{
			Text: "WEEKLY DRAW RESULTS ARE IN",
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#6F62A4",
			},
		},
		SubTitle:    &fePb.Text{Text: "Sorry, you’re not in the Top 50"},
		Description: &fePb.Text{Text: "Chin up! Keep playing & maybe you will climb up the ranks next week"},
	}
}

// nolint: dupl
func (s *Service) getWeeklyDrawZeroStateCard() *fePb.BannerCard {
	return &fePb.BannerCard{
		Id: "weekly_bonanza",
		Title: &fePb.Text{
			Text: "Weekly bonanza",
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#6F62A4",
				BgColor:   "#CDC6E8",
			},
		},
		CardDisplayInfo: &fePb.CardDisplayInfo{
			BgColor: "#C0B7E1",
			ImgUrl:  "https://epifi-icons.pointz.in/fittt-images/sports-challenge/championship-cup-tilted-banner.png",
		},
		CardCta: &fePb.CTA{
			Action: &deeplinkpb.Deeplink{
				Screen:        deeplinkpb.Screen_REWARDS_WAYS_TO_EARN,
				ScreenOptions: nil,
			}},
		Header: &fePb.Text{
			Text: s.getWeeklyDrawRevealTimeString(),
			DisplayInfo: &fePb.TextDisplayInfo{
				FontColor: "#6F62A4",
			},
		},
		SubTitle:    &fePb.Text{Text: "Chance to win prizes worth ₹2L"},
		Description: &fePb.Text{Text: "Top 50 ranked on the Leaderboard win <br>₹1000 cashback <b>Know more</b>"},
		KnowMoreCta: &fePb.CTA{
			Id: "know_more_cta",
		},
	}
}

func (s *Service) getWeeklyDrawRevealTimeString() string {
	timeDisplayString := getTimeDisplayString(s.conf.Fittt().FiSavingsLeague().LeaderboardRefreshTime().Hours(),
		s.conf.Fittt().FiSavingsLeague().LeaderboardRefreshTime().Minutes())
	return fmt.Sprintf("RESULTS EVERY %s %s",
		time.Weekday(s.conf.Fittt().FiSavingsLeague().WeeklyRewardsDeclarationDay()).String(), timeDisplayString)
}
