package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/pkg/banking"
)

const (
	EventBeneficiaryNameLookupFailure = "BeneficiaryNameLookupFailure"
	ServiceName                       = "frontend-timeline"
)

// BeneficiaryNameLookupFailureEvent represents a failure in looking up beneficiary name details
type BeneficiaryNameLookupFailureEvent struct {
	ActorId         string
	EventId         string
	Timestamp       time.Time
	EventType       string
	ServiceName     string
	EventName       string
	BankName        string
	BankCode        string
	ResponseCode    string
	ResponseReason  string
	PaymentProtocol string
}

// NewBeneficiaryNameLookupFailureEvent creates a new event for tracking beneficiary name lookup failures
func NewBeneficiaryNameLookupFailureEvent(
	actorId string,
	ifscCode string,
	responseCode string,
	responseReason string,
	paymentProtocol string,
) *BeneficiaryNameLookupFailureEvent {
	// Extract bank code (first 4 characters of IFSC)
	bankCode := ""
	if len(ifscCode) >= 4 {
		bankCode = ifscCode[:4]
	}

	// Get bank name from bank code
	bankName := "UNKNOWN"
	bank, err := banking.GetBankFromIfsc(ifscCode)
	if err == nil {
		bankName = bank.String()
	}

	return &BeneficiaryNameLookupFailureEvent{
		ActorId:         actorId,
		EventId:         uuid.New().String(),
		Timestamp:       time.Now(),
		EventType:       events.EventTrack,
		ServiceName:     ServiceName,
		EventName:       EventBeneficiaryNameLookupFailure,
		BankName:        bankName,
		BankCode:        bankCode,
		ResponseCode:    responseCode,
		ResponseReason:  responseReason,
		PaymentProtocol: paymentProtocol,
	}
}

func (e *BeneficiaryNameLookupFailureEvent) GetEventId() string {
	return e.EventId
}

func (e *BeneficiaryNameLookupFailureEvent) GetUserId() string {
	return e.ActorId
}

func (e *BeneficiaryNameLookupFailureEvent) GetProspectId() string {
	return ""
}

func (e *BeneficiaryNameLookupFailureEvent) GetEventName() string {
	return e.EventName
}

func (e *BeneficiaryNameLookupFailureEvent) GetEventType() string {
	return e.EventType
}

func (e *BeneficiaryNameLookupFailureEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(e, properties)
	return properties
}

func (e *BeneficiaryNameLookupFailureEvent) GetEventTraits() map[string]interface{} {
	return nil
}
