package rewards

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/Knetic/govaluate"
	"github.com/golang/protobuf/ptypes" // nolint:depguard
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	anyPb "google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/gamma/pkg/accrual"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	beCasperPb "github.com/epifi/gamma/api/casper"
	beDiscountsPb "github.com/epifi/gamma/api/casper/discounts"
	beExchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	beRedemptionPb "github.com/epifi/gamma/api/casper/redemption"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palEnumFePb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/frontend/rewards"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	rewardsFrontendPkgPb "github.com/epifi/gamma/api/frontend/rewards/pkg"
	beKycPb "github.com/epifi/gamma/api/kyc"
	vkycBe "github.com/epifi/gamma/api/kyc/vkyc"
	beRewardsPb "github.com/epifi/gamma/api/rewards"
	luckydrawPb "github.com/epifi/gamma/api/rewards/luckydraw"
	beRewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	savingsPb "github.com/epifi/gamma/api/savings"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	rewardsScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"

	"github.com/epifi/gamma/frontend/config"
	rewardsFrontendPkg "github.com/epifi/gamma/frontend/rewards/pkg"
	"github.com/epifi/gamma/frontend/rewards/tags"
	"github.com/epifi/gamma/pkg/address"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

// nolint:dupl
func isRewardUnitsUtilMaxed(offer *beRewardOffersPb.RewardOffer, offerGroup *beRewardOffersPb.RewardOfferGroup, offerRewardUnitsUtil *beRewardOffersPb.RewardOfferRewardUnitsActorUtilisation, offerGroupRewardUnitsUtil *beRewardOffersPb.RewardOfferGroupRewardUnitsActorUtilisation) bool {
	unitsCaps := offer.GetRewardMeta().GetRewardAggregates().GetRewardUnitsCapUserAggregate().GetUnitsCaps()
	for _, unitsCap := range unitsCaps {
		switch unitsCap.GetRewardType() {
		case beRewardsPb.RewardType_FI_COINS:
			if unitsCap.GetUnits() != 0 && unitsCap.GetUnits() <= offerRewardUnitsUtil.GetFiCoinsUnits() {
				return true
			}
		case beRewardsPb.RewardType_CASH:
			if unitsCap.GetUnits() != 0 && unitsCap.GetUnits() <= offerRewardUnitsUtil.GetCashUnits() {
				return true
			}
		case beRewardsPb.RewardType_SMART_DEPOSIT:
			if unitsCap.GetUnits() != 0 && unitsCap.GetUnits() <= offerRewardUnitsUtil.GetSdCashUnits() {
				return true
			}
		default:
			logger.ErrorNoCtx("Cannot identify Reward Type in isRewardOfferUnitsUtilMaxed", zap.Any("Reward Type", unitsCap.GetRewardType()))

		}
	}

	if offerGroup == nil {
		return false
	}

	groupUnitsCaps := offerGroup.GetRewardUnitsCapUserAggregate().GetUnitsCaps()
	for _, unitsCap := range groupUnitsCaps {
		switch unitsCap.GetRewardType() {
		case beRewardsPb.RewardType_FI_COINS:
			if unitsCap.GetUnits() != 0 && unitsCap.GetUnits() <= offerGroupRewardUnitsUtil.GetFiCoinsUnits() {
				return true
			}
		case beRewardsPb.RewardType_CASH:
			if unitsCap.GetUnits() != 0 && unitsCap.GetUnits() <= offerGroupRewardUnitsUtil.GetCashUnits() {
				return true
			}
		case beRewardsPb.RewardType_SMART_DEPOSIT:
			if unitsCap.GetUnits() != 0 && unitsCap.GetUnits() <= offerGroupRewardUnitsUtil.GetSdCashUnits() {
				return true
			}
		default:
			logger.ErrorNoCtx("Cannot identify Reward Type in isRewardOfferUnitsUtilMaxed", zap.Any("Reward Type", unitsCap.GetRewardType()))

		}
	}
	return false
}

func (r *RewardService) getRewardOfferTileTagProperties(ctx context.Context, beRewardOffer *beRewardOffersPb.RewardOffer, offerInventory *fePb.RewardOfferInventory, isRewardOfferUnitsMaxed bool) (tagText, tagBgColor string) {
	if beRewardOffer == nil {
		return "", ""
	}
	tagTypeToTagTextMap, tagTypeToTagBgColorMap := r.rewardsFrontendMeta.RewardOfferTileTagTypeToTagText, r.rewardsFrontendMeta.RewardOfferTileTagTypeToTagBgColor

	// totalCount 0 implies infinite inventory, for finite inventory cases only we should show 'COMPLETED' tag
	// if total count of offer equals claimed count, then we want to show completed tag.
	if isRewardOfferUnitsMaxed || (offerInventory != nil && offerInventory.GetTotalCount() != 0 && offerInventory.GetClaimedCount() == offerInventory.GetTotalCount()) {
		tagType := "COMPLETED"
		return tagTypeToTagTextMap[tagType], tagTypeToTagBgColorMap[tagType]
	}

	// if reward offer was created withing last 24 hours, then we want to show 'NEW' tag on that offer.
	displaySince, err := time.Parse(time.RFC3339, beRewardOffer.GetDisplayMeta().GetDisplaySince())
	if err != nil {
		// parsing error shouldn't fail the request
		logger.Error(ctx, "error parsing reward offer display since time", zap.String("display_since", beRewardOffer.GetDisplayMeta().GetDisplaySince()), zap.Error(err))
		return "", ""
	}
	if time.Now().Before(displaySince.Add(24 * time.Hour)) {
		tagType := "NEW"
		return tagTypeToTagTextMap[tagType], tagTypeToTagBgColorMap[tagType]
	}
	return "", ""
}

func (r *RewardService) getFeDisplayType(beDisplayType beRewardOffersPb.DisplayType) fePb.DisplayType {
	switch beDisplayType {
	case beRewardOffersPb.DisplayType_FRINGE:
		return fePb.DisplayType_FRINGE
	case beRewardOffersPb.DisplayType_HEADLINE:
		return fePb.DisplayType_HEADLINE
	default:
		return fePb.DisplayType_UNSPECIFIED_DISPLAY_TYPE
	}
}

// nolint:funlen
func (r *RewardService) getFeReward(ctx context.Context, actorId string, beReward *beRewardsPb.Reward, userAndAppAttributes *UserAndAppAttributes, deeplinksMap map[deeplinkName]*deepLinkPb.Deeplink) (*fePb.Reward, error) {
	var (
		appPlatform = userAndAppAttributes.AppPlatform
		appVersion  = userAndAppAttributes.AppVersionCode
	)

	status, err := r.getRewardStatus(ctx, beReward, userAndAppAttributes, appVersion, appPlatform)
	if err != nil {
		return nil, errors.Wrap(err, "unable to compute status of reward")
	}

	tileText, err := r.getTileText(status, beReward, userAndAppAttributes)
	if err != nil {
		return nil, errors.Wrap(err, "unable to compute reward tile text")
	}

	statusDesc, err := r.getRewardStatusDescText(status, beReward)
	if err != nil {
		return nil, errors.Wrap(err, "unable to compute reward status desc")
	}

	tileBgImage := r.getRewardTileBgImage(status, beReward)

	feChosenRewardOption, err := r.getFeChosenRewardOption(ctx, beReward.GetChosenReward(), beReward)
	if err != nil {
		return nil, errors.Wrap(err, "unable to compute chosen reward option")
	}

	rewardOptions, err := r.getFeRewardOptions(ctx, actorId, beReward, appPlatform, appVersion, userAndAppAttributes)
	if err != nil {
		return nil, errors.Wrap(err, "unable to compute reward options")
	}

	// note: can be broken down into two separate methods if the logic for bottom tag and border color diverge tomorrow
	bottomTag, borderColor := r.getRewardTileBottomTagAndBorderColor(ctx, actorId, beReward, appPlatform, appVersion, userAndAppAttributes)
	// currently the tag at tile bottom is same as reward-details tag below reward-units
	if feChosenRewardOption != nil {
		feChosenRewardOption.GetDisplayDetails().RewardDetailsTag = bottomTag
	}

	// populate reward unlock details if reward is in locked state
	unlockDetails, unlockDetailsErr := r.getRewardUnlockDisplayDetails(ctx, userAndAppAttributes, status, beReward, deeplinksMap)
	if unlockDetailsErr != nil {
		// we won't break the flow if we fail to fetch unlocking CTA, and simply pass `nil`
		logger.Error(ctx, "failed to get cta containing info on how to unlock locked reward", zap.String(logger.REWARD_ID, beReward.GetId()), zap.Error(unlockDetailsErr))
	}

	res := &fePb.Reward{
		Id:         beReward.GetId(),
		ExternalId: beReward.GetExternalId(),
		RefId:      r.fetchFeRewardRefId(ctx, beReward),
		Status:     status,
		// todo(divyadeep): deprecate this field"
		StatusBgColor:       r.getStatusBgColor(status, beReward, userAndAppAttributes),
		ProcessingSubStatus: beReward.GetSubStatus(), // todo (utkarsh) : what would it be for lucky draw ?
		CreatedAt:           beReward.GetCreatedAt(),
		RewardOptions:       rewardOptions,
		ClaimedReward:       feChosenRewardOption,
		ActionDesc:          beReward.GetRewardOptions().GetActionDetails(),
		StatusDesc:          statusDesc,
		IsTileClickable:     r.isTileClickable(),
		TileText:            tileText,
		TileBgImage:         tileBgImage,
		TileTopRightIcon:    r.getFeRewardTileTopRightIcon(ctx, actorId, status, beReward, userAndAppAttributes, appPlatform, appVersion),
		InoperableInfo:      r.getFeRewardBottomSheetInfo(ctx, status, beReward, userAndAppAttributes),
		TileThemeType:       r.getFeRewardTileThemeType(beReward.GetRewardDisplay().GetRewardTileThemeType()),
		SkipAnimationCta:    r.getSkipAnimationCta(beReward.GetRewardDisplay().GetIsAnimationUnskippable()),
		SkipAnimation:       beReward.GetRewardDisplay().GetSkipAnimation(),
		AnimationSpeed:      beReward.GetRewardDisplay().GetAnimationSpeed(),
		BottomTag:           bottomTag,
		BorderColor:         borderColor,
		RewardUnlockDetails: unlockDetails,
	}
	return res, nil
}

// getRewardTileBottomTagAndBorderColor generates the bottom tag and border color for the reward tile based on the tags associated
// with the reward and the priorities associated with them
// nolint:funlen
func (r *RewardService) getRewardTileBottomTagAndBorderColor(ctx context.Context, actorId string, beReward *beRewardsPb.Reward, appPlatform commontypes.Platform, appVersion uint32, userAndAppAttributes *UserAndAppAttributes) (*commontypes.Text, string) {
	if userAndAppAttributes != nil {
		if !r.checkIfClientSupportsBoosterFields(ctx, actorId, false, userAndAppAttributes.IsTieringEnabled, userAndAppAttributes.AccountTier, appPlatform, appVersion) {
			return nil, ""
		}
	} else {
		// if tiering related info is not available, we'd force check them in the evaluator method
		if !r.checkIfClientSupportsBoosterFields(ctx, actorId, true, false, tieringExtPb.Tier_TIER_UNSPECIFIED, appPlatform, appVersion) {
			return nil, ""
		}
	}

	var (
		bottomTag         *commontypes.Text
		borderColor       string
		associatedTag     beRewardsPb.RewardTag
		tagsToCheckWithin = beReward.GetTags()
	)

	for _, option := range beReward.GetRewardOptions().GetOptions() {
		tagsToCheckWithin = lo.Union(tagsToCheckWithin, option.GetDisplay().GetTags())
	}

	// priority for the tags matter here
	for _, tag := range []beRewardsPb.RewardTag{beRewardsPb.RewardTag_SALARY_PROGRAM, beRewardsPb.RewardTag_TIER_FI_INFINITE, beRewardsPb.RewardTag_TIER_FI_PLUS} {
		if lo.Contains(tagsToCheckWithin, tag) {
			associatedTag = tag
			break
		}
	}

	logger.Debug(ctx, "associated tag for rewards", zap.Any("associatedTag", associatedTag))

	switch associatedTag {
	case beRewardsPb.RewardTag_SALARY_PROGRAM:
		bottomTag = &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "SALARY EXCLUSIVE"},
			FontColor:    "#4F71AB",
			BgColor:      "#D1DAF1",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_3,
			},
		}
		borderColor = "#BBC8E9"
	case beRewardsPb.RewardTag_TIER_FI_INFINITE:
		bottomTag = &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "INFINITE"},
			FontColor:    "#694980",
			BgColor:      "#CDC6E8",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_3,
			},
		}
		borderColor = "#CDC6E8"
	case beRewardsPb.RewardTag_TIER_FI_PLUS:
		bottomTag = &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "PLUS"},
			FontColor:    "#5D7D4C",
			BgColor:      "#D9F2CC",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_3,
			},
		}
		borderColor = "#C5E9B2"
	default:
		// do nothing
	}

	return bottomTag, borderColor
}

// Deprecated: do not use
// not required any more as it was kept only for backwards compatibility
// todo(divyadeep): remove this method if not using it doesn't break anything on old clients.
func (r *RewardService) getFeRewardTileTopLeftIcon(ctx context.Context, actorId string, rewardStatus fePb.RewardStatus, beReward *beRewardsPb.Reward, userAndAppAttributes *UserAndAppAttributes, appPlatform commontypes.Platform, appVersion uint32) *commontypes.Image {
	switch rewardStatus {
	case fePb.RewardStatus_LOCKED:
		// we don't want to show a lock icon when a reward is time locked
		if beReward.GetRewardOptions().GetUnlockDate().AsTime().After(time.Now().Add(r.dyconf.RewardsFrontendMeta().MinTimeDifferenceToShowTimeBasedLockDetails())) {
			return nil
		}
		// show the locked icon on top-left corner of tile when the reward is supposed to be locked for min-kyc users
		if userAndAppAttributes != nil && userAndAppAttributes.IsLockingRewardsForMinKycUsersSupported &&
			!beReward.GetRewardOptions().GetIsUnlockedForMinKyc() && userAndAppAttributes.KycLevel == beKycPb.KYCLevel_MIN_KYC &&
			!r.checkIfClientSupportsBoosterFields(ctx, actorId, false, userAndAppAttributes.IsTieringEnabled, userAndAppAttributes.AccountTier, appPlatform, appVersion) {
			return &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/rewards/top-left-tile-lock.png",
			}
		}
	default:
	}

	return nil
}

func (r *RewardService) getFeRewardTileTopRightIcon(ctx context.Context, actorId string, rewardStatus fePb.RewardStatus, beReward *beRewardsPb.Reward, userAndAppAttributes *UserAndAppAttributes, appPlatform commontypes.Platform, appVersion uint32) *commontypes.Image {
	switch rewardStatus {
	case fePb.RewardStatus_LOCKED:
		// we don't want to show a lock icon when a reward is time locked
		if beReward.GetRewardOptions().GetUnlockDate().AsTime().After(time.Now().Add(r.dyconf.RewardsFrontendMeta().MinTimeDifferenceToShowTimeBasedLockDetails())) {
			return nil
		}

		// show the locked icon on top-right corner of tile when the reward is supposed to be locked for min-kyc users
		if userAndAppAttributes != nil && userAndAppAttributes.IsLockingRewardsForMinKycUsersSupported &&
			!beReward.GetRewardOptions().GetIsUnlockedForMinKyc() && userAndAppAttributes.KycLevel == beKycPb.KYCLevel_MIN_KYC &&
			!beReward.GetRewardOptions().GetIsImplicitLockingDisabled() &&
			r.checkIfClientSupportsBoosterFields(ctx, actorId, false, userAndAppAttributes.IsTieringEnabled, userAndAppAttributes.AccountTier, appPlatform, appVersion) {
			return &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/rewards/tile-top-right-lock.png",
			}
		}

		// we would want to show a lock icon if reward is IMPLICITLY or EXPLICITLY LOCKED
		if beReward.GetSubStatusV2() == beRewardsPb.SubStatus_SUB_STATUS_IMPLICITLY_LOCKED || beReward.GetSubStatusV2() == beRewardsPb.SubStatus_SUB_STATUS_EXPLICITLY_LOCKED {
			return &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/rewards/tile-top-right-lock.png",
			}
		}
	default:
	}

	return nil
}

// nolint:funlen
func (r *RewardService) getFeRewardBottomSheetInfo(ctx context.Context, rewardStatus fePb.RewardStatus, beReward *beRewardsPb.Reward, userAndAppAttributes *UserAndAppAttributes) *fePb.BottomSheetInfo {
	switch rewardStatus {
	case fePb.RewardStatus_LOCKED:
		// if reward is supposed to be locked for min-kyc users, we show a bottom-sheet with CTA to VKYC
		if userAndAppAttributes != nil && userAndAppAttributes.IsLockingRewardsForMinKycUsersSupported &&
			!beReward.GetRewardOptions().GetIsUnlockedForMinKyc() && userAndAppAttributes.KycLevel == beKycPb.KYCLevel_MIN_KYC {
			bottomSheet := &fePb.BottomSheetInfo{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: accrual.ReplaceCoinWithPointIfApplicable(r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().Title())},
					FontColor:    r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().TitleColor(),
				},
				Desc: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().Desc()},
					FontColor:    r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().DescColor(),
				},
				Image: &commontypes.Image{
					ImageUrl: r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().ImageUrl(),
				},
			}

			if r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().Cta().IsEnabled() {
				vkycDeeplink, _ := vkycPkg.BuildVKYCStatusDeeplink(&vkycPkg.StatusScreenOptions{
					EntryPoint:           vkycBe.EntryPoint_ENTRY_POINT_REWARDS,
					EntryPointDeprecated: deepLinkPb.EntryPoint_ENTRY_POINT_REWARDS,
				})
				bottomSheet.Cta = &fePb.CTA{
					Text: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().Cta().Text()},
						FontColor:    r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().Cta().TextColor(),
						BgColor:      r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().Cta().BgColor(),
					},
					IsVisible: r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().Cta().IsVisible(),
					// todo: read deeplink config from yml once screen-options is supported via the same
					DeeplinkAction: vkycDeeplink,
				}
			}

			return bottomSheet
		}

		// case of explicit locking
		if beReward.GetSubStatusV2() == beRewardsPb.SubStatus_SUB_STATUS_EXPLICITLY_LOCKED {
			// when reward is explicitly locked, we need to fetch offer details to get the unlocking CTA
			rewardOffersRes, err := r.rewardOffersClient.GetRewardOffersByIds(ctx, &beRewardOffersPb.GetRewardOffersByIdsRequest{Ids: []string{beReward.GetOfferId()}})
			if rpcErr := epifigrpc.RPCError(rewardOffersRes, err); rpcErr != nil {
				logger.Error(ctx, "error while fetching reward offer to obtain unlocking CTA, no CTA will be returned", zap.String(logger.REWARD_ID, beReward.GetId()), zap.Error(rpcErr))
				return nil
			}

			if len(rewardOffersRes.GetRewardOffers()) == 0 {
				logger.Error(ctx, "no reward offer found for given ID, no  unlock CTA will be returned", zap.String(logger.REWARD_ID, beReward.GetId()))
				return nil
			}
			unlockMeta := rewardOffersRes.GetRewardOffers()[0].GetUnlockMeta()

			bottomSheet := &fePb.BottomSheetInfo{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: unlockMeta.GetCta().GetName()},
					FontColor:    r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().TitleColor(),
				},
				Desc: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: unlockMeta.GetCta().GetDesc()},
					FontColor:    r.dyconf.RewardsFrontendMeta().LockedRewardInoperableInfoForMinKyc().DescColor(),
				},
				Image: &commontypes.Image{
					ImageUrl: unlockMeta.GetCta().GetIconUrl(),
				},
				Cta: &fePb.CTA{
					Text: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Continue"},
						FontColor:    colorFiSnow,
						BgColor:      colorFiGreen,
					},
					DeeplinkAction: unlockMeta.GetCta().GetDeeplink(),
				},
			}
			return bottomSheet
		}
		// ** NOTE ** - we don't need to handle Fi-lite locking case here, as
		// InoperableInfo has been marked deprecated on fi-lite supporting clients and is
		// replaced by RewardUnlockDetails
	default:
	}

	return nil
}

func (r *RewardService) getSkipAnimationCta(isAnimationUnskippable bool) *fePb.Reward_SkipAnimationCta {
	if isAnimationUnskippable {
		return nil
	}
	return &fePb.Reward_SkipAnimationCta{
		CtaText: &commontypes.Text{
			FontColor: "#00B899",
			BgColor:   "#282828",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Skip to reward",
			},
		},
		IconUrl: "https://epifi-icons.pointz.in/rewards/skip_animation_icon.png",
	}
}

func (r *RewardService) isTileClickable() bool {
	// method can be enriched later (again) to add checks and make the tile clickable conditionally
	return true
}

func (r *RewardService) getStatusBgColor(rewardStatus fePb.RewardStatus, beReward *beRewardsPb.Reward, userAndAppAttributes *UserAndAppAttributes) string {
	// using lowercase for rewardStatus as keys in RewardStatusToBgColorMap are in lowercase
	rewardStatusString := rewardStatus.String()
	val, ok := r.rewardsFrontendMeta.RewardStatusToBgColorMap[rewardStatusString]

	switch rewardStatus {
	case fePb.RewardStatus_LOCKED:
		// if rewards are need to be locked for min-kyc users, no need to send any tile bg color based on status
		if userAndAppAttributes != nil && userAndAppAttributes.IsLockingRewardsForMinKycUsersSupported &&
			!beReward.GetRewardOptions().GetIsUnlockedForMinKyc() && userAndAppAttributes.KycLevel == beKycPb.KYCLevel_MIN_KYC {
			return ""
		} else if ok {
			return val
		}
	default:
		if ok {
			return val
		}
	}

	return ""
}

func (r *RewardService) getTileText(rewardStatus fePb.RewardStatus, beReward *beRewardsPb.Reward, userAndAppAttributes *UserAndAppAttributes) (string, error) {
	switch rewardStatus {
	case fePb.RewardStatus_LOCKED:
		// if rewards are need to be locked for min-kyc users, no need to send any tile-text
		if userAndAppAttributes != nil && userAndAppAttributes.IsLockingRewardsForMinKycUsersSupported &&
			!beReward.GetRewardOptions().GetIsUnlockedForMinKyc() && userAndAppAttributes.KycLevel == beKycPb.KYCLevel_MIN_KYC {
			return "", nil
		}
	default:
	}

	// using lowercase for rewardStatus as keys in RewardStatusToStatusMsgTemplate are in lowercase
	rewardStatusString := rewardStatus.String()
	val, ok := r.rewardsFrontendMeta.RewardStatusToStatusMsgTemplate[rewardStatusString]
	if ok {
		switch rewardStatus {
		case fePb.RewardStatus_LOCKED:
			if beReward.GetRewardOptions().GetUnlockDate() != nil {
				unlockDate, err := ptypes.Timestamp(beReward.GetRewardOptions().GetUnlockDate())
				if err != nil {
					return "", errors.Wrap(err, "unable to convert unlock date timestamp to time")
				}
				unlockDateString := unlockDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
				return fmt.Sprintf(val, unlockDateString), nil
			}
		case fePb.RewardStatus_EXPIRED:
			// this is a temp approach and only applicable for reward type fi-coins,
			// this gives false info about points expiry as actually points may have been used before expiry by offers redemption flow
			fiCons := beReward.GetChosenReward().GetFiCoins()
			if fiCons == nil {
				return "", errors.New("no fi coins for expired reward")
			}
			expiryDate, err := ptypes.Timestamp(fiCons.GetExpiresAt())
			if err != nil {
				return "", errors.Wrap(err, "unable to convert expiry date timestamp to time")
			}
			expiryDateString := expiryDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
			return fmt.Sprintf(val, expiryDateString), nil
		case fePb.RewardStatus_DELAYED_UNLOCK_WITH_AUTOCLAIM:
			unlockDate := beReward.GetRewardOptions().GetUnlockDate().AsTime()
			unlockDateString := unlockDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
			return fmt.Sprintf(val, unlockDateString), nil
		case fePb.RewardStatus_ARRIVING:
			processingDate := beReward.GetChosenReward().GetProcessingDate().AsTime()
			processingDateString := processingDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
			return fmt.Sprintf(val, processingDateString), nil

		default:
			return val, nil
		}
	}
	return "", nil
}

// nolint:funlen
func (r *RewardService) getTileTextV1ForReward(rewardStatus fePb.RewardStatus, beReward *beRewardsPb.Reward, userAndAppAttributes *UserAndAppAttributes) (*commontypes.Text, error) {
	// using lowercase for rewardStatus as keys in RewardStatusToStatusMsgTemplate are in lowercase
	rewardStatusString := rewardStatus.String()
	val, ok := r.rewardsFrontendMeta.RewardStatusToStatusMsgTemplate[rewardStatusString]
	if !ok {
		return nil, nil
	}
	switch rewardStatus {
	case fePb.RewardStatus_LOCKED:
		// ** NOTE **: we are not adding a check for EXPLICIT_LOCKING here for backwards compatibility
		if beReward.GetRewardOptions().GetUnlockDate() != nil {
			unlockDate := beReward.GetRewardOptions().GetUnlockDate().AsTime()
			if unlockDate.After(time.Now().Add(r.dyconf.RewardsFrontendMeta().MinTimeDifferenceToShowTimeBasedLockDetails())) {
				unlockDateString := unlockDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
				return &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf(val, unlockDateString)},
					FontColor:    colorFiSnow,
					BgColor:      colorFiGreen,
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
				}, nil
			}
		}

		return nil, nil
	case fePb.RewardStatus_UNLOCKED:
		rewardExpiredAt := beReward.GetExpiresAt()
		minUntilExpiry := time.Until(rewardExpiredAt.AsTime()).Minutes()
		displayString := val
		if rewardExpiredAt != nil && minUntilExpiry >= 1 {
			displayString = fmt.Sprintf("EXPIRES IN %s", rewardsFrontendPkg.TimeUntilString(rewardExpiredAt.AsTime()))
		}
		return &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: displayString},
			FontColor:    colorFiSnow,
			BgColor:      colorFiGreen,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
			},
		}, nil
	case fePb.RewardStatus_ARRIVING:
		processingDate := beReward.GetChosenReward().GetProcessingDate().AsTime()
		processingDateString := processingDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
		return &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf(val, processingDateString)},
			FontColor:    colorFiSnow,
			BgColor:      colorGrayLead,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
			},
		}, nil
	case fePb.RewardStatus_DELAYED_UNLOCK_WITH_AUTOCLAIM:
		unlockDate := beReward.GetRewardOptions().GetUnlockDate().AsTime()
		unlockDateString := unlockDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
		return &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf(val, unlockDateString)},
			FontColor:    colorFiSnow,
			BgColor:      colorGrayLead,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
			},
		}, nil
	case fePb.RewardStatus_PROCESSING:
		return &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: val},
			FontColor:    colorFiSnow,
			BgColor:      colorGraySlate,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
			},
		}, nil
	case fePb.RewardStatus_EXPIRED:
		// Currently applicable only for Fi-coin reward
		// NOTE: this info is not accurate about points expiry as actually points may have been used before expiry by offers redemption flow
		fiCoins := beReward.GetChosenReward().GetFiCoins()
		if fiCoins == nil {
			return nil, fmt.Errorf("no fi coins for expired reward")
		}
		if fiCoins.GetExpiresAt().AsTime().After(time.Now()) {
			return nil, fmt.Errorf("reward is not expired")
		}

		expiryDateString := fiCoins.GetExpiresAt().AsTime().In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
		return &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf(val, expiryDateString)},
			FontColor:    colorFiSnow,
			BgColor:      colorGrayLead,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
			},
		}, nil
	case fePb.RewardStatus_REVERSED:
		return &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: val},
			FontColor:    colorFiSnow,
			BgColor:      colorGraySlate,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
			},
		}, nil
	default:
		return nil, nil
	}
}

func (r *RewardService) getTileTextV1ForExchangerOrder(status fePb.ExchangerOrderStatus) (*commontypes.Text, error) {
	switch status {
	case fePb.ExchangerOrderStatus_IN_PROGRESS:
		return &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "PROCESSING"},
			FontColor:    colorFiSnow,
			BgColor:      colorGraySlate,
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
			},
		}, nil
	default:
		// no handling needed for other statuses
	}

	return nil, nil
}

func (r *RewardService) getRewardTileDisplayStateForReward(status fePb.RewardStatus) fePb.RewardWrapperV1_RewardTileDisplayState {
	switch status {
	case fePb.RewardStatus_PROCESSING, fePb.RewardStatus_ARRIVING, fePb.RewardStatus_DELAYED_UNLOCK_WITH_AUTOCLAIM:
		return fePb.RewardWrapperV1_REWARD_TILE_DISPLAY_STATE_WASHED_OUT
	case fePb.RewardStatus_PROCESSING_FAILED, fePb.RewardStatus_EXPIRED, fePb.RewardStatus_REVERSED:
		return fePb.RewardWrapperV1_REWARD_TILE_DISPLAY_STATE_GREYED_OUT
	default:
		return fePb.RewardWrapperV1_REWARD_TILE_DISPLAY_STATE_UNSPECIFIED
	}
}

func (r *RewardService) getRewardTileDisplayStateForExchangerOrder(status fePb.ExchangerOrderStatus) fePb.RewardWrapperV1_RewardTileDisplayState {
	switch status {
	case fePb.ExchangerOrderStatus_IN_PROGRESS:
		return fePb.RewardWrapperV1_REWARD_TILE_DISPLAY_STATE_WASHED_OUT
	default:
		return fePb.RewardWrapperV1_REWARD_TILE_DISPLAY_STATE_UNSPECIFIED
	}
}

// nolint:funlen
func (r *RewardService) getRewardStatusDescText(rewardStatus fePb.RewardStatus, beReward *beRewardsPb.Reward) (string, error) {
	// for locked state, description is generic irrespective of reward type.
	if rewardStatus == fePb.RewardStatus_LOCKED {
		// ** NOTE **: we are not adding a check for EXPLICIT_LOCKING here for backwards compatibility
		// time based locking
		unlockDate := datetime.TimestampToTime(beReward.GetRewardOptions().GetUnlockDate())
		if unlockDate.After(time.Now().Add(r.dyconf.RewardsFrontendMeta().MinTimeDifferenceToShowTimeBasedLockDetails())) {
			statusDescExpression := "This Money-Plant will be unlocked on %s"
			// for lucky draw rewards in locked state, we need to show a slight different text. Following handling is with assumption that
			// lucky draw rewards will be single choice rewards (as per product requirement), hence checking if 1st option from the list is
			// of lucky draw type or not. For choice rewards (choice between luckydraw and some other reward type), we'll need a different handling.
			if beReward.GetRewardOptions().GetOptions()[0].GetRewardType() == beRewardsPb.RewardType_LUCKY_DRAW {
				statusDescExpression = "This lucky draw plant will be unlocked on %s"
			}

			unlockDateString := unlockDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
			return fmt.Sprintf(statusDescExpression, unlockDateString), nil
		}

		// returning empty status desc for all other cases rewards
		return "", nil
	}

	expressionKeyToValueMap := make(map[string]string)

	// status description varies based on type of reward claimed and its current status.
	rewardType := beReward.GetChosenReward().GetRewardType()
	// if reward is in DELAYED_UNLOCK_WITH_AUTOCLAIM state, the auto claim date will
	// be the same as unlock date. chosen option will be nil in this case, so reward
	// type will be reward type of the 0th option.
	if rewardStatus == fePb.RewardStatus_DELAYED_UNLOCK_WITH_AUTOCLAIM {
		unlockDate := beReward.GetRewardOptions().GetUnlockDate().AsTime()
		unlockDateString := unlockDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
		expressionKeyToValueMap["UNLOCK_DATE_KEY"] = unlockDateString

		// setting reward type as that of default option
		rewardType = beReward.GetRewardOptions().GetOptions()[0].GetRewardType()
	}

	if beReward.GetChosenReward().GetProcessingDate() != nil {
		processingDate, err := ptypes.Timestamp(beReward.GetChosenReward().GetProcessingDate())
		if err != nil {
			return "", errors.Wrap(err, "unable to convert processing date on timestamp to time")
		}
		processingDateString := processingDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
		expressionKeyToValueMap["PROCESSING_DATE_KEY"] = processingDateString
	}

	if rewardType == beRewardsPb.RewardType_FI_COINS {
		fiCoins := beReward.GetChosenReward().GetFiCoins()
		if fiCoins.GetExpiresAt() != nil {
			expiryDate, err := ptypes.Timestamp(fiCoins.GetExpiresAt())
			if err != nil {
				return "", errors.Wrap(err, "unable to convert expiry date timestamp to time")
			}
			expiryDateString := expiryDate.In(datetime.IST).Format(r.rewardsFrontendMeta.AppDisplayDateTimeFormat)
			expressionKeyToValueMap["EXPIRY_DATE_KEY"] = expiryDateString
		}
	}

	statusToStatusDescMap, ok := r.rewardsFrontendMeta.RewardTypeAndStatusToStatusDescTemplate[rewardType.String()]
	if !ok {
		return "", nil
	}
	statusDescExpression, ok := statusToStatusDescMap[rewardStatus.String()]
	if !ok {
		return "", nil
	}
	for k, v := range expressionKeyToValueMap {
		statusDescExpression = strings.ReplaceAll(statusDescExpression, k, v)
	}
	return accrual.ReplaceCoinWithPointIfApplicable(statusDescExpression), nil
}

func (r *RewardService) getStatusDescTextForExchangerOrder(status fePb.ExchangerOrderStatus, rewardType beExchangerPb.RewardType) string {
	switch status {
	case fePb.ExchangerOrderStatus_FULFILLED:
		switch rewardType {
		case beExchangerPb.RewardType_REWARD_TYPE_FI_COINS:
			// todo(divyadeep): move these to config
			return accrual.ReplaceCoinWithPointIfApplicable("This reward has been added to your Fi-Coin balance")
		case beExchangerPb.RewardType_REWARD_TYPE_CASH:
			return "This reward has been added to your savings account"
		case beExchangerPb.RewardType_REWARD_TYPE_CREDIT_CARD_BILL_ERASER:
			return "This has been added to your credit card, on your unbilled amount"
		default:
			// do nothing
		}
	case fePb.ExchangerOrderStatus_IN_PROGRESS:
		switch rewardType {
		case beExchangerPb.RewardType_REWARD_TYPE_FI_COINS:
			return "This reward will be added to your Fi-Coin balance in 24-48 hours"
		case beExchangerPb.RewardType_REWARD_TYPE_CASH:
			return "This reward will be added to your savings account in 24-48 hours"
		case beExchangerPb.RewardType_REWARD_TYPE_CREDIT_CARD_BILL_ERASER:
			return "This will be added to your credit card limit on your unbilled amount in 24-48 hours "
		default:
			// do nothing
		}
	}
	return ""
}

// returns background image for reward tile.
// reward states PROCESSING, ARRIVING, PROCESSED, EXPIRED implies that reward was successfully claimed so after claim image is returned.
// for other reward states before claim image is returned.
func (r *RewardService) getRewardTileBgImage(rewardStatus fePb.RewardStatus, beReward *beRewardsPb.Reward) string {
	switch rewardStatus {
	case fePb.RewardStatus_PROCESSING, fePb.RewardStatus_ARRIVING, fePb.RewardStatus_PROCESSED, fePb.RewardStatus_EXPIRED:
		return beReward.GetRewardDisplay().GetTileBgImageAfterClaim()
	default:
		return beReward.GetRewardDisplay().GetTileBgImageBeforeClaim()
	}
}

func (r *RewardService) getFeRewardTileThemeType(beRewardTileThemeType beRewardsPb.RewardTileThemeType) fePb.RewardTileThemeType {
	switch beRewardTileThemeType {
	case beRewardsPb.RewardTileThemeType_PLANT_THEME_CHRYSANTHEMUM:
		return fePb.RewardTileThemeType_PLANT_THEME_CHRYSANTHEMUM
	case beRewardsPb.RewardTileThemeType_PLANT_THEME_ORCHID:
		return fePb.RewardTileThemeType_PLANT_THEME_ORCHID
	default:
		return fePb.RewardTileThemeType_REWARD_TILE_THEME_UNSPECIFIED
	}
}

func (r *RewardService) getRewardStatus(ctx context.Context, beReward *beRewardsPb.Reward, userAndAppAttributes *UserAndAppAttributes, appVersion uint32, appPlatform commontypes.Platform) (fePb.RewardStatus, error) {
	const maxTimeDeltaBetweenUnlockAndAutoClaimTimeForArrivingState = time.Minute * 2

	switch beReward.GetStatus() {
	case beRewardsPb.RewardStatus_LOCKED:
		return fePb.RewardStatus_LOCKED, nil
	case beRewardsPb.RewardStatus_CREATED:
		// prioritise min-kyc check for locking of rewards (for backwards compatibility)
		// todo(divyadeep): evaluate if we need to check for IsImplicitLockingDisabled flag (part of reward offer) here (only required until LOCKED rewards start getting generated on prod)
		if userAndAppAttributes != nil && userAndAppAttributes.IsLockingRewardsForMinKycUsersSupported &&
			!beReward.GetRewardOptions().GetIsUnlockedForMinKyc() && userAndAppAttributes.KycLevel == beKycPb.KYCLevel_MIN_KYC &&
			!beReward.GetRewardOptions().GetIsImplicitLockingDisabled() {
			return fePb.RewardStatus_LOCKED, nil
		}

		if beReward.GetRewardOptions().GetUnlockDate() != nil {
			unlockDate := beReward.GetRewardOptions().GetUnlockDate().AsTime()
			autoClaimTime := beReward.GetRewardOptions().GetAutoClaimTime().AsTime()

			// if time delta between unlock and auto claim time is small, the state of the reward is ARRIVING.
			if beReward.GetRewardOptions().GetAutoClaimTime() != nil &&
				datetime.GetAbsoluteTimeDifference(autoClaimTime, unlockDate) < maxTimeDeltaBetweenUnlockAndAutoClaimTimeForArrivingState &&
				time.Now().Before(autoClaimTime) {
				if appPlatform == commontypes.Platform_IOS && appVersion >= r.dyconf.RewardsFrontendMeta().MinIosAppVersionSupportingYourNewRewardTiles() ||
					appPlatform == commontypes.Platform_ANDROID && appVersion >= r.dyconf.RewardsFrontendMeta().MinAndroidAppVersionSupportingNewRewardTiles() {
					return fePb.RewardStatus_DELAYED_UNLOCK_WITH_AUTOCLAIM, nil
				} else {
					return fePb.RewardStatus_LOCKED, nil
				}
			}
			// if unlock time is of future we'll show locked state
			if time.Now().Before(unlockDate) {
				return fePb.RewardStatus_LOCKED, nil
			}
		}
		// return UNLOCKED in all other cases
		return fePb.RewardStatus_UNLOCKED, nil
	case beRewardsPb.RewardStatus_PROCESSED:
		// custom reward status handling for fi coin reward
		if beReward.GetChosenReward().GetFiCoins() != nil {
			fiCons := beReward.GetChosenReward().GetFiCoins()
			expiryDate, err := ptypes.Timestamp(fiCons.GetExpiresAt())
			if err != nil {
				return fePb.RewardStatus_UNSPECIFIED_REWARD_STATUS, errors.Wrap(err, "unable to convert expiry date timestamp to time")
			}
			if expiryDate.Before(time.Now()) {
				return fePb.RewardStatus_EXPIRED, nil
			} else {
				return fePb.RewardStatus_PROCESSED, nil
			}
		}
		// If LuckyDraw reward option was chosen, then return reward status based on LuckyDraw/Winning processing status.
		if beReward.GetChosenReward().GetLuckyDraw() != nil {
			return r.getFeRewardStatusFromChosenLuckyDrawOption(ctx, beReward)
		}
		return fePb.RewardStatus_PROCESSED, nil

	case beRewardsPb.RewardStatus_PROCESSING_FAILED:
		return fePb.RewardStatus_PROCESSING_FAILED, nil

	case beRewardsPb.RewardStatus_PROCESSING_MANUAL_INTERVENTION:
		return fePb.RewardStatus_PROCESSING_FAILED, nil

	case beRewardsPb.RewardStatus_PROCESSING_PENDING:
		processingDate, err := ptypes.Timestamp(beReward.GetChosenReward().GetProcessingDate())
		if err != nil {
			return fePb.RewardStatus_UNSPECIFIED_REWARD_STATUS, errors.Wrap(err, "unable to convert processing date on timestamp to time")
		}
		if processingDate.Before(time.Now()) {
			return fePb.RewardStatus_PROCESSING, nil
		} else {
			return fePb.RewardStatus_ARRIVING, nil
		}

	case beRewardsPb.RewardStatus_PROCESSING_IN_PROGRESS:
		return fePb.RewardStatus_PROCESSING, nil
	default:
		return fePb.RewardStatus_UNSPECIFIED_REWARD_STATUS, nil
	}
}

// nolint:funlen,dupl
func (r *RewardService) getRewardUnlockDisplayDetails(ctx context.Context, userAndAppAttributes *UserAndAppAttributes, feRewardStatus fePb.RewardStatus, beReward *beRewardsPb.Reward, deeplinksMap map[deeplinkName]*deepLinkPb.Deeplink) (*fePb.RewardUnlockDetails, error) {
	var (
		rewardSubStatus = beReward.GetSubStatusV2()
	)
	if feRewardStatus != fePb.RewardStatus_LOCKED {
		return nil, nil
	}

	// CTA for locking rewards generated in LOCKED state (BE state), or old rewards in CREATED state
	// ** NOTE **: only user state based locking (MinKYC, FiLite) will be applicable when reward is in CREATED state.
	// todo: discuss if we need to check isImplicitLockingDisabled with created status
	if rewardSubStatus == beRewardsPb.SubStatus_SUB_STATUS_IMPLICITLY_LOCKED || beReward.Status == beRewardsPb.RewardStatus_CREATED {
		if userAndAppAttributes.SavingsAccountStatus != savingsPb.State_CREATED {
			ctas := []*fePb.CtaV1{
				{
					CtaTitle: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Go back"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_M,
						},
						FontColor: colorFiGreen,
					},
					BgColor:   colorIvory,
					BgColorV2: widget.GetBlockBackgroundColour(colorIvory),
					Action: &fePb.CtaV1_CustomAction{
						CustomAction: &fePb.CustomAction{
							ActionType: fePb.CustomAction_GO_BACK_TO_PREVIOUS_SCREEN,
						},
					},
				},
			}

			if deeplinksMap[openSavingsAccountDeeplink] != nil {
				ctas = append(ctas, &fePb.CtaV1{
					CtaTitle: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Continue"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_M,
						},
						FontColor: colorFiSnow,
					},
					BgColor:   colorFiGreen,
					BgColorV2: widget.GetBlockBackgroundColour(colorFiGreen),
					Action: &fePb.CtaV1_DeeplinkAction{
						DeeplinkAction: deeplinksMap[openSavingsAccountDeeplink],
					},
				})
			}

			return &fePb.RewardUnlockDetails{
				Icon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.pointz.in/rewards/reward_unlock_action_open_sa.png",
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  86,
								Height: 86,
							},
						},
					},
				},
				Heading: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Open a Savings Account\nto unlock this reward"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
					},
					FontColor: colorGrayNight,
				},
				Desc: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "It takes only 5 min & is 100% digital."},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
					},
					FontColor: colorGrayLead,
				},
				Ctas: ctas,
			}, nil
		}
		if userAndAppAttributes.KycLevel != beKycPb.KYCLevel_FULL_KYC {
			ctas := []*fePb.CtaV1{
				{
					CtaTitle: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Go back"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_M,
						},
						FontColor: colorFiGreen,
					},
					BgColor:   colorIvory,
					BgColorV2: widget.GetBlockBackgroundColour(colorIvory),
					Action: &fePb.CtaV1_CustomAction{
						CustomAction: &fePb.CustomAction{
							ActionType: fePb.CustomAction_GO_BACK_TO_PREVIOUS_SCREEN,
						},
					},
				},
			}

			if deeplinksMap[minKycToFullKycConversionDeeplink] != nil {
				ctas = append(ctas, &fePb.CtaV1{
					CtaTitle: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Continue"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_M,
						},
						FontColor: colorFiSnow,
					},
					BgColor:   colorFiGreen,
					BgColorV2: widget.GetBlockBackgroundColour(colorFiGreen),
					Action: &fePb.CtaV1_DeeplinkAction{
						DeeplinkAction: deeplinksMap[minKycToFullKycConversionDeeplink],
					},
				})
			}

			return &fePb.RewardUnlockDetails{
				Icon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.pointz.in/rewards/reward_unlock_action_complete_kyc.png",
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  86,
								Height: 86,
							},
						},
					},
				},
				Heading: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Complete 2-min KYC call \nto unlock the reward"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
					},
					FontColor: colorGrayNight,
				},
				Desc: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "It’s a quick call to verify your identity"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
					},
					FontColor: colorGrayLead,
				},
				Ctas: ctas,
			}, nil
		}
		return nil, fmt.Errorf("unhandled case encountered while populating unlock CTA")
	}

	// when reward is explicitly locked, we need to fetch offer details to get the unlocking CTA
	rewardOffersRes, err := r.rewardOffersClient.GetRewardOffersByIds(ctx, &beRewardOffersPb.GetRewardOffersByIdsRequest{Ids: []string{beReward.GetOfferId()}})
	if rpcErr := epifigrpc.RPCError(rewardOffersRes, err); rpcErr != nil {
		return nil, fmt.Errorf("error while calling GetRewardOffersByIds, err: %w", rpcErr)
	}

	if len(rewardOffersRes.GetRewardOffers()) == 0 {
		return nil, fmt.Errorf("no reward offer found for given ID")
	}
	unlockMeta := rewardOffersRes.GetRewardOffers()[0].GetUnlockMeta()

	// we won't show any CTA if it's not configured as part of the reward offer.
	// this will be true for cases in which rewards will unlock on a future date and aren't locked on an event.
	if unlockMeta.GetCta().GetDeeplink().GetScreen() != deepLinkPb.Screen_DEEP_LINK_URI_UNSPECIFIED &&
		unlockMeta.GetCta().GetIconUrl() != "" &&
		unlockMeta.GetCta().GetName() != "" &&
		unlockMeta.GetCta().GetDesc() != "" {
		return &fePb.RewardUnlockDetails{
			Icon: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: unlockMeta.GetCta().GetIconUrl(),
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  86,
							Height: 86,
						},
					},
				},
			},
			Heading: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: unlockMeta.GetCta().GetName()},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
				},
				FontColor: colorGrayNight,
			},
			Desc: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: unlockMeta.GetCta().GetDesc()},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
				},
				FontColor: colorGrayLead,
			},
			Ctas: []*fePb.CtaV1{
				{
					CtaTitle: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Go back"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_M,
						},
						FontColor: colorFiGreen,
					},
					BgColor: colorIvory,
					Action: &fePb.CtaV1_CustomAction{
						CustomAction: &fePb.CustomAction{
							ActionType: fePb.CustomAction_GO_BACK_TO_PREVIOUS_SCREEN,
						},
					},
				},
				{
					CtaTitle: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Continue"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BUTTON_M,
						},
						FontColor: colorFiSnow,
					},
					BgColor: colorFiGreen,
					Action: &fePb.CtaV1_DeeplinkAction{
						DeeplinkAction: unlockMeta.GetCta().GetDeeplink(),
					},
				},
			},
		}, nil
	}

	logger.WarnWithCtx(ctx, "no unlock CTA details present for offer for reward in locked state", zap.String(logger.REWARD_OFFER_ID, beReward.GetOfferId()))
	return nil, nil
}

// getFeRewardStatusFromChosenLuckyDrawOption returns the reward status based on the LuckyDraw/Winning status.
// Following cases are handled ->
//  1. If user is not registered in LuckyDraw yet, return 'LOCKED' status to keep the reward tile locked.
//  2. If user is registered in LuckyDraw but LuckyDraw is not revealed yet, return 'LOCKED' status to keep the reward tile locked.
//  3. If user is registered in LuckyDraw is revealed, return reward status based on Winning status.
//     3.1 If winning is not claimed yet, return 'UNLOCKED' status.
//     3.2 If winning processing is IN_PROGRESS, return 'PROCESSING' status.
//     3.3 If winning processing FAILED, return 'FAILED' status.
//     3.4 If winning processing is PROCESSED, return 'PROCESSED' status.
func (r *RewardService) getFeRewardStatusFromChosenLuckyDrawOption(ctx context.Context, beReward *beRewardsPb.Reward) (fePb.RewardStatus, error) {
	luckyDrawReward := beReward.GetChosenReward().GetLuckyDraw()
	if luckyDrawReward == nil {
		return fePb.RewardStatus_UNSPECIFIED_REWARD_STATUS, errors.New("cannot get LuckyDraw reward status, LuckyDraw reward option isn't chosen")
	}
	// If user is not registered in LuckyDraw yet, return 'LOCKED' status to keep the reward tile locked.
	isActorRegisteredForLuckyDraw, err := r.isActorRegisteredForLuckyDraw(ctx, beReward.GetActorId(), luckyDrawReward.GetLuckyDrawId())
	if err != nil {
		return fePb.RewardStatus_UNSPECIFIED_REWARD_STATUS, err
	}
	if !isActorRegisteredForLuckyDraw {
		return fePb.RewardStatus_LOCKED, nil
	}
	// If user is registered in LuckyDraw but LuckyDraw is not revealed yet, return 'LOCKED' status to keep the reward tile locked.
	isLuckyDrawRevealed, err := r.isLuckyDrawRevealed(ctx, luckyDrawReward.GetLuckyDrawId())
	if err != nil {
		return fePb.RewardStatus_UNSPECIFIED_REWARD_STATUS, err
	}
	if !isLuckyDrawRevealed {
		return fePb.RewardStatus_LOCKED, nil
	}

	// Since user has registered for LuckyDraw and LuckyDraw is also revealed, return status based on Winning status.
	winning, err := r.getWinningByLuckyDrawAndActorId(ctx, luckyDrawReward.GetLuckyDrawId(), beReward.GetActorId())
	if err != nil {
		return fePb.RewardStatus_UNSPECIFIED_REWARD_STATUS, err
	}

	// get fe reward status from LuckyDraw Winning reward status
	switch winning.GetRewardStatus() {
	case luckydrawPb.RewardStatus_CREATED:
		return fePb.RewardStatus_UNLOCKED, nil

	case luckydrawPb.RewardStatus_PROCESSING_PENDING, luckydrawPb.RewardStatus_PROCESSING_IN_PROGRESS:
		return fePb.RewardStatus_PROCESSING, nil

	case luckydrawPb.RewardStatus_PROCESSING_FAILED:
		return fePb.RewardStatus_PROCESSING_FAILED, nil

	case luckydrawPb.RewardStatus_PROCESSING_MANUAL_INTERVENTION:
		return fePb.RewardStatus_PROCESSING_FAILED, nil

	case luckydrawPb.RewardStatus_PROCESSED:
		return fePb.RewardStatus_PROCESSED, nil
	default:
		return fePb.RewardStatus_UNSPECIFIED_REWARD_STATUS, nil
	}
}

func (r *RewardService) getFeRewardOptions(ctx context.Context, actorId string, beReward *beRewardsPb.Reward, appPlatform commontypes.Platform, appVersion uint32, userAndAppAttributes *UserAndAppAttributes) (*fePb.RewardOptions, error) {
	beRewardOptions := beReward.GetRewardOptions()
	if beRewardOptions == nil {
		return nil, nil
	}
	res := &fePb.RewardOptions{
		DefaultDecideTimeInSecs: beRewardOptions.GetDefaultDecideTimeInSecs(),
		UnlockDate:              beRewardOptions.GetUnlockDate(),
		ActionDetails:           beRewardOptions.GetActionDetails(),
	}

	var (
		allRewardOptions []*fePb.RewardOption
		currentTime      = time.Now()
	)
	for _, beRewardOption := range beRewardOptions.GetOptions() {
		if isRewardOptionExpired(currentTime, beRewardOption) {
			continue
		}
		allRewardOptions = append(allRewardOptions, r.getFeRewardOption(ctx, actorId, beRewardOption, appPlatform, appVersion, userAndAppAttributes))
	}

	// If LuckyDraw option is chosen, then ONLY show the LuckyDraw/Winning option, so that user can claim the Winning (if not already done)
	// by choosing the LuckyDraw option again. Until LuckyDraw is revealed, display LuckyDraw, after that display Winning in reward option.
	if beReward.GetChosenReward().GetLuckyDraw() != nil {
		rewardOption, err := r.getFeRewardOptionForLuckyDrawReward(context.Background(), beReward.GetChosenReward(), beReward.GetActorId())
		if err != nil {
			return nil, err
		}
		allRewardOptions = []*fePb.RewardOption{rewardOption}
	}
	res.Options = allRewardOptions

	return res, nil
}

// isRewardOptionExpired checks if the reward option is expired or not.
// Currently supporting FI_COINS expiry check only.
func isRewardOptionExpired(currentTime time.Time, beRewardOption *beRewardsPb.RewardOption) bool {
	switch {
	case beRewardOption.GetRewardType() == beRewardsPb.RewardType_FI_COINS && beRewardOption.GetFiCoins().GetExpiresAt().AsTime().Before(currentTime):
		// if fi-coins expiry is in the past, we'll consider the option as expired.
		return true
	default:
		return false
	}
}

func (r *RewardService) getFeRewardOption(ctx context.Context, actorId string, beRewardOption *beRewardsPb.RewardOption, appPlatform commontypes.Platform, appVersion uint32, userAndAppAttributes *UserAndAppAttributes) *fePb.RewardOption {
	if beRewardOption == nil {
		return nil
	}
	res := &fePb.RewardOption{
		Id:                         beRewardOption.GetId(),
		DisplayDetails:             r.getFeRewardOptionDisplayDetails(beRewardOption.GetDisplay(), false),
		ProcessingDate:             beRewardOption.GetProcessingDate(),
		RewardUnitsCalculationInfo: r.getFeRewardUnitsCalculationInfo(ctx, actorId, beRewardOption.GetRewardUnitsCalculationInfo(), beRewardOption.GetDisplay(), appPlatform, appVersion, userAndAppAttributes),
	}
	if beRewardOption.GetCash() != nil {
		res.Option = r.getFeRewardOptionCash(beRewardOption.GetCash())
	}
	if beRewardOption.GetFiCoins() != nil {
		if cfg.IsNonProdEnv(r.dyconf.Application().Environment) {
			value := strconv.FormatInt(int64(beRewardOption.GetFiCoins().GetUnits()), 10)
			res.OptionText = &fePb.RewardOptionText{
				RewardValue:       commontypes.GetTextFromStringFontColourFontStyle(value, colorFiSnow, commontypes.FontStyle_DISPLAY_L),
				RewardDescription: commontypes.GetTextFromStringFontColourFontStyle(beRewardOption.GetDisplay().GetBeforeClaimTitle(), "#A4A4A4", commontypes.FontStyle_BODY_4),
				PostOptionSelectionLoader: &fePb.RewardOptionText_PostOptionSelectionLoader{
					Icon:                 commontypes.GetVisualElementFromUrlHeightAndWidth(res.GetDisplayDetails().GetIcon(), 60, 60),
					Title:                commontypes.GetTextFromStringFontColourFontStyle(value, colorFiSnow, commontypes.FontStyle_DISPLAY_L),
					Subtitle:             commontypes.GetTextFromStringFontColourFontStyle(beRewardOption.GetDisplay().GetAfterClaimTitle(), "#A4A4A4", commontypes.FontStyle_BODY_4),
					BottomBannerText:     commontypes.GetTextFromStringFontColourFontStyle("Processing", colorFiSnow, commontypes.FontStyle_SUBTITLE_S),
					BottomBannerBgColour: widget.GetBlockBackgroundColour("#3E3E3E"),
				},
			}
		} else {
			res.Option = r.getFeRewardOptionFiCoins(beRewardOption.GetFiCoins())
		}
	}
	if beRewardOption.GetSmartDeposit() != nil {
		res.Option = r.getFeRewardOptionSmartDeposit(beRewardOption.GetSmartDeposit())
	}
	if beRewardOption.GetNoReward() != nil {
		res.Option = r.getFeRewardOptionNoReward(beRewardOption.GetNoReward())
	}
	if beRewardOption.GetGiftHamper() != nil {
		res.Option = r.getFeRewardOptionGiftHamper(beRewardOption.GetGiftHamper())
		// add shipping address in formatted description.
		shippingAddress := beRewardOption.GetGiftHamper().GetShippingAddress()
		if res.GetDisplayDetails() != nil && shippingAddress != nil {
			res.GetDisplayDetails().HtmlFormattedDetails = append(res.GetDisplayDetails().HtmlFormattedDetails, &fePb.RewardOption_DisplayDetails_HtmlFormattedDetail{
				Header: deliveryAddressHeader,
				Body:   address.ConvertPostalAddressToString(shippingAddress),
			})
		}
	}

	// for lucky draw as reward option we display the lucky draw details and
	// for lucky draw as chosen option,we display the details of actual reward won from lucky draw
	if beRewardOption.GetLuckyDraw() != nil {
		res.Option = r.getFeRewardOptionLuckyDraw(beRewardOption.GetLuckyDraw())
	}
	return res
}

// getFeRewardUnitsCalculationInfo generates the ledger of reward calculation entries to be displayed at the client
func (r *RewardService) getFeRewardUnitsCalculationInfo(
	ctx context.Context, actorId string,
	beRewardUnitsCalcInfo *beRewardsPb.RewardUnitsCalculationInfo,
	beRewardOptionDisplayDetails *beRewardsPb.RewardOptionDisplay,
	appPlatform commontypes.Platform, appVersion uint32, userAndAppAttributes *UserAndAppAttributes,
) *fePb.RewardUnitsCalculationInfo {
	if userAndAppAttributes != nil {
		if !r.checkIfClientSupportsBoosterFields(ctx, actorId, false, userAndAppAttributes.IsTieringEnabled, userAndAppAttributes.AccountTier, appPlatform, appVersion) {
			return nil
		}
	} else {
		// if tiering related info is not available, we'd force check them in the evaluator method
		if !r.checkIfClientSupportsBoosterFields(ctx, actorId, true, false, tieringExtPb.Tier_TIER_UNSPECIFIED, appPlatform, appVersion) {
			return nil
		}
	}
	if beRewardUnitsCalcInfo == nil || len(beRewardUnitsCalcInfo.GetRewardUnitsCalculationEntries()) < 2 {
		return nil
	}

	firstBeEntry := beRewardUnitsCalcInfo.GetRewardUnitsCalculationEntries()[0]

	feRewardUnitsCalcInfo := &fePb.RewardUnitsCalculationInfo{
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Reward boosters added"},
			FontColor:    "#FFFFFF",
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
			},
		},
		RewardUnitsCalculationEntries: []*fePb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{
			{
				Units: uint32(firstBeEntry.GetRewardValue()),
				Delta: 0,
			},
		},
	}

	var (
		baseRewardUnits     = firstBeEntry.GetRewardValue()
		previousRewardUnits = firstBeEntry.GetRewardValue()
	)

	// iterating the entries excluding the first
	for _, beEntry := range beRewardUnitsCalcInfo.GetRewardUnitsCalculationEntries()[1:] {
		feEntry := &fePb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry{
			Delta: uint32(beEntry.GetRewardValue()) - uint32(previousRewardUnits),
			DisplayDetails: &fePb.RewardUnitsCalculationInfo_RewardUnitsCalculationEntry_DisplayDetails{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: beEntry.GetDisplayDetails().GetTitle()},
					FontColor:    beEntry.GetDisplayDetails().GetTitleColor(),
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
				BgColor:    beEntry.GetDisplayDetails().GetBgColor(),
				UnitsColor: beEntry.GetDisplayDetails().GetTitleColor(),
				DeltaColor: beEntry.GetDisplayDetails().GetTitleColor(),
				IconUrl:    beRewardOptionDisplayDetails.GetIcon(),
			},
		}

		switch beRewardUnitsCalcInfo.GetClaimFlowRewardValueRevealType() {
		// the value of units will be the intermediate (final reward units till that particular entry)
		case beRewardsPb.ClaimFlowRewardValueRevealType_CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_INTERMEDIATE_VALUES, beRewardsPb.ClaimFlowRewardValueRevealType_CLAIM_FLOW_REVEAL_TYPE_UNSPECIFIED:
			feEntry.Units = uint32(beEntry.GetRewardValue())
		// the value of units will be the base value always. Only the delta will carry the diff
		case beRewardsPb.ClaimFlowRewardValueRevealType_CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_BASE_VALUE:
			feEntry.Units = uint32(baseRewardUnits)
		default:
			logger.Error(ctx, "unsupported ClaimFlowRewardValueRevealType. Ignoring silently and not returning any calculations info",
				zap.String("revealType", beRewardUnitsCalcInfo.GetClaimFlowRewardValueRevealType().String()),
			)
			return nil
		}

		feRewardUnitsCalcInfo.RewardUnitsCalculationEntries = append(feRewardUnitsCalcInfo.RewardUnitsCalculationEntries, feEntry)
		previousRewardUnits = beEntry.GetRewardValue()
	}

	return feRewardUnitsCalcInfo
}

func (r *RewardService) convertWinningToFeRewardOption(luckyDrawRewardOption *beRewardsPb.RewardOption, winning *luckydrawPb.LuckyDrawWinning) *fePb.RewardOption {
	rewardOption := &fePb.RewardOption{
		Id:             luckyDrawRewardOption.GetId(),
		DisplayDetails: r.getFeRewardOptionDisplayDetails(winning.GetReward().GetDisplay(), false),
		ProcessingDate: luckyDrawRewardOption.GetProcessingDate(),
	}
	// todo (utkarsh) : maintain beChosenOption -> feRewardOption and winning->feRewardOption mapping logic in a single place/method?
	// update reward option with reward type specific details.
	switch winning.GetRewardType() {
	// if winning contains ficoins reward, create a ficoins reward option.
	case beRewardsPb.RewardType_FI_COINS:
		rewardOption.Option = r.getFeRewardOptionFiCoins(winning.GetReward().GetFiCoins())
	// if winning contains cash reward, create a cash reward option.
	case beRewardsPb.RewardType_CASH:
		rewardOption.Option = r.getFeRewardOptionCash(winning.GetReward().GetCash())
	// if winning contains smart deposit reward, create a smart deposit reward option.
	case beRewardsPb.RewardType_SMART_DEPOSIT:
		rewardOption.Option = r.getFeRewardOptionSmartDeposit(winning.GetReward().GetSmartDeposit())
	// if winning contains gift hamper reward, create a gift hamper reward option.
	case beRewardsPb.RewardType_GIFT_HAMPER:
		rewardOption.Option = r.getFeRewardOptionGiftHamper(winning.GetReward().GetGiftHamper())
		// for gift hamper rewards, add shipping address in html formatted description for display on reward details page.
		shippingAddress := winning.GetReward().GetGiftHamper().GetShippingAddress()
		if rewardOption.GetDisplayDetails() != nil && shippingAddress != nil {
			rewardOption.GetDisplayDetails().HtmlFormattedDetails = append(rewardOption.GetDisplayDetails().HtmlFormattedDetails, &fePb.RewardOption_DisplayDetails_HtmlFormattedDetail{
				Header: deliveryAddressHeader,
				Body:   address.ConvertPostalAddressToString(shippingAddress),
			})
		}
	// if winning does not contain a reward, create a no reward type reward option.
	case beRewardsPb.RewardType_NO_REWARD:
		rewardOption.Option = r.getFeRewardOptionNoReward(winning.GetReward().GetNoReward())
		// default block added to avoid lint errors
	default:
	}
	return rewardOption
}

// nolint:funlen
func (r *RewardService) getFeChosenRewardOption(ctx context.Context, beChosenRewardOption *beRewardsPb.RewardOption, beReward *beRewardsPb.Reward) (*fePb.RewardOption, error) {
	// if no option was chosen, return nil
	if beChosenRewardOption == nil {
		return nil, nil
	}
	// setting ProcessingDate as created at date as we use this to show 'Added at' in the money plant
	res := &fePb.RewardOption{
		DisplayDetails: r.getFeRewardOptionDisplayDetails(beChosenRewardOption.GetDisplay(), true),
		ProcessingDate: beReward.GetCreatedAt(),
	}

	// For lucky draw chosen option, we should display the winning as the chosen option.
	// So until the lucky draw is not revealed or winning is not claimed, chosen option would be nil.
	if beChosenRewardOption.GetLuckyDraw() != nil {
		return r.getFeChosenRewardOptionForLuckyDrawReward(ctx, beReward)
	}
	// for cash, ficoins, sd, gifthamper reward the handling of chosen reward option
	// would be exactly same as a non chosen reward option.
	if beChosenRewardOption.GetCash() != nil {
		res.Option = r.getFeRewardOptionCash(beChosenRewardOption.GetCash())
	}
	if beChosenRewardOption.GetCreditCardBillEraser() != nil {
		res.Option = r.getFeRewardOptionCreditCardBillEraser(beChosenRewardOption.GetCreditCardBillEraser())
	}
	if beChosenRewardOption.GetFiCoins() != nil {
		res.Option = r.getFeRewardOptionFiCoins(beChosenRewardOption.GetFiCoins())
	}

	if beChosenRewardOption.GetSmartDeposit() != nil {
		res.Option = r.getFeRewardOptionSmartDeposit(beChosenRewardOption.GetSmartDeposit())
	}
	if beChosenRewardOption.GetNoReward() != nil {
		res.Option = r.getFeRewardOptionNoReward(beChosenRewardOption.GetNoReward())
	}
	if beChosenRewardOption.GetGiftHamper() != nil {
		res.Option = r.getFeRewardOptionGiftHamper(beChosenRewardOption.GetGiftHamper())
		// add shipping address in html formatted description for display on reward details page.
		shippingAddress := beChosenRewardOption.GetGiftHamper().GetShippingAddress()
		if res.GetDisplayDetails() != nil && shippingAddress != nil {
			res.GetDisplayDetails().HtmlFormattedDetails = append(res.GetDisplayDetails().HtmlFormattedDetails, &fePb.RewardOption_DisplayDetails_HtmlFormattedDetail{
				Header: deliveryAddressHeader,
				Body:   address.ConvertPostalAddressToString(shippingAddress),
			})
		}
	}

	return res, nil
}

// For lucky draw chosen option, claimed winning is treated as chosen reward option, So ->
// 1. If lucky draw is not revealed yet OR winning is not claimed yet -> return chosen option as nil.
// 2. If lucky draw is revealed and winning is claimed -> return winning details in chosen reward option.
func (r *RewardService) getFeChosenRewardOptionForLuckyDrawReward(ctx context.Context, beReward *beRewardsPb.Reward) (*fePb.RewardOption, error) {
	luckyDrawOption := beReward.GetChosenReward().GetLuckyDraw()
	if luckyDrawOption == nil {
		return nil, errors.New("lucky draw reward option is not chosen")
	}
	// check if lucky draw is revealed or not.
	isLuckyDrawRevealed, err := r.isLuckyDrawRevealed(ctx, luckyDrawOption.GetLuckyDrawId())
	if err != nil {
		return nil, err
	}
	// if not revealed, return chosen option as nil
	if !isLuckyDrawRevealed {
		return nil, nil
	}

	// get lucky draw winning
	winning, err := r.getWinningByLuckyDrawAndActorId(ctx, luckyDrawOption.GetLuckyDrawId(), beReward.GetActorId())
	if err != nil {
		return nil, err
	}
	// if winning is not claimed yet, return nil chosen option
	if winning.GetRewardStatus() == luckydrawPb.RewardStatus_CREATED {
		return nil, nil
	}

	// convert winning to reward option
	return r.convertWinningToFeRewardOption(beReward.GetChosenReward(), winning), nil
}

// For LuckyDraw option in all reward options ->
//  1. If user is not registered for given LuckyDraw OR the LuckyDraws not revealed yet  -> display LuckyDraw details in reward option.
//  2. If user was registered for LuckyDraw AND LuckyDraw was revealed -> display LuckyDraw winning details in reward option.
func (r *RewardService) getFeRewardOptionForLuckyDrawReward(ctx context.Context, beRewardOption *beRewardsPb.RewardOption, actorId string) (*fePb.RewardOption, error) {
	luckyDrawOption := beRewardOption.GetLuckyDraw()
	if luckyDrawOption == nil {
		return nil, errors.New("option is not of lucky draw type")
	}

	isActorRegisteredForLuckyDraw, err := r.isActorRegisteredForLuckyDraw(ctx, actorId, luckyDrawOption.GetLuckyDrawId())
	if err != nil {
		return nil, err
	}

	// if actor is not registered in LuckyDraw, display LuckyDraw details in reward option
	if !isActorRegisteredForLuckyDraw {
		return &fePb.RewardOption{
			DisplayDetails: r.getFeRewardOptionDisplayDetails(beRewardOption.GetDisplay(), false),
			Option:         r.getFeRewardOptionLuckyDraw(luckyDrawOption),
			ProcessingDate: beRewardOption.ProcessingDate,
			Id:             beRewardOption.GetId(),
		}, nil
	}

	isLuckyDrawRevealed, err := r.isLuckyDrawRevealed(ctx, luckyDrawOption.GetLuckyDrawId())
	if err != nil {
		return nil, err
	}
	// if LuckyDraw is not revealed yet, display LuckyDraw details in reward option
	if !isLuckyDrawRevealed {
		return &fePb.RewardOption{
			DisplayDetails: r.getFeRewardOptionDisplayDetails(beRewardOption.GetDisplay(), false),
			Option:         r.getFeRewardOptionLuckyDraw(luckyDrawOption),
			ProcessingDate: beRewardOption.ProcessingDate,
			Id:             beRewardOption.GetId(),
		}, nil
	}
	winning, err := r.getWinningByLuckyDrawAndActorId(ctx, luckyDrawOption.GetLuckyDrawId(), actorId)
	if err != nil {
		return nil, err
	}

	// display LuckyDraw winning in reward option
	return r.convertWinningToFeRewardOption(beRewardOption, winning), nil
}

func (r *RewardService) getFeRewardOptionDisplayDetails(beRewardOptionDisplayDetails *beRewardsPb.RewardOptionDisplay, isOptionChosen bool) *fePb.RewardOption_DisplayDetails {
	if beRewardOptionDisplayDetails == nil {
		return nil
	}

	desc := beRewardOptionDisplayDetails.GetBeforeClaimTitle()
	bannerText := beRewardOptionDisplayDetails.GetBeforeClaimBannerText()

	if isOptionChosen {
		// reward option description depends on whether that option is already chosen or not.
		// For eg : Before claim we may want to display "100 FiCoins After 7 days", after claim we just want
		// to display "100 FiCoins" in description.
		desc = beRewardOptionDisplayDetails.GetAfterClaimTitle()
		// currently banner text is used only for before claim flow.
		bannerText = ""
	}

	// todo (utkarsh) : remove it after we completely migrate to before_claim_title/after_claim_title
	if desc == "" {
		desc = beRewardOptionDisplayDetails.GetTitle()
	}

	var htmlFormattedDetails []*fePb.RewardOption_DisplayDetails_HtmlFormattedDetail
	for _, htmlFormattedDetail := range beRewardOptionDisplayDetails.GetHtmlFormattedDetails() {
		htmlFormattedDetails = append(htmlFormattedDetails, &fePb.RewardOption_DisplayDetails_HtmlFormattedDetail{
			Header: htmlFormattedDetail.GetHeader(),
			Body:   htmlFormattedDetail.GetBody(),
		})
	}

	return &fePb.RewardOption_DisplayDetails{
		Desc:                 desc,
		BannerText:           bannerText,
		Icon:                 beRewardOptionDisplayDetails.GetIcon(),
		BgColor:              beRewardOptionDisplayDetails.GetBgColor(),
		HtmlFormattedDetails: htmlFormattedDetails,
	}
}

func (r *RewardService) getFeRewardOptionCash(cash *beRewardsPb.Cash) *fePb.RewardOption_Cash {
	if cash == nil {
		return nil
	}
	return &fePb.RewardOption_Cash{
		Cash: &fePb.Cash{Amount: types.GetFromBeMoney(cash.GetAmount())},
	}
}

// using cash option of reward here, as adding a new fe reward option will require client to add handling for it
// todo(sresth): use a generic reward type for this
func (r *RewardService) getFeRewardOptionCreditCardBillEraser(creditCardBillEraser *beRewardsPb.CreditCardBillEraser) *fePb.RewardOption_Cash {
	if creditCardBillEraser == nil {
		return nil
	}
	return &fePb.RewardOption_Cash{
		Cash: &fePb.Cash{Amount: types.GetFromBeMoney(creditCardBillEraser.GetAmount())},
	}
}

func (r *RewardService) getFeRewardOptionFiCoins(fiCoins *beRewardsPb.FiCoins) *fePb.RewardOption_FiCoins {
	if fiCoins == nil {
		return nil
	}
	return &fePb.RewardOption_FiCoins{
		FiCoins: &fePb.FiCoins{
			Points:    fiCoins.GetUnits(),
			ExpiresAt: fiCoins.GetExpiresAt(),
		},
	}
}

func (r *RewardService) getFeRewardOptionSmartDeposit(sd *beRewardsPb.SmartDeposit) *fePb.RewardOption_Sd {
	if sd == nil {
		return nil
	}
	return &fePb.RewardOption_Sd{
		Sd: &fePb.SmartDeposit{
			Amount: types.GetFromBeMoney(sd.GetAmount()),
		},
	}
}

func (r *RewardService) getFeRewardOptionGiftHamper(giftHamper *beRewardsPb.GiftHamper) *fePb.RewardOption_GiftHamper {
	if giftHamper == nil {
		return nil
	}
	return &fePb.RewardOption_GiftHamper{
		GiftHamper: &fePb.GiftHamper{
			ProductName:     giftHamper.GetProductName(),
			ShippingAddress: convertToClientPostalAddressType(giftHamper.GetShippingAddress()),
		},
	}
}

func (r *RewardService) getFeRewardOptionLuckyDraw(luckyDraw *beRewardsPb.LuckyDraw) *fePb.RewardOption_Luckydraw {
	if luckyDraw == nil {
		return nil
	}
	return &fePb.RewardOption_Luckydraw{
		Luckydraw: &fePb.LuckyDraw{
			LuckyDrawId: luckyDraw.GetLuckyDrawId(),
		},
	}
}

func (r *RewardService) getFeRewardOptionNoReward(noReward *beRewardsPb.NoReward) *fePb.RewardOption_NoReward {
	if noReward == nil {
		return nil
	}
	return &fePb.RewardOption_NoReward{
		NoReward: &fePb.NoReward{},
	}
}

func (r *RewardService) getFePageContextFromBeRewardPageContext(bePageContext *rpc.PageContextResponse) *fePb.PageContextResponse {
	if bePageContext == nil {
		return nil
	}
	return &fePb.PageContextResponse{
		BeforeToken: bePageContext.GetBeforeToken(),
		HasBefore:   bePageContext.GetHasBefore(),
		AfterToken:  bePageContext.GetAfterToken(),
		HasAfter:    bePageContext.GetHasAfter(),
	}
}

func (r *RewardService) getFePageContextFromBeOfferPageContext(bePageContext *beCasperPb.PageContextResponse) *fePb.PageContextResponse {
	if bePageContext == nil {
		return nil
	}
	return &fePb.PageContextResponse{
		BeforeToken: bePageContext.GetBeforeToken(),
		HasBefore:   bePageContext.GetHasBefore(),
		AfterToken:  bePageContext.GetAfterToken(),
		HasAfter:    bePageContext.GetHasAfter(),
	}
}

func (r *RewardService) getFePageContextFromBeRpcPageContext(bePageContext *rpc.PageContextResponse) *fePb.PageContextResponse {
	if bePageContext == nil {
		return nil
	}
	return &fePb.PageContextResponse{
		BeforeToken: bePageContext.GetBeforeToken(),
		HasBefore:   bePageContext.GetHasBefore(),
		AfterToken:  bePageContext.GetAfterToken(),
		HasAfter:    bePageContext.GetHasAfter(),
	}
}

func (r *RewardService) getRpcPageContextRespFromFePageContextResp(fePageContextResp *fePb.PageContextResponse) *rpc.PageContextResponse {
	if fePageContextResp == nil {
		return nil
	}
	return &rpc.PageContextResponse{
		BeforeToken: fePageContextResp.GetBeforeToken(),
		HasBefore:   fePageContextResp.GetHasBefore(),
		AfterToken:  fePageContextResp.GetAfterToken(),
		HasAfter:    fePageContextResp.GetHasAfter(),
	}
}

func (r *RewardService) getBeRewardPageContext(fePageContext *fePb.PageContextRequest, rewardsFrontendMeta *config.RewardsFrontendMeta) *rpc.PageContextRequest {
	pc := &rpc.PageContextRequest{
		PageSize: rewardsFrontendMeta.AppFetchRewardsPageSize,
	}
	// if FE page context is nil, BE page context to fetch the first page is returned
	// it mainly happens for first request received from the app when the before/after
	// token is not available.
	if fePageContext == nil {
		return pc
	}
	switch fePageContext.GetToken().(type) {
	case *fePb.PageContextRequest_AfterToken:
		pc.Token = &rpc.PageContextRequest_AfterToken{AfterToken: fePageContext.GetAfterToken()}
	case *fePb.PageContextRequest_BeforeToken:
		pc.Token = &rpc.PageContextRequest_BeforeToken{BeforeToken: fePageContext.GetBeforeToken()}
	}
	return pc
}

func (r *RewardService) getBeOfferPageContext(fePageContext *fePb.PageContextRequest, pageSize uint32) *beCasperPb.PageContextRequest {
	if fePageContext == nil {
		return nil
	}
	pc := &beCasperPb.PageContextRequest{
		PageSize: pageSize,
	}
	switch fePageContext.GetToken().(type) {
	case *fePb.PageContextRequest_AfterToken:
		pc.Token = &beCasperPb.PageContextRequest_AfterToken{AfterToken: fePageContext.GetAfterToken()}
	case *fePb.PageContextRequest_BeforeToken:
		pc.Token = &beCasperPb.PageContextRequest_BeforeToken{BeforeToken: fePageContext.GetBeforeToken()}
	}
	return pc
}

func (r *RewardService) getBeRpcPageContext(fePageContext *fePb.PageContextRequest, pageSize uint32) *rpc.PageContextRequest {
	if fePageContext == nil {
		return nil
	}
	pc := &rpc.PageContextRequest{
		PageSize: pageSize,
	}
	switch fePageContext.GetToken().(type) {
	case *fePb.PageContextRequest_AfterToken:
		pc.Token = &rpc.PageContextRequest_AfterToken{AfterToken: fePageContext.GetAfterToken()}
	case *fePb.PageContextRequest_BeforeToken:
		pc.Token = &rpc.PageContextRequest_BeforeToken{BeforeToken: fePageContext.GetBeforeToken()}
	}
	return pc
}

func (r *RewardService) getFePageCtxReqFromRpcPageCtxReq(rpcPageContextReq *rpc.PageContextRequest) *fePb.PageContextRequest {
	// checking for empty string AfterToken since APK 1535 was sending empty string instead of nil pageContextReq.
	if rpcPageContextReq == nil || rpcPageContextReq.GetAfterToken() == "" {
		return nil
	}
	pc := &fePb.PageContextRequest{}
	switch rpcPageContextReq.GetToken().(type) {
	case *rpc.PageContextRequest_AfterToken:
		pc.Token = &fePb.PageContextRequest_AfterToken{AfterToken: rpcPageContextReq.GetAfterToken()}
	case *rpc.PageContextRequest_BeforeToken:
		pc.Token = &fePb.PageContextRequest_BeforeToken{BeforeToken: rpcPageContextReq.GetBeforeToken()}
	}
	return pc
}

func (r *RewardService) getBeOfferRedemptionMode(feOfferRedemptionMode fePb.OfferRedemptionMode) beCasperPb.OfferRedemptionMode {
	switch feOfferRedemptionMode {
	case fePb.OfferRedemptionMode_FI_COINS:
		return beCasperPb.OfferRedemptionMode_FI_COINS
	case fePb.OfferRedemptionMode_FI_CARD:
		return beCasperPb.OfferRedemptionMode_FI_CARD
	default:
		return beCasperPb.OfferRedemptionMode_UNSPECIFIED_REDEMPTION_MODE
	}
}

func (r *RewardService) getBeOfferRedemptionModeFromCardType(cardType fePb.CardType) beCasperPb.OfferRedemptionMode {
	switch cardType {
	case fePb.CardType_DEBIT_CARD:
		return beCasperPb.OfferRedemptionMode_FI_CARD
	case fePb.CardType_CREDIT_CARD:
		return beCasperPb.OfferRedemptionMode_FI_CREDIT_CARD
	default:
		return beCasperPb.OfferRedemptionMode_UNSPECIFIED_REDEMPTION_MODE
	}
}

// nolint:funlen,dupl
func (r *RewardService) getFeOffer(
	ctx context.Context,
	beOffer *beCasperPb.Offer,
	beOfferListing *beCasperPb.OfferListing,
	displayExpressionFunctionMap map[string]govaluate.ExpressionFunction,
	isFullSalaryProgramActiveForActor bool,
	appVersion uint32,
	appPlatform commontypes.Platform,
	renderLocation tags.RenderLocation,
	beOfferIdToOfferLevelInventoryMap map[string]*beCasperPb.OfferInventory,
	fiCoinsBalance uint32,
	offerIdToUserLevelAttemptsRemainingMap map[string]int32, // todo(sresth) think of a better variable name
	isMonthlyMaxCapHit bool,
) (*fePb.Offer, error) {
	if beOffer == nil {
		return nil, nil
	}

	beTags, _, _ := r.getUniqueTagsFromOffers([]*beExchangerPb.ExchangerOffer{}, []*beCasperPb.Offer{beOffer})

	tags, _, err := r.tagsManager.GetTagsDetailsOrderedByPriority(ctx, beTags, renderLocation)
	if err != nil {
		return nil, fmt.Errorf("error while getting tags details, err: %w", err)
	}

	res := &fePb.Offer{
		Id:         beOffer.GetId(),
		ExternalId: beOffer.GetExternalId(),
		Price:      r.getOfferPrice(beOffer.GetPrice(), beOffer.GetDiscountDetails(), appVersion, appPlatform),
		DisplayDetails: &fePb.Offer_DisplayDetails{
			Name:                beOffer.GetName(),
			Desc:                beOffer.GetDesc(),
			IsSelectiveEligible: true,
		},
		OfferType:     r.getFeOfferType(beOffer.GetOfferType()),
		OfferMetadata: r.getFeOfferMetadata(beOffer.GetOfferMetadata()),
		Tags:          tags,
	}
	if beOffer.GetAdditionalDetails() != nil {
		res.DisplayDetails.OfferDetails = beOffer.GetAdditionalDetails().GetOfferDetails()
		res.DisplayDetails.HowToRedeem = removeHTMLTags(beOffer.GetAdditionalDetails().GetHowToRedeem())
		res.DisplayDetails.NextSteps = beOffer.GetAdditionalDetails().GetNextSteps()
		res.DisplayDetails.BgColor = beOffer.GetAdditionalDetails().GetBgColor()
		res.DisplayDetails.AfterRedemptionOfferName = beOffer.GetAdditionalDetails().GetAfterRedemptionOfferName()
		res.DisplayDetails.OfferTitle = beOffer.GetAdditionalDetails().GetOfferTitle()
		res.DisplayDetails.HomeTitle = beOffer.GetAdditionalDetails().GetHomeTitle()
		res.DisplayDetails.BrandName = beOffer.GetAdditionalDetails().GetBrandName()
		res.DisplayDetails.TileImageContentType = beOffer.GetAdditionalDetails().GetTileImageContentType()
	}
	for _, beImage := range beOffer.GetImages() {
		res.DisplayDetails.Images = append(res.DisplayDetails.Images, r.getFeOfferImage(beImage))
	}
	if beOffer.GetTnc() != nil {
		res.DisplayDetails.Tnc = &fePb.OfferTnc{TncList: removeHTMLTags(beOffer.GetTnc().GetTncList()), TncListV2: r.getFormattedCatalogOfferTncs(beOffer.GetTnc().GetTncList())}
	}
	// enrich feOffer with offer listing information if present
	if beOfferListing != nil {
		var err error
		res.ActiveFrom, err = datetime.ParseStringTimeStampProto(time.RFC3339, beOfferListing.GetActiveSince())
		if err != nil {
			return nil, errors.Wrap(err, "error parsing offer active since time string")
		}
		res.ActiveTill, err = datetime.ParseStringTimeStampProto(time.RFC3339, beOfferListing.GetActiveTill())
		if err != nil {
			return nil, errors.Wrap(err, "error parsing offer active till time string")
		}

		res.DisplayDetails.UnredeemableOfferLabel, err = r.getUnredeemableOfferLabel(beOfferListing.GetActiveSince(), beOfferListing.GetDisplaySince())
		if err != nil {
			return nil, fmt.Errorf("error while getting unredeemable offer label, err:%w", err)
		}
		if res.DisplayDetails.UnredeemableOfferLabel != nil {
			res.DisplayDetails.IsOfferNotRedeemable = true
		}
	}

	// needed for rendering of salary-exclusive-tag on old clients
	salaryAccountTag := beOffer.GetAdditionalDetails().GetSalaryAccountDisplayTag()

	// ***NOTE***: SalaryAccountTag is only send for backwards compatibility
	isOfferSalaryExclusive := salaryAccountTag != nil || beOffer.GetAdditionalDetails().GetIsSalaryAccountExclusive() || lo.Contains(beOffer.GetTagsInfo().GetManualTags(), beCasperPb.TagName_SALARY_EXCLUSIVE_V2)
	if isOfferSalaryExclusive {
		// add salary program related tag
		res.DisplayDetails.SalaryAccountTag = &fePb.Tag{
			Text: &commontypes.Text{
				// todo(divyadeep): replace with valued picked from config
				Text:      "Salary account\nexclusive",
				FontColor: "#FFFFFF",
				BgColor:   "#********",
			},
			Image: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/casper/salary-program-tag-star.png",
			},
		}
	}

	finalOfferPrice := res.GetPrice()

	if beOffer.GetDiscountDetails() != nil {
		res.DiscountDetails = &fePb.DiscountDetails{
			DiscountedPrice: uint32(beOffer.GetDiscountDetails().GetDiscountedPrice()),
			DisplayMode:     getFeDiscountDisplayMode(beOffer.GetDiscountDetails().GetDisplayMode()),
		}
		finalOfferPrice = int32(beOffer.GetDiscountDetails().GetDiscountedPrice())
	}
	finalOfferPriceStringInIndianFormat := money.ToDisplayStringInIndianFormatFromFloatValue(float64(finalOfferPrice), 0)

	var activeSince time.Time
	var displaySince time.Time

	if beOfferListing != nil {
		// parsing activeSince and displaySince to check coming soon case
		activeSince, err = time.Parse(time.RFC3339, beOfferListing.GetActiveSince())
		if err != nil {
			return nil, errors.Wrap(err, "error parsing offer active since time string")
		}
		displaySince, err = time.Parse(time.RFC3339, beOfferListing.GetDisplaySince())
		if err != nil {
			return nil, errors.Wrap(err, "error parsing offer display since time string")
		}
	}

	// checking for different cases when offer is not redeemable and setting inoperableInfo and isOfferNotRedeemable according to that
	switch {
	// coming soon
	case beOfferListing != nil && displaySince.Before(time.Now()) && time.Now().Before(activeSince):
		res.DisplayDetails.IsOfferNotRedeemable = true
		res.DisplayDetails.ShowGreyedOutOfferCard = true
		res.DisplayDetails.CtaLabel = r.getComingSoonCtaLabel()
	// salary program not active for user
	case (salaryAccountTag != nil || beOffer.GetAdditionalDetails().GetIsSalaryAccountExclusive()) && !isFullSalaryProgramActiveForActor:
		res.DisplayDetails.SalaryAccountTag.IsTappable = true
		res.DisplayDetails.SalaryAccountTag.Action = &fePb.Tag_BottomSheetAction{
			BottomSheetAction: r.getSalaryProgramInactiveBottomSheetForOffer(),
		}
		res.DisplayDetails.InoperableInfo = r.getSalaryProgramInactiveBottomSheetForOffer()
		res.DisplayDetails.IsOfferNotRedeemable = true
		res.DisplayDetails.IsSelectiveEligible = false
	// user level inventory exhausted
	case offerIdToUserLevelAttemptsRemainingMap != nil && offerIdToUserLevelAttemptsRemainingMap[beOffer.GetId()] == 0:
		res.DisplayDetails.IsOfferNotRedeemable = true
		res.DisplayDetails.InoperableInfo = r.getInoperableInfoForUserLevelInventoryExhausted()
		res.DisplayDetails.IsSelectiveEligible = false
	// global inventory exhausted
	case beOfferIdToOfferLevelInventoryMap[beOffer.GetId()].GetAvailableCount() == 0:
		res.DisplayDetails.IsOfferNotRedeemable = true
		res.DisplayDetails.ShowGreyedOutOfferCard = true
		res.DisplayDetails.CtaLabel = r.getOutOfStockCtaLabel()
	// monthly cap on redemptions hit
	case isMonthlyMaxCapHit:
		res.DisplayDetails.IsOfferNotRedeemable = true
		res.DisplayDetails.InoperableInfo = r.getInoperableInfoForMonthlyAttemptsMaxCapHit()
		res.DisplayDetails.IsSelectiveEligible = false
	case beOffer.GetDiscountDetails().GetDisplayMode() == beDiscountsPb.DisplayMode_SLASHED_PRICE_WITH_TIMER:
		res.DisplayDetails.CtaLabel = r.getLimitedTimeOfferCtaLabelWithTimer(beOffer.GetDiscountDetails().GetDiscountEndTime())
	}

	// not enough Fi-Coin Balance
	// NOTE : Don't move this as this is the last case in the switch case and has been added here separately to
	//        fill IsSelectiveEligible field even if the offer is not affordable for a user.
	if (int32(fiCoinsBalance) < finalOfferPrice) && res.GetDisplayDetails().GetInoperableInfo() == nil {
		res.DisplayDetails.IsOfferNotRedeemable = true
		res.DisplayDetails.InoperableInfo = r.getInoperableInfoForInsufficientFiCoinBalance(finalOfferPriceStringInIndianFormat)
		res.DisplayDetails.IsSelectiveEligible = true
	}

	// getting InoperableInfo
	if beOffer.GetAdditionalDetails().GetDisplayConstraintExpression() != "" && displayExpressionFunctionMap != nil {
		isEligible, inoperableInfo, err := r.offerDisplayEngine.EvaluateEligibility(ctx, beOffer.GetAdditionalDetails().GetDisplayConstraintExpression(), displayExpressionFunctionMap)
		if err != nil {
			return nil, fmt.Errorf("error evaluating display constraint expression for offer, err: %w", err)
		}
		if !isEligible && inoperableInfo == nil {
			return nil, nil
		}
		if inoperableInfo != nil {
			res.DisplayDetails.IsOfferNotRedeemable = true
			res.DisplayDetails.InoperableInfo = inoperableInfo
		}
		if !isEligible {
			res.DisplayDetails.IsSelectiveEligible = false
		}
	}

	// getting ctaText data
	switch {
	// setting DiscountDetails to nil as part of CTA is handled by client for discount case,
	// so was conflicting with "offer collected" state
	case offerIdToUserLevelAttemptsRemainingMap != nil && offerIdToUserLevelAttemptsRemainingMap[beOffer.GetId()] == 0:
		res.DisplayDetails.CtaText = r.getCtaText(ctx, "Offer Collected", finalOfferPriceStringInIndianFormat, res.GetDisplayDetails().GetIsOfferNotRedeemable())
		res.DiscountDetails = nil
	// custom cta text if alternateCtaText is set
	case beOffer.GetAdditionalDetails().GetAlternateCtaText() != "":
		res.DisplayDetails.CtaText = r.getCtaText(ctx, beOffer.GetAdditionalDetails().GetAlternateCtaText(), finalOfferPriceStringInIndianFormat, res.GetDisplayDetails().GetIsOfferNotRedeemable())
		res.DiscountDetails = nil
	default:
		res.DisplayDetails.CtaText = r.getCtaText(ctx, fmt.Sprintf("%s Fi-Coins", finalOfferPriceStringInIndianFormat), finalOfferPriceStringInIndianFormat, res.GetDisplayDetails().GetIsOfferNotRedeemable())
	}

	return res, nil
}

func (r *RewardService) getCtaText(ctx context.Context, ctaTextString string, offerPriceString string, isCtaDisabled bool) *commontypes.Text {

	priceKey := "{{REDEMPTION_PRICE}}"
	paramsMap := map[string]string{
		priceKey: offerPriceString,
	}
	ctaTextStringAfterEvaluatingTemplateTextExpression := r.evaluateTemplateTextExpression(ctaTextString, paramsMap)

	var ctaText *commontypes.Text
	if isCtaDisabled {
		if !rewardsFrontendPkg.IsOffersCatalogPageV2Enabled(ctx, r.dyconf) {
			ctaTextStringAfterEvaluatingTemplateTextExpression = fmt.Sprintf("<font color='#B9B9B9'>%s</font>", ctaTextStringAfterEvaluatingTemplateTextExpression)
		}

		ctaText = &commontypes.Text{
			FontColor: "#B9B9B9",
			DisplayValue: &commontypes.Text_Html{
				Html: ctaTextStringAfterEvaluatingTemplateTextExpression,
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BUTTON_M,
			},
		}
	} else {
		if !rewardsFrontendPkg.IsOffersCatalogPageV2Enabled(ctx, r.dyconf) {
			ctaTextStringAfterEvaluatingTemplateTextExpression = fmt.Sprintf("<font color='#00B899'>%s</font>", ctaTextStringAfterEvaluatingTemplateTextExpression)
		}

		ctaText = &commontypes.Text{
			FontColor: "#00B899",
			DisplayValue: &commontypes.Text_Html{
				Html: ctaTextStringAfterEvaluatingTemplateTextExpression,
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BUTTON_M,
			},
		}
	}

	return ctaText
}

func (r *RewardService) evaluateTemplateTextExpression(templateTextExpression string, templateParamsMap map[string]string) string {
	result := templateTextExpression
	// use templateParamsMap for evaluating template params.
	for paramName, paramValue := range templateParamsMap {
		result = strings.ReplaceAll(result, paramName, paramValue)
	}
	return result
}

func (r *RewardService) getOutOfStockCtaLabel() *rewards.Label {
	outOfStockCtaLabel := &rewards.Label{
		Text: &commontypes.Text{
			FontColor: "#FFFFFF",
			BgColor:   "#A73F4B",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Out of stock",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
			},
		},
		BgColor: "#A73F4B",
	}

	return outOfStockCtaLabel
}

func (r *RewardService) getComingSoonCtaLabel() *rewards.Label {
	comingSoonCtaLabel := &rewards.Label{
		Text: &commontypes.Text{
			FontColor: "#FFFFFF",
			BgColor:   "#87BA6B",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Coming Soon",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
			},
		},
		BgColor: "#87BA6B",
	}

	return comingSoonCtaLabel
}

func (r *RewardService) getNoMoreTriesLeftCtaLabel() *rewards.Label {
	noMoreTriesLeftCtaLabel := &rewards.Label{
		Text: &commontypes.Text{
			FontColor: "#FFFFFF",
			BgColor:   "#A73F4B",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "No more tries left",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
			},
		},
		BgColor: "#A73F4B",
	}

	return noMoreTriesLeftCtaLabel
}

func (r *RewardService) getLimitedTimeOfferCtaLabelWithTimer(timerEndTime *timestamppb.Timestamp) *rewards.Label {
	return &rewards.Label{
		Text:    r.getText(colorFiSnow, "", "Limited time offer!", commontypes.FontStyle_SUBTITLE_S),
		BgColor: colorSupportingAmber700,
		CountdownTimer: &rewards.CountdownTimer{
			Text:              r.getText(colorSupportingAmber100, "", "ENDS IN", commontypes.FontStyle_NUMBER_2XS),
			Icon:              commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/rewards/countdown_timer_separator.png"),
			CountdownTillTime: timerEndTime,
			BgColor:           &widget.BackgroundColour{Colour: &widget.BackgroundColour_BlockColour{BlockColour: colorSupportingAmber900}},
		},
	}
}

func (r *RewardService) getInoperableInfoForUserLevelInventoryExhausted() *rewards.BottomSheetInfo {
	inoperableInfo := &rewards.BottomSheetInfo{
		Title: &commontypes.Text{
			FontColor: "#333333",
			BgColor:   "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Offer collected",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
			},
		},
		Desc: &commontypes.Text{
			FontColor: "#646464",
			BgColor:   "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "You have collected this offer already, you can find it in your 'collected offers'",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
			},
		},
		Cta: &rewards.CTA{
			Text: &commontypes.Text{
				Text:      "View Collected Offers",
				FontColor: "#FFFFFF",
				BgColor:   "#00B899",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_3,
				},
			},
			IsVisible: true,
			DeeplinkAction: &deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_REDEEMED_OFFERS_SCREEN,
			},
		},
	}

	return inoperableInfo
}

// nolint:dupl
func (r *RewardService) getInoperableInfoForMonthlyAttemptsMaxCapHit() *rewards.BottomSheetInfo {
	inoperableInfo := &rewards.BottomSheetInfo{
		Title: &commontypes.Text{
			FontColor: "#333333",
			BgColor:   "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Offer collected",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
			},
		},
		Desc: &commontypes.Text{
			FontColor: "#646464",
			BgColor:   "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "You have collected this offer already, you can find it in your 'collected offers'",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
			},
		},
		Cta: &rewards.CTA{
			Text: &commontypes.Text{
				Text:      "View Collected Offers",
				FontColor: "#FFFFFF",
				BgColor:   "#00B899",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_3,
				},
			},
			IsVisible: true,
			DeeplinkAction: &deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_REDEEMED_OFFERS_SCREEN,
			},
		},
	}

	return inoperableInfo
}

func (r *RewardService) getInoperableInfoForInsufficientFiCoinBalance(offerPriceString string) *rewards.BottomSheetInfo {
	inoperableInfo := &rewards.BottomSheetInfo{
		Title: &commontypes.Text{
			FontColor: "#333333",
			BgColor:   "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Not enough Fi-Coins",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
			},
		},
		Desc: &commontypes.Text{
			FontColor: "#646464",
			BgColor:   "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: fmt.Sprintf("You will need at least %s Fi-Coins to be able to claim the offer.", offerPriceString),
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
			},
		},
		Cta: &rewards.CTA{
			Text: &commontypes.Text{
				Text:      "Earn Money-Plants",
				FontColor: "#FFFFFF",
				BgColor:   "#00B899",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_3,
				},
			},
			IsVisible: true,
			DeeplinkAction: &deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_REWARDS_WAYS_TO_EARN,
			},
		},
	}

	return inoperableInfo
}

// getOfferPrice returns the price at which the catalog offer will be redeemable.
// For newer clients we'll send the original price as they have the capability of
// showing price with discounting. Newer clients, however, lack this ability but
// if they try to redeem the offer it'll be redeemed on discounted price only.
// Hence, we send the discounted price as original price for older client so
// there's no discrepancy in the displayed offer price and amount of fi-coins
// deducted for redemption of offer.
func (r *RewardService) getOfferPrice(originalPrice float32, discountDetails *beCasperPb.DiscountDetails, appVersion uint32, appPlatform commontypes.Platform) int32 {
	// returning original price for newer clients as they have capability of showing
	// discount in catalog
	if discountDetails == nil ||
		(appPlatform == commontypes.Platform_ANDROID && appVersion > r.dyconf.RewardsFrontendMeta().MinAndroidVersionSupportingCatalogDiscounts()) ||
		(appPlatform == commontypes.Platform_IOS && appVersion > r.dyconf.RewardsFrontendMeta().MinIosVersionSupportingCatalogDiscounts()) {
		return int32(originalPrice)
	}

	// returning discounted price as original price for older clients as redemption
	// will be made at discounted price for them as well and we want to show that
	// value in catalog offer
	return int32(discountDetails.GetDiscountedPrice())
}

func getFeDiscountDisplayMode(beDisplayMode beDiscountsPb.DisplayMode) fePb.DiscountDetails_DisplayMode {
	switch beDisplayMode {
	case beDiscountsPb.DisplayMode_SLASHED_PRICE, beDiscountsPb.DisplayMode_SLASHED_PRICE_WITH_TIMER:
		return fePb.DiscountDetails_SLASHED_PRICE
	default:
		return fePb.DiscountDetails_UNSPECIFIED_DISPLAY_MODE
	}
}

func (r *RewardService) getUnredeemableOfferLabel(activeSinceString, displaySinceString string) (*ui.IconTextComponent, error) {
	var err error
	activeSince, err := time.Parse(time.RFC3339, activeSinceString)
	if err != nil {
		return nil, errors.Wrap(err, "error parsing offer active since time string")
	}
	displaySince, err := time.Parse(time.RFC3339, displaySinceString)
	if err != nil {
		return nil, errors.Wrap(err, "error parsing offer display since time string")
	}

	if displaySince.Before(time.Now()) && time.Now().Before(activeSince) {
		comingSoonLabelConfig, ok := r.dyconf.RewardsFrontendMeta().UnredeemableOfferLabelConfig().Load(strings.ToLower(UNREDEEMABLE_OFFER_LABEL_COMING_SOON))
		if !ok {
			// returning error as "coming soon" label info needs to be present for all environments, otherwise inactive offer would be displayed without any context
			return nil, fmt.Errorf("coming soon label config not found")
		}

		return &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_PlainString{PlainString: comingSoonLabelConfig.Text()},
					FontColor:    comingSoonLabelConfig.FontColor(),
					BgColor:      comingSoonLabelConfig.BgColor(),
				},
			},
			LeftIcon: &commontypes.Image{ImageUrl: comingSoonLabelConfig.ImageUrl()},
		}, nil
	}

	return nil, nil
}

// todo(divyadeep): figure out a way of obtaining exchanger offer listing's ActiveSince and DisplaySince here to add "Coming Soon" tag
// nolint:funlen,dupl
func (r *RewardService) convertToFeExchangerOffer(
	ctx context.Context,
	beExchangerOffer *beExchangerPb.ExchangerOffer,
	offerListing *beExchangerPb.ExchangerOfferListing,
	actorAttemptsForOffer int32,
	fiCoinsBalance uint32,
	utilisationMaxed, isUserFullSalaryProgramActive bool,
	displayExpressionFunctionMap map[string]govaluate.ExpressionFunction,
	renderLocation tags.RenderLocation,
	isMonthlyCapHit bool,
) (*fePb.ExchangerOfferWidget, error) {
	if beExchangerOffer == nil {
		return nil, nil
	}

	feTags, _, _ := r.getUniqueTagsFromOffers([]*beExchangerPb.ExchangerOffer{beExchangerOffer}, []*beCasperPb.Offer{})

	tags, _, err := r.tagsManager.GetTagsDetailsOrderedByPriority(ctx, feTags, renderLocation)
	if err != nil {
		return nil, fmt.Errorf("error while getting tags details, err: %w", err)
	}

	beExchangerOfferRedemptionPriceString := money.ToDisplayStringInIndianFormatFromFloatValue(float64(beExchangerOffer.GetRedemptionPrice()), 0)
	feExchangerOffer := &fePb.ExchangerOfferWidget{
		Id: beExchangerOffer.GetId(),
		DisplayDetails: &fePb.ExchangerOfferWidget_DisplayDetails{
			Title:     beExchangerOffer.GetOfferDisplayDetails().GetTitle(),
			HomeTitle: beExchangerOffer.GetOfferDisplayDetails().GetHomeTitle(),
			Subtitle:  beExchangerOffer.GetOfferDisplayDetails().GetSubtitle(),
			BgColor:   beExchangerOffer.GetOfferDisplayDetails().GetTileBgColor(),
			ImageUrl:  beExchangerOffer.GetOfferDisplayDetails().GetImageUrl(),
			// todo: remove this field after force upgrade
			EnterOfferCtaText: fmt.Sprintf("Redeem with %s Fi-Coins", beExchangerOfferRedemptionPriceString),
			EnterOfferCtaTextV1: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: fmt.Sprintf("<font color='#333333'>Redeem with </font><font color='#00B899'>%s Fi-Coins</font>", beExchangerOfferRedemptionPriceString),
				},
			},
			ExpandedDetails: &fePb.ExchangerOfferWidget_DisplayDetails_ExpandedDetails{
				// todo(rohanchougule): make it configurable later if reqd
				Title: "Swipe to uncover your secret treasure!",
				Desc:  beExchangerOffer.GetOfferDisplayDetails().GetDesc(),
				BeforeRedeemCta: &fePb.ExchangerOfferWidget_DisplayDetails_ExpandedDetails_CTA{
					Text:        fmt.Sprintf("USE %s FI-COINS", beExchangerOfferRedemptionPriceString),
					IsClickable: true,
				},
				AfterRedeemCta: &fePb.ExchangerOfferWidget_DisplayDetails_ExpandedDetails_CTA{
					Text:        "NICE!",
					IsClickable: true,
				},
				ExpandedStateScreen:     &deepLinkPb.Deeplink{Screen: deepLinkPb.Screen_REDEEM_EXCHANGER_OFFER},
				RedemptionAnimationType: r.getFeAnimationType(beExchangerOffer.GetOfferDisplayDetails().GetRedemptionAnimationType()),
			},
			InfoBannerTitle:      beExchangerOffer.GetOfferDisplayDetails().GetInfoBannerTitle(),
			InfoBannerBgColor:    beExchangerOffer.GetOfferDisplayDetails().GetInfoBannerBgColor(),
			InfoBannerIconUrl:    beExchangerOffer.GetOfferDisplayDetails().GetInfoBannerIconUrl(),
			HowToRedeem:          removeHTMLTags(beExchangerOffer.GetOfferDisplayDetails().GetHowToRedeem()),
			Tnc:                  removeHTMLTags(beExchangerOffer.GetOfferDisplayDetails().GetTnc()),
			TncsV2:               r.getFormattedCatalogOfferTncs(beExchangerOffer.GetOfferDisplayDetails().GetTnc()),
			BrandName:            beExchangerOffer.GetOfferDisplayDetails().GetBrandName(),
			TileImageContentType: beExchangerOffer.GetOfferDisplayDetails().GetTileImageContentType(),
			IsSelectiveEligible:  true,
		},
		Tags: tags,
	}

	redemptionPrice, err := r.getFeExchangerOfferRedemptionPrice(beExchangerOffer.GetRedemptionPrice(), beExchangerOffer.GetRedemptionCurrency())
	if err != nil {
		return nil, errors.Wrap(err, "couldn't fetch feOfferRedemptionPrice from beExchangerOffer")
	}
	feExchangerOffer.RedemptionPrice = redemptionPrice

	// To mitigate the race condition which could happen at the backend when actor-attempts exceeds the offer-limit
	if actorAttemptsForOffer > int32(beExchangerOffer.GetOfferAggregatesConfig().GetDailyAllowedAttemptsPerUser()) {
		logger.Warn("AttemptsLeft is negative in convertFeExchangerOffer. Setting it to 0 explicitly",
			zap.String(logger.OFFER_ID, beExchangerOffer.GetId()),
		)
		feExchangerOffer.AttemptsLeft = 0
	} else {
		feExchangerOffer.AttemptsLeft = beExchangerOffer.GetOfferAggregatesConfig().GetDailyAllowedAttemptsPerUser() - uint32(actorAttemptsForOffer)
	}

	// we won't show the top label ('X tries left') if it's configured to be hidden, or monthly redemption cap has been hit
	if !(beExchangerOffer.GetOfferDisplayDetails().GetHideTopLabel() || isMonthlyCapHit) {
		topLabelTextString := ""
		if feExchangerOffer.AttemptsLeft == 1 {
			topLabelTextString = fmt.Sprintf("%d TRY LEFT TODAY", feExchangerOffer.AttemptsLeft)
		} else {
			topLabelTextString = fmt.Sprintf("%d TRIES LEFT TODAY", feExchangerOffer.AttemptsLeft)
		}
		feExchangerOffer.DisplayDetails.TopLabel = &rewards.Label{
			Text: &commontypes.Text{
				FontColor: "#333333",
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: topLabelTextString,
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
				},
			},
			BgColor: "#7AFFFFFF",
		}
	}

	isSalaryAccountExclusive := beExchangerOffer.GetOfferDisplayDetails().GetSalaryAccountTag() != nil || beExchangerOffer.GetAdditionalDetails().GetIsSalaryAccountExclusive() || lo.Contains(beExchangerOffer.GetTagsInfo().GetManualTags(), beCasperPb.TagName_SALARY_EXCLUSIVE_V2)

	// populate InoperableInfoV2. cases are ordered in decreasing order of priority,
	// i.e., 1st case has a higher priority than 2nd case if both conditions are
	// satisfied
	switch {
	// monthly cap hit case
	case isMonthlyCapHit:
		feExchangerOffer.DisplayDetails.IsOfferNotRedeemable = true
		feExchangerOffer.DisplayDetails.ShowGreyedOutOfferCard = true

		feExchangerOffer.DisplayDetails.InoperableInfo = &fePb.ExchangerOfferWidget_DisplayDetails_InoperableInfo{
			Desc: "No more tries left. Come back next month.",
			DetailedInfo: &fePb.ExchangerOfferWidget_DisplayDetails_InoperableInfo_DetailedInfo{
				Title: "Monthly limit reached",
				Description: fmt.Sprintf("You’ve reached the maximum limit of %d monthly tries. Please come back next month.",
					beExchangerOffer.GetOfferAggregatesConfig().GetUserLevelMonthlyRedemptionCap(),
				),
				CtaText: "OK",
			},
		}

		feExchangerOffer.DisplayDetails.ExpandedDetails.BeforeRedeemCta.IsClickable = false
		feExchangerOffer.DisplayDetails.ExpandedDetails.AfterRedeemCta.IsClickable = false

		// ctaLabel when user had exhausted the number of tries
		feExchangerOffer.DisplayDetails.CtaLabel = r.getNoMoreTriesLeftCtaLabel()

		// inoperableInfoV2 when user had hit the max cap for the month
		feExchangerOffer.DisplayDetails.InoperableInfoV2 = r.getMonthlyAttemptsExhaustedInoperableInfoV2()
		feExchangerOffer.DisplayDetails.IsSelectiveEligible = false
	case feExchangerOffer.GetAttemptsLeft() == 0:
		feExchangerOffer.DisplayDetails.IsOfferNotRedeemable = true
		feExchangerOffer.DisplayDetails.ShowGreyedOutOfferCard = true

		feExchangerOffer.DisplayDetails.InoperableInfo = &fePb.ExchangerOfferWidget_DisplayDetails_InoperableInfo{
			Desc: "No more tries left. Come back tomorrow.",
			DetailedInfo: &fePb.ExchangerOfferWidget_DisplayDetails_InoperableInfo_DetailedInfo{
				Title: "Daily limit reached",
				Description: fmt.Sprintf("You’ve reached the maximum limit of %d daily tries. Please come back tomorrow.",
					beExchangerOffer.GetOfferAggregatesConfig().GetDailyAllowedAttemptsPerUser(),
				),
				CtaText: "OK",
			},
		}

		feExchangerOffer.DisplayDetails.ExpandedDetails.BeforeRedeemCta.IsClickable = false
		feExchangerOffer.DisplayDetails.ExpandedDetails.AfterRedeemCta.IsClickable = false

		// ctaLabel when user had exhausted the number of tries
		feExchangerOffer.DisplayDetails.CtaLabel = r.getNoMoreTriesLeftCtaLabel()

		// inoperableInfoV2 when user had exhausted the number of tries
		feExchangerOffer.DisplayDetails.InoperableInfoV2 = r.getAttemptsExhaustedInoperableInfoV2()
		feExchangerOffer.DisplayDetails.IsSelectiveEligible = false
	}

	if fiCoinsBalance < feExchangerOffer.GetRedemptionPrice().GetPrice() &&
		feExchangerOffer.GetDisplayDetails().GetInoperableInfo() == nil &&
		feExchangerOffer.GetDisplayDetails().GetInoperableInfoV2() == nil {

		feExchangerOffer.DisplayDetails.IsOfferNotRedeemable = true
		feExchangerOfferRedemptionPriceStringInIndianFormat := money.ToDisplayStringInIndianFormatFromFloatValue(float64(feExchangerOffer.GetRedemptionPrice().GetPrice()), 0)
		feExchangerOffer.DisplayDetails.InoperableInfo = &fePb.ExchangerOfferWidget_DisplayDetails_InoperableInfo{
			Desc: fmt.Sprintf("You need at least %s Fi-Coins to play.", feExchangerOfferRedemptionPriceStringInIndianFormat),
			DetailedInfo: &fePb.ExchangerOfferWidget_DisplayDetails_InoperableInfo_DetailedInfo{
				Title: "Not enough Fi-Coins",
				Description: fmt.Sprintf("You will need at least %s Fi-Coins to be able to play.",
					feExchangerOfferRedemptionPriceStringInIndianFormat,
				),
				CtaText: "OK",
			},
		}

		feExchangerOffer.DisplayDetails.InoperableInfoV2 = r.getInoperableInfoForInsufficientFiCoinBalance(feExchangerOfferRedemptionPriceStringInIndianFormat)
		feExchangerOffer.DisplayDetails.IsSelectiveEligible = true

		feExchangerOffer.DisplayDetails.ExpandedDetails.BeforeRedeemCta.IsClickable = false
		feExchangerOffer.DisplayDetails.ExpandedDetails.AfterRedeemCta.IsClickable = false
	}

	// adding this check as this is not required for some flows so nil value is passed from there
	if offerListing != nil {
		activeSince, err1 := time.Parse(time.RFC3339, offerListing.GetActiveSince())
		if err1 != nil {
			return nil, errors.Wrap(err1, "error parsing offer active since time string")
		}
		displaySince, err2 := time.Parse(time.RFC3339, offerListing.GetDisplaySince())
		if err2 != nil {
			return nil, errors.Wrap(err2, "error parsing offer display since time string")
		}
		// ctaLabel data for coming soon offers
		if displaySince.Before(time.Now()) && time.Now().Before(activeSince) {
			feExchangerOffer.DisplayDetails.IsOfferNotRedeemable = true
			feExchangerOffer.DisplayDetails.ShowGreyedOutOfferCard = true
			feExchangerOffer.DisplayDetails.CtaLabel = r.getComingSoonCtaLabel()
			feExchangerOffer.DisplayDetails.InoperableInfo = nil
			feExchangerOffer.DisplayDetails.InoperableInfoV2 = nil
			feExchangerOffer.DisplayDetails.IsSelectiveEligible = true
		}
	}

	// todo(divyadeep): refactor remaining flows and make them part of switch/case
	// todo(rohanchougule): see if the meesaging can be made generic later on
	if utilisationMaxed {
		feExchangerOffer.DisplayDetails.InoperableInfo = &fePb.ExchangerOfferWidget_DisplayDetails_InoperableInfo{
			Desc: "You have reached the maximum limit of cash rewards for this offer",
			DetailedInfo: &fePb.ExchangerOfferWidget_DisplayDetails_InoperableInfo_DetailedInfo{
				Title:       "Rewards limit reached",
				Description: "You have reached the maximum limit of cash rewards for this offer. Try again when new offers are live.",
				CtaText:     "OK",
			},
		}

		feExchangerOffer.DisplayDetails.ExpandedDetails.BeforeRedeemCta.IsClickable = false
		feExchangerOffer.DisplayDetails.ExpandedDetails.AfterRedeemCta.IsClickable = false
		feExchangerOffer.DisplayDetails.IsOfferNotRedeemable = true
		feExchangerOffer.DisplayDetails.IsSelectiveEligible = false
	}

	// needed for support of salary-exclusive-tags old clients

	if isSalaryAccountExclusive {
		// set salary program tag
		feExchangerOffer.DisplayDetails.SalaryAccountTag = &fePb.Tag{
			Text: &commontypes.Text{
				Text:      "Salary account\nexclusive",
				FontColor: "#FFFFFF",
				BgColor:   "#********",
			},
			Image: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/casper/salary-program-tag-star.png",
			},
		}

		// fields to set in case salary program is inactive for the user for a salary-account-exclusive offer
		if !isUserFullSalaryProgramActive {
			feExchangerOffer.DisplayDetails.SalaryAccountTag.IsTappable = true
			feExchangerOffer.DisplayDetails.SalaryAccountTag.Action = &fePb.Tag_BottomSheetAction{
				BottomSheetAction: r.getSalaryProgramInactiveBottomSheetForOffer(),
			}
			// InoperableInfoV2 has higher priority over InoperableInfo
			feExchangerOffer.DisplayDetails.InoperableInfoV2 = r.getSalaryProgramInactiveBottomSheetForOffer()
			feExchangerOffer.DisplayDetails.IsOfferNotRedeemable = true
			feExchangerOffer.DisplayDetails.IsSelectiveEligible = false
		}
	}

	if beExchangerOffer.GetOfferDisplayDetails().GetDisplayConstraintExpression() != "" && displayExpressionFunctionMap != nil {
		isEligible, inoperableInfo, evalErr := r.offerDisplayEngine.EvaluateEligibility(ctx, beExchangerOffer.GetOfferDisplayDetails().GetDisplayConstraintExpression(), displayExpressionFunctionMap)
		if evalErr != nil {
			return nil, fmt.Errorf("error evaluating display constraint expression for offer, err: %w", err)
		}
		if !isEligible && inoperableInfo == nil {
			return nil, nil
		}

		if inoperableInfo != nil {
			feExchangerOffer.DisplayDetails.InoperableInfoV2 = inoperableInfo
			feExchangerOffer.DisplayDetails.IsOfferNotRedeemable = true
		}

		if !isEligible {
			feExchangerOffer.DisplayDetails.IsSelectiveEligible = false
		}
	}

	if offerListing != nil {
		feExchangerOffer.DisplayDetails.UnredeemableOfferLabel, err = r.getUnredeemableOfferLabel(offerListing.GetActiveSince(), offerListing.GetDisplaySince())
		if err != nil {
			return nil, fmt.Errorf("error while getting unredeemable offer label, err:%w", err)
		}
		if feExchangerOffer.DisplayDetails.UnredeemableOfferLabel != nil {
			feExchangerOffer.DisplayDetails.IsOfferNotRedeemable = true
		}
	}

	// setting cta text greyed out if the offer is not redeemable
	if feExchangerOffer.GetDisplayDetails().GetIsOfferNotRedeemable() {
		feExchangerOffer.DisplayDetails.EnterOfferCtaTextV1 = &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: fmt.Sprintf("<font color='#B9B9B9'>Redeem with %s Fi-Coins</font>", beExchangerOfferRedemptionPriceString),
			},
		}
	}

	// if alternateCtaText is set then show this in cta text
	if beExchangerOffer.GetAdditionalDetails().GetAlternateCtaText() != "" {
		feExchangerOffer.DisplayDetails.EnterOfferCtaTextV1 = r.getCtaText(ctx, beExchangerOffer.GetAdditionalDetails().GetAlternateCtaText(), beExchangerOfferRedemptionPriceString, feExchangerOffer.GetDisplayDetails().GetIsOfferNotRedeemable())
	}

	return feExchangerOffer, nil
}

func (r *RewardService) getExternalVendorRedemptionOrderStatusFromFeStatus(feRedeemedOfferStatus fePb.RedeemedOfferStatus) []evrPb.OrderStatus {
	successOrderStatuses := []evrPb.OrderStatus{
		evrPb.OrderStatus_ORDER_STATUS_CONFIRMED,
		evrPb.OrderStatus_ORDER_STATUS_SHIPPED,
		evrPb.OrderStatus_ORDER_STATUS_DELIVERED,
		evrPb.OrderStatus_ORDER_STATUS_PROCESSING,
		evrPb.OrderStatus_ORDER_STATUS_OUT_FOR_DELIVERY,
		evrPb.OrderStatus_ORDER_STATUS_RTO_INITIATED,
		evrPb.OrderStatus_ORDER_STATUS_RTO_COMPLETED,
		evrPb.OrderStatus_ORDER_STATUS_RETURN_INITIATED,
		evrPb.OrderStatus_ORDER_STATUS_RETURNED,
	}

	cancelledOrderStatuses := []evrPb.OrderStatus{
		evrPb.OrderStatus_ORDER_STATUS_CANCELLED,
		evrPb.OrderStatus_ORDER_STATUS_FAILED,
	}

	switch feRedeemedOfferStatus {
	case fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_CANCELLED:
		return cancelledOrderStatuses
	case fePb.RedeemedOfferStatus_REDEEMED_OFFER_STATUS_SUCCESSFUL:
		return successOrderStatuses
	default:
		return append(successOrderStatuses, cancelledOrderStatuses...)
	}
}

func (r *RewardService) getAttemptsExhaustedInoperableInfoV2() *rewards.BottomSheetInfo {
	attemptsExhaustedInoperableInfoV2 := &rewards.BottomSheetInfo{
		Title: &commontypes.Text{
			FontColor: "#333333",
			BgColor:   "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Attempts exhausted",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
			},
		},
		Desc: &commontypes.Text{
			FontColor: "#646464",
			BgColor:   "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "You've exhausted your attempts today. Come back tomorrow.",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
			},
		},
		Cta: &rewards.CTA{
			Text: &commontypes.Text{
				Text: "Ok, got it",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_3,
				},
				FontColor: "#FFFFFF",
				BgColor:   "#00B899",
			},
			IsVisible: true,
		},
	}

	return attemptsExhaustedInoperableInfoV2
}

// nolint:dupl
func (r *RewardService) getMonthlyAttemptsExhaustedInoperableInfoV2() *rewards.BottomSheetInfo {
	attemptsExhaustedInoperableInfoV2 := &rewards.BottomSheetInfo{
		Title: &commontypes.Text{
			FontColor: "#333333",
			BgColor:   "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Attempts exhausted",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
			},
		},
		Desc: &commontypes.Text{
			FontColor: "#646464",
			BgColor:   "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "You've exhausted your attempts for this month. Come back next month.",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
			},
		},
		Cta: &rewards.CTA{
			Text: &commontypes.Text{
				Text: "Ok, got it",
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_3,
				},
				FontColor: "#FFFFFF",
				BgColor:   "#00B899",
			},
			IsVisible: true,
		},
	}

	return attemptsExhaustedInoperableInfoV2
}

func (r *RewardService) getFeExchangerOfferRedemptionPrice(bePrice float32, beCurrency beExchangerPb.ExchangerOfferRedemptionCurrency) (*fePb.ExchangerOfferRedemptionPrice, error) {
	switch beCurrency {
	case beExchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS:
		return &fePb.ExchangerOfferRedemptionPrice{
			RedemptionMode: fePb.OfferRedemptionMode_FI_COINS,
			Price:          uint32(bePrice),
		}, nil
	default:
		return nil, fmt.Errorf("unsupported redemption currency: %v", beCurrency.String())
	}
}

func (r *RewardService) getFeAnimationType(beAnimationType beExchangerPb.ExchangerOfferRedemptionAnimationType) fePb.ExchangerOfferRedemptionAnimationType {
	switch beAnimationType {
	case beExchangerPb.ExchangerOfferRedemptionAnimationType_DIGGING_DOG_ANIMATION:
		return fePb.ExchangerOfferRedemptionAnimationType_DIGGING_DOG_ANIMATION
	case beExchangerPb.ExchangerOfferRedemptionAnimationType_UNDERWATER_TREASURE_ANIMATION:
		return fePb.ExchangerOfferRedemptionAnimationType_UNDERWATER_TREASURE_ANIMATION
	case beExchangerPb.ExchangerOfferRedemptionAnimationType_CRICKET_CUP_ANIMATION:
		return fePb.ExchangerOfferRedemptionAnimationType_CRICKET_CUP_ANIMATION
	default:
		return fePb.ExchangerOfferRedemptionAnimationType_EXCHANGER_OFFER_REDEMPTION_ANIMATION_TYPE_UNSPECIFIED
	}
}

// todo(rohanchougule): "user-input-needed" BE state is yet to be handled. Please accommodate in one of the status fns
func (r *RewardService) getFeExchangerOrderStatus(beStatus beExchangerPb.ExchangerOfferOrderState) (fePb.ExchangerOrderStatus, error) {
	switch {
	case isBeExchangerOrderUnclaimed(beStatus):
		return fePb.ExchangerOrderStatus_UNCLAIMED, nil
	case isBeExchangerOrderInProgress(beStatus):
		return fePb.ExchangerOrderStatus_IN_PROGRESS, nil
	case isBeExchangerOrderFulfilled(beStatus):
		return fePb.ExchangerOrderStatus_FULFILLED, nil
	case isBeExchangerOrderWaitingForUserInput(beStatus):
		return fePb.ExchangerOrderStatus_USER_INTERVENTION_REQUIRED, nil
	case isBeExchangerOrderFailed(beStatus):
		return fePb.ExchangerOrderStatus_FAILED, nil
	default:
		return fePb.ExchangerOrderStatus_UNSPECIFIED_ORDER_STATUS, fmt.Errorf("invalid BE exchanger-order status: %s", beStatus.String())
	}
}

// isBeExchangerOrderFulfilled decides whether the order is fulfilled at the backend or not
func isBeExchangerOrderFulfilled(beStatus beExchangerPb.ExchangerOfferOrderState) bool {
	switch beStatus {
	case beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLED:
		return true
	default:
		return false
	}
}

// isBeExchangerOrderFailed decides weather the order is failed at the backend or not
func isBeExchangerOrderFailed(beStatus beExchangerPb.ExchangerOfferOrderState) bool {
	switch beStatus {
	case beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_OFFER_AMOUNT_DEBIT_FAILED,
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REVERSED_DEBITED_OFFER_AMOUNT,
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REDEMPTION_FAILED,
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATION_FAILED:
		return true
	default:
		return false
	}
}

// isBeExchangerOrderUnclaimed decides whether the order is yet to be claimed by the user.
// It encapsulates all the intermediate steps from redeeming till an option is chosen
func isBeExchangerOrderUnclaimed(beStatus beExchangerPb.ExchangerOfferOrderState) bool {
	switch beStatus {
	case
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_CREATED,
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_OFFER_AMOUNT_DEBITED,
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATED:
		return true
	default:
		return false
	}
}

// isBeExchangerOrderInProgress decides whether the order is in progress at backend, i.e.
// option is chosen by the user but is yet to be credited
func isBeExchangerOrderInProgress(beStatus beExchangerPb.ExchangerOfferOrderState) bool {
	switch beStatus {
	case
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTION_CHOSEN,
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_FULFILLMENT_INITIATED,
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_FULFILLMENT_MANUAL_INTERVENTION:
		return true
	default:
		return false
	}
}

// isBeExchangerOrderWaitingForUserInput decides if input is needed from user for fulfillment of order, i.e.
// option is chosen by the user but some input is required to proceed (e.g. address in case of physical merchandise)
func isBeExchangerOrderWaitingForUserInput(beStatus beExchangerPb.ExchangerOfferOrderState) bool {
	switch beStatus {
	case
		beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_USER_INPUT_NEEDED_FOR_FULFILLMENT:
		return true
	default:
		return false
	}
}

func (r *RewardService) getExchangerOrderRedemptionCurrency(redemptionCurrency beExchangerPb.ExchangerOfferRedemptionCurrency) fePb.ExchangerOfferRedemptionCurrency {
	switch redemptionCurrency {
	case beExchangerPb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS:
		return fePb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS
	default:
		return fePb.ExchangerOfferRedemptionCurrency_EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED
	}
}

// nolint:funlen
func (r *RewardService) convertToFeExchangerOrder(ctx context.Context, beExchangerOrder *beExchangerPb.ExchangerOfferOrder, beExchangerOffer *beExchangerPb.ExchangerOffer, renderLocation tags.RenderLocation) (*fePb.ExchangerOrder, error) {
	if beExchangerOrder == nil {
		return nil, nil
	}

	feOptions := make([]*fePb.ExchangerOrderOption, len(beExchangerOrder.GetOptions().GetOptions()))
	// todo(rohanchougule): see if we can improve the BE proto to remove the nested GetOptions()
	for i, beOption := range beExchangerOrder.GetOptions().GetOptions() {
		feOptions[i] = r.convertToFeExchangerOrderOption(beOption, beExchangerOrder.GetChosenOption() != nil, beExchangerOrder.GetState())
	}

	beTags, _, _ := r.getUniqueTagsFromOffers([]*beExchangerPb.ExchangerOffer{beExchangerOffer}, []*beCasperPb.Offer{})

	tagDetails, _, err := r.tagsManager.GetTagsDetailsOrderedByPriority(ctx, beTags, renderLocation)
	if err != nil {
		return nil, fmt.Errorf("error while getting tags details, err: %w", err)
	}

	feExchangerOrder := &fePb.ExchangerOrder{
		Id:                         beExchangerOrder.GetId(),
		Options:                    feOptions,
		DefaultDecideTimeInSeconds: beExchangerOrder.GetOptions().GetDefaultDecideTimeInSecs(),
		CreatedAt:                  beExchangerOrder.GetCreatedAt(),
		ExternalId:                 beExchangerOrder.GetExternalId(),
		Tags:                       tagDetails,
	}

	// adding redemption price for this exchanger offer if exchangerOffer is passed as parameter.
	// this is kept optional as it's only used in "My orders" page currently, an isn't required for all FE exchangerOrders
	if beExchangerOffer != nil {
		feExchangerOrder.RedemptionPrice = beExchangerOffer.GetRedemptionPrice()
		feExchangerOrder.RedemptionCurrency = r.getExchangerOrderRedemptionCurrency(beExchangerOffer.GetRedemptionCurrency())
	}

	feExchangerOrderStatus, err := r.getFeExchangerOrderStatus(beExchangerOrder.GetState())
	if err != nil {
		return nil, errors.Wrap(err, "error converting BE exchanger-order status to FE")
	}
	// Introduced failed state, to avoid client changes we still return nil but do not throw error as it's an expected
	// state.
	if feExchangerOrderStatus == fePb.ExchangerOrderStatus_FAILED {
		logger.Debug(ctx,
			fmt.Sprintf("Failed status received for the given order - %s and BE status: %s",
				beExchangerOrder.GetId(),
				beExchangerOrder.GetState().String()))
		return nil, nil
	}
	statusDesc := r.getStatusDescTextForExchangerOrder(feExchangerOrderStatus, beExchangerOrder.GetRewardType())

	feExchangerOrder.Status = feExchangerOrderStatus
	feExchangerOrder.StatusDesc = statusDesc

	if beExchangerOrder.GetChosenOption() != nil {

		feExchangerOrder.ChosenOption = r.convertToFeExchangerOrderOption(beExchangerOrder.GetChosenOption(), true, beExchangerOrder.GetState())

		switch beExchangerOrder.GetChosenOption().GetRewardType() {
		case beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE:
			if beExchangerOffer.GetAdditionalDetails().GetOfferDescription() != nil {
				feExchangerOrder.RedeemedOfferDetailsSections = append(feExchangerOrder.RedeemedOfferDetailsSections, r.getOfferDescriptionSection(beExchangerOffer.GetAdditionalDetails().GetOfferDescription().GetHeading(), beExchangerOffer.GetAdditionalDetails().GetOfferDescription().GetPoints()))
			}
			feExchangerOrder.RedeemedOfferDetailsSections = append(feExchangerOrder.RedeemedOfferDetailsSections,
				r.getDeliveryDetailsSection(beExchangerOrder.GetChosenOption().GetPhysicalMerchandiseRewardMetadata().GetShippingAddress()),
				r.getOfferIdSection(beExchangerOrder.GetExternalId()),
				r.getNextStepsSection(beExchangerOrder.GetChosenOption().GetDisplayDetails().GetAdditionalDetails().GetNextSteps(), beExchangerOffer.GetOfferDisplayDetails().GetTnc()),
			)

		case beExchangerPb.RewardType_REWARD_TYPE_EGV:
			egvRewardMetadata := beExchangerOrder.GetChosenOption().GetEgvRewardMetadata()

			if beExchangerOffer.GetAdditionalDetails().GetOfferDescription() != nil {
				feExchangerOrder.RedeemedOfferDetailsSections = append(feExchangerOrder.RedeemedOfferDetailsSections, r.getOfferDescriptionSection(beExchangerOffer.GetAdditionalDetails().GetOfferDescription().GetHeading(), beExchangerOffer.GetAdditionalDetails().GetOfferDescription().GetPoints()))
			}

			if feExchangerOrderStatus == fePb.ExchangerOrderStatus_IN_PROGRESS {
				feExchangerOrder.RedeemedOfferDetailsSections = append(feExchangerOrder.RedeemedOfferDetailsSections, r.getOfferProcessingSection())
			} else if feExchangerOrderStatus == fePb.ExchangerOrderStatus_FULFILLED {
				feExchangerOrder.RedeemedOfferDetailsSections = append(feExchangerOrder.RedeemedOfferDetailsSections,
					r.getCopyCodeSectionForEgv(egvRewardMetadata.GetActivationCode(), egvRewardMetadata.GetPin(), egvRewardMetadata.GetCardNo(), egvRewardMetadata.GetActivationUrl(), beExchangerOffer.GetOfferDisplayDetails().GetDesc()),
					r.getOfferPeriodSection(egvRewardMetadata.GetExpiryTime()),
				)
			}

			feExchangerOrder.RedeemedOfferDetailsSections = append(feExchangerOrder.RedeemedOfferDetailsSections,
				r.getOfferIdSection(beExchangerOrder.GetExternalId()),
				r.getHowToRedeemSection(beExchangerOffer.GetOfferDisplayDetails().GetHowToRedeem(), beExchangerOffer.GetOfferDisplayDetails().GetTnc()),
			)

		default:
			// adding common sections for old offer types
			feExchangerOrder.RedeemedOfferDetailsSections = append(feExchangerOrder.RedeemedOfferDetailsSections, r.getOfferIdSection(beExchangerOrder.GetExternalId()))
		}
	}

	return feExchangerOrder, nil
}

func (r *RewardService) convertToFeExchangerOrderOption(beExchangerOrderOption *beExchangerPb.ExchangerOfferOption, optionClaimed bool, orderState beExchangerPb.ExchangerOfferOrderState) *fePb.ExchangerOrderOption {
	if beExchangerOrderOption == nil {
		return nil
	}

	var (
		optionDisplayTitle    string
		optionDisplaySubtitle string
		imageUrl              string
	)

	if optionClaimed {
		optionDisplayTitle = beExchangerOrderOption.GetDisplayDetails().GetAfterClaimTitle()
		optionDisplaySubtitle = beExchangerOrderOption.GetDisplayDetails().GetAfterClaimSubtitle()
		imageUrl = beExchangerOrderOption.GetDisplayDetails().GetAfterClaimIconUrl()
	} else {
		optionDisplayTitle = beExchangerOrderOption.GetDisplayDetails().GetBeforeClaimTitle()
		optionDisplaySubtitle = beExchangerOrderOption.GetDisplayDetails().GetBeforeClaimSubtitle()
		imageUrl = beExchangerOrderOption.GetDisplayDetails().GetBeforeClaimIconUrl()
	}

	feExchangerOrderOption := &fePb.ExchangerOrderOption{
		Id: beExchangerOrderOption.GetId(),
		DisplayDetails: &fePb.ExchangerOrderOption_Display{
			ImageUrl: imageUrl,
			Title:    optionDisplayTitle,
			Subtitle: optionDisplaySubtitle,
			Desc:     beExchangerOrderOption.GetDisplayDetails().GetDesc(),
			AdditionalDetails: &fePb.ExchangerOrderOption_Display_AdditionalDetails{
				BannerLogoUrl:  beExchangerOrderOption.GetDisplayDetails().GetAdditionalDetails().GetBannerLogoUrl(),
				BannerImageUrl: beExchangerOrderOption.GetDisplayDetails().GetAdditionalDetails().GetBannerImageUrl(),
				BannerTitle:    beExchangerOrderOption.GetDisplayDetails().GetAdditionalDetails().GetBannerTitle(),
				BannerBgColor:  beExchangerOrderOption.GetDisplayDetails().GetAdditionalDetails().GetBannerBgColor(),
				Desc:           beExchangerOrderOption.GetDisplayDetails().GetAdditionalDetails().GetDesc(),
				NextSteps:      beExchangerOrderOption.GetDisplayDetails().GetAdditionalDetails().GetNextSteps(),
				Tncs:           beExchangerOrderOption.GetDisplayDetails().GetAdditionalDetails().GetTnc(),
			},
		},
	}

	switch beExchangerOrderOption.GetRewardType() {
	case beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE:
		feExchangerOrderOption.DisplayDetails.DynamicFields = []*fePb.ExchangerOrderOption_Display_DynamicFields{
			{
				Name:       "DELIVERY ADDRESS",
				Value:      address.ConvertPostalAddressToString(beExchangerOrderOption.GetPhysicalMerchandiseRewardMetadata().GetShippingAddress()),
				IsCopyable: false,
			},
		}
	case beExchangerPb.RewardType_REWARD_TYPE_EGV:
		feExchangerOrderOption.DisplayDetails.DynamicFields = []*fePb.ExchangerOrderOption_Display_DynamicFields{
			{
				Name:       "PIN",
				Value:      beExchangerOrderOption.GetEgvRewardMetadata().GetPin(),
				IsCopyable: true,
			},
			{
				Name:       "CARD NO",
				Value:      beExchangerOrderOption.GetEgvRewardMetadata().GetCardNo(),
				IsCopyable: true,
			},
		}
	default:

	}

	// use-case: to decide the text to be shown at the bottom of the card in MyOrders page
	switch {
	case isBeExchangerOrderWaitingForUserInput(orderState):
		// currently, we just require user input for PM rewards
		if beExchangerOrderOption.GetRewardType() == beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE {
			feExchangerOrderOption.DisplayDetails.AdditionalDetails.IntermediateStateText = "Address confirmation required"
		}
	case isBeExchangerOrderInProgress(orderState):
		feExchangerOrderOption.DisplayDetails.AdditionalDetails.IntermediateStateText = "Processing. Check back later"
	default:
		// don't show any text for other states
	}

	return feExchangerOrderOption
}

func (r *RewardService) getFeOfferMetadata(beOfferMetadata *beCasperPb.OfferMetadata) *fePb.OfferMetadata {
	if beOfferMetadata == nil {
		return nil
	}
	feOfferMetadata := &fePb.OfferMetadata{}

	// Gift card offer metadata
	if beOfferMetadata.GetGiftCardMetadata() != nil {
		feOfferMetadata.OfferTypeSpecificMetadata = &fePb.OfferMetadata_GiftCardMetadata{
			GiftCardMetadata: &fePb.GiftCardOfferMetadata{GiftCardValue: beOfferMetadata.GetGiftCardMetadata().GetGiftCardValue()},
		}
	}
	// Charity offer metadata
	if beOfferMetadata.GetCharityMetadata() != nil {
		feOfferMetadata.OfferTypeSpecificMetadata = &fePb.OfferMetadata_CharityMetadata{
			CharityMetadata: &fePb.CharityOfferMetadata{CharityAmount: beOfferMetadata.GetCharityMetadata().GetCharityAmount()},
		}
	}
	// Coupon offer metadata
	if beOfferMetadata.GetCouponMetadata() != nil {
		feOfferMetadata.OfferTypeSpecificMetadata = &fePb.OfferMetadata_CouponMetadata{
			CouponMetadata: &fePb.CouponOfferMetadata{CouponCodeV2: beOfferMetadata.GetCouponMetadata().GetCouponCodeV2()},
		}
	}

	return feOfferMetadata
}

func (r *RewardService) getFeOfferType(beOfferType beCasperPb.OfferType) fePb.OfferType {
	switch beOfferType {
	case beCasperPb.OfferType_GIFT_CARD:
		return fePb.OfferType_GIFT_CARD
	case beCasperPb.OfferType_CHARITY:
		return fePb.OfferType_CHARITY
	case beCasperPb.OfferType_PHYSICAL_MERCHANDISE:
		return fePb.OfferType_PHYSICAL_MERCHANDISE
	case beCasperPb.OfferType_COUPON:
		return fePb.OfferType_COUPON
	case beCasperPb.OfferType_SUBSCRIPTION:
		return fePb.OfferType_SUBSCRIPTION
	case beCasperPb.OfferType_POWER_UP:
		return fePb.OfferType_POWER_UP
	case beCasperPb.OfferType_THRIWE_BENEFITS_PACKAGE:
		return fePb.OfferType_THRIWE_BENEFITS_PACKAGE
	case beCasperPb.OfferType_CMS_COUPON, beCasperPb.OfferType_LOUNGE_ACCESS, beCasperPb.OfferType_EXTERNAL_VENDOR:
		return fePb.OfferType_DEFAULT_OFFER_TYPE
	case beCasperPb.OfferType_VISTARA_AIR_MILES, beCasperPb.OfferType_CLUB_ITC_GREEN_POINTS:
		return fePb.OfferType_FI_COINS_TO_POINTS_CONVERSION
	default:
		return fePb.OfferType_UNSPECIFIED_OFFER_TYPE
	}
}

func (r *RewardService) getFeOfferImage(beImage *beCasperPb.OfferImage) *fePb.OfferImage {
	if beImage == nil {
		return nil
	}
	return &fePb.OfferImage{
		ImageType: r.getFeOfferImageType(beImage.GetImageType()),
		Url:       beImage.GetUrl(),
	}
}

func (r *RewardService) getFeOfferImageType(imageType beCasperPb.ImageType) fePb.ImageType {
	switch imageType {
	case beCasperPb.ImageType_BACKGROUND_IMAGE:
		return fePb.ImageType_BACKGROUND_IMAGE
	case beCasperPb.ImageType_BRAND_IMAGE:
		return fePb.ImageType_BRAND_IMAGE
	default:
		return fePb.ImageType_UNSPECIFIED_IMAGE_TYPE
	}
}

func (r *RewardService) getFeRedeemedOffer(ctx context.Context, beRedeemedOffer *beRedemptionPb.RedeemedOffer, beOffer *beCasperPb.Offer, appVersion uint32, appPlatform commontypes.Platform) (*fePb.RedeemedOffer, error) {
	// copy redeemed offer details to fe proto

	feRedeemedOfferDetails := &fePb.RedeemedOffer_RedeemedOfferDetails{}
	var redeemedOfferDetailsSections []*fePb.OfferDetailsSection

	feRedeemedOfferProcessingStatus := r.getFeRedeemOfferProcessingStatus(beRedeemedOffer.GetRedemptionState())

	switch beRedeemedOffer.GetOfferType() {

	// populate EGV details
	case beCasperPb.OfferType_GIFT_CARD:
		beEgvDetails := beRedeemedOffer.GetRedeemedOfferDetails().GetEgiftCardDetails()
		feRedeemedOfferDetails.OfferTypeSpecificDetails = &fePb.RedeemedOffer_RedeemedOfferDetails_EgiftCardDetails{
			EgiftCardDetails: &fePb.EGiftCardDetails{
				CardNo:         beEgvDetails.GetCardNo(),
				Pin:            beEgvDetails.GetPin(),
				ActivationCode: beEgvDetails.GetActivationCode(),
				ActivationUrl:  beEgvDetails.GetActivationUrl(),
				ExpiryTime:     beEgvDetails.GetExpiryTime(),
			}}
		if beOffer.GetAdditionalDetails().GetOfferDescription() != nil {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferDescriptionSection(beOffer.GetAdditionalDetails().GetOfferDescription().GetHeading(), beOffer.GetAdditionalDetails().GetOfferDescription().GetPoints()))
		}
		if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_IN_PROGRESS {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferProcessingSection())
		} else if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections,
				r.getCopyCodeSectionForEgv(beEgvDetails.GetActivationCode(), beEgvDetails.GetPin(), beEgvDetails.GetCardNo(), beEgvDetails.GetActivationUrl(), beOffer.GetDesc()),
				r.getOfferPeriodSection(beEgvDetails.GetExpiryTime()),
			)
		}
		redeemedOfferDetailsSections = append(redeemedOfferDetailsSections,
			r.getOfferIdSection(beOffer.GetExternalId()),
			r.getHowToRedeemSection(beOffer.GetAdditionalDetails().GetHowToRedeem(), beOffer.GetTnc().GetTncList()),
		)

	// populate charity details
	case beCasperPb.OfferType_CHARITY:
		_ = beRedeemedOffer.GetRedeemedOfferDetails().GetCharityDetails()
		feRedeemedOfferDetails.OfferTypeSpecificDetails = &fePb.RedeemedOffer_RedeemedOfferDetails_CharityDetails{
			CharityDetails: &fePb.CharityDetails{},
		}
		if beOffer.GetAdditionalDetails().GetOfferDescription() != nil {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferDescriptionSection(beOffer.GetAdditionalDetails().GetOfferDescription().GetHeading(), beOffer.GetAdditionalDetails().GetOfferDescription().GetPoints()))
		}
		redeemedOfferDetailsSections = append(redeemedOfferDetailsSections,
			r.getOfferIdSection(beOffer.GetExternalId()),
			r.getHowToRedeemSection(beOffer.GetAdditionalDetails().GetHowToRedeem(), beOffer.GetTnc().GetTncList()),
		)

	// populate physical merchandise details
	case beCasperPb.OfferType_PHYSICAL_MERCHANDISE:
		bePhysicalMerchandiseDetails := beRedeemedOffer.GetRedeemedOfferDetails().GetPhysicalMerchandiseDetails()
		feRedeemedOfferDetails.OfferTypeSpecificDetails = &fePb.RedeemedOffer_RedeemedOfferDetails_PhysicalMerchandiseDetails{
			PhysicalMerchandiseDetails: &fePb.PhysicalMerchandiseDetails{
				ShippingAddress: convertToClientPostalAddressType(bePhysicalMerchandiseDetails.GetShippingAddress()),
			},
		}
		if beOffer.GetAdditionalDetails().GetOfferDescription() != nil {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferDescriptionSection(beOffer.GetAdditionalDetails().GetOfferDescription().GetHeading(), beOffer.GetAdditionalDetails().GetOfferDescription().GetPoints()))
		}
		redeemedOfferDetailsSections = append(redeemedOfferDetailsSections,
			r.getDeliveryDetailsSection(bePhysicalMerchandiseDetails.GetShippingAddress()),
			r.getOfferIdSection(beOffer.GetExternalId()),
			r.getNextStepsSection(beOffer.GetAdditionalDetails().GetNextSteps(), beOffer.GetTnc().GetTncList()),
		)

	case beCasperPb.OfferType_POWER_UP:
		bePowerUpDetails := beRedeemedOffer.GetRedeemedOfferDetails().GetPowerUpDetails()
		// though empty, we need to pass this as client expects the oneof field `OfferTypeSpecificDetails` to contain a value
		feRedeemedOfferDetails.OfferTypeSpecificDetails = &fePb.RedeemedOffer_RedeemedOfferDetails_PowerUpDetails{
			PowerUpDetails: &fePb.PowerUpDetails{
				// todo: add as per requirements
			},
		}

		// propagate the extra details via the dynamic fields
		for _, extraDetail := range bePowerUpDetails.GetExtraDetails() {
			feRedeemedOfferDetails.DynamicFields = append(feRedeemedOfferDetails.DynamicFields,
				&fePb.RedeemedOffer_RedeemedOfferDetails_DynamicField{Name: extraDetail.GetName(), Value: extraDetail.GetValue(), IsSharable: true},
			)
		}

		// todo (sresth) : migrate to use the new offer description field
		if beOffer.GetDesc() != "" {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferDescriptionSection(beOffer.GetDesc(), nil))
		}
		if len(bePowerUpDetails.GetExtraDetails()) != 0 {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getCopyCodeSectionForPowerUp(bePowerUpDetails.GetExtraDetails()))
		}
		redeemedOfferDetailsSections = append(redeemedOfferDetailsSections,
			r.getOfferIdSection(beOffer.GetExternalId()),
			r.getHowToRedeemSection(beOffer.GetAdditionalDetails().GetHowToRedeem(), beOffer.GetTnc().GetTncList()),
		)

	case beCasperPb.OfferType_THRIWE_BENEFITS_PACKAGE:
		beThriweBenefitsPackageDetails := beRedeemedOffer.GetRedeemedOfferDetails().GetThriweBenefitsPackageDetails()

		feRedeemedOfferDetails.OfferTypeSpecificDetails = &fePb.RedeemedOffer_RedeemedOfferDetails_ThriweBenefitsPackageDetails{
			ThriweBenefitsPackageDetails: &fePb.ThriweBenefitsPackageDetails{
				ExpiryTime: beThriweBenefitsPackageDetails.GetExpiryTime(),
			},
		}

		if beOffer.GetAdditionalDetails().GetOfferDescription() != nil {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferDescriptionSection(beOffer.GetAdditionalDetails().GetOfferDescription().GetHeading(), beOffer.GetAdditionalDetails().GetOfferDescription().GetPoints()))
		}

		if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_IN_PROGRESS {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferProcessingSection())
		} else if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS {
			// activate benefits package section
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, &fePb.OfferDetailsSection{
				Infos: []*fePb.OfferDetailsSection_Info{
					{
						Data: &fePb.OfferDetailsSection_Info_ImageAndTextInfo{
							ImageAndTextInfo: &fePb.ImageAndTextInfo{
								Text: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Activate your reward benefits here"}, FontColor: "#333333"},
							},
						},
					},
				},
				Cta: &fePb.CtaV1{
					CtaTitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Activate"}, FontColor: "#FFFFFF"},
					Action: &fePb.CtaV1_CustomAction{
						CustomAction: &fePb.CustomAction{
							ActionType: fePb.CustomAction_MAKE_API_CALL,
							ActionApi:  fePb.CustomActionApi_GET_REDEEMED_OFFER_DETAILS_REDIRECTION_INFO,
						},
					},
					BgColor:     "#00B899",
					CtaPosition: fePb.CtaV1_RIGHT,
				},
				BgColor: "#FFFFFF",
			})

			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferPeriodSection(beThriweBenefitsPackageDetails.GetExpiryTime()))
		}
		redeemedOfferDetailsSections = append(redeemedOfferDetailsSections,
			r.getOfferIdSection(beOffer.GetExternalId()),
			r.getHowToRedeemSection(beOffer.GetAdditionalDetails().GetHowToRedeem(), beOffer.GetTnc().GetTncList()),
		)

	case beCasperPb.OfferType_VISTARA_AIR_MILES:
		vistaraAirMilesDetails := beRedeemedOffer.GetRedeemedOfferDetails().GetVistaraAirMilesDetails()

		feRedeemedOfferDetails.OfferTypeSpecificDetails = &fePb.RedeemedOffer_RedeemedOfferDetails_VistaraAirMilesDetails{
			VistaraAirMilesDetails: &fePb.VistaraAirMilesDetails{
				ActivationUrl: vistaraAirMilesDetails.GetActivationUrl(),
				ExpiryTime:    vistaraAirMilesDetails.GetExpiryTime(),
				CvPoints:      vistaraAirMilesDetails.GetCvPoints(),
			},
		}

		if beOffer.GetAdditionalDetails().GetOfferDescription() != nil {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferDescriptionSection(beOffer.GetAdditionalDetails().GetOfferDescription().GetHeading(), beOffer.GetAdditionalDetails().GetOfferDescription().GetPoints()))
		}
		if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_IN_PROGRESS || feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_PENDING_UPDATE {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferProcessingSection())
		} else if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getActivationLinkSection(vistaraAirMilesDetails.GetActivationUrl()), r.getOfferPeriodSection(vistaraAirMilesDetails.GetExpiryTime()))
		}
		redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferIdSection(beOffer.GetExternalId()), r.getHowToRedeemSection(beOffer.GetAdditionalDetails().GetHowToRedeem(), beOffer.GetTnc().GetTncList()))

	case beCasperPb.OfferType_CLUB_ITC_GREEN_POINTS:
		clubItcGreenPointsDetails := beRedeemedOffer.GetRedeemedOfferDetails().GetClubItcGreenPointsDetails()
		if beOffer.GetAdditionalDetails().GetOfferDescription() != nil {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferDescriptionSection(beOffer.GetAdditionalDetails().GetOfferDescription().GetHeading(), beOffer.GetAdditionalDetails().GetOfferDescription().GetPoints()))
		}
		if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_IN_PROGRESS || feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_PENDING_UPDATE {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferProcessingSection())
		} else if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getActivationLinkSection(clubItcGreenPointsDetails.GetActivationUrl()))
		}
		redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferIdSection(beOffer.GetExternalId()), r.getHowToRedeemSection(beOffer.GetAdditionalDetails().GetHowToRedeem(), beOffer.GetTnc().GetTncList()))

	case beCasperPb.OfferType_CMS_COUPON:
		beCmsCouponDetails := beRedeemedOffer.GetRedeemedOfferDetails().GetCmsCouponDetails()
		feRedeemedOfferDetails.DynamicFields = r.getFeDynamicFieldsFromBeCmsCouponDetails(beCmsCouponDetails)
		if beOffer.GetAdditionalDetails().GetOfferDescription() != nil {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferDescriptionSection(beOffer.GetAdditionalDetails().GetOfferDescription().GetHeading(), beOffer.GetAdditionalDetails().GetOfferDescription().GetPoints()))
		}
		if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_IN_PROGRESS {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferProcessingSection())
		} else if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections,
				r.getCopyCodeSectionForCmsCoupon(beCmsCouponDetails, beOffer.GetDesc()),
				r.getOfferPeriodSection(beCmsCouponDetails.GetValidTill()),
			)
		}
		redeemedOfferDetailsSections = append(redeemedOfferDetailsSections,
			r.getOfferIdSection(beOffer.GetExternalId()),
			r.getHowToRedeemSection(beOffer.GetAdditionalDetails().GetHowToRedeem(), beOffer.GetTnc().GetTncList()),
		)

	case beCasperPb.OfferType_LOUNGE_ACCESS:
		beLoungeAccessDetails := beRedeemedOffer.GetRedeemedOfferDetails().GetLoungeAccessDetails()
		if beOffer.GetAdditionalDetails().GetOfferDescription() != nil {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferDescriptionSection(beOffer.GetAdditionalDetails().GetOfferDescription().GetHeading(), beOffer.GetAdditionalDetails().GetOfferDescription().GetPoints()))
		}
		if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_IN_PROGRESS {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferProcessingSection())
		} else if feRedeemedOfferProcessingStatus == fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS {
			redeemedOfferDetailsSections = append(redeemedOfferDetailsSections,
				r.getVoucherDetailsSectionForLoungeAccess(beLoungeAccessDetails),
				r.getOfferPeriodSection(beLoungeAccessDetails.GetExpiryTime()),
			)
		}
		redeemedOfferDetailsSections = append(redeemedOfferDetailsSections,
			r.getOfferIdSection(beOffer.GetExternalId()),
			r.getHowToRedeemSection(beOffer.GetAdditionalDetails().GetHowToRedeem(), beOffer.GetTnc().GetTncList()),
		)

	default:
		// adding common sections for old offer types
		redeemedOfferDetailsSections = append(redeemedOfferDetailsSections, r.getOfferIdSection(beOffer.GetExternalId()))

	}

	getOfferInventoryByOfferIdsResponse, err := r.offerInventoryService.GetOfferInventoryByOfferIds(ctx,
		&beCasperPb.GetOfferInventoryByOfferIdsRequest{
			OfferIds: []string{beOffer.GetId()},
		},
	)
	if err != nil || !getOfferInventoryByOfferIdsResponse.Status.IsSuccess() {
		logger.Error(ctx, "GetOfferInventoryByOfferIds call failed", zap.Error(err), zap.Any("res", getOfferInventoryByOfferIdsResponse))
		return nil, errors.New("GetOfferInventoryByOfferIds call failed in getFeRedeemedOffer")
	}

	actorId := beRedeemedOffer.GetActorId()
	fiCoinBalance, err := r.getFiCoinsBalanceForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "getFiCoinsBalanceForActor call failed", zap.Error(err))
		return nil, errors.New("getFiCoinsBalanceForActor call failed in GetOfferDetailsByIdRequest")
	}

	// passing `isFullSalaryProgramActiveForActor` as false as there is no dependency on it for this flow
	// passing 'offerIdToAvailableUserLevelInventoryMap' as nil as there is no dependency on it for this flow
	// passing `isMonthlyMaxCapHit` as false as it's irrelevant for this flow
	// todo(rohan): pass valid salary program status if we need to show salary program specific data for redeemed offers
	feOffer, err := r.getFeOffer(ctx, beOffer, nil, nil, false, appVersion, appPlatform, tags.RenderLocationCollectedOffersScreen, getOfferInventoryByOfferIdsResponse.GetOfferIdToOfferInventoryMap(), fiCoinBalance, nil, false)
	if err != nil {
		return nil, err
	}

	var feDisplayTags []*commontypes.Text
	for _, beDisplayTag := range beOffer.GetAdditionalDetails().GetDisplayTags() {
		feDisplayTags = append(feDisplayTags, &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: beDisplayTag.GetText()},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_3},
			FontColor:    beDisplayTag.GetTextColor(),
			BgColor:      beDisplayTag.GetBgColor(),
		})
	}

	shouldNotDisplayRedemptionPrice := false
	// if redemption mode was INTERNAL then redemption price shouldn't be displayed.
	if beOffer.GetRedemptionMode() == beCasperPb.OfferRedemptionMode_INTERNAL {
		shouldNotDisplayRedemptionPrice = true
	}

	return &fePb.RedeemedOffer{
		Id:                              beRedeemedOffer.GetId(),
		ExternalId:                      beRedeemedOffer.GetExternalId(),
		Offer:                           feOffer,
		ProcessingStatus:                r.getFeRedeemOfferProcessingStatus(beRedeemedOffer.GetRedemptionState()),
		RedeemedOfferDetails:            feRedeemedOfferDetails,
		CreatedAt:                       beRedeemedOffer.GetCreatedAt(),
		RedemptionPrice:                 uint32(beRedeemedOffer.GetRedemptionPrice()),
		RedeemedOfferDetailsSections:    redeemedOfferDetailsSections,
		DisplayTags:                     feDisplayTags,
		ShouldNotDisplayRedemptionPrice: shouldNotDisplayRedemptionPrice,
	}, nil
}

func (r *RewardService) getFeDynamicFieldsFromBeCmsCouponDetails(beCmsCouponDetails *beCasperPb.CmsCouponDetails) []*fePb.RedeemedOffer_RedeemedOfferDetails_DynamicField {
	var feDynamicFields []*fePb.RedeemedOffer_RedeemedOfferDetails_DynamicField
	for _, beKeyValuePair := range beCmsCouponDetails.GetKeyValuePairs() {
		feDynamicFields = append(feDynamicFields, &fePb.RedeemedOffer_RedeemedOfferDetails_DynamicField{
			Name:       beKeyValuePair.GetKey(),
			Value:      beKeyValuePair.GetValue(),
			IsSharable: true,
		})
	}
	return feDynamicFields
}

// if offerDescriptionHeading and offerDescriptionPoints exists then returns OfferDetailsSection with pointsInfo containing points with heading
// if only offerDescriptionPoints exists then returns OfferDetailsSection with pointsInfo containing only points without heading
// if only offerDescriptionHeading exists then returns OfferDetailsSection with pointsInfo containing only heading
func (r *RewardService) getOfferDescriptionSection(offerDescriptionHeading string, offerDescriptionPoints []string) *fePb.OfferDetailsSection {
	var points []*commontypes.Text
	var heading *commontypes.Text
	if offerDescriptionPoints != nil {
		for _, offerDescriptionPoint := range offerDescriptionPoints {
			points = append(points, r.getText("#646464", "#FFFFFF", offerDescriptionPoint, commontypes.FontStyle_BODY_S))
		}
	}
	if offerDescriptionHeading != "" {
		heading = r.getText("#646464", "#FFFFFF", offerDescriptionHeading, commontypes.FontStyle_BODY_S)
	}

	offerDescriptionSection := &fePb.OfferDetailsSection{
		Infos: []*fePb.OfferDetailsSection_Info{
			{
				Data: &fePb.OfferDetailsSection_Info_PointsInfo{
					PointsInfo: &fePb.PointsInfo{
						Heading:    heading,
						Points:     points,
						PointsType: fePb.PointsType_BULLET_POINTS,
					},
				},
			},
		},
		BgColor: "#FFFFFF",
	}

	return offerDescriptionSection
}

func (r *RewardService) getDeliveryDetailsSection(shippingAddress *postaladdress.PostalAddress) *fePb.OfferDetailsSection {
	deliveryDetailsSection := &fePb.OfferDetailsSection{
		Header: &fePb.OfferDetailsSection_Header{
			Title: r.getText("#333333", "#F7F9FC", "Delivery Details", commontypes.FontStyle_SUBTITLE_S),
		},
		Infos: []*fePb.OfferDetailsSection_Info{
			{
				Data: &fePb.OfferDetailsSection_Info_KeyValueInfo{
					KeyValueInfo: &fePb.KeyValueInfo{
						Key: r.getText("#A4A4A4", "#FFFFFF", "DELIVERY ADDRESS", commontypes.FontStyle_OVERLINE_XS_CAPS),
						// todo(sresth) remove the deprecated value field
						Value:   r.getText("#646464", "#FFFFFF", address.ConvertPostalAddressToString(shippingAddress), commontypes.FontStyle_BODY_S),
						ValueV1: r.getText("#646464", "#FFFFFF", address.ConvertPostalAddressToString(shippingAddress), commontypes.FontStyle_BODY_S),
					},
				},
			},
		},
		BgColor: "#FFFFFF",
	}

	return deliveryDetailsSection
}

func (r *RewardService) getOfferIdSection(offerId string) *fePb.OfferDetailsSection {

	offerIdSection := &fePb.OfferDetailsSection{
		Header: &fePb.OfferDetailsSection_Header{
			Title: r.getText("#333333", "#F7F9FC", "Offer Id", commontypes.FontStyle_SUBTITLE_S),
		},
		Infos: []*fePb.OfferDetailsSection_Info{
			{
				Data: &fePb.OfferDetailsSection_Info_ImageAndTextInfo{
					ImageAndTextInfo: &fePb.ImageAndTextInfo{
						Text: r.getText("#646464", "#FFFFFF", offerId, commontypes.FontStyle_BODY_S),
					},
				},
			},
		},
		BgColor: "#FFFFFF",
	}

	return offerIdSection
}

func (r *RewardService) getHowToRedeemSection(howToRedeemList []string, tncList []string) *fePb.OfferDetailsSection {
	var howToRedeemPointsInfo []*commontypes.Text
	for _, howToRedeem := range howToRedeemList {
		howToRedeemPointInfo := r.getHtmlText("#646464", "#FFFFFF", howToRedeem, commontypes.FontStyle_BODY_S)
		howToRedeemPointsInfo = append(howToRedeemPointsInfo, howToRedeemPointInfo)
	}

	howToRedeemSection := &fePb.OfferDetailsSection{
		Header: &fePb.OfferDetailsSection_Header{
			Title: r.getText("#333333", "#F7F9FC", "How to redeem", commontypes.FontStyle_SUBTITLE_S),
			Cta:   r.getViewTncsCta(tncList),
		},
		Infos: []*fePb.OfferDetailsSection_Info{
			{
				Data: &fePb.OfferDetailsSection_Info_PointsInfo{
					PointsInfo: &fePb.PointsInfo{
						Points:     howToRedeemPointsInfo,
						PointsType: fePb.PointsType_NUMBERED_POINTS,
					},
				},
			},
		},
		BgColor: "#FFFFFF",
	}

	return howToRedeemSection
}

func (r *RewardService) getCopyCodeSectionForEgv(egiftCardActivationCode string, egiftCardPin string, egiftCardNo string, egiftCardActivationUrl string, offerDetails string) *fePb.OfferDetailsSection {

	var egiftCardInfoSections []*fePb.OfferDetailsSection_Info
	if egiftCardActivationCode != "" {
		activationCodeKeyValueInfo := &fePb.KeyValueInfo{
			Key: r.getText("#A4A4A4", "#FFFFFF", "CODE", commontypes.FontStyle_OVERLINE_XS_CAPS),
			// todo(sresth) remove the deprecated value field
			Value:           r.getText("#646464", "#FFFFFF", egiftCardActivationCode, commontypes.FontStyle_SUBTITLE_M),
			ValueV1:         r.getText("#646464", "#FFFFFF", egiftCardActivationCode, commontypes.FontStyle_SUBTITLE_M),
			IsValueCopyable: true,
		}
		egiftCardInfoSections = append(egiftCardInfoSections, &fePb.OfferDetailsSection_Info{
			Data: &fePb.OfferDetailsSection_Info_KeyValueInfo{
				KeyValueInfo: activationCodeKeyValueInfo,
			},
		})
	}

	if egiftCardPin != "" {
		pinKeyValueInfo := &fePb.KeyValueInfo{
			Key: r.getText("#A4A4A4", "#FFFFFF", "PIN", commontypes.FontStyle_OVERLINE_XS_CAPS),
			// todo(sresth) remove the deprecated value field
			Value:           r.getText("#646464", "#FFFFFF", egiftCardPin, commontypes.FontStyle_SUBTITLE_M),
			ValueV1:         r.getText("#646464", "#FFFFFF", egiftCardPin, commontypes.FontStyle_SUBTITLE_M),
			IsValueCopyable: true,
		}
		egiftCardInfoSections = append(egiftCardInfoSections, &fePb.OfferDetailsSection_Info{
			Data: &fePb.OfferDetailsSection_Info_KeyValueInfo{
				KeyValueInfo: pinKeyValueInfo,
			},
		})
	}

	if egiftCardNo != "" {
		CardNoKeyValueInfo := &fePb.KeyValueInfo{
			Key: r.getText("#A4A4A4", "#FFFFFF", "CARD NUMBER", commontypes.FontStyle_OVERLINE_XS_CAPS),
			// todo(sresth) remove the deprecated value field
			Value:           r.getText("#646464", "#FFFFFF", egiftCardNo, commontypes.FontStyle_SUBTITLE_M),
			ValueV1:         r.getText("#646464", "#FFFFFF", egiftCardNo, commontypes.FontStyle_SUBTITLE_M),
			IsValueCopyable: true,
		}
		egiftCardInfoSections = append(egiftCardInfoSections, &fePb.OfferDetailsSection_Info{
			Data: &fePb.OfferDetailsSection_Info_KeyValueInfo{
				KeyValueInfo: CardNoKeyValueInfo,
			},
		})
	}

	if egiftCardActivationUrl != "" {
		activationUrlKeyValueInfo := &fePb.KeyValueInfo{
			Key: r.getText("#A4A4A4", "#FFFFFF", "ACTIVATION LINK", commontypes.FontStyle_OVERLINE_XS_CAPS),
			// todo(sresth) remove the deprecated value field
			Value:   r.getText("#00B899", "#FFFFFF", egiftCardActivationUrl, commontypes.FontStyle_SUBTITLE_S),
			ValueV1: r.getText("#00B899", "#FFFFFF", egiftCardActivationUrl, commontypes.FontStyle_SUBTITLE_S),
		}
		egiftCardInfoSections = append(egiftCardInfoSections, &fePb.OfferDetailsSection_Info{
			Data: &fePb.OfferDetailsSection_Info_KeyValueInfo{
				KeyValueInfo: activationUrlKeyValueInfo,
			},
		})
	}

	// creating script for cta
	script := fmt.Sprintf("Hey, here's a cool %s that I redeemed on Fi which might be useful for you. Enjoy!\n", offerDetails)
	if egiftCardActivationCode != "" {
		script += "Card Activation Code: " + egiftCardActivationCode + "\n"
	}
	if egiftCardPin != "" {
		script += "Card Pin: " + egiftCardPin + "\n"
	}
	if egiftCardNo != "" {
		script += "Card No: " + egiftCardNo + "\n"
	}
	if egiftCardActivationUrl != "" {
		script += "Card Activation Url: " + egiftCardActivationUrl + "\n"
	}

	copyCodeSection := &fePb.OfferDetailsSection{
		Infos: egiftCardInfoSections,
		Cta: &fePb.CtaV1{
			CtaTitle:    r.getText("#00B899", "#F7F9FC", "Share code as gift", commontypes.FontStyle_BUTTON_M),
			LeftIconUrl: "https://epifi-icons.pointz.in/rewards/share-code-as-gift-icon.png",
			Action: &fePb.CtaV1_CustomAction{
				CustomAction: &fePb.CustomAction{
					ActionType: fePb.CustomAction_SHARE_COUPON_CODE,
					ActionData: &fePb.CustomAction_ShareCouponCodeActionData_{
						ShareCouponCodeActionData: &fePb.CustomAction_ShareCouponCodeActionData{
							Script: script,
						},
					},
				},
			},
			BgColor: "#F7F9FC",
		},
		BgColor: "#FFFFFF",
	}

	return copyCodeSection
}

func (r *RewardService) getCopyCodeSectionForCmsCoupon(cmsCouponDetails *beCasperPb.CmsCouponDetails, offerDetails string) *fePb.OfferDetailsSection {

	var cmsCouponInfoSections []*fePb.OfferDetailsSection_Info

	for _, keyValuePair := range cmsCouponDetails.GetKeyValuePairs() {
		keyValueInfo := &fePb.KeyValueInfo{
			Key: r.getText("#A4A4A4", "#FFFFFF", keyValuePair.GetKey(), commontypes.FontStyle_OVERLINE_XS_CAPS),
			// todo(sresth) remove the deprecated value field
			Value:           r.getText("#646464", "#FFFFFF", keyValuePair.GetValue(), commontypes.FontStyle_SUBTITLE_M),
			ValueV1:         r.getText("#646464", "#FFFFFF", keyValuePair.GetValue(), commontypes.FontStyle_SUBTITLE_M),
			IsValueCopyable: true,
		}
		cmsCouponInfoSections = append(cmsCouponInfoSections, &fePb.OfferDetailsSection_Info{
			Data: &fePb.OfferDetailsSection_Info_KeyValueInfo{
				KeyValueInfo: keyValueInfo,
			},
		})
	}

	// creating script for cta
	script := fmt.Sprintf("Hey, here's a cool %s that I redeemed on Fi which might be useful for you. Enjoy!\n", offerDetails)
	for _, keyValuePair := range cmsCouponDetails.GetKeyValuePairs() {
		script += keyValuePair.GetKey() + ": " + keyValuePair.GetValue() + "\n"
	}

	copyCodeSection := &fePb.OfferDetailsSection{
		Infos: cmsCouponInfoSections,
		Cta: &fePb.CtaV1{
			CtaTitle:    r.getText("#00B899", "#F7F9FC", "Share code as gift", commontypes.FontStyle_BUTTON_M),
			LeftIconUrl: "https://epifi-icons.pointz.in/rewards/share-code-as-gift-icon.png",
			Action: &fePb.CtaV1_CustomAction{
				CustomAction: &fePb.CustomAction{
					ActionType: fePb.CustomAction_SHARE_COUPON_CODE,
					ActionData: &fePb.CustomAction_ShareCouponCodeActionData_{
						ShareCouponCodeActionData: &fePb.CustomAction_ShareCouponCodeActionData{
							Script: script,
						},
					},
				},
			},
			BgColor: "#F7F9FC",
		},
		BgColor: "#FFFFFF",
	}

	return copyCodeSection
}

func (r *RewardService) getVoucherDetailsSectionForLoungeAccess(loungeAccessDetails *beCasperPb.LoungeAccessDetails) *fePb.OfferDetailsSection {
	var loungeVoucherInfoSections []*fePb.OfferDetailsSection_Info

	voucherCodeInfo := &fePb.KeyValueInfo{
		Key:             r.getText("#A4A4A4", "#FFFFFF", "Voucher Code", commontypes.FontStyle_OVERLINE_XS_CAPS),
		ValueV1:         r.getText("#646464", "#FFFFFF", loungeAccessDetails.GetVoucherCode(), commontypes.FontStyle_SUBTITLE_M),
		IsValueCopyable: true,
	}

	loungeVoucherInfoSections = append(loungeVoucherInfoSections,
		&fePb.OfferDetailsSection_Info{Data: &fePb.OfferDetailsSection_Info_KeyValueInfo{KeyValueInfo: voucherCodeInfo}},
	)

	return &fePb.OfferDetailsSection{
		Infos:   loungeVoucherInfoSections,
		BgColor: "#FFFFFF",
		Cta: &fePb.CtaV1{
			CtaTitle: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "Download Voucher",
				},
			},
			Action: &fePb.CtaV1_DeeplinkAction{
				DeeplinkAction: &deepLinkPb.Deeplink{
					Screen: deepLinkPb.Screen_EXTERNAL_REDIRECTION,
					ScreenOptions: &deepLinkPb.Deeplink_ExternalRedirectionScreenOptions{
						ExternalRedirectionScreenOptions: &deepLinkPb.ExternalRedirectionScreenOptions{ExternalUrl: loungeAccessDetails.GetVoucherQrUrl()},
					},
				},
			},
		},
	}
}

func (r *RewardService) getActivationLinkSection(activationUrl string) *fePb.OfferDetailsSection {
	return &fePb.OfferDetailsSection{
		Infos: []*fePb.OfferDetailsSection_Info{
			{
				Data: &fePb.OfferDetailsSection_Info_KeyValueInfo{
					KeyValueInfo: &fePb.KeyValueInfo{
						Key: r.getText("#A4A4A4", "#FFFFFF", "ACTIVATION LINK", commontypes.FontStyle_OVERLINE_XS_CAPS),
						// todo(sresth) remove the deprecated value field
						Value:   r.getText("#00B899", "#FFFFFF", activationUrl, commontypes.FontStyle_SUBTITLE_S),
						ValueV1: r.getText("#00B899", "#FFFFFF", activationUrl, commontypes.FontStyle_SUBTITLE_S),
					},
				},
			},
		},
		BgColor: "#FFFFFF",
	}
}

func (r *RewardService) getOfferPeriodSection(offerValidTill *timestamppb.Timestamp) *fePb.OfferDetailsSection {

	offerPeriodSection := &fePb.OfferDetailsSection{
		Header: &fePb.OfferDetailsSection_Header{
			Title: r.getText("#333333", "#F7F9FC", "Offer Period", commontypes.FontStyle_SUBTITLE_S),
		},
		Infos: []*fePb.OfferDetailsSection_Info{
			{
				Data: &fePb.OfferDetailsSection_Info_ImageAndTextInfo{
					ImageAndTextInfo: &fePb.ImageAndTextInfo{
						Text: r.getText("#646464", "#FFFFFF", fmt.Sprintf("Valid till %s", offerValidTill.AsTime().Format("January 2, 2006")), commontypes.FontStyle_BODY_S),
					},
				},
			},
		},
		BgColor: "#FFFFFF",
	}

	return offerPeriodSection
}

func (r *RewardService) getOfferProcessingSection() *fePb.OfferDetailsSection {
	offerProcessingSection := &fePb.OfferDetailsSection{
		Infos: []*fePb.OfferDetailsSection_Info{
			{
				Data: &fePb.OfferDetailsSection_Info_ImageAndTextInfo{
					ImageAndTextInfo: &fePb.ImageAndTextInfo{
						Image: &commontypes.Image{
							ImageType: commontypes.ImageType_PNG,
							ImageUrl:  "https://epifi-icons.pointz.in/rewards/loading-icon.png",
						},
						// todo (utkarsh) : check if we can make this processing text generic for multiple offer types ?
						Text: r.getText("#333333", "#F7F9FC", "Offer processing. This should be completed within 72 hours.", commontypes.FontStyle_SUBTITLE_S),
					},
				},
			},
		},
		BgColor: "#F7F9FC",
	}

	return offerProcessingSection
}

func (r *RewardService) getCopyCodeSectionForPowerUp(powerUpExtraDetails []*beCasperPb.OfferKeyValueMetadata) *fePb.OfferDetailsSection {
	var powerUpInfoSections []*fePb.OfferDetailsSection_Info
	for _, powerUpExtraDetail := range powerUpExtraDetails {
		keyValueInfo := &fePb.KeyValueInfo{
			Key: r.getText("#A4A4A4", "#FFFFFF", powerUpExtraDetail.GetName(), commontypes.FontStyle_OVERLINE_XS_CAPS),
			// todo(sresth) remove the deprecated value field
			Value:           r.getText("#646464", "#FFFFFF", powerUpExtraDetail.GetValue(), commontypes.FontStyle_SUBTITLE_S),
			ValueV1:         r.getText("#646464", "#FFFFFF", powerUpExtraDetail.GetValue(), commontypes.FontStyle_SUBTITLE_S),
			IsValueCopyable: true,
		}
		powerUpInfoSections = append(powerUpInfoSections, &fePb.OfferDetailsSection_Info{
			Data: &fePb.OfferDetailsSection_Info_KeyValueInfo{
				KeyValueInfo: keyValueInfo,
			},
		})
	}

	copyCodeSection := &fePb.OfferDetailsSection{
		Infos:   powerUpInfoSections,
		BgColor: "#FFFFFF",
	}

	return copyCodeSection
}

func (r *RewardService) getNextStepsSection(nextStepsList []string, tncList []string) *fePb.OfferDetailsSection {
	var howToRedeemPointsInfo []*commontypes.Text
	for _, nextSteps := range nextStepsList {
		howToRedeemPointInfo := r.getText("#646464", "#FFFFFF", nextSteps, commontypes.FontStyle_BODY_S)
		howToRedeemPointsInfo = append(howToRedeemPointsInfo, howToRedeemPointInfo)
	}

	howToRedeemSection := &fePb.OfferDetailsSection{
		Header: &fePb.OfferDetailsSection_Header{
			Title: r.getText("#333333", "#F7F9FC", "Next steps", commontypes.FontStyle_SUBTITLE_S),
			Cta:   r.getViewTncsCta(tncList),
		},
		Infos: []*fePb.OfferDetailsSection_Info{
			{
				Data: &fePb.OfferDetailsSection_Info_PointsInfo{
					PointsInfo: &fePb.PointsInfo{
						Points:     howToRedeemPointsInfo,
						PointsType: fePb.PointsType_NUMBERED_POINTS,
					},
				},
			},
		},
		BgColor: "#FFFFFF",
	}

	return howToRedeemSection
}

func (r *RewardService) getViewTncsCta(tncList []string) *fePb.CtaV1 {
	tncsPointsInfo := r.getFormattedCatalogOfferTncs(tncList)

	tncsCta := &fePb.CtaV1{
		CtaTitle:     r.getText("#646464", "#F7F9FC", "VIEW TnCs", commontypes.FontStyle_BUTTON_XS),
		RightIconUrl: "https://epifi-icons.pointz.in/rewards/view-tncs-right-arrow",
		Action: &fePb.CtaV1_CustomAction{
			CustomAction: &fePb.CustomAction{
				ActionType: fePb.CustomAction_VIEW_TNC,
				ActionData: &fePb.CustomAction_ViewTncActionData_{
					ViewTncActionData: &fePb.CustomAction_ViewTncActionData{
						Title: r.getText("#333333", "#F7F9FC", "Important information", commontypes.FontStyle_SUBTITLE_S),
						Info: &fePb.PointsInfo{
							Points:     tncsPointsInfo,
							PointsType: fePb.PointsType_BULLET_POINTS,
						},
					},
				},
			},
		},
		BgColor: "#F7F9FC",
	}

	return tncsCta
}

func (r *RewardService) getText(fontColor string, bgColor string, plainString string, standardFontStyle commontypes.FontStyle) *commontypes.Text {
	text := &commontypes.Text{
		FontColor: fontColor,
		BgColor:   bgColor,
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: plainString,
		},
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: standardFontStyle,
		},
	}

	return text
}

func (r *RewardService) getHtmlText(fontColor string, bgColor string, plainString string, standardFontStyle commontypes.FontStyle) *commontypes.Text {
	text := &commontypes.Text{
		FontColor: fontColor,
		BgColor:   bgColor,
		DisplayValue: &commontypes.Text_Html{
			Html: plainString,
		},
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: standardFontStyle,
		},
	}

	return text
}

func (r *RewardService) getFeRedeemOfferProcessingStatus(state beRedemptionPb.OfferRedemptionState) fePb.RedeemedOfferProcessingStatus {
	switch {
	case state == beRedemptionPb.OfferRedemptionState_OFFER_REDEMPTION_SUCCESSFUL:
		return fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_SUCCESS
	case state == beRedemptionPb.OfferRedemptionState_OFFER_REDEMPTION_FAILED:
		return fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_FAILED
	case state == beRedemptionPb.OfferRedemptionState_VENDOR_REDEMPTION_PENDING_UPDATE:
		return fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_PENDING_UPDATE
	case beRedemptionPb.IsRedemptionStatePresentInList(state, r.getOfferRedemptionInProgressStates()):
		return fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_IN_PROGRESS
	default:
		return fePb.RedeemedOfferProcessingStatus_RedeemedOfferProcessingStatus_UNSPECIFIED
	}
}

func (r *RewardService) getOfferRedemptionInProgressStates() []beRedemptionPb.OfferRedemptionState {
	return []beRedemptionPb.OfferRedemptionState{
		beRedemptionPb.OfferRedemptionState_OFFER_REDEMPTION_INITIATED,
		beRedemptionPb.OfferRedemptionState_PURCHASED_OFFER_FROM_INVENTORY,
		beRedemptionPb.OfferRedemptionState_PURCHASE_FROM_INVENTORY_FAILED,
		beRedemptionPb.OfferRedemptionState_AMOUNT_DEBITED_FROM_ACCOUNT,
		beRedemptionPb.OfferRedemptionState_AMOUNT_DEBIT_FAILED,
		beRedemptionPb.OfferRedemptionState_VENDOR_REDEMPTION_SUCCESSFUL,
		beRedemptionPb.OfferRedemptionState_VENDOR_REDEMPTION_FAILED,
		beRedemptionPb.OfferRedemptionState_DEBITED_AMOUNT_REVERSED,
		beRedemptionPb.OfferRedemptionState_PURCHASE_FROM_INVENTORY_RETURNED,
		beRedemptionPb.OfferRedemptionState_VENDOR_REDEMPTION_MANUAL_INTERVENTION,
	}
}

func (r *RewardService) getBeRedemptionRequestMetadata(feMetadata *fePb.InitiateRedemptionRequest_RedemptionRequestMetadata) (*beRedemptionPb.RedemptionRequestMetadata, error) {
	if feMetadata == nil {
		return nil, nil
	}
	beRedemptionRequestMetadata := &beRedemptionPb.RedemptionRequestMetadata{
		FiCoins:         feMetadata.GetFiCoins(),
		ShippingAddress: convertToGooglePostalAddressType(feMetadata.GetShippingAddress()),
	}

	if feMetadata.OfferTypeSpecificMetadata != nil {
		switch feMetadataConcreteType := feMetadata.OfferTypeSpecificMetadata.(type) {
		case *fePb.InitiateRedemptionRequest_RedemptionRequestMetadata_VistaraAirMilesRedemptionMetadata_:
			beRedemptionRequestMetadata.OfferTypeSpecificMetadata = &beRedemptionPb.RedemptionRequestMetadata_VistaraAirMilesRedemptionMetadata_{
				VistaraAirMilesRedemptionMetadata: &beRedemptionPb.RedemptionRequestMetadata_VistaraAirMilesRedemptionMetadata{
					EmailId: feMetadataConcreteType.VistaraAirMilesRedemptionMetadata.EmailId,
					CvId:    feMetadataConcreteType.VistaraAirMilesRedemptionMetadata.CvId,
					FiCoins: feMetadataConcreteType.VistaraAirMilesRedemptionMetadata.FiCoins,
				},
			}
		default:
			return nil, fmt.Errorf("invalid redemption request metadata type")
		}
	}

	if len(feMetadata.GetAdditionalInputs()) != 0 {
		beAdditionalInputs := make([]*beRedemptionPb.RedemptionRequestMetadata_AdditionalInput, 0)
		for _, additionalInput := range feMetadata.GetAdditionalInputs() {
			beAdditionalInputs = append(beAdditionalInputs, &beRedemptionPb.RedemptionRequestMetadata_AdditionalInput{
				Id:    additionalInput.GetId(),
				Value: additionalInput.GetValue(),
			})
		}
		beRedemptionRequestMetadata.AdditionalInputs = beAdditionalInputs
	}

	return beRedemptionRequestMetadata, nil
}

// getBeRewardClaimMetadata converts frontend reward-claim-metadata to backend reward-claim-metadata.
func (r *RewardService) getBeRewardClaimMetadata(feClaimRewardMetadata *fePb.ClaimRewardRequest_ClaimMetadata) *beRewardsPb.RewardClaimMetadata {
	if feClaimRewardMetadata == nil {
		return nil
	}
	beChooseOptionMetadata := &beRewardsPb.RewardClaimMetadata{
		ShippingAddress: convertToGooglePostalAddressType(feClaimRewardMetadata.GetShippingAddress()),
	}

	if feClaimRewardMetadata.GetSdMetadata() != nil {
		beChooseOptionMetadata.RewardTypeSpecificData = &beRewardsPb.RewardClaimMetadata_SdMetadata{
			SdMetadata: &beRewardsPb.RewardClaimMetadata_SDMetadata{
				NomineeInfoList: r.getBeNomineeInfoList(feClaimRewardMetadata.GetSdMetadata().GetNomineeInfoList()),
				SdName:          feClaimRewardMetadata.GetSdMetadata().GetSdName(),
			},
		}
	}
	// add logic for populating other metadata here

	return beChooseOptionMetadata
}

func (r *RewardService) getBeNomineeInfoList(feNomineeInfoList []*fePb.ClaimRewardRequest_ClaimMetadata_SDMetadata_DepositNomineeInfo) []*beRewardsPb.DepositNomineeInfo {
	if len(feNomineeInfoList) == 0 {
		return nil
	}

	var beNomineeInfoList []*beRewardsPb.DepositNomineeInfo
	for _, nomineeInfo := range feNomineeInfoList {
		beNomineeInfoList = append(beNomineeInfoList, &beRewardsPb.DepositNomineeInfo{
			NomineeId:       nomineeInfo.GetNomineeId(),
			PercentageShare: nomineeInfo.GetPercentageShare(),
		})
	}
	return beNomineeInfoList
}

func (r *RewardService) getSalaryProgramInactiveBottomSheetForOffer() *fePb.BottomSheetInfo {
	return &fePb.BottomSheetInfo{
		Title: &commontypes.Text{
			Text:      "Get access to this offer by upgrading to a salary account",
			FontColor: "#333333", // Night
		},
		Desc: &commontypes.Text{
			Text:      "The salary account comes with exclusive offers and epic benefits",
			FontColor: "#646464", // Lead
		},
		Cta: &fePb.CTA{
			Text: &commontypes.Text{
				Text:      "View Fi salary benefits",
				FontColor: "#FFFFFF", // Snow
				BgColor:   "#00B899", // Forest
			},
			IsVisible: true,
			DeeplinkAction: &deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_SALARY_PROGRAM_INTRO_SCREEN,
			},
		},
	}
}

func (r *RewardService) getBeCatalogTagsFromTagNamesList(tags []string) []beCasperPb.TagName {
	var beTags []beCasperPb.TagName
	for _, tag := range tags {
		if beTag, ok := beCasperPb.TagName_value[tag]; ok {
			beTags = append(beTags, beCasperPb.TagName(beTag))
		} else {
			logger.Warn("unhandled tag name encountered while converting to BE tag", zap.String("tagName", tag))
			continue
		}
	}
	return beTags
}

// checkIfClientSupportsBoosterFields checks whether the tiering/booster is supported or not based on the following:
// 1. whether tiering is enabled or not for the actor
// 2. tier of the actor
// 3. appPlatform and appVersion checks
func (r *RewardService) checkIfClientSupportsBoosterFields(
	ctx context.Context, actorId string,
	fetchTieringViaApi, isTieringEnabledForActor bool, actorAccountTier tieringExtPb.Tier,
	appPlatform commontypes.Platform, appVersion uint32,
) bool {
	// local vars for force check abilities
	var (
		isTieringEnabled = isTieringEnabledForActor
		accountTier      = actorAccountTier
	)

	// if tiering feature display is disabled for the user, then don't show the reward boosters as well.
	// context : https://epifi.slack.com/archives/C01LV8HGC65/p1680161289094049
	if appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.Tiering().TieringFeature().MinVersionAndroid() ||
		appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.Tiering().TieringFeature().MinVersionIos() {
		return false
	}

	// force fetch tiering info via API
	if fetchTieringViaApi {
		isTieringEnabled, accountTier = getTieringFeatureFlagAndCurrentTier(ctx, actorId, r.tieringClient)
	}

	// if tiering is disabled (or not yet enabled) for the actor, or if tiering is unspecified for the actor, return false
	if !isTieringEnabled || accountTier == tieringExtPb.Tier_TIER_UNSPECIFIED {
		return false
	}

	// check if client supports the tiering/booster fields
	if appPlatform == commontypes.Platform_ANDROID && appVersion < r.dyconf.RewardsFrontendMeta().MinAndroidVersionSupportingBoosterFields() ||
		appPlatform == commontypes.Platform_IOS && appVersion < r.dyconf.RewardsFrontendMeta().MinIosVersionSupportingBoosterFields() {
		return false
	}

	return true
}

// getFeCardOfferFromBeOffer get FE card offer from be offer, if populatePromotionalDetails is true then some fields(like title, image etc) will be updated with promotional details
// populatePromotionalDetails can be true only for promoted offers.
func (r *RewardService) getFeCardOfferFromBeOffer(beOffer *beCasperPb.Offer, offerListing *beCasperPb.OfferListing, populatePromotionalDetails bool) (*fePb.CardOffer, error) {
	const greyColor = "#ECEEF0"
	var (
		title = beOffer.GetName()
		// card catalog offer tile default bg color
		bgColor = greyColor
	)

	var bgImageUrl, offerDetailsImageUrl, brandLogoUrl, promoImageUrl string
	for _, img := range beOffer.GetImages() {
		switch img.GetImageType() {
		case beCasperPb.ImageType_BACKGROUND_IMAGE:
			bgImageUrl = img.GetUrl()
			offerDetailsImageUrl = img.GetUrl()
		case beCasperPb.ImageType_BRAND_IMAGE:
			brandLogoUrl = img.GetUrl()
		case beCasperPb.ImageType_PROMO_IMAGE:
			promoImageUrl = img.GetUrl()
		default:
			return nil, fmt.Errorf("image type not supported, image_type: %s", img.GetImageType().String())
		}
	}

	// additional handling for promoted offers
	if populatePromotionalDetails {
		title = beOffer.GetAdditionalDetails().GetPromoTitle()
		if title == "" {
			return nil, fmt.Errorf("promo offer title cannot be empty")
		}
		// promo image will be as offer bg image for promoted offers
		bgImageUrl = promoImageUrl
		bgColor = beOffer.GetAdditionalDetails().GetBgColor()
	}

	tncs := make([]*commontypes.Text, len(beOffer.GetTnc().GetTncList()))
	for idx, tnc := range beOffer.GetTnc().GetTncList() {
		tncs[idx] = &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: tnc,
			},
		}
	}

	howToRedeemSteps := make([]*commontypes.Text, len(beOffer.GetAdditionalDetails().GetHowToRedeem()))
	for idx, howToRedeemStep := range beOffer.GetAdditionalDetails().GetHowToRedeem() {
		howToRedeemSteps[idx] = &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: howToRedeemStep,
			},
		}
	}

	activeSince, err := time.Parse(time.RFC3339, offerListing.GetActiveSince())
	if err != nil {
		return nil, fmt.Errorf("error parsing offer active since time string, err: %w", err)
	}
	activeTill, err := time.Parse(time.RFC3339, offerListing.GetActiveTill())
	if err != nil {
		return nil, fmt.Errorf("error parsing offer active till time string, err: %w", err)
	}

	var offerCouponCodes []*fePb.CardOffer_DisplayDetails_DynamicField
	if beOffer.GetOfferMetadata().GetCouponMetadata().GetCouponCodeV2() != "" {
		offerCouponCodes = append(offerCouponCodes, &fePb.CardOffer_DisplayDetails_DynamicField{
			Name:       "CODE",
			Value:      beOffer.GetOfferMetadata().GetCouponMetadata().GetCouponCodeV2(),
			IsSharable: true,
		})
	}

	feCardOffer := &fePb.CardOffer{
		Id:         beOffer.GetId(),
		ExternalId: beOffer.GetExternalId(),
		ActiveFrom: timestamppb.New(activeSince),
		ActiveTill: timestamppb.New(activeTill),
		DisplayDetails: &fePb.CardOffer_DisplayDetails{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: title,
				},
			},
			Desc: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: beOffer.GetDesc(),
				},
			},
			OfferDetailsTitle: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: beOffer.GetAdditionalDetails().GetOfferTitle(),
				},
			},
			BgColor:              bgColor,
			BrandName:            beOffer.GetAdditionalDetails().GetBrandName(),
			Tncs:                 tncs,
			HowToRedeem:          howToRedeemSteps,
			ImageUrl:             bgImageUrl,
			OfferDetailsImageUrl: offerDetailsImageUrl,
			BrandLogoUrl:         brandLogoUrl,
			OfferCouponCodes:     offerCouponCodes,
			// card catalog offer details page tile default bg color
			OfferDetailsBgColor: greyColor,
		},
	}
	return feCardOffer, nil
}

// getFormattedCatalogOfferTncs returns passed list of string TnCs as commontypes.Text. It also
// adds a common TnC applicable to all catalog offers (redemption and CBR)
func (r *RewardService) getFormattedCatalogOfferTncs(tncs []string) []*commontypes.Text {
	var formattedTncs []*commontypes.Text
	formattedTncs = append(formattedTncs, r.formatStringListToHtmlTextList(tncs)...)
	// appending common TnC to the list of formatted TnCs
	formattedTncs = append(formattedTncs, r.getHtmlText(colorGrayLead, colorFiSnow, r.dyconf.RewardsFrontendMeta().OfferCatalogCommonTnc(), commontypes.FontStyle_BODY_S))

	return formattedTncs
}

func (r *RewardService) formatStringListToHtmlTextList(stringList []string) []*commontypes.Text {
	var formattedHtmlText []*commontypes.Text
	for _, text := range stringList {
		formattedHtmlText = append(formattedHtmlText, r.getHtmlText(colorGrayLead, colorFiSnow, text, commontypes.FontStyle_BODY_S))
	}
	return formattedHtmlText
}

// removeHTMLTags removes HTML tags from a given list of strings
func removeHTMLTags(input []string) []string {
	// Define a regular expression pattern to match HTML tags
	re := regexp.MustCompile(`<.*?>`)
	output := make([]string, len(input))

	for i, str := range input {
		// Replace all HTML tags with an empty string
		output[i] = re.ReplaceAllString(str, "")
	}

	return output
}

func shouldClosedRewardTileBeShown(rewardStatus fePb.RewardStatus, claimType beRewardsPb.ClaimType) bool {
	// if reward can be automatically claimed, we don't need to show closed reward tile
	if claimType == beRewardsPb.ClaimType_CLAIM_TYPE_AUTOMATIC {
		return false
	}
	// if reward was to be manually claimed and hasn't been claimed yet
	if claimType == beRewardsPb.ClaimType_CLAIM_TYPE_MANUAL && lo.Contains([]fePb.RewardStatus{fePb.RewardStatus_UNLOCKED, fePb.RewardStatus_LOCKED}, rewardStatus) {
		return true
	}
	// showing opened reward tile in all other cases, i.e. when claim_type was manual but reward has been chosen
	return false
}

func getFeConversionRatio(fiCoinsEquivalent, cashEquivalent uint64) *fePb.FiCoinsConversionRatio {
	if fiCoinsEquivalent == 0 || cashEquivalent == 0 {
		return nil
	}

	return &fePb.FiCoinsConversionRatio{
		Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/rewards/catalog_conversion_ratio_icon.png"),
		FiCoinsValue: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/rewards/catalog_conversion_ratio_fi_coin_icon.png"),
			Texts:             []*commontypes.Text{commontypes.GetPlainStringText(strconv.FormatUint(fiCoinsEquivalent, 10))},
		},
		CashEquivalent: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/rewards/catalog_conversion_ratio_rupee_icon.png"),
			Texts:             []*commontypes.Text{commontypes.GetPlainStringText(strconv.FormatUint(cashEquivalent, 10))},
		},
	}
}

// we'll only have at-most two top right tags - one tag applied to the offer and
// other conversion ratio. this is being done as there's limited space on the
// catalog card, and we can't show more than two top-right tags
func getTopRightTagsForCatalogOffer(feTags []*ui.IconTextComponent, conversionRatio *fePb.FiCoinsConversionRatio) []*fePb.CatalogCardTopRightTag {
	var topRightTags []*fePb.CatalogCardTopRightTag
	if len(feTags) > 0 {
		topRightTags = append(topRightTags, &fePb.CatalogCardTopRightTag{TopRightTag: &fePb.CatalogCardTopRightTag_AppliedTag{AppliedTag: feTags[0]}})
	}

	if conversionRatio != nil {
		topRightTags = append(topRightTags, &fePb.CatalogCardTopRightTag{TopRightTag: &fePb.CatalogCardTopRightTag_FiCoinsConversionRatio{FiCoinsConversionRatio: conversionRatio}})
	}

	return topRightTags
}

func getAllTags(tagsInfo *beCasperPb.TagsInfo) []string {
	tags := lo.Map(tagsInfo.GetTags(), func(tagName beCasperPb.TagName, index int) string {
		return tagName.String()
	})
	manualTags := lo.Map(tagsInfo.GetManualTags(), func(tagName beCasperPb.TagName, index int) string {
		return tagName.String()
	})
	return append(tags, manualTags...)
}

// fetchFeRewardRefId returns reference id in different formats for fe rewards based on action type:
// * CREDIT_CARD_BILLING : Billing date extracted from CC billing ref id
// * Default : BE reward ref id
func (r *RewardService) fetchFeRewardRefId(ctx context.Context, reward *beRewardsPb.Reward) string {
	switch reward.GetActionType() {
	case beRewardsPb.CollectedDataType_CREDIT_CARD_BILLING:
		ccBillingEventGenerationDate, err := r.fetchCcBillingDateFromRefId(reward.GetRefId())
		if err != nil {
			logger.Error(ctx, "failed to fetch cc billing date from reward ref id", zap.String(logger.REWARD_ID, reward.GetId()), zap.String(logger.REFERENCE_ID, reward.GetRefId()), zap.Error(err))
			return reward.GetRefId()
		}
		return ccBillingEventGenerationDate
	default:
		return reward.GetRefId()
	}
}

func (r *RewardService) fetchCcBillingDateFromRefId(ccBillingRefId string) (string, error) {
	if len(ccBillingRefId) < 8 {
		return "", fmt.Errorf("cc billing ref id size less than 8, unable to extract date from it")
	}

	// Extract the date string from ref id
	n := len(ccBillingRefId)
	dateString := ccBillingRefId[n-8 : n-2]

	// Extract year, month, and day from the input string
	year := "20" + dateString[0:2] // "23" -> "2023"
	month := dateString[2:4]       // "06"
	day := dateString[4:6]         // "24"

	// Parse the date string into a time.Time object
	date, err := time.Parse("2006-01-02", fmt.Sprintf("%s-%s-%s", year, month, day))
	if err != nil {
		return "", fmt.Errorf("error parsing date from cc billing ref id, err : %w", err)
	}

	// Format the date as dd mm yyyy (02 Jan 2006) format
	formattedDate := date.Format("02 Jan 2006")

	return formattedDate, nil
}

// getSduiCatalogOfferCardRedemptionCta returns the SDUI component for the redemption Button
func (r *RewardService) getSduiCatalogOfferCardRedemptionCta(displayTexts []*commontypes.Text, redemptionButtonDeeplink *deeplinkPb.Deeplink, analyticsEvent *analytics.AnalyticsEvent) *sections.HorizontalListSection {
	return &sections.HorizontalListSection{
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
		Components: []*components.Component{
			{
				Content: GetAnyWithoutError(ui.NewITC().WithTexts(displayTexts...).WithContainerPaddingSymmetrical(10, 2)),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: properties.GetContainerProperty().
						WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 158).
						WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 32).
						WithMargin(0, 0, 0, 12).
						WithBlockBgColor(colorFiSnow).
						WithAllCornerRadii(16, 16, 16, 16),
				},
			},
		},
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
					OnClickBehavior: &behaviors.OnClickBehavior{
						Action: GetAnyWithoutError(redemptionButtonDeeplink),
					},
				},
				AnalyticsEvent: analyticsEvent,
			},
		},
	}
}

// convertInoperableInfoToCatalogOfferRedemptionBottomSheetScreenOptions converts the inoperable info to generic catalog offer redemption bottom sheet screen options
func (r *RewardService) convertInoperableInfoToCatalogOfferRedemptionBottomSheetScreenOptions(inoperableInfo *fePb.BottomSheetInfo) *rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions {
	if !inoperableInfo.GetCta().GetIsVisible() {
		return nil
	}

	nextActionCta := &rewardsFrontendPkgPb.Cta{
		Itc: getNextActionCtaItc().
			WithTexts(UpdateTextWithDisplayValue(inoperableInfo.GetCta().GetText().WithFontColor(colorFiSnow).WithFontStyle(commontypes.FontStyle_BUTTON_M).WithAlignment(commontypes.Text_ALIGNMENT_CENTER))),
		Shadow: greenShadow,
		Action: &rewardsFrontendPkgPb.Cta_DeeplinkAction{
			DeeplinkAction: inoperableInfo.GetCta().GetDeeplinkAction(),
		},
	}

	return &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions{
		BottomSheetData: &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions_OfferRedemptionBottomSheetData{
			OfferRedemptionBottomSheetData: &rewardsScreenOptionsPb.OfferRedemptionBottomSheetData{
				Title: ui.NewITC().WithTexts(UpdateTextWithDisplayValue(inoperableInfo.GetTitle().WithFontColor(colorInk).WithFontStyle(commontypes.FontStyle_SUBTITLE_L).WithAlignment(commontypes.Text_ALIGNMENT_CENTER))),
				Descriptions: []*ui.IconTextComponent{
					ui.NewITC().WithTexts(UpdateTextWithDisplayValue(inoperableInfo.GetDesc().WithFontColor(colorContentOnDarkLowEmphasis).WithFontStyle(commontypes.FontStyle_BODY_S).WithAlignment(commontypes.Text_ALIGNMENT_CENTER))),
				},
				NextActionCta: nextActionCta,
				CloseCta:      &rewardsFrontendPkgPb.Cta{},
			},
		},
	}
}

// getSduiCatalogOfferRedemptionDefaultBottomSheetScreenOptions returns the screen options for the catalog offer redemption bottom sheet
// This is the base case which is used when the offer is redeemable via exact fi coins.
func (r *RewardService) getSduiCatalogOfferRedemptionDefaultBottomSheetScreenOptions(offerId string, redemptionText []*commontypes.Text) *rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions {
	nextActionCta := &rewardsFrontendPkgPb.Cta{
		Itc: ui.NewITC().
			WithContainer(40, 90, 20, colorFiGreen).
			WithContainerPaddingSymmetrical(16, 12).
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Confirm", colorFiSnow, commontypes.FontStyle_BUTTON_M).WithMaxLines(1)),
		Shadow: greenShadow,
		Action: &rewardsFrontendPkgPb.Cta_CustomAction{
			CustomAction: &rewardsFrontendPkgPb.CustomAction{
				ActionType: rewardsFrontendPkgPb.CustomAction_MAKE_API_CALL,
				ActionApi:  rewardsFrontendPkgPb.CustomAction_INITIATE_REDEMPTION_RPC,
				ActionData: &rewardsFrontendPkgPb.CustomAction_GetInitiateRedemptionApiActionData_{
					GetInitiateRedemptionApiActionData: &rewardsFrontendPkgPb.CustomAction_GetInitiateRedemptionApiActionData{
						OfferId: offerId,
					},
				},
			},
		},
	}
	return &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions{
		BottomSheetData: &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions_OfferRedemptionBottomSheetData{
			OfferRedemptionBottomSheetData: &rewardsScreenOptionsPb.OfferRedemptionBottomSheetData{
				Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Get this offer with", colorInk, commontypes.FontStyle_SUBTITLE_L, commontypes.Text_ALIGNMENT_CENTER)),
				Banner: ui.NewITC().
					WithContainer(72, 246, 20, colorContentOnDarkHighEmphasis).
					WithContainerPadding(20, 20, 20, 20).
					WithLeftVisualElementUrlHeightAndWidth(fiCoinsIconUrl, 32, 32).
					WithTexts(redemptionText...).
					WithLeftImagePadding(12),
				Descriptions: []*ui.IconTextComponent{
					ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment("This offer cannot be returned or exchanged.\nConfirm to redeem the offer using your Fi-Coins.", colorContentOnDarkLowEmphasis, commontypes.FontStyle_BODY_S, commontypes.Text_ALIGNMENT_CENTER).WithMaxLines(2)),
				},
				NextActionCta: nextActionCta,
				CloseCta:      &rewardsFrontendPkgPb.Cta{},
				EntityId:      offerId,
			},
		},
	}
}

// getSduiCatalogOfferRedemptionLoanDefaultUserBottomSheetScreenOptions returns the screen options for the catalog offer redemption bottom sheet
// when a user who has a default loan clicks on rededmption button
func (r *RewardService) getSduiCatalogOfferRedemptionLoanDefaultUserBottomSheetScreenOptions(redemptionButtonDeeplink *deeplinkPb.Deeplink) *rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions {
	var (
		nextActionCta = &rewardsFrontendPkgPb.Cta{
			Itc: ui.NewITC().
				WithContainer(44, 90, 20, colorFiGreen).
				WithContainerPaddingSymmetrical(52, 12).
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Pay EMI", colorFiSnow, commontypes.FontStyle_BUTTON_M).WithMaxLines(1)),
			Shadow: greenShadow,
			Action: &rewardsFrontendPkgPb.Cta_DeeplinkAction{
				DeeplinkAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
						PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
							LoanHeader: &palEnumFePb.LoanHeader{
								EventData: &palEnumFePb.EventData{
									EntryPointV2: palEnumFePb.EntryPoint_ENTRY_POINT_CATALOG_OFFER_REDEMPTION_SCREEN.String(),
								},
							},
						},
					},
				},
			},
		}
		secondaryActionCta = &rewardsFrontendPkgPb.Cta{
			Itc: ui.NewITC().
				WithContainer(40, 90, 20, colorContentOnDarkHighEmphasis).
				WithContainerPaddingSymmetrical(24, 12).
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Spend Fi-Coins", colorFiGreen, commontypes.FontStyle_BUTTON_M).WithMaxLines(1)),
			Shadow: greyShadow,
			Action: &rewardsFrontendPkgPb.Cta_DeeplinkAction{
				DeeplinkAction: redemptionButtonDeeplink,
			},
		}
	)
	return &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions{
		BottomSheetData: &rewardsScreenOptionsPb.CatalogOfferRedemptionBottomSheetScreenOptions_OfferRedemptionBottomSheetData{
			OfferRedemptionBottomSheetData: &rewardsScreenOptionsPb.OfferRedemptionBottomSheetData{
				Title: ui.NewITC().
					WithContainer(80, 80, 0, colorFiSnow).
					WithLeftVisualElementUrlHeightAndWidth(lockIconUrl, 80, 80),
				Descriptions: []*ui.IconTextComponent{
					ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment("You have pending loan dues", colorGrayNight, commontypes.FontStyle_SUBTITLE_1, commontypes.Text_ALIGNMENT_CENTER)),
					ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyleFontAlignment("This is a reminder to clear your pending dues", colorGrayLead, commontypes.FontStyle_BODY_3_PARA, commontypes.Text_ALIGNMENT_CENTER).WithMaxLines(2)),
				},
				NextActionCta:      nextActionCta,
				SecondaryActionCta: secondaryActionCta,
				CloseCta:           &rewardsFrontendPkgPb.Cta{},
			},
		},
	}
}

// getSduiCatalogOfferRedemptionText returns the redemption text for the catalog offer based on the redemptionTextPlace
// nolint : funlen
func (r *RewardService) getSduiCatalogOfferRedemptionText(ctx context.Context, redemptionTextPlace string, catalogOffer *fePb.CatalogOfferV1, catalogOffersAdditionalDetails *rewardsFrontendPkg.CatalogOffersAdditionalDetails) []*commontypes.Text {
	var (
		offerRedemptionPrice           int32
		offerRedemptionDiscountedPrice int32
		isOfferNotRedeemable           bool
		redemptionButtonText           []*commontypes.Text
	)
	switch catalogOffer.GetOfferData().(type) {
	case *fePb.CatalogOfferV1_Offer:
		feOffer := catalogOffer.GetOffer()
		beOffer := catalogOffersAdditionalDetails.GetOfferIdToBeOfferMap()[feOffer.GetId()]
		offerRedemptionPrice = feOffer.GetPrice()
		isOfferNotRedeemable = feOffer.GetDisplayDetails().GetIsOfferNotRedeemable()
		offerRedemptionDiscountedPrice = int32(feOffer.GetDiscountDetails().GetDiscountedPrice())
		switch redemptionTextPlace {
		case offerRedemptionButton:
			switch {
			case !isOfferNotRedeemable && beOffer.GetAdditionalDetails().GetAlternateCtaText() != "":
				redemptionButtonText = append(redemptionButtonText, feOffer.GetDisplayDetails().GetCtaText().WithMaxLines(1).WithFontColor(colorFiGreen).WithFontStyle(commontypes.FontStyle_BUTTON_XS))
			case !isOfferNotRedeemable && offerRedemptionPrice != 0 && offerRedemptionDiscountedPrice != 0:
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("<s>%s</s> ", money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)), colors.ColorAsh, commontypes.FontStyle_BUTTON_XS).WithMaxLines(1))
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionDiscountedPrice), 0)+" Fi-Coins", colorFiGreen, commontypes.FontStyle_BUTTON_XS).WithMaxLines(1))
			case !isOfferNotRedeemable && offerRedemptionPrice != 0 && offerRedemptionDiscountedPrice == 0:
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)+" Fi-Coins", colorFiGreen, commontypes.FontStyle_BUTTON_XS).WithMaxLines(1))
			case isOfferNotRedeemable && beOffer.GetAdditionalDetails().GetAlternateCtaText() != "":
				redemptionButtonText = append(redemptionButtonText, feOffer.GetDisplayDetails().GetCtaText().WithMaxLines(1).WithFontColor(colors.ColorMonochromeAsh).WithFontStyle(commontypes.FontStyle_BUTTON_XS))
			case isOfferNotRedeemable && offerRedemptionPrice != 0 && offerRedemptionDiscountedPrice != 0:
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("<s>%s</s> ", money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)), colors.ColorAsh, commontypes.FontStyle_BUTTON_XS).WithMaxLines(1))
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionDiscountedPrice), 0)+" Fi-Coins", colors.ColorMonochromeAsh, commontypes.FontStyle_BUTTON_XS).WithMaxLines(1))
			case isOfferNotRedeemable && offerRedemptionPrice != 0 && offerRedemptionDiscountedPrice == 0:
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)+" Fi-Coins", colors.ColorMonochromeAsh, commontypes.FontStyle_BUTTON_XS).WithMaxLines(1))
			default:
				redemptionButtonText = append(redemptionButtonText, feOffer.GetDisplayDetails().GetCtaText().WithMaxLines(1).WithFontColor(colors.ColorMonochromeAsh).WithFontStyle(commontypes.FontStyle_BUTTON_XS))
			}
		case viewOfferDetailsRedemptionButton:
			switch {
			case !isOfferNotRedeemable && beOffer.GetAdditionalDetails().GetAlternateCtaText() != "":
				redemptionButtonText = append(redemptionButtonText, feOffer.GetDisplayDetails().GetCtaText().WithMaxLines(1).WithFontColor(colorFiSnow).WithFontStyle(commontypes.FontStyle_BUTTON_M))
			case !isOfferNotRedeemable && offerRedemptionPrice != 0 && offerRedemptionDiscountedPrice != 0:
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle("Get with ", colorFiSnow, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("<s>%s</s> ", money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)), colorDeepGreen, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionDiscountedPrice), 0)+" Fi-Coins", colorFiSnow, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
			case !isOfferNotRedeemable && offerRedemptionPrice != 0 && offerRedemptionDiscountedPrice == 0:
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle("Get with ", colorFiSnow, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)+" Fi-Coins", colorFiSnow, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
			case isOfferNotRedeemable && beOffer.GetAdditionalDetails().GetAlternateCtaText() != "":
				redemptionButtonText = append(redemptionButtonText, feOffer.GetDisplayDetails().GetCtaText().WithMaxLines(1).WithFontColor(colors.ColorMonochromeAsh).WithFontStyle(commontypes.FontStyle_BUTTON_M))
			case isOfferNotRedeemable && offerRedemptionPrice != 0 && offerRedemptionDiscountedPrice != 0:
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle("Get with ", colors.ColorMonochromeAsh, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("<s>%s</s> ", money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)), colors.ColorMonochromeAsh, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionDiscountedPrice), 0)+" Fi-Coins", colors.ColorMonochromeAsh, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
			case isOfferNotRedeemable && offerRedemptionPrice != 0 && offerRedemptionDiscountedPrice == 0:
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle("Get with ", colors.ColorMonochromeAsh, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)+" Fi-Coins", colors.ColorMonochromeAsh, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
			default:
				redemptionButtonText = append(redemptionButtonText, feOffer.GetDisplayDetails().GetCtaText().WithMaxLines(1).WithFontColor(colorFiSnow).WithFontStyle(commontypes.FontStyle_BUTTON_M))
			}
		case offerRedemptionBottomSheet:
			finalOfferRedemptionPrice := func() int32 {
				if offerRedemptionDiscountedPrice != 0 {
					return offerRedemptionDiscountedPrice
				}
				return offerRedemptionPrice
			}()
			redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(finalOfferRedemptionPrice), 0)+" Fi-Coins", colorContentOnLightHighEmphasis, commontypes.FontStyle_NUMBER_L).WithMaxLines(1))
		default:
			// would not be called without redemptionTextPlace implemented
		}
	case *fePb.CatalogOfferV1_ExchangerOffer:
		feExchangerOffer := catalogOffer.GetExchangerOffer()
		offerRedemptionPrice = int32(feExchangerOffer.GetRedemptionPrice().GetPrice())
		isOfferNotRedeemable = feExchangerOffer.GetDisplayDetails().GetIsOfferNotRedeemable()
		switch redemptionTextPlace {
		case offerRedemptionButton:
			if isOfferNotRedeemable {
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)+" Fi-Coins", colors.ColorMonochromeAsh, commontypes.FontStyle_BUTTON_XS).WithMaxLines(1))
			} else {
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)+" Fi-Coins", colorFiGreen, commontypes.FontStyle_BUTTON_XS).WithMaxLines(1))
			}
		case viewOfferDetailsRedemptionButton:
			if isOfferNotRedeemable {
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle("Get with ", colors.ColorMonochromeAsh, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)+" Fi-Coins", colors.ColorMonochromeAsh, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
			} else {
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle("Get with ", colorFiSnow, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)+" Fi-Coins", colorFiSnow, commontypes.FontStyle_BUTTON_M).WithMaxLines(1))
			}
		case offerRedemptionBottomSheet:
			if offerRedemptionPrice != 0 {
				redemptionButtonText = append(redemptionButtonText, commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringInIndianFormatFromFloatValue(float64(offerRedemptionPrice), 0)+" Fi-Coins", colorContentOnLightHighEmphasis, commontypes.FontStyle_NUMBER_L).WithMaxLines(1))
			}
		default:
			// would not be called without redemptionTextPlace implemented
		}
	default:
		logger.Error(ctx, "invalid offer data type", zap.Any("offerData", catalogOffer.GetOfferData()))
	}
	return redemptionButtonText
}

// getSduiCatalogCardCtaLabel returns the CTA label for the sdui catalog card redemption cta
func (r *RewardService) getSduiCatalogCardCtaLabel(ctaLabel *fePb.Label, topLabel *fePb.Label) *anyPb.Any {
	var (
		finalContent              *anyPb.Any
		ctaLabelContainerProperty = properties.GetContainerProperty().WithAllCornerRadii(8, 8, 0, 0).
						WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 16).
						WithWidth(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
						WithPadding(7, 2, 7, 2)
	)
	switch {
	case ctaLabel != nil && ctaLabel.GetCountdownTimer() != nil:
		ctaLabelContainerProperty.BgColor = ctaLabel.GetCountdownTimer().GetBgColor()
		ctaLabelContainerProperty.WithPadding(7, 0, 7, 0)
		finalContent = GetAnyWithoutError(&components.CountdownTimerTag{
			Text: UpdateTextWithDisplayValue(ctaLabel.GetCountdownTimer().GetText().
				WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS).
				WithAlignment(commontypes.Text_ALIGNMENT_CENTER).
				WithMaxLines(1)),
			CountdownTillTime: ctaLabel.GetCountdownTimer().GetCountdownTillTime(),
			BgColor:           ctaLabel.GetCountdownTimer().GetBgColor(),
		})
	case ctaLabel != nil:
		ctaLabelContainerProperty.WithBlockBgColor(ctaLabel.GetBgColor())
		finalContent = GetAnyWithoutError(UpdateTextWithDisplayValue(ctaLabel.GetText().WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)))
	case topLabel != nil:
		ctaLabelContainerProperty.WithBlockBgColor("#D9FFFFFF")
		finalContent = GetAnyWithoutError(UpdateTextWithDisplayValue(topLabel.GetText().WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS).WithAlignment(commontypes.Text_ALIGNMENT_CENTER)))
	default:
		return nil
	}
	return GetAnyWithoutError(&sections.HorizontalListSection{
		Components: []*components.Component{
			{
				Content: finalContent,
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: ctaLabelContainerProperty,
				},
			},
		},
	})
}

// getSduiCatalogOfferCardTopRightTag returns the top right tag for the catalog offer card
// currently only one tag is supported
func (r *RewardService) getSduiCatalogOfferCardTopRightTag(topRightTags []*fePb.CatalogCardTopRightTag) *ui.IconTextComponent {
	if len(topRightTags) > 0 {
		topRightTag := topRightTags[0].GetAppliedTag()
		itc := ui.NewITC().WithContainerPaddingSymmetrical(8, 4)
		texts := topRightTag.GetTexts()
		if len(texts) > 0 {
			for _, text := range texts {
				itc.Texts = append(itc.Texts, UpdateTextWithDisplayValue(text.WithFontStyle(commontypes.FontStyle_OVERLINE_2XS_CAPS)))
			}
		}
		return itc
	}
	return nil
}

// UpdateTextWithDisplayValue updates the input text with display value using the text string
// and sets the background color to empty string as it is not recommended to use text bg
func UpdateTextWithDisplayValue(inputText *commontypes.Text) *commontypes.Text {
	if inputText == nil {
		return inputText
	}
	inputText.BgColor = ""
	if inputText.GetDisplayValue() != nil {
		return inputText
	}
	inputText.DisplayValue = &commontypes.Text_PlainString{
		PlainString: inputText.GetText(),
	}
	return inputText
}

// getNextActionCtaItc returns the default green ITC for the next action CTA
func getNextActionCtaItc() *ui.IconTextComponent {
	return ui.NewITC().
		WithContainerBackgroundColor(colorFiGreen).
		WithContainerCornerRadius(20).
		WithContainerPaddingSymmetrical(16, 10)
}

// fetchFiCoinOfferImageUrls returns offer logo and bg image urls from offer images
func fetchFiCoinOfferImageUrls(images []*fePb.OfferImage) (string, string) {
	var (
		offerLogoUrl, offerBgUrl string
	)
	for _, image := range images {
		switch image.GetImageType() {
		case fePb.ImageType_BRAND_IMAGE:
			offerLogoUrl = image.GetUrl()
		case fePb.ImageType_BACKGROUND_IMAGE:
			offerBgUrl = image.GetUrl()
		default:
			logger.ErrorNoCtx("unimplemented offer image type", zap.String("imageType", image.GetImageType().String()))
		}
	}
	return offerLogoUrl, offerBgUrl
}

// getCatalogCardLeftMargin returns left margin for catalog card based on index and orientation
func getCatalogCardLeftMargin(index int, orientation sections.GridListSection_Orientation) int32 {
	switch {
	case orientation == sections.GridListSection_ORIENTATION_HORIZONTAL && index == 0:
		return horizontalPaddingBetweenCatalogCards
	case orientation == sections.GridListSection_ORIENTATION_VERTICAL && index%2 == 0:
		return horizontalPaddingBetweenCatalogCards
	default:
		return 0
	}
}

// getCatalogCardRightMargin returns right margin for catalog card based on index, total offers and orientation
func getCatalogCardRightMargin(index int, totalOffers int, orientation sections.GridListSection_Orientation) int32 {
	if orientation == sections.GridListSection_ORIENTATION_VERTICAL && index%2 == 1 {
		return horizontalPaddingBetweenCatalogCards
	}
	if orientation == sections.GridListSection_ORIENTATION_HORIZONTAL && index == totalOffers-1 {
		return horizontalPaddingBetweenCatalogCards
	}
	return 0
}

func (r *RewardService) convertFeFiltersToBeFilters(feFilters *fePb.CatalogFilters) *beCasperPb.CatalogFilters {
	var (
		beTags         []beCasperPb.TagName
		beCategoryTags []beCasperPb.CategoryTag
	)
	for _, tag := range feFilters.GetTags() {
		if beCategoryTag := beCasperPb.CategoryTag(beCasperPb.CategoryTag_value[tag]); beCategoryTag != 0 {
			beCategoryTags = append(beCategoryTags, beCategoryTag)
		} else if beTag := beCasperPb.TagName(beCasperPb.TagName_value[tag]); beTag != 0 {
			beTags = append(beTags, beTag)
		} else {
			logger.Warn("unhandled tag or category tag name encountered while converting to BE category tag", zap.String("tag", tag))
			continue
		}
	}
	return &beCasperPb.CatalogFilters{
		Tags:         beTags,
		CategoryTags: beCategoryTags,
	}
}

// getOffersCatalogPageV2AnalyticsEvent returns the analytics event for the offers catalog page v2 for sdui components
func (r *RewardService) getOffersCatalogPageV2AnalyticsEvent(eventName, offerId, subComponent, ctaText, ctaState, isCtaCountdownTimerPresent string, offerHorizontalRank, offerVerticalRank uint32) *analytics.AnalyticsEvent {
	return &analytics.AnalyticsEvent{
		EventName: eventName,
		Properties: map[string]string{
			"offer_id":        offerId,
			"sub_component":   subComponent,
			"cta_text":        ctaText,
			"cta_state":       ctaState,
			"cta_offer_timer": isCtaCountdownTimerPresent,
			"horizontal_rank": strconv.FormatUint(uint64(offerHorizontalRank), 10),
			"vertical_rank":   strconv.FormatUint(uint64(offerVerticalRank), 10),
		},
	}
}

// getCatalogOfferRedemptionBottomSheetEventProperties returns the analytics event properties for the catalog offer redemption bottom sheet
func getCatalogOfferRedemptionBottomSheetEventProperties(offerId, screeName, offerType, title, descripton string) *rewardsScreenOptionsPb.AnalyticsDetails {
	return &rewardsScreenOptionsPb.AnalyticsDetails{
		EventProperties: map[string]string{
			"offer_id":    offerId,
			"screen_name": screeName,
			"offer_type":  offerType,
			"title":       title,
			"description": descripton,
		},
	}
}

// getTextsValue returns the string value of the texts
func getTextsValue(texts []*commontypes.Text) string {
	var result string
	for _, text := range texts {
		switch text.GetDisplayValue().(type) {
		case *commontypes.Text_PlainString:
			result += text.GetPlainString()
		case *commontypes.Text_Html:
			result += text.GetHtml()
		case *commontypes.Text_EmbeddedLinks:
			result += text.GetEmbeddedLinks()
		default:
			result += text.GetText()
		}
	}
	return result
}

func getTextsFromItcs(itcs []*ui.IconTextComponent) []*commontypes.Text {
	var texts []*commontypes.Text
	for _, itc := range itcs {
		texts = append(texts, itc.GetTexts()...)
	}
	return texts
}

func fetchNumberOfOffersAsString(count int) string {
	if count == 1 {
		return fmt.Sprintf("%d offer", count)
	}
	return fmt.Sprintf("%d offers", count)
}
