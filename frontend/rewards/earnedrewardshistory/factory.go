package earnedrewardshistory

import (
	"fmt"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	rewardsTypes "github.com/epifi/gamma/api/typesv2/rewards"
	"github.com/epifi/gamma/frontend/config/genconf"
)

type ScreenDataBuilderFactoryImpl struct {
	CcScreenDataBuilder ScreenDataBuilder
}

// NewScreenDataBuilderFactory creates a new instance of ScreenBuilderFactory
func NewScreenDataBuilderFactory(conf *genconf.Config, raClient rewardspinotpb.RewardsAggregatesClient, fireflyV2Client ffBeV2Pb.FireflyV2Client) *ScreenDataBuilderFactoryImpl {
	return &ScreenDataBuilderFactoryImpl{
		CcScreenDataBuilder: NewCreditCardScreenDataBuilder(conf, raClient, fireflyV2Client),
	}
}

// GetScreenDataBuilder returns the appropriate screen data builder for the given domain ID
func (f *ScreenDataBuilderFactoryImpl) GetScreenDataBuilder(domainId rewardsTypes.EarnedRewardsHistoryDomainId) (ScreenDataBuilder, error) {
	switch domainId {
	case rewardsTypes.EarnedRewardsHistoryDomainId_EARNED_REWARDS_HISTORY_DOMAIN_ID_CREDIT_CARD:
		return f.CcScreenDataBuilder, nil
	default:
		return nil, fmt.Errorf("unsupported domain ID: %v", domainId)
	}
}
