package earnedrewardshistory

import (
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
)

type EarnedBenefitsHistoryScreenData struct {
	TopBanner                 *ui.IconTextComponent
	PageContextResponse       *fePb.PageContextResponse
	HeaderView                *fePb.EarnedRewardsHistoryHeaderView
	CurrentMonthRewardView    *fePb.RewardsProcessingTimelineInfo
	MonthlyEarnedRewardsViews []*fePb.MonthlyEarnedRewardsView
}

// GetTopBanner returns the TopBanner field
func (e *EarnedBenefitsHistoryScreenData) GetTopBanner() *ui.IconTextComponent {
	if e != nil {
		return e.TopBanner
	}
	return nil
}

// GetPageContextResponse returns the PageContextResponse field
func (e *EarnedBenefitsHistoryScreenData) GetPageContextResponse() *fePb.PageContextResponse {
	if e != nil {
		return e.PageContextResponse
	}
	return nil
}

// GetHeaderView returns the HeaderView field
func (e *EarnedBenefitsHistoryScreenData) GetHeaderView() *fePb.EarnedRewardsHistoryHeaderView {
	if e != nil {
		return e.HeaderView
	}
	return nil
}

// GetCurrentMonthRewardView returns the CurrentMonthRewardView field
func (e *EarnedBenefitsHistoryScreenData) GetCurrentMonthRewardView() *fePb.RewardsProcessingTimelineInfo {
	if e != nil {
		return e.CurrentMonthRewardView
	}
	return nil
}

// GetMonthlyEarnedRewardsViews returns the MonthlyEarnedRewardsViews field
func (e *EarnedBenefitsHistoryScreenData) GetMonthlyEarnedRewardsViews() []*fePb.MonthlyEarnedRewardsView {
	if e != nil {
		return e.MonthlyEarnedRewardsViews
	}
	return nil
}
