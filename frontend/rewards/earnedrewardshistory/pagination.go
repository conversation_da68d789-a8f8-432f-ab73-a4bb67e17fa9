package earnedrewardshistory

import (
	"encoding/base64"
	"encoding/json"
	"fmt"

	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
)

const defaultPageSize = 20

type EarnedRewardsPageToken struct {
	PageSize   uint32
	FirstMonth *dateTimePkg.MonthYear // first month of current page
}

func (e *EarnedRewardsPageToken) Marshal() (string, error) {
	b, err := json.Marshal(e)
	if err != nil {
		return "", fmt.Errorf("failed to marshal, %w", err)
	}

	return base64.StdEncoding.EncodeToString(b), nil
}

func UnmarshalToken(tokenStr string) (*EarnedRewardsPageToken, error) {
	b, err := base64.StdEncoding.DecodeString(tokenStr)
	if err != nil {
		return nil, fmt.Errorf("failed to decode token string, %w", err)
	}

	token := &EarnedRewardsPageToken{}
	unmarshalErr := json.Unmarshal(b, token)
	return token, unmarshalErr
}

func (e *EarnedRewardsPageToken) GetNextPageToken() *EarnedRewardsPageToken {
	nextPageFirstMonth := e.GetFirstMonth().GetStartOfMonth(dateTimePkg.IST).AddDate(0, -int(e.PageSize), 0)
	return &EarnedRewardsPageToken{
		PageSize:   e.GetPageSize(),
		FirstMonth: dateTimePkg.GetMonthYear(nextPageFirstMonth),
	}
}

func (e *EarnedRewardsPageToken) GetPrevPageToken() *EarnedRewardsPageToken {
	prevPageFirstMonth := e.GetFirstMonth().GetStartOfMonth(dateTimePkg.IST).AddDate(0, int(e.PageSize), 0)
	return &EarnedRewardsPageToken{
		PageSize:   e.GetPageSize(),
		FirstMonth: dateTimePkg.GetMonthYear(prevPageFirstMonth),
	}
}

// GetMonthsForToken lists n months from FirstMonth in reverse. n -> PageSize
func (e *EarnedRewardsPageToken) GetMonthsForToken() []*dateTimePkg.MonthYear {
	var months []*dateTimePkg.MonthYear
	for p := 0; p < int(e.GetPageSize()); p++ {
		m := dateTimePkg.GetMonthYear(e.GetFirstMonth().GetStartOfMonth(dateTimePkg.IST).AddDate(0, -p, 0))
		months = append(months, m)
	}

	return months
}

func (e *EarnedRewardsPageToken) GetPageSize() uint32 {
	if e != nil {
		return e.PageSize
	}
	return 0
}

func (e *EarnedRewardsPageToken) GetFirstMonth() *dateTimePkg.MonthYear {
	if e != nil {
		return e.FirstMonth
	}
	return nil
}
