package earnedrewardshistory

import (
	"context"
	"errors"
	"strconv"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	colorPkg "github.com/epifi/be-common/pkg/colors"
	dateTimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	errorsPkg "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	ffBeV2Pb "github.com/epifi/gamma/api/firefly/v2"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardspinotpb "github.com/epifi/gamma/api/rewards/pinot"
	rewardsTypes "github.com/epifi/gamma/api/typesv2/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
)

// CreditCardScreenBuilder is the screen builder for credit card domain
type CreditCardScreenBuilder struct {
	conf            *genconf.Config
	raClient        rewardspinotpb.RewardsAggregatesClient
	fireflyV2Client ffBeV2Pb.FireflyV2Client
}

func NewCreditCardScreenDataBuilder(conf *genconf.Config, raClient rewardspinotpb.RewardsAggregatesClient, fireflyV2Client ffBeV2Pb.FireflyV2Client) *CreditCardScreenBuilder {
	return &CreditCardScreenBuilder{
		conf:            conf,
		raClient:        raClient,
		fireflyV2Client: fireflyV2Client,
	}
}

// BuildScreenData builds the credit card earned benefits history screen data
func (b *CreditCardScreenBuilder) BuildScreenData(ctx context.Context, actorId string, pageContext *fePb.PageContextRequest) (*EarnedBenefitsHistoryScreenData, error) {
	// get credit card creation time
	ccCreationTime, err := b.getCreditCardCreationTime(ctx, actorId)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "failed to get credit card creation time")
	}

	// unmarshall page token
	pageToken, tokenErr := b.getEarnedRewardsHistoryPageToken(pageContext)
	if tokenErr != nil {
		return nil, errorsPkg.Wrap(tokenErr, "failed to get page token")
	}

	// get months to fetch from page token
	monthsToFetch, err := b.getMonthsToFetch(pageToken, ccCreationTime)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "failed to get months to fetch from page token")
	}

	totalFiCoinAggregates, err := b.getFiCoinAggregatesForTimeRange(ctx, actorId, ccCreationTime, timestamppb.Now())
	if err != nil {
		return nil, errorsPkg.Wrap(err, "failed to get total fi coin aggregates")
	}

	// get fi coin aggregates
	fiCoinsAggregates, err := b.getFiCoinAggregatesForMonth(ctx, actorId, monthsToFetch)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "failed to get fi coin aggregates for month")
	}

	// get the screen config
	screenConfig, err := b.conf.RewardsFrontendMeta().EarnedRewardsHistoryConfig().GetEarnedRewardsHistoryScreenConfig(rewardsTypes.EarnedRewardsHistoryDomainId_EARNED_REWARDS_HISTORY_DOMAIN_ID_CREDIT_CARD.String())
	if err != nil {
		return nil, errorsPkg.Wrap(err, "failed to get earned rewards history screen config for cc rewards history screen")
	}

	// build response page context
	respPageContext, err := b.getResponsePageContext(pageToken, ccCreationTime)
	if err != nil {
		return nil, errorsPkg.Wrap(err, "failed to get response page context")
	}

	return &EarnedBenefitsHistoryScreenData{
		TopBanner:           screenConfig.TopBanner().GetUiITCComponent(),
		PageContextResponse: respPageContext,
		HeaderView: &fePb.EarnedRewardsHistoryHeaderView{
			HeaderIcon:              screenConfig.HeaderViewConfig().HeaderIcon().GetUiVisualElementImageComponent(),
			TotalEarnedRewardsTitle: screenConfig.HeaderViewConfig().TotalBenefitsEarnedTitle().GetUiITCComponent(),
			TotalEarnedRewardsValue: screenConfig.HeaderViewConfig().TotalBenefitsEarnedValue().GetUiITCComponent().
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(strconv.FormatFloat(totalFiCoinAggregates, 'f', 2, 64), colorPkg.ColorOnLightHighEmphasis, commontypes.FontStyle_NUMBER_2XL)),
			BottomText: screenConfig.HeaderViewConfig().BottomText().GetUiITCComponent(),
		},
		CurrentMonthRewardView: &fePb.RewardsProcessingTimelineInfo{
			HeaderView: &fePb.EarnedRewardsHeader{
				BackgroundColour: widget.GetBlockBackgroundColour(colorPkg.ColorOnDarkHighEmphasis),
				Title:            commontypes.GetTextFromStringFontColourFontStyle(time.Now().Month().String(), colorPkg.ColorOnLightHighEmphasis, commontypes.FontStyle_SUBTITLE_M),
			},
			ProcessingInfo: screenConfig.RewardProcessingTimelineConfig().GetUiITCComponent(),
		},
		MonthlyEarnedRewardsViews: b.buildMonthlyEarnedRewardViews(fiCoinsAggregates),
	}, nil
}

func (b *CreditCardScreenBuilder) buildMonthlyEarnedRewardViews(fiCoinsAggregates map[dateTimePkg.MonthYear]float64) []*fePb.MonthlyEarnedRewardsView {
	var monthlyEarnedRewardViews []*fePb.MonthlyEarnedRewardsView
	for monthYear, monthlyAggregates := range fiCoinsAggregates {
		fiCoinsValueStr := strconv.FormatFloat(monthlyAggregates, 'f', 2, 64)
		monthlyEarnedRewardViews = append(monthlyEarnedRewardViews, &fePb.MonthlyEarnedRewardsView{
			HeaderView: &fePb.EarnedRewardsHeader{
				BackgroundColour: widget.GetBlockBackgroundColour(colorPkg.ColorOnDarkHighEmphasis),
				Title:            commontypes.GetTextFromStringFontColourFontStyle(monthYear.GetMonth().String()+" "+strconv.FormatInt(int64(monthYear.GetYear()), 10), colorPkg.ColorDarkLayer2, commontypes.FontStyle_SUBTITLE_M),
			},
			CardBackgroundColour:        widget.GetBlockBackgroundColour(colorPkg.ColorSnow),
			TotalBenefitsEarnedTitle:    commontypes.GetTextFromStringFontColourFontStyle(monthlyViewBenefitsEarnedTitleText, colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS),
			TotalBenefitsEarnedValue:    commontypes.GetTextFromStringFontColourFontStyle(fiCoinsValueStr, colorPkg.ColorOnLightHighEmphasis, commontypes.FontStyle_NUMBER_XL),
			BenefitCardBackgroundColour: widget.GetBlockBackgroundColour(colorPkg.ColorOnDarkHighEmphasis),
			BenefitCardItems: []*fePb.EarnedRewardsCard{
				{
					Title: commontypes.GetTextFromStringFontColourFontStyle(benefitCardViewFiCoinsEarnedText, colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_OVERLINE_2XS_CAPS),
					Value: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(fiCoinsValueStr, colorPkg.ColorOnLightHighEmphasis, commontypes.FontStyle_NUMBER_M)).
						WithLeftVisualElement(commontypes.GetVisualElementImageFromUrl(fiPointsIconUrlSmall)),
				},
			},
		})
	}
	return monthlyEarnedRewardViews
}

func (b *CreditCardScreenBuilder) getResponsePageContext(currentPageToken *EarnedRewardsPageToken, ccCreationTime *timestamppb.Timestamp) (*fePb.PageContextResponse, error) {
	var (
		respPageContext = &fePb.PageContextResponse{}
	)

	afterToken, err := currentPageToken.GetNextPageToken().Marshal()
	if err != nil {
		return nil, errorsPkg.Wrap(err, "failed to unmarshal after token")
	}

	beforeToken, err := currentPageToken.GetPrevPageToken().Marshal()
	if err != nil {
		return nil, errorsPkg.Wrap(err, "failed to unmarshal before token")
	}

	if dateTimePkg.IsMonthYearEqual(currentPageToken.GetFirstMonth(), dateTimePkg.GetMonthYear(time.Now())) {
		beforeToken = ""
	}

	if currentPageToken.GetMonthsForToken()[currentPageToken.GetPageSize()-1].GetStartOfMonth(dateTimePkg.IST).Before(ccCreationTime.AsTime()) {
		afterToken = ""
	}

	if afterToken != "" {
		respPageContext.AfterToken = afterToken
		respPageContext.HasAfter = true
	}

	if beforeToken != "" {
		respPageContext.BeforeToken = beforeToken
		respPageContext.HasBefore = true
	}

	return respPageContext, nil
}

func (b *CreditCardScreenBuilder) getFiCoinAggregatesForMonth(ctx context.Context, actorId string, monthsToFetch []*dateTimePkg.MonthYear) (map[dateTimePkg.MonthYear]float64, error) {
	var (
		fiCoinAggregates = make(map[dateTimePkg.MonthYear]float64)
	)
	for _, monthYear := range monthsToFetch {
		fiCoinAggregatesForMonth, err := b.getFiCoinAggregatesForTimeRange(ctx, actorId, timestamppb.New(monthYear.GetStartOfMonth(dateTimePkg.IST)), timestamppb.New(monthYear.GetEndOfMonth(dateTimePkg.IST)))
		if err != nil {
			return nil, errorsPkg.Wrap(err, "failed to get fi coin aggregates for time range")
		}
		fiCoinAggregates[*monthYear] = fiCoinAggregatesForMonth
	}
	if len(fiCoinAggregates) == 0 {
		return nil, errors.New("no fi coin aggregates found")
	}

	return fiCoinAggregates, nil
}

func (b *CreditCardScreenBuilder) getFiCoinAggregatesForTimeRange(ctx context.Context, actorId string, from, to *timestamppb.Timestamp) (float64, error) {
	res, err := b.raClient.GetRewardsAggregates(ctx, &rewardspinotpb.GetRewardsAggregatesRequest{
		ActorId: actorId,
		Filters: &rewardspinotpb.Filters{
			RewardType:       rewardsPb.RewardType_FI_COINS,
			RewardStatuses:   []rewardsPb.RewardStatus{rewardsPb.RewardStatus_PROCESSED},
			RewardOfferTypes: []rewardsPb.RewardOfferType{rewardsPb.RewardOfferType_MASS_UNSECURED_CREDIT_CARD_CVP_OFFER},
		},
		TimeRange: rewardspinotpb.NewTimeRangeFilter(rewardspinotpb.TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME, from, to),
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return 0, errorsPkg.Wrap(te, "failed to get fi coin aggregates for time range")
	}
	for _, rewardOptions := range res.GetRewardOptionAggregates() {
		if rewardOptions.GetRewardType() == rewardsPb.RewardType_FI_COINS {
			return rewardOptions.GetRewardUnits(), nil
		}
	}
	return 0, nil
}

func (b *CreditCardScreenBuilder) getMonthsToFetch(pageToken *EarnedRewardsPageToken, maxAllowedEndTime *timestamppb.Timestamp) ([]*dateTimePkg.MonthYear, error) {
	var monthsToFetch []*dateTimePkg.MonthYear
	for idx, month := range pageToken.GetMonthsForToken() {
		// skip current month
		if time.Now().Month() == month.GetMonth() {
			continue
		}
		if idx > defaultPageSize || month.GetStartOfMonth(dateTimePkg.IST).After(maxAllowedEndTime.AsTime()) {
			break
		}
		monthsToFetch = append(monthsToFetch, month)
	}
	if len(monthsToFetch) == 0 {
		return nil, errors.New("no months to fetch")
	}
	return monthsToFetch, nil
}

func (b *CreditCardScreenBuilder) getEarnedRewardsHistoryPageToken(pageContext *fePb.PageContextRequest) (*EarnedRewardsPageToken, error) {
	var (
		token    *EarnedRewardsPageToken
		tokenStr = pageContext.GetBeforeToken()
	)
	if pageContext.GetAfterToken() != "" {
		tokenStr = pageContext.GetAfterToken()
	}

	if tokenStr == "" {
		token = &EarnedRewardsPageToken{
			PageSize:   defaultPageSize,
			FirstMonth: dateTimePkg.GetMonthYear(time.Now()),
		}
	} else {
		var unmarshalErr error
		token, unmarshalErr = UnmarshalToken(tokenStr)
		if unmarshalErr != nil {
			return nil, errorsPkg.Wrap(unmarshalErr, "failed to unmarshal page token")
		}
	}

	return token, nil
}

func (b *CreditCardScreenBuilder) getCreditCardCreationTime(ctx context.Context, actorId string) (*timestamppb.Timestamp, error) {
	creditCardsResp, err := b.fireflyV2Client.GetCreditCards(ctx, &ffBeV2Pb.GetCreditCardsRequest{
		Identifier: &ffBeV2Pb.GetCreditCardsRequest_ActorId{
			ActorId: actorId,
		},
	})
	if te := epifigrpc.RPCError(creditCardsResp, err); te != nil {
		logger.Error(ctx, "failed to get credit card creation time", zap.Error(te))
		return nil, errors.New("failed to get credit card creation time")
	}

	if len(creditCardsResp.GetCreditCards()) == 0 {
		return nil, errors.New("no credit card found")
	}
	return creditCardsResp.GetCreditCards()[0].GetCreatedAt(), nil
}
