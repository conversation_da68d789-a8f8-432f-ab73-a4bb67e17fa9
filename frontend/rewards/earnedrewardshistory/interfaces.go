package earnedrewardshistory

import (
	"context"

	"github.com/google/wire"

	"github.com/epifi/gamma/api/frontend/rewards"
	rewardsTypes "github.com/epifi/gamma/api/typesv2/rewards"
)

var (
	WireSet = wire.NewSet(
		NewCreditCardScreenDataBuilder,
		NewScreenDataBuilderFactory, wire.Bind(new(ScreenDataBuilderFactory), new(*ScreenDataBuilderFactoryImpl)))
)

// ScreenDataBuilder is the interface for building earned benefits history screens
type ScreenDataBuilder interface {
	BuildScreenData(ctx context.Context, actorId string, pageContext *rewards.PageContextRequest) (*EarnedBenefitsHistoryScreenData, error)
}

// ScreenDataBuilderFactory creates screen builders based on domain ID
type ScreenDataBuilderFactory interface {
	GetScreenDataBuilder(domainId rewardsTypes.EarnedRewardsHistoryDomainId) (ScreenDataBuilder, error)
}
