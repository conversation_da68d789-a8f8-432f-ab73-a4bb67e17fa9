package rewards

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/epificontext"

	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	onbPkg "github.com/epifi/gamma/pkg/onboarding"

	beCardPb "github.com/epifi/gamma/api/card"
	"github.com/epifi/gamma/api/card/provisioning"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	beCasperPb "github.com/epifi/gamma/api/casper"
	beExchangerPb "github.com/epifi/gamma/api/casper/exchanger"
	beRedemptionPb "github.com/epifi/gamma/api/casper/redemption"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	types "github.com/epifi/gamma/api/typesv2"
	casperDeeplinkScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/casper"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	beUserPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/frontend/rewards/tags"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
)

// todo(rohanchougule): check if message should be generic instead of cash
var exchangerRedemptionMaxCapHitCashFailure = feErrors.NewBottomSheetErrorView(
	"",
	"An error occurred", "",
	"Looks like you have exceeded the maximum cash rewards limit for this offer",
	&errorsPb.CTA{
		Text: "Back to Offers",
		Type: errorsPb.CTA_CUSTOM,
		Action: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
		},
		DisplayTheme: errorsPb.CTA_PRIMARY,
	},
)

// GetRedeemOfferInputScreen checks if any user input is required for redeeming a given offer.
// If required, it returns deeplink to the screen where input should be taken.
func (r *RewardService) GetRedeemOfferInputScreen(ctx context.Context, req *fePb.RedeemOfferInputScreenRequest) (*fePb.RedeemOfferInputScreenResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	// fetch offer details
	catalogOfferRes, err := r.offerCatalogClient.GetOfferDetailsById(ctx, &beCasperPb.GetOfferDetailsByIdRequest{OfferId: req.GetOfferId()})
	if err != nil || !catalogOfferRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching offer from catalog", zap.Any("response", catalogOfferRes), zap.Error(err))
		return &fePb.RedeemOfferInputScreenResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching offer from catalog"),
		}, nil
	}
	catalogOffer := catalogOfferRes.GetOffer()

	actorResp, err := r.actorServiceClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: req.GetReq().GetAuth().GetActorId()})
	if err = epifigrpc.RPCError(actorResp, err); err != nil {
		logger.Error(ctx, "error in finding actor", zap.Error(err))
		return &fePb.RedeemOfferInputScreenResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	inputScreenDeeplink, err := r.getRedeemOfferInputScreenDeeplink(ctx, catalogOffer.GetId(), catalogOffer.GetOfferMetadata(), catalogOffer.GetOfferType(), catalogOffer.GetVendorName(), actorResp.GetActor(), req.GetReq().GetAuth().GetDevice().GetPlatform(), req.GetReq().GetAppVersionCode())
	if err != nil {
		logger.Error(ctx, "error fetching deeplink for input screen", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any("offer_type", catalogOffer.GetOfferType()), zap.Error(err))
		return &fePb.RedeemOfferInputScreenResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching deeplink for input screen " + err.Error()),
		}, nil
	}

	return &fePb.RedeemOfferInputScreenResponse{
		Status:      rpc.StatusOk(),
		InputScreen: inputScreenDeeplink,
	}, nil
}

// getRedeemOfferInputScreenDeeplink returns the deeplink for the redeem offer input screen.
// Input screen depends on the actor and type of offer being redeemed.
// nolint:funlen
func (r *RewardService) getRedeemOfferInputScreenDeeplink(ctx context.Context, offerId string, offerMetadata *beCasperPb.OfferMetadata, offerType beCasperPb.OfferType, offerVendor beCasperPb.OfferVendor, actor *types.Actor, platform commontypes.Platform, appVersion uint32) (*deeplinkPb.Deeplink, error) {
	switch offerType {

	// for physical merchandise offers, we require shipping address as input from user.
	// on input screen, we also display the existing shipping addresses present in user profile.
	case beCasperPb.OfferType_PHYSICAL_MERCHANDISE:
		// fetch existing shipping and permanent address from user profile
		userAddressRes, err := r.usersClient.GetAllAddresses(ctx, &beUserPb.GetAllAddressesRequest{UserId: actor.GetEntityId()})
		if err != nil || !userAddressRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "GetAllAddresses rpc call failed", zap.String(logger.USER_ID, actor.GetEntityId()), zap.Any("response", userAddressRes), zap.Error(err))

			// if client doesn't support graceful handling of the empty addresses, then return error
			if !r.checkIfClientSupportsEmptyAddressesGracefully(platform, appVersion) {
				return nil, errors.New("GetAllAddresses rpc call failed")
			}
		}

		// convert google postal address to types.postalAddress
		var addresses []*types.PostalAddress
		for addressType, address := range userAddressRes.GetAddresses() {
			addressList := address.GetAddresses()
			if (addressType == types.AddressType_SHIPPING.String() || addressType == types.AddressType_PERMANENT.String()) && len(addressList) > 0 {
				addresses = append(addresses, convertToClientPostalAddressType(addressList[0]))
			}
		}

		// return deeplink with user's existing shipping address details
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_REWARD_SHIPPING_ADDRESS_INPUT_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_RewardShippingAddressInputScreenOptions{
				RewardShippingAddressInputScreenOptions: &deeplinkPb.RewardShippingAddressInputScreenOptions{
					// existing shipping addresses present in user profile
					Addresses: addresses,
				},
			},
		}, nil

	case beCasperPb.OfferType_VISTARA_AIR_MILES:
		if !r.checkIfClientSupportsConvertFiCoinsToPointsOfferRedemption(platform, appVersion) {
			return nil, errors.New("fi coins to points offer type redemption not supported on client")
		}

		return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_CONVERT_FI_COINS_OFFER_REDEMPTION_BOTTOM_SHEET,
			&casperDeeplinkScreenOptions.ConvertFiCoinsOfferRedemptionBottomSheetOptions{
				OfferId: offerId,
			})

	case beCasperPb.OfferType_CLUB_ITC_GREEN_POINTS:
		if !r.checkIfClientSupportsConvertFiCoinsToPointsOfferRedemption(platform, appVersion) {
			return nil, errors.New("fi coins to points offer type redemption not supported on client")
		}

		return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_CONVERT_FI_COINS_OFFER_REDEMPTION_BOTTOM_SHEET,
			&casperDeeplinkScreenOptions.ConvertFiCoinsOfferRedemptionBottomSheetOptions{
				OfferId: offerId,
			})

	case beCasperPb.OfferType_EXTERNAL_VENDOR:
		if !r.checkIfClientSupportsExternalVendorOfferRedemption(platform, appVersion) {
			return nil, fmt.Errorf("external vendor offer type redemption not supported on client")
		}
		deeplink, err := r.getExternalVendorDeeplink(ctx, actor.GetId(), offerMetadata.GetExternalVendorOfferMetadata(), offerVendor)
		if err != nil {
			logger.Error(ctx, "error while fetching external vendor deep link", zap.String(logger.ACTOR_ID_V2, actor.GetId()), zap.Error(err))
			return nil, fmt.Errorf("error while fetching external vendor deep link for actor, err: %w", err)
		}
		return deeplink, nil
	default:
		// no input required
		return nil, nil
	}
}

// InitiateRedemption initiates a redemption for an offer
func (r *RewardService) InitiateRedemption(ctx context.Context, req *fePb.InitiateRedemptionRequest) (*fePb.InitiateRedemptionResponse, error) {
	// check if fi coins to fi points migration is active
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return &fePb.InitiateRedemptionResponse{
			Status: rpc.StatusUnavailableWithDebugMsg(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error()),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: GetFiCoinsToFiPointsDowntimeErrorView(),
			},
		}, nil
	}

	beRedemptionRequestMetadata, err := r.getBeRedemptionRequestMetadata(req.GetRequestMetadata())
	if err != nil {
		logger.Error(ctx, "error while converting fe redemption metadata data to be redemption metadata", zap.Error(err))
		return &fePb.InitiateRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg("error while converting fe redemption metadata data to be redemption metadata")}, nil
	}

	initiateRedemptionRes, err := r.offerRedemptionClient.InitiateRedemption(ctx, &beRedemptionPb.InitiateRedemptionRequest{
		OfferId:         req.GetOfferId(),
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		RequestMetadata: beRedemptionRequestMetadata,
	})
	if err != nil || !initiateRedemptionRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "initiate redemption call failed", zap.Any("response", initiateRedemptionRes), zap.String("failureReason", initiateRedemptionRes.GetFailureReason().String()), zap.Error(err))
		switch {
		case initiateRedemptionRes.GetStatus().IsInvalidArgument():
			var errorMessage *commontypes.Text
			switch initiateRedemptionRes.GetFailureReason() {
			case beRedemptionPb.InitiateRedemptionResponse_FAILURE_REASON_INVALID_CV_ID:
				errorMessage = &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Please enter a valid Club Vistara ID"}}
			case beRedemptionPb.InitiateRedemptionResponse_FAILURE_REASON_CV_MEMBERSHIP_VALIDATION_FAILURE:
				errorMessage = &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Membership details do not match"}}
			case beRedemptionPb.InitiateRedemptionResponse_FAILURE_REASON_VISTARA_USER_DOES_NOT_EXIST:
				errorMessage = &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Membership linked to this Email Id does not exist"}}
			case beRedemptionPb.InitiateRedemptionResponse_FAILURE_REASON_NON_REDEEMABLE_OFFER:
				errorMessage = &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "This offer is not redeemable"}}
			}
			return &fePb.InitiateRedemptionResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg("initiate redemption call failed"), ErrorMessage: errorMessage}, nil
		case initiateRedemptionRes.GetStatus().IsInternal() && initiateRedemptionRes.GetFailureReason() == beRedemptionPb.InitiateRedemptionResponse_FAILURE_REASON_VENDOR_API_FAILURE:
			return &fePb.InitiateRedemptionResponse{
				Status:       rpc.StatusInternalWithDebugMsg("initiate redemption call failed"),
				ErrorMessage: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Details could not be confirmed. Please try again"}},
			}, nil
		default:
			return &fePb.InitiateRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg("initiate redemption call failed")}, nil
		}
	}
	return &fePb.InitiateRedemptionResponse{Status: rpc.StatusOk(), RedemptionRequestId: initiateRedemptionRes.GetRedemptionRequestId()}, nil
}

// ConfirmRedemption confirms an initiated redemption and returns the redeemed offer details.
// Redemption is splitted into initiation and confirmation steps to de-dup requests so that on retry
// multiple redemption of same offer (which was not intended) can be avoided.
// This api is idempotent so no need of a status check api.
func (r *RewardService) ConfirmRedemption(ctx context.Context, req *fePb.ConfirmRedemptionRequest) (*fePb.ConfirmRedemptionResponse, error) {
	confirmRedemptionRes, err := r.offerRedemptionClient.ConfirmRedemption(ctx, &beRedemptionPb.ConfirmRedemptionRequest{RedemptionRequestId: req.GetRedemptionRequestId()})
	if err != nil || !confirmRedemptionRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "confirm redemption call failed", zap.Any("response", confirmRedemptionRes.GetStatus()), zap.Error(err))
		return &fePb.ConfirmRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg("confirm redemption call failed")}, nil
	}
	// get redeemed offer from confirm response
	beRedeemedOffer := confirmRedemptionRes.GetRedeemedOffer()

	// decrypt redeemed offer
	if beRedeemedOffer != nil {
		decryptedRedeemedOffersRes, err2 := r.offerRedemptionClient.DecryptRedeemedOffersDetails(ctx, &beRedemptionPb.DecryptRedeemedOffersDetailsRequest{RedeemedOffers: []*beRedemptionPb.RedeemedOffer{beRedeemedOffer}})
		if err2 != nil || !decryptedRedeemedOffersRes.GetStatus().IsSuccess() {
			logger.Error(ctx, "get active redeemed offers call failed", zap.Any("response", decryptedRedeemedOffersRes.GetStatus()), zap.Error(err2))
			return &fePb.ConfirmRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg("error decryption redeemed offer details")}, nil
		}
		beRedeemedOffer = decryptedRedeemedOffersRes.GetRedeemedOffers()[0]
	}

	beOfferDetailRes, err := r.offerCatalogClient.GetOfferDetailsById(ctx, &beCasperPb.GetOfferDetailsByIdRequest{OfferId: beRedeemedOffer.GetOfferId()})
	if err != nil || !beOfferDetailRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "GetOfferDetailsById call failed", zap.Any("response", beOfferDetailRes), zap.Error(err))
		return &fePb.ConfirmRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg("GetOfferDetailsById call failed")}, nil
	}

	// create frontend redeemed offer from backend offer and backend redeemed offer
	// todo(rohan): do we need to pass salary program status from here?
	feRedeemedOffer, err := r.getFeRedeemedOffer(ctx, beRedeemedOffer, beOfferDetailRes.GetOffer(), req.GetReq().GetAppVersionCode(), req.GetReq().GetAuth().GetDevice().GetPlatform())
	if err != nil {
		logger.Error(ctx, "error converting beRedeemedOffer to feRedeemedOffer", zap.String(logger.REDEEMED_OFFER_ID, beRedeemedOffer.GetId()), zap.Error(err))
		return &fePb.ConfirmRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg("error converting beRedeemedOffer to feRedeemedOffer " + err.Error())}, nil
	}

	return &fePb.ConfirmRedemptionResponse{Status: rpc.StatusOk(), RedeemedOffer: feRedeemedOffer,
		NextScreenCta: &uiPb.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor: "#FFFFFF",
					BgColor:   "#00B899",
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BUTTON_M,
					},
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "View in 'Collected Offers'",
					},
					FontColorOpacity: 100,
				},
			},
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN,
			},
			ContainerProperties: &uiPb.IconTextComponent_ContainerProperties{
				BgColor:       "#00B899",
				CornerRadius:  19,
				Height:        40,
				Width:         312,
				LeftPadding:   64,
				RightPadding:  64,
				TopPadding:    10,
				BottomPadding: 10,
			},
		},
	}, nil
}

// RedeemExchangerOffer RPC redeems the exchanger-offer for the actor and returns the exchanger-order along with the
// remaining attempts for the offer.
// todo(rohanchougule): try to move the "remaining attempts" orchestration to the backend
// nolint:funlen
func (r *RewardService) RedeemExchangerOffer(ctx context.Context, req *fePb.RedeemExchangerOfferRequest) (*fePb.RedeemExchangerOfferResponse, error) {
	// check if fi coins to fi points migration is active
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return &fePb.RedeemExchangerOfferResponse{
			Status: rpc.StatusUnavailableWithDebugMsg(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error()),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: GetFiCoinsToFiPointsDowntimeErrorView(),
			},
		}, nil
	}

	var (
		actorId          = req.GetReq().GetAuth().GetActorId()
		exchangerOfferId = req.GetExchangerOfferId()
		requestId        = req.GetRequestId()
	)
	beRedeemExchangerOfferReq := &beExchangerPb.RedeemExchangerOfferRequest{
		ActorId:          actorId,
		ExchangerOfferId: exchangerOfferId,
		RequestId:        requestId,
	}

	// redeem the offer and get the order in return
	redeemExchangerOfferResp, err := r.exchangerOfferClient.RedeemExchangerOffer(ctx, beRedeemExchangerOfferReq)
	switch {
	case redeemExchangerOfferResp.GetStatus().GetCode() == uint32(beExchangerPb.RedeemExchangerOfferResponse_CREDIT_FROZEN):
		return &fePb.RedeemExchangerOfferResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("error while redeeming exchanger offer"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: feErrors.NewBottomSheetErrorView("", "Account Frozen", "", "Your savings account is currently not allowing incoming funds. Please reach out to customer support to unblock.", &errorsPb.CTA{
					Text: "Back to Offers",
					Type: errorsPb.CTA_CUSTOM,
					Action: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
					},
					DisplayTheme: errorsPb.CTA_PRIMARY,
				}),
			},
		}, nil
	case redeemExchangerOfferResp.GetStatus().GetCode() == uint32(beExchangerPb.RedeemExchangerOfferResponse_SAVING_ACCOUNT_NOT_FOUND):
		ctas := []*errorsPb.CTA{
			{
				Text: "Go Back",
				Type: errorsPb.CTA_CUSTOM,
				Action: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
				},
				DisplayTheme: errorsPb.CTA_SECONDARY,
			},
		}

		saBenefitsDeeplink, getErr := onbPkg.GetSABenefitsScreen(ctx)
		if getErr != nil {
			// log the error and return bottom sheet without the cta
			logger.ErrorNoCtx("error while getting savings account benefits screen deeplink", zap.Error(getErr))
		}

		if saBenefitsDeeplink != nil {
			ctas = append(ctas, &errorsPb.CTA{
				Text:         "Open Savings Account",
				Type:         errorsPb.CTA_CUSTOM,
				Action:       saBenefitsDeeplink,
				DisplayTheme: errorsPb.CTA_PRIMARY,
			})
		}

		return &fePb.RedeemExchangerOfferResponse{
			Status: rpc.StatusFailedPreconditionWithDebugMsg("error while redeeming exchanger offer"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: feErrors.NewBottomSheetErrorView("", "Currently only Available to savings account holders", "Get access to this offer by opening a Federal Bank savings account", "", ctas...),
			},
		}, nil
	// if rpc status is ResourceExhausted, just propagate the same status code in response
	case redeemExchangerOfferResp.GetStatus().IsResourceExhausted():
		return &fePb.RedeemExchangerOfferResponse{Status: rpc.StatusResourceExhausted()}, nil
	// if there's err in rpc call or status is not OK, return ISE
	case err != nil || !redeemExchangerOfferResp.GetStatus().IsSuccess():
		logger.Error(ctx, "error while redeeming exchanger offer",
			zap.String(logger.ACTOR_ID, actorId), zap.String(logger.OFFER_ID, exchangerOfferId), zap.Error(err),
			zap.Any(logger.RPC_STATUS, redeemExchangerOfferResp.GetStatus()), zap.String(logger.REQUEST_ID, requestId),
		)
		return &fePb.RedeemExchangerOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while redeeming exchanger offer"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: feErrors.NewBottomSheetErrorView("", "An error occurred", "", "Redemption failed, please try again in some time!", &errorsPb.CTA{
					Text: "Back to Offers",
					Type: errorsPb.CTA_CUSTOM,
					Action: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
					},
					DisplayTheme: errorsPb.CTA_PRIMARY,
				}),
			},
		}, nil
	}

	// Check if options generation failed and return a custom error view. Failure of options generation leads to reversal of debited fi-coins.
	// Options generation can fail for the following reasons currently:
	// 1. Max-cap hit for all the possible rewardTypes in all the options
	exchangerOrder := redeemExchangerOfferResp.GetExchangerOfferOrder()
	if exchangerOrder.GetState() == beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REWARD_OPTIONS_GENERATION_FAILED ||
		exchangerOrder.GetState() == beExchangerPb.ExchangerOfferOrderState_ORDER_STATE_REVERSED_DEBITED_OFFER_AMOUNT {
		logger.Error(ctx, "options generation failed during redemption", zap.String(logger.EXCHANGER_OFFER_ORDER_ID, exchangerOrder.GetId()),
			zap.String(logger.STATE, exchangerOrder.GetState().String()),
		)
		return &fePb.RedeemExchangerOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("options generation failure"),
			RespHeader: &feHeaderPb.ResponseHeader{
				ErrorView: exchangerRedemptionMaxCapHitCashFailure,
			},
		}, nil
	}

	// convert the order to fe exchanger-offer-order
	feExchangerOrder, err := r.convertToFeExchangerOrder(ctx, exchangerOrder, nil, tags.RenderLocationUnspecified)
	if err != nil {
		logger.Error(ctx, "error converting be-exchanger-offer-order to fe",
			zap.String(logger.ACTOR_ID, actorId), zap.String(logger.OFFER_ID, exchangerOfferId), zap.Error(err),
		)
		// todo(rohanchougule): should we instead send out generic error than implementation specific ones?
		return &fePb.RedeemExchangerOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("error converting be-exchanger-offer to fe"),
		}, nil
	}

	// calculate remaining attempts
	exchangerOfferRes, err := r.exchangerOfferClient.GetExchangerOffersByIds(ctx, &beExchangerPb.GetExchangerOffersByIdsRequest{
		Ids: []string{exchangerOfferId},
	})
	if err != nil || !exchangerOfferRes.GetStatus().IsSuccess() || len(exchangerOfferRes.GetExchangerOffers()) == 0 {
		logger.Error(ctx, "redemption done but couldn't fetch exchanger-offer for calculating remaining attempts",
			zap.String(logger.OFFER_ID, exchangerOfferId), zap.Any(logger.RPC_STATUS, exchangerOfferRes.GetStatus()),
			zap.Error(err),
		)
		// todo(rohanchougule): since redemption is successful, we are still returning error. discuss once
		return &fePb.RedeemExchangerOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching exchanger-offer"),
		}, nil
	}

	currentDayStartTime := datetime.GetTimeAtStartOfTheDay(time.Now().In(datetime.IST))
	actorAttemptsRes, err := r.exchangerOfferClient.GetExchangerOffersActorAttemptsCount(ctx, &beExchangerPb.GetExchangerOffersActorAttemptsCountRequest{
		ActorId:           actorId,
		ExchangerOfferIds: []string{exchangerOfferId},
		FromAttemptedTime: timestamppb.New(currentDayStartTime),
		ToAttemptedTime:   timestamppb.New(currentDayStartTime.AddDate(0, 0, 1)),
	})
	if err != nil || !actorAttemptsRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "error fetching attempts count for offer by actor", zap.String(logger.ACTOR_ID, actorId),
			zap.String(logger.OFFER_ID, exchangerOfferId), zap.Any(logger.RPC_STATUS, actorAttemptsRes.GetStatus()),
			zap.Error(err),
		)
		// todo(rohanchougule): since redemption is successful, we are still returning error. discuss once
		return &fePb.RedeemExchangerOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching attempts count for offer by the actor"),
		}, nil
	}

	// Note: we are assuming "daily" attempts here. Update the logic in case we have different intervals in the future
	maxAttemptsForOffer := exchangerOfferRes.GetExchangerOffers()[0].GetOfferAggregatesConfig().GetDailyAllowedAttemptsPerUser()
	currentAttemptsForOffer := actorAttemptsRes.GetOfferIdToAttemptsCountMap()[exchangerOfferId]
	var remainingAttempts uint32

	// to handle possible race condition at the backend when attempts exceed the allowed value
	if currentAttemptsForOffer > int32(maxAttemptsForOffer) {
		remainingAttempts = 0
	} else {
		remainingAttempts = maxAttemptsForOffer - uint32(currentAttemptsForOffer)
	}

	// fetch the remaining fi-coins balance
	// todo(rohanchougule): can be fetched via a separate RPC as well by the client. remove it if not reqd
	fiCoinsBalance, err := r.getFiCoinsBalanceForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error fetching fi-coins balance in RedeemExchangerOffer", zap.Error(err))
		return &fePb.RedeemExchangerOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("error fetching remaining fi-coins balance for the actor"),
		}, nil
	}

	return &fePb.RedeemExchangerOfferResponse{
		Status:              rpc.StatusOk(),
		ExchangerOfferOrder: feExchangerOrder,
		AttemptsLeft:        remainingAttempts,
		FiCoinsBalance:      fiCoinsBalance,
		Subtitle:            r.getOptionsScreenSubtitle(ctx, exchangerOrder.GetOptions().GetOptions()),
	}, nil
}

// GetRedeemedOfferDetailsRedirectionInfo rpc is useful to fetch the redirection url for viewing the redeemed offer details,
// useful for offers where the post the offer redemption on the Fi App, the redeemed offer details are visible on the vendor app only.
func (r *RewardService) GetRedeemedOfferDetailsRedirectionInfo(ctx context.Context, req *fePb.GetRedeemedOfferDetailsRedirectionInfoRequest) (*fePb.GetRedeemedOfferDetailsRedirectionInfoResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()

	// get redeemed offer details
	redeemedOfferRes, err := r.offerRedemptionClient.GetRedeemedOfferById(ctx, &beRedemptionPb.GetRedeemedOfferByIdRequest{RedeemedOfferId: req.GetRedeemedOfferId()})
	if rpcErr := epifigrpc.RPCError(redeemedOfferRes, err); rpcErr != nil {
		logger.Error(ctx, "offerRedemptionClient.GetRedeemedOfferById rpc call failed", zap.Error(rpcErr))
		return &fePb.GetRedeemedOfferDetailsRedirectionInfoResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("offerRedemptionClient.GetRedeemedOfferById rpc call failed")},
		}, nil
	}
	redeemedOffer := redeemedOfferRes.GetRedeemedOffer()

	// get details of the catalog offer which was redeemed
	offerDetailsRes, err := r.offerCatalogClient.GetBulkOfferDetailsByIds(ctx, &beCasperPb.GetBulkOfferDetailsByIdsRequest{OfferIds: []string{redeemedOffer.GetOfferId()}})
	if rpcErr := epifigrpc.RPCError(offerDetailsRes, err); rpcErr != nil || len(offerDetailsRes.GetOffers()) == 0 {
		logger.Error(ctx, "offerCatalogClient.GetBulkOfferDetailsByIds rpc call failed", zap.Error(rpcErr))
		return &fePb.GetRedeemedOfferDetailsRedirectionInfoResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("offerCatalogClient.GetBulkOfferDetailsByIds rpc call failed")},
		}, nil
	}
	offer := offerDetailsRes.GetOffers()[0]

	// get redirection url
	redirectionUrlRes, err := r.offerRedemptionClient.GetRedeemedOfferVendorRedirectionUrl(ctx, &beRedemptionPb.GetRedeemedOfferVendorRedirectionUrlRequest{
		ActorId:     actorId,
		OfferVendor: offer.GetVendorName(),
	})
	if rpcErr := epifigrpc.RPCError(redirectionUrlRes, err); rpcErr != nil {
		logger.Error(ctx, "offerRedemptionClient.GetRedeemedOfferVendorRedirectionUrl rpc call failed", zap.Error(rpcErr))
		return &fePb.GetRedeemedOfferDetailsRedirectionInfoResponse{
			RespHeader: &feHeaderPb.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("offerRedemptionClient.GetRedeemedOfferVendorRedirectionUrl rpc call failed")},
		}, nil
	}

	return &fePb.GetRedeemedOfferDetailsRedirectionInfoResponse{
		RespHeader:     &feHeaderPb.ResponseHeader{Status: rpc.StatusOk()},
		RedirectionUrl: redirectionUrlRes.GetRedirectionUrl(),
	}, nil
}

// getOptionsScreenSubtitle returns subtitle text to be shown on the generated options screen.
func (r *RewardService) getOptionsScreenSubtitle(ctx context.Context, exchangerOfferOptions []*beExchangerPb.ExchangerOfferOption) string {
	if len(exchangerOfferOptions) == 0 {
		return ""
	}
	if len(exchangerOfferOptions) > 1 {
		return ""
	}

	rewardType := exchangerOfferOptions[0].GetRewardType()
	switch rewardType {
	case beExchangerPb.RewardType_REWARD_TYPE_CASH:
		return "You can find it under ‘My Rewards’"
	case beExchangerPb.RewardType_REWARD_TYPE_FI_COINS:
		return "This will be added to your Fi-Coin balance"
	case beExchangerPb.RewardType_REWARD_TYPE_PHYSICAL_MERCHANDISE, beExchangerPb.RewardType_REWARD_TYPE_EGV:
		return "You can find it under ‘My Orders’"
	default:
		logger.Error(ctx, "unsupported rewardType encountered while selecting subtitle text for exchanger offer options screen, continuing without one")
		return ""
	}
}

// getExternalVendorDeeplink returns deeplink for vendors type.
func (r *RewardService) getExternalVendorDeeplink(ctx context.Context, actorId string, externalVendorOfferMetadata *beCasperPb.ExternalVendorOfferMetadata, offerVendor beCasperPb.OfferVendor) (*deeplinkPb.Deeplink, error) {
	webpageUrl := ""
	switch offerVendor {
	case beCasperPb.OfferVendor_DPANDA:
		resp, err := r.offerCatalogClient.GetExternalVendorDynamicWebpageUrl(ctx, &beCasperPb.GetExternalVendorDynamicWebpageUrlRequest{
			ActorId:    actorId,
			VendorName: beCasperPb.OfferVendor_DPANDA,
			TargetUrl:  externalVendorOfferMetadata.GetBaseUrl(),
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			logger.Error(ctx, "error while fetching poshVine's redirection url", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			return nil, err
		}
		webpageUrl = resp.GetWebpageUrl()
	case beCasperPb.OfferVendor_POSHVINE:
		resp, err := r.offerCatalogClient.GetExternalVendorDynamicWebpageUrl(ctx, &beCasperPb.GetExternalVendorDynamicWebpageUrlRequest{
			ActorId:    actorId,
			VendorName: beCasperPb.OfferVendor_POSHVINE,
			TargetUrl:  externalVendorOfferMetadata.GetBaseUrl(),
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			logger.Error(ctx, "error while fetching poshVine's redirection url", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
			return nil, err
		}
		webpageUrl = resp.GetWebpageUrl()
	default:
		return nil, fmt.Errorf("invalid vendor for external vendor offer type: %s", offerVendor)
	}

	webPageDeeplink := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_WEB_PAGE,
		ScreenOptions: &deeplinkPb.Deeplink_WebPageScreenOptions{
			WebPageScreenOptions: &deeplinkPb.WebpageScreenOptions{
				WebpageTitle:             externalVendorOfferMetadata.GetWebPageTitle(),
				WebpageUrl:               webpageUrl,
				DisableHardwareBackPress: true,
			},
		},
	}
	if r.dyconf.RewardsFrontendMeta().IsWebPageWithCardDetailsScreenEnabled() {
		appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
		if (appPlatform == commontypes.Platform_ANDROID && appVersion >= int(r.dyconf.RewardsFrontendMeta().MinAndroidVersionForWebPageWithCardDetailsScreen())) ||
			(appPlatform == commontypes.Platform_IOS && appVersion >= int(r.dyconf.RewardsFrontendMeta().MinIosVersionForWebPageWithCardDetailsScreen())) {
			var (
				debitCardId          string
				showDebitCardDetails bool
			)
			// fetch debit card id to pass in deeplink to show debit card details in webpage.
			cards, cardFetchErr := r.cardClient.FetchCards(ctx, &provisioning.FetchCardsRequest{
				Actor:            &types.Actor{Id: actorId},
				IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
				CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
				CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
				CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
				CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
				SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
			})
			switch rpcErr := epifigrpc.RPCError(cards, cardFetchErr); {
			case rpc.StatusFromError(rpcErr).IsRecordNotFound():
				showDebitCardDetails = false
			case rpcErr != nil:
				logger.Error(ctx, "error while fetching debit card details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
				return nil, rpcErr
			case len(cards.GetCards()) == 0:
				showDebitCardDetails = false
			default:
				showDebitCardDetails = true
				debitCardId = cards.GetCards()[0].GetId()
			}
			webPageDeeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_WEB_PAGE_WITH_CARD_DETAILS_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&pkgScreenOptionsPb.WebPageWithCardDetailsScreenOptions{
					WebpageTitle:             externalVendorOfferMetadata.GetWebPageTitle(),
					WebpageUrl:               webpageUrl,
					DisableHardwareBackPress: true,
					DisableDropDownAnimation: true,
					ShowUpiDetails:           true,
					ShowCreditCardDetails:    true,
					ShowDebitCardDetails:     showDebitCardDetails,
					DebitCardId:              debitCardId,
				}),
			}
		}
	}

	return webPageDeeplink, nil
}
