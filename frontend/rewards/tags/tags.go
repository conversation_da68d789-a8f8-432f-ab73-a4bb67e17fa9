package tags

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"github.com/mohae/deepcopy"
	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/accrual"

	beCasperPb "github.com/epifi/gamma/api/casper"
	feRewardsPb "github.com/epifi/gamma/api/frontend/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"

	"github.com/epifi/gamma/frontend/config/genconf"
)

type tagDetails struct {
	TagName beCasperPb.TagName
	// GenericTagName is the tag which may be comprised of multiple different enum tags.
	// This is being done so that if any view as filters of multiple type but looks and works
	// the same can be converted using a single implementation unlike using TagName which will
	// only be valid for a single type of tag.
	GenericTagName  string
	DisplayText     string
	ImageUrl        string
	TextColor       string
	BackgroundColor string
	Priority        uint32
	TextFontStyle   commontypes.FontStyle
	TopPadding      int32
	BottomPadding   int32
	LeftPadding     int32
	RightPadding    int32
	CornerRadius    int32
	ImageHeight     int32
	ImageWidth      int32
}

type IManager interface {
	GetTagsDetailsOrderedByPriority(ctx context.Context, tags []beCasperPb.TagName, renderLocation RenderLocation) ([]*ui.IconTextComponent, []beCasperPb.TagName, error)
	GetGenericTagsDetailsOrderedByPriority(ctx context.Context, tagList []string, renderLocation RenderLocation) ([]*ui.IconTextComponent, []string, error)
	// Deprecated method. Use GetActiveCatalogTagFilterDetailsV2 instead.
	// deprecated because it doesn't return the IT component with container and border properties.
	GetActiveCatalogTagFilterDetails(inactiveCatalogTagFilterDetails *ui.IconTextComponent) *ui.IconTextComponent
	GetActiveCatalogTagFilterDetailsV2(tag beCasperPb.TagName, renderLocation RenderLocation) (*ui.IconTextComponent, error)
	GetVerticalTagFiltersOrderedByPriority(ctx context.Context, tagList []string, renderLocation RenderLocation, changeTagPriorityTag string, tagPriority uint32) ([]*feRewardsPb.VerticalCatalogTagFilter, error)
	GetTagsOrderedByPriority(ctx context.Context, tagList []string, renderLocation RenderLocation, changeTagPriorityTag string, tagPriority uint32) ([]string, []string)
}

type Manager struct {
	dyconf *genconf.Config
}

func NewManager(dyconf *genconf.Config) IManager {
	return &Manager{dyconf: dyconf}
}

// GetTagsDetailsOrderedByPriority returns tags' display details ordered by their
// priority. since the unique TagName identifier gets lost in the process and
// there's no way for the function caller to know the TagName to tag details
// mapping, this function returns a list of ordered tag names as a 2nd parameter
// which is a 1-to-1 mapping to the tag display details list.
func (m *Manager) GetTagsDetailsOrderedByPriority(ctx context.Context, tags []beCasperPb.TagName, renderLocation RenderLocation) ([]*ui.IconTextComponent, []beCasperPb.TagName, error) {
	if renderLocation == RenderLocationUnspecified {
		logger.WarnWithCtx(ctx, "render location passed as unspecified")
		return nil, nil, nil
	}

	var tagsDetails []*tagDetails

	for _, tag := range tags {
		tagDisplayDetails, err := m.getRenderLocationSpecificTagDetails(tag.String(), renderLocation)
		if err != nil {
			// logger.Debug(
			// 	ctx,
			// 	"error while fetching tag's display details specific to renderLocation.",
			// 	zap.Error(err),
			// 	zap.String("tagName", tag.String()),
			// 	zap.String("renderLocation", string(renderLocation)),
			// )
			continue
		}
		tagsDetails = append(tagsDetails, tagDisplayDetails)
	}

	// order tagsDetails by priority
	sort.Slice(tagsDetails, func(i, j int) bool {
		return tagsDetails[i].Priority < tagsDetails[j].Priority
	})

	var orderedTagsNames []beCasperPb.TagName

	// generate FE tags from tagsDetails
	var feTags []*ui.IconTextComponent
	for _, singleTagDetails := range tagsDetails {
		var (
			leftVisualElement *commontypes.VisualElement
			leftIcon          *commontypes.Image
			leftImgTxtPadding int32
		)
		if singleTagDetails.ImageUrl != "" {
			leftImgTxtPadding = 4
			if singleTagDetails.ImageHeight != 0 && singleTagDetails.ImageWidth != 0 {
				leftVisualElement = commontypes.GetVisualElementFromUrlHeightAndWidth(singleTagDetails.ImageUrl, singleTagDetails.ImageHeight, singleTagDetails.ImageWidth)
			} else {
				leftIcon = &commontypes.Image{ImageUrl: singleTagDetails.ImageUrl}
			}
		}

		feTags = append(feTags, &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    singleTagDetails.TextColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: singleTagDetails.DisplayText},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: singleTagDetails.TextFontStyle},
				},
			},
			LeftImgTxtPadding: leftImgTxtPadding,
			LeftVisualElement: leftVisualElement,
			LeftIcon:          leftIcon,
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: singleTagDetails.BackgroundColor, CornerRadius: singleTagDetails.CornerRadius, LeftPadding: singleTagDetails.LeftPadding, RightPadding: singleTagDetails.RightPadding, TopPadding: singleTagDetails.TopPadding, BottomPadding: singleTagDetails.BottomPadding},
		})

		orderedTagsNames = append(orderedTagsNames, singleTagDetails.TagName)
	}

	return feTags, orderedTagsNames, nil
}

func (m *Manager) GetGenericTagsDetailsOrderedByPriority(ctx context.Context, tagList []string, renderLocation RenderLocation) ([]*ui.IconTextComponent, []string, error) {
	if renderLocation == RenderLocationUnspecified {
		logger.WarnWithCtx(ctx, "render location passed as unspecified")
		return nil, nil, nil
	}

	var tagsDetails []*tagDetails

	for _, tag := range tagList {
		tagDisplayDetails, err := m.getRenderLocationSpecificTagDetails(tag, renderLocation)
		if err != nil {
			logger.Debug(
				ctx,
				"error while fetching tag's display details specific to renderLocation.",
				zap.Error(err),
				zap.String("tagName", tag),
				zap.String("renderLocation", string(renderLocation)),
			)
			continue
		}
		tagsDetails = append(tagsDetails, tagDisplayDetails)
	}

	// order tagsDetails by priority
	sort.Slice(tagsDetails, func(i, j int) bool {
		return tagsDetails[i].Priority < tagsDetails[j].Priority
	})

	// generate FE tags from tagsDetails
	var (
		feTags                   []*ui.IconTextComponent
		tagListOrderedByPriority []string
	)
	for _, singleTagDetails := range tagsDetails {
		var (
			leftVisualElement *commontypes.VisualElement
			leftIcon          *commontypes.Image
			leftImgTxtPadding int32
		)
		if singleTagDetails.ImageUrl != "" {
			leftImgTxtPadding = 4
			if singleTagDetails.ImageHeight != 0 && singleTagDetails.ImageWidth != 0 {
				leftVisualElement = commontypes.GetVisualElementFromUrlHeightAndWidth(singleTagDetails.ImageUrl, singleTagDetails.ImageHeight, singleTagDetails.ImageWidth)
			} else {
				leftIcon = &commontypes.Image{ImageUrl: singleTagDetails.ImageUrl}
			}
		}

		feTags = append(feTags, &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    singleTagDetails.TextColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: singleTagDetails.DisplayText},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: singleTagDetails.TextFontStyle},
				},
			},
			LeftImgTxtPadding: leftImgTxtPadding,
			LeftVisualElement: leftVisualElement,
			LeftIcon:          leftIcon,
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: singleTagDetails.BackgroundColor, CornerRadius: singleTagDetails.CornerRadius, LeftPadding: singleTagDetails.LeftPadding, RightPadding: singleTagDetails.RightPadding, TopPadding: singleTagDetails.TopPadding, BottomPadding: singleTagDetails.BottomPadding},
		})

		tagListOrderedByPriority = append(tagListOrderedByPriority, singleTagDetails.GenericTagName)
	}

	return feTags, tagListOrderedByPriority, nil
}

func (m *Manager) getRenderLocationSpecificTagDetails(tagName string, renderLocation RenderLocation) (*tagDetails, error) {
	tagNameInLowerCase := strings.ToLower(tagName)
	renderLocationConfigName := strings.ToLower(string(renderLocation))
	tagNameToConfigMap := m.dyconf.RewardsFrontendMeta().TagsConfig().TagNameToConfigMap()
	tagDisplayConfig, ok := tagNameToConfigMap.Load(tagNameInLowerCase)
	if !ok {
		return nil, fmt.Errorf("tag config not found. tagName: %s", tagNameInLowerCase)
	}

	tagConfig, ok := tagDisplayConfig.RenderLocationToDisplayDetailsMap().Load(renderLocationConfigName)
	if !ok {
		return nil, fmt.Errorf("render location specific tag details not found. tagName: %s, renderLocation: %s", tagNameInLowerCase, string(renderLocation))
	}

	fontStyle := RenderLocationToFontStyleMap[renderLocation]
	if fontStyle == commontypes.FontStyle_FONT_STYLE_UNSPECIFIED {
		fontStyle = commontypes.FontStyle_SUBTITLE_S
	}

	renderLocationSpecificDetails := tagDisplayProperties{CornerRadius: 16, LeftPadding: 6, RightPadding: 6, TopPadding: 2, BottomPadding: 2}
	if props, exist := RenderLocationToTagDisplayPropertiesMap[renderLocation]; exist {
		renderLocationSpecificDetails = props
	}

	return &tagDetails{
		TagName:         beCasperPb.TagName(beCasperPb.TagName_value[tagName]),
		GenericTagName:  tagName,
		DisplayText:     accrual.ReplaceCoinWithPointIfApplicable(tagConfig.DisplayText()),
		ImageUrl:        tagConfig.ImageUrl(),
		TextColor:       tagConfig.TextColor(),
		BackgroundColor: tagConfig.BackgroundColor(),
		Priority:        tagConfig.Priority(),
		TextFontStyle:   fontStyle,
		TopPadding:      renderLocationSpecificDetails.TopPadding,
		BottomPadding:   renderLocationSpecificDetails.BottomPadding,
		LeftPadding:     renderLocationSpecificDetails.LeftPadding,
		RightPadding:    renderLocationSpecificDetails.RightPadding,
		CornerRadius:    renderLocationSpecificDetails.CornerRadius,
		ImageHeight:     renderLocationSpecificDetails.ImageHeight,
		ImageWidth:      renderLocationSpecificDetails.ImageWidth,
	}, nil
}

// GetActiveCatalogTagFilterDetails returns tag filter with active state colors.
// this is needed as we are using different colors in active and inactive state
// of filters.
func (m *Manager) GetActiveCatalogTagFilterDetails(inactiveCatalogTagFilterDetails *ui.IconTextComponent) *ui.IconTextComponent {
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				FontColor:    "#333333",
				BgColor:      "#FFFFFF",
				DisplayValue: inactiveCatalogTagFilterDetails.GetTexts()[0].GetDisplayValue(),
				FontStyle:    inactiveCatalogTagFilterDetails.GetTexts()[0].GetFontStyle(),
			},
		},
		LeftIcon: inactiveCatalogTagFilterDetails.GetLeftIcon(),
	}
}

// GetActiveCatalogTagFilterDetailsV2 returns tag filter with active state color, border.
// this is needed as we are using different colors in active and inactive state
// of filters.
func (m *Manager) GetActiveCatalogTagFilterDetailsV2(tag beCasperPb.TagName, renderLocation RenderLocation) (*ui.IconTextComponent, error) {
	if renderLocation == RenderLocationUnspecified {
		return nil, fmt.Errorf("render location passed as unspecified")
	}

	renderLocationConfigName := strings.ToLower(string(renderLocation))
	tagNameToConfigMap := m.dyconf.RewardsFrontendMeta().TagsConfig().TagNameToConfigMap()
	tagDisplayConfig, ok := tagNameToConfigMap.Load(strings.ToLower(tag.String()))
	if !ok {
		return nil, fmt.Errorf("tag config not found. tagName: %s", tag.String())
	}

	tagConfig, ok := tagDisplayConfig.RenderLocationToDisplayDetailsMap().Load(renderLocationConfigName)
	if !ok {
		return nil, fmt.Errorf("render location specific tag details not found. tagName: %s, renderLocation: %s", tag.String(), string(renderLocation))
	}

	fontStyle := RenderLocationToFontStyleMap[renderLocation]
	if fontStyle == commontypes.FontStyle_FONT_STYLE_UNSPECIFIED {
		fontStyle = commontypes.FontStyle_SUBTITLE_S
	}

	renderLocationSpecificDetails := tagDisplayProperties{CornerRadius: 19, LeftPadding: 12, RightPadding: 12, TopPadding: 12, BottomPadding: 12}
	if props, exist := RenderLocationToTagDisplayPropertiesMap[renderLocation]; exist {
		renderLocationSpecificDetails = props
	}

	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				FontColor:    tagConfig.TextColor(),
				DisplayValue: &commontypes.Text_PlainString{PlainString: accrual.ReplaceCoinWithPointIfApplicable(tagConfig.DisplayText())},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: fontStyle},
			},
		},
		LeftImgTxtPadding:  4,
		RightImgTxtPadding: 4,
		LeftVisualElement:  commontypes.GetVisualElementFromUrlHeightAndWidth(tagConfig.ImageUrl(), renderLocationSpecificDetails.ImageHeight, renderLocationSpecificDetails.ImageWidth),
		RightVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/close-icon.png", renderLocationSpecificDetails.ImageHeight, renderLocationSpecificDetails.ImageWidth),
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:       tagConfig.BackgroundColor(),
			CornerRadius:  renderLocationSpecificDetails.CornerRadius,
			LeftPadding:   renderLocationSpecificDetails.LeftPadding,
			RightPadding:  renderLocationSpecificDetails.RightPadding,
			TopPadding:    renderLocationSpecificDetails.TopPadding,
			BottomPadding: renderLocationSpecificDetails.BottomPadding,
			BorderWidth:   2,
			BorderColor:   colors.ColorForest,
		},
	}, nil
}

// GetVerticalTagFiltersOrderedByPriority returns vertical tag filters ordered by their priority based on the render location
// todo : refactor this to be more configurable
func (m *Manager) GetVerticalTagFiltersOrderedByPriority(ctx context.Context, tagList []string, renderLocation RenderLocation, changeTagPriorityTag string, tagPriority uint32) ([]*feRewardsPb.VerticalCatalogTagFilter, error) {
	if renderLocation == RenderLocationUnspecified {
		logger.WarnWithCtx(ctx, "render location passed as unspecified")
		return nil, nil
	}

	var tagsDetails []*tagDetails

	for _, tag := range tagList {
		tagDisplayDetails, err := m.getRenderLocationSpecificTagDetails(tag, renderLocation)
		if err != nil {
			logger.Debug(ctx, "error while fetching tag's display details specific to renderLocation", zap.Error(err), zap.String("tagName", tag), zap.String("renderLocation", string(renderLocation)))
			continue
		}
		if tag == changeTagPriorityTag {
			tagDisplayDetails.Priority = tagPriority
		}
		tagsDetails = append(tagsDetails, tagDisplayDetails)
	}

	sort.Slice(tagsDetails, func(i, j int) bool {
		return tagsDetails[i].Priority < tagsDetails[j].Priority
	})

	var feTags []*feRewardsPb.VerticalCatalogTagFilter

	for _, singleTagDetails := range tagsDetails {
		inactiveCta := fetchInactiveTagFilterDetails(singleTagDetails)
		activeCta := fetchActiveTagFilterDetails(inactiveCta)

		feTags = append(feTags, &feRewardsPb.VerticalCatalogTagFilter{
			TagName:           singleTagDetails.GenericTagName,
			InactiveFilterCta: inactiveCta,
			ActiveFilterCta:   activeCta,
		})
	}

	return feTags, nil
}

func fetchInactiveTagFilterDetails(singleTagDetails *tagDetails) *ui.VerticalKeyValuePair {
	return &ui.VerticalKeyValuePair{
		Title: &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(singleTagDetails.ImageUrl, singleTagDetails.ImageHeight, singleTagDetails.ImageWidth),
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       singleTagDetails.BackgroundColor,
				CornerRadius:  singleTagDetails.CornerRadius,
				LeftPadding:   singleTagDetails.LeftPadding,
				RightPadding:  singleTagDetails.RightPadding,
				TopPadding:    singleTagDetails.TopPadding,
				BottomPadding: singleTagDetails.BottomPadding,
				Height:        singleTagDetails.ImageHeight,
				Width:         singleTagDetails.ImageWidth,
			},
		},
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor:    singleTagDetails.TextColor,
					DisplayValue: &commontypes.Text_PlainString{PlainString: singleTagDetails.DisplayText},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: singleTagDetails.TextFontStyle},
					MaxLines:     2,
					Alignment:    commontypes.Text_ALIGNMENT_CENTER,
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				Height: 16,
				Width:  90,
			},
		},
		VerticalPaddingBtwTitleValue: 8,
		HAlignment:                   ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_CENTER,
	}
}

func fetchActiveTagFilterDetails(inactiveCta *ui.VerticalKeyValuePair) *ui.VerticalKeyValuePair {
	activeCta := deepcopy.Copy(inactiveCta).(*ui.VerticalKeyValuePair)

	// update activeCta to have active state for icon
	activeCta.GetTitle().GetContainerProperties().BgColor = colors.ColorSnow
	activeCta.GetTitle().GetContainerProperties().BorderColor = colors.ColorForest
	activeCta.GetTitle().GetContainerProperties().BorderWidth = 2

	// should not happen,added to avoid panic
	if len(activeCta.GetValue().GetTexts()) == 0 {
		return activeCta
	}

	// update activeCta to have active state for text
	activeCta.GetValue().GetTexts()[0].FontColor = colors.ColorOnDarkHighEmphasis

	return activeCta
}

// GetTagsOrderedByPriority returns tags and their display names ordered by their priority based on the render location
func (m *Manager) GetTagsOrderedByPriority(ctx context.Context, tagList []string, renderLocation RenderLocation, changeTagPriorityTag string, tagPriority uint32) ([]string, []string) {
	if renderLocation == RenderLocationUnspecified {
		logger.WarnWithCtx(ctx, "render location passed as unspecified")
		return nil, nil
	}

	var tagsDetails []*tagDetails

	for _, tag := range tagList {
		tagDisplayDetails, err := m.getRenderLocationSpecificTagDetails(tag, renderLocation)
		if err != nil {
			logger.Debug(ctx, "error while fetching tag's display details specific to renderLocation", zap.Error(err), zap.String("tagName", tag), zap.String("renderLocation", string(renderLocation)))
			continue
		}
		if tag == changeTagPriorityTag {
			tagDisplayDetails.Priority = tagPriority
		}
		tagsDetails = append(tagsDetails, tagDisplayDetails)
	}

	sort.Slice(tagsDetails, func(i, j int) bool {
		return tagsDetails[i].Priority < tagsDetails[j].Priority
	})

	return getTagNamesFromTagDetails(tagsDetails)
}

func getTagNamesFromTagDetails(details []*tagDetails) ([]string, []string) {
	var tagNames, tagDisplayNames []string
	for _, detail := range details {
		tagNames = append(tagNames, detail.GenericTagName)
		tagDisplayNames = append(tagDisplayNames, detail.DisplayText)
	}
	return tagNames, tagDisplayNames
}
