package transaction

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"

	"github.com/golang/protobuf/proto"

	beUPIPb "github.com/epifi/gamma/api/upi"
	payPkgTxns "github.com/epifi/gamma/pay/pkg/transactions"
	"github.com/epifi/gamma/pkg/pay"

	"github.com/epifi/be-common/pkg/epifigrpc"

	actorPb "github.com/epifi/gamma/api/actor"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/frontend/account/upi"
	"github.com/epifi/gamma/api/frontend/header"
	errors2 "github.com/epifi/gamma/pkg/frontend/errors"

	"github.com/golang/protobuf/ptypes"
	"github.com/golang/protobuf/ptypes/duration"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	beAuthPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/frontend/pay/transaction"
	beOrderPb "github.com/epifi/gamma/api/order"
	bePaymentPb "github.com/epifi/gamma/api/order/payment"
)

var (
	statusPaymentFailed rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatusWithoutDebug(
			uint32(transaction.InitiatePaymentResponse_PAYMENT_FAILED),
			PAYMENT_FAILED,
		)
	}
	statusCoolOffValidationFailedInitiatePay rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(
			uint32(transaction.CreateFundTransferOrderResponse_COOL_OFF_VALIDATION_FAILED),
			"Device is in cool off period. Total amount of the transaction should be less then 5k",
			"Device is in cool off period. Total amount of the transaction should be less then 5k",
		)
	}
)

// InitiatePayment creates transactions and initiates its payment in the system.
// It does the following operation in the specified sequence-
//  1. Validate and get order
//     Following validations are performed against the order
//     a. Does the actor have permission to access the order?
//     b. Is order still payable? Has payment been done already or is a payment in progress?
//     c. Can a payment be made against the order
//     Orders such as digital gold payments have prices locked for x minutes. For such orders, payments
//     have to be done within the time frame.
//     d. Is transactionID part of the order
//  2. checks if payment is blocked and returns permission denied
//  3. check if the payment protocol has changed from the one in order payload
//  4. In case no, use the details in the payload to create transaction
//  5. In case yes, call decision engine to take a decision but this time with a preferred payment protocol
//     and create transaction based on the response.
//  6. Initiate the payment with the payment service
//
// nolint:funlen
func (s *Service) InitiatePayment(ctx context.Context, req *transaction.InitiatePaymentRequest) (*transaction.InitiatePaymentResponse, error) {
	res := &transaction.InitiatePaymentResponse{
		RespHeader: &header.ResponseHeader{},
	}
	res.Status = &rpc.Status{}
	actorId := req.GetReq().GetAuth().GetActorId()
	var ph *commontypes.PhoneNumber

	order, _, orderValidationStatus := s.beValidateAndGetOrder(ctx, actorId, req.OrderId, req.GetReq().GetAuth().GetDevice())
	switch {
	case orderValidationStatus.GetCode() == statusCoolOffValidationFailed().GetCode():
		res.Status = statusCoolOffValidationFailedInitiatePay()
		res.ErrorView = proto.Clone(CoolOffValidationFailedErrorView).(*transaction.ErrorView)
		res.ErrorView.SubTitle = orderValidationStatus.GetShortMessage()
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		return res, nil
	case orderValidationStatus != nil:
		res.Status = orderValidationStatus
		res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		return res, nil
	}

	// in case payee actor has blocked or is blocked by current actor payment is not allowed
	if err := s.isPaymentBlocked(ctx, actorId, order.GetToActorId()); err != nil {
		if errors.Is(err, errPaymentBlocked) {
			logger.Info(ctx, "payment blocked for payee actor", zap.String("payeeActorId", order.GetToActorId()))
			res.Status = rpc.StatusPermissionDenied()
			res.ErrorView = proto.Clone(PaymentBlocked).(*transaction.ErrorView)
			res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		} else {
			res.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
			res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		}
		return res, nil
	}

	paymentDetails, err := pay.GetPaymentDetailsFromPayload(order)
	if err != nil {
		logger.Error(ctx, "failed while fetching order payload", zap.String("order-id", order.Id), zap.Error(err))
		res.Status = rpc.StatusInternal()
		res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		return res, nil
	}

	// checking if location is mandatory for the protocol
	if req.GetReq().GetAuth().GetDevice().GetLocationToken() == "" &&
		s.isLocationMandatoryForProtocol(paymentDetails.GetPaymentProtocol()) {
		logger.Error(ctx, "location not passed for location mandatory protocol",
			zap.String(logger.PAYMENT_PROTOCOL, paymentDetails.GetPaymentProtocol().String()))
		res.Status = rpc.StatusInvalidArgument()
		res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
	}

	piVerifyStatus, errorView := s.verifyPI(ctx, paymentDetails.GetPiFrom(), paymentDetails.GetPiTo(), order.IsCollectRequest())
	if piVerifyStatus != nil {
		res.ErrorView = errorView
		res.Status = piVerifyStatus
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		return res, nil
	}

	// Supports backward compatibility
	// Do not accept both deprecated and new one
	// Optimise when deprecated field is unsupported
	var (
		npciCredBlock  *upi.CredBlock
		npciCredBlocks *transaction.NpciCredBlocks
	)
	switch {
	case req.GetNpciCredBlocks() != nil:
		npciCredBlocks = req.GetNpciCredBlocks()
	case req.GetCredential() != nil:
		npciCredBlock = req.GetNpciCredBlock()
	default:
		npciCredBlock = req.GetCredBlock()
	}

	if paymentDetails.GetPaymentProtocol() == bePaymentPb.PaymentProtocol_UPI &&
		npciCredBlock == nil && npciCredBlocks == nil {
		logger.Error(ctx, "credblock missing for UPI transaction")
		res.Status = rpc.StatusInvalidArgument()
		res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		return res, nil
	}

	// TODO(nitesh): create transaction and initiate payment can be merged into one single RPC.
	// transaction needs not be created for a P2P_COLLECT. This is so as for incoming
	// P2P_COLLECT order, transaction is already pre-created.
	if order.GetWorkflow() != beOrderPb.OrderWorkflow_P2P_COLLECT {
		createTxnReq := &bePaymentPb.CreateTransactionRequest{
			PiFrom:  paymentDetails.GetPiFrom(),
			PiTo:    paymentDetails.GetPiTo(),
			Amount:  paymentDetails.GetAmount(),
			Remarks: paymentDetails.GetRemarks(),
			OrderId: req.OrderId,
			ReqInfo: &bePaymentPb.PaymentRequestInformation{
				ReqId: req.TransactionId,
				UpiInfo: &bePaymentPb.PaymentRequestInformation_UPI{
					MerchantRefId:  req.GetMerchantRefId(),
					RefUrl:         paymentDetails.GetReferenceUrl(),
					InitiationMode: paymentDetails.GetInitiationMode(),
					Purpose:        paymentDetails.GetPurpose(),
					MerchantDetails: &beUPIPb.MerchantDetails{
						MerchantId:         paymentDetails.GetMerchantId(),
						MerchantStoreId:    paymentDetails.GetMerchantStoreId(),
						MerchantTerminalId: paymentDetails.GetMerchantTerminalId(),
					},
					OrgId:              paymentDetails.GetOrgId(),
					Mcc:                paymentDetails.GetMcc(),
					TxnOriginTimestamp: ptypes.TimestampNow(),
					TransactionType:    beUPIPb.TransactionType_PAY,
					PayerUpiNumber:     req.GetPayerUpiNumber(),
					PayeeUpiNumber:     req.GetPayeeUpiNumber(),
				},
			},
			PaymentProtocol:         paymentDetails.GetPaymentProtocol(),
			Status:                  bePaymentPb.TransactionStatus_CREATED,
			BaseAmountQuoteCurrency: req.GetBaseAmountQuoteCurrency().GetBeMoney(),
			CurrentActorId:          req.GetReq().GetAuth().GetActorId(),
		}

		if createTxnReq.Ownership, err = payPkgTxns.GetOwnershipForTheTxn(ctx, paymentDetails.GetPiFrom(),
			paymentDetails.GetPiTo(), paymentDetails.GetPaymentProtocol(), s.piClient, s.conf.NonTpapPspHandles()); err != nil {
			logger.Error(ctx, "error deciding ownership for the transaction", zap.String(logger.ORDER_ID, order.GetId()),
				zap.Error(err))
			res.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
			res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		}

		createTxnStatus := s.beCreateTransaction(ctx, createTxnReq)
		if createTxnStatus != nil {
			res.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
			res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
			return res, nil
		}
	}

	// Using actor id, get device details
	deviceToken, userProfileId, getDeviceAuthDetailsStatus := s.beGetDeviceAuthDetails(ctx, actorId)
	if !getDeviceAuthDetailsStatus.IsSuccess() {
		res.Status = rpc.StatusInternal()
		res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		return res, nil
	}

	// in case of deposit payment, we need customer id for checking if both account belong to same user
	// and for other case customerId will be blank
	customerId := ""
	if beOrderPb.IsTagExist(order.GetTags(), beOrderPb.OrderTag_DEPOSIT) {
		customerId, err = s.getCustomerId(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "error in getting customerId",
				zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
			res.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
			res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
			return res, nil
		}
	}

	if paymentDetails.GetPaymentProtocol() == bePaymentPb.PaymentProtocol_UPI {
		var getPhStatus *rpc.Status
		ph, getPhStatus = s.beGetActorPhoneNumber(ctx, req.GetReq().GetAuth().GetActorId())
		if getPhStatus != nil {
			res.Status = getPhStatus
			return res, nil
		}
	}

	initiateTxnReq := &bePaymentPb.InitiateTransactionRequest{
		PaymentAuth: &bePaymentPb.InitiateTransactionRequest_AuthHeader{
			Device:        beUPIPb.FromFeDevice(ctx, req.GetReq().GetAuth().GetDevice(), ph),
			DeviceToken:   deviceToken,
			UserProfileId: userProfileId,
			CustomerId:    customerId,
		},
		TransactionReqId: req.GetTransactionId(),
		OrderId:          req.GetOrderId(),
		// Since for internal system `COLLECT_SHORT_CIRCUIT` goes as normal payment hence
		// we set the flag `IsCollectRequest` as false while initiating payment
		// for a `COLLECT_SHORT_CIRCUIT` order
		IsCollectRequest:        order.GetWorkflow() == beOrderPb.OrderWorkflow_P2P_COLLECT,
		ActorId:                 actorId,
		Urn:                     req.GetUrn(),
		BaseAmountQuoteCurrency: req.GetBaseAmountQuoteCurrency().GetBeMoney(),
		OrchestrationMetadata:   req.GetOrchestrationMetadata(),
	}

	if paymentDetails.GetPaymentProtocol() == bePaymentPb.PaymentProtocol_UPI {
		if npciCredBlocks != nil {
			initiateTxnReq.PaymentAuth.Credential = &bePaymentPb.InitiateTransactionRequest_AuthHeader_NpciCredBlocks{
				NpciCredBlocks: ConvertToBeNpciCredBlocks(npciCredBlocks),
			}
		} else {
			initiateTxnReq.PaymentAuth.Credential = &bePaymentPb.InitiateTransactionRequest_AuthHeader_NpciCredBlock{
				NpciCredBlock: npciCredBlock.ConvertToBECredBlock(),
			}
		}
	} else {
		initiateTxnReq.PaymentAuth.Credential = &bePaymentPb.InitiateTransactionRequest_AuthHeader_PartnerSdkCredBlock{
			PartnerSdkCredBlock: req.GetPartnerSdkCredBlock(),
		}
		// todo(Harleen Singh): remove this post debugging the invalid cred block issue
		logger.Info(ctx, fmt.Sprintf("length of partner sdk cred block: %v", len(req.GetPartnerSdkCredBlock())), zap.String(logger.ORDER_ID, req.GetOrderId()))
	}

	if payStatus, errorView := s.beInitiateTransaction(ctx, initiateTxnReq); payStatus != nil {
		res.Status = payStatus
		res.ErrorView = errorView
		res.RespHeader.ErrorView = errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView())
		return res, nil
	}

	res.Status = rpc.StatusOk()
	// TODO(pruthvi): Add retry limit checks
	res.StatusTimer = &duration.Duration{Seconds: 1}
	return res, nil
}

// beGetDeviceAuthDetails fetches device details for a given actorId
// Returns rpc.Status along with device details, caller to take action depending on this status code
func (s *Service) beGetDeviceAuthDetails(ctx context.Context, currentActorId string) (deviceToken, userProfileId string, status *rpc.Status) {
	authResp, err := s.authClient.GetDeviceAuth(ctx, &beAuthPb.GetDeviceAuthRequest{
		ActorId: currentActorId,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "authClient.GetDeviceAuth() failed",
			zap.String("actor-id", currentActorId),
			zap.Error(err))
		return "", "", rpc.StatusInternal()

	case !authResp.GetStatus().IsSuccess():
		logger.Error(ctx, fmt.Sprintf("authClient.GetDeviceAuth() failed with status: %s", authResp.GetStatus()),
			zap.String("actor-id", currentActorId))
		return "", "", rpc.StatusInternal()
	default:
		return authResp.GetDeviceToken(),
			authResp.GetUserProfileId(), rpc.StatusOk()
	}
}

// return federal customerId for a given actorId otherwise error if customerId doesn't exist or any error encounter
func (s *Service) getCustomerId(ctx context.Context, actorId string) (string, error) {
	bankCustomerInfo, errResp := s.bankCustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if er := epifigrpc.RPCError(bankCustomerInfo, errResp); er != nil {
		return "", fmt.Errorf("cannot find bankcustomer details for actorId %v, found error: %w", actorId, er)
	}
	return bankCustomerInfo.GetBankCustomer().GetVendorCustomerId(), nil
}

// beGetActorEntityDetails returns actors entity information like profile image url, name, phone number, etc.
func (s *Service) beGetActorPhoneNumber(ctx context.Context, actorId string) (*commontypes.PhoneNumber, *rpc.Status) {
	res, status := s.beGetActorEntityDetails(ctx, actorId)
	if status != nil {
		return nil, status
	}

	return res.GetMobileNumber(), nil
}

// beGetActorEntityDetails returns actors entity information like profile image url, name, phone number, etc.
func (s *Service) beGetActorEntityDetails(ctx context.Context, actorId string) (*actorPb.GetEntityDetailsByActorIdResponse, *rpc.Status) {
	res, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	switch {
	case err != nil:
		logger.Error(ctx, "failed to get actor entity details", zap.Error(err))
		return nil, rpc.StatusInternal()
	case !res.GetStatus().IsSuccess():
		logger.Error(ctx, "failed to get actor entity details")
		return nil, rpc.StatusInternal()
	}

	return res, nil
}

// ConvertToBeNpciCredBlocks - converts the FE NpciCredBlocks to BE
func ConvertToBeNpciCredBlocks(npciCredBlocks *transaction.NpciCredBlocks) *bePaymentPb.NpciCredBlocks {
	var credBlocks []*beUPIPb.CredBlock
	for _, credBlock := range npciCredBlocks.GetCredBlocks() {
		credBlocks = append(credBlocks, credBlock.ConvertToBECredBlock())
	}

	return &bePaymentPb.NpciCredBlocks{
		CredBlocks: credBlocks,
	}
}
