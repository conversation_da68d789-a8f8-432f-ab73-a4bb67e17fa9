package transaction

import (
	"github.com/samber/lo"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	cardPb "github.com/epifi/gamma/api/card/provisioning"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	fireflyv2 "github.com/epifi/gamma/api/firefly/v2"
	inappreferralPb "github.com/epifi/gamma/api/inappreferral"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	"github.com/epifi/gamma/api/recurringpayment/paymentgateway"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	userIntelPb "github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options"
	dataCollector "github.com/epifi/gamma/frontend/pay/transaction/payment_options/data_collector"
	"github.com/epifi/gamma/frontend/pay/transaction/payment_options/providers"
	tieringData "github.com/epifi/gamma/frontend/tiering/data_collector"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	operstatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/categorizer"
	fePayPb "github.com/epifi/gamma/api/frontend/pay"
	"github.com/epifi/gamma/api/frontend/pay/transaction"
	"github.com/epifi/gamma/api/health_engine"
	beOrderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardOfferPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	savingsPb "github.com/epifi/gamma/api/savings"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	timelinePb "github.com/epifi/gamma/api/timeline"
	upiPb "github.com/epifi/gamma/api/upi"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	"github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/pay/transaction/validator"
	tieringAddFunds "github.com/epifi/gamma/frontend/tiering/add_funds"
	tieringDeeplink "github.com/epifi/gamma/frontend/tiering/deeplink"
	"github.com/epifi/gamma/frontend/tiering/feedback_flow_integration"
	"github.com/epifi/gamma/pkg/feature/release"
)

// between backend status and frontend status
var (
	beStatusToFeStatus = map[beOrderPb.OrderStatus]fePayPb.OrderStatus{
		beOrderPb.OrderStatus_CREATED:                    fePayPb.OrderStatus_PAYMENT_IN_PROGRESS,
		beOrderPb.OrderStatus_IN_PAYMENT:                 fePayPb.OrderStatus_PAYMENT_IN_PROGRESS,
		beOrderPb.OrderStatus_PAID:                       fePayPb.OrderStatus_PAYMENT_SUCCESS,
		beOrderPb.OrderStatus_PAYMENT_FAILED:             fePayPb.OrderStatus_PAYMENT_FAILED,
		beOrderPb.OrderStatus_COLLECT_REGISTERED:         fePayPb.OrderStatus_COLLECT_REGISTERED,
		beOrderPb.OrderStatus_COLLECT_IN_PROGRESS:        fePayPb.OrderStatus_COLLECT_REGISTRATION_IN_PROGRESS,
		beOrderPb.OrderStatus_COLLECT_FAILED:             fePayPb.OrderStatus_COLLECT_REGISTRATION_FAILED,
		beOrderPb.OrderStatus_COLLECT_DISMISSED_BY_PAYER: fePayPb.OrderStatus_COLLECT_DECLINED,
		beOrderPb.OrderStatus_COLLECT_DISMISSED_BY_PAYEE: fePayPb.OrderStatus_COLLECT_CANCELLED,
		beOrderPb.OrderStatus_PAYMENT_REVERSED:           fePayPb.OrderStatus_PAYMENT_FAILED,
		beOrderPb.OrderStatus_EXPIRED:                    fePayPb.OrderStatus_COLLECT_EXPIRED,
		beOrderPb.OrderStatus_INITIATED:                  fePayPb.OrderStatus_PAYMENT_IN_PROGRESS,
	}

	feToBeUiEntryPoint = map[transaction.UIEntryPoint]beOrderPb.UIEntryPoint{
		transaction.UIEntryPoint_ACCOUNT_DETAILS:                              beOrderPb.UIEntryPoint_ACCOUNT_DETAILS,
		transaction.UIEntryPoint_REFERRALS:                                    beOrderPb.UIEntryPoint_REFERRALS,
		transaction.UIEntryPoint_ACCOUNT_SUMMARY:                              beOrderPb.UIEntryPoint_ACCOUNT_SUMMARY,
		transaction.UIEntryPoint_TRANSFER_IN:                                  beOrderPb.UIEntryPoint_TRANSFER_IN,
		transaction.UIEntryPoint_ONBOARD_ADD_FUNDS:                            beOrderPb.UIEntryPoint_ONBOARD_ADD_FUNDS,
		transaction.UIEntryPoint_UI_ENTRY_POINT_ONBOARD_ADD_FUNDS_PRE_FUNDING: beOrderPb.UIEntryPoint_ONBOARD_ADD_FUNDS_PRE_FUNDING,
		transaction.UIEntryPoint_HOME:                                         beOrderPb.UIEntryPoint_HOME,
		transaction.UIEntryPoint_TIMELINE:                                     beOrderPb.UIEntryPoint_TIMELINE,
		transaction.UIEntryPoint_HOME_PERSISTENT_CTA:                          beOrderPb.UIEntryPoint_HOME_PERSISTENT_CTA,
		transaction.UIEntryPoint_DEPOSIT_CREATION:                             beOrderPb.UIEntryPoint_DEPOSIT_CREATION,
		transaction.UIEntryPoint_BONUS_JAR_CREATION:                           beOrderPb.UIEntryPoint_BONUS_JAR_CREATION,
		transaction.UIEntryPoint_ASK_FI:                                       beOrderPb.UIEntryPoint_ASK_FI,
	}
)

type Service struct {
	// UnimplementedTransactionServer embedded to have forward compatible implementations.
	transaction.UnimplementedTransactionServer
	orderClient                          beOrderPb.OrderServiceClient
	payClient                            paymentPb.PaymentClient
	actorClient                          actor.ActorClient
	savingsClient                        savingsPb.SavingsClient
	categorizerClient                    categorizer.TxnCategorizerClient
	accountPiRelationClient              accountPiPb.AccountPIRelationClient
	piClient                             piPb.PiClient
	decisionEngineClient                 paymentPb.DecisionEngineClient
	authClient                           authPb.AuthClient
	upiClient                            upiPb.UPIClient
	timelineClient                       timelinePb.TimelineServiceClient
	usersClient                          user.UsersClient
	bankIcon                             *config.BankIcon
	bottomSheets                         *config.BottomSheets
	orderStatusIconUrl                   *config.OrderStatusIconUrl
	onboardingClient                     onboarding.OnboardingClient
	AddFundsScreenParams                 map[string][]*config.AddFundsParamOptions
	eventBroker                          events.Broker
	FirstTransactionRewardsBanner        *config.RewardsBanner
	bypassP2PCollectShortCircuit         bool
	rewardsOfferClient                   rewardOfferPb.RewardOffersClient
	addFundsParams                       *genconf.AddFundsParams
	userGroupClient                      userGroupPb.GroupClient
	paymentInfoBanner                    *config.PaymentInfoBanner
	fundTransferValidator                validator.IFundTransferValidator
	payV1Client                          payPb.PayClient
	investmentConfig                     *genconf.Investment
	upiOnboardingClient                  upiOnboardingPb.UpiOnboardingClient
	conf                                 *genconf.Config
	bankCustClient                       bankCustPb.BankCustomerServiceClient
	tieringClient                        tieringPb.TieringClient
	tieringPinotClient                   tieringPinotPb.EODBalanceClient
	tieringDeeplinkManager               tieringDeeplink.Manager
	tieringAddFundsManager               tieringAddFunds.TieringAddFundsManager
	healthEngineClient                   health_engine.HealthEngineServiceClient
	releaseEvaluator                     release.IEvaluator
	serverConf                           *config.Config
	inappreferralClient                  inappreferralPb.InAppReferralClient
	userIntelClient                      userIntelPb.UserIntelServiceClient
	affluentUserAddFundsBonusABEvaluator *release.ABEvaluator[string]
	operationalStatusClient              operstatusPb.OperationalStatusServiceClient
	accountBalanceClient                 accountBalancePb.BalanceClient
	paymentOptionsDataCollector          dataCollector.DataCollector
	tierMovementFeedbackFlowSvcClient    feedback_flow_integration.ITierMovementBasedFeedbackFlowIntegrationSvc
	paymentOptions                       []providers.IPaymentOption
	rewardsGeneratorClient               rewardsPb.RewardsGeneratorClient
	salaryProgramClient                  salaryPb.SalaryProgramClient
	cardClient                           cardPb.CardProvisioningClient
	paymentGatewayClient                 paymentgateway.PaymentGatewayServiceClient
	recurringPaymentPgClient             paymentgateway.PaymentGatewayServiceClient
	recurringPaymentClient               recurringPaymentPb.RecurringPaymentServiceClient
	dataCollector                        tieringData.DataCollector
	ffAccountingClient                   ffAccountsPb.AccountingClient
	ffClientv2                           fireflyv2.FireflyV2Client
}

// Factory method for creating an instance of Transaction service. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewTransactionService(
	conf *genconf.Config,
	orderClient beOrderPb.OrderServiceClient,
	payClient paymentPb.PaymentClient,
	savingsClient savingsPb.SavingsClient,
	categorizerClient categorizer.TxnCategorizerClient,
	actorClient actor.ActorClient,
	accountPiRelationClient accountPiPb.AccountPIRelationClient,
	piClient piPb.PiClient,
	decisionEngineClient paymentPb.DecisionEngineClient,
	authClient authPb.AuthClient,
	upiClient upiPb.UPIClient,
	timelineClient timelinePb.TimelineServiceClient,
	userClient user.UsersClient,
	onboardingClient onboarding.OnboardingClient,
	broker events.Broker,
	rewardsOfferClient rewardOfferPb.RewardOffersClient,
	userGroupClient userGroupPb.GroupClient,
	fundTransferValidator validator.IFundTransferValidator,
	payV1Client payPb.PayClient,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	bankCustClient bankCustPb.BankCustomerServiceClient,
	tieringClient tieringPb.TieringClient,
	tieringPinotClient tieringPinotPb.EODBalanceClient,
	tieringDeeplinkManager tieringDeeplink.Manager,
	tieringAddFundsManager tieringAddFunds.TieringAddFundsManager,
	healthEngineClient health_engine.HealthEngineServiceClient,
	releaseEvaluator release.IEvaluator,
	serverConf *config.Config,
	inappreferralClient inappreferralPb.InAppReferralClient,
	userIntelClient userIntelPb.UserIntelServiceClient,
	operationalStatusClient operstatusPb.OperationalStatusServiceClient,
	accountBalanceClient accountBalancePb.BalanceClient,
	paymentOptionsDataCollector dataCollector.DataCollector,
	tierMovementFeedbackFlowSvcClient feedback_flow_integration.ITierMovementBasedFeedbackFlowIntegrationSvc,
	paymentOptionProviders []providers.IPaymentOption,
	rewardsGeneratorClient rewardsPb.RewardsGeneratorClient,
	salaryProgramClient salaryPb.SalaryProgramClient,
	cardClient cardPb.CardProvisioningClient,
	paymentGatewayClient paymentgateway.PaymentGatewayServiceClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	dataCollector tieringData.DataCollector,
	ffAccountingClient ffAccountsPb.AccountingClient,
	ffClientv2 fireflyv2.FireflyV2Client,
) (*Service, error) {

	err := LoadDisplayCategoryMapping(conf.DisplayCategoryMappingJson())
	if err != nil {
		logger.ErrorNoCtx("error in loading display cat mapping", zap.Error(err))
		return nil, err
	}

	err = payment_options.LoadBankVendorInfo(conf.BankVendorInfoJsonPath())
	if err != nil {
		logger.ErrorNoCtx("error in loading vendor bank info mapping", zap.Error(err))
		return nil, err
	}

	// generic string to experiment function to map generic ab experiment types
	genericStringExprFn := func(str string) string { return str }
	// initializing ab evaluator for AFFLUENT_USER_ADD_FUNDS_BONUS feature
	affluentUserAddFundsBonusABEvaluator := getABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, conf.ABFeatureReleaseConfig(), genericStringExprFn)

	return &Service{
		orderClient:                          orderClient,
		payClient:                            payClient,
		actorClient:                          actorClient,
		savingsClient:                        savingsClient,
		categorizerClient:                    categorizerClient,
		accountPiRelationClient:              accountPiRelationClient,
		piClient:                             piClient,
		decisionEngineClient:                 decisionEngineClient,
		authClient:                           authClient,
		upiClient:                            upiClient,
		timelineClient:                       timelineClient,
		usersClient:                          userClient,
		bankIcon:                             conf.BankIcon(),
		bottomSheets:                         conf.BottomSheets(),
		onboardingClient:                     onboardingClient,
		AddFundsScreenParams:                 conf.AddFundsScreenParams(),
		eventBroker:                          broker,
		FirstTransactionRewardsBanner:        conf.FirstTransactionRewardsBanner(),
		bypassP2PCollectShortCircuit:         conf.Flags().BypassP2PCollectShortCircuit(),
		rewardsOfferClient:                   rewardsOfferClient,
		userGroupClient:                      userGroupClient,
		addFundsParams:                       conf.AddFundsParams(),
		paymentInfoBanner:                    conf.PaymentInfoBanner(),
		fundTransferValidator:                fundTransferValidator,
		payV1Client:                          payV1Client,
		investmentConfig:                     conf.Investment(),
		orderStatusIconUrl:                   conf.OrderStatusIconUrl(),
		upiOnboardingClient:                  upiOnboardingClient,
		conf:                                 conf,
		bankCustClient:                       bankCustClient,
		tieringClient:                        tieringClient,
		tieringPinotClient:                   tieringPinotClient,
		tieringDeeplinkManager:               tieringDeeplinkManager,
		tieringAddFundsManager:               tieringAddFundsManager,
		healthEngineClient:                   healthEngineClient,
		releaseEvaluator:                     releaseEvaluator,
		serverConf:                           serverConf,
		inappreferralClient:                  inappreferralClient,
		userIntelClient:                      userIntelClient,
		affluentUserAddFundsBonusABEvaluator: affluentUserAddFundsBonusABEvaluator,
		operationalStatusClient:              operationalStatusClient,
		accountBalanceClient:                 accountBalanceClient,
		paymentOptionsDataCollector:          paymentOptionsDataCollector,
		tierMovementFeedbackFlowSvcClient:    tierMovementFeedbackFlowSvcClient,
		paymentOptions:                       paymentOptionProviders,
		rewardsGeneratorClient:               rewardsGeneratorClient,
		salaryProgramClient:                  salaryProgramClient,
		cardClient:                           cardClient,
		paymentGatewayClient:                 paymentGatewayClient,
		recurringPaymentPgClient:             paymentGatewayClient,
		recurringPaymentClient:               recurringPaymentClient,
		dataCollector:                        dataCollector,
		ffAccountingClient:                   ffAccountingClient,
		ffClientv2:                           ffClientv2,
	}, nil
}

// isLocationMandatoryForProtocol returns if location is mandatory for protocol or not
func (s *Service) isLocationMandatoryForProtocol(paymentProtocol paymentPb.PaymentProtocol) bool {
	return !lo.Contains[string](s.conf.PayParams().ProtocolsWithOptionalLocation().ToStringArray(), paymentPb.PaymentProtocol_name[int32(paymentProtocol)])
}

// getABEvaluatorOfFeature returns an instance of the AB evaluator to perform experiments of type ABExperiment
func getABEvaluatorOfFeature[ABExperiment any](
	actorClient actor.ActorClient, userClient user.UsersClient, userGroupClient userGroupPb.GroupClient,
	abFeatureReleaseConf *releaseGenConf.ABFeatureReleaseConfig,
	strToExprFn func(str string) ABExperiment,
) *release.ABEvaluator[ABExperiment] {
	abEvaluator := release.NewABEvaluator[ABExperiment](
		abFeatureReleaseConf,
		release.NewConstraintFactoryImpl(
			release.NewAppVersionConstraint(),
			release.NewStickinessConstraint(),
			release.NewUserGroupConstraint(actorClient, userClient, userGroupClient),
		),
		strToExprFn,
	)

	return abEvaluator
}
