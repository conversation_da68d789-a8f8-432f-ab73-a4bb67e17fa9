package transaction

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"

	"context"
	"fmt"
	"testing"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	mock_release "github.com/epifi/gamma/pkg/feature/release/mocks"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	deeplink2 "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2/ui"

	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	"github.com/epifi/gamma/api/frontend/pay/transaction"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/mocks"
	tieringPb "github.com/epifi/gamma/api/tiering"
	mocks2 "github.com/epifi/gamma/api/tiering/mocks"
	userGroupMocks "github.com/epifi/gamma/api/user/group/mocks"
	usersMocks "github.com/epifi/gamma/api/user/mocks"

	"github.com/golang/mock/gomock"
)

func Test_getRewardDetailsSection(t *testing.T) {
	t.Parallel()
	logger.Init(cfg.TestEnv)
	type args struct {
		ctx                context.Context
		actorId            string
		refId              string
		orderExecutionTime *timestampPb.Timestamp
	}
	ctx := context.Background()
	ctx = epificontext.CtxWithAppPlatform(ctx, commontypes.Platform_ANDROID)
	ctx = epificontext.CtxWithAppVersionCode(ctx, "222")
	currentTimeStamp := timestampPb.Now()

	tests := []struct {
		name           string
		args           args
		setupMockCalls func(releaseEvaluator *mock_release.MockIEvaluator, rewardsGeneratorClient *mocks.MockRewardsGeneratorClient, tieringClient *mocks2.MockTieringClient, actorClient *actorMocks.MockActorClient, usersClient *usersMocks.MockUsersClient, userGroupClient *userGroupMocks.MockGroupClient)
		want           *transaction.RewardDetails
		wantErr        bool
	}{
		{
			name: "should return nil when release evaluator returns false",
			args: args{
				ctx:                ctx,
				actorId:            "actor-id",
				refId:              "ref-id",
				orderExecutionTime: currentTimeStamp,
			},
			setupMockCalls: func(releaseEvaluator *mock_release.MockIEvaluator, rewardsGeneratorClient *mocks.MockRewardsGeneratorClient, tieringClient *mocks2.MockTieringClient, actorClient *actorMocks.MockActorClient, usersClient *usersMocks.MockUsersClient, userGroupClient *userGroupMocks.MockGroupClient) {
				releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil)
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "should return error when GetAllRewardsAndProjections fails",
			args: args{
				ctx:                ctx,
				actorId:            "actor-id",
				refId:              "ref-id",
				orderExecutionTime: currentTimeStamp,
			},
			setupMockCalls: func(releaseEvaluator *mock_release.MockIEvaluator, rewardsGeneratorClient *mocks.MockRewardsGeneratorClient, tieringClient *mocks2.MockTieringClient, actorClient *actorMocks.MockActorClient, usersClient *usersMocks.MockUsersClient, userGroupClient *userGroupMocks.MockGroupClient) {
				releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				rewardsGeneratorClient.EXPECT().GetAllRewardsAndProjections(gomock.Any(), &rewardsPb.GetAllRewardsAndProjectionRequest{
					ActorId:    "actor-id",
					ActionType: rewardsPb.CollectedDataType_ORDER,
					RefIds:     []string{"ref-id"},
				}).Return(&rewardsPb.GetAllRewardsAndProjectionResponse{
					Status: rpc.StatusInternalWithDebugMsg("error while fetching rewards"),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "should return error when EvaluateTierForActor fails",
			args: args{
				ctx:                ctx,
				actorId:            "actor-id",
				refId:              "ref-id",
				orderExecutionTime: currentTimeStamp,
			},
			setupMockCalls: func(releaseEvaluator *mock_release.MockIEvaluator, rewardsGeneratorClient *mocks.MockRewardsGeneratorClient, tieringClient *mocks2.MockTieringClient, actorClient *actorMocks.MockActorClient, usersClient *usersMocks.MockUsersClient, userGroupClient *userGroupMocks.MockGroupClient) {
				releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				rewardsGeneratorClient.EXPECT().GetAllRewardsAndProjections(gomock.Any(), &rewardsPb.GetAllRewardsAndProjectionRequest{
					ActorId:    "actor-id",
					ActionType: rewardsPb.CollectedDataType_ORDER,
					RefIds:     []string{"ref-id"},
				}).Return(&rewardsPb.GetAllRewardsAndProjectionResponse{
					Status: rpc.StatusOk(),
					RefIdToRewardEntitiesMap: map[string]*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities{
						"ref-id": {
							RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
								{
									EntityId:         "rwd-1",
									RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(100)}},
									RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
								},
							},
						},
					},
				}, nil)
				tieringClient.EXPECT().GetTierAtTime(gomock.Any(), &tieringPb.GetTierAtTimeRequest{ActorId: "actor-id", TierTimestamp: currentTimeStamp}).Return(&tieringPb.GetTierAtTimeResponse{
					Status: rpc.StatusInternalWithDebugMsg("error while fetching tiering"),
				}, nil)
			},
			wantErr: true,
		},
		{
			name: "should return empty reward details when no reward entities are present for given refId",
			args: args{
				ctx:                ctx,
				actorId:            "actor-id",
				refId:              "ref-id",
				orderExecutionTime: currentTimeStamp,
			},
			setupMockCalls: func(releaseEvaluator *mock_release.MockIEvaluator, rewardsGeneratorClient *mocks.MockRewardsGeneratorClient, tieringClient *mocks2.MockTieringClient, actorClient *actorMocks.MockActorClient, usersClient *usersMocks.MockUsersClient, userGroupClient *userGroupMocks.MockGroupClient) {
				releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				rewardsGeneratorClient.EXPECT().GetAllRewardsAndProjections(gomock.Any(), &rewardsPb.GetAllRewardsAndProjectionRequest{
					ActorId:    "actor-id",
					ActionType: rewardsPb.CollectedDataType_ORDER,
					RefIds:     []string{"ref-id"},
				}).Return(&rewardsPb.GetAllRewardsAndProjectionResponse{
					Status:                   rpc.StatusOk(),
					RefIdToRewardEntitiesMap: nil,
				}, nil)
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "should return successful reward details when reward entities are present for given refId",
			args: args{
				ctx:                ctx,
				actorId:            "actor-id",
				refId:              "ref-id",
				orderExecutionTime: currentTimeStamp,
			},
			setupMockCalls: func(releaseEvaluator *mock_release.MockIEvaluator, rewardsGeneratorClient *mocks.MockRewardsGeneratorClient, tieringClient *mocks2.MockTieringClient, actorClient *actorMocks.MockActorClient, usersClient *usersMocks.MockUsersClient, userGroupClient *userGroupMocks.MockGroupClient) {
				releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				rewardsGeneratorClient.EXPECT().GetAllRewardsAndProjections(gomock.Any(), &rewardsPb.GetAllRewardsAndProjectionRequest{
					ActorId:    "actor-id",
					ActionType: rewardsPb.CollectedDataType_ORDER,
					RefIds:     []string{"ref-id"},
				}).Return(&rewardsPb.GetAllRewardsAndProjectionResponse{
					Status: rpc.StatusOk(),
					RefIdToRewardEntitiesMap: map[string]*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities{
						"ref-id": {
							RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
								{
									EntityId:         "rwd-1",
									RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(100)}},
									RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
								},
								{
									EntityId:         "rwd-2",
									RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_CASH, Units: float32(200)}},
									RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
								},
								{
									EntityId:         "rwd-3",
									RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(300)}},
									RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD,
								},
								{
									EntityId:         "rwd-4",
									RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_CASH, Units: float32(400)}},
									RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD,
								},
							},
						},
					},
				}, nil)
				tieringClient.EXPECT().GetTierAtTime(gomock.Any(), &tieringPb.GetTierAtTimeRequest{ActorId: "actor-id", TierTimestamp: currentTimeStamp}).Return(&tieringPb.GetTierAtTimeResponse{
					Status: rpc.StatusOk(),
					TierInfo: &external.TierInfo{
						Tier: external.Tier_TIER_FI_INFINITE,
					},
				}, nil)
			},
			want: &transaction.RewardDetails{
				Summary: &transaction.RewardSummarySection{
					Title: &commontypes.Text{
						FontColor: "#333333",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: gconf.RewardDetailsForOrderReceiptConfig().RewardSummarySection().Title(),
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_S,
						},
					},
					SubTitle: &commontypes.Text{
						FontColor: "#A4A4A4",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "on Infinite Plan",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_XS,
						},
					},
					RewardValues: []*transaction.RewardSummarySection_EarnedValueChip{
						{
							Value: &ui.IconTextComponent{
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:       "#EFF2F6",
									CornerRadius:  14,
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    8,
									BottomPadding: 8,
								},
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/rupee-symbol-icon.png", 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "200",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
									},
								},
								Deeplink: &deeplink2.Deeplink{
									Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
								},
							},
							ShowWashedOutChip: false,
						},
						{
							Value: &ui.IconTextComponent{
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:       "#EFF2F6",
									CornerRadius:  14,
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    8,
									BottomPadding: 8,
								},
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/fi-coin-symbol-icon.png", 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "100",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
									},
								},
								Deeplink: &deeplink2.Deeplink{
									Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
								},
							},
							ShowWashedOutChip: false,
						},
						{
							Value: &ui.IconTextComponent{
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:       "#EFF2F6",
									CornerRadius:  14,
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    8,
									BottomPadding: 8,
								},
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/cashback-projected-icon.png", 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "400",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
									},
								},
							},
							ShowWashedOutChip: true,
						},
					},
				},
				BottomSheet: &transaction.RewardDetailsBottomSheet{
					Title: &commontypes.Text{
						FontColor: "#282828",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "You earned",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_2,
						},
					},
					RewardDetailsCards: []*transaction.RewardDetailsBottomSheet_RewardDetailsRow{
						{
							Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().CashbackEarnedIconUrl(), 32, 32),
							Title: &commontypes.Text{
								FontColor: "#313234",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Cashback",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
								},
							},
							EarnedValue: &ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().RupeeSymbolIconUrl(), 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "200",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
									},
								},
								Deeplink: &deeplink2.Deeplink{
									Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
								},
							},
							Deeplink: &deeplink2.Deeplink{
								Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_RadialGradient{
									RadialGradient: &widget.RadialGradient{
										Colours: []string{"#F6F9FD"},
									},
								},
							},
							ShowWashedOutCard: false,
						},
						{
							Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().FiCoinsEarnedIconUrl(), 32, 32),
							Title: &commontypes.Text{
								FontColor: "#313234",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Fi-Coins",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
								},
							},
							EarnedValue: &ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().FiCoinSymbolIconUrl(), 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "100",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
									},
								},
								Deeplink: &deeplink2.Deeplink{
									Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
								},
							},
							Deeplink: &deeplink2.Deeplink{
								Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_RadialGradient{
									RadialGradient: &widget.RadialGradient{
										Colours: []string{"#F6F9FD"},
									},
								},
							},
							ShowWashedOutCard: false,
						},
						{
							Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().CashbackEarnedIconUrl(), 32, 32),
							Title: &commontypes.Text{
								FontColor: "#313234",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Projected Cashback",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
								},
							},
							EarnedValue: &ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().ProjectedRupeeSymbolIconUrl(), 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "400",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
									},
								},
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_RadialGradient{
									RadialGradient: &widget.RadialGradient{
										Colours: []string{"#F6F9FD"},
									},
								},
							},
							ShowWashedOutCard: true,
							Desc: &ui.IconTextComponent{
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:       "#FFFFFF",
									CornerRadius:  11,
									LeftPadding:   5,
									RightPadding:  5,
									TopPadding:    5,
									BottomPadding: 5,
								},
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().TimerIconUrl(), 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: fmt.Sprintf(gconf.RewardDetailsForOrderReceiptConfig().ProjectedRewardDescription(), "credit", currentTimeStamp.AsTime().In(datetime.IST).AddDate(0, 1, 0).Format("Jan")),
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
									},
								},
							},
						},
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_RadialGradient{
							RadialGradient: &widget.RadialGradient{
								Colours: []string{"#E6E9ED"},
							},
						},
					},
					BottomSheetInfoText: commontypes.GetTextFromStringWithCustomFontStyle(gconf.RewardDetailsForOrderReceiptConfig().RewardDetailsBottomSheet().ProjectedRewardTncBottomSheetInfoText(), "#A4A4A4", &commontypes.FontStyleInfo{
						FontFamily: "Inter",
						FontStyle:  "normal",
						FontSize:   "9",
					}),
				},
			},
			wantErr: false,
		},
		{
			name: "should return successful reward details when reward entities are present for given refId but one with 0 value and another with negative",
			args: args{
				ctx:                ctx,
				actorId:            "actor-id",
				refId:              "ref-id",
				orderExecutionTime: currentTimeStamp,
			},
			setupMockCalls: func(releaseEvaluator *mock_release.MockIEvaluator, rewardsGeneratorClient *mocks.MockRewardsGeneratorClient, tieringClient *mocks2.MockTieringClient, actorClient *actorMocks.MockActorClient, usersClient *usersMocks.MockUsersClient, userGroupClient *userGroupMocks.MockGroupClient) {
				releaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil)
				rewardsGeneratorClient.EXPECT().GetAllRewardsAndProjections(gomock.Any(), &rewardsPb.GetAllRewardsAndProjectionRequest{
					ActorId:    "actor-id",
					ActionType: rewardsPb.CollectedDataType_ORDER,
					RefIds:     []string{"ref-id"},
				}).Return(&rewardsPb.GetAllRewardsAndProjectionResponse{
					Status: rpc.StatusOk(),
					RefIdToRewardEntitiesMap: map[string]*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities{
						"ref-id": {
							RewardEntities: []*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity{
								{
									EntityId:         "rwd-1",
									RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(-15)}},
									RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
								},
								{
									EntityId:         "rwd-2",
									RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_CASH, Units: float32(2150)}},
									RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD,
								},
								{
									EntityId:         "rwd-3",
									RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_FI_COINS, Units: float32(-15)}},
									RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD,
								},
								{
									EntityId:         "rwd-4",
									RewardOptions:    []*rewardsPb.RewardOptionMinimal{{RewardType: rewardsPb.RewardType_CASH, Units: float32(0)}},
									RewardEntityType: rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD,
								},
							},
						},
					},
				}, nil)
				tieringClient.EXPECT().GetTierAtTime(gomock.Any(), &tieringPb.GetTierAtTimeRequest{ActorId: "actor-id", TierTimestamp: currentTimeStamp}).Return(&tieringPb.GetTierAtTimeResponse{
					Status: rpc.StatusOk(),
					TierInfo: &external.TierInfo{
						Tier: external.Tier_TIER_FI_INFINITE,
					},
				}, nil)
			},
			want: &transaction.RewardDetails{
				Summary: &transaction.RewardSummarySection{
					Title: &commontypes.Text{
						FontColor: "#333333",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: gconf.RewardDetailsForOrderReceiptConfig().RewardSummarySection().Title(),
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_S,
						},
					},
					SubTitle: &commontypes.Text{
						FontColor: "#A4A4A4",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "on Infinite Plan",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_XS,
						},
					},
					RewardValues: []*transaction.RewardSummarySection_EarnedValueChip{
						{
							Value: &ui.IconTextComponent{
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:       "#EFF2F6",
									CornerRadius:  14,
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    8,
									BottomPadding: 8,
								},
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/rupee-symbol-icon.png", 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "2.2K",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
									},
								},
								Deeplink: &deeplink2.Deeplink{
									Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
								},
							},
							ShowWashedOutChip: false,
						},
						{
							Value: &ui.IconTextComponent{
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:       "#EFF2F6",
									CornerRadius:  14,
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    8,
									BottomPadding: 8,
								},
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/fi-coin-symbol-icon.png", 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "-15",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
									},
								},
								Deeplink: &deeplink2.Deeplink{
									Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
								},
							},
							ShowWashedOutChip: false,
						},
						{
							Value: &ui.IconTextComponent{
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:       "#EFF2F6",
									CornerRadius:  14,
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    8,
									BottomPadding: 8,
								},
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/fi-coin-symbol-projected.png", 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "-15",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
									},
								},
							},
							ShowWashedOutChip: true,
						},
					},
				},
				BottomSheet: &transaction.RewardDetailsBottomSheet{
					Title: &commontypes.Text{
						FontColor: "#282828",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "You earned",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_2,
						},
					},
					RewardDetailsCards: []*transaction.RewardDetailsBottomSheet_RewardDetailsRow{
						{
							Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().CashbackEarnedIconUrl(), 32, 32),
							Title: &commontypes.Text{
								FontColor: "#313234",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Cashback",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
								},
							},
							EarnedValue: &ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().RupeeSymbolIconUrl(), 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "2150",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
									},
								},
								Deeplink: &deeplink2.Deeplink{
									Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
								},
							},
							Deeplink: &deeplink2.Deeplink{
								Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_RadialGradient{
									RadialGradient: &widget.RadialGradient{
										Colours: []string{"#F6F9FD"},
									},
								},
							},
							ShowWashedOutCard: false,
						},
						{
							Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().FiCoinsEarnedIconUrl(), 32, 32),
							Title: &commontypes.Text{
								FontColor: "#313234",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Fi-Coins",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
								},
							},
							EarnedValue: &ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().FiCoinSymbolIconUrl(), 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "-15",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
									},
								},
								Deeplink: &deeplink2.Deeplink{
									Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
								},
							},
							Deeplink: &deeplink2.Deeplink{
								Screen: deeplink2.Screen_MY_REWARDS_SCREEN,
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_RadialGradient{
									RadialGradient: &widget.RadialGradient{
										Colours: []string{"#F6F9FD"},
									},
								},
							},
							ShowWashedOutCard: false,
						},
						{
							Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().FiCoinsEarnedIconUrl(), 32, 32),
							Title: &commontypes.Text{
								FontColor: "#313234",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: "Projected Fi-Coins",
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
								},
							},
							EarnedValue: &ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().ProjectedFiCoinSymbolIconUrl(), 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: "-15",
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
									},
								},
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_RadialGradient{
									RadialGradient: &widget.RadialGradient{
										Colours: []string{"#F6F9FD"},
									},
								},
							},
							ShowWashedOutCard: true,
							Desc: &ui.IconTextComponent{
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									BgColor:       "#FFFFFF",
									CornerRadius:  11,
									LeftPadding:   5,
									RightPadding:  5,
									TopPadding:    5,
									BottomPadding: 5,
								},
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(gconf.RewardDetailsForOrderReceiptConfig().TimerIconUrl(), 18, 18),
								Texts: []*commontypes.Text{
									{
										FontColor: "#313234",
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: fmt.Sprintf(gconf.RewardDetailsForOrderReceiptConfig().ProjectedRewardDescription(), "debit", currentTimeStamp.AsTime().In(datetime.IST).AddDate(0, 1, 0).Format("Jan")),
										},
										FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
									},
								},
							},
						},
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_RadialGradient{
							RadialGradient: &widget.RadialGradient{
								Colours: []string{"#E6E9ED"},
							},
						},
					},
					BottomSheetInfoText: commontypes.GetTextFromStringWithCustomFontStyle(gconf.RewardDetailsForOrderReceiptConfig().RewardDetailsBottomSheet().ProjectedRewardTncBottomSheetInfoText(), "#A4A4A4", &commontypes.FontStyleInfo{
						FontFamily: "Inter",
						FontStyle:  "normal",
						FontSize:   "9",
					}),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			beMockRewardsGeneratorClient := mocks.NewMockRewardsGeneratorClient(ctr)
			beMockTieringClient := mocks2.NewMockTieringClient(ctr)
			beMockActorClient := actorMocks.NewMockActorClient(ctr)
			beUsersClient := usersMocks.NewMockUsersClient(ctr)
			beUserGroupClient := userGroupMocks.NewMockGroupClient(ctr)
			releaseEvaluator := mock_release.NewMockIEvaluator(ctr)
			tt.setupMockCalls(releaseEvaluator, beMockRewardsGeneratorClient, beMockTieringClient, beMockActorClient, beUsersClient, beUserGroupClient)
			s := &Service{
				conf:                   gconf,
				releaseEvaluator:       releaseEvaluator,
				actorClient:            beMockActorClient,
				rewardsGeneratorClient: beMockRewardsGeneratorClient,
				tieringClient:          beMockTieringClient,
				usersClient:            beUsersClient,
				userGroupClient:        beUserGroupClient,
			}
			got, err := s.getRewardDetailsSection(tt.args.ctx, tt.args.actorId, tt.args.refId, tt.args.orderExecutionTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("getRewardDetailsSection() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(tt.want, got, protocmp.Transform()); diff != "" {
				t.Errorf("getRewardDetailsSection() got = %v, want %v,  diff = %v", got, tt.want, diff)
			}
		})
	}
}
