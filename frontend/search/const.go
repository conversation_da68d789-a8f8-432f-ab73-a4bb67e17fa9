package search

import (
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/search/widget"
	vkycBe "github.com/epifi/gamma/api/kyc/vkyc"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

type EntrySearch int

const (
	HomePage EntrySearch = iota
	HelpPage
)

// action-bar constants
const (
	TrendingWidgetTitle  = "Trending Searches"
	QuickLinksTitle      = "Quick Links"
	UpcomingPaymentTitle = "Upcoming"
	QuickInfoTitle       = "Financial Activity"
	HelpTitle            = "Help"
)

// financial activity deeplink text
const (
	ViewMoreText = "View More"
	ViewAllText  = "View All"
)

// amount color map for txn page
type AmountColourMap struct {
	DebitColour   string
	CreditColour  string
	SavingsColour string
	DefaultColour string
}

var (
	amountColorMap = AmountColourMap{
		DebitColour:   "#333333",
		CreditColour:  "#333333",
		SavingsColour: "#333333",
		DefaultColour: "#333333",
	}
)

const OpenSDAccountS3IconUrl = "https://epifi-icons.pointz.in/quick-link-icons/Open+SD.svg"
const OpenFDAccountS3IconUrl = "https://epifi-icons.pointz.in/quick-link-icons/Open+FD.svg"
const AllSDAccountS3IconUrl = "https://epifi-icons.pointz.in/quick-link-icons/SD.svg"
const AllFDAccountS3IconUrl = "https://epifi-icons.pointz.in/quick-link-icons/FD.svg"

//nolint:dupl
var DeeplinkToS3IconUrlMap = map[deeplinkPb.Screen]string{
	deeplinkPb.Screen_PAY_QR_SCREEN:                        "https://epifi-icons.pointz.in/quick-link-icons/Scan+QR.svg",
	deeplinkPb.Screen_PAY_VIA_UPI:                          "https://epifi-icons.pointz.in/quick-link-icons/Pay+via+Phone.svg",
	deeplinkPb.Screen_HELP_MAIN:                            "https://epifi-icons.pointz.in/quick-link-icons/Help.svg",
	deeplinkPb.Screen_PROFILE_SCREEN:                       "https://epifi-icons.pointz.in/quick-link-icons/Profile.svg",
	deeplinkPb.Screen_PROFILE_PRIVACY_SCREEN:               "https://epifi-icons.pointz.in/quick-link-icons/Privacy.svg",
	deepLinkPb.Screen_PROFILE_SETTINGS:                     "https://epifi-icons.pointz.in/quick-link-icons/Settings.svg",
	deeplinkPb.Screen_PROFILE_APP_PERMISSION_SCREEN:        "https://epifi-icons.pointz.in/quick-link-icons/Privacy.svg",
	deepLinkPb.Screen_PROFILE_PERSONAL_DETAILS:             "https://epifi-icons.pointz.in/quick-link-icons/Profile.svg",
	deepLinkPb.Screen_PROFILE_SETTINGS_NOTIFICATION:        "https://epifi-icons.pointz.in/quick-link-icons/Notifications.svg",
	deeplinkPb.Screen_PROFILE_UPI_QR_CODE_SCREEN:           "https://epifi-icons.pointz.in/quick-link-icons/Scan+QR.svg",
	deepLinkPb.Screen_PROFILE_SETTINGS_LEGAL:               "https://epifi-icons.pointz.in/quick-link-icons/Legal.svg",
	deepLinkPb.Screen_PROFILE_ABOUT_FI:                     "https://epifi-icons.pointz.in/quick-link-icons/Legal.svg",
	deepLinkPb.Screen_TRANSFER_IN:                          "https://epifi-icons.pointz.in/quick-link-icons/Add+Money.svg",
	deeplinkPb.Screen_CARD_USAGE_SCREEN:                    "https://epifi-icons.pointz.in/quick-link-icons/Card+Usage.svg",
	deeplinkPb.Screen_CARD_HOME_SCREEN:                     "https://epifi-icons.pointz.in/quick-link-icons/Card.svg",
	deeplinkPb.Screen_CARD_SETTINGS_SCREEN:                 "https://epifi-icons.pointz.in/quick-link-icons/Card+Settings.svg",
	deeplinkPb.Screen_CARD_LIMITS_HOME_SCREEN:              "https://epifi-icons.pointz.in/quick-link-icons/Card+Limit.svg",
	deeplinkPb.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN:        "https://epifi-icons.pointz.in/quick-link-icons/Card+Offers.svg",
	deeplinkPb.Screen_MY_REWARDS_SCREEN:                    "https://epifi-icons.pointz.in/quick-link-icons/Rewards.svg",
	deeplinkPb.Screen_REWARDS_WAYS_TO_EARN:                 "https://epifi-icons.pointz.in/quick-link-icons/Rewards+FAQ.svg",
	deeplinkPb.Screen_OFFERS_LANDING_SCREEN:                "https://epifi-icons.pointz.in/quick-link-icons/Offers.svg",
	deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN:               "https://epifi-icons.pointz.in/quick-link-icons/Offers+Used.svg",
	deeplinkPb.Screen_FIT_LANDING_SCREEN:                   "https://epifi-icons.pointz.in/quick-link-icons/FIT.svg",
	deeplinkPb.Screen_FIT_MY_RULES_PAGE:                    "https://epifi-icons.pointz.in/quick-link-icons/FIT.svg",
	deeplinkPb.Screen_REFERRALS_INVITE_FRIENDS:             "https://epifi-icons.pointz.in/quick-link-icons/Refer.svg",
	deeplinkPb.Screen_VKYC_STATUS_SCREEN:                   "https://epifi-icons.pointz.in/quick-link-icons/VKYC.svg",
	deeplinkPb.Screen_VIEW_VKYC_SCHEDULE_SCREEN:            "https://epifi-icons.pointz.in/quick-link-icons/VKYC.svg",
	deeplinkPb.Screen_MUTUAL_FUND_INVESTMENT_DIGEST_SCREEN: "https://epifi-icons.pointz.in/quick-link-icons/Mutual+Funds.svg",
	deeplinkPb.Screen_MUTUAL_FUNDS_LIST_VIEW_SCREEN:        "https://epifi-icons.pointz.in/quick-link-icons/Mutual+Funds.svg",
	deepLinkPb.Screen_TRANSACTION_TIMELINE:                 "https://epifi-icons.pointz.in/quick-link-icons/All+Txns.svg",
	deeplinkPb.Screen_PROFILE_ACCOUNT_SETTINGS:             "https://epifi-icons.pointz.in/quick-link-icons/Personal+Settings.svg",
	deeplinkPb.Screen_P2P_INVESTMENT_INVEST_SCREEN:         "https://epifi-icons.pointz.in/quick-link-icons/Jump.svg",
	deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN:               "https://epifi-icons.pointz.in/quick-link-icons/SD.svg",
	deeplinkPb.Screen_PAY_VIA_MOBILE_NUMBER:                "https://epifi-icons.pointz.in/quick-link-icons/Pay+via+Phone.svg",
	deeplinkPb.Screen_PAY_VIA_BANK_TRANSFER:                "https://epifi-icons.pointz.in/quick-link-icons/Pay+via+Bank+Transfer.svg",
	deeplinkPb.Screen_UPI_PIN_SETUP:                        "https://epifi-icons.pointz.in/quick-link-icons/UPI_Settings.svg",
	deeplinkPb.Screen_FORGOT_PIN_SCREEN:                    "https://epifi-icons.pointz.in/quick-link-icons/UPI_Forgot.svg",
	deeplinkPb.Screen_RESET_PIN_SCREEN:                     "https://epifi-icons.pointz.in/quick-link-icons/UPI_Reset.svg",
	deepLinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN:            "https://epifi-icons.pointz.in/quick-link-icons/Auto+Pay.svg",
	deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN:     "https://epifi-icons.pointz.in/quick-link-icons/loanLandingPage.svg",
	deeplinkPb.Screen_PRE_APPROVED_LOAN_DASHBOARD_SCREEN:   "https://epifi-icons.pointz.in/quick-link-icons/loanDashboard.svg",
	deeplinkPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN:         "https://epifi-icons.pointz.in/quick-link-icons/Card.svg",
	deeplinkPb.Screen_NET_WORTH_HUB_SCREEN:                 "https://epifi-icons.pointz.in/quick-link-icons/networth.png",
	deeplinkPb.Screen_EPF_DASHBOARD:                        "https://epifi-icons.pointz.in/quick-link-icons/epf.png",
}

//nolint:dupl
var DeeplinkToShortDescMap = map[deeplinkPb.Screen]string{
	deeplinkPb.Screen_PAY_QR_SCREEN:                        "Scan QR To Pay",
	deeplinkPb.Screen_PAY_VIA_UPI:                          "Pay Via UPI",
	deeplinkPb.Screen_HELP_MAIN:                            "Help",
	deeplinkPb.Screen_PROFILE_SCREEN:                       "Profile",
	deeplinkPb.Screen_PROFILE_PRIVACY_SCREEN:               "Privacy & Security",
	deepLinkPb.Screen_PROFILE_SETTINGS:                     "Settings",
	deeplinkPb.Screen_PROFILE_APP_PERMISSION_SCREEN:        "Manage App Permissions",
	deepLinkPb.Screen_PROFILE_PERSONAL_DETAILS:             "Personal Details",
	deepLinkPb.Screen_PROFILE_SETTINGS_NOTIFICATION:        "Notifications",
	deeplinkPb.Screen_PROFILE_UPI_QR_CODE_SCREEN:           "Profile Qr",
	deepLinkPb.Screen_PROFILE_SETTINGS_LEGAL:               "Legal",
	deepLinkPb.Screen_PROFILE_ABOUT_FI:                     "About Fi",
	deepLinkPb.Screen_TRANSFER_IN:                          "Add Money",
	deeplinkPb.Screen_CARD_USAGE_SCREEN:                    "Card Usage",
	deeplinkPb.Screen_CARD_HOME_SCREEN:                     "Card Details",
	deeplinkPb.Screen_CARD_SETTINGS_SCREEN:                 "Card Settings",
	deeplinkPb.Screen_CARD_LIMITS_HOME_SCREEN:              "Card Limits",
	deeplinkPb.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN:        "Card Offers",
	deeplinkPb.Screen_MY_REWARDS_SCREEN:                    "Your Rewards",
	deeplinkPb.Screen_REWARDS_WAYS_TO_EARN:                 "Ways to earn rewards",
	deeplinkPb.Screen_OFFERS_LANDING_SCREEN:                "Use your Fi-Coins",
	deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN:               "Collected Offers",
	deeplinkPb.Screen_FIT_LANDING_SCREEN:                   "Explore Fit",
	deeplinkPb.Screen_FIT_MY_RULES_PAGE:                    "Fit Rules",
	deeplinkPb.Screen_REFERRALS_INVITE_FRIENDS:             "Refer & Earn",
	deeplinkPb.Screen_VKYC_STATUS_SCREEN:                   "vKYC",
	deeplinkPb.Screen_VIEW_VKYC_SCHEDULE_SCREEN:            "Schedule vKYC",
	deeplinkPb.Screen_MUTUAL_FUND_INVESTMENT_DIGEST_SCREEN: "Mutual Fund",
	deeplinkPb.Screen_MUTUAL_FUNDS_LIST_VIEW_SCREEN:        "Mutual Fund List",
	deepLinkPb.Screen_TRANSACTION_TIMELINE:                 "All Transactions",
	deeplinkPb.Screen_PROFILE_ACCOUNT_SETTINGS:             "Account Settings",
	deeplinkPb.Screen_P2P_INVESTMENT_INVEST_SCREEN:         "P2P Investment",
	deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN:               "Deposits",
	deeplinkPb.Screen_PAY_VIA_MOBILE_NUMBER:                "Pay Via Mobile Number",
	deeplinkPb.Screen_PAY_VIA_BANK_TRANSFER:                "Pay Via Bank",
	deeplinkPb.Screen_UPI_PIN_SETUP:                        "UPI Pin Setup",
	deeplinkPb.Screen_FORGOT_PIN_SCREEN:                    "Reset UPI Pin",
	deeplinkPb.Screen_RESET_PIN_SCREEN:                     "Change UPI Pin",
	deepLinkPb.Screen_FIT_CUSTOMISE_RULE_SCREEN:            "Auto Pay",
	deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN:     "Loan",
	deeplinkPb.Screen_PRE_APPROVED_LOAN_DASHBOARD_SCREEN:   "My Loans",
	deeplinkPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN:         "Credit Card",
	deeplinkPb.Screen_NET_WORTH_HUB_SCREEN:                 "Net Worth",
	deeplinkPb.Screen_EPF_DASHBOARD:                        "EPF",
}

// widget unitquick link screen to function map
var screenToQuickLink = map[deeplinkPb.Screen]func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink{
	deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN],
			link,
			deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN)
	},
	deeplinkPb.Screen_NET_WORTH_HUB_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_NET_WORTH_HUB_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_NET_WORTH_HUB_SCREEN],
			link,
			deeplinkPb.Screen_NET_WORTH_HUB_SCREEN)
	},
	deeplinkPb.Screen_EPF_DASHBOARD: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_EPF_DASHBOARD],
			DeeplinkToShortDescMap[deeplinkPb.Screen_EPF_DASHBOARD],
			link,
			deeplinkPb.Screen_EPF_DASHBOARD)
	},
	deeplinkPb.Screen_PRE_APPROVED_LOAN_DASHBOARD_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_PRE_APPROVED_LOAN_DASHBOARD_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PRE_APPROVED_LOAN_DASHBOARD_SCREEN],
			link,
			deeplinkPb.Screen_PRE_APPROVED_LOAN_DASHBOARD_SCREEN)
	},
	deeplinkPb.Screen_PAY_QR_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_PAY_QR_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PAY_QR_SCREEN],
			link,
			deeplinkPb.Screen_PAY_QR_SCREEN)
	},
	deeplinkPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN],
			link,
			deeplinkPb.Screen_CREDIT_CARD_DASHBOARD_SCREEN)
	},
	deeplinkPb.Screen_PAY_VIA_UPI: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_PAY_VIA_UPI],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PAY_VIA_UPI],
			link,
			deeplinkPb.Screen_PAY_VIA_UPI)
	},
	deepLinkPb.Screen_HELP_MAIN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deepLinkPb.Screen_HELP_MAIN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_HELP_MAIN],
			link,
			deeplinkPb.Screen_HELP_MAIN)
	},
	deepLinkPb.Screen_PROFILE_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deepLinkPb.Screen_PROFILE_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PROFILE_SCREEN],
			link,
			deeplinkPb.Screen_PROFILE_SCREEN)
	},
	deepLinkPb.Screen_PROFILE_PRIVACY_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deepLinkPb.Screen_PROFILE_PRIVACY_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PROFILE_PRIVACY_SCREEN],
			link,
			deeplinkPb.Screen_PROFILE_PRIVACY_SCREEN)
	},
	deepLinkPb.Screen_PROFILE_SETTINGS: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deepLinkPb.Screen_PROFILE_SETTINGS],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PROFILE_SETTINGS],
			link,
			deeplinkPb.Screen_PROFILE_SETTINGS)
	},
	deepLinkPb.Screen_PROFILE_APP_PERMISSION_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deepLinkPb.Screen_PROFILE_APP_PERMISSION_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PROFILE_APP_PERMISSION_SCREEN],
			link,
			deeplinkPb.Screen_PROFILE_APP_PERMISSION_SCREEN)
	},
	deepLinkPb.Screen_PROFILE_PERSONAL_DETAILS: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deepLinkPb.Screen_PROFILE_PERSONAL_DETAILS],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PROFILE_PERSONAL_DETAILS],
			link,
			deeplinkPb.Screen_PROFILE_PERSONAL_DETAILS)
	},
	deepLinkPb.Screen_PROFILE_SETTINGS_NOTIFICATION: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deepLinkPb.Screen_PROFILE_SETTINGS_NOTIFICATION],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PROFILE_SETTINGS_NOTIFICATION],
			link,
			deeplinkPb.Screen_PROFILE_SETTINGS_NOTIFICATION)
	},
	deeplinkPb.Screen_PROFILE_UPI_QR_CODE_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_PROFILE_UPI_QR_CODE_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PROFILE_UPI_QR_CODE_SCREEN],
			link,
			deeplinkPb.Screen_PROFILE_UPI_QR_CODE_SCREEN)
	},
	deepLinkPb.Screen_PROFILE_SETTINGS_LEGAL: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deepLinkPb.Screen_PROFILE_SETTINGS_LEGAL],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PROFILE_SETTINGS_LEGAL],
			link,
			deeplinkPb.Screen_PROFILE_SETTINGS_LEGAL)
	},
	deepLinkPb.Screen_PROFILE_ABOUT_FI: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deepLinkPb.Screen_PROFILE_ABOUT_FI],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PROFILE_ABOUT_FI],
			link,
			deeplinkPb.Screen_PROFILE_ABOUT_FI)
	},
	deepLinkPb.Screen_TRANSFER_IN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deepLinkPb.Screen_TRANSFER_IN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_TRANSFER_IN],
			link,
			deeplinkPb.Screen_TRANSFER_IN)
	},
	deeplinkPb.Screen_CARD_USAGE_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_CARD_USAGE_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_CARD_USAGE_SCREEN],
			link,
			deeplinkPb.Screen_CARD_USAGE_SCREEN)
	},
	deeplinkPb.Screen_CARD_HOME_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_CARD_HOME_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_CARD_HOME_SCREEN],
			link,
			deeplinkPb.Screen_CARD_HOME_SCREEN)
	},
	deeplinkPb.Screen_CARD_SETTINGS_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_CARD_SETTINGS_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_CARD_SETTINGS_SCREEN],
			link,
			deeplinkPb.Screen_CARD_SETTINGS_SCREEN)
	},
	deeplinkPb.Screen_CARD_LIMITS_HOME_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_CARD_LIMITS_HOME_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_CARD_LIMITS_HOME_SCREEN],
			link,
			deeplinkPb.Screen_CARD_LIMITS_HOME_SCREEN)
	},
	deeplinkPb.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN],
			link,
			deeplinkPb.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN)
	},
	deeplinkPb.Screen_MY_REWARDS_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_MY_REWARDS_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_MY_REWARDS_SCREEN],
			link,
			deeplinkPb.Screen_MY_REWARDS_SCREEN)
	},
	deeplinkPb.Screen_REWARDS_WAYS_TO_EARN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_REWARDS_WAYS_TO_EARN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_REWARDS_WAYS_TO_EARN],
			link,
			deeplinkPb.Screen_REWARDS_WAYS_TO_EARN)
	},
	deeplinkPb.Screen_OFFERS_LANDING_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_OFFERS_LANDING_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_OFFERS_LANDING_SCREEN],
			link,
			deeplinkPb.Screen_OFFERS_LANDING_SCREEN)
	},
	deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN],
			link,
			deeplinkPb.Screen_REDEEMED_OFFERS_SCREEN)
	},
	deeplinkPb.Screen_FIT_LANDING_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_FIT_LANDING_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_FIT_LANDING_SCREEN],
			link,
			deeplinkPb.Screen_FIT_LANDING_SCREEN)
	},
	deeplinkPb.Screen_FIT_MY_RULES_PAGE: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_FIT_MY_RULES_PAGE],
			DeeplinkToShortDescMap[deeplinkPb.Screen_FIT_MY_RULES_PAGE],
			link,
			deeplinkPb.Screen_FIT_MY_RULES_PAGE)
	},
	deeplinkPb.Screen_REFERRALS_INVITE_FRIENDS: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_REFERRALS_INVITE_FRIENDS],
			DeeplinkToShortDescMap[deeplinkPb.Screen_REFERRALS_INVITE_FRIENDS],
			link,
			deeplinkPb.Screen_REFERRALS_INVITE_FRIENDS)
	},
	deeplinkPb.Screen_VKYC_STATUS_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		vkycDeeplink, _ := vkycPkg.BuildVKYCStatusDeeplink(&vkycPkg.StatusScreenOptions{
			EntryPoint: vkycBe.EntryPoint_ENTRY_POINT_SEARCH,
		})
		if link == nil {
			link = vkycDeeplink
		}
		return getWidgetQuickLinkUnit(DeeplinkToS3IconUrlMap[deeplinkPb.Screen_VKYC_STATUS_SCREEN], DeeplinkToShortDescMap[deeplinkPb.Screen_VKYC_STATUS_SCREEN], link, deeplinkPb.Screen_VKYC_STATUS_SCREEN)
	},
	deeplinkPb.Screen_VIEW_VKYC_SCHEDULE_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(DeeplinkToS3IconUrlMap[deeplinkPb.Screen_VKYC_STATUS_SCREEN], DeeplinkToShortDescMap[deeplinkPb.Screen_VIEW_VKYC_SCHEDULE_SCREEN], link, deeplinkPb.Screen_VIEW_VKYC_SCHEDULE_SCREEN)
	},
	deeplinkPb.Screen_MUTUAL_FUNDS_LIST_VIEW_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_MUTUAL_FUNDS_LIST_VIEW_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_MUTUAL_FUNDS_LIST_VIEW_SCREEN],
			link,
			deeplinkPb.Screen_MUTUAL_FUNDS_LIST_VIEW_SCREEN)
	},
	deeplinkPb.Screen_PROFILE_ACCOUNT_SETTINGS: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_PROFILE_ACCOUNT_SETTINGS],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PROFILE_ACCOUNT_SETTINGS],
			link,
			deeplinkPb.Screen_PROFILE_ACCOUNT_SETTINGS)
	},
	deeplinkPb.Screen_P2P_INVESTMENT_INVEST_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_P2P_INVESTMENT_INVEST_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_P2P_INVESTMENT_INVEST_SCREEN],
			link,
			deeplinkPb.Screen_P2P_INVESTMENT_INVEST_SCREEN)
	},
	deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN],
			link,
			deeplinkPb.Screen_DEPOSIT_LANDING_SCREEN)
	},
	deeplinkPb.Screen_PAY_VIA_MOBILE_NUMBER: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_PAY_VIA_MOBILE_NUMBER],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PAY_VIA_MOBILE_NUMBER],
			link,
			deeplinkPb.Screen_PAY_VIA_MOBILE_NUMBER)
	},
	deeplinkPb.Screen_PAY_VIA_BANK_TRANSFER: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_PAY_VIA_BANK_TRANSFER],
			DeeplinkToShortDescMap[deeplinkPb.Screen_PAY_VIA_BANK_TRANSFER],
			link,
			deeplinkPb.Screen_PAY_VIA_BANK_TRANSFER)
	},
	deeplinkPb.Screen_UPI_PIN_SETUP: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_UPI_PIN_SETUP],
			DeeplinkToShortDescMap[deeplinkPb.Screen_UPI_PIN_SETUP],
			link,
			deeplinkPb.Screen_UPI_PIN_SETUP)
	},
	deeplinkPb.Screen_FORGOT_PIN_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_FORGOT_PIN_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_FORGOT_PIN_SCREEN],
			link,
			deeplinkPb.Screen_FORGOT_PIN_SCREEN)
	},
	deeplinkPb.Screen_RESET_PIN_SCREEN: func(link *deeplinkPb.Deeplink) *widget.UnitQuickLink {
		return getWidgetQuickLinkUnit(
			DeeplinkToS3IconUrlMap[deeplinkPb.Screen_RESET_PIN_SCREEN],
			DeeplinkToShortDescMap[deeplinkPb.Screen_RESET_PIN_SCREEN],
			link,
			deeplinkPb.Screen_RESET_PIN_SCREEN)
	},
}

func getWidgetQuickLinkUnit(imgUrl, shortDesc string, link *deeplinkPb.Deeplink, screen deeplinkPb.Screen) *widget.UnitQuickLink {
	currLink := link
	if currLink == nil {
		currLink = &deepLinkPb.Deeplink{
			Screen: screen,
		}
	}
	return &widget.UnitQuickLink{
		ImageUrl:  imgUrl,
		ShortDesc: shortDesc,
		Link:      currLink,
	}
}

// FipId filter for Fi to Fi bottom sheet to appear
const (
	AllBanksSelected = "All"
	FISelected       = "Fi"
)
