// nolint: goimports
//
//go:generate conf_gen github.com/epifi/gamma/frontend/config Config
package config

import (
	"fmt"
	"math"
	"os"
	"path/filepath"
	"runtime"
	"sort"
	"strconv"
	"sync"
	"time"

	"github.com/pkg/errors"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	cfgv2 "github.com/epifi/be-common/pkg/cfg/v2"
	deviceIntegrityConfig "github.com/epifi/be-common/pkg/epifigrpc/interceptors/device_integrity/config"
	"github.com/epifi/be-common/pkg/frontend/app"
	typesPkg "github.com/epifi/be-common/pkg/types"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"

	fePb "github.com/epifi/gamma/api/frontend"
	"github.com/epifi/gamma/api/frontend/card"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	timelinePb "github.com/epifi/gamma/api/frontend/deeplink/timeline"
	"github.com/epifi/gamma/api/frontend/fittt"
	"github.com/epifi/gamma/api/frontend/investment/aggregator"
	txnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	types "github.com/epifi/gamma/api/typesv2"
	pkgDeeplink "github.com/epifi/gamma/pkg/deeplink"
	deeplinkCfg "github.com/epifi/gamma/pkg/deeplink/cfg"
	deeplinkcfgv2 "github.com/epifi/gamma/pkg/deeplink/cfg/v2"
	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
	pkgLoansConfig "github.com/epifi/gamma/pkg/loans/config"
	"github.com/epifi/gamma/pkg/usstocks"
	"github.com/epifi/gamma/preapprovedloan/config/common"
)

const (
	RudderWriteKey                       = "RudderWriteKey"
	DeviceIdsEnabledForSafetyNetV2       = "DeviceIdsEnabledForSafetyNetV2"
	ActiveDeviceIntegrityTokenSigningKey = "ActiveDeviceIntegrityTokenSigningKey"
	RazorpaySecretsKey                   = "RazorpaySecrets"
)

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(false)
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}

	return config, nil
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(true)
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to load config")
	}

	return config, nil
}

func loadConfig(onlyStaticFiles bool) (*Config, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()

	// loads config from file
	k, _, err1 := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.FRONTEND_SERVICE)

	if err1 != nil {
		return nil, fmt.Errorf("failed to load dynamic config: %w", err1)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to refresh dymanic config: %w", err)
	}
	if onlyStaticFiles {
		return conf, nil
	}
	if err = updateSecrets(conf); err != nil {
		return nil, fmt.Errorf("failed to load and update secrets var: %w", err)
	}
	err = readAndSetEnv(conf)
	if err != nil {
		return nil, errors.Wrap(err, "failed to read and set env")
	}

	return conf, nil
}

func updateSecrets(conf *Config) error {
	keyToSecret, err := cfg.LoadSecrets(conf.Secrets.Ids, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return err
	}
	for k, v := range keyToSecret {
		if conf.Secrets.Ids != nil {
			conf.Secrets.Ids[k] = v
		}
	}
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

// TODO(kunal): Add validate tags later and verify config struct post unmarshalling

type Config struct {
	Application                    *Application
	Server                         *Server
	Logging                        *cfg.Logging
	RedisOptions                   *cfg.RedisOptions
	PayErrorViewJson               string
	LegalDocuments                 *LegalDocuments
	ActorActivityAmountColourMap   *AmountColourMap
	TimelineEventDescription       *TimelineEventDescription
	TimelineEventBottomDescription *TimelineEventBottomDescription
	TimelineActionDisplayName      *TimelineActionDisplayName
	TimelineCards                  *TimelineCards
	BankIcon                       *BankIcon
	// Deprecated: in favour of VendorToAccountTypeToBankNameMapfron
	VendorToBankNameMap                *VendorToBankNameMap
	VendorToAccountTypeToNameMap       *VendorToAccountTypeToNameMap
	BottomSheets                       *BottomSheets
	DepositIcons                       *DepositIcon
	HomeV2DepositIcons                 *DepositIcon
	AttributionLinkParseConfigs        map[string]*AttributionLinkParseConfig `dynamic:"true"` // map["some_conf_name"]*AttributionLinkParseConfig
	P2PIcons                           *P2PIcons
	P2PActors                          *P2PActors
	Flags                              *Flags               `dynamic:"true"`
	RewardsFrontendMeta                *RewardsFrontendMeta `dynamic:"true" ,quest:"component,area:Rewards"`
	RudderStack                        *cfg.RudderStackBroker
	Secrets                            *cfg.Secrets
	Aws                                *AWS
	ActorActivityTimeStampLayout       string
	CardErrorViewJson                  string
	VKYC                               *VKYC           `dynamic:"true"`
	InsightsParams                     *InsightsParams `dynamic:"true"`
	AddFundsScreenParams               map[string][]*AddFundsParamOptions
	Fittt                              *Fittt `dynamic:"true" ,quest:"component,area:FIT"`
	AccountStatementErrorViewParams    *AccountStatementErrorViewParams
	Card                               *Card `dynamic:"true" ,quest:"component,area:DebitCard"`
	Comms                              *Comms
	TimelineTimestampFormats           *TimelineTimestampFormats
	AddFundsCTA                        *CTA `dynamic:"true"`
	FirstTransactionRewardsBanner      *RewardsBanner
	Signup                             *Signup      `dynamic:"true"`
	Screening                          *Screening   `dynamic:"true"`
	Referrals                          *Referrals   `dynamic:"true"`
	Alfred                             *Alfred      `dynamic:"true"`
	ReferralsV1                        *ReferralsV1 `dynamic:"true" ,quest:"component"`
	ReferralsColourMap                 *ReferralsColourMap
	UpiPinSetMessage                   *UpiPinSetMessage
	ConnectedAccount                   *ConnectedAccount                            `dynamic:"true"`
	DeviceIntegrity                    *deviceIntegrityConfig.DeviceIntegrityConfig `dynamic:"true"`
	SecureLogging                      *Logging
	ConnectedAccountUserGroupParams    *ConnectedAccountUserGroupParams
	HomeParams                         *HomeParams
	IPInterceptorParams                *IPInterceptorParams
	Investment                         *Investment                           `dynamic:"true"`
	AddFundsParams                     *AddFundsParams                       `dynamic:"true"`
	AFU                                *AFU                                  `dynamic:"true"`
	FeatureReleaseConfig               *releaseConfig.FeatureReleaseConfig   `dynamic:"true"`
	ABFeatureReleaseConfig             *releaseConfig.ABFeatureReleaseConfig `dynamic:"true"`
	PaymentInfoBanner                  *PaymentInfoBanner
	Tracing                            *cfg.Tracing
	Profiling                          *cfg.Profiling
	SalaryProgram                      *SalaryProgram `dynamic:"true" ,quest:"component,area:Salary"`
	Deposit                            *Deposit       `dynamic:"true" ,quest:"component,area:FixedDeposits"`
	Goals                              *Goals         `dynamic:"true"`
	ShowAutoRenewCta                   bool           `dynamic:"true"`
	MaxDaysBeforeDepositMaturityAcion  time.Duration  `dynamic:"true"`
	MinDaysBeforeDepositMaturityAction time.Duration  `dynamic:"true"`
	MinDepositAgeForRenewalEligible    time.Duration  `dynamic:"true"`
	// DisplayCategoryMappingJsonFilePath represents the file path which contains the
	// mapping b/w Display category enum and the String to deploy on UI and the icon of the category
	DisplayCategoryMappingJson        string
	BlacklistIfscForFip               []string
	AnalyserParams                    *AnalyserParams `dynamic:"true" ,quest:"component,area:Analyser"`
	TxnCatUserGroupParams             *TxnCatUserGroupParams
	SyncVendorApiDeviceIntegrityCheck *SyncVendorApiDeviceIntegrityCheck
	SharedConfig                      *SharedConfig  `dynamic:"true"`
	P2PInvestment                     *P2PInvestment `dynamic:"true" ,quest:"component,area:Jump"`
	Lending                           *Lending       `dynamic:"true" ,quest:"component,area:PersonalLoan"`
	UserProfileView                   *UserProfileView
	TxnCatParams                      *TxnCatParams
	Cx                                *Cx                  `dynamic:"true"`
	UpiOnboardingParams               *UpiOnboardingParams `dynamic:"true"`
	AppLogs                           *AppLogs             `dynamic:"true"`
	QrDeeplinkParams                  *QrDeeplinkParams    `dynamic:"true"`
	HomeRevampParams                  *HomeRevampParams    `dynamic:"true" ,quest:"component,area:Home"`
	LiteHomeRevampParams              *HomeRevampParams    `dynamic:"true" ,quest:"component,area:Home"`
	UserProfile                       *UserProfile         `dynamic:"true"`
	HomeExploreConfig                 *HomeExploreConfig   `dynamic:"true" ,quest:"component,area:Home"`
	OrderStatusIconUrl                *OrderStatusIconUrl
	// max remarks length for a transaction
	MaxRemarksLength                   int                    `dynamic:"true"`
	SimilarActivityParams              *SimilarActivityParams `dynamic:"true"`
	Chat                               *Chat                  `dynamic:"true"`
	ActorActivityAmountBadgeIconUrlMap *AmountBadgeIconUrlMap `dynamic:"true"`
	// Config to control forced add money stage for MIN KYC users
	MinKycMandatoryAddFundConfig *MandatoryMinKycAddFundConfig `dynamic:"true"`
	// Config for loading timeline events description for CC events
	CCTimelineEventDescription *CCTimelineEventDescription
	// Payment related params
	PayParams               *PayParams `dynamic:"true"`
	EpifiIconsBucketName    string     `iam:"s3-readonly"`
	SalaryProgramBucketName string     `iam:"s3-readonly"`
	Tiering                 *Tiering   `dynamic:"true" ,quest:"component,area:Tiering"`
	TimelineEventUIParams   *TimelineEventUIParams
	USStocks                *USStocks        `dynamic:"true" ,quest:"component,area:USStocks"`
	FiMinutesConfig         *FiMinutesConfig `dynamic:"true"`
	// Parameters related to add funds v2 flow
	AddFundsV2Params   *AddFundsV2Params    `dynamic:"true"`
	RpcRateLimitConfig *cfg.RateLimitConfig `dynamic:"true"`
	// UpiNumberParams - config params for upi numbers
	UpiNumberParams *UpiNumberParams `dynamic:"true"`
	// MerchantIdCapabilityMap contains merchant id for which we need to
	// enable special capabilities on timeline like pay and request
	MerchantIdTimelineCapabilityMap map[string]map[string]bool
	// dispute config
	Dispute  *Dispute          `dynamic:"true"`
	QuestSdk *sdkconfig.Config `dynamic:"true"`

	SavingsBalanceTracker *SavingsBalanceTracker
	// user should see a warning message while creating a recurring payment of type mandates
	WarningMessageForMandateCreation *WarningMessageForMandateCreation `dynamic:"true"`
	// TODO(SAURABH) : Add Quest Engine Support to the variables of creditCard
	CreditCard            *CreditCard            `dynamic:"true" quest:"component,area:CreditCard"`
	AutoInvestStoryConfig *AutoInvestStoryConfig `dynamic:"true"`
	EmailDomainCheck      *EmailDomainCheck      `dynamic:"true"`
	// json file that contains SMS scan config to be shared with clients
	SMSScannerConfigJsonFilePath string
	// json file that contains the mapping sms regex templates for various SMS vendor names
	SMSTemplatesConfigJsonFilePath string
	// mapping for ifsc code (first 4 letters of IFSC in uppercase to be considered) to bankLogoUrl
	IfscToBankLogoUrlMap map[string]string
	// List of non Tpap psp handles
	NonTpapPspHandles []string
	// url of the image where we show a clock in case the txn is pending
	PendingTransactionShortDescIconUrl string
	// text which we need to show on all transactions page in case transaction is failed, reversed or in payment.
	AllTransactionsShortDesc *AllTransactionsShortDesc

	AskFiHomeSearchBarConfig *AskFiHomeSearchBarConfig `dynamic:"true" ,quest:"component,area:AskFi"`
	// Success screen for upi pin set
	UpiPinSetSuccessScreenInfo *UpiPinSetSuccessScreenInfo
	// UpiPinActionConfig - used to represent upi pin set options categorized in the following way:
	// - FlowType
	//		- Fi
	// 			- DEBIT_CARD
	// 			- AADHAAR_NUMBER
	//      - NonFi
	//       	- DEBIT_CARD
	// 			- AADHAAR_NUMBER
	UpiPinActionConfig map[string]map[string]*UpiPinSetIntroScreenItem
	// PaymentHealthStateInfo to be shown to the user based upon the health of the different entities involved in the transaction like banks,psp etc
	PaymentHealthStateInfo *PaymentHealthStateInfo `dynamic:"true"`
	// upi international config for different international payment status for account manager screen
	InternationalPaymentActionParam *InternationalPaymentActionParam

	// ActivateAccountsForInternationalUpiPaymentsInfo -
	//  represents the info to be shown when
	//	no account with international payments is
	//	enabled for the user
	ActivateAccountsForInternationalUpiPaymentsInfo *ActivateAccountsForInternationalUpiPaymentsInfo

	// ForeignCurrencyInfo -
	// - represents the currency code to currency info mapping
	// - User should see all the available currencies in which
	//   payment could be made to the merchant
	ForeignCurrencyInfoMap map[string]*ForeignCurrencyInfo

	// DefaultCurrencyCodeInfo - info to be used in case an unknown currency code is received
	// in QR while making international upi payments
	DefaultCurrencyCodeInfo *ForeignCurrencyInfo
	// International Payment Action status success screen details
	InternationalPaymentSuccessScreenInfo *ActionStatusScreenInfo
	// International Payment Action status failure screen details
	InternationalPaymentFailureScreenInfo *ActionStatusScreenInfo
	// UpiActionParams - params used for generating actions for an upi account
	UpiActionParams *UpiActionParams `dynamic:"true"`
	// Params used in showing eligible accounts for payments
	EligibleAccountParams *EligibleAccountParams `dynamic:"true"`
	// PostPaymentScreenParams - params associated with new post payment screens
	// https://www.figma.com/file/xzcr5E8lsH1ihsh3RmqQTU/Pay-%E2%80%A2-FFF-%E2%80%A2--v1.3?type=design&node-id=9096-20294&t=kaYzMXYLlKK5KUih-0
	PostPaymentScreenParams *PostPaymentScreenParams
	// SharePostPaymentScreenParams - params associated with share post payment screens
	// https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=16998-101112&node-type=canvas&t=3FKV3ggIgB4Sunf9-0
	SharePostPaymentScreenParams *SharePostPaymentScreenParams `dynamic:"true"`
	// FeedbackEngineConfig contains various flags and values for inapphelp feedback engine
	FeedbackEngineConfig *FeedbackEngineConfig `dynamic:"true"`

	// EnableUpiLiteOptionParams - contains params required to
	// give enable upi lite option on account manager screen
	EnableUpiLiteOptionParams *EnableUpiLiteOptionParams

	// UpiLiteAddMoneyScreenParams - stores the all the styling info for upi lite add money screen
	// figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=16082-99808&mode=design&t=t0disJUEJxOmPfkn-4
	UpiLiteAddMoneyScreenParams *UpiLiteAddMoneyScreenParams
	NetworthConfig              *NetworthConfig     `dynamic:"true"`
	BKYC                        *BiometricKYCConfig `dynamic:"true"`
	TargetGroup                 *TargetGroupConfig  `dynamic:"true"`

	// min age to onboard to AA (used during PAN validation)
	NsdlPanValidationAgeThreshold int32 `dynamic:"true"`

	RecordContactDetailsVersion int32 `dynamic:"true"`
	TimelinePaymentPrompts      *TimelinePaymentPrompts
	OrderReceipt                *OrderReceipt `dynamic:"true"`

	// params used in showing list account screen content
	ListAccountScreenParams *ListAccountScreenParams

	// vpa migration screen params in different scenarios
	// Intro screen, response screen (Success/Pending/Failure)
	VpaMigrationScreenParams *VpaMigrationScreenParams
	ExploreSectionsForCC     []*ExploreSection

	// Params for the intro screen to connect tpap bank accounts
	// E.g. In Fi lite , if user tries to pay but has no tpap account
	// connected, then it will be prompted to connect bank account
	// using this screen
	ConnectTpapBankAccountIntroScreenParams *ConnectTpapBankAccountIntroScreenParams

	// user should see the option to activate account
	// in account manager screen for all the inactive
	// accounts
	ActivateAccountButtonParams *TextObject

	ParentNameSanityCheckConfig *ParentNameSanityChecks `dynamic:"true"`

	SavingsAccountClosure *SavingsAccountClosure `dynamic:"true"`

	// Params for the pay landing screen details
	PayLandingScreenParams *PayLandingScreenParams `dynamic:"true"`

	// Params for AMB entrypoint attention banner
	AMBEntrypointBannerParams *AMBEntrypointBannerParams `dynamic:"true" ,quest:"component,area:Tiering"`
	// config to switch on/off the option to add tpap accounts
	TpapLinkingEntryPointConfig map[string]*SaUserAndNonSaUserConfig `dynamic:"true"`

	// config to switch on/off the option to link credit cards for payments
	CreditCardLinkingEntryPointConfig map[string]*SaUserAndNonSaUserConfig `dynamic:"true"`

	CreditReportConfig *common.CreditReportConfig `dynamic:"true"`

	// config for payment options screen(screen that comes after add funds etc.)
	PaymentOptionsConfig *PaymentOptionsConfig `dynamic:"true"`

	DynamicElementsConfig *DynamicElementsConfig `dynamic:"true"`

	// mapping b/w bank name and bank logo urls.
	// It refers to the bank names received in
	// the ListAccount vendor api response.
	BankNameToLogoUrlMap map[string]string

	// FaqServingConfig contains parameters to serve FAQs using inapphelp backend service
	FaqServingConfig *FaqServingConfig `dynamic:"true"`

	// version checks for Tpap unified flow (VPA Migration + Other bank accounts)
	TpapUnifiedFlowReleaseVersions *TpapUnifiedFlowReleaseVersions `dynamic:"true"`

	FiStoreConfig *FiStoreConfig `dynamic:"true"`

	MediaConf *MediaConfig `dynamic:"true"`

	EnableGetPaymentOptionsV1 bool `dynamic:"true"`

	BankVendorInfoJsonPath string `dynamic:"true"`

	// config which aids in debugging the on-app enach issues related to redirection to webview
	OnAppEnachRedirectionDebug *OnAppEnachRedirectionDebug `dynamic:"true"`

	// Config to drive client initialization and other options for Ms clarity sdk. Ref:
	// https://learn.microsoft.com/en-us/clarity/mobile-sdk/android-sdk#configuration-options
	MsClarityConfig *MsClarityConfig `dynamic:"true"`

	// config which sets the text in trust marker widget
	TrustMarkerInfoConfig *TrustMarkerInfoConfig `dynamic:"true"`
	// config which sets the text in trust marker widget for pay landing screen
	TrustMarkerFooterPayConfig *TrustMarkerFooterPayConfig `dynamic:"true"`
	// fi store collected offers card configs
	FiStoreCollectedOffersConfig *FiStoreCollectedOffersConfig `dynamic:"true"`
	// config for pay order receipt screen to show reward details.
	RewardDetailsForOrderReceiptConfig *RewardDetailsForOrderReceiptConfig `dynamic:"true"`
	// config for showing rewards details on actor activities page.
	RewardSummaryForActorActivitiesPageConfig *RewardSummaryForActorActivitiesPageConfig `dynamic:"true"`
	// config for UI entry point support in the ResolveQrData flow to ensure client is sending
	// the right data in the new builds.
	UIEntryPointConfigForResolveQr *app.FeatureConfig `dynamic:"true"`
	// config for screen details support for Call language preference screens
	CallLanguagePreferenceConfig *CallLanguagePreferenceConfig `dynamic:"true"`
	// Client retry polling strategy for GetNextOnboardingAction
	GetNextOnboardingActionRetryStrategy *cfg.RetryParams
	// Home params config for unit tests
	HomeRevampParamsForUnitTests *HomeRevampParamsForUnitTests `dynamic:"true"`

	// Config for recurring payment screen.(This has config related to revoking recurring payment).
	// Figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=23674-47816&t=ywXRjXK7NL1od7aG-0
	RecurringPaymentScreenParams *RecurringPaymentScreenParams

	// Has subject and description for cx support fresh desk ticket.
	// We are creating this ticket for requesting cancellation of off app eNACH recurring payment.
	OffAppEnachCancellationCxTicketParams *OffAppEnachCancellationCxTicketParams
	// GetNextOnboardingActionFiLiteRedirectTime signifies duration after which continued polling on GNOA would redirect user to FiLite.
	GetNextOnboardingActionFiLiteRedirectTime time.Duration `dynamic:"true"`

	VKYCCall *VKYCCall `dynamic:"true"`

	EnableReferralProgramForSavingsAccount bool `dynamic:"true"`

	// config to determine whether to use the new device registration flow in UPI flows
	UpiNewDeviceRegFeatureConfig *app.FeatureConfig `dynamic:"true"`

	// config related to help recent activity owned by CX
	HelpRecentActivity *HelpRecentActivity `dynamic:"true"`

	// config related to contact us flow
	// which is used to get information about issue from user before reaching out customer care
	InAppContactUsFlowConfig *InAppContactUsFlowConfig `dynamic:"true"`

	// Configs for money secrets
	MoneySecretsConfig *MoneySecretsConfig `dynamic:"true"`

	// flags to enable/disable the new home features using quest variables.
	HomeFeatureQuestFlags *HomeFeatureQuestFlags `dynamic:"true" ,quest:"component,area:Home"`

	MoneySecrets *MoneySecrets `dynamic:"true"`

	AlternateAppIconConfig *AlternateAppIconConfig

	// figma -https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=31038-69137&t=zxd7EHpknkSuDLOq-0
	PaySettingsWidgetUiConfig *PaySettingsWidgetUiConfig `dynamic:"true"`

	HomeBalanceSummaryWidgetUiConfig *HomeBalanceSummaryWidgetUiConfig `dynamic:"true"`

	MapperQuickLinkConfig *MapperQuickLinkConfig `dynamic:"true"`

	TimelineActionV2Config *TimelineActionV2Config `dynamic:"true"`

	DailyNetworthConfig *DailyNetworthConfig `dynamic:"true"`

	PayWidgetAllTxnConfig          *PaySettingsWidgetUiConfig `dynamic:"true"`
	PayWidgetAutoPayConfig         *PaySettingsWidgetUiConfig `dynamic:"true"`
	PayWidgetManageUpiNumberConfig *PaySettingsWidgetUiConfig `dynamic:"true"`

	// Tiering based notch configurations
	TieringNotchConfig *TieringNotchConfig `dynamic:"true" quest:"component,area:Tiering"`
	// adding logs only to specific actors for debugging
	// todo(Suruthiga) remove logs after debugging
	ActorsWhitelistedForLogs map[string]bool `dynamic:"true"`

	CcStatementBanner                       *AllTransactionHeaderBanner
	AllTransactionHeaderBanner              *AllTransactionHeaderBanner              `dynamic:"true"`
	TieringBenefitsEarnedScreenHeaderBanner *TieringBenefitsEarnedScreenHeaderBanner `dynamic:"true"`
	CcAllTransactionHeaderBanner            *AllTransactionHeaderBanner              `dynamic:"true"`
	PostConversionMigrationDate             *PostConversionMigrationDate             `dynamic:"true"`
}

type PostConversionMigrationDate struct {
	Day   int   `dynamic:"true"`
	Month int32 `dynamic:"true"`
	Year  int   `dynamic:"true"`
}

type TieringBenefitsEarnedScreenHeaderBanner struct {
	Text          string `dynamic:"true"`
	LeftPadding   int32  `dynamic:"true"`
	RightPadding  int32  `dynamic:"true"`
	TopPadding    int32  `dynamic:"true"`
	BottomPadding int32  `dynamic:"true"`
	ImageUrl      string `dynamic:"true"`
}

type AllTransactionHeaderBanner struct {
	PreConversion  *FiPointsConversionBanner `yaml:"PreConversion" dynamic:"true"`
	PostConversion *FiPointsConversionBanner `yaml:"PostConversion" dynamic:"true"`
}

type FiPointsConversionBanner struct {
	Text               string `dynamic:"true"`
	LeftPadding        int32  `dynamic:"true"`
	RightPadding       int32  `dynamic:"true"`
	TopPadding         int32  `dynamic:"true"`
	BottomPadding      int32  `dynamic:"true"`
	FiPointImageUrl    string `dynamic:"true"`
	RightArrowImageUrl string `dynamic:"true"`
	LeftImgTxtPadding  int32  `dynamic:"true"`
	RightImgTxtPadding int32  `dynamic:"true"`
}

// SegmentConf defines common attributes of a segment
type SegmentConf struct {
	// SegmentId is used to determine actor's membership with the segment using segment service in case of UseRealtime=false
	SegmentId string `dynamic:"true" quest:"variable"`
	// SegmentTemplate defines the template to be used in the notch message if segmented into the current segment
	SegmentTemplate string `dynamic:"true" quest:"variable"`
	// Users are segmented to the first segment that the user satisfy, in the order of Priority
	Priority int32 `dynamic:"true" quest:"variable"`
	// UseRealtime flag helps in determining either to use realtime or nonRealtime implementation for determining the segment
	// NonRealTime : determine the user's membership with segment with the help of segment service. This might contain stale membership of users into segments.
	// RealTime : By depending on the criteria used for determining the segment, we can find the membership with segment by fetching and analyzing fresh data required for the segment to be determined.
	UseRealtime bool `dynamic:"true" quest:"variable"`
}

type TieringNotchConfig struct {
	UpgradeJourneyConf   *UpgradeJourneyConf   `dynamic:"true" quest:"component"`
	EarningPotentialConf *EarningPotentialConf `dynamic:"true" quest:"component"`
	// AbuserConfig defines  AbuserFlagged segment specific configurations
	// external.Tier -> tier minimum amb to maintain to not mark as AbuserFlagged segment member
	AbuserConfig map[string]int32 `dynamic:"true"`
	// SegmentConfMap defines the common config properties
	// SegmentName->SegmentConf
	SegmentConfMap map[string]*SegmentConf `dynamic:"true,readonlymap" quest:"component"`

	// Feature flag to enable/disable notch config enrichment logic
	EnableNotchConfigEnrichmentFeatureFlag *cfg.FeatureReleaseConfig `dynamic:"true"`
}

// EarningPotentialConf defines EarningPotentialLow/EarningPotentialHigh segment specific configurations
type EarningPotentialConf struct {
	// LowPotentialLimit defines below how much aggregated fi coins rewards, is the user segmented into either EarningPotentialLow or EarningPotentialHigh segment
	LowPotentialLimit int32 `dynamic:"true" quest:"variable"`
}

// UpgradeJourneyConf defines UpgradeJourney segment specific configurations
type UpgradeJourneyConf struct {
	// SegmentExpiry is the duration (in days) a user can remain in the segment after first being entered into the segment.
	SegmentExpiry int32 `dynamic:"true" quest:"variable"`
}

type TimelineActionV2Config struct {
	// PayButtonConfig defines the visual styling and text for timeline payment buttons
	PayButtonConfig *ActionV2PayButtonConfig `dynamic:"true"`
}

type ActionV2PayButtonConfig struct {
	// Container defines the button's layout properties
	Container *ActionV2ContainerConfig `dynamic:"true"`
	// Text defines the button's text content and style
	Text *ActionV2TextConfig `dynamic:"true"`
}

type ActionV2ContainerConfig struct {
	BgColor       string `dynamic:"true"`
	CornerRadius  int32  `dynamic:"true"`
	BottomPadding int32  `dynamic:"true"`
	LeftPadding   int32  `dynamic:"true"`
	RightPadding  int32  `dynamic:"true"`
	TopPadding    int32  `dynamic:"true"`
}

type ActionV2TextConfig struct {
	Content   string `dynamic:"true"`
	Color     string `dynamic:"true"`
	FontStyle string `dynamic:"true"`
}

type PaySettingsWidgetUiConfig struct {
	TitleText    string `dynamic:"true"`
	LeftIconUrl  string `dynamic:"true"`
	RightIconUrl string `dynamic:"true"`
}

type AlternateAppIconConfig struct {
	OccasionRanges map[string][]DateRange
}

type DateRange struct {
	Start time.Time
	End   time.Time
}

type RewardSummaryForActorActivitiesPageConfig struct {
	FiCoinSymbolIconUrl     string                  `dynamic:"true"`
	RupeeSymbolIconUrl      string                  `dynamic:"true"`
	FiPointSymbolIconUrl    string                  `dynamic:"true"`
	RewardedFiCoinsSummary  *RewardSummaryWidgetRow `dynamic:"true"`
	RewardedCashbackSummary *RewardSummaryWidgetRow `dynamic:"true"`
	RewardedFiPointsSummary *RewardSummaryWidgetRow `dynamic:"true"`
	ForexRefundsSummary     *RewardSummaryWidgetRow `dynamic:"true"`
	RewardsEarnedValueChip  *RewardsEarnedValueChip `dynamic:"true"`
}

type RewardSummaryWidgetRow struct {
	Icon        *VisualElementImage `dynamic:"true"`
	Title       *Text               `dynamic:"true"`
	EarnedValue *IconTextComponent  `dynamic:"true"`
	BgColor     string              `dynamic:"true"`
}

type RewardsEarnedValueChip struct {
	Value           *IconTextComponent `dynamic:"true"`
	AdditionalValue *IconTextComponent `dynamic:"true"`
}

type RewardDetailsForOrderReceiptConfig struct {
	FiCoinSymbolIconUrl            string                    `dynamic:"true"`
	RupeeSymbolIconUrl             string                    `dynamic:"true"`
	ProjectedFiCoinSymbolIconUrl   string                    `dynamic:"true"`
	FiPointsSymbolIconUrl          string                    `dynamic:"true"`
	ProjectedFiPointsSymbolIconUrl string                    `dynamic:"true"`
	ProjectedRupeeSymbolIconUrl    string                    `dynamic:"true"`
	FiCoinsEarnedIconUrl           string                    `dynamic:"true"`
	CashbackEarnedIconUrl          string                    `dynamic:"true"`
	TimerIconUrl                   string                    `dynamic:"true"`
	ProjectedRewardDescription     string                    `dynamic:"true"`
	RewardSummarySection           *RewardSummarySection     `dynamic:"true"`
	RewardDetailsBottomSheet       *RewardDetailsBottomSheet `dynamic:"true"`
}

type RewardSummarySection struct {
	Title                     string `dynamic:"true"`
	SubTitle                  string `dynamic:"true"`
	FontColorForFooterInfo    string `dynamic:"true"`
	TextForFooterInfo         string `dynamic:"true"`
	LeftElementForFooterInfo  string `dynamic:"true"`
	RightElementForFooterInfo string `dynamic:"true"`
	TopPadding                int32  `dynamic:"true"`
	BottomPadding             int32  `dynamic:"true"`
	LeftImgTxtPadding         int32  `dynamic:"true"`
}

type RewardDetailsBottomSheet struct {
	Title                                  string `dynamic:"true"`
	BgColor                                string `dynamic:"true"`
	RowBgColor                             string `dynamic:"true"`
	ProjectedRewardTncBottomSheetInfoText  string `dynamic:"true"`
	ActualizedRewardTncBottomSheetInfoText string `dynamic:"true"`
	CtaBgColor                             string `dynamic:"true"`
	CtaFontColor                           string `dynamic:"true"`
	CtaText                                string `dynamic:"true"`
}

type TrustMarkerInfoConfig struct {
	// header line 1 text
	FirstTitleHeader string `dynamic:"true"`
	// header second line
	SecondTitleHeader string `dynamic:"true"`
	// subtitle text
	SubtitleText string `dynamic:"true"`
	// Stories Button Text
	StoriesButtonText string `dynamic:"true"`
	// Stories Deeplink Url
	StoriesDeeplinkURL string `dynamic:"true"`
	// Disable Click Action
	DisableClickFeature bool `dynamic:"true"`
}

type TrustMarkerFooterPayConfig struct {
	// header line 1 text
	FirstTitleHeader string `dynamic:"true"`
	// header second line
	SecondTitleHeader string `dynamic:"true"`
	// Stories Button Text
	StoriesButtonText string `dynamic:"true"`
	// Stories Deeplink Url
	StoriesDeeplinkURL string `dynamic:"true"`
	// Disable Click Action
	DisableClickFeature bool `dynamic:"true"`
	// Disclaimer text
	DisclaimerText string `dynamic:"true"`
}

type OnAppEnachRedirectionDebug struct {
	// flag to toggle whether we should send the redirection URL to the client or not.
	// this will help us in tapping into the URL and the form-data so that we can debug the same using, lets say, postman.
	ReturnRedirectionUrl bool `dynamic:"true"`
	// list (technically map) to open the debugging only for select actors-ids.
	// Note: it can allow for all actors by specifying 'ALL' as the actor-id.
	AllowedActors map[string]bool `dynamic:"true"`
}

// PaymentOptionDisplayInfo will contain the display attributes of various payment options
type PaymentOptionDisplayInfo struct {
	ScreenTitle                        string                              `dynamic:"true"`
	CardPaymentOptionDisplayInfo       *CardPaymentOptionDisplayInfo       `dynamic:"true"`
	NetbankingPaymentOptionDisplayInfo *NetbankingPaymentOptionDisplayInfo `dynamic:"true"`
	TpapPaymentOptionDisplayInfo       *TpapPaymentOptionDisplayInfo       `dynamic:"true"`
	IntentPaymentOptionDisplayInfo     *IntentPaymentOptionDisplayInfo     `dynamic:"true"`
	NeftImpsPaymentOptionDisplayInfo   *NeftImpsPaymentOptionDisplayInfo   `dynamic:"true"`
	CollectPaymentOptionDisplayInfo    *CollectPaymentOptionDisplayInfo    `dynamic:"true"`
}

// CollectPaymentOptionDisplayInfo contains display details of the collect display option
type CollectPaymentOptionDisplayInfo struct {
	Title     string `dynamic:"true"`
	FontColor string `dynamic:"true"`
}

// NeftImpsPaymentOptionDisplayInfo contains display details of the neft/imps display option
type NeftImpsPaymentOptionDisplayInfo struct {
	Title     string `dynamic:"true"`
	FontColor string `dynamic:"true"`
}

// IntentPaymentOptionDisplayInfo contains display details of the intent display option
type IntentPaymentOptionDisplayInfo struct {
	Title     string `dynamic:"true"`
	FontColor string `dynamic:"true"`
}

// TpapPaymentOptionDisplayInfo contains display details of the tpap payment option
type TpapPaymentOptionDisplayInfo struct {
	Title     string `dynamic:"true"`
	FontColor string `dynamic:"true"`
}

// NetbankingPaymentOptionDisplayInfo contains display details of the netbanking payment option
type NetbankingPaymentOptionDisplayInfo struct {
	Title            string `dynamic:"true"`
	FontColor        string `dynamic:"true"`
	DefaultBankCount int    `dynamic:"true"`
}

// CardPaymentOptionDisplayInfo contains texts and  placeholders for various text fields to be shown
// for payment using card. For eg. cvv, card number, etc.
type CardPaymentOptionDisplayInfo struct {
	Title                            string `dynamic:"true"`
	FontColor                        string `dynamic:"true"`
	CvvTextFieldPlaceholder          string `dynamic:"true"`
	CardNumberTextFieldPlaceholder   string `dynamic:"true"`
	CustomerNameTextFieldPlaceholder string `dynamic:"true"`
	CardExpiryTextFieldPlaceholder   string `dynamic:"true"`
	CvvCharacterLimit                int32  `dynamic:"true"`
}

type FaqServingConfig struct {
	// categoryGroupEnumToText contains the mapping from the category group enum
	// to the actual category text that would be displayed to the user
	CategoryGroupEnumToText map[string]string `dynamic:"true"`
	// categoryGroupEnumToRank contains the mapping from the category group enum
	// to the rank to the category list
	CategoryGroupEnumToRank map[string]int32 `dynamic:"true"`
}

type DynamicElementsConfig struct {
	BannerElementContentV2UiVariantV2FeatureConfig *app.FeatureConfig `dynamic:"true"`
}

type SaUserAndNonSaUserConfig struct {
	IsEnabledForSaUser    bool `dynamic:"true"`
	IsEnabledForNonSaUser bool `dynamic:"true"`
}

type PaymentOptionsConfig struct {
	// Map with transaction entry point as String() as key and Amount bucket rules as value
	// amount buckets should be non overlapping for a valid config
	EntryPointToBucketRulesMap map[string][]*BucketRule
	TpapOptionsConfig          *TpapOptionsConfig   `dynamic:"true"`
	IntentOptionsConfig        *IntentOptionsConfig `dynamic:"true"`
	// CoolOffPeriodDuration is the duration which we want to check whether user is in upi payments cool off in that past duration until now
	// eg: if CoolOffPeriodDuration is 24h then cool off will be checked for time.Now()-24h
	// Note: This needs to be in sync with UPIParams.UpiLimitWindow in order config
	CoolOffPeriodDuration time.Duration `dynamic:"true"`
	// Map with transaction entry point as string() as key and paymentOptionsDisplayInfo(for eg - title, ctaText etc.) as value
	EntryPointToPaymentOptionsDisplayInfoMap map[string]*PaymentOptionDisplayInfo `dynamic:"true"`
	// Map with transaction entry point as string() as key and DataCollector svc config,
	// DataCollector svc here is the sub-service used to fetch DATA required to construction paymentOptions screen
	EntryPointToDataCollectorConfigMap map[string]*PaymentOptionsDataCollectorConfig `dynamic:"true"`
}

type PaymentOptionsDataCollectorConfig struct {
	// flag to enable/disable listing of internal account on AllPaymentOptions screen for payments
	AllowInternalAccountListing bool `dynamic:"true"`
	// flag to enable/disable listing of rupay account on AllPaymentOptions screen for payments
	AllowRupayAccountListing bool `dynamic:"true"`
}

type TpapOptionsConfig struct {
	// Max amount allowed in TPAP payment option from a single account
	MaxAmountAllowed int64 `dynamic:"true"`
	// Max amount allowed in TPAP payment option from a single account while in cool off
	MaxAmountAllowedWhileInCoolOff int64 `dynamic:"true"`
}

type IntentOptionsConfig struct {
	// Package info for android
	// For ios, we will reuse the AddFundsV2Params.UpiAppUrnPrefixMap config
	UpiAppsAndroidPackages   []*UpiAppsAndroidPackage
	TotalAppsToShowByDefault int32 `dynamic:"true"`
	// used to override SectionsToDisplay config for users in these groups
	AllowedUserGroups []commontypes.UserGroup
}

type UpiAppsAndroidPackage struct {
	// package id for android, for e.g. com.phonepe.app
	PackageId string
	AppLogo   string
	// App name, for e.g. "Phone Pe", "Google Pay"
	AppName string
}

type BucketRule struct {
	// Amount bucket wth lower and upper bounds
	AmountBucket *AmountBucket
	// TpapAccountsCoolOff is one of the conditions for the bucket rule
	TpapAccountsCoolOff *TpapAccountsCoolOff
}

type AmountBucket struct {
	// LowerBound of the bucket(inclusive)
	LowerBound int64 `dynamic:"true"`
	// UpperBound of the bucket(inclusive)
	// if upper bound is not provided/zero then consider INT64_MAX
	UpperBound int64 `dynamic:"true"`
}

// TpapAccountsCoolOff defines the config for cool off behaviour of connected tpap accounts
// User can have no accounts/in cool off/ not in cool off
type TpapAccountsCoolOff struct {
	// CoolOffNotApplicable case if user doesn't have any TPAP accounts presence
	CoolOffNotApplicable *OptionsDisplay
	// InCoolOff case when user has any one account in cool off
	InCoolOff *OptionsDisplay
	// NotInCoolOff case when user has any of the accounts in cool off
	NotInCoolOff *OptionsDisplay
}

// OptionsDisplay is a custom data type to define the behaviour and order of different sections
type OptionsDisplay struct {
	// SectionsToDisplay defines the config for what sections to display and in what order
	// eg: [ 1, 4 ]
	SectionsToDisplay []txnPb.PaymentOptionType
	// SectionsToExpand defines the config for what sections to expand
	// eg: [ 4 ]
	SectionsToExpand []txnPb.PaymentOptionType
	// UnavailableSections defines the config for what sections are unavailable
	// eg: [1]
	UnavailableSections []txnPb.PaymentOptionType
}

type SavingsAccountClosure struct {
	UserFeedBackOptions []*UserFeedbackOption
	CancelRequestWindow time.Duration             `dynamic:"true"`
	DisplayValues       *SaClosureDisplayValues   `dynamic:"true"`
	IssueCategoryIds    *SaClosureIssueCategoryId `dynamic:"true"`
	// duration after which support ticket is considered as expired and a new ticket is created
	SupportTicketExpiryDuration        time.Duration          `dynamic:"true"`
	FullGroupCriteiaItemMinAppVersions *MinVersionConstraints `dynamic:"true"`
}

type SaClosureIssueCategoryId struct {
	UnResolvedIssueFeedback       string `dynamic:"true"`
	CloseAccountsWithCreditFreeze string `dynamic:"true"`
}

type SaClosureDisplayValues struct {
	BenefitsScreen                *BenefitsScreen                `dynamic:"true"`
	FeedbackTicketSubmittedScreen *FeedbackTicketSubmittedScreen `dynamic:"true"`
	CriteriaScreen                *SaClosureCriteriaScreen       `dynamic:"true"`
}

type SaClosureCriteriaScreen struct {
	// map of saClosureEnums.SaClosureCriteriaItem to *SaClosureCriteriaItem
	CriteriaItems map[string]*SaClosureCriteriaItem `dynamic:"true"`
}

type SaClosureCriteriaItem struct {
	IconUrl              string `dynamic:"true"`
	Title1               string `dynamic:"true"`
	Title2               string `dynamic:"true"`
	CtaText              string `dynamic:"true"`
	NegativeValueCtaText string `dynamic:"true"`
	CtaDeeplink          string `dynamic:"true"`
}

type BenefitsScreen struct {
	PageTitle                 *typesPkg.Text               `dynamic:"true"`
	BenefitsSectionHeaderText *typesPkg.Text               `dynamic:"true"`
	BenefitsSectionHeaderIcon *typesPkg.VisualElementImage `dynamic:"true"`
	BenefitsSectionProperties *ContainerPropertiesDynamic  `dynamic:"true"`
	BenefitItems              []*BenefitItem
	ProceedCta                *CTA `dynamic:"true"`
	CancelCta                 *CTA `dynamic:"true"`
}

type BenefitItem struct {
	Icon     *typesPkg.VisualElementImage
	Title    *typesPkg.Text
	SubTitle *typesPkg.Text
	BgColor  string
}

type FeedbackTicketSubmittedScreen struct {
	Icon     *typesPkg.VisualElementImage `dynamic:"true"`
	Title    *typesPkg.Text               `dynamic:"true"`
	SubTitle *typesPkg.Text               `dynamic:"true"`
	Cta      *CTA                         `dynamic:"true"`
	BackIcon *typesPkg.VisualElementImage `dynamic:"true"`
	BgColor  string                       `dynamic:"true"`
}

type UserFeedbackOption struct {
	Text                      string
	TakeToResolveIssuesScreen bool
	IsCustomFeedback          bool
	CustomFeedbackPlaceholder string
}

type ParentNameSanityChecks struct {
	StopWords []string `dynamic:"true"`
}

type ConnectTpapBankAccountIntroScreenParams struct {
	Icon                  *ImageProperties
	Title                 *TextObject
	Subtitle              *TextObject
	Cta                   *CtaForAction
	Tnc                   string
	TncUrl                string
	TncContentFontColor   string
	TncHyperlinkFontColor string
}

type VpaMigrationScreenParams struct {
	// success screen config for vpa migration from @fbl to @fifederal
	// figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=6377%3A67415&mode=design&t=cmQL3Z4nhYh5IfO0-1
	VpaMigrationSuccessScreenParams *VpaMigrationSuccessScreenParams
	// introduction screen for vpa migration of user
	VpaMigrationIntroScreenParams *VpaMigrationIntroScreenParams
	VpaMigrationFailureErrorView  *VpaMigrationErrorView
	VpaMigrationPendingErrorView  *VpaMigrationErrorView
	OldVpaHandle                  string
	NewVpaHandle                  string
}

type VpaMigrationErrorView struct {
	Title    string
	Subtitle string
	Ctas     []*CtaForAction
}

type TimelinePaymentPrompts struct {
	Title                 *TextObject
	Description           *TextObject
	PaymentDetailMetaData *TextObject
}

type EnableUpiLiteOptionParams struct {
	UpiLiteIcon      string
	ProceedArrowIcon string
	Title            *TextObject
	Subtitle         *TextObject
	NewTag           *TextObject
	TryIt            *TextObject
}

type UpiLiteAddMoneyScreenParams struct {
	UpiLiteAddMoneyInfo *UpiLiteAddMoneyInfo
	FooterInfo          *TextObject
	BalanceCardInfo     *UpiLiteBalanceCardInfo
	DeleteUpiLiteAction *UpiLiteAction
	ScreenBgColour      string
}

type UpiLiteAddMoneyInfo struct {
	UpiLiteAccountActiveTitle      *TextObject
	UpiLiteAccountActiveSubtitle   *TextObject
	UpiLiteAccountInactiveTitle    *TextObject
	UpiLiteAccountInactiveSubtitle *TextObject
	AddMoneyCardBgColour           string
	Cta                            *CtaForAction
	EnterAmountCardInfo            *UpiLiteEnterAmountCardInfo
	UpiLiteTopUpAmountOptions      []*UpiLiteTopupAmountOption
	UpiLiteMaxAllowedMoneyOption   *UpiLiteTopupAmountOption
}

type UpiLiteTopupAmountOption struct {
	AmountInfo          *AmountInfo
	DefaultBgColour     string
	ErrorBgColour       string
	DefaultStrokeColour string
	ErrorStrokeColour   string
}

type UpiLiteEnterAmountCardInfo struct {
	EnteredAmountInfo   *AmountInfo
	DefaultBgColour     string
	ErrorBgColour       string
	DefaultStrokeColour string
	ErrorStrokeColour   string
	ErrorText           *TextObject
}

type AmountInfo struct {
	Amount      *types.Money
	Currency    *TextObject
	AmountStyle *TextObject
}

type UpiLiteAction struct {
	ActionName  string
	Icon        string
	Title       *TextObject
	Subtitle    *TextObject
	Description []*TextObject
	Ctas        []*CtaForAction
}

type UpiLiteBalanceCardInfo struct {
	Title      *TextObject
	BgColour   string
	AmountInfo *AmountInfo
}

type IconTextParams struct {
	LeftIcon            *ImageProperties     `dynamic:"true"`
	RightIcon           *ImageProperties     `dynamic:"true"`
	Text                string               `dynamic:"true"`
	Color               string               `dynamic:"true"`
	Style               string               `dynamic:"true"`
	BgColor             string               `dynamic:"true"`
	LeftImgTxtPadding   int32                `dynamic:"true"`
	RightImgTxtPadding  int32                `dynamic:"true"`
	ContainerProperties *ContainerProperties `dynamic:"true"`
}

type ContainerProperties struct {
	Padding      *AlignmentValues `dynamic:"true"`
	CornerRadius int32            `dynamic:"true"`
	BgColor      string           `dynamic:"true"`
}
type ImageProperties struct {
	Url     string           `dynamic:"true"`
	Height  int32            `dynamic:"true"`
	Width   int32            `dynamic:"true"`
	Padding *AlignmentValues `dynamic:"true"`
}

type TextProperties struct {
	Style  string
	Size   string
	Family string
	Color  string
}

type TxnDetails struct {
	Details *IconTextParams
	Amount  []*TextProperties
}
type PostPaymentScreenParams struct {
	HeaderImage           *ImageProperties
	HeaderRightCtaParams  *IconTextParams
	SuccessfulTxnDetails  *TxnDetails
	FailedTxnDetails      *TxnDetails
	PendingTxnDetails     *TxnDetails
	PaymentSuccessDetails *PaymentStatusDetails
	PaymentFailedDetails  *PaymentStatusDetails
	PaymentPendingDetails *PaymentStatusDetails
	TxnFailedErrorItem    *InfoItem
	TxnPendingErrorItem   *InfoItem
	Ctas                  []*IconTextParams
}

type InfoItem struct {
	Icon  string
	Title *IconTextParams
	Desc  *IconTextParams
	CTA   *IconTextParams
}

type PaymentStatusDetails struct {
	Icon                *ImageProperties
	PaymentStatusLottie *VisualElementLottie
	Title               *IconTextParams
	StatusTitle         *IconTextParams
	BgColor             string
}

// SharePostPaymentScreenParams have the config related to the share post payment screen
type SharePostPaymentScreenParams struct {
	HeaderIconUrl           string                                                    `dynamic:"true"`
	SuccessParams           *SharePostPaymentScreenTxnStatusParams                    `dynamic:"true"`
	FailedParams            *SharePostPaymentScreenTxnStatusParams                    `dynamic:"true"`
	PendingParams           *SharePostPaymentScreenTxnStatusParams                    `dynamic:"true"`
	TxnDetailsSectionParams map[string]*SharePostPaymentScreenTxnSectionDetailsParams `dynamic:"true"`
	FooterIconUrl           string                                                    `dynamic:"true"`
}

// SharePostPaymentScreenTxnStatusParams have the config related to the status icon, status title and error info
// these values are based on the payment status
type SharePostPaymentScreenTxnStatusParams struct {
	StatusIconUrl string                                 `dynamic:"true"`
	StatusTitle   string                                 `dynamic:"true"`
	ErrorInfo     *SharePostPaymentScreenErrorInfoParams `dynamic:"true"`
}

// SharePostPaymentScreenErrorInfoParams have the config related to the error view
// we only have this in case of failed or pending payment
type SharePostPaymentScreenErrorInfoParams struct {
	IconUrl     string `dynamic:"true"`
	Title       string `dynamic:"true"`
	Description string `dynamic:"true"`
}

// SharePostPaymentScreenTxnSectionDetailsParams have the config related to the txn details in post payment screen
// added SectionIdentifier so that we can support enriching the txn details based on the section
type SharePostPaymentScreenTxnSectionDetailsParams struct {
	// needed for supporting arrays in dynamic config.
	ArrayElement      *cfg.DynamicArrayElement `dynamic:"true"`
	SectionIdentifier string                   `dynamic:"true"`
	Title             string                   `dynamic:"true"`
}

type PaymentHealthStateInfo struct {
	BankHealthStateInfoMessage *BankHealthStateInfoMessage `dynamic:"true"`
}

type BankHealthStateInfoMessage struct {
	UnhealthyStateInfo *UnhealthyStateInfo `dynamic:"true"`
	WarningStateInfo   *WarningStateInfo   `dynamic:"true"`
}

type UnhealthyStateInfo struct {
	Message string `dynamic:"true"`
	IconUrl string `dynamic:"true"`
}

type WarningStateInfo struct {
	Message string `dynamic:"true"`
	IconUrl string `dynamic:"true"`
}

type AllTransactionsShortDesc struct {
	FailedShortDesc   string
	PendingShortDesc  string
	ReversedShortDesc string
}

type CreditCard struct {
	AppVersionSupport            *CreditCardAppVersionSupport `dynamic:"true"`
	OnboardingRetryAttemptCutoff int32                        `dynamic:"true"`
	EnableCCAllTxnPagination     bool                         `dynamic:"true"`
	// Time after which payment successful banner has to be not shown.
	PaymentSuccessBannerTimeInMinutes int32 `dynamic:"true"`
	// flag to show credit card tab as default when hitting the top right card image in the home
	// screen
	ShowCreditCardTabByDefaultFromCardTab bool                                       `dynamic:"true"`
	WorkflowConstraints                   map[string]*releaseConfig.ConstraintConfig `dynamic:"true"`
	// these are the configurations the new CCIntro Screen Horizontal layout
	CCIntroScreenOptions *CCIntroScreenOptions `dynamic:"true"`
	// these are new configurations for the new experimentation that can be used by quests
	AmplifiIntroScreenOptionsV1 *CCIntroScreenOptions `dynamic:"true"`
	AmplifiIntroScreenOptionsV2 *CCIntroScreenOptions `dynamic:"true"`
	// Amplifi Template with vertical flag enabled as true configuration
	AmplifiIntroScreenOptionsV3 *CCIntroScreenOptions `dynamic:"true"`
	// this is the new amplifi cvp cofiguration under the EnableNewCvpForUnsecuredCreditCard flag
	CCIntroScreenOptionsV2                          *CCIntroScreenOptions         `dynamic:"true"`
	SecuredCCIntroScreenOptions                     *CCIntroScreenOptions         `dynamic:"true"`
	MassUnsecuredIntroScreenOptions                 *CCIntroScreenOptions         `dynamic:"true"`
	EnableMultiCardPlatformSupport                  bool                          `dynamic:"true"`
	FiLiteOnboardingHomeScreenRedirectAttemptCutoff int32                         `dynamic:"true"`
	FiLiteOnboardingBottomTextDisplayAttemptCutoff  int32                         `dynamic:"true"`
	EnableVLForIntroScreenByCardProgramType         *EnableFlagForCardProgramType `dynamic:"true"`
	EnableNewCvpForUnsecuredCreditCard              bool                          `dynamic:"true"`
	SpendsThresholdForCcLoungeAccess                int32                         `dynamic:"true"`
	IsCcChoicesComponentListViewEnabled             bool                          `dynamic:"true"`
	AllEligibleCcScreenConfig                       *AllEligibleCcScreenConfig    `dynamic:"true"`
	CcNetworkSelectionScreenVersionCheck            *cfg.PlatformVersionCheck     `dynamic:"true"`
	FiLiteBottomCtaConfigs                          *FiLiteBottomCtaConfigs       `dynamic:"true"`
	EnableCardTabsScreen                            *cfg.PlatformVersionCheck     `dynamic:"true"`
	ChoicesListComponentImageConfigs                map[string]*ImageConfigs
	FiLiteChoicesListComponentImageConfigs          map[string]*ImageConfigs
	SimplifiOnboardingQuestConfig                   *SimplifiOnboardingQuestConfig `dynamic:"true" ,quest:"component,area:CreditCard"`
	EnableFeedbackFormsForCCScreens                 bool                           `dynamic:"true"`
	EnableDashboardSegmentationCarousels            bool
	EnableRenewalFeeWaiverCarousels                 bool `dynamic:"true"`
	// This will contain the mapping from segment ID to CC dashboard carousel. It will contain display information and redirection actions
	SegmentIdToCarouselObjectMap        map[string]*CCCarouselObject
	UnsecuredCCRewardsStateSectionMap   map[string]*UnsecuredCCRewards `dynamic:"true"`
	BillEraserOfferIDForOffersCatalogue string                         `dynamic:"true"`
	ShowAmplifiZeroForexBanner          bool                           `dynamic:"true"`
	CCIntroScreenV2Config               *CCIntroScreenV2Config         `dynamic:"true"`
}

type UnsecuredCCRewards struct {
	RewardsAndOffers      []*RewardsAndOffers
	RewardCoinSummaryData *RewardCoinSummaryData `dynamic:"true"`
	BoosterRewardsData    *BoosterRewardsData    `dynamic:"true"`
}
type RewardsAndOffers struct {
	ImageUrl string
	Title    string
}
type RewardCoinSummaryData struct {
	Title string `dynamic:"true"`
}
type BoosterRewardsData struct {
	Heading        string                    `dynamic:"true"`
	RewardInfoText string                    `dynamic:"true"`
	FooterData     *BoosterRewardsFooterData `dynamic:"true"`
}
type BoosterRewardsFooterData struct {
	BrandsStackImageUrl string `dynamic:"true"`
	CtaTitle            string `dynamic:"true"`
}

type SimplifiOnboardingQuestConfig struct {
	EnableEditDeposit         bool `dynamic:"true" quest:"variable"`
	DisableConsentBottomSheet bool `dynamic:"true" quest:"variable"`
}

type CCCarouselObject struct {
	// Timestamps between which the segment ID to CC dashboard carousel mapping will be valid, else invalid
	// If these are not set, then the condition check is bypassed as every banner might not be limited to certain timestamps
	StartTimestamp  time.Time
	ExpiryTimeStamp time.Time

	InfoIcon                        string
	InfoTitle                       string
	InfoDescription                 string
	InfoCopyAllowed                 bool
	InfoToolTipIconUrl              string
	InfoToolTipInfoBlockTitle       string
	InfoToolTipInfoBlockDescription string

	CtaType            int32
	CtaText            string
	DeeplinkScreenName int32
}

type FiLiteBottomCtaConfigs struct {
	EnableFiLiteBottomCtaVersionCheckFlag *cfg.PlatformVersionCheck `dynamic:"true"`
	IsCcChoicesComponentListViewEnabled   bool                      `dynamic:"true"`
}

type AllEligibleCcScreenConfig struct {
	CardComponentTemplateVersion          int32 `dynamic:"true"`
	CardComponentTemplateVersionConfigMap map[string]map[string]*ImageConfigs
}

type ImageConfigs struct {
	ImageUrl string `dynamic:"true"`
	Height   int32  `dynamic:"true"`
	Width    int32  `dynamic:"true"`
}

// EnableFlagForCardProgramType is a struct defining the configuration for enabling flags for card program types.
type EnableFlagForCardProgramType struct {
	CardProgramTypes []string           // CardProgramTypes is a slice containing the types of card programs.
	FeatureConfig    *app.FeatureConfig `dynamic:"true"` // FeatureConfig is a pointer to app.FeatureConfig struct
}

type CreditCardAppVersionSupport struct {
	MinIosVersionForCreditCard            uint32 `dynamic:"true"`
	MinAndroidVersionForCreditCard        uint32 `dynamic:"true"`
	MinIosVersionForCreditCardIntroV2     uint32 `dynamic:"true"`
	MinAndroidVersionForCreditCardIntroV2 uint32 `dynamic:"true"`
}

type AddFundsV2Params struct {
	ReleaseParams       *MinVersionConstraints `dynamic:"true"`
	IsIosUrnFlowEnabled bool                   `dynamic:"true"`
	// Map containing the urn prefix and some additional details for every UPI app
	UpiAppUrnPrefixMap  map[string]*UpiAppUrnPrefixMap
	WhitelistedActorIds []string
	SkipAddFundsV2      bool `dynamic:"true"`
	// Details related to onboarding add funds v2 screen
	OnboardingAddFundsV2ScreenDetails *OnboardingAddFundsV2ScreenDetails `dynamic:"true"`
}

type OnboardingAddFundsV2ScreenDetails struct {
	MinAmount                                  int64           `dynamic:"true"`
	MaxAmount                                  int64           `dynamic:"true"`
	MinKYCMaxAmount                            int64           `dynamic:"true"`
	DefaultAmount                              int64           `dynamic:"true"`
	SuggestedAmounts                           []int64         `dynamic:"true"`
	SalaryB2BSignupUrl                         string          `dynamic:"true"`
	AffluenceClassesEligibleForUpdatedBenefits map[string]bool `dynamic:"true"`
	// Map storing all flows and the related page infos
	PageInfoForFlowsMap                map[string]*PageInfoForFlow `dynamic:"true"`
	ShowManualAccountBalanceRefreshCta bool                        `dynamic:"true"`
}

type PageInfoForFlow struct {
	// flag to determine whether the flow is enabled or not
	IsEnabled bool `dynamic:"true"`
	// amount below beyond which user will be nudged to add higher funds
	NudgeForHigherAmountAdditionThreshold                int64
	NudgeForHigherAmountAdditionBottomSheetTitle         string
	NudgeForHigherAmountAdditionBottomSheetTitleAffluent string
	NudgeForHigherAmountAdditionBottomSheetSubtitle      string
	BenefitInfoPopUpDetails                              *OnbAddFundsV2BenefitInfoPopUpDetails
	FaqDetails                                           []*OnbAddFundsV2FaqDetails
	RangeBasedBenefits                                   map[string]*OnbAddFundsV2RangeBasedBenefit `dynamic:"true"`
}

type OnbAddFundsV2FaqDetails struct {
	ImageUrl    string
	Title       string
	Description string
}

type OnbAddFundsV2BenefitInfoPopUpDetails struct {
	ImageUrl    string
	Title       string
	Description string
}

type OnbAddFundsV2RangeBasedBenefit struct {
	MinAmount int64 `dynamic:"true"`
	MaxAmount int64 `dynamic:"true"`
	Benefits  []*OnboardingAddFundsV2BenefitMeta
}

type OnboardingAddFundsV2BenefitMeta struct {
	MinAmount                 int64
	LeftIcon                  string
	LeftIconDisabled          string
	Title                     string
	TitleDisabled             string
	RightIconEnabled          string
	RightIconDisabled         string
	RightTextEnabled          string
	RightTextDisabled         string
	TitleAffluentUser         string
	TitleDisabledAffluentUser string
}

type UpiAppUrnPrefixMap struct {
	DisplayName string
	UrnPrefix   string
	ImageUrl    string
}

type TxnCatParams struct {
	// flag to determine the default state of check box for future txn cat on recat txn screen
	IsFutureTxnCatSetByDefault bool
}

type SimilarActivityParams struct {
	// number of max similar activity records fetched from BE at a time to be shown to client in paginated view
	PageSize int32 `dynamic:"true"`
	// max number of last similar activity user can recat using similar recat flow
	MaxSimilarActivities int32 `dynamic:"true"`
	// Duration in month to consider for similar activities (activities before this will not be considered in current similar activity recat)
	// e.g if duration is 12 and activity's TS is 3 oct 2022, then we consider all similar txns till 3 oct 2021 only
	DurationInMonths int32 `dynamic:"true"`
}

type CTA struct {
	DisplayString string `dynamic:"true"`
	IconUrl       string `dynamic:"true"`
	BgColor       string `dynamic:"true"`
	TextColor     string `dynamic:"true"`
}

// AttributionLinkParseConfig stores the config to identify an acquisition source.
type AttributionLinkParseConfig struct {
	AcquisitionSource    string                            `dynamic:"true"`
	SourceIdentification *AcquisitionSourceIdentifications `dynamic:"true"`

	IsEnabled bool `dynamic:"true"`
}

// AcquisitionSourceIdentifications config for defining ways of identification of acq source.
// Note: This will be deprecated in favour of config to identify L2, L3, L3... of acquisition.
type AcquisitionSourceIdentifications struct {
	CampaignName string `dynamic:"true"`

	/*
		standard params of AF
	*/
	DeepLinkValue string `dynamic:"true"`
	DeepLinkSub1  string `dynamic:"true"`
	DeepLinkSub2  string `dynamic:"true"`
	/*
		Note: more fields can be added which can be considered for AND condition to identify a
		source successfully.
	*/
}

type Application struct {
	Environment                string
	Name                       string
	EnableDeviceIntegrityCheck bool
	// If enabled, requests from users for whom actor is created can bypass device integrity checks
	// i.e., if an actor is found on phone number or email or device ID, device integrity is not mandated
	// NOTE: Not to be enabled in Prod
	SkipDeviceIntegrityCheckForExistingActors bool
	EnableLocationInterceptor                 bool

	// MaxGRPCTimeout sets the maximum allowed gRPC timeout for all the requests to the Server.
	// The timeout ensures there is no unbounded requests to the servers. Hence, releasing the resources
	// after a particular duration.
	// Ref- https://grpc.io/blog/deadlines/
	MaxGRPCTimeout time.Duration
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Logging struct {
	EnablePartnerLog bool
	EnableSecureLog  bool
	PartnerLogPath   string
	SecureLogPath    string // file path where secure logs will be stored
	MaxSizeInMBs     int    // megabytes
	MaxBackups       int    // There will be MaxBackups + 1 total files
}

type LegalDocuments struct {
	FiTncUrl                  string
	FederalBankTncUrl         string
	FiPrivacyPolicyUrl        string
	FiWealthTncUrl            string
	FederalLoansTncUrl        string
	LiquiloansLoansTncUrl     string
	USStocksUseAndRiskUrl     string
	USStocksTncUrl            string
	USStocksPrivacyNoticeUrl  string
	USStocksPFOFUrl           string
	USStocksMarginDiscStmtUrl string
	USStocksExtHrsRiskUrl     string
	USStocksBCPSummaryUrl     string
	USStocksFormCRSUrl        string
	OpenSourceLicenses        *OpenSourceLicenses
	AaOnemoneyTncUrl          string
	AaFinvuTncUrl             string
	SalaryAccountTncUrl       string
}

type OpenSourceLicenses struct {
	Firebase      string
	Cronet        string
	ChromeWebView string
}

type AmountColourMap struct {
	DebitColour    string
	CreditColour   string
	SavingsColour  string
	DefaultColour  string
	PendingColour  string
	FailedColour   string
	ReversedColour string
}

type TimelineEventDescription struct {
	PaymentReceived                               string
	PaymentReceivedWithoutRemarks                 string
	PaymentSent                                   string
	PaymentSentWithoutRemarks                     string
	PaymentFailedSender                           string
	PaymentFailedSenderWithoutRemarks             string
	PaymentFailedReceiver                         string
	PaymentFailedReceiverWithoutRemarks           string
	PaymentPendingSender                          string
	PaymentPendingSenderWithoutRemarks            string
	PaymentPendingReceiver                        string
	PaymentPendingReceiverWithoutRemarks          string
	CollectRequestSending                         string
	CollectRequestSendingWithoutRemarks           string
	CollectRequestFailed                          string
	CollectRequestFailedWithoutRemarks            string
	CollectRequestSentReceiver                    string
	CollectRequestSentReceiverWithoutRemarks      string
	CollectRequestSentSender                      string
	CollectRequestSentSenderWithoutRemarks        string
	CollectRequestDeclinedReceiver                string
	CollectRequestDeclinedReceiverWithoutRemarks  string
	CollectRequestDeclinedSender                  string
	CollectRequestDeclinedSenderWithoutRemarks    string
	CollectRequestCancelledReceiver               string
	CollectRequestCancelledReceiverWithoutRemarks string
	CollectRequestCancelledSender                 string
	CollectRequestCancelledSenderWithoutRemarks   string
	CollectRequestExpiredSender                   string
	CollectRequestExpiredSenderWithoutRemarks     string
	CollectRequestExpiredReceiver                 string
	CollectRequestExpiredReceiverWithoutRemarks   string
}

type CCTimelineEventDescription struct {
	PaymentReceived string
	PaymentSent     string
	PaymentFailed   string
}

type TimelineEventBottomDescription struct {
	CollectRequestExpiryTime       string
	CollectRequestExpiryTimeLayout string
}

type TimelineActionDisplayName struct {
	Pay        string
	Cancel     string
	Decline    string
	Repeat     string
	Retry      string
	ReportSpam string
	Unblock    string
	Accept     string
}

type TimelineCards struct {
	BlockedActor *TimelineCard
}

type TimelineCard struct {
	Title       string
	Description string
	ImageUrl    string
}

type TimelineEventUIParams struct {
	FailureBackgroundColour       string
	CreditSuccessBackgroundColour string
	DebitSuccessBackgroundColour  string
	DefaultTextColour             string
	FailureTextColour             string
	CreditSuccessIconUrl          string
	DebitSuccessIconUrl           string
	FailureIconUrl                string
	DebitSuccessStrokeColour      string
	CreditSuccessStrokeColour     string
	FailureStrokeColour           string
}

type BankIcon struct {
	FederalBankUrl                      string
	FiBankUrl                           string
	ActorActivityFiBankShortDescIconUrl string
	DefaultBankLogoUrl                  string
	FederalFiIconUrl                    string
}

// Deprecated: in favour of VendorToAccountTypeToNameMap
type VendorToBankNameMap map[string]string

type VendorToAccountTypeToNameMap map[string]map[string]string

type DepositIcon struct {
	FDUrl string
	SDUrl string
}

type AppVersionRange struct {
	MinVersion int
	MaxVersion int
}

type P2PIcons struct {
	LiquiloansIcon string
}

type P2PActors struct {
	LiquiloansActorId string
}

type BottomSheets struct {
	RaisingDispute         *BottomSheet
	RaiseDisputeProhibited *BottomSheet
}

type BottomSheet struct {
	Title string
	Text  string
}

type Flags struct {
	// Flag to control whether the new liveness flow should be used, which includes the CameraX library migration and the removal of hardcoded polling logic.
	// NextAction is mandatory in CheckLivenessScreenOptions in the new flow
	// Applicable only for android
	UseNewLivenessFlowForAFU *app.FeatureConfig `dynamic:"true"`
	// Enabling this flag will return a custom status code from FAT RPC call, allowing client to retry FAT with new OIDC token
	ReturnRetryStatusForOIDCExpiry *app.FeatureConfig `dynamic:"true"`
	// to skip add money step in onboarding
	SkipAddMoney bool
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
	// Flag for enabling mother father kyc name check in UpdateMotherFatherName rpc
	EnableMotherFatherKycNameCheck bool `dynamic:"true"`
	// A flag to enable/disable delivery tracking details dynamic tile.
	// We will show users card delivery details such as AWB, Carrier, Delivery state information via a dynamic tile if the flag is enabled
	// If disabled we will always show an informative tile regarding the expected delivery time.
	EnableCardTracking bool
	// A flag to enable/disable block card and request new card flows.
	// This is a short term hack and we will stop using this flag once we get block card api from vendor
	EnableCardBlock bool
	// A flag to enable/disable QR code scan i.e. physical card activation.
	// This is a short term hack and we will stop using this flag once we start delivering physical cards to the user.
	// If the flag is true we will get QR code scan tile on UI.
	EnableCardQRCodeScan bool
	// To enable VKYC only for internal users
	// Deprecated: used only for launch
	EnableVKYCOnlyForInternalUsers bool
	// flag to enable vkyc flow
	// Deprecated: used only for launch
	EnableVkycScheduleFlow bool
	// Flag to enable tile under debit card to inform first-time users that the debit card is ready for
	// online use + mention of physical card when their online txns are enabled.
	EnableCardOnlineTxnEnabledTile bool
	// Flag to enable/disable card offers
	EnableCardOffers bool
	// struct to enable bharatQr to user group.
	EnableBharatQrToUserGroup *EnableBharatQrToUserGroup
	// Use search aggregation api to reduce multiple api calls to rms service which finds aggregation in-memory
	UseSearchAggregationAPIForFIT bool
	// To skip check if an user has access revoked
	SkipUserRevokeStateCheck bool
	// feature flag to switch b/w internal collect and external collect
	BypassP2PCollectShortCircuit bool
	// does client need to pass device integrity check to fetch apikeys
	VerifyDeviceIntegrityInFetchApiKey bool
	// New tile for activation will be shown and the dynamic tile for card activation
	// will be removed and addition of new states in card tracking details.
	EnableCardTrackingAndActivationChanges *cfg.PlatformVersionCheck
	// flag to decide whether to consider the evaluated/identified source & intent ready for further propagation,
	// for e.g. BE usage, storage etc
	EnableAcqSourceAndIntentPropagation bool `dynamic:"true"`
	// Flag for UPI pin set on max wrong retries
	UpiMaxRetriesPinSet *cfg.PlatformVersionCheck
	// If enabled, the balance summary has 2 sections(Spent and Saved) and amount invested is part of Saved bucket.
	// If disabled, the amount invested is part of Spent bucket. This is default behavior.
	// Deprecated: DO NOT USE
	MoveInvestmentAmountToSavedBucket bool
	// feature flag for manual review of liveness in AFU
	EnableLivenessManualReviewInAfu bool
	// feature flag to check for access revoke
	EnableCheckForAccessRevoke bool
	// feature flag to allow only eligible users to access analyser feature
	AnalyserFeatureFlag *cfg.FeatureReleaseConfig
	// EnableRecentUserActivities is a temporary flag added to disable recent activities by default and enable based on this flag.
	// This is added to stop bad queries on order tabele in CRDB database.
	EnableRecentUserActivities bool `dynamic:"true"`

	// flag to enable/disable overriding of transaction ref id for bharat qr while parsing
	// since we parse ref id from two tags in qr, this flag decides whether we want to override the previous values
	// from the other tag
	OverrideTransactionRefIdForBharatQr bool `dynamic:"true"`

	AllTransactionWithCCFeatureFlag *cfg.FeatureReleaseConfig `dynamic:"true"`

	// flag to enable timeline events from multiple sources
	TimelineEventsFromMultipleSourcesFlag *cfg.FeatureReleaseConfig `dynamic:"true"`

	// flag to enable physical card dispatch with charges & tiering changes
	EnablePhysicalCardChargesFlowWithTiering *cfg.PlatformVersionCheck `dynamic:"true"`

	// flag to enable physical card dispatch with charges (without tiering changes)
	EnablePhysicalCardChargesFlow *cfg.PlatformVersionCheck `dynamic:"true"`

	// Feature flag to enable cc all transactions
	EnableCCAllTransactionFeatureFlag *cfg.FeatureReleaseConfig `dynamic:"true"`

	// Feature flag to enable cc recent activity
	EnableCCRecentActivityFeatureFlag *cfg.FeatureReleaseConfig `dynamic:"true"`

	// Feature flag to enable cc timeline events
	EnableCCTimelineEventFeatureFlag *cfg.FeatureReleaseConfig `dynamic:"true"`

	// flag to enable EMI conversion of CC transactions
	EnableCcEmiConversionFeatureFlag *cfg.PlatformVersionCheck `dynamic:"true"`

	// flag to enable custom reminders for credit card
	EnableCcCustomRemindersFeatureFlag *cfg.PlatformVersionCheck `dynamic:"true"`

	// flag to enable credit card details and benefits section
	EnableCcDetailsAndBenefitsFeatureFlag *cfg.PlatformVersionCheck `dynamic:"true"`

	// flag to enable new cvp credit card details and benefits section for unsecured card
	EnableUnsecuredNewCvpCcDetailsAndBenefitsFeatureFlag *cfg.PlatformVersionCheck `dynamic:"true"`

	// flag to enable credit card freeze/unfreeze icon in home card
	EnableCcFreezeUnfreezeHomeCardIconFlag *cfg.PlatformVersionCheck `dynamic:"true"`

	USStocksFeatureFlag *cfg.FeatureReleaseConfig

	// Flag to enable red listed apps check
	EnableRedListedAppsCheck bool `dynamic:"true"`

	// Enables rate limiting interceptor
	EnableRateLimitingInterceptor bool `dynamic:"true"`

	// Enable AFU before device registration
	EnableAFUBeforeDevReg bool `dynamic:"true"`

	// Enable Auto Invest component on invest landing
	EnableAutoInvestComponentOnInvestLanding *app.FeatureConfig `dynamic:"true"`

	// Enables US stocks instrument card and navigation toggle
	EnableUSStocksInstrumentCardFlag bool `dynamic:"true"`

	// Enables MF instrument card and navigation toggle
	EnableMFInstrumentCardFlag bool `dynamic:"true"`

	// Enables SD instrument card and navigation toggle
	EnableSDInstrumentCardFlag bool `dynamic:"true"`

	// Enables FD instrument card and navigation toggle
	EnableFDInstrumentCardFlag bool `dynamic:"true"`

	// Enables Jump instrument card and navigation toggle
	EnableJumpInstrumentCardFlag bool `dynamic:"true"`

	// Enable Onboarding Details Min cache
	EnableOnboardingDetailsMin bool `dynamic:"true"`

	// Enables Quick links card in invest landing
	EnableInvestmentLandingQuickLinksComponent *app.FeatureConfig `dynamic:"true"`

	InvestmentLandingQuickLinksComponentAllowedUserGroups []commontypes.UserGroup

	// Enables cc rewards on dashboard
	EnableCcRewardsOnDashboardFeatureFlag *cfg.PlatformVersionCheck `dynamic:"true"`

	// Enable AFU for cases with email phone actorIds mismatch
	EnableConflictingAFU bool `dynamic:"true"`

	// flag to enable CC lounge access
	EnableCcLoungeAccessFeatureFlag *cfg.PlatformVersionCheck `dynamic:"true"`

	// flag to enable CC copy card details
	EnableCCCopyCardDetails *cfg.PlatformVersionCheck `dynamic:"true"`

	// Enable AFU before customer creation
	EnableAFUBeforeCustomerCreation bool `dynamic:"true"`

	// flag to enable CC repayment screen top banner
	EnableCreditCardRepaymentScreenTopBanner bool `dynamic:"true"`

	// flag to enable device deactivated handling
	EnableDevDeactivatedHandling bool `dynamic:"true"`

	// flag to enable PartnersComponent on invest landing
	EnablePartnersComponentInvestLanding *cfg.PlatformVersionCheck `dynamic:"true"`
	// Flag to denote whether the AppsFlyer v2 client key should be used or not
	// It will be used for AppsFlyer dev key rotation
	UseAppsFlyerClientKeyV2 bool `dynamic:"true"`
	// Flag to allow re-onboarding for users whose accounts are closed
	AllowClosedAccountReOnboarding bool `dynamic:"true"`
	// Flag to control the showing v2 billing info on cc dashboard to the user
	// based on client version
	EnableCCBillDashboardV2FeatureFlag *cfg.PlatformVersionCheck `dynamic:"true"`

	EnableSimID *app.FeatureConfig `dynamic:"true"`

	// Flag to control the usage of IsPhonePermissionGranted flag in auth rpc
	// if this flag is true and IsPhonePermissionGranted is false
	// then BE will return PHONE_PERMISSION_REQUIRED error code.
	EnablePhonePermissionCheck *app.FeatureConfig `dynamic:"true"`

	// Flag to enable/disable secured credit cards reward dashboard
	EnableSecuredCardsRewardsDashboard *cfg.PlatformVersionCheck `dynamic:"true"`

	// Flag to block users with fi lite intent if their risk screening failed
	BlockRiskScreeningFailedUsersWithFiLiteIntent bool `dynamic:"true"`

	EnableLoungeAccessV2FeatureFlag *cfg.PlatformVersionCheck `dynamic:"true"`

	// Flag to enable/disable cc generic dashboard
	EnableGenericRewardsDashboard *cfg.PlatformVersionCheck `dynamic:"true"`

	// flag for pop up to link other bank accounts on pay landing screen.
	LinkOtherBankAccountsPopUpFlag bool `dynamic:"true"`

	EnableInhouseBiometricLiveness *app.FeatureConfig `dynamic:"true"`

	EnableGetNextOnboardingActionFiLiteRedirection bool `dynamic:"true"`

	AllowQatarForSignup bool `dynamic:"true"`

	EnableLAMFPrePayV2 *app.FeatureConfig `dynamic:"true"`

	// flag to enable/disable screen options V2 for reOOBE Cooldown
	ReOOBECooldownBottomSheetScreenOptionsV2 *app.FeatureConfig `dynamic:"true"`

	// flag to enable/disable NR onboarding
	BlockNrOnboarding bool `dynamic:"true"`

	// flag to enable/disable recording consent in LoginWithOauth RPC.
	// These consent are collected from user on phone verification screen
	AllowConsentCollectionOnLogin *app.FeatureConfig `dynamic:"true"`

	// flag use to hide consent description and show NR consent on phone verification screen
	ShowNrConsentOnPhoneScreen *app.FeatureConfig `dynamic:"true"`

	// flag use to decide version from which cards design fixit UI is enabled
	EnableCardDesignEnhancement *app.FeatureConfig `dynamic:"true"`

	ReconVendorIds bool `dynamic:"true"`
}

type RewardsFrontendMeta struct {
	AppFetchRewardsPageSize                     uint32
	AppFetchOffersPageSize                      uint32
	AppFetchRedeemedOffersPageSize              uint32
	AppDisplayDateTimeFormat                    string
	MinTimeDifferenceToShowTimeBasedLockDetails time.Duration
	RewardPostClaimCtaToConfigMap               map[string]*RewardsPostClaimCtaConfig
	RewardStatusToBgColorMap                    map[string]string
	RewardStatusToIsTileClickableMap            map[string]bool
	RewardStatusToStatusMsgTemplate             map[string]string
	RewardStatusToStatusDescTemplate            map[string]string
	RewardTypeAndStatusToStatusDescTemplate     map[string]map[string]string
	RewardOfferTileTagTypeToTagBgColor          map[string]string
	RewardOfferTileTagTypeToTagText             map[string]string
	LockedRewardInoperableInfoForMinKyc         *RewardsBottomSheetInfo `dynamic:"true"`
	OfferCatalogCommonTnc                       string
	MaxUnopenedRewardsToShowOnMyRewardsScreen   uint32
	MaxOpenedRewardsToShowOnMyRewardsScreen     uint32
	// this flag is added as there was a bug in one of the android app
	// build that it couldn't display multi choice rewards correctly. So
	// this flag is checked and used to display only single reward choice
	// on that android app version.
	AndroidAppVersionsNotSupportingMultiChoiceRewards []int
	// this flag is added as after one of the android app version, we made
	// reward tiles clickable irrespective of the reward status.
	// But all apps before this version should have clickable handling as before.
	MinAndroidAppVersionWithAllRewardTileStatesClickable int
	// android app >= below app version supports claiming gift hamper reward.
	MinAndroidAppVersionSupportingGiftHamperReward int
	// ios app >= below app version supports claiming gift hamper reward.
	MinIosAppVersionSupportingGiftHamperReward int
	// android app >= below app version supports CBR-V2.
	MinAndroidAppVersionSupportingCBRV2 int
	// ios app >= below app version supports CBR-V2.
	MinIosAppVersionSupportingCBRV2 int
	// minimum version of android build which supports salary-exclusive-offers
	MinAndroidAppVersionSupportingSalaryExclusiveOffer int `dynamic:"true"`
	// minimum version of ios build which supports salary-exclusive-offers
	MinIosAppVersionSupportingSalaryExclusiveOffer int `dynamic:"true"`
	// minimum version of android build which supports locking of rewards for min-kyc users
	MinAndroidVersionSupportingLockingOfRewardsForMinKycUsers uint32 `dynamic:"true"`
	// minimum version of ios build which supports locking of rewards for min-kyc users
	MinIosVersionSupportingLockingOfRewardsForMinKycUsers uint32 `dynamic:"true"`
	// minimum version of android build which supports power-up type rewards
	MinAndroidVersionSupportingPowerUpRewards int `dynamic:"true"`
	// minimum version of iOS which supports rendering of dynamic data for redeemed offers
	MinIosVersionSupportingDynamicDataForReedemedOffers uint32 `dynamic:"true"`
	// minimum version of android build which supports CBR EGV redirection to "MyOrders" flow
	MinAndroidVersionSupportingCbrEgvRedirectionFlow int `dynamic:"true"`
	// max number of rewards that can be claimed in bulk by actor
	MaxRewardsToBeClaimedInBulk int `dynamic:"true"`
	// minimum version of android build which supports graceful handling of empty addresses for rewards-claim / offer-redemption
	MinAndroidVersionForEmptyAddressesGracefulHandling uint32 `dynamic:"true"`
	// minimum version of ios build which supports graceful handling of empty addresses for rewards-claim / offer-redemption
	MinIosVersionForEmptyAddressesGracefulHandling uint32 `dynamic:"true"`
	// minimum android version supporting catalog discounts
	MinAndroidVersionSupportingCatalogDiscounts uint32 `dynamic:"true"`
	// minimum ios version supporting catalog discounts
	MinIosVersionSupportingCatalogDiscounts uint32 `dynamic:"true"`
	// minimum android version supporting booster fields
	MinAndroidVersionSupportingBoosterFields uint32 `dynamic:"true"`
	// minimum ios version supporting booster fields
	MinIosVersionSupportingBoosterFields uint32 `dynamic:"true"`
	// config for home component of rewards and offers
	RewardsAndOffersHome *RewardsAndOffersHome `dynamic:"true"`
	// tags display config
	TagsConfig *TagsConfig `dynamic:"true"`
	// android app >= below app version supports bottom sheet error view
	MinAndroidAppVersionSupportingBottomSheetErrorView int `dynamic:"true"`
	// ios app >= below app version supports bottom sheet error view
	MinIosAppVersionSupportingBottomSheetErrorView int `dynamic:"true"`
	// android app >= below app version supports unredeemable offer labels
	MinAndroidAppVersionSupportingUnredeemableOfferLabel uint32 `dynamic:"true"`
	// ios app >= below app version supports unredeemable offer labels
	MinIosAppVersionSupportingUnredeemableOfferLabel uint32 `dynamic:"true"`
	// flags to decide Next-Screen-CTA to be shown after rewards claim flow
	RewardClaimFlowAllowedNextScreenCTAs map[string]bool `dynamic:"true"`
	// unredeemable offers labels config
	UnredeemableOfferLabelConfig map[string]*UnredeemableOfferLabelDisplayDetails `dynamic:"true"`
	// config related to catalog filters
	CatalogFiltersConfig *CatalogFiltersConfig `dynamic:"true"`
	// config related to card catalog
	CardCatalogConfig *CardCatalogConfig `dynamic:"true"`
	// android app >= below app version supports offer for which user/global level inventory is exhausted
	MinAndroidAppVersionSupportingInventoryExhaustedOffer uint32 `dynamic:"true"`
	// IOS app >= below app version supports offer for which user/global level inventory is exhausted
	MinIosAppVersionSupportingInventoryExhaustedOffer uint32 `dynamic:"true"`
	// android app >= below app version supports coming soon offer
	MinAndroidAppVersionSupportingComingSoonOffer uint32 `dynamic:"true"`
	// IOS app >= below app version supports coming soon offer
	MinIosAppVersionSupportingComingSoonOffer uint32 `dynamic:"true"`
	// android app >= below app version supports new card offer catalog
	MinAndroidAppVersionSupportingCardOfferCatalogV2 uint32 `dynamic:"true"`
	// IOS app >= below app version supports new card offer catalog
	MinIosAppVersionSupportingCardOfferCatalogV2 uint32 `dynamic:"true"`
	// android app >= below app version supports new card offer details page
	MinAndroidAppVersionSupportingCardOfferDetailsV2 uint32 `dynamic:"true"`
	// IOS app >= below app version supports new card offer details page
	MinIosAppVersionSupportingCardOfferDetailsV2 uint32 `dynamic:"true"`
	// android app >= below app version supports credit card offers on home card offers tab
	MinAndroidAppVersionSupportingCreditCardOffersOnHome uint32 `dynamic:"true"`
	// IOS app >= below app version supports credit card offers on home card offers tab
	MinIosAppVersionSupportingCreditCardOffersOnHome uint32 `dynamic:"true"`
	// android app >= below app version supports vistara air miles offer type redemption
	MinAndroidAppVersionSupportingVistaraAirMilesOffer uint32 `dynamic:"true"`
	// IOS app >= below app version supports vistara air miles offer type redemption
	MinIosAppVersionSupportingVistaraAirMilesOffer uint32 `dynamic:"true"`
	// android app >= below app version supports thriwe benefits package offer type for redemption
	MinAndroidAppVersionSupportingThriweBenefitsPackageOffers uint32 `dynamic:"true"`
	// ios app >= below app version supports thriwe benefits package offer type for redemption
	MinIosAppVersionSupportingThriweBenefitsPackageOffers uint32 `dynamic:"true"`
	// android app >= below app version supports your rewards card v2 on the offers widget
	MinAndroidAppVersionSupportingYourRewardsCardV2 uint32 `dynamic:"true"`
	// ios app >= below app version supports your rewards card v2 on the offers widget
	MinIosAppVersionSupportingYourRewardsCardV2 uint32 `dynamic:"true"`
	// android app >= below app version supports new reward tile states like DELAYED_UNLOCK_WITH_AUTOCLAIM
	MinAndroidAppVersionSupportingNewRewardTiles uint32 `dynamic:"true"`
	// ios app >= below app version supports new reward tile states like DELAYED_UNLOCK_WITH_AUTOCLAIM
	MinIosAppVersionSupportingYourNewRewardTiles uint32 `dynamic:"true"`
	// android app >= below app version supports offer with offer type CMS_COUPON
	MinAndroidAppVersionSupportingDefaultOfferType uint32 `dynamic:"true"`
	// IOS app >= below app version supports offer with offer type CMS_COUPON
	MinIosAppVersionSupportingDefaultOfferType uint32 `dynamic:"true"`
	// Android version >= this version supports CTA on card offers screen with updated design
	MinAndroidVersionSupportingUpdatedOffersScreenCta uint32 `dynamic:"true"`
	// IOS app >= below app version support MyRewardsV2 screen, so any version before this will be shown old screen
	MinIosVersionSupportingMyRewardsV2Screen uint32 `dynamic:"true"`
	// IOS app >= below app version contains redeemed offers screen pagination fix
	MinIosVersionWithRedeemedOffersScreenPaginationFix uint32 `dynamic:"true"`
	// android app >= below app version supports external vendor offer type
	MinAndroidAppVersionSupportingExternalVendorOffer uint32 `dynamic:"true"`
	// IOS app >= below app version supports external vendor offer type
	MinIosAppVersionSupportingExternalVendorOffer uint32 `dynamic:"true"`
	// android app >= below app version supports fi coins to points offer type
	MinAndroidAppVersionSupportingFiCoinsToPointsOfferType uint32 `dynamic:"true"`
	// IOS app >= below app version supports fi coins to points offer type
	MinIosAppVersionSupportingFiCoinsToPointsOfferType uint32 `dynamic:"true"`
	// Flag to enable web page screen with card details for fi store
	IsWebPageWithCardDetailsScreenEnabled bool `dynamic:"true"`
	// android app >= below app version supports fi store with card details web page
	MinAndroidVersionForWebPageWithCardDetailsScreen uint32 `dynamic:"true"`
	// IOS app >= below app version supports fi store with card details web page
	MinIosVersionForWebPageWithCardDetailsScreen uint32 `dynamic:"true"`
	// Android app >= below app version supports navbar highlight on rewards top bar icon.
	MinAndroidAppVersionToSupportTopNavBarHighlight uint32 `dynamic:"true"`
	// IOS app >= below app version supports navbar highlight on rewards top bar icon.
	MinIOSAppVersionToSupportTopNavBarHighlight uint32 `dynamic:"true"`
	// waysToEarnRewards screen version to be shown on app
	WaysToEarnRewardsScreenVersionToRender uint32 `dynamic:"true"`
	// ways to earn rewards v2 screen feature config
	WaysToEarnRewardsV2ScreenFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for claimed reward screen deeplink support
	ClaimedRewardScreenDeeplinkFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for BE driven reward post claim screen
	BeDrivenRewardPostClaimScreenFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for CTAs carousel support on My rewards screen
	// figma: https://www.figma.com/design/Bc8Y41Lgv4YMmX00n8V9ZU/%F0%9F%8E%81-Rewards-%E2%80%A2-Workfile-2?node-id=20866-16269&t=jfKu1NNuZ9Fq4tj6-1
	MyRewardsScreenCtaCarouselFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for showing offers widget on my rewards page
	// figma:https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=6478-18282&t=XLEAnzvYxELnZLBU-1
	MyRewardsScreenOfferWidgetFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// figma:https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=6478-18282&t=XLEAnzvYxELnZLBU-1
	MyRewardsScreenNewOpenedRewardsWidgetFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for card offers v2 widget on home,salary landing etc
	// v2: https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=4287%3A13360&mode=design&t=axNsfJHNc0Hy2NQM-1
	CardOffersV2WidgetFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// v2: https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=4287%3A13360&mode=design&t=axNsfJHNc0Hy2NQM-1
	CatalogOffersV2WidgetConfig       *CatalogOffersV2WidgetConfig `dynamic:"true"`
	IsDebitCardOfferWidgetHomeEnabled bool                         `dynamic:"true" ,quest:"variable"`
	OffersCatalogPageConfig           *OffersCatalogPageConfig     `dynamic:"true" ,quest:"component"`
	DebitCardRewardsConfig            *DebitCardRewardsConfig      `dynamic:"true"`
	// feature config for filters on collected offers page
	// https://www.figma.com/design/Bc8Y41Lgv4YMmX00n8V9ZU/%F0%9F%8E%81-Rewards-%E2%80%A2-Workfile-2?node-id=21338-21680&t=e0cOEHASze5ksZ5U-1
	CollectedOffersPageFiltersFeatureConfig     *app.FeatureConfig `dynamic:"true"`
	IsAllFiltersCtaEnabledOnCollectedOffersPage bool               `dynamic:"true"`
	// exclude catalog offers having NonCCUsersCatalogOffersExcludedCategoryTags category tags for non cc users from catalog offers page and home offers widget
	// it's a comma separated list of category tags
	NonCCUsersCatalogOffersExcludedCategoryTags string `dynamic:"true" ,quest:"variable"`
	// Earned rewards history screen configuration
	EarnedRewardsHistoryConfig *EarnedRewardsHistoryConfig `dynamic:"true"`
}

type DebitCardRewardsConfig struct {
	InternationalSpendsRewardOfferId string `dynamic:"true"`
}

type OffersCatalogPageConfig struct {
	IsQuestEnabled       bool                  `dynamic:"true" ,quest:"variable"`
	FeatureConfig        *app.FeatureConfig    `dynamic:"true"`
	TopBarDetailsConfig  *TopBarDetailsConfig  `dynamic:"true"`
	SectionsConfig       *SectionsConfig       `dynamic:"true" ,quest:"component"`
	PageUiDetails        *PageUiDetails        `dynamic:"true"`
	OffersOrderingConfig *OffersOrderingConfig `dynamic:"true" ,quest:"component"`
	// ForceUpgradeFeatureConfig config cannot be empty
	ForceUpgradeFeatureConfig *app.FeatureConfig `dynamic:"true"`
}

type TopBarDetailsConfig struct {
	Title            string `dynamic:"true"`
	BackgroundColour string `dynamic:"true"`
}

type SectionsConfig struct {
	MyEarningsSection      *MyEarningsSection      `dynamic:"true"`
	BannersSection         *BannersSection         `dynamic:"true"`
	OfferFiltersSection    *OfferFiltersSection    `dynamic:"true"`
	OffersSection          *OffersSection          `dynamic:"true" ,quest:"component"`
	UnopenedRewardsSection *UnopenedRewardsSection `dynamic:"true"`
}

type MyEarningsSection struct {
	BackgroundColour          string                     `dynamic:"true"`
	RedeemedRewardsPageConfig *RedeemedRewardsPageConfig `dynamic:"true"`
}

type RedeemedRewardsPageConfig struct {
	RewardsSummaryWidget *RewardsSummaryWidget `dynamic:"true"`
}

type RewardsSummaryWidget struct {
	FiCoinsBalanceSummary   *RewardSummaryWidgetRow `dynamic:"true"`
	LifetimeCashbackSummary *RewardSummaryWidgetRow `dynamic:"true"`
}

type BannersSection struct {
	FeatureConfig    *app.FeatureConfig `dynamic:"true"`
	BackgroundColour string             `dynamic:"true"`
}

type OfferFiltersSection struct {
	BackgroundColour string `dynamic:"true"`
}

type OffersSection struct {
	BackgroundColour                            string             `dynamic:"true"`
	IsHorizontalScroll                          bool               `dynamic:"true" ,quest:"variable"`
	LoanDefaultReminderBottomSheetFeatureConfig *app.FeatureConfig `dynamic:"true"`
}

type UnopenedRewardsSection struct {
	BackgroundColour string `dynamic:"true"`
}

type PageUiDetails struct {
	BackgroundColour string `dynamic:"true"`
}

type OffersOrderingConfig struct {
	IsDynamicSortingEnabled                        bool  `dynamic:"true" ,quest:"variable"`
	OfferPotentialAffordabilityMultiplier          uint8 `dynamic:"true" ,quest:"variable"`
	ExchangerOfferPotentialAffordabilityMultiplier uint8 `dynamic:"true" ,quest:"variable"`
}

type CatalogOffersV2WidgetConfig struct {
	FeatureConfig                           *app.FeatureConfig                   `dynamic:"true"`
	ShopOffersTagToCardDisplayDetailsMap    map[string]*CatalogCardDisplayConfig `dynamic:"true"`
	ConvertOffersTagToCardDisplayDetailsMap map[string]*CatalogCardDisplayConfig `dynamic:"true"`
	// TagToCardDisplayDetailsMap is tag to display details map for showing all offers in a single tab
	// ConvertOffersTagToCardDisplayDetailsMap and ShopOffersTagToCardDisplayDetailsMap are used for showing offers in separate shop and convert tabs
	TagToCardDisplayDetailsMap map[string]*CatalogCardDisplayConfig `dynamic:"true"`
}

type CatalogCardDisplayConfig struct {
	Rank   uint32 `dynamic:"true"`
	Title  string `dynamic:"true"`
	ImgUrl string `dynamic:"true"`
}

type RewardsPostClaimCtaConfig struct {
	Title            string
	IconUrl          string
	SecondaryIconUrl string
	BgColor          string
	FontColor        string
}

type RewardsAndOffersHome struct {
	Title           string           `dynamic:"true"`
	YourRewardsTile *YourRewardsTile `dynamic:"true"`
	WaysToEarnTile  *WaysToEarnTile  `dynamic:"true"`
	OfferTile       *OfferTile       `dynamic:"true"`
	OffersOrder     []string         `dynamic:"true"`
}

type YourRewardsTile struct {
	Title                string          `dynamic:"true"`
	TitleColor           string          `dynamic:"true"`
	ImageUrl             string          `dynamic:"true"`
	FiCoinSymbolImageUrl string          `dynamic:"true"`
	BgColor              *RadialGradient `dynamic:"true"`
	Shadow               *BlockShadow    `dynamic:"true"`
	CtaImageUrl          string          `dynamic:"true"`
}

type WaysToEarnTile struct {
	Title       string       `dynamic:"true"`
	TitleColor  string       `dynamic:"true"`
	ImageUrl    string       `dynamic:"true"`
	BgColor     string       `dynamic:"true"`
	Shadow      *BlockShadow `dynamic:"true"`
	CtaImageUrl string       `dynamic:"true"`
	IsVisible   bool         `dynamic:"true"`
}

type UnredeemableOfferLabelDisplayDetails struct {
	ImageUrl  string `dynamic:"true"`
	Text      string `dynamic:"true"`
	FontColor string `dynamic:"true"`
	BgColor   string `dynamic:"true"`
}

type TagsConfig struct {
	TagNameToConfigMap map[string]*TagDisplayDetails `dynamic:"true"`
}

type TagDisplayDetails struct {
	RenderLocationToDisplayDetailsMap map[string]*TagRenderLocationSpecificDisplayDetails `dynamic:"true"`
}

type TagRenderLocationSpecificDisplayDetails struct {
	ImageUrl        string `dynamic:"true"`
	DisplayText     string `dynamic:"true"`
	TextColor       string `dynamic:"true"`
	BackgroundColor string `dynamic:"true"`
	Priority        uint32 `dynamic:"true"`
}

type OfferTile struct {
	TitleColor     string `dynamic:"true"`
	BrandNameColor string `dynamic:"true"`
}

type RewardsBottomSheetInfo struct {
	ImageUrl   string      `dynamic:"true"`
	Title      string      `dynamic:"true"`
	TitleColor string      `dynamic:"true"`
	Desc       string      `dynamic:"true"`
	DescColor  string      `dynamic:"true"`
	Cta        *RewardsCta `dynamic:"true"`
}

type RewardsCta struct {
	Text      string `dynamic:"true"`
	TextColor string `dynamic:"true"`
	BgColor   string `dynamic:"true"`
	ImageUrl  string `dynamic:"true"`
	Deeplink  *deeplinkPb.Deeplink
	// is visible tell whether the CTA is to be shown or not. Though, the functionality decision relies on app
	IsVisible bool `dynamic:"true"`
	// IsEnabled tells whether the CTA is to be sent in response to client or not, i.e. whether the CTA exists or not
	IsEnabled bool `dynamic:"true"`
}

type CatalogFiltersConfig struct {
	MaxPromotedFiltersForTopBar int32 `dynamic:"true"`
}

type CardCatalogConfig struct {
	MaxPromotedFiltersForTopBar int32 `dynamic:"true"`
}

type CardDynamicTileDuration struct {
	// Time after which we will start showing delivery tracking tile
	ViewCardDeliveryTracking time.Duration `dynamic:"true"`
	// Time after which we will show scan qr code as the primary tile and delivery info as secondary tile
	QRCodeAsPrimaryTile time.Duration `dynamic:"true"`
	// Time for which tile under debit card to inform first-time users that the debit card is ready for
	// online use + mention of physical card is to be shown post card creation when their online txns are enabled.
	ViewCardOnlineTxnEnabledTile time.Duration `dynamic:"true"`
	// Time after which we will start showing qr code scan option to the users
	ViewQRCodeScanTime time.Duration `dynamic:"true"`
	// Time after qr code visibility that the activation path via secure pin is displayed
	ViewSecurePinActivationTime time.Duration `dynamic:"true"`
}

type Card struct {
	MinAndroidVersionForFMAuth int
	CardDynamicTileDuration    *CardDynamicTileDuration `dynamic:"true"`
	// List of actions to be shown on card settings screen
	CardSettingTiles []*card.CardSettingTile
	// List of actions to be shown on card home screen
	CardHomeActions                  []card.CardActionType
	MinAndroidVersionForCardOffers   int
	MinAndroidVersionForCardTracking int
	// Min android version above which we will enable users to set/reset atm pin via secure pin
	// validation flow.
	MinAndroidVersionForSecurePinValidation uint32
	// Min ios app version above which we will enable users to set/reset atm pin via secure pin
	// validation flow.
	MinIOSVersionForSecurePinValidation uint32
	// If flag is enabled we will enable secure pin validation for all the users
	// If flag is disabled we will enable secure pin validation for users in `AllowedUserGrpForSecurePinValidationAuth` user groups.
	EnableSecurePinValidationAuth            bool
	AllowedUserGrpForSecurePinValidationAuth []commontypes.UserGroup
	// Error messages to be shown to the user when user tries to scan qr code for card activation for different card states
	CardQRScanErrorMessages *CardQRScanErrorMessages
	// Flag to enable/disable dynamic tile related to card offers
	EnableCardOffersInformativeDynamicTile bool
	// Time after which we will stop showing the informative dynamic tile related to card offers
	OffersDynamicTileExpiryTime string
	// Time after which we will start showing ready to dispatch tile to users.
	// Till this point we have not received any delivery details.
	PrintingToDispatchDynamicTileDuration time.Duration
	// Params associated with requesting a physical debit card for Fi Base tier users
	PhysicalDebitCardRequestParamsFiBase *PhysicalDebitCardRequest
	// Params associated with requesting a physical debit card for Fi Plus users
	PhysicalDebitCardRequestParamsFiPlus *PhysicalDebitCardRequest
	// Params associated with requesting a physical debit card for Fi Infinite tier users
	PhysicalDebitCardRequestParamsFiInfinite *PhysicalDebitCardRequest
	// Params associated with requesting a physical debit card for user (non-salaried user) without tiering flow
	PhysicalDebitCardRequestParamsDefaultUser *PhysicalDebitCardRequest
	// Params associated with requesting a physical debit card for aa salary user
	PhysicalDebitCardRequestParamsAaSalaryUser *PhysicalDebitCardRequest
	// Params associated with requesting a physical debit card for salary lite user
	PhysicalDebitCardRequestParamsSalaryLiteUser *PhysicalDebitCardRequest
	// Params associated with requesting a physical debit card for salaried user
	PhysicalDebitCardRequestParamsSalariedUser *PhysicalDebitCardRequest
	// Params associated with insufficient funds while initiating a card dispatch
	PhysicalCardInsufficientFundsParams *PhysicalCardInsufficientFundsParams
	// Card creation time post which we will start charging users for physical debit card
	// This is due to compliance guidelines as we cant charge for existing users
	PhysicalCardChargingFlowStartTimestamp int64 `dynamic:"true"`
	// Params associated with a physical dispatch request when payment is in progress
	PhysicalDebitCardRequestPaymentInProgressParams *PhysicalDebitCardRequest

	// flag to enable debit card copy card details
	// Deprecated: in favour of EnableDCCopyCardDetailsV1
	EnableDCCopyCardDetails *cfg.PlatformVersionCheck `dynamic:"true"`

	// EnableDCCopyCardDetailsV1 feature release config for controlling the users for which copy
	// DC details have been enabled
	EnableDCCopyCardDetailsV1 *cfg.FeatureReleaseConfig `dynamic:"true"`

	// flag to enable debit card consolidated card controls
	// Deprecated: in favour of EnableDCConsolidatedCardControlsV1
	EnableDCConsolidatedCardControls *cfg.PlatformVersionCheck `dynamic:"true"` // Deprecated

	// EnableDCConsolidatedCardControlsV1 feature release config for controlling the users for which consolidated
	// DC controls have been enabled
	EnableDCConsolidatedCardControlsV1 *cfg.FeatureReleaseConfig `dynamic:"true"`

	// flag to enable debit card activation revamped flow
	EnableSecurePinActivationFlow *cfg.PlatformVersionCheck `dynamic:"true"`

	// DcCardRenewalTypeSelectionScreenConfig contains some common fields value details for DcCardRenewalTypeSelectionScreenOptions
	DcCardRenewalTypeSelectionScreenConfig *DcCardRenewalTypeSelectionScreenConfig

	// EnableDcCardRenewalChargesFlow flag enables dc card renewal charges flow
	EnableDcCardRenewalChargesFlow *cfg.PlatformVersionCheck `dynamic:"true"`

	// flag to enable/disable physical card order entry point in debit card settings
	ShowPhysicalCardOrderEntryPointInCardSettings *cfg.PlatformVersionCheck `dynamic:"true"`

	// flag to enable/disable redirection from card renewal success screen to set card pin flow
	EnablePinSetFlowRedirectionPlatformVersionCheck *cfg.PlatformVersionCheck `dynamic:"true"`

	// configuration for dashboard v2
	DashboardV2Config *DcDashboardV2Config `dynamic:"true" ,quest:"component"`

	// Quest variables for ordering physical DC offers campaign
	OrderPhysicalDCQuest *OrderPhysicalDCQuest `dynamic:"true" ,quest:"component"`

	// F30IssuanceFeeRefund promotional content
	// figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=21727-50562&t=fllFLIjjAXC168nx-4
	F30IssuanceFeeRefundContent *F30IssuanceFeeRefundContent `dynamic:"true"`

	// Feedback engine for DC order page
	DcOrderPageFeedbackEngine *DcOrderPageFeedbackEngine `dynamic:"true"`

	// Onboarding dc intro screen configs
	DcOnboardingIntroScreenConfig *DcOnboardingIntroScreenConfig `dynamic:"true"`

	DcUsageAndLimitsScreenConfig *DcUsageAndLimitsScreenConfig `dynamic:"true"`

	PhysicalCardBenefitsBottomSheetConfig *PhysicalDebitCardBenefitsBottomSheetConfig `dynamic:"true"`
	InternationalDcWidgetConfig           *InternationalDcWidgetConfig                `dynamic:"true"`
}

// PhysicalDebitCardBenefitsBottomSheetConfig will have either FullPageImg or other fields(all) populated,
// If both are populated FullPageImg will be given priority
type PhysicalDebitCardBenefitsBottomSheetConfig struct {
	BenefitTypeToConfigMap     map[string]*PhysicalCardBenefitItem             `dynamic:"true,readonlymap"`
	WelcomeOfferIdToDetailsMap map[string]*PhysicalDebitCardWelcomeOfferConfig `dynamic:"true,readonlymap"`
}

type PhysicalDebitCardWelcomeOfferConfig struct {
	Title   *Text   `dynamic:"true"`
	Details []*Text `dynamic:"true,readonlylist"`
}

type PhysicalCardBenefitItem struct {
	// config will have either `FullPageImage` or the rest other fields populated
	FullPageImage *VisualElementImage `dynamic:"true"`
	TopPageImage  *VisualElementImage `dynamic:"true"`
	PageTitle     *Text               `dynamic:"true"`
}

type DcUsageAndLimitsScreenConfig struct {
	OrderPhysicalCardComponentTitle *Text `dynamic:"true"`
	OrderPhysicalCardComponentDesc  *Text `dynamic:"true"`
}

type DcOnboardingIntroScreenConfig struct {
	Title          *Text                         `dynamic:"true"`
	PagerContent   []*ImageConfigs               `dynamic:"true, readonlylist"`
	SwipeCtaText   *Text                         `dynamic:"true"`
	ExploreProduct *IconTextComponent            `dynamic:"true"`
	Consents       []*OnboardingIntroConsentItem `dynamic:"true, readonlylist"`
}

type OnboardingIntroConsentItem struct {
	ConsentInfo     *HyperLinksWidget
	ConsentIds      []string      `dynamic:"true"`
	ConsentCheckBox *CheckboxItem `dynamic:"true"`
}

type DcOrderPageFeedbackEngine struct {
	IsEnabled bool `dynamic:"true"`
}

type F30IssuanceFeeRefundContent struct {
	IsEnabled               bool `dynamic:"true"`
	SegmentIds              []string
	PhysicalCardSectionText string `dynamic:"true"`
}

type OrderPhysicalDCQuest struct {
	IsEnabled              bool   `dynamic:"true" ,quest:"variable"`
	HomeWidgetTitleString  string `dynamic:"true" ,quest:"variable"`
	HomeWidgetImageUrl     string `dynamic:"true" ,quest:"variable"`
	HomeWidgetBgColour     string `dynamic:"true" ,quest:"variable"`
	HomeWidgetShadowColour string `dynamic:"true" ,quest:"variable"`
}

type DcCardRenewalTypeSelectionScreenConfig struct {
	DigitalCardRenewalItem  *CardRenewalItem
	PhysicalCardRenewalItem *CardRenewalItem
}

type CardRenewalItem struct {
	BenefitsInfo []*DcCardRenewalTypeBenefitInfo
}

type DcCardRenewalTypeBenefitInfo struct {
	DisPlayValue string
	FontColor    string
	BgColor      string
}

type PhysicalCardInsufficientFundsParams struct {
	// Title to be shown
	TitleForInsufficientFunds string
	// Description to be shown
	DescriptionForInsufficientFunds string
	// CTA name to be shown
	CTANameForInsufficientFunds string
}

type PhysicalDebitCardRequest struct {
	// Title to be shown
	TitleForChargesFlow string
	// Description to be shown
	DescriptionForChargesFlow string
	// CTA name to be shown
	CTANameForChargesFlow string
	// Image url to be used
	ImageUrlForChargesFlow string
}

// Error messages to be shown to the user when user tries to scan qr code for card activation for different card states
type CardQRScanErrorMessages struct {
	CreatedCardError   *ErrorMessage
	SuspendedCardError *ErrorMessage
	BlockedCardError   *ErrorMessage
}

type ErrorMessage struct {
	// Title of the error message to be shown
	Title string
	// Description of the error
	Description string
}

type AWS struct {
	Region string
}

type VKYC struct {
	SlotDays                    int `dynamic:"true"`
	MorningStart                int `dynamic:"true"`
	SplitMorning                int `dynamic:"true"`
	SplitAfternoon              int `dynamic:"true"`
	SplitLateAfternoon          int `dynamic:"true"`
	EveningEnd                  int `dynamic:"true"`
	ScheduleToLiveCutOffMinutes int `dynamic:"true"`
	// duration in hours after which popup can be shown again after first dismissal
	PopupTileDuration      int32         `dynamic:"true"`
	AccountFreezeThreshold time.Duration `dynamic:"true"`
	// days remaining for account freeze after which popup cannot be dismissed
	// example - for 30, 30 days before account freeze popup will not be dismissible
	AccountFreezePopupNonDismissibleWithinDays int32 `dynamic:"true"`
	// duration in minutes after which popup cannot be dismissed
	PopupTileNonDismissableAfter int32 `dynamic:"true"`
	// show popup when user has used SavingsBalanceLimitPercent % of their savings balance limit
	SavingsBalanceLimitPercent int32 `dynamic:"true"`
	// show popup when user has used CreditBalanceLimitPercent % of their credit limit
	CreditBalanceLimitPercent int32 `dynamic:"true"`
	// redo ekyc if last ekyc attempt was successful more than PerformEkycAfter duration ago
	PerformEkycAfter           time.Duration `dynamic:"true"`
	ShowAlreadyFullKYCTileTill time.Duration `dynamic:"true"`
	// can be used to simulate flow in non prod
	UnsupportedCustomerDeviceModels []string
	// when true, vkyc tiles will not be shown to LSO users out of biz hours
	DisableVKYCOutOfBizHoursForForceLSO  bool `dynamic:"true"`
	NewPopupTileAccountCreationTimeLimit int  `dynamic:"true"`
	// duration in hours after which popup can be shown again after first dismissal
	NewPopupTileDuration int32 `dynamic:"true"`
	// vkyc home banner background color hex code
	HomeBannerColorHex string `dynamic:"true"`

	// instruction page skip option timer in seconds
	InstructionPageSkipOptionTime int32 `dynamic:"true"`
	// landing page skip option timer in seconds
	LandingPageSkipOptionTime int32 `dynamic:"true"`

	// enable call retry
	EnableCallRetry bool `dynamic:"true"`

	// enable new vkyc error screen
	EnableVES     *app.FeatureConfig `dynamic:"true"`
	FAQCategoryId string             `dynamic:"true"`

	// enable EPAN feature config
	EnableEPAN *app.FeatureConfig `dynamic:"true"`
	// epan roll out percentage
	EPANRolloutPercentage       int                                   `dynamic:"true"`
	ShowAuditorAcceptedTileTime time.Duration                         `dynamic:"true"`
	ABFeatureReleaseConfig      *releaseConfig.ABFeatureReleaseConfig `dynamic:"true"`
}

type InsightsParams struct {
	GetInsightConfig                   *GetInsightConfig
	EpfConfig                          *EpfConfig `dynamic:"true"`
	SmsReadConsentPocAndroidAppVersion int        `dynamic:"true"`
}

type EpfConfig struct {
	// RecentEpfPassbookRequestFetchDuration to get duration to fetch passbook request from dao
	RecentEpfPassbookRequestFetchDuration time.Duration `dynamic:"true"`
	// flag to control enable/disable epf otp flow
	// will use to show epf downtime error screen in case of otp failure rate increase from source
	DisableEpfPassbookOtpFlow bool `dynamic:"true"`
	// flag to skip setting redirection deeplink.
	// client handles navigation to the originating flow in such cases.
	AndroidAppVersionSkipEpfDeeplink uint32 `dynamic:"true"`
}

type GetInsightConfig struct {
	// Time in second after the serving of insight to client, the client respond back with insight NOTICED engagement
	MarkNoticedAfter int32
}

type AddFundsParamOptions struct {
	// default amount to be selected
	DefaultAmount *moneyPb.Money
	// min amount allowed for the flow
	MinAmount *moneyPb.Money
	// max amount allowed for the flow
	MaxAmount *moneyPb.Money
	// list of suggested amounts
	SuggestedAmounts []*SuggestedAmount
	// Remarks t be passed int the intent
	Remarks string
	// Rewards banner to be shown
	RewardsBanner *RewardsBanner
	// denotes if the user should be blocked on the screen
	// until add funds is complete
	//
	// in case of ONBOARD_ADD_FUND wwe will block the user from moving forward until
	// at least the first leg of transaction is complete
	IsBlocking bool
	// denotes if the option is disabled/enabled
	IsEnabled bool
}

type SuggestedAmount struct {
	// suggested amount
	Amount *moneyPb.Money

	// list of tags for the suggested amount. eg. Popular
	Tags []*SuggestedAmountTag
}

type SuggestedAmountTag struct {
	// title for the tag
	Title string
	// icon ur for the tag
	IconUrl string
}

type RewardsBanner struct {
	// description for the rewards banner
	Description string
	// icon url for the rewards banner
	IconUrl string
}

type Lending struct {
	// FE properties related to pre-approved-loan feature
	PreApprovedLoan   *PreApprovedLoan `dynamic:"true" ,quest:"component"`
	LoanProgram       int
	SecuredLoanParams *SecuredLoanParams `dynamic:"true" ,quest:"component"`
	// feature flag to define if to use landing data v3 provider
	UseLandingDataV3 *app.FeatureConfig `dynamic:"true"`
	// allows user to take concurrent loan i.e. another loan even if one loan account is active
	ConcurrentLoansConfig       *ConcurrentLoansConfig `dynamic:"true"`
	EligibilityNuggetBotFeature *app.FeatureConfig     `dynamic:"true"`
}

type ConcurrentLoansConfig struct {
	Enabled bool `dynamic:"true"`
	// this represents the minimum duration from the previous disbursal that user has to wait before taking another loan
	// this only applies to loans of same type, there is no restriction on taking a different type of loan
	// key is the string value of loan type enum
	LoanTypeToMinFreezeDuration map[string]time.Duration `dynamic:"true"`
}

type PreApprovedLoan struct {
	// signifies if pre-approved-loan as a product is enabled or not
	Enabled                                             bool
	FederalPlAppVersionConstraintConfig                 *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	LiquiloansPlAppVersionConstraintConfig              *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	LiquiloansEsAppVersionConstraintConfig              *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	LiquiloansFldgAppVersionConstraintConfig            *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	LiquiloansStplAppVersionConstraintConfig            *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	LiquiloansFlPlAppVersionConstraintConfig            *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	LiquiloansNonFiCoreAppVersionConstraintConfig       *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	IdfcPlAppVersionConstraintConfig                    *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	FiftyfinLamfAppVersionConstraintConfig              *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	PollScreenV2AppVersionConstraintConfig              *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	AbflPlAppVersionConstraintConfig                    *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	AbflPwaAppVersionConstraintConfig                   *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	LendenPlAppVersionConstraintConfig                  *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	RealTimeEtbEligibilityAppVersionConstraintConfig    *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	SkipLandingScreenConstraintConfig                   *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	MvPlAppVersionConstraintConfig                      *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	StockGuradianRealTimeSubvAppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	StockGuradianEarlySalaryAppVersionConstraintConfig  *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	RealTimeSubventionAppVersionConstraintConfig        *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	RealTimeStplAppVersionConstraintConfig              *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	RealTimeOfferUpdateAppVersionConstraintConfig       *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	FederalAaAppVersionConstraintConfig                 *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	FederalRealTimeDistNtbAppVersionConstraintConfig    *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	MvNonFiCorePwaAppVersionConstraintConfig            *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	DesiredLoanAmountAppVersionConstraintConfig         *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	PostDisbursalV2FlowAppVersionConstraintConfig       *PostDisbursalV2Config                    `dynamic:"true"`
	EnablePlLiquiloans                                  bool                                      `dynamic:"true"`
	PrePay                                              *PrePay                                   `dynamic:"true"`
	Vendor                                              fePb.Vendor
	Downtime                                            *Downtime
	// Deprecated: not in use now
	LoanOfferDetailsScreenV2 *LoanOfferDetailsScreenV2 `dynamic:"true" ,quest:"component"`
	// Deprecated: not in use now
	LoanApplicationDetailsScreenV2 *LoanApplicationDetailsScreenV2 `dynamic:"true" ,quest:"component"`
	InstantCashSegmentId           string                          `dynamic:"true"`
	PrePayConfig                   *PrePayConfigMap                `dynamic:"true"`
	LoanNonEligibleScreenV2        *LoanNonEligibleScreenV2        `dynamic:"true"`

	// IsLoanOriginationDropOffFeedbackEnabled flag is useful to denote whether the drop-off related feedbacks for loan origination flow are enabled or not.
	IsLoanOriginationDropOffFeedbackEnabled bool `dynamic:"true"`

	// IsAlternateAccountFlowEnabled flag determines if alternate account flow is enabled for the user
	// this variable is marked as quest for AB experimentation
	IsAlternateAccountFlowEnabled bool `dynamic:"true" ,quest:"variable"`

	LoanDetailsSelectionV2Flow *LoanDetailsSelectionV2Flow `dynamic:"true" ,quest:"component"`
	// category format <vendor>:<loan_program>
	CategoryToEmiComms map[string]*pkgLoansConfig.EmiComms `dynamic:"true"`

	// IsAlternateAccountFlowEnabledForLL flag determines if alternate account flow is enabled for the user in liquiloans journey
	// this variable is marked as quest for AB experimentation
	IsAlternateAccountFlowEnabledForLL bool     `dynamic:"true" ,quest:"variable"`
	ITRFlow                            *ITRFlow `dynamic:"true"`
	// contains the config for new loan offer animated highlight in the bottom navigation bar
	NewLoanOfferNavBarHighLight *pkgLoansConfig.NavBarHighlight `dynamic:"true"`
	ApplicationStatusPollConfig *LendingApplicationStatusPollRetryConfig

	MinKycUsersReleaseConfig        *MinKycUsersReleaseConfig `dynamic:"true"`
	OfferDetailsV3Config            *OfferDetailsV3Config     `dynamic:"true" ,quest:"component"`
	PrePayShortcutSegmentExpression string
	OfferDetailsV4Config            *OfferDetailsV4Config   `dynamic:"true"`
	A2LLandingScreenConfig          *A2LLandingScreenConfig `dynamic:"true" ,quest:"component"`
	// allows user to cancel current LR and start a new one in a single click
	AutoCancelCurrentLrConfig         *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	ShowEligibilityInMultiOfferConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	BadgeOnOfferIntroScreenConfig     *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	RestrictShowingMultipleOffers     bool                                      `dynamic:"true" ,quest:"variable"`
}

type A2LLandingScreenConfig struct {
	Header       *A2LLandingScreenHeader       `dynamic:"true" ,quest:"component"`
	BenefitsGrid *A2LLandingScreenBenefitsGrid `dynamic:"true" ,quest:"component"`
	Banner       *A2LLandingScreenBanner       `dynamic:"true" ,quest:"component"`
	Footer       *A2LLandingScreenFooter       `dynamic:"true" ,quest:"component"`
}

type A2LLandingScreenHeader struct {
	Title    string `dynamic:"true" ,quest:"variable"`
	Subtitle string `dynamic:"true" ,quest:"variable"`
}

type A2LLandingScreenBenefitsGrid struct {
	TopLeftBenefit     *A2LLandingScreenBenefit `dynamic:"true" ,quest:"component"`
	TopRightBenefit    *A2LLandingScreenBenefit `dynamic:"true" ,quest:"component"`
	BottomLeftBenefit  *A2LLandingScreenBenefit `dynamic:"true" ,quest:"component"`
	BottomRightBenefit *A2LLandingScreenBenefit `dynamic:"true" ,quest:"component"`
}

type A2LLandingScreenBenefit struct {
	IconUrl string `dynamic:"true" ,quest:"variable"`
	Text    string `dynamic:"true" ,quest:"variable"`
}

type A2LLandingScreenBanner struct {
	Text string `dynamic:"true" ,quest:"variable"`
}

type A2LLandingScreenFooter struct {
	PrimaryCTAText string `dynamic:"true" ,quest:"variable"`
}

type OfferDetailsV4Config struct {
	IsEnabled                  bool                                      `dynamic:"true" ,quest:"variable"`
	AppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	VendorLoanProgramMap       map[string]*OfferDetailsV4Config          `dynamic:"true"`
	SkipAmountSelectionScreen  bool                                      `dynamic:"true" ,quest:"variable"`
	ShowInterestRate           bool                                      `dynamic:"true" ,quest:"variable"`
	ShowZeroPreClosureTag      bool                                      `dynamic:"true" ,quest:"variable"`
}

type OfferDetailsV3Config struct {
	IsEnabled                  bool                                      `dynamic:"true" ,quest:"variable"`
	AppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	VendorLoanProgramMap       map[string]bool                           `dynamic:"true"`
	SkipAmountSelectionScreen  bool                                      `dynamic:"true" ,quest:"variable"`
	ShowInterestRate           bool                                      `dynamic:"true" ,quest:"variable"`
	ShowZeroPreClosureTag      bool                                      `dynamic:"true" ,quest:"variable"`
}

type PrePayViaPGConfig struct {
	// "LIQUILOANS:LOAN_PROGRAM_FLDG": false
	// "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN": false
	// "LIQUILOANS:LOAN_PROGRAM_STPL": false
	LenderLoanProgramToIsPgEnabledMap map[string]bool `dynamic:"true"`
}

type MinKycUsersReleaseConfig struct {
	MinAndroidVersion int `dynamic:"true"`
	MinIOSVersion     int `dynamic:"true"`

	// segment expression to which the actor should belong to see this highlight
	SegmentExpression string `dynamic:"true"`
}

type LoanOfferDetailsScreenV2 struct {
	Enable                     bool                                      `dynamic:"true"`
	AppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	LoanConstraintsComponent   *LoanConstraintsComponent                 `dynamic:"true" ,quest:"component"`
	LoanInfoComponent          *LoanInfoComponent                        `dynamic:"true" ,quest:"component"`
}

type LoanConstraintsComponent struct {
	Version   *Version   `dynamic:"true" ,quest:"component"`
	CtaButton *CtaButton `dynamic:"true" ,quest:"component"`
}

type LoanInfoComponent struct {
	Version *Version `dynamic:"true" ,quest:"component"`
}

type Version struct {
	Minor int32 `dynamic:"true" ,quest:"variable"`
	Major int32 `dynamic:"true" ,quest:"variable"`
}

type LoanApplicationDetailsScreenV2 struct {
	Enable                                  bool                                      `dynamic:"true"`
	AppVersionConstraintConfig              *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	ApplicationLoanInfoComponentVersion     *Version                                  `dynamic:"true" ,quest:"component"`
	ApplicationEmiInfoComponentVersion      *Version                                  `dynamic:"true" ,quest:"component"`
	ApplicationTncInfoComponentVersion      *Version                                  `dynamic:"true" ,quest:"component"`
	ApplicationBenefitsInfoComponentVersion *Version                                  `dynamic:"true" ,quest:"component"`
}

type CtaButton struct {
	FontColor   string           `dynamic:"true" ,quest:"variable"`
	BgColor     string           `dynamic:"true" ,quest:"variable"`
	Text        string           `dynamic:"true" ,quest:"variable"`
	WrapContent bool             `dynamic:"true" ,quest:"variable"`
	Padding     *AlignmentValues `dynamic:"true" ,quest:"component"`
	Margin      *AlignmentValues `dynamic:"true" ,quest:"component"`
}

type AlignmentValues struct {
	Left   int32 `dynamic:"true" ,quest:"variable"`
	Right  int32 `dynamic:"true" ,quest:"variable"`
	Top    int32 `dynamic:"true" ,quest:"variable"`
	Bottom int32 `dynamic:"true" ,quest:"variable"`
}

// LoanDetailsSelectionV2Flow will have all the config to determine whether new loans selection screens are used or not.
// It will all the related configs to enable A/B experimentation on loan selection journey, e.g. skip loader screen, show reward tab or not etc...
type LoanDetailsSelectionV2Flow struct {
	IsEnabled                  bool `dynamic:"true" ,quest:"variable"`
	IsRewardsEnabled           bool `dynamic:"true" ,quest:"variable"`
	SkipAmountSelectionScreen  bool `dynamic:"true" ,quest:"variable"`
	ShowMultipleOfferSelection bool `dynamic:"true" ,quest:"variable"`
	ShowCollapsedBreakdownView bool `dynamic:"true" ,quest:"variable"`
	ShowDiscountedPf           bool `dynamic:"true" ,quest:"variable"`
	ShowCongratulatoryText     bool `dynamic:"true" ,quest:"variable"`
	// ShowAprPopup decides if APR details are shown as a popup(true) or in grid(false) on new s2 screen
	ShowAprPopup bool `dynamic:"true" ,quest:"variable"`
	// ShowUserTestimonials decides if user reviews are shown on landing screen or not
	ShowUserTestimonials bool `dynamic:"true" ,quest:"variable"`
	// ShowExtraCtaInfo checks if we will show the extra info above cta on new landing screen
	ShowExtraCtaInfo bool `dynamic:"true" ,quest:"variable"`
	// vendor and loan program level config. For vendors where single program is live (e.g: Federal, IDFC and ABFL), vendor name will used to enable the flow
	// for others, loan programs in the list will be checked and used to enable the flow
	EnableLoanPrograms      []string           `dynamic:"true"`
	DefaultAmountPercentage map[string]float64 `dynamic:"true"`
}

type PrePay struct {
	// signifies if pre-pay in pre-approved-loan is enabled or not
	Enabled bool `dynamic:"true"`
	// signifies if loan pre-pay, repayment breakup in V2 flow is enabled or not
	V2Config *PrePayV2Config `dynamic:"true"`
}

type PrePayV2Config struct {
	Enabled                    bool                                      `dynamic:"true"`
	AppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
}

type PrePayConfigMap struct {
	LLFldg            *PrePayConfig      `dynamic:"true"`
	LLEs              *PrePayConfig      `dynamic:"true"`
	LLAcqToLend       *PrePayConfig      `dynamic:"true"`
	IdfcPl            *PrePayConfig      `dynamic:"true"`
	FederalPl         *PrePayConfig      `dynamic:"true"`
	RTSubvention      *PrePayConfig      `dynamic:"true"`
	PrePayViaPGConfig *PrePayViaPGConfig `dynamic:"true"`
}

type LoanAnalyserInsightsParams struct {
	PlSegmentAndInsightContent   map[string]*SegmentAndInsightContent `dynamic:"true"`
	LamfSegmentAndInsightContent map[string]*SegmentAndInsightContent `dynamic:"true"`
}

type PrePayConfig struct {
	Enabled                    bool                                      `dynamic:"true"`
	AppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	Tpap                       *PlTpap                                   `dynamic:"true"`
}

type SegmentAndInsightContent struct {
	// needed for supporting arrays in dynamic config.
	ArrayElement      *cfg.DynamicArrayElement `dynamic:"true"`
	SegmentExpression string                   `dynamic:"true"`
	InsightDetails    *InsightDetails          `dynamic:"true"`
}

type InsightDetails struct {
	Title, TitleColor       string `dynamic:"true"`
	Subtitle, SubtitleColor string `dynamic:"true"`
	Image                   string `dynamic:"true"`
	CtaText, CtaTextColor   string `dynamic:"true"`
	CtaColor                string `dynamic:"true"`
	BgColor                 string `dynamic:"true"`
}

type PostDisbursalV2Config struct {
	AppVersionConstraintConfig               *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	VendorLoanProgramMap                     map[string]bool                           `dynamic:"true"`
	NewOfferBannerAppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	SkipVendorLoanProgramCheck               bool                                      `dynamic:"true"`
}

type Downtime struct {
	Start string
	End   string
}

type PlTpap struct {
	Enabled bool `dynamic:"true"`
}

type ITRFlow struct {
	Enabled                    bool                                      `dynamic:"true"`
	AppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
}

type SecuredLoanParams struct {
	ReleasedSegmentDetails *ReleasedSegmentDetails `dynamic:"true"`
	// This variable for AB experimentation
	SecuredLoanExperiments      *SecuredLoanExperiments `dynamic:"true" ,quest:"component"`
	HomeNavBarSegmentExpression string                  `dynamic:"true"`
	DowntimeConfig              *Downtime
	EnablePreCloseFlow          bool `dynamic:"true"`
	EnableAutoPaySetupBanner    bool `dynamic:"true"`
}

type SecuredLoanExperiments struct {
	SkipInfoPage                     bool `dynamic:"true" ,quest:"variable"`
	SkipLandingPage                  bool `dynamic:"true" ,quest:"variable"`
	ChangeButtonTextLandingPage      bool `dynamic:"true" ,quest:"variable"`
	IsDeclarationCheckboxJourneyPage bool `dynamic:"true" ,quest:"variable"`
}
type ReleasedSegmentDetails struct {
	IsEnabled  bool   `dynamic:"true"`
	Expression string `dynamic:"true"`
}

type Fittt struct {
	SDPreclosureMessageBox                            *FitMessageBox `dynamic:"true" ,quest:"component:SDPreclosureMessageBox"`
	SDPreclosureAlertBar                              *FitAlertBar
	SIRevokedOrInvalidAlertBar                        *FitAlertBar
	RuleTemporarilyUnavailableAlertBar                *FitAlertBar
	ActorIneligibleForMFPurchaseOrderAlertBar         *FitAlertBar
	ActorIneligibleForMFKYCNotVerified                *FitAlertBar
	FiSavingsLeague                                   *FiSavingsLeague `dynamic:"true"`
	AndroidAppPackageNameMap                          *AndroidAppPackageNameMapping
	ExecutionInfoLimit                                uint32 `dynamic:"true"`
	ExecutionHistoryPageSize                          uint32 `dynamic:"true"`
	ExecutionHistoryPageSizeOnSubscriptionDetailsPage uint32 `dynamic:"true"`
	MaxNoOfMoreCollectionsOnCollectionInfoPage        uint32 `dynamic:"true"`
	MaxNoOfCollectionsOnFitDigest                     uint32 `dynamic:"true"`
	MaxNoOfHomeCardsOnFitDigest                       uint32 `dynamic:"true"`
	MaxLengthOfFitttRuleDescription                   uint32 `dynamic:"true"`
	MinAndroidVersionToSupportAggregationTileV2       uint32 `dynamic:"true"`
	MinIOSVersionToSupportAggregationTileV2           uint32 `dynamic:"true"`
	ExploreCollectionsDefaultSelectedCategory         uint32 `dynamic:"true"`
	FitttDigestCtaText                                string `dynamic:"true"`
	FitttDigestTitleTextDefaultVariant                uint32 `dynamic:"true"`
	MinAndroidVersionToSupportFeaturedRulesBanner     uint32 `dynamic:"true"`
	MinIOSVersionToSupportFeaturedRulesBanner         uint32 `dynamic:"true"`
	ShowSiAmountPreRequisiteDialog                    bool   `dynamic:"true"`
	RuleStoryDefaultVisibility                        bool   `dynamic:"true"`
	FitttDigestTitleTextVariants                      []string
	AllowedUserGroupsForSportsChallenge               []string
	// map of param type to message to be shown on rule param carousel in case editing is not allowed for param
	// eg: Payee cannot be edited, Mutual fund cannot be edited etc
	ParamNonEditableMessage                  map[string]string `dynamic:"true"`
	AutoPayCollectionBannerAllowedUserGroups []commontypes.UserGroup
	RuleIds                                  *FitttRuleIds
	MyRulesPageBanners                       []*MyRulesPageBanner
	InvestRegularlyCollectionId              string       `dynamic:"true"`
	SaveWhenYouShopCollectionId              string       `dynamic:"true"`
	MyRulesPage                              *MyRulesPage `dynamic:"true" ,quest:"component"`
}

type MyRulesPage struct {
	NewRulesCTAText string `dynamic:"true" ,quest:"variable"`
}

type FitttRuleIds struct {
	AutoSaveDaily     string
	AutoSaveWeekly    string
	AutoSaveMonthly   string
	AutoInvestDaily   string
	AutoInvestWeekly  string
	AutoInvestMonthly string
	AutoPayOneTime    string
	AutoPayRecurring  string
}

type FiSavingsLeague struct {
	TournamentTagId string            `dynamic:"true"`
	CollectionId    string            `dynamic:"true"`
	Rule1           *FitRule          `dynamic:"true"`
	Rule2           *FitRule          `dynamic:"true"`
	Rule3           *FitRule          `dynamic:"true"`
	TeamLogos       map[string]string `dynamic:"true"`
	// LuckyDrawRevealCutOffTime holds the time in each day when the lucky draw for the league will be evaluated by rewards service.
	// Till this time, FIT will assume that the lucky draw is not completed and show Zeroth state card for Lucky draw
	LuckyDrawRevealCutOffTime *Time `dynamic:"true"`
	// Duration before the match start from when the Zeroth state card should be displayed
	LuckyDrawZerothCardDurationBeforeMatchStart time.Duration `dynamic:"true"`
	// Duration before the start of next match till when the Match ended card for previous match should be displayed
	MatchEndedCardDurationBeforeNextMatch time.Duration `dynamic:"true"`
	// After the match end time, we have buffer time
	MatchExecutionBufferDuration time.Duration `dynamic:"true"`
	// No of days to fetch previous and upcoming matches
	FetchMatchWindowInDays uint32 `dynamic:"true"`
	LeaderboardRankLimit   uint32 `dynamic:"true"`
	LeaderboardRefreshTime *Time  `dynamic:"true"`
	// Start Date Of the tournament
	StartDateOfTournament              int64                         `dynamic:"true"`
	EndDateOfTournament                int64                         `dynamic:"true"`
	TournamentSecondWeekStartTimestamp int64                         `dynamic:"true"`
	WeeklyRewardsFirstWeekNo           uint32                        `dynamic:"true"`
	WeeklyRewardsCardDisplayData       *WeeklyRewardsCardDisplayData `dynamic:"true"`
	ShouldShowWeeklyRewardsBannerFirst bool                          `dynamic:"true"`
	// Duration before start of first match of a new week when we want to show empty leaderboard to the user
	LeaderboardResetDurationBeforeFirstMatchOfTheWeek time.Duration `dynamic:"true"`
	// reset to zero state card after WeeklyCardStateResetDuration hours from start of the week
	WeeklyCardStateResetDuration time.Duration `dynamic:"true"`
	// new home card design
	HomeCardVariants []*NewHomeCard
	// if HardCodedHomeCardVariant is non-zero, show the home card to all the users
	// if HardCodedHomeCardVariant is 0, hash function will decide the home card to be shown for different users
	HardCodedHomeCardVariant    int  `dynamic:"true"`
	WeeklyRewardsDeclarationDay int  `dynamic:"true"`
	ShowDailyBanner             bool `dynamic:"true"`
	ShowDefaultBanner           bool `dynamic:"true"`
	ShowInvestInTheTeamBanner   bool `dynamic:"true"`
	ShowIntroPage               bool `dynamic:"true"`
	ShowLeaderboard             bool `dynamic:"true"`
	ShowRewardsDigest           bool `dynamic:"true"`
}

type MyRulesPageBanner struct {
	Id                     string `dynamic:"true"`
	TitleText              string `dynamic:"true"`
	TitleFontColor         string `dynamic:"true"`
	CardDisplayInfoUrl     string `dynamic:"true"`
	CardDisplayInfoBgColor string `dynamic:"true"`
	CardCtaId              string `dynamic:"true"`
	CardCtaText            string `dynamic:"true"`
	CardCtaTextFontColor   string `dynamic:"true"`
	CardCtaImgUrl          string `dynamic:"true"`
	AnalyticsId            string `dynamic:"true"`
	// If set to nothing/0, banner will remain active forever
	// If a timestamp in unix is passed, banner will not be displayed to user after the configured timestamp
	ActiveTill        int64           `dynamic:"true"`
	MinAndroidVersion uint32          `dynamic:"true"`
	MinIOSVersion     uint32          `dynamic:"true"`
	Deeplink          *FitttDeeplinks `dynamic:"true"`
	DisabledForFiLite bool            `dynamic:"true"`
	Disabled          bool            `dynamic:"true"`
}

type FitttDeeplinks struct {
	Screen        string                `dynamic:"true"`
	ScreenOptions *FitttDeeplinkOptions `dynamic:"true"`
}

type FitttDeeplinkOptions struct {
	RuleId         string `dynamic:"true"`
	SubscriptionId string `dynamic:"true"`
	CollectionType string `dynamic:"true"`
	PageType       string `dynamic:"true"`
	TournamentId   string `dynamic:"true"`
}

type NewHomeCard struct {
	Title     string                            `dynamic:"true"`
	FontColor string                            `dynamic:"true"`
	ImgUrl    string                            `dynamic:"true"`
	BgColor   string                            `dynamic:"true"`
	CardType  fittt.FiSavingsLeagueHomeCardType `dynamic:"true"`
	Id        string                            `dynamic:"true"`
}

type WeeklyRewardsCardDisplayData struct {
	ShowWeeklyBanner bool             `dynamic:"true"`
	Rank1            *CardDisplayData `dynamic:"true"`
	Rank2            *CardDisplayData `dynamic:"true"`
	Rank3            *CardDisplayData `dynamic:"true"`
	Rank4To50        *CardDisplayData `dynamic:"true"`
}

type CardDisplayData struct {
	Title                       string `dynamic:"true"`
	SubTitle                    string `dynamic:"true"`
	Description                 string `dynamic:"true"`
	ImgUrl                      string `dynamic:"true"`
	ClaimPrizeScreenTitle       string `dynamic:"true"`
	ClaimPrizeScreenDescription string `dynamic:"true"`
}

type FitRule struct {
	Id                   string                `dynamic:"true"`
	RuleCardDisplayTexts *RuleCardDisplayTexts `dynamic:"true"`
}

type RuleCardDisplayTexts struct {
	ExistingSubTitle  string `dynamic:"true"`
	NoSubTitle        string `dynamic:"true"`
	NoSubDesc         string `dynamic:"true"`
	SelectionLimitMsg string `dynamic:"true"`
	DefaultMoneyVal   int64  `dynamic:"true"`
	ImgUrl            string `dynamic:"true"`
	Name              string `dynamic:"true"`
}

type AndroidAppPackageNameMapping struct {
	NameToPackageMap map[string]string
}

type FitMessageBox struct {
	Title             string `dynamic:"true" ,quest:"variable"`
	DescriptionFormat string
	LogoURL           string
}

type FitAlertBar struct {
	Description string
	BGColor     string
}

type AccountStatementErrorViewParams struct {
	Title                 string
	DefaultSubtitle       string
	WrongDurationSubtitle string
}

type Comms struct {
	NotificationsPageSize int64
}

type EnableBharatQrToUserGroup struct {
	// if flag is disabled then bharatqr will be enabled for all user group.
	// if enable then bharat qr will be enabled for user group in `AllowedUserGrpForBharatQr`
	IsBharatQRRestricted      bool
	AllowedUserGrpForBharatQr []commontypes.UserGroup
}

type TimelineTimestampFormats struct {
	// timestamp format for the title of the timeline event froup
	// for eg. title should be shown in the format 'APR 24'
	TitleTimestampFormat string
	// timestamp format for the timeline events
	// for eg. event timestamp should be shown in the format '12:23 PM'
	EventTimestampFormat string
}

type Signup struct {
	// feature config for availability of themes in info acknowledgement screen options
	ThemeBasedInfoAckScreen *app.FeatureConfig

	// AcctClosureBalTransfer is the feature config for transferring balance
	// from Fi account after closure due to Min KYC Expiry.
	// It enables CTA "Add bank account details" on account closure landing screen.
	AcctClosureBalTransfer *app.FeatureConfig

	// ShowAccountFrozenScreenV2 gives the minimum versions for the
	// new account freeze screen to be showed
	ShowAccountFrozenScreenV2 *app.FeatureConfig

	// bottom sheet on account closure's know more cta is hardcoded in IOS
	// we disabled know more cta from IOS for app version having this as hardcoded
	EnableKnowMoreAccountClosureFlowIOS *app.FeatureConfig `dynamic:"true"`

	// As per NPCI regulations, we can't allow users on android versions below this to onboard.
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=41533
	MinAndroidAPIVersionForSignup float64
	// Enable feature to block users below MinimumAndroidVersionForSignup
	EnableAndroidAPIVersionCheckForSignup bool `dynamic:"true"`
	// BlockOnboardingDueToUnlinkedPANAndAadhaar states that the onboarding process is blocked because the PAN and Aadhaar are not linked
	BlockOnboardingDueToUnlinkedPANAndAadhaar bool `dynamic:"true"`
	// EnableDeviceIntegrityScreen is the feature flag to enable device integrity screen from LoginWithOAuth
	EnableDeviceIntegrityScreen bool `dynamic:"true"`

	// BabaPhoneNumberHashes is a map of phone number hashes of "Baba" role / internal testers.
	// It allows testing of one-time journeys, such as onboarding, without any restrictions.
	// Key is the SHA-1 hash of a 12-digit phone num. Use obfuscator.HashedPhoneNum(phoneNum) to generate the hash.
	BabaPhoneNumberHashes map[string]bool `dynamic:"true"`
}

type Screening struct {
	EmpVerificationCheckStatusPollIntervalInSecs          int32              `dynamic:"true"`
	CheckCreditReportAvailabilityStatusPollIntervalInSecs int32              `dynamic:"true"`
	CheckCreditReportVerificationStatusPollIntervalInSecs int32              `dynamic:"true"`
	ShowVerifyOtpScreenOptionsErr                         *app.FeatureConfig `dynamic:"true"`
}

type ReferralsV1 struct {
	RewardTypeToRewardV1IconMap map[string]string `dynamic:"true"`
	ReferralsHistoryPageSize    uint32            `dynamic:"true"`
	// flag to enable/disable referrals for d2h users
	IsReferralForD2hUsersEnabled bool `dynamic:"true"`

	MinAndroidVersionSupportingInviteContactsSection uint32 `dynamic:"true"`
	MinIosVersionSupportingInviteContactsSection     uint32 `dynamic:"true"`

	MinAndroidVersionForImplicitCopyCodeButton uint32 `dynamic:"true"`
	MinIosVersionForImplicitCopyCodeButton     uint32 `dynamic:"true"`

	ReferralLinkGenerationInfoConf *ReferralLinkGenerationInfoConf `dynamic:"true"`

	// stage specific reminder scripts to be shared with the referee by the referrer.
	// current stages to be reminded: NEEDS_TO_SIGN_UP, NEEDS_TO_ADD_FUNDS
	RefereeStageRemindScripts map[string]string `dynamic:"true"`

	// app-update config to show hard-nudge for app-versions below the specified values for select user-buckets
	AppUpdateHardNudgeConf *ReferralsAppUpdateNudgeConf `dynamic:"true"`

	// app-update config to show soft-nudge for app-versions below the specified values for select user-buckets
	AppUpdateSoftNudgeConf *ReferralsAppUpdateNudgeConf `dynamic:"true"`

	// config specific to referral rewards v1 stacked rewards
	StackedRewards *ReferralsV1StackedRewardsConf `dynamic:"true"`

	// figma: https://www.figma.com/design/5TZH6I0awKw8DMtj3aMzrC/Referrals-%E2%80%A2-FFF?node-id=11840-4543&t=58c1Rjjbc0lz0APN-1
	LandingPageWeeklyEarningsComponentFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for reward cap reset info component on landing page
	LandingPageRewardCapResetInfoFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for reward home v2 widget
	HomeWidgetV2FeatureConfig *app.FeatureConfig `dynamic:"true"`
	// id of referrals segmented component to be used for home widget v2
	HomeWidgetV2SegmentedComponentId string `dynamic:"true" ,quest:"variable"`
}

type ReferralsV1StackedRewardsConf struct {
	StartDateTime time.Time `dynamic:"true"`
	EndDateTime   time.Time `dynamic:"true"`
	// Flag to enable disable component related to earning summary in referral history
	ShowReferralHistoryEarningSummary bool `dynamic:"true"`
	// duration after which the referee signup action expires
	// this duration is calculated after finite code claim
	RefereeSignupActionExpiryDuration time.Duration `dynamic:"true"`
	// duration after which the referee add money action expires
	// this duration is calculated after debit card pin set
	RefereeAddMoneyActionExpiryDuration time.Duration `dynamic:"true"`
}

type ReferralsAppUpdateNudgeConf struct {
	// app version above which nudge SHOULD NOT be shown for Android
	ShowNudgeTillAppVersionAndroid uint32 `dynamic:"true"`
	// app version above which nudge SHOULD NOT be shown for iOS
	ShowNudgeTillAppVersionIos uint32 `dynamic:"true"`

	// user buckets for whom nudge has to be shown [start,end]
	UserBucketStart int32 `dynamic:"true"`
	UserBucketEnd   int32 `dynamic:"true"`
}

type ReferralLinkGenerationInfoConf struct {
	LinkValidForSinceGeneration time.Duration `dynamic:"true"`

	// AF Onelink template id, for e.g. 5vGH
	OnelinkTemplateId string `dynamic:"true"`
	// value to be used for `af_channel`
	AfChannel string `dynamic:"true"`
	// value to be used for `c`/`campaign`
	Campaign string `dynamic:"true"`
	// remaining params to be used for generation of the URL
	OnelinkGenerationParams map[string]string `dynamic:"true"`
}

type Referrals struct {
	// if flag is true, referrals via finite code is enabled for all user groups present in AllowedUserGroupsForReferrals
	IsReferralsViaFiniteCodeAllowed bool

	// user groups for which referrals via finite code is allowed
	AllowedUserGroupsForReferrals []commontypes.UserGroup

	// feature flag to claim an in-app referral finite code
	CanClaimInAppReferralFiniteCode bool

	// page size for referrals history page
	ReferralsHistoryPageSize uint32 `dynamic:"true"`

	// page size to fetch referrals for showing the recent referees, and also calculating lifetime winnings etc
	RecentReferralsPageSize uint32 `dynamic:"true"`

	// achievement pop-up image
	AchievementImageUrl string

	// season completion title
	SeasonCompletionTitle string

	// season completion description text
	SeasonCompletionText []string

	// season completion image
	SeasonCompletionImage string

	// season completion TnCs
	SeasonCompletionTnc []string

	// landing screen promotional banner
	LandingScreenPromotionalBanner *ReferralsLandingBanner `dynamic:"true"`

	// minimum android version supporting home referrals widget v1
	MinAndroidVersionSupportingNewHomeReferralsWidget uint32 `dynamic:"true"`

	// minimum IOS version supporting home referrals widget v1
	MinIosVersionSupportingNewHomeReferralsWidget uint32 `dynamic:"true"`

	// minimum iOS version onwards which the fix to support single/double CTA exists for home-v1 referrals entry point
	MinIosVersionSupportingCtasFixForHomeV1EntryPoint uint32 `dynamic:"true"`

	// prefix cta for iOS home-v1 referrals entry point when unlocked for backward compatibility
	PrefixCtaForIosHomeV1ReferralsUnlockedEntryPoint *ReferralsHomeV1EntryPointCta `dynamic:"true"`

	// app download URL for referrals
	AppDownloadUrl string `dynamic:"true"`

	// app download URL for referrals that includes referrer's FiniteCode
	AppDownloadUrlWithFiniteCode string `dynamic:"true"`

	// landing page to be shown to all the users.
	// 0 -> unspecified
	// 1 -> V0 (old referrals)
	// 2 -> V1 (new referrals)
	LandingPageVersion uint32 `dynamic:"true"`

	// info for referrals related screens during onboarding
	InfoDuringOnboarding *ReferralsInfoDuringOnboarding `dynamic:"true"`

	// config for restricting referrals based on some params
	FeatureRestrictionParams *ReferralsFeatureRestrictionParams `dynamic:"true"`
	// share message for referral in-eligible SA users
	InEligibleSAUsersShareMsg string `dynamic:"true"`
}

type ReferralsFeatureRestrictionParams struct {
	// Params to apply restriction on basis of age
	AgeRestriction *ReferralsAgeRestrictionParams `dynamic:"true"`
}

type ReferralsAgeRestrictionParams struct {
	// Flag to enable/disable restriction
	Enable bool `dynamic:"true"`
	// Lower bound for age; below which referrals feature is restricted
	AgeLowerBound int `dynamic:"true"`
}

type Alfred struct {
	// page size for service request history page
	ServiceRequestHistoryPageSize uint32 `dynamic:"true"`
	// enable view request history tab
	EnableVRH *app.FeatureConfig `dynamic:"true"`
	// enable cancelled cheque
	EnableCancelledCheque *app.FeatureConfig `dynamic:"true"`
	// enable request choice bottom sheet
	EnableRequestChoiceBottomSheet  *app.FeatureConfig `dynamic:"true"`
	EnableAddressUpdate             *app.FeatureConfig `dynamic:"true"`
	EnableAddressUpdateReqHistories *app.FeatureConfig `dynamic:"true"`
	// enable copy tracking url feature
	EnableCopyTrackingUrl *app.FeatureConfig `dynamic:"true"`
	// feature for updating savings acc signature
	EnableSavingsAccSignUpdate *app.FeatureConfig `dynamic:"true"`
}

type ReferralsInfoDuringOnboarding struct {
	// default offer codes to be shown during onboarding. For e.g. FI200, FI2500
	// map[any-string-to-describe-code]*ReferralsOnbScreenOfferCode
	OfferCodes map[string]*ReferralsOnbScreenOfferCode `dynamic:"true"`
	// popup details when user applies a referral code (i.e. finite-code. different from the offer-codes)
	ReferralCodeAppliedPopupDetails *ReferralsOnbScreenCodeSubmittedDialogBox `dynamic:"true"`
	// popup details when user applies referral code FI200
	FI200ReferralCodeAppliedPopupDetails *ReferralsOnbScreenCodeSubmittedDialogBox `dynamic:"true"`
	// popup details when user applies WEALTH_ANALYSER intent referral code (WealthAnalyserReferralCode)
	WealthAnalyserReferralCodeAppliedPopupDetails *ReferralsOnbScreenCodeSubmittedDialogBox `dynamic:"true"`
	// popup details when claim for an entered referral code fails (i.e. finite-code. different from the offer-codes)
	ReferralCodeClaimFailedPopupDetails *ReferralsOnbScreenCodeSubmittedDialogBox `dynamic:"true"`
	// min version for popup to be shown in the onboarding referrals screen v1 and v2, when code claim fails
	ReferralCodeClaimFailedPopup *app.FeatureConfig `dynamic:"true"`
	// maximum no. of times a user can try to claim a referral code
	MaxReferralCodeClaimAttempts int32  `dynamic:"true"`
	WealthAnalyserReferralCode   string `dynamic:"true"`
}

type ReferralsOnbScreenOfferCode struct {
	// flag to decide whether offer-code is enabled or not
	IsEnabled bool `dynamic:"true"`

	Icon string `dynamic:"true"`
	// code to be displayed.
	// Note: this can be different from finite-code, for e.g. FI200
	DisplayCode string `dynamic:"true"`
	// actual finite-code to be used for claiming.
	// Note: can be empty
	UnderlyingFiniteCode string `dynamic:"true"`
	// offer text, a short sentence
	Offer string `dynamic:"true"`
	// description of the offer, slightly longer than `offer`
	Desc string `dynamic:"true"`
	// name of the CTA to apply the code
	CtaText string `dynamic:"true"`
	// popup details when the code is applied
	CodeAppliedPopupDetails *ReferralsOnbScreenCodeSubmittedDialogBox `dynamic:"true"`
	// identifier to find the offer code
	Identifier string `dynamic:"true"`
}

type ReferralsOnbScreenCodeSubmittedDialogBox struct {
	// image for the dialog box
	Image string `dynamic:"true"`
	// title
	Title string `dynamic:"true"`
	// subtitle
	Subtitle string `dynamic:"true"`
	// description
	Desc string `dynamic:"true"`
	// CTA text
	CtaText string `dynamic:"true"`
	// flag to decide whether the user can dismiss the dialog box or not.
	// Note: assuming that `Accept/Continue` action will be shown by-default
	CanDismiss bool `dynamic:"true"`
}

type ReferralsHomeV1EntryPointCta struct {
	// text for the cta
	Text string `dynamic:"true"`
	// font color of the cta
	FontColor string `dynamic:"true"`
	// icon to be shown on the right of the cta
	IconUrl string `dynamic:"true"`
	// background color of the cta
	BgColor string `dynamic:"true"`
	// cta shadow height
	ShadowHeight int32 `dynamic:"true"`
	// cta shadow color
	ShadowColor string `dynamic:"true"`
	// message to share if it's a share-code cta
	ShareFiniteCodeMessage string `dynamic:"true"`
	// deeplink for the cta if it's a redirection cta
	DeeplinkActionScreen uint32 `dynamic:"true"`
}

type ReferralsLandingBanner struct {
	// field to decide whether the banner is supported to be shown or not
	ShowBanner bool `dynamic:"true"`
	// fields for the banner's collapsed state

	Title      string        `dynamic:"true"`
	TitleColor string        `dynamic:"true"`
	ImageUrl   string        `dynamic:"true"`
	BgColor    string        `dynamic:"true"`
	Cta        *ReferralsCta `dynamic:"true"`

	// fields for the banner's expanded state, i.e. when you tap on it

	ExpandedStateTitle      string `dynamic:"true"`
	ExpandedStateTitleColor string `dynamic:"true"`
	ExpandedStateDesc       string `dynamic:"true"`
	ExpandedStateDescColor  string `dynamic:"true"`
	ExpandedStateIconUrl    string `dynamic:"true"`
	// CTA to be shown at the bottom of the expanded state
	ExpandedStateCta *ReferralsCta `dynamic:"true"`
	// heading for the info to be shown first (separate field instead of slice till dynamic config supports slice/array)
	ExpandedStateHeading1 string `dynamic:"true"`
	// info under heading1 separated by newline (using string instead of slice till dynamic config supports slice/array)
	ExpandedStateInfos1 string `dynamic:"true"`
	// heading for the info to be shown second (separate field instead of slice till dynamic config supports slice/array)
	ExpandedStateHeading2 string `dynamic:"true"`
	// info under heading2 separated by newline (using string instead of slice till dynamic config supports slice/array)
	ExpandedStateInfos2 string `dynamic:"true"`
}

type ReferralsCta struct {
	Text      string `dynamic:"true"`
	TextColor string `dynamic:"true"`
	BgColor   string `dynamic:"true"`
	ImageUrl  string `dynamic:"true"`
	Deeplink  *deeplinkPb.Deeplink
	// is visible tell whether the CTA is to be shown or not. Though, the functionality decision relies on app
	IsVisible bool `dynamic:"true"`
	// IsEnabled tells whether the CTA is to be sent in response to client or not, i.e. whether the CTA exists or not
	IsEnabled bool `dynamic:"true"`
}

// Map to store the colour codes for the referral section in invite friends screen
type ReferralsColourMap struct {
	NameColour             string
	OnboardingStatusColour string
}

type UpiPinSetMessage struct {
	EtbPinSetTitle string
	EtbPinSetBody  string
}

type ConnectedAccount struct {
	AccountDiscoveryTitleText                   string        `dynamic:"true"`
	AccountDiscoverySubtitleText                string        `dynamic:"true"`
	V2FlowParams                                *V2FlowParams `dynamic:"true"`
	FinvuAccountDiscoveryTimeoutSeconds         int32         `dynamic:"true"`
	OnemoneyAccountDiscoveryTimeoutSeconds      int32         `dynamic:"true"`
	DataPullStatusNextPoll                      int32         `dynamic:"true"`
	FiBankIconUrl                               string        `dynamic:"true"`
	BlacklistIfscForFip                         []string
	DisableWealthOnboardingMinAndroidVersion    uint32               `dynamic:"true"`
	DisableWealthOnboardingMinIosVersion        uint32               `dynamic:"true"`
	AccountTxnNotVisibleTitle                   string               `dynamic:"true"`
	AccountTxnNotVisibleBody                    string               `dynamic:"true"`
	MaxDataPullStatusPollAttempts               int32                `dynamic:"true"`
	FiuId                                       string               `dynamic:"true"`
	FinvuDisconnectUrl                          string               `dynamic:"true"`
	MinVersionCheckForNewBanks                  uint32               `dynamic:"true"`
	JupiterMetaData                             *JupiterMetaData     `dynamic:"true"`
	IsConnectedAccountEnabled                   bool                 `dynamic:"true"`
	HomeBannerCutoffDays                        uint32               `dynamic:"true"`
	HomeEntryPoint                              *CaEntryPoint        `dynamic:"true"`
	SearchEntryPoint                            *CaEntryPoint        `dynamic:"true"`
	AnalyserEntryPoint                          *CaEntryPoint        `dynamic:"true"`
	ProfileEntryPoint                           *CaEntryPoint        `dynamic:"true"`
	AccountManagerEntryPoint                    *CaEntryPoint        `dynamic:"true"`
	AllTransactionsEntryPoint                   *CaEntryPoint        `dynamic:"true"`
	BottomSheetOnDisconnectMinAndroidVersion    int                  `dynamic:"true"`
	BottomSheetOnDisconnectMinIosVersion        int                  `dynamic:"true"`
	SearchBannerEntryPoint                      *CaEntryPoint        `dynamic:"true"`
	MinimumDurationRequiredToPermitDisconnect   time.Duration        `dynamic:"true"`
	DisconnectBottomSheetMinVersionIos          int                  `dynamic:"true"`
	DisconnectBottomSheetMinVersionAndroid      int                  `dynamic:"true"`
	DeleteBottomSheetMinVersionIos              int                  `dynamic:"true"`
	DeleteBottomSheetMinVersionAndroid          int                  `dynamic:"true"`
	FinvuAsyncDiscovery                         *FinvuAsyncDiscovery `dynamic:"true"`
	SegmentIds                                  *SegmentIds
	BalanceDashboardRenewalPopupDismissDuration time.Duration `dynamic:"true"`
	EnableConsentRenewalSegmentNonProdTest      bool          `dynamic:"true"`
	// InvalidateConnectFiToFiFlowRetries field belongs to Connect Fi to Fi flow to connect Fi Federal savings bank account,
	// this represents time after which the number of times user has tried the flow gets invalidated, this KV pair logic is
	// maintained at client side
	InvalidateConnectFiToFiFlowRetries time.Duration `dynamic:"true"`
	// MaxRetryAllowedForFiToFiFlowFailureCase field belongs to Connect Fi to Fi flow to connect Fi Federal savings bank account,
	// this represents maximum number of times user can retry flow after which Fi To Fi bottom sheet will not be shown,
	// this logic is maintained at client side
	MaxRetryAllowedForFiToFiFlowFailureCase uint32 `dynamic:"true"`
	// MaxRetryAllowedForFiToFiAccountDiscoveryFailureCase field belongs to Connect Fi to Fi flow to connect Fi Federal savings bank account,
	// this represents the number of retries allowed for account discovery in case of failure
	MaxRetryAllowedForFiToFiAccountDiscoveryFailureCase uint32 `dynamic:"true"`
	// MaxAllowedConsentHandleStatusPoll represents maximmum number of times, the get consent handle status can be polled.
	MaxAllowedConsentHandleStatusPoll int32 `dynamic:"true"`
	// ConsentHandleStatusNextPollDuration represents after what duration consent handle status can be again be polled.
	ConsentHandleStatusNextPollDuration          time.Duration         `dynamic:"true"`
	EnableAccountManagerConsentRenewalEntryPoint bool                  `dynamic:"true"`
	DisplayScreenOptions                         *DisplayScreenOptions `dynamic:"true"`
	ForceFinvuForIndianSecurities                bool                  `dynamic:"true"`
	SDKFlowUiParams                              *SDKFlowUiParams      `dynamic:"true"`
	// This is the minimum wait time in seconds before the user can retry sending OTP
	AutoReadTimeoutAllDiscoveredFipsOtp int32 `dynamic:"true"`
	// TODO: Convert this to a map of CAFlowName to int32 if multiple cases come up
	AutoReadTimeoutForIndStocksDiscoveredFipsOtp int32 `dynamic:"true"`
}

type DisplayScreenOptions struct {
	// map of caEnumPb.CAFlowName to BenefitsScreenOptions
	BenefitsScreenOptionsMap        map[string]*BenefitsScreenOptions         `dynamic:"true"`
	AccountDiscoveryScreenOptionMap map[string]*AccountDiscoveryScreenOptions `dynamic:"true"`
	NoAccDiscoveryScreenOptionsMap  map[string]*NoAccDiscoveryScreenOptions   `dynamic:"true"`
}

type SDKFlowUiParams struct {
	UnifiedFlowParams *UnifiedFlowParams `dynamic:"true"`
}

type UnifiedFlowParams struct {
	FlowHeader                       string `dynamic:"true"`
	ProgressBarStatus                int32  `dynamic:"true"`
	DisplayProgressBar               bool   `dynamic:"true"`
	AccountDiscoverySearchingText    string `dynamic:"true"`
	ConnectDiscoveredAccountsCtaText string `dynamic:"true"`
	RetryCtaText                     string `dynamic:"true"`
	SkipFailedAccountsCtaText        string `dynamic:"true"`
	NoAccountsFoundTitle             string `dynamic:"true"`
	NoAccountsFoundSubTitle          string `dynamic:"true"`
	NoAccountFoundCtaText            string `dynamic:"true"`
	NoAccountsFoundIconUrl           string `dynamic:"true"`
}

type NoAccDiscoveryScreenOptions struct {
	TitleText               string `dynamic:"true"`
	SubTitleText            string `dynamic:"true"`
	FipIcon                 string `dynamic:"true"`
	FipListNotDiscoveryText string `dynamic:"true"`
	ProceedDeeplinkText     string `dynamic:"true"`
}

type AccountDiscoveryScreenOptions struct {
	Title                    *IconTextComponent `dynamic:"true"`
	SubTitle                 *IconTextComponent `dynamic:"true"`
	CtaText                  *IconTextComponent `dynamic:"true"`
	SubTitleSearchingText    *IconTextComponent `dynamic:"true"`
	LoadingText              *IconTextComponent `dynamic:"true"`
	RegisterMoreAccountsText *IconTextComponent `dynamic:"true"`
}

type BenefitsScreenOptions struct {
	// If both image and lottie are present lottie takes priority
	CloseIconImage  *VisualElementImage  `dynamic:"true"`
	CloseIconLottie *VisualElementLottie `dynamic:"true"`
	BackgroundColor string               `dynamic:"true"`
	FipContainer    *FipContainer        `dynamic:"true"`
	Title           *IconTextComponent   `dynamic:"true"`
	Desc            *IconTextComponent   `dynamic:"true"`
	Benefits        []*IconTextComponent
	WealthTnc       *CheckboxItem `dynamic:"true"`
	FiLiteTnc       *CheckboxItem `dynamic:"true"`
	Cta             *CtaButton    `dynamic:"true"`
}

type CheckboxItem struct {
	Id          string `dynamic:"true"`
	IsChecked   bool   `dynamic:"true"`
	DisplayText *Text  `dynamic:"true"`
}

type FipContainer struct {
	BackgroundColor string             `dynamic:"true"`
	Title           *IconTextComponent `dynamic:"true"`
	SubTitle        *IconTextComponent `dynamic:"true"`
	Fips            []*VerticalIconTextComponent
}

type IconTextComponent struct {
	Texts                   []*Text
	LeftImgTxtPadding       int32                       `dynamic:"true"`
	RightImgTxtPadding      int32                       `dynamic:"true"`
	ContainerProperties     *ContainerPropertiesDynamic `dynamic:"true"`
	LeftVisualElementImage  *VisualElementImage         `dynamic:"true"`
	RightVisualElementImage *VisualElementImage         `dynamic:"true"`
}

type VerticalIconTextComponent struct {
	TopVisualElement    *VisualElementImage         `dynamic:"true"`
	BottomVisualElement *VisualElementImage         `dynamic:"true"`
	Text                *Text                       `dynamic:"true"`
	ContainerProperties *ContainerPropertiesDynamic `dynamic:"true"`
}

type ImagePropertiesDynamic struct {
	Url     string           `dynamic:"true"`
	Height  int32            `dynamic:"true"`
	Width   int32            `dynamic:"true"`
	Padding *AlignmentValues `dynamic:"true"`
}

type ContainerPropertiesDynamic struct {
	Height       int32            `dynamic:"true"`
	Width        int32            `dynamic:"true"`
	Padding      *AlignmentValues `dynamic:"true"`
	CornerRadius int32            `dynamic:"true"`
	BgColor      string           `dynamic:"true"`
	BorderColor  string           `dynamic:"true"`
	BorderWidth  int32            `dynamic:"true"`
}

type VisualElementProperties struct {
	Width  int32 `dynamic:"true"`
	Height int32 `dynamic:"true"`
}

type VisualElementImage struct {
	// If both url and data string is present data string will take priority
	Url string `dynamic:"true"`
	// If populated and currently in fc-fp post conversion phase then overwrites IconUrl
	FcFpPostConversionUrl string                   `dynamic:"true"`
	DataString            string                   `dynamic:"true"`
	Properties            *VisualElementProperties `dynamic:"true"`
}

type VisualElementLottie struct {
	LottieJsonString string                   `dynamic:"true"`
	LottieUrl        string                   `dynamic:"true"`
	RepeatCount      int32                    `dynamic:"true"`
	Properties       *VisualElementProperties `dynamic:"true"`
}

type SegmentIds struct {
	ConsentRenewal string
}

type FinvuAsyncDiscovery struct {
	AndroidMinVersion uint32 `dynamic:"true"`
	IosMinVersion     uint32 `dynamic:"true"`
}

type V2FlowParams struct {
	UseV2Flow                             bool   `dynamic:"true"`
	AccountDiscoveryTitleText             string `dynamic:"true"`
	AccountDiscoverySubtitleText          string `dynamic:"true"`
	AccountDiscoverySubtitleSearchingText string `dynamic:"true"`
	AccountDiscoveryCtaText               string `dynamic:"true"`
	MinVersionAndroid                     uint32 `dynamic:"true"`
	MinVersionIos                         uint32 `dynamic:"true"`
	AccountDiscoveryLoadingText           string `dynamic:"true"`
	RegisterOtherAccountsText             string `dynamic:"true"`
}

type CaEntryPoint struct {
	Enabled bool   `dynamic:"true"`
	Text    string `dynamic:"true"`
	LogoUrl string `dynamic:"true"`
}

type ConnectedAccountUserGroupParams struct {
	// bool to decide if connected account is restricted to user groups
	IsConnectedAccountRestricted bool
	// if connected account is restricted, the list of user groups that have access to connected accounts
	AllowedUserGrps []commontypes.UserGroup
}

type Tiering struct {
	NewTiersConstraints                      *NewTiersConstraints   `dynamic:"true"`
	TieringFeature                           *MinVersionConstraints `dynamic:"true"`
	HeroBenefits                             *MinVersionConstraints `dynamic:"true"`
	TierIntroduction                         *TierIntroduction      `dynamic:"true"`
	MaxNumberOfManualUpgradeRetries          int32
	SegmentIds                               *TieringSegmentIds        `dynamic:"true"`
	DisplayConfig                            *DisplayConfig            `dynamic:"true"`
	OnbAddFundsSuccessVersionConstraints     *cfg.PlatformVersionCheck `dynamic:"true"`
	IsTierAllPlansDropOffFeedbackFlowEnabled bool                      `dynamic:"true"`
	EnableSalaryHighConfidenceNotch          bool                      `dynamic:"true"`
	SalaryHighConfNotchEnabledTimestamp      time.Time                 `dynamic:"true"`
	TierTimeRangeFilterDepth                 time.Duration             `dynamic:"true"`
	ForexRefundFilterDepth                   time.Duration             `dynamic:"true"`
	EarnedBenefitsHistoryDepth               time.Duration             `dynamic:"true"`
	// threshold after which we nudge the user to spend the fi coins in tiering earned benefits page
	SpendFiCoinsThreshold int32 `dynamic:"true"`
	// debit card order charge is fetched only if dc is ordered within this time window
	FetchDebitCardOrderChargesTimeWindow time.Duration `dynamic:"true"`
	// AMB screen configuration
	AMBScreen *AMBScreenConfig `dynamic:"true"`
	// fails components in earned benefits screen for these actors
	// used for qa testing to simulate rpc failure for given actors
	FailEarnedBenefitComponentsForActors []string `dynamic:"true"`
	// map of segment id to campaign details
	HomeNotchCampaigns map[string]*HomeNotchCampaignDetails
	// used to enable Hero V3 for experiment
	EnableHeroV3Component                         bool                                    `dynamic:"true" ,quest:"variable"`
	HeroBenefitsV3Constraints                     *MinVersionConstraints                  `dynamic:"true"`
	SegmentIdsForInfiniteBoost                    []string                                `dynamic:"true"`
	SegmentIdsForPlanBoost                        []string                                `dynamic:"true"`
	MinBalanceRequiredForInfinite3PercentCashback int64                                   `dynamic:"true"`
	MinBalanceRequiredForPlus1PercentCashback     int64                                   `dynamic:"true"`
	RewardIdForInfiniteBoost                      string                                  `dynamic:"true"`
	RewardIdForPlusBoost                          string                                  `dynamic:"true"`
	TierV2ThemeColorMap                           *TierV2ThemeColorMap                    `dynamic:"true"`
	TierV2ColorMap                                *TierV2ColorMap                         `dynamic:"true"`
	TierAllPlansV2ReleaseParams                   *TierAllPlansV2ReleaseParams            `dynamic:"true"`
	TierAllPlansV2ConfigParams                    *TierAllPlansV2ConfigParams             `dynamic:"true"`
	TierSuccessScreenV2UiParams                   *TierSuccessScreenV2UiParams            `dynamic:"true"`
	TierDropOffBottomSheet                        *TierDropOffBottomSheet                 `dynamic:"true"`
	DeltaBalanceParams                            *DeltaBalanceParams                     `dynamic:"true"`
	SegmentIdsForAMB                              []string                                `dynamic:"true"`
	ExcludeSegmentsFromAMBScreenEntrypoint        *ExcludeSegmentsFromAMBScreenEntrypoint `dynamic:"true"`
	AMBConfig                                     *AMBConfig                              `dynamic:"true"`
}

type ExcludeSegmentsFromAMBScreenEntrypoint struct {
	ExcludedSegmentIds []string `dynamic:"true"`
	StartTime          int64    `dynamic:"true"`
	EndTime            int64    `dynamic:"true"`
}

type AMBConfig struct {
	ExcludedTiers                  []string                           `dynamic:"true"`
	HomeFooterTickerParams         *AMBHomeFooterTickerParams         `dynamic:"true"`
	AccountSummaryEntrypointConfig *AMBAccountSummaryEntryPointConfig `dynamic:"true"`
	MonthlyBenefitsEntryPoint      *AMBMonthlyBenefitsEntrypoint      `dynamic:"true"`
}

type AMBMonthlyBenefitsEntrypoint struct {
	Default   *IconTextComponent `dynamic:"true"`
	Shortfall *IconTextComponent `dynamic:"true"`
}

type AMBHomeFooterTickerParams struct {
	RegularShortfall    *FooterTickerParams `dynamic:"true"`
	NonRegularShortfall *FooterTickerParams `dynamic:"true"`
	RegularGeneric      *FooterTickerParams `dynamic:"true"`
	NonRegularGeneric   *FooterTickerParams `dynamic:"true"`
}

type FooterTickerParams struct {
	DisplayText string   `dynamic:"true"`
	ImageUrl    string   `dynamic:"true"`
	BgColor     *BgColor `dynamic:"true"`
}

type DeltaBalanceParams struct {

	// params for profile screen
	BalanceForPitchingPrimeForBasic    int64  `dynamic:"true"`
	TextForPitchingPrimeForBasic       string `dynamic:"true"`
	BalanceForPitchingInfiniteForBasic int64  `dynamic:"true"`
	TextForPitchingInfiniteForBasic    string `dynamic:"true"`
	TextForPitchingPlusForBasic        string `dynamic:"true"`
	BalanceForPitchingInfiniteForPlus  int64  `dynamic:"true"`
	TextForPitchingInfiniteForPlus     string `dynamic:"true"`
	BalanceForPitchingPrimeForPlus     int64  `dynamic:"true"`
	TextForPitchingPrimeForPlus        string `dynamic:"true"`
	BalanceForPitchingPrimeForInfinite int64  `dynamic:"true"`
	TextForPitchingPrimeForInfinite    string `dynamic:"true"`

	// params for plans v2 cta
	BalanceDiffFirstForPrime            int64  `dynamic:"true"`
	TextForBalanceDiffFirstForPrime     string `dynamic:"true"`
	BalanceDiffSecondForPrime           int64  `dynamic:"true"`
	TextForBalanceDiffSecondForPrime    string `dynamic:"true"`
	BalanceDiffThirdForPrime            int64  `dynamic:"true"`
	TextForBalanceDiffThirdForPrime     string `dynamic:"true"`
	TextForBalanceDiffFourthForPrime    string `dynamic:"true"`
	BalanceDiffFirstForInfinite         int64  `dynamic:"true"`
	TextForBalanceDiffFirstForInfinite  string `dynamic:"true"`
	BalanceDiffSecondForInfinite        int64  `dynamic:"true"`
	TextForBalanceDiffSecondForInfinite string `dynamic:"true"`
	TextForBalanceDiffThirdForInfinite  string `dynamic:"true"`

	BalanceForPlus     int64  `dynamic:"true"`
	BalanceForInfinite int64  `dynamic:"true"`
	BalanceForPrime    int64  `dynamic:"true"`
	ActionText         string `dynamic:"true"`
}

type TierV2ThemeColorMap struct {
	TierV2ThemeColors map[string]*TierV2ThemeColor `dynamic:"true"`
}

type TierV2ThemeColor struct {
	HeaderOverlayBgColor *BgColor `dynamic:"true"`
	ContentBgColor       *BgColor `dynamic:"true"`
}

type TierV2ColorMap struct {
	TierV2Colors map[string]*TierV2Color `dynamic:"true"`
}

type TierV2Color struct {
	HeaderBgColor *RadialGradient `dynamic:"true"`
	Theme         string          `dynamic:"true"`
}

type TierAllPlansV2ConfigParams struct {
	Title                 *Text `dynamic:"true"`
	TierPlansOrdering     []string
	TierPlans             map[string]*TierAllPlansV2TierPlans
	ShouldShowEntryBanner bool `dynamic:"true"`
}

type TierAllPlansV2TierPlans struct {
	IconURL              string                  `dynamic:"true"`
	LoaderIconUrl        string                  `dynamic:"true"`
	SwipeIconURL         string                  `dynamic:"true"`
	PlanCard             *TierAllPlansV2PlanCard `dynamic:"true"`
	PlanBenefitsOrdering []string
	PlanBenefits         map[string]*TierAllPlansV2PlanBenefit
	BenefitsBottomSheet  *BenefitsBottomSheet `dynamic:"true"`
	Cta                  *deeplinkCfg.CTA
}

type BenefitsBottomSheet struct {
	DetailedBenefitsBottomSheet *DetailedBenefitsBottomSheet `dynamic:"true"`
	CashbackDetailsBottomSheet  *CashbackDetailsBottomSheet  `dynamic:"true"`
}

type DetailedBenefitsBottomSheet struct {
	Title                                    *Text                                       `dynamic:"true"`
	BgColor                                  *BgColor                                    `dynamic:"true"`
	DetailedBenefitsBottomSheetTilesOrdering []string                                    `dynamic:"true"`
	DetailedBenefitsBottomSheetTiles         map[string]*DetailedBenefitsBottomSheetTile `dynamic:"true"`
	Cta                                      *deeplinkCfg.CTA                            `dynamic:"true"`
}

type DetailedBenefitsBottomSheetTile struct {
	Title           *Text  `dynamic:"true"`
	TopIconUrl      string `dynamic:"true"`
	Subtitle        *Text  `dynamic:"true"`
	MiddleIcon      *VisualElementImage
	BackgroundColor *BgColor `dynamic:"true"`
	TileHeight      int32    `dynamic:"true"`
	TileWidth       int32    `dynamic:"true"`
}

type CashbackDetailsBottomSheet struct {
	BgColor                                 *BgColor                                   `dynamic:"true"`
	CashbackDetailsBottomSheetTilesOrdering []string                                   `dynamic:"true"`
	CashbackDetailsBottomSheetTiles         map[string]*CashbackDetailsBottomSheetTile `dynamic:"true"`
	Cta                                     *deeplinkCfg.CTA                           `dynamic:"true"`
}

type CashbackDetailsBottomSheetTile struct {
	Title            *Text  `dynamic:"true"`
	TopIconUrl       string `dynamic:"true"`
	Subtitle         *Text  `dynamic:"true"`
	HorizontalBorder *Text  `dynamic:"true"`
	TileHeight       int32  `dynamic:"true"`
	TileWidth        int32  `dynamic:"true"`
}

type TierAllPlansV2PlanCard struct {
	BgColor                 *BgColor                `dynamic:"true"`
	Title                   *Text                   `dynamic:"true"`
	RightVisualElementImage string                  `dynamic:"true"`
	BorderColor             *BgColor                `dynamic:"true"`
	PlanInfo                *TierAllPlansV2PlanInfo `dynamic:"true"`
	BottomSheetType         string                  `dynamic:"true"`
}

type TierAllPlansV2PlanInfo struct {
	BgColor           *BgColor `dynamic:"true"`
	BorderColor       *BgColor `dynamic:"true"`
	ShadowColor       *BgColor `dynamic:"true"`
	InfoTilesOrdering []string
	InfoTiles         map[string]*TierAllPlansV2PlanCardInfoTiles
	DividerColor      *BgColor `dynamic:"true"`
}

type TierAllPlansV2PlanCardInfoTiles struct {
	Title     *IconTextComponent `dynamic:"true"`
	RupeeIcon *Text              `dynamic:"true"`
	Rewards   *Text              `dynamic:"true"`
	RewardsOn *Text              `dynamic:"true"`
}

type TierAllPlansV2Cta struct {
	Text     string    `dynamic:"true"`
	Deeplink *DeepLink `dynamic:"true"`
	Status   string    `dynamic:"true"`
}

type TierAllPlansV2PlanBenefit struct {
	BgColor         *BgColor                `dynamic:"true"`
	Title           *IconTextComponent      `dynamic:"true"`
	Benefit         *TierAllPlansV2Benefits `dynamic:"true"`
	BenefitType     string                  `dynamic:"true"`
	BottomSheetType string                  `dynamic:"true"`
}

type TierAllPlansV2Benefits struct {
	BenefitCard     *TierAllPlansV2BenefitCard     `dynamic:"true"`
	EntryBanner     *TierAllPlansV2EntryBanner     `dynamic:"true"`
	InfoBanner      *TierAllPlansV2InfoBanner      `dynamic:"true"`
	LearnMoreBanner *TierAllPlansV2LearnMoreBanner `dynamic:"true"`
}

type TierAllPlansV2BenefitCard struct {
	BenefitTilesRowsOrdering []string
	BenefitTileRows          map[string]*TierAllPlansV2BenefitTileRow
}

type TierAllPlansV2BenefitTileRow struct {
	BenefitTileOrdering []string
	BenefitTile         map[string]*TierAllPlansV2BenefitTile
}

type TierAllPlansV2BenefitTile struct {
	BgColor     *BgColor                     `dynamic:"true"`
	BorderColor *BgColor                     `dynamic:"true"`
	Icon        *typesPkg.VisualElementImage `dynamic:"true"`
	Title       *Text                        `dynamic:"true"`
	Subtitle    *Text                        `dynamic:"true"`
	Style       string                       `dynamic:"true"`
	Deeplink    *deeplinkCfg.Deeplink
}

type TierAllPlansV2EntryBanner struct {
	SmallEntryBanner map[string]*TierAllPlansV2SmallEntryBanner `dynamic:"true"`
	LargeEntryBanner map[string]*TierAllPlansV2LargeEntryBanner `dynamic:"true"`
}

type TierAllPlansV2SmallEntryBanner struct {
	EntryPoint string              `dynamic:"true"`
	BgColor    *BgColor            `dynamic:"true"`
	LeftIcon   *VisualElementImage `dynamic:"true"`
	Content    *Text               `dynamic:"true"`
	RightCta   *CtaButton          `dynamic:"true"`
}

type TierAllPlansV2LargeEntryBanner struct {
	EntryPoint  string              `dynamic:"true"`
	BgColor     *BgColor            `dynamic:"true"`
	TopContent  *CtaButton          `dynamic:"true"`
	Content     *Text               `dynamic:"true"`
	RightCta    *CtaButton          `dynamic:"true"`
	BannerImage *VisualElementImage `dynamic:"true"`
}

type TierAllPlansV2InfoBanner struct {
	BgColor *BgColor           `dynamic:"true"`
	Title   *IconTextComponent `dynamic:"true"`
	Content *Text              `dynamic:"true"`
}

type TierAllPlansV2LearnMoreBanner struct {
	Banners map[string]*VerticalIconTextComponent
}

type TierAllPlansV2ReleaseParams struct {
	ReleaseConstraints *MinVersionConstraints `dynamic:"true"`
}

type TierSuccessScreenV2UiParams struct {
	TierPlans   map[string]*TierSuccessScreenV2TierPlans `dynamic:"true"`
	PillarIcons map[string]*typesPkg.VisualElementImage  `dynamic:"true"`
}

type TierSuccessScreenV2TierPlans struct {
	PillarOverlayColor *BgColor                     `dynamic:"true"`
	Title              *typesPkg.Text               `dynamic:"true"`
	SubTitle           *typesPkg.Text               `dynamic:"true"`
	PlanImage          *typesPkg.VisualElementImage `dynamic:"true"`
	PillarPlanImageGap int32                        `dynamic:"true"`
	PillarTheme        string                       `dynamic:"true"`
	CtaText            string                       `dynamic:"true"`
}

type TierDropOffBottomSheet struct {
	ShouldShowUSStocks     bool `dynamic:"true"`
	ShouldShowFixedDeposit bool `dynamic:"true"`
	ShouldShowAddFunds     bool `dynamic:"true"`
}

type NewTiersConstraints struct {
	AaSalary *cfg.PlatformVersionCheck `dynamic:"true"`
}

type HomeNotchCampaignDetails struct {
	StartDate   time.Time
	EndDate     time.Time
	DefaultCopy string
	// Campaign day to copy map
	NotchCopyMap   map[time.Duration]string
	DeeplinkScreen string
}

type DisplayConfig struct {
	// Map with tier.String() and with value OnbAddFundsSuccessScreenOptions
	OnbAddFundsSuccessScreenOptionsMap map[string]*OnbAddFundsSuccessScreenOptions `dynamic:"true"`
}

type OnbAddFundsSuccessScreenOptions struct {
	BgColor            *BgColor                           `dynamic:"true"`
	SuccessMessage     *typesPkg.Text                     `dynamic:"true"`
	TierLogo           *typesPkg.VisualElementImage       `dynamic:"true"`
	SocialProofElement *VisualElementTitleSubtitleElement `dynamic:"true"`
	BenefitsContainer  *OnbAddFundsBenefitsContainer      `dynamic:"true"`
	CtaText            string                             `dynamic:"true"`
}

type OnbAddFundsBenefitsContainer struct {
	Title        *typesPkg.Text `dynamic:"true"`
	BgColor      *BgColor       `dynamic:"true"`
	BenefitsList []*IconTextComponent
}

type VisualElementTitleSubtitleElement struct {
	VisualElement   *typesPkg.VisualElementImage `dynamic:"true"`
	TitleText       *typesPkg.Text               `dynamic:"true"`
	SubtitleText    *typesPkg.Text               `dynamic:"true"`
	BackgroundColor string                       `dynamic:"true"`
}

// todo: refactor to get it from config via a method
type BgColor struct {
	BgColorBlockColor           string           `dynamic:"true"`
	BgColorLinearGradientStart  string           `dynamic:"true"`
	BgColorLinearGradientEnd    string           `dynamic:"true"`
	BgColorLinearGradientDegree map[string]int32 `dynamic:"true"`
}

type TieringSegmentIds struct {
	StandardPlusAndUnAssigned        string `dynamic:"true"`
	SalaryHighConfidenceNotchSegment string `dynamic:"true"`
	AaSalary                         string `dynamic:"true"`
}

type MinVersionConstraints struct {
	MinVersionAndroid uint32 `dynamic:"true"`
	MinVersionIos     uint32 `dynamic:"true"`
}

type TierIntroduction struct {
	// Min version constraints for tier launch animation
	ReleaseConstraints *MinVersionConstraints `dynamic:"true"`
	// Play the animation only after an inactivity of these many seconds
	LaunchAnimationInactivitySeconds int32 `dynamic:"true"`
}

// AMBScreenConfig contains configuration for the Average Monthly Balance screen
type AMBScreenConfig struct {
	// Colors used in the AMB screen
	Colors *AMBColors `dynamic:"true"`
	// Dimensions used in the AMB screen
	Dimensions *AMBDimensions `dynamic:"true"`
	// Default image URL used in the AMB screen
	DefaultImageURL string `dynamic:"true"`
	// Target AMB values for different tiers
	TargetAMB map[string]float64 `dynamic:"true"`
	// Cashback percentages for different tiers
	CashbackPercentage map[string]string `dynamic:"true"`
	// Banner cashback percentages for different tiers
	BannerCashbackPercentage map[string]string `dynamic:"true"`
	// Text content for the AMB screen
	TextContent *AMBTextContent `dynamic:"true"`

	Images             *AMBImages `dynamic:"true"`
	MaxShortfallAmount int64      `dynamic:"true"`
	// Max shortfall amount for different tiers
	TierShortfallAmountMap map[string]int32 `dynamic:"true"`
	// AMB Ratio Threshold for showing upsell pro-tip to user
	AMBRatioThresholdForUpsellTip map[string]float64 `dynamic:"true"`
	// Tier upgrade configuration for TIER_FI_BASIC users
	TierUpgrade *AMBTierUpgrade `dynamic:"true"`
}

// AMBTierUpgrade defines tier upgrade thresholds and mappings for TIER_FI_BASIC users
type AMBTierUpgrade struct {
	PlusThreshold     int64 `dynamic:"true"` // AMB threshold for Plus tier (25k)
	InfiniteThreshold int64 `dynamic:"true"` // AMB threshold for Infinite tier (50k)
}

type AMBImages struct {
	PlusIcon              string            `dynamic:"true"`
	Announcement          string            `dynamic:"true"`
	Info                  string            `dynamic:"true"`
	Warning               string            `dynamic:"true"`
	Wallet                string            `dynamic:"true"`
	Book                  string            `dynamic:"true"`
	Regular               string            `dynamic:"true"`
	Play                  string            `dynamic:"true"`
	DownArrow             string            `dynamic:"true"`
	UpArrow               string            `dynamic:"true"`
	SalaryA               string            `dynamic:"true"`
	Cash                  string            `dynamic:"true"`
	Alert                 string            `dynamic:"true"`
	AMBAverageCalculation string            `dynamic:"true"`
	Calendar              string            `dynamic:"true"`
	WhiteCheck            string            `dynamic:"true"`
	WarningError          string            `dynamic:"true"`
	InfoGreen             string            `dynamic:"true"`
	AnnouncementGreen     string            `dynamic:"true"`
	AMBOnTrackImagesMap   map[string]string `dynamic:"true"`
	AMBOnTrackLottie      string            `dynamic:"true"`
	// TIER_FI_BASIC specific images
	BasicTierPromoBanner  string `dynamic:"true"`
	BasicTierArrowUpRight string `dynamic:"true"`
	BasicTierUsersIcon    string `dynamic:"true"`
	// Tier-specific promotional banners based on recommended tier
	BasicTierPromoBannerMap map[string]string `dynamic:"true"`
}

// AMBColors contains color values used in the AMB screen
type AMBColors struct {
	White              string            `dynamic:"true"`
	Primary            string            `dynamic:"true"`
	Secondary          string            `dynamic:"true"`
	Text               string            `dynamic:"true"`
	Subtle             string            `dynamic:"true"`
	Warning            string            `dynamic:"true"`
	Subtitle           string            `dynamic:"true"`
	Banner             string            `dynamic:"true"`
	Progress           string            `dynamic:"true"`
	Track              string            `dynamic:"true"`
	Plus               string            `dynamic:"true"`
	Alert              string            `dynamic:"true"`
	Green              string            `dynamic:"true"`
	Content            string            `dynamic:"true"`
	AMBOnTrackColorMap map[string]string `dynamic:"true"`
	// TIER_FI_BASIC specific colors
	BasicTierInfoTextColor string `dynamic:"true"`
}

// AMBDimensions contains dimension values used in the AMB screen
type AMBDimensions struct {
	CornerRadius int32 `dynamic:"true"`
	Padding      int32 `dynamic:"true"`
	Margin       int32 `dynamic:"true"`
}

// AMBTextContent contains text content used in the AMB screen
type AMBTextContent struct {
	AddMoneyButtonText       string                          `dynamic:"true"`
	WhatsAMBText             string                          `dynamic:"true"`
	YourAMBHistoryText       string                          `dynamic:"true"`
	BannerText               string                          `dynamic:"true"`
	RewardsAtStakeText       string                          `dynamic:"true"`
	AMBMaintainedText        string                          `dynamic:"true"`
	AMBMaintainedTextRegular string                          `dynamic:"true"`
	WhyMaintainAMBCharges    string                          `dynamic:"true"`
	WhyMaintainAMBRewards    string                          `dynamic:"true"`
	CurrentAMBText           string                          `dynamic:"true"`
	TargetAMBText            string                          `dynamic:"true"`
	LearningCenterText       string                          `dynamic:"true"`
	LearningItems            *LearningItems                  `dynamic:"true"`
	PossibleChargeText       string                          `dynamic:"true"`
	AvoidChargesText         string                          `dynamic:"true"`
	AvoidLossOfRewardsText   string                          `dynamic:"true"`
	AMBOutOfReachText        string                          `dynamic:"true"`
	AMBOutOfReachTextRegular string                          `dynamic:"true"`
	AMBPenaltyWarning        string                          `dynamic:"true"`
	AMBRewardsLossWarning    string                          `dynamic:"true"`
	RequiredAmbChargesText   string                          `dynamic:"true"`
	RequiredAmbRewardsText   string                          `dynamic:"true"`
	AMBOnTrackText           string                          `dynamic:"true"`
	AMBOnTrackProTipText     string                          `dynamic:"true"`
	AMBOnTrackProTipMap      map[string]*AMBOnTrackComponent `dynamic:"true"`
	// TIER_FI_BASIC specific text content
	BasicTierKnowMoreText   string `dynamic:"true"`
	BasicTierUpgradeText    string `dynamic:"true"`
	BasicTierInfoText       string `dynamic:"true"`
	BasicTierCurrentAMBText string `dynamic:"true"`
	BasicTierNoAMBText      string `dynamic:"true"`
	// Tier upgrade text templates based on AMB
	BasicTierKnowMoreTextTemplate string `dynamic:"true"`
	BasicTierUpgradeTextTemplate  string `dynamic:"true"`
	BasicTierInfoTextTemplate     string `dynamic:"true"`
}

type AMBOnTrackComponent struct {
	HtmlText       string `dynamic:"true"`
	Screen         string `dynamic:"true"`
	NextHigherTier string `dynamic:"true"`
}

type LearningItems struct {
	Titles       []string `dynamic:"true"`
	StorifiLinks []string `dynamic:"true"`
	Images       []string `dynamic:"true"`
}

type HomeParams struct {
	// Deprecated: in favour of AccountDiscrepancyInfo
	LedgerBalanceMismatchInfo string
	MissingTransactionsInfo   string
	// Deprecated: in favour of StaleBalanceDeeplink
	StaleBalanceInfo             string
	StaleBalanceTimeFormat       string
	AmountColourMap              *AmountColourMap
	AccountDiscrepancyInfo       string
	AccountDiscrepancyDeeplink   *deeplinkCfg.Deeplink
	StaleBalanceDeeplink         *deeplinkCfg.Deeplink
	RefreshAccountSummaryCTA     *CTA
	LastSyncedAccountSummaryInfo *CTA
}

type IPInterceptorParams struct {
	EnableIPInterceptor bool
	BlockedIPAddresses  []string
}

type Investment struct {
	// list of payment instruments for the AMCs we support. This can be used to filter out transactions made to the AMCs.
	AmcPiIds                                         []string
	InvestmentSummary                                *InvestedAmountSummary
	MinAndroidVersionToSupportOneTimeInvestment      uint32
	MinIOSVersionToSupportOneTimeInvestment          uint32
	IsWithdrawalAllowedWithoutOtp                    bool
	MinAndroidVersionToSupportMINKYCCheckForPurchase uint32 `dynamic:"true"`
	MinIOSVersionToSupportMINKYCCheckForPurchase     uint32 `dynamic:"true"`
	PreferredPaymentProtocolForOTI                   string `dynamic:"true"`
	MinAndroidVersionToUploadPan                     uint32 `dynamic:"true"`
	MinIosVersionToUploadPan                         uint32 `dynamic:"true"`
	ISKYCCheckOnPurchaseEnabledForIOS                bool   `dynamic:"true"`
	ISKYCCheckOnPurchaseEnabledForAndroid            bool   `dynamic:"true"`
	FundActivityManualInterventionSupport            *cfg.PlatformVersionCheck
	MinAndroidVersionForNextOnboardingStep           uint32 `dynamic:"true"`
	MinIOSVersionForNextOnboardingStep               uint32 `dynamic:"true"`
	CollectionInfoDeeplink                           *deeplinkCfg.Deeplink
	MFLandingPageCollectionIDForCuratedFunds         string                                 `dynamic:"true"`
	MFLandingPageCollectionNameForCuratedFunds       string                                 `dynamic:"true"`
	MFValuePropositionEmojiURL                       string                                 `dynamic:"true"`
	EnableAggregatorNotificationAlertFlag            bool                                   `dynamic:"true"`
	PromotionalContentUsecase                        string                                 `dynamic:"true"`
	InvestmentLandingInvestedNudgesParams            *InvestmentLandingInvestedNudgesParams `dynamic:"true"`
	AggregatorRecommendationID                       string                                 `dynamic:"true"`
	EnableOTIReminderPNFlag                          bool                                   `dynamic:"true"`
	AggregatorNotificationAlertFeatureConfig         *app.FeatureConfig                     `dynamic:"true"`
	MinAndroidVersionToSupportGraph                  uint32                                 `dynamic:"true"`
	MinIOSVersionToSupportGraph                      uint32                                 `dynamic:"true"`
	InvestmentLandingRecommendations                 *InvestmentLandingRecommendations      `dynamic:"true"`
	IsMFOneTimePurchaseAllowedWithoutOtp             bool
	IsMFNAVChartEnabled                              bool                         `dynamic:"true"`
	IsMFNAVChartTagsEnabled                          bool                         `dynamic:"true"`
	InvestmentHomeElementParams                      *InvestmentHomeElementParams `dynamic:"true"`
	MFNavChart                                       *MfNavChart
	MFReturnCalculator                               *MFReturnCalculator
	// flag enable fetch promotional ui element from Investment Svc
	UseDynamicUIElementSvc bool `dynamic:"true"`
	// this flag help in decide to show old banner
	// or show new banner using dynamic ui element
	// we need this since the banner would not display properly
	MinAndroidVersionHomeSupportForDynamicUISvc uint32
	// path to json file containing config for demat depository participant details
	DematDepositoryParticipantDetailsConfigJson string
	// path to json file containing indian stocks details
	IndianStocksDetailsConfigJson string
	// config for enabling SIP renewal support
	SIPRenewSupportConfig *SIPRenewSupportConfig `dynamic:"true"`
	// flag if set to true will use the individual holdings to calculate the total portfolio value
	// rather using the portfolio value in AA response
	IsIndianStocksPortfolioValueCalculated bool `dynamic:"true"`
	// flag to enable the investment digest tile dedupe
	EnableInvestmentDigestTileDedupe bool `dynamic:"true"`
	// flag to enable withdrawal calculations for hard lockin funds that have matured
	EnableHardLockinCalculator bool `dynamic:"true"`
}

type SIPRenewSupportConfig struct {
	MinAndroidVersionSIPRenewSupport uint32 `dynamic:"true"`
	MinIOSVersionSIPRenewSupport     uint32 `dynamic:"true"`
}

type MFReturnCalculator struct {
	// The maximum enter the user can enter in the UI.
	// Client will perform a validation using this when user enters the amount.
	MaximumAmount int
}

type MfNavChart struct {
	// represent web url to render graph
	WebUrl string
	// represent last update time stamp
	// so that client will invalid the cache and preload the graph
	LastUpdatedAt int64
}

type InvestmentHomeElementParams struct {
	InvestmentSegmentABExperimentParams map[string]*InvestmentSegmentABExperimentParams `dynamic:"true"`
}

type InvestmentSegmentABExperimentParams struct {
	SegmentExpression   string `dynamic:"true"`
	ABExperimentVariant string `dynamic:"true"`
	UseCase             string `dynamic:"true"`
}

type InvestmentLandingInvestedNudgesParams struct {
	// might contain some other params for home nudge widget

	// list of themes for home nudges, kept as map for dynamic config support
	InvestmentNudgeThemes map[string]*InvestmentLandingInvestedNudgeTheme `dynamic:"true"`
}

type InvestmentLandingInvestedNudgeTheme struct {
	Header             *Text        `dynamic:"true"`
	Title              *Text        `dynamic:"true"`
	SubTitle           *Text        `dynamic:"true"`
	BgColor            string       `dynamic:"true"`
	CheckmarkFillColor string       `dynamic:"true"`
	Shadow             *BlockShadow `dynamic:"true"`
	Action             *CTA         `dynamic:"true"`
	Tag                *Text        `dynamic:"true"`
}

type InvestmentLandingRecommendations struct {
	SegmentExpressionToRecommendationDetailsMap map[string]*RecommendationDetails `dynamic:"true"`
	// Fall back recommendation details to be used if we are not able to find value for a given segment-id key in SegmentExpressionToRecommendationDetailsMap
	FallBackRecommendationDetails *RecommendationDetails `dynamic:"true"`

	MFRecommendationIDToDetailsMap map[string]*MFRecommendationDetails `dynamic:"true"`
	// Fall back mf recommendation details to be used if we are not able to find value for a given recommendation-id key in MFRecommendationIDToDetailsMap
	FallBackMFRecommendationDetails *MFRecommendationDetails `dynamic:"true"`

	USStocksRecommendationIDToDetailsMap map[string]*USStocksRecommendationDetails `dynamic:"true"`
	// Fall back us stocks recommendation details to be used if we are not able to find value for a given recommendation-id key in USStocksRecommendationIDToDetailsMap
	FallBackUSStocksRecommendationDetails *USStocksRecommendationDetails `dynamic:"true"`

	HybridRecommendationIDToDetailsMap map[string]*HybridRecommendationDetails `dynamic:"true"`
	// Fall back recommendation details to be used if we are not able to find value for a given recommendation-id key in HybridRecommendationIDToDetailsMap
	FallBackHybridRecommendationDetails *HybridRecommendationDetails `dynamic:"true"`

	MultipleHybridRecommendationIDToDetailsMap map[string]*MultipleHybridRecommendationDetails `dynamic:"true"`
	// Fall back recommendation details to be used if we are not able to find value for a given recommendation-id key in MultipleHybridRecommendationIDToDetailsMap
	FallBackMultipleHybridRecommendationDetails *MultipleHybridRecommendationDetails `dynamic:"true"`

	MinIosVersionForV2Recommendations     int `dynamic:"true"`
	MinAndroidVersionForV2Recommendations int `dynamic:"true"`

	// Because of a client side bug in android, subtitle and info fields are interchanged. So, to launch hybrid recommendations, we
	// are swapping the subtitle and info fields of a recommendation card to nullify the effect.
	// ToDo(Junaid): Clean up after force upgrade. https://monorail.pointz.in/p/fi-app/issues/detail?id=51375
	AndroidVersionForSubtitleSwapFix int `dynamic:"true"`

	// check if we will use multiple recommendation flow
	EnableMultipleRecommendation bool `dynamic:"true"`
}

type MultipleHybridRecommendationDetails struct {
	Recommendations map[string]*HybridRecommendationDetails `dynamic:"true"`
}

type RecommendationDetails struct {
	RecommendationID string `dynamic:"true"`
	// 0 represents Hybrid Instruments i.e a combination of multiple instruments
	// 1 represents Mutual Funds
	// 2 represents P2P Jump
	// 3 represents Smart Deposits
	// 4 represents Fixed Deposits
	// 5 represents US Stocks
	// Only 0, 1 and 5 are the supported instruments currently for recommendations.
	InstrumentType int32 `dynamic:"true"`
}

type MFRecommendationDetails struct {
	// Either fill collectionID or filterID
	FilterIDs           []string `dynamic:"true"`
	CollectionID        string   `dynamic:"true"`
	HeaderDisplayString string   `dynamic:"true"`
	HeaderImageUrl      string   `dynamic:"true"`
	SubtitleString      string   `dynamic:"true"`
}

type USStocksRecommendationDetails struct {
	CollectionID        string `dynamic:"true"`
	HeaderDisplayString string `dynamic:"true"`
	HeaderImageUrl      string `dynamic:"true"`
	SubtitleString      string `dynamic:"true"`
}

type HybridRecommendationDetails struct {
	// Key of USStockRecommendationCards is the us stock id
	USStockRecommendationCards map[string]*USStockRecommendationCard `dynamic:"true"`
	// Key of MFRecommendationCards is the mutual funds id
	MFRecommendationCards map[string]*MFRecommendationCard `dynamic:"true"`
	// Key of DepositRecommendationCards is the deposit template id
	FixedDepositRecommendationCards map[string]*DepositRecommendationCard `dynamic:"true"`
	// Key of DepositRecommendationCards is the deposit template id
	SmartDepositRecommendationCards map[string]*DepositRecommendationCard `dynamic:"true"`
	// Key of DepositRecommendationCards is the scheme id
	P2PRecommendationCards map[string]*P2PRecommendationCard `dynamic:"true"`
	// Key of represent collection id in respective instrument
	// it has necessary details to show in card such deeplink ..etc
	CollectionRecommendationCards map[string]*CollectionRecommendationCard `dynamic:"true"`

	TitleString    string `dynamic:"true"`
	SubtitleString string `dynamic:"true"`
	TitleImageUrl  string `dynamic:"true"`
	// according to instrument the cta is being show
	CTAForInstrumentType aggregator.InstrumentType
}

type USStockRecommendationCard struct {
	Id   string `dynamic:"true"`
	Tag  *Text  `dynamic:"true"`
	Rank int32  `dynamic:"true"`
}

type MFRecommendationCard struct {
	Id   string `dynamic:"true"`
	Tag  *Text  `dynamic:"true"`
	Rank int32  `dynamic:"true"`
}

type DepositRecommendationCard struct {
	Id       string `dynamic:"true"`
	Tag      *Text  `dynamic:"true"`
	Rank     int32  `dynamic:"true"`
	Title    string `dynamic:"true"`
	Subtitle string `dynamic:"true"`
	Info     string `dynamic:"true"`
}

type P2PRecommendationCard struct {
	Tag      *Text  `dynamic:"true"`
	Rank     int32  `dynamic:"true"`
	Title    string `dynamic:"true"`
	Subtitle string `dynamic:"true"`
	Info     string `dynamic:"true"`
}

type CollectionRecommendationCard struct {
	Rank           int32  `dynamic:"true"`
	Title          string `dynamic:"true"`
	Subtitle       string `dynamic:"true"`
	ImgUrl         string `dynamic:"true"`
	InstrumentType aggregator.InstrumentType
}

type InvestedAmountSummary struct {
	// 1. If flag is true: only enable for user which are allowed based on user group check
	// 2. If flag is false: enable this for all the users
	RestrictInvestedAmountAsSaved bool
	AllowedUserGroups             []commontypes.UserGroup
}

type AddFundsParams struct {
	// bool to decide if add funds is restricted to user groups
	IsAddFundsRestricted bool `dynamic:"true"`
	// if add funds is restricted, the list of user groups that have access to add funds
	AllowedUserGrps []commontypes.UserGroup

	DownTime *DownTime `dynamic:"true"`

	MinKycParams *MinKycParams `dynamic:"true"`
	AddFundsIcon *AddFundsIcon `dynamic:"true"`
	// Params needed for add funds v3 flow(intent/collect flow)
	AddFundsV3Params *AddFundsV3Params `dynamic:"true"`
	// IsTierMovementDropOffFeedbackFlowEnabled depicts id feedback flow engine is enabled or not for add funds tier entry points
	IsTierMovementDropOffFeedbackFlowEnabled bool `dynamic:"true"`
	// Params needed for add funds v4 flow(payment options flow)
	AddFundsV4Params *AddFundsV4Params `dynamic:"true"`
	// Params needed for add funds polling instead of add money status to pay status
	// This contains min version after which we should navigate user to pay status screen
	IntentNavigateToPayStatusAndroidVersion  int `dynamic:"true"`
	CollectNavigateToPayStatusAndroidVersion int `dynamic:"true"`
}

type AddFundsV4Params struct {
	// Threshold bounds for TPAP account linking/vpa migration flow
	// <= Lowerbound and >= Upperbound will redirect the user to payment options flow
	TpapOptionThreshold *AmountBucket `dynamic:"true"`
}

type AddFundsV3Params struct {
	// Intent threshold to decide whether to show intent flow or collect flow
	// <= threshold will show intent flow
	IntentThreshold int64 `dynamic:"true"`
	// List of some popular vpa handles
	VpaHandles []string
	// Default amount to show in add funds
	DefaultAmount int64 `dynamic:"true"`
	// Min amount user needs to add to allow add funds
	MinAmount int64 `dynamic:"true"`
	// Max amount user can add in add funds flow
	MaxAmount int64 `dynamic:"true"`
	// ImageWithText top card component app version constraints
	ImageWithTextConstraints *cfg.PlatformVersionCheck `dynamic:"true"`
}

type AddFundsIcon struct {
	AccountCreditCheckFailIcon   string `dynamic:"true"`
	AccountBalanceCheckFailIcon  string `dynamic:"true"`
	AccountDurationCheckFailIcon string `dynamic:"true"`
	AccountBlockedCheckFailIcon  string `dynamic:"true"`
}

type MinKycParams struct {
	AccountFreezeMonthThreshold  int `dynamic:"true"`
	SavingsLimitPercentThreshold int `dynamic:"true"`
	CreditLimitPercentThreshold  int `dynamic:"true"`
}

type DownTime struct {
	IsDownTimeEnabled bool   `dynamic:"true"`
	Timezone          string `dynamic:"true"`
	DateTimeFormat    string `dynamic:"true"`
	StartDateTime     string `dynamic:"true"`
	EndDateTime       string `dynamic:"true"`
}

type SanityCheck struct {
	MotherNameStopWords map[string]bool
	FatherNameStopWords map[string]bool
}

type AFU struct {
	AllowOnboardingOnRegisteredDevice   bool `dynamic:"true"`
	AllowReOnboardingOnRegisteredDevice bool `dynamic:"true"`
	AllowSimUpdateAFU                   bool `dynamic:"true"`
	EnableAccountInactiveCheck          bool `dynamic:"true"`
	EnableForceUpdateSimUpdate          bool `dynamic:"true"`
	EnableAFUForCC                      bool `dynamic:"true"`
	EnableDeviceOrSimUpdateForNRUser    bool `dynamic:"true"`
	// AllowDeeplinkForWhitelistedActions to allow deeplink for whitelisted AFU actions
	AllowDeeplinkForWhitelistedActions bool `dynamic:"true"`
}

// PaymentInfo Information to show for payments on screen
type PaymentInfoBanner struct {
	NeftTxnInfoBanner string
}

// Time captures clock time in a day excluding date field
type Time struct {
	// Hours in range 0..23
	// Minutes and second in range 0..59
	Hours, Minutes, Seconds int `dynamic:"true"`
}

type Deposit struct {
	PreclosureNudge          *PreclosureNudge // TODO(mounish): move under preclosure struct
	Preclosure               *Preclosure
	MaturityAmountVisibility *DepositMaturityAmountVisibility `dynamic:"true"`
	MaturityAmount           *DepositMaturityAmount
	Goals                    *DepositGoals    `dynamic:"true"`
	AutoSave                 *DepositAutoSave `dynamic:"true"`
	// Can take values as "SD" and "FD"
	SaveDeeplink string            `dynamic:"true"`
	TaxSaving    *DepositTaxSaving `dynamic:"true"`
	// config related to deposit statement generation
	Statement *DepositStatement `dynamic:"true"`
	// config related to the new summary screen
	Summary *DepositSummary `dynamic:"true"`
	// penalty percentage for preClose
	PreClosurePenaltyPercentage float64
	// max day for which preClosure doesnt have penalty
	MaxDayForNonPenaltyPreClosure int64
	//  RetentionFeatureIsEnable is true if user is enable for retention feature
	QuestFDRetentionFeatureIsEnable bool `dynamic:"true" ,quest:"variable"`
	// flag to control if quest experiment need to be consider or not
	ShouldEnableExperiment bool `dynamic:"true"`
	// DurationForDuplicateDepositCheck to get duration to check duplicate deposit creation
	DurationForDuplicateDepositCheck time.Duration
	// flag to enable fetch transaction using new rpc
	EnableTransactionFetchV1 bool `dynamic:"true"`
	// config to handle downtime for deposits
	EnableTimestampBasedDepositDownTime *DepositTimestampBasedDowntime `dynamic:"true"`
}

type DepositTimestampBasedDowntime struct {
	IsEnable       bool   `dynamic:"true"`
	StartTimestamp string `dynamic:"true"`
	EndTimestamp   string `dynamic:"true"`
	Title          string `dynamic:"true"`
	Msg            string `dynamic:"true"`
}

type DepositSummary struct {
	MinIosVersionForAddMoneyBottomSheet     int `dynamic:"true"`
	MinAndroidVersionForAddMoneyBottomSheet int `dynamic:"true"`
}

type DepositStatement struct {
	// if true, statement generation option will be enabled in deposit details screen
	Enable            bool `dynamic:"true"`
	AllowedUserGroups []commontypes.UserGroup
}

type DepositTaxSaving struct {
	Enable bool `dynamic:"true"`
	// min android version to show tax saving
	MinAndroidVersion uint32 `dynamic:"true"`
	// min ios version to show tax saving
	MinIosVersion uint32 `dynamic:"true"`
}

type DepositAutoSave struct {
	// auto save rule setup in deposit post creation flow
	PostCreationFlow *DepositAutoSavePostCreationFlow `dynamic:"true"`
	// auto save discovery in deposit details screen
	DetailsFlow *DepositAutoSaveDetailsFlow `dynamic:"true"`
	// auto save rule setup in deposit pre creation flow
	PreCreationFlow *DepositAutoSavePreCreationFlow `dynamic:"true"`
}

type DepositAutoSavePreCreationFlow struct {
	Enable            bool `dynamic:"true"`
	AllowedUserGroups []commontypes.UserGroup
}

type DepositAutoSavePostCreationFlow struct {
	Enable                     bool `dynamic:"true"`
	GlobalAllowedUserGroups    []commontypes.UserGroup
	DepositStatusPollingTimout int `dynamic:"true"`
}

type DepositAutoSaveDetailsFlow struct {
	// global flag for auto save discovery in deposit details screen
	// if false, both suggestions and rule list will be hidden
	Enable                  bool `dynamic:"true"`
	GlobalAllowedUserGroups []commontypes.UserGroup
	// flag for auto save suggestion cards (save 50 daily, save 150 weekly...)
	EnableAutoSaveSuggestions bool `dynamic:"true"`
	// flag for auto save rule list associated with the deposit account
	EnableAutoSaveRuleList bool `dynamic:"true"`
}

type DepositGoals struct {
	GoalDetailsInDepositList    *GoalDetailsInDepositList    `dynamic:"true"`
	GoalDetailsInDepositDetails *GoalDetailsInDepositDetails `dynamic:"true"`
	InfoText                    *DepositGoalsInfoText
}

type DepositGoalsInfoText struct {
	DepositClosedAndGoalReached       string
	DepositClosedAndGoalNotReached    string
	DepositNotClosedAndGoalReached    string
	DepositNotClosedAndGoalNotReached string
}

type GoalDetailsInDepositList struct {
	Enable bool `dynamic:"true"`
}

type GoalDetailsInDepositDetails struct {
	Enable bool `dynamic:"true"`
}
type DepositMaturityAmount struct {
	InfoText               string
	InfoTextInterestPayout string
}

type DepositMaturityAmountVisibility struct {
	Global                  bool `dynamic:"true"`
	GlobalAllowedUserGroups []commontypes.UserGroup
	SDCreation              bool `dynamic:"true"`
	FDCreation              bool `dynamic:"true"`
	SDDetails               bool `dynamic:"true"`
	FDDetails               bool `dynamic:"true"`
	SDAddFunds              bool `dynamic:"true"`
}

type Preclosure struct {
	ConfirmationNudge *ConfirmationNudge
}

type ConfirmationNudge struct {
	FaqCategoryId          string
	Title                  string
	CautionText            string
	AccountNumberText      string
	RepayAccountNumberText string
	InterestForfeit        map[string]string
}

type PreclosureNudge struct {
	Title      string
	SubTitle   string
	Attributes map[string]*PreclosureNudgeAttribute
}

type PreclosureNudgeAttribute struct {
	Title string
	Body  string
	Icon  string
}

type AnalyserParams struct {
	// feature flag for analyser. If `ShowAnalyser` is false then all requests to GetAnalyser will return Internal error.
	ShowAnalyser bool `dynamic:"true"`
	// path to json file containing config for Analysers and Filters.
	AnalyserConfigJson string
	// release config for all analysers
	AnalyserReleaseConfig map[string]*AnalyserReleaseConfig `dynamic:"true"`
	// release config for analyser landing pages
	AnalyserLandingPageReleaseConfig map[string]*AnalyserAppReleaseConfig `dynamic:"true"`
	// whether to return dummy rpc response
	GetAnalyserGenerateDummyResponse bool
	// config for converting lenses to old format
	LensOldFormatConversionConfig *LensOldFormatConversionConfig
	// config for add funds banner in category analyser
	AddFundsBannerConf *AnalyserAddFundsBannerConfig `dynamic:"true"`
	// analyser hub config
	AnalyserHubConfig *AnalyserHubConfig `dynamic:"true"`
	// Credit report params
	CreditReportParams *CreditReportParams `dynamic:"true"`
	// Credit Score Analyser config
	CreditScoreAnalyserConfig *CreditScoreAnalyserConfig `dynamic:"true"`
	AnalyserFeedbackParams    *AnalyserFeedbackParams    `dynamic:"true"`
	// Analyser experiments configs
	Experiment *AnalyserExperiment `dynamic:"true" ,quest:"component"`
	// release config for refresh banner in mf analysers
	MfAnalyserRefreshBannerReleaseConfig *AnalyserFeatureReleaseConfig `dynamic:"true"`
	LoanAnalyserInsightsParams           *LoanAnalyserInsightsParams   `dynamic:"true"`
}

type AnalyserReleaseConfig struct {
	ReleaseConfig      *AnalyserAppReleaseConfig     `dynamic:"true"`
	LensReleaseConfigs map[string]*LensReleaseConfig `dynamic:"true"`
}

type LensReleaseConfig struct {
	ReleaseConfig *AnalyserAppReleaseConfig `dynamic:"true"`
}

type AnalyserAppReleaseConfig struct {
	AndroidReleaseConfig *AnalyserFeatureReleaseConfig `dynamic:"true"`
	IOSReleaseConfig     *AnalyserFeatureReleaseConfig `dynamic:"true"`
}

type AnalyserFeatureReleaseConfig struct {
	MinAppVersion       int      `dynamic:"true"`
	IsFeatureRestricted bool     `dynamic:"true"`
	AllowedUserGroups   []string `dynamic:"true"`
}

type LensOldFormatConversionConfig struct {
	// convert analysers lens to old format
	OldFormatAnalysers []string
}

type AnalyserAddFundsBannerConfig struct {
	// number of days from end date of month when it is considered end of month period
	EndOfMonthDays int `dynamic:"true"`
	// number of days from start date of month when it is considered start of month period
	StartOfMonthDays int `dynamic:"true"`
}

type AnalyserHubConfig struct {
	// It defines all the Experiments that we want to show to users. One user will see only one type of Experiment Screen.
	// The percentage of users seeing a particular type will vary based on the number of Experiments defined in the Array.
	// The sequence of Experiment Names in Array also defines what type of Experiment a particular group of users see.
	Experiments []string `dynamic:"true"`
}

type CreditReportParams struct {
	// We will reuse a credit report download process till it doesn't expire. If it expires we will create a new process.
	DownloadProcessExpiry time.Duration `dynamic:"true"`
}

type CreditScoreAnalyserConfig struct {
	// duration after the last credit report download, the credit report refresh banner should be shown
	CreditReportRefreshBannerDurationInDays int `dynamic:"true"`
	// duration after a report fetch post which we'll show a banner mentioning the date on/after which
	// a new report fetch will be available for the user.
	CreditReportRefreshAfterBannerDurationInDays int `dynamic:"true"`
	// image to be shown to the user if credit report not present with us.
	ZeroStateImageUrl        string `dynamic:"true"`
	ReportNotPresentImageUrl string `dynamic:"true"`
	// duration for which user won't be able to retry credit report download flow if no report not found in last attempt
	CoolOffDurationForReportNotFound time.Duration `dynamic:"true"`
	// Experian Logo url
	ExperianLogoUrl string `dynamic:"true"`
	// days since last successful report fetch post which auto refresh can be attempted
	AutoRefreshCoolOffDurationInDays int `dynamic:"true"`
	// time duration to wait for refresh to complete. post the timeout the older/stale report should be returned
	AutoRefreshPollerTimeoutDuration time.Duration `dynamic:"true"`
	// This config is for showing insights card about the changes in experian credit score system.
	ExperianV2InsightsConfig *ExperianV2InsightsConfig `dynamic:"true"`
}

type ExperianV2InsightsConfig struct {
	IsEnabled  bool      `dynamic:"true"`
	ActiveFrom time.Time `dynamic:"true"`
	ActiveTill time.Time `dynamic:"true"`
}

type TxnCatUserGroupParams struct {
	// bool to decide if displaying txn category is restricted to user groups
	IstxnCatRestricted bool
	// if displaying txn category is restricted, the list of user groups that have access to connected accounts
	AllowedUserGrps []commontypes.UserGroup
}

type SyncVendorApiDeviceIntegrityCheck struct {
	IsEnabled      bool
	AllowedDevices []types.DeviceVerificationResult
}

// This config object is shared between client and backend and used to control feature flags on client
// Any use case which does not want to use firebase remote config should be added here. For backend only
// configurations/flags we should NOT use this
type SharedConfig struct {
	// Feature flag to enable disable v2 safety net and oauth decoupled flow
	EnableV2SafetyNetFlow bool `dynamic:"true"`

	// Flag for making pan mandatory in nominee flow
	IsNomineePanMandatory bool `dynamic:"true"`

	// Flag for mutual fund status screen time out
	MutualFundStatusScreenTimeoutInMillis int64 `dynamic:"true"`

	// Flag for p2p status screen time out
	P2PStatusScreenTimeoutInMillis int64 `dynamic:"true"`
}

type SalaryProgram struct {
	// homescreen salary card details (default config)
	HomeCard *SalaryProgramHomeCard `dynamic:"true"`
	// home cards map to support multiple configs, i.e. time-bound
	HomeCardsTimeBound map[string]*SalaryProgramHomeCard `dynamic:"true"`
	// home cards map for users having employers onboarded via B2B route to support multiple configs, i.e. time-bound
	HomeCardsB2BTimeBound map[string]*SalaryProgramHomeCard `dynamic:"true"`

	// salaryprogram entrypoint section info
	EntryPointSectionInfo *SalaryProgramEntryPointSectionInfo `dynamic:"true"`
	// salaryprogram entrypoint section info map to support multiple configs, i.e. time bound
	EntryPointSectionInfoTimeBound map[string]*SalaryProgramEntryPointSectionInfo `dynamic:"true"`
	// salaryprogram entrypoint section info map for users having employers onboarded via B2B route to support multiple configs, i.e. time bound
	EntryPointSectionInfoB2BTimeBound map[string]*SalaryProgramEntryPointSectionInfo `dynamic:"true"`

	// B2B employers map for salary program, map[EMPLOYER_ID]NAME
	B2BEmployersMap map[string]string `dynamic:"true"`

	// intro page details
	IntroPage *SalaryProgramIntroPage `dynamic:"true"`
	// intro page v3 config
	IntroPageV3 *SalaryProgramIntroPageV3 `dynamic:"true"`
	// steps to unlock details
	StepsToUnlockInfo *SalaryProgramStepsToUnlockInfo `dynamic:"true"`
	// eligibility info
	EligibilityDetails *SalaryProgramEligibilityDetails `dynamic:"true"`
	// salary payslips mail info
	SalaryPayslipsMailInfo *SalaryProgramPayslipsMailInfo `dynamic:"true"`
	// employer confirmation flow
	EmployerConfirmationFlow *EmployerConfirmationFlow `dynamic:"true"`
	// share account details mail info
	ShareAccountDetailsMailInfo *ShareAccountDetailsMailInfo `dynamic:"true"`
	// salary txn verification flow
	TransactionsPage *SalaryProgramTransactionsPage `dynamic:"true"`
	// Salary transaction Filters
	SalaryTransactionFilters *SalaryTransactionFilters `dynamic:"true"`
	// DisableEmployerSearchByGSTIN is used to turn searching employers by
	// gstin numbers on and off.
	DisableEmployerSearchByGSTIN bool `dynamic:"true"`
	// salary payslip mail info during salary verification failure
	SalaryPayslipMailDuringFailureInfo *SalaryPayslipMailDuringVerificationFailureInfo `dynamic:"true"`
	// employer update nudge info to be shown when we detect salary from a different employer
	ConfirmEmployerUpdateNudgeInfo *SalaryProgramConfirmEmployerUpdateNudgeInfo `dynamic:"true"`
	// employer update nudge info to be shown when we detect salary from a different employer
	BenefitsActivatedAndConfirmEmployerCommsTile *SalaryProgramConfirmEmployerUpdateNudgeInfo `dynamic:"true"`
	// top banner info that gets displayed on the benefits section on salary account landing page.
	SalaryAccountBenefitsSectionTopBannerInfo *SalaryAccountBenefitsSectionBannerInfo `dynamic:"true"`
	// employer to prevent accessing salary referrals program, map[EMPLOYER_ID]EMPLOYER_NAME
	SalaryReferralEmployersBlockedMap map[string]string `dynamic:"true"`
	// invite colleagues invitation message
	InviteColleaguesInvitationMsg string `dynamic:"true"`
	// useful to apply a capping  while displaying colleagues count for a user as we
	// don't want to show the user the exact count of their colleagues on Fi
	ReferralsCapColleaguesCountAtForDisplay int `dynamic:"true"`
	// salary-benefits-landing-page quick links section info.
	SalaryBenefitsLandingPageQuickLinksSection *SalaryBenefitsLandingPageQuickLinksSection `dynamic:"true"`
	// salary-benefits-landing-page banner section info.
	SalaryBenefitsLandingPageBannerSectionInfo *SalaryBenefitsLandingPageBannerSectionInfo `dynamic:"true"`
	// salary-benefits-landing-page help section info.
	HelpSectionInfo *HelpSectionInfo `dynamic:"true"`
	// category id of salaryprogram related help FAQs
	SalaryProgramFAQsCategoryId string `dynamic:"true"`
	// SalaryAccountVerificationStepperInfo is the new stepper for salary verification
	SalaryAccountVerificationStepperInfo *SalaryAccountVerificationStepperInfo `dynamic:"true"`
	// landing page top sections comms info map to support multiple infos
	LandingPageTopSectionCommsInfoMap map[string]*SalaryProgramLandingPageTopSectionCommsInfo `dynamic:"true"`

	// min android/ios app version on which rpc error views are handled properly.
	MinAndroidAppVersionHandlingRpcResponseErrorView uint32 `dynamic:"true"`
	MinIosAppVersionHandlingRpcResponseErrorView     uint32 `dynamic:"true"`

	// min android/ios app version on which winnings section design v2 is handled.
	// https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=10037%3A81342&t=TXXiwuAWilL4MBEi-1
	MinAndroidAppVersionHandlingWinningsSectionV2 uint32 `dynamic:"true"`
	MinIosAppVersionHandlingWinningsSectionV2     uint32 `dynamic:"true"`

	// ios app version in which Salary Referrals How It Works hardcoding was fixed.
	// GetSalaryProgramReferralsLandingPage RPC should return app update error popup for ios app version < this
	// TODO(kunal): In logic should we be checking for MinIosAppVersionHandlingRpcResponseErrorView as well?
	IosAppVersionSalaryReferralHowItWorksFix uint32 `dynamic:"true"`

	// date in RFC3339 format of a salaryprogram rewards campaign, this would be shown on salaryprogram entrypoints on home/profile page.
	// todo (utkarsh) : clean this variable once the campaign is over
	SalaryProgramSpecialRewardCampaignEndDate string `dynamic:"true"`
	// cancelled cheque image path relative to a s3 bucket
	CancelledChequeImageS3Path string
	// Details of text to be superimposed on cancelled cheque image
	CancelledChequeSuperImposeTextsDetails map[string]*SalaryProgramSuperImposeTextOnImageDetails
	// early salary benefit config
	EarlySalaryBenefitConfig *SalaryProgramEarlySalaryBenefitConfig `dynamic:"true"`

	// health insurance reward offer id to policy config map
	HealthInsuranceRewardOfferIdToPolicyConfigMap map[string]*HealthInsurancePolicyConfig `dynamic:"true"`

	// benefits calculator page details
	BenefitsCalculatorPage *SalaryProgramBenefitsCalculatorPage

	// min android/ios app version on which benefits calculator page is supported
	MinAndroidAppVersionToSupportBenefitsCalculatorPage uint32 `dynamic:"true"`
	MinIosAppVersionToSupportBenefitsCalculatorPage     uint32 `dynamic:"true"`
	// Share detail on mail v1 flow feature config. In v1 we separately call GetDetailsToShareOnEmail rpc to get email content.
	ShareDetailsOnMailV1FeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for attachments support with email
	EmailAttachmentsSupportFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// min required duration since last activation for showing expiry comms
	MinReqDurationSinceLastActivationForBenefitsExpiryComms time.Duration
	// min android app version supporting SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION screen
	MinAndroidAppVersionSupportingLandingScreenRedirection uint32 `dynamic:"true"`
	// min ios app version supporting SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION screen
	MinIosAppVersionSupportingLandingScreenRedirection uint32 `dynamic:"true"`
	// mail info for sharing salary payslip for employer verification
	SharePayslipForEmployerVerificationMailInfo *SharePayslipForEmployerVerificationMailInfo `dynamic:"true"`
	// in benefits section v1, we do not need to show ACTIVE/INACTIVE tags on the benefits tiles,
	// also the benefits section header is updated to show inactive state for inactive users
	// figma : https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=11935-89746&t=JWnu6cZ01xZ7ZQ2p-0
	MinAndroidAppVersionSupportingBenefitsSectionV1 uint32 `dynamic:"true"`
	MinIosAppVersionSupportingBenefitsSectionV1     uint32 `dynamic:"true"`
	// feature config for screen shown when we are unable to verify that user is employed to the selected employer while employer confirmation for salary program.
	EmpConfirmationFailureScreenFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for banner on landing page for showing salary eligibility info
	LandingPageEligibilityBannerFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for comms info section on salary landing page
	LandingPageCommsInfoSectionFeatureConfig *app.FeatureConfig `dynamic:"true"`
	// feature config for V1 quick link tiles on salary landing page
	LandingPageQuickLinkTilesV1FeatureConfig *app.FeatureConfig `dynamic:"true"`
	// salary lite configs
	SalaryLiteConfig *SalaryLiteConfig `dynamic:"true"`

	// in app csat survey configs
	SalaryProgramSurvey *SalaryProgramSurvey `dynamic:"true"`
	// IsSalaryProgramBenefitAmazon500VoucherEnabled is the flag for enabling or disabling the Amazon 500 voucher
	IsSalaryProgramBenefitAmazon500VoucherEnabled bool `dynamic:"true"`
	// SalaryLiteFlowsEligibleActors denotes hardcoded actors for whom the salary lite flows are enabled.
	SalaryLiteFlowsEligibleActors []string      `dynamic:"true"`
	AaSalaryTpapCooloffDuration   time.Duration `dynamic:"true"`
	// flag for salary program referrals season banner
	EnableSalaryReferralsSeason bool `dynamic:"true"`
	// Aa salary configs
	AaSalaryConfig *AaSalaryConfig `dynamic:"true" ,quest:"component"`
	// health insurance configs for onsurity flows
	HealthInsuranceOnsurityPolicyFlowsConfig *app.FeatureConfig `dynamic:"true"`
	// Intervals for which Salary winings widget needs to be deprioritized
	SalaryWinningSectionPrioritizedMonthlyIntervals *SalaryWinningSectionPrioritizedMonthlyIntervals `dynamic:"true"`
	// Flag for disabling SalaryB2C Entrypoints
	DisableB2CSalaryEntryPointsFlag bool `dynamic:"true"`
}

type SalaryWinningSectionPrioritizedMonthlyIntervals struct {
	StartDate int `dynamic:"true"`
	EndDate   int `dynamic:"true"`
}

// Aa salary config
type AaSalaryConfig struct {
	// Aa Salary Amount Setup Version V2 support specific to os
	EnableAaSalaryAmountSetupVersionV2       bool                                      `dynamic:"true" ,quest:"variable"`
	AaSalaryAmountSetupVersionV2             *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	AaSalarySourceScreenHideConnectTitle     *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	AaSalaryAmountSetupBackConfirmationPopup *AaSalaryAmountSetupBackConfirmationPopup `dynamic:"true" ,quest:"component"`
}

type AaSalaryAmountSetupBackConfirmationPopup struct {
	AppVersion *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
	Enable     bool                                      `dynamic:"true" ,quest:"variable"`
	Icon       string                                    `dynamic:"true" ,quest:"variable"`
	Title      string                                    `dynamic:"true" ,quest:"variable"`
	SubTitle   string                                    `dynamic:"true" ,quest:"variable"`
	Cta1Text   string                                    `dynamic:"true" ,quest:"variable"`
	Cta2Text   string                                    `dynamic:"true" ,quest:"variable"`
}

type SalaryProgramSurvey struct {
	// segment to which we want to show survey on benefits landing page
	BenefitsLandingPageSurveySegmentIdExpression string `dynamic:"true"`
}

type SalaryLiteConfig struct {
	// feature release config for salary lite
	FeatureReleaseConfig *app.FeatureConfig `dynamic:"true"`
	// flag to enable salary lite for already salary active INTERNAL user group users
	IsEnabledForInternalActiveUsers bool `dynamic:"true"`
	// display segment expression for salary lite banner on salary landing page
	LandingPageBannerDisplaySegmentExpression string `dynamic:"true"`
	// min required amount salary lite mandate setup
	MinReqAmountForMandateSetup int64
	// max allowed amount salary lite mandate setup
	MaxAllowedAmountForMandateSetup int64
	// default amount shown to user for salary lite mandate setup
	DefaultAmountForMandateSetup int64
	// salary lite mandate creation cool off period in days
	MandateCreationCoolOffPeriodInDays uint32 `dynamic:"true"`
	// upgrade full salary program page config
	UpgradeToFullPageQuickLinksSectionConfig *UpgradeToFullPageQuickLinksSection `dynamic:"true"`
	// min required duration since last activation for showing salary activation grace period
	MinReqDurationSinceLastActivationForShowingGracePeriod time.Duration
	// enach mandate bank level charges doc url
	EnachMandateBankLevelChargesDocUrl string `dynamic:"true"`
	// s3 path for enach epifi tnc doc
	EnachEpifiTncDocS3Path string `dynamic:"true"`

	IsSalaryLiteMandateSetupDropOffFeedbackFlowEnabled bool `dynamic:"true"`

	IsSalaryLiteEnachDropOffFeedbackFlowEnabled bool `dynamic:"true"`

	// flag to enable salary lite landing page banner entrypoint
	EnableLandingPageBanner bool `dynamic:"true"`
}

type UpgradeToFullPageQuickLinksSection struct {
	// should the section be visible for not.
	IsVisible       bool                       `dynamic:"true"`
	QuickLinksTiles map[string]*QuickLinksTile `dynamic:"true"`
}

type HealthInsurancePolicyConfig struct {
	HealthInsurancePolicyFAQsDocS3Path           string `dynamic:"true"`
	HealthInsurancePolicyClaimProcessDocS3Path   string `dynamic:"true"`
	HealthInsuranceCashlessHospitalListDocS3Path string `dynamic:"true"`
	HealthInsuranceTncsDocS3Path                 string `dynamic:"true"`
	HealthInsurancePolicyType                    string `dynamic:"true"`
}

type SalaryAccountVerificationStepperInfo struct {
	IsEnabled bool `dynamic:"true"`

	// app version fields
	MinAndroidAppVersionSupported uint32 `dynamic:"true"`
	MinIosAppVersionSupported     uint32 `dynamic:"true"`
}

type SalaryProgramLandingPageTopSectionCommsInfo struct {
	IsEnabled bool `dynamic:"true"`

	// condition fields
	OnlyForSalaryProgramActiveUsers bool `dynamic:"true"`
	OnlyForB2BEmployers             bool `dynamic:"true"`
	OnlyForB2CEmployers             bool `dynamic:"true"`

	// app version fields
	MinAndroidAppVersionSupported uint32 `dynamic:"true"`
	MaxAndroidAppVersionAllowed   uint32 `dynamic:"true"`
	MinIosAppVersionSupported     uint32 `dynamic:"true"`
	MaxIosAppVersionAllowed       uint32 `dynamic:"true"`

	// comms display fields
	ImageUrl   string            `dynamic:"true"`
	Title      string            `dynamic:"true"`
	TitleColor string            `dynamic:"true"`
	Desc       string            `dynamic:"true"`
	DescColor  string            `dynamic:"true"`
	PrimaryCta *SalaryProgramCta `dynamic:"true"`
}

type SalaryProgramEntryPointSectionInfo struct {
	// since when the config will be applicable (inclusive)
	ActiveFrom string `dynamic:"true"`
	// till when the config will be applicable (exclusive)
	ActiveTill string `dynamic:"true"`

	IsVisible bool `dynamic:"true"`

	// summary tile info for different states
	BenefitsActiveSummaryTileInfo                        *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	RegCompletedBenefitsInActiveSummaryTileInfo          *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	RegNotCompletedSummaryTileInfo                       *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	SalaryLiteRegCompletedSummaryTileInfo                *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	SalaryLiteBenefitsActiveSummaryTileInfo              *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	SalaryLiteBenefitsActiveInGracePeriodSummaryTileInfo *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`

	// status tile info for different states
	BenefitsActiveStatusTileInfo               *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	RegCompletedBenefitsInActiveStatusTileInfo *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	// useful for showing some campaign specific info on entrypoint section status tile.
	// RegCompletedBenefitsInActiveStatusTileCampaignInfo info should be prioritized over RegCompletedBenefitsInActiveStatusTileInfo if it's present.
	RegCompletedBenefitsInActiveStatusTileCampaignInfo *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	RegNotCompletedStatusTileInfo                      *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`

	// promo-banner tile info for different states
	BenefitsActivePromoBannerTileInfo                        *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	RegCompletedBenefitsInActivePromoBannerTileInfo          *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	RegNotCompletedPromoBannerTileInfo                       *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	SalaryLiteRegCompletedPromoBannerTileInfo                *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	SalaryLiteBenefitsActivePromoBannerTileInfo              *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
	SalaryLiteBenefitsActiveInGracePeriodPromoBannerTileInfo *SalaryProgramEntryPointSectionTileInfo `dynamic:"true"`
}

type SalaryProgramEntryPointSectionTileInfo struct {
	IsVisible bool `dynamic:"true"`

	// currently supported only on summary tile, ex text- Premium
	HeaderTitle      string            `dynamic:"true"`
	HeaderTitleColor string            `dynamic:"true"`
	Title            string            `dynamic:"true"`
	TitleColor       string            `dynamic:"true"`
	BgColor          string            `dynamic:"true"`
	ImageUrl         string            `dynamic:"true"`
	Cta              *SalaryProgramCta `dynamic:"true"`
	// list of AppVersionedCtas with each of the cta containing details of app version range on which it is supported,
	// if any app versioned cta is applicable for an app version then it should take priority over the default Cta field,
	// use-case is to support different ctas on different client app versions.
	// **Note** : map DS is used as proxy for list as dynamic config does not supports list DS
	AppVersionedCtas map[string]*AppVersionedSalaryProgramCta `dynamic:"true"`
	Tag              *SalaryProgramCardTag                    `dynamic:"true"`
}

type SalaryBenefitsLandingPageQuickLinksSection struct {
	// should the section be visible for not.
	IsVisible                                    bool   `dynamic:"true"`
	MinAndroidVersionSupportingQuickLinksSection uint32 `dynamic:"true"`
	MinIOSVersionSupportingQuickLinksSection     uint32 `dynamic:"true"`
	// used map instead of list as list is not supported current in dynamic config
	QuickLinksTiles map[string]*QuickLinksTile `dynamic:"true"`
}

type QuickLinksTile struct {
	// visible on android version >= VisibleFromAndroidVersion
	VisibleFromAndroidVersion uint32 `dynamic:"true"`
	// visible on ios version >= VisibleFromIosVersion
	VisibleFromIosVersion uint32            `dynamic:"true"`
	Title                 string            `dynamic:"true"`
	TitleColor            string            `dynamic:"true"`
	ImageUrl              string            `dynamic:"true"`
	BgColor               string            `dynamic:"true"`
	CTA                   *SalaryProgramCta `dynamic:"true"`
	// TileRank denotes position (relative) on quick links section.
	TileRank int `dynamic:"true"`
}

type HelpSectionInfo struct {
	// should the section be visible for not.
	IsVisible                              bool   `dynamic:"true"`
	MinAndroidVersionSupportingHelpSection uint32 `dynamic:"true"`
	MinIOSVersionSupportingHelpSection     uint32 `dynamic:"true"`

	Title         string `dynamic:"true"`
	TitleColor    string `dynamic:"true"`
	Subtitle      string `dynamic:"true"`
	SubtitleColor string `dynamic:"true"`
	IconUrl       string `dynamic:"true"`

	HelpSupportMailId string `dynamic:"true"`
}

type SalaryBenefitsLandingPageBannerSectionInfo struct {
	IsVisible                                bool   `dynamic:"true"`
	MinAndroidVersionSupportingBannerSection uint32 `dynamic:"true"`
	MinIOSVersionSupportingBannerSection     uint32 `dynamic:"true"`

	Title    *Text             `dynamic:"true"`
	BgColor  string            `dynamic:"true"`
	ImageUrl string            `dynamic:"true"`
	Cta      *SalaryProgramCta `dynamic:"true"`
}

type SalaryProgramEarlySalaryBenefitConfig struct {
	// id of early salary rewardOffer
	EarlySalaryBenefitRewardOfferId string `dynamic:"true"`
}

type SalaryProgramIntroPage struct {
	Title      string `dynamic:"true"`
	TitleColor string
	// title to be used for v2 version of intro page
	TitleV2   string `dynamic:"true"`
	ImageUrl  string `dynamic:"true"`
	Desc      string `dynamic:"true"`
	DescColor string
	// text on intro page which leads to steps-to-unlock info
	StepsToUnlockEntryText          string `dynamic:"true"`
	StepsToUnlockEntryTextFontColor string
	// eligibility details text which leads to further info
	EligibilityDetailsEntryText          string `dynamic:"true"`
	EligibilityDetailsEntryTextFontColor string
	// eligibility text/font-color to be used for v1 version of intro page
	// todo (utkarsh) : separate out v1 config if multiple display attributes are different across versions.
	EligibilityDetailsEntryTextV1          string `dynamic:"true"`
	EligibilityDetailsEntryTextFontColorV1 string

	TncUrl string
	// cta to be used when employer confirmation flow needs to be initiated
	EmployerConfirmationReqdCta *SalaryProgramCta `dynamic:"true"`
	// cta to be used when full kyc flow needs to be initiated
	FullKycReqdCta *SalaryProgramCta `dynamic:"true"`
	// salary landing page cta
	SalaryLandingPageCta *SalaryProgramCta `dynamic:"true"`
	// story cta on the landing page
	StoryCta *SalaryProgramCta `dynamic:"true"`
}

type SalaryProgramIntroPageV3 struct {
	// flag to decide whether to display or not display the salary calculator cta on salary intro page
	ShowSalaryCalculatorCta bool `dynamic:"true"`
	// feature config for hero benefit card on the intro page
	HeroBenefitFeatureConfig *app.FeatureConfig `dynamic:"true"`
}

type SalaryProgramStepsToUnlockInfo struct {
	Title         string `dynamic:"true"`
	TitleColor    string
	Steps         []*SalaryProgramUnlockStep
	Subtitle      string `dynamic:"true"`
	SubtitleColor string
}

type SalaryProgramUnlockStep struct {
	Step      string `dynamic:"true"`
	FontColor string
	ImageUrl  string `dynamic:"true"`
}

type SalaryProgramEligibilityDetails struct {
	Title        string `dynamic:"true"`
	TitleColor   string
	ImageUrl     string `dynamic:"true"`
	Requirements []string
}

type SalaryProgramPayslipsMailInfo struct {
	EmailId      string `dynamic:"true"`
	EmailIdColor string
	ImageUrl     string `dynamic:"true"`
	Desc         string `dynamic:"true"`
	DescColor    string
	EmailSubject string `dynamic:"true"`
	EmailBody    string `dynamic:"true"`
}

// todo(yuvraj): use generic email template struct for all use cases
type SharePayslipForEmployerVerificationMailInfo struct {
	// to-email-id, email id of the receiver
	ToEmailId string `dynamic:"true"`
	// email body
	EmailBody string `dynamic:"true"`
}

type ShareAccountDetailsMailInfo struct {
	EmailSubject string `dynamic:"true"`
	// EmailBody is default email body.
	EmailBody string `dynamic:"true"`
	// EmailBodyWithCancelledCheque will be email body when cancelled cheque is attached with email
	EmailBodyWithCancelledCheque string `dynamic:"true"`
}

type EmployerConfirmationFlow struct {
	EligibilityStatement    string            `dynamic:"true"`
	RegistrationCompleteCta *SalaryProgramCta `dynamic:"true"`
	FullKycReqdCta          *SalaryProgramCta `dynamic:"true"`
}

type SalaryProgramCta struct {
	Text      string `dynamic:"true"`
	TextColor string `dynamic:"true"`
	BgColor   string `dynamic:"true"`
	ImageUrl  string `dynamic:"true"`
	// is visible tell whether the CTA is to be shown or not. Though, the functionality decision relies on app
	IsVisible bool                    `dynamic:"true"`
	Deeplink  *deeplinkcfgv2.Deeplink `dynamic:"true"`
}

type AppVersionedSalaryProgramCta struct {
	MinAndroidAppVersionSupportingCta int `dynamic:"true"`
	MaxAndroidAppVersionSupportingCta int `dynamic:"true"`
	MinIosAppVersionSupportingCta     int `dynamic:"true"`
	MaxIosAppVersionSupportingCta     int `dynamic:"true"`

	Cta *SalaryProgramCta `dynamic:"true"`
}

type SalaryProgramCardTag struct {
	Tag       string `dynamic:"true"`
	TextColor string `dynamic:"true"`
	BgColor   string `dynamic:"true"`
}

type SalaryProgramHomeCard struct {
	// since when the config will be applicable (inclusive)
	ActiveFrom string `dynamic:"true"`
	// till when the config will be applicable (exclusive)
	ActiveTill string `dynamic:"true"`

	// bool to decide whether home screen salary program card should be visible or not
	ShowCard bool `dynamic:"true"`
	// card's header title, i.e. above the card
	HeaderTitle string `dynamic:"true"`

	// card's title when salary benefits are inactive
	BenefitsInactiveTitle string `dynamic:"true"`
	// card's title when salary benefits are inactive, but registration is complete
	RegCompleteBenefitsInactiveTitle string `dynamic:"true"`
	// card's title when salary benefits are active
	BenefitsActiveTitle string `dynamic:"true"`

	// CTA details when benefits are INACTIVE
	BenefitsInactiveCta *SalaryProgramCta `dynamic:"true"`
	// CTA details when benefits are INACTIVE, but registration is complete
	RegCompleteBenefitsInactiveCta *SalaryProgramCta `dynamic:"true"`
	// CTA details when benefits are ACTIVE
	BenefitsActiveCta *SalaryProgramCta `dynamic:"true"`

	// tag to be shown on the card when benefits are INACTIVE for the user
	BenefitsInactiveTag *SalaryProgramCardTag `dynamic:"true"`
	// tag to be shown on the card when benefits are INACTIVE for the user, but registration is complete
	RegCompleteBenefitsInactiveTag *SalaryProgramCardTag `dynamic:"true"`
	// tag to be shown on the card when benefits are ACTIVE for the user
	BenefitsActiveTag *SalaryProgramCardTag `dynamic:"true"`

	// card's image url when benefits are INACTIVE
	BenefitsInactiveImageUrl string `dynamic:"true"`
	// card's image url when benefits are INACTIVE, but registration is complete
	RegCompleteBenefitsInactiveImageUrl string `dynamic:"true"`
	// card's image url when benefits are ACTIVE
	BenefitsActiveImageUrl string `dynamic:"true"`

	// card's background color when salary benefits are inactive
	BenefitsInactiveBgColor string `dynamic:"true"`
	// card's background color when salary benefits are inactive, but registration is complete
	RegCompleteBenefitsInactiveBgColor string `dynamic:"true"`
	// card's background color when salary benefits are active
	BenefitsActiveBgColor string `dynamic:"true"`
}

type SalaryProgramTransactionsPage struct {
	Title              string `dynamic:"true"`
	Subtitle           string `dynamic:"true"`
	EmptyTnxErrorImage string `dynamic:"true"`
}

type SalaryTransactionFilters struct {
	MinSalaryAmount                int64    `dynamic:"true"`
	AllowedTransactionProtocols    []string `dynamic:"true"`
	MaxDaysOfTransactionsForSalary int32    `dynamic:"true"`
	// ontology ids which denote that a txn is not an income txn
	NonIncomeRelatedTxnCategoryOntologyIds []string `dynamic:"true"`
}

type SalaryPayslipMailDuringVerificationFailureInfo struct {
	// title for the verification failure top section
	Title string `dynamic:"true"`
	// desc for the verification failure top section
	Desc string `dynamic:"true"`
	// email-id to mail the payslips to
	EmailId             string            `dynamic:"true"`
	EmailSubject        string            `dynamic:"true"`
	EmailBody           string            `dynamic:"true"`
	SharePayslipCta     *SalaryProgramCta `dynamic:"true"`
	SkipSharePayslipCta *SalaryProgramCta `dynamic:"true"`
}

type SalaryProgramConfirmEmployerUpdateNudgeInfo struct {
	// title for the top section
	Title string `dynamic:"true"`
	// desc for the top section
	Desc string `dynamic:"true"`
	// CTA for confirming the employer update nudge
	ConfirmEmployerCta *SalaryProgramCta `dynamic:"true"`
	// CTA to dismiss the employer update nudge
	DismissEmployerCta *SalaryProgramCta `dynamic:"true"`
}

type SalaryProgramSuperImposeTextOnImageDrawPosition struct {
	// distance of starting point of text from left is X - (W * Ax), W is width of text
	// distance of starting point of text from top is Y - (H * Ay), H is height of text
	PosX, PosY, Ax, Ay float64
}

type SalaryProgramSuperImposeTextOnImageDetails struct {
	FontSize float64
	// If the width of text becomes more than MaxTextWidth, then text splits into multiple lines
	MaxTextWidth float64
	DrawPosition *SalaryProgramSuperImposeTextOnImageDrawPosition
}

type SalaryAccountBenefitsSectionBannerInfo struct {
	// useful for switching on/off the banner.
	IsVisible bool `dynamic:"true"`
	// title of the banner
	Title string `dynamic:"true"`
	// font color of the title
	TitleFontColor string `dynamic:"true"`
	// background color for the banner
	BgColor string `dynamic:"true"`
	// icon for left side of the banner
	LeftIconUrl string `dynamic:"true"`
	// icon for left side of the banner
	RightIconUrl string `dynamic:"true"`
}

type SalaryProgramBenefitsCalculatorPage struct {
	ImportantInfoBulletPoints []*ImportantInfoBulletPoint
}

type ImportantInfoBulletPoint struct {
	Html     string
	Deeplink string
}

type P2PInvestment struct {
	Activity           *Activity           `dynamic:"true"`
	RecentActivity     *RecentActivity     `dynamic:"true"`
	WithdrawalDowntime *WithdrawalDowntime `dynamic:"true"`
	CloseJump          *CloseJump          `dynamic:"true"`
	// SchemeName (string) -> P2PInvestmentScheme
	SchemeInfos map[string]*P2PInvestmentScheme `dynamic:"true"`
	// Flags for showing force upgrade screen for Jump V2 launch
	ShowForceUpgradeForAndroid                      bool  `dynamic:"true"`
	ShowForceUpgradeForIOS                          bool  `dynamic:"true"`
	UseUpdatedDashboard                             bool  `dynamic:"true"`
	EnableDummyDataForRenewalFlow                   bool  `dynamic:"true"`
	MinIosVersionForErrorDeeplinkInInvestScreen     int32 `dynamic:"true"`
	MinAndroidVersionForErrorDeeplinkInInvestScreen int32 `dynamic:"true"`
	// Flags for maintaining backward compatibility for 8% scheme tenure string change
	SchemeTenureBackwardCompatibilityIOSVersion     int32 `dynamic:"true"`
	SchemeTenureBackwardCompatibilityAndroidVersion int32 `dynamic:"true"`
	// Flags for maintaining deeplink compatibility for maturity consent launch
	DeeplinkV2CompatibilityAndroidVersion int32                              `dynamic:"true"`
	SchemesAvailability                   map[string]*P2PSchemeAvailability  `dynamic:"true"`
	QuestDashboardBanners                 *P2PInvestmentQuestDashboardBanner `dynamic:"true" ,quest:"component"`
	Flexi8SchemeEnabled                   bool                               `dynamic:"true"`
	// this is done to avoid sending the backward incompatible proto change
	MinIosVersionForDynamicSlider              uint32 `dynamic:"true"`
	MinIosVersionForPromotionLoadingScreen     uint32 `dynamic:"true"`
	MinAndroidVersionForPromotionLoadingScreen uint32 `dynamic:"true"`
	MinIosVersionForActivityPagination         uint32 `dynamic:"true"`
	MinAndroidVersionForActivityPagination     uint32 `dynamic:"true"`
	MinIosVersionForConsentCardV2              uint32 `dynamic:"true"`
	MinAndroidVersionForConsentCardV2          uint32 `dynamic:"true"`
	DisableFlexiSchemeBanners                  bool   `dynamic:"true"`
}

type P2PSchemeAvailability struct {
	IsUnavailable bool `dynamic:"true"`
	// timestamps during which scheme is unavailable (in "2006-01-02 15:04:05" format)
	// should be populated if IsUnavailable is true
	StartTime string `dynamic:"true"`
	EndTime   string `dynamic:"true"`
}

type P2PInvestmentScheme struct {
	// title of the scheme
	// e.g: "Flexi", "Short-term", "Long-term"
	Title string `dynamic:"true"`
	// description of the scheme
	// e.g: "Great for parking a lumpsum amount temporarily"
	Description string `dynamic:"true"`
	// contains all the image URLs for the given scheme
	Images *P2PInvestmentSchemeImages `dynamic:"true"`
}

type P2PInvestmentSchemeImages struct {
	// e.g: images with text "Flexi 7%", "Short-term 8%"...
	PrimaryBadge string `dynamic:"true"`
	// this is different from the Primary, here we don't have the scheme name in the badge
	// e.g: images with text "up to 7%", "up to 8%"...
	SecondaryBadge string `dynamic:"true"`
	// same as secondary but grey
	SecondaryGreyBadge string `dynamic:"true"`
	// different version of Secondary badge which have a lock icon on it
	SecondaryLockedBadge string `dynamic:"true"`
	// different version of Secondary badge which is greyed out and have a lock icon on it
	SecondaryLockedGreyBadge string `dynamic:"true"`
}

type P2PInvestmentQuestDashboardBanner struct {
	// BannerJson represents the serialised json for p2pinvestment.JumpBanner object
	// This is populated from Quest
	BannerJson string `dynamic:"true" ,quest:"variable"`
}

type Activity struct {
	AppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
}

type RecentActivity struct {
	AppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
}

type JupiterMetaData struct {
	IfscCode    string `dynamic:"true"`
	DisplayName string `dynamic:"true"`
	LogoUrl     string `dynamic:"true"`
}

type ProfileEmploymentUpdateView struct {
	EmploymentTypeUiView  *pkgDeeplink.EmploymentTypeUiView
	EmploymentProofUiView *pkgDeeplink.EmploymentProofUiView
	AnnualSalaryUIView    *pkgDeeplink.AnnualSalaryUIView
	AnnualSalaryUIView2   *pkgDeeplink.AnnualSalaryUIView2
	IncomeDiscrepancyView *pkgDeeplink.IncomeDiscrepancyView
}

type UserProfileView struct {
	ProfileEmploymentUpdateView *ProfileEmploymentUpdateView
}

type Cx struct {
	Ticket *CxTicket `dynamic:"true"`
	// IsModelBasedProductCategoryRecommendationEnabled denotes if we will get the product category
	// based recommendation from the DS Model or not
	IsModelBasedProductCategoryRecommendationEnabled bool                   `dynamic:"true"`
	IsAuthRequiredOnCallButtonTap                    bool                   `dynamic:"true"`
	RespondQueryRpcTimeoutDuration                   time.Duration          `dynamic:"true"`
	CxHomeWidgetConfig                               *CxHomeWidgetConfig    `dynamic:"true"`
	CxLandingPageV2Config                            *CxLandingPageV2Config `dynamic:"true"`
	IsLLMSearchDropOffSurveyEnabled                  bool                   `dynamic:"true"`
	IsForceEnabledForNugget                          bool                   `dynamic:"true"`
	// IsNuggetTransactionBotEnabled flag to determine whether to show bot entry point on txn receipt page
	IsNuggetTransactionBotEnabled bool `dynamic:"true"`
}

type CxLandingPageV2Config struct {
	MaxNumberOfOpenIndividualTickets int64  `dynamic:"true"`
	MaxNumberOfTicketsToFetch        uint32 `dynamic:"true"`
	HelpSectionConfig                *HelpSectionConfig
}

type HelpSectionConfig struct {
	FAQDetails []*FAQDetails
}

type FAQDetails struct {
	FAQId    int64
	FAQType  string
	Priority int64
}

type CxHomeWidgetConfig struct {
	IsLoggingEnabledForHomeWidget            bool  `dynamic:"true"`
	MinIosVersionForBrowseFaqDeprecation     int64 `dynamic:"true"`
	MinAndroidVersionForBrowseFaqDeprecation int64 `dynamic:"true"`
}

type CxTicket struct {
	SupportTicketsInAppPageSize int64 `dynamic:"true"`
	// flag to determine whether to show user's latest ticket in app's home page
	IsDisplayingTicketOnHomeEnabled bool `dynamic:"true"`
	// max duration till which we will show the ticket update on home support ticket banner
	// adding this as after certain period of time ticket update is irrelevant for user
	MaxDurationToShowTicketUpdateOnHome time.Duration `dynamic:"true"`
	// feature a flag to represent whether new status note needs to be displayed
	IsStatusNoteV2Enabled bool `dynamic:"true"`
}

type Goals struct {
	GoalDiscovery                               *GoalDiscovery                               `dynamic:"true"`
	GoalDiscoveryInExistingInvestmentInstrument *GoalDiscoveryInExistingInvestmentInstrument `dynamic:"true"`
	GoalAlterOptions                            *GoalAlterOptions
	GoalRemoveOptions                           *GoalRemoveOptions
}

type GoalDiscovery struct {
	Enable                     bool `dynamic:"true"` // if false, goal discovery is hidden
	AllowedGroups              []commontypes.UserGroup
	IconUrl                    string
	Title                      string
	Description                string
	AddCtaText                 string
	SliderTitle                string
	SliderInfoText             string
	SliderInfoTextMaxGoal      string
	SliderInfoTextColor        string
	SliderInfoTextWarningColor string
	FinishVideoKycDescription  string
	UpgradeText                string
}

type GoalDiscoveryInExistingInvestmentInstrument struct {
	Enable                    bool `dynamic:"true"` // if false, goal discovery in existing investment instrument is hidden
	AllowedGroups             []commontypes.UserGroup
	IconUrl                   string
	Title                     string
	AddCtaText                string
	FinishVideoKycDescription string
	UpgradeText               string
	BottomSheetTitle          string
	BottomSheetDescription    string
	SliderInfoText            string
	SliderInfoTextColor       string
	CancelCtaText             string
	ProceedCtaText            string
}

type GoalAlterOptions struct {
	IconUrl           string
	BottomSheetTitle  string
	SliderTitle       string
	RemoveGoalCtaText string
	CancelCtaText     string
	ProceedCtaText    string
}

type GoalRemoveOptions struct {
	IconUrl        string
	Title          string
	Description    string
	CancelCtaText  string
	ProceedCtaText string
}

type UpiOnboardingParams struct {
	PopularBanksIfsc              []string `dynamic:"true"`
	VpaMigrationDeeplink          *deeplinkCfg.Deeplink
	LinkOtherBankAccountsDeeplink *deeplinkCfg.Deeplink
	// we are using string to string map, as enum to enum or string to enum map is not supported in dynamic config for now
	EntrypointToActionMap map[string]string `dynamic:"true"`
}

type WithdrawalDowntime struct {
	IsEnable  bool   `dynamic:"true"`
	StartTime string `dynamic:"true"`
	EndTime   string `dynamic:"true"`
}

type AppLogs struct {
	// max number of messages we should process in app logs stream
	// if we get more messages from the client than the threshold, we will close the stream and return failure
	MaxMessageCountThreshold int `dynamic:"true"`
}

type QrDeeplinkParams struct {
	MinAndroidVersionForAmountScreenDeeplink uint32 `dynamic:"true"`
	MinIosVersionForAmountScreenDeeplink     uint32 `dynamic:"true"`
}

type AppBottomBarParams struct {
	// key: Icon id of app bottom bar icon, value: Icon id of corresponding sticky icon
	StickyIconMapping map[string]string `dynamic:"true"`
	AppBarIcons       []string          `dynamic:"true"`
	DefaultIconType   string            `dynamic:"true"`
}

type Icon struct {
	// In this array, user will be shown the icon property that first satisfies the platform constraint
	IconWithVersionConstraints []*IconWithVersionConstraints
}

type IconWithVersionConstraints struct {
	VersionConstraints    *cfg.PlatformVersionCheck
	IconType              string
	ImageUrl              string
	LottieUrl             string
	VisualElementWidth    int32
	VisualElementHeight   int32
	OnclickImageUrl       string
	Title                 string
	FontColour            string
	FontStyle             string
	TitleOnSelection      string
	FontColourOnSelection string
	FontStyleOnSelection  string
}

type VersionConstraints struct {
	MinVersion int32 `dynamic:"true"`
	MaxVersion int32 `dynamic:"true"`
}

type HomeRevampParams struct {
	AllHomeIcons                            map[string]*Icon
	AppBottomBarParams                      *AppBottomBarParams `dynamic:"true"`
	HomeWidgetParams                        *HomeWidgetParams   `dynamic:"true"`
	SegmentLayoutMapping                    map[string]*HomeWidgetParams
	HomeLayoutConfigurationV2Params         *HomeLayoutConfigurationV2Params `dynamic:"true"`
	RecentActivitiesParams                  *RecentActivitiesParams          `dynamic:"true"`
	UpcomingActivitiesParams                *UpcomingActivitiesParams        `dynamic:"true"`
	HomeNudgeParams                         *HomeNudgeParams                 `dynamic:"true"`
	MaxDurationSinceOnbForNewAppWalkthrough time.Duration
	ExploreSections                         []*ExploreSection
	NRExploreSections                       []*ExploreSection
	WealthAnalyserExploreSections           []*ExploreSection
	ShortcutIconTypeToDetailsMap            map[string]*ShortcutIconDetails
	RecentUpcomingWidgetParams              *RecentUpcomingWidgetParams `dynamic:"true"`
	LowBalAlertParams                       *LowBalAlertParams          `dynamic:"true" ,quest:"component"`
	HomeLayoutParams                        *HomeLayoutParams           `dynamic:"true" ,quest:"component"`
	HomeShortcutParams                      *HomeShortcutParams         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2Params                      *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2LiteParams                  *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2LiteCCParams                *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2LitePLParams                *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2FiNRParams                  *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2D0To7Params                 *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2D8To14Params                *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2D15To28Params               *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2WealthAnalyserParams        *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2WealthAnalyserNoAssetParams *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2UpiTPAPConnected 	        *HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	HomeLayoutV2SADropOffParams 			*HomeLayoutV2Params         `dynamic:"true" ,quest:"component"`
	LayoutBySegFeatureRelease               *cfg.PlatformVersionCheck
	HomeLayoutUIRevamp                      *cfg.PlatformVersionCheck
	ToShowTieringInfoInSavingsDashboard     bool          `dynamic:"true"`
	TieringDangerCutoffDuration             time.Duration `dynamic:"true"`
	DashboardVersionV2ReleaseConfig         *cfg.PlatformVersionCheck
	NewSearchUIReleaseConfig                *cfg.PlatformVersionCheck
	RewardsWidgetSeparationReleaseConfig    *cfg.PlatformVersionCheck `dynamic:"true"`
	FeatureWidgetsReleaseConfig             *cfg.PlatformVersionCheck `dynamic:"true"`
	EnableHomeProfileInfoV2                 bool                      `dynamic:"true" ,quest:"variable"`
	BgColorForBottomNavBarReleaseConfig     *cfg.PlatformVersionCheck `dynamic:"true"`
	NetWorthDashboardCardReleaseConfig      *cfg.PlatformVersionCheck `dynamic:"true"`
	EnableSADashboardAddMoneyV2             bool                      `dynamic:"true" ,quest:"variable"`
	NreSavingsDashboardReleaseConfig        *cfg.PlatformVersionCheck `dynamic:"true"`
	NroSavingsDashboardReleaseConfig        *cfg.PlatformVersionCheck `dynamic:"true"`
	MutualfundDashboardCardReleaseConfig    *cfg.PlatformVersionCheck `dynamic:"true"`
	CreditScoreDashboardCardReleaseConfig   *cfg.PlatformVersionCheck `dynamic:"true"`
	EpfDashboardCardReleaseConfig           *cfg.PlatformVersionCheck `dynamic:"true"`
	DcInternationalWidgetReleaseConfig      *cfg.PlatformVersionCheck `dynamic:"true"`
}

type HomeExploreConfig struct {
	EnableAskFiSection           bool                          `dynamic:"true" ,quest:"variable"`
	EnableFeedbackSection        bool                          `dynamic:"true" ,quest:"variable"`
	PageTitle                    *Text                         `dynamic:"true"`
	ExploreFeedbackSectionConfig *ExploreFeedbackSectionConfig `dynamic:"true"`
	ShortcutStickyIcon           *ShortcutStickyIcon           `dynamic:"true"`
	ToastText                    *Text                         `dynamic:"true"`
	FullVisualCardSectionConfig  *FullVisualCardSectionConfig  `dynamic:"true"`
}

type FullVisualCardSectionConfig struct {
	FeatureFlag *app.FeatureConfig `dynamic:"true"`
	// List of visual cards to be shown in the section
	VisualCards []*ExploreVisualCardConfig
}

type ExploreVisualCardConfig struct {
	// Unique identifier for the Explore visual card,
	// used to enable custom logic configurations based on the card ID.
	Id string
	// Title of the visual card
	Title *Text
	// Visual element card to be shown if the card is primary (or first) slot among the group.
	PrimaryVisualElementCard *VisualElementCard
	// Visual element card to be shown if the card is not in primary slot.
	SecondaryVisualElementCard *VisualElementCard
	// Deeplink Screen
	Deeplink string
	// Background color of the card
	BgColor string
}

type VisualElementCard struct {
	// Visual element image of the card
	FullCardImage *VisualElementImage
	// Visual element lottie of the card
	// Note: If FullCardLottie is present, FullCardImage will be ignored
	FullCardLottie *VisualElementLottie
}

type ExploreFeedbackSectionConfig struct {
	Highlight *VisualElementImage `dynamic:"true"`
	Title     *Text               `dynamic:"true"`
	Subtitle  *Text               `dynamic:"true"`
	Button    *IconTextComponent  `dynamic:"true"`
	BgColor   string              `dynamic:"true"`
}

type ShortcutStickyIcon struct {
	VisualElement *VisualElementImage `dynamic:"true"`
	BgColor       string              `dynamic:"true"`
	Shadow        *Shadow             `dynamic:"true"`
	Title         *Text               `dynamic:"true"`
}

type ShortcutIconDetails struct {
	Title       string
	ImageUrl    string
	ImageUrlV2  string
	FeatureTag  string
	Deeplink    string
	BorderColor string
	BgColor     BgConfig
}

type LowBalAlertParams struct {
	// Title shown on warning icon. This will be empty if title not required
	BalTitle string `dynamic:"true" ,quest:"variable"`
	// Balance text colour
	BalColor string `dynamic:"true" ,quest:"variable"`
	// Balance warning image
	BalImage string `dynamic:"true" ,quest:"variable"`
}

type HomeLayoutParams struct {
	// Layout is given as a Json object,
	// layoutconfiguration.HomeLayout object is serialized and store in the variable from quest
	LayoutJson string `dynamic:"true" ,quest:"variable"`
	// Json for explore icons section
	// []*ExploreSection object is serialized and stored in the variable from quest
	ExploreSectionsJson string `dynamic:"true" ,quest:"variable"`
}

type HomeShortcutParams struct {
	DefaultShortcutsFromExperiment string                                     `dynamic:"true" ,quest:"variable"`
	ShouldShowExploreFiShortcut    bool                                       `dynamic:"true" ,quest:"variable"`
	UserTypeToShortcutParamsMap    map[string]*UserTypeSpecificShortcutParams `dynamic:"true,readonlymap" ,quest:"component"`
	AreaToSegmentConfigMap         map[string]*ShortcutsSegmentConfig
	PlatformVersionDetails         *cfg.PlatformVersionCheck
	MaximumNoOfShortcuts           uint32
	Title                          string           `dynamic:"true" ,quest:"variable"`
	SoftIntentToShortcutMap        map[string]int32 `dynamic:"true"`
}

type UserTypeSpecificShortcutParams struct {
	ShowExploreFiShortcut bool `dynamic:"true" ,quest:"variable"`
	HideAddOrEditShortcut bool `dynamic:"true"`
	DefaultShortcuts      []int32
}

type ShortcutsSegmentConfig struct {
	SegmentId         string
	ShortcutEnumValue int32
}

type HomeLayoutV2Params struct {
	DashboardVersion                                              string `dynamic:"true" ,quest:"variable"`
	ZeroStateDashboardCardVariant                                 string `dynamic:"true" ,quest:"variable"`
	SegmentLayoutV2Mapping                                        []*SegmentLayoutV2
	LayoutV2Json                                                  string `dynamic:"true" ,quest:"variable"`
	DefaultSelectedIconIdForBottomNavBarWidget                    string `dynamic:"true" ,quest:"variable"`
	BgColorForBottomNavBarWidget                                  string `dynamic:"true" ,quest:"variable"`
	SlotIdToScreenElementIdMap                                    *SlotIdToScreenElementIdMap
	WidgetToIdMapping                                             *WidgetToIdMapping
	SlotIdToScreenElementIdsMap                                   *SlotIdToScreenElementIdsMap
	HomeElementAttributes                                         *HomeElementAttributes
	SegmentExpressionToDefaultSelectedIconIdForBottomNavBarWidget []*SegmentExpressionToDefaultSelectedIconIdForBottomNavBarWidget
}

type SegmentExpressionToDefaultSelectedIconIdForBottomNavBarWidget struct {
	SegmentExpression                          string
	DefaultSelectedIconIdForBottomNavBarWidget string
}

type SlotIdToScreenElementIdsMap struct {
	TopNavBarSlotSection    *TopNavBarSlotV2Section
	DashboardSlotSection    map[string][]string
	VerticalSlotSection     map[string][]string
	BottomNavBarSlotSection map[string][]string
	StickyIconSlotSection   map[string][]string
}

type TopNavBarSlotV2Section struct {
	LeftSlots  map[string][]string
	RightSlots map[string][]string
}

type HomeElementAttributes struct {
	/*
		will contain entries of the form -
		"debit-card":
			- Expression: "IsMember('us-stocks-segment')"
			  Score: 0
			- Expression: "IsMember('loans-segment') && IsMember('cc-segment')"
			  Score: 1
	*/
	ElementIdToSegmentExpressionScores map[string][]*HomeElementExpressionScore
	/*
		will contain entries of the form -
		"debit-card":
			- Expression: "IsHardIntent('SA') || IsSoftIntent('SA') || IsEligible('SA') || IsActivated('SA')"
			  Score: 1
			- Expression: "!IsHardIntent('SA') && !IsSoftIntent('SA') && !IsEligible('SA') && !IsActivated('SA')"
			  Score: 0
	*/
	ElementIdToFeatureLifecycleExpressionScores map[string][]*HomeElementExpressionScore
	/*
		will contain entries of the form -
		"debit-card":
			- Expression: "!(IsUserGroup('INTERNAL') || IsUserGroup('DC_INTERNAL'))"
			  Score: -1
	*/
	ElementIdToUserGroupExpressionScores map[string][]*HomeElementExpressionScore
	/*
		will contain entries of the form -
		"debit-card":
			- Expression: "IsCrossAttachIntent('PRODUCT_TYPE_PERSONAL_LOANS')"
			  Score: 0
	*/
	ElementIdToCrossAttachExpressionScores map[string][]*HomeElementExpressionScore
}

type HomeElementExpressionScore struct {
	Expression string
	Score      float32
}

type HomeRevampParamsForUnitTests struct {
	HomeLayoutV2ParamsWithoutAttributes       *HomeLayoutV2Params `dynamic:"true"`
	HomeLayoutV2ParamsWithAttributes          *HomeLayoutV2Params `dynamic:"true"`
	HomeLayoutV2ParamsWithNoDuplicateElements *HomeLayoutV2Params `dynamic:"true"`
	HomeLayoutV2ParamsWithPriorityElements    *HomeLayoutV2Params `dynamic:"true"`
}

type SegmentLayoutV2 struct {
	SegmentIdExpression      string
	PlatformVersionCheck     *cfg.PlatformVersionCheck
	SlotIdToScreenElementMap *SlotIdToScreenElementIdMap
}

type HomeWidgetParams struct {
	TopWidgetList             []string                   `dynamic:"true"`
	BottomWidgetList          []string                   `dynamic:"true"`
	HomeWidgetTopNavBarParams *HomeWidgetTopNavBarParams `dynamic:"true"`
	HomeWidgetDashboardParams *HomeWidgetDashboardParams
	WidgetCriteriaMap         map[string]*WidgetCriteria
	// For layoutV2
	TopSectionWidgetList    []string `dynamic:"true"`
	MiddleSectionWidgetList []string `dynamic:"true"`
	BottomNavBarWidgetList  []string
}

type HomeLayoutConfigurationV2Params struct {
	NewUserWalkthroughElementIdConfig []string `dynamic:"true"`
	SectionSlotsConfig                *SectionSlotsConfig
	SectionScreenElementsConfig       *SectionScreenElementsConfig
}

type WidgetToIdMapping struct {
	TopNavBarSection    string
	DashboardSection    string
	MiddleSection       string
	BottomNavBarSection string
}

type SectionSlotsConfig struct {
	TopNavBarSectionSlotsConfig    *TopNavBarSectionSlotsConfig
	DashboardSectionSlotsConfig    []string
	VerticalSectionSlotsConfig     []string
	BottomNavBarSectionSlotsConfig []string
	StickyIconSectionSlotsConfig   []string
}

type TopNavBarSectionSlotsConfig struct {
	LeftSlots  []string
	RightSlots []string
}

type HomeLayoutConfigV2 struct {
	SlotsToScreenElementsMap SlotIdToScreenElementIdMap
}

type SectionScreenElementsConfig struct {
	TopNavBarSection    map[string]*ScreenElementProperties
	DashboardSection    map[string]*ScreenElementProperties
	VerticalSection     map[string]*ScreenElementProperties
	BottomNavBarSection *BottomNavBarSection
}

type BottomNavBarSection struct {
	StickyIconMapping     map[string]*ScreenElementProperties
	ScreenElementsMapping map[string]*ScreenElementProperties
}

type ScreenElementIdToSlotIdMap struct {
	TopNavBarSlotSection *TopNavBarSlotSection
	DashboardSlotSection map[string]string
	VerticalSlotSection  map[string]string
	BottomNavBarSection  map[string]string
}

type SlotIdToScreenElementIdMap struct {
	LayoutId                string
	TopNavBarSlotSection    *TopNavBarSlotSection
	DashboardSlotSection    map[string]string
	VerticalSlotSection     map[string]string
	BottomNavBarSlotSection map[string]string
	StickyIconSlotSection   map[string]string
}

type TopNavBarSlotSection struct {
	LeftSlots  map[string]string
	RightSlots map[string]string
}

type WidgetCriteria struct {
	MinDaysSinceOnboarding int
}

type HomeWidgetTopNavBarParams struct {
	RightIconIds []string `dynamic:"true"`
	LeftIconIds  []string `dynamic:"true"`
}

type HomeWidgetDashboardParams struct {
	HomeDashboardViews []*HomeDashboardView
}

type UserProfile struct {
	EnableEditEmployment      bool                   `dynamic:"true"`
	EnableEditCommAddress     bool                   `dynamic:"true"`
	ShowIncomeAbsolute        bool                   `dynamic:"true"`
	ProfileHeaderV2MinVersion *MinVersionConstraints `dynamic:"true"`
	IsEmailEditableConfig     *app.FeatureConfig     `dynamic:"true"`
	// IsContactDetailsEditable is a feature flag to enable/disable the edit contact details feature
	IsContactDetailsEditable *app.FeatureConfig `dynamic:"true"`
	// IsAadhaarCommsAddressUpdateEnabled is a feature flag to enable/disable the aadhaar-based flow for updating the communication address
	IsAadhaarCommsAddressUpdateEnabled *app.FeatureConfig `dynamic:"true"`
	// Used for showing the FAQ category upon navigating help section in the user profile page
	HelpSectionFAQ *HelpSectionFAQ `dynamic:"true"`
}

// OrderStatusIconUrl contains icon url shown in order status.
type OrderStatusIconUrl struct {
	Success string
	Fail    string
	Pending string
}

type Chat struct {
	Title    string `dynamic:"true"`
	SubTitle string `dynamic:"true"`
}

type Text struct {
	Content    string `dynamic:"true"`
	FontColor  string `dynamic:"true"`
	BgColor    string `dynamic:"true"`
	FontFamily string `dynamic:"true"`
	FontSize   string `dynamic:"true"`
	FontStyle  string `dynamic:"true"`
}

type RadialGradient struct {
	CenterX     int32    `dynamic:"true"`
	CenterY     int32    `dynamic:"true"`
	OuterRadius int32    `dynamic:"true"`
	Colours     []string `dynamic:"true"`
}

type BlockShadow struct {
	Height int32  `dynamic:"true"`
	Blur   int32  `dynamic:"true"`
	Colour string `dynamic:"true"`
}

type CloseJump struct {
	IsEnable  bool   `dynamic:"true"`
	StartDate string `dynamic:"true"`
	EndDate   string `dynamic:"true"`
	Icon      string `dynamic:"true"`
	Title     string `dynamic:"true"`
	SubTitle  string `dynamic:"true"`
}

type RecentActivitiesParams struct {
	EmptySlotImages       []string              `dynamic:"true"`
	MaxNumberOfEmptySlots int32                 `dynamic:"true"`
	MaxNumberOfSlots      int32                 `dynamic:"true"`
	HomeTxnTypeImageUrls  *HomeTxnTypeImageUrls `dynamic:"true"`
}

type UpcomingActivitiesParams struct {
	EmptySlotImages            []string `dynamic:"true"`
	MaxNumberOfEmptySlots      int32    `dynamic:"true"`
	MaxNumberOfSlots           int32    `dynamic:"true"`
	StraightArrowNESmallImgURL string   `dynamic:"true"`
	ZigZagArrowNESmallImgURL   string   `dynamic:"true"`
	// starting from current time what duration to consider for upcoming activities
	NextDurationToConsider time.Duration `dynamic:"true"`
}

type AmountBadgeIconUrlMap struct {
	DebitIconUrl    string `dynamic:"true"`
	CreditIconUrl   string `dynamic:"true"`
	SavingIconUrl   string `dynamic:"true"`
	FailedIconUrl   string `dynamic:"true"`
	PendingIconUrl  string `dynamic:"true"`
	ReversedIconUrl string `dynamic:"true"`
}

type StandardNudgeStyle struct {
	Header             *Text
	Title              *Text
	SubTitle           *Text
	BgColor            string
	DismissCtaUrl      string
	CheckmarkFillColor string
	Shadow             *BlockShadow
	Action             *CTA
}

type GTMNudgeStyle struct {
	Header             *Text
	Title              *Text
	BgColors           []string
	DismissCtaUrl      string
	CheckmarkFillColor string
	Shadow             *BlockShadow
	Action             *CTA
}

type HomeNudgeTheme struct {
	// to theme the nudge in standard style
	StandardNudgeStyle *StandardNudgeStyle
	// to theme the nudge in gtm style
	GTMNudgeStyle *GTMNudgeStyle
}

type HomeNudgeParams struct {
	// map of theme name to config for home nudges
	HomeNudgeThemeMap map[string]*HomeNudgeTheme
	// for explore nudge app version check on android
	MinAndroidAppVersionToSupportExploreNudge uint32
	// for explore nudge app version check on ios
	MinIOSAppVersionToSupportExploreNudge uint32
	// for gtm nudge app version check on android
	MinAndroidAppVersionToSupportGTMNudge uint32
	// for gtm nudge app version check on ios
	MinIOSAppVersionToSupportGTMNudge uint32
	// for v2 standard nudge app version check on android
	MinAndroidAppVersionToSupportV2StandardNudge uint32
	// for v2 standard nudge app version check on ios
	MinIOSAppVersionToSupportV2StandardNudge uint32
	// flag to toggle new nudge animation
	IsNudgeEntryAnimationEnabled bool `dynamic:"true"`
	// feature config to enable/disable banners via nudges
	BannerFeatureReleaseConfig *app.FeatureConfig `dynamic:"true"`
	// Params related to tiering
	TieringParams *TieringParams `dynamic:"true"`
	// Params to overwrite the banner start index on app load
	// Set this to -1 to not overwrite the banner start index
	ForceBannerStartIndex int32 `dynamic:"true"`
}

type TieringParams struct {
	GraceWindowDuration     time.Duration `dynamic:"true"`
	DowngradeWindowDuration time.Duration `dynamic:"true"`
	ToUseV2States           bool          `dynamic:"true"`
}

type HomeTxnTypeImageUrls struct {
	CreditUrl   string `dynamic:"true"`
	DebitUrl    string `dynamic:"true"`
	InvestUrl   string `dynamic:"true"`
	PendingUrl  string `dynamic:"true"`
	FailedUrl   string `dynamic:"true"`
	ReversedUrl string `dynamic:"true"`
}

type MandatoryMinKycAddFundConfig struct {
	IsEnabled     bool `dynamic:"true"`
	MinimumAmount *moneyPb.Money
}

type AnalyserFeedbackParams struct {
	MaxFeedbackEntriesFetchLimit int32 `dynamic:"true"`
	// cool off duration for an analyser feedback, once a user submits the feedback for it
	ExitAnalyserFeedbackCoolOff time.Duration `dynamic:"true"`
	// cool off duration for a lens feedback, once a user submits the feedback for it
	OnScreenLensFeedbackCoolOffDuration time.Duration `dynamic:"true"`
	// cool off duration for an analyser, once a user submits feedback for any other analyser
	AcrossAnalyserFeedbackCoolOff time.Duration      `dynamic:"true"`
	AnalyserFeedbackRateLimit     *FeedbackRateLimit `dynamic:"true"`
}

// FeedbackRateLimit - defines the period and maximum of feedbacks that can be asked for an entity (analyser/lens) in that duration
type FeedbackRateLimit struct {
	TimePeriod          time.Duration `dynamic:"true"`
	MaxFeedbackAskLimit int32         `dynamic:"true"`
}

type ExploreSection struct {
	SectionType string
	Title       string
	BgColour    string
	IconTypes   []string
}

type ExploreIcon struct {
	Title    string
	Type     string
	Deeplink string
	ImageUrl string
}

// PayParams contains params related to payments
type PayParams struct {
	// list of payment protocols for which location is not mandatory
	ProtocolsWithOptionalLocation []string `dynamic:"true"`
	// params for upi number
	UpiNumberParams *UpiNumberParams `dynamic:"true"`
	// retry-timer duration for GetOrderStatus RPC
	RetryTimerDurationGetOrderStatus time.Duration `dynamic:"true"`
}

type UpiNumberParams struct {
	// title for chat head badges for upi number
	UpiNumberChatHeadBadgeTitle string
	// delimiter for upi number chat head
	UpiNumberChatHeadTitleDelimiter  string
	ManageUpiNumberActionDescription string                       `dynamic:"true"`
	UpiNumberIntroScreenOptions      *UpiNumberIntroScreenOptions `dynamic:"true"`
	// ManageUpiNumberScreenOptions stores details on ManageUpiNumberScreen
	ManageUpiNumberScreenOptions *ManageUpiNumberScreenOptions
}

type USStocks struct {
	MorningStarBasedUIFlag *MorningStarBasedUIFlag `dynamic:"true"`
	PoolRetryTime          int32                   `dynamic:"true"`
	MaxPoolDuration        int64                   `dynamic:"true"`
	// Retry strategy for price updates stream RPC to reattempt BE connection in case of failures
	PriceUpdatesRetryStrategy *cfg.RetryParams
	Explore                   *UsStocksExplore
	// Bank account details of the usstocks broker
	USStocksBrokerBeneficiaryDetails *InternationAccountBeneficiaryDetails `dynamic:"true"`
	VersionSupport                   *VersionSupport                       `dynamic:"true"`

	// Timeout after which client should stop polling for buy order status
	BuyTimeoutInMilliseconds int32 `dynamic:"true"`

	// URL to load price chart web-views
	PriceGraphURL string `dynamic:"true"`

	// URL to load ratio chart web-views
	RatioGraphURL string `dynamic:"true"`

	// Represent suggest amount in usstocks buy screen
	BuySuggestedAmount *USSSuggestedAmount

	// UssQuantityDetails represents the min and max buy sell quantity details
	UssQuantityDetails *USSQuantityDetails

	// Represents min amount in USD that can be entered by user while selling stocks
	MinSellAmount *types.Money

	// Represent tag for instant purchase to show in UI
	InstantPurchaseTag string `dynamic:"true"`

	// Clients can cache the web-views with required JS libraries loaded and just call the JS browser window manipulation functions
	// with relevant data to render/re-render the chart whenever needed.
	// The web view caching strategy is based on these timestamps. Clients can store the timestamps locally and
	// invalidate the cache whenever the timestamp doesn't match with the one stored by them already.

	// Timestamp of when the web view code for rending price charts was last updated
	PriceGraphUpdatedAt int64 `dynamic:"true"`

	// Timestamp of when the web view code for rending ratio charts was last updated
	RatioGraphUpdatedAt int64 `dynamic:"true"`

	A2FormUrl string
	// if true, then user's won't be able to buy stocks
	IsBuyDisabled bool `dynamic:"true"`
	// if true, then user's won't be able to sell stocks
	IsSellDisabled bool `dynamic:"true"`

	// First app version to inline error during buy flow in android
	MinAndroidAppVersionToSupportInlineErrDuringBuyFlow uint32 `dynamic:"true"`
	// First app version to inline error during buy flow in iOS
	MinIOSAppVersionToSupportInlineErrDuringBuyFlow uint32 `dynamic:"true"`

	// ETF contains etf related check
	ETF *ETF `dynamic:"true"`

	// Represent suggest amount in stocks add funds screen
	AddFundsAmountConfig *USSWalletFundingAmountConfig

	// Represent suggest amount in stocks withdraw funds screen
	WithdrawFundsAmountConfig *USSWalletFundingAmountConfig

	// flag to enable instant funding option to users
	// If set to true, user will be provided with option to opt for instant funding
	// else no option is displayed to user
	EnableInstantFundingOption bool `dynamic:"true"`
	// flag controls if sof transactions analysis flow is on
	IsSofAnalysisFlowActive bool `dynamic:"true"`
	// flag controls to disable withdrawal order
	DisableAddFunds bool `dynamic:"true"`
	// flag controls to disable withdrawal order
	DisableWithdrawFunds bool `dynamic:"true"`

	// map of duration to list of stock ticker, returns, tag
	HighReturnStocks map[string][]*HighReturnStock

	// Brokerage stores configuration related to commission that will be charged from user's wallet on trade orders
	Brokerage *usstocks.Brokerage `dynamic:"true"`
	// duration which bottom sheet is cool off
	WithdrawBottomSheetDisplayDuration time.Duration `dynamic:"true"`

	// First app version to support suitability refresh bottom sheet during add funds in android
	MinAndroidAppVersionToSupportSuitabilityBottomSheet uint32 `dynamic:"true"`
	// First app version to support suitability refresh bottom sheet  during add funds in IOS
	MinIOSAppVersionToSupportSuitabilityBottomSheet uint32 `dynamic:"true"`
	// experiment to disable stock price in explore page
	IsCollectionPriceDisabled bool `dynamic:"true" ,quest:"variable"`
	// minimum ios version to support price update disable
	MinIOSAppVersionToSupportDisablePrice uint32 `dynamic:"true"`
	// minimum android version to support price update disable
	MinAndroidAppVersionToSupportDisablePrice uint32 `dynamic:"true"`

	// Start and end time of downtime at broker's end
	BrokerDowntime *TimestampBasedDowntime `dynamic:"true"`

	// quest var to control if double pin flow is enabled for user
	IsDoublePinAddFundFlowEnabled bool `dynamic:"true" ,quest:"variable"`

	SIPProjectionConfig *SIPProjectionConfig
	// duration between two subsequent withdraw order to be placed by user
	WithdrawFundsBlockedDuration time.Duration `dynamic:"true"`
}

type MorningStarBasedUIFlag struct {
	// Disable all UI components using morning star as source
	Disable bool `dynamic:"true"`
}
type SIPProjectionConfig struct {
	AnnualBankFDRate float64

	Nifty50Value float64
}

type TimestampBasedDowntime struct {
	IsEnabled bool      `dynamic:"true"`
	StartTs   time.Time `dynamic:"true"`
	EndTs     time.Time `dynamic:"true"`
}

type ETF struct {
	IsEnabled bool `dynamic:"true"`
}

type USSSuggestedAmount struct {
	// represent default value to be show in display
	DefaultAmount *types.Money
	// represent list of possible value to be show
	SuggestedAmount []*types.Money
	// represent max  possible value enter by user
	MaxAmount *types.Money
	// represent min possible value enter by user
	MinAmount *types.Money
}

type USSQuantityDetails struct {
	MinBuyQuantity  float64
	MaxBuyQuantity  float64
	MinSellQuantity float64
	// represents the default quantity to show in display
	DefaultQuantity float64
}

type USSWalletFundingAmountConfig struct {
	// represent default value to be show in display in USD
	DefaultAmount *types.Money
	// represent list of possible value to be show
	SuggestedAmountConfig []*USSSuggestedAmountConfig
	// represent max  possible value enter by user in USD
	MaxAmount *types.Money
	// represent min possible value enter by user in USD
	MinAmount *types.Money
}

type USSSuggestedAmountConfig struct {
	// in USD
	Amount  *types.Money
	Tag     string
	IconUrl string
}

type VersionSupport struct {
	// First app version to support pre-req check in on-boarding flow in android
	MinAndroidAppVersionToSupportOnboardingPreRequisites uint32 `dynamic:"true"`
	// First app version to support pre-req check in on-boarding flow in iOS
	MinIOSAppVersionToSupportOnboardingPreRequisites uint32 `dynamic:"true"`
	// First app version to support vkyc validation check in on-boarding flow in android
	MinAndroidAppVersionToSupportVkycCheck uint32 `dynamic:"true"`
	// First app version to support vkyc validation check in on-boarding flow in iOS
	MinIOSAppVersionToSupportVkycCheck uint32 `dynamic:"true"`
	// First app version to support average balance check in on-boarding flow in android
	MinAndroidAppVersionToSupportPanAadhaarLinkCheck uint32 `dynamic:"true"`
	// First app version to support average balance check in on-boarding flow in iOS
	MinIOSAppVersionToSupportPanAadhaarLinkCheck uint32 `dynamic:"true"`
	// First app version to support announcements in Symbol details screen in android
	MinAndroidAppVersionToSupportAnnouncementsInSymbolDetails uint32 `dynamic:"true"`
	// First app version to support average balance check in on-boarding flow in ios
	MinIOSAppVersionToSupportAnnouncementsInSymbolDetails uint32 `dynamic:"true"`
	// First app version to support Fi-lite feature in android
	MinAndroidAppVersionToSupportFiLite uint32 `dynamic:"true"`
	// First app version to support Fi-lite feature in iOS
	MinIOSAppVersionToSupportFiLite uint32 `dynamic:"true"`
	// First app version to support profile suitability check feature in android
	MinAndroidAppVersionToSupportProfileSuitabilityCheck uint32 `dynamic:"true"`
	// First app version to support profile suitability check feature in iOS
	MinIOSAppVersionToSupportProfileSuitabilityCheck uint32 `dynamic:"true"`

	MinAndroidAppVersionToSupportDropdownForCurrentlyInvestedInstruments uint32 `dynamic:"true"`
	MinIOSAppVersionToSupportDropdownForCurrentlyInvestedInstruments     uint32 `dynamic:"true"`

	MinAndroidAppVersionToSupportNavBarAnimation uint32 `dynamic:"true"`
	MinIOSAppVersionToSupportNavBarAnimation     uint32 `dynamic:"true"`

	MinAndroidAppVersionToSupportWalletHeaderPromo uint32 `dynamic:"true"`
	MinIOSAppVersionToSupportWalletHeaderPromo     uint32 `dynamic:"true"`

	MinAndroidAppVersionToSupportEmploymentDetailsInSuitability uint32 `dynamic:"true"`
	MinIOSAppVersionToSupportEmploymentDetailsInSuitability     uint32 `dynamic:"true"`

	MinAndroidAppVersionToSupportSimilarStocks uint32 `dynamic:"true"`
	MinIOSAppVersionToSupportSimilarStocks     uint32 `dynamic:"true"`

	// iOS layout auto-adjusts to fill the horizontal space in case of a single tab.
	// Hence, it doesn't require a version check.
	MinAndroidAppVersionToSupportHiddenActivityTab uint32 `dynamic:"true"`
}

// UsStocksExplore contains config related to US stocks explore screen
type UsStocksExplore struct {
	// sections are vertically stacked section on explore screen
	// e.g: search bar, top gainers, learning section...
	Sections []*UsStocksExploreSection
}

type UsStocksExploreSection struct {
	// represents ComponentType in api/frontend/usstocks/usstocks.proto
	// e.g: COMPONENT_TYPE_STOCK_COLLECTION_VARIANT_TITLE_TOP, COMPONENT_TYPE_STOCK_COLLECTION_VARIANT_TITLE_LEFT
	ComponentType string
	// collection id to fetch the stocks from
	// applicable only if the Type is a stock collection
	CollectionId string
	// background color for the section
	BackgroundColor string
}

type InternationAccountBeneficiaryDetails struct {
	// name of the account holder
	// eg: Alpaca Securities LLC
	Name string `dynamic:"true"`
	// address of the beneficiary
	// eg: 3 East Third Ave Suite 233 San Mateo CA 94401 USA
	Address string `dynamic:"true"`
	// beneficiary account number or IBAN
	// eg: ***************
	AccountNumber string `dynamic:"true"`
	// beneficiary bank and branch name
	// eg: BMO Harris Bank NA - BMO Harris Bank 111. W. Monroe Street Chicago IL 60603 USA
	BankAndBranch string `dynamic:"true"`
	// swift code of the benificiary bank
	// eg: HATRUS44
	SwiftCode string `dynamic:"true"`
	// ABA Routing No. / Sort Code/ BS / Branch & Transit Code (*)
	// eg: *********
	TransitCode string `dynamic:"true"`
	// An intermediary bank is a middleman between an issuing bank and a receiving bank
	// An intermediary bank is needed for international fund transfers
	// eg: BMO Harris Bank NA - BMO Harris Bank 111. W. Monroe Street Chicago IL 60603 USA
	IntermediaryBankAndBranch string `dynamic:"true"`
	// Intermediary bank's swift code
	// eg: FDRLINBBIBD
	IntermediaryBankSwiftCode string `dynamic:"true"`
	// Foreign Bank Charges to be borne by Remitter/Full value
	ForeignBankChargesToBeBornBy string `dynamic:"true"`
}

type FiMinutesConfig struct {
	StoryGroupConfig *StoryGroupConfig `dynamic:"true"`
}

type StoryGroupConfig struct {
	Name string `dynamic:"true"`
}

type UpiNumberIntroScreenOptions struct {
	LogoUrl                              string `dynamic:"true"`
	Title                                string `dynamic:"true"`
	DescriptionPrefix                    string `dynamic:"true"`
	DescriptionSuffix                    string `dynamic:"true"`
	ManageUpiNumberIntroTitleColor       string `dynamic:"true"`
	ManageUpiNumberIntroDescriptionColor string `dynamic:"true"`
	PhoneNumberTextColor                 string `dynamic:"true"`
}

// ManageUpiNumberScreenOptions - Screen options for manage upi number screen
type ManageUpiNumberScreenOptions struct {
	CreateCustomUpiNumber          *ManageUpiNumberScreenItem
	PrimaryAccountTagInfo          *ManageUpiNumberScreenItem
	ProcessingInfo                 *ManageUpiNumberScreenItem
	UpiNumberBeingProcessedFaqInfo *FaqInfo
	DefaultFaqInfo                 *FaqInfo
	ActivateNumberAction           *UpiNumberNextAction
	DeactivateNumberAction         *UpiNumberNextAction
	DeleteNumberAction             *UpiNumberNextAction
	DeleteCustomUpiNumberAction    *UpiNumberNextAction
	SetPhoneNumberAsFi             *ManageUpiNumberScreenItem
	AccountDetailInfo              *ManageUpiNumberScreenItem
	ActiveUpiNumberDetail          *ManageUpiNumberScreenItem
	InactiveUpiNumberDetail        *ManageUpiNumberScreenItem
	DeregisteredUpiNumberDetail    *ManageUpiNumberScreenItem
	ProcessingUpiNumberDetail      *ManageUpiNumberScreenItem
	PhoneNumberNotLinkedDetail     *ManageUpiNumberScreenItem
	MaxNumberOfNumericIdsAllowed   int32
}

// FaqInfo to be shown to the user
type FaqInfo struct {
	ImageUrl               string
	ImageHeight            int32
	ImageWidth             int32
	Description            []*TextObject
	LeftImgTxtPadding      int32
	ContainerLeftPadding   int32
	ContainerRightPadding  int32
	ContainerTopPadding    int32
	ContainerBottomPadding int32
	ContainerCornerRadius  int32
	ContainerBorderColour  string
	ContainerBgColour      string
	Deeplink               []*DeeplinkForAction
}

// ManageUpiNumberScreenItem - display information on ManageUpiNumberScreen
type ManageUpiNumberScreenItem struct {
	Title        *TextObject
	Description  []*TextObject
	LogoUrls     []string
	LeftIconUrl  string
	RightIconUrl string
	Ctas         []*CtaForAction
	Deeplink     []*DeeplinkForAction
}

// UpiNumberNextAction - info regarding action on a upi number
type UpiNumberNextAction struct {
	Title              *TextObject
	ActionType         string
	SelectedActionInfo *ManageUpiNumberScreenItem
}

// TextObject - Description for a single string
type TextObject struct {
	DisplayValue string `dynamic:"true"`
	FontColour   string `dynamic:"true"`
	BgColour     string `dynamic:"true"`
	FontStyle    string `dynamic:"true"`
	DisplayType  string `dynamic:"true"`
}

type CtaForAction struct {
	Type         string
	Text         string
	DisplayTheme string
}

type DeeplinkForAction struct {
	Screen string
}

type Dispute struct {
	// if this flag is set to true then we will only populate
	// actor id, txn id, dispute type in GetNextQuestionsForApp rpc call in GetFirstQuestions rpc
	// if this is set to false then we will populate all parameters in GetNextQuestionsForApp rpc request
	// by calling payment client
	IsGetNextQuestionsForAppRequestChangeEnabled bool `dynamic:"true"`
	// Error view config for RaiseDispute RPC call
	RaiseDisputeErrorViewConfig *RaiseDisputeErrorViewConfig `dynamic:"true"`
	// Configuration for complaint summary feature in dispute flow.
	// Contains validation rules and UI text.
	ComplaintSummaryConfig *ComplaintSummaryConfig `dynamic:"true"`
}

type RaiseDisputeErrorViewConfig struct {
	// if this flag is set to true we will return error view bottom sheet
	// to client in RaiseDispute RPC call
	// this flag alone does not control to return error view to client
	// other params like backend RPC call status and response also contributes to the final decision
	IsBottomSheetErrorViewPropagationEnabled bool `dynamic:"true"`
	// bottom sheet error view title
	BottomSheetTitle string `dynamic:"true"`
	// bottom sheet error view subtitle
	BottomSheetSubTitle string `dynamic:"true"`
}

type SavingsBalanceTracker struct {
	DaysOfHistoryToShow int
	WidthOfCalendarView int
}

type WarningMessageForMandateCreation struct {
	Title       string `dynamic:"true"`
	Subtitle    string `dynamic:"true"`
	Description string `dynamic:"true"`
	Ctas        []*CtaForAction
}

type AutoInvestStoryConfig struct {
	StoryId string `dynamic:"true"`
}

type EmailDomainCheck struct {
	EnableDomainCheck        bool `dynamic:"true"`
	InvalidDomains           []string
	InvalidDomainPopupConfig *app.FeatureConfig `dynamic:"true"`
}

type RecentUpcomingWidgetParams struct {
	FilterViewType string `dynamic:"true"`
}

type AskFiHomeSearchBarConfig struct {
	HomeSearchBarWidgetTypeVariant int `dynamic:"true" ,quest:"variable"`
}

// UpiPinSetIntroScreenItem - represents the upi pin set intro screen
type UpiPinSetIntroScreenItem struct {
	Icon     string
	Title    *TextObject
	Subtitle *TextObject
	// map of upi pin set option type to option details map
	UpiPinSetOptionTypeToDetailsMap map[string]*UpiPinSetOptionItem
	Cta                             *CtaForAction
}

// UpiPinSetOptionItem - represents each of the possible option for upi pin set
type UpiPinSetOptionItem struct {
	Icon        string
	Title       *TextObject
	Subtitle    *TextObject
	Description *TextObject
}

// UpiPinSetSuccessScreenInfo - represents the success screen for upi pin set
type UpiPinSetSuccessScreenInfo struct {
	Icon        string
	Title       *TextObject
	Description []*TextObject
	Cta         *CtaForAction
}

type AnalyserExperiment struct {
	Widgets *AnalyserWidgets `dynamic:"true" ,quest:"component"`
}

type AnalyserWidgets struct {
	CreditScoreAnalyser   *AnalyserWidget `dynamic:"true" ,quest:"component"`
	TopCategoriesAnalyser *AnalyserWidget `dynamic:"true" ,quest:"component"`
	TopMerchantsAnalyser  *AnalyserWidget `dynamic:"true" ,quest:"component"`
	TimeSpendAnalyser     *AnalyserWidget `dynamic:"true" ,quest:"component"`
	MutualFundLandingPage *AnalyserWidget `dynamic:"true" ,quest:"component"`
}

type AnalyserWidget struct {
	Header string `dynamic:"true" ,quest:"variable"`
	Footer string `dynamic:"true" ,quest:"variable"`
}

type InternationalPaymentActionParam struct {
	InternationalPaymentActivated      *Text
	InternationalPaymentDeactivated    *Text
	InternationalPaymentActivating     *Text
	InternationalPaymentDeactivating   *Text
	InternationalPaymentActionTitle    *Text
	InternationalPaymentActionSubtitle *Text
}

type ActivateAccountsForInternationalUpiPaymentsInfo struct {
	Icon        string
	Title       *TextObject
	Description *TextObject
	Cta         *CtaForAction
	BankInfo    *TextObject
}

type ForeignCurrencyInfo struct {
	// E.g. $
	Symbol string
	// image of $ (US dollar)
	Image string
	// E.g. In US Dollar(USD), US Dollar is the currency name
	Name string
}

type ActionStatusScreenInfo struct {
	BackgroundImageUrl string
	BackgroundColor    string
	Title              *Text
	Subtitle           *Text
	StatusImageUrl     string
	Cta                *CtaForAction
}

type UpiActionParams struct {
	ForgotUpiPinTitle                     string `dynamic:"true"`
	ChangeUpiPinTitle                     string `dynamic:"true"`
	ManageUpiNumbersTitle                 string `dynamic:"true"`
	CheckCardBalanceTitle                 string `dynamic:"true"`
	ViewBalanceTitle                      string `dynamic:"true"`
	DefaultMerchantPaymentsTitle          string `dynamic:"true"`
	ViewCardTransactionsTitle             string `dynamic:"true"`
	ArrowIcon                             *Image `dynamic:"true"`
	DefaultMerchantActionDisabledToastMsg string `dynamic:"true"`
	RefreshOptionTitle                    string `dynamic:"true"`
}

type EligibleAccountParams struct {
	CCLinkingTitle            *Text  `dynamic:"true"`
	BankLinkingTitle          *Text  `dynamic:"true"`
	EntryPointIcon            *Image `dynamic:"true"`
	EntryPointIconTextPadding int32  `dynamic:"true"`
	Description               *TextObject
	// AccountTypeToIconMap contains icon url for different account types, which we will be showing corresponding to eligible accounts for payment.
	// Example `Rupay` icon for Rupay credit card account type.
	// figma - https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=14226-101873&node-type=frame&t=91Uor3tqiBxtcDgH-0
	AccountTypeToIconMap map[string]*Image
}

type Image struct {
	IconUrl    string `dynamic:"true"`
	IconHeight int32  `dynamic:"true"`
	IconWidth  int32  `dynamic:"true"`
}

type FeedbackEngineConfig struct {
	// ResponseHeaderPopulationConfig contains boolean variables which
	// control whether feedback engine related info is being populated in response header
	// of the frontend server RPC
	ResponseHeaderPopulationConfig *ResponseHeaderPopulationConfig `dynamic:"true"`
	FlowIdSeparator                string                          `dynamic:"true"`
}

type ResponseHeaderPopulationConfig struct {
	IsPopulationInGetAnalyserEnabled bool `dynamic:"true"`
	// IsPopulationInGetP2POrderStatusEnabled is used for starting an app flow survey
	// when user withdraws money from jump
	IsPopulationInGetP2POrderStatusEnabled bool `dynamic:"true"`
	// IsPopulationInCollectDataFromCustomerEnabled is used for US stocks onboarding flow
	IsPopulationInCollectDataFromCustomerEnabled bool `dynamic:"true"`
	// IsPopulationInGetNextOnboardingStepEnabled is used for US stocks onboarding flow
	IsPopulationInGetNextOnboardingStepEnabled bool `dynamic:"true"`
	// IsPopulationInGetSupportTicketsForAppEnabled is used for sample flow testing in GetSupportTicketByIdForApp
	IsPopulationInGetSupportTicketsForAppEnabled bool `dynamic:"true"`
	// IsPopulationInGetSupportTicketByIdForAppEnabled is used for CTA based in app CSAT survey
	IsPopulationInGetSupportTicketByIdForAppEnabled bool `dynamic:"true"`
	// IsPopulationInGetChatInitInformationForActor is used for drop off while chat exit
	IsPopulationInGetChatInitInformationForActorEnabled bool `dynamic:"true"`
	// IsPopulationIGetProfileSettingPageSection is used for language preference survey when the user enters the screen
	IsPopulationInGetProfileSettingPageSectionEnabled bool `dynamic:"true"`
	// IsPopulationInUSSGetLandingPageForNewUserEnabled is used for starting an drop off survey
	// when user enter back from us-stocks landingPage and user is new user
	IsPopulationInUSSGetLandingPageForNewUserEnabled bool `dynamic:"true"`
	// IsPopulationInUSSGetLandingPageForNewWalletUserEnabled is used for starting an drop off survey
	// when user enter back from us-stocks landingPage and user has never add funds
	IsPopulationInUSSGetLandingPageForNewWalletUserEnabled bool `dynamic:"true"`
	// IsPopulationInUSSGetLandingPageForExistingWalletUserEnabled is used for starting an drop off survey
	// when user enter back from us-stocks landingPage and user has added funds
	IsPopulationInUSSGetLandingPageForExistingWalletUserEnabled bool `dynamic:"true"`
	// IsPopulationInGetWalletAddFundsDetailsForExistingWalletUserEnabled is used for starting an drop off survey
	// if user has added fund atleast one time
	// when user enter back from us-stocks walletPage
	IsPopulationInGetWalletAddFundsDetailsForExistingWalletUserEnabled bool `dynamic:"true"`
	// IsPopulationInGetWalletAddFundsDetailsForNewWalletUserEnabled is used for starting an drop off survey
	// if user never added fund one time
	// when user enter back from us-stocks walletPage
	IsPopulationInGetWalletAddFundsDetailsForNewWalletUserEnabled bool `dynamic:"true"`
	// IsPopulationInGetWalletAddFundsDetailsForNewWalletUserEnabled is used for starting an drop off survey
	// for us stocks withdraw screen
	IsPopulationInGetWalletWithdrawFundsDetailsForUserEnabled bool `dynamic:"true"`
	// IsPopulationInCreateWalletWithdrawFundsOrderForUserEnabled for app flow during withdraw funds in usstocks
	IsPopulationInCreateWalletWithdrawFundsOrderForUserEnabled bool `dynamic:"true"`
	IsPopulationInEKYCForOnboardingForUserEnabled              bool `dynamic:"true"`
}

type HomeLayoutSectionSlots struct {
	Section string
	Slots   []string
}

type TopNavBarSectionSlotsToScreenElementMap struct {
	leftSlots  map[string]ScreenElement
	rightSlots map[string]ScreenElement
}

type ScreenElement struct {
	Id string
}

type WidgetScreenElementsConfig struct {
	Section string
}

type ScreenElementConfig struct {
	Id string
}

type ScreenElementProperties struct {
	Id                         string
	WidgetType                 string
	Area                       string
	Title                      string
	HomeWidgetDashboardParams  HomeWidgetDashboardParamsV2
	WidgetBg                   BgConfig
	IconWithVersionConstraints IconWithVersionConstraints
	IconActionInfoProperties   IconActionInfoProperties
	WalkthroughInfo            *WalkthroughInfo
	IconMappingKey             string
}

type HomeWidgetDashboardParamsV2 struct {
	HomeDashboardView HomeDashboardView
}

type HomeDashboardView struct {
	Name         string
	Priority     string
	WaterMarkUrl string
}

type ScreenElementIdToPropertiesMap struct {
	ScreenElementProperties map[string]*ScreenElementProperties
}

type BgConfig struct {
	BackgroundColour BackgroundColourProperties
}

type BackgroundColourProperties struct {
	BlockColour    BlockColourProperties
	RadialGradient RadialGradientProperties
	LinearGradient LinearGradientProperties
}

type BlockColourProperties struct {
	BlockColour string
}

type RadialGradientProperties struct {
	Colours []string
}

type LinearGradientProperties struct {
	LinearColorStops []ColorStops
}

type ColorStops struct {
	Color          string
	StopPercentage int32
}

type SpacerProperties struct {
	SpacerValue string
	BgColour    BgConfig
}

type IconActionInfoProperties struct {
	ActionType string
	Action     ActionProperties
}

type ActionProperties struct {
	Deeplink DeeplinkProperties
}

type DeeplinkProperties struct {
	Screen string
}

type WalkthroughInfo struct {
	ElementId string
	// will be used in case layout is changed
	LayoutChangeDescription string
	// will be used in case new user onboards
	NewUserDescription string
	// will be used in case user comes back after long
	RefresherDescription string
	// will be used in case fi lite layout is changed
	FiLiteLayoutChangeDescription string
}

type CommonCTA struct {
	Text struct {
		Text             string
		FontColor        string
		BgColor          string
		DisplayValue     string
		FontStyle        string
		FontColorOpacity int32
	}
	Image struct {
		ImageType       string
		ImageDataBase64 string
		ImageUrl        string
		Width           int32
		Height          int32
		Padding         struct {
			Left   int32
			Right  int32
			Bottom int32
			Top    int32
		}
		Margin struct {
			Left   int32
			Right  int32
			Bottom int32
			Top    int32
		}
	}
	IsVisible bool
	Action    struct {
		CustomAction struct {
			Action string
		}
		DeeplinkAction struct {
			Screen string
		}
	}
	BgColor struct {
		Colour BackgroundColourProperties
	}
	Shadow struct {
		Height  int32
		Blur    int32
		Opacity int32
		Colour  struct {
			Colour BackgroundColourProperties
		}
	}
}

type DashboardIntroCardDetails struct {
	StartIcon struct {
		Title struct {
			DisplayValue string
			FontColor    string
			FontStyle    string
		}
		BgColor struct {
			Color BackgroundColourProperties
		}
		IconImage struct {
			ImageUrl string
		}
	}
	ShowCardMaxImpressions int32
	Title                  struct {
		DisplayValue struct {
			PlainString string
		}
		FontStyle struct {
			StandardFontStyle int32
		}
	}
	Image struct {
		ImageUrl string
	}
	Shadow struct {
		Height  int32
		Blur    int32
		Opacity int32
		Colour  struct {
			Colour BackgroundColourProperties
		}
	}
}

type StartIcon struct {
	Title   StartIconTitle
	BgColor BackgroundColourProperties
}

type StartIconTitle struct {
	DisplayValue string
	FontColor    string
	FontStyle    string
}

type NetworthConfig struct {
	ConfigPath                         string
	IsNetworthDashboardFeedbackEnabled bool                  `dynamic:"true"`
	EpfPassbookParams                  *EpfPassbookParams    `dynamic:"true"`
	NetworthD2HDashboardFeatureConfig  *app.FeatureConfig    `dynamic:"true"`
	FeatureFlags                       *NetWorthFeatureFlags `dynamic:"true"`
	// TODO(sainath): Remove this once its stable in prod
	// Keep this in sync with DebugActorIdsForDailyReport in insights/config
	DebugActorIdsForDailyReport             map[string]bool        `dynamic:"true"`
	NetworthMcp                             *NetworthMcp           `dynamic:"true"`
	AppVersionsForWBDashboardV2             *MinVersionConstraints `dynamic:"true"`
	AppVersionsForConnectMoreAssetsScreenV2 *MinVersionConstraints `dynamic:"true"`
}

type NetworthMcp struct {
	AllowedMCPAndroidApps                []string               `dynamic:"true"`
	MCPHowItWorksURL                     string                 `dynamic:"true"`
	MCPTermsConditions                   string                 `dynamic:"true"`
	AppVersionsForNetworthDataReqPayload *MinVersionConstraints `dynamic:"true"`
}

type NetWorthFeatureFlags struct {
	AIFSearch *FeatureSupportConstraints `dynamic:"true"`

	// Flag to disable the MF Story in the Wealth Builder onboarding flow.
	DisableWBOnbMFStory bool `dynamic:"true"`
}

type FeatureSupportConstraints struct {
	// Minimum Android app version which supports a feature. To disable, use a very high app version.
	MinAndroidAppVersion int `dynamic:"true"`

	// Minimum iOS app version which supports a feature. To disable, use a very high app version.
	MinIOSAppVersion int `dynamic:"true"`
}

type EpfPassbookParams struct {
	IsEpfRedirectionEnabled bool `dynamic:"true"`
}

type BiometricKYCConfig struct {
	QRConfig                 *cfg.QRCode        `dynamic:"true"`
	AllowedAgentEmailDomains []string           `dynamic:"true"`
	EnableEPANForBKYC        *app.FeatureConfig `dynamic:"true"`
}

type TargetGroupConfig struct {
	Age          int                `dynamic:"true"`
	IsEnable     bool               `dynamic:"true"`
	VersionCheck *app.FeatureConfig `dynamic:"true"`
}

type CCIntroScreenV2Config struct {
	CardProgramTypeToScreenConfigMap map[string]*CCIntroScreenV2Options `dynamic:"true"`
}

type CCIntroScreenV2Options struct {
	CardRejectedErrorOptions        *CCIntroScreenErrorOptions `dynamic:"true"`
	CardClosedErrorOptions          *CCIntroScreenErrorOptions `dynamic:"true"`
	IneligibilityScreenErrorOptions *CCIntroScreenErrorOptions `dynamic:"true"`
	IntroScreenContent              *CCIntroScreenV2Content    `dynamic:"true"`
}

type CCIntroScreenErrorOptions struct {
	IconUrl        string                          `dynamic:"true"`
	Title          string                          `dynamic:"true"`
	TitleColor     string                          `dynamic:"true"`
	Body           string                          `dynamic:"true"`
	BodyColor      string                          `dynamic:"true"`
	BodyBackground *LinearGradient                 `dynamic:"true"`
	BodyBorder     *LinearGradient                 `dynamic:"true"`
	Cta            *CCIntroScreenV2CtaWithInfoText `dynamic:"true"`
}

// https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2453-631&t=kZwHAZuJe3kDCXrU-4
type CCIntroScreenV2Content struct {
	CardProgramTitleSubtitleImageUrl   string                                `dynamic:"true"`
	CardImageUrl                       string                                `dynamic:"true"`
	CardLightRays                      string                                `dynamic:"true"`
	SubTextHeader                      string                                `dynamic:"true"` // eg. Congratulations! Pre-approved offer of
	SubTextHeaderColor                 string                                `dynamic:"true"` // eg. #FFFFFF
	SubTextContent                     string                                `dynamic:"true"` // ₹4,00,000
	DualBenefitsImageUrl               string                                `dynamic:"true"`
	JoiningFeeImageUrl                 string                                `dynamic:"true"`
	ChevronUp                          string                                `dynamic:"true"`
	ChevronDown                        string                                `dynamic:"true"`
	ExpandableComponents               []*CCIntroScreenV2ExpandableComponent `dynamic:"true,readonlylist"`
	HorizontalScrollableComponentTitle string                                `dynamic:"true"`
	HorizontalScrollableComponents     []*HorizontalScrollableComponent      `dynamic:"true,readonlylist"`
	BottomCtaText                      string                                `dynamic:"true"` // eg. Learn more
	FooterImageUrl                     string                                `dynamic:"true"`

	InitiateOnboardingCta *CCIntroScreenV2CtaWithTnc `dynamic:"true"`
}

type CCIntroScreenV2CtaWithTnc struct {
	CtaTncText       string          `dynamic:"true"`
	CtaText          string          `dynamic:"true"`
	CtaBorder        *LinearGradient `dynamic:"true"`
	CtaColor         *LinearGradient `dynamic:"true"`
	ContainerBgColor *LinearGradient `dynamic:"true"`
}

// https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2484-262&t=fcrHqtuHtWJ2rFWA-4
type CCIntroScreenV2CtaWithBanner struct {
	BannerImageUrl     string          `dynamic:"true"`
	BannerTopText      string          `dynamic:"true"`
	BannerTopTextColor string          `dynamic:"true"`
	PitchText          string          `dynamic:"true"` // eg. Check approval limit!
	PitchTextColor     string          `dynamic:"true"`
	CtaText            string          `dynamic:"true"`
	CtaColor           *LinearGradient `dynamic:"true"`
}

// https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2484-724&t=fcrHqtuHtWJ2rFWA-4
type CCIntroScreenV2CtaWithInfoText struct {
	InfoText      string          `dynamic:"true"`
	InfoTextColor string          `dynamic:"true"`
	CtaText       string          `dynamic:"true"`
	CtaColor      string          `dynamic:"true"`
	CtaBorder     *LinearGradient `dynamic:"true"`
	CtaBackground *LinearGradient `dynamic:"true"`
}

type CCIntroScreenV2ExpandableComponent struct {
	IsExpanded           bool            `dynamic:"true"`
	ContainerBorderColor *LinearGradient `dynamic:"true"`
	HeadingText          string          `dynamic:"true"`
	HeadingTextColor     string          `dynamic:"true"`
	ContentImageUrl      string          `dynamic:"true"`
}

type HorizontalScrollableComponent struct {
	ContainerBorderColor string `dynamic:"true"`
	HeadingText          string `dynamic:"true"`
	HeadingColor         string `dynamic:"true"`
	BodyText             string `dynamic:"true"`
	BodyTextColor        string `dynamic:"true"`
}

type CCIntroScreenOptions struct {
	TemplatesMap                 map[string]*IntroScreenTemplate `dynamic:"true,readonlymap"`
	PageIndicatorWidget          *PageIndicatorWidget            `dynamic:"true"`
	ConsentText                  *WrappedHyperLinksWidget        `dynamic:"true"`
	WrappedBtnInfo               *WrappedBtnInfo                 `dynamic:"true"`
	BgColor                      string                          `dynamic:"true"`
	PartnerIcon                  *WrappedVisualElement           `dynamic:"true"`
	LimitAmountString            string                          `dynamic:"true"`
	PagerAutoScrollProperties    *PagerAutoScrollProperties      `dynamic:"true"`
	WrappedIconTextToolBar       *WrappedIconTextToolBar         `dynamic:"true"`
	WrappedIconTextToolBarFiLite *WrappedIconTextToolBar         `dynamic:"true"`
	StickyBottomBackgroundColour *LinearGradient                 `dynamic:"true"`
}

type IntroScreenTemplate struct {
	CardIntroWidgetsMap map[string]*CardIntroWidget `dynamic:"true,readonlymap"`
	LayoutProperties    *LayoutProperties           `dynamic:"true"`
	DrawableProperties  *DrawableProperties         `dynamic:"true"`
	TemplateType        string
}

type CardIntroWidget struct {
	WrappedVisualElement    *WrappedVisualElement    `dynamic:"true"`
	WrappedBtnInfo          *WrappedBtnInfo          `dynamic:"true"`
	WrappedTextInfo         *WrappedTextInfo         `dynamic:"true"`
	MultiBenefitCard        *MultiBenefitCard        `dynamic:"true"`
	MultiFeeCardInfo        *MultiFeeCardInfo        `dynamic:"true"`
	WrappedHyperLinksWidget *WrappedHyperLinksWidget `dynamic:"true"`
	// This is a base64 string which needs to be generated for the section
	// helper for creating SDUISection : frontend/firefly/helper/sdui_section_helper.go
	SdUiSection string `dynamic:"true"`
}

type WrappedVisualElement struct {
	ImgUrl             string              `dynamic:"true"`
	LottieUrl          string              `dynamic:"true"`
	Height             int32               `dynamic:"true"`
	Width              int32               `dynamic:"true"`
	LayoutProperties   *LayoutProperties   `dynamic:"true"`
	DrawableProperties *DrawableProperties `dynamic:"true"`
}

type WrappedTextInfo struct {
	Text                       string                          `dynamic:"true"`
	FontColor                  string                          `dynamic:"true"`
	FontStyle                  int32                           `dynamic:"true"`
	LayoutProperties           *LayoutProperties               `dynamic:"true"`
	DrawableProperties         *DrawableProperties             `dynamic:"true"`
	TextSpannablePropertiesMap map[string]*SpannableProperties `dynamic:"true,readonlymap"`
}

type WrappedBtnInfo struct {
	BtnText            string                        `dynamic:"true"`
	FontColor          string                        `dynamic:"true"`
	FontStyle          int32                         `dynamic:"true"`
	LayoutProperties   *LayoutProperties             `dynamic:"true"`
	DrawableProperties *DrawableProperties           `dynamic:"true"`
	DeeplinkData       *CCIntroScreenDeeplinkOptions `dynamic:"true"`
	EventParameter     string                        `dynamic:"true"`
}

type WrappedChevron struct {
	WrappedVisualElement *WrappedVisualElement `dynamic:"true"`
	LayoutProperties     *LayoutProperties     `dynamic:"true"`
	DrawableProperties   *DrawableProperties   `dynamic:"true"`
}

type MultiBenefitCard struct {
	BenefitCardInfoMap map[string]*BenefitCardInfo `dynamic:"true,readonlymap"`
	LayoutProperties   *LayoutProperties           `dynamic:"true"`
	DrawableProperties *DrawableProperties         `dynamic:"true"`
}
type BenefitCardInfo struct {
	Title                 string                        `dynamic:"true"`
	TitleFontColor        string                        `dynamic:"true"`
	TitleFontStyle        int32                         `dynamic:"true"`
	Desc                  string                        `dynamic:"true"`
	DescFontColor         string                        `dynamic:"true"`
	DescFontStyle         int32                         `dynamic:"true"`
	VisualElement         *WrappedVisualElement         `dynamic:"true"`
	Chevron               *WrappedChevron               `dynamic:"true"`
	CCIntroScreenDeeplink *CCIntroScreenDeeplinkOptions `dynamic:"true"`
	LayoutProperties      *LayoutProperties             `dynamic:"true"`
	DrawableProperties    *DrawableProperties           `dynamic:"true"`
	EventParameter        string                        `dynamic:"true"`
}
type MultiFeeCardInfo struct {
	FeeCardInfoMap     map[string]*FeeCardInfo `dynamic:"true,readonlymap"`
	LayoutProperties   *LayoutProperties       `dynamic:"true"`
	DrawableProperties *DrawableProperties     `dynamic:"true"`
}
type FeeCardInfo struct {
	Title              string              `dynamic:"true"`
	TitleFontColor     string              `dynamic:"true"`
	TitleFontStyle     int32               `dynamic:"true"`
	Subtitle           *WrappedTextInfo    `dynamic:"true"`
	Desc               string              `dynamic:"true"`
	DescFontColor      string              `dynamic:"true"`
	DescFontStyle      int32               `dynamic:"true"`
	LayoutProperties   *LayoutProperties   `dynamic:"true"`
	DrawableProperties *DrawableProperties `dynamic:"true"`
}

type LayoutProperties struct {
	Padding *Padding `dynamic:"true"`
	Margin  *Margin  `dynamic:"true"`
	Size    *Size    `dynamic:"true"`
}

type DrawableProperties struct {
	BgColor               string          `dynamic:"true"`
	BgColorLinearGradient *LinearGradient `dynamic:"true"`
	Corners               *Corner         `dynamic:"true"`
	Border                *Border         `dynamic:"true"`
	Shadow                *Shadow         `dynamic:"true"`
}

type Padding struct {
	Start  int32 `dynamic:"true"`
	Top    int32 `dynamic:"true"`
	End    int32 `dynamic:"true"`
	Bottom int32 `dynamic:"true"`
}

type Margin struct {
	Start  int32 `dynamic:"true"`
	Top    int32 `dynamic:"true"`
	End    int32 `dynamic:"true"`
	Bottom int32 `dynamic:"true"`
}

type Size struct {
	Width  int32 `dynamic:"true"`
	Height int32 `dynamic:"true"`
}

type Corner struct {
	TopStartCornerRadius int32 `dynamic:"true"`
	TopEndCornerRadius   int32 `dynamic:"true"`
	BottomStartCorner    int32 `dynamic:"true"`
	BottomEndCorner      int32 `dynamic:"true"`
}

type Border struct {
	BorderThickness int32  `dynamic:"true"`
	BorderColor     string `dynamic:"true"`
}

type Shadow struct {
	Height  int32  `dynamic:"true"`
	Blur    int32  `dynamic:"true"`
	Opacity int32  `dynamic:"true"`
	BgColor string `dynamic:"true"`
}

type CCIntroScreenDeeplinkOptions struct {
	Title             string `dynamic:"true"`
	TitleFontColor    string `dynamic:"true"`
	TitleFontStyle    int32  `dynamic:"true"`
	Subtitle          string `dynamic:"true"`
	SubtitleFontColor string `dynamic:"true"`
	SubtitleFontStyle int32  `dynamic:"true"`
	BgColor           string `dynamic:"true"`
	// this filed we will use for cc intro bottom sheet
	Promotion      *PromotionInfo          `dynamic:"true"`
	WrappedBtnInfo *WrappedDeeplinkBtnInfo `dynamic:"true"`
	ScreenName     string                  `dynamic:"true"`
	// this filed we will use for cc consent bottom sheet
	Consent *ConsentInfo `dynamic:"true"`
}

type ConsentInfo struct {
	ProgramVisualElement               *WrappedVisualElement `dynamic:"true"`
	ConsentContainerDrawableProperties *DrawableProperties   `dynamic:"true"`
	CommonCheckBoxItem                 *CheckBoxItem
	CreditCardOnboardingConsent        string `dynamic:"true"`
	ConsentItems                       []*ConsentItem
	ConsentTextWithHyperLink           *HyperLinksWidget
	ConsentCheckBox                    *ConsentCheckBox `dynamic:"true"`
}

type ConsentCheckBox struct {
	IsChecked                   bool   `dynamic:"true"`
	CreditCardOnboardingConsent string `dynamic:"true"`
}

type CheckBoxItem struct {
	Id           string
	DisplayText  *typesPkg.Text
	SubTitleText *typesPkg.Text
}

type ConsentItem struct {
	Title               *typesPkg.Text
	SubTitle            *HyperLinksWidget
	LeftVisualElement   *WrappedVisualElement
	BottomVisualElement *WrappedVisualElement
}

type PromotionInfo struct {
	VisualElement      *WrappedVisualElement `dynamic:"true"`
	LayoutProperties   *LayoutProperties     `dynamic:"true"`
	DrawableProperties *DrawableProperties   `dynamic:"true"`
}

type PageIndicatorWidget struct {
	SelectedColor      string              `dynamic:"true"`
	UnselectedColor    string              `dynamic:"true"`
	LayoutProperties   *LayoutProperties   `dynamic:"true"`
	DrawableProperties *DrawableProperties `dynamic:"true"`
}

type WrappedDeeplinkBtnInfo struct {
	BtnText            string              `dynamic:"true"`
	FontColor          string              `dynamic:"true"`
	FontStyle          int32               `dynamic:"true"`
	LayoutProperties   *LayoutProperties   `dynamic:"true"`
	DrawableProperties *DrawableProperties `dynamic:"true"`
}

type WrappedHyperLinksWidget struct {
	Text               string                `dynamic:"true"`
	TextColor          string                `dynamic:"true"`
	TextFrontStyle     int32                 `dynamic:"true"`
	HyperLinkMap       map[string]*Hyperlink `dynamic:"true,readonlymap"`
	LayoutProperties   *LayoutProperties     `dynamic:"true"`
	DrawableProperties *DrawableProperties   `dynamic:"true"`
	EventParameter     string                `dynamic:"true"`
}

type HyperLinksWidget struct {
	Text           string
	TextColor      string
	TextFrontStyle int32
	HyperLinks     []*Hyperlink
}

type Hyperlink struct {
	Key            string `dynamic:"true"`
	Link           string `dynamic:"true"`
	EventParameter string `dynamic:"true"`
}

type PagerAutoScrollProperties struct {
	AutoScrollDelay      int64 `dynamic:"true"`
	UserInteractionDelay int64 `dynamic:"true"`
}

type WrappedIconTextToolBar struct {
	BackArrowColor       string                `dynamic:"true"`
	WrappedVisualElement *WrappedVisualElement `dynamic:"true"`
	Title                *WrappedTextInfo      `dynamic:"true"`
	LayoutProperties     *LayoutProperties     `dynamic:"true"`
	DrawableProperties   *DrawableProperties   `dynamic:"true"`
}

type SpannableProperties struct {
	HasStrikeThrough bool   `dynamic:"true"`
	TextColor        string `dynamic:"true"`
}

type LoanNonEligibleScreenV2 struct {
	AppVersionConstraintConfig *releaseConfig.AppVersionConstraintConfig `dynamic:"true"`
}

type OrderReceipt struct {
	OrderTagToChargeTxnDescriptionMap map[string]*ChargeTxnDescription
	IsHtmlRenderingEnabledIOnPlatform *cfg.PlatformVersionCheck `dynamic:"true"`
	// The time duration after which we assume the cheque transaction to have reached a terminal state. This is because
	// for cheque amount credit we initially receive a shadow credit notification, but money hasn't moved into the
	// account yet, and we don't receive any notification when the money is actually moved into the account.
	ChequeTransactionSuccessTimeDuration time.Duration `dynamic:"true"`
	// ChequeReceiptTileTxnDescription has config related to description tile based on order tag
	// Figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=30150-8387&t=r4C0xPplCHvBT1Mx-0
	ChequeReceiptTileTxnDescription map[string]*TileTxnDescription `dynamic:"true"`
	// ChequeBounceReasonToFailureTileMap stores a mapping of a unique substring in the failed cheque transaction
	// particulars and the corresponding error message that needs to be displayed. If none of the passed error reasons
	// match the fetched particular then a generic error view is returned.
	// Example particulars: https://docs.google.com/spreadsheets/d/1hzZKWmkZWNy8vXLuDJ2riJHxaOmNaWR9rtBZfKR5k4w/edit?gid=0#gid=0
	ChequeBounceReasonToFailureTileMap map[string]*TileTxnDescription `dynamic:"true"`
	// EnachFailedTxnStatusCodeToFailureTileMap stores a mapping of the Fi Status Code of ENACH failed transaction
	// status code and the corresponding error message that needs to be displayed. If none of the passed error reasons
	// match the fetched status code, then a generic error view is returned.
	EnachFailedTxnStatusCodeToFailureTileMap map[string]*TileTxnDescription `dynamic:"true"`
}

type ChargeTxnDescription struct {
	TitleText           string
	DescriptionText     string
	DescriptionHtmlText string
	// The fallback texts must be free of any formatting parameters/template variables, and need to be used when
	// the variables cannot be computed for generating the description text.
	DescriptionFallbackText     string
	DescriptionHtmlFallbackText string
	DeepLinks                   []*DeepLink
}

type DeepLink struct {
	Screen     string
	IconUrl    string
	Title      string
	StoryTitle string
	StoryUrl   string
	StoryId    string
}

type TileTxnDescription struct {
	IconUrl         string                                `dynamic:"true"`
	TitleText       string                                `dynamic:"true"`
	DescriptionText string                                `dynamic:"true"`
	CtaParamsList   map[string]*OrderReceiptTileCtaParams `dynamic:"true"`
}

type OrderReceiptTileCtaParams struct {
	// needed for supporting arrays in dynamic config.
	ArrayElement *cfg.DynamicArrayElement `dynamic:"true"`
	IconUrl      string                   `dynamic:"true"`
	Title        string                   `dynamic:"true"`
	Screen       string                   `dynamic:"true"`
	StoryTitle   string                   `dynamic:"true"`
	StoryUrl     string                   `dynamic:"true"`
	StoryId      string                   `dynamic:"true"`
}

type VpaMigrationSuccessScreenParams struct {
	Icon         *ImageProperties
	Title        *TextObject
	NewVpaParams *IconTextParams
	Description  *TextObject
	Ctas         []*CtaForAction
}

type VpaMigrationIntroScreenParams struct {
	Icon        *ImageProperties
	Title       *TextObject
	Description *TextObject
	Ctas        []*CtaForAction
}

type ListAccountScreenParams struct {
	Tnc                   string
	TncUrl                string
	PrivacyNoticeUrl      string
	TncContentFontColor   string
	TncHyperlinkFontColor string
}

type LinearGradient struct {
	Degree           int32 `dynamic:"true"`
	LinearColorStops []*LinearColorStops
}

type LinearColorStops struct {
	Color          string
	StopPercentage int32
}

type PayLandingScreenParams struct {
	TpapEntryPointBanner            *Banner
	TpapEntryPointBannerV2          *Banner
	UpiMapperQuickLinkBanner        *Banner
	UpiMapperQuickLinkBannerV2      *Banner
	RuPayCCLinkingBanner            *Banner
	RuPayCCLinkingBannerV2          *Banner
	QuickActions                    *QuickActions `dynamic:"true"`
	QuickActionsV2                  *QuickActions `dynamic:"true"`
	PayLandingScreenTitle           *TextObject
	IOSMinVersionForDesignFixit     uint32 `dynamic:"true"`
	AndroidMinVersionForDesignFixit uint32 `dynamic:"true"`
}

type QuickActions struct {
	FontColor    string       `dynamic:"true"`
	ScanQrAction *QuickAction `dynamic:"true"`
	PayUpiId     *QuickAction `dynamic:"true"`
	BankTransfer *QuickAction `dynamic:"true"`
	PayNumber    *QuickAction `dynamic:"true"`
}

type QuickAction struct {
	IconUrl   string `dynamic:"true"`
	CtaText   string `dynamic:"true"`
	LottieUrl string `dynamic:"true"`
}

type Banner struct {
	Title *TextObject
	Cta   *IconTextParams
	Icon  *ImageProperties
}

type AMBAccountSummaryEntryPointConfig struct {
	DefaultAMBBannerParams                *AMBEntrypointBannerParams `dynamic:"true"`
	RegularInsufficientAMBBannerParams    *AMBEntrypointBannerParams `dynamic:"true"`
	NonRegularInsufficientAMBBannerParams *AMBEntrypointBannerParams `dynamic:"true"`
}

type AMBEntrypointBannerParams struct {
	Title                     *TextObject      `dynamic:"true"`
	Cta                       *IconTextParams  `dynamic:"true"`
	Icon                      *ImageProperties `dynamic:"true"`
	BgColour                  string           `dynamic:"true"`
	BorderColour              string           `dynamic:"true"`
	IsQuestCheckEnabledForAMB bool             `dynamic:"true"`
	IsAMBEnabledByQuest       bool             `dynamic:"true" ,quest:"variable"`
}

type TpapUnifiedFlowReleaseVersions struct {
	MinAndroidVersion int `dynamic:"true"`
	MinIOSVersion     int `dynamic:"true"`
}

type TpapAccountsCoolOffEnum int

const (
	TPAP_ACCOUNTS_COOL_OFF_NOT_APPLICABLE  TpapAccountsCoolOffEnum = 0
	TPAP_ACCOUNTS_COOL_OFF_IN_COOL_OFF     TpapAccountsCoolOffEnum = 1
	TPAP_ACCOUNTS_COOL_OFF_NOT_IN_COOL_OFF TpapAccountsCoolOffEnum = 2
)

// GetOptionsDisplay gets the options display for a given entrypoint, amount and cool off state
// If config for given entrypoint not present, returns default config
// Error, if config doesn't cover all possible ranges
func (o *PaymentOptionsConfig) GetOptionsDisplay(entrypoint timelinePb.TransactionUIEntryPoint, amount int64,
	tpapAccountsCoolOffEnum TpapAccountsCoolOffEnum) (*OptionsDisplay, error) {
	bucketRules, isEntrypointConfigPresent := o.EntryPointToBucketRulesMap[entrypoint.String()]
	if !isEntrypointConfigPresent {
		defaultBucketRules, isDefaultConfigPresent := o.EntryPointToBucketRulesMap["DEFAULT"]
		if !isDefaultConfigPresent {
			return nil, fmt.Errorf("default entrypoint not present in EntryPointToBucketRulesMap")
		}
		bucketRules = defaultBucketRules
	}
	// Check if the config has coverage for all ranges
	if !isCoveringAllRanges(bucketRules) {
		return nil, fmt.Errorf("bucketRules not covering all the ranges")
	}
	var tpapAccountsCoolOff *TpapAccountsCoolOff
	for _, bucketRule := range bucketRules {
		lowerBound := bucketRule.AmountBucket.LowerBound
		upperBound := bucketRule.AmountBucket.UpperBound
		if upperBound == 0 {
			upperBound = math.MaxInt64
		}
		// Note: If any amount value got covered in two ranges we consider the first range it got covered
		if amount >= lowerBound && amount <= upperBound {
			tpapAccountsCoolOff = bucketRule.TpapAccountsCoolOff
			break
		}
	}

	var optionsDisplay *OptionsDisplay
	switch tpapAccountsCoolOffEnum {
	case TPAP_ACCOUNTS_COOL_OFF_NOT_APPLICABLE:
		optionsDisplay = tpapAccountsCoolOff.CoolOffNotApplicable
	case TPAP_ACCOUNTS_COOL_OFF_IN_COOL_OFF:
		optionsDisplay = tpapAccountsCoolOff.InCoolOff
	case TPAP_ACCOUNTS_COOL_OFF_NOT_IN_COOL_OFF:
		optionsDisplay = tpapAccountsCoolOff.NotInCoolOff
	default:
		return nil, fmt.Errorf("invalid value for enum tpapAccountsCoolOffEnum: %d", tpapAccountsCoolOffEnum)
	}
	return optionsDisplay, nil
}

// isCoveringAllRanges checks if the config has coverage for all ranges
func isCoveringAllRanges(bucketRules []*BucketRule) bool {
	if len(bucketRules) < 1 {
		return false
	}
	// sort bucket rules base
	sort.Slice(bucketRules, func(i, j int) bool {
		return bucketRules[i].AmountBucket.LowerBound < bucketRules[j].AmountBucket.LowerBound
	})
	lastUpperBound := bucketRules[0].AmountBucket.UpperBound
	for _, bucketRule := range bucketRules[1:] {
		if bucketRule.AmountBucket.LowerBound <= lastUpperBound+1 {
			lastUpperBound = bucketRule.AmountBucket.UpperBound
		} else {
			// There is a gap between ranges, not covering all
			return false
		}
	}
	return true
}

type FiStoreConfig struct {
	SegmentIds []string `dynamic:"true"`
	// Flag to enable web page screen with card details for fi store
	IsWebPageWithCardDetailsScreenEnabled bool `dynamic:"true"`
	// android app >= below app version supports fi store with card details web page
	MinAndroidVersionForWebPageWithCardDetailsScreen uint32 `dynamic:"true"`
	// IOS app >= below app version supports fi store with card details web page
	MinIosVersionForWebPageWithCardDetailsScreen uint32 `dynamic:"true"`
}

type MediaConfig struct {
	VKYCPanCaptureDocumentDetectionThreshold int32 `dynamic:"true"`
}

type DefaultShortcutsValueForAreas struct {
	LoanShortcutValue       int32
	CCShortcutValue         int32
	USStocksShortcutValue   int32
	PrePayLoanShortcutValue int32
}

// HighReturnStock represents high return stock
type HighReturnStock struct {
	// Ticker for stock
	Ticker string
	// percentage returns
	Return float64
	// tag to be shown on top right of stock card
	Tag string
}

type LendingApplicationStatusPollRetryConfig struct {
	RetryConfig map[string]*cfgv2.RetryParams
}

// MsClarityConfig Instructs client apps about how to initialise and configure MS Clarity Sdk
type MsClarityConfig struct {
	IsEnabled            bool     `dynamic:"true"`
	AllowedScreenNames   []string `dynamic:"true"`
	AllowedActivityNames []string `dynamic:"true"`
}

type FiStoreCollectedOffersConfig struct {
	IsFiStoreCollectedOffersEnabled            bool                              `dynamic:"true"`
	MinAndroidVersionForFiStoreCollectedOffers int                               `dynamic:"true"`
	MinIosVersionForFiStoreCollectedOffers     int                               `dynamic:"true"`
	EComCategoryCard                           *FiStoreCollectedOffersCardConfig `dynamic:"true"`
	EComDiscountsCard                          *FiStoreCollectedOffersCardConfig `dynamic:"true"`
	GiftCardsCard                              *FiStoreCollectedOffersCardConfig `dynamic:"true"`
	FlightsCard                                *FiStoreCollectedOffersCardConfig `dynamic:"true"`
	HotelsCard                                 *FiStoreCollectedOffersCardConfig `dynamic:"true"`
	MilesExchangeCard                          *FiStoreCollectedOffersCardConfig `dynamic:"true"`
	FiStoreIconUrl                             string                            `dynamic:"true"`
	FiCoinIconUrl                              string                            `dynamic:"true"`
	FiPointIconUrl                             string                            `dynamic:"true"`
	CancelIconUrl string `dynamic:"true"`
}

type FiStoreCollectedOffersCardConfig struct {
	BackgroundImageUrl string `dynamic:"true"`
	BackgroundColor    string `dynamic:"true"`
	LogoUrl            string `dynamic:"true"`
	Description        string `dynamic:"true"`
	WebpageUrl         string `dynamic:"true"`
}

type CallLanguagePreferenceConfig struct {
	CallLangPrefEntryPointScreenConfigMap       map[string]*CallLangPrefEntryPointScreenConfig `dynamic:"true"`
	PreferredCallLangFlowSuccessScreenConfigMap map[string]*CallLangPrefSuccessScreenConfig    `dynamic:"true"`
	SuggestedCallLangFlowSuccessScreenConfigMap map[string]*CallLangPrefSuccessScreenConfig    `dynamic:"true"`
}

type CallLangPrefEntryPointScreenConfig struct {
	Title       *Text `dynamic:"true"`
	Desc        *Text `dynamic:"true"`
	LangCtaDesc *Text `dynamic:"true"`
	ButtonText  *Text `dynamic:"true"`
}

type CallLangPrefSuccessScreenConfig struct {
	Title        *Text              `dynamic:"true"`
	Desc         *Text              `dynamic:"true"`
	EditPrefInfo *Text              `dynamic:"true"`
	InfoIcon     *IconTextComponent `dynamic:"true"`
}

type DcDashboardV2Config struct {
	// contains section order against a given layout
	LayoutSectionOrderMap map[string]*DcHomeLayoutSectionOrder `dynamic:"true"`
	SectionsConfig        *DcDashboardSectionsConfig           `dynamic:"true"`
	// Quest variable to enable DC dashboard v2 screen
	IsQuestCheckEnabledForDashboardV2 bool `dynamic:"true"`
	IsDashboardV2EnabledByQuest       bool `dynamic:"true" ,quest:"variable"`
}

type DcDashboardSectionsConfig struct {
	CardSectionConfig              *DcCardSectionConfig `dynamic:"true"`
	PhysicalCardOrderSectionConfig *DcPhysicalCardOrderSectionConfig
	OffersAndPromosSectionConfig   *DcOffersAndPromosSectionConfig
	BottomSectionConfig            *DcHomeBottomSectionConfig
	ShortcutsSectionConfig         *ShortcutsSectionConfig `dynamic:"true"`
}

type ShortcutsSectionConfig struct {
	PlanTravelBudgetUrl      string                    `dynamic:"true"`
	EnableAtmLocatorShortcut *cfg.PlatformVersionCheck `dynamic:"true"`
	AtmLocatorUserGrpCheck   *UserGroupCheck           `dynamic:"true"`
}
type UserGroupCheck struct {
	// Flag to enable feature for a set of users present in the allowed user groups below.
	// If false we will not have any user group checks.
	EnableUserGroupCheck bool `dynamic:"true"`
	// Set of user group for which feature will be enabled if the EnableUserGroupCheck is true.
	AllowedUserGrp []commontypes.UserGroup
}

type DcHomeBottomSectionConfig struct {
	DcControlsConfig []*DcControlsConfig
}

type DcOffersAndPromosSectionConfig struct {
	PromoWidgetsConfig []*DcPromoWidgetsConfig
}
type DcPromoWidgetsConfig struct {
	Title    *TextObject          `dynamic:"true"`
	SubTitle *TextObject          `dynamic:"true"`
	Image    *VisualElementImage  `dynamic:"true"`
	Lottie   *VisualElementLottie `dynamic:"true"`
	// using this flag can switch between using lottie and image
	UseLottie  bool     `dynamic:"true"`
	SegmentIds []string `dynamic:"true"`
	// mandatory field
	StartDate string `dynamic:"true"` // date layout - "02-01-2006"
	// optional field
	EndDate  string `dynamic:"true"` // date layout - "02-01-2006"
	Deeplink *deeplinkPb.Deeplink
}

type DcPhysicalCardOrderSectionConfig struct {
	UserTierToBenefitsMap map[string][]*DcPhysicalCardBenefitInfoItem
}

type DcPhysicalCardBenefitInfoItem struct {
	IconUrl string `dynamic:"true"`
	// If populated and currently in fc-fp post conversion phase then overwrites IconUrl
	FcFpPostConversionIconUrl string                         `dynamic:"true"`
	Desc                      string                         `dynamic:"true"`
	Deeplink                  *DcPhysicalCardBenefitDeeplink `dynamic:"true"`
}

type DcPhysicalCardBenefitDeeplink struct {
	ScreenName string              `dynamic:"true"`
	Image      *VisualElementImage `dynamic:"true"`
}

type DcCardSectionConfig struct {
	ActivatedStateVersion    int                       `dynamic:"true"`
	ShowTapnPaySettingOnHome *cfg.PlatformVersionCheck `dynamic:"true"`
}

type DcHomeLayoutSectionOrder struct {
	LayoutSectionsOrder []string `dynamic:"true"`
}

type DcControlsConfig struct {
	Title        string
	SubTitle     string
	LeftIconUrl  string
	RightIconUrl string
	Deeplink     string
}

type RecurringPaymentScreenParams struct {
	// map of feRecurringPayment.Action to its corresponding Cta icon url and display text
	RecurringPaymentSupportedActionToCtaIconUrlAndDisplayTextParams map[string]RecurringPaymentSupportedActionToCtaIconUrlAndDisplayText

	// map of recurringPaymentPb.RecurringPaymentType to RecurringPaymentCancellationDisclaimerScreenParams
	RecurringPaymentCancellationDisclaimerScreenParams map[string]RecurringPaymentCancellationDisclaimerScreenParams

	// map of recurringPaymentPb.RecurringPaymentType to RecurringPaymentCancellationSupportTicketParams
	RecurringPaymentCancellationSupportTicketParams map[string]RecurringPaymentCancellationSupportTicketParams

	// map of recurringPaymentPb.RecurringPaymentType to RecurringPaymentCancellationRejectedBannerParams
	RecurringPaymentCancellationRejectedBannerParams map[string]RecurringPaymentCancellationRejectedBannerParams
}

// RecurringPaymentSupportedActionToCtaIconUrlAndDisplayText have config related to supported action ctas in recurring payment.
type RecurringPaymentSupportedActionToCtaIconUrlAndDisplayText struct {
	IconUrl string
	Text    string
}

// RecurringPaymentCancellationDisclaimerScreenParams have config related to RecurringPaymentCancellationDisclaimerScreen.
type RecurringPaymentCancellationDisclaimerScreenParams struct {
	TopImageUrl  string
	CloseIconUrl string
	Title        string
	// config for key points (These are icon text components).
	KeyPoints       []*RecurringPaymentScreenITCParams
	Description     string
	SwipeButtonText string
}

// RecurringPaymentCancellationSupportTicketParams have config related to support ticket section on recurring payment screen.
// It's having config for both active and closed ticket, populating which ever we need.
type RecurringPaymentCancellationSupportTicketParams struct {
	Heading       string
	ActiveTagText string
	ClosedTagText string
	// config for progress fields for active support ticket.
	ProgressFieldsInfoForActive []*ProgressFieldsInfoComponent
	// config for progress fields for closed support ticket.
	ProgressFieldsInfoForClosed []*ProgressFieldsInfoComponent
	// Issue category id which is to abstract out L!,L2 and L3 values for this corresponding support ticket.
	IssueCategoryIdForCxSupportTicket string
}

// ProgressFieldsInfoComponent have config for all the fields to be shown inside support ticket.
type ProgressFieldsInfoComponent struct {
	Title              string
	Description        string
	LeftIconUrl        string
	IsProcessCompleted bool
	Ctas               []*RecurringPaymentScreenITCParams
	CopyOperation      *RecurringPaymentScreenCopyOperationComponent
}

// RecurringPaymentCancellationRejectedBannerParams have config for RecurringPaymentCancellationRejectedBanner.(Which is an icon text component)
type RecurringPaymentCancellationRejectedBannerParams struct {
	Banner *RecurringPaymentScreenITCParams
}

// RecurringPaymentScreenITCParams have config for icon text components in Recurring payment screen.
type RecurringPaymentScreenITCParams struct {
	Content            string
	LeftIconUrl        string
	DeepLinkScreenName string
}

// RecurringPaymentScreenCopyOperationComponent have config related to Copy icon to be shown in recurring payment support ticket section, which helps in copying the ticket id.
type RecurringPaymentScreenCopyOperationComponent struct {
	CopyImageUrl string
	ToastText    string
}

// OffAppEnachCancellationCxTicketParams have subject and description to be added while creating support ticket for cancelling off app eNACH recurring payment.
// We are not generating any comms from this, we are just creating a fresh desk ticket with subject and description present config.
type OffAppEnachCancellationCxTicketParams struct {
	Subject     string
	Description string
}

type VKYCCall struct {
	// TODO(sakthi) remove after a valid dynamic config in this struct
	Dummy bool `dynamic:"true"`

	StreamRetryStrategy *cfg.RetryParams

	MockInitiateVKYCCallParam *MockInitiateVKYCCallParam `dynamic:"true"`
}

// MockInitiateVKYCCallParam have config for mock InitiateVkycCall request params and context
type MockInitiateVKYCCallParam struct {
	IsMockingEnable    bool     `dynamic:"true"`
	WhitelistedActorId []string `dynamic:"true"`
	IpAddress          string   `dynamic:"true"`
	Latitude           string   `dynamic:"true"`
	Longitude          string   `dynamic:"true"`
	IsVpnConnected     bool     `dynamic:"true"`
}

type HelpRecentActivity struct {
	// IsSearchBarShown denotes whether the search bar will be shown to the user
	IsSearchBarShown bool `dynamic:"true"`
	// IsFeatureEnabled serves as global feature flag for help recent activity feature
	IsFeatureEnabled bool `dynamic:"true"`
}

type InAppContactUsFlowConfig struct {
	MinQueryLength int64 `dynamic:"true"`
	MaxQueryLength int64 `dynamic:"true"`
	MinWordCount   int64 `dynamic:"true"`
	// defines what message to be displayed to user when their query doesn't fulfil our validation criteria
	QueryValidationFailureMessage string `dynamic:"true"`
	IsNonFcrFlowEnabled           bool   `dynamic:"true"`
	// CategorySelectionViewMoreThreshold denotes the number of items we will show in the
	// category selection screen, rest of the items will be shown in view more screen
	CategorySelectionViewMoreThreshold int `dynamic:"true"`
	// TerminalScreenRelatedFaqArticleThreshold denotes the number of related faqs that we will show in the
	// terminal screen, we will not show the extra faq articles
	TerminalScreenRelatedFaqArticleThreshold      int                             `dynamic:"true"`
	TicketCreationConfig                          *TicketCreationConfig           `dynamic:"true"`
	AiGeneratedTextNote                           string                          `dynamic:"true"`
	FeedbackComponentConfig                       *FeedbackComponentConfig        `dynamic:"true"`
	RecommendedCategoryDirectUsageConfidenceScore int64                           `dynamic:"true"`
	IsCategoryRecommenderV2Enabled                bool                            `dynamic:"true"`
	NumberOfCategoryCombinationToBeDisplayed      int64                           `dynamic:"true"`
	NumberOfRecommendationForL1Sorting            int64                           `dynamic:"true"`
	NumberOfRecommendationForL2Sorting            int64                           `dynamic:"true"`
	NumberOfRecommendationForL3Sorting            int64                           `dynamic:"true"`
	MaxAndroidVersionForAppUpgradeRedirection     int64                           `dynamic:"true"`
	MaxIosVersionForAppUpgradeRedirection         int64                           `dynamic:"true"`
	ContactUsButtonAnimationConfig                *ContactUsButtonAnimationConfig `dynamic:"true"`
	// Force users to land on chatbot when they click on search bar in the Contact Us screen
	// Temporary experiment
	ForcedChatbotFlowExperimentConfig *ForcedChatbotFlowExperimentConfig `dynamic:"true"`
}

type ForcedChatbotFlowExperimentConfig struct {
	// Master switch to enable/disable the forced chatbot flow
	Enable bool `dynamic:"true"`
	// Percentage of users to be included in the experiment for every cohort
	// Ex - 50 means 50% of the users will be included in the experiment for every cohort
	UserLayerPercentage int64 `dynamic:"true"`
	// Allowed cohorts for whom experiment can be enabled
	// Ex - "ACCOUNT_FREEZE", etc.
	// Constants defined in frontend/inapphelp/contact_us/constant.go
	AllowedCohorts []string `dynamic:"true"`
	// User groups to be included in the experiment
	// NOTE - For these users, we will force chatbot flow irrespective of the cohort they belong to/the user layer percentage
	AllowedUserGroups []commontypes.UserGroup
}

type FeedbackComponentConfig struct {
	IsRequired bool   `dynamic:"true"`
	Title      string `dynamic:"true"`
}

type TicketCreationConfig struct {
	MaxAndroidVersionForTicketCreationOnReporting uint32 `dynamic:"true"`
	MaxIOSVersionForTicketCreationOnReporting     uint32 `dynamic:"true"`
}

type ContactUsButtonAnimationConfig struct {
	// when no delay in display is expected send this default value
	SectionDisplayMinDelay time.Duration `dynamic:"true"`
	// delay after which contact_us should be displayed to user on terminal screen
	SectionDisplayMaxDelay time.Duration `dynamic:"true"`
}

type MoneySecretsConfig struct {
	DisabledMoneySecrets         []string                                  `dynamic:"true"`
	ExplicitLockingFeatureConfig *MoneySecretsExplicitLockingFeatureConfig `dynamic:"true"`
}

type MoneySecretsExplicitLockingFeatureConfig struct {
	// feature config for explicit locking feature
	FeatureConfig *app.FeatureConfig `dynamic:"true"`

	// explicit locking feature will be enabled for users in the rollout percentage range
	EnableForRolloutPercentageStart uint64 `dynamic:"true"`
	EnableForRolloutPercentageEnd   uint64 `dynamic:"true"`

	// explicit locking feature will be disabled for the users in the segment expression
	DisableForSegmentExpression string `dynamic:"true"`

	// this nudge will be used to determine the unlock status of money secrets
	// only if the nudge is completed, the money secrets will be unlocked
	UnlockNudgeId string `dynamic:"true"`
}

type HomeFeatureQuestFlags struct {
	// flag to enable/disable the underlay upi component.
	EnableUnderlayUpiComponent bool `dynamic:"true" ,quest:"variable"`
}

type MoneySecrets struct {
	MfStocksBreakdown *MfStocksBreakdown `dynamic:"true"`
}
type MfStocksBreakdown struct {
	MaxStocks int `dynamic:"true"`
}

type InternationalDcWidgetConfig struct {
	CountryToTopSectionBackgroundImageMap   map[string]string                  `dynamic:"true"`
	DefaultCountryTopSectionBackgroundImage string                             `dynamic:"true"`
	BottomSectionStateWiseVisualElementMap  map[string]string                  `dynamic:"true"`
	CurrencyHeaderText                      string                             `dynamic:"true"`
	CurrencyDescriptionFooter               string                             `dynamic:"true"`
	MidSection                              []*InternationalDcWidgetMidSection `dynamic:"true,readonlylist"`
	CurrencySelectionCtaText                string                             `dynamic:"true"`
	DefaultCountry                          string                             `dynamic:"true"`
}

type InternationalDcWidgetMidSection struct {
	Text     string `dynamic:"true"`
	ImageUrl string `dynamic:"true"`
}

type HomeBalanceSummaryWidgetUiConfig struct {
	StaleBalanceWarningTickerIconTextComponent         *NonCriticalWarningTickerIconTextComponentConfig `dynamic:"true"`
	StaleComputedBalanceWarningTickerIconTextComponent *NonCriticalWarningTickerIconTextComponentConfig `dynamic:"true"`
}

type NonCriticalWarningTickerIconTextComponentConfig struct {
	IsEnabled   bool   `dynamic:"true"`
	TextContent string `dynamic:"true"`
}

type MapperQuickLinkConfig struct {
	// If this is 0, auto link will be disabled and user will have to click on CTA to link
	TimerDuration                int32 `dynamic:"true"`
	ShouldStopAutoLinkOnCtaClick bool  `dynamic:"true"`
}

type DailyNetworthConfig struct {
	NextSteps     map[string]*DailyNetworthNextStepConfig `dynamic:"true"`
	MarketInsight *MarketInsightConfig                    `dynamic:"true"`
}

type MarketInsightConfig struct {
	Header          *MarketInsightHeaderConfig `dynamic:"true"`
	Description     string                     `dynamic:"true"`
	GraphImageURL   string                     `dynamic:"true"`
	Disclaimer      string                     `dynamic:"true"`
	BackgroundColor string                     `dynamic:"true"`
	CornerRadius    int32                      `dynamic:"true"`
}

type MarketInsightHeaderConfig struct {
	Title   string `dynamic:"true"`
	IconURL string `dynamic:"true"`
}

type DailyNetworthNextStepConfig struct {
	Title    string                           `dynamic:"true"`
	Subtitle string                           `dynamic:"true"`
	ImageURL string                           `dynamic:"true"`
	Cards    []*DailyNetworthActionCardConfig `dynamic:"true,readonlylist"`
}

type DailyNetworthActionCardConfig struct {
	Title    string                           `dynamic:"true"`
	Subtitle string                           `dynamic:"true"`
	ImageURL string                           `dynamic:"true"`
	Cta      string                           `dynamic:"true"`
	Deeplink *DailyNetworthActionCardDeeplink `dynamic:"true"`
}

type DailyNetworthActionCardDeeplink struct {
	ScreenName                  string                       `dynamic:"true"`
	SecretAnalyserScreenOptions *SecretAnalyserScreenOptions `dynamic:"true"`
}

type SecretAnalyserScreenOptions struct {
	SecretName           string `dynamic:"true"`
	SecretCollectionName string `dynamic:"true"`
}

// ComplaintSummaryConfig defines the configuration parameters for the complaint summary feature in dispute flow.
// This configuration is served directly from frontend to eliminate unnecessary routing of static configuration through multiple services.
type ComplaintSummaryConfig struct {
	// MinLength specifies the minimum number of characters required in a complaint summary
	MinLength int32 `dynamic:"true"`
	// MaxLength specifies the maximum number of characters allowed in a complaint summary
	MaxLength int32 `dynamic:"true"`
	// RegexPattern defines the allowed characters pattern for complaint summary text
	RegexPattern string `dynamic:"true"`
	// Question represents the prompt text shown to users when asking for complaint details
	Question string `dynamic:"true"`
}

type HelpSectionFAQ struct {
	IsFAQEnabled bool   `dynamic:"true"`
	CategoryId   string `dynamic:"true"`
}

type EarnedRewardsHistoryConfig struct {
	// Map of domain ID string to screen configuration
	DomainIdToScreenConfigmap map[string]*EarnedRewardsScreenConfig `dynamic:"true"`
}

// EarnedRewardsScreenConfig holds static configuration for each domain's earned benefits screen
type EarnedRewardsScreenConfig struct {
	// Top banner configuration
	TopBanner *IconTextComponent `dynamic:"true"`
	// Header configuration
	HeaderViewConfig *EarnedRewardsHistoryHeaderConfig `dynamic:"true"`
	// Timeline configuration for reward processing
	RewardProcessingTimelineConfig *IconTextComponent `dynamic:"true"`
}

// EarnedRewardsHistoryHeaderConfig holds configuration for the header section
type EarnedRewardsHistoryHeaderConfig struct {
	HeaderIcon               *VisualElementImage `dynamic:"true"`
	TotalBenefitsEarnedTitle *IconTextComponent  `dynamic:"true"`
	TotalBenefitsEarnedValue *IconTextComponent  `dynamic:"true"`
	BottomText               *IconTextComponent  `dynamic:"true"`
}
