package secrets

import (
	"context"
	"fmt"
	wbComponents "github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/dashboard"

	strategyImpl "github.com/epifi/gamma/frontend/insights/secrets/portfolio_tracker_builder/strategy"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	goUtils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/logger"
	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"

	headerPb "github.com/epifi/gamma/api/frontend/header"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	"github.com/epifi/gamma/api/insights/networth"
	secretFeEnums "github.com/epifi/gamma/api/insights/secrets/frontend"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"
)

func (s *Service) GetPortfolioTrackerAssetDetailsPage(ctx context.Context, req *secretsFePb.GetPortfolioTrackerAssetDetailsPageRequest) (*secretsFePb.GetPortfolioTrackerAssetDetailsPageResponse, error) {
	errRes := func(rpcStatus *rpcPb.Status, msg string) (*secretsFePb.GetPortfolioTrackerAssetDetailsPageResponse, error) {
		rpcStatus.SetDebugMessage(msg)
		return &secretsFePb.GetPortfolioTrackerAssetDetailsPageResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcStatus,
			},
		}, nil
	}

	actorId := req.GetReq().GetAuth().GetActorId()
	assetType := goUtils.Enum(req.GetRequestParams().GetAssetType(), networth.AssetType_value, networth.AssetType_ASSET_TYPE_UNSPECIFIED)

	assetDistributionBuilder, err := s.assetWiseDistributionFactory.GetAssetWiseDistributionBuilder(assetType)
	if err != nil {
		logger.Error(ctx, "failed to get asset wise distribution builder", zap.Any(logger.ASSET_TYPE, assetType), zap.Error(err))
		return errRes(rpcPb.StatusInternal(), err.Error())
	}

	isWeeklyPortfolioTrackerEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_WEEKLY_PORTFOLIO_TRACKER).WithActorId(actorId))
	if err != nil {
		return nil, fmt.Errorf("failed to evaluate release config for %v: %w", types.Feature_FEATURE_WEEKLY_PORTFOLIO_TRACKER, err)
	}

	weeklyTrackerEnabled := wbComponents.IsWeeklyTrackerEnabled(isWeeklyPortfolioTrackerEnabled)

	// Select strategy based on isWeeklyReport flag
	var strategy strategyImpl.PortfolioTrackerStrategy
	strategy = s.dailyTracker
	if weeklyTrackerEnabled {
		strategy = s.weeklyTracker
	}

	analysisVariables := strategy.GetComponentToVariableMap()[secretFeEnums.PortfolioTrackerComponentType_PORTFOLIO_TRACKER_COMPONENT_TYPE_ASSET_DISTRIBUTION]
	getAnalysisVariableResp, varErr := s.variableGeneratorClient.GetAnalysisVariables(ctx, &analyserVariablePb.GetAnalysisVariablesRequest{
		ActorId:               actorId,
		AnalysisVariableNames: analysisVariables,
	})
	if varErr != nil {
		logger.Error(ctx, "failed to get analysis variables", zap.Error(varErr))
		return errRes(rpcPb.StatusInternal(), varErr.Error())
	}

	assetWiseDistribution, assetWiseDistErr := assetDistributionBuilder.BuildAssetWiseDistribution(ctx, &strategyImpl.BuildAssetWiseDistributionRequest{
		ActorId:             actorId,
		AssetType:           assetType,
		AnalysisVariableMap: getAnalysisVariableResp.GetVariableEnumMap(),
		TrackingStrategy:    strategy,
	})
	if assetWiseDistErr != nil {
		logger.Error(ctx, "failed to build asset wise distribution for asset", zap.Any(logger.ASSET_TYPE, assetType), zap.Error(assetWiseDistErr))
		return errRes(rpcPb.StatusInternal(), assetWiseDistErr.Error())
	}

	var assetDetailsPageTitle string
	// add different titles according to other asset types
	switch assetType {
	case networth.AssetType_ASSET_TYPE_INDIAN_SECURITIES:
		assetDetailsPageTitle = "Indian Stocks Distribution"
	case networth.AssetType_ASSET_TYPE_MUTUAL_FUND:
		assetDetailsPageTitle = "Mutual Fund Distribution"
	}

	return &secretsFePb.GetPortfolioTrackerAssetDetailsPageResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		AssetDetails: &secretsFePb.PortfolioTrackerAssetDetails{
			Title:            commontypes.GetTextFromStringFontColourFontStyle(assetDetailsPageTitle, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_L),
			AssetDetailsCard: assetWiseDistribution.PortfolioTrackerAssetDetailsCard,
		},
	}, nil
}
