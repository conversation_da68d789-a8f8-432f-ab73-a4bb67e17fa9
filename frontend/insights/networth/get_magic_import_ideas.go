package networth

import (
	"context"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	feNetworthPb "github.com/epifi/gamma/api/frontend/insights/networth"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/insights/networth/common"
	"github.com/epifi/gamma/pkg/frontend/header"
)

func (s *Service) GetMagicImportIdeas(ctx context.Context, req *feNetworthPb.GetMagicImportIdeasRequest) (*feNetworthPb.GetMagicImportIdeasResponse, error) {
	logger.Info(ctx, " GetMagicImportIdeas request received")
	ideasSection := s.getIdeasSection(ctx)
	return &feNetworthPb.GetMagicImportIdeasResponse{
		RespHeader: header.SuccessRespHeader(),
		Section:    ideasSection,
	}, nil
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=16048-23163&t=LLSUgth4uga5aj4V-0
func (s *Service) getIdeasSection(ctx context.Context) *sections.Section {
	platform := epificontext.AppPlatformFromContext(ctx)
	spacerComponent := func(spacingValue components.Spacing) *components.Component {
		return &components.Component{
			Content: ui.GetAnyWithoutError(
				&components.Spacer{
					SpacingValue: spacingValue,
				},
			),
		}
	}

	headerComponent := &components.Component{
		Content: ui.GetAnyWithoutError(
			ui.NewITC().WithTexts(
				commontypes.GetTextFromStringFontColourFontStyle("Need ideas?", "#ffffff", commontypes.FontStyle_DISPLAY_M),
				commontypes.GetTextFromStringFontColourFontStyle(" Check below", "#929599", commontypes.FontStyle_DISPLAY_M),
			).
				WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportSparkleIcon, 24, 24)).
				WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(common.MagicImportSparkleIcon, 24, 24)).
				WithContainer(40, 380, 0, "#18191B"),
		),
	}

	assetsListComponent := &components.Component{
		Content: ui.GetAnyWithoutError(
			commontypes.GetVisualElementImageFromUrl(common.MagicImportAssetsListIcon),
		),
	}

	securityComponent := &components.Component{
		Content: ui.GetAnyWithoutError(
			commontypes.GetVisualElementImageFromUrl(common.MagicImportSecurityFirstIcon),
		),
	}

	// Make sure this is always even
	exampleAssetsIcons := []string{
		common.MagicImportAssetsExamplesNikeIcon,
		common.MagicImportAssetsExamplesNikeIcon,
		common.MagicImportAssetsExamplesNikeIcon,
		common.MagicImportAssetsExamplesNikeIcon,
	}
	exampleAssetIconComponent := func(iconUrl string) *components.Component {
		if platform == commontypes.Platform_ANDROID {
			return &components.Component{
				Content: ui.GetAnyWithoutError(
					&sections.HorizontalListSection{
						Components: []*components.Component{
							{
								Content: ui.GetAnyWithoutError(
									commontypes.GetVisualElementImageFromUrl(iconUrl),
								),
							},
						},
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: properties.GetContainerProperty().
										WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 254).
										WithWidthWeight(50),
								},
							},
						},
					},
				),
			}
		}
		return &components.Component{
			Content: ui.GetAnyWithoutError(
				commontypes.GetVisualElementImageFromUrl(iconUrl),
			),
		}
	}
	allComponents := []*components.Component{
		headerComponent,
		spacerComponent(components.Spacing_SPACING_L),
		assetsListComponent,
		spacerComponent(components.Spacing_SPACING_L),
		securityComponent,
		spacerComponent(components.Spacing_SPACING_L),
	}
	// Note: below logic only handles even number of cards
	// todo: no spacer found in between cards
	for i := 0; i < len(exampleAssetsIcons); i += 2 {
		exampleSection := &sections.HorizontalListSection{
			Components: []*components.Component{
				exampleAssetIconComponent(exampleAssetsIcons[i]),
				spacerComponent(components.Spacing_SPACING_S),
				exampleAssetIconComponent(exampleAssetsIcons[i+1]),
			},
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
			VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
		}
		exampleComponent := &components.Component{
			Content: ui.GetAnyWithoutError(
				exampleSection,
			),
		}
		allComponents = append(allComponents, exampleComponent, spacerComponent(components.Spacing_SPACING_L))
	}

	parentSection := &sections.Section_VerticalListSection{
		VerticalListSection: &sections.VerticalListSection{
			Components: allComponents,
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: properties.GetContainerProperty().
							WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 1424).
							WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 412).
							WithBlockBgColor("#18191B").
							WithPadding(16, 0, 16, 0),
					},
				},
			},
		},
	}

	return &sections.Section{
		Content: parentSection,
	}
}
