// nolint
package firefly

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strconv"
	"time"

	pkgErr "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	anyPb "google.golang.org/protobuf/types/known/anypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	typesUiWidget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	accountPb "github.com/epifi/gamma/api/accounts"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountBalEnumsPb "github.com/epifi/gamma/api/accounts/balance/enums"
	consentPb "github.com/epifi/gamma/api/consent"
	depositPb "github.com/epifi/gamma/api/deposit"
	ffBePb "github.com/epifi/gamma/api/firefly"
	ffBeAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	ffEnumsBePb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/errors"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/frontend/firefly/enums"
	"github.com/epifi/gamma/api/frontend/header"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	savingsPb "github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	ffHelper "github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/gamma/frontend/firefly/helper"
	"github.com/epifi/gamma/frontend/firefly/internal"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

var (
	screenToSignalContextMap = map[deeplink.Screen]ffEnumsBePb.SignalContext{
		deeplink.Screen_DEEP_LINK_URI_UNSPECIFIED:             ffEnumsBePb.SignalContext_SIGNAL_CONTEXT_UNSPECIFIED,
		deeplink.Screen_SECURED_CREDIT_CARD_FD_DETAILS_SCREEN: ffEnumsBePb.SignalContext_SIGNAL_CONTEXT_USER_CONTINUE_FD_SUCCESS,
		deeplink.Screen_CC_CREDIT_LIMIT_UPDATE_SCREEN:         ffEnumsBePb.SignalContext_SIGNAL_CONTEXT_RECORDED_USER_ACTION_ON_LIMIT_CHANGE_SCREEN,
	}

	visibleDurationForDurationSlider = []int32{
		400,
		500,
		730,
		1095,
		1460,
	}
)

func (s *Service) GetSecuredCreditCardDepositCalculations(ctx context.Context, req *ffPb.GetSecuredCreditCardDepositCalculationsRequest) (*ffPb.GetSecuredCreditCardDepositCalculationsResponse, error) {
	var (
		res                                                                                                                     = &ffPb.GetSecuredCreditCardDepositCalculationsResponse{}
		depositConfigRes                                                                                                        = &ffBePb.GetDepositConfigResponse{}
		depositCalculationRes                                                                                                   = &depositPb.CalculateMaturityInfoResponse{}
		savingsAccInfo                                                                                                          = &savingsPb.GetAccountResponse{}
		depositAmount                                                                                                           = &money.Money{}
		depositInterestRatesResp                                                                                                = &depositPb.GetInterestRatesForActorResponse{}
		accountBalanceInfo                                                                                                      = &accountBalancePb.GetAccountBalanceResponse{}
		updateCreditLimitResp                                                                                                   = &ffBePb.UpdateSecuredCardCreditLimitResponse{}
		depositConfigErr, depositCalculationErr, savingsAccErr, depositInterestRateErr, accountBalanceErr, updateCreditLimitErr error
	)

	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*ffPb.GetSecuredCreditCardDepositCalculationsResponse, error) {
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		depositConfigRes, depositConfigErr = s.fireFlyClient.GetDepositConfig(grpCtx, &ffBePb.GetDepositConfigRequest{
			Vendor: types.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
		})

		if configErr := epifigrpc.RPCError(depositConfigRes, depositConfigErr); configErr != nil {
			logger.Error(grpCtx, "error in fetching config from lending: ", zap.Error(configErr))
			return configErr
		}
		if req.GetDepositTerm() == nil {
			req.DepositTerm = depositConfigRes.GetDepositTerm()
		}
		depositAmount = ffPkg.GetDepositAmountFromCreditLimit(req.GetCreditLimit().GetBeMoney(), depositConfigRes.GetDepositMultiplier())
		depositCalculationRes, depositCalculationErr = s.depositClient.CalculateMaturityInfo(grpCtx, &depositPb.CalculateMaturityInfoRequest{
			Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
			CreationDate: datetime.TimeToDateInLoc(timestampPb.Now().AsTime(), datetime.IST),
			Term:         req.GetDepositTerm(),
			Amount:       depositAmount,
			ActorId:      req.GetReq().GetAuth().GetActorId(),
			DepositType:  accountPb.Type_FIXED_DEPOSIT,
			Scheme:       depositPb.DepositScheme_FD_CASH_CERTIFICATE,
		})
		if depositCalcErr := epifigrpc.RPCError(depositCalculationRes, depositCalculationErr); depositCalcErr != nil {
			logger.Error(grpCtx, "error in fetching calculations from deposits: ", zap.Error(depositCalcErr))
			return depositCalcErr
		}
		updateCreditLimitResp, updateCreditLimitErr = s.fireFlyClient.UpdateSecuredCardCreditLimit(grpCtx, &ffBePb.UpdateSecuredCardCreditLimitRequest{
			ActorId:       req.GetReq().GetAuth().GetActorId(),
			CreditLimit:   req.GetCreditLimit().GetBeMoney(),
			DepositAmount: depositAmount,
		})
		if updateErr := epifigrpc.RPCError(updateCreditLimitResp, updateCreditLimitErr); updateErr != nil {
			logger.Error(ctx, "error updating the credit limit", zap.Error(updateErr))
			return updateErr
		}
		return nil
	})
	grp.Go(func() error {
		savingsAccInfo, savingsAccErr = s.savingsClient.GetAccount(grpCtx,
			&savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_ActorId{
				ActorId: req.GetReq().GetAuth().GetActorId(),
			}})
		if savingsAccErr != nil {
			logger.Error(grpCtx, pkgErr.Wrap(savingsAccErr, "error in fetching saving account details").Error())
			return savingsAccErr
		} else if savingsAccInfo.GetAccount() == nil {
			logger.Error(grpCtx, "No savings account record found")
			return epifierrors.ErrRecordNotFound
		}
		accountBalanceInfo, accountBalanceErr = s.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
			Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: savingsAccInfo.GetAccount().GetId()},
			ActorId:       req.GetReq().GetAuth().GetActorId(),
			DataFreshness: accountBalEnumsPb.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
		})
		if te := epifigrpc.RPCError(accountBalanceInfo, accountBalanceErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching account balance").Error())
			return pkgErr.Wrap(te, "error in fetching account balance")
		}
		return nil
	})
	grp.Go(func() error {
		depositInterestRatesResp, depositInterestRateErr = s.depositClient.GetInterestRatesForActor(grpCtx, &depositPb.GetInterestRatesForActorRequest{
			Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
			ActorId:     req.GetReq().GetAuth().GetActorId(),
			DepositType: accountPb.Type_FIXED_DEPOSIT,
		})
		if depositInterestErr := epifigrpc.RPCError(depositInterestRatesResp, depositInterestRateErr); depositInterestErr != nil {
			logger.Error(grpCtx, "error in fetching interest rates from deposits", zap.Error(depositInterestErr))
			return depositInterestErr
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in collecting details for secured cards deposit calculation", zap.Error(err))
		return responseWithStatus(rpc.StatusInternal(), nil)
	}

	if len(depositInterestRatesResp.GetInterestRates()) == 0 {
		logger.Error(ctx, "error while fetching deposit interest details from deposit client")
		return responseWithStatus(rpc.StatusInternal(), nil)
	}
	depositEditableDetails, err := s.getDepositEditableDetails(ctx, depositCalculationRes, depositInterestRatesResp.GetInterestRates(), depositConfigRes, req.GetDepositTerm())
	if err != nil {
		logger.Error(ctx, "error while fetching deposit editable details", zap.Error(err))
		return responseWithStatus(rpc.StatusInternal(), nil)
	}

	return &ffPb.GetSecuredCreditCardDepositCalculationsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		DepositAmount:  types.GetFromBeMoney(depositAmount),
		MaturityAmount: types.GetFromBeMoney(depositCalculationRes.GetMaturityAmount()),
		CreditLimit:    req.GetCreditLimit(),
		DepositDisplayDetails: []*deeplink.InfoItemV2{
			{
				Icon: internal.BestInMarketInterestIconUrl,
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{Html: internal.InterestRateTitleHtml},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
					FontColor: internal.FontColorGrayLead,
				},
				Desc: &commontypes.Text{
					FontColor:    internal.FontColorGrayLead,
					DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%.2f%%", depositCalculationRes.GetInterestRate())},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
					},
				},
			},
			{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{Html: internal.MaturityAmountTitleHtml},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
					FontColor: internal.FontColorGrayLead,
				},
				Desc: &commontypes.Text{
					FontColor:    internal.FontColorGrayLead,
					DisplayValue: &commontypes.Text_PlainString{PlainString: moneyPkg.ToDisplayString(depositCalculationRes.GetMaturityAmount())},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
					},
				},
			},
		},
		CurrentAccountBalance:  types.GetFromBeMoney(accountBalanceInfo.GetAvailableBalance()),
		DepositEditableDetails: depositEditableDetails,
	}, nil
}

func (s *Service) CreateDeposit(ctx context.Context, req *ffPb.CreateDepositRequest) (*ffPb.CreateDepositResponse, error) {
	var (
		res = &ffPb.CreateDepositResponse{}
	)

	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*ffPb.CreateDepositResponse, error) {
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}

	consentResp, err := s.consentClient.RecordConsents(ctx, &consentPb.RecordConsentsRequest{
		Consents: []*consentPb.ConsentRequestInfo{
			{
				ConsentType: consentPb.ConsentType_SECURED_CREDIT_CARD_OPEN_FIXED_DEPOSIT,
			},
			{
				ConsentType: consentPb.ConsentType_SECURED_CREDIT_CARD_KFS,
			},
			{
				ConsentType: consentPb.ConsentType_SECURED_CREDIT_CARD_MOST_IMP_TNC,
			},
			{
				ConsentType: consentPb.ConsentType_FI_CREDIT_CARD_TNC,
			},
		},
		ActorId: req.GetReq().GetAuth().GetActorId(),
		Device:  req.GetReq().GetAuth().GetDevice(),
		Owner:   commontypes.Owner_OWNER_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(consentResp, err); te != nil {
		logger.Error(ctx, "error while calling RecordConsents", zap.Error(te))
		return responseWithStatus(consentResp.GetStatus(), genericErrorView)
	}
	cardRequestResp, err := s.fireFlyClient.GetCardRequestByActorIdAndWorkflow(ctx, &ffBePb.GetCardRequestByActorIdAndWorkflowRequest{
		ActorId:             req.GetReq().GetAuth().GetActorId(),
		CardRequestWorkFlow: ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
	})
	if te := epifigrpc.RPCError(cardRequestResp, err); te != nil {
		logger.Error(ctx, "error fetching card request", zap.Error(te))
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}
	cardProgram := cardRequestResp.GetCardRequest().GetRequestDetails().GetCardProgram()

	depositConfig, depositConfigErr := s.fireFlyClient.GetDepositConfig(ctx, &ffBePb.GetDepositConfigRequest{
		Vendor: types.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
	})
	if te := epifigrpc.RPCError(depositConfig, depositConfigErr); te != nil {
		logger.Error(ctx, "error while calling RecordConsents", zap.Error(te))
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}

	if !moneyPkg.IsZero(req.GetCreditLimit().GetBeMoney()) && !moneyPkg.IsZero(req.GetDepositAmount().GetBeMoney()) {
		calculatedCreditLimit := ffPkg.GetCreditLimitFromDepositAmount(req.GetDepositAmount().GetBeMoney(), depositConfig.GetCreditMultiplier())
		if !moneyPkg.AreEquals(calculatedCreditLimit, req.GetCreditLimit().GetBeMoney()) {
			// TODO: add bottom sheet error view
			return responseWithStatus(rpc.StatusInvalidArgument(), nil)
		}
		if !ffPkg.IsFiLiteProgram(cardProgram) && ffPkg.IsCreditCardProgramSecured(cardProgram) {
			savingsAccInfo, savingsAccErr := s.savingsClient.GetAccount(ctx,
				&savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_ActorId{
					ActorId: req.GetReq().GetAuth().GetActorId(),
				}})
			if savingsAccErr != nil {
				logger.Error(ctx, pkgErr.Wrap(savingsAccErr, "error in fetching saving account details").Error())
				return responseWithStatus(rpc.StatusInternal(), genericErrorView)
			} else if savingsAccInfo.GetAccount() == nil {
				logger.Error(ctx, "No savings account record found")
				return responseWithStatus(rpc.StatusInternal(), genericErrorView)
			}
			accountBalanceInfo, accountBalanceErr := s.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
				Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: savingsAccInfo.GetAccount().GetId()},
				ActorId:       req.GetReq().GetAuth().GetActorId(),
				DataFreshness: accountBalEnumsPb.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
			})
			if te := epifigrpc.RPCError(accountBalanceInfo, accountBalanceErr); te != nil {
				logger.Error(ctx, pkgErr.Wrap(te, "error in fetching account balance").Error())
				return responseWithStatus(rpc.StatusInternal(), genericErrorView)
			}

			compareAmount, err := moneyPkg.CompareV2(req.GetDepositAmount().GetBeMoney(), accountBalanceInfo.GetAvailableBalance())
			if err != nil {
				logger.Error(ctx, pkgErr.Wrap(err, "error in comparing deposit amount and balance").Error())
				return responseWithStatus(rpc.StatusInternal(), genericErrorView)
			}
			if compareAmount > 0 {
				logger.Debug(ctx, "insufficient funds in account, sending bottom sheet cta")
				return responseWithStatus(rpc.StatusInvalidArgument(), insufficientFundBottomSheetErrorView)
			}
		}
	}

	beRes, err := s.fireFlyClient.CreateDeposit(ctx, &ffBePb.CreateDepositRequest{
		Amount:         req.GetAmount().GetBeMoney(),
		NomineeDetails: getNomineeDetails(req.GetNomineeDetails()),
		ActorId:        req.GetReq().GetAuth().GetActorId(),
		DepositTerm:    req.GetDepositTerm(),
		DepositAmount:  req.GetDepositAmount().GetBeMoney(),
		CreditLimit:    req.GetCreditLimit().GetBeMoney(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		switch {
		case beRes.GetStatus().IsAlreadyExists():
			logger.Error(ctx, "deposit id already exists for card request id", zap.Error(te))
			res.NextAction = beRes.GetNextAction()
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusOk(),
			}
		default:
			logger.Error(ctx, "error in be rpc for initiating deposit request", zap.Error(te))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			}
		}
		return res, nil
	}

	res.NextAction = beRes.GetNextAction()
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func getNomineeDetails(nominees []*ffPb.NomineeDetails) *types.DepositNomineeDetails {
	nomineeDetails := make([]*types.DepositNomineeDetails_DepositNomineeInfo, 0)
	for _, nominee := range nominees {
		nomineeDetails = append(nomineeDetails, &types.DepositNomineeDetails_DepositNomineeInfo{
			NomineeId:       nominee.GetNomineeId(),
			PercentageShare: strconv.FormatFloat(nominee.GetNomineeSharePercentage(), 'f', -1, 64),
		})
	}
	return &types.DepositNomineeDetails{
		NomineeInfoList: nomineeDetails,
	}
}

func (s *Service) startCardOnboardingV2(ctx context.Context, req *ffPb.StartCardOnboardingRequest) *ffPb.StartCardOnboardingResponse {
	var (
		res = &ffPb.StartCardOnboardingResponse{}
	)

	isWfEnabled, err := ffPkg.EvaluateConstraintsForWorkflow(ctx, s.dynamicConf.CreditCard(), ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_CARD_ONBOARDING)
	if err != nil {
		logger.Error(ctx, "Error in performing workflow constraints evaluation for : ", zap.String(logger.WORKFLOW, ffEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_CARD_ONBOARDING.String()), zap.Error(err))
		res.NextAction = ffHelper.GetRetryableFailureDeeplink(&deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
		}, &types.CardProgram{
			CardProgramVendor:     types.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
			CardProgramSource:     types.CardProgramSource_CARD_PROGRAM_SOURCE_REALTIME_BRE,
			CardProgramType:       types.CardProgramType_CARD_PROGRAM_TYPE_SECURED,
			CardProgramCollateral: types.CardProgramCollateral_CARD_PROGRAM_COLLATERAL_FD,
			CardProgramOrigin:     types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI,
		})
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusOk(),
		}
		return res
	}

	if !isWfEnabled {
		logger.Info(ctx, "User is on an older app version than required for secured card onboarding",
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()),
			zap.String(logger.PLATFORM, req.GetReq().GetAuth().GetDevice().GetPlatform().String()),
			zap.String(logger.APP_PLATFORM, req.GetReq().GetAuth().GetDevice().GetPlatform().String()),
			zap.Int(logger.APP_VERSION_CODE, int(req.GetReq().GetAuth().GetDevice().GetAppVersion())))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusOk(),
		}
		res.NextAction = s.getForceUpdateScreenNextAction(req.GetReq(), "We’ve rolled out some new fixes that are necessary to avail a credit card.")
		return res
	}

	beRes, err := s.fireFlyClient.StartCardOnboardingV2(ctx, &ffBePb.StartCardOnboardingV2Request{
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		Provenance:      ffEnumsBePb.Provenance_PROVENANCE_APP,
		CardNetworkType: feCardNetworkTypeToBeCardNetworkType[req.GetCardNetworkType()],
		CardProgram:     req.GetCardProgram(),
	})
	if te := epifigrpc.RPCError(beRes, err); te != nil {
		logger.Error(ctx, "error in be rpc for starting card onboarding", zap.Error(te))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
			ErrorView: &errors.ErrorView{
				Type:    errors.ErrorViewType_BOTTOM_SHEET,
				Options: &errors.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errors.BottomSheetErrorView{}},
			},
		}
		return res
	}
	res.CardRequestId = beRes.GetCardRequestId()
	res.NextAction = beRes.GetNextAction()
	res.OfferId = beRes.GetOfferId()
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res
}

func (s *Service) ProceedWithUserAction(ctx context.Context, req *ffPb.ProceedWithUserActionRequest) (*ffPb.ProceedWithUserActionResponse, error) {
	var (
		res = &ffPb.ProceedWithUserActionResponse{}
	)

	signalContext, ok := screenToSignalContextMap[req.GetScreen()]
	if !ok {
		logger.Error(ctx, "error fetching signal context from screen", zap.String(logger.SCREEN, req.GetScreen().String()))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	}

	moveOnResponse, err := s.fireFlyClient.SignalMoveOn(ctx, &ffBePb.SignalMoveOnRequest{
		CardRequestId: req.GetCardRequestId(),
		SignalContext: signalContext,
	})

	if te := epifigrpc.RPCError(moveOnResponse, err); te != nil {
		logger.Error(ctx, "error signalling to move on: ", zap.Error(te))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	}

	res.NextAction = moveOnResponse.GetNextAction()
	res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}

	return res, nil
}

func (s *Service) getDepositEditableDetails(ctx context.Context, depositCalculationRes *depositPb.CalculateMaturityInfoResponse, depositInterestRates []*types.DepositInterestDetails, depositConfigRes *ffBePb.GetDepositConfigResponse, depositTerm *types.DepositTerm) ([]*deeplink.InfoItemWithCtaV3,
	error) {

	depositInterestRates = ffPkg.GetDepositInterestDetailsList(depositInterestRates, depositConfigRes.GetMinDepositTerm(), depositConfigRes.GetMaxDepositTerm())
	if depositInterestRates == nil {
		return nil, pkgErr.New("deposit interest rates and terms not found for given min and max terms")
	}
	durationSliderValues := s.getDurationSliderValuesFromDepositInterestDetails(depositInterestRates)
	depositEditableDetails := make([]*deeplink.InfoItemWithCtaV3, 0)

	tenureDetailsView := ffHelper.GetDepositTenureDetailsDeeplink(depositInterestRates, depositTerm, depositConfigRes, durationSliderValues)

	tenureDetailsMarshalled, err := anyPb.New(tenureDetailsView)
	if err != nil {
		return nil, pkgErr.Wrap(err, "error in marshalling tenure details: ")
	}

	depositEditableDetails = append(depositEditableDetails,
		&deeplink.InfoItemWithCtaV3{
			Info: &deeplink.InfoItemV3{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{Html: internal.InterestRateTitleHtml},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
					FontColor: internal.FontColorGrayLead,
				},
				SubTitle: &commontypes.Text{
					FontColor:    internal.FontColorGrayLead,
					DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%.2f%%", depositCalculationRes.GetInterestRate())},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
					},
				},
			},
		},
		&deeplink.InfoItemWithCtaV3{
			Info: &deeplink.InfoItemV3{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{Html: internal.MaturityAmountTitleHtml},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
					FontColor: internal.FontColorGrayLead,
				},
				SubTitle: &commontypes.Text{
					FontColor:    internal.FontColorGrayLead,
					DisplayValue: &commontypes.Text_PlainString{PlainString: moneyPkg.ToDisplayString(depositCalculationRes.GetMaturityAmount())},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
					},
				},
			},
		},
		&deeplink.InfoItemWithCtaV3{
			Info: &deeplink.InfoItemV3{
				Icon: commontypes.GetVisualElementImageFromUrl(internal.EditPencilIcon).WithProperties(&commontypes.VisualElementProperties{Width: 12, Height: 12}),
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{Html: internal.DepositTenureTitleHtml},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
					FontColor: internal.FontColorGrayLead,
				},
				SubTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: ffPkg.GetDepositTermString(depositTerm)},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
					FontColor: internal.FontColorGrayLead,
				},
			},
			Cta: &deeplink.Cta{
				Deeplink: &deeplink.Deeplink{
					Screen:          deeplink.Screen_SECURED_CC_DEPOSIT_TENURE_SELECTION_BOTTOM_SHEET_SCREEN,
					ScreenOptionsV2: tenureDetailsMarshalled,
				},
			},
		},
	)
	return depositEditableDetails, nil
}

func (s *Service) getDurationSliderValuesFromDepositInterestDetails(depositInterestDetails []*types.DepositInterestDetails) []*types.DurationSliderValue {
	durationSliderValues := make([]*types.DurationSliderValue, 0)
	for _, depositInterestDetail := range depositInterestDetails {
		durationSliderValues = append(durationSliderValues, &types.DurationSliderValue{
			DurationText: &commontypes.Text{
				FontColor:    "#A4A4A4",
				DisplayValue: &commontypes.Text_PlainString{PlainString: ffPkg.GetDurationInShortText(ffPkg.GetDurationFromDepositTerm(depositInterestDetail.GetTerm(), time.Now()))},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_1},
			},
			InterestRateText: &commontypes.Text{
				FontColor:    "#694980",
				BgColor:      "#CDC6E8",
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%s%% p.a.", depositInterestDetail.GetInterestRate())},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_1},
			},
			Duration:     depositInterestDetail.GetTerm(),
			InterestRate: depositInterestDetail.GetInterestRate(),
		})
	}
	return ffPkg.SetShouldDisplayValueForSlider(visibleDurationForDurationSlider, durationSliderValues)
}

func (s *Service) startRealtimeEligibilityCheck(ctx context.Context, req *ffPb.StartCardOnboardingRequest) *ffPb.StartCardOnboardingResponse {
	res := &ffPb.StartCardOnboardingResponse{}
	beRes, err := s.fireFlyClient.TriggerRealtimeCardEligibilityCheck(ctx, &ffBePb.TriggerRealtimeCardEligibilityCheckRequest{
		ActorId:          req.GetReq().GetAuth().GetActorId(),
		ScreenIdentifier: req.GetScreenIdentifier(),
		CardProgram:      req.GetCardProgram(),
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in triggering real time eligibility check", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res
	case !beRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non success status for triggering real time eligibility check for the user",
			zap.String(logger.STATUS, beRes.GetStatus().String()))
		res.RespHeader = &header.ResponseHeader{
			Status: beRes.GetStatus(),
		}
		return res
	default:
		res.NextAction = beRes.GetNextAction()
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusOk(),
		}
		return res
	}
}

func (s *Service) GetSecuredCardRewardsDashboardInfo(ctx context.Context, creditCardInfo *ffBePb.GetCreditCardResponse, creditAccountInfo *ffBeAccountsPb.GetAccountResponse) (*ffPb.GetRewardDetailsResponse_SecuredRewardsConstructInformation, error) {
	startDate, endDate, startTimeStamp, endTimeStamp := ffPkg.GetBillingWindowFromTime(creditCardInfo.GetCreditCard().GetBasicInfo().GetBillGenDate(), datetime.TimestampToTime(timestampPb.Now()))
	weekdaysRewardOffer, err := s.rewardsProjectionClient.GetRewardsProjections(ctx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
		ActorId: creditCardInfo.GetCreditCard().GetActorId(),
		Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
			OfferType: []rewardsPb.RewardOfferType{
				rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER,
			},
			TimeWindow: &rewardsPb.TimeWindow{
				FromTime: startTimeStamp,
				TillTime: endTimeStamp,
			},
		},
		FetchAggregates: true,
	})
	if te := epifigrpc.RPCError(weekdaysRewardOffer, err); te != nil {
		return nil, pkgErr.Wrap(te, "error fetching weekday rewards info")
	}
	weekendRewardsOffer, err := s.rewardsProjectionClient.GetRewardsProjections(ctx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
		ActorId: creditCardInfo.GetCreditCard().GetActorId(),
		Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
			OfferType: []rewardsPb.RewardOfferType{
				rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER,
			},
			TimeWindow: &rewardsPb.TimeWindow{
				FromTime: startTimeStamp,
				TillTime: endTimeStamp,
			},
		},
		FetchAggregates: true,
	})
	if te := epifigrpc.RPCError(weekendRewardsOffer, err); te != nil {
		return nil, pkgErr.Wrap(te, "error fetching weekend rewards info")
	}

	weekdayRewards, weekendRewards := 0, 0
	if len(weekdaysRewardOffer.GetAggregateProjections().GetRewardUnitsDetails()) > 0 {
		weekdayRewards = int(weekdaysRewardOffer.GetAggregateProjections().GetRewardUnitsDetails()[0].GetProjectedRewardUnits())
	}

	if len(weekendRewardsOffer.GetAggregateProjections().GetRewardUnitsDetails()) > 0 {
		weekendRewards = int(weekendRewardsOffer.GetAggregateProjections().GetRewardUnitsDetails()[0].GetProjectedRewardUnits())
	}

	return &ffPb.GetRewardDetailsResponse_SecuredRewardsConstructInformation{
		SecuredRewardsConstructInformation: &ffPb.SecuredRewardsConstructInformation{
			BillingCycleInfo: getBillingCycleInfo(startDate, endDate, creditAccountInfo.GetAccount().GetCardProgram()),
			RewardsConstructInformation: []*ffPb.RewardsConstructInformation{
				{
					RewardsInformationCategory: &ffPb.RewardsConstructInformation_RewardsInformationCategoryBody{
						RewardsInformationCategoryBody: &ffPb.RewardsInformationCategoryBody{
							PrimaryVisualElement: commontypes.GetVisualElementImageFromUrl(ffPkg.GetSecuredCardsWeekdayRewardsDashboardIconUrl()).
								WithImageType(commontypes.ImageType_PNG).
								WithProperties(&commontypes.VisualElementProperties{Width: 48, Height: 48}),
							SecondaryVisualElement: commontypes.GetVisualElementImageFromUrl(internal.FiProcessingRewardsIconUrl).
								WithImageType(commontypes.ImageType_PNG).
								WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28}),
							PrimaryText: &commontypes.Text{
								DisplayValue: &commontypes.Text_Html{
									Html: fmt.Sprintf("%dX WEEKDAY REWARDS <font color='#8D8D8D'>(MON-FRI)</font>", ffPkg.GetSecuredCardRewardsConstructWeekdayMultiplier()),
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS,
								},
							},
							SecondaryText: &commontypes.Text{
								FontColor: "#8D8D8D",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: fmt.Sprintf("%d", int(weekdayRewards)),
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_NUMBER_XL,
								},
							},
							BgColor: &typesUiWidget.BackgroundColour{
								Colour: &typesUiWidget.BackgroundColour_BlockColour{
									BlockColour: "#FFFFFF",
								},
							},
						},
					},
				},
				{
					RewardsInformationCategory: &ffPb.RewardsConstructInformation_RewardsInformationCategoryBody{
						RewardsInformationCategoryBody: &ffPb.RewardsInformationCategoryBody{
							PrimaryVisualElement: commontypes.GetVisualElementImageFromUrl(ffPkg.GetSecuredCardsWeekendRewardsDashboardIconUrl()).
								WithImageType(commontypes.ImageType_PNG).
								WithProperties(&commontypes.VisualElementProperties{Width: 48, Height: 48}),
							SecondaryVisualElement: commontypes.GetVisualElementImageFromUrl(internal.FiProcessingRewardsIconUrl).
								WithImageType(commontypes.ImageType_PNG).
								WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28}),
							PrimaryText: &commontypes.Text{
								DisplayValue: &commontypes.Text_Html{
									Html: fmt.Sprintf("%dX WEEKEND REWARDS <font color='#8D8D8D'>(SAT-SUN)</font>", ffPkg.GetSecuredCardRewardsConstructWeekendMultiplier()),
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS,
								},
							},
							SecondaryText: &commontypes.Text{
								FontColor: "#8D8D8D",
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: fmt.Sprintf("%d", int(weekendRewards)),
								},
								FontStyle: &commontypes.Text_StandardFontStyle{
									StandardFontStyle: commontypes.FontStyle_NUMBER_XL,
								},
							},
							BgColor: &typesUiWidget.BackgroundColour{
								Colour: &typesUiWidget.BackgroundColour_BlockColour{
									BlockColour: "#FFFFFF",
								},
							},
						},
					},
				},
				{
					RewardsInformationCategory: &ffPb.RewardsConstructInformation_RewardsInformationCategoryFooter{
						RewardsInformationCategoryFooter: &ffPb.RewardsInformationCategoryFooter{
							FooterText: &commontypes.Text{
								FontColor:    "#646464",
								DisplayValue: &commontypes.Text_PlainString{PlainString: "Fi-Coins get credited 10 days after bill generation"},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							},
							BgColor: &typesUiWidget.BackgroundColour{
								Colour: &typesUiWidget.BackgroundColour_BlockColour{
									BlockColour: "#F7F9FA",
								},
							},
						},
					},
				},
			},
		},
	}, nil
}

func (s *Service) getSecuredTxnRewardsInfo(ctx context.Context, refIds []string, creditAccount *ffBeAccountsPb.CreditAccount) (map[string]*ffPb.RewardsInfo, error) {
	// fetching rewards for non reversal/refund txns
	externalTxnIdToRewardsInfo := make(map[string]*ffPb.RewardsInfo, 0)
	getRewardsResWeekdays, err := s.rewardsProjectionClient.GetRewardsProjections(ctx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
		ActorId: creditAccount.GetActorId(),
		Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
			RefIds: refIds,
			OfferType: []rewardsPb.RewardOfferType{
				rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER,
			},
		},
		PageCtxRequest: &rpc.PageContextRequest{
			PageSize: uint32(len(refIds)),
		},
	})
	if te := epifigrpc.RPCError(getRewardsResWeekdays, err); te != nil {
		return nil, fmt.Errorf("error in GetRewardsProjections api call %w", te)
	}
	getRewardsResWeekends, err := s.rewardsProjectionClient.GetRewardsProjections(ctx, &rewardsProjectionPb.GetRewardsProjectionsRequest{
		ActorId: creditAccount.GetActorId(),
		Filters: &rewardsProjectionPb.GetRewardsProjectionsRequest_Filters{
			RefIds: refIds,
			OfferType: []rewardsPb.RewardOfferType{
				rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER,
			},
		},
		PageCtxRequest: &rpc.PageContextRequest{
			PageSize: uint32(len(refIds)),
		},
	})
	if te := epifigrpc.RPCError(getRewardsResWeekends, err); te != nil {
		return nil, fmt.Errorf("error in GetRewardsProjections api call %w", te)
	}
	for _, txnProjection := range getRewardsResWeekdays.GetIndividualProjections().GetProjections() {
		// if the txn projection contains a reward id, the actual reward is generated
		var rewardUnits float32
		rewardsIcon := internal.FiCoinInProgressIcon
		if len(txnProjection.GetProjectedOptions().GetRewardUnitsWithTypes()) > 0 {
			rewardUnits = txnProjection.GetProjectedOptions().GetRewardUnitsWithTypes()[0].GetRewardUnits()
		}
		if txnProjection.GetRewardId() != "" && len(txnProjection.GetRewardContributions().GetRewardUnitsWithTypes()) > 0 {
			rewardUnits = txnProjection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits()
			rewardsIcon = internal.FiCoinSuccessIcon
		}
		externalTxnIdToRewardsInfo[txnProjection.GetRefId()] = &ffPb.RewardsInfo{
			RewardUnits: rewardUnits,
			ImageUrl:    rewardsIcon,
		}
	}
	for _, txnProjection := range getRewardsResWeekends.GetIndividualProjections().GetProjections() {
		// if the txn projection contains a reward id, the actual reward is generated
		var rewardUnits float32
		rewardsIcon := internal.FiCoinInProgressIcon
		if len(txnProjection.GetProjectedOptions().GetRewardUnitsWithTypes()) > 0 {
			rewardUnits = txnProjection.GetProjectedOptions().GetRewardUnitsWithTypes()[0].GetRewardUnits()
		}
		if txnProjection.GetRewardId() != "" && len(txnProjection.GetRewardContributions().GetRewardUnitsWithTypes()) > 0 {
			rewardUnits = txnProjection.GetRewardContributions().GetRewardUnitsWithTypes()[0].GetRewardUnits()
			rewardsIcon = internal.FiCoinSuccessIcon
		}
		externalTxnIdToRewardsInfo[txnProjection.GetRefId()] = &ffPb.RewardsInfo{
			RewardUnits: rewardUnits,
			ImageUrl:    rewardsIcon,
		}
	}
	return externalTxnIdToRewardsInfo, nil
}

func (s *Service) getSecuredBillAggregatedRewards(ctx context.Context, billIds []string, billResp []*billing.CreditCardBill, creditAccount *ffBeAccountsPb.CreditAccount) ([]*ffPb.AggregatedRewardsInfo, error) {
	if creditAccount.GetCardProgram().GetCardProgramType() != types.CardProgramType_CARD_PROGRAM_TYPE_SECURED {
		return nil, pkgErr.New("card program sent for fetching secured bill aggregates is not SECURED")
	}
	weekdayRewards := &rewardsPb.GetRewardsResponse{}
	weekendRewards := &rewardsPb.GetRewardsResponse{}
	var weekdayRewardErr, weekendRewardErr error
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		// weekdays rewards fetch
		weekdayRewards, weekdayRewardErr = s.rewardsClient.GetRewards(grpCtx, &rewardsPb.GetRewardsRequest{
			ExternalRefIds:  billIds,
			RewardOfferType: rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKDAYS_OFFER,
		})
		if te := epifigrpc.RPCError(weekdayRewards, weekdayRewardErr); te != nil {
			logger.Error(ctx, "error in fetching weekday rewards", zap.Error(te))
			return nil
		}
		return nil
	})
	grp.Go(func() error {
		// weekdays rewards fetch
		weekendRewards, weekendRewardErr = s.rewardsClient.GetRewards(grpCtx, &rewardsPb.GetRewardsRequest{
			ExternalRefIds:  billIds,
			RewardOfferType: rewardsPb.RewardOfferType_SECURED_CREDIT_CARD_WEEKEND_OFFER,
		})
		if te := epifigrpc.RPCError(weekdayRewards, weekendRewardErr); te != nil {
			logger.Error(ctx, "error in fetching weekend rewards", zap.Error(te))
			return nil
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return nil, err
	}

	weekdayRewardsMap := getRewardMapFromRewardResponse(weekdayRewards, ffPkg.GetSecuredCardsWeekdayRewardsText())
	weekendRewardsMap := getRewardMapFromRewardResponse(weekendRewards, ffPkg.GetSecuredCardsWeekendRewardsText())
	ccTxnRewards := make([]*ffPb.AggregatedRewardsInfo, 0)
	for _, bill := range billResp {
		rewardList := make([]*ffPb.RewardsInfoWithHeaderIcon, 0)
		weekdayRewardInfo := weekdayRewardsMap[bill.GetId()]
		weekendRewardsInfo := weekendRewardsMap[bill.GetId()]
		if weekdayRewardInfo != nil {
			rewardList = append(rewardList, weekdayRewardInfo)
		}
		if weekendRewardsInfo != nil {
			rewardList = append(rewardList, weekendRewardsInfo)
		}
		if len(rewardList) == 0 {
			continue
		}
		stmtTime := bill.GetStatementDate().AsTime()
		ccTxnRewards = append(ccTxnRewards, &ffPb.AggregatedRewardsInfo{
			RewardsMultipliers: rewardList,
			BillGenTimestamp:   timestamppb.New(*datetime.AddNMonths(&stmtTime, -1)),
		})
	}

	return ccTxnRewards, nil
}

func getRewardMapFromRewardResponse(rewardsResp *rewardsPb.GetRewardsResponse, rewardType string) map[string]*ffPb.RewardsInfoWithHeaderIcon {
	rewardsMap := make(map[string]*ffPb.RewardsInfoWithHeaderIcon)
	for _, rewardInfo := range rewardsResp.GetRewards() {
		rewardCalculationEntries := rewardInfo.GetChosenReward().GetFiCoins()
		if rewardCalculationEntries == nil {
			continue
		}
		rewardUnits := int(rewardCalculationEntries.GetUnits())
		if rewardInfo.GetStatus() == rewardsPb.RewardStatus_CLAWED_BACK {
			rewardUnits = -1 * rewardUnits
		}
		rewardsMap[rewardInfo.GetRefId()] = &ffPb.RewardsInfoWithHeaderIcon{
			RewardsInfo: &deeplink.InfoItem{
				Icon:  internal.FiCoinSuccessIcon,
				Title: fmt.Sprintf("%d", int(rewardUnits)),
			},
			HeaderInfoItem: &deeplink.InfoItem{
				Icon:  ffPkg.GetSecuredCardsWeekdayRewardsDashboardIconUrl(),
				Title: rewardType,
			},
		}
	}
	return rewardsMap
}

func (s *Service) getForceUpdateScreenNextAction(requestHeader *header.RequestHeader, subText string) *deeplink.Deeplink {
	var nextAction *deeplink.Cta

	switch requestHeader.GetAuth().GetDevice().GetPlatform() {
	case commontypes.Platform_ANDROID:
		nextAction = &deeplink.Cta{
			Text:     "Back to home",
			Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_HOME},
		}
	case commontypes.Platform_IOS:
		nextAction = &deeplink.Cta{
			Text:     "Update Now",
			Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_UPDATE_APP_SCREEN},
		}
	default:
	}

	return ffHelper.GetForceUpdateScreen(
		internal.UfoIconUrl,
		"Update your Fi app to continue",
		subText,
		nextAction,
	)
}

func (s *Service) GetSecuredFixedDepositScreenDetails(ctx context.Context, req *ffPb.GetSecuredFixedDepositScreenDetailsRequest) (*ffPb.GetSecuredFixedDepositScreenDetailsResponse, error) {
	var (
		resp                                                                               = &ffPb.GetSecuredFixedDepositScreenDetailsResponse{}
		depositConfigRes                                                                   = &ffBePb.GetDepositConfigResponse{}
		depositInterestRatesResp                                                           = &depositPb.GetInterestRatesForActorResponse{}
		accountBalanceInfo                                                                 = &accountBalancePb.GetAccountBalanceResponse{}
		depositCalculationRes                                                              = &depositPb.CalculateMaturityInfoResponse{}
		depositConfigErr, depositInterestRateErr, accountBalanceErr, depositCalculationErr error
	)

	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*ffPb.GetSecuredFixedDepositScreenDetailsResponse, error) {
		resp.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return resp, nil
	}

	cardRequestResp, err := s.fireFlyClient.GetCardRequestByActorIdAndWorkflow(ctx, &ffBePb.GetCardRequestByActorIdAndWorkflowRequest{
		ActorId:             req.GetReq().GetAuth().GetActorId(),
		CardRequestWorkFlow: ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING,
	})
	if te := epifigrpc.RPCError(cardRequestResp, err); te != nil {
		logger.Error(ctx, "error fetching card request", zap.Error(te))
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}
	cardProgram := cardRequestResp.GetCardRequest().GetRequestDetails().GetCardProgram()
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		depositConfigRes, depositConfigErr = s.fireFlyClient.GetDepositConfig(grpCtx, &ffBePb.GetDepositConfigRequest{
			Vendor: types.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
		})

		if configErr := epifigrpc.RPCError(depositConfigRes, depositConfigErr); configErr != nil {
			logger.Error(grpCtx, "error in fetching config from lending: ", zap.Error(configErr))
			return configErr
		}
		if req.GetDepositTerm() == nil {
			req.DepositTerm = depositConfigRes.GetDepositTerm()
		}
		return nil
	})
	grp.Go(func() error {
		if ffPkg.IsFiLiteProgram(cardProgram) && ffPkg.IsCreditCardProgramSecured(cardProgram) {
			return nil
		}
		savingsAccInfo, savingsAccErr := s.savingsClient.GetAccount(grpCtx,
			&savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_ActorId{
				ActorId: req.GetReq().GetAuth().GetActorId(),
			}})
		if savingsAccErr != nil {
			logger.Error(grpCtx, pkgErr.Wrap(savingsAccErr, "error in fetching saving account details").Error())
			return savingsAccErr
		} else if savingsAccInfo.GetAccount() == nil {
			logger.Error(grpCtx, "No savings account record found")
			return epifierrors.ErrRecordNotFound
		}
		accountBalanceInfo, accountBalanceErr = s.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
			Identifier:    &accountBalancePb.GetAccountBalanceRequest_Id{Id: savingsAccInfo.GetAccount().GetId()},
			ActorId:       req.GetReq().GetAuth().GetActorId(),
			DataFreshness: accountBalEnumsPb.DataFreshness_DATA_FRESHNESS_NEAR_REAL_TIME,
		})
		if te := epifigrpc.RPCError(accountBalanceInfo, accountBalanceErr); te != nil {
			logger.Error(grpCtx, pkgErr.Wrap(te, "error in fetching account balance").Error())
			return nil
		}
		return nil
	})
	grp.Go(func() error {
		depositInterestRatesResp, depositInterestRateErr = s.depositClient.GetInterestRatesForActor(grpCtx, &depositPb.GetInterestRatesForActorRequest{
			Vendor:      commonvgpb.Vendor_FEDERAL_BANK,
			ActorId:     req.GetReq().GetAuth().GetActorId(),
			DepositType: accountPb.Type_FIXED_DEPOSIT,
		})
		if depositInterestErr := epifigrpc.RPCError(depositInterestRatesResp, depositInterestRateErr); depositInterestErr != nil {
			logger.Error(grpCtx, "error in fetching interest rates from deposits", zap.Error(depositInterestErr))
			return depositInterestErr
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in collecting details for secured cards deposit calculation", zap.Error(err))
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}

	if len(depositInterestRatesResp.GetInterestRates()) == 0 {
		logger.Error(ctx, "error while fetching deposit interest details from deposit client")
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}

	depositAmount := depositConfigRes.GetDefaultSliderAmountDetail().GetDepositAmount()
	creditLimit := ffPkg.GetCreditLimitFromDepositAmount(depositAmount, depositConfigRes.GetCreditMultiplier())
	defaultSliderAmountDetail := depositConfigRes.GetDefaultSliderAmountDetail()
	if !moneyPkg.IsZero(req.GetDepositAmount().GetBeMoney()) {
		depositAmount = req.GetDepositAmount().GetBeMoney()
		creditLimit = ffPkg.GetCreditLimitFromDepositAmount(depositAmount, depositConfigRes.GetCreditMultiplier())
		defaultSliderAmountDetail = &ffBePb.SliderAmountDetail{
			DepositAmount:     depositAmount,
			CreditLimit:       creditLimit,
			AmountSliderValue: int32(depositAmount.GetUnits()),
		}
	}
	if req.GetDepositTerm() == nil {
		req.DepositTerm = depositConfigRes.GetDepositTerm()
	}

	depositCalculationRes, depositCalculationErr = s.depositClient.CalculateMaturityInfo(ctx, &depositPb.CalculateMaturityInfoRequest{
		Vendor:       commonvgpb.Vendor_FEDERAL_BANK,
		CreationDate: datetime.TimeToDateInLoc(timestampPb.Now().AsTime(), datetime.IST),
		Term:         req.GetDepositTerm(),
		Amount:       depositAmount,
		ActorId:      req.GetReq().GetAuth().GetActorId(),
		DepositType:  accountPb.Type_FIXED_DEPOSIT,
		Scheme:       depositPb.DepositScheme_FD_CASH_CERTIFICATE,
	})
	if depositCalcErr := epifigrpc.RPCError(depositCalculationRes, depositCalculationErr); depositCalcErr != nil {
		logger.Error(ctx, "error in fetching calculations from deposits: ", zap.Error(depositCalcErr))
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}

	resp.ScreenBgColor = widget.GetBlockBackgroundColour(internal.DarkBlack)
	resp.ScreenHeader = helper.GetFdScreenHeaderDetails()
	resp.FdBottomSectionDetails = s.getFdBottomSectionDetails(ctx, defaultSliderAmountDetail,
		depositConfigRes, depositCalculationRes.GetInterestRate(), req.GetDepositTerm(), depositInterestRatesResp.GetInterestRates())
	resp.FdTopSectionDetails = helper.GetFdTopSectionDetails(creditLimit)
	if !ffPkg.IsFiLiteProgram(cardProgram) && ffPkg.IsCreditCardProgramSecured(cardProgram) {
		resp.CurrentAccountBalance = types.GetFromBeMoney(accountBalanceInfo.GetAvailableBalance())
	} else {
		resp.DisableBalanceCheck = true
		// todo(@saurabh) : remove post client changes.
		resp.CurrentAccountBalance = types.GetFromBeMoney(&money.Money{
			CurrencyCode: "INR",
			Units:        ********,
			Nanos:        0,
		})
	}
	resp.SelectedDepositTerm = req.GetDepositTerm()
	if s.dynamicConf.CreditCard().EnableFeedbackFormsForCCScreens() {
		resp.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusOk(),
			FeedbackEngineInfo: &header.FeedbackEngineInfo{
				FlowIdDetails: &header.FlowIdentifierDetails{
					FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
					FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FD_CREATION_SCREEN_SIMPLIFI.String(),
				},
			},
		}
	}
	return responseWithStatus(rpc.StatusOk(), nil)
}

func (s *Service) getFdBottomSectionDetails(ctx context.Context, selectedSliderAmount *ffBePb.SliderAmountDetail, depositConfigResp *ffBePb.GetDepositConfigResponse, interestRate float64, depositTerm *types.DepositTerm,
	depositInterestRates []*types.DepositInterestDetails) *ffPb.FdBottomSectionDetails {
	enableEditDeposit := s.dynamicConf.CreditCard().SimplifiOnboardingQuestConfig().EnableEditDeposit(ctx)
	colorStop := &widget.ColorStop{
		Color: internal.GreyBlack,
	}
	return &ffPb.FdBottomSectionDetails{
		BottomSectionBgProperties: ffPb.NewDrawableProperties().
			WithCornerProperty(
				ffPb.NewCornerProperty().WithTopEndCornerRadius(25).WithTopStartCornerRadius(25)).WithLinearGradientBackgroundColour(90, []*widget.ColorStop{
			colorStop, colorStop, colorStop, colorStop, colorStop, {
				Color: internal.DarkBlack,
			},
		}),
		FdDescriptionText:        commontypes.GetTextFromStringFontColourFontStyle(internal.FixedDepositWorthText, internal.JadeGreen, commontypes.FontStyle_SUBTITLE_M),
		DepositAmountDisplayText: commontypes.GetTextFromStringFontColourFontStyle(moneyPkg.ToDisplayStringInIndianFormat(selectedSliderAmount.GetDepositAmount(), 0, true), internal.White, commontypes.FontStyle_NUMBER_2XL),
		FdSliderInfo: ffPb.GetNewSliderInfo().
			WithSliderProperties(
				ffPb.GetNewSliderProperties().
					WithAttributes(internal.Ivory, internal.ForestGreen, internal.CharcoalBlack)).
			WithSliderElements(helper.GetSliderElements(depositConfigResp)).
			WithSelectedSliderElement(helper.GetSingleSliderElement(selectedSliderAmount)),
		FdDetails:        helper.GetFdDetails(interestRate, depositTerm, depositConfigResp, depositInterestRates, enableEditDeposit),
		OpenDepositCta:   helper.GetOpenDepositCta(),
		BalanceErrorText: commontypes.GetTextFromStringFontColourFontStyle(internal.InsufficientBalanceText, internal.Amber, commontypes.FontStyle_BODY_XS),
		AddFundsCta:      helper.GetAddFundsCta(),
		Tnc:              helper.GetTncTextWithHyperLink(),
		VeBankLogo:       helper.GetVisualElementWithImageUrl("https://epifi-icons.pointz.in/tiering/add_funds/federal_logo.png", 56, 14),
	}
}
