package firefly

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"

	"google.golang.org/protobuf/proto"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	beCasperPb "github.com/epifi/gamma/api/casper"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/firefly/helper"
	"github.com/epifi/gamma/frontend/firefly/internal"
	cardPkg "github.com/epifi/gamma/pkg/card"
)

type TotalRewardsEarningBanner struct {
	Title *ui.VerticalKeyValuePair
}

type RewardCard struct {
	BackgroundImage  *commontypes.VisualElement
	TopLeftTag       *ui.IconTextComponent
	CardIcon         *commontypes.VisualElement
	DescriptionLabel *commontypes.Text
	Deeplink         *deepLinkPb.Deeplink
}

type RewardHistoryItemInput struct {
	VisualElement      *commontypes.VisualElement
	VerticalKeyValue   *ui.VerticalKeyValuePair
	ActionIconTextComp *ui.IconTextComponent
}

type RewardsHistoryListInput struct {
	ShowActionButton bool
	Items            []*RewardHistoryItemInput
}

func getGenericOfferCard(offer *beCasperPb.Offer, bgColor string, _ *anyPb.Any, leftMargin int) *sections.DepthWiseListSection {

	var brandImg, backgroundImg string
	for _, img := range offer.GetImages() {
		if img.GetImageType() == beCasperPb.ImageType_BRAND_IMAGE && img.GetUrl() != "" && brandImg == "" {
			brandImg = img.GetUrl()
		}
		if img.GetImageType() == beCasperPb.ImageType_BACKGROUND_IMAGE && img.GetUrl() != "" && backgroundImg == "" {
			backgroundImg = img.GetUrl()
		}
	}

	title := offer.GetName()
	if offer.GetAdditionalDetails().GetHomeTitle() != "" {
		title = offer.GetAdditionalDetails().GetHomeTitle()
	}

	deeplink := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFER_DETAILS_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOfferDetailsScreenOptions{
			CardOfferDetailsScreenOptions: &deepLinkPb.CardOfferDetailsScreenOptions{OfferId: offer.GetId()},
		},
	}

	return &sections.DepthWiseListSection{
		IsScrollable:     false,
		VisualProperties: getGenericOfferCardVisualProps(leftMargin),
		Alignment:        sections.DepthWiseListSection_TOP_CENTER,
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
					OnClickBehavior: &behaviors.OnClickBehavior{
						Action: helper.GetAnyWithoutError(deeplink),
					},
				},
			},
		},
		Components: []*components.Component{
			{
				// Actual content without bottom shadow
				Content: helper.GetAnyWithoutError(&sections.VerticalListSection{
					IsScrollable:        false,
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
					Components: []*components.Component{
						{
							Content: helper.GetAnyWithoutError(&sections.DepthWiseListSection{
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: &properties.ContainerProperty{
												Size: &properties.Size{
													Width: &properties.Size_Dimension{
														Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
													},
													Height: &properties.Size_Dimension{
														Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
														ExactValue: 76,
													},
												},
												BgColor: widget.GetBlockBackgroundColour("#F0F3F7"),
												Margin: &properties.PaddingProperty{
													Left:  3,
													Top:   3,
													Right: 3,
												},
												Corner: &properties.CornerProperty{
													TopLeftCornerRadius:  16,
													TopRightCornerRadius: 16,
													BottomLeftCorner:     16,
													BottomRightCorner:    16,
												},
											},
										},
									},
								},
								Components: []*components.Component{
									{
										Content: helper.GetAnyWithoutError(&sections.HorizontalListSection{
											HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
											VisualProperties: []*properties.VisualProperty{
												{
													Properties: &properties.VisualProperty_ContainerProperty{
														ContainerProperty: &properties.ContainerProperty{
															Size: &properties.Size{
																Width: &properties.Size_Dimension{
																	Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
																},
																Height: &properties.Size_Dimension{
																	Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																	ExactValue: 76,
																},
															},
														},
													},
												},
											},
											Components: []*components.Component{
												{
													// Card top image
													Content: helper.GetAnyWithoutError(
														&commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
															Source:    &commontypes.VisualElement_Image_Url{Url: backgroundImg},
															ImageType: commontypes.ImageType_PNG,
															Properties: &commontypes.VisualElementProperties{
																Height: 76,
																Width:  94,
															},
														}}}),
												},
											},
										}),
									},
									{
										Content: helper.GetAnyWithoutError(&sections.VerticalListSection{
											VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
											VisualProperties: []*properties.VisualProperty{
												{
													Properties: &properties.VisualProperty_ContainerProperty{
														ContainerProperty: &properties.ContainerProperty{
															Size: &properties.Size{
																Width: &properties.Size_Dimension{
																	Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
																},
																Height: &properties.Size_Dimension{
																	Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
																},
															},
															Padding: &properties.PaddingProperty{
																Top:  8,
																Left: 16,
															},
														},
													},
												},
											},
											Components: []*components.Component{
												{
													Content: helper.GetAnyWithoutError(&sections.HorizontalListSection{
														Components: []*components.Component{cardPkg.GetVisualElementComponent(brandImg, 34, 34, commontypes.ImageType_PNG)},
														VisualProperties: []*properties.VisualProperty{
															{
																Properties: &properties.VisualProperty_ContainerProperty{
																	ContainerProperty: &properties.ContainerProperty{
																		Size: &properties.Size{
																			Width: &properties.Size_Dimension{
																				Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
																			},
																		},
																	},
																},
															},
														},
														VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
														HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
														ListElementOverlapProps: &properties.ListElementOverlapProps{
															OverlapDevicePixels:       12,
															OverlapPaddingPixels:      2,
															OverlapCornerRadiusPixels: 50,
															PaddingBgColor:            widget.GetBlockBackgroundColour(bgColor),
														},
													}),
												},
											},
										}),
									},
								},
							}),
						},
						{
							Content: helper.GetAnyWithoutError(&components.Spacer{
								SpacingValue: components.Spacing_SPACING_S,
							}),
						},
						{
							// Offer title
							Content: helper.GetAnyWithoutError(&sections.HorizontalListSection{
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: &properties.ContainerProperty{
												Padding: &properties.PaddingProperty{
													Left:  12,
													Right: 12,
												},
											},
										},
									},
								},
								Components: []*components.Component{
									{
										Content: helper.GetAnyWithoutError(&commontypes.Text{
											FontColor: colors.ColorInk,
											DisplayValue: &commontypes.Text_PlainString{
												PlainString: title,
											},
											FontStyle: &commontypes.Text_StandardFontStyle{
												StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
											},
											Alignment: commontypes.Text_ALIGNMENT_LEFT,
											MaxLines:  2,
										}),
									},
								},
							}),
						},
						{
							Content: helper.GetAnyWithoutError(&components.Spacer{}),
						},
					},
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
											ExactValue: offerWidgetCardWidth,
										},
										Height: &properties.Size_Dimension{
											Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
											ExactValue: offerWidgetCardHeight,
										},
									},
									BgColor: widget.GetBlockBackgroundColour(bgColor),
									Corner: &properties.CornerProperty{
										TopLeftCornerRadius:  20,
										TopRightCornerRadius: 20,
										BottomLeftCorner:     20,
										BottomRightCorner:    20,
									},
									Padding: &properties.PaddingProperty{
										Bottom: 16,
									},
									Margin: &properties.PaddingProperty{
										Bottom: 4,
									},
								},
							},
						},
					},
				}),
			},
		},
		LoadBehavior: &behaviors.LifecycleBehavior{
			Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
		},
		VisibleBehavior: &behaviors.LifecycleBehavior{
			Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
		},
	}
}

func getGenericOfferCardVisualProps(leftMargin int) []*properties.VisualProperty {
	return []*properties.VisualProperty{
		{
			Properties: &properties.VisualProperty_ContainerProperty{
				ContainerProperty: &properties.ContainerProperty{
					Size: &properties.Size{
						Width: &properties.Size_Dimension{
							Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
							ExactValue: offerWidgetCardWidth,
						},
						Height: &properties.Size_Dimension{
							Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
							ExactValue: offerWidgetCardHeight + offerWidgetCardShadowHeight,
						},
					},
					BgColor: widget.GetBlockBackgroundColour("#D9DEE3"),
					Corner: &properties.CornerProperty{
						TopLeftCornerRadius:  20,
						TopRightCornerRadius: 20,
						BottomLeftCorner:     20,
						BottomRightCorner:    20,
					},
					Margin: &properties.PaddingProperty{
						Left: int32(leftMargin),
					},
				},
			},
		},
	}
}

func getHorizontalSection(componentList []*components.Component, bgColor string) *sections.HorizontalListSection {
	return &sections.HorizontalListSection{
		Components:   componentList,
		IsScrollable: true,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_BgColor{
					BgColor: widget.GetBlockBackgroundColour(bgColor),
				},
			},
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
						},
					},
				},
			},
		},
	}
}

// Structure:
//
// DepthWiseListSection
//   - VerticalListSection
//   - DepthWiseListSection
//   - HorizontalListSection
//   - VisualElement (Background Image)
//   - VerticalListSection
//   - HorizontalListSection
//   - VisualElement (Brand Logo)
//   - VerticalListSection
//   - HorizontalListSection
//   - Tag
//   - HorizontalListSection
//   - Text (Title)
func getGenericFiCoinsExchangeOfferCard(offer *beCasperPb.Offer, bgColor string, tag *anyPb.Any, _ int) *fePb.GetRewardsAndOffersWidgetResponse_TabData_Card {
	var brandLogoUrl, bgImageUrl string
	for _, img := range offer.GetImages() {
		if img.GetImageType() == beCasperPb.ImageType_BRAND_IMAGE && img.GetUrl() != "" && brandLogoUrl == "" {
			brandLogoUrl = img.GetUrl()
		}
		if img.GetImageType() == beCasperPb.ImageType_BACKGROUND_IMAGE && img.GetUrl() != "" && bgImageUrl == "" {
			bgImageUrl = img.GetUrl()
		}
	}

	title := offer.GetName()
	if offer.GetAdditionalDetails().GetHomeTitle() != "" {
		title = offer.GetAdditionalDetails().GetHomeTitle()
	}

	deeplink := &deepLinkPb.Deeplink{
		Screen: deepLinkPb.Screen_CARD_OFFER_DETAILS_SCREEN,
		ScreenOptions: &deepLinkPb.Deeplink_CardOfferDetailsScreenOptions{
			CardOfferDetailsScreenOptions: &deepLinkPb.CardOfferDetailsScreenOptions{OfferId: offer.GetId()},
		},
	}

	return &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card{
		Card: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard_{
			GenericOfferCard: &fePb.GetRewardsAndOffersWidgetResponse_TabData_Card_GenericOfferCard{
				Section: &sections.Section{
					Content: &sections.Section_DepthWiseListSection{
						DepthWiseListSection: &sections.DepthWiseListSection{
							IsScrollable:     false,
							VisualProperties: nil,
							Alignment:        sections.DepthWiseListSection_TOP_CENTER,
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: getAnyWithoutError(deeplink),
										},
									},
									AnalyticsEvent: nil,
								},
							},
							Components: []*components.Component{
								{
									Content: getAnyWithoutError(&sections.VerticalListSection{
										IsScrollable:        false,
										HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
										VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: properties.GetContainerProperty().
														WithBlockBgColor(bgColor).
														WithAllCornerRadii(16, 16, 16, 16).
														WithPadding(4, 4, 4, 4).
														WithMargin(0, 0, 0, 4).
														WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardHeight).
														WithWidth(properties.Size_Dimension_DIMENSION_TYPE_EXACT, offerWidgetCardWidth),
												},
											},
										},
										Components: []*components.Component{
											{
												Content: getAnyWithoutError(&sections.DepthWiseListSection{
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithBlockBgColor(colors.ColorMonochromeChalk).
																	WithAllCornerRadii(14, 14, 14, 14).
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 80),
															},
														},
													},
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(&sections.HorizontalListSection{
																HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
																VisualProperties: []*properties.VisualProperty{
																	{
																		Properties: &properties.VisualProperty_ContainerProperty{
																			ContainerProperty: properties.GetContainerProperty().
																				WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																				WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 80),
																		},
																	}},
																Components: []*components.Component{
																	cardPkg.GetVisualElementComponent(bgImageUrl, 80, 98, commontypes.ImageType_PNG),
																},
															}),
														},
														{
															Content: getAnyWithoutError(&sections.VerticalListSection{
																VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
																VisualProperties: []*properties.VisualProperty{
																	{
																		Properties: &properties.VisualProperty_ContainerProperty{
																			ContainerProperty: properties.GetContainerProperty().
																				WithPadding(8, 8, 0, 0).
																				WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																				WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
																		},
																	},
																},
																Components: []*components.Component{
																	{
																		Content: getAnyWithoutError(&sections.HorizontalListSection{
																			Components: []*components.Component{cardPkg.GetVisualElementComponent(brandLogoUrl, 34, 34, commontypes.ImageType_PNG)},
																			VisualProperties: []*properties.VisualProperty{
																				{
																					Properties: &properties.VisualProperty_ContainerProperty{
																						ContainerProperty: properties.GetContainerProperty().
																							WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
																					},
																				},
																			},
																			VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
																			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
																		}),
																	},
																},
															}),
														},
														{
															Content: getAnyWithoutError(&sections.VerticalListSection{
																Components: []*components.Component{
																	{
																		Content: getAnyWithoutError(&sections.HorizontalListSection{
																			Components: []*components.Component{
																				{
																					Content: tag,
																				},
																			},
																			VisualProperties: []*properties.VisualProperty{
																				{
																					Properties: &properties.VisualProperty_ContainerProperty{
																						ContainerProperty: properties.GetContainerProperty().
																							WithPadding(0, 4, 4, 0).
																							WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																							WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0),
																					},
																				},
																			},
																			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
																		}),
																	},
																},
																VisualProperties: []*properties.VisualProperty{
																	{
																		Properties: &properties.VisualProperty_ContainerProperty{
																			ContainerProperty: properties.GetContainerProperty().
																				WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																				WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
																		},
																	},
																},
																VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
															}),
														},
													},
												}),
											},
											{
												Content: getAnyWithoutError(&sections.HorizontalListSection{
													IsScrollable: false,
													Components: []*components.Component{
														{
															Content: getAnyWithoutError(&commontypes.Text{
																FontColor: "#282828",
																DisplayValue: &commontypes.Text_PlainString{
																	PlainString: title,
																},
																FontStyle: &commontypes.Text_StandardFontStyle{
																	StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
																},
																Alignment: commontypes.Text_ALIGNMENT_LEFT,
																MaxLines:  2,
															}),
														},
													},
													VisualProperties: []*properties.VisualProperty{
														{
															Properties: &properties.VisualProperty_ContainerProperty{
																ContainerProperty: properties.GetContainerProperty().
																	WithPadding(8, 8, 8, 0).
																	WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
																	WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
															},
														},
													},
													VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
													HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
												}),
											},
										},
									},
									),
								},
							},
							LoadBehavior: &behaviors.LifecycleBehavior{
								Behavior:       &behaviors.LifecycleBehavior_LoadBehavior{},
								AnalyticsEvent: nil,
							},
							VisibleBehavior: &behaviors.LifecycleBehavior{
								Behavior:       &behaviors.LifecycleBehavior_ViewedBehavior{},
								AnalyticsEvent: nil,
							},
						},
					},
				},
			},
		},
	}
}

func getSduiViewAllOffersCard() *sections.Section {
	return &sections.Section{
		Content: &sections.Section_DepthWiseListSection{
			DepthWiseListSection: &sections.DepthWiseListSection{
				IsScrollable:     false,
				VisualProperties: getGenericOfferCardVisualProps(12),
				Alignment:        sections.DepthWiseListSection_TOP_CENTER,
				InteractionBehaviors: []*behaviors.InteractionBehavior{
					{
						Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
							OnClickBehavior: &behaviors.OnClickBehavior{
								Action: getAnyWithoutError(&deepLinkPb.Deeplink{
									Screen: deepLinkPb.Screen_OFFERS_LANDING_SCREEN,
								}),
							},
						},
						AnalyticsEvent: nil,
					},
				},
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(&sections.VerticalListSection{
							IsScrollable:        false,
							HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
							VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
							Components: []*components.Component{
								{
									Content: getAnyWithoutError(&sections.HorizontalListSection{
										IsScrollable: false,
										Components: []*components.Component{
											{
												Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/home/<USER>", 104, 116)),
											},
										},
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: &properties.ContainerProperty{
														Size: &properties.Size{
															Width: &properties.Size_Dimension{
																Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																ExactValue: offerWidgetCardWidth,
															},
															Height: &properties.Size_Dimension{
																Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																ExactValue: 104,
															},
														},
													},
												},
											},
										},
										VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
										HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
									}),
								},
								{
									Content: getAnyWithoutError(&sections.HorizontalListSection{
										IsScrollable: false,
										Components: []*components.Component{
											{
												Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("View all", "#6A6D70", commontypes.FontStyle_SUBTITLE_S)),
											},
											{
												Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/rewards/chevron-right-lead.png", 16, 16)),
											},
										},
										VisualProperties: []*properties.VisualProperty{
											{
												Properties: &properties.VisualProperty_ContainerProperty{
													ContainerProperty: &properties.ContainerProperty{
														Size: &properties.Size{
															Width: &properties.Size_Dimension{
																Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																ExactValue: offerWidgetCardWidth,
															},
															Height: &properties.Size_Dimension{
																Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
															},
														},
														Padding: &properties.PaddingProperty{
															Left: 15,
														},
														Margin: &properties.PaddingProperty{
															Top: 12,
														},
														Position: nil,
													},
												},
											},
										},
										VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
										HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
									}),
								},
							},
							VisualProperties: []*properties.VisualProperty{
								{
									Properties: &properties.VisualProperty_ContainerProperty{
										ContainerProperty: &properties.ContainerProperty{
											Size: &properties.Size{
												Width: &properties.Size_Dimension{
													Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
													ExactValue: offerWidgetCardWidth,
												},
												Height: &properties.Size_Dimension{
													Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
													ExactValue: 152,
												},
											},
											BgColor: widget.GetBlockBackgroundColour(colors.ColorSnow),
											Corner: &properties.CornerProperty{
												TopLeftCornerRadius:  20,
												TopRightCornerRadius: 20,
												BottomLeftCorner:     20,
												BottomRightCorner:    20,
											},
											Margin: &properties.PaddingProperty{
												Bottom: 4,
											},
										},
									},
								},
							},
						},
						),
					},
				},
			},
		},
	}
}

// getTitleWithViewAllComponent creates a horizontal list section with title and view all cta on the right
func getTitleWithViewAllComponent(title, titleColor, viewAllText, viewAllColor string, viewAllDl *deepLinkPb.Deeplink) *sections.HorizontalListSection {
	return &sections.HorizontalListSection{
		Components: []*components.Component{
			{
				Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(title, titleColor, commontypes.FontStyle_DISPLAY_M)),
			},
			{
				Content: getAnyWithoutError(
					ui.NewITC().
						WithTexts(commontypes.GetTextFromStringFontColourFontStyle(
							viewAllText,
							viewAllColor,
							commontypes.FontStyle_OVERLINE_XS_CAPS,
						)).
						WithRightImageUrlHeightAndWidth(
							internal.DashboardV2RightArrowIconUrl, 24, 24,
						)),
				InteractionBehaviors: []*behaviors.InteractionBehavior{
					{
						Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
							OnClickBehavior: &behaviors.OnClickBehavior{
								Action: getAnyWithoutError(viewAllDl),
							},
						},
					},
				},
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: 32,
							},
						},
						Padding: &properties.PaddingProperty{
							Left:  16,
							Right: 16,
						},
					},
				},
			},
		},
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
	}
}

func getGenericOffersSection(
	title string,
	titleColor string,
	viewAllText string,
	viewAllColor string,
	offersTabData []*components.Component,
	viewAllDl *deepLinkPb.Deeplink,
) *sections.Section {
	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				Components: []*components.Component{
					{
						Content: getAnyWithoutError(getTitleWithViewAllComponent(title, titleColor, viewAllText, viewAllColor, viewAllDl)),
					},
					{
						Content: getAnyWithoutError(&components.Spacer{
							SpacingValue: components.Spacing_SPACING_M,
						}),
					},
					{
						Content: getAnyWithoutError(&sections.HorizontalListSection{
							Components:   offersTabData,
							IsScrollable: true,
							VisualProperties: []*properties.VisualProperty{
								{
									Properties: &properties.VisualProperty_ContainerProperty{
										ContainerProperty: &properties.ContainerProperty{
											Size: &properties.Size{
												Width: &properties.Size_Dimension{
													Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
												},
												Height: &properties.Size_Dimension{
													Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
												},
											},
										},
									},
								},
							},
						}),
					},
				},
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Height: &properties.Size_Dimension{
										Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
									},
								},
								Padding: &properties.PaddingProperty{
									Top:    12,
									Bottom: 12,
								},
							},
						},
					},
				},
			},
		},
	}
}

func getAnyWithoutError(msg proto.Message) *anyPb.Any {
	res, _ := anyPb.New(msg)
	return res
}
