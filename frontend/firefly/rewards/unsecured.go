// nolint
package rewards

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strconv"
	"time"

	pkgErr "github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	typesUiWidget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	types "github.com/epifi/gamma/api/typesv2"

	"github.com/epifi/gamma/api/casper"
	ffPinotPb "github.com/epifi/gamma/api/firefly/pinot"
	"github.com/epifi/gamma/api/frontend/deeplink"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	"github.com/epifi/gamma/api/frontend/header"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	rewardsProjectionPb "github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/firefly/helper"
	feHelper "github.com/epifi/gamma/frontend/firefly/helper"
	"github.com/epifi/gamma/frontend/firefly/internal"
	"github.com/epifi/gamma/pkg/feature/release"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
)

const (
	// spend limit to get 5X Rewards Coins booster
	fiveXBoosterSpendLimit       = 15000
	fiveXBoosterSpendLimitString = "15,000"

	// These are the different states a user can be in based on spends
	steadyStateIdentifier          = "SteadyState"
	fiveXBoosterUnlockedIdentifier = "5XBoosterUnlocked"
	fiveXBoosterLockedIdentifier   = "5XBoosterLocked"

	rewardsMultiplier = 5
)

type UnsecuredRewardsProvider struct {
	rewardsProjectionClient rewardsProjectionPb.ProjectorServiceClient
	genConf                 *genconf.Config
	pinotClient             ffPinotPb.TxnAggregatesClient
	releaseEvaluator        release.IEvaluator
}

func NewUnsecuredRewards(
	genConf *genconf.Config,
	rewardsProjectionClient rewardsProjectionPb.ProjectorServiceClient,
	pinotClient ffPinotPb.TxnAggregatesClient, releaseEvaluator release.IEvaluator) *UnsecuredRewardsProvider {
	return &UnsecuredRewardsProvider{
		rewardsProjectionClient: rewardsProjectionClient,
		genConf:                 genConf,
		releaseEvaluator:        releaseEvaluator,
		pinotClient:             pinotClient,
	}
}

// nolint:funlen
func (u *UnsecuredRewardsProvider) GetDashboardRewardView(ctx context.Context, dashboardRewardViewRequest *GetDashboardRewardViewRequest) (*GetDashboardRewardViewResponse, error) {
	var (
		rewardsSpends1x                        = &rewardsProjectionPb.GetProjectionAggregatesResponse{}
		rewardsSpends5x                        = &rewardsProjectionPb.GetProjectionAggregatesResponse{}
		rewardsSpends1xErr, rewardsSpends5xErr error
		rewardsSpends1xUnits                   = float32(0.0)
		rewardsSpends5xUnits                   = float32(0.0)
		dashboardRewardViewResponse            *GetDashboardRewardViewResponse
		cardDesignEnhancementEnabled           = dashboardRewardViewRequest.CardDesignEnhancementEnabled
	)
	// Check if the unsecured rewards dashboard feature is enabled
	if !helper.CheckFeatureReleaseConstraints(ctx, types.Feature_FEATURE_CC_REWARD_DASHBOARD_UNSECURED_CARD, dashboardRewardViewRequest.ActorId, u.releaseEvaluator) {
		return nil, nil
	}

	grp, grpCtx := errgroup.WithContext(ctx)
	// This will return the rewards on all brands: both top 20 & others
	grp.Go(func() error {
		rewardsSpends1x, rewardsSpends1xErr = u.rewardsProjectionClient.GetProjectionAggregates(grpCtx, &rewardsProjectionPb.GetProjectionAggregatesRequest{
			ActorId: dashboardRewardViewRequest.ActorId,
			Filters: &rewardsProjectionPb.GetProjectionAggregatesRequest_Filters{
				AccountId: dashboardRewardViewRequest.CreditCard.GetAccountId(),
				OfferTypes: []rewardsPb.RewardOfferType{
					rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
					rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_BASE_OFFER,
					rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_BASE_OFFER,
				},
				TimeWindows: []*rewardsPb.TimeWindow{
					{
						FromTime: dashboardRewardViewRequest.StartTimestamp,
						TillTime: dashboardRewardViewRequest.EndTimestamp,
					},
				},
			},
		})
		if te := epifigrpc.RPCError(rewardsSpends1x, rewardsSpends1xErr); te != nil {
			return pkgErr.Wrap(te, "error fetching 1x projections rewards info")
		}
		return nil
	})
	// This will return 5x coins regardless of user spend
	grp.Go(func() error {
		rewardsSpends5x, rewardsSpends5xErr = u.rewardsProjectionClient.GetProjectionAggregates(grpCtx, &rewardsProjectionPb.GetProjectionAggregatesRequest{
			ActorId: dashboardRewardViewRequest.ActorId,
			Filters: &rewardsProjectionPb.GetProjectionAggregatesRequest_Filters{
				AccountId: dashboardRewardViewRequest.CreditCard.GetAccountId(),
				OfferTypes: []rewardsPb.RewardOfferType{
					rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER,
					rewardsPb.RewardOfferType_UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_ACCELERATED_OFFER,
				},
				TimeWindows: []*rewardsPb.TimeWindow{
					{
						FromTime: dashboardRewardViewRequest.StartTimestamp,
						TillTime: dashboardRewardViewRequest.EndTimestamp,
					},
				},
			},
		})
		if te := epifigrpc.RPCError(rewardsSpends5x, rewardsSpends5xErr); te != nil {
			return pkgErr.Wrap(te, "error fetching top merchant spends info")
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return nil, err
	}

	for _, rewardsSpend1x := range rewardsSpends1x.GetAggregates() {
		if rewardsSpend1x.GetRewardType() == rewardsPb.RewardType_FI_COINS {
			rewardsSpends1xUnits += rewardsSpend1x.GetProjectedRewardUnits()
		}
	}

	for _, rewardsSpend5x := range rewardsSpends5x.GetAggregates() {
		if rewardsSpend5x.GetRewardType() == rewardsPb.RewardType_FI_COINS {
			rewardsSpends5xUnits += rewardsSpend5x.GetProjectedRewardUnits()
		}
	}
	rewardCoinsOnOtherBrands := rewardsSpends1xUnits - (rewardsSpends5xUnits / rewardsMultiplier)
	rewardCoinsOnSpecialBrands := rewardsSpends5xUnits / rewardsMultiplier

	if rewardCoinsOnOtherBrands < 0 {
		rewardCoinsOnOtherBrands = 0
	}

	if rewardCoinsOnSpecialBrands < 0 {
		rewardCoinsOnSpecialBrands = 0
	}
	billingCycleDeepLink := &deeplink.Cta{
		Text: "Monthly Rewards",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_STORY_SCREEN,
			ScreenOptions: &deeplink.Deeplink_StoryScreenOptions{
				StoryScreenOptions: &deeplink.StoryScreenOptions{
					StoryTitle: "How it works",
					StoryUrl:   "https://stories.fi.money/stories/credit-card-rewards",
				},
			},
		},
	}

	billingCycleInfo := feHelper.GetBillingCycleInfo(dashboardRewardViewRequest.StartDate, dashboardRewardViewRequest.EndDate, billingCycleDeepLink)

	// Rewards Dashboard content for section
	var rewardDetailsResponseSection *sections.Section
	billingCycleInfoCCRewardDashboardWidget, rewardsAndOfferData, rewardCoinSummaryData, boosterRewardsData, displayRewardsDashboardError := u.fetchCCRewardsSectionContent(ctx, billingCycleDeepLink, dashboardRewardViewRequest, rewardCoinsOnOtherBrands, rewardCoinsOnSpecialBrands)
	switch {
	case displayRewardsDashboardError != nil:
		// not returning as this will just not display the rewards dashboard
		logger.Error(ctx, "Error in fetching rewards dashboard content", zap.Error(displayRewardsDashboardError), zap.String(logger.ACTOR_ID_V2, dashboardRewardViewRequest.ActorId))
	default:
		rewardDetailsResponseSection = feHelper.GetCCRewardDashboardWidget(billingCycleInfoCCRewardDashboardWidget, rewardsAndOfferData, rewardCoinSummaryData, boosterRewardsData, cardDesignEnhancementEnabled)
	}

	dashboardRewardViewResponse = &GetDashboardRewardViewResponse{
		GetRewardDetailsResponse: &ffPb.GetRewardDetailsResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			BillingCycleInfo: billingCycleInfo,
			DashboardRewardsInformation: &ffPb.DashboardRewardsInformation{
				BillingCycleInfo: billingCycleInfo,
				RewardsConstructInformation: []*ffPb.RewardsConstructInformation{
					{
						RewardsInformationCategory: &ffPb.RewardsConstructInformation_RewardsInformationCategoryBody{
							RewardsInformationCategoryBody: &ffPb.RewardsInformationCategoryBody{
								PrimaryVisualElement: commontypes.GetVisualElementImageFromUrl(internal.UnsecuredAcceleratedRewardsDashboardIconUrl).
									WithImageType(commontypes.ImageType_PNG).
									WithProperties(&commontypes.VisualElementProperties{Width: 48, Height: 48}),
								SecondaryVisualElement: commontypes.GetVisualElementImageFromUrl(internal.FiProcessingRewardsIconUrl).
									WithImageType(commontypes.ImageType_PNG).
									WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28}),
								PrimaryText: &commontypes.Text{
									DisplayValue: &commontypes.Text_Html{
										Html: "5X REWARDS <font color='#8D8D8D'>(ON FI COLLECTION)</font>",
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS,
									},
								},
								SecondaryText: &commontypes.Text{
									FontColor: "#8D8D8D",
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: fmt.Sprintf("%d", int(rewardsSpends5xUnits)),
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_NUMBER_XL,
									},
								},
								BgColor: &typesUiWidget.BackgroundColour{
									Colour: &typesUiWidget.BackgroundColour_BlockColour{
										BlockColour: "#FFFFFF",
									},
								},
							},
						},
					},
					{
						RewardsInformationCategory: &ffPb.RewardsConstructInformation_RewardsInformationCategoryBody{
							RewardsInformationCategoryBody: &ffPb.RewardsInformationCategoryBody{
								PrimaryVisualElement: commontypes.GetVisualElementImageFromUrl(internal.UnsecuredBaseRewardsDashboardIconUrl).
									WithImageType(commontypes.ImageType_PNG).
									WithProperties(&commontypes.VisualElementProperties{Width: 48, Height: 48}),
								SecondaryVisualElement: commontypes.GetVisualElementImageFromUrl(internal.FiProcessingRewardsIconUrl).
									WithImageType(commontypes.ImageType_PNG).
									WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28}),
								PrimaryText: &commontypes.Text{
									DisplayValue: &commontypes.Text_Html{
										Html: "1X REWARDS <font color='#8D8D8D'>(ON ALL SPENDS)</font>",
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS,
									},
								},
								SecondaryText: &commontypes.Text{
									FontColor: "#8D8D8D",
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: fmt.Sprintf("%d", int(rewardsSpends1xUnits)),
									},
									FontStyle: &commontypes.Text_StandardFontStyle{
										StandardFontStyle: commontypes.FontStyle_NUMBER_XL,
									},
								},
								BgColor: &typesUiWidget.BackgroundColour{
									Colour: &typesUiWidget.BackgroundColour_BlockColour{
										BlockColour: "#FFFFFF",
									},
								},
							},
						},
					},
					{
						RewardsInformationCategory: &ffPb.RewardsConstructInformation_RewardsInformationCategoryFooter{
							RewardsInformationCategoryFooter: &ffPb.RewardsInformationCategoryFooter{
								FooterText: &commontypes.Text{
									FontColor:    "#646464",
									DisplayValue: &commontypes.Text_PlainString{PlainString: "Fi-Coins get credited 10 days after bill generation"},
									FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
								},
								BgColor: &typesUiWidget.BackgroundColour{
									Colour: &typesUiWidget.BackgroundColour_BlockColour{
										BlockColour: "#F7F9FA",
									},
								},
							},
						},
					},
				},
			},
			Section: rewardDetailsResponseSection,
		},
	}
	return dashboardRewardViewResponse, nil
}

// This function is used to construct all the Rewards Dashboard content based on the user's spends
func (u *UnsecuredRewardsProvider) fetchCCRewardsSectionContent(ctx context.Context, billingCycleDeepLink *deeplink.Cta, req *GetDashboardRewardViewRequest, rewardCoinsOnOtherBrands, rewardCoinsOnSpecialBrands float32) (*feHelper.BillingCycleInfo, []*feHelper.RewardsAndOffersData, *feHelper.RewardCoinSummaryData, *feHelper.BoosterRewardsData, error) {
	var (
		billingCycleInfoCCRewardDashboardWidget *feHelper.BillingCycleInfo
		rewardsAndOfferData                     []*feHelper.RewardsAndOffersData
		rewardCoinSummaryData                   *feHelper.RewardCoinSummaryData
		boosterRewardsData                      *feHelper.BoosterRewardsData
	)

	// Get user spends
	getEffectiveUserSpendsByOntologyIdsRes, getEffectiveUserSpendsByOntologyIdsErr := ffPkg.GetEffectiveUserSpendsByOntologyIds(ctx, req.ActorId, req.StartTimestamp, req.EndTimestamp, ffPkg.GetOntologyIdsToExcludeWhileCalculatingAggregateSpends(), u.pinotClient)
	if getEffectiveUserSpendsByOntologyIdsErr != nil {
		return nil, nil, nil, nil, getEffectiveUserSpendsByOntologyIdsErr
	}
	userSpend := getEffectiveUserSpendsByOntologyIdsRes.GetPb().GetUnits()
	logger.Info(ctx, fmt.Sprintf("Amplifi rewards dashboard: Is user spend >= 15k: %t", userSpend >= fiveXBoosterSpendLimit), zap.String(logger.ACTOR_ID_V2, req.ActorId))
	// Get state: The rewards screens are divided into the following stated based on the user's spends:
	// Steady State/ Zero State: When the user has spent <15k, and it is not the last day of the billing cycle (https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-52008&t=QzN0sKHEBPECAtYb-4, https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-53222&t=QzN0sKHEBPECAtYb-4)
	// 5X booster locked: User has spent <15k & it is the last day of the billing cycle (https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-53912&t=QzN0sKHEBPECAtYb-4)
	// 5X booster unlocked: User has spent >15k (https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-52541&t=QzN0sKHEBPECAtYb-4)
	// The below "state" names should match the names in frontend yaml UnsecuredCCRewardsStateSectionMap parameter
	state := steadyStateIdentifier
	if userSpend >= fiveXBoosterSpendLimit && rewardCoinsOnSpecialBrands > 0 {
		// 5X booster unlocked condition
		state = fiveXBoosterUnlockedIdentifier
	} else if userSpend < fiveXBoosterSpendLimit && time.Now().After(req.EndTimestamp.AsTime().Add(-24*time.Hour)) && time.Now().Before(req.EndTimestamp.AsTime()) {
		// 5X booster locked condition, else we leave it to steady state
		state = fiveXBoosterLockedIdentifier
	}

	// fetch content from config
	unsecuredCCRewardsStateSectionMap := u.genConf.CreditCard().UnsecuredCCRewardsStateSectionMap().Get(state)
	// Billing cycle information (https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-53749&t=QzN0sKHEBPECAtYb-4)
	billingCycleInfoCCRewardDashboardWidget = &feHelper.BillingCycleInfo{
		Title:     billingCycleDeepLink.Text,
		StartDate: req.StartDate,
		EndDate:   req.EndDate,
	}
	// Rewards and offers
	rewardsAndOfferData = u.fetchRewardsAndOfferData(unsecuredCCRewardsStateSectionMap)
	// Reward coins summary
	rewardCoinSummaryData = u.fetchRewardCoinSummaryData(unsecuredCCRewardsStateSectionMap, rewardCoinsOnOtherBrands)
	// Booster Rewards
	boosterRewardsData = u.fetchBoosterRewardsData(ctx, unsecuredCCRewardsStateSectionMap, rewardCoinsOnSpecialBrands, state, userSpend)
	return billingCycleInfoCCRewardDashboardWidget, rewardsAndOfferData, rewardCoinSummaryData, boosterRewardsData, nil
}

// Rewards and offers (https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43354-19770&t=QzN0sKHEBPECAtYb-4)
// Currently there are 3 elements in this section. If editing, make sure the deeplinks are in order
func (u *UnsecuredRewardsProvider) fetchRewardsAndOfferData(unsecuredCCRewardsStateSectionMap *genconf.UnsecuredCCRewards) []*feHelper.RewardsAndOffersData {
	var rewardsAndOfferData []*feHelper.RewardsAndOffersData
	// tag filters for respective deeplinks
	// display first offer IDs for respective deeplinks
	tagFilters := [][]string{[]string{casper.CategoryTag_CATEGORY_TAG_VOUCHERS.String()}, []string{casper.TagName_FLIGHT.String()}, []string{}}
	displayFirstOfferIds := [][]string{[]string{}, []string{}, []string{u.genConf.CreditCard().BillEraserOfferIDForOffersCatalogue()}}
	for index, _ := range unsecuredCCRewardsStateSectionMap.RewardsAndOffers() {
		rewardsAndOfferData = append(rewardsAndOfferData, &feHelper.RewardsAndOffersData{
			ImageUrl: unsecuredCCRewardsStateSectionMap.RewardsAndOffers()[index].ImageUrl,
			Title:    unsecuredCCRewardsStateSectionMap.RewardsAndOffers()[index].Title,
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_OFFERS_LANDING_SCREEN,
				ScreenOptions: &deeplink.Deeplink_OfferCatalogScreenOptions{
					OfferCatalogScreenOptions: &deeplink.OfferCatalogScreenOptions{
						TagFilters:           tagFilters[index],
						DisplayFirstOfferIds: displayFirstOfferIds[index],
					},
				},
			},
		})
	}
	return rewardsAndOfferData
}

// Reward coins summary (https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-53817&t=QzN0sKHEBPECAtYb-4)
// This section contains 1x coins earned, i.e. offers on other brands
func (u *UnsecuredRewardsProvider) fetchRewardCoinSummaryData(unsecuredCCRewardsStateSectionMap *genconf.UnsecuredCCRewards, rewardCoinsOnOtherBrands float32) *feHelper.RewardCoinSummaryData {
	return &feHelper.RewardCoinSummaryData{
		Title:       unsecuredCCRewardsStateSectionMap.RewardCoinSummaryData().Title(),
		CoinsEarned: strconv.Itoa(int(rewardCoinsOnOtherBrands)),
		InfoDeeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_INFORMATION_POPUP,
			ScreenOptions: &deeplink.Deeplink_InformationPopupOptions{
				InformationPopupOptions: &deeplink.InformationPopupOptions{
					InfoList: []*deeplink.InformationPopupOptions_Info{
						{
							TextTitle: commontypes.GetTextFromHtmlStringFontColourFontStyle("Rewards on other brands", colors.ColorNight, commontypes.FontStyle_SUBTITLE_L),
							BodyTexts: []*commontypes.Text{
								commontypes.GetTextFromHtmlStringFontColourFontStyle("• Get 20 Fi-Coins for every ₹100 spent.", colors.ColorSlate, commontypes.FontStyle_BODY_S),
								commontypes.GetTextFromHtmlStringFontColourFontStyle("• Fi-Coins get credited 10 days after the card bill generation date.", colors.ColorSlate, commontypes.FontStyle_BODY_S),
								commontypes.GetTextFromHtmlStringFontColourFontStyle("• Wallet reloads, cash advances, fees & charges or repayments and any other credit transactions are not eligible for any form of rewards.", colors.ColorSlate, commontypes.FontStyle_BODY_S),
							},
						},
					},
					Icon:    commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/credit_card_images/rewards_dashboard_simplifi/info_icon_green.png", 48, 48),
					BgColor: colors.ColorSnow,
				},
			},
		},
	}
}

// Booster Rewards (https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-53762&t=QzN0sKHEBPECAtYb-4)
// This section contains 5x coins earned, i.e. offers on top brands
func (u *UnsecuredRewardsProvider) fetchBoosterRewardsData(ctx context.Context, unsecuredCCRewardsStateSectionMap *genconf.UnsecuredCCRewards, rewardCoinsOnSpecialBrands float32, state string, userSpend int64) *feHelper.BoosterRewardsData {
	rewardInfoText := unsecuredCCRewardsStateSectionMap.BoosterRewardsData().RewardInfoText()
	switch state {
	case steadyStateIdentifier:
		rewardInfoText = fmt.Sprintf("Convert to <span style=\"color:%s;\">%d Fi-Coins</span> when you spend <span style=\"color:%s;\">₹%s</span>", colors.ColorMoss700, int(rewardCoinsOnSpecialBrands*rewardsMultiplier), colors.ColorMoss700, fiveXBoosterSpendLimitString)
		// If the user has earned 0 5x Coins, then we display a different message (https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-50168&t=SXCO0LsYmoPgyBdR-4)
		if rewardCoinsOnSpecialBrands == 0 {
			rewardInfoText = fmt.Sprintf("<span style=\"color:%s;\">%dX Fi-Coins</span> on monthly spends above ₹%s", colors.ColorMoss700, rewardsMultiplier, fiveXBoosterSpendLimitString)
		}
	case fiveXBoosterLockedIdentifier:
		rewardInfoText = fmt.Sprintf("<span style=\"color:#D48647;\">%d Fi-Coins</span> missed this month 😔", int((rewardsMultiplier-1)*rewardCoinsOnSpecialBrands))
	}

	ccIntroScreenOptionsV2 := u.genConf.CreditCard().CCIntroScreenOptionsV2()
	wrappedButtonGenConf := ccIntroScreenOptionsV2.TemplatesMap().Get("template1").CardIntroWidgetsMap().Get("widget1").WrappedBtnInfo()
	wrappedButtonInfo := feHelper.GetWrappedButtonInfo(ctx, wrappedButtonGenConf)

	if state == fiveXBoosterUnlockedIdentifier {
		rewardCoinsOnSpecialBrands = rewardCoinsOnSpecialBrands * rewardsMultiplier
	}

	return &feHelper.BoosterRewardsData{
		Heading:                  unsecuredCCRewardsStateSectionMap.BoosterRewardsData().Heading(),
		RewardInfoText:           rewardInfoText,
		BoosterRewardCoinsEarned: strconv.Itoa(int(rewardCoinsOnSpecialBrands)),
		LoaderPercentage:         int32(userSpend * 100 / fiveXBoosterSpendLimit),
		IsRewardLocked:           state == fiveXBoosterLockedIdentifier,
		FooterData: &feHelper.BoosterRewardsFooterData{
			BrandsStackImageUrl: unsecuredCCRewardsStateSectionMap.BoosterRewardsData().FooterData().BrandsStackImageUrl(),
			CtaTitle:            unsecuredCCRewardsStateSectionMap.BoosterRewardsData().FooterData().CtaTitle(),
			Deeplink:            wrappedButtonInfo.GetDeeplink(),
		},
		IsAmplifiCard: true,
	}
}

func (u *UnsecuredRewardsProvider) GetIntroScreenRewardView(ctx context.Context, _ *GetIntroScreenRewardViewRequest) (*GetIntroScreenRewardViewResponse, error) {
	var (
		ccIntroScreenOptions *genconf.CCIntroScreenOptions
	)

	if u.genConf.CreditCard().EnableNewCvpForUnsecuredCreditCard() {
		ccIntroScreenOptions = u.genConf.CreditCard().CCIntroScreenOptionsV2()
	} else {
		ccIntroScreenOptions = u.genConf.CreditCard().CCIntroScreenOptions()
	}
	templates, err := helper.GetTemplatesForType(ctx, ccIntroScreenOptions,
		ffPb.IntroScreenTemplateType_INTRO_SCREEN_TEMPLATE_TYPE_REWARDS, false)
	if err != nil {
		return nil, err
	}
	return &GetIntroScreenRewardViewResponse{RewardsTemplates: templates}, nil
}
