// nolint
package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"

	"google.golang.org/genproto/googleapis/type/date"

	typesUiWidget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/firefly/internal"
)

// RewardsAndOffersData figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43354-19770&t=AgDy9aNDY5KKW8zp-4
// represent the reward and offer data that use to spend fi coins.
type RewardsAndOffersData struct {
	// rewards image url
	ImageUrl string

	// Name of the reward or offer like:"Gift cards", "Airmiles" or "Bill eraser"
	Title string

	// Deeplink of the respective offer screen, when user click on rewards
	Deeplink *deeplink.Deeplink
}

// RewardCoinSummaryData represents the data for a widget that displays either
// the total Fi coin earned or the total rewards on other brands.
// figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-53817&t=AgDy9aNDY5KKW8zp-4
type RewardCoinSummaryData struct {
	// Title is the text displayed as the widget's title,
	// either "REWARDS ON OTHER BRANDS" or "TOTAL Fi-COINS".
	Title string

	// CoinsEarned is the amount of coin earned.
	CoinsEarned string

	// InfoDeeplink, if not nil, will cause an info icon to be displayed.
	// Clicking the icon will open an info dialog corresponding to InfoDeeplink
	// If InfoDeeplink is nil, the info icon will not be shown.
	// figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43394-37898&t=AgDy9aNDY5KKW8zp-4
	InfoDeeplink *deeplink.Deeplink
}

// BillingCycleInfo figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43394-37884&t=AgDy9aNDY5KKW8zp-4
// Data to show billing info section.
type BillingCycleInfo struct {
	Title     string
	StartDate *date.Date
	EndDate   *date.Date
	// ViewHistoryDeeplink, if not nil, will cause View history ITC to be displayed.
	// Clicking the icon will open an history page corresponding to ViewHistoryDeeplink
	// If ViewHistoryDeeplink is  nil, the View history will not be shown.
	ViewHistoryDeeplink *deeplink.Deeplink
}

// BoosterRewardsData figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43394-37915&t=AgDy9aNDY5KKW8zp-4
// https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-53616&t=AgDy9aNDY5KKW8zp-4
type BoosterRewardsData struct {
	// heading to show info like "REWARDS ON TOP 20+ BRANDS" or "3X WEEKEND BOOSTER"
	Heading string
	// Text to show info like "500 Fi-Coins won on weekend spends", "650 Fi-Coins missed this month 😔" or "Convert to 750 Fi-Coins when you spend ₹15,000"
	RewardInfoText string
	// coins amount earned (in Amplifi card)
	BoosterRewardCoinsEarned string
	// will show loader when loading percentage is greater than 0 and less than 100
	// If loading percentage is 100 then we hide the loader and show "5X BOOSTER APPLIED" tag
	LoaderPercentage int32
	// this flag is used to show reward locked state
	// https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-54326&t=AgDy9aNDY5KKW8zp-4
	IsRewardLocked bool

	// List of image url to be shown in the booster widget.
	// in amplifi card -> pass empty or nil
	// in magnifi card -> pass list of 4 brands image
	// in simplifi card -> pass list of calendar image
	ImageUrlList  []string
	FooterData    *BoosterRewardsFooterData
	IsAmplifiCard bool
}

// figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-53641&t=AgDy9aNDY5KKW8zp-4
type BoosterRewardsFooterData struct {
	BrandsStackImageUrl string
	CtaTitle            string
	Deeplink            *deeplink.Deeplink
}

const leftWidgetWidth = 192

func GetCCRewardDashboardWidget(billingCycleInfo *BillingCycleInfo, rewardsAndOfferData []*RewardsAndOffersData, rewardCoinSummaryData *RewardCoinSummaryData, boosterRewardsData *BoosterRewardsData, cardDesignEnhancementEnabled bool,
) *sections.Section {
	bgColor := colors.ColorFog
	if cardDesignEnhancementEnabled {
		bgColor = colors.ColorSnow
	}
	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				VisualProperties: getVisualProperties(
					properties.Size_Dimension_DIMENSION_TYPE_FILL, 0,
					properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0,
					bgColor, &properties.PaddingProperty{
						Top:    28,
						Left:   16,
						Right:  16,
						Bottom: 28,
					}, nil, nil),
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
				Components: []*components.Component{
					GetCCDashboardRewardWidgetHeader(billingCycleInfo),
					GetCCDashboardRewardWidgetBody(rewardsAndOfferData, rewardCoinSummaryData, boosterRewardsData, cardDesignEnhancementEnabled),
				},
			},
		},
	}
}

func GetCCDashboardRewardWidgetHeader(data *BillingCycleInfo) *components.Component {
	headerComponent := []*components.Component{
		{
			Content: anyWoErr(
				&ui.VerticalKeyValuePair{
					Title:                        ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(data.Title, "#313234", commontypes.FontStyle_SUBTITLE_S)),
					Value:                        ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(getBillingCycleText(data.StartDate, data.EndDate), "#646464", commontypes.FontStyle_BODY_XS)),
					VerticalPaddingBtwTitleValue: 4,
					HAlignment:                   ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT,
				}),
		},
	}
	if data.ViewHistoryDeeplink != nil {
		viewHistoryItcComponent := &components.Component{
			Content: anyWoErr(
				ui.NewITC().
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle("VIEW HISTORY", "#646464", commontypes.FontStyle_OVERLINE_XS_CAPS)).
					WithRightVisualElement(commontypes.GetVisualElementImageFromUrl(internal.RightArrowIconUrl).
						WithImageType(commontypes.ImageType_PNG).
						WithProperties(&commontypes.VisualElementProperties{Width: 16, Height: 16})),
			),
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{OnClickBehavior: &behaviors.OnClickBehavior{Action: anyWoErr(data.ViewHistoryDeeplink)}},
				},
			},
		}
		headerComponent = append(headerComponent, viewHistoryItcComponent)
	}
	headerComponent = append(headerComponent, getSpacerComponent())
	return &components.Component{
		Content: anyWoErr(&sections.HorizontalListSection{
			VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_FILL,
				0, properties.Size_Dimension_DIMENSION_TYPE_UNSPECIFIED, 0, "",
				nil, nil, nil),
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
			VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
			Components:            headerComponent,
		}),
	}
}

func GetCCDashboardRewardWidgetBody(rewardsAndOfferData []*RewardsAndOffersData, rewardCoinSummaryData *RewardCoinSummaryData, boosterRewardsData *BoosterRewardsData, cardDesignEnhancementEnabled bool) *components.Component {
	return &components.Component{
		Content: anyWoErr(&sections.HorizontalListSection{
			VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0,
				properties.Size_Dimension_DIMENSION_TYPE_UNSPECIFIED, 0, "", nil,
				&properties.PaddingProperty{Top: 20}, nil),
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
			VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_TOP,
			Components: []*components.Component{
				GetCCDashboardRewardWidgetLeftSideBody(rewardCoinSummaryData, boosterRewardsData, cardDesignEnhancementEnabled),
				GetCCDashboardRewardWidgetRightSideBody(rewardsAndOfferData, cardDesignEnhancementEnabled),
			},
		}),
	}

}

func GetCCDashboardRewardWidgetLeftSideBody(rewardCoinSummaryData *RewardCoinSummaryData, boosterRewardsData *BoosterRewardsData, cardDesignEnhancementEnabled bool) *components.Component {
	var (
		leftWidgetComponent []*components.Component
	)
	if boosterRewardsData.IsAmplifiCard {
		leftWidgetComponent = []*components.Component{
			GetCCDashboardRewardWidgetBoosterSection(boosterRewardsData, cardDesignEnhancementEnabled),
			{
				Content: anyWoErr(&components.Spacer{
					SpacingValue: components.Spacing_SPACING_S,
				}),
			},
			GetRewardsSummaryWidget(rewardCoinSummaryData, cardDesignEnhancementEnabled),
		}
	} else {
		leftWidgetComponent = []*components.Component{
			GetRewardsSummaryWidget(rewardCoinSummaryData, cardDesignEnhancementEnabled),
			{
				Content: anyWoErr(&components.Spacer{
					SpacingValue: components.Spacing_SPACING_S,
				}),
			},
			GetCCDashboardRewardWidgetBoosterSection(boosterRewardsData, cardDesignEnhancementEnabled),
		}
	}
	return &components.Component{
		Content: anyWoErr(
			&sections.VerticalListSection{
				VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_EXACT, leftWidgetWidth, properties.Size_Dimension_DIMENSION_TYPE_WRAP,
					0, "", nil, &properties.PaddingProperty{Right: 12}, nil),
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
				Components:          leftWidgetComponent,
			},
		),
	}
}

func GetCCDashboardRewardWidgetBoosterSection(data *BoosterRewardsData, cardDesignEnhancementEnabled bool) *components.Component {
	boosterRewardsComponent := make([]*components.Component, 0)

	// Heading : "REWARDS ON top 20+ brands" or "4x weekend booster"
	boosterRewardsComponent = append(boosterRewardsComponent, &components.Component{
		Content: anyWoErr(commontypes.GetTextFromStringFontColourFontStyle(data.Heading, "#929599", commontypes.FontStyle_SUBTITLE_2XS).WithAlignment(commontypes.Text_ALIGNMENT_LEFT)),
	})

	// total rewards coins earned on 20+ brands
	if data.BoosterRewardCoinsEarned != "" {
		boosterRewardsComponent = append(boosterRewardsComponent, &components.Component{
			Content: anyWoErr(ui.NewITC().
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(data.BoosterRewardCoinsEarned, "#313234", commontypes.FontStyle_NUMBER_2XL)).
				WithLeftVisualElement(commontypes.GetVisualElementImageFromUrl(internal.FiProcessingRewardsIconUrl).
					WithImageType(commontypes.ImageType_PNG).
					WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28})).
				WithLeftImagePadding(4).WithContainerPadding(4, 0, 0, 0)),
		})
	}

	// RewardInfoText : "500 Fi-Coins won on weekend spends" or "4X Fi-Coins missed this month 😔" or "Convert to 750 Fi-Coins when you spend ₹15,000"
	if data.RewardInfoText != "" {
		rewardInfoTextFontStyle := commontypes.FontStyle_SUBTITLE_M
		if data.IsAmplifiCard {
			rewardInfoTextFontStyle = commontypes.FontStyle_BODY_XS
		}
		boosterRewardsComponent = append(boosterRewardsComponent, &components.Component{
			Content: anyWoErr(&components.Spacer{
				SpacingValue: components.Spacing_SPACING_XS,
			}),
		}, &components.Component{
			Content: anyWoErr(commontypes.GetTextFromHtmlStringFontColourFontStyle(data.RewardInfoText, "#313234", rewardInfoTextFontStyle).WithAlignment(commontypes.Text_ALIGNMENT_LEFT)),
		})
	}

	// horizontal images
	if data.ImageUrlList != nil && len(data.ImageUrlList) > 0 {
		boosterRewardsComponent = append(boosterRewardsComponent, &components.Component{
			Content: anyWoErr(&components.Spacer{
				SpacingValue: components.Spacing_SPACING_S,
			}),
		}, GetHorizontalImageList(data.ImageUrlList, 32, components.Spacing_SPACING_XS))
	}

	// progress bar
	if data.LoaderPercentage > 0 && data.LoaderPercentage < 100 {
		boosterRewardsComponent = append(boosterRewardsComponent,
			&components.Component{Content: anyWoErr(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
			GetProgressBarWidget(data.LoaderPercentage, data.IsRewardLocked))
	}

	// 5X booster applied tag
	if data.LoaderPercentage >= 100 {
		boosterRewardsComponent = append(boosterRewardsComponent,
			&components.Component{Content: anyWoErr(&components.Spacer{SpacingValue: components.Spacing_SPACING_XS})},
			&components.Component{Content: anyWoErr(ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("5X BOOSTER APPLIED", "#648E4D", commontypes.FontStyle_SUBTITLE_2XS)).
				WithLeftVisualElement(commontypes.GetVisualElementImageFromUrl(internal.BoosterAppliedCheckmark).
					WithImageType(commontypes.ImageType_PNG).
					WithProperties(&commontypes.VisualElementProperties{Width: 12, Height: 12})).
				WithLeftImagePadding(2).WithContainerPadding(2, 2, 2, 2).WithContainerBackgroundColor("#EDF5EB").
				WithContainerCornerRadius(12)),
			})
	}

	boosterRewardsBodyBgColor := colors.ColorSnow
	boosterRewardsFooterBgColor := colors.ColorOnDarkHighEmphasis
	if cardDesignEnhancementEnabled {
		boosterRewardsBodyBgColor = colors.ColorOnDarkHighEmphasis
		boosterRewardsFooterBgColor = colors.ColorFog
	}

	boosterRewardsBody := &components.Component{
		Content: anyWoErr(&sections.VerticalListSection{
			VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0,
				properties.Size_Dimension_DIMENSION_TYPE_EXACT, 144, boosterRewardsBodyBgColor,
				&properties.PaddingProperty{
					Top:    16,
					Left:   16,
					Right:  16,
					Bottom: 16,
				},
				nil,
				&properties.CornerProperty{
					TopRightCornerRadius: 16,
					TopLeftCornerRadius:  16,
				}),
			HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
			VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
			Components:          boosterRewardsComponent,
		}),
	}

	footerCtaITC := ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(data.FooterData.CtaTitle, "#00B899", commontypes.FontStyle_SUBTITLE_XS)).WithContainerPaddingSymmetrical(10, 10)
	if data.FooterData.BrandsStackImageUrl != "" {
		footerCtaITC.WithLeftVisualElement(
			commontypes.GetVisualElementImageFromUrl(data.FooterData.BrandsStackImageUrl).
				WithImageType(commontypes.ImageType_PNG).
				WithProperties(&commontypes.VisualElementProperties{Width: 65, Height: 24})).
			WithLeftImagePadding(8).WithContainerPaddingSymmetrical(5, 5)

	}

	boosterRewardsFooter := &components.Component{
		Content: anyWoErr(&sections.HorizontalListSection{
			VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0,
				properties.Size_Dimension_DIMENSION_TYPE_EXACT, 34, boosterRewardsFooterBgColor,
				nil,
				nil,
				&properties.CornerProperty{
					TopRightCornerRadius: 0,
					TopLeftCornerRadius:  0,
					BottomLeftCorner:     16,
					BottomRightCorner:    16,
				}),
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
			VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
			Components: []*components.Component{
				{
					Content: anyWoErr(footerCtaITC),
					InteractionBehaviors: []*behaviors.InteractionBehavior{
						{
							Behavior: &behaviors.InteractionBehavior_OnClickBehavior{OnClickBehavior: &behaviors.OnClickBehavior{Action: anyWoErr(data.FooterData.Deeplink)}},
						},
					},
				},
			},
		}),
	}

	return &components.Component{
		Content: anyWoErr(
			&sections.VerticalListSection{
				Components: []*components.Component{
					boosterRewardsBody, boosterRewardsFooter,
				},
			},
		),
	}
}

// GetHorizontalImageList This function takes a list of image URLs and constructs a horizontal list of image components. Each image
// component is created with a specified size (square image). Additionally, a spacing component is added between each image
// component.
func GetHorizontalImageList(imageUrls []string, imageSize int32, spacingBetweenImage components.Spacing) *components.Component {
	imageComponent := make([]*components.Component, 0)
	for index, imageUrl := range imageUrls {
		if index != 0 {
			// adding spacing between them.
			imageComponent = append(imageComponent, &components.Component{
				Content: anyWoErr(&components.Spacer{
					SpacingValue: spacingBetweenImage,
				}),
			})
		}

		imageComponent = append(imageComponent, &components.Component{
			Content: anyWoErr(commontypes.GetVisualElementFromUrlHeightAndWidth(imageUrl, imageSize, imageSize).WithImageType(commontypes.ImageType_PNG)),
		})

	}
	return &components.Component{
		Content: anyWoErr(
			&sections.HorizontalListSection{
				Components: imageComponent,
			},
		),
	}
}

func GetProgressBarWidget(loadingPercent int32, isRewardLocked bool) *components.Component {
	progressBarTotalWidth := leftWidgetWidth - 32
	filledWidth := (loadingPercent * int32(progressBarTotalWidth)) / 100
	filledColor := "#AFD2A2"
	unfilledColor := "#EDF5EB"
	if isRewardLocked {
		filledColor = "#E6E9ED"
		unfilledColor = "#EFF2F6"
	}
	progressBar := &sections.HorizontalListSection{
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_START,
		Components: []*components.Component{
			{
				Content: anyWoErr(&sections.HorizontalListSection{
					Components: []*components.Component{},
					VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_EXACT, filledWidth, properties.Size_Dimension_DIMENSION_TYPE_EXACT, 6,
						filledColor, nil, nil,
						&properties.CornerProperty{
							TopLeftCornerRadius:  12,
							TopRightCornerRadius: 12,
							BottomLeftCorner:     12,
							BottomRightCorner:    12,
						}),
				}),
			},
		},
		VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0, properties.Size_Dimension_DIMENSION_TYPE_EXACT, 6,
			unfilledColor, nil, nil,
			&properties.CornerProperty{
				TopLeftCornerRadius:  12,
				TopRightCornerRadius: 12,
				BottomLeftCorner:     12,
				BottomRightCorner:    12,
			}),
	}
	return &components.Component{
		Content: anyWoErr(progressBar),
	}
}

func GetRewardsSummaryWidget(data *RewardCoinSummaryData, cardDesignEnhancementEnabled bool) *components.Component {
	summaryWidgetBgColor := colors.ColorSnow
	if cardDesignEnhancementEnabled {
		summaryWidgetBgColor = colors.ColorOnDarkHighEmphasis
	}
	coinsEarnedSummaryITC := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(data.CoinsEarned, "#313234", commontypes.FontStyle_NUMBER_2XL)).
		WithLeftVisualElement(commontypes.GetVisualElementImageFromUrl(internal.FiProcessingRewardsIconUrl).
			WithImageType(commontypes.ImageType_PNG).
			WithProperties(&commontypes.VisualElementProperties{Width: 28, Height: 28})).
		WithLeftImagePadding(4)

	infoIconInteractionBehavior := make([]*behaviors.InteractionBehavior, 0)

	if data.InfoDeeplink != nil {
		coinsEarnedSummaryITC = coinsEarnedSummaryITC.
			WithRightVisualElement(commontypes.GetVisualElementImageFromUrl(internal.InfoIconUrl).
				WithImageType(commontypes.ImageType_PNG).
				WithProperties(&commontypes.VisualElementProperties{Width: 24, Height: 24})).
			WithRightImagePadding(4)

		infoIconInteractionBehavior = append(infoIconInteractionBehavior, &behaviors.InteractionBehavior{
			Behavior: &behaviors.InteractionBehavior_OnClickBehavior{OnClickBehavior: &behaviors.OnClickBehavior{Action: anyWoErr(data.InfoDeeplink)}},
		})
	}
	return &components.Component{
		Content: anyWoErr(&sections.VerticalListSection{
			VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0, properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0,
				summaryWidgetBgColor,
				&properties.PaddingProperty{
					Top:    16,
					Left:   16,
					Right:  16,
					Bottom: 16,
				}, nil,
				&properties.CornerProperty{
					TopRightCornerRadius: 12,
					TopLeftCornerRadius:  12,
					BottomLeftCorner:     12,
					BottomRightCorner:    12,
				}),
			HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
			VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_TOP,
			Components: []*components.Component{
				{
					Content: anyWoErr(commontypes.GetTextFromStringFontColourFontStyle(data.Title, "#929599", commontypes.FontStyle_SUBTITLE_2XS)),
				},
				{
					Content: anyWoErr(&components.Spacer{
						SpacingValue: components.Spacing_SPACING_XXS,
					}),
				},
				{
					Content:              anyWoErr(coinsEarnedSummaryITC),
					InteractionBehaviors: infoIconInteractionBehavior,
				},
			},
		}),
	}

}

func GetCCDashboardRewardWidgetRightSideBody(rewardsAndOffersData []*RewardsAndOffersData, cardDesignEnhancementEnabled bool) *components.Component {
	rewardAndOfferComponents := make([]*components.Component, 0)

	// card title component "SPEND FI-COINS"
	rewardAndOfferComponents = append(rewardAndOfferComponents, &components.Component{
		Content: anyWoErr(commontypes.GetTextFromStringFontColourFontStyle("SPEND FI-COINS", "#929599", commontypes.FontStyle_SUBTITLE_2XS)),
	})

	for index, data := range rewardsAndOffersData {
		if index != 0 {
			// Adding a divider of height 1 px between 2 ITC component
			rewardAndOfferComponents = append(rewardAndOfferComponents, &components.Component{
				Content: anyWoErr(&sections.HorizontalListSection{
					VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0, properties.Size_Dimension_DIMENSION_TYPE_EXACT, 1,
						"#E6E9ED", nil, nil, nil),
				}),
			})
		}
		rewardAndOfferComponents = append(rewardAndOfferComponents, &components.Component{
			Content: anyWoErr(
				ui.NewITC().
					WithTexts(commontypes.GetTextFromStringFontColourFontStyle(data.Title, "#313234", commontypes.FontStyle_SUBTITLE_S)).
					WithLeftVisualElement(commontypes.GetVisualElementImageFromUrl(data.ImageUrl).
						WithImageType(commontypes.ImageType_PNG).
						WithProperties(&commontypes.VisualElementProperties{Width: 36, Height: 36})).
					WithLeftImagePadding(8).
					WithRightVisualElement(commontypes.GetVisualElementImageFromUrl(internal.RightArrowIconUrl).
						WithImageType(commontypes.ImageType_PNG).
						WithProperties(&commontypes.VisualElementProperties{Width: 16, Height: 16})).
					WithRightImagePadding(2).
					WithContainerProperties(&ui.IconTextComponent_ContainerProperties{
						TopPadding:    16,
						BottomPadding: 16,
					}),
			),
			InteractionBehaviors: []*behaviors.InteractionBehavior{
				{
					Behavior: &behaviors.InteractionBehavior_OnClickBehavior{OnClickBehavior: &behaviors.OnClickBehavior{Action: anyWoErr(data.Deeplink)}},
				},
			},
		})
	}
	topComponentBgColor := colors.ColorSnow
	if cardDesignEnhancementEnabled {
		topComponentBgColor = colors.ColorOnDarkHighEmphasis
	}
	rewardAndOfferTopComponents := &components.Component{
		Content: anyWoErr(&sections.VerticalListSection{
			VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0, properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0,
				topComponentBgColor,
				&properties.PaddingProperty{
					Top:    16,
					Left:   16,
					Right:  16,
					Bottom: 0,
				}, nil,
				&properties.CornerProperty{
					TopRightCornerRadius: 12,
					TopLeftCornerRadius:  12,
					BottomLeftCorner:     0,
					BottomRightCorner:    0,
				}),
			HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_LEFT,
			Components:          rewardAndOfferComponents,
		}),
	}

	return &components.Component{
		Content: anyWoErr(
			&sections.VerticalListSection{
				Components: []*components.Component{
					rewardAndOfferTopComponents, getRewardsAndOffersFooterComponent(cardDesignEnhancementEnabled),
				},
			},
		),
	}
}

func getRewardsAndOffersFooterComponent(cardDesignEnhancementEnabled bool) *components.Component {
	componentBgColor := colors.ColorOnDarkHighEmphasis
	if cardDesignEnhancementEnabled {
		componentBgColor = colors.ColorFog
	}
	return &components.Component{
		Content: anyWoErr(&sections.HorizontalListSection{
			VisualProperties: getVisualProperties(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0, properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0,
				componentBgColor,
				&properties.PaddingProperty{
					Top:    10,
					Left:   8,
					Right:  8,
					Bottom: 10,
				}, nil, &properties.CornerProperty{
					TopRightCornerRadius: 0,
					TopLeftCornerRadius:  0,
					BottomLeftCorner:     12,
					BottomRightCorner:    12,
				}),
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
			Components: []*components.Component{
				{
					Content: anyWoErr(ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("100 Fi-Coins = ₹3", "#929599", commontypes.FontStyle_BODY_XS))),
				},
			},
		}),
	}
}

func getVisualProperties(widthType properties.Size_Dimension_Type, widthExactValue int32,
	heightType properties.Size_Dimension_Type, heightExactValue int32, bgColor string, paddingProperty *properties.PaddingProperty,
	marginProperty *properties.PaddingProperty, cornerProperty *properties.CornerProperty) []*properties.VisualProperty {
	return []*properties.VisualProperty{
		{
			Properties: &properties.VisualProperty_ContainerProperty{
				ContainerProperty: &properties.ContainerProperty{
					Size: &properties.Size{
						Width: &properties.Size_Dimension{
							Type:       widthType,
							ExactValue: widthExactValue,
						},
						Height: &properties.Size_Dimension{
							Type:       heightType,
							ExactValue: heightExactValue,
						},
					},
					BgColor: typesUiWidget.GetBlockBackgroundColour(bgColor),
					Padding: paddingProperty,
					Margin:  marginProperty,
					Corner:  cornerProperty,
				},
			},
		},
	}
}
