// nolint
package firefly

import (
	"context"
	"fmt"
	"math"
	"sort"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgettheme "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/rewards"

	pkgErr "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"
	actorPb "github.com/epifi/gamma/api/actor"
	categorizerBePb "github.com/epifi/gamma/api/categorizer"
	ffBePb "github.com/epifi/gamma/api/firefly"
	ffBeAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffBeAccountsEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ffBeBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ffEnumsBePb "github.com/epifi/gamma/api/firefly/enums"
	categorizerFePb "github.com/epifi/gamma/api/frontend/categorizer"
	"github.com/epifi/gamma/api/frontend/deeplink"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	"github.com/epifi/gamma/api/frontend/header"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/firefly/helper"
	feHelper "github.com/epifi/gamma/frontend/firefly/helper"
	"github.com/epifi/gamma/frontend/firefly/internal"
	fireflyPkg "github.com/epifi/gamma/pkg/firefly"
)

const (
	recentTransactionCount   = 5
	allTxnBillingCycleLayout = "%d %s - %d %s"
	viewStatementCtaText     = "VIEW STATEMENT"
	allTxnDateLayout         = "%s, %d %s"
)

func (s *Service) GetRecentTransactions(ctx context.Context, req *ffPb.GetRecentTransactionsRequest) (*ffPb.GetRecentTransactionsResponse, error) {
	var (
		res = &ffPb.GetRecentTransactionsResponse{}
		err error
	)
	res.ViewAllTransactionsCta = &deeplink.Cta{
		Text: "VIEW ALL",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_VIEW_ALL_TRANSACTIONS,
			ScreenOptions: &deeplink.Deeplink_CreditCardViewAllTransactionScreenOptions{
				CreditCardViewAllTransactionScreenOptions: &deeplink.CreditCardViewAllTransactionScreenOptions{
					EnableViewAllTxnPagination: s.dynamicConf.CreditCard().EnableCCAllTxnPagination(),
				},
			},
		},
	}
	getTxnsViewResp, err := s.fireflyAccountingClient.GetPaginatedCreditCardTxnView(ctx, &ffBeAccountsPb.GetPaginatedCreditCardTxnViewRequest{
		Identifier:     &ffBeAccountsPb.GetPaginatedCreditCardTxnViewRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
		StartTimestamp: timestamppb.New(time.Now()),
		PageSize:       recentTransactionCount,
		Descending:     true,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in fetching txns view by account id", zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	case !getTxnsViewResp.GetStatus().IsSuccess():
		logger.Error(ctx, "error in fetching txns view by account id", zap.String(logger.STATUS, getTxnsViewResp.GetStatus().String()),
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{Status: getTxnsViewResp.GetStatus()}
		return res, nil
	}
	creditAccountResp, err := s.fireflyAccountingClient.GetAccounts(ctx, &ffBeAccountsPb.GetAccountsRequest{
		GetBy: &ffBeAccountsPb.GetAccountsRequest_ActorId{
			ActorId: req.GetReq().GetAuth().GetActorId(),
		}})
	if te := epifigrpc.RPCError(creditAccountResp, err); te != nil {
		logger.Error(ctx, "error in fetching credit account info", zap.Error(err))
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	}

	transactions := s.convertBeTransactionToFeTransactionV2(ctx, getTxnsViewResp.GetTxnsViewModelList(),
		req.GetReq().GetAuth().GetActorId(), false, creditAccountResp.GetAccounts()[0], req.GetReq())
	res.RecentTransactions = transactions
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	rewardInfoTitle := "Fi-Coins will get credited 10 days after bill generation"
	res.RewardInfoItem = &deeplink.InfoItem{
		Icon:  "https://epifi-icons.pointz.in/credit_card_images/coins_pending.png",
		Title: rewardInfoTitle,
	}
	if featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled(ctx, &featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		ExternalDeps: &common.ExternalDependencies{
			Evaluator: s.releaseEvaluator,
		},
	}) {
		res.RewardInfoItem = &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/FiCoinsToFiPoints/bannerIcon.png",
			Title: "Fi-Points take up to 10 days to get credited post statement generation",
		}
	}

	return res, nil
}

func (s *Service) GetTransactions(ctx context.Context, req *ffPb.GetTransactionsRequest) (*ffPb.GetTransactionsResponse, error) {
	var (
		res                 = &ffPb.GetTransactionsResponse{}
		cardResp            = &ffBePb.GetCreditCardResponse{}
		cardErr             error
		billResp            = &ffBeBillingPb.GetCreditCardBillResponse{}
		billErr             error
		getTxnsViewResp     = &ffBeAccountsPb.GetPaginatedCreditCardTxnViewResponse{}
		txnsViewErr         error
		aggregateRewardResp []*ffPb.AggregatedRewardsInfo
		aggregateRewardErr  error
		accountResp         = &ffBeAccountsPb.GetAccountResponse{}
		accountFetchErr     error
	)
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		getTxnsViewResp, txnsViewErr = s.fireflyAccountingClient.GetPaginatedCreditCardTxnView(grpCtx,
			&ffBeAccountsPb.GetPaginatedCreditCardTxnViewRequest{
				Identifier:     &ffBeAccountsPb.GetPaginatedCreditCardTxnViewRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
				StartTimestamp: timestamppb.New(time.Now()),
				// keeping this as very high number until we add pagination from frontend side
				PageSize:   100000,
				Descending: true,
			})
		if te := epifigrpc.RPCError(getTxnsViewResp, txnsViewErr); te != nil {
			if !getTxnsViewResp.GetStatus().IsRecordNotFound() {
				logger.Error(ctx, "error in fetching txn by account id", zap.Error(te), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
				return te
			}
		}
		return nil
	})
	grp.Go(func() error {
		cardResp, cardErr = s.fireFlyClient.GetCreditCard(grpCtx, &ffBePb.GetCreditCardRequest{
			GetBy: &ffBePb.GetCreditCardRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
			SelectFieldMasks: []ffEnumsBePb.CreditCardFieldMask{ffEnumsBePb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID,
				ffEnumsBePb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
				ffEnumsBePb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACCOUNT_ID},
		})
		if te := epifigrpc.RPCError(cardResp, cardErr); te != nil {
			logger.Error(ctx, "Error fetching card by actor id", zap.Error(te), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
			return te
		}
		accountResp, accountFetchErr = s.fireflyAccountingClient.GetAccount(ctx, &ffBeAccountsPb.GetAccountRequest{
			GetBy: &ffBeAccountsPb.GetAccountRequest_AccountId{AccountId: cardResp.GetCreditCard().GetAccountId()}})
		if te := epifigrpc.RPCError(accountResp, accountFetchErr); te != nil {
			logger.Error(ctx, "Error fetching account by account id", zap.Error(te), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()),
				zap.String(logger.ACCOUNT_ID, cardResp.GetCreditCard().GetAccountId()))
			return te
		}
		return nil
	})
	grp.Go(func() error {
		billResp, billErr = s.ffBillingClient.GetCreditCardBill(grpCtx, &ffBeBillingPb.GetCreditCardBillRequest{
			GetBy: &ffBeBillingPb.GetCreditCardBillRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()}})
		if te := epifigrpc.RPCError(billResp, billErr); te != nil {
			logger.Error(ctx, "Error fetching bill by actor id", zap.Error(te), zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
			if !billResp.GetStatus().IsRecordNotFound() {
				return te
			}
		}
		return nil
	})
	grp.Go(func() error {
		aggregateRewardResp, aggregateRewardErr = s.getAllTxnAggregatedRewards(grpCtx, req.GetReq().GetAuth().GetActorId(), accountResp.GetAccount())
		if aggregateRewardErr != nil {
			logger.Error(ctx, "error in fetching statement wise aggregated rewards", zap.Error(aggregateRewardErr))
		}
		return nil
	})
	err := grp.Wait()
	if err != nil {
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	}
	res.Transactions = s.convertBeTransactionToFeTransactionV2(ctx, getTxnsViewResp.GetTxnsViewModelList(), req.GetReq().GetAuth().GetActorId(), true, accountResp.GetAccount(), req.GetReq())
	res.RewardInfoItem = &deeplink.InfoItem{
		Icon:  internal.InfoIconUrl,
		Title: internal.RewardInfoTitle,
	}
	res.CardId = cardResp.GetCreditCard().GetId()
	res.StartDate = cardResp.GetCreditCard().GetBasicInfo().GetBillGenDate()
	// In cases where bill gen date is 1, this could break. Discussed with product and bill gen date cannot be first day of month.
	res.EndDate = cardResp.GetCreditCard().GetBasicInfo().GetBillGenDate() - 1
	res.ViewStatementCta = &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:        "",
			Title:       "View statement",
			Desc:        "",
			ToolTip:     nil,
			CopyAllowed: false,
		},
		Cta: helper.GetViewStatementCta(cardResp.GetCreditCard().GetId(), nil),
	}

	if len(getTxnsViewResp.GetTxnsViewModelList()) > 0 {
		res.IsLatestStatementGenerated = feHelper.IsLatestStatementGenerated(getTxnsViewResp.GetTxnsViewModelList()[0].GetTransactionTimestamp(), billResp)
	}

	s.fcFpPostMigrationHeaderEnrichment(ctx, req.GetReq().GetAuth().GetActorId(), res)

	res.AggregatedRewards = aggregateRewardResp
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

func (s *Service) fcFpPostMigrationHeaderEnrichment(ctx context.Context, actorId string, res *ffPb.GetTransactionsResponse) {
	// Add header banner if feature flag is enabled
	if featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled(ctx, &featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator: s.releaseEvaluator,
		},
	}) {
		res.HeaderBanner = &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(s.dynamicConf.AllTransactionHeaderBanner().PostConversion().FiPointImageUrl(), 24, 24),
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(s.dynamicConf.CcAllTransactionHeaderBanner().PostConversion().Text(), "#000000", commontypes.FontStyle_SUBTITLE_XS),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BackgroundColour: widgettheme.GetLinearGradientBackgroundColour(90, []*widgettheme.ColorStop{
					{
						Color:          "#FCF1E4",
						StopPercentage: 0,
					},
					{
						Color:          "#FDDCA8",
						StopPercentage: 100,
					},
				}),
				LeftPadding:   s.dynamicConf.AllTransactionHeaderBanner().PostConversion().LeftPadding(),
				RightPadding:  s.dynamicConf.AllTransactionHeaderBanner().PostConversion().RightPadding(),
				TopPadding:    s.dynamicConf.AllTransactionHeaderBanner().PostConversion().TopPadding(),
				BottomPadding: s.dynamicConf.AllTransactionHeaderBanner().PostConversion().BottomPadding(),
			},
			Deeplink: rewards.GetFiCoinsFiPointsKnowMoreButtonDeeplink(ctx, actorId, s.fireflyAccountingClient, s.fireflyV2Client, s.tieringClient, true),
		}
	}
}

func (s *Service) getAllTxnAggregatedRewards(ctx context.Context, actorId string, creditAccount *ffBeAccountsPb.CreditAccount) ([]*ffPb.AggregatedRewardsInfo, error) {
	billResp, err := s.ffBillingClient.FetchAllBillAndBillPayments(ctx, &ffBeBillingPb.FetchAllBillAndBillPaymentsRequest{ActorId: actorId})
	cardProgram := fireflyPkg.GetCardProgramWithFallback(creditAccount.GetCardProgram())
	var (
		billIds []string
	)
	if te := epifigrpc.RPCError(billResp, err); te != nil {
		return nil, pkgErr.Wrap(te, "error in FetchAllBillAndBillPayments")
	}
	// In case we do not get any bills, we return directly from here to avoid further calls
	if len(billResp.GetCardBills()) == 0 {
		logger.Info(ctx, "no bill found for given actor id: ", zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, nil
	}

	for _, bill := range billResp.GetCardBills() {
		billIds = append(billIds, bill.GetId())
	}

	if cardProgram.GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_SECURED {
		return s.getSecuredBillAggregatedRewards(ctx, billIds, billResp.GetCardBills(), creditAccount)
	}

	billIdToAggregatedReward2X := make(map[string]*ffPb.RewardsInfoWithHeaderIcon)
	billIdToAggregatedReward5X := make(map[string]*ffPb.RewardsInfoWithHeaderIcon)

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		// 5x rewards fetch
		billIdToAggregatedReward5X, err = s.getRewardsMultiplierInfoOverABillingCycle(grpCtx, billIds,
			rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER, "https://epifi-icons.pointz.in/credit_card_images/5x_reward_img.png", "Extra rewards")
		if err != nil {
			logger.Error(ctx, "error in fetching 5x rewards map", zap.Error(err))
			return nil
		}
		return nil
	})
	grp.Go(func() error {
		// 2x rewards fetch
		billIdToAggregatedReward2X, err = s.getRewardsMultiplierInfoOverABillingCycle(grpCtx, billIds,
			rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER, "https://epifi-icons.pointz.in/credit_card_images/2x_reward_img.png", "Extra rewards")
		if err != nil {
			logger.Error(ctx, "error in fetching 2x rewards map", zap.Error(err))
			return nil
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return nil, err
	}
	ccTxnRewards := make([]*ffPb.AggregatedRewardsInfo, 0)
	for _, bill := range billResp.GetCardBills() {
		rewardList := make([]*ffPb.RewardsInfoWithHeaderIcon, 0)
		reward2x := billIdToAggregatedReward2X[bill.GetId()]
		reward5x := billIdToAggregatedReward5X[bill.GetId()]
		if reward5x != nil {
			rewardList = append(rewardList, reward5x)
		}
		if reward2x != nil {
			rewardList = append(rewardList, reward2x)
		}
		if len(rewardList) == 0 {
			continue
		}
		stmtTime := bill.GetStatementDate().AsTime()
		ccTxnRewards = append(ccTxnRewards, &ffPb.AggregatedRewardsInfo{
			RewardsMultipliers: rewardList,
			BillGenTimestamp:   timestamppb.New(*datetime.AddNMonths(&stmtTime, -1)),
		})
	}
	return ccTxnRewards, nil
}

// convertBeTransactionToFeTransaction converts be transaction to fe transaction proto
// fetchTxnDisplayCategories boolean to determine if txn categories need to be fetched and displayed
// this is deprecated in favour of convertBeTransactionToFeTransactionV2
func (s *Service) convertBeTransactionToFeTransaction(ctx context.Context, transactions []*ffBeAccountsPb.CreditCardTransactionViewModel, actorId string, fetchTxnDisplayCategories bool, creditAccount *ffBeAccountsPb.CreditAccount, reqHeader *header.RequestHeader) []*ffPb.Transaction {
	var (
		feTransactions               []*ffPb.Transaction
		externalTxnIds               []string
		iconUrl                      string
		actorBgColour                string
		externalTxnIdToRewardInfoMap = make(map[string]*ffPb.RewardsInfo)
		err                          error
	)
	cardProgram := fireflyPkg.GetCardProgramWithFallback(creditAccount.GetCardProgram())
	if cardProgram.GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED {
		for _, txn := range transactions {
			externalTxnIds = append(externalTxnIds, txn.GetExternalTxnId())
		}
		// we will not fail the call for fetching rewards info
		externalTxnIdToRewardInfoMap, err = s.getRewardsInfo(ctx, externalTxnIds)
		if err != nil {
			logger.Error(ctx, "error in fetching rewards info for txns", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		}
	} else {
		for _, txn := range transactions {
			externalTxnIds = append(externalTxnIds, txn.GetExternalTxnId())
		}
		externalTxnIdToRewardInfoMap, err = s.getSecuredTxnRewardsInfo(ctx, externalTxnIds, creditAccount)
		if err != nil {
			logger.Error(ctx, "error in fetching rewards info for txns", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		}
	}
	for _, txn := range transactions {
		var (
			txnDisplayCategories []*ffPb.TransactionDisplayCategory
		)
		txnType, ok := beTxnTypeToFeTxnTransferTypeMap[txn.GetTransactionType()]
		if !ok {
			logger.Error(ctx, "error in fetching fe transaction type", zap.String(logger.TXN_TYPE,
				txn.GetTransactionType().String()), zap.String(logger.TXN_ID, txn.GetTransactionId()))
			continue
		}
		switch {
		case txn.GetTransactionStatus() == ffBeAccountsEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE:
			iconUrl = internal.FailureTxnIconUrl
		case txn.GetTransactionType() == ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT:
			iconUrl = internal.DebitTxnIconUrl
		default:
			iconUrl = internal.CreditTxnIconUrl
		}
		getEntityDetailsRes, err := s.actorClient.GetEntityDetailsByActorId(ctx,
			&actorPb.GetEntityDetailsByActorIdRequest{ActorId: txn.GetOtherActorId()})
		if te := epifigrpc.RPCError(getEntityDetailsRes, err); te != nil {
			logger.Error(ctx, "error in fetching entity details by actor id", zap.Error(te),
				zap.String(logger.SECONDARY_ACTOR_ID, txn.GetOtherActorId()))
			continue
		}
		if fetchTxnDisplayCategories {
			txnDisplayCategories, err = s.fetchDisplayCategoriesForTxn(ctx, txn.GetTransactionId(), actorId, reqHeader)
			if err != nil {
				logger.Error(ctx, "error in fetching display categories for txn", zap.Error(err))
			}
		}
		if getEntityDetailsRes.GetProfileImageUrl() == "" {
			actorBgColour = actorPb.GetColourCodeForActor(txn.GetOtherActorId())
		}
		feTransaction := &ffPb.Transaction{
			TransactionId:      txn.GetTransactionId(),
			DestinationIconUrl: getEntityDetailsRes.GetProfileImageUrl(),
			DestinationName:    getEntityDetailsRes.GetName().ToSentenceCaseString(),
			Amount:             types.GetFromBeMoney(txn.GetAmount()),
			TransferType:       txnType,
			TransactionDate: datetime.DateToString(datetime.TimestampToDateInLoc(txn.GetTransactionTimestamp(),
				datetime.IST), internal.TxnTimeLayout, datetime.IST),
			TransactionDetails: &deeplink.Cta{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_TRANSACTION_RECEIPT,
					ScreenOptions: &deeplink.Deeplink_CreditCardTransactionReceiptScreenOptions{
						CreditCardTransactionReceiptScreenOptions: &deeplink.CreditCardTransactionReceiptScreenOptions{
							TxnId: txn.GetTransactionId(),
						},
					},
				},
			},
			TxnTime:              txn.GetTransactionTimestamp(),
			TxnStatusIconUrl:     iconUrl,
			ActorBgColour:        actorBgColour,
			TxnDisplayCategories: txnDisplayCategories,
		}

		if externalTxnIdToRewardInfoMap != nil {
			rewardInfo, ok := externalTxnIdToRewardInfoMap[txn.GetExternalTxnId()]
			if !ok {
				logger.Error(ctx, "reward info not found for txn", zap.String(logger.EXTERNAL_ID, txn.GetExternalTxnId()))
			}
			feTransaction.RewardsInfo = rewardInfo
		}

		feTransactions = append(feTransactions, feTransaction)
	}
	return feTransactions
}

// getRewardsMultiplierInfoOverABillingCycle fetches the map of  bill id vs rewards generated for that particular bill's cycle
// This will fetch rewards on an aggregate level .
func (s *Service) getRewardsMultiplierInfoOverABillingCycle(ctx context.Context, billIds []string, rewardsOfferType rewardsPb.RewardOfferType, rewardHeaderIcon, rewardHeaderTitle string) (map[string]*ffPb.RewardsInfoWithHeaderIcon, error) {
	rewardsResp, err := s.rewardsClient.GetCreditCardLinkedRewardDetails(ctx, &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
		RefIds:          billIds,
		RewardOfferType: rewardsOfferType,
	})
	if te := epifigrpc.RPCError(rewardsResp, err); te != nil {
		return nil, pkgErr.Wrap(te, "error in fetching reward offers ")
	}
	rewardUnitsMap := make(map[string]float32)
	rewardMap := make(map[string]*ffPb.RewardsInfoWithHeaderIcon)
	for _, rewardDetail := range rewardsResp.GetDetailsList() {
		rewardUnits := float32(0)
		var rewardStatus rewardsPb.RewardStatus
		refId := ""
		switch rewardDetail.GetDetails().(type) {
		case *rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails:
			rewardUnits = -rewardDetail.GetRewardClawbackDetails().GetClawedBackRewardUnits()
			refId = rewardDetail.GetRewardClawbackDetails().GetClawbackRefId()
			rewardStatus = rewardsPb.RewardStatus_CLAWED_BACK
		case *rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails:
			rewardUnits = rewardDetail.GetRewardDetails().GetRewardUnits()
			refId = rewardDetail.GetRewardDetails().GetRefId()
			rewardStatus = rewardDetail.GetRewardDetails().GetStatus()
		}
		if rewardUnits == 0 {
			continue
		}
		rewardUnitsNetSoFar, ok := rewardUnitsMap[refId]
		if ok {
			rewardUnits += rewardUnitsNetSoFar
		}
		rewardUnitsMap[refId] = rewardUnits
		imageUrl := internal.FiCoinInProgressIcon
		if rewardStatus == rewardsPb.RewardStatus_PROCESSED || rewardStatus == rewardsPb.RewardStatus_CLAWED_BACK {
			imageUrl = internal.FiCoinSuccessIcon
		}
		sign := "+"
		if rewardUnits < 0 {
			sign = "-"
		}
		rewardUnitStr := sign + moneyPb.ToDisplayStringFromFloatValue(float32(math.Abs(float64(rewardUnits))), 0)
		rewardMap[refId] = &ffPb.RewardsInfoWithHeaderIcon{
			RewardsInfo: &deeplink.InfoItem{
				Icon:  imageUrl,
				Title: rewardUnitStr,
			},
			HeaderInfoItem: &deeplink.InfoItem{
				Icon:  rewardHeaderIcon,
				Title: rewardHeaderTitle,
			},
		}
	}
	return rewardMap, nil
}

// getRewardsInfo for fetching external txn id to rewards info map
// note that we will have to make two calls to rewards service for fetching the rewards info for normal txns and the
// other one for refund or reversal txns
func (s *Service) getRewardsInfo(ctx context.Context, externalTxnIds []string) (map[string]*ffPb.RewardsInfo, error) {
	var (
		err error
	)

	// fetching rewards for non reversal/refund txns
	getRewardsRes, err := s.rewardsClient.GetCreditCardLinkedRewardDetails(ctx, &rewardsPb.GetCreditCardLinkedRewardDetailsRequest{
		RefIds:          externalTxnIds,
		RewardOfferType: rewardsPb.RewardOfferType_CREDIT_CARD_SPENDS_1X_OFFER,
	})
	if te := epifigrpc.RPCError(getRewardsRes, err); te != nil {
		return nil, fmt.Errorf("error in GetCreditCardLinkedRewardDetails api call %w", te)
	}

	externalTxnIdToRewardsInfo := getExternalTxnIdToRewardsInfoMap(getRewardsRes.GetDetailsList())
	return externalTxnIdToRewardsInfo, nil
}

// getExternalTxnIdToRewardsInfoMap return the rewards amount and image to be shown on the client
func getExternalTxnIdToRewardsInfoMap(rewards []*rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details) map[string]*ffPb.RewardsInfo {
	externalTxnIdToRewardsInfo := make(map[string]*ffPb.RewardsInfo)
	for _, rewardsInfo := range rewards {
		var (
			rewardUnit  float32
			rewardImage string
		)
		rewardType := rewardsPb.RewardType_REWARD_TYPE_UNSPECIFIED
		var rewardStatus rewardsPb.RewardStatus
		var refId string
		switch rewardsInfo.GetDetails().(type) {
		case *rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardDetails:
			rewardUnit = rewardsInfo.GetRewardDetails().GetRewardUnits()
			rewardType = rewardsInfo.GetRewardDetails().GetRewardType()
			rewardStatus = rewardsInfo.GetRewardDetails().GetStatus()
			refId = rewardsInfo.GetRewardDetails().GetRefId()
		case *rewardsPb.GetCreditCardLinkedRewardDetailsResponse_Details_RewardClawbackDetails:
			rewardUnit = -rewardsInfo.GetRewardClawbackDetails().GetClawedBackRewardUnits()
			rewardType = rewardsInfo.GetRewardClawbackDetails().GetRewardType()
			rewardStatus = rewardsPb.RewardStatus_CLAWED_BACK
			refId = rewardsInfo.GetRewardClawbackDetails().GetClawbackRefId()
		}
		switch rewardType {
		case rewardsPb.RewardType_FI_COINS:
			if rewardStatus == rewardsPb.RewardStatus_PROCESSED {
				rewardImage = internal.FiCoinSuccessIcon
			} else {
				rewardImage = internal.FiCoinInProgressIcon
			}
		// TODO(priyansh) : Add reward image for cash once we have it from design
		case rewardsPb.RewardType_CASH:
		default:

		}

		externalTxnIdToRewardsInfo[refId] = &ffPb.RewardsInfo{
			RewardUnits: rewardUnit,
			ImageUrl:    rewardImage,
		}
	}
	return externalTxnIdToRewardsInfo
}

// fetchDisplayCategoriesForTxn returns the list of currently selected display categories for the txn
// the list would contain all the display categories as per identified by the categorizer service and
// the ones explicitly added for the user
func (s *Service) fetchDisplayCategoriesForTxn(ctx context.Context, txnId, actorId string, reqHeader *header.RequestHeader) ([]*ffPb.TransactionDisplayCategory, error) {
	dispCategory, err := s.txnCategorizerClient.GetTxnCategoryDetails(ctx, &categorizerBePb.GetTxnCategoryDetailsRequest{
		ActorId:      actorId,
		Id:           &categorizerBePb.GetTxnCategoryDetailsRequest_TxnId{TxnId: txnId},
		Provenance:   categorizerBePb.Provenance_PROVENANCE_UNSPECIFIED,
		DisplayState: categorizerBePb.DisplayState_DISPLAY_STATE_ENABLED,
		ActivityId: &categorizerBePb.ActivityId{
			Id: &categorizerBePb.ActivityId_FiCardTxnId{
				FiCardTxnId: txnId,
			},
		},
	})
	if te := epifigrpc.RPCError(dispCategory, err); te != nil {
		if dispCategory.GetStatus().IsRecordNotFound() {
			return nil, nil
		}
		return nil, pkgErr.Wrap(te, "error in fetching display categories from txn categorizer")
	}
	dispCategoryArr := make([]categorizerBePb.DisplayCategory, 0)
	for _, ontology := range dispCategory.GetTxnCategories().GetOntologies() {
		dispCategoryArr = append(dispCategoryArr, ontology.GetDisplayCategory())
	}
	if len(dispCategoryArr) == 0 {
		return nil, nil
	}
	txnDisplayCategories, err := s.getDisplayCategories(ctx, dispCategoryArr, reqHeader)
	if err != nil {
		logger.Error(ctx, "error in fetching display categories", zap.Error(err))
		return nil, pkgErr.Wrap(err, "error in fetching display categories")
	}
	return txnDisplayCategories, nil
}

// This function returns a map: transaction ID -> list of display categories for that transaction
func (s *Service) fetchDisplayCategoryDetailsBulk(ctx context.Context, actorId string, minUpdatedAt *timestamppb.Timestamp, reqHeader *header.RequestHeader) (map[string][]*ffPb.TransactionDisplayCategory, error) {
	// get transaction category for each transaction in a list of transactions for an actor
	getTxnCategoryDetailsBulkRes, getTxnCategoryDetailsBulkErr := s.txnCategorizerClient.GetTxnCategoryDetailsBulk(ctx, &categorizerBePb.GetTxnCategoryDetailsBulkRequest{
		ActorId:      actorId,
		DataChannel:  categorizerBePb.DataChannel_DATA_CHANNEL_FI_CARD,
		Provenance:   categorizerBePb.Provenance_PROVENANCE_UNSPECIFIED,
		MinUpdatedAt: minUpdatedAt,
		DisplayState: categorizerBePb.DisplayState_DISPLAY_STATE_ENABLED,
	})
	if te := epifigrpc.RPCError(getTxnCategoryDetailsBulkRes, getTxnCategoryDetailsBulkErr); te != nil {
		if getTxnCategoryDetailsBulkRes.GetStatus().IsRecordNotFound() {
			return nil, nil
		}
		return nil, pkgErr.Wrap(te, "error in bulk fetching display categories")
	}

	// make an empty map of all categories so that each category only appears once(avoiding multiple fetches for the same category) and fetch the display categories
	txnCategorySet := make(map[categorizerBePb.DisplayCategory]struct{})
	for _, txnCategory := range getTxnCategoryDetailsBulkRes.GetTxnCategories() {
		for _, ontology := range txnCategory.GetOntologies() {
			txnCategorySet[ontology.GetDisplayCategory()] = struct{}{}
		}
	}
	txnCategoryArr := make([]categorizerBePb.DisplayCategory, 0, len(txnCategorySet))
	for category := range txnCategorySet {
		txnCategoryArr = append(txnCategoryArr, category)
	}
	if len(txnCategoryArr) == 0 {
		return nil, nil
	}

	// Get a map: category -> display category
	txnDisplayCategories, err := s.getDisplayCategoriesMap(ctx, txnCategoryArr, reqHeader)
	if err != nil {
		return nil, pkgErr.Wrap(err, "error in fetching display categories")
	}

	// We have a map from category string to display category object
	// We'll create a map from transaction ID to display categories
	txnIdToDisplayCategories := make(map[string][]*ffPb.TransactionDisplayCategory)
	for _, txnCategory := range getTxnCategoryDetailsBulkRes.GetTxnCategories() {
		for _, ontology := range txnCategory.GetOntologies() {
			txnIdToDisplayCategories[txnCategory.GetTransactionId()] = append(txnIdToDisplayCategories[txnCategory.GetTransactionId()], txnDisplayCategories[ontology.GetDisplayCategory().String()])
		}
	}
	return txnIdToDisplayCategories, nil
}

// This function processes the display category API response to create & return a map of the display category for each category
// `display category` is the text and icon to be displayed for a category in the transaction receipt & list of transactions. `category` is the category of the transaction.
func (s *Service) getDisplayCategoriesMap(ctx context.Context, displayCategories []categorizerBePb.DisplayCategory, reqHeader *header.RequestHeader) (map[string]*ffPb.TransactionDisplayCategory, error) {
	displayCategoriesFe := make(map[string]*ffPb.TransactionDisplayCategory)
	// convert category to string
	strDisplayCategories := make([]string, 0)
	for _, category := range displayCategories {
		strDisplayCategories = append(strDisplayCategories, category.String())
	}

	// We call GetCategoryDetailsBatch in batches of 100 as that is the limit of the API
	const batchSize = 100
	for start := 0; start < len(strDisplayCategories); start += batchSize {
		end := start + batchSize
		if end > len(strDisplayCategories) {
			end = len(strDisplayCategories)
		}
		batch := strDisplayCategories[start:end]
		// get display category for all categories
		displayCategory, err := s.txnCategorizerFeClient.GetCategoryDetailsBatch(ctx, &categorizerFePb.GetCategoryDetailsBatchRequest{
			Req:             reqHeader,
			DisplayCategory: batch,
		})
		if te := epifigrpc.RPCError(displayCategory.GetRespHeader(), err); te != nil {
			return nil, pkgErr.Wrap(te, "error in fetching display categories in batch")
		}

		// create a map: category -> display category
		for key, category := range displayCategory.GetCategoryDetails() {
			displayCategoriesFe[key] = &ffPb.TransactionDisplayCategory{
				CategoryTitle:          category.GetDisplayName(),
				TxnDisplayCategoryIcon: category.GetIconUrl(),
				CategoryId:             key,
			}
		}
	}
	return displayCategoriesFe, nil
}

func (s *Service) GetTransactionsV2(ctx context.Context, req *ffPb.GetTransactionsV2Request) (*ffPb.GetTransactionsV2Response, error) {
	var (
		res                           = &ffPb.GetTransactionsV2Response{}
		cardResp                      = &ffBePb.GetCreditCardResponse{}
		cardErr                       error
		aggregateRewardAndBillingView []*ffPb.AllTransactionView
		aggregateRewardErr            error
		startTimestamp                *timestamppb.Timestamp
		endTimestamp                  *timestamppb.Timestamp
	)
	beRes, err := s.fireflyAccountingClient.GetPaginatedTxnWithAdditionalInfo(ctx, &ffBeAccountsPb.GetPaginatedTxnWithAdditionalInfoRequest{
		Identifier:  &ffBeAccountsPb.GetPaginatedTxnWithAdditionalInfoRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
		PageContext: req.GetPageContextRequest(),
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in fetching txn with additional info", zap.Error(err), zap.String(logger.ACTOR_ID_V2,
			req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	case !beRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non success status in fetching txn with additional info", zap.Error(err), zap.String(logger.ACTOR_ID_V2,
			req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status: beRes.GetStatus(),
		}
		return res, nil
	default:
		logger.Debug(ctx, "fetched txns with additional info from be", zap.String(logger.ACTOR_ID_V2,
			req.GetReq().GetAuth().GetActorId()))
	}
	startTimestamp, endTimestamp, err = getStartAndEndTimestampForRewards(req.GetPageContextRequest(),
		beRes.GetCardTransactionWithAdditionalInfos())
	if err != nil {
		logger.Error(ctx, "failed to fetch start and end timestamp", zap.Error(err))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}
	cardResp, cardErr = s.fireFlyClient.GetCreditCard(ctx, &ffBePb.GetCreditCardRequest{
		GetBy: &ffBePb.GetCreditCardRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
		SelectFieldMasks: []ffEnumsBePb.CreditCardFieldMask{ffEnumsBePb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ID,
			ffEnumsBePb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
			ffEnumsBePb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACCOUNT_ID},
	})
	if te := epifigrpc.RPCError(cardResp, cardErr); te != nil {
		logger.Error(ctx, "Error fetching card by actor id", zap.Error(te),
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}
	accountResp, accErr := s.fireflyAccountingClient.GetAccount(ctx, &ffBeAccountsPb.GetAccountRequest{
		GetBy: &ffBeAccountsPb.GetAccountRequest_AccountId{AccountId: cardResp.GetCreditCard().GetAccountId()},
	})
	if te := epifigrpc.RPCError(accountResp, accErr); te != nil {
		logger.Error(ctx, "Error fetching account by actor id", zap.Error(te),
			zap.String(logger.ACTOR_ID_V2, req.GetReq().GetAuth().GetActorId()),
			zap.String(logger.ACCOUNT_ID, cardResp.GetCreditCard().GetAccountId()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		aggregateRewardAndBillingView, aggregateRewardErr = s.getAggregatedRewardsAndBillingView(grpCtx,
			req.GetReq().GetAuth().GetActorId(), startTimestamp, endTimestamp,
			cardResp.GetCreditCard().GetId(), cardResp.GetCreditCard().GetBasicInfo().GetBillGenDate(),
			req.GetPageContextRequest(), beRes.GetCardTransactionWithAdditionalInfos())
		if aggregateRewardErr != nil {
			logger.Error(ctx, "error in fetching statement wise aggregated rewards", zap.Error(aggregateRewardErr))
		}
		return nil
	})
	err = grp.Wait()
	if err != nil {
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		return res, nil
	}
	txnView := s.convertBeTxnWithAdditionalInfoToFeAllTransactionView(ctx, beRes.GetCardTransactionWithAdditionalInfos(),
		req.GetReq().GetAuth().GetActorId(), accountResp.GetAccount(), req.GetReq())
	txnView = append(txnView, aggregateRewardAndBillingView...)
	mergedAllTxnView, err := mergeAllTxnViews(req.GetPageContextRequest(), txnView)
	if err != nil {
		logger.Error(ctx, "error in fetching merged all txn view", zap.Error(err))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}
	res.AllTransactionViewList = mergedAllTxnView
	res.PageContextResponse = beRes.GetPageContext()
	res.RespHeader = &header.ResponseHeader{
		Status: rpc.StatusOk(),
	}
	return res, nil
}

// getStartAndEndTimestampForRewards returns the time between which rewards needs to be fetched
func getStartAndEndTimestampForRewards(pageContextRequest *rpc.PageContextRequest,
	txnWithAdditionalInfos []*ffBeAccountsPb.CardTransactionWithAdditionalInfo) (*timestamppb.Timestamp, *timestamppb.Timestamp, error) {
	// if page context request is empty then we are trying to fetch the latest page
	// so the start timestamp will be the timestamp of last txn and end timestamp will be current time
	if pageContextRequest == nil {
		return txnWithAdditionalInfos[len(txnWithAdditionalInfos)-1].GetTransaction().GetTxnTime(), timestamppb.Now(), nil
	}
	token, err := pagination.GetPageToken(pageContextRequest)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to fetch token %w", err)
	}
	if token.IsReverse {
		return txnWithAdditionalInfos[0].GetTransaction().GetTxnTime(), token.GetTimestamp(), nil
	}
	return token.GetTimestamp(), txnWithAdditionalInfos[len(txnWithAdditionalInfos)-1].GetTransaction().GetTxnTime(), nil
}

func (s *Service) getAggregatedRewardsAndBillingView(ctx context.Context, actorId string,
	startTimestamp, endTimestamp *timestamppb.Timestamp, cardId string, billGenDay int32,
	pageContextRequest *rpc.PageContextRequest, txnWithAdditionalInfos []*ffBeAccountsPb.CardTransactionWithAdditionalInfo) ([]*ffPb.AllTransactionView, error) {
	var (
		billIds []string
		views   []*ffPb.AllTransactionView
	)
	billResp, err := s.ffBillingClient.FetchAllBillAndBillPayments(ctx, &ffBeBillingPb.FetchAllBillAndBillPaymentsRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(billResp, err); te != nil {
		return nil, pkgErr.Wrap(te, "error in FetchAllBillAndBillPayments")
	}
	if pageContextRequest == nil {
		// if no bills are present for a user we will show the billing view of the upcoming bill
		if len(billResp.GetCardBills()) == 0 {
			views = append(views, getUpcomingBillView(billGenDay, time.Now().In(datetime.IST)))
		} else {
			lastBillCreatedAtTime := billResp.GetCardBills()[0].GetCreatedAt()
			firstTxnTime := txnWithAdditionalInfos[0].GetTransaction().GetTxnTime()
			// if the latest txn time is post the last bill we will show these txns in the upcoming billing view
			// due to which adding a check here
			if firstTxnTime.AsTime().After(lastBillCreatedAtTime.AsTime()) {
				views = append(views, getUpcomingBillView(billGenDay, firstTxnTime.AsTime()))
			}
		}
	}
	// In case we do not get any bills, we return directly from here to avoid further calls
	if len(billResp.GetCardBills()) == 0 {
		logger.Info(ctx, "no bill found for given actor id: ", zap.String(logger.ACTOR_ID_V2, actorId))
		return views, nil
	}
	for _, bill := range billResp.GetCardBills() {
		// we will only add billing views for bills between the first and last txn time
		if datetime.IsBetweenTimestamp(bill.GetStatementDate().AsTime(),
			startTimestamp.AsTime(), endTimestamp.AsTime()) {
			views = append(views, getBillingView(bill, cardId, billGenDay))
			billIds = append(billIds, bill.GetId())
		}
	}
	billIdToAggregatedReward2X := make(map[string]*ffPb.RewardsInfoWithHeaderIcon)
	billIdToAggregatedReward5X := make(map[string]*ffPb.RewardsInfoWithHeaderIcon)

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		// 5x rewards fetch
		billIdToAggregatedReward5X, err = s.getRewardsMultiplierInfoOverABillingCycle(grpCtx, billIds,
			rewardsPb.RewardOfferType_CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER, "https://epifi-icons.pointz.in/credit_card_images/5x_reward_img.png", "Extra rewards")
		if err != nil {
			logger.Error(ctx, "error in fetching 5x rewards map", zap.Error(err))
			return nil
		}
		return nil
	})
	grp.Go(func() error {
		// 2x rewards fetch
		billIdToAggregatedReward2X, err = s.getRewardsMultiplierInfoOverABillingCycle(grpCtx, billIds,
			rewardsPb.RewardOfferType_CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER, "https://epifi-icons.pointz.in/credit_card_images/2x_reward_img.png", "Extra rewards")
		if err != nil {
			logger.Error(ctx, "error in fetching 2x rewards map", zap.Error(err))
			return nil
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return nil, err
	}
	for _, bill := range billResp.GetCardBills() {
		rewardList := make([]*ffPb.RewardsInfoWithHeaderIcon, 0)
		reward2x := billIdToAggregatedReward2X[bill.GetId()]
		reward5x := billIdToAggregatedReward5X[bill.GetId()]
		if reward5x != nil {
			rewardList = append(rewardList, reward5x)
		}
		if reward2x != nil {
			rewardList = append(rewardList, reward2x)
		}
		if len(rewardList) == 0 {
			continue
		}
		views = append(views, &ffPb.AllTransactionView{
			View:     &ffPb.AllTransactionView_AggregatedRewardsInfoView{AggregatedRewardsInfoView: &ffPb.AggregatedRewardsInfoView{RewardsMultipliers: rewardList}},
			ViewTime: timestamppb.New(bill.GetStatementDate().AsTime().Add(-1 * time.Second)),
		})
	}
	return views, nil
}

func (s *Service) convertBeTxnWithAdditionalInfoToFeAllTransactionView(ctx context.Context, txnWithAdditionalInfos []*ffBeAccountsPb.CardTransactionWithAdditionalInfo, actorId string, creditAccount *ffBeAccountsPb.CreditAccount, reqHeader *header.RequestHeader) []*ffPb.AllTransactionView {
	var (
		txnViewModels []*ffBeAccountsPb.CreditCardTransactionViewModel
		allTxnView    []*ffPb.AllTransactionView
	)
	for _, txnWithAdditionalInfo := range txnWithAdditionalInfos {
		txnViewModels = append(txnViewModels, convertTxnWithAdditionalInfoToViewModel(txnWithAdditionalInfo))
	}

	feTransactions := s.convertBeTransactionToFeTransactionV2(ctx, txnViewModels, actorId, true, creditAccount, reqHeader)
	for _, feTransaction := range feTransactions {
		allTxnView = append(allTxnView, &ffPb.AllTransactionView{
			View:     &ffPb.AllTransactionView_Transaction{Transaction: feTransaction},
			ViewTime: feTransaction.GetTxnTime(),
		})
	}
	return allTxnView
}

func convertTxnWithAdditionalInfoToViewModel(txnWithAdditionalInfo *ffBeAccountsPb.CardTransactionWithAdditionalInfo) *ffBeAccountsPb.CreditCardTransactionViewModel {
	var (
		primaryActorId string
		otherActorId   string
		cardPi         string
		otherPiId      string
	)
	if txnWithAdditionalInfo.GetTransaction().GetTxnType() == ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_CREDIT {
		primaryActorId = txnWithAdditionalInfo.GetAdditionalInfo().GetActorTo()
		otherActorId = txnWithAdditionalInfo.GetAdditionalInfo().GetActorFrom()
		cardPi = txnWithAdditionalInfo.GetAdditionalInfo().GetPiTo()
		otherPiId = txnWithAdditionalInfo.GetAdditionalInfo().GetPiFrom()
	} else {
		primaryActorId = txnWithAdditionalInfo.GetAdditionalInfo().GetActorFrom()
		otherActorId = txnWithAdditionalInfo.GetAdditionalInfo().GetActorTo()
		cardPi = txnWithAdditionalInfo.GetAdditionalInfo().GetPiFrom()
		otherPiId = txnWithAdditionalInfo.GetAdditionalInfo().GetPiTo()
	}
	return &ffBeAccountsPb.CreditCardTransactionViewModel{
		TransactionId:        txnWithAdditionalInfo.GetTransaction().GetId(),
		TransactionTimestamp: txnWithAdditionalInfo.GetTransaction().GetTxnTime(),
		Amount:               txnWithAdditionalInfo.GetTransaction().GetAmount(),
		TransactionStatus:    txnWithAdditionalInfo.GetTransaction().GetTxnStatus(),
		TransactionType:      txnWithAdditionalInfo.GetTransaction().GetTxnType(),
		PrimaryActorId:       primaryActorId,
		OtherActorId:         otherActorId,
		CardPi:               cardPi,
		OtherActorPi:         otherPiId,
		TransactionCategory:  txnWithAdditionalInfo.GetTransaction().GetTxnCategory(),
		TxnDescription:       txnWithAdditionalInfo.GetTransaction().GetDescription(),
		ExternalTxnId:        txnWithAdditionalInfo.GetTransaction().GetExternalTxnId(),
	}
}

func getUpcomingBillView(billGenDay int32, timeToFetch time.Time) *ffPb.AllTransactionView {
	startDate, endDate, _, endTimestamp := fireflyPkg.GetBillingWindowFromTime(billGenDay, timeToFetch)
	billingCycleText := fmt.Sprintf(allTxnBillingCycleLayout, startDate.GetDay(), datetime.DateToString(startDate, "Jan", datetime.IST),
		endDate.GetDay(), datetime.DateToString(endDate, "Jan", datetime.IST))
	return &ffPb.AllTransactionView{
		View: &ffPb.AllTransactionView_BillingWindowView{BillingWindowView: &deeplink.InfoItemWithCtaV2{
			Info: &deeplink.InfoItemV2{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: billingCycleText},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_3},
				},
			},
		}},
		ViewTime: endTimestamp,
	}
}

// getBillingView fetches billing view based on the bill generation day and statement day
func getBillingView(bill *ffBeBillingPb.CreditCardBill, cardId string, billGenDay int32) *ffPb.AllTransactionView {
	startDate, endDate, _, endTimestamp := fireflyPkg.GetBillingWindowFromTime(billGenDay,
		bill.GetStatementDate().AsTime().AddDate(0, 0, -1))
	billingCycleText := fmt.Sprintf(allTxnBillingCycleLayout, startDate.GetDay(), datetime.DateToString(startDate, "Jan", datetime.IST),
		endDate.GetDay(), datetime.DateToString(endDate, "Jan", datetime.IST))
	return &ffPb.AllTransactionView{
		View: &ffPb.AllTransactionView_BillingWindowView{BillingWindowView: &deeplink.InfoItemWithCtaV2{
			Info: &deeplink.InfoItemV2{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: billingCycleText},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_3},
				},
			},
			Cta: &deeplink.Cta{
				Type: deeplink.Cta_CUSTOM,
				Text: viewStatementCtaText,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_STATEMENT_VIEW_SCREEN,
					ScreenOptions: &deeplink.Deeplink_CreditCardStatementViewScreenOptions{CreditCardStatementViewScreenOptions: &deeplink.CreditCardStatementViewScreenOptions{
						StatementDuration: &deeplink.StatementDuration{
							FromDate: types.NewFromBeDate(startDate),
							ToDate:   types.NewFromBeDate(endDate),
						},
						CardId: cardId,
					}},
				},
			},
		}},
		ViewTime: endTimestamp,
	}
}

// mergeAllTxnViews adds date views for each unique date in the txn list and sorts all the views based on the view time
// in descending or ascending order based on the page context request
func mergeAllTxnViews(pageContextRequest *rpc.PageContextRequest, allTxnView []*ffPb.AllTransactionView) ([]*ffPb.AllTransactionView, error) {
	var (
		currentDate *date.Date
	)
	sortedAllTxnView, err := sortAllTxnView(pageContextRequest, allTxnView)
	if err != nil {
		return nil, err
	}
	for _, view := range sortedAllTxnView {
		switch view.GetView().(type) {
		// we will only add date view if the current view is a transaction view
		case *ffPb.AllTransactionView_Transaction:
			txnDate := datetime.TimeToDateInLoc(view.GetTransaction().GetTxnTime().AsTime(), datetime.IST)
			if currentDate == nil || !datetime.DateEquals(txnDate, currentDate) {
				currentDate = datetime.TimeToDateInLoc(view.GetTransaction().GetTxnTime().AsTime(), datetime.IST)
				// we need to show date view at the top after the rewards view
				// TODO(priyansh) : Remove this hack by adding sorting logic to handle priority for each view
				currentDateViewTime := datetime.DateToTimeV2(currentDate, datetime.IST).AddDate(0, 0, 1).Add(-2 * time.Second)
				dateText := fmt.Sprintf(allTxnDateLayout, datetime.DateToString(currentDate, "Monday", datetime.IST),
					currentDate.GetDay(), datetime.DateToString(currentDate, "Jan", datetime.IST))
				dateView := &ffPb.AllTransactionView{
					View: &ffPb.AllTransactionView_DateView{
						DateView: &deeplink.InfoItem{
							Title: dateText,
						},
					},
					ViewTime: timestamppb.New(currentDateViewTime),
				}
				allTxnView = append(allTxnView, dateView)
			}
		}
	}
	// sorting the list after adding date views
	return sortAllTxnView(pageContextRequest, allTxnView)
}

// sortAllTxnView sorts all the txn views based on the page context request
func sortAllTxnView(pageContextRequest *rpc.PageContextRequest, allTxnView []*ffPb.AllTransactionView) ([]*ffPb.AllTransactionView, error) {
	descending := true
	if pageContextRequest != nil {
		token, err := pagination.GetPageToken(pageContextRequest)
		if err != nil {
			return nil, fmt.Errorf("error in fetching page token %w", err)
		}
		if token.IsReverse {
			descending = false
		}
	}
	if descending {
		sort.Slice(allTxnView, func(i, j int) bool {
			return allTxnView[i].GetViewTime().AsTime().After(allTxnView[j].GetViewTime().AsTime())
		})
	} else {
		sort.Slice(allTxnView, func(i, j int) bool {
			return allTxnView[i].GetViewTime().AsTime().Before(allTxnView[j].GetViewTime().AsTime())
		})
	}
	return allTxnView, nil
}
