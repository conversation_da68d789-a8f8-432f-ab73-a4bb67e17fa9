package firefly

import (
	"context"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	actorPb "github.com/epifi/gamma/api/actor"
	ffBeAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffBeAccountsEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	"github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/gamma/api/frontend/deeplink"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	"github.com/epifi/gamma/api/frontend/header"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/firefly/internal"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

const (
	batchSize = 30
)

// rewardsInfoWithEntityType contains the rewardInfo along with rewardEntityType
type rewardsInfoWithEntityType struct {
	rewardInfo       *ffPb.RewardsInfo
	rewardEntityType rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntityType
}

func (s *Service) convertBeTransactionToFeTransactionV2(ctx context.Context, transactions []*ffBeAccountsPb.CreditCardTransactionViewModel, actorId string, fetchTxnDisplayCategories bool, creditAccount *ffBeAccountsPb.CreditAccount, reqHeader *header.RequestHeader) []*ffPb.Transaction {
	var (
		feTransactions               []*ffPb.Transaction
		externalTxnIds               []string
		iconUrl                      string
		actorBgColour                string
		externalTxnIdToRewardInfoMap map[string]*rewardsInfoWithEntityType
		txnIdToDisplayCategoriesMap  = make(map[string][]*ffPb.TransactionDisplayCategory)
		isPostMigrationPhaseEnabled  bool
	)

	isPostMigrationPhaseEnabled = featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled(ctx, &featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest{
		ActorId: actorId,
		ExternalDeps: &common.ExternalDependencies{
			Evaluator: s.releaseEvaluator,
		},
	})

	for _, txn := range transactions {
		externalTxnIds = append(externalTxnIds, txn.GetExternalTxnId())
	}

	// call GetTxnCategoryDetailsBulk() to get a map of txn ID to txn display categories in 1 RPC call for all given transactions
	if fetchTxnDisplayCategories {
		// fetch the earliest created time for all the transactions as GetTxnCategoryDetailsBulk RPC fetches transactions from an actor after that timestamp
		var earliestTxnCreatedTimestamp *timestamppb.Timestamp
		for _, txn := range transactions {
			if earliestTxnCreatedTimestamp == nil || txn.GetTransactionTimestamp().AsTime().Before(earliestTxnCreatedTimestamp.AsTime()) {
				earliestTxnCreatedTimestamp = txn.GetTransactionTimestamp()
			}
		}
		var err error
		txnIdToDisplayCategoriesMap, err = s.fetchDisplayCategoryDetailsBulk(ctx, actorId, earliestTxnCreatedTimestamp, reqHeader)
		if err != nil {
			// if categories are not fetched, then display the transactions without categories, hence this is a non-blocking error
			logger.Error(ctx, "error in fetching display categories for txn", zap.Error(err))
		}
	}

	externalTxnIdToRewardInfoMap = s.getExtTxnIdToRewardsMap(ctx, externalTxnIds, actorId)

	for _, txn := range transactions {
		var (
			txnDisplayCategories []*ffPb.TransactionDisplayCategory
		)
		txnType, ok := beTxnTypeToFeTxnTransferTypeMap[txn.GetTransactionType()]
		if !ok {
			logger.Error(ctx, "error in fetching fe transaction type", zap.String(logger.TXN_TYPE,
				txn.GetTransactionType().String()), zap.String(logger.TXN_ID, txn.GetTransactionId()))
			continue
		}
		switch {
		case txn.GetTransactionStatus() == ffBeAccountsEnumsPb.TransactionStatus_TRANSACTION_STATUS_FAILURE:
			iconUrl = internal.FailureTxnIconUrl
		case txn.GetTransactionType() == ffBeAccountsEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT:
			iconUrl = internal.DebitTxnIconUrl
		default:
			iconUrl = internal.CreditTxnIconUrl
		}
		getEntityDetailsRes, err := s.actorClient.GetEntityDetailsByActorId(ctx,
			&actorPb.GetEntityDetailsByActorIdRequest{ActorId: txn.GetOtherActorId()})
		if te := epifigrpc.RPCError(getEntityDetailsRes, err); te != nil {
			logger.Error(ctx, "error in fetching entity details by actor id", zap.Error(te),
				zap.String(logger.SECONDARY_ACTOR_ID, txn.GetOtherActorId()))
			continue
		}

		if fetchTxnDisplayCategories && txnIdToDisplayCategoriesMap != nil {
			txnDisplayCategories, _ = txnIdToDisplayCategoriesMap[txn.GetTransactionId()]
		}

		if getEntityDetailsRes.GetProfileImageUrl() == "" {
			actorBgColour = actorPb.GetColourCodeForActor(txn.GetOtherActorId())
		}
		feTransaction := &ffPb.Transaction{
			TransactionId:      txn.GetTransactionId(),
			DestinationIconUrl: getEntityDetailsRes.GetProfileImageUrl(),
			DestinationName:    getEntityDetailsRes.GetName().ToSentenceCaseString(),
			Amount:             types.GetFromBeMoney(txn.GetAmount()),
			TransferType:       txnType,
			TransactionDate: datetime.DateToString(datetime.TimestampToDateInLoc(txn.GetTransactionTimestamp(),
				datetime.IST), internal.TxnTimeLayout, datetime.IST),
			TransactionDetails: &deeplink.Cta{
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_TRANSACTION_RECEIPT,
					ScreenOptions: &deeplink.Deeplink_CreditCardTransactionReceiptScreenOptions{
						CreditCardTransactionReceiptScreenOptions: &deeplink.CreditCardTransactionReceiptScreenOptions{
							TxnId: txn.GetTransactionId(),
						},
					},
				},
			},
			TxnTime:              txn.GetTransactionTimestamp(),
			TxnStatusIconUrl:     iconUrl,
			ActorBgColour:        actorBgColour,
			TxnDisplayCategories: txnDisplayCategories,
		}

		if externalTxnIdToRewardInfoMap != nil {
			rewardInfoWithEntityType, ok := externalTxnIdToRewardInfoMap[txn.GetExternalTxnId()]
			if !ok {
				logger.Error(ctx, "reward info not found for txn", zap.String(logger.EXTERNAL_ID, txn.GetExternalTxnId()))
			}

			// determine whether to use fi-coin or fi-point icon considering all the edge cases during migration
			rewardInfoWithEntityType.rewardInfo.ImageUrl = s.getFcFpPostConversionRewardInfoImageUrl(ctx, actorId, txn, rewardInfoWithEntityType, isPostMigrationPhaseEnabled)
			feTransaction.RewardsInfo = rewardInfoWithEntityType.rewardInfo
		}

		feTransactions = append(feTransactions, feTransaction)
	}
	return feTransactions
}

// getFcFpPostConversionRewardInfoImageUrl determines the appropriate reward info image URL based on transaction
// timestamps, feature flags, and billing information for post-conversion logic.
func (s *Service) getFcFpPostConversionRewardInfoImageUrl(ctx context.Context, actorId string, txn *ffBeAccountsPb.CreditCardTransactionViewModel, rewardsInfoWithEntityType *rewardsInfoWithEntityType, isPostMigrationPhaseEnabled bool) string {
	if isPostMigrationPhaseEnabled {
		// 1. If transaction time is post fc-fp migration show fi-points.
		if txn.TransactionTimestamp.AsTime().In(datetime.IST).After(accrualPkg.GetFiCoinsToFiPointsMigrationTime()) {
			return convertToFiPointsIcon(rewardsInfoWithEntityType.rewardInfo.ImageUrl)
		}

		// 2. If the transaction occurred more than one month ( + one day to be on the safer side ) before the Fi Coins to Fi Points migration, display the Fi Coins icon.
		if txn.TransactionTimestamp.AsTime().In(datetime.IST).Before(accrualPkg.GetFiCoinsToFiPointsMigrationTime().AddDate(0, -1, -1)) {
			return rewardsInfoWithEntityType.rewardInfo.ImageUrl
		}

		// 3. If reward was immediately generated and transaction time was before fc-fp migration then display Fi Coins icon.
		if rewardsInfoWithEntityType.rewardEntityType == rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD {
			return rewardsInfoWithEntityType.rewardInfo.ImageUrl
		}

		// 4. If bill ref id not present in the transaction i.e. no bill generated for this transaction then show fi points.
		if txn.GetBillRefId() == "" {
			return convertToFiPointsIcon(rewardsInfoWithEntityType.rewardInfo.ImageUrl)
		}

		// 5. If billing ref id present, try to get the bill
		//    check If bill generated before fc-fp migration show fi coins else show fi points
		getCreditCardBillRes, err := s.ffBillingClient.GetCreditCardBill(ctx, &billing.GetCreditCardBillRequest{
			GetBy: &billing.GetCreditCardBillRequest_BillId{
				BillId: txn.GetBillRefId(),
			},
		})
		if te := epifigrpc.RPCError(getCreditCardBillRes, err); te != nil {
			logger.Error(ctx, "error in fetching bill for transaction", zap.Error(te),
				zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.TXN_ID, txn.GetTransactionId()))
			return rewardsInfoWithEntityType.rewardInfo.ImageUrl
		}

		if getCreditCardBillRes.GetBillWindow().GetToTimestamp().AsTime().In(datetime.IST).Before(accrualPkg.GetFiCoinsToFiPointsMigrationTime()) {
			return rewardsInfoWithEntityType.rewardInfo.ImageUrl
		} else {
			return convertToFiPointsIcon(rewardsInfoWithEntityType.rewardInfo.ImageUrl)
		}
	}

	return rewardsInfoWithEntityType.rewardInfo.ImageUrl
}

func (s *Service) getExtTxnIdToRewardsMap(ctx context.Context, extTxnIds []string, actorId string) map[string]*rewardsInfoWithEntityType {
	externalTxnIdToRewardInfoMap := make(map[string]*rewardsInfoWithEntityType)

	rewardsInfo := s.getRewardRewardsInfoInBatches(ctx, extTxnIds, actorId)
	for extTxnId, rewardInfo := range rewardsInfo {
		var selectedRewardForDisplay, fiCoinUrl = s.getSelectedRewardAndIconFromRewardEntity(rewardInfo)
		if selectedRewardForDisplay != nil && len(selectedRewardForDisplay.GetRewardOptions()) != 0 &&
			selectedRewardForDisplay.GetRewardOptions()[0].GetRewardType() == rewardsPb.RewardType_FI_COINS {
			externalTxnIdToRewardInfoMap[extTxnId] = &rewardsInfoWithEntityType{
				rewardInfo: &ffPb.RewardsInfo{
					RewardUnits: selectedRewardForDisplay.GetRewardOptions()[0].GetUnits(),
					ImageUrl:    fiCoinUrl,
				},
				rewardEntityType: selectedRewardForDisplay.RewardEntityType,
			}
		}
	}
	return externalTxnIdToRewardInfoMap
}

func (s *Service) getRewardRewardsInfoInBatches(ctx context.Context, refIds []string, actorId string) map[string]*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities {
	respMap := make(map[string]*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities)
	batchIdx := 0
	fetchNextBatch := true
	for fetchNextBatch {
		batchStart := batchIdx * batchSize
		batchEnd := min(len(refIds), batchStart+batchSize)
		if batchEnd == len(refIds) {
			fetchNextBatch = false
		}
		rewardResp, err := s.rewardsClient.GetAllRewardsAndProjections(ctx, &rewardsPb.GetAllRewardsAndProjectionRequest{
			ActorId:    actorId,
			ActionType: rewardsPb.CollectedDataType_CREDIT_CARD_TRANSACTION,
			RefIds:     refIds[batchStart:batchEnd],
		})
		if te := epifigrpc.RPCError(rewardResp, err); te != nil {
			logger.Error(ctx, "error in fetching rewards and projections", zap.Error(err))
		} else {
			for key, value := range rewardResp.GetRefIdToRewardEntitiesMap() {
				respMap[key] = value
			}
		}
		batchIdx += 1
	}
	return respMap
}

func (s *Service) getSelectedRewardAndIconFromRewardEntity(rewardInfo *rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntities) (*rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity, string) {
	var (
		projectedReward          *rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity
		actualisedReward         *rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity
		generatedReward          *rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity
		selectedRewardForDisplay *rewardsPb.GetAllRewardsAndProjectionResponse_RewardEntity
		fiCoinUrl                string
	)
	for _, rewardEntity := range rewardInfo.GetRewardEntities() {
		switch rewardEntity.GetRewardEntityType() {
		case rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_ACTUALISED_PROJECTED_REWARD:
			projectedReward = rewardEntity
		case rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_PROJECTED_REWARD:
			actualisedReward = rewardEntity
		case rewardsPb.GetAllRewardsAndProjectionResponse_REWARD_ENTITY_TYPE_GENERATED_REWARD:
			generatedReward = rewardEntity
		}
	}
	if actualisedReward != nil {
		selectedRewardForDisplay = actualisedReward
		fiCoinUrl = internal.FiCoinSuccessIcon
	} else if generatedReward != nil {
		selectedRewardForDisplay = generatedReward
		fiCoinUrl = internal.FiCoinInProgressIcon
	} else if projectedReward != nil {
		selectedRewardForDisplay = projectedReward
		fiCoinUrl = internal.FiCoinInProgressIcon
	}
	return selectedRewardForDisplay, fiCoinUrl
}

func convertToFiPointsIcon(rewardInfoImageUrl string) string {
	switch rewardInfoImageUrl {
	case internal.FiCoinSuccessIcon:
		return internal.FiPointsSuccessIcon
	case internal.FiCoinInProgressIcon:
		return internal.FiPointsProgressIcon
	default:
		return rewardInfoImageUrl
	}
}
