// nolint
package firefly

import (
	"context"
	"fmt"
	"strconv"
	"time"

	pkgErr "github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/accrual"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	ffBePb "github.com/epifi/gamma/api/firefly"
	ffBeAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	ffBeBillingPb "github.com/epifi/gamma/api/firefly/billing"
	ffEnumsBePb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/errors"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/frontend/firefly/enums"
	"github.com/epifi/gamma/api/frontend/header"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/firefly/helper"
	feHelper "github.com/epifi/gamma/frontend/firefly/helper"
	"github.com/epifi/gamma/frontend/firefly/internal"
	ffPkg "github.com/epifi/gamma/pkg/firefly"
	fireflyPkg "github.com/epifi/gamma/pkg/firefly"
)

const (
	openingBalanceStringFormat           = "on %s"
	nextBillGenDateTextFormat            = "Your next statement will be generated on %s"
	exportStatementAcknowledgementFormat = "Your statement will be emailed to %s shortly"
	noStatementAvailableIcon             = "https://epifi-icons.pointz.in/credit_card_images/no_statement_available_icon_4x.png"
)

var (
	statementNotFoundErrorView = &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{BottomSheetErrorView: &errors.BottomSheetErrorView{
			ErrorCode:   "CARD_0013",
			Title:       "Statement Not found",
			Description: "The statement you requested for could not be found.",
		}},
	}
)

// nolint:funlen
func (s *Service) GetStatementDetails(ctx context.Context, req *ffPb.GetStatementDetailsRequest) (*ffPb.GetStatementDetailsResponse, error) {
	var (
		res          = &ffPb.GetStatementDetailsResponse{}
		billInfoResp = &ffBeBillingPb.GetCreditCardBillResponse{}
		billInfoErr  error
		cardInfo     = &ffBePb.GetCreditCardResponse{}
		cardErr      error
		userInfo     = &userPb.GetUserResponse{}
		userErr      error
		accountInfo  = &ffBeAccountsPb.GetAccountResponse{}
		accErr       error
		statements   []*ffBeBillingPb.CreditCardBill
	)
	//Day will be from date's day since to date's day will be one day before statement generation.
	statementDate := &date.Date{
		Year:  req.GetStatementDuration().GetToDate().GetYear(),
		Month: req.GetStatementDuration().GetToDate().GetMonth(),
		Day:   req.GetStatementDuration().GetFromDate().GetDay(),
	}
	actorId := req.GetReq().GetAuth().GetActorId()
	statementDateTimeStamp := datetime.DateToTimestamp(statementDate, datetime.IST)
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*ffPb.GetStatementDetailsResponse, error) {
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		cardInfo, cardErr = s.fireFlyClient.GetCreditCard(grpCtx, &ffBePb.GetCreditCardRequest{
			GetBy: &ffBePb.GetCreditCardRequest_CreditCardId{CreditCardId: req.GetCardId()},
		})
		if te := epifigrpc.RPCError(cardInfo, cardErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching card info").Error())
			return pkgErr.Wrap(te, "error in fetching card info")
		}
		accountInfo, accErr = s.fireflyAccountingClient.GetAccount(grpCtx, &ffBeAccountsPb.GetAccountRequest{
			GetBy: &ffBeAccountsPb.GetAccountRequest_AccountId{AccountId: cardInfo.GetCreditCard().GetAccountId()},
		})
		if te := epifigrpc.RPCError(accountInfo, accErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching acc info").Error())
			return pkgErr.Wrap(te, "error in fetching acc info")
		}
		return nil
	})
	grp.Go(func() error {
		userInfo, userErr = s.userClient.GetUser(grpCtx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
		})
		if te := epifigrpc.RPCError(userInfo, userErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching user info").Error())
			return pkgErr.Wrap(te, "error in fetching user info")
		}
		return nil
	})

	grp.Go(func() error {
		billInfoResp, billInfoErr = s.ffBillingClient.GetCreditCardBill(grpCtx, &ffBeBillingPb.GetCreditCardBillRequest{
			GetBy: &ffBeBillingPb.GetCreditCardBillRequest_ActorIdAndStatementDate_{
				ActorIdAndStatementDate: &ffBeBillingPb.GetCreditCardBillRequest_ActorIdAndStatementDate{
					ActorId:       req.GetReq().GetAuth().GetActorId(),
					StatementDate: statementDateTimeStamp,
				},
			},
		})
		if te := epifigrpc.RPCError(billInfoResp, billInfoErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching bill info").Error())
			if billInfoResp.GetStatus().IsRecordNotFound() {
				// Silently ignore error in case bill is not found. In such cases we fetch the latest bill info
				return nil
			}
			return pkgErr.Wrap(te, "error in fetching bill info")
		}
		return nil
	})
	grp.Go(func() error {
		billRes, billErr := s.ffBillingClient.GetCreditCardBills(ctx, &ffBeBillingPb.GetCreditCardBillsRequest{ActorId: actorId})
		switch {
		case billErr != nil:
			logger.Error(ctx, "error in calling GetCreditCardBills RPC",
				zap.Error(billErr),
				zap.String(logger.ACTOR_ID_V2, actorId))
			return billErr
		case billRes.GetStatus().IsRecordNotFound():
			statements = make([]*ffBeBillingPb.CreditCardBill, 0)
			return nil
		case !billRes.GetStatus().IsSuccess():
			logger.Error(ctx, fmt.Sprintf("[%v] response from GetCreditCardBills", billRes.GetStatus()))
			return pkgErr.Wrap(epifigrpc.RPCError(billRes, billErr), "error in fetching cc bills")
		}
		statements = billRes.GetCreditCardBills()
		return nil
	})
	if err := grp.Wait(); err != nil {
		return responseWithStatus(rpc.StatusInternal(), genericErrorView)
	}

	billInfo := billInfoResp.GetCreditCardBill()
	// in case we were not able to fetch the credit card bill for given duration,
	// we will move to latest bill since this case is expected to happen if latest statement gen fails for a
	// particular reason post statement generation date.
	if billInfo == nil {
		billInfo = statements[0]
		// Subtracting 1 from statement day so that billing window lies in the latest generated statement billing window
		statementEndingTime := datetime.TimestampToTime(billInfo.GetStatementDate()).Add(-24 * time.Hour)
		statementDate := datetime.TimeToDateInLoc(statementEndingTime, datetime.IST)
		billingPeriodStartingDate := billInfo.GetStatementDate().AsTime().AddDate(0, -1, 0)
		_, _, billingDay := billingPeriodStartingDate.Date()
		fromDate, toDate, _, _ := fireflyPkg.GetBillingWindowFromTime(int32(billingDay), datetime.DateToTimeV2(statementDate, datetime.IST))
		req.StatementDuration = &ffPb.StatementDuration{
			FromDate: types.NewFromBeDate(fromDate),
			ToDate:   types.NewFromBeDate(toDate),
		}
	}

	res.StatementBreakupDetails = s.getStatementBreakupDetails(ctx, billInfo, req.GetStatementDuration(), accountInfo.GetAccount())
	res.PartnerBankLogo = internal.PartnerLogoUrl
	res.StatementDurations = getBillingPeriodsFromBills(statements, statementDate)
	res.ExportStatement = s.getExportStatementCta(req.GetStatementDuration(), userInfo.GetUser().GetProfile().GetEmail())
	res.ViewTransactions = s.getViewTransactionsCta(req.GetStatementDuration())
	res.PayBill = s.getPayBillCta(ctx, req.GetReq().GetAuth().GetActorId(), cardInfo.GetCreditCard().GetId())
	res.CreditCardHeader = &ffPb.CreditCardHeader{
		CardProgram:           ffPkg.GetCardProgramStringFromCardProgram(ffPkg.GetCardProgramWithFallback(accountInfo.GetAccount().GetCardProgram())),
		CardProgramAttributes: ffPkg.GetCardProgramAttributes(ffPkg.GetCardProgramWithFallback(accountInfo.GetAccount().GetCardProgram())),
	}
	return responseWithStatus(rpc.StatusOk(), nil)
}

func getBillingPeriodsFromBills(bills []*ffBeBillingPb.CreditCardBill, currentlySelectedStatementDate *date.Date) []*ffPb.StatementDuration {
	durations := make([]*ffPb.StatementDuration, 0)
	for idx, bill := range bills {
		currentStmtFromDate := datetime.TimestampToDateInLoc(timestamp.New(bill.GetStatementDate().AsTime().AddDate(0, -1, 0)), datetime.IST)
		currentStmtToDate := datetime.TimestampToDateInLoc(timestamp.New(bill.GetStatementDate().AsTime().AddDate(0, 0, -1)), datetime.IST)
		if idx > 0 && durations[idx-1].FromDate.Day != currentStmtFromDate.GetDay() {
			currentStmtToDate = types.GetBeDate(&types.Date{
				Year:  durations[idx-1].FromDate.GetYear(),
				Month: durations[idx-1].FromDate.GetMonth(),
				Day:   durations[idx-1].FromDate.GetDay() - 1,
			})
		}
		selected := false
		currentStmtDate := datetime.TimestampToDateInLoc(bill.GetStatementDate(), datetime.IST)
		if datetime.DateEquals(currentStmtDate, currentlySelectedStatementDate) {
			selected = true
		}

		durations = append(durations, &ffPb.StatementDuration{
			FromDate: types.NewFromBeDate(currentStmtFromDate),
			ToDate:   types.NewFromBeDate(currentStmtToDate),
			Selected: selected,
		})
	}
	return durations
}

func (s *Service) ExportStatement(ctx context.Context, req *ffPb.ExportStatementRequest) (*ffPb.ExportStatementResponse, error) {
	var (
		res          = &ffPb.ExportStatementResponse{}
		billInfoResp = &ffBeBillingPb.GetCreditCardBillResponse{}
		billInfoErr  error
		cardInfo     = &ffBePb.GetCreditCardResponse{}
		cardErr      error
		userInfo     = &userPb.GetUserResponse{}
		userErr      error
	)
	// Day will be from date's day since to date's day will be one day before statement generation.
	statementDate := &date.Date{
		Year:  req.GetStatementDuration().GetToDate().GetYear(),
		Month: req.GetStatementDuration().GetToDate().GetMonth(),
		Day:   req.GetStatementDuration().GetFromDate().GetDay(),
	}
	statementDateTimeStamp := datetime.DateToTimestamp(statementDate, datetime.IST)
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*ffPb.ExportStatementResponse, error) {
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		cardInfo, cardErr = s.fireFlyClient.GetCreditCard(grpCtx, &ffBePb.GetCreditCardRequest{
			GetBy: &ffBePb.GetCreditCardRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
		})
		if te := epifigrpc.RPCError(cardInfo, cardErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching card info").Error())
			return pkgErr.Wrap(te, "error in fetching card info")
		}
		return nil
	})
	grp.Go(func() error {
		billInfoResp, billInfoErr = s.ffBillingClient.GetCreditCardBill(grpCtx, &ffBeBillingPb.GetCreditCardBillRequest{
			GetBy: &ffBeBillingPb.GetCreditCardBillRequest_ActorIdAndStatementDate_{
				ActorIdAndStatementDate: &ffBeBillingPb.GetCreditCardBillRequest_ActorIdAndStatementDate{
					ActorId:       req.GetReq().GetAuth().GetActorId(),
					StatementDate: statementDateTimeStamp,
				},
			},
		})
		if te := epifigrpc.RPCError(billInfoResp, billInfoErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching bill info").Error())
			return pkgErr.Wrap(te, "error in fetching bill info")
		}
		return nil
	})
	grp.Go(func() error {
		userInfo, userErr = s.userClient.GetUser(grpCtx, &userPb.GetUserRequest{
			Identifier: &userPb.GetUserRequest_ActorId{ActorId: req.GetReq().GetAuth().GetActorId()},
		})
		if te := epifigrpc.RPCError(userInfo, userErr); te != nil {
			logger.Error(ctx, pkgErr.Wrap(te, "error in fetching user info").Error())
			return pkgErr.Wrap(te, "error in fetching user info")
		}
		return nil
	})
	if err := grp.Wait(); err != nil {
		return responseWithStatus(rpc.StatusFromError(err), exportEmailErrorView)
	}
	initWorkflowResp, initWorkflowErr := s.fireFlyClient.InitiateCardReq(ctx, &ffBePb.InitiateCardReqRequest{
		CardId:              cardInfo.GetCreditCard().GetId(),
		CardRequestWorkFlow: ffEnumsBePb.CardRequestWorkFlow_CARD_REQUEST_WORK_FLOW_TYPE_EXPORT_STATEMENT,
		Provenance:          ffEnumsBePb.Provenance_PROVENANCE_APP,
		RequestData: &ffBePb.InitiateCardReqRequest_ExportStatementData{
			ExportStatementData: &ffBePb.ExportStatementData{
				BillId: billInfoResp.GetCreditCardBill().GetId(),
			}},
	})
	if te := epifigrpc.RPCError(initWorkflowResp, initWorkflowErr); te != nil {
		logger.Error(ctx, pkgErr.Wrap(te, "error in initiating card req workflow").Error())
		return responseWithStatus(rpc.StatusInternal(), exportEmailErrorView)
	}
	res.ExportResponse = s.getExportStatementSuccessResponse(userInfo.GetUser().GetProfile().GetEmail())
	return responseWithStatus(rpc.StatusOk(), nil)
}

// nolint:funlen
func (s *Service) getStatementBreakupDetails(ctx context.Context, bill *ffBeBillingPb.CreditCardBill, stmtDuration *ffPb.StatementDuration, creditAccount *ffBeAccountsPb.CreditAccount) *ffPb.StatementBreakupDetails {
	statementDetails := &ffPb.StatementBreakupDetails{}
	statementDetails.MinimumAmountDueInfo = &ffPb.AmountInfo{
		AmountTitle:   "MINIMUM AMOUNT DUE",
		DisplayAmount: moneyPb.ToDisplayString(bill.GetMinDue()),
		Amount:        types.GetFromBeMoney(bill.GetMinDue()),
	}
	statementDetails.TotalAmountDueInfo = &ffPb.AmountInfo{
		AmountTitle:   "TOTAL AMOUNT DUE",
		DisplayAmount: moneyPb.ToDisplayString(bill.GetTotalDue()),
		Amount:        types.GetFromBeMoney(bill.GetTotalDue()),
	}
	statementDetails.DueAmountInfo = &deeplink.InfoItem{
		Icon:        internal.InfoIconUrl,
		Title:       "Minimum amount due is 5% of the total due + active EMIs. Interest will be charged if your total amount due is not paid.",
		Desc:        "",
		ToolTip:     nil,
		CopyAllowed: false,
	}
	openingDateString := datetime.DateToString(&date.Date{
		Year:  stmtDuration.GetFromDate().GetYear(),
		Month: stmtDuration.GetFromDate().GetMonth(),
		Day:   stmtDuration.GetFromDate().GetDay(),
	}, "02 January 2006", datetime.IST)

	statementDetails.StatementAmountCategories = []*ffPb.StatementAmountDetails{
		// opening balance:
		{
			StatementAmountTitle: "Opening Balance",
			StatementAmountDesc:  fmt.Sprintf(openingBalanceStringFormat, openingDateString),
			Amount:               types.GetFromBeMoney(bill.GetStatementSummary().GetOpeningBalance()),
			DisplayAmount:        moneyPb.ToDisplayString(bill.GetStatementSummary().GetOpeningBalance()),
			AmountCategory:       ffEnumsPb.AmountCategory_AMOUNT_CATEGORY_DEBIT,
		}, // spends
		{
			StatementAmountTitle: "Spends",
			StatementAmountDesc:  "in billing cycle",
			Amount:               types.GetFromBeMoney(bill.GetStatementSummary().GetSpends()),
			DisplayAmount:        moneyPb.ToDisplayString(bill.GetStatementSummary().GetSpends()),
			AmountCategory:       ffEnumsPb.AmountCategory_AMOUNT_CATEGORY_DEBIT,
		},
		// Fees
		{
			StatementAmountTitle: "Fees",
			StatementAmountDesc:  "",
			Amount:               types.GetFromBeMoney(bill.GetStatementSummary().GetFees()),
			DisplayAmount:        moneyPb.ToDisplayString(bill.GetStatementSummary().GetFees()),
			AmountCategory:       ffEnumsPb.AmountCategory_AMOUNT_CATEGORY_DEBIT,
		},
		// Repayments and Refunds
		{
			StatementAmountTitle: "Repayments & Refunds",
			StatementAmountDesc:  "",
			Amount:               types.GetFromBeMoney(bill.GetStatementSummary().GetRepaymentAndRefunds()),
			DisplayAmount:        moneyPb.ToDisplayString(bill.GetStatementSummary().GetRepaymentAndRefunds()),
			AmountCategory:       ffEnumsPb.AmountCategory_AMOUNT_CATEGORY_CREDIT,
		},
		// Spend converted to EMI
		{
			StatementAmountTitle: "Spend converted to EMI",
			StatementAmountDesc:  "",
			Amount:               types.GetFromBeMoney(bill.GetStatementSummary().GetSpendConvertedToEmi()),
			DisplayAmount:        moneyPb.ToDisplayString(bill.GetStatementSummary().GetSpendConvertedToEmi()),
			AmountCategory:       ffEnumsPb.AmountCategory_AMOUNT_CATEGORY_CREDIT,
		},
	}

	statementEndDate := &date.Date{
		Year:  stmtDuration.GetToDate().GetYear(),
		Month: stmtDuration.GetToDate().GetMonth(),
		Day:   stmtDuration.GetFromDate().GetDay(),
	}

	// Add banner notifying fi coins were converted to fi-points if feature flag is enabled and the statement is for a pre-migration month
	if featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled(ctx, &featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest{
		ActorId: creditAccount.GetActorId(),
		ExternalDeps: &common.ExternalDependencies{
			Evaluator: s.releaseEvaluator,
		},
	}) && datetime.DateToTimestamp(statementEndDate, datetime.IST).AsTime().Before(accrual.GetFiCoinsToFiPointsMigrationTime()) {

		statementDetails.Banner = &ui.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(s.dynamicConf.CcStatementBanner().PostConversion.FiPointImageUrl, 24, 24),
			LeftImgTxtPadding: 15,
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyleFontAlignment(s.dynamicConf.CcStatementBanner().PostConversion.Text, "#000000", commontypes.FontStyle_SUBTITLE_S, commontypes.Text_ALIGNMENT_LEFT),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BackgroundColour: widget.GetLinearGradientBackgroundColour(90, []*widget.ColorStop{
					{
						Color:          "#FCF1E4",
						StopPercentage: 0,
					},
					{
						Color:          "#FDDCA8",
						StopPercentage: 100,
					},
				}),
				CornerRadius:  16,
				TopPadding:    s.dynamicConf.CcStatementBanner().PostConversion.TopPadding,
				BottomPadding: s.dynamicConf.CcStatementBanner().PostConversion.BottomPadding,
				LeftPadding:   s.dynamicConf.CcStatementBanner().PostConversion.LeftPadding,
				RightPadding:  s.dynamicConf.CcStatementBanner().PostConversion.RightPadding,
			},
		}
	}

	s.populateStatementRewardDetails(bill, creditAccount, statementDetails, statementEndDate)
	return statementDetails
}

func (s *Service) populateStatementRewardDetails(bill *ffBeBillingPb.CreditCardBill, creditAccount *ffBeAccountsPb.CreditAccount, statementDetails *ffPb.StatementBreakupDetails, statementEndDate *date.Date) {
	cardProgram := fireflyPkg.GetCardProgramWithFallback(creditAccount.GetCardProgram())
	switch {
	case cardProgram.GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
		statementDetails.RewardCoins = &deeplink.InfoItem{
			Icon:        accrual.ReturnApplicableValue(internal.FiRewardsIconUrl, internal.FiPointsIconUrl, datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string),
			Title:       accrual.ReturnApplicableValue("Fi-Coins earned", "Fi-Points earned", datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string),
			Desc:        strconv.Itoa(int(bill.GetRewardsInfo().GetSecuredCardsRewardInfo().GetProjectedTotalRewardCoins())),
			CopyAllowed: false,
		}
		statementDetails.SecuredCardRewardsInfo = &ffPb.SecuredCardRewardsInfo{
			RewardTitle: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{
					Html: "Monthly rewards breakdown",
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_2,
				},
			},
			SecuredCardsRewardComponents: []*ffPb.RewardsInformationCategoryBody{
				{
					PrimaryVisualElement: commontypes.GetVisualElementImageFromUrl(ffPkg.GetSecuredCardsWeekdayRewardsDashboardIconUrl()).
						WithImageType(commontypes.ImageType_PNG).
						WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}),
					SecondaryVisualElement: commontypes.GetVisualElementImageFromUrl(accrual.ReturnApplicableValue(internal.FiRewardsIconUrl, internal.FiPointsIconUrl, datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string)).
						WithImageType(commontypes.ImageType_PNG).
						WithProperties(&commontypes.VisualElementProperties{Width: 16, Height: 16}),
					PrimaryText: &commontypes.Text{
						DisplayValue: &commontypes.Text_Html{
							Html: "Weekday rewards",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
						},
						FontColor: "#282828",
					},
					SecondaryText: &commontypes.Text{
						FontColor: "#282828",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: fmt.Sprintf("%d", bill.GetRewardsInfo().GetSecuredCardsRewardInfo().GetProjectedWeekdayRewardCoins()),
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_NUMBER_S,
						},
					},
				},
				{
					PrimaryVisualElement: commontypes.GetVisualElementImageFromUrl(ffPkg.GetSecuredCardsWeekendRewardsDashboardIconUrl()).
						WithImageType(commontypes.ImageType_PNG).
						WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}),
					SecondaryVisualElement: commontypes.GetVisualElementImageFromUrl(accrual.ReturnApplicableValue(internal.FiRewardsIconUrl, internal.FiPointsIconUrl, datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string)).
						WithImageType(commontypes.ImageType_PNG).
						WithProperties(&commontypes.VisualElementProperties{Width: 16, Height: 16}),
					PrimaryText: &commontypes.Text{
						DisplayValue: &commontypes.Text_Html{
							Html: "Weekend rewards",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
						},
						FontColor: "#282828",
					},
					SecondaryText: &commontypes.Text{
						FontColor: "#282828",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: fmt.Sprintf("%d", bill.GetRewardsInfo().GetSecuredCardsRewardInfo().GetProjectedWeekendRewardCoins()),
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_NUMBER_S,
						},
					},
				},
			},
			DrawableProperties: &ffPb.DrawableProperties{
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#F6F9FD"},
				},
				CornerProperty: &ffPb.CornerProperty{
					TopStartCornerRadius: 12,
					TopEndCornerRadius:   12,
					BottomStartCorner:    12,
					BottomEndCorner:      12,
				},
			},
		}
		return
	case fireflyPkg.IsCreditCardProgramMassUnsecured(creditAccount.GetCardProgram()):
		statementDetails.RewardCoins = &deeplink.InfoItem{
			Icon:        accrual.ReturnApplicableValue(internal.FiRewardsIconUrl, internal.FiPointsIconUrl, datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string),
			Title:       accrual.ReturnApplicableValue("Fi-Coins earned", "Fi-Points earned", datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string),
			Desc:        strconv.Itoa(int(bill.GetRewardsInfo().GetRewardsConstructInfo().GetProjectedTotalRewardCoins())),
			ToolTip:     nil,
			CopyAllowed: false,
		}
		statementDetails.MerchantRewards = &ffPb.MerchantRewardsInfo{
			RewardTitle: "Monthly rewards breakdown",
			MerchantWiseRewards: []*ffPb.MerchantRewardsInfo_MerchantWiseReward{
				{
					MerchantName: "1x Rewards",
					MerchantLogo: internal.MassUnsecuredBaseRewardsIconUrl,
					RewardsLogo:  accrual.ReturnApplicableValue(internal.FiRewardsIconUrl, internal.FiPointsIconUrl, datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string),
					RewardCoins:  bill.GetRewardsInfo().GetRewardsConstructInfo().GetProjectedBaseRewardCoins(),
				},
				{
					MerchantName: "4x Rewards",
					MerchantLogo: internal.MassUnsecuredAcceleratedRewardsIconUrl,
					RewardsLogo:  accrual.ReturnApplicableValue(internal.FiRewardsIconUrl, internal.FiPointsIconUrl, datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string),
					RewardCoins:  bill.GetRewardsInfo().GetRewardsConstructInfo().GetProjectedAcceleratedRewardCoins(),
				},
			},
		}
		return

	default:
	}
	// Reward Coins Info:
	statementDetails.RewardCoins = &deeplink.InfoItem{
		Icon:        accrual.ReturnApplicableValue(internal.FiRewardsIconUrl, internal.FiPointsIconUrl, datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string),
		Title:       accrual.ReturnApplicableValue("Fi-Coins earned", "Fi-Points earned", datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string),
		Desc:        strconv.Itoa(int(bill.GetRewardsInfo().GetExtraRewardsConstructInfo().GetTotalRewardCoins())),
		ToolTip:     nil,
		CopyAllowed: false,
	}
	if len(bill.GetRewardsInfo().GetTopMerchantRewards()) > 0 {
		statementDetails.MerchantRewards = &ffPb.MerchantRewardsInfo{
			RewardTitle:         rewardTypeToDisplayStringMap[bill.GetRewardsInfo().GetTopMerchantRewards()[0].GetMerchantRewardType()],
			MerchantWiseRewards: s.getMerchantWiseRewards(bill.GetRewardsInfo().GetTopMerchantRewards(), statementEndDate),
		}
		return
	}
	if !s.dynamicConf.CreditCard().EnableNewCvpForUnsecuredCreditCard() {
		return
	}
	statementDetails.MerchantRewards = &ffPb.MerchantRewardsInfo{
		RewardTitle: "Monthly rewards breakdown",
		MerchantWiseRewards: []*ffPb.MerchantRewardsInfo_MerchantWiseReward{
			{
				MerchantName: "1x Rewards",
				MerchantLogo: internal.MultiplierRewardsIcon1x,
				RewardsLogo:  accrual.ReturnApplicableValue(internal.FiRewardsIconUrl, internal.FiPointsIconUrl, datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string),
				RewardCoins:  bill.GetRewardsInfo().GetExtraRewardsConstructInfo().GetTotal_1XRewardsCoins(),
			},
			{
				MerchantName: "5x Rewards",
				MerchantLogo: internal.MultiplierRewardsIcon5x,
				RewardsLogo:  accrual.ReturnApplicableValue(internal.FiRewardsIconUrl, internal.FiPointsIconUrl, datetime.DateToTimestamp(statementEndDate, datetime.IST), true).(string),
				RewardCoins:  bill.GetRewardsInfo().GetExtraRewardsConstructInfo().GetProjected_5XRewardsCoins(),
			},
		},
	}
}

func (s *Service) getMerchantWiseRewards(rewards []*ffBeBillingPb.MerchantRewardsInfo, statementDate *date.Date) []*ffPb.MerchantRewardsInfo_MerchantWiseReward {
	merchantWiseRewards := make([]*ffPb.MerchantRewardsInfo_MerchantWiseReward, 0)
	for _, rewardInfo := range rewards {
		merchantWiseRewards = append(merchantWiseRewards, &ffPb.MerchantRewardsInfo_MerchantWiseReward{
			Amount:       types.GetFromBeMoney(rewardInfo.GetAmountSpent()),
			RewardsLogo:  accrual.ReturnApplicableValue(internal.FiRewardsIconUrl, internal.FiPointsIconUrl, datetime.DateToTimestamp(statementDate, datetime.IST), true).(string),
			RewardCoins:  rewardInfo.GetTotalRewardCoins(),
			MerchantName: ffPkg.GetMerchantDisplayNameByMerchantName(rewardInfo.GetMerchantName()),
			MerchantLogo: ffPkg.GetMerchantLogoByMerchantName(rewardInfo.GetMerchantName()),
		})
	}
	return merchantWiseRewards
}

func (s *Service) getExportStatementCta(stmtDuration *ffPb.StatementDuration, email string) *deeplink.Cta {
	return &deeplink.Cta{
		Text: "Export statement",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_EXPORT_STATEMENT,
			ScreenOptions: &deeplink.Deeplink_CreditCardExportStatementScreenOptions{
				CreditCardExportStatementScreenOptions: &deeplink.CreditCardExportStatementScreenOptions{
					StatementDuration: &deeplink.StatementDuration{
						FromDate: stmtDuration.GetFromDate(),
						ToDate:   stmtDuration.GetToDate(),
					},
					Email: email,
				}},
		},
	}
}

func (s *Service) getViewStatementDeeplinkFromBillingInfo(latestBill *ffBeBillingPb.CreditCardBill, cardId string, nextBillGenDate *date.Date) *deeplink.Deeplink {
	switch {
	// In case latest bill is not generated or the statement summary is nil we return no statements available.
	case latestBill == nil || latestBill.GetStatementDate() == nil:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_SUCCESS_BOTTOM_VIEW_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CreditCardSuccessBottomViewScreenOptions{
				CreditCardSuccessBottomViewScreenOptions: &deeplink.CreditCardSuccessBottomViewScreenOptions{
					CreditCardId: cardId,
					InfoItemWithCta: &deeplink.InfoItemWithCta{
						Info: &deeplink.InfoItem{
							Icon:        noStatementAvailableIcon,
							Title:       "No statements available",
							Desc:        fmt.Sprintf(nextBillGenDateTextFormat, datetime.DateToString(nextBillGenDate, "02 January", datetime.IST)),
							ToolTip:     nil,
							CopyAllowed: false,
						},
						Cta: &deeplink.Cta{
							Type: deeplink.Cta_CUSTOM,
							Text: "Ok, got it",
						},
					},
				},
			},
		}
	default:
		statementDate := latestBill.GetStatementDate()
		fromDate := datetime.TimeToDateInLoc(datetime.TimestampToTime(statementDate).AddDate(0, -1, 0), datetime.IST)
		toDate := datetime.TimeToDateInLoc(datetime.TimestampToTime(statementDate).AddDate(0, 0, -1), datetime.IST)

		return &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_STATEMENT_VIEW_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CreditCardStatementViewScreenOptions{
				CreditCardStatementViewScreenOptions: &deeplink.CreditCardStatementViewScreenOptions{
					StatementDuration: &deeplink.StatementDuration{
						FromDate: feHelper.GetFromBeDate(fromDate),
						ToDate:   feHelper.GetFromBeDate(toDate),
					},
					CardId: cardId,
				}},
		}
	}
}

func (s *Service) getExportStatementSuccessResponse(email string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_SUCCESS_BOTTOM_VIEW_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardSuccessBottomViewScreenOptions{
			CreditCardSuccessBottomViewScreenOptions: &deeplink.CreditCardSuccessBottomViewScreenOptions{
				InfoItemWithCta: &deeplink.InfoItemWithCta{
					Info: &deeplink.InfoItem{
						Title:       "Statement requested",
						Desc:        fmt.Sprintf(exportStatementAcknowledgementFormat, email),
						CopyAllowed: false,
					},
					Cta: &deeplink.Cta{
						Type: deeplink.Cta_CUSTOM,
						Text: "Ok, got it",
					},
				},
			},
		},
	}
}

func (s *Service) getPayBillCta(ctx context.Context, actorId string, cardId string) *deeplink.Cta {
	billRes, _, _, _, err := s.getDashboardBillingInfo(ctx, cardId, actorId)
	if err != nil {
		logger.Error(ctx, "error fetching pay bill cta", zap.Error(err))
	}

	// In case bill is paid, or we are not able to fetch the dashboard billing info,
	//we will redirect user to the dashboard
	if billRes.GetActionCta() == nil || billRes.GetActionCta().GetDeeplink() == nil || err != nil {
		return &deeplink.Cta{
			Text:     "Pay bill",
			Deeplink: helper.GetCreditCardDashboardDeeplink(),
		}
	}
	return &deeplink.Cta{
		Text:     "Pay bill",
		Deeplink: billRes.GetActionCta().GetDeeplink(),
	}
}

func (s *Service) getViewTransactionsCta(stmtDuration *ffPb.StatementDuration) *deeplink.Cta {
	return &deeplink.Cta{
		Text: "View transactions",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_VIEW_ALL_TRANSACTIONS,
			ScreenOptions: &deeplink.Deeplink_CreditCardViewAllTransactionScreenOptions{
				CreditCardViewAllTransactionScreenOptions: &deeplink.CreditCardViewAllTransactionScreenOptions{
					StatementDuration: &deeplink.StatementDuration{
						FromDate: stmtDuration.GetFromDate(),
						ToDate:   stmtDuration.GetToDate(),
					}},
			},
		},
	}
}
