package user

import (
	errorsPkg "github.com/pkg/errors"

	feupionbpb "github.com/epifi/gamma/api/frontend/upi/onboarding"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/frontend/cx"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	bankcustPb "github.com/epifi/gamma/api/bankcust"
	feAccounts "github.com/epifi/gamma/api/frontend/account"
	feConnectedAccountEnums "github.com/epifi/gamma/api/frontend/account/connected_account/enums"

	feConnectedAccPb "github.com/epifi/gamma/api/frontend/connected_account"
	feUpiOnbEnumsPb "github.com/epifi/gamma/api/frontend/upi/onboarding/enums"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	onbScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	"github.com/epifi/gamma/pkg/pay"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	pkgColors "github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	accountPb "github.com/epifi/gamma/api/accounts"
	cardPb "github.com/epifi/gamma/api/card"
	cardProvisioningPb "github.com/epifi/gamma/api/card/provisioning"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	pb "github.com/epifi/gamma/api/frontend/user"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	typesPb "github.com/epifi/gamma/api/typesv2"
	upiDeeplinkPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	bePb "github.com/epifi/gamma/api/user"
	fePkgUpi "github.com/epifi/gamma/frontend/pkg/upi"
)

var (
	connectedAccActionMap = map[feConnectedAccPb.AccountAction]pb.UserAccountAction{
		feConnectedAccPb.AccountAction_ACCOUNT_ACTION_DISCONNECT: pb.UserAccountAction_USER_ACCOUNT_ACTION_DISCONNECT_ACCOUNT,
		feConnectedAccPb.AccountAction_ACCOUNT_ACTION_DELETE:     pb.UserAccountAction_USER_ACCOUNT_ACTION_DELETE_ACCOUNT,
	}
)

// getUpiDetails - fetches the required upi details like vpa and its state based on account id
func (s *Service) getUpiDetails(ctx context.Context, accountId string, accountType accountPb.Type, derivedAccId, actorId string, isPinSet bool) ([]*pb.UpiDetails, error) {
	var (
		upiDetails []*pb.UpiDetails
		piIds      []string
		upiActions []*pb.UpiAction
		vpa        string
	)

	accountPiResp, err := s.accountPiClient.GetByAccountId(ctx, &accountPiPb.GetByAccountIdRequest{
		AccountId:   accountId,
		AccountType: accountType,
	})
	if err = epifigrpc.RPCError(accountPiResp, err); err != nil {
		return nil, fmt.Errorf("error in fetching account PIs using account id : %s, %w", accountId, err)
	}

	for _, accountPi := range accountPiResp.GetAccountPis() {
		piIds = append(piIds, accountPi.GetPiId())
	}

	piResp, err := s.piClient.GetPIsByIds(ctx, &piPb.GetPIsByIdsRequest{Ids: piIds})
	if err = epifigrpc.RPCError(piResp, err); err != nil {
		return nil, fmt.Errorf("error in fetching PI using PI ids : %w", err)
	}

	for _, pi := range piResp.GetPaymentinstruments() {
		if pi.GetType() != piPb.PaymentInstrumentType_UPI || pi.IsMandateVPA() {
			continue
		}
		vpa = pi.GetUpi().GetVpa()
		vpaState, ok := piStateToVpaStateMap[pi.GetState()]
		if !ok {
			continue
		}

		upiActions, err = s.getUpiActions(ctx, pi, derivedAccId, actorId, isPinSet, accountType)
		if err != nil {
			return nil, fmt.Errorf("error in fetching actions for upi pi %s : %w", pi.GetId(), err)
		}

		upiDetails = append(upiDetails, &pb.UpiDetails{
			VpaHandle:  vpa,
			VpaState:   vpaState,
			UpiActions: upiActions,
		})
	}

	return upiDetails, nil
}

// getCardDetails - fetch card details for given account
func (s *Service) getCardDetails(ctx context.Context, accountId string) ([]*pb.BasicCardInfo, error) {
	var (
		basicCardInfo []*pb.BasicCardInfo
	)
	cardReq := &cardProvisioningPb.FetchCardDetailsByAccountIdRequest{
		SavingsAccountId: accountId,
		CardStates:       []cardPb.CardState{cardPb.CardState_CREATED, cardPb.CardState_ACTIVATED, cardPb.CardState_SUSPENDED},
	}
	cardDetails, err := s.cardClient.FetchCardDetailsByAccountId(ctx, cardReq)
	if err != nil {
		logger.Error(ctx, "failed to fetch card details",
			zap.String(logger.ACCOUNT_ID, accountId), zap.Error(err))
		return nil, fmt.Errorf("failed to fetch card details: %s, %w", accountId, err)
	}

	for _, card := range cardDetails.GetCard() {
		basicCardDetails := &pb.BasicCardInfo{
			MaskedCardNumber: card.GetBasicInfo().GetMaskedCardNumber(),
			Expiry:           card.GetBasicInfo().GetExpiry(),
			CardNetworkType:  beToFeCardNetworkTypeMap[card.GetNetworkType()],
			CardId:           card.GetId(),
		}
		basicCardInfo = append(basicCardInfo, basicCardDetails)
	}

	return basicCardInfo, nil
}

// isInternalAccountPrimary - goes through all tpap accounts and checks if any of that is primary, if not, internal account is primary
func (s *Service) isInternalAccountPrimary(ctx context.Context, actorId string) (bool, error) {
	accountsResp, err := s.upiOnboardingClient.GetAccounts(ctx, &upiOnboardingPb.GetAccountsRequest{
		ActorId:       actorId,
		AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE},
	})
	if err = epifigrpc.RPCError(accountsResp, err); err != nil {
		if accountsResp.GetStatus().IsRecordNotFound() {
			return true, nil
		}
		return false, fmt.Errorf("error in fetching tpap accounts using actor id : %s, %w", actorId, err)
	}

	for _, account := range accountsResp.GetAccounts() {
		if account.GetAccountPreference() == upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY {
			return false, nil
		}
	}
	return true, nil
}

// getUpiActions - returns list of upi actions that can be taken for a given upi id
func (s *Service) getUpiActions(ctx context.Context, pi *piPb.PaymentInstrument, derivedAccountId, actorId string, isPinSet bool, accType accountPb.Type) ([]*pb.UpiAction, error) {
	var (
		derivedAccountIdProto = &accountPb.DerivedAccountId{}
	)

	err := idgen.DecodeProtoFromStdBase64(derivedAccountId, derivedAccountIdProto)
	if err != nil {
		logger.Error(ctx, "error while decoding derived account id", zap.Error(err))
		return nil, err
	}

	accountId := derivedAccountIdProto.GetTpapAccountId()
	if accountId == "" {
		accountId = derivedAccountIdProto.GetInternalAccountId()
	}

	upiActions, err := s.getUpiPinActions(ctx, actorId, derivedAccountId, accountId, pi, isPinSet)
	if err != nil {
		return nil, fmt.Errorf("failed to get upi pin set actions: err = %w", err)
	}

	// upi mapper related actions
	if fePkgUpi.IsUpiMapperEnabledForActor(ctx, actorId, s.upiOnboardingClient) && accType != accountPb.Type_CREDIT {
		mapperActions, err := s.getUpiMapperAction(ctx, derivedAccountId, derivedAccountIdProto, pi.GetUpi().GetVpa(), actorId)
		if err != nil {
			return nil, fmt.Errorf("failed to get upi mapper actions: err = %w", err)
		}
		upiActions = append(upiActions, mapperActions...)
	}

	// upi international related actions
	if fePkgUpi.IsUpiInternationalPaymentEnabledForActor(ctx, actorId, s.upiOnboardingClient) && accType != accountPb.Type_CREDIT {
		internationalPaymentAction, err := s.getInternationalPaymentsDetail(ctx, derivedAccountIdProto.GetTpapAccountId())
		if err != nil {
			return nil, fmt.Errorf("error in fetching international payment action,%w", err)
		}
		upiActions = append(upiActions, internationalPaymentAction)
	}

	if fePkgUpi.IsCcLinkingEnabledForActor(ctx, actorId, s.upiOnboardingClient) && accType == accountPb.Type_CREDIT {
		ccUpiActions, err := s.getCreditAccountActions(ctx, derivedAccountId, actorId, derivedAccountIdProto.GetTpapAccountId(), accType)
		if err != nil {
			return nil, fmt.Errorf("failed to get ccUpiActions: err = %w", err)
		}
		upiActions = append(upiActions, ccUpiActions...)
	}

	// we only show balance enquiry option for non Fi accounts
	if derivedAccountIdProto.GetInternalAccountId() == "" {
		balanceEnquiryAction, err := s.getBalanceEnquiryAction(derivedAccountId, accType)
		if err != nil {
			return nil, fmt.Errorf("failed to get balanceEnquiryAction: err = %w", err)
		}
		upiActions = append(upiActions, balanceEnquiryAction)
	}

	return upiActions, nil
}

// getUpiPinActions - decides the upi pin set actions based on type of account (TPAP / NON - TPAP)
// If account is Tpap enabled then user should go via new flow (UpiPinSetOptionsV2)
// else - user should go via old flow (UpiPinSetOptions)
// nolint: dupl
func (s *Service) getUpiPinActions(ctx context.Context, actorId, derivedAccountId, accountId string, pi *piPb.PaymentInstrument, isPinSet bool) ([]*pb.UpiAction, error) {
	if fePkgUpi.IsUpiTpapEnabledForActor(ctx, actorId, s.upiOnboardingClient) {
		return s.getUpiPinSetActionsForTpapAccount(derivedAccountId, pi, isPinSet)
	}
	var (
		upiAction = []*pb.UpiAction{
			{
				Title: s.genConfig.UpiActionParams().ForgotUpiPinTitle(),
				ActionDeeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_UPI_PIN_SETUP,
					ScreenOptions: &deeplinkPb.Deeplink_UpiPinSetupOptions{
						UpiPinSetupOptions: &deeplinkPb.UpiPinSetupOptions{
							NpciFlowType:     typesPb.NpciFlowType_FORGOT_PIN,
							DerivedAccountId: derivedAccountId,
							Vpa:              pi.GetUpi().GetVpa(),
							AccountId:        accountId,
						},
					},
				},
				ActionCta: &pb.UpiAction_IconCta{
					IconCta: &commontypes.Image{
						ImageUrl: s.genConfig.UpiActionParams().ArrowIcon().IconUrl(),
						Height:   s.genConfig.UpiActionParams().ArrowIcon().IconHeight(),
						Width:    s.genConfig.UpiActionParams().ArrowIcon().IconWidth(),
					},
				},
				UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
			},
		}
		changePinFlow = typesPb.NpciFlowType_CHANGE_PIN
	)

	if !isPinSet {
		changePinFlow = typesPb.NpciFlowType_FORGOT_PIN
	}

	upiAction = append(upiAction, &pb.UpiAction{
		Title: s.genConfig.UpiActionParams().ChangeUpiPinTitle(),
		ActionDeeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_UPI_PIN_SETUP,
			ScreenOptions: &deeplinkPb.Deeplink_UpiPinSetupOptions{
				UpiPinSetupOptions: &deeplinkPb.UpiPinSetupOptions{
					NpciFlowType:     changePinFlow,
					DerivedAccountId: derivedAccountId,
					Vpa:              pi.GetUpi().GetVpa(),
					AccountId:        accountId,
				},
			},
		},
		ActionCta: &pb.UpiAction_IconCta{
			IconCta: &commontypes.Image{
				ImageUrl: s.genConfig.UpiActionParams().ArrowIcon().IconUrl(),
				Height:   s.genConfig.UpiActionParams().ArrowIcon().IconHeight(),
				Width:    s.genConfig.UpiActionParams().ArrowIcon().IconWidth(),
			},
		},
		UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
	})

	return upiAction, nil
}

// getUpiPinSetActionsForTpapAccount - gets the deeplinks for Forgot and Change Pin for a tpap account
// nolint:funlen, dupl
func (s *Service) getUpiPinSetActionsForTpapAccount(derivedAccountId string, pi *piPb.PaymentInstrument, isPinSet bool) ([]*pb.UpiAction, error) {
	vendor := commonvgpb.Vendor_FEDERAL_BANK
	// client req id to be passed to the backend
	// from client to store consent corresponding to
	// it, and pass the same to vendor
	// so that we can have a common id throughout
	txnId, err := pay.GenerateVendorRequestId(vendor, paymentPb.PaymentProtocol_UPI)
	if err != nil {
		return nil, fmt.Errorf("error generate vendor req id, err = %w", err)
	}

	var (
		upiAction = []*pb.UpiAction{
			{
				Title: s.genConfig.UpiActionParams().ForgotUpiPinTitle(),
				ActionDeeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_UPI_PIN_SETUP_V2,
					ScreenOptions: &deeplinkPb.Deeplink_UpiPinSetupOptionsV2{
						UpiPinSetupOptionsV2: &deeplinkPb.UpiPinSetupOptionsV2{
							NpciFlowType:     typesPb.NpciFlowType_FORGOT_PIN,
							DerivedAccountId: derivedAccountId,
							Vpa:              pi.GetUpi().GetVpa(),
							ClientReqId:      txnId,
						},
					},
				},
				ActionCta: &pb.UpiAction_IconCta{
					IconCta: &commontypes.Image{
						ImageUrl: s.genConfig.UpiActionParams().ArrowIcon().IconUrl(),
						Height:   s.genConfig.UpiActionParams().ArrowIcon().IconHeight(),
						Width:    s.genConfig.UpiActionParams().ArrowIcon().IconWidth(),
					},
				},
				UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
			},
		}
		changePinFlow = typesPb.NpciFlowType_CHANGE_PIN
	)

	if !isPinSet {
		changePinFlow = typesPb.NpciFlowType_FORGOT_PIN
	}

	upiAction = append(upiAction, &pb.UpiAction{
		Title: s.genConfig.UpiActionParams().ChangeUpiPinTitle(),
		ActionDeeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_UPI_PIN_SETUP_V2,
			ScreenOptions: &deeplinkPb.Deeplink_UpiPinSetupOptionsV2{
				UpiPinSetupOptionsV2: &deeplinkPb.UpiPinSetupOptionsV2{
					NpciFlowType:     changePinFlow,
					DerivedAccountId: derivedAccountId,
					Vpa:              pi.GetUpi().GetVpa(),
					ClientReqId:      txnId,
				},
			},
		},
		ActionCta: &pb.UpiAction_IconCta{
			IconCta: &commontypes.Image{
				ImageUrl: s.genConfig.UpiActionParams().ArrowIcon().IconUrl(),
				Height:   s.genConfig.UpiActionParams().ArrowIcon().IconHeight(),
				Width:    s.genConfig.UpiActionParams().ArrowIcon().IconWidth(),
			},
		},
		UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
	})

	return upiAction, nil
}

// createUpiNumberIntroScreenDeeplink - creates and returns deeplink for upi number introduction screen
func (s *Service) createUpiNumberIntroScreenDeeplink(ctx context.Context, actorId, vpa, derivedAccountId string, beAccountInfo *upiOnboardingPb.AccountInfoForUpiMapper) (*deeplinkPb.Deeplink, error) {
	userResp, err := s.client.GetUser(ctx, &bePb.GetUserRequest{
		Identifier: &bePb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})

	if grpcErr := epifigrpc.RPCError(userResp, err); grpcErr != nil {
		return nil, fmt.Errorf("error fetching user details for given actor id %s %w", actorId, grpcErr)
	}

	createCustomUpiNumberDeeplink, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_CREATE_CUSTOM_UPI_NUMBER_SCREEN, s.getScreenOptionsToCreateCustomUpiNumber(beAccountInfo, vpa))
	if err != nil {
		return nil, fmt.Errorf("error while generating deeplink to create custom upi number : %w", err)
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_UPI_NUMBER_INTRO_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_UpiNumberIntroScreenOptions{
			UpiNumberIntroScreenOptions: &deeplinkPb.UpiNumberIntroScreenOptions{
				LogoUrl: s.genConfig.PayParams().UpiNumberParams().UpiNumberIntroScreenOptions().LogoUrl(),
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: s.genConfig.PayParams().UpiNumberParams().UpiNumberIntroScreenOptions().Title()},
					FontColor:    s.genConfig.PayParams().UpiNumberParams().UpiNumberIntroScreenOptions().ManageUpiNumberIntroTitleColor(),
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
				},
				Description: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(
						s.genConfig.PayParams().UpiNumberParams().UpiNumberIntroScreenOptions().DescriptionPrefix(),
						s.genConfig.PayParams().UpiNumberParams().UpiNumberIntroScreenOptions().ManageUpiNumberIntroDescriptionColor(),
						commontypes.FontStyle_BODY_XS,
					),
					commontypes.GetTextFromStringFontColourFontStyle(
						strconv.FormatUint(userResp.GetUser().GetProfile().GetPhoneNumber().GetNationalNumber(), 10),
						s.genConfig.PayParams().UpiNumberParams().UpiNumberIntroScreenOptions().PhoneNumberTextColor(),
						commontypes.FontStyle_BUTTON_5,
					),
					commontypes.GetTextFromStringFontColourFontStyle(
						s.genConfig.PayParams().UpiNumberParams().UpiNumberIntroScreenOptions().DescriptionSuffix(),
						s.genConfig.PayParams().UpiNumberParams().UpiNumberIntroScreenOptions().ManageUpiNumberIntroDescriptionColor(),
						commontypes.FontStyle_BODY_XS,
					),
				},
				SetUpiNumberCta: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CONTINUE,
					Text:         SetUpiNumber,
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
				},
				OverflowOptions: []*deeplinkPb.OverflowOption{
					{
						ActionType: deeplinkPb.OverflowOption_OVERFLOW_ACTION_CREATE_A_CUSTOM_UPI_NUMBER,
						DisplayText: commontypes.GetTextFromStringFontColourFontStyle(
							CreateCustomUpiNumber,
							s.genConfig.PayParams().UpiNumberParams().UpiNumberIntroScreenOptions().ManageUpiNumberIntroTitleColor(),
							commontypes.FontStyle_BODY_3,
						),
						Deeplink: createCustomUpiNumberDeeplink,
					},
				},
				Vpa:              vpa,
				PhoneNumber:      strconv.FormatUint(userResp.GetUser().GetProfile().GetPhoneNumber().GetNationalNumber(), 10),
				DerivedAccountId: derivedAccountId,
			},
		},
	}, nil
}

// getInternationalPaymentsStatusDetail fetches details of international payment status for an upi account to show to the user
// nolint: funlen, dupl
func (s *Service) getInternationalPaymentsDetail(ctx context.Context, tpapAccountId string) (*pb.UpiAction, error) {

	var (
		err                error
		upiGlobalDetailRes *upiOnboardingPb.GetInternationPaymentDetailsForAccountResponse
	)

	upiAction := &pb.UpiAction{
		Title:    s.config.InternationalPaymentActionParam.InternationalPaymentActionTitle.Content,
		SubTitle: s.config.InternationalPaymentActionParam.InternationalPaymentActionSubtitle.Content,
	}
	upiGlobalDetailRes, err = s.upiOnboardingClient.GetInternationPaymentDetailsForAccount(ctx, &upiOnboardingPb.GetInternationPaymentDetailsForAccountRequest{
		AccountId: tpapAccountId,
	})
	if err = epifigrpc.RPCError(upiGlobalDetailRes, err); err != nil {
		return nil, err
	}

	switch upiGlobalDetailRes.GetIntPaymentStatus() {
	case upiOnboardingEnumsPb.UpiInternationalPaymentStatus_UPI_INTERNATIONAL_PAYMENT_STATUS_ACTIVATED:
		upiAction.UpiActionCta = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(s.config.InternationalPaymentActionParam.InternationalPaymentActivated.Content,
					s.config.InternationalPaymentActionParam.InternationalPaymentActivated.FontColor,
					commontypes.FontStyle(commontypes.FontStyle_value[s.config.InternationalPaymentActionParam.InternationalPaymentActivated.FontStyle])),
			},
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_UPI_INTERNATIONAL_PAYMENT_DEACTIVATION,
			},
		}
		upiAction.ActionCta = &pb.UpiAction_TextCta{
			TextCta: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(s.config.InternationalPaymentActionParam.InternationalPaymentActivated.Content,
						s.config.InternationalPaymentActionParam.InternationalPaymentActivated.FontColor,
						commontypes.FontStyle(commontypes.FontStyle_value[s.config.InternationalPaymentActionParam.InternationalPaymentActivated.FontStyle])),
				},
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_UPI_INTERNATIONAL_PAYMENT_DEACTIVATION,
				},
			},
		}
	case upiOnboardingEnumsPb.UpiInternationalPaymentStatus_UPI_INTERNATIONAL_PAYMENT_STATUS_DEACTIVATED:
		upiAction.UpiActionCta = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(s.config.InternationalPaymentActionParam.InternationalPaymentDeactivated.Content,
					s.config.InternationalPaymentActionParam.InternationalPaymentDeactivated.FontColor,
					commontypes.FontStyle(commontypes.FontStyle_value[s.config.InternationalPaymentActionParam.InternationalPaymentDeactivated.FontStyle])),
			},
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_UPI_INTERNATIONAL_PAYMENT_ACTIVATION,
			},
		}
		upiAction.ActionCta = &pb.UpiAction_TextCta{
			TextCta: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(s.config.InternationalPaymentActionParam.InternationalPaymentDeactivated.Content,
						s.config.InternationalPaymentActionParam.InternationalPaymentDeactivated.FontColor,
						commontypes.FontStyle(commontypes.FontStyle_value[s.config.InternationalPaymentActionParam.InternationalPaymentDeactivated.FontStyle])),
				},
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_UPI_INTERNATIONAL_PAYMENT_ACTIVATION,
				},
			},
		}
	case upiOnboardingEnumsPb.UpiInternationalPaymentStatus_UPI_INTERNATIONAL_PAYMENT_STATUS_ACTIVATING:
		upiAction.ActionTag = commontypes.GetTextFromStringFontColourFontStyle(s.config.InternationalPaymentActionParam.InternationalPaymentActivating.Content,
			s.config.InternationalPaymentActionParam.InternationalPaymentActivating.FontColor, commontypes.FontStyle_FONT_STYLE_UNSPECIFIED)
		upiAction.ActionTag.FontStyle = &commontypes.Text_CustomFontStyle{
			CustomFontStyle: &commontypes.FontStyleInfo{
				FontFamily: s.config.InternationalPaymentActionParam.InternationalPaymentActivating.FontFamily,
			},
		}
		upiAction.ActionTag.BgColor = s.config.InternationalPaymentActionParam.InternationalPaymentActivating.BgColor

	case upiOnboardingEnumsPb.UpiInternationalPaymentStatus_UPI_INTERNATIONAL_PAYMENT_STATUS_DEACTIVATING:
		upiAction.ActionTag = commontypes.GetTextFromStringFontColourFontStyle(s.config.InternationalPaymentActionParam.InternationalPaymentDeactivating.Content,
			s.config.InternationalPaymentActionParam.InternationalPaymentDeactivating.FontColor, commontypes.FontStyle_FONT_STYLE_UNSPECIFIED)
		upiAction.ActionTag.FontStyle = &commontypes.Text_CustomFontStyle{
			CustomFontStyle: &commontypes.FontStyleInfo{
				FontFamily: s.config.InternationalPaymentActionParam.InternationalPaymentDeactivating.FontFamily,
			},
		}
		upiAction.ActionTag.BgColor = s.config.InternationalPaymentActionParam.InternationalPaymentDeactivating.BgColor

	default:
		return nil, fmt.Errorf("wanted status activated/deactivated/deactivating/activating, but got something else")
	}

	return upiAction, nil
}

// getUpiMapperAction - returns action related to upi mapper
// nolint: funlen
func (s *Service) getUpiMapperAction(ctx context.Context, derivedAccountId string, derivedAccountIdProto *accountPb.DerivedAccountId, vpa, actorId string) ([]*pb.UpiAction, error) {
	return []*pb.UpiAction{
		{
			Title: s.genConfig.UpiActionParams().ManageUpiNumbersTitle(),
			ActionDeeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_MANAGE_UPI_SETTINGS,
			},
			SubTitle: s.genConfig.PayParams().UpiNumberParams().ManageUpiNumberActionDescription(),
			ActionCta: &pb.UpiAction_IconCta{
				IconCta: &commontypes.Image{
					ImageUrl: s.genConfig.UpiActionParams().ArrowIcon().IconUrl(),
					Height:   s.genConfig.UpiActionParams().ArrowIcon().IconHeight(),
					Width:    s.genConfig.UpiActionParams().ArrowIcon().IconWidth(),
				},
			},
			UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
		},
	}, nil
}

// nolint:funlen
// getCreditAccountActions - returns action related to cc linked upi account
func (s *Service) getCreditAccountActions(ctx context.Context, derivedAccountId, actorId, accountId string, accType accountPb.Type) ([]*pb.UpiAction, error) {
	var (
		creditAccountActions []*pb.UpiAction
		toggleButtonState    = pb.ToggleCta_TOGGLE_STATE_OFF
		actionCtaState       = pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED
		toggleAction         = feUpiOnbEnumsPb.AccountPreferenceAction_ACCOUNT_PREFERENCE_ACTION_ENABLE
		actionToastMessage   string
		bankName             string
		ifscCode             string
	)

	accountsResp, err := s.upiOnboardingClient.GetAccounts(ctx, &upiOnboardingPb.GetAccountsRequest{
		ActorId:       actorId,
		AccountStatus: []upiOnboardingEnumsPb.UpiAccountStatus{upiOnboardingEnumsPb.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE},
		AccountTypes:  []accountPb.Type{accountPb.Type_CREDIT},
	})
	if err = epifigrpc.RPCError(accountsResp, err); err != nil {
		return nil, fmt.Errorf("error in fetching tpap accounts using actor id : %s, %w", actorId, err)
	}

	for _, account := range accountsResp.GetAccounts() {
		if account.GetAccountPreference() == upiOnboardingEnumsPb.UpiAccountPreference_ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS {
			if account.GetId() == accountId {
				// account preference already set to default merchant payments account
				toggleButtonState = pb.ToggleCta_TOGGLE_STATE_ON
				toggleAction = feUpiOnbEnumsPb.AccountPreferenceAction_ACCOUNT_PREFERENCE_ACTION_DISABLE
			} else {
				// some other account is set as default merchant payments account, so toggle is off and action cta is disabled
				actionCtaState = pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_DISABLED
				actionToastMessage = s.genConfig.UpiActionParams().DefaultMerchantActionDisabledToastMsg()
			}
		}
	}

	defaultMerchantPaymentDeeplink, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_UPDATE_DEFAULT_MERCHANT_PAYMENT_ACCOUNT_SCREEN, &upiDeeplinkPb.UpdateDefaultMerchantPaymentAccountScreenOptions{
		DerivedAccountId: derivedAccountId,
		Action:           toggleAction,
	})
	if err != nil {
		return nil, fmt.Errorf("error creating defaultMerchantPaymentDeeplink for upi action %w", err)
	}

	defaultMerchantPaymentAction := &pb.UpiAction{
		Title: s.genConfig.UpiActionParams().DefaultMerchantPaymentsTitle(),
		ActionCta: &pb.UpiAction_ToggleCta{
			ToggleCta: &pb.ToggleCta{
				ToggleState:    toggleButtonState,
				ToggleDeeplink: defaultMerchantPaymentDeeplink,
			},
		},
		UpiActionCtaState:  actionCtaState,
		ActionToastMessage: actionToastMessage,
	}

	actorActivitiesDeeplink := &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_ALL_TRANSACTIONS_ACCOUNT_FILTER,
		ScreenOptions: &deeplinkPb.Deeplink_AllTransactionsAccountFilterOptions{
			AllTransactionsAccountFilterOptions: &deeplinkPb.AllTransactionsAccountFilterOptions{
				AccountType:      accType,
				DerivedAccountId: derivedAccountId,
			},
		},
	}

	actorActivitiesAction := &pb.UpiAction{
		Title:          s.genConfig.UpiActionParams().ViewCardTransactionsTitle(),
		ActionDeeplink: actorActivitiesDeeplink,
		ActionCta: &pb.UpiAction_IconCta{
			IconCta: &commontypes.Image{
				ImageUrl: s.genConfig.UpiActionParams().ArrowIcon().IconUrl(),
				Height:   s.genConfig.UpiActionParams().ArrowIcon().IconHeight(),
				Width:    s.genConfig.UpiActionParams().ArrowIcon().IconWidth(),
			},
		},
		UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
	}

	creditAccountActions = append(creditAccountActions, actorActivitiesAction, defaultMerchantPaymentAction)

	// ******* Code for Refresh Option for RuPay Accounts ********
	// Since we already have the account list hence not fetching it again, instead iterating over the list
	for _, account := range accountsResp.GetAccounts() {
		if account.GetId() == accountId {
			bankName = account.GetBankName()
			ifscCode = account.GetIfscCode()
			break
		}
	}
	// If we did not find matching account, early return without appending refresh option.
	if bankName == "" || ifscCode == "" {
		logger.Error(ctx, "no matching rupay credit card account found for accountId", zap.String(logger.ACCOUNT_ID, accountId))
		return creditAccountActions, nil
	}
	refreshOptionDeeplink := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LIST_UPI_ACCOUNTS, &upiDeeplinkPb.ListUpiAccountsScreenOptions{
		UpiAccountType: feUpiOnbEnumsPb.UpiAccountType_UPI_ACCOUNT_TYPE_CREDIT_CARD,
		BankInfo: &feupionbpb.BankInfo{
			Name:     bankName,
			IfscCode: ifscCode,
			Logo:     s.getBankLogoFromBankName(bankName),
		},
	})
	refreshOptionAction := &pb.UpiAction{
		Title:          s.genConfig.UpiActionParams().RefreshOptionTitle(),
		ActionDeeplink: refreshOptionDeeplink,
		ActionCta: &pb.UpiAction_IconCta{
			IconCta: &commontypes.Image{
				ImageUrl: s.genConfig.UpiActionParams().ArrowIcon().IconUrl(),
				Height:   s.genConfig.UpiActionParams().ArrowIcon().IconHeight(),
				Width:    s.genConfig.UpiActionParams().ArrowIcon().IconWidth(),
			},
		},
		UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
	}
	// Append refresh option if an account was found
	creditAccountActions = append(creditAccountActions, refreshOptionAction)

	return creditAccountActions, nil
}

// getBalanceEnquiryAction - returns action related to balance enquiry
func (s *Service) getBalanceEnquiryAction(derivedAccountId string, accType accountPb.Type) (*pb.UpiAction, error) {
	title := s.genConfig.UpiActionParams().ViewBalanceTitle()
	if accType == accountPb.Type_CREDIT {
		title = s.genConfig.UpiActionParams().CheckCardBalanceTitle()
	}

	balanceEnquiryDeeplink, err := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_BALANCE_ENQUIRY_SCREEN, &upiDeeplinkPb.BalanceEnquiryScreenOptions{
		DerivedAccountId: derivedAccountId,
	})
	if err != nil {
		return nil, fmt.Errorf("error creating balanceEnquiryDeeplink for upi action %w", err)
	}

	return &pb.UpiAction{
		Title:          title,
		ActionDeeplink: balanceEnquiryDeeplink,
		ActionCta: &pb.UpiAction_IconCta{
			IconCta: &commontypes.Image{
				ImageUrl: s.genConfig.UpiActionParams().ArrowIcon().IconUrl(),
				Height:   s.genConfig.UpiActionParams().ArrowIcon().IconHeight(),
				Width:    s.genConfig.UpiActionParams().ArrowIcon().IconWidth(),
			},
		},
		UpiActionCtaState: pb.UpiActionCtaState_UPI_ACTION_CTA_STATE_ENABLED,
	}, nil
}

// isSalaryRegisteredUser return whether the user is registered for salary program or not
func (s *Service) isSalaryRegisteredUser(ctx context.Context, actorId string) bool {
	salaryRegDetails, rpcErr := s.salaryProgramClient.GetRegistrationDetails(ctx, &beSalaryPb.GetRegistrationDetailsRequest{
		ActorId:  actorId,
		FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if rpcErr = epifigrpc.RPCError(salaryRegDetails, rpcErr); rpcErr != nil {
		// not returning error here since we don't want to block user for this
		logger.Error(ctx, "salaryProgramClient.GetRegistrationDetails call failed", zap.Error(rpcErr))
	}
	return salaryRegDetails.GetRegistrationStatus() == beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED
}

func getSavingsAccountProfileSection() *pb.ProfileSection {
	section := &pb.ProfileSection{
		SubSections: []*pb.ProfileSubSection{
			getInterestCertificateProfileSubSection(),
		},
		Heading: getTextByFontColorAndStyle(ReportsAndDownloadsSavingsAccountSectionHeading, ColorCodeNight, commontypes.FontStyle_HEADLINE_4),
	}

	return section
}

func getInterestCertificateProfileSubSection() *pb.ProfileSubSection {
	return &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle(IntersetCertificateTitle, ColorCodeNight, commontypes.FontStyle_BODY_3),
		Subtitle: getTextByFontColorAndStyle(InterestCertificateSubtitle, ColorCodeGraySteel, commontypes.FontStyle_BODY_3),
		Cta: &deeplinkPb.Cta{
			Type:    deeplinkPb.Cta_CUSTOM,
			Weblink: IntersetCertificateWebLink,
		},
		CustomData: &pb.ProfileSubSection_CopyHelpBox{
			CopyHelpBox: &pb.CopyHelpBox{
				Title: getTextByFontColorAndStyle(IntersetCertificateCopyBoxTitle, ColorCodeNight, commontypes.FontStyle_BODY_3),
				CopyText: &commontypes.Text{
					FontColor: ColorCodeNight,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: fmt.Sprintf("{#%v#}", commontypes.StringFormatter_STRING_FORMATTER_SAVINGS_ACCOUNT_NUMBER.String()),
					},
					FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3},
					StringFormatters: []commontypes.StringFormatter{commontypes.StringFormatter_STRING_FORMATTER_SAVINGS_ACCOUNT_NUMBER},
				},
				BgColor:      ColorCodeIvory,
				MaskCopyText: true,
			},
		},
	}
}

func getReportsAndDownloadsSections() []*pb.ProfileSection {
	interestCertSection := getSavingsAccountProfileSection()

	return []*pb.ProfileSection{
		interestCertSection,
	}
}

// this function returns types.text conponent with the given font color and font style
func getTextByFontColorAndStyle(text, fontColor string, style commontypes.FontStyle) *commontypes.Text {
	return &commontypes.Text{
		FontColor: fontColor,
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: text,
		},
		FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: style},
	}
}

func (s *Service) getMyProfileSections(ctx context.Context, actorId string) ([]*pb.ProfileSection, error) {
	fDetResp, err := s.onboardingClient.GetFeatureDetails(ctx, &onboarding.GetFeatureDetailsRequest{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(fDetResp, err); err != nil {
		logger.Error(ctx, "error in getting feature details", zap.Error(err))
		return nil, err
	}

	switch fDetResp.GetIsFiLiteUser() {
	case true:
		return s.getMyProfileSectionsForFiLite(ctx, actorId), nil
	case false:
		return s.getMyProfileSectionsForSavingsAccountUser(ctx, actorId), nil
	default:
		logger.Error(ctx, "feature type no supported for getting profile page sections")
		return nil, errors.New("feature type not supported")
	}
}

func (s *Service) getMyProfileSectionsForFiLite(ctx context.Context, actorId string) []*pb.ProfileSection {
	subSections := []*pb.ProfileSubSection{
		MyProfileAboutMeSubSection,
		s.getHelpSubSection(ctx, actorId),
		MyProfilePrivacyAndSecuritySubSection,
		MyProfileSettingsSubSection,
	}
	isMcpTotpCodeEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to evaluate if fi mcp totp req is released or not", zap.Error(err))
	}
	if isMcpTotpCodeEnabled {
		subSections = append(subSections, MyProfileMcpCodeSubSection)
	}
	return []*pb.ProfileSection{{SubSections: subSections}}
}

func (s *Service) getHelpSubSection(ctx context.Context, actorId string) *pb.ProfileSubSection {
	releaseConstraint := release.NewCommonConstraintData(types.Feature_FEATURE_IN_APP_ISSUE_REPORTING_FLOW).
		WithActorId(actorId)
	isIssueReportingFlowEnabled, releaseErr := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if releaseErr != nil {
		logger.Error(ctx, "error in release evaluator for feature FEATURE_IN_APP_ISSUE_REPORTING_FLOW", zap.Error(releaseErr))
	}

	deeplink := &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_HELP_MAIN}
	if isIssueReportingFlowEnabled {
		deeplink = cx.GetContactUsDeeplink(ctx)
	}

	releaseConstraint = release.NewCommonConstraintData(types.Feature_FEATURE_CX_NEW_LANDING_PAGE).
		WithActorId(actorId)
	isCXNewLandingPageEnabled, releaseErr := s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
	if releaseErr != nil {
		logger.Error(ctx, "error in release evaluator for feature FEATURE_CX_NEW_LANDING_PAGE", zap.Error(releaseErr))
	}
	if isCXNewLandingPageEnabled {
		deeplink = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_CONTACT_US_LANDING_SCREEN_V2}
	}

	if s.genConfig.UserProfile().HelpSectionFAQ().IsFAQEnabled() {
		isWealthAnalyserUser, err := featureflags.IsWealthAnalyserUser(ctx, &featureflags.IsWealthAnalyserUserRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				OnboardingClient:     s.onboardingClient,
				QuestSdkClient:       s.questSdkClient,
				UserAttributeFetcher: s.userAttrFetcher,
				NetWorthClient:       s.networthClient,
			},
		})
		if err != nil {
			logger.Error(ctx, "failed to check if user is wealth analyser user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		}
		if isWealthAnalyserUser {
			deeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_FAQ_CATEGORY,
				ScreenOptions: &deeplinkPb.Deeplink_FaqCategoryOptions{
					FaqCategoryOptions: &deeplinkPb.FaqCategoryOptions{
						CategoryId: s.genConfig.UserProfile().HelpSectionFAQ().CategoryId(),
					},
				},
			}
		}
	}

	return &pb.ProfileSubSection{
		Title:    getTextByFontColorAndStyle("Help", ColorCodeNight, commontypes.FontStyle_SUBTITLE_S),
		Subtitle: getTextByFontColorAndStyle("For any assistance you need", ColorCodeGraySteel, commontypes.FontStyle_BODY_XS),
		IconUrl:  "https://epifi-icons.pointz.in/onboarding/helpIcon.png",
		Cta: &deeplinkPb.Cta{
			Deeplink: deeplink,
			IconUrl:  ProfileLandingCtaIconUrl,
		},
	}
}

func (s *Service) getMyProfileSectionsForSavingsAccountUser(ctx context.Context, actorId string) []*pb.ProfileSection {
	sections := []*pb.ProfileSection{
		{
			SubSections: []*pb.ProfileSubSection{
				MyProfileSavingsAccountInfoSection,
				MyProfileAboutMeSubSection,
				s.getHelpSubSection(ctx, actorId),
				MyProfilePrivacyAndSecuritySubSection,
				MyProfileSettingsSubSection,
				MyProfileReportsAndDownloadsSubsection,
				MyProfileChequeBookSubSection,
			},
		},
	}
	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConfig.Alfred().EnableAddressUpdate()) {
		sections[0].SubSections = append(sections[0].SubSections, MyProfileAddressUpdateSubSection)
	}

	isUsStocksDocumentsReleased, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_ALFRED_USS_DOCUMENT_REQUEST).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to evaluate if alfred uss tax document req is released or not", zap.Error(err))
	}
	if isUsStocksDocumentsReleased {
		sections[0].SubSections = append(sections[0].GetSubSections(), MyProfileUSStocksSubSection)
	}
	sections[0].SubSections = append(sections[0].GetSubSections(), MyProfileRewardsAndOffersSubSection)
	isMcpTotpCodeEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to evaluate if fi mcp totp req is released or not", zap.Error(err))
	}
	if isMcpTotpCodeEnabled {
		sections[0].SubSections = append(sections[0].GetSubSections(), MyProfileMcpCodeSubSection)
	}
	return sections
}

// getAccountActions - returns user account actions from connected account actions
func (s *Service) getAccountActions(connectedAccActions []*feConnectedAccPb.AccountActionOption) []*pb.UserAccountActionInfo {
	var userAccActions []*pb.UserAccountActionInfo
	for _, connectedAccAction := range connectedAccActions {
		userAccActions = append(userAccActions, &pb.UserAccountActionInfo{
			UserAccountAction:      connectedAccActionMap[connectedAccAction.GetAccountAction()],
			UserAccountActionState: pb.UserAccountActionState_USER_ACCOUNT_ACTION_STATE_ENABLED,
			Deeplink:               connectedAccAction.GetDeeplink(),
		})
	}
	return userAccActions
}

// getBannerForConnectedAccountStatus - returns banner to be shown on account details page based on account status
func (s *Service) getBannerForConnectedAccountStatus(accountStatus feConnectedAccPb.AccountStatus) *ui.IconTextComponent {
	var (
		displayText string
		fontColor   string
		bgColor     string
	)
	switch accountStatus {
	case feConnectedAccPb.AccountStatus_ACCOUNT_STATUS_DATA_SYNC_OFF_STOPPED, feConnectedAccPb.AccountStatus_ACCOUNT_STATUS_DISCONNECTED:
		displayText = ConnectedAccountDisconnectedText
		fontColor = ConnectedAccountDisconnectedFontColor
		bgColor = ConnectedAccountDisconnectedBgColor
	default:
		return nil
	}
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: displayText,
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BODY_3,
				},
				BgColor:   bgColor,
				FontColor: fontColor,
			},
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			TopPadding:    12,
			BottomPadding: 12,
			LeftPadding:   20,
			RightPadding:  20,
		},
	}
}

// Explicitly override account control flags only if in case of account is connected account.
// It is on consumer of user session details RPC to decide what operation has to be performed for these connected accounts
// for e.g. if we explicitly enable connected account txn control flag, its on actor activity service to show whether
// to show transactions of that connected account.
func (s *Service) OverrideAccountControlFlags(accounts []*feAccounts.Account) {
	for _, acc := range accounts {
		for _, caFeature := range acc.GetConnectedAccount().GetFeatures() {
			if caFeature.GetFeature() == feConnectedAccountEnums.ConnectedAccountFeature_CONNECTED_ACCOUNT_FEATURE_TRANSACTIONS {
				caFeature.ControlFlag = types.ControlFlag_CONTROL_FLAG_ENABLE
				caFeature.Title = EmptyString
				caFeature.Body = EmptyString
			}
		}
	}
}

func (s *Service) isFeatureEnabled(ctx context.Context, actorId string, feature types.Feature) (bool, error) {
	isFeatureEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(feature).WithActorId(actorId))
	if err != nil {
		return false, fmt.Errorf("failed to evaluate feature, %w", err)
	}
	return isFeatureEnabled, nil
}

// getScreenOptionsToCreateCustomUpiNumber : generates the screen options for CREATE_CUSTOM_UPI_NUMBER screen
func (s *Service) getScreenOptionsToCreateCustomUpiNumber(beAccountInfo *upiOnboardingPb.AccountInfoForUpiMapper, vpa string) *upiDeeplinkPb.CreateCustomUpiNumberScreenOptions {
	accountDetailConf := s.config.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.AccountDetailInfo
	return &upiDeeplinkPb.CreateCustomUpiNumberScreenOptions{
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			"Create UPI number",
			pkgColors.ColorNight,
			commontypes.FontStyle_SUBTITLE_2),
		AccountInfo: &upiDeeplinkPb.AccountInfo{
			BankIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(s.getBankLogoFromBankName(beAccountInfo.GetBankName()), 32, 32),
			// reusing the similar logic from upi number details screen
			BankAccountInfo: commontypes.GetTextFromStringFontColourFontStyle(s.getBankInfoText(beAccountInfo),
				accountDetailConf.Description[0].FontColour,
				commontypes.FontStyle(commontypes.FontStyle_value[accountDetailConf.Description[0].FontStyle])),
		},
		RulesSection: &upiDeeplinkPb.RulesSection{
			Title: commontypes.GetTextFromStringFontColourFontStyle("Private UPI Number Criteria", pkgColors.ColorNight, commontypes.FontStyle_SUBTITLE_2),
			Rules: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle("Should not start with '0'", pkgColors.ColorSlate, commontypes.FontStyle_BODY_4),
				commontypes.GetTextFromStringFontColourFontStyle("Should not have all same digits", pkgColors.ColorSlate, commontypes.FontStyle_BODY_4),
				commontypes.GetTextFromStringFontColourFontStyle("Should not end with same three digits", pkgColors.ColorSlate, commontypes.FontStyle_BODY_4),
				commontypes.GetTextFromStringFontColourFontStyle("Must be 8-9 digits", pkgColors.ColorSlate, commontypes.FontStyle_BODY_4),
				commontypes.GetTextFromStringFontColourFontStyle("Should not have serial ascending or descending order. For example: ********, *********", pkgColors.ColorSlate, commontypes.FontStyle_BODY_4),
			},
		},
		Vpa: vpa,
	}
}

// getBankInfoText generates bank info in the form: `Fi Account 4656`
func (s *Service) getBankInfoText(accountInfo *upiOnboardingPb.AccountInfoForUpiMapper) string {
	trimmedAccountNumber := ".." + accountInfo.GetMaskedAccountNumber()[len(accountInfo.GetMaskedAccountNumber())-4:]
	return strings.Join([]string{
		accountInfo.GetBankName(),
		s.config.PayParams.UpiNumberParams.ManageUpiNumberScreenOptions.AccountDetailInfo.Description[0].DisplayValue,
		trimmedAccountNumber}, " ")
}

func (s *Service) getBankLogoFromBankName(bankName string) string {
	bankLogoUrl, ok := s.genConfig.BankNameToLogoUrlMap()[bankName]
	if !ok {
		return s.genConfig.BankIcon().DefaultBankLogoUrl
	}
	return bankLogoUrl
}

func (s *Service) getMapAddressPointScreenDeeplink() *deeplinkPb.Deeplink {
	screenOptions := deeplinkv3.GetScreenOptionV2WithoutError(&onbScreenTypes.MapAddressPointerScreenOptions{
		SearchPlaceholder: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Search for apartment, area",
			},
		},
		MapPinBubble: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor: "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Your card will be delivered here",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#00B899",
				CornerRadius:  10,
				LeftPadding:   28,
				RightPadding:  28,
				TopPadding:    8,
				BottomPadding: 8,
			},
		},
		PrimaryActionCta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CONTINUE,
			Text:         "Confirm location",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		},
		LatLng:     nil,
		BoundRange: 5000,
		// We need to send
	})

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_MAP_ADDRESS_POINTER_SCREEN,
		ScreenOptionsV2: screenOptions,
	}
}

func (s *Service) getAddAddressDetailsScreenDeeplink(ctx context.Context, actorId string, addressType types.AddressType, postalAddress *types.PostalAddress, locationToken string) (*deeplinkPb.Deeplink, error) {
	var (
		addressTypeToSubTextMap = map[types.AddressType]string{
			types.AddressType_CREDIT_CARD_SHIPPING: "Your Fi-Federal Co-branded Credit Card will \nbe delivered to this address",
		}
	)

	bcRes, rpcErr := s.bcClient.GetBankCustomer(ctx, &bankcustPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcustPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if err := epifigrpc.RPCError(bcRes, rpcErr); err != nil {
		logger.Error(ctx, "error fetching bank customer", zap.Error(err))
		return nil, fmt.Errorf("error fetching bank customer: %w", err)
	}

	screenOptions := deeplinkv3.GetScreenOptionV2WithoutError(&onbScreenTypes.AddNewAddressDetailsScreenOption{
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Add address details",
			},
		},
		Subtitle: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: addressTypeToSubTextMap[addressType],
			},
		},
		Address: &types.PostalAddress{
			PostalCode:         postalAddress.GetPostalCode(),
			Locality:           postalAddress.GetLocality(),
			AdministrativeArea: postalAddress.GetAdministrativeArea(),
			Sublocality:        postalAddress.GetSublocality(),
		},
		IsPincodeEditable: true,
		IsCityEditable:    true,
		IsStateEditable:   true,
		IsCountryEditable: true,
		ProceedCta: &deeplinkPb.Cta{
			Text: "Proceed",
		},
		AddressType:   addressType,
		KycLevel:      types.KYCLevel(bcRes.GetBankCustomer().GetKycInfo().GetKycLevel()),
		LocationToken: locationToken,
	})

	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_ADD_NEW_ADDRESS_DETAILS_SCREEN,
		ScreenOptionsV2: screenOptions,
	}, nil
}

func (s *Service) isUserOnboarded(ctx context.Context, actorId string) (bool, error) {
	getOnboardingDetailsResp, err := s.onboardingClient.GetDetails(ctx, &onboarding.GetDetailsRequest{
		ActorId:    actorId,
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		CachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(getOnboardingDetailsResp, err); rpcErr != nil {
		return false, errorsPkg.Wrap(rpcErr, "error fetching onboarding details")
	}
	return getOnboardingDetailsResp.GetDetails().GetCompletedAt() != nil, nil
}
