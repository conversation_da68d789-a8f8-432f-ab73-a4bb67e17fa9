// nolint
package vkyc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"

	"context"
	"errors"
	"fmt"
	"math"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/async/goroutine"
	goUtils "github.com/epifi/be-common/pkg/go_utils"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	bcPb "github.com/epifi/gamma/api/bankcust"
	panPb "github.com/epifi/gamma/api/pan"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	feVkycDl "github.com/epifi/gamma/frontend/kyc/vkyc/deeplink"
	"github.com/epifi/gamma/pkg/feature/release"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"

	"github.com/epifi/gamma/frontend/config/genconf"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feErr "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	feVkycPb "github.com/epifi/gamma/api/frontend/kyc/vkyc"
	kycPb "github.com/epifi/gamma/api/kyc"
	beVkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	location2 "github.com/epifi/gamma/api/user/location"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/kyc/vkyc/vkyc_clients"
)

var (
	vkycClientProcessor = map[deeplinkPb.EntryPoint]vkyc_clients.VKYCClientsInterface{
		deeplinkPb.EntryPoint_ENTRY_POINT_SALARY_PROGRAM: &vkyc_clients.SalaryProgram{
			EntryPoint: deeplinkPb.EntryPoint_ENTRY_POINT_SALARY_PROGRAM,
		},
		deeplinkPb.EntryPoint_ENTRY_POINT_PRE_APPROVED_LOANS: &vkyc_clients.PreApprovedLoans{
			EntryPoint: deeplinkPb.EntryPoint_ENTRY_POINT_PRE_APPROVED_LOANS,
		},
		deeplinkPb.EntryPoint_ENTRY_POINT_UNSPECIFIED: &vkyc_clients.Unspecified{
			EntryPoint: deeplinkPb.EntryPoint_ENTRY_POINT_UNSPECIFIED,
		},
		deeplinkPb.EntryPoint_ENTRY_POINT_REWARDS: &vkyc_clients.Rewards{
			EntryPoint: deeplinkPb.EntryPoint_ENTRY_POINT_REWARDS,
		},
		deeplinkPb.EntryPoint_ENTRY_POINT_SD_GOALS: &vkyc_clients.SDGoals{
			EntryPoint: deeplinkPb.EntryPoint_ENTRY_POINT_SD_GOALS,
		},
		deeplinkPb.EntryPoint_ENTRY_POINT_CHEQUEBOOK: &vkyc_clients.Chequebook{
			EntryPoint: deeplinkPb.EntryPoint_ENTRY_POINT_CHEQUEBOOK,
		},
	}
)

type Service struct {
	feVkycPb.UnimplementedVkycServer
	beVkycClient         beVkycPb.VKYCClient
	kycClient            kycPb.KycClient
	onbClient            onbPb.OnboardingClient
	flags                *config.Flags
	genConf              *genconf.Config
	savingsClient        savingsPb.SavingsClient
	locationClient       location2.LocationClient
	conf                 *config.Config
	bcClient             bcPb.BankCustomerServiceClient
	evaluator            release.IEvaluator
	panClient            panPb.PanClient
	accountBalanceClient accountBalancePb.BalanceClient
	timeClient           datetime.Time
	actorClient          actorPb.ActorClient
	userClient           userPb.UsersClient
	userGroupClient      userGroupPb.GroupClient
	abEvaluatorGeneric   *release.ABEvaluator[string]
}

func NewService(beVkycClient beVkycPb.VKYCClient, onbClient onbPb.OnboardingClient, flags *config.Flags, genConf *genconf.Config,
	kycClient kycPb.KycClient, savingsClient savingsPb.SavingsClient, locationClient location2.LocationClient, conf *config.Config,
	bcClient bcPb.BankCustomerServiceClient, evaluator release.IEvaluator, panClient panPb.PanClient, accountBalanceClient accountBalancePb.BalanceClient,
	timeClient datetime.Time, actorClient actorPb.ActorClient, userClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient) *Service {
	return &Service{
		beVkycClient:         beVkycClient,
		onbClient:            onbClient,
		flags:                flags,
		genConf:              genConf,
		kycClient:            kycClient,
		savingsClient:        savingsClient,
		locationClient:       locationClient,
		conf:                 conf,
		bcClient:             bcClient,
		evaluator:            evaluator,
		panClient:            panClient,
		accountBalanceClient: accountBalanceClient,
		timeClient:           timeClient,
		abEvaluatorGeneric:   GetABEvaluatorOfFeature[string](actorClient, userClient, userGroupClient, genConf.VKYC().ABFeatureReleaseConfig(), func(str string) string { return str }),
	}
}

var _ feVkycPb.VkycServer = &Service{}

const MaxInt32 = int32(^uint32(0) >> 1)
const maxLocationRetryAllowed = 3

func (s *Service) InitiateLiveVKYC(ctx context.Context, _ *feVkycPb.InitiateLiveVKYCRequest) (*feVkycPb.InitiateLiveVKYCResponse, error) {
	return &feVkycPb.InitiateLiveVKYCResponse{
		Status: rpcPb.StatusUnimplemented(),
		RespHeader: &header.ResponseHeader{
			Status:    rpcPb.StatusUnimplemented(),
			ErrorView: feErrors.ClientErrAsErrView(ctx, feErrors.UPDATE_APP_FOR_VKYC),
		},
	}, nil
}

func (s *Service) UpdateCallInfo(ctx context.Context, request *feVkycPb.UpdateCallInfoRequest) (*feVkycPb.UpdateCallInfoResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	entryPoint, exist := beVkycPb.EntryPoint_value[request.GetCallInfoMetadata().GetEntryPointStr()]
	if !exist {
		logger.Info(ctx, fmt.Sprintf("incorrect EntryPointStr mentioned in Req %v", request.GetCallInfoMetadata().GetEntryPointStr()))
		entryPoint = int32(getEntryPoint(request.GetCallInfoMetadata().GetEntryPoint()))
	}
	resp, err := s.beVkycClient.UpdateCallInfo(ctx, &beVkycPb.UpdateCallInfoRequest{
		ActorId: actorId,
		CallInfoMetadata: &beVkycPb.VKYCKarzaCallInfoCallMetadata{
			EntryPoint:     beVkycPb.EntryPoint(entryPoint),
			WebviewVersion: request.GetCallInfoMetadata().GetWebviewVersion(),
			BrowserVersion: request.GetCallInfoMetadata().GetBrowserVersion(),
		},
		LocationToken: request.GetReq().GetAuth().GetDevice().GetLocationToken(),
		EntryPointStr: request.GetCallInfoMetadata().GetEntryPointStr(),
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error updating call info for user", zap.Error(rpcErr))
		return getUpateCallInfoResponse(ctx, request, resp)
	}
	return &feVkycPb.UpdateCallInfoResponse{
		Status: rpcPb.StatusOk(),
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		NextAction: resp.GetNextAction(),
	}, nil
}

func getUpateCallInfoResponse(ctx context.Context, req *feVkycPb.UpdateCallInfoRequest, resp *beVkycPb.UpdateCallInfoResponse) (*feVkycPb.UpdateCallInfoResponse, error) {
	ctas := []*feErr.CTA{
		{
			Type:         feErr.CTA_RETRY,
			Text:         errorScreenRetryCta,
			DisplayTheme: feErr.CTA_PRIMARY,
		},
	}
	if resp.GetStatus().GetCode() == uint32(beVkycPb.UpdateCallInfoResponse_EMPTY_LOCATION_TOKEN) {
		retryCount := req.GetRetries() + 1
		// return error view in case client reached max retries
		if retryCount >= maxLocationRetryAllowed {
			return &feVkycPb.UpdateCallInfoResponse{
				RespHeader: &header.ResponseHeader{
					ErrorView: getFullScreenErrorViewDeeplink("", locationUpdateErrorTitle, locationUpdateErrorSubtitle, locationErrorScreenIcon, ctas),
					Status:    rpcPb.NewStatusWithoutDebug(uint32(feVkycPb.UpdateCallInfoResponse_EMPTY_LOCATION_RETRIES_EXHAUST), ""),
				},
			}, nil
		}
		// return error code asking client to retry
		return &feVkycPb.UpdateCallInfoResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.NewStatusWithoutDebug(uint32(feVkycPb.UpdateCallInfoResponse_EMPTY_LOCATION_RETRY), ""),
			},
			Retries: retryCount,
		}, nil
	}
	// return error view in case of user is outside india
	if resp.GetStatus().GetCode() == uint32(beVkycPb.UpdateCallInfoResponse_LOCATION_OUTSIDE_INDIA) {
		return &feVkycPb.UpdateCallInfoResponse{
			RespHeader: &header.ResponseHeader{
				ErrorView: getFullScreenErrorViewDeeplink("", locationOutsideIndiaErrorScreenTitle, fmt.Sprintf(locationOutSideIndiaErrorScreenSubtitle,
					resp.GetStatus().GetShortMessage()), locationErrorScreenIcon, ctas),
				Status: rpcPb.NewStatusWithoutDebug(uint32(feVkycPb.UpdateCallInfoResponse_LOCATION_OUTSIDE_INDIA), ""),
			},
		}, nil
	}
	// return error view in case of BE rpc error
	if resp.GetStatus().GetCode() == uint32(beVkycPb.UpdateCallInfoResponse_LOCATION_UPDATE_ERROR) {
		return &feVkycPb.UpdateCallInfoResponse{
			RespHeader: &header.ResponseHeader{
				ErrorView: getFullScreenErrorViewDeeplink("", locationUpdateErrorTitle, locationUpdateErrorSubtitle, locationErrorScreenIcon, ctas),
				Status:    rpcPb.NewStatusWithoutDebug(uint32(feVkycPb.UpdateCallInfoResponse_LOCATION_UPDATE_ERROR), ""),
			},
		}, nil
	}

	if resp.GetStatus().GetCode() == uint32(beVkycPb.UpdateCallInfoResponse_LOCATION_MISMATCH_WITH_COMMUNICATION_ADDRESS) {
		return &feVkycPb.UpdateCallInfoResponse{
			RespHeader: &header.ResponseHeader{
				ErrorView: getFullScreenErrorViewV2Deeplink(
					commontypes.GetTextFromStringFontColourFontStyle(locationMismatchWithAddressTitle, "#313234", commontypes.FontStyle_HEADLINE_L),
					commontypes.GetTextFromStringFontColourFontStyle(locationMismatchWithAddressSubtitle, "#B2B5B9", commontypes.FontStyle_SUBTITLE_2),
					commontypes.GetVisualElementFromUrlHeightAndWidth(locationMismatchWithAddress, 144, 136),
					nil),
				Status: rpcPb.NewStatusWithoutDebug(uint32(feVkycPb.UpdateCallInfoResponse_LOCATION_MISMATCH_WITH_COMMUNICATION_ADDRESS), ""),
			},
		}, nil
	}
	logger.Error(ctx, "error updating call info for user")
	return &feVkycPb.UpdateCallInfoResponse{Status: rpcPb.StatusInternal()}, nil
}

func getEntryPoint(entryPoint deeplinkPb.VKYCLandingOptions_EntryPoint) beVkycPb.EntryPoint {
	switch entryPoint {
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_VKYC_HOME:
		return beVkycPb.EntryPoint_ENTRY_POINT_VKYC_HOME
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_DEPOSIT_PRECLOSURE:
		return beVkycPb.EntryPoint_ENTRY_POINT_DEPOSIT_PRECLOSURE
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_DEPOSIT_MIN_KYC_COMMS:
		return beVkycPb.EntryPoint_ENTRY_POINT_DEPOSIT_MIN_KYC_COMMS
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_ONBOARDING:
		return beVkycPb.EntryPoint_ENTRY_POINT_ONBOARDING
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_ONBOARDING_LSO:
		return beVkycPb.EntryPoint_ENTRY_POINT_ONBOARDING_LSO
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_ACCOUNT_FREEZE_THRESHOLD_REACH:
		return beVkycPb.EntryPoint_ENTRY_POINT_ACCOUNT_FREEZE_THRESHOLD_REACH
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_SAVINGS_BALANCE_LIMIT_BREACH:
		return beVkycPb.EntryPoint_ENTRY_POINT_SAVINGS_BALANCE_LIMIT_BREACH
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_CREDIT_LIMIT_BREACH:
		return beVkycPb.EntryPoint_ENTRY_POINT_CREDIT_LIMIT_BREACH
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_ONBOARDING_STUDENT:
		return beVkycPb.EntryPoint_ENTRY_POINT_ONBOARDING_STUDENT
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_DEDUPE_PARTIAL_KYC:
		return beVkycPb.EntryPoint_ENTRY_POINT_ONBOARDING_DEDUPE_PARTIAL_KYC
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_CREDIT_CARD:
		return beVkycPb.EntryPoint_ENTRY_POINT_CREDIT_CARD
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_ADD_FUNDS_MIN_KYC_CHECK_FAIL:
		return beVkycPb.EntryPoint_ENTRY_POINT_ADD_FUNDS_MIN_KYC_CHECK_FAIL
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_SD_GOALS:
		return beVkycPb.EntryPoint_ENTRY_POINT_SD_GOALS
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_REWARDS:
		return beVkycPb.EntryPoint_ENTRY_POINT_REWARDS
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_PRE_APPROVED_LOAN:
		return beVkycPb.EntryPoint_ENTRY_POINT_PRE_APPROVED_LOAN
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_FORCE_LSO_USERS:
		return beVkycPb.EntryPoint_ENTRY_POINT_FORCE_LSO_USERS
	case deeplinkPb.VKYCLandingOptions_ENTRY_POINT_SALARY_PROGRAM:
		return beVkycPb.EntryPoint_ENTRY_POINT_SALARY_PROGRAM
	default:
		return beVkycPb.EntryPoint_ENTRY_POINT_UNSPECIFIED
	}
}

func (s *Service) GetAvailableSlots(ctx context.Context, request *feVkycPb.GetAvailableSlotsRequest) (
	*feVkycPb.GetAvailableSlotsResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	startTime := timestamppb.New(time.Now().Add(20 * time.Minute).In(datetime.IST))
	endTime := timestamppb.New(time.Now().AddDate(0, 0, s.genConf.VKYC().SlotDays()).In(datetime.IST))
	resp, err := s.beVkycClient.GetAvailableSlots(ctx, &beVkycPb.GetAvailableSlotsRequest{
		ActorId:   actorId,
		StartTime: startTime,
		EndTime:   endTime,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error getting available slots from vkyc backend service", zap.Error(te))
		return &feVkycPb.GetAvailableSlotsResponse{Status: rpcPb.StatusInternal()}, nil
	}
	slotDetailsList := resp.GetSlotDetailsList()
	uiView := s.convertToUiView(slotDetailsList)
	return &feVkycPb.GetAvailableSlotsResponse{Status: rpcPb.StatusOk(), DateToTimePeriodList: uiView}, nil
}

func (s *Service) BookSlot(ctx context.Context, req *feVkycPb.BookSlotRequest) (*feVkycPb.BookSlotResponse, error) {
	if req.GetEntryPoint() == "" {
		logger.Info(ctx, "Entry point is missing")
	}
	entryPoint := beVkycPb.EntryPoint(beVkycPb.EntryPoint_value[req.GetEntryPoint()])
	bookSlotResp, err := s.beVkycClient.BookSlot(ctx, &beVkycPb.BookSlotRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		SlotDetails: &beVkycPb.SlotDetails{
			SlotId:    req.GetSlotDetails().GetSlotId(),
			StartTime: req.GetSlotDetails().GetStartTime(),
			EndTime:   req.GetSlotDetails().GetEndTime(),
		},
		EntryPoint: entryPoint,
	})
	if te := epifigrpc.RPCError(bookSlotResp, err); te != nil {
		return &feVkycPb.BookSlotResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg(te.Error()),
			},
		}, nil
	}
	return &feVkycPb.BookSlotResponse{
		NextAction: bookSlotResp.GetNextAction(),
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
	}, nil
}

func (s *Service) GetSchedule(ctx context.Context, request *feVkycPb.GetScheduleRequest) (*feVkycPb.GetScheduleResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	getScheduleResp, err := s.beVkycClient.GetSchedule(ctx, &beVkycPb.GetScheduleRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(getScheduleResp, err); te != nil {
		logger.Error(ctx, "error in getting scheduled slot from vkyc backend", zap.Error(te))
		return &feVkycPb.GetScheduleResponse{
			Status: rpcPb.StatusInternalWithDebugMsg("error in getting scheduled slot from vkyc backend"),
		}, nil
	}
	displayText := vkycPkg.GetDayDisplayText(getScheduleResp.GetStartTime()) + " " + vkycPkg.GetSlotDisplayText(getScheduleResp.GetStartTime())
	return &feVkycPb.GetScheduleResponse{Status: rpcPb.StatusOk(), SlotDetails: &feVkycPb.SlotDetails{
		SlotId:      getScheduleResp.GetSlotId(),
		StartTime:   getScheduleResp.GetStartTime(),
		EndTime:     getScheduleResp.GetEndTime(),
		DisplayText: displayText,
		IsAvailable: true,
	}, Weblink: getScheduleResp.GetWeblink(), AttemptId: getScheduleResp.GetAttemptId()}, nil
}

func (s *Service) RegisterUser(ctx context.Context, _ *feVkycPb.RegisterUserRequest) (*feVkycPb.RegisterUserResponse, error) {
	return &feVkycPb.RegisterUserResponse{
		Status: rpcPb.StatusUnimplemented(),
		RespHeader: &header.ResponseHeader{
			Status:    rpcPb.StatusUnimplemented(),
			ErrorView: feErrors.ClientErrAsErrView(ctx, feErrors.UPDATE_APP_FOR_VKYC),
		},
	}, nil
}

func (s *Service) GetCallStatus(ctx context.Context, request *feVkycPb.GetCallStatusRequest) (*feVkycPb.GetCallStatusResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	resp, err := s.beVkycClient.GetCallStatus(ctx, &beVkycPb.GetCallStatusRequest{
		ActorId:   actorId,
		AttemptId: request.GetAttemptId(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error registering customer in onb backend service", zap.Error(te))
		return &feVkycPb.GetCallStatusResponse{Status: rpcPb.StatusInternal()}, nil
	}
	callStatusList := resp.GetCallStatus()
	feCallStatus := s.getFeCallStatus(ctx, callStatusList, request.GetReq().GetAuth().GetDevice().GetModel())
	logger.Info(ctx, "VKYC Fe call Status", zap.Any("feCallStatus", feCallStatus))
	return &feVkycPb.GetCallStatusResponse{Status: rpcPb.StatusOk(), CallStatus: feCallStatus}, nil
}

func (s *Service) GetCallExitRedirectDeeplink(ctx context.Context, req *feVkycPb.GetCallExitRedirectDeeplinkRequest) (*feVkycPb.GetCallExitRedirectDeeplinkResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	callExitRedirectDlResp, respErr := s.beVkycClient.GetCallExitRedirectDeeplink(ctx, &beVkycPb.GetCallExitRedirectDeeplinkRequest{
		ActorId:    actorId,
		EntryPoint: goUtils.Enum(req.GetEntryPoint(), beVkycPb.EntryPoint_value, beVkycPb.EntryPoint_ENTRY_POINT_UNSPECIFIED),
	})
	if te := epifigrpc.RPCError(callExitRedirectDlResp, respErr); te != nil && !callExitRedirectDlResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error calling be GetCallExitRedirectDeeplink", zap.Error(te))
		return &feVkycPb.GetCallExitRedirectDeeplinkResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}
	if callExitRedirectDlResp.GetNextAction() != nil {
		return &feVkycPb.GetCallExitRedirectDeeplinkResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusOk(),
			},
			NextAction: callExitRedirectDlResp.GetNextAction(),
		}, nil
	}
	resp, err := s.beVkycClient.GetCallStatus(ctx, &beVkycPb.GetCallStatusRequest{
		ActorId:   actorId,
		AttemptId: req.GetAttemptId(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error registering customer in onb backend service", zap.Error(te))
		return &feVkycPb.GetCallExitRedirectDeeplinkResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}
	callStatusList := resp.GetCallStatus()
	feCallStatus := s.getFeCallStatus(ctx, callStatusList, req.GetReq().GetAuth().GetDevice().GetModel())
	logger.Info(ctx, "VKYC Fe call Status", zap.Any("feCallStatus", feCallStatus))
	deeplink, err := s.getCallExitDeeplink(ctx, feCallStatus, req.GetEntryPoint(), req.GetBlob(), req.GetIsTimerOver(), actorId, callStatusList)
	if err != nil && !errors.Is(err, callNotInTerminalState) {
		return &feVkycPb.GetCallExitRedirectDeeplinkResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}
	if errors.Is(err, callNotInTerminalState) {
		return &feVkycPb.GetCallExitRedirectDeeplinkResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusFailedPrecondition(),
			},
		}, nil
	}
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		vkycPkg.RecordVKYCCallActivity(vkycPkg.CallEnded, req.GetEntryPoint())
	})
	return &feVkycPb.GetCallExitRedirectDeeplinkResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		NextAction: deeplink,
	}, nil
}

func (s *Service) getFeCallStatus(ctx context.Context, karzaCallStatuses []*beVkycPb.KarzaCallStatus, deviceModel string) feVkycPb.CallStatus {
	for _, callStatus := range karzaCallStatuses {
		switch callStatus.GetStatus() {
		case beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_IN_PROGRESS:
			switch callStatus.GetSubStatus() {
			case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_CALL_INITIATED:
				return feVkycPb.CallStatus_CALL_INITIATED
			default:
				return feVkycPb.CallStatus_CALL_WAITING
			}
		case beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_IN_REVIEW, beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_APPROVED, beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_REJECTED:
			return feVkycPb.CallStatus_CALL_FINISHED
		case beVkycPb.VKYCKarzaCallInfoStatus_VKYC_KARZA_CALL_INFO_STATUS_FAILED:
			return s.getCallFailFeStatus(ctx, callStatus, deviceModel)
		default:
			return feVkycPb.CallStatus_CALL_WAITING
		}
	}
	return feVkycPb.CallStatus_CALL_WAITING
}

func (s *Service) getCallFailFeStatus(ctx context.Context, callStatus *beVkycPb.KarzaCallStatus, deviceModel string) feVkycPb.CallStatus {
	isUnsupportedCustomerDeviceModel := s.checkIfUnsupportedCustomerDeviceModel(deviceModel)
	// use device models to simulate INCOMPATIBLE_CUSTOMER_DEVICE failure flow
	if isUnsupportedCustomerDeviceModel {
		logger.Error(ctx, "unexpected device check in PROD")
		return feVkycPb.CallStatus_CALL_FAIL_INCOMPATIBLE_CUSTOMER_DEVICE
	}
	logger.Info(ctx, "get fe call status", zap.String("subStatus", callStatus.GetSubStatus().String()))
	switch callStatus.GetSubStatus() {
	case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_AGENT_NOT_AVAILABLE:
		return feVkycPb.CallStatus_CALL_EXIT
	case beVkycPb.VKYCKarzaCallInfoSubStatus_VKYC_KARZA_CALL_INFO_SUB_STATUS_INCOMPATIBLE_CUSTOMER_DEVICE:
		return feVkycPb.CallStatus_CALL_FAIL_INCOMPATIBLE_CUSTOMER_DEVICE
	default:
		return feVkycPb.CallStatus_CALL_FAILED
	}
}

func (s *Service) GetVKYCInfo(ctx context.Context, request *feVkycPb.GetVKYCInfoRequest) (*feVkycPb.GetVKYCInfoResponse, error) {
	var (
		actorId = request.GetReq().GetAuth().GetActorId()
		resp    = func(status *rpcPb.Status, showTile bool) *feVkycPb.GetVKYCInfoResponse {
			return &feVkycPb.GetVKYCInfoResponse{
				Status: status,
				RespHeader: &header.ResponseHeader{
					Status: status,
				},
				ShowTile: showTile,
			}
		}
		now = s.timeClient.Now()
	)

	bankCustInfo, errResp := s.bcClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if err := epifigrpc.RPCError(bankCustInfo, errResp); err != nil {
		if bankCustInfo.GetStatus().IsRecordNotFound() {
			return &feVkycPb.GetVKYCInfoResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		logger.Error(ctx, "error getting bank customer info ", zap.Error(err))
		return &feVkycPb.GetVKYCInfoResponse{Status: rpcPb.StatusInternal()}, nil
	}
	if bankCustInfo.GetBankCustomer().GetStatus() != bcPb.Status_STATUS_ACTIVE ||
		!bankCustInfo.GetBankCustomer().GetVendorCreationSucceededAt().IsValid() {
		return resp(rpcPb.StatusFailedPrecondition(), false), nil
	}
	if bankCustInfo.GetBankCustomer().GetKycInfo().GetKycLevel() == kycPb.KYCLevel_FULL_KYC {
		return resp(rpcPb.StatusOk(), false), nil
	}
	// indicates bkyc flow is in progress, hence disabling the vkyc entry point
	if bankCustInfo.GetBankCustomer().GetKycInfo().GetKycLevel() == kycPb.KYCLevel_MIN_KYC &&
		bankCustInfo.GetBankCustomer().GetVendorMetadata().GetFederalMetadata().GetKycUpgradeInfo().GetStatus() != bcPb.KYCUpgradeStatus_KYC_UPGRADE_STATUS_UNSPECIFIED {
		return resp(rpcPb.StatusRecordNotFound(), false), nil
	}
	cifSucceededAt := bankCustInfo.GetBankCustomer().GetVendorCreationSucceededAt()
	accFreezeOn := timestamppb.New(cifSucceededAt.AsTime().Add(vkycPkg.AccountClosureTimeLimit).Add(-1 * 24 * time.Hour))
	fundTransferAvlAfter := timestamppb.New(cifSucceededAt.AsTime().Add(vkycPkg.AccountClosureTimeLimit).Add(-fundTransferAvlAfterInDays * 24 * time.Hour))

	if now.After(accFreezeOn.AsTime()) {
		logger.Info(ctx, "account deemed to frozen, not showing vkyc to user")
		return resp(rpcPb.StatusOk(), false), nil
	}

	// To fetch failure reason , vkycStatus and ekycExpired
	vkycInfoResp, vkycInfoerr := s.beVkycClient.GetVKYCInfo(ctx, &beVkycPb.GetVKYCInfoRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(vkycInfoResp, vkycInfoerr); te != nil {
		logger.Error(ctx, "unable to fetch vkyc info by actor id", zap.Error(te))
		return &feVkycPb.GetVKYCInfoResponse{Status: rpcPb.StatusInternal()}, nil
	}

	// if previous call failed because of terminal failure we won't show vKYC entry point
	if lo.Contains[beVkycPb.VKYCKarzaCallInfoSubStatus](vkycPkg.TerminalFailureReasons, vkycPkg.GetLatestVKYCCallInfo(vkycInfoResp).GetSubStatus()) &&
		!vkycPkg.CheckIfReRegister(vkycInfoResp) {
		return &feVkycPb.GetVKYCInfoResponse{Status: rpcPb.StatusRecordNotFound()}, nil
	}

	vkycSummaryStatus := vkycInfoResp.GetVkycStatusResponse().GetVkycSummary().GetStatus()

	if vkycSummaryStatus == beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED ||
		vkycSummaryStatus == beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW {
		return resp(rpcPb.StatusOk(), false), nil
	}

	isLiveFlowEnabled := vkycInfoResp.GetIsLiveFlowEnabled()
	logger.Debug(ctx, fmt.Sprintf("Cif creation succeeded at %v", cifSucceededAt.AsTime()))

	switch {
	// checking if customer creation date is valid and not show vkyc option if its been almost a year
	case cifSucceededAt.IsValid() && now.After(accFreezeOn.AsTime()):
		logger.Info(ctx, "account deemed to frozen, not showing vkyc to user")
		return &feVkycPb.GetVKYCInfoResponse{Status: rpcPb.StatusOk(), ShowTile: false}, nil
	// X no. of days before account closure, users should get option to transfer their money
	case s.isUserEligibleToTransferFund(cifSucceededAt, fundTransferAvlAfter):
		dayUntilExpiry := math.Ceil(time.Until(cifSucceededAt.AsTime().Add(vkycPkg.AccountClosureTimeLimit).Add(-1*24*time.Hour)).Hours() / 24)
		popupTile := getAccountClosureFundTransferPopupTile(dayUntilExpiry)
		logger.Info(ctx, "Fund transfer is enabled and user is eligible for fund transfer also")
		return &feVkycPb.GetVKYCInfoResponse{
			Status:                       rpcPb.StatusOk(),
			ShowTile:                     true,
			PopupVkycTile:                popupTile,
			PopupTileNonDismissableAfter: MaxInt32,
			PopupTileDuration:            0,
		}, nil
	}
	if vkycSummaryStatus == beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED {
		return resp(rpcPb.StatusOk(), false), nil
	}
	popupTile, popupTileDuration := s.getPopupTile(ctx, actorId, isLiveFlowEnabled, cifSucceededAt)
	logger.Info(ctx, fmt.Sprintf("get vkyc info resp:  poptile %v, and duration %v", popupTile, popupTileDuration))
	return &feVkycPb.GetVKYCInfoResponse{
		Status:                       rpcPb.StatusOk(),
		ShowTile:                     false,
		PopupVkycTile:                popupTile,
		PopupTileNonDismissableAfter: MaxInt32,
		PopupTileDuration:            popupTileDuration,
	}, nil
}

func (s *Service) GetVKYCStatus(ctx context.Context, request *feVkycPb.GetVKYCStatusRequest) (*feVkycPb.GetVKYCStatusResponse, error) {
	var (
		actorId        = request.GetReq().GetAuth().GetActorId()
		responseHeader = &header.ResponseHeader{}
		deeplink       = &deeplinkPb.Deeplink{}
		entryPoint     = beVkycPb.EntryPoint_ENTRY_POINT_UNSPECIFIED
	)
	if _, ok := beVkycPb.EntryPoint_value[request.GetEntryPointStr()]; ok {
		entryPoint = beVkycPb.EntryPoint(beVkycPb.EntryPoint_value[request.GetEntryPointStr()])
	}

	bankCustInfo, errResp := s.bcClient.GetBankCustomer(ctx, &bcPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bcPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if err := epifigrpc.RPCError(bankCustInfo, errResp); err != nil {
		logger.Error(ctx, "error in getting bank customer details of user", zap.Error(err))
		responseHeader.Status = rpcPb.StatusInternal()
		return &feVkycPb.GetVKYCStatusResponse{RespHeader: responseHeader}, nil
	}

	clientProcessor, ok := vkycClientProcessor[request.GetEntryPoint()]
	if !ok {
		clientProcessor = vkycClientProcessor[deeplinkPb.EntryPoint_ENTRY_POINT_UNSPECIFIED]
	}

	// check if get vkyc next action is enabled and user is full kyc
	if bankCustInfo.GetBankCustomer().GetKycInfo().GetKycLevel() == kycPb.KYCLevel_FULL_KYC && !vkycPkg.IsAllowVKYCForFullKyc(actorId) {
		StatusDeeplinkBlob, statusDeeplinkErr := vkycPkg.UnmarshalStatusDeeplinkBlob(request.GetBlob())
		if statusDeeplinkErr != nil {
			logger.Error(ctx, "Error in unmarshal deeplink", zap.Error(statusDeeplinkErr))
			return &feVkycPb.GetVKYCStatusResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusInternal(),
				},
			}, nil
		}
		fullKycDeeplink, fullKycDeeplinkErr := feVkycDl.GetVKYCApprovedOrFKYCDeeplink(ctx, StatusDeeplinkBlob)
		if fullKycDeeplinkErr != nil {
			logger.Error(ctx, "Error while generating full kyc deeplink", zap.Error(fullKycDeeplinkErr))
			return &feVkycPb.GetVKYCStatusResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusInternal(),
				},
			}, nil
		}
		return &feVkycPb.GetVKYCStatusResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusOk(),
			},
			Deeplink: fullKycDeeplink,
		}, nil
	}

	// early return in case of full kyc user
	if bankCustInfo.GetBankCustomer().GetKycInfo().GetKycLevel() != kycPb.KYCLevel_MIN_KYC && !vkycPkg.IsAllowVKYCForFullKyc(actorId) {
		if request.Deeplink != nil {
			deeplink = request.GetDeeplink()
		} else {
			deeplink = clientProcessor.GetApprovedOrFullKycUserDeeplink()
		}
		return &feVkycPb.GetVKYCStatusResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusOk(),
			},
			Deeplink: deeplink,
		}, nil
	}

	// To fetch failure reason , vkycStatus and ekycExpired
	vkycInfoResp, vkycInfoerr := s.beVkycClient.GetVKYCInfo(ctx, &beVkycPb.GetVKYCInfoRequest{
		ActorId:    actorId,
		EntryPoint: request.GetEntryPointStr(),
	})

	if te := epifigrpc.RPCError(vkycInfoResp, vkycInfoerr); te != nil {
		logger.Error(ctx, "unable to fetch vkyc info by actor id", zap.Error(te))
		responseHeader.Status = rpcPb.StatusInternal()
		return &feVkycPb.GetVKYCStatusResponse{RespHeader: responseHeader}, nil
	}
	vkycSummary := vkycInfoResp.GetVkycRecord().GetVkycSummary()
	karzaCallStatus := &beVkycPb.KarzaCallStatus{
		SubStatus:     vkycInfoResp.GetVkycStatusResponse().GetVkycCallInfo().GetSubStatus(),
		CallStartedAt: vkycInfoResp.GetVkycStatusResponse().GetVkycCallInfo().GetCreatedAt(),
	}

	return s.fetchVKYCStatusDeeplink(ctx, entryPoint, request.GetBlob(), vkycSummary, actorId, karzaCallStatus, vkycInfoResp, request.GetReq().GetAppName())
}

// is user eligible to transfer his funds
func (s *Service) isUserEligibleToTransferFund(cifSucceededAt *timestamp.Timestamp, fundTransferAvlAfter *timestamp.Timestamp) bool {
	return cifSucceededAt.IsValid() && time.Now().After(fundTransferAvlAfter.AsTime())
}

func (s *Service) checkIfUnsupportedCustomerDeviceModel(customerDeviceModel string) bool {
	trimmedLowerCaseDeviceModel := strings.TrimSpace(strings.ToLower(customerDeviceModel))
	for _, x := range s.genConf.VKYC().UnsupportedCustomerDeviceModels() {
		lowerCaseUnsupportedWebviewDeviceModels := strings.TrimSpace(strings.ToLower(x))
		// ignoring err since regMatch would be false in that case
		regCompile := regexp.MustCompile(lowerCaseUnsupportedWebviewDeviceModels)
		regMatch := regCompile.MatchString(trimmedLowerCaseDeviceModel)
		if strings.EqualFold(trimmedLowerCaseDeviceModel, lowerCaseUnsupportedWebviewDeviceModels) ||
			regMatch {
			return true
		}
	}
	return false
}

func (s *Service) getDaysRemainingBeforeAccountFreeze(accountFreezeDate *timestamp.Timestamp) int {
	duration := accountFreezeDate.AsTime().Sub(time.Now())
	durationInHours := int(duration.Hours())
	durationInDays := durationInHours / 24
	return durationInDays
}

// checkIfGoToDeeplink returns true if BE and app version both enables weblink screen to be shown to users who get INCOMPATIBLE_CUSTOMER_DEVICE from vendor
func (s *Service) checkIfGoToDeeplink(isUnsupportedCustomerDeviceModel bool, startLiveCallResp *beVkycPb.StartLiveCallResponse) bool {
	// USER_NOT_AUDIBLE(callFailureForUnsupportedWebviewMap) returns webview screen on one call failure, use that with device model combination to simulate incompatible device in non prod
	return vkycPkg.ValidateIncompatibleDeviceAppVersionCheck(startLiveCallResp.GetEkycDate(), s.genConf) &&
		(startLiveCallResp.GetIsCustomerDeviceIncompatible() || (isUnsupportedCustomerDeviceModel && startLiveCallResp.GetIsUnsupportedWebview()))
}

func (s *Service) getAccountFreezePopupTitle(ctx context.Context, accFreezeDate *timestamp.Timestamp) string {
	accFreezeInDays := s.getDaysRemainingBeforeAccountFreeze(accFreezeDate)
	if accFreezeInDays < 1 {
		logger.Error(ctx, fmt.Sprintf("VKYC ALERT: unexpected account freeze days: %v", accFreezeInDays))
	}
	if accFreezeInDays >= 30 {
		return accountFreezeLimitReachPast30DaysTitles
	}
	if accFreezeInDays == 0 {
		return fmt.Sprintf(accountFreezeLimitReachTitle, "less than 1 day")
	}
	if accFreezeInDays == 1 {
		return fmt.Sprintf(accountFreezeLimitReachTitle, "1 day")
	}
	return fmt.Sprintf(accountFreezeLimitReachTitle, strconv.Itoa(accFreezeInDays)+" days")
}

// ValidateVKYCPreCallConditions just logs the info
// once we get hold on data we will start blocking user on this stage
func (s *Service) ValidateVKYCPreCallConditions(_ context.Context, _ *feVkycPb.ValidateVKYCPreCallConditionsRequest) (*feVkycPb.ValidateVKYCPreCallConditionsResponse, error) {
	return &feVkycPb.ValidateVKYCPreCallConditionsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusUnimplemented(),
		},
	}, nil
}

func (s *Service) GetVKYCNextAction(ctx context.Context, req *feVkycPb.GetVKYCNextActionRequest) (*feVkycPb.GetVKYCNextActionResponse, error) {
	entryPoint := beVkycPb.EntryPoint_ENTRY_POINT_UNSPECIFIED
	if _, ok := beVkycPb.EntryPoint_value[req.GetEntryPoint()]; ok {
		entryPoint = beVkycPb.EntryPoint(beVkycPb.EntryPoint_value[req.GetEntryPoint()])
	}
	clientLastState := beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_UNSPECIFIED
	if _, ok := beVkycPb.VKYCClientState_value[req.GetClientLastState()]; ok {
		clientLastState = beVkycPb.VKYCClientState(beVkycPb.VKYCClientState_value[req.GetClientLastState()])
	}
	vkycNextActionRes, vkycNextActionErr := s.beVkycClient.GetVKYCNextAction(ctx, &beVkycPb.GetVKYCNextActionRequest{
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		EntryPoint:      entryPoint,
		ClientLastState: clientLastState,
		Blob:            req.GetBlob(),
		AppName:         req.GetReq().GetAppName(),
	})
	if rpcError := epifigrpc.RPCError(vkycNextActionRes, vkycNextActionErr); rpcError != nil {
		logger.Error(ctx, "Error from BE vkyc next action", zap.Error(rpcError))
		return &feVkycPb.GetVKYCNextActionResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg(rpcError.Error()),
			},
		}, nil
	}
	logger.Info(ctx, "vkyc next action", zap.String(logger.RESULT, vkycNextActionRes.GetNextAction().GetScreen().String()),
		zap.String(logger.FEEDBACK_ENGINE_APP_FLOW, entryPoint.String()), zap.String(logger.STATUS, clientLastState.String()), zap.Int(logger.APP_VERSION_CODE, epificontext.AppVersionFromContext(ctx)))
	return &feVkycPb.GetVKYCNextActionResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		NextAction: vkycNextActionRes.GetNextAction(),
	}, nil
}

func (s *Service) fetchVKYCStatusDeeplink(ctx context.Context, entryPoint beVkycPb.EntryPoint, deeplinkBlob []byte, summary *beVkycPb.VKYCSummary, actorId string, karzaCallStatus *beVkycPb.KarzaCallStatus, vkycInfoResp *beVkycPb.GetVKYCInfoResponse, appName commontypes.AppName) (*feVkycPb.GetVKYCStatusResponse, error) {
	var (
		deeplink = &deeplinkPb.Deeplink{}
		err      error
	)

	statusDeeplinkBlob, statusDeeplinkErr := vkycPkg.UnmarshalStatusDeeplinkBlob(deeplinkBlob)
	if statusDeeplinkErr != nil {
		logger.Error(ctx, "Error in unmarshal deeplink", zap.Error(statusDeeplinkErr))
		return &feVkycPb.GetVKYCStatusResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}

	isNewVKYCReviewScreenEnabled := s.isNewVKYCReviewScreenEnabled(ctx, actorId)

	switch {
	case summary.GetStatus() == beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED:
		deeplink, err = feVkycDl.GetVKYCApprovedOrFKYCDeeplink(ctx, statusDeeplinkBlob)
	case summary.GetStatus() == beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW:
		accountRes, err := s.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
			Identifier: &savingsPb.GetAccountRequest_ActorId{
				ActorId: actorId,
			}})
		if err != nil {
			logger.Error(ctx, "failed to fetch savings accoun", zap.Error(err))
		}
		accountCreatedAtTime := accountRes.GetAccount().GetCreationInfo().GetFiCreationSucceededAt()
		deeplink, err = feVkycDl.GetVKYCReviewDeeplink(ctx, entryPoint, statusDeeplinkBlob, s.genConf.VKYC(), karzaCallStatus,
			isNewVKYCReviewScreenEnabled, accountCreatedAtTime, s.evaluator, actorId)
	case summary.GetStatus() == beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED:
		deeplink, err = feVkycDl.GetVKYCRejectedDeeplink(ctx, entryPoint, statusDeeplinkBlob, isNewVKYCReviewScreenEnabled, summary)
	default:
		deeplink, err = s.handleUserVKYCEligible(ctx, entryPoint, statusDeeplinkBlob, actorId, vkycInfoResp, appName)
	}
	if err != nil {
		logger.Error(ctx, "Error in generating vkyc deeplink", zap.Error(err))
		return &feVkycPb.GetVKYCStatusResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
			},
		}, nil
	}
	return &feVkycPb.GetVKYCStatusResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		Deeplink: deeplink,
	}, nil
}

func (s *Service) handleUserVKYCEligible(ctx context.Context, entryPoint beVkycPb.EntryPoint, statusDeeplinkBlob *vkycPkg.StatusDeeplinkBlob, actorId string, vkycInfoRes *beVkycPb.GetVKYCInfoResponse, appName commontypes.AppName) (*deeplinkPb.Deeplink, error) {
	if lo.Contains[beVkycPb.VKYCKarzaCallInfoSubStatus](vkycPkg.TerminalFailureReasons, vkycPkg.GetLatestVKYCCallInfo(vkycInfoRes).GetSubStatus()) &&
		!vkycPkg.CheckIfReRegister(vkycInfoRes) {
		return vkycPkg.ConstructVkycErrorScreen(blockVKYCTitle, blockVKYCSubtitle, blockVKYCImageUrl, []*deeplinkPb.Cta{
			{
				Type: deeplinkPb.Cta_CUSTOM,
				Text: vkycPkg.OkGotItCtaTxt,
				Deeplink: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_GET_NEXT_ONBOARDING_ACTION_API,
				},
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
		}, nil, true), nil
	}

	var (
		deeplink = &deeplinkPb.Deeplink{}
		err      error
	)
	// check if entry point intent to skip intro page
	if statusDeeplinkBlob == nil || statusDeeplinkBlob.VKYCEligibleScreenDeeplink == nil {
		switch {
		case vkycPkg.IsSkipIntroPage(entryPoint):
			getNextResp, getNextErr := s.beVkycClient.GetVKYCNextAction(ctx, &beVkycPb.GetVKYCNextActionRequest{
				ActorId:         actorId,
				ClientLastState: beVkycPb.VKYCClientState_VKYC_CLIENT_STATE_INITIATED,
				EntryPoint:      entryPoint,
				AppName:         appName,
			})
			if te := epifigrpc.RPCError(getNextResp, getNextErr); te != nil {
				logger.Error(ctx, "Error in fetching vkyc next action", zap.Error(te))
				return nil, te
			}
			return getNextResp.GetNextAction(), nil
		}
	}
	deeplink, err = feVkycDl.GetInitialVKYCScreen(ctx, entryPoint, statusDeeplinkBlob, vkycInfoRes)
	return deeplink, err
}

func getVKYCStatusScreen(ctx context.Context, entryPoint beVkycPb.EntryPoint) *deeplinkPb.Deeplink {
	deeplink, err := vkycPkg.BuildVKYCStatusDeeplink(&vkycPkg.StatusScreenOptions{
		EntryPoint: entryPoint,
	})
	if err != nil {
		logger.Error(ctx, "Error while generating vkyc deeplink", zap.Error(err))
		return nil
	}
	return deeplink
}

func (s *Service) UploadVKYCCallQualityData(ctx context.Context, req *feVkycPb.UploadVKYCCallQualityDataRequest) (*feVkycPb.UploadVKYCCallQualityDataResponse, error) {
	beCallQualityData, err := convertToBECallQualityData(req.GetCallQualityData())
	if err != nil {
		logger.Error(ctx, "unable to convert to call quality data to BE", zap.Error(err))
		return &feVkycPb.UploadVKYCCallQualityDataResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg(err.Error()),
			},
		}, nil
	}
	resp, respErr := s.beVkycClient.UploadVKYCCallQualityData(ctx, &beVkycPb.UploadVKYCCallQualityDataRequest{
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		CallQualityData: beCallQualityData,
		EntryPoint:      goUtils.Enum(req.GetEntryPoint(), beVkycPb.EntryPoint_value, beVkycPb.EntryPoint_ENTRY_POINT_UNSPECIFIED),
	})
	if rpcErr := epifigrpc.RPCError(resp, respErr); rpcErr != nil {
		logger.Error(ctx, "error in uploading data to Backend", zap.Error(rpcErr))
		return &feVkycPb.UploadVKYCCallQualityDataResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg(rpcErr.Error()),
			},
		}, nil
	}
	return &feVkycPb.UploadVKYCCallQualityDataResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		NextAction: resp.GetNextAction(),
	}, nil
}
