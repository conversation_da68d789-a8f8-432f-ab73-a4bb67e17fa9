package lenden

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	fegenconf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	helperMocks "github.com/epifi/gamma/frontend/preapprovedloan/helper/mocks"
)

// Test data setup
var (
	testLoanHeader = &palPbFeEnums.LoanHeader{
		LoanProgram: palPbFeEnums.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palPbFeEnums.Vendor_LENDEN,
	}

	testLoanRequest = &palBePb.LoanRequest{
		Id:          "loan-req-123",
		ActorId:     "actor-456",
		Status:      palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
		LoanProgram: palBePb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Details: &palBePb.LoanRequestDetails{
			LoanInfo: &palBePb.LoanRequestDetails_LoanInfo{
				Amount:         &money.Money{CurrencyCode: "INR", Units: 50000},
				TenureInMonths: 24,
			},
		},
	}

	testLoanOffer = &palBePb.LoanOffer{}

	testKycURL     = "https://example.com/kyc"
	testMandateURL = "https://example.com/mandate"
)

// Test fixtures
type testFixtures struct {
	mockHelper        *helperMocks.MockIRpcHelper
	mockOnbClient     *onbMocks.MockOnboardingClient
	mockSavingsClient *savingsMocks.MockSavingsClient
	mockUserClient    *userMocks.MockUsersClient
	lendenProvider    *Provider
}

func setupTestFixtures(t *testing.T) *testFixtures {
	// Initialize logger to prevent nil pointer errors
	logger.Init(cfg.TestEnv)

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	mockHelper := helperMocks.NewMockIRpcHelper(ctrl)
	mockOnbClient := onbMocks.NewMockOnboardingClient(ctrl)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
	mockUserClient := userMocks.NewMockUsersClient(ctrl)

	// Create mock configurations
	mockPreApprovedLoanConf := &fegenconf.PreApprovedLoan{}
	mockLendingConf := &fegenconf.Lending{}

	baseProvider := baseprovider.NewBaseDeeplinkProvider(
		mockPreApprovedLoanConf,
		mockLendingConf,
		mockHelper,
		mockOnbClient,
		mockSavingsClient,
		mockUserClient,
	)

	lendenProvider := NewLendenProvider(baseProvider)

	return &testFixtures{
		mockHelper:        mockHelper,
		mockOnbClient:     mockOnbClient,
		mockSavingsClient: mockSavingsClient,
		mockUserClient:    mockUserClient,
		lendenProvider:    lendenProvider,
	}
}

// Helper functions for common assertions
func assertValidResponse(t *testing.T, result *provider.GetDashboardLoanApplicationDetailsResponse, expectedComponentLen int) {
	t.Helper()
	assert.NotNil(t, result)
	assert.NotNil(t, result.GetActiveApplicationCards())

	components := result.GetActiveApplicationCards().GetComponents()
	assert.Len(t, components, expectedComponentLen)
}

func assertNilResponse(t *testing.T, result *provider.GetDashboardLoanApplicationDetailsResponse) {
	t.Helper()
	assert.Nil(t, result)
}

func assertPWACopyComponent(t *testing.T, result *provider.GetDashboardLoanApplicationDetailsResponse, componentIndex int) {
	t.Helper()
	components := result.GetActiveApplicationCards().GetComponents()
	assert.Less(t, componentIndex, len(components), "Component index out of range")

	component := components[componentIndex]
	assert.NotNil(t, component.GetBannerWithLeftIconAndRightCta(), "Expected BannerWithLeftIconAndRightCta component")

	banner := component.GetBannerWithLeftIconAndRightCta()
	assert.NotNil(t, banner.GetTitle(), "Banner title should not be nil")
	assert.NotNil(t, banner.GetDescription(), "Banner description should not be nil")
	assert.NotNil(t, banner.GetRightCta(), "Banner right CTA should not be nil")
	assert.NotNil(t, banner.GetRightCta().GetDeeplink(), "Banner CTA deeplink should not be nil")

	// Verify that the deeplink contains the expected URL in the screen options
	deeplink := banner.GetRightCta().GetDeeplink()
	assert.NotNil(t, deeplink.GetScreenOptionsV2(), "Deeplink screen options should not be nil")
}

func TestProvider_GetLoanApplicationDetailsForDashboard(t *testing.T) {
	type args struct {
		ctx       context.Context
		lh        *palPbFeEnums.LoanHeader
		lr        *palBePb.LoanRequest
		lses      []*palBePb.LoanStepExecution
		loanOffer *palBePb.LoanOffer
	}

	type expectedOutput struct {
		wantErr          bool
		wantNilResult    bool
		wantComponentLen int
	}

	tests := []struct {
		name     string
		args     args
		expected expectedOutput
	}{
		{
			name: "CKYC step with non-success status and valid KYC URL - should add PWA copy component",
			args: args{
				ctx: context.Background(),
				lh:  testLoanHeader,
				lr:  testLoanRequest,
				lses: []*palBePb.LoanStepExecution{
					{
						StepName: palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC,
						Status:   palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						Details: &palBePb.LoanStepExecutionDetails{
							Details: &palBePb.LoanStepExecutionDetails_KycStepData{
								KycStepData: &palBePb.KycStepData{
									KycUrl: testKycURL,
								},
							},
						},
					},
				},
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 2, // Base card + PWA copy component
			},
		},
		{
			name: "CKYC step with success status - should not add PWA copy component",
			args: args{
				ctx: context.Background(),
				lh:  testLoanHeader,
				lr:  testLoanRequest,
				lses: []*palBePb.LoanStepExecution{
					{
						StepName: palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC,
						Status:   palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
						Details: &palBePb.LoanStepExecutionDetails{
							Details: &palBePb.LoanStepExecutionDetails_KycStepData{
								KycStepData: &palBePb.KycStepData{
									KycUrl: testKycURL,
								},
							},
						},
					},
				},
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 1, // Only base card
			},
		},
		{
			name: "CKYC step with non-success status but empty KYC URL - should not add PWA copy component",
			args: args{
				ctx: context.Background(),
				lh:  testLoanHeader,
				lr:  testLoanRequest,
				lses: []*palBePb.LoanStepExecution{
					{
						StepName: palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC,
						Status:   palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						Details: &palBePb.LoanStepExecutionDetails{
							Details: &palBePb.LoanStepExecutionDetails_KycStepData{
								KycStepData: &palBePb.KycStepData{
									KycUrl: "",
								},
							},
						},
					},
				},
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 1, // Only base card
			},
		},
		{
			name: "MANDATE step with non-success status and valid mandate URL - should add PWA copy component",
			args: args{
				ctx: context.Background(),
				lh:  testLoanHeader,
				lr:  testLoanRequest,
				lses: []*palBePb.LoanStepExecution{
					{
						StepName: palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE,
						Status:   palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						Details: &palBePb.LoanStepExecutionDetails{
							Details: &palBePb.LoanStepExecutionDetails_MandateData{
								MandateData: &palBePb.MandateData{
									Url: testMandateURL,
								},
							},
						},
					},
				},
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 2, // Base card + PWA copy component
			},
		},
		{
			name: "MANDATE step with success status - should not add PWA copy component",
			args: args{
				ctx: context.Background(),
				lh:  testLoanHeader,
				lr:  testLoanRequest,
				lses: []*palBePb.LoanStepExecution{
					{
						StepName: palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE,
						Status:   palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
						Details: &palBePb.LoanStepExecutionDetails{
							Details: &palBePb.LoanStepExecutionDetails_MandateData{
								MandateData: &palBePb.MandateData{
									Url: testMandateURL,
								},
							},
						},
					},
				},
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 1, // Only base card
			},
		},
		{
			name: "MANDATE step with non-success status but empty mandate URL - should not add PWA copy component",
			args: args{
				ctx: context.Background(),
				lh:  testLoanHeader,
				lr:  testLoanRequest,
				lses: []*palBePb.LoanStepExecution{
					{
						StepName: palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE,
						Status:   palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						Details: &palBePb.LoanStepExecutionDetails{
							Details: &palBePb.LoanStepExecutionDetails_MandateData{
								MandateData: &palBePb.MandateData{
									Url: "",
								},
							},
						},
					},
				},
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 1, // Only base card
			},
		},
		{
			name: " CKYC step is success and MANDATE step is in non-terminal with valid URLs - should add two PWA copy components",
			args: args{
				ctx: context.Background(),
				lh:  testLoanHeader,
				lr:  testLoanRequest,
				lses: []*palBePb.LoanStepExecution{
					{
						StepName: palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC,
						Status:   palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
						Details: &palBePb.LoanStepExecutionDetails{
							Details: &palBePb.LoanStepExecutionDetails_KycStepData{
								KycStepData: &palBePb.KycStepData{
									KycUrl: testKycURL,
								},
							},
						},
					},
					{
						StepName: palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE,
						Status:   palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
						Details: &palBePb.LoanStepExecutionDetails{
							Details: &palBePb.LoanStepExecutionDetails_MandateData{
								MandateData: &palBePb.MandateData{
									Url: testMandateURL,
								},
							},
						},
					},
				},
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 2, // Base card + 2 PWA copy components
			},
		},
		{
			name: "No relevant steps - should return only base card",
			args: args{
				ctx: context.Background(),
				lh:  testLoanHeader,
				lr:  testLoanRequest,
				lses: []*palBePb.LoanStepExecution{
					{
						StepName: palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
						Status:   palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
					},
				},
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 1, // Only base card
			},
		},
		{
			name: "Nil loan request - should return nil",
			args: args{
				ctx:       context.Background(),
				lh:        testLoanHeader,
				lr:        nil,
				lses:      nil,
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:       false,
				wantNilResult: true,
			},
		},
		{
			name: "SUCCESS status loan request - should return nil",
			args: args{
				ctx: context.Background(),
				lh:  testLoanHeader,
				lr: &palBePb.LoanRequest{
					Status: palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
				},
				lses:      nil,
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:       false,
				wantNilResult: true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fixtures := setupTestFixtures(t)

			result, err := fixtures.lendenProvider.GetLoanApplicationDetailsForDashboard(
				tt.args.ctx,
				tt.args.lh,
				tt.args.lr,
				tt.args.lses,
				tt.args.loanOffer,
			)

			// Validate error expectation
			if tt.expected.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)

			// Validate nil result expectation
			if tt.expected.wantNilResult {
				assertNilResponse(t, result)
				return
			}

			// Validate non-nil result structure
			assertValidResponse(t, result, tt.expected.wantComponentLen)

			// Additional validation for PWA copy components
			if tt.expected.wantComponentLen > 1 {
				// Verify that the additional components are PWA copy components
				for i := 1; i < tt.expected.wantComponentLen; i++ {
					assertPWACopyComponent(t, result, i)
				}
			}
		})
	}
}
