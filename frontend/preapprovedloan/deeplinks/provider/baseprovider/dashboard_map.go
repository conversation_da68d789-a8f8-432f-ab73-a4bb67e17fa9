package baseprovider

import (
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	loansPkg "github.com/epifi/gamma/pkg/loans"
	mvLoansPkg "github.com/epifi/gamma/pkg/loans/moneyview"
)

type enrichData struct {
	title         string
	bgColor       string
	message       string
	icon          string
	statusPollDl  bool
	showReloadCta bool
	canCancel     bool
	ctaText       string
}

type lrStatusEnrichDataMap map[palPb.LoanRequestStatus]*enrichData
type lseStatusEnrichDataMap map[palPb.LoanStepExecutionStepName]map[palPb.LoanStepExecutionStatus]*enrichData
type lseSubStatusEnrichDataMap map[palPb.LoanStepExecutionStepName]map[palPb.LoanStepExecutionStatus]map[palPb.LoanStepExecutionSubStatus]*enrichData

var (
	lrStatusDashboardMap = lrStatusEnrichDataMap{
		palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED:             lrStatusCreated,
		palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED:           lrStatusCancelled,
		palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
	}
	lseSubStatusDashboardMap = lseSubStatusEnrichDataMap{
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_APPLICANT_CREATION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_CREATED: lseSubStatusApplicantCreated,
			},
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_APPLICANT_CREATED: lseSubStatusApplicantCreated,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ADDRESS_DETAILS_ADDED:    lseSubStatusAddressAdded,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_EMPLOYMENT_DETAILS_ADDED: lseSubStatusEmploymentDetailsAdded,
			},
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ADDRESS_DETAILS_ADDED:    lseSubStatusAddressAdded,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_EMPLOYMENT_DETAILS_ADDED: lseSubStatusEmploymentDetailsAdded,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_PENDING_UPLOAD: lseBackgroundProcessInProgress,
			},
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_PENDING_UPLOAD: lseBackgroundProcessInProgress,
			},
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_PENDING_UPLOAD: lseBackgroundProcessInProgress,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UPLOAD_FILE: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_PENDING_UPLOAD:     lseBackgroundProcessInProgress,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_UPLOADED: lseBackgroundProcessInProgress,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_KFS_UPLOADED:       lseBackgroundProcessInProgress,
			},
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_PENDING_UPLOAD:     lseBackgroundProcessInProgress,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_UPLOADED: lseBackgroundProcessInProgress,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_KFS_UPLOADED:       lseBackgroundProcessInProgress,
			},
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_PENDING_UPLOAD:     lseBackgroundProcessInProgress,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_UPLOADED: lseBackgroundProcessInProgress,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_KFS_UPLOADED:       lseBackgroundProcessInProgress,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_REVIEW: lseSubStatusManualReview,
			},
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_REVIEW: lseSubStatusManualReview,
			},
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANUAL_REVIEW: lseStatusFailedSubStatusManualReview,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_LINK_FETCHED:        lseSubStatusMandateLinkFetched,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_MANDATE_INITIATED_AT_VENDOR: lseSubStatusMandateInitiatedAtVendor,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED: lseSubStatusRevisedLoan,
			},
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED: lseSubStatusRevisedLoan,
			},
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_CHANGED: lseSubStatusDifferentOffer,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_DETAILS_VERIFIED:  lseSubStatusCkycverified,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_USER_TEMPORARY_BLOCKED: lseSubStatusCkycOtpVerificationTemporaryBlockUser,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_PAN_ADDED: lseSubStatusPanAdded,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VKYC: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_MAKER_CHECK_APPROVED:      lseSubStatusVkycMakerApproved,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_VERIFICATION_FAILED:       lseSubStatusVkycVerificationFailed,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_INITIATED:            lseStatusVkycInProgress,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_VKYC_CALL_IN_REVIEW:            lseSubStatusVkycInReview,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_IN_PROGRESS: lseSubStatusVkycDataRedactionInProgress,
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KYC_DATA_REDACTION_DONE:        lseStatusVkycInProgress,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CONTACTABILITY: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_CONTACTABILITY_COOL_OFF_PERIOD: lseSubStatusContactabilityCooloff,
			},
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED: {
				palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_DETAILS_REVISED: lseSubStatusRevisedLoan,
			},
		},
	}
	lseStatusDashboardMap = lseStatusEnrichDataMap{
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KYC_DOCUMENT_DOWNLOAD: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusKycInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusKycCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusKycFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusKfsSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lrStatusKycExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AML: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: lseStatusDefaultAml,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:     lseStatusDefaultAml,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:     lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusLivenessInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusLivenessCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusLivenessFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusLivenessSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusCkycInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusCkycCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusCkycFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusCkycSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusMandateInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusMandateCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusMandateFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusMandateSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_APPLICANT_CREATION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusApplicantCreationInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusApplicantCreationCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusApplicantCreationFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusApplicantCreationSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_DRAWDOWN: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusDrawdownInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusDrawdownCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusDrawdownFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusDrawdownSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_AMOUNT_DISBURSAL: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusDisbursementInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusDisbursementCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusDisbursementFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusDisbursementSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusAccountCreationInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusAccountCreationCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusAccountCreationFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusAccountCreationSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusFetchOfferInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusFetchOfferCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusFetchOfferFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusFetchOfferSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusFetchOfferInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusFetchOfferCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusFetchOfferFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusFetchOfferSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: lseBackgroundProcessInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:     lseBackgroundProcessInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:      lseStatusAccountCreationFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:     lseBackgroundProcessInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:     lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusKfsInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusKfsCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusKfsFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusKfsSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lrStatusKfsExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusAddDetailsInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusAddDetailsCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusAddDetailsFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusAddDetailsSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusProfileValidationInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusProfileValidationCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusProfileValidationFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusProfileValidationSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PAN_VERIFICATION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusPanValidationCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusPanValidationCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusPanValidationFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusAddDetailsSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_RISK_CHECK: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusRiskCheckInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusRiskCheckCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusRiskCheckFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusRiskCheckSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UPLOAD_FILE: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseBackgroundProcessInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseBackgroundProcessInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusUploadFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseBackgroundProcessInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_BANKING_DETAILS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusAddBankingInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusAddBankingInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusAddDetailsFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusAddDetailsSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AADHAAR: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusAddAadhaarInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusAddAadhaarInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusAddDetailsFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusAddDetailsSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_UPDATE_LEAD_DETAILS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusUpdateLeadInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusUpdateLeadCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusUpdateLeadFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusDrawdownSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADDRESS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusAddDetailsInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusAddDetailsCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusAddDetailsFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusDrawdownSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseSubStatusAddressAdded,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseSubStatusAddressAdded,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusAddDetailsFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusAddDetailsSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusFetchOfferInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusFetchOfferCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusBreCheckFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusFetchOfferSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KYC_CHECK: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusKycCheckInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusKycCheckInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusRiskCheckFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusFetchOfferSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_SELFIE_CAPTURE: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusLivenessInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusLivenessCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusLivenessFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusLivenessSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_REFERENCES: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusReferencesInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusReferencesInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusAddDetailsFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusDrawdownSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VKYC: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusVkycInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusVkycInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusVkycFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusMandateCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lrStatusexpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LAT_LONG: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusLatLongInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusLatLongCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusLatLongFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusLatLongSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CREDIT_REPORT_FETCH: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: lseStatusCreditReportFetchInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:     lseStatusCreditReportFetchCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:     lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CONTACTABILITY: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: lseStatusContactabilityInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:     lseStatusContactabilityInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:      lseStatusContactabilityFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:     lseStatusContactabilitySuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:     lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PENNY_DROP: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusPennyDropInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusAddBankingInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusPennyDropFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusPennyDropSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},

		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusNoStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusNoStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusCommonFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_ADD_DETAILS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusNoStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusNoStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusCommonFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: lseStatusStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:     lseStatusStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:      lseStatusCommonFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:     lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED: lseStatusCommonFailed,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ROI_MODIFICATION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED: lseStatusCommonFailed,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_RE_KFS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusKfsInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusKfsCreated,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusKfsFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:             lseStatusKfsSuccess,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lrStatusKfsExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_INITIALISE_LOAN: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusNoStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusNoStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusCommonFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_USER_OFFER_SELECTION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS:         lseStatusUserOfferSelectionInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:             lseStatusUserOfferSelectionInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:              lseStatusCommonFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION: lrStatusManualIntervention,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:             lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT_CHECK: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED: lseStatusEmpCheckPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:  lseStatusCommonFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED: lseStatusExpired,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_INCOME_ESTIMATION: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED:     lseStatusStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS: lseStatusStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:      lseStatusCommonFailed,
		},
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_BRE_DATA_FETCH_LOAN_DETAILS: {
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED: lseStatusStatusPollInProgress,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:  lseStatusCommonFailed,
			palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED: lseStatusExpired,
		},
	}
)

func IsApplicationCancellable(lr *palPb.LoanRequest, lse *palPb.LoanStepExecution) bool {
	if lr.GetType() == palPb.LoanRequestType_LOAN_REQUEST_TYPE_ELIGIBILITY {
		return true
	}
	canCancel := getApplicationStateData(lr, lse).GetCanCancel()
	if loansPkg.IsPwaJourney(lr) {
		switch lr.GetVendor() {
		case palPb.Vendor_MONEYVIEW:
			if lse.GetStepName() != palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES {
				return canCancel
			}
			latestStage := mvLoansPkg.GetBestEffortLatestPwaStage(lse)
			return mvLoansPkg.IsStageCancellable(latestStage)
		default:
			return false
		}
	}
	return canCancel
}

func getApplicationStateData(lr *palPb.LoanRequest, lse *palPb.LoanStepExecution) *enrichData {
	if lr != nil {
		if data, ok := lrStatusDashboardMap[lr.GetStatus()]; ok {
			return data
		}
	}
	if lse != nil {
		if data, ok := lseSubStatusDashboardMap[lse.GetStepName()][lse.GetStatus()][lse.GetSubStatus()]; ok {
			return data
		}
		if data, ok := lseStatusDashboardMap[lse.GetStepName()][lse.GetStatus()]; ok {
			return data
		}
	}
	return defaultEnrichData
}

func (e *enrichData) GetTitle() string {
	if e != nil {
		return e.title
	}
	return ""
}
func (e *enrichData) GetBgColor() string {
	if e != nil {
		return e.bgColor
	}
	return ""
}
func (e *enrichData) GetMessage() string {
	if e != nil {
		return e.message
	}
	return ""
}
func (e *enrichData) GetIcon() string {
	if e != nil {
		return e.icon
	}
	return ""
}
func (e *enrichData) GetDl() bool {
	if e != nil {
		return e.statusPollDl
	}
	return false
}
func (e *enrichData) GetShowReloadCta() bool {
	if e != nil {
		return e.showReloadCta
	}
	return false
}
func (e *enrichData) GetCanCancel() bool {
	if e != nil {
		return e.canCancel
	}
	return false
}
func (e *enrichData) GetCtaText() string {
	if e == nil || e.ctaText == "" {
		return "View"
	}
	return e.ctaText
}
