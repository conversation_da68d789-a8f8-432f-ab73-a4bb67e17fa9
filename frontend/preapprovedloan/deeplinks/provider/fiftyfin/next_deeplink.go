package fiftyfin

import (
	"context"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
)

func (lp *LamfProvider) GetNextDeeplink(ctx context.Context, lh *palPbFeEnums.LoanHeader, res *palBePb.GetDashboardResponse, showOfferV2 bool, actorId string, postLoansV2Active bool) (*deeplinkPb.Deeplink, error) {
	return lp.GetLoanDashboardScreenDeepLinkWithScreenOptionsV2(ctx, lh, res, showOfferV2, "")
}
