package federal

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/logger"

	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	palBePbMocks "github.com/epifi/gamma/api/preapprovedloan/mocks"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	fegenconf "github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	helperMocks "github.com/epifi/gamma/frontend/preapprovedloan/helper/mocks"
)

// Test data setup
var (
	testLoanHeader = &palPbFeEnums.LoanHeader{
		LoanProgram: palPbFeEnums.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Vendor:      palPbFeEnums.Vendor_FEDERAL_BANK,
	}

	testLoanRequest = &palBePb.LoanRequest{
		Id:          "loan-req-123",
		ActorId:     "actor-456",
		Status:      palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING,
		LoanProgram: palBePb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Details: &palBePb.LoanRequestDetails{
			LoanInfo: &palBePb.LoanRequestDetails_LoanInfo{
				Amount:         &money.Money{CurrencyCode: "INR", Units: 20000},
				TenureInMonths: 12,
			},
		},
	}

	testLoanStepExecutions = []*palBePb.LoanStepExecution{
		{
			StepName: palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS,
			Status:   palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
		},
	}

	testLoanOffer = &palBePb.LoanOffer{}

	testSignURL = "https://example.com/sign"
)

// Test fixtures
type testFixtures struct {
	mockPreApprovedLoanClient *palBePbMocks.MockPreApprovedLoanClient
	mockHelper                *helperMocks.MockIRpcHelper
	mockOnbClient             *onbMocks.MockOnboardingClient
	mockSavingsClient         *savingsMocks.MockSavingsClient
	mockUserClient            *userMocks.MockUsersClient
	loansProvider             *LoansProvider
}

func setupTestFixtures(t *testing.T) *testFixtures {
	// Initialize logger to prevent nil pointer errors
	logger.Init(cfg.TestEnv)

	ctrl := gomock.NewController(t)
	t.Cleanup(ctrl.Finish)

	mockPreApprovedLoanClient := palBePbMocks.NewMockPreApprovedLoanClient(ctrl)
	mockHelper := helperMocks.NewMockIRpcHelper(ctrl)
	mockOnbClient := onbMocks.NewMockOnboardingClient(ctrl)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
	mockUserClient := userMocks.NewMockUsersClient(ctrl)

	// Create mock configurations
	mockPreApprovedLoanConf := &fegenconf.PreApprovedLoan{}
	mockLendingConf := &fegenconf.Lending{}

	baseProvider := baseprovider.NewBaseDeeplinkProvider(
		mockPreApprovedLoanConf,
		mockLendingConf,
		mockHelper,
		mockOnbClient,
		mockSavingsClient,
		mockUserClient,
	)

	loansProvider := &LoansProvider{
		BaseDeeplinkProvider:  baseProvider,
		preApprovedLoanClient: mockPreApprovedLoanClient,
	}

	return &testFixtures{
		mockPreApprovedLoanClient: mockPreApprovedLoanClient,
		mockHelper:                mockHelper,
		mockOnbClient:             mockOnbClient,
		mockSavingsClient:         mockSavingsClient,
		mockUserClient:            mockUserClient,
		loansProvider:             loansProvider,
	}
}

// Helper functions for common assertions
func assertValidResponse(t *testing.T, result *provider.GetDashboardLoanApplicationDetailsResponse, expectedComponentLen int) {
	t.Helper()
	assert.NotNil(t, result)
	assert.NotNil(t, result.GetActiveApplicationCards())

	components := result.GetActiveApplicationCards().GetComponents()
	assert.Len(t, components, expectedComponentLen)
}

func assertNilResponse(t *testing.T, result *provider.GetDashboardLoanApplicationDetailsResponse) {
	t.Helper()
	assert.Nil(t, result)
}

// Mock preparation functions
func prepareMockInitiateESignError(f *testFixtures) {
	f.mockPreApprovedLoanClient.EXPECT().
		InitiateESign(gomock.Any(), gomock.Any()).
		Return(&palBePb.InitiateESignResponse{
			Status: rpc.StatusInternal(),
		}, status.Error(codes.Internal, "internal error")).
		Times(1)
}

func prepareMockInitiateESignSuccess(f *testFixtures, signURL string) {
	f.mockPreApprovedLoanClient.EXPECT().
		InitiateESign(gomock.Any(), gomock.Any()).
		Return(&palBePb.InitiateESignResponse{
			Status:  rpc.StatusOk(),
			SignUrl: signURL,
		}, nil).
		Times(1)
}

func TestLoansProvider_GetLoanApplicationDetailsForDashboard(t *testing.T) {
	type args struct {
		ctx       context.Context
		lh        *palPbFeEnums.LoanHeader
		lr        *palBePb.LoanRequest
		lses      []*palBePb.LoanStepExecution
		loanOffer *palBePb.LoanOffer
	}

	type expectedOutput struct {
		wantErr          bool
		wantNilResult    bool
		wantComponentLen int
	}

	tests := []struct {
		name     string
		args     args
		expected expectedOutput
		prepare  func(*testFixtures)
	}{
		{
			name: "KFS step with InitiateESign error - should log error but not fail",
			args: args{
				ctx:       context.Background(),
				lh:        testLoanHeader,
				lr:        testLoanRequest,
				lses:      testLoanStepExecutions,
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 1, // Only base card, no PWA copy component
			},
			prepare: prepareMockInitiateESignError,
		},
		{
			name: "KFS step with successful InitiateESign - should add PWA copy component",
			args: args{
				ctx:       context.Background(),
				lh:        testLoanHeader,
				lr:        testLoanRequest,
				lses:      testLoanStepExecutions,
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 2, // Base card + PWA copy component
			},
			prepare: func(f *testFixtures) {
				prepareMockInitiateESignSuccess(f, testSignURL)
			},
		},
		{
			name: "KFS step with successful InitiateESign but missing sign URL - should not add PWA copy component",
			args: args{
				ctx:       context.Background(),
				lh:        testLoanHeader,
				lr:        testLoanRequest,
				lses:      testLoanStepExecutions,
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:          false,
				wantNilResult:    false,
				wantComponentLen: 1, // Only base card
			},
			prepare: func(f *testFixtures) {
				prepareMockInitiateESignSuccess(f, "") // Empty sign URL
			},
		},
		{
			name: "Nil loan request - should return nil",
			args: args{
				ctx:       context.Background(),
				lh:        &palPbFeEnums.LoanHeader{},
				lr:        nil,
				lses:      nil,
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:       false,
				wantNilResult: true,
			},
			prepare: func(f *testFixtures) {
				// No mocks needed for nil loan request
			},
		},
		{
			name: "SUCCESS status loan request - should return nil",
			args: args{
				ctx: context.Background(),
				lh:  &palPbFeEnums.LoanHeader{},
				lr: &palBePb.LoanRequest{
					Status: palBePb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS,
				},
				lses:      nil,
				loanOffer: testLoanOffer,
			},
			expected: expectedOutput{
				wantErr:       false,
				wantNilResult: true,
			},
			prepare: func(f *testFixtures) {
				// No mocks needed for SUCCESS status
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fixtures := setupTestFixtures(t)

			if tt.prepare != nil {
				tt.prepare(fixtures)
			}

			result, err := fixtures.loansProvider.GetLoanApplicationDetailsForDashboard(
				tt.args.ctx,
				tt.args.lh,
				tt.args.lr,
				tt.args.lses,
				tt.args.loanOffer,
			)

			// Validate error expectation
			if tt.expected.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)

			// Validate nil result expectation
			if tt.expected.wantNilResult {
				assertNilResponse(t, result)
				return
			}

			// Validate non-nil result structure
			assertValidResponse(t, result, tt.expected.wantComponentLen)
		})
	}
}
