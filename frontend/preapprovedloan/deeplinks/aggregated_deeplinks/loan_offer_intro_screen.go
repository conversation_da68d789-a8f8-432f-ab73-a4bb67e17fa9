// nolint:dupl
package aggregated_deeplinks

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	"github.com/epifi/gamma/pkg/feature/release"
	helper2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palPbFeEnums "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	uiFrontend "github.com/epifi/gamma/frontend/preapprovedloan/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

const chatbotScreenNameKey = "screen_name"

var popularQuestionsComponent = &palTypesPb.LoansScreenUiComponents{
	Component: &palTypesPb.LoansScreenUiComponents_FaqsScrollableCardsViewComponent{
		FaqsScrollableCardsViewComponent: &palTypesPb.FaqsScrollableCardsViewComponent{
			TopMargin:      48,
			ComponentTitle: provider.GetText("Popular Questions", "#313234", commontypes.FontStyle_HEADLINE_S),
			Faqs: []*deeplinkPb.InfoItemV3{
				{
					BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
					Title:   provider.GetText("Will I need any paperwork?", "#313234", commontypes.FontStyle_HEADLINE_S),
					Desc:    provider.GetText("You can get your loan without any paperwork required.", "#929599", commontypes.FontStyle_BODY_XS),
				},
				{
					BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
					Title:   provider.GetText("Will there be any charges if I close my loan early?", "#313234", commontypes.FontStyle_HEADLINE_S),
					Desc:    provider.GetText("Preclosure charges vary from lender to lender. Please read the loan documents carefully before accepting the loan.", "#929599", commontypes.FontStyle_BODY_XS),
				},
			},
		},
	},
}

func (adp *AggregatedDeeplinkProvider) GetLoanOfferIntroScreen(ctx context.Context, req *provider.LandingInfoRequest) (*deeplinkPb.Deeplink, error) {
	toolbarRightCta := ui.NewITC().WithLeftImageUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/grey_question_info_icon.png", 16, 16).WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Get help", "#6A6D70", commontypes.FontStyle_HEADLINE_M)).WithDeeplink(&deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_HELP_MAIN,
	}).WithContainerPadding(4, 17, 4, 8).WithContainerBackgroundColor("#FFFFFF").WithContainerCornerRadius(16)
	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, adp.lendingConf.EligibilityNuggetBotFeature()) {
		toolbarRightCta = ui.NewITC().WithLeftImageUrlHeightAndWidth(uiFrontend.GreyMessageIcon, 24, 24).WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Confused? Ask our expert", "#929599", commontypes.FontStyle_HEADLINE_M)).WithDeeplink(helper2.GetChatbotEntryPointDeeplink(map[string]string{chatbotScreenNameKey: deeplinkPb.Screen_LOANS_OFFER_INTRO_SCREEN.String()})).WithContainerPadding(4, 12, 4, 8).WithContainerBackgroundColor("#FFFFFF").WithContainerCornerRadius(16)
	}
	screenOptionsLoanOfferScreen := &palTypesPb.LoansOfferScreenOptions{
		ToolbarRightCta: toolbarRightCta,
		Components:      []*palTypesPb.LoansScreenUiComponents{},
		CtaBanner: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				provider.GetText("Fi is trusted by 35+ Lakh Users ", "#007A56", commontypes.FontStyle_BUTTON_XS),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       "#DCF3EE",
				LeftPadding:   20,
				RightPadding:  20,
				TopPadding:    8,
				BottomPadding: 8,
			},
			LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GreenPeopleIcon, 14, 14),
		},
	}

	loansPreQualOfferRelConst := release.NewCommonConstraintData(typesv2.Feature_FEATURE_LOANS_PREQUAL_OFFER_FLOW).WithActorId(req.ActorId)
	isLoansPreQualOfferFlowEnabled, err := adp.releaseEvaluator.Evaluate(ctx, loansPreQualOfferRelConst)
	if err != nil {
		return nil, errors.Wrap(err, "failed to evaluate feature")
	}

	dpMultiOffer, filteredOptions, errMultiOffer := adp.GetMultiOfferScreenWithSupportedOffers(ctx, req.LoanOptions, nil, req.ActorId)
	if errMultiOffer != nil {
		return nil, errors.Wrap(errMultiOffer, "failed to get multi offer screen deeplink")
	}
	if len(filteredOptions) == 0 {
		return nil, errors.New("no loan options provided")
	}
	if dpMultiOffer != nil && len(filteredOptions) > 1 && !adp.lendingConf.PreApprovedLoan().RestrictShowingMultipleOffers(ctx) {
		seeMoreOffersComponent := getSeeMoreOffersComponent(len(filteredOptions), dpMultiOffer)
		screenOptionsLoanOfferScreen.Components = append(screenOptionsLoanOfferScreen.GetComponents(), seeMoreOffersComponent)
	}
	if len(req.GetRejectedLoanOffers()) > 0 {
		rejectedLoanOffersComponent := getRejectedLoanOffersComponent(req.GetRejectedLoanOffers())
		screenOptionsLoanOfferScreen.Components = append(screenOptionsLoanOfferScreen.GetComponents(), rejectedLoanOffersComponent)
	}
	screenOptionsLoanOfferScreen.Components = append(screenOptionsLoanOfferScreen.GetComponents(), popularQuestionsComponent)

	dlProvider, err := adp.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
		Vendor:      helper.GetPalFeVendorFromBe(filteredOptions[0].GetLoanHeader().GetVendor()),
		LoanProgram: helper.GetFeLoanProgramFromBe(filteredOptions[0].GetLoanHeader().GetLoanProgram()),
	})
	if err != nil {
		return nil, errors.Wrap(err, "error while getting deeplink provider in base")
	}

	var errUpdateScreen error
	switch {
	case filteredOptions[0].GetEligibilityHeader() != nil:
		screenOptionsLoanOfferScreen, errUpdateScreen = adp.UpdateDeepLinkForEligibilityScreen(ctx, filteredOptions[0].GetEligibilityHeader(), screenOptionsLoanOfferScreen)
	case filteredOptions[0].GetLoanOffer() != nil:
		// this case is for backward compatibility in case loan options are not coming
		switch filteredOptions[0].GetLoanOffer().GetLoanOfferType() {
		case palPb.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED:
			screenOptionsLoanOfferScreen, errUpdateScreen = adp.UpdateDeepLinkForPreQualLOScreen(ctx, dlProvider, filteredOptions[0].GetLoanOffer(), screenOptionsLoanOfferScreen, isLoansPreQualOfferFlowEnabled)
		case palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD:
			screenOptionsLoanOfferScreen, errUpdateScreen = adp.UpdateDeepLinkForHardLOScreen(ctx, dlProvider, filteredOptions[0].GetLoanOffer(), screenOptionsLoanOfferScreen, isLoansPreQualOfferFlowEnabled)
		default:
			screenOptionsLoanOfferScreen, errUpdateScreen = adp.UpdateDeepLinkForSoftLOScreen(ctx, dlProvider, filteredOptions[0].GetLoanOffer(), screenOptionsLoanOfferScreen, isLoansPreQualOfferFlowEnabled)
		}
	}
	if errUpdateScreen != nil {
		return nil, errors.Wrap(errUpdateScreen, "failed to update screen for loan offers screen")
	}

	loDeepLink := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OFFER_INTRO_SCREEN, screenOptionsLoanOfferScreen)

	return loDeepLink, nil
}

func (adp *AggregatedDeeplinkProvider) UpdateDeepLinkForEligibilityScreen(ctx context.Context, lh *palPb.LoanHeader, screenOptions *palTypesPb.LoansOfferScreenOptions) (*palTypesPb.LoansOfferScreenOptions, error) {
	rpcReq, err := anyPb.New(&palFePb.CheckLoanEligibilityRequest{
		LoanHeader: helper.GetFeLoanHeaderByBeLoanHeader(lh),
	})
	if err != nil {
		return nil, errors.Wrap(err, "cannot convert check loan eligibility request to any type")
	}
	screenOptions.PrimaryLoansCta = &palTypesPb.LoansCta{
		CtaContent: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Continue",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		},
		CtaAction: &palTypesPb.LoansCtaAction{Action: &palTypesPb.LoansCtaAction_CallRpc_{CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
			RpcName:    palTypesPb.LoansCtaAction_RPC_NAME_CHECK_LOAN_ELIGIBILITY,
			RpcRequest: rpcReq,
		}}},
	}
	screenOptions.ScreenBgColor = widget.GetBlockBackgroundColour("#E6E9ED")
	screenOptions.Components = append([]*palTypesPb.LoansScreenUiComponents{adp.getPreQualLoanOfferCardComponent(ctx, baseprovider.GetDummyOfferForEligibility(lh).GetOfferConstraints().GetMaxLoanAmount(), lh.GetVendor(), lh.GetLoanProgram())}, screenOptions.GetComponents()...)

	return screenOptions, nil
}

func (adp *AggregatedDeeplinkProvider) UpdateDeepLinkForPreQualLOScreen(ctx context.Context, dlProvider provider.IDeeplinkProvider, loanOffer *palPb.LoanOffer,
	screenOptions *palTypesPb.LoansOfferScreenOptions, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoansOfferScreenOptions, error) {
	screenOptions.PrimaryCta = adp.getPrimaryCta("Continue", loanOffer)

	loanCta, err := dlProvider.GetPreQualOfferLandingScreenLoansCta(loanOffer)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting loan cta")
	}
	if isLoansPreQualOfferFlowEnabled {
		screenOptions.PrimaryLoansCta = loanCta
	}
	screenOptions.ScreenBgColor = widget.GetBlockBackgroundColour("#E6E9ED")
	screenOptions.Components = append([]*palTypesPb.LoansScreenUiComponents{adp.getPreQualLoanOfferCardComponent(ctx, loanOffer.GetOfferConstraints().GetMaxLoanAmount(), loanOffer.GetVendor(), loanOffer.GetLoanProgram())}, screenOptions.GetComponents()...)

	return screenOptions, nil
}

func (adp *AggregatedDeeplinkProvider) getPreQualLoanOfferCardComponent(ctx context.Context, maxAmount *moneyPb.Money, vendor palPb.Vendor, loanProgram palPb.LoanProgram) *palTypesPb.LoansScreenUiComponents {
	var cardTitle *commontypes.Text
	var cardTopBadge *palTypesPb.CardTopBadge
	// TODO (sharath): create an interface for fetching offer card component and remove this if condition
	if loanProgram == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2 {
		appVersionConstraintData := release.NewAppVersionConstraintData(adp.lendingConf.PreApprovedLoan().BadgeOnOfferIntroScreenConfig())
		isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
		if appVerErr != nil {
			logger.Error(ctx, "error in checking app version for BadgeOnOfferIntroScreenConfig", zap.Error(appVerErr))
			isAppVersionGreater = false
		}
		if isAppVersionGreater {
			cardTopBadge = getOfferIntroCardTopBadge("EARLY SALARY")
		} else {
			cardTitle = provider.GetText("Early Salary", "#313234", commontypes.FontStyle_HEADLINE_2)
		}
	}
	return &palTypesPb.LoansScreenUiComponents{
		Component: &palTypesPb.LoansScreenUiComponents_LoanOfferCardComponent{
			LoanOfferCardComponent: &palTypesPb.LoanOfferCardComponent{
				CardTitle:    cardTitle,
				CardTopBadge: cardTopBadge,
				CardBgColor:  "#FFFFFF",
				OfferDetailsCard: &palTypesPb.LoanOfferCardComponent_OfferDetailsCard{
					OfferTitleV2: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							provider.GetText("Your loan is a few steps away", "#313234", commontypes.FontStyle_HEADLINE_2),
						},
					},
					OfferAmountDetails: &palTypesPb.LoanOfferCardComponent_OfferAmountDetails{
						OfferSubtitle: provider.GetText("You may be qualified for up to:", "#6A6D70", commontypes.FontStyle_HEADLINE_S),
						OfferAmount: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								provider.GetText("₹", "#8D8D8D", commontypes.FontStyle_CURRENCY_XL),
								provider.GetText(money.ToDisplayStringInIndianFormat(maxAmount, 0, false), "#313234", commontypes.FontStyle_NUMBER_3XL),
							},
						},
					},
					OfferDescriptionChips: getPreQualOfferDescriptionChips(vendor, loanProgram),
				},
				OfferProgressStageCard: &palTypesPb.LoanOfferCardComponent_OfferProgressCard{
					BgColor: "#EFF2F6",
					StageProgress: &palTypesPb.SectionTypeProgress{
						ProgressComponents: []*palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
							{
								Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.MoneyBagIcon, 27, 27),
								Text:               provider.GetText("Get approved\nby partner", "#313234", commontypes.FontStyle_SUBTITLE_XS),
								IsCompleted:        true,
								FollowUpLineColour: "#BCDCE7",
							},
							{
								Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ConfirmDetailsIcon, 27, 27),
								Text:               provider.GetText("Verify\ndetails", "#313234", commontypes.FontStyle_SUBTITLE_XS),
								IsCompleted:        true,
								FollowUpLineColour: "#BCDCE7",
							},
							{
								Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GetMoneyIcon, 27, 27),
								Text:               provider.GetText("Get\nmoney ", "#313234", commontypes.FontStyle_SUBTITLE_XS),
								IsCompleted:        true,
								FollowUpLineColour: "#BCDCE7",
							},
						},
					},
					PartnerLogo: uiFrontend.GetOfferedByVendorIcon(vendor),
				},
			},
		},
	}
}

// nolint: unparam
func getPreQualOfferDescriptionChips(vendor palPb.Vendor, loanProgram palPb.LoanProgram) []*ui.IconTextComponent {
	switch loanProgram {
	case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
		return []*ui.IconTextComponent{
			{
				Texts: []*commontypes.Text{
					provider.GetText("@ 0% p.a", "#98712F", commontypes.FontStyle_SUBTITLE_S),
				},
				LeftImgTxtPadding: 4,
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					LeftPadding:   8,
					RightPadding:  8,
					TopPadding:    9,
					BottomPadding: 9,
					CornerRadius:  16,
					BgColor:       "#FFFCEB",
				},
			},
		}
	default:
		return []*ui.IconTextComponent{
			{
				LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.HighVoltageIcon, 20, 20),
				Texts: []*commontypes.Text{
					provider.GetText("Get approved within minutes", "#007A56", commontypes.FontStyle_SUBTITLE_S),
				},
				LeftImgTxtPadding: 4,
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					LeftPadding:   8,
					RightPadding:  8,
					TopPadding:    9,
					BottomPadding: 9,
					CornerRadius:  16,
					BgColor:       "#DCF3EE",
				},
			},
		}
	}
}

// nolint: unparam
func getSoftOfferDescriptionChips(offer *palPb.LoanOffer) []*ui.IconTextComponent {
	switch offer.GetLoanProgram() {
	case palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
		return []*ui.IconTextComponent{
			{
				Texts: []*commontypes.Text{
					provider.GetText("@ 0% p.a", "#98712F", commontypes.FontStyle_SUBTITLE_S),
				},
				LeftImgTxtPadding: 4,
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					LeftPadding:   8,
					RightPadding:  8,
					TopPadding:    9,
					BottomPadding: 9,
					CornerRadius:  16,
					BgColor:       "#FFFCEB",
				},
			},
		}
	default:
		return []*ui.IconTextComponent{
			{
				LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.HighVoltageIcon, 20, 20),
				Texts: []*commontypes.Text{
					provider.GetText("Takes less than 2 mins", "#007A56", commontypes.FontStyle_SUBTITLE_S),
				},
				LeftImgTxtPadding: 4,
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					LeftPadding:   8,
					RightPadding:  8,
					TopPadding:    9,
					BottomPadding: 9,
					CornerRadius:  16,
					BgColor:       "#DCF3EE",
				},
			},
		}
	}
}

func (adp *AggregatedDeeplinkProvider) UpdateDeepLinkForSoftLOScreen(ctx context.Context, dlProvider provider.IDeeplinkProvider, loanOffer *palPb.LoanOffer, screenOptions *palTypesPb.LoansOfferScreenOptions, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoansOfferScreenOptions, error) {
	loanCard, err := dlProvider.GetSoftOfferCardMultiOfferScreenComponent(ctx, loanOffer, true, isLoansPreQualOfferFlowEnabled)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting loan card")
	}
	if isLoansPreQualOfferFlowEnabled {
		screenOptions.PrimaryLoansCta = loanCard.GetLoansCta()
	}
	screenOptions.PrimaryCta = adp.getPrimaryCta("Continue", loanOffer)

	screenOptions.ScreenBgColor = widget.GetBlockBackgroundColour("#E6E9ED")
	screenOptions.BackgroundVisualElement = commontypes.GetVisualElementLottieFromUrl(uiFrontend.MoneyFallingLottie).WithRepeatCount(-1)

	moneyInString := money.ToDisplayStringInIndianFormat(loanOffer.GetOfferConstraints().GetMaxLoanAmount(), 0, false)

	cardTitle := provider.GetText("You have an offer:", "#313234", commontypes.FontStyle_HEADLINE_2)
	var cardTopBadge *palTypesPb.CardTopBadge
	// TODO (sharath): create an interface for fetching offer card component and remove this if condition
	if loanOffer.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2 {
		appVersionConstraintData := release.NewAppVersionConstraintData(adp.lendingConf.PreApprovedLoan().BadgeOnOfferIntroScreenConfig())
		isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
		if appVerErr != nil {
			logger.Error(ctx, "error in checking app version for BadgeOnOfferIntroScreenConfig", zap.Error(appVerErr))
			isAppVersionGreater = false
		}
		if isAppVersionGreater {
			cardTopBadge = getOfferIntroCardTopBadge("EARLY SALARY")
		} else {
			cardTitle = provider.GetText("Early Salary", "#313234", commontypes.FontStyle_HEADLINE_2)
		}
	}

	screenOptions.Components = append([]*palTypesPb.LoansScreenUiComponents{
		{
			Component: &palTypesPb.LoansScreenUiComponents_LoanOfferCardComponent{
				LoanOfferCardComponent: &palTypesPb.LoanOfferCardComponent{
					CardBgColor:  "#EFF2F6",
					CardTitle:    cardTitle,
					CardTopBadge: cardTopBadge,
					OfferDetailsCard: &palTypesPb.LoanOfferCardComponent_OfferDetailsCard{
						OfferAmountDetails: &palTypesPb.LoanOfferCardComponent_OfferAmountDetails{
							OfferSubtitle: provider.GetText("You can borrow upto:", "#6A6D70", commontypes.FontStyle_HEADLINE_S),
							OfferAmount: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									provider.GetText("₹", "#8D8D8D", commontypes.FontStyle_CURRENCY_XL),
									provider.GetText(moneyInString, "#313234", commontypes.FontStyle_NUMBER_3XL),
								},
							},
						},
						OfferDescriptionChips: getSoftOfferDescriptionChips(loanOffer),
					},
					OfferProgressStageCard: &palTypesPb.LoanOfferCardComponent_OfferProgressCard{
						BgColor: "#EFF2F6",
						StageProgress: &palTypesPb.SectionTypeProgress{
							ProgressComponents: []*palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.MoneyBagIcon, 27, 27),
									Text:               provider.GetText("Choose\namount", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ConfirmDetailsIcon, 27, 27),
									Text:               provider.GetText("Confirm\ndetails", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GetMoneyIcon, 27, 27),
									Text:               provider.GetText("Get\nmoney ", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
							},
						},
						PartnerLogo: uiFrontend.GetPoweredByVendorIcon(loanOffer.GetVendor()),
					},
				},
			},
		},
	}, screenOptions.GetComponents()...)

	return screenOptions, nil
}

func (adp *AggregatedDeeplinkProvider) UpdateDeepLinkForHardLOScreen(ctx context.Context, _ provider.IDeeplinkProvider, loanOffer *palPb.LoanOffer, screenOptions *palTypesPb.LoansOfferScreenOptions, isLoansPreQualOfferFlowEnabled bool) (*palTypesPb.LoansOfferScreenOptions, error) {
	screenOptions.PrimaryCta = adp.getPrimaryCta("Continue", loanOffer)
	if isLoansPreQualOfferFlowEnabled {
		screenOptions.PrimaryLoansCta = &palTypesPb.LoansCta{
			CtaContent: adp.getPrimaryCta("Continue", loanOffer),
			CtaAction: &palTypesPb.LoansCtaAction{
				Action: &palTypesPb.LoansCtaAction_CallRpc_{CallRpc: &palTypesPb.LoansCtaAction_CallRpc{
					RpcName: palTypesPb.LoansCtaAction_RPC_NAME_OFFER_DETAILS,
					CommonMetaData: &palTypesPb.LoansCtaAction_CallRpc_CommonMetaData{
						LoanHeader: &palPbFeEnums.LoanHeader{
							LoanProgram: helper.GetFeLoanProgramFromBe(loanOffer.GetLoanProgram()),
							Vendor:      helper.GetPalFeVendorFromBe(loanOffer.GetVendor()),
						},
						LoId: loanOffer.GetId(),
					},
				}},
			},
		}
	}
	vendorIcon, vendorIconWidth, vendorIconHeight, _ := uiFrontend.GetVendorImageAndText(loanOffer.GetVendor())
	screenOptions.ScreenBgColor = widget.GetBlockBackgroundColour("#E6E9ED")
	screenOptions.BackgroundVisualElement = commontypes.GetVisualElementLottieFromUrl(uiFrontend.MoneyFallingLottie).WithRepeatCount(-1)

	moneyInString := money.ToDisplayStringInIndianFormat(loanOffer.GetOfferConstraints().GetMaxLoanAmount(), 0, false)
	tenureString := strconv.Itoa(int(loanOffer.GetOfferConstraints().GetMinTenureMonths())) + "-" + strconv.Itoa(int(loanOffer.GetOfferConstraints().GetMaxTenureMonths())) + " months"
	interestRatePercentageValue := loanOffer.GetProcessingInfo().GetInterestRate()[0].GetPercentage()
	if interestRatePercentageValue == 0 {
		return nil, errors.New("interest rate percentage is zero")
	}
	if loanOffer.GetVendor() == palPb.Vendor_LENDEN {
		// LDC provides interest rate in monthly terms, but we need to show it in yearly terms for compliance reasons
		interestRatePercentageValue = math.Round(loanOffer.GetProcessingInfo().GetInterestRate()[0].GetPercentage()*12*100) / 100
	}
	var cardTitle *commontypes.Text
	var cardTopBadge *palTypesPb.CardTopBadge
	// TODO (sharath): create an interface for fetching offer card component and remove this if condition
	if loanOffer.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2 {
		appVersionConstraintData := release.NewAppVersionConstraintData(adp.lendingConf.PreApprovedLoan().BadgeOnOfferIntroScreenConfig())
		isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
		if appVerErr != nil {
			logger.Error(ctx, "error in checking app version for BadgeOnOfferIntroScreenConfig", zap.Error(appVerErr))
			isAppVersionGreater = false
		}
		if isAppVersionGreater {
			cardTopBadge = getOfferIntroCardTopBadge("EARLY SALARY")
		} else {
			cardTitle = provider.GetText("Early Salary", "#313234", commontypes.FontStyle_HEADLINE_2)
		}
	}
	screenOptions.Components = append([]*palTypesPb.LoansScreenUiComponents{
		{
			Component: &palTypesPb.LoansScreenUiComponents_LoanOfferCardComponent{
				LoanOfferCardComponent: &palTypesPb.LoanOfferCardComponent{
					CardTitle:    cardTitle,
					CardTopBadge: cardTopBadge,
					OfferDetailsCard: &palTypesPb.LoanOfferCardComponent_OfferDetailsCard{
						BgColor: "#F6F9FD",
						OfferTitleV2: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								provider.GetText("Congratulations! 🎉", "#313234", commontypes.FontStyle_HEADLINE_2),
							},
						},
						OfferAmountDetails: &palTypesPb.LoanOfferCardComponent_OfferAmountDetails{
							PartnerLogo: &ui.IconTextComponent{
								LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(vendorIcon, vendorIconHeight, vendorIconWidth),
							},
							OfferSubtitle: provider.GetText("has approved you for", "#6A6D70", commontypes.FontStyle_HEADLINE_S),
							OfferAmount: &ui.IconTextComponent{
								Texts: []*commontypes.Text{
									provider.GetText("₹", "#8D8D8D", commontypes.FontStyle_CURRENCY_XL),
									provider.GetText(moneyInString, "#313234", commontypes.FontStyle_NUMBER_3XL),
								},
							},
						},
						OfferDescriptionChips: []*ui.IconTextComponent{
							{
								Texts: []*commontypes.Text{
									provider.GetText(fmt.Sprintf("@ %.1f%% p.a.", interestRatePercentageValue), "#98712F", commontypes.FontStyle_SUBTITLE_S),
								},
								LeftImgTxtPadding: 4,
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    9,
									BottomPadding: 9,
									CornerRadius:  15,
									BgColor:       "#FFFCEB",
								},
							},
							{
								Texts: []*commontypes.Text{
									provider.GetText(fmt.Sprintf("Tenure of %s", tenureString), "#98712F", commontypes.FontStyle_SUBTITLE_S),
								},
								LeftImgTxtPadding: 4,
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{
									LeftPadding:   8,
									RightPadding:  8,
									TopPadding:    9,
									BottomPadding: 9,
									CornerRadius:  15,
									BgColor:       "#FFFCEB",
								},
							},
						},
					},
					OfferProgressStageCard: &palTypesPb.LoanOfferCardComponent_OfferProgressCard{
						BgColor: "#FFFFFF",
						StageProgress: &palTypesPb.SectionTypeProgress{
							ProgressComponents: []*palTypesPb.SectionTypeProgress_SectionTypeProgressComponent{
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GreenRightIcon, 27, 27),
									Text:               provider.GetText("Approved by\npartner", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#86BA6F",
								},
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ConfirmDetailsIcon, 27, 27),
									Text:               provider.GetText("Verify\ndetails", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
								{
									Icon:               commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.GetMoneyIcon, 27, 27),
									Text:               provider.GetText("Get\nmoney ", "#313234", commontypes.FontStyle_SUBTITLE_XS),
									IsCompleted:        true,
									FollowUpLineColour: "#BCDCE7",
								},
							},
						},
					},
				},
			},
		},
	}, screenOptions.GetComponents()...)

	return screenOptions, nil
}

func (adp *AggregatedDeeplinkProvider) getPrimaryCta(ctaText string, loanOffer *palPb.LoanOffer) *deeplinkPb.Cta {
	var nextActionCta *deeplinkPb.Deeplink
	if loanOffer.GetLoanOfferType() != palPb.LoanOfferType_LOAN_OFFER_TYPE_PRE_QUALIFIED {
		nextActionCta = deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_OFFER_DETAILS_SCREEN, &palTypesPb.LoansOfferDetailsScreenOptions{
			LoanHeader: &palPbFeEnums.LoanHeader{
				LoanProgram: helper.GetFeLoanProgramFromBe(loanOffer.GetLoanProgram()),
				Vendor:      helper.GetPalFeVendorFromBe(loanOffer.GetVendor()),
			},
			OfferId: loanOffer.GetId(),
		})
	}

	return &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         ctaText,
		Deeplink:     nextActionCta,
		Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}
}

func getSeeMoreOffersComponent(loanOffers int, deeplinkMultiOfferScreen *deeplinkPb.Deeplink) *palTypesPb.LoansScreenUiComponents {
	var offerText string
	if loanOffers-1 > 1 {
		offerText = fmt.Sprintf("See %d other offers", loanOffers-1)
	} else {
		offerText = fmt.Sprintf("See %d other offer", loanOffers-1)
	}

	return &palTypesPb.LoansScreenUiComponents{
		Component: &palTypesPb.LoansScreenUiComponents_ViewMoreOfferCardComponent{
			ViewMoreOfferCardComponent: &palTypesPb.ViewMoreOfferCardComponent{
				BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
				OfferDescription: &ui.VerticalKeyValuePair{
					Title: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							provider.GetText(offerText, "#313234", commontypes.FontStyle_HEADLINE_S),
						},
						LeftImgTxtPadding: 12,
						LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.MultiLenderLogo, 40, 50),
						Deeplink:          deeplinkMultiOfferScreen,
					},
				},
				ActionIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ChevronIcon, 32, 32),
			},
		},
	}
}

func getRejectedLoanOffersComponent(rejectedLoanOffers []*palPb.LoanHeader) *palTypesPb.LoansScreenUiComponents {
	var keyValuePairs []*palTypesPb.KeyValueRow
	for _, rejectedOffer := range rejectedLoanOffers {
		loanProgramText := uiFrontend.GetLoanProgramDisplayText(rejectedOffer.GetLoanProgram())
		vendorIcon, imgWidth, imgHeight, vendorName := uiFrontend.GetVendorSquareImageAndText(rejectedOffer.GetVendor())
		maxLoanAmountToBeShown := baseprovider.GetDummyOfferForEligibility(rejectedOffer).GetOfferConstraints().GetMaxLoanAmount()
		maxAmountStr := "upto " + money.ToDisplayStringInIndianFormat(maxLoanAmountToBeShown, 0, true)
		var tagToBeShown *ui.IconTextComponent
		if loanProgramText != "" {
			tagToBeShown = &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					provider.GetText(loanProgramText, "#6A6D70", commontypes.FontStyle_OVERLINE_2XS_CAPS),
				},
				ContainerProperties: &ui.IconTextComponent_ContainerProperties{
					LeftPadding:      8,
					RightPadding:     8,
					TopPadding:       2,
					BottomPadding:    2,
					CornerRadius:     12,
					BackgroundColour: widget.GetBlockBackgroundColour("#EFF2F6"),
				},
			}
		}

		keyValuePair := &palTypesPb.KeyValueRow{
			BgColor: "",
			Key: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					provider.GetText(vendorName, "#313234", commontypes.FontStyle_HEADLINE_S),
				},
				LeftImgTxtPadding: 8,
				LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(vendorIcon, imgHeight, imgWidth),
			},
			Value: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					provider.GetText(maxAmountStr, "#313234", commontypes.FontStyle_HEADLINE_S),
				},
			},
			Tag: tagToBeShown,
		}
		keyValuePairs = append(keyValuePairs, keyValuePair)
	}

	rejectedOfferBottomSheet := &palTypesPb.LoansKeyValueRowsBottomSheetScreenOptions{
		Header:       nil,
		LoanHeader:   nil,
		BgColor:      "#FFFFFF",
		KeyValueInfo: keyValuePairs,
		VisualElementTitleSubtitleElement: &widget.VisualElementTitleSubtitleElement{
			VisualElement:   commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.NoOfferIcon, 80, 80),
			TitleText:       provider.GetText("Rejected offers", "#313234", commontypes.FontStyle_HEADLINE_XL),
			BackgroundColor: "#FFFFFF",
		},
		Cta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_DONE,
			Text:         "Ok, got it",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		},
	}
	rejectedOfferBottomSheetDeepLink := deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_KEY_VALUE_ROWS_BOTTOM_SHEET, rejectedOfferBottomSheet)

	return &palTypesPb.LoansScreenUiComponents{
		Component: &palTypesPb.LoansScreenUiComponents_ViewMoreOfferCardComponent{
			ViewMoreOfferCardComponent: &palTypesPb.ViewMoreOfferCardComponent{
				BgColor: widget.GetBlockBackgroundColour("#EFF2F6"),
				OfferDescription: &ui.VerticalKeyValuePair{
					Title: &ui.IconTextComponent{
						Texts: []*commontypes.Text{
							provider.GetText("Rejected offers", "#313234", commontypes.FontStyle_HEADLINE_S),
						},
						Deeplink: rejectedOfferBottomSheetDeepLink,
					},
				},
				ActionIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(uiFrontend.ChevronIcon, 32, 32),
			},
		},
	}
}

func (adp *AggregatedDeeplinkProvider) isOfferDetailsV4FlowEnabled(ctx context.Context, lh *palPbFeEnums.LoanHeader) (bool, error) {
	conf := adp.lendingConf.PreApprovedLoan().OfferDetailsV4Config()
	if !conf.IsEnabled(ctx) {
		return false, nil
	}
	appVersionConstraintData := release.NewAppVersionConstraintData(adp.lendingConf.PreApprovedLoan().OfferDetailsV4Config().AppVersionConstraintConfig())
	isAppVersionGreater, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		return false, errors.Wrap(appVerErr, "failed to evaluate app version constraint for overall multi offer screen")
	}
	if !isAppVersionGreater {
		return false, nil
	}

	if lhConf := conf.VendorLoanProgramMap().Get(strings.Join([]string{
		lh.GetVendor().String(),
		lh.GetLoanProgram().String(),
	}, ":")); lhConf != nil && lhConf.IsEnabled(ctx) {
		if lhConf.AppVersionConstraintConfig() != nil {
			appVersionConstraintData = release.NewAppVersionConstraintData(lhConf.AppVersionConstraintConfig())
			isAppVersionGreater, appVerErr = release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
			if appVerErr != nil {
				return false, errors.Wrap(appVerErr, fmt.Sprintln("failed to evaluate app version constraint for multi offer screen for vendor: ", lh.GetVendor().String(), ", program: ", lh.GetLoanProgram().String()))
			}
			if !isAppVersionGreater {
				return false, nil
			}
		}

		return true, nil
	}
	return false, nil
}

func (adp *AggregatedDeeplinkProvider) GetMultiOfferScreenWithSupportedOffers(ctx context.Context, loanOptions []*palPb.LoanOption, currentLr *palPb.LoanRequest, actorId string) (*deeplinkPb.Deeplink, []*palPb.LoanOption, error) {
	appVersionConstraintData := release.NewAppVersionConstraintData(adp.lendingConf.PreApprovedLoan().ShowEligibilityInMultiOfferConfig())
	showEligInMultiOffer, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		return nil, nil, errors.Wrap(appVerErr, "failed to evaluate app version constraint for showing eligibility in multi offer screen")
	}
	var filteredOptions []*palPb.LoanOption
	for _, lo := range loanOptions {
		if lo.GetEligibilityHeader() != nil && !showEligInMultiOffer {
			continue
		}
		isEnabled, err := adp.isOfferDetailsV4FlowEnabled(ctx, helper.GetFeLoanHeaderByBeLoanHeader(lo.GetLoanHeader()))
		if err != nil {
			return nil, nil, errors.Wrap(err, fmt.Sprintf("failed to evaluate if multi offer screen is live for particular loan header: %v", lo.GetLoanHeader()))
		}
		if isEnabled {
			filteredOptions = append(filteredOptions, lo)
		}
	}
	if len(filteredOptions) == 0 {
		return nil, nil, nil
	}

	loansPreQualOfferRelConst := release.NewCommonConstraintData(typesv2.Feature_FEATURE_LOANS_PREQUAL_OFFER_FLOW)
	isLoansPreQualOfferFlowEnabled, err := adp.releaseEvaluator.Evaluate(ctx, loansPreQualOfferRelConst)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to evaluate feature")
	}
	dl, err := adp.GetMultiOfferScreen(ctx, filteredOptions, isLoansPreQualOfferFlowEnabled, currentLr, actorId)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to get multi offer screen")
	}
	return dl, filteredOptions, nil
}

func getOfferIntroCardTopBadge(text string) *palTypesPb.CardTopBadge {
	return &palTypesPb.CardTopBadge{
		Text: provider.GetText(text, "#38393B", commontypes.FontStyle_OVERLINE_XS_CAPS),
		BgColorGradient: &widget.LinearGradient{
			Degree: 0,
			LinearColorStops: []*widget.ColorStop{
				{
					Color:          "#AFD2A2",
					StopPercentage: 30,
				},
				{
					Color:          "#D5E6CE",
					StopPercentage: 60,
				},
				{
					Color:          "#AFD2A2",
					StopPercentage: 60,
				},
			},
		},
	}
}
