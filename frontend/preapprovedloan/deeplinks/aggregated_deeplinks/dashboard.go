package aggregated_deeplinks

import (
	"context"
	"fmt"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider/baseprovider"
	uiFrontend "github.com/epifi/gamma/frontend/preapprovedloan/ui"
	"github.com/epifi/gamma/pkg/feature/release"
	helper2 "github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	money2 "github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	beHelper "github.com/epifi/gamma/preapprovedloan/helper"
)

type DashboardDeeplinkRes struct {
	Deeplink       *deeplinkPb.Deeplink
	EventSubStatus string
}

// nolint:funlen
func (adp *AggregatedDeeplinkProvider) GetLoanDashboardDeepLinkV3(ctx context.Context, res *palBePb.GetDashboardResponse) (*DashboardDeeplinkRes, error) {
	screenOptions := &palTypesPb.LoansDashboardScreenOptions{
		PageTitle:     provider.GetText("Your loans", "#F6F9FD", commontypes.FontStyle_HEADLINE_M),
		PageSubtitle:  nil,
		TopSection:    &palTypesPb.LoanDashboardTopSection{},
		BottomSection: &palTypesPb.LoanDashboardBottomSection{Divisions: []*palTypesPb.LoanDashboardBottomSection_SectionDivision{}},
	}

	// Top section
	accountDetailsResp, laDetailsErr := adp.getLoanAccountsDetailsForLoanDashboard(ctx, res.GetLoanInfoList())
	if laDetailsErr != nil {
		return nil, errors.Wrap(laDetailsErr, "unable to get loan account details for given loan info list")
	}

	topSectionComponent, tscErr := adp.getLoanDashboardTopSectionComponent(accountDetailsResp, res.GetLoanSteps(), res.GetRecentLoanRequest())
	if tscErr != nil {
		return nil, errors.Wrap(laDetailsErr, "unable to get top section component for dashboard")
	}

	screenOptions.GetTopSection().Divisions = []*palTypesPb.LoanDashboardTopSection_SectionDivision{
		{
			Components: []*palTypesPb.LoanDashboardTopSection_Component{
				topSectionComponent,
			},
		},
	}

	// Bottom section
	var applicationDetailsResp *provider.GetDashboardLoanApplicationDetailsResponse
	if res.GetRecentLoanRequest() != nil {
		loanApplicationDlProvider, _ := adp.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
			Vendor:      helper.GetPalFeVendorFromBe(res.GetRecentLoanRequest().GetVendor()),
			LoanProgram: helper.GetFeLoanProgramFromBe(res.GetRecentLoanRequest().GetLoanProgram()),
		})
		var appDetailsErr error
		var corresOffer *palBePb.LoanOffer
		for _, lo := range res.GetLoanOptions() {
			if lo.GetLoanOffer().GetId() == res.GetRecentLoanRequest().GetOfferId() {
				corresOffer = lo.GetLoanOffer()
			}
		}
		applicationDetailsResp, appDetailsErr = loanApplicationDlProvider.GetLoanApplicationDetailsForDashboard(ctx, loanApplicationDlProvider.GetLoanHeader(), res.GetRecentLoanRequest(), res.GetLoanSteps(), corresOffer)
		if appDetailsErr != nil {
			return nil, errors.Wrap(appDetailsErr, "unable to get loan account details for given loan info list")
		}
	}

	eventSubStatus := ""
	// 1. Active application/eligibility
	if applicationDetailsResp.GetActiveApplicationCards() != nil {
		screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), applicationDetailsResp.GetActiveApplicationCards())
		var activeLse *palBePb.LoanStepExecution
		if len(res.GetLoanSteps()) > 0 {
			activeLse = res.GetLoanSteps()[0]
		}
		isCancellable := baseprovider.IsApplicationCancellable(res.GetRecentLoanRequest(), activeLse)
		switch {
		case res.GetRecentLoanRequest().IsFailedTerminal():
			eventSubStatus = "app_terminal_"
		case isCancellable:
			eventSubStatus = "app_cancellable_"
		default:
			eventSubStatus = "app_non_cancellable_"
		}
	}

	// 2. Other loan options
	otherOptionsAvailable := areOtherOptionsAvailable(res.GetLoanOptions(), res.GetRecentLoanRequest())
	if otherOptionsAvailable && adp.isAllowedToTakeNewLoan(res.GetLoanInfoList()) {
		exploreOffersDiv, err := adp.getExploreOffersDivision(ctx, res, applicationDetailsResp)
		if err != nil {
			return nil, errors.Wrap(err, "error in getExploreOffersDivision")
		}
		if exploreOffersDiv != nil {
			screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), exploreOffersDiv)
		}
		eventSubStatus += "options_available"
	} else {
		eventSubStatus += "options_unavailable"
	}

	// 3. Active account
	if accountDetailsResp.getDisbursalBanner() != nil {
		screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), accountDetailsResp.getDisbursalBanner())
	}
	if accountDetailsResp.getActiveLoanCards() != nil {
		screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), accountDetailsResp.getActiveLoanCards())
		eventSubStatus += "_active_loan"
	}

	// 4. Support
	screenOptions.GetBottomSection().Divisions = append(screenOptions.GetBottomSection().GetDivisions(), &palTypesPb.LoanDashboardBottomSection_SectionDivision{
		Title: ui.NewITC().WithTexts(provider.GetText("Support", "#6A6D70", commontypes.FontStyle_HEADLINE_S)),
		Components: []*palTypesPb.LoanDashboardBottomSection_Component{
			// TODO: previous loans row
			adp.getLoanDashboardFaqComponent(ctx, res),
		},
	})

	dl, dlErr := deeplinkV3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_DASHBOARD_SCREEN, screenOptions)
	if dlErr != nil {
		return nil, errors.Wrap(dlErr, "unable to create loans dashboard deeplinkV3")
	}
	return &DashboardDeeplinkRes{
		Deeplink:       dl,
		EventSubStatus: eventSubStatus,
	}, nil
}

func areOtherOptionsAvailable(opts []*palBePb.LoanOption, currentLr *palBePb.LoanRequest) bool {
	// if current loan request is terminal and there is at least one option, then user can start a new loan request
	if len(opts) > 0 && currentLr.IsFailedTerminal() {
		return true
	}
	for _, opt := range opts {
		if currentLr.GetLoanProgram() == palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY {
			// if current loan request is for eligibility and there is an option which is not eligibility, then return true
			// this is being done because all the eligibility options will lead to the same flow and there is no point in showing multiple options
			if opt.GetEligibilityHeader() == nil {
				return true
			}
			continue
		}
		// if current loan request is not eligibility, return true only if there is different vendor:program option available
		if currentLr.GetVendor() != opt.GetLoanHeader().GetVendor() || currentLr.GetLoanProgram() != opt.GetLoanHeader().GetLoanProgram() {
			return true
		}
	}
	return false
}

// isAllowedToTakeNewLoan returns true if user can take a new loan by checking the existing loan accounts against the concurrent loans constraints
func (adp *AggregatedDeeplinkProvider) isAllowedToTakeNewLoan(loanInfos []*palBePb.LoanInfo) bool {
	// TODO (sharath): allow user to take other loan types without freeze period check
	for _, info := range loanInfos {
		if info.GetLoanAccount().GetStatus() != palBePb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
			continue
		}
		if !adp.lendingConf.ConcurrentLoansConfig().Enabled() {
			return false
		}
		freezeDuration := adp.lendingConf.ConcurrentLoansConfig().LoanTypeToMinFreezeDuration().Get(beHelper.GetLoanTypeFromLoanProgram(info.GetLoanAccount().GetLoanProgram()).String())
		if info.GetLoanAccount().GetCreatedAt().AsTime().Add(freezeDuration).After(time.Now()) {
			return false
		}
		// it is ok to check the first active account and break because loanInfos are ordered in desc order by created_at
		break
	}
	return true
}

func (adp *AggregatedDeeplinkProvider) getExploreOffersDivision(ctx context.Context, res *palBePb.GetDashboardResponse, applicationDetailsResp *provider.GetDashboardLoanApplicationDetailsResponse) (*palTypesPb.LoanDashboardBottomSection_SectionDivision, error) {
	appVersionConstraintData := release.NewAppVersionConstraintData(adp.lendingConf.PreApprovedLoan().AutoCancelCurrentLrConfig())
	isAppVersionCompatible, appVerErr := release.NewAppVersionConstraint().Evaluate(ctx, appVersionConstraintData, nil)
	if appVerErr != nil {
		return nil, errors.Wrap(appVerErr, "unable to evaluate app version constraint")
	}

	if !isAppVersionCompatible {
		loBanner, loBannerErr := adp.getLoanOfferBanner(ctx, res.GetActiveLoanOffer(),
			applicationDetailsResp.GetShowOfferBannerAccordingToLseDashboardMap() && res.GetCheckUserEligibility())
		if loBannerErr != nil {
			return nil, errors.Wrap(loBannerErr, "unable to get loan offer banner")
		}
		return loBanner, nil
	}

	exploreDl := deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_MULTIPLE_OFFER_SELECTION_SCREEN, &palTypesPb.LoansMultipleOfferSelectionScreenOptions{
		GetDataFromRpcCall: true,
	})
	var activeLse *palBePb.LoanStepExecution
	if len(res.GetLoanSteps()) > 0 {
		activeLse = res.GetLoanSteps()[0]
	}
	ctaColor := "#00B899"
	// if there is an active loan application request in non-cancellable state, do not show multi offer screen
	if res.GetRecentLoanRequest() != nil && res.GetRecentLoanRequest().GetCompletedAt() == nil && !baseprovider.IsApplicationCancellable(res.GetRecentLoanRequest(), activeLse) {
		exploreDl = baseprovider.GetApplicationNonCancellableBottomSheet()
		ctaColor = "#B2B5B9"
	}

	return &palTypesPb.LoanDashboardBottomSection_SectionDivision{
		Title: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Start a new loan application", "#6A6D70", commontypes.FontStyle_HEADLINE_S)),
		Components: []*palTypesPb.LoanDashboardBottomSection_Component{
			{
				Component: &palTypesPb.LoanDashboardBottomSection_Component_BannerWithLeftIconAndRightCta{
					BannerWithLeftIconAndRightCta: &palTypesPb.BannerWithLeftIconAndRightCta{
						LeftIcon: provider.GetVisualElementPng(uiFrontend.MultiLenderLogo, 50, 40),
						Title:    ui.NewITC().WithTexts(provider.GetText("Compare & choose from other offers", "#313234", commontypes.FontStyle_SUBTITLE_XS)),
						RightCta: ui.NewITC().WithTexts(provider.GetText("Explore offers", ctaColor, commontypes.FontStyle_BUTTON_S)).WithDeeplink(exploreDl),
						BgColor:  "#FFFFFF",
					},
				},
			},
		},
	}, nil
}

func (adp *AggregatedDeeplinkProvider) getLoanDashboardFaqComponent(ctx context.Context, beRes *palBePb.GetDashboardResponse) *palTypesPb.LoanDashboardBottomSection_Component {
	var items []*palTypesPb.SingleColumnLineItems_SingleColumnLineItem
	if beRes.GetRecentLoanRequest().GetLoanProgram() == palBePb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY && apputils.IsFeatureEnabledFromCtxDynamic(ctx, adp.lendingConf.EligibilityNuggetBotFeature()) {
		items = append(items, &palTypesPb.SingleColumnLineItems_SingleColumnLineItem{
			Element: &widget.VisualElementTitleSubtitleElement{
				VisualElement:   provider.GetVisualElementPng(uiFrontend.GreenMessageBoxIcon, 36, 36),
				TitleText:       provider.GetText("Confused? Ask our expert", "#313234", commontypes.FontStyle_SUBTITLE_S),
				BackgroundColor: "#FFFFFF",
			},
			Deeplink: helper2.GetChatbotEntryPointDeeplink(map[string]string{chatbotScreenNameKey: deeplinkPb.Screen_LOANS_DASHBOARD_SCREEN.String()}),
		})
	}
	items = append(items, &palTypesPb.SingleColumnLineItems_SingleColumnLineItem{
		Element: &widget.VisualElementTitleSubtitleElement{
			VisualElement:   provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loan_dashboard_faq_icon.png", 36, 36),
			TitleText:       provider.GetText("FAQs", "#313234", commontypes.FontStyle_SUBTITLE_S),
			SubtitleText:    provider.GetText("Need help? Reach out to us", "#6A6D70", commontypes.FontStyle_BODY_XS),
			BackgroundColor: "#FFFFFF",
		},
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_HELP_MAIN,
		},
	})
	return &palTypesPb.LoanDashboardBottomSection_Component{
		Component: &palTypesPb.LoanDashboardBottomSection_Component_SingleColumnLineItems{SingleColumnLineItems: &palTypesPb.SingleColumnLineItems{Items: items}},
	}
}

func (adp *AggregatedDeeplinkProvider) getLoanAccountsDetailsForLoanDashboard(ctx context.Context, loanInfos []*palBePb.LoanInfo) (*getLoanAccountDetailsResponse, error) {
	if len(loanInfos) < 1 {
		return nil, nil
	}
	res := &getLoanAccountDetailsResponse{
		activeLoanCards: &palTypesPb.LoanDashboardBottomSection_SectionDivision{
			Title:      ui.NewITC().WithTexts(provider.GetText("Active loan", "#6A6D70", commontypes.FontStyle_HEADLINE_S)),
			Components: []*palTypesPb.LoanDashboardBottomSection_Component{},
		},
	}
	outstandingAmount := money2.ZeroINR().GetPb()
	totalPayableAmount := money2.ZeroINR().GetPb()

	for i, li := range loanInfos {
		if li.GetLoanAccount().GetStatus() != palBePb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
			continue
		}
		loanDetailsDlProvider, _ := adp.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
			Vendor:      helper.GetPalFeVendorFromBe(li.GetLoanAccount().GetVendor()),
			LoanProgram: helper.GetFeLoanProgramFromBe(li.GetLoanAccount().GetLoanProgram()),
		})
		accountDetailsResp, accDetailsErr := loanDetailsDlProvider.GetLoanAccountDetailsForDashboard(ctx, loanDetailsDlProvider.GetLoanHeader(), li)
		if accDetailsErr != nil {
			return nil, errors.Wrap(accDetailsErr, fmt.Sprintf("unable to get loan account details for given loan info list, index: %d", i))
		}
		if accountDetailsResp == nil {
			continue
		}
		if accountDetailsResp.GetActiveLoanCard().GetCardWithLineProgress().GetCtaBottomRow() != nil {
			res.overdueState = true
		}
		res.getActiveLoanCards().Components = append(res.getActiveLoanCards().GetComponents(), accountDetailsResp.GetActiveLoanCard())
		if accountDetailsResp.GetDisbursalBanner() != nil {
			res.disbursalBanner = accountDetailsResp.GetDisbursalBanner()
		}
		var err error
		// TODO: to check for moneyview flow, since for moneyview this info might be invalid
		outstandingAmount, err = money2.Sum(outstandingAmount, li.GetLoanAccount().GetLoanAmountInfo().GetOutstandingAmount())
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("unable to sum outstanding amount for dashboard, index: %d", i))
		}
		// TODO: to check for moneyview flow, since for moneyview this info might be invalid
		totalPayableAmount, err = money2.Sum(totalPayableAmount, li.GetLoanAccount().GetLoanAmountInfo().GetTotalPayableAmount())
		if err != nil {
			return nil, errors.Wrap(err, fmt.Sprintf("unable to sum total payable amount for dashboard, index: %d", i))
		}
	}
	if len(res.getActiveLoanCards().GetComponents()) == 0 && res.getDisbursalBanner() == nil {
		res.activeLoanCards = nil
	}
	res.outstandingAmount = types.GetFromBeMoney(outstandingAmount)
	res.totalLoanAmount = types.GetFromBeMoney(totalPayableAmount)
	return res, nil
}

type getLoanAccountDetailsResponse struct {
	activeLoanCards   *palTypesPb.LoanDashboardBottomSection_SectionDivision
	outstandingAmount *types.Money
	totalLoanAmount   *types.Money
	disbursalBanner   *palTypesPb.LoanDashboardBottomSection_SectionDivision
	overdueState      bool
}

func (p *getLoanAccountDetailsResponse) getActiveLoanCards() *palTypesPb.LoanDashboardBottomSection_SectionDivision {
	if p != nil {
		return p.activeLoanCards
	}
	return nil
}

func (p *getLoanAccountDetailsResponse) getDisbursalBanner() *palTypesPb.LoanDashboardBottomSection_SectionDivision {
	if p != nil {
		return p.disbursalBanner
	}
	return nil
}

func (p *getLoanAccountDetailsResponse) getTotalLoanAmount() *types.Money {
	if p != nil {
		return p.totalLoanAmount
	}
	return nil
}

func (p *getLoanAccountDetailsResponse) getOutstandingAmount() *types.Money {
	if p != nil {
		return p.outstandingAmount
	}
	return nil
}

// nolint: funlen
func (adp *AggregatedDeeplinkProvider) getLoanDashboardTopSectionComponent(
	accountRes *getLoanAccountDetailsResponse,
	lses []*palBePb.LoanStepExecution,
	loanRequest *palBePb.LoanRequest,
) (*palTypesPb.LoanDashboardTopSection_Component, error) {
	var component *palTypesPb.LoanDashboardTopSection_Component
	if accountRes != nil {
		component = &palTypesPb.LoanDashboardTopSection_Component{
			Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
				VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/orange-book-icon.png", 48, 48),
				VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan is disbursed", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
			}},
		}
	}

	if loanRequest != nil {
		component = &palTypesPb.LoanDashboardTopSection_Component{
			Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
				VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/orange-clock-icon.png", 48, 48),
				VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan application in progress", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
			}},
		}
	}

	// check for disbursal lse, if yes changing top section accordingly
	if len(lses) > 0 {
		latestLse := lses[0]
		if latestLse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_AMOUNT_DISBURSAL ||
			latestLse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LOAN_ACCOUNT_CREATION ||
			latestLse.GetStepName() == palBePb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ROI_MODIFICATION {
			maxETAText := "ETA 24 hours"
			if loanRequest.GetVendor() == palBePb.Vendor_LENDEN {
				tenureInMonths := loanRequest.GetDetails().GetLoanInfo().GetTenureInMonths()
				if tenureInMonths >= 5 {
					// Note: This is a quick fix based on the current state of information we have from LDC.
					// We are following up with Lenden to get tighter ETAs for 6-9 month tenure slab and beyond 9-month slab, if possible,
					// and also confirming if the expected_time_to_get_funding field from check-hard-eligibility API should be used instead.
					maxETAText = "ETA 3-4 days"
				}
			}
			component = &palTypesPb.LoanDashboardTopSection_Component{
				Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
					VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/orange-clock-icon.png", 48, 48),
					VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan disbursal in progress", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
					VisualisationTag: ui.NewITC().WithTexts(provider.GetText(maxETAText, "#FFFFFF", commontypes.FontStyle_OVERLINE_2XS_CAPS)).
						WithContainerBackgroundColor("#D48647").WithContainerPadding(3, 10, 3, 10).WithContainerCornerRadius(10),
				}},
			}
		} else {
			switch latestLse.GetStatus() {
			case palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:
				component = &palTypesPb.LoanDashboardTopSection_Component{
					Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
						VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/pal/dashboard/rejectionIcon.png", 48, 48),
						VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan application failed", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
					}},
				}
			case palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED:
				component = &palTypesPb.LoanDashboardTopSection_Component{
					Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
						VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/pal/dashboard/cancelledIcon.png", 48, 48),
						VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan application was cancelled", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
					}},
				}
			case palBePb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:
				component = &palTypesPb.LoanDashboardTopSection_Component{
					Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
						VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/pal/dashboard/rejectionIcon.png", 48, 48),
						VisualisationValue: ui.NewITC().WithTexts(provider.GetText("Loan application expired", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
					}},
				}
			}
		}
	}
	return component, nil
}

func (adp *AggregatedDeeplinkProvider) getScreenOptionsForClosedLoan() *palTypesPb.LoansDashboardScreenOptions {
	return &palTypesPb.LoansDashboardScreenOptions{
		PageTitle: provider.GetText("Your loans", "#F6F9FD", commontypes.FontStyle_HEADLINE_M),
		TopSection: &palTypesPb.LoanDashboardTopSection{
			Divisions: []*palTypesPb.LoanDashboardTopSection_SectionDivision{
				{
					Components: []*palTypesPb.LoanDashboardTopSection_Component{
						{
							Component: &palTypesPb.LoanDashboardTopSection_Component_CardSemiCircularProgress{CardSemiCircularProgress: &palTypesPb.CardSemiCircularProgress{
								VisualisationTitle: ui.NewITC().WithLeftVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/pl-circular-loan-bag.png", 48, 48),
								VisualisationValue: ui.NewITC().WithTexts(provider.GetText("No loan offer", "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
							}},
						},
					}},
			},
		},
		BottomSection: &palTypesPb.LoanDashboardBottomSection{
			Divisions: []*palTypesPb.LoanDashboardBottomSection_SectionDivision{
				{
					Components: []*palTypesPb.LoanDashboardBottomSection_Component{
						{
							Component: &palTypesPb.LoanDashboardBottomSection_Component_SingleColumnLineItems{SingleColumnLineItems: &palTypesPb.SingleColumnLineItems{Items: []*palTypesPb.SingleColumnLineItems_SingleColumnLineItem{
								{
									Element: &widget.VisualElementTitleSubtitleElement{
										VisualElement:   provider.GetVisualElementPng("https://epifi-icons.pointz.in/loans/loan_dashboard_faq_icon.png", 32, 32),
										TitleText:       provider.GetText("FAQs", "#313234", commontypes.FontStyle_SUBTITLE_S),
										SubtitleText:    provider.GetText("Need help? Reach out to us", "#6A6D70", commontypes.FontStyle_BODY_XS),
										BackgroundColor: "#FFFFFF",
									},
									Deeplink: &deeplinkPb.Deeplink{
										Screen: deeplinkPb.Screen_HELP_MAIN,
									},
								},
							}}},
						},
					}},
			},
		},
	}
}

func (adp *AggregatedDeeplinkProvider) getLoanOfferBanner(ctx context.Context, lo *palBePb.LoanOffer, showOfferBanner bool) (*palTypesPb.LoanDashboardBottomSection_SectionDivision, error) {
	if lo.IsActiveNow() && showOfferBanner {
		offerDl, offerDlErr := adp.getLoanOfferDeeplinkScreen(ctx, lo)
		if offerDlErr != nil {
			return nil, errors.Wrap(offerDlErr, "unable to get loan offer deeplink screen")
		}
		return &palTypesPb.LoanDashboardBottomSection_SectionDivision{
			Components: []*palTypesPb.LoanDashboardBottomSection_Component{
				{
					Component: &palTypesPb.LoanDashboardBottomSection_Component_BannerWithRightIconAndBottomLeftCta{
						BannerWithRightIconAndBottomLeftCta: &palTypesPb.BannerWithRightIconAndBottomLeftCta{
							RightIcon: provider.GetVisualElementPng("https://epifi-icons.pointz.in/preapprovedloan/pld_dashboard_new_offer_banner_icon.png", 125, 110),
							Title:     ui.NewITC().WithTexts(provider.GetText(fmt.Sprintf("Get a new loan up to\n%s", money2.ToDisplayStringInIndianFormat(lo.GetOfferConstraints().GetMaxLoanAmount(), 0, true)), "#FFFFFF", commontypes.FontStyle_SUBTITLE_M)),
							BottomLeftCta: ui.NewITC().WithTexts(provider.GetText("Get Loan", "#00B899", commontypes.FontStyle_BUTTON_S)).
								WithContainerBackgroundColor("#FFFFFF").WithDeeplink(offerDl).WithContainerPadding(8, 24, 8, 24).WithContainerCornerRadius(19),
							Deeplink: offerDl,
							BgColor:  "#6294A6",
						},
					},
				},
			},
		}, nil
	}
	return nil, nil
}

func (adp *AggregatedDeeplinkProvider) getLoanOfferDeeplinkScreen(ctx context.Context, lo *palBePb.LoanOffer) (*deeplinkPb.Deeplink, error) {
	isEnabled, err := adp.isOfferDetailsV4FlowEnabled(ctx, helper.GetFeLoanHeaderByBeLoanHeader(&palBePb.LoanHeader{
		LoanProgram: lo.GetLoanProgram(),
		Vendor:      lo.GetVendor(),
	}))
	if err != nil {
		return nil, errors.Wrap(err, fmt.Sprintf("failed to evaluate if multi offer screen is live for particular loan header: %s:%s", lo.GetVendor(), lo.GetLoanProgram()))
	}
	if isEnabled {
		return &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_LANDING_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanLandingScreenOptions{
				PreApprovedLoanLandingScreenOptions: &deeplinkPb.PreApprovedLoanLandingScreenOptions{
					LandingInfoFilters: []string{palBePb.LandingInfoFilter_LANDING_INFO_SKIP_FAILED_LR_FETCH.String()},
				},
			},
		}, nil
	}

	loanDetailsDlProvider, providerErr := adp.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinks.GetDeeplinkProviderRequest{
		Vendor:      helper.GetPalFeVendorFromBe(lo.GetVendor()),
		LoanProgram: helper.GetFeLoanProgramFromBe(lo.GetLoanProgram()),
	})
	if providerErr != nil {
		return nil, errors.Wrap(providerErr, fmt.Sprintf("unable to get deeplink generator for vendor: %s, program: %s", lo.GetVendor().String(), lo.GetLoanProgram().String()))
	}
	return loanDetailsDlProvider.GetLoanOfferOnClickDeeplink(ctx, lo)
}
