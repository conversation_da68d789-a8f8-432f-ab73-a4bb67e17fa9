package salaryprogram

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/pkg/accrual"

	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/salaryprogram"
	vkycBe "github.com/epifi/gamma/api/kyc/vkyc"
	beSalaryPb "github.com/epifi/gamma/api/salaryprogram"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
)

// GetSalaryProgramIntroPage fetches the intro page content for salary program
// nolint:funlen
func (s *Service) GetSalaryProgramIntroPage(ctx context.Context, req *fePb.GetSalaryProgramIntroPageRequest) (*fePb.GetSalaryProgramIntroPageResponse, error) {
	var (
		actorId     = req.GetReq().GetAuth().GetActorId()
		appPlatform = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersion  = req.GetReq().GetAuth().GetDevice().GetAppVersion()
	)

	// getOrCreateSalaryProgramRegistration create salary registration if not present already and return next stage to complete
	regNextStage, err := s.getOrCreateSalaryProgramRegistration(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while getting/creating salary program registration", zap.Error(err))
		return &fePb.GetSalaryProgramIntroPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error while getting/creating salary program registration")},
		}, nil
	}

	nextCta, err := s.getSalaryIntroPageNextStageRedirectionCta(regNextStage)
	if err != nil {
		logger.Error(ctx, "error getting salary program next stage to complete cta", zap.String("nextStage", regNextStage.String()), zap.Error(err))
		return &fePb.GetSalaryProgramIntroPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error getting salary program next stage to complete cta")},
		}, nil
	}

	// populate steps to unlock information
	stepsToUnlockInfo := &fePb.GetSalaryProgramIntroPageResponse_StepsToUnlockInfo{
		Title: &fePb.Text{Text: s.conf.SalaryProgram.IntroPage.StepsToUnlockEntryText, FontColor: s.conf.SalaryProgram.IntroPage.StepsToUnlockEntryTextFontColor},
		StepsInfo: &fePb.GetSalaryProgramIntroPageResponse_StepsToUnlockInfo_StepsToUnlock{
			Title:    &fePb.Text{Text: s.conf.SalaryProgram.StepsToUnlockInfo.Title, FontColor: s.conf.SalaryProgram.StepsToUnlockInfo.TitleColor},
			SubTitle: &fePb.Text{Text: s.conf.SalaryProgram.StepsToUnlockInfo.Subtitle, FontColor: s.conf.SalaryProgram.StepsToUnlockInfo.SubtitleColor},
			Steps:    []*fePb.GetSalaryProgramIntroPageResponse_StepsToUnlockInfo_StepsToUnlock_Step{},
		},
	}
	for _, stepInfo := range s.conf.SalaryProgram.StepsToUnlockInfo.Steps {
		stepsToUnlockInfo.StepsInfo.Steps = append(stepsToUnlockInfo.StepsInfo.Steps, &fePb.GetSalaryProgramIntroPageResponse_StepsToUnlockInfo_StepsToUnlock_Step{
			Text:     &fePb.Text{Text: stepInfo.Step, FontColor: stepInfo.FontColor},
			ImageUrl: stepInfo.ImageUrl,
		})
	}

	// populate eligibility details proto
	eligibilityDetails := &fePb.EligibilityInfo{
		Title:             &fePb.Text{Text: s.conf.SalaryProgram.EligibilityDetails.Title, FontColor: s.conf.SalaryProgram.EligibilityDetails.TitleColor},
		ImageUrl:          s.conf.SalaryProgram.EligibilityDetails.ImageUrl,
		EligibilityPoints: s.conf.SalaryProgram.EligibilityDetails.Requirements,
		// can be moved to config if needed
		BottomInfo: &fePb.EligibilityInfo_BottomInfo{
			Text:     &fePb.Text{Text: "We’re rolling out salary benefits to more users soon. Stay tuned!", FontColor: "#333333"}, // night
			ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/eligibility-details-clock.png",
		},
	}

	// note: sending `isSalaryProgramActive` arg as `false` for now as we don't want any special handling provided by the flag.
	// todo: pass the correct status of salary program in case we need special handling based on the status for the intro page
	benefitsCards, totalCashEquivalent, err := s.getBenefitsCardsAndCashEquivalentForActor(ctx, actorId, false, false, false, SalaryIntroPage, appPlatform, appVersion)
	if err != nil {
		logger.Error(ctx, "error fetching benefits cards for salary intro page", zap.Error(err))
		return &fePb.GetSalaryProgramIntroPageResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg("error fetching benefits cards for salary intro page")},
		}, nil
	}

	// intro page note info
	noteInfo := &fePb.GetSalaryProgramIntroPageResponse_NoteInfo{
		Text: &fePb.Text{
			Text:      s.dyconf.SalaryProgram().IntroPage().EligibilityDetailsEntryText(),
			FontColor: s.conf.SalaryProgram.IntroPage.EligibilityDetailsEntryTextFontColor,
		},
		EligibilityInfo: eligibilityDetails,
	}

	// overriding some display properties based on layout version.
	if req.GetLayoutVersion() == fePb.GetSalaryProgramIntroPageRequest_LAYOUT_VERSION_V1 || req.GetLayoutVersion() == fePb.GetSalaryProgramIntroPageRequest_LAYOUT_VERSION_V2 {
		// update background color of benefits cards
		for _, benefitCard := range benefitsCards {
			benefitCard.DisplayInfo.BgColor = "#282828" // Gray/Ink
		}

		// use v1 version of eligibility details entry-text.
		noteInfo.Text.Text = s.dyconf.SalaryProgram().IntroPage().EligibilityDetailsEntryTextV1()
		noteInfo.Text.FontColor = s.conf.SalaryProgram.IntroPage.EligibilityDetailsEntryTextFontColorV1
	}

	// nolint:gocritic
	// benefitsSectionCTA := &fePb.CTA{
	//	Text:     &fePb.Text{Text: "Calculate your total benefits", FontColor: "#00B899"},
	//	ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-green.png",
	//	BgColor:  "#333333", // Gray/Night
	//	Action: &fePb.CTA_DeeplinkAction{
	//		DeeplinkAction: &deeplinkPb.Deeplink{
	//			Screen: deeplinkPb.Screen_SALARY_PROGRAM_BENEFITS_CALCULATOR_SCREEN,
	//		},
	//	},
	// }
	//
	// // show information popup for app update if user's app version does not support benefits calculator
	// if appPlatform == commontypes.Platform_IOS && appVersion < s.dyconf.SalaryProgram().MinIosAppVersionToSupportBenefitsCalculatorPage() ||
	//	appPlatform == commontypes.Platform_ANDROID && appVersion < s.dyconf.SalaryProgram().MinAndroidAppVersionToSupportBenefitsCalculatorPage() {
	//	benefitsSectionCTA.Action = &fePb.CTA_DeeplinkAction{
	//		DeeplinkAction: &deeplinkPb.Deeplink{
	//			Screen: deeplinkPb.Screen_INFORMATION_POPUP,
	//			ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
	//				InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
	//					TextTitle:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Get benefits upto ₹10000 per year"}, FontColor: "#333333"},
	//					TextSubTitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Upgrade your app version to know how much you can earn"}, FontColor: "#646464"},
	//					BgColor:      "#FFFFFF",
	//					Ctas: []*deeplinkPb.Cta{
	//						{
	//							Type: deeplinkPb.Cta_CUSTOM,
	//							Text: "Upgrade now",
	//							Deeplink: &deeplinkPb.Deeplink{
	//								Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
	//							},
	//							DisplayTheme: deeplinkPb.Cta_PRIMARY,
	//							Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
	//						},
	//					},
	//				},
	//			},
	//		},
	//	}
	// }

	return &fePb.GetSalaryProgramIntroPageResponse{
		RespHeader:        &header.ResponseHeader{Status: rpc.StatusOk()},
		Title:             &fePb.Text{Text: s.conf.SalaryProgram.IntroPage.Title, FontColor: s.conf.SalaryProgram.IntroPage.TitleColor},
		TitleV2:           &commontypes.Text{DisplayValue: &commontypes.Text_Html{Html: fmt.Sprintf(s.conf.SalaryProgram.IntroPage.TitleV2, money.ToDisplayStringFromFloatValue(float32(totalCashEquivalent), 0))}},
		Desc:              &fePb.Text{Text: s.conf.SalaryProgram.IntroPage.Desc, FontColor: s.conf.SalaryProgram.IntroPage.DescColor},
		StepsToUnlockInfo: stepsToUnlockInfo,
		BenefitsCards:     benefitsCards,
		ImageUrl:          s.conf.SalaryProgram.IntroPage.ImageUrl,
		NoteInfo:          noteInfo,
		Cta:               nextCta,
		// BenefitsSectionCta: benefitsSectionCTA,
		StoryCta: &fePb.CTA{
			Text: &fePb.Text{
				Text:      accrual.ReplaceCoinWithPointIfApplicable(s.conf.SalaryProgram.IntroPage.StoryCta.Text),
				FontColor: s.conf.SalaryProgram.IntroPage.StoryCta.TextColor,
			},
			IsVisible: s.conf.SalaryProgram.IntroPage.StoryCta.IsVisible,
			ImageUrl:  s.conf.SalaryProgram.IntroPage.StoryCta.ImageUrl,
			Action: &fePb.CTA_DeeplinkAction{
				DeeplinkAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_STORY_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_StoryScreenOptions{
						StoryScreenOptions: &deeplinkPb.StoryScreenOptions{
							StoryTitle: "Fi-Coins Intro",
							StoryUrl:   "https://stories.fi.money/stories/fi-coins-intro",
							StoryId:    "e24825d2-740b-4fce-9aba-6a1ecc47d0ef",
						},
					},
				},
			},
		},
		TncUrl: s.conf.SalaryProgram.IntroPage.TncUrl,
	}, nil

}

// GetSalaryProgramIntroPageV1 rpc to get v3 salary intro page, v0-v2 are driven by GetSalaryProgramIntroPage rpc
// v3 figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=11181%3A81092&t=OWS3VfqmSB5gAHL1-1
func (s *Service) GetSalaryProgramIntroPageV1(ctx context.Context, req *fePb.GetSalaryProgramIntroPageV1Request) (*fePb.GetSalaryProgramIntroPageV1Response, error) {
	var (
		actorId        = req.GetReq().GetAuth().GetActorId()
		appPlatform    = req.GetReq().GetAuth().GetDevice().GetPlatform()
		appVersion     = req.GetReq().GetAuth().GetDevice().GetAppVersion()
		nextCta        *fePb.CTA
		salaryBenefits *fePb.SalaryProgramBenefits
	)

	errGroup, gctx := errgroup.WithContext(ctx)

	errGroup.Go(func() error {
		// getOrCreateSalaryProgramRegistration create salary registration if not present already and return next stage to complete
		regNextStage, err := s.getOrCreateSalaryProgramRegistration(gctx, actorId)
		if err != nil {
			logger.Error(gctx, "error while getting/creating salary program registration", zap.Error(err))
			return fmt.Errorf("error while getting/creating salary program registration")
		}

		// get cta to redirect to salary program next stage
		nextCta, err = s.getSalaryIntroPageNextStageRedirectionCta(regNextStage)
		if err != nil {
			logger.Error(gctx, "error getting salary program next stage to complete cta", zap.String("nextStage", regNextStage.String()), zap.Error(err))
			return fmt.Errorf("error getting salary program next stage to complete cta")
		}
		return nil
	})

	errGroup.Go(func() error {
		res, err := s.salaryBenefitsSvc.GetBenefitsForActor(ctx, actorId, appPlatform, appVersion)
		if err != nil {
			logger.Error(ctx, "error while getting salary benefits for actor", zap.Error(err))
			return fmt.Errorf("error while getting salary benefits for actor")
		}
		salaryBenefits = res
		return nil
	})

	if errGrpErr := errGroup.Wait(); errGrpErr != nil {
		return &fePb.GetSalaryProgramIntroPageV1Response{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternalWithDebugMsg(errGrpErr.Error())},
		}, nil
	}

	isHeroBenefitSupportedOnClient := apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.dyconf.SalaryProgram().IntroPageV3().HeroBenefitFeatureConfig())

	// create FE benefit cards
	var benefitCards, heroBenefitCards []*fePb.GetSalaryProgramIntroPageV1Response_BenefitCard
	for _, benefit := range salaryBenefits.GetSalaryProgramRewardOffers() {
		var desc *commontypes.Text
		if benefit.GetBottomSheetSubheading() != "" {
			desc = commontypes.GetTextFromStringFontColourFontStyle(benefit.GetBottomSheetSubheading(), "#646464", commontypes.FontStyle_BODY_S)
		}
		// todo add version
		benefitCard := &fePb.GetSalaryProgramIntroPageV1Response_BenefitCard{
			Title:    commontypes.GetTextFromStringFontColourFontStyle(benefit.GetTitle(), "#282828", commontypes.FontStyle_SUBTITLE_S),
			Subtitle: commontypes.GetTextFromStringFontColourFontStyle(benefit.GetDescription(), "#8D8D8D", commontypes.FontStyle_BODY_XS),
			Desc:     desc,
			ImageUrl: benefit.GetImageUrl(),
			BgColor:  "#F5F5F5", // Gray/Steel
			BgColorV1: &widgetPb.BackgroundColour{
				Colour: &widgetPb.BackgroundColour_BlockColour{
					BlockColour: "#F5F5F5", // Gray/Steel
				},
			},
		}
		if benefit.GetIsHeroOffer() && isHeroBenefitSupportedOnClient {
			benefitCard.BgColor = "#FFFFFF"
			benefitCard.BgColorV1 = &widgetPb.BackgroundColour{
				Colour: &widgetPb.BackgroundColour_RadialGradient{
					RadialGradient: &widgetPb.RadialGradient{
						Center: &widgetPb.CenterCoordinates{
							CenterX: 1,
						},
						OuterRadius: 1200,
						Colours:     []string{"#7088A0", "#DBE7F3"},
					},
				},
			}
			benefitCard.Shadow = &widgetPb.Shadow{
				Blur: 16,
				Colour: &widgetPb.BackgroundColour{
					Colour: &widgetPb.BackgroundColour_BlockColour{
						BlockColour: "#C3D2E1",
					},
				},
			}
			heroBenefitCards = append(heroBenefitCards, benefitCard)
		} else {
			benefitCards = append(benefitCards, benefitCard)
		}
	}

	benefitsCashEquivalent := int(salaryBenefits.GetTotalCashEquivalent())
	topSectionFooterInfoSubtitle := fmt.Sprintf("₹%s/yr", money.ToDisplayStringFromIntValue(benefitsCashEquivalent))

	var benefitsCalculatorCta *fePb.CTA
	if s.dyconf.SalaryProgram().IntroPageV3().ShowSalaryCalculatorCta() {
		benefitsCalculatorCta = s.getSalaryCalculatorCta(appPlatform, appVersion)
	}

	bottomBanners := []*fePb.GetSalaryProgramIntroPageV1Response_BottomBanner{
		{
			Title:   commontypes.GetTextFromHtmlStringFontColourFontStyle("Your monthly salary must be at least ₹20,000. <a style='color: #00B899; font-family: Inter; text-decoration: none;' href=\"https://fi.money/blog/salary-program-tnc\">View TnC</a>", "#6294A6", commontypes.FontStyle_SUBTITLE_XS),
			Heading: commontypes.GetTextFromStringFontColourFontStyle("Eligibility", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
			BgColor: "#E4F1F5", // Ocean
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/salaryprogram/info-ocean.png",
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  40,
							Height: 40,
						},
					},
				},
			},
		},
		{
			Title:   commontypes.GetTextFromHtmlStringFontColourFontStyle("Salary must get credited in your Federal Bank Savings A/C", "#6294A6", commontypes.FontStyle_SUBTITLE_XS),
			Heading: commontypes.GetTextFromStringFontColourFontStyle("To upgrade", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
			BgColor: "#E4F1F5", // Ocean
		},
	}

	return &fePb.GetSalaryProgramIntroPageV1Response{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		TopSection: &fePb.GetSalaryProgramIntroPageV1Response_TopSection{
			Title: commontypes.GetTextFromStringFontColourFontStyle("Salary Plan", "#333333", commontypes.FontStyle_HEADLINE_M),
			FooterInfo: &fePb.GetSalaryProgramIntroPageV1Response_TopSection_FooterInfo{
				Title:    commontypes.GetTextFromStringFontColourFontStyle("BENEFITS UP TO", "#AC7C44", commontypes.FontStyle_OVERLINE_XS_CAPS),
				Subtitle: commontypes.GetTextFromStringFontColourFontStyle(topSectionFooterInfoSubtitle, "#AC7C44", commontypes.FontStyle_NUMBER_L),
				BgColor:  "#F4E7BF", // Pastel Lemon
			},
			ImageUrl:    "https://epifi-icons.pointz.in/salaryprogram/golden-star.png",
			ShadowColor: "#591F1F1F", // 35% transparency
			BgColor:     "#C29E54",
			Cta:         benefitsCalculatorCta,
		},
		BenefitsSection: &fePb.GetSalaryProgramIntroPageV1Response_BenefitsSection{
			Title:            commontypes.GetTextFromStringFontColourFontStyle("What you get", "#282828", commontypes.FontStyle_SUBTITLE_S),
			BenefitCards:     benefitCards,
			HeroBenefitCards: heroBenefitCards,
		},
		TncSection: &fePb.GetSalaryProgramIntroPageV1Response_TncSection{
			Text: commontypes.GetTextFromHtmlStringFontColourFontStyle("Understand our <a style='color: #00B899; text-decoration: none;' href=\"https://fi.money/blog/salary-program-tnc\">Terms and Conditions</a>", "#8D8D8D", commontypes.FontStyle_BODY_XS),
		},
		BottomBanner: &fePb.GetSalaryProgramIntroPageV1Response_BottomBanner{
			Title:   commontypes.GetTextFromStringFontColourFontStyle("Upgrade now and get a zero-balance account", "#282828", commontypes.FontStyle_SUBTITLE_S),
			BgColor: "#ECEEF0", // Gray/Fog
			AdditionalInfo: &fePb.GetSalaryProgramIntroPageV1Response_AdditionalInfo{
				Title:    commontypes.GetTextFromStringFontColourFontStyle("MINIMUM BALANCE", "#555555", commontypes.FontStyle_OVERLINE_2XS_CAPS),
				Subtitle: commontypes.GetTextFromStringFontColourFontStyle("₹0", "#282828", commontypes.FontStyle_SUBTITLE_M),
			},
		},
		BottomBanners: bottomBanners,
		Cta:           nextCta,
	}, nil
}

// getOrCreateSalaryProgramRegistration return salary reg next stage, if salary registration is not present already then it creates registration and return next reg stage
func (s *Service) getOrCreateSalaryProgramRegistration(ctx context.Context, actorId string) (beSalaryPb.SalaryProgramRegistrationStage, error) {
	registrationStatusRes, err := s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
		ActorId:  actorId,
		FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
	})
	if rpcErr := epifigrpc.RPCError(registrationStatusRes, err); rpcErr != nil {
		return beSalaryPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_UNSPECIFIED, fmt.Errorf("error while fetching salary registration status for actor, err: %w", rpcErr)
	}

	// if registration is not initiated for the user, then create new registration
	if registrationStatusRes.GetRegistrationStatus() == beSalaryPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_NOT_INITIATED {
		// create registration
		newRegistrationRes, regErr := s.salaryProgramClient.CreateRegistration(ctx, &beSalaryPb.CreateRegistrationRequest{
			ActorId:              actorId,
			RegistrationFlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
			AccountType:          beSalaryPb.SalaryProgramRegistrationAccountType_SALARY_PROGRAM_REGISTRATION_ACCOUNT_TYPE_FI_FED_SAVINGS_ACC,
		})
		if rpcErr := epifigrpc.RPCError(newRegistrationRes, regErr); rpcErr != nil {
			return beSalaryPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_UNSPECIFIED, fmt.Errorf("error creating new registration of salary program for actor, err: %w", rpcErr)
		}

		// fetching the newly generated registration to get the next-stage to be completed
		registrationStatusRes, err = s.salaryProgramClient.GetCurrentRegStatusAndNextRegStage(ctx, &beSalaryPb.CurrentRegStatusAndNextRegStageRequest{
			ActorId:  actorId,
			FlowType: beSalaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE,
		})
		if rpcErr := epifigrpc.RPCError(registrationStatusRes, err); rpcErr != nil {
			return beSalaryPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_UNSPECIFIED, fmt.Errorf("error while fetching salary registration status again for actor, err: %w", rpcErr)
		}
	}
	return registrationStatusRes.GetNextStage(), nil
}

func (s *Service) getSalaryIntroPageNextStageRedirectionCta(nextStage beSalaryPb.SalaryProgramRegistrationStage) (*fePb.CTA, error) {
	switch nextStage {
	// take the user to employer confirmation flow if that's the next stage to complete
	case beSalaryPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_EMPLOYMENT_CONFIRMATION:
		return &fePb.CTA{
			Text: &fePb.Text{
				Text:      s.dyconf.SalaryProgram().IntroPage().EmployerConfirmationReqdCta().Text(),
				FontColor: s.dyconf.SalaryProgram().IntroPage().EmployerConfirmationReqdCta().TextColor(),
				BgColor:   s.dyconf.SalaryProgram().IntroPage().EmployerConfirmationReqdCta().BgColor(),
			},
			Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_EMPLOYER_CONFIRMATION_SCREEN}},
			IsVisible: s.dyconf.SalaryProgram().IntroPage().EmployerConfirmationReqdCta().IsVisible(),
		}, nil

	// take the user to FULL KYC flow if that's the next stage to complete
	case beSalaryPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_FULL_KYC_COMPLETION:
		vkycDeeplink, _ := vkycPkg.BuildVKYCStatusDeeplink(&vkycPkg.StatusScreenOptions{
			EntryPoint:           vkycBe.EntryPoint_ENTRY_POINT_SALARY_PROGRAM,
			EntryPointDeprecated: deeplinkPb.EntryPoint_ENTRY_POINT_SALARY_PROGRAM,
		})
		return &fePb.CTA{
			Text: &fePb.Text{
				Text:      s.dyconf.SalaryProgram().IntroPage().FullKycReqdCta().Text(),
				FontColor: s.dyconf.SalaryProgram().IntroPage().FullKycReqdCta().TextColor(),
				BgColor:   s.dyconf.SalaryProgram().IntroPage().FullKycReqdCta().BgColor(),
			},
			Action: &fePb.CTA_DeeplinkAction{
				DeeplinkAction: vkycDeeplink,
			},
			IsVisible: s.dyconf.SalaryProgram().IntroPage().FullKycReqdCta().IsVisible(),
		}, nil

	// take the user to salary landing page if there is no next stage to be completed by the user
	case beSalaryPb.SalaryProgramRegistrationStage_REGISTRATION_STAGE_UNSPECIFIED:
		return &fePb.CTA{
			Text: &fePb.Text{
				Text:      s.dyconf.SalaryProgram().IntroPage().SalaryLandingPageCta().Text(),
				FontColor: s.dyconf.SalaryProgram().IntroPage().SalaryLandingPageCta().TextColor(),
				BgColor:   s.dyconf.SalaryProgram().IntroPage().SalaryLandingPageCta().BgColor(),
			},
			Action:    &fePb.CTA_DeeplinkAction{DeeplinkAction: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN}},
			IsVisible: s.dyconf.SalaryProgram().IntroPage().SalaryLandingPageCta().IsVisible(),
		}, nil

	// shouldn't reach this stage ideally
	default:
		return nil, errors.New("no valid CTA available to proceed based on the next-stage to be completed")
	}
}

func (s *Service) getSalaryCalculatorCta(appPlatform commontypes.Platform, appVersion uint32) *fePb.CTA {
	deeplinkAction := &fePb.CTA_DeeplinkAction{
		DeeplinkAction: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_SALARY_PROGRAM_BENEFITS_CALCULATOR_SCREEN,
		},
	}

	// show information popup for app update if user's app version does not support benefits calculator
	if appPlatform == commontypes.Platform_IOS && appVersion < s.dyconf.SalaryProgram().MinIosAppVersionToSupportBenefitsCalculatorPage() ||
		appPlatform == commontypes.Platform_ANDROID && appVersion < s.dyconf.SalaryProgram().MinAndroidAppVersionToSupportBenefitsCalculatorPage() {
		deeplinkAction = &fePb.CTA_DeeplinkAction{
			DeeplinkAction: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_INFORMATION_POPUP,
				ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
					InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
						TextTitle:    &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Get benefits upto ₹10000 per year"}, FontColor: "#333333"},
						TextSubTitle: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Upgrade your app version to know how much you can earn"}, FontColor: "#646464"},
						BgColor:      "#FFFFFF",
						Ctas: []*deeplinkPb.Cta{
							{
								Type: deeplinkPb.Cta_CUSTOM,
								Text: "Upgrade now",
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_UPDATE_APP_SCREEN,
								},
								DisplayTheme: deeplinkPb.Cta_PRIMARY,
								Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
							},
						},
					},
				},
			},
		}
	}

	return &fePb.CTA{
		Text:     &fePb.Text{Text: "Calculate your total benefits", FontColor: "#FFFFFF"},
		ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-white.png",
		BgColor:  "#661F1F1F", // 40% transparency
		Action:   deeplinkAction,
	}
}
