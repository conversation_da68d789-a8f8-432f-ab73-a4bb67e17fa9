package billpay

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	billpayPb "github.com/epifi/gamma/api/billpay"
	billpayEnums "github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/api/frontend/billpay"
	frontendBillpayEnums "github.com/epifi/gamma/api/frontend/billpay/enums"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	layoutPb "github.com/epifi/gamma/api/typesv2/billpay"
	billPayScreenOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/billpay"
	models "github.com/epifi/gamma/frontend/billpay/models"
	uiBuilder "github.com/epifi/gamma/frontend/billpay/ui"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

type Service struct {
	billpay.UnimplementedBillPayServer
	billpayClient billpayPb.BillPayClient
}

func NewService(billpayClient billpayPb.BillPayClient) *Service {
	return &Service{
		billpayClient: billpayClient,
	}
}

func (s *Service) CreateRechargeOrder(ctx context.Context, req *billpay.CreateRechargeOrderRequest) (*billpay.CreateRechargeOrderResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
		res     = &billpay.CreateRechargeOrderResponse{
			RespHeader: &header.ResponseHeader{},
		}
	)

	// Map account type from frontend to backend enum
	backendAccountType := billpayEnums.RechargeAccountType(billpayEnums.RechargeAccountType_value[req.GetAccountType()])
	if backendAccountType == billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_UNSPECIFIED {
		res.RespHeader.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	// Map frontend request to backend request
	backendReq := &billpayPb.CreateRechargeOrderRequest{
		ActorId:           actorId,
		AccountType:       backendAccountType,
		AccountIdentifier: req.GetAccountIdentifier(),
		Operator:          billpayEnums.Operator(billpayEnums.Operator_value[req.GetOperatorId()]),
		PlanId:            req.GetPlanId(),
	}

	// Call backend service
	croRes, err := s.billpayClient.CreateRechargeOrder(ctx, backendReq)
	if rpcErr := epifigrpc.RPCError(croRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling billpay CreateRechargeOrder RPC", zap.Error(rpcErr))
		return &billpay.CreateRechargeOrderResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	dl := deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_RECHARGE_POLLING_SCREEN, &billPayScreenOptions.RechargePollingScreenOptions{
		ClientRequestId: croRes.GetClientRequestId(),
		RetryIntervalMs: 500,
	})

	// Map backend response to frontend response
	return &billpay.CreateRechargeOrderResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: dl,
	}, nil
}

func (s *Service) GetRechargeOrderStatus(ctx context.Context, req *billpay.GetRechargeOrderStatusRequest) (*billpay.GetRechargeOrderStatusResponse, error) {
	clientRequestId := req.GetClientRequestId()

	// Map frontend request to backend request
	backendReq := &billpayPb.GetRechargeOrderStatusRequest{
		ClientRequestId: clientRequestId,
	}

	// Call backend service
	grosRes, err := s.billpayClient.GetRechargeOrderStatus(ctx, backendReq)
	if rpcErr := epifigrpc.RPCError(grosRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling billpay GetRechargeOrderStatus RPC", zap.Error(rpcErr))
		return &billpay.GetRechargeOrderStatusResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	// Map backend response to frontend response
	return &billpay.GetRechargeOrderStatusResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		OrderStatus: frontendBillpayEnums.RechargeOrderStatus(grosRes.GetOrderStatus()),
		NextAction:  grosRes.GetNextAction(),
	}, nil
}

func (s *Service) GetRechargeIntroScreen(ctx context.Context, req *billpay.GetRechargeIntroScreenRequest) (*billpay.GetRechargeIntroScreenResponse, error) {
	// For now, returning a basic response structure
	return &billpay.GetRechargeIntroScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Layout: &layoutPb.RechargeIntroScreen{
			BgColor: &widget.BackgroundColour{},
			TopNavBar: &layoutPb.TopNavBar{
				BgColor:   widget.GetBlockBackgroundColour(""),
				Title:     commontypes.GetTextFromStringFontColourFontStyleFontAlignment("", "", commontypes.FontStyle_BODY_1, commontypes.Text_ALIGNMENT_CENTER),
				RightIcon: nil,
			},
			TopSection: nil,
			ContactsSection: &layoutPb.RechargeIntroScreen_ContactsSection{
				BgColor:                 nil,
				SearchBar:               nil,
				ContactsPermissionNudge: nil,
			},
			Footer_Section: nil,
		},
	}, nil
}

func (s *Service) GetRechargePlansScreen(ctx context.Context, req *billpay.GetRechargePlansScreenRequest) (*billpay.GetRechargePlansScreenResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
		res     = &billpay.GetRechargePlansScreenResponse{
			RespHeader: &header.ResponseHeader{},
		}
	)

	// Map account type from frontend to backend enum
	backendAccountType := billpayEnums.RechargeAccountType(billpayEnums.RechargeAccountType_value[req.GetAccountType()])
	if backendAccountType == billpayEnums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_UNSPECIFIED {
		res.RespHeader.Status = rpc.StatusInvalidArgument()
		return res, nil
	}

	// Map frontend request to backend request
	backendReq := &billpayPb.FetchRechargePlansRequest{
		ActorId:           actorId,
		AccountType:       backendAccountType,
		AccountIdentifier: req.GetAccountIdentifier(),
	}

	// Call backend service
	frpRes, err := s.billpayClient.FetchRechargePlans(ctx, backendReq)
	if rpcErr := epifigrpc.RPCError(frpRes, err); rpcErr != nil {
		logger.Error(ctx, "error while calling billpay FetchRechargePlans RPC", zap.Error(rpcErr))
		return &billpay.GetRechargePlansScreenResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}
	// TODO: Build RechargePlansScreen layout from backend response
	_ = uiBuilder.BuildRechargePlanCard(models.RechargePlanDetails{})
	_ = uiBuilder.BuildAccountInfoCard(models.RechargeAccountInfo{})
	_ = uiBuilder.BuildBillDetailsComponent(models.BillDetails{})

	// Map backend response to frontend response
	return &billpay.GetRechargePlansScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Layout: nil, // TODO: Build RechargePlansScreen layout from frpRes
	}, nil
}

func (s *Service) GetBillDetailsConfirmationScreen(ctx context.Context, req *billpay.GetBillDetailsConfirmationScreenRequest) (*billpay.GetBillDetailsConfirmationScreenResponse, error) {
	// This RPC builds the bill details confirmation screen
	// No backend call needed - this is a frontend-only screen builder

	// TODO: Build the actual confirmation screen layout based on request params
	// For now, returning a basic response structure
	return &billpay.GetBillDetailsConfirmationScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Layout:          nil, // TODO: Build BillDetailsConfirmationScreen layout
		ClientRequestId: "",  // TODO: Generate or extract client request ID
		// PaymentParams will be set based on the request type (recharge_params, etc.)
	}, nil
}
