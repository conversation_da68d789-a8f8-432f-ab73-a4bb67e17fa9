package lens

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"

	"github.com/epifi/gamma/frontend/analyser/spends/merchant/store"
)

func TestTopPeopleByAmountProcessor_GenerateLensData(t1 *testing.T) {
	tests := []struct {
		name             string
		entityAggregates []*store.EntityAggregate
		before           func(m *fields)
		wantErr          bool
	}{
		{
			name: "Successfully generate lens data",
			entityAggregates: []*store.EntityAggregate{
				merchantAgg, userAgg,
			},
			before: func(m *fields) {
				m.mockEntityProcessor.EXPECT().GenerateLensData(gomock.Any(), []*store.EntityAggregate{
					userAgg,
				}).Return(nil)
			},
		},
		{
			name: "Error while generating lens data should return error",
			entityAggregates: []*store.EntityAggregate{
				merchantAgg, userAgg,
			},
			before: func(m *fields) {
				m.mockEntityProcessor.EXPECT().GenerateLensData(gomock.Any(), []*store.EntityAggregate{
					userAgg,
				}).Return(fmt.Errorf("some err"))
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		tt := tt
		t1.Run(tt.name, func(t1 *testing.T) {
			t1.Parallel()
			ctl := gomock.NewController(t1)
			defer ctl.Finish()
			m := initMocks(ctl)
			tt.before(m)
			t := NewTopPeopleByAmountProcessor(store.NewTopMerchantSpendsStore(nil, nil, tt.entityAggregates, nil), m.mockEntityProcessor)
			if err := t.GenerateLensData(context.Background(), "", nil); (err != nil) != tt.wantErr {
				t1.Errorf("GenerateLensData() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
