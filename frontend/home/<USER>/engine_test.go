package layoutconfiguration

import (
	"context"
	"log"
	"testing"

	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	productPb "github.com/epifi/gamma/api/product"

	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"

	"github.com/epifi/gamma/api/acquisition/crossattach/mocks"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	usersPb "github.com/epifi/gamma/api/user"
	usergroupPb "github.com/epifi/gamma/api/user/group"

	mockUserGroup "github.com/epifi/gamma/api/user/group/mocks"
	mockUsers "github.com/epifi/gamma/api/user/mocks"

	"github.com/golang/mock/gomock"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	segmentPb "github.com/epifi/gamma/api/segment"
	mockSegmentation "github.com/epifi/gamma/api/segment/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	mockOnboarding "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/home/<USER>/attributes"
	"github.com/epifi/gamma/frontend/home/<USER>/sort"
)

func TestEngine_EvaluateLayout(t *testing.T) {
	logger.Init("test")
	dynConf, err := genconf.Load()
	if err != nil {
		log.Fatal("failed to load dynamic config", err)
	}

	type args struct {
		ctx            context.Context
		actorId        string
		layoutV2Params *genconf.HomeLayoutV2Params
	}

	tests := []struct {
		name           string
		args           args
		setupMockCalls func(segmentMock *mockSegmentation.MockSegmentationServiceClient, mockOnboardingClient *mockOnboarding.MockOnboardingClient, mockUsersClient *mockUsers.MockUsersClient, mockUserGroupClient *mockUserGroup.MockGroupClient, mockCrossAttachClient *mocks.MockCrossAttachClient)
		want           *config.SlotIdToScreenElementIdMap
		wantErr        bool
	}{
		{
			name: "should return default SlotIdToScreenElementIdMap, when dynamic layout has only single values, no attributes to evaluate",
			args: args{
				ctx:            context.Background(),
				actorId:        "actor-1",
				layoutV2Params: dynConf.HomeRevampParamsForUnitTests().HomeLayoutV2ParamsWithoutAttributes(),
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient, mockOnboardingClient *mockOnboarding.MockOnboardingClient, mockUsersClient *mockUsers.MockUsersClient, mockUserGroupClient *mockUserGroup.MockGroupClient, mockCrossAttachClient *mocks.MockCrossAttachClient) {
			},
			want: &config.SlotIdToScreenElementIdMap{
				LayoutId: "50c54a07-3dc4-5c05-bee5-93a046434e30",
				TopNavBarSlotSection: &config.TopNavBarSlotSection{
					LeftSlots: map[string]string{
						"slot_1": "profile-1",
					},
					RightSlots: map[string]string{
						"slot_1": "refer-1",
						"slot_2": "card-1",
						"slot_3": "notification-1",
					},
				},
				DashboardSlotSection: map[string]string{
					"slot_1": "intro-1",
					"slot_2": "primarysavings-1",
					"slot_3": "networth-1",
					"slot_4": "invest-1",
					"slot_5": "creditcards-1",
					"slot_6": "loans-1",
				},
				VerticalSlotSection: map[string]string{
					"slot_1":  "searchbar-1",
					"slot_2":  "promotionalbanner-1",
					"slot_3":  "suggestedforyou-1",
					"slot_4":  "shortcuts-2",
					"slot_5":  "primary-feature-1",
					"slot_6":  "recentupcomingactivities-1",
					"slot_7":  "analyser-2",
					"slot_8":  "secondary-feature-1",
					"slot_9":  "rewards-2",
					"slot_10": "rewards-3",
					"slot_11": "tabbed-card-1",
					"slot_12": "refer-2",
					"slot_13": "",
					"slot_14": "",
					"slot_15": "",
					"slot_16": "help-1",
					"slot_17": "trust-marker",
				},
				BottomNavBarSlotSection: map[string]string{
					"slot_1": "home-1",
					"slot_2": "pay-1",
					"slot_3": "invest-3",
					"slot_4": "analyser-1",
					"slot_5": "discover-1",
				},
				StickyIconSlotSection: map[string]string{
					"slot_1": "qr_code-1",
					"slot_2": "qr_code-2",
					"slot_3": "qr_code-3",
				},
			},
			wantErr: false,
		},
		{
			name: "should return connected accounts element on dashboard, and loans in bottom nav when user falls in connected-accounts segment, and eligible for loans",
			args: args{
				ctx:            context.Background(),
				actorId:        "actor-id",
				layoutV2Params: dynConf.HomeRevampParamsForUnitTests().HomeLayoutV2ParamsWithAttributes(),
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient, mockOnboardingClient *mockOnboarding.MockOnboardingClient, mockUsersClient *mockUsers.MockUsersClient, mockUserGroupClient *mockUserGroup.MockGroupClient, mockCrossAttachClient *mocks.MockCrossAttachClient) {
				segmentMock.EXPECT().IsMemberOfExpressions(gomock.Any(), &segmentPb.IsMemberOfExpressionsRequest{
					ActorId:              "actor-id",
					SegmentIdExpressions: []string{"IsMember('segment-1')"},
				}).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status: rpc.StatusOk(),
					SegmentExpressionMembershipMap: map[string]*segmentPb.SegmentExpressionMembership{
						"IsMember('segment-1')": {
							SegmentExpressionStatus: segmentPb.SegmentExpressionStatus_OK,
							IsActorMember:           true,
						},
					},
				}, nil).Times(1)
				mockCrossAttachClient.EXPECT().GetCrossSellInfo(gomock.Any(), gomock.Any()).Return(&crossAttachPb.GetCrossSellInfoResponse{
					Status:       rpc.StatusOk(),
					CanCrossSell: commontypes.BooleanEnum_FALSE,
				}, nil).Times(1)
				mockOnboardingClient.EXPECT().GetFeatureLifecycle(gomock.Any(), &onbPb.GetFeatureLifecycleRequest{
					ActorId:        "actor-id",
					Features:       []onbPb.Feature{onbPb.Feature_FEATURE_PL, onbPb.Feature_FEATURE_SA, onbPb.Feature_FEATURE_UPI_TPAP},
					WantCachedData: true,
				}).Return(&onbPb.GetFeatureLifecycleResponse{
					Status: rpc.StatusOk(),
					FeatureLifecycleMap: map[string]*onbPb.FeatureLifecycle{
						onbPb.Feature_FEATURE_PL.String(): {
							IntentSelectionInfo: &onbPb.FeatureIntentSelectionInfo{
								SelectedAsHardIntent: true,
							},
						},
						onbPb.Feature_FEATURE_SA.String(): {
							EligibilityStatus: &onbPb.FeatureEligibility{
								Status: onbPb.FeatureEligibility_STATUS_PASSED,
							},
						},
						onbPb.Feature_FEATURE_UPI_TPAP.String(): {
							ActivationStatus: onbPb.FeatureStatus_FEATURE_STATUS_UNSPECIFIED,
						},
					},
				}, nil).Times(1)
				mockUsersClient.EXPECT().GetUser(gomock.Any(), &usersPb.GetUserRequest{
					Identifier: &usersPb.GetUserRequest_ActorId{
						ActorId: "actor-id",
					},
				}).Return(&usersPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &usersPb.User{
						Profile: &usersPb.Profile{
							Email: "<EMAIL>",
						},
					},
				}, nil).Times(1)
				mockUserGroupClient.EXPECT().GetGroupsMappedToIdentifier(gomock.Any(), &usergroupPb.GetGroupsMappedToIdentifierRequest{
					IdentifierValue: &usergroupPb.IdentifierValue{
						Identifier: &usergroupPb.IdentifierValue_Email{
							Email: "<EMAIL>",
						},
					},
				}).Return(&usergroupPb.GetGroupsMappedToIdentifierResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{
						commontypes.UserGroup_INTERNAL,
						commontypes.UserGroup_FI_STORE_INTERNAL,
					},
				}, nil).Times(1)
			},
			want: &config.SlotIdToScreenElementIdMap{
				LayoutId: "d6b916f7-846b-57bd-bb68-a69e99e85c3f",
				TopNavBarSlotSection: &config.TopNavBarSlotSection{
					LeftSlots: map[string]string{
						"slot_1": "profile-1",
					},
					RightSlots: map[string]string{
						"slot_1": "refer-1",
						"slot_2": "card-1",
						"slot_3": "notification-1",
					},
				},
				DashboardSlotSection: map[string]string{
					"slot_1": "intro-1",
					"slot_2": "primarysavings-1",
					"slot_3": "connectedaccounts-1",
					"slot_4": "invest-1",
					"slot_5": "creditcards-1",
					"slot_6": "loans-1",
				},
				VerticalSlotSection: map[string]string{
					"slot_1":  "searchbar-1",
					"slot_2":  "promotionalbanner-1",
					"slot_3":  "suggestedforyou-1",
					"slot_4":  "shortcuts-2",
					"slot_5":  "primary-feature-1",
					"slot_6":  "",
					"slot_7":  "analyser-2",
					"slot_8":  "secondary-feature-1",
					"slot_9":  "rewards-2",
					"slot_10": "rewards-3",
					"slot_11": "tabbed-card-1",
					"slot_12": "refer-2",
					"slot_13": "",
					"slot_14": "",
					"slot_15": "",
					"slot_16": "help-1",
					"slot_17": "trust-marker",
				},
				BottomNavBarSlotSection: map[string]string{
					"slot_1": "home-1",
					"slot_2": "",
					"slot_3": "loans-2",
					"slot_4": "analyser-1",
					"slot_5": "discover-1",
				},
				StickyIconSlotSection: map[string]string{
					"slot_1": "",
					"slot_2": "",
					"slot_3": "",
				},
			},
			wantErr: false,
		},
		{
			name: "should return invest-3 element on bottom nav if user is eligible & has intent in cross sell priority list",
			args: args{
				ctx:            context.Background(),
				actorId:        "actor-id",
				layoutV2Params: dynConf.HomeRevampParamsForUnitTests().HomeLayoutV2ParamsWithAttributes(),
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient, mockOnboardingClient *mockOnboarding.MockOnboardingClient, mockUsersClient *mockUsers.MockUsersClient, mockUserGroupClient *mockUserGroup.MockGroupClient, mockCrossAttachClient *mocks.MockCrossAttachClient) {
				segmentMock.EXPECT().IsMemberOfExpressions(gomock.Any(), &segmentPb.IsMemberOfExpressionsRequest{
					ActorId:              "actor-id",
					SegmentIdExpressions: []string{"IsMember('segment-1')"},
				}).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status: rpc.StatusOk(),
					SegmentExpressionMembershipMap: map[string]*segmentPb.SegmentExpressionMembership{
						"IsMember('segment-1')": {
							SegmentExpressionStatus: segmentPb.SegmentExpressionStatus_OK,
							IsActorMember:           true,
						},
					},
				}, nil).Times(1)
				mockCrossAttachClient.EXPECT().GetCrossSellInfo(gomock.Any(), gomock.Any()).Return(&crossAttachPb.GetCrossSellInfoResponse{
					Status:       rpc.StatusOk(),
					CanCrossSell: commontypes.BooleanEnum_TRUE,
					PrioritizedProductsToPitch: []productPb.ProductType{
						productPb.ProductType_PRODUCT_TYPE_USSTOCKS,
						productPb.ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT,
						productPb.ProductType_PRODUCT_TYPE_PERSONAL_LOANS,
					},
				}, nil).Times(1)
				mockOnboardingClient.EXPECT().GetFeatureLifecycle(gomock.Any(), &onbPb.GetFeatureLifecycleRequest{
					ActorId:        "actor-id",
					Features:       []onbPb.Feature{onbPb.Feature_FEATURE_PL, onbPb.Feature_FEATURE_SA, onbPb.Feature_FEATURE_UPI_TPAP},
					WantCachedData: true,
				}).Return(&onbPb.GetFeatureLifecycleResponse{
					Status: rpc.StatusOk(),
					FeatureLifecycleMap: map[string]*onbPb.FeatureLifecycle{
						onbPb.Feature_FEATURE_PL.String(): {
							IntentSelectionInfo: &onbPb.FeatureIntentSelectionInfo{
								SelectedAsHardIntent: true,
							},
						},
						onbPb.Feature_FEATURE_SA.String(): {
							EligibilityStatus: &onbPb.FeatureEligibility{
								Status: onbPb.FeatureEligibility_STATUS_PASSED,
							},
						},
						onbPb.Feature_FEATURE_UPI_TPAP.String(): {
							ActivationStatus: onbPb.FeatureStatus_FEATURE_STATUS_UNSPECIFIED,
						},
					},
				}, nil).Times(1)
				mockUsersClient.EXPECT().GetUser(gomock.Any(), &usersPb.GetUserRequest{
					Identifier: &usersPb.GetUserRequest_ActorId{
						ActorId: "actor-id",
					},
				}).Return(&usersPb.GetUserResponse{
					Status: rpc.StatusOk(),
					User: &usersPb.User{
						Profile: &usersPb.Profile{
							Email: "<EMAIL>",
						},
					},
				}, nil).Times(1)
				mockUserGroupClient.EXPECT().GetGroupsMappedToIdentifier(gomock.Any(), &usergroupPb.GetGroupsMappedToIdentifierRequest{
					IdentifierValue: &usergroupPb.IdentifierValue{
						Identifier: &usergroupPb.IdentifierValue_Email{
							Email: "<EMAIL>",
						},
					},
				}).Return(&usergroupPb.GetGroupsMappedToIdentifierResponse{
					Status: rpc.StatusOk(),
					Groups: []commontypes.UserGroup{
						commontypes.UserGroup_INTERNAL,
						commontypes.UserGroup_FI_STORE_INTERNAL,
					},
				}, nil).Times(1)
			},
			want: &config.SlotIdToScreenElementIdMap{
				LayoutId: "ae8ae416-281a-58d6-b15e-************",
				TopNavBarSlotSection: &config.TopNavBarSlotSection{
					LeftSlots: map[string]string{
						"slot_1": "profile-1",
					},
					RightSlots: map[string]string{
						"slot_1": "refer-1",
						"slot_2": "card-1",
						"slot_3": "notification-1",
					},
				},
				DashboardSlotSection: map[string]string{
					"slot_1": "intro-1",
					"slot_2": "primarysavings-1",
					"slot_3": "connectedaccounts-1",
					"slot_4": "invest-1",
					"slot_5": "creditcards-1",
					"slot_6": "loans-1",
				},
				VerticalSlotSection: map[string]string{
					"slot_1":  "searchbar-1",
					"slot_2":  "promotionalbanner-1",
					"slot_3":  "suggestedforyou-1",
					"slot_4":  "shortcuts-2",
					"slot_5":  "primary-feature-1",
					"slot_6":  "",
					"slot_7":  "analyser-2",
					"slot_8":  "secondary-feature-1",
					"slot_9":  "rewards-2",
					"slot_10": "rewards-3",
					"slot_11": "tabbed-card-1",
					"slot_12": "refer-2",
					"slot_13": "",
					"slot_14": "",
					"slot_15": "",
					"slot_16": "help-1",
					"slot_17": "trust-marker",
				},
				BottomNavBarSlotSection: map[string]string{
					"slot_1": "home-1",
					"slot_2": "",
					"slot_3": "invest-3",
					"slot_4": "analyser-1",
					"slot_5": "discover-1",
				},
				StickyIconSlotSection: map[string]string{
					"slot_1": "",
					"slot_2": "",
					"slot_3": "",
				},
			},
			wantErr: false,
		},
		{
			name: "should return cc SlotIdToScreenElementIdMap in bottom nav, when cc element is in both bottom and top nav bar (no duplicate elements should present on overall slots of layout)",
			args: args{
				ctx:            context.Background(),
				actorId:        "actor-id",
				layoutV2Params: dynConf.HomeRevampParamsForUnitTests().HomeLayoutV2ParamsWithNoDuplicateElements(),
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient, mockOnboardingClient *mockOnboarding.MockOnboardingClient, mockUsersClient *mockUsers.MockUsersClient, mockUserGroupClient *mockUserGroup.MockGroupClient, mockCrossAttachClient *mocks.MockCrossAttachClient) {
				segmentMock.EXPECT().IsMemberOfExpressions(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status: rpc.StatusOk(),
					SegmentExpressionMembershipMap: map[string]*segmentPb.SegmentExpressionMembership{
						"IsMember('segment-1')": {
							SegmentExpressionStatus: segmentPb.SegmentExpressionStatus_OK,
							IsActorMember:           false,
						},
						"IsMember('segment-2')": {
							SegmentExpressionStatus: segmentPb.SegmentExpressionStatus_OK,
							IsActorMember:           true,
						},
					},
				}, nil).Times(1)
			},
			want: &config.SlotIdToScreenElementIdMap{
				LayoutId: "49360a1e-de03-5978-992a-9cf236dce873",
				TopNavBarSlotSection: &config.TopNavBarSlotSection{
					LeftSlots: map[string]string{
						"slot_1": "profile-1",
					},
					RightSlots: map[string]string{
						"slot_1": "refer-1",
						"slot_2": "analyser-1",
						"slot_3": "notification-1",
					},
				},
				DashboardSlotSection: map[string]string{
					"slot_1": "intro-1",
					"slot_2": "primarysavings-1",
					"slot_3": "networth-1",
					"slot_4": "invest-1",
					"slot_5": "creditcards-1",
					"slot_6": "loans-1",
				},
				VerticalSlotSection: map[string]string{
					"slot_1":  "searchbar-1",
					"slot_2":  "promotionalbanner-1",
					"slot_3":  "suggestedforyou-1",
					"slot_4":  "shortcuts-2",
					"slot_5":  "primary-feature-1",
					"slot_6":  "recentupcomingactivities-1",
					"slot_7":  "analyser-2",
					"slot_8":  "secondary-feature-1",
					"slot_9":  "rewards-2",
					"slot_10": "rewards-3",
					"slot_11": "tabbed-card-1",
					"slot_12": "refer-2",
					"slot_13": "",
					"slot_14": "",
					"slot_15": "",
					"slot_16": "help-1",
					"slot_17": "trust-marker",
				},
				BottomNavBarSlotSection: map[string]string{
					"slot_1": "home-1",
					"slot_2": "pay-1",
					"slot_3": "invest-3",
					"slot_4": "card-1",
					"slot_5": "discover-1",
				},
				StickyIconSlotSection: map[string]string{
					"slot_1": "qr_code-1",
					"slot_2": "qr_code-2",
					"slot_3": "qr_code-3",
				},
			},
			wantErr: false,
		},
		{
			name: "should return loans SlotIdToScreenElementIdMap, when user is more affinity towards loan and user falls in cc and loans",
			args: args{
				ctx:            context.Background(),
				actorId:        "actor-id",
				layoutV2Params: dynConf.HomeRevampParamsForUnitTests().HomeLayoutV2ParamsWithPriorityElements(),
			},
			setupMockCalls: func(segmentMock *mockSegmentation.MockSegmentationServiceClient, mockOnboardingClient *mockOnboarding.MockOnboardingClient, mockUsersClient *mockUsers.MockUsersClient, mockUserGroupClient *mockUserGroup.MockGroupClient, mockCrossAttachClient *mocks.MockCrossAttachClient) {
				segmentMock.EXPECT().IsMemberOfExpressions(gomock.Any(), gomock.Any()).Return(&segmentPb.IsMemberOfExpressionsResponse{
					Status: rpc.StatusOk(),
					SegmentExpressionMembershipMap: map[string]*segmentPb.SegmentExpressionMembership{
						"IsMember('segment-1')": {
							SegmentExpressionStatus: segmentPb.SegmentExpressionStatus_OK,
							IsActorMember:           false,
						},
						"IsMember('segment-2')": {
							SegmentExpressionStatus: segmentPb.SegmentExpressionStatus_OK,
							IsActorMember:           true,
						},
						"IsMember('segment-3')": {
							SegmentExpressionStatus: segmentPb.SegmentExpressionStatus_OK,
							IsActorMember:           true,
						},
					},
				}, nil).Times(1)
			},
			want: &config.SlotIdToScreenElementIdMap{
				LayoutId: "393c42cd-4d8d-59a4-9afd-25e936908826",
				TopNavBarSlotSection: &config.TopNavBarSlotSection{
					LeftSlots: map[string]string{
						"slot_1": "profile-1",
					},
					RightSlots: map[string]string{
						"slot_1": "refer-1",
						"slot_2": "card-1",
						"slot_3": "notification-1",
					},
				},
				DashboardSlotSection: map[string]string{
					"slot_1": "intro-1",
					"slot_2": "primarysavings-1",
					"slot_3": "networth-1",
					"slot_4": "invest-1",
					"slot_5": "creditcards-1",
					"slot_6": "loans-1",
				},
				VerticalSlotSection: map[string]string{
					"slot_1":  "searchbar-1",
					"slot_2":  "promotionalbanner-1",
					"slot_3":  "suggestedforyou-1",
					"slot_4":  "shortcuts-2",
					"slot_5":  "primary-feature-1",
					"slot_6":  "recentupcomingactivities-1",
					"slot_7":  "analyser-2",
					"slot_8":  "secondary-feature-1",
					"slot_9":  "rewards-2",
					"slot_10": "rewards-3",
					"slot_11": "tabbed-card-1",
					"slot_12": "refer-2",
					"slot_13": "",
					"slot_14": "",
					"slot_15": "",
					"slot_16": "help-1",
					"slot_17": "trust-marker",
				},
				BottomNavBarSlotSection: map[string]string{
					"slot_1": "home-1",
					"slot_2": "pay-1",
					"slot_3": "invest-3",
					"slot_4": "loans-2",
					"slot_5": "discover-1",
				},
				StickyIconSlotSection: map[string]string{
					"slot_1": "qr_code-1",
					"slot_2": "qr_code-2",
					"slot_3": "qr_code-3",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockSegment := mockSegmentation.NewMockSegmentationServiceClient(ctr)
			mockOnboardingClient := mockOnboarding.NewMockOnboardingClient(ctr)
			mockUsersClient := mockUsers.NewMockUsersClient(ctr)
			mockUserGroupClient := mockUserGroup.NewMockGroupClient(ctr)
			mockCrossAttachClient := mocks.NewMockCrossAttachClient(ctr)

			segmentExprAttributeEvaluator := attributes.NewSegmentExprAttributeEvaluator(mockSegment)
			featureLifecycleAttributeEvaluator := attributes.NewFeatureLifecycleAttributeEvaluator(mockOnboardingClient)
			userGroupAttributeEvaluator := attributes.NewUserGroupAttributeEvaluator(mockUsersClient, mockUserGroupClient)
			crossAttachAttributeEvaluator := attributes.NewCrossAttachAttributeEvaluator(mockCrossAttachClient)

			attributeEvaluatorFactory := attributes.NewAttributeEvaluatorFactory(segmentExprAttributeEvaluator, featureLifecycleAttributeEvaluator, userGroupAttributeEvaluator, crossAttachAttributeEvaluator)
			priorityOrderSortingStrategy := sort.NewPrioritySortingStrategy()
			sortingStrategyFactory := sort.NewSortingStrategyFactory(priorityOrderSortingStrategy)

			s := &Engine{
				AttributeEvaluatorFactory: attributeEvaluatorFactory,
				SortingStrategyFactory:    sortingStrategyFactory,
			}
			tt.setupMockCalls(mockSegment, mockOnboardingClient, mockUsersClient, mockUserGroupClient, mockCrossAttachClient)
			got, err := s.EvaluateLayout(tt.args.ctx, tt.args.actorId, tt.args.layoutV2Params)
			if (err != nil) != tt.wantErr {
				t.Errorf("EvaluateLayout() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("EvaluateLayout() got = %v, want = %v, \n diff = %v", got, tt.want, diff)
			}
		})
	}
}
