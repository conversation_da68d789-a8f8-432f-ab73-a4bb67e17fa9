package constants

type ComponentId string

func (c ComponentId) String() string {
	return string(c)
}

const (
	// section component ids
	TopNavBarSectionComponentId    ComponentId = "TopNavBarSection-1"
	DashboardSectionComponentId    ComponentId = "DashboardSection-1"
	MiddleSectionComponentId       ComponentId = "MiddleSection-1"
	BottomNavBarSectionComponentId ComponentId = "BottomNavBarSection-1"

	// sticky top component ids
	MaintenanceStickyTopComponentId ComponentId = "maintenance-1"

	// dashboard component ids
	IntroDashboardComponentId             ComponentId = "intro-1"
	PrimarySavingDashboardComponentId     ComponentId = "primarysavings-1"
	NreSavingDashboardComponentId         ComponentId = "nresavings-1"
	NroSavingDashboardComponentId         ComponentId = "nrosavings-1"
	ConnectedAccountsDashboardComponentId ComponentId = "connectedaccounts-1"
	InvestmentSummaryDashboardComponentId ComponentId = "invest-1"
	CreditCardDashboardComponentId        ComponentId = "creditcards-1"
	LoansDashboardComponentId             ComponentId = "loans-1"
	NetworthDashboardComponentId          ComponentId = "networth-1"
	MutualfundDashboardComponentId        ComponentId = "mutualfund-card-1"
	EpfDashboardComponentId               ComponentId = "epf-card-1"
	CreditScoreDashboardComponentId       ComponentId = "creditscore-card-1"

	// scrollable component ids
	CriticalNotificationScrollableComponentId           ComponentId = "critical-notification"
	SearchBarScrollableComponentId                      ComponentId = "searchbar-1"
	PromotionalBannerScrollableComponentId              ComponentId = "promotionalbanner-1"
	ShortcutsScrollableComponentId                      ComponentId = "shortcuts-2"
	SuggestedForYouScrollableComponentId                ComponentId = "suggestedforyou-1"
	SalaryAccountScrollableComponentId                  ComponentId = "salaryaccount-1"
	RecentAndUpcomingScrollableComponentId              ComponentId = "recentupcomingactivities-1"
	PromotionalBanner2ScrollableComponentId             ComponentId = "promotionalbanner2-1"
	AnalyserCardsScrollableComponentId                  ComponentId = "analyser-2"
	RewardsScrollableComponentId                        ComponentId = "rewards-1"
	CatalogOffersScrollableComponentId                  ComponentId = "rewards-2"
	CardOffersScrollableComponentId                     ComponentId = "rewards-3"
	InvestmentScrollableComponentId                     ComponentId = "invest-2"
	ReferralScrollableComponentId                       ComponentId = "refer-2"
	PrimaryFeatureScrollableComponentId                 ComponentId = "primary-feature-1"
	SecondaryFeatureScrollableComponentId               ComponentId = "secondary-feature-1"
	TrustMarkerScrollableComponentId                    ComponentId = "trust-marker"
	HelpScrollableComponentId                           ComponentId = "help-1"
	TabbedCardScrollableComponentId                     ComponentId = "tabbed-card-1"
	MoneySecretsScrollableComponentId                   ComponentId = "money-secrets-1"
	UpiConnectScrollableComponentId                     ComponentId = "upi-connect"
	JourneysScrollableComponentId                       ComponentId = "journeys"
	WealthAnalyserScrollableComponentId                 ComponentId = "wealth-analyser"
	DcInternationalWidgetScrollableComponentId          ComponentId = "dc-international-widget"
	ActivationWidgetScrollableComponentId               ComponentId = "activation-widget"
	WealthBuilderNetworthDashboardScrollableComponentId ComponentId = "wealth-builder-networth-dashboard"

	// bottom nav bar component ids
	NetWorthBottomNavBarId      ComponentId = "networth-2"
	AnalyserBottomNavBarId      ComponentId = "analyser-1"
	WealthBuilderBottomNavBarId ComponentId = "wealth-builder-1"

	HomeBottomNavBarId = "home-1"

	USStocksBottomNavBarId   ComponentId = "usstocks-1"
	InvestmentBottomNavBarId ComponentId = "invest-3"
	// top nav bar component ids
	NetworthTopNavBarId      ComponentId = "networth-2"
	WealthBuilderTopNavBarId ComponentId = "wealth-builder-1"
)

var StickyTopComponentIds = []string{
	TopNavBarSectionComponentId.String(),
	MaintenanceStickyTopComponentId.String(),
}

var ScrollableComponentIds = []string{
	DashboardSectionComponentId.String(),
	SearchBarScrollableComponentId.String(),
	PromotionalBannerScrollableComponentId.String(),
	RecentAndUpcomingScrollableComponentId.String(),
	SuggestedForYouScrollableComponentId.String(),
	RewardsScrollableComponentId.String(),
	ReferralScrollableComponentId.String(),
	HelpScrollableComponentId.String(),
	CriticalNotificationScrollableComponentId.String(),
	InvestmentScrollableComponentId.String(),
	PromotionalBanner2ScrollableComponentId.String(),
	AnalyserCardsScrollableComponentId.String(),
	ShortcutsScrollableComponentId.String(),
	CatalogOffersScrollableComponentId.String(),
	CardOffersScrollableComponentId.String(),
	PrimaryFeatureScrollableComponentId.String(),
	SecondaryFeatureScrollableComponentId.String(),
	TrustMarkerScrollableComponentId.String(),
	TabbedCardScrollableComponentId.String(),
	MoneySecretsScrollableComponentId.String(),
	UpiConnectScrollableComponentId.String(),
	JourneysScrollableComponentId.String(),
	WealthAnalyserScrollableComponentId.String(),
	DcInternationalWidgetScrollableComponentId.String(),
	ActivationWidgetScrollableComponentId.String(),
	WealthBuilderNetworthDashboardScrollableComponentId.String(),
}

var StickyBottomComponentIds = []string{
	BottomNavBarSectionComponentId.String(),
}

var DashboardSubComponentIds = []string{
	IntroDashboardComponentId.String(),
	PrimarySavingDashboardComponentId.String(),
	NreSavingDashboardComponentId.String(),
	NroSavingDashboardComponentId.String(),
	ConnectedAccountsDashboardComponentId.String(),
	InvestmentSummaryDashboardComponentId.String(),
	CreditCardDashboardComponentId.String(),
	LoansDashboardComponentId.String(),
	NetworthDashboardComponentId.String(),
}
