package home

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"google.golang.org/grpc/metadata"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/gamma/api/frontend/header"
	feHomePb "github.com/epifi/gamma/api/frontend/home"
	"github.com/epifi/gamma/api/segment"
	mocks2 "github.com/epifi/gamma/api/segment/mocks"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/home/<USER>"
)

func TestService_getBestLayoutForUser(t *testing.T) {
	var (
		segmentId1 = "AWS_segment1"
		segmentId2 = "segment2"
		actorId    = "actorId"
	)
	ctrl := gomock.NewController(t)
	mockSegmentServiceClient := mocks2.NewMockSegmentationServiceClient(ctrl)
	ctx := context.Background()
	ctx = context.WithValue(ctx, epificontext.CtxAppPlatformKey, header.Platform_ANDROID.String())
	ctx = context.WithValue(ctx, epificontext.CtxAppVersionCodeKey, "2000")
	ctx = epificontext.WithAppVersionCode(ctx, metadata.MD{})
	ctx = epificontext.WithAppPlatform(ctx, metadata.MD{})

	type test struct {
		name                     string
		mockSegmentServiceClient func(mockSegmentServiceClient *mocks2.MockSegmentationServiceClient)
		wantBestLayout           *layoutconfiguration.HomeLayout
	}

	tests := []test{
		{
			mockSegmentServiceClient: func(mockSegmentServiceClient *mocks2.MockSegmentationServiceClient) {
				mockSegmentServiceClient.EXPECT().IsMember(ctx, gomock.Any()).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						segmentId1: {
							IsActorMember: true,
						},
						segmentId2: {
							IsActorMember: true,
						},
					},
				}, nil)
			},
			wantBestLayout: &layoutconfiguration.HomeLayout{
				BottomWidget: []feHomePb.HomeWidget_WidgetType{
					feHomePb.HomeWidget_WIDGET_TYPE_CRITICAL_NOTIFICATION,
					feHomePb.HomeWidget_WIDGET_TYPE_DASHBOARD,
					feHomePb.HomeWidget_WIDGET_TYPE_SEARCH_BAR,
					feHomePb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER,
					feHomePb.HomeWidget_WIDGET_TYPE_SUGGESTED_FOR_YOU,
					feHomePb.HomeWidget_WIDGET_TYPE_PROMOTIONAL_BANNER_2,
					feHomePb.HomeWidget_WIDGET_TYPE_RECENT_UPCOMING_ACTIVITIES,
					feHomePb.HomeWidget_WIDGET_TYPE_SALARY_ACCOUNT,
					feHomePb.HomeWidget_WIDGET_TYPE_REWARDS,
					feHomePb.HomeWidget_WIDGET_TYPE_INVEST_HOME_ELEMENT,
					feHomePb.HomeWidget_WIDGET_TYPE_REFERRAL,
					feHomePb.HomeWidget_WIDGET_TYPE_HELP,
				},
				TopWidgetLeftIcons:  nil,
				TopWidgetRightIcons: nil,
				BottomBarIcons:      nil,
			},
		},
		{
			mockSegmentServiceClient: func(mockSegmentServiceClient *mocks2.MockSegmentationServiceClient) {
				mockSegmentServiceClient.EXPECT().IsMember(ctx, gomock.Any()).Return(&segment.IsMemberResponse{
					Status: rpc.StatusOk(),
					SegmentMembershipMap: map[string]*segment.SegmentMembership{
						segmentId1: {
							IsActorMember: false,
						},
						segmentId2: {
							IsActorMember: false,
						},
					},
				}, nil)
			},
			wantBestLayout: nil,
		},

		{
			mockSegmentServiceClient: func(mockSegmentServiceClient *mocks2.MockSegmentationServiceClient) {
				mockSegmentServiceClient.EXPECT().IsMember(ctx, gomock.Any()).Return(nil, nil)
			},
			wantBestLayout: nil,
		},
	}

	s := NewService(conf, gconf, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, mockSegmentServiceClient, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockSegmentServiceClient != nil {
				tt.mockSegmentServiceClient(mockSegmentServiceClient)
			}
			bestLayout := s.getBestLayoutForUser(ctx, conf.HomeRevampParams, actorId)
			if !reflect.DeepEqual(tt.wantBestLayout, bestLayout) {
				t.Errorf("Layout mismatched got: %v, want: %v", bestLayout, tt.wantBestLayout)
			}
		})
	}
}

func TestService_getLayoutV2FromSegment(t *testing.T) {
	var (
		segmentId1 = "IsMember('562e2816-dcfb-4812-b7d4-c1c739e3aa3b')"
		segmentId2 = "IsMember('85fd8ec4-c854-42ce-b925-b6313ebe2d37')"
		actorId    = "actorId"
	)
	ctrl := gomock.NewController(t)
	mockSegmentServiceClient := mocks2.NewMockSegmentationServiceClient(ctrl)
	ctx := context.Background()
	ctx = context.WithValue(ctx, epificontext.CtxAppPlatformKey, header.Platform_ANDROID.String())
	ctx = context.WithValue(ctx, epificontext.CtxAppVersionCodeKey, "2000")
	ctx = epificontext.WithAppVersionCode(ctx, metadata.MD{})
	ctx = epificontext.WithAppPlatform(ctx, metadata.MD{})

	type test struct {
		name                     string
		mockSegmentServiceClient func(mockSegmentServiceClient *mocks2.MockSegmentationServiceClient)
		wantBestLayout           *config.SlotIdToScreenElementIdMap
	}

	tests := []test{
		{
			name: "Should successfully get the layout string for user in the segment",
			mockSegmentServiceClient: func(mockSegmentServiceClient *mocks2.MockSegmentationServiceClient) {
				mockSegmentServiceClient.EXPECT().IsMemberOfExpressions(ctx, gomock.Any()).Return(&segment.IsMemberOfExpressionsResponse{
					Status: rpc.StatusOk(),
					SegmentExpressionMembershipMap: map[string]*segment.SegmentExpressionMembership{
						segmentId1: {
							SegmentExpressionStatus: segment.SegmentExpressionStatus_OK,
							IsActorMember:           true,
						},
						segmentId2: {
							SegmentExpressionStatus: segment.SegmentExpressionStatus_OK,
							IsActorMember:           false,
						},
					},
				}, nil)
			},
			wantBestLayout: &config.SlotIdToScreenElementIdMap{
				LayoutId: "f43aec57-44df-43a5-bea1-dc921be5ac7f",
				TopNavBarSlotSection: &config.TopNavBarSlotSection{
					LeftSlots: map[string]string{
						"slot_1": "profile-1",
					},
					RightSlots: map[string]string{
						"slot_1": "refer-1",
						"slot_2": "card-1",
						"slot_3": "notification-1",
					},
				},
				DashboardSlotSection: map[string]string{
					"slot_1": "intro-1",
					"slot_2": "primarysavings-1",
					"slot_3": "networth-1",
					"slot_4": "invest-1",
					"slot_5": "creditcards-1",
					"slot_6": "loans-1",
				},
				VerticalSlotSection: map[string]string{
					"slot_1":  "searchbar-1",
					"slot_2":  "promotionalbanner-1",
					"slot_3":  "suggestedforyou-1",
					"slot_4":  "shortcuts-2",
					"slot_5":  "promotionalbanner2-1",
					"slot_6":  "recentupcomingactivities-1",
					"slot_7":  "salaryaccount-1",
					"slot_8":  "rewards-1",
					"slot_9":  "analyser-2",
					"slot_10": "invest-2",
					"slot_11": "refer-2",
					"slot_12": "",
					"slot_13": "",
					"slot_14": "",
					"slot_15": "",
					"slot_16": "",
					"slot_17": "help-1",
				},
				BottomNavBarSlotSection: map[string]string{
					"slot_1": "home-1",
					"slot_2": "pay-1",
					"slot_3": "invest-3",
					"slot_4": "analyser-1",
					"slot_5": "discover-1",
				},
				StickyIconSlotSection: map[string]string{
					"slot_1": "qr_code-1",
					"slot_2": "qr_code-2",
					"slot_3": "qr_code-3",
				},
			},
		},
		{
			name: "Should return empty string because of non-ok segment expression status",
			mockSegmentServiceClient: func(mockSegmentServiceClient *mocks2.MockSegmentationServiceClient) {
				mockSegmentServiceClient.EXPECT().IsMemberOfExpressions(ctx, gomock.Any()).Return(&segment.IsMemberOfExpressionsResponse{
					Status: rpc.StatusOk(),
					SegmentExpressionMembershipMap: map[string]*segment.SegmentExpressionMembership{
						segmentId1: {
							SegmentExpressionStatus: segment.SegmentExpressionStatus_SEGMENT_EXPRESSION_STATUS_UNSPECIFIED,
							IsActorMember:           true,
						},
						segmentId2: {
							SegmentExpressionStatus: segment.SegmentExpressionStatus_OK,
							IsActorMember:           false,
						},
					},
				}, nil)
			},
			wantBestLayout: nil,
		},
		{
			name: "Should return empty string because of nil response from segment service for this user",
			mockSegmentServiceClient: func(mockSegmentServiceClient *mocks2.MockSegmentationServiceClient) {
				mockSegmentServiceClient.EXPECT().IsMemberOfExpressions(ctx, gomock.Any()).Return(nil, nil)
			},
			wantBestLayout: nil,
		},
		{
			name: "Should successfully get the layout string for user in the second matching segment",
			mockSegmentServiceClient: func(mockSegmentServiceClient *mocks2.MockSegmentationServiceClient) {
				mockSegmentServiceClient.EXPECT().IsMemberOfExpressions(ctx, gomock.Any()).Return(&segment.IsMemberOfExpressionsResponse{
					Status: rpc.StatusOk(),
					SegmentExpressionMembershipMap: map[string]*segment.SegmentExpressionMembership{
						segmentId1: {
							SegmentExpressionStatus: segment.SegmentExpressionStatus_SEGMENT_EXPRESSION_STATUS_UNSPECIFIED,
							IsActorMember:           true,
						},
						segmentId2: {
							SegmentExpressionStatus: segment.SegmentExpressionStatus_OK,
							IsActorMember:           true,
						},
					},
				}, nil)
			},
			wantBestLayout: &config.SlotIdToScreenElementIdMap{
				LayoutId: "82e5af23-a97d-4fb7-b2ac-523f71c68a56",
				TopNavBarSlotSection: &config.TopNavBarSlotSection{
					LeftSlots: map[string]string{
						"slot_1": "profile-1",
					},
					RightSlots: map[string]string{
						"slot_1": "",
						"slot_2": "card-1",
						"slot_3": "notification-1",
					},
				},
				DashboardSlotSection: map[string]string{
					"slot_1": "intro-1",
					"slot_2": "primarysavings-1",
					"slot_3": "networth-1",
					"slot_4": "invest-1",
					"slot_5": "creditcards-1",
					"slot_6": "loans-1",
				},
				VerticalSlotSection: map[string]string{
					"slot_1":  "searchbar-1",
					"slot_2":  "promotionalbanner-1",
					"slot_3":  "suggestedforyou-1",
					"slot_4":  "shortcuts-2",
					"slot_5":  "salaryaccount-1",
					"slot_6":  "recentupcomingactivities-1",
					"slot_7":  "promotionalbanner2-1",
					"slot_8":  "rewards-1",
					"slot_9":  "analyser-2",
					"slot_10": "invest-2",
					"slot_11": "",
					"slot_12": "",
					"slot_13": "",
					"slot_14": "",
					"slot_15": "",
					"slot_16": "",
					"slot_17": "help-1",
				},
				BottomNavBarSlotSection: map[string]string{
					"slot_1": "home-1",
					"slot_2": "pay-1",
					"slot_3": "invest-3",
					"slot_4": "analyser-1",
					"slot_5": "discover-1",
				},
				StickyIconSlotSection: map[string]string{
					"slot_1": "qr_code-1",
					"slot_2": "qr_code-2",
					"slot_3": "qr_code-3",
				},
			},
		},
	}

	s := NewService(conf, gconf, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil,
		nil, nil, nil, mockSegmentServiceClient, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.mockSegmentServiceClient != nil {
				tt.mockSegmentServiceClient(mockSegmentServiceClient)
			}
			bestLayout := s.getLayoutV2FromSegment(ctx, actorId, s.genconf.HomeRevampParams().HomeLayoutV2Params())
			if !reflect.DeepEqual(tt.wantBestLayout, bestLayout) {
				t.Errorf("Layout mismatched got: %v, want: %v", bestLayout, tt.wantBestLayout)
			}
		})
	}
}
