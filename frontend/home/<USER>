package home

import (
	"context"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/mohae/deepcopy"
	"github.com/stretchr/testify/require"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/money"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feHomePb "github.com/epifi/gamma/api/frontend/home"
	"github.com/epifi/gamma/api/kyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config"
	deepLinkPkg "github.com/epifi/gamma/pkg/deeplink"
	deeplinkCfg "github.com/epifi/gamma/pkg/deeplink/cfg"
	releaseMock "github.com/epifi/gamma/pkg/feature/release/mocks"
)

func TestService_getHomeLayoutV2FromString(t *testing.T) {
	tests := []struct {
		name         string
		layoutString string
		want         *config.SlotIdToScreenElementIdMap
		wantErr      bool
	}{
		{
			name:         "Test - get home layoutV2 SlotIdToScreenElementIdMap from string",
			wantErr:      false,
			layoutString: "{\"LayoutId\":\"26f81686-6422-11ee-8c99-0242ac120002\",\"TopNavBarSlotSection\":{\"LeftSlots\":{\"slot_1\":\"profile-1\"},\"RightSlots\":{\"slot_1\":\"refer-1\",\"slot_2\":\"card-1\",\"slot_3\":\"notification-1\"}},\"DashboardSlotSection\":{\"slot_1\":\"intro-1\",\"slot_2\":\"primarysavings-1\",\"slot_3\":\"connectedaccounts-1\",\"slot_4\":\"invest-1\",\"slot_5\":\"creditcards-1\",\"slot_6\":\"loans-1\"},\"VerticalSlotSection\":{\"slot_1\":\"searchbar-1\",\"slot_2\":\"promotionalbanner-1\",\"slot_3\":\"shortcuts-2\",\"slot_4\":\"suggestedforyou-1\",\"slot_5\":\"salaryaccount-1\",\"slot_6\":\"recentupcomingactivities-1\",\"slot_7\":\"promotionalbanner2-1\",\"slot_8\":\"rewards-1\",\"slot_9\":\"analyser-2\",\"slot_10\":\"invest-2\",\"slot_11\":\"refer-2\",\"slot_12\":\"help-1\"},\"BottomNavBarSlotSection\":{\"slot_1\":\"home-1\",\"slot_2\":\"pay-1\",\"slot_3\":\"invest-3\",\"slot_4\":\"analyser-1\",\"slot_5\":\"discover-1\"},\"StickyIconSlotSection\":{\"slot_1\":\"qr_code-1\",\"slot_2\":\"qr_code-2\",\"slot_3\":\"qr_code-3\"}}",
			want: &config.SlotIdToScreenElementIdMap{
				LayoutId: "26f81686-6422-11ee-8c99-0242ac120002",
				TopNavBarSlotSection: &config.TopNavBarSlotSection{
					LeftSlots: map[string]string{
						"slot_1": "profile-1",
					},
					RightSlots: map[string]string{
						"slot_1": "refer-1",
						"slot_2": "card-1",
						"slot_3": "notification-1",
					},
				},
				DashboardSlotSection: map[string]string{
					"slot_1": "intro-1",
					"slot_2": "primarysavings-1",
					"slot_3": "connectedaccounts-1",
					"slot_4": "invest-1",
					"slot_5": "creditcards-1",
					"slot_6": "loans-1",
				},
				VerticalSlotSection: map[string]string{
					"slot_1":  "searchbar-1",
					"slot_2":  "promotionalbanner-1",
					"slot_3":  "shortcuts-2",
					"slot_4":  "suggestedforyou-1",
					"slot_5":  "salaryaccount-1",
					"slot_6":  "recentupcomingactivities-1",
					"slot_7":  "promotionalbanner2-1",
					"slot_8":  "rewards-1",
					"slot_9":  "analyser-2",
					"slot_10": "invest-2",
					"slot_11": "refer-2",
					"slot_12": "help-1",
				},
				BottomNavBarSlotSection: map[string]string{
					"slot_1": "home-1",
					"slot_2": "pay-1",
					"slot_3": "invest-3",
					"slot_4": "analyser-1",
					"slot_5": "discover-1",
				},
				StickyIconSlotSection: map[string]string{
					"slot_1": "qr_code-1",
					"slot_2": "qr_code-2",
					"slot_3": "qr_code-3",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{}
			got, err := s.getSlotIdToScreenElementIdMapFromString(tt.layoutString)
			if (err != nil) != tt.wantErr {
				t.Errorf("getSlotIdToScreenElementIdMapFromString() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getSlotIdToScreenElementIdMapFromString() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getScreenElementToSlotMap(t *testing.T) {
	tests := []struct {
		name         string
		sourceLayout *config.SlotIdToScreenElementIdMap
		args         args
		want         *config.ScreenElementIdToSlotIdMap
	}{
		{
			name: "Test for getting screen element to slot map",
			sourceLayout: &config.SlotIdToScreenElementIdMap{
				LayoutId: "26f81686-6422-11ee-8c99-0242ac120002",
				TopNavBarSlotSection: &config.TopNavBarSlotSection{
					LeftSlots: map[string]string{
						"slot_1": "profile-1",
					},
					RightSlots: map[string]string{
						"slot_1": "refer-1",
						"slot_2": "card-1",
						"slot_3": "notification-1",
					},
				},
				DashboardSlotSection: map[string]string{
					"slot_1": "intro-1",
					"slot_2": "primarysavings-1",
					"slot_3": "connectedaccounts-1",
					"slot_4": "invest-1",
					"slot_5": "creditcards-1",
					"slot_6": "loans-1",
				},
				VerticalSlotSection: map[string]string{
					"slot_1":  "searchbar-1",
					"slot_2":  "promotionalbanner-1",
					"slot_3":  "shortcuts-2",
					"slot_4":  "suggestedforyou-1",
					"slot_5":  "salaryaccount-1",
					"slot_6":  "recentupcomingactivities-1",
					"slot_7":  "promotionalbanner2-1",
					"slot_8":  "rewards-1",
					"slot_9":  "analyser-2",
					"slot_10": "invest-2",
					"slot_11": "refer-2",
					"slot_12": "help-1",
				},
				BottomNavBarSlotSection: map[string]string{
					"slot_1": "home-1",
					"slot_2": "pay-1",
					"slot_3": "invest-3",
					"slot_4": "analyser-1",
					"slot_5": "discover-1",
				},
				StickyIconSlotSection: map[string]string{
					"slot_1": "qr_code-1",
					"slot_2": "qr_code-2",
					"slot_3": "qr_code-3",
				},
			},
			want: &config.ScreenElementIdToSlotIdMap{
				TopNavBarSlotSection: &config.TopNavBarSlotSection{
					LeftSlots: map[string]string{
						"profile-1": "slot_1",
					},
					RightSlots: map[string]string{
						"refer-1":        "slot_1",
						"card-1":         "slot_2",
						"notification-1": "slot_3",
					},
				},
				DashboardSlotSection: map[string]string{
					"intro-1":             "slot_1",
					"primarysavings-1":    "slot_2",
					"connectedaccounts-1": "slot_3",
					"invest-1":            "slot_4",
					"creditcards-1":       "slot_5",
					"loans-1":             "slot_6",
				},
				VerticalSlotSection: map[string]string{
					"searchbar-1":                "slot_1",
					"promotionalbanner-1":        "slot_2",
					"shortcuts-2":                "slot_3",
					"suggestedforyou-1":          "slot_4",
					"salaryaccount-1":            "slot_5",
					"recentupcomingactivities-1": "slot_6",
					"promotionalbanner2-1":       "slot_7",
					"rewards-1":                  "slot_8",
					"analyser-2":                 "slot_9",
					"invest-2":                   "slot_10",
					"refer-2":                    "slot_11",
					"help-1":                     "slot_12",
				},
				BottomNavBarSection: map[string]string{
					"home-1":     "slot_1",
					"pay-1":      "slot_2",
					"invest-3":   "slot_3",
					"analyser-1": "slot_4",
					"discover-1": "slot_5",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getScreenElementToSlotMap(tt.sourceLayout); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getScreenElementToSlotMap() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getNewlyAddedScreenElementsInTargetLayout(t *testing.T) {
	type args struct {
		targetLayout                        *config.SlotIdToScreenElementIdMap
		sourceLayoutScreenElementsToSlotMap *config.ScreenElementIdToSlotIdMap
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			name: "Test - get newly added screen elements in target layout - when a new screen element id is present in target layout",
			args: args{
				sourceLayoutScreenElementsToSlotMap: &config.ScreenElementIdToSlotIdMap{
					TopNavBarSlotSection: &config.TopNavBarSlotSection{
						LeftSlots: map[string]string{
							"profile-1": "slot_1",
						},
						RightSlots: map[string]string{
							"refer-1":        "slot_1",
							"card-1":         "slot_2",
							"notification-1": "slot_3",
						},
					},
					DashboardSlotSection: map[string]string{
						"intro-1":             "slot_1",
						"primarysavings-1":    "slot_2",
						"connectedaccounts-1": "slot_3",
						"invest-1":            "slot_4",
						"creditcards-1":       "slot_5",
						"loans-1":             "slot_6",
					},
					VerticalSlotSection: map[string]string{
						"searchbar-1":                "slot_1",
						"promotionalbanner-1":        "slot_2",
						"shortcuts-2":                "slot_3",
						"suggestedforyou-1":          "slot_4",
						"salaryaccount-1":            "slot_5",
						"recentupcomingactivities-1": "slot_6",
						"promotionalbanner2-1":       "slot_7",
						"rewards-1":                  "slot_8",
						"analyser-2":                 "slot_9",
						"invest-2":                   "slot_10",
						"refer-2":                    "slot_11",
						"help-1":                     "slot_12",
					},
					BottomNavBarSection: map[string]string{
						"home-1":     "slot_1",
						"pay-1":      "slot_2",
						"invest-3":   "slot_3",
						"analyser-1": "slot_4",
						"discover-1": "slot_5",
					},
				},
				targetLayout: &config.SlotIdToScreenElementIdMap{
					LayoutId: "26f81686-6422-11ee-8c99-0242ac120002",
					TopNavBarSlotSection: &config.TopNavBarSlotSection{
						LeftSlots: map[string]string{
							"slot_1": "profile-1",
						},
						RightSlots: map[string]string{
							"slot_1": "refer-1",
							"slot_2": "card-1",
							"slot_3": "notification-1",
						},
					},
					DashboardSlotSection: map[string]string{
						"slot_1": "intro-1",
						"slot_2": "primarysavings-1",
						"slot_3": "connectedaccounts-1",
						"slot_4": "invest-1",
						"slot_5": "creditcards-1",
						"slot_6": "loans-1",
					},
					VerticalSlotSection: map[string]string{
						"slot_1":  "searchbar-1",
						"slot_2":  "promotionalbanner-1",
						"slot_3":  "shortcuts-2",
						"slot_4":  "suggestedforyou-1",
						"slot_5":  "salaryaccount-1",
						"slot_6":  "recentupcomingactivities-1",
						"slot_7":  "promotionalbanner2-1",
						"slot_8":  "rewards-1",
						"slot_9":  "analyser-2",
						"slot_10": "invest-2",
						"slot_11": "refer-2",
						"slot_12": "help-1",
					},
					BottomNavBarSlotSection: map[string]string{
						"slot_1": "home-1",
						"slot_2": "pay-1",
						"slot_3": "invest-3",
						"slot_4": "analyser-1",
						"slot_5": "discover-2",
					},
				},
			},
			want: []string{"discover-2"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getNewlyAddedScreenElementsInTargetLayout(tt.args.targetLayout, tt.args.sourceLayoutScreenElementsToSlotMap); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getNewlyAddedScreenElementsInTargetLayout() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_doesScreenElementSlotDiffCrossThreshold(t *testing.T) {
	type args struct {
		screenElement string
		sourceSection map[string]string
		targetSection map[string]string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "Test - does screen element slot diff crosses threshold - 5 slots diff",
			args: args{
				sourceSection: map[string]string{
					"searchbar-1":                "slot_1",
					"promotionalbanner-1":        "slot_2",
					"shortcuts-2":                "slot_3",
					"suggestedforyou-1":          "slot_4",
					"salaryaccount-1":            "slot_5",
					"recentupcomingactivities-1": "slot_6",
					"promotionalbanner2-1":       "slot_7",
					"rewards-1":                  "slot_8",
					"analyser-2":                 "slot_9",
					"invest-2":                   "slot_10",
					"refer-2":                    "slot_11",
					"help-1":                     "slot_12",
				},
				targetSection: map[string]string{
					"searchbar-1":                "slot_1",
					"promotionalbanner-1":        "slot_2",
					"shortcuts-2":                "slot_3",
					"suggestedforyou-1":          "slot_4",
					"salaryaccount-1":            "slot_5",
					"recentupcomingactivities-1": "slot_6",
					"help-1":                     "slot_7",
					"rewards-1":                  "slot_8",
					"analyser-2":                 "slot_9",
					"invest-2":                   "slot_10",
					"refer-2":                    "slot_11",
					"promotionalbanner2-1":       "slot_12",
				},
				screenElement: "help-1",
			},
			want: true,
		},
		{
			name: "Test - does screen element slot diff less than threshold - less than 5 slots diff",
			args: args{
				sourceSection: map[string]string{
					"searchbar-1":                "slot_1",
					"promotionalbanner-1":        "slot_2",
					"shortcuts-2":                "slot_3",
					"suggestedforyou-1":          "slot_4",
					"salaryaccount-1":            "slot_5",
					"recentupcomingactivities-1": "slot_6",
					"promotionalbanner2-1":       "slot_7",
					"rewards-1":                  "slot_8",
					"analyser-2":                 "slot_9",
					"invest-2":                   "slot_10",
					"refer-2":                    "slot_11",
					"help-1":                     "slot_12",
				},
				targetSection: map[string]string{
					"searchbar-1":                "slot_1",
					"promotionalbanner-1":        "slot_2",
					"shortcuts-2":                "slot_3",
					"suggestedforyou-1":          "slot_4",
					"salaryaccount-1":            "slot_5",
					"recentupcomingactivities-1": "slot_6",
					"promotionalbanner2-1":       "slot_7",
					"rewards-1":                  "slot_8",
					"analyser-2":                 "slot_9",
					"help-1":                     "slot_10",
					"refer-2":                    "slot_11",
					"invest-2":                   "slot_12",
				},
				screenElement: "help-1",
			},
			want: false,
		},
	}
	for _, tt := range tests {
		s := &Service{}
		t.Run(tt.name, func(t *testing.T) {
			if got := s.doesScreenElementSlotDiffCrossThreshold(tt.args.screenElement, tt.args.sourceSection, tt.args.targetSection); got != tt.want {
				t.Errorf("doesScreenElementSlotDiffCrossThreshold() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getDiscrepancyIcon(t *testing.T) {
	type args struct {
		ctx                      context.Context
		availableBal             *gmoney.Money
		ledgerBal                *gmoney.Money
		toShowTieringRelatedInfo bool
		tieringPitchResp         *beTieringPb.GetTieringPitchV2Response
	}
	tieringGraceResp := &beTieringPb.GetTieringPitchV2Response{
		Status:      rpc.StatusOk(),
		CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
		MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
			{
				TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
				IsMovementAllowed: false,
				Options: []*criteriaPb.Option{
					{
						Actions: []*criteriaPb.Action{
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Balance{
										Balance: &criteriaPb.Balance{
											MinBalance: &gmoney.Money{
												CurrencyCode: "INR",
												Units:        10000,
											},
										},
									},
								},
							},
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Kyc{
										Kyc: &criteriaPb.Kyc{
											KycLevel: kyc.KYCLevel_FULL_KYC,
										},
									},
								},
							},
						},
					},
				},
			},
			{
				TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
				IsMovementAllowed: true,
				Options: []*criteriaPb.Option{
					{
						Actions: []*criteriaPb.Action{
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Balance{
										Balance: &criteriaPb.Balance{
											MinBalance: &gmoney.Money{
												CurrencyCode: "INR",
												Units:        50000,
											},
										},
									},
								},
							},
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Kyc{
										Kyc: &criteriaPb.Kyc{
											KycLevel: kyc.KYCLevel_FULL_KYC,
										},
									},
								},
							},
						},
					},
				},
			},
			{
				TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
				IsMovementAllowed: true,
				Options: []*criteriaPb.Option{
					{
						Actions: []*criteriaPb.Action{
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Kyc{
										Kyc: &criteriaPb.Kyc{
											KycLevel: kyc.KYCLevel_FULL_KYC,
										},
									},
								},
							},
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Salary{
										Salary: &criteriaPb.Salary{
											MinSalary: &gmoney.Money{
												CurrencyCode: "INR",
												Units:        20000,
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	tieringNonGraceResp := &beTieringPb.GetTieringPitchV2Response{
		Status:      rpc.StatusOk(),
		CurrentTier: tieringExtPb.Tier_TIER_FI_PLUS,
		MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
			{
				TierName:          tieringExtPb.Tier_TIER_FI_PLUS,
				IsMovementAllowed: true,
				Options: []*criteriaPb.Option{
					{
						Actions: []*criteriaPb.Action{
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Balance{
										Balance: &criteriaPb.Balance{
											MinBalance: &gmoney.Money{
												CurrencyCode: "INR",
												Units:        10000,
											},
										},
									},
								},
							},
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Kyc{
										Kyc: &criteriaPb.Kyc{
											KycLevel: kyc.KYCLevel_FULL_KYC,
										},
									},
								},
							},
						},
					},
				},
			},
			{
				TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
				IsMovementAllowed: true,
				Options: []*criteriaPb.Option{
					{
						Actions: []*criteriaPb.Action{
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Balance{
										Balance: &criteriaPb.Balance{
											MinBalance: &gmoney.Money{
												CurrencyCode: "INR",
												Units:        50000,
											},
										},
									},
								},
							},
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Kyc{
										Kyc: &criteriaPb.Kyc{
											KycLevel: kyc.KYCLevel_FULL_KYC,
										},
									},
								},
							},
						},
					},
				},
			},
			{
				TierName:          tieringExtPb.Tier_TIER_FI_SALARY,
				IsMovementAllowed: true,
				Options: []*criteriaPb.Option{
					{
						Actions: []*criteriaPb.Action{
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Kyc{
										Kyc: &criteriaPb.Kyc{
											KycLevel: kyc.KYCLevel_FULL_KYC,
										},
									},
								},
							},
							{
								ActionDetails: &criteriaPb.QualifyingCriteria{
									Criteria: &criteriaPb.QualifyingCriteria_Salary{
										Salary: &criteriaPb.Salary{
											MinSalary: &gmoney.Money{
												CurrencyCode: "INR",
												Units:        20000,
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	tests := []struct {
		name string
		args args
		want *feHomePb.Icon
	}{
		{
			name: "available balance and ledger balance are not equal, not to show tiering info",
			args: args{
				ctx: context.Background(),
				availableBal: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        200,
				},
				ledgerBal: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        201,
				},
				toShowTieringRelatedInfo: false,
				tieringPitchResp:         nil,
			},
			want: &feHomePb.Icon{
				Action: &feHomePb.Icon_Deeplink{
					Deeplink: &deeplinkPb.Deeplink{
						ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
							InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
								TextTitle: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "You may not be able to use some parts of your fund right now",
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "available balance and ledger balance are not equal, to show tiering info, user in grace",
			args: args{
				ctx: context.Background(),
				availableBal: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        200,
				},
				ledgerBal: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        201,
				},
				toShowTieringRelatedInfo: true,
				tieringPitchResp:         tieringGraceResp,
			},
			want: &feHomePb.Icon{
				Action: &feHomePb.Icon_Deeplink{
					Deeplink: &deeplinkPb.Deeplink{
						ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
							InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
								TextTitle: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "You will lose access to 2x rewards and your Plus plan",
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "available balance and ledger balance are not equal, to show tiering info, user not in grace",
			args: args{
				ctx: context.Background(),
				availableBal: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        200,
				},
				ledgerBal: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        201,
				},
				toShowTieringRelatedInfo: true,
				tieringPitchResp:         tieringNonGraceResp,
			},
			want: &feHomePb.Icon{
				Action: &feHomePb.Icon_Deeplink{
					Deeplink: &deeplinkPb.Deeplink{
						ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
							InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
								TextTitle: &commontypes.Text{
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: "You may not be able to use some parts of your fund right now",
									},
								},
							},
						},
					},
				},
			},
		},
		{
			name: "available balance and ledger balance are equal",
			args: args{
				ctx: context.Background(),
				availableBal: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        200,
				},
				ledgerBal: &gmoney.Money{
					CurrencyCode: "INR",
					Units:        200,
				},
				toShowTieringRelatedInfo: true,
				tieringPitchResp:         tieringNonGraceResp,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				conf:       conf,
				homeParams: conf.HomeParams,
			}
			if got := s.getDiscrepancyIcon(tt.args.ctx, tt.args.availableBal, tt.args.ledgerBal, tt.args.toShowTieringRelatedInfo, tt.args.tieringPitchResp); !reflect.DeepEqual(got.GetDeeplink().GetInformationPopupOptions().GetTextTitle().GetPlainString(),
				tt.want.GetDeeplink().GetInformationPopupOptions().GetTextTitle().GetPlainString()) {
				t.Errorf("getDiscrepancyIcon() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getNonCriticalWarningTickerIconTextComponent(t *testing.T) {
	t.Parallel()

	personalisedAccountDiscrepancyDeeplink := deepcopy.Copy(conf.HomeParams.AccountDiscrepancyDeeplink).(*deeplinkCfg.Deeplink)

	personalisedAccountDiscrepancyDeeplink.InformationPopupDeeplinkScreenOptions.SubTitle = fmt.Sprintf(personalisedAccountDiscrepancyDeeplink.InformationPopupDeeplinkScreenOptions.SubTitle, money.ToDisplayString(&gmoney.Money{
		CurrencyCode: "INR",
		Units:        6000,
	}), money.ToDisplayString(&gmoney.Money{
		CurrencyCode: "INR",
		Units:        5000,
	}))
	if personalisedAccountDiscrepancyDeeplink.InformationPopupDeeplinkScreenOptions.TextSubTitle != nil {
		personalisedAccountDiscrepancyDeeplink.InformationPopupDeeplinkScreenOptions.TextSubTitle.PlainString = fmt.Sprintf(personalisedAccountDiscrepancyDeeplink.InformationPopupDeeplinkScreenOptions.TextSubTitle.PlainString, money.ToDisplayString(&gmoney.Money{
			CurrencyCode: "INR",
			Units:        6000,
		}), money.ToDisplayString(&gmoney.Money{
			CurrencyCode: "INR",
			Units:        5000,
		}))
	}

	personalisedDl, dlErr := deepLinkPkg.NewDeeplinkFromConfig(personalisedAccountDiscrepancyDeeplink)
	require.NoError(t, dlErr, "Error in generating personalised account discrepancy deeplink")

	type args struct {
		toShowTieringRelatedInfo bool
		tieringPitchResp         *beTieringPb.GetTieringPitchV2Response
		balSummaryRes            *savingsPb.GetAccountBalanceWithSummaryResponse
	}
	type want struct {
		privacyModeIconTextComponent    *ui.IconTextComponent
		nonPrivacyModeIconTextComponent *ui.IconTextComponent
	}

	tests := []struct {
		name string
		args args
		want want
	}{
		{
			name: "should return low balance icon text component when account balance is less than 500 units",
			args: args{
				toShowTieringRelatedInfo: false,
				tieringPitchResp: &beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_INFINITE,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: false,
							MovementTimestamp: nil,
							Options: []*criteriaPb.Option{
								{
									CriteriaOptionType: 0,
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{Criteria: &criteriaPb.QualifyingCriteria_Balance{Balance: &criteriaPb.Balance{
												MinBalance: &gmoney.Money{
													CurrencyCode: "INR",
													Units:        0,
													Nanos:        0,
												},
												MaxBalance: &gmoney.Money{
													CurrencyCode: "INR",
													Units:        25000,
													Nanos:        0,
												},
											}}},
										},
									},
								},
							},
							GraceParams: nil,
						},
					},
				},
				balSummaryRes: &savingsPb.GetAccountBalanceWithSummaryResponse{
					Status: rpc.StatusOk(),
					OpeningBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        10000,
					},
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        400,
					},
					LedgerBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
					},
					IsBalanceStale: false,
				},
			},
			want: want{
				privacyModeIconTextComponent: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-red-warning.png"),
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Low balance"},
							FontColor:    "#929599",
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
					Deeplink:           getDeeplinkForLowBalance(),
				},
				nonPrivacyModeIconTextComponent: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-red-warning.png"),
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Low balance"},
							FontColor:    "#929599",
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
					Deeplink:           getDeeplinkForLowBalance(),
				},
			},
		},
		{
			name: "should return low balance icon text component when account balance is less than 500 units",
			args: args{
				toShowTieringRelatedInfo: false,
				tieringPitchResp: &beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_INFINITE,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: false,
							MovementTimestamp: nil,
							Options: []*criteriaPb.Option{
								{
									CriteriaOptionType: 0,
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{Criteria: &criteriaPb.QualifyingCriteria_Balance{Balance: &criteriaPb.Balance{
												MinBalance: &gmoney.Money{
													CurrencyCode: "INR",
													Units:        0,
													Nanos:        0,
												},
												MaxBalance: &gmoney.Money{
													CurrencyCode: "INR",
													Units:        25000,
													Nanos:        0,
												},
											}}},
										},
									},
								},
							},
							GraceParams: nil,
						},
					},
				},
				balSummaryRes: &savingsPb.GetAccountBalanceWithSummaryResponse{
					Status: rpc.StatusOk(),
					OpeningBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        10000,
					},
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        -100,
					},
					LedgerBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        1000,
					},
					IsBalanceStale: false,
				},
			},
			want: want{
				privacyModeIconTextComponent: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-red-warning.png"),
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Your balance is -100. Some funds will be available soon"},
							FontColor:    "#929599",
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
					Deeplink:           getDeeplinkForLowBalance(),
				},
				nonPrivacyModeIconTextComponent: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-red-warning.png"),
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Your balance is -100. Some funds will be available soon"},
							FontColor:    "#929599",
							FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
					Deeplink:           getDeeplinkForLowBalance(),
				},
			},
		},
		{
			name: "should return some funds not available icon text component when ledger balance is not same as available balance",
			args: args{
				toShowTieringRelatedInfo: false,
				tieringPitchResp: &beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_INFINITE,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: false,
							MovementTimestamp: nil,
							Options: []*criteriaPb.Option{
								{
									CriteriaOptionType: 0,
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{Criteria: &criteriaPb.QualifyingCriteria_Balance{Balance: &criteriaPb.Balance{
												MinBalance: &gmoney.Money{
													CurrencyCode: "INR",
													Units:        0,
													Nanos:        0,
												},
												MaxBalance: &gmoney.Money{
													CurrencyCode: "INR",
													Units:        25000,
													Nanos:        0,
												},
											}}},
										},
									},
								},
							},
							GraceParams: nil,
						},
					},
				},
				balSummaryRes: &savingsPb.GetAccountBalanceWithSummaryResponse{
					Status: rpc.StatusOk(),
					OpeningBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        10000,
					},
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        5000,
					},
					LedgerBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        6000,
					},
					IsBalanceStale: true,
					BalanceAt:      timestamppb.New(time.Date(2025, 01, 27, 0, 0, 0, 0, datetime.IST)),
				},
			},
			want: want{
				privacyModeIconTextComponent: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: "Balance isn't up-to-date. Some funds will be available soon.",
							},
							FontColor: "#929599",
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
					Deeplink:           personalisedDl,
				},
				nonPrivacyModeIconTextComponent: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: fmt.Sprintf("Your actual balance is %v. Some funds will be available soon.", convertMoneyToReadableString(&gmoney.Money{
									CurrencyCode: "INR",
									Units:        6000,
								})),
							},
							FontColor: "#929599",
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
					Deeplink:           personalisedDl,
				},
			},
		},
		{
			name: "should return stale balance icon text component when the balance status is stale",
			args: args{
				toShowTieringRelatedInfo: false,
				tieringPitchResp: &beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_INFINITE,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: false,
							MovementTimestamp: nil,
							Options: []*criteriaPb.Option{
								{
									CriteriaOptionType: 0,
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{Criteria: &criteriaPb.QualifyingCriteria_Balance{Balance: &criteriaPb.Balance{
												MinBalance: &gmoney.Money{
													CurrencyCode: "INR",
													Units:        0,
													Nanos:        0,
												},
												MaxBalance: &gmoney.Money{
													CurrencyCode: "INR",
													Units:        25000,
													Nanos:        0,
												},
											}}},
										},
									},
								},
							},
							GraceParams: nil,
						},
					},
				},
				balSummaryRes: &savingsPb.GetAccountBalanceWithSummaryResponse{
					Status: rpc.StatusOk(),
					OpeningBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        10000,
					},
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        5000,
					},
					LedgerBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        5000,
					},
					IsBalanceStale: true,
					BalanceAt:      timestamppb.New(time.Date(2025, 01, 27, 0, 0, 0, 0, datetime.IST)),
				},
			},
			want: want{
				privacyModeIconTextComponent: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: fmt.Sprintf(
									gconf.HomeBalanceSummaryWidgetUiConfig().StaleBalanceWarningTickerIconTextComponent().TextContent(),
									timestamppb.New(time.Date(2025, 01, 27, 0, 0, 0, 0, datetime.IST)).AsTime().In(datetime.IST).Format(conf.HomeParams.StaleBalanceTimeFormat),
								),
							},
							FontColor: "#929599",
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
					Deeplink:           nil,
				},
				nonPrivacyModeIconTextComponent: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: fmt.Sprintf(
									gconf.HomeBalanceSummaryWidgetUiConfig().StaleBalanceWarningTickerIconTextComponent().TextContent(),
									timestamppb.New(time.Date(2025, 01, 27, 0, 0, 0, 0, datetime.IST)).AsTime().In(datetime.IST).Format(conf.HomeParams.StaleBalanceTimeFormat),
								),
							},
							FontColor: "#929599",
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
					Deeplink:           nil,
				},
			},
		},
		{
			name: "should return computed balance stale icon text component when the computed balance status is stale",
			args: args{
				toShowTieringRelatedInfo: false,
				tieringPitchResp: &beTieringPb.GetTieringPitchV2Response{
					Status:      rpc.StatusOk(),
					CurrentTier: tieringExtPb.Tier_TIER_FI_INFINITE,
					MovementDetailsList: []*tieringExtPb.MovementExternalDetails{
						{
							TierName:          tieringExtPb.Tier_TIER_FI_INFINITE,
							IsMovementAllowed: false,
							MovementTimestamp: nil,
							Options: []*criteriaPb.Option{
								{
									CriteriaOptionType: 0,
									Actions: []*criteriaPb.Action{
										{
											ActionDetails: &criteriaPb.QualifyingCriteria{Criteria: &criteriaPb.QualifyingCriteria_Balance{Balance: &criteriaPb.Balance{
												MinBalance: &gmoney.Money{
													CurrencyCode: "INR",
													Units:        0,
													Nanos:        0,
												},
												MaxBalance: &gmoney.Money{
													CurrencyCode: "INR",
													Units:        25000,
													Nanos:        0,
												},
											}}},
										},
									},
								},
							},
							GraceParams: nil,
						},
					},
				},
				balSummaryRes: &savingsPb.GetAccountBalanceWithSummaryResponse{
					Status: rpc.StatusOk(),
					OpeningBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        10000,
					},
					AvailableBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        5000,
					},
					LedgerBalance: &gmoney.Money{
						CurrencyCode: "INR",
						Units:        5000,
					},
					IsComputedBalanceStale: true,
					BalanceAt:              timestamppb.New(time.Date(2025, 01, 27, 0, 0, 0, 0, datetime.IST)),
				},
			},
			want: want{
				privacyModeIconTextComponent: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: gconf.HomeBalanceSummaryWidgetUiConfig().StaleComputedBalanceWarningTickerIconTextComponent().TextContent(),
							},
							FontColor: "#929599",
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
					Deeplink:           nil,
				},
				nonPrivacyModeIconTextComponent: &ui.IconTextComponent{
					LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/union-orange-warning.png"),
					Texts: []*commontypes.Text{
						{
							DisplayValue: &commontypes.Text_PlainString{
								PlainString: gconf.HomeBalanceSummaryWidgetUiConfig().StaleComputedBalanceWarningTickerIconTextComponent().TextContent(),
							},
							FontColor: "#929599",
							FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
						},
					},
					RightVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/right-arrow-vector.png"),
					Deeplink:           nil,
				},
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			releaseEvaluatorMock := releaseMock.NewMockIEvaluator(ctrl)
			releaseEvaluatorMock.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes()
			s := &Service{
				conf:       conf,
				genconf:    gconf,
				homeParams: conf.HomeParams,
				evaluator:  releaseEvaluatorMock,
			}
			gotNonPrivacyComponent, gotPrivacyComponent := s.getNonCriticalWarningTickerIconTextComponent(context.Background(), "", tc.args.toShowTieringRelatedInfo, tc.args.tieringPitchResp, tc.args.balSummaryRes)

			require.Equal(t, tc.want.nonPrivacyModeIconTextComponent, gotNonPrivacyComponent, "Non-privacy mode component is not same as expected")
			require.Equal(t, tc.want.privacyModeIconTextComponent, gotPrivacyComponent, "Privacy mode component is not same as expected")
		})
	}
}
