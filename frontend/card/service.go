// nolint: dupl,protogetter
package card

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	colorPkg "github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"

	errorsPkg "github.com/pkg/errors"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/thoas/go-funk"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	widgetUi "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	pkggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	authPb "github.com/epifi/gamma/api/auth"
	cardcipb "github.com/epifi/gamma/api/card/currencyinsights"
	beCasperPb "github.com/epifi/gamma/api/casper"
	feCard "github.com/epifi/gamma/api/frontend/card"
	orderActivityPb "github.com/epifi/gamma/api/order/actoractivity"
	"github.com/epifi/gamma/api/pan"
	payPb "github.com/epifi/gamma/api/pay"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/typesv2"
	typesUi "github.com/epifi/gamma/api/typesv2/ui"
	dashboardSections "github.com/epifi/gamma/frontend/card/dashboard_sections"
	pkgCard "github.com/epifi/gamma/pkg/card"
	stringPkg "github.com/epifi/gamma/pkg/string"
	webFeTravel "github.com/epifi/gamma/webfe/travel"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth/liveness"
	"github.com/epifi/gamma/api/bankcust"
	beCardPb "github.com/epifi/gamma/api/card"
	beCcPb "github.com/epifi/gamma/api/card/control"
	bePb "github.com/epifi/gamma/api/card/provisioning"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/frontend"
	fePb "github.com/epifi/gamma/api/frontend/card"
	"github.com/epifi/gamma/api/frontend/card/errormapping"
	"github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	salaryProgramPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	dcScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/debitcard"
	upiPb "github.com/epifi/gamma/api/upi"
	beUserPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config"
	dynConf "github.com/epifi/gamma/frontend/config/genconf"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	featureCfg "github.com/epifi/gamma/pkg/feature"
	"github.com/epifi/gamma/pkg/feature/release"
	errors2 "github.com/epifi/gamma/pkg/frontend/errors"
	payPkg "github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/pkg/qr"
	pkgUser "github.com/epifi/gamma/pkg/user"
	questSdk "github.com/epifi/gamma/quest/sdk"
)

const (
	// Time in seconds
	shippingAddressUpdatePollingTime = 2
	cardCreationStatusPollingTime    = 2
	cardBlockStatusPollingTime       = 2
	physicalCardDispatchPollingTime  = 2
	vendorKey                        = "vendor"
	cardActivationEncryptedData      = "digestValue"
)

var (
	feProvenanceToBeProvenanceMap = map[fePb.Provenance]beCardPb.Provenance{
		fePb.Provenance_USER_APP: beCardPb.Provenance_USER_APP,
		fePb.Provenance_SHERLOCK: beCardPb.Provenance_SHERLOCK,
	}
	bePinSetFlowToFePinSetFlow = map[bePb.PinSetFlow]fePb.PinSetFlow{
		bePb.PinSetFlow_PIN_SET_WITH_OTP:   fePb.PinSetFlow_PIN_SET_WITH_OTP,
		bePb.PinSetFlow_PIN_SET_WITH_TOKEN: fePb.PinSetFlow_PIN_SET_WITH_TOKEN,
	}
	cardCreationBusinessFailureReasons = []string{accountClosure}

	cardPrintingVendorMap = map[string]bePb.CardPrintingVendor{
		"mct": bePb.CardPrintingVendor_MCT,
	}

	feCardFormToBeCardFormMap = map[fePb.CardForm]beCardPb.CardForm{
		fePb.CardForm_PHYSICAL: beCardPb.CardForm_PHYSICAL,
		fePb.CardForm_DIGITAL:  beCardPb.CardForm_DIGITAL,
	}

	physicalCardBenefitsSortingOrder = []pkgCard.PhysicalCardBenefitType{
		pkgCard.PhysicalCardBenefitTypeSpends,
		pkgCard.PhysicalCardBenefitTypeCashBack,
		pkgCard.PhysicalCardBenefitTypeAtmWithdrawal,
		pkgCard.PhysicalCardBenefitTypeCardOffers,
		pkgCard.PhysicalCardBenefitTypeFeeDetails,
	}
)

// Service implements Card GRPC service for the FE layer. All the cardProvisioningClient facing card RPCs
// must be implemented by this service.
type Service struct {
	fePb.UnimplementedCardServer
	cardProvisioningClient   bePb.CardProvisioningClient
	cardCtrlClient           beCcPb.CardControlClient
	upiClient                upiPb.UPIClient
	config                   *config.Config
	userGroupClient          userGroupPb.GroupClient
	actorClient              actorPb.ActorClient
	userClient               beUserPb.UsersClient
	onboardingClient         onboardingPb.OnboardingClient
	livenessClient           liveness.LivenessClient
	savingsClient            savingsPb.SavingsClient
	kycClient                kyc.KycClient
	vKYCClient               vkycPb.VKYCClient
	featureFlags             *dynConf.Flags
	beTieringClient          beTieringPb.TieringClient
	salaryProgramClient      salaryProgramPb.SalaryProgramClient
	cardDynamicConf          *dynConf.Card
	bankCustClient           bankcust.BankCustomerServiceClient
	releaseEvaluator         release.IEvaluator
	uiSectionBuilderFactory  *dashboardSections.UiSectionBuilderFactory
	offersListingClient      beCasperPb.OfferListingServiceClient
	orderActorActivityClient orderActivityPb.ActorActivityClient
	rewardsGeneratorClient   rewardsPb.RewardsGeneratorClient
	segmentationClient       segment.SegmentationServiceClient
	authClient               authPb.AuthClient
	accountBalanceClient     accountBalancePb.BalanceClient
	eventBroker              events.Broker
	panClient                pan.PanClient
	cardCiClient             cardcipb.CurrencyInsightsClient
	payClient                payPb.PayClient
	dynamicConf              *dynConf.Config
	questSdkClient           *questSdk.Client
	userAttributeFetcher     pkgUser.UserAttributesFetcher
	networthClient           networthPb.NetWorthClient
}

// Factory method for creating an instance of card FE service. This method will be used by the injector
// when providing the dependencies at initialization time.
func NewCardService(provisioningClient bePb.CardProvisioningClient, controlClient beCcPb.CardControlClient,
	upiClient upiPb.UPIClient, config *config.Config, userGroupClient userGroupPb.GroupClient,
	actorClient actorPb.ActorClient, userClient beUserPb.UsersClient, onboardingClient onboardingPb.OnboardingClient,
	livenessClient liveness.LivenessClient, savingsClient savingsPb.SavingsClient, kycClient kyc.KycClient,
	vKYCClient vkycPb.VKYCClient, featureFlags *dynConf.Flags,
	beTieringClient beTieringPb.TieringClient,
	salaryProgramClient salaryProgramPb.SalaryProgramClient,
	cardDynamicConf *dynConf.Card, bcClient bankcust.BankCustomerServiceClient,
	uiSectionBuilderFactory *dashboardSections.UiSectionBuilderFactory, releaseEvaluator release.IEvaluator,
	offersListingClient beCasperPb.OfferListingServiceClient, orderActorActivityClient orderActivityPb.ActorActivityClient,
	rewardsGeneratorClient rewardsPb.RewardsGeneratorClient, segmentationClient segment.SegmentationServiceClient,
	authClient authPb.AuthClient, accountBalanceClient accountBalancePb.BalanceClient, eventBroker events.Broker, panClient pan.PanClient,
	cardCiClient cardcipb.CurrencyInsightsClient, payClient payPb.PayClient, dynamicConf *dynConf.Config,
	questSdkClient *questSdk.Client, userAttributeFetcher pkgUser.UserAttributesFetcher, networthClient networthPb.NetWorthClient) (*Service, error) {
	err := errormapping.LoadCardErrorView(config.CardErrorViewJson)
	if err != nil {
		logger.ErrorNoCtx("error loading card error views", zap.Error(err))
		return nil, err
	}
	return &Service{
		cardProvisioningClient:   provisioningClient,
		cardCtrlClient:           controlClient,
		upiClient:                upiClient,
		config:                   config,
		userClient:               userClient,
		actorClient:              actorClient,
		userGroupClient:          userGroupClient,
		onboardingClient:         onboardingClient,
		livenessClient:           livenessClient,
		savingsClient:            savingsClient,
		kycClient:                kycClient,
		vKYCClient:               vKYCClient,
		featureFlags:             featureFlags,
		beTieringClient:          beTieringClient,
		salaryProgramClient:      salaryProgramClient,
		cardDynamicConf:          cardDynamicConf,
		bankCustClient:           bcClient,
		uiSectionBuilderFactory:  uiSectionBuilderFactory,
		offersListingClient:      offersListingClient,
		releaseEvaluator:         releaseEvaluator,
		orderActorActivityClient: orderActorActivityClient,
		rewardsGeneratorClient:   rewardsGeneratorClient,
		segmentationClient:       segmentationClient,
		authClient:               authClient,
		accountBalanceClient:     accountBalanceClient,
		eventBroker:              eventBroker,
		panClient:                panClient,
		cardCiClient:             cardCiClient,
		payClient:                payClient,
		dynamicConf:              dynamicConf,
		questSdkClient:           questSdkClient,
		userAttributeFetcher:     userAttributeFetcher,
		networthClient:           networthClient,
	}, nil
}

// GetCurrentCards fetches the current card for an actor. An actor can have multiple cards issued, we will fetch
// the card which is in active state i.e. card states in CREATED, ACTIVATED or SUSPENDED. If no cards exist in activated state
// then we will fetch the card in initiated state i.e. cards for which creation request has been initiated and
// pending either from backend or vendor. Lastly if not card exists in activated or initiated states then we will return
// the blocked or expired cards.
func (s *Service) GetCurrentCards(ctx context.Context, req *fePb.GetCurrentCardsRequest) (*fePb.GetCurrentCardsResponse, error) {
	resp := &fePb.GetCurrentCardsResponse{}

	actorResp, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: req.GetReq().GetAuth().GetActorId()})
	if err = epifigrpc.RPCError(actorResp, err); err != nil {
		logger.Error(ctx, "error in finding actor", zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	beResp, err := s.cardProvisioningClient.FetchCards(ctx, &bePb.FetchCardsRequest{
		Actor:        actorResp.GetActor(),
		IssuingBanks: []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
		CardStates: []beCardPb.CardState{beCardPb.CardState_INITIATED, beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED,
			beCardPb.CardState_SUSPENDED, beCardPb.CardState_BLOCKED, beCardPb.CardState_EXPIRED},
		CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
		CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
		CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
		SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
	})
	if te := epifigrpc.RPCError(beResp, err); te != nil {
		logger.Error(ctx, "failed to fetch current cards", zap.String("actor-id", req.GetReq().GetAuth().GetActorId()), zap.Error(te))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	card := GetCurrentCard(beResp.GetCards())

	if card == nil {
		logger.Error(ctx, "failed to fetch card for actor",
			zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	feCard, err := getFeCardDetail(ctx, card)
	if err != nil {
		logger.Error(ctx, "error while fetching card for card id",
			zap.String(logger.CARD_ID, card.Id), zap.Error(err))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}
	resp.Cards = []*fePb.CardDetail{feCard}
	resp.Status = rpc.StatusOk()
	return resp, nil
}

// getCurrentCard return the current card to be shown in UI among all the cards returned from backend.
// Currently only single card is returned to UI.
// TODO(priyansh) : Change this when we support multiple cards in UI
func GetCurrentCard(cards []*beCardPb.Card) *beCardPb.Card {
	var (
		activeCards    []*beCardPb.Card
		initiatedCards []*beCardPb.Card
		blockedCards   []*beCardPb.Card
	)
	for _, card := range cards {
		switch card.GetState() {
		case beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED, beCardPb.CardState_SUSPENDED:
			activeCards = append(activeCards, card)
		case beCardPb.CardState_INITIATED:
			initiatedCards = append(initiatedCards, card)
		case beCardPb.CardState_BLOCKED, beCardPb.CardState_EXPIRED:
			blockedCards = append(blockedCards, card)
		}
	}
	if len(activeCards) != 0 {
		return activeCards[0]
	}
	if len(initiatedCards) != 0 {
		return initiatedCards[0]
	}
	if len(blockedCards) != 0 {
		return blockedCards[0]
	}
	return nil
}

func (s *Service) GetCardDetails(ctx context.Context, req *fePb.GetCardDetailsRequest) (*fePb.GetCardDetailsResponse, error) {
	// TODO(anand) bug-id-764. Handle additional auth validation.
	resp := &fePb.GetCardDetailsResponse{}
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*fePb.GetCardDetailsResponse, error) {
		resp.Status = status
		resp.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return resp, nil
	}
	_, status, errorView := s.actorAuthorisedToAccessCard(ctx, req.GetCardId(), req.GetReq().GetAuth().GetActorId())
	if !status.IsSuccess() {
		return responseWithStatus(status, errorView)
	}

	cardsResp, err := s.cardProvisioningClient.FetchCardDetails(ctx, &bePb.FetchCardDetailsRequest{
		IssuingBank: commonvgpb.Vendor_FEDERAL_BANK,
		CardIds:     []string{req.CardId},
	})
	if te := epifigrpc.RPCError(cardsResp, err); te != nil {
		logger.Error(ctx, "failed to fetch card details", zap.String("card-id", req.CardId), zap.Error(te))
		resp.Status = rpc.StatusInternal()
		return resp, nil
	}

	feCard, err := getFeCardDetail(ctx, cardsResp.Cards[req.CardId])
	if err != nil {
		resp.Status = rpc.StatusInternal()
		logger.Error(ctx, "failed to construct card-details", zap.String("card-id", req.CardId), zap.Error(err))
		return resp, nil
	}
	resp.Status = rpc.StatusOk()
	resp.Card = feCard
	return resp, nil
}

// RPC returns the list of supported reasons a user can select when requesting for a new card.
func (s *Service) GetCardRenewalReasons(context.Context, *fePb.GetCardRenewalReasonsRequest) (*fePb.GetCardRenewalReasonsResponse, error) {
	resp := &fePb.GetCardRenewalReasonsResponse{}
	for reasonType := range fePb.CardRenewalReasonType_name {
		displayString, err := fePb.GetRenewalReasonDisplayString(reasonType)
		if err != nil || reasonType == 0 {
			continue
		}
		resp.Reasons = append(resp.Reasons, &fePb.CardRenewalReason{
			Id:            reasonType,
			DisplayString: displayString,
		})
	}
	sort.SliceStable(resp.Reasons, func(i, j int) bool {
		return resp.Reasons[i].Id < resp.Reasons[j].Id
	})
	resp.Status = rpc.StatusOk()
	return resp, nil
}

func (s *Service) GenerateTxnId(ctx context.Context, req *fePb.GenerateTxnIdRequest) (*fePb.GenerateTxnIdResponse, error) {
	var (
		txnType bePb.TxnType
		err     error
	)
	res := &fePb.GenerateTxnIdResponse{}

	beVendor, ok := req.GetVendor().GetBeVendor()
	if !ok {
		logger.Error(ctx, "no such vendor found")
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if req.GetActionType() == fePb.CardActionType_ENABLE_ATM_WITHDRAWALS || req.GetActionType() == fePb.CardActionType_ENABLE_POS {
		txnType, err = s.getBeTxnTypeForOfflineControlsEnable(ctx, req.GetCardId(), req.GetActionType())
		if err != nil {
			logger.Error(ctx, "failed to fetch txn type", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	} else {
		txnType, err = req.GetActionType().FromFeActionToBeTxnType()
		if err != nil {
			logger.Error(ctx, "Action to txn type not found", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	genTxnIdRes, err := s.cardProvisioningClient.GenerateTxnId(ctx, &bePb.GenerateTxnIdRequest{
		TxnType: txnType,
		Vendor:  beVendor,
		CardId:  req.GetCardId(),
	})

	if genTxnIdRes.GetStatus().GetCode() == uint32(cpPb.GenerateTxnIdResponse_PIN_SET_ATTEMPT_REQUEST_BEFORE_THRESHOLD) {
		res.Status = rpc.NewStatusWithoutDebug(uint32(fePb.GenerateTxnIdResponse_PIN_SET_ATTEMPT_REQUEST_BEFORE_THRESHOLD), genTxnIdRes.GetStatus().GetShortMessage())
		return res, nil
	}
	if te := epifigrpc.RPCError(genTxnIdRes, err); te != nil {
		logger.Error(ctx, "error in getting txn id from card be", zap.String(logger.ACTION_TYPE, req.GetActionType().String()),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if req.GetActionType() == fePb.CardActionType_RESET_PIN || req.GetActionType() == fePb.CardActionType_ACTIVATE {
		switch genTxnIdRes.GetResponseParams().(type) {
		case *bePb.GenerateTxnIdResponse_PinSetFlow:
			pinSetFlow, ok := bePinSetFlowToFePinSetFlow[genTxnIdRes.GetPinSetFlow()]
			if !ok {
				logger.Error(ctx, "invalid pin set flow", zap.String(logger.CARD_ID, req.GetCardId()))
				res.Status = rpc.StatusInternal()
			}
			logger.Info(ctx, "fetched pin set successfully",
				zap.String(logger.CARD_ID, req.GetCardId()), zap.String("pinSetFlow", pinSetFlow.String()),
				zap.String("actionType", req.GetActionType().String()))
			res.GenerateTxnIdResponseParam = &fePb.GenerateTxnIdResponse_PinSetFlow{PinSetFlow: pinSetFlow}
		}
	}

	res.TxnId = genTxnIdRes.GetTxnId()
	res.MaskedCardNumber = genTxnIdRes.GetMaskedCardNumber()
	res.Status = rpc.StatusOk()
	return res, nil
}

// getBeTxnTypeForOfflineControlsEnable fetches the transaction type for ATM and POS enable
// 1. If the current limit for ATM & POS is set to 1 then we will use limit txn type flow as previously we used
// to enable these controls via changing the limits
// 2. If the limit is not 1 and user is still trying to enable the control then we will use the new api txn type
func (s *Service) getBeTxnTypeForOfflineControlsEnable(ctx context.Context, cardId string, feAction fePb.CardActionType) (bePb.TxnType, error) {
	var beTxnType beCardPb.CardTransactionType
	switch feAction {
	case fePb.CardActionType_ENABLE_ATM_WITHDRAWALS:
		beTxnType = beCardPb.CardTransactionType_ATM
	case fePb.CardActionType_ENABLE_POS:
		beTxnType = beCardPb.CardTransactionType_POS
	default:
		return bePb.TxnType_TXN_TYPE_UNSPECIFIED, fmt.Errorf("invalid action %s", feAction.String())
	}
	fetchCardLimitsRes, err := s.cardCtrlClient.FetchCardLimits(ctx, &beCcPb.FetchCardLimitsRequest{CardId: cardId})
	if te := epifigrpc.RPCError(fetchCardLimitsRes, err); te != nil {
		return bePb.TxnType_TXN_TYPE_UNSPECIFIED, fmt.Errorf("error in fetching card limits %w", te)
	}
	for _, limitDetails := range fetchCardLimitsRes.GetCardLimitData().GetCardLimitDetails() {
		if limitDetails.GetTxnType() == beTxnType {
			if moneyPkg.AreEquals(limitDetails.GetCurrentAllowedAmount(), &moneyPb.Money{
				CurrencyCode: moneyPkg.RupeeCurrencyCode,
				Units:        1,
				Nanos:        0,
			}) {
				return bePb.TxnType_LIMIT_TXN_TYPE, nil
			} else {
				return bePb.TxnType_CONSL_CARD_CONTROL_TXN_TYPE, nil
			}
		}
	}
	return bePb.TxnType_TXN_TYPE_UNSPECIFIED, fmt.Errorf("limit could not be fetched for txn type %s", beTxnType.String())
}

func (s *Service) GetCardDetailsWithCvv(ctx context.Context, req *fePb.GetCardDetailsWithCvvRequest) (*fePb.GetCardDetailsWithCvvResponse, error) {
	var (
		res = &fePb.GetCardDetailsWithCvvResponse{}
	)
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*fePb.GetCardDetailsWithCvvResponse, error) {
		res.Status = status
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}
	_, status, errorView := s.actorAuthorisedToAccessCard(ctx, req.GetCardId(), req.GetReq().GetAuth().GetActorId())
	if !status.IsSuccess() {
		return responseWithStatus(status, errorView)
	}
	status, errorView = downtimeCheck(ctx)
	if !status.IsSuccess() {
		return responseWithStatus(status, errorView)
	}
	// TODO(vivek): Add validation if actor has access to the requested card-id.
	cvvEnqRes, err := s.cardProvisioningClient.GetCardDetailsWithCvv(ctx, &bePb.GetCardDetailsWithCvvRequest{
		CardId:    req.GetCardId(),
		CredBlock: req.GetCredBlock(),
		RequestId: req.GetRequestId(),
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in getting card-cvv", zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()),
			zap.String(logger.CARD_ID, req.GetCardId()))
		return responseWithStatus(rpc.StatusInternal(), errormapping.DefaultErrorView)
	case cvvEnqRes.GetStatus().IsDeadlineExceeded():
		logger.Error(ctx, "deadline exceeded while fetching cvv details", zap.String(logger.CARD_ID, req.GetCardId()))
		return responseWithStatus(rpc.StatusDeadlineExceeded(), errormapping.TimeoutErrorView)
	case !cvvEnqRes.GetStatus().IsSuccess():
		logger.Error(ctx, fmt.Sprintf("non-success error code %s for cvv enquiry", cvvEnqRes.GetStatus().String()),
			zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()), zap.String(logger.CARD_ID, req.GetCardId()))
		errorView = errormapping.GetCardErrorView(cvvEnqRes.GetInternalStatusCode())
		return responseWithStatus(cvvEnqRes.GetStatus(), errorView)
	default:
		cardExpiryDate, err := getCardExpiryDate(cvvEnqRes.GetCardInfo().GetExpiry())
		if err != nil {
			logger.Error(ctx, "error while converting card expiry date",
				zap.String("expiry-date", cvvEnqRes.GetCardInfo().GetExpiry()), zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		res.CardInfo = &fePb.BasicCardInfo{
			CardNumber:       cvvEnqRes.GetCardInfo().GetCardNumber(),
			Expiry:           cardExpiryDate,
			Cvv:              cvvEnqRes.GetCardInfo().GetCvv(),
			MaskedCardNumber: cvvEnqRes.GetCardInfo().GetMaskedCardNumber(),
		}
		res.SecureCardInfo = &fePb.SecureCardInfo{
			CardNumber:       []byte(cvvEnqRes.GetCardInfo().GetCardNumber()),
			Expiry:           cardExpiryDate,
			Cvv:              []byte(cvvEnqRes.GetCardInfo().GetCvv()),
			MaskedCardNumber: cvvEnqRes.GetCardInfo().GetMaskedCardNumber(),
			ExpiryToken:      cvvEnqRes.GetCardInfo().GetExpiryToken(),
		}
		res.DisplayData = &fePb.DisplayData{
			Title:       CVVDetailsSuccessTitle,
			Description: CVVDetailsSuccessSubtitle,
		}
		if featureCfg.IsFeatureEnabledForUser(ctx, req.GetReq().GetAuth().GetActorId(), &cfg.FeatureReleaseConfig{
			IsFeatureRestricted: s.cardDynamicConf.EnableDCCopyCardDetailsV1().IsFeatureRestricted(),
			AllowedUserGroups:   s.cardDynamicConf.EnableDCCopyCardDetailsV1().AllowedUserGroups(),
		}, s.userGroupClient, s.userClient, s.actorClient) &&
			pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.cardDynamicConf.EnableDCCopyCardDetails()) {
			res.IsCopyCardEnabled = true
			res.Expiry = 60
			res.OverlayCopyCardText = overlayCopyCardText
			res.EnableOverlayCardDetails = false
		}
	}

	return responseWithStatus(rpc.StatusOk(), nil)
}

func (s *Service) CreateCard(ctx context.Context, req *fePb.CreateCardRequest) (*fePb.CreateCardResponse, error) {
	res := &fePb.CreateCardResponse{}

	actorResp, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: req.GetReq().GetAuth().GetActorId()})
	if err = epifigrpc.RPCError(actorResp, err); err != nil {
		res.Status = rpc.NewStatus(uint32(fePb.CreateCardResponse_FAILED), "error in finding actor", err.Error())
		res.RespHeader = &header.ResponseHeader{Status: res.GetStatus()}
		return res, nil
	}
	createCardRes, err := s.cardProvisioningClient.CreateCard(ctx, &bePb.CreateCardRequest{
		Actor:         actorResp.GetActor(),
		IssuingBank:   commonvgpb.Vendor_FEDERAL_BANK,
		Type:          beCardPb.CardType_DEBIT,
		BlockedCardId: req.GetBlockedCardId(),
		CardSkuType:   beCardPb.CardSKUType_CLASSIC,
	})
	if te := epifigrpc.RPCError(createCardRes, err); te != nil {
		logger.Error(ctx, "error in creating card ",
			zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()), zap.Error(err))
		res.Status = rpc.NewStatus(uint32(fePb.CreateCardResponse_FAILED), "card creation failed", err.Error())
		res.RespHeader = &header.ResponseHeader{Status: res.GetStatus()}
		return res, nil
	}
	res.Status = rpc.NewStatus(uint32(fePb.CreateCardResponse_PENDING), "card creation in pending state", "")
	res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
	cardIds := make([]string, 0)
	for _, card := range createCardRes.GetCards() {
		cardIds = append(cardIds, card.Id)
	}
	res.NewCardIds = cardIds
	res.DisplayMessage = CreateCardPendingMessage
	res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_CREATE_CARD, cardIds[0], res.GetDisplayMessage(), &timestamp.Timestamp{Seconds: int64(0)})
	return res, nil
}

func (s *Service) CheckCreateCardStatus(ctx context.Context, req *fePb.CheckCreateCardStatusRequest) (*fePb.CheckCreateCardStatusResponse, error) {
	res := &fePb.CheckCreateCardStatusResponse{}

	cardCreationStatusResp, err := s.cardProvisioningClient.FetchCardCreationStatus(ctx, &bePb.FetchCardCreationStatusRequest{
		CardIds:     []string{req.GetCardId()},
		IssuingBank: commonvgpb.Vendor_FEDERAL_BANK,
	})
	if te := epifigrpc.RPCError(cardCreationStatusResp, err); te != nil {
		logger.Error(ctx, "error in fetching status for card",
			zap.String(logger.CARD_ID, req.GetCardId()), zap.Error(err))
		res.NextPollTimeGap = &timestamp.Timestamp{
			Seconds: int64(cardCreationStatusPollingTime),
		}
		res.DisplayMessage = CreateCardPendingMessage
		res.Status = rpc.NewStatus(uint32(fePb.CheckCreateCardStatusResponse_PENDING), "card creation pending", err.Error())
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_CREATE_CARD, req.GetCardId(), res.GetDisplayMessage(), res.GetNextPollTimeGap())
		return res, nil
	}

	cardCreationReq, ok := cardCreationStatusResp.GetCreationStates()[req.GetCardId()]
	if !ok {
		res.NextPollTimeGap = &timestamp.Timestamp{
			Seconds: int64(cardCreationStatusPollingTime),
		}
		res.DisplayMessage = CreateCardPendingMessage
		res.Status = rpc.NewStatus(uint32(fePb.CheckCreateCardStatusResponse_PENDING), "card creation pending", "")
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_CREATE_CARD, req.GetCardId(), res.GetDisplayMessage(), res.GetNextPollTimeGap())
		return res, nil
	}

	switch cardCreationReq.GetCardCreationRequest().GetState() {
	case bePb.RequestState_SUCCESS:
		res.Status = rpc.StatusOk()
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		successScreen, _ := s.getSuccessDeeplinkLandingScreen(ctx, cardCreationReq.GetCard(), "Request approved!", "Your brand-new digital card is ready!\nActivate it to start making online transactions.")
		res.NextAction = successScreen
		return res, nil
	case bePb.RequestState_FAILED:
		res.Status = getCardCreationFailureStatusCode(cardCreationReq.GetCardCreationRequest())
		res.RespHeader = &header.ResponseHeader{Status: res.GetStatus()}
		return res, nil
	case bePb.RequestState_QUEUED, bePb.RequestState_INITIATED, bePb.RequestState_MANUAL_INTERVENTION:
		res.DisplayMessage = CreateCardPendingMessage
		res.NextPollTimeGap = &timestamp.Timestamp{
			Seconds: int64(cardCreationStatusPollingTime),
		}
		res.Status = rpc.NewStatus(uint32(fePb.CheckCreateCardStatusResponse_PENDING), "card creation pending", "")
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_CREATE_CARD, req.GetCardId(), res.GetDisplayMessage(), res.GetNextPollTimeGap())
		return res, nil
	default:
		logger.Error(ctx, "invalid card creation status", zap.Any(logger.CARD_ID, req.GetCardId()),
			zap.String("card-creation-status", cardCreationReq.GetCardCreationRequest().GetState().String()))
		res.Status = rpc.StatusInternal()
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusInternal()}
		res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_CREATE_CARD, req.GetCardId(), res.GetDisplayMessage(), res.GetNextPollTimeGap())
		return res, nil
	}
}

// getCardCreationFailureStatusCode checks if card creation has failed due to some business failure and return
// corresponding status code. We will also ignore the rpc alerts in case of these business failures
func getCardCreationFailureStatusCode(cardCreationReq *bePb.CardCreationRequest) *rpc.Status {
	failureResponseReason := cardCreationReq.GetFailureResponseReason()
	if funk.ContainsString(cardCreationBusinessFailureReasons, strings.Trim(failureResponseReason, " ")) {
		return rpc.NewStatus(uint32(fePb.CheckCreateCardStatusResponse_BUSINESS_FAILURE), "card creation "+
			"failed due to business validation", "")
	}
	return rpc.NewStatus(uint32(fePb.CheckCreateCardStatusResponse_FAILED), "card creation failed", "")
}

func (s *Service) RenewCard(ctx context.Context, req *fePb.RenewCardRequest) (*fePb.RenewCardResponse, error) {
	var (
		res = &fePb.RenewCardResponse{}
	)
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*fePb.RenewCardResponse, error) {
		res.Status = status
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}
	logger.Info(ctx, "attempt to renew card", zap.String(logger.CARD_ID, req.GetCardId()))

	status, errorView := downtimeCheck(ctx)
	if !status.IsSuccess() {
		return responseWithStatus(status, errorView)
	}

	beBlockCardProvenance, ok := feProvenanceToBeProvenanceMap[req.GetProvenance()]
	if !ok {
		logger.Error(ctx, "error in fetching block card source",
			zap.String(logger.CARD_ID, req.GetCardId()), zap.String("source", req.GetProvenance().String()))
	}

	beCardForm, ok := feCardFormToBeCardFormMap[req.GetCardForm()]
	if !ok {
		if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.cardDynamicConf.EnableDcCardRenewalChargesFlow()) {
			logger.Error(ctx, "invalid cardForm passed from client", zap.String(logger.CARD_FORM, req.GetCardForm().String()))
			res.DisplayMessage = RenewCardFailure
			return responseWithStatus(rpc.StatusInternalWithDebugMsg("invalid card form"), errors2.NewBottomSheetErrorView(internalErrorStatusCode,
				renewCardFailureErrorTitle, "", internalFailureErrorDescription, &errors.CTA{Type: errors.CTA_CANCEL, Text: okCTA}))
		}
		beCardForm = beCardPb.CardForm_CARD_FORM_UNSPECIFIED
	}

	renewCardRes, err := s.cardProvisioningClient.RenewCard(ctx, &bePb.RenewCardRequest{
		CardId:              req.GetCardId(),
		BlockCardReason:     req.GetReason(),
		BlockCardProvenance: beBlockCardProvenance,
		ActorId:             req.GetReq().GetAuth().GetActorId(),
		AddressType:         req.GetAddressType(),
		CardForm:            beCardForm,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in renew card",
			zap.String(logger.CARD_ID, req.GetCardId()), zap.Error(err))
		res.DisplayMessage = RenewCardFailure
		return responseWithStatus(rpc.NewStatus(uint32(fePb.RenewCardResponse_SHIPPING_ADDRESS_UPDATE_FAILED),
			"shipping address update failed", err.Error()), errors2.NewBottomSheetErrorView(internalErrorStatusCode, renewCardFailureErrorTitle,
			"", internalFailureErrorDescription, &errors.CTA{Type: errors.CTA_DONE, Text: okCTA}))
	case renewCardRes.GetStatus().GetCode() == uint32(fePb.RenewCardResponse_INSUFFICIENT_FUNDS):
		logger.Error(ctx, "precondition failed to initiate card renewal", zap.String(logger.CARD_ID, req.GetCardId()),
			zap.Error(err), zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()))
		return responseWithStatus(rpc.NewStatus(uint32(fePb.RenewCardResponse_INSUFFICIENT_FUNDS),
			"failed to renew card request for physical card due to insufficient funds", ""), errors2.NewBottomSheetErrorView(payPkg.InsufficientBalanceErr,
			s.config.Card.PhysicalCardInsufficientFundsParams.TitleForInsufficientFunds, s.config.Card.PhysicalCardInsufficientFundsParams.DescriptionForInsufficientFunds, "",
			&errors.CTA{Type: errors.CTA_DONE, Text: s.config.Card.PhysicalCardInsufficientFundsParams.CTANameForInsufficientFunds,
				Action: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_TRANSFER_IN,
					ScreenOptions: &deeplinkPb.Deeplink_TransferInScreenOptions{
						TransferInScreenOptions: &deeplinkPb.TransferInScreenOptions{},
					},
				}}))
	case renewCardRes.GetStatus().IsPermissionDenied():
		logger.Error(ctx, "permission denied for renew card", zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()))
		res.DisplayMessage = RenewCardRateLimitBreachedFailure
		return responseWithStatus(rpc.StatusPermissionDenied(), renewCardRateLimitReachedErrorView)
	case renewCardRes.GetStatus().GetCode() == uint32(bePb.RenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_FAILED):
		logger.Error(ctx, "shipping address update failed for renew card", zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()),
			zap.String(logger.STATUS_CODE, renewCardRes.GetStatus().String()))
		res.DisplayMessage = RenewCardFailure
		return responseWithStatus(rpc.NewStatusWithoutDebug(uint32(fePb.RenewCardResponse_SHIPPING_ADDRESS_UPDATE_FAILED), ""),
			errors2.NewBottomSheetErrorView(internalErrorStatusCode, renewCardFailureErrorTitle, "",
				internalFailureErrorDescription, &errors.CTA{Type: errors.CTA_DONE, Text: okCTA}))
	case renewCardRes.GetStatus().IsSuccess():
		logger.Debug(ctx, "initiated card renewal for", zap.String(logger.CARD_ID, req.GetCardId()))
		res.Status = rpc.NewStatus(uint32(fePb.RenewCardResponse_SHIPPING_ADDRESS_UPDATE_PENDING),
			"shipping address update pending", "")
		res.DisplayMessage = ShippingAddressUpdateMessage
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, req.GetCardId(), res.GetDisplayMessage(), &timestamp.Timestamp{Seconds: int64(0)})
		return res, nil
	default:
		logger.Error(ctx, "non success status for new card request", zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()),
			zap.String(logger.STATUS_CODE, renewCardRes.GetStatus().String()))
		res.DisplayMessage = RenewCardFailure
		return responseWithStatus(renewCardRes.GetStatus(), errors2.NewBottomSheetErrorView(internalErrorStatusCode,
			renewCardFailureErrorTitle, "", internalFailureErrorDescription, &errors.CTA{Type: errors.CTA_DONE, Text: okCTA}))
	}
}

func (s *Service) CheckRenewCardStatus(ctx context.Context, req *fePb.CheckRenewCardStatusRequest) (*fePb.CheckRenewCardStatusResponse, error) {
	var (
		res = &fePb.CheckRenewCardStatusResponse{}
	)
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*fePb.CheckRenewCardStatusResponse, error) {
		res.Status = status
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}

	renewCardStatusRes, err := s.cardProvisioningClient.RenewCardStatus(ctx, &bePb.RenewCardStatusRequest{
		CardId: req.GetCardId(),
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in renew card status", zap.String(logger.CARD_ID, req.GetCardId()), zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg("error in renew card status")
		res.NextPollTimeGap = &timestamp.Timestamp{
			Seconds: int64(shippingAddressUpdatePollingTime),
		}
		res.DisplayMessage = RenewCardGenericErrorMessage
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, req.GetCardId(), res.GetDisplayMessage(), res.GetNextPollTimeGap())
		return res, nil
	case renewCardStatusRes.Status.Code == uint32(bePb.RenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_PENDING):
		res.Status = rpc.NewStatusWithoutDebug(uint32(fePb.CheckRenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_PENDING),
			"shipping address update pending")
		res.NextPollTimeGap = &timestamp.Timestamp{
			Seconds: int64(shippingAddressUpdatePollingTime),
		}
		res.DisplayMessage = ShippingAddressUpdateMessage
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, req.GetCardId(), res.GetDisplayMessage(), res.GetNextPollTimeGap())
		if renewCardStatusRes.GetNextAction() != nil {
			res.NextAction = renewCardStatusRes.GetNextAction()
		}
		return res, nil
	case renewCardStatusRes.Status.Code == uint32(bePb.RenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_FAILED):
		logger.Info(ctx, "shipping address update failed", zap.String(logger.CARD_ID, req.GetCardId()))
		res.Status = rpc.NewStatusWithoutDebug(uint32(fePb.CheckRenewCardStatusResponse_SHIPPING_ADDRESS_UPDATE_FAILED),
			"shipping address update failed")
		res.RespHeader = &header.ResponseHeader{Status: res.GetStatus()}
		return res, nil
	case renewCardStatusRes.Status.Code == uint32(bePb.RenewCardStatusResponse_CARD_BLOCK_PENDING):
		res.Status = rpc.NewStatus(uint32(fePb.CheckRenewCardStatusResponse_CARD_BLOCK_PENDING),
			"card block pending", "")
		res.NextPollTimeGap = &timestamp.Timestamp{
			Seconds: int64(cardBlockStatusPollingTime),
		}
		res.DisplayMessage = DeactivatingCardMessage
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, req.GetCardId(), res.GetDisplayMessage(), res.GetNextPollTimeGap())
		if renewCardStatusRes.GetNextAction() != nil {
			res.NextAction = renewCardStatusRes.GetNextAction()
		}
		return res, nil
	case renewCardStatusRes.Status.Code == uint32(bePb.RenewCardStatusResponse_CARD_BLOCK_FAILED):
		logger.Info(ctx, "card block failed", zap.String(logger.CARD_ID, req.GetCardId()))
		res.Status = rpc.NewStatus(uint32(fePb.CheckRenewCardStatusResponse_CARD_BLOCK_FAILED),
			"card block failed", "")
		res.RespHeader = &header.ResponseHeader{Status: res.GetStatus()}
		return res, nil
	case renewCardStatusRes.Status.Code == uint32(bePb.RenewCardStatusResponse_CARD_CREATION_PENDING):
		logger.Debug(ctx, "card creation pending", zap.String(logger.CARD_ID, req.GetCardId()))
		res.Status = rpc.NewStatus(uint32(fePb.CheckRenewCardStatusResponse_CARD_CREATION_PENDING),
			"card creation pending", "")
		res.NextPollTimeGap = &timestamp.Timestamp{
			Seconds: int64(cardCreationStatusPollingTime),
		}
		res.DisplayMessage = RequestingNewCardMessage
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, req.GetCardId(), res.GetDisplayMessage(), res.GetNextPollTimeGap())
		if renewCardStatusRes.GetNextAction() != nil {
			res.NextAction = renewCardStatusRes.GetNextAction()
		}
		return res, nil
	case renewCardStatusRes.Status.Code == uint32(bePb.RenewCardStatusResponse_CARD_CREATION_FAILED):
		logger.Info(ctx, "card creation failed in renew card flow", zap.String(logger.CARD_ID, req.GetCardId()))
		res.Status = rpc.NewStatus(uint32(fePb.CheckRenewCardStatusResponse_CARD_CREATION_FAILED),
			"card creation failed", "")
		res.RespHeader = &header.ResponseHeader{Status: res.GetStatus()}
		// TODO(CB): refactor card errorView and add support to add action in config itself.
		if renewCardStatusRes.GetInternalStatusCode() != "" {
			var errorView *errors.ErrorView
			if renewCardStatusRes.GetInternalStatusCode() == deviceDeactivatedByBankRespCode {
				errorView = renewCardDeviceDeactivatedBYBankErrorView
			} else {
				errorView = errormapping.GetCardErrorView(renewCardStatusRes.GetInternalStatusCode())
			}
			return responseWithStatus(res.GetStatus(), errorView)
		}
		return res, nil
	case renewCardStatusRes.Status.IsSuccess(),
		renewCardStatusRes.Status.Code == uint32(bePb.RenewCardStatusResponse_PHYSICAL_DISPATCH_PENDING):
		logger.Debug(ctx, "card created successfully", zap.String("blocked-card-id", req.GetCardId()),
			zap.String("new-card-id", renewCardStatusRes.GetNewCardId()))
		res.Status = rpc.StatusOk()
		res.NewCardId = renewCardStatusRes.GetNewCardId()
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		nextAction, err := s.getRequestNewCardSuccessNextAction(ctx, res.GetNewCardId())
		if err != nil {
			logger.Error(ctx, "error getting card renewal success next action", zap.Error(err))
			res.DisplayMessage = RequestingNewCardMessage
			res.NextPollTimeGap = &timestamp.Timestamp{Seconds: int64(cardCreationStatusPollingTime)}
			nextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, res.GetNewCardId(), RequestingNewCardMessage, res.GetNextPollTimeGap())
		}
		if renewCardStatusRes.GetNextAction() != nil {
			nextAction = renewCardStatusRes.GetNextAction()
		}
		res.NextAction = nextAction
		return res, nil
	case renewCardStatusRes.Status.Code == uint32(bePb.RenewCardStatusResponse_INITIATE_PHYSICAL_DISPATCH_FAILED):
		logger.Debug(ctx, "virtual card created successfully, physical dispatch init failed", zap.String("blocked-card-id", req.GetCardId()),
			zap.String("new-card-id", renewCardStatusRes.GetNewCardId()))
		res.Status = rpc.StatusOk()
		res.NewCardId = renewCardStatusRes.GetNewCardId()
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		nextAction, err := s.getRenewCardPhysicalDispatchInitFailedNextAction(res.GetNewCardId(), "Payment unsuccessful")
		if err != nil {
			logger.Error(ctx, "error getting card renewal success next action", zap.Error(err))
			res.DisplayMessage = RequestingNewCardMessage
			res.NextPollTimeGap = &timestamp.Timestamp{Seconds: int64(cardCreationStatusPollingTime)}
			nextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, res.GetNewCardId(), RequestingNewCardMessage, res.GetNextPollTimeGap())
		}
		if renewCardStatusRes.GetNextAction() != nil {
			nextAction = renewCardStatusRes.GetNextAction()
		}
		res.NextAction = nextAction
		return res, nil
	default:
		logger.Info(ctx, "got unknown status for card renew status",
			zap.String(logger.CARD_ID, req.GetCardId()))
		res.Status = rpc.StatusInternal()
		res.NextPollTimeGap = &timestamp.Timestamp{
			Seconds: int64(cardCreationStatusPollingTime),
		}
		res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
		res.NextAction, _ = s.getCheckFlowStatusDeeplink(fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD, req.GetCardId(), res.GetDisplayMessage(), res.GetNextPollTimeGap())
		if renewCardStatusRes.GetNextAction() != nil {
			res.NextAction = renewCardStatusRes.GetNextAction()
		}
		return res, nil
	}
}

func (s *Service) getRequestNewCardSuccessNextAction(ctx context.Context, cardId string) (*deeplinkPb.Deeplink, error) {
	cardDetails, err := s.getBeCard(ctx, cardId)
	if err != nil {
		return nil, fmt.Errorf("error in fetching card details %w", err)
	}
	desc, err := s.getRequestNewCardSuccessScreenText(ctx, cardDetails)
	if err != nil {
		return nil, err
	}
	nextAction, err := s.getSuccessDeeplinkLandingScreen(ctx, cardDetails, "Request approved!", desc)
	return nextAction, err
}

func (s *Service) getRenewCardPhysicalDispatchInitFailedNextAction(cardId string, description string) (*deeplinkPb.Deeplink, error) {
	screen := deeplinkPb.Screen_DEBIT_CARD_REQUEST_NEW_CARD_SUCCESS_SCREEN
	screenOptions := &dcScreenOptionsPb.DebitCardRequestNewCardSuccessScreenOptions{
		VisualElementTitleSubtitleElement: &widgetUi.VisualElementTitleSubtitleElement{
			TitleText: &commontypes.Text{
				FontColor:    "#333333",
				DisplayValue: &commontypes.Text_PlainString{PlainString: description},
				FontStyle: &commontypes.Text_CustomFontStyle{
					CustomFontStyle: &commontypes.FontStyleInfo{
						FontFamily: "Gilroy",
						FontStyle:  "normal",
						FontSize:   "20",
					},
				},
			},
			SubtitleText: &commontypes.Text{
				FontColor:    "#333333",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "We were unable to place your order for a physical debit card. Don't worry, no money has been debited.\n\nYour new digital debit card has been created & can be used instantly via the app."},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
			},
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/cards-payment-unsuccessful-icon.png",
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  200,
							Height: 196,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
		},
		Cta: &deeplink.Cta{
			Type: deeplink.Cta_CONTINUE,
			Text: "OK",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CARD_HOME_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CardHomeScreenOptions{
					CardHomeScreenOptions: &deeplink.CardHomeScreenOptions{
						CardId: cardId,
					},
				},
			},
			DisplayTheme: deeplink.Cta_PRIMARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		},
	}
	return deeplinkV3.GetDeeplinkV3(screen, screenOptions)
}

func (s *Service) getRequestNewCardSuccessScreenText(_ context.Context, cardDetails *beCardPb.Card) (string, error) {
	switch cardDetails.GetForm() {
	case beCardPb.CardForm_DIGITAL:
		return "Your brand-new digital card is ready!\nActivate it to start making online transactions.", nil
	case beCardPb.CardForm_PHYSICAL:
		return "Your brand-new digital card will appear instantly in the app. The physical card will reach you in 3-10 days 💳", nil
	default:
		return "", fmt.Errorf("invalid card form %s", cardDetails.GetForm().String())
	}
}

func (s *Service) VerifyQRCode(ctx context.Context, req *fePb.VerifyQRCodeRequest) (*fePb.VerifyQRCodeResponse, error) {
	var (
		res = &fePb.VerifyQRCodeResponse{}
	)
	responseWithStatus := func(status *rpc.Status, errorView *errors.ErrorView) (*fePb.VerifyQRCodeResponse, error) {
		res.Status = status
		res.RespHeader = &header.ResponseHeader{
			Status:    status,
			ErrorView: errorView,
		}
		return res, nil
	}
	// validating if the card belongs to the actor
	savedCard, status, errorView := s.actorAuthorisedToAccessCard(ctx, req.GetCardId(), req.GetReq().GetAuth().GetActorId())
	if !status.IsSuccess() {
		return responseWithStatus(status, errorView)
	}
	status, errorView = downtimeCheck(ctx)
	if !status.IsSuccess() {
		return responseWithStatus(status, errorView)
	}
	qrData, vendor, err := s.parseCardActivationQRData(req.GetQrData())
	if err != nil {
		logger.Error(ctx, "error while parsing card qr code", zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()),
			zap.String(logger.CARD_ID, req.GetCardId()), zap.Error(err))
		return responseWithStatus(rpc.StatusInvalidArgument(), errormapping.DefaultErrorView)
	}

	actorResp, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: req.GetReq().GetAuth().GetActorId()})
	if err = epifigrpc.RPCError(actorResp, err); err != nil {
		logger.Error(ctx, "error in finding actor", zap.Error(err))
		return responseWithStatus(rpc.StatusInternal(), errormapping.DefaultErrorView)
	}
	beResp, err := s.cardProvisioningClient.VerifyQRCode(ctx, &bePb.VerifyQRCodeRequest{
		Actor:              actorResp.GetActor(),
		CardId:             req.GetCardId(),
		QrData:             qrData,
		CardPrintingVendor: vendor,
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while verifying qr code", zap.Error(err))
		return responseWithStatus(rpc.StatusInternal(), errormapping.DefaultErrorView)
	case beResp.GetStatus().Code == uint32(bePb.VerifyQRCodeResponse_VENDOR_API_FAILURE):
		logger.Error(ctx, "vendor api failure while verifying qr code")
		vendorFailureErrorView := errors2.NewBottomSheetErrorView("CARD0013", QRCodeVerificationFailureTitle, "",
			QRCodeVerificationVendorFailureDescription, &errors.CTA{
				Type: errors.CTA_RETRY,
				Text: "RETRY",
			})
		return responseWithStatus(rpc.NewStatusWithoutDebug(uint32(fePb.VerifyQRCodeResponse_VENDOR_API_FAILURE), "vendor api failure"),
			vendorFailureErrorView)
	case beResp.GetStatus().Code == uint32(bePb.VerifyQRCodeResponse_VERIFICATION_FAILED):
		logger.Error(ctx, "failed to verify qr code")
		lastFourDigits := savedCard.GetBasicInfo().GetMaskedCardNumber()[len(savedCard.GetBasicInfo().GetMaskedCardNumber())-4:]
		description := fmt.Sprintf("Please make sure that you are scanning the QR code you received with card ending %s", lastFourDigits)
		verificationFailureErrorView := errors2.NewBottomSheetErrorView("CARD0013", QRCodeInvalidFailureTitle, "",
			description, &errors.CTA{
				Type: errors.CTA_RETRY,
				Text: "RETRY",
			})
		return responseWithStatus(rpc.NewStatusWithoutDebug(uint32(fePb.VerifyQRCodeResponse_VERIFICATION_FAILED), "The code does not seem to be valid, please try scanning a different code."),
			verificationFailureErrorView)
	case beResp.GetStatus().Code == uint32(bePb.VerifyQRCodeResponse_CARD_ALREADY_ACTIVATED):
		return responseWithStatus(rpc.NewStatusWithoutDebug(uint32(fePb.VerifyQRCodeResponse_CARD_ALREADY_ACTIVATED), "card already activated"), errors2.NewBottomSheetErrorView("", QRCodeVerificationCardAlreadyActivatedTitle, "",
			QRCodeVerificationCardAlreadyActivatedDescription, &errors.CTA{
				Type: errors.CTA_CUSTOM,
				Text: "PROCEED",
				Action: &deeplinkPb.Deeplink{
					Screen:        deeplinkPb.Screen_CARD_USAGE_SCREEN,
					ScreenOptions: &deeplinkPb.Deeplink_CardUsageScreenOptions{CardUsageScreenOptions: &deeplinkPb.CardUsageScreenOptions{CardId: req.GetCardId()}}},
			}))
	case !beResp.GetStatus().IsSuccess():
		logger.Error(ctx, "non success state while verifying qr code")
		return responseWithStatus(rpc.StatusInternal(), errormapping.DefaultErrorView)
	default:
		isOfflineTxnEnabled, err := s.offlineTxnEnabled(ctx, req.GetCardId())
		if err != nil {
			logger.Error(ctx, "error while fetching control action for atm & pos", zap.String(logger.CARD_ID, req.GetCardId()),
				zap.Error(err))
			return responseWithStatus(rpc.StatusInternal(), errormapping.DefaultErrorView)
		}

		// Both ATM & POS are enabled
		if isOfflineTxnEnabled {
			res.DisplayData = &fePb.DisplayData{
				Title:       SuccessfulQRCodeScanWithOfflineTxnEnabledTitle,
				Description: SuccessfulQRCodeScanWithOfflineTxnEnabledDetails,
			}
		} else {
			res.DisplayData = &fePb.DisplayData{
				Title:       SuccessfulQRCodeScanWithOfflineTxnDisabledTitle,
				Description: SuccessfulQRCodeScanWithOfflineTxnDisabledDetails,
			}
		}
		res.Status = rpc.StatusOk()
		return res, nil
	}
}

func (s *Service) CardDeliveryTracking(ctx context.Context, req *fePb.CardDeliveryTrackingRequest) (*fePb.CardDeliveryTrackingResponse, error) {
	var (
		res = &fePb.CardDeliveryTrackingResponse{}
		err error
	)
	beResp, err := s.cardProvisioningClient.CardDeliveryTracking(ctx, &bePb.CardDeliveryTrackingRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
		CardId:  req.GetCardId(),
	})
	if te := epifigrpc.RPCError(beResp, err); te != nil {
		logger.Error(ctx, "error in fetching be card tracking details", zap.String(logger.CARD_ID, req.GetCardId()),
			zap.Error(te))
		res.Status = rpc.StatusFromErrorWithDefaultInternal(te)
		return res, nil
	}
	res.TrackingId = beResp.GetAwb()
	res.CourierPartner = beResp.GetCourierPartner()
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) FetchTransactionableCards(ctx context.Context, req *fePb.FetchTransactionableCardsRequest) (*fePb.FetchTransactionableCardsResponse, error) {
	var (
		res                  = &fePb.FetchTransactionableCardsResponse{}
		transactionableCards []*fePb.CardDetail
	)
	beResp, err := s.cardProvisioningClient.FetchTransactionableCards(ctx, &bePb.FetchTransactionableCardsRequest{
		ActorId: req.GetReq().GetAuth().GetActorId(),
	})
	if te := epifigrpc.RPCError(beResp, err); te != nil {
		logger.Error(ctx, "error in fetching be transactionable cards",
			zap.String(logger.ACTOR_ID, req.GetReq().GetAuth().GetActorId()), zap.Error(te))
		res.Status = rpc.StatusFromErrorWithDefaultInternal(te)
		return res, nil
	}

	for _, beCard := range beResp.GetCards() {
		feCard, err := getFeCardDetail(ctx, beCard)
		if err != nil {
			logger.Error(ctx, "error while fetching fe card",
				zap.String(logger.CARD_ID, beCard.GetId()), zap.Error(err))
			continue
		}
		transactionableCards = append(transactionableCards, feCard)
	}
	res.CardDetails = transactionableCards
	res.Status = rpc.StatusOk()
	return res, nil
}

// nolint:funlen
func getFeCardDetail(ctx context.Context, beCard *beCardPb.Card) (*fePb.CardDetail, error) {
	if beCard == nil {
		return nil, fmt.Errorf("nil card received from backend")
	}
	feCard := &fePb.CardDetail{
		Id:           beCard.Id,
		CardCategory: beCard.CardCategory,
	}
	feState, err := getFeCardState(beCard.State)
	if err != nil {
		return nil, err
	}
	feCard.State = feState
	if beCard.GetState() == beCardPb.CardState_INITIATED {
		logger.Info(ctx, "card creation pending", zap.String(logger.CARD_ID, beCard.GetId()))
		feCard.TransactionFallbackDisplay = PendingCardCreationString
		return feCard, nil
	}
	feCardType, err := getFeCardType(beCard.Type)
	if err != nil {
		return nil, err
	}
	feCard.Type = feCardType
	feCardForm, err := getFeCardForm(beCard.Form)
	if err != nil {
		return nil, err
	}
	feCard.Form = feCardForm
	feNetworkType, err := getFeNetworkType(beCard.NetworkType)
	if err != nil {
		return nil, err
	}
	feCard.NetworkType = feNetworkType
	feVendor, ok := frontend.GetFeVendor(beCard.IssuerBank)
	if !ok {
		return nil, fmt.Errorf("invalid vg Vendor from BE %v", beCard.IssuerBank.String())
	}
	feCard.IssuerBank = feVendor
	feBasicInfo, err := getFeCardInfo(beCard.BasicInfo)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("error while converting card basic info err: %v", err))
		return nil, err
	}
	feCard.BasicInfo = feBasicInfo
	feCard.Name = beCard.BasicInfo.CustomerName
	feCard.PinSetTokenExpireAt = beCard.PinSetOtpToken.ExpireAt
	switch beCard.GetState() {
	case beCardPb.CardState_SUSPENDED:
		feCard.LockedCardDisplay = "Card temporarily frozen"
	case beCardPb.CardState_BLOCKED:
		feCard.LockedCardDisplay = "Card permanently deactivated"
	case beCardPb.CardState_CREATED:
		// need to activate card if card is in created
		feCard.CardInfoFallbackDisplay = ActivateCardString
	default:
		feCard.LockedCardDisplay = ""
	}
	return feCard, nil
}

func getFeCardState(state beCardPb.CardState) (fePb.CardState, error) {
	switch state {
	case beCardPb.CardState_CREATED:
		return fePb.CardState_CREATED, nil
	case beCardPb.CardState_ACTIVATED:
		return fePb.CardState_ACTIVATED, nil
	case beCardPb.CardState_INITIATED:
		return fePb.CardState_INITIATED, nil
	case beCardPb.CardState_SUSPENDED:
		return fePb.CardState_SUSPENDED, nil
	case beCardPb.CardState_BLOCKED:
		return fePb.CardState_BLOCKED, nil
	case beCardPb.CardState_EXPIRED:
		return fePb.CardState_EXPIRED, nil
	default:
		return 0, fmt.Errorf("invalid card state from BE %v", state.String())
	}
}

func getFeCardType(cardType beCardPb.CardType) (fePb.CardType, error) {
	switch cardType {
	case beCardPb.CardType_DEBIT:
		return fePb.CardType_DEBIT, nil
	case beCardPb.CardType_CREDIT:
		return fePb.CardType_CREDIT, nil
	default:
		return 0, fmt.Errorf("invalid card type from BE %v", cardType.String())
	}
}

func getFeNetworkType(networkType beCardPb.CardNetworkType) (fePb.CardNetworkType, error) {
	switch networkType {
	case beCardPb.CardNetworkType_VISA:
		return fePb.CardNetworkType_VISA, nil
	case beCardPb.CardNetworkType_AMEX:
		return fePb.CardNetworkType_AMEX, nil
	case beCardPb.CardNetworkType_DINERS:
		return fePb.CardNetworkType_DINERS, nil
	case beCardPb.CardNetworkType_DISCOVER:
		return fePb.CardNetworkType_DISCOVER, nil
	case beCardPb.CardNetworkType_MASTER:
		return fePb.CardNetworkType_MASTER, nil
	case beCardPb.CardNetworkType_RUPAY:
		return fePb.CardNetworkType_RUPAY, nil
	default:
		return 0, fmt.Errorf("invalid card network type from BE %v", networkType.String())
	}
}

func getFeCardForm(cardForm beCardPb.CardForm) (fePb.CardForm, error) {
	switch cardForm {
	case beCardPb.CardForm_DIGITAL:
		return fePb.CardForm_DIGITAL, nil
	case beCardPb.CardForm_PHYSICAL:
		return fePb.CardForm_PHYSICAL, nil
	default:
		return 0, fmt.Errorf("invalid card form from BE %v", cardForm.String())
	}
}

func getFeCardInfo(beCardInfo *beCardPb.BasicCardInfo) (*fePb.BasicCardInfo, error) {
	cardExpiryDate, err := getCardExpiryDate(beCardInfo.Expiry)
	if err != nil {
		logger.ErrorNoCtx(fmt.Sprintf("error while converting card expiry date: %v err: %v", beCardInfo.Expiry, err))
		return nil, err
	}
	return &fePb.BasicCardInfo{
		MaskedCardNumber: beCardInfo.MaskedCardNumber,
		Expiry:           cardExpiryDate,
		Cvv:              beCardInfo.Cvv,
		Name:             beCardInfo.CustomerName,
	}, nil
}

// getCardActionsWithState sets corresponding action as active or not according to the card state.
//  1. For fetching the card details if the card is in created, suspended or blocked state we will disable the Card details action
//     and enable only for card in activated state
//  2. For enabling and disabling e-comm we will only allow user to enable or disable e-comm is card is in activated state.
//  3. We will allow freeze and unfreeze card action only if card is in activated or suspended state else we will disable the action
//  4. Card setting will be enabled for all card states
func (s *Service) getCardActionsWithState(card *beCardPb.Card, cardActions []*fePb.CardAction, cardDelivered bool) []*fePb.CardAction {
	for _, action := range cardActions {
		switch action.Type {
		case fePb.CardActionType_CVV_ENQUIRY:
			switch card.GetState() {
			case beCardPb.CardState_ACTIVATED:
				action.Active = true
			default:
				action.Active = false
			}
		case fePb.CardActionType_ENABLE_ECOMM, fePb.CardActionType_DISABLE_ECOMM:
			switch card.GetState() {
			case beCardPb.CardState_ACTIVATED:
				action.Active = true
			default:
				action.Active = false
			}
		case fePb.CardActionType_LOCK, fePb.CardActionType_UNLOCK:
			switch card.GetState() {
			case beCardPb.CardState_ACTIVATED, beCardPb.CardState_SUSPENDED:
				action.Active = true
			default:
				action.Active = false
			}
		case fePb.CardActionType_CARD_SETTINGS:
			action.Active = true
		default:
			continue
		}
	}
	return cardActions
}

func (s *Service) getAuthOptions(isBiometricEnabled bool) []*fePb.CardActionAuthOptions {
	var authOptions []*fePb.CardActionAuthOptions
	if !isBiometricEnabled {
		authOptions = append(authOptions, &fePb.CardActionAuthOptions{
			Auth:      fePb.CardActionAuth_VALIDATE_UPI_PIN,
			CanBeUsed: true,
		})
	}
	return authOptions
}

// Converts Card expiry date from MMYY to MM/YY
func getCardExpiryDate(cardExpiry string) (string, error) {
	if len(cardExpiry) != 4 {
		return "", fmt.Errorf("invalid card expiry date %v", cardExpiry)
	}
	return cardExpiry[0:2] + "/" + cardExpiry[len(cardExpiry)-2:], nil
}

// getTransactionUsageTile updates the card usage tile corresponding to the given transaction type and action
func (s *Service) getTransactionUsageTile(ctx context.Context, txnType beCardPb.CardTransactionType,
	cardAction beCardPb.CardControlAction, cardDelivered bool) (*fePb.CardUsageTile, error) {
	var cardUsageTile *fePb.CardUsageTile

	feCardActionType, applicableAuths := getNextFeCardActionAndAuthByTxn(ctx, txnType, cardAction)
	if feCardActionType == fePb.CardActionType_CARD_ACTION_UNSPECIFIED {
		return nil, fmt.Errorf("invalid card action type")
	}

	switch txnType {
	case beCardPb.CardTransactionType_ATM:
		cardUsageTile = &fePb.CardUsageTile{
			DisplayTitle:    EnableATMTitle,
			DisplaySubtitle: EnableATMMessage,
			CardAction: &fePb.CardAction{
				Type:                  feCardActionType,
				AuthOptions:           []fePb.CardActionAuth{applicableAuths[0].Auth},
				CardActionAuthOptions: applicableAuths,
				Active:                cardDelivered,
				Name:                  "",
				DisplayFormat:         fePb.DisplayFormat_TOGGLE,
			},
			CurrentState: getCurrentFeCardControlActionState(cardAction),
		}
	case beCardPb.CardTransactionType_POS:
		cardUsageTile = &fePb.CardUsageTile{
			DisplayTitle:    EnablePOSTitle,
			DisplaySubtitle: EnablePOSMessage,
			CardAction: &fePb.CardAction{
				Type:                  feCardActionType,
				AuthOptions:           []fePb.CardActionAuth{applicableAuths[0].Auth},
				CardActionAuthOptions: applicableAuths,
				Active:                cardDelivered,
				Name:                  "",
				DisplayFormat:         fePb.DisplayFormat_TOGGLE,
			},
			CurrentState: getCurrentFeCardControlActionState(cardAction),
		}
	case beCardPb.CardTransactionType_NFC:
		cardUsageTile = &fePb.CardUsageTile{
			DisplayTitle:    EnableNFCTitle,
			DisplaySubtitle: EnableNFCMessage,
			CardAction: &fePb.CardAction{
				Type:                  feCardActionType,
				AuthOptions:           []fePb.CardActionAuth{applicableAuths[0].Auth},
				CardActionAuthOptions: applicableAuths,
				Active:                cardDelivered,
				Name:                  "",
				DisplayFormat:         fePb.DisplayFormat_TOGGLE,
			},
			CurrentState: getCurrentFeCardControlActionState(cardAction),
		}
	case beCardPb.CardTransactionType_ECOMMERCE:
		cardUsageTile = &fePb.CardUsageTile{
			DisplayTitle:    EnableEcommTitle,
			DisplaySubtitle: EnableEcommMessage,
			CardAction: &fePb.CardAction{
				Type:                  feCardActionType,
				AuthOptions:           []fePb.CardActionAuth{applicableAuths[0].Auth},
				CardActionAuthOptions: applicableAuths,
				Active:                true,
				Name:                  "",
				DisplayFormat:         fePb.DisplayFormat_TOGGLE,
			},
			CurrentState: getCurrentFeCardControlActionState(cardAction),
		}
	default:
		return nil, fmt.Errorf("invalid transaction type %v", txnType.String())
	}
	return cardUsageTile, nil
}

// getLocationUsageTile updates the card usage tile corresponding to the given location type and action
func (s *Service) getLocationUsageTile(ctx context.Context, locationType beCardPb.CardUsageLocationType,
	cardAction beCardPb.CardControlAction, cardDelivered bool) (*fePb.CardUsageTile, error) {

	var cardUsageTile *fePb.CardUsageTile
	feCardActionType, applicableAuths := getNextFeCardActionAndAuthByLocation(ctx, locationType, cardAction)
	if feCardActionType == fePb.CardActionType_CARD_ACTION_UNSPECIFIED {
		return nil, fmt.Errorf("invalid card action type")
	}

	switch locationType {
	case beCardPb.CardUsageLocationType_INTERNATIONAL:
		cardUsageTile = &fePb.CardUsageTile{
			DisplayTitle:    EnableInternationalUsageTitle,
			DisplaySubtitle: "",
			CardAction: &fePb.CardAction{
				AuthOptions:           []fePb.CardActionAuth{applicableAuths[0].Auth},
				CardActionAuthOptions: applicableAuths,
				Active:                cardDelivered,
				DisplayFormat:         fePb.DisplayFormat_CHECKBOX,
				Type:                  feCardActionType,
			},
			CurrentState: getCurrentFeCardControlActionState(cardAction),
		}
	default:
		return nil, fmt.Errorf("invalid location type %v", locationType.String())
	}

	return cardUsageTile, nil
}

// getCardDeliveryInfo fetches card delivery tracking state and returns true if card is delivered
// to the user and false otherwise
func (s *Service) getCardDeliveryInfo(ctx context.Context, actorId, cardId string, beCardForm beCardPb.CardForm) (bool, bePb.CardDeliveryTrackingState, bool, error) {
	if beCardForm == beCardPb.CardForm_DIGITAL {
		return false, bePb.CardDeliveryTrackingState_CARD_DELIVERY_STATE_UNSPECIFIED, false, nil
	}
	trackingInfo, err := s.cardProvisioningClient.FetchDeliveryTrackingStatus(ctx, &bePb.FetchDeliveryTrackingStatusRequest{
		CardId: cardId,
	})
	if te := epifigrpc.RPCError(trackingInfo, err); te != nil {
		return false, bePb.CardDeliveryTrackingState_CARD_DELIVERY_STATE_UNSPECIFIED, false, fmt.Errorf("be error in fetch card delivery tracking details for card id %s actor id %s", cardId, actorId)
	}
	if trackingInfo.GetDeliveryState() == bePb.CardDeliveryTrackingState_RECEIVED_BY_USER {
		return true, trackingInfo.GetDeliveryState(), trackingInfo.GetMobileNumberUpdated(), nil
	}
	return false, trackingInfo.GetDeliveryState(), trackingInfo.GetMobileNumberUpdated(), nil
}

// actorAuthorisedToAccessCard checks if the request initiated is for the same actor to which the card belongs to.
func (s *Service) actorAuthorisedToAccessCard(ctx context.Context, cardId, authActorId string) (*beCardPb.Card, *rpc.Status, *errors.ErrorView) {
	beCard, err := s.getBeCard(ctx, cardId)
	if err != nil {
		logger.Error(ctx, "error while fetching card", zap.String(logger.CARD_ID, cardId), zap.Error(err))
		return nil, rpc.StatusInternal(), errormapping.DefaultErrorView
	}
	if beCard.ActorId != authActorId {
		logger.Error(ctx, "card does not belong to the given actor id", zap.String(logger.CARD_ID, cardId),
			zap.String("authActorId", authActorId), zap.String("cardActorId", beCard.ActorId), zap.Error(err))
		return nil, rpc.StatusPermissionDenied(), errormapping.ActorValidationFailureErrorView
	}
	return beCard, rpc.StatusOk(), nil
}

// getBeCard fetches backend card details for a given card id. Also checks if the card belongs to the
// actor for which request has been initiated, if not we will return error
func (s *Service) getBeCard(ctx context.Context, cardId string) (*beCardPb.Card, error) {
	cardDetailRes, err := s.cardProvisioningClient.FetchCardDetails(ctx, &bePb.FetchCardDetailsRequest{
		CardIds: []string{cardId},
	})
	if te := epifigrpc.RPCError(cardDetailRes, err); te != nil || cardDetailRes.GetCards() == nil {
		return nil, fmt.Errorf("be error in fetch card details for card id %s", cardId)
	}

	beCard, ok := cardDetailRes.GetCards()[cardId]
	if !ok {
		return nil, fmt.Errorf("error in getting card for card id %s", cardId)
	}

	return beCard, nil
}

// getPhysicalCardCreationTime fetches physical card creation time
func (s *Service) getPhysicalCardCreationTime(ctx context.Context, card *beCardPb.Card) (time.Time, error) {
	dispatchRequests, err := s.cardProvisioningClient.FetchPhysicalCardDispatchRequests(ctx, &bePb.FetchPhysicalCardDispatchRequestsRequest{
		CardId: card.GetId(),
	})
	switch {
	case err != nil:
		return time.Time{}, fmt.Errorf("be error in fetching card dispatch requests for card id %s", card.GetId())
	case dispatchRequests.GetStatus().IsRecordNotFound() || len(dispatchRequests.GetPhysicalCardDispatchRequests()) == 0:
		return card.GetCreatedAt().AsTime(), nil
	case !dispatchRequests.GetStatus().IsSuccess():
		return time.Time{}, fmt.Errorf("non success status while fetching dispatch requests for cardId: %s, status: %v", card.GetId(), dispatchRequests.GetStatus())
	case dispatchRequests.GetPhysicalCardDispatchRequests()[0].GetState() != cpPb.RequestState_SUCCESS:
		return time.Time{}, fmt.Errorf("unexpected error while fetching physical card order requests at fe, requestState: %s, cardForm: %s",
			dispatchRequests.GetPhysicalCardDispatchRequests()[0].GetState().String(), card.GetForm().String())
	default:
		return dispatchRequests.GetPhysicalCardDispatchRequests()[0].GetUpdatedAt().AsTime(), nil
	}
}

// offlineTxnEnabled checks if both ATM & POS are enabled or not
func (s *Service) offlineTxnEnabled(ctx context.Context, cardId string) (bool, error) {
	var (
		atmEnabled bool
		posEnabled bool
	)
	defaultDisableLimit := &moneyPb.Money{
		CurrencyCode: moneyPkg.RupeeCurrencyCode,
		Units:        1,
		Nanos:        0,
	}
	beCardLimitResponse, err := s.cardCtrlClient.FetchCardLimits(ctx, &beCcPb.FetchCardLimitsRequest{
		CardId: cardId,
	})
	if te := epifigrpc.RPCError(beCardLimitResponse, err); te != nil {
		logger.Error(ctx, "error in getting be card limit", zap.String(logger.CARD_ID, cardId), zap.Error(err))
		return false, te
	}
	cardLimitDetails := beCardLimitResponse.GetCardLimitData().GetCardLimitDetails()
	for idx := range cardLimitDetails {
		cardLimitDetail := cardLimitDetails[idx]
		if cardLimitDetail.GetTxnType() == beCardPb.CardTransactionType_ATM {
			if moneyPkg.Compare(cardLimitDetail.GetCurrentAllowedAmount(), defaultDisableLimit) == 1 {
				atmEnabled = true
			}
		}
		if cardLimitDetail.GetTxnType() == beCardPb.CardTransactionType_POS {
			if moneyPkg.Compare(cardLimitDetail.GetCurrentAllowedAmount(), defaultDisableLimit) == 1 {
				posEnabled = true
			}
		}
	}
	if atmEnabled && posEnabled {
		return true, nil
	}
	return false, nil
}

// parseCardActivationQRData checks if raw qr data is of length 64 and return early in that case, otherwise we will parse
// the updated qr url (cardactivation://card?vendor=mct&cardType=debit&digestValue=encryptedData) to fetch the vendor
// and encrypted data
func (s *Service) parseCardActivationQRData(rawQRData string) (string, bePb.CardPrintingVendor, error) {
	if len(rawQRData) == qr.CardActivationQRLength {
		return rawQRData, bePb.CardPrintingVendor_SESHAASAI, nil
	}
	parsedUrlData, err := url.Parse(rawQRData)
	if err != nil {
		return "", bePb.CardPrintingVendor_CARD_PRINTING_VENDOR_UNSPECIFIED, fmt.Errorf("failed to parse qr data %w", err)
	}
	queryMap, err := url.ParseQuery(parsedUrlData.RawQuery)
	if err != nil {
		return "", bePb.CardPrintingVendor_CARD_PRINTING_VENDOR_UNSPECIFIED, fmt.Errorf("failed to parsed qr raw query %w", err)
	}
	vendorString, ok := queryMap[vendorKey]
	if !ok {
		return "", bePb.CardPrintingVendor_CARD_PRINTING_VENDOR_UNSPECIFIED,
			fmt.Errorf("failed to parse vendor for qr data %s", queryMap[vendorKey])
	}
	vendor, ok := cardPrintingVendorMap[vendorString[0]]
	if !ok {
		return "", bePb.CardPrintingVendor_CARD_PRINTING_VENDOR_UNSPECIFIED,
			fmt.Errorf("failed to fetch vendor for vendor string %s", vendorString[0])
	}
	qrData, ok := queryMap[cardActivationEncryptedData]
	if !ok {
		return "", bePb.CardPrintingVendor_CARD_PRINTING_VENDOR_UNSPECIFIED,
			fmt.Errorf("failed to parse encrypted data for qr data %v", len(queryMap[cardActivationEncryptedData]))
	}
	// By default, all the '+' are parsed as spaces. In order to keep it in sync with the original value,
	// updating the qr data to replace all the spaces with '+'.
	updatedQrData := strings.ReplaceAll(qrData[0], " ", "+")
	return updatedQrData, vendor, nil
}

func (s *Service) ActivatePhysicalCard(ctx context.Context, req *fePb.ActivatePhysicalCardRequest) (*fePb.ActivatePhysicalCardResponse, error) {
	res := &fePb.ActivatePhysicalCardResponse{RespHeader: &header.ResponseHeader{}}
	if !s.isCardBelongsToUser(ctx, req.GetCardId(), req.GetReq().GetAuth().GetActorId()) {
		logger.Error(ctx, "card does not belong to user", zap.String(logger.CARD_ID, req.GetCardId()))
		res.RespHeader.Status = rpc.StatusPermissionDenied()
		return res, nil
	}

	beResp, err := s.cardProvisioningClient.ActivatePhysicalCard(ctx, &bePb.ActivatePhysicalCardRequest{
		CardId:    req.GetCardId(),
		RequestId: req.GetRequestId(),
		CredBlock: req.GetCredBlock(),
		ActorId:   req.GetReq().GetAuth().GetActorId(),
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error in BE ActivatePhysicalCard RPC",
			zap.Error(err),
			zap.String(logger.CARD_ID, req.GetCardId()))
		res.RespHeader.Status = rpc.StatusInternal()
		res.RespHeader.ErrorView = errormapping.DefaultErrorView
		return res, nil
	case beResp.GetStatus().IsAlreadyExists():
		logger.Info(ctx, "card was already activated",
			zap.String(logger.CARD_ID, req.GetCardId()))
		res.RespHeader.Status = rpc.StatusOk()
		res.NextAction = beResp.GetNextAction()
		return res, nil
	case !beResp.GetStatus().IsSuccess():
		logger.Error(ctx, "non success response from BE ActivatePhysicalCard RPC",
			zap.String(logger.CARD_ID, req.GetCardId()))
		res.RespHeader.Status = rpc.StatusInternal()
		res.RespHeader.ErrorView = errormapping.GetCardErrorView(beResp.GetInternalResponseCode())
		return res, nil
	default:
		logger.Info(ctx, "physical card activation successful from backend",
			zap.String(logger.CARD_ID, req.GetCardId()))
		res.RespHeader.Status = rpc.StatusOk()
		res.NextAction = beResp.GetNextAction()
		return res, nil
	}
}

// generic rpc to initiate a card related flow
func (s *Service) InitiateCardFlow(ctx context.Context, req *fePb.InitiateCardFlowRequest) (*fePb.InitiateCardFlowResponse, error) {
	var (
		res = &fePb.InitiateCardFlowResponse{}
	)
	switch req.GetCardFlowType() {
	case fePb.CardFlowType_CARD_FLOW_TYPE_CREATE_CARD:
		req.GetCreateCardRequest().Req = req.GetReq()
		createCardRes, err := s.CreateCard(ctx, req.GetCreateCardRequest())

		switch {
		case err != nil:
			logger.Error(ctx, "error initiating create card flow", zap.Error(err))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			}
			return res, nil
		case !createCardRes.GetRespHeader().GetStatus().IsSuccess():
			logger.Error(ctx, "non success status received initiating create card flow", zap.Error(err))
			res.RespHeader = createCardRes.GetRespHeader()
			return res, nil
		default:
			res.RespHeader = createCardRes.GetRespHeader()
			res.NextAction = createCardRes.GetNextAction()
			return res, nil
		}
	case fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD:
		req.GetRenewCardRequest().Req = req.GetReq()
		renewCardRes, err := s.RenewCard(ctx, req.GetRenewCardRequest())

		switch {
		case err != nil:
			logger.Error(ctx, "error initiating renew card flow", zap.Error(err))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			}
			return res, nil
		case !renewCardRes.GetRespHeader().GetStatus().IsSuccess():
			logger.Error(ctx, "non success status received initiating renew card flow", zap.Error(err))
			res.RespHeader = renewCardRes.GetRespHeader()
			return res, nil
		default:
			res.RespHeader = renewCardRes.GetRespHeader()
			res.NextAction = renewCardRes.GetNextAction()
			return res, nil
		}
	default:
		logger.Error(ctx, "invalid card flow type", zap.String("cardFlowType", req.GetCardFlowType().String()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}
}

// generic rpc to check status of a card related flow
func (s *Service) CheckFlowStatus(ctx context.Context, req *fePb.CheckFlowStatusRequest) (*fePb.CheckFlowStatusResponse, error) {
	var (
		res = &fePb.CheckFlowStatusResponse{}
	)
	res.RetryAttemptNumber = req.GetRetryAttemptNumber() + 1

	switch req.GetCardFlowType() {
	case fePb.CardFlowType_CARD_FLOW_TYPE_CREATE_CARD:
		req.GetCheckCreateCardStatusRequest().Req = req.GetReq()
		checkCreateCardStatusRes, err := s.CheckCreateCardStatus(ctx, req.GetCheckCreateCardStatusRequest())

		switch {
		case err != nil:
			logger.Error(ctx, "error in checking create card status", zap.Error(err))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			}
			return res, nil
		case !checkCreateCardStatusRes.GetRespHeader().GetStatus().IsSuccess():
			logger.Error(ctx, "non success status received while checking create card status", zap.Error(err))
			res.RespHeader = checkCreateCardStatusRes.GetRespHeader()
			return res, nil
		default:
			res.RespHeader = checkCreateCardStatusRes.GetRespHeader()
			res.NextAction = checkCreateCardStatusRes.GetNextAction()
			return res, nil
		}
	case fePb.CardFlowType_CARD_FLOW_TYPE_RENEW_CARD:
		req.GetCheckRenewCardStatusRequest().Req = req.GetReq()
		checkRenewCardStatusRes, err := s.CheckRenewCardStatus(ctx, req.GetCheckRenewCardStatusRequest())

		switch {
		case err != nil:
			logger.Error(ctx, "error in checking renew card status", zap.Error(err))
			res.RespHeader = &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			}
			return res, nil
		case !checkRenewCardStatusRes.GetRespHeader().GetStatus().IsSuccess():
			logger.Error(ctx, "non success status received while checking renew card status", zap.Error(err))
			res.RespHeader = checkRenewCardStatusRes.GetRespHeader()
			return res, nil
		default:
			res.RespHeader = checkRenewCardStatusRes.GetRespHeader()
			res.NextAction = checkRenewCardStatusRes.GetNextAction()
			return res, nil
		}
	default:
		logger.Error(ctx, "invalid card flow type", zap.String("cardFlowType", req.GetCardFlowType().String()))
		res.RespHeader = &header.ResponseHeader{
			Status: rpc.StatusInternal(),
		}
		return res, nil
	}
}

// nolint: funlen
func (s *Service) GetOnboardingIntro(ctx context.Context, req *fePb.GetOnboardingIntroRequest) (*fePb.GetOnboardingIntroResponse, error) {
	onbIntentSelectionDlResp, err := s.onboardingClient.GetIntentSelectionDeeplink(ctx, &onboardingPb.GetIntentSelectionDeeplinkRequest{
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		EntryPoint: onboardingPb.IntentSelectionEntryPoint_INTENT_SELECTION_ENTRY_POINT_DC_INTRO_SCREEN.String(),
	})
	if rpcErr := epifigrpc.RPCError(onbIntentSelectionDlResp, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching intent selection deeplink from onboarding", zap.Error(rpcErr))
		return &fePb.GetOnboardingIntroResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}

	dcOnboardingIntroScreenConfig := s.cardDynamicConf.DcOnboardingIntroScreenConfig()
	var (
		titleConf          = dcOnboardingIntroScreenConfig.Title()
		pagerContentConf   = dcOnboardingIntroScreenConfig.PagerContent()
		swipeCtaTextConf   = dcOnboardingIntroScreenConfig.SwipeCtaText()
		exploreProductConf = dcOnboardingIntroScreenConfig.ExploreProduct()
		consentsConf       = dcOnboardingIntroScreenConfig.Consents()
	)

	res := &fePb.GetOnboardingIntroResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		ScreenTitle:  commontypes.GetTextFromStringFontColourFontStyle(titleConf.Content(), titleConf.FontColor(), commontypes.FontStyle(commontypes.FontStyle_value[titleConf.FontStyle()])),
		SwipeCtaText: commontypes.GetTextFromStringFontColourFontStyle(swipeCtaTextConf.Content(), swipeCtaTextConf.FontColor(), commontypes.FontStyle(commontypes.FontStyle_value[swipeCtaTextConf.FontStyle()])),
		ExploreProduct: &typesUi.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(exploreProductConf.Texts()[0].Content, exploreProductConf.Texts()[0].FontColor, commontypes.FontStyle(commontypes.FontStyle_value[exploreProductConf.Texts()[0].FontStyle])),
			},
			LeftImgTxtPadding: exploreProductConf.LeftImgTxtPadding(),
			LeftVisualElement: commontypes.GetVisualElementImageFromUrl(exploreProductConf.LeftVisualElementImage().Url()).WithProperties(&commontypes.VisualElementProperties{
				Width:  exploreProductConf.LeftVisualElementImage().Properties().Width(),
				Height: exploreProductConf.LeftVisualElementImage().Properties().Height(),
			}).WithImageType(commontypes.ImageType_PNG),
			Deeplink: onbIntentSelectionDlResp.GetIntentSelectionDeeplink(),
		},
	}

	// populate pager content from config
	for _, pagerContent := range pagerContentConf {
		res.PagerContent = append(res.GetPagerContent(), &typesUi.IconTextComponent{
			LeftVisualElement: commontypes.GetVisualElementImageFromUrl(pagerContent.ImageUrl).WithProperties(&commontypes.VisualElementProperties{
				Width:  pagerContent.Width,
				Height: pagerContent.Height,
			}).WithImageType(commontypes.ImageType_PNG),
		})
	}

	// populate consent info from configs
	for _, consentItem := range consentsConf {
		consentProto := &fePb.Consent{
			ConsentText: &typesUi.TextWithHyperlinks{
				Text: commontypes.GetTextFromStringFontColourFontStyle(consentItem.ConsentInfo.Text, consentItem.ConsentInfo.TextColor, commontypes.FontStyle(consentItem.ConsentInfo.TextFrontStyle)),
				HyperlinkMap: map[string]*typesUi.HyperLink{
					consentItem.ConsentInfo.HyperLinks[0].Key: {
						Link: &typesUi.HyperLink_Url{
							Url: consentItem.ConsentInfo.HyperLinks[0].Link,
						},
					},
				},
			},
			ConsentId: consentItem.ConsentIds,
		}
		if consentItem.ConsentCheckBox != nil {
			consentProto.ConsentBox = &fePb.ConsentBox{
				IsChecked: consentItem.ConsentCheckBox.IsChecked,
			}
		}
		res.Consent = append(res.GetConsent(), consentProto)
	}

	return res, nil
}

func (s *Service) GetCardBenefitsDetailsScreen(ctx context.Context, req *fePb.GetCardBenefitsDetailsScreenRequest) (*fePb.GetCardBenefitsDetailsScreenResponse, error) {
	var (
		cardBenefitsConf = s.cardDynamicConf.PhysicalCardBenefitsBottomSheetConfig()
		res              = &fePb.GetCardBenefitsDetailsScreenResponse{}
	)

	beOffersResp, err := s.offersListingClient.GetCardOffers(ctx, &beCasperPb.GetCardOffersRequest{
		RedemptionMode: beCasperPb.OfferRedemptionMode_FI_CARD,
		FiltersV2: &beCasperPb.CatalogFiltersV2{
			OrTags:  nil,
			AndTags: nil,
		},
	})
	if rpcErr := epifigrpc.RPCError(beOffersResp, err); rpcErr != nil {
		// making this non-blocking,
		// will log the error and continue with loading the screen with available info
		logger.Error(ctx, "error fetching offers from listing service", zap.Error(rpcErr))
	}

	benefitsTypeToFeBenefitDetailsMap := make(map[string]*fePb.CardBenefitDetails)
	cardBenefitsConf.BenefitTypeToConfigMap().Iterate(func(key string, value *dynConf.PhysicalCardBenefitItem) (stop bool) {
		switch key {
		case string(pkgCard.PhysicalCardBenefitTypeCardOffers):
			benefitsTypeToFeBenefitDetailsMap[string(pkgCard.PhysicalCardBenefitTypeCardOffers)] = s.getFeCardBenefitsOffersSection(ctx, beOffersResp.GetOffers())
		case string(pkgCard.PhysicalCardBenefitTypeFeeDetails):
			cardFeeSection, sectionErr := s.getFeCardBenefitsFeeDetailsSection(ctx, req.GetCardIssuanceFee(), req.GetWelcomeOfferId())
			if sectionErr != nil {
				logger.Error(ctx, "error while building card fee section for physical card benefits screen", zap.Error(sectionErr))
			} else {
				benefitsTypeToFeBenefitDetailsMap[string(pkgCard.PhysicalCardBenefitTypeFeeDetails)] = cardFeeSection
			}
		case string(pkgCard.PhysicalCardBenefitTypeCashBack):

			bottomSheetImageUrl := value.FullPageImage().Url()
			if featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled(ctx, &featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest{
				ActorId: req.GetReq().GetAuth().GetActorId(),
				ExternalDeps: &common.ExternalDependencies{
					Evaluator: s.releaseEvaluator,
				},
			}) {
				bottomSheetImageUrl = value.FullPageImage().FcFpPostConversionUrl()
			}
			benefitsTypeToFeBenefitDetailsMap[key] = &fePb.CardBenefitDetails{
				BenefitTypeId: key,
				Details: &fePb.CardBenefitDetails_Details{
					Details: &fePb.CardBenefitDetails_Details_Image{
						Image: commontypes.GetVisualElementImageFromUrl(bottomSheetImageUrl).
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  value.FullPageImage().Properties().Width(),
							Height: value.FullPageImage().Properties().Height(),
						}),
					},
				},
			}
		default:
			benefitsTypeToFeBenefitDetailsMap[key] = &fePb.CardBenefitDetails{
				BenefitTypeId: key,
				Details: &fePb.CardBenefitDetails_Details{
					Details: &fePb.CardBenefitDetails_Details_Image{
						Image: commontypes.GetVisualElementImageFromUrl(value.FullPageImage().Url()).
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  value.FullPageImage().Properties().Width(),
							Height: value.FullPageImage().Properties().Height(),
						}),
					},
				},
			}
		}
		return false
	})

	selectedBenefitDetails, ok := benefitsTypeToFeBenefitDetailsMap[req.GetSelectedCardBenefitTypeId()]
	if !ok {
		logger.Error(ctx, fmt.Sprintf("failed to get details for selected physical card benefit type %s", req.GetSelectedCardBenefitTypeId()))
	}
	if selectedBenefitDetails != nil {
		res.CardBenefitDetails = append(res.GetCardBenefitDetails(), selectedBenefitDetails)
	}

	for _, benefitType := range physicalCardBenefitsSortingOrder {
		if string(benefitType) == req.GetSelectedCardBenefitTypeId() {
			continue
		}
		benefitDetails, found := benefitsTypeToFeBenefitDetailsMap[string(benefitType)]
		if !found {
			logger.Error(ctx, fmt.Sprintf("failed to get details for physical card benefit type %s", string(benefitType)))
			continue
		}
		res.CardBenefitDetails = append(res.GetCardBenefitDetails(), benefitDetails)
	}

	res.RespHeader = &header.ResponseHeader{Status: rpc.StatusOk()}
	return res, nil
}

func (s *Service) getFeCardBenefitsOffersSection(ctx context.Context, offers []*beCasperPb.Offer) *fePb.CardBenefitDetails {
	if len(offers) > 0 {
		offerPageConf, ok := s.cardDynamicConf.PhysicalCardBenefitsBottomSheetConfig().BenefitTypeToConfigMap().Load(string(pkgCard.PhysicalCardBenefitTypeCardOffers))
		if !ok {
			logger.Error(ctx, "offers benefits page conf not found")
			return nil
		}

		// offers should be ordered by display rank (in ascending order), then creation date (in descending order)
		sort.Slice(offers, func(i, j int) bool {
			offerIDisplayRank, offerJDisplayRank := offers[i].GetAdditionalDetails().GetOfferDisplayRank(), offers[j].GetAdditionalDetails().GetOfferDisplayRank()
			// order by display rank in ascending order
			if offerIDisplayRank != offerJDisplayRank {
				return offerIDisplayRank < offerJDisplayRank
			}
			// for equal display rank order by creation date in descending order
			return offers[i].GetCreatedAt().AsTime().After(offers[j].GetCreatedAt().AsTime())
		})

		feCardOffersList := make([]*fePb.CardBenefitDetails_CardOffersSection_CardOffer, 0)
		for _, offer := range offers[0:min(len(offers), 6)] {
			backgroundImageUrl := ""
			brandIconUrl := ""
			for _, image := range offer.GetImages() {
				if image.GetImageType() == beCasperPb.ImageType_BACKGROUND_IMAGE {
					backgroundImageUrl = image.GetUrl()
				}
				if image.GetImageType() == beCasperPb.ImageType_BRAND_IMAGE {
					brandIconUrl = image.GetUrl()
				}
			}

			feCardOffersList = append(feCardOffersList, &fePb.CardBenefitDetails_CardOffersSection_CardOffer{
				Title:       commontypes.GetTextFromStringFontColourFontStyle(offer.GetAdditionalDetails().GetBrandName(), colorPkg.ColorSlate, commontypes.FontStyle_SUBTITLE_S),
				Description: commontypes.GetTextFromStringFontColourFontStyle(offer.GetName(), colorPkg.ColorInk, commontypes.FontStyle_SUBTITLE_M),
				Icon: commontypes.GetVisualElementImageFromUrl(brandIconUrl).WithImageType(commontypes.ImageType_PNG).
					WithProperties(&commontypes.VisualElementProperties{
						Width:  40,
						Height: 40,
					}),
				BgImage: commontypes.GetVisualElementImageFromUrl(backgroundImageUrl).WithImageType(commontypes.ImageType_PNG),
				BgColor: colorPkg.ColorMonochromeChalk,
			})
		}

		return &fePb.CardBenefitDetails{
			BenefitTypeId: string(pkgCard.PhysicalCardBenefitTypeCardOffers),
			Details: &fePb.CardBenefitDetails_Details{
				Details: &fePb.CardBenefitDetails_Details_CardOffersSection{
					CardOffersSection: &fePb.CardBenefitDetails_CardOffersSection{
						TopVisualElement: commontypes.GetVisualElementImageFromUrl(offerPageConf.TopPageImage().Url()).
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  offerPageConf.TopPageImage().Properties().Width(),
							Height: offerPageConf.TopPageImage().Properties().Height(),
						}),
						Title: commontypes.GetTextFromStringFontColourFontStyle(offerPageConf.PageTitle().Content(),
							offerPageConf.PageTitle().FontColor(), commontypes.FontStyle(commontypes.FontStyle_value[offerPageConf.PageTitle().FontStyle()])),
						CardOffersList: feCardOffersList,
					},
				},
			},
		}
	}
	return nil
}

func (s *Service) getFeCardBenefitsFeeDetailsSection(ctx context.Context, amountWithoutGst *typesv2.Money, welcomeOfferId string) (*fePb.CardBenefitDetails, error) {
	gstAmountFloat := (float64(amountWithoutGst.GetBeMoney().GetUnits()) + float64(amountWithoutGst.GetBeMoney().GetNanos())/1e9) * gstMultiplier
	totalAmount, err := moneyPkg.Sum(amountWithoutGst.GetBeMoney(), moneyPkg.ParseFloat(gstAmountFloat, moneyPkg.RupeeCurrencyCode))
	if err != nil {
		return nil, errorsPkg.Wrap(err, "error while calculating total card issuance fee")
	}
	cardFeeSection := &fePb.CardBenefitDetails{
		BenefitTypeId: string(pkgCard.PhysicalCardBenefitTypeFeeDetails),
		Details: &fePb.CardBenefitDetails_Details{
			Details: &fePb.CardBenefitDetails_Details_CardFeeSection{
				CardFeeSection: &fePb.CardBenefitDetails_CardFeeSection{
					CardFeesTitle: commontypes.GetTextFromStringFontColourFontStyle(orderCardV2CardFeeSectionTitle, colorPkg.ColorOnLightHighEmphasis, commontypes.FontStyle_DISPLAY_2XL),
					FeesBreakdownTable: &fePb.CardBenefitDetails_CardFeeSection_FeesBreakdownTable{
						Rows: []*fePb.CardBenefitDetails_CardFeeSection_FeesBreakdownTable_FeesBreakdownRow{
							{
								Title:   commontypes.GetTextFromStringFontColourFontStyle(cardFeeBreakDownHeaderTitle, oceanColor, commontypes.FontStyle_OVERLINE_XS_CAPS),
								Value:   commontypes.GetTextFromStringFontColourFontStyle(cardFeeBreakDownHeaderValue, oceanColor, commontypes.FontStyle_OVERLINE_XS_CAPS),
								BgColor: softPastelBlue,
							},
							{
								Title: commontypes.GetTextFromStringFontColourFontStyle(feeBreakDownIssuanceFeeTitle, colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
								Value: commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(feeBreakDownIssuanceFeeValueFormat,
									stringPkg.AddStrikeThrough(moneyPkg.ToDisplayStringInIndianFormat(moneyPkg.ParseInt(399, moneyPkg.RupeeCurrencyCode), 0, true)),
									moneyPkg.ToDisplayStringInIndianFormat(amountWithoutGst.GetBeMoney(), 0, true)), colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
							},
							{
								Title: commontypes.GetTextFromStringFontColourFontStyle(feeBreakDownGstChargeTitle, colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
								Value: commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(feeBreakDownGstChargeValueFormat, gstAmountFloat), colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_BODY_S),
							},
							{
								Title: commontypes.GetTextFromStringFontColourFontStyle(feeBreakDownTotalFeeTitle, colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_BUTTON_M),
								Value: commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(feeBreakDownTotalFeeValueFormat, moneyPkg.ToDisplayString(totalAmount)), colorPkg.ColorOnDarkLowEmphasis, commontypes.FontStyle_BUTTON_M),
							},
						},
					},
				},
			},
		},
	}

	welcomeOfferDetails, found := s.cardDynamicConf.PhysicalCardBenefitsBottomSheetConfig().WelcomeOfferIdToDetailsMap().Load(welcomeOfferId)
	if welcomeOfferId != "" && !found {
		logger.Error(ctx, "no physical debit card welcome offer details found on config", zap.String(logger.OFFER_ID, welcomeOfferId))
	}
	if welcomeOfferDetails != nil {
		cardFeeSection.Details.GetCardFeeSection().WelcomeOfferTitle = commontypes.GetTextFromStringFontColourFontStyle(welcomeOfferDetails.Title().Content(), welcomeOfferDetails.Title().FontColor(), commontypes.FontStyle(commontypes.FontStyle_value[welcomeOfferDetails.Title().FontStyle()]))
		cardFeeSection.Details.GetCardFeeSection().BottomNote = commontypes.GetTextFromStringFontColourFontStyle(orderCardV2CardFeeBenefitsBottomTextNote, colorPkg.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_XS)
		cardFeeSection.Details.GetCardFeeSection().WelcomeOfferDetails = make([]*commontypes.Text, 0)
		for _, offerDetails := range welcomeOfferDetails.Details() {
			cardFeeSection.Details.GetCardFeeSection().WelcomeOfferDetails = append(cardFeeSection.Details.GetCardFeeSection().WelcomeOfferDetails, commontypes.GetTextFromStringFontColourFontStyle(offerDetails.Content, offerDetails.FontColor, commontypes.FontStyle(commontypes.FontStyle_value[offerDetails.FontStyle])))
		}
	}

	return cardFeeSection, nil
}

func (s *Service) GetDCInternationalWidget(ctx context.Context, req *fePb.GetDCInternationalWidgetRequest) (*fePb.GetDCInternationalWidgetResponse, error) {
	beModel, err := s.getInternationalDcWidget(ctx, req.GetReq().GetAuth().GetActorId(), req.GetCountryCode())
	if err != nil {
		logger.Error(ctx, "Error in fetching DC International card", zap.Error(err))
		return &fePb.GetDCInternationalWidgetResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInternal()},
		}, nil
	}
	if beModel.isWidgetToBeDisplayed == false {
		// BE Model has disabled the card display, hence return record not found
		return &fePb.GetDCInternationalWidgetResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusRecordNotFound()},
		}, nil
	}

	return &fePb.GetDCInternationalWidgetResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		Section:    getDCInternationalWidgetCard(beModel),
	}, nil
}

// rpc for client to fetch all countries in the DC International Widget country selection bottomsheet
func (s *Service) GetDCCountrySelectionList(ctx context.Context, req *fePb.GetDCInternationalCountrySelectionRequest) (*fePb.GetDCInternationalCountrySelectionResponse, error) {
	countriesDetail := make([]*feCard.InternationalLimitDetail, 0)
	for key, value := range webFeTravel.CountryInfoMap {
		countriesDetail = append(countriesDetail, &feCard.InternationalLimitDetail{
			Flag: &commontypes.Text{
				FontColor:    "#646464",
				DisplayValue: &commontypes.Text_PlainString{PlainString: value.Flag},
			},
			CountryName: &commontypes.Text{
				FontColor:    "#646464",
				DisplayValue: &commontypes.Text_PlainString{PlainString: key},
			},
			CountryCode: key,
		})
	}
	return &fePb.GetDCInternationalCountrySelectionResponse{
		RespHeader:               &header.ResponseHeader{Status: rpc.StatusOk()},
		Countries:                countriesDetail,
		DefaultSelectedCountryId: s.cardDynamicConf.InternationalDcWidgetConfig().DefaultCountry(),
	}, nil
}
