// nolint: funlen,dupl,unparam
package dashboard_sections

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/pkg/feature/release"

	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	types "github.com/epifi/gamma/api/typesv2"

	"context"
	"fmt"
	"strconv"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"

	"github.com/samber/lo"
	"google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	pkggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"

	cardPb "github.com/epifi/gamma/api/card"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	feCardPb "github.com/epifi/gamma/api/frontend/card"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	dcScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/debitcard"
	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/config"
	"github.com/epifi/gamma/frontend/config/genconf"
	cardFePkg "github.com/epifi/gamma/frontend/pkg/debitcard"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

var (
	dispatchInProgressStates = []cpPb.RequestState{cpPb.RequestState_INITIATED, cpPb.RequestState_QUEUED, cpPb.RequestState_MANUAL_INTERVENTION}
)

type PhysicalCardOrderSection struct {
	conf               *genconf.Card
	cpClient           cpPb.CardProvisioningClient
	segmentationClient segment.SegmentationServiceClient
	releaseEvaluator   release.IEvaluator
}

func NewPhysicalCardOrderSection(conf *genconf.Card, cpClient cpPb.CardProvisioningClient, segmentationClient segment.SegmentationServiceClient, releaseEvaluator release.IEvaluator) *PhysicalCardOrderSection {
	return &PhysicalCardOrderSection{
		conf:               conf,
		cpClient:           cpClient,
		segmentationClient: segmentationClient,
		releaseEvaluator:   releaseEvaluator,
	}
}

func (p *PhysicalCardOrderSection) BuildSection(ctx context.Context, req *SectionBuilderRequest) (*SectionBuilderResponse, error) {
	// fetch benefits info from conf
	benefitItemsList, ok := p.conf.DashboardV2Config().SectionsConfig().PhysicalCardOrderSectionConfig().UserTierToBenefitsMap[req.CurrentTier.String()]
	if !ok {
		return nil, fmt.Errorf("failed to build physical card order section: no benefits config found for tier: %s", req.CurrentTier.String())
	}

	sectionsList := make([]*components.Component, 0)
	switch {
	case req.CurrentCard.GetState() == cardPb.CardState_CREATED ||
		(req.CurrentCard.GetForm() == cardPb.CardForm_PHYSICAL &&
			req.PhysicalCardDeliveryState == cpPb.CardDeliveryTrackingState_RECEIVED_BY_USER):
		// order card widgets should not be visible to user in case digital card is not activated or physical is already activated
		return nil, nil

	case req.CurrentCard.GetState() == cardPb.CardState_BLOCKED:
		topComponent := p.buildBlockStateTopComponent()
		middleComponent, err := p.buildBlockStateMiddleSection(ctx, benefitItemsList[0:min(len(benefitItemsList), 3)], req)
		if err != nil {
			return nil, fmt.Errorf("error while building middle section of the widget: %w", err)
		}
		bottomComponent := p.buildBlockStateBottomSection(ctx, req)
		sectionsList = append(sectionsList, topComponent, middleComponent, bottomComponent)

	case req.CurrentCard.GetForm() == cardPb.CardForm_DIGITAL:
		// if vkyc attempt was rejected hide physical card order flow entryPoints
		if req.VKYCSummary.GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED {
			return nil, nil
		}

		topComponent := p.buildDigitalStateTopComponent()
		middleComponent, err := p.buildDigitalStateMiddleSection(ctx, benefitItemsList[0:min(len(benefitItemsList), 3)], req)
		if err != nil {
			return nil, fmt.Errorf("error while building middle section of the widget: %w", err)
		}

		var bottomComponentBenefitConfig *config.DcPhysicalCardBenefitInfoItem
		if len(benefitItemsList) > 3 {
			bottomComponentBenefitConfig = benefitItemsList[3]
		}
		bottomComponent := p.buildDigitalStateBottomSection(ctx, bottomComponentBenefitConfig, req)
		sectionsList = append(sectionsList, topComponent, middleComponent, bottomComponent)

	case req.CurrentCard.GetForm() == cardPb.CardForm_PHYSICAL &&
		(req.TrackingDetails.GetDeliveryState() == cpPb.CardTrackingDeliveryState_DELIVERED ||
			time.Now().Sub(req.LatestPhysicalDispatchRequest.GetCreatedAt().AsTime()) > p.conf.CardDynamicTileDuration().ViewQRCodeScanTime()):
		topComponent := p.buildActivateCardTopComponent()
		middleComponent, err := p.buildActivateCardMiddleSection()
		if err != nil {
			return nil, fmt.Errorf("error while building middle section of the widget: %w", err)
		}

		bottomComponent := p.buildActivateCardBottomSection(ctx, req)
		sectionsList = append(sectionsList, topComponent, getComponentFromHorizontalSection(middleComponent), bottomComponent)

	default:
		return nil, nil
	}

	return &SectionBuilderResponse{
		Section: &feCardPb.Section{
			CardHomeSectionType: types.CardHomeSectionType_CARD_HOME_SECTION_TYPE_PHYSICAL_CARD_ORDER_WIDGETS,
			Type: &feCardPb.Section_GenericServerDrivenSection{
				GenericServerDrivenSection: &feCardPb.GenericServerDrivenSection{
					Sections: &sections.Section{
						Content: &sections.Section_VerticalListSection{
							VerticalListSection: &sections.VerticalListSection{
								Components: []*components.Component{
									{
										// Card component
										Content: getAnyWithoutError(&sections.VerticalListSection{
											VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_SPACE_BETWEEN,
											Components:          sectionsList,
											VisualProperties: []*properties.VisualProperty{
												{
													Properties: &properties.VisualProperty_ContainerProperty{
														ContainerProperty: &properties.ContainerProperty{
															Size: &properties.Size{
																Width: &properties.Size_Dimension{
																	Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
																},
																Height: &properties.Size_Dimension{
																	Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
																	ExactValue: 254,
																},
															},
															BgColor: widget.GetBlockBackgroundColour(colorSnow),
															Corner: &properties.CornerProperty{
																TopLeftCornerRadius:  20,
																TopRightCornerRadius: 20,
																BottomLeftCorner:     20,
																BottomRightCorner:    20,
															},
															Border: &properties.BorderProperty{
																BorderThickness: 1,
																BorderColor:     "#E4F1F5",
																CornerRadius:    20,
															},
															Padding: &properties.PaddingProperty{
																Top: 20,
															},
														},
													},
												},
											},
										}),
									},
								},
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: &properties.ContainerProperty{
												Size: &properties.Size{
													Width: &properties.Size_Dimension{
														Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
													},
													Height: &properties.Size_Dimension{
														Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
														ExactValue: 286,
													},
												},
												Padding: &properties.PaddingProperty{
													Left:   24,
													Top:    24,
													Right:  24,
													Bottom: 8,
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}, nil
}

func (p *PhysicalCardOrderSection) buildBlockStateTopComponent() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Order a new Debit Card", colorNeutralsNight, commontypes.FontStyle_DISPLAY_L)),
	}
}

func (p *PhysicalCardOrderSection) buildActivateCardTopComponent() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Activate your physical card", colorNeutralsNight, commontypes.FontStyle_DISPLAY_L)),
	}
}

func (p *PhysicalCardOrderSection) buildDigitalStateTopComponent() *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Unlock a world of benefits!", colorNeutralsNight, commontypes.FontStyle_DISPLAY_L)),
	}
}

func (p *PhysicalCardOrderSection) buildBlockStateMiddleSection(ctx context.Context, benefitsConf []*config.DcPhysicalCardBenefitInfoItem, req *SectionBuilderRequest) (*components.Component, error) {
	return &components.Component{
		Content: getAnyWithoutError(p.buildBenefitWidgets(ctx, benefitsConf, req.LayoutId, req.HadPhysicalCardInPast, req.ActorId)),
	}, nil
}

func (p *PhysicalCardOrderSection) buildDigitalStateMiddleSection(ctx context.Context, benefitsConf []*config.DcPhysicalCardBenefitInfoItem, req *SectionBuilderRequest) (*components.Component, error) {
	return &components.Component{
		Content: getAnyWithoutError(p.buildBenefitWidgets(ctx, benefitsConf, req.LayoutId, req.HadPhysicalCardInPast, req.ActorId)),
	}, nil
}

func (p *PhysicalCardOrderSection) buildActivateCardMiddleSection() (*sections.HorizontalListSection, error) {
	return &sections.HorizontalListSection{
		Components: []*components.Component{
			{
				Content: getAnyWithoutError(commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/QrCodeGreen_4x.png").WithProperties(&commontypes.VisualElementProperties{Width: 62, Height: 62}).WithImageType(commontypes.ImageType_PNG)),
			},
			{
				Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyleFontAlignment("Scan the QR code in your\nDebit Card kit to activate\nyour Physical Card", "#6A6D70", commontypes.FontStyle_HEADLINE_M, commontypes.Text_ALIGNMENT_LEFT)),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
							},
						},
						Padding: &properties.PaddingProperty{
							Left:   20,
							Right:  20,
							Top:    20,
							Bottom: 20,
						},
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  19,
							TopRightCornerRadius: 19,
							BottomLeftCorner:     19,
							BottomRightCorner:    19,
						},
						Margin: &properties.PaddingProperty{
							Left:   24,
							Top:    4,
							Right:  24,
							Bottom: 4,
						},
						BgColor: widget.GetBlockBackgroundColour("#EDF5EB"),
					},
				},
			},
		},
		VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
	}, nil
}

func (p *PhysicalCardOrderSection) buildBenefitWidgets(ctx context.Context, benefitsConf []*config.DcPhysicalCardBenefitInfoItem, layoutId string, hadPhysicalCardInPast bool, actorId string) *sections.HorizontalListSection {
	widgetsList := make([]*components.Component, 0)

	for index, benefitConf := range benefitsConf {
		widgetsComponents := make([]*components.Component, 0)

		fcFpPostMigrationPhaseEnabled := featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled(ctx, &featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &common.ExternalDependencies{
				Evaluator: p.releaseEvaluator,
			},
		})

		// over-writing the iconUrl with FcFpPostConversionIconUrl, if FcFpPostConversionIconUrl is provided And fcFpPostMigrationPhaseEnabled flag is enabled
		iconUrl := lo.Ternary(fcFpPostMigrationPhaseEnabled, benefitConf.FcFpPostConversionIconUrl, benefitConf.IconUrl)
		BottomSheetImageUrl := lo.Ternary(fcFpPostMigrationPhaseEnabled, benefitConf.Deeplink.Image.FcFpPostConversionUrl, benefitConf.Deeplink.Image.Url)

		widgetsComponents = append(widgetsComponents,
			// append widget image
			&components.Component{Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, 40, 40))},
			// append title component
			&components.Component{Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(benefitConf.Desc, "#313234", commontypes.FontStyle_SUBTITLE_S))},
			// append the arrow
			&components.Component{
				Content: getAnyWithoutError(&sections.HorizontalListSection{
					HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_END,
					VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
					Components: []*components.Component{
						{
							Content: getAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(rightChevronIconGreen, 12, 12)),
						},
					},
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Size: &properties.Size{
										Width: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
										},
										Height: &properties.Size_Dimension{
											Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
										},
									},
								},
							},
						},
					},
				}),
			},
		)

		// add widget interactional behaviour
		var redirectionDl *deeplinkPb.Deeplink
		if benefitConf.Deeplink != nil {
			redirectionDl = deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen(deeplinkPb.Screen_value[benefitConf.Deeplink.ScreenName]),
				&pkgScreenOptionsPb.SduiBottomSheetOptions{
					Section: &sections.Section{
						Content: &sections.Section_VerticalListSection{
							VerticalListSection: &sections.VerticalListSection{
								HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: &properties.ContainerProperty{
												Size: &properties.Size{
													Width: &properties.Size_Dimension{
														Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
													},
												},
											},
										},
									},
								},
								Components: []*components.Component{
									{
										Content: getAnyWithoutError(commontypes.GetVisualElementImageFromUrl(BottomSheetImageUrl).
											WithProperties(&commontypes.VisualElementProperties{Width: benefitConf.Deeplink.Image.Properties.Width, Height: benefitConf.Deeplink.Image.Properties.Height})),
									},
								},
							},
						},
					},
				})
		}

		// append widget into list
		widgetsList = append(widgetsList, &components.Component{
			Content: getAnyWithoutError(&sections.VerticalListSection{
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_SPACE_BETWEEN,
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
				Components:          widgetsComponents,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: &properties.ContainerProperty{
								Size: &properties.Size{
									Width: &properties.Size_Dimension{
										Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
										ExactValue: 100,
									},
									Height: &properties.Size_Dimension{
										Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
										ExactValue: 114,
									},
								},
								Corner: &properties.CornerProperty{
									TopLeftCornerRadius:  12,
									TopRightCornerRadius: 12,
									BottomLeftCorner:     12,
									BottomRightCorner:    12,
								},
								Border: &properties.BorderProperty{
									BorderThickness: 1,
									BorderColor:     "#E2EAEF",
									CornerRadius:    12,
								},
								Padding: &properties.PaddingProperty{
									Left:   8,
									Top:    8,
									Right:  8,
									Bottom: 8,
								},
								Margin: &properties.PaddingProperty{
									Left:  4,
									Right: 4,
								},
							},
						},
					},
				},
				InteractionBehaviors: []*behaviors.InteractionBehavior{
					{
						Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
							OnClickBehavior: &behaviors.OnClickBehavior{
								Action: getAnyWithoutError(redirectionDl),
							},
						},
						AnalyticsEvent: &analytics.AnalyticsEvent{
							EventName: "DCClickOnDCDashboardV2Screen",
							Properties: map[string]string{
								"section":                   "OrderPhysicalCardWidgetBenefits" + strconv.Itoa(index),
								"redirection_screen_name":   redirectionDl.GetScreen().String(),
								"title":                     benefitConf.Desc,
								"layout_id":                 layoutId,
								"had_physical_card_in_past": strconv.FormatBool(hadPhysicalCardInPast),
							}},
					},
				},
			}),
		})
	}

	return &sections.HorizontalListSection{
		Components:            widgetsList,
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size: &properties.Size{
							Width: &properties.Size_Dimension{
								Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
							},
							Height: &properties.Size_Dimension{
								Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
								ExactValue: 114,
							},
						},
					},
				},
			},
		},
	}
}

func (p *PhysicalCardOrderSection) buildBlockStateBottomSection(_ context.Context, req *SectionBuilderRequest) *components.Component {
	return &components.Component{
		Content: getAnyWithoutError(&sections.HorizontalListSection{
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
			Components: []*components.Component{
				{
					// Primary button
					Content: p.buildCardPrimaryButton(
						"Order now",
						colorSnow,
						colorForestGreen,
						req.RenewCardSelectionTypeScreenDl,
						false,
						req.LayoutId,
						req.HadPhysicalCardInPast,
					),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Size: &properties.Size{
								Width: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
								},
								Height: &properties.Size_Dimension{
									Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
									ExactValue: 60,
								},
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: colorSnow,
								},
							},
							Corner: &properties.CornerProperty{
								TopLeftCornerRadius:  0,
								TopRightCornerRadius: 0,
								BottomLeftCorner:     20,
								BottomRightCorner:    20,
							},
							Padding: &properties.PaddingProperty{
								Left:  16,
								Right: 16,
							},
						},
					},
				},
			},
		}),
	}
}

func (p *PhysicalCardOrderSection) buildDigitalStateBottomSection(ctx context.Context, benefitsConf *config.DcPhysicalCardBenefitInfoItem, req *SectionBuilderRequest) *components.Component {
	var (
		bottomSectionComponentsList   = make([]*components.Component, 0)
		toShowWithFixedPrimaryBtnSize = false
		primaryBtnText                = "Order a physical card"
		primaryBtnTextColor           = colorSnow
		primaryBtnBgColor             = colorForestGreen
		nextAction                    *deeplinkPb.Deeplink
		benefitsText                  = ""
		benefitsTextColor             = "#0D3641"
		benefitsIcon                  = ""
		containerBgColor              = "#B2E4F1F5"
	)

	// initialize benefits text
	if benefitsConf != nil {
		benefitsText = benefitsConf.Desc
		benefitsIcon = benefitsConf.IconUrl
	}

	switch {
	case req.LatestPhysicalDispatchRequest == nil, req.LatestPhysicalDispatchRequest.GetState() == cpPb.RequestState_FAILED:
		if req.LatestPhysicalDispatchRequest.GetState() == cpPb.RequestState_FAILED &&
			time.Now().Before(req.LatestPhysicalDispatchRequest.GetUpdatedAt().AsTime().Add(toShowPhysicalDispatchFailureStatusTTL)) {
			benefitsText = "Card order unsuccessful.\nPlease try again."
			benefitsTextColor = "#AA301F"
			containerBgColor = "#F8E5EB"
		}
		nextAction = deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_DEBIT_CARD_RPC_BASED_REDIRECTION, &dcScreenOptionsPb.DebitCardRpcBasedRedirectionScreenOption{
			RpcParams: &dcScreenOptionsPb.DebitCardRpcBasedRedirectionScreenOption_FetchPhysicalCardChargesForUserRequest{
				FetchPhysicalCardChargesForUserRequest: &feCardPb.FetchPhysicalCardChargesForUserRequest{},
			},
		})

	case lo.Contains(dispatchInProgressStates, req.LatestPhysicalDispatchRequest.GetState()):
		benefitsText = "Your request is being processed.\nThis may take up to 30 mins."
		benefitsTextColor = "#C0723D"
		benefitsIcon = "https://epifi-icons.pointz.in/card-images/clock-icon-4x.png"

		if req.LatestPhysicalDispatchRequest.GetCurrentStage() == cpPb.DCRequestStage_DC_REQUEST_STAGE_CHECK_PAYMENT_STATUS ||
			req.LatestPhysicalDispatchRequest.GetCurrentStage() == cpPb.DCRequestStage_DC_REQUEST_STAGE_UNSPECIFIED {
			benefitsText = "Your payment is being processed.\nThis may take up to 30 mins."
		}
		containerBgColor = "#F6E1C1"
	}

	iconTextBottomComponent := make([]*components.Component, 0)

	// append benefits icon if applicable
	if benefitsIcon != "" {
		iconTextBottomComponent = append(iconTextBottomComponent, &components.Component{
			Content: getAnyWithoutError(commontypes.GetVisualElementImageFromUrl(benefitsIcon).WithProperties(&commontypes.VisualElementProperties{
				Width:  28,
				Height: 28,
			}).WithImageType(commontypes.ImageType_PNG)),
		})
		toShowWithFixedPrimaryBtnSize = true
	}

	if p.conf.F30IssuanceFeeRefundContent().IsEnabled() && len(p.conf.F30IssuanceFeeRefundContent().SegmentIds()) > 0 {
		// check if user is in segment
		isMemberResp, err := p.segmentationClient.IsMember(ctx, &segment.IsMemberRequest{
			ActorId:    req.ActorId,
			SegmentIds: p.conf.F30IssuanceFeeRefundContent().SegmentIds(),
			LatestBy:   timestampPb.Now(),
		})

		if err != nil {
			// non blocking error
			logger.Error(ctx, "error fetching from segmentation service", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.ActorId))
		}
		for _, segmentId := range p.conf.F30IssuanceFeeRefundContent().SegmentIds() {
			if isMemberResp.GetSegmentMembershipMap()[segmentId].GetSegmentStatus() == segment.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
				isMemberResp.GetSegmentMembershipMap()[segmentId].GetIsActorMember() {
				benefitsText = p.conf.F30IssuanceFeeRefundContent().PhysicalCardSectionText()
			}
		}
	}

	// append offer text if applicable
	if benefitsText != "" {
		iconTextBottomComponent = append(iconTextBottomComponent, &components.Component{
			Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(benefitsText, benefitsTextColor, commontypes.FontStyle_SUBTITLE_XS, commontypes.Text_ALIGNMENT_LEFT)),
		})
		toShowWithFixedPrimaryBtnSize = true
	}

	if len(iconTextBottomComponent) > 0 {
		bottomSectionComponentsList = append(bottomSectionComponentsList, &components.Component{
			Content: getAnyWithoutError(&sections.HorizontalListSection{
				Components: iconTextBottomComponent,
			}),
		})
	}

	// append primary button to order card if applicable
	if nextAction != nil {
		bottomSectionComponentsList = append(bottomSectionComponentsList, &components.Component{
			// Primary button
			Content: p.buildCardPrimaryButton(
				primaryBtnText,
				primaryBtnTextColor,
				primaryBtnBgColor,
				nextAction,
				toShowWithFixedPrimaryBtnSize,
				req.LayoutId,
				req.HadPhysicalCardInPast,
			),
		})
	}

	if nextAction == nil && benefitsText != "" {
		// Add spacer
		bottomSectionComponentsList = append(bottomSectionComponentsList, &components.Component{
			Content: getAnyWithoutError(&components.Spacer{}),
		})
	}

	return &components.Component{
		Content: getAnyWithoutError(&sections.HorizontalListSection{
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
			VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
			Components:            bottomSectionComponentsList,
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Size: &properties.Size{
								Width: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
								},
								Height: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_WRAP,
								},
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: containerBgColor,
								},
							},
							Corner: &properties.CornerProperty{
								BottomLeftCorner:  20,
								BottomRightCorner: 20,
							},
							Padding: &properties.PaddingProperty{
								Left:   16,
								Right:  16,
								Top:    16,
								Bottom: 16,
							},
						},
					},
				},
			},
		}),
	}
}

func (p *PhysicalCardOrderSection) buildActivateCardBottomSection(ctx context.Context, req *SectionBuilderRequest) *components.Component {
	activateCardDl, _ := cardFePkg.GetQrCodeDeeplink(req.CurrentCard.GetId(), pkggenconf.IsFeatureEnabledOnPlatform(ctx, p.conf.EnableSecurePinActivationFlow()))

	return &components.Component{
		Content: getAnyWithoutError(&sections.HorizontalListSection{
			HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
			Components: []*components.Component{
				{
					// Primary button
					Content: p.buildCardPrimaryButton(
						"Activate Card",
						colorSnow,
						colorForestGreen,
						activateCardDl,
						false,
						req.LayoutId,
						req.HadPhysicalCardInPast,
					),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: &properties.ContainerProperty{
							Size: &properties.Size{
								Width: &properties.Size_Dimension{
									Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
								},
								Height: &properties.Size_Dimension{
									Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
									ExactValue: 60,
								},
							},
							BgColor: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: colorSnow,
								},
							},
							Corner: &properties.CornerProperty{
								BottomLeftCorner:  20,
								BottomRightCorner: 20,
							},
							Padding: &properties.PaddingProperty{
								Left:  16,
								Right: 16,
							},
						},
					},
				},
			},
		}),
	}
}

func (p *PhysicalCardOrderSection) buildCardPrimaryButton(
	btnText, btnTextColor, btnBgColor string,
	nextActionDl *deeplinkPb.Deeplink,
	toShowWithFixedSize bool,
	layoutId string,
	hadPhysicalCardInPast bool,
) *anypb.Any {

	var containerSizeProps *properties.Size
	if toShowWithFixedSize {
		containerSizeProps = &properties.Size{
			Width: &properties.Size_Dimension{
				Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
				ExactValue: 172,
			},
			Height: &properties.Size_Dimension{
				Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
				ExactValue: 36,
			},
		}
	} else {
		containerSizeProps = &properties.Size{
			Width: &properties.Size_Dimension{
				Type: properties.Size_Dimension_DIMENSION_TYPE_FILL,
			},
			Height: &properties.Size_Dimension{
				Type:       properties.Size_Dimension_DIMENSION_TYPE_EXACT,
				ExactValue: 36,
			},
		}
	}

	var horizontalSection = &sections.HorizontalListSection{
		Components: []*components.Component{
			{
				Content: getAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyleFontAlignment(btnText, btnTextColor, commontypes.FontStyle_BUTTON_S, commontypes.Text_ALIGNMENT_CENTER)),
			},
		},
		HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: &properties.ContainerProperty{
						Size:    containerSizeProps,
						BgColor: widget.GetBlockBackgroundColour(btnBgColor),
						Corner: &properties.CornerProperty{
							TopLeftCornerRadius:  20,
							TopRightCornerRadius: 20,
							BottomLeftCorner:     20,
							BottomRightCorner:    20,
						},
						Padding: &properties.PaddingProperty{
							Left:   16,
							Right:  16,
							Bottom: 10,
							Top:    10,
						},
					},
				},
			},
		},
		InteractionBehaviors: []*behaviors.InteractionBehavior{
			{
				Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
					OnClickBehavior: &behaviors.OnClickBehavior{
						Action: getAnyWithoutError(nextActionDl),
					},
				},
				AnalyticsEvent: &analytics.AnalyticsEvent{
					EventName: "DCClickOnDCDashboardV2Screen",
					Properties: map[string]string{
						"section":                   "OrderPhysicalCardWidgetOrderButton",
						"redirection_screen_name":   nextActionDl.GetScreen().String(),
						"title":                     btnText,
						"layout_id":                 layoutId,
						"had_physical_card_in_past": strconv.FormatBool(hadPhysicalCardInPast),
					}},
			},
		},
	}

	if !toShowWithFixedSize {
		horizontalSection.HorizontalArrangement = sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER
	}

	return getAnyWithoutError(horizontalSection)
}
