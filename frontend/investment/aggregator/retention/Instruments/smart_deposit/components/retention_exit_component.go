// nolint: dupl
package components

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/frontend/deeplink"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	retentionScreenfePb "github.com/epifi/gamma/api/frontend/investment/aggregator/retention_screen"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/investment/aggregator/retention/navigation"
	"github.com/epifi/gamma/pkg/investment"
)

var (
	SDRetentionOkGotIt      = "Ok, got it"
	SDRetentionTitleText    = "Smart move!"
	SDRetentionSubTitleText = "You chose to earn full returns."
)

type SDRetentionExitComponent struct {
	*SDBaseComponent
}

func NewSDRetentionExitComponent(depositClient depositPb.DepositClient, genConfig *genconf.Config) *SDRetentionExitComponent {
	return &SDRetentionExitComponent{SDBaseComponent: NewSDBaseComponent(retentionScreenfePb.RetentionScreen_RetentionScreen_Retention_EXIT_SCREEN, depositClient, genConfig)}
}

func (s *SDRetentionExitComponent) GetComponent(ctx context.Context, retentionParam *retentionScreenfePb.RetentionScreenParams, navigationParam *navigation.NavigationParams) (*retentionScreenfePb.RetentionScreenDisplayData, error) {
	var exitComponent *retentionScreenfePb.RetentionExitScreen
	if lo.Contains(retentionParam.GetVisitedScreens(), retentionScreenfePb.RetentionScreen_RetentionScreen_Retention_REWARDS_AND_OFFERS_SCREEN) {
		depositAcc, err := s.GetDepositAccountByDepositAccId(ctx, retentionParam.GetInstrumentData().GetFixedDepositInstrumentData().GetDepositAccountId())
		if err != nil {
			return nil, errors.Wrap(err, "error while getting deposit account")
		}
		retentionPeriodForReward := investment.GetDaysForWhichRewardsWillBeGiven(depositAcc.GetMaturityDate(), timestamp.New(time.Now()), depositAcc.GetPrincipalAmount())
		rewardAcceptedExitComponent := &retentionScreenfePb.RetentionExitScreen{
			ExitVisualElement: getVisualElementForRetentionExitComponent("https://epifi-icons.pointz.in/investments/retention_screen/image+1768.png"),
			ExitTitle:         commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("Congrats! Your Fi coins will be ready in %v days.", retentionPeriodForReward), "#313234", commontypes.FontStyle_HEADLINE_L),
			ExitSubTitle:      commontypes.GetTextFromStringFontColourFontStyle("You'll receive the Fi coins in your account when this deposit is closed or has matured.", "#313234", commontypes.FontStyle_HEADLINE_L),
			Cta:               getCtaForComponent(),
		}
		exitComponent = rewardAcceptedExitComponent
	} else {
		exitComponent = &retentionScreenfePb.RetentionExitScreen{
			ExitVisualElement: getVisualElementForRetentionExitComponent("https://epifi-icons.pointz.in/investments/retention_screen/retention-fd-exit-thumb-up-2.png"),
			ExitTitle:         commontypes.GetTextFromHtmlStringFontColourFontStyle(SDRetentionTitleText+" <br> "+SDRetentionSubTitleText, "#313234", commontypes.FontStyle_HEADLINE_L),
			Cta:               getCtaForComponent(),
		}
	}
	return &retentionScreenfePb.RetentionScreenDisplayData{
		RetentionScreen: &retentionScreenfePb.RetentionScreenDisplayData_RetentionExitScreen{RetentionExitScreen: exitComponent},
	}, nil
}

func getVisualElementForRetentionExitComponent(iconUrl string) *commontypes.VisualElement {
	return &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
		Image: &commontypes.VisualElement_Image{
			Source: &commontypes.VisualElement_Image_Url{
				Url: iconUrl,
			},
			Properties: &commontypes.VisualElementProperties{
				Height: 140,
				Width:  140,
			},
			ImageType: commontypes.ImageType_PNG,
		},
	}}
}

func getCtaForComponent() *deeplink.Cta {
	return &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_DONE,
		Text:         SDRetentionOkGotIt,
		DisplayTheme: deeplink.Cta_PRIMARY,
		Status:       deeplink.Cta_CTA_STATUS_ENABLED,
	}
}
