package components

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/frontend/deeplink"
	retentionScreenfePb "github.com/epifi/gamma/api/frontend/investment/aggregator/retention_screen"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/investment/aggregator"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/investment/aggregator/retention/navigation"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/investment"
)

type FDRetentionRewardsComponent struct {
	*FDBaseComponent
}

func NewFDRetentionRewardsComponent(depositClient depositPb.DepositClient, genConfig *genconf.Config) *FDRetentionRewardsComponent {
	return &FDRetentionRewardsComponent{FDBaseComponent: NewFDBaseComponent(retentionScreenfePb.RetentionScreen_RetentionScreen_Retention_REWARDS_AND_OFFERS_SCREEN, depositClient, genConfig)}
}

func (s *FDRetentionRewardsComponent) GetComponent(ctx context.Context, retentionParam *retentionScreenfePb.RetentionScreenParams, navigationParam *navigation.NavigationParams) (*retentionScreenfePb.RetentionScreenDisplayData, error) {
	depositAcc, err := s.GetDepositAccountByDepositAccId(ctx, retentionParam.GetInstrumentData().GetFixedDepositInstrumentData().GetDepositAccountId())
	if err != nil {
		return nil, errors.Wrap(err, "error while getting deposit account")
	}
	swipeButton, err := getSwipeButtonForRetentionBenefit(depositAcc)
	if err != nil {
		return nil, errors.Wrap(err, "error while getting swipe button")
	}
	rewardCoins := investment.CalculateFiCoins(depositAcc.GetMaturityDate(), timestamp.New(time.Now()), depositAcc.GetPrincipalAmount())
	retentionPeriodForReward := investment.GetDaysForWhichRewardsWillBeGiven(depositAcc.GetMaturityDate(), timestamp.New(time.Now()), depositAcc.GetPrincipalAmount())
	rewardsComponent := &retentionScreenfePb.RetentionRewardsAndOffersScreen{
		TitleVisualElement: getClosureScreenVisualElement(depositAcc.GetDepositIcon().GetImageUrl()),
		Title:              commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(FDClosureScreenTitle, depositAcc.GetName()), "#313234", commontypes.FontStyle_SUBTITLE_1),
		ChooseRewardsComponent: &retentionScreenfePb.RetentionChooseRewardsComponent{
			VisualElement: getChoseRewardsScreenVisualElement(),
			Info:          commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("Keep this deposit for next %v days and get rewarded on deposit closure!", retentionPeriodForReward), "#453C86", commontypes.FontStyle_HEADLINE_L),
			Cta:           getCtaForChoseRewards(navigationParam, retentionParam),
			Shadows: []*ui.Shadow{
				{
					Height:  4,
					Opacity: 1,
					Colour:  ui.GetBlockColor("#4A4476"),
				},
			},
			RewardAmount:    commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%v", rewardCoins), "#FFFFFF", commontypes.FontStyle_HEADLINE_2XL),
			BackgroundColor: widget.GetBlockBackgroundColour("#CDC6E8"),
		},
		SkipRewardsComponent: &retentionScreenfePb.RetentionSkipRewardsComponent{
			Title: commontypes.GetTextFromStringFontColourFontStyle("OR SKIP REWARDS", "#929599", commontypes.FontStyle_SUBTITLE_S),
		},
		SwipeComponent: &retentionScreenfePb.SwipeComponent{
			DisplayText: commontypes.GetTextFromStringFontColourFontStyle("Amount will be transferred to your Federal Bank a/c", "#929599", commontypes.FontStyle_BODY_XS),
			SwipeButton: swipeButton,
		},
	}
	return &retentionScreenfePb.RetentionScreenDisplayData{
		RetentionScreen: &retentionScreenfePb.RetentionScreenDisplayData_RetentionRewardsAndOffersScreen{RetentionRewardsAndOffersScreen: rewardsComponent},
	}, nil
}

func getCtaForChoseRewards(navParams *navigation.NavigationParams, retentionScreenParams *retentionScreenfePb.RetentionScreenParams) *ui.IconTextComponent {
	return ui.NewITC().
		WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle("Keep the deposit & get Fi coins", "#F6F9FD", commontypes.FontStyle_BUTTON_M)).
		WithContainer(44, 316, 19, "#6F62A4").
		WithDeeplink(deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_INVESTMENT_RETENTION_SCREEN, &aggregator.RetentionScreenOptions{
			Params: &retentionScreenfePb.RetentionScreenParams{
				InstrumentData: retentionScreenParams.GetInstrumentData(),
				VisitedScreens: navParams.NextActionVisitedScreen,
			},
		}))
}

func getChoseRewardsScreenVisualElement() *commontypes.VisualElement {
	return &commontypes.VisualElement{Asset: &commontypes.VisualElement_Lottie_{
		Lottie: &commontypes.VisualElement_Lottie{
			Source: &commontypes.VisualElement_Lottie_Url{
				Url: "https://epifi-icons.pointz.in/deposit/FD+Closure+Blank.json",
			},
			Properties: &commontypes.VisualElementProperties{
				Width:  128,
				Height: 128,
			},
		}}}
}
