// nolint
package mutualfund

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/constants"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	actionPb "github.com/epifi/gamma/api/fittt/action"
	feHdr "github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/investment/mutualfund"
	"github.com/epifi/gamma/api/investment/mutualfund/catalog"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	phPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
	rmsPb "github.com/epifi/gamma/api/rms/manager"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/investment"
	"github.com/epifi/gamma/pkg/rms"
)

const (
	// TODO: Quick fix to solve for multi-pagination issue
	// Please keep this in sync page size in 'investment/mutualfund/dao/impl/mutual_fund_order.go'
	// Keep clients also updated on the same
	defaultPageSize = 10
)

// InvestmentDigest is responsible for all the dynamic parts of showing MF investment digest tiles
// Refer: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE?node-id=1:2616#167824635
func (s *Service) InvestmentDigest(ctx context.Context, feReq *fePb.InvestmentDigestRequest) (
	*fePb.InvestmentDigestResponse, error) {
	feResp := &fePb.InvestmentDigestResponse{RespHeader: &feHdr.ResponseHeader{}}
	actorId := feReq.GetReq().GetAuth().GetActorId()
	investedMfTiles := []*fePb.InvestmentDigestTile{}
	var investedMfIds []string
	var pageContext *rpc.PageContextResponse
	reqPageContext := feReq.GetPageContext()
	/*
		Since we are filtering out 'non-invested' mutual funds in frontend, we need to make multiple calls in BE to fetch
		all 'invested' funds for a page
		'IOS' has logic to not make subsequent call for next despite the page token if the response does not have at-least
		4 tiles.
	*/
	for {
		invDigestReq := &catalog.GetInvestmentsRequest{
			ActorId:     actorId,
			PageContext: reqPageContext,
		}
		beResp, beErr := s.catalogClient.GetInvestments(ctx, invDigestReq)
		if err2 := epifigrpc.RPCError(beResp, beErr); err2 != nil {
			logger.Error(ctx, "Error while fetching investments", zap.String(logger.ACTOR_ID, actorId), zap.Error(err2))
			feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(err2.Error())
			return feResp, nil
		}
		pageContext = beResp.GetPageContext()
		tiles, err := s.getInvestedMfTiles(ctx, beResp.GetMutualFundsInvestmentInfos())
		if err != nil {
			logger.Error(ctx, "Error while adding invested MF tiles", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
			feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return feResp, nil
		}
		for _, tile := range investedMfTiles {
			investedMfIds = append(investedMfIds, tile.GetMutualFundId())
		}
		investedMfTiles = append(investedMfTiles, tiles...)
		if pageContext.GetHasAfter() && len(investedMfTiles) < defaultPageSize {
			reqPageContext.Token = &rpc.PageContextRequest_AfterToken{
				AfterToken: pageContext.GetAfterToken(),
			}
			continue
		}
		break
	}

	var fitttSubscriptionTiles []*fePb.InvestmentDigestTile
	// do it for first page only
	// TODO: Remove after adding proper support in upcoming auto invest
	if feReq.GetPageContext().GetToken() == nil && len(investedMfIds) < defaultPageSize {
		fitttSubs, fitttErr := s.getFitttSubscriptions(ctx, actorId, investedMfIds)
		if fitttErr != nil {
			logger.Error(ctx, "Error while fetching FIT rules", zap.String(logger.ACTOR_ID, actorId), zap.Error(fitttErr))
			feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(fitttErr.Error())
			return feResp, nil
		}
		var fitttErr2 error
		fitttSubscriptionTiles, fitttErr2 = s.getFitttSubscriptionTiles(ctx, fitttSubs)
		if fitttErr2 != nil {
			logger.Error(ctx, "Error while adding FIT-rule tiles", zap.String(logger.ACTOR_ID, actorId), zap.Error(fitttErr2))
			feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(fitttErr2.Error())
			return feResp, nil
		}
	}
	var allTiles, digestTiles []*fePb.InvestmentDigestTile
	allTiles = append(allTiles, investedMfTiles...)
	allTiles = append(allTiles, fitttSubscriptionTiles...)
	if s.config.Investment().EnableInvestmentDigestTileDedupe() {
		mfIdsForDisplay := map[string]bool{}
		for _, tile := range allTiles {
			if _, ok := mfIdsForDisplay[tile.GetMutualFundId()]; !ok {
				digestTiles = append(digestTiles, tile)
				mfIdsForDisplay[tile.GetMutualFundId()] = true
				continue
			} else {
				logger.Error(ctx, "duplicate mutual fund id found in investment digest tiles", zap.String(logger.MF_ID, tile.GetMutualFundId()))
			}
		}
	} else {
		// If we are not de-duping then all tiles are shown to the user
		digestTiles = allTiles
	}
	feResp = &fePb.InvestmentDigestResponse{
		Tiles:       digestTiles,
		PageContext: pageContext,
		RespHeader:  &feHdr.ResponseHeader{Status: rpc.StatusOk()},
	}
	return feResp, nil
}

// Get tiles for all MFs where at least one order has been made
func (s *Service) getInvestedMfTiles(ctx context.Context, mutualFunds []*catalog.MutualFundInvestmentInfo) ([]*fePb.InvestmentDigestTile, error) {
	var investedMfTiles []*fePb.InvestmentDigestTile
	for _, mf := range mutualFunds {
		if vErr := verifyOptionalFields(mf); vErr != nil {
			return nil, vErr
		}

		// Skipping MFs which have been exited (i.e., last order was a SELL order, and it succeeded)
		//
		if (mf.GetInvestmentSummary().GetLatestOrder().GetOrderStatus() == orderPb.OrderStatus_SETTLED ||
			mf.GetInvestmentSummary().GetLatestOrder().GetOrderStatus() == orderPb.OrderStatus_FAILURE) &&
			money.IsZero(mf.GetInvestmentSummary().GetTotalInvestedValue()) {
			continue
		}

		tile := &fePb.InvestmentDigestTile{
			FundName:      mf.GetMutualFundName(),
			IconUrl:       investment.IconsForAmc[mf.GetAmc()],
			CurrentValue:  getCurrentValueForTile(mf.GetInvestmentSummary().GetCurrentValue()),
			GrowthPercent: mf.GetInvestmentSummary().GetGrowthPercentage(),
			MutualFundId:  mf.GetMutualFundId(),
		}

		// Conditions for setting tile state display-text, state enum-val and overall sub-text
		err := s.setTileStateSubText(ctx, tile, mf)
		if err != nil {
			return nil, err
		}
		investedMfTiles = append(investedMfTiles, tile)
	}
	return investedMfTiles, nil
}

// These optional fields in should be present in the BE response for investment digest to work as expected
func verifyOptionalFields(invInfo *catalog.MutualFundInvestmentInfo) error {
	switch {
	case invInfo.GetInvestmentSummary().GetLatestOrder() == nil:
		return fmt.Errorf("latest order should be present in investment digest. MutualfundId : %s", invInfo.GetMutualFundId())
	case invInfo.GetInvestmentSummary().GetPendingOrdersPresent() && invInfo.GetInvestmentSummary().GetLatestPendingOrder() == nil:
		return fmt.Errorf("latest pending order should be present if pendingOrderPresent flag is true. MutualfundId : %s",
			invInfo.GetMutualFundId())
	case invInfo.GetInvestmentSummary().GetPendingOrdersPresent() && invInfo.GetInvestmentSummary().GetPendingOrdersCount() == 0:
		return fmt.Errorf("pending order count should be higher than zero if pendingOrderPresent flag is true. MutualfundId : %s",
			invInfo.GetMutualFundId())
	}
	return nil
}

func getCurrentValueForTile(currentValue *moneyPb.Money) *types.Money {
	if money.IsZero(currentValue) {
		return nil
	} else {
		return types.GetFromBeMoney(currentValue)
	}
}

/*
Tile state enum-val is set according to the following priorities:
1. Use the latest order if it is in failed state, or in processing state, or has been recently bought (succeeded)
2. Else use the latest pending order (even if it is older than the latest order)

Tile state display-text and sub-text are driven the sub-conditions of enum-val. Comments are added inline for better understanding.
*/
func (s *Service) setTileStateSubText(ctx context.Context, tile *fePb.InvestmentDigestTile, mf *catalog.MutualFundInvestmentInfo) error {
	switch {
	case isFailed(mf.GetInvestmentSummary().GetLatestOrder().GetOrderStatus()):
		failedOrder := mf.GetInvestmentSummary().GetLatestOrder()
		// If order has failed within 24 hours, show failed state otherwise show NO_OP
		if time.Now().Add(-1 * 24 * time.Hour).After(failedOrder.GetUpdatedAt().AsTime()) {
			// No state display-text shown if order failed
			tile.State = fePb.InvestmentDigestTileState_DIGEST_TILE_FAILED
			if money.IsZero(mf.InvestmentSummary.GetTotalInvestedValue()) {
				tile.SubText = InvDigestSubTextZeroBalance
			} else {
				tile.SubText = getSubTextFromInvestmentReturns(ctx, mf.InvestmentSummary)
			}
		} else {
			tile.State = fePb.InvestmentDigestTileState_DIGEST_TILE_NO_OP
			if !money.IsZero(mf.GetInvestmentSummary().GetTotalInvestedValue()) {
				tile.SubText = getSubTextFromInvestmentReturns(ctx, mf.GetInvestmentSummary())
			}
		}

	case isRecentlyBought(mf.GetInvestmentSummary().GetLatestOrder().GetOrderStatus(), mf.GetFolioUpdatedAt()):
		// Note: SELL orders are not considered for showing "NEW" tag
		tile.StateDisplayText = InvDigestStateTextNew
		tile.State = fePb.InvestmentDigestTileState_DIGEST_TILE_NEW
		tile.SubText = getSubTextFromInvestmentReturns(ctx, mf.InvestmentSummary)
	case mf.GetInvestmentSummary().GetPendingOrdersPresent():
		tile.StateDisplayText = fmt.Sprintf(InvDigestStateTextProcessing, mf.InvestmentSummary.GetPendingOrdersCount())

		paymentRes, paymentErr := s.phClient.GetPaymentDetails(ctx, &phPb.GetPaymentDetailsRequest{
			OrderId:     mf.GetInvestmentSummary().GetLatestPendingOrder().GetId(),
			PaymentMode: phPb.PaymentMode(mf.GetInvestmentSummary().GetLatestPendingOrder().GetPaymentMode()),
		})
		if err := epifigrpc.RPCError(paymentRes, paymentErr); err != nil {
			if !rpc.StatusFromError(err).IsRecordNotFound() {
				return err
			}
		}

		eta, etaErr := investment.GetETADate(investment.ETAParams{
			PendingOrder:  mf.InvestmentSummary.GetLatestPendingOrder(),
			AssetClass:    mf.GetAssetClass(),
			PaymentStatus: paymentRes.GetPaymentStatus(),
			PaymentTime:   paymentRes.GetTransactionTime(),
			CategoryName:  mf.GetCategoryName(),
		})
		if etaErr != nil {
			return etaErr
		}

		// Setting tile state and subtext
		if eta.Before(time.Now()) || mf.GetInvestmentSummary().GetLatestPendingOrder().OrderStatus == orderPb.OrderStatus_MANUAL_INTERVENTION {
			tile.State = fePb.InvestmentDigestTileState_DIGEST_TILE_DELAYED
			// No sub-text shown if 1st order is delayed; but shown for subsequent orders
			if !money.IsZero(mf.InvestmentSummary.GetTotalInvestedValue()) {
				tile.SubText = getSubTextFromInvestmentReturns(ctx, mf.InvestmentSummary)
			}
		} else {
			tile.State = fePb.InvestmentDigestTileState_DIGEST_TILE_PROCESSING
			// Show ETA if no invested value, else show returns info
			if money.IsZero(mf.InvestmentSummary.GetTotalInvestedValue()) {
				tile.SubText = fmt.Sprintf(InvDigestSubTextOngoing, eta.Format("02 Jan"))
			} else {
				tile.SubText = getSubTextFromInvestmentReturns(ctx, mf.InvestmentSummary)
			}
		}
	default:
		// Handling remaining cases: zero pending orders and where the latest order has succeeded
		// and is either a SELL order or an old BUY order
		tile.State = fePb.InvestmentDigestTileState_DIGEST_TILE_NO_OP
		if !money.IsZero(mf.GetInvestmentSummary().GetTotalInvestedValue()) {
			tile.SubText = getSubTextFromInvestmentReturns(ctx, mf.GetInvestmentSummary())
		}
	}
	return nil
}

func isFailed(orderStatus orderPb.OrderStatus) bool {
	return orderStatus == orderPb.OrderStatus_FAILURE ||
		orderStatus == orderPb.OrderStatus_EXPIRED
}

func isRecentlyBought(orderStatus orderPb.OrderStatus, boughtAt *timestamp.Timestamp) bool {
	return orderStatus == orderPb.OrderStatus_CONFIRMED_BY_RTA &&
		boughtAt.AsTime().After(time.Now().Add(-24*time.Hour))
}

// Refer: https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE?node-id=1:2616#167824635
func getSubTextFromInvestmentReturns(ctx context.Context, invSummary *catalog.InvestmentSummaryInfo) string {
	switch {
	case invSummary.GetGrowthPercentage() > 0:
		diffValue, err := money.Subtract(invSummary.GetCurrentValue(), invSummary.GetTotalInvestedValue())
		if err != nil {
			logger.Error(ctx, "Error subtracting money", zap.Error(err))
			return ""
		}
		return fmt.Sprintf(InvDigestSubTextNonZeroReturns, money.ToDisplayStringWithoutSymbol(diffValue), invSummary.GrowthPercentage)
	case invSummary.GetGrowthPercentage() < 0:
		diffValue, err := money.Subtract(invSummary.GetTotalInvestedValue(), invSummary.GetCurrentValue())
		if err != nil {
			logger.Error(ctx, "Error subtracting money", zap.Error(err))
			return ""
		}
		return fmt.Sprintf(InvDigestSubTextNonZeroReturns, money.ToDisplayStringWithoutSymbol(diffValue), invSummary.GrowthPercentage)
	default:
		return InvDigestSubTextZeroReturns
	}
}

// Get one FIT subscription for each MF which actor has subscribed to but has not made any investments till date
// MFs in which actor has already invested are ignored
// Active subscriptions for a MF are prioritized over its paused subscriptions
func (s *Service) getFitttSubscriptions(ctx context.Context, actorId string, investedMfIds []string) ([]*rmsPb.RuleSubscription, error) {
	var fitttSubs []*rmsPb.RuleSubscription
	fitttRuleIds := constants.FitAutoInvestRuleIds
	ruleIdToSubscriptionMap, err := s.GetAllFitRuleSubscriptionsForMutualFundsByActor(ctx, actorId, fitttRuleIds)
	if err != nil {
		logger.Error(ctx, "Failed to fetch FIT subscriptions", zap.String(logger.ACTOR_ID, actorId))
		return nil, err
	}

	fitActionExecutions, err := s.FetchActionExecutionInfos(ctx, ruleIdToSubscriptionMap, actorId)
	if err != nil {
		logger.Error(ctx, "Failed to FetchActionExecutionInfos", zap.String(logger.ACTOR_ID, actorId))
		return nil, err
	}

	// no action execution present for user, returning nil
	if fitActionExecutions == nil {
		return nil, nil
	}

	mfToActiveSubscriptionMap := GetNextScheduledSubscriptionForMf(ruleIdToSubscriptionMap, fitActionExecutions)

	// Filtering out invested MFs
	for mfId, activeSub := range mfToActiveSubscriptionMap {
		if !List(investedMfIds).contains(mfId) {
			fitttSubs = append(fitttSubs, activeSub)
		}
	}

	mfToPausedSubscriptionMap := GetPausedSubscriptionForMf(ruleIdToSubscriptionMap, fitActionExecutions)

	// Filtering out invested MFs, and MFs with an active subscription
	for mfId, pausedSub := range mfToPausedSubscriptionMap {
		if _, ok := mfToActiveSubscriptionMap[mfId]; !ok && !List(investedMfIds).contains(mfId) {
			fitttSubs = append(fitttSubs, pausedSub)
		}
	}
	return fitttSubs, nil
}

// FIT subscription tiles are only shown for those FIT rules through which no orders have been made till date
func (s *Service) getFitttSubscriptionTiles(ctx context.Context, subs []*rmsPb.RuleSubscription) ([]*fePb.InvestmentDigestTile, error) {
	// Add tile for each mutual fund present in active subscription map
	var fitttSubscriptionTiles []*fePb.InvestmentDigestTile
	for _, sub := range subs {
		for _, ruleParams := range sub.GetRuleParamValues().GetRuleParamValues() {
			mfVal := ruleParams.GetMutualFundVal()
			if mfVal == nil {
				continue
			}

			// If FIT doesn't provide an icon, get it from MF catalog
			mfIconUrl := mfVal.GetIconUrl()
			if mfIconUrl == "" {
				mfRes, mfErr := s.catalogClient.GetMutualFund(ctx, &catalog.GetMutualFundRequest{Id: mfVal.GetMfId()})
				if err := epifigrpc.RPCError(mfRes, mfErr); err != nil {
					logger.Error(ctx, "Error getting mutual fund", zap.Error(err))
					return nil, err
				}
				mfIconUrl = investment.IconsForAmc[mfRes.GetMutualFund().GetAmc()]
			}

			tile := &fePb.InvestmentDigestTile{
				FundName:         mfVal.GetName(),
				IconUrl:          mfIconUrl,
				CurrentValue:     nil,
				GrowthPercent:    0,
				MutualFundId:     mfVal.GetMfId(),
				StateDisplayText: "",
				State:            fePb.InvestmentDigestTileState_DIGEST_TILE_NO_OP,
			}

			// Setting tile sub-text
			switch sub.GetState() {
			case rmsPb.RuleSubscriptionState_ACTIVE:
				timeZoneLocation, tErr := time.LoadLocation(ISTTimeZoneLocation)
				if tErr != nil {
					logger.Error(ctx, "error while trying to load IST time zone", zap.Error(tErr))
					return nil, tErr
				}
				tile.SubText = fmt.Sprintf(InvDigestSubTextScheduled, sub.GetNextExecutionTime().AsTime().In(timeZoneLocation).Format("02 Jan"))
			case rmsPb.RuleSubscriptionState_INACTIVE:
				tile.SubText = InvDigestSubTextPaused
			default:
				tile.SubText = ""
			}
			fitttSubscriptionTiles = append(fitttSubscriptionTiles, tile)
		}
	}
	return fitttSubscriptionTiles, nil
}

// GetNextScheduledSubscriptionForMf takes in map of FIT rules to all subscriptions corresponding to that rule ID
// and returns one subscription per MF which is next in line to be triggered
func GetNextScheduledSubscriptionForMf(ruleIdToSubscriptionsMap map[string]*rmsPb.Subscriptions, subscriptionIdToActionExecMap map[string]*actionPb.ActionExecution) map[string]*rmsPb.RuleSubscription {
	mfIdToSubscriptionMap := map[string]*rmsPb.RuleSubscription{}
	for _, subscriptions := range ruleIdToSubscriptionsMap {
		for _, sub := range subscriptions.GetRuleSubscriptions() {
			var mf *rmsPb.MutualFundValue
			if _, ok := sub.RuleParamValues.RuleParamValues[rms.RmsMutualFundVal]; ok {
				mf = sub.RuleParamValues.RuleParamValues[rms.RmsMutualFundVal].GetMutualFundVal()
			}

			// subscription is skipped, if the action is executed successfully.
			if _, ok := subscriptionIdToActionExecMap[sub.GetId()]; ok {
				action := subscriptionIdToActionExecMap[sub.GetId()]
				if action.Status == actionPb.ActionStatus_SUCCESS {
					continue
				}
			}
			if mf == nil || sub.GetState() != rmsPb.RuleSubscriptionState_ACTIVE {
				continue
			}
			if existingSubInMap, present := mfIdToSubscriptionMap[mf.GetMfId()]; present {
				// If subscription is already present for MF but will be executed later, replace it
				if sub.GetNextExecutionTime().AsTime().Before(existingSubInMap.GetNextExecutionTime().AsTime()) {
					mfIdToSubscriptionMap[mf.GetMfId()] = sub
				}
			} else {
				mfIdToSubscriptionMap[mf.GetMfId()] = sub
			}
		}
	}
	return mfIdToSubscriptionMap
}

// GetPausedSubscriptionForMf takes in map of FIT rules to all subscriptions corresponding to that rule ID
// and returns one paused subscription per MF (any random one in case of multiple paused subscriptions)
func GetPausedSubscriptionForMf(ruleIdToSubscriptionsMap map[string]*rmsPb.Subscriptions, subscriptionIdToActionExecMap map[string]*actionPb.ActionExecution) map[string]*rmsPb.RuleSubscription {
	mfIdToSubscriptionMap := map[string]*rmsPb.RuleSubscription{}
	for _, subscriptions := range ruleIdToSubscriptionsMap {
		for _, sub := range subscriptions.GetRuleSubscriptions() {
			var mf *rmsPb.MutualFundValue
			if _, ok := sub.RuleParamValues.RuleParamValues[rms.RmsMutualFundVal]; ok {
				mf = sub.RuleParamValues.RuleParamValues[rms.RmsMutualFundVal].GetMutualFundVal()
			}
			// skipped, if the action is executed successfully.
			if _, ok := subscriptionIdToActionExecMap[sub.GetId()]; ok {
				action := subscriptionIdToActionExecMap[sub.GetId()]
				if action.Status == actionPb.ActionStatus_SUCCESS {
					continue
				}
			}
			if mf == nil || sub.GetState() != rmsPb.RuleSubscriptionState_INACTIVE {
				continue
			}
			mfIdToSubscriptionMap[mf.GetMfId()] = sub
		}
	}
	return mfIdToSubscriptionMap
}

// List is an implementation of array with a contains(string) method similar to other functional languages
type List []string

func (strs List) contains(strToCheck string) bool {
	for _, str := range strs {
		if strings.EqualFold(str, strToCheck) {
			return true
		}
	}
	return false
}
