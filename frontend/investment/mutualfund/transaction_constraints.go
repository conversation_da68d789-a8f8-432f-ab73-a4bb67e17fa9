package mutualfund

import (
	"context"
	"errors"
	"fmt"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/frontend/header"
	fePb "github.com/epifi/gamma/api/frontend/investment/mutualfund"
	mfpb "github.com/epifi/gamma/api/investment/mutualfund"
	orderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	types "github.com/epifi/gamma/api/typesv2"
	wob "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/pkg/investment"
)

const (
	AMOUNTS_LIKE_ARRAY_SIZE                 = 3
	AMOUNTS_LIKE_PRECISION_WITH_DECIMAL     = 2
	AMOUNTS_LIKE_PRECISION_WITH_OUT_DECIMAL = 0
)

//nolint:funlen
func (s *Service) GetWithdrawalConstraints(ctx context.Context, req *fePb.GetWithdrawalConstraintsRequest) (*fePb.GetWithdrawalConstraintsResponse, error) {

	withdrawalConstraintsResp, err := s.getWithdrawalConstraints(ctx, req.Req.Auth.ActorId, req.MutualFundId)
	if err != nil {
		logger.Error(ctx, "error while fetching withdrawalConstraints",
			zap.String(logger.ACTOR_ID, req.Req.Auth.ActorId),
			zap.String(logger.MF_ID, req.MutualFundId),
			zap.Error(err))
		return s.getGetWithdrawalConstraintsErrorResponse(
			fmt.Sprintf("error while fetching withdrawalConstraints: %s", err.Error())), nil
	}

	maskedAccountNumber, err := s.getSavingsAccountNumber(ctx, req.Req.Auth.ActorId)
	if err != nil {
		logger.Error(ctx, "error while fetching maskedAccountNumber",
			zap.String(logger.ACTOR_ID, req.Req.Auth.ActorId),
			zap.String(logger.MF_ID, req.MutualFundId),
			zap.Error(err))
		return s.getGetWithdrawalConstraintsErrorResponse(
			fmt.Sprintf("error while fetching maskedAccountNumber: %s", err.Error())), nil
	}

	amountsLike, err := s.getAmountsLike(withdrawalConstraintsResp.MinimumWithdrawalAmount,
		withdrawalConstraintsResp.IncrementalWithdrawalAmount)
	if err != nil {
		logger.Error(ctx, "error while calculating amountsLike",
			zap.String(logger.ACTOR_ID, req.Req.Auth.ActorId),
			zap.String(logger.MF_ID, req.MutualFundId),
			zap.Error(err))
		return s.getGetWithdrawalConstraintsErrorResponse(
			fmt.Sprintf("non-success status in GetWithdrawalConstraints with shortMessage: %s and debugMessage: %s",
				withdrawalConstraintsResp.Status.ShortMessage, withdrawalConstraintsResp.Status.DebugMessage)), nil
	}

	var maximumWithdrawalAmount *moneyPb.Money
	// MaximumWithdrawalAmount returned from backend is the maximum amount that the fund house allows to withdraw at a time for the given fund.
	// If MaximumWithdrawalAmount is null, then we return WithdrawableAmount as the withdrawable amount that user has in folio.
	// If it is not null, then we return min(MaximumWithdrawalAmount and WithdrawableAmount) as that is the maximum amount user can
	// withdraw in a single go.
	if withdrawalConstraintsResp.MaximumWithdrawalAmount == nil || money.IsZero(withdrawalConstraintsResp.MaximumWithdrawalAmount) {
		maximumWithdrawalAmount = withdrawalConstraintsResp.WithdrawableAmount
	} else {
		if money.Compare(withdrawalConstraintsResp.MaximumWithdrawalAmount, withdrawalConstraintsResp.WithdrawableAmount) == 1 {
			maximumWithdrawalAmount = withdrawalConstraintsResp.WithdrawableAmount
		} else {
			maximumWithdrawalAmount = withdrawalConstraintsResp.MaximumWithdrawalAmount
		}
	}

	var incrementalWithdrawalAmount *types.Money
	if !money.IsZero(withdrawalConstraintsResp.IncrementalWithdrawalAmount) {
		incrementalWithdrawalAmount = types.GetFromBeMoney(withdrawalConstraintsResp.IncrementalWithdrawalAmount)
	} else {
		incrementalWithdrawalAmount = nil
	}

	response := &fePb.GetWithdrawalConstraintsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		TotalAmount:                 types.GetFromBeMoney(withdrawalConstraintsResp.TotalAmount),
		MaskedAccountNumber:         maskedAccountNumber,
		MinimumWithdrawalAmount:     types.GetFromBeMoney(withdrawalConstraintsResp.MinimumWithdrawalAmount),
		MaximumWithdrawalAmount:     types.GetFromBeMoney(maximumWithdrawalAmount),
		IncrementalWithdrawalAmount: incrementalWithdrawalAmount,
		AmountsLike:                 amountsLike,
		WithdrawalEta:               investment.GetWithdrawalETADays(withdrawalConstraintsResp.AssetClass, withdrawalConstraintsResp.Category),
		WithdrawableAmount:          types.GetFromBeMoney(withdrawalConstraintsResp.WithdrawableAmount),
		InfoMessage:                 WithdrawalInfoMessage,
		Tags:                        s.getTags(maskedAccountNumber, withdrawalConstraintsResp.AssetClass, withdrawalConstraintsResp.GetCategory()),
	}

	logger.Debug(ctx, "Sending following response for 'GetWithdrawalConstraints'", zap.String(logger.ACTOR_ID, req.Req.Auth.ActorId),
		zap.String(logger.MF_ID, req.MutualFundId))
	return response, nil
}

func (s *Service) getWithdrawalConstraints(ctx context.Context, actorId string, mutualFundID string) (*orderPb.GetWithdrawalConstraintsResponse, error) {
	withdrawalConstraintsResp, err := s.orderManagerClient.GetWithdrawalConstraints(ctx, &orderPb.GetWithdrawalConstraintsRequest{
		MfId:    mutualFundID,
		ActorId: actorId,
	})

	if err != nil {
		return nil, err
	}
	if !withdrawalConstraintsResp.Status.IsSuccess() {
		return nil, fmt.Errorf("non-success status in GetWithdrawalConstraints with shortMessage: %s and debugMessage: %s",
			withdrawalConstraintsResp.Status.ShortMessage, withdrawalConstraintsResp.Status.DebugMessage)
	}
	return withdrawalConstraintsResp, nil
}

func (s *Service) getSavingsAccountNumber(ctx context.Context, actorId string) (string, error) {
	wobResponse, err := s.wealthOnboardingClient.GetInvestmentData(ctx, &wob.GetInvestmentDataRequest{ActorIds: []string{actorId}})

	if te := epifigrpc.RPCError(wobResponse, err); te != nil {
		return "", te
	}
	if wobResponse.InvestmentDetailInfo[actorId] == nil ||
		wobResponse.InvestmentDetailInfo[actorId].BankDetails == nil ||
		wobResponse.InvestmentDetailInfo[actorId].BankDetails.AccountNumber == "" {
		return "", errors.New("no pre-investment details or bank details present for actor in wealth onboarding response")
	}

	return mask.GetMaskedAccountNumber(wobResponse.InvestmentDetailInfo[actorId].BankDetails.AccountNumber, "x"), nil
}

func (s *Service) getAmountsLike(minimumAmount *moneyPb.Money, incrementalAmount *moneyPb.Money) ([]string, error) {
	var amountsLike []string

	if (minimumAmount == nil || money.IsZero(minimumAmount)) && (incrementalAmount == nil || money.IsZero(incrementalAmount)) {
		return []string{}, nil
	}
	if incrementalAmount == nil || money.IsZero(incrementalAmount) {
		incrementalAmount = &moneyPb.Money{CurrencyCode: "INR", Units: 1}
	}

	currentAmount := &moneyPb.Money{
		CurrencyCode: minimumAmount.CurrencyCode,
		Units:        minimumAmount.Units,
		Nanos:        minimumAmount.Nanos,
	}
	for i := 0; i < AMOUNTS_LIKE_ARRAY_SIZE; i++ {
		moneyInPaise, err := money.ToPaise(currentAmount)
		if err != nil {
			return nil, err
		}
		var currentAmountInString string
		if moneyInPaise%100 == 0 {
			currentAmountInString, err = money.ToString(currentAmount, AMOUNTS_LIKE_PRECISION_WITH_OUT_DECIMAL)
			if err != nil {
				return nil, err
			}
		} else {
			currentAmountInString, err = money.ToString(currentAmount, AMOUNTS_LIKE_PRECISION_WITH_DECIMAL)
			if err != nil {
				return nil, err
			}
		}
		amountsLike = append(amountsLike, currentAmountInString)
		currentAmount, err = money.Sum(currentAmount, incrementalAmount)
		if err != nil {
			return nil, err
		}
	}
	return amountsLike, nil
}

func (s *Service) getTags(maskedAccountNumber string, assetClass mfpb.AssetClass, category mfpb.MutualFundCategoryName) []string {
	var tags []string
	if category == mfpb.MutualFundCategoryName_ELSS_TAX_SAVING {
		tags = append(tags, ElssLockInMessage)
	}
	tags = append(tags, fmt.Sprintf(ProcessingInDaysMessage, investment.GetWithdrawalETADays(assetClass, category)), fmt.Sprintf(MoneyDepositInfoMessage, maskedAccountNumber))
	return tags
}

func (s *Service) getGetWithdrawalConstraintsErrorResponse(debugMessage string) *fePb.GetWithdrawalConstraintsResponse {
	return &fePb.GetWithdrawalConstraintsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusInternalWithDebugMsg(debugMessage),
		},
	}
}
