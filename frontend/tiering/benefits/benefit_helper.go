package benefits

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/pkg/accrual"

	"fmt"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	benefitsPb "github.com/epifi/gamma/api/frontend/tiering/benefits"
	feTieringExtPb "github.com/epifi/gamma/api/frontend/tiering/benefits/external"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/frontend/tiering/deeplink/plan_options/data"
	feTieringErrors "github.com/epifi/gamma/frontend/tiering/errors"
)

// source of truth for tier to benefits mapping
var (
	fiRegularBenefits = []benefitsPb.BenefitItem_Benefit{
		benefitsPb.BenefitItem_PAID_DEBIT_CARD,
		benefitsPb.BenefitItem_FOREX_X_PERCENT,
		benefitsPb.BenefitItem_PAID_CHEQUE_BOOK,
	}
	fiBasicBenefits = []benefitsPb.BenefitItem_Benefit{
		benefitsPb.BenefitItem_ONE_TIME_FI_COINS_ON_SPENDS,
		benefitsPb.BenefitItem_ONE_X_COINS_DEBIT_SPENDS,
		benefitsPb.BenefitItem_PAID_DEBIT_CARD,
		benefitsPb.BenefitItem_FOREX_X_PERCENT,
		benefitsPb.BenefitItem_ZERO_BALANCE_ACCOUNT,
		benefitsPb.BenefitItem_PAID_CHEQUE_BOOK,
	}
	fiPlusBenefits = []benefitsPb.BenefitItem_Benefit{
		benefitsPb.BenefitItem_TWO_TIMES_FI_COINS_ON_SPENDS,
		benefitsPb.BenefitItem_TWO_X_COINS_DEBIT_SPENDS,
		benefitsPb.BenefitItem_DEBIT_CARD_NO_ANNUAL_FEE,
		benefitsPb.BenefitItem_FOREX_CHARGES_ZERO_1,
		benefitsPb.BenefitItem_CHEQUEBOOK,
	}
	fiInfiniteBenefits = []benefitsPb.BenefitItem_Benefit{
		benefitsPb.BenefitItem_FOUR_TIMES_FI_COINS_ON_SPENDS,
		benefitsPb.BenefitItem_FOUR_X_COINS_DEBIT_SPENDS,
		benefitsPb.BenefitItem_FREE_DEBIT_CARD,
		benefitsPb.BenefitItem_FOREX_CHARGES_ZERO_UNLIMITED,
		benefitsPb.BenefitItem_PRIORITY_CUSTOMER_SUPPORT,
		benefitsPb.BenefitItem_CHEQUEBOOK,
	}
	fiAaSalaryBenefits = []benefitsPb.BenefitItem_Benefit{
		benefitsPb.BenefitItem_SALARY_ACCOUNT,
	}
	fiSalaryLiteBenefits = []benefitsPb.BenefitItem_Benefit{
		benefitsPb.BenefitItem_SALARY_ACCOUNT,
	}
	fiSalaryBenefits = []benefitsPb.BenefitItem_Benefit{
		benefitsPb.BenefitItem_SALARY_ACCOUNT,
	}
	feTierToBenefitsMap = map[beTieringExtPb.Tier][]benefitsPb.BenefitItem_Benefit{
		beTieringExtPb.Tier_TIER_FI_REGULAR:          fiRegularBenefits,
		beTieringExtPb.Tier_TIER_FI_BASIC:            fiBasicBenefits,
		beTieringExtPb.Tier_TIER_FI_PLUS:             fiPlusBenefits,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         fiInfiniteBenefits,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: fiAaSalaryBenefits,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: fiAaSalaryBenefits,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: fiAaSalaryBenefits,
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE:      fiSalaryLiteBenefits,
		beTieringExtPb.Tier_TIER_FI_SALARY:           fiSalaryBenefits,
	}
	fiRegularHeroBenefits  = []benefitsPb.BenefitItem_Benefit{}
	fiBasicHeroBenefits    = []benefitsPb.BenefitItem_Benefit{}
	fiPlusHeroBenefits     = []benefitsPb.BenefitItem_Benefit{}
	fiInfiniteHeroBenefits = []benefitsPb.BenefitItem_Benefit{
		benefitsPb.BenefitItem_CASHBACK_GET_X_PERCENT,
	}
	fiSalaryLiteHeroBenefits = []benefitsPb.BenefitItem_Benefit{}
	fiAaSalaryHeroBenefits   = []benefitsPb.BenefitItem_Benefit{}
	fiSalaryHeroBenefits     = []benefitsPb.BenefitItem_Benefit{
		benefitsPb.BenefitItem_SALARY_ACCOUNT,
	}
	feTierToHeroBenefitsMap = map[beTieringExtPb.Tier][]benefitsPb.BenefitItem_Benefit{
		beTieringExtPb.Tier_TIER_FI_REGULAR:          fiRegularHeroBenefits,
		beTieringExtPb.Tier_TIER_FI_BASIC:            fiBasicHeroBenefits,
		beTieringExtPb.Tier_TIER_FI_PLUS:             fiPlusHeroBenefits,
		beTieringExtPb.Tier_TIER_FI_INFINITE:         fiInfiniteHeroBenefits,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1: fiAaSalaryHeroBenefits,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2: fiAaSalaryHeroBenefits,
		beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3: fiAaSalaryHeroBenefits,
		beTieringExtPb.Tier_TIER_FI_SALARY_LITE:      fiSalaryLiteHeroBenefits,
		beTieringExtPb.Tier_TIER_FI_SALARY:           fiSalaryHeroBenefits,
	}
)

// nolint:funlen
// getBenefit takes benefit enum and returns full BenefitItem
// internal to benefits manager
func getBenefit(benefit benefitsPb.BenefitItem_Benefit, _ *data.PlanOptionsData) (*benefitsPb.BenefitItem, error) {
	switch benefit {
	case benefitsPb.BenefitItem_FREE_DEBIT_CARD:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitDebitCardHeading,
			SubHeading: BenefitDebitCardSubHeading,
			ImageUrl:   BenefitDebitCardIconUrl,
		}, nil
	case benefitsPb.BenefitItem_TWO_TIMES_FI_COINS_ON_SPENDS:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    accrual.ReplaceCoinWithPointIfApplicable(BenefitDoubleCoinsHeading),
			SubHeading: accrual.ReplaceCoinWithPointIfApplicable(BenefitDoubleCoinsSubHeading),
			ImageUrl:   BenefitDoubleCoinsIconUrl,
		}, nil
	case benefitsPb.BenefitItem_EXCLUSIVE_REWARDS_PLUS:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitExclusiveRewardsHeading,
			SubHeading: BenefitExclusiveRewardsSubHeading,
			ImageUrl:   BenefitExclusiveRewardsIconUrl,
		}, nil
	case benefitsPb.BenefitItem_FOUR_TIMES_FI_COINS_ON_SPENDS:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    accrual.ReplaceCoinWithPointIfApplicable(BenefitFourTimesCoinsHeading),
			SubHeading: accrual.ReplaceCoinWithPointIfApplicable(BenefitFourTimesCoinsSubHeading),
			ImageUrl:   BenefitFourTimesCoinsIconUrl,
		}, nil
	case benefitsPb.BenefitItem_JOINING_BONUS:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    accrual.ReplaceCoinWithPointIfApplicable(BenefitJoiningBonusHeading),
			SubHeading: BenefitJoiningBonusSubHeading,
			ImageUrl:   BenefitJoiningBonusIconUrl,
		}, nil
	case benefitsPb.BenefitItem_TEN_PERCENT_SALARY_AS_COINS:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    accrual.ReplaceCoinWithPointIfApplicable(BenefitSalaryHeading),
			SubHeading: accrual.ReplaceCoinWithPointIfApplicable(BenefitSalarySubHeading),
			ImageUrl:   BenefitSalaryIconUrl,
		}, nil
	case benefitsPb.BenefitItem_EXCLUSIVE_REWARDS_INFINITE:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitExclusiveRewardsInfHeading,
			SubHeading: BenefitExclusiveRewardsInfSubHeading,
			ImageUrl:   BenefitExclusiveRewardsInfIconUrl,
		}, nil
	case benefitsPb.BenefitItem_CHEQUEBOOK:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitFreeChequeHeading,
			SubHeading: BenefitFreeChequeSubHeading,
			ImageUrl:   BenefitFreeChequeIconUrl,
		}, nil
	case benefitsPb.BenefitItem_JUMP_SEVEN_PERCENT:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitJumpBasicHeading,
			SubHeading: BenefitJumpBasicSubHeading,
			ImageUrl:   BenefitJumpBasicIconUrl,
		}, nil
	case benefitsPb.BenefitItem_JUMP_EIGHT_PERCENT:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitJumpPlusHeading,
			SubHeading: BenefitJumpPlusSubHeading,
			ImageUrl:   BenefitJumpPlusIconUrl,
		}, nil
	case benefitsPb.BenefitItem_JUMP_NINE_PERCENT:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitJumpInfHeading,
			SubHeading: BenefitJumpInfSubHeading,
			ImageUrl:   BenefitJumpInfIconUrl,
		}, nil
	case benefitsPb.BenefitItem_CASHBACK_GET_X_PERCENT:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitUpiCashbackHeading,
			SubHeading: BenefitUpiCashbackSubHeading,
			ImageUrl:   BenefitUpiCashbackIconUrl,
		}, nil
	case benefitsPb.BenefitItem_PRIORITY_CUSTOMER_SUPPORT:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitCustomerSupportHeading,
			SubHeading: BenefitCustomerSupportSubHeading,
			ImageUrl:   BenefitCustomerSupportIconUrl,
		}, nil
	case benefitsPb.BenefitItem_FOREX_CHARGES_ZERO_1:
		return &benefitsPb.BenefitItem{
			Benefit:               benefit,
			Heading:               BenefitForexWLimitHeading,
			SubHeading:            BenefitForexWLimitSubHeading,
			ImageUrl:              BenefitForexWLimitIconUrl,
			BottomSheetHeading:    BenefitForexWLimitBottomSheetHeading,
			BottomSheetSubHeading: BenefitForexWLimitBottomSheetSubHeading,
		}, nil
	case benefitsPb.BenefitItem_FOREX_CHARGES_ZERO_UNLIMITED:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitForexWoLimitHeading,
			SubHeading: BenefitForexWoLimitSubHeading,
			ImageUrl:   BenefitForexWoLimitIconUrl,
		}, nil
	case benefitsPb.BenefitItem_ZERO_BALANCE_ACCOUNT:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitZeroAccountHeading,
			SubHeading: BenefitZeroAccountSubHeading,
			ImageUrl:   BenefitZeroAccountIconUrl,
		}, nil
	case benefitsPb.BenefitItem_ONE_TIME_FI_COINS_ON_SPENDS:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    accrual.ReplaceCoinWithPointIfApplicable(BenefitOneTimeCoinsHeading),
			SubHeading: accrual.ReplaceCoinWithPointIfApplicable(BenefitOneTimeCoinsSubHeading),
			ImageUrl:   BenefitOneTimeCoinsIconUrl,
		}, nil
	case benefitsPb.BenefitItem_PAID_DEBIT_CARD:
		return &benefitsPb.BenefitItem{
			Benefit:               benefit,
			Heading:               BenefitPaidDebitCardHeading,
			SubHeading:            BenefitPaidDebitCardSubHeading,
			ImageUrl:              BenefitPaidDebitCardIconUrl,
			BottomSheetHeading:    BenefitPaidDebitCardBSHeading,
			BottomSheetSubHeading: BenefitPaidDebitCardBSSubHeading,
		}, nil
	case benefitsPb.BenefitItem_DEBIT_CARD_NO_ANNUAL_FEE:
		return &benefitsPb.BenefitItem{
			Benefit:               benefit,
			Heading:               BenefitDebitCardNoRenewalHeading,
			SubHeading:            BenefitDebitCardNoRenewalSubHeading,
			ImageUrl:              BenefitDebitCardNoRenewalIconUrl,
			BottomSheetHeading:    BenefitDebitCardNoRenewalBSHeading,
			BottomSheetSubHeading: BenefitDebitCardNoRenewalBSSubHeading,
		}, nil
	case benefitsPb.BenefitItem_ZERO_PENALTY_BENEFIT:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitZeroPenaltyHeading,
			SubHeading: BenefitZeroPenaltySubHeading,
			ImageUrl:   BenefitZeroPenaltyIconUrl,
		}, nil
	case benefitsPb.BenefitItem_PAID_CHEQUE_BOOK:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitPaidChequeBookHeading,
			SubHeading: BenefitPaidChequeBookSubHeading,
			ImageUrl:   BenefitPaidChequeBookIconUrl,
		}, nil
	case benefitsPb.BenefitItem_FOREX_X_PERCENT:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitForexXPercentHeading,
			SubHeading: BenefitForexXPercentSubHeading,
			ImageUrl:   BenefitForexXPercentIconUrl,
		}, nil
	case benefitsPb.BenefitItem_ONE_X_COINS_DEBIT_SPENDS:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    accrual.ReplaceCoinWithPointIfApplicable(BenefitOneXDebitHeading),
			SubHeading: accrual.ReplaceCoinWithPointIfApplicable(BenefitOneXDebitSubHeading),
			ImageUrl:   BenefitOneXDebitIconUrl,
		}, nil
	case benefitsPb.BenefitItem_TWO_X_COINS_DEBIT_SPENDS:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    accrual.ReplaceCoinWithPointIfApplicable(BenefitTwoXDebitHeading),
			SubHeading: accrual.ReplaceCoinWithPointIfApplicable(BenefitTwoXDebitSubHeading),
			ImageUrl:   BenefitTwoXDebitIconUrl,
		}, nil
	case benefitsPb.BenefitItem_FOUR_X_COINS_DEBIT_SPENDS:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    accrual.ReplaceCoinWithPointIfApplicable(BenefitFourXDebitHeading),
			SubHeading: accrual.ReplaceCoinWithPointIfApplicable(BenefitFourXDebitSubHeading),
			ImageUrl:   BenefitFourXDebitIconUrl,
		}, nil
	case benefitsPb.BenefitItem_HEALTH_INSURANCE:
		return &benefitsPb.BenefitItem{
			Benefit:    benefit,
			Heading:    BenefitHealthInsuranceHeading,
			SubHeading: BenefitHealthInsuranceSubHeading,
			ImageUrl:   BenefitHealthInsuranceIconUrl,
		}, nil

	default:
		return nil, feTieringErrors.ErrUnknownBenefit
	}
}

type AllTierBenefitsResponse struct {
	HeroBenefits     []*benefitsPb.BenefitItem
	DefaultBenefits  []*benefitsPb.BenefitItem
	UnlockedBenefits []*benefitsPb.BenefitItem
	LockedBenefits   []*benefitsPb.BenefitItem
}

func (r *AllTierBenefitsResponse) GetHeroBenefits() []*benefitsPb.BenefitItem {
	if r != nil {
		return r.HeroBenefits
	}
	return nil
}

func (r *AllTierBenefitsResponse) GetDefaultBenefits() []*benefitsPb.BenefitItem {
	if r != nil {
		return r.DefaultBenefits
	}
	return nil
}

func (r *AllTierBenefitsResponse) GetUnlockedBenefits() []*benefitsPb.BenefitItem {
	if r != nil {
		return r.UnlockedBenefits
	}
	return nil
}

func (r *AllTierBenefitsResponse) GetLockedBenefits() []*benefitsPb.BenefitItem {
	if r != nil {
		return r.LockedBenefits
	}
	return nil
}

// GetAllBenefitsForTier returns all benefits for a tier as list of BenefitItem
func (s *Service) GetAllBenefitsForTier(planOptionsData *data.PlanOptionsData,
	toShowHeroBenefits bool,
) (*AllTierBenefitsResponse, error) {
	feTier := planOptionsData.GetFeTier()
	var heroBenefitItems []*benefitsPb.BenefitItem
	heroBenefits, ok := feTierToHeroBenefitsMap[feTier]
	if toShowHeroBenefits && !ok {
		return nil, fmt.Errorf("hero benefits not mapped for the tier :%s", feTier.String())
	}
	benefits, ok := feTierToBenefitsMap[feTier]
	if !ok {
		return nil, fmt.Errorf("benefits not mapped for the tier :%s", feTier.String())
	}
	if toShowHeroBenefits {
		for _, benefitEnum := range heroBenefits {
			benefitItemsFromWrapper, getBenefitErr := s.getBenefitWrapper(planOptionsData, benefitEnum, true)
			if getBenefitErr != nil {
				return nil, fmt.Errorf("error fetching benefits for %s, %w", feTier.String(), getBenefitErr)
			}
			heroBenefitItems = append(heroBenefitItems, benefitItemsFromWrapper.GetHeroBenefits()...)
		}
	} else {
		benefits = append(heroBenefits, benefits...)
	}
	var defaultBenefitItems, unlockedBenefitItems, lockedBenefitItems []*benefitsPb.BenefitItem
	for _, benefitEnum := range benefits {
		benefitItemsFromWrapper, getBenefitErr := s.getBenefitWrapper(planOptionsData, benefitEnum, false)
		if getBenefitErr != nil {
			return nil, fmt.Errorf("error fetching benefits for %s, %w", feTier.String(), getBenefitErr)
		}
		defaultBenefitItems = append(defaultBenefitItems, benefitItemsFromWrapper.GetDefaultBenefits()...)
		unlockedBenefitItems = append(unlockedBenefitItems, benefitItemsFromWrapper.GetUnlockedBenefits()...)
		lockedBenefitItems = append(lockedBenefitItems, benefitItemsFromWrapper.GetLockedBenefits()...)
	}
	return &AllTierBenefitsResponse{
		HeroBenefits:     heroBenefitItems,
		DefaultBenefits:  defaultBenefitItems,
		UnlockedBenefits: unlockedBenefitItems,
		LockedBenefits:   lockedBenefitItems,
	}, nil
}

// nolint:funlen
// getBenefitWrapper is a wrapper on top of getBenefit(for non-prefetched benefits) and prefetched benefits(benefits from cmap)
func (s *Service) getBenefitWrapper(planOptionsData *data.PlanOptionsData, benefitEnum benefitsPb.BenefitItem_Benefit,
	isHeroBenefit bool,
) (*AllTierBenefitsResponse, error) {
	preFetchedBenefitEnums := map[benefitsPb.BenefitItem_Benefit]bool{
		benefitsPb.BenefitItem_SALARY_ACCOUNT: true,
		benefitsPb.BenefitItem_JUMP_X_PERCENT: true,
	}
	if isBenefitPreFetched := preFetchedBenefitEnums[benefitEnum]; isBenefitPreFetched {
		// if benefit type is already prefetched, then get the benefit from cmap
		existingBenefits := planOptionsData.GetPreFetchedBenefitsMap().Get(planOptionsData.GetFeTier().String())
		var (
			defaultBenefits  []*benefitsPb.BenefitItem
			unlockedBenefits []*benefitsPb.BenefitItem
			lockedBenefits   []*benefitsPb.BenefitItem
			heroBenefits     []*benefitsPb.BenefitItem
		)
		for _, section := range []feTieringExtPb.BenefitsSection{
			feTieringExtPb.BenefitsSection_BENEFITS_SECTION_DEFAULT,
			feTieringExtPb.BenefitsSection_BENEFITS_SECTION_UNLOCKED,
			feTieringExtPb.BenefitsSection_BENEFITS_SECTION_LOCKED,
			feTieringExtPb.BenefitsSection_BENEFITS_SECTION_HERO,
		} {
			benefitsList, ok := existingBenefits[section]
			if !ok {
				continue
			}
			for _, convertedBenefit := range benefitsList {
				if convertedBenefit.GetBenefit() == benefitEnum {
					switch section {
					case feTieringExtPb.BenefitsSection_BENEFITS_SECTION_DEFAULT:
						defaultBenefits = append(defaultBenefits, convertedBenefit)
					case feTieringExtPb.BenefitsSection_BENEFITS_SECTION_UNLOCKED:
						unlockedBenefits = append(unlockedBenefits, convertedBenefit)
					case feTieringExtPb.BenefitsSection_BENEFITS_SECTION_LOCKED:
						lockedBenefits = append(lockedBenefits, convertedBenefit)
					case feTieringExtPb.BenefitsSection_BENEFITS_SECTION_HERO:
						heroBenefits = append(heroBenefits, convertedBenefit)
					default:
						return nil, fmt.Errorf("section not handled: %s", section.String())
					}
				}
			}
		}
		return &AllTierBenefitsResponse{
			DefaultBenefits:  defaultBenefits,
			UnlockedBenefits: unlockedBenefits,
			LockedBenefits:   lockedBenefits,
			HeroBenefits:     heroBenefits,
		}, nil
	}
	benefit, getBenefitErr := getBenefit(benefitEnum, planOptionsData)
	if getBenefitErr != nil {
		return nil, getBenefitErr
	}
	var resp *AllTierBenefitsResponse
	if isHeroBenefit {
		resp = &AllTierBenefitsResponse{
			HeroBenefits: []*benefitsPb.BenefitItem{benefit},
		}
	} else {
		resp = &AllTierBenefitsResponse{
			DefaultBenefits: []*benefitsPb.BenefitItem{benefit},
		}
	}
	return resp, nil
}

// getIndividualBenefitForTierLaunchInfo populates ui properties wrt to tier launch info screen
func getIndividualBenefitForTierLaunchInfo(benefit *benefitsPb.BenefitItem) *deeplinkPb.BenefitItem {
	var bottomSheetDeeplink *deeplinkPb.Deeplink
	if benefit.GetBottomSheetHeading() != "" {
		bottomSheetDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_TIER_BENEFIT_BOTTOMSHEET,
			ScreenOptions: &deeplinkPb.Deeplink_TierBenefitBottomSheetOptions{
				TierBenefitBottomSheetOptions: &deeplinkPb.TierBenefitBottomSheetOptions{
					Icon: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  benefit.GetImageUrl(),
						Width:     int32(BenefitBottomSheetImageWidth),
						Height:    int32(BenefitBottomSheetImageHeight),
					},
					Heading: commontypes.GetTextFromStringFontColourFontStyle(
						benefit.GetBottomSheetHeading(),
						BenefitBottomSheetFontColor,
						commontypes.FontStyle_HEADLINE_L,
					),
					SubHeading: commontypes.GetTextFromStringFontColourFontStyle(
						benefit.GetBottomSheetSubHeading(),
						BenefitBottomSheetSubheadingFontColor,
						commontypes.FontStyle_BODY_S,
					),
					Cta: &deeplinkPb.Cta{
						Type:         deeplinkPb.Cta_DONE,
						Text:         BenefitBottomSheetCtaText,
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
					},
					BackgroundColour: BenefitBottomSheetBgColor,
					Identifier:       benefit.String(),
				},
			},
		}
	}
	return &deeplinkPb.BenefitItem{
		Heading: commontypes.GetTextFromStringFontColourFontStyle(
			benefit.GetHeading(),
			BenefitItemTierLaunchInfoHeadingFontColor,
			commontypes.FontStyle_SUBTITLE_S,
		),
		SubHeading: commontypes.GetTextFromStringFontColourFontStyle(
			benefit.GetSubHeading(),
			BenefitItemTierLaunchInfoSubHeadingFontColor,
			commontypes.FontStyle_BODY_XS,
		),
		Icon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  benefit.GetImageUrl(),
			Width:     int32(BenefitItemTierLaunchInfoIconWidth),
			Height:    int32(BenefitItemTierLaunchInfoIconHeight),
		},
		Deeplink:   bottomSheetDeeplink,
		Identifier: benefit.GetBenefit().String(),
	}
}

// getIndividualBenefitForAllPlans populates ui properties wrt to all plans screen
func getIndividualBenefitForAllPlans(benefit *benefitsPb.BenefitItem) *deeplinkPb.BenefitItem {
	var bottomSheetDeeplink *deeplinkPb.Deeplink
	if benefit.GetBottomSheetHeading() != "" {
		bottomSheetDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_TIER_BENEFIT_BOTTOMSHEET,
			ScreenOptions: &deeplinkPb.Deeplink_TierBenefitBottomSheetOptions{
				TierBenefitBottomSheetOptions: &deeplinkPb.TierBenefitBottomSheetOptions{
					Icon: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  benefit.GetImageUrl(),
						Width:     int32(BenefitBottomSheetImageWidth),
						Height:    int32(BenefitBottomSheetImageHeight),
					},
					Heading: commontypes.GetTextFromStringFontColourFontStyle(
						benefit.GetBottomSheetHeading(),
						BenefitBottomSheetFontColor,
						commontypes.FontStyle_HEADLINE_L,
					),
					SubHeading: commontypes.GetTextFromStringFontColourFontStyle(
						benefit.GetBottomSheetSubHeading(),
						BenefitBottomSheetSubheadingFontColor,
						commontypes.FontStyle_BODY_S,
					),
					Cta: &deeplinkPb.Cta{
						Type:         deeplinkPb.Cta_DONE,
						Text:         BenefitBottomSheetCtaText,
						DisplayTheme: deeplinkPb.Cta_PRIMARY,
					},
					CtaList: append(benefit.GetBottomSheetCtaList(),
						&deeplinkPb.Cta{
							Type:         deeplinkPb.Cta_DONE,
							Text:         BenefitBottomSheetCtaText,
							DisplayTheme: deeplinkPb.Cta_PRIMARY,
						},
					),
					BackgroundColour: BenefitBottomSheetBgColor,
				},
			},
		}
	}
	return &deeplinkPb.BenefitItem{
		Heading: commontypes.GetTextFromStringFontColourFontStyle(
			benefit.GetHeading(),
			BenefitItemHeadingFontColor,
			commontypes.FontStyle_SUBTITLE_S,
		),
		SubHeading: commontypes.GetTextFromStringFontColourFontStyle(
			benefit.GetSubHeading(),
			BenefitItemSubHeadingFontColor,
			commontypes.FontStyle_BODY_XS,
		),
		Icon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  benefit.GetImageUrl(),
			Width:     int32(BenefitItemIconWidth),
			Height:    int32(BenefitItemIconHeight),
		},
		Deeplink:   bottomSheetDeeplink,
		Identifier: benefit.GetBenefit().String(),
	}
}

// getInfoItemList returns InfoItem with check mark icon and benefits heading
// To be used all benefits bottom sheet
func (s *Service) getInfoItemList(planOptionsData *data.PlanOptionsData) ([]*deeplinkPb.InfoItem, error) {
	fiBenefitsResp, fiBenefitsErr := s.GetAllBenefitsForTier(planOptionsData, false)
	if fiBenefitsErr != nil {
		return nil, fiBenefitsErr
	}
	var infoItems []*deeplinkPb.InfoItem
	for _, benefit := range fiBenefitsResp.GetDefaultBenefits() {
		infoItems = append(infoItems, &deeplinkPb.InfoItem{
			Icon:  AllBenefitsCheckMarkIconUrl,
			Title: benefit.GetHeading(),
		})
	}
	return infoItems, nil
}
