package earned_benefits

import (
	"context"
	"fmt"
	"strconv"
	"time"

	accrualPkg "github.com/epifi/gamma/pkg/accrual"

	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/tiering/amb/ui"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/colors"

	enums2 "github.com/epifi/gamma/api/salaryprogram/enums"
	types "github.com/epifi/gamma/api/typesv2"
	salaryScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/accounts"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fePb "github.com/epifi/gamma/api/frontend/rewards"
	feTieringPb "github.com/epifi/gamma/api/frontend/tiering"
	"github.com/epifi/gamma/api/tiering/enums"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExternalPb "github.com/epifi/gamma/api/tiering/external"
	popupPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/popup"
	tieringScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	usStocksDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks"
	metadata2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks/metadata"
	uiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/usstocks/frontend"
	feTieringCta "github.com/epifi/gamma/frontend/tiering/cta"
	"github.com/epifi/gamma/frontend/tiering/data_collector"
	displayNames "github.com/epifi/gamma/frontend/tiering/display_names"
	feTieringErrors "github.com/epifi/gamma/frontend/tiering/errors"
	"github.com/epifi/gamma/frontend/tiering/helper"
	tieringAaSalHelper "github.com/epifi/gamma/frontend/tiering/helper/aasalary"
	"github.com/epifi/gamma/pkg/card"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	rewardsPkg "github.com/epifi/gamma/pkg/rewards"

	"golang.org/x/text/language"
	"golang.org/x/text/message"
	gmoney "google.golang.org/genproto/googleapis/type/money"
)

const (
	monthNameFormat = "Jan"
)

var getContentColorForProgressBar = func() *widget.BackgroundColour {
	return &widget.BackgroundColour{
		Colour: &widget.BackgroundColour_LinearGradient{
			LinearGradient: &widget.LinearGradient{
				Degree:           45,
				LinearColorStops: []*widget.ColorStop{{Color: midOrange, StopPercentage: 0}, {Color: lightOrange, StopPercentage: 70}, {Color: lightYellow, StopPercentage: 95}},
			},
		},
	}
}

var getExpiredMoneyPlant = func() *feTieringPb.BenefitsOptions {
	return &feTieringPb.BenefitsOptions{
		Priority: 70,
		Option: &feTieringPb.BenefitsOptions_RewardView{
			RewardView: &feTieringPb.RewardView{
				Status: feTieringPb.RewardView_EXPIRED,
				Header: &feTieringPb.RewardView_TitleComponent{
					Title: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(expiredText, platinumGrey, commontypes.FontStyle_SUBTITLE_S)).WithContainerPadding(4, 24, 4, 24),
					BackgroundColour: &widget.BackgroundColour{
						Colour: &widgetPb.BackgroundColour_LinearGradient{
							LinearGradient: &widgetPb.LinearGradient{
								Degree: 90,
								LinearColorStops: []*widgetPb.ColorStop{
									{
										Color: aliceBlue,
									},
									{
										Color:          aliceBlue,
										StopPercentage: 80,
									},
								},
							},
						},
					},
					BorderColour: &widget.BackgroundColour{
						Colour: &widgetPb.BackgroundColour_RadialGradient{
							RadialGradient: &widgetPb.RadialGradient{
								Center: &widgetPb.CenterCoordinates{
									CenterX: 100,
									CenterY: 70,
								},
								OuterRadius: 100,
								Colours:     []string{white},
							},
						},
					},
				},
				PlantIcon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: moneyPlantBlurIcon,
							},
							Properties: &commontypes.VisualElementProperties{
								Height: 60,
								Width:  43,
							},
							ImageType: commontypes.ImageType_PNG,
						},
					},
				},
				BackgroundColour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: jetBlack,
					},
				},
				BorderColour: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_LinearGradient{
						LinearGradient: &widget.LinearGradient{
							Degree: 90,
							LinearColorStops: []*widgetPb.ColorStop{
								{
									Color:          white,
									StopPercentage: 70,
								},
								{
									Color: white,
								},
							},
						},
					},
				},
			},
		},
	}
}

var getExpiryContentForMoneyPlant = func(daysSinceMoneyPlantCreated, noOfDaysToExpire uint32) *feTieringPb.RewardView_TitleComponent {
	if daysSinceMoneyPlantCreated > 3 && noOfDaysToExpire > 5 {
		return nil
	}

	var (
		fontColor, bgColor string
	)

	if daysSinceMoneyPlantCreated <= 3 {
		fontColor, bgColor = moderateOrange, amber50
	} else {
		fontColor, bgColor = moderatePink, cherry50
	}

	return &feTieringPb.RewardView_TitleComponent{
		Title: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("Expires in %d days", noOfDaysToExpire), fontColor, commontypes.FontStyle_SUBTITLE_XS)).WithContainerPadding(4, 24, 4, 24),
		BackgroundColour: &widget.BackgroundColour{
			Colour: &widgetPb.BackgroundColour_BlockColour{
				BlockColour: bgColor,
			},
		},
	}
}

var tierToMultiplierMap = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_PLUS:             plusRewardsMultiplier,
	tieringExternalPb.Tier_TIER_FI_INFINITE:         infiniteRewardsMultiplier,
	tieringExternalPb.Tier_TIER_FI_SALARY:           infiniteRewardsMultiplier,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_1: aaSalaryRewardsMultiplier,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_2: aaSalaryRewardsMultiplier,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3: aaSalaryRewardsMultiplier,
	tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:     plusRewardsMultiplier,
}

var tierToValueBackMap = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_PLUS:             plusValueBack,
	tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:     salaryBasicValueBack,
	tieringExternalPb.Tier_TIER_FI_INFINITE:         infiniteValueBack,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3: primeValueBack,
	tieringExternalPb.Tier_TIER_FI_SALARY:           salaryValueBack,
}

var tierToMinOrderValueMap = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_PLUS:             plusMinOrderValue,
	tieringExternalPb.Tier_TIER_FI_INFINITE:         infiniteMinOrderValue,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3: primeMinOrderValue,
}

var tierToFiCoinsLimitsMap = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_PLUS:             fiCoinsLimitsForPlus,
	tieringExternalPb.Tier_TIER_FI_INFINITE:         fiCoinsLimitsForInfinite,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3: fiCoinsLimitsTextForPrime,
}

var tierToLightningTextMap = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_PLUS:             plusLightningText,
	tieringExternalPb.Tier_TIER_FI_INFINITE:         infiniteLightningText,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3: primeLightningText,
}

var tierToDismissIconMap = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_PLUS:             dismissIconPlus,
	tieringExternalPb.Tier_TIER_FI_INFINITE:         dismissIconInfinite1,
	tieringExternalPb.Tier_TIER_FI_SALARY:           dismissIconInfinite, // todo: use salary dismiss icon
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_1: dismissIconAaSalary,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_2: dismissIconAaSalary,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3: dismissIconAaSalary,
}

var tierToIconContainerColor = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_PLUS:     liteCocoaBrown,
	tieringExternalPb.Tier_TIER_FI_INFINITE: cadetBlue,
	tieringExternalPb.Tier_TIER_FI_SALARY:   lightSlateBlue, // todo: use salary container color
}

var tierToInfoIconUrl = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_PLUS:     plusInfoIcon,
	tieringExternalPb.Tier_TIER_FI_INFINITE: infiniteInfoIcon,
}

var tierToForexPopupTitle = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_PLUS:             plusForexRefundPopupTitle,
	tieringExternalPb.Tier_TIER_FI_INFINITE:         infiniteForexPopupTitle,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_1: plusForexRefundPopupTitle,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_2: infiniteForexPopupTitle,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3: infiniteForexPopupTitle,
	tieringExternalPb.Tier_TIER_FI_SALARY:           salaryForexPopupTitle,
	tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:     salaryBasicForexPopupTitle,
}

var tierToForexPopupBody = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_PLUS:             plusForexRefundPopupBody,
	tieringExternalPb.Tier_TIER_FI_INFINITE:         infiniteForexPopupBody,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_1: plusForexRefundPopupBody,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_2: infiniteForexPopupBody,
	tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3: infiniteForexPopupBody,
	tieringExternalPb.Tier_TIER_FI_SALARY:           salaryForexPopupBody,
	tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:     salaryBasicForexPopupBody,
}

var tierToAMCPopupTitle = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_SALARY:       salaryAMCPopupTitle,
	tieringExternalPb.Tier_TIER_FI_SALARY_BASIC: salaryBasicAMCPopupTitle,
}

var tierToDCAMCValue = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_SALARY:       debitCardSalaryAmcValue,
	tieringExternalPb.Tier_TIER_FI_SALARY_BASIC: debitCardSalaryBasicAmcValue,
}

// tierToDCAMCStrikeThroughValue maintains the strike through value for the debit card AMC value for the respective tiers.
// Even empty string is considered as a valid value, as it is used to determine if the strike through value is mapped for
// all the tiers and not accidentally missed to configure.
var tierToDCAMCStrikeThroughValue = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_SALARY_BASIC: debitCardAmcSalaryBasicStrikethroughValue,
	tieringExternalPb.Tier_TIER_FI_SALARY:       debitCardAmcSalaryStrikethroughValue,
}

var tierToAMCPopupBody = map[tieringExternalPb.Tier]string{
	tieringExternalPb.Tier_TIER_FI_SALARY:       salaryAMCPopupBody,
	tieringExternalPb.Tier_TIER_FI_SALARY_BASIC: salaryBasicAMCPopupBody,
}

var getDebitCardPopupTitle = func(data *EarnedBenefitData) (string, error) {
	var popupTitle string

	var tierToDebitCardPopupTitle = map[tieringExternalPb.Tier]string{
		tieringExternalPb.Tier_TIER_FI_PLUS:             plusCardChargesTitle,
		tieringExternalPb.Tier_TIER_FI_INFINITE:         infiniteCardChargesTitle,
		tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_1: plusCardChargesTitle,
		tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_2: infiniteCardChargesTitle,
		tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3: infiniteCardChargesTitle,
		tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:     salaryBasicCardChargesTitle,
		tieringExternalPb.Tier_TIER_FI_SALARY:           salaryCardChargesTitle,
	}

	switch data.GetTierToLoad() {
	case tieringExternalPb.Tier_TIER_FI_PLUS, tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_1:
		diffAmount, diffAmountErr := money.Subtract(data.GetDebitCardOrderChargesData().GetActualCardCharge(), data.GetDebitCardOrderChargesData().GetUserPaidCharge())
		if diffAmountErr != nil {
			return "", fmt.Errorf("failed to get diff amount, %w", diffAmountErr)
		}
		popupTitle = fmt.Sprintf(tierToDebitCardPopupTitle[data.GetTierToLoad()], diffAmount.GetUnits())
	default:
		popupTitle = tierToDebitCardPopupTitle[data.GetTierToLoad()]
	}
	return popupTitle, nil
}

var getDebitCardPopupBody = func(data *EarnedBenefitData) (string, error) {
	tier := data.GetTierToLoad()
	dispName, err := displayNames.GetTitleCaseDisplayString(tier)
	if err != nil {
		return "", fmt.Errorf("failed to get display string, %w", err)
	}

	actualPrice := money.ToDisplayStringWithPrecision(card.ActualPhysicalDCCharges, 0)
	discountedPrice := money.ToDisplayStringWithPrecision(data.GetDebitCardOrderChargesData().GetUserPaidCharge(), 0)
	if discountedPrice == "₹0" {
		discountedPrice = "free"
	}

	var tierToDebitCardPopupBody = map[tieringExternalPb.Tier]string{
		tieringExternalPb.Tier_TIER_FI_PLUS:             fmt.Sprintf(debitCardChargeDiscountedBody, dispName, actualPrice, discountedPrice),
		tieringExternalPb.Tier_TIER_FI_INFINITE:         fmt.Sprintf(debitCardChargesFeeBody, dispName, actualPrice, discountedPrice),
		tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_1: fmt.Sprintf(debitCardChargeDiscountedBody, dispName, actualPrice, discountedPrice),
		tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_2: fmt.Sprintf(debitCardChargesFeeBody, dispName, actualPrice, discountedPrice),
		tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3: fmt.Sprintf(debitCardChargesFeeBody, dispName, actualPrice, discountedPrice),
		tieringExternalPb.Tier_TIER_FI_SALARY:           salaryDebitCardChargesFeeBody,
		tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:     salaryBasicDebitCardChargesBody,
	}

	body, ok := tierToDebitCardPopupBody[tier]
	if !ok {
		return "", fmt.Errorf("debit card popup body not configured for %s tier", tier.String())
	}

	return body, nil
}

var rewardsWaysToEarnDeeplink = func() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_REWARDS_WAYS_TO_EARN,
	}
}

var payLandingPageDeeplink = func() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PAY_LANDING_SCREEN,
	}
}

var rewardsLandingPage = func() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_OfferCatalogScreenOptions{
			OfferCatalogScreenOptions: &deeplinkPb.OfferCatalogScreenOptions{
				// optional filters
			},
		},
	}
}

var aaSalaryLandingPage = func() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_AA_SALARY_LANDING_SCREEN,
	}
}

var termsAndConditionsDeeplink = func() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PROFILE_SETTINGS_LEGAL,
	}
}

var faqDeeplink = func() *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_HELP_MAIN,
	}
}

func getRightChevronUrl(tier tieringExternalPb.Tier) (string, error) {
	switch tier {
	case tieringExternalPb.Tier_TIER_FI_PLUS:
		return plusChevron, nil
	case tieringExternalPb.Tier_TIER_FI_INFINITE:
		return infiniteChevron, nil
	case tieringExternalPb.Tier_TIER_FI_SALARY, tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:
		return platinumGreyChevron, nil
	default:
		if tier.IsAaSalaryTier() {
			return aaSalaryChevron, nil
		}

		return "", fmt.Errorf("no right chevron configured for %s tier", tier.String())
	}
}

func getTotalBenefitTextColor(tier tieringExternalPb.Tier) (textColor, containerColor string, err error) {
	switch tier {
	case tieringExternalPb.Tier_TIER_FI_PLUS:
		return brownBramble, tuftBush, nil
	case tieringExternalPb.Tier_TIER_FI_INFINITE:
		return nearCyprusBlue, softPastelBlue, nil
	case tieringExternalPb.Tier_TIER_FI_SALARY, tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:
		return brownBramble, lightYellow, nil
	default:
		if tier.IsAaSalaryTier() {
			return blue, pattensBlue, nil
		}

		return "", "", fmt.Errorf("no color configured for %s tier", tier.String())
	}
}

func (c *ComponentBuilder) getMonthlyRewardsInfoPopup(data *EarnedBenefitData) *deeplinkPb.Deeplink {
	var infoList []*deeplinkPb.InformationPopupOptions_Info
	if time.Now().Before(time.Date(2025, 3, 1, 0, 0, 0, 0, datetime.IST)) && !cfg.IsNonProdEnv(c.gconf.Application().Environment) {
		infoList = c.getInfoListsForInfoPopup(data)
	} else {
		infoList = getNewInfoListsForInfoPopup(data)
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
				Icon:          commontypes.GetVisualElementFromUrlHeightAndWidth(popupInfoIcon, 48, 48),
				BgColor:       white,
				InfoList:      infoList,
				DividerColor:  neutralFog200,
				DisplayScreen: deeplinkPb.InformationPopupOptions_DISPLAY_SCREEN_TIERING_EARNED_BENEFITS,
			},
		},
	}
}

func getNewInfoListsForInfoPopup(data *EarnedBenefitData) []*deeplinkPb.InformationPopupOptions_Info {
	var infoList []*deeplinkPb.InformationPopupOptions_Info
	tier := data.GetTierToLoad()

	var (
		monthlyBase  = rewardsPkg.BeTierToFiCoinsMonthlyCapMap[tier]
		dailyBase    = rewardsPkg.BeTierToFiCoinsDailyCapMap[tier]
		txnBase      = rewardsPkg.BeTierToFiCoinsTxnCapMap[tier]
		monthlyWorth = rewardsPkg.BeTierToTieringMonthlyRewardMonthlyCashEquivalentCapMap[tier]
		dailyWorth   = rewardsPkg.BeTierToTieringMonthlyRewardDailyCashEquivalentCapMap[tier]
		txnWorth     = rewardsPkg.BeTierToTieringMonthlyRewardTxnCashEquivalentCapMap[tier]
	)

	// This function handles the check for pre- / post-migration internally.
	// Values in maps are small (max 50,000), so conversion to int32 is safe
	monthly := accrualPkg.ConvertFiCoinsToFiPointsIfApplicable(int32(monthlyBase), false, nil) //nolint:gosec
	daily := accrualPkg.ConvertFiCoinsToFiPointsIfApplicable(int32(dailyBase), false, nil)     //nolint:gosec
	txn := accrualPkg.ConvertFiCoinsToFiPointsIfApplicable(int32(txnBase), false, nil)         //nolint:gosec

	var conversionString string
	if time.Now().After(accrualPkg.GetFiCoinsToFiPointsMigrationTime()) {
		// Post-migration rule: "4 Fi-Points = ₹1"
		pointsPerRupee := int(1 / rewardsPkg.FiPointsToCashConversionRatio)
		conversionString = fmt.Sprintf("%d Fi-Points = ₹1", pointsPerRupee)
	} else {
		// Pre-migration rule: "100 Fi-Coins = ₹2"
		rupeesPer100Coins := int(100 * rewardsPkg.FiCoinsToCashConversionRatioForNonCCUser)
		conversionString = fmt.Sprintf("100 Fi-Coins = ₹%d", rupeesPer100Coins)
	}

	if tier.IsSalaryTier() {
		var infoBodyText string
		if tier == tieringExternalPb.Tier_TIER_FI_SALARY {
			infoBodyText = fmt.Sprintf(accrualPkg.ReplaceCoinWithPointIfApplicable(infoPopupBodyTextSalary), monthly, monthlyWorth, daily, dailyWorth, txn, txnWorth, conversionString)
		} else if tier == tieringExternalPb.Tier_TIER_FI_SALARY_BASIC {
			infoBodyText = fmt.Sprintf(accrualPkg.ReplaceCoinWithPointIfApplicable(infoPopupBodyTextSalaryBasic), monthly, monthlyWorth, daily, dailyWorth, txn, txnWorth, conversionString)
		}
		infoList = append(infoList,
			&deeplinkPb.InformationPopupOptions_Info{
				TextTitle: commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(infoPopupTitle, tierToValueBackMap[tier]), blackCat, commontypes.FontStyle_SUBTITLE_2),
				BodyTexts: []*commontypes.Text{
					commontypes.GetTextFromHtmlStringFontColourFontStyle(infoBodyText, platinumGrey, commontypes.FontStyle_BODY_3_PARA),
				},
			},
		)
	} else {
		// Use the format string from the map and replace label if needed
		limitsFormat := tierToFiCoinsLimitsMap[tier]
		limitsFormat = accrualPkg.ReplaceCoinWithPointIfApplicable(limitsFormat)

		// Formating the limits text here based on tier - some tiers have additional text requiring more arguments
		var limitsText string
		switch tier {
		case tieringExternalPb.Tier_TIER_FI_INFINITE, tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3:
			// These tiers have additional text about earning across all plans, so they need 8 arguments
			limitsText = fmt.Sprintf(limitsFormat, monthly, monthlyWorth, daily, dailyWorth, txn, txnWorth, monthly, monthlyWorth)
		default:
			// Other tiers (like PLUS) only need 6 arguments
			limitsText = fmt.Sprintf(limitsFormat, monthly, monthlyWorth, daily, dailyWorth, txn, txnWorth)
		}

		infoList = append(infoList,
			&deeplinkPb.InformationPopupOptions_Info{
				TextTitle: commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(infoPopupTitle, tierToValueBackMap[tier]), blackCat, commontypes.FontStyle_SUBTITLE_2),
				BodyTexts: []*commontypes.Text{
					commontypes.GetTextFromHtmlStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable(fmt.Sprintf(infoPopupBodyText1, tierToValueBackMap[tier], tierToMinOrderValueMap[tier])), platinumGrey, commontypes.FontStyle_BODY_3_PARA),
				},
			},
			&deeplinkPb.InformationPopupOptions_Info{
				TextTitle: commontypes.GetTextFromStringFontColourFontStyle("Reward Limits", blackCat, commontypes.FontStyle_SUBTITLE_2),
				BodyTexts: []*commontypes.Text{
					commontypes.GetTextFromHtmlStringFontColourFontStyle(limitsText, platinumGrey, commontypes.FontStyle_BODY_3_PARA),
				},
			},
			&deeplinkPb.InformationPopupOptions_Info{
				TextTitle: commontypes.GetTextFromStringFontColourFontStyle("Reward Amount", blackCat, commontypes.FontStyle_SUBTITLE_2),
				BodyTexts: []*commontypes.Text{
					commontypes.GetTextFromHtmlStringFontColourFontStyle(rewardAmountText, platinumGrey, commontypes.FontStyle_BODY_3_PARA),
				},
			},
			&deeplinkPb.InformationPopupOptions_Info{
				TextTitle: commontypes.GetTextFromStringFontColourFontStyle("Reward Giveaway", blackCat, commontypes.FontStyle_SUBTITLE_2),
				BodyTexts: []*commontypes.Text{
					commontypes.GetTextFromHtmlStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable(rewardGiveawayText), platinumGrey, commontypes.FontStyle_BODY_3_PARA),
				},
			},
			&deeplinkPb.InformationPopupOptions_Info{
				TextTitle: commontypes.GetTextFromStringFontColourFontStyle(conversionString, blackCat, commontypes.FontStyle_SUBTITLE_2),
			},
		)
	}

	return infoList
}

func (c *ComponentBuilder) getInfoListsForInfoPopup(data *EarnedBenefitData) []*deeplinkPb.InformationPopupOptions_Info {
	var infoList []*deeplinkPb.InformationPopupOptions_Info
	tier := data.GetTierToLoad()
	monthlyUpperCap := money.ToDisplayStringWithPrecision(data.GetRewardsData().GetMonthlyCashRewardUpperCap(), 0)

	switch {
	case tier.IsCashbackEligibleTier() && time.Now().Before(time.Date(2025, 3, 1, 0, 0, 0, 0, datetime.IST)) && !cfg.IsNonProdEnv(c.gconf.Application().Environment):
		cashbackBodyText := fmt.Sprintf(accrualPkg.ReplaceCoinWithPointIfApplicable(monthlyRewardPopUp2PercentCashbackText), tieringAaSalHelper.TierToCashbackPercent[tier], monthlyUpperCap)
		infoList = append(infoList, &deeplinkPb.InformationPopupOptions_Info{
			TextTitle: commontypes.GetTextFromStringFontColourFontStyle(monthlyRewardsPopupCashbackTitle, blackCat, commontypes.FontStyle_SUBTITLE_2),
			BodyTexts: []*commontypes.Text{commontypes.GetTextFromHtmlStringFontColourFontStyle(cashbackBodyText, platinumGrey, commontypes.FontStyle_BODY_3_PARA)},
		})
	default:
	}

	multiplierText, ok := tierToMultiplierMap[tier]
	if !ok {
		return infoList
	}

	fiCoinsRewardText := fmt.Sprintf(accrualPkg.ReplaceCoinWithPointIfApplicable(monthlyRewardPopUpFiCoinsRewardsText), multiplierText, 100, int32(rewardsPkg.FiPointsToCashConversionRatio*100))
	infoList = append(infoList, &deeplinkPb.InformationPopupOptions_Info{
		TextTitle: commontypes.GetTextFromStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable(monthlyRewardsPopupFiCoinsTitle), blackCat, commontypes.FontStyle_SUBTITLE_2),
		BodyTexts: []*commontypes.Text{commontypes.GetTextFromHtmlStringFontColourFontStyle(fiCoinsRewardText, platinumGrey, commontypes.FontStyle_BODY_3_PARA)},
	})

	return infoList
}

func getProgressBarForCashReward(data *EarnedBenefitData) (*feTieringPb.MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile, error) {
	if !data.GetTierToLoad().IsCashbackEligibleTier() {
		return nil, nil
	}

	monthlyCashRewardUpperCap := data.GetRewardsData().GetMonthlyCashRewardUpperCap()
	dailyCashRewardUpperCap := data.GetRewardsData().GetDailyCashRewardUpperCap()

	currMonthTotalCashReward := data.GetRewardsData().GetCurrMonthProjectedCash()
	currDayTotalCashReward := data.GetRewardsData().GetCurrDayProjectedCash()

	cmpRes, cmpErr := money.CompareV2(currMonthTotalCashReward, monthlyCashRewardUpperCap)
	if cmpErr != nil {
		return nil, fmt.Errorf("failed to compare uppercap with cash reward earned, %w", cmpErr)
	}
	if cmpRes == 1 {
		currMonthTotalCashReward = monthlyCashRewardUpperCap
	}

	progressPercentage := uint32(float64(currMonthTotalCashReward.GetUnits()) / float64(monthlyCashRewardUpperCap.GetUnits()) * 100)

	subtitle := uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(cashRewardTileSubtitle, aircraftExteriorGrey, commontypes.FontStyle_SUBTITLE_XS))
	if progressPercentage == 100 {
		subtitle = getLimitReachedComponent(monthlyLimitReached)
	}
	if currDayTotalCashReward.GetUnits() >= dailyCashRewardUpperCap.GetUnits() {
		subtitle = getLimitReachedComponent(fmt.Sprintf(dailyLimitReached, dailyCashRewardUpperCap.GetUnits()))
	}

	return &feTieringPb.MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile{
		LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(cashIcon, 64, 64),
		Title:    commontypes.GetTextFromStringFontColourFontStyle(getCashRewardProgressBarTitleBasedOnTier(data.GetTierToLoad()), platinumGrey, commontypes.FontStyle_OVERLINE_XS_CAPS),
		DividerView: &feTieringPb.MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView{
			Numerator:   uiPb.NewITC().WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle(money.ToDisplayStringWithPrecision(currMonthTotalCashReward, 0), blackCat, commontypes.FontStyle_SUBTITLE_L)),
			Divider:     commontypes.GetTextFromStringFontColourFontStyle("/", blackCat, commontypes.FontStyle_SUBTITLE_L),
			Denominator: uiPb.NewITC().WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle(money.ToDisplayStringWithPrecision(monthlyCashRewardUpperCap, 0), blackCat, commontypes.FontStyle_SUBTITLE_L)),
		},
		ProgressPercentage:      progressPercentage,
		ProgressActiveColor:     fiGreen,
		ProgressBackgroundColor: chalk100,
		Subtitle:                subtitle,
	}, nil
}

func getCashRewardProgressBarTitleBasedOnTier(tier tieringExternalPb.Tier) string {
	if tier.IsAaSalaryTier() || tier == tieringExternalPb.Tier_TIER_FI_INFINITE {
		return fmt.Sprintf(cashRewardProgressBarTitleInfiniteAaSalary, tieringAaSalHelper.TierToCashbackPercent[tier])
	}
	return cashRewardProgressBarTileTitle
}

func (c *ComponentBuilder) getProgressBarForFiCoinsReward(data *EarnedBenefitData) (*feTieringPb.MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile, error) {
	cashbackPercent := tierToValueBackMap[data.GetTierToLoad()]
	response := &feTieringPb.MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile{
		Title:                   commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(backWorthProgressBarTileTitle, cashbackPercent), platinumGrey, commontypes.FontStyle_OVERLINE_XS_CAPS),
		LeftIcon:                commontypes.GetVisualElementFromUrlHeightAndWidth(fiCoinsIcon, 64, 64),
		ProgressActiveColor:     fiGreen,
		ProgressBackgroundColor: chalk100,
		TitleItc:                uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(backOnPaymentsTitle, cashbackPercent), platinumGrey, commontypes.FontStyle_SUBTITLE_XS)),
		PlantIcon:               commontypes.GetVisualElementFromUrlHeightAndWidth(moneyPlantBlackAndWhiteIcon, 60, 60),
		NoOfRotations:           2,
	}

	monthlyFiCoinsUpperCap := int32(rewardsPkg.BeTierToFiCoinsMonthlyCapMap[data.GetTierToLoad()]) //nolint:gosec
	currMonthTotalFiCoins := int32(data.GetRewardsData().GetCurrMonthProjectedFiCoins())

	if currMonthTotalFiCoins > monthlyFiCoinsUpperCap {
		currMonthTotalFiCoins = monthlyFiCoinsUpperCap
	}

	currMonthTotalFiCoinsConvertedCash := float64(currMonthTotalFiCoins) * rewardsPkg.FiCoinsToCashConversionRatioForNonCCUser
	monthlyFiCoinsUpperCapConvertedCash := float64(monthlyFiCoinsUpperCap) * rewardsPkg.FiCoinsToCashConversionRatioForNonCCUser

	response.ProgressPercentage = uint32(float64(currMonthTotalFiCoins) / float64(monthlyFiCoinsUpperCap) * 100)
	if response.GetProgressPercentage() == 100 {
		response.Subtitle, response.TitleItc = getLimitReachedComponent(monthlyLimitReached), getLimitAchievedComponent(monthlyLimitAchieved)
		response.PlantIcon = commontypes.GetVisualElementFromUrlHeightAndWidth(moneyPlantColor, 60, 60)
	}

	response.DividerView = &feTieringPb.MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_DividerView{
		Numerator:   uiPb.NewITC().WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle(money.ToDisplayStringWithPrecision(getMoneyFromFloat(currMonthTotalFiCoinsConvertedCash), 0), blackCat, commontypes.FontStyle_SUBTITLE_L)),
		Divider:     commontypes.GetTextFromStringFontColourFontStyle("/", blackCat, commontypes.FontStyle_SUBTITLE_L),
		Denominator: uiPb.NewITC().WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle(money.ToDisplayStringWithPrecision(getMoneyFromFloat(monthlyFiCoinsUpperCapConvertedCash), 0), blackCat, commontypes.FontStyle_SUBTITLE_L)),
	}

	response.EarningViews = []*feTieringPb.MonthlyBenefitView_MonthlyBenefits_MonthlyBenefitTile_EarningView{
		{
			Items: []*uiPb.IconTextComponent{
				uiPb.NewITC().WithLeftVisualElementUrlHeightAndWidth(fiCoinsCurrencyIconV2, 16, 16),
				uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(strconv.Itoa(int(currMonthTotalFiCoins)), blackCat, commontypes.FontStyle_HEADLINE_S)),
				uiPb.NewITC().WithLeftVisualElementUrlHeightAndWidth(progressBarSlash, 18, 6),
				uiPb.NewITC().WithLeftVisualElementUrlHeightAndWidth(fiCoinsCurrencyIconV2, 16, 16),
				uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(strconv.Itoa(int(monthlyFiCoinsUpperCap)), blackCat, commontypes.FontStyle_HEADLINE_S)),
			},
			ContentColor:  getContentColorForProgressBar(),
			VisibleMillis: 2000,
		},
		{
			Items: []*uiPb.IconTextComponent{
				uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("worth ", blackCat, commontypes.FontStyle_HEADLINE_S)),
				uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringWithPrecision(getMoneyFromFloat(currMonthTotalFiCoinsConvertedCash), 0)+" ", blackCat, commontypes.FontStyle_HEADLINE_S)),
				uiPb.NewITC().WithLeftVisualElementUrlHeightAndWidth(progressBarSlash, 18, 6),
				uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(" "+money.ToDisplayStringWithPrecision(getMoneyFromFloat(monthlyFiCoinsUpperCapConvertedCash), 0), blackCat, commontypes.FontStyle_HEADLINE_S)),
			},
			ContentColor:  getContentColorForProgressBar(),
			VisibleMillis: 2000,
		},
	}

	return response, nil
}

func getFiCoinCurrencyIcon() *commontypes.VisualElement {
	return commontypes.GetVisualElementFromUrlHeightAndWidth(fiCoinsCurrencyIcon, 15, 9)
}

func getMoneyFromFloat(amount float64) *gmoney.Money {
	return &gmoney.Money{
		CurrencyCode: "INR",
		Units:        int64(amount),
	}
}

func getLimitReachedComponent(text string) *uiPb.IconTextComponent {
	return uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, palmLeafGreen, commontypes.FontStyle_SUBTITLE_XS)).
		WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(checkCircle, 24, 24))
}

func getLimitAchievedComponent(text string) *uiPb.IconTextComponent {
	return uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, primo, commontypes.FontStyle_SUBTITLE_XS)).
		WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(checkCircleV2, 16, 16))
}

func (c *ComponentBuilder) getMonthlyRewardsInfoView(data *EarnedBenefitData) *uiPb.IconTextComponent {
	infoView := uiPb.NewITC().WithContainerCornerRadius(16).
		WithContainerPadding(8, 8, 8, 8).
		WithLeftImagePadding(4)

	if int32(data.GetRewardsData().GetCurrMonthFiCoins()) > c.gconf.Tiering().SpendFiCoinsThreshold() {
		return infoView.WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable(redeemInfoText), tropicalRainForestGreen, commontypes.FontStyle_SUBTITLE_XS)).
			WithContainerBackgroundColor(aquaPuraGreen).
			WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(purpleBag, 24, 24)).
			WithDeeplink(rewardsLandingPage())
	}

	lightningText := ""
	if time.Now().Before(time.Date(2025, 3, 1, 0, 0, 0, 0, datetime.IST)) && !cfg.IsNonProdEnv(c.gconf.Application().Environment) {
		lightningText = earnMoreInfoText
	} else {
		lightningText = tierToLightningTextMap[data.GetTierToLoad()]
	}

	return infoView.WithTexts(commontypes.GetTextFromHtmlStringFontColourFontStyle(lightningText, platinumGrey, commontypes.FontStyle_SUBTITLE_XS)).
		WithContainerBackgroundColor(amber50).
		WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(lightningEmoji, 24, 24)).
		WithDeeplink(payLandingPageDeeplink())
}

func getInactiveDebitCardComponent(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions_InActiveStateListView, error) {

	var benefitsList []*feTieringPb.InActiveStateListView_ContainerView_BenefitList

	if data.GetTierToLoad().IsSalaryTier() {
		tier := data.GetTierToLoad()
		// Get benefit items for the tier
		benefitItems, exists := SalaryTierDebitCardBenefits[tier]
		if !exists {
			return nil, fmt.Errorf("debit card benefits not defined for tier: %v", tier)
		}

		// Add benefits based on the tier configuration
		for _, item := range benefitItems {
			benefitsList = append(benefitsList, &feTieringPb.InActiveStateListView_ContainerView_BenefitList{
				LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(item.Icon, 48, 48),
				Title:    commontypes.GetTextFromHtmlStringFontColourFontStyle(item.Title, blackCat, commontypes.FontStyle_SUBTITLE_S),
				Subtitle: commontypes.GetTextFromHtmlStringFontColourFontStyle(item.Subtitle, platinumGrey, commontypes.FontStyle_BODY_XS),
			})
		}
	} else {
		benefitsList = []*feTieringPb.InActiveStateListView_ContainerView_BenefitList{
			{
				LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(plant3D, 48, 48),
				Title:    commontypes.GetTextFromStringFontColourFontStyle(zeroForexTitle, blackCat, commontypes.FontStyle_SUBTITLE_S),
				Subtitle: commontypes.GetTextFromStringFontColourFontStyle(zeroForexSubTitle, platinumGrey, commontypes.FontStyle_BODY_XS),
			},
			{
				LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(blackDebitCard3D, 48, 48),
				Title:    commontypes.GetTextFromStringFontColourFontStyle(tapPayAndEarnTitle, blackCat, commontypes.FontStyle_SUBTITLE_S),
				Subtitle: commontypes.GetTextFromStringFontColourFontStyle(tapPayAndEarnSubTitle, platinumGrey, commontypes.FontStyle_BODY_XS),
			},
		}
	}

	if data.GetTierToLoad() == tieringExternalPb.Tier_TIER_FI_INFINITE || data.GetTierToLoad().IsAaSalaryTier() {
		cashbackTitle := fmt.Sprintf(dcCashbackTitle, tieringAaSalHelper.TierToCashbackPercent[data.GetTierToLoad()])
		cashbackSubTitle := fmt.Sprintf(dcCashbackSubTitle, tieringAaSalHelper.TierToCashbackPercent[data.GetTierToLoad()])
		benefitsList = append(benefitsList, &feTieringPb.InActiveStateListView_ContainerView_BenefitList{
			LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(shoppingBag3D, 48, 48),
			Title:    commontypes.GetTextFromStringFontColourFontStyle(cashbackTitle, blackCat, commontypes.FontStyle_SUBTITLE_S),
			Subtitle: commontypes.GetTextFromStringFontColourFontStyle(cashbackSubTitle, platinumGrey, commontypes.FontStyle_BODY_XS),
		})
	}

	if data.GetTierToLoad() == tieringExternalPb.Tier_TIER_FI_PLUS {
		benefitsList = append(benefitsList, &feTieringPb.InActiveStateListView_ContainerView_BenefitList{
			LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(shoppingBag3D, 48, 48),
			Title:    commontypes.GetTextFromStringFontColourFontStyle(freeAtmWithdrawalTitle, blackCat, commontypes.FontStyle_SUBTITLE_S),
			Subtitle: commontypes.GetTextFromStringFontColourFontStyle(freeAtmWithdrawalSubTitle, platinumGrey, commontypes.FontStyle_BODY_XS),
		})
	}

	return &feTieringPb.BenefitsOptions_InActiveStateListView{
		InActiveStateListView: &feTieringPb.InActiveStateListView{
			TopIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(twoDebitCards, 64, 64),
			Title:   commontypes.GetTextFromStringFontColourFontStyle(inactiveStateTitle, blackCat, commontypes.FontStyle_SUBTITLE_M),
			Action: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(orderNow, white, commontypes.FontStyle_BUTTON_S)).
				WithDeeplink(data.GetDebitCardOrderChargesData().GetDeeplinkToOrderCard()).
				WithContainerPaddingSymmetrical(24, 12).
				WithContainer(32, 0, 19, fiGreen),
			ContainerView: &feTieringPb.InActiveStateListView_ContainerView{
				BackgroundColour: widgetPb.GetBlockBackgroundColour(aliceBlue),
				BenefitLists:     benefitsList,
				CornerRadius:     16,
			},
		},
	}, nil
}

// nolint:unparam
func getInactiveHealthInsuranceComponent(data *EarnedBenefitData) *feTieringPb.BenefitsOptions_InActiveStateListView {
	genderList := []types.Gender{types.Gender_MALE, types.Gender_FEMALE, types.Gender_OTHER}
	var genderOptions []*salaryScreenOptionsPb.OnsurityPolicyPurchaseInfoInputScreenOptions_DropdownSelectionInputOption_DropdownSelectionOptionComponent_Option
	for _, genderEl := range genderList {
		genderOptions = append(genderOptions,
			&salaryScreenOptionsPb.OnsurityPolicyPurchaseInfoInputScreenOptions_DropdownSelectionInputOption_DropdownSelectionOptionComponent_Option{
				DisplayText: commontypes.GetTextFromStringFontColourFontStyle(
					genderEl.String(),
					colors.ColorLead,
					commontypes.FontStyle_SUBTITLE_3,
				),
			},
		)
	}

	title := data.GetHealthInsuranceDetails().GetHealthInsuranceTitle()
	if title == "" {
		title = healthInsuranceInactiveTitle
	}

	return &feTieringPb.BenefitsOptions_InActiveStateListView{
		InActiveStateListView: &feTieringPb.InActiveStateListView{
			TopIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(firstAidBox, 64, 64),
			Title:   commontypes.GetTextFromStringFontColourFontStyle(title, blackCat, commontypes.FontStyle_SUBTITLE_M),
			Action: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(activateNow, white, commontypes.FontStyle_BUTTON_S)).
				WithDeeplink(data.GetHealthInsuranceDetails().GetNextActionDeeplink()).
				WithContainerPaddingSymmetrical(24, 12).
				WithContainer(32, 0, 19, fiGreen),
		},
	}
}

// nolint:unparam
func getActiveHealthInsuranceComponent(data *EarnedBenefitData) *feTieringPb.BenefitsOptions_MoreBenefits {
	return &feTieringPb.BenefitsOptions_MoreBenefits{MoreBenefits: &feTieringPb.MoreInfoView{
		IconTextComponents: []*uiPb.IconTextComponent{
			uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(healthInsuranceBenefits, black, commontypes.FontStyle_SUBTITLE_S)).
				WithContainer(64, 0, 16, white).
				WithContainerPadding(12, 16, 12, 16).
				WithLeftImagePadding(11).
				WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(grayPencilIcon, 24, 24)).
				WithDeeplink(data.GetHealthInsuranceDetails().GetNextActionDeeplink()),
		},
	}}

}

func getRetryViewComponent() *feTieringPb.BenefitsOptions_RetryView {
	return &feTieringPb.BenefitsOptions_RetryView{
		RetryView: &feTieringPb.RetryView{
			Icon:  commontypes.GetVisualElementFromUrlHeightAndWidth(thunderCloudSmall, 40, 40),
			Title: commontypes.GetTextFromStringFontColourFontStyle(loadFailedTitle, blackCat, commontypes.FontStyle_HEADLINE_S),
			Cta: uiPb.NewITC().
				WithTexts(commontypes.GetTextFromStringFontColourFontStyle(Retry, white, commontypes.FontStyle_BUTTON_S)).
				WithContainer(32, 0, 19, fiGreen).
				WithContainerPaddingSymmetrical(24, 12),
		},
	}
}

func getActiveDebitCardComponent(ctx context.Context, data *EarnedBenefitData) (*feTieringPb.BenefitsOptions_ActiveStateListView, error) {
	activeListView := &feTieringPb.ActiveStateListView{
		Info: uiPb.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(exploreDcOffers, royalPurple, commontypes.FontStyle_SUBTITLE_XS)).
			WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(purpleBag, 24, 24)).
			WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(rightChevronPurple, 24, 24)).
			WithContainerCornerRadius(16).
			WithContainerBackgroundColor(solitudePurple).
			WithContainerPaddingSymmetrical(16, 16).
			WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_DEBIT_CARD_OFFERS_HOME_SCREEN}),
	}

	dcBenefitsItem, err := getDcOrderBenefitItem(ctx, data)
	if err != nil {
		return nil, fmt.Errorf("failed to get dc order benefit item, %w", err)
	}
	if data.GetDebitCardOrderChargesData().GetTierAtOrderedTime() == data.GetTierToLoad() {
		activeListView.BenefitList = append(activeListView.GetBenefitList(), dcBenefitsItem)
	}
	if data.GetTieringEssentials().GetCurrentTier().IsSalaryTier() {
		amcBenefitItem, err2 := getAmcWaivedOffBenefitItem(ctx, data)
		if err2 != nil {
			return nil, fmt.Errorf("failed to get AMC waived off benefit item, %w", err2)
		}
		activeListView.BenefitList = append(activeListView.GetBenefitList(), amcBenefitItem)
	}
	forexBenefitItem, err := getForexRefundBenefitItem(ctx, data)
	if err != nil {
		return nil, fmt.Errorf("failed to get forex refund benefit item, %w", err)
	}
	activeListView.BenefitList = append(activeListView.GetBenefitList(), forexBenefitItem)

	return &feTieringPb.BenefitsOptions_ActiveStateListView{
		ActiveStateListView: activeListView,
	}, nil
}

func getDcOrderBenefitItem(ctx context.Context, data *EarnedBenefitData) (*feTieringPb.ActiveStateListView_BenefitList, error) {
	popupTitle, err := getDebitCardPopupTitle(data)
	if err != nil {
		return nil, fmt.Errorf("failed to get dc popup title, %w", err)
	}
	popupBody, err := getDebitCardPopupBody(data)
	if err != nil {
		return nil, fmt.Errorf("failed to get dc popup body, %w", err)
	}
	var dcOrderChargesTitle = uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(freeCardActiveTitle, blackCat, commontypes.FontStyle_SUBTITLE_S)).
		WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(infoIcon, 16, 16)).
		WithRightImagePadding(2).
		WithDeeplink(getInfoPopup(popupTitle, popupBody))

	var platform = epificontext.AppPlatformFromContext(ctx)
	var androidVersion = epificontext.AppVersionFromContext(ctx)
	if platform == commontypes.Platform_ANDROID && androidVersion >= 410 {
		var spans []*commontypes.Text_AnnotatedText_Span
		spans = append(spans,
			&commontypes.Text_AnnotatedText_Span{
				SpanContent: &commontypes.Text_AnnotatedText_Span_Text{
					Text: freeCardActiveTitle,
				},
			},
			&commontypes.Text_AnnotatedText_Span{
				SpanContent: &commontypes.Text_AnnotatedText_Span_Icon{
					Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(infoIcon, 16, 16),
				},
			},
		)
		dcOrderChargesTitle = uiPb.NewITC().
			WithTexts(
				&commontypes.Text{
					DisplayValue: &commontypes.Text_AnnotatedText_{
						AnnotatedText: &commontypes.Text_AnnotatedText{
							Spans: spans,
						},
					},
					FontColor: blackCat,
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			).
			WithDeeplink(getInfoPopup(popupTitle, popupBody))
	}
	dcOrderCharges := &feTieringPb.ActiveStateListView_BenefitList{
		LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(fiDebitCard, 64, 64),
		Title:    dcOrderChargesTitle,
	}

	if data.GetDebitCardOrderChargesData().GetDiscountedAmount() == money.ZeroINR().GetPb() ||
		!data_collector.IsMonthYearEqual(data_collector.GetMonthYear(data.GetDebitCardOrderChargesData().GetOrderedAt()), data_collector.GetMonthYear(time.Now())) {
		dcOrderCharges.RightView = &feTieringPb.ActiveStateListView_BenefitList_Icon{
			Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(fiGreenTick, 24, 24),
		}

		return dcOrderCharges, nil
	}

	actualCardCharge := money.ToDisplayStringWithPrecision(data.GetDebitCardOrderChargesData().GetActualCardCharge(), 0)
	userPaidCardCharge := money.ToDisplayStringWithPrecision(data.GetDebitCardOrderChargesData().GetUserPaidCharge(), 0)
	dcOrderCharges.RightView = &feTieringPb.ActiveStateListView_BenefitList_RightAmount_{
		RightAmount: &feTieringPb.ActiveStateListView_BenefitList_RightAmount{
			Amount:              commontypes.GetTextFromStringFontColourFontStyle(userPaidCardCharge, blackCat, commontypes.FontStyle_NUMBER_L),
			StrikethroughAmount: commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("<s>%s</s>", actualCardCharge), platinumGrey, commontypes.FontStyle_NUMBER_XS),
		},
	}

	return dcOrderCharges, nil
}

func getForexRefundBenefitItem(ctx context.Context, data *EarnedBenefitData) (*feTieringPb.ActiveStateListView_BenefitList, error) {
	tier := data.GetTierToLoad()

	// Check if popup title exists for the tier
	popupTitle, ok := tierToForexPopupTitle[tier]
	if !ok {
		logger.Error(ctx, "Forex popup title not found for tier", zap.String("tier", tier.String()))
		return nil, fmt.Errorf("forex popup title not found for tier %s", tier.String())
	}

	// Check if popup body exists for the tier
	popupBody, ok := tierToForexPopupBody[tier]
	if !ok {
		logger.Error(ctx, "Forex popup body not found for tier", zap.String("tier", tier.String()))
		return nil, fmt.Errorf("forex popup body not found for tier %s", tier.String())
	}

	var forexRefundTitle = uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(forexRefundActiveTitle, blackCat, commontypes.FontStyle_SUBTITLE_S)).
		WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(infoIcon, 16, 16)).
		WithRightImagePadding(2).
		WithDeeplink(getInfoPopup(popupTitle, popupBody))

	var platform = epificontext.AppPlatformFromContext(ctx)
	var androidVersion = epificontext.AppVersionFromContext(ctx)
	if platform == commontypes.Platform_ANDROID && androidVersion >= 410 {
		var spans []*commontypes.Text_AnnotatedText_Span
		spans = append(spans,
			&commontypes.Text_AnnotatedText_Span{
				SpanContent: &commontypes.Text_AnnotatedText_Span_Text{
					Text: forexRefundActiveTitle,
				},
			},
			&commontypes.Text_AnnotatedText_Span{
				SpanContent: &commontypes.Text_AnnotatedText_Span_Icon{
					Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(infoIcon, 16, 16),
				},
			},
		)
		forexRefundTitle = uiPb.NewITC().
			WithTexts(
				&commontypes.Text{
					DisplayValue: &commontypes.Text_AnnotatedText_{
						AnnotatedText: &commontypes.Text_AnnotatedText{
							Spans: spans,
						},
					},
					FontColor: blackCat,
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			).
			WithDeeplink(getInfoPopup(popupTitle, popupBody))
	}
	forexRefund := &feTieringPb.ActiveStateListView_BenefitList{
		LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(globe, 64, 64),
		Title:    forexRefundTitle,
	}

	forexRefundValue := money.ToDisplayStringWithPrecision(data.GetForexRefundAtTier(), 0)
	forexRefund.RightView = &feTieringPb.ActiveStateListView_BenefitList_RightAmount_{
		RightAmount: &feTieringPb.ActiveStateListView_BenefitList_RightAmount{
			Amount: commontypes.GetTextFromStringFontColourFontStyle(forexRefundValue, blackCat, commontypes.FontStyle_NUMBER_L),
		},
	}

	return forexRefund, nil
}

// nolint: dupl
func getAmcWaivedOffBenefitItem(ctx context.Context, data *EarnedBenefitData) (*feTieringPb.ActiveStateListView_BenefitList, error) {
	tier := data.GetTierToLoad()

	// Check if popup title exists for the tier
	popupTitle, ok := tierToAMCPopupTitle[tier]
	if !ok {
		logger.Error(ctx, "AMC popup title not found for tier", zap.String("tier", tier.String()))
		return nil, fmt.Errorf("AMC popup title not found for tier %s", tier.String())
	}

	// Check if popup body exists for the tier
	popupBody, ok := tierToAMCPopupBody[tier]
	if !ok {
		logger.Error(ctx, "AMC popup body not found for tier", zap.String("tier", tier.String()))
		return nil, fmt.Errorf("AMC popup body not found for tier %s", tier.String())
	}

	var AmcWaivedOfTitle = uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(dcAmcTitle, blackCat, commontypes.FontStyle_SUBTITLE_S)).
		WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(infoIcon, 16, 16)).
		WithRightImagePadding(2).
		WithDeeplink(getInfoPopup(popupTitle, popupBody))

	var platform = epificontext.AppPlatformFromContext(ctx)
	var androidVersion = epificontext.AppVersionFromContext(ctx)
	if platform == commontypes.Platform_ANDROID && androidVersion >= 410 {
		var spans []*commontypes.Text_AnnotatedText_Span
		spans = append(spans,
			&commontypes.Text_AnnotatedText_Span{
				SpanContent: &commontypes.Text_AnnotatedText_Span_Text{
					Text: dcAmcTitle,
				},
			},
			&commontypes.Text_AnnotatedText_Span{
				SpanContent: &commontypes.Text_AnnotatedText_Span_Icon{
					Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(infoIcon, 16, 16),
				},
			},
		)
		AmcWaivedOfTitle = uiPb.NewITC().
			WithTexts(
				&commontypes.Text{
					DisplayValue: &commontypes.Text_AnnotatedText_{
						AnnotatedText: &commontypes.Text_AnnotatedText{
							Spans: spans,
						},
					},
					FontColor: blackCat,
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			).
			WithDeeplink(getInfoPopup(popupTitle, popupBody))
	}
	amcWaivedOf := &feTieringPb.ActiveStateListView_BenefitList{
		LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(screwDriverSpanner, 64, 64),
		Title:    AmcWaivedOfTitle,
	}
	// Check if AMC value exists for the tier
	dcAmcValue, ok := tierToDCAMCValue[tier]
	if !ok {
		logger.Error(ctx, "AMC value not found for tier", zap.String("tier", tier.String()))
		return nil, fmt.Errorf("AMC value not found for tier %s", tier.String())
	}

	// Check if AMC value exists for the tier
	dcAmcStrikeThroughValue, ok := tierToDCAMCStrikeThroughValue[tier]
	if !ok {
		logger.Error(ctx, "AMC value not found for tier", zap.String("tier", tier.String()))
		return nil, fmt.Errorf("AMC value not found for tier %s", tier.String())
	}

	amcWaivedOf.RightView = &feTieringPb.ActiveStateListView_BenefitList_RightAmount_{
		RightAmount: &feTieringPb.ActiveStateListView_BenefitList_RightAmount{
			Amount: commontypes.GetTextFromStringFontColourFontStyle(dcAmcValue, blackCat, commontypes.FontStyle_NUMBER_L),
		},
	}
	if dcAmcStrikeThroughValue != "" {
		amcWaivedOf.GetRightAmount().StrikethroughAmount = commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("<s>%s</s>", dcAmcStrikeThroughValue), platinumGrey, commontypes.FontStyle_NUMBER_XS)
	}

	return amcWaivedOf, nil
}

func getInfoPopup(header, body string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
				InfoList: []*deeplinkPb.InformationPopupOptions_Info{
					{
						TextTitle: commontypes.GetTextFromStringFontColourFontStyle(header, blackCat, commontypes.FontStyle_SUBTITLE_2),
						BodyTexts: []*commontypes.Text{commontypes.GetTextFromHtmlStringFontColourFontStyle(body, platinumGrey, commontypes.FontStyle_BODY_3_PARA)},
					},
				},
				BgColor:       white,
				Icon:          commontypes.GetVisualElementFromUrlHeightAndWidth(popupInfoIcon, 48, 48),
				DisplayScreen: deeplinkPb.InformationPopupOptions_DISPLAY_SCREEN_TIERING_EARNED_BENEFITS,
			},
		},
	}
}

func getInactiveOtherBenefitsComponent(data *EarnedBenefitData) *feTieringPb.BenefitsOptions_InActiveStateListView {
	return &feTieringPb.BenefitsOptions_InActiveStateListView{
		InActiveStateListView: &feTieringPb.InActiveStateListView{
			TopIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(purpleChequeBook, 64, 64),
			Title:   commontypes.GetTextFromStringFontColourFontStyle(chequeBookInactiveTitle, blackCat, commontypes.FontStyle_SUBTITLE_M),
			Action: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(orderChequeBook, white, commontypes.FontStyle_BUTTON_S)).
				WithDeeplink(data.GetChequeBookOrderData().GetDeeplinkToOrderChequebook()).
				WithContainerPaddingSymmetrical(24, 12).
				WithContainer(32, 0, 19, fiGreen),
		},
	}
}

func getActiveOtherBenefitsComponent(data *EarnedBenefitData) *feTieringPb.BenefitsOptions_ActiveStateListView {
	activeListView := &feTieringPb.ActiveStateListView{}

	activeListView.BenefitList = append(activeListView.GetBenefitList(), getChequeBookBenefitItem(data))

	return &feTieringPb.BenefitsOptions_ActiveStateListView{
		ActiveStateListView: activeListView,
	}
}

func getChequeBookBenefitItem(data *EarnedBenefitData) *feTieringPb.ActiveStateListView_BenefitList {
	chequeBookCharges := &feTieringPb.ActiveStateListView_BenefitList{
		LeftIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(purpleChequeBook, 64, 64),
		Title:    uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(chequeBookBenefitItemTitle, blackCat, commontypes.FontStyle_SUBTITLE_S)),
	}

	if data.GetTierToLoad() != data.GetChequeBookOrderData().GetTierAtOrderTime() ||
		// show green tick if chequebook is ordered but refund has not been done
		data.GetChequeBookOrderData().GetHasOrderedChequeBook() && !data.GetChequeBookOrderData().GetHasGotRefund() {
		chequeBookCharges.RightView = &feTieringPb.ActiveStateListView_BenefitList_Icon{
			Icon: commontypes.GetVisualElementFromUrlHeightAndWidth(fiGreenTick, 24, 24),
		}
	} else {
		actualCardCharge := money.ToDisplayStringWithPrecision(data.GetChequeBookOrderData().GetChequeBookChargesAtOrderTime(), 0)
		userPaidCardCharge := money.ToDisplayStringWithPrecision(money.ZeroINR().GetPb(), 0)
		chequeBookCharges.RightView = &feTieringPb.ActiveStateListView_BenefitList_RightAmount_{
			RightAmount: &feTieringPb.ActiveStateListView_BenefitList_RightAmount{
				Amount:              commontypes.GetTextFromStringFontColourFontStyle(userPaidCardCharge, blackCat, commontypes.FontStyle_NUMBER_L),
				StrikethroughAmount: commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf("<s>%s</s>", actualCardCharge), platinumGrey, commontypes.FontStyle_NUMBER_XS),
			},
		}
	}

	return chequeBookCharges
}

func (c *ComponentBuilder) getItcCtaForUpgrade(data *EarnedBenefitData) (*uiPb.IconTextComponent, error) {
	cta := uiPb.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(upgradeNow, white, commontypes.FontStyle_BUTTON_S)).
		WithContainer(32, 0, 19, fiGreen).
		WithContainerPaddingSymmetrical(24, 12)

	higherTierToPitch := data.GetTierToPitch()

	upgradeDeeplink, getUpgradeCtaErr := c.tieringCtaManager.GetUpgradeCta(&feTieringCta.UpgradeCtaData{
		TieringPitchResp: data.GetTieringEssentials().GetTieringPitchResp(),
		ConfigParamsResp: data.GetTieringEssentials().GetGetConfigParamsResp(),
		UpgradeTier:      higherTierToPitch,
		EvaluatedTier:    data.GetEvaluatedTier(),
	})
	if getUpgradeCtaErr != nil {
		return nil, fmt.Errorf("failed to get upgrade cta, %w", getUpgradeCtaErr)
	}

	cta.WithDeeplink(upgradeDeeplink)
	return cta, nil
}

// nolint:funlen
func getCardsForPitchingHigherTier(data *EarnedBenefitData) (*feTieringPb.UpgradeBenefitsView_Card, *feTieringPb.UpgradeBenefitsView_Card, error) {
	p := message.NewPrinter(language.Hindi)

	leftCardIcon, leftCardBgColor, leftCardErr := getIconAndGradientForHigherRewardsComponent(data.GetTierToLoad())
	if leftCardErr != nil {
		return nil, nil, fmt.Errorf("failed to get icon and gradient for left card, %w", leftCardErr)
	}

	rightCardIcon, rightCardBgColor, rightCardErr := getIconAndGradientForHigherRewardsComponent(data.GetTierToPitch())
	if rightCardErr != nil {
		return nil, nil, fmt.Errorf("failed to get icon and gradient for right card, %w", rightCardErr)
	}

	var currTierFiCoins, currTierCash *uiPb.IconTextComponent
	var higherTierFiCoins, higherTierCash *uiPb.IconTextComponent
	if data.GetTierToPitch() == tieringExternalPb.Tier_TIER_FI_INFINITE {
		currTierFiCoinsStr := p.Sprintf("%v", int32(data.GetRewardsData().GetCurrMonthFiCoins()))
		currTierFiCoins = uiPb.NewITC().WithTexts(
			commontypes.GetTextFromStringFontColourFontStyle(currTierFiCoinsStr+accrualPkg.ReplaceCoinWithPointIfApplicable(fiCoins), white, commontypes.FontStyle_SUBTITLE_S),
		)

		currTierCashStr := money.ToDisplayStringWithPrecision(data.GetRewardsData().GetCurrMonthProjectedCash(), 0)
		currTierCash = uiPb.NewITC().WithTexts(
			commontypes.GetTextFromStringFontColourFontStyle(currTierCashStr+back, white, commontypes.FontStyle_SUBTITLE_S),
		)

		higherTierFiCoinsStr := p.Sprintf("%v", int32(data.GetRewardsData().GetCurrMonthEstFiCoinsInHigherTier()))
		higherTierFiCoins = uiPb.NewITC().WithTexts(
			commontypes.GetTextFromStringFontColourFontStyle(higherTierFiCoinsStr+accrualPkg.ReplaceCoinWithPointIfApplicable(fiCoins), white, commontypes.FontStyle_SUBTITLE_S),
		)

		higherTierCashStr := money.ToDisplayStringWithPrecision(data.GetRewardsData().GetCurrMonthEstCashInHigherTier(), 0)
		higherTierCash = uiPb.NewITC().WithTexts(
			commontypes.GetTextFromStringFontColourFontStyle(higherTierCashStr+back, white, commontypes.FontStyle_SUBTITLE_S),
		)
	}

	if data.GetTierToPitch().IsAaSalaryTier() {
		currTierCashStr := money.ToDisplayStringWithPrecision(data.GetRewardsData().GetCurrMonthProjectedCash(), 0)
		currTierCash = uiPb.NewITC().WithTexts(
			commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(haveEarned, currTierCashStr), white, commontypes.FontStyle_SUBTITLE_S),
		)

		higherTierCashStr := money.ToDisplayStringWithPrecision(data.GetRewardsData().GetCurrMonthEstCashInHigherTier(), 0)
		higherTierCash = uiPb.NewITC().WithTexts(
			commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(couldHaveEarned, higherTierCashStr), white, commontypes.FontStyle_SUBTITLE_S),
		)
	}

	leftCardTag, rightCardTag := getTagsForHigherRewardsComponent(data)

	leftCard := &feTieringPb.UpgradeBenefitsView_Card{
		Tag:              leftCardTag,
		VisualElement:    leftCardIcon,
		Cashback:         currTierCash,
		Coins:            currTierFiCoins,
		BackgroundColour: leftCardBgColor,
		CornerRadius:     20,
	}

	rightCard := &feTieringPb.UpgradeBenefitsView_Card{
		Tag:              rightCardTag,
		VisualElement:    rightCardIcon,
		Cashback:         higherTierCash,
		Coins:            higherTierFiCoins,
		BackgroundColour: rightCardBgColor,
		CornerRadius:     20,
	}

	return leftCard, rightCard, nil
}

func getTag(text, textColor, bgColor string) *uiPb.IconTextComponent {
	return uiPb.NewITC().
		WithContainerCornerRadius(11).
		WithContainerBackgroundColor(bgColor).
		WithContainerPaddingSymmetrical(8, 2).
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, textColor, commontypes.FontStyle_OVERLINE_2XS_CAPS))
}

func getIconAndGradientTier(tier tieringExternalPb.Tier) (*commontypes.VisualElement, *widgetPb.BackgroundColour, error) {
	var iconUrl, gradientLeftColor, gradientRightColor string
	switch tier {
	case tieringExternalPb.Tier_TIER_FI_PLUS:
		iconUrl = plusIconWithText
		gradientLeftColor = chestNutBrown
		gradientRightColor = tanBrown
	case tieringExternalPb.Tier_TIER_FI_INFINITE:
		iconUrl = infiniteIconWithText
		gradientLeftColor = airForceBlue
		gradientRightColor = hawkesBlue
	case tieringExternalPb.Tier_TIER_FI_SALARY, tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:
		iconUrl = salaryIconWithTextDark
		gradientLeftColor = saddleBrown
		gradientRightColor = creamyYellow
	default:
		if tier.IsAaSalaryTier() {
			iconUrl = aaSalaryIconWithText
			gradientLeftColor = berryGradientLeft
			gradientRightColor = berryGradientRight
		} else {
			return nil, nil, fmt.Errorf("icon and gradient color not configured for %s tier", tier.String())
		}
	}

	return commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, 50, 68), widgetPb.GetLinearGradientBackgroundColour(45, []*widgetPb.ColorStop{
		{
			Color:          gradientLeftColor,
			StopPercentage: 0,
		},
		{
			Color:          gradientRightColor,
			StopPercentage: 80,
		},
	}), nil
}

func getIconAndGradientForHigherRewardsComponent(tier tieringExternalPb.Tier) (*commontypes.VisualElement, *widgetPb.BackgroundColour, error) {
	icon, gradient, err := getIconAndGradientTier(tier)
	if err != nil {
		return nil, nil, err
	}

	switch tier {
	case tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_1:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(twoPercentCashback3dSilver, 50, 68), gradient, nil
	case tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_2:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(twoPercentCashback3dSilver, 50, 68), gradient, nil
	case tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(threePercentCashback3dSilver, 50, 68), gradient, nil
	default:
		return icon, gradient, nil
	}
}

func getPlanInfo(iconUrl, title string, deeplink *deeplinkPb.Deeplink) *uiPb.IconTextComponent {
	return uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(title, black, commontypes.FontStyle_SUBTITLE_S)).
		WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(iconUrl, 40, 40)).
		WithContainer(64, 0, 16, white).
		WithContainerPadding(12, 16, 12, 16).
		WithLeftImagePadding(11).
		WithRightVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(platinumGreyChevron, 24, 24)).
		WithDeeplink(deeplink)
}

func getTierIconAndTitleForMoreInfo(data *EarnedBenefitData) (string, string, error) {
	tier := data.GetTierToLoad()
	dispName, getDispNameErr := displayNames.GetTitleCaseDisplayString(tier)
	if getDispNameErr != nil {
		return "", "", fmt.Errorf("failed to get display name, %w", getDispNameErr)
	}

	title := fmt.Sprintf(tierBenefitsText, dispName)

	var iconUrl string
	switch tier {
	case tieringExternalPb.Tier_TIER_FI_PLUS:
		iconUrl = plusIconSmall
	case tieringExternalPb.Tier_TIER_FI_INFINITE:
		iconUrl = infiniteIconSmall
	case tieringExternalPb.Tier_TIER_FI_SALARY:
		iconUrl = salaryIconSmall
	case tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:
		iconUrl = salaryBasicIconSmall
	default:
		if tier.IsAaSalaryTier() {
			iconUrl = aaSalaryIconSmall
		} else {
			return "", "", fmt.Errorf("icon and title not configured for %s tier", tier.String())
		}
	}

	return title, iconUrl, nil
}

func getScreenBgColor(tier tieringExternalPb.Tier) (*widgetPb.BackgroundColour, string, error) {
	_, gradientBg, _ := getIconAndGradientTier(tier)

	switch tier {
	case tieringExternalPb.Tier_TIER_FI_PLUS:
		return gradientBg, coffeeBrown, nil
	case tieringExternalPb.Tier_TIER_FI_INFINITE:
		return gradientBg, nepalBlue, nil
	case tieringExternalPb.Tier_TIER_FI_SALARY, tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:
		return gradientBg, creamyYellow, nil
	default:
		if tier.IsAaSalaryTier() {
			return gradientBg, berry900, nil
		}

		return nil, "", fmt.Errorf("screen background color not configured for %s tier", tier.String())
	}
}

func getHeaderIcon(tier tieringExternalPb.Tier, height int32, width int32) (*commontypes.VisualElement, error) {
	switch tier {
	case tieringExternalPb.Tier_TIER_FI_PLUS:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(plusIconWithTextEarnedHistory, height, width), nil
	case tieringExternalPb.Tier_TIER_FI_INFINITE:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(infiniteIconWithTextEarnedHistory, height, width), nil
	case tieringExternalPb.Tier_TIER_FI_SALARY:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(salaryIconWithTextWhite, height, width), nil
	case tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(salaryBasicIconWithText, height, width), nil
	default:
		if tier.IsAaSalaryTier() {
			return commontypes.GetVisualElementFromUrlHeightAndWidth(aaSalaryIconWithText, height, width), nil
		}

		return nil, fmt.Errorf("header icon not configured for %s tier", tier.String())
	}
}

func getHeaderIconForHistoryScreen(tier tieringExternalPb.Tier, height int32, width int32) (*commontypes.VisualElement, error) {
	switch tier {
	case tieringExternalPb.Tier_TIER_FI_PLUS:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(plusIconWithTextEarnedHistory, height, width), nil
	case tieringExternalPb.Tier_TIER_FI_INFINITE:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(infiniteIconWithTextEarnedHistory, height, width), nil
	case tieringExternalPb.Tier_TIER_FI_SALARY:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(salaryIconWithTextDark, height, width), nil
	case tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:
		return commontypes.GetVisualElementFromUrlHeightAndWidth(salaryBasicIconWithTextDark, height, width), nil
	default:
		if tier.IsAaSalaryTier() {
			return commontypes.GetVisualElementFromUrlHeightAndWidth(aaSalaryIconWithTextDark, height, width), nil
		}

		return nil, fmt.Errorf("header icon not configured for %s tier", tier.String())
	}
}

func getNavBarTitle(tier tieringExternalPb.Tier) (*commontypes.Text, error) {
	tierDispName, dispStringErr := displayNames.GetTitleCaseDisplayString(tier)
	if dispStringErr != nil {
		return nil, fmt.Errorf("failed to get display name for tier, %w", dispStringErr)
	}

	titleText := fmt.Sprintf(navigationBarTitle, tierDispName)

	return commontypes.GetTextFromStringFontColourFontStyle(titleText, blackCat, commontypes.FontStyle_HEADLINE_M), nil
}

func GetNavBarTitleForHistory(tier tieringExternalPb.Tier) (*commontypes.Text, error) {
	tierDispName, dispStringErr := displayNames.GetTitleCaseDisplayString(tier)
	if dispStringErr != nil {
		return nil, fmt.Errorf("failed to get display name for tier, %w", dispStringErr)
	}

	titleText := fmt.Sprintf("%s lifetime benefits", tierDispName)

	resp := commontypes.GetTextFromStringFontColourFontStyle(titleText, blackCat, commontypes.FontStyle_HEADLINE_M)
	resp.BgColor = neutralFog200
	return resp, nil
}

func GetEarnedBenefitsScreenOption(tier tieringExternalPb.Tier) (*tieringScreenOptionsPb.EarnedBenefitScreenOptions, error) {
	navBarTitle, getNavBarErr := getNavBarTitle(tier)
	if getNavBarErr != nil {
		return nil, fmt.Errorf("failed to get nav bar title, %w", getNavBarErr)
	}

	headerBgColor, shimmerHeaderColor, getBgColorErr := getScreenBgColor(tier)
	if getBgColorErr != nil {
		return nil, fmt.Errorf("failed to get screen bg color, %w", getBgColorErr)
	}

	headerIcon, getHeaderIconErr := getHeaderIcon(tier, 108, 156)
	if getHeaderIconErr != nil {
		return nil, fmt.Errorf("failed to get header icon, %w", getHeaderIconErr)
	}

	return &tieringScreenOptionsPb.EarnedBenefitScreenOptions{
		HeaderBgColor:        headerBgColor,
		HeaderVisualElement:  headerIcon,
		PageBackgroundColour: widgetPb.GetBlockBackgroundColour("#E6E9ED"),
		ShimmerHeaderColor:   shimmerHeaderColor,
		NavigationBarTitle:   navBarTitle,
		TierIdentifier:       tier.String(),
	}, nil
}

func GetEarnedBenefitsDeeplink(tier tieringExternalPb.Tier) (*deeplinkPb.Deeplink, error) {
	screenOption, getScreenOptErr := GetEarnedBenefitsScreenOption(tier)
	if getScreenOptErr != nil {
		return nil, fmt.Errorf("failed to get earned benefits screen options, %w", getScreenOptErr)
	}

	dl, getDlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_TIERING_EARNED_BENEFIT_SCREEN, screenOption)
	if getDlErr != nil {
		return nil, fmt.Errorf("failed to get dl for earned benefits screen, %w", getDlErr)
	}

	return dl, nil
}

func getCelebPopupInfoComponent(text, leftIconUrl, leftIconContainerColor string) *popupPb.CelebrationPopupScreenOptions_InfoComponent {
	return &popupPb.CelebrationPopupScreenOptions_InfoComponent{
		LeftIcon: uiPb.NewITC().
			WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(leftIconUrl, 24, 24)).
			WithContainer(48, 48, 20, leftIconContainerColor).
			WithContainerPaddingSymmetrical(12, 12),
		InfoText: commontypes.GetTextFromStringFontColourFontStyle(text, softPastelBlue, commontypes.FontStyle_SUBTITLE_M),
	}
}

// nolint:gocritic
func (c *ComponentBuilder) getInfoListsForEarnedBenefitHistoryInfoPopup(tier tieringExternalPb.Tier) []*deeplinkPb.InformationPopupOptions_Info {
	var infoList []*deeplinkPb.InformationPopupOptions_Info

	multiplierText, ok := tierToMultiplierMap[tier]
	if !ok {
		return infoList
	}

	var bodyTexts []*commontypes.Text
	if time.Now().Before(time.Date(2025, 3, 1, 0, 0, 0, 0, datetime.IST)) && !cfg.IsNonProdEnv(c.gconf.Application().Environment) {
		fiCoinsRewardText := accrualPkg.ReplaceCoinWithPointIfApplicable(fmt.Sprintf(totalBenefitsCalcuatedSubtext, multiplierText))
		bodyTexts = []*commontypes.Text{
			commontypes.GetTextFromHtmlStringFontColourFontStyle(fiCoinsRewardText, platinumGrey, commontypes.FontStyle_BODY_3_PARA),
			commontypes.GetTextFromHtmlStringFontColourFontStyle(monthlyPopupSubTitle, platinumGrey, commontypes.FontStyle_BODY_3_PARA),
		}
	} else {
		var conversionString string
		if time.Now().After(accrualPkg.GetFiCoinsToFiPointsMigrationTime()) {
			// Post-migration rule: "4 Fi-Points = ₹1"
			pointsPerRupee := int(1 / rewardsPkg.FiPointsToCashConversionRatio)
			conversionString = fmt.Sprintf("%d Fi-Points = ₹1", pointsPerRupee)
		} else {
			// Pre-migration rule: "100 Fi-Coins = ₹2"
			rupeesPer100Coins := int(100 * rewardsPkg.FiCoinsToCashConversionRatioForNonCCUser)
			conversionString = fmt.Sprintf("100 Fi-Coins = ₹%d", rupeesPer100Coins)
		}

		if tier == tieringExternalPb.Tier_TIER_FI_SALARY {
			bodyTexts = []*commontypes.Text{
				commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf(totalWorthCalcuatedSubtextForSalary, tierToValueBackMap[tier], conversionString), platinumGrey, commontypes.FontStyle_BODY_3_PARA),
			}
		} else if tier == tieringExternalPb.Tier_TIER_FI_SALARY_BASIC {
			bodyTexts = []*commontypes.Text{
				commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf(totalWorthCalcuatedSubtextForSalaryBasic, tierToValueBackMap[tier], conversionString), platinumGrey, commontypes.FontStyle_BODY_3_PARA),
			}
		} else {
			bodyTexts = []*commontypes.Text{
				commontypes.GetTextFromHtmlStringFontColourFontStyle(fmt.Sprintf(totalWorthCalcuatedSubtext, tierToValueBackMap[tier], conversionString), platinumGrey, commontypes.FontStyle_BODY_3_PARA),
			}
		}
	}

	infoList = append(infoList, &deeplinkPb.InformationPopupOptions_Info{
		TextTitle: commontypes.GetTextFromStringFontColourFontStyle(totalBenefitsCalcuatedHeader, blackCat, commontypes.FontStyle_SUBTITLE_2),
		BodyTexts: bodyTexts,
	})

	return infoList
}

func (c *ComponentBuilder) getTotalBenefitsValueInfoPopup(tier tieringExternalPb.Tier) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
				Icon:          commontypes.GetVisualElementFromUrlHeightAndWidth(popupInfoIcon, 48, 48),
				BgColor:       white,
				InfoList:      c.getInfoListsForEarnedBenefitHistoryInfoPopup(tier),
				DividerColor:  neutralFog200,
				DisplayScreen: deeplinkPb.InformationPopupOptions_DISPLAY_SCREEN_TIERING_EARNED_BENEFITS,
			},
		},
	}
}

func getHeaderOfMonthlyEarnedView(monthYear *data_collector.MonthYear) *feTieringPb.MonthlyRewardEarnedView_HeaderView {
	resp := &feTieringPb.MonthlyRewardEarnedView_HeaderView{}
	resp.BackgroundColour = widgetPb.GetBlockBackgroundColour(aliceBlue)
	titleText := monthYear.GetStartOfMonth().Format(monthYearFormat)
	resp.Title = commontypes.GetTextFromStringFontColourFontStyle(titleText, blackCat, commontypes.FontStyle_SUBTITLE_M)

	if monthYear.GetYear() == time.Now().Year() && monthYear.GetMonth() == time.Now().Month() {
		resp.Tag = uiPb.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle("ONGOING", black, commontypes.FontStyle_OVERLINE_2XS_CAPS)).
			WithContainer(64, 16, 11, paleGreen).
			WithContainerPadding(2, 8, 2, 8)
	}

	return resp
}

func getBenefitCardItemsForMonthlyReward(tier tieringExternalPb.Tier, data *monthlyBenefitsDisplayData) []*feTieringPb.MonthlyRewardEarnedView_BenefitCardItem {
	var cardItems []*feTieringPb.MonthlyRewardEarnedView_BenefitCardItem
	p := message.NewPrinter(language.Hindi)

	fiCoinItem := &feTieringPb.MonthlyRewardEarnedView_BenefitCardItem{
		Title: commontypes.GetTextFromStringFontColourFontStyle(accrualPkg.ReplaceCoinWithPointIfApplicable("Fi-COINS EARNED"), platinumGrey, commontypes.FontStyle_OVERLINE_2XS_CAPS),
		Value: uiPb.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(p.Sprintf("%v", data.GetFiCoinsEarned()), blackCat, commontypes.FontStyle_NUMBER_M)).
			WithLeftVisualElementUrlHeightAndWidth(fiCoinsIconGreen, 12, 12).
			WithLeftImagePadding(2),
	}
	cardItems = append(cardItems, fiCoinItem)

	feesSavedItem := &feTieringPb.MonthlyRewardEarnedView_BenefitCardItem{
		Title: commontypes.GetTextFromStringFontColourFontStyle("FEES SAVED", platinumGrey, commontypes.FontStyle_OVERLINE_2XS_CAPS),
		Value: uiPb.NewITC().
			WithTexts(commontypes.GetTextFromStringFontColourFontStyle(money.ToDisplayStringWithPrecision(data.GetFeesSaved(), 0), blackCat, commontypes.FontStyle_NUMBER_M)),
	}
	cardItems = append(cardItems, feesSavedItem)

	return cardItems
}

func (c *ComponentBuilder) shouldShowRewardRockstarPopup(data *EarnedBenefitData) bool {
	if data.GetTieringEssentials().GetIsMultipleWaysToEnterTieringEnabledForActor() {
		return false
	}

	if cfg.IsNonProdEnv(c.gconf.Application().Environment) {
		return true
	}

	// show only after 5th of the month since prev month reward is credited on 5th
	if time.Now().Day() <= 5 {
		return false
	}

	if money.IsPositive(data.GetRewardsData().GetCurrMonthRealisedCash()) && data.GetTierToLoad().IsCashbackEligibleTier() {
		return true
	}

	if data.GetRewardsData().GetPrevMonthFiCoins() > 0 {
		return true
	}

	return false
}

var allEarnedBenefitComponents = []feTieringPb.BenefitsType{
	feTieringPb.BenefitsType_BENEFITS_TYPE_DEBIT_CARD,
	feTieringPb.BenefitsType_BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW,
	feTieringPb.BenefitsType_BENEFITS_TYPE_MORE_INFO_VIEW,
	feTieringPb.BenefitsType_BENEFITS_TYPE_MONTHLY_VIEW,
	feTieringPb.BenefitsType_BENEFITS_TYPE_OTHER,
}

func GetComponentsToLoadForEarnedBenefitsScreen(requestedComponents []string) map[feTieringPb.BenefitsType]bool {
	componentsToLoad := make(map[feTieringPb.BenefitsType]bool)
	if len(requestedComponents) != 0 {
		for _, component := range requestedComponents {
			componentEnum := feTieringPb.BenefitsType(feTieringPb.BenefitsType_value[component])
			componentsToLoad[componentEnum] = true
		}

		return componentsToLoad
	}

	for _, componentEnum := range allEarnedBenefitComponents {
		componentsToLoad[componentEnum] = true
	}

	return componentsToLoad
}

func GetItcCtaForEarnedBenefits(text string, dl *deeplinkPb.Deeplink) *uiPb.IconTextComponent {
	return uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, white, commontypes.FontStyle_BUTTON_S)).
		WithDeeplink(dl).
		WithContainerPaddingSymmetrical(24, 12).
		WithContainer(32, 0, 19, fiGreen)
}

func (c *ComponentBuilder) getAaSalaryRightCtaAndInfoView(data *EarnedBenefitData) (rightCta, infoView *uiPb.IconTextComponent, priority uint32, getAaSalaryRightCtaAndInfoViewErr error) {
	transferWindowEndTime := data.GetAaSalaryData().GetUpcomingTransferCriteria().GetTransferWindowEndTime().AsTime()
	transferWindowEndDay := transferWindowEndTime.Day()

	rewardsBackPercent, ok := tieringAaSalHelper.TierToCashbackPercent[data.GetTierToLoad()]
	if !ok {
		return nil, nil, 0, fmt.Errorf("cashback percent not available for %s", data.GetTierToLoad().String())
	}

	rightCta = getTag(completedCaps, hunterGreen, paleGreen)
	infoView = getInfoViewComponent(fmt.Sprintf(AddFundsToEarnBackMessage, integer.GetOrdinalSuffix(transferWindowEndDay), rewardsBackPercent), tableCalender, amber50, nil)

	return rightCta, infoView, 0, nil
}

func getInfoViewComponent(infoText, leftIconUrl, bgColor string, dl *deeplinkPb.Deeplink) *uiPb.IconTextComponent {
	return uiPb.NewITC().WithContainerCornerRadius(16).
		WithContainerPadding(8, 16, 8, 8).
		WithLeftImagePadding(4).
		WithContainerBackgroundColor(bgColor).
		WithTexts(
			commontypes.GetTextFromStringFontColourFontStyle(infoText, platinumGrey, commontypes.FontStyle_SUBTITLE_XS),
		).
		WithDeeplink(dl).
		WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth(leftIconUrl, 24, 24))
}

func shouldShowHigherRewardsComponent(data *EarnedBenefitData, componentsToLoad map[feTieringPb.BenefitsType]bool) bool {
	if !componentsToLoad[feTieringPb.BenefitsType_BENEFITS_TYPE_UPGRADE_BENEFIT_VIEW] {
		return false
	}

	if data.GetTierToPitch() == tieringExternalPb.Tier_TIER_UNSPECIFIED {
		return false
	}

	if data.GetTierToLoad() == tieringExternalPb.Tier_TIER_FI_INFINITE {
		return false
	}

	if data.GetTierToLoad().IsSalaryTier() {
		return false
	}

	if data.GetTierToLoad().IsAaSalaryTier() {
		return false
	}

	if data.GetTierToPitch().IsAaSalaryTier() {
		return true
	}

	return true
}

func getTagsForHigherRewardsComponent(data *EarnedBenefitData) (leftCardTag, rightCardTag *uiPb.IconTextComponent) {
	if data.GetTierToLoad().IsAaSalaryTier() && data.GetTierToPitch() == tieringExternalPb.Tier_TIER_FI_SALARY {
		return nil, getTag(popularCaps, hunterGreen, paleGreen)
	}

	if data.GetTierToLoad().IsSalaryRelatedTier() && data.GetTierToPitch().IsSalaryRelatedTier() {
		return getTag(currentCaps, platinumGrey, chalk100), nil
	}

	return nil, getTag(recommended, hunterGreen, paleGreen)
}

func (c *ComponentBuilder) getAASalaryTransferView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error) {
	var (
		infoView  *uiPb.IconTextComponent
		rightCta  *uiPb.IconTextComponent
		amountITC *uiPb.IconTextComponent
		priority  = uint32(60)
	)

	if data.GetAaSalaryData() == nil {
		return nil, nil
	}

	// skip showing transfer view for users excluded for aa salary criteria
	var isActorPartOfAaSalaryExcludedSegments bool
	for _, segment := range data.GetTieringEssentials().GetGetConfigParamsResp().GetCriteriaSegmentExclusionConfigs().GetAaSalaryExcludedSegments() {
		if data.GetSegmentMembershipMap()[segment].GetIsActorMember() {
			isActorPartOfAaSalaryExcludedSegments = true
			break
		}
	}

	if isActorPartOfAaSalaryExcludedSegments {
		return nil, nil
	}

	if !lo.Contains([]enums2.AaSalaryStage{
		enums2.AaSalaryStage_AA_SALARY_STAGE_ACTIVATED,
		enums2.AaSalaryStage_AA_SALARY_STAGE_ACTIVATED_IN_GRACE,
		enums2.AaSalaryStage_AA_SALARY_STAGE_DEACTIVATED,
		enums2.AaSalaryStage_AA_SALARY_STAGE_AMOUNT_TRANSFERRED,
	},
		data.GetAaSalaryData().GetDetailsResp().GetCurrentStage()) {
		return nil, nil
	}

	transferWindowStartTime := data.GetAaSalaryData().GetUpcomingTransferCriteria().GetTransferWindowStartTime().AsTime()
	transferWindowEndTime := data.GetAaSalaryData().GetUpcomingTransferCriteria().GetTransferWindowEndTime().AsTime()
	transferWindowEndDay := transferWindowEndTime.Day()
	daysSinceTransferWindowStart := int32(time.Now().Sub(transferWindowStartTime).Hours()) / 24
	committedSalary := money.ToDisplayStringWithPrecision(data.GetAaSalaryData().GetLatestSalTxnVerificationReq().GetSalaryAmountCommitted(), 0)

	dueDaysForRewardActivation, getDueDaysErr := tieringAaSalHelper.GetAaSalDueDaysForRewardActivation(data.GetAaSalaryData().GetUpcomingTransferCriteria(), data.GetAaSalaryData().GetLatestActivation())
	if getDueDaysErr != nil {
		return nil, fmt.Errorf("failed to get due days for reward activation, %w", getDueDaysErr)
	}
	rewardsBackPercent, ok := tieringAaSalHelper.TierToCashbackPercent[data.GetTierToLoad()]
	if !ok {
		return nil, fmt.Errorf("cashback percent not available for %s", data.GetTierToLoad().String())
	}
	rightCta = getRightCta(transferNow, warningErrorIcon, lightYellow, darkYellow, aaSalaryLandingPage())
	infoView = getInfoViewITC(fmt.Sprintf(AddFundsToEarnBackMessage, integer.GetOrdinalSuffix(transferWindowEndDay), rewardsBackPercent), tableCalender, paleGreen, hunterGreen, aaSalaryLandingPage()).WithRightVisualElementUrlHeightAndWidth(arrowIcon, 24, 24)
	switch {
	case dueDaysForRewardActivation < 0, dueDaysForRewardActivation == 1, dueDaysForRewardActivation <= 5, dueDaysForRewardActivation <= 10, daysSinceTransferWindowStart > 0:
		// do nothing
	default:
		rightCta = getRightCta(completedCaps, checkCircleIcon, paleGreen, hunterGreen, aaSalaryLandingPage())
	}

	amountITC = getAmountITC(bankTransferIcon, committedSalary, blackCat)
	return getTransferSalaryView(priority, infoView, rightCta, amountITC, addFundsSATitle, lightGreen, paleGreen), nil
}

//nolint:dupl
func (c *ComponentBuilder) getUSStocksTransferView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error) {
	tierToLoad := data.GetTierToLoad()
	if !IsTierAllowedForEarnedBenefitsScreen(tierToLoad) {
		return nil, nil
	}
	var (
		infoView  *uiPb.IconTextComponent
		rightCta  *uiPb.IconTextComponent
		amountITC *uiPb.IconTextComponent
		priority  = uint32(60)
	)

	isUserInGrace := data.GetTieringEssentials().GetIsUserInGrace()
	isUserDowngraded := data.GetTieringEssentials().GetIsUserInDowngradedWindow()
	tierCriteriaMinValuesMap := data.GetTieringEssentials().GetTierCriteriaMinValuesMap()
	amount := getAmountFromTierAndCriteria(tierToLoad, tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC, tierCriteriaMinValuesMap)

	if amount == nil {
		return nil, feTieringErrors.ErrTierHasNoMinBalanceCriteria
	}

	amountString := money.ToDisplayStringWithSuffixAndPrecisionV2(amount, true, true, 2, false, money.IndianNumberSystem)

	switch {
	case isUserInGrace, isUserDowngraded:
		deeplink, _ := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_USSTOCKS_WALLET_ADD_FUNDS_SCREEN, getScreenOptionsForUSStocksAddFunds(tierToLoad))
		cashbackPercent := tieringAaSalHelper.TierToCashbackPercent[tierToLoad]
		infoView = getInfoViewITC(fmt.Sprintf(topUpUSStocksWalletText, cashbackPercent), lightningIcon, lightPeriwinkle, coolBlack, deeplink).WithRightVisualElementUrlHeightAndWidth(arrowIcon, 24, 24)
		rightCta = getRightCta(pendingCaps, warningErrorIcon, lightYellow, darkYellow, deeplink)
	default:
		infoView = getInfoViewITC(investEveryMonthToEarnCashback, addMoneyIcon, lightPeriwinkle, coolBlack, nil)
		rightCta = getRightCta(completedCaps, checkCircleUSStocksIcon, lightPeriwinkle, coolBlack, nil)
	}

	amountITC = getAmountITC(usDollarIcon, amountString, blackCat)
	return getTransferSalaryView(priority, infoView, rightCta, amountITC, investInUSStocks, brightGray, lightPeriwinkle), nil
}

//nolint:dupl
func (c *ComponentBuilder) getDepositsTransferSalaryView(data *EarnedBenefitData) (*feTieringPb.BenefitsOptions, error) {
	tierToLoad := data.GetTierToLoad()
	if !IsTierAllowedForEarnedBenefitsScreen(tierToLoad) {
		return nil, nil
	}
	var (
		infoView  *uiPb.IconTextComponent
		rightCta  *uiPb.IconTextComponent
		amountITC *uiPb.IconTextComponent
		priority  = uint32(60)
	)

	isUserInGrace := data.GetTieringEssentials().GetIsUserInGrace()
	isUserDowngraded := data.GetTieringEssentials().GetIsUserInDowngradedWindow()
	tierCriteriaMinValuesMap := data.GetTieringEssentials().GetTierCriteriaMinValuesMap()
	amount := getAmountFromTierAndCriteria(tierToLoad, tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC, tierCriteriaMinValuesMap)

	if amount == nil {
		return nil, feTieringErrors.ErrTierHasNoMinBalanceCriteria
	}

	amountString := money.ToDisplayStringWithSuffixAndPrecisionV2(amount, true, true, 2, false, money.IndianNumberSystem)

	switch {
	case isUserInGrace, isUserDowngraded:
		deeplink := &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_DEPOSIT_TEMPLATES_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_DepositTemplatesScreenOptions{
				DepositTemplatesScreenOptions: &deeplinkPb.DepositTemplatesScreenOptions{
					DepositType: accounts.Type_FIXED_DEPOSIT,
				},
			},
		}
		cashbackPercent := tieringAaSalHelper.TierToCashbackPercent[tierToLoad]
		infoView = getInfoViewITC(fmt.Sprintf(renewFDText, cashbackPercent), lightningIcon, blueHaze, berry900, deeplink).WithRightVisualElementUrlHeightAndWidth(arrowIcon, 24, 24)
		rightCta = getRightCta(pendingCaps, warningErrorIcon, lightYellow, darkYellow, deeplink)
	default:
		infoView = getInfoViewITC(keepYourFDReady, addMoneyIcon, blueHaze, berry900, nil)
		rightCta = getRightCta(completedCaps, checkCircleDepositIcon, blueHaze, berry900, nil)
	}

	amountITC = getAmountITC(lockerSign, amountString, blackCat)
	return getTransferSalaryView(priority, infoView, rightCta, amountITC, maintainFixedDeposit, solitudePurple, blueHaze), nil
}

func getAmountFromTierAndCriteria(tierToLoad tieringExternalPb.Tier, criteria tieringEnumPb.CriteriaOptionType, tierCriteriaMinValuesMap helper.TierCriteriaMinValuesMap) *gmoney.Money {
	for _, criteriaMinValue := range tierCriteriaMinValuesMap[tierToLoad] {
		if criteriaMinValue.Criteria == criteria {
			return criteriaMinValue.MinValue
		}
	}
	return nil
}

func getRightCta(text, leftIcon, bgColor, fontColor string, dl *deeplinkPb.Deeplink) *uiPb.IconTextComponent {
	return uiPb.NewITC().
		WithLeftVisualElementUrlHeightAndWidth(leftIcon, 16, 16).
		WithLeftImagePadding(2).
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(text, fontColor, commontypes.FontStyle_SUBTITLE_XS)).
		WithContainerPadding(4, 8, 4, 8).
		WithContainerCornerRadius(12).
		WithContainerBackgroundColor(bgColor).
		WithDeeplink(dl)
}

func getAmountITC(leftIcon, amount, fontColor string) *uiPb.IconTextComponent {
	return uiPb.NewITC().
		WithLeftVisualElementUrlHeightAndWidth(leftIcon, 32, 32).
		WithLeftImagePadding(10).
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(amount, fontColor, commontypes.FontStyle_SUBTITLE_L))
}

func getInfoViewITC(infoText, leftIcon, bgColor, fontColor string, dl *deeplinkPb.Deeplink) *uiPb.IconTextComponent {
	return uiPb.NewITC().
		WithLeftVisualElementUrlHeightAndWidth(leftIcon, 24, 24).
		WithLeftImagePadding(8).
		WithContainerPaddingSymmetrical(8, 8).
		WithContainerCornerRadius(16).
		WithContainerBackgroundColor(bgColor).
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(infoText, fontColor, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)
}

func headerTitleViewV2(title, bgColor string) *feTieringPb.TitleView {
	return &feTieringPb.TitleView{
		Title:            commontypes.GetTextFromStringFontColourFontStyle(title, blackCat, commontypes.FontStyle_SUBTITLE_M),
		BackgroundColour: widget.GetBlockBackgroundColour(bgColor),
	}
}

//nolint:unused
func getTransferSalaryView(priority uint32, infoView, rightCta, amountITC *uiPb.IconTextComponent, title, bgColor, headerTitleBgColor string) *feTieringPb.BenefitsOptions {
	return &feTieringPb.BenefitsOptions{
		Title:            commontypes.GetTextFromStringFontColourFontStyle(title, blackCat, commontypes.FontStyle_SUBTITLE_M),
		CornerRadius:     20,
		ShouldGrayscale:  false,
		BackgroundColour: widget.GetBlockBackgroundColour(bgColor),
		BenefitType:      feTieringPb.BenefitsType_BENEFITS_TYPE_MONTHLY_TRANSFER_VIEW.String(),
		Priority:         priority,
		Option: &feTieringPb.BenefitsOptions_TransferSalaryView{
			TransferSalaryView: &feTieringPb.TransferSalaryView{
				HeaderView:              headerTitleViewV2(title, headerTitleBgColor),
				CornerRadius:            20,
				BackgroundColour:        widget.GetBlockBackgroundColour(bgColor),
				InfoView:                infoView,
				RightCta:                rightCta,
				AmountIconTextComponent: amountITC,
			},
		},
	}
}

func getScreenOptionsForUSStocksAddFunds(tier tieringExternalPb.Tier) *usStocksDlOptions.USStocksAddFundsScreenOptions {
	return &usStocksDlOptions.USStocksAddFundsScreenOptions{
		EntryPoint: deeplinkPb.Screen_TIERING_EARNED_BENEFIT_SCREEN,
		UsStocksScreenMetadata: &metadata2.USStocksScreenMetadata{
			DropOffBottomSheetId: getDropOffBottomSheetIdForTierAllPlansV2(tier),
		},
	}
}

func getDropOffBottomSheetIdForTierAllPlansV2(feTier tieringExternalPb.Tier) string {
	switch feTier {
	case tieringExternalPb.Tier_TIER_FI_PLUS:
		return frontend.DropOffBottomSheetType_DROP_OFF_BOTTOM_SHEET_TYPE_PITCH_PLUS.String()
	case tieringExternalPb.Tier_TIER_FI_INFINITE:
		return frontend.DropOffBottomSheetType_DROP_OFF_BOTTOM_SHEET_TYPE_PITCH_INFINITE.String()
	case tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return frontend.DropOffBottomSheetType_DROP_OFF_BOTTOM_SHEET_TYPE_PITCH_PRIME.String()
	default:
		return frontend.DropOffBottomSheetType_DROP_OFF_BOTTOM_SHEET_TYPE_UNSPECIFIED.String()
	}
}

// fetchFiCoinOfferImageUrls returns offer logo and bg image urls from offer images
func fetchFiCoinOfferImageUrls(images []*fePb.OfferImage) (string, string) {
	var (
		offerLogoUrl, offerBgUrl string
	)
	for _, image := range images {
		switch image.GetImageType() {
		case fePb.ImageType_BRAND_IMAGE:
			offerLogoUrl = image.GetUrl()
		case fePb.ImageType_BACKGROUND_IMAGE:
			offerBgUrl = image.GetUrl()
		default:
			logger.ErrorNoCtx("unimplemented offer image type", zap.String("imageType", image.GetImageType().String()))
		}
	}
	return offerLogoUrl, offerBgUrl
}

func getContentForCarousel(catalogOffer *fePb.CatalogOfferV1) (string, string, string, string, string) {
	var (
		bannerIconUrl    string
		bannerBgImageUrl string
		title            string
		bannerBgColor    string
		offerId          string
	)

	switch catalogOffer.GetOfferData().(type) {
	case *fePb.CatalogOfferV1_Offer:
		offer := catalogOffer.GetOffer()
		offerId = offer.GetId()
		bannerBgColor = offer.GetDisplayDetails().GetBgColor()
		title = offer.GetDisplayDetails().GetName()
		bannerIconUrl, bannerBgImageUrl = fetchFiCoinOfferImageUrls(offer.GetDisplayDetails().GetImages())
	case *fePb.CatalogOfferV1_ExchangerOffer:
		exchangerOffer := catalogOffer.GetExchangerOffer()
		offerId = exchangerOffer.GetId()
		bannerBgColor = exchangerOffer.GetDisplayDetails().GetBgColor()
		title = exchangerOffer.GetDisplayDetails().GetTitle()
		bannerIconUrl = exchangerOffer.GetDisplayDetails().GetInfoBannerIconUrl()
		bannerBgImageUrl = exchangerOffer.GetDisplayDetails().GetImageUrl()
	default:
		logger.ErrorNoCtx("unimplemented offer data type", zap.Any("offerDataType", catalogOffer.GetOfferData()))
		return "", "", "", "", ""
	}
	return bannerIconUrl, bannerBgImageUrl, title, bannerBgColor, offerId
}

// Helper to build SDUI section with a visual element and deeplink
func buildSDUISectionWithDeeplink(visualElement *commontypes.VisualElement, deeplink *deeplinkPb.Deeplink) *sections.Section {
	return &sections.Section{
		Content: &sections.Section_VerticalListSection{
			VerticalListSection: &sections.VerticalListSection{
				Components: []*components.Component{
					{
						Content: ui.GetAnyWithoutError(visualElement),
						InteractionBehaviors: []*behaviors.InteractionBehavior{
							{
								Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
									OnClickBehavior: &behaviors.OnClickBehavior{
										Action: ui.GetAnyWithoutError(deeplink),
									},
								},
							},
						},
					},
				},
				VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_CENTER,
				HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
				VisualProperties: []*properties.VisualProperty{
					{
						Properties: &properties.VisualProperty_ContainerProperty{
							ContainerProperty: properties.GetContainerProperty().
								WithPadding(0, 0, 0, 0).
								WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
								WithHeight(properties.Size_Dimension_DIMENSION_TYPE_EXACT, 212),
						},
					},
				},
			},
		},
	}
}

func (c *ComponentBuilder) getOfferSection(data *EarnedBenefitData) *feTieringPb.MonthlyBenefitView_MonthlyBenefits_OfferSection {

	if data.GetRewardsData().GetCurrMonthProjectedFiCoins() < 3000 || len(data.GetCatalogOffers().GetOffers()) == 0 {
		if data.GetIsFiCoinsPostMigrationEnabled() {
			deeplink := data.GetFiCoinsFiPointsDeeplink()
			visualElement := commontypes.GetVisualElementFromUrlHeightAndWidth(postConversionStaticImg, 212, 340)
			sduiSection := buildSDUISectionWithDeeplink(visualElement, deeplink)
			return &feTieringPb.MonthlyBenefitView_MonthlyBenefits_OfferSection{
				Title: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(rewardsGotBetter, platinumGrey, commontypes.FontStyle_SUBTITLE_S)),
				OfferContent: &feTieringPb.MonthlyBenefitView_MonthlyBenefits_OfferSection_Section{
					Section: sduiSection,
				},
			}
		}

		if data.GetIsFiCoinsPreMigrationEnabled() {
			deeplink := data.GetFiCoinsFiPointsDeeplink()
			visualElement := commontypes.GetVisualElementFromUrlHeightAndWidth(preConversionStaticImg, 212, 340)
			sduiSection := buildSDUISectionWithDeeplink(visualElement, deeplink)
			return &feTieringPb.MonthlyBenefitView_MonthlyBenefits_OfferSection{
				OfferContent: &feTieringPb.MonthlyBenefitView_MonthlyBenefits_OfferSection_Section{
					Section: sduiSection,
				},
			}
		}

		// Default case: show a generic visual element without deeplink
		visualElement := commontypes.GetVisualElementLottieFromUrlHeightAndWidth(upiCoinOptJson, 212, 340).WithRepeatCount(3)

		// Create SDUI section for default case
		sduiSection := &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: &sections.VerticalListSection{
					Components: []*components.Component{
						{
							Content: ui.GetAnyWithoutError(visualElement),
						},
					},
					VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_CENTER,
					HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: properties.GetContainerProperty().
									WithPadding(0, 0, 0, 0).
									WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
							},
						},
					},
				},
			},
		}

		return &feTieringPb.MonthlyBenefitView_MonthlyBenefits_OfferSection{
			Title: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(speedUpEarningText, platinumGrey, commontypes.FontStyle_SUBTITLE_S)),
			OfferContent: &feTieringPb.MonthlyBenefitView_MonthlyBenefits_OfferSection_Section{
				Section: sduiSection,
			},
		}
	}

	var offerCards []*feTieringPb.OfferView_OfferCard
	for _, offer := range data.GetCatalogOffers().GetOffers() {
		bannerIconUrl, bannerBgImageUrl, title, bannerBgColor, offerId := getContentForCarousel(offer)
		if bannerBgColor == "" || bannerBgImageUrl == "" || bannerIconUrl == "" || title == "" {
			continue
		}

		offerCard := &feTieringPb.OfferView_OfferCard{
			BackgroundColour: &widget.BackgroundColour{
				Colour: &widgetPb.BackgroundColour_BlockColour{
					BlockColour: bannerBgColor,
				},
			},
			BorderColour: &widget.BackgroundColour{
				Colour: &widgetPb.BackgroundColour_LinearGradient{
					LinearGradient: &widgetPb.LinearGradient{
						Degree: 0,
						LinearColorStops: []*widgetPb.ColorStop{
							{
								Color: silverLake,
							},
							{
								Color:          approxWhisper,
								StopPercentage: 80,
							},
						},
					},
				},
			},
			Logo: commontypes.GetVisualElementFromUrlHeightAndWidth(bannerIconUrl, 32, 32),
			Title: &commontypes.Text{
				FontColor:    aliceBlue,
				DisplayValue: &commontypes.Text_PlainString{PlainString: title},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
				Alignment:    commontypes.Text_ALIGNMENT_LEFT,
			},
			Image: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: bannerBgImageUrl,
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  124,
							Height: 107,
							PaddingProperty: &commontypes.PaddingProperty{
								Left: 29,
								Top:  11,
							},
						},
						ImageType: commontypes.ImageType_PNG,
					},
				}},
		}
		if offerId != "" {
			offerCard.Deeplink = &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_OfferCatalogScreenOptions{
					OfferCatalogScreenOptions: &deeplinkPb.OfferCatalogScreenOptions{
						DisplayFirstOfferIds: []string{offerId},
					},
				},
			}
		}
		offerCards = append(offerCards, offerCard)
	}

	return &feTieringPb.MonthlyBenefitView_MonthlyBenefits_OfferSection{
		Title: uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(rewardsGettingCloseText, nero, commontypes.FontStyle_SUBTITLE_S)).WithLeftVisualElementUrlHeightAndWidth(starIcons, 28, 28).WithRightVisualElementUrlHeightAndWidth(starIcons, 28, 28).WithLeftImagePadding(4).WithRightImagePadding(4),
		OfferContent: &feTieringPb.MonthlyBenefitView_MonthlyBenefits_OfferSection_OfferView{
			OfferView: &feTieringPb.OfferView{
				Cards: offerCards,
				Cta: &deeplinkPb.Cta{
					Type: deeplinkPb.Cta_CUSTOM,
					Text: viewMoreText,
					Deeplink: &deeplinkPb.Deeplink{
						Screen:        deeplinkPb.Screen_OFFERS_LANDING_SCREEN,
						ScreenOptions: &deeplinkPb.Deeplink_OfferCatalogScreenOptions{},
					},
					DisplayTheme: deeplinkPb.Cta_SECONDARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				},
			},
		},
	}
}

//nolint:dupl
func getWarningViewForUpgradedUserV2(data *EarnedBenefitData, warningView *feTieringPb.WarningView) (*feTieringPb.WarningView, error) {
	daysSinceUpgraded := time.Now().Sub(data.GetTieringEssentials().GetLastUpgradeDetails().GetMovementTimestamp().AsTime())
	if daysSinceUpgraded.Hours() > 24 {
		return nil, nil
	}
	tierToLoad := data.GetTierToLoad()
	warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(makeEverySpendCountText, tieringAaSalHelper.TierToCashbackPercent[tierToLoad]), aliceBlue, commontypes.FontStyle_SUBTITLE_S)
	warningView.LeftView = &feTieringPb.WarningView_VisualElement{VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(moneyIcon, 40, 40)}
	warningView.BackgroundColour = widget.GetLinearGradientBackgroundColour(277, getColorStops(tierToLoad))
	warningView.Deeplink = tieringScreenOptionsPb.AllPlansDeeplink(tierToLoad, true)
	warningView.RightTextComponent = uiPb.NewITC().WithRightVisualElementUrlHeightAndWidth(whiteChevron, 24, 24).WithDeeplink(tieringScreenOptionsPb.AllPlansDeeplink(tierToLoad, true))

	if tierToLoad.IsSalaryTier() {
		warningView.Deeplink = &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PAY_LANDING_SCREEN}
		warningView.Title.FontColor = chestNutBrown
		warningView.BackgroundColour = widget.GetBlockBackgroundColour("#F6E1C1")
		warningView.RightTextComponent = uiPb.NewITC().WithRightVisualElementUrlHeightAndWidth(whiteChevron, 24, 24).WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_PAY_LANDING_SCREEN})
	}

	return warningView, nil
}

//nolint:dupl
func getWarningViewForGraceUserV2(data *EarnedBenefitData, warningView *feTieringPb.WarningView, daysRemainingForDowngrade int) (*feTieringPb.WarningView, error) {
	tierToLoad := data.GetTierToLoad()

	if tierToLoad.IsSalaryTier() {
		warningView.Subtitle = commontypes.GetTextFromStringFontColourFontStyle(salaryCreditedButNotReflectingYet, moderateOrange, commontypes.FontStyle_SUBTITLE_S)
		warningView.LeftView = &feTieringPb.WarningView_VisualElement{VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(alertIcon, 40, 40)}
		warningView.BackgroundColour = widget.GetBlockBackgroundColour("#F6E1C1")
		warningView.Deeplink = helper.GetDeeplinkForManualSalaryTxnSelect()
		warningView.RightTextComponent = uiPb.NewITC().WithDeeplink(helper.GetDeeplinkForManualSalaryTxnSelect()).WithRightImageUrlHeightAndWidth(plusChevron, 36, 32)
		return warningView, nil
	}

	var (
		balanceToAddStr, fontColor, bgColor string
	)
	for _, criteriaMinValue := range data.GetTieringEssentials().GetTierCriteriaMinValuesMap()[tierToLoad] {
		if criteriaMinValue.Criteria == enums.CriteriaOptionType_BALANCE_V2_AND_KYC {
			balanceToAdd, subErr := money.Subtract(criteriaMinValue.MinValue, data.GetUserBalance())
			if subErr != nil || money.IsNegative(balanceToAdd) {
				return nil, nil
			}
			balanceToAddStr = money.ToDisplayStringWithSuffixAndPrecisionV2(balanceToAdd, true, true, 2, false, money.IndianNumberSystem)
		}
	}
	tierToPitchString, _ := displayNames.GetTitleCaseDisplayString(tierToLoad)
	switch {
	case daysRemainingForDowngrade > 15:
		warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(tierGraceText1, balanceToAddStr, tierToPitchString), amber50, commontypes.FontStyle_SUBTITLE_S)
		warningView.BackgroundColour = widget.GetLinearGradientBackgroundColour(80, getColorStops(tierToLoad))
		fontColor, bgColor = getFontAndBgColorForGraceV1(tierToLoad)
	case daysRemainingForDowngrade > 10:
		warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(addFundsToAvoidLosingText, amber50, commontypes.FontStyle_SUBTITLE_S)
		warningView.BackgroundColour = widget.GetLinearGradientBackgroundColour(80, getColorStops(tierToLoad))
		fontColor, bgColor = getFontAndBgColorForGraceV1(tierToLoad)
	case daysRemainingForDowngrade > 5:
		warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(dontLoseBackOnYourSpendsText, tieringAaSalHelper.TierToCashbackPercent[tierToLoad], balanceToAddStr), moderateOrange, commontypes.FontStyle_SUBTITLE_S)
		warningView.BackgroundColour = widget.GetBlockBackgroundColour("#F6E1C1")
		fontColor, bgColor = "#C0723D", amber50
	case daysRemainingForDowngrade > 1:
		warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(daysLeftAddMoney, daysRemainingForDowngrade, balanceToAddStr, tieringAaSalHelper.TierToCashbackPercent[tierToLoad]), moderateOrange, commontypes.FontStyle_SUBTITLE_S)
		warningView.BackgroundColour = widget.GetBlockBackgroundColour("#F6E1C1")
		fontColor, bgColor = "#C0723D", amber50
	default:
		warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(finalCallBackExpiresTodayText, tieringAaSalHelper.TierToCashbackPercent[tierToLoad]), OldMauve, commontypes.FontStyle_SUBTITLE_S)
		warningView.BackgroundColour = widget.GetBlockBackgroundColour("#F0BECE")
		fontColor, bgColor = OldMauve, "#F8E5EB"
	}

	warningView.LeftView = &feTieringPb.WarningView_VisualElement{VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(alertIcon, 40, 40)}
	warningView.Deeplink = helper.GetDeeplinkForAddFunds(tierToLoad)
	warningView.RightTextComponent = uiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(addBalanceText, fontColor, commontypes.FontStyle_BUTTON_S)).WithContainerBackgroundColor(bgColor).WithContainerCornerRadius(20).WithContainerPadding(8, 16, 8, 16).WithDeeplink(helper.GetDeeplinkForAddFunds(tierToLoad))

	return warningView, nil
}

//nolint:dupl
func getWarningViewForDowngradedUserV2(data *EarnedBenefitData, warningView *feTieringPb.WarningView) (*feTieringPb.WarningView, error) {
	tierToLoad := data.GetTierToLoad()

	var balanceToAddStr string
	for _, criteriaMinValue := range data.GetTieringEssentials().GetTierCriteriaMinValuesMap()[tierToLoad] {
		if criteriaMinValue.Criteria == enums.CriteriaOptionType_BALANCE_V2_AND_KYC {
			balanceToAdd, subErr := money.Subtract(criteriaMinValue.MinValue, data.GetUserBalance())
			if subErr != nil || money.IsNegative(balanceToAdd) {
				return nil, nil
			}
			balanceToAddStr = money.ToDisplayStringWithSuffixAndPrecisionV2(balanceToAdd, true, true, 2, false, money.IndianNumberSystem)
		}
	}
	tierToPitchString, _ := displayNames.GetTitleCaseDisplayString(tierToLoad)
	warningView.Title = commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(tierDowngradedText, tierToPitchString, balanceToAddStr), aliceBlue, commontypes.FontStyle_SUBTITLE_S)
	warningView.LeftView = &feTieringPb.WarningView_VisualElement{VisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(alertIcon, 40, 40)}
	warningView.Deeplink = helper.GetDeeplinkForAddFunds(tierToLoad)
	warningView.BackgroundColour = widget.GetLinearGradientBackgroundColour(277, getColorStops(tierToLoad))
	warningView.RightTextComponent = uiPb.NewITC().WithRightVisualElementUrlHeightAndWidth(whiteChevron, 24, 24).WithDeeplink(helper.GetDeeplinkForAddFunds(tierToLoad))
	return warningView, nil
}

func getColorStops(tier tieringExternalPb.Tier) []*widgetPb.ColorStop {
	switch tier {
	case tieringExternalPb.Tier_TIER_FI_PLUS:
		return []*widgetPb.ColorStop{{Color: "#DBB295", StopPercentage: -34}, {Color: "#84432E", StopPercentage: 105}}
	case tieringExternalPb.Tier_TIER_FI_INFINITE:
		return []*widgetPb.ColorStop{{Color: "#DBE7F3", StopPercentage: -58}, {Color: "#3C5D7E", StopPercentage: 102}}
	case tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return []*widgetPb.ColorStop{{Color: "#4F71AB", StopPercentage: 2}, {Color: "#17478A", StopPercentage: 113}}
	case tieringExternalPb.Tier_TIER_FI_SALARY, tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:
		return []*widgetPb.ColorStop{{Color: "#F6E1C1", StopPercentage: 2}, {Color: "#C0723D", StopPercentage: 105}}

	default:
		return nil
	}
}

//nolint:dupl
func getFontAndBgColorForGraceV1(tier tieringExternalPb.Tier) (string, string) {
	switch tier {
	case tieringExternalPb.Tier_TIER_FI_PLUS:
		return "#D48647", amber50
	case tieringExternalPb.Tier_TIER_FI_INFINITE:
		return "#6294A6", "#E4F1F5"
	case tieringExternalPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return "#4F71AB", "#E7EBF2"
	case tieringExternalPb.Tier_TIER_FI_SALARY, tieringExternalPb.Tier_TIER_FI_SALARY_BASIC:
		return "#C0723D", amber50
	default:
		return "", ""
	}
}
