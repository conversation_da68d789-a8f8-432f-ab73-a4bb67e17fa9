package chat

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	beChatPb "github.com/epifi/gamma/api/cx/chat"
	feChatPb "github.com/epifi/gamma/api/frontend/cx/chat"
	"github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/frontend/cx"
)

type Service struct {
	cxChatClient beChatPb.ChatsClient
	genConf      *genconf.Config
}

func NewService(cxChatClient beChatPb.ChatsClient, genConf *genconf.Config) *Service {
	return &Service{
		cxChatClient: cxChatClient,
		genConf:      genConf,
	}
}

var _ feChatPb.ChatsServer = &Service{}

func (s *Service) GetChatInitInformationForActor(ctx context.Context, req *feChatPb.GetChatInitInformationForActorRequest) (*feChatPb.GetChatInitInformationForActorResponse, error) {

	actorId := req.GetReq().GetAuth().GetActorId()
	if actorId == "" {
		return &feChatPb.GetChatInitInformationForActorResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id is mandatory"),
			},
		}, nil
	}

	resp, err := s.cxChatClient.GetChatInitInformationForActor(ctx, &beChatPb.GetChatInitInformationForActorRequest{
		ActorId:                        actorId,
		LastSuccessfullyLoadedChatView: req.GetLastSuccessfullyLoadedChatView(),
		LastSuccessfulSessionTime:      req.GetLastSuccessfulSessionTime(),
		ClientSideChatFailureInfo: &beChatPb.ClientSideChatFailureInfo{
			LastTriedChatView: req.GetClientSideChatFailureInfo().GetLastTriedChatView(),
			FailureCount:      req.GetClientSideChatFailureInfo().GetFailureCount(),
			FailureReason:     req.GetClientSideChatFailureInfo().GetFailureReason(),
		},
		ForceNewSession:          req.GetForceNewSession(),
		Device:                   req.GetReq().GetAuth().GetDevice(),
		SenseforthBotContextCode: req.GetSenseforthBotContextCode(),
		Metadata:                 req.GetMetadata(),
		ScreenMetadata:           req.GetScreenMetadata(),
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error calling backend service", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		return s.getErrorViewResponse(ctx, resp)
	}
	response := &feChatPb.GetChatInitInformationForActorResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		ReferenceId:                   resp.GetReferenceId(),
		AppId:                         resp.GetAppId(),
		AppKey:                        resp.GetAppKey(),
		Domain:                        resp.GetDomain(),
		CustomUserProperties:          resp.GetCustomUserProperties(),
		Email:                         resp.GetEmail(),
		ChatViewToBeLoaded:            resp.GetChatViewToBeLoaded(),
		SenseforthChatInitInformation: convertToFeSenseforthChatInitInfo(resp.GetSenseforthChatInitInformation()),
		ShouldAutoRetry:               resp.GetShouldAutoRetry(),
		TopicTags:                     resp.GetTopicTags(),
		ChatbotInitInformation:        resp.GetChatbotInitInformation(),
	}
	return response, nil
}

func (s *Service) getErrorViewResponse(ctx context.Context, beResp *beChatPb.GetChatInitInformationForActorResponse) (*feChatPb.GetChatInitInformationForActorResponse, error) {
	errorView := &errors.ErrorView{
		Type: errors.ErrorViewType_BOTTOM_SHEET,
		Options: &errors.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errors.BottomSheetErrorView{
				Title:    s.genConf.Chat().Title(),
				Subtitle: s.genConf.Chat().SubTitle(),
				Ctas: []*errors.CTA{
					{
						Type: errors.CTA_CX_CHAT,
						Text: "Retry",
					},
					cx.GetContactUsErrorCta(ctx),
				},
			},
		},
	}
	switch {
	case beResp.GetStatus().IsPermanentFailure():
		return &feChatPb.GetChatInitInformationForActorResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusPermanentFailure(),
				ErrorView: errorView,
			},
			ShouldAutoRetry: commontypes.BooleanEnum_FALSE,
		}, nil
	default:
		return &feChatPb.GetChatInitInformationForActorResponse{
			RespHeader: &header.ResponseHeader{
				Status:    beResp.GetStatus(),
				ErrorView: errorView,
			},
		}, nil
	}
}

func convertToFeSenseforthChatInitInfo(information *beChatPb.SenseforthChatInitInformation) *feChatPb.SenseforthChatInitInformation {
	return &feChatPb.SenseforthChatInitInformation{
		WebViewUrl:     information.GetWebViewUrl(),
		ShortToken:     information.GetShortToken(),
		ReuseCacheData: information.GetReuseCacheData(),
		BotContextCode: information.GetBotContextCode(),
	}
}

func (s *Service) GetReferenceIdForActor(ctx context.Context, req *feChatPb.GetReferenceIdForActorRequest) (*feChatPb.GetReferenceIdForActorResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	if actorId == "" {
		return &feChatPb.GetReferenceIdForActorResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id is mandatory"),
		}, nil
	}
	resp, err := s.cxChatClient.GetReferenceIdForActor(ctx, &beChatPb.GetReferenceIdForActorRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error calling backend service", zap.String(logger.ACTOR_ID, actorId), zap.Error(err))
		return &feChatPb.GetReferenceIdForActorResponse{
			Status: resp.GetStatus(),
		}, nil
	}
	return &feChatPb.GetReferenceIdForActorResponse{
		Status:      rpc.StatusOk(),
		ActorId:     resp.GetActorId(),
		ReferenceId: resp.GetReferenceId(),
		AppId:       resp.GetAppId(),
		AppKey:      resp.GetAppKey(),
		Domain:      resp.GetDomain(),
	}, nil
}

func (s *Service) FetchAccessToken(ctx context.Context, req *feChatPb.FetchAccessTokenRequest) (*feChatPb.FetchAccessTokenResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	resp, err := s.cxChatClient.FetchAccessTokenForNuggetChatbot(ctx, &beChatPb.FetchAccessTokenForNuggetChatbotRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error calling cx backend service", zap.String(logger.ACTOR_ID, actorId), zap.Error(te))
		return &feChatPb.FetchAccessTokenResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusFromErrorWithDefaultInternal(te),
			},
		}, nil
	}

	return &feChatPb.FetchAccessTokenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		AccessToken: resp.GetAccessToken(),
	}, nil
}
