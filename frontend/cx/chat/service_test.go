package chat

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"flag"
	"os"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	rpcPb "github.com/epifi/be-common/api/rpc"

	beChatPb "github.com/epifi/gamma/api/cx/chat"
	"github.com/epifi/gamma/api/cx/chat/mocks"
	feChatPb "github.com/epifi/gamma/api/frontend/cx/chat"
	"github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/test"
	"github.com/epifi/gamma/pkg/frontend/cx"
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	flag.Parse()
	var teardown func()
	_, genConf, teardown := test.InitTestServer()
	cts = ChatTestSuite{
		genConf: genConf,
	}
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}

type ChatTestSuite struct {
	genConf *genconf.Config
}

var (
	cts ChatTestSuite
)

const botContextCode1 = "ctx code 1"

func TestService_GetChatInitInformationForActor(t *testing.T) {
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCxChatClient := mocks.NewMockChatsClient(ctr)
	type args struct {
		mocks []interface{}
		ctx   context.Context
		req   *feChatPb.GetChatInitInformationForActorRequest
	}
	tests := []struct {
		name    string
		args    args
		want    *feChatPb.GetChatInitInformationForActorResponse
		wantErr bool
	}{
		{
			name: "internal error",
			args: args{
				mocks: []interface{}{
					mockCxChatClient.EXPECT().GetChatInitInformationForActor(gomock.Any(), gomock.Any()).Return(&beChatPb.GetChatInitInformationForActorResponse{
						Status: rpcPb.StatusInternal(),
					}, nil),
				},
				ctx: context.Background(),
				req: &feChatPb.GetChatInitInformationForActorRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "act-101",
						},
					},
				},
			},
			want: &feChatPb.GetChatInitInformationForActorResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusInternal(),
					ErrorView: &errors.ErrorView{
						Type: errors.ErrorViewType_BOTTOM_SHEET,
						Options: &errors.ErrorView_BottomSheetErrorView{
							BottomSheetErrorView: &errors.BottomSheetErrorView{
								Title:    cts.genConf.Chat().Title(),
								Subtitle: cts.genConf.Chat().SubTitle(),
								Ctas: []*errors.CTA{
									{
										Type: errors.CTA_CX_CHAT,
										Text: "Retry",
									},
									cx.GetContactUsErrorCta(context.Background()),
								},
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "should auto retry is false",
			args: args{
				mocks: []interface{}{
					mockCxChatClient.EXPECT().GetChatInitInformationForActor(gomock.Any(), gomock.Any()).Return(&beChatPb.GetChatInitInformationForActorResponse{
						Status:          rpcPb.StatusPermanentFailure(),
						ShouldAutoRetry: commontypes.BooleanEnum_FALSE,
					}, nil),
				},
				ctx: context.Background(),
				req: &feChatPb.GetChatInitInformationForActorRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "act-101",
						},
					},
				},
			},
			want: &feChatPb.GetChatInitInformationForActorResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusPermanentFailure(),
					ErrorView: &errors.ErrorView{
						Type: errors.ErrorViewType_BOTTOM_SHEET,
						Options: &errors.ErrorView_BottomSheetErrorView{
							BottomSheetErrorView: &errors.BottomSheetErrorView{
								Title:    cts.genConf.Chat().Title(),
								Subtitle: cts.genConf.Chat().SubTitle(),
								Ctas: []*errors.CTA{
									{
										Type: errors.CTA_CX_CHAT,
										Text: "Retry",
									},
									cx.GetContactUsErrorCta(context.Background()),
								},
							},
						},
					},
				},
				ShouldAutoRetry: commontypes.BooleanEnum_FALSE,
			},
			wantErr: false,
		},
		{
			name: "should auto retry is true",
			args: args{
				mocks: []interface{}{
					mockCxChatClient.EXPECT().GetChatInitInformationForActor(gomock.Any(), gomock.Any()).Return(&beChatPb.GetChatInitInformationForActorResponse{
						Status:          rpcPb.StatusOk(),
						ShouldAutoRetry: commontypes.BooleanEnum_TRUE,
						SenseforthChatInitInformation: &beChatPb.SenseforthChatInitInformation{
							WebViewUrl:     "abc",
							ShortToken:     "xyz",
							ReuseCacheData: commontypes.BooleanEnum_FALSE,
							BotContextCode: botContextCode1,
						},
					}, nil),
				},
				ctx: context.Background(),
				req: &feChatPb.GetChatInitInformationForActorRequest{
					Req: &header.RequestHeader{
						Auth: &header.AuthHeader{
							ActorId: "act-101",
						},
					},
				},
			},
			want: &feChatPb.GetChatInitInformationForActorResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpcPb.StatusOk(),
				},
				ShouldAutoRetry: commontypes.BooleanEnum_TRUE,
				SenseforthChatInitInformation: &feChatPb.SenseforthChatInitInformation{
					WebViewUrl:     "abc",
					ShortToken:     "xyz",
					ReuseCacheData: commontypes.BooleanEnum_FALSE,
					BotContextCode: botContextCode1,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc := NewService(mockCxChatClient, cts.genConf)
			got, err := svc.GetChatInitInformationForActor(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetChatInitInformationForActor() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetChatInitInformationForActor() got = %v, want %v", got, tt.want)
			}
		})
	}
}
