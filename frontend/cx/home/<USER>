// nolint:goimports
package home

import (
	"context"
	"fmt"
	"strconv"
	"time"

	homeFePb "github.com/epifi/gamma/api/frontend/home"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"

	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	feHomePb "github.com/epifi/gamma/api/frontend/cx/home"
	"github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	cxDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/help"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/inapphelp/contact_us/constants"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/frontend/cx"
)

const (
	homeWidgetTitle                  = "We're here for you"
	getHelpButtonText                = "Get Help"
	chatWithFiButtonText             = "Chat with Fi"
	monthNameFormat                  = "Jan"
	helpIconURL                      = "https://epifi-icons.pointz.in/faq/get_help.png"
	chatWithFiIconURL                = "https://epifi-icons.pointz.in/faq/chat_with_fi.png"
	rightChevronIconURL              = "https://epifi-icons.pointz.in/faq/chevron_right.png"
	viewTicketRightChevronIconURL    = "https://epifi-icons.pointz.in/faq/view_tickets_chevron_right.png"
	viewTicketButtonText             = "VIEW TICKETS"
	escalationEmail                  = "<EMAIL>"
	ticketResolvedAndRequestCsatText = "✅ Done! Let us know how we did"
	ticketResolvedText               = "✅ Done! Your issue has been resolved"
	ticketUpdateChatInitMessage      = "I would like an update on my issue: %s"
)

var (
	statusToTextMapping = map[ticketPb.TicketStatusForUser]string{
		ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_WAITING_ON_CUSTOMER: "🚨 Pending action from you",
		ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_CLOSED:              "✅ Done! Let us know how we did",
		ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE:              "⌛️ We’re working on it",
		ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_DELAY:               "⌛️ We’re working on it",
		ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_RE_OPEN:             "⌛️ We’re working on it",
	}
	statusToColorMapping = map[ticketPb.TicketStatusForUser]string{
		ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_WAITING_ON_CUSTOMER: "#AA301F", // Red shade
		ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE:              "#6294A6", // Blue shade
		ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_DELAY:               "#6294A6", // Blue shade
		ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_RE_OPEN:             "#6294A6", // Blue shade,
		ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_CLOSED:              "#648E4D", // Green shade
	}

	chatWithUsChipParams = &IconTextChipParams{
		IconUrl: chatWithFiIconURL,
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CHAT_WITH_US_SCREEN,
			ScreenOptions: &deeplink.Deeplink_ChatWithUsScreenOptions{
				ChatWithUsScreenOptions: &deeplink.ChatWithUsScreenOptions{
					ChatViewType: types.InAppChatViewType_IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW,
				},
			},
		},
		TitleText: chatWithFiButtonText,
	}
	getHelpChipParams = &IconTextChipParams{
		IconUrl:   helpIconURL,
		Deeplink:  &deeplink.Deeplink{Screen: deeplink.Screen_HELP_MAIN},
		TitleText: getHelpButtonText,
	}
	contactUsChipParams = func(ctx context.Context) *IconTextChipParams {
		return &IconTextChipParams{
			IconUrl:   "https://epifi-icons.pointz.in/contact_us/contact_us.png",
			Deeplink:  cx.GetContactUsDeeplink(ctx),
			TitleText: "Get help",
		}
	}
	contactUsChipParamsV2 = &IconTextChipParams{
		IconUrl:   "https://epifi-icons.pointz.in/contact_us/contact_us.png",
		Deeplink:  &deeplink.Deeplink{Screen: deeplink.Screen_CONTACT_US_LANDING_SCREEN_V2},
		TitleText: "Get help",
	}
	browseFaqParams = &IconTextChipParams{
		IconUrl:   "https://epifi-icons.pointz.in/contact_us/browse_faq.png",
		Deeplink:  &deeplink.Deeplink{Screen: deeplink.Screen_HELP_MAIN},
		TitleText: "Browse FAQs",
	}
	callUsChipParams = &IconTextChipParams{
		IconUrl:   "https://epifi-icons.pointz.in/contact_us/call_us.png",
		Deeplink:  constants.CallDeeplink,
		TitleText: "Call Us",
	}
	nrUserEmailUsChipParams = &IconTextChipParams{
		IconUrl:   "https://epifi-icons.pointz.in/contact_us/contact_us.png",
		Deeplink:  getEmailUsDeeplink(escalationEmail),
		TitleText: "Email Us",
	}
	containerPropertiesForCTAV2 = &ui.IconTextComponent_ContainerProperties{
		BgColor:       "#FAFAFA",
		CornerRadius:  16,
		Height:        72,
		Width:         184,
		TopPadding:    26,
		BottomPadding: 26,
		BorderWidth:   1,
	}
)

func getChatWithUsParamForTicket(ticketId int64) *IconTextChipParams {
	initMsg := fmt.Sprintf(ticketUpdateChatInitMessage, strconv.FormatInt(ticketId, 10))
	// to add an initial message on a chatbot load, we need to pass the message in screen options
	return &IconTextChipParams{
		IconUrl:   chatWithFiIconURL,
		Deeplink:  cx.GetChatbotDeeplinkWithInitMsg(initMsg),
		TitleText: chatWithFiButtonText,
	}
}

func getVerticalKeyValuePairs(chipParams []*IconTextChipParams) []*ui.VerticalKeyValuePair {
	var keyValuePairList []*ui.VerticalKeyValuePair
	for _, chipParam := range chipParams {
		keyValuePairList = append(keyValuePairList, getIconTextChip(chipParam))
	}
	return keyValuePairList
}

func getDateAndMonthName(t time.Time) (string, string) {
	_, _, date := t.Date()
	monthName := t.Format(monthNameFormat)
	return strconv.Itoa(date), monthName
}

//nolint:funlen
func getSupportTicketWidget(ticket *ticketPb.TicketDetailsForUser) *feHomePb.HomeSupportTicket {
	screenOptionsV2, _ := deeplinkv3.GetScreenOptionV2(&cxDlOptions.CXTicketDetailScreenOptions{
		TicketId: ticket.GetId(),
	})
	date, monthName := getDateAndMonthName(ticket.GetCreatedTime().AsTime())
	if ticket.GetLastInteractionTime() != nil {
		date, monthName = getDateAndMonthName(ticket.GetLastInteractionTime().AsTime())
	}
	if ticket.GetClosedTime() != nil {
		date, monthName = getDateAndMonthName(ticket.GetClosedTime().AsTime())
	}

	return &feHomePb.HomeSupportTicket{
		CreatedOn: &ui.VerticalKeyValuePair{
			Title: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						FontColor: colors.ColorOnDarkLowEmphasis,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: date,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_NUMBER_XL,
						},
					},
				},
			},
			Value: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						FontColor: colors.ColorOnDarkLowEmphasis,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: monthName,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
						},
					},
				},
			},
			VerticalPaddingBtwTitleValue: 0,
			HAlignment:                   ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_CENTER,
		},
		TicketDetails: &ui.VerticalKeyValuePair{
			Title: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						FontColor: colors.ColorOnLightHighEmphasis,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: ticket.GetTitle(),
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_HEADLINE_S,
						},
						Alignment: commontypes.Text_ALIGNMENT_LEFT,
					},
				},
				Deeplink: &deeplink.Deeplink{
					Screen:          deeplink.Screen_CX_TICKET_DETAIL_SCREEN,
					ScreenOptionsV2: screenOptionsV2,
				},
			},
			Value: &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					{
						FontColor: statusToColorMapping[ticket.GetStatus()],
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: getStatusText(ticket),
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_HEADLINE_XS,
						},
						Alignment: commontypes.Text_ALIGNMENT_LEFT,
					},
				},
				Deeplink: &deeplink.Deeplink{
					Screen:          deeplink.Screen_CX_TICKET_DETAIL_SCREEN,
					ScreenOptionsV2: screenOptionsV2,
				},
			},
			VerticalPaddingBtwTitleValue: 4,
			HAlignment:                   ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT,
		},
		TicketActionImage: &commontypes.Image{
			ImageType:       commontypes.ImageType_PNG,
			ImageDataBase64: "",
			ImageUrl:        rightChevronIconURL,
			Width:           24,
			Height:          24,
			Padding:         nil,
			Margin:          nil,
		},
		TicketBorderColor:     homeFePb.GetHomeWidgetBorderColor(),
		TicketBackgroundColor: homeFePb.GetHomeWidgetTicketBackgroundColor(),
	}
}

func getStatusText(ticket *ticketPb.TicketDetailsForUser) string {
	if ticket.GetStatus() != ticketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_CLOSED {
		return statusToTextMapping[ticket.GetStatus()]
	}
	// for tickets in terminal state (i.e., closed for user)
	// we need to have a status text modified based on CSAT survey requirement
	if ticket.GetIsInAppCsatSurveyRequired() {
		return ticketResolvedAndRequestCsatText
	}
	return ticketResolvedText
}

func getSupportTicketsCTA() *ui.IconTextComponent {
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			{
				FontColor: colors.ColorOnDarkLowEmphasis,
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: viewTicketButtonText,
				},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS,
				},
			},
		},
		RightVisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: viewTicketRightChevronIconURL,
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  24,
						Height: 24,
					},
					ImageType:     commontypes.ImageType_PNG,
					RenderingType: nil,
				},
			},
		},
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CX_TICKET_LIST_SCREEN,
		},
	}
}

type IconTextChipParams struct {
	IconUrl   string
	Deeplink  *deeplink.Deeplink
	TitleText string
}

func getIconTextChip(params *IconTextChipParams) *ui.VerticalKeyValuePair {
	return &ui.VerticalKeyValuePair{
		Title: &ui.IconTextComponent{
			LeftVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: params.IconUrl,
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  32,
							Height: 32,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: colors.ColorSnow,
			},
			Deeplink: params.Deeplink,
		},
		Value: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				{
					FontColor: colors.ColorOnDarkLowEmphasis,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: params.TitleText,
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BUTTON_S,
					},
				},
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: colors.ColorSnow,
			},
			Deeplink: params.Deeplink,
		},
		VerticalPaddingBtwTitleValue: 4,
	}
}

func getEmailUsDeeplink(emailId string) *deeplink.Deeplink {
	externalUrl := "mailto:" + emailId
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_EXTERNAL_REDIRECTION,
		ScreenOptions: &deeplink.Deeplink_ExternalRedirectionScreenOptions{
			ExternalRedirectionScreenOptions: &deeplink.ExternalRedirectionScreenOptions{
				ExternalUrl: externalUrl,
			},
		},
	}
}
