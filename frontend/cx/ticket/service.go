//nolint:funlen
package ticket

import (
	"context"
	"strconv"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	cxDlOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/help"
	"github.com/epifi/gamma/cx/ticket"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	"github.com/samber/lo"

	"github.com/epifi/gamma/api/frontend/deeplink"
	errorspb "github.com/epifi/gamma/api/frontend/errors"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/cx/ticket/widget"
	errors2 "github.com/epifi/gamma/pkg/frontend/errors"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	beTicketPb "github.com/epifi/gamma/api/cx/ticket"
	feTicketPb "github.com/epifi/gamma/api/frontend/cx/ticket"
	"github.com/epifi/gamma/api/frontend/header"
)

type Service struct {
	feTicketPb.UnimplementedTicketServer
	ticketClient beTicketPb.TicketClient
	genConf      *genconf.Config
}

func NewService(ticketClient beTicketPb.TicketClient, genConf *genconf.Config) *Service {
	return &Service{
		ticketClient: ticketClient,
		genConf:      genConf,
	}
}

var (
	fetchTicketErrorView = errors2.NewBottomSheetErrorView("", "Ticket not found", "", "Oops! Sorry, we could not fetch the ticket details", &errorspb.CTA{
		Text: "View all tickets",
		Type: errorspb.CTA_DONE,
		Action: &deeplink.Deeplink{
			Screen: deeplink.Screen_CX_TICKET_LIST_SCREEN,
		},
		DisplayTheme: errorspb.CTA_PRIMARY,
	})
	noTicketsForUser = &feTicketPb.DisplayMessageForBanner{
		IconUrl: "https://epifi-icons.pointz.in/server-driven-ui/icons/ic_no_open_tickets.png",
		Message: &commontypes.Text{
			FontColor: colors.ColorDarkLayer2,
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Your active support-related ticket appears here",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
			},
			Alignment: commontypes.Text_ALIGNMENT_LEFT,
		},
	}
	unableToLoadTicket = &feTicketPb.DisplayMessageForBanner{
		IconUrl: "https://epifi-icons.pointz.in/server-driven-ui/icons/ic_ticket_unable_to_load.png",
		Message: &commontypes.Text{
			FontColor: colors.ColorDarkLayer2,
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Unable to load this ticket",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
			},
			Alignment: commontypes.Text_ALIGNMENT_LEFT,
		},
	}
	noOpenTicketsForUser = &feTicketPb.DisplayMessageForBanner{
		IconUrl: "https://epifi-icons.pointz.in/server-driven-ui/icons/ic_no_open_tickets.png",
		Message: &commontypes.Text{
			FontColor: colors.ColorDarkLayer2,
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Woohoo! No open tickets",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
			},
			Alignment: commontypes.Text_ALIGNMENT_LEFT,
		},
	}
	// allowed ticket states that will be showed in the primary ticket in help screen
	allowedPrimaryTicketStates = []beTicketPb.TicketStatusForUser{
		beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE,
		beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_WAITING_ON_CUSTOMER,
		beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_DELAY,
		beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_RE_OPEN,
	}
	beTicketStatusToFeTicketStatus = map[beTicketPb.TicketStatusForUser]feTicketPb.TicketStatusForUser{
		beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE:              feTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE,
		beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_CLOSED:              feTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_CLOSED,
		beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_WAITING_ON_CUSTOMER: feTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_WAITING_ON_CUSTOMER,
		beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_DELAY:               feTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE,
		beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_RE_OPEN:             feTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE,
	}
)
var _ feTicketPb.TicketServer = &Service{}

func (s *Service) GetSupportTicketsForApp(ctx context.Context, req *feTicketPb.GetSupportTicketsForAppRequest) (*feTicketPb.GetSupportTicketsForAppResponse, error) {
	// Client checks whether this feature is enabled or not from the response of frontend.user.GetUserSessionDetails RPC
	// using FeatureMap[SHOW_SUPPORT_TICKETS_IN_APP]
	if req.GetPageContextRequest().GetPageSize() == 0 {
		if req.GetPageContextRequest() == nil {
			req.PageContextRequest = &rpc.PageContextRequest{}
		}
		req.GetPageContextRequest().PageSize = uint32(s.genConf.Cx().Ticket().SupportTicketsInAppPageSize())
	}
	beResp, err := s.ticketClient.GetSupportTicketsForApp(ctx, &beTicketPb.GetSupportTicketsForAppRequest{
		ActorId:            req.GetReq().GetAuth().GetActorId(),
		TicketFilters:      convertToBeTicketFilters(req.GetTicketFilters()),
		PageContextRequest: req.GetPageContextRequest(),
	})
	if te := epifigrpc.RPCError(beResp, err); te != nil {
		logger.Error(ctx, "error invoking backend cx ticket service", zap.Error(te))
		if beResp.GetStatus().IsRecordNotFound() {
			// Return status OK with nil list as the client is not handling differentiation of error codes
			return &feTicketPb.GetSupportTicketsForAppResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
			}, nil
		}
		return &feTicketPb.GetSupportTicketsForAppResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternal(),
			},
		}, nil
	}

	return s.populateSampleFlowId(&feTicketPb.GetSupportTicketsForAppResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		Tickets:                            convertToFeTickets(beResp.GetTickets()),
		IsLatestTicketDetailsStillUpdating: beResp.GetIsLatestTicketDetailsStillUpdating(),
		PageContextResponse:                beResp.GetPageContextResponse(),
	}), nil
}

func convertToBeTicketFilters(feFilters *feTicketPb.TicketFiltersForUser) *beTicketPb.TicketFiltersForUser {
	return &beTicketPb.TicketFiltersForUser{
		StatusList: convertToBeStatusList(feFilters.GetStatusList()),
	}
}

func convertToBeStatusList(feStatusList []feTicketPb.TicketStatusForUser) []beTicketPb.TicketStatusForUser {
	var beStatusList []beTicketPb.TicketStatusForUser
	for _, feStatus := range feStatusList {
		beStatusList = append(beStatusList, convertToBeStatus(feStatus))
	}
	return beStatusList
}

func convertToBeStatus(feStatus feTicketPb.TicketStatusForUser) beTicketPb.TicketStatusForUser {
	beStatus, ok := beTicketPb.TicketStatusForUser_value[feStatus.String()]
	if !ok {
		return beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_UNSPECIFIED
	}
	return beTicketPb.TicketStatusForUser(beStatus)
}

func convertToFeTickets(beTickets []*beTicketPb.TicketDetailsForUser) []*feTicketPb.TicketDetailsForUser {
	var feTickets []*feTicketPb.TicketDetailsForUser
	for _, beTicket := range beTickets {
		feTickets = append(feTickets, convertToFeTicket(beTicket))
	}
	return feTickets
}

func convertToFeTicket(beTicket *beTicketPb.TicketDetailsForUser) *feTicketPb.TicketDetailsForUser {
	return &feTicketPb.TicketDetailsForUser{
		Id:                   beTicket.GetId(),
		Title:                beTicket.GetTitle(),
		Description:          beTicket.GetDescription(),
		Status:               convertToFeStatus(beTicket.GetStatus()),
		CreatedTime:          beTicket.GetCreatedTime(),
		ExpectedResolutionBy: beTicket.GetExpectedResolutionBy(),
		LastInteractionTime:  beTicket.GetLastInteractionTime(),
		ClosedTime:           beTicket.GetClosedTime(),
		OtherTicketDetails:   convertToFeDetailsMap(beTicket.GetOtherTicketDetails()),
	}
}

func convertToFeDetailsMap(beDetailsMap map[string]*beTicketPb.TicketDetailsForUser_MapValue) map[string]*feTicketPb.TicketDetailsForUser_MapValue {
	feDetailMap := make(map[string]*feTicketPb.TicketDetailsForUser_MapValue)
	for key, value := range beDetailsMap {
		if key != ticket.AutoIdTag {
			feDetailMap[key] = convertToFeMapValue(value)
		}
	}
	return feDetailMap
}

func convertToFeMapValue(beValue *beTicketPb.TicketDetailsForUser_MapValue) *feTicketPb.TicketDetailsForUser_MapValue {
	switch beValue.GetVal().(type) {
	case *beTicketPb.TicketDetailsForUser_MapValue_StrVal:
		return &feTicketPb.TicketDetailsForUser_MapValue{
			Val: &feTicketPb.TicketDetailsForUser_MapValue_StrVal{StrVal: beValue.GetStrVal()},
		}
	default:
	}
	return nil
}

func convertToFeStatus(beStatus beTicketPb.TicketStatusForUser) feTicketPb.TicketStatusForUser {
	feStatus, ok := beTicketStatusToFeTicketStatus[beStatus]
	if !ok {
		return feTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_UNSPECIFIED
	}
	return feStatus
}

func (s *Service) populateSampleFlowId(resp *feTicketPb.GetSupportTicketsForAppResponse) *feTicketPb.GetSupportTicketsForAppResponse {
	if s.genConf.FeedbackEngineConfig().ResponseHeaderPopulationConfig().IsPopulationInGetSupportTicketsForAppEnabled() {
		resp.RespHeader.FeedbackEngineInfo = &header.FeedbackEngineInfo{FlowIdDetails: &header.FlowIdentifierDetails{
			FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_APP_FLOW,
			FlowIdentifier:     "sample_flow",
		}}
	}
	return resp
}

func (s *Service) GetSupportTicketByIdForApp(ctx context.Context, req *feTicketPb.GetSupportTicketByIdForAppRequest) (*feTicketPb.GetSupportTicketByIdForAppResponse, error) {
	if req.GetTicketId() == 0 {
		return &feTicketPb.GetSupportTicketByIdForAppResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInvalidArgumentWithDebugMsg("mandatory field ticket id not passed"),
				ErrorView: fetchTicketErrorView,
			},
		}, nil
	}
	resp, err := s.ticketClient.GetSupportTicketByIdForApp(ctx, &beTicketPb.GetSupportTicketByIdForAppRequest{TicketId: req.GetTicketId(), ActorId: req.GetReq().GetAuth().GetActorId()})
	if te := epifigrpc.RPCError(resp, err); te != nil {
		logger.Error(ctx, "error invoking backend cx ticket service", zap.Error(te))
		if resp.GetStatus().IsRecordNotFound() {
			return &feTicketPb.GetSupportTicketByIdForAppResponse{
				RespHeader: &header.ResponseHeader{
					Status:    rpc.StatusRecordNotFound(),
					ErrorView: fetchTicketErrorView,
				},
			}, nil
		}
		return &feTicketPb.GetSupportTicketByIdForAppResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: fetchTicketErrorView,
			},
		}, nil
	}
	return &feTicketPb.GetSupportTicketByIdForAppResponse{
		RespHeader: s.populateInAppCSATFlowId(ctx, &header.ResponseHeader{Status: rpc.StatusOk()}, resp.GetTicket()),
		Ticket:     convertToFeTicket(resp.GetTicket()),
	}, nil
}

func (s *Service) populateInAppCSATFlowId(ctx context.Context, respHeader *header.ResponseHeader, ticketDetailsForUser *beTicketPb.TicketDetailsForUser) *header.ResponseHeader {
	// we will only populate the flow id if backend populates the flag that in app csat survey
	// and the flow id population is enabled in the config
	logger.Debug(ctx, "in app CSAT ticket flags", zap.Int64("ticketId", ticketDetailsForUser.GetId()), zap.Bool("responseHeaderPopulation", s.genConf.FeedbackEngineConfig().ResponseHeaderPopulationConfig().IsPopulationInGetSupportTicketByIdForAppEnabled()), zap.Bool("isInAppCsatSurveyRequired", ticketDetailsForUser.GetIsInAppCsatSurveyRequired()))

	flowIdentifier := typesPb.FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT
	if autoIdDetail, ok := ticketDetailsForUser.GetOtherTicketDetails()[ticket.AutoIdTag]; ok && autoIdDetail.GetBoolVal() == commontypes.BooleanEnum_TRUE {
		flowIdentifier = typesPb.FeedbackAppFlowIdentifier_FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT_AUTO_IDS
	}

	if ticketDetailsForUser.GetIsInAppCsatSurveyRequired() &&
		s.genConf.FeedbackEngineConfig().ResponseHeaderPopulationConfig().IsPopulationInGetSupportTicketByIdForAppEnabled() {
		respHeader.FeedbackEngineInfo = &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_APP_FLOW,
				FlowIdentifier:     flowIdentifier.String() + s.genConf.FeedbackEngineConfig().FlowIdSeparator() + strconv.FormatInt(ticketDetailsForUser.GetId(), 10),
			},
		}
	}
	return respHeader
}

func (s *Service) GetTicketBanner(ctx context.Context, request *feTicketPb.GetTicketBannerRequest) (*feTicketPb.GetTicketBannerResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	if actorId == "" {
		logger.Error(ctx, "actor id cannot be empty to get ticket banner")
		return &feTicketPb.GetTicketBannerResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusInvalidArgument()},
		}, nil
	}
	// fetch closed and active tickets for the user based on ticket_created_at
	beResp, err := s.ticketClient.GetSupportTicketsForApp(ctx, &beTicketPb.GetSupportTicketsForAppRequest{
		ActorId: actorId,
		TicketFilters: &beTicketPb.TicketFiltersForUser{
			StatusList: []beTicketPb.TicketStatusForUser{
				beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_ACTIVE,
				beTicketPb.TicketStatusForUser_TICKET_STATUS_FOR_USER_CLOSED,
			},
		},
	})
	if te := epifigrpc.RPCError(beResp, err); te != nil {
		logger.Error(ctx, "error invoking backend cx ticket service", zap.Error(te))
		if beResp.GetStatus().IsRecordNotFound() {
			// Return status OK with nil list as the client is not handling differentiation of error codes
			return &feTicketPb.GetTicketBannerResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				// no tickets available for user, hence we will return the relevant message view
				TicketBannerDetails: &feTicketPb.GetTicketBannerResponse_MessageView{
					MessageView: noTicketsForUser,
				},
			}, nil
		}
		// if there is an error, we will return the unable to load error message view
		return &feTicketPb.GetTicketBannerResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			TicketBannerDetails: &feTicketPb.GetTicketBannerResponse_MessageView{
				MessageView: unableToLoadTicket,
			},
		}, nil
	}
	// as the tickets were fetched, we will check if there are any open tickets
	// if there are multiple open tickets we will choose the latest one to show to the user
	latestOpenTicket := &beTicketPb.TicketDetailsForUser{}
	for _, ticket := range beResp.GetTickets() {
		if lo.Contains(allowedPrimaryTicketStates, ticket.GetStatus()) &&
			ticket.GetCreatedTime().AsTime().After(latestOpenTicket.GetCreatedTime().AsTime()) {
			latestOpenTicket = ticket
		}
	}
	// if there is no empty ticket found with a valid ticket id
	// we will return no open tickets view
	if latestOpenTicket.GetId() == 0 {
		logger.Info(ctx, "no open tickets found for user", zap.String(logger.ACTOR_ID_V2, actorId))
		return &feTicketPb.GetTicketBannerResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			TicketBannerDetails: &feTicketPb.GetTicketBannerResponse_MessageView{
				MessageView: noOpenTicketsForUser,
			},
		}, nil
	}
	// otherwise we will return the ticket banner containing latest open ticket to client
	return &feTicketPb.GetTicketBannerResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		TicketBannerDetails: &feTicketPb.GetTicketBannerResponse_TicketDetails{
			TicketDetails: widget.GetTicketBanner(latestOpenTicket, s.genConf.Cx().Ticket().IsStatusNoteV2Enabled()),
		},
	}, nil
}

// CollectCsatSurvey fetches if there are any latest resolved ticket for which CSAT hasn't been collected
func (s *Service) CollectCsatSurvey(ctx context.Context, request *feTicketPb.CollectCsatSurveyRequest) (*feTicketPb.CollectCsatSurveyResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	if actorId == "" {
		logger.Error(ctx, "actor id cannot be empty to collect CSAT survey")
		return &feTicketPb.CollectCsatSurveyResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id cannot be empty to collect CSAT survey"),
			},
		}, nil
	}

	beResp, err := s.ticketClient.FetchLatestResolvedTicketIdForCSAT(ctx, &beTicketPb.FetchLatestResolvedTicketIdForCSATRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(beResp, err); te != nil {
		logger.Error(ctx, "error invoking backend cx ticket service", zap.Error(te))
		return &feTicketPb.CollectCsatSurveyResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("error invoking backend cx ticket service"),
			},
		}, nil
	}

	return &feTicketPb.CollectCsatSurveyResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		TicketScreenDeeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CX_TICKET_DETAIL_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&cxDlOptions.CXTicketDetailScreenOptions{
				TicketId: beResp.GetTicketId(),
			}),
		},
	}, nil
}
