package alfred

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/logger"

	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/alfred"
	feAlfredPb "github.com/epifi/gamma/api/frontend/alfred"
	"github.com/epifi/gamma/api/frontend/header"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/feature/release"
	feErrors "github.com/epifi/gamma/pkg/frontend/errors"
)

type Service struct {
	genConf          *genconf.Config
	alfredClient     alfredPb.AlfredClient
	releaseEvaluator release.IEvaluator
}

func NewService(genConf *genconf.Config, alfredClient alfredPb.AlfredClient, releaseEvaluator release.IEvaluator) *Service {
	return &Service{
		genConf:          genConf,
		alfredClient:     alfredClient,
		releaseEvaluator: releaseEvaluator,
	}
}

func (s *Service) GetRequestChoices(ctx context.Context, req *alfred.GetRequestChoicesRequest) (*alfred.GetRequestChoicesResponse, error) {
	var (
		actorId        = req.GetReq().GetAuth().GetActorId()
		requestBundles []*alfred.RequestBundle
	)
	reqFilters := &alfredPb.RequestChoiceFilter{}
	if len(req.GetChoicesFilter()) > 0 {
		err := protojson.Unmarshal(req.GetChoicesFilter(), reqFilters)
		if err != nil {
			logger.Error(ctx, "failed to unmarshal alfred req choice filter", zap.Error(err))
		}
	}

	if isBundleTypeRequested(reqFilters.GetBundleTypes(), alfredPb.RequestBundleType_REQUEST_BUNDLE_TYPE_CHEQUEBOOK) {
		cbReqChoices := chequebookReqChoices
		if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConf.Alfred().EnableRequestChoiceBottomSheet()) {
			logger.Info(ctx, "show request bottom ")
			cbReqChoices = newChequebookReqChoices
		}
		if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConf.Alfred().EnableCancelledCheque()) {
			cbReqChoices = append(cbReqChoices, cancelChequeSectionElement)
		}
		cbReqBundle := &alfred.RequestBundle{
			Label:          chbqLabel,
			RequestChoices: cbReqChoices,
		}
		if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConf.Alfred().EnableVRH()) {
			cbReqBundle.RequestHistories = chequebookReqHistories
		}
		requestBundles = append(requestBundles, cbReqBundle)
	}
	if isBundleTypeRequested(reqFilters.GetBundleTypes(), alfredPb.RequestBundleType_REQUEST_BUNDLE_TYPE_ELSS_TAX_STATEMENTS) {
		requestBundles = append(requestBundles, taxStatementReqBundle)
	}

	if isBundleTypeRequested(reqFilters.GetBundleTypes(), alfredPb.RequestBundleType_REQUEST_BUNDLE_TYPE_PROFILE_UPDATE) {
		addressUpdateReqBundle := &alfred.RequestBundle{
			Label:          profileUpdateLabel,
			RequestChoices: updateReqChoices,
		}
		if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConf.Alfred().EnableAddressUpdateReqHistories()) {
			addressUpdateReqBundle.RequestHistories = addressUpdateRequestHistories
		}
		if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConf.Alfred().EnableAddressUpdate()) {
			requestBundles = append(requestBundles, addressUpdateReqBundle)
		}
	}

	if isBundleTypeRequested(reqFilters.GetBundleTypes(), alfredPb.RequestBundleType_REQUEST_BUNDLE_TYPE_UPDATE_SIGNATURE) {
		isSignUpdateFlowEnabled, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(
			types.Feature_ALFRED_SAVINGS_ACC_SIGN_UPDATE).WithActorId(actorId))
		if err != nil {
			logger.Error(ctx, "error checking if SAVINGS_ACC_SIGN_UPDATE is enabled or not", zap.Any(logger.ACTOR_ID_V2, actorId))
		} else if isSignUpdateFlowEnabled {
			requestBundles = append(requestBundles, updateSignReqBundle)
		}
	}

	if isBundleTypeRequested(reqFilters.GetBundleTypes(), alfredPb.RequestBundleType_REQUEST_BUNDLE_TYPE_USS_DOCUMENTS) {
		isUssDocRequestReleased, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(
			types.Feature_FEATURE_ALFRED_USS_DOCUMENT_REQUEST).WithActorId(actorId))
		if err != nil {
			logger.Error(ctx, "error checking if uss tax document request feature is released or not", zap.Any(logger.ACTOR_ID_V2, actorId))
		} else if isUssDocRequestReleased {
			requestBundles = append(requestBundles, ussDocumentsReqBundle)
		}
	}

	return &feAlfredPb.GetRequestChoicesResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		RequestBundles: requestBundles,
	}, nil
}

// isBundleTypeRequested returns true if bundle type is part of requested bundles list
// if requested bundles types is not present it returns a true as default behaviour (to maintain backward compatibility)
func isBundleTypeRequested(bundleTypes []alfredPb.RequestBundleType, bundleType alfredPb.RequestBundleType) bool {
	if len(bundleTypes) == 0 {
		return true
	}
	return lo.Contains(bundleTypes, bundleType)
}

func (s *Service) ProvisionNewRequest(ctx context.Context, req *alfred.ProvisionNewReqRequest) (*alfred.ProvisionNewReqResponse, error) {
	var (
		actorId = req.GetReq().GetAuth().GetActorId()
		reqType = req.GetRequestType()
	)
	if actorId == "" || reqType == "" {
		logger.Error(ctx, "actorId or requestType are missing")
		return &feAlfredPb.ProvisionNewReqResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInvalidArgumentWithDebugMsg("actorId or requestType are missing"),
				ErrorView: feErrors.ClientErrAsErrView(ctx, feErrors.UNKNOWN_CLIENT_ERR),
			},
		}, nil
	}
	reqTypeInt, exist := alfredPb.RequestType_value[reqType]
	if !exist {
		logger.Error(ctx, "incorrect req type sent for the new provision request")
		return &feAlfredPb.ProvisionNewReqResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInvalidArgumentWithDebugMsg("request type does not exist"),
				ErrorView: feErrors.ClientErrAsErrView(ctx, feErrors.UNKNOWN_CLIENT_ERR),
			},
		}, nil
	}
	provisionChbqRes, err := s.alfredClient.ProvisionNewRequest(ctx, &alfredPb.ProvisionNewRequestRequest{
		ActorId:     actorId,
		RequestType: alfredPb.RequestType(reqTypeInt),
		Blob:        req.GetBlob(),
	})
	if rpcErr := epifigrpc.RPCError(provisionChbqRes, err); rpcErr != nil {
		logger.Info(ctx, "failed to initialise new request", zap.Error(rpcErr))
		// todo (rishu sahu) trickle down backend errors to client error screens
		return &feAlfredPb.ProvisionNewReqResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("failed to provision new request"),
				ErrorView: feErrors.ClientErrAsErrView(ctx, feErrors.UNKNOWN_CLIENT_ERR),
			},
		}, nil
	}
	return &feAlfredPb.ProvisionNewReqResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: provisionChbqRes.GetNextAction(),
	}, nil
}

func (s *Service) PollRequestStatus(ctx context.Context, req *alfred.PollRequestStatusRequest) (*alfred.PollRequestStatusResponse, error) {
	var (
		requestId = req.GetRequestId()
	)
	if requestId == "" {
		logger.Error(ctx, "request id is missing in request")
		return &feAlfredPb.PollRequestStatusResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("request id is missing in request"),
			},
		}, nil
	}
	pollRequestRes, err := s.alfredClient.PollRequestStatus(ctx, &alfredPb.PollRequestStatusRequest{
		RequestId: requestId,
	})
	if rpcErr := epifigrpc.RPCError(pollRequestRes, err); rpcErr != nil {
		logger.Info(ctx, "getting unexpected response from PollRequestStatus backend", zap.Error(rpcErr))
		return &feAlfredPb.PollRequestStatusResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
				ErrorView: feErrors.ClientErrAsErrView(ctx, feErrors.UNKNOWN_CLIENT_ERR),
			},
		}, nil
	}
	return &feAlfredPb.PollRequestStatusResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: pollRequestRes.GetNextAction(),
	}, nil
}

func (s *Service) GetFilteredRequestSummaries(ctx context.Context, req *feAlfredPb.GetFilteredRequestSummariesRequest) (*feAlfredPb.GetFilteredRequestSummariesResponse, error) {
	var (
		requestTypes  []alfredPb.RequestType
		categoryType  alfredPb.CategoryType
		sortOrder     alfredPb.SortOrder
		statuses      []alfredPb.Status
		pageContext   = req.GetPageContext()
		constructResp = func(status *rpc.Status) *feAlfredPb.GetFilteredRequestSummariesResponse {
			return &feAlfredPb.GetFilteredRequestSummariesResponse{
				RespHeader: &header.ResponseHeader{
					Status: status,
				},
			}
		}
	)

	if pageContext == nil {
		pageContext = &rpc.PageContextRequest{}
	}
	pageContext.PageSize = s.genConf.Alfred().ServiceRequestHistoryPageSize()

	sortOrder = covertSortOrderToEnums(req.GetSortOrder())
	categoryType = convertCategoryTypeToEnum(ctx, req.GetCategoryType())
	if categoryType == alfredPb.CategoryType_CATEGORY_TYPE_UNSPECIFIED {
		return constructResp(rpc.StatusInvalidArgumentWithDebugMsg("category type does not exist")), nil
	}
	requestTypes = convertRequestTypesToEnums(ctx, req.GetRequestTypes(), categoryType)
	statuses = convertStatusesToEnums(ctx, req.GetStatuses())

	resp, err := s.alfredClient.GetAllRequestStatusDetails(ctx, &alfredPb.GetAllRequestStatusDetailsRequest{
		Filters: &alfredPb.Filters{
			ActorId:      req.GetReq().GetAuth().GetActorId(),
			RequestTypes: requestTypes,
			StatusList:   statuses,
		},
		PageContext: pageContext,
		SortOrder:   sortOrder,
	})

	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "error in getting request summaries from be", zap.Error(rpcErr))
		return constructResp(rpc.StatusInternalWithDebugMsg("error in getting request summaries from be")), nil
	}
	if len(resp.GetServiceRequestList()) == 0 {
		return constructResp(rpc.StatusRecordNotFoundWithDebugMsg("no record found for the category type")), nil
	}
	logger.Debug(ctx, fmt.Sprintf("logging the request summaries %v", constructRequestSummaries(resp.GetServiceRequestList(), s.isCopyTrackingUrlFeatureEnabled(ctx))))
	return &feAlfredPb.GetFilteredRequestSummariesResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		RequestSummaries: constructRequestSummaries(resp.GetServiceRequestList(), s.isCopyTrackingUrlFeatureEnabled(ctx)),
		PageContext:      resp.GetPageContext(),
	}, nil
}

func (s *Service) isCopyTrackingUrlFeatureEnabled(ctx context.Context) bool {
	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, s.genConf.Alfred().EnableCopyTrackingUrl()) {
		logger.Info(ctx, "copy tracking feature is enabled")
		return true
	}
	return false
}
