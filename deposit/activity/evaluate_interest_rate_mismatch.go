package activity

import (
	"context"
	"fmt"

	activity2 "go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/pkg/errors"

	accountPb "github.com/epifi/gamma/api/accounts"
	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/deposit/activity"
	types "github.com/epifi/gamma/api/typesv2"
	vgDepositPb "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	accesor "github.com/epifi/gamma/deposit/accessor"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

func (p *Processor) EvaluateDepositInterestRateMismatch(ctx context.Context, request *activity.EvaluateDepositInterestRateMismatchRequest) (*activity.EvaluateDepositInterestRateMismatchResponse, error) {
	lg := activity2.GetLogger(ctx)
	maxSlabAmount := moneyPkg.ParseInt(p.workerConfig.MaxDepositSlab, moneyPkg.RupeeCurrencyCode)
	interestRateInfoResp, err := p.vgDepositAccessor.GetInterestRateFetch(ctx, &accesor.GetInterestRateFetchRequest{
		InterestCategory: request.GetInterestRateCategory(),
		EffectiveDate:    datetimePkg.TimeToDateInLoc(request.GetInterestEnquiryAt().AsTime(), datetimePkg.IST),
		MaxSlabAmount:    maxSlabAmount,
		Vendor:           request.GetVendor(),
	})
	if err != nil {
		lg.Error("error while getting interest", zap.Error(err), zap.String(logger.CATEGORY_ID, request.GetInterestRateCategory().String()))
		return nil, epifitemporal.NewTransientError(err)
	}
	currentInterestRatesFromVendor := p.parseInterestDetailsInfo(interestRateInfoResp, request.GetAccountType(), maxSlabAmount)
	storedInterestRates, err := p.interestRateDao.GetEffective(ctx, request.GetInterestEnquiryAt(), request.GetVendor(), request.GetInterestRateCategory(), request.GetAccountType(), []depositPb.InterestRateStatus{depositPb.InterestRateStatus_INTEREST_RATE_STATUS_ACTIVE})
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		lg.Error("error while getting stored interest rate", zap.Error(err), zap.String(logger.CATEGORY_ID, request.GetInterestRateCategory().String()), zap.String(logger.ACCOUNT_TYPE, request.GetAccountType().String()))
		return nil, epifitemporal.NewTransientError(err)
	}

	if isInterestRateUpdated(currentInterestRatesFromVendor, storedInterestRates.GetInterestDetails()) {
		lg.Info("interest rate got updated")
		return &activity.EvaluateDepositInterestRateMismatchResponse{
			InterestRateUpdateDetected: true,
			InterestRateDetails:        currentInterestRatesFromVendor,
		}, nil
	}
	return &activity.EvaluateDepositInterestRateMismatchResponse{
		InterestRateUpdateDetected: false,
	}, nil
}

func isInterestRateUpdated(currentInterestRatesFromVendor *depositPb.DepositInterestRateDetails, storedInterestRate *depositPb.DepositInterestRateDetails) bool {
	if storedInterestRate == nil {
		return true
	}
	// store all possible value
	storedInterestRateMap := make(map[int]string, 0)
	for _, interestRate := range storedInterestRate.GetDetails() {
		storedInterestRateMap[interestRate.GetTerm().GetTotalDays()] = interestRate.GetInterestRate()
	}
	for _, currentInterestRate := range currentInterestRatesFromVendor.GetDetails() {
		durationKey := currentInterestRate.GetTerm().GetTotalDays()
		if _, ok := storedInterestRateMap[durationKey]; !ok {
			return true
		}
		// if interest rate is getting update
		if storedInterestRateMap[durationKey] != currentInterestRate.GetInterestRate() {
			return true
		}
	}
	return false
}

func (p *Processor) parseInterestDetailsInfo(interestInfos []*vgDepositPb.InterestRateEntry, accountType accountPb.Type, maxSlabAmount *money.Money) *depositPb.DepositInterestRateDetails {
	res := &depositPb.DepositInterestRateDetails{Details: make([]*types.DepositInterestDetails, 0)}
	for _, interestInfo := range interestInfos {
		// take only max slab amount and consider that interest only
		if moneyPkg.AreEquals(interestInfo.GetMaxSlabAmount(), maxSlabAmount) {
			if p.checkIfDurationCanBeAddedByType(accountType, interestInfo) {
				res.Details = append(res.Details, &types.DepositInterestDetails{
					// since deposit valid till last 3 digit
					InterestRate: fmt.Sprintf("%.3f", interestInfo.InterestRate),
					Term:         interestInfo.GetEndPeriod(),
				})
			}
		}
	}
	return res
}

func (p *Processor) checkIfDurationCanBeAddedByType(accountType accountPb.Type, interestInfo *vgDepositPb.InterestRateEntry) bool {
	if accountType == accountPb.Type_SMART_DEPOSIT {
		// for sd type only one year interest matter
		if interestInfo.GetEndPeriod().GetTotalDays() <= p.workerConfig.MaxSDDuration {
			return true
		}
		return false
	}
	return true
}
