package activity

import (
	"context"
	"fmt"
	"strconv"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	actorPb "github.com/epifi/gamma/api/actor"
	notificationPb "github.com/epifi/gamma/api/celestial/activity/notification"
	commsPb "github.com/epifi/gamma/api/comms"
	deepLinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	upiActivityPb "github.com/epifi/gamma/api/upi/activity"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnbEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	upiWorkerConfig "github.com/epifi/gamma/upi/config/worker"
)

// GenerateNotificationForIntPaymentAction - generates the notification template for the user, when international payment for an upi account is activated/deactivated
// Flow:
// 1. Fetches the onboarding detail using client-req-id and checks if upi-onboarding-action has completed successfully
// 2. Fetches the userDetails using actor-id in order to get the phone number of the user
// 3. Generates notification for the user based on the type of upi-onboarding-action
//
//nolint:funlen
func (p *Processor) GenerateNotificationForIntPaymentAction(ctx context.Context, req *upiActivityPb.GenerateNotificationForIntPaymentActionRequest) (*upiActivityPb.GenerateNotificationForIntPaymentActionResponse, error) {
	var (
		res            = &upiActivityPb.GenerateNotificationForIntPaymentActionResponse{}
		lg             = activity.GetLogger(ctx)
		notification   *notificationPb.Notification
		userDetailResp *actorPb.GetEntityDetailsByActorIdResponse
		err            error
		upiOnbDetail   *upiOnboardingPb.UpiOnboardingDetail
	)
	upiOnbDetail, err = p.upiOnbDetailsDao.GetByClientRequestId(ctx, req.GetRequestHeader().GetClientReqId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		lg.Error("no onb details found corresponding to given client req id", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "no onb details found corresponding to given client req id")
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		lg.Error("invalid parameter passed while fetching onb details for clientReqId:", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "invalid parameter passed")
	case err != nil:
		lg.Error("failed to fetch upi onb details corresponding to given client req id", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch upi onb details corresponding to given client req id: %s", err))
	case upiOnbDetail.GetAction() != upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS &&
		upiOnbDetail.GetAction() != upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS:
		lg.Error("unexpected-action type received", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.UPI_ONBOARDING_DETAILS_ACTION_TYPE, upiOnbDetail.GetAction().String()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("invalid action type received: expected = %s or %s, got = %s",
			upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS.String(), upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS.String(),
			upiOnbDetail.GetAction().String()))
	case upiOnbDetail.GetStatus() != upiOnbEnumsPb.UpiOnboardingStatus_UPI_ONBOARDING_STATUS_SUCCESSFUL: // check if the desired action has completed successfully or not
		lg.Error("desired action is not yet completed successfully", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.STATUS, upiOnbDetail.GetStatus().String()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "workflow in unexpected state")
	}

	userDetailResp, err = p.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{
		ActorId: req.GetActorId(),
	})
	switch {
	case err != nil:
		lg.Error("failed to get user details", zap.String(logger.CLIENT_REQUEST_ID,
			req.GetRequestHeader().GetClientReqId()), zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in calling GetEntityDetailsByActorId rpc %s", err.Error()))
	case userDetailResp.GetStatus().IsRecordNotFound():
		lg.Error("no user details found for the given actor id",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "GetEntityDetailsByActorId rpc returned not found status")
	case userDetailResp.GetStatus().IsInvalidArgument():
		lg.Error("failed to get user details because invalid argument was passed",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "GetEntityDetailsByActorId returns invalid argument status")
	case !userDetailResp.GetStatus().IsSuccess():
		lg.Error("GetEntityDetailsByActorId rpc returned non success status", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()),
			zap.String(logger.STATUS_CODE, userDetailResp.GetStatus().String()))
		return nil, errors.Wrap(epifierrors.ErrPermanent, "GetEntityDetailsByActorId rpc returned non-success status")
	}

	if notification, err = p.generateIntPaymentActionNotificationDetails(upiOnbDetail, userDetailResp, p.config); err != nil {
		lg.Error("error generating international payment action notification details", zap.String(logger.UPI_ONBOARDING_DETAILS_ACTION_TYPE, upiOnbDetail.GetAction().String()),
			zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("failed to generate notification details for the international payment action: %s", upiOnbDetail.GetAction().String()))
	}
	if len(notification.GetCommunicationList()) == 0 {
		lg.Info("no notification template generated", zap.String(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()))
		return res, nil
	}

	res.Notification = notification
	return res, nil
}

// generateIntPaymentActionNotificationDetails will generate notification template to be sent to the user as PN when after international payment is activated/deactivated
func (p *Processor) generateIntPaymentActionNotificationDetails(upiOnbDetail *upiOnboardingPb.UpiOnboardingDetail, userDetail *actorPb.GetEntityDetailsByActorIdResponse, conf *upiWorkerConfig.Config) (*notificationPb.Notification, error) {

	var (
		commonTemplateFields *fcmPb.CommonTemplateFields
	)

	switch upiOnbDetail.GetAction() {
	case upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_ACTIVATE_INTERNATIONAL_PAYMENTS:
		commonTemplateFields = &fcmPb.CommonTemplateFields{
			Title: conf.UPINotificationParams.InternationalPaymentActivationNotification.Title,
			Body:  conf.UPINotificationParams.InternationalPaymentActivationNotification.Body,
			IconAttributes: &fcmPb.IconAttributes{
				IconUrl: conf.UPINotificationParams.InternationalPaymentActivationNotification.NotificationIconURL,
			},
			Deeplink: &deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_QUICK_ACCOUNT_DETAILS_SCREEN,
			},
		}
	case upiOnbEnumsPb.UpiOnboardingAction_UPI_ONBOARDING_ACTION_DEACTIVATE_INTERNATIONAL_PAYMENTS:
		commonTemplateFields = &fcmPb.CommonTemplateFields{
			Title: conf.UPINotificationParams.InternationalPaymentDeactivationNotification.Title,
			Body:  conf.UPINotificationParams.InternationalPaymentDeactivationNotification.Body,
			IconAttributes: &fcmPb.IconAttributes{
				IconUrl: conf.UPINotificationParams.InternationalPaymentDeactivationNotification.NotificationIconURL,
			},
			Deeplink: &deepLinkPb.Deeplink{
				Screen: deepLinkPb.Screen_QUICK_ACCOUNT_DETAILS_SCREEN,
			},
		}
	default:
		// Ideally this shouldn't happen as we have already verified the action type while fetching upi-onboarding-detail
		return nil, fmt.Errorf("unexpected upi-onboarding-action type received type: err = %w", epifierrors.ErrInvalidArgument)
	}

	return &notificationPb.Notification{
		UserIdentifier: &notificationPb.Notification_PhoneNumber{
			PhoneNumber: strconv.FormatUint(userDetail.GetMobileNumber().GetNationalNumber(), 10),
		},
		CommunicationList: []*commsPb.Communication{
			{
				Medium: commsPb.Medium_NOTIFICATION,
				Message: &commsPb.Communication_Notification{
					Notification: &commsPb.NotificationMessage{
						Notification: &fcmPb.Notification{
							NotificationType: fcmPb.NotificationType_SYSTEM_TRAY,
							NotificationTemplates: &fcmPb.Notification_SystemTrayTemplate{
								SystemTrayTemplate: &fcmPb.SystemTrayTemplate{
									CommonTemplateFields: commonTemplateFields,
								},
							},
						},
					},
				},
			},
		},
		QualityOfService: commsPb.QoS_GUARANTEED,
		ClientId:         upiOnbDetail.GetClientReqId(),
	}, nil
}
