package consumer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/epifi/be-common/api/rpc"

	payPkgTxns "github.com/epifi/gamma/pay/pkg/transactions"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/queue"

	"github.com/epifi/gamma/api/accounts"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	upiPb "github.com/epifi/gamma/api/upi"
	upiMandatePb "github.com/epifi/gamma/api/upi/mandate"
	vgUpiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	"github.com/epifi/gamma/api/vendors"
	federalUPI "github.com/epifi/gamma/api/vendors/federal/upi"
	upiPkg "github.com/epifi/gamma/pkg/upi"

	"github.com/jinzhu/copier"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type orderPayload interface {
	GetPaymentDetails() *orderPb.PaymentDetails
}

// unmarshalToFederalStruct unmarshal the bytes to federal struct based on the the consumer request
func unmarshalToFederalStruct(_ context.Context, req UPIConsumerRequest) (interface{}, error) {
	var (
		res          interface{}
		unmarshalErr error
	)
	switch req.(type) {
	case *upiPb.ProcessReqAuthRequest:
		target := &federalUPI.ReqAuthDetails{}
		unmarshalErr = xml.Unmarshal(req.GetRawData(), target)
		res = target
	case *upiPb.ProcessResPayRequest:
		target := &federalUPI.RespPay{}
		unmarshalErr = xml.Unmarshal(req.GetRawData(), target)
		res = target
	case *upiPb.ProcessReqTxnConfirmationRequest:
		target := &federalUPI.ReqTxnConfirmation{}
		unmarshalErr = xml.Unmarshal(req.GetRawData(), target)
		res = target
	case *upiPb.ProcessReqValAddressRequest:
		target := &federalUPI.ReqValAdd{}
		unmarshalErr = xml.Unmarshal(req.GetRawData(), target)
		res = target
	case *upiPb.ProcessListPspKeysRequest:
		target := &federalUPI.ListKeyResponse{}
		unmarshalErr = xml.Unmarshal(req.GetRawData(), target)
		res = target
	case *upiPb.ProcessListVaeRequest:
		target := &federalUPI.ListVaeResponse{}
		unmarshalErr = xml.Unmarshal(req.GetRawData(), target)
		res = target
	case *upiPb.ProcessReqTxnConfirmationComplaintRequest:
		target := &federalUPI.ReqTxnConfirmation{}
		unmarshalErr = xml.Unmarshal(req.GetRawData(), target)
		res = target
	case *upiPb.ProcessRespComplaintRequest:
		target := &federalUPI.ComplaintCallBackResponse{}
		unmarshalErr = xml.Unmarshal(req.GetRawData(), target)
		res = target
	default:
		return nil, fmt.Errorf("unsupported upi consumer request: %T", req)
	}

	if unmarshalErr != nil {
		return nil, fmt.Errorf("failed to unmarshal raw data: %w", unmarshalErr)
	}

	return res, nil
}

// createRespAuthReqFromReqAuthFederal helper method to create `RespAuthReq` VG proto struct from
// `ReqAuth`
// nolint
func (s *Service) createRespAuthReqFromReqAuthFederal(
	ctx context.Context,
	reqAuth *federalUPI.ReqAuthDetails,
	accDetail *upiPb.CustomerAccountDetails,
	info *upiPb.CustomerInformation,
	mandateEntity *upiMandatePb.MandateEntity,
	currentActorId string,
	isInvalid bool,
) (*vgUpiPb.RespAuthDetailsRequest, error) {
	var err error
	req := &vgUpiPb.RespAuthDetailsRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		Resp: &vgUpiPb.ResponseHeader{
			ReqMsgId: reqAuth.Head.Msg,
			Result:   vgUpiPb.ResponseHeader_SUCCESS,
		},
		ReqType:    reqAuthTypeMap[reqAuth.Txn.Type],
		UpiVersion: upiPb.ParseStringToVersion(reqAuth.Head.Ver),
	}

	if reqAuth != nil && reqAuth.Txn.Rules != nil {
		vgRules := reqAuth.Txn.Rules.ConvertToVGRulesProto(ctx)
		if vgRules != nil {
			req.Rules = &vgUpiPb.RespAuthDetailsRequest_Rules{}
			req.Rules.MinAmount = vgRules.GetMinAmount()
			req.Rules.ExpireAfter = vgRules.GetExpireAfter()
		}
	}

	req.TxnHeader, err = reqAuth.Txn.ConvertToVGTransactionHeader(vendors.UPIEpifiURL)
	if err != nil {
		return nil, fmt.Errorf("unable to parse transaction header for respAuth: %w", err)
	}

	req.Payer, err = reqAuth.Payer.ConvertToVGCustomerProto()
	if err != nil {
		return nil, fmt.Errorf("unable to parse payer for respAuth: %w", err)
	}

	// populate creds for mandate entities only if ReqAuth is valid(since these details are not required for errorsdeclines)
	if mandateEntity != nil && !isInvalid {
		creds := []*upiPb.CredBlock{
			{
				Type:    upiPb.CredBlock_UPI_MANDATE,
				SubType: upiPb.CredBlock_DS,
				Data: &upiPb.CredBlock_Data{
					Text: mandateEntity.GetSignedToken(),
				},
			},
		}
		req.Payer.Creds = creds
		if req.GetPayer().GetName() == "" {
			req.Payer.Name = info.GetIdentity().GetVerifiedName()
		}
		req.Payer.AccountDetails = accDetail
		req.Payer.Info = info
		req.Payer.Device, err = s.actorProcessor.GetInternalDeviceFromRegisteredDevice(ctx, currentActorId)
		if err != nil {
			return nil, fmt.Errorf("error fetching device details for actor :%s :%w", currentActorId, err)
		}
	}

	if len(reqAuth.Payees.Payee) != 0 {
		vgCust, err := reqAuth.Payees.Payee[0].ConvertToVGCustomerProto()
		if err != nil {
			return nil, fmt.Errorf("unable to parse payee for respAuth: %w", err)
		}
		if reqAuth.Payees.Payee[0].Code == "" || reqAuth.Payees.Payee[0].Code == vendors.PersonMccCode {
			vgCust.Merchant = nil
		}
		if mandateEntity == nil {
			vgCust.Name = info.GetIdentity().GetVerifiedName()
			vgCust.AccountDetails = accDetail
			vgCust.Info = info
		}
		// request type PAY implies currentActorId is payee, hence populating device details
		if reqAuth.GetRequestType() == vendors.UPITxnTypePay && len(currentActorId) > 0 {
			vgCust.Device, err = s.actorProcessor.GetInternalDeviceFromRegisteredDevice(ctx, currentActorId)
			if err != nil {
				// TODO(akk) - only logging error to begin with, modify to throw error after monitoring on prod
				logger.Error(ctx, "error getting internal device for payee during respAuth", zap.String(logger.REQUEST_ID, reqAuth.GetTransactionReqId()), zap.Error(err))
			}
		}
		req.Payees = []*upiPb.Customer{vgCust}
	}

	return req, nil
}

func createRespTxnReqFromReqTxnFederal(reqTxnConfirmation *federalUPI.ReqTxnConfirmation) (*vgUpiPb.RespTxnConfirmationRequest, error) {
	var err error
	req := &vgUpiPb.RespTxnConfirmationRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		Resp: &vgUpiPb.ResponseHeader{
			ReqMsgId: reqTxnConfirmation.Head.Msg,
			Result:   vgUpiPb.ResponseHeader_SUCCESS,
		},
		UpiVersion: upiPb.ParseStringToVersion(reqTxnConfirmation.Head.Ver),
	}

	req.TxnHeader, err = reqTxnConfirmation.Txn.ConvertToVGTransactionHeader(vendors.UPIEpifiURL)
	if err != nil {
		return nil, fmt.Errorf("unable to parse transaction header for respTxnConfirmation: %w", err)
	}

	return req, nil
}

func (s *Service) CreateResValAddFromReqValAddFederal(ctx context.Context, reqValAddress *federalUPI.ReqValAdd, errCode, result, verifiedName, ifsc string,
	merchantDetails *upiPb.MerchantDetails, pType vgUpiPb.PType, isUpiMandateAllowed bool) (*vgUpiPb.RespValidateAddressRequest, error) {
	var err error
	req := &vgUpiPb.RespValidateAddressRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		Resp: &vgUpiPb.RespValidateAddressRequest_Resp{
			ReqMsgId: reqValAddress.Head.Msg,
			Result:   result,
			MaskName: verifiedName,
			ErrCode:  errCode,
		},
		UpiVersion:       upiPb.ParseStringToVersion(reqValAddress.Head.Ver),
		EpifiCustomerVpa: strings.ToLower(reqValAddress.GetPayeeVPA()),
	}

	if isUpiMandateAllowed {

		if pType != vgUpiPb.PType_P_TYPE_UNSPECIFIED {
			req.Resp.PType = pType
		}

		req.Resp.FeatureSupported = &vgUpiPb.RespValidateAddressRequest_Resp_FeatureSupported{
			Value: vendors.MandateFeatureSupportedValue,
		}
	}

	if errCode == errorCodeSuccess {
		req.Resp.IFSC = ifsc
		req.Resp.AccountType = accounts.Type_SAVINGS

		if merchantDetails == nil {
			req.Resp.Code = vendors.UPIPersonMCCCode
			req.Resp.Type = upiPb.CustomerType_PERSON
		} else {
			req.Resp.Code = merchantDetails.SubCode
			req.Resp.Type = upiPb.CustomerType_ENTITY
			req.Resp.MerchantDetails = merchantDetails
		}
	}

	req.TxnHeader, err = reqValAddress.Txn.ConvertToVGTransactionHeader(vendors.UPIEpifiURL)
	if err != nil {
		return nil, fmt.Errorf("unable to parse transaction header for reqValAddress: %w", err)
	}

	return req, nil
}

// buildCreateOrderAndTxnReqFromFederalReqAuth helper method to generate order and transaction creation request for a given
// `reqAuth`
// nolint:funlen
func (s *Service) buildCreateOrderAndTxnReqFromFederalReqAuth(
	ctx context.Context,
	reqAuth *federalUPI.ReqAuthDetails,
	actorFrom, actorTo, piFrom, piTo string,
	payerInfo, payeeInfo *upiPb.Customer,
) (*orderPb.CreateOrderWithTransactionRequest, error) {
	var (
		err     error
		payload orderPayload
		res     = &orderPb.CreateOrderWithTransactionRequest{}
	)
	if reqAuth.Payer.Amount == nil {
		return nil, fmt.Errorf("amount should not be nil")
	}
	amt, err := money.ParseString(reqAuth.Payer.Amount.Value, reqAuth.Payer.Amount.Curr)
	if err != nil {
		return nil, fmt.Errorf("unable to parse amount: %s : %w", reqAuth.Payer.Amount.Value, err)
	}

	createTransactionReq, err := s.buildTxnReqFromReqAuthFederal(
		ctx,
		reqAuth,
		piFrom,
		piTo,
		amt,
		payerInfo,
		payeeInfo,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create txn request from reqAuth: %w", err)
	}

	orderCreationParam := &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
		ActorFrom:  actorFrom,
		ActorTo:    actorTo,
		Workflow:   reqTypeToWorkflowMap[reqAuth.Txn.Type],
		Provenance: orderPb.OrderProvenance_EXTERNAL,
	}

	// add MERCHANT orderTag for P2M transaction
	if reqAuth.GetTransactionType() == vendors.UPITxnTypePay {
		payerCode := reqAuth.GetPayerCode()
		if payerCode != "" && payerCode != vendors.UPIPersonMCCCode {
			orderCreationParam.Tags = append(orderCreationParam.Tags, orderPb.OrderTag_MERCHANT)
		}
	} else if reqAuth.GetTransactionType() == vendors.UPITxnTypeCollect {
		payeeCode := reqAuth.GetPayeeCode()
		if payeeCode != "" && payeeCode != vendors.UPIPersonMCCCode {
			orderCreationParam.Tags = append(orderCreationParam.Tags, orderPb.OrderTag_MERCHANT)
		}
	}

	paymentDetails := &orderPb.PaymentDetails{
		PiFrom:          piFrom,
		PiTo:            piTo,
		Amount:          amt,
		Remarks:         reqAuth.Txn.Note,
		ReqId:           reqAuth.Txn.ID,
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		MerchantRefId:   reqAuth.Txn.RefId,
		ReferenceUrl:    reqAuth.Txn.RefUrl,
		InitiationMode:  reqAuth.Txn.InitiationMode,
		Purpose:         reqAuth.Txn.Purpose,
	}

	switch reqAuth.Txn.Type {
	case vendors.UPITxnTypePay:
		orderCreationParam.Status = orderPb.OrderStatus_IN_PAYMENT
		payload = &orderPb.P2PFundTransfer{
			PaymentDetails: paymentDetails,
		}
	case vendors.UPITxnTypeCollect:
		orderCreationParam.Status = orderPb.OrderStatus_COLLECT_REGISTERED
		payload = &orderPb.P2PCollect{
			PaymentDetails: paymentDetails,
		}
	default:
		return nil, fmt.Errorf("unknown txn type: %s", reqAuth.Txn.Type)
	}

	marshaledPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal order payload: %w", err)
	}

	orderCreationParam.OrderPayload = marshaledPayload

	txnCreationParam := &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{}
	if err := copier.Copy(txnCreationParam, createTransactionReq); err != nil {
		return nil, fmt.Errorf("failed to copy create txn req: %w", err)
	}

	var expMin time.Duration
	// populate order expiry as per specified in the rules expire after
	// in case if the value is missing set expiry after 30 mins (default NPCI expiry)
	if txnCreationParam.GetReqInfo().GetUpiInfo().GetRules().GetExpireAfter() > 0 {
		expMin = time.Duration(txnCreationParam.GetReqInfo().GetUpiInfo().GetRules().GetExpireAfter()) * time.Minute
	} else {
		expMin = vendors.UPIDefaultExpiry
	}

	expireTime := txnCreationParam.GetReqInfo().GetUpiInfo().GetTxnOriginTimestamp().AsTime().Add(expMin)
	orderCreationParam.ExpireAt = timestamppb.New(expireTime)

	res.TransactionParam = txnCreationParam
	res.OrderParam = orderCreationParam
	res.Amount = amt

	return res, nil
}

// buildTxnReqFromReqAuthFederal helper to generate transaction creation request for a given `ReqAuth`
// nolint:funlen
func (s *Service) buildTxnReqFromReqAuthFederal(
	ctx context.Context,
	reqAuth *federalUPI.ReqAuthDetails,
	piFrom, piTo string,
	amt *moneyPb.Money,
	payerInfo, payeeInfo *upiPb.Customer,
) (*paymentPb.CreateTransactionRequest, error) {
	var (
		initiationMode, purpose string
	)
	txnOrgTs, err := datetime.ParseStringTimeStampProto(federalUPI.NPCI_TIMESTAMP_LAYOUT, reqAuth.Txn.Ts)
	if err != nil {
		return nil, fmt.Errorf("unable to parse ReqAuth txn timestamp: %s: %v: %w", reqAuth.Txn.Ts, err, queue.ErrPermanent)
	}
	initiationMode = reqAuth.Txn.InitiationMode
	purpose = reqAuth.Txn.Purpose

	if initiationMode == "" {
		initiationMode = vendors.DefaultInitiationMode
	}
	if purpose == "" {
		purpose = vendors.DefaultPurpose
	}

	// TODO(nitesh): populate txn rules and risk scores
	createTransactionReq := &paymentPb.CreateTransactionRequest{
		PiFrom:          piFrom,
		PiTo:            piTo,
		Amount:          amt,
		Remarks:         reqAuth.Txn.Note,
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		Status:          paymentPb.TransactionStatus_CREATED,
		ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
		Utr:             reqAuth.Txn.CustRef,
		ReqInfo: &paymentPb.PaymentRequestInformation{
			ReqId: reqAuth.Txn.ID,
			UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
				MerchantRefId:      reqAuth.Txn.RefId,
				MsgId:              reqAuth.Head.Msg,
				RefUrl:             reqAuth.Txn.RefUrl,
				TxnOriginTimestamp: txnOrgTs,
				InitiationMode:     initiationMode,
				Purpose:            purpose,
				TransactionType:    txnTypeStrToEnumMap[reqAuth.GetTransactionType()],
				RiskScoreList:      reqAuth.GetRiskScores(),
				PayerVpa:           payerInfo.GetPaymentAddress(),
				PayeeVpa:           payeeInfo.GetPaymentAddress(),
				PayerUpiNumber:     payerInfo.GetCmId(),
				PayeeUpiNumber:     payeeInfo.GetCmId(),
				SeqNum:             payerInfo.GetSeqNum(),
				RefCategory:        reqAuth.Txn.RefCategory,
			},
		},
	}

	payerPi := piFrom
	payeePi := piTo

	if reqAuth.GetTransactionType() == vendors.UPITxnTypeCollect {
		payerPi = piTo
		payeePi = piFrom
	}
	if createTransactionReq.Ownership, err = payPkgTxns.GetOwnershipForTheTxn(ctx, payerPi, payeePi, createTransactionReq.GetPaymentProtocol(),
		s.piClient, s.conf.NonTpapPspHandles); err != nil {
		logger.Error(ctx, "error deciding ownership for the transaction", zap.String(logger.PI_ID, piFrom),
			zap.Error(err))
		return nil, rpc.StatusAsError(rpc.StatusInternal())
	}

	if reqAuth.Txn.Rules != nil && len(reqAuth.Txn.Rules.Rule) != 0 {
		createTransactionReq.ReqInfo.UpiInfo.Rules = &upiPb.Rules{}
		for _, rule := range reqAuth.Txn.Rules.Rule { //nolint:gocritic
			switch rule.Name {
			case vendors.UPIRuleTagExpireAfter:
				expAfter, err := strconv.Atoi(rule.Value)
				if err != nil {
					logger.Error(ctx, fmt.Sprintf("failed to parse UPI rule: %s", vendors.UPIRuleTagExpireAfter), zap.Error(err))
					continue
				}
				createTransactionReq.ReqInfo.UpiInfo.Rules.ExpireAfter = uint32(expAfter)
			case vendors.UPIRuleTagMinAmount:
				amt, err := money.ParseString(rule.Value, reqAuth.Payer.Amount.Curr)
				if err != nil {
					logger.Error(ctx, fmt.Sprintf("failed to parse UPI rule: %s", vendors.UPIRuleTagMinAmount), zap.Error(err))
					continue
				}

				createTransactionReq.ReqInfo.UpiInfo.Rules.MinAmount = amt
			}
		}
	}

	if reqAuth.Txn.Type == vendors.UPITxnTypePay {
		createTransactionReq.Status = paymentPb.TransactionStatus_IN_PROGRESS
		createTransactionReq.ReqInfo.UpiInfo.Mcc = reqAuth.Payer.Code
		createTransactionReq.ReqInfo.UpiInfo.MerchantDetails = reqAuth.Payer.Merchant.ConvertToBEMerchant(reqAuth.Payer.Code)
	} else if len(reqAuth.Payees.Payee) != 0 {
		payee := reqAuth.Payees.Payee[0]
		createTransactionReq.ReqInfo.UpiInfo.Mcc = payee.Code

		cust, err := payee.ConvertToVGCustomerProto()
		if err != nil {
			return nil, fmt.Errorf("failed to conver to customer proto: %v: %w", err, epifierrors.ErrPermanent)
		}

		// dumping the payee info and account details in DB so that we can
		// re-use the data while forming the respAuth on collect approval
		createTransactionReq.ReqInfo.UpiInfo.CustomerInfo = cust.GetInfo()
		createTransactionReq.ReqInfo.UpiInfo.CustomerAccountInfo = cust.GetAccountDetails()
		createTransactionReq.ReqInfo.UpiInfo.MerchantDetails = cust.GetMerchant()
	}

	return createTransactionReq, nil
}

// buildCreateOrderAndTxnReqFromFederalReqTxnConfirmation helper method to generate order and transaction creation request for a given
// `reqTxnConfirmation`
// nolint
func (s *Service) buildCreateOrderAndTxnReqFromFederalReqTxnConfirmation(ctx context.Context, reqTxnConfirmation *federalUPI.ReqTxnConfirmation, actorFrom,
	actorTo, piFrom, piTo string) (*orderPb.CreateOrderWithTransactionRequest, error) {
	var (
		err     error
		payload orderPayload
		res     = &orderPb.CreateOrderWithTransactionRequest{}
	)

	payerInfo, err := reqTxnConfirmation.GetPayerInfo()
	if err != nil {
		return nil, fmt.Errorf("failed to get payer info: %w", err)
	}

	amt := payerInfo.GetAmount()
	if amt == nil {
		return nil, fmt.Errorf("amount should not be nil")
	}
	orderStatus, ok := reqTxnConfirmationResultToOrderStatusMap[reqTxnConfirmation.TxnConfirmation.OrgStatus]
	if !ok {
		return nil, fmt.Errorf("unable to map req txn confirmation result %q to order status",
			reqTxnConfirmation.TxnConfirmation.OrgStatus)
	}
	createTransactionReq, err := s.buildTxnReqFromReqTxnConfirmationFederal(ctx, reqTxnConfirmation, piFrom, piTo, amt)
	if err != nil {
		return nil, fmt.Errorf("failed to create txn request from reqTxnConfirmation: %w", err)
	}

	paymentDetails := &orderPb.PaymentDetails{
		PiFrom:          piFrom,
		PiTo:            piTo,
		Amount:          amt,
		Remarks:         reqTxnConfirmation.Txn.Note,
		ReqId:           reqTxnConfirmation.Txn.ID,
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		MerchantRefId:   reqTxnConfirmation.Txn.RefId,
		ReferenceUrl:    reqTxnConfirmation.Txn.RefUrl,
		InitiationMode:  reqTxnConfirmation.Txn.InitiationMode,
		Purpose:         reqTxnConfirmation.Txn.Purpose,
	}

	orderCreationParam := &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
		ActorFrom:  actorFrom,
		ActorTo:    actorTo,
		Workflow:   reqTypeToWorkflowMap[reqTxnConfirmation.GetTxnType()],
		Provenance: orderPb.OrderProvenance_EXTERNAL,
		Status:     orderStatus,
	}

	if reqTxnConfirmation.TxnConfirmation.Type == vendors.UPITxnTypePay {
		payload = &orderPb.P2PFundTransfer{
			PaymentDetails: paymentDetails,
		}
	} else {
		return nil, fmt.Errorf("unknown txn type: %s", reqTxnConfirmation.Txn.Type)
	}

	marshaledPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal order payload: %w", err)
	}

	orderCreationParam.OrderPayload = marshaledPayload

	txnCreationParam := &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{}
	if err := copier.Copy(txnCreationParam, createTransactionReq); err != nil {
		return nil, fmt.Errorf("failed to copy create txn req: %w", err)
	}

	res.OrderParam = orderCreationParam
	res.TransactionParam = txnCreationParam
	res.Amount = amt
	return res, nil
}

// buildTxnReqFromReqTxnConfirmationFederal helper to generate transaction creation request for a given `ReqTxnConfirmation`
// nolint
func (s *Service) buildTxnReqFromReqTxnConfirmationFederal(ctx context.Context, reqTxnConfirmation *federalUPI.ReqTxnConfirmation, piFrom, piTo string,
	amt *moneyPb.Money) (*paymentPb.CreateTransactionRequest, error) {
	var (
		transactionDetailedStatus = &paymentPb.TransactionDetailedStatus_DetailedStatus{}
	)
	txnOrgTs, err := upiPkg.TimestampFromString(reqTxnConfirmation.Txn.Ts)
	if err != nil {
		return nil, fmt.Errorf("unable to parse txn timestamp: %s: %w", reqTxnConfirmation.Txn.Ts, err)
	}

	txnExecutedTs, err := reqTxnConfirmation.GetHeadTs()
	if err != nil {
		return nil, fmt.Errorf("unable to parse txn execution timestamp: %s: %w", reqTxnConfirmation.Head.Ts, err)
	}

	if txnExecutedTs.AsTime().IsZero() || txnExecutedTs == nil {
		logger.Warn("received invalid head timestamp using current timestamp")
		txnExecutedTs = timestamppb.Now()
	}

	txnStatus, ok := resultToTxnStatusMap[reqTxnConfirmation.TxnConfirmation.OrgStatus]
	if !ok {
		return nil, fmt.Errorf("failed to map req txn confirmation result: %q to txn status",
			reqTxnConfirmation.TxnConfirmation.OrgStatus)
	}

	transactionDetailedStatus = reqTxnConfirmation.GetTxnDetailedStatus()

	createTransactionReq := &paymentPb.CreateTransactionRequest{
		PiFrom:          piFrom,
		PiTo:            piTo,
		Amount:          amt,
		Remarks:         reqTxnConfirmation.Txn.Note,
		PaymentProtocol: paymentPb.PaymentProtocol_UPI,
		Status:          txnStatus,
		ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_TXN_CONFIRMATION_RECEIVED,
		ExecutedAt:      txnExecutedTs,
		Utr:             reqTxnConfirmation.Txn.CustRef,
		ReqInfo: &paymentPb.PaymentRequestInformation{
			ReqId: reqTxnConfirmation.Txn.ID,
			UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
				MerchantRefId:      reqTxnConfirmation.Txn.RefId,
				MsgId:              reqTxnConfirmation.Head.Msg,
				RefUrl:             reqTxnConfirmation.Txn.RefUrl,
				TxnOriginTimestamp: txnOrgTs,
				InitiationMode:     reqTxnConfirmation.Txn.InitiationMode,
				Purpose:            reqTxnConfirmation.Txn.Purpose,
				RiskScoreList:      reqTxnConfirmation.GetRiskScores(),
				RefCategory:        reqTxnConfirmation.Txn.RefCategory,
			},
		},
		DetailedStatus: &paymentPb.TransactionDetailedStatus{
			DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
				transactionDetailedStatus,
			},
		},
	}

	payerPi := piFrom
	payeePi := piTo

	if reqTxnConfirmation.GetTxnType() == vendors.UPITxnTypeCollect {
		payerPi = piTo
		payeePi = piFrom
	}
	if createTransactionReq.Ownership, err = payPkgTxns.GetOwnershipForTheTxn(ctx, payerPi, payeePi,
		createTransactionReq.GetPaymentProtocol(), s.piClient, s.conf.NonTpapPspHandles); err != nil {
		logger.Error(ctx, "error deciding ownership for the transaction", zap.String(logger.PI_ID, piFrom),
			zap.Error(err))
		return nil, rpc.StatusAsError(rpc.StatusInternal())
	}

	return createTransactionReq, nil
}
