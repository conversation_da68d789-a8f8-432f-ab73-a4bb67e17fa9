package consumer_test

import (
	"context"
	"encoding/xml"
	"fmt"
	"reflect"
	"testing"
	"time"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	eventMock "github.com/epifi/be-common/pkg/events/mocks"
	queueMocks "github.com/epifi/be-common/pkg/queue/mocks"

	"github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	actorMocks "github.com/epifi/gamma/api/actor/mocks"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/mocks"
	orderPb "github.com/epifi/gamma/api/order"
	orderMocks "github.com/epifi/gamma/api/order/mocks"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	paymentMocks "github.com/epifi/gamma/api/order/payment/mocks"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	piMocks "github.com/epifi/gamma/api/paymentinstrument/mocks"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	mockRecurPaymentClient "github.com/epifi/gamma/api/recurringpayment/mocks"
	accountPb "github.com/epifi/gamma/api/typesv2/account"
	upiPb "github.com/epifi/gamma/api/upi"
	"github.com/epifi/gamma/api/upi/mandate"
	upiMocks "github.com/epifi/gamma/api/upi/mocks"
	vgUPIPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	vgMocks "github.com/epifi/gamma/api/vendorgateway/openbanking/upi/mocks"
	"github.com/epifi/gamma/api/vendors"
	federalUPI "github.com/epifi/gamma/api/vendors/federal/upi"
	"github.com/epifi/gamma/upi/config"
	"github.com/epifi/gamma/upi/config/genconf"
	"github.com/epifi/gamma/upi/consumer"
	daoMocks "github.com/epifi/gamma/upi/dao/mocks"
	actorProcessorMocks "github.com/epifi/gamma/upi/helper/actor/mocks"
	upiOnboardingProcessorMocks "github.com/epifi/gamma/upi/helper/mocks"
	piHelperMocks "github.com/epifi/gamma/upi/helper/paymentinstrument/mocks"
	timelineHelper "github.com/epifi/gamma/upi/helper/timeline"
	timelineHelperMocks "github.com/epifi/gamma/upi/helper/timeline/mocks"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
)

const (
	reqAuthTimeoutErrCode = "X1"
	disabledVPAErrCode    = "ZG"
)

type reqAuthTestSuite struct {
	conf           *config.Config
	consumerServer upiPb.ConsumerServer
}

func newReqAuthTestSuite(conf *config.Config, consumerServer upiPb.ConsumerServer) reqAuthTestSuite {
	return reqAuthTestSuite{conf: conf, consumerServer: consumerServer}
}

type createOrderArgMatcher struct {
	want *orderPb.CreateOrderWithTransactionRequest
}

func newCreateOrderArgMatcher(want *orderPb.CreateOrderWithTransactionRequest) *createOrderArgMatcher {
	return &createOrderArgMatcher{want: want}
}

func (c *createOrderArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*orderPb.CreateOrderWithTransactionRequest)
	if !ok {
		return false
	}

	c.want.OrderParam.OrderPayload = got.OrderParam.OrderPayload
	c.want.OrderParam.ExpireAt = got.OrderParam.ExpireAt
	c.want.TransactionParam.ReqInfo = got.TransactionParam.ReqInfo
	return reflect.DeepEqual(c.want, got)
}

func (c *createOrderArgMatcher) String() string {
	return fmt.Sprintf("want: %v", c.want)
}

// enables custom argument matching logic for update txn request
// can be used to generate field which are auto generates inside the code
type respAuthDetailsArgMatcher struct {
	want *vgUPIPb.RespAuthDetailsRequest
}

func newRespAuthDetailsArgMatcher(want *vgUPIPb.RespAuthDetailsRequest) *respAuthDetailsArgMatcher {
	return &respAuthDetailsArgMatcher{want: want}
}

func (r *respAuthDetailsArgMatcher) Matches(x interface{}) bool {
	got, ok := x.(*vgUPIPb.RespAuthDetailsRequest)
	if !ok {
		return false
	}

	if r.want == nil {
		return true
	}

	return r.want.Resp.ErrCode == got.Resp.ErrCode && r.want.Resp.Result == got.Resp.Result
}

func (r *respAuthDetailsArgMatcher) String() string {
	return r.want.String()
}

var (
	rts reqAuthTestSuite

	reqAuth = &federalUPI.ReqAuthDetails{
		Head: federalUPI.Head{
			Msg:   "FDR121",
			OrgId: "40002",
		},
		Txn: federalUPI.Transaction{
			ID:             "FDR0001",
			RefId:          "FDR0002",
			Type:           "PAY",
			CustRef:        "************",
			RefUrl:         "npci.com",
			Ts:             "2018-02-17T13:39:54.944+05:30",
			InitiationMode: "00",
		},
		Payer: federalUPI.Customer{
			Addr:   "jack@okaxis",
			Code:   "0000",
			Name:   "jack",
			SeqNum: "1",
			Type:   "PERSON",
			Info: &federalUPI.Info{
				Identity: struct {
					ID           string `xml:"id,attr,omitempty"`
					Type         string `xml:"type,attr,omitempty"`
					VerifiedName string `xml:"verifiedName,attr,omitempty"`
				}{
					ID:           "random-acc-number",
					Type:         "ACCOUNT",
					VerifiedName: "jack",
				},
				Rating: struct {
					VerifiedAddress string `xml:"verifiedAddress,attr,omitempty"`
				}{
					VerifiedAddress: "TRUE",
				},
			},
			Ac: &federalUPI.Ac{
				AddrType: "ACCOUNT",
				Details: []federalUPI.Detail{
					{
						Name:  "ACTYPE",
						Value: "SAVINGS",
					},
					{
						Name:  "ACNUM",
						Value: "random-acc-number",
					},
					{
						Name:  "IFSC",
						Value: "AXIS0000058",
					},
				},
			},
			Amount: &federalUPI.Amount{
				Value: "2.00",
				Curr:  "INR",
			},
		},
		Payees: federalUPI.Payees{
			Payee: []federalUPI.Customer{
				{
					Addr:   "johnwick@okaxis",
					Code:   "0000",
					SeqNum: "1",
					Type:   "PERSON",
					Name:   "john wick",
					Amount: &federalUPI.Amount{
						Value: "2.00",
						Curr:  "INR",
					},
					Ac: &federalUPI.Ac{
						AddrType: "ACCOUNT",
						Details: []federalUPI.Detail{
							{
								Name:  "ACTYPE",
								Value: "SAVINGS",
							},
							{
								Name:  "ACNUM",
								Value: "random-acc-number#1",
							},
							{
								Name:  "IFSC",
								Value: "ICICI0000058",
							},
						},
					},
				},
			},
		},
	}
)

func TestService_ProcessReqAuth(t *testing.T) {
	expireRawData, err := xml.Marshal(reqAuth)
	require.Nil(t, err)

	reqAuth.Txn.Type = vendors.UPITxnTypeCollect
	expireCollectRawData, err := xml.Marshal(reqAuth)
	require.Nil(t, err)

	reqAuth.Txn.Type = vendors.UPITxnTypePay
	reqAuth.Txn.Ts = time.Now().Format("2006-01-02T15:04:05.000-07:00")
	rawData, err := xml.Marshal(reqAuth)
	require.Nil(t, err)
	oldPayerDetails := reqAuth.Payer.Ac.Details
	reqAuth.Payer.Ac.Details = []federalUPI.Detail{
		{
			Name:  "ACTYPE",
			Value: "UOD",
		},
		{
			Name:  "ACNUM",
			Value: "random-acc-number",
		},
		{
			Name:  "IFSC",
			Value: "AXIS0000058",
		},
	}
	uodPayerInfo, uodErr := reqAuth.GetPayerInfo()
	require.NoError(t, uodErr)
	uodPayerData, uodPayErr := xml.Marshal(reqAuth)
	reqAuth.Payer.Ac.Details = oldPayerDetails
	require.NoError(t, uodPayErr)

	oldPayeeAddr := reqAuth.Payees.Payee[0].Addr
	reqAuth.Payees.Payee[0].Addr = "ccrepay@fifederal"
	ccRepayData, ccErr := xml.Marshal(reqAuth)
	require.NoError(t, ccErr)
	reqAuth.Payees.Payee[0].Addr = oldPayeeAddr

	reqAuth.Txn.Type = vendors.UPITxnTypeCollect
	reqAuth.Txn.Ts = time.Now().Format("2006-01-02T15:04:05.000-07:00")
	txnOrgTs, err := datetime.ParseStringTimeStampProto(federalUPI.NPCI_TIMESTAMP_LAYOUT, reqAuth.Txn.Ts)
	if err != nil {
		t.Errorf("error in parsing timestamp string %v %v", reqAuth.Txn.Ts, err)
	}
	collectRawData, err := xml.Marshal(reqAuth)
	require.Nil(t, err)

	payerInfo, err := reqAuth.GetPayerInfo()
	require.Nil(t, err)

	payeeInfo, err := reqAuth.GetPayeeInfo()
	require.Nil(t, err)

	txnHeader, err := reqAuth.Txn.ConvertToVGTransactionHeader(vendors.UPIEpifiURL)
	require.Nil(t, err)

	reqAuth.Payer.Addr = "test@fede"
	oldHandleCollectData, err := xml.Marshal(reqAuth)
	require.Nil(t, err)

	oldHandleCollectPayerInfo, err := reqAuth.GetPayerInfo()
	require.Nil(t, err)

	type mockPublish struct {
		enable  bool
		payload interface{}
		delay   time.Duration
		msgId   string
		err     error
	}

	type mockGetTransaction struct {
		enable bool
		req    *paymentPb.GetTransactionRequest
		res    *paymentPb.GetTransactionResponse
		err    error
	}
	type mockUpdateTransaction struct {
		req *paymentPb.UpdateTransactionRequest
		res *paymentPb.UpdateTransactionResponse
		err error
	}
	type mockRespAuth struct {
		enable bool
		req    *vgUPIPb.RespAuthDetailsRequest
		res    *vgUPIPb.RespAuthDetailsResponse
		err    error
	}
	type mockCreateOrderAndTxn struct {
		enable bool
		req    *orderPb.CreateOrderWithTransactionRequest
		res    *orderPb.CreateOrderWithTransactionResponse
		err    error
	}
	type mockGetOrder struct {
		enable bool
		req    *orderPb.GetOrderRequest
		res    *orderPb.GetOrderResponse
		err    error
	}
	type mockGetPIsByIds struct {
		enable bool
		req    *piPb.GetPIsByIdsRequest
		res    *piPb.GetPIsByIdsResponse
		err    error
	}
	type mockGetRelationshipWithActor struct {
		enable bool
		req    *actorPb.GetRelationshipWithActorRequest
		res    *actorPb.GetRelationshipWithActorResponse
		err    error
	}
	type mockResolveTimelineHelper struct {
		enable  bool
		payer   *upiPb.Customer
		payee   *upiPb.Customer
		reqType string
		res     *timelineHelper.ResolveTimelineResult
		err     error
	}
	type mockGetPIFromVPA struct {
		enable bool
		vpa    string
		pi     *piPb.PaymentInstrument
		err    error
	}
	type mockGetPhoneNumber struct {
		enable  bool
		actorId string
		ph      *commontypes.PhoneNumber
		err     error
	}
	type mockGetActorIdFromPI struct {
		enable  bool
		PiId    string
		actorId string
		err     error
	}
	type mockGetByUmnDao struct {
		enable        bool
		umn           string
		mandateEntity *mandate.MandateEntity
		err           error
	}
	type mockExecuteRecurringPayment struct {
		enable bool
		req    *recurringPaymentPb.ExecuteRecurringPaymentRequest
		res    *recurringPaymentPb.ExecuteRecurringPaymentResponse
		err    error
	}
	type mockGetPiById struct {
		enable bool
		piId   string
		pi     *piPb.PaymentInstrument
		err    error
	}
	type mockGetDeviceDetails struct {
		enable bool
		req    *authPb.GetDeviceDetailsRequest
		res    *authPb.GetDeviceDetailsResponse
		err    error
	}
	type mockGetInternalDeviceForActor struct {
		enable  bool
		actorId string
		res     *upiPb.Device
		err     error
	}
	type mockGetOrderId struct {
		enable bool
		req    *paymentPb.GetOrderIdRequest
		res    *paymentPb.GetOrderIdResponse
		err    error
	}
	type mockGetVerifiedAddressEntryByVpa struct {
		enable    bool
		vpa       string
		vaeEntity *upiPb.VerifiedAddressEntry
		err       error
	}
	type mockIsNewVpaHandleAssignedToActor struct {
		enable  bool
		actorId string
		res     bool
	}

	tests := []struct {
		name                              string
		req                               *upiPb.ProcessReqAuthRequest
		mockGetTransaction                mockGetTransaction
		mockUpdateTransactions            []mockUpdateTransaction
		mockRespAuth                      mockRespAuth
		mockCreateOrderAndTxn             mockCreateOrderAndTxn
		mockGetOrder                      []mockGetOrder
		mockGetOrderId                    mockGetOrderId
		mockGetPIsByIds                   []mockGetPIsByIds
		mockGetRelationshipWithActor      mockGetRelationshipWithActor
		mockResolveTimelineHelper         mockResolveTimelineHelper
		mockGetPIFromVPA                  []mockGetPIFromVPA
		mockExecuteRecurringPayment       mockExecuteRecurringPayment
		mockGetPhoneNumber                []mockGetPhoneNumber
		mockGetActorIdFromPI              []mockGetActorIdFromPI
		mockGetByUmnDao                   mockGetByUmnDao
		mockGetPiById                     []mockGetPiById
		mockGetDeviceDetails              []mockGetDeviceDetails
		mockGetInternalDeviceForActor     []mockGetInternalDeviceForActor
		mockPublish                       mockPublish
		mockGetVerifiedAddressEntryByVpa  mockGetVerifiedAddressEntryByVpa
		mockIsNewVpaHandleAssignedToActor mockIsNewVpaHandleAssignedToActor
		want                              *upiPb.ProcessReqAuthResponse
		wantErr                           bool
	}{
		{
			name: "successful processing PAY federal",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetActorIdFromPI: []mockGetActorIdFromPI{
				{
					enable:  true,
					PiId:    "pi-1",
					actorId: "actor-1",
					err:     nil,
				},
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT,
						PiFrom:         "pi-1",
						PiTo:           "pi-2",
					},
				},
				err: nil,
			},
			mockGetOrderId: mockGetOrderId{
				enable: true,
				req:    &paymentPb.GetOrderIdRequest{TransactionId: "random-txn-id"},
				res:    &paymentPb.GetOrderIdResponse{Status: rpc.StatusOk(), OrderId: "order-1"},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: "order-1"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order:  &orderPb.Order{Id: "order-1", ToActorId: "actor-1"},
					},
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										StatusCodePayer: "00",
										StatusCodePayee: "00",
										State:           paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										RawStatusCode:   "00",
										Api:             paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_SUCCESS, ErrCode: ""}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:        rpc.StatusOk(),
					ReqMsgId:      "random-msg-id",
					RawStatusCode: "00",
					StatusCode:    "00",
				},
				err: nil,
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{
				{
					enable:  true,
					actorId: "actor-1",
					res:     &upiPb.Device{},
				},
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{"pi-1", "pi-2"},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:    "pi-2",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},

			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    reqAuth.GetPayeeVPA(),
					pi: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},
			mockGetPhoneNumber: []mockGetPhoneNumber{
				{
					enable:  true,
					actorId: "actor-1",
					ph:      &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
					err:     nil,
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockPublish: mockPublish{
				enable: true,
				payload: &paymentPb.ProcessPaymentRequest{
					TransactionId: "random-txn-id",
					ActorId:       "actor-1",
				},
				delay: upiConf.PaymentStatusEnquiryDelay,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "unsupported bank",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_EXOTEL,
				RawData:       nil,
			},
			mockGetTransaction: mockGetTransaction{
				enable: false,
				err:    nil,
			},
			mockUpdateTransactions: nil,
			mockRespAuth: mockRespAuth{
				enable: false,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "get transaction RPC failure",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: nil,
				err: fmt.Errorf("RPC failure"),
			},
			mockUpdateTransactions: nil,
			mockRespAuth: mockRespAuth{
				enable: false,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "delete event since resp pay already sent for txn",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT,
						PiFrom:         "pi-1",
						PiTo:           "pi-2",
					},
				},
				err: nil,
			},
			mockGetOrderId: mockGetOrderId{
				enable: true,
				req:    &paymentPb.GetOrderIdRequest{TransactionId: "random-txn-id"},
				res:    &paymentPb.GetOrderIdResponse{Status: rpc.StatusOk(), OrderId: "order-1"},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: "order-1"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order:  &orderPb.Order{Id: "order-1"},
					},
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockUpdateTransactions: nil,
			mockRespAuth: mockRespAuth{
				enable: false,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "transient error while sending resp auth",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						PiFrom:         "pi-1",
						PiTo:           "pi-2",
					},
				},
				err: nil,
			},
			mockGetOrderId: mockGetOrderId{
				enable: true,
				req:    &paymentPb.GetOrderIdRequest{TransactionId: "random-txn-id"},
				res:    &paymentPb.GetOrderIdResponse{Status: rpc.StatusOk(), OrderId: "order-1"},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: "order-1"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order:  &orderPb.Order{Id: "order-1", ToActorId: "actor-1"},
					},
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    reqAuth.GetPayeeVPA(),
					pi: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},
			mockGetActorIdFromPI: []mockGetActorIdFromPI{
				{
					enable:  true,
					PiId:    "pi-1",
					actorId: "actor-1",
					err:     nil,
				},
			},
			mockGetPhoneNumber: []mockGetPhoneNumber{
				{
					enable:  true,
					actorId: "actor-1",
					ph:      &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
					err:     nil,
				},
			},
			mockGetPIsByIds: []mockGetPIsByIds{{
				enable: true,
				req: &piPb.GetPIsByIdsRequest{
					Ids: []string{
						"pi-1",
						"pi-2",
					},
				},
				res: &piPb.GetPIsByIdsResponse{
					Status: rpc.StatusOk(),
					Paymentinstruments: []*piPb.PaymentInstrument{
						{
							Id:    "pi-1",
							State: piPb.PaymentInstrumentState_CREATED,
							Capabilities: map[string]bool{
								piPb.Capability_INBOUND_TXN.String():  true,
								piPb.Capability_OUTBOUND_TXN.String(): true,
							},
						},
						{
							Id:    "pi-2",
							State: piPb.PaymentInstrumentState_CREATED,
							Capabilities: map[string]bool{
								piPb.Capability_INBOUND_TXN.String():  true,
								piPb.Capability_OUTBOUND_TXN.String(): true,
							},
						},
					},
				},
				err: nil},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{
				{
					enable:  true,
					actorId: "actor-1",
					res:     &upiPb.Device{},
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    nil,
				res:    nil,
				err:    fmt.Errorf("RPC error"),
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										ErrorCategory:          paymentPb.TransactionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
										SystemErrorDescription: "failed to send respAuth: RPC error : rpc call failed: transient failure",
										Api:                    paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
				},
			},
			mockPublish: mockPublish{
				enable: true,
				payload: &paymentPb.ProcessPaymentRequest{
					TransactionId: "random-txn-id",
					ActorId:       "actor-1",
				},
				delay: upiConf.PaymentStatusEnquiryDelay,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "transient error in case of updating transaction state machine",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT,
						PiFrom:         "pi-1",
						PiTo:           "pi-2",
					},
				},
				err: nil,
			},
			mockGetOrderId: mockGetOrderId{
				enable: true,
				req:    &paymentPb.GetOrderIdRequest{TransactionId: "random-txn-id"},
				res:    &paymentPb.GetOrderIdResponse{Status: rpc.StatusOk(), OrderId: "order-1"},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: "order-1"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order:  &orderPb.Order{Id: "order-1"},
					},
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusInternal(),
					},
					err: nil,
				},
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "incoming PAY req auth",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: false,
					vpa:    reqAuth.GetPayerVPA(),
					pi: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					vpa:    reqAuth.GetPayeeVPA(),
					pi: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},
			mockGetByUmnDao: mockGetByUmnDao{
				enable:        false,
				umn:           "jack@okaxis",
				mandateEntity: nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			mockGetActorIdFromPI: []mockGetActorIdFromPI{
				{
					enable:  true,
					PiId:    "pi-1",
					actorId: "actor-1",
					err:     nil,
				},
			},
			mockGetPhoneNumber: []mockGetPhoneNumber{
				{
					enable:  true,
					actorId: "actor-1",
					ph:      &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
					err:     nil,
				},
			},
			mockCreateOrderAndTxn: mockCreateOrderAndTxn{
				enable: true,
				req: &orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:  "external-actor-1",
						ActorTo:    "internal-actor-1",
						Workflow:   orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						Provenance: orderPb.OrderProvenance_EXTERNAL,
						Status:     orderPb.OrderStatus_IN_PAYMENT,
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiFrom:          "pi-ex-1",
						PiTo:            "pi-1",
						Remarks:         reqAuth.Txn.Note,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_IN_PROGRESS,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						Utr:             "************",
						Ownership:       commontypes.Ownership_EPIFI_TECH,
					},
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2,
					},
				},
				res: &orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-1",
						FromActorId: "external-actor-1",
						ToActorId:   "internal-actor-1",
						Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						Provenance:  orderPb.OrderProvenance_EXTERNAL,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2,
						},
						Status: orderPb.OrderStatus_IN_PAYMENT,
					},
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						PiFrom:         "pi-ex-1",
						PiTo:           "pi-1",
					},
				},
				err: nil,
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-ex-1",
								State:                piPb.PaymentInstrumentState_CREATED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Type:                 piPb.PaymentInstrumentType_UPI,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra@fifederal",
									},
								},
							},
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:    "pi-2",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-ex-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
				{
					enable: true,
					piId:   "pi-ex-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-ex-1",
							PiTo:           "pi-1",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
							paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT,
							PiFrom:         "pi-ex-1",
							PiTo:           "pi-1",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										RawStatusCode: "00",
										State:         paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										Api:           paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{
				{
					enable:  true,
					actorId: "internal-actor-1",
					res:     &upiPb.Device{},
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_SUCCESS, ErrCode: ""}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:        rpc.StatusOk(),
					ReqMsgId:      "random-msg-id",
					RawStatusCode: "00",
				},
				err: nil,
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{ExternalId: "FDR0002"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
						Order:  nil,
					},
					err: nil,
				},
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   payerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypePay,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "external-actor-1",
					PayeeActorId: "internal-actor-1",
					PayerPiId:    "pi-ex-1",
					PayeePiId:    "pi-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				payload: &paymentPb.ProcessPaymentRequest{
					TransactionId: "random-txn-id",
					ActorId:       "internal-actor-1",
				},
				delay: upiConf.PaymentStatusEnquiryDelay,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "incoming COLLECT req auth",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       collectRawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockCreateOrderAndTxn: mockCreateOrderAndTxn{
				enable: true,
				req: &orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:  "internal-actor-1",
						ActorTo:    "external-actor-1",
						Workflow:   orderPb.OrderWorkflow_P2P_COLLECT,
						Provenance: orderPb.OrderProvenance_EXTERNAL,

						Status: orderPb.OrderStatus_COLLECT_REGISTERED,
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiTo:            "pi-ex-1",
						PiFrom:          "pi-1",
						Remarks:         reqAuth.Txn.Note,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_CREATED,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						Utr:             "************",
						Ownership:       commontypes.Ownership_EPIFI_TECH,
					},
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2,
					},
				},
				res: &orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-1",
						FromActorId: "internal-actor-1",
						ToActorId:   "external-actor-1",
						Workflow:    orderPb.OrderWorkflow_P2P_COLLECT,
						Provenance:  orderPb.OrderProvenance_EXTERNAL,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2,
						},
						Status: orderPb.OrderStatus_COLLECT_REGISTERED,
					},
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_CREATED,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						PiTo:           "pi-ex-1",
						PiFrom:         "pi-1",
					},
				},
				err: nil,
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-ex-1",
								State:                piPb.PaymentInstrumentState_CREATED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Type:                 piPb.PaymentInstrumentType_UPI,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra@fifederal",
									},
								},
							},
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-1",
							"pi-ex-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:    "pi-ex-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{ExternalId: "FDR0002"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
						Order:  nil,
					},
					err: nil,
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockGetRelationshipWithActor: mockGetRelationshipWithActor{
				enable: true,
				req: &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "internal-actor-1",
					OtherActorId:   "external-actor-1",
				},
				res: &actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				},
				err: nil,
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   payerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypeCollect,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "internal-actor-1",
					PayeeActorId: "external-actor-1",
					PayerPiId:    "pi-1",
					PayeePiId:    "pi-ex-1",
				},
				err: nil,
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{
				{
					enable:  true,
					actorId: "internal-actor-1",
					res:     &upiPb.Device{},
				},
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    payerInfo.GetPaymentAddress(),
					pi: &piPb.PaymentInstrument{
						Id:    "pi-1",
						Type:  piPb.PaymentInstrumentType_UPI,
						State: piPb.PaymentInstrumentState_CREATED,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payerInfo.GetPaymentAddress(),
								AccountReferenceNumber: payerInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payerInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payerInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payerInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "invalid req auth",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       expireRawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT,
						PiFrom:         "pi-1",
						PiTo:           "pi-2",
					},
				},
				err: nil,
			},
			mockGetOrderId: mockGetOrderId{
				enable: true,
				req:    &paymentPb.GetOrderIdRequest{TransactionId: "random-txn-id"},
				res:    &paymentPb.GetOrderIdResponse{Status: rpc.StatusOk(), OrderId: "order-1"},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: "order-1"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order:  &orderPb.Order{Id: "order-1", FromActorId: "internal-actor-1", ToActorId: "actor-2"},
					},
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_FAILED,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},

				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_FAILED,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										State: paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH,
									},
									{
										ErrorCategory:          paymentPb.TransactionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
										SystemErrorDescription: "req auth older than 5m0s",
										Api:                    paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_AUTH,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS,
							paymentPb.TransactionFieldMask_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{
				{
					enable:  true,
					actorId: "actor-2",
					res:     &upiPb.Device{},
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_FAILURE, ErrCode: reqAuthTimeoutErrCode}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:   rpc.StatusOk(),
					ReqMsgId: "random-msg-id",
				},
				err: nil,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "invalid COLLECT req auth",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       expireCollectRawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockCreateOrderAndTxn: mockCreateOrderAndTxn{
				enable: true,
				req: &orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:  "internal-actor-1",
						ActorTo:    "external-actor-1",
						Workflow:   orderPb.OrderWorkflow_P2P_COLLECT,
						Provenance: orderPb.OrderProvenance_EXTERNAL,

						Status: orderPb.OrderStatus_COLLECT_REGISTERED,
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiTo:            "pi-ex-1",
						PiFrom:          "pi-1",
						Remarks:         reqAuth.Txn.Note,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_CREATED,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						Utr:             "************",
						Ownership:       commontypes.Ownership_EPIFI_TECH,
					},
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2,
					},
				},
				res: &orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-1",
						FromActorId: "internal-actor-1",
						ToActorId:   "external-actor-1",
						Workflow:    orderPb.OrderWorkflow_P2P_COLLECT,
						Provenance:  orderPb.OrderProvenance_EXTERNAL,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2,
						},
						Status: orderPb.OrderStatus_COLLECT_REGISTERED,
					},
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_CREATED,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						PiTo:           "pi-ex-1",
						PiFrom:         "pi-1",
					},
				},
				err: nil,
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{ExternalId: "FDR0002"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
						Order:  nil,
					},
					err: nil,
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:     "random-txn-id",
							Status: paymentPb.TransactionStatus_FAILED,
							ReqId:  reqAuth.GetTransactionReqId(),
							PiTo:   "pi-ex-1",
							PiFrom: "pi-1",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										ErrorCategory:          paymentPb.TransactionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
										SystemErrorDescription: "req auth older than 5m0s",
										Api:                    paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_AUTH,
									},
								},
							},
							ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
							paymentPb.TransactionFieldMask_DETAILED_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetRelationshipWithActor: mockGetRelationshipWithActor{
				enable: true,
				req: &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "internal-actor-1",
					OtherActorId:   "external-actor-1",
				},
				res: &actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				},
				err: nil,
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   payerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypeCollect,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "internal-actor-1",
					PayeeActorId: "external-actor-1",
					PayerPiId:    "pi-1",
					PayeePiId:    "pi-ex-1",
				},
				err: nil,
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-ex-1",
								State:                piPb.PaymentInstrumentState_CREATED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Type:                 piPb.PaymentInstrumentType_UPI,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra@fifederal",
									},
								},
							},
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
							},
						},
					},
					err: nil,
				},
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    payerInfo.GetPaymentAddress(),
					pi: &piPb.PaymentInstrument{
						Id:    "pi-1",
						Type:  piPb.PaymentInstrumentType_UPI,
						State: piPb.PaymentInstrumentState_CREATED,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payerInfo.GetPaymentAddress(),
								AccountReferenceNumber: payerInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payerInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payerInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payerInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "payer PI Disabled COLLECT",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       collectRawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-ex-1",
								State:                piPb.PaymentInstrumentState_CREATED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Type:                 piPb.PaymentInstrumentType_UPI,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra@fifederal",
									},
								},
							},
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-1",
							"pi-ex-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-1",
								State:                piPb.PaymentInstrumentState_SUSPENDED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:    "pi-ex-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{{
				enable:  true,
				actorId: "internal-actor-1",
				res:     &upiPb.Device{Id: "device-1", PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}},
				err:     nil,
			}},
			mockCreateOrderAndTxn: mockCreateOrderAndTxn{
				enable: true,
				req: &orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:  "internal-actor-1",
						ActorTo:    "external-actor-1",
						Workflow:   orderPb.OrderWorkflow_P2P_COLLECT,
						Provenance: orderPb.OrderProvenance_EXTERNAL,

						Status: orderPb.OrderStatus_COLLECT_REGISTERED,
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiTo:            "pi-ex-1",
						PiFrom:          "pi-1",
						Remarks:         reqAuth.Txn.Note,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_CREATED,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						Utr:             "************",
						Ownership:       commontypes.Ownership_EPIFI_TECH,
					},
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2,
					},
				},
				res: &orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-1",
						FromActorId: "internal-actor-1",
						ToActorId:   "external-actor-1",
						Workflow:    orderPb.OrderWorkflow_P2P_COLLECT,
						Provenance:  orderPb.OrderProvenance_EXTERNAL,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2,
						},
						Status: orderPb.OrderStatus_COLLECT_REGISTERED,
					},
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_CREATED,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						PiTo:           "pi-ex-1",
						PiFrom:         "pi-1",
					},
				},
				err: nil,
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{ExternalId: "FDR0002"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
						Order:  nil,
					},
					err: nil,
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:     "random-txn-id",
							Status: paymentPb.TransactionStatus_FAILED,
							ReqId:  reqAuth.GetTransactionReqId(),
							PiTo:   "pi-ex-1",
							PiFrom: "pi-1",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										ErrorCategory:          paymentPb.TransactionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
										SystemErrorDescription: "payer pi is inactive: pi inactive: permanent failure",
										Api:                    paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_AUTH,
									},
								},
							},
							ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
							paymentPb.TransactionFieldMask_DETAILED_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetRelationshipWithActor: mockGetRelationshipWithActor{
				enable: true,
				req: &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "internal-actor-1",
					OtherActorId:   "external-actor-1",
				},
				res: &actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				},
				err: nil,
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   payerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypeCollect,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "internal-actor-1",
					PayeeActorId: "external-actor-1",
					PayerPiId:    "pi-1",
					PayeePiId:    "pi-ex-1",
				},
				err: nil,
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    payerInfo.GetPaymentAddress(),
					pi: &piPb.PaymentInstrument{
						Id:    "pi-1",
						Type:  piPb.PaymentInstrumentType_UPI,
						State: piPb.PaymentInstrumentState_CREATED,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payerInfo.GetPaymentAddress(),
								AccountReferenceNumber: payerInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payerInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payerInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payerInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
				{
					enable: false,
					vpa:    reqAuth.GetPayeeVPA(),
					pi: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},

			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "payee PI disabled PAY (PI state SUSPENDED)",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT,
						PiFrom:         "pi-1",
						PiTo:           "pi-2",
					},
				},
				err: nil,
			},
			mockGetOrderId: mockGetOrderId{
				enable: true,
				req:    &paymentPb.GetOrderIdRequest{TransactionId: "random-txn-id"},
				res:    &paymentPb.GetOrderIdResponse{Status: rpc.StatusOk(), OrderId: "order-1"},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: "order-1"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order:  &orderPb.Order{Id: "order-1", FromActorId: "actor-id-1", ToActorId: "actor-id-2"},
					},
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_FAILED,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_FAILED,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										State: paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH,
									},
									{
										ErrorCategory:          paymentPb.TransactionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
										SystemErrorDescription: "payee pi is inactive: pi inactive: permanent failure",
										Api:                    paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_AUTH,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS,
							paymentPb.TransactionFieldMask_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-1",
							"pi-2",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:                   "pi-2",
								State:                piPb.PaymentInstrumentState_SUSPENDED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra-baller@fede",
									},
								},
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{
				{
					enable:  true,
					actorId: "actor-id-2",
					res:     &upiPb.Device{},
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_FAILURE, ErrCode: disabledVPAErrCode}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:   rpc.StatusOk(),
					ReqMsgId: "random-msg-id",
				},
				err: nil,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "payee PI disabled PAY (PI state CLOSED)",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT,
						PiFrom:         "pi-1",
						PiTo:           "pi-2",
					},
				},
				err: nil,
			},
			mockGetOrderId: mockGetOrderId{
				enable: true,
				req:    &paymentPb.GetOrderIdRequest{TransactionId: "random-txn-id"},
				res:    &paymentPb.GetOrderIdResponse{Status: rpc.StatusOk(), OrderId: "order-1"},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: "order-1"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order:  &orderPb.Order{Id: "order-1", FromActorId: "actor-id-1", ToActorId: "actor-id-2"},
					},
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_FAILED,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_FAILED,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										State: paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH,
									},
									{
										ErrorCategory:          paymentPb.TransactionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
										SystemErrorDescription: "payee pi is inactive: pi inactive: permanent failure",
										Api:                    paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_AUTH,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS,
							paymentPb.TransactionFieldMask_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-1",
							"pi-2",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:                   "pi-2",
								State:                piPb.PaymentInstrumentState_CLOSED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra-baller@fede",
									},
								},
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{
				{
					enable:  true,
					actorId: "actor-id-2",
					res:     &upiPb.Device{},
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_FAILURE, ErrCode: disabledVPAErrCode}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:   rpc.StatusOk(),
					ReqMsgId: "random-msg-id",
				},
				err: nil,
			},
			mockIsNewVpaHandleAssignedToActor: mockIsNewVpaHandleAssignedToActor{
				enable:  true,
				actorId: "actor-id-2",
				res:     false,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "incoming COLLECT req auth from blocked and reported actor",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       collectRawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockCreateOrderAndTxn: mockCreateOrderAndTxn{
				enable: true,
				req: &orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:  "internal-actor-1",
						ActorTo:    "external-actor-1",
						Workflow:   orderPb.OrderWorkflow_P2P_COLLECT,
						Provenance: orderPb.OrderProvenance_EXTERNAL,

						Status: orderPb.OrderStatus_CREATED,
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiTo:            "pi-ex-1",
						PiFrom:          "pi-1",
						Remarks:         reqAuth.Txn.Note,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_CREATED,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						Utr:             "************",
						Ownership:       commontypes.Ownership_EPIFI_TECH,
					},
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2,
					},
				},
				res: &orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-1",
						FromActorId: "internal-actor-1",
						ToActorId:   "external-actor-1",
						Workflow:    orderPb.OrderWorkflow_P2P_COLLECT,
						Provenance:  orderPb.OrderProvenance_EXTERNAL,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2,
						},
						Status: orderPb.OrderStatus_COLLECT_REGISTERED,
					},
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_CREATED,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						PiTo:           "pi-ex-1",
						PiFrom:         "pi-1",
					},
				},
				err: nil,
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{ExternalId: "FDR0002"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
						Order:  nil,
					},
					err: nil,
				},
			},
			mockGetRelationshipWithActor: mockGetRelationshipWithActor{
				enable: true,
				req: &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "internal-actor-1",
					OtherActorId:   "external-actor-1",
				},
				res: &actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_REPORTED,
				},
				err: nil,
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   payerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypeCollect,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "internal-actor-1",
					PayeeActorId: "external-actor-1",
					PayerPiId:    "pi-1",
					PayeePiId:    "pi-ex-1",
				},
				err: nil,
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    payerInfo.GetPaymentAddress(),
					pi: &piPb.PaymentInstrument{
						Id:    "pi-1",
						Type:  piPb.PaymentInstrumentType_UPI,
						State: piPb.PaymentInstrumentState_CREATED,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payerInfo.GetPaymentAddress(),
								AccountReferenceNumber: payerInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payerInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payerInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payerInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-ex-1",
								State:                piPb.PaymentInstrumentState_CREATED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Type:                 piPb.PaymentInstrumentType_UPI,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra@fifederal",
									},
								},
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "unable to validate PI due to transient failure",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       collectRawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-ex-1",
								State:                piPb.PaymentInstrumentState_CREATED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Type:                 piPb.PaymentInstrumentType_UPI,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra@fifederal",
									},
								},
							},
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-1",
							"pi-ex-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusInternal(),
					},
					err: nil,
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{{
				enable:  false,
				actorId: "internal-actor-1",
				res:     &upiPb.Device{Id: "device-1", PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}},
			}},
			mockCreateOrderAndTxn: mockCreateOrderAndTxn{
				enable: true,
				req: &orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:  "internal-actor-1",
						ActorTo:    "external-actor-1",
						Workflow:   orderPb.OrderWorkflow_P2P_COLLECT,
						Provenance: orderPb.OrderProvenance_EXTERNAL,

						Status: orderPb.OrderStatus_COLLECT_REGISTERED,
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiTo:            "pi-ex-1",
						PiFrom:          "pi-1",
						Remarks:         reqAuth.Txn.Note,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_CREATED,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						Utr:             "************",
						Ownership:       commontypes.Ownership_EPIFI_TECH,
					},
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2,
					},
				},
				res: &orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-1",
						FromActorId: "internal-actor-1",
						ToActorId:   "external-actor-1",
						Workflow:    orderPb.OrderWorkflow_P2P_COLLECT,
						Provenance:  orderPb.OrderProvenance_EXTERNAL,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2,
						},
						Status: orderPb.OrderStatus_COLLECT_REGISTERED,
					},
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_CREATED,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						PiTo:           "pi-ex-1",
						PiFrom:         "pi-1",
					},
				},
				err: nil,
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{ExternalId: "FDR0002"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
						Order:  nil,
					},
					err: nil,
				},
			},
			mockGetRelationshipWithActor: mockGetRelationshipWithActor{
				enable: true,
				req: &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "internal-actor-1",
					OtherActorId:   "external-actor-1",
				},
				res: &actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				},
				err: nil,
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   payerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypeCollect,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "internal-actor-1",
					PayeeActorId: "external-actor-1",
					PayerPiId:    "pi-1",
					PayeePiId:    "pi-ex-1",
				},
				err: nil,
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    payerInfo.GetPaymentAddress(),
					pi: &piPb.PaymentInstrument{
						Id:    "pi-1",
						Type:  piPb.PaymentInstrumentType_UPI,
						State: piPb.PaymentInstrumentState_CREATED,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payerInfo.GetPaymentAddress(),
								AccountReferenceNumber: payerInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payerInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payerInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payerInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
				{
					enable: false,
					vpa:    reqAuth.GetPayeeVPA(),
					pi: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},

			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "successful processing mandate collect from federal",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       collectRawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{
							ExternalId: "FDR0002",
						},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
					},
					err: nil,
				},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{{
				enable:  true,
				actorId: "internal-actor-1",
				res:     &upiPb.Device{Id: "device-1", PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}},
			},
				{
					enable:  true,
					actorId: "internal-actor-1",
					res:     &upiPb.Device{Id: "device-1", PhoneNumber: &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********}},
				}},
			mockGetPhoneNumber: []mockGetPhoneNumber{
				{
					enable:  true,
					actorId: "internal-actor-1",
					ph:      &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
					err:     nil,
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    payerInfo.GetPaymentAddress(),
					pi: &piPb.PaymentInstrument{
						Id:    "pi-1",
						Type:  piPb.PaymentInstrumentType_UPI,
						State: piPb.PaymentInstrumentState_CREATED,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payerInfo.GetPaymentAddress(),
								AccountReferenceNumber: payerInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payerInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payerInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payerInfo.GetAccountDetails().GetType(),
								VpaType:                piPb.Upi_MANDATE,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					vpa:    payerInfo.GetPaymentAddress(),
					pi: &piPb.PaymentInstrument{
						Id:    "pi-1",
						Type:  piPb.PaymentInstrumentType_UPI,
						State: piPb.PaymentInstrumentState_CREATED,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
								VpaType:                piPb.Upi_MANDATE,
							},
						},
					},
					err: nil,
				},
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   payerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypeCollect,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "external-actor-1",
					PayeeActorId: "internal-actor-1",
					PayerPiId:    "pi-ex-1",
					PayeePiId:    "pi-1",
				},
				err: nil,
			},
			mockGetRelationshipWithActor: mockGetRelationshipWithActor{
				enable: true,
				req: &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "external-actor-1",
					OtherActorId:   "internal-actor-1",
				},
				res: &actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				},
				err: nil,
			},
			mockGetByUmnDao: mockGetByUmnDao{
				enable: true,
				umn:    "jack@okaxis",
				mandateEntity: &mandate.MandateEntity{
					Id:                 "mandate-id",
					RecurringPaymentId: "recur-pay-id",
					Umn:                "jack@okaxis",
					SignedToken:        "token",
				},
				err: nil,
			},
			mockExecuteRecurringPayment: mockExecuteRecurringPayment{
				enable: true,
				req: &recurringPaymentPb.ExecuteRecurringPaymentRequest{
					RecurringPaymentId: "recur-pay-id",
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2,
					},
					ClientRequestId: "FDR0001",
					ClientId:        &celestialPb.ClientReqId{Id: "FDR0001", Client: workflowPb.Client_USER_APP},
					ExecutionPayload: &recurringPaymentPb.ExecuteRecurringPaymentRequest_UpiMandateExecutionInfo{
						UpiMandateExecutionInfo: &recurringPaymentPb.UpiMandateExecuteInfo{
							PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
								DeviceId: "",
								ReqId:    "FDR0001",
								UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
									MerchantRefId:      "FDR0002",
									MsgId:              "FDR121",
									RefUrl:             "npci.com",
									TxnOriginTimestamp: txnOrgTs,
									InitiationMode:     "00",
									Purpose:            "00",
									Mcc:                "0000",
									MerchantId:         "",
									MerchantStoreId:    "",
									MerchantTerminalId: "",
									OrgId:              "",
									RiskScores:         nil,
									Rules:              nil,
									CustomerInfo:       nil,
									CustomerAccountInfo: &upiPb.CustomerAccountDetails{
										AccountNumber: "random-acc-number#1",
										Ifsc:          "ICICI0000058",
										Type:          accounts.Type_SAVINGS,
										Apo:           accountPb.AccountProductOffering_APO_REGULAR,
									},
									MerchantDetails: nil,
									TransactionType: upiPb.TransactionType_COLLECT,
									RiskScoreList:   nil,
									PayerVpa:        "jack@okaxis",
									PayeeVpa:        "johnwick@okaxis",
									SeqNum:          "1",
								},
							},
							Utr:    "************",
							Note:   "",
							SeqNum: 1,
						},
					},
				},
				res: &recurringPaymentPb.ExecuteRecurringPaymentResponse{
					Status:  rpc.StatusOk(),
					OrderId: "order-id",
					OrderWithTxn: &orderPb.OrderWithTransactions{
						Order: &orderPb.Order{
							Id:          "order-1",
							FromActorId: "internal-actor-1",
							ToActorId:   "external-actor-1",
							Workflow:    orderPb.OrderWorkflow_P2P_COLLECT,
							Provenance:  orderPb.OrderProvenance_EXTERNAL,
							Amount: &moneyPb.Money{
								CurrencyCode: "INR",
								Units:        2,
							},
							Status: orderPb.OrderStatus_COLLECT_REGISTERED,
						},
						Transactions: []*paymentPb.Transaction{
							{
								Id:             "random-txn-id",
								Status:         paymentPb.TransactionStatus_IN_PROGRESS,
								ReqId:          reqAuth.GetTransactionReqId(),
								ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
								PiTo:           "pi-2",
								PiFrom:         "pi-1",
							},
						},
					},
				},
				err: nil,
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										StatusCodePayer: "00",
										StatusCodePayee: "00",
										State:           paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										RawStatusCode:   "00",
										Api:             paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_SUCCESS, ErrCode: ""}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:        rpc.StatusOk(),
					ReqMsgId:      "random-msg-id",
					RawStatusCode: "00",
					StatusCode:    "00",
				},
				err: nil,
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-1",
							"pi-ex-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-1",
								State:                piPb.PaymentInstrumentState_CREATED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Type:                 piPb.PaymentInstrumentType_UPI,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra@fifederal",
									},
								},
							},
							{
								Id:    "pi-ex-1",
								State: piPb.PaymentInstrumentState_CREATED,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{"pi-1", "pi-2"},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:    "pi-2",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetActorIdFromPI: []mockGetActorIdFromPI{
				{
					enable:  true,
					PiId:    "pi-1",
					actorId: "internal-actor-1",
					err:     nil,
				},
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "validation failure while executing mandate",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       collectRawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-1",
							"pi-ex-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-1",
								State:                piPb.PaymentInstrumentState_CREATED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Type:                 piPb.PaymentInstrumentType_UPI,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra@fifederal",
									},
								},
							},
							{
								Id:    "pi-ex-1",
								State: piPb.PaymentInstrumentState_CREATED,
							},
						},
					},
					err: nil,
				},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{
							ExternalId: "FDR0002",
						},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
					},
					err: nil,
				},
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    payerInfo.GetPaymentAddress(),
					pi: &piPb.PaymentInstrument{
						Id:    "pi-1",
						Type:  piPb.PaymentInstrumentType_UPI,
						State: piPb.PaymentInstrumentState_CREATED,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
								VpaType:                piPb.Upi_MANDATE,
							},
						},
					},
					err: nil,
				},
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   payerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypeCollect,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "external-actor-1",
					PayeeActorId: "internal-actor-1",
					PayerPiId:    "pi-ex-1",
					PayeePiId:    "pi-1",
				},
				err: nil,
			},
			mockGetRelationshipWithActor: mockGetRelationshipWithActor{
				enable: true,
				req: &actorPb.GetRelationshipWithActorRequest{
					CurrentActorId: "external-actor-1",
					OtherActorId:   "internal-actor-1",
				},
				res: &actorPb.GetRelationshipWithActorResponse{
					Status:       rpc.StatusOk(),
					Relationship: actorPb.GetRelationshipWithActorResponse_NOT_BLOCKED,
				},
				err: nil,
			},
			mockGetByUmnDao: mockGetByUmnDao{
				enable: true,
				umn:    "jack@okaxis",
				mandateEntity: &mandate.MandateEntity{
					Id:                 "mandate-id",
					RecurringPaymentId: "recur-pay-id",
					Umn:                "jack@okaxis",
					SignedToken:        "token",
				},
				err: nil,
			},
			mockExecuteRecurringPayment: mockExecuteRecurringPayment{
				enable: true,
				req: &recurringPaymentPb.ExecuteRecurringPaymentRequest{
					RecurringPaymentId: "recur-pay-id",
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2,
					},
					ClientRequestId: "FDR0001",
					ClientId:        &celestialPb.ClientReqId{Id: "FDR0001", Client: workflowPb.Client_USER_APP},
					ExecutionPayload: &recurringPaymentPb.ExecuteRecurringPaymentRequest_UpiMandateExecutionInfo{
						UpiMandateExecutionInfo: &recurringPaymentPb.UpiMandateExecuteInfo{
							PaymentRequestInfo: &paymentPb.PaymentRequestInformation{
								DeviceId: "",
								ReqId:    "FDR0001",
								UpiInfo: &paymentPb.PaymentRequestInformation_UPI{
									MerchantRefId:      "FDR0002",
									MsgId:              "FDR121",
									RefUrl:             "npci.com",
									TxnOriginTimestamp: txnOrgTs,
									InitiationMode:     "00",
									Purpose:            "00",
									Mcc:                "0000",
									MerchantId:         "",
									MerchantStoreId:    "",
									MerchantTerminalId: "",
									OrgId:              "",
									RiskScores:         nil,
									Rules:              nil,
									CustomerInfo:       nil,
									CustomerAccountInfo: &upiPb.CustomerAccountDetails{
										AccountNumber: "random-acc-number#1",
										Ifsc:          "ICICI0000058",
										Type:          accounts.Type_SAVINGS,
										Apo:           accountPb.AccountProductOffering_APO_REGULAR,
									},
									MerchantDetails: nil,
									TransactionType: upiPb.TransactionType_COLLECT,
									RiskScoreList:   nil,
									PayerVpa:        "jack@okaxis",
									PayeeVpa:        "johnwick@okaxis",
									SeqNum:          "1",
								},
							},
							Utr:    "************",
							Note:   "",
							SeqNum: 1,
						},
					},
				},
				res: &recurringPaymentPb.ExecuteRecurringPaymentResponse{
					Status:       rpc.NewStatus(uint32(recurringPaymentPb.ExecuteRecurringPaymentResponse_INVALID_SEQ_NUM), "", ""),
					OrderWithTxn: &orderPb.OrderWithTransactions{},
				},
				err: nil,
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req: &vgUPIPb.RespAuthDetailsRequest{
					Header:     &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
					ReqType:    vgUPIPb.RespAuthDetailsRequest_COLLECT,
					TxnHeader:  txnHeader,
					UpiVersion: upiPb.ParseStringToVersion(reqAuth.Head.Ver),
					Resp: &vgUPIPb.ResponseHeader{
						Result:  vgUPIPb.ResponseHeader_FAILURE,
						ErrCode: "Q1",
					},
					Payer:  payerInfo,
					Payees: []*upiPb.Customer{payeeInfo},
				},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:        rpc.StatusOk(),
					ReqMsgId:      "random-msg-id",
					RawStatusCode: "00",
					StatusCode:    "00",
				},
				err: nil,
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "skip payment enquiry packet publish as the payer is internal",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetActorIdFromPI: []mockGetActorIdFromPI{
				{
					enable:  true,
					PiId:    "pi-1",
					actorId: "actor-1",
					err:     nil,
				},
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_PAY_REQ_PAY_SENT,
						PiFrom:         "pi-1",
						PiTo:           "pi-2",
					},
				},
				err: nil,
			},
			mockGetOrderId: mockGetOrderId{
				enable: true,
				req:    &paymentPb.GetOrderIdRequest{TransactionId: "random-txn-id"},
				res:    &paymentPb.GetOrderIdResponse{Status: rpc.StatusOk(), OrderId: "order-1"},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: "order-1"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order:  &orderPb.Order{Id: "order-1", ToActorId: "actor-1"},
					},
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT,
							PiFrom:         "pi-1",
							PiTo:           "pi-2",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										StatusCodePayer: "00",
										StatusCodePayee: "00",
										State:           paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										RawStatusCode:   "00",
										Api:             paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{
				{
					enable:  true,
					actorId: "actor-1",
					res:     &upiPb.Device{},
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_SUCCESS, ErrCode: ""}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:        rpc.StatusOk(),
					ReqMsgId:      "random-msg-id",
					RawStatusCode: "00",
					StatusCode:    "00",
				},
				err: nil,
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{"pi-1", "pi-2"},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:    "pi-2",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    payeeInfo.GetPaymentAddress(),
					pi: &piPb.PaymentInstrument{
						Id:    "pi-1",
						Type:  piPb.PaymentInstrumentType_UPI,
						State: piPb.PaymentInstrumentState_CREATED,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},
			mockGetPhoneNumber: []mockGetPhoneNumber{
				{
					enable:  true,
					actorId: "actor-1",
					ph:      &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
					err:     nil,
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id:                   "pi-1",
						IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					},
				},
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id:                   "pi-1",
						IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
					},
				},
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "Resp Auth already initiated",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusOk(),
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_INITIATED,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
						PiFrom:         "pi-1",
						PiTo:           "pi-2",
					},
				},
				err: nil,
			},
			mockGetOrderId: mockGetOrderId{
				enable: true,
				req:    &paymentPb.GetOrderIdRequest{TransactionId: "random-txn-id"},
				res:    &paymentPb.GetOrderIdResponse{Status: rpc.StatusOk(), OrderId: "order-1"},
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_OrderId{OrderId: "order-1"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusOk(),
						Order:  &orderPb.Order{Id: "order-1"},
					},
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "invalid COLLECT req auth as it was raised to old vpa",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       oldHandleCollectData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{ExternalId: "FDR0002"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
						Order:  nil,
					},
					err: nil,
				},
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   oldHandleCollectPayerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypeCollect,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "internal-actor-1",
					PayeeActorId: "external-actor-1",
					PayerPiId:    "pi-1",
					PayeePiId:    "pi-ex-1",
					PayeeName:    "payee-name",
					PayerName:    "payer-name",
				},
				err: nil,
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: true,
					vpa:    "test@fede",
					pi: &piPb.PaymentInstrument{
						Id:    "pi-1",
						Type:  piPb.PaymentInstrumentType_UPI,
						State: piPb.PaymentInstrumentState_CLOSED,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    "test@fede",
								AccountReferenceNumber: payerInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payerInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payerInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payerInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_FAILURE, ErrCode: disabledVPAErrCode}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:   rpc.StatusOk(),
					ReqMsgId: "random-msg-id",
				},
				err: nil,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE},
			},
			wantErr: false,
		},
		{
			name: "incoming PAY req auth to old vpa handle (old vpa handle for tpap accounts can receive money)",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       rawData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetPIFromVPA: []mockGetPIFromVPA{
				{
					enable: false,
					vpa:    reqAuth.GetPayerVPA(),
					pi: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					vpa:    reqAuth.GetPayeeVPA(),
					pi: &piPb.PaymentInstrument{
						Id:   "pi-1",
						Type: piPb.PaymentInstrumentType_UPI,
						Identifier: &piPb.PaymentInstrument_Upi{
							Upi: &piPb.Upi{
								Vpa:                    payeeInfo.GetPaymentAddress(),
								AccountReferenceNumber: payeeInfo.GetAccountDetails().GetAccountNumber(),
								MaskedAccountNumber:    payeeInfo.GetAccountDetails().GetAccountNumber(),
								IfscCode:               payeeInfo.GetAccountDetails().GetIfsc(),
								AccountType:            payeeInfo.GetAccountDetails().GetType(),
							},
						},
					},
					err: nil,
				},
			},
			mockGetByUmnDao: mockGetByUmnDao{
				enable:        false,
				umn:           "jack@okaxis",
				mandateEntity: nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			mockGetActorIdFromPI: []mockGetActorIdFromPI{
				{
					enable:  true,
					PiId:    "pi-1",
					actorId: "actor-1",
					err:     nil,
				},
			},
			mockGetPhoneNumber: []mockGetPhoneNumber{
				{
					enable:  true,
					actorId: "actor-1",
					ph:      &commontypes.PhoneNumber{CountryCode: 91, NationalNumber: **********},
					err:     nil,
				},
			},
			mockCreateOrderAndTxn: mockCreateOrderAndTxn{
				enable: true,
				req: &orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:  "external-actor-1",
						ActorTo:    "internal-actor-1",
						Workflow:   orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						Provenance: orderPb.OrderProvenance_EXTERNAL,
						Status:     orderPb.OrderStatus_IN_PAYMENT,
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiFrom:          "pi-ex-1",
						PiTo:            "pi-1",
						Remarks:         reqAuth.Txn.Note,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_IN_PROGRESS,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						Utr:             "************",
						Ownership:       commontypes.Ownership_EPIFI_TECH,
					},
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2,
					},
				},
				res: &orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-1",
						FromActorId: "external-actor-1",
						ToActorId:   "internal-actor-1",
						Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						Provenance:  orderPb.OrderProvenance_EXTERNAL,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2,
						},
						Status: orderPb.OrderStatus_IN_PAYMENT,
					},
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						PiFrom:         "pi-ex-1",
						PiTo:           "pi-1",
					},
				},
				err: nil,
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-ex-1",
								State:                piPb.PaymentInstrumentState_CREATED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Type:                 piPb.PaymentInstrumentType_UPI,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra@fifederal",
									},
								},
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CLOSED,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra-baller@fede",
									},
								},
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CLOSED,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra-baller@fede",
									},
								},
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
							{
								Id:    "pi-2",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-ex-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
				{
					enable: true,
					piId:   "pi-ex-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-ex-1",
							PiTo:           "pi-1",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
							paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_IN_PROGRESS,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT,
							PiFrom:         "pi-ex-1",
							PiTo:           "pi-1",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										RawStatusCode: "00",
										State:         paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										Api:           paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{
				{
					enable:  true,
					actorId: "internal-actor-1",
					res:     &upiPb.Device{},
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_SUCCESS, ErrCode: ""}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:        rpc.StatusOk(),
					ReqMsgId:      "random-msg-id",
					RawStatusCode: "00",
				},
				err: nil,
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{ExternalId: "FDR0002"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
						Order:  nil,
					},
					err: nil,
				},
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   payerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypePay,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "external-actor-1",
					PayeeActorId: "internal-actor-1",
					PayerPiId:    "pi-ex-1",
					PayeePiId:    "pi-1",
				},
				err: nil,
			},
			mockPublish: mockPublish{
				enable: true,
				payload: &paymentPb.ProcessPaymentRequest{
					TransactionId: "random-txn-id",
					ActorId:       "internal-actor-1",
				},
				delay: upiConf.PaymentStatusEnquiryDelay,
			},
			mockIsNewVpaHandleAssignedToActor: mockIsNewVpaHandleAssignedToActor{
				enable:  true,
				actorId: "internal-actor-1",
				res:     true,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "incoming UOD req auth to non merchant",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       uodPayerData,
			},
			mockGetTransaction: mockGetTransaction{
				enable: true,
				req: &paymentPb.GetTransactionRequest{
					Identifier: &paymentPb.GetTransactionRequest_ReqId{ReqId: reqAuth.GetTransactionReqId()},
					GetReqInfo: true,
				},
				res: &paymentPb.GetTransactionResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetByUmnDao: mockGetByUmnDao{
				enable:        false,
				umn:           "jack@okaxis",
				mandateEntity: nil,
				err:           epifierrors.ErrRecordNotFound,
			},
			mockCreateOrderAndTxn: mockCreateOrderAndTxn{
				enable: true,
				req: &orderPb.CreateOrderWithTransactionRequest{
					OrderParam: &orderPb.CreateOrderWithTransactionRequest_OrderCreationParams{
						ActorFrom:  "external-actor-1",
						ActorTo:    "internal-actor-1",
						Workflow:   orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						Provenance: orderPb.OrderProvenance_EXTERNAL,
						Status:     orderPb.OrderStatus_IN_PAYMENT,
					},
					TransactionParam: &orderPb.CreateOrderWithTransactionRequest_TransactionCreationParams{
						PiFrom:          "pi-ex-1",
						PiTo:            "pi-1",
						Remarks:         reqAuth.Txn.Note,
						PaymentProtocol: paymentPb.PaymentProtocol_UPI,
						Status:          paymentPb.TransactionStatus_IN_PROGRESS,
						ProtocolStatus:  paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						Utr:             "************",
						Ownership:       commontypes.Ownership_EPIFI_TECH,
					},
					Amount: &moneyPb.Money{
						CurrencyCode: "INR",
						Units:        2,
					},
				},
				res: &orderPb.CreateOrderWithTransactionResponse{
					Status: rpc.StatusOk(),
					Order: &orderPb.Order{
						Id:          "order-1",
						FromActorId: "external-actor-1",
						ToActorId:   "internal-actor-1",
						Workflow:    orderPb.OrderWorkflow_P2P_FUND_TRANSFER,
						Provenance:  orderPb.OrderProvenance_EXTERNAL,
						Amount: &moneyPb.Money{
							CurrencyCode: "INR",
							Units:        2,
						},
						Status: orderPb.OrderStatus_IN_PAYMENT,
					},
					Transaction: &paymentPb.Transaction{
						Id:             "random-txn-id",
						Status:         paymentPb.TransactionStatus_IN_PROGRESS,
						ReqId:          reqAuth.GetTransactionReqId(),
						ProtocolStatus: paymentPb.TransactionProtocolStatus_REQ_AUTH_RECEIVED,
						PiFrom:         "pi-ex-1",
						PiTo:           "pi-1",
					},
				},
				err: nil,
			},
			mockGetPIsByIds: []mockGetPIsByIds{
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:                   "pi-ex-1",
								State:                piPb.PaymentInstrumentState_CREATED,
								IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
								Type:                 piPb.PaymentInstrumentType_UPI,
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa: "yatinkwatra@fifederal",
									},
								},
							},
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &piPb.GetPIsByIdsRequest{
						Ids: []string{
							"pi-ex-1",
							"pi-1",
						},
					},
					res: &piPb.GetPIsByIdsResponse{
						Status: rpc.StatusOk(),
						Paymentinstruments: []*piPb.PaymentInstrument{
							{
								Id:    "pi-ex-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
								Identifier: &piPb.PaymentInstrument_Upi{
									Upi: &piPb.Upi{
										Vpa:         "yatinkwatra@fifederal",
										AccountType: accounts.Type_UNSECURED_OVERDRAFT,
									},
								},
							},
							{
								Id:    "pi-1",
								State: piPb.PaymentInstrumentState_CREATED,
								Capabilities: map[string]bool{
									piPb.Capability_INBOUND_TXN.String():  true,
									piPb.Capability_OUTBOUND_TXN.String(): true,
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetPiById: []mockGetPiById{
				{
					enable: true,
					piId:   "pi-ex-1",
					pi: &piPb.PaymentInstrument{
						Id: "pi-1",
					},
				},
			},
			mockUpdateTransactions: []mockUpdateTransaction{
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_FAILED,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_INITIATED,
							PiFrom:         "pi-ex-1",
							PiTo:           "pi-1",
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_STATUS,
							paymentPb.TransactionFieldMask_PROTOCOL_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
				{
					req: &paymentPb.UpdateTransactionRequest{
						Transaction: &paymentPb.Transaction{
							Id:             "random-txn-id",
							Status:         paymentPb.TransactionStatus_FAILED,
							ReqId:          reqAuth.GetTransactionReqId(),
							ProtocolStatus: paymentPb.TransactionProtocolStatus_RESP_AUTH_SENT,
							PiFrom:         "pi-ex-1",
							PiTo:           "pi-1",
							DetailedStatus: &paymentPb.TransactionDetailedStatus{
								DetailedStatusList: []*paymentPb.TransactionDetailedStatus_DetailedStatus{
									{
										State: paymentPb.TransactionDetailedStatus_DetailedStatus_IN_PROGRESS,
										Api:   paymentPb.TransactionDetailedStatus_DetailedStatus_RESP_AUTH,
									},
									{
										ErrorCategory:          paymentPb.TransactionDetailedStatus_DetailedStatus_SYSTEM_ERROR,
										SystemErrorDescription: "UOD accounts cannot make P2P transactions",
										Api:                    paymentPb.TransactionDetailedStatus_DetailedStatus_REQ_AUTH,
									},
								},
							},
						},
						FieldMasks: []paymentPb.TransactionFieldMask{paymentPb.TransactionFieldMask_DETAILED_STATUS, paymentPb.TransactionFieldMask_PROTOCOL_STATUS, paymentPb.TransactionFieldMask_STATUS},
					},
					res: &paymentPb.UpdateTransactionResponse{
						Status: rpc.StatusOk(),
					},
					err: nil,
				},
			},
			mockGetInternalDeviceForActor: []mockGetInternalDeviceForActor{
				{
					enable:  true,
					actorId: "internal-actor-1",
					res:     &upiPb.Device{},
				},
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_FAILURE, ErrCode: "ZE"}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:   rpc.StatusOk(),
					ReqMsgId: "random-msg-id",
				},
				err: nil,
			},
			mockGetOrder: []mockGetOrder{
				{
					enable: true,
					req: &orderPb.GetOrderRequest{
						Identifier: &orderPb.GetOrderRequest_ExternalId{ExternalId: "FDR0002"},
					},
					res: &orderPb.GetOrderResponse{
						Status: rpc.StatusRecordNotFound(),
						Order:  nil,
					},
					err: nil,
				},
			},
			mockResolveTimelineHelper: mockResolveTimelineHelper{
				enable:  true,
				payer:   uodPayerInfo,
				payee:   payeeInfo,
				reqType: vendors.UPITxnTypePay,
				res: &timelineHelper.ResolveTimelineResult{
					PayerActorId: "external-actor-1",
					PayeeActorId: "internal-actor-1",
					PayerPiId:    "pi-ex-1",
					PayeePiId:    "pi-1",
				},
				err: nil,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
		{
			name: "should early return resp auth when cc pool account is payee",
			req: &upiPb.ProcessReqAuthRequest{
				RequestHeader: &queuePb.ConsumerRequestHeader{IsLastAttempt: false},
				PartnerBank:   commonvgpb.Vendor_FEDERAL_BANK,
				RawData:       ccRepayData,
			},
			mockRespAuth: mockRespAuth{
				enable: true,
				req:    &vgUPIPb.RespAuthDetailsRequest{Resp: &vgUPIPb.ResponseHeader{Result: vgUPIPb.ResponseHeader_SUCCESS, ErrCode: ""}},
				res: &vgUPIPb.RespAuthDetailsResponse{
					Status:   rpc.StatusOk(),
					ReqMsgId: "random-msg-id",
				},
				err: nil,
			},
			want: &upiPb.ProcessReqAuthResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{Status: queuePb.MessageConsumptionStatus_SUCCESS},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			mockPaymentClient := paymentMocks.NewMockPaymentClient(ctr)
			mockOrderClient := orderMocks.NewMockOrderServiceClient(ctr)
			mockPiClient := piMocks.NewMockPiClient(ctr)
			mockActorClient := actorMocks.NewMockActorClient(ctr)
			mockVgUPIClient := vgMocks.NewMockUPIClient(ctr)
			mockTimelineHelperSvc := timelineHelperMocks.NewMockTimelineHelper(ctr)
			mockPiHelperSvc := piHelperMocks.NewMockPIHelper(ctr)
			brokerMock := eventMock.NewMockBroker(ctr)
			mockActorProcessor := actorProcessorMocks.NewMockActorProcessor(ctr)
			mockMandateDao := daoMocks.NewMockMandateDao(ctr)
			mockRecurPayClient := mockRecurPaymentClient.NewMockRecurringPaymentServiceClient(ctr)
			brokerMock.EXPECT().AddToBatch(gomock.Any(), gomock.Any()).Return().AnyTimes()
			mockAuthClient := mocks.NewMockAuthClient(ctr)
			mockPublisher := queueMocks.NewMockDelayPublisher(ctr)
			mockVerifiedAddressEntryDao := daoMocks.NewMockVerifiedAddressEntryDao(ctr)
			mockUpiOnboardingProcessor := upiOnboardingProcessorMocks.NewMockUpiOnboardingProcessor(ctr)
			mockUpiClient := upiMocks.NewMockUPIClient(ctr)
			mockDynConf, err := genconf.Load()
			require.NoError(t, err)

			consumerServer := consumer.NewService(mockPaymentClient, mockVgUPIClient,
				mockPiClient, mockOrderClient, mockActorClient, nil, mockVerifiedAddressEntryDao,
				mockTimelineHelperSvc, mockPiHelperSvc, brokerMock, nil, nil, rts.conf,
				mockActorProcessor, mockAuthClient, nil, nil, mockMandateDao, mockRecurPayClient, nil, nil, mockUpiOnboardingProcessor, nil, nil, mockPublisher, nil, nil, nil, nil, nil,
				mockDynConf, mockUpiClient)

			defer func() {
				ctr.Finish()
			}()
			for _, mock := range tt.mockGetOrder {
				if mock.enable {
					mockOrderClient.EXPECT().GetOrder(context.Background(), mock.req).
						Return(mock.res, mock.err)
				}
			}
			if tt.mockGetTransaction.enable {
				mockPaymentClient.EXPECT().
					GetTransaction(context.Background(), tt.mockGetTransaction.req).
					Return(tt.mockGetTransaction.res, tt.mockGetTransaction.err)
			}
			for _, mockUpdateTransaction := range tt.mockUpdateTransactions {
				mockPaymentClient.EXPECT().
					UpdateTransaction(context.Background(), newUpdateTxnArgMatcher(mockUpdateTransaction.req)).
					Return(mockUpdateTransaction.res, mockUpdateTransaction.err)
			}
			if tt.mockRespAuth.enable {
				mockVgUPIClient.EXPECT().
					RespAuthDetails(context.Background(), newRespAuthDetailsArgMatcher(tt.mockRespAuth.req)).
					Return(tt.mockRespAuth.res, tt.mockRespAuth.err)
			}
			if tt.mockCreateOrderAndTxn.enable {
				mockOrderClient.EXPECT().
					CreateOrderWithTransaction(context.Background(), newCreateOrderArgMatcher(tt.mockCreateOrderAndTxn.req)).
					Return(tt.mockCreateOrderAndTxn.res, tt.mockCreateOrderAndTxn.err)
			}

			for _, mock := range tt.mockGetPIsByIds {
				if mock.enable {
					mockPiClient.EXPECT().GetPIsByIds(context.Background(), mock.req).
						Return(mock.res, mock.err)
				}
			}

			if tt.mockGetRelationshipWithActor.enable {
				mockActorClient.EXPECT().
					GetRelationshipWithActor(context.Background(), tt.mockGetRelationshipWithActor.req).
					Return(tt.mockGetRelationshipWithActor.res, tt.mockGetRelationshipWithActor.err)
			}
			if tt.mockResolveTimelineHelper.enable {
				mockTimelineHelperSvc.EXPECT().
					ResolveTimeline(
						context.Background(),
						tt.mockResolveTimelineHelper.payer,
						tt.mockResolveTimelineHelper.payee,
						tt.mockResolveTimelineHelper.reqType,
						true,
						true,
					).
					Return(tt.mockResolveTimelineHelper.res, tt.mockResolveTimelineHelper.err)
			}
			for _, mock := range tt.mockGetPiById {
				if mock.enable {
					mockPiHelperSvc.EXPECT().GetPiById(context.Background(), mock.piId).
						Return(mock.pi, mock.err)
				}
			}

			for _, mockGetPi := range tt.mockGetPIFromVPA {
				if mockGetPi.enable {
					mockPiHelperSvc.EXPECT().
						GetPIFromVpa(context.Background(), mockGetPi.vpa).
						Return(mockGetPi.pi, mockGetPi.err)
				}
			}
			for _, mock := range tt.mockGetActorIdFromPI {
				if mock.enable {
					mockPiHelperSvc.EXPECT().GetActorIdForPi(context.Background(), mock.PiId).
						Return(mock.actorId, mock.err)
				}
			}
			for _, mock := range tt.mockGetInternalDeviceForActor {
				if mock.enable {
					mockActorProcessor.EXPECT().GetInternalDeviceFromRegisteredDevice(context.Background(), mock.actorId).
						Return(mock.res, mock.err)
				}
			}
			for _, mock := range tt.mockGetPhoneNumber {
				if mock.enable {
					mockActorProcessor.EXPECT().GetPhoneNumber(context.Background(), mock.actorId).
						Return(mock.ph, mock.err)
				}
			}
			if tt.mockGetByUmnDao.enable {
				mockMandateDao.EXPECT().GetByUmn(context.Background(), tt.mockGetByUmnDao.umn).
					Return(tt.mockGetByUmnDao.mandateEntity, tt.mockGetByUmnDao.err)
			}
			if tt.mockExecuteRecurringPayment.enable {
				mockRecurPayClient.EXPECT().ExecuteRecurringPayment(context.Background(), tt.mockExecuteRecurringPayment.req).
					Return(tt.mockExecuteRecurringPayment.res, tt.mockExecuteRecurringPayment.err)
			}
			if tt.mockGetOrderId.enable {
				mockPaymentClient.EXPECT().GetOrderId(context.Background(), tt.mockGetOrderId.req).
					Return(tt.mockGetOrderId.res, tt.mockGetOrderId.err)
			}
			if tt.mockPublish.enable {
				mockPublisher.EXPECT().PublishWithDelay(context.Background(), tt.mockPublish.payload, tt.mockPublish.delay).
					Return(tt.mockPublish.msgId, tt.mockPublish.err)
			}
			if tt.mockGetVerifiedAddressEntryByVpa.enable {
				mockVerifiedAddressEntryDao.EXPECT().GetVerifiedAddressEntryByVpa(context.Background(), tt.mockGetVerifiedAddressEntryByVpa.vpa).
					Return(tt.mockGetVerifiedAddressEntryByVpa.vaeEntity, tt.mockGetVerifiedAddressEntryByVpa.err)
			}
			if tt.mockIsNewVpaHandleAssignedToActor.enable {
				mockUpiOnboardingProcessor.EXPECT().IsNewUpiHandleAssignedToActor(context.Background(), tt.mockIsNewVpaHandleAssignedToActor.actorId).
					Return(tt.mockIsNewVpaHandleAssignedToActor.res)
			}
			got, err := consumerServer.ProcessReqAuth(context.Background(), tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessReqAuth() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessReqAuth() got = %v, want %v", got, tt.want)
			}
		})
	}
}
