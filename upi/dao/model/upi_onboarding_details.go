package model

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"time"

	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnumsPb "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/be-common/pkg/nulltypes"
)

type UpiOnboardingDetail struct {

	// Unique identifier for each row
	Id string `gorm:"type:uuid;default:gen_random_uuid();primary_key;column:id"`

	// account id of the upi account
	AccountId string

	// Upi onboarding partner bank
	Vendor commonvgpb.Vendor

	// Vpa to be linked with account
	Vpa string

	// req id used for creating a workflow request in celestial for onboarding
	ClientRequestId string

	// type of the action taken - LINKING/DELINKING
	Action upiOnboardingEnumsPb.UpiOnboardingAction

	// Current status of the account onboarding.
	// CREATED -> INITIATED -> LINKED/FAILED -> DELINKED_INITIATED -> DELINKED/MANUAL_INTERVNETION
	// If the user drops in between entry will be marked as INVALID
	Status upiOnboardingEnumsPb.UpiOnboardingStatus

	// Req id for the request initiated with vendor
	VendorRequestId nulltypes.NullString

	// payload for upi onboarding details
	Payload *upiOnboardingPb.UpiOnboardingDetailsPayload

	// default timestamps
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt
}

// NewUpiOnboardingDetail - Converts the given UpiOnboardingDetail proto to UpiOnboardingDetail model
func NewUpiOnboardingDetail(upiOnboardingDetail *upiOnboardingPb.UpiOnboardingDetail) *UpiOnboardingDetail {
	return &UpiOnboardingDetail{
		Id:              upiOnboardingDetail.GetId(),
		AccountId:       upiOnboardingDetail.GetAccountId(),
		Vendor:          upiOnboardingDetail.GetVendor(),
		Vpa:             upiOnboardingDetail.GetVpa(),
		ClientRequestId: upiOnboardingDetail.GetClientReqId(),
		VendorRequestId: nulltypes.NewNullString(upiOnboardingDetail.GetVendorReqId()),
		Action:          upiOnboardingDetail.GetAction(),
		Status:          upiOnboardingDetail.GetStatus(),
		Payload:         upiOnboardingDetail.GetPayload(),
	}
}

// ToProto - Converts the given UpiOnboardingDetail model to UpiOnboardingDetail proto
func (u *UpiOnboardingDetail) ToProto() *upiOnboardingPb.UpiOnboardingDetail {
	if u == nil {
		return nil
	}
	proto := &upiOnboardingPb.UpiOnboardingDetail{
		Id:          u.Id,
		AccountId:   u.AccountId,
		Vendor:      u.Vendor,
		Vpa:         u.Vpa,
		ClientReqId: u.ClientRequestId,
		Action:      u.Action,
		Status:      u.Status,
		VendorReqId: u.VendorRequestId.String,
		CreatedAt:   timestamppb.New(u.CreatedAt),
		UpdatedAt:   timestamppb.New(u.UpdatedAt),
		Payload:     u.Payload,
	}

	if u.DeletedAt.Valid {
		proto.DeletedAt = timestamppb.New(u.DeletedAt.Time)
	}

	return proto
}

// NewUpiOnboardingDetail - Converts the given UpiOnboardingDetail proto to UpiOnboardingDetail model
func NewUpiOnboardingDetails(upiOnboardingDetails []*upiOnboardingPb.UpiOnboardingDetail) []*UpiOnboardingDetail {
	var upiOnboardingDetailModels []*UpiOnboardingDetail
	for _, upiOnboardingDetailProto := range upiOnboardingDetails {
		upiOnboardingDetailModels = append(upiOnboardingDetailModels, NewUpiOnboardingDetail(upiOnboardingDetailProto))
	}
	return upiOnboardingDetailModels
}
