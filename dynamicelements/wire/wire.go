//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/cache"
	types2 "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/events"
	questSdkGenConf "github.com/epifi/be-common/quest/sdk/config/genconf"
	crossAttachPb "github.com/epifi/gamma/api/acquisition/crossattach"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	connectedaccountpb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/feature/release"

	actorPb "github.com/epifi/gamma/api/actor"
	dynamicelements1 "github.com/epifi/gamma/api/analyser/dynamicelements"
	"github.com/epifi/gamma/api/bankcust"
	compliancePb "github.com/epifi/gamma/api/bankcust/compliance"
	"github.com/epifi/gamma/api/casper"
	tcpb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/firefly"
	invNotificationPb "github.com/epifi/gamma/api/investment/mutualfund/notifications"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/preapprovedloan"
	questManagerPb "github.com/epifi/gamma/api/quest/manager"
	riskPb "github.com/epifi/gamma/api/risk"
	salaryProgramPb "github.com/epifi/gamma/api/salaryprogram"
	savingsClientPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	usstocksPb "github.com/epifi/gamma/api/usstocks/uss_dynamic_elements"
	"github.com/epifi/gamma/dynamicelements"
	"github.com/epifi/gamma/dynamicelements/collector"
	"github.com/epifi/gamma/dynamicelements/config"
	dynamicElementsGenConf "github.com/epifi/gamma/dynamicelements/config/genconf"
	"github.com/epifi/gamma/dynamicelements/sort"
	wireTypes "github.com/epifi/gamma/dynamicelements/wire/types"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	questsdk "github.com/epifi/gamma/quest/sdk"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
)

func CollectorFactoryProvider(inAppTargetedCommsClient tcpb.InAppTargetedCommsClient,
	investmentNotificationClient invNotificationPb.NotificationsClient,
	vkycClient vkyc.VKYCFeClient, savingsClient savingsClientPb.SavingsClient, palClient preapprovedloan.PreApprovedLoanClient,
	fireflyClient firefly.FireflyClient, usstocksClient usstocksPb.USSDynamicElementsManagerClient, tieringClient tieringPb.TieringClient, riskClient riskPb.RiskClient, payClient pay.PayClient, employmentFeClient employment.EmploymentFeClient,
	offerCatalogServiceClient casper.OfferCatalogServiceClient,
	compClient compliancePb.ComplianceClient, salaryProgramClient salaryProgramPb.SalaryProgramClient, bankCustClient bankcust.BankCustomerServiceClient,
	analyserDEClient dynamicelements1.DynamicElementsClient, networthClient networth.NetWorthClient, caClient connectedaccountpb.ConnectedAccountClient, panClient pan.PanClient,
	cardProvisioningClient cpPb.CardProvisioningClient) collector.IDynamicElementsCollectorFactory {
	dynamicElementsCollectorFactory := collector.NewDynamicElementsCollectorFactory()
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_COMMS_SERVICE, inAppTargetedCommsClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_INVESTMENT_SERVICE, investmentNotificationClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_KYC_SERVICE, vkycClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_SAVINGS_SERVICE, savingsClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_PRE_APPROVED_LOAN_SERVICE, palClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_FIREFLY_SERVICE, fireflyClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_US_STOCKS_SERVICE, usstocksClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_TIERING_SERVICE, tieringClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_RISK_SERVICE, riskClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_PAY_SERVICE, payClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_EMPLOYMENT_SERVICE, employmentFeClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_KYC_COMPLIANCE_SERVICE, compClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_CASPER_SERVICE, offerCatalogServiceClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_SALARY_PROGRAM_SERVICE, salaryProgramClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_BANK_CUSTOMER_SERVICE, bankCustClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_ANALYSER_SERVICE, analyserDEClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_NET_WORTH_SERVICE, networthClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_CONNECTED_ACC_SERVICE, caClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_PAN_SERVICE, panClient)
	dynamicElementsCollectorFactory.RegisterDynamicElementsCollector(types.ServiceName_CARD_SERVICE, cardProvisioningClient)

	return dynamicElementsCollectorFactory
}
func InitializeDynamicElementsService(conf *config.Config, genconf *dynamicElementsGenConf.Config, questCacheStorage types2.QuestCacheStorage, actorClient actorPb.ActorClient,
	userClient userPb.UsersClient, userGroupClient userGroupPb.GroupClient, inAppTargetedCommsClient wireTypes.InAppTargetedCommsClientWithInterceptors,
	investmentNotificationClient invNotificationPb.NotificationsClient,
	vkycClient vkyc.VKYCFeClient, savingsClient savingsClientPb.SavingsClient, palClient preapprovedloan.PreApprovedLoanClient,
	fireflyClient firefly.FireflyClient, usstocksClient usstocksPb.USSDynamicElementsManagerClient, tieringClient tieringPb.TieringClient, riskClient riskPb.RiskClient, payClient pay.PayClient,
	segmentClient segmentPb.SegmentationServiceClient, questManagerCl questManagerPb.ManagerClient, eventsBroker events.Broker, employmentFeClient employment.EmploymentFeClient, offerCatalogServiceClient casper.OfferCatalogServiceClient,
	compClient compliancePb.ComplianceClient, salaryProgramClient salaryProgramPb.SalaryProgramClient, bankCustClient bankcust.BankCustomerServiceClient, onboardingClient onboarding.OnboardingClient,
	analyserDEClient dynamicelements1.DynamicElementsClient, networthClient networth.NetWorthClient, caClient connectedaccountpb.ConnectedAccountClient, panClient pan.PanClient,
	cardProvisioningClient cpPb.CardProvisioningClient, crossAttachClient crossAttachPb.CrossAttachClient) *dynamicelements.DynamicElementsService {
	wire.Build(
		wireTypes.InAppTargetedCommsClientProvider,
		CollectorFactoryProvider,
		dynamicelements.NewDynamicElementsService,
		sort.NewSortingStrategyFactory,
		sort.NewRandomSortingStrategy,
		sort.NewPrioritySortingStrategy,
		sort.NewSegmentSortingStrategy,
		sort.NewEligibilityIntentSortingStrategy,
		sort.NewSoftIntentSortingStrategy,
		sort.NewCrossAttachSortingStrategy,
		sort.NewNonB2BBaseTierSortingStrategy,
		InitializeQuestSdkClient,
		release.EvaluatorWireSet,
		FeatureReleaseConfigProvider,
	)
	return &dynamicelements.DynamicElementsService{}
}

func InitializeQuestSdkClient(
	questCacheStorage types2.QuestCacheStorage,
	genconf *dynamicElementsGenConf.Config,
	segmentClient segmentPb.SegmentationServiceClient,
	actorClient actorPb.ActorClient,
	usersClient userPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	questManagerCl questManagerPb.ManagerClient,
	eventsBroker events.Broker) *questsdk.Client {
	wire.Build(
		QuestCacheStorageProvider,
		questSDKClientConfProvider,
		questsdkinit.GetQuestSDKClient,
	)
	return &questsdk.Client{}
}

func questSDKClientConfProvider(conf *dynamicElementsGenConf.Config) *questSdkGenConf.Config {
	return conf.QuestSdk()
}

func QuestCacheStorageProvider(cacheStorage types2.QuestCacheStorage) cache.CacheStorage {
	return cacheStorage
}

func FeatureReleaseConfigProvider(conf *dynamicElementsGenConf.Config) *releaseGenConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}
