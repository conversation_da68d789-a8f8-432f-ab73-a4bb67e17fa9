//nolint:gosec
package ignosis

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/labstack/gommon/random"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	igVendorPb "github.com/epifi/gamma/api/vendors/aa/analytics/ignosis"
)

type IgnosisAaService struct {
	stubs map[string]string
}

func NewIgnosisAaService(
	stubs map[string]string,
) *IgnosisAaService {
	return &IgnosisAaService{
		stubs: stubs,
	}
}

// This represents map of reference id to time of creation of fast api request to temporily behave like delay in process.
// Initially simulator will respond as created, then processing after 1 min and then completed after 2 mins.
// This is based on assumption that simulator will not use more than on instance at a time. Corner case will only be on deployment time,
// which is very less likely and even if got this case, only result will be to need to wait more than expected.
var globalReferenceIdMap = map[string]time.Time{}

func (s *IgnosisAaService) SyncFastAnalysis(w http.ResponseWriter, req *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	data, err := io.ReadAll(req.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read request body", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(err.Error()))
		return
	}
	reqBody := &igVendorPb.SyncFastAnalysisRequest{}
	if err = protojson.Unmarshal(data, reqBody); err != nil {
		logger.ErrorNoCtx("failed to unmarshal request into struct", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(err.Error()))
		return
	}

	cfgDir, err := cfg.GetConfigDir()
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte("could not get config directory"))
		return
	}
	fileContent, err := os.ReadFile(filepath.Join(cfgDir, s.stubs["ignosisfastresponse"]))
	if err != nil {
		logger.ErrorNoCtx("error reading fast response file", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(err.Error()))
		return
	}

	// Parse the JSON response
	var responseMap map[string]interface{}
	if err := json.Unmarshal(fileContent, &responseMap); err != nil {
		logger.ErrorNoCtx("error parsing response JSON", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(err.Error()))
		return
	}

	// Update trackingId and referenceId
	responseMap["trackingId"] = reqBody.GetTrackingId()
	refId := random.String(32, random.Alphanumeric)
	responseMap["referenceId"] = refId

	// Update linkedAccRef in subAnalyticsResponse
	if subAnalytics, ok := responseMap["subAnalyticsResponse"].(map[string]interface{}); ok {
		if accountProfiles, ok := subAnalytics["accountProfiles"].([]interface{}); ok {
			logger.InfoNoCtx("accountProfiles", zap.Any("response", accountProfiles))
			if len(accountProfiles) > 0 {
				if account, ok := accountProfiles[0].(map[string]interface{})["Account"].(map[string]interface{}); ok {
					// Get the first data item from the request
					if len(reqBody.GetData()) > 0 {
						firstReqBodyData := reqBody.GetData()[0]
						if firstReqBodyData.GetProfile() != nil && firstReqBodyData.GetProfile().GetAccount() != nil {
							logger.InfoNoCtx(fmt.Sprintf("updating linked acc ref in subAnalyticsResponse with value in req body: %s", firstReqBodyData.GetProfile().GetAccount().GetLinkedAccRef()))
							account["linkedAccRef"] = firstReqBodyData.GetProfile().GetAccount().GetLinkedAccRef()
						}
					}
				}
			}
		}
	}

	// Convert back to JSON
	fileContentStr, err := json.Marshal(responseMap)
	if err != nil {
		logger.ErrorNoCtx("error marshaling response", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(err.Error()))
		return
	}

	globalReferenceIdMap[refId] = time.Now()
	w.WriteHeader(http.StatusOK)
	_, err = w.Write(fileContentStr)
	if err != nil {
		logger.ErrorNoCtx("error while writing response", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(err.Error()))
		return
	}
	return
}

func (s *IgnosisAaService) SyncDetailedAnalysis(w http.ResponseWriter, req *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	urlSplit := strings.Split(req.URL.Path, "/")
	trackingId := urlSplit[len(urlSplit)-2]
	referenceId := urlSplit[len(urlSplit)-1]

	cfgDir, err := cfg.GetConfigDir()
	if err != nil {
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte("could not get config directory"))
		return
	}
	fileContent, err := os.ReadFile(filepath.Join(cfgDir, s.stubs["ignosisdetailedresponse"]))
	if err != nil {
		logger.ErrorNoCtx("error reading detailed response file", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(err.Error()))
		return
	}
	// Convert file content to string
	fileContentStr := string(fileContent)

	// Step 2: Replace the field value
	// The old value (make sure this matches exactly in the JSON)
	oldValue1 := `"trackingId": "Detailed-Analysis-Sample-Response"`
	oldValue2 := `"referenceId": "74435baeb1ea41a2ab6930847a7933ad"`
	// The new value
	newValue1 := `"trackingId": "` + trackingId + `"`
	newValue2 := `"referenceId": "` + referenceId + `"`

	// Replace the old values with the new values
	fileContentStr = strings.Replace(fileContentStr, oldValue1, newValue1, 1)
	fileContentStr = strings.Replace(fileContentStr, oldValue2, newValue2, 1)

	w.WriteHeader(http.StatusOK)
	_, err = w.Write([]byte(fileContentStr))
	if err != nil {
		logger.ErrorNoCtx("error while writing response", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(err.Error()))
		return
	}
	return
}

func (s *IgnosisAaService) SyncDetailedAnalysisStatus(w http.ResponseWriter, req *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	urlSplit := strings.Split(req.URL.Path, "/")
	trackingId := urlSplit[len(urlSplit)-2]
	referenceId := urlSplit[len(urlSplit)-1]

	status := "CREATED"
	m, ok := globalReferenceIdMap[referenceId]
	if !ok {
		globalReferenceIdMap[referenceId] = time.Now()
	} else {
		// if elapsed time is greater than 5 seconds, change status to PROCESSING
		if datetime.IsBefore(timestamp.New(m.Add(5*time.Second)), timestamp.Now()) {
			status = "PROCESSING"
		}
		// if elapsed time is greater than 15 seconds, change status to COMPLETED
		if datetime.IsBefore(timestamp.New(m.Add(15*time.Second)), timestamp.Now()) {
			status = "COMPLETED"
		}
	}

	resData := &igVendorPb.DetailedAnalysisStatusResponse{
		TrackingId:  trackingId,
		ReferenceId: referenceId,
		JobStatus:   status,
	}

	res, err := json.Marshal(resData)
	if err != nil {
		logger.ErrorNoCtx("failed to marshal SyncDetailedAnalysisStatusResponse", zap.Error(err))
		return
	}

	w.WriteHeader(http.StatusOK)
	_, err = w.Write(res)
	if err != nil {
		logger.ErrorNoCtx("error while writing response", zap.Error(err))
		w.WriteHeader(http.StatusInternalServerError)
		_, _ = w.Write([]byte(err.Error()))
		return
	}
	return
}
