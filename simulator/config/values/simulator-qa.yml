Application:
  Environment: "qa"
  Name: "simulator"

  # TODO(pruthvi): Generate and add backup keys across environments
  PartnerSdkKeyMap:
    "ANDROID":
      PrimaryEncryptionKey:
        KeyId: "d6b0cf3d-afff-475b-b5e9-70a6107698a5"
        PublicKey: "MIIBMTCB6gYHKoZIzj0CATCB3gIBATArBgcqhkjOPQEBAiB/////////////////////////////////////////7TBEBCAqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqYSRShRAQge0Je0Je0Je0Je0Je0Je0Je0Je0Je0Je0JgtenHcQyGQEQQQqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq0kWiCuGaG4oIa04B7dLHdI0UySPU1+bXxhsinpxaJ+ztPZAiAQAAAAAAAAAAAAAAAAAAAAFN753qL3nNZYEmMaXPXT7QIBCANCAAR7IHZRbHVLk+Xfw0K4/aTHhzEncASB3V3h2hHSe/7BQn3jUYsjEteEe9nUChuMmVJ5YpAQyucTxLGF4McX7Qik"
        PrivateKey: "MIICRwIBADCB6gYHKoZIzj0CATCB3gIBATArBgcqhkjOPQEBAiB/////////////////////////////////////////7TBEBCAqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqYSRShRAQge0Je0Je0Je0Je0Je0Je0Je0Je0Je0Je0JgtenHcQyGQEQQQqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq0kWiCuGaG4oIa04B7dLHdI0UySPU1+bXxhsinpxaJ+ztPZAiAQAAAAAAAAAAAAAAAAAAAAFN753qL3nNZYEmMaXPXT7QIBCASCAVMwggFPAgEBBCAPZ12ZHW/OkzQ+Ak7GHtLf4q4Z4Jwophhm879/1as2OaCB4TCB3gIBATArBgcqhkjOPQEBAiB/////////////////////////////////////////7TBEBCAqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqYSRShRAQge0Je0Je0Je0Je0Je0Je0Je0Je0Je0Je0JgtenHcQyGQEQQQqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq0kWiCuGaG4oIa04B7dLHdI0UySPU1+bXxhsinpxaJ+ztPZAiAQAAAAAAAAAAAAAAAAAAAAFN753qL3nNZYEmMaXPXT7QIBCKFEA0IABHsgdlFsdUuT5d/DQrj9pMeHMSdwBIHdXeHaEdJ7/sFCfeNRiyMS14R72dQKG4yZUnlikBDK5xPEsYXgxxftCKQ="
        IsCompromised: false
      BackupEncryptionKey:
        KeyId: ""
        PublicKey: ""
        PrivateKey: ""
        IsCompromised: ""
      PrimarySigningKey:
        KeyId: "ff1068f7-aee4-4cc9-82fb-af3a0e1b483b"
        PublicKey: "MCowBQYDK2VwAyEAiI+88hKoCD0pxR7r7NCXGkaMgzxriH7WuiNJ7tW7bkg="
        PrivateKey: "MFECAQEwBQYDK2VwBCIEIMdlhm14jSJXqLSexX1RCHbaJr9AO2KL2V9s4i+fZrAzgSEAiI+88hKoCD0pxR7r7NCXGkaMgzxriH7WuiNJ7tW7bkg="
        IsCompromised: false
      BackupSigningKey:
        KeyId: ""
        PublicKey: ""
        PrivateKey: ""
        IsCompromised: ""
    "IOS":
      PrimaryEncryptionKey:
        KeyId: "d6b0cf3d-afff-475b-b5e9-70a6107698a5"
        PublicKey: "CNeLnboFEtsBCs4BCj10eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5FY2llc0FlYWRIa2RmUHVibGljS2V5EooBEkQKBAgCEAMSOhI4CjB0eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5BZXNHY21LZXkSAhAQGAEYARog6JG8/taVFyPwyT4JJrJNug4fI3QxstA1p3rUBSJ0A2UiIAv2Zr3zabSiv3C0v1NoyMol8zVaxQqYEzPbLhsSh/4tGAMQARjXi526BSAB"
        PrivateKey: "CNeLnboFEoECCvQBCj50eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5FY2llc0FlYWRIa2RmUHJpdmF0ZUtleRKvARKKARJECgQIAhADEjoSOAowdHlwZS5nb29nbGVhcGlzLmNvbS9nb29nbGUuY3J5cHRvLnRpbmsuQWVzR2NtS2V5EgIQEBgBGAEaIOiRvP7WlRcj8Mk+CSayTboOHyN0MbLQNad61AUidANlIiAL9ma982m0or9wtL9TaMjKJfM1WsUKmBMz2y4bEof+LRog1rA4KBwwwDYQ8FclfGvWkDU0EG3vYYWYvB6XVKVrMzYYAhABGNeLnboFIAE="
        IsCompromised: false
      BackupEncryptionKey:
        KeyId: ""
        PublicKey: ""
        PrivateKey: ""
        IsCompromised: ""
      PrimarySigningKey:
        KeyId: "ff1068f7-aee4-4cc9-82fb-af3a0e1b483b"
        PublicKey: "CNbstfICEmsKXwo3dHlwZS5nb29nbGVhcGlzLmNvbS9nb29nbGUuY3J5cHRvLnRpbmsuRWQyNTUxOVB1YmxpY0tleRIiEiBjts6pD4sCHGnmBcsKHzg70i8+HcX7DwORO0M5NpF23BgDEAEY1uy18gIgAQ=="
        PrivateKey: "CNbstfICEpEBCoQBCjh0eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5FZDI1NTE5UHJpdmF0ZUtleRJGEiCHw3GxdJARrGqFdUqDvL3WbEgNvljTVmM1ArsVl5x3KRoiEiBjts6pD4sCHGnmBcsKHzg70i8+HcX7DwORO0M5NpF23BgCEAEY1uy18gIgAQ=="
        IsCompromised: false
      BackupSigningKey:
        KeyId: ""
        PublicKey: ""
        PrivateKey: ""
        IsCompromised: ""
  PartnerSDKJavaUrl: "https://partnersdk.pointz.in/getSessionParams"
  M2PPartnerSdkKeyMap:
    "ANDROID":
      PrimaryEncryptionKey:
        KeyId: "d6b0cf3d-afff-475b-b5e9-70a6107698a5"
        PublicKey: "MIIBMTCB6gYHKoZIzj0CATCB3gIBATArBgcqhkjOPQEBAiB/////////////////////////////////////////7TBEBCAqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqYSRShRAQge0Je0Je0Je0Je0Je0Je0Je0Je0Je0Je0JgtenHcQyGQEQQQqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq0kWiCuGaG4oIa04B7dLHdI0UySPU1+bXxhsinpxaJ+ztPZAiAQAAAAAAAAAAAAAAAAAAAAFN753qL3nNZYEmMaXPXT7QIBCANCAAR7IHZRbHVLk+Xfw0K4/aTHhzEncASB3V3h2hHSe/7BQn3jUYsjEteEe9nUChuMmVJ5YpAQyucTxLGF4McX7Qik"
        PrivateKey: "MIICRwIBADCB6gYHKoZIzj0CATCB3gIBATArBgcqhkjOPQEBAiB/////////////////////////////////////////7TBEBCAqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqYSRShRAQge0Je0Je0Je0Je0Je0Je0Je0Je0Je0Je0JgtenHcQyGQEQQQqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq0kWiCuGaG4oIa04B7dLHdI0UySPU1+bXxhsinpxaJ+ztPZAiAQAAAAAAAAAAAAAAAAAAAAFN753qL3nNZYEmMaXPXT7QIBCASCAVMwggFPAgEBBCAPZ12ZHW/OkzQ+Ak7GHtLf4q4Z4Jwophhm879/1as2OaCB4TCB3gIBATArBgcqhkjOPQEBAiB/////////////////////////////////////////7TBEBCAqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqYSRShRAQge0Je0Je0Je0Je0Je0Je0Je0Je0Je0Je0JgtenHcQyGQEQQQqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq0kWiCuGaG4oIa04B7dLHdI0UySPU1+bXxhsinpxaJ+ztPZAiAQAAAAAAAAAAAAAAAAAAAAFN753qL3nNZYEmMaXPXT7QIBCKFEA0IABHsgdlFsdUuT5d/DQrj9pMeHMSdwBIHdXeHaEdJ7/sFCfeNRiyMS14R72dQKG4yZUnlikBDK5xPEsYXgxxftCKQ="
        IsCompromised: false
      BackupEncryptionKey:
        KeyId: ""
        PublicKey: ""
        PrivateKey: ""
        IsCompromised: ""
      PrimarySigningKey:
        KeyId: "ff1068f7-aee4-4cc9-82fb-af3a0e1b483b"
        PublicKey: "MCowBQYDK2VwAyEAiI+88hKoCD0pxR7r7NCXGkaMgzxriH7WuiNJ7tW7bkg="
        PrivateKey: "MFECAQEwBQYDK2VwBCIEIMdlhm14jSJXqLSexX1RCHbaJr9AO2KL2V9s4i+fZrAzgSEAiI+88hKoCD0pxR7r7NCXGkaMgzxriH7WuiNJ7tW7bkg="
        IsCompromised: false
      BackupSigningKey:
        KeyId: ""
        PublicKey: ""
        PrivateKey: ""
        IsCompromised: ""
    "IOS":
      PrimaryEncryptionKey:
        KeyId: "d6b0cf3d-afff-475b-b5e9-70a6107698a5"
        PublicKey: "CNeLnboFEtsBCs4BCj10eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5FY2llc0FlYWRIa2RmUHVibGljS2V5EooBEkQKBAgCEAMSOhI4CjB0eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5BZXNHY21LZXkSAhAQGAEYARog6JG8/taVFyPwyT4JJrJNug4fI3QxstA1p3rUBSJ0A2UiIAv2Zr3zabSiv3C0v1NoyMol8zVaxQqYEzPbLhsSh/4tGAMQARjXi526BSAB"
        PrivateKey: "CNeLnboFEoECCvQBCj50eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5FY2llc0FlYWRIa2RmUHJpdmF0ZUtleRKvARKKARJECgQIAhADEjoSOAowdHlwZS5nb29nbGVhcGlzLmNvbS9nb29nbGUuY3J5cHRvLnRpbmsuQWVzR2NtS2V5EgIQEBgBGAEaIOiRvP7WlRcj8Mk+CSayTboOHyN0MbLQNad61AUidANlIiAL9ma982m0or9wtL9TaMjKJfM1WsUKmBMz2y4bEof+LRog1rA4KBwwwDYQ8FclfGvWkDU0EG3vYYWYvB6XVKVrMzYYAhABGNeLnboFIAE="
        IsCompromised: false
      BackupEncryptionKey:
        KeyId: ""
        PublicKey: ""
        PrivateKey: ""
        IsCompromised: ""
      PrimarySigningKey:
        KeyId: "ff1068f7-aee4-4cc9-82fb-af3a0e1b483b"
        PublicKey: "CNbstfICEmsKXwo3dHlwZS5nb29nbGVhcGlzLmNvbS9nb29nbGUuY3J5cHRvLnRpbmsuRWQyNTUxOVB1YmxpY0tleRIiEiBjts6pD4sCHGnmBcsKHzg70i8+HcX7DwORO0M5NpF23BgDEAEY1uy18gIgAQ=="
        PrivateKey: "CNbstfICEpEBCoQBCjh0eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5FZDI1NTE5UHJpdmF0ZUtleRJGEiCHw3GxdJARrGqFdUqDvL3WbEgNvljTVmM1ArsVl5x3KRoiEiBjts6pD4sCHGnmBcsKHzg70i8+HcX7DwORO0M5NpF23BgCEAEY1uy18gIgAQ=="
        IsCompromised: false
      BackupSigningKey:
        KeyId: ""
        PublicKey: ""
        PrivateKey: ""
        IsCompromised: ""
  Cams:
    S3Bucket: "epifi-qa-mutualfund"
    CamsS3Bucket: "epifi-qa-mutualfund"

  Liveness:
    LivenessBucketName: "epifi-qa-liveness"


Server:
  Port: 8080
  GrpcPort: 9090
  GrpcHttpPort: 9091
  HealthCheckPort: 9999
  UnsecureHttpPort: 8079

SimulatorDb:
  AppName: "simulator"
  StatementTimeout: 1s
  Username: "simulator_dev_user"
  Password: ""
  Name: "simulator"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.simulator_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.simulator_dev_user.key"
  MaxOpenConn: 40
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

ComplaintStatusSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-complaint-status-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ComplaintStatusPublisher:
  QueueName: "qa-simulator-complaint-status-queue"


FederalPaymentEngineSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-federal-payment-engine-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

FederalPaymentEnginePublisher:
  QueueName: "qa-simulator-federal-payment-engine-queue"

FederalUpiCallBackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-federal-upi-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

FederalUpiCallBackPublisher:
  QueueName: "qa-simulator-federal-upi-callback-queue"

FederalPaymentCallBackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-federal-payment-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

FederalPaymentCallBackPublisher:
  QueueName: "qa-simulator-federal-payment-callback-queue"

FederalDepositEngineSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-federal-deposit-engine-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

FederalDepositEnginePublisher:
  QueueName: "qa-simulator-federal-deposit-engine-queue"

FederalDepositCallBackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-federal-deposit-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

FederalDepositCallBackPublisher:
  QueueName: "qa-simulator-federal-deposit-callback-queue"

FederalCardCallBackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-federal-card-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

FederalCardDispatchCallBackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-federal-card-dispatch-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

FederalCardDispatchCallBackPublisher:
  QueueName: "qa-simulator-federal-card-dispatch-callback-queue"

FederalInboundTransactionNotificationPublisher:
  QueueName: "qa-simulator-inbound-transaction-notification-queue"

FederalInboundTransactionNotificationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-inbound-transaction-notification-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

FederalCardCallBackPublisher:
  QueueName: "qa-simulator-federal-card-callback-queue"

FederalShippingAddressUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-federal-shipping-address-update-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

FederalShippingAddressUpdatePublisher:
  QueueName: "qa-simulator-federal-shipping-address-update-callback-queue"

FederalAccountsCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-simulator-accounts-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

FederalAccountsCallbackPublisher:
  QueueName: "qa-simulator-accounts-callback-queue"

UploadCreditMISToVendorSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-mf-upload-credit-mis-non-prod-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

Stubs:
  # keep the keys lowercase. Config load turns it to lowercase by default.
  ckyc: "./stubs/ckyc_stub_test.json"
  pan: "./stubs/pan_stub.json"
  unnamecheck: "./stubs/unnamecheck_stub.json"
  wealthonb: "./stubs/wealthonb_stub.json"
  fiftyfin: "./stubs/fiftyfin_stub.json"
  alpacaform1042: "./stubs/alpaca_form_1042s.pdf"
  alpacamonthlystatement: "./stubs/alpaca_monthly_statement.pdf"
  ignosisfastresponse: "./stubs/ignosis_fast_response.json"
  ignosisdetailedresponse: "./stubs/ignosis_detailed_response.json"

PlStubs:
  liquiloans: "./stubs/liquiloans_stub.json"
  federal: "./stubs/federal_stub.json"

# amounts are in paisa
PaymentProtocolDecisionParams:
  ImpsMinAmount: 100
  ImpsMaxAmount: 200000000
  RtgsMinAmount: 0
  RtgsStartTime: "07:00"
  RtgsEndTime:   "18:00"
  TimeZoneLocation: "Asia/Kolkata"
  RTGSLastWeekDay: 5
  UpiMaxAmount: 10000000

DeviceRegistrationRetryCount: 3

UPICallbackUrl: "https://vnotificationgw.qa.pointz.in/upi/federal"

AclDlrStatusCallbackUrl: "https://vnotificationgw.qa.pointz.in/whatsapp/callback/acl/UrlListner/requestListener"

Secrets:
  Ids:
    FederalPgpPassphrase: "qa/simulator/pgp-federal-key-passphrase"
    FederalPgpPrivateKey : "qa/simulator/pgp-federal-dummy-private-key"
    EpifiFederalPgpPublicKey: "qa/simulator/pgp-epifi-pub-key-for-federal"
    EpifiFederalRsaPublicKey: "qa/simulator/rsa-epifi-pub-key-for-carddata-federal"
    SenderCode: "qa/vg-vn-simulator-vgpci/federal-auth-sender-code"
    CamsSftpUser: "qa/investment/cams-sftp-user"
    CamsSftpPass: "qa/investment/cams-sftp-pass"
    CamsSftpSshKey: "qa/investment/cams-sftp-ssh-key"
    CommonSftpUser: "qa/vendorgateway-simulator/sftp-upload-user"
    CommonSftpPass: "qa/vendorgateway-simulator/sftp-upload-pass"

Flags:
  TrimDebugMessageFromStatus: false

InboundTxnNotification:
  Url: "https://vnotificationgw.qa.pointz.in/openbanking/payment/federal/notification"

AaErrorThreshold: 0
AaConsentNotificationEp: "https://vnotificationgw.qa.pointz.in/Consent/Notification"
AaFiNotificationEp: "https://vnotificationgw.qa.pointz.in/FI/Notification"
AaAccountLinkNotificationEp: "https://vnotificationgw.qa.pointz.in/Account/link/Notification"
NumOfAaTxns: 5

SmallCase:
  MFHoldingsImportCallbackEndpoint: "https://vnotificationgw.qa.pointz.in/smallcase/mf/holdings"
  EncryptAndSignUrl: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/encryptAndSign"
  VerifyAndDecryptUrl: "https://nsdl-forwardsecrecy.deploy.pointz.in/mfcentral/v1/verifyAndDecrypt"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

IsBalEnqSendFailureResult: false


EPAN:
  EPANCallbackUrl: "https://vnotificationgw.qa.pointz.in/epan/karza/response"

DeviceRegistration:
  SMSAcknowledgementURL: "https://vnotificationgw.qa.pointz.in/openbanking/auth/federal/user-device/registration-sms-update"

PGPInMemoryEntityStoreParams:
  InternalEntity:
    - Secret: "qa/pgp/v1/federal-simulator"
  ExternalEntity:
    - Secret: "qa/pgp/v1/pgp-epifi-fed-api"

ITR:
  ComputedIncome: 1919199

MFHoldingsImport:
  SimulateInvalidPanMobile:
    - "**********": true
  SimulateInvalidPanEmail:
    - "<EMAIL>": true

CreditReportCibil:
  PingResponse: "SUCCESS"
  FulfillOfferResponse: "SUCCESS"
  GetQuestionsResponse: "SUCCESS"
  VerifyQuestionsResponse: "SUCCESS"
  GetAssetsResponse: "SUCCESS"
  GetWebTokenResponse: "SUCCESS"

KYCTypeChangeCallbackUrl: "https://vnotificationgw.qa.pointz.in/openbanking/kyctypechange/federal"

OnsurityDocumentWebUrl: "https://test-doc-url.com/"

Lending:
  PreApprovedLoan:
    Lenden:
      KYCCompletionDelay: 10s
      MandateCompletionDelay: 10s
      ROIModificationDeadlineDelay: 90s
    EnableExperianBeforeCibil: true
