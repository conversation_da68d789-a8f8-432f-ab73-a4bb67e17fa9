package preapprovedloan

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	structPb "google.golang.org/protobuf/types/known/structpb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	preapprovedloanPb "github.com/epifi/gamma/api/simulator/lending/preapprovedloan"
	vendorsLendenPb "github.com/epifi/gamma/api/vendors/lenden"
	"github.com/epifi/gamma/simulator"
	vgLendenPb "github.com/epifi/gamma/vendorgateway/lending/preapprovedloan/lenden"
)

const (
	loanTenureInMonths        = 5
	paymentPendingStatusValue = "PENDING"
	paymentSuccessStatusValue = "SUCCESS"
	paymentLink               = "https://fi.money/"
	trackingIdDelimiter       = ":"
)

// PanResponseStruct encapsulates various response types for a given PAN.
type PanResponseStruct struct {
	createUserRes    *vendorsLendenPb.CreateUserResponseWrapper
	initKYCRes       *vendorsLendenPb.KycInitResponseWrapper
	checkKYCRes      *vendorsLendenPb.CheckKycStatusResponseWrapper
	hardEligRes      *vendorsLendenPb.CheckHardEligibilityResponseWrapper
	addBankRes       *vendorsLendenPb.AddBankDetailsResponseWrapper
	initMandateRes   *vendorsLendenPb.InitMandateResponseWrapper
	mandateStatusRes *vendorsLendenPb.CheckMandateStatusResponseWrapper
	generateKfsLaRes *vendorsLendenPb.GenerateKfsLaResponseWrapper
}

var panResponseMap = map[string]*PanResponseStruct{
	"**********": {
		// Hard eligibility rejection mapping.
		hardEligRes: &vendorsLendenPb.CheckHardEligibilityResponseWrapper{
			Message: "Eligibility Check Failed",
			Response: &vendorsLendenPb.CheckHardEligibilityResponseWrapper_ResponseData{
				TraceId:     uuid.NewString(),
				MessageCode: "4001",
				Message:     "User not eligible",
				ResponseData: &vendorsLendenPb.CheckHardEligibilityResponseWrapper_CheckHardEligibilityResponse{
					Action: "REJECTED",
				},
			},
		},
	},
	"**********": {
		// KYC failed mapping.
		checkKYCRes: &vendorsLendenPb.CheckKycStatusResponseWrapper{
			Message: "KYC Failed",
			Response: &vendorsLendenPb.CheckKycStatusResponseWrapper_ResponseData{
				TraceId:     uuid.NewString(),
				MessageCode: "2001",
				Message:     "KYC Failed",
				ResponseData: &vendorsLendenPb.CheckKycStatusResponseWrapper_CheckKycStatusResponse{
					KycStatus: "FAILED",
				},
			},
		},
	},
	"**********": {
		// Invalid bank details mapping.
		addBankRes: &vendorsLendenPb.AddBankDetailsResponseWrapper{
			Message: "Invalid Bank Details",
			Response: &vendorsLendenPb.AddBankDetailsResponseWrapper_ResponseData{
				TraceId:      uuid.NewString(),
				MessageCode:  "4005",
				Message:      "Invalid Bank Details",
				ResponseData: &vendorsLendenPb.AddBankDetailsResponseWrapper_AddBankDetailsResponse{},
			},
		},
	},
	"PPPPP0000E": {
		// Invalid mandate mapping.
		initMandateRes: &vendorsLendenPb.InitMandateResponseWrapper{
			Message: "Mandate already set up",
			Response: &vendorsLendenPb.InitMandateResponseWrapper_ResponseData{
				TraceId:     uuid.NewString(),
				MessageCode: "4062",
				Message:     "Request processed successfully",
				ResponseData: &vendorsLendenPb.InitMandateResponseWrapper_InitMandateResponse{
					TrackingId: "",
					Umrn:       "FDRL7031604252003100",
				},
			},
		},
	},
	"PPPPP0000F": {
		// Invalid mandate mapping.
		mandateStatusRes: &vendorsLendenPb.CheckMandateStatusResponseWrapper{
			Message: "Invalid Mandate",
			Response: &vendorsLendenPb.CheckMandateStatusResponseWrapper_ResponseData{
				TraceId:     uuid.NewString(),
				MessageCode: "2001",
				Message:     "Request processed successfully",
				ResponseData: &vendorsLendenPb.CheckMandateStatusResponseWrapper_CheckMandateStatusResponse{
					MandateStatus: "FAILED",
				},
			},
		},
	},
	"**********": {
		generateKfsLaRes: &vendorsLendenPb.GenerateKfsLaResponseWrapper{
			Message: "Already signed",
			Response: &vendorsLendenPb.GenerateKfsLaResponseWrapper_ResponseData{
				TraceId:     uuid.NewString(),
				MessageCode: "4082",
				Message:     "Request processed successfully",
			},
		},
	},
}

// ExtractPanFromUserID splits the userId string (expected in "PAN_random" format)
// and returns the PAN (the first part).
func extractPanFromUserID(userId string) (string, error) {
	parts := strings.Split(userId, "_")
	if len(parts) < 2 {
		return "", errors.Errorf("invalid user ID format: %s", userId)
	}
	return parts[0], nil
}

// getResponseForPan returns the PanResponseStruct for a given PAN.
func getResponseForPan(panID string) (*PanResponseStruct, bool) {
	resp, exists := panResponseMap[panID]
	return resp, exists
}

// TODO(mohitswain): generate different response codes based on some user info
func (s *Service) LendenCommonApi(ctx context.Context, req *vendorsLendenPb.LendenEncryptedRequest) (*vendorsLendenPb.LendenEncryptedResponse, error) {
	logger.Info(ctx, "LendenCommonApi req", zap.Any("req", req))
	responsePayload, err := s.getLendenSimulatorResponse(ctx, []byte(req.GetPayload()))
	if err != nil {
		logger.Error(ctx, "error getting simulator response", zap.Error(err))
		return &vendorsLendenPb.LendenEncryptedResponse{
			MessageCode: "INTERNAL SERVER ERROR FROM SIMULATOR",
			Message:     fmt.Sprintf("error getting simulator response: %v", err),
			Response:    nil,
		}, nil
	}
	marshalResponse, err := protojson.Marshal(responsePayload)
	if err != nil {
		logger.Error(ctx, "error marshalling response", zap.Error(err))
		return &vendorsLendenPb.LendenEncryptedResponse{
			MessageCode: "INTERNAL SERVER ERROR FROM SIMULATOR",
			Message:     fmt.Sprintf("error marshalling response: %v", err),
			Response:    nil,
		}, nil
	}
	resp := &vendorsLendenPb.LendenEncryptedResponse_ResponseData{
		Payload: string(marshalResponse),
	}
	logger.Info(ctx, "LendenCommonApi response", zap.Any("resp", resp))
	return &vendorsLendenPb.LendenEncryptedResponse{
		MessageCode: "SUCCESS",
		Message:     "From simulator",
		Response:    resp,
	}, nil
}

func (s *Service) getLendenSimulatorResponse(ctx context.Context, payload []byte) (proto.Message, error) {
	var responsePayload proto.Message
	var err error
	var apiCodePayload ApiCodePayload

	// Unmarshal the payload into the common structure for API code.
	if err = json.Unmarshal(payload, &apiCodePayload); err != nil {
		return nil, fmt.Errorf("error while unmarshalling api code payload: %v", err)
	}
	apiCode := apiCodePayload.ApiCode
	switch apiCode {
	case string(vgLendenPb.InternalApiCodeCheckHardEligibility):
		responsePayload, err = s.GetHardOfferEligibilitySimulatorResp(ctx, payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting hard eligibility offer simulator response")
		}
	case string(vgLendenPb.InternalApiCodeCreateUser):
		responsePayload, err = GetCreateUserSimulatorResp(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting InternalApiCodeCreateUser simulator response")
		}
	case string(vgLendenPb.InternalApiCodeCreateLoan):
		responsePayload, err = s.GetCreateLoanSimulatorResp(ctx, payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting InternalApiCodeCreateLoan simulator response")
		}
	case string(vgLendenPb.InternalApiCodeAddEligibilityData):
		responsePayload, err = s.GetPostExternalDataSimulatorResp(ctx, payload)
		if err != nil {
			return nil, errors.New("error while post external data simulator response")
		}
	case string(vgLendenPb.InternalApiCodeGetPreDisbursementDetails):
		responsePayload, err = getPreDisbursementDetailsRes(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error getting pre disbursement details response")
		}
	case string(vgLendenPb.InternalApiCodeSelectOffer):
		responsePayload, err = GetSelectOfferSimulatorResp(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting select offer simulator response")
		}
	case string(vgLendenPb.InternalApiCodeKycInit):
		responsePayload, err = s.getInitKycSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting init kyc simulator response")
		}
	case string(vgLendenPb.InternalApiCodeCheckKycStatus):
		responsePayload, err = GetKycStatusSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting kyc status simulator response")
		}
	case string(vgLendenPb.InternalApiCodeInitMandate):
		responsePayload, err = s.getInitMandateSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting init mandate simulator response")
		}
	case string(vgLendenPb.InternalApiCodeCheckMandateStatus):
		responsePayload, err = GetMandateStatusSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting mandate status simulator response")
		}
	case string(vgLendenPb.InternalApiCodeAddBankDetails):
		responsePayload, err = GetAddBankDetailsSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting add bank details simulator response")
		}
	case string(vgLendenPb.InternalApiCodeModifyRoi):
		responsePayload, err = ModifyRoiListSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting modify roi simulator response")
		}
	case string(vgLendenPb.InternalApiCodeGenerateKfsLa):
		var isSignedPayload IsSignedPayload
		if err = json.Unmarshal(payload, &isSignedPayload); err != nil {
			return nil, fmt.Errorf("error while unmarshalling payload: %v", err)
		}
		if isSignedPayload.Json.IsSigned {
			responsePayload, err = s.getSignKfsLaSimulatorResponse(payload)
			if err != nil {
				return nil, errors.Wrap(err, "error while getting sign kfs la simulator response")
			}
		} else {
			responsePayload, err = getGenerateKfsLaSimulatorResponse(ctx, payload)
			if err != nil {
				return nil, fmt.Errorf("error while getting generate kfs la simulator response: %v", err)
			}
		}
	case string(vgLendenPb.InternalApiCodeGetLoanDetails):
		responsePayload, err = getLoanDetailsSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting loan details simulator response")
		}
	case string(vgLendenPb.InternalApiCodeGetAmortizationSchedule):
		responsePayload, err = getAmortizationScheduleSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting amortization schedule simulator response")
		}
	case string(vgLendenPb.InternalApiCodeGetForeclosureDetails):
		responsePayload, err = getForeclosureDetailsSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting foreclosure details simulator response")
		}
	case string(vgLendenPb.InternalApiCodeGeneratePaymentLink):
		responsePayload, err = getGeneratePaymentLinkSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting generate payment link simulator response")
		}
	case string(vgLendenPb.InternalApiCodeGetPaymentStatus):
		responsePayload, err = getPaymentStatusSimulatorResponse(payload)
		if err != nil {
			return nil, errors.Wrap(err, "error while getting get payment status simulator response")
		}
	default:
		return nil, errors.New("invalid api code")
	}
	return responsePayload, nil
}

func GetAddBankDetailsSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.AddBankDetailsRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling add bank details request")
	}
	userId := req.GetJson().GetUserId()
	panFromUid, err := extractPanFromUserID(userId)
	if err != nil {
		return nil, err
	}
	if resp, exists := getResponseForPan(panFromUid); exists && resp.addBankRes != nil {
		return resp.addBankRes, nil
	}
	// Default response if no mapping found.
	return &vendorsLendenPb.AddBankDetailsResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.AddBankDetailsResponseWrapper_ResponseData{
			TraceId:      uuid.NewString(),
			MessageCode:  "2001",
			Message:      "From simulator",
			ResponseData: &vendorsLendenPb.AddBankDetailsResponseWrapper_AddBankDetailsResponse{},
		},
	}, nil
}

func getLoanDetailsSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.GetLoanDetailsRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling get loan details request")
	}
	productId := req.GetJson().GetProductId()
	loanId := req.GetJson().GetLoanId()
	loanDetails := &vendorsLendenPb.LoanDetails{
		// TODO(Brijesh): Understand all these fields from LDC to simulate all cases
		Id:            loanId,
		UserId:        "PPPPP0000A_3367", // Simulated user id
		ProductId:     productId,
		PartnerLoanId: uuid.NewString(),
		Tenure: &vendorsLendenPb.TenureDetails{
			Type:  "MONTHLY",
			Value: loanTenureInMonths,
		},
		Interest: &vendorsLendenPb.InterestDetails{
			Type:  "FLAT",
			Value: 46.8,
		},
		SanctionedAmount:     50_000,
		DisbursedAmount:      47_050,
		ProcessingFee:        2_950,
		PrincipalOutstanding: 50_000,
		LoanAmortisationType: "EQUAL_INSTALLMENTS",
		FirstDueDate:         datetime.StartOfMonth(time.Now().AddDate(0, 1, 0)).Format(datetime.DATE_LAYOUT_DDMMYYYY),
		NextDueDate:          datetime.StartOfMonth(time.Now().AddDate(0, 2, 0)).Format(datetime.DATE_LAYOUT_DDMMYYYY),
		LastDueDate:          datetime.StartOfMonth(time.Now().AddDate(0, 6, 0)).Format(datetime.DATE_LAYOUT_DDMMYYYY),
		LoanStatus:           "DISBURSED",
		EnablePaymentGateway: true,
	}
	response := &vendorsLendenPb.GetLoanDetailsResponseWrapper{
		Message: "Success",
		Response: &vendorsLendenPb.GetLoanDetailsResponseWrapper_ResponseData{
			TraceId:     uuid.NewString(),
			MessageCode: "2001",
			Message:     "Request processed successfully",
			ResponseData: &vendorsLendenPb.GetLoanDetailsResponseWrapper_GetLoanDetailsResponse{
				LoanDetails: loanDetails,
			},
		},
	}
	return response, nil
}

func getGenerateKfsLaSimulatorResponse(ctx context.Context, decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.GenerateKfsLaRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling generate kfs la request")
	}
	userId := req.GetJson().GetUserId()
	panFromUid, err := extractPanFromUserID(userId)
	if err != nil {
		return nil, errors.Wrap(err, "error extracting pan from user id")
	}
	if resp, exists := getResponseForPan(panFromUid); exists && resp.generateKfsLaRes != nil {
		// TODO(Brijesh): Get http status from pan-response map instead of harcoding
		err = grpc.SetHeader(ctx, metadata.Pairs("x-http-code", strconv.Itoa(400)))
		if err != nil {
			return nil, errors.Wrap(err, "error setting x-http-code")
		}
		return resp.generateKfsLaRes, nil
	}
	return &vendorsLendenPb.GenerateKfsLaResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.GenerateKfsLaResponseWrapper_ResponseData{
			TraceId:     "",
			MessageCode: "",
			Message:     "From simulator",
			ResponseData: &vendorsLendenPb.GenerateKfsLaResponseWrapper_GenerateKfsLaResponse{
				Kfs:           sampleKFSDocURL,
				LoanAgreement: sampleLoanAgreementDocURL,
			},
		},
	}, nil
}

func (s *Service) getSignKfsLaSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.SignKfsLaRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling sign kfs la request")
	}
	return &vendorsLendenPb.SignKfsLaResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.SignKfsLaResponseWrapper_ResponseData{
			TraceId:     "",
			MessageCode: "",
			Message:     "From simulator",
			ResponseData: &vendorsLendenPb.SignKfsLaResponseWrapper_SignKfsLaResponse{
				Kfs:                    sampleKFSDocURL,
				LoanAgreement:          sampleLoanAgreementDocURL,
				ModifyRoiExpirationDtm: getModifyRoiTime("", s.conf.PreApprovedLoan().Lenden().ROIModificationDeadlineDelay()),
			},
		},
	}, nil
}

type ApiCodePayload struct {
	ApiCode string `json:"api_code"`
}

type IsSignedPayload struct {
	Json struct {
		IsSigned bool `json:"is_signed"`
	} `json:"json"`
}

func generateKycTrackingId(_ string, waitingDuration time.Duration) string {
	waitingTime := time.Now().Add(waitingDuration).Unix()
	finalStatus := "COMPLETED" // can change according to userId
	return strconv.FormatInt(waitingTime, 10) + trackingIdDelimiter + finalStatus
}

// nolint: dupl
func getKycStatusFromTrackingId(trackingId string) (string, error) {
	if len(trackingId) == 0 {
		return "", errors.New("empty tracking id")
	}
	splitString := strings.Split(trackingId, trackingIdDelimiter)
	if len(splitString) != 2 {
		return "", errors.New("invalid tracking id")
	}
	expiryTimeInInt, err := strconv.ParseInt(splitString[0], 10, 64)
	if err != nil {
		return "", fmt.Errorf("error while parsing expiry time: %s", splitString[0])
	}
	expiryTime := time.Unix(expiryTimeInInt, 0)
	finalStatus := splitString[1]
	if time.Now().After(expiryTime) {
		return finalStatus, nil
	} else {
		return "IN_PROGRESS", nil
	}
}

// mandate api helpers
func getMandateValidity(_ string, expiryDuration time.Duration) string {
	expiryTimeValue := time.Now().Add(expiryDuration)
	return expiryTimeValue.In(datetime.IST).Format(vgLendenPb.MandateURLExpiryTimeFormat)
}

func generateMandateTrackingId(_ string, waitingDuration time.Duration) string {
	waitingTime := time.Now().Add(waitingDuration).Unix()
	finalStatus := "COMPLETED" // can change according to userId
	return strconv.FormatInt(waitingTime, 10) + trackingIdDelimiter + finalStatus
}

// nolint: dupl
func getMandateStatusFromTrackingId(trackingId string) (string, error) {
	if len(trackingId) == 0 {
		return "", errors.New("empty tracking id")
	}
	splitString := strings.Split(trackingId, trackingIdDelimiter)
	if len(splitString) != 2 {
		return "", errors.New("invalid tracking id")
	}
	expiryTimeInInt, err := strconv.ParseInt(splitString[0], 10, 64)
	if err != nil {
		return "", fmt.Errorf("error while parsing expiry time: %s", splitString[0])
	}
	expiryTime := time.Unix(expiryTimeInInt, 0)
	finalStatus := splitString[1]
	if time.Now().After(expiryTime) {
		return finalStatus, nil
	} else {
		return "IN_PROGRESS", nil
	}
}

// esign api helpers
func getModifyRoiTime(_ string, modifyRoiDuration time.Duration) string {
	modifyRoiTimeValue := time.Now().In(datetime.IST).Add(modifyRoiDuration)
	return modifyRoiTimeValue.Format(vgLendenPb.ROIModificationDeadlineFormat)
}

func (s *Service) getInitKycSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.KycInitRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling kyc init request")
	}
	userId := req.GetJson().GetUserId()
	trackingId := generateKycTrackingId(userId, s.conf.PreApprovedLoan().Lenden().KYCCompletionDelay())
	return &vendorsLendenPb.KycInitResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.KycInitResponseWrapper_Response{
			TraceId:     "",
			MessageCode: "",
			Message:     "From simulator",
			ResponseData: &vendorsLendenPb.KycInitResponseWrapper_ResponseData{
				KycMessage: "kyc is in progress",
				KycStatus:  "IN_PROGRESS",
				KycResponse: &vendorsLendenPb.KycInitResponseWrapper_KycResponse{
					KycUrl:     "https://fi.money/",
					TrackingId: trackingId,
				},
			},
		},
	}, nil
}

// nolint: dupl
func GetKycStatusSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.CheckKycStatusRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling check kyc status request")
	}
	userId := req.GetJson().GetUserId()
	panFromUid, err := extractPanFromUserID(userId)
	if err != nil {
		return nil, err
	}
	if resp, exists := getResponseForPan(panFromUid); exists && resp.checkKYCRes != nil {
		return resp.checkKYCRes, nil
	}
	trackingId := req.GetJson().GetReferenceId()
	status, err := getKycStatusFromTrackingId(trackingId)
	if err != nil {
		return nil, err
	}
	return &vendorsLendenPb.CheckKycStatusResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.CheckKycStatusResponseWrapper_ResponseData{
			TraceId:     "",
			MessageCode: "",
			Message:     "From simulator",
			ResponseData: &vendorsLendenPb.CheckKycStatusResponseWrapper_CheckKycStatusResponse{
				KycStatus: status,
			},
		},
	}, nil
}

// nolint: dupl
func GetMandateStatusSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.CheckMandateStatusRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling check mandate status request")
	}
	userId := req.GetJson().GetUserId()
	panFromUid, err := extractPanFromUserID(userId)
	if err != nil {
		return nil, err
	}
	if resp, exists := getResponseForPan(panFromUid); exists && resp.mandateStatusRes != nil {
		return resp.mandateStatusRes, nil
	}
	trackingId := req.GetJson().GetTrackingId()
	mandateStatus, trackingIdErr := getMandateStatusFromTrackingId(trackingId)
	if trackingIdErr != nil {
		return nil, trackingIdErr
	}
	return &vendorsLendenPb.CheckMandateStatusResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.CheckMandateStatusResponseWrapper_ResponseData{
			TraceId:     "",
			MessageCode: "",
			Message:     "From simulator",
			ResponseData: &vendorsLendenPb.CheckMandateStatusResponseWrapper_CheckMandateStatusResponse{
				MandateStatus: mandateStatus,
			},
		},
	}, nil
}

func GetSelectOfferSimulatorResp(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.SelectOfferRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling select offer request")
	}
	return &vendorsLendenPb.SelectOfferResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.SelectOfferResponseWrapper_ResponseData{
			TraceId:     "",
			MessageCode: "",
			Message:     "",
			ResponseData: &vendorsLendenPb.SelectOfferResponseWrapper_SelectOfferResponse{
				SelectedOffer: req.GetJson().GetSelectedOffer(),
				Amount:        float64(req.GetJson().GetSelectedAmount()),
				IsDuplicate:   false,
				OfferCode:     req.GetJson().GetSelectedOffer(),
				Tenure:        req.GetJson().GetTenure(),
			},
		},
	}, nil
}

const (
	sampleKFSDocURL           = "https://epifi-icons.pointz.in/preapprovedloan/lenden/sample-kfs.pdf"
	sampleLoanAgreementDocURL = "https://epifi-icons.pointz.in/preapprovedloan/lenden/sample-loan-agreement.pdf"
)

func (s *Service) getInitMandateSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.InitMandateRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling init mandate request")
	}
	userId := req.GetJson().GetUserId()
	panFromUid, err := extractPanFromUserID(userId)
	if err != nil {
		return nil, errors.Wrapf(err, "error extracting pan from user id: %s", userId)
	}
	trackingId := generateMandateTrackingId(userId, s.conf.PreApprovedLoan().Lenden().MandateCompletionDelay())
	if res, exists := getResponseForPan(panFromUid); exists && res.initMandateRes != nil {
		res.initMandateRes.GetResponse().GetResponseData().TrackingId = trackingId
		return res.initMandateRes, nil
	}
	return &vendorsLendenPb.InitMandateResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.InitMandateResponseWrapper_ResponseData{
			TraceId:     "",
			MessageCode: "",
			Message:     "From simulator",
			ResponseData: &vendorsLendenPb.InitMandateResponseWrapper_InitMandateResponse{
				Links: &vendorsLendenPb.InitMandateResponseWrapper_Links{
					Url:             "https://fi.money/",
					UrlValidity:     getMandateValidity(userId, 1*time.Hour),
					MandateValidity: getMandateValidity(userId, 1*time.Hour),
				},
				TrackingId: trackingId,
			},
		},
	}, nil
}

func (s *Service) GetHardOfferEligibilitySimulatorResp(ctx context.Context, decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.CheckHardEligibilityRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, err
	}
	userId := req.GetJson().GetUserId()
	panFromUid, err := extractPanFromUserID(userId)
	if err != nil {
		return nil, err
	}
	if resp, exists := getResponseForPan(panFromUid); exists && resp.hardEligRes != nil {
		return resp.hardEligRes, nil
	}
	resp, err := s.vendorResponseDao.GetById(ctx, req.GetJson().GetUserId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_INSTANT_LOAN_OFFER, commonvgpb.Vendor_LENDEN)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			simulator.SetHttpCode(ctx, 404)
			return &vendorsLendenPb.CheckHardEligibilityResponseWrapper{
				Message:  "",
				Response: &vendorsLendenPb.CheckHardEligibilityResponseWrapper_ResponseData{},
			}, nil
		}
		return nil, errors.Wrap(err, "unable to get entry in vendorResponse DB in simulator")
	}
	requestedLoanAmount := resp.GetResponseData().GetVendorSimulatedData().GetLoanAmount()
	if requestedLoanAmount < 10000 {
		return &vendorsLendenPb.CheckHardEligibilityResponseWrapper{
			Message: "",
			Response: &vendorsLendenPb.CheckHardEligibilityResponseWrapper_ResponseData{
				TraceId: "34b36322f8b43a799650505fa545ea0d",
				// 4001 / 4005 / 4010 / 4012 / 4048 / 4061 / 4080 / 4081 / 2001 / 5002
				MessageCode: "4001",
				Message:     "",
				ResponseData: &vendorsLendenPb.CheckHardEligibilityResponseWrapper_CheckHardEligibilityResponse{
					Action:    "REJECTED",
					OfferData: &vendorsLendenPb.OfferData{},
				},
			},
		}, nil
	} else if requestedLoanAmount > 10000 && requestedLoanAmount < 20000 && !resp.GetResponseData().GetVendorSimulatedData().GetIsRequiredDataPresent() {
		return &vendorsLendenPb.CheckHardEligibilityResponseWrapper{
			Message: "",
			Response: &vendorsLendenPb.CheckHardEligibilityResponseWrapper_ResponseData{
				TraceId: "34b36322f8b43a799650505fa545ea0d",
				// 4001 / 4005 / 4010 / 4012 / 4048 / 4061 / 4080 / 4081 / 2001 / 5002
				MessageCode: "4001",
				Message:     "",
				ResponseData: &vendorsLendenPb.CheckHardEligibilityResponseWrapper_CheckHardEligibilityResponse{
					Action:    "FAILED",
					OfferData: &vendorsLendenPb.OfferData{},
				},
			},
		}, nil
	}
	amountIncrementStepSize := 500
	applicableTenureList := map[string]*structPb.ListValue{
		// multiple amount and tenure choices
		"1000-49500": {
			Values: []*structPb.Value{
				{Kind: &structPb.Value_NumberValue{NumberValue: 2}},
				{Kind: &structPb.Value_NumberValue{NumberValue: 3}},
				{Kind: &structPb.Value_NumberValue{NumberValue: 4}},
			},
		},
		// multiple amount choices but single tenure choice
		"50000-99000": {
			Values: []*structPb.Value{
				{Kind: &structPb.Value_NumberValue{NumberValue: 5}},
			},
		},
		// single amount and multiple tenure choices
		"99500": {
			Values: []*structPb.Value{
				{Kind: &structPb.Value_NumberValue{NumberValue: 5}},
				{Kind: &structPb.Value_NumberValue{NumberValue: 6}},
			},
		},
		// single amount and single tenure choice
		"100000": {
			Values: []*structPb.Value{
				{Kind: &structPb.Value_NumberValue{NumberValue: 6}},
			},
		},
	}
	return &vendorsLendenPb.CheckHardEligibilityResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.CheckHardEligibilityResponseWrapper_ResponseData{
			TraceId: "34b36322f8b43a799650505fa545ea0d",
			// 4001 / 4005 / 4010 / 4012 / 4048 / 4061 / 4080 / 4081 / 2001 / 5002
			MessageCode: "4001",
			Message:     "",
			ResponseData: &vendorsLendenPb.CheckHardEligibilityResponseWrapper_CheckHardEligibilityResponse{
				Action: "APPROVED",
				OfferData: &vendorsLendenPb.OfferData{
					Offers: []*vendorsLendenPb.Offer{
						{
							OfferCode:                "A",
							Roi:                      4,
							FundingProbability:       "HIGH",
							ExpectedTimeToGetFunding: "Minimum 2 hours",
							IsRecommended:            true,
							ModifyRoiList:            []float64{2, 3, 4, 5, 6, 7, 8},
						},
						{
							OfferCode:                "B",
							Roi:                      3.98,
							FundingProbability:       "MEDIUM",
							ExpectedTimeToGetFunding: "Minimum 2 days",
							IsRecommended:            false,
							ModifyRoiList:            []float64{2, 3, 4, 5, 6, 7, 8},
						},
						{
							OfferCode:                "C",
							Roi:                      3.96,
							FundingProbability:       "LOW",
							ExpectedTimeToGetFunding: "Minimum 5 days",
							IsRecommended:            false,
							ModifyRoiList:            []float64{2, 3, 4, 5, 6, 7, 8},
						},
					},
					InterestType:           "FLAT",
					TenureType:             "MONTHLY",
					InterestRateFrequency:  "MONTHLY",
					OfferSelectionMultiple: int32(amountIncrementStepSize),
					MinAmount:              1000,
					MaxAmount:              100000,
					ApplicableTenures:      applicableTenureList,
				},
			},
		},
	}, nil
}

func GetCreateUserSimulatorResp(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.CreateUserRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, err
	}
	pan := req.GetJson().GetPanNumber()
	if pan == "" {
		return nil, errors.New("pan number cannot be empty")
	}
	userId := pan + "_" + uuid.NewString()

	return &vendorsLendenPb.CreateUserResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.CreateUserResponseWrapper_ResponseData{
			TraceId:     "",
			MessageCode: "",
			Message:     "",
			ResponseData: &vendorsLendenPb.CreateUserResponse{
				UserId:      userId,
				IsDuplicate: false,
				CreatedDtm:  "",
			},
		},
	}, nil
}

func (s *Service) GetCreateLoanSimulatorResp(ctx context.Context, decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.ApplyLoanRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, err
	}
	_, err = s.vendorResponseDao.Create(ctx, &preapprovedloanPb.VendorResponse{
		Id:                 req.GetJson().GetUserId(),
		ApplicationNumber:  req.GetJson().GetUserId(),
		Vendor:             commonvgpb.Vendor_LENDEN,
		VendorResponseType: preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_INSTANT_LOAN_OFFER,
		ResponseData: &preapprovedloanPb.VendorResponseData{
			Data: &preapprovedloanPb.VendorResponseData_VendorSimulatedData{
				VendorSimulatedData: &preapprovedloanPb.GenericSimulatorData{
					LoanAmount: req.GetJson().GetRequestedAmount(),
					// doing for storing AA data
					IsRequiredDataPresent: false,
				},
			},
		},
	})
	if err != nil && !errors.Is(err, epifierrors.ErrDuplicateEntry) {
		return nil, errors.Wrap(err, "unable to create entry in vendorResponse DB in simulator")
	}
	return &vendorsLendenPb.ApplyLoanResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.ApplyLoanResponseWrapper_ResponseData{
			TraceId:     "23456789789",
			MessageCode: "2001",
			Message:     "",
			ResponseData: &vendorsLendenPb.ApplyLoanResponseWrapper_ApplyLoanResponse{
				LoanId:      uuid.NewString(),
				CreatedDtm:  "",
				IsDuplicate: false,
			},
		},
	}, nil
}

func getPreDisbursementDetailsRes(reqPayload []byte) (*vendorsLendenPb.GetPreDisbursementDetailsResponseWrapper, error) {
	preDisbursementDetailsReq := &vendorsLendenPb.GetPreDisbursementDetailsRequest{}
	err := protojson.Unmarshal(reqPayload, preDisbursementDetailsReq)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling pre disbursement details request")
	}
	preDisbursementDetails := calculatePreDisbursementDetails(preDisbursementDetailsReq.GetJson().GetAmount(), int(preDisbursementDetailsReq.GetJson().GetTenure()), preDisbursementDetailsReq.GetJson().GetInterestRate())
	return &vendorsLendenPb.GetPreDisbursementDetailsResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.GetPreDisbursementDetailsResponse{
			MessageCode:  "2001",
			Message:      "Request processed successfully",
			ResponseData: preDisbursementDetails,
		},
	}, nil
}

func calculatePreDisbursementDetails(amount float64, tenureInMonths int, monthlyInterestRatePercentage float64) *vendorsLendenPb.GetPreDisbursementDetailsResponseData {
	// Note: Some calculations below may just be approximate. Should work for simulation purposes. Thanks to ChatGPT!
	calculationDate := time.Now()
	processingFee := amount * 0.05
	monthlyInterestRate := monthlyInterestRatePercentage / 100
	totalInterest := amount * monthlyInterestRate * float64(tenureInMonths)
	totalRepaymentAmount := amount + totalInterest

	// EMI Calculation (Reducing Balance Interest Formula)
	emi := (amount * monthlyInterestRate * math.Pow(1+monthlyInterestRate, float64(tenureInMonths))) /
		(math.Pow(1+monthlyInterestRate, float64(tenureInMonths)) - 1)

	// Determine the first installment date (assuming next month's 1st)
	firstInstallmentDate := time.Date(calculationDate.Year(), calculationDate.Month()+1, 1, 0, 0, 0, 0, time.UTC).Format("02-01-2006")

	// APR Approximation
	apr := (totalInterest / (amount * float64(tenureInMonths) / 12)) * 100

	// Gap Interest Calculation (if calculation date > 20th)
	gapInterest := 0.0
	if calculationDate.Day() > 20 {
		daysLeft := 1 + int(time.Date(calculationDate.Year(), calculationDate.Month()+1, 1, 0, 0, 0, 0, time.UTC).Sub(calculationDate).Hours()/24)
		gapInterest = (amount * (monthlyInterestRate * 12 / 100) / 365) * float64(daysLeft)
	}
	return &vendorsLendenPb.GetPreDisbursementDetailsResponseData{
		ProcessingFee:        processingFee,
		DisbursalAmount:      amount - processingFee,
		TotalInterest:        totalInterest,
		TotalRepaymentAmount: totalRepaymentAmount,
		InstallmentAmount:    emi,
		FirstInstallmentDate: firstInstallmentDate,
		Apr:                  apr,
		GapInterest:          gapInterest,
	}
}
func (s *Service) GetPostExternalDataSimulatorResp(ctx context.Context, decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.PostExternalDataRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, err
	}

	if len(req.GetJson().GetData().GetFields()) == 0 {
		return &vendorsLendenPb.PostExternalDataResponseWrapper{
			Response: &vendorsLendenPb.PostExternalDataResponseWrapper_ResponseData{
				ResponseData: &vendorsLendenPb.PostExternalDataResponseWrapper_PostExternalDataResponse{
					Status: "failed due to data is empty",
				},
			},
		}, nil
	}

	resData, err := s.vendorResponseDao.GetById(ctx, req.GetJson().GetUserId(), preapprovedloanPb.VendorResponseType_VENDOR_RESPONSE_TYPE_INSTANT_LOAN_OFFER, commonvgpb.Vendor_LENDEN)
	if err != nil {
		return &vendorsLendenPb.PostExternalDataResponseWrapper{
			Response: &vendorsLendenPb.PostExternalDataResponseWrapper_ResponseData{
				ResponseData: &vendorsLendenPb.PostExternalDataResponseWrapper_PostExternalDataResponse{
					Status: "failed",
				},
			},
		}, nil
	}
	// marking that AA data has been collected
	resData.GetResponseData().GetVendorSimulatedData().IsRequiredDataPresent = true

	if err = s.vendorResponseDao.Update(ctx, resData); err != nil {
		return &vendorsLendenPb.PostExternalDataResponseWrapper{
			Response: &vendorsLendenPb.PostExternalDataResponseWrapper_ResponseData{
				ResponseData: &vendorsLendenPb.PostExternalDataResponseWrapper_PostExternalDataResponse{
					Status: "failed",
				},
			},
		}, nil
	}

	return &vendorsLendenPb.PostExternalDataResponseWrapper{
		Response: &vendorsLendenPb.PostExternalDataResponseWrapper_ResponseData{
			ResponseData: &vendorsLendenPb.PostExternalDataResponseWrapper_PostExternalDataResponse{
				Status: "success",
			},
		},
	}, nil
}

func ModifyRoiListSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.ModifyRoiRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling modify roi details request")
	}

	// Default response if no mapping found.
	return &vendorsLendenPb.ModifyRoiResponseWrapper{
		Message: "",
		Response: &vendorsLendenPb.ModifyRoiResponseWrapper_ResponseData{
			TraceId:     uuid.NewString(),
			MessageCode: "2001",
			Message:     "From simulator",
			ResponseData: &vendorsLendenPb.ModifyRoiResponseWrapper_ModifyRoiResponse{
				InstallmentAmount: 15000,
				Documents: &vendorsLendenPb.Documents{
					Kfs:           sampleKFSDocURL,
					LoanAgreement: sampleLoanAgreementDocURL,
				},
			},
		},
	}, nil
}

func getAmortizationScheduleSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.AmortizationScheduleRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling amortization schedule request")
	}
	var schedule []*vendorsLendenPb.AmortizationScheduleItem
	for i := 0; i < loanTenureInMonths; i++ {
		item := &vendorsLendenPb.AmortizationScheduleItem{
			DueDate:   datetime.StartOfMonth(time.Now().AddDate(0, i+1, 0)).Format(datetime.DATE_LAYOUT_DDMMYYYY),
			DueAmount: 2377.92,
			Details: []*vendorsLendenPb.AmortizationAmountBreakupComponent{
				{Purpose: "PRINCIPAL", Amount: 2166.67},
				{Purpose: "INTEREST", Amount: 211.25},
			},
			Status:            "UPCOMING",
			OutstandingAmount: 2377.92,
		}
		schedule = append(schedule, item)
	}
	response := &vendorsLendenPb.AmortizationScheduleResponseWrapper{
		Message: "Request processed successfully",
		Response: &vendorsLendenPb.AmortizationScheduleResponse{
			TraceId: uuid.NewString(),
			// Allowed codes: LMS_4014 / LMS_4012 / LMS_4015 / LMS_4038 / LMS_4013 / 4101 / 4010 / 4102 / 4001 / 4010 / 4005  / 5002 / 4105 / 4103 / 4106 / 2027 / 2001
			MessageCode: "2001",
			Message:     "Request processed successfully",
			ResponseData: &vendorsLendenPb.AmortizationScheduleResponseData{
				AmortizationSchedule: schedule,
			},
		},
	}
	return response, nil
}

// getForeclosureDetailsSimulatorResponse returns a simulated foreclosure details response.
func getForeclosureDetailsSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.GetForeclosureDetailsRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling foreclosure details request")
	}
	details := &vendorsLendenPb.ForeclosureDetails{
		PrincipalOutstanding: 50_000,
		ForeclosureAmount:    50_000,
	}
	// TODO(Brijesh): Make a stateful payment simulator to simulate different cool-off and foreclosure states.
	response := &vendorsLendenPb.GetForeclosureDetailsResponseWrapper{
		Message: "Request processed successfully",
		Response: &vendorsLendenPb.GetForeclosureDetailsResponse{
			TraceId:      uuid.NewString(),
			MessageCode:  "2001",
			Message:      "Request processed successfully",
			ResponseData: details,
		},
	}
	return response, nil
}

func generatePaymentOrderIdForTracking(loanId string, waitingDuration time.Duration) string {
	waitingTime := time.Now().Add(waitingDuration).Unix()
	return loanId + trackingIdDelimiter +
		strconv.FormatInt(waitingTime, 10) + trackingIdDelimiter +
		paymentSuccessStatusValue
}

func getGeneratePaymentLinkSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.GeneratePaymentLinkRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling generate payment link request")
	}
	orderId := generatePaymentOrderIdForTracking(req.GetJson().GetLoanId(), 90*time.Second)
	return &vendorsLendenPb.GeneratePaymentLinkResponseWrapper{
		Message: "Request processed successfully",
		Response: &vendorsLendenPb.GeneratePaymentLinkResponse{
			TraceId:     uuid.NewString(),
			MessageCode: "2001",
			Message:     "Request processed successfully",
			ResponseData: &vendorsLendenPb.GeneratePaymentLinkResponseData{
				Url:     paymentLink,
				OrderId: orderId,
			},
		},
	}, nil
}

func getPaymentStatusSimulatorResponse(decryptedPayload []byte) (proto.Message, error) {
	req := &vendorsLendenPb.GetPaymentStatusRequest{}
	err := protojson.Unmarshal(decryptedPayload, req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling get payment status request")
	}
	orderId := req.GetJson().GetOrderId()
	if len(orderId) == 0 {
		return nil, errors.New("empty order id")
	}
	orderIdParts := strings.Split(orderId, trackingIdDelimiter)
	if len(orderIdParts) != 3 {
		return nil, errors.Errorf("unexpected order id format: %s", orderId)
	}
	expiryTimeInInt, err := strconv.ParseInt(orderIdParts[1], 10, 64)
	if err != nil {
		return nil, errors.Errorf("error while parsing expiry time: %s", orderIdParts[1])
	}
	expiryTime := time.Unix(expiryTimeInInt, 0)
	status := paymentPendingStatusValue
	if time.Now().Equal(expiryTime) || time.Now().After(expiryTime) {
		status = orderIdParts[2]
	}
	return &vendorsLendenPb.GetPaymentStatusResponseWrapper{
		Message: "Request processed successfully",
		Response: &vendorsLendenPb.GetPaymentStatusResponse{
			TraceId:     uuid.NewString(),
			MessageCode: "2001",
			Message:     "Request processed successfully",
			ResponseData: &vendorsLendenPb.GetPaymentStatusResponseData{
				OrderId: orderId,
				Status:  status,
			},
		},
	}, nil
}
