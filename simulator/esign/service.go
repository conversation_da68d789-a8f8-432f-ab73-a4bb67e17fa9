//nolint:dupl,funlen
package esign

import (
	"context"
	"time"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/datetime"
	esignPb "github.com/epifi/gamma/api/simulator/esign"
	"github.com/epifi/gamma/api/vendors/leegality"
)

type Service struct {
	esignPb.UnimplementedESignServer
}

func NewService() *Service {
	return &Service{}
}

const (
	docIdDateTimeLayout = "20060102T150405Z"
	signUrl             = "https://theuselessweb.com/"
)

func (s *Service) CreateESign(ctx context.Context, req *leegality.CreateESigningRequest) (*leegality.CreateESigningResponse, error) {
	// profile id for savings account
	if req.GetProfileId() == "oyLVQSr" {
		return &leegality.CreateESigningResponse{
			Status: 1,
			Messages: []*leegality.Messages{
				{},
			},
			Data: &leegality.CreateESigningResponse_Data{
				DocumentId: "1",
				Irn:        req.<PERSON>(),
				Invitees: []*leegality.CreateESigningResponse_Data_Invitees{
					{
						Name:       "test name",
						Email:      "<EMAIL>",
						Phone:      "**********",
						SignUrl:    "https://cgi-lib.berkeley.edu/ex/fup.html",
						Active:     true,
						ExpiryDate: time.Now().AddDate(0, 1, 0).Format("2006-01-02T15:04:05Z"),
					},
				},
			},
		}, nil
	}
	return &leegality.CreateESigningResponse{
		Status: 1,
		Messages: []*leegality.Messages{
			{},
		},
		Data: &leegality.CreateESigningResponse_Data{
			DocumentId: time.Now().In(datetime.IST).Format(docIdDateTimeLayout),
			Irn:        req.GetIrn(),
			Invitees: []*leegality.CreateESigningResponse_Data_Invitees{
				{
					Name:  "Abcd",
					Email: "<EMAIL>",
				},
				{
					Name:       "test name",
					Email:      "<EMAIL>",
					Phone:      "**********",
					SignUrl:    signUrl,
					Active:     true,
					ExpiryDate: time.Now().AddDate(0, 1, 0).Format("2006-01-02T15:04:05Z"),
				},
			},
		},
	}, nil
}

func (s *Service) CheckESignStatus(_ context.Context, req *esignPb.CheckESignStatusRequest) (*leegality.CheckESigningStatusResponse, error) {
	var isSigned bool
	if req.GetDocumentId() == "" {
		isSigned = true // fallback
	}
	docId := req.GetDocumentId()
	timeInit, err := time.ParseInLocation(docIdDateTimeLayout, docId, datetime.IST)
	if err != nil {
		return nil, errors.Wrap(err, "parse time error")
	}
	if timeInit.Before(time.Now().In(datetime.IST).Add(-3 * time.Minute)) {
		isSigned = true
	}
	return &leegality.CheckESigningStatusResponse{
		Status: 1,
		Messages: []*leegality.Messages{
			{},
		},
		Data: &leegality.CheckESigningStatusResponse_Data{
			DocumentId: docId,
			Irn:        "",
			FolderId:   "",
			AuditTrail: "",
			Requests: []*leegality.CheckESigningStatusResponse_Data_Request{
				{
					Name:       "test name",
					Email:      "<EMAIL>",
					Phone:      "**********",
					SignUrl:    signUrl,
					ExpiryDate: time.Now().AddDate(0, 1, 0).Format("02-01-2006 15:04:05"),
					SignType:   "",
					Signed:     isSigned,
					Active:     true,
					Rejected:   false,
					Expired:    false,
				},
			},
			Files:   nil,
			Signers: []*leegality.CheckESigningStatusResponse_Data_Signer{},
		},
	}, nil
}
