Go memory stats:
{
  "Alloc": 353597520,
  "TotalAlloc": 479461432,
  "Sys": 389079384,
  "Lookups": 0,
  "Mallocs": 1563932,
  "Frees": 339547,
  "HeapAlloc": 353597520,
  "HeapSys": 368893952,
  "HeapIdle": 4104192,
  "HeapInuse": 364789760,
  "HeapReleased": 3563520,
  "HeapObjects": 1224385,
  "StackInuse": 8486912,
  "StackSys": 8486912,
  "MSpanInuse": 3311200,
  "MSpanSys": 3312960,
  "MCacheInuse": 14400,
  "MCacheSys": 15600,
  "BuckHashSys": 1622925,
  "GCSys": 5021672,
  "OtherSys": 1725363,
  "NextGC": 459586160,
  "LastGC": 1753281059237519000,
  "PauseTotalNs": 686543,
  "PauseNs": [
    11416,
    37251,
    186709,
    77000,
    122959,
    47125,
    204083,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "PauseEnd": [
    1753281059141233000,
    1753281059148681000,
    1753281059173601000,
    1753281059193782000,
    1753281059209840000,
    1753281059225392000,
    1753281059237519000,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "NumGC": 7,
  "NumForcedGC": 0,
  "GCCPUFraction": 0.042029960298247314,
  "EnableGC": true,
  "DebugGC": false,
  "BySize": [
    {
      "Size": 0,
      "Mallocs": 0,
      "Frees": 0
    },
    {
      "Size": 8,
      "Mallocs": 25796,
      "Frees": 691
    },
    {
      "Size": 16,
      "Mallocs": 381691,
      "Frees": 65098
    },
    {
      "Size": 24,
      "Mallocs": 109555,
      "Frees": 10652
    },
    {
      "Size": 32,
      "Mallocs": 110094,
      "Frees": 16418
    },
    {
      "Size": 48,
      "Mallocs": 152823,
      "Frees": 27424
    },
    {
      "Size": 64,
      "Mallocs": 176089,
      "Frees": 11882
    },
    {
      "Size": 80,
      "Mallocs": 44064,
      "Frees": 4549
    },
    {
      "Size": 96,
      "Mallocs": 47098,
      "Frees": 2494
    },
    {
      "Size": 112,
      "Mallocs": 44446,
      "Frees": 12356
    },
    {
      "Size": 128,
      "Mallocs": 29141,
      "Frees": 6255
    },
    {
      "Size": 144,
      "Mallocs": 17869,
      "Frees": 354
    },
    {
      "Size": 160,
      "Mallocs": 29064,
      "Frees": 1921
    },
    {
      "Size": 176,
      "Mallocs": 15035,
      "Frees": 571
    },
    {
      "Size": 192,
      "Mallocs": 27894,
      "Frees": 626
    },
    {
      "Size": 208,
      "Mallocs": 7790,
      "Frees": 278
    },
    {
      "Size": 224,
      "Mallocs": 12068,
      "Frees": 418
    },
    {
      "Size": 240,
      "Mallocs": 21407,
      "Frees": 75
    },
    {
      "Size": 256,
      "Mallocs": 14630,
      "Frees": 721
    },
    {
      "Size": 288,
      "Mallocs": 16554,
      "Frees": 197
    },
    {
      "Size": 320,
      "Mallocs": 11855,
      "Frees": 1488
    },
    {
      "Size": 352,
      "Mallocs": 8944,
      "Frees": 183
    },
    {
      "Size": 384,
      "Mallocs": 8081,
      "Frees": 51
    },
    {
      "Size": 416,
      "Mallocs": 5452,
      "Frees": 129
    },
    {
      "Size": 448,
      "Mallocs": 7443,
      "Frees": 121
    },
    {
      "Size": 480,
      "Mallocs": 7601,
      "Frees": 71
    },
    {
      "Size": 512,
      "Mallocs": 2844,
      "Frees": 77
    },
    {
      "Size": 576,
      "Mallocs": 3448,
      "Frees": 90
    },
    {
      "Size": 640,
      "Mallocs": 2488,
      "Frees": 56
    },
    {
      "Size": 704,
      "Mallocs": 7912,
      "Frees": 1263
    },
    {
      "Size": 768,
      "Mallocs": 4122,
      "Frees": 14
    },
    {
      "Size": 896,
      "Mallocs": 6894,
      "Frees": 189
    },
    {
      "Size": 1024,
      "Mallocs": 8479,
      "Frees": 129
    },
    {
      "Size": 1152,
      "Mallocs": 2615,
      "Frees": 68
    },
    {
      "Size": 1280,
      "Mallocs": 781,
      "Frees": 105
    },
    {
      "Size": 1408,
      "Mallocs": 7763,
      "Frees": 1138
    },
    {
      "Size": 1536,
      "Mallocs": 3138,
      "Frees": 12
    },
    {
      "Size": 1792,
      "Mallocs": 2586,
      "Frees": 99
    },
    {
      "Size": 2048,
      "Mallocs": 635,
      "Frees": 56
    },
    {
      "Size": 2304,
      "Mallocs": 670,
      "Frees": 41
    },
    {
      "Size": 2688,
      "Mallocs": 521,
      "Frees": 19
    },
    {
      "Size": 3072,
      "Mallocs": 1702,
      "Frees": 58
    },
    {
      "Size": 3200,
      "Mallocs": 34,
      "Frees": 1
    },
    {
      "Size": 3456,
      "Mallocs": 102,
      "Frees": 20
    },
    {
      "Size": 4096,
      "Mallocs": 1022,
      "Frees": 51
    },
    {
      "Size": 4864,
      "Mallocs": 307,
      "Frees": 24
    },
    {
      "Size": 5376,
      "Mallocs": 482,
      "Frees": 31
    },
    {
      "Size": 6144,
      "Mallocs": 295,
      "Frees": 22
    },
    {
      "Size": 6528,
      "Mallocs": 13,
      "Frees": 2
    },
    {
      "Size": 6784,
      "Mallocs": 10,
      "Frees": 2
    },
    {
      "Size": 6912,
      "Mallocs": 11,
      "Frees": 1
    },
    {
      "Size": 8192,
      "Mallocs": 550,
      "Frees": 20
    },
    {
      "Size": 9472,
      "Mallocs": 60,
      "Frees": 6
    },
    {
      "Size": 9728,
      "Mallocs": 21,
      "Frees": 3
    },
    {
      "Size": 10240,
      "Mallocs": 15,
      "Frees": 2
    },
    {
      "Size": 10880,
      "Mallocs": 14,
      "Frees": 3
    },
    {
      "Size": 12288,
      "Mallocs": 52,
      "Frees": 10
    },
    {
      "Size": 13568,
      "Mallocs": 312,
      "Frees": 5
    },
    {
      "Size": 14336,
      "Mallocs": 19,
      "Frees": 3
    },
    {
      "Size": 16384,
      "Mallocs": 88,
      "Frees": 4
    },
    {
      "Size": 18432,
      "Mallocs": 13,
      "Frees": 3
    }
  ]
}
----
Non-Go stats:
{
  "CGoAllocatedBytes": 8488016,
  "CGoTotalBytes": 16498688
}
