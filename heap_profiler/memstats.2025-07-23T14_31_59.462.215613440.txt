Go memory stats:
{
  "Alloc": 181691136,
  "TotalAlloc": 645225344,
  "Sys": 496499064,
  "Lookups": 0,
  "Mallocs": 2882784,
  "Frees": 2182090,
  "HeapAlloc": 181691136,
  "HeapSys": 474382336,
  "HeapIdle": 250904576,
  "HeapInuse": 223477760,
  "HeapReleased": 1294336,
  "HeapObjects": 700694,
  "StackInuse": 7831552,
  "StackSys": 7831552,
  "MSpanInuse": 2056640,
  "MSpanSys": 4292160,
  "MCacheInuse": 14400,
  "MCacheSys": 15600,
  "BuckHashSys": 1670405,
  "GCSys": 6003680,
  "OtherSys": 2303331,
  "NextGC": 489713840,
  "LastGC": 1753281099513420000,
  "PauseTotalNs": 1987918,
  "PauseNs": [
    11416,
    37251,
    186709,
    77000,
    122959,
    47125,
    204083,
    1301375,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "PauseEnd": [
    1753281059141233000,
    1753281059148681000,
    1753281059173601000,
    1753281059193782000,
    1753281059209840000,
    1753281059225392000,
    1753281059237519000,
    1753281099513420000,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "NumGC": 8,
  "NumForcedGC": 0,
  "GCCPUFraction": 0.0005263865809555297,
  "EnableGC": true,
  "DebugGC": false,
  "BySize": [
    {
      "Size": 0,
      "Mallocs": 0,
      "Frees": 0
    },
    {
      "Size": 8,
      "Mallocs": 45391,
      "Frees": 31417
    },
    {
      "Size": 16,
      "Mallocs": 780940,
      "Frees": 525881
    },
    {
      "Size": 24,
      "Mallocs": 134867,
      "Frees": 110219
    },
    {
      "Size": 32,
      "Mallocs": 151901,
      "Frees": 118985
    },
    {
      "Size": 48,
      "Mallocs": 253312,
      "Frees": 172541
    },
    {
      "Size": 64,
      "Mallocs": 427549,
      "Frees": 311409
    },
    {
      "Size": 80,
      "Mallocs": 68444,
      "Frees": 47001
    },
    {
      "Size": 96,
      "Mallocs": 97810,
      "Frees": 58116
    },
    {
      "Size": 112,
      "Mallocs": 71250,
      "Frees": 54881
    },
    {
      "Size": 128,
      "Mallocs": 38755,
      "Frees": 30042
    },
    {
      "Size": 144,
      "Mallocs": 21293,
      "Frees": 17322
    },
    {
      "Size": 160,
      "Mallocs": 38663,
      "Frees": 28934
    },
    {
      "Size": 176,
      "Mallocs": 16746,
      "Frees": 14024
    },
    {
      "Size": 192,
      "Mallocs": 31963,
      "Frees": 22800
    },
    {
      "Size": 208,
      "Mallocs": 11172,
      "Frees": 7522
    },
    {
      "Size": 224,
      "Mallocs": 20712,
      "Frees": 15692
    },
    {
      "Size": 240,
      "Mallocs": 55363,
      "Frees": 34711
    },
    {
      "Size": 256,
      "Mallocs": 19868,
      "Frees": 13785
    },
    {
      "Size": 288,
      "Mallocs": 21703,
      "Frees": 17787
    },
    {
      "Size": 320,
      "Mallocs": 13989,
      "Frees": 12344
    },
    {
      "Size": 352,
      "Mallocs": 10230,
      "Frees": 8813
    },
    {
      "Size": 384,
      "Mallocs": 9240,
      "Frees": 7983
    },
    {
      "Size": 416,
      "Mallocs": 5933,
      "Frees": 5224
    },
    {
      "Size": 448,
      "Mallocs": 7716,
      "Frees": 4656
    },
    {
      "Size": 480,
      "Mallocs": 12693,
      "Frees": 9356
    },
    {
      "Size": 512,
      "Mallocs": 3191,
      "Frees": 2699
    },
    {
      "Size": 576,
      "Mallocs": 4957,
      "Frees": 3993
    },
    {
      "Size": 640,
      "Mallocs": 3345,
      "Frees": 3031
    },
    {
      "Size": 704,
      "Mallocs": 10989,
      "Frees": 8988
    },
    {
      "Size": 768,
      "Mallocs": 5153,
      "Frees": 4093
    },
    {
      "Size": 896,
      "Mallocs": 7925,
      "Frees": 6582
    },
    {
      "Size": 1024,
      "Mallocs": 8983,
      "Frees": 7941
    },
    {
      "Size": 1152,
      "Mallocs": 3283,
      "Frees": 2565
    },
    {
      "Size": 1280,
      "Mallocs": 864,
      "Frees": 714
    },
    {
      "Size": 1408,
      "Mallocs": 9797,
      "Frees": 7423
    },
    {
      "Size": 1536,
      "Mallocs": 3542,
      "Frees": 3295
    },
    {
      "Size": 1792,
      "Mallocs": 2860,
      "Frees": 2184
    },
    {
      "Size": 2048,
      "Mallocs": 754,
      "Frees": 566
    },
    {
      "Size": 2304,
      "Mallocs": 878,
      "Frees": 547
    },
    {
      "Size": 2688,
      "Mallocs": 652,
      "Frees": 488
    },
    {
      "Size": 3072,
      "Mallocs": 1755,
      "Frees": 966
    },
    {
      "Size": 3200,
      "Mallocs": 35,
      "Frees": 29
    },
    {
      "Size": 3456,
      "Mallocs": 143,
      "Frees": 123
    },
    {
      "Size": 4096,
      "Mallocs": 1157,
      "Frees": 835
    },
    {
      "Size": 4864,
      "Mallocs": 615,
      "Frees": 449
    },
    {
      "Size": 5376,
      "Mallocs": 646,
      "Frees": 484
    },
    {
      "Size": 6144,
      "Mallocs": 322,
      "Frees": 232
    },
    {
      "Size": 6528,
      "Mallocs": 15,
      "Frees": 6
    },
    {
      "Size": 6784,
      "Mallocs": 14,
      "Frees": 14
    },
    {
      "Size": 6912,
      "Mallocs": 15,
      "Frees": 10
    },
    {
      "Size": 8192,
      "Mallocs": 692,
      "Frees": 541
    },
    {
      "Size": 9472,
      "Mallocs": 79,
      "Frees": 45
    },
    {
      "Size": 9728,
      "Mallocs": 30,
      "Frees": 22
    },
    {
      "Size": 10240,
      "Mallocs": 18,
      "Frees": 8
    },
    {
      "Size": 10880,
      "Mallocs": 15,
      "Frees": 7
    },
    {
      "Size": 12288,
      "Mallocs": 57,
      "Frees": 39
    },
    {
      "Size": 13568,
      "Mallocs": 312,
      "Frees": 11
    },
    {
      "Size": 14336,
      "Mallocs": 32,
      "Frees": 21
    },
    {
      "Size": 16384,
      "Mallocs": 108,
      "Frees": 70
    },
    {
      "Size": 18432,
      "Mallocs": 19,
      "Frees": 9
    }
  ]
}
----
Non-Go stats:
{
  "CGoAllocatedBytes": 21922768,
  "CGoTotalBytes": 30556160
}
