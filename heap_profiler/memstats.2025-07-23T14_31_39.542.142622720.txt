Go memory stats:
{
  "Alloc": 133724736,
  "TotalAlloc": 597258944,
  "Sys": 496171384,
  "Lookups": 0,
  "Mallocs": 2397232,
  "Frees": 2097315,
  "HeapAlloc": 133724736,
  "HeapSys": 475987968,
  "HeapIdle": 280641536,
  "HeapInuse": 195346432,
  "HeapReleased": 1507328,
  "HeapObjects": 299917,
  "StackInuse": 6225920,
  "StackSys": 6225920,
  "MSpanInuse": 1923520,
  "MSpanSys": 4292160,
  "MCacheInuse": 14400,
  "MCacheSys": 15600,
  "BuckHashSys": 1657805,
  "GCSys": 5862008,
  "OtherSys": 2129923,
  "NextGC": 489713840,
  "LastGC": 1753281099513420000,
  "PauseTotalNs": 1987918,
  "PauseNs": [
    11416,
    37251,
    186709,
    77000,
    122959,
    47125,
    204083,
    1301375,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "PauseEnd": [
    1753281059141233000,
    1753281059148681000,
    1753281059173601000,
    1753281059193782000,
    1753281059209840000,
    1753281059225392000,
    1753281059237519000,
    1753281099513420000,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "NumGC": 8,
  "NumForcedGC": 0,
  "GCCPUFraction": 0.0005263865809555297,
  "EnableGC": true,
  "DebugGC": false,
  "BySize": [
    {
      "Size": 0,
      "Mallocs": 0,
      "Frees": 0
    },
    {
      "Size": 8,
      "Mallocs": 38386,
      "Frees": 31417
    },
    {
      "Size": 16,
      "Mallocs": 611591,
      "Frees": 525881
    },
    {
      "Size": 24,
      "Mallocs": 126056,
      "Frees": 110219
    },
    {
      "Size": 32,
      "Mallocs": 137513,
      "Frees": 118985
    },
    {
      "Size": 48,
      "Mallocs": 213744,
      "Frees": 172541
    },
    {
      "Size": 64,
      "Mallocs": 345491,
      "Frees": 311409
    },
    {
      "Size": 80,
      "Mallocs": 54355,
      "Frees": 47001
    },
    {
      "Size": 96,
      "Mallocs": 77217,
      "Frees": 58116
    },
    {
      "Size": 112,
      "Mallocs": 62805,
      "Frees": 54881
    },
    {
      "Size": 128,
      "Mallocs": 35251,
      "Frees": 30042
    },
    {
      "Size": 144,
      "Mallocs": 20198,
      "Frees": 17322
    },
    {
      "Size": 160,
      "Mallocs": 35730,
      "Frees": 28934
    },
    {
      "Size": 176,
      "Mallocs": 16218,
      "Frees": 14024
    },
    {
      "Size": 192,
      "Mallocs": 29551,
      "Frees": 22800
    },
    {
      "Size": 208,
      "Mallocs": 10220,
      "Frees": 7522
    },
    {
      "Size": 224,
      "Mallocs": 20402,
      "Frees": 15692
    },
    {
      "Size": 240,
      "Mallocs": 41772,
      "Frees": 34711
    },
    {
      "Size": 256,
      "Mallocs": 18218,
      "Frees": 13785
    },
    {
      "Size": 288,
      "Mallocs": 20457,
      "Frees": 17787
    },
    {
      "Size": 320,
      "Mallocs": 13292,
      "Frees": 12344
    },
    {
      "Size": 352,
      "Mallocs": 9606,
      "Frees": 8813
    },
    {
      "Size": 384,
      "Mallocs": 8831,
      "Frees": 7983
    },
    {
      "Size": 416,
      "Mallocs": 5782,
      "Frees": 5224
    },
    {
      "Size": 448,
      "Mallocs": 7628,
      "Frees": 4656
    },
    {
      "Size": 480,
      "Mallocs": 11034,
      "Frees": 9356
    },
    {
      "Size": 512,
      "Mallocs": 3084,
      "Frees": 2699
    },
    {
      "Size": 576,
      "Mallocs": 4396,
      "Frees": 3993
    },
    {
      "Size": 640,
      "Mallocs": 3285,
      "Frees": 3031
    },
    {
      "Size": 704,
      "Mallocs": 9757,
      "Frees": 8988
    },
    {
      "Size": 768,
      "Mallocs": 4891,
      "Frees": 4093
    },
    {
      "Size": 896,
      "Mallocs": 7665,
      "Frees": 6582
    },
    {
      "Size": 1024,
      "Mallocs": 8802,
      "Frees": 7941
    },
    {
      "Size": 1152,
      "Mallocs": 3121,
      "Frees": 2565
    },
    {
      "Size": 1280,
      "Mallocs": 856,
      "Frees": 714
    },
    {
      "Size": 1408,
      "Mallocs": 8734,
      "Frees": 7423
    },
    {
      "Size": 1536,
      "Mallocs": 3421,
      "Frees": 3295
    },
    {
      "Size": 1792,
      "Mallocs": 2783,
      "Frees": 2184
    },
    {
      "Size": 2048,
      "Mallocs": 726,
      "Frees": 566
    },
    {
      "Size": 2304,
      "Mallocs": 797,
      "Frees": 547
    },
    {
      "Size": 2688,
      "Mallocs": 631,
      "Frees": 488
    },
    {
      "Size": 3072,
      "Mallocs": 1714,
      "Frees": 966
    },
    {
      "Size": 3200,
      "Mallocs": 34,
      "Frees": 29
    },
    {
      "Size": 3456,
      "Mallocs": 143,
      "Frees": 123
    },
    {
      "Size": 4096,
      "Mallocs": 1110,
      "Frees": 835
    },
    {
      "Size": 4864,
      "Mallocs": 485,
      "Frees": 449
    },
    {
      "Size": 5376,
      "Mallocs": 607,
      "Frees": 484
    },
    {
      "Size": 6144,
      "Mallocs": 308,
      "Frees": 232
    },
    {
      "Size": 6528,
      "Mallocs": 15,
      "Frees": 6
    },
    {
      "Size": 6784,
      "Mallocs": 14,
      "Frees": 14
    },
    {
      "Size": 6912,
      "Mallocs": 15,
      "Frees": 10
    },
    {
      "Size": 8192,
      "Mallocs": 665,
      "Frees": 541
    },
    {
      "Size": 9472,
      "Mallocs": 71,
      "Frees": 45
    },
    {
      "Size": 9728,
      "Mallocs": 27,
      "Frees": 22
    },
    {
      "Size": 10240,
      "Mallocs": 18,
      "Frees": 8
    },
    {
      "Size": 10880,
      "Mallocs": 15,
      "Frees": 7
    },
    {
      "Size": 12288,
      "Mallocs": 55,
      "Frees": 39
    },
    {
      "Size": 13568,
      "Mallocs": 312,
      "Frees": 11
    },
    {
      "Size": 14336,
      "Mallocs": 30,
      "Frees": 21
    },
    {
      "Size": 16384,
      "Mallocs": 105,
      "Frees": 70
    },
    {
      "Size": 18432,
      "Mallocs": 17,
      "Frees": 9
    }
  ]
}
----
Non-Go stats:
{
  "CGoAllocatedBytes": 13106640,
  "CGoTotalBytes": 21413888
}
