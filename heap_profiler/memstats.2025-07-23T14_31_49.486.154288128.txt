Go memory stats:
{
  "Alloc": 156259664,
  "TotalAlloc": 619793872,
  "Sys": 496171384,
  "Lookups": 0,
  "Mallocs": 2630320,
  "Frees": 2127401,
  "HeapAlloc": 156259664,
  "HeapSys": 475234304,
  "HeapIdle": 268058624,
  "HeapInuse": 207175680,
  "HeapReleased": 1392640,
  "HeapObjects": 502919,
  "StackInuse": 6979584,
  "StackSys": 6979584,
  "MSpanInuse": 1944000,
  "MSpanSys": 4292160,
  "MCacheInuse": 14400,
  "MCacheSys": 15600,
  "BuckHashSys": 1664237,
  "GCSys": 5884656,
  "OtherSys": 2100843,
  "NextGC": 489713840,
  "LastGC": 1753281099513420000,
  "PauseTotalNs": 1987918,
  "PauseNs": [
    11416,
    37251,
    186709,
    77000,
    122959,
    47125,
    204083,
    1301375,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "PauseEnd": [
    1753281059141233000,
    1753281059148681000,
    1753281059173601000,
    1753281059193782000,
    1753281059209840000,
    1753281059225392000,
    1753281059237519000,
    1753281099513420000,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "NumGC": 8,
  "NumForcedGC": 0,
  "GCCPUFraction": 0.0005263865809555297,
  "EnableGC": true,
  "DebugGC": false,
  "BySize": [
    {
      "Size": 0,
      "Mallocs": 0,
      "Frees": 0
    },
    {
      "Size": 8,
      "Mallocs": 42083,
      "Frees": 31417
    },
    {
      "Size": 16,
      "Mallocs": 707973,
      "Frees": 525881
    },
    {
      "Size": 24,
      "Mallocs": 128771,
      "Frees": 110219
    },
    {
      "Size": 32,
      "Mallocs": 142406,
      "Frees": 118985
    },
    {
      "Size": 48,
      "Mallocs": 234017,
      "Frees": 172541
    },
    {
      "Size": 64,
      "Mallocs": 378482,
      "Frees": 311409
    },
    {
      "Size": 80,
      "Mallocs": 65067,
      "Frees": 47001
    },
    {
      "Size": 96,
      "Mallocs": 92791,
      "Frees": 58116
    },
    {
      "Size": 112,
      "Mallocs": 65497,
      "Frees": 54881
    },
    {
      "Size": 128,
      "Mallocs": 36620,
      "Frees": 30042
    },
    {
      "Size": 144,
      "Mallocs": 20493,
      "Frees": 17322
    },
    {
      "Size": 160,
      "Mallocs": 36676,
      "Frees": 28934
    },
    {
      "Size": 176,
      "Mallocs": 16297,
      "Frees": 14024
    },
    {
      "Size": 192,
      "Mallocs": 29822,
      "Frees": 22800
    },
    {
      "Size": 208,
      "Mallocs": 10502,
      "Frees": 7522
    },
    {
      "Size": 224,
      "Mallocs": 20475,
      "Frees": 15692
    },
    {
      "Size": 240,
      "Mallocs": 48405,
      "Frees": 34711
    },
    {
      "Size": 256,
      "Mallocs": 18700,
      "Frees": 13785
    },
    {
      "Size": 288,
      "Mallocs": 20795,
      "Frees": 17787
    },
    {
      "Size": 320,
      "Mallocs": 13488,
      "Frees": 12344
    },
    {
      "Size": 352,
      "Mallocs": 9716,
      "Frees": 8813
    },
    {
      "Size": 384,
      "Mallocs": 8993,
      "Frees": 7983
    },
    {
      "Size": 416,
      "Mallocs": 5830,
      "Frees": 5224
    },
    {
      "Size": 448,
      "Mallocs": 7657,
      "Frees": 4656
    },
    {
      "Size": 480,
      "Mallocs": 11481,
      "Frees": 9356
    },
    {
      "Size": 512,
      "Mallocs": 3114,
      "Frees": 2699
    },
    {
      "Size": 576,
      "Mallocs": 4520,
      "Frees": 3993
    },
    {
      "Size": 640,
      "Mallocs": 3304,
      "Frees": 3031
    },
    {
      "Size": 704,
      "Mallocs": 10157,
      "Frees": 8988
    },
    {
      "Size": 768,
      "Mallocs": 4955,
      "Frees": 4093
    },
    {
      "Size": 896,
      "Mallocs": 7716,
      "Frees": 6582
    },
    {
      "Size": 1024,
      "Mallocs": 8814,
      "Frees": 7941
    },
    {
      "Size": 1152,
      "Mallocs": 3155,
      "Frees": 2565
    },
    {
      "Size": 1280,
      "Mallocs": 856,
      "Frees": 714
    },
    {
      "Size": 1408,
      "Mallocs": 9046,
      "Frees": 7423
    },
    {
      "Size": 1536,
      "Mallocs": 3457,
      "Frees": 3295
    },
    {
      "Size": 1792,
      "Mallocs": 2830,
      "Frees": 2184
    },
    {
      "Size": 2048,
      "Mallocs": 735,
      "Frees": 566
    },
    {
      "Size": 2304,
      "Mallocs": 821,
      "Frees": 547
    },
    {
      "Size": 2688,
      "Mallocs": 638,
      "Frees": 488
    },
    {
      "Size": 3072,
      "Mallocs": 1716,
      "Frees": 966
    },
    {
      "Size": 3200,
      "Mallocs": 35,
      "Frees": 29
    },
    {
      "Size": 3456,
      "Mallocs": 143,
      "Frees": 123
    },
    {
      "Size": 4096,
      "Mallocs": 1116,
      "Frees": 835
    },
    {
      "Size": 4864,
      "Mallocs": 540,
      "Frees": 449
    },
    {
      "Size": 5376,
      "Mallocs": 622,
      "Frees": 484
    },
    {
      "Size": 6144,
      "Mallocs": 311,
      "Frees": 232
    },
    {
      "Size": 6528,
      "Mallocs": 15,
      "Frees": 6
    },
    {
      "Size": 6784,
      "Mallocs": 14,
      "Frees": 14
    },
    {
      "Size": 6912,
      "Mallocs": 15,
      "Frees": 10
    },
    {
      "Size": 8192,
      "Mallocs": 674,
      "Frees": 541
    },
    {
      "Size": 9472,
      "Mallocs": 74,
      "Frees": 45
    },
    {
      "Size": 9728,
      "Mallocs": 29,
      "Frees": 22
    },
    {
      "Size": 10240,
      "Mallocs": 18,
      "Frees": 8
    },
    {
      "Size": 10880,
      "Mallocs": 15,
      "Frees": 7
    },
    {
      "Size": 12288,
      "Mallocs": 56,
      "Frees": 39
    },
    {
      "Size": 13568,
      "Mallocs": 312,
      "Frees": 11
    },
    {
      "Size": 14336,
      "Mallocs": 30,
      "Frees": 21
    },
    {
      "Size": 16384,
      "Mallocs": 107,
      "Frees": 70
    },
    {
      "Size": 18432,
      "Mallocs": 18,
      "Frees": 9
    }
  ]
}
----
Non-Go stats:
{
  "CGoAllocatedBytes": 21733584,
  "CGoTotalBytes": 30195712
}
