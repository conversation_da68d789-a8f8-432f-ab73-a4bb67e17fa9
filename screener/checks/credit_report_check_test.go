package checks

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/creditreportv2"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/gamma/screener/config"
	"github.com/epifi/gamma/screener/events"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"
)

func TestCreditReportCheck_RunCheck(t *testing.T) {
	var (
		actorId       = "actor-id"
		screenerAttId = "screener-attempt-id"
		checkType     = screener.CheckType_CHECK_TYPE_CREDIT_REPORT
		checkAtt      = &screener.ScreenerCheckAttempt{
			ScreenerAttemptId: screenerAttId,
			CheckType:         checkType,
			CheckResult:       screener.CheckResult_CHECK_RESULT_IN_PROGRESS,
		}
		failedCa = &screener.ScreenerCheckAttempt{
			ScreenerAttemptId: screenerAttId,
			CheckType:         checkType,
			CheckResult:       screener.CheckResult_CHECK_RESULT_FAILED,
			DeletedAtUnix:     100,
		}
	)
	type args struct {
		req  *RunCheckRequest
		conf *config.CreditReportConfig
	}
	tests := map[string]struct {
		args       args
		want       *screener.RunCheckResponse
		wantErr    error
		setupMocks func(*mockedDependencies)
	}{
		"credit report successfully verified - score<750": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusOk(),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_PASSED,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return(nil, epifierrors.ErrRecordNotFound)
				m.scaDao.EXPECT().Create(gomock.Any(), checkAtt).Return(checkAtt, nil)
				m.crV2Client.EXPECT().GetCreditReport(gomock.Any(), &creditreportv2.GetCreditReportRequest{
					ActorId: actorId,
				}).Return(&creditreportv2.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					VerificationStatus: creditreportv2.VerificationStatus_VERIFICATION_STATUS_SUCCESS,
					DownloadConsent:    creditreportv2.DownloadConsent_DOWNLOAD_CONSENT_CONSENT_GIVEN,
					CreditReportData: &creditreportv2.CreditReportData{
						CreditScore: 749,
					},
				}, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&user.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				m.eventLogger.EXPECT().LogCreditReportEvent(gomock.Any(), actorId, int32(749), events.EventScreenerCompleted)
				m.scaDao.EXPECT().UpdateCheckDetails(gomock.Any(), &screener.ScreenerCheckAttempt{
					ScreenerAttemptId: screenerAttId,
					CheckType:         checkType,
					CheckResult:       screener.CheckResult_CHECK_RESULT_PASSED,
				}, []screener.ScreenerCheckAttemptFieldMask{
					screener.ScreenerCheckAttemptFieldMask_SCREENER_CHECK_ATTEMPT_FIELD_MASK_RESULT,
				}).Return(nil)
				m.scaDao.EXPECT().DeleteCheckAttempt(gomock.Any(), screenerAttId, checkType).Return(nil)
			},
		},
		"credit report successfully verified - score>750": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusOk(),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_PASSED,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return(nil, epifierrors.ErrRecordNotFound)
				m.scaDao.EXPECT().Create(gomock.Any(), checkAtt).Return(checkAtt, nil)
				m.crV2Client.EXPECT().GetCreditReport(gomock.Any(), &creditreportv2.GetCreditReportRequest{
					ActorId: actorId,
				}).Return(&creditreportv2.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					VerificationStatus: creditreportv2.VerificationStatus_VERIFICATION_STATUS_SUCCESS,
					DownloadConsent:    creditreportv2.DownloadConsent_DOWNLOAD_CONSENT_CONSENT_GIVEN,
					CreditReportData: &creditreportv2.CreditReportData{
						CreditScore: 760,
					},
				}, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&user.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				m.eventLogger.EXPECT().LogCreditReportEvent(gomock.Any(), actorId, int32(760), events.EventScreenerCompleted)
				m.scaDao.EXPECT().UpdateCheckDetails(gomock.Any(), &screener.ScreenerCheckAttempt{
					ScreenerAttemptId: screenerAttId,
					CheckType:         checkType,
					CheckResult:       screener.CheckResult_CHECK_RESULT_PASSED,
				}, []screener.ScreenerCheckAttemptFieldMask{
					screener.ScreenerCheckAttemptFieldMask_SCREENER_CHECK_ATTEMPT_FIELD_MASK_RESULT,
				}).Return(nil)
				m.scaDao.EXPECT().DeleteCheckAttempt(gomock.Any(), screenerAttId, checkType).Return(nil)
			},
		},
		"credit report verification - consent not given": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusOk(),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_FAILED,
					RetriesLeft: 0,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return(nil, epifierrors.ErrRecordNotFound)
				m.scaDao.EXPECT().Create(gomock.Any(), checkAtt).Return(checkAtt, nil)
				m.crV2Client.EXPECT().GetCreditReport(gomock.Any(), &creditreportv2.GetCreditReportRequest{
					ActorId: actorId,
				}).Return(&creditreportv2.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					VerificationStatus: creditreportv2.VerificationStatus_VERIFICATION_STATUS_FAILED,
					DownloadConsent:    creditreportv2.DownloadConsent_DOWNLOAD_CONSENT_CONSENT_GIVEN,
				}, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&user.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				m.eventLogger.EXPECT().LogCreditReportEvent(gomock.Any(), actorId, int32(0), events.CRVerificationFailedEntryPoint)
				m.scaDao.EXPECT().UpdateCheckDetails(gomock.Any(), &screener.ScreenerCheckAttempt{
					ScreenerAttemptId: screenerAttId,
					CheckType:         checkType,
					CheckResult:       screener.CheckResult_CHECK_RESULT_FAILED,
				}, []screener.ScreenerCheckAttemptFieldMask{
					screener.ScreenerCheckAttemptFieldMask_SCREENER_CHECK_ATTEMPT_FIELD_MASK_RESULT,
				}).Return(nil)
				m.scaDao.EXPECT().DeleteCheckAttempt(gomock.Any(), screenerAttId, checkType).Return(nil)
			},
		},
		"credit report verification in-progress": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusOk(),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_IN_PROGRESS,
					RetriesLeft: 1,
				},
				NextAction: &dlPb.Deeplink{
					Screen: dlPb.Screen_CREDIT_REPORT_VERIFICATION_STATUS,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return(nil, epifierrors.ErrRecordNotFound)
				m.scaDao.EXPECT().Create(gomock.Any(), checkAtt).Return(checkAtt, nil)
				m.crV2Client.EXPECT().GetCreditReport(gomock.Any(), &creditreportv2.GetCreditReportRequest{
					ActorId: actorId,
				}).Return(&creditreportv2.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					VerificationStatus: creditreportv2.VerificationStatus_VERIFICATION_STATUS_IN_PROGRESS,
					DownloadConsent:    creditreportv2.DownloadConsent_DOWNLOAD_CONSENT_CONSENT_GIVEN,
				}, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&user.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
		},
		"credit report verification in sus state": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusOk(),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_IN_PROGRESS,
					RetriesLeft: 1,
				},
				NextAction: &dlPb.Deeplink{
					Screen: dlPb.Screen_CREDIT_REPORT_VERIFICATION_STATUS,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return(nil, epifierrors.ErrRecordNotFound)
				m.scaDao.EXPECT().Create(gomock.Any(), checkAtt).Return(checkAtt, nil)
				m.crV2Client.EXPECT().GetCreditReport(gomock.Any(), &creditreportv2.GetCreditReportRequest{
					ActorId: actorId,
				}).Return(&creditreportv2.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					VerificationStatus: creditreportv2.VerificationStatus_VERIFICATION_STATUS_UNSPECIFIED,
					DownloadConsent:    creditreportv2.DownloadConsent_DOWNLOAD_CONSENT_CONSENT_GIVEN,
				}, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&user.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
		},
		"consent not given": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusOk(),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_FAILED,
					RetriesLeft: 0,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return(nil, epifierrors.ErrRecordNotFound)
				m.scaDao.EXPECT().Create(gomock.Any(), checkAtt).Return(checkAtt, nil)
				m.crV2Client.EXPECT().GetCreditReport(gomock.Any(), &creditreportv2.GetCreditReportRequest{
					ActorId: actorId,
				}).Return(&creditreportv2.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					VerificationStatus: creditreportv2.VerificationStatus_VERIFICATION_STATUS_UNSPECIFIED,
					DownloadConsent:    creditreportv2.DownloadConsent_DOWNLOAD_CONSENT_NO_CONSENT,
				}, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&user.GetUserResponse{
					Status: rpc.StatusInternal(),
				}, nil)
				m.eventLogger.EXPECT().LogCreditReportEvent(gomock.Any(), actorId, int32(0), events.NoConsentEntryPoint)
				m.scaDao.EXPECT().UpdateCheckDetails(gomock.Any(), &screener.ScreenerCheckAttempt{
					ScreenerAttemptId: screenerAttId,
					CheckType:         checkType,
					CheckResult:       screener.CheckResult_CHECK_RESULT_FAILED,
				}, []screener.ScreenerCheckAttemptFieldMask{
					screener.ScreenerCheckAttemptFieldMask_SCREENER_CHECK_ATTEMPT_FIELD_MASK_RESULT,
				}).Return(nil)
				m.scaDao.EXPECT().DeleteCheckAttempt(gomock.Any(), screenerAttId, checkType).Return(nil)
			},
		},
		"consent screen not showed": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusOk(),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_IN_PROGRESS,
					RetriesLeft: 1,
				},
				NextAction: &dlPb.Deeplink{
					Screen: dlPb.Screen_CREDIT_REPORT_CONSENT,
					// Sending screen options with false because iOS needs screen options to be available
					ScreenOptions: &dlPb.Deeplink_CreditReportConsentScreenOptions{
						CreditReportConsentScreenOptions: &dlPb.CreditReportConsentScreenOptions{
							BackAction: &dlPb.BackAction{
								ShowButton: commontypes.BooleanEnum_FALSE,
							},
							IsUserPanAvailable: false,
							IsB2BUser:          false,
							Cta: &dlPb.ToggleableScreenerCta{
								ShowCta: false,
							},
						},
					},
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return([]*screener.ScreenerCheckAttempt{checkAtt}, nil)
				m.crV2Client.EXPECT().GetCreditReport(gomock.Any(), &creditreportv2.GetCreditReportRequest{
					ActorId: actorId,
				}).Return(&creditreportv2.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					VerificationStatus: creditreportv2.VerificationStatus_VERIFICATION_STATUS_UNSPECIFIED,
					DownloadConsent:    creditreportv2.DownloadConsent_DOWNLOAD_CONSENT_UNSPECIFIED,
				}, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
					},
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
		"consent screen not showed: b2bUser ": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
					BackAction: &dlPb.Deeplink{
						Screen: dlPb.Screen_ANALYSER_HUB,
					},
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusOk(),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_IN_PROGRESS,
					RetriesLeft: 1,
				},
				NextAction: &dlPb.Deeplink{
					Screen: dlPb.Screen_CREDIT_REPORT_CONSENT,
					// Sending screen options with false because iOS needs screen options to be available
					ScreenOptions: &dlPb.Deeplink_CreditReportConsentScreenOptions{
						CreditReportConsentScreenOptions: &dlPb.CreditReportConsentScreenOptions{
							BackAction: &dlPb.BackAction{
								ShowButton: commontypes.BooleanEnum_TRUE,
								Deeplink: &dlPb.Deeplink{
									Screen: dlPb.Screen_ANALYSER_HUB,
								},
							},
							IsUserPanAvailable: false,
							IsB2BUser:          true,
							Cta: &dlPb.ToggleableScreenerCta{
								ShowCta: false,
							},
						},
					},
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return([]*screener.ScreenerCheckAttempt{checkAtt}, nil)
				m.crV2Client.EXPECT().GetCreditReport(gomock.Any(), &creditreportv2.GetCreditReportRequest{
					ActorId: actorId,
				}).Return(&creditreportv2.GetCreditReportResponse{
					Status:             rpc.StatusOk(),
					VerificationStatus: creditreportv2.VerificationStatus_VERIFICATION_STATUS_UNSPECIFIED,
					DownloadConsent:    creditreportv2.DownloadConsent_DOWNLOAD_CONSENT_UNSPECIFIED,
				}, nil)
				m.userClient.EXPECT().GetUser(gomock.Any(), &user.GetUserRequest{
					Identifier: &user.GetUserRequest_ActorId{
						ActorId: actorId,
					},
				}).Return(&user.GetUserResponse{
					User: &user.User{
						Id: "userId",
						AcquisitionInfo: &user.AcquisitionInfo{
							Platform: commontypes.Platform_WEB,
						},
					},
					Status: rpc.StatusOk(),
				}, nil)
			},
		},
		"error while getting credit report": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg("error in GetCreditReport"),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_UNSCPECIFIED,
					RetriesLeft: 1,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return(nil, epifierrors.ErrRecordNotFound)
				m.scaDao.EXPECT().Create(gomock.Any(), checkAtt).Return(checkAtt, nil)
				m.crV2Client.EXPECT().GetCreditReport(gomock.Any(), &creditreportv2.GetCreditReportRequest{
					ActorId: actorId,
				}).Return(nil, epifierrors.ErrResourceExhausted)
			},
		},
		"error while creating check attempt": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while creating entry in db"),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_UNSCPECIFIED,
					RetriesLeft: 1,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return(nil, epifierrors.ErrRecordNotFound)
				m.scaDao.EXPECT().Create(gomock.Any(), checkAtt).Return(nil, epifierrors.ErrInvalidSQL)
			},
		},
		"retries exhausted": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusOk(),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_FAILED,
					RetriesLeft: 0,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return([]*screener.ScreenerCheckAttempt{failedCa}, nil)
			},
		},
		"more than 1 active attempt": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg("more than 1 active attempts"),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_UNSCPECIFIED,
					RetriesLeft: 0,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return([]*screener.ScreenerCheckAttempt{checkAtt, checkAtt}, nil)
			},
		},
		"error while getting check attempts": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: false,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching entries from db"),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_UNSCPECIFIED,
					RetriesLeft: 1,
				},
			},
			wantErr: nil,
			setupMocks: func(m *mockedDependencies) {
				m.scaDao.EXPECT().GetCheckAttempts(gomock.Any(), screenerAttId, checkType).Return(nil, epifierrors.ErrInvalidSQL)
			},
		},
		"check disabled": {
			args: args{
				req: &RunCheckRequest{
					ActorId:           actorId,
					ScreenerAttemptId: screenerAttId,
				},
				conf: &config.CreditReportConfig{
					IsDisabled: true,
					MaxRetries: 1,
				},
			},
			want: &screener.RunCheckResponse{
				Status: rpc.StatusOk(),
				CheckDetails: &screener.CheckDetails{
					CheckType:   checkType,
					CheckResult: screener.CheckResult_CHECK_RESULT_DISABLED,
					RetriesLeft: 0,
				},
			},
			wantErr:    nil,
			setupMocks: func(m *mockedDependencies) {},
		},
	}
	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			md := newMockDeps(t)

			if tt.setupMocks != nil {
				tt.setupMocks(md)
			}

			testConf := checkTS.genConf
			_ = testConf.CreditReportConfig().Set(tt.args.conf, false, nil)

			c := NewCreditReportCheck(testConf, md.userClient, md.scaDao, md.eventLogger, md.crV2Client)
			got, err := c.RunCheck(context.Background(), tt.args.req)
			if (err != nil) != (tt.wantErr != nil) {
				t.Errorf("Unexpected error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				return
			}
			if err != nil {
				if tt.wantErr.Error() != err.Error() {
					t.Errorf("Incorrect error received. GotErr = %v, wantErr = %v", err, tt.wantErr)
				}
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("CreditReportCheck() value is mismatch (-got +want):%s\n", diff)
			}
		})
	}
}
