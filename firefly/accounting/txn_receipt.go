package accounting

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"regexp"
	"strings"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	actorPb "github.com/epifi/gamma/api/actor"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	ffPb "github.com/epifi/gamma/api/firefly"
	faPb "github.com/epifi/gamma/api/firefly/accounting"
	faEnumsPb "github.com/epifi/gamma/api/firefly/accounting/enums"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	paymentinstrumentPb "github.com/epifi/gamma/api/paymentinstrument"
)

const (
	forexPremiumFeeDetailsLayout     = "1%% Forex Premium for %s transaction of %s dated %s"
	serviceTaxFeeDetailsLayout       = "Service Tax for %s transaction of %s dated %s"
	settledDetailsLayout             = "Settlement from %s for transaction dated %s of amount %s"
	transactionTimeLayout            = "Jan 02,2006 at 15:04:05"
	markupFeesVendorExtTxnIdentifier = "MARKUP"
	settledVendorExtTxnIdentifier    = "SETTLED"
	internationalTxnDetailsLayout    = "International transaction currency code - %s"
)

var (
	domesticCountryCodes       = []string{"INR", "0356", "356"}
	inAppFailureDescriptionMap = map[faEnumsPb.TransactionFailureType]string{
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INSUFFICIENT_FUND:         "Insufficient credit limit",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INVALID_PIN:               "Invalid PIN entered",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CVV2_FAIL:                 "Incorrect CVV entered",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_INVALID_EXPIRY_DATE:       "Incorrect Expiry date entered",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_DEBIT_TXN_LIMIT_EXCEEDED:  "Daily transaction limit exceeded",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_BLOCKED:              "Card is blocked",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_REPLACED:             "Card is not activated",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_LOCKED:               "Card is not activated",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT:             "Transaction limit exceeded",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_MOTO_NOT_ALLOWED:          "Transaction declined by bank",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_PIN_RETRIES_EXCEEDED:      "PIN retry attempts exceeded",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_SURRENDERD:           "Card is deactivated",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_IP_NOT_ALLOWED:            "Transaction declined by bank",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_FRAUD:                "Transaction declined by bank",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CVV_FAIL:                  "Incorrect CVV entered",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_EXCEEDS_LIMIT_CONTACTLESS: "Maximum amount for contactless transaction is exceeded",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CREDIT_CARD_BILL_UNPAID:   "Credit Card bill unpaid",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_NO_CVV2:                   "Security Credentials Failed",
		faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_CARD_NOT_ACTIVE:           "Card not active",
	}
)

// nolint: funlen
func (s *Service) FetchTxnReceipt(ctx context.Context, req *faPb.FetchTxnReceiptRequest) (*faPb.FetchTxnReceiptResponse, error) {
	var (
		res               = &faPb.FetchTxnReceiptResponse{}
		displayCategories []categorizerPb.DisplayCategory
		otherPartyDetails = &actorPb.GetEntityDetailsByActorIdResponse{}
	)
	txn, err := s.creditCardTransactionDao.GetById(ctx, req.GetTxnId(), nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no txn for given txn id", zap.String(logger.TXN_ID, req.GetTxnId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching txn entry", zap.Error(err), zap.String(logger.TXN_ID, req.GetTxnId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	txnAddInfo, err := s.creditCardTxnAdditionalInfoDao.GetByTxnId(ctx, req.GetTxnId(), nil)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no txn additional info for given txn id", zap.String(logger.TXN_ID, req.GetTxnId()))
		res.Status = rpc.StatusRecordNotFound()
		return res, nil
	case err != nil:
		logger.Error(ctx, "error in fetching txn additional info entry", zap.Error(err), zap.String(logger.TXN_ID, req.GetTxnId()))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	card, err := s.creditCardDao.GetById(ctx, txn.GetCardId(), []ffEnumsPb.CreditCardFieldMask{
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_CARD_INFO,
		ffEnumsPb.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_ACTOR_ID,
	})
	if err != nil {
		logger.Error(ctx, "error in fetching credit card entry", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	// validating actor id from card against the actor id from ctx
	actorIdFromCtx := epificontext.ActorIdFromContext(ctx)
	if actorIdFromCtx == epificontext.UnknownId {
		actorIdFromCtx = ""
	}
	if card.GetActorId() != "" && actorIdFromCtx != "" && card.GetActorId() != actorIdFromCtx {
		logger.Error(ctx, fmt.Sprintf("actor id from card [%s] does not match the one from ctx : [%s]", card.GetActorId(), actorIdFromCtx))
		res.Status = rpc.StatusPermissionDenied()
		return res, nil
	}

	piFromActorInfo, piToActorInfo, err := s.getPiActorInfoForTxn(ctx, txn, txnAddInfo, card)
	if err != nil {
		logger.Error(ctx, "error in fetching PI actor details for the txn", zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}
	// error group to parallelize the pi fetching and
	// fetching of display categories for the txn
	grp, grpCtx := errgroup.WithContext(ctx)
	grp.Go(func() error {
		displayCategories, err = s.fetchDisplayCategoriesForTxn(grpCtx, card, txn)
		if err != nil {
			return errors.Wrap(err, "error in fetching txn display categories for txn")
		}
		return nil
	})

	if grpErr := grp.Wait(); grpErr != nil {
		logger.Error(ctx, "error in fetching PI information OR display categories", zap.Error(grpErr), zap.String(logger.TXN_ID, req.GetTxnId()))
		res.Status = rpc.StatusFromError(grpErr)
		return res, nil
	}

	fromDisplayInfo := ""
	toDisplayInfo := ""
	if txn.GetTxnType() == faEnumsPb.TransactionType_TRANSACTION_TYPE_DEBIT {
		otherPartyDetails = piToActorInfo
		fromDisplayInfo = card.GetBasicInfo().GetMaskedCardNumber()
	} else {
		otherPartyDetails = piFromActorInfo
		toDisplayInfo = card.GetBasicInfo().GetMaskedCardNumber()
	}

	receiptAdditionalDetails, err := s.getReceiptAdditionalDetails(ctx, txn)
	if err != nil {
		logger.Error(ctx, "failed to get receipt additional details", zap.Error(err))
	}
	res = &faPb.FetchTxnReceiptResponse{
		Status:                       rpc.StatusOk(),
		ExternalTxnId:                txn.GetExternalTxnId(),
		IconUrl:                      otherPartyDetails.GetProfileImageUrl(),
		Remarks:                      getHumanReadableRemarks(ctx, txn.GetFailureInfo().GetFailureType(), txn.GetTxnOrigin(), txn.GetDescription()),
		Amount:                       txn.GetAmount(),
		FromName:                     getDisplayName(piFromActorInfo.GetName(), fromDisplayInfo),
		ToName:                       getDisplayName(piToActorInfo.GetName(), toDisplayInfo),
		TxnStatus:                    txn.GetTxnStatus(),
		TxnType:                      txn.GetTxnType(),
		TxnCategory:                  txn.GetTxnCategory(),
		TxnTime:                      txn.GetTxnTime(),
		PartnerUrl:                   "https://epifi-icons.pointz.in/credit_card_images/federal_partner_url_vertical.png",
		OtherPartyName:               otherPartyDetails.GetName(),
		TransactionDisplayCategories: displayCategories,
		VendorExternalTxnId:          txn.GetVendorExtTxnId(),
	}
	if receiptAdditionalDetails != nil {
		res.ReceiptAdditionalDetails = receiptAdditionalDetails
	}
	return res, nil
}

// nolint:funlen
func (s *Service) getReceiptAdditionalDetails(ctx context.Context, txn *faPb.CardTransaction) (*faPb.ReceiptAdditionalDetails, error) {
	parentTxnId := txn.GetParentTransactionId()
	if parentTxnId == "" {
		onlyContainsAlphabets := regexp.MustCompile(`^[A-Za-z]+$`).MatchString
		txnCurrency := strings.TrimSpace(txn.GetConversionInfo().GetOriginalTxnCurrency())
		// we will show details of currency code is it is an international txn and transaction currency only
		// contains alphabets
		if !lo.Contains[string](domesticCountryCodes, txnCurrency) && onlyContainsAlphabets(txnCurrency) {
			return &faPb.ReceiptAdditionalDetails{
				Details: &faPb.ReceiptAdditionalDetails_FeesAdditionalDetails{
					FeesAdditionalDetails: &faPb.FeesAdditionalDetails{
						Description: fmt.Sprintf(internationalTxnDetailsLayout, txnCurrency),
					},
				},
			}, nil
		}
		return nil, nil
	}
	parentTxn, err := s.creditCardTransactionDao.GetById(ctx, parentTxnId, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch parent txn %w", err)
	}
	parentTxnAdditionalInfo, err := s.creditCardTxnAdditionalInfoDao.GetByTxnId(ctx, parentTxnId, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch parent txn additional info %w", err)
	}
	var (
		beneficiaryName string
	)
	if parentTxnAdditionalInfo.GetEnrichedBeneficiaryInfo().GetResolvedBeneficiaryName() != "" {
		beneficiaryName = parentTxnAdditionalInfo.GetEnrichedBeneficiaryInfo().GetResolvedBeneficiaryName()
	} else {
		beneficiaryName = parentTxn.GetBeneficiaryInfo().GetBeneficiaryName()
	}
	txnAmount := moneyPkg.ToDisplayStringInIndianFormatWithCurrencyCode(parentTxn.GetAmount(), 2)
	txnTime := datetimePkg.TimestampToString(parentTxn.GetTxnTime(), transactionTimeLayout, datetimePkg.IST)
	switch txn.GetTxnCategory() {
	case faEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_SERVICE_TAX:
		return &faPb.ReceiptAdditionalDetails{
			Details: &faPb.ReceiptAdditionalDetails_FeesAdditionalDetails{
				FeesAdditionalDetails: &faPb.FeesAdditionalDetails{
					Description: fmt.Sprintf(serviceTaxFeeDetailsLayout, beneficiaryName, txnAmount, txnTime),
				},
			},
		}, nil
	case faEnumsPb.TransactionCategory_TRANSACTION_CATEGORY_FEES:
		vendorExtTxnId := txn.GetVendorExtTxnId()
		splits := strings.Split(vendorExtTxnId, "_")
		if lo.Contains[string](splits, markupFeesVendorExtTxnIdentifier) {
			return &faPb.ReceiptAdditionalDetails{
				Details: &faPb.ReceiptAdditionalDetails_FeesAdditionalDetails{
					FeesAdditionalDetails: &faPb.FeesAdditionalDetails{
						Description: fmt.Sprintf(forexPremiumFeeDetailsLayout, beneficiaryName, txnAmount, txnTime),
					},
				},
			}, nil
		}
	default:
	}
	// handling of late settlement for a txn
	vendorExtTxnId := txn.GetVendorExtTxnId()
	splits := strings.Split(vendorExtTxnId, "_")
	if lo.Contains[string](splits, settledVendorExtTxnIdentifier) {
		return &faPb.ReceiptAdditionalDetails{
			Details: &faPb.ReceiptAdditionalDetails_FeesAdditionalDetails{
				FeesAdditionalDetails: &faPb.FeesAdditionalDetails{
					Description: fmt.Sprintf(settledDetailsLayout, beneficiaryName, txnTime, txnAmount),
				},
			},
		}, nil
	}
	return nil, nil
}

// getName returns the display name
func getDisplayName(name *commontypes.Name, displayInfo string) string {
	var (
		nameString string
	)
	if name.GetMiddleName() == "" {
		nameString = fmt.Sprintf("%s %s", name.GetFirstName(), name.GetLastName())
	} else {
		nameString = fmt.Sprintf("%s %s %s", name.GetFirstName(), name.GetMiddleName(), name.GetLastName())
	}
	if displayInfo == "" {
		return nameString
	}
	return fmt.Sprintf("%s : %s", nameString, displayInfo)
}

// getPiActorInfoForTxn fetches actor entity details for the payer and payee PI ids
func (s *Service) getPiActorInfoForTxn(ctx context.Context, _ *faPb.CardTransaction, txnAddInfo *faPb.TransactionAdditionalInfo, _ *ffPb.CreditCard) (*actorPb.GetEntityDetailsByActorIdResponse, *actorPb.GetEntityDetailsByActorIdResponse, error) {
	var (
		piFromActorDetails = &actorPb.GetEntityDetailsByActorIdResponse{}
		piToActorDetails   = &actorPb.GetEntityDetailsByActorIdResponse{}
		fromErr            error
		toErr              error
	)
	toActorId := txnAddInfo.GetActorTo()
	fromActorId := txnAddInfo.GetActorFrom()

	// inititalizing error group to parallelize the entity details fetch
	// for both actors involved in the txn
	grp, grpCtx := errgroup.WithContext(ctx)

	grp.Go(func() error {
		piFromActorDetails, fromErr = s.getEntityDetails(grpCtx, fromActorId)
		if fromErr != nil {
			return errors.Wrap(fromErr, "error in fetching entity details for piFromId actor")
		}
		return nil
	})

	grp.Go(func() error {
		piToActorDetails, toErr = s.getEntityDetails(grpCtx, toActorId)
		if toErr != nil {
			return errors.Wrap(toErr, "error in fetching entity details for piToId actor")
		}
		return nil
	})

	if err := grp.Wait(); err != nil {
		return nil, nil, err
	}

	return piFromActorDetails, piToActorDetails, nil
}

func (s *Service) getEntityDetails(ctx context.Context, actorId string) (*actorPb.GetEntityDetailsByActorIdResponse, error) {
	res, err := s.actorClient.GetEntityDetailsByActorId(ctx, &actorPb.GetEntityDetailsByActorIdRequest{ActorId: actorId})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, errors.Wrap(te, "error in fetching entity details for actor")
	}
	return res, nil
}

// resolving the payee actor from the pi id and the payer actor name
func (s *Service) resolveActorTo(ctx context.Context, piTo, actorFrom, actorToName string) (string, error) {
	actorTo, err := s.actorClient.ResolveActorTo(ctx, &actorPb.ResolveActorToRequest{
		ActorFrom:   actorFrom,
		PiTo:        piTo,
		ActorToName: actorToName,
		Ownership:   commontypes.Ownership_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(actorTo, err); te != nil {
		return "", errors.Wrap(te, "error in pi resolution to")
	}
	return actorTo.GetActorTo(), nil
}

// resolving the payer actor from the pi id and the payee actor name
func (s *Service) resolveActorFrom(ctx context.Context, piFrom, actorTo, actorFromName string) (string, error) {
	actorFrom, err := s.actorClient.ResolveActorFrom(ctx, &actorPb.ResolveActorFromRequest{
		ActorTo:       actorTo,
		PiFrom:        piFrom,
		ActorFromName: actorFromName,
		Ownership:     commontypes.Ownership_EPIFI_TECH,
	})
	if te := epifigrpc.RPCError(actorFrom, err); te != nil {
		return "", errors.Wrap(te, "error in pi resolution from")
	}
	return actorFrom.GetActorFrom(), nil
}

func (s *Service) fetchPiMap(ctx context.Context, piIdArray []string) (map[string]*paymentinstrumentPb.PaymentInstrument, error) {
	piList, err := s.piClient.GetPIsByIds(ctx, &paymentinstrumentPb.GetPIsByIdsRequest{Ids: piIdArray})
	if te := epifigrpc.RPCError(piList, err); te != nil {
		return nil, errors.Wrap(te, "error in fetching pi by id")
	}
	piMap := make(map[string]*paymentinstrumentPb.PaymentInstrument, 0)
	for _, pi := range piList.GetPaymentinstruments() {
		piMap[pi.GetId()] = pi
	}
	return piMap, nil
}

// fetchDisplayCategoriesForTxn returns the list of currently selected display categories for the txn
// the list would contain all the display categories as per identified by the categorizer service and
// the ones explicitly added for the user
func (s *Service) fetchDisplayCategoriesForTxn(ctx context.Context, card *ffPb.CreditCard, txn *faPb.CardTransaction) ([]categorizerPb.DisplayCategory, error) {
	dispCategory, err := s.txnCategorizerClient.GetTxnCategoryDetails(ctx, &categorizerPb.GetTxnCategoryDetailsRequest{
		ActorId:      card.GetActorId(),
		Id:           &categorizerPb.GetTxnCategoryDetailsRequest_TxnId{TxnId: txn.GetId()},
		Provenance:   categorizerPb.Provenance_PROVENANCE_UNSPECIFIED,
		DisplayState: categorizerPb.DisplayState_DISPLAY_STATE_ENABLED,
		ActivityId: &categorizerPb.ActivityId{
			Id: &categorizerPb.ActivityId_FiCardTxnId{
				FiCardTxnId: txn.GetId(),
			},
		},
	})
	if dispCategory.GetStatus().IsRecordNotFound() {
		return []categorizerPb.DisplayCategory{}, nil
	} else if te := epifigrpc.RPCError(dispCategory, err); te != nil {
		return nil, errors.Wrap(te, "error in fetching display categories from txn categorizer")
	}
	dispCategoryArr := make([]categorizerPb.DisplayCategory, 0)
	for _, ontology := range dispCategory.GetTxnCategories().GetOntologies() {
		dispCategoryArr = append(dispCategoryArr, ontology.GetDisplayCategory())
	}
	return dispCategoryArr, nil
}

//nolint:unused
func getHumanReadableRemarks(ctx context.Context, failureType faEnumsPb.TransactionFailureType, txnOrigin faEnumsPb.TransactionOrigin, remark string) string {
	remarkVal, ok := inAppFailureDescriptionMap[failureType]
	if ok {
		return remarkVal
	}

	if failureType == faEnumsPb.TransactionFailureType_TRANSACTION_FAILURE_TYPE_PREFERENCE_FAILURE {
		switch {
		case remark == "Customer Preference Failed: CONTACTLESS":
			return "Contactless transactions disabled"
		case remark == "Customer Preference Failed: INTERNATIONAL":
			return "International transactions disabled"
		case txnOrigin == faEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_ATM:
			return "ATM withdrawals not allowed"
		case txnOrigin == faEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_POS ||
			txnOrigin == faEnumsPb.TransactionOrigin_TRANSACTION_ORIGIN_CASH_AT_POS:
			return "POS transactions disabled"
		default:
			// continue
		}
	}

	logger.Info(ctx, fmt.Sprintf("unhandled txn failure description: %v", remark))
	return remark
}
