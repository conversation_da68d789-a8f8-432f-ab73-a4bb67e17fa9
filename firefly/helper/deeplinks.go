// nolint
package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	anyPb "google.golang.org/protobuf/types/known/anypb"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/constants"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/frontend/app"
	"github.com/epifi/be-common/pkg/integer"
	"github.com/epifi/be-common/pkg/money"
	typesPkg "github.com/epifi/be-common/pkg/types"

	depositPb "github.com/epifi/gamma/api/deposit"
	ccFireflyPb "github.com/epifi/gamma/api/firefly"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/firefly/lms"
	"github.com/epifi/gamma/api/frontend/analyser"
	pb "github.com/epifi/gamma/api/frontend/analyser"
	"github.com/epifi/gamma/api/frontend/budgeting/reminder/meta"
	"github.com/epifi/gamma/api/frontend/deeplink"
	ffFePb "github.com/epifi/gamma/api/frontend/firefly"
	ffFeEnumsPb "github.com/epifi/gamma/api/frontend/firefly/enums"
	"github.com/epifi/gamma/api/frontend/waitlist/waitlist_enums"
	vkycBe "github.com/epifi/gamma/api/kyc/vkyc"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"
	ffDeeplink "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/firefly"
	ffScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/firefly"
	cxScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/inapphelp"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	ffGenConf "github.com/epifi/gamma/firefly/config/genconf"
	"github.com/epifi/gamma/firefly/config/worker"
	workerConf "github.com/epifi/gamma/firefly/config/worker"
	workerGenConf "github.com/epifi/gamma/firefly/config/worker/genconf"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/gamma/pkg/vkyc"
)

var (
	cardDetails = &deeplink.InfoBlock{
		InfoItems: []*deeplink.InfoItem{
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/bear_with_us/newCVP_3Percent.png",
				Title: "3% back on spends",
				Desc:  "On India’s top 20 brands",
			},
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/bear_with_us/newCVP_zeroPercent.png",
				Title: "0 Forex charges",
				Desc:  "On all your international spends",
			},
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/bear_with_us/newCVP_greenHeart.png",
				Title: "Welcome benefits",
				Desc:  "Gift cards worth more than ₹4,000",
			},
		},
	}
	securedCardDetails = &deeplink.InfoBlock{
		InfoItems: []*deeplink.InfoItem{
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/secured_card_benifits_spends_4x.png",
				Title: "20% off on weekend spends",
				Desc:  "On Swiggy, PayTM & BookMyShow",
			},
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/secured_card_benifits_green_tick_4x.png",
				Title: "Guaranteed approval",
				Desc:  "100% approval rate",
			},
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/secured_card_benifits_notebook_4x.png",
				Title: "No credit check",
				Desc:  "No proof required",
			},
		},
	}
	massUnsecuredCardDetails = &deeplink.InfoBlock{
		InfoItems: []*deeplink.InfoItem{
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/mass_unsecured_benefits_20_percent.png",
				Title: "Up to 20% cashback on weekends",
				Desc:  "On Zomato, Ajio and more",
			},
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/mass_unsecured_benefits_golden_check_mark.png",
				Title: "Lifetime free",
				Desc:  "No joining fee. No annual fee",
			},
			{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/mass_unsecured_benefits_golden_4x_image.png",
				Title: "4x rewards every weekend",
				Desc:  "On Uber, Flipkart and more",
			},
		},
	}
	ccBenefits = []*deeplink.InfoItem{
		{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/rotating_arrow.png",
			Title: "5x rewards on top 3 merchants",
		},
		{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/bell_icon.png",
			Title: "Choose your own billing cycle",
		},
		{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/rotating_arrow.png",
			Title: "Set personalised reminders",
		},
		{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/bell_icon.png",
			Title: "Lounge access & 1% forex charges",
		},
		{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/rotating_arrow.png",
			Title: "Pick your own welcome offer",
		},
		{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/bell_icon.png",
			Title: "Track all spends in one place",
		},
		{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/rotating_arrow.png",
			Title: "Access exciting catalogue offers",
		},
		{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/bell_icon.png",
			Title: "Milestone offers on spends of ₹2.5L & ₹4L",
		},
	}

	thresholdForLimit = &moneyPb.Money{
		CurrencyCode: money.RupeeCurrencyCode,
		Units:        200000,
	}

	thresholdLimitForShowingLimitUpdate = &moneyPb.Money{
		CurrencyCode: money.RupeeCurrencyCode,
		Units:        200000,
	}
)

const goldenAddressSelectPinIcon = "https://epifi-icons.pointz.in/credit_card_images/CCShippingAddressSelectGoldPinIcon.png"

func GetCreditCardActivationDeepLink(ctx context.Context, cardRequest *ccFireflyPb.CardRequest, conf *workerGenConf.Config) (*deeplink.Deeplink, error) {
	var (
		cardInfoItems         []*deeplink.InfoItemV2
		defaultBillGenDate    int64
		defaultPaymentDueDate int64
	)
	staticScreenOptions := &workerConf.CardActivationScreenOptions{
		InfoItems:                  conf.Screens().CardActivationScreenOptions().InfoItems(),
		CardImageUrl:               conf.Screens().CardActivationScreenOptions().CardImageUrl(),
		Heading:                    conf.Screens().CardActivationScreenOptions().Heading(),
		SubHeading:                 conf.Screens().CardActivationScreenOptions().SubHeading(),
		Tnc:                        conf.Screens().CardActivationScreenOptions().Tnc(),
		ActivateDigitalCardCtaText: conf.Screens().CardActivationScreenOptions().ActivateDigitalCardCtaText(ctx),
		ImportantTncUrl:            conf.Screens().CardActivationScreenOptions().ImportantTncUrl(),
		KeyFactStatementTncUrl:     conf.Screens().CardActivationScreenOptions().KeyFactStatementTncUrl(),
		CreditCardTncUrl:           conf.Screens().CardActivationScreenOptions().CreditCardTncUrl(),
	}
	switch {
	case cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSkipBillGenDateCaptureStage():
		defaultBillGenDate = conf.Screens().CardActivationScreenOptions().DefaultBillGenDate(ctx)
		defaultPaymentDueDate = conf.Screens().CardActivationScreenOptions().DefaultPaymentDueDate(ctx)
	case cardRequest.GetRequestDetails().GetBillGenDate() != 0:
		defaultBillGenDate = cardRequest.GetRequestDetails().GetBillGenDate()
		defaultPaymentDueDate = cardRequest.GetRequestDetails().GetPaymentDueDate()
	default:
		defaultBillGenDate = conf.Screens().CardActivationScreenOptions().DefaultBillGenDate(ctx)
		defaultPaymentDueDate = conf.Screens().CardActivationScreenOptions().DefaultPaymentDueDate(ctx)
	}
	marshalledScreenOptions, err := json.Marshal(staticScreenOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to marshall screen options %w", err)
	}
	confScreenOptionsCopy := &workerConf.CardActivationScreenOptions{}
	err = json.Unmarshal(marshalledScreenOptions, confScreenOptionsCopy)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshall screen options %w", err)
	}
	for idx, infoItem := range confScreenOptionsCopy.InfoItems {
		switch idx {
		case 0:
			infoItem.SubTitleText.PlainString = fmt.Sprintf(infoItem.SubTitleText.PlainString,
				money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, true))
			limitComparison, err := money.CompareV2(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), thresholdLimitForShowingLimitUpdate)
			if err != nil {
				return nil, fmt.Errorf("failed to compare limit %w", err)
			}
			// if limit is greater than 2 lacs then we will not show description text to the user informing
			// about limit increase
			if limitComparison >= 0 {
				infoItem.DescriptionText = nil
			}
		case 1:
			infoItem.SubTitleText.PlainString = fmt.Sprintf(infoItem.SubTitleText.PlainString,
				defaultBillGenDate, getDateSuffix(int(defaultBillGenDate)))
		}
		cardInfoItem := &deeplink.InfoItemV2{
			Title:    typesPkg.NewText(infoItem.TitleText, ""),
			Desc:     typesPkg.NewText(infoItem.DescriptionText, ""),
			SubTitle: typesPkg.NewText(infoItem.SubTitleText, ""),
		}
		if infoItem.InfoBlockTitle != "" {
			cardInfoItem.Tooltip = &deeplink.InfoToolTip{
				Info: &deeplink.InfoBlock{
					Title: infoItem.InfoBlockTitle,
					Desc:  infoItem.InfoBlockDescription,
				},
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/information_grey_4x.png",
			}
		}

		switch idx {
		case 0:
			cardInfoItem.InfoType = deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_CREDIT_LIMIT
			cardInfoItem.HyperLinkSubTitle = &deeplink.TextWithHyperlinks{
				Text:         typesPkg.NewText(infoItem.DescriptionText, ""),
				HyperlinkMap: nil,
			}
		case 1:
			cardInfoItem.InfoType = deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_BILL_GENERATION
			cardInfoItem.HyperLinkSubTitle = &deeplink.TextWithHyperlinks{
				Text:         typesPkg.NewText(infoItem.DescriptionText, ""),
				HyperlinkMap: nil,
			}
		case 2:
			cardInfoItem.InfoType = deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_WELCOME_OFFER
			cardInfoItem.HyperLinkSubTitle = &deeplink.TextWithHyperlinks{
				Text:         typesPkg.NewText(infoItem.DescriptionText, ""),
				HyperlinkMap: nil,
			}
		case 3:
			cardInfoItem.InfoType = deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_FEE_INFO
			cardInfoItem.HyperLinkSubTitle = &deeplink.TextWithHyperlinks{
				Text:         typesPkg.NewText(infoItem.DescriptionText, ""),
				HyperlinkMap: nil,
			}
		default:
		}

		// if there is skipped bill gen date capture stage then tooltip for InfoType_CARD_ACTIVATION_INFO_TYPE_BILL_GENERATION is nil and
		// VisualElementCta will be populated
		if cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSkipBillGenDateCaptureStage() &&
			cardInfoItem.GetInfoType() == deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_BILL_GENERATION {
			cardInfoItem.Tooltip = nil
			cardInfoItem.IconCta = &deeplink.VisualElementCta{
				Icon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/edit_pencil_gray_4x.png"},
						},
					},
				},
				Deeplink: GetBillGenBottomSheetDeeplink("", false),
			}
		}

		switch {
		case cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSkipBillGenDateCaptureOnActivationScreen() &&
			cardInfoItem.GetInfoType() == deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_BILL_GENERATION:
			continue
		case !cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSkipBillGenDateCaptureOnActivationScreen() &&
			cardInfoItem.GetInfoType() == deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_FEE_INFO:
			continue
		}
		cardInfoItems = append(cardInfoItems, cardInfoItem)
	}
	screenOptions := &deeplink.FireflyCardActivationScreenOptions{
		CardImageUrl: "https://epifi-icons.pointz.in/credit_card_images/card_image_no_name.png",
		Text:         "Congrats! Your Fi-Federal Co-branded Credit Card is ready",
		SubText:      "Start using your card instantly to shop online & get up to 5% valueback",
		BillDates: &deeplink.InfoBlock{InfoItems: []*deeplink.InfoItem{
			{
				Title: "Credit limit: ₹",
				Desc:  money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, false),
				ToolTip: &deeplink.InfoToolTip{
					Info: &deeplink.InfoBlock{
						Title: "Credit limit",
						Desc:  "A credit limit is the maximum amount of credit offered to a customer. It is a factor determined based on consumers' credit scores and can impact their ability to get credit in the future",
					},
				},
			},
			{
				Title: "Bill generation: ",
				Desc:  fmt.Sprintf("%d%s", cardRequest.GetRequestDetails().GetBillGenDate(), getDateSuffix(int(cardRequest.GetRequestDetails().GetBillGenDate()))),
				ToolTip: &deeplink.InfoToolTip{
					Info: &deeplink.InfoBlock{
						Title: "Bill generation",
						Desc:  "The bill generation date is the date on\nwhich your credit card statement is\ngenerated every month.",
					},
				},
			},
		}},
		CardImage: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: confScreenOptionsCopy.CardImageUrl},
				Properties: &commontypes.VisualElementProperties{
					Width:  216,
					Height: 134,
				},
			},
		}},
		Heading:       typesPkg.NewText(confScreenOptionsCopy.Heading, ""),
		SubHeading:    typesPkg.NewText(confScreenOptionsCopy.SubHeading, ""),
		CardInfoItems: cardInfoItems,
		Tnc: fmt.Sprintf(confScreenOptionsCopy.Tnc, money.ToDisplayStringInIndianFormat(firefly.JoiningFee, 0, true)+"+GST",
			confScreenOptionsCopy.ImportantTncUrl, confScreenOptionsCopy.KeyFactStatementTncUrl, confScreenOptionsCopy.CreditCardTncUrl),
		ActivateDigitalCardCta: &deeplink.Cta{
			Type:         deeplink.Cta_DONE,
			Text:         confScreenOptionsCopy.ActivateDigitalCardCtaText,
			Deeplink:     GetCreateCardCtaDeeplink(cardRequest.GetId()),
			DisplayTheme: deeplink.Cta_PRIMARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		},
		CardRequestId:         cardRequest.GetId(),
		DefaultBillGenDate:    defaultBillGenDate,
		DefaultPaymentDueDate: defaultPaymentDueDate,
		VeBankLogo: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/issued_by_bank_logo.png"},
				Properties: &commontypes.VisualElementProperties{
					Width:  112,
					Height: 30,
				},
			},
		}},
	}
	return &deeplink.Deeplink{
		Screen:        deeplink.Screen_FIREFLY_CARD_ACTIVATION_SCREEN,
		ScreenOptions: &deeplink.Deeplink_FireflyCardActivationScreenOptions{FireflyCardActivationScreenOptions: screenOptions},
	}, nil
}

func GetBillGenBottomSheetDeeplink(cardId string, isUpdate bool) *deeplink.Deeplink {
	return deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_CC_BILL_GENERATION_DATE_SELECTION_SCREEN, &ffScreenTypes.CcBillGenerationDateSelectionScreenOptions{
		Header:   &deeplink_screen_option.ScreenOptionHeader{},
		CardId:   cardId,
		IsUpdate: isUpdate,
	})
}
func GetCreateCardCtaDeeplink(cardRequestId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_CARD_CREATION_SCREEN,
		ScreenOptions: &deeplink.Deeplink_FireflyCreateCardScreenOptions{
			FireflyCreateCardScreenOptions: &deeplink.CreditCardCreateCardScreenOptions{CardRequestId: cardRequestId}},
	}
}

func GetRetryableFailureDeeplink(redirectAction *deeplink.Deeplink, cardProgram *types.CardProgram) *deeplink.Deeplink {
	details := getDetailsByCardProgram(cardProgram)

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
			IconUrl: "https://epifi-icons.pointz.in/credit_card_images/credit_card_no_offer.png",
			Text:    "Bear with us",
			SubText: "Sorry! It seems that we have run into some issues at our end. Please retry.",
			Cta: &deeplink.Cta{
				Text:     "Restart my application",
				Deeplink: redirectAction,
			},
			Details: details.GetInfoItems(),
		}},
	}
}

// getAppUpdateDeeplink generates a deeplink for prompting the user to update the app based on the given platform.
func getAppUpdateDeeplink(platform commontypes.Platform) *deeplink.Deeplink {
	// Initialize a CTA (Call to Action) for the update.
	var updateCta *deeplink.Cta

	// Check if the platform is iOS.
	if platform == commontypes.Platform_IOS {
		// If it is, set the update CTA with specific text and deeplink.
		updateCta = &deeplink.Cta{
			Text:     "Update Now",
			Deeplink: &deeplink.Deeplink{Screen: deeplink.Screen_UPDATE_APP_SCREEN},
		}
	}

	// Return the deeplink with options for the generic credit card halt screen.
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/ufo_ladder_image.png",
				Text:    "Update your Fi app to continue",
				SubText: "We've rolled out some new fixes that are necessary to avail a credit card. Note: It could take 48 hours for a new version to be available.",
				Cta:     updateCta,
			},
		},
	}
}

func GetPanAadhaarVerifyFailureDeeplink(redirectAction *deeplink.Deeplink, cardProgram *types.CardProgram) *deeplink.Deeplink {
	details := getDetailsByCardProgram(cardProgram)
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
			IconUrl: "https://epifi-icons.pointz.in/credit_card_images/alert_circle.png",
			Text:    "Uh oh! Your PAN & Aadhaar are not linked ",
			SubText: "As per regulations, we cannot offer you a Credit Card if your PAN & Aadhaar are not linked. You can retry once you’ve linked them.",
			Cta: &deeplink.Cta{
				Text:     "Retry",
				Deeplink: redirectAction,
			},
			Details: details.GetInfoItems(),
		}},
	}
}

func getDetailsByCardProgram(cardProgram *types.CardProgram) *deeplink.InfoBlock {
	switch cardProgram.GetCardProgramType() {
	case types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
		return securedCardDetails
	case types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
		return massUnsecuredCardDetails
	default:
		return cardDetails
	}
}

func GetFiLiteCardCreationSyncPollDeeplink(ctx workflow.Context, cardRequestId string) *deeplink.Deeplink {
	screenOptions := &ffScreenTypes.FireflySyncPollStatusScreenOptions{
		Header:         &deeplink_screen_option.ScreenOptionHeader{},
		CardRequestId:  cardRequestId,
		DisplayMessage: "Hold on! Your Credit Card is getting created",
		RetryDelay:     1000,
		WorkflowId:     workflow.GetInfo(ctx).WorkflowExecution.ID,
		ScreenImage: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/card_img.png"},
				Properties: &commontypes.VisualElementProperties{
					Width:  214,
					Height: 135,
				},
				ImageType: commontypes.ImageType_PNG,
			},
		}},
		BgColor: "#FFFFFF",
		DisplayMessageObject: &commontypes.Text{
			FontColor:    "#333333",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Hold on! Your Credit Card is getting created"},
		},
	}
	return deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_FIREFLY_SYNC_POLL_STATUS_SCREEN, screenOptions)
}

func GetOnboardingRetryAttemptHaltDeeplink(isUserFiLite bool, redirectAction *deeplink.Deeplink) *deeplink.Deeplink {
	// TODO(akk) - revert text and retry cta changes
	switch {
	case isUserFiLite:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
			ScreenOptions: &deeplink.Deeplink_DetailedErrorViewScreenOptions{
				DetailedErrorViewScreenOptions: &deeplink.DetailedErrorViewScreenOptions{
					TitleText: &commontypes.Text{
						FontColor:    "#333333",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Your application is in progress"},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_1},
					},
					SubtitleText: &commontypes.Text{
						FontColor:    "#8D8D8D",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "We are processing your request. Please come back in a few minutes. "},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3},
					},
					Ctas: []*deeplink.Cta{
						{
							Text:     "Retry",
							Deeplink: redirectAction,
							Type:     deeplink.Cta_CUSTOM,
						},
					},
					Image: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/manual_review_img.png"},
								Properties: &commontypes.VisualElementProperties{
									Width:  220,
									Height: 180,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
					BgColour: nil,
				},
			},
		}
	default:
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/digging_dog.png",
				Text:    "This is taking longer than expected",
				SubText: "Please come back in a few minutes.",
				Cta: &deeplink.Cta{
					Text: "Back to home",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_HOME,
						ScreenOptions: &deeplink.Deeplink_HomeScreenOptions{
							HomeScreenOptions: &deeplink.HomeScreenOptions{}},
					},
				},
			}},
		}
	}
}

func GetDedupeErrorDL(context context.Context, dedupeStatus customer.DedupeStatus) *deeplink.Deeplink {
	// TODO(Swapnil) add error screen details
	return nil
}

func GetNonRetryableFailureDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/HL_main_card_with_coin.png",
				Text:    "You are not eligible for a Credit Card at the moment",
				SubText: "Few reasons why this may have happened:" +
					"<br>1. Low credit score or poor credit history on other loans/cards." +
					"<br>2. We may not be offering cards at your location currently." +
					"<br>3. There are age restrictions which may apply." +
					"<br>4. Other active relationships with our partner bank." +
					"<br>These reasons are only some possible reasons why you are not yet eligible for a credit card. Please note that there may be other reasons as per bank policy.",
			},
		},
	}
}

func GetManualReviewDeeplink(cardProgram *types.CardProgram) *deeplink.Deeplink {
	details := getDetailsByCardProgram(cardProgram)
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
			IconUrl: "https://epifi-icons.pointz.in/credit_card_images/manual_review_img.png",
			Text:    "Your video has been submitted for review",
			SubText: "Sit tight and we’ll notify you once video verification is complete in 3 business days",
			Details: details.GetInfoItems(),
		}},
	}
}

func GetCreditCardDashboardDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardDashboardScreenOptions{
			CreditCardDashboardScreenOptions: &deeplink.CreditCardDashboardScreenOptions{}},
	}
}

func GetBiometricRevalidationDeeplink(creditCardId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/selfi_video.png",
				Text:    "We noticed a change in your device unlock method",
				SubText: "You understand and agree that your device unlock methods are used to unlock the Fi App and features within it. To verify your profile take a quick selfie video and continue.",
				Cta: &deeplink.Cta{
					Text: "Verify Now",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_INITIATE_CARD_REQUEST_SCREEN,
						ScreenOptions: &deeplink.Deeplink_InitiateCardRequestScreenOptions{
							InitiateCardRequestScreenOptions: &deeplink.InitiateCardRequestScreenOptions{
								CardId:   creditCardId,
								Workflow: ffFeEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_BIOMETRIC_REVALIDATION,
							},
						},
					},
				},
			},
		},
	}
}

func GetBiometricRevalidationFailureDeeplink(creditCardId string) *deeplink.Deeplink {
	res := GetGenericHaltScreenDeeplink(
		"Profile could not be verified",
		"Uh-oh! We faced an issue while doing this. Something’s up at our end. Please try again.",
	)
	res.GetCreditCardHaltScreenOptions().Cta.Deeplink = &deeplink.Deeplink{
		Screen: deeplink.Screen_INITIATE_CARD_REQUEST_SCREEN,
		ScreenOptions: &deeplink.Deeplink_InitiateCardRequestScreenOptions{
			InitiateCardRequestScreenOptions: &deeplink.InitiateCardRequestScreenOptions{
				CardId:   creditCardId,
				Workflow: ffFeEnumsPb.CardRequestWorkflow_CARD_REQUEST_WORKFLOW_BIOMETRIC_REVALIDATION,
			},
		},
	}
	return res
}

func GetGenericHaltScreenDeeplink(text, subText string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/halt-screen-icon.png",
				Text:    text,
				SubText: subText,
				Cta: &deeplink.Cta{
					Type:         deeplink.Cta_RETRY,
					Text:         constants.RetryText,
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		},
	}
}

func GetPanAadhaarHaltScreenDeeplink(text, subText, imageUrl string, showRetryCta bool) *deeplink.Deeplink {
	var cta *deeplink.Cta
	if showRetryCta {
		cta = &deeplink.Cta{
			Type:         deeplink.Cta_RETRY,
			Text:         constants.RetryText,
			DisplayTheme: deeplink.Cta_PRIMARY,
		}
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: imageUrl,
				Text:    text,
				SubText: subText,
				Cta:     cta,
			},
		},
	}
}

func GetCreditCardWaitlistInquiryDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CARD_OFFERS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_GetOffersScreenOptions{
			GetOffersScreenOptions: &deeplink.GetOffersScreenOptions{
				CardImageUrl:   "https://epifi-icons.pointz.in/credit_card_images/CardImage.png",
				PartnershipUrl: "https://epifi-icons.pointz.in/credit_card_images/PartnerIcon.png",
				InfoBlock:      cardDetails,

				TncString: fmt.Sprintf("Read Federal Bank’s <a href=%s> Most Important Terms & Conditions</a>, <a href=%s>Key Fact Statement</a> and <a href=%s>Terms & Conditions</a>", "https://fi.money/credit-card/important-T&Cs", "https://fi.money/credit-card/key-fact-statement", "https://fi.money/credit-card/T&Cs"),

				GetCreditCard: &deeplink.Cta{
					Text:     "Hop on the waitlist",
					Deeplink: GetCreditCardWaitlistConfirmDeeplink(),
				},
				KnowMore: &deeplink.Cta{
					Text:     "Know More",
					Deeplink: GetCcBenefitsDeeplink(),
				},
			},
		},
	}
}

func GetCreditCardWaitlistConfirmDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/tilted_card_image.png",
				Text:    "You’re on the waitlist!",
				SubText: "Your Fi-Federal Credit Card will be delivered to this address",
				Cta: &deeplink.Cta{
					Text:     "View card benefits",
					Deeplink: GetCcBenefitsDeeplink(),
				},
			},
		},
	}
}

func GetCCRewardsPollingDeeplink(rewardOfferType ffFeEnumsPb.CCRewardOfferType, clientReqId, cardReqId, displayMessage string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_REWARDS_POLLING_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CardRewardsPollingScreenOptions{
			CardRewardsPollingScreenOptions: &deeplink.CreditCardRewardsPollingScreenOptions{
				CardRequestId:   cardReqId,
				ClientRequestId: clientReqId,
				RewardOfferType: rewardOfferType,
				DisplayMessage:  displayMessage,
			},
		},
	}
}

func GetCCRewardsSelectionDeeplink(attributeScreenOptions *deeplink.Deeplink_CardRewardsSelectionScreenOptions) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen:        deeplink.Screen_CREDIT_CARD_REWARDS_SELECTION_SCREEN,
		ScreenOptions: attributeScreenOptions,
	}
}

func getAllBrands() []*deeplink.InfoItem {
	allBrands := make([]*deeplink.InfoItem, 0)
	for merchantName, merchantIcon := range firefly.GetAllMerchantToMerchantIconMappings() {
		allBrands = append(allBrands, &deeplink.InfoItem{
			Icon:  merchantIcon,
			Title: merchantName,
		})
	}
	return allBrands
}

func GetFiBrandsScreenCta() *deeplink.Cta {
	return &deeplink.Cta{
		Text: "Know More",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_INTRO_SCREEN_BRANDS,
			ScreenOptions: &deeplink.Deeplink_CreditCardIntroScreenBrandScreenOptions{
				CreditCardIntroScreenBrandScreenOptions: &deeplink.CreditCardIntroScreenBrandScreenOptions{
					RewardDescriptions: &deeplink.InfoBlock{
						Title: "Rewards that evolve to match your spends",
						InfoItems: []*deeplink.InfoItem{
							{
								Icon:  "https://epifi-icons.pointz.in/credit_card_images/1x_image.png",
								Title: "On all spends, including fuel",
								Desc:  "Get 1 Fi-Coin for every ₹5 spent",
							},
							{
								Icon:  "https://epifi-icons.pointz.in/credit_card_images/2x_image.png",
								Title: "On brands listed in the Fi Collection",
								Desc:  "Get 2 Fi-Coins for every ₹5 spent",
							},
							{
								Icon:  "https://epifi-icons.pointz.in/credit_card_images/5x_reward_img.png",
								Title: "On your top 3 brands from the Fi Collection",
								Desc:  "Get 5 Fi-Coins for every ₹5 spent",
							},
						},
					},
					TopBrandsImage: "https://epifi-icons.pointz.in/credit_card_images/top_brands_2.png",
					AllBrandList: &deeplink.InfoBlock{
						Title:     "The Fi Collection",
						Desc:      "An ever-expanding list of India’s top brands",
						InfoItems: getAllBrands(),
					},
					BrandsLottie: &commontypes.VisualElement{
						Asset: &commontypes.VisualElement_Image_{
							Image: &commontypes.VisualElement_Image{
								Source: &commontypes.VisualElement_Image_Url{
									Url: "https://epifi-icons.pointz.in/credit_card_images/top_brands_2.png",
								},
								Properties: &commontypes.VisualElementProperties{
									Width:  200,
									Height: 290,
								},
								ImageType: commontypes.ImageType_PNG,
							},
						},
					},
				},
			},
		},
	}
}

func GetCcBenefitsDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_BENEFITS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardBenefitsScreenOptions{
			CreditCardBenefitsScreenOptions: &deeplink.CreditCardBenefitsScreenOptions{
				HeaderIconUrl: "https://epifi-icons.pointz.in/credit_card_images/the_5x_card.png",
				Benefits:      ccBenefits,
				Faq: &deeplink.InfoItemWithCta{
					Info: &deeplink.InfoItem{
						Icon:  "https://epifi-icons.pointz.in/preapprovedloan/landinginfo/landing-learn-more.png",
						Title: "Frequently asked questions",
					},
					Cta: &deeplink.Cta{
						Text: "Frequently asked questions",
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_CARD_KNOW_MORE_SCREEN,
						},
					},
				},
			},
		},
	}
}

func GetAddressSelectionScreenDeeplink(customerName *commontypes.Name, addressSelectionStepNumber, ccTotalSteps int64, cardRequestId, cardDeliveryScreenIconUrl string, canAddAddress bool) *deeplink.Deeplink {
	if canAddAddress {
		return &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_ADDRESS_SELECTION_SCREEN,
			ScreenOptions: &deeplink.Deeplink_FireflySetDeliveryAddressScreenOptions{FireflySetDeliveryAddressScreenOptions: &deeplink.FireflySetDeliveryAddressScreenOptions{
				CardRequestId: cardRequestId,
				Text:          "Confirm card delivery address",
				SubText:       fmt.Sprintf("Your %s Credit Card will be delivered to this address", firefly.CreditCardType),
				StepInfo: &deeplink.StepInfo{
					CurrentStep: addressSelectionStepNumber,
					TotalSteps:  ccTotalSteps,
				},
				DeliveryAddressIconUrl: cardDeliveryScreenIconUrl,
				CustomerName:           customerName,
				IsAddAddressEnabled:    canAddAddress,
			}},
		}
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_ADDRESS_SELECTION_SCREEN,
		ScreenOptions: &deeplink.Deeplink_FireflySetDeliveryAddressScreenOptions{FireflySetDeliveryAddressScreenOptions: &deeplink.FireflySetDeliveryAddressScreenOptions{
			CardRequestId: cardRequestId,
			Text:          "Confirm card delivery address",
			SubText:       fmt.Sprintf("Your %s Credit Card will be delivered to this address", firefly.CreditCardType),
			StepInfo: &deeplink.StepInfo{
				CurrentStep: addressSelectionStepNumber,
				TotalSteps:  ccTotalSteps,
			},
			DeliveryAddressIconUrl: cardDeliveryScreenIconUrl,
			CustomerName:           customerName,
			IsAddAddressEnabled:    canAddAddress,
			FooterText:             "Only the addresses present on your KYC details with our partner bank can be selected. If you wish to change your delivery address, update your Aadhaar details on the UIDAI website and request a KYC update through customer support.",
		}},
	}
}

// nolint:funlen
func GetAddressSelectionScreenDeeplinkV2(ctx context.Context, flags *worker.Flags, cardRequest *ccFireflyPb.CardRequest,
	canAddAddress bool, kycName *commontypes.Name, genConf *workerGenConf.Config) (*deeplink.Deeplink, error) {
	var (
		headerImgUrl string
	)
	addressSelectionScreenConfig := genConf.Screens().AddressSelectionScreenOptions()

	switch {
	case firefly.IsCreditCardProgramSecured(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardProgram()):
		addressSelectionScreenConfig = genConf.Screens().SecuredAddressSelectionScreenOptions()
		headerImgUrl = addressSelectionScreenConfig.HeaderImageUrl(ctx)
	case firefly.IsCreditCardProgramMassUnsecured(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardProgram()):
		headerImgUrl = goldenAddressSelectPinIcon
	default:
		headerImgUrl = addressSelectionScreenConfig.HeaderImageUrl(ctx)
	}

	if !isAddressV2Enabled(ctx, flags) {
		return GetAddressSelectionScreenDeeplink(kycName, int64(addressSelectionScreenConfig.AddressSelectionStepNumber(ctx)),
			int64(addressSelectionScreenConfig.TotalSteps(ctx)), cardRequest.GetId(), headerImgUrl, canAddAddress), nil
	}
	// we are sending add address info item (AddAddressCtaV2) value in a platform specific manner because of different handling on ios and android platforms
	platform, _ := epificontext.AppPlatformAndVersion(ctx)
	screenOptions := &ffScreenTypes.CreditCardAddressSelectionV2ScreenOptions{
		StepInfo: &deeplink.StepInfo{
			CurrentStep: int64(addressSelectionScreenConfig.AddressSelectionStepNumber(ctx)),
			TotalSteps:  int64(addressSelectionScreenConfig.TotalSteps(ctx)),
		},
		ContinueCta: &deeplink.Cta{
			Type: deeplink.Cta_CONTINUE,
			Text: addressSelectionScreenConfig.ContinueCtaText(ctx),
		},
		CardRequestId: cardRequest.GetId(),
		HeaderDetails: &deeplink.InfoItemV3{
			Icon: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: headerImgUrl,
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  addressSelectionScreenConfig.HeaderImageWidth(ctx),
						Height: addressSelectionScreenConfig.HeaderImageHeight(ctx),
					},
					ImageType: commontypes.ImageType_PNG,
				},
			}},
			Title: typesPkg.NewText(addressSelectionScreenConfig.HeaderText(), ""),
			Desc:  typesPkg.NewText(addressSelectionScreenConfig.HeaderSubText(), ""),
		},
		Text:                   "Select card delivery address",
		DeliveryAddressIconUrl: headerImgUrl,
	}
	if canAddAddress {
		screenOptions.AddAddressCta = &deeplink.InfoItemWithCtaV2{
			Info: &deeplink.InfoItemV2{
				Icon: "",
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Add address"},
				},
			},
		}
		screenOptions.AddAddressCtaV2 = &deeplink.InfoItemWithCtaV3{
			Info: &deeplink.InfoItemV3{
				Icon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: addressSelectionScreenConfig.AddAddressCtaImageUrl(ctx),
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  addressSelectionScreenConfig.AddAddressCtaImageWidth(ctx),
								Height: addressSelectionScreenConfig.AddAddressCtaImageHeight(ctx),
							},
							ImageType: commontypes.ImageType_PNG,
						},
					},
				},
				Title: typesPkg.NewText(addressSelectionScreenConfig.AddAddressCtaText(), ""),
			},
			Cta: &deeplink.Cta{
				Status: deeplink.Cta_CTA_STATUS_ENABLED,
			},
		}
	} else if platform == commontypes.Platform_ANDROID {
		screenOptions.AddAddressCtaV2 = &deeplink.InfoItemWithCtaV3{
			Cta: &deeplink.Cta{
				Status: deeplink.Cta_CTA_STATUS_DISABLED,
			},
		}
	}

	dl, err := deeplinkV3.GetDeeplinkV3(deeplink.Screen_CREDIT_CARD_ADDRESS_SELECTION_V2_SCREEN, screenOptions)
	if err != nil {
		return nil, errors.Wrap(err, "error getting deeplink v2")
	}

	return dl, nil
}

func GetCreditCardPollingScreen(cardRequestId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_FIREFLY_GET_REQUEST_STATUS,
		ScreenOptions: &deeplink.Deeplink_FireflyGetRequestStatusScreenOptions{
			FireflyGetRequestStatusScreenOptions: &deeplink.FireflyGetRequestStatusScreenOptions{
				CardRequestId: cardRequestId,
			},
		},
	}
}

func GetCreditCardPollingScreenWithDisplayMessage(cardRequestId, displayMessage string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_FIREFLY_GET_REQUEST_STATUS,
		ScreenOptions: &deeplink.Deeplink_FireflyGetRequestStatusScreenOptions{
			FireflyGetRequestStatusScreenOptions: &deeplink.FireflyGetRequestStatusScreenOptions{
				CardRequestId:  cardRequestId,
				DisplayMessage: displayMessage,
			},
		},
	}
}

func GetRewardConfirmationDeeplink(rewardType ffEnumsPb.CCRewardType, cardReqId, postCtaDisplayMessage string, ctaAction *deeplink.Deeplink, config *ffGenConf.Config) *deeplink.Deeplink {
	if ctaAction == nil {
		ctaAction = GetCreditCardPollingScreenWithDisplayMessage(cardReqId, postCtaDisplayMessage)
	}
	switch rewardType {
	case ffEnumsPb.CCRewardType_CC_REWARD_TYPE_GIFT_VOUCHERS:
		if config.EnableNewCvpForUnsecuredCreditCard() {
			return getCreditCardSuccessConfirmationScreen(ctaAction, "Tada! Vouchers confirmed",
				&deeplink.InfoItem{
					Icon:  "https://epifi-icons.pointz.in/credit_card_images/locked_vouchers_img.png",
					Title: "₹4000+",
					Desc:  "worth of welcome gift cards for you",
				})
		}
		return getCreditCardSuccessConfirmationScreen(ctaAction, "Tada! Offer confirmed",
			&deeplink.InfoItem{
				Icon:  "https://epifi-icons.pointz.in/credit_card_images/locked_vouchers_img.png",
				Title: "₹5,000",
				Desc:  "worth of welcome vouchers for you",
			})
	case ffEnumsPb.CCRewardType_CC_REWARD_TYPE_FI_COINS:
		return getCreditCardSuccessConfirmationScreen(ctaAction, "Tada! Offer confirmed", &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/locked_fi_coin_img.png",
			Title: "50,000",
			Desc:  "Fi-Coins",
		})
	default:
		return GetCreditCardPollingScreenWithDisplayMessage(cardReqId, postCtaDisplayMessage)
	}
}
func getCreditCardSuccessConfirmationScreen(ctaAction *deeplink.Deeplink, title string, rewardInfo *deeplink.InfoItem) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_SUCCESS_CONFIRMATION_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardSuccessConfirmationScreen{
			CreditCardSuccessConfirmationScreen: &deeplink.CreditCardSuccessConfirmationScreen{
				Title:      title,
				RewardInfo: rewardInfo,
				Description: &commontypes.Text{
					BgColor:      "#383838",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "This offer will only be unlocked when you pay minimum due of first bill of your Fi-Federal Co-branded Credit Card."},
				},
				Cta: &deeplink.Cta{
					Text:     "Continue",
					Deeplink: ctaAction,
				},
			},
		},
	}
}
func GetBillingDaysSubmitDeeplink(cardRequestId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_BILL_DATES_SELECTION_SCREEN,
		ScreenOptions: &deeplink.Deeplink_FireflyBillDateSelectionScreenOptions{
			FireflyBillDateSelectionScreenOptions: &deeplink.CreditCardBillDatesSelectionScreenOptions{
				StepInfo: &deeplink.StepInfo{
					// TODO(Abhishek) clarify from product
					CurrentStep: 0,
					TotalSteps:  0,
				},
				DateOptions: nil,
				ContinueCta: &deeplink.Cta{
					Type: deeplink.Cta_CONTINUE,
					Text: "Continue",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CREDIT_CARD_SUBMIT_BILLING_DATE_SCREEN,
						ScreenOptions: &deeplink.Deeplink_CreditCardSubmitBillingDatesScreenOptions{
							CreditCardSubmitBillingDatesScreenOptions: &deeplink.CreditCardSubmitBillingDatesScreenOptions{
								CardRequestId: cardRequestId}},
					},
					DisplayTheme: deeplink.Cta_DISPLAY_THEME_DELAY_ENABLE,
					Status:       deeplink.Cta_CTA_STATUS_ENABLED,
				},
				CardRequestId: cardRequestId,
			}},
	}
}

func GetVkycPendingScreen(_ context.Context) *deeplink.Deeplink {
	dl, _ := vkyc.BuildVKYCStatusDeeplink(&vkyc.StatusScreenOptions{
		VKYCEligibleScreenDeeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_VKYC_LANDING_SCREEN,
			ScreenOptions: &deeplink.Deeplink_CreditCardVkycLandingScreenOptions{
				CreditCardVkycLandingScreenOptions: &deeplink.CreditCardVKYCLandingScreenOptions{
					StepInfo: &deeplink.StepInfo{
						CurrentStep: 4,
						TotalSteps:  4,
					},
					LogoUrl:  "https://epifi-icons.pointz.in/credit_card_images/vkyc_image.png",
					Title:    "Complete a quick KYC video call",
					SubTitle: "Required to access our credit card — plus it unlocks lifetime account validity & removes all transfer/deposit limits.",
					KycButton: &deeplink.Cta{
						Type: 0,
						Text: "Complete KYC now",
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_GET_VKYC_NEXT_ACTION_API,
							ScreenOptions: &deeplink.Deeplink_GetVkycNextActionApiScreenOptions{
								GetVkycNextActionApiScreenOptions: &deeplink.GetVKYCNextActionApiScreenOptions{
									EntryPoint:      vkycBe.EntryPoint_ENTRY_POINT_CREDIT_CARD.String(),
									ClientLastState: vkycBe.VKYCClientState_VKYC_CLIENT_STATE_INITIATED.String(),
								}},
						},
					},
				},
			},
		},
		EntryPoint: vkycBe.EntryPoint_ENTRY_POINT_CREDIT_CARD,
	})
	return dl
}

func GetCreditCardControlsScreenDeeplink(cardId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_CONTROLS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardControlsScreenOptions{
			CreditCardControlsScreenOptions: &deeplink.CreditCardControlsScreenOptions{
				CreditCardId: cardId,
			},
		},
	}
}

func GetYourEmiScreenDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_EMI_DASHBOARD_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardEmiDashboardScreenOptions{
			CreditCardEmiDashboardScreenOptions: &deeplink.CreditCardEmiDashboardScreenOptions{
				EmiCalculator: &deeplink.InfoItemWithCta{
					Info: &deeplink.InfoItem{
						Icon:  "https://epifi-icons.pointz.in/credit_card_images/pieChart.png",
						Title: "Calculate your EMI for your upcoming expenses",
						Desc:  "https://fi.money/calculators/personal-loan-emi-calculator",
					},
					Cta: &deeplink.Cta{},
				},
			},
		},
	}
}

func GetCustomReminderDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_REMINDERS_CONFIG_SCREEN,
		ScreenOptions: &deeplink.Deeplink_RemindersConfigScreenOptions{
			RemindersConfigScreenOptions: &deeplink.RemindersConfigScreenOptions{
				ConfigType:   meta.ReminderConfigType_CONFIG_CREATE,
				ReminderType: meta.ReminderType_CREDIT_CARD_DUE_DATE,
				ConfigFlow: &deeplink.RemindersConfigScreenOptions_CreateConfig{
					CreateConfig: &deeplink.CreateReminderConfig{
						Type: meta.ReminderType_CREDIT_CARD_DUE_DATE,
					},
				},
				// https://docs.google.com/spreadsheets/d/16trD2CRrJnq75uA0CAf61CnrFkot8NtBEwRtux8rz1g/edit#gid=0
				Source: "CCTrayReminder",
			},
		},
	}
}

func GetEmiInfoToolTip() *deeplink.InfoToolTip {
	return &deeplink.InfoToolTip{
		Info: &deeplink.InfoBlock{
			Title: "What is EMI?",
			Desc:  "EMI stands for \"Equated Monthly Instalment.\"\n\nIt's a payment option that allows you to pay for your purchases in monthly instalments, instead of paying the entire amount at once.\n\nThe EMI you pay is dependent on the interest rate and the duration you have selected.",
		},
	}
}

func GetEmiInfoToolTipV2() *deeplink.InfoToolTipV2 {
	return &deeplink.InfoToolTipV2{
		Icon: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/credit_card_images/information_grey_4x.png",
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  20,
						Height: 20,
					},
				},
			},
		},
		Info: &deeplink.InfoBlockV2{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "What is EMI?"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_2,
				},
			},
			InfoItems: []*deeplink.InfoItemV2{
				{
					Title: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "EMI stands for \"Equated Monthly Instalment\".",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_3,
						},
						FontColor: "#8D8D8D",
					},
				},
				{
					Title: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "It's a payment option that allows you to pay for your purchases made through your credit card in monthly instalments, instead of paying the entire amount at once.",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_3,
						},
						FontColor: "#8D8D8D",
					},
				},
				{
					Title: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "The EMI you pay is dependent on the interest rate and the duration you have selected.",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_BODY_3,
						},
						FontColor: "#8D8D8D",
					},
				},
			},
			DialogIcon: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/credit_card_images/green_i_icon_with_green_bg.png",
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						},
					},
				},
			},
		},
	}
}

func GetClosureCta() *deeplink.Cta {
	// TODO: Add the Closure Certificate implementation cta if web view needed over here
	return nil

}

func GetClosureScreenDeeplink(loanAccountId, merchantLogo, merchantName, merchantBgColour string, emiAmount, payableAmount *moneyPb.Money, emiClosureDate, nextBillGenerationDate *date.Date, accountStatus lms.LoanAccountStatus, emiFlowType ffFeEnumsPb.EmiFlowType) *deeplink.Deeplink {
	var merchantLogoVisualElement *commontypes.VisualElement

	title, subTitle := getClosedEmiTitleAndSubtitle(emiClosureDate, nextBillGenerationDate, payableAmount, accountStatus, emiFlowType)
	if merchantLogo != "" {
		merchantLogoVisualElement = &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: merchantLogo,
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  40,
						Height: 40,
					},
				},
			},
		}
	}

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_EMI_PRE_CLOSE_LOAN_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardEmiPreCloseScreenOptions{
			CreditCardEmiPreCloseScreenOptions: &deeplink.CreditCardEmiPreCloseScreenOptions{
				LoanAccountId:         loanAccountId,
				MerchantLogo:          merchantLogo,
				MerchantName:          merchantName,
				EmiAmount:             types.GetFromBeMoney(emiAmount),
				Title:                 title,
				SubTitle:              subTitle,
				ClosureCertificateCta: GetClosureCta(),
				MerchantInfo: &deeplink.InfoItemV3{
					Title: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: merchantName,
						},
						BgColor: merchantBgColour,
					},
					Icon: merchantLogoVisualElement,
					SubTitle: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: fmt.Sprintf("%s / month", money.ToDisplayStringInIndianFormat(emiAmount, 0, true)),
						},
					},
				},
				TitleDescription: &commontypes.TitleDescriptionComponent{
					Title: typesPkg.NewText(&typesPkg.Text{
						FontColor:         "#333333",
						PlainString:       title,
						StandardFontStyle: "SUBTITLE_L",
					}, ""),
					Description: typesPkg.NewText(&typesPkg.Text{
						FontColor:         "#646464",
						PlainString:       subTitle,
						StandardFontStyle: "BODY_3_PARA",
					}, ""),
				},
				VeBankInfo: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.pointz.in/emi/bank_info_image.png",
							},
							Properties: &commontypes.VisualElementProperties{
								Width:  240,
								Height: 35,
							},
						},
					},
				},
			},
		},
	}
}

func getClosedEmiTitleAndSubtitle(emiClosureDate, nextBillGenerationDate *date.Date, payableAmount *moneyPb.Money, accountStatus lms.LoanAccountStatus, emiFlowType ffFeEnumsPb.EmiFlowType) (string, string) {
	switch {
	case accountStatus == lms.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CANCELLED || emiFlowType == ffFeEnumsPb.EmiFlowType_EMI_FLOW_TYPE_CANCEL_LOAN:
		return fmt.Sprintf("EMI cancelled on %s %s", integer.GetOrdinalSuffix(int(emiClosureDate.GetDay())), datetime.DateToString(emiClosureDate, "January, 2006", nil)),
			fmt.Sprintf("No cancellation fees charged.\n\nTotal payable amount of %s is added to your bill due on %s %s.", money.ToDisplayStringInIndianFormat(payableAmount, 2, true), integer.GetOrdinalSuffix(int(nextBillGenerationDate.GetDay())), datetime.DateToString(nextBillGenerationDate, "January, 2006", nil))
	case accountStatus == lms.LoanAccountStatus_LOAN_ACCOUNT_STATUS_PRE_CLOSED || emiFlowType == ffFeEnumsPb.EmiFlowType_EMI_FLOW_TYPE_PRE_CLOSE_LOAN:
		return fmt.Sprintf("EMI pre-closed on %s %s", integer.GetOrdinalSuffix(int(emiClosureDate.GetDay())), datetime.DateToString(emiClosureDate, "January, 2006", nil)),
			fmt.Sprintf("Total payable amount of %s is added to your bill due on %s %s.", money.ToDisplayStringInIndianFormat(payableAmount, 2, true), integer.GetOrdinalSuffix(int(nextBillGenerationDate.GetDay())), datetime.DateToString(nextBillGenerationDate, "January, 2006", nil))
	default:
		return "", ""
	}
}

func GetCreditCardPaymentStatusPollingScreen(paymentReqId, orderId string, retryAttemptNumber int32) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_PAYMENT_STATUS_POLLING_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardPaymentStatusPollingScreenOptions{
			CreditCardPaymentStatusPollingScreenOptions: &deeplink.CreditCardPaymentStatusPollingScreenOptions{
				RetryAttemptNumber: retryAttemptNumber,
				RetryDelay:         1000,
				PaymentReqId:       paymentReqId,
				OrderId:            orderId,
			},
		},
	}
}

func GetPaymentStatusNextAction(paymentStatus ffEnumsPb.PaymentStatus) *deeplink.Deeplink {
	var statusInfo *deeplink.InfoItem
	cta := &deeplink.Cta{
		Type:         deeplink.Cta_DONE,
		Text:         "Ok, got it",
		Deeplink:     GetCreditCardDashboardDeeplink(),
		DisplayTheme: deeplink.Cta_SECONDARY,
		Status:       deeplink.Cta_CTA_STATUS_ENABLED,
	}
	switch paymentStatus {
	case ffEnumsPb.PaymentStatus_PAYMENT_STATUS_SUCCESS:
		statusInfo = &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/Green_Tick_4x.png",
			Title: "Payment successful!",
		}
	case ffEnumsPb.PaymentStatus_PAYMENT_STATUS_FAILED:
		statusInfo = &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/Warning_Excalamation_4x.png",
			Title: "Payment failed",
			Desc:  "Don't worry, your money is safe. It will return to your account in 2-3 business days.",
		}
		cta.Text = "Retry"
		cta.Type = deeplink.Cta_CUSTOM
		// TODO ADD A SCREEN HERE LATER FOR UNSPECIFIED CASES..
	default:
		statusInfo = &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/Clock_4x.png",
			Title: "Processing payment",
			Desc:  "Your payment has been sent to our partner bank for processing. You will be notified once it's done.",
		}
	}

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_PAYMENT_STATUS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardPaymentStatusScreenOptions{
			CreditCardPaymentStatusScreenOptions: &deeplink.CreditCardPaymentStatusScreenOptions{
				StatusInfo: statusInfo,
				Cta:        cta,
			},
		},
	}
}

func GetPaymentInitiationFailedNextAction() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
			IconUrl: "https://epifi-icons.pointz.in/credit_card_images/halt_screen_alert_circle_4x.png",
			Text:    "Could not initiate payment",
			SubText: "Please come back in a few minutes",
			Cta: &deeplink.Cta{
				Text:     "Back to home",
				Deeplink: GetCreditCardDashboardDeeplink(),
			},
		}},
	}
}

func GetViewStatementCta(cardId string, statementDuration *deeplink.StatementDuration) *deeplink.Cta {
	return &deeplink.Cta{
		Type: deeplink.Cta_CUSTOM,
		Text: "",
		// TODO: Enable deeplink once data is back-filled.
		// Deeplink: &deeplink.Deeplink{
		//	Screen: deeplink.Screen_CREDIT_CARD_STATEMENT_VIEW_SCREEN,
		//	ScreenOptions: &deeplink.Deeplink_CreditCardStatementViewScreenOptions{
		//		CreditCardStatementViewScreenOptions: &deeplink.CreditCardStatementViewScreenOptions{
		//			StatementDuration: statementDuration,
		//			CardId:            cardId,
		//		},
		//	},
		// },
	}
}

// Deprecated: use ineligible screen instead
func GetBreLimitChangedErrorScreen(cardRequest *ccFireflyPb.CardRequest) *deeplink.Deeplink {
	var screenIdentifer int32
	if cardRequest.GetRequestDetails().GetCardProgram().GetCardProgramOrigin() == types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE {
		screenIdentifer = int32(deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API)
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_PRE_APPROVED_LOAN_ERROR_SCREEN,
		ScreenOptions: &deeplink.Deeplink_PreApprovedLoanErrorScreenOptions{
			PreApprovedLoanErrorScreenOptions: &deeplink.PreApprovedLoanErrorScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/alert_circle.png",
				Details: []*deeplink.InfoItem{
					{
						Title: "Please restart your credit card application",
						Desc:  "Your eligibility for the credit card has been updated. Please start a new application.",
					},
				},
				Cta: &deeplink.Cta{
					Type: deeplink.Cta_CUSTOM,
					Text: "Restart your application",
					Deeplink: deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_CC_INTRO_SCREEN,
						&ffScreenTypes.CcIntroScreenOptions{
							CardProgramType:  ffFeEnumsPb.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED,
							ScreenIdentifier: screenIdentifer,
						}),
					DisplayTheme: deeplink.Cta_PRIMARY,
					Status:       deeplink.Cta_CTA_STATUS_ENABLED,
				},
				BgColour: "#282828",
			},
		},
	}
}

func GetWaitlistIntroDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_IN_APP_WAITLIST_SCREEN,
		ScreenOptions: &deeplink.Deeplink_InAppWaitlistScreenOptions{InAppWaitlistScreenOptions: &deeplink.InAppWaitlistScreenOptions{
			WaitlistFeature: waitlist_enums.WaitlistFeature_WAITLIST_FEATURE_CREDIT_CARD,
			StaticComponentImageUrls: []string{
				"https://epifi-icons.pointz.in/credit_card_images/waitlist_intro_screen/waitlist_intro_top_image.png",
				"https://epifi-icons.pointz.in/credit_card_images/waitlist_intro_screen/fi_coins_middle_section.png",
				"https://epifi-icons.pointz.in/credit_card_images/waitlist_intro_screen/fi_coin_redemption_bottom_section.png",
			},
			RedirectAction: &deeplink.Deeplink{
				Screen: deeplink.Screen_IN_APP_WAITLIST_CONFIRMATION_SCREEN,
				ScreenOptions: &deeplink.Deeplink_InAppWaitlistConfirmationScreenOptions{
					InAppWaitlistConfirmationScreenOptions: &deeplink.InAppWaitlistConfirmationScreenOptions{
						TopImageUrl: "https://epifi-icons.pointz.in/credit_card_images/waitlist_confirmation.png",
						Text:        "You’re on the waitlist!",
						SubText:     "Not a fan of waiting? Check your credit score for faster access.",
						Cta: &deeplink.Cta{
							Text: "Check credit score",
							Deeplink: &deeplink.Deeplink{
								Screen: deeplink.Screen_ANALYSER_SCREEN,
								ScreenOptions: &deeplink.Deeplink_AnalyserScreenOptions{
									AnalyserScreenOptions: &deeplink.AnalyserScreenOptions{
										AnalyserName:   pb.AnalyserName_ANALYSER_NAME_CREDIT_SCORE.String(),
										EncodedRequest: "",
									},
								},
							},
						},
					},
				},
			},
			Cta: &deeplink.Cta{
				Text: "Hop on the waitlist",
			},
			BottomComponentImageUrl: "https://epifi-icons.pointz.in/credit_card_images/PartnerIcon.png",
		},
		},
	}
}

func GetWaitlistIntroDeeplinkV2(cardProgram *types.CardProgram) *deeplink.Deeplink {
	details := getDetailsByCardProgram(cardProgram)
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/credit_card_with_coins.png",
				Text:    "You are not eligible for a Credit Card at the moment",
				SubText: "We will let you know once you are eligible",
				Details: details.GetInfoItems(),
			},
		},
	}
}

func GetWebApprovedDeeplink(cardProgram *types.CardProgram) *deeplink.Deeplink {
	details := getDetailsByCardProgram(cardProgram)
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/credit_card_with_coins.png",
				Text:    "Thank you for showing interest in the AmpliFi card!",
				SubText: "We will let you know once you are eligible",
				Details: details.GetInfoItems(),
			},
		},
	}
}

func GetOnboardingPausedDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/card_paused_icon.png",
				Text:    "We've paused new credit card applications for the moment",
				SubText: "You'll be the first to know once we're \nback up and running!",
			},
		},
	}
}

func GetOfferExpiredScreenDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/credit_card_no_offer.png",
				Text:    "We are unable to offer you our credit card anymore",
				SubText: "Unfortunately, you are no longer eligible for our credit card. We will let you know when you can apply again.",
				Cta: &deeplink.Cta{
					Text: "Notify me when I’m eligible",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_HOME,
						ScreenOptions: &deeplink.Deeplink_HomeScreenOptions{
							HomeScreenOptions: &deeplink.HomeScreenOptions{},
						},
					},
				},
			},
		},
	}
}

func GetOnboardingDisabledScreenDeeplink(c *ffGenConf.DisabledOnboardingScreenOptions) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: c.Icon(),
				Text:    c.Text(),
				SubText: c.SubText(),
				Cta: &deeplink.Cta{
					Text: "Notify me",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_HOME,
						ScreenOptions: &deeplink.Deeplink_HomeScreenOptions{
							HomeScreenOptions: &deeplink.HomeScreenOptions{},
						},
					},
				},
			},
		},
	}
}

func GetTransactionReceiptDeeplink(txnId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_TRANSACTION_RECEIPT,
		ScreenOptions: &deeplink.Deeplink_CreditCardTransactionReceiptScreenOptions{
			CreditCardTransactionReceiptScreenOptions: &deeplink.CreditCardTransactionReceiptScreenOptions{
				TxnId: txnId,
			},
		},
	}
}

func GetCreditCardBottomViewDeeplink(cardId, iconUrl, title, subtitle string, cta *deeplink.Cta) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_SUCCESS_BOTTOM_VIEW_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardSuccessBottomViewScreenOptions{
			CreditCardSuccessBottomViewScreenOptions: &deeplink.CreditCardSuccessBottomViewScreenOptions{
				CreditCardId: cardId,
				InfoItemWithCta: &deeplink.InfoItemWithCta{
					Info: &deeplink.InfoItem{
						Icon:  iconUrl,
						Title: title,
						Desc:  subtitle,
					},
					Cta: cta,
				},
			},
		},
	}
}

func GetViewEligibleMerchantCta() *deeplink.Cta {
	return &deeplink.Cta{
		Type: deeplink.Cta_CUSTOM,
		Text: "View brand list",
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_CREDIT_CARD_ELIGIBLE_MERCHANT_REWARDS_SCREEN,
		},
	}
}

func GetMyRewardsCta() *deeplink.Cta {
	return &deeplink.Cta{
		Type: deeplink.Cta_CUSTOM,
		Deeplink: &deeplink.Deeplink{
			Screen:        deeplink.Screen_MY_REWARDS_SCREEN,
			ScreenOptions: nil,
		},
	}
}

func getDateSuffix(n int) string {
	suffix := "th"
	switch {
	case n%100 >= 11 && n%100 <= 20:
		return suffix
	case n%10 == 1:
		suffix = "st"
	case n%10 == 2:
		suffix = "nd"
	case n%10 == 3:
		suffix = "rd"
	default:
	}
	return suffix
}

func isAddressV2Enabled(ctx context.Context, conf *worker.Flags) bool {
	return app.IsFeatureEnabledFromCtx(ctx, conf.EnableAddressV2Screen)
}

func GetCreditCardRealTimeEligibilityCheckDeeplink() (*deeplink.Deeplink, error) {
	screenOptions := &ffScreenTypes.CreditCardRealTimeEligibilityCheckIntroScreenOptions{
		ImageTop: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/credit_card_images/credit_card_with_coins_bg_tint.png",
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  380,
						Height: 338,
					},
					ImageType: commontypes.ImageType_PNG,
				},
			},
		},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "The most rewarding\nCredit Card",
			},
		},
		SubTitle: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Cancel your other Credit Cards. This is the one.\nGet 3% back on 20+ brands.",
			},
		},
		CheckCreditScoreCta: &deeplink.Cta{
			Text: "Check your eligibility",
		},
		ImageBottom: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/credit_card_images/PartnerIcon.png",
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  112,
						Height: 32,
					},
					ImageType: commontypes.ImageType_PNG,
				},
			},
		},
	}
	screenDeeplink, err := deeplinkV3.GetDeeplinkV3(deeplink.Screen_CREDIT_CARD_REAL_TIME_ELIGIBILITY_CHECK_INTRO_SCREEN, screenOptions)
	if err != nil {
		return nil, fmt.Errorf("error in fetching deeplink v3 %w", err)
	}
	return screenDeeplink, nil
}

func GetSecuredCreditCardCreateFdScreenDeeplink(configResponse *ccFireflyPb.GetDepositConfigResponse) (*deeplink.Deeplink, error) {
	defaultCreditLimits := make([]*types.Money, 0)
	for _, defaultLimit := range configResponse.DefaultCreditLimits {
		defaultCreditLimits = append(defaultCreditLimits, types.GetFromBeMoney(defaultLimit))
	}

	moreDetailsBottomSheetDeeplink, err := GetSecuredCardsMoreDetailsScreen(configResponse.GetMinDepositAmount())
	if err != nil {
		return nil, errors.Wrap(err, "error fetching more details deeplink")
	}
	screenOptions := GetSecuredCardDepositScreenOptions(configResponse, moreDetailsBottomSheetDeeplink, defaultCreditLimits)

	nextAction, err := deeplinkV3.GetDeeplinkV3(deeplink.Screen_SECURED_CREDIT_CARD_DEPOSIT_SCREEN, screenOptions)
	if err != nil {
		return nil, errors.Wrap(err, "error in marshalling screen options")
	}
	return nextAction, nil
}

func GetSecuredCardDepositScreenOptions(configResponse *ccFireflyPb.GetDepositConfigResponse, moreDetailsBottomSheetDeeplink *deeplink.Deeplink, defaultCreditLimits []*types.Money) *ffScreenTypes.SecuredCreditCardDepositScreenOptions {
	var (
		fdScreenDesc string
	)
	fdScreenDesc = fmt.Sprintf("<font color='#8D8D8D'>Open a FD with as little as %s.<br>Your credit limit will be 90%% of the<br> deposited amount", money.ToDisplayStringInIndianFormat(configResponse.GetMinDepositAmount(), 0, true))
	return &ffDeeplink.SecuredCreditCardDepositScreenOptions{
		StepInfo: &deeplink.StepInfo{
			CurrentStep: 1,
			TotalSteps:  4,
		},
		TopSectionDetails: &deeplink.InfoItemWithCtaV2{
			Info: &deeplink.InfoItemV2{
				Icon: "",
				Title: &commontypes.Text{
					FontColor:    "#FFFFFF",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Choose your credit limit with a Fixed Deposit"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
				},
				Desc: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{Html: fdScreenDesc},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
				},
			},
		},
		MinDepositAmount: types.GetFromBeMoney(configResponse.MinDepositAmount),
		BottomText: &deeplink.InfoItemV2{
			Icon: "https://epifi-icons.pointz.in/credit_card_images/information_grey_4x.png",
			Title: &commontypes.Text{
				FontColor:    "#B9B9B9",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "The deposit will be auto-renewed and remain open until your card is closed."},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4_PARA},
			},
		},
		InsufficientBalanceCta: &deeplink.Cta{
			Text: "Add money",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_TRANSFER_IN,
			},
			DisplayTheme: deeplink.Cta_PRIMARY,
		},
		OpenDepositCta: &deeplink.Cta{
			Text:         "Open fixed deposit",
			DisplayTheme: deeplink.Cta_PRIMARY,
		},
		IneligibleActionText: &commontypes.Text{
			FontColor:    "#A73F4B",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "You have insufficient funds"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
		},
		AddNomineeCta: &deeplink.Cta{
			Text: "Add / select a nominee",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_COLLECT_NOMINEE_DETAILS_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CollectNomineeDetailsScreenOptions{
					CollectNomineeDetailsScreenOptions: &deeplink.CollectNomineeDetailsScreenOptions{
						Flow:                                deeplink.DataCollectionFlow_DATA_COLLECTION_FLOW_DEPOSIT_CREATION,
						Title:                               "Add/select a nominee",
						MaxNominees:                         1,
						OptOutText:                          "I want to continue without nominee",
						NomineeCollectionAuthenticationType: deeplink.NomineeCollectionAuthenticationType_AuthenticationType_NO_AUTH_REQUIRED,
					},
				},
			},
			DisplayTheme: deeplink.Cta_PRIMARY,
		},
		AddNomineeVisualElement: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/credit_card_images/large_plus_4x.png"},
					Properties: &commontypes.VisualElementProperties{
						Width:  32,
						Height: 32,
					},
					ImageType: commontypes.ImageType_PNG,
				},
			},
		},
		TermsAndConditionsText: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_Html{Html: "By continuing I agree to the <a href='https://www.federalbank.co.in/epifi-tandc'>T&C's</a> of Federal's Fixed Deposit."},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4_PARA},
		},
		DefaultCreditLimit:    types.GetFromBeMoney(configResponse.SelectedCreditLimit),
		SuggestedCreditLimits: defaultCreditLimits,
		CreditLimitHeading: &commontypes.Text{
			FontColor:    "#646464",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "CREDIT LIMIT"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
		},
		DepositLimitHeading: &commontypes.Text{
			FontColor:    "#646464",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "FIXED DEPOSIT AMOUNT"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
		},
		MaxDepositAmount: types.GetFromBeMoney(configResponse.MaxDepositAmount),
		MinDepositText: &commontypes.Text{
			FontColor:    "#A73F4B",
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Deposit amount should be greater than %s", money.ToDisplayStringInIndianFormat(configResponse.MinDepositAmount, 0, true))},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
		},
		MaxDepositText: &commontypes.Text{
			FontColor:    "#A73F4B",
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Deposit amount should be less than %s", money.ToDisplayStringInIndianFormat(configResponse.MaxDepositAmount, 0, true))},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
		},
	}
}

func GetSecuredCardsMoreDetailsScreen(minDepositAmount *moneyPb.Money) (*deeplink.Deeplink, error) {
	return deeplinkV3.GetDeeplinkV3(deeplink.Screen_SECURED_CC_DETAILS_BOTTOM_SHEET_SCREEN,
		&ffDeeplink.SecuredCcDetailsBottomSheetScreenOptions{
			HeaderDetails: &deeplink.InfoItemV3{
				Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/fd_locker_4x.png").
					WithProperties(&commontypes.VisualElementProperties{
						Width:  54,
						Height: 54,
					}),
				Title: &commontypes.Text{
					FontColor:    "#333333",
					DisplayValue: &commontypes.Text_Html{Html: "Open a Fixed Deposit &<br>get a Credit Card instantly"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_HEADLINE_2,
					},
				},
				SubTitle: &commontypes.Text{
					FontColor:    "#646464",
					DisplayValue: &commontypes.Text_Html{Html: "A fixed deposit helps us securely provide you<br> with a Fi-Federal Co-branded Credit Card"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_BODY_3_PARA,
					},
				},
			},
			FeatureDetails: []*commontypes.TextWithIcon{
				{
					Text: &commontypes.Text{
						FontColor:    "#646464",
						DisplayValue: &commontypes.Text_Html{Html: "Helps build your credit score"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
						},
					},
					IconUrl: "https://epifi-icons.pointz.in/credit_card_images/secured_card_green_tick_4x.png",
				},
				{
					Text: &commontypes.Text{
						FontColor:    "#646464",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Earn up to 7.50% on your deposit"},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
						},
					},
					IconUrl: "https://epifi-icons.pointz.in/credit_card_images/secured_card_green_tick_4x.png",
				},
				{
					Text: &commontypes.Text{
						FontColor:    "#646464",
						DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Minimum deposit: %s", money.ToDisplayStringInIndianFormat(minDepositAmount, 0, true))},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_3,
						},
					},
					IconUrl: "https://epifi-icons.pointz.in/credit_card_images/secured_card_green_tick_4x.png",
				},
			},
			FeatureBgColor: "#F7F9FA",
		})
}

func GetNextActionForFdCreationFailure() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
			IconUrl: "https://epifi-icons.pointz.in/credit_card_images/credit_card_no_offer.png",
			Text:    "Could not create fixed deposit",
			SubText: "Sorry! It seems that we were unable to create a fixed deposit. Please retry.",
			Cta: &deeplink.Cta{
				Text: "Back to home",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_HOME,
					ScreenOptions: &deeplink.Deeplink_HomeScreenOptions{
						HomeScreenOptions: &deeplink.HomeScreenOptions{}},
				},
			},
		}},
	}
}

func GetSuccessfulDepositNextAction(cardRequest *ccFireflyPb.CardRequest, depositInfo *depositPb.GetByIdResponse, depositStatus types.DepositStatus) *deeplink.Deeplink {
	screenOptions := &ffDeeplink.SecuredCreditCardFdDetailsScreenOptions{
		StepInfo: &deeplink.StepInfo{
			CurrentStep: 1,
			TotalSteps:  4,
		},
		HeaderDetails: &deeplink.InfoItemV3{
			Icon: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/credit_card_images/fd_locker_4x.png"},
						Properties: &commontypes.VisualElementProperties{
							Width:  100,
							Height: 100,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
			Title: &commontypes.Text{
				FontColor:    "#FFFFFF",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Fixed Deposit\ncreated successfully"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
				},
			},
		},
		FdDetails: []*deeplink.InfoItemV3{
			{
				Title: &commontypes.Text{
					FontColor:    "#8D8D8D",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "CREDIT LIMIT"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
				},
				SubTitle: &commontypes.Text{
					FontColor:    "#CED2D6",
					DisplayValue: &commontypes.Text_PlainString{PlainString: money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, true)},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_NUMBER_L,
					},
				},
			},
			{
				Title: &commontypes.Text{
					FontColor:    "#8D8D8D",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "DEPOSIT AMOUNT"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
				},
				SubTitle: &commontypes.Text{
					FontColor:    "#CED2D6",
					DisplayValue: &commontypes.Text_PlainString{PlainString: money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSecuredCardOnboardingDetails().GetDepositAmount(), 0, true)},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_NUMBER_L,
					},
				},
			},
			{
				Title: &commontypes.Text{
					FontColor:    "#8D8D8D",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "INTEREST RATE"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
				},
				SubTitle: &commontypes.Text{
					FontColor:    "#CED2D6",
					DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("%s%%", depositInfo.GetAccount().GetInterestRate())},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_NUMBER_L,
					},
				},
			},
			{
				Title: &commontypes.Text{
					FontColor:    "#8D8D8D",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "MATURITY AMOUNT"},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS,
					},
				},
				SubTitle: &commontypes.Text{
					FontColor:    "#CED2D6",
					DisplayValue: &commontypes.Text_PlainString{PlainString: money.ToDisplayStringInIndianFormat(depositInfo.GetAccount().GetMaturityAmount(), 0, true)},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_NUMBER_L,
					},
				},
			},
		},
		NextCta: &deeplink.Cta{
			Type:   deeplink.Cta_CUSTOM,
			Text:   "Next: Select shipping address",
			Status: deeplink.Cta_CTA_STATUS_ENABLED,
		},
		FdCreationStatus: depositStatus,
		CardRequestId:    cardRequest.GetId(),
		VeBankLogo: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/powered_by_bank_logo.png"},
				Properties: &commontypes.VisualElementProperties{
					Width:  112,
					Height: 30,
				},
			},
		}},
	}

	fdSuccessDeeplink, _ := deeplinkV3.GetDeeplinkV3(deeplink.Screen_SECURED_CREDIT_CARD_FD_DETAILS_SCREEN, screenOptions)
	return fdSuccessDeeplink
}

func GetFailedDepositNextAction(depositStatus types.DepositStatus, message string, cardRequestId string) *deeplink.Deeplink {
	screenOptions := &ffDeeplink.SecuredCreditCardFdDetailsScreenOptions{
		HeaderDetails: &deeplink.InfoItemV3{
			Icon: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/credit_card_images/Warning_Excalamation_4x.png"},
						Properties: &commontypes.VisualElementProperties{
							Width:  100,
							Height: 100,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
			Title: &commontypes.Text{
				FontColor:    "#FFFFFF",
				DisplayValue: &commontypes.Text_PlainString{PlainString: message},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_SUBTITLE_1,
				},
			},
		},
		NextCta: &deeplink.Cta{
			Type:   deeplink.Cta_CUSTOM,
			Text:   "Back to home",
			Status: deeplink.Cta_CTA_STATUS_ENABLED,
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_HOME,
				ScreenOptions: &deeplink.Deeplink_HomeScreenOptions{
					HomeScreenOptions: &deeplink.HomeScreenOptions{}},
			},
		},
		VeBankLogo: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/powered_by_bank_logo.png"},
				Properties: &commontypes.VisualElementProperties{
					Width:  112,
					Height: 30,
				},
			},
		}},
		FdCreationStatus: depositStatus,
		CardRequestId:    cardRequestId,
	}
	fdFailureDeeplink, _ := deeplinkV3.GetDeeplinkV3(deeplink.Screen_SECURED_CREDIT_CARD_FD_DETAILS_SCREEN, screenOptions)
	return fdFailureDeeplink
}

// nolint:dupl
func GetSecuredCreditCardActivationDeepLink(ctx context.Context, cardRequest *ccFireflyPb.CardRequest, conf *workerGenConf.Config) (*deeplink.Deeplink, error) {
	var (
		cardInfoItems         []*deeplink.InfoItemV2
		defaultBillGenDate    int64
		defaultPaymentDueDate int64
	)
	staticScreenOptions := &workerConf.CardActivationScreenOptions{
		InfoItems:                  conf.Screens().SecuredCardActivationScreenOptions().InfoItems(),
		CardImageUrl:               conf.Screens().SecuredCardActivationScreenOptions().CardImageUrl(),
		Heading:                    conf.Screens().SecuredCardActivationScreenOptions().Heading(),
		SubHeading:                 conf.Screens().SecuredCardActivationScreenOptions().SubHeading(),
		Tnc:                        conf.Screens().SecuredCardActivationScreenOptions().Tnc(),
		ActivateDigitalCardCtaText: conf.Screens().SecuredCardActivationScreenOptions().ActivateDigitalCardCtaText(ctx),
		ImportantTncUrl:            conf.Screens().SecuredCardActivationScreenOptions().ImportantTncUrl(),
		KeyFactStatementTncUrl:     conf.Screens().SecuredCardActivationScreenOptions().KeyFactStatementTncUrl(),
		CreditCardTncUrl:           conf.Screens().SecuredCardActivationScreenOptions().CreditCardTncUrl(),
	}
	if cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSkipBillGenDateCaptureStage() {
		defaultBillGenDate = conf.Screens().SecuredCardActivationScreenOptions().DefaultBillGenDate(ctx)
		defaultPaymentDueDate = conf.Screens().SecuredCardActivationScreenOptions().DefaultPaymentDueDate(ctx)
	} else {
		defaultBillGenDate = cardRequest.GetRequestDetails().GetBillGenDate()
		defaultPaymentDueDate = cardRequest.GetRequestDetails().GetPaymentDueDate()
	}

	marshalledScreenOptions, err := json.Marshal(staticScreenOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to marshall screen options %w", err)
	}
	confScreenOptionsCopy := &workerConf.CardActivationScreenOptions{}
	err = json.Unmarshal(marshalledScreenOptions, confScreenOptionsCopy)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshall screen options %w", err)
	}
	for idx, infoItem := range confScreenOptionsCopy.InfoItems {
		switch idx {
		case 0:
			infoItem.SubTitleText.PlainString = fmt.Sprintf(infoItem.SubTitleText.PlainString,
				money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, true))
			limitComparison, err := money.CompareV2(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), thresholdLimitForShowingLimitUpdate)
			if err != nil {
				return nil, fmt.Errorf("failed to compare limit %w", err)
			}
			// if limit is greater than 2 lacs then we will not show description text to the user informing
			// about limit increase
			if limitComparison >= 0 {
				infoItem.DescriptionText = nil
			}
		case 1:
			infoItem.SubTitleText.PlainString = fmt.Sprintf(infoItem.SubTitleText.PlainString,
				defaultBillGenDate, getDateSuffix(int(defaultBillGenDate)))
		case 2:
			infoItem.SubTitleText.PlainString = fmt.Sprintf(infoItem.SubTitleText.PlainString,
				money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSecuredCardOnboardingDetails().GetDepositAmount(), 0, true))
			limitComparison, err := money.CompareV2(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSecuredCardOnboardingDetails().GetDepositAmount(), thresholdLimitForShowingLimitUpdate)
			if err != nil {
				return nil, fmt.Errorf("failed to compare limit %w", err)
			}
			// if limit is greater than 2 lacs then we will not show description text to the user informing
			// about limit increase
			if limitComparison >= 0 {
				infoItem.DescriptionText = nil
			}
		}
		cardInfoItem := &deeplink.InfoItemV2{
			Title:    typesPkg.NewText(infoItem.TitleText, ""),
			Desc:     typesPkg.NewText(infoItem.DescriptionText, ""),
			SubTitle: typesPkg.NewText(infoItem.SubTitleText, ""),
		}
		if infoItem.InfoBlockTitle != "" {
			cardInfoItem.Tooltip = &deeplink.InfoToolTip{
				Info: &deeplink.InfoBlock{
					Title: infoItem.InfoBlockTitle,
					Desc:  infoItem.InfoBlockDescription,
				},
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/information_grey_4x.png",
			}
		}

		switch idx {
		case 0:
			cardInfoItem.InfoType = deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_CREDIT_LIMIT
			cardInfoItem.HyperLinkSubTitle = &deeplink.TextWithHyperlinks{
				Text:         typesPkg.NewText(infoItem.DescriptionText, ""),
				HyperlinkMap: nil,
			}
		case 1:
			cardInfoItem.InfoType = deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_BILL_GENERATION
			cardInfoItem.HyperLinkSubTitle = &deeplink.TextWithHyperlinks{
				Text:         typesPkg.NewText(infoItem.DescriptionText, ""),
				HyperlinkMap: nil,
			}
		case 2:
			cardInfoItem.InfoType = deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_WELCOME_OFFER
			cardInfoItem.HyperLinkSubTitle = &deeplink.TextWithHyperlinks{
				Text:         typesPkg.NewText(infoItem.DescriptionText, ""),
				HyperlinkMap: nil,
			}
		default:
		}

		cardInfoItems = append(cardInfoItems, cardInfoItem)
	}
	screenOptions := &deeplink.FireflyCardActivationScreenOptions{
		CardImageUrl: "https://epifi-icons.pointz.in/credit_card_images/card_image_no_name.png",
		Text:         "Congrats! Your Fi-Federal Co-branded Credit Card is ready",
		SubText:      "Kudos to taking the first step in building your credit score!",
		BillDates: &deeplink.InfoBlock{InfoItems: []*deeplink.InfoItem{
			{
				Title: "Credit limit: ",
				Desc:  money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, false),
				ToolTip: &deeplink.InfoToolTip{
					Info: &deeplink.InfoBlock{
						Title: "Credit limit",
						Desc:  "A credit limit is the maximum amount of credit offered to a customer. It is a factor determined based on consumers' credit scores and can impact their ability to get credit in the future",
					},
				},
			},
			{
				Title: "Bill generation: ",
				Desc:  fmt.Sprintf("%d%s", cardRequest.GetRequestDetails().GetBillGenDate(), getDateSuffix(int(cardRequest.GetRequestDetails().GetBillGenDate()))),
				ToolTip: &deeplink.InfoToolTip{
					Info: &deeplink.InfoBlock{
						Title: "Bill generation",
						Desc:  "The bill generation date is the date on\nwhich your credit card statement is\ngenerated every month.",
					},
				},
			},
			{
				Title: "Deposit Amount: ",
				Desc:  money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, false),
				ToolTip: &deeplink.InfoToolTip{
					Info: &deeplink.InfoBlock{
						Title: "Deposit Amount",
					},
				},
			},
		}},
		VeBankLogo: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/issued_by_bank_logo.png"},
				Properties: &commontypes.VisualElementProperties{
					Width:  112,
					Height: 30,
				},
			},
		}},
		CardImage: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: confScreenOptionsCopy.CardImageUrl},
				Properties: &commontypes.VisualElementProperties{
					Width:  216,
					Height: 134,
				},
			},
		}},
		Heading:       typesPkg.NewText(confScreenOptionsCopy.Heading, ""),
		SubHeading:    typesPkg.NewText(confScreenOptionsCopy.SubHeading, ""),
		CardInfoItems: cardInfoItems,
		Tnc: fmt.Sprintf(confScreenOptionsCopy.Tnc,
			confScreenOptionsCopy.ImportantTncUrl, confScreenOptionsCopy.KeyFactStatementTncUrl, confScreenOptionsCopy.CreditCardTncUrl),
		ActivateDigitalCardCta: &deeplink.Cta{
			Type:         deeplink.Cta_DONE,
			Text:         confScreenOptionsCopy.ActivateDigitalCardCtaText,
			Deeplink:     GetCreateCardCtaDeeplink(cardRequest.GetId()),
			DisplayTheme: deeplink.Cta_PRIMARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		},
		CardRequestId:         cardRequest.GetId(),
		DefaultBillGenDate:    defaultBillGenDate,
		DefaultPaymentDueDate: defaultPaymentDueDate,
	}
	return &deeplink.Deeplink{
		Screen:        deeplink.Screen_FIREFLY_CARD_ACTIVATION_SCREEN,
		ScreenOptions: &deeplink.Deeplink_FireflyCardActivationScreenOptions{FireflyCardActivationScreenOptions: screenOptions},
	}, nil
}

// nolint:dupl
func GetMassUnsecuredCardActivationDeepLink(ctx context.Context, cardRequest *ccFireflyPb.CardRequest, conf *workerGenConf.Config) (*deeplink.Deeplink, error) {
	var (
		cardInfoItems         []*deeplink.InfoItemV2
		defaultBillGenDate    int64
		defaultPaymentDueDate int64
	)
	staticScreenOptions := &workerConf.CardActivationScreenOptions{
		InfoItems:                  conf.Screens().MassUnsecuredCardActivationScreenOptions().InfoItems(),
		CardImageUrl:               conf.Screens().MassUnsecuredCardActivationScreenOptions().CardImageUrl(),
		Heading:                    conf.Screens().MassUnsecuredCardActivationScreenOptions().Heading(),
		SubHeading:                 conf.Screens().MassUnsecuredCardActivationScreenOptions().SubHeading(),
		Tnc:                        conf.Screens().MassUnsecuredCardActivationScreenOptions().Tnc(),
		ActivateDigitalCardCtaText: conf.Screens().MassUnsecuredCardActivationScreenOptions().ActivateDigitalCardCtaText(ctx),
		ImportantTncUrl:            conf.Screens().MassUnsecuredCardActivationScreenOptions().ImportantTncUrl(),
		KeyFactStatementTncUrl:     conf.Screens().MassUnsecuredCardActivationScreenOptions().KeyFactStatementTncUrl(),
		CreditCardTncUrl:           conf.Screens().MassUnsecuredCardActivationScreenOptions().CreditCardTncUrl(),
	}
	if cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetSkipBillGenDateCaptureStage() {
		defaultBillGenDate = conf.Screens().MassUnsecuredCardActivationScreenOptions().DefaultBillGenDate(ctx)
		defaultPaymentDueDate = conf.Screens().MassUnsecuredCardActivationScreenOptions().DefaultPaymentDueDate(ctx)
	} else {
		defaultBillGenDate = cardRequest.GetRequestDetails().GetBillGenDate()
		defaultPaymentDueDate = cardRequest.GetRequestDetails().GetPaymentDueDate()
	}

	marshalledScreenOptions, err := json.Marshal(staticScreenOptions)
	if err != nil {
		return nil, fmt.Errorf("failed to marshall screen options %w", err)
	}
	confScreenOptionsCopy := &workerConf.CardActivationScreenOptions{}
	err = json.Unmarshal(marshalledScreenOptions, confScreenOptionsCopy)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshall screen options %w", err)
	}
	for idx, infoItem := range confScreenOptionsCopy.InfoItems {
		switch idx {
		case 0:
			infoItem.SubTitleText.PlainString = fmt.Sprintf(infoItem.SubTitleText.PlainString,
				money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, true))
			limitComparison, err := money.CompareV2(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), thresholdLimitForShowingLimitUpdate)
			if err != nil {
				return nil, fmt.Errorf("failed to compare limit %w", err)
			}
			// if limit is greater than 2 lacs then we will not show description text to the user informing
			// about limit increase
			if limitComparison >= 0 {
				infoItem.DescriptionText = nil
			}
		}
		cardInfoItem := &deeplink.InfoItemV2{
			Title:    typesPkg.NewText(infoItem.TitleText, ""),
			Desc:     typesPkg.NewText(infoItem.DescriptionText, ""),
			SubTitle: typesPkg.NewText(infoItem.SubTitleText, ""),
		}
		if infoItem.InfoBlockTitle != "" {
			cardInfoItem.Tooltip = &deeplink.InfoToolTip{
				Info: &deeplink.InfoBlock{
					Title: infoItem.InfoBlockTitle,
					Desc:  infoItem.InfoBlockDescription,
				},
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/information_grey_4x.png",
			}
		}

		switch idx {
		case 0:
			cardInfoItem.InfoType = deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_CREDIT_LIMIT
			cardInfoItem.HyperLinkSubTitle = &deeplink.TextWithHyperlinks{
				Text:         typesPkg.NewText(infoItem.DescriptionText, ""),
				HyperlinkMap: nil,
			}
		case 1:
			cardInfoItem.InfoType = deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_FEE_INFO
			cardInfoItem.HyperLinkSubTitle = &deeplink.TextWithHyperlinks{
				Text:         typesPkg.NewText(infoItem.DescriptionText, ""),
				HyperlinkMap: nil,
			}
		default:
		}

		cardInfoItems = append(cardInfoItems, cardInfoItem)
	}
	screenOptions := &deeplink.FireflyCardActivationScreenOptions{
		CardImage: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: confScreenOptionsCopy.CardImageUrl},
				Properties: &commontypes.VisualElementProperties{
					Width:  226,
					Height: 146,
				},
			},
		}},
		Heading:       typesPkg.NewText(confScreenOptionsCopy.Heading, ""),
		SubHeading:    typesPkg.NewText(confScreenOptionsCopy.SubHeading, ""),
		CardInfoItems: cardInfoItems,
		Tnc: fmt.Sprintf(confScreenOptionsCopy.Tnc,
			confScreenOptionsCopy.ImportantTncUrl, confScreenOptionsCopy.KeyFactStatementTncUrl, confScreenOptionsCopy.CreditCardTncUrl),
		ActivateDigitalCardCta: &deeplink.Cta{
			Type:         deeplink.Cta_DONE,
			Text:         confScreenOptionsCopy.ActivateDigitalCardCtaText,
			Deeplink:     GetCreateCardCtaDeeplink(cardRequest.GetId()),
			DisplayTheme: deeplink.Cta_PRIMARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		},
		CardRequestId:         cardRequest.GetId(),
		DefaultBillGenDate:    defaultBillGenDate,
		DefaultPaymentDueDate: defaultPaymentDueDate,
		VeBankLogo: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/powered_by_bank_logo.png"},
				Properties: &commontypes.VisualElementProperties{
					Width:  112,
					Height: 30,
				},
			},
		}}}
	return &deeplink.Deeplink{
		Screen:        deeplink.Screen_FIREFLY_CARD_ACTIVATION_SCREEN,
		ScreenOptions: &deeplink.Deeplink_FireflyCardActivationScreenOptions{FireflyCardActivationScreenOptions: screenOptions},
	}, nil
}

func GetForceUpdateScreen(icon string, text string, subText string, redirectAction *deeplink.Cta) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: icon,
				Text:    text,
				SubText: subText,
				Cta:     redirectAction,
			},
		},
	}
}

func GetDepositTenureDetailsDeeplink(depositInterestRates []*types.DepositInterestDetails, selectedTerm *types.DepositTerm, depositConfig *ccFireflyPb.GetDepositConfigResponse, durationSliderValues []*types.DurationSliderValue) *ffScreenTypes.SecuredCcDepositTenureSelectionBottomSheetScreenOptions {

	for _, depositInterestRate := range depositInterestRates {
		depositInterestRate.InterestRate = fmt.Sprintf("%s%% p.a", depositInterestRate.InterestRate)
	}

	tenureDetails := &ffScreenTypes.SecuredCcDepositTenureSelectionBottomSheetScreenOptions{
		HeadingText: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Select deposit tenure"},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
			},
			FontColor: "#333333",
		},
		TenureDaysText: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "days"},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_0,
			},
			FontColor: "#333333",
		},
		TenureMonthText: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{PlainString: "months"},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
			},
			FontColor: "#A4A4A4",
		},
		SliderTermDetails:   depositInterestRates,
		SelectedDepositTerm: selectedTerm,
		ConfirmationCta: &deeplink.Cta{
			Type:         deeplink.Cta_CUSTOM,
			Text:         "Confirm",
			DisplayTheme: deeplink.Cta_PRIMARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		},
		MinDepositTermErrorText: &commontypes.Text{
			FontColor:    "#A73F4B",
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Deposit tenure should be greater than %s", firefly.GetDepositTermString(depositConfig.GetMinDepositTerm()))},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
		},
		MaxDepositTermErrorText: &commontypes.Text{
			FontColor:    "#A73F4B",
			DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Deposit tenure should be less than %s", firefly.GetDepositTermString(depositConfig.GetMaxDepositTerm()))},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
		},
		DurationSliderValues: durationSliderValues,
	}
	return tenureDetails
}

func GetCCFiLiteRiskBlockedScreen() *deeplink.Deeplink {
	// TODO(Swapnil): add screen details when designs are in
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
	}
}

func GetCCFiLiteRiskManualReviewScreen() *deeplink.Deeplink {
	// TODO(Swapnil): add screen details when designs are in
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
	}
}

func GetMagnifiCardTnCConsentScreen(cardRequest *ccFireflyPb.CardRequest) (*deeplink.Deeplink, error) {
	screenOptions := &ffScreenTypes.CCCardTnCConsentScreenOptions{
		BackgroundColour: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#18191B"},
		},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: "Getting your card ready",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
			FontColor: "#F6F9FD",
		},
		Subtitle: &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: "All you need to do next is confirm your shipping address & do a quick identity verification",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
			FontColor: "#929599",
		},
		CardBenefitsContainerProperty: &ffFePb.DrawableProperties{
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#28292B"},
			},
			CornerProperty: &ffFePb.CornerProperty{
				TopStartCornerRadius: 20,
				TopEndCornerRadius:   20,
				BottomStartCorner:    20,
				BottomEndCorner:      20,
			},
		},
		CardBenefits: []*deeplink.InfoItemV2{
			{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "CREDIT LIMIT",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
					FontColor: "#A4A4A4",
				},
				Desc: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "This will increase over time as you keep using your card and keep making repayments.",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
					FontColor: "#6A6D70",
				},
				Tooltip: &deeplink.InfoToolTip{
					Info: &deeplink.InfoBlock{
						Title: "Credit limit",
						Desc:  "A credit limit is the maximum amount of credit offered to a customer.\n\nIt is a factor determined based on consumers' credit scores and can impact their ability to get credit in the future",
					},
					IconUrl: "https://epifi-icons.pointz.in/credit_card_images/information_grey_4x.png",
				},
				SubTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: fmt.Sprintf("%s", money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, true)),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					FontColor: "#FFFFFF",
				},
				InfoType: deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_CREDIT_LIMIT,
				HyperLinkSubTitle: &deeplink.TextWithHyperlinks{
					Text: &commontypes.Text{
						DisplayValue: &commontypes.Text_Html{
							Html: "This will increase over time as you keep using your card and keep making repayments.",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
						FontColor: "#6A6D70",
					},
				},
			},
			{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "JOINING FEE",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
					FontColor: "#A4A4A4",
				},
				SubTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "Lifetime free",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					FontColor: "#FFFFFF",
				},
				InfoType: deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_FEE_INFO,
			},
		},
		Cta: &deeplink.Cta{
			Text: "Continue to address selection",
		},
		ConsentCheckBox: &ffFePb.ConsentBox{
			CardOnboardingConsent: ffFeEnumsPb.CreditCardOnboardingConsent_CREDIT_CARD_ONBOARDING_CONSENT_MAGNIFI,
		},
		ConsentText: &ui.TextWithHyperlinks{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "By proceeding, you agree with the Federal Bank’s MITC, KFS and T&C’s",
				},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
				FontColor: "#929599",
			},
			HyperlinkMap: map[string]*ui.HyperLink{
				"MITC": {
					Link: &ui.HyperLink_Url{
						Url: "https://fi.money/documents/credit-card/magnifi/important-tnc",
					},
					EventParameter: "MagnifiMostImpT&CButton",
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_WEB_PAGE,
						ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplink.WebpageScreenOptions{
								WebpageTitle: "MITC",
								WebpageUrl:   "https://fi.money/documents/credit-card/magnifi/important-tnc",
							},
						},
					},
				},
				"KFS": {
					Link: &ui.HyperLink_Url{
						Url: "https://fi.money/documents/credit-card/magnifi/key-fact-statement",
					},
					EventParameter: "MagnifiKeyFactStatementButton",
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_WEB_PAGE,
						ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplink.WebpageScreenOptions{
								WebpageTitle: "KFS",
								WebpageUrl:   "https://fi.money/documents/credit-card/magnifi/key-fact-statement",
							},
						},
					},
				},
				"T&C": {
					Link: &ui.HyperLink_Url{
						Url: "https://fi.money/credit-card/T&Cs",
					},
					EventParameter: "MagnifiT&CButton",
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_WEB_PAGE,
						ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplink.WebpageScreenOptions{
								WebpageTitle: "T&C",
								WebpageUrl:   "https://fi.money/credit-card/T&Cs",
							},
						},
					},
				},
			},
		},
		BottomVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/federal_co_branded_intro_footer.png"},
				Properties: &commontypes.VisualElementProperties{
					Width:  412,
					Height: 32,
				},
				ImageType: commontypes.ImageType_PNG,
			},
		}},
		CardRequestId: cardRequest.GetId(),
	}
	dl, err := deeplinkV3.GetDeeplinkV3(deeplink.Screen_CC_CARD_TNC_CONSENT_SCREEN, screenOptions)
	if err != nil {
		return nil, err
	}
	return dl, nil
}

func GetAmplifiCardTnCConsentScreen(cardRequest *ccFireflyPb.CardRequest) (*deeplink.Deeplink, error) {
	screenOptions := &ffScreenTypes.CCCardTnCConsentScreenOptions{
		BackgroundColour: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#18191B"},
		},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: "Getting your card ready",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
			FontColor: "#F6F9FD",
		},
		Subtitle: &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: "All you need to do next is confirm your shipping address & do a quick identity verification",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
			FontColor: "#929599",
		},
		CardBenefitsContainerProperty: &ffFePb.DrawableProperties{
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#28292B"},
			},
			CornerProperty: &ffFePb.CornerProperty{
				TopStartCornerRadius: 20,
				TopEndCornerRadius:   20,
				BottomStartCorner:    20,
				BottomEndCorner:      20,
			},
		},
		CardBenefits: []*deeplink.InfoItemV2{
			{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "CREDIT LIMIT",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
					FontColor: "#A4A4A4",
				},
				Desc: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "This will increase over time as you keep using your card and keep making repayments.",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
					FontColor: "#6A6D70",
				},
				Tooltip: &deeplink.InfoToolTip{
					Info: &deeplink.InfoBlock{
						Title: "Credit limit",
						Desc:  "A credit limit is the maximum amount of credit offered to a customer.\n\nIt is a factor determined based on consumers' credit scores and can impact their ability to get credit in the future",
					},
					IconUrl: "https://epifi-icons.pointz.in/credit_card_images/information_grey_4x.png",
				},
				SubTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: fmt.Sprintf("%s", money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, true)),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					FontColor: "#FFFFFF",
				},
				InfoType: deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_CREDIT_LIMIT,
				HyperLinkSubTitle: &deeplink.TextWithHyperlinks{
					Text: &commontypes.Text{
						DisplayValue: &commontypes.Text_Html{
							Html: "This will increase over time as you keep using your card and keep making repayments.",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
						FontColor: "#6A6D70",
					},
				},
			},
			{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "WELCOME GIFT CARDS",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
					FontColor: "#A4A4A4",
				},
				SubTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "₹4,250",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					FontColor: "#FFFFFF",
				},
				InfoType: deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_WELCOME_OFFER,
				HyperLinkSubTitle: &deeplink.TextWithHyperlinks{
					Text: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "View details",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
						FontColor: "#00B899",
					},
					HyperlinkMap: map[string]*deeplink.Deeplink{
						"View details": GetWelcomeVouchersBottomSheet(),
					},
				},
			},
			{
				Title: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "JOINING FEE",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
					FontColor: "#A4A4A4",
				},
				SubTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_Html{
						Html: "₹2,000 + GST",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					FontColor: "#FFFFFF",
				},
				InfoType: deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_FEE_INFO,
				HyperLinkSubTitle: &deeplink.TextWithHyperlinks{
					Text: &commontypes.Text{
						DisplayValue: &commontypes.Text_Html{
							Html: "Renewal fee is waived off when you spend over ₹2.5 lakhs in the first year.",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
						FontColor: "#6A6D70",
					},
				},
			},
		},
		Cta: &deeplink.Cta{
			Text: "Continue to address selection",
		},
		ConsentCheckBox: &ffFePb.ConsentBox{
			CardOnboardingConsent: ffFeEnumsPb.CreditCardOnboardingConsent_CREDIT_CARD_ONBOARDING_CONSENT_AMPLIFI,
		},
		ConsentText: &ui.TextWithHyperlinks{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "By proceeding, you agree with the Joining Fees of ₹2,000 + GST & agree with Federal Bank’s MITC, KFS and T&C’s",
				},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
				FontColor: "#929599",
			},
			HyperlinkMap: map[string]*ui.HyperLink{
				"MITC": {
					Link: &ui.HyperLink_Url{
						Url: "https://fi.money/credit-card/important-T&Cs",
					},
					EventParameter: "AmplifiMostImpT&CButton",
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_WEB_PAGE,
						ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplink.WebpageScreenOptions{
								WebpageTitle: "MITC",
								WebpageUrl:   "https://fi.money/credit-card/important-T&Cs",
							},
						},
					},
				},
				"KFS": {
					Link: &ui.HyperLink_Url{
						Url: "https://fi.money/credit-card/key-fact-statement",
					},
					EventParameter: "AmplifiKeyFactStatementButton",
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_WEB_PAGE,
						ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplink.WebpageScreenOptions{
								WebpageTitle: "KFS",
								WebpageUrl:   "https://fi.money/credit-card/key-fact-statement",
							},
						},
					},
				},
				"T&C": {
					Link: &ui.HyperLink_Url{
						Url: "https://fi.money/credit-card/T&Cs",
					},
					EventParameter: "AmplifiT&CButton",
					NextAction: &deeplink.Deeplink{
						Screen: deeplink.Screen_WEB_PAGE,
						ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
							WebPageScreenOptions: &deeplink.WebpageScreenOptions{
								WebpageTitle: "T&C",
								WebpageUrl:   "https://fi.money/credit-card/T&Cs",
							},
						},
					},
				},
			},
		},
		BottomVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/federal_co_branded_intro_footer.png"},
				Properties: &commontypes.VisualElementProperties{
					Width:  412,
					Height: 32,
				},
				ImageType: commontypes.ImageType_PNG,
			},
		}},
		CardRequestId: cardRequest.GetId(),
	}
	dl, err := deeplinkV3.GetDeeplinkV3(deeplink.Screen_CC_CARD_TNC_CONSENT_SCREEN, screenOptions)
	if err != nil {
		return nil, err
	}
	return dl, nil
}

func GetLimitChangeScreen(cardRequest *ccFireflyPb.CardRequest) (*deeplink.Deeplink, error) {
	screenOptions := &ffScreenTypes.CCCreditLimitUpdateScreenOptions{
		BackgroundColour: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#18191B"},
		},
		TopVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
			Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/info_icon_limit_change.png"},
				Properties: &commontypes.VisualElementProperties{
					Width:  100,
					Height: 100,
				},
				ImageType: commontypes.ImageType_PNG,
			},
		}},
		Title: &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: "Your credit limit has changed",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
			FontColor: "#F6F9FD",
		},
		Subtitle: &commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: "Based on your latest credit bureau information, your credit limit has been updated.",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
			FontColor: "#929599",
		},
		CreditLimitInfo: &ui.VerticalIconTextComponent{
			Texts: []*commontypes.Text{
				{
					DisplayValue: &commontypes.Text_Html{
						Html: "NEW CREDIT LIMIT<br/>",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
					FontColor: "#929599",
				},
				{
					DisplayValue: &commontypes.Text_Html{
						Html: fmt.Sprintf("%s", money.ToDisplayStringInIndianFormat(cardRequest.GetRequestDetails().GetCardOnboardingDetails().GetCardLimit(), 0, true)),
					},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_2XL},
					FontColor: "#FFFFFF",
				},
			},
			ContainerProperties: &ui.VerticalIconTextComponent_ContainerProperties{
				BgColor:       "#28292B",
				CornerRadius:  16,
				Height:        -2,
				Width:         -2,
				LeftPadding:   50,
				RightPadding:  50,
				TopPadding:    16,
				BottomPadding: 16,
			},
		},
		PrimaryCta: &deeplink.Cta{
			Text: "Accept & create card",
		},
		SecondaryCta: ui.NewITC().WithTexts(&commontypes.Text{
			DisplayValue: &commontypes.Text_Html{
				Html: "I don’t want this card",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
			FontColor: "#00B899",
		}),
		CardRequestId: cardRequest.GetId(),
	}
	dl, err := deeplinkV3.GetDeeplinkV3(deeplink.Screen_CC_CREDIT_LIMIT_UPDATE_SCREEN, screenOptions)
	if err != nil {
		return nil, err
	}
	return dl, nil
}

func GetAmplifiConsentScreen(cardRequest *ccFireflyPb.CardRequest, addressType types.AddressType, kycLevel types.KYCLevel, limit *moneyPb.Money, cardProgram *types.CardProgram) (*deeplink.Deeplink, error) {
	screenOptions := &ffScreenTypes.CcAmpliFiScreenOptions{
		Header: &deeplink_screen_option.ScreenOptionHeader{},
		ToolBar: &ffFePb.WrappedIconTextToolBar{
			VisualElement: &ffFePb.WrappedVisualElement{
				VisualElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/Text_Icon_Toolbar_fi_icon.png"},
							Properties: &commontypes.VisualElementProperties{
								Width:  48,
								Height: 48,
							},
						},
					},
				},
				Properties: &ffFePb.LayoutProperties{
					Size: &ffFePb.Size{
						Width:  48,
						Height: 48,
					},
				},
			},
			Properties: &ffFePb.LayoutProperties{
				Size: &ffFePb.Size{
					Width:  -1,
					Height: -2,
				},
			},
		},
		Title: &ffFePb.WrappedTextInfo{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "You are eligible",
				},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
				FontColor: "#313234",
			},
			Properties: &ffFePb.LayoutProperties{
				Size: &ffFePb.Size{
					Width:  -1,
					Height: -2,
				},
			},
		},
		SubTitle: &ffFePb.WrappedTextInfo{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: "Complete a 3 step application to get the card",
				},
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
				FontColor: "#646464",
			},
			Properties: &ffFePb.LayoutProperties{
				Size: &ffFePb.Size{
					Width:  -1,
					Height: -2,
				},
			},
		},
		AmpliFiCard: &ffFePb.WrappedAmpliFiCard{
			InfoItems: []*deeplink.InfoItemV2{
				{
					Title: &commontypes.Text{
						FontColor: "#929599",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "CREDIT LIMIT",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
						},
					},
					Tooltip: &deeplink.InfoToolTip{
						IconUrl: "https://epifi-icons.pointz.in/credit_card_images/information_grey_4x.png",
						Info: &deeplink.InfoBlock{
							Title:     "Credit limit",
							Desc:      "A credit limit is the maximum amount of credit offered to a customer.\n\nIt is a factor determined based on consumers' credit scores and can impact their ability to get credit in the future.",
							InfoItems: nil,
						},
					},
					SubTitle: &commontypes.Text{
						FontColor:    "#313234",
						DisplayValue: &commontypes.Text_PlainString{PlainString: money.ToDisplayStringInIndianFormat(limit, 0, true)},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					},
					InfoType: deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_CREDIT_LIMIT,
				},
				{
					Title: &commontypes.Text{
						FontColor: "#929599",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "SHIPPING ADDRESS",
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS,
						},
					},
					SubTitle: &commontypes.Text{
						FontColor:    "#313234",
						DisplayValue: &commontypes.Text_PlainString{PlainString: ""},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_M},
					},
					InfoType: deeplink.InfoType_CARD_ACTIVATION_INFO_TYPE_ADDRESS,
					IconCta: &deeplink.VisualElementCta{
						Icon: &commontypes.VisualElement{
							Asset: &commontypes.VisualElement_Image_{
								Image: &commontypes.VisualElement_Image{
									Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/edit_pencil_gray_4x.png"},
								},
							},
						},
						Deeplink: nil,
					},
				},
			},
			DrawableProperties: &ffFePb.DrawableProperties{
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"},
				},
				BorderProperty: &ffFePb.BorderProperty{
					BorderThickness: 2,
					BorderColor:     "#3BB7A2",
				},
				CornerProperty: &ffFePb.CornerProperty{
					TopStartCornerRadius: 20,
					TopEndCornerRadius:   20,
					BottomStartCorner:    20,
					BottomEndCorner:      20,
				},
			},
		},
		DividerColour: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#BCDCE7"},
		},
		AmpliFiBenefit: &ffFePb.AmpliFiBenefit{
			BenefitHeading: &commontypes.Text{
				FontColor:    "#313234",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Enjoy exciting benefits"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_M},
			},
			Benefits: getBenefitsFromCardProgram(cardRequest.GetRequestDetails().GetCardProgram()),
		},
		TextWithHyperlinks: &ffFePb.WrappedHyperLinksWidget{
			ConsentText: getConsentTextByProgram(cardProgram),
			Properties: &ffFePb.LayoutProperties{
				Size: &ffFePb.Size{
					Width:  -1,
					Height: -2,
				},
			},
		},
		SliderProperties: &ffFePb.SliderProperties{
			SliderIconColor:    "#00B899",
			SliderDisableColor: "#9DA1A4",
			SliderActiveColor:  "#00B899",
			SliderText: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: fmt.Sprintf("Get %s", firefly.GetCardNameFromProgram(cardRequest.GetRequestDetails().GetCardProgram()))},
			},
		},
		FooterIcon: &ffFePb.WrappedVisualElement{
			VisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/credit_card_images/PartnerIcon.png"},
						Properties: &commontypes.VisualElementProperties{
							Width:  112,
							Height: 30,
						},
					},
				},
			},
			Properties: &ffFePb.LayoutProperties{
				Size: &ffFePb.Size{
					Width:  -1,
					Height: -2,
				},
			},
		},
		ScreenBackgroundColour: &ffFePb.DrawableProperties{
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#EFF2F6"},
			},
		},
		AddressType:   addressType,
		KycLevel:      kycLevel,
		CardRequestId: cardRequest.GetId(),
	}

	dl, err := deeplinkV3.GetDeeplinkV3(deeplink.Screen_CC_AMPLI_FI_SCREEN, screenOptions)
	if err != nil {
		return nil, err
	}

	return dl, nil
}

func getConsentTextByProgram(cardProgram *types.CardProgram) *ui.TextWithHyperlinks {
	switch cardProgram.GetCardProgramType() {
	case types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
		return &ui.TextWithHyperlinks{
			Text: &commontypes.Text{
				FontColor:    "#6A6D70",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "By proceeding, you agree with Federal Bank’s Most Important Terms & Conditions, Key Fact Statement and Terms & Conditions"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
			},
			HyperlinkMap: map[string]*ui.HyperLink{
				"Most Important Terms & Conditions": {
					Link: &ui.HyperLink_Url{Url: "https://fi.money/documents/credit-card/magnifi/important-tnc"},
				},
				"Key Fact Statement": {
					Link: &ui.HyperLink_Url{Url: "https://fi.money/documents/credit-card/magnifi/key-fact-statement"},
				},
				"Terms & Conditions": {
					Link: &ui.HyperLink_Url{Url: "https://fi.money/credit-card/T&Cs"},
				},
			},
		}
	default:
		return &ui.TextWithHyperlinks{
			Text: &commontypes.Text{
				FontColor:    "#6A6D70",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "By proceeding, you agree with the Joining Fees of ₹2,000+GST and agree with Federal Bank’s Most Important Terms & Conditions, Key Fact Statement and Terms & Conditions"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3_PARA},
			},
			HyperlinkMap: map[string]*ui.HyperLink{
				"Most Important Terms & Conditions": {
					Link: &ui.HyperLink_Url{Url: "https://fi.money/credit-card/important-T&Cs"},
				},
				"Key Fact Statement": {
					Link: &ui.HyperLink_Url{Url: "https://fi.money/credit-card/key-fact-statement"},
				},
				"Terms & Conditions": {
					Link: &ui.HyperLink_Url{Url: "https://fi.money/credit-card/T&Cs"},
				},
			},
		}
	}
}

func GetCreditCardUserIneligibleToOnboardScreen(ctx context.Context) (*deeplink.Deeplink, error) {

	appPlatform := epificontext.AppPlatformFromContext(ctx)
	var mainCta *deeplink.Cta
	switch {
	// Since android has special handling we will use custom screen option.
	case appPlatform == commontypes.Platform_ANDROID:
		mainCta = &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "Share feedback",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_FEEDBACK_ENGINE_CTA_FLOW_SCREEN,
			},
			DisplayTheme: deeplink.Cta_SECONDARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		}
	case appPlatform == commontypes.Platform_IOS:
		screenOptions := &cxScreenTypes.FeedbackEngineScreenOptions{
			FlowIdentifierDetails: &cxScreenTypes.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA,
				FlowIdentifier:     types.FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_CREDIT_CARD_USER_INELIGIBLE.String(),
			},
		}
		screenOptionsMarshalled, err := anyPb.New(screenOptions)
		if err != nil {
			return nil, errors.Wrap(err, "error marshalling screen options:")
		}
		mainCta = &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "Share feedback",
			Deeplink: &deeplink.Deeplink{
				Screen:          deeplink.Screen_FEEDBACK_ENGINE_SCREEN,
				ScreenOptionsV2: screenOptionsMarshalled,
			},
			DisplayTheme: deeplink.Cta_SECONDARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		}
	default:
		return nil, errors.New("could not determine the app platform from ctx")
	}

	screenOptions := &ffScreenTypes.CcIneligibleUserScreenOptions{
		Header: nil,
		TopSectionDetails: &deeplink.InfoItemV3{
			Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/black_yellow_card_4x.png").
				WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
				Width:  115,
				Height: 85,
			}),
			Title: &commontypes.Text{
				FontColor:    "#F6F9FD",
				DisplayValue: &commontypes.Text_Html{Html: "You’re not eligible for a <br>Credit Card at the moment"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_XL,
				},
			},
			Desc: &commontypes.Text{
				FontColor:    "#929599",
				DisplayValue: &commontypes.Text_Html{Html: "We will let you know once you are eligible"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BODY_S,
				},
			},
		},
		SeparatorVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/user_ineligible_separator_4x.png").
			WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  40,
			Height: 4,
		}),
		BottomSectionHeading: &commontypes.Text{
			FontColor:    "#F6F9FD",
			DisplayValue: &commontypes.Text_Html{Html: "Here are some factors that can <br>help improve eligibility"},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
			},
		},
		IneligibleUserDetails: []*ffScreenTypes.IneligibleUserDetails{
			{
				BottomSectionDetails: &deeplink.InfoItemWithCtaV3{
					Info: &deeplink.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/credit_score_meter_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Good credit score"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "A good credit score can help you get a<br> credit card easily"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
					Cta: &deeplink.Cta{
						Type: deeplink.Cta_CUSTOM,
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_ANALYSER_SCREEN,
							ScreenOptions: &deeplink.Deeplink_AnalyserScreenOptions{
								AnalyserScreenOptions: &deeplink.AnalyserScreenOptions{
									AnalyserName: analyser.AnalyserName_ANALYSER_NAME_CREDIT_SCORE.String(),
								},
							},
						},
						DisplayTheme: deeplink.Cta_SECONDARY,
						Status:       deeplink.Cta_CTA_STATUS_ENABLED,
						Text:         "Check your credit score",
						IconUrl:      "https://epifi-icons.pointz.in/credit_card_images/chevron_right_green_4x.png",
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
			{
				BottomSectionDetails: &deeplink.InfoItemWithCtaV3{
					Info: &deeplink.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/timely_repayments_green_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Timely repayments"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "Ensure timely credit card & loan<br> repayments"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
			{
				BottomSectionDetails: &deeplink.InfoItemWithCtaV3{
					Info: &deeplink.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/green_download_arrow_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Reduce hard enquiries"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "Avoid multiple credit enquiries in a<br> short period of time"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
			{
				BottomSectionDetails: &deeplink.InfoItemWithCtaV3{
					Info: &deeplink.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/red_clock_frame_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Long credit age"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "A long & healthy credit history<br> improves credit card approval chances"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
		},
		FooterText: &commontypes.Text{
			FontColor:    "#929599",
			DisplayValue: &commontypes.Text_Html{Html: "Note: This list is not exhaustive but covers <br>some common factors."},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_S,
			},
		},
		MainCta: mainCta,
	}

	screenOptionsMarshalled, err := anyPb.New(screenOptions)
	if err != nil {
		return nil, errors.Wrap(err, "error in marshalling screen options for user ineligible screen: ")
	}

	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_CC_INELIGIBLE_USER_SCREEN,
		ScreenOptionsV2: screenOptionsMarshalled,
	}, nil
}

func GetCcIneligibleScreenOptions(ctaText string, ctaDl *deeplink.Deeplink) *anyPb.Any {
	screenOptions := &ffScreenTypes.CcIneligibleUserScreenOptions{
		Header: nil,
		TopSectionDetails: &deeplink.InfoItemV3{
			Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/black_yellow_card_4x.png").
				WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
				Width:  115,
				Height: 85,
			}),
			Title: &commontypes.Text{
				FontColor:    "#F6F9FD",
				DisplayValue: &commontypes.Text_Html{Html: "You’re not eligible for a <br>Credit Card at the moment"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_XL,
				},
			},
			Desc: &commontypes.Text{
				FontColor:    "#929599",
				DisplayValue: &commontypes.Text_Html{Html: "We will let you know once you are eligible"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BODY_S,
				},
			},
		},
		SeparatorVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/user_ineligible_separator_4x.png").
			WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  40,
			Height: 4,
		}),
		BottomSectionHeading: &commontypes.Text{
			FontColor:    "#F6F9FD",
			DisplayValue: &commontypes.Text_Html{Html: "Here are some factors that can <br>help improve eligibility"},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
			},
		},
		IneligibleUserDetails: []*ffScreenTypes.IneligibleUserDetails{
			{
				BottomSectionDetails: &deeplink.InfoItemWithCtaV3{
					Info: &deeplink.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/credit_score_meter_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Good credit score"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "A good credit score can help you get a<br> credit card easily"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
					Cta: &deeplink.Cta{
						Type: deeplink.Cta_CUSTOM,
						Deeplink: &deeplink.Deeplink{
							Screen: deeplink.Screen_ANALYSER_SCREEN,
							ScreenOptions: &deeplink.Deeplink_AnalyserScreenOptions{
								AnalyserScreenOptions: &deeplink.AnalyserScreenOptions{
									AnalyserName: analyser.AnalyserName_ANALYSER_NAME_CREDIT_SCORE.String(),
								},
							},
						},
						DisplayTheme: deeplink.Cta_SECONDARY,
						Status:       deeplink.Cta_CTA_STATUS_ENABLED,
						Text:         "Check your credit score",
						IconUrl:      "https://epifi-icons.pointz.in/credit_card_images/chevron_right_green_4x.png",
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
			{
				BottomSectionDetails: &deeplink.InfoItemWithCtaV3{
					Info: &deeplink.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/timely_repayments_green_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Timely repayments"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "Ensure timely credit card & loan<br> repayments"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
			{
				BottomSectionDetails: &deeplink.InfoItemWithCtaV3{
					Info: &deeplink.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/green_download_arrow_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Reduce hard enquiries"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "Avoid multiple credit enquiries in a<br> short period of time"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
			{
				BottomSectionDetails: &deeplink.InfoItemWithCtaV3{
					Info: &deeplink.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/red_clock_frame_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Long credit age"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "A long & healthy credit history<br> improves credit card approval chances"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
		},
		FooterText: &commontypes.Text{
			FontColor:    "#929599",
			DisplayValue: &commontypes.Text_Html{Html: "Note: This list is not exhaustive but covers <br>some common factors."},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_S,
			},
		},
		MainCta: &deeplink.Cta{
			Type:         deeplink.Cta_CUSTOM,
			Text:         ctaText,
			Deeplink:     ctaDl,
			DisplayTheme: deeplink.Cta_PRIMARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		},
	}

	return deeplinkV3.GetScreenOptionV2WithoutError(screenOptions)
}

func GetCreditCardUserIneligibleToOnboardScreenForWorkflow() *deeplink.Deeplink {
	screenOptionsMarshalled := GetCcIneligibleScreenOptions("Explore Fi", &deeplink.Deeplink{
		Screen: deeplink.Screen_HOME,
	})

	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_CC_INELIGIBLE_USER_SCREEN,
		ScreenOptionsV2: screenOptionsMarshalled,
	}
}

func GetCcIneligibleScreenWithCustomCta(ctaText string, ctaDl *deeplink.Deeplink) *deeplink.Deeplink {
	screenOptionsMarshalled := GetCcIneligibleScreenOptions(ctaText, ctaDl)

	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_CC_INELIGIBLE_USER_SCREEN,
		ScreenOptionsV2: screenOptionsMarshalled,
	}
}

func GetSecuredCardsBillingCycleCta() *deeplink.Cta {
	screenOptions := &ffScreenTypes.CcIntroBottomSheetScreenOptions{
		Title: &commontypes.Text{
			FontColor:    "#313234",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "Earn rewards every day"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
		},
		Promotion: &ffFePb.PromotionInfo{
			PromotionWidgets: []*ffFePb.PromotionWidget{
				{
					Widget: &ffFePb.PromotionWidget_TravelWidget{
						TravelWidget: &ffFePb.TravelPromotionWidget{
							Title: &commontypes.Text{
								FontColor:    "#313234",
								DisplayValue: &commontypes.Text_PlainString{PlainString: "Get 5 Fi-Coins for every ₹100 spent"},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
								MaxLines:     2,
							},
							Desc: &commontypes.Text{
								FontColor:    "#6A6D70",
								DisplayValue: &commontypes.Text_PlainString{PlainString: "From Monday to Friday"},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
								MaxLines:     2,
							},
							Properties: &ffFePb.LayoutProperties{
								Size: &ffFePb.Size{
									Width:  -1,
									Height: -2,
								},
								Paddings: &ffFePb.PaddingProperty{
									Top:    20,
									Bottom: 10,
									End:    16,
									Start:  16,
								},
							},
							DrawableProperties: &ffFePb.DrawableProperties{
								BgColor: &widget.BackgroundColour{
									Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#F6F9FD"},
								},
								CornerProperty: &ffFePb.CornerProperty{
									TopStartCornerRadius: 19,
									TopEndCornerRadius:   19,
								},
							},
							VisualElement: commontypes.GetVisualElementImageFromUrl(firefly.GetSecuredCardsWeekdayRewardsDashboardIconUrl()).
								WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
								Width:  42,
								Height: 42,
							}),
						},
					},
				},
				{
					Widget: &ffFePb.PromotionWidget_TravelWidget{
						TravelWidget: &ffFePb.TravelPromotionWidget{
							Title: &commontypes.Text{
								FontColor:    "#313234",
								DisplayValue: &commontypes.Text_PlainString{PlainString: "Get 15 Fi-Coins for every ₹100 spent"},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
							},
							Desc: &commontypes.Text{
								FontColor:    "#6A6D70",
								DisplayValue: &commontypes.Text_PlainString{PlainString: "From Saturday to Sunday"},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
							},
							Properties: &ffFePb.LayoutProperties{
								Size: &ffFePb.Size{
									Width:  -1,
									Height: -2,
								},
								Paddings: &ffFePb.PaddingProperty{
									Bottom: 20,
									Top:    10,
									End:    16,
									Start:  16,
								},
							},
							DrawableProperties: &ffFePb.DrawableProperties{
								BgColor: &widget.BackgroundColour{
									Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#F6F9FD"},
								},
								CornerProperty: &ffFePb.CornerProperty{
									BottomStartCorner: 19,
									BottomEndCorner:   19,
								},
							},
							VisualElement: commontypes.GetVisualElementImageFromUrl(firefly.GetSecuredCardsWeekendRewardsDashboardIconUrl()).
								WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
								Width:  42,
								Height: 42,
							}),
						},
					},
				},
				{
					Widget: &ffFePb.PromotionWidget_TravelWidget{
						TravelWidget: &ffFePb.TravelPromotionWidget{
							Title: &commontypes.Text{
								FontColor:    "#929599",
								DisplayValue: &commontypes.Text_Html{Html: "• 3x rewards are not applicable on utilities,<br> insurance, education or post office-related spends."},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
							},
							Properties: &ffFePb.LayoutProperties{
								Size: &ffFePb.Size{
									Width:  -1,
									Height: -2,
								},
								Margins: &ffFePb.MarginProperty{
									Top:    24,
									Bottom: 8,
								},
							},
							DrawableProperties: &ffFePb.DrawableProperties{
								BgColor: &widget.BackgroundColour{
									Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"},
								},
							},
						},
					},
				},
				{
					Widget: &ffFePb.PromotionWidget_TravelWidget{
						TravelWidget: &ffFePb.TravelPromotionWidget{
							Title: &commontypes.Text{
								FontColor:    "#929599",
								DisplayValue: &commontypes.Text_Html{Html: "• Wallet reloads, cash advances, fees & charges or<br>repayments and any other credit transactions are not<br>eligible for any form of rewards."},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
							},
							Properties: &ffFePb.LayoutProperties{
								Size: &ffFePb.Size{
									Width:  -1,
									Height: -2,
								},
								Margins: &ffFePb.MarginProperty{
									Top:    8,
									Bottom: 16,
								},
							},
							DrawableProperties: &ffFePb.DrawableProperties{
								BgColor: &widget.BackgroundColour{
									Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"},
								},
							},
						},
					},
				},
			},
			Properties: &ffFePb.LayoutProperties{
				Size: &ffFePb.Size{
					Width:  -1,
					Height: -2,
				},
				Margins: &ffFePb.MarginProperty{
					Start:  0,
					End:    0,
					Top:    16,
					Bottom: 16,
				},
			},
		},
		VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/fi_gold_coins_4x.png").
			WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  74,
			Height: 74,
		}),
	}
	bottomSheetCta, _ := deeplinkV3.GetDeeplinkV3(deeplink.Screen_CC_INTRO_BOTTOM_SHEET, screenOptions)
	return &deeplink.Cta{
		Type:     deeplink.Cta_CUSTOM,
		Text:     "HOW IT WORKS",
		Deeplink: bottomSheetCta,
	}
}

func GetMassUnsecuredCardsBillingCycleCta() *deeplink.Cta {
	screenOptions := &ffScreenTypes.CcIntroBottomSheetScreenOptions{
		VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/fi_gold_coins_4x.png").
			WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  74,
			Height: 74,
		}),
		Promotion: &ffFePb.PromotionInfo{
			PromotionWidgets: []*ffFePb.PromotionWidget{
				{
					Widget: &ffFePb.PromotionWidget_PromotionVisualElement{
						PromotionVisualElement: &ffFePb.WrappedVisualElement{
							VisualElement: &commontypes.VisualElement{
								Asset: &commontypes.VisualElement_Image_{
									Image: &commontypes.VisualElement_Image{
										Source: &commontypes.VisualElement_Image_Url{
											Url: "https://epifi-icons.pointz.in/credit_card_images/mass_unsecured_rewards_bottom_HIW_4x.png",
										},
										ImageType: commontypes.ImageType_PNG,
										Properties: &commontypes.VisualElementProperties{
											Width:  328,
											Height: 296,
										},
									},
								},
							},
							Properties: &ffFePb.LayoutProperties{
								Size: &ffFePb.Size{
									// wrapContent
									Width: -2,
									// wrapContent
									Height: -2,
								},
							},
						},
					},
				},
				{
					Widget: &ffFePb.PromotionWidget_TravelWidget{
						TravelWidget: &ffFePb.TravelPromotionWidget{
							Title: &commontypes.Text{
								FontColor:    "#929599",
								DisplayValue: &commontypes.Text_Html{Html: "Wallet reloads, cash advances, fees & charges or<br> repayments and any other credit transactions are<br>not eligible for any form of rewards."},
								FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
							},
							Properties: &ffFePb.LayoutProperties{
								Size: &ffFePb.Size{
									Width:  -1,
									Height: -2,
								},
							},
							DrawableProperties: &ffFePb.DrawableProperties{
								BgColor: &widget.BackgroundColour{
									Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"},
								},
							},
						},
					},
				},
			},
			Properties: &ffFePb.LayoutProperties{
				Size: &ffFePb.Size{
					Width:  -1,
					Height: -2,
				},
				Margins: &ffFePb.MarginProperty{
					Start: 0,
					End:   0,
				},
			},
		},
	}
	bottomSheetCta, _ := deeplinkV3.GetDeeplinkV3(deeplink.Screen_CC_INTRO_BOTTOM_SHEET, screenOptions)
	return &deeplink.Cta{
		Type:     deeplink.Cta_CUSTOM,
		Text:     "HOW IT WORKS",
		Deeplink: bottomSheetCta,
	}
}

func getBenefitsFromCardProgram(cardProgram *types.CardProgram) []*deeplink.InfoItemV2 {
	switch cardProgram.GetCardProgramType() {
	case types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:
		return []*deeplink.InfoItemV2{
			{
				Icon: "https://epifi-icons.pointz.in/credit_card_images/3_percent_green.png",
				Title: &commontypes.Text{
					FontColor: "#6A6D70",
					DisplayValue: &commontypes.Text_Html{
						Html: "back on top<br/>20+ brands",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			},
			{
				Icon: "https://epifi-icons.pointz.in/credit_card_images/0_percent_green.png",
				Title: &commontypes.Text{
					FontColor: "#6A6D70",
					DisplayValue: &commontypes.Text_Html{
						Html: "0 Forex<br/> markup charges",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			},
			{
				Icon: "https://epifi-icons.pointz.in/credit_card_images/heart_green.png",
				Title: &commontypes.Text{
					FontColor: "#6A6D70",
					DisplayValue: &commontypes.Text_Html{
						Html: "Welcome gifts worth ₹4,000+",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			},
		}
	case types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
		return []*deeplink.InfoItemV2{
			{
				Icon: "https://epifi-icons.pointz.in/credit_card_images/20_percent_green.png",
				Title: &commontypes.Text{
					FontColor: "#6A6D70",
					DisplayValue: &commontypes.Text_Html{
						Html: "Cashback on weekends*",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			},
			{
				Icon: "https://epifi-icons.pointz.in/credit_card_images/tick_green.png",
				Title: &commontypes.Text{
					FontColor: "#6A6D70",
					DisplayValue: &commontypes.Text_Html{
						Html: "Lifetime<br/>free<br/>",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			},
			{
				Icon: "https://epifi-icons.pointz.in/credit_card_images/rupee_green.png",
				Title: &commontypes.Text{
					FontColor: "#6A6D70",
					DisplayValue: &commontypes.Text_Html{
						Html: "Save ₹1,000<br/> monthly",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			},
		}
	default:
		return []*deeplink.InfoItemV2{
			{
				Icon: "https://epifi-icons.pointz.in/credit_card_images/3_percent_green.png",
				Title: &commontypes.Text{
					FontColor: "#6A6D70",
					DisplayValue: &commontypes.Text_Html{
						Html: "back on top<br/>20+ brands",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			},
			{
				Icon: "https://epifi-icons.pointz.in/credit_card_images/0_percent_green.png",
				Title: &commontypes.Text{
					FontColor: "#6A6D70",
					DisplayValue: &commontypes.Text_Html{
						Html: "0 Forex<br/> markup charges",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			},
			{
				Icon: "https://epifi-icons.pointz.in/credit_card_images/heart_green.png",
				Title: &commontypes.Text{
					FontColor: "#6A6D70",
					DisplayValue: &commontypes.Text_Html{
						Html: "Welcome gifts worth ₹4,000+",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
					},
				},
			},
		}
	}
}

func GetGenericPermanentFailureScreen() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_GENERIC_HALT_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardHaltScreenOptions{
			CreditCardHaltScreenOptions: &deeplink.CreditCardHaltScreenOptions{
				IconUrl: "https://epifi-icons.pointz.in/credit_card_images/HL_main_card_with_coin.png",
				Text:    "You are not eligible for a Credit Card at the moment",
				SubText: "Few reasons why this may have happened:" +
					"<br>1. Low credit score or poor credit history on other loans/cards." +
					"<br>2. We may not be offering cards at your location currently." +
					"<br>3. There are age restrictions which may apply." +
					"<br>4. Other active relationships with our partner bank." +
					"<br>These reasons are only some possible reasons why you are not yet eligible for a credit card. Please note that there may be other reasons as per bank policy.",
			},
		},
	}
}

// nolint
func GetSecuredProgramCreditCardUserIneligibleToOnboardScreen(ctx context.Context) (*deeplink.Deeplink, error) {
	appPlatform := epificontext.AppPlatformFromContext(ctx)
	var mainCta *deeplink.Cta
	switch {
	// Since android has special handling we will use custom screen option.
	case appPlatform == commontypes.Platform_ANDROID:
		mainCta = &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "Share feedback",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_FEEDBACK_ENGINE_CTA_FLOW_SCREEN,
			},
			DisplayTheme: deeplink.Cta_SECONDARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		}
	case appPlatform == commontypes.Platform_IOS:
		screenOptions := &cxScreenTypes.FeedbackEngineScreenOptions{
			FlowIdentifierDetails: &cxScreenTypes.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA,
				FlowIdentifier:     types.FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_CREDIT_CARD_USER_INELIGIBLE.String(),
			},
		}
		screenOptionsMarshalled, err := anyPb.New(screenOptions)
		if err != nil {
			return nil, errors.Wrap(err, "error marshalling screen options:")
		}
		mainCta = &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "Share feedback",
			Deeplink: &deeplink.Deeplink{
				Screen:          deeplink.Screen_FEEDBACK_ENGINE_SCREEN,
				ScreenOptionsV2: screenOptionsMarshalled,
			},
			DisplayTheme: deeplink.Cta_SECONDARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		}
	default:
		return nil, errors.New("could not determine the app platform from ctx")
	}

	screenOptions := &ffScreenTypes.CcIneligibleUserScreenOptions{
		Header: nil,
		TopSectionDetails: &deeplink.InfoItemV3{
			Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/simplifi_cc_user_ineligible_screen_4x.png").
				WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
				Width:  115,
				Height: 85,
			}),
			Title: &commontypes.Text{
				FontColor:    "#F6F9FD",
				DisplayValue: &commontypes.Text_Html{Html: "You are not eligible for a Credit Card at the moment"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_HEADLINE_XL,
				},
			},
			Desc: &commontypes.Text{
				FontColor:    "#929599",
				DisplayValue: &commontypes.Text_Html{Html: "We will let you know once you are eligible"},
				FontStyle: &commontypes.Text_StandardFontStyle{
					StandardFontStyle: commontypes.FontStyle_BODY_S,
				},
			},
		},
		SeparatorVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/user_ineligible_separator_4x.png").
			WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  40,
			Height: 4,
		}),
		BottomSectionHeading: &commontypes.Text{
			FontColor:    "#F6F9FD",
			DisplayValue: &commontypes.Text_Html{Html: "Here are some reasons why"},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_M,
			},
		},
		IneligibleUserDetails: []*ffScreenTypes.IneligibleUserDetails{
			{
				BottomSectionDetails: &deeplink.InfoItemWithCtaV3{
					Info: &deeplink.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/internal_policies_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Internal policies"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "As per bank's internal policies, you don't yet<br> qualify for this Credit Card.<br><br>But don't let that get you down,<br> because we are reviewing & updating<br> our policies to ensure that we can get<br> this card to you!"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: false,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/chevron_upper_gray_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
			{
				BottomSectionDetails: &deeplink.InfoItemWithCtaV3{
					Info: &deeplink.InfoItemV3{
						Icon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/existing_card_holder_4x.png").
							WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
							Width:  48,
							Height: 48,
						}),
						Title: &commontypes.Text{
							FontColor:    "#F6F9FD",
							DisplayValue: &commontypes.Text_Html{Html: "Existing card holder"},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_SUBTITLE_M,
							},
						},
						SubTitle: &commontypes.Text{
							FontColor:    "#929599",
							DisplayValue: &commontypes.Text_Html{Html: "You are already a Federal Bank Credit<br> Card holder."},
							FontStyle: &commontypes.Text_StandardFontStyle{
								StandardFontStyle: commontypes.FontStyle_BODY_S,
							},
						},
					},
				},
				IsCollapsed: true,
				Chevron: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/lower_arrow_grey_4x.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  24,
					Height: 24,
				}),
			},
		},
		FooterText: &commontypes.Text{
			FontColor:    "#929599",
			DisplayValue: &commontypes.Text_Html{Html: "Note: This list is not exhaustive but covers <br>some common factors."},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_S,
			},
		},
		MainCta: mainCta,
	}

	screenOptionsMarshalled, err := anyPb.New(screenOptions)
	if err != nil {
		return nil, errors.Wrap(err, "error in marshalling screen options for user ineligible screen: ")
	}

	return &deeplink.Deeplink{
		Screen:          deeplink.Screen_CC_INELIGIBLE_USER_SCREEN,
		ScreenOptionsV2: screenOptionsMarshalled,
	}, nil
}

func GetCcCreditReportAddressScreen(cardRequestID string) *deeplink.Deeplink {
	screenOptions := &ffScreenTypes.CcCreditReportAddressScreenOptions{
		Header: nil,
		TopSection: &deeplink.InfoItemV3{
			Icon: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{
					Url: "https://epifi-icons.pointz.in/credit-card/credit-report-address-top-icon.png",
				},
				ImageType: commontypes.ImageType_PNG,
				Properties: &commontypes.VisualElementProperties{
					Width:  91,
					Height: 91,
				},
			}}},
			Title:    commontypes.GetTextFromStringFontColourFontStyle("What's your current address?", "#18191B", commontypes.FontStyle_HEADLINE_L),
			SubTitle: commontypes.GetTextFromHtmlStringFontColourFontStyle("We need this to check your eligibility & <br> to ship your card to this address.", "#6A6D70", commontypes.FontStyle_SUBTITLE_S),
		},

		AddressSectionProperties: &ffFePb.DrawableProperties{
			BgColor: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{
					BlockColour: "#FFFFFF",
				},
			},
			BorderProperty: &ffFePb.BorderProperty{
				BorderThickness: 1,
				BorderColor:     "#E6E9ED",
			},
			CornerProperty: &ffFePb.CornerProperty{
				TopStartCornerRadius: 19,
				TopEndCornerRadius:   19,
				BottomStartCorner:    19,
				BottomEndCorner:      19,
			},
		},
		ContinueCta: &deeplink.Cta{
			Text: "Continue",
			Type: deeplink.Cta_CUSTOM,
		},
		ToolBar: &ffFePb.WrappedIconTextToolBar{
			VisualElement: &ffFePb.WrappedVisualElement{
				VisualElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.pointz.in/credit_card/address_screen/fi_logo.png",
							},
							Properties: &commontypes.VisualElementProperties{
								Height: 48,
								Width:  48,
							},
							ImageType: commontypes.ImageType_PNG,
						},
					},
				},
				Properties: &ffFePb.LayoutProperties{
					Size: &ffFePb.Size{Width: -1, Height: -2},
				},
			},
		},
		BackgroundColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_LinearGradient{LinearGradient: &widget.LinearGradient{
				Degree: 0,
				LinearColorStops: []*widget.ColorStop{
					{
						Color:          "#CFE4F2",
						StopPercentage: 30,
					},
					{
						Color:          "#C8E3DF",
						StopPercentage: 60,
					},
					{
						Color:          "#FFFFFF",
						StopPercentage: 60,
					},
				},
			}},
		},
		AddAddressCta: &ui.IconTextComponent{
			Texts: []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Add new address", "#00B899", commontypes.FontStyle_SUBTITLE_S)},
			LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{Image: &commontypes.VisualElement_Image{
				Source: &commontypes.VisualElement_Image_Url{
					Url: "https://epifi-icons.pointz.in/credit_card/credit_report_address/add_address_icon.png",
				},
				ImageType: commontypes.ImageType_PNG,
				Properties: &commontypes.VisualElementProperties{
					Width:  14,
					Height: 14,
				},
			}}},
			LeftImgTxtPadding: 4,
		},
		CardRequestId: cardRequestID,
	}

	return deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_CC_CREDIT_REPORT_ADDRESS_SCREEN, screenOptions)
}

func GetCcIneligibleTransitionScreen(cardProgram *types.CardProgram) (*deeplink.Deeplink, error) {
	var (
		screenIdentifer   int32
		cardProgramOrigin = types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI
		mostImportantTnCs string
	)
	if cardProgram.GetCardProgramOrigin() == types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE {
		cardProgramOrigin = types.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE
		screenIdentifer = int32(deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API)
	}
	switch cardProgram.GetCardProgramType() {
	case types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
		mostImportantTnCs = "https://fi.money/documents/credit-card/magnifi/important-tnc"
	default:
		mostImportantTnCs = "https://fi.money/credit-card/important-T&Cs"
	}
	simpliFiCardProgram := &types.CardProgram{
		CardProgramVendor:     types.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
		CardProgramSource:     types.CardProgramSource_CARD_PROGRAM_SOURCE_REALTIME_BRE,
		CardProgramType:       types.CardProgramType_CARD_PROGRAM_TYPE_SECURED,
		CardProgramCollateral: types.CardProgramCollateral_CARD_PROGRAM_COLLATERAL_FD,
		CardProgramOrigin:     cardProgramOrigin,
	}

	screenOptionsFeedbackEngineMarshalled, err := anyPb.New(
		&cxScreenTypes.FeedbackEngineScreenOptions{
			FlowIdentifierDetails: &cxScreenTypes.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA,
				FlowIdentifier:     types.FeedbackCTAFlowIdentifier_FEEDBACK_CTA_FLOW_IDENTIFIER_CREDIT_CARD_USER_INELIGIBLE.String(),
			},
		})

	screenOptionsCcIntroMarshalled := deeplinkV3.GetScreenOptionV2WithoutError(
		&ffScreenTypes.CcIntroScreenOptions{
			Header:           nil,
			ScreenIdentifier: screenIdentifer,
			CardProgram:      simpliFiCardProgram,
			CreditCardRequestHeader: &types.CreditCardRequestHeader{
				CardProgram: firefly.GetCardProgramStringFromCardProgram(simpliFiCardProgram),
			},
		},
	)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling screen options:")
	}
	screenOptions := &ffScreenTypes.CcUserIneligibleTransitionScreenOptions{
		Header: nil,
		ScreenHeaderVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/ineligible_transition_screen_amplifi_card_image.png").
			WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  80,
			Height: 80,
		}),
		Title: &commontypes.Text{
			FontColor:    "#313234",
			DisplayValue: &commontypes.Text_Html{Html: fmt.Sprintf("Looks like you’re <br>not eligible for %s", firefly.GetCardProgramStringFromCardProgram(cardProgram))},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_XL,
			},
		},
		SubTitle: &ui.TextWithHyperlinks{
			Text: &commontypes.Text{
				FontColor:    "#929599",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "We’re sorry about this. Know more"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
			},
			// TODO: Add corresponding deeplink here for know more
			HyperlinkMap: map[string]*ui.HyperLink{
				"Know more": {
					Link: &ui.HyperLink_Url{Url: mostImportantTnCs},
				},
			},
		},
		InfoText: &commontypes.Text{
			FontColor:    "#007A56",
			DisplayValue: &commontypes.Text_Html{Html: "You can still apply <br>for SimpliFi!"},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_HEADLINE_L,
			},
		},
		InfoVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/credit_card_images/simplifi_benefits_card_info.png").
			WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
			Width:  332,
			Height: 200,
		}),
		ShareFeedbackCta: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "Share feedback",
			Deeplink: &deeplink.Deeplink{
				Screen:          deeplink.Screen_FEEDBACK_ENGINE_SCREEN,
				ScreenOptionsV2: screenOptionsFeedbackEngineMarshalled,
			},
			DisplayTheme: deeplink.Cta_SECONDARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		},
		GetCardCta: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: "Get SimpliFi",
			Deeplink: &deeplink.Deeplink{
				Screen:          deeplink.Screen_CC_INTRO_SCREEN,
				ScreenOptionsV2: screenOptionsCcIntroMarshalled,
			},
			DisplayTheme: deeplink.Cta_PRIMARY,
			Status:       deeplink.Cta_CTA_STATUS_ENABLED,
		},
		ToolBar: &ffFePb.WrappedIconTextToolBar{
			BackArrowColor: "#646464",
			VisualElement: &ffFePb.WrappedVisualElement{
				VisualElement: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: "https://epifi-icons.pointz.in/credit_card/address_screen/fi_logo.png",
							},
							Properties: &commontypes.VisualElementProperties{
								Height: 48,
								Width:  48,
							},
							ImageType: commontypes.ImageType_PNG,
						},
					},
				},
				Properties: &ffFePb.LayoutProperties{
					Size: &ffFePb.Size{Width: -1, Height: -2},
				},
			},
		},
	}
	return deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_CC_USER_INELIGIBLE_TRANSITION_SCREEN, screenOptions), nil
}

func GetCreditCardDashboardDeeplinkWithRewardsCardTypeId(cardTypeId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_DASHBOARD_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardDashboardScreenOptions{
			CreditCardDashboardScreenOptions: &deeplink.CreditCardDashboardScreenOptions{
				RewardsCardTypeId: cardTypeId,
			}},
	}
}
