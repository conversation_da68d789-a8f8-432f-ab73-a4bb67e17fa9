//nolint:dupl,funlen
package helper

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/gamma/api/frontend/deeplink"
	ffPb "github.com/epifi/gamma/api/frontend/firefly"
	"github.com/epifi/gamma/api/frontend/firefly/enums"
	types "github.com/epifi/gamma/api/typesv2"
	ffScreenTypes "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/firefly"
	dynconf "github.com/epifi/gamma/firefly/config/genconf"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/gamma/pkg/firefly"
	"github.com/epifi/be-common/pkg/frontend/app"
	pkggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
)

const (
	enableMassUnsecuredOnboardingV2      = "EnableMassUnsecuredOnboardingV2"
	enableUnsecuredOnboardingV2          = "EnableUnsecuredOnboardingV2"
	enableUnsecuredConsentFlowOnboarding = "EnableUnsecuredConsentFlowOnboarding"
	trueString                           = "TRUE"
)

var (
	cardProgramTypeToEnumMap = map[types.CardProgramType]enums.CardProgramType{
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED: enums.CardProgramType_CARD_PROGRAM_TYPE_UNSPECIFIED,
		types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:     enums.CardProgramType_CARD_PROGRAM_TYPE_SECURED,
		types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:   enums.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED,
	}
)

// Deprecated
func GetCreditCardOffersDeeplink(customerName *commontypes.Name, creditLimit *moneyPb.Money) *deeplink.Deeplink {
	limitAmountString := fmt.Sprintf("Credit limit: <b>Up to ₹%s</b>", "10 lakh")
	isAmountGreaterThanThreshold, err := money.CompareV2(creditLimit, thresholdForLimit)
	if err == nil && isAmountGreaterThanThreshold >= 0 && creditLimit != nil {
		limitAmountString = fmt.Sprintf("Credit limit: <b>%s</b>",
			money.ToDisplayStringInIndianFormat(creditLimit, 0, true))
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CARD_OFFERS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_GetOffersScreenOptions{GetOffersScreenOptions: &deeplink.GetOffersScreenOptions{
			CardImageUrl: "https://epifi-icons.pointz.in/credit_card_images/card_img2.png",
			// Deprecated
			InfoBlock: cardDetails,
			// Deprecated
			KnowMore: &deeplink.Cta{
				Text: "Know More",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CARD_KNOW_MORE_SCREEN,
				},
			},
			// Deprecated
			PopUpTextBlock: &deeplink.InfoItemWithCta{
				Info: &deeplink.InfoItem{
					Title: "Does this card come at no cost?",
					Desc:  fmt.Sprintf("You will be charged a joining fee of ₹2000 for the %s credit card, which will be added to your first bill.\nAn annual fee of ₹2000 for your credit card will also be charged, but if you spend approximately ₹2.5L in an year, it will get waived off.", firefly.CreditCardType),
				},
				Cta: &deeplink.Cta{Text: "Okay, got it"},
			},
			TncString:      fmt.Sprintf("Read Federal Bank’s <a href=%s> Most Important Terms & Conditions</a>, <a href=%s>Key Fact Statement</a> and <a href=%s>Terms & Conditions</a>", "https://fi.money/credit-card/important-T&Cs", "https://fi.money/credit-card/key-fact-statement", "https://fi.money/credit-card/T&Cs"),
			PartnershipUrl: "https://epifi-icons.pointz.in/credit_card_images/PartnerIcon.png",
			GetCreditCard: &deeplink.Cta{
				Text: "Love rewards? Get this card now!",
			},
			Title:               "You spend, we reward. Every single time.",
			HeaderDescription:   "Earn yearly benefits worth ₹20,000",
			CustomerName:        customerName,
			CreditLimitStr:      limitAmountString,
			WelcomeVouchers:     GetWelcomeVouchersInfoItemWithCTA(),
			ValuebackCheatsheet: GetValueBackCheatSheetInfoItemWithCTA(),
			Brands:              GetTopBrandsInfoItemWithCTA(),
			StaticImages: []*deeplink.InfoItem{
				{
					Icon:  "https://epifi-icons.pointz.in/credit_card_images/coin_benifits_intro_4x.png",
					Title: "Don't just collect Fi-Coins.\nPut them to use.",
				},
				{
					Icon:  "https://epifi-icons.pointz.in/credit_card_images/annual_milestone_rewards_4x.png",
					Title: "Annual milestone rewards \nworth over ₹10,000",
				},
				{
					Icon:  "https://epifi-icons.pointz.in/credit_card_images/extra_benifits_intro_4x.png",
					Title: "That’s not all!",
				},
			},
			FeesInfoHeader: "Great things come at a price.\nFees & Charges.",
			FeesInfo: []*deeplink.GetOffersScreenOptions_InfoItemWithBadgeInfoAndCta{
				{
					Info: &deeplink.InfoItemWithCta{
						Info: &deeplink.InfoItem{
							Title: "Joining fee: ₹2,000",
							Desc:  "Get <a href=>welcome vouchers worth ₹5,000</a> 🎁",
						},
						Cta: &deeplink.Cta{
							Deeplink: GetWelcomeVouchersDeeplink(),
						},
					},
					BadgeText: &commontypes.Text{
						BgColor:      "#383838",
						FontColor:    "#A4A4A4",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "BILLED ON SIGN UP"},
					},
				},
				{
					Info: &deeplink.InfoItemWithCta{
						Info: &deeplink.InfoItem{
							Title: "Renewal fee: ₹2,000",
							Desc:  "Waived off if spends are more than ₹2.5 lakh in the year.",
						},
						Cta: &deeplink.Cta{
							Text:    "View all fees & charges",
							Weblink: "https://fi.money/credit-card/key-fact-statement",
						},
					},
					BadgeText: &commontypes.Text{
						BgColor:      "#383838",
						FontColor:    "#A4A4A4",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "BILLED AFTER 1 YEAR"},
					},
				},
			},
			RewardsWorth: &deeplink.InfoItemWithCtaV2{
				Info: &deeplink.InfoItemV2{
					Icon: "",
					Title: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "Annual rewards worth over"},
					},
					Desc: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: "₹20,000"},
					},
				},
				Cta: &deeplink.Cta{
					Text: "Know more",
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CREDIT_CARD_INTRO_REWARD_ESTIMATION_SCREEN,
						ScreenOptions: &deeplink.Deeplink_CreditCardIntroRewardEstimationScreenOptions{
							CreditCardIntroRewardEstimationScreenOptions: &deeplink.CreditCardIntroRewardEstimationScreenOptions{
								ScreenTitle: "Get 5% valueback annually",
								Description: "Valueback is the rupee value of the benefits you will receive. You can get 5% or more of your annual spends using this card.",
								Subtitle:    "See how much you can earn ",
								Slider: &deeplink.CreditCardIntroRewardEstimationScreenOptions_Slider{
									CurrentlySelectedValue: 35000,
									SliderLowerLimit:       15000,
									SliderUpperLimit:       60000,
									SkipValue:              5000,
								},
							},
						},
					},
				},
			},
			BroadVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: "https://epifi-icons.pointz.in/credit_card_images/full_width_5x_valueback_img_4x.png",
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  430,
							Height: 160,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
			FeeInfoV2: []*deeplink.InfoItemWithCtaV2WithInfoItemV2AndBadge{
				{
					Info: &deeplink.InfoItemV2{
						Icon: "",
						Title: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Joining Fee"},
						},
						Desc: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "₹2,000"},
						},
					},
					BadgeInfo: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: " • Billed on sign up"},
					},
					InfoWithCta: GetJoiningFeeVoucherInfo(),
				},
				{
					Info: &deeplink.InfoItemV2{
						Icon: "",
						Title: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Renewal fee"},
						},
						Desc: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "₹2,000"},
						},
						SubTitle: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{PlainString: "Waived off if spends are more than ₹2.5 lakh in the year."},
						},
					},
					BadgeInfo: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: " • Billed after 1 year"},
					},
				},
			},
			AllFeeCta: &deeplink.Cta{
				Text:    "View all fees & charges",
				Weblink: "https://fi.money/credit-card/key-fact-statement",
			},
			AcceleratedRewardInfo: &deeplink.GetOffersScreenOptions_AcceleratedRewardInfo{
				AcceleratedRewardTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Accelerated rewards on every spend"},
				},

				AcceleratedRewardV2: GetAcceleratedRewardsInfo(),
			},
			FiCollectionTitle: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Learn about the Fi Collection"},
			},
			FiCollectionCard: GetFiBrandCollectionInfo(),
		}},
	}
}

func GetWelcomeVouchersInfoItemWithCTA() *deeplink.InfoItemWithCta {
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/Welcome_voucher_v2.png",
			Title: "Get welcome gift cards worth over ₹5,000!",
		},
		Cta: &deeplink.Cta{
			Text:     "View details",
			Deeplink: GetWelcomeVouchersDeeplink(),
		},
	}
}

func GetValueBackCheatSheetInfoItemWithCTA() *deeplink.InfoItemWithCta {
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/5%25_across.png",
			Title: "Get annual benefits worth up to ₹20,000",
		},
		Cta: &deeplink.Cta{
			Text: "Know more",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_INTRO_REWARD_ESTIMATION_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CreditCardIntroRewardEstimationScreenOptions{
					CreditCardIntroRewardEstimationScreenOptions: &deeplink.CreditCardIntroRewardEstimationScreenOptions{
						ScreenTitle: "Get 5% valueback annually",
						Description: "Valueback is the rupee value of the benefits you will receive. You can get 5% or more of your annual spends using this card.",
						Subtitle:    "See how much you can earn ",
						Slider: &deeplink.CreditCardIntroRewardEstimationScreenOptions_Slider{
							CurrentlySelectedValue: 35000,
							SliderLowerLimit:       15000,
							SliderUpperLimit:       60000,
							SkipValue:              5000,
						},
					},
				},
			},
		},
	}
}

func GetTopBrandsInfoItemWithCTA() *deeplink.InfoItemWithCta {
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/top_brands_icons.png",
			Title: "Up to 5x rewards on 20+ top brands",
		},
		Cta: &deeplink.Cta{
			Text: "Know more",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_INTRO_SCREEN_BRANDS,
				ScreenOptions: &deeplink.Deeplink_CreditCardIntroScreenBrandScreenOptions{
					CreditCardIntroScreenBrandScreenOptions: &deeplink.CreditCardIntroScreenBrandScreenOptions{
						RewardDescriptions: &deeplink.InfoBlock{
							Title: "Rewards that evolve to match your spends",
							InfoItems: []*deeplink.InfoItem{
								{
									Icon:  "https://epifi-icons.pointz.in/credit_card_images/1x_image.png",
									Title: "On all spends, including fuel",
									Desc:  "Get 1 Fi-Coin for every ₹5 spent",
								},
								{
									Icon:  "https://epifi-icons.pointz.in/credit_card_images/2x_image.png",
									Title: "On brands listed in the Fi Collection",
									Desc:  "Get 2 Fi-Coins for every ₹5 spent",
								},
								{
									Icon:  "https://epifi-icons.pointz.in/credit_card_images/5x_reward_img.png",
									Title: "On your top 3 brands from the Fi Collection",
									Desc:  "Get 5 Fi-Coins for every ₹5 spent",
								},
							},
						},
						TopBrandsImage: "https://epifi-icons.pointz.in/credit_card_images/top_brands_2.png",
						AllBrandList: &deeplink.InfoBlock{
							Title:     "The Fi Collection",
							Desc:      "An ever-expanding list of India’s top brands",
							InfoItems: getAllBrands(),
						},
						BrandsLottie: &commontypes.VisualElement{
							Asset: &commontypes.VisualElement_Lottie_{
								Lottie: &commontypes.VisualElement_Lottie{
									Source: &commontypes.VisualElement_Lottie_Url{
										Url: "https://epifi-icons.pointz.in/credit_card_images/cc_rewards.json",
									},
									Properties: &commontypes.VisualElementProperties{
										Width:  290,
										Height: 175,
									},
								},
							},
						},
					},
				},
			},
		},
	}
}

func GetTopBrandsInfoItemWithCTAV2(ctx context.Context) *deeplink.InfoItemWithCta {
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:  "https://epifi-icons.pointz.in/credit_card_images/top_brands_icons.png",
			Title: "Up to 5x rewards on 20+ top brands",
		},
		Cta: &deeplink.Cta{
			Text: "Know more",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_INTRO_SCREEN_BRANDS,
				ScreenOptions: &deeplink.Deeplink_CreditCardIntroScreenBrandScreenOptions{
					CreditCardIntroScreenBrandScreenOptions: &deeplink.CreditCardIntroScreenBrandScreenOptions{
						RewardDescriptions: &deeplink.InfoBlock{
							Title: "Rewards that evolve to match your spends",
							InfoItems: []*deeplink.InfoItem{
								{
									Icon:  "https://epifi-icons.pointz.in/credit_card_images/1x_image.png",
									Title: "On all spends, including fuel",
									Desc:  "Get 1 Fi-Coin for every ₹5 spent",
								},
								{
									Icon:  "https://epifi-icons.pointz.in/credit_card_images/2x_image.png",
									Title: "On brands listed in the Fi Collection",
									Desc:  "Get 2 Fi-Coins for every ₹5 spent",
								},
								{
									Icon:  "https://epifi-icons.pointz.in/credit_card_images/5x_reward_img.png",
									Title: "On your top 3 brands from the Fi Collection",
									Desc:  "Get 5 Fi-Coins for every ₹5 spent",
								},
							},
						},
						TopBrandsImage: "https://epifi-icons.pointz.in/credit_card_images/top_brands_2.png",
						AllBrandList: &deeplink.InfoBlock{
							Title:     "The Fi Collection",
							Desc:      "An ever-expanding list of India’s top brands",
							InfoItems: getAllBrands(),
						},
						BrandsLottie: getTopBrandLottieElement(ctx),
					},
				},
			},
		},
	}
}

func GetWelcomeVouchersDeeplink() *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_WELCOME_VOUCHERS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardWelcomeVouchersScreenOptions{
			CreditCardWelcomeVouchersScreenOptions: &deeplink.CreditCardWelcomeVouchersScreenOptions{
				VoucherInfo: &deeplink.InfoBlock{
					Title: "Welcome Vouchers worth over ₹5,000!",
					InfoItems: []*deeplink.InfoItem{
						{
							Icon:  "https://epifi-icons.pointz.in/credit_card_images/sony_liv_img.png",
							Title: "1 year subscription to Sony Liv worth ₹999",
						},
						{
							Icon:  firefly.GetMerchantLogoByMerchantName("uber"),
							Title: "Uber vouchers worth ₹500",
						},
						{
							Icon:  firefly.GetMerchantLogoByMerchantName("myntra"),
							Title: "Myntra vouchers worth ₹1000",
						},
						{
							Icon:  firefly.GetMerchantLogoByMerchantName("croma"),
							Title: "Croma voucher worth ₹500",
						},
						{
							Icon:  firefly.GetMerchantLogoByMerchantName("zomato"),
							Title: "Zomato vouchers worth ₹1000",
						},
						{
							Icon:  firefly.GetMerchantLogoByMerchantName("urban_company"),
							Title: "6 month Urban Company Plus membership worth ₹699",
						},
						{
							Icon:  "https://epifi-icons.pointz.in/credit_card_images/tattva_brand_img.png",
							Title: "₹1,000 off on a Tattva Wellness treatment for you or your loved ones",
						},
					},
				},
				BottomInfoItem: &deeplink.InfoItem{
					Icon:  "https://epifi-icons.pointz.in/credit_card_images/information.png",
					Title: fmt.Sprintf("This offer will be unlocked once you pay the Joining Fee on your bill for your %s Credit Card.", firefly.CreditCardType),
				},
			},
		},
	}
}

func GetWelcomeVouchersBottomSheet() *deeplink.Deeplink {
	return deeplinkv3.GetDeeplinkV3WithoutError(deeplink.Screen_CC_INTRO_BOTTOM_SHEET,
		&ffScreenTypes.CcIntroBottomSheetScreenOptions{
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{Html: "Welcome gift cards worth over ₹4,000"},
				FontColor:    "#333333",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_HEADLINE_L},
			},
			SubTitle: &commontypes.Text{
				DisplayValue: &commontypes.Text_Html{Html: "Pay a joining fee of just ₹2,000 & earn exclusive rewards."},
				FontColor:    "#646464",
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
			},
			BackgroundColour: &widget.BackgroundColour{
				Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#FFFFFF"},
			},
			Promotion: &ffPb.PromotionInfo{
				Properties: &ffPb.LayoutProperties{
					Paddings: &ffPb.PaddingProperty{},
					Margins: &ffPb.MarginProperty{
						Top: 16,
					},
					Size: &ffPb.Size{
						Width:  -2,
						Height: -2,
					},
				},
				DrawableProperties: &ffPb.DrawableProperties{
					CornerProperty: &ffPb.CornerProperty{
						TopStartCornerRadius: 20,
						TopEndCornerRadius:   20,
						BottomStartCorner:    20,
						BottomEndCorner:      20,
					},
					BorderProperty: &ffPb.BorderProperty{},
					Shadow: &widget.Shadow{
						Colour: &widget.BackgroundColour{},
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{BlockColour: "#EFF2F6"},
					},
				},
				PromotionWidgets: []*ffPb.PromotionWidget{
					{
						Widget: &ffPb.PromotionWidget_PromotionVisualElement{
							PromotionVisualElement: &ffPb.WrappedVisualElement{
								VisualElement: &commontypes.VisualElement{
									Asset: &commontypes.VisualElement_Image_{
										Image: &commontypes.VisualElement_Image{
											Source: &commontypes.VisualElement_Image_Url{
												Url: "https://epifi-icons.pointz.in/credit_card_images/cc_intro_amplifi_v2_Welocme_offer_Bottomsheet.png",
											},
											ImageType: commontypes.ImageType_PNG,
											Properties: &commontypes.VisualElementProperties{
												Width:  356,
												Height: 376,
											},
										},
									},
								},
								Properties: &ffPb.LayoutProperties{
									Paddings: &ffPb.PaddingProperty{},
									Margins: &ffPb.MarginProperty{
										Top: 32,
									},
									Size: &ffPb.Size{
										Width:  -2,
										Height: -2,
									},
								},
							},
						},
					},
				},
			},
		})
}

func GetJoiningFeeVoucherInfo() *deeplink.InfoItemWithCtaV2 {
	return &deeplink.InfoItemWithCtaV2{
		Info: &deeplink.InfoItemV2{
			Icon: "https://epifi-icons.pointz.in/credit_card_images/merchant_all_cards_intro_4x.png",
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Get welcome gift cards\n <a href=>worth over ₹5,000!</a>"},
			},
		},
		Cta: &deeplink.Cta{
			Text:     "View details",
			Deeplink: GetWelcomeVouchersDeeplink(),
		},
	}
}

func GetAcceleratedRewardsInfo() *deeplink.InfoItemWithCtaV2 {
	return &deeplink.InfoItemWithCtaV2{
		Info: &deeplink.InfoItemV2{
			Icon: "https://epifi-icons.pointz.in/credit_card_images/accelarated_reward_intro_4x.png",
		},
		Cta: &deeplink.Cta{
			Text: "See how much you can earn",
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_INTRO_REWARD_ESTIMATION_SCREEN,
				ScreenOptions: &deeplink.Deeplink_CreditCardIntroRewardEstimationScreenOptions{
					CreditCardIntroRewardEstimationScreenOptions: &deeplink.CreditCardIntroRewardEstimationScreenOptions{
						ScreenTitle: "Get 5% valueback annually",
						Description: "Valueback is the rupee value of the benefits you will receive. You can get 5% or more of your annual spends using this card.",
						Subtitle:    "See how much you can earn ",
						Slider: &deeplink.CreditCardIntroRewardEstimationScreenOptions_Slider{
							CurrentlySelectedValue: 35000,
							SliderLowerLimit:       15000,
							SliderUpperLimit:       60000,
							SkipValue:              5000,
						},
					},
				},
			},
		},
	}
}

func GetFiBrandCollectionInfo() *deeplink.InfoItemWithCtaV2 {

	return &deeplink.InfoItemWithCtaV2{
		Info: &deeplink.InfoItemV2{
			Icon: "https://epifi-icons.pointz.in/credit_card_images/merchant_collection_intro_4x.png",
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Welcome gift cards worth over ₹5,000!"},
			},
		},
		Cta: GetFiBrandsScreenCta(),
	}
}

func boolToCapString(b bool) string {
	if b {
		return "TRUE"
	}
	return "FALSE"
}

func getCreditCardOffersDeeplinkV3(ctx context.Context, customerName *commontypes.Name, cardProgram *types.CardProgram, creditLimit *moneyPb.Money, conf *dynconf.Config, showHorizontalLayout bool, ccReqHeader *types.CreditCardRequestHeader) *deeplink.Deeplink {
	if showHorizontalLayout && !conf.DisableHorizontalLayoutByQuestEngine(ctx) {
		// TODO(team) : This is a hacky solve for sending card program specific attributes in card request header,
		// because of request header being picked in choice framework(frontend) from whatever is being sent from here
		// for any card program
		switch {
		case conf.EnableMassUnsecuredOnboardingV2(ctx) && ccReqHeader.GetAttributes() != nil && firefly.IsCreditCardProgramMassUnsecured(cardProgram):
			logger.Info(ctx, "enabled magnifi onboarding v2")
			ccReqHeader.GetAttributes()[enableMassUnsecuredOnboardingV2] = trueString
		case conf.EnableMassUnsecuredOnboardingV2(ctx) && firefly.IsCreditCardProgramMassUnsecured(cardProgram):
			logger.Info(ctx, "enabled magnifi onboarding v2")
			ccReqHeader.Attributes = map[string]string{
				enableMassUnsecuredOnboardingV2: trueString,
			}
		case ccReqHeader.GetAttributes() != nil && firefly.IsCreditCardProgramUnsecured(cardProgram):
			ccReqHeader.GetAttributes()[enableUnsecuredOnboardingV2] = boolToCapString(conf.EnableUnsecuredOnboardingV2(ctx))
			ccReqHeader.GetAttributes()[enableUnsecuredConsentFlowOnboarding] = boolToCapString(conf.EnableUnsecuredConsentFlowOnboarding(ctx))
		case firefly.IsCreditCardProgramUnsecured(cardProgram):
			ccReqHeader.Attributes = map[string]string{
				enableUnsecuredOnboardingV2:          boolToCapString(conf.EnableUnsecuredOnboardingV2(ctx)),
				enableUnsecuredConsentFlowOnboarding: boolToCapString(conf.EnableUnsecuredConsentFlowOnboarding(ctx)),
			}
		}

		return deeplinkv3.GetDeeplinkV3WithoutError(deeplink.Screen_CC_INTRO_SCREEN,
			&ffScreenTypes.CcIntroScreenOptions{
				// TODO(SAURABH): Add relevant use case of screen identifier currently sending o by default no use
				ScreenIdentifier:        0,
				CardProgramType:         cardProgramTypeToEnumMap[cardProgram.GetCardProgramType()],
				CardProgram:             cardProgram,
				CreditCardRequestHeader: ccReqHeader,
			})
	}
	ctaNameToCtaMap := map[string]*deeplink.InfoItemWithCta{
		"WelcomeVoucherCta": {
			Info: &deeplink.InfoItem{
				Icon:  conf.CreditCardOffersScreenOptionsV2().WelcomeVoucherCta().IconLink(ctx),
				Title: conf.CreditCardOffersScreenOptionsV2().WelcomeVoucherCta().Title(ctx),
			},
			Cta: &deeplink.Cta{
				Text:     conf.CreditCardOffersScreenOptionsV2().WelcomeVoucherCta().CtaText(ctx),
				Deeplink: GetWelcomeVouchersDeeplinkV3(ctx, conf),
			},
		},
		"RewardEstimationCta": {
			Info: &deeplink.InfoItem{
				Icon:  conf.CreditCardOffersScreenOptionsV2().RewardEstimationCta().IconLink(ctx),
				Title: conf.CreditCardOffersScreenOptionsV2().RewardEstimationCta().Title(ctx),
			},
			Cta: &deeplink.Cta{
				Text: conf.CreditCardOffersScreenOptionsV2().RewardEstimationCta().CtaText(ctx),
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_INTRO_REWARD_ESTIMATION_SCREEN,
					ScreenOptions: &deeplink.Deeplink_CreditCardIntroRewardEstimationScreenOptions{
						CreditCardIntroRewardEstimationScreenOptions: &deeplink.CreditCardIntroRewardEstimationScreenOptions{
							ScreenTitle: "Get 5% valueback annually",
							Description: "Valueback is the rupee value of the benefits you will receive. You can get 5% or more of your annual spends using this card.",
							Subtitle:    "See how much you can earn ",
							Slider: &deeplink.CreditCardIntroRewardEstimationScreenOptions_Slider{
								CurrentlySelectedValue: 35000,
								SliderLowerLimit:       15000,
								SliderUpperLimit:       60000,
								SkipValue:              5000,
							},
						},
					},
				},
			},
		},
		"TopBrandsCta": {
			Info: &deeplink.InfoItem{
				Icon:  conf.CreditCardOffersScreenOptionsV2().TopBrandsCta().IconLink(ctx),
				Title: conf.CreditCardOffersScreenOptionsV2().TopBrandsCta().Title(ctx),
			},
			Cta: &deeplink.Cta{
				Text: conf.CreditCardOffersScreenOptionsV2().TopBrandsCta().CtaText(ctx),
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CREDIT_CARD_INTRO_SCREEN_BRANDS,
					ScreenOptions: &deeplink.Deeplink_CreditCardIntroScreenBrandScreenOptions{
						CreditCardIntroScreenBrandScreenOptions: &deeplink.CreditCardIntroScreenBrandScreenOptions{
							RewardDescriptions: &deeplink.InfoBlock{
								Title: "Rewards that evolve to match your spends",
								InfoItems: []*deeplink.InfoItem{
									{
										Icon:  "https://epifi-icons.pointz.in/credit_card_images/1x_image.png",
										Title: "On all spends, including fuel",
										Desc:  "Get 1 Fi-Coin for every ₹5 spent",
									},
									{
										Icon:  "https://epifi-icons.pointz.in/credit_card_images/2x_image.png",
										Title: "On brands listed in the Fi Collection",
										Desc:  "Get 2 Fi-Coins for every ₹5 spent",
									},
									{
										Icon:  "https://epifi-icons.pointz.in/credit_card_images/5x_reward_img.png",
										Title: "On your top 3 brands from the Fi Collection",
										Desc:  "Get 5 Fi-Coins for every ₹5 spent",
									},
								},
							},
							TopBrandsImage: "https://epifi-icons.pointz.in/credit_card_images/top_brands_2.png",
							AllBrandList: &deeplink.InfoBlock{
								Title:     "The Fi Collection",
								Desc:      "An ever-expanding list of India’s top brands",
								InfoItems: getAllBrands(),
							},
							BrandsLottie: getTopBrandLottieElement(ctx),
						},
					},
				},
			},
		},
	}

	limitAmountString := conf.CreditCardOffersScreenOptionsV2().CreditLimitAmountString(ctx)
	acceleratedRewardsTitle := conf.CreditCardOffersScreenOptionsV2().AcceleratedRewardsInfo().Title(ctx)
	rewardsWorthInfoTitle := conf.CreditCardOffersScreenOptionsV2().RewardsWorthInfo().Title(ctx)
	isAmountGreaterThanThreshold, err := money.CompareV2(creditLimit, thresholdForLimit)
	if err == nil && isAmountGreaterThanThreshold >= 0 && creditLimit != nil {
		limitAmountString = fmt.Sprintf("Credit limit <br><b>%s</b>",
			money.ToDisplayStringInIndianFormat(creditLimit, 0, true))
	}

	// this check is needed to ensure below block strings send with \n tag instead of <br> for ios platform as
	// client in ios doesn't support <br> tag, currently app over android version 2000 and ios version 0 supported by this check
	if app.IsFeatureEnabledFromCtx(ctx, conf.EnableCcOffersScreenOlderStringAlignment()) {
		limitAmountString = removeBrTagFromString(limitAmountString)
		acceleratedRewardsTitle = removeBrTagFromString(acceleratedRewardsTitle)
		rewardsWorthInfoTitle = removeBrTagFromString(rewardsWorthInfoTitle)
	}

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CARD_OFFERS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_GetOffersScreenOptions{GetOffersScreenOptions: &deeplink.GetOffersScreenOptions{
			// Deprecated
			InfoBlock: cardDetails,
			// Deprecated
			KnowMore: &deeplink.Cta{
				Text: "Know More",
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_CARD_KNOW_MORE_SCREEN,
				},
			},
			// Deprecated
			PopUpTextBlock: &deeplink.InfoItemWithCta{
				Info: &deeplink.InfoItem{
					Title: "Does this card come at no cost?",
					Desc:  fmt.Sprintf("You will be charged a joining fee of ₹2000 for the %s credit card, which will be added to your first bill.\nAn annual fee of ₹2000 for your credit card will also be charged, but if you spend approximately ₹2.5L in an year, it will get waived off.", firefly.CreditCardType),
				},
				Cta: &deeplink.Cta{Text: "Okay, got it"},
			},
			// Deprecated
			FeesInfo: []*deeplink.GetOffersScreenOptions_InfoItemWithBadgeInfoAndCta{
				{
					Info: &deeplink.InfoItemWithCta{
						Info: &deeplink.InfoItem{
							Title: "Joining fee: ₹2,000",
							Desc:  "Get <a href=>welcome vouchers worth ₹5,000</a> 🎁",
						},
						Cta: &deeplink.Cta{
							Deeplink: GetWelcomeVouchersDeeplink(),
						},
					},
					BadgeText: &commontypes.Text{
						BgColor:      "#383838",
						FontColor:    "#A4A4A4",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "BILLED ON SIGN UP"},
					},
				},
				{
					Info: &deeplink.InfoItemWithCta{
						Info: &deeplink.InfoItem{
							Title: "Renewal fee: ₹2,000",
							Desc:  "Waived off if spends are more than ₹2.5 lakh in the year.",
						},
						Cta: &deeplink.Cta{
							Text:    "View all fees & charges",
							Weblink: "https://fi.money/credit-card/key-fact-statement",
						},
					},
					BadgeText: &commontypes.Text{
						BgColor:      "#383838",
						FontColor:    "#A4A4A4",
						DisplayValue: &commontypes.Text_PlainString{PlainString: "BILLED AFTER 1 YEAR"},
					},
				},
			},
			// Deprecated
			WelcomeVouchers: GetWelcomeVouchersInfoItemWithCTAV3(ctx, conf, ctaNameToCtaMap),
			// Deprecated
			ValuebackCheatsheet: GetValueBackCheatSheetInfoItemWithCTAV3(ctx, conf, ctaNameToCtaMap),
			// Deprecated
			Brands:         GetTopBrandsInfoItemWithCTAV3(ctx, conf, ctaNameToCtaMap),
			CardImageUrl:   conf.CreditCardOffersScreenOptionsV2().CardImageUrl(ctx),
			TncString:      conf.CreditCardOffersScreenOptionsV2().TermsAndConditions(ctx),
			PartnershipUrl: conf.CreditCardOffersScreenOptionsV2().PartnershipUrl(ctx),
			GetCreditCard: &deeplink.Cta{
				Text: conf.CreditCardOffersScreenOptionsV2().GetCreditCardCtaText(ctx),
			},
			Title:             conf.CreditCardOffersScreenOptionsV2().Title(ctx),
			HeaderDescription: conf.CreditCardOffersScreenOptionsV2().HeaderDescription(ctx),
			CustomerName:      customerName,
			CreditLimitStr:    limitAmountString,
			StaticImages: []*deeplink.InfoItem{
				{
					Icon:  conf.CreditCardOffersScreenOptionsV2().StaticImages().IconLink(ctx),
					Title: conf.CreditCardOffersScreenOptionsV2().StaticImages().Title(ctx),
				},
				{
					Icon:  conf.CreditCardOffersScreenOptionsV2().StaticImages().IconLink1(ctx),
					Title: conf.CreditCardOffersScreenOptionsV2().StaticImages().Title1(ctx),
				},
				{
					Icon:  conf.CreditCardOffersScreenOptionsV2().StaticImages().IconLink2(ctx),
					Title: conf.CreditCardOffersScreenOptionsV2().StaticImages().Title2(ctx),
				},
			},
			FeesInfoHeader: conf.CreditCardOffersScreenOptionsV2().FeesInfoHeader(ctx),
			RewardsWorth: &deeplink.InfoItemWithCtaV2{
				Info: &deeplink.InfoItemV2{
					Icon: "",
					Title: &commontypes.Text{
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_1},
						DisplayValue: &commontypes.Text_PlainString{PlainString: rewardsWorthInfoTitle},
						FontColor:    "#ffffff",
					},
					Desc: &commontypes.Text{
						DisplayValue: &commontypes.Text_PlainString{PlainString: conf.CreditCardOffersScreenOptionsV2().RewardsWorthInfo().Desc(ctx)},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
					},
				},
				Cta: &deeplink.Cta{
					Text: conf.CreditCardOffersScreenOptionsV2().RewardsWorthCtaInfo().CtaText(ctx),
					Deeplink: &deeplink.Deeplink{
						Screen: deeplink.Screen_CREDIT_CARD_INTRO_REWARD_ESTIMATION_SCREEN,
						ScreenOptions: &deeplink.Deeplink_CreditCardIntroRewardEstimationScreenOptions{
							CreditCardIntroRewardEstimationScreenOptions: &deeplink.CreditCardIntroRewardEstimationScreenOptions{
								ScreenTitle: conf.CreditCardOffersScreenOptionsV2().RewardsWorthCtaInfo().DpLinkScreenTitle(ctx),
								Description: conf.CreditCardOffersScreenOptionsV2().RewardsWorthCtaInfo().DpLinkDescription(ctx),
								Subtitle:    conf.CreditCardOffersScreenOptionsV2().RewardsWorthCtaInfo().DpLinkSubtitle(ctx),
								Slider: &deeplink.CreditCardIntroRewardEstimationScreenOptions_Slider{
									CurrentlySelectedValue: 35000,
									SliderLowerLimit:       15000,
									SliderUpperLimit:       60000,
									SkipValue:              5000,
								},
							},
						},
					},
				},
			},
			BroadVisualElement: &commontypes.VisualElement{
				Asset: &commontypes.VisualElement_Image_{
					Image: &commontypes.VisualElement_Image{
						Source: &commontypes.VisualElement_Image_Url{
							Url: getBroadVisualElementUrl(ctx, conf),
						},
						Properties: &commontypes.VisualElementProperties{
							Width:  430,
							Height: 160,
						},
						ImageType: commontypes.ImageType_PNG,
					},
				},
			},
			FeeInfoV2: []*deeplink.InfoItemWithCtaV2WithInfoItemV2AndBadge{
				{
					Info: &deeplink.InfoItemV2{
						Icon: "",
						Title: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{PlainString: conf.CreditCardOffersScreenOptionsV2().FeesInfoV2().Title(ctx)},
						},
						Desc: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{PlainString: conf.CreditCardOffersScreenOptionsV2().FeesInfoV2().Desc(ctx)},
						},
						SubTitle: &commontypes.Text{
							DisplayValue: &commontypes.Text_PlainString{PlainString: conf.CreditCardOffersScreenOptionsV2().FeesInfoV2().SubTitle(ctx)},
						},
					},
					InfoWithCta: GetJoiningFeeVoucherInfoV3(ctx, conf),
				},
			},
			AllFeeCta: &deeplink.Cta{
				Text:    conf.CreditCardOffersScreenOptionsV2().AllFeesCta().CtaText(ctx),
				Weblink: conf.CreditCardOffersScreenOptionsV2().AllFeesCta().CtaWebLink(ctx),
			},

			AcceleratedRewardInfo: &deeplink.GetOffersScreenOptions_AcceleratedRewardInfo{
				AcceleratedRewardTitle: &commontypes.Text{
					DisplayValue: &commontypes.Text_PlainString{PlainString: acceleratedRewardsTitle},
				},

				AcceleratedRewardV2: GetAcceleratedRewardsInfoV3(ctx, conf),
			},
			FiCollectionCard: GetFiBrandCollectionInfoV3(ctx, conf),
		}},
	}
}

// GetOnboardingNextActionForUser retrieves the next onboarding action for a user based on various parameters.
func GetOnboardingNextActionForUser(ctx context.Context, customerName *commontypes.Name, cardProgram *types.CardProgram, creditLimit *moneyPb.Money, conf *dynconf.Config, showHorizontalLayout bool, ccReqHeader *types.CreditCardRequestHeader) (*deeplink.Deeplink, error) {
	var (
		nextActionDeeplink *deeplink.Deeplink
		err                error
	)

	// check if the feature for credit card onboarding is disabled on the platform.
	if !pkggenconf.IsFeatureEnabledOnPlatform(ctx, conf.Flags().MinVersionForCCOnboarding()) {
		// If the feature is disabled, retrieve the next action for updating the app.
		nextActionDeeplink, err = getAppUpdateNextAction(ctx, cardProgram, conf)
	} else {
		// Get the current platform and version from the application context.
		platform, appVersion := epificontext.AppPlatformAndVersion(ctx)
		logger.Info(ctx, "app version is sufficient for user to onboard if not yet onboarded already",
			zap.Int(logger.APP_VERSION_CODE, appVersion), zap.String(logger.APP_PLATFORM, platform.String()))
	}

	// return an error if there was an issue retrieving the next action.
	if err != nil {
		return nil, err
	}

	// if there is a next action for updating the app, return it.
	if nextActionDeeplink != nil {
		return nextActionDeeplink, nil
	}

	// if there is no update action, return the deep link for credit card offers.
	return getCreditCardOffersDeeplinkV3(ctx, customerName, cardProgram, creditLimit, conf, showHorizontalLayout, ccReqHeader), nil
}

// getAppUpdateNextAction retrieves the next action for updating the app based on the given card program and configuration.
func getAppUpdateNextAction(ctx context.Context, cardProgram *types.CardProgram, _ *dynconf.Config) (*deeplink.Deeplink, error) {
	// Check if the card program is unsecured and originates from a FI-BRE-approved source.
	if !(cardProgram.GetCardProgramType() == types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED && cardProgram.GetCardProgramSource() == types.CardProgramSource_CARD_PROGRAM_SOURCE_FI_BRE_APPROVED) {
		// If not, return nil as there is no update action needed.
		return nil, nil
	}

	// Get the current platform and version from the application context.
	platform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	logger.Info(ctx, "app version is not sufficient for consent flow, enforcing user to update the app as user not onboarded yet",
		zap.Int(logger.APP_VERSION_CODE, appVersion), zap.String(logger.APP_PLATFORM, platform.String()))

	return getAppUpdateDeeplink(platform), nil
}

func GetWelcomeVouchersDeeplinkV3(ctx context.Context, conf *dynconf.Config) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_CREDIT_CARD_WELCOME_VOUCHERS_SCREEN,
		ScreenOptions: &deeplink.Deeplink_CreditCardWelcomeVouchersScreenOptions{
			CreditCardWelcomeVouchersScreenOptions: &deeplink.CreditCardWelcomeVouchersScreenOptions{
				VoucherInfo: &deeplink.InfoBlock{
					Title: conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaTitle(ctx),
					InfoItems: []*deeplink.InfoItem{
						{
							Icon:  conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().IconLink(ctx),
							Title: conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().Title(ctx),
						},
						{
							Icon:  conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().IconLink1(ctx),
							Title: conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().Title1(ctx),
						},
						{
							Icon:  conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().IconLink2(ctx),
							Title: conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().Title2(ctx),
						},
						{
							Icon:  conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().IconLink3(ctx),
							Title: conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().Title3(ctx),
						},
						{
							Icon:  conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().IconLink4(ctx),
							Title: conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().Title4(ctx),
						},
						{
							Icon:  conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().IconLink5(ctx),
							Title: conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().Title5(ctx),
						},
						{
							Icon:  conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().IconLink6(ctx),
							Title: conf.CreditCardOffersScreenOptionsV2().FeesInfo().CtaInfoItem().Title6(ctx),
						},
					},
				},
				BottomInfoItem: &deeplink.InfoItem{
					Icon:  conf.CreditCardOffersScreenOptionsV2().FeesInfo().BottomInfoItem().IconLink(ctx),
					Title: conf.CreditCardOffersScreenOptionsV2().FeesInfo().BottomInfoItem().Title(ctx),
				},
			},
		},
	}
}

func GetJoiningFeeVoucherInfoV3(ctx context.Context, conf *dynconf.Config) *deeplink.InfoItemWithCtaV2 {
	return &deeplink.InfoItemWithCtaV2{
		Info: &deeplink.InfoItemV2{
			Icon: conf.CreditCardOffersScreenOptionsV2().JoiningFeeVoucherInfo().IconLink(ctx),
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: conf.CreditCardOffersScreenOptionsV2().JoiningFeeVoucherInfo().Title(ctx)},
			},
		},
		Cta: &deeplink.Cta{
			Text:     conf.CreditCardOffersScreenOptionsV2().JoiningFeeVoucherInfo().CtaText(ctx),
			Deeplink: GetWelcomeVouchersDeeplinkV3(ctx, conf),
		},
	}
}

func GetAcceleratedRewardsInfoV3(ctx context.Context, conf *dynconf.Config) *deeplink.InfoItemWithCtaV2 {
	return &deeplink.InfoItemWithCtaV2{
		Info: &deeplink.InfoItemV2{
			Icon: conf.CreditCardOffersScreenOptionsV2().AcceleratedRewardsInfo().InfoIconLink(ctx),
		},
		Cta: &deeplink.Cta{
			Text: conf.CreditCardOffersScreenOptionsV2().AcceleratedRewardsInfo().CtaText(ctx),
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_CREDIT_CARD_INTRO_SCREEN_BRANDS,
				ScreenOptions: &deeplink.Deeplink_CreditCardIntroScreenBrandScreenOptions{
					CreditCardIntroScreenBrandScreenOptions: &deeplink.CreditCardIntroScreenBrandScreenOptions{
						RewardDescriptions: &deeplink.InfoBlock{
							Title: "Rewards that evolve to match your spends",
							InfoItems: []*deeplink.InfoItem{
								{
									Icon:  "https://epifi-icons.pointz.in/credit_card_images/1x_image.png",
									Title: "On all spends, including fuel",
									Desc:  "20 Fi-Coins for every ₹100 spent",
								},
								{
									Icon:  "https://epifi-icons.pointz.in/credit_card_images/2x_image.png",
									Title: "On all brands listed in the Fi Collection",
									Desc:  "40 Fi-Coins for every ₹100 spent",
								},
								{
									Icon:  "https://epifi-icons.pointz.in/credit_card_images/5x_reward_img.png",
									Title: "On the 3 brands you spend the most on each month from the Fi Collection",
									Desc:  "100 Fi-Coins for every ₹100 spent",
								},
							},
						},
						TopBrandsImage: "https://epifi-icons.pointz.in/credit_card_images/lotte_group_4x.png",
						AllBrandList: &deeplink.InfoBlock{
							Title:     "The Fi Collection",
							Desc:      "An ever-expanding list of India’s top brands",
							InfoItems: getAllBrands(),
						},
						BrandsLottie: getTopBrandLottieElement(ctx),
					},
				},
			},
		},
	}
}

func GetWelcomeVouchersInfoItemWithCTAV3(ctx context.Context, conf *dynconf.Config, ctaNameToCtaMap map[string]*deeplink.InfoItemWithCta) *deeplink.InfoItemWithCta {
	if dpLink, ok := ctaNameToCtaMap[conf.CreditCardOffersScreenOptionsV2().Cta1Name(ctx)]; ok {
		if dpLink.Cta.Text == "" {
			dpLink.Cta.Deeplink = nil
		}
		return dpLink
	}
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:  conf.CreditCardOffersScreenOptionsV2().WelcomeVoucherCta().IconLink(ctx),
			Title: conf.CreditCardOffersScreenOptionsV2().WelcomeVoucherCta().Title(ctx),
		},
	}
}

func GetValueBackCheatSheetInfoItemWithCTAV3(ctx context.Context, conf *dynconf.Config, ctaNameToCtaMap map[string]*deeplink.InfoItemWithCta) *deeplink.InfoItemWithCta {
	if dpLink, ok := ctaNameToCtaMap[conf.CreditCardOffersScreenOptionsV2().Cta2Name(ctx)]; ok {
		if dpLink.Cta.Text == "" {
			dpLink.Cta.Deeplink = nil
		}
		return dpLink
	}
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:  conf.CreditCardOffersScreenOptionsV2().RewardEstimationCta().IconLink(ctx),
			Title: conf.CreditCardOffersScreenOptionsV2().RewardEstimationCta().Title(ctx),
		},
	}
}

func GetTopBrandsInfoItemWithCTAV3(ctx context.Context, conf *dynconf.Config, ctaNameToCtaMap map[string]*deeplink.InfoItemWithCta) *deeplink.InfoItemWithCta {
	if dpLink, ok := ctaNameToCtaMap[conf.CreditCardOffersScreenOptionsV2().Cta3Name(ctx)]; ok {
		if dpLink.Cta.Text == "" {
			dpLink.Cta.Deeplink = nil
		}
		return dpLink
	}
	return &deeplink.InfoItemWithCta{
		Info: &deeplink.InfoItem{
			Icon:  conf.CreditCardOffersScreenOptionsV2().TopBrandsCta().IconLink(ctx),
			Title: conf.CreditCardOffersScreenOptionsV2().TopBrandsCta().Title(ctx),
		},
	}
}

func GetFiBrandCollectionInfoV3(ctx context.Context, conf *dynconf.Config) *deeplink.InfoItemWithCtaV2 {

	return &deeplink.InfoItemWithCtaV2{
		Info: &deeplink.InfoItemV2{
			Icon: "https://epifi-icons.pointz.in/credit_card_images/new_welcome_five_4x.png",
			Title: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Welcome gift cards worth over ₹5,000!"},
			},
		},
		Cta: GetWelcomeVoucherV3(ctx, conf),
	}
}

func GetWelcomeVoucherV3(ctx context.Context, conf *dynconf.Config) *deeplink.Cta {
	return &deeplink.Cta{
		Text:     "View details",
		Deeplink: GetWelcomeVouchersDeeplinkV3(ctx, conf),
	}
}

func getTopBrandLottieElement(ctx context.Context) *commontypes.VisualElement {
	return &commontypes.VisualElement{
		Asset: &commontypes.VisualElement_Lottie_{
			Lottie: &commontypes.VisualElement_Lottie{
				Source: &commontypes.VisualElement_Lottie_Url{
					Url: "https://epifi-icons.pointz.in/credit_card_images/rewards_lotte_Acc_rewards.json",
				},
				Properties: getBrandsLottieProperties(ctx),
			},
		},
	}
}

func getBrandsLottieProperties(ctx context.Context) *commontypes.VisualElementProperties {
	platform, _ := epificontext.AppPlatformAndVersion(ctx)
	brandsLottieProperties := &commontypes.VisualElementProperties{}
	if platform == commontypes.Platform_IOS {
		brandsLottieProperties = &commontypes.VisualElementProperties{
			Width:  290,
			Height: 175,
		}
	}

	return brandsLottieProperties
}

func removeBrTagFromString(amountString string) string {
	amountString = strings.ReplaceAll(amountString, "<br>", "\n")
	return amountString
}

func getBroadVisualElementUrl(ctx context.Context, conf *dynconf.Config) string {
	var (
		url string
	)
	platform, _ := epificontext.AppPlatformAndVersion(ctx)
	if platform == commontypes.Platform_IOS {
		url = conf.CreditCardOffersScreenOptionsV2().BroadVisualElementImgUrlIOS(ctx)
	} else {
		url = conf.CreditCardOffersScreenOptionsV2().BroadVisualElementImgUrl(ctx)
	}
	return url
}
