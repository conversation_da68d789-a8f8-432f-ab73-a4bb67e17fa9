package rewards

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	ffBeAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	fireflyV2Pb "github.com/epifi/gamma/api/firefly/v2"
	"github.com/epifi/gamma/api/frontend/deeplink"
	tieringPb "github.com/epifi/gamma/api/tiering"
	types "github.com/epifi/gamma/api/typesv2"
	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
)

func IsCCUserAndCardType(ctx context.Context, actorId string, fireflyAccountingClient ffBeAccountsPb.AccountingClient, fireflyClient2 fireflyV2Pb.FireflyV2Client) (bool, types.CardProgramType) {
	var cardProgramType types.CardProgramType
	isCCUser := false

	// 1. Try credit card from accounting
	accRes, rpcErr := fireflyAccountingClient.GetAccounts(ctx, &ffBeAccountsPb.GetAccountsRequest{
		GetBy: &ffBeAccountsPb.GetAccountsRequest_ActorId{ActorId: actorId},
	})
	if err := epifigrpc.RPCError(accRes, rpcErr); err != nil {
		logger.Error(ctx, "error getting accounts", zap.Error(err))
	} else if len(accRes.GetAccounts()) > 0 {
		cardProgramType = accRes.GetAccounts()[0].GetCardProgram().GetCardProgramType()
		isCCUser = true
	}

	// 2. If not found, try credit card from fireflyV2
	if !isCCUser {
		creditCardDetail, err := fireflyClient2.GetCreditCards(ctx, &fireflyV2Pb.GetCreditCardsRequest{
			Identifier: &fireflyV2Pb.GetCreditCardsRequest_ActorId{ActorId: actorId},
		})
		if te := epifigrpc.RPCError(creditCardDetail, err); te != nil {
			logger.Error(ctx, "error while getting credit cards", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(te))
		} else if len(creditCardDetail.GetCreditCards()) > 0 {
			cardProgramType = creditCardDetail.GetCreditCards()[0].GetCardProgram().GetCardProgramType()
			isCCUser = true
		}
	}

	return isCCUser, cardProgramType
}

// GetFiCoinsFiPointsKnowMoreButtonDeeplink selects the image URL for the deeplink which will be triggered when user clicks on Fi Coins - Fi Points Conversion banners or widgets .
// return the bottom sheet deeplink if image url found else return error view deeplink
// Priority Order for getting deeplink image : First we are checking for Credit Card then for Tier.
func GetFiCoinsFiPointsKnowMoreButtonDeeplink(ctx context.Context, actorId string, fireflyAccountingClient ffBeAccountsPb.AccountingClient, fireflyClient2 fireflyV2Pb.FireflyV2Client, tieringClient tieringPb.TieringClient, forPreMigration bool) *deeplink.Deeplink {
	imageUrlForDeeplink, err := GetImageUrlForFiCoinsFiPointsKnowMoreButtonDeeplink(ctx, actorId, fireflyAccountingClient, fireflyClient2, tieringClient, forPreMigration)

	// 3. Deeplink to redirect when User Clicks on fiPointsPreConversionPromoWidget
	var fiCoinsFiPointsDeeplink *deeplink.Deeplink
	if err != nil {
		fiCoinsFiPointsDeeplink = &deeplink.Deeplink{
			Screen: deeplink.Screen_DETAILED_ERROR_VIEW_SCREEN,
			ScreenOptions: &deeplink.Deeplink_DetailedErrorViewScreenOptions{
				DetailedErrorViewScreenOptions: &deeplink.DetailedErrorViewScreenOptions{
					TitleText:    commontypes.GetTextFromHtmlStringFontColourFontStyle("Something went wrong", "#313234", commontypes.FontStyle_HEADLINE_L),
					SubtitleText: commontypes.GetTextFromHtmlStringFontColourFontStyle("Please try again later", "#929599", commontypes.FontStyle_BODY_S),
					Image: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/profileUpdate/errorDino").WithProperties(&commontypes.VisualElementProperties{
						Height: 240,
						Width:  240,
					}).WithImageType(commontypes.ImageType_PNG),
					Ctas: []*deeplink.Cta{
						{
							Type:         deeplink.Cta_DONE,
							DisplayTheme: deeplink.Cta_PRIMARY,
							Text:         "OK, got it",
						},
					},
				},
			},
		}
	} else {
		fiCoinsFiPointsDeeplink = deeplinkV3.GetDeeplinkV3WithoutError(deeplink.Screen_SDUI_BOTTOM_SHEET, &pkgScreenOptionsPb.SduiBottomSheetOptions{
			IsFullScreen: true,
			Section: &sections.Section{
				Content: &sections.Section_VerticalListSection{
					VerticalListSection: &sections.VerticalListSection{
						IsScrollable: true,
						Components: []*components.Component{
							{
								Content: ui.GetAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(imageUrlForDeeplink, 0, 0)),
								VisibleBehavior: &behaviors.LifecycleBehavior{
									Behavior: &behaviors.LifecycleBehavior_ViewedBehavior{},
									AnalyticsEvent: &analytics.AnalyticsEvent{
										EventName: "ViewedFiCoinsToFiPointsPromoWidget",
									},
								},
							},
						},
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: properties.GetContainerProperty().
										WithBlockBgColor(colors.ColorSnow).
										WithAllCornerRadii(24, 24, 0, 0).
										WithPadding(16, 0, 16, 28),
								},
							},
						},
						VerticalArrangement: sections.VerticalListSection_VERTICAL_ARRANGEMENT_BOTTOM,
						HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
					},
				},
			},
		})
	}
	return fiCoinsFiPointsDeeplink
}

// GetImageUrlForFiCoinsFiPointsKnowMoreButtonDeeplink selects the image URL for the deeplink which will be triggered when user clicks on Fi Coins - Fi Points Conversion banners or widgets .
// Priority Order : First we are checking for Credit Card then for Tier.
func GetImageUrlForFiCoinsFiPointsKnowMoreButtonDeeplink(ctx context.Context, actorId string, fireflyAccountingClient ffBeAccountsPb.AccountingClient, fireflyClient2 fireflyV2Pb.FireflyV2Client, tieringClient tieringPb.TieringClient, forPreMigration bool) (string, error) {
	isCCUser, cardProgramType := IsCCUserAndCardType(ctx, actorId, fireflyAccountingClient, fireflyClient2)

	// 1. If CCUser found, use the card program type
	// Note: skipping to treat as CC user if card program type is CARD_PROGRAM_TYPE_UNSPECIFIED
	if isCCUser {
		switch cardProgramType {
		case types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:
			if forPreMigration {
				return "https://epifi-icons.pointz.in/rewards/fc-fp-webpage-amplifi-v3.png", nil
			} else {
				return "https://epifi-icons.pointz.in/rewards/fc-fp-webpage-post-migration-amplifi-v3.png", nil
			}
		case types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
			if forPreMigration {
				return "https://epifi-icons.pointz.in/rewards/fc-fp-webpage-simplifi-v4.png", nil
			} else {
				return "https://epifi-icons.pointz.in/rewards/fc-fp-webpage-post-migration-simplifi-v3.png", nil
			}
		case types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
			if forPreMigration {
				return "https://epifi-icons.pointz.in/rewards/fc-fp-webpage-magnifi-v4.png", nil
			} else {
				return "https://epifi-icons.pointz.in/rewards/fc-fp-webpage-post-migration-magnifi-v4.png", nil
			}
		}
	}

	// 2. Try tier
	tierResp, getTierErr := tieringClient.GetCurrentTierForActor(ctx, &tieringPb.GetCurrentTierForActorRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(tierResp, getTierErr); rpcErr != nil {
		logger.Error(ctx, "error fetching current tier for actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return "", fmt.Errorf("unable to determine image url for deeplink as user does not belong to any tier")
	}
	if tierResp.GetTier().IsBaseTier() {
		if forPreMigration {
			return "https://epifi-icons.pointz.in/rewards/fc-fp-webpage-regular-or-standard-tier-v3.png", nil
		} else {
			return "https://epifi-icons.pointz.in/rewards/fc-fp-webpage-post-migration-regular-or-standard-tier-v3.png", nil
		}
	} else {
		if forPreMigration {
			return "https://epifi-icons.pointz.in/rewards/fc-fp-webpage-highertier-v3.png", nil
		} else {
			return "https://epifi-icons.pointz.in/rewards/fc-fp-webpage-post-migration-highertier-v5.png", nil
		}
	}
}
