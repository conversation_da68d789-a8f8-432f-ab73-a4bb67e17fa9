package investment

import (
	"fmt"
	"sort"
	"strings"
	"time"

	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/logger"
	mfpb "github.com/epifi/gamma/api/investment/mutualfund"
	invOrderPb "github.com/epifi/gamma/api/investment/mutualfund/order"
	invPhPb "github.com/epifi/gamma/api/investment/mutualfund/payment_handler"
)

const (
	InvestmentRequest   = "Investment request"
	PaymentReceived     = "Payment received"
	FundHouseProcessing = "Fund House processing"
	UnitAllocation      = "Unit allocation"
	WithdrawalRequest   = "Withdrawal request"
	SellingUnits        = "Selling units"
	MoneyTransfer       = "Money transfer"
	ProcessingDelayed   = "Processing delayed"
	Success             = "Success"
	Processing          = "Processing"
	ISTTimeZoneLocation = "Asia/Kolkata"
)

/*
We are maintaining three things for any type of order whose timeline we wish to display
  - <>Timeline : String array of steps we wish to show in the receipt. These names would used as the name of the step
  - <>SuccessOrderStatuses : 2D array to store possible success states for a particular step provided in timeline.
    The step would remain in pending until one of the success status is not present in the timeline

For Buy orders
- From CREATED to SENT_TO_RTA -> Investment Request
- From SENT_TO_RTA to PAID -> Payment received
- From PAID to  IN_FULFILLMENT -> Fund House Processing
- After Mis upload till Reverse feed is read -> Unit Allocation

For Sell Orders
- From Created to oms order creation -> Withdrawal Request
- SendOrderToVendor -> Fund House Processing complete
- Reverse Feed file -> Selling units
- Another feed file for money transfer -> Money Transfer
*/
var (
	AutoInvestTimeline = []string{
		InvestmentRequest,
		PaymentReceived,
		FundHouseProcessing,
		UnitAllocation,
	}
	AutoInvestSuccessOrderStatuses = [][]invOrderPb.OrderStatus{
		{invOrderPb.OrderStatus_SENT_TO_RTA},
		{invOrderPb.OrderStatus_PAID},
		{invOrderPb.OrderStatus_NOTIFYING_PAYMENT_CREDIT},
		{invOrderPb.OrderStatus_CONFIRMED_BY_RTA},
	}

	WithdrawalTimeline = []string{
		WithdrawalRequest,
		FundHouseProcessing,
		SellingUnits,
		MoneyTransfer,
	}
	WithdrawalSuccessOrderStatuses = [][]invOrderPb.OrderStatus{
		{invOrderPb.OrderStatus_INITIATED},
		{invOrderPb.OrderStatus_SENT_TO_RTA},
		{invOrderPb.OrderStatus_IN_SETTLEMENT},
		{invOrderPb.OrderStatus_SETTLED},
	}

	ExternalInvestTimeline = []string{
		UnitAllocation,
	}
	ExternalInvestOrderStatuses = [][]invOrderPb.OrderStatus{
		{invOrderPb.OrderStatus_CONFIRMED_BY_RTA},
	}

	ExternalWithdrawalTimeline = []string{
		SellingUnits,
		MoneyTransfer,
	}
	ExternalWithdrawalStatuses = [][]invOrderPb.OrderStatus{
		{invOrderPb.OrderStatus_IN_SETTLEMENT},
		{invOrderPb.OrderStatus_SETTLED},
	}
	FailureStatuses = []invOrderPb.OrderStatus{
		invOrderPb.OrderStatus_FAILURE,
		invOrderPb.OrderStatus_EXPIRED,
	}
	RetryStatuses = []invOrderPb.OrderStatus{
		invOrderPb.OrderStatus_MANUAL_INTERVENTION,
	}

	SuccessStatuses = []invOrderPb.OrderStatus{
		invOrderPb.OrderStatus_CONFIRMED_BY_RTA,
		invOrderPb.OrderStatus_SETTLED,
	}

	OrderManualInterventionReasonToUserText = map[invOrderPb.FailureReason]string{
		invOrderPb.FailureReason_FAILURE_REASON_UNSPECIFIED:          DefaultManualInterventionReason,
		invOrderPb.FailureReason_FWD_ORDER_FEED_FILE_UPLOAD_ERROR:    FundHouseNotReachableFailureReason,
		invOrderPb.FailureReason_PAYMENT_ERROR_INTERNAL_SERVER_ERROR: DefaultManualInterventionReason,
	}
)

const (
	DefaultManualInterventionReason    = "We'll retry & update if successful."
	FundHouseNotReachableFailureReason = "Fund House not reachable. We'll retry."
)

type OrderStepStatus int32

const (
	OrderStepStatus_ORDER_STEP_STATUS_UNSPECIFIED OrderStepStatus = iota
	OrderStepStatus_SUCCESS
	OrderStepStatus_FAILED
	OrderStepStatus_PENDING
)

func (s OrderStepStatus) String() string {
	switch s {
	case OrderStepStatus_SUCCESS:
		return "SUCCESS"
	case OrderStepStatus_FAILED:
		return "FAILED"
	case OrderStepStatus_PENDING:
		return "PENDING"
	default:
		return "ORDER_STEP_STATUS_UNSPECIFIED"
	}
}

type SuccessStatusInfo struct {
	// Would be 'Success'
	StatusString string
	// Timestamp of the step
	TimestampString string
	// Success timestamp, used for sorting purposes
	SuccessTimestamp *timestamp.Timestamp
}

type PendingStatusInfo struct {
	// Would be 'Pending'
	StatusString string
	// Timestamp of the step Eg: 'ETA 23 Jan'
	TimestampString string
	// Reason for order being in Pending Status if any.
	ReasonString string
}

type FailedStatusInfo struct {
	// Would be something like 'Failed at 23 Jan 8:10 PM'
	StatusString string
	// failure reason
	FailureReason invOrderPb.FailureReason
	// Boolean to inform client to display the 'retry' option or not
	IsRetryable bool
	// Failed timestamp, used for sorting purposes
	FailedTimestamp *timestamp.Timestamp
}

type OrderTimelineEntry struct {
	// Display name of the order step
	// Eg: 'Investment Request' or 'Unit Allocation'
	OrderStepName string
	// Enum for the status of the step 'Success', 'Failed' or 'Pending'
	// Based on this the status details would be present
	StepStatus OrderStepStatus
	// Types that are assignable to StatusInfo:
	//	SuccessInfo
	SuccessInfo *SuccessStatusInfo
	//	FailedInfo
	FailedInfo *FailedStatusInfo
	//	PendingInfo
	PendingInfo *PendingStatusInfo
	// be order state
	InternalOrderStatus invOrderPb.OrderStatus
}

type OrderTimeline struct {
	order             *invOrderPb.Order
	orderStatusUpdate []*invOrderPb.OrderStatusUpdate
	mutualFund        *mfpb.MutualFund
	paymentStatus     invPhPb.PaymentStatus
	txnTime           *timestamp.Timestamp
}

func NewOrderTimeline(order *invOrderPb.Order, orderStatusTimeline []*invOrderPb.OrderStatusUpdate,
	paymentStatus invPhPb.PaymentStatus, txnTime *timestamp.Timestamp, mutualFund *mfpb.MutualFund) *OrderTimeline {
	return &OrderTimeline{
		order:             order,
		orderStatusUpdate: orderStatusTimeline,
		mutualFund:        mutualFund,
		paymentStatus:     paymentStatus,
		txnTime:           txnTime,
	}
}

type Timeline []*OrderTimelineEntry

/*
	Order timeline shows the lifecycle of an investment order in chronological order
	The approach for resolving the timeline is as follows
	- Get Complete order status updates from backend in DESC order. Remove duplicate status updates and keep the
	  latest status updates and sort the same in ASC order
	- For each step in the timeline, iterate the ASC sorted Status timeline
	- If we find a 'Success' status for that step, we mark the step success and move on to the next step
	- If we do not find any 'Success' status, then either the step is pending or it has failed
	- We then check the latest status update, if the status is one of the failure or retry step, we mark the step failed
    - Otherwise, it is marked pending
*/
// nolint:funlen
func (o *OrderTimeline) GetTimelineFromStatusUpdates() (Timeline, error) {

	timeLineSteps, terminalStates := o.getTimeLineStepsAndStates()
	timeline := make([]*OrderTimelineEntry, 0)
	statusInd := 0
	ascStatusUpdates := getLatestStatusInASCOrder(o.orderStatusUpdate)

	for stepInd, stepName := range timeLineSteps {
		stepDetails := &OrderTimelineEntry{
			OrderStepName: stepName,
		}
		succFlag := false
		for ; statusInd < len(ascStatusUpdates); statusInd++ {
			if StatusList(terminalStates[stepInd]).contains(ascStatusUpdates[statusInd].GetOrderStatus()) {
				succFlag = true
				stepDetails.StepStatus = OrderStepStatus_SUCCESS
				statInfo, err := getSuccessStatusInfo(ascStatusUpdates[statusInd].GetUpdatedAt().AsTime(),
					stepName, o.paymentStatus, o.txnTime)
				if err != nil {
					return nil, err
				}
				stepDetails.SuccessInfo = statInfo
				stepDetails.InternalOrderStatus = ascStatusUpdates[statusInd].GetOrderStatus()
				timeline = append(timeline, stepDetails)
				break
			}
		}

		// If there are no status updates that means the order is in 'CREATED' state
		// and we need to show the current step of the timeline in 'PENDING' status
		if len(ascStatusUpdates) == 0 {
			// Add pending state for current step
			stepDetails.StepStatus = OrderStepStatus_PENDING
			eta, err := GetETADate(ETAParams{
				PendingOrder:  o.order,
				AssetClass:    o.mutualFund.GetAssetClass(),
				PaymentStatus: o.paymentStatus,
				PaymentTime:   o.txnTime,
				CategoryName:  o.mutualFund.GetCategoryName(),
			})
			if err != nil {
				return nil, err
			}
			statInfo := getPendingStatusInfo(eta)
			stepDetails.PendingInfo = statInfo
			stepDetails.InternalOrderStatus = o.order.GetOrderStatus()
			timeline = append(timeline, stepDetails)
			break
		}

		// Check if we evaluated all status updates and all success states processed
		if !succFlag && len(ascStatusUpdates) > 0 && statusInd == len(ascStatusUpdates) {
			// Get latest status updated
			latestUpdate := ascStatusUpdates[len(ascStatusUpdates)-1]
			// Check for Failed and retry steps
			if StatusList(RetryStatuses).contains(latestUpdate.GetOrderStatus()) {
				stepDetails.StepStatus = OrderStepStatus_PENDING
				statInfo := getRetryStatusInfo(o.order)
				stepDetails.PendingInfo = statInfo
				stepDetails.InternalOrderStatus = latestUpdate.GetOrderStatus()
				timeline = append(timeline, stepDetails)
				break
			}
			if StatusList(FailureStatuses).contains(latestUpdate.GetOrderStatus()) {
				stepDetails.StepStatus = OrderStepStatus_FAILED
				statInfo, err := getFailedStatusInfo(latestUpdate.GetUpdatedAt().AsTime(), stepName, o.order, false,
					o.paymentStatus, o.txnTime)
				if err != nil {
					return nil, err
				}
				stepDetails.FailedInfo = statInfo
				stepDetails.InternalOrderStatus = latestUpdate.GetOrderStatus()
				timeline = append(timeline, stepDetails)
				break
			}
			// Check for Success step and add the same
			if StatusList(SuccessStatuses).contains(latestUpdate.GetOrderStatus()) {
				stepDetails.StepStatus = OrderStepStatus_SUCCESS
				statInfo, err := getSuccessStatusInfo(latestUpdate.GetUpdatedAt().AsTime(),
					stepName, o.paymentStatus, o.txnTime)
				if err != nil {
					return nil, err
				}
				stepDetails.SuccessInfo = statInfo
				stepDetails.InternalOrderStatus = latestUpdate.GetOrderStatus()
				timeline = append(timeline, stepDetails)
				break
			}
			// Add pending state
			stepDetails.StepStatus = OrderStepStatus_PENDING
			eta, err := GetETADate(ETAParams{
				PendingOrder:  o.order,
				AssetClass:    o.mutualFund.GetAssetClass(),
				PaymentStatus: o.paymentStatus,
				PaymentTime:   o.txnTime,
				CategoryName:  o.mutualFund.GetCategoryName(),
			})
			if err != nil {
				return nil, err
			}
			statInfo := getPendingStatusInfo(eta)
			stepDetails.PendingInfo = statInfo
			stepDetails.InternalOrderStatus = latestUpdate.GetOrderStatus()
			timeline = append(timeline, stepDetails)
			break
		}
	}

	/*
		TODO (Ayush): Remove after figuring out a better way to handle this
		Do this only if order is for onetime investment.
		- Append PaymentReceived Step if not present.
			- For Appending check the payment status and then add corresponding info
		- Stable sort the timeline
	*/
	if o.order.GetOrderType() == invOrderPb.OrderType_BUY {
		isPaymentStepPresent := Timeline(timeline).contains(PaymentReceived)
		if !isPaymentStepPresent {
			stepDetails := &OrderTimelineEntry{
				OrderStepName: PaymentReceived,
			}
			switch o.paymentStatus {
			case invPhPb.PaymentStatus_PAYMENT_STATUS_SUCCESSFUL:
				statInfo, err := getSuccessStatusInfo(time.Time{}, PaymentReceived, o.paymentStatus, o.txnTime)
				if err != nil {
					return nil, err
				}
				stepDetails.SuccessInfo = statInfo
				stepDetails.StepStatus = OrderStepStatus_SUCCESS
				timeline = append(timeline, stepDetails)
			case invPhPb.PaymentStatus_PAYMENT_STATUS_FAILED:
				statInfo, err := getFailedStatusInfo(time.Time{}, PaymentReceived, o.order, true, o.paymentStatus, o.txnTime)
				if err != nil {
					return nil, err
				}
				stepDetails.FailedInfo = statInfo
				stepDetails.InternalOrderStatus = o.order.GetOrderStatus()
				stepDetails.StepStatus = OrderStepStatus_FAILED
				timeline = append(timeline, stepDetails)
			case invPhPb.PaymentStatus_PAYMENT_STATUS_PENDING, invPhPb.PaymentStatus_PAYMENT_STATUS_UNSPECIFIED:
				eta, err := GetETADate(ETAParams{
					PendingOrder:  o.order,
					AssetClass:    o.mutualFund.GetAssetClass(),
					PaymentStatus: o.paymentStatus,
					PaymentTime:   o.txnTime,
					CategoryName:  o.mutualFund.GetCategoryName(),
				})
				if err != nil {
					return nil, err
				}
				statInfo := getPendingStatusInfo(eta)
				stepDetails.PendingInfo = statInfo
				stepDetails.InternalOrderStatus = o.order.GetOrderStatus()
				stepDetails.StepStatus = OrderStepStatus_PENDING
				// Prepending the payment step as for onetime investment and Auto Invest  it should always be the first step if in pending
				timeline = append([]*OrderTimelineEntry{stepDetails}, timeline...)
			}
		}
	}
	sort.Stable(Timeline(timeline))
	return timeline, nil
}

func (o *OrderTimeline) getTimeLineStepsAndStates() ([]string, [][]invOrderPb.OrderStatus) {
	if o.order.GetOrderType() == invOrderPb.OrderType_BUY {
		switch o.order.GetOrderSubType() {
		case invOrderPb.OrderSubType_BUY_EXTERNAL, invOrderPb.OrderSubType_SWITCH_IN_EXTERNAL, invOrderPb.OrderSubType_SWITCH_IN, invOrderPb.OrderSubType_RECONCILIATION_ORDER:
			return ExternalInvestTimeline, ExternalInvestOrderStatuses
		default:
			return AutoInvestTimeline, AutoInvestSuccessOrderStatuses
		}
	} else {
		switch o.order.GetOrderSubType() {
		case invOrderPb.OrderSubType_SELL_EXTERNAL, invOrderPb.OrderSubType_SWITCH_OUT_EXTERNAL, invOrderPb.OrderSubType_SWITCH_OUT, invOrderPb.OrderSubType_RECONCILIATION_ORDER:
			return ExternalWithdrawalTimeline, ExternalWithdrawalStatuses
		default:
			return WithdrawalTimeline, WithdrawalSuccessOrderStatuses
		}
	}
}

/*
Expecting the passed records to be in descending order.
Iterating the timeline, removing duplicate statuses and prepending the entries
to get timeline sorted in ascending order.
*/
func getLatestStatusInASCOrder(records []*invOrderPb.OrderStatusUpdate) []*invOrderPb.OrderStatusUpdate {
	dupMap := make(map[invOrderPb.OrderStatus]bool)
	timeline := make([]*invOrderPb.OrderStatusUpdate, 0)
	for _, rec := range records {
		if !dupMap[rec.GetOrderStatus()] {
			timeline = append([]*invOrderPb.OrderStatusUpdate{
				rec,
			}, timeline...)
			dupMap[rec.GetOrderStatus()] = true
		}
	}
	return timeline
}

/*
Overriding relevant interface functions for enabling sorting on the Timeline
TODO (Ayush): Remove all these when we upgrade to go 1.18 and use 'sort.Slice' instead.
*/
func (t Timeline) Len() int {
	return len(t)
}

func (t Timeline) Less(i, j int) bool {
	statusI := t[i].StepStatus
	statusJ := t[j].StepStatus
	switch {
	// Terminal State should always come before non-terminal step in the timeline
	case !isStepTerminal(statusI) && isStepTerminal(statusJ):
		return false
	case isStepTerminal(statusI) && !isStepTerminal(statusJ):
		return true
	// If both terminal then the one with update Time before should be 'lesser'
	case isStepTerminal(statusI) && isStepTerminal(statusJ):
		timeI, err := getTimeFromTimelineStep(t[i])
		if err != nil {
			logger.Warn("order timeline is having invalid terminal step, skipping sorting",
				zap.Error(err), zap.Any("timelineStep", t[i]))
			return false
		}
		timeJ, err := getTimeFromTimelineStep(t[j])
		if err != nil {
			logger.Warn("order timeline is having invalid terminal step, skipping sorting",
				zap.Error(err), zap.Any("timelineStep", t[j]))
			return false
		}

		return timeI.Before(timeJ)
	// If both steps are non-terminal keep as is.
	case !isStepTerminal(statusI) && !isStepTerminal(statusJ):
		logger.Warn("order timeline is having multiple non terminal steps",
			zap.Any("timelineStep1", t[i]), zap.Any("timelineStep2", t[j]))
		return false
	}
	logger.Warn("Not sorting as the timeline steps do not conform to any expected scenario",
		zap.Any("timelineStep1", t[i]), zap.Any("timelineStep2", t[j]))
	return false
}

func (t Timeline) Swap(i, j int) {
	t[i], t[j] = t[j], t[i]
}

func (t Timeline) contains(stepToCheck string) bool {
	for _, s := range t {
		if strings.EqualFold(s.OrderStepName, stepToCheck) {
			return true
		}
	}
	return false
}

func isStepTerminal(status OrderStepStatus) bool {
	return status != OrderStepStatus_PENDING
}

func getTimeFromTimelineStep(step *OrderTimelineEntry) (time.Time, error) {
	if step.StepStatus == OrderStepStatus_SUCCESS {
		return step.SuccessInfo.SuccessTimestamp.AsTime(), nil
	} else if step.StepStatus == OrderStepStatus_FAILED {
		return step.FailedInfo.FailedTimestamp.AsTime(), nil
	}
	return time.Time{}, fmt.Errorf("invalid terminal status: '%s' in the timeline", step.StepStatus)
}

func getFailedStatusInfo(orderUpdateTime time.Time, stepName string, order *invOrderPb.Order, isRetryable bool,
	paymentStatus invPhPb.PaymentStatus, txnTime *timestamp.Timestamp) (*FailedStatusInfo, error) {
	timeZoneLocation, err := time.LoadLocation(ISTTimeZoneLocation)
	if err != nil {
		return nil, fmt.Errorf("error while trying to load IST time zone: %w", err)
	}
	var (
		timeStampString string
		failureTime     time.Time
	)
	// For Payment received step we would be using the timestamp from the Transaction
	if strings.EqualFold(stepName, PaymentReceived) && paymentStatus == invPhPb.PaymentStatus_PAYMENT_STATUS_FAILED {
		// If transaction time is zero, fallback to using order updated time
		if txnTime.AsTime().Unix() == 0 {
			if !orderUpdateTime.IsZero() {
				failureTime = orderUpdateTime.In(timeZoneLocation)
				timeStampString = fmt.Sprintf("Failed at %s", failureTime.Format("02 Jan 03:04 PM"))
			} else {
				timeStampString = "Failed"
			}
		} else {
			failureTime = txnTime.AsTime().In(timeZoneLocation)
			timeStampString = fmt.Sprintf("Failed at %s", failureTime.Format("02 Jan 03:04 PM"))
		}
	} else {
		if orderUpdateTime.IsZero() {
			timeStampString = "Failed"
		} else {
			failureTime = orderUpdateTime.In(timeZoneLocation)
			timeStampString = fmt.Sprintf("Failed at %s", failureTime.Format("02 Jan 03:04 PM"))
		}
	}

	return &FailedStatusInfo{
		StatusString:    timeStampString,
		FailureReason:   order.GetFailureReason(),
		IsRetryable:     isRetryable,
		FailedTimestamp: timestamp.New(failureTime),
	}, nil
}

type StatusList []invOrderPb.OrderStatus

func (status StatusList) contains(statusToCheck invOrderPb.OrderStatus) bool {
	for _, s := range status {
		if s == statusToCheck {
			return true
		}
	}
	return false
}

func getPendingStatusInfo(eta time.Time) *PendingStatusInfo {
	if eta.Before(time.Now()) {
		return &PendingStatusInfo{
			StatusString:    ProcessingDelayed,
			TimestampString: "",
			ReasonString:    "",
		}
	} else {
		displayString := fmt.Sprintf("ETA %s", eta.Format("02 January"))
		return &PendingStatusInfo{
			StatusString:    Processing,
			TimestampString: displayString,
		}
	}
}

func getSuccessStatusInfo(orderUpdateTime time.Time, stepName string,
	paymentStatus invPhPb.PaymentStatus, txnTime *timestamp.Timestamp) (*SuccessStatusInfo, error) {
	timeZoneLocation, err := time.LoadLocation(ISTTimeZoneLocation)
	var successTime time.Time
	// For Payment received step we would be using the timestamp from the Transaction
	if strings.EqualFold(stepName, PaymentReceived) && paymentStatus == invPhPb.PaymentStatus_PAYMENT_STATUS_SUCCESSFUL {
		// If transaction time is zero, fallback to order update time
		if txnTime.AsTime().Unix() == 0 {
			successTime = orderUpdateTime.In(timeZoneLocation)
		} else {
			successTime = txnTime.AsTime().In(timeZoneLocation)
		}
	} else {
		successTime = orderUpdateTime.In(timeZoneLocation)
	}

	if err != nil {
		return nil, fmt.Errorf("error while trying to load IST time zone: %w", err)
	}
	return &SuccessStatusInfo{
		StatusString:     Success,
		TimestampString:  successTime.Format("02 Jan 03:04 PM"),
		SuccessTimestamp: timestamp.New(successTime),
	}, nil
}

func getRetryStatusInfo(order *invOrderPb.Order) *PendingStatusInfo {
	reason, ok := OrderManualInterventionReasonToUserText[order.GetFailureReason()]
	if !ok {
		return &PendingStatusInfo{
			StatusString: ProcessingDelayed,
			ReasonString: DefaultManualInterventionReason,
		}
	}
	return &PendingStatusInfo{
		StatusString: ProcessingDelayed,
		ReasonString: reason,
	}
}
