package tcs

import (
	"context"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/google/uuid"
	"github.com/google/wire"
	"github.com/pkg/errors"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	iftVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	iftPkg "github.com/epifi/gamma/pkg/internationalfundtransfer"
	moneyPkg "github.com/epifi/be-common/pkg/money"
)

var APIBasedTCSCalculatorWireSet = wire.NewSet(NewAPIBasedTCSCalculator, wire.Bind(new(iftPkg.APIBasedTCSCalculatorType), new(*APIBasedTCSCalculator)))

type APIBasedTCSCalculator struct {
	bankCustomerServiceClient bankCustomerPb.BankCustomerServiceClient
	iftVgClient               iftVgPb.InternationalFundTransferClient
}

func NewAPIBasedTCSCalculator(
	bankCustomerServiceClient bankCustomerPb.BankCustomerServiceClient,
	iftVgClient iftVgPb.InternationalFundTransferClient,
) *APIBasedTCSCalculator {
	return &APIBasedTCSCalculator{
		bankCustomerServiceClient: bankCustomerServiceClient,
		iftVgClient:               iftVgClient,
	}
}

// CalculateTCS depends on partner bank to memorize the total amount remittance in a finanacial year
// and hence doesn't require the total amount remitted.
func (s *APIBasedTCSCalculator) CalculateTCS(ctx context.Context, req *iftPkg.TCSCalculatorRequest) (*iftPkg.TCS, error) {
	if req.ActorId == "" {
		return nil, errors.New("actor id is required")
	}
	if req.CurrentTransactionAmount.GetCurrencyCode() != moneyPkg.RupeeCurrencyCode {
		return nil, errors.Errorf("invalid currency code: %s", req.CurrentTransactionAmount.GetCurrencyCode())
	}
	if !moneyPkg.IsValid(req.CurrentTransactionAmount) {
		return nil, errors.Errorf("invalid amount: %v", req.CurrentTransactionAmount)
	}
	if moneyPkg.IsNegative(req.CurrentTransactionAmount) {
		return nil, errors.Errorf("negative amount: %v", req.CurrentTransactionAmount)
	}
	customerRes, err := s.bankCustomerServiceClient.GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: req.ActorId},
	})
	if err = epifigrpc.RPCError(customerRes, err); err != nil {
		return nil, errors.Wrapf(err, "error getting bank customer for actor id: %s", req.ActorId)
	}
	res, err := s.iftVgClient.CalculateTcs(ctx, &iftVgPb.CalculateTcsRequest{
		Header:          &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		CustomerId:      customerRes.GetBankCustomer().GetVendorCustomerId(),
		AmountInInr:     req.CurrentTransactionAmount,
		RequestType:     iftVgPb.TCSRequestType_TCS_REQUEST_TYPE_CALCULATE,
		ClientReqId:     uuid.NewString(),
		TransactionDate: datetime.TimestampToDateInLoc(timestampPb.Now(), datetime.IST),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return nil, errors.Wrap(err, "error getting TCS")
	}
	return &iftPkg.TCS{
		TaxableAmount: res.GetTcsApplicableAmount(),
		Tax:           moneyPkg.Round(res.GetTcsAmount(), iftPkg.MaxBankSupportedPrecision),
	}, nil
}

func (s *APIBasedTCSCalculator) CalculateTotalAmountRemittedFY(_, _ *moneyPb.Money) (*moneyPb.Money, error) {
	return nil, errors.New("unimplemented for API based TCS calculator")
}
