package countrystdinfo

type CountryPhoneInfo struct {
	ISDCode uint32
	// Minimum number of phone number digits excluding ISD Code
	MinDigits uint32
	// Maximum number of phone number digits excluding ISD Code
	MaxDigits uint32
}

func (p *CountryPhoneInfo) GetISDCode() uint32 {
	if p == nil {
		return 0
	}
	return p.ISDCode
}

func (p *CountryPhoneInfo) GetMinDigits() uint32 {
	if p == nil {
		return 0
	}
	return p.MinDigits
}

func (p *CountryPhoneInfo) GetMaxDigits() uint32 {
	if p == nil {
		return 0
	}
	return p.MaxDigits
}
