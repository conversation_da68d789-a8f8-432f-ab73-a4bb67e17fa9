package dao

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/tools/dao_metrics_gen/metric_util"

	"github.com/epifi/gamma/pkg/changefeed/dao/model"
)

const upperLimit = 5000

type ChangeFeedCRDB struct {
	db *gorm.DB
}

func NewChangefeedDao(db *gorm.DB) ChangeFeedDao {
	return &ChangeFeedCRDB{
		db: db,
	}
}

var _ ChangeFeedDao = &ChangeFeedCRDB{}

func (c *ChangeFeedCRDB) Create(ctx context.Context, rowIdentifier, tableName, updatedColumn string, changes []*model.Change) error {
	defer metric_util.TrackDuration("pkg/changefeed/dao", "ChangeFeedCRDB", "Create", time.Now())
	// Defensive check to avoid creating a changefeed entry for the change_feeds table to avoid going into an infinite loop.
	if tableName == "change_feeds" {
		return fmt.Errorf("invalid tableName given: %s", tableName)
	}
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	changeFeedEntries := getChangeFeedEntriesFromChanges(tableName, updatedColumn, rowIdentifier, changes)
	if err := db.Create(changeFeedEntries).Error; err != nil {
		logger.Error(ctx, "error while creating an entry in the ChangeFeed table", zap.Error(err))
		return err
	}
	return nil
}

func (c *ChangeFeedCRDB) GetByRowIdentifierAndTableName(ctx context.Context, rowIdentifier, tableName string, limit int) ([]*model.ChangeFeed, error) {
	defer metric_util.TrackDuration("pkg/changefeed/dao", "ChangeFeedCRDB", "GetByRowIdentifierAndTableName", time.Now())
	if rowIdentifier == "" {
		return nil, fmt.Errorf("empty row identifier value provided")
	}
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	var changeFeeds []*model.ChangeFeed
	query := db.Model(&model.ChangeFeed{}).Where("table_name = ? AND row_identifier = ?", tableName, rowIdentifier).Find(&changeFeeds)
	if limit != 0 {
		query = query.Limit(lo.Min([]int{upperLimit, limit}))
	}
	if query.RowsAffected == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	if err := query.Error; err != nil {
		logger.Error(ctx, "error when fetching entries for given row identifier", zap.Error(err), zap.String("row_identifier", rowIdentifier))
		return nil, err
	}
	return changeFeeds, nil
}

func (c *ChangeFeedCRDB) GetByRowIdentifierAndColumnName(ctx context.Context, tableName, rowIdentifier, columnName string, limit int) ([]*model.ChangeFeed, error) {
	defer metric_util.TrackDuration("pkg/changefeed/dao", "ChangeFeedCRDB", "GetByRowIdentifierAndColumnName", time.Now())
	if tableName == "" || columnName == "" || rowIdentifier == "" {
		return nil, fmt.Errorf("empty argument provided")
	}
	db := gormctxv2.FromContextOrDefault(ctx, c.db)
	var changeFeeds []*model.ChangeFeed
	query := db.Debug().Model(&model.ChangeFeed{}).Where("table_name = ? AND row_identifier = ? AND updated_column = ?", tableName, rowIdentifier, columnName).Find(&changeFeeds).Order("table_name ASC, row_identifier ASC, updated_column ASC, created_at DESC")
	if limit != 0 {
		query = query.Limit(lo.Min([]int{upperLimit, limit}))
	}
	if query.RowsAffected == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	if err := query.Error; err != nil {
		return nil, err
	}
	return changeFeeds, nil
}

func getChangeFeedEntriesFromChanges(tableName, updatedColumn, rowIdentifier string, changes []*model.Change) []*model.ChangeFeed {
	var changeFeedEntries []*model.ChangeFeed
	for _, change := range changes {
		changeFeedEntry := &model.ChangeFeed{
			RowIdentifier: rowIdentifier,
			UpdatedColumn: updatedColumn,
			TableName:     tableName,
			ChangeLog:     *change,
		}
		changeFeedEntries = append(changeFeedEntries, changeFeedEntry)
	}
	return changeFeedEntries
}
