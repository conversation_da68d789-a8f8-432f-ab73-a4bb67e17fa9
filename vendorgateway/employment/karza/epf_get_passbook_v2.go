package karza

import (
	"context"
	"fmt"
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/vendorgateway/employment"
	"github.com/epifi/gamma/api/vendors/karza"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
)

const (
	successStatusCode = "101"
	// Incorrect OTP or request ID
	// OTP already used
	// OTP Session timeout
	invalidRequestStatusCode = "102"
	// No records found for the given ID or combination of inputs
	recordNotFoundStatusCode = "103"
	// Passbook already retried basis given request ID
	// User is already logged in on EPFO Unified Portal using another device
	maxRetriesExceededStatusCode = "104"
)

// GetEPFPassbookV2Request provides functionality for adapting to <PERSON>rz<PERSON>'s PF Passbook API.
type GetEPFPassbookV2Request struct {
	Req      *employment.EPFGetPassbookRequestV2
	Url, Key string
}

func (r *GetEPFPassbookV2Request) Add(req *http.Request) *http.Request {
	req.Header.Add("x-karza-key", r.Key)
	return req
}

// Marshal provides the json for checkliveness request call.
func (r *GetEPFPassbookV2Request) Marshal() ([]byte, error) {
	req := &karza.EPFGetPassbookRequestV2{
		RequestId:  r.Req.RequestId,
		Otp:        r.Req.Otp,
		EpfBalance: "Y",
	}
	return protojson.Marshal(req)
}

// URL provides the URL to send the request to
func (r *GetEPFPassbookV2Request) URL() string {
	return r.Url
}

// HTTPMethod returns the http method to use for the API call.
func (r *GetEPFPassbookV2Request) HTTPMethod() string {
	return http.MethodPost
}

// NewResponse returns Response struct that can deserialize the vendor response
func (r *GetEPFPassbookV2Request) GetResponse() vendorapi.Response {
	return &GetPFPassbookV2Response{}
}

type GetPFPassbookV2Response struct {
}

func (r *GetPFPassbookV2Response) Unmarshal(b []byte) (proto.Message, error) {
	m := karza.EPFGetPassbookResponseV2{}
	err := protojson.Unmarshal(b, &m)
	if err != nil {
		return nil, fmt.Errorf("failed to parse EPFGetPassbookResponseV2: %w", err)
	}

	if m.GetStatusCode() != successStatusCode {
		logger.ErrorNoCtx(fmt.Sprintf("Status code %s not OK for requestId %s", m.GetStatusCode(), m.GetRequestId()))
		var status *rpc.Status
		switch m.GetStatusCode() {
		case invalidRequestStatusCode:
			status = rpc.StatusInvalidArgument()
		case recordNotFoundStatusCode:
			status = rpc.StatusRecordNotFound()
		case maxRetriesExceededStatusCode:
			status = rpc.StatusResourceExhausted()
		default:
			return nil, fmt.Errorf("unhandled failure status code %s", m.GetStatusCode())
		}
		return &employment.EPFGetPassbookResponseV2{Status: status}, nil
	}
	if m.Result == nil {
		return nil, fmt.Errorf("empty result in EPFGetPassbookResponseV2")
	}

	return &employment.EPFGetPassbookResponseV2{
		Status:     rpc.StatusOk(),
		UanDetails: m.GetResult(),
	}, nil
}

func (r *GetPFPassbookV2Response) HandleHttpError(ctx context.Context, httpStatusCode int, responseBody []byte) (proto.Message, error) {
	m := karza.EPFGetPassbookResponseV2{}
	err := protojson.Unmarshal(responseBody, &m)
	if err != nil {
		logger.Error(ctx, "Could not parse error response: %w", zap.Error(err))
	}
	logger.Error(ctx, fmt.Sprintf("http error in GetPFPassbookV2Response requestId %s status %d message %s", m.GetRequestId(), m.GetStatus(), m.GetError()))
	res := &employment.EPFGetPassbookResponseV2{}
	if httpStatusCode == http.StatusServiceUnavailable {
		res.Status = rpc.StatusUnavailableWithDebugMsg(m.GetError())
	} else {
		res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("received http error code: %d, desc: %s", httpStatusCode, m.Error))
	}
	return res, nil
}
