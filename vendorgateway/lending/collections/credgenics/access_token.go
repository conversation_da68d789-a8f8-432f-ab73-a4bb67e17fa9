// nolint: dupl,errcheck
package credgenics

import (
	"context"
	"net/http"
	"time"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	vgCredgenicsPb "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	"github.com/epifi/gamma/vendorgateway/config"
)

// nolint: gosec
const (
	sgTokenKey         = "sg-credgenics_access_token"
	epifiTokenKey      = "epifi-credgenics_access_token"
	sgExpiryTimeKey    = "sg-credgenics_expiry_time"
	epifiExpiryTimeKey = "epifi-credgenics_expiry_time"
)

func (s *Service) getAccessTokenFromVendor(ctx context.Context, req *vgCredgenicsPb.CreateAccessTokenRequest) *vgCredgenicsPb.CreateAccessTokenResponse {
	vendorReq := &GetTokenRequest{
		Method: http.MethodPost,
		Req:    req,
		Conf:   s.conf.Application.Credgenics,
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in placing GetAccessTokenRequest", zap.Error(err))
		return &vgCredgenicsPb.CreateAccessTokenResponse{Status: vendorapi.GetStatusFromError(err)}
	}
	return res.(*vgCredgenicsPb.CreateAccessTokenResponse)
}

func (s *Service) getAuthToken(ctx context.Context, productVendor vendorgateway.Vendor) (string, error) {
	authSecrets, tokenKey, tokenExpiryTime := s.getSecretsAndTokenKeyAsPerVendor(productVendor)

	token := s.getTokenIfNotExpired(ctx, tokenKey, tokenExpiryTime)
	if token != "" {
		return token, nil
	}

	// taking lock to avoid multiple token fetch from credgenics
	err := s.tokenStore.AcquireLock(ctx)
	if err != nil {
		return "", errors.Wrap(err, "error while acquiring lock")
	}
	defer s.tokenStore.ReleaseLock(ctx)

	// rechecking if some other thread haven't fetched and updated the token
	reFetchedToken := s.getTokenIfNotExpired(ctx, tokenKey, tokenExpiryTime)

	if reFetchedToken != "" {
		return token, nil
	}

	// getting new token from cg
	res := s.getAccessTokenFromVendor(ctx, &vgCredgenicsPb.CreateAccessTokenRequest{
		ClientId:     authSecrets.ClientId,
		ClientSecret: authSecrets.ClientSecret,
	})
	if !res.GetStatus().IsSuccess() {
		return "", errors.New("error while getting access token for credgenics")
	}
	// storing token to token store with expiry
	expiresAt := res.GetExpiredAt().AsTime()
	err = s.tokenStore.SetToken(ctx, tokenKey, res.GetApiKey())
	if err != nil {
		return "", err
	}
	err = s.tokenStore.SetToken(ctx, tokenExpiryTime, expiresAt)
	if err != nil {
		return "", err
	}
	return res.GetApiKey(), nil
}

func (s *Service) getTokenIfNotExpired(ctx context.Context, tokenStringKey string, expiryTokenKey string) string {
	token, _ := s.tokenStore.GetToken(ctx, tokenStringKey)
	// if token not found in map, return empty string as we want to fetch it from vendor
	if token == nil {
		return ""
	}
	tokenValue := token.(string)
	expiry, _ := s.tokenStore.GetToken(ctx, expiryTokenKey)
	expiryTimeValue := expiry.(time.Time)

	if expiry != nil {
		isExpired := s.isTokenExpired(expiryTimeValue)
		if !isExpired {
			return tokenValue
		} else {
			logger.Info(ctx, "token expired")
		}
	}
	// not returning error from here as if the caller needs to know if it needs to fetch new token from vendor or not.
	// if we return error then client will not know whether error was because of token fetch failed from store or this
	// error was for token not present or expired.
	return ""
}

func (s *Service) isTokenExpired(tokenExpiry time.Time) bool {
	if !tokenExpiry.After(time.Now().Add(time.Minute * 2)) {
		return true
	}
	return false
}

func (s *Service) getSecretsAndTokenKeyAsPerVendor(productVendor vendorgateway.Vendor) (config.CredgenicsAuthenticationSecrets, string, string) {
	if productVendor == vendorgateway.Vendor_STOCK_GUARDIAN_LSP {
		return s.conf.Application.Credgenics.Secret.SgAuthenticationSecrets, sgTokenKey, sgExpiryTimeKey
	}

	return s.conf.Application.Credgenics.Secret.EpifiAuthenticationSecrets, epifiTokenKey, epifiExpiryTimeKey
}
