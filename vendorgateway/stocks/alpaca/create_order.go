package alpaca

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/vendorapi"

	stocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	alpacaPb "github.com/epifi/gamma/api/vendors/alpaca"
	"github.com/epifi/gamma/vendorgateway/config"
)

const (
	// Order Class represent behaviour of order execution
	// For Non-simple order class, there are array of Order entities associated with this order
	// reference: https://alpaca.markets/docs/api-references/trading-api/orders/#order-entity
	// Default is simple order class

	ORDER_CLASS_SIMPLE = "simple"

	// Represent the order is created with vendor and request is send again
	// Following comparison of error message because there is no unique identifier as
	// error code or http status from vendor

	DUPLICATE_CLIENT_ORDER_ID_MESSAGE = "client_order_id must be unique"
)

type CreateOrderReq struct {
	Method string
	Req    *stocksPb.CreateOrderRequest
	Conf   *config.Alpaca
}

func (g *CreateOrderReq) Marshal() ([]byte, error) {
	var err error
	limitPriceStr, err := getStrFromMoneyWithNilAllowed(g.Req.GetLimitPrice(), 0)
	if err != nil {
		logger.ErrorNoCtx("unable to convert money to string for LimitPrice")
		return nil, err
	}
	notionalStr, err := getStrFromMoneyWithNilAllowed(g.Req.GetNotional(), 0)
	if err != nil {
		logger.ErrorNoCtx("unable to convert money to string for Notional")
		return nil, err
	}
	stopPriceStr, err := getStrFromMoneyWithNilAllowed(g.Req.GetStopPrice(), 0)
	if err != nil {
		logger.ErrorNoCtx("unable to convert money to string for StopPrice")
		return nil, err
	}

	commissionAmountStr, commissionInBps, err := getCommission(g.Req)
	if err != nil {
		logger.ErrorNoCtx("unable to parse commission")
		return nil, err
	}

	sideStr, err := getStrFromSideType(g.Req.GetSide())
	if err != nil {
		logger.ErrorNoCtx("unable to parse order type")
		return nil, err
	}
	typeStr, err := getStrFromOrderType(g.Req.GetOrderType())
	if err != nil {
		logger.ErrorNoCtx("unable to parse Tradetype")
		return nil, err
	}
	timeInForceStr, err := getStrFromTimeInForce(g.Req.GetTimeInForce())
	if err != nil {
		logger.ErrorNoCtx("unable to parse timeInForce type")
		return nil, err
	}

	req := &alpacaPb.CreateOrderRequest{
		Symbol:        g.Req.GetSymbol(),
		Qty:           g.Req.GetQty(),
		Notional:      notionalStr,
		Side:          sideStr,
		Type:          typeStr,
		OrderClass:    ORDER_CLASS_SIMPLE,
		ClientOrderId: g.Req.GetClientOrderId(),
		TimeInForce:   timeInForceStr,
		LimitPrice:    limitPriceStr,
		StopPrice:     stopPriceStr,
		Commission:    commissionAmountStr,
		CommissionBps: commissionInBps,
	}
	return protojson.Marshal(req)
}

func getCommission(req *stocksPb.CreateOrderRequest) (commissionAmountStr, commissionInBpsStr string, err error) {
	if req.GetBrokerage().GetBrokerageAmount() != nil {
		commissionAmountStr, err = getStrFromMoneyWithNilAllowed(req.GetBrokerage().GetBrokerageAmount(), 0)
		if err != nil {
			logger.ErrorNoCtx("unable to convert money to string for commission")
			return "", "", err
		}
		return commissionAmountStr, "", err
	}

	// converting commission percentage to commission bps
	return "", fmt.Sprintf("%f", req.GetBrokerage().GetBrokerageInPercentage()*100), nil
}

func (g *CreateOrderReq) RedactRequestBody(ctx context.Context, requestBody []byte, contentType string) ([]byte, error) {
	return httpcontentredactor.GetInstance().Redact(ctx, requestBody, contentType, map[string]mask.MaskingStrategy{
		"qty": mask.MaskAllChars,
	})
}

func (g *CreateOrderReq) URL() string {
	return fmt.Sprintf("%v/%v/trading/accounts/%v/orders", g.Conf.BrokerApiHost, g.Conf.BrokerApiVersion, g.Req.GetAccountId())
}

func (g *CreateOrderReq) HTTPMethod() string {
	return g.Method
}
func (g *CreateOrderReq) SetAuth(r *http.Request) *http.Request {
	r.SetBasicAuth(g.Conf.Secret.BrokerApiKey, g.Conf.Secret.BrokerApiSecret)
	return r
}

func (g *CreateOrderReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (g *CreateOrderReq) GetResponse() vendorapi.Response {
	return &CreateOrderResp{
		Symbol: g.Req.GetSymbol(),
	}
}

func (g *CreateOrderReq) CanLogUnredactedEncryptedPayload() bool {
	return true
}

type CreateOrderResp struct {
	Symbol string
}

func (g *CreateOrderResp) Unmarshal(b []byte) (proto.Message, error) {
	res := &alpacaPb.CreateOrderResponse{Order: &alpacaPb.Order{}}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, res.Order)
	if err != nil {
		return nil, err
	}
	data := res.Order
	order, err := getOrderObject(data)
	if err != nil {
		return nil, errors.Wrap(err, "unable Parse OrderObject in createOrder")
	}

	return &stocksPb.CreateOrderResponse{
		Status: rpc.StatusOk(),
		Order:  order,
	}, nil
}

func (g *CreateOrderResp) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	errorMessage := &alpacaPb.AlpacaErrorMessage{}
	err := json.Unmarshal(b, &errorMessage)
	if err != nil {
		return nil, errors.Wrap(err, "unable unmarshal error message")
	}
	if errorMessage.GetMessage() == DUPLICATE_CLIENT_ORDER_ID_MESSAGE {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{
			Code: uint32(stocksPb.CreateOrderResponse_DUPLICATED_CLIENT_ORDER_ID),
		}}, nil
	}
	// to handle symbols not supporting fractional qty orders
	// sample vendor response
	// http_status = 403, response = {"code":********,"message":"asset \"ATXI\" is not fractionable"}
	if httpStatus == http.StatusForbidden && errorMessage.GetCode() == ******** && strings.Contains(errorMessage.GetMessage(), "not fractionable") {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{
			Code: uint32(stocksPb.CreateOrderResponse_FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL),
		}}, nil
	}
	// given symbol for order is inactive for trade at exchange
	// sample vendor response
	// http error, http_status = 422, response = {"code":40010001,"message":"asset BHG is not active"}
	if httpStatus == http.StatusUnprocessableEntity && errorMessage.GetCode() == 40010001 && strings.Contains(errorMessage.GetMessage(), "is not active") {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{
			Code: uint32(stocksPb.CreateOrderResponse_SYMBOL_INACTIVE_FOR_TRADE),
		}}, nil
	}
	// if request sell qty is less than portfolio qty for that symbol
	// then order is not placed with vendor
	// sample vendor response
	// http error, http_status = 403, response = {"available":"2.*********","code":********,"existing_qty":"2.*********","held_for_orders":"0","message":"insufficient qty available for order (requested: 2.857142857, available: 2.*********)","symbol":"ADCT"}
	if httpStatus == http.StatusForbidden && errorMessage.GetCode() == ******** && strings.Contains(errorMessage.GetMessage(), "insufficient qty available for order") {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{
			Code: uint32(stocksPb.CreateOrderResponse_INSUFFICIENT_SELL_QTY_REQUESTED),
		}}, nil
	}
	// that primarily impacts non-US Persons who invest in US PTP Securities
	// With effect from 1 January 2023, non-US Persons will incur a 10% withholding tax on gross proceeds from sales or trading of US PTP securities.
	// PTP symbols with no exceptions are by default blocked from being purchased.
	// but it is a configuration that can be modified as well
	// ref: https://www.irs.gov/individuals/international-taxpayers/partnership-withholding
	// PTP list: https://ibkr.info/node/4706
	// http error, http_status = 403, response = {"code":********,"message":"account not enabled for PTP no-exception order entry"}
	if httpStatus == http.StatusForbidden && errorMessage.GetCode() == ******** && strings.Contains(errorMessage.GetMessage(), "account not enabled for PTP") {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{
			Code: uint32(stocksPb.CreateOrderResponse_SYMBOL_IS_PTP_AND_ACCOUNT_IS_NOT_CONFIGURED),
		}}, nil
	}
	// given symbol for order is not found by alpaca.
	// possible reasons:
	// 1. Exchange has halted trading on the stock
	// sample vendor response
	// http error, http_status = 422, response = {"code":********,"message":"asset \"SUNW\" not found"}
	if httpStatus == http.StatusUnprocessableEntity && errorMessage.GetCode() == ******** && strings.EqualFold(errorMessage.GetMessage(), fmt.Sprintf("asset \"%s\" not found", g.Symbol)) {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{
			Code: uint32(stocksPb.CreateOrderResponse_SYMBOL_NOT_FOUND_AT_VENDOR),
		}}, nil
	}
	// given symbol for order is not tradable via alpaca
	// possible reasons:
	// 1. alpaca doesn't support this stock anymore
	// sample vendor response
	// http error, http_status = 422, response = {"code":********,"message":"asset \"SHPH\" is not tradable"}
	if httpStatus == http.StatusUnprocessableEntity && errorMessage.GetCode() == ******** && strings.EqualFold(errorMessage.GetMessage(), fmt.Sprintf("asset \"%s\" is not tradable", g.Symbol)) {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{
			Code: uint32(stocksPb.CreateOrderResponse_SYMBOL_NON_TRADABLE_AT_VENDOR),
		}}, nil
	}
	// user is trying to short selling, but we don't support short selling
	// https://www.investopedia.com/terms/s/shortselling.asp
	// sample vendor response
	// http error, http_status = 403, response = {"code":********,"message":"account is not allowed to short"}
	if httpStatus == http.StatusForbidden && errorMessage.GetCode() == ******** && strings.EqualFold(errorMessage.GetMessage(), "account is not allowed to short") {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{
			Code: uint32(stocksPb.CreateOrderResponse_SHORT_SELLING_IS_NOT_ALLOWED),
		}}, nil
	}

	// user doesn't have enough buying power.
	// example scenario: user has placed an order to withdraw all funds from their wallet, but then tries to buy stocks.
	// sample vendor response:
	// http error, http_status = 403, response = {"buying_power":"0","code":********","message":"insufficient buying power"}
	if httpStatus == http.StatusForbidden && errorMessage.GetCode() == ******** &&
		strings.EqualFold(strings.ToLower(errorMessage.GetMessage()), "insufficient buying power") {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{Code: uint32(stocksPb.CreateOrderResponse_INSUFFICIENT_BUYING_POWER)}}, nil
	}

	// user has placed an opposite side order just recently, and is probably trying to do wash trading.
	// https://www.investopedia.com/terms/w/washtrading.asp
	// sample vendor response
	// http error, http_status = 403, response = {"code":********,"existing_order_id":"xyz","message":"potential wash trade detected. use complex orders","reject_reason":"opposite side market/stop order exists"}
	if httpStatus == http.StatusForbidden && errorMessage.GetCode() == ******** &&
		strings.EqualFold(strings.ToLower(errorMessage.GetMessage()), "potential wash trade detected. use complex orders") &&
		strings.EqualFold(strings.ToLower(errorMessage.GetRejectReason()), "opposite side market/stop order exists") {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{Code: uint32(stocksPb.CreateOrderResponse_OPPOSITE_SIDE_ORDER_EXISTS)}}, nil
	}

	// order rejected by vendor due to day trade protection, this happens due to multiple buy sell order for same symbol on same day
	// max limit on number of intraday trades imposed by US government
	// sample vendor response:
	// http error, http_status = 403, response = {"code":40310100,"message":"trade denied due to pattern day trading protection"}
	if httpStatus == http.StatusForbidden && errorMessage.GetCode() == 40310100 &&
		strings.EqualFold(strings.ToLower(errorMessage.GetMessage()), "trade denied due to pattern day trading protection") {
		return &stocksPb.CreateOrderResponse{Status: &rpc.Status{Code: uint32(stocksPb.CreateOrderResponse_DAY_TRADING_PROTECTION)}}, nil
	}

	return nil, errors.New(fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
}

func (g *CreateOrderResp) CanLogUnredactedEncryptedPayload() bool {
	return true
}
