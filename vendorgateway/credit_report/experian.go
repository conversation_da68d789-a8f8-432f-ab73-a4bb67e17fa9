package credit_report

import (
	"fmt"
	"net/http"

	"google.golang.org/protobuf/proto"

	creditReportVgPb "github.com/epifi/gamma/api/vendorgateway/credit_report"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendorgateway/credit_report/experian"
)

// NewCreditReportRequest creates a new request for experian's credit report APIs depending on the type of the proto message.
func (s *Service) NewCreditReportRequest(req proto.Message) vendorapi.SyncRequest {
	conf, _ := config.Load()
	switch v := req.(type) {
	case *creditReportVgPb.CheckReportPresenceRequest:
		return &experian.CheckCreditReportPresenceReq{
			Method:      http.MethodPost,
			Req:         req.(*creditReportVgPb.CheckReportPresenceRequest),
			Url:         conf.Application.Experian.CheckCreditReportPresenceURL,
			ClientName:  conf.Secrets.Ids[config.ExperianCreditReportPresenceClientName],
			VoucherCode: conf.Secrets.Ids[config.ExperianVoucherCode],
		}
	case *creditReportVgPb.FetchReportRequest:
		return &experian.FetchCreditReportReq{
			Method:      http.MethodPost,
			Req:         req.(*creditReportVgPb.FetchReportRequest),
			Url:         conf.Application.Experian.FetchCreditReportURL,
			ClientName:  conf.Secrets.Ids[config.ExperianCreditReportFetchClientName],
			VoucherCode: conf.Secrets.Ids[config.ExperianVoucherCode],
		}
	case *creditReportVgPb.FetchReportForExistingUserRequest:
		return &experian.FetchCreditReportForExistingUserReq{
			Method:     http.MethodPost,
			Req:        req.(*creditReportVgPb.FetchReportForExistingUserRequest),
			Url:        conf.Application.Experian.FetchCreditReportForExistingUserURL,
			ClientName: conf.Secrets.Ids[config.ExperianCreditReportForExistingUserClientName],
		}
	case *creditReportVgPb.ExtendSubscriptionRequest:
		return &experian.ExtendSubscriptionReq{
			Method:     http.MethodPost,
			Req:        req.(*creditReportVgPb.ExtendSubscriptionRequest),
			Url:        conf.Application.Experian.FetchExtendSubscriptionURL,
			ClientName: conf.Secrets.Ids[config.ExperianExtendSubscriptionClientName],
		}
	default:
		logger.ErrorNoCtx(fmt.Sprintf("Unsupported request type %v", v))
		return nil
	}
}

func (s *Service) NewCreditReportRequestV1(req proto.Message) vendorapi.SyncRequest {
	conf, _ := config.Load()
	switch v := req.(type) {
	case *creditReportVgPb.FetchReportRequest:
		return &experian.FetchCreditReportReqV1{
			FetchCreditReportReq: &experian.FetchCreditReportReq{
				Method:      http.MethodPost,
				Req:         req.(*creditReportVgPb.FetchReportRequest),
				Url:         conf.Application.Experian.FetchCreditReportURLV1,
				ClientName:  conf.Secrets.Ids[config.ExperianCreditReportFetchClientName],
				VoucherCode: conf.Secrets.Ids[config.ExperianVoucherCode],
			},
			TokenStoreManager: s.TokenStoreManager,
		}
	case *creditReportVgPb.FetchReportForExistingUserRequest:
		return &experian.FetchCreditReportForExistingUserReqV1{
			FetchCreditReportForExistingUserReq: &experian.FetchCreditReportForExistingUserReq{
				Method:     http.MethodPost,
				Req:        req.(*creditReportVgPb.FetchReportForExistingUserRequest),
				Url:        conf.Application.Experian.FetchCreditReportForExistingUserURLV1,
				ClientName: conf.Secrets.Ids[config.ExperianCreditReportForExistingUserClientName],
			},
			TokenStoreManager: s.TokenStoreManager,
		}
	case *creditReportVgPb.ExtendSubscriptionRequest:
		return &experian.ExtendSubscriptionReqV1{
			ExtendSubscriptionReq: &experian.ExtendSubscriptionReq{
				Method:     http.MethodPost,
				Req:        req.(*creditReportVgPb.ExtendSubscriptionRequest),
				Url:        conf.Application.Experian.FetchExtendSubscriptionURLV1,
				ClientName: conf.Secrets.Ids[config.ExperianExtendSubscriptionClientName],
			},
			TokenStoreManager: s.TokenStoreManager,
		}
	default:
		logger.ErrorNoCtx(fmt.Sprintf("Unsupported request type %v", v))
		return nil
	}
}
