//nolint:unparam
package ignosis

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	datetimePkg "github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/vendorapi"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/api/typesv2"
	igVgPb "github.com/epifi/gamma/api/vendorgateway/aa/analytics/ignosis"
	igVendorPb "github.com/epifi/gamma/api/vendors/aa/analytics/ignosis"
	genConf "github.com/epifi/gamma/vendorgateway/config/genconf"
)

const (
	txnTimeStampLayout     = "2006-01-02T15:04:05"
	balanceTimeStampLayout = "2006-01-02T15:04:05.000Z"
)

type FastAnalysisRequest struct {
	Req     *igVgPb.GetFastAnalysisRequest
	GenConf *genConf.Config
}

type FastAnalysisResponse struct{}

func (r *FastAnalysisRequest) HTTPMethod() string {
	return http.MethodPost
}

func (r *FastAnalysisRequest) URL() string {
	baseUrl := r.GenConf.Application().AA().Ignosis().Url() + "/transactions/analysis/sync/consolidated-analysis/initiate"
	req, _ := http.NewRequest(http.MethodPost, baseUrl, nil)
	q := req.URL.Query()
	if r.Req.GetEmploymentType() == typesv2.EmploymentType_EMPLOYMENT_TYPE_SALARIED {
		q.Add("employmentType", "SALARIED")
	} else if r.Req.GetEmploymentType() == typesv2.EmploymentType_EMPLOYMENT_TYPE_SELF_EMPLOYED {
		q.Add("employmentType", "SELF_EMPLOYED")
	}
	// if other type of employment type, then don't add it in url since it is optional and only supports 2 values

	if r.Req.GetEmployer() != "" {
		// encode employer with base64
		encodedEmployerStr := base64.StdEncoding.EncodeToString([]byte(r.Req.GetEmployer()))
		q.Add("employer", encodedEmployerStr)
	}
	req.URL.RawQuery = q.Encode()
	return req.URL.String()
}

func (r *FastAnalysisRequest) Add(req *http.Request) *http.Request {
	req.Header["API_KEY"] = []string{r.GenConf.Application().AA().AaVgSecretsV1().IgnosisApiKey}
	return req
}

func (r *FastAnalysisRequest) GetResponse() vendorapi.Response {
	return &FastAnalysisResponse{}
}

func (r *FastAnalysisRequest) Marshal() ([]byte, error) {
	data, err := convertToVendorAccountsData(r.Req.GetData())
	if err != nil {
		return nil, errors.Wrap(err, "unable to fill data")
	}

	req := &igVendorPb.SyncFastAnalysisRequest{
		Data:       data,
		TrackingId: r.Req.GetTrackingId(),
	}

	a, err := protojson.Marshal(req)
	return a, err
}

func convertToVendorAccountsData(accountsData []*igVgPb.GetFastAnalysisRequest_Data) ([]*igVendorPb.SyncFastAnalysisRequest_Data, error) {
	var vendorData []*igVendorPb.SyncFastAnalysisRequest_Data
	for _, accountData := range accountsData {
		filledAccount, err := convertToVendorAccountData(accountData)
		if err != nil {
			return nil, errors.Wrap(err, "unable to fill account")
		}
		vendorData = append(vendorData, filledAccount)
	}
	return vendorData, nil
}

func convertToVendorAccountData(accountData *igVgPb.GetFastAnalysisRequest_Data) (*igVendorPb.SyncFastAnalysisRequest_Data, error) {
	if accountData == nil {
		return nil, errors.New("account data is nil")
	}
	summary, err := fillSummary(accountData.GetSummary())
	if err != nil {
		return nil, errors.Wrap(err, "unable to fill summary")
	}
	transactions, err := validateAndGetTransactions(accountData.GetTransactions())
	if err != nil {
		return nil, errors.Wrap(err, "unable to fill transactions")
	}
	account, err := validateAndGetAccount(accountData.GetProfile().GetAccount())
	if err != nil {
		return nil, errors.Wrap(err, "unable to get account")
	}
	return &igVendorPb.SyncFastAnalysisRequest_Data{
		Profile: &igVendorPb.SyncFastAnalysisRequest_Data_Profile{
			Account: account,
			// optional field Holders
			Holders: &igVendorPb.SyncFastAnalysisRequest_Data_Profile_Holders{
				Type:   strings.TrimPrefix(accountData.GetProfile().GetHolders().GetType().String(), "HOLDER_TYPE_"),
				Holder: fillHolders(accountData.GetProfile().GetHolders().GetHolder()),
			},
		},
		Summary:      summary,
		Transactions: transactions,
	}, nil
}

func validateAndGetAccount(account *igVgPb.GetFastAnalysisRequest_Data_Profile_Account) (*igVendorPb.SyncFastAnalysisRequest_Data_Profile_Account, error) {
	if account == nil {
		return nil, errors.New("account data is nil")
	}
	if account.GetNumber() == "" {
		return nil, errors.New("account number is empty")
	}
	if account.GetAccType() == caEnumPb.DepositAccountType_DEPOSIT_ACCOUNT_TYPE_TYPE_UNSPECIFIED {
		return nil, errors.New("account type is empty")
	}
	if account.GetFiType() == caEnumPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_TYPE_UNSPECIFIED {
		return nil, errors.New("account type is empty")
	}
	if account.GetBank() == typesv2.Bank_BANK_UNSPECIFIED {
		return nil, errors.New("account bank is empty")
	}
	return &igVendorPb.SyncFastAnalysisRequest_Data_Profile_Account{
		Number:  account.GetNumber(),
		AccType: getAccountType(account.GetAccType()),
		FiType:  getFiType(account.GetFiType()),
		Bank:    account.GetBank().String(),
		FipId:   account.GetFipId(),
		// optional
		LinkedAccRef: account.GetLinkedAccRef(),
	}, nil
}

func validateAndGetTransactions(vgTransactions []*igVgPb.GetFastAnalysisRequest_Data_Transaction) ([]*igVendorPb.SyncFastAnalysisRequest_Data_Transaction, error) {
	var transactions []*igVendorPb.SyncFastAnalysisRequest_Data_Transaction
	for _, txn := range vgTransactions {
		err := validateTransaction(txn)
		if err != nil {
			return nil, errors.Wrapf(err, "error validating transaction, ref_id: %s", txn.GetTxnRefId())
		}
		transactions = append(transactions, &igVendorPb.SyncFastAnalysisRequest_Data_Transaction{
			Type:                 strings.TrimPrefix(txn.GetType().String(), "TRANSACTION_TYPE_"),
			Amount:               txn.GetAmount(),
			Narration:            txn.GetNarration(),
			CurrentBalance:       txn.GetCurrentBalance(),
			TransactionTimestamp: bestEffortTimestampToString(txn.GetTransactionTimestamp(), txnTimeStampLayout),
			TxnRefId:             txn.GetTxnRefId(),
		})
	}
	return transactions, nil
}

func validateTransaction(txn *igVgPb.GetFastAnalysisRequest_Data_Transaction) error {
	if txn.GetTxnRefId() == "" {
		return errors.New("txn ref id is empty")
	}
	if txn.GetType() == caEnumPb.TransactionType_TRANSACTION_TYPE_TYPE_UNSPECIFIED {
		return errors.New("transaction type is empty")
	}
	if txn.GetAmount() == "" {
		return errors.New("transaction amount is empty")
	}
	if txn.GetCurrentBalance() == "" {
		return errors.New("transaction current balance is empty")
	}
	if !txn.GetTransactionTimestamp().IsValid() {
		return errors.New("transaction timestamp is invalid")
	}
	return nil
}

func fillSummary(summary *igVgPb.GetFastAnalysisRequest_Data_Summary) (*igVendorPb.SyncFastAnalysisRequest_Data_Summary, error) {
	if summary == nil {
		return nil, errors.New("summary is nil")
	}
	matAmount, err := bestEffortMoneyToString(summary.GetMaturityAmount(), 5)
	if err != nil {
		return nil, errors.Wrap(err, "unable to convert maturity amount to string")
	}
	principalAmount, err := bestEffortMoneyToString(summary.GetPrincipalAmount(), 5)
	if err != nil {
		return nil, errors.Wrap(err, "unable to convert principal amount to string")
	}
	interestPeriodicPayoutAmount, err := bestEffortMoneyToString(summary.GetInterestPeriodicPayoutAmount(), 5)
	if err != nil {
		return nil, errors.Wrap(err, "unable to convert interest periodic payout amount to string")
	}
	recurringAmount, err := bestEffortMoneyToString(summary.GetRecurringAmount(), 5)
	if err != nil {
		return nil, errors.Wrap(err, "unable to convert recurring amount to string")
	}
	currentBalanceAmount, err := bestEffortMoneyToString(summary.GetCurrentBalance(), 5)
	if err != nil {
		return nil, errors.Wrap(err, "unable to convert current balance amount to string")
	}
	err = datetimePkg.ValidateDate(summary.GetTransactionStartDate())
	if err != nil {
		return nil, errors.Wrapf(err, "error validating transaction start date: %s", summary.GetTransactionStartDate())
	}
	err = datetimePkg.ValidateDate(summary.GetTransactionEndDate())
	if err != nil {
		return nil, errors.Wrapf(err, "error validating transaction end date: %s", summary.GetTransactionEndDate())
	}
	return &igVendorPb.SyncFastAnalysisRequest_Data_Summary{
		// mandatory
		TransactionStartDate: datetimePkg.DateToString(summary.GetTransactionStartDate(), datetimePkg.DATE_LAYOUT_YYYYMMDD, datetimePkg.IST),
		TransactionEndDate:   datetimePkg.DateToString(summary.GetTransactionEndDate(), datetimePkg.DATE_LAYOUT_YYYYMMDD, datetimePkg.IST),
		// optional
		OpeningDate:                  bestEffortDateToString(datetimePkg.TimestampToDateInLoc(summary.GetOpeningDate(), datetimePkg.IST)),
		BalanceDateTime:              bestEffortTimestampToString(summary.GetBalanceDateTime(), balanceTimeStampLayout),
		Branch:                       summary.GetBranch(),
		Ifsc:                         summary.GetIfsc(),
		MicrCode:                     summary.GetMicrCode(),
		MaturityAmount:               matAmount,
		MaturityDate:                 bestEffortDateToString(summary.GetMaturityDate()),
		Description:                  summary.GetDescription(),
		InterestPayout:               summary.GetInterestPayout(),
		InterestRate:                 fmt.Sprintf("%.2f", summary.GetInterestRate()),
		PrincipalAmount:              principalAmount,
		TenureDays:                   summary.GetTenureDays(),
		TenureMonths:                 summary.GetTenureMonths(),
		TenureYears:                  summary.GetTenureYears(),
		InterestComputation:          summary.GetInterestComputation(),
		CompoundingFrequency:         summary.GetCompoundingFrequency(),
		InterestPeriodicPayoutAmount: interestPeriodicPayoutAmount,
		InterestOnMaturity:           summary.GetInterestOnMaturity(),
		CurrentValue:                 summary.GetCurrentValue(),
		RecurringAmount:              recurringAmount,
		RecurringDepositDay:          summary.GetRecurringDepositDay(),
		CurrentBalance:               currentBalanceAmount,
	}, nil
}

func bestEffortTimestampToString(ts *timestampPb.Timestamp, layout string) string {
	if ts == nil {
		return ""
	}
	return datetimePkg.TimestampToString(ts, layout, datetimePkg.IST)
}

func bestEffortDateToString(date *date.Date) string {
	if date == nil {
		return ""
	}
	return datetimePkg.DateToString(date, datetimePkg.DATE_LAYOUT_YYYYMMDD, datetimePkg.IST)
}

func bestEffortMoneyToString(money *money.Money, precision int32) (string, error) {
	if money == nil {
		return "", nil
	}
	amount, err := moneyPkg.ToString(money, precision)
	if err != nil {
		return "", errors.Wrap(err, "unable to convert money to string")
	}
	return amount, nil
}

func fillHolders(holders []*igVgPb.GetFastAnalysisRequest_Data_Profile_Holders_Holder) []*igVendorPb.SyncFastAnalysisRequest_Data_Profile_Holders_Holder {
	var resHolders []*igVendorPb.SyncFastAnalysisRequest_Data_Profile_Holders_Holder
	for _, holder := range holders {
		resHolders = append(resHolders, &igVendorPb.SyncFastAnalysisRequest_Data_Profile_Holders_Holder{
			Name:    holder.GetName().ToSentenceCaseString(),
			Mobile:  holder.GetMobile().ToStringNationalNumber(),
			Address: holder.GetAddress(),
			Email:   holder.GetEmail(),
			Pan:     holder.GetPan(),
		})
	}
	return resHolders
}

func getFiType(fiType caEnumPb.AccInstrumentType) string {
	return strings.TrimPrefix(fiType.String(), "ACC_INSTRUMENT_TYPE_")
}

func getAccountType(accType caEnumPb.DepositAccountType) string {
	return strings.TrimPrefix(accType.String(), "DEPOSIT_ACCOUNT_TYPE_")
}

func (c *FastAnalysisResponse) Unmarshal(b []byte) (proto.Message, error) {
	res := &igVendorPb.AnalysisShortResponse{}

	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, res)
	if err != nil {
		return nil, errors.Wrap(err, "unable to unmarshal byte array to ignosis proto message")
	}

	// TODO: remove later
	sizeInMBs := float64(len(b)) / (1024 * 1024)
	logger.InfoNoCtx("response size from FastAnalysis api", zap.Any("sizeInMBs", sizeInMBs),
		zap.String("TrackingId", res.GetTrackingId()), zap.String("ReferenceId", res.GetReferenceId()))

	vgResponse := &igVgPb.GetFastAnalysisResponse{
		Status:      rpcPb.StatusOk(),
		TrackingId:  res.GetTrackingId(),
		ReferenceId: res.GetReferenceId(),
		RawResponse: b,
	}
	return vgResponse, nil
}

func (c *FastAnalysisResponse) HandleHttpError(ctx context.Context, httpStatusCode int, responseBody []byte) (proto.Message, error) {
	logger.Error(ctx, "error in FastAnalysis api", zap.Int("httpStatusCode", httpStatusCode), zap.String("rawResponse", string(responseBody)))

	return &igVgPb.GetFastAnalysisResponse{
		Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("FastAnalysis api failed")),
	}, nil
}

// RedactRequestBody overrides the redaction method. Redacted logging is not required for this API hence returning
// empty response to avoid logging redacted request
func (r *FastAnalysisRequest) RedactRequestBody(ctx context.Context, b []byte, _ string) ([]byte, error) {
	newFilteredJson, err := extractAndConvert(b, "trackingId")
	if err != nil {
		logger.Error(ctx, "error while redacting request", zap.Error(err))
		return nil, nil
	}
	return newFilteredJson, nil
}

// RedactResponseBody overrides the redaction method. Redacted logging is not required for this API hence returning
// empty response to avoid logging redacted response
func (c *FastAnalysisResponse) RedactResponseBody(ctx context.Context, b []byte, _ string) ([]byte, error) {
	newFilteredJson, err := extractAndConvert(b, "trackingId", "referenceId")
	if err != nil {
		logger.Error(ctx, "error while redacting response", zap.Error(err))
		return nil, nil
	}
	return newFilteredJson, nil
}
