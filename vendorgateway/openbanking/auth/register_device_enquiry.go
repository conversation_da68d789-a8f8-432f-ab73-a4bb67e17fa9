package auth

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"fmt"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	vendorfedral "github.com/epifi/gamma/api/vendors/federal"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/vendorgateway/federal"
	federalCryptor "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/federal"
)

const (
	REGISTER_DEVICE_ENQUIRY_API_ID = "OB_DEV_REGISTER"
)

var (
	// status of the enquiry API response
	enquiryAPIStatusMap = map[string]*rpc.Status{
		"000": rpc.StatusOk(),
	}
)

// getRegisterDeviceStatus checks the status of device registration and returns corresponding device token
type getRegisterDeviceStatus struct {
	*federal.DefaultHeaderAdder
	*federalCryptor.DefaultPGPSecuredExchange

	req *auth.GetRegisterDeviceStatusRequest
	url string

	senderCode        string
	serviceAccessId   string
	serviceAccessCode string
	reqId             string
}

func (c getRegisterDeviceStatus) Marshal() ([]byte, error) {
	requestPayload := &vendorfedral.EnquiryStatusRequest{
		SenderCode:        c.senderCode,
		ServiceAccessId:   c.serviceAccessId,
		ServiceAccessCode: c.serviceAccessCode,
		RequestId:         c.reqId,
		DeviceId:          c.req.GetDeviceId(),
		ApiId:             REGISTER_DEVICE_ENQUIRY_API_ID,
		OriginalRequestId: c.req.GetOriginalReqId(),
		MobileNumber:      c.req.GetPhoneNumber().ToString(),
		UserProfileId:     c.req.GetUserProfileId(),
	}

	return protojson.Marshal(requestPayload)
}

func (c getRegisterDeviceStatus) URL() string {
	return c.url
}

func (c getRegisterDeviceStatus) HTTPMethod() string {
	return "POST"
}

func (c getRegisterDeviceStatus) GetResponse() vendorapi.Response {
	return c
}

func (c getRegisterDeviceStatus) Unmarshal(b []byte) (proto.Message, error) {
	errRes := func(rpcStatus *rpc.Status, debugMsg string, vendorStatus *commonvgpb.VendorStatus) (proto.Message, error) {
		rpcStatus.SetDebugMessage(debugMsg)
		return &auth.GetRegisterDeviceStatusResponse{
			Status:       rpcStatus,
			VendorStatus: vendorStatus,
		}, nil
	}

	// unmarshal enquiry API response
	fedRes := vendorfedral.EnquiryStatusDeviceRegistrationResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &fedRes)
	if err != nil {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("error in unmarshal res: %v", err), &commonvgpb.VendorStatus{})
	}

	vendorStatus := &commonvgpb.VendorStatus{
		Code:        fedRes.GetResponseCode(),
		Description: fedRes.GetResponseReason(),
	}
	// check enquiry API status
	apiStatus := mapResCodeToStatus(enquiryAPIStatusMap, fedRes.GetResponseCode())
	if !apiStatus.IsSuccess() {
		return errRes(apiStatus, "", vendorStatus)
	}

	// check original device registration request status
	details := fedRes.GetDetails()
	regDeviceStatus := mapResCodeToStatus(registerDeviceErrorCodeMapping, details.GetResponseCode())
	if !regDeviceStatus.IsSuccess() {
		return errRes(regDeviceStatus, "", vendorStatus)
	}

	// check device token
	if details.GetDeviceToken() == "" {
		return errRes(rpc.StatusInternal(), fmt.Sprintf("empty device token: %v", err), vendorStatus)
	}

	// success
	return &auth.GetRegisterDeviceStatusResponse{
		Status:       rpc.StatusOk(),
		DeviceToken:  details.GetDeviceToken(),
		VendorStatus: vendorStatus,
	}, nil
}
