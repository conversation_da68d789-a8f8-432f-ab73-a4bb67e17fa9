package job

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/json"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	cardPb "github.com/epifi/gamma/api/card"
	cpPb "github.com/epifi/gamma/api/card/provisioning"
	cardDao "github.com/epifi/gamma/card/dao"
)

var (
	defaultCardForm        = cardPb.CardForm_DIGITAL
	defaultRenewCardReason = "hot marked by bank for security reasons"
)

type DcRenewCardJob struct {
	cpClient cpPb.CardProvisioningClient
	cardDao  cardDao.CardDao
}

// DcRenewCardJobArgs eg - {"RenewRequests":[{"card_id":"card_1","actor_id":"actor_1"},{"card_id":"card_2","actor_id":"actor_2"}]}
type DcRenewCardJobArgs struct {
	RenewRequests []*RenewReq `json:"RenewRequests"`
}

type RenewReq struct {
	CardId   string `json:"CardId"`
	ActorId  string `json:"ActorId"`
	CardForm string `json:"CardForm"`
}

func (r *RenewReq) GetCardId() string {
	if r == nil {
		return ""
	}
	return r.CardId
}

func (r *RenewReq) GetActorId() string {
	if r == nil {
		return ""
	}
	return r.ActorId
}

func (r *RenewReq) GetCardForm() cardPb.CardForm {
	if r == nil {
		return cardPb.CardForm_CARD_FORM_UNSPECIFIED
	}
	return cardPb.CardForm(cardPb.CardForm_value[r.CardForm])
}

func NewDcRenewCardJob(cpClient cpPb.CardProvisioningClient, cardDao cardDao.CardDao) *DcRenewCardJob {
	return &DcRenewCardJob{
		cpClient: cpClient,
		cardDao:  cardDao,
	}
}

func (d *DcRenewCardJob) Run(ctx context.Context, argString string) error {

	// populate config to trigger RenewFlowV2
	ctx = epificontext.CtxWithAppPlatform(ctx, commontypes.Platform_ANDROID)
	ctx = epificontext.CtxWithAppVersionCode(ctx, "325")

	args := &DcRenewCardJobArgs{}
	err := json.Unmarshal([]byte(argString), args)
	if err != nil {
		logger.Error(ctx, "error in unmarshalling args for DcRenewCardJob", zap.Error(err))
		return errors.Wrap(err, "error in unmarshalling args for DcRenewCardJob")
	}

	for i, renewReq := range args.RenewRequests {
		switch {
		case renewReq.GetCardId() == "" && renewReq.GetActorId() == "":
			logger.Error(ctx, "invalid arguments passed, empty cardId and actorId", zap.Int(logger.SEQ_NUM, i),
				zap.String(logger.CARD_ID, renewReq.GetCardId()), zap.String(logger.ACTOR_ID_V2, renewReq.GetActorId()))
			continue
		case renewReq.GetActorId() != "":
			cards, err := d.cardDao.GetCardsForActor(ctx, renewReq.GetActorId(), []cardPb.CardState{
				cardPb.CardState_CREATED, cardPb.CardState_ACTIVATED, cardPb.CardState_SUSPENDED, cardPb.CardState_EXPIRED, cardPb.CardState_BLOCKED,
			}, nil, nil, nil, []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK}, cardPb.CardFieldMask_CARD_CREATED_AT, 0)
			if err != nil || len(cards) == 0 {
				logger.Error(ctx, "error in fetching card by actor",
					zap.Error(err),
					zap.String(logger.ACTOR_ID_V2, renewReq.GetActorId()))
				continue
			}
			renewReq.CardId = cards[0].GetId()
		case renewReq.GetCardId() != "":
			card, err := d.cardDao.GetByID(ctx, renewReq.GetCardId())
			if err != nil || card == nil {
				logger.Error(ctx, "error in fetching card by id",
					zap.Error(err),
					zap.String(logger.ACTOR_ID_V2, renewReq.GetCardId()))
				continue
			}
			renewReq.ActorId = card.GetActorId()

		default:
		}
		if renewReq.GetCardForm() == cardPb.CardForm_CARD_FORM_UNSPECIFIED {
			logger.Error(ctx, "invalid card form", zap.String(logger.CARD_ID, renewReq.GetCardId()), zap.String(logger.ACTOR_ID_V2, renewReq.GetActorId()))
			continue
		}

		res, err := d.cpClient.RenewCard(ctx, &cpPb.RenewCardRequest{
			CardId:              renewReq.GetCardId(),
			BlockCardReason:     defaultRenewCardReason,
			BlockCardProvenance: cardPb.Provenance_SHERLOCK,
			ActorId:             renewReq.GetActorId(),
			CardForm:            renewReq.GetCardForm(),
		})
		if te := epifigrpc.RPCError(res, err); te != nil {
			logger.Error(ctx, "error placing renew request", zap.Error(te), zap.String(logger.CARD_ID, renewReq.GetCardId()))
			continue
		}
		time.Sleep(200 * time.Millisecond)
	}
	return nil
}
