Application:
  Environment: "qa"
  Name: "watson_incident_state_update"

SherlockDbConfig:
  AppName: "cx"
  StatementTimeout: 5s
  Name: "sherlock"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Secrets:
  Ids:
    DbUsernamePassword: "qa/rds/postgres/sherlock"

Aws:
  Region: "ap-south-1"
