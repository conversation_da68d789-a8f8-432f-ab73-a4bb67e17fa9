package config

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

const (
	ClientEncryptionKey                  = "ClientEncryptionKey"
	ClientEncryptionInitialisationVector = "ClientEncryptionInitialisationVector"
)

var (
	_, b, _, _ = runtime.Caller(0)

	once   sync.Once
	config *Config
	err    error
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, "card_qr_code_data")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	keyToSecret, err := cfg.LoadSecretsAndPrepareDBConfig(conf.Secrets, conf.Application.Environment, conf.Aws.Region, conf.EpifiDb, conf.EpifiPgDb, conf.ActorDb)
	if err != nil {
		return nil, err
	}

	updateDefaultConfig(conf, keyToSecret)

	return conf, nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println(configPath)
	return configPath
}

// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) {
	readAndSetEnv(c)
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)
	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}
}

type Config struct {
	Application               *application
	EpifiDb                   *cfg.DB
	ActorDb                   *cfg.DB
	EpifiPgDb                 *cfg.DB
	Secrets                   *cfg.Secrets
	Aws                       *cfg.AWS
	UsePgdbConnForDebitCardDb bool
	DbConnectionAliases       *DbConnectionAlias
}

type application struct {
	Environment string
	Name        string
}

type DbConnectionAlias struct {
	DebitCardPgdbConnAlias string
	ActorPgdbConnAlias     string
}
