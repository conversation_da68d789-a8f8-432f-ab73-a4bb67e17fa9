package usecases

import (
	"fmt"
	"regexp"

	"github.com/epifi/be-common/pkg/syncmap"
	"github.com/epifi/gamma/scripts/risk_sms_filter/internal"
	"github.com/epifi/gamma/scripts/risk_sms_filter/internal/processor"
)

var uniqueProfiles = &syncmap.Map[string, struct{}]{}

func PrintStats() {
	count := 0
	uniqueProfiles.Range(func(key string, value struct{}) bool {
		count++
		return true
	})
	fmt.Printf("Count  of unique profiles: %d\n", count)
}

// UseCase defines a processing function for a specific use case
type UseCase struct {
	ProcessFunc func(input *internal.InputJSON) *internal.ProcessedJSON
}

// UseCases maps use case names to their processing logic
var UseCases = map[string]UseCase{
	"account": {
		ProcessFunc: func(input *internal.InputJSON) *internal.ProcessedJSON {
			var includeRegex = regexp.MustCompile(`(?i)(?:(?:account|a/c|acct(?:ount)?|current\s+a/c|current\s+account)(?:[\s\S]{0,80}?(?:open(?:ed)?|has\s+been\s+opened|activated|activating|is\s+active|welcome\s+to|welcomes|congratulations|Congratulations!))|(?:open(?:ed)?|activated|activating|is\s+active|welcome\s+to|welcomes|congratulations|Congratulations!)(?:[\s\S]{0,80}?(?:account|a/c|acct(?:ount)?|current\s+a/c|current\s+account)))`)
			var excludeRegex = regexp.MustCompile(`(?i)\b(?:otp|one\s+time\s+password|transaction(?:\s+(?:alert|successful|failed))?|debited|credited|apply\s+for|get\s+a|zero\s+balance|offer|cashback|emi|earning|Livemint|data\s+coupon|Fibernet|promotion|balance\s+enquiry|statement|password\s+reset|login|auto-debit|OMNICARD|purchase|closed|Rentrip|Smytten|DND|application|Fibe|Auto\s+debit|Premium|earning\s+upto|SpiceClub|NEFT|minutes|initial\s+deposit|plan(?:\s+has\s+been\s+activated)?|cloud\s+storage|times\s+prime|hotstar|mobile\s+plan|looking\s+for|credit\s+card|loan|RRN|resume|statutory|changed|keyword|ADHO|placed|pending|UPIBLOCK|disabled|employee|invalid|WhatsApp|request|requested|PSP|monthly|Reactivation|PPF|mutual\s+fund|freeze|demat|interest\s+rate|device|biometrics|card\s+replacement|fastag|under process|authentication|authenticated|incomplete|CKYC|stopped midway|PAN|consent|Clear Bal|few more steps|Continue|Open now|Deposit A/c|FedMobile/FedNet|Confirmation Code|mature|Flat|deactivated|interest|reactivated|iPhone|smart TV|refrigerator)\b`)

			return processor.ProcessMessages(input, func(msg internal.Message) bool {
				return !excludeRegex.MatchString(msg.Content) && includeRegex.MatchString(msg.Content)
			})
		},
	},
	"lea": {
		ProcessFunc: func(input *internal.InputJSON) *internal.ProcessedJSON {
			var leaRegex = regexp.MustCompile(`(?i)\b(?:account|a/c|acct|ac)\b[\s\S]{0,10}?\b(?:frozen|freeze(?:d|s)?|block(?:ed|s)?|hold(?:s|ing|ed)?|restriction|suspension|investigation|review|alert|complaint|directive|order|under investigation|under review|non-compliance|unlawful activities|compliance requirement|temporary hold|permanent hold|fraud alert|suspicious transaction|partial freeze|full freeze|transaction restriction|debit/credit restriction|lea)\b`)
			var excludeRegex = regexp.MustCompile(`(?i)(v-kyc|kyc|re-kyc|mPin|wrong account|overdue and account blocked|in payment|Repayment|login attempt|attempt|dues|loan account|login|video-kyc|invalid PIN|bill of|can now freeze|unpaid|will freeze|cashback|\border\b|credit|debit|inactive|avoid account suspension|off|removed|one-time mandate|maximum permissible tries|welcome kit| share your)`)
			return processor.ProcessMessages(input, func(msg internal.Message) bool {
				return !excludeRegex.MatchString(msg.Content) && leaRegex.MatchString(msg.Content)
			})
		},
	},
	"other": {
		ProcessFunc: func(input *internal.InputJSON) *internal.ProcessedJSON {

			var includeRegex = regexp.MustCompile(`(?i)(?:(?:account|a/c|acct(?:ount)?|current\s+a/c|current\s+account)(?:[\s\S]{0,80}?(?:open(?:ed)?|has\s+been\s+opened|activated|activating|is\s+active|welcome\s+to|welcomes|congratulations|Congratulations!))|(?:open(?:ed)?|activated|activating|is\s+active|welcome\s+to|welcomes|congratulations|Congratulations!)(?:[\s\S]{0,80}?(?:account|a/c|acct(?:ount)?|current\s+a/c|current\s+account)))`)
			var excludeRegex = regexp.MustCompile(`(?i)\b(?:otp|one\s+time\s+password|transaction(?:\s+(?:alert|successful|failed))?|debited|credited|apply\s+for|get\s+a|zero\s+balance|offer|cashback|emi|earning|Livemint|data\s+coupon|Fibernet|promotion|balance\s+enquiry|statement|password\s+reset|login|auto-debit|OMNICARD|purchase|closed|Rentrip|Smytten|DND|application|Fibe|Auto\s+debit|Premium|earning\s+upto|SpiceClub|NEFT|minutes|initial\s+deposit|plan(?:\s+has\s+been\s+activated)?|cloud\s+storage|times\s+prime|hotstar|mobile\s+plan|looking\s+for|credit\s+card|loan|RRN|resume|statutory|changed|keyword|ADHO|placed|pending|UPIBLOCK|disabled|employee|invalid|WhatsApp|request|requested|PSP|monthly|Reactivation|PPF|mutual\s+fund|freeze|demat|interest\s+rate|device|biometrics|card\s+replacement|fastag|under process|authentication|authenticated|incomplete|CKYC|stopped midway|PAN|consent|Clear Bal|few more steps|Continue)\b`)

			return processor.ProcessMessages(input, func(msg internal.Message) bool {
				return !excludeRegex.MatchString(msg.Content) && includeRegex.MatchString(msg.Content)
			})
		},
	},
	"cc-bill": {
		ProcessFunc: func(input *internal.InputJSON) *internal.ProcessedJSON {
			for _, msg := range input.Messages {
				uniqueProfiles.LoadOrStore(msg.ProfileNumber, struct{}{})
			}
			var includeRegex = regexp.MustCompile(`(?i)(txn|used|spent)\s+rs\.?\s*\d+(\.\d{1,2})?.*?hdfc.*?card.*?@[\w\d\.\-]+.*?by\s+upi\s+\d+.*?(on\s+\d{1,2}[\/\-]\d{1,2}(\/\d{2,4})?)?`)
			return processor.ProcessMessages(input, func(msg internal.Message) bool {
				return includeRegex.MatchString(msg.Content)
			})
		},
	},
}

// GetAvailableUseCases returns the list of valid use case names
func GetAvailableUseCases() []string {
	keys := make([]string, 0, len(UseCases))
	for k := range UseCases {
		keys = append(keys, k)
	}
	return keys
}
