Application:
  Environment: "staging"
  Name: "ift-recon"

AWS:
  Region: "ap-south-1"

EpifiDb:
  AppName: "ift-recon"
  StatementTimeout: 10s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true


Secrets:
  Ids:
    IFTReportsSlackBotOauthToken: "staging/ift/slack-bot-oauth-token"
