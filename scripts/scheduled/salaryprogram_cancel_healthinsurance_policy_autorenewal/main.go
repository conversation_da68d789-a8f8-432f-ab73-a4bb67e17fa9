package main

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	healthinsurancePb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	salaryDbModel "github.com/epifi/gamma/salaryprogram/dao/model"
	"github.com/epifi/gamma/salaryprogram/healthinsurance/dao/model"
	"github.com/epifi/gamma/scripts/scheduled/salaryprogram_cancel_healthinsurance_policy_autorenewal/config"
)

var (
	currentTime = time.Now()
)

// todo (utkarsh) : add alerting for job failure cases
// nolint: funlen
func main() {
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}

	ctx, cancelFunc := context.WithTimeout(context.Background(), 3*time.Hour)
	defer cancelFunc()

	// connect to salaryprogram db
	db, err := storageV2.NewPostgresDBWithConfig(conf.SalaryProgramDb, conf.Tracing.Enable)
	if err != nil {
		logger.Panic("failed to establish DB conn", zap.Error(err))
	}
	sqlDB, err := db.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = sqlDB.Close() }()

	salaryProgramSvcConn := epifigrpc.NewConnByService(cfg.SALARY_PROGRAM_SERVICE)
	defer epifigrpc.CloseConn(salaryProgramSvcConn)
	healthInsuranceClient := healthinsurancePb.NewHealthInsuranceClient(salaryProgramSvcConn)

	// taking a very early time as start time for pagination
	paginationFromTime := time.Date(2000, 1, 1, 1, 0, 0, 0, datetime.IST)
	const pageSize, paginationIterationsHardLimit, waitTimeBetweenPaginatedCalls = 100, 10000, 1 * time.Second

	// todo (utkarsh) : optimize this logic to fetch lesser data for checking policy auto-renewable cancellation cases.
	for i := 0; i < paginationIterationsHardLimit; i++ {
		autoRenewEnabledPolicies, err := getAutoRenewEnabledPoliciesPaginated(ctx, db, &paginationFromTime, pageSize)
		if err != nil {
			logger.Panic("error fetching auto-renew enabled policies", zap.Error(err))
		}
		if len(autoRenewEnabledPolicies) == 0 {
			logger.Info(ctx, "no auto renew enabled policy left")
			break
		}
		paginationFromTime = autoRenewEnabledPolicies[len(autoRenewEnabledPolicies)-1].CreatedAt.AsTime()

		var actorIds []string
		actorIdToPolicyDetailsMap := make(map[string][]*healthinsurancePb.HealthInsurancePolicyDetails, len(autoRenewEnabledPolicies))
		for _, policyDetails := range autoRenewEnabledPolicies {
			actorIds = append(actorIds, policyDetails.GetActorId())
			actorIdToPolicyDetailsMap[policyDetails.ActorId] = append(actorIdToPolicyDetailsMap[policyDetails.ActorId], policyDetails)
		}

		// get salaryprogram activation status of the users
		actorIdToIsSalaryActiveMap, err := getSalaryProgramActivationStatusOfUsers(ctx, db, actorIds)
		if err != nil {
			logger.Panic("error checking if given users are salaryprogram active", zap.Error(err))
		}

		// cancel policy auto-renewal for actors who have become salaryprogram inactive
		for idx, actorId := range actorIds {
			var shouldCancel bool
			isSalaryActive := actorIdToIsSalaryActiveMap[actorId]
			if !isSalaryActive {
				shouldCancel = true
			}
			autoRenewEnabledPoliciesForActor := actorIdToPolicyDetailsMap[actorId]
			for _, policyDetail := range autoRenewEnabledPoliciesForActor {
				// policy should be cancelled if it has crossed one year (policy not applicable after 12 months)
				policyThreshold := currentTime.AddDate(-1, 0, 0)
				if policyDetail.GetPolicyActiveFrom().AsTime().Before(policyThreshold) {
					shouldCancel = true
					logger.Info(ctx, "user is salary active for more than 1 year, cancelling policy auto-renewal", zap.String("actorId", actorId), zap.Time("activeFrom", policyDetail.GetPolicyActiveFrom().AsTime()))
				}
				if !shouldCancel {
					continue
				}
				cancelAutoRenewalRes, err := healthInsuranceClient.CancelPolicyAutoRenewal(ctx, &healthinsurancePb.CancelPolicyAutoRenewalRequest{PolicyDetailsId: policyDetail.GetId()})
				if rpcErr := epifigrpc.RPCError(cancelAutoRenewalRes, err); rpcErr != nil {
					logger.Error(ctx, "healthInsuranceClient.CancelPolicyAutoRenewal rpc call failed", zap.String("policyDetailsId", policyDetail.GetId()), zap.Error(rpcErr))
					continue
				}
				logger.Info(ctx, "successfully cancelled policy auto-renewal", zap.String("policyDetailsId", policyDetail.GetId()))
			}
			// adding wait time to rate limit calls to vendor api.
			if idx%25 == 0 {
				time.Sleep(2 * time.Second)
			}
		}
		// adding wait time to prevent overloading the db and services
		time.Sleep(waitTimeBetweenPaginatedCalls)
	}

	logger.Info(ctx, "cancel policy auto-renewal script completed successfully")
}

func getAutoRenewEnabledPoliciesPaginated(ctx context.Context, db *gorm.DB, createdAfter *time.Time, limit int) ([]*healthinsurancePb.HealthInsurancePolicyDetails, error) {
	db = gormctxv2.FromContextOrDefault(ctx, db)

	var policyDetailsDbModels []*model.HealthInsurancePolicyDetails
	if err := db.Where("policy_active_till is null and created_at > ?", createdAfter).Order("created_at asc").Limit(limit).Find(&policyDetailsDbModels).Error; err != nil {
		return nil, fmt.Errorf("error fetching auto-renew enabled policy details list, err : %w", err)
	}

	policyDetailsProtos := make([]*healthinsurancePb.HealthInsurancePolicyDetails, len(policyDetailsDbModels))
	for i, policyDetailsDbModel := range policyDetailsDbModels {
		policyDetailsProtos[i] = policyDetailsDbModel.GetProto()
	}

	return policyDetailsProtos, nil
}

func getSalaryProgramActivationStatusOfUsers(ctx context.Context, db *gorm.DB, actorIds []string) (map[string]bool, error) {
	db = gormctxv2.FromContextOrDefault(ctx, db)

	// fetch salaryprogram registration ids
	var registrations []*salaryDbModel.SalaryProgramRegistration
	if err := db.Where("actor_id in (?) and registration_flow_type = (?)", actorIds, salaryPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE).Select("id", "actor_id").Find(&registrations).Error; err != nil {
		return nil, fmt.Errorf("error fetching salaryprogram registrations for given actorIds, err : %w", err)
	}

	actorIdToRegIdMap := make(map[string]string)
	var regIds []string
	for _, reg := range registrations {
		actorIdToRegIdMap[reg.ActorId.GetValue()] = reg.Id
		regIds = append(regIds, reg.Id)
	}

	// check which registrations are currently salary program active.
	var activationHistories []*salaryDbModel.SalaryProgramActivationHistory
	// we are considering a salary activation to be active even if it is inactive in the last 10 days
	// eg. if the job runs on 27 May, and user's activation ended on 25 May, we consider the activation to be active
	// and skip cancelling the health insurance policy.
	gracePeriodTime := currentTime.AddDate(0, 0, -10)
	if err := db.Where("salary_program_registration_id in (?) and active_from <= ? and active_till > ?", regIds, currentTime, gracePeriodTime).
		Select("id", "salary_program_registration_id").Find(&activationHistories).Error; err != nil {
		return nil, fmt.Errorf("error fetching salaryprogram activation history for given registrationIds, err : %w", err)
	}
	regIdToIsSalaryActiveMap := make(map[string]bool)
	for _, actHistory := range activationHistories {
		regIdToIsSalaryActiveMap[actHistory.SalaryProgramRegistrationId.GetValue()] = true
	}

	actorIdToIsSalaryActiveMap := make(map[string]bool)
	for actorId, regId := range actorIdToRegIdMap {
		actorIdToIsSalaryActiveMap[actorId] = regIdToIsSalaryActiveMap[regId]
	}
	return actorIdToIsSalaryActiveMap, nil
}
