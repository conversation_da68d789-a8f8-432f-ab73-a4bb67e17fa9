package main

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"math"
	"strconv"
	"time"

	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"gorm.io/gorm"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/bankcust"
	commsPb "github.com/epifi/gamma/api/comms"
	depositPb "github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/frontend/deeplink"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/deposit/dao/model"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"
	"github.com/epifi/gamma/scripts/scheduled/deposit_accounts_min_kyc_comms/config"
)

type notifPart int

const (
	fromEmailId             = "<EMAIL>"
	fromEmailName           = "Fi Money"
	title         notifPart = 0
	body          notifPart = 1
	layoutFormat            = "2006-01-02"
)

var (
	minKycMaxSavingsBalance = &moneyPb.Money{
		CurrencyCode: "INR",
		Units:        100000,
		Nanos:        0,
	}
	// represents list of number of days before deposit maturity to send communication to user
	tMinusXNumberOfDays = []int{0, 6}
	// represents list of number of days after deposit maturity to send communication to user
	tPlusXNumberOfDays = []int{1, 7}

	// TODO(harish): make below config driven
	tPlusXNotification = map[int]map[notifPart]string{
		7: {title: "Your Smart Deposit matured a week ago!",
			body: "Complete your video KYC to remove min KYC limitations and receive the amount from your matured %v %v",
		},
		1: {title: "Your Smart Deposit matured yesterday!",
			body: "Complete your video KYC to remove min KYC limitations and receive the amount from your matured %v %v",
		},
	}
	tMinusXNotification = map[int]map[notifPart]string{
		6: {title: "Your Smart Deposit matures in 7 days!",
			body: "Complete your video KYC to remove min KYC limitations and receive the amount from your soon to mature %v %v",
		},
		0: {title: "Your Smart Deposit matures tomorrow!",
			body: "Complete your video KYC to remove min KYC limitations and receive the amount from your soon to mature %v %v",
		},
	}
)

// Communicate to min-kyc users if their deposit accounts:
// 1. Will mature in 7 days and will breach min-kyc limits.
// 2. Will mature in 1 day and will breach min-kyc limits.
// 3. Matured 7 days back and user is still min-kyc because of which credit blocked.
// 4. Matured 1 day back and user is still min-kyc because of which credit blocked.
// Communication methods: Email and push-notifications
// nolint: funlen
func main() {
	// Get environment
	envName, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %w", err))
	}
	// Setup logger
	logger.Init(envName)
	defer func() { _ = logger.Log.Sync() }()

	// Load deposit configuration
	depositConf, err := config.Load()
	if err != nil {
		panic(err)
	}

	// Connect to Epifi CRDB
	dbConn, err := storagev2.NewCRDBWithConfig(depositConf.EpifiDb, depositConf.Tracing.Enable)
	if err != nil {
		panic(err)
	}
	sqlDb, err := dbConn.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	storagev2.InitDefaultCRDBTransactionExecutor(dbConn)
	defer func() { _ = sqlDb.Close() }()

	ctx := context.TODO()
	db := gormctxv2.FromContextOrDefault(ctx, dbConn)

	bcConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	defer epifigrpc.CloseConn(bcConn)
	bcClient := bankcust.NewBankCustomerServiceClient(bcConn)

	// connect to actor service
	commsConn := epifigrpc.NewConnByService(cfg.COMMS_SERVICE)
	defer epifigrpc.CloseConn(commsConn)
	commsClient := commsPb.NewCommsClient(commsConn)

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	defer epifigrpc.CloseConn(savingsConn)
	savingsClient := savingsPb.NewSavingsClient(savingsConn)

	vkycConn := epifigrpc.NewConnByService(cfg.KYC_SERVICE)
	defer epifigrpc.CloseConn(vkycConn)
	vkycClient := vkycPb.NewVKYCClient(vkycConn)

	sendTPlusXCommunication(ctx, db, bcClient, savingsClient, commsClient, vkycClient)
	sendTMinusXCommunication(ctx, db, bcClient, savingsClient, commsClient, vkycClient)
}

// sends notification to deposit accounts that were matured X days earlier, but still in created state
func sendTPlusXCommunication(ctx context.Context, db *gorm.DB, bcClient bankcust.BankCustomerServiceClient,
	savingsClient savingsPb.SavingsClient, commsClient commsPb.CommsClient, vkycClient vkycPb.VKYCClient) {
	year, month, day := time.Now().Date()

	for _, dayNum := range tPlusXNumberOfDays {
		maturityDate := fetchMaturityDate(year, month, day, -1*(dayNum+1))
		depositAccounts, err := fetchDepositAccounts(db, maturityDate)
		if err != nil {
			logger.Error(ctx, "failed to fetch deposit accounts for maturity date",
				zap.Any("maturity date: ", maturityDate),
				zap.Error(err),
			)
			continue
		}
		logger.Info(ctx, fmt.Sprintf("Total deposit accounts maturing on %v: %d", maturityDate, len(depositAccounts)))
		for _, depositAccount := range depositAccounts {
			// For deposits with no renew option and for whom one or more min-kyc limits breach, send communication
			if depositAccount.RenewInfo == nil || !depositAccount.RenewInfo.IsAutoRenewable {
				tPlusXComms := getTPlusXCommsRequestMessages(depositAccount, dayNum)
				if err := checkVkycAndCommunicate(ctx, bcClient, savingsClient, commsClient, vkycClient,
					tPlusXComms, depositAccount); err != nil {
					logger.Panic("failed to check min-kyc limit breach and send communication", zap.Error(err))
				}
			}
		}

	}
}

// sends notification to deposit accounts maturing in X days from now
func sendTMinusXCommunication(ctx context.Context, db *gorm.DB, bcClient bankcust.BankCustomerServiceClient,
	savingsClient savingsPb.SavingsClient, commsClient commsPb.CommsClient, vkycClient vkycPb.VKYCClient) {
	year, month, day := time.Now().Date()

	for _, dayNum := range tMinusXNumberOfDays {
		maturityDate := fetchMaturityDate(year, month, day, dayNum)
		depositAccounts, err := fetchDepositAccounts(db, maturityDate)
		if err != nil {
			logger.Error(ctx, "failed to fetch deposit accounts for maturity date",
				zap.Any("maturity date: ", maturityDate),
				zap.Error(err),
			)
			continue
		}
		logger.Info(ctx, fmt.Sprintf("Total deposit accounts maturing on %v: %d", maturityDate, len(depositAccounts)))
		if len(depositAccounts) > 0 {
			for _, depositAccount := range depositAccounts {
				// For deposits with no renew option and for whom one or more min-kyc limits breach, send communication
				if depositAccount.RenewInfo == nil || !depositAccount.RenewInfo.IsAutoRenewable {
					tPlusXComms := getTMinusXCommsRequestMessages(depositAccount, dayNum)
					if err := checkVkycAndCommunicate(ctx, bcClient, savingsClient, commsClient, vkycClient,
						tPlusXComms, depositAccount); err != nil {
						logger.Error(ctx, "failed to check min-kyc limit breach and send communication", zap.Error(err))
					}
				}
			}
		}
	}
}

// fetches comms request messages to be sent for deposit accounts matured X days earlier
func getTPlusXCommsRequestMessages(depositAccount *model.DepositAccount, xDays int) []*commsPb.SendMessageRequest {
	tPlusXComs := make([]*commsPb.SendMessageRequest, 0)
	tPlusXEmailOption := &commsPb.EmailOption{
		Option: &commsPb.EmailOption_DepositMaturityMinKycTPlusXEmailOption{
			DepositMaturityMinKycTPlusXEmailOption: &commsPb.DepositMaturingMinKycTPlusXEmailOption{
				EmailType: commsPb.EmailType_DEPOSIT_MATURING_MIN_KYC_T_PLUS_X,
				Option: &commsPb.DepositMaturingMinKycTPlusXEmailOption_DepositMaturingMinKycTPlusXEmailOptionV1{
					DepositMaturingMinKycTPlusXEmailOptionV1: &commsPb.DepositMaturingMinKycTPlusXEmailOptionV1{
						DepositName:     depositAccount.Name,
						XDays:           getTPlusXDays(float64(xDays)),
						DepositType:     getDepositType(depositAccount.Type),
						TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
					},
				},
			},
		},
	}
	emailComms := getEmailRequest(tPlusXEmailOption)

	notifTitle := tPlusXNotification[xDays][title]
	notifBody := fmt.Sprintf(tPlusXNotification[xDays][body], depositAccount.Name, getDepositType(depositAccount.Type))

	pushNotif := getNotifRequest(notifTitle, notifBody)
	return append(tPlusXComs, emailComms, pushNotif)
}

// fetches comms request messages to be sent for deposit accounts maturing in X days
func getTMinusXCommsRequestMessages(depositAccount *model.DepositAccount, xDays int) []*commsPb.SendMessageRequest {
	tMinusXComms := make([]*commsPb.SendMessageRequest, 0)
	tMinusXEmailOption := &commsPb.EmailOption{
		Option: &commsPb.EmailOption_DepositMaturityMinKycTMinusXEmailOption{
			DepositMaturityMinKycTMinusXEmailOption: &commsPb.DepositMaturingMinKycTMinusXEmailOption{
				EmailType: commsPb.EmailType_DEPOSIT_MATURING_MIN_KYC_T_MINUS_X,
				Option: &commsPb.DepositMaturingMinKycTMinusXEmailOption_DepositMaturingMinKycTMinusXEmailOptionV1{
					DepositMaturingMinKycTMinusXEmailOptionV1: &commsPb.DepositMaturingMinKycTMinusXEmailOptionV1{
						DepositName:     depositAccount.Name,
						XDays:           getTMinusXDays(float64(xDays)),
						DepositType:     getDepositType(depositAccount.Type),
						MaturityDate:    depositAccount.MaturityDate.In(datetime.IST).Format(layoutFormat),
						TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
					},
				},
			},
		},
	}
	emailComms := getEmailRequest(tMinusXEmailOption)

	notifTitle := tMinusXNotification[xDays][title]
	notifBody := fmt.Sprintf(tMinusXNotification[xDays][body], depositAccount.Name, getDepositType(depositAccount.Type))

	pushNotif := getNotifRequest(notifTitle, notifBody)
	return append(tMinusXComms, emailComms, pushNotif)
}

func getEmailRequest(emailOptions *commsPb.EmailOption) *commsPb.SendMessageRequest {
	return &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_BEST_EFFORT,
		Medium: commsPb.Medium_EMAIL,
		Message: &commsPb.SendMessageRequest_Email{Email: &commsPb.EmailMessage{
			FromEmailId:   fromEmailId,
			FromEmailName: fromEmailName,
			EmailOption:   emailOptions,
		},
		},
	}
}
func getNotifRequest(notifTitle, notifBody string) *commsPb.SendMessageRequest {
	vkycDeeplink, _ := vkycPkg.BuildVKYCStatusDeeplink(&vkycPkg.StatusScreenOptions{
		EntryPoint:           vkycPb.EntryPoint_ENTRY_POINT_DEPOSIT_MIN_KYC_COMMS,
		EntryPointDeprecated: deeplink.EntryPoint_ENTRY_POINT_DEPOSIT_MIN_KYC_COMMS,
	})
	return &commsPb.SendMessageRequest{
		Type:   commsPb.QoS_BEST_EFFORT,
		Medium: commsPb.Medium_NOTIFICATION,
		Message: &commsPb.SendMessageRequest_Notification{Notification: &commsPb.NotificationMessage{
			Priority: commsPb.NotificationPriority_NORMAL,
			AndroidConfig: &commsPb.AndroidConfig{
				NotificationDelivery: commsPb.DeliveryQoS_IMMEDIATE,
			},
			Notification: &fcmPb.Notification{
				NotificationType: fcmPb.NotificationType_SYSTEM_TRAY,
				NotificationTemplates: &fcmPb.Notification_SystemTrayTemplate{SystemTrayTemplate: &fcmPb.SystemTrayTemplate{
					CommonTemplateFields: &fcmPb.CommonTemplateFields{
						Title:    notifTitle,
						Body:     notifBody,
						Deeplink: vkycDeeplink,
					},
				},
				}},
		},
		}}
}
func getTMinusXDays(xDays float64) string {
	xDays = math.Abs(xDays)
	if xDays >= 1 {
		return strconv.Itoa(int(xDays+1)) + " days"
	} else {
		return strconv.Itoa(int(xDays+1)) + " day"
	}
}

func getTPlusXDays(xDays float64) string {
	xDays = math.Abs(xDays)
	if xDays > 1 {
		return strconv.Itoa(int(xDays)) + " days"
	} else {
		return strconv.Itoa(int(xDays)) + " day"
	}
}

func getDepositType(depositType accounts.Type) string {
	switch depositType {
	case accounts.Type_SMART_DEPOSIT:
		return "Smart Deposit"
	case accounts.Type_FIXED_DEPOSIT:
		return "Fixed Deposit"
	default:
		return ""
	}
}

// fetchMaturityDate fetches maturity date X days from/before now
func fetchMaturityDate(year int, month time.Month, day, xDays int) time.Time {
	todayInUTC := time.Date(year, month, day, 18, 30, 0, 0, time.UTC)
	return todayInUTC.AddDate(0, 0, xDays)
}

func fetchDepositAccounts(db *gorm.DB, maturityDate time.Time) ([]*model.DepositAccount, error) {
	depositAccounts := make([]*model.DepositAccount, 0)

	if err := db.Where("state = ? AND maturity_date = ?", depositPb.DepositState_CREATED, maturityDate).
		Order("maturity_date ASC").
		Find(&depositAccounts).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to fetch deposit accounts with CREATED state which are past maturity date, %w", err)
	}
	return depositAccounts, nil
}

func checkVkycAndCommunicate(ctx context.Context,
	bcClient bankcust.BankCustomerServiceClient, savingsClient savingsPb.SavingsClient,
	commsClient commsPb.CommsClient, vkycClient vkycPb.VKYCClient,
	messageToSend []*commsPb.SendMessageRequest, depositAccount *model.DepositAccount) error {
	bankCustomer, err := getBankCustomerByActorId(ctx, depositAccount.ActorId, bcClient)
	if err != nil {
		return fmt.Errorf("failed to get bank customer by actorID, %w", err)
	}

	// account is min-kyc and VKYC status is not in review
	if bankCustomer.GetKycInfo().GetKycLevel() != kycPb.KYCLevel_FULL_KYC {
		// check if user's VKYC is not in review
		vkycPending, vkycErr := isVkycInProgress(ctx, depositAccount.ActorId, vkycClient)
		if vkycErr != nil {
			return vkycErr
		}
		if vkycPending {
			// if VKYC already started, do not send communication
			return nil
		}
		savingsAccount, err := getSavingsAccountByEntityId(ctx, bankCustomer.GetUserId(), savingsClient)
		if err != nil {
			return fmt.Errorf("failed to get savings account by entityID, %w", err)
		}
		depositAmount := depositAccount.PrincipalAmount
		// if maturity amount exists, deposit amount will be equal to that as it holds interest value too.
		if depositAccount.MaturityAmount != nil && !money.IsZero(depositAccount.MaturityAmount.GetPb()) {
			depositAmount = depositAccount.MaturityAmount
		}
		totalAmount, err := money.Sum(savingsAccount.GetBalanceFromPartner(), depositAmount.GetPb())
		if err != nil {
			return fmt.Errorf("failed to add savings balance and deposit balance, %w", err)
		}

		// If total amount is greater than minKycMaxSavingsBalance, actor will break min-kyc limit post maturity
		if money.Compare(totalAmount, minKycMaxSavingsBalance) == 1 {
			// send communication
			for _, message := range messageToSend {
				message.UserIdentifier = &commsPb.SendMessageRequest_UserId{UserId: bankCustomer.GetUserId()}
				response, err := commsClient.SendMessage(ctx, message)
				if err != nil || !response.GetStatus().IsSuccess() {
					return fmt.Errorf("error in sending communication for deposit maturity breaking min-kyc limits, %w", err)
				}
			}
		}
	}
	return nil
}

func getBankCustomerByActorId(ctx context.Context, actorId string, bcClient bankcust.BankCustomerServiceClient) (*bankcust.BankCustomer, error) {
	bcResp, errResp := bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
		logger.Error(ctx, "error while fetching bank customer", zap.Error(err))
		return nil, err
	}
	return bcResp.GetBankCustomer(), nil
}

func getSavingsAccountByEntityId(ctx context.Context, entityId string, savingsClient savingsPb.SavingsClient) (*savingsPb.Account, error) {
	res, err := savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_PrimaryUserId{
		PrimaryUserId: entityId,
	}})
	switch {
	case err != nil:
		return nil, fmt.Errorf("savingsClient.GetAccount() failed to get savings account by entityID %v: %w", entityId, err)
	default:
		return res.GetAccount(), nil
	}
}

func isVkycInProgress(ctx context.Context, actorId string, vkycClient vkycPb.VKYCClient) (bool, error) {
	vkycSummary, err := vkycClient.GetVKYCSummary(ctx, &vkycPb.GetVKYCSummaryRequest{ActorId: actorId})
	switch {
	case err != nil:
		return false, fmt.Errorf("vkycClient.GetVKYCSummary erred out, %w", err)
	case vkycSummary.GetStatus().IsSuccess():
		if vkycSummary.GetVkycRecord().GetVkycSummary().GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_IN_REVIEW ||
			vkycSummary.GetVkycRecord().GetVkycSummary().GetStatus() == vkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED {
			return true, nil
		} else {
			return false, nil
		}
	case vkycSummary.GetStatus().IsInternal():
		return false, fmt.Errorf("vkycClient.GetVKYCSummary gave internal status, %v", vkycSummary.GetStatus())
	default:
		return false, nil
	}
}
