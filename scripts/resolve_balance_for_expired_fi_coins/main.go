package main

import (
	"context"
	"flag"
	"time"

	"go.uber.org/ratelimit"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	accrualPb "github.com/epifi/gamma/api/accrual"
	rewardsPb "github.com/epifi/gamma/api/rewards"

	dm "github.com/epifi/gamma/accrual/dao/model"
	"github.com/epifi/gamma/accrual/model"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/scripts/resolve_balance_for_expired_fi_coins/config"
)

const (
	maxAllowedPaginatedCalls = 300
	maxPageSize              = 100
)

// nolint funlen
func main() {
	var (
		normalTrafficRate         int
		normalTrafficRateDuration time.Duration
		leanTrafficRate           int
		leanTrafficRateDuration   time.Duration
	)
	flag.IntVar(&normalTrafficRate, "normal-traffic-rate", 20, "normal traffic rate")
	flag.DurationVar(&normalTrafficRateDuration, "normal-traffic-rate-duration", time.Second, "normal traffic rate duration")
	flag.IntVar(&leanTrafficRate, "lean-traffic-rate", 100, "lean traffic rate")
	flag.DurationVar(&leanTrafficRateDuration, "lean-traffic-rate-duration", time.Second, "lean traffic rate duration")
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}
	ctx, cancelFunc := context.WithTimeout(context.Background(), 120*time.Minute)
	defer cancelFunc()

	// init rate limiter
	normalLimiter := ratelimit.New(normalTrafficRate, ratelimit.Per(normalTrafficRateDuration))
	leanLimiter := ratelimit.New(leanTrafficRate, ratelimit.Per(leanTrafficRateDuration))

	// connect to accrual db
	db, err := storagev2.NewPostgresDBWithConfig(conf.AccrualDb, false)
	if err != nil {
		logger.Panic("failed to establish DB conn", zap.Error(err))
	}
	sqlDB, err := db.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = sqlDB.Close() }()

	// init accrual service client
	accrualConn := epifigrpc.NewConnByService(cfg.ACCRUAL_SERVICE)
	defer epifigrpc.CloseConn(accrualConn)
	accrualClient := accrualPb.NewAccrualClient(accrualConn)

	// check if fi coins to fi points migration is active
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		logger.Panic(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error())
	}

	var (
		totalAccountsCount     int
		successResolutionCount int
		failedAccountsCount    int
	)
	for i := 0; i < maxAllowedPaginatedCalls; i++ {
		// fetch account ids for expired balance accounts (expired fi coins)
		accrualAccounts, err := getExpiredFiCoinsAccountIds(ctx, db)
		if err != nil {
			logger.Panic("error fetching account ids which are expired", zap.Int("totalAccountsCount", totalAccountsCount), zap.Int("successfulResolutionCount", successResolutionCount), zap.Int("failureResolutionCount", failedAccountsCount), zap.Error(err))
		}
		if len(accrualAccounts) == 0 {
			logger.Info(ctx, "no more accounts with expired fi coins")
			// if no accounts are present with expired fi coins condition, stop the process.
			break
		}
		totalAccountsCount += len(accrualAccounts)
		// call resolve balance RPC for these account ids
		for _, account := range accrualAccounts {
			limiter := getRateLimiter(leanLimiter, normalLimiter)
			limiter.Take()
			resp, err := accrualClient.ResolveBalance(ctx, &accrualPb.ResolveBalanceRequest{AccountId: account.AccountId})
			rpcErr := epifigrpc.RPCError(resp, err)
			// success & failure resolution count
			if rpcErr != nil {
				logger.Error(ctx, "error while resolving balance for expired fi coin account", zap.String("accountId", account.AccountId), zap.String("nextExpiryTime", account.BalanceNextExpiryTime.String()), zap.Error(rpcErr))
				failedAccountsCount++

				// if ctx deadline is exceeded, fail the script
				if resp.GetStatus().IsDeadlineExceeded() {
					logger.Panic("failing the script as the script context deadline exceeded, please re-run the scriptg", zap.Int("totalAccountsCount", totalAccountsCount), zap.Int("successfulResolutionCount", successResolutionCount), zap.Int("failureResolutionCount", failedAccountsCount))
				}
			} else {
				successResolutionCount++
			}
		}
	}

	if failedAccountsCount > 0 {
		logger.Panic("failed to resolve balance for some accounts", zap.Int("totalAccountsCount", totalAccountsCount), zap.Int("successfulResolutionCount", successResolutionCount), zap.Int("failureResolutionCount", failedAccountsCount))
	} else {
		logger.Info(ctx, "script completed successfully", zap.Int("totalAccountsCount", totalAccountsCount), zap.Int("successfulResolutionCount", successResolutionCount), zap.Int("failureResolutionCount", failedAccountsCount))
	}
}

func getRateLimiter(leanLimiter ratelimit.Limiter, normalLimiter ratelimit.Limiter) ratelimit.Limiter {
	var limiter ratelimit.Limiter
	now := time.Now().In(datetime.IST)
	if now.Hour() >= 2 && now.Hour() < 8 {
		limiter = leanLimiter
	} else {
		limiter = normalLimiter
	}
	return limiter
}

func getExpiredFiCoinsAccountIds(ctx context.Context, db *gorm.DB) ([]*model.Account, error) {
	currTime := time.Now()
	db = db.Model(&dm.AccrualAccount{})
	// add account_type as AccountType_ACCOUNT_TYPE_FICOINS
	db = db.Where("account_type = ?", accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestampPb.Now()))
	// add expiry time as less than current time
	db = db.Where("next_expiry_time < ?", currTime)
	// available balance must be greater than zero
	db = db.Where("available_balance > ?", 0)
	// limit max accounts to get in one query to 100
	db = db.Limit(maxPageSize)
	var accrualAccounts []*dm.AccrualAccount
	res := db.Select([]string{"id", "next_expiry_time"}).Find(&accrualAccounts)
	if res.Error != nil {
		logger.Error(ctx, "failed to get accounts for expired balance accounts", zap.Error(res.Error))
		return nil, res.Error
	}
	var accounts []*model.Account
	for _, account := range accrualAccounts {
		accounts = append(accounts, account.ConvertToServiceModel())
	}
	return accounts, nil
}
