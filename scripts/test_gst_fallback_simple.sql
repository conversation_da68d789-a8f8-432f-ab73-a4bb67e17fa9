-- Simple GST Fallback Test Script
-- Replace 'ACTOR_ID_HERE' with your actual actor ID

-- Test the GST fallback logic for a specific actor
WITH actor_id AS (
    SELECT 'ACTOR_ID_HERE' as id  -- Replace this with your actor ID
),
bank_customer_check AS (
    SELECT 
        bc.actor_id,
        bc.vendor_customer_id,
        (bc.vendor_metadata->'federal_metadata'->>'sol_id') as sol_id,
        CASE 
            WHEN (bc.vendor_metadata->'federal_metadata'->>'sol_id') IS NULL 
                 OR (bc.vendor_metadata->'federal_metadata'->>'sol_id') = ''
            THEN true
            ELSE false
        END as needs_fallback
    FROM bank_customers bc, actor_id a
    WHERE bc.actor_id = a.id 
      AND bc.vendor = 'FEDERAL_BANK'
),
savings_account_check AS (
    SELECT 
        sa.actor_id,
        sa.account_no,
        sa.ifsc_code,
        CASE 
            WHEN sa.ifsc_code IS NOT NULL AND LENGTH(sa.ifsc_code) >= 4
            THEN RIGHT(sa.ifsc_code, 4)
            ELSE NULL
        END as ifsc_last_4_chars
    FROM savings_accounts sa, actor_id a
    WHERE sa.actor_id = a.id 
      AND sa.partner_bank = 'FEDERAL_BANK'
)
SELECT 
    'GST Fallback Test Result' as test_type,
    COALESCE(bc.actor_id, sa.actor_id) as actor_id,
    bc.sol_id as current_sol_id,
    bc.needs_fallback,
    sa.ifsc_code,
    sa.ifsc_last_4_chars as fallback_sol_id,
    CASE 
        WHEN NOT bc.needs_fallback THEN bc.sol_id
        WHEN bc.needs_fallback AND sa.ifsc_last_4_chars IS NOT NULL THEN sa.ifsc_last_4_chars
        ELSE 'ERROR: No valid SOL ID'
    END as final_user_sol_id,
    CASE 
        WHEN NOT bc.needs_fallback THEN 'Normal flow - using existing SOL ID'
        WHEN bc.needs_fallback AND sa.ifsc_last_4_chars IS NOT NULL THEN 'Fallback flow - using IFSC last 4 chars'
        WHEN bc.needs_fallback AND sa.ifsc_code IS NULL THEN 'ERROR: No savings account found'
        WHEN bc.needs_fallback AND LENGTH(sa.ifsc_code) < 4 THEN 'ERROR: IFSC code too short'
        ELSE 'ERROR: Unknown issue'
    END as flow_description
FROM bank_customer_check bc
FULL OUTER JOIN savings_account_check sa ON bc.actor_id = sa.actor_id;
