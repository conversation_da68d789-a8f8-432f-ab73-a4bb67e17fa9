# User SOL ID Fallback Testing Script

This script tests the fallback logic for `userSolId` in GST reporting when the SOL ID is not available in bank customer metadata.

## Purpose

The script validates the same fallback logic implemented in the GST reporting code:
1. Attempts to get `userSolId` from bank customer metadata
2. If empty, falls back to calling `GetSavingsAccountEssentials` RPC
3. Extracts IFSC code from the savings account
4. Uses the last 4 characters of the IFSC code as `userSolId`

## Usage

```bash
# Test with a specific actor ID
go run scripts/wealth_dragon/*.go -JobName=TEST_USER_SOL_ID_FALLBACK -Args1=<ACTOR_ID>
```

### Parameters

- **JobName**: `TEST_USER_SOL_ID_FALLBACK`
- **Args1**: Actor ID (required) - The actor ID to test the fallback logic for

### Example

```bash
go run scripts/wealth_dragon/*.go -JobName=TEST_USER_SOL_ID_FALLBACK -Args1=AC230206pfc2PlEFSayZjijTLKizOA==
```

## Output Scenarios

### Scenario 1: SOL ID Available (No Fallback Needed)
```
✅ SUCCESS: userSolId found in bank customer metadata
   Actor ID: AC230206pfc2PlEFSayZjijTLKizOA==
   userSolId: 5555
   Status: NOT using fallback
```

### Scenario 2: SOL ID Empty (Fallback Successful)
```
⚠️  WARNING: userSolId is empty, using fallback logic
✅ SUCCESS: Fallback logic worked
   Actor ID: AC230206pfc2PlEFSayZjijTLKizOA==
   IFSC Code: FDRL0005555
   Extracted userSolId: 5555
   Status: USING fallback
```

### Scenario 3: Fallback Failed
```
⚠️  WARNING: userSolId is empty, using fallback logic
❌ FAILED: Could not get savings account essentials
   Actor ID: AC230206pfc2PlEFSayZjijTLKizOA==
   Error: rpc error: code = NotFound desc = record not found
```

## What the Script Tests

1. **Bank Customer Lookup**: Verifies if the actor has bank customer metadata with SOL ID
2. **Savings Account Lookup**: Tests the fallback RPC call to get savings account essentials
3. **IFSC Code Extraction**: Validates IFSC code is present and has sufficient length
4. **SOL ID Derivation**: Extracts last 4 characters from IFSC code

## Error Handling

The script handles various error scenarios:
- Missing actor ID parameter
- Bank customer not found
- Savings account not found
- Empty IFSC code
- IFSC code too short (< 4 characters)

## Logging

The script provides both console output for immediate feedback and structured logging for debugging purposes.

## Environment Support

- **QA**: Supported
- **Staging**: Supported  
- **Production**: Supported (with caution)

## Related Code

This script tests the same logic implemented in:
- `pay/internationalfundtransfer/get_gst_reporting_data_for_inward_txn.go`
- Method: `getGstDetails()`
