package main

import (
	"context"

	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	llmPb "github.com/epifi/gamma/api/llm"
	insightsgconf "github.com/epifi/gamma/insights/config/genconf"
	magicimport "github.com/epifi/gamma/insights/networth/magic_import"
	llmPkg "github.com/epifi/gamma/pkg/llm"
)

type jobMagicImportCacheSystemContext struct {
	llmHandler llmPkg.LLMHandler
	gconf      *insightsgconf.Config
}

func (j *jobMagicImportCacheSystemContext) PerformJob(ctx context.Context, req *JobRequest) error {
	modelName := j.getModelForMagicImport(ctx)
	logger.Info(ctx, "caching magic import system context", zap.String("model_name", modelName.String()))
	res, err := j.llmHandler.CacheSystemContext(ctx, &llmPb.CacheSystemContextRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_GOOGLE,
		},
		SystemContext: magicimport.GetMagicImportContext(),
		UseCase:       llmPb.UseCase_USE_CASE_NETWORTH_MAGIC_IMPORT,
		Model:         modelName,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "error in caching magic import system context", zap.Error(rpcErr))
		return rpcErr
	}
	logger.Info(ctx, "caching magic import system context", zap.Any("result", res))
	return nil
}

func (j *jobMagicImportCacheSystemContext) getModelForMagicImport(ctx context.Context) llmPb.Model {
	model := llmPb.Model_MODEL_GEMINI_V2_5_FLASH
	modelValue, ok := llmPb.Model_value[j.gconf.MagicImportConfig().ModelName()]
	if !ok {
		logger.Info(ctx, "model name not found in llmPb.Model_value", zap.String(logger.REQUEST_TYPE, j.gconf.MagicImportConfig().ModelName()))
		return model
	}
	return llmPb.Model(modelValue)
}
