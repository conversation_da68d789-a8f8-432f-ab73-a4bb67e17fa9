package main

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"go.uber.org/zap"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	mfPb "github.com/epifi/gamma/api/investment/mutualfund"
	mfVgPb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund"
	"github.com/epifi/gamma/investment/mutualfund/dao/impl"
)

var (
	obsoleteFundTypeMapping = map[mfVgPb.ObsoleteFundType]mfPb.FundInvestmentStatus{
		mfVgPb.ObsoleteFundType_LIQUIDATED: mfPb.FundInvestmentStatus_UNAVAILABLE_FOR_INVESTMENT_LIQUIDATED,
		mfVgPb.ObsoleteFundType_MERGED:     mfPb.FundInvestmentStatus_UNAVAILABLE_FOR_INVESTMENT_MERGED,
	}
)

type jobMfObsoleteFundsBackfill struct {
	mfVGClient mfVgPb.MutualFundClient
	mfDao      *impl.MutualFundCrdb
}

// nolint:funlen
func (j *jobMfObsoleteFundsBackfill) PerformJob(ctx context.Context, req *JobRequest) error {
	var errs []error
	for obsoleteFundType, mfInvestmentStatus := range obsoleteFundTypeMapping {
		obsoleteFundsRes, obsoleteFundsErr := j.mfVGClient.GetObsoleteFunds(ctx, &mfVgPb.GetObsoleteFundsRequest{
			Header:           &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_MORNINGSTAR},
			ObsoleteFundType: obsoleteFundType,
		})
		if rpcErr := epifigrpc.RPCError(obsoleteFundsRes, obsoleteFundsErr); rpcErr != nil {
			logger.Error(ctx, "error received while fetching obsolete funds", zap.Any("obsoleteFundType", obsoleteFundType.String()), zap.Error(rpcErr))
			return fmt.Errorf("error fetching obsolete funds: %w", rpcErr)
		}
		obsoleteFunds := obsoleteFundsRes.GetObsoleteFunds()
		obsoleteUpdateErr := j.updateObsoleteFunds(ctx, obsoleteFunds, mfInvestmentStatus)
		if obsoleteUpdateErr != nil {
			logger.Error(ctx, "error in updateObsoleteFunds", zap.Any("obsoleteFundType", obsoleteFundType.String()), zap.Error(obsoleteUpdateErr))
			errs = append(errs, obsoleteUpdateErr)
		}
	}
	if len(errs) > 0 {
		return fmt.Errorf("error when updating obsolete funds %v", errs)
	}
	return nil
}

func (j *jobMfObsoleteFundsBackfill) updateObsoleteFunds(ctx context.Context, obsoleteFunds []*mfVgPb.ObsoleteFund, fundInvestmentStatus mfPb.FundInvestmentStatus) error {

	var errorObsoleteFunds []*mfVgPb.ObsoleteFund
	var unprocessedObsoleteFunds []*mfVgPb.ObsoleteFund

	var mfs []*mfPb.MutualFund
	for _, obsoleteFund := range obsoleteFunds {
		if obsoleteFund.GetIsin() != "" && obsoleteFund.GetRtaCode() != "" && obsoleteFund.GetAmfiCode() != "" {
			obsoleteDate, parsErr := datetime.ParseStringTimeStampProto("2006.01.02", strings.ReplaceAll(obsoleteFund.GetObsoleteDate(), "-", "."))
			if parsErr != nil {
				errorObsoleteFunds = append(errorObsoleteFunds, obsoleteFund)
				logger.Error(ctx, "error in ParseStringTimeStampProto obsolete date", zap.Error(parsErr), zap.String(logger.DATE, obsoleteFund.GetObsoleteDate()))
				continue
			}
			mfObj := prepareMFObjFromObsoleteFund(obsoleteFund.GetIsin(), fundInvestmentStatus, obsoleteDate)
			mfs = append(mfs, mfObj)

		} else {
			unprocessedObsoleteFunds = append(unprocessedObsoleteFunds, obsoleteFund)
		}

	}
	// commenting these as these are mostly noise, can uncomment and test in non prod if needed
	// logger.Error(ctx, "error obsolete funds", zap.Any(logger.PAYLOAD, errorObsoleteFunds), zap.Any(logger.LENGTH, len(errorObsoleteFunds)))
	// logger.Info(ctx, "unprocessed funds", zap.Any(logger.PAYLOAD, unprocessedObsoleteFunds), zap.Any(logger.LENGTH, len(errorObsoleteFunds)))
	updateMasks := []mfPb.MutualFundFieldMask{mfPb.MutualFundFieldMask_OBSOLETE_DATE, mfPb.MutualFundFieldMask_FUND_INVESTMENT_STATUS}

	logger.Info(ctx, "processed funds", zap.Any(logger.LENGTH, len(mfs)))
	for _, mf := range mfs {
		mfResp, err := j.mfDao.GetByISIN(ctx, mf.GetIsinNumber())
		// nolint: gocritic
		if err == nil {
			mfResp.ObsoleteDate = mf.GetObsoleteDate()
			mfResp.FundInvestmentStatus = mf.GetFundInvestmentStatus()
			if _, updateErr := j.mfDao.UpdateByISIN(ctx, mfResp, updateMasks); updateErr != nil {
				logger.Error(ctx, fmt.Sprintf("error in mf Update: %v", updateErr), zap.Any("mfResp", mfResp))
			} else {
				logger.Info(ctx, "updated funds", zap.String("isin", mf.GetIsinNumber()))
			}
		} else if errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "isin not found", zap.String("isin", mf.GetIsinNumber()))
		} else {
			logger.Error(ctx, "error in GetByISIN", zap.Error(err))
		}
	}
	return nil
}

func prepareMFObjFromObsoleteFund(isin string, fundInvestmentStatus mfPb.FundInvestmentStatus, obsoleteDate *timestamp.Timestamp) *mfPb.MutualFund {
	return &mfPb.MutualFund{
		IsinNumber:           isin,
		FundInvestmentStatus: fundInvestmentStatus,
		ObsoleteDate:         obsoleteDate,
	}
}
