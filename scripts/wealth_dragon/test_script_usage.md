# Testing the User SOL ID Fallback Script

## Quick Test Commands

### 1. Build the script
```bash
cd /Users/<USER>/go/src/github.com/epifi/gamma
go build -o wealth_dragon_test scripts/wealth_dragon/*.go
```

### 2. Run the test with an actor ID
```bash
./wealth_dragon_test -JobName=TEST_USER_SOL_ID_FALLBACK -Args1=<ACTOR_ID>
```

### 3. Example with a real actor ID
```bash
./wealth_dragon_test -JobName=TEST_USER_SOL_ID_FALLBACK -Args1=AC230206pfc2PlEFSayZjijTLKizOA==
```

## Expected Output Formats

### Case 1: SOL ID exists (no fallback needed)
```
JOB: 'TEST_USER_SOL_ID_FALLBACK'
✅ SUCCESS: userSolId found in bank customer metadata
   Actor ID: AC230206pfc2PlEFSayZjijTLKizOA==
   userSolId: 5555
   Status: NOT using fallback
```

### Case 2: SOL ID empty (fallback works)
```
JOB: 'TEST_USER_SOL_ID_FALLBACK'
⚠️  WARNING: userSolId is empty, using fallback logic
✅ SUCCESS: Fallback logic worked
   Actor ID: AC230206pfc2PlEFSayZjijTLKizOA==
   IFSC Code: FDRL0005555
   Extracted userSolId: 5555
   Status: USING fallback
```

### Case 3: Fallback fails
```
JOB: 'TEST_USER_SOL_ID_FALLBACK'
⚠️  WARNING: userSolId is empty, using fallback logic
❌ FAILED: Could not get savings account essentials
   Actor ID: AC230206pfc2PlEFSayZjijTLKizOA==
   Error: rpc error: code = NotFound desc = record not found
```

## Troubleshooting

### If build fails:
1. Ensure you're in the correct directory
2. Check Go modules are properly initialized
3. Verify all dependencies are available

### If script fails to run:
1. Check if the actor ID exists in the system
2. Verify network connectivity to required services
3. Check service configurations and credentials

## Integration with CI/CD

This script can be integrated into testing pipelines to validate the fallback logic works correctly across different environments.
