package main

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	savingsPb "github.com/epifi/gamma/api/savings"
)

type jobTestUserSolIdFallback struct {
	bankCustClient bankCustomerPb.BankCustomerServiceClient
	savingsClient  savingsPb.SavingsClient
}

// PerformJob tests the userSolId fallback logic for a given actor ID
// Args1: Actor ID (required)
func (j *jobTestUserSolIdFallback) PerformJob(ctx context.Context, req *JobRequest) error {
	if req.Args1 == "" {
		return fmt.Errorf("actor ID is required as Args1")
	}

	actorId := req.Args1
	logger.Info(ctx, "Testing userSolId fallback logic", zap.String("actor_id", actorId))

	// Step 1: Try to get userSolId from bank customer metadata
	bankCustResp, err := j.bankCustClient.GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
	})
	if err != nil {
		logger.Error(ctx, "failed to get bank customer", zap.String("actor_id", actorId), zap.Error(err))
		return fmt.Errorf("failed to get bank customer: %w", err)
	}

	userSolId := bankCustResp.GetBankCustomer().GetVendorMetadata().GetFederalMetadata().GetSolId()
	
	if len(userSolId) > 0 {
		fmt.Printf("✅ SUCCESS: userSolId found in bank customer metadata\n")
		fmt.Printf("   Actor ID: %s\n", actorId)
		fmt.Printf("   userSolId: %s\n", userSolId)
		fmt.Printf("   Status: NOT using fallback\n")
		logger.Info(ctx, "userSolId found in bank customer metadata", 
			zap.String("actor_id", actorId), 
			zap.String("user_sol_id", userSolId))
		return nil
	}

	// Step 2: Fallback - Get IFSC code from GetSavingsAccountEssentials RPC
	fmt.Printf("⚠️  WARNING: userSolId is empty, using fallback logic\n")
	logger.Info(ctx, "userSolId is empty, attempting fallback", zap.String("actor_id", actorId))

	savingsResp, err := j.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier{
			ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	if err := epifigrpc.RPCError(savingsResp, err); err != nil {
		fmt.Printf("❌ FAILED: Could not get savings account essentials\n")
		fmt.Printf("   Actor ID: %s\n", actorId)
		fmt.Printf("   Error: %v\n", err)
		logger.Error(ctx, "failed to get savings account essentials for actor", 
			zap.String("actor_id", actorId), zap.Error(err))
		return fmt.Errorf("failed to get savings account essentials: %w", err)
	}

	ifscCode := savingsResp.GetAccount().GetIfscCode()
	if len(ifscCode) == 0 {
		fmt.Printf("❌ FAILED: IFSC code is empty in savings account essentials\n")
		fmt.Printf("   Actor ID: %s\n", actorId)
		logger.Error(ctx, "IFSC code is empty", zap.String("actor_id", actorId))
		return fmt.Errorf("got empty IFSC code from savings account essentials")
	}

	// Step 3: Extract last 4 characters from IFSC code
	if len(ifscCode) < 4 {
		fmt.Printf("❌ FAILED: IFSC code is too short to extract last 4 characters\n")
		fmt.Printf("   Actor ID: %s\n", actorId)
		fmt.Printf("   IFSC Code: %s\n", ifscCode)
		logger.Error(ctx, "IFSC code is too short", 
			zap.String("actor_id", actorId), 
			zap.String("ifsc_code", ifscCode))
		return fmt.Errorf("IFSC code is too short to extract last 4 characters")
	}

	fallbackUserSolId := ifscCode[len(ifscCode)-4:]

	fmt.Printf("✅ SUCCESS: Fallback logic worked\n")
	fmt.Printf("   Actor ID: %s\n", actorId)
	fmt.Printf("   IFSC Code: %s\n", ifscCode)
	fmt.Printf("   Extracted userSolId: %s\n", fallbackUserSolId)
	fmt.Printf("   Status: USING fallback\n")

	logger.Info(ctx, "fallback logic successful", 
		zap.String("actor_id", actorId), 
		zap.String("ifsc_code", ifscCode), 
		zap.String("fallback_user_sol_id", fallbackUserSolId))

	return nil
}
