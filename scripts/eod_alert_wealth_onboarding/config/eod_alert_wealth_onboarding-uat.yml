Application:
  Environment: "uat"

Aws:
  Region: "ap-south-1"

EpifiDb:
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "uat/cockroach/ca.crt"
  SSLClientCert: "uat/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "uat/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

# checks only for those actors whose created_at time gap with current time is more than or equal than to DownloadThresholdDays and UploadThresholdDays
DownloadThresholdDays: 7
UploadThresholdDays: 7
