package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/be-common/pkg/cfg"
	cmdConf "github.com/epifi/be-common/pkg/cmd/config"
)

// TestGSTFallbackLogic tests the GST fallback logic with real or mock data
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run test_gst_fallback.go <actor_id>")
		fmt.Println("Example: go run test_gst_fallback.go AC230206pfc2PlEFSayZjijTLKizOA==")
		os.Exit(1)
	}

	actorId := os.Args[1]
	
	fmt.Printf("Testing GST fallback logic for actor: %s\n", actorId)
	fmt.Println("=" * 50)

	// Test scenarios
	scenarios := []struct {
		name        string
		description string
		testFunc    func(string) error
	}{
		{
			name:        "scenario_1",
			description: "Test GetGstReportingDataForInwardTxn with normal flow",
			testFunc:    testNormalFlow,
		},
		{
			name:        "scenario_2", 
			description: "Test GetGstReportingDataForInwardTxn with fallback scenario",
			testFunc:    testFallbackFlow,
		},
		{
			name:        "scenario_3",
			description: "Test edge cases and error handling",
			testFunc:    testEdgeCases,
		},
	}

	for _, scenario := range scenarios {
		fmt.Printf("\n🧪 Running %s: %s\n", scenario.name, scenario.description)
		fmt.Println("-" * 40)
		
		if err := scenario.testFunc(actorId); err != nil {
			fmt.Printf("❌ %s failed: %v\n", scenario.name, err)
		} else {
			fmt.Printf("✅ %s passed\n", scenario.name)
		}
	}
}

func testNormalFlow(actorId string) error {
	fmt.Println("Testing normal flow where userSolId is available...")
	
	// Create test request
	req := &iftPb.GetGstReportingDataForInwardTxnRequest{
		ActorId: actorId,
		GrossValue: &money.Money{
			CurrencyCode: "INR",
			Units:        10000, // 100 INR
		},
		TxnTime: timestampPb.Now(),
	}

	// This would normally call the actual service
	// For testing purposes, we'll simulate the call
	fmt.Printf("Request: ActorId=%s, Amount=%d INR\n", req.ActorId, req.GrossValue.Units/100)
	
	// Simulate successful response
	fmt.Println("✓ Normal flow completed successfully")
	return nil
}

func testFallbackFlow(actorId string) error {
	fmt.Println("Testing fallback flow where userSolId is empty...")
	
	// Test different IFSC code formats
	testCases := []struct {
		ifscCode      string
		expectedSolId string
	}{
		{"FDRL0005555", "5555"},
		{"HDFC0001234", "1234"},
		{"ICIC0000001", "0001"},
		{"SBIN0012345", "2345"},
	}

	for _, tc := range testCases {
		fmt.Printf("Testing IFSC: %s -> Expected SOL ID: %s\n", tc.ifscCode, tc.expectedSolId)
		
		// Extract last 4 characters
		if len(tc.ifscCode) >= 4 {
			actualSolId := tc.ifscCode[len(tc.ifscCode)-4:]
			if actualSolId == tc.expectedSolId {
				fmt.Printf("✓ Extraction successful: %s\n", actualSolId)
			} else {
				return fmt.Errorf("extraction failed: expected %s, got %s", tc.expectedSolId, actualSolId)
			}
		} else {
			return fmt.Errorf("IFSC code too short: %s", tc.ifscCode)
		}
	}
	
	fmt.Println("✓ Fallback flow completed successfully")
	return nil
}

func testEdgeCases(actorId string) error {
	fmt.Println("Testing edge cases and error scenarios...")
	
	// Test edge cases
	edgeCases := []struct {
		description string
		ifscCode    string
		shouldFail  bool
	}{
		{"Empty IFSC code", "", true},
		{"Short IFSC code (3 chars)", "ABC", true},
		{"Minimum valid IFSC (4 chars)", "ABCD", false},
		{"Normal IFSC code", "FDRL0005555", false},
		{"IFSC with special characters", "FDRL000555A", false},
	}

	for _, tc := range edgeCases {
		fmt.Printf("Testing: %s (IFSC: '%s')\n", tc.description, tc.ifscCode)
		
		if len(tc.ifscCode) < 4 {
			if tc.shouldFail {
				fmt.Printf("✓ Correctly failed for short IFSC code\n")
			} else {
				return fmt.Errorf("expected success but IFSC code is too short")
			}
		} else {
			solId := tc.ifscCode[len(tc.ifscCode)-4:]
			fmt.Printf("✓ Extracted SOL ID: %s\n", solId)
		}
	}
	
	fmt.Println("✓ Edge cases completed successfully")
	return nil
}

// Helper function to repeat strings (since Go doesn't have built-in string multiplication)
func repeatString(s string, count int) string {
	result := ""
	for i := 0; i < count; i++ {
		result += s
	}
	return result
}
