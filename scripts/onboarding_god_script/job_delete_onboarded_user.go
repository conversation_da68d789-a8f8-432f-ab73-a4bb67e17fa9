package main

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	pkgErr "github.com/pkg/errors"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	employmentPb "github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/user"
	authDao "github.com/epifi/gamma/auth/dao"
	bankCustDao "github.com/epifi/gamma/bankcust/dao"
	onbDao "github.com/epifi/gamma/user/onboarding/dao"
)

type DeleteOnboardedUser struct {
	authClient         auth.AuthClient
	userClient         user.UsersClient
	actorClient        actor.ActorClient
	bankCustomerClient bankCustomerPb.BankCustomerServiceClient
	employmentClient   employmentPb.EmploymentClient
	bcDao              bankCustDao.BankCustomerDao
	onbDao             onbDao.OnboardingDao
	devRegDao          authDao.DeviceRegistrationDao
}

// List of deleted users
// actorId = "AC210426EzpxCjinRQmKUCaPFetuWw==", userId  = "34536a0e-8791-497c-b024-929e614f5434"
// actorId = "AC230126tWUdNX5eRkiz8RZqm6BJMQ==", userId  = "af6b5302-f617-471b-8b6a-1b290b0ace8d", reason: user is stuck due to privaterelay.appleid
// actorId = "AClroOe5rGQO6t4kX1FbBHHQ230403==", userId  = "a9128e3a-2aca-4ea0-859a-32bc558fe6d0", reason: internal user is stuck due to privaterelay.appleid
// actorId = "AC210614R6TMh4emSVum2s85nzUaFA==", userId  = "862efad6-7214-4305-93c7-dd77deef52a2", reason: POC for salary program, requested by Ayushi Lamba
// actorId = "AC220317c2U+lK+mTUCRnIOuQeXp5w==", userId  = "efe54587-1d52-4db2-8dd7-3cb529fefb12", reason: Sujith's fnf min kyc expiry
// actorId = "AC220325ylWOKdKnS+2RvSZSVB8X+A==", userId  = "66457e20-6269-4497-bf54-749f93ebddb5", reason: Risk ops request
// actorId = "AC210908RJRd7obGR/awVb1b29N6AQ==", userId  = "c5d48715-5381-4985-9a97-1b6fbde466f0", reason: deleted upon user request
// actorId = "AC210718yinE65/DSWeBExwnC5G4SQ==", userId  = "462df172-f4f4-4eab-ab8b-0949af400521", reason: deleted upon user request
// actorId = "AC220128SE9PkupxTNCVxUnyV9qgfA==", userId  = "d217a41d-1180-4f65-a69b-b3c48855df6a", reason: deleted upon user request
// actorId = "ACgFxkEbHCQ+Shicskk37E5g230802==", userId  = "ac393e33-88be-4046-a335-df9995fd9963", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=68006
// actorId = "AC220429rqpvB9iWSuCiEmdoTWIwdw==", userId = "d8986f1b-52a4-4843-a96e-78c1f9cf98cc", reason: https://epifi.slack.com/archives/C05N2BY8AAU/p1704447318587699
// actorId = "ACJlofbtlKQQWDXnPlVvKp1w231212==", userId = "5aa8b4b8-3352-4db3-86f8-e6f1ed729525", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=73449
// actorId = "ACRtZfdBHzQ/Gksm8OHFKA3A240131==", userId = "0dddd18a-5478-4889-9023-d6150b558436", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=76740, deleted using scripts/onboarding_god_script/job_delete_userdetails.go
// actorId = "AC220720RBwvc1aASGaRwcy8ovn6mg==", userId = "b1a42089-aa81-4365-8e8b-5241f5389836", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=91946
// actorId = "AC2204259hdFhiFvTVG4bPWv6fbBLQ==", userId = "6cab82be-b5f4-4eac-ada6-b92879f89f9f", reason: https://epifi.slack.com/archives/C02N8JXBTRT/p1738572703210899
// actorId = "AC2203125C/008pQR2OmRI6RXzHDag==", userId = "4f6e6a28-2eb0-4cca-981f-ec184f610b37", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=95988
// actorId = "AC220307FfRWXO1MSnGLtckU+Oob4w==", userId = "11c4fe82-892b-4520-86a3-1949e29a18d6", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=99063
// actorId = "AC211023DRn8HM+3SQmoKKrRZgEzEQ==", userId = "23b3056d-4fe6-416a-951d-c45a9e66ea7e", reason: https://monorail.pointz.in/p/fi-app/issues/detail?id=99423
// actorId = "ACfYOT39YsQbGfu1UOEq869w230420==", userId = "eaf6e333-999d-4420-b418-45f9a9c8b155", reason: https://github.com/epiFi/tickets/issues/56457

// Params of user to be deleted

// nolint: funlen
func (d *DeleteOnboardedUser) DoJob(ctx context.Context, req *JobRequest) error {
	/*
		1. DeleteToken
		2. Delete Device Registration
		2. DeleteUser
		3. DeleteActor
		4. onb details Delete
	*/

	const (
		actorID = "ACfYOT39YsQbGfu1UOEq869w230420=="
		userId  = "eaf6e333-999d-4420-b418-45f9a9c8b155"
		vendor  = commonvgpb.Vendor_FEDERAL_BANK
	)

	onbDetails, errOnb := d.onbDao.GetOnboardingDetailsByActor(ctx, actorID)
	if errOnb != nil {
		logger.Error(ctx, "error in GetOnboardingDetailsByActor", zap.Error(errOnb))
		return errOnb
	}

	userResp, errUser := d.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_Id{
			Id: userId,
		},
	})
	if grpcErr := epifigrpc.RPCError(userResp, errUser); grpcErr != nil {
		logger.Error(ctx, "error in GetUser", zap.Error(grpcErr))
		return grpcErr
	}

	if errDeReg := d.permanentDeregisterDevice(ctx, d.authClient, actorID); errDeReg != nil {
		logger.Error(ctx, "error in DeactivateDevice Device", zap.Error(errDeReg))
		return errDeReg
	}

	if err := DeleteToken(ctx, d.authClient, userResp.GetUser().GetProfile().GetPhoneNumber()); err != nil {
		logger.Error(ctx, "error in DeleteToken", zap.Error(err))
		return err
	}

	if err := DeleteDeviceRegistration(ctx, d.devRegDao, actorID); err != nil {
		logger.Error(ctx, "error in DeleteDeviceRegistration", zap.Error(err))
		return err
	}

	if err := DeleteEmploymentData(ctx, d.employmentClient, actorID); err != nil {
		logger.Error(ctx, "error in DeleteEmploymentData", zap.Error(err))
		return err
	}

	if err := DeleteUser(ctx, d.userClient, userId); err != nil {
		logger.Error(ctx, "error in DeleteUser", zap.Error(err))
		return err
	}

	if err := DeleteBankCustomer(ctx, d.bankCustomerClient, actorID, vendor); err != nil {
		logger.Error(ctx, "error in DeleteBankCustomer", zap.Error(err))
		return err
	}

	if err := DeleteActor(ctx, d.actorClient, actorID); err != nil {
		logger.Error(ctx, "error in DeleteActor", zap.Error(err))
		return err
	}

	if err := DeleteOnbDetails(ctx, d.onbDao, onbDetails.GetOnboardingId()); err != nil {
		logger.Error(ctx, "error in DeleteOnbDetails", zap.Error(err))
		return err
	}

	return nil
}

func DeleteEmploymentData(ctx context.Context, employmentClient employmentPb.EmploymentClient, actorId string) error {
	res, err := employmentClient.DeleteEmploymentData(ctx, &employmentPb.DeleteEmploymentDataRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil && !res.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in deleting employment data", zap.Error(err))
		return err
	}
	return nil
}

func DeleteToken(ctx context.Context, authClient auth.AuthClient, phoneNumber *commontypes.PhoneNumber) error {
	res, err := authClient.UpdateToken(ctx, &auth.UpdateTokenRequest{
		Status: auth.UpdateTokenRequest_DELETE,
		Identifier: &auth.UpdateTokenRequest_PhoneNumber{
			PhoneNumber: phoneNumber,
		},
		TokenTypes: []auth.TokenType{
			auth.TokenType_ACCESS_TOKEN,
			auth.TokenType_REFRESH_TOKEN,
		},
		TokenUpdationReason: auth.TokenDeletionReason_TOKEN_DELETION_REASON_RESET_USER,
	})

	return epifigrpc.RPCError(res, err)
}

func DeleteUser(ctx context.Context, userClient user.UsersClient, userId string) error {
	res, err := userClient.DeleteUser(ctx, &user.DeleteUserRequest{
		UserId: userId,
		DeletionDetails: &user.DeletionDetails{
			DeletionReason: user.DeletionDetails_DELETION_REASON_SCRIPT,
		},
	})
	return epifigrpc.RPCError(res, err)
}

func DeleteActor(ctx context.Context, actorClient actor.ActorClient, actorId string) error {
	res, err := actorClient.DeleteActor(ctx, &actor.DeleteActorRequest{
		ActorId: actorId,
	})
	return epifigrpc.RPCError(res, err)
}

func DeleteOnbDetails(ctx context.Context, onboardingDao onbDao.OnboardingDao, onbId string) error {
	return onboardingDao.Delete(ctx, onbId)
}

func DeleteDeviceRegistration(ctx context.Context, dao authDao.DeviceRegistrationDao, actorId string) error {
	devReg, err := dao.GetDeviceRegistration(ctx, actorId, "")
	if err != nil {
		if pkgErr.Is(err, gorm.ErrRecordNotFound) {
			logger.Info(ctx, "no active device registration record found for actor")
			return nil
		}
		return pkgErr.Wrap(err, "error finding dev reg record from actor")
	}

	if err = dao.DeleteDeviceRegistration(ctx, actorId, devReg.GetDeviceId()); err != nil {
		if pkgErr.Is(err, gorm.ErrRecordNotFound) {
			logger.Info(ctx, "no active device registration record found for deletion")
			return nil
		}
		return pkgErr.Wrap(err, "error deleting dev reg record from actor and device")
	}

	return nil
}

func DeleteBankCustomer(ctx context.Context, bankCustomerClient bankCustomerPb.BankCustomerServiceClient, actorId string, vendor commonvgpb.Vendor) error {
	res, err := bankCustomerClient.DeleteBankCustomer(ctx, &bankCustomerPb.DeleteBankCustomerRequest{
		ActorId: actorId,
		Vendor:  vendor,
	})
	return epifigrpc.RPCError(res, err)
}

func (d *DeleteOnboardedUser) permanentDeregisterDevice(ctx context.Context, authClient auth.AuthClient, actorId string) error {
	deActResp, errDeAct := authClient.DeactivateDevice(ctx, &auth.DeactivateDeviceRequest{
		ActorId:          actorId,
		DeactivationType: auth.DeactivateDeviceRequest_DEACTIVATION_TYPE_PERMANENT,
	})

	return epifigrpc.RPCError(deActResp, errDeAct)
}
