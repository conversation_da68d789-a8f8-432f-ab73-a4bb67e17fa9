#!/bin/bash

# GST Fallback Test Runner for Staging
# Usage: ./run_gst_fallback_test.sh <actor_id>

if [ $# -eq 0 ]; then
    echo "Usage: $0 <actor_id>"
    echo "Example: $0 'AC230206pfc2PlEFSayZjijTLKizOA=='"
    exit 1
fi

ACTOR_ID="$1"
echo "Testing GST fallback logic for Actor ID: $ACTOR_ID"
echo "=================================================="

# Create temporary SQL file with the actor ID
TEMP_SQL=$(mktemp /tmp/gst_test_XXXXXX.sql)

cat > "$TEMP_SQL" << EOF
-- GST Fallback Logic Test for Actor: $ACTOR_ID
-- Generated on: $(date)

\echo 'Testing GST Fallback Logic'
\echo 'Actor ID: $ACTOR_ID'
\echo '=========================='

-- Step 1: Check bank customer SOL ID
\echo ''
\echo 'Step 1: Bank Customer SOL ID Check'
\echo '-----------------------------------'
SELECT 
    'Bank Customer' as source,
    actor_id,
    vendor_customer_id,
    (vendor_metadata->'federal_metadata'->>'sol_id') as sol_id,
    CASE 
        WHEN (vendor_metadata->'federal_metadata'->>'sol_id') IS NULL 
             OR (vendor_metadata->'federal_metadata'->>'sol_id') = ''
        THEN 'EMPTY - Will use fallback'
        ELSE 'AVAILABLE - Will use this'
    END as status
FROM bank_customers 
WHERE actor_id = '$ACTOR_ID' 
  AND vendor = 'FEDERAL_BANK';

-- Step 2: Check savings account IFSC (fallback source)
\echo ''
\echo 'Step 2: Savings Account IFSC Check (Fallback Source)'
\echo '----------------------------------------------------'
SELECT 
    'Savings Account' as source,
    actor_id,
    account_no,
    ifsc_code,
    CASE 
        WHEN LENGTH(ifsc_code) >= 4 
        THEN RIGHT(ifsc_code, 4)
        ELSE 'N/A'
    END as last_4_chars,
    CASE 
        WHEN ifsc_code IS NULL OR ifsc_code = ''
        THEN 'EMPTY - Fallback will fail'
        WHEN LENGTH(ifsc_code) < 4
        THEN 'TOO SHORT - Fallback will fail'
        ELSE 'VALID - Fallback will work'
    END as fallback_status
FROM savings_accounts 
WHERE actor_id = '$ACTOR_ID' 
  AND partner_bank = 'FEDERAL_BANK';

-- Step 3: Simulate the complete fallback logic
\echo ''
\echo 'Step 3: Complete Fallback Logic Simulation'
\echo '-------------------------------------------'
WITH bank_data AS (
    SELECT 
        actor_id,
        (vendor_metadata->'federal_metadata'->>'sol_id') as sol_id
    FROM bank_customers 
    WHERE actor_id = '$ACTOR_ID' AND vendor = 'FEDERAL_BANK'
),
savings_data AS (
    SELECT 
        actor_id,
        ifsc_code,
        CASE 
            WHEN LENGTH(ifsc_code) >= 4 
            THEN RIGHT(ifsc_code, 4)
            ELSE NULL
        END as ifsc_sol_id
    FROM savings_accounts 
    WHERE actor_id = '$ACTOR_ID' AND partner_bank = 'FEDERAL_BANK'
)
SELECT 
    'Final Result' as test_result,
    '$ACTOR_ID' as actor_id,
    bd.sol_id as bank_sol_id,
    sd.ifsc_code as savings_ifsc,
    sd.ifsc_sol_id as ifsc_extracted_sol_id,
    CASE 
        WHEN bd.sol_id IS NOT NULL AND bd.sol_id != ''
        THEN bd.sol_id
        WHEN sd.ifsc_sol_id IS NOT NULL
        THEN sd.ifsc_sol_id
        ELSE 'ERROR'
    END as final_user_sol_id,
    CASE 
        WHEN bd.sol_id IS NOT NULL AND bd.sol_id != ''
        THEN 'SUCCESS: Using bank customer SOL ID (normal flow)'
        WHEN sd.ifsc_sol_id IS NOT NULL
        THEN 'SUCCESS: Using IFSC fallback (fallback flow)'
        ELSE 'FAILURE: No valid SOL ID available'
    END as result_description
FROM bank_data bd
FULL OUTER JOIN savings_data sd ON bd.actor_id = sd.actor_id;

\echo ''
\echo 'Test completed for Actor ID: $ACTOR_ID'
\echo '======================================'
EOF

echo ""
echo "Generated SQL test file: $TEMP_SQL"
echo ""
echo "To run this test against your staging database:"
echo "psql -h <staging_host> -U <username> -d <database> -f $TEMP_SQL"
echo ""
echo "Or copy and paste the SQL content into your database client:"
echo ""
cat "$TEMP_SQL"

# Clean up
# rm "$TEMP_SQL"
