Application:
  Environment: "qa"

Aws:
  Region: "ap-south-1"

EpifiDb:
  Username: "epifi_wealth_dev_user"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "verify-full"
  StatementTimeout: 2m
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_wealth_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_wealth_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

S3Conf:
  Bucket: "epifi-qa-wealth-onboarding"
  LivenessPath: "liveness"
  DownloadedKraDocS3Path: "downloaded_kra_doc"
