package config

import (
	"fmt"
	"path/filepath"
	"runtime"
	"sync"

	"github.com/epifi/be-common/pkg/cfg"
)

var (
	once       sync.Once
	config     *Config
	err        error
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig() (*Config, error) {
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	conf := &Config{}
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, "risk_trigger_manual_case_reprioritisation")
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}
	unmarshalErr := k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if unmarshalErr != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", unmarshalErr)
	}
	loadSecretsErr := cfg.LoadAllSecretsV3(conf, conf.Application.Environment, conf.AWS.Region)
	if loadSecretsErr != nil {
		return nil, loadSecretsErr
	}
	return conf, nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..")
	fmt.Println(configPath)
	return configPath
}

type Config struct {
	Application         *application
	AWS                 *cfg.AWS
	TemporalCodecConfig *TemporalCodecConfig
}

type application struct {
	Name        string
	Environment string
}

type TemporalCodecConfig struct {
	TemporalCodecAesKeyValue string `field:"TemporalCodecAesKey"`
	TemporalCodecAesKey      string `iam:"sm-read"`
}
