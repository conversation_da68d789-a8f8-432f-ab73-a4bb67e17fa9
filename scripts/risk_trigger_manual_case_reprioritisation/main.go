//nolint:funlen,dupl,gocritic,unused
package main

import (
	"context"
	"flag"
	"fmt"
	"strings"

	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"
	workflowPkg "github.com/epifi/be-common/pkg/epifitemporal/workflow"

	riskWorkflowPb "github.com/epifi/gamma/api/risk/workflow"
	"github.com/epifi/gamma/api/vendorgateway/crm/risk"

	"github.com/epifi/gamma/scripts/risk_trigger_manual_case_reprioritisation/config"

	workflowPb "github.com/epifi/be-common/api/celestial/workflow"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"

	epifitemporalClient "github.com/epifi/be-common/pkg/epifitemporal/client"

	"github.com/epifi/be-common/pkg/epifitemporal/router"
	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"

	"google.golang.org/grpc/metadata"
)

const (
	workflowName = "CaseReprioritisation"
)

func main() {
	var cronExp string
	flag.StringVar(&cronExp, "cron-exp", "", "Cron expression, keep empty if triggering once")
	var dryRun, isManualUpload bool
	var ticketsS3Url string
	flag.BoolVar(&dryRun, "dry-run", false, "Dry run")
	flag.BoolVar(&isManualUpload, "is-manual-upload", false, "Is manual upload?")
	flag.StringVar(&ticketsS3Url, "s3-url", "", "Tickets S3 URL")
	flag.Parse()

	cronExp = strings.TrimSpace(cronExp)
	// Get environment
	envName, err := cfg.GetEnvironment()
	if err != nil {
		panic(fmt.Errorf("failed to get environment: %w", err))
	}

	logger.Init(envName)

	// Load configuration
	conf, err := config.Load()
	if err != nil {
		panic(err)
	}

	defer func() { _ = logger.Log.Sync() }()
	ctx := epificontext.WithTraceId(context.Background(), metadata.New(map[string]string{}))

	logger.Info(ctx, "case reprioritisation trigger started")

	workflowRouter := router.GetRouter().GetWorkflowRouter()
	workflow := getWorkflow(workflowName, workflowPb.Version_V0.String())

	namespace, err := workflowRouter.GetNamespace(workflow)
	if err != nil {
		logger.Panic("error in fetching namespace for workflow", zap.String(logger.WORKFLOW, string(workflow)), zap.Error(err))
	}

	envNamespace := fmt.Sprintf("%s-%s", envName, strings.ToLower(string(namespace)))

	if err != nil {
		logger.Panic("error converting payload", zap.Error(err))
	}

	temporalClient, err := epifitemporalClient.NewWorkflowClient(envNamespace, true, conf.TemporalCodecConfig.TemporalCodecAesKeyValue)
	if err != nil {
		logger.Panic("failed to initialise workflow client", zap.Error(err))
	}
	defer temporalClient.Close()

	workflowRun, err := workflowPkg.ExecuteAsync(ctx, temporalClient, riskNs.CaseReprioritisation, &riskWorkflowPb.CaseReprioritisationRequest{
		DryRun:         dryRun,
		IsManualUpload: isManualUpload,
		TicketsS3Url:   ticketsS3Url,
		Filters: &riskWorkflowPb.CaseFetchFilters{
			ReviewTypes: []risk.ReviewType{
				risk.ReviewType_REVIEW_TYPE_TRANSACTION_REVIEW,
			},
		},
	}, nil)
	if err != nil {
		panic(err)
	}

	logger.Info(ctx, "executed workflow successfully", zap.String(logger.WORKFLOW_REQ_ID, workflowRun.GetID()))
}

func getWorkflow(workflowType string, workflowVersion string) epifitemporal.Workflow {
	if workflowVersion == "V0" {
		return epifitemporal.Workflow(workflowType)
	}
	return epifitemporal.Workflow(workflowType + workflowVersion)
}
