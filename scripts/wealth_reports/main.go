package main

import (
	"context"
	"flag"
	"fmt"
	"strings"
	"time"

	"github.com/slack-go/slack"
	"go.uber.org/zap"

	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	cfgConf "github.com/epifi/be-common/pkg/cfg/genconf"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	vgStocksPb "github.com/epifi/gamma/api/vendorgateway/stocks"
	"github.com/epifi/gamma/investment/mutualfund/dao/impl"
	fileGeneratorDaoImpl "github.com/epifi/gamma/pay/internationalfundtransfer/file_generator/dao/impl"
	orderDaoImpl "github.com/epifi/gamma/usstocks/order/dao/impl"

	orderPb "github.com/epifi/gamma/api/usstocks/order"
	"github.com/epifi/gamma/scripts/wealth_reports/config"
	"github.com/epifi/gamma/scripts/wealth_reports/reports"
	usCatalog "github.com/epifi/gamma/usstocks/catalog/dao/impl"
)

// Available auditor types
const (
	dateFormat                      = "2006-01-02"
	IFTFileGenAttemptDAOMaxPageSize = 20
)

// By default, the slack channel id is set to the save-reports channel
var (
	opsReportSlackChannelId = flag.String("channelid", "C04S8KRHHD5", "slack channel id to post reports in")
	dateStr                 = flag.String("date", "", "date in YYYY-MM-DD format (defaults to 2 business days back)")
	auditorsStr             = flag.String("auditors", "", "comma-separated list of auditors to run")
)

// Helper function to get map keys
func getMapKeys[K comparable, V any](m map[K]V) []K {
	keys := make([]K, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

func getTargetDate(dateStr string) (time.Time, error) {
	if dateStr == "" {
		// Get 2 business days back
		d := time.Now().UTC()
		for i := 0; i < 5; i++ { // max 5 days back to get 2 business days
			d = d.AddDate(0, 0, -2)
			if d.Weekday() != time.Saturday && d.Weekday() != time.Sunday {
				return d, nil
			}
		}
		return d, nil
	}

	parsedDate, err := time.Parse(dateFormat, dateStr)
	if err != nil {
		return time.Time{}, fmt.Errorf("invalid date format, use YYYY-MM-DD: %v", err)
	}

	if parsedDate.Weekday() == time.Saturday || parsedDate.Weekday() == time.Sunday {
		return time.Time{}, fmt.Errorf("specified date is a weekend, please provide a business day")
	}

	return parsedDate.UTC(), nil
}

func main() {
	flag.Parse()
	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}
	logger.Init(env)
	defer func() { _ = logger.Log.Sync() }()

	// Validate and get target date
	targetDate, err := getTargetDate(*dateStr)
	if err != nil {
		logger.Panic("Invalid date", zap.Error(err))
	}

	// Validate auditor types
	if *auditorsStr == "" {
		logger.Panic("No auditors specified. Use -auditors flag with comma-separated values.")
	}

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}

	ctx := context.Background()

	// Initialize database connections
	epifiTechDB, err := storagev2.NewGormDB(conf.DbConfigMap["EPIFI_TECH"])
	if err != nil {
		logger.Panic("Failed to initialize epifi tech database", zap.Error(err))
	}

	usStocksAlpacaDB, err := storagev2.NewGormDB(conf.DbConfigMap["US_STOCKS_ALPACA"])
	if err != nil {
		logger.Panic("Failed to initialize US stocks database", zap.Error(err))
	}

	// Initialize database connections
	epifiWealthDB, err := storagev2.NewGormDB(conf.DbConfigMap["EPIFI_WEALTH"])
	if err != nil {
		logger.Panic("Failed to initialize wealth database", zap.Error(err))
	}

	// Initialize S3 client
	awsConf, err := awsconfpkg.NewAWSConfig(ctx, conf.Aws.Region, false)
	if err != nil {
		logger.Panic("failed to get AWS config", zap.Error(err))
	}

	bucketName := conf.Aws.S3BucketName
	s3Client := s3.NewClient(awsConf, bucketName)

	// Initialize dependencies
	idg := idgen.NewDomainIdGenerator(idgen.NewClock())
	orderDao := impl.NewOrderCrdb(epifiWealthDB, idg)

	// Initialize VG client
	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	vgStocksClient := vgStocksPb.NewStocksClient(vgConn)

	// Initialize OrderManager client
	orderManagerClient := orderPb.NewOrderManagerClient(epifigrpc.NewConnByService(cfg.US_STOCKS_SERVICE))

	// Initialize file generation attempt dao
	fileGenerationAttemptDao := fileGeneratorDaoImpl.NewFileGenerationAttemptCrdb(epifiTechDB, IFTFileGenAttemptDAOMaxPageSize)

	// Initialize wallet order dao
	pgdbConfig := &cfgConf.PgdbConn{}
	walletOrderDao := orderDaoImpl.NewWalletOrderPgdb(pgdbConfig, usStocksAlpacaDB, idg, IFTFileGenAttemptDAOMaxPageSize)

	// Initialize Slack client
	slackClient := slack.New(conf.Secrets.Ids[config.SlackBotOauthToken], slack.OptionDebug(true))

	// Initialize Presto DB connections
	ruleSubscriptionsDB, err := storagev2.NewPrestoDBFromConfig(conf.PrestoConfig["RMS_TECH"], conf.Application.Environment)
	if err != nil {
		logger.Panic("Failed to initialize database", zap.Error(err))
		logger.ErrorNoCtx("Failed to initialize rule subscriptions Presto database", zap.Error(err))
	}
	defer func() {
		if err := ruleSubscriptionsDB.Close(); err != nil {
			logger.ErrorNoCtx("Failed to close rule subscriptions Presto database", zap.Error(err))
		}
	}()

	actionExecutionsDB, err := storagev2.NewPrestoDBFromConfig(conf.PrestoConfig["FITT_TECH"], conf.Application.Environment)
	if err != nil {
		logger.Panic("Failed to initialize action executions Presto database", zap.Error(err))
	}
	defer func() {
		if err := actionExecutionsDB.Close(); err != nil {
			logger.ErrorNoCtx("Failed to close action executions Presto database", zap.Error(err))
		}
	}()

	// Initialize StockDao for usstocks
	usstocksStockDao := usCatalog.NewStockPgdb(pgdbConfig, usStocksAlpacaDB, idg)

	mutualFundDao := impl.NewMutualFundCrdb(epifiWealthDB, idg)

	// Initialize mutual fund Presto database
	mutualFundPrestoDB, err := storagev2.NewPrestoDBFromConfig(conf.PrestoConfig["EPIFI_WEALTH"], conf.Application.Environment)
	if err != nil {
		logger.Panic("Failed to initialize mutual fund Presto database", zap.Error(err))
	}
	defer func() {
		if err := mutualFundPrestoDB.Close(); err != nil {
			logger.ErrorNoCtx("Failed to close mutual fund Presto database", zap.Error(err))
		}
	}()

	// Define auditor factories
	auditorFactories := map[string]func() reports.Auditor{
		"mf_orders": func() reports.Auditor {
			return reports.NewMFOrdersAuditor(orderDao)
		},
		"alpaca_outward_recon": func() reports.Auditor {
			return reports.NewAlpacaOutwardReconAuditor(
				"alpaca_outward_recon",
				vgStocksClient,
				fileGenerationAttemptDao,
				orderManagerClient,
				*walletOrderDao,
				s3Client,
			)
		},
		"alpaca_statement": func() reports.Auditor {
			return reports.NewAlpacaOutwardReconAuditor(
				"alpaca_statement",
				vgStocksClient,
				fileGenerationAttemptDao,
				orderManagerClient,
				*walletOrderDao,
				s3Client,
			)
		},
		"uss_sip_execution": func() reports.Auditor {
			return reports.USSSIPAuditor(
				ruleSubscriptionsDB,
				actionExecutionsDB,
			)
		},
		"mf_sip_execution": func() reports.Auditor {
			return reports.MutualFundSIPAuditor(
				ruleSubscriptionsDB,
				actionExecutionsDB,
			)
		},
		"uss_catalog_diff": func() reports.Auditor {
			return reports.USSCatalogDiffAuditor(vgStocksClient, usstocksStockDao)
		},
		"mf_allowed_funds_report": func() reports.Auditor {
			return reports.NewMFAllowedFundsAuditor(mutualFundPrestoDB, mutualFundDao)
		},
		"missing_nav_auditor": func() reports.Auditor {
			return reports.NewMissingNAVAuditor(mutualFundPrestoDB)
		},
	}

	requestedAuditors := strings.Split(*auditorsStr, ",")
	logger.Info(ctx, "Running auditors",
		zap.Time("target_date", targetDate),
		zap.Strings("auditors", requestedAuditors))

	// Run requested auditors
	for _, auditor := range requestedAuditors {
		factory, exists := auditorFactories[auditor]
		if !exists {
			logger.Panic("Invalid auditor type",
				zap.String("auditor", auditor),
				zap.String("available", strings.Join(getMapKeys(auditorFactories), ",")))
		}

		auditor := factory()
		success, reportData, err := auditor.Audit(ctx, targetDate)
		if err != nil {
			logger.Panic("Failed to run audit", zap.Error(err))
		}

		if err := reports.SendReportViaSlack(slackClient, *opsReportSlackChannelId, success, *reportData); err != nil {
			logger.Panic("Failed to send Slack report", zap.Error(err))
		}
	}
}
