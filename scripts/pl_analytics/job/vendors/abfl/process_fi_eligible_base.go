package abfl

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/csv"
	"fmt"
	"strings"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"

	airflowPb "github.com/epifi/gamma/api/pkg/airflow"
	airflowPayloadPb "github.com/epifi/gamma/api/pkg/airflow/payload"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors"
)

const (
	vendorRequestIdMaxLength = 16
)

// order of columns in  input csv file
// firehose_id, loan_scheme, batch_id

// nolint: funlen, gocritic
func (a *Processor) ProcessFiEligibleBase(ctx context.Context, req *vendors.ProcessFiEligibleBaseReq) error {
	loanProgram := vendors.StringToLoanProgram(req.LoanProgram)

	csvRows := req.CsvRecords
	lecCreated := 0
	uniqueCustomersInBatch := 0
	alreadyPresent := 0 // idempotent AND duplicate entries
	errMap := make(map[string]error)
	firehoseIdToErrorMap := make(map[string]error)

	firstCsvRow := (*csvRows)[0]
	if len(firstCsvRow) != 3 {
		return fmt.Errorf("number of columns should be 3 and should be in order: firehose_id,loan_scheme,batch_id")
	}
	if strings.ToUpper(firstCsvRow[0]) != strings.ToUpper("firehose_id") || strings.ToUpper(firstCsvRow[1]) != strings.ToUpper("loan_scheme") || strings.ToUpper(firstCsvRow[2]) != strings.ToUpper("batch_id") {
		return fmt.Errorf("column heading names is not as per the expected behaviour, it should be in order: firehose_id,loan_scheme,batch_id")
	}

	fireHoseIds := make([]string, 0)
	for _, rawRecord := range (*csvRows)[1:] {
		fireHoseIds = append(fireHoseIds, rawRecord[0])
	}
	fireHoseIdToActorIdMap := vendors.GetActorIdsFromFirehoseIds(ctx, fireHoseIds, a.vendorMappingDao)
	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_LOANS_ABFL)
	for rawRecordIndex, rawRecord := range (*csvRows)[1:] {
		// log progress after every 1000 records
		if rawRecordIndex%1000 == 0 {
			logger.Info(ctx, fmt.Sprintf("processing record %vth", rawRecordIndex))
		}
		uniqueCustomersInBatch++

		actorId, ok := fireHoseIdToActorIdMap[rawRecord[0]]
		if !ok {
			firehoseIdToErrorMap[rawRecord[0]] = fmt.Errorf("actorId not found for firehoseId: %v", rawRecord[0])
			continue
		}

		isCreated, err := a.checkAndcreateLoec(ctx, actorId, rawRecord[2], rawRecord[1], loanProgram)
		if err != nil {
			errMap[actorId] = fmt.Errorf("failed to create lec: %v", zap.Error(err))
			continue
		}
		if isCreated {
			lecCreated++
		} else {
			alreadyPresent++
		}

		// ~500 QPS (2ms sleep)
		time.Sleep(2 * time.Millisecond)
	}

	for k, v := range errMap {
		logger.InfoNoCtx(fmt.Sprintf("actorId: %v, Err:%v", k, v.Error()))
	}
	for k, v := range firehoseIdToErrorMap {
		logger.InfoNoCtx(fmt.Sprintf("firehoseId: %v, Err:%v", k, v.Error()))
	}
	logger.Info(ctx, fmt.Sprintf("error in loec creation for %v", len(errMap)))
	logger.Info(ctx, fmt.Sprintf("not able to find actorIds from firehose id %v", len(firehoseIdToErrorMap)))
	logger.Info(ctx, fmt.Sprintf("number of customers already present (idempotent or duplicate) : %v", alreadyPresent))
	logger.Info(ctx, fmt.Sprintf("successfully performed loec creation for %v, out of total customers %v", lecCreated, uniqueCustomersInBatch))

	dagTriggerErr := a.triggerPiiDataDag(ctx, fireHoseIdToActorIdMap)
	if dagTriggerErr != nil {
		logger.Info(ctx, "failed to trigger airflow DAG to generate hash and masked file", zap.Error(dagTriggerErr))
		return errors.Wrap(dagTriggerErr, "failed to trigger airflow DAG to generate hash and masked file")
	}

	if len(errMap) > 0 || len(firehoseIdToErrorMap) > 0 {
		return errors.New("error in creating some of the loec entries")
	}
	return nil
}

func (a *Processor) checkAndcreateLoec(ctx context.Context, actorId, batchId, loanScheme string, loanProgram palPb.LoanProgram) (bool, error) {
	lecs, lecsErr := a.loanOfferEligibilityCriteriaDao.GetByActorIdLoanProgramsAndStatuses(ctx, actorId, []palPb.LoanProgram{loanProgram},
		[]palPb.LoanOfferEligibilityCriteriaStatus{
			palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_APPROVED,
			palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED,
		}, 0, true)
	if lecsErr != nil && !errors.Is(lecsErr, epifierrors.ErrRecordNotFound) {
		return false, errors.Wrap(lecsErr, fmt.Sprintf("failed to fetch entry in loan_offers_eligibility_criteria table for actorId: %s", actorId))
	}

	// idempotency condition :
	if lecs != nil && lecs[0].GetBatchId() == batchId {
		logger.Info(ctx, "idempotency condition", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, nil
	}

	// if isDuplicateEntry is true, transaction would have got cancelled ,so we have to create the loec separately
	_, lecErr := a.loanOfferEligibilityCriteriaDao.Create(ctx, &palPb.LoanOfferEligibilityCriteria{
		ActorId:         actorId,
		Vendor:          palPb.Vendor_ABFL,
		Status:          palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED,
		VendorResponse:  nil,
		BatchId:         batchId,
		LoanProgram:     loanProgram,
		LoanScheme:      loanScheme,
		VendorRequestId: idgen.RandAlphaNumericString(vendorRequestIdMaxLength),
	})
	if lecErr != nil {
		return false, fmt.Errorf("unable to create lec : %w", lecErr)
	}
	return true, nil
}

func (p *Processor) convertCsvRecordsToBytes(
	csvRecords *[][]string,
) ([]byte, error) {
	var output strings.Builder
	csvWriter := csv.NewWriter(&output)
	defer func() {
		csvWriter.Flush()
	}()
	err := csvWriter.WriteAll(*csvRecords)
	if err != nil {
		return nil, fmt.Errorf("failed to write csv records, err: %v", err)
	}
	return []byte(output.String()), nil
}

func (p *Processor) uploadToS3(ctx context.Context,
	data []byte,
	path string,
) error {
	err := p.s3Client.Write(ctx, path, data, "bucket-owner-full-control")
	if err != nil {
		return fmt.Errorf("failed to upload file to s3, err: %v", err)
	}
	return nil
}

// getFirehoseIdCsvColumn converts firehose ids to csv records
func getFirehoseIdCsvColumn(rawLoecRecords map[string]string) *[][]string {
	var csvRecords [][]string

	// add headers
	csvRecords = append(csvRecords, []string{
		"FIREHOSE_ID",
	})

	for firehoseId, _ := range rawLoecRecords {
		csvRecords = append(csvRecords, []string{
			firehoseId,
		})
	}

	return &csvRecords
}

func (a *Processor) triggerPiiDataDag(ctx context.Context, fireHoseIdToActorIdMap map[string]string) error {
	outputCsvRecords := getFirehoseIdCsvColumn(fireHoseIdToActorIdMap)

	outputFileBytes, err := a.convertCsvRecordsToBytes(outputCsvRecords)
	if err != nil {
		return fmt.Errorf("failed to generate and store csv file, err: %v", err)
	}

	currentDateStr := fmt.Sprintf("%v-%v-%v", a.time.Now().Day(), a.time.Now().Month(), a.time.Now().Year())
	eligibleBaseSubPath := fmt.Sprintf("%v/%v", currentDateStr, "pl_eligible_base_abfl.csv")
	eligibleBaseOutputS3Path := fmt.Sprintf("pl_analytics/abfl/%v", eligibleBaseSubPath)
	err = a.uploadToS3(ctx, outputFileBytes, eligibleBaseOutputS3Path)
	if err != nil {
		return fmt.Errorf("failed to upload file to s3, err: %v", err)
	}

	logger.Info(ctx, "triggering airflow DAG to generate hash and masked file", zap.String("eligibleBaseOutputS3Path", eligibleBaseOutputS3Path))

	airflowRes, err := a.airflow.TriggerDag(ctx, &airflowPb.TriggerDagRequest{
		DagName: airflowPb.DAGName_DERIVED_USER_ATTRIBUTES,
		DagConfig: &airflowPb.DagConfig{
			Config: &airflowPb.DagConfig_DerivedUserAttributes{
				DerivedUserAttributes: &airflowPayloadPb.DerivedUserAttributesJobConfig{
					InputFileS3Path:       fmt.Sprintf("abfl/%v", eligibleBaseSubPath),
					FirehoseIdColumnName:  "FIREHOSE_ID",
					OutputFileS3Path:      currentDateStr,
					OutputFileColumnNames: "FIREHOSE_ID, PAN_FIRST_1_LETTER",
					// using lending_idfc as a usecase to fetch the feature as the above feature is already present for idfc
					UseCase: "LENDING_IDFC",
				},
			},
		},
	})
	if err != nil {
		return errors.Wrap(err, "failed to trigger airflow DAG to generate hash and masked file")
	}
	logger.Info(ctx, "successfully triggered airflow DAG to generate hash and masked file", zap.Any("airflowRes", airflowRes))

	return nil
}
