-- GST Fallback Logic Testing Script for Staging
-- This script helps test the fallback mechanism for userSolId in GST reporting
-- Usage: Replace 'YOUR_ACTOR_ID_HERE' with the actual actor ID you want to test

-- Set the actor ID to test (replace this value)
\set actor_id 'YOUR_ACTOR_ID_HERE'

-- Display test information
\echo '================================================'
\echo 'GST Fallback Logic Database Test Script'
\echo '================================================'
\echo 'Testing Actor ID: ' :actor_id
\echo ''

-- Test 1: Check if actor exists and get basic info
\echo '🔍 Test 1: Checking actor existence and basic info'
\echo '------------------------------------------------'
SELECT 
    'Actor Info' as test_name,
    id as actor_id,
    entity_id,
    entity_type,
    created_at
FROM actors 
WHERE id = :'actor_id';

-- Test 2: Check bank customer data and SOL ID availability
\echo ''
\echo '🏦 Test 2: Checking bank customer data and SOL ID'
\echo '------------------------------------------------'
SELECT 
    'Bank Customer SOL ID' as test_name,
    actor_id,
    vendor_customer_id,
    vendor_metadata->>'federal_metadata' as federal_metadata,
    (vendor_metadata->'federal_metadata'->>'sol_id') as current_sol_id,
    CASE 
        WHEN (vendor_metadata->'federal_metadata'->>'sol_id') IS NULL 
             OR (vendor_metadata->'federal_metadata'->>'sol_id') = '' 
        THEN 'EMPTY - Will trigger fallback'
        ELSE 'AVAILABLE - No fallback needed'
    END as sol_id_status
FROM bank_customers 
WHERE actor_id = :'actor_id' 
  AND vendor = 'FEDERAL_BANK';

-- Test 3: Check savings account and IFSC code (fallback source)
\echo ''
\echo '💰 Test 3: Checking savings account and IFSC code (fallback source)'
\echo '-------------------------------------------------------------------'
SELECT 
    'Savings Account IFSC' as test_name,
    actor_id,
    account_no,
    ifsc_code,
    CASE 
        WHEN ifsc_code IS NULL OR ifsc_code = '' 
        THEN 'EMPTY - Fallback will fail'
        WHEN LENGTH(ifsc_code) < 4 
        THEN 'TOO SHORT - Fallback will fail'
        ELSE 'AVAILABLE - Fallback will work'
    END as ifsc_status,
    CASE 
        WHEN LENGTH(ifsc_code) >= 4 
        THEN RIGHT(ifsc_code, 4)
        ELSE 'N/A'
    END as extracted_sol_id,
    partner_bank,
    state as account_state
FROM savings_accounts 
WHERE actor_id = :'actor_id' 
  AND partner_bank = 'FEDERAL_BANK';

-- Test 4: Simulate the fallback logic decision
\echo ''
\echo '🔄 Test 4: Simulating fallback logic decision'
\echo '--------------------------------------------'
WITH bank_customer_data AS (
    SELECT 
        actor_id,
        (vendor_metadata->'federal_metadata'->>'sol_id') as sol_id
    FROM bank_customers 
    WHERE actor_id = :'actor_id' 
      AND vendor = 'FEDERAL_BANK'
),
savings_data AS (
    SELECT 
        actor_id,
        ifsc_code,
        CASE 
            WHEN LENGTH(ifsc_code) >= 4 
            THEN RIGHT(ifsc_code, 4)
            ELSE NULL
        END as ifsc_last_4
    FROM savings_accounts 
    WHERE actor_id = :'actor_id' 
      AND partner_bank = 'FEDERAL_BANK'
)
SELECT 
    'Fallback Logic Simulation' as test_name,
    bc.actor_id,
    bc.sol_id as bank_customer_sol_id,
    sa.ifsc_code as savings_ifsc_code,
    sa.ifsc_last_4 as ifsc_extracted_sol_id,
    CASE 
        WHEN bc.sol_id IS NOT NULL AND bc.sol_id != '' 
        THEN bc.sol_id
        WHEN sa.ifsc_last_4 IS NOT NULL 
        THEN sa.ifsc_last_4
        ELSE 'ERROR: No valid SOL ID available'
    END as final_user_sol_id,
    CASE 
        WHEN bc.sol_id IS NOT NULL AND bc.sol_id != '' 
        THEN 'Used bank customer SOL ID (normal flow)'
        WHEN sa.ifsc_last_4 IS NOT NULL 
        THEN 'Used IFSC fallback (fallback flow)'
        ELSE 'ERROR: Both sources failed'
    END as logic_path
FROM bank_customer_data bc
FULL OUTER JOIN savings_data sa ON bc.actor_id = sa.actor_id;

-- Test 5: Check for potential issues
\echo ''
\echo '⚠️  Test 5: Checking for potential issues'
\echo '----------------------------------------'
WITH issue_check AS (
    SELECT 
        :'actor_id' as actor_id,
        -- Check if actor exists
        EXISTS(SELECT 1 FROM actors WHERE id = :'actor_id') as actor_exists,
        -- Check if bank customer exists
        EXISTS(SELECT 1 FROM bank_customers WHERE actor_id = :'actor_id' AND vendor = 'FEDERAL_BANK') as bank_customer_exists,
        -- Check if savings account exists
        EXISTS(SELECT 1 FROM savings_accounts WHERE actor_id = :'actor_id' AND partner_bank = 'FEDERAL_BANK') as savings_account_exists,
        -- Check SOL ID availability
        (SELECT (vendor_metadata->'federal_metadata'->>'sol_id') 
         FROM bank_customers 
         WHERE actor_id = :'actor_id' AND vendor = 'FEDERAL_BANK') as sol_id,
        -- Check IFSC availability
        (SELECT ifsc_code 
         FROM savings_accounts 
         WHERE actor_id = :'actor_id' AND partner_bank = 'FEDERAL_BANK' 
         LIMIT 1) as ifsc_code
)
SELECT 
    'Issue Analysis' as test_name,
    actor_id,
    CASE 
        WHEN NOT actor_exists THEN 'ERROR: Actor does not exist'
        WHEN NOT bank_customer_exists THEN 'ERROR: Bank customer record missing'
        WHEN NOT savings_account_exists THEN 'ERROR: Savings account missing'
        WHEN (sol_id IS NULL OR sol_id = '') AND (ifsc_code IS NULL OR ifsc_code = '') THEN 'ERROR: Both SOL ID and IFSC are empty'
        WHEN (sol_id IS NULL OR sol_id = '') AND LENGTH(ifsc_code) < 4 THEN 'ERROR: SOL ID empty and IFSC too short'
        WHEN (sol_id IS NULL OR sol_id = '') THEN 'OK: Will use IFSC fallback'
        ELSE 'OK: Will use existing SOL ID'
    END as status,
    CASE 
        WHEN sol_id IS NOT NULL AND sol_id != '' THEN 'Normal flow'
        WHEN ifsc_code IS NOT NULL AND LENGTH(ifsc_code) >= 4 THEN 'Fallback flow'
        ELSE 'Error flow'
    END as expected_flow
FROM issue_check;

-- Test 6: Show what the API response would contain
\echo ''
\echo '📊 Test 6: Expected API behavior simulation'
\echo '------------------------------------------'
WITH final_result AS (
    SELECT 
        bc.actor_id,
        CASE 
            WHEN (bc.vendor_metadata->'federal_metadata'->>'sol_id') IS NOT NULL 
                 AND (bc.vendor_metadata->'federal_metadata'->>'sol_id') != ''
            THEN (bc.vendor_metadata->'federal_metadata'->>'sol_id')
            WHEN sa.ifsc_code IS NOT NULL AND LENGTH(sa.ifsc_code) >= 4
            THEN RIGHT(sa.ifsc_code, 4)
            ELSE NULL
        END as user_sol_id,
        CASE 
            WHEN (bc.vendor_metadata->'federal_metadata'->>'sol_id') IS NOT NULL 
                 AND (bc.vendor_metadata->'federal_metadata'->>'sol_id') != ''
            THEN 'BANK_CUSTOMER'
            WHEN sa.ifsc_code IS NOT NULL AND LENGTH(sa.ifsc_code) >= 4
            THEN 'IFSC_FALLBACK'
            ELSE 'ERROR'
        END as source,
        bc.vendor_customer_id,
        sa.ifsc_code,
        sa.account_no
    FROM bank_customers bc
    LEFT JOIN savings_accounts sa ON bc.actor_id = sa.actor_id AND sa.partner_bank = 'FEDERAL_BANK'
    WHERE bc.actor_id = :'actor_id' 
      AND bc.vendor = 'FEDERAL_BANK'
)
SELECT 
    'API Response Simulation' as test_name,
    actor_id,
    user_sol_id,
    source as sol_id_source,
    vendor_customer_id,
    account_no as savings_account_number,
    ifsc_code as savings_ifsc_code,
    CASE 
        WHEN user_sol_id IS NOT NULL 
        THEN 'SUCCESS: GST reporting will work'
        ELSE 'FAILURE: GST reporting will fail'
    END as expected_result
FROM final_result;

-- Summary
\echo ''
\echo '📋 Summary'
\echo '----------'
\echo 'This script tests the GST fallback logic by:'
\echo '1. Checking if the actor exists'
\echo '2. Verifying bank customer SOL ID availability'
\echo '3. Checking savings account IFSC code as fallback'
\echo '4. Simulating the fallback decision logic'
\echo '5. Identifying potential issues'
\echo '6. Showing expected API behavior'
\echo ''
\echo 'To test with a different actor, update the actor_id variable at the top.'
\echo '================================================'
