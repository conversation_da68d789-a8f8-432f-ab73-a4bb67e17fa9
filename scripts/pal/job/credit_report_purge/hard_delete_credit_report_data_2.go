package credit_report_purge

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"time"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/logger"
	palJobConfig "github.com/epifi/gamma/scripts/pal/config"
)

// HardDeleteFlattenedCreditReportDataJob2 job hard deletes flattened credit report data
// that has been soft-deleted before a particular time duration.
type HardDeleteFlattenedCreditReportData2Job struct {
	config       *palJobConfig.Config
	featureEngDb *gorm.DB
}

func NewHardDeleteFlattenedCreditReportData2Job(
	featureEngDb types.PostgresPGDB,
	config *palJobConfig.Config,
) *HardDeleteFlattenedCreditReportData2Job {
	return &HardDeleteFlattenedCreditReportData2Job{
		featureEngDb: featureEngDb,
		config:       config,
	}
}

type HardDeleteFlattenedCreditReportData2JobArgs struct {
	DeleteBatchSize int `json:"deleteBatchSize"`
	NoOfLoops       int `json:"noOfLoops"`
}

// HARD_DELETE_FLATTENED_CREDIT_REPORT_DATA_2
// {"noOfLoops": 5000, "deleteBatchSize": 1000}

func (p *HardDeleteFlattenedCreditReportData2Job) GetArgs() interface{} {
	return &HardDeleteFlattenedCreditReportData2JobArgs{}
}

// nolint:funlen, gocritic, govet, gosec
func (p *HardDeleteFlattenedCreditReportData2Job) Run(ctx context.Context, args ...interface{}) error {
	jobArgs := args[0].(*HardDeleteFlattenedCreditReportData2JobArgs)
	err := p.validateJobArgs(ctx, jobArgs)
	if err != nil {
		return fmt.Errorf("error validating job args : %w", err)
	}
	logger.Info(ctx, "starting hard deletion of flattened credit report data 2.",
		zap.Int("noOfLoops", jobArgs.NoOfLoops),
		zap.Int("deleteBatchSize", jobArgs.DeleteBatchSize),
	)
	var (
		entriesProcessedPerTable = map[string]int{}
	)

	maxNoOfLoops := jobArgs.NoOfLoops

	batchNum := 0
	for _, table := range flattenCreditReportTables {
		for ; batchNum < maxNoOfLoops; batchNum++ {
			if batchNum == 0 && jobArgs.DeleteBatchSize == 1 {
				result := p.featureEngDb.Unscoped().Exec(`EXPLAIN ANALYSE
				DELETE
				FROM credit_account_histories
				WHERE id IN (SELECT id FROM credit_account_histories WHERE deleted_at IS NOT NULL LIMIT 1)`)
				if result.Error != nil {
					logger.Error(ctx, "error analyse delete hard deleted credit report data: %w", zap.Error(result.Error))
				}
				logger.Info(ctx, "analyse delete hard deleted credit report data",
					zap.Any("result", result))

				result = p.featureEngDb.Unscoped().Exec(`EXPLAIN
					DELETE
					FROM credit_account_histories
					WHERE id IN (SELECT id FROM credit_account_histories WHERE deleted_at IS NOT NULL LIMIT 1);`)
				if result.Error != nil {
					logger.Error(ctx, "error explain delete hard deleted credit report data: %w", zap.Error(result.Error))
				}
				logger.Info(ctx, "explain delete hard deleted credit report data",
					zap.Any("result", result))

				result = p.featureEngDb.Unscoped().Exec(`SELECT
					pg_column_size(credit_account_histories.*) AS total_row_size
					FROM credit_account_histories
					LIMIT 10`)
				if result.Error != nil {
					logger.Error(ctx, "error column size delete hard deleted credit report data: %w", zap.Error(result.Error))
				}
				logger.Info(ctx, "column size delete delete hard deleted credit report data",
					zap.Any("result", result))
			}
			// Deleted at is not null only when the row is soft-deleted.
			// Records are soft-deleted after SoftDeletionMinAge (config value used by soft-deletion job) duration.
			deleteQuery := fmt.Sprintf(`DELETE FROM %s WHERE id IN (SELECT id FROM %s WHERE deleted_at IS NOT NULL LIMIT %d)`, table, table, jobArgs.DeleteBatchSize)
			dbResult := p.featureEngDb.Unscoped().Exec(deleteQuery)
			if dbResult.Error != nil {
				logger.Error(ctx, fmt.Sprintf("error while hard deleting data for %s table", table),
					zap.Error(dbResult.Error),
					zap.Int("batchNum", batchNum+1),
					zap.Any("tableWiseDeletedEntriesCount", entriesProcessedPerTable))
				return fmt.Errorf("error while hard deleting data for %s table : %w", table, dbResult.Error)
			}
			entriesProcessedPerTable[table] += int(dbResult.RowsAffected)
			logger.InfoNoCtx(fmt.Sprintf("batchNum %d completed for table %s. Rows deleted in batchNum: %d, Total rows deleted for table -> %s : %d", batchNum+1, table, dbResult.RowsAffected, table, entriesProcessedPerTable[table]), zap.String("table", table))
			if dbResult.RowsAffected == 0 {
				break
			}
			// Creating a gap between DB queries to achieve the following benefits:
			// Reduce DB load spikes, allow other queries room to access the table &
			// let autovacuum catch up thus preventing table bloat
			time.Sleep(500 * time.Millisecond)
		}
	}
	logger.Info(ctx, "successfully hard deleted entries from all tables",
		zap.Any("tableWiseDeletedEntriesCount", entriesProcessedPerTable))
	return nil
}

func (p *HardDeleteFlattenedCreditReportData2Job) validateJobArgs(_ context.Context, jobArgs *HardDeleteFlattenedCreditReportData2JobArgs) error {
	if jobArgs.NoOfLoops <= 0 {
		return fmt.Errorf("no of loops cannot be smaller than 1")
	}
	if jobArgs.DeleteBatchSize <= 0 {
		return fmt.Errorf("delete batch size cannot be smaller than 1")
	}

	return nil
}
