// nolint : goconst
package job

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/preapprovedloan"
)

// mapLSEToFunnelStep maps a loan step execution to a loan funnel step and status
// Considers vendor, loan program, and specific step details

// doc link: https://docs.google.com/spreadsheets/d/1vDm6MIR2JH2aIG3zrVssL0GEFwFLynHU1WokN8vNgYA/edit?gid=1698338970#gid=1698338970
func mapLSEToFunnelStep(
	lse *preapprovedloan.LoanStepExecution,
	ownership commontypes.Ownership,
	loanProgram preapprovedloan.LoanProgram,
) (string, string, string, string, string) {
	// Default values
	step := StepUnknown
	status := StatusUnknown
	userAction := ""
	dsaAction := ""
	troubleshootingTip := ""

	// Map the status using switch
	switch lse.GetStatus() {
	case preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:
		status = StatusSuccess
	case preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED:
		status = StatusFailed
	case preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_EXPIRED:
		status = StatusExpired
	default:
		status = StatusInProgress
	}

	// Map step based on ownership and loan program
	switch ownership {
	case commontypes.Ownership_EPIFI_TECH_V2:
		switch loanProgram {
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_ELIGIBILITY:
			switch {
			case lse.GetStepName() == preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_INCOME_ESTIMATION:
				step = StepOfferGeneration
				userAction = "Connect Salary/primary bank account"
				dsaAction = "Ask user to connect their salary/primary account"
				troubleshootingTip = "This is standard Account aggregator flow - user discovers the accounts connected to their phone no, selects the account and consents to pull and share the data of that bank account"
			case lse.GetStepName() == preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CIBIL_REPORT_FETCH:
				step = StepOfferGeneration
				userAction = "Give consent (CIBIL)"
				dsaAction = "Ask user to provide consent and OTP"
				troubleshootingTip = "This doesn't affect their CIBIL score"
			case lse.GetStepName() == preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EXPERIAN_REPORT_FETCH:
				step = StepOfferGeneration
				userAction = "Give consent (EXPERIAN)"
				dsaAction = "Ask user to provide consent and OTP"
				troubleshootingTip = "This doesn't affect their Experian score"
			case lse.GetGroupStage() == preapprovedloan.GroupStage_GROUP_STAGE_DATA_COLLECTION:
				step = StepEligibilityFLOW
				userAction = "Fill basic details (PAN/DOB/employment/pincode)"
				dsaAction = "Speak to the user and ask them to proceed"
				troubleshootingTip = "User can find PAN in ITR notifications Look for the following email in your inbox or SMS header.\n\nEmail :\<EMAIL>\<EMAIL>\nSMS header :\nITDCPC"
			case lse.GetGroupStage() == preapprovedloan.GroupStage_GROUP_STAGE_OFFER_CREATION:
				step = StepOfferGeneration
				userAction = "Offer generation in progress, please wait"
				dsaAction = "Offer generation in progress, please wait"
			}
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
			switch lse.GetStepName() {
			case preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_ADD_DETAILS:
				step = StepWebEligibilityFLOW
				userAction = "Fill basic details (PAN/DOB/employment)"
				dsaAction = "Speak to the user and ask them to proceed"
				troubleshootingTip = "User can find PAN in ITR notifications Look for the following email in your inbox or SMS header.\n\nEmail :\<EMAIL>\<EMAIL>\nSMS header :\nITDCPC"
			case preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_OFFER_GENERATION:
				step = StepWebOfferGeneration
				userAction = "user rejected"
				if status == StatusSuccess {
					userAction = "Ask user to install app"
					dsaAction = "Ensure user is installing the app from the download CTA for proper attribution"
					troubleshootingTip = "In case the link is taking user to incorrect store(app store for android or playstore for ios), provide the short link to the user to install"
				}

			}
		}

	case commontypes.Ownership_FEDERAL_BANK:
		switch loanProgram {
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			switch lse.GetGroupStage() {
			case preapprovedloan.GroupStage_GROUP_STAGE_PRE_KFS:
				step = StepApplicationStarted
			case preapprovedloan.GroupStage_GROUP_STAGE_AUTH:
				step = StepKYC
				dsaAction = "Ask user to complete the liveness check"
			case preapprovedloan.GroupStage_GROUP_STAGE_CHECK_BRE:
				step = StepVendorOffer
			case preapprovedloan.GroupStage_GROUP_STAGE_RISK:
				step = StepVendorOffer
				dsaAction = "User can get rejected here due to risk checks"
			case preapprovedloan.GroupStage_GROUP_STAGE_KFS:
				step = StepVendorOffer
				userAction = "Complete esign"
				dsaAction = "Ask user to provide Aadhaar no and OTP & esign the KFS"
				troubleshootingTip = "KFS signing would require user to provide location access to the app and the browser"
			case preapprovedloan.GroupStage_GROUP_STAGE_DISBURSAL:
				step = StepDisbursal
			}
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB:
			switch lse.GetGroupStage() {
			case preapprovedloan.GroupStage_GROUP_STAGE_OFFER_CREATION:
				step = StepVendorOffer
				userAction = "User rejected by vendor"
				if status == StatusSuccess {
					userAction = "Select the loan details"
					dsaAction = "Ask user to enter PAN, Address details and select the loan amount as per needs"
					troubleshootingTip = "Federal provides one of the competitive interest rates, this is probably one of the best loan offer a user can get\n-Federal does include broken period interest in the net disbursal, so ask user to select loan amount accordingly"
				}
			case preapprovedloan.GroupStage_GROUP_STAGE_KYC:
				step = StepMandate
				userAction = "Complete eKYC & Mandate & esign"
				dsaAction = "Ask user to provide Aadhaar no and OTP, post that user has to complete mandate setup and then esign the KFS"
				troubleshootingTip = "KFS signing would require user to provide location access to the app and the browser"
			case preapprovedloan.GroupStage_GROUP_STAGE_MANDATE:
				step = StepMandate
				userAction = "Complete eKYC & Mandate & esign"
				dsaAction = "Ask user to provide Aadhaar no and OTP, post that user has to complete mandate setup and then esign the KFS"
				troubleshootingTip = "KFS signing would require user to provide location access to the app and the browser"
			case preapprovedloan.GroupStage_GROUP_STAGE_PRE_KFS:
				step = StepMandate
				userAction = "Complete eKYC & Mandate & esign"
				dsaAction = "Ask user to provide Aadhaar no and OTP, post that user has to complete mandate setup and then esign the KFS"
				troubleshootingTip = "KFS signing would require user to provide location access to the app and the browser"
			case preapprovedloan.GroupStage_GROUP_STAGE_E_SIGN:
				step = StepMandate
				userAction = "Complete eKYC & Mandate & esign"
				dsaAction = "Ask user to provide Aadhaar no and OTP, post that user has to complete mandate setup and then esign the KFS"
				troubleshootingTip = "KFS signing would require user to provide location access to the app and the browser"
			case preapprovedloan.GroupStage_GROUP_STAGE_VKYC:
				step = StepKYC
				userAction = "Complete vKYC"
				dsaAction = "Ask user to complete the VKYC"
				troubleshootingTip = "Agent timings are:\n-Ask user to be ready with physical PAN card\nEnsure user is in a wel lit background with no disturbance"
			case preapprovedloan.GroupStage_GROUP_STAGE_LOAN_ACCOUNT_CREATION:
				step = StepDisbursal
				userAction = "Disbursed"

			case preapprovedloan.GroupStage_GROUP_STAGE_DISBURSAL:
				step = StepDisbursal
			}
		}

	case commontypes.Ownership_MONEYVIEW_PL:
		switch loanProgram {
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN:
			switch lse.GetGroupStage() {
			case preapprovedloan.GroupStage_GROUP_STAGE_ONBOARDING:
				step = StepApplicationStarted

			case preapprovedloan.GroupStage_GROUP_STAGE_VENDOR_PWA:
				if details := lse.GetDetails(); details != nil {
					if vendorPwaData := details.GetVendorPwaStagesStepData(); vendorPwaData != nil {
						stageInfos := vendorPwaData.GetStageInfos()
						if len(stageInfos) > 0 {
							latestStage := stageInfos[len(stageInfos)-1].GetStageName()
							switch latestStage {
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_USER_REGISTRATION,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_APPLICATION_SUBMISSION,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_APPLICATION_RESUBMISSION:
								step = StepApplicationStarted
								userAction = "Provide required details(name, marital status, employment type,Income and consents)"
								dsaAction = "Ask user to provide relevant details to get the final offer from Lender"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_OFFER_SELECTION,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_OFFER_REFRESH,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_OFFER_RECEIVED,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_OFFER_GENERATION:
								step = StepVendorOffer
								userAction = "Select the offer and proceed"
								dsaAction = "Ask user to select loan offer as per needs"
								troubleshootingTip = "In case user is not satisfied with this offer, they can go back to loan dashboard and see if they have some other offers. Please note, hard pull has already happened for the user"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_INCOME_VERIFICATION:
								step = StepVendorOffer
								userAction = "Ignore"
								dsaAction = "Ignore"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_COMPLIANCE_REVIEW,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_KYC:
								step = StepKYC
								userAction = "Complete KYC"
								dsaAction = "Ask user to complete the KYC"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_ADD_BANK_DETAILS,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_AGREEMENT_SIGNING,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_MANDATE:
								step = StepMandate
								userAction = "Complete mandate and do e-sign post that"
								dsaAction = "Ask user to complete the mandate and do e-sign post that"
								troubleshootingTip = "User can be in manual review stage from lender side post KYC and before they are allowed to set up a mandate"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_DISBURSAL:
								step = StepDisbursal
							}
						}
					}
				}
			case preapprovedloan.GroupStage_GROUP_STAGE_LOAN_ACCOUNT_CREATION:
				step = StepDisbursal
			}
		case preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			switch lse.GetGroupStage() {
			case preapprovedloan.GroupStage_GROUP_STAGE_ONBOARDING:
				step = StepApplicationStarted
			case preapprovedloan.GroupStage_GROUP_STAGE_VENDOR_PWA:
				if details := lse.GetDetails(); details != nil {
					if vendorPwaData := details.GetVendorPwaStagesStepData(); vendorPwaData != nil {
						stageInfos := vendorPwaData.GetStageInfos()
						if len(stageInfos) > 0 {
							latestStage := stageInfos[len(stageInfos)-1].GetStageName()
							switch latestStage {
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_USER_REGISTRATION,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_APPLICATION_RESUBMISSION:
								step = StepApplicationStarted
								userAction = "Provide required details(name, marital status, employment type,Income and consents)"
								dsaAction = "Ask user to provide relevant details to get the final offer from Lender"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_APPLICATION_SUBMISSION:
								step = StepApplicationStarted
								userAction = "Provide required details(name, marital status, employment type,Income and consents)"
								dsaAction = "Ask user to provide relevant details to get the final offer from Lender"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_OFFER_SELECTION,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_OFFER_REFRESH,
								preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_OFFER_RECEIVED:
								step = StepVendorOffer
								userAction = "Select the offer and proceed"
								dsaAction = "Ask user to select loan offer as per needs"
								troubleshootingTip = "In case user is not satisfied with this offer, they can go back to loan dashboard and see if they have some other offers. Please note, hard pull has already happened for the user"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_OFFER_GENERATION:
								step = StepVendorOffer
								userAction = "Select the offer and proceed"
								dsaAction = "Ask user to select loan offer as per needs"
								troubleshootingTip = "In case user is not satisfied with this offer, they can go back to loan dashboard and see if they have some other offers. Please note, hard pull has already happened for the user"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_INCOME_VERIFICATION:
								step = StepVendorOffer
								userAction = "Ignore"
								dsaAction = "Ignore"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_COMPLIANCE_REVIEW:
								step = StepKYC
								userAction = "Complete KYC"
								dsaAction = "Ask user to complete the KYC"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_KYC:
								step = StepKYC
								userAction = "Complete KYC"
								dsaAction = "Ask user to complete the KYC"

							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_ADD_BANK_DETAILS:
								step = StepMandate
								userAction = "Complete mandate and do e-sign post that"
								dsaAction = "Ask user to complete the mandate and do e-sign post that"
								troubleshootingTip = "User can be in manual review stage from lender side post KYC and before they are allowed to set up a mandate"

							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_AGREEMENT_SIGNING:
								step = StepMandate
								userAction = "Complete mandate and do e-sign post that"
								dsaAction = "Ask user to complete the mandate and do e-sign post that"
								troubleshootingTip = "User can be in manual review stage from lender side post KYC and before they are allowed to set up a mandate"
							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_MANDATE:
								step = StepMandate
								userAction = "Complete mandate and do e-sign post that"
								dsaAction = "Ask user to complete the mandate and do e-sign post that"
								troubleshootingTip = "User can be in manual review stage from lender side post KYC and before they are allowed to set up a mandate"

							case preapprovedloan.VendorPWAStagesStepData_PWA_STAGE_DISBURSAL:
								step = StepDisbursal
							}
						}
					}
				}
			case preapprovedloan.GroupStage_GROUP_STAGE_LOAN_ACCOUNT_CREATION:
				step = StepDisbursal
			}

		}

	case commontypes.Ownership_LOANS_STOCK_GUARDIAN_LSP:

		switch {
		case lse.GetStepName() == preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MANDATE:
			step = StepMandate
			userAction = "Set up the mandate"
			dsaAction = "Ask user to set up the mandate on bank account. In cases where AA was connected, the account would be same as the one where salary was detected"

		case lse.GetStepName() == preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_VKYC:
			step = StepKYC
			userAction = "Complete VKYC & esign"
			dsaAction = "Ask user to complete the VKYC & esign"
			troubleshootingTip = "Agent timings are: 10 am to 7PM, Mon-Sat\n-Ask user to be ready with physical PAN card\n-Ensure user is in a wel lit background with no disturbance"

		case lse.GetGroupStage() == preapprovedloan.GroupStage_GROUP_STAGE_ONBOARDING,
			lse.GetGroupStage() == preapprovedloan.GroupStage_GROUP_STAGE_AUTH,
			lse.GetGroupStage() == preapprovedloan.GroupStage_GROUP_STAGE_DRAWDOWN:
			step = StepApplicationStarted
			userAction = "Complete KYC via digilocker or CKYC"
			dsaAction = "Ask user to complete the KYC via digilocker preferably"
			troubleshootingTip = "Even if user doesn't have digilocker account, same can be created on the fly in the digilocker flow"

		case lse.GetGroupStage() == preapprovedloan.GroupStage_GROUP_STAGE_E_SIGN:
			step = StepKYC
			userAction = "Complete VKYC & esign"
			dsaAction = "Ask user to complete esign"
			troubleshootingTip = ""
		case lse.GetGroupStage() == preapprovedloan.GroupStage_GROUP_STAGE_DISBURSAL:
			step = StepDisbursal
		}

	case commontypes.Ownership_LOANS_LENDEN:
		if loanProgram == preapprovedloan.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION {
			switch lse.GetGroupStage() {
			case preapprovedloan.GroupStage_GROUP_STAGE_INIT_LOAN:
				step = StepApplicationStarted
			case preapprovedloan.GroupStage_GROUP_STAGE_KYC:
				step = StepKYC
				userAction = "Complete Digilocker and selfie"
				dsaAction = "Ask user to complete the digilocker and selfie"
			case preapprovedloan.GroupStage_GROUP_STAGE_MANDATE,
				preapprovedloan.GroupStage_GROUP_STAGE_E_SIGN:
				step = StepMandate
				userAction = "Setup mandate and esign"
				dsaAction = "Ask user to set up the mandate on bank account. In cases where AA was connected, the account would be same as the one where salary was detected"
			case preapprovedloan.GroupStage_GROUP_STAGE_DISBURSAL:
				step = StepDisbursal
			}
		}
	}

	return step, status, userAction, dsaAction, troubleshootingTip
}
