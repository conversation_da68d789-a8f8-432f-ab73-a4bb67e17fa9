//nolint:all
package job

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	leadPb "github.com/epifi/gamma/api/leads"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/leads/dao"
	"github.com/epifi/gamma/leads/dao/model"
	"github.com/epifi/gamma/scripts/pal/helper"
	"github.com/epifi/gamma/scripts/pal/job/vendors"
)

type RefreshUserLeadJob struct {
	dbResourceProvider  *storageV2.DBResourceProvider[*gorm.DB]
	helper              *helper.Helper
	userLeadDao         dao.UserLeadDao
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	palClient           palPb.PreApprovedLoanClient
}

func NewRefreshUserLeadJob(
	dbResourceProvider *storageV2.DBResourceProvider[*gorm.DB],
	helper *helper.Helper,
	userLeadDao dao.UserLeadDao,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	palClient palPb.PreApprovedLoanClient,
) *RefreshUserLeadJob {
	return &RefreshUserLeadJob{
		dbResourceProvider:  dbResourceProvider,
		helper:              helper,
		userLeadDao:         userLeadDao,
		txnExecutorProvider: txnExecutorProvider,
		palClient:           palClient,
	}
}

// Job Name - REFRESH_USER_LEAD
// jobArguments {"actorIds":["AC230307wQpyRyAJSJ+ii66ZKGsHcA==","AC230307wQpyRyAJSJ+ii3rZKGsHcA=="],"productType":"PRODUCT_TYPE_FI_PERSONAL_LOAN"}

// empty actor id will fetch all user leads in active state
type RefreshUserLeadJobArgs struct {
	ActorIds    []string           `json:"actorIds"`
	ProductType leadPb.ProductType `json:"productType"`
}

func (p *RefreshUserLeadJob) GetArgs() interface{} {
	return &RefreshUserLeadJobArgs{}
}

const (
	defaultWaitTime         = 1 * time.Second
	userLeadBatchSize       = 1000
	expiryExtensionDuration = 7 * 24 * time.Hour // 7 days
)

func (p *RefreshUserLeadJob) Run(ctx context.Context, args ...interface{}) error {
	jobArgs, ok := args[0].(*RefreshUserLeadJobArgs)
	if !ok {
		return errors.New("invalid job arguments type")
	}
	actorIds := jobArgs.ActorIds
	productType := jobArgs.ProductType

	if productType != leadPb.ProductType_PRODUCT_TYPE_FI_PERSONAL_LOAN {
		return fmt.Errorf("product type not supported for this job, productType: %v", productType)
	}

	ctx = epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH_V2)

	db, err := vendors.GetConnFromContextOrProvider(ctx, p.dbResourceProvider)
	if err != nil {
		return fmt.Errorf("error getting DB connection: %w", err)
	}

	query := db.WithContext(ctx).Where("completed_at IS NULL") // Assuming we only process leads not yet completed
	if len(actorIds) > 0 {
		query = query.Where("actor_id IN (?)", actorIds)
	}

	var (
		userLeads            []*model.UserLead
		failedLeadIdToErrMap map[string]error
		processedCount       int
	)
	failedLeadIdToErrMap = make(map[string]error)

	dbResults := query.Select("id,actor_id,created_at,expired_at").Where("product_type = ?", productType.String()).FindInBatches(&userLeads, userLeadBatchSize, func(tx *gorm.DB, batch int) error {
		logger.Info(ctx, "Processing batch of user leads", zap.Int("batch_size", len(userLeads)))

		for _, leadModel := range userLeads {
			err = p.processUserLead(ctx, leadModel)
			if err != nil {
				failedLeadIdToErrMap[leadModel.GetProto().GetId()] = err
				// Continue processing other leads in the batch
			}
			processedCount++
		}
		logger.Info(ctx, "Finished processing batch", zap.Int("batch_processed_count", len(userLeads)))
		time.Sleep(defaultWaitTime) // Consider if this sleep is necessary or configurable
		return nil                  // Return nil to continue processing batches even if some leads failed
	})

	if dbResults.Error != nil {
		return fmt.Errorf("failed during batch processing of user leads: %w", dbResults.Error)
	}

	for leadId, err := range failedLeadIdToErrMap {
		logger.Error(ctx, "Failed to process user lead",
			zap.String(logger.ID, leadId),
			zap.Error(err))
	}

	if len(failedLeadIdToErrMap) > 0 {
		return fmt.Errorf("failed to process %d user leads", len(failedLeadIdToErrMap))
	}
	logger.Info(ctx, "RefreshUserLeadJob completed successfully")
	return nil
}

// processUserLead handles the logic for refreshing a single user lead.
func (p *RefreshUserLeadJob) processUserLead(ctx context.Context, leadModel *model.UserLead) error {
	userLead := leadModel.GetProto()
	if userLead == nil {
		return errors.New("lead model returned nil proto")
	}
	actorID := userLead.GetActorId()

	if actorID == "" {
		if userLead.GetExpiredAt().AsTime().Before(timestampPb.Now().AsTime()) {
			// case for active application, active eligibility, offer available and eligible to apply will be handled by this
			userLead.LeadStatus = leadPb.UserLeadStatus_USER_LEAD_STATUS_EXPIRED
			userLead.CompletedAt = userLead.GetExpiredAt()
			fieldsToUpdate := []leadPb.UserLeadFieldMask{leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS,
				leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_COMPLETED_AT}
			updateErr := p.userLeadDao.Update(ctx, userLead, fieldsToUpdate)
			if updateErr != nil {
				// Returning error here will add it to failedActorIDs in the calling function
				return fmt.Errorf("failed to update user lead: %w", updateErr)
			}
		}
	}

	userStatusResp, err := p.palClient.GetLoanUserStatusV2(ctx, &palPb.GetLoanUserStatusV2Request{
		ActorId:        actorID,
		UpdatedAtAfter: userLead.GetCreatedAt(),
	})
	if te := epifigrpc.RPCError(userStatusResp, err); te != nil {
		// Returning error here will add it to failedActorIDs in the calling function
		return fmt.Errorf("failed to get loan user status: %w", te)
	}

	var (
		fieldsToUpdate []leadPb.UserLeadFieldMask
	)

	latestExpiryTs, err := getLatestLeadExpiryTime(userLead, userStatusResp)
	if err != nil {
		// Returning error here will add it to failedActorIDs in the calling function
		return fmt.Errorf("failed to get latest lead expiry time: %w", err)
	}
	userLead.ExpiredAt = latestExpiryTs
	if latestExpiryTs.AsTime().Before(timestampPb.Now().AsTime()) {
		// case for active application, active eligibility, offer available and eligible to apply will be handled by this
		userLead.LeadStatus = leadPb.UserLeadStatus_USER_LEAD_STATUS_EXPIRED
		userLead.CompletedAt = latestExpiryTs
		fieldsToUpdate = []leadPb.UserLeadFieldMask{leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS,
			leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_COMPLETED_AT,
			leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_EXPIRED_AT}
	}

	// Determine new status and expiry based on user status
	switch userStatusResp.GetUserStatus() {
	case palPb.UserStatus_USER_STATUS_ACTIVE_LOAN:
		disbursedTs := userStatusResp.GetLoanEventTimestamps().GetLatestLoanDisbursedTimestamp()
		if disbursedTs != nil && disbursedTs.AsTime().Before(userLead.GetExpiredAt().AsTime()) && disbursedTs.AsTime().After(userLead.GetCreatedAt().AsTime()) {
			userLead.LeadStatus = leadPb.UserLeadStatus_USER_LEAD_STATUS_CONVERTED
			userLead.CompletedAt = disbursedTs
		} else if disbursedTs != nil && disbursedTs.AsTime().After(userLead.GetExpiredAt().AsTime()) {
			userLead.LeadStatus = leadPb.UserLeadStatus_USER_LEAD_STATUS_EXPIRED
			userLead.CompletedAt = userLead.GetExpiredAt()
		}
		fieldsToUpdate = []leadPb.UserLeadFieldMask{leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS,
			leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_COMPLETED_AT,
			leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_EXPIRED_AT}
	case palPb.UserStatus_USER_STATUS_REJECTED:
		rejectionTs := userStatusResp.GetLoanEventTimestamps().GetLatestRejectionTimestamp()
		if rejectionTs != nil && rejectionTs.AsTime().After(userLead.GetExpiredAt().AsTime()) {
			userLead.LeadStatus = leadPb.UserLeadStatus_USER_LEAD_STATUS_EXPIRED
			userLead.CompletedAt = userLead.GetExpiredAt()
		} else {
			if rejectionTs == nil {
				rejectionTs = timestampPb.New(time.Now())
			}
			userLead.LeadStatus = leadPb.UserLeadStatus_USER_LEAD_STATUS_REJECTED
			userLead.CompletedAt = rejectionTs
		}
		fieldsToUpdate = []leadPb.UserLeadFieldMask{leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_LEAD_STATUS,
			leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_COMPLETED_AT,
			leadPb.UserLeadFieldMask_USER_LEAD_FIELD_MASK_EXPIRED_AT}
	}

	logger.Info(ctx, "Updating user lead status/expiry",
		zap.String(logger.ID, userLead.GetId()),
		zap.Any("fields", fieldsToUpdate))
	if len(fieldsToUpdate) != 0 {
		updateErr := p.userLeadDao.Update(ctx, userLead, fieldsToUpdate)
		if updateErr != nil {
			// Returning error here will add it to failedActorIDs in the calling function
			return fmt.Errorf("failed to update user lead: %w", updateErr)
		}
	}
	return nil
}

func getLatestLeadExpiryTime(userLead *leadPb.UserLead, loansLeadStatusResponse *palPb.GetLoanUserStatusV2Response) (*timestampPb.Timestamp, error) {
	timestamps := loansLeadStatusResponse.GetLoanEventTimestamps()

	var candidates []time.Time

	if userLead.GetExpiredAt() != nil {
		candidates = append(candidates, userLead.GetExpiredAt().AsTime())
	}
	if ts := timestamps.GetLatestEligibilityReqStartTimestamp(); ts != nil {
		candidates = append(candidates, ts.AsTime().Add(expiryExtensionDuration))
	}
	if ts := timestamps.GetLatestApplicationReqStartTimestamp(); ts != nil {
		candidates = append(candidates, ts.AsTime().Add(expiryExtensionDuration))
	}

	// if there is no timestamps right now, then we will assume the current time is the new expiry time,
	// Rejection state can be nil, if no user have created any loan data
	if len(candidates) == 0 {
		return nil, errors.New("no expiry time candidates found")
	}

	// Find the latest (max) time
	maxTime := candidates[0]
	for _, t := range candidates[1:] {
		if t.After(maxTime) {
			maxTime = t
		}
	}

	return timestampPb.New(maxTime), nil
}
