-- GST Fallback Logic Test Script
-- INSTRUCTIONS: Replace 'YOUR_ACTOR_ID_HERE' with your actual actor ID before running

-- ============================================================================
-- GST FALLBACK LOGIC TEST
-- ============================================================================

-- 🔧 CONFIGURATION: Replace this with your actor ID
-- Example: 'AC230206pfc2PlEFSayZjijTLKizOA=='
\set test_actor_id 'YOUR_ACTOR_ID_HERE'

\echo '🧪 GST Fallback Logic Test'
\echo '=========================='
\echo 'Actor ID: ' :test_actor_id
\echo ''

-- ============================================================================
-- TEST 1: Check Bank Customer SOL ID (Primary Source)
-- ============================================================================
\echo '🏦 TEST 1: Bank Customer SOL ID Check'
\echo '------------------------------------'

SELECT 
    '1. Bank Customer SOL ID' as test_name,
    actor_id,
    vendor_customer_id,
    (vendor_metadata->'federal_metadata'->>'sol_id') as current_sol_id,
    CASE 
        WHEN (vendor_metadata->'federal_metadata'->>'sol_id') IS NULL 
        THEN '❌ NULL - Will trigger fallback'
        WHEN (vendor_metadata->'federal_metadata'->>'sol_id') = ''
        THEN '❌ EMPTY - Will trigger fallback'
        ELSE '✅ AVAILABLE - Normal flow will be used'
    END as sol_id_status
FROM bank_customers 
WHERE actor_id = :'test_actor_id'
  AND vendor = 'FEDERAL_BANK';

-- ============================================================================
-- TEST 2: Check Savings Account IFSC Code (Fallback Source)
-- ============================================================================
\echo ''
\echo '💰 TEST 2: Savings Account IFSC Code Check (Fallback Source)'
\echo '------------------------------------------------------------'

SELECT 
    '2. Savings Account IFSC' as test_name,
    actor_id,
    account_no,
    ifsc_code,
    LENGTH(ifsc_code) as ifsc_length,
    CASE 
        WHEN LENGTH(ifsc_code) >= 4 
        THEN RIGHT(ifsc_code, 4)
        ELSE 'N/A'
    END as extracted_sol_id,
    CASE 
        WHEN ifsc_code IS NULL 
        THEN '❌ NULL - Fallback will fail'
        WHEN ifsc_code = ''
        THEN '❌ EMPTY - Fallback will fail'
        WHEN LENGTH(ifsc_code) < 4
        THEN '❌ TOO SHORT - Fallback will fail'
        ELSE '✅ VALID - Fallback will work'
    END as fallback_viability,
    state as account_state
FROM savings_accounts 
WHERE actor_id = :'test_actor_id'
  AND partner_bank = 'FEDERAL_BANK';

-- ============================================================================
-- TEST 3: Complete Fallback Logic Simulation
-- ============================================================================
\echo ''
\echo '🔄 TEST 3: Complete Fallback Logic Simulation'
\echo '---------------------------------------------'

WITH bank_customer_data AS (
    SELECT 
        actor_id,
        (vendor_metadata->'federal_metadata'->>'sol_id') as bank_sol_id,
        vendor_customer_id
    FROM bank_customers 
    WHERE actor_id = :'test_actor_id' 
      AND vendor = 'FEDERAL_BANK'
),
savings_account_data AS (
    SELECT 
        actor_id,
        ifsc_code,
        account_no,
        CASE 
            WHEN ifsc_code IS NOT NULL AND LENGTH(ifsc_code) >= 4
            THEN RIGHT(ifsc_code, 4)
            ELSE NULL
        END as ifsc_derived_sol_id
    FROM savings_accounts 
    WHERE actor_id = :'test_actor_id' 
      AND partner_bank = 'FEDERAL_BANK'
)
SELECT 
    '3. Fallback Logic Result' as test_name,
    COALESCE(bc.actor_id, sa.actor_id, :'test_actor_id') as actor_id,
    bc.bank_sol_id as primary_sol_id,
    sa.ifsc_code as fallback_ifsc,
    sa.ifsc_derived_sol_id as fallback_sol_id,
    -- This is the actual logic from the code
    CASE 
        WHEN bc.bank_sol_id IS NOT NULL AND bc.bank_sol_id != ''
        THEN bc.bank_sol_id
        WHEN sa.ifsc_derived_sol_id IS NOT NULL
        THEN sa.ifsc_derived_sol_id
        ELSE 'ERROR: No valid SOL ID'
    END as final_user_sol_id,
    CASE 
        WHEN bc.bank_sol_id IS NOT NULL AND bc.bank_sol_id != ''
        THEN '✅ SUCCESS: Normal flow (using bank customer SOL ID)'
        WHEN sa.ifsc_derived_sol_id IS NOT NULL
        THEN '✅ SUCCESS: Fallback flow (using IFSC last 4 chars)'
        WHEN bc.actor_id IS NULL
        THEN '❌ ERROR: Bank customer record not found'
        WHEN sa.actor_id IS NULL
        THEN '❌ ERROR: Savings account not found'
        WHEN sa.ifsc_code IS NULL OR sa.ifsc_code = ''
        THEN '❌ ERROR: IFSC code is empty'
        WHEN LENGTH(sa.ifsc_code) < 4
        THEN '❌ ERROR: IFSC code too short'
        ELSE '❌ ERROR: Unknown issue'
    END as flow_result
FROM bank_customer_data bc
FULL OUTER JOIN savings_account_data sa ON bc.actor_id = sa.actor_id;

-- ============================================================================
-- TEST 4: API Call Simulation
-- ============================================================================
\echo ''
\echo '📡 TEST 4: API Call Simulation'
\echo '------------------------------'

-- Simulate what GetSavingsAccountEssentials would return
SELECT 
    '4. GetSavingsAccountEssentials Response' as test_name,
    actor_id,
    account_no,
    ifsc_code,
    partner_bank,
    state,
    CASE 
        WHEN state = 'ACTIVE'
        THEN '✅ Account is active'
        ELSE '⚠️  Account state: ' || state
    END as account_status
FROM savings_accounts 
WHERE actor_id = :'test_actor_id'
  AND partner_bank = 'FEDERAL_BANK';

-- ============================================================================
-- TEST 5: Expected GST Reporting Data
-- ============================================================================
\echo ''
\echo '📊 TEST 5: Expected GST Reporting Data Structure'
\echo '------------------------------------------------'

WITH final_sol_id AS (
    SELECT 
        CASE 
            WHEN (SELECT (vendor_metadata->'federal_metadata'->>'sol_id') 
                  FROM bank_customers 
                  WHERE actor_id = :'test_actor_id' AND vendor = 'FEDERAL_BANK') IS NOT NULL
                 AND (SELECT (vendor_metadata->'federal_metadata'->>'sol_id') 
                      FROM bank_customers 
                      WHERE actor_id = :'test_actor_id' AND vendor = 'FEDERAL_BANK') != ''
            THEN (SELECT (vendor_metadata->'federal_metadata'->>'sol_id') 
                  FROM bank_customers 
                  WHERE actor_id = :'test_actor_id' AND vendor = 'FEDERAL_BANK')
            WHEN (SELECT ifsc_code FROM savings_accounts 
                  WHERE actor_id = :'test_actor_id' AND partner_bank = 'FEDERAL_BANK') IS NOT NULL
                 AND LENGTH((SELECT ifsc_code FROM savings_accounts 
                            WHERE actor_id = :'test_actor_id' AND partner_bank = 'FEDERAL_BANK')) >= 4
            THEN RIGHT((SELECT ifsc_code FROM savings_accounts 
                       WHERE actor_id = :'test_actor_id' AND partner_bank = 'FEDERAL_BANK'), 4)
            ELSE NULL
        END as user_sol_id
)
SELECT 
    '5. GST Reporting Data' as test_name,
    :'test_actor_id' as actor_id,
    user_sol_id,
    user_sol_id as location_code,  -- userSolId is used as locationCode
    CASE 
        WHEN user_sol_id IS NOT NULL
        THEN '✅ GST reporting will succeed'
        ELSE '❌ GST reporting will fail'
    END as gst_reporting_status,
    CASE 
        WHEN user_sol_id IS NOT NULL
        THEN 'userSolId and locationCode will be set to: ' || user_sol_id
        ELSE 'API will return error due to missing userSolId'
    END as expected_behavior
FROM final_sol_id;

-- ============================================================================
-- SUMMARY
-- ============================================================================
\echo ''
\echo '📋 SUMMARY'
\echo '=========='
\echo 'This test validates the GST fallback logic implementation:'
\echo '1. ✓ Checks primary SOL ID source (bank_customers.vendor_metadata.federal_metadata.sol_id)'
\echo '2. ✓ Checks fallback SOL ID source (savings_accounts.ifsc_code last 4 chars)'
\echo '3. ✓ Simulates the complete fallback decision logic'
\echo '4. ✓ Validates GetSavingsAccountEssentials API response'
\echo '5. ✓ Shows expected GST reporting data structure'
\echo ''
\echo 'To test with a different actor:'
\echo '1. Replace YOUR_ACTOR_ID_HERE at the top with the actual actor ID'
\echo '2. Re-run this script'
\echo ''
\echo 'Expected behavior:'
\echo '• If bank customer SOL ID exists → Use it (normal flow)'
\echo '• If bank customer SOL ID is empty → Use IFSC last 4 chars (fallback flow)'
\echo '• If both are unavailable → Return error'
