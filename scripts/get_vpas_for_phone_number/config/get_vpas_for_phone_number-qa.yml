Application:
  Environment: "qa"
  Name: "get_vpas_for_phone_number"

EpifiDb:
  AppName: "auth"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 50
  MaxIdleConn: 14
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

PspListForPhoneNumberPay:
  - "paytm"
  - "ybl"
  - "ibl"
  - "axl"

GooglePaySuffixList:
  - "okaxis"
  - "okhdfcbank"
  - "okicici"
  - "oksbi"
