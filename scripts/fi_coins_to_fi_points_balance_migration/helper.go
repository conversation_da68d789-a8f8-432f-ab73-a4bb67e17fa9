// nolint: gosec
package main

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/epifi/be-common/pkg/epificontext/gormctxv2"

	"gorm.io/gorm"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	storage "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/accrual"
	"github.com/epifi/gamma/accrual/dao"
	dm "github.com/epifi/gamma/accrual/dao/model"
	sm "github.com/epifi/gamma/accrual/model"
	accrualPb "github.com/epifi/gamma/api/accrual"
	fireflyPb "github.com/epifi/gamma/api/firefly"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

type Helper struct {
	db             *gorm.DB
	accountDao     dao.AccountDao
	txnExecutor    storage.TxnExecutor
	accrualService *accrual.Service
	fireflyClient  fireflyPb.FireflyClient
}

func NewHelper(
	db *gorm.DB,
	accountDao dao.AccountDao,
	txnExecutor storage.TxnExecutor,
	accrualService *accrual.Service,
	fireflyClient fireflyPb.FireflyClient,
) *Helper {
	return &Helper{
		db:             db,
		accountDao:     accountDao,
		txnExecutor:    txnExecutor,
		accrualService: accrualService,
		fireflyClient:  fireflyClient,
	}
}

func (h *Helper) MigrateFiCoinsToFiPoints(ctx context.Context) error {
	var (
		accountsMigrated = 0
		errorCount       = 0
	)
	for {
		if errorCount >= 100 {
			return fmt.Errorf("error count reached 100, stopping migration, please check logs for error and retry")
		}

		accounts, err := h.getEligibleFiCoinsAccountsToMigrate(ctx, int(*pageSize))
		if err != nil {
			logger.Error(ctx, "failed to get eligible fi coins accounts", zap.Error(err))
			errorCount++
			continue
		}
		if len(accounts) == 0 {
			logger.Info(ctx, "no more accounts to migrate", zap.Int("accountsMigrated", accountsMigrated))
			break
		}

		for _, account := range accounts {
			logger.Info(ctx, "migrating account", zap.String("accountId", account.ID))
			err := h.migrateAccount(ctx, account)
			if err != nil {
				logger.Error(ctx, "failed to migrate account", zap.String("accountId", account.ID), zap.Error(err))
				errorCount++
				continue
			}
			accountsMigrated++
			time.Sleep(time.Duration(*sleepPerMigration) * time.Millisecond) // Sleep after each account migration
		}

		logger.Info(ctx, "processed page of accounts", zap.Int("accountsMigrated", accountsMigrated), zap.Int("errorCount", errorCount))
		time.Sleep(time.Duration(*sleepPerPage) * time.Millisecond)

		if *actorIds != "" {
			break
		}
	}

	return nil
}

func (h *Helper) getEligibleFiCoinsAccountsToMigrate(ctx context.Context, pageSize int) ([]*dm.AccrualAccount, error) {
	db := gormctxv2.FromContextOrDefault(ctx, h.db)

	db = db.Model(&dm.AccrualAccount{})
	// add account_type as AccountType_ACCOUNT_TYPE_FICOINS
	db = db.Where("account_type = ?", accrualPb.AccountType_ACCOUNT_TYPE_FICOINS)
	// available balance must be greater than zero
	db = db.Where("available_balance > ?", 0)
	// order by created_at to process oldest accounts first
	db = db.Order("created_at ASC")
	// limit max accounts to get in one query to 100
	db = db.Limit(pageSize)

	if *actorIds != "" {
		db = db.Where("actor_id IN (?)", sanitizeActorIds(*actorIds))
	}

	var accrualAccounts []*dm.AccrualAccount
	res := db.Select([]string{"id", "actor_id"}).Find(&accrualAccounts)
	if res.Error != nil {
		return nil, res.Error
	}
	return accrualAccounts, nil
}

func (h *Helper) migrateAccount(ctx context.Context, account *dm.AccrualAccount) error {
	actorId := account.ActorId
	fcAccountId := account.ID
	isCCActiveUser, err := accrualPkg.IsCCActiveUser(ctx, actorId, h.fireflyClient)
	if err != nil {
		return errors.Wrap(err, "failed to check if user is cc active user")
	}

	return h.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		// lock the FC account
		fcAccount, err := h.accountDao.GetByIdWithLock(txnCtx, fcAccountId)
		if err != nil {
			return errors.Wrap(err, "failed to lock fi coins account")
		}
		if fcAccount.IsBalanceDirty() {
			_, err = h.accrualService.ResolveBalanceExported(txnCtx, fcAccountId)
			if err != nil {
				return errors.Wrap(err, "failed to resolve balance for fi coins account")
			}
		}

		monthBalances, err := h.GetMonthlyBalances(txnCtx, fcAccountId)
		if err != nil {
			return errors.Wrap(err, "failed to get grouped monthly balance")
		}

		// fetch or create FP account
		fpAccount, err := h.accrualService.GetOrCreateAccount(txnCtx, &sm.AccountIdentifier{
			ActorId:     actorId,
			AccountType: accrualPb.AccountType_ACCOUNT_TYPE_FI_POINTS,
		})
		if err != nil {
			return errors.Wrap(err, "failed to find or create fi points account")
		}
		fpAccountId := fpAccount.AccountId

		for _, monthBalance := range monthBalances {
			var (
				startOfNextMonth = time.Date(monthBalance.Year, time.Month(monthBalance.Month), 1, 0, 0, 0, 0, datetime.IST).AddDate(0, 1, 0)

				debitTxnRefId  = getMigrationTxnRefId(fpAccountId, monthBalance.Year, monthBalance.Month, accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT)
				creditTxnRefId = getMigrationTxnRefId(fcAccountId, monthBalance.Year, monthBalance.Month, accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT)

				debitTxnAmount = monthBalance.Balance
				// Convert FC to FP points value
				creditTxnAmount = accrualPkg.ConvertFiCoinsToFiPoints(debitTxnAmount, isCCActiveUser)
			)

			// 1. Get latest FC Account balance
			fcAccount, err = h.accountDao.GetById(txnCtx, fcAccountId)
			if err != nil {
				return errors.Wrap(err, "failed to get fi coins account details")
			}

			// 2. Debit from FC Account (create transaction)
			_, err = h.accrualService.PerformTransaction(txnCtx, &sm.TransactRequest{
				AccountId:               fcAccountId,
				AccountAvailableBalance: fcAccount.AvailableBalance,
				TxnAmount:               monthBalance.Balance,
				TransactionType:         accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
				TransactionSubType:      accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_POINT_MIGRATION,
				RefId:                   debitTxnRefId,
			})
			if err != nil {
				return errors.Wrap(err, "failed to create debit transaction")
			}

			// 3. Credit to FP Account (create transaction)
			// Not passing AccountAvailableBalance here as we are not checking balance before crediting
			_, err = h.accrualService.PerformTransaction(txnCtx, &sm.TransactRequest{
				AccountId:          fpAccountId,
				TxnAmount:          creditTxnAmount,
				TransactionType:    accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT,
				TransactionSubType: accrualPb.TransactionSubType_TRANSACTION_SUB_TYPE_POINT_MIGRATION,
				RefId:              creditTxnRefId,
				BalanceExpiryTime:  &startOfNextMonth,
				MetaData: &accrualPb.TxnMetaData{
					CoinToPoints: &accrualPb.FiCoinsToFiPointsConversion{
						CoinsConverted: debitTxnAmount,
						IsCcUser:       isCCActiveUser,
					},
				},
			})
			if err != nil {
				return errors.Wrap(err, "failed to create credit transaction")
			}
		}

		_, err = h.accountDao.UpdateAccountDetails(txnCtx, &sm.UpdateAccountDetailsRequest{
			AccountId:                fcAccountId,
			AccountOperationalStatus: accrualPb.AccountOperationalStatus_ACCOUNT_OPERATIONAL_STATUS_CLOSED,
		})
		if err != nil {
			return errors.Wrap(err, "failed to close fi coins account")
		}

		return nil
	})
}

// MonthBalance represents the balance of a month for a given account.
type MonthBalance struct {
	Year    int
	Month   int
	Balance int32
}

func (h *Helper) GetMonthlyBalances(ctx context.Context, accountId string) ([]MonthBalance, error) {
	var (
		monthBalances []MonthBalance
		db            = gormctxv2.FromContextOrDefault(ctx, h.db)
		query         = `
			SELECT
				EXTRACT(YEAR FROM expiry_time AT TIME ZONE 'Asia/Kolkata') AS year,
				EXTRACT(MONTH FROM expiry_time AT TIME ZONE 'Asia/Kolkata') AS month,
				SUM(current_balance) AS balance
			FROM point_buckets
			WHERE account_id = ? AND status = ? AND current_balance > 0 AND expiry_time > ?
			GROUP BY year, month
		`
	)

	res := db.Raw(query, accountId, sm.AVAILABLE, accrualPkg.GetFiCoinsToFiPointsMigrationTime().In(time.UTC)).Scan(&monthBalances)
	if err := res.Error; err != nil {
		logger.Debug(ctx, "failed to get monthly balances", zap.Any(logger.RESPONSE, res), zap.Error(err))
		return nil, err
	}

	sort.Slice(monthBalances, func(i, j int) bool {
		if monthBalances[i].Year != monthBalances[j].Year {
			return monthBalances[i].Year < monthBalances[j].Year
		}
		return monthBalances[i].Month < monthBalances[j].Month
	})

	return monthBalances, nil
}

// getMigrationTxnRefId returns the ref id for the migration transaction.
// The ref id is in the following format:
// 1. Debit transaction (From FC to FP): MIGRATION-TO-<FI_POINTS_ACCOUNT_ID>-<year>-<month> : We will have the account id of the account to which we are transferring the balance
// 2. Credit transaction (From FP to FC): MIGRATION-FROM-<FI_COINS_ACCOUNT_ID>-<year>-<month> : We will have the account id of the account from which we are transferring the balance
func getMigrationTxnRefId(accountId string, year int, month int, transactionType accrualPb.TransactionType) string {
	switch transactionType {
	case accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT:
		// MIGRATION-TO-<FI_POINTS_ACCOUNT_ID>-<year>-<month>
		return fmt.Sprintf("MIGRATION-TO-%s-%d-%02d", accountId, year, month)
	case accrualPb.TransactionType_TRANSACTION_TYPE_CREDIT:
		// MIGRATION-FROM-<FI_COINS_ACCOUNT_ID>-<year>-<month>
		return fmt.Sprintf("MIGRATION-FROM-%s-%d-%02d", accountId, year, month)
	default:
		return ""
	}
}

func sanitizeActorIds(actorIds string) []string {
	actorIdsList := strings.Split(actorIds, ",")
	return lo.Map(actorIdsList, func(actorId string, _ int) string {
		return strings.TrimSpace(actorId)
	})
}
