package main

import (
	"context"
	"flag"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/accrual"
	"github.com/epifi/gamma/accrual/dao"
	fireflyPb "github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/scripts/fi_coins_to_fi_points_balance_migration/config"
)

var (
	pageSize          = flag.Uint("pageSize", 100, "Number of accounts to process in each batch")
	sleepPerMigration = flag.Uint("sleepPerMigration", 50, "Sleep duration after processing each account (for rate limiting, if needed)")
	sleepPerPage      = flag.Uint("sleepPerPage", 200, "Sleep duration after processing each page of accounts (for rate limiting, if needed")
	actorIds          = flag.String("actorIds", "", "Actor IDs to migrate, comma separated")
)

func main() {
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}

	db, err := storagev2.NewPostgresDBWithConfig(conf.AccrualDb, false)
	if err != nil {
		logger.Panic("failed to establish DB conn", zap.Error(err))
	}
	sqlDB, err := db.DB()
	if err != nil {
		logger.Panic("failed to get sql DB", zap.Error(err))
	}
	defer func() { _ = sqlDB.Close() }()

	ctx := context.Background()

	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	accountDao := dao.NewAccountDaoImpl(db, domainIdGenerator)
	txnExecutor := storagev2.NewGormTxnExecutor(db)
	transactionDaoImpl := dao.NewTransactionDaoImpl(db)
	pointBucketDaoImpl := dao.NewPointBucketDaoImpl(db)
	transactionSplitDaoImpl := dao.NewTransactionSplitDaoImpl(db)
	accrualService := accrual.NewService(txnExecutor, domainIdGenerator, accountDao, transactionDaoImpl, pointBucketDaoImpl, transactionSplitDaoImpl, true)

	fireflyConn := epifigrpc.NewConnByService(cfg.FIREFLY_SERVICE)
	defer epifigrpc.CloseConn(fireflyConn)
	fireflyClient := fireflyPb.NewFireflyClient(fireflyConn)

	helper := NewHelper(db, accountDao, txnExecutor, accrualService, fireflyClient)

	logger.Info(ctx, "----------------------------starting fi-coins to fi-points migration----------------------------")
	if err := helper.MigrateFiCoinsToFiPoints(ctx); err != nil {
		logger.PanicWithCtx(ctx, "error during migration", zap.Error(err))
	}
	logger.Info(ctx, "----------------------------fi-coins to fi-points migration completed successfully----------------------------")
}
