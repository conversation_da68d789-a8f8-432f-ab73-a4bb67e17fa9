package main

import (
	"context"
	"encoding/csv"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	accrualPb "github.com/epifi/gamma/api/accrual"
	rewardsPb "github.com/epifi/gamma/api/rewards"

	accrualPkg "github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/scripts/debit_fi_coins/config"
)

// main debits given number of fi coins from actors fi coins account
// args have to be provided in the following format:
// -actorIdList=ACnOiEmLsaRCSTVA3jdo9d/Q230928==,ACnOiEmLsaRCSTVA3jdo9d/Q230928== -fiCoinsList=50,25
// nolint:funlen,gosec
// todo (himanshu) use csv reader in case this list grows in size in future
func main() {
	actorIdListStr := flag.String("actorIdList", "", "comma separated list of actor ids")
	fiCoinsListStr := flag.String("fiCoinsList", "", "comma separated list of fi coins to be debited corresponding to each actor")
	flag.Parse()

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	logger.Init(env)
	defer func() {
		_ = logger.Log.Sync()
	}()

	conf, err := config.Load()
	if err != nil {
		logger.Panic("failed to load config", zap.Error(err))
	}

	// init accrual service client
	accrualConn := epifigrpc.NewConnByService(cfg.ACCRUAL_SERVICE)
	defer epifigrpc.CloseConn(accrualConn)
	accrualClient := accrualPb.NewAccrualClient(accrualConn)

	ctx, cancelFunc := context.WithTimeout(context.Background(), 15*time.Minute)
	defer cancelFunc()

	// check if fi coins to fi points migration is active
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		logger.Panic(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error())
	}

	var (
		actorIdList []string
		fiCoinsList []int
	)
	if len(*actorIdListStr) > 0 {
		actorIdList = lo.Map(strings.Split(*actorIdListStr, ","), func(actorId string, _ int) string {
			return strings.TrimSpace(actorId)
		})
		fiCoinsList = lo.Map(strings.Split(*fiCoinsListStr, ","), func(fiCoins string, _ int) int {
			fiCoinsInt, err := strconv.Atoi(strings.TrimSpace(fiCoins))
			if err != nil {
				logger.Panic("incorrect fi coins value", zap.String("fiCoins", fiCoins))
			}
			return fiCoinsInt
		})
	}

	// reading from CSV in case actorIdList is empty
	if len(actorIdList) == 0 {
		logger.Info(ctx, "no actor ID found in input, using csv (if available)", zap.String("csvFileName", conf.CsvFile))

		wd, err := os.Getwd()
		if err != nil {
			logger.Panic("error while trying to fetch current working directory", zap.Error(err))
		}
		csvFilePath := filepath.Join(wd, "config", conf.CsvFile)
		data, readErr := readFromCsvFile(csvFilePath)
		if readErr != nil {
			logger.Panic("error while reading csv file", zap.Error(readErr))
		}
		for _, row := range data[1:] {
			if len(row[0]) == 0 || len(row[1]) == 0 {
				logger.Error(ctx, "incorrect data encountered in CSV, skipping row", zap.String("actorID", row[0]), zap.String("fiCoins", row[1]))
			}
			fiCoinsInt, parseErr := strconv.Atoi(strings.TrimSpace(row[1]))
			if parseErr != nil {
				logger.Error(ctx, "incorrect fi coins value, skipping row", zap.String("actorID", row[0]), zap.String("fiCoins", row[1]))
			}
			actorIdList = append(actorIdList, strings.TrimSpace(row[0]))
			fiCoinsList = append(fiCoinsList, fiCoinsInt)
		}
	}

	if len(actorIdList) != len(fiCoinsList) {
		logger.Panic(fmt.Sprintf("length of actor ids: %d and fi coins to be debited: %d is not the same", len(actorIdList), len(fiCoinsList)), zap.Any("actorIds", actorIdList), zap.Any("fiCoinsToBeDebits", fiCoinsList))
	}

	successCount := 0
	failureCount := 0
	totalFiCoinsDeducted := uint64(0)
	for idx, actorId := range actorIdList {
		processingRef := fmt.Sprintf("MANUAL-DEBIT-DUPL-RWRD-%s-%s", actorId, time.Now().Month().String())
		logger.Info(ctx, fmt.Sprintf("debiting fi coins: %d for actor: %s with processing ref: %s", fiCoinsList[idx], actorId, processingRef))

		// perform fi coins debit transaction of respective amount
		req := &accrualPb.TransactRequest{
			RequestRefId:    processingRef,
			ActorId:         actorId,
			Amount:          int32(fiCoinsList[idx]),
			AccountType:     accrualPkg.FetchAccrualAccountType(rewardsPb.RewardType_FI_COINS, timestamppb.Now()),
			TransactionType: accrualPb.TransactionType_TRANSACTION_TYPE_DEBIT,
			Options: &accrualPb.TransactRequest_Options{
				IsPendingDebitAllowed: false,
			},
		}
		transactRes, err := accrualClient.Transact(ctx, req)
		if rpcErr := epifigrpc.RPCError(transactRes, err); rpcErr != nil {
			logger.Error(ctx, "error while performing debit txn for actor", zap.String(logger.REFERENCE_ID, processingRef), zap.String(logger.ACTOR_ID_V2, actorId), zap.Int("fi_coins", fiCoinsList[idx]), zap.Error(rpcErr))
			failureCount++
			continue
		}
		switch transactRes.GetTransactionStatus() {
		case accrualPb.TransactionStatus_TRANSACTION_STATUS_COMPLETED, accrualPb.TransactionStatus_TRANSACTION_STATUS_PENDING:
			logger.Info(ctx, "successfully performed debit txn for actor", zap.String(logger.REFERENCE_ID, processingRef), zap.String(logger.ACTOR_ID_V2, actorId), zap.Int("fi_coins", fiCoinsList[idx]))
			successCount++
			totalFiCoinsDeducted += uint64(fiCoinsList[idx])
		default:
			logger.Error(ctx, "error while performing debit txn for actor", zap.String(logger.REFERENCE_ID, processingRef), zap.String(logger.ACTOR_ID_V2, actorId), zap.Int("fi_coins", fiCoinsList[idx]))
			failureCount++
		}

		// sleep for 1 second after 100 requests
		if idx%100 == 0 {
			time.Sleep(time.Second)
			logger.Info(ctx, "completed scan of 100 entries", zap.Int("totalRowsScanned", idx), zap.Int("successCount", successCount), zap.Int("failureCount", failureCount))
		}
	}

	logger.Info(ctx, "script completed successfully", zap.Int("successCount", successCount), zap.Int("failureCount", failureCount), zap.Uint64("totalFiCoinsDeducted", totalFiCoinsDeducted))
}

// nolint:gosec
func readFromCsvFile(csvFileName string) ([][]string, error) {
	csvFile, err := os.Open(csvFileName)
	if err != nil {
		return nil, fmt.Errorf("error opening file: %s, err: %w", csvFileName, err)
	}
	defer func() {
		_ = csvFile.Close()
	}()

	csvReader := csv.NewReader(csvFile)

	records, err := csvReader.ReadAll()
	if err != nil {
		return nil, fmt.Errorf("error reading file: %w", err)
	}

	return records, nil
}
