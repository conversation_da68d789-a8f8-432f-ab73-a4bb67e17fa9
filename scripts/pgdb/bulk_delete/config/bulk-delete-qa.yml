Application:
  Environment: "qa"
  Name: "bulk-delete"

VendorMappingDb:
  AppName: "vendormapping"
  StatementTimeout: 1s
  Name: "vendormapping"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"

FitttDb:
  AppName: "fittt"
  StatementTimeout: 1s
  Name: "fittt"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"


BulkDeleteTestDb:
  AppName: "vendormapping"
  StatementTimeout: 5m
  Name: "bulk_delete_test"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Secrets:
  Ids:
    VendorMappingDbUsernamePassword: "qa/rds/postgres/vendormapping"
    FitttDbUsernamePassword: "qa/rds/postgres/fittt"

Aws:
  Region: "ap-south-1"

Tables:
  - DBName: "vendormapping"
    TableName: "dp_vendor_mappings"
    PurgeDataDuration: "168h" # 7 days
    BatchDelay: "30s"
    DeleteBatchSize: 100
    MaxBatchIter: 1
    PurgeDataSelector: "prospect_id not like 'onb-%'"
    ColumnName: "prospect_id"
    MaxRunDuration: "3h"
  - DBName: "fittt"
    TableName: "events"
    PurgeDataDuration: "168h" # 7 days
    BatchDelay: "30s"
    DeleteBatchSize: 50
    MaxBatchIter: 100
    PurgeDataSelector: "updated_at < '2023-02-25 10:50:08.984454+00'"
    ColumnName: "id"
    MaxRunDuration: "3h"
