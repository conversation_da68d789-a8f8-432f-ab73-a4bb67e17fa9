Application:
  Environment: "test"
  Name: "dispute_daily_dump"

EpifiDb:
  AppName: "cx"
  StatementTimeout: 5s
  Name: "sherlock"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbUsernamePassword: "{\"username\": \"root\", \"password\": \"\"}"

DisputeCreateTicketPublisher:
  QueueName: "cx-dispute-create-ticket-queue"


