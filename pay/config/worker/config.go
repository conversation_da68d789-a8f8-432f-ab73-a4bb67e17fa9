package worker

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	cfgV2 "github.com/epifi/be-common/pkg/cfg/v2"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/pay/pgerrorcodes"

	orderConfig "github.com/epifi/gamma/order/config"
	payConfig "github.com/epifi/gamma/pay/config/server"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

var (
	_, b, _, _    = runtime.Caller(0)
	once          sync.Once
	configLoadErr error
	conf          *Config
)

const IFTReportsSlackBotOauthToken = "IFTReportsSlackBotOauthToken"

//go:generate conf_gen github.com/epifi/gamma/pay/config/worker Config
type Config struct {
	Server                           *cfg.ServerPorts
	RazorPayResponseCodesJson        string
	Application                      *cfg.TemporalWorkerApplication `dynamic:"true"`
	OffAppUpiApplication             *cfg.TemporalWorkerApplication `dynamic:"true"`
	BillpayApplication               *cfg.TemporalWorkerApplication `dynamic:"true"`
	WorkflowParamsList               cfgV2.WorkflowParamsList
	DefaultActivityParamsList        cfgV2.ActivityParamsList
	PausedWorkflowList               cfgV2.PausedWorkflowsList
	DbConfigMap                      cfg.DbConfigMap
	UsecaseDbConfigMap               cfg.UseCaseDbConfigMap
	AWS                              *cfg.AWS
	OrderUpdatePublisher             *cfg.SnsPublisher
	TxnDetailedStatusUpdatePublisher *cfg.SnsPublisher
	WorkflowUpdatePublisher          *cfg.SnsPublisher
	Tracing                          *cfg.Tracing
	Profiling                        *cfg.Profiling
	Secrets                          *cfg.Secrets
	ExecutionReportGenerationParams  *ExecutionReportGenerationParams
	InternationalFundTransfer        *InternationalFundTransfer `dynamic:"true"`
	FundTransferParams               *FundTransferParams
	OrderCacheConfig                 *orderConfig.OrderCacheConfig `dynamic:"true"`
	PgProgramToAuthSecretMap         map[string]*payConfig.PgProgramToAuthSecret
	PayOrderCacheConfig              *payConfig.PayOrderCacheConfig       `dynamic:"true"`
	PayTransactionCacheConfig        *payConfig.PayTransactionCacheConfig `dynamic:"true"`
	// LockRedisStore : Redis Store which can be used for locking related use cases
	LockRedisStore *cfg.RedisOptions
	// config which holds various SLAs for payment enquiry
	PaymentEnquiryParams *payPkg.PaymentEnquiryParams
	// flag to indicate whether to enable resource provider based db instance provision or whether to
	// go with a static db instance
	EnableEntitySegregation           bool                `dynamic:"true"`
	IsGSTReportedWithNewInvoiceNumber bool                `dynamic:"true"`
	PgParams                          *payConfig.PgParams `dynamic:"true"`
}

func loadConfig() (*Config, error) {
	confg := &Config{}

	koanf, _, err := cfg.LoadWorkerConfigUsingKoanf(testEnvConfigDir(epifitemporal.PayWorker), "", cfg.ServiceName(epifitemporal.PayWorker))
	if err != nil {
		return nil, err
	}

	if err = koanf.UnmarshalWithConf("", confg, cfg.DefaultUnmarshallingConfig(confg)); err != nil {
		return nil, err
	}

	dbOwnershipMap := confg.DbConfigMap.GetOwnershipToDbConfigMap()
	if err = updateDefaultConfig(confg, dbOwnershipMap); err != nil {
		return nil, fmt.Errorf("failed to update default values in the config: %w", err)
	}

	if err = readAndSetEnv(confg, dbOwnershipMap); err != nil {
		return nil, fmt.Errorf("failed to read and set env var: %w", err)
	}
	err = cfg.LoadAllSecretsV3(confg.PgProgramToAuthSecretMap, confg.Application.Environment, confg.AWS.Region)
	if err != nil {
		return nil, err
	}

	// TODO(Sundeep): Move the payment gateway error status mapping to vendorgateway after evaluating the changes.
	// The challenge with moving to VG currently is that, in pay service we still need to
	// do the reverse mapping to extract other info, hence it needs to accessed at both the places.
	err = pgerrorcodes.LoadPaymentGatewayErrorMappings(confg.RazorPayResponseCodesJson)
	if err != nil {
		return nil, fmt.Errorf("error in loading razorpay error mappings from json: %w", err)
	}

	return confg, nil
}

func Load() (*Config, error) {
	once.Do(func() {
		conf, configLoadErr = loadConfig()
	})

	if configLoadErr != nil {
		return nil, configLoadErr
	}

	return conf, nil
}

func testEnvConfigDir(worker epifitemporal.Worker) string {
	configPath := filepath.Join(b, "..", "..", "..", "..", "cmd", "worker", worker.GetDirectory(), "config")
	return configPath
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointsInConfigMap(dbOwnershipMap, dbServerEndpoint)
	var err error
	if _, err = cfg.LoadAndUpdateSecretValues(dbOwnershipMap, c.Secrets, c.Application.Environment, c.AWS.Region); err != nil {
		return fmt.Errorf("failed to load and update secret values %w", err)
	}

	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config, dbOwnershipMap map[commontypes.Ownership]*cfg.DB) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Namespace = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.HttpPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		cfg.OverwriteDbHost(dbOwnershipMap, val)
	}

	return nil
}

// ExecutionReportGenerationParams allow fetching workflows on the basis of different filters at regular intervals of ReportStalenessDuration
type ExecutionReportGenerationParams struct {
	ReportStalenessDuration time.Duration
}

type InternationalFundTransfer struct {
	DocumentsBucketName           string
	ForexRateReportSlackChannelId string
	// currently we report tcs using a reporting file
	ReportTcsChargesFromApi bool
	// flag controls if sof transactions analysis flow is on
	IsSofAnalysisFlowActive bool
	// flag controls if the gst reporting api flow is active
	// we should switch the flag only when all IFT workflows are either before the GST reporting stage or after the GST reporting status check stage
	IsGstReportingViaApiFlowActive bool
	SherlockHost                   string `dynamic:"true"`
	EnableFederalSherlock          bool   `dynamic:"true"`
	// This is the domain of the federal sherlock as part of IFT migration
	FederalSherlockHost string `dynamic:"true"`
}

type FundTransferParams struct {
	// map between sms type and the sms option version to be used
	SMSTypeToOptionVersionMap map[string]*cfg.SMSOptionVersion
	PaymentNotificationParams *PaymentNotificationParams
}

type PaymentNotificationParams struct {
	HistoricPaymentSupportedTill time.Duration
	TransactionDebitSuccess      *payPkg.NotificationTemplateParams
	TransactionFailed            *payPkg.NotificationTemplateParams
	TransactionReversed          *payPkg.NotificationTemplateParams
}

type PgProgramToAuthSecret struct {
	AuthParam      string `iam:"sm-read"`
	AuthParamValue string `field:"AuthParam"`
}
