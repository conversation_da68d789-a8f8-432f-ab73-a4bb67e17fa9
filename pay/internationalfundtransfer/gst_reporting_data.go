package internationalfundtransfer

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	rpcPb "github.com/epifi/be-common/api/rpc"
	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	iftPayloadPb "github.com/epifi/gamma/api/pay/payload"
	iftErrors "github.com/epifi/gamma/pay/internationalfundtransfer/errors"
	"github.com/epifi/be-common/pkg/logger"
	pkgMoney "github.com/epifi/be-common/pkg/money"
)

// nolint: dupl
func (s *Service) GetGSTReportingData(ctx context.Context, req *iftPb.GetGSTReportingDataRequest) (*iftPb.GetGSTReportingDataResponse, error) {
	wfReqs, err := s.GetWfReqsForWfClientReqIds(ctx, req.GetIftClientRequestIds())
	if err != nil {
		logger.Error(ctx, "error getting wf reqs for wf client req ids", zap.Error(err))
		return &iftPb.GetGSTReportingDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	gstData := make([]*iftPb.GstReportingDetails, 0)
	for _, wf := range wfReqs {
		gstReportingDataForTxn, err := s.getGSTReportingDataForTxn(ctx, wf)
		if err != nil {
			logger.Error(ctx, "error getting GST report data for txn, skipping", zap.Error(errors.Wrap(iftErrors.TxnDroppedFromGstReportingFileError, err.Error())), zap.String(logger.WORKFLOW_REQ_ID, wf.GetId()))
			continue
		}
		gstData = append(gstData, gstReportingDataForTxn...)
	}
	return &iftPb.GetGSTReportingDataResponse{Status: rpcPb.StatusOk(), ReportingDetails: gstData}, nil
}

// nolint: funlen
func (s *Service) getGSTReportingDataForTxn(ctx context.Context, wf *celestialPb.WorkflowRequest) ([]*iftPb.GstReportingDetails, error) {
	iftPayloadBytes := wf.GetPayload()
	iftPayload := &iftPayloadPb.InternationalFundTransferWorkflowPayload{}
	err := protojson.Unmarshal(iftPayloadBytes, iftPayload)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling ift for gst reporting payload: %w", err)
	}
	swiftTxnTime, err := s.getSwiftTxnTime(ctx, wf)
	if err != nil {
		return nil, fmt.Errorf("error getting SWIFT txn time for gst reporting: %w", err)
	}

	// to handle previous order which uses old logic
	gstChargedAmount := iftPayload.GetCurrencyExchangeDetails().GetGstChargedAmount()

	// todo(numan) remove this logic when old order are migrated
	// if min charge amount should be charged
	if pkgMoney.Compare(s.config.InternationalFundTransfer().GstReportingInfo().MinTaxableGstAmount, gstChargedAmount) > 0 {
		gstChargedAmount = s.config.InternationalFundTransfer().GstReportingInfo().MinTaxableGstAmount
	}
	// ref: https://docs.google.com/spreadsheets/d/1x3RPGD542SKyv4NVaV0t4wOBY2g5mp2K/edit#gid=*********
	txnAmount, err := pkgMoney.Sum(iftPayload.GetCurrencyExchangeDetails().GetRemitterAmount(), iftPayload.GetCurrencyExchangeDetails().GetGST())
	if err != nil {
		return nil, fmt.Errorf("error getting taxable value: %w", err)
	}
	gstDataModel, err := s.getGstDetails(ctx, &GstDetailsForSwiftTxn{
		Gst:           iftPayload.GetCurrencyExchangeDetails().GetGST(),
		TxnTime:       swiftTxnTime,
		ServiceAmount: iftPayload.GetCurrencyExchangeDetails().GetGstChargedAmount(),
		TxnAmount:     txnAmount,
		ActorId:       wf.GetActorId(),
	})
	if err != nil {
		return nil, err
	}
	gstReportingDetails := make([]*iftPb.GstReportingDetails, 0)
	for _, gstModel := range gstDataModel {
		gstReportingDetails = append(gstReportingDetails, &iftPb.GstReportingDetails{
			TxnId:     wf.GetId(),
			DataModel: gstModel,
			WfClientReqId: &workflowPb.ClientReqId{
				Id:     wf.GetClientReqId().GetId(),
				Client: wf.GetClientReqId().GetClient(),
			},
		})

	}
	return gstReportingDetails, nil
}
