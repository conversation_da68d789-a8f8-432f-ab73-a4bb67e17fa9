package sof

//go:generate mockgen -source=$PWD/override_params.go -destination=$PWD/mocks/override_params.go -package=mocks

import (
	"errors"
	"fmt"

	"github.com/google/wire"
	"golang.org/x/net/context"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/pay/internationalfundtransfer/sof/datafetcher"
	"github.com/epifi/gamma/pay/internationalfundtransfer/sof/migrator"
	"github.com/epifi/be-common/pkg/epifierrors"
)

var SofLimitOverrideParamsGeneratorWireSet = wire.NewSet(NewSofLimitOverrideParamsGenerator, wire.Bind(new(ISofLimitOverrideParamsGenerator), new(*SofLimitOverrideParamsGenerator)))

type ISofLimitOverrideParamsGenerator interface {
	GetSofLimitOverrideParams(ctx context.Context, actorId string) (*iftPb.SofLimitComputationOverrideParams, error)
}

type SofLimitOverrideParamsGenerator struct {
	sofMigrator                 migrator.ISofMigrator
	connectedAccountDataFetcher datafetcher.IConnectedAccountDataFetcher
	savingsAccountDateFetcher   datafetcher.ISavingsAccountDataFetcher
}

func NewSofLimitOverrideParamsGenerator(
	sofMigrator migrator.ISofMigrator,
	connectedAccountDataFetcher datafetcher.IConnectedAccountDataFetcher,
	savingsAccountDateFetcher datafetcher.ISavingsAccountDataFetcher,
) *SofLimitOverrideParamsGenerator {
	return &SofLimitOverrideParamsGenerator{
		sofMigrator:                 sofMigrator,
		connectedAccountDataFetcher: connectedAccountDataFetcher,
		savingsAccountDateFetcher:   savingsAccountDateFetcher,
	}
}

func (s *SofLimitOverrideParamsGenerator) GetSofLimitOverrideParams(ctx context.Context, actorId string) (*iftPb.SofLimitComputationOverrideParams, error) {
	sofDetails, err := s.sofMigrator.GetSofForActor(ctx, actorId)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to generate sof limit override params: %w", err)
	}

	// if sof is in manual override state or
	// sof is already manually verified, no limit override params are required
	if sofDetails.GetSofState() == iftPb.SofState_SOF_STATE_MANUAL_REVIEW_REQUIRED || isSofManualOverridden(sofDetails) {
		return nil, nil
	}

	var params *iftPb.SofLimitComputationOverrideParams
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound) || sofDetails.GetDocumentType() == iftPb.SOFDocumentType_SOF_DOCUMENT_TYPE_FI_ACCOUNT_STATEMENT:
		// we assume that for users with no sof document fi account statement will be used as the sof document later
		// in the ift flow. the actual eligibility of using fi statement was checked in GetNextSOFStep
		params, err = s.fetchFiAccountStatementOverrideParams(ctx, actorId, sofDetails.GetDocumentInfo().GetFiAccountDocInfo().GetFiSavingsAccountId())
	case sofDetails.GetDocumentType() == iftPb.SOFDocumentType_SOF_DOCUMENT_TYPE_CONNECTED_ACCOUNT_STATEMENT:
		params, err = s.fetchAaAccountStatementOverrideParams(ctx, sofDetails.GetDocumentInfo().GetAaAccountDocInfo().GetAaAccountId())
	default:
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to generate sof limit override params: %w", err)
	}
	return params, nil
}

func (s *SofLimitOverrideParamsGenerator) fetchFiAccountStatementOverrideParams(ctx context.Context, actorId string, accountId string) (*iftPb.SofLimitComputationOverrideParams, error) {
	var (
		balance *moneyPb.Money
		err     error
	)
	if accountId == "" {
		balance, err = s.savingsAccountDateFetcher.GetAccountBalanceForActor(ctx, actorId)
		if err != nil {
			return nil, fmt.Errorf("failed to get account balance for actor: %w", err)
		}
	} else {
		balance, err = s.savingsAccountDateFetcher.GetAccountBalance(ctx, actorId, accountId)
		if err != nil {
			return nil, fmt.Errorf("failed to get account balance for savings account %s: %w", accountId, err)
		}
	}
	return &iftPb.SofLimitComputationOverrideParams{
		AccountBalance: balance,
	}, nil
}

func (s *SofLimitOverrideParamsGenerator) fetchAaAccountStatementOverrideParams(ctx context.Context, aaAccountId string) (*iftPb.SofLimitComputationOverrideParams, error) {
	balance, err := s.connectedAccountDataFetcher.GetAccountBalance(ctx, aaAccountId)
	if err != nil {
		return nil, fmt.Errorf("failed to get connected account balance %s: %w", aaAccountId, err)
	}
	return &iftPb.SofLimitComputationOverrideParams{
		AccountBalance: balance,
	}, nil
}

func isSofManualOverridden(sofDetails *iftPb.SofDetails) bool {
	if sofDetails.GetLimitStrategies() == nil {
		return false
	}
	_, found := sofDetails.GetLimitStrategies()[iftPb.SOFLimitStrategy_SOF_LIMIT_STRATEGY_MANUAL_OVERRIDE.String()]
	return found
}
