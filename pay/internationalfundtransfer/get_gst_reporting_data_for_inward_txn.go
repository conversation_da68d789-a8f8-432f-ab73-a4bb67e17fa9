package internationalfundtransfer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/accounts"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
)

type GstDetailsForSwiftTxn struct {

	// represent the actual gst amount which is being charged by user
	Gst *money.Money

	// represent the amount which is being charged by user
	TxnTime *timestampPb.Timestamp

	// represent the amount on which gst is charged
	ServiceAmount *money.Money

	// represent the amount on which txn is made
	TxnAmount *money.Money

	// represent actorId of user
	ActorId string
}

// nolint: funlen
func (s *Service) getGstDetails(ctx context.Context, swiftTxn *GstDetailsForSwiftTxn) ([]*iftPb.GSTReportingDataModel, error) {
	valueOfGoods, err := moneyPb.ToString(swiftTxn.TxnAmount, PRECISION)
	if err != nil {
		logger.Error(ctx, "error converting value of goods to string")
		logger.SecureError(ctx, commonvgpb.Vendor_FEDERAL_BANK, "error converting value of goods to string", zap.Any(logger.AMOUNT, swiftTxn.TxnAmount))
		return nil, fmt.Errorf("error converting value of goods to string %w", err)
	}
	gstData := make([]*iftPb.GSTReportingDataModel, 0)

	// this state code is fetch according given state
	stateCode, err := s.getGstStateCodeForUser(ctx, swiftTxn.ActorId)
	if err != nil {
		logger.Error(ctx, "failed to get state code for actor", zap.String(logger.ACTOR_ID_V2, swiftTxn.ActorId))
		return nil, err
	}

	bankCustResp, err := s.bankCustProcessor.GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
		Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: swiftTxn.ActorId},
	})
	if err != nil {
		return nil, errors.Wrap(err, "failed to get bank customer")
	}
	userSolId := bankCustResp.GetBankCustomer().GetVendorMetadata().GetFederalMetadata().GetSolId()
	if len(userSolId) == 0 {
		// Fallback: Get IFSC code from GetSavingsAccountEssentials RPC
		savingsResp, err := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
			Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier{
				ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
					ActorId:     swiftTxn.ActorId,
					PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
				},
			},
		})
		if err := epifigrpc.RPCError(savingsResp, err); err != nil {
			logger.Error(ctx, "failed to get savings account essentials for actor", zap.String(logger.ACTOR_ID_V2, swiftTxn.ActorId), zap.Error(err))
			return nil, errors.Wrap(err, "failed to get savings account essentials")
		}

		ifscCode := savingsResp.GetAccount().GetIfscCode()
		if len(ifscCode) == 0 {
			return nil, errors.New("got empty IFSC code from savings account essentials")
		}

		userSolId = ifscCode[len(ifscCode)-4:]
		logger.Info(ctx, "using last 4 chars of IFSC code as userSolId fallback", zap.String(logger.ACTOR_ID_V2, swiftTxn.ActorId), zap.String("ifsc_code", ifscCode), zap.String("user_sol_id", userSolId))
	}
	// 'user state' is same as 'service provider state' then cgstAmount=0.5*gst, sgstAmount=0.5*gstAmount
	// else igstAmount = gstAmount
	if stateCode == s.config.InternationalFundTransfer().GstReportingInfo().ServiceProviderStateCode {
		// adding cgst entry
		gstData = append(gstData, &iftPb.GSTReportingDataModel{
			GstinOfFilingOrganisation: s.config.InternationalFundTransfer().GstReportingInfo().FilingOrganisation,
			TransactionTime:           swiftTxn.TxnTime,
			InvoiceType:               s.config.InternationalFundTransfer().GstReportingInfo().InvoiceType,
			LocationCode:              userSolId,
			InvoiceNumber:             s.config.InternationalFundTransfer().GstReportingInfo().InvoiceNumber,
			TxnAmount:                 swiftTxn.TxnAmount,
			TaxableAmount:             swiftTxn.ServiceAmount,
			InvoiceHsn:                s.config.InternationalFundTransfer().GstReportingInfo().Hsn,
			ValueOfGoodsSold:          valueOfGoods,
			GstPercent:                s.config.InternationalFundTransfer().GstReportingInfo().CgstPercentage,
			IgstAmount:                moneyPb.ZeroINR().GetPb(),
			// since half amount goes to cgst amount
			CgstAmount:               moneyPb.Multiply(swiftTxn.Gst, decimal.NewFromFloat(0.5)),
			SgstAmount:               moneyPb.ZeroINR().GetPb(),
			CessAmount:               moneyPb.ZeroINR().GetPb(),
			Pos:                      stateCode,
			ReverseCharge:            s.config.InternationalFundTransfer().GstReportingInfo().ReverseCharge,
			SectionName:              s.config.InternationalFundTransfer().GstReportingInfo().SectionName,
			IsSez:                    s.config.InternationalFundTransfer().GstReportingInfo().IsSez,
			ReceiverAccountNumber:    s.config.InternationalFundTransfer().CgstAccountNumber(),
			CgstAccountSolId:         s.config.InternationalFundTransfer().CgstAccountSolId(),
			CustomerId:               bankCustResp.GetBankCustomer().GetVendorCustomerId(),
			ServiceProviderStateCode: s.config.InternationalFundTransfer().GstReportingInfo().ServiceProviderStateCode,
			ServiceProviderSolId:     s.config.InternationalFundTransfer().GstReportingInfo().ServiceProviderSolId,
			UserSolId:                userSolId,
		}, &iftPb.GSTReportingDataModel{
			GstinOfFilingOrganisation: s.config.InternationalFundTransfer().GstReportingInfo().FilingOrganisation,
			TransactionTime:           swiftTxn.TxnTime,
			InvoiceType:               s.config.InternationalFundTransfer().GstReportingInfo().InvoiceType,
			LocationCode:              userSolId,
			InvoiceNumber:             s.config.InternationalFundTransfer().GstReportingInfo().InvoiceNumber,
			TxnAmount:                 swiftTxn.TxnAmount,
			TaxableAmount:             swiftTxn.ServiceAmount,
			InvoiceHsn:                s.config.InternationalFundTransfer().GstReportingInfo().Hsn,
			ValueOfGoodsSold:          valueOfGoods,
			Pos:                       stateCode,
			GstPercent:                s.config.InternationalFundTransfer().GstReportingInfo().SgstPercentage,
			IgstAmount:                moneyPb.ZeroINR().GetPb(),
			// since half amount go to sgst
			SgstAmount:               moneyPb.Multiply(swiftTxn.Gst, decimal.NewFromFloat(0.5)),
			CgstAmount:               moneyPb.ZeroINR().GetPb(),
			CessAmount:               moneyPb.ZeroINR().GetPb(),
			SectionName:              s.config.InternationalFundTransfer().GstReportingInfo().SectionName,
			ReverseCharge:            s.config.InternationalFundTransfer().GstReportingInfo().ReverseCharge,
			IsSez:                    s.config.InternationalFundTransfer().GstReportingInfo().IsSez,
			ReceiverAccountNumber:    s.config.InternationalFundTransfer().SgstAccountNumber(),
			SgstAccountSolId:         s.config.InternationalFundTransfer().SgstAccountSolId(),
			CustomerId:               bankCustResp.GetBankCustomer().GetVendorCustomerId(),
			ServiceProviderStateCode: s.config.InternationalFundTransfer().GstReportingInfo().ServiceProviderStateCode,
			ServiceProviderSolId:     s.config.InternationalFundTransfer().GstReportingInfo().ServiceProviderSolId,
			UserSolId:                userSolId,
		},
		)
	} else {
		gstData = append(gstData, &iftPb.GSTReportingDataModel{
			GstinOfFilingOrganisation: s.config.InternationalFundTransfer().GstReportingInfo().FilingOrganisation,
			TransactionTime:           swiftTxn.TxnTime,
			InvoiceType:               s.config.InternationalFundTransfer().GstReportingInfo().InvoiceType,
			LocationCode:              userSolId,
			InvoiceNumber:             s.config.InternationalFundTransfer().GstReportingInfo().InvoiceNumber,
			TxnAmount:                 swiftTxn.TxnAmount,
			TaxableAmount:             swiftTxn.ServiceAmount,
			InvoiceHsn:                s.config.InternationalFundTransfer().GstReportingInfo().Hsn,
			ValueOfGoodsSold:          valueOfGoods,
			Pos:                       stateCode,
			SectionName:               s.config.InternationalFundTransfer().GstReportingInfo().SectionName,
			GstPercent:                s.config.InternationalFundTransfer().GstReportingInfo().IgstPercentage,
			IgstAmount:                swiftTxn.Gst,
			SgstAmount:                moneyPb.ZeroINR().GetPb(),
			CgstAmount:                moneyPb.ZeroINR().GetPb(),
			CessAmount:                moneyPb.ZeroINR().GetPb(),
			ReverseCharge:             s.config.InternationalFundTransfer().GstReportingInfo().ReverseCharge,
			IsSez:                     s.config.InternationalFundTransfer().GstReportingInfo().IsSez,
			ReceiverAccountNumber:     s.config.InternationalFundTransfer().IgstAccountNumber(),
			IgstAccountSolId:          s.config.InternationalFundTransfer().IgstAccountSolId(),
			CustomerId:                bankCustResp.GetBankCustomer().GetVendorCustomerId(),
			ServiceProviderStateCode:  s.config.InternationalFundTransfer().GstReportingInfo().ServiceProviderStateCode,
			ServiceProviderSolId:      s.config.InternationalFundTransfer().GstReportingInfo().ServiceProviderSolId,
			UserSolId:                 userSolId,
		},
		)
	}
	return gstData, nil
}

func (s *Service) GetGstReportingDataForInwardTxn(ctx context.Context, req *iftPb.GetGstReportingDataForInwardTxnRequest) (*iftPb.GetGstReportingDataForInwardTxnResponse, error) {
	gst, err := s.gstCalculator.CalculateGST(req.GetGrossValue())
	if err != nil {
		logger.Error(ctx, "error while getting gst break down", zap.Error(err))
		return &iftPb.GetGstReportingDataForInwardTxnResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	resp := &iftPb.GetGstReportingDataForInwardTxnResponse{
		Status: rpc.StatusOk(),
	}
	actorId := ""
	switch {
	case req.GetAccountNumber() != "":
		// get account with given account number
		account, err2 := s.savingsProcessor.GetSavingsAccountByAccountNumberAndVendor(ctx, req.GetAccountNumber(), commonvgpb.Vendor_FEDERAL_BANK)
		if err2 != nil {
			logger.Error(ctx, "error getting account", zap.Error(err2))
			return &iftPb.GetGstReportingDataForInwardTxnResponse{Status: rpc.StatusInternalWithDebugMsg(err2.Error())}, nil
		}
		// get pi with account id
		accountPiResp, err2 := s.accountPiRelationClient.GetByAccountId(ctx, &account_pi.GetByAccountIdRequest{
			AccountId:   account.GetId(),
			AccountType: accounts.Type_SAVINGS,
		})
		if grpcErr := epifigrpc.RPCError(accountPiResp, err2); grpcErr != nil {
			logger.Error(ctx, "error getting Pi for account", zap.Error(grpcErr))
			return &iftPb.GetGstReportingDataForInwardTxnResponse{Status: rpc.StatusInternalWithDebugMsg(grpcErr.Error())}, nil
		}
		// if we dont have pi then return err2
		if len(accountPiResp.GetAccountPis()) == 0 {
			logger.Error(ctx, "no pi for given account and type")
			return &iftPb.GetGstReportingDataForInwardTxnResponse{Status: rpc.StatusInternalWithDebugMsg("no pi for given account")}, nil
		}
		// taking the first actorid
		actorId = accountPiResp.GetAccountPis()[0].GetActorId()
	case req.GetActorId() != "":
		actorId = req.GetActorId()
	}
	resp.Gst = gst.Tax
	resp.ActorId = actorId
	resp.ReportingData, err = s.getGstDetails(ctx, &GstDetailsForSwiftTxn{
		Gst:           gst.Tax,
		TxnTime:       req.GetTxnTime(),
		ServiceAmount: gst.TaxableAmount,
		TxnAmount:     req.GetGrossValue(),
		ActorId:       actorId,
	})
	if err != nil {
		logger.Error(ctx, "error while getting gst reporting data", zap.Error(err))
		return &iftPb.GetGstReportingDataForInwardTxnResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return resp, nil

}
