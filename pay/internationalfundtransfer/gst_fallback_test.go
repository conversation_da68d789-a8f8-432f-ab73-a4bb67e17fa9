package internationalfundtransfer

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	bankCustomerPb "github.com/epifi/gamma/api/bankcust"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/pay/internal/mocks"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	payServerGenConf "github.com/epifi/gamma/pay/config/server/genconf"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/gst"
)

func TestService_getGstDetails_UserSolIdFallback(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockBankCustProcessor := mocks.NewMockIBankCustomerProcessor(ctrl)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
	mockUserProcessor := mocks.NewMockIUserProcessor(ctrl)

	// Create a minimal config for testing
	config := &payServerGenConf.Config{}
	
	service := &Service{
		bankCustProcessor: mockBankCustProcessor,
		savingsClient:     mockSavingsClient,
		userProcessor:     mockUserProcessor,
		config:           config,
		gstCalculator:    &gst.Calculator{}, // Use actual calculator or mock as needed
	}

	ctx := context.Background()
	actorId := "test-actor-id"
	
	swiftTxn := &GstDetailsForSwiftTxn{
		Gst: &money.Money{
			CurrencyCode: "INR",
			Units:        100,
		},
		TxnTime: timestampPb.Now(),
		ServiceAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        500,
		},
		TxnAmount: &money.Money{
			CurrencyCode: "INR",
			Units:        600,
		},
		ActorId: actorId,
	}

	tests := []struct {
		name                    string
		setupMocks             func()
		expectedUserSolId      string
		expectError            bool
		expectedErrorContains  string
	}{
		{
			name: "Success - userSolId available from bank customer",
			setupMocks: func() {
				// Mock getGstStateCodeForUser
				mockUserProcessor.EXPECT().GetUserFromActorId(ctx, actorId).Return(&userPb.User{
					Id: "user-id",
					CommunicationAddress: &postaladdress.PostalAddress{
						AdministrativeArea: "Karnataka",
					},
				}, nil)

				mockBankCustProcessor.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						VendorCustomerId: "vendor-customer-id",
						VendorMetadata: &bankCustomerPb.VendorMetadata{
							FederalMetadata: &bankCustomerPb.FederalMetadata{
								SolId: "1234", // SOL ID available
							},
						},
					},
				}, nil)
			},
			expectedUserSolId: "1234",
			expectError:       false,
		},
		{
			name: "Success - fallback to IFSC code last 4 chars (FDRL0005555 -> 5555)",
			setupMocks: func() {
				// Mock getGstStateCodeForUser
				mockUserProcessor.EXPECT().GetUserFromActorId(ctx, actorId).Return(&userPb.User{
					Id: "user-id",
					CommunicationAddress: &postaladdress.PostalAddress{
						AdministrativeArea: "Karnataka",
					},
				}, nil)

				// Bank customer returns empty SOL ID
				mockBankCustProcessor.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						VendorCustomerId: "vendor-customer-id",
						VendorMetadata: &bankCustomerPb.VendorMetadata{
							FederalMetadata: &bankCustomerPb.FederalMetadata{
								SolId: "", // Empty SOL ID triggers fallback
							},
						},
					},
				}, nil)

				// Savings client returns IFSC code
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier{
						ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
							ActorId:     actorId,
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						IfscCode: "FDRL0005555", // Should extract "5555"
					},
				}, nil)
			},
			expectedUserSolId: "5555",
			expectError:       false,
		},
		{
			name: "Success - fallback with different IFSC format (HDFC0001234 -> 1234)",
			setupMocks: func() {
				// Mock getGstStateCodeForUser
				mockUserProcessor.EXPECT().GetUserFromActorId(ctx, actorId).Return(&userPb.User{
					Id: "user-id",
					CommunicationAddress: &postaladdress.PostalAddress{
						AdministrativeArea: "Karnataka",
					},
				}, nil)

				// Bank customer returns empty SOL ID
				mockBankCustProcessor.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						VendorCustomerId: "vendor-customer-id",
						VendorMetadata: &bankCustomerPb.VendorMetadata{
							FederalMetadata: &bankCustomerPb.FederalMetadata{
								SolId: "",
							},
						},
					},
				}, nil)

				// Savings client returns different IFSC format
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(ctx, gomock.Any()).Return(
					&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpc.StatusOk(),
						Account: &savingsPb.SavingsAccountEssentials{
							IfscCode: "HDFC0001234", // Should extract "1234"
						},
					}, nil)
			},
			expectedUserSolId: "1234",
			expectError:       false,
		},
		{
			name: "Error - fallback fails due to savings client error",
			setupMocks: func() {
				// Mock getGstStateCodeForUser
				mockUserProcessor.EXPECT().GetUserFromActorId(ctx, actorId).Return(&userPb.User{
					Id: "user-id",
					CommunicationAddress: &postaladdress.PostalAddress{
						AdministrativeArea: "Karnataka",
					},
				}, nil)

				// Bank customer returns empty SOL ID
				mockBankCustProcessor.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						VendorCustomerId: "vendor-customer-id",
						VendorMetadata: &bankCustomerPb.VendorMetadata{
							FederalMetadata: &bankCustomerPb.FederalMetadata{
								SolId: "",
							},
						},
					},
				}, nil)

				// Savings client returns error
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(ctx, gomock.Any()).Return(
					&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpc.StatusInternal(),
					}, nil)
			},
			expectError:           true,
			expectedErrorContains: "failed to get savings account essentials",
		},
		{
			name: "Error - fallback fails due to empty IFSC code",
			setupMocks: func() {
				// Mock getGstStateCodeForUser
				mockUserProcessor.EXPECT().GetUserFromActorId(ctx, actorId).Return(&userPb.User{
					Id: "user-id",
					CommunicationAddress: &postaladdress.PostalAddress{
						AdministrativeArea: "Karnataka",
					},
				}, nil)

				// Bank customer returns empty SOL ID
				mockBankCustProcessor.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						VendorCustomerId: "vendor-customer-id",
						VendorMetadata: &bankCustomerPb.VendorMetadata{
							FederalMetadata: &bankCustomerPb.FederalMetadata{
								SolId: "",
							},
						},
					},
				}, nil)

				// Savings client returns empty IFSC code
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(ctx, gomock.Any()).Return(
					&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpc.StatusOk(),
						Account: &savingsPb.SavingsAccountEssentials{
							IfscCode: "", // Empty IFSC code
						},
					}, nil)
			},
			expectError:           true,
			expectedErrorContains: "got empty IFSC code",
		},
		{
			name: "Error - fallback fails due to short IFSC code",
			setupMocks: func() {
				// Mock getGstStateCodeForUser
				mockUserProcessor.EXPECT().GetUserFromActorId(ctx, actorId).Return(&userPb.User{
					Id: "user-id",
					CommunicationAddress: &postaladdress.PostalAddress{
						AdministrativeArea: "Karnataka",
					},
				}, nil)

				// Bank customer returns empty SOL ID
				mockBankCustProcessor.EXPECT().GetBankCustomer(ctx, &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: actorId},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						VendorCustomerId: "vendor-customer-id",
						VendorMetadata: &bankCustomerPb.VendorMetadata{
							FederalMetadata: &bankCustomerPb.FederalMetadata{
								SolId: "",
							},
						},
					},
				}, nil)

				// Savings client returns short IFSC code
				mockSavingsClient.EXPECT().GetSavingsAccountEssentials(ctx, gomock.Any()).Return(
					&savingsPb.GetSavingsAccountEssentialsResponse{
						Status: rpc.StatusOk(),
						Account: &savingsPb.SavingsAccountEssentials{
							IfscCode: "ABC", // Too short (less than 4 chars)
						},
					}, nil)
			},
			expectError:           true,
			expectedErrorContains: "IFSC code is too short",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupMocks()

			result, err := service.getGstDetails(ctx, swiftTxn)

			if tt.expectError {
				assert.Error(t, err)
				if tt.expectedErrorContains != "" {
					assert.Contains(t, err.Error(), tt.expectedErrorContains)
				}
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				
				// Verify that userSolId is used correctly in the response
				if len(result) > 0 {
					assert.Equal(t, tt.expectedUserSolId, result[0].UserSolId)
					assert.Equal(t, tt.expectedUserSolId, result[0].LocationCode)
				}
			}
		})
	}
}
