package internationalfundtransfer

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/gamma/pkg/internationalfundtransfer/gst"

	"context"
	"testing"

	bankCustomerPb "github.com/epifi/gamma/api/bankcust"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/accounts"
	iftPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	mockAccountPi "github.com/epifi/gamma/api/paymentinstrument/account_pi/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	mockOnb "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/pay/config/server/genconf"

	mockProcessor "github.com/epifi/gamma/pay/internal/mocks"
)

func TestService_GetGstReportingDataForInwardTxn(t *testing.T) {
	userSolId := "random-sol-id"
	vendorCustomerId := "vendor-customer-id"
	type fields struct {
		userProcrssor           *mockProcessor.MockIUserProcessor
		config                  *genconf.Config
		savingsProcessor        *mockProcessor.MockISavingsProcessor
		accountPiRelationClient *mockAccountPi.MockAccountPIRelationClient
		onboardingClient        *mockOnb.MockOnboardingClient
		bankCustProcessor       *mockProcessor.MockIBankCustomerProcessor
		savingsClient           *mockSavings.MockSavingsClient
	}

	type args struct {
		ctx context.Context
		req *iftPb.GetGstReportingDataForInwardTxnRequest
	}

	timeStamp := timestampPb.Now()
	var tests = []struct {
		name    string
		args    args
		want    *iftPb.GetGstReportingDataForInwardTxnResponse
		wantErr bool
		prepare func(*args, *fields)
	}{
		{
			name: "when getting  with <1L with in same state",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetGstReportingDataForInwardTxnRequest{
					Identifier: &iftPb.GetGstReportingDataForInwardTxnRequest_AccountNumber{
						AccountNumber: "account-id",
					},
					GrossValue: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        2000,
						Nanos:        0,
					},
					TxnTime: timeStamp,
				},
			},
			want: &iftPb.GetGstReportingDataForInwardTxnResponse{
				Status: rpc.StatusOk(),
				ReportingData: []*iftPb.GSTReportingDataModel{
					{
						GstinOfFilingOrganisation: conf.InternationalFundTransfer().GstReportingInfo().FilingOrganisation,
						TransactionTime:           timeStamp,
						InvoiceType:               conf.InternationalFundTransfer().GstReportingInfo().InvoiceType,
						LocationCode:              userSolId,
						InvoiceNumber:             conf.InternationalFundTransfer().GstReportingInfo().InvoiceNumber,
						TxnAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        2000,
							Nanos:        0,
						},
						TaxableAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        250,
							Nanos:        0,
						},
						InvoiceHsn:       conf.InternationalFundTransfer().GstReportingInfo().Hsn,
						ValueOfGoodsSold: "2000.00",
						Pos:              conf.InternationalFundTransfer().GstReportingInfo().StateCode["kerala"],
						GstPercent:       conf.InternationalFundTransfer().GstReportingInfo().CgstPercentage,
						IgstAmount:       money.ZeroINR().GetPb(),
						// since half amount go to sgst
						SgstAmount: money.ZeroINR().GetPb(),
						CgstAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        22,
							Nanos:        *********,
						},
						CessAmount:               money.ZeroINR().GetPb(),
						SectionName:              conf.InternationalFundTransfer().GstReportingInfo().SectionName,
						ReverseCharge:            conf.InternationalFundTransfer().GstReportingInfo().ReverseCharge,
						IsSez:                    conf.InternationalFundTransfer().GstReportingInfo().IsSez,
						ReceiverAccountNumber:    conf.InternationalFundTransfer().CgstAccountNumber(),
						CgstAccountSolId:         conf.InternationalFundTransfer().CgstAccountSolId(),
						CustomerId:               vendorCustomerId,
						ServiceProviderStateCode: conf.InternationalFundTransfer().GstReportingInfo().ServiceProviderStateCode,
						ServiceProviderSolId:     conf.InternationalFundTransfer().GstReportingInfo().ServiceProviderSolId,
						UserSolId:                userSolId,
					},
					{
						GstinOfFilingOrganisation: conf.InternationalFundTransfer().GstReportingInfo().FilingOrganisation,
						TransactionTime:           timeStamp,
						InvoiceType:               conf.InternationalFundTransfer().GstReportingInfo().InvoiceType,
						LocationCode:              userSolId,
						InvoiceNumber:             conf.InternationalFundTransfer().GstReportingInfo().InvoiceNumber,
						TxnAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        2000,
							Nanos:        0,
						},
						TaxableAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        250,
							Nanos:        0,
						},
						InvoiceHsn:       conf.InternationalFundTransfer().GstReportingInfo().Hsn,
						ValueOfGoodsSold: "2000.00",
						Pos:              conf.InternationalFundTransfer().GstReportingInfo().StateCode["kerala"],
						GstPercent:       conf.InternationalFundTransfer().GstReportingInfo().SgstPercentage,
						IgstAmount:       money.ZeroINR().GetPb(),
						// since half amount go to sgst
						SgstAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        22,
							Nanos:        *********,
						},
						CgstAmount:               money.ZeroINR().GetPb(),
						CessAmount:               money.ZeroINR().GetPb(),
						SectionName:              conf.InternationalFundTransfer().GstReportingInfo().SectionName,
						ReverseCharge:            conf.InternationalFundTransfer().GstReportingInfo().ReverseCharge,
						IsSez:                    conf.InternationalFundTransfer().GstReportingInfo().IsSez,
						ReceiverAccountNumber:    conf.InternationalFundTransfer().SgstAccountNumber(),
						SgstAccountSolId:         conf.InternationalFundTransfer().SgstAccountSolId(),
						CustomerId:               vendorCustomerId,
						ServiceProviderStateCode: conf.InternationalFundTransfer().GstReportingInfo().ServiceProviderStateCode,
						ServiceProviderSolId:     conf.InternationalFundTransfer().GstReportingInfo().ServiceProviderSolId,
						UserSolId:                userSolId,
					},
				},
				Gst: &moneyPb.Money{
					CurrencyCode: money.RupeeCurrencyCode,
					Units:        45,
					Nanos:        0,
				},
				ActorId: "actor_id",
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.userProcrssor.EXPECT().GetUserWithPinCode(gomock.Any(), "actor_id").Return(&userPb.User{
					Id: "user-id",
					Profile: &userPb.Profile{Addresses: map[string]*postaladdress.PostalAddress{
						types.AddressType_MAILING.String(): {
							PostalCode: "314001",
						},
					}},
				}, nil)

				f.onboardingClient.EXPECT().GetPinCodeDetails(gomock.Any(), &onboardingPb.GetPinCodeDetailsRequest{PinCode: "314001", FieldMask: []onboardingPb.PinCodeField{onboardingPb.PinCodeField_PIN_CODE_FIELD_STATE}}).Return(
					&onboardingPb.GetPinCodeDetailsResponse{
						Status: rpc.StatusOk(),
						Details: []*onboardingPb.PinCodeDetails{
							{State: "kerala"},
						},
					}, nil)

				f.savingsProcessor.EXPECT().GetSavingsAccountByAccountNumberAndVendor(gomock.Any(), "account-id", commonvgpb.Vendor_FEDERAL_BANK).Times(1).Return(&savingsPb.Account{
					Id: "internal-account-id",
				}, nil)
				f.accountPiRelationClient.EXPECT().GetByAccountId(gomock.Any(), &account_pi.GetByAccountIdRequest{
					AccountId:   "internal-account-id",
					AccountType: accounts.Type_SAVINGS,
				}).Return(&account_pi.GetByAccountIdResponse{
					Status: rpc.StatusOk(),
					AccountPis: []*account_pi.AccountPI{
						{
							ActorId: "actor_id",
						},
					},
				}, nil)
				f.bankCustProcessor.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: "actor_id"},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						VendorCustomerId: vendorCustomerId,
						VendorMetadata: &bankCustomerPb.VendorMetadata{
							Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{
								FederalMetadata: &bankCustomerPb.FederalMetadata{
									SolId: userSolId,
								},
							},
						},
					},
				}, nil)
			},
		},
		{
			name: "when getting  with < 1L with in different state",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetGstReportingDataForInwardTxnRequest{
					Identifier: &iftPb.GetGstReportingDataForInwardTxnRequest_AccountNumber{
						AccountNumber: "account-id",
					},
					GrossValue: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        2000,
						Nanos:        0,
					},
					TxnTime: timeStamp,
				},
			},
			want: &iftPb.GetGstReportingDataForInwardTxnResponse{
				Status: rpc.StatusOk(),
				ReportingData: []*iftPb.GSTReportingDataModel{
					{
						GstinOfFilingOrganisation: conf.InternationalFundTransfer().GstReportingInfo().FilingOrganisation,
						TransactionTime:           timeStamp,
						InvoiceType:               conf.InternationalFundTransfer().GstReportingInfo().InvoiceType,
						LocationCode:              userSolId,
						InvoiceNumber:             conf.InternationalFundTransfer().GstReportingInfo().InvoiceNumber,
						TxnAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        2000,
							Nanos:        0,
						},
						TaxableAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        250,
							Nanos:        0,
						},
						InvoiceHsn:       conf.InternationalFundTransfer().GstReportingInfo().Hsn,
						ValueOfGoodsSold: "2000.00",
						Pos:              conf.InternationalFundTransfer().GstReportingInfo().StateCode["telangana"],
						GstPercent:       conf.InternationalFundTransfer().GstReportingInfo().IgstPercentage,
						IgstAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        45,
							Nanos:        0,
						},

						SgstAmount:               money.ZeroINR().GetPb(),
						CgstAmount:               money.ZeroINR().GetPb(),
						CessAmount:               money.ZeroINR().GetPb(),
						SectionName:              conf.InternationalFundTransfer().GstReportingInfo().SectionName,
						ReverseCharge:            conf.InternationalFundTransfer().GstReportingInfo().ReverseCharge,
						IsSez:                    conf.InternationalFundTransfer().GstReportingInfo().IsSez,
						ReceiverAccountNumber:    conf.InternationalFundTransfer().IgstAccountNumber(),
						IgstAccountSolId:         conf.InternationalFundTransfer().IgstAccountSolId(),
						CustomerId:               vendorCustomerId,
						ServiceProviderStateCode: conf.InternationalFundTransfer().GstReportingInfo().ServiceProviderStateCode,
						ServiceProviderSolId:     conf.InternationalFundTransfer().GstReportingInfo().ServiceProviderSolId,
						UserSolId:                userSolId,
					},
				},
				Gst: &moneyPb.Money{
					CurrencyCode: money.RupeeCurrencyCode,
					Units:        45,
					Nanos:        0,
				},
				ActorId: "actor_id",
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {

				f.savingsProcessor.EXPECT().GetSavingsAccountByAccountNumberAndVendor(gomock.Any(), "account-id", commonvgpb.Vendor_FEDERAL_BANK).Times(1).Return(&savingsPb.Account{
					Id: "internal-account-id",
				}, nil)
				f.accountPiRelationClient.EXPECT().GetByAccountId(gomock.Any(), &account_pi.GetByAccountIdRequest{
					AccountId:   "internal-account-id",
					AccountType: accounts.Type_SAVINGS,
				}).Return(&account_pi.GetByAccountIdResponse{
					Status: rpc.StatusOk(),
					AccountPis: []*account_pi.AccountPI{
						{
							ActorId: "actor_id",
						},
					},
				}, nil)

				f.userProcrssor.EXPECT().GetUserWithPinCode(gomock.Any(), "actor_id").Return(&userPb.User{
					Id: "user-id",
					Profile: &userPb.Profile{Addresses: map[string]*postaladdress.PostalAddress{
						types.AddressType_MAILING.String(): {
							PostalCode: "314001",
						},
					}},
				}, nil)

				f.onboardingClient.EXPECT().GetPinCodeDetails(gomock.Any(), &onboardingPb.GetPinCodeDetailsRequest{PinCode: "314001", FieldMask: []onboardingPb.PinCodeField{onboardingPb.PinCodeField_PIN_CODE_FIELD_STATE}}).Return(
					&onboardingPb.GetPinCodeDetailsResponse{
						Status: rpc.StatusOk(),
						Details: []*onboardingPb.PinCodeDetails{
							{State: "telangana"},
						},
					}, nil)

				f.bankCustProcessor.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: "actor_id"},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						VendorCustomerId: vendorCustomerId,
						VendorMetadata: &bankCustomerPb.VendorMetadata{
							Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{
								FederalMetadata: &bankCustomerPb.FederalMetadata{
									SolId: userSolId,
								},
							},
						},
					},
				}, nil)
			},
		},
		{
			name: "error while getting user",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetGstReportingDataForInwardTxnRequest{
					Identifier: &iftPb.GetGstReportingDataForInwardTxnRequest_AccountNumber{
						AccountNumber: "account-id",
					},
					GrossValue: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        2000,
						Nanos:        0,
					},
					TxnTime: timeStamp,
				},
			},
			want: &iftPb.GetGstReportingDataForInwardTxnResponse{
				Status: rpc.StatusInternalWithDebugMsg(epifierrors.ErrRecordNotFound.Error()),
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.savingsProcessor.EXPECT().GetSavingsAccountByAccountNumberAndVendor(gomock.Any(), "account-id", commonvgpb.Vendor_FEDERAL_BANK).Times(1).Return(&savingsPb.Account{
					Id: "internal-account-id",
				}, nil)
				f.accountPiRelationClient.EXPECT().GetByAccountId(gomock.Any(), &account_pi.GetByAccountIdRequest{
					AccountId:   "internal-account-id",
					AccountType: accounts.Type_SAVINGS,
				}).Return(&account_pi.GetByAccountIdResponse{
					Status: rpc.StatusOk(),
					AccountPis: []*account_pi.AccountPI{
						{
							ActorId: "actor_id",
						},
					},
				}, nil)
				f.userProcrssor.EXPECT().GetUserWithPinCode(a.ctx, "actor_id").Return(nil, epifierrors.ErrRecordNotFound)

			},
		},
		{
			name: "error while getting user address",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetGstReportingDataForInwardTxnRequest{
					Identifier: &iftPb.GetGstReportingDataForInwardTxnRequest_AccountNumber{
						AccountNumber: "account-id",
					},
					GrossValue: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        2000,
						Nanos:        0,
					},
					TxnTime: timeStamp,
				},
			},
			want: &iftPb.GetGstReportingDataForInwardTxnResponse{
				Status: rpc.StatusInternalWithDebugMsg(epifierrors.ErrRecordNotFound.Error()),
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.savingsProcessor.EXPECT().GetSavingsAccountByAccountNumberAndVendor(gomock.Any(), "account-id", commonvgpb.Vendor_FEDERAL_BANK).Times(1).Return(&savingsPb.Account{
					Id: "internal-account-id",
				}, nil)
				f.accountPiRelationClient.EXPECT().GetByAccountId(gomock.Any(), &account_pi.GetByAccountIdRequest{
					AccountId:   "internal-account-id",
					AccountType: accounts.Type_SAVINGS,
				}).Return(&account_pi.GetByAccountIdResponse{
					Status: rpc.StatusOk(),
					AccountPis: []*account_pi.AccountPI{
						{
							ActorId: "actor_id",
						},
					},
				}, nil)
				f.userProcrssor.EXPECT().GetUserWithPinCode(gomock.Any(), "actor_id").Return(&userPb.User{
					Id: "user-id",
					Profile: &userPb.Profile{Addresses: map[string]*postaladdress.PostalAddress{
						types.AddressType_MAILING.String(): {
							PostalCode: "314001",
						},
					}},
				}, nil)

				f.onboardingClient.EXPECT().GetPinCodeDetails(gomock.Any(), &onboardingPb.GetPinCodeDetailsRequest{PinCode: "314001", FieldMask: []onboardingPb.PinCodeField{onboardingPb.PinCodeField_PIN_CODE_FIELD_STATE}}).Return(
					&onboardingPb.GetPinCodeDetailsResponse{
						Status: rpc.StatusInternal(),
					}, nil)
			},
		},
		{
			name: "error while getting Account",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetGstReportingDataForInwardTxnRequest{
					Identifier: &iftPb.GetGstReportingDataForInwardTxnRequest_AccountNumber{
						AccountNumber: "account-id",
					},
					GrossValue: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        2000,
						Nanos:        0,
					},
					TxnTime: timeStamp,
				},
			},
			want: &iftPb.GetGstReportingDataForInwardTxnResponse{
				Status: rpc.StatusInternalWithDebugMsg(epifierrors.ErrRecordNotFound.Error()),
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.savingsProcessor.EXPECT().GetSavingsAccountByAccountNumberAndVendor(gomock.Any(), "account-id", commonvgpb.Vendor_FEDERAL_BANK).Times(1).Return(nil, epifierrors.ErrRecordNotFound)
			},
		},
		{
			name: "error while getting PI",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetGstReportingDataForInwardTxnRequest{
					Identifier: &iftPb.GetGstReportingDataForInwardTxnRequest_AccountNumber{
						AccountNumber: "account-id",
					},
					GrossValue: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        2000,
						Nanos:        0,
					},
					TxnTime: timeStamp,
				},
			},
			want: &iftPb.GetGstReportingDataForInwardTxnResponse{
				Status: rpc.StatusInternalWithDebugMsg(epifierrors.ErrRecordNotFound.Error()),
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.savingsProcessor.EXPECT().GetSavingsAccountByAccountNumberAndVendor(gomock.Any(), "account-id", commonvgpb.Vendor_FEDERAL_BANK).Times(1).Return(&savingsPb.Account{
					Id: "internal-account-id",
				}, nil)
				f.accountPiRelationClient.EXPECT().GetByAccountId(gomock.Any(), &account_pi.GetByAccountIdRequest{
					AccountId:   "internal-account-id",
					AccountType: accounts.Type_SAVINGS,
				}).Return(&account_pi.GetByAccountIdResponse{
					Status: rpc.StatusInternalWithDebugMsg(epifierrors.ErrRecordNotFound.Error()),
				}, nil)
			},
		},

		{
			name: "getting zero PI for given account",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetGstReportingDataForInwardTxnRequest{
					Identifier: &iftPb.GetGstReportingDataForInwardTxnRequest_AccountNumber{
						AccountNumber: "account-id",
					},
					GrossValue: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        2000,
						Nanos:        0,
					},
					TxnTime: timeStamp,
				},
			},
			want: &iftPb.GetGstReportingDataForInwardTxnResponse{
				Status: rpc.StatusInternalWithDebugMsg("no pi for given account and type"),
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.savingsProcessor.EXPECT().GetSavingsAccountByAccountNumberAndVendor(gomock.Any(), "account-id", commonvgpb.Vendor_FEDERAL_BANK).Times(1).Return(&savingsPb.Account{
					Id: "internal-account-id",
				}, nil)
				f.accountPiRelationClient.EXPECT().GetByAccountId(gomock.Any(), &account_pi.GetByAccountIdRequest{
					AccountId:   "internal-account-id",
					AccountType: accounts.Type_SAVINGS,
				}).Return(&account_pi.GetByAccountIdResponse{
					Status:     rpc.StatusOk(),
					AccountPis: []*account_pi.AccountPI{},
				}, nil)
			},
		},
		{
			name: "empty SOL ID with successful fallback to IFSC code",
			args: args{
				ctx: context.Background(),
				req: &iftPb.GetGstReportingDataForInwardTxnRequest{
					Identifier: &iftPb.GetGstReportingDataForInwardTxnRequest_AccountNumber{
						AccountNumber: "account-id",
					},
					GrossValue: &moneyPb.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        2000,
						Nanos:        0,
					},
					TxnTime: timeStamp,
				},
			},
			want: &iftPb.GetGstReportingDataForInwardTxnResponse{
				Status: rpc.StatusOk(),
				ReportingData: []*iftPb.GSTReportingDataModel{
					{
						GstinOfFilingOrganisation: conf.InternationalFundTransfer().GstReportingInfo().FilingOrganisation,
						TransactionTime:           timeStamp,
						InvoiceType:               conf.InternationalFundTransfer().GstReportingInfo().InvoiceType,
						LocationCode:              "5555", // Last 4 chars of FDRL0005555
						InvoiceNumber:             conf.InternationalFundTransfer().GstReportingInfo().InvoiceNumber,
						TxnAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        2000,
							Nanos:        0,
						},
						TaxableAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        250,
							Nanos:        0,
						},
						InvoiceHsn:       conf.InternationalFundTransfer().GstReportingInfo().Hsn,
						ValueOfGoodsSold: "2000.00",
						Pos:              conf.InternationalFundTransfer().GstReportingInfo().StateCode["kerala"],
						GstPercent:       conf.InternationalFundTransfer().GstReportingInfo().CgstPercentage,
						IgstAmount:       money.ZeroINR().GetPb(),
						SgstAmount:       money.ZeroINR().GetPb(),
						CgstAmount: &moneyPb.Money{
							CurrencyCode: money.RupeeCurrencyCode,
							Units:        22,
							Nanos:        *********,
						},
						CessAmount:               money.ZeroINR().GetPb(),
						SectionName:              conf.InternationalFundTransfer().GstReportingInfo().SectionName,
						ReverseCharge:            conf.InternationalFundTransfer().GstReportingInfo().ReverseCharge,
						IsSez:                    conf.InternationalFundTransfer().GstReportingInfo().IsSez,
						ReceiverAccountNumber:    conf.InternationalFundTransfer().CgstAccountNumber(),
						CgstAccountSolId:         conf.InternationalFundTransfer().CgstAccountSolId(),
						CustomerId:               vendorCustomerId,
						ServiceProviderStateCode: conf.InternationalFundTransfer().GstReportingInfo().ServiceProviderStateCode,
						ServiceProviderSolId:     conf.InternationalFundTransfer().GstReportingInfo().ServiceProviderSolId,
						UserSolId:                "5555", // Last 4 chars of FDRL0005555
					},
				},
				Gst: &moneyPb.Money{
					CurrencyCode: money.RupeeCurrencyCode,
					Units:        45,
					Nanos:        0,
				},
				ActorId: "actor_id",
			},
			wantErr: false,
			prepare: func(a *args, f *fields) {
				f.userProcrssor.EXPECT().GetUserWithPinCode(gomock.Any(), "actor_id").Return(&userPb.User{
					Id: "user-id",
					Profile: &userPb.Profile{Addresses: map[string]*postaladdress.PostalAddress{
						types.AddressType_MAILING.String(): {
							PostalCode: "314001",
						},
					}},
				}, nil)

				f.onboardingClient.EXPECT().GetPinCodeDetails(gomock.Any(), &onboardingPb.GetPinCodeDetailsRequest{PinCode: "314001", FieldMask: []onboardingPb.PinCodeField{onboardingPb.PinCodeField_PIN_CODE_FIELD_STATE}}).Return(
					&onboardingPb.GetPinCodeDetailsResponse{
						Status: rpc.StatusOk(),
						Details: []*onboardingPb.PinCodeDetails{
							{State: "kerala"},
						},
					}, nil)

				f.savingsProcessor.EXPECT().GetSavingsAccountByAccountNumberAndVendor(gomock.Any(), "account-id", commonvgpb.Vendor_FEDERAL_BANK).Times(1).Return(&savingsPb.Account{
					Id: "internal-account-id",
				}, nil)
				f.accountPiRelationClient.EXPECT().GetByAccountId(gomock.Any(), &account_pi.GetByAccountIdRequest{
					AccountId:   "internal-account-id",
					AccountType: accounts.Type_SAVINGS,
				}).Return(&account_pi.GetByAccountIdResponse{
					Status: rpc.StatusOk(),
					AccountPis: []*account_pi.AccountPI{
						{
							ActorId: "actor_id",
						},
					},
				}, nil)

				// Bank customer returns empty SOL ID
				f.bankCustProcessor.EXPECT().GetBankCustomer(gomock.Any(), &bankCustomerPb.GetBankCustomerRequest{
					Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bankCustomerPb.GetBankCustomerRequest_ActorId{ActorId: "actor_id"},
				}).Return(&bankCustomerPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bankCustomerPb.BankCustomer{
						VendorCustomerId: vendorCustomerId,
						VendorMetadata: &bankCustomerPb.VendorMetadata{
							Vendor: &bankCustomerPb.VendorMetadata_FederalMetadata{
								FederalMetadata: &bankCustomerPb.FederalMetadata{
									SolId: "", // Empty SOL ID to trigger fallback
								},
							},
						},
					},
				}, nil)

				// Fallback: GetSavingsAccountEssentials returns IFSC code
				f.savingsClient.EXPECT().GetSavingsAccountEssentials(gomock.Any(), &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier{
						ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
							ActorId:     "actor_id",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				}).Return(&savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
					Account: &savingsPb.SavingsAccountEssentials{
						IfscCode: "FDRL0005555", // IFSC code with last 4 chars as "5555"
					},
				}, nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctr := gomock.NewController(t)
			userProcessor := mockProcessor.NewMockIUserProcessor(ctr)
			savingProcessor := mockProcessor.NewMockISavingsProcessor(ctr)
			accountPiRelationClient := mockAccountPi.NewMockAccountPIRelationClient(ctr)
			onbClient := mockOnb.NewMockOnboardingClient(ctr)
			bankCustomerProcessor := mockProcessor.NewMockIBankCustomerProcessor(ctr)
			savingsClient := mockSavings.NewMockSavingsClient(ctr)
			f := fields{
				userProcrssor:           userProcessor,
				config:                  conf,
				savingsProcessor:        savingProcessor,
				accountPiRelationClient: accountPiRelationClient,
				onboardingClient:        onbClient,
				bankCustProcessor:       bankCustomerProcessor,
				savingsClient:           savingsClient,
			}
			if tt.prepare != nil {
				tt.prepare(&tt.args, &f)
			}
			s := &Service{
				gstCalculator:           gst.NewSlabRateBasedGSTCalculator(),
				userProcessor:           userProcessor,
				config:                  conf,
				savingsProcessor:        savingProcessor,
				accountPiRelationClient: accountPiRelationClient,
				onboardingClient:        onbClient,
				bankCustProcessor:       bankCustomerProcessor,
				savingsClient:           savingsClient,
			}
			got, err := s.GetGstReportingDataForInwardTxn(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetGstReportingDataForInwardTxn() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			opts := []cmp.Option{
				protocmp.Transform(),
				protocmp.IgnoreFields(&rpc.Status{}, "short_message", "debug_message"),
			}
			if diff := cmp.Diff(got, tt.want, opts...); diff != "" {
				t.Errorf("GetGstReportingDataForInwardTxn()  got = %v,\n want %v", got, tt.want)
			}
		})
	}
}
