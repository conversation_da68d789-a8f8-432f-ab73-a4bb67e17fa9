Application:
  Environment: "test"
  Name: "insights"
  GmailDataEncrKeyKMSId: "insights/gmail-data-encryption"
  EmailIdRegex: "[a-zA-Z0-9.!#$%&'*+\\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*"
  MailFetchDateRangeInMonths: 3

Server:
  Ports:
    GrpcPort: 8096
    GrpcSecurePort: 9511
    HttpPort: 9886

InsightsDb:
  AppName: "insights"
  StatementTimeout: 5m
  Name: "insights_test"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

ActorInsightsDb:
  AppName: "insights"
  StatementTimeout: 5m
  Name: "actor_insights_test"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  EnableDebug: true
  SSLMode: "disable"

GmailBatchGetApiParams:
  ClientMaxIdleConns: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 15000
  BatchReqUrl: "https://www.googleapis.com/batch/gmail/v1"
  GetMessageApi: "GET /gmail/v1/users/me/messages/"

GmailListApiParams:
  ClientMaxIdleConns: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 2000

EmailParserParams:
  ClientMaxIdleConns: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 1000
  ParseReqUrl: "http://localhost:80/parse"

GoogleOAuthParams:
  ClientMaxIdleConns: 10
  ClientIdleConnTimeout: 30
  TimeoutInMillis: 1500
  RevokeTokenUrl: "https://oauth2.googleapis.com/revoke"

MailFetchConcurrencyParams:
  MaxGoroutinesPerUser: 5
  MessagePerGoroutine: 10

UserEmailAccessPublisher:
  QueueName: "new-user-email-access-queue"

UserEmailAccessSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "new-user-email-access-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 6
      TimeUnit: "Second"

PeriodicEmaiSyncPublisher:
  QueueName: "periodic-mail-sync-queue"

PeriodicEmailSyncSubscriber:
  StartOnServerStart: true
  NumWorkers: 6
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "periodic-mail-sync-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"

MailDataParserPublisher:
  QueueName: "mail-data-parser-queue"

MailDataParserSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "mail-data-parser-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 2
      MaxAttempts: 6
      TimeUnit: "Second"

GmailUserSpendsPublisher:
  TopicName: "gmail-user-spends-topic"

EpfPassbookImportEventPublisher:
  TopicName: "epf-passbook-import-topic"

CreateOrUpdateGenerationStatusSubscriber:
  StartOnServerStart: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-insight-generation-script-run-status"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"

StoreGeneratedActorInsightsSubscriber:
  StartOnServerStart: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-generated-actor-insights"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 20
      TimeUnit: "Minute"

OnboardingStageUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "insights-onb-stage-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 5
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 600
        Period: 1m
    Namespace: "insights"

GenerateInsightsSubscriber:
  StartOnServerStart: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 2
  QueueName: "dev-generate-insights"
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "insights"

ProcessCaNewDataFetchEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 2
  QueueName: "insights-ca-data-new-data-fetch-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "insights"

AWS:
  Region: "ap-south-1"

Secrets:
  Ids:
    GoogleOAuthCredentials: "{\"web\":{\"client_id\":\"*************-82nkl0i26p2q1rpb8ok3ki0vvdn3b6uo.apps.googleusercontent.com\",\"project_id\":\"emailparser-*************\",\"auth_uri\":\"https://accounts.google.com/o/oauth2/auth\",\"token_uri\":\"https://oauth2.googleapis.com/token\",\"auth_provider_x509_cert_url\":\"https://www.googleapis.com/oauth2/v1/certs\",\"client_secret\":\"crxYMCBazjCJxVmRTm1Qnx2S\",\"redirect_uris\":[\"http://www.epifi.com/\"]}}"
    EmailParserInsightsDbUsernamePassword: "{\"username\": \"root\", \"password\": \"\"}"
    ActorInsightsDbUsernamePassword: "{\"username\": \"root\", \"password\": \"\"}"
    MailDataEncryptionKey: "WmZq4t7w!z%C*F-JaNdRgUjXn2r5u8x/"

Flags:
  TrimDebugMessageFromStatus: false
  ShowTeaserInsights: false

AddGmailAccountBannerParams:
  ShowAddAccountBanner: true
  AddAccountRedirectUrl: "https://fi.money/insights"
  AddAccountWebViewTitle: "Insights"

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true


RealTimeInsightParams:
  SegmentCoolDownPeriod: "240h" # 10 days
  RealTimeInsightProb: 10 # prob on scale of [1, 100] that real time insight flow is chosen to generate insight

NetWorthRefreshParams:
  AssetsRefreshThreshold:
    - "NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS": 720h # 1 month
    - "NET_WORTH_REFRESH_ASSET_EPF": 1440h # 2 month
    - "NET_WORTH_REFRESH_ASSET_CREDIT_SCORE": 2160h # 3 month
  ManualAssetsRefreshThreshold:
    - "REAL_ESTATE": 4320h # 6 month
    - "AIF": 8640h # 1 year
    - "PRIVATE_EQUITY": 8640h # 1 year
    - "CASH": 4320h # 6 month
    - "BOND": 8640h # 1 year
    - "ART_AND_ARTEFACTS": 8640h # 1 year
  AssetsProcessingRefreshThreshold:
    - "NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS": 30m
    - "NET_WORTH_REFRESH_ASSET_EPF": 30m
    - "NET_WORTH_REFRESH_ASSET_CREDIT_SCORE": 30m
  InstrumentsRefreshOrder: [
    "NET_WORTH_REFRESH_ASSET_CREDIT_SCORE",
    "NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS",
    "NET_WORTH_REFRESH_ASSET_EPF",
    "NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS" ]

EpfPassbookDataFlatteningSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "epf-passbook-data-flattening-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 2
      TimeUnit: "Second"

NetworthParams:
  UseSchemeAnalyticsApiForMfAggVal: true

DbConfigMap:
  FEDERAL_BANK:
    DbType: "PGDB"
    Host: "localhost"
    Port: 5432
    Username: "root"
    Password: ""
    Name: "insights_federal_test"
    StatementTimeout: 5m
    EnableDebug: false
    SSLMode: "disable"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true



LLMHandlerConfig:
  Environment: "test"
  GeminiConf:
    GoogleCloudCredentialsSecretPath: "{\"project_id\": \"sampleProject\"}"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 0
    HystrixCommand:
      CommandName: "llm_redis_circuit_breaker_command"
      TemplateName: "Q20"
      OverrideTemplateConfig:
        ExecutionMaxConcurrency: 500
        ExecutionTimeout: 1s # set higher value to debug smoke test tenant from local machine
        RequiredConsecutiveSuccessful: 6
        HalfOpenAttemptsAllowedPerSleepWindow: 10
        ErrorThresholdPercentage: 80
  VendorApiConf:
    # HTTP client config inspired from DefaultTransport of http package
    # https://golang.org/src/net/http/transport.go?h=DefaultTransport#L42
    HttpClientConfig:
      Transport:
        DialContext:
          Timeout: 30s
          KeepAlive: 30s
        TLSHandshakeTimeout: 10s
        MaxIdleConns: 100
        IdleConnTimeout: 90s
        MaxConnsPerHost: 500
        MaxIdleConnsPerHost: 50


MagicImportConfig:
  S3BucketName: "epifi-wealth-insights"
