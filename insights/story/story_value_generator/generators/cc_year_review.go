// nolint:funlen
package generators

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"strconv"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/gamma/api/firefly"
	ffPb "github.com/epifi/gamma/api/firefly"
	ffEnums "github.com/epifi/gamma/api/firefly/enums"
	"github.com/epifi/gamma/api/frontend/insights/story"
	storyPb "github.com/epifi/gamma/api/insights/story"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
)

const (
	CcYearReview_YearReview_Text1         = "YearReview_Text1"
	CcYearReview_YearReview_BottomSheet   = "YearReview_BottomSheet"
	CcYearReview_YearReview_Show          = "YearReview_Show"
	CcYearReview_YearReview_BottomSheetBg = "YearReview_BottomSheetBgLinearGradient"

	CCYearEarn_BgLinearGradient = "CCYearEarn_BgLinearGradient"
	CCYearEarn_Lottie           = "CCYearEarn_Lottie"
	CCYearEarn_Title            = "CCYearEarn_Title"
	CCYearEarn_Subtitle         = "CCYearEarn_Subtitle"
	CCYearEarn_Show             = "CCYearEarn_Show"

	welcomeGiftIconUrl = "https://epifi-icons.pointz.in/credit_card_images/offers.png"
	fiCoinsIconUrl     = "https://epifi-icons.pointz.in/credit_card_images/fi-coins.png"
	forexIconUrl       = "https://epifi-icons.pointz.in/credit_card_images/globe.png"
	loungeIconUrl      = "https://epifi-icons.pointz.in/credit_card_images/heart.png"
	rupeeIconUrl       = "https://epifi-icons.pointz.in/credit_card_images/green_rupee.png"

	creamTextColor = "#F6F9FD"
)

type CcYearReviewGenerator struct {
	fireflyClient firefly.FireflyClient
}

func NewCcYearReviewGenerator(
	fireflyClient firefly.FireflyClient,
) *CcYearReviewGenerator {
	return &CcYearReviewGenerator{
		fireflyClient: fireflyClient,
	}
}

func (c *CcYearReviewGenerator) GenerateValues(ctx context.Context, actorId string) (map[string]string, map[string]*story.StoryAppAction, error) {
	rewardsData, err := c.fireflyClient.GetRedeemedRewardsInfo(ctx, &ffPb.GetRedeemedRewardsInfoRequest{ActorId: actorId})
	if rpcErr := epifigrpc.RPCError(rewardsData, err); rpcErr != nil {
		logger.Error(ctx, "failed to get actor's CC rewards data", zap.Error(rpcErr))
		return map[string]string{
			CcYearReview_YearReview_Show: strconv.FormatBool(false),
			CCYearEarn_Show:              strconv.FormatBool(false),
		}, map[string]*story.StoryAppAction{}, nil
	}

	values, actions, err := c.generateValues(ctx, rewardsData)
	if err != nil {
		logger.Error(ctx, "failed to generate cc yearReview values", zap.Error(err))
		return map[string]string{
			CcYearReview_YearReview_Show: strconv.FormatBool(false),
			CCYearEarn_Show:              strconv.FormatBool(false),
		}, map[string]*story.StoryAppAction{}, nil
	}

	return values, actions, nil
}

func (c *CcYearReviewGenerator) generateValues(ctx context.Context, rewardsData *ffPb.GetRedeemedRewardsInfoResponse) (map[string]string, map[string]*story.StoryAppAction, error) {
	bottomSheet := &storyPb.BottomSheetLineItemsView{}
	var (
		subtitleStr    []byte
		showCcYearEarn bool
	)

	if rewardsData.GetSelectedRewardType() == ffEnums.CCRewardType_CC_REWARD_TYPE_GIFT_VOUCHERS {
		bottomSheet.Items = append(bottomSheet.GetItems(), getLineItem("Welcome Gift Cards", welcomeGiftIconUrl, money.ToDisplayStringInIndianFormat(&moneyPb.Money{
			CurrencyCode: "INR",
			Units:        4250,
		}, 0, true), creamTextColor))
	} else if rewardsData.GetSelectedRewardType() == ffEnums.CCRewardType_CC_REWARD_TYPE_FI_COINS {
		bottomSheet.Items = append(bottomSheet.GetItems(), getLineItem("Welcome Gift Cards", welcomeGiftIconUrl, "50,000 fi-coins", creamTextColor))
	}

	bottomSheet.Items = append(bottomSheet.GetItems(), getLineItem("Fi-Coins earned", fiCoinsIconUrl, strconv.FormatFloat(rewardsData.GetFiCoinsCount(), 'f', 2, 64), creamTextColor),
		getLineItem("Forex refunds", forexIconUrl, money.ToDisplayStringInIndianFormat(rewardsData.GetForexFeesSaved(), 0, true), creamTextColor),
		getLineItem("Lounge passes used", loungeIconUrl, strconv.Itoa(int(rewardsData.GetLoungePassAvailed())), creamTextColor),
		getLineItem("Cashback & Discounts", rupeeIconUrl, money.ToDisplayStringInIndianFormat(rewardsData.GetCashbackReceived(), 0, true), creamTextColor))

	bottomSheetStr, err := protojson.MarshalOptions{UseProtoNames: true}.Marshal(bottomSheet)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to marshal bottom sheet, cannot generate ccYearReview")
	}

	linearGradientStr, err := getLinearGradient("#3B2E56", "#050507", "#234646")
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to get linear gradient string")
	}

	title := commontypes.GetTextFromStringFontColourFontStyle(
		"And you earned even more!",
		"#FFFFFF",
		commontypes.FontStyle_DISPLAY_XL,
	)
	titleStr, err := protojson.MarshalOptions{UseProtoNames: true}.Marshal(title)
	if err != nil {
		return nil, nil, errors.Wrap(err, "failed to marshal title")
	}

	totalRewardsEarned, err := money.Sum(rewardsData.GetCashbackReceived(), rewardsData.GetCashbackValueOfFiCoins())
	switch {
	case err != nil:
		logger.Error(ctx, "failed to get sum of cashback and cashback value", zap.Error(err))
		showCcYearEarn = false
	default:
		totalRewardsEarned, err = money.Sum(totalRewardsEarned, rewardsData.GetForexFeesSaved())
		switch {
		case err != nil:
			logger.Error(ctx, "failed to get sum of totalRewards and forexFees", zap.Error(err))
			showCcYearEarn = false
		default:
			subtitle := commontypes.GetTextFromStringFontColourFontStyle(
				money.ToDisplayStringInIndianFormat(totalRewardsEarned, 0, true),
				"#FFFFFF",
				commontypes.FontStyle_NUMBER_3XL,
			)
			subtitleStr, err = protojson.MarshalOptions{UseProtoNames: true}.Marshal(subtitle)
			if err != nil {
				return nil, nil, errors.Wrap(err, "failed to marshal subtitle")
			}
			showCcYearEarn = true
		}
	}

	return map[string]string{
		CcYearReview_YearReview_Show:          strconv.FormatBool(true),
		CcYearReview_YearReview_Text1:         "Your AmpliFi benefits in the last year",
		CcYearReview_YearReview_BottomSheet:   string(bottomSheetStr),
		CcYearReview_YearReview_BottomSheetBg: linearGradientStr,

		CCYearEarn_Show:             strconv.FormatBool(showCcYearEarn),
		CCYearEarn_BgLinearGradient: linearGradientStr,
		CCYearEarn_Title:            string(titleStr),
		CCYearEarn_Subtitle:         string(subtitleStr),
		CCYearEarn_Lottie:           "https://epifi-icons.pointz.in/credit_card_images/cc_minutes_earn_lottie.json",
	}, map[string]*story.StoryAppAction{}, nil
}
