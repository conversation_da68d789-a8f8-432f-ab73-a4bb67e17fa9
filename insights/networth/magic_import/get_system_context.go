package magicimport

import (
	"encoding/json"

	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/gamma/api/insights/networth/model"
	"github.com/epifi/gamma/api/typesv2"
)

func GetMagicImportContext() string {
	prompt := MagicImportSystemContext
	samplePrompts := &MagicImportPromptResponse{
		AICommentary: "This is a sample AI commentary for all the assets",
		ConventionalAssets: []*ConventionalAssets{
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_ART_AND_ARTEFACTS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_ArtAndArtefacts{
							ArtAndArtefacts: &model.ArtAndArtefacts{
								InvestmentName: "Laptop",
								InvestedValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_GADGETS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Gadgets{
							Gadgets: &model.Gadgets{
								InvestmentName: "Macbook Pro",
								DeviceType:     "laptop",
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
								Condition: "Good",
								YearOfPurchase: &date.Date{
									Year:  2000,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_VEHICLES,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Vehicle{
							Vehicle: &model.Vehicle{
								VehicleType:    "car",
								InvestmentName: "Honda City",
								YearOfPurchase: &date.Date{
									Year:  2000,
									Month: 1,
									Day:   1,
								},
								Condition:        "Good",
								DepreciationRate: 10,
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_CRYPTO,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Crypto{
							Crypto: &model.Crypto{
								InvestmentName: "Bitcoin",
								NumberOfUnits:  0.1,
								CurrentValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        5000000,
								},
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_FURNITURE,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Furniture{
							Furniture: &model.Furniture{
								InvestmentName: "Table chair",
								FurnitureType:  "table",
								YearOfPurchase: &date.Date{
									Year:  2000,
									Month: 1,
									Day:   1,
								},
								Condition: "Good",
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_COLLECTIBLES,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Collectible{
							Collectible: &model.Collectible{
								InvestmentName: "Comic book",
								ItemBrand:      "Into the Spider-Verse",
								ItemType:       "comic book",
								ItemModel:      "comic book",
								Condition:      "Good",
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_JEWELLERY,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Jewellery{
							Jewellery: &model.Jewellery{
								InvestmentName: "Gold Necklace",
								BaseMetal:      "Gold",
								Purity:         "24K",
								WeightInGrams:  100,
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        100000,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_INVESTMENT_INSTRUMENT_TYPE_OTHERS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_Others{
							Others: &model.Others{
								InvestmentName: "New unconventional asset",
								InvestmentType: "furniture",
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
								DateOfPurchase: &date.Date{
									Year:  2000,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_MUTUAL_FUNDS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_MutualFundDetails{
							MutualFundDetails: &model.MutualFundDetails{
								MutualFundName: "Nifty 50",
								NumberOfUnits:  10,
								InvestedValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
								CurrentValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        2000,
								},
								InvestmentDate: &date.Date{
									Year:  2022,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_NPS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_NpsDetails{
							NpsDetails: &model.NPSDetails{
								SchemeName: "BSE",
								InvestedValue: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
								CurrentValue: &money.Money{
									CurrencyCode: "INR",
									Units:        2000,
								},
								InvestmentDate: &date.Date{
									Year:  2022,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
			{
				FileName: "File name from which asset was extracted",
				InvestmentDeclaration: &model.InvestmentDeclaration{
					InstrumentType: typesv2.InvestmentInstrumentType_INDIAN_STOCKS,
					DeclarationDetails: &model.OtherDeclarationDetails{
						Details: &model.OtherDeclarationDetails_IndianStockDetails{
							IndianStockDetails: &model.IndianStockDetails{
								StockName:     "BSE",
								NumberOfUnits: 10,
								InvestedValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        1000,
								},
								CurrentValuePerUnit: &money.Money{
									CurrencyCode: "INR",
									Units:        2000,
								},
								InvestmentDate: &date.Date{
									Year:  2022,
									Month: 1,
									Day:   1,
								},
							},
						},
					},
				},
			},
		},
		UnconventionalAssets: []*UnconventionalAssets{
			{
				Name:         "Kookaburra bat",
				AssetType:    "Sports Equipment",
				Reason:       "This is a cricket bat which is not a conventional asset but has value.",
				AICommentary: "This cricket bat is a valuable item, possibly signed by a famous player.",
			},
		},
	}

	res, err := json.Marshal(samplePrompts)
	if err != nil {
		// ideally this must never fail, preferring method simplicity over error handling
		// This logic will be verified in non-prod via UT execution in CI
		panic(err)
	}
	prompt += string(res) + "\n"

	return prompt
}
