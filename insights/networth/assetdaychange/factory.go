package assetdaychange

//go:generate mockgen -source=factory.go -destination=./mocks/mock_factory.go -package=mocks

import (
	"context"
	"fmt"

	"github.com/google/wire"

	enumsPb "github.com/epifi/gamma/api/insights/networth/enums"
)

var FactoryWireSet = wire.NewSet(
	NewDayChangeCalculatorFactory,
	wire.Bind(new(Factory), new(*FactoryService)),
)

type Factory interface {
	GetCalculator(ctx context.Context, assetType enumsPb.AssetType) (DayChangeCalculator, error)
}

// FactoryService implements Factory
type FactoryService struct {
	mfCalculator           *MutualFundCalculator
	indianStocksCalculator *IndianStocksCalculator
	npsCalculator          *NPSCalculator
}

// NewDayChangeCalculatorFactory creates a new instance of FactoryService
func NewDayChangeCalculatorFactory(
	mfCalculator *MutualFundCalculator,
	indianStocksCalculator *IndianStocksCalculator,
	npsCalculator *NPSCalculator,
) *FactoryService {
	return &FactoryService{
		mfCalculator:           mfCalculator,
		indianStocksCalculator: indianStocksCalculator,
		npsCalculator:          npsCalculator,
	}
}

// GetCalculator returns the day change calculator for the given asset type
func (f *FactoryService) GetCalculator(ctx context.Context, assetType enumsPb.AssetType) (DayChangeCalculator, error) {
	switch assetType {
	case enumsPb.AssetType_ASSET_TYPE_MUTUAL_FUND:
		return f.mfCalculator, nil
	case enumsPb.AssetType_ASSET_TYPE_INDIAN_SECURITIES:
		return f.indianStocksCalculator, nil
	case enumsPb.AssetType_ASSET_TYPE_NPS:
		return f.npsCalculator, nil
	default:
		return nil, fmt.Errorf("no day change calculator available for asset type: %v", assetType)
	}
}
