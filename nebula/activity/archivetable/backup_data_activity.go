package archivetable

import (
	"context"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/log"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"

	nebulaActivity "github.com/epifi/gamma/api/nebula/activity"
	"github.com/epifi/gamma/api/nebula/workflow"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	lg "github.com/epifi/be-common/pkg/logger"
)

type s3BackupResult struct {
	RowsUploaded  int64
	FilesUploaded int64
	BytesUploaded int64
}

func (p *Processor) BackupData(ctx context.Context,
	input *nebulaActivity.BackupDataActivityRequest) (*nebulaActivity.BackupDataActivityResponse, error) {
	if input.GetArchivalConstraint() == workflow.ArchivalConstraint_UNDEFINED {
		// TODO: uncomment this once we have moved to the new version
		// logger.Error("oldest updated at fetch stage failed.", archivetable.LogWithIdentifier(request)...)
		// return nil, epifierrors.ErrInvalidArgument

		// for backward compatibility
		input.ArchivalConstraint = workflow.ArchivalConstraint_UPDATED_AT
	}

	logger := activity.GetLogger(ctx)

	cancelFn := activityPkg.SendPeriodicHeartbeat(ctx, 5*time.Second, func() interface{} {
		return nil
	})
	defer cancelFn()

	date := input.BackupDate.AsTime().In(datetime.IST)
	date = datetime.GetTimeAtStartOfTheDay(date)

	// Cleanup the previous state on S3.
	err := p.cleanUpS3State(ctx, input, date, logger)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient,
			fmt.Sprintf("Failed while cleaning up S3 state for DB %s, %s : %s",
				input.DbConfig.Name, input.TableName, err))
	}

	db, err := p.DbConnAllocator.GetPgdbConn(input.DbConfig)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient,
			fmt.Sprintf("Error while creating connection to PGDB for DB %s : %s", input.DbConfig.Name, err))
	}

	// Backup the data to S3.
	err = p.backupToS3(ctx, db, input, date, logger)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient,
			fmt.Sprintf("Error while backing up data to S3 for DB %s, %s : %s", input.DbConfig.Name, input.TableName, err))
	}

	result := nebulaActivity.BackupDataActivityResponse{
		ResponseHeader: nil,
		BackupDate:     timestampPb.New(date),
	}

	return &result, nil
}

// Cleans up s3 files for the duration we want to sync.
// We want to ensure that no files already exist for the same duration from the last failed run to ensure idempotency.
func (p *Processor) cleanUpS3State(ctx context.Context, input *nebulaActivity.BackupDataActivityRequest,
	date time.Time, logger log.Logger) error {

	bucketName := p.Config.PgdbArchivalConf().ArchivalS3Bucket()
	partitionPath := getPartitionPath(input.DbConfig.Name, input.TableName, date)

	client := p.archivalS3Client

	resp, err := client.ListObjectsV2(
		&s3.ListObjectsV2Input{
			Bucket:  aws.String(bucketName),
			Prefix:  aws.String(partitionPath),
			MaxKeys: aws.Int64(1),
		},
	)
	if err != nil {
		return errors.Wrap(err, "Failed to check if folder exists.")
	}

	if *resp.KeyCount == 0 {

		return nil
	}

	logger.Info("folder not empty. Deleting...", LogWithIdentifier(input, zap.Time(lg.DATE, date),
		zap.String(lg.BUCKET_NAME, bucketName), zap.String(lg.FOLDER_NAME, partitionPath))...)

	// Iterator to delete all files
	iter := s3manager.NewDeleteListIterator(client,
		&s3.ListObjectsInput{
			Bucket: aws.String(bucketName),
			Prefix: aws.String(partitionPath),
		},
	)

	// Use the iterator to delete the files.
	if err = s3manager.NewBatchDeleteWithClient(client).Delete(ctx, iter); err != nil {
		return errors.Wrap(err, fmt.Sprintf("failed to delete folder from S3."))
	}

	return nil
}

func (p *Processor) backupToS3(ctx context.Context, db *gorm.DB, input *nebulaActivity.BackupDataActivityRequest,
	date time.Time, logger log.Logger) error {

	logger.Info("going to create backup for date", LogWithIdentifier(input, zap.Time(lg.DATE, date))...)

	bucketName := p.Config.PgdbArchivalConf().ArchivalS3Bucket()
	awsRegion := p.Config.AWS().Region

	folderPath := getPartitionPath(input.DbConfig.Name, input.TableName, date)

	targetTime := date.Add(24 * time.Hour)
	fromTime := date

	for i := 0; !fromTime.Equal(targetTime); i++ {

		if epificontext.IsContextCancelled(ctx) {
			logger.Info(fmt.Sprintf("context is cancelled"), LogWithIdentifier(input, zap.Time(lg.DATE, date))...)
			return nil
		}

		toTime := datetime.Min(fromTime.Add(input.BatchDuration.AsDuration()), targetTime)

		logger.Info("going to take backup to S3", LogWithIdentifier(input, zap.Time(lg.START_TIME, fromTime), zap.Time(lg.END_TIME, toTime))...)
		objectName := fmt.Sprintf("%sdata_%d.csv", folderPath, i)

		columnName, err := getColumnNameFromArchivalConstraint(input)
		if err != nil {
			return err
		}

		var selectQuery string
		if input.GetArchivalConstraint() == workflow.ArchivalConstraint_DELETED_AT_UNIX {
			selectQuery = fmt.Sprintf("SELECT * FROM %s WHERE %s >= %d AND %s < %d ",
				input.GetTableName(), columnName, fromTime.Unix(), columnName, toTime.Unix())
		} else {
			selectQuery = fmt.Sprintf("SELECT * FROM %s WHERE %s >= ''%s'' AND %s < ''%s'' ",
				input.GetTableName(), columnName, fromTime.Format(time.RFC3339Nano), columnName, toTime.Format(time.RFC3339Nano))
		}

		// Write to S3
		query := fmt.Sprintf("SELECT * FROM aws_s3.query_export_to_s3('%s', '%s', '%s', '%s', options :='format csv, delimiter $$,$$');",
			selectQuery, bucketName, objectName, awsRegion)

		var queryRes s3BackupResult

		if dbErr := db.WithContext(ctx).Raw(query).Scan(&queryRes).Error; dbErr != nil {
			return errors.Wrap(dbErr, "error while pushing data to S3")
		}

		logger.Info("backed up data to S3", LogWithIdentifier(input, zap.Int64(lg.COUNT, queryRes.RowsUploaded), zap.String(lg.FILE_NAME, objectName))...)

		fromTime = toTime

		time.Sleep(input.BatchDelay.AsDuration())
	}

	return nil
}

func getPartitionPath(dbName string, tableName string, date time.Time) string {
	return fmt.Sprintf("%s/%s/date=%s/", dbName, tableName, date.Format(DateOnly))
}
