//go:build wireinject
// +build wireinject

package wire

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/google/wire"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/aws/v2/s3"
	sqsPkg "github.com/epifi/be-common/pkg/aws/v2/sqs"
	pkgWire "github.com/epifi/be-common/pkg/aws/v2/wire"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	idGen "github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/queue"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	connectedaccountPb "github.com/epifi/gamma/api/connected_account"
	caAnalytics "github.com/epifi/gamma/api/connected_account/analytics"
	creditReportV2Pb "github.com/epifi/gamma/api/creditreportv2"
	workerConf "github.com/epifi/gamma/connectedaccount/config/worker"
	"github.com/epifi/gamma/connectedaccount/workflow/activity"

	insightsPkg "github.com/epifi/gamma/insights/pkg"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/docs"
	aaOrderPb "github.com/epifi/gamma/api/order/aa"
	parserPb "github.com/epifi/gamma/api/parser"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	salaryProgramPb "github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/screener"
	segmentPb "github.com/epifi/gamma/api/segment"
	userPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	userIntelPb "github.com/epifi/gamma/api/userintel"
	vgAaPb "github.com/epifi/gamma/api/vendorgateway/aa"
	vgAaIgnosisPb "github.com/epifi/gamma/api/vendorgateway/aa/analytics/ignosis"
	vgBcPb "github.com/epifi/gamma/api/vendorgateway/bouncycastle"
	"github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	ncPb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	woPb "github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/connectedaccount"
	"github.com/epifi/gamma/connectedaccount/analytics"
	"github.com/epifi/gamma/connectedaccount/config"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/consent"
	"github.com/epifi/gamma/connectedaccount/consumer"
	"github.com/epifi/gamma/connectedaccount/crypto"
	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/gamma/connectedaccount/data"
	consentorchestrator "github.com/epifi/gamma/connectedaccount/data/consentorchestrator"
	"github.com/epifi/gamma/connectedaccount/data/fi_types"
	"github.com/epifi/gamma/connectedaccount/dataanalytics"
	"github.com/epifi/gamma/connectedaccount/developer"
	"github.com/epifi/gamma/connectedaccount/developer/processor"
	"github.com/epifi/gamma/connectedaccount/external"
	"github.com/epifi/gamma/connectedaccount/notification"
	"github.com/epifi/gamma/connectedaccount/notification/comms"
	"github.com/epifi/gamma/connectedaccount/property"
	"github.com/epifi/gamma/connectedaccount/transaction"
	"github.com/epifi/gamma/connectedaccount/typedef"
	caTypes "github.com/epifi/gamma/connectedaccount/wire/types"
	anltActProcessor "github.com/epifi/gamma/connectedaccount/workflow/activity/analytics"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/gamma/pkg/feature/release"
)

func NewRedisOptions(conf *genconf.Config) *cfg.RedisOptions {
	return conf.RedisOptions()
}

func InitializeCaService(pgdb types.ConnectedAccountPGDB, vgAaClient vgAaPb.AccountAggregatorClient, conf *genconf.Config,
	consentPublisher typedef.ProcessConsentSqsPublisher, publisher typedef.FetchDataSqsDelayPublisher,
	bcClient vgBcPb.BouncyCastleClient, txnBatchPub typedef.TransactionBatchProcessDelayPublisher,
	txnExternalSnsPub typedef.TransactionEventExternalPublisher, accUpdateExtPub typedef.AccountUpdateEventExternalPublisher,
	createAttemptPub typedef.CreateAttemptSqsPublisher, consentProcessDelayPub typedef.ProcessConsentSqsDelayPublisher,
	broker events.Broker, actorClient actorPb.ActorClient, userClient userPb.UsersClient, userGrpClient userGroupPb.GroupClient,
	commsClient caTypes.CaCommsClientWithInterceptors, sendNotiDelayPub typedef.SendNotificationSqsDelayPublisher,
	fetchDataPub typedef.FetchDataSqsPublisher, processConsentDataRefreshPub typedef.ProcessConsentDataRefreshSqsPublisher,
	captureColumnUpdatePub typedef.CaptureColumnUpdateSqsPublisher, captureHeartbeatAndSendNotificationPub typedef.CaptureHeartbeatAndSendNotificationSqsPublisher,
	docsClient docs.DocsClient, firstDataPullDelayPub typedef.FirstDataPullSqsDelayPublisher,
	redisClient types.ConnectedAccountRedisStore, firstDataPullPub typedef.FirstDataPullSqsPublisher, onboardingClient onboardingPb.OnboardingClient,
	savingsClient savingsPb.SavingsClient, salaryProgramClient salaryProgramPb.SalaryProgramClient, nameCheckClient ncPb.UNNameCheckClient,
	wealthOnboardingClient woPb.WealthOnboardingClient, segmentationServiceClient segmentPb.SegmentationServiceClient,
	incomeEstClient incomeestimator.IncomeEstimatorClient, parserClient parserPb.ParserClient, piClient piPb.PiClient,
	accountPIRelationClient accountPiPb.AccountPIRelationClient, aaTxnBackfillPublisher typedef.AaTxnBackfillPublisher,
	caTxnBackfillS3Client caTypes.CATxnBackfillS3Client, aaOrderClient aaOrderPb.AccountAggregatorClient,
	uiClient userIntelPb.UserIntelServiceClient, screenerClient screener.ScreenerClient, docsS3Client caTypes.DocsS3Client,
	preapprovedloanClient preapprovedloan.PreApprovedLoanClient, caAccountDataSyncPublisher typedef.AccountDataSyncExternalPublisher,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB], creditReportClient creditReportV2Pb.CreditReportManagerClient,
	connectedAccountClient connectedaccountPb.ConnectedAccountClient) *connectedaccount.CaService {
	wire.Build(
		caTypes.CommsClientProvider,
		typedef.ReleaseConfigProvider,
		connectedaccount.NewCaService,
		fi_types.NewDepositProcessor,
		fi_types.NewRecurringDepositProcessor,
		fi_types.NewTermDepositProcessor,
		fi_types.NewEquitiesProcessor,
		fi_types.NewEtfProcessor,
		fi_types.NewReitProcessor,
		fi_types.NewInvitProcessor,
		fi_types.NewNpsProcessor,
		dao.DaoCacheWireSet,
		NewRedisOptions,
		datetime.WireDefaultTimeSet,
		cache.RedisStorageWithHystrixWireSet,
		wire.NewSet(transaction.NewAaTransactionProcessor, wire.Bind(new(transaction.Processor), new(*transaction.AaTransactionProcessor))),
		wire.NewSet(data.NewFIFactory, wire.Bind(new(data.IFIFactory), new(*data.FIFactory))),
		wire.NewSet(fi_types.NewTxnBatchProcessor, wire.Bind(new(fi_types.BatchProcessor), new(*fi_types.TxnBatchProcessor))),
		wire.NewSet(fi_types.NewTransactionExternalProcessor, wire.Bind(new(fi_types.FITransactionExternalProcessor), new(*fi_types.TransactionExternalProcessor))),
		wire.NewSet(consent.NewManagerService, wire.Bind(new(consent.Manager), new(*consent.ManagerService))),
		wire.NewSet(data.NewProcessorService, wire.Bind(new(data.Processor), new(*data.ProcessorService))),
		wire.NewSet(consentorchestrator.NewAccountConsentOrchestratorSvc, wire.Bind(new(consentorchestrator.AccountConsentOrchestrator), new(*consentorchestrator.AccountConsentOrchestratorSvc))),
		wire.NewSet(crypto.NewEdeService, wire.Bind(new(crypto.Ede), new(*crypto.EdeService))),
		wire.NewSet(dao.NewConsentRequestDaoImpl, wire.Bind(new(dao.ConsentRequestDao), new(*dao.ConsentRequestDaoImpl))),
		wire.NewSet(dao.NewConnectionFlowDaoImpl, wire.Bind(new(dao.ConnectionFlowDao), new(*dao.ConnectionFlowDaoImpl))),
		wire.NewSet(dao.NewConsentDaoImpl, wire.Bind(new(dao.ConsentDao), new(*dao.ConsentDaoImpl))),
		wire.NewSet(dao.NewDataFetchAttemptDaoImpl, wire.Bind(new(dao.DataFetchAttemptDao), new(*dao.AADataFetchAttemptDaoImpl))),
		wire.NewSet(dao.NewDataProcessAttemptDaoImpl, wire.Bind(new(dao.DataProcessAttemptDao), new(*dao.DataProcessAttemptDaoImpl))),
		wire.NewSet(dao.NewAaRecurringDepositFiDaoImpl, wire.Bind(new(dao.AaRecurringDepositFiDao), new(*dao.AaRecurringDepositFiDaoImpl))),
		wire.NewSet(dao.NewAaTermDepositFiDaoImpl, wire.Bind(new(dao.AaTermDepositFiDao), new(*dao.AaTermDepositFiDaoImpl))),
		wire.NewSet(dao.NewAaBatchProcessorDaoImpl, wire.Bind(new(dao.AaBatchProcessDao), new(*dao.AaBatchProcessorDaoImpl))),
		wire.NewSet(dao.NewConsentAccountMappingDaoImpl, wire.Bind(new(dao.ConsentAccountMappingDao), new(*dao.ConsentAccountMappingDaoImpl))),
		wire.NewSet(dao.NewAaUserBankPreferenceDaoImpl, wire.Bind(new(dao.AaUserBankPreferenceDao), new(*dao.AaUserBankPreferenceDaImpl))),
		wire.NewSet(external.NewAccountExtProc, wire.Bind(new(external.AccountExtProcessor), new(*external.AccountExtProc))),
		wire.NewSet(property.NewProperty, wire.Bind(new(property.CaProperties), new(*property.Property))),
		wire.NewSet(notification.NewSenderSvc, wire.Bind(new(notification.Sender), new(*notification.SenderSvc))),
		wire.NewSet(dao.NewAaNotificationDaoImpl, wire.Bind(new(dao.AaNotificationDao), new(*dao.AaNotificationDaoImpl))),
		wire.NewSet(notification.NewCommsFactorySvc, wire.Bind(new(notification.CommsFactory), new(*notification.CommsFactorySvc))),
		wire.NewSet(dao.NewAaUserHeartbeatDaoImpl, wire.Bind(new(dao.AaUserHeartbeatDao), new(*dao.AaUserHeartbeatDaoImpl))),
		wire.NewSet(dao.NewAaEquityFiDaoImpl, wire.Bind(new(dao.AaEquityFiDao), new(*dao.AaEquityFiDaoImpl))),
		wire.NewSet(dao.NewAaEtfFiDaoImpl, wire.Bind(new(dao.AaEtfFiDao), new(*dao.AaEtfFiDaoImpl))),
		wire.NewSet(dao.NewAaReitFiDaoImpl, wire.Bind(new(dao.AaReitDao), new(*dao.AaReitFiDaoImpl))),
		wire.NewSet(dao.NewAaInvitFiDaoImpl, wire.Bind(new(dao.AaInvitDao), new(*dao.AaInvitFiDaoImpl))),
		dao.AaNpsFiDaoWireSet,
		caPkg.InMemoryAaCacheWireSet,
		comms.NewFirstDataPullFail,
		comms.NewFirstDataPullSuccess,
		comms.NewAccountDeLinkedAA,
		comms.NewConsentPausedAA,
		comms.NewAaHeartbeatUp,
		comms.NewAaConsentExpired,
		release.EvaluatorWireSet,
		storageV2.DefaultTxnExecutorWireSet,
		idGen.WireSet,
		idGen.NewClock,
		types.ConnectedAccountPGDBGormDBProvider,
		insightsPkg.InsightsPanProcessorWireSet,
		fi_types.DataValidationProcessorWireSet,
		insightsPkg.InsightsNameCheckProcessorWireSet,
	)
	return &connectedaccount.CaService{}
}

func InitializeCaConsumerService(ctx context.Context, pgdb types.ConnectedAccountPGDB, vgAaClient vgAaPb.AccountAggregatorClient, genconf *genconf.Config, awsConf aws.Config, conf *config.Config,
	bcClient vgBcPb.BouncyCastleClient, txnBatchPub typedef.TransactionBatchProcessDelayPublisher,
	txnExternalSnsPub typedef.TransactionEventExternalPublisher,
	accUpdateExtPub typedef.AccountUpdateEventExternalPublisher,
	createAttemptPub typedef.CreateAttemptSqsPublisher, dataFetchDelayPub typedef.FetchDataSqsDelayPublisher, broker events.Broker,
	actorClient actorPb.ActorClient, userClient userPb.UsersClient, userGrpClient userGroupPb.GroupClient, commsClient caTypes.CaCommsClientWithInterceptors,
	sendNotiDelayPub typedef.SendNotificationSqsDelayPublisher, fetchDataPub typedef.FetchDataSqsPublisher, onboardingClient onboardingPb.OnboardingClient, savingsClient savingsPb.SavingsClient, salaryProgramClient salaryProgramPb.SalaryProgramClient,
	processConsentDataRefreshPub typedef.ProcessConsentDataRefreshSqsPublisher, captureColumnUpdatePub typedef.CaptureColumnUpdateSqsPublisher, captureHeartbeatAndSendNotificationPub typedef.CaptureHeartbeatAndSendNotificationSqsPublisher, firstDataPullDelayPub typedef.FirstDataPullSqsDelayPublisher, redisClient types.ConnectedAccountRedisStore, firstDataPullPub typedef.FirstDataPullSqsPublisher, caAccountDataSyncPublisher typedef.AccountDataSyncExternalPublisher, CaUserTxnDataS3Client caTypes.CaUserTxnDataS3Client, nameCheckClient ncPb.UNNameCheckClient) *consumer.CaConsumer {
	wire.Build(
		caTypes.CommsClientProvider,
		consumer.NewCaConsumer,
		fi_types.NewDepositProcessor,
		fi_types.NewRecurringDepositProcessor,
		fi_types.NewTermDepositProcessor,
		fi_types.NewEquitiesProcessor,
		fi_types.NewEtfProcessor,
		fi_types.NewReitProcessor,
		fi_types.NewInvitProcessor,
		fi_types.NewNpsProcessor,
		dao.DaoCacheWireSet,
		NewRedisOptions,
		lock.DefaultLockMangerWireSet,
		datetime.WireDefaultTimeSet,
		cache.RedisStorageWithHystrixWireSet,
		wire.NewSet(data.NewFIFactory, wire.Bind(new(data.IFIFactory), new(*data.FIFactory))),
		wire.NewSet(fi_types.NewTxnBatchProcessor, wire.Bind(new(fi_types.BatchProcessor), new(*fi_types.TxnBatchProcessor))),
		wire.NewSet(fi_types.NewTransactionExternalProcessor, wire.Bind(new(fi_types.FITransactionExternalProcessor), new(*fi_types.TransactionExternalProcessor))),
		wire.NewSet(consent.NewManagerService, wire.Bind(new(consent.Manager), new(*consent.ManagerService))),
		wire.NewSet(data.NewProcessorService, wire.Bind(new(data.Processor), new(*data.ProcessorService))),
		wire.NewSet(consentorchestrator.NewAccountConsentOrchestratorSvc, wire.Bind(new(consentorchestrator.AccountConsentOrchestrator), new(*consentorchestrator.AccountConsentOrchestratorSvc))),
		wire.NewSet(crypto.NewEdeService, wire.Bind(new(crypto.Ede), new(*crypto.EdeService))),
		wire.NewSet(dao.NewConsentRequestDaoImpl, wire.Bind(new(dao.ConsentRequestDao), new(*dao.ConsentRequestDaoImpl))),
		wire.NewSet(dao.NewConsentDaoImpl, wire.Bind(new(dao.ConsentDao), new(*dao.ConsentDaoImpl))),
		wire.NewSet(dao.NewDataFetchAttemptDaoImpl, wire.Bind(new(dao.DataFetchAttemptDao), new(*dao.AADataFetchAttemptDaoImpl))),
		wire.NewSet(dao.NewDataProcessAttemptDaoImpl, wire.Bind(new(dao.DataProcessAttemptDao), new(*dao.DataProcessAttemptDaoImpl))),
		wire.NewSet(dao.NewAaRecurringDepositFiDaoImpl, wire.Bind(new(dao.AaRecurringDepositFiDao), new(*dao.AaRecurringDepositFiDaoImpl))),
		wire.NewSet(dao.NewAaTermDepositFiDaoImpl, wire.Bind(new(dao.AaTermDepositFiDao), new(*dao.AaTermDepositFiDaoImpl))),
		wire.NewSet(dao.NewAaBatchProcessorDaoImpl, wire.Bind(new(dao.AaBatchProcessDao), new(*dao.AaBatchProcessorDaoImpl))),
		wire.NewSet(dao.NewConsentAccountMappingDaoImpl, wire.Bind(new(dao.ConsentAccountMappingDao), new(*dao.ConsentAccountMappingDaoImpl))),
		wire.NewSet(external.NewAccountExtProc, wire.Bind(new(external.AccountExtProcessor), new(*external.AccountExtProc))),
		wire.NewSet(property.NewProperty, wire.Bind(new(property.CaProperties), new(*property.Property))),
		wire.NewSet(notification.NewSenderSvc, wire.Bind(new(notification.Sender), new(*notification.SenderSvc))),
		wire.NewSet(dao.NewAaNotificationDaoImpl, wire.Bind(new(dao.AaNotificationDao), new(*dao.AaNotificationDaoImpl))),
		wire.NewSet(dao.NewAaAccountColumnHistoryDaoImpl, wire.Bind(new(dao.AaAccountColumnHistoryDao), new(*dao.AaAccountColumnHistoryDaoImpl))),
		wire.NewSet(notification.NewCommsFactorySvc, wire.Bind(new(notification.CommsFactory), new(*notification.CommsFactorySvc))),
		wire.NewSet(dao.NewAaUserHeartbeatDaoImpl, wire.Bind(new(dao.AaUserHeartbeatDao), new(*dao.AaUserHeartbeatDaoImpl))),
		wire.NewSet(dao.NewAaEquityFiDaoImpl, wire.Bind(new(dao.AaEquityFiDao), new(*dao.AaEquityFiDaoImpl))),
		wire.NewSet(dao.NewAaEtfFiDaoImpl, wire.Bind(new(dao.AaEtfFiDao), new(*dao.AaEtfFiDaoImpl))),
		wire.NewSet(dao.NewAaReitFiDaoImpl, wire.Bind(new(dao.AaReitDao), new(*dao.AaReitFiDaoImpl))),
		wire.NewSet(dao.NewAaInvitFiDaoImpl, wire.Bind(new(dao.AaInvitDao), new(*dao.AaInvitFiDaoImpl))),
		dao.AaNpsFiDaoWireSet,
		comms.NewFirstDataPullFail,
		comms.NewFirstDataPullSuccess,
		comms.NewAccountDeLinkedAA,
		comms.NewConsentPausedAA,
		comms.NewAaHeartbeatUp,
		comms.NewAaConsentExpired,
		ProcessDataPublisherProvider,
		FetchDecryptDataPublisherProvider,
		storageV2.DefaultTxnExecutorWireSet,
		types.ConnectedAccountPGDBGormDBProvider,
		typedef.ReleaseConfigProvider,
		release.EvaluatorWireSet,
		fi_types.DataValidationProcessorWireSet,
		insightsPkg.InsightsNameCheckProcessorWireSet,
	)
	return &consumer.CaConsumer{}
}

func ProcessDataPublisherProvider(ctx context.Context, awsConf aws.Config, conf *config.Config) typedef.ProcessDataPublisher {
	return newExtendedPublisher(ctx, awsConf, conf.ProcessDataSqsPublisher.GetQueueName(), conf.ProcessDataSqsPublisher.GetBucketName())
}

func newExtendedPublisher(ctx context.Context, awsConf aws.Config, queueName, bucketName string) queue.ExtendedPublisher {
	return pkgWire.InitializeExtendedPublisher(ctx, awsConf, queue.NewDefaultMessage(),
		sqsPkg.QueueName(queueName), bucketName, sqsPkg.ServiceName(cfg.CONNECTED_ACC_ORDER_SERVICE))
}

func FetchDecryptDataPublisherProvider(ctx context.Context, awsConf aws.Config, conf *config.Config) typedef.DecryptDataPublisher {
	return newExtendedPublisher(ctx, awsConf, conf.DecryptDataSqsPublisher.GetQueueName(), conf.DecryptDataSqsPublisher.GetBucketName())
}

func InitializeCaDevService(pgdb types.ConnectedAccountPGDB, redisClient types.ConnectedAccountRedisStore, conf *genconf.Config, featureEngineeringDb types.FeatureEngineeringPGDB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB]) *developer.CaDevService {
	wire.Build(
		dao.DaoCacheWireSet,
		NewRedisOptions,
		cache.RedisStorageWithHystrixWireSet,
		wire.NewSet(dao.NewConsentDaoImpl, wire.Bind(new(dao.ConsentDao), new(*dao.ConsentDaoImpl))),
		wire.NewSet(dao.NewDataFetchAttemptDaoImpl, wire.Bind(new(dao.DataFetchAttemptDao), new(*dao.AADataFetchAttemptDaoImpl))),
		wire.NewSet(dao.NewConsentRequestDaoImpl, wire.Bind(new(dao.ConsentRequestDao), new(*dao.ConsentRequestDaoImpl))),
		wire.NewSet(dao.NewDataProcessAttemptDaoImpl, wire.Bind(new(dao.DataProcessAttemptDao), new(*dao.DataProcessAttemptDaoImpl))),
		wire.NewSet(dao.NewAaBatchProcessorDaoImpl, wire.Bind(new(dao.AaBatchProcessDao), new(*dao.AaBatchProcessorDaoImpl))),
		wire.NewSet(dao.NewAaRecurringDepositFiDaoImpl, wire.Bind(new(dao.AaRecurringDepositFiDao), new(*dao.AaRecurringDepositFiDaoImpl))),
		wire.NewSet(dao.NewAaTermDepositFiDaoImpl, wire.Bind(new(dao.AaTermDepositFiDao), new(*dao.AaTermDepositFiDaoImpl))),
		wire.NewSet(dao.NewConsentAccountMappingDaoImpl, wire.Bind(new(dao.ConsentAccountMappingDao), new(*dao.ConsentAccountMappingDaoImpl))),
		wire.NewSet(dao.NewAaUserBankPreferenceDaoImpl, wire.Bind(new(dao.AaUserBankPreferenceDao), new(*dao.AaUserBankPreferenceDaImpl))),
		wire.NewSet(dao.NewAaNotificationDaoImpl, wire.Bind(new(dao.AaNotificationDao), new(*dao.AaNotificationDaoImpl))),
		wire.NewSet(dao.NewAaEquityFiDaoImpl, wire.Bind(new(dao.AaEquityFiDao), new(*dao.AaEquityFiDaoImpl))),
		wire.NewSet(dao.NewAaEtfFiDaoImpl, wire.Bind(new(dao.AaEtfFiDao), new(*dao.AaEtfFiDaoImpl))),
		wire.NewSet(dao.NewAaReitFiDaoImpl, wire.Bind(new(dao.AaReitDao), new(*dao.AaReitFiDaoImpl))),
		wire.NewSet(dao.NewAaInvitFiDaoImpl, wire.Bind(new(dao.AaInvitDao), new(*dao.AaInvitFiDaoImpl))),
		wire.NewSet(dao.NewAnalysedUserDaoImpl, wire.Bind(new(dao.AnalysedUserDao), new(*dao.AnalysedUserDaoImpl))),
		wire.NewSet(dao.NewAnalysisAttemptDaoImpl, wire.Bind(new(dao.AnalysisAttemptDao), new(*dao.AnalysisAttemptDaoImpl))),
		wire.NewSet(dao.NewAnalysisRequestDaoImpl, wire.Bind(new(dao.AnalysisRequestsDao), new(*dao.AnalysisRequestDaoImpl))),
		dao.AaNpsFiDaoWireSet,
		processor.NewDevCaEntity,
		processor.NewDevDataAttemptEntity,
		processor.NewDevCaAccountEntity,
		processor.NewDevCaBankPreferenceEntity,
		processor.NewDevCaNotificationEntity,
		processor.NewDevCaTransactionEntity,
		processor.NewDevCaAnalysedUserEntity,
		processor.NewDevCaAnalysisAttemptEntity,
		processor.NewDevCaAnalysisRequestEntity,
		developer.NewDevFactory,
		developer.NewCaDevService,
		storageV2.DefaultTxnExecutorWireSet,
		types.ConnectedAccountPGDBGormDBProvider,
	)
	return &developer.CaDevService{}
}

func InitialiseDataAnalyticsService(
	featureEngineeringDb types.FeatureEngineeringPGDB,
	dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	temporalClient typedef.ConnectedAccountClient,
	caAnalyticsClient caAnalytics.AnalyticsClient,
) *dataanalytics.Service {
	wire.Build(
		dao.DaoAnalyticsWireSet,
		dataanalytics.NewService,
		typedef.ConnectedAccountClientProvider,
	)
	return &dataanalytics.Service{}
}

func InitiateAaAnalyserService(
	caPgdb types.ConnectedAccountPGDB, dbConnProvider *storageV2.DBResourceProvider[*gorm.DB], temporalClient typedef.ConnectedAccountClient,
	vmClient vmPb.VendorMappingServiceClient,
	caAnalyticsS3Client caTypes.CaAnalyticsS3Client,
	caClient connectedaccountPb.ConnectedAccountClient,
) *analytics.Service {
	wire.Build(
		analytics.NewService,
		dao.DaoAnalyticsWireSet,
		typedef.ConnectedAccountClientProvider,
		wire.Bind(new(s3.S3Client), new(caTypes.CaAnalyticsS3Client)),
	)
	return &analytics.Service{}
}

func InitiateAnalysisIgnosisActivityProcessor(
	conf *workerConf.Config, caPgdb types.ConnectedAccountPGDB, dbConnProvider *storageV2.DBResourceProvider[*gorm.DB],
	ignosisVgClient vgAaIgnosisPb.IgnosisAaAnalyticsServiceClient, vmClient vmPb.VendorMappingServiceClient,
	caClient connectedaccountPb.ConnectedAccountClient, caAnalyticsS3Client caTypes.CaAnalyticsS3Client,
) *anltActProcessor.Processor {
	wire.Build(
		anltActProcessor.NewProcessor,
		dao.DaoAnalyticsWireSet,
		wire.Bind(new(s3.S3Client), new(caTypes.CaAnalyticsS3Client)),
	)
	return &anltActProcessor.Processor{}
}

func InitialiseActivities(
	caPgDb types.ConnectedAccountPGDB, fePgDb types.FeatureEngineeringPGDB,
	analyticsDbConnProvider *storageV2.DBResourceProvider[*gorm.DB], caAnalyticsClient caAnalytics.AnalyticsClient,
	broker events.Broker,
) *activity.Activities {
	wire.Build(
		dao.DaoAnalyticsWireSet,
		activity.NewActivities,
	)
	return &activity.Activities{}
}
