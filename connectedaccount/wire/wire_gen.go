// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/aws/v2/wire"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/connected_account/analytics"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/docs"
	aa2 "github.com/epifi/gamma/api/order/aa"
	"github.com/epifi/gamma/api/parser"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/screener"
	"github.com/epifi/gamma/api/segment"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/aa"
	"github.com/epifi/gamma/api/vendorgateway/aa/analytics/ignosis"
	"github.com/epifi/gamma/api/vendorgateway/bouncycastle"
	"github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/api/wealthonboarding"
	"github.com/epifi/gamma/connectedaccount"
	analytics2 "github.com/epifi/gamma/connectedaccount/analytics"
	"github.com/epifi/gamma/connectedaccount/config"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/config/worker"
	"github.com/epifi/gamma/connectedaccount/consent"
	"github.com/epifi/gamma/connectedaccount/consumer"
	"github.com/epifi/gamma/connectedaccount/crypto"
	"github.com/epifi/gamma/connectedaccount/dao"
	"github.com/epifi/gamma/connectedaccount/data"
	"github.com/epifi/gamma/connectedaccount/data/consentorchestrator"
	"github.com/epifi/gamma/connectedaccount/data/fi_types"
	"github.com/epifi/gamma/connectedaccount/dataanalytics"
	"github.com/epifi/gamma/connectedaccount/developer"
	"github.com/epifi/gamma/connectedaccount/developer/processor"
	"github.com/epifi/gamma/connectedaccount/external"
	"github.com/epifi/gamma/connectedaccount/notification"
	"github.com/epifi/gamma/connectedaccount/notification/comms"
	"github.com/epifi/gamma/connectedaccount/property"
	"github.com/epifi/gamma/connectedaccount/transaction"
	"github.com/epifi/gamma/connectedaccount/typedef"
	types2 "github.com/epifi/gamma/connectedaccount/wire/types"
	"github.com/epifi/gamma/connectedaccount/workflow/activity"
	analytics3 "github.com/epifi/gamma/connectedaccount/workflow/activity/analytics"
	"github.com/epifi/gamma/insights/pkg"
	connectedaccount2 "github.com/epifi/gamma/pkg/connectedaccount"
	"github.com/epifi/gamma/pkg/feature/release"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeCaService(pgdb types.ConnectedAccountPGDB, vgAaClient aa.AccountAggregatorClient, conf *genconf.Config, consentPublisher typedef.ProcessConsentSqsPublisher, publisher typedef.FetchDataSqsDelayPublisher, bcClient bouncycastle.BouncyCastleClient, txnBatchPub typedef.TransactionBatchProcessDelayPublisher, txnExternalSnsPub typedef.TransactionEventExternalPublisher, accUpdateExtPub typedef.AccountUpdateEventExternalPublisher, createAttemptPub typedef.CreateAttemptSqsPublisher, consentProcessDelayPub typedef.ProcessConsentSqsDelayPublisher, broker events.Broker, actorClient actor.ActorClient, userClient user.UsersClient, userGrpClient group.GroupClient, commsClient types2.CaCommsClientWithInterceptors, sendNotiDelayPub typedef.SendNotificationSqsDelayPublisher, fetchDataPub typedef.FetchDataSqsPublisher, processConsentDataRefreshPub typedef.ProcessConsentDataRefreshSqsPublisher, captureColumnUpdatePub typedef.CaptureColumnUpdateSqsPublisher, captureHeartbeatAndSendNotificationPub typedef.CaptureHeartbeatAndSendNotificationSqsPublisher, docsClient docs.DocsClient, firstDataPullDelayPub typedef.FirstDataPullSqsDelayPublisher, redisClient types.ConnectedAccountRedisStore, firstDataPullPub typedef.FirstDataPullSqsPublisher, onboardingClient onboarding.OnboardingClient, savingsClient savings.SavingsClient, salaryProgramClient salaryprogram.SalaryProgramClient, nameCheckClient namecheck.UNNameCheckClient, wealthOnboardingClient wealthonboarding.WealthOnboardingClient, segmentationServiceClient segment.SegmentationServiceClient, incomeEstClient incomeestimator.IncomeEstimatorClient, parserClient parser.ParserClient, piClient paymentinstrument.PiClient, accountPIRelationClient account_pi.AccountPIRelationClient, aaTxnBackfillPublisher typedef.AaTxnBackfillPublisher, caTxnBackfillS3Client types2.CATxnBackfillS3Client, aaOrderClient aa2.AccountAggregatorClient, uiClient userintel.UserIntelServiceClient, screenerClient screener.ScreenerClient, docsS3Client types2.DocsS3Client, preapprovedloanClient preapprovedloan.PreApprovedLoanClient, caAccountDataSyncPublisher typedef.AccountDataSyncExternalPublisher, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], creditReportClient creditreportv2.CreditReportManagerClient, connectedAccountClient connected_account.ConnectedAccountClient) *connectedaccount.CaService {
	consentRequestDaoImpl := dao.NewConsentRequestDaoImpl(pgdb)
	consentDaoImpl := dao.NewConsentDaoImpl(pgdb)
	db := types.ConnectedAccountPGDBGormDBProvider(pgdb)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	consentAccountMappingDaoImpl := dao.NewConsentAccountMappingDaoImpl(pgdb, gormTxnExecutor)
	aaAccountDaoImpl := dao.NewAaAccountDaoImpl(pgdb)
	client := types.ConnectedAccountRedisStoreProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	redisOptions := NewRedisOptions(conf)
	hystrixCommand := redisOptions.HystrixCommand
	cacheStorageWithHystrix := cache.NewRedisCacheStorageWithHystrix(redisCacheStorage, hystrixCommand)
	aaAccountDaoCache := dao.NewAaAccountDaoCache(aaAccountDaoImpl, cacheStorageWithHystrix, conf)
	accountExtProc := external.NewAccountExtProc(accUpdateExtPub)
	featureReleaseConfig := typedef.ReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	propertyProperty := property.NewProperty(userGrpClient, userClient, actorClient, conf, evaluator)
	managerService := consent.NewManagerService(conf, vgAaClient, consentRequestDaoImpl, consentDaoImpl, consentAccountMappingDaoImpl, aaAccountDaoCache, createAttemptPub, accountExtProc, propertyProperty, gormTxnExecutor, onboardingClient, savingsClient, salaryProgramClient)
	aaDataFetchAttemptDaoImpl := dao.NewDataFetchAttemptDaoImpl(pgdb)
	edeService := crypto.NewEdeService(bcClient)
	dataProcessAttemptDaoImpl := dao.NewDataProcessAttemptDaoImpl(pgdb)
	aaBatchProcessorDaoImpl := dao.NewAaBatchProcessorDaoImpl(pgdb)
	aaDepositFiDaoImpl := dao.NewAaDepositFiDaoImpl(pgdb)
	aaDepositFiDaoCache := dao.NewAaDepositFiDaoCache(aaDepositFiDaoImpl, cacheStorageWithHystrix, conf)
	aaTransactionDaoImpl := dao.NewAaTransactionDaoImpl(pgdb)
	aaTransactionDaoCache := dao.NewAaTransactionDaoCache(aaTransactionDaoImpl, cacheStorageWithHystrix, conf)
	txnBatchProcessor := fi_types.NewTxnBatchProcessor(aaBatchProcessorDaoImpl, aaDataFetchAttemptDaoImpl, consentDaoImpl, conf, gormTxnExecutor)
	transactionExternalProcessor := fi_types.NewTransactionExternalProcessor(txnExternalSnsPub)
	depositProcessor := fi_types.NewDepositProcessor(aaAccountDaoCache, aaDepositFiDaoCache, aaTransactionDaoCache, conf, aaBatchProcessorDaoImpl, txnBatchPub, txnBatchProcessor, transactionExternalProcessor, captureColumnUpdatePub, consentDaoImpl, gormTxnExecutor, caAccountDataSyncPublisher)
	aaRecurringDepositFiDaoImpl := dao.NewAaRecurringDepositFiDaoImpl(pgdb)
	recurringDepositProcessor := fi_types.NewRecurringDepositProcessor(aaAccountDaoCache, aaRecurringDepositFiDaoImpl, aaTransactionDaoCache, conf, aaBatchProcessorDaoImpl, txnBatchPub, txnBatchProcessor, transactionExternalProcessor, gormTxnExecutor, consentDaoImpl)
	aaTermDepositFiDaoImpl := dao.NewAaTermDepositFiDaoImpl(pgdb)
	termDepositProcessor := fi_types.NewTermDepositProcessor(aaAccountDaoCache, aaTermDepositFiDaoImpl, aaTransactionDaoCache, conf, aaBatchProcessorDaoImpl, txnBatchPub, txnBatchProcessor, transactionExternalProcessor, gormTxnExecutor, consentDaoImpl)
	aaEquityFiDaoImpl := dao.NewAaEquityFiDaoImpl(pgdb)
	nameCheckProcessor := pkg.NewInsightNameCheckProcessor(userClient, nameCheckClient)
	dataValidationProcessor := fi_types.NewDataValidationProcessor(userClient, nameCheckProcessor)
	equitiesProcessor := fi_types.NewEquitiesProcessor(conf, consentDaoImpl, aaAccountDaoCache, aaEquityFiDaoImpl, aaTransactionDaoCache, captureColumnUpdatePub, gormTxnExecutor, txnBatchProcessor, dataValidationProcessor, caAccountDataSyncPublisher)
	aaEtfFiDaoImpl := dao.NewAaEtfFiDaoImpl(pgdb)
	etfProcessor := fi_types.NewEtfProcessor(conf, consentDaoImpl, aaAccountDaoCache, aaEtfFiDaoImpl, aaTransactionDaoCache, captureColumnUpdatePub, gormTxnExecutor, txnBatchProcessor, dataValidationProcessor)
	aaReitFiDaoImpl := dao.NewAaReitFiDaoImpl(pgdb)
	reitProcessor := fi_types.NewReitProcessor(conf, consentDaoImpl, aaAccountDaoCache, aaReitFiDaoImpl, aaTransactionDaoCache, captureColumnUpdatePub, gormTxnExecutor, txnBatchProcessor, dataValidationProcessor)
	aaInvitFiDaoImpl := dao.NewAaInvitFiDaoImpl(pgdb)
	invitProcessor := fi_types.NewInvitProcessor(conf, consentDaoImpl, aaAccountDaoCache, aaInvitFiDaoImpl, aaTransactionDaoCache, captureColumnUpdatePub, gormTxnExecutor, txnBatchProcessor, dataValidationProcessor)
	aaNpsFiDaoImpl := dao.NewAaNpsFiDaoImpl(pgdb)
	npsProcessor := fi_types.NewNpsProcessor(conf, consentDaoImpl, aaAccountDaoCache, aaNpsFiDaoImpl, aaTransactionDaoCache, captureColumnUpdatePub, gormTxnExecutor, txnBatchProcessor, dataValidationProcessor, caAccountDataSyncPublisher)
	fiFactory := data.NewFIFactory(depositProcessor, recurringDepositProcessor, termDepositProcessor, equitiesProcessor, etfProcessor, reitProcessor, invitProcessor, npsProcessor)
	aaNotificationDaoImpl := dao.NewAaNotificationDaoImpl(pgdb)
	firstDataPullFail := comms.NewFirstDataPullFail(conf, aaAccountDaoCache)
	firstDataPullSuccess := comms.NewFirstDataPullSuccess(conf, aaAccountDaoCache)
	accountDeLinkedAA := comms.NewAccountDeLinkedAA(conf)
	consentPausedAA := comms.NewConsentPausedAA(conf, aaAccountDaoCache)
	aaHeartbeatUp := comms.NewAaHeartbeatUp(conf)
	accountConsentOrchestratorSvc := consentorchestrator.NewAccountConsentOrchestratorSvc(consentAccountMappingDaoImpl, aaAccountDaoCache, managerService, consentDaoImpl, aaTransactionDaoCache, accUpdateExtPub, consentRequestDaoImpl, vgAaClient, conf, propertyProperty, gormTxnExecutor)
	aaConsentExpired := comms.NewAaConsentExpired(conf, accountConsentOrchestratorSvc)
	commsFactorySvc := notification.NewCommsFactorySvc(firstDataPullFail, firstDataPullSuccess, accountDeLinkedAA, consentPausedAA, aaHeartbeatUp, aaConsentExpired)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	senderSvc := notification.NewSenderSvc(aaNotificationDaoImpl, commsFactorySvc, actorClient, userClient, commsCommsClient, conf, sendNotiDelayPub)
	defaultTime := datetime.NewDefaultTime()
	processorService := data.NewProcessorService(edeService, vgAaClient, aaDataFetchAttemptDaoImpl, consentDaoImpl, conf, dataProcessAttemptDaoImpl, aaBatchProcessorDaoImpl, fiFactory, aaAccountDaoCache, aaTransactionDaoCache, consentRequestDaoImpl, senderSvc, gormTxnExecutor, evaluator, defaultTime)
	aaUserBankPreferenceDaImpl := dao.NewAaUserBankPreferenceDaoImpl(pgdb)
	aaUserHeartbeatDaoImpl := dao.NewAaUserHeartbeatDaoImpl(pgdb)
	cacheService := connectedaccount2.NewCacheService(vgAaClient)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	aaTransactionProcessor := transaction.NewAaTransactionProcessor(processorService, parserClient, userClient, actorClient, piClient, accountPIRelationClient, aaTxnBackfillPublisher, caTxnBackfillS3Client)
	connectionFlowDaoImpl := dao.NewConnectionFlowDaoImpl(pgdb)
	panProcessor := pkg.NewUnverifiedPanProcessor(userClient, creditReportClient, connectedAccountClient)
	caService := connectedaccount.NewCaService(managerService, consentPublisher, consentRequestDaoImpl, consentDaoImpl, aaDataFetchAttemptDaoImpl, aaAccountDaoCache, processorService, publisher, conf, accountConsentOrchestratorSvc, consentProcessDelayPub, aaUserBankPreferenceDaImpl, broker, propertyProperty, accountExtProc, aaTransactionDaoCache, transactionExternalProcessor, fiFactory, processConsentDataRefreshPub, captureColumnUpdatePub, captureHeartbeatAndSendNotificationPub, aaUserHeartbeatDaoImpl, aaDepositFiDaoCache, aaRecurringDepositFiDaoImpl, aaTermDepositFiDaoImpl, txnExternalSnsPub, cacheService, docsClient, evaluator, userClient, actorClient, nameCheckClient, wealthOnboardingClient, gormTxnExecutor, segmentationServiceClient, incomeEstClient, domainIdGenerator, aaTransactionProcessor, piClient, accountPIRelationClient, aaOrderClient, consentAccountMappingDaoImpl, uiClient, screenerClient, docsS3Client, connectionFlowDaoImpl, preapprovedloanClient, panProcessor)
	return caService
}

func InitializeCaConsumerService(ctx context.Context, pgdb types.ConnectedAccountPGDB, vgAaClient aa.AccountAggregatorClient, genconf2 *genconf.Config, awsConf aws.Config, conf *config.Config, bcClient bouncycastle.BouncyCastleClient, txnBatchPub typedef.TransactionBatchProcessDelayPublisher, txnExternalSnsPub typedef.TransactionEventExternalPublisher, accUpdateExtPub typedef.AccountUpdateEventExternalPublisher, createAttemptPub typedef.CreateAttemptSqsPublisher, dataFetchDelayPub typedef.FetchDataSqsDelayPublisher, broker events.Broker, actorClient actor.ActorClient, userClient user.UsersClient, userGrpClient group.GroupClient, commsClient types2.CaCommsClientWithInterceptors, sendNotiDelayPub typedef.SendNotificationSqsDelayPublisher, fetchDataPub typedef.FetchDataSqsPublisher, onboardingClient onboarding.OnboardingClient, savingsClient savings.SavingsClient, salaryProgramClient salaryprogram.SalaryProgramClient, processConsentDataRefreshPub typedef.ProcessConsentDataRefreshSqsPublisher, captureColumnUpdatePub typedef.CaptureColumnUpdateSqsPublisher, captureHeartbeatAndSendNotificationPub typedef.CaptureHeartbeatAndSendNotificationSqsPublisher, firstDataPullDelayPub typedef.FirstDataPullSqsDelayPublisher, redisClient types.ConnectedAccountRedisStore, firstDataPullPub typedef.FirstDataPullSqsPublisher, caAccountDataSyncPublisher typedef.AccountDataSyncExternalPublisher, CaUserTxnDataS3Client types2.CaUserTxnDataS3Client, nameCheckClient namecheck.UNNameCheckClient) *consumer.CaConsumer {
	consentRequestDaoImpl := dao.NewConsentRequestDaoImpl(pgdb)
	consentDaoImpl := dao.NewConsentDaoImpl(pgdb)
	db := types.ConnectedAccountPGDBGormDBProvider(pgdb)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	consentAccountMappingDaoImpl := dao.NewConsentAccountMappingDaoImpl(pgdb, gormTxnExecutor)
	aaAccountDaoImpl := dao.NewAaAccountDaoImpl(pgdb)
	client := types.ConnectedAccountRedisStoreProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	redisOptions := NewRedisOptions(genconf2)
	hystrixCommand := redisOptions.HystrixCommand
	cacheStorageWithHystrix := cache.NewRedisCacheStorageWithHystrix(redisCacheStorage, hystrixCommand)
	aaAccountDaoCache := dao.NewAaAccountDaoCache(aaAccountDaoImpl, cacheStorageWithHystrix, genconf2)
	accountExtProc := external.NewAccountExtProc(accUpdateExtPub)
	featureReleaseConfig := typedef.ReleaseConfigProvider(genconf2)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGrpClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	propertyProperty := property.NewProperty(userGrpClient, userClient, actorClient, genconf2, evaluator)
	managerService := consent.NewManagerService(genconf2, vgAaClient, consentRequestDaoImpl, consentDaoImpl, consentAccountMappingDaoImpl, aaAccountDaoCache, createAttemptPub, accountExtProc, propertyProperty, gormTxnExecutor, onboardingClient, savingsClient, salaryProgramClient)
	edeService := crypto.NewEdeService(bcClient)
	aaDataFetchAttemptDaoImpl := dao.NewDataFetchAttemptDaoImpl(pgdb)
	dataProcessAttemptDaoImpl := dao.NewDataProcessAttemptDaoImpl(pgdb)
	aaBatchProcessorDaoImpl := dao.NewAaBatchProcessorDaoImpl(pgdb)
	aaDepositFiDaoImpl := dao.NewAaDepositFiDaoImpl(pgdb)
	aaDepositFiDaoCache := dao.NewAaDepositFiDaoCache(aaDepositFiDaoImpl, cacheStorageWithHystrix, genconf2)
	aaTransactionDaoImpl := dao.NewAaTransactionDaoImpl(pgdb)
	aaTransactionDaoCache := dao.NewAaTransactionDaoCache(aaTransactionDaoImpl, cacheStorageWithHystrix, genconf2)
	txnBatchProcessor := fi_types.NewTxnBatchProcessor(aaBatchProcessorDaoImpl, aaDataFetchAttemptDaoImpl, consentDaoImpl, genconf2, gormTxnExecutor)
	transactionExternalProcessor := fi_types.NewTransactionExternalProcessor(txnExternalSnsPub)
	depositProcessor := fi_types.NewDepositProcessor(aaAccountDaoCache, aaDepositFiDaoCache, aaTransactionDaoCache, genconf2, aaBatchProcessorDaoImpl, txnBatchPub, txnBatchProcessor, transactionExternalProcessor, captureColumnUpdatePub, consentDaoImpl, gormTxnExecutor, caAccountDataSyncPublisher)
	aaRecurringDepositFiDaoImpl := dao.NewAaRecurringDepositFiDaoImpl(pgdb)
	recurringDepositProcessor := fi_types.NewRecurringDepositProcessor(aaAccountDaoCache, aaRecurringDepositFiDaoImpl, aaTransactionDaoCache, genconf2, aaBatchProcessorDaoImpl, txnBatchPub, txnBatchProcessor, transactionExternalProcessor, gormTxnExecutor, consentDaoImpl)
	aaTermDepositFiDaoImpl := dao.NewAaTermDepositFiDaoImpl(pgdb)
	termDepositProcessor := fi_types.NewTermDepositProcessor(aaAccountDaoCache, aaTermDepositFiDaoImpl, aaTransactionDaoCache, genconf2, aaBatchProcessorDaoImpl, txnBatchPub, txnBatchProcessor, transactionExternalProcessor, gormTxnExecutor, consentDaoImpl)
	aaEquityFiDaoImpl := dao.NewAaEquityFiDaoImpl(pgdb)
	nameCheckProcessor := pkg.NewInsightNameCheckProcessor(userClient, nameCheckClient)
	dataValidationProcessor := fi_types.NewDataValidationProcessor(userClient, nameCheckProcessor)
	equitiesProcessor := fi_types.NewEquitiesProcessor(genconf2, consentDaoImpl, aaAccountDaoCache, aaEquityFiDaoImpl, aaTransactionDaoCache, captureColumnUpdatePub, gormTxnExecutor, txnBatchProcessor, dataValidationProcessor, caAccountDataSyncPublisher)
	aaEtfFiDaoImpl := dao.NewAaEtfFiDaoImpl(pgdb)
	etfProcessor := fi_types.NewEtfProcessor(genconf2, consentDaoImpl, aaAccountDaoCache, aaEtfFiDaoImpl, aaTransactionDaoCache, captureColumnUpdatePub, gormTxnExecutor, txnBatchProcessor, dataValidationProcessor)
	aaReitFiDaoImpl := dao.NewAaReitFiDaoImpl(pgdb)
	reitProcessor := fi_types.NewReitProcessor(genconf2, consentDaoImpl, aaAccountDaoCache, aaReitFiDaoImpl, aaTransactionDaoCache, captureColumnUpdatePub, gormTxnExecutor, txnBatchProcessor, dataValidationProcessor)
	aaInvitFiDaoImpl := dao.NewAaInvitFiDaoImpl(pgdb)
	invitProcessor := fi_types.NewInvitProcessor(genconf2, consentDaoImpl, aaAccountDaoCache, aaInvitFiDaoImpl, aaTransactionDaoCache, captureColumnUpdatePub, gormTxnExecutor, txnBatchProcessor, dataValidationProcessor)
	aaNpsFiDaoImpl := dao.NewAaNpsFiDaoImpl(pgdb)
	npsProcessor := fi_types.NewNpsProcessor(genconf2, consentDaoImpl, aaAccountDaoCache, aaNpsFiDaoImpl, aaTransactionDaoCache, captureColumnUpdatePub, gormTxnExecutor, txnBatchProcessor, dataValidationProcessor, caAccountDataSyncPublisher)
	fiFactory := data.NewFIFactory(depositProcessor, recurringDepositProcessor, termDepositProcessor, equitiesProcessor, etfProcessor, reitProcessor, invitProcessor, npsProcessor)
	aaNotificationDaoImpl := dao.NewAaNotificationDaoImpl(pgdb)
	firstDataPullFail := comms.NewFirstDataPullFail(genconf2, aaAccountDaoCache)
	firstDataPullSuccess := comms.NewFirstDataPullSuccess(genconf2, aaAccountDaoCache)
	accountDeLinkedAA := comms.NewAccountDeLinkedAA(genconf2)
	consentPausedAA := comms.NewConsentPausedAA(genconf2, aaAccountDaoCache)
	aaHeartbeatUp := comms.NewAaHeartbeatUp(genconf2)
	accountConsentOrchestratorSvc := consentorchestrator.NewAccountConsentOrchestratorSvc(consentAccountMappingDaoImpl, aaAccountDaoCache, managerService, consentDaoImpl, aaTransactionDaoCache, accUpdateExtPub, consentRequestDaoImpl, vgAaClient, genconf2, propertyProperty, gormTxnExecutor)
	aaConsentExpired := comms.NewAaConsentExpired(genconf2, accountConsentOrchestratorSvc)
	commsFactorySvc := notification.NewCommsFactorySvc(firstDataPullFail, firstDataPullSuccess, accountDeLinkedAA, consentPausedAA, aaHeartbeatUp, aaConsentExpired)
	commsCommsClient := types2.CommsClientProvider(commsClient)
	senderSvc := notification.NewSenderSvc(aaNotificationDaoImpl, commsFactorySvc, actorClient, userClient, commsCommsClient, genconf2, sendNotiDelayPub)
	defaultTime := datetime.NewDefaultTime()
	processorService := data.NewProcessorService(edeService, vgAaClient, aaDataFetchAttemptDaoImpl, consentDaoImpl, genconf2, dataProcessAttemptDaoImpl, aaBatchProcessorDaoImpl, fiFactory, aaAccountDaoCache, aaTransactionDaoCache, consentRequestDaoImpl, senderSvc, gormTxnExecutor, evaluator, defaultTime)
	processDataPublisher := ProcessDataPublisherProvider(ctx, awsConf, conf)
	decryptDataPublisher := FetchDecryptDataPublisherProvider(ctx, awsConf, conf)
	aaAccountColumnHistoryDaoImpl := dao.NewAaAccountColumnHistoryDaoImpl(pgdb)
	aaUserHeartbeatDaoImpl := dao.NewAaUserHeartbeatDaoImpl(pgdb)
	clock := lock.NewRealClockProvider()
	uuidGenerator := idgen.NewUuidGenerator()
	redisLockManager := lock.NewRedisLockManager(client, clock, uuidGenerator)
	caConsumer := consumer.NewCaConsumer(consentRequestDaoImpl, consentDaoImpl, managerService, processorService, processDataPublisher, aaDataFetchAttemptDaoImpl, aaBatchProcessorDaoImpl, accountConsentOrchestratorSvc, aaAccountDaoCache, decryptDataPublisher, dataFetchDelayPub, genconf2, broker, senderSvc, fetchDataPub, aaAccountColumnHistoryDaoImpl, aaUserHeartbeatDaoImpl, createAttemptPub, processConsentDataRefreshPub, firstDataPullDelayPub, firstDataPullPub, gormTxnExecutor, userClient, evaluator, CaUserTxnDataS3Client, redisLockManager)
	return caConsumer
}

func InitializeCaDevService(pgdb types.ConnectedAccountPGDB, redisClient types.ConnectedAccountRedisStore, conf *genconf.Config, featureEngineeringDb types.FeatureEngineeringPGDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB]) *developer.CaDevService {
	consentDaoImpl := dao.NewConsentDaoImpl(pgdb)
	consentRequestDaoImpl := dao.NewConsentRequestDaoImpl(pgdb)
	db := types.ConnectedAccountPGDBGormDBProvider(pgdb)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(db)
	consentAccountMappingDaoImpl := dao.NewConsentAccountMappingDaoImpl(pgdb, gormTxnExecutor)
	devCaConsentEntity := processor.NewDevCaEntity(consentDaoImpl, consentRequestDaoImpl, consentAccountMappingDaoImpl)
	aaDataFetchAttemptDaoImpl := dao.NewDataFetchAttemptDaoImpl(pgdb)
	dataProcessAttemptDaoImpl := dao.NewDataProcessAttemptDaoImpl(pgdb)
	aaBatchProcessorDaoImpl := dao.NewAaBatchProcessorDaoImpl(pgdb)
	devCaDataAttemptEntity := processor.NewDevDataAttemptEntity(aaDataFetchAttemptDaoImpl, dataProcessAttemptDaoImpl, aaBatchProcessorDaoImpl)
	aaAccountDaoImpl := dao.NewAaAccountDaoImpl(pgdb)
	client := types.ConnectedAccountRedisStoreProvider(redisClient)
	redisCacheStorage := cache.NewRedisCacheStorage(client)
	redisOptions := NewRedisOptions(conf)
	hystrixCommand := redisOptions.HystrixCommand
	cacheStorageWithHystrix := cache.NewRedisCacheStorageWithHystrix(redisCacheStorage, hystrixCommand)
	aaAccountDaoCache := dao.NewAaAccountDaoCache(aaAccountDaoImpl, cacheStorageWithHystrix, conf)
	aaDepositFiDaoImpl := dao.NewAaDepositFiDaoImpl(pgdb)
	aaDepositFiDaoCache := dao.NewAaDepositFiDaoCache(aaDepositFiDaoImpl, cacheStorageWithHystrix, conf)
	aaRecurringDepositFiDaoImpl := dao.NewAaRecurringDepositFiDaoImpl(pgdb)
	aaTermDepositFiDaoImpl := dao.NewAaTermDepositFiDaoImpl(pgdb)
	aaEquityFiDaoImpl := dao.NewAaEquityFiDaoImpl(pgdb)
	aaEtfFiDaoImpl := dao.NewAaEtfFiDaoImpl(pgdb)
	aaReitFiDaoImpl := dao.NewAaReitFiDaoImpl(pgdb)
	aaInvitFiDaoImpl := dao.NewAaInvitFiDaoImpl(pgdb)
	aaNpsFiDaoImpl := dao.NewAaNpsFiDaoImpl(pgdb)
	devCaAccountEntity := processor.NewDevCaAccountEntity(aaAccountDaoCache, aaDepositFiDaoCache, aaRecurringDepositFiDaoImpl, aaTermDepositFiDaoImpl, aaEquityFiDaoImpl, aaEtfFiDaoImpl, aaReitFiDaoImpl, aaInvitFiDaoImpl, aaNpsFiDaoImpl)
	aaUserBankPreferenceDaImpl := dao.NewAaUserBankPreferenceDaoImpl(pgdb)
	devCaBankPreferenceEntity := processor.NewDevCaBankPreferenceEntity(aaUserBankPreferenceDaImpl)
	aaNotificationDaoImpl := dao.NewAaNotificationDaoImpl(pgdb)
	devCaNotificationEntity := processor.NewDevCaNotificationEntity(aaNotificationDaoImpl)
	aaTransactionDaoImpl := dao.NewAaTransactionDaoImpl(pgdb)
	aaTransactionDaoCache := dao.NewAaTransactionDaoCache(aaTransactionDaoImpl, cacheStorageWithHystrix, conf)
	devCaTransactionEntity := processor.NewDevCaTransactionEntity(aaTransactionDaoCache, aaDepositFiDaoCache, aaRecurringDepositFiDaoImpl, aaTermDepositFiDaoImpl, aaEquityFiDaoImpl, aaEtfFiDaoImpl)
	analysedUserDaoImpl := dao.NewAnalysedUserDaoImpl(dbConnProvider)
	devCaAnalysedUserEntity := processor.NewDevCaAnalysedUserEntity(analysedUserDaoImpl)
	analysisRequestDaoImpl := dao.NewAnalysisRequestDaoImpl(pgdb)
	devCaAnalysisRequestEntity := processor.NewDevCaAnalysisRequestEntity(analysisRequestDaoImpl)
	analysisAttemptDaoImpl := dao.NewAnalysisAttemptDaoImpl(featureEngineeringDb)
	devCaAnalysisAttemptEntity := processor.NewDevCaAnalysisAttemptEntity(analysisAttemptDaoImpl)
	devFactory := developer.NewDevFactory(devCaConsentEntity, devCaDataAttemptEntity, devCaAccountEntity, devCaBankPreferenceEntity, devCaNotificationEntity, devCaTransactionEntity, devCaAnalysedUserEntity, devCaAnalysisRequestEntity, devCaAnalysisAttemptEntity)
	caDevService := developer.NewCaDevService(devFactory)
	return caDevService
}

func InitialiseDataAnalyticsService(featureEngineeringDb types.FeatureEngineeringPGDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], temporalClient typedef.ConnectedAccountClient, caAnalyticsClient analytics.AnalyticsClient) *dataanalytics.Service {
	analysedUserDaoImpl := dao.NewAnalysedUserDaoImpl(dbConnProvider)
	analysisAttemptDaoImpl := dao.NewAnalysisAttemptDaoImpl(featureEngineeringDb)
	client := typedef.ConnectedAccountClientProvider(temporalClient)
	service := dataanalytics.NewService(analysedUserDaoImpl, analysisAttemptDaoImpl, client, caAnalyticsClient)
	return service
}

func InitiateAaAnalyserService(caPgdb types.ConnectedAccountPGDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], temporalClient typedef.ConnectedAccountClient, vmClient vendormapping.VendorMappingServiceClient, caAnalyticsS3Client types2.CaAnalyticsS3Client, caClient connected_account.ConnectedAccountClient) *analytics2.Service {
	analysisRequestDaoImpl := dao.NewAnalysisRequestDaoImpl(caPgdb)
	analysedUserDaoImpl := dao.NewAnalysedUserDaoImpl(dbConnProvider)
	client := typedef.ConnectedAccountClientProvider(temporalClient)
	service := analytics2.NewService(analysisRequestDaoImpl, analysedUserDaoImpl, client, vmClient, caAnalyticsS3Client, caClient)
	return service
}

func InitiateAnalysisIgnosisActivityProcessor(conf *worker.Config, caPgdb types.ConnectedAccountPGDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], ignosisVgClient ignosis.IgnosisAaAnalyticsServiceClient, vmClient vendormapping.VendorMappingServiceClient, caClient connected_account.ConnectedAccountClient, caAnalyticsS3Client types2.CaAnalyticsS3Client) *analytics3.Processor {
	analysisRequestDaoImpl := dao.NewAnalysisRequestDaoImpl(caPgdb)
	analysedUserDaoImpl := dao.NewAnalysedUserDaoImpl(dbConnProvider)
	analyticsProcessor := analytics3.NewProcessor(conf, analysisRequestDaoImpl, analysedUserDaoImpl, ignosisVgClient, vmClient, caClient, caAnalyticsS3Client)
	return analyticsProcessor
}

func InitialiseActivities(caPgDb types.ConnectedAccountPGDB, fePgDb types.FeatureEngineeringPGDB, analyticsDbConnProvider *storagev2.DBResourceProvider[*gorm.DB], caAnalyticsClient analytics.AnalyticsClient, broker events.Broker) *activity.Activities {
	analysisAttemptDaoImpl := dao.NewAnalysisAttemptDaoImpl(fePgDb)
	analysedUserDaoImpl := dao.NewAnalysedUserDaoImpl(analyticsDbConnProvider)
	activities := activity.NewActivities(analysisAttemptDaoImpl, analysedUserDaoImpl, caAnalyticsClient, broker)
	return activities
}

// wire.go:

func NewRedisOptions(conf *genconf.Config) *cfg.RedisOptions {
	return conf.RedisOptions()
}

func ProcessDataPublisherProvider(ctx context.Context, awsConf aws.Config, conf *config.Config) typedef.ProcessDataPublisher {
	return newExtendedPublisher(ctx, awsConf, conf.ProcessDataSqsPublisher.GetQueueName(), conf.ProcessDataSqsPublisher.GetBucketName())
}

func newExtendedPublisher(ctx context.Context, awsConf aws.Config, queueName, bucketName string) queue.ExtendedPublisher {
	return wire.InitializeExtendedPublisher(ctx, awsConf, queue.NewDefaultMessage(), sqs.QueueName(queueName), bucketName, sqs.ServiceName(cfg.CONNECTED_ACC_ORDER_SERVICE))
}

func FetchDecryptDataPublisherProvider(ctx context.Context, awsConf aws.Config, conf *config.Config) typedef.DecryptDataPublisher {
	return newExtendedPublisher(ctx, awsConf, conf.DecryptDataSqsPublisher.GetQueueName(), conf.DecryptDataSqsPublisher.GetBucketName())
}
