package fi_types

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	caPb "github.com/epifi/gamma/api/connected_account"
	caEnumPb "github.com/epifi/gamma/api/connected_account/enums"
	"github.com/epifi/gamma/connectedaccount/config/genconf"
	"github.com/epifi/gamma/connectedaccount/dao"

	"github.com/epifi/gamma/connectedaccount/test/mocks/mock_dao"
	caPkg "github.com/epifi/gamma/pkg/connectedaccount"
)

var (
	batch1 = &caPb.BatchProcessTransaction{
		Id:                "test-id",
		FetchAttemptId:    "test-fetch-attempt-id",
		ProcessAttemptId:  "test-process-attempt-id",
		BatchNumber:       1,
		TxnStartTimestamp: timestampPb.Now(),
		TxnEndTimestamp:   timestampPb.Now(),
		Status:            caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS,
	}
	attempt1 = &caPb.DataFetchAttempt{
		Id:            "test-id",
		ActorId:       "test-actor-id",
		ConsentId:     "test-consent-id",
		TransactionId: "test-txn-id",
		SessionId:     "test-session-id",
		FetchStatus:   caEnumPb.DataFetchStatus_DATA_FETCH_STATUS_COMPLETED,
		DataRangeFrom: timestampPb.Now(),
		DataRangeTo:   timestampPb.Now(),
	}
	consent1 = &caPb.Consent{
		Id:            "test-consent-1",
		ConsentStatus: caEnumPb.ConsentStatus_CONSENT_STATUS_ACTIVE,
		Accounts: &caPb.Accounts{
			AccountList: []*caPb.Account{
				{
					FiType:        caEnumPb.FIType_FI_TYPE_DEPOSIT,
					FipId:         "HDFC-FIP",
					AccType:       caPkg.Savings,
					LinkRefNumber: "link-ref-number-hdfc",
				},
			},
		},
		ConsentId:                "aa-consent-id-1",
		ConsentRequestId:         "test-consent-request-id-1",
		ActorId:                  "test-actor-id-1",
		CustomerId:               "**********@onemoney",
		DataRangeFrom:            timestampPb.New(time.Now().AddDate(-1, 0, 0)),
		DataRangeTo:              timestampPb.Now(),
		Signature:                "eyJhbGciOiJSUzI1NiIsImI2NCI6ZmFsc2UsImtpZCI6InRlc3QifQ..VhHkiw-Vz8Ob1WT5MP4ZHEIOV1gdRIc46v_2fZSx1jPD_ipnkGdm8g1hrbkXViy2yKTkDMgX4yLqSgYUGhsBYeV7Yw4JsBCupqFG1f-aOVQ1nk7ePjJuDVzMP0yus1O8Gh2K8c_dd9yf7sHDYbnSTIzqJBpEUSua3qrMg_egAAQiYCcSAplYWYSLomy8510M0fz0z2Xa197awBqeRObN2CfcBqnZKq17TfksZ4qnvIb58euf7KvJgnnhUM8JYjC7L4JoFQCCo6mL74IeLZI7qkwn_vRqR3dd6GZs_EMoYzpjpo5oCylR3xxzQbFqZrC9WnQov1KIRGDMDPPsSYQ2eA",
		ConsentDataRefreshStatus: caEnumPb.ConsentDataRefreshStatus_CONSENT_DATA_REFRESH_STATUS_IN_PROGRESS,
	}
)

func TestTxnBatchProcessor_PersistBatches(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCDao := mock_dao.NewMockConsentDao(ctr)
	mockDfaDao := mock_dao.NewMockDataFetchAttemptDao(ctr)
	mockBatchProcessDao := mock_dao.NewMockAaBatchProcessDao(ctr)
	type fields struct {
		batchProcessDao dao.AaBatchProcessDao
		attemptDao      dao.DataFetchAttemptDao
		consentDao      dao.ConsentDao
		conf            *genconf.Config
	}
	type args struct {
		ctx     context.Context
		batches []*caPb.BatchProcessTransaction
		mocks   []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "failed to create batches due to db error",
			fields: fields{
				batchProcessDao: mockBatchProcessDao,
				attemptDao:      mockDfaDao,
				consentDao:      mockCDao,
				conf:            dynconf,
			},
			args: args{
				ctx: context.Background(),
				batches: []*caPb.BatchProcessTransaction{
					batch1,
				},
				mocks: []interface{}{
					mockBatchProcessDao.EXPECT().CreateOrGetBatch(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")),
				},
			},
			wantErr: true,
		},
		{
			name: "success in creating batches",
			fields: fields{
				batchProcessDao: mockBatchProcessDao,
				attemptDao:      mockDfaDao,
				consentDao:      mockCDao,
				conf:            dynconf,
			},
			args: args{
				ctx: context.Background(),
				batches: []*caPb.BatchProcessTransaction{
					{
						Id:                "test-id",
						FetchAttemptId:    "test-fetch-attempt-id",
						ProcessAttemptId:  "test-process-attempt-id",
						BatchNumber:       1,
						TxnStartTimestamp: timestampPb.Now(),
						TxnEndTimestamp:   timestampPb.Now(),
						Status:            caEnumPb.BatchProcessStatus_BATCH_PROCESS_STATUS_SUCCESS,
					},
				},
				mocks: []interface{}{
					mockBatchProcessDao.EXPECT().CreateOrGetBatch(gomock.Any(), gomock.Any()).Return(batch1, nil),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			tx := &TxnBatchProcessor{
				BatchProcessDao: tt.fields.batchProcessDao,
				AttemptDao:      tt.fields.attemptDao,
				ConsentDao:      tt.fields.consentDao,
				Conf:            tt.fields.conf,
				txnExecutor:     mockTxnExecutor,
			}
			got, err := tx.PersistBatches(tt.args.ctx, tt.args.batches)
			if (err != nil) != tt.wantErr {
				t1.Errorf("PersistBatches() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err == nil {
				assert.NotNil(t, got)
			}
		})
	}
}

func TestTxnBatchProcessor_UpdateBatch(t *testing.T) {
	t.Parallel()
	ctr := gomock.NewController(t)
	defer ctr.Finish()
	mockCDao := mock_dao.NewMockConsentDao(ctr)
	mockDfaDao := mock_dao.NewMockDataFetchAttemptDao(ctr)
	mockBatchProcessDao := mock_dao.NewMockAaBatchProcessDao(ctr)
	type fields struct {
		BatchProcessDao dao.AaBatchProcessDao
		AttemptDao      dao.DataFetchAttemptDao
		ConsentDao      dao.ConsentDao
		Conf            *genconf.Config
	}
	type args struct {
		ctx         context.Context
		batch       *caPb.BatchProcessTransaction
		attempt     *caPb.DataFetchAttempt
		isLastBatch bool
		mocks       []interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "error in updating batch by id",
			fields: fields{
				BatchProcessDao: mockBatchProcessDao,
				AttemptDao:      mockDfaDao,
				ConsentDao:      mockCDao,
				Conf:            dynconf,
			},
			args: args{
				ctx:         context.Background(),
				batch:       batch1,
				attempt:     attempt1,
				isLastBatch: false,
				mocks: []interface{}{
					mockBatchProcessDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(errors.New("err")),
				},
			},
			wantErr: true,
		},
		{
			name: "success, not last batch",
			fields: fields{
				BatchProcessDao: mockBatchProcessDao,
				AttemptDao:      mockDfaDao,
				ConsentDao:      mockCDao,
				Conf:            dynconf,
			},
			args: args{
				ctx:         context.Background(),
				batch:       batch1,
				attempt:     attempt1,
				isLastBatch: false,
				mocks: []interface{}{
					mockBatchProcessDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil),
				},
			},
			wantErr: false,
		},
		{
			name: "error updating attempt for last batch",
			fields: fields{
				BatchProcessDao: mockBatchProcessDao,
				AttemptDao:      mockDfaDao,
				ConsentDao:      mockCDao,
				Conf:            dynconf,
			},
			args: args{
				ctx:         context.Background(),
				batch:       batch1,
				attempt:     attempt1,
				isLastBatch: true,
				mocks: []interface{}{
					mockBatchProcessDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("err")),
				},
			},
			wantErr: true,
		},
		{
			name: "error in getting consent from db error in updating consent in db for last batch",
			fields: fields{
				BatchProcessDao: mockBatchProcessDao,
				AttemptDao:      mockDfaDao,
				ConsentDao:      mockCDao,
				Conf:            dynconf,
			},
			args: args{
				ctx:         context.Background(),
				batch:       batch1,
				attempt:     attempt1,
				isLastBatch: true,
				mocks: []interface{}{
					mockBatchProcessDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(nil, errors.New("err")),
				},
			},
			wantErr: true,
		},
		{
			name: "error in updating consent in db for last batch",
			fields: fields{
				BatchProcessDao: mockBatchProcessDao,
				AttemptDao:      mockDfaDao,
				ConsentDao:      mockCDao,
				Conf:            dynconf,
			},
			args: args{
				ctx:         context.Background(),
				batch:       batch1,
				attempt:     attempt1,
				isLastBatch: true,
				mocks: []interface{}{
					mockBatchProcessDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockCDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("err")),
				},
			},
			wantErr: true,
		},
		{
			name: "success in updating consent in db for last batch",
			fields: fields{
				BatchProcessDao: mockBatchProcessDao,
				AttemptDao:      mockDfaDao,
				ConsentDao:      mockCDao,
				Conf:            dynconf,
			},
			args: args{
				ctx:         context.Background(),
				batch:       batch1,
				attempt:     attempt1,
				isLastBatch: true,
				mocks: []interface{}{
					mockBatchProcessDao.EXPECT().UpdateBatchById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil),
					mockDfaDao.EXPECT().UpdateById(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil),
					mockCDao.EXPECT().Get(gomock.Any(), gomock.Any()).Return(consent1, nil),
					mockCDao.EXPECT().UpdateByConsentId(gomock.Any(), gomock.Any(), gomock.Any()).Return(consent1, nil),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t1 *testing.T) {
			tx := &TxnBatchProcessor{
				BatchProcessDao: tt.fields.BatchProcessDao,
				AttemptDao:      tt.fields.AttemptDao,
				ConsentDao:      tt.fields.ConsentDao,
				Conf:            tt.fields.Conf,
				txnExecutor:     mockTxnExecutor,
			}
			if err := tx.UpdateBatch(tt.args.ctx, tt.args.batch, tt.args.attempt, tt.args.isLastBatch); (err != nil) != tt.wantErr {
				t1.Errorf("UpdateBatch() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
