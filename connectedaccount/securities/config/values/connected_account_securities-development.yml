Application:
  Environment: "development"
  Name: "securities"

ConnectedAccountDb:
  DbType: "PGDB"
  AppName: "connectedaccount"
  StatementTimeout: 1m
  Name: "connected_account"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

EpifiWealthDb:
  DbType: "CRDB"
  AppName: "connectedaccount"
  StatementTimeout: 5s
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi_wealth"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true
    EnableMultiDBSupport: true
    DBResolverList:
      - TableName: [ ]
        Alias: "connected_account_pgdb"
        DbDsn:
          DbType: "PGDB"
          AppName: "connected_account_securities"
          Host: "localhost"
          Port: 5432
          StatementTimeout: 1m
          Name: "connected_account_pgdb"
          EnableDebug: true
          SSLMode: "disable"
          SecretName: "{\"username\": \"root\", \"password\": \"\"}"

Secrets:
  Ids:
    ConnectedAccountDbUserNamePassword: "{\"username\": \"root\", \"password\": \"\"}"

AWS:
  Region: "ap-south-1"
