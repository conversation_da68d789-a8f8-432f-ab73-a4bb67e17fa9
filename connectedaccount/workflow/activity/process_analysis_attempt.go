package activity

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	caAnalytics "github.com/epifi/gamma/api/connected_account/analytics"
	"github.com/epifi/gamma/api/connected_account/data_analytics"
	"github.com/epifi/gamma/api/connected_account/workflow"
	"github.com/epifi/gamma/connectedaccount/events"
)

func (s *Activities) UpdateAnalysisAttempt(ctx context.Context, req *workflow.UpdateAnalysisAttemptRequest) (*workflow.UpdateAnalysisAttemptResponse, error) {
	lg := activity.GetLogger(ctx)
	ctx = epificontext.CtxWithActorId(ctx, req.GetAttempt().GetActorId())
	err := s.analysisAttemptDao.Update(ctx, req.GetAttempt(), req.GetUpdateFieldMasks())
	if err != nil {
		lg.Error("error updating analysis attempt", zap.Error(err), zap.String(logger.ATTEMPT_ID, req.GetAttempt().GetId()))
		return nil, errors.Wrapf(err, "error updating analysis attempt with id: %s", req.GetAttempt().GetId())
	}

	// Publish events based on analysis status
	attempt := req.GetAttempt()
	if attempt.GetStatus() == data_analytics.AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL {
		s.eventBroker.AddToBatch(ctx, events.NewAnalysisSuccessEvent(
			attempt.GetActorId(),
			attempt.GetId(),
			attempt.GetClientReqId(),
			time.Now(),
		))
		lg.Info("published analysis success event", zap.String(logger.ATTEMPT_ID, attempt.GetId()))
	} else if attempt.GetStatus() == data_analytics.AnalysisStatus_ANALYSIS_STATUS_FAILED {
		s.eventBroker.AddToBatch(ctx, events.NewAnalysisFailedEvent(
			attempt.GetActorId(),
			attempt.GetId(),
			attempt.GetClientReqId(),
			time.Now(),
		))
		lg.Info("published analysis failed event", zap.String(logger.ATTEMPT_ID, attempt.GetId()))
	}

	return &workflow.UpdateAnalysisAttemptResponse{}, nil
}

func (s *Activities) InitiateWealthAnalysis(ctx context.Context, req *workflow.InitiateWealthAnalysisRequest) (*workflow.InitiateWealthAnalysisResponse, error) {
	lg := activity.GetLogger(ctx)
	attempt, err := s.analysisAttemptDao.GetById(ctx, req.GetAttemptId())
	if err != nil {
		lg.Error("error getting analysis attempt", zap.Error(err), zap.String(logger.ATTEMPT_ID, req.GetAttemptId()))
		return nil, errors.Wrapf(err, "error updating analysis attempt with id: %s", req.GetAttemptId())
	}
	ctx = epificontext.CtxWithActorId(ctx, attempt.GetActorId())

	res, err := s.caAnalyticsClient.InitiateAnalysis(ctx, &caAnalytics.InitiateAnalysisRequest{
		ActorId:            attempt.GetActorId(),
		ClientReqId:        attempt.GetClientReqId(),
		Client:             common.Owner_OWNER_EPIFI_TECH,
		DataExchangeRecord: attempt.GetRequestParams().GetDataExchangeRecord(),
		EmploymentType:     attempt.GetRequestParams().GetEmploymentType(),
		OrganisationName:   attempt.GetRequestParams().GetOrganisationName(),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		if res.GetStatus().IsAlreadyExists() ||
			res.GetStatus().GetCode() == uint32(caAnalytics.InitiateAnalysisResponse_NO_NEW_DATA_FOR_ANALYSIS_FOR_ANY_ACCOUNT_FOR_ACTOR) {
			return &workflow.InitiateWealthAnalysisResponse{}, nil
		}
		lg.Error("error initiating analysis", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, attempt.GetClientReqId()))
		return nil, errors.Wrapf(err, "error initiating analysis with client req id: %s", attempt.GetClientReqId())
	}
	return &workflow.InitiateWealthAnalysisResponse{}, nil
}

func (s *Activities) UpdateAnalysedUser(ctx context.Context, req *workflow.UpdateAnalysedUserRequest) (*workflow.UpdateAnalysedUserResponse, error) {
	lg := activity.GetLogger(ctx)
	attempt, err := s.analysisAttemptDao.GetById(ctx, req.GetAttemptId())
	if err != nil {
		lg.Error("error getting analysis attempt", zap.Error(err), zap.String(logger.ATTEMPT_ID, req.GetAttemptId()))
		return nil, epifitemporal.NewTransientError(errors.Wrapf(err, "error getting analysis attempt with id: %s", req.GetAttemptId()))
	}
	ctx = epificontext.CtxWithActorId(ctx, attempt.GetActorId())
	analysisStatusRes, err := s.caAnalyticsClient.GetAnalysisStatus(ctx, &caAnalytics.GetAnalysisStatusRequest{
		ActorId:     attempt.GetActorId(),
		ClientReqId: attempt.GetClientReqId(),
	})
	if err = epifigrpc.RPCError(analysisStatusRes, err); err != nil {
		lg.Error("error getting analysis status", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, attempt.GetClientReqId()))
		return nil, epifitemporal.NewTransientError(errors.Wrapf(err, "error getting analysis status with client req id: %s", attempt.GetClientReqId()))
	}
	switch analysisStatusRes.GetAnalysisStatus() {
	case caAnalytics.GetAnalysisStatusResponse_ANALYSIS_STATUS_SUCCESS:
		if err = s.getAndUpdateAnalysis(ctx, attempt); err != nil {
			lg.Error("error getting and updating analysis", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, attempt.GetClientReqId()))
			return nil, epifitemporal.NewTransientError(errors.Wrapf(err, "error getting and updating analysis for client req id: %s", attempt.GetClientReqId()))
		}
		return &workflow.UpdateAnalysedUserResponse{}, nil
	case caAnalytics.GetAnalysisStatusResponse_ANALYSIS_STATUS_FAILED:
		lg.Error("wealth analysis failed", zap.String(logger.CLIENT_REQUEST_ID, attempt.GetClientReqId()))
		return nil, epifitemporal.NewPermanentError(errors.Errorf("wealth analysis failed for client req id: %s", attempt.GetClientReqId()))
	default:
		lg.Error(fmt.Sprintf("no specific handling for analysis status: %s", analysisStatusRes.GetAnalysisStatus()), zap.String(logger.CLIENT_REQUEST_ID, attempt.GetClientReqId()))
		return nil, epifitemporal.NewTransientError(errors.Errorf("no specific handling for analysis status: %s", analysisStatusRes.GetAnalysisStatus()))
	}
}

func (s *Activities) getAndUpdateAnalysis(ctx context.Context, attempt *data_analytics.AnalysisAttempt) error {
	analysisRes, err := s.caAnalyticsClient.GetAnalysis(ctx, &caAnalytics.GetAnalysisRequest{
		ActorId:     attempt.GetActorId(),
		ClientReqId: attempt.GetClientReqId(),
	})
	if err = epifigrpc.RPCError(analysisRes, err); err != nil {
		return errors.Wrapf(err, "error getting analysis with client req id: %s", attempt.GetClientReqId())
	}
	if err = s.createOrUpdateAnalysedUser(ctx, attempt.GetActorId(), analysisRes.GetAnalysis()); err != nil {
		return errors.Wrapf(err, "error creating or updating analysed user for actor id: %s", attempt.GetActorId())
	}
	return nil
}

func (s *Activities) createOrUpdateAnalysedUser(ctx context.Context, actorId string, analysis *caAnalytics.Analysis) error {
	analysedUser, err := s.analysedUserDao.GetByActorId(ctx, actorId, common.Owner_OWNER_FEATURE_ENGINEERING_TECH)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			_, err = s.analysedUserDao.Create(
				ctx,
				&caAnalytics.AnalysedUser{
					ActorId:  actorId,
					Analysis: analysis,
				},
				common.Owner_OWNER_FEATURE_ENGINEERING_TECH,
			)
			if err != nil {
				return errors.Wrapf(err, "error creating analysed user for actor id: %s", actorId)
			}
			return nil
		}
		return errors.Wrapf(err, "error getting analysed user for actor id: %s", actorId)
	}
	updAnalysedUser := &caAnalytics.AnalysedUser{
		Id:       analysedUser.GetId(),
		Analysis: analysis,
	}
	err = s.analysedUserDao.Update(
		ctx,
		updAnalysedUser,
		[]caAnalytics.AnalysedUserFieldMask{caAnalytics.AnalysedUserFieldMask_ANALYSED_USER_FIELD_MASK_ANALYSIS},
		common.Owner_OWNER_FEATURE_ENGINEERING_TECH,
	)
	if err != nil {
		return errors.Wrapf(err, "error updating analysed user for actor id: %s", actorId)
	}
	return nil
}
