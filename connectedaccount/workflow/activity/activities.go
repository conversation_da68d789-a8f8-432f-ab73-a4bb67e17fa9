package activity

import (
	"github.com/epifi/be-common/pkg/events"
	caAnalytics "github.com/epifi/gamma/api/connected_account/analytics"
	"github.com/epifi/gamma/connectedaccount/dao"
)

type Activities struct {
	analysisAttemptDao dao.AnalysisAttemptDao
	analysedUserDao    dao.AnalysedUserDao
	caAnalyticsClient  caAnalytics.AnalyticsClient
	eventBroker        events.Broker
}

func NewActivities(
	analysisAttemptDao dao.AnalysisAttemptDao,
	analysedUserDao dao.AnalysedUserDao,
	caAnalyticsClient caAnalytics.AnalyticsClient,
	eventBroker events.Broker,
) *Activities {
	return &Activities{
		analysisAttemptDao: analysisAttemptDao,
		analysedUserDao:    analysedUserDao,
		caAnalyticsClient:  caAnalyticsClient,
		eventBroker:        eventBroker,
	}
}
