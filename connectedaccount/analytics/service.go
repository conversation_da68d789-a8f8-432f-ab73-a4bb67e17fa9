// nolint: gocritic,goimports
package analytics

import (
	"context"
	"fmt"
	"time"

	"github.com/gogo/status"
	"github.com/pkg/errors"
	temporalClient "go.temporal.io/sdk/client"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common"
	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	caNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/connectedaccount"
	"github.com/epifi/be-common/pkg/epifitemporal/workflow"
	"github.com/epifi/be-common/pkg/logger"
	caPb "github.com/epifi/gamma/api/connected_account"
	caxPb "github.com/epifi/gamma/api/connected_account/external"
	"github.com/epifi/gamma/pkg/salaryestimation"

	aaAnalyticsPb "github.com/epifi/gamma/api/connected_account/analytics"
	caWorkflowPb "github.com/epifi/gamma/api/connected_account/workflow"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	caDao "github.com/epifi/gamma/connectedaccount/dao"
	caWfNamespace "github.com/epifi/gamma/connectedaccount/workflow/namespace"
)

type Service struct {
	aaAnalyticsPb.UnimplementedAnalyticsServer
	arDao          caDao.AnalysisRequestsDao
	auDao          caDao.AnalysedUserDao
	temporalClient temporalClient.Client
	vmClient       vmPb.VendorMappingServiceClient
	s3Client       s3.S3Client
	caClient       caPb.ConnectedAccountClient
}

func NewService(
	arDao caDao.AnalysisRequestsDao,
	auDao caDao.AnalysedUserDao,
	temporalClient temporalClient.Client,
	vmClient vmPb.VendorMappingServiceClient,
	s3Client s3.S3Client,
	caClient caPb.ConnectedAccountClient,
) *Service {
	return &Service{
		arDao:          arDao,
		auDao:          auDao,
		temporalClient: temporalClient,
		vmClient:       vmClient,
		s3Client:       s3Client,
		caClient:       caClient,
	}
}

const (
	ACTION_UNSPECIFIED = iota
	ACTION_INITIATE_ANALYSIS
	ACTION_ANALYSIS_ALREADY_PRESENT
	ACTION_REJECT_ANALYSIS_REQUEST
	analysisDataPresignedUrlExpiryTime = 2 * time.Hour
)

const wfIdPrefixForWealthWorkflow = "WEALTH"

func (s *Service) InitiateAnalysis(ctx context.Context, req *aaAnalyticsPb.InitiateAnalysisRequest) (*aaAnalyticsPb.InitiateAnalysisResponse, error) {
	// check if analysis request entity already exists with same orch id
	ar, arErr := s.arDao.GetByOrchId(ctx, req.GetClientReqId())
	// check if GetByOrch id failed due to some error other than record not found
	if arErr != nil && !errors.Is(arErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching analysis request by client request id",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(arErr))
		return &aaAnalyticsPb.InitiateAnalysisResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if ar != nil {
		if ar.GetActorId() != req.GetActorId() {
			logger.Debug(ctx, "analysis already initiated with this client request id for another actor")
			return &aaAnalyticsPb.InitiateAnalysisResponse{
				Status: rpcPb.StatusInvalidArgumentWithDebugMsg("client req id and actor id mismatch"),
			}, nil
		}
		logger.Debug(ctx, "analysis already initiated with this client request id",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
		return &aaAnalyticsPb.InitiateAnalysisResponse{
			Status: rpcPb.StatusAlreadyExists(),
		}, nil
	}

	// check to not allow multiple analysis requests for the same actor
	ar, arErr = s.arDao.GetActiveByActorId(ctx, req.GetActorId())
	// check if GetActiveByActorId id failed due to some error other than record not found
	if arErr != nil && !errors.Is(arErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching active analysis request by actor id",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(arErr))
		return &aaAnalyticsPb.InitiateAnalysisResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if ar != nil {
		logger.Info(ctx, "analysis already initiated for this actor for orch id",
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.ORCHESTRATION_ID, ar.GetOrchId()))
		return &aaAnalyticsPb.InitiateAnalysisResponse{
			Status: rpcPb.NewStatusWithoutDebug(uint32(aaAnalyticsPb.InitiateAnalysisResponse_ANOTHER_ANALYSIS_FOR_ACTOR_ALREADY_IN_PROGRESS), "another analysis in progress for actor"),
		}, nil
	}

	adpsRes, err := salaryestimation.GetAADataPullStatus(ctx, s.caClient, req.GetActorId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching aa data pull status for actor", zap.Error(err))
		return &aaAnalyticsPb.InitiateAnalysisResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "analysis not needed, no accounts found for actor")
		return &aaAnalyticsPb.InitiateAnalysisResponse{
			Status: rpcPb.NewStatusWithoutDebug(uint32(aaAnalyticsPb.InitiateAnalysisResponse_NO_NEW_DATA_FOR_ANALYSIS_FOR_ANY_ACCOUNT_FOR_ACTOR), "analysis not needed, no accounts found for actor"),
		}, nil
	}
	if !adpsRes.IsDataPullSuccess() {
		logger.Info(ctx, "data pull not successful yet for actor", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &aaAnalyticsPb.InitiateAnalysisResponse{Status: rpcPb.NewStatusWithoutDebug(uint32(aaAnalyticsPb.InitiateAnalysisResponse_AA_DATA_PULL_IN_PROGRESS_FOR_ACTOR), "data pull not successful yet for actor")}, nil
	}

	if req.GetDataExchangeRecord() == nil {
		isAnalysisNeeded := false
		for _, acc := range adpsRes.GetSuccessAccounts() {
			isAnalysisNeededForAcc, err := s.isAnalysisNeeded(ctx, acc)
			if err != nil {
				logger.Error(ctx, "error while checking if analysis is needed", zap.Error(err))
				return &aaAnalyticsPb.InitiateAnalysisResponse{
					Status: rpcPb.StatusInternal(),
				}, nil
			}
			// if analysis is needed for any account, break the loop
			if isAnalysisNeededForAcc {
				isAnalysisNeeded = true
				break
			}
		}
		// for wealth, if no new data is present, we do not need to initiate new analysis
		// for others, we maintain an audit trail and only allow data to be fetched if analysis is success for the particular requested client req id
		if !isAnalysisNeeded && req.GetClient() == common.Owner_OWNER_EPIFI_WEALTH {
			logger.Info(ctx, "analysis not needed for accounts, no new transactions found on any account", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &aaAnalyticsPb.InitiateAnalysisResponse{Status: rpcPb.NewStatusWithoutDebug(uint32(aaAnalyticsPb.InitiateAnalysisResponse_NO_NEW_DATA_FOR_ANALYSIS_FOR_ANY_ACCOUNT_FOR_ACTOR), "analysis not needed for accounts, no new transactions found on any account")}, nil
		}
	}

	// create analysis request entity
	ar, arErr = s.arDao.Create(ctx, &aaAnalyticsPb.AnalysisRequest{
		OrchId:  req.GetClientReqId(),
		ActorId: req.GetActorId(),
		Status:  aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_CREATED,
		Details: &aaAnalyticsPb.Details{
			Client:              req.GetClient(),
			IsInitiatedWithData: req.GetDataExchangeRecord() != nil,
			EmploymentType:      req.GetEmploymentType(),
			OrganisationName:    req.GetOrganisationName(),
		},
	})
	if arErr != nil {
		retStatus := rpcPb.StatusInternal()
		if errors.Is(arErr, epifierrors.ErrAlreadyExists) {
			retStatus = rpcPb.StatusAlreadyExists()
		}
		if errors.Is(arErr, epifierrors.ErrFailedPrecondition) {
			retStatus = rpcPb.NewStatusWithoutDebug(uint32(aaAnalyticsPb.InitiateAnalysisResponse_ANOTHER_ANALYSIS_FOR_ACTOR_ALREADY_IN_PROGRESS), "another analysis in progress for actor")
		}
		logger.Error(ctx, "error while creating analysis request",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(arErr))
		return &aaAnalyticsPb.InitiateAnalysisResponse{
			Status: retStatus,
		}, nil
	}

	// default behaviour is to set action to initiate analysis
	action := ACTION_INITIATE_ANALYSIS
	if req.GetDataExchangeRecord() != nil {
		var err error
		action, err = s.validateRequestDataAndGetAction(ctx, req)
		if err != nil {
			logger.Error(ctx, "error while validating request data and getting action", zap.Error(err))
			return &aaAnalyticsPb.InitiateAnalysisResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
	}

	switch action {
	case ACTION_INITIATE_ANALYSIS:
		resp, err := workflow.ExecuteAsync(ctx, s.temporalClient, caNs.RefreshWealthAnalysis, &caWorkflowPb.RefreshAnalysisWorkflowRequest{
			ActorId:            req.GetActorId(),
			DataExchangeRecord: req.GetDataExchangeRecord(),
			AnalysisRequest:    ar,
			OrchId:             req.GetClientReqId(),
		}, &temporalClient.StartWorkflowOptions{
			ID: wfIdPrefixForWealthWorkflow + req.GetClientReqId(),
		})
		if err != nil {
			logger.Error(ctx, "unable to execute workflow", zap.Error(err), zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
			return &aaAnalyticsPb.InitiateAnalysisResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		logger.Info(ctx, fmt.Sprintf("started workflow with id: %s, run Id: %s", resp.GetID(), resp.GetRunID()),
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))

	case ACTION_ANALYSIS_ALREADY_PRESENT:
		ar.Status = aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_SUCCESS
		ar.CompletedAt = timestampPb.Now()
		if err := s.arDao.Update(ctx, ar, []aaAnalyticsPb.AnalysisRequestFieldMask{
			aaAnalyticsPb.AnalysisRequestFieldMask_ANALYSIS_REQUEST_FIELD_MASK_STATUS,
			aaAnalyticsPb.AnalysisRequestFieldMask_ANALYSIS_REQUEST_FIELD_MASK_COMPLETED_AT,
		}); err != nil {
			logger.Error(ctx, "error while updating analysis request", zap.Error(err))
			return &aaAnalyticsPb.InitiateAnalysisResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}

	case ACTION_REJECT_ANALYSIS_REQUEST:
		ar.Status = aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_FAILED
		ar.CompletedAt = timestampPb.Now()
		if err := s.arDao.Update(ctx, ar, []aaAnalyticsPb.AnalysisRequestFieldMask{
			aaAnalyticsPb.AnalysisRequestFieldMask_ANALYSIS_REQUEST_FIELD_MASK_STATUS,
			aaAnalyticsPb.AnalysisRequestFieldMask_ANALYSIS_REQUEST_FIELD_MASK_COMPLETED_AT,
		}); err != nil {
			logger.Error(ctx, "error while updating analysis request", zap.Error(err))
			return &aaAnalyticsPb.InitiateAnalysisResponse{
				Status: rpcPb.StatusInternal(),
			}, nil
		}
		return &aaAnalyticsPb.InitiateAnalysisResponse{Status: rpcPb.StatusPermissionDenied()}, nil

	default:
		logger.Error(ctx, "unspecified action", zap.Int("action", action))
		return &aaAnalyticsPb.InitiateAnalysisResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &aaAnalyticsPb.InitiateAnalysisResponse{
		Status:      rpcPb.StatusOk(),
		ClientReqId: req.GetClientReqId(),
	}, nil
}

func (s *Service) validateRequestDataAndGetAction(ctx context.Context, req *aaAnalyticsPb.InitiateAnalysisRequest) (int, error) {
	au, auErr := s.auDao.GetByActorId(ctx, req.GetActorId(), common.Owner_OWNER_CONNECTED_ACCOUNT_WEALTH)
	if auErr != nil && !errors.Is(auErr, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "error while fetching analysed user by actor id",
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.CLIENT, req.GetClient().String()), zap.Error(auErr))
		return ACTION_UNSPECIFIED, errors.Wrap(auErr, "error while fetching analysed user by actor id")
	}
	if auErr != nil && errors.Is(auErr, epifierrors.ErrRecordNotFound) {
		return ACTION_INITIATE_ANALYSIS, nil
	}

	// default action set to analysis already present
	action := ACTION_ANALYSIS_ALREADY_PRESENT
	// go through all the files
	for _, file := range req.GetDataExchangeRecord().GetData().GetFiles() {
		// within each file, go through all the accounts
		for _, accMd := range file.GetFileMetadata().GetAccountsMetadata() {
			// check if account is present in the analysed user
			m, ok := au.GetAnalysis().GetAccountAnalyses()[accMd.GetAccountId()]
			// if account not present in analysed user, or last analysed transaction ts is before the latest requested transaction ts
			if !ok || datetime.IsBefore(m.GetLastAnalysedTransactionTs(), accMd.GetLatestTransactionTs()) {
				// for now directly reject if ts is before the latest requested transaction ts
				// TODO: remove this once workflow/activity logic is built
				return ACTION_REJECT_ANALYSIS_REQUEST, nil

				// action = ACTION_INITIATE_ANALYSIS
				// // no need to return as we need to check for all accounts for rejection of analysis request
				// continue
			}
			// if last analysed transaction ts is after the latest requested transaction ts for any case, reject the analysis request
			if datetime.IsAfter(m.GetLastAnalysedTransactionTs(), accMd.GetLatestTransactionTs()) {
				return ACTION_REJECT_ANALYSIS_REQUEST, nil
			}
		}
	}

	return action, nil
}

func (s *Service) GetAnalysisStatus(ctx context.Context, req *aaAnalyticsPb.GetAnalysisStatusRequest) (*aaAnalyticsPb.GetAnalysisStatusResponse, error) {
	ar, arErr := s.arDao.GetByOrchId(ctx, req.GetClientReqId())
	if arErr != nil {
		if errors.Is(arErr, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "analysis request not found with this client request id",
				zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
			return &aaAnalyticsPb.GetAnalysisStatusResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching analysis request by client request id",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(arErr))
		return &aaAnalyticsPb.GetAnalysisStatusResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	var analysisStatus aaAnalyticsPb.GetAnalysisStatusResponse_AnalysisStatus
	switch ar.GetStatus() {
	case aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_CREATED,
		aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_PROCESSING:
		analysisStatus = aaAnalyticsPb.GetAnalysisStatusResponse_ANALYSIS_STATUS_PENDING
	case aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_FAILED:
		analysisStatus = aaAnalyticsPb.GetAnalysisStatusResponse_ANALYSIS_STATUS_FAILED
	case aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_SUCCESS:
		analysisStatus = aaAnalyticsPb.GetAnalysisStatusResponse_ANALYSIS_STATUS_SUCCESS
	default:
		logger.Error(ctx, "unspecified analysis status", zap.Int32("status", int32(ar.GetStatus())))
		return &aaAnalyticsPb.GetAnalysisStatusResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &aaAnalyticsPb.GetAnalysisStatusResponse{
		Status:         rpcPb.StatusOk(),
		AnalysisStatus: analysisStatus,
	}, nil
}

func (s *Service) GetAnalysis(ctx context.Context, req *aaAnalyticsPb.GetAnalysisRequest) (*aaAnalyticsPb.GetAnalysisResponse, error) {
	ar, arErr := s.arDao.GetByOrchId(ctx, req.GetClientReqId())
	if arErr != nil {
		if errors.Is(arErr, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "analysis request not found with this client request id",
				zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()))
			return &aaAnalyticsPb.GetAnalysisResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching analysis request by client request id",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(arErr))
		return &aaAnalyticsPb.GetAnalysisResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if ar.GetDetails().GetClient() != common.Owner_OWNER_EPIFI_WEALTH && !ar.GetDetails().GetIsInitiatedWithData() {
		logger.Debug(ctx, "sharing analysis results not allowed when initiated without data for non epifi wealth entities",
			zap.String(logger.CLIENT, ar.GetDetails().GetClient().String()))
		return &aaAnalyticsPb.GetAnalysisResponse{
			Status: rpcPb.StatusPermissionDenied(),
		}, nil
	}

	isAnalysisCompleted := ar.GetStatus() == aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_SUCCESS ||
		ar.GetStatus() == aaAnalyticsPb.AttemptStatus_ATTEMPT_REQUEST_STATUS_FAILED
	if !isAnalysisCompleted || ar.GetCompletedAt() == nil {
		logger.Debug(ctx, "analysis request not completed yet",
			zap.String(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.String(logger.STATUS, ar.GetStatus().String()))
		return &aaAnalyticsPb.GetAnalysisResponse{
			Status: rpcPb.StatusFailedPrecondition(),
		}, nil
	}

	au, auErr := s.auDao.GetByActorId(ctx, ar.GetActorId(), common.Owner_OWNER_CONNECTED_ACCOUNT_WEALTH)
	if auErr != nil {
		if errors.Is(auErr, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "analysed user not found with this actor id",
				zap.String(logger.ACTOR_ID_V2, ar.GetActorId()), zap.String(logger.CLIENT, ar.GetDetails().GetClient().String()))
			return &aaAnalyticsPb.GetAnalysisResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching analysed user by actor id",
			zap.String(logger.ACTOR_ID_V2, ar.GetActorId()), zap.String(logger.CLIENT, ar.GetDetails().GetClient().String()), zap.Error(auErr))
		return &aaAnalyticsPb.GetAnalysisResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &aaAnalyticsPb.GetAnalysisResponse{
		Status:   rpcPb.StatusOk(),
		Analysis: au.GetAnalysis(),
	}, nil
}

func (s *Service) GetAnalysisByActor(ctx context.Context, req *aaAnalyticsPb.GetAnalysisByActorRequest) (*aaAnalyticsPb.GetAnalysisByActorResponse, error) {
	// todo: find some alternative check
	// if req.GetClient() != common.Owner_OWNER_EPIFI_WEALTH {
	//	logger.Debug(ctx, "sharing analysis results by actor id not allowed for non epifi wealth entities",
	//		zap.String(logger.CLIENT, req.GetClient().String()))
	//	return &aaAnalyticsPb.GetAnalysisByActorResponse{
	//		Status: rpcPb.StatusPermissionDenied(),
	// 	}, nil
	// }

	au, auErr := s.auDao.GetByActorId(ctx, req.GetActorId(), common.Owner_OWNER_CONNECTED_ACCOUNT_WEALTH)
	if auErr != nil {
		if errors.Is(auErr, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "analysed user not found with this actor id",
				zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.CLIENT, req.GetClient().String()))
			return &aaAnalyticsPb.GetAnalysisByActorResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching analysed user by actor id",
			zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.CLIENT, req.GetClient().String()), zap.Error(auErr))
		return &aaAnalyticsPb.GetAnalysisByActorResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	preSignedUrl, preSignedUrlErr := s.s3Client.GetPreSignedUrl(ctx, au.GetAnalysis().GetL1AnalysisFilePath(), analysisDataPresignedUrlExpiryTime)
	if preSignedUrlErr != nil {
		logger.Error(ctx, "error in getting the presigned url", zap.Error(preSignedUrlErr))
		return &aaAnalyticsPb.GetAnalysisByActorResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &aaAnalyticsPb.GetAnalysisByActorResponse{
		Status:              rpcPb.StatusOk(),
		Analysis:            au.GetAnalysis(),
		L1AnalysisSignedUrl: preSignedUrl,
	}, nil
}

func (s *Service) ProcessAnalysisStatusCallback(ctx context.Context, req *aaAnalyticsPb.ProcessAnalysisStatusCallbackRequest) (*aaAnalyticsPb.ProcessAnalysisStatusCallbackResponse, error) {
	vmRes, vmErr := s.vmClient.GetInputIdByVendor(ctx, &vmPb.GetInputIdByVendorRequest{
		Id:     req.GetVendorActorId(),
		Vendor: vgPb.Vendor_IGNOSIS,
	})
	if te := epifigrpc.RPCError(vmRes, vmErr); te != nil {
		logger.Error(ctx, "failed to get input id by vendor", zap.Error(te), zap.String("user_ref_id", req.GetVendorActorId()))
		return nil, status.Errorf(codes.Internal, "internal server error")
	}
	actorId := vmRes.GetInputId()

	ar, arErr := s.arDao.GetActiveByActorId(ctx, actorId)
	if arErr != nil {
		if errors.Is(arErr, epifierrors.ErrRecordNotFound) {
			logger.Debug(ctx, "analysis request not found with this actor id",
				zap.String(logger.ACTOR_ID_V2, actorId))
			return &aaAnalyticsPb.ProcessAnalysisStatusCallbackResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		logger.Error(ctx, "error while fetching active analysis request by actor id",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(arErr))
		return &aaAnalyticsPb.ProcessAnalysisStatusCallbackResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	if ar.GetDetails().GetReferenceId() != req.GetVendorRequestId() {
		logger.Debug(ctx, "analysis request not active for this vendor request id",
			zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.VENDOR_REQUEST, req.GetVendorRequestId()))
		return &aaAnalyticsPb.ProcessAnalysisStatusCallbackResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	payload, payloadErr := protojson.Marshal(&caWorkflowPb.GetAnalysisStatusRequest{
		AnalysisRequest: ar,
		AnalysisStatus:  req.GetAnalysisStatus(),
	})
	if payloadErr != nil {
		logger.Error(ctx, "error while marshalling payload for signal",
			zap.String(logger.ORCHESTRATION_ID, ar.GetOrchId()), zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(payloadErr))
		return &aaAnalyticsPb.ProcessAnalysisStatusCallbackResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	err := s.temporalClient.SignalWorkflow(ctx, wfIdPrefixForWealthWorkflow+ar.GetOrchId(), "", string(caWfNamespace.WealthAnalysisVendorCallbackSignal), payload)
	if err != nil {
		logger.Error(ctx, "failed to signal workflow", zap.Error(err), zap.String(logger.ORCHESTRATION_ID, ar.GetOrchId()), zap.String(logger.ACTOR_ID_V2, actorId))
		return &aaAnalyticsPb.ProcessAnalysisStatusCallbackResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &aaAnalyticsPb.ProcessAnalysisStatusCallbackResponse{
		Status: rpcPb.StatusOk(),
	}, nil
}

func (s *Service) isAnalysisNeeded(ctx context.Context, acc *caxPb.AccountDetails) (bool, error) {
	// check if account is already analysed
	au, auErr := s.auDao.GetByActorId(ctx, acc.GetActorId(), common.Owner_OWNER_CONNECTED_ACCOUNT_WEALTH)
	if auErr != nil && !errors.Is(auErr, epifierrors.ErrRecordNotFound) {
		return false, errors.Wrap(auErr, "error while fetching analysed user by actor id")
	}
	if auErr != nil && errors.Is(auErr, epifierrors.ErrRecordNotFound) {
		return true, nil
	}

	auAnalysedAcc, ok := au.GetAnalysis().GetAccountAnalyses()[acc.GetAccountId()]
	// if account not present in analysed user, analysis is needed
	if !ok {
		return true, nil
	}

	// adding one ms to not fetch the last analysed transaction again
	startTs := timestampPb.New(auAnalysedAcc.GetLastAnalysedTransactionTs().AsTime().Add(1 * time.Millisecond))

	txnsResp, txnsErr := s.caClient.GetRawTxnsForAccountV2(ctx, &caPb.GetRawTxnsForAccountV2Request{
		// even if a single new txn is present, we are allowing for new analysis
		PageContext: &rpcPb.PageContextRequest{PageSize: 1},
		FiType:      acc.GetAccInstrumentType(),
		AccountId:   acc.GetAccountId(),
		Filters: &caPb.RawTransactionFilters{
			TransactionDateAfter:  startTs,
			TransactionDateBefore: timestampPb.Now(),
		},
	})
	if te := epifigrpc.RPCError(txnsResp, txnsErr); te != nil {
		if txnsResp.GetStatus().GetCode() == rpcPb.StatusRecordNotFound().GetCode() {
			logger.Info(ctx, "no new transactions found for account", zap.String(logger.ACTOR_ID_V2, acc.GetActorId()), zap.String(logger.ACCOUNT_ID, acc.GetAccountId()))
			return false, nil
		}
		return false, fmt.Errorf("failed to get raw txns for account, err: %v", te)
	}

	// if new transactions are present, analysis is needed
	return true, nil
}
