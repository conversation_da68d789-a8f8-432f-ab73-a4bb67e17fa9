package events

import (
	"time"

	"github.com/fatih/structs"
	"github.com/google/uuid"

	"github.com/epifi/be-common/pkg/events"
)

type GaveConsentAAServer struct {
	ActorId    string
	ProspectId string
	EventId    string
	Timestamp  time.Time
	EventType  string
	EventName  string
	// consent validity in days
	ConsentValidity int
}

func NewGaveConsentAAServer(actorId string, timestamp time.Time, consentValidity int) *GaveConsentAAServer {
	return &GaveConsentAAServer{
		ActorId:         actorId,
		ProspectId:      "",
		Timestamp:       timestamp,
		EventId:         uuid.New().String(),
		EventType:       events.EventTrack,
		EventName:       EventGaveConsentAAServer,
		ConsentValidity: consentValidity,
	}
}

func (s *GaveConsentAAServer) GetEventType() string {
	return s.EventType
}

func (s *GaveConsentAAServer) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *GaveConsentAAServer) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *GaveConsentAAServer) GetEventId() string {
	return s.EventId
}

func (s *GaveConsentAAServer) GetUserId() string {
	return s.ActorId
}

func (s *GaveConsentAAServer) GetProspectId() string {
	return s.ProspectId
}

func (s *GaveConsentAAServer) GetEventName() string {
	return s.EventName
}

type ConsentPausedNotification struct {
	ActorId    string
	ProspectId string
	EventId    string
	Timestamp  time.Time
	EventType  string
	EventName  string
	// consent properties
	ConsentHandle string
}

func NewConsentPausedNotification(actorId string, timestamp time.Time, consentHandle string) *ConsentPausedNotification {
	return &ConsentPausedNotification{
		ActorId:       actorId,
		ProspectId:    "",
		Timestamp:     timestamp,
		EventId:       uuid.New().String(),
		EventType:     events.EventTrack,
		EventName:     EventConsentPausedNotification,
		ConsentHandle: consentHandle,
	}
}

func (s *ConsentPausedNotification) GetEventType() string {
	return s.EventType
}

func (s *ConsentPausedNotification) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *ConsentPausedNotification) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *ConsentPausedNotification) GetEventId() string {
	return s.EventId
}

func (s *ConsentPausedNotification) GetUserId() string {
	return s.ActorId
}

func (s *ConsentPausedNotification) GetProspectId() string {
	return s.ProspectId
}

func (s *ConsentPausedNotification) GetEventName() string {
	return s.EventName
}

type ConsentRevokedNotification struct {
	ActorId    string
	ProspectId string
	EventId    string
	Timestamp  time.Time
	EventType  string
	EventName  string
	// consent properties
	ConsentHandle string
}

func NewConsentRevokedNotification(actorId string, timestamp time.Time, consentHandle string) *ConsentRevokedNotification {
	return &ConsentRevokedNotification{
		ActorId:       actorId,
		ProspectId:    "",
		Timestamp:     timestamp,
		EventId:       uuid.New().String(),
		EventType:     events.EventTrack,
		EventName:     EventConsentRevokedNotification,
		ConsentHandle: consentHandle,
	}
}

func (s *ConsentRevokedNotification) GetEventType() string {
	return s.EventType
}

func (s *ConsentRevokedNotification) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *ConsentRevokedNotification) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *ConsentRevokedNotification) GetEventId() string {
	return s.EventId
}

func (s *ConsentRevokedNotification) GetUserId() string {
	return s.ActorId
}

func (s *ConsentRevokedNotification) GetProspectId() string {
	return s.ProspectId
}

func (s *ConsentRevokedNotification) GetEventName() string {
	return s.EventName
}

type ConsentExpiredNotification struct {
	ActorId    string
	ProspectId string
	EventId    string
	Timestamp  time.Time
	EventType  string
	EventName  string
	// consent properties
	ConsentHandle string
}

func NewConsentExpiredNotification(actorId string, timestamp time.Time, consentHandle string) *ConsentExpiredNotification {
	return &ConsentExpiredNotification{
		ActorId:       actorId,
		ProspectId:    "",
		Timestamp:     timestamp,
		EventId:       uuid.New().String(),
		EventType:     events.EventTrack,
		EventName:     EventConsentExpiredNotification,
		ConsentHandle: consentHandle,
	}
}

func (s *ConsentExpiredNotification) GetEventType() string {
	return s.EventType
}

func (s *ConsentExpiredNotification) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *ConsentExpiredNotification) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *ConsentExpiredNotification) GetEventId() string {
	return s.EventId
}

func (s *ConsentExpiredNotification) GetUserId() string {
	return s.ActorId
}

func (s *ConsentExpiredNotification) GetProspectId() string {
	return s.ProspectId
}

func (s *ConsentExpiredNotification) GetEventName() string {
	return s.EventName
}

type AnalysisSuccessEvent struct {
	ActorId            string
	ProspectId         string
	EventId            string
	Timestamp          time.Time
	EventType          string
	EventName          string
	AttemptId          string
	AttemptClientReqId string
	Status             string
}

func NewAnalysisSuccessEvent(actorId, attemptId, attemptClientReqId string, timestamp time.Time) *AnalysisSuccessEvent {
	return &AnalysisSuccessEvent{
		ActorId:            actorId,
		ProspectId:         "",
		Timestamp:          timestamp,
		EventId:            uuid.New().String(),
		EventType:          events.EventTrack,
		EventName:          EventAnalysisSuccess,
		AttemptId:          attemptId,
		AttemptClientReqId: attemptClientReqId,
		Status:             "success",
	}
}

func (s *AnalysisSuccessEvent) GetEventType() string {
	return s.EventType
}

func (s *AnalysisSuccessEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *AnalysisSuccessEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *AnalysisSuccessEvent) GetEventId() string {
	return s.EventId
}

func (s *AnalysisSuccessEvent) GetUserId() string {
	return s.ActorId
}

func (s *AnalysisSuccessEvent) GetProspectId() string {
	return s.ProspectId
}

func (s *AnalysisSuccessEvent) GetEventName() string {
	return s.EventName
}

type AnalysisFailedEvent struct {
	ActorId            string
	ProspectId         string
	EventId            string
	Timestamp          time.Time
	EventType          string
	EventName          string
	AttemptId          string
	AttemptClientReqId string
	Status             string
}

func NewAnalysisFailedEvent(actorId, attemptId, attemptClientReqId string, timestamp time.Time) *AnalysisFailedEvent {
	return &AnalysisFailedEvent{
		ActorId:            actorId,
		ProspectId:         "",
		Timestamp:          timestamp,
		EventId:            uuid.New().String(),
		EventType:          events.EventTrack,
		EventName:          EventAnalysisFailed,
		AttemptId:          attemptId,
		AttemptClientReqId: attemptClientReqId,
		Status:             "failed",
	}
}

func (s *AnalysisFailedEvent) GetEventType() string {
	return s.EventType
}

func (s *AnalysisFailedEvent) GetEventProperties() map[string]interface{} {
	properties := map[string]interface{}{}
	structs.FillMap(s, properties)
	return properties
}

func (s *AnalysisFailedEvent) GetEventTraits() map[string]interface{} {
	return nil
}

func (s *AnalysisFailedEvent) GetEventId() string {
	return s.EventId
}

func (s *AnalysisFailedEvent) GetUserId() string {
	return s.ActorId
}

func (s *AnalysisFailedEvent) GetProspectId() string {
	return s.ProspectId
}

func (s *AnalysisFailedEvent) GetEventName() string {
	return s.EventName
}
