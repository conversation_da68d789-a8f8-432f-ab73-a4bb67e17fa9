// nolint: dupl
package comms

import (
	"context"

	"github.com/epifi/be-common/pkg/logger"

	"go.uber.org/zap"

	cardCommsPb "github.com/epifi/gamma/api/card/comms"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/card/helper"
)

type NoIdfErrorRule struct {
	helper *helper.CommsHelper
}

func NewNoIdfErrorRule(helper *helper.CommsHelper) *NoIdfErrorRule {
	return &NoIdfErrorRule{
		helper: helper,
	}
}

func (c *NoIdfErrorRule) GetComms(ctx context.Context, data *cardCommsPb.ActionData) (res []commsPb.CommMessage, resErr error) {
	if c.isApplicable(data) {
		// Pn
		pnTitle, pnBody, err := c.helper.GetPnContentForSwitch(data.GetSwitchNotificationData().GetSwitchNotificationResponse())
		if err != nil {
			logger.Error(ctx, "error in fetching pn content", zap.Error(err))
		} else {
			res = append(res, helper.GetPnWithCommonFields(pnTitle, pnBody, nil),
				helper.GetInAppNotificationWithCommonFields(pnTitle, pnBody))
		}
		res = append(res, &commsPb.SendMessageRequest_Sms{
			Sms: &commsPb.SMSMessage{
				SmsOption: &commsPb.SmsOption{
					Option: &commsPb.SmsOption_DebitCardNoIdfErrorSmsOption{
						DebitCardNoIdfErrorSmsOption: &commsPb.DebitCardNoIdfErrorSmsOption{
							SmsType: commsPb.SmsType_DEBIT_CARD_NO_IDF_ERROR,
							Option: &commsPb.DebitCardNoIdfErrorSmsOption_DebitCardNoIdfErrorSmsOptionV1{
								DebitCardNoIdfErrorSmsOptionV1: &commsPb.DebitCardNoIdfErrorSmsOptionV1{
									TemplateVersion: commsPb.TemplateVersion_VERSION_V1,
									Merchant:        data.GetSwitchNotificationData().GetMerchant(),
									LastFourDigit:   data.GetSwitchNotificationData().GetLastFourDigits(),
								},
							},
						},
					},
				},
			},
		})
	}

	return
}

func (c *NoIdfErrorRule) isApplicable(data *cardCommsPb.ActionData) bool {
	if data.GetSwitchNotificationData().GetSwitchNotificationResponse() != cardEnumsPb.SwitchNotificationResponse_SWITCH_NOTIFICATION_RESPONSE_NO_IDF {
		return false
	}
	return true
}
