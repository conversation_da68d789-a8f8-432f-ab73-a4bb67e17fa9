// nolint:govet,unparam,funlen,protogetter
package provisioning

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/gamma/pkg/accrual"

	beVkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	"github.com/epifi/gamma/card/helper"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"strconv"

	"github.com/mohae/deepcopy"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	pkggenconf "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/bankcust"
	cardPb "github.com/epifi/gamma/api/card"
	cardEnumsPb "github.com/epifi/gamma/api/card/enums"
	pb "github.com/epifi/gamma/api/card/provisioning"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/firefly"
	kycPb "github.com/epifi/gamma/api/kyc"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	segmentPb "github.com/epifi/gamma/api/segment"
	tieringPb "github.com/epifi/gamma/api/tiering"
	externalPb "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	accountsTypes "github.com/epifi/gamma/api/typesv2/account"
	dcScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/debitcard"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/card/config"
	gconf "github.com/epifi/gamma/card/config/genconf"
	cardPkg "github.com/epifi/gamma/pkg/card"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	featureCfg "github.com/epifi/gamma/pkg/feature"
)

type orderPhysicalCardScreenBuilder func(ctx context.Context, screenOptions *dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions,
	cardConf *gconf.Config, amountWithoutGst *types.Money, totalAmountToBePaid *types.Money, cardId string, tier externalPb.Tier, welcomeOfferId string) (*deeplinkPb.Deeplink, error)

var (
	uiEntryPointToOrderPhysicalCardScreenBuilderMap = map[cardEnumsPb.OrderPhysicalCardUiEntryPoint]orderPhysicalCardScreenBuilder{
		cardEnumsPb.OrderPhysicalCardUiEntryPoint_ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_UNSPECIFIED:     getOrderPhysicalCardScreenV2DlDefault,
		cardEnumsPb.OrderPhysicalCardUiEntryPoint_ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_RENEW_CARD_FLOW: getOrderPhysicalCardScreenV2DlDefault,
		cardEnumsPb.OrderPhysicalCardUiEntryPoint_ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_ONBOARDING:      getOrderPhysicalCardScreenV2DlForOnboarding,
	}
)

const (
	defaultTierKey             = "DEFAULT"
	orderCardSkipCtaText       = "Skip"
	tierPropKey                = "UserTier"
	isBalanceAbove360          = "IsBalanceAbove360"
	pricePropKey               = "Price"
	welcomeOfferTextPropKey    = "WelcomeOfferText"
	isAddressVisiblePropKey    = "IsAddressVisible"
	isSkipButtonVisiblePropKey = "IsSkipButtonVisible"
)

// FetchPhysicalCardChargesForUser - fetches charges to be populated on order physical card screen on the basis of user
func (s *Service) FetchPhysicalCardChargesForUser(ctx context.Context, req *pb.FetchPhysicalCardChargesForUserRequest) (*pb.FetchPhysicalCardChargesForUserResponse, error) {
	var (
		res                        = &pb.FetchPhysicalCardChargesForUserResponse{}
		onSkipNextAction           = req.GetOnSkipNextAction()
		postSuccessNextAction      = req.GetPostSuccessNextAction()
		isUserOnboarded            bool
		latestCard                 *cardPb.Card
		kycLevel                   kycPb.KYCLevel
		apo                        accountsTypes.AccountProductOffering
		appPlatform                commontypes.Platform
		appVersion                 uint32
		isTieringAllPlansV2Enabled bool
	)
	ctx = epificontext.CtxWithActorId(ctx, req.GetActorId())

	vkycStatusResp, vkycErr := s.vkycClient.GetVKYCStatus(ctx, &beVkycPb.GetVKYCStatusRequest{ActorId: req.GetActorId()})
	if rpcErr := epifigrpc.RPCError(vkycStatusResp, vkycErr); rpcErr != nil && !vkycStatusResp.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error while fetching vkyc status for actor", zap.Error(rpcErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if vkycStatusResp.GetVkycSummary().GetStatus() == beVkycPb.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_REJECTED {
		res.Status = rpc.NewStatus(uint32(pb.FetchPhysicalCardChargesForUserResponse_VKYC_REJECTED), "VKYC Rejected", "")
		return res, nil
	}

	var onbCheckErr error
	isUserOnboarded, onbCheckErr = s.onboardingCompletionCheck(ctx, req.GetActorId())
	if onbCheckErr != nil {
		logger.Error(ctx, "error while check onb completion status for actor", zap.Error(onbCheckErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	grp, grpCtx := errgroup.WithContext(ctx)
	grp.SetLimit(3)
	grp.Go(func() error {
		var kycErr error
		kycLevel, kycErr = s.fetchKYCLevelByActorId(grpCtx, req.GetActorId())
		if kycErr != nil {
			return errors.Wrap(kycErr, "failed to fetch kyc level for actor")
		}
		return nil
	})

	if epificontext.AppPlatformFromContext(ctx) == commontypes.Platform_PLATFORM_UNSPECIFIED {
		grp.Go(func() error {
			var getDeviceInfoErr error
			appPlatform, appVersion, getDeviceInfoErr = s.getDeviceDetails(grpCtx, req.GetActorId())
			if getDeviceInfoErr != nil {
				if isUserOnboarded {
					return errors.Wrap(getDeviceInfoErr, "error in fetching device details for the user")
				} else {
					logger.Error(grpCtx, "error in fetching device details for the user", zap.Error(getDeviceInfoErr))
				}
			}
			return nil
		})
	}

	grp.Go(func() error {
		confParamsResp, confParamsErr := s.tieringClient.GetConfigParams(grpCtx, &tieringPb.GetConfigParamsRequest{ActorId: req.GetActorId()})
		if rpcErr := epifigrpc.RPCError(confParamsResp, confParamsErr); rpcErr != nil {
			logger.Error(grpCtx, "error while fetching config params for actor", zap.Error(rpcErr))
		}

		isTieringAllPlansV2Enabled = confParamsResp.GetIsMultipleWaysToEnterTieringEnabledForActor()
		return nil
	})

	grp.Go(func() error {
		var cardFetchErr error
		latestCard, cardFetchErr = s.getLatestCardForUser(grpCtx, req.GetActorId())
		if cardFetchErr != nil {
			if errors.Is(cardFetchErr, epifierrors.ErrRecordNotFound) {
				return errors.Wrap(epifierrors.ErrFailedPrecondition, "no active cards found for actor")
			}
			return errors.Wrap(cardFetchErr, "failed to fetch latest card for actor")
		}
		return nil
	})

	grp.Go(func() error {
		savingsAccountResp, saFetchErr := s.savingsClient.GetAccount(grpCtx, &savingsPb.GetAccountRequest{Identifier: &savingsPb.GetAccountRequest_ActorId{
			ActorId: req.GetActorId(),
		}})
		if savingsAccountResp == nil || saFetchErr != nil {
			return errors.Wrap(saFetchErr, "failed to fetch apo for the actor Id")
		}
		apo = savingsAccountResp.GetAccount().GetSkuInfo().GetAccountProductOffering()
		return nil
	})

	waitErr := grp.Wait()
	switch {
	case errors.Is(waitErr, epifierrors.ErrFailedPrecondition):
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	case waitErr != nil:
		logger.Error(ctx, "error collecting data to construct order card page", zap.Error(waitErr))
		res.Status = rpc.StatusFromErrorWithDefaultInternal(waitErr)
		return res, nil
	default:
	}

	// populate app version configs in ctx
	if epificontext.AppPlatformFromContext(ctx) == commontypes.Platform_PLATFORM_UNSPECIFIED &&
		appPlatform != commontypes.Platform_PLATFORM_UNSPECIFIED {
		ctx = epificontext.CtxWithAppPlatform(ctx, appPlatform)
		ctx = epificontext.CtxWithAppVersionCode(ctx, strconv.Itoa(int(appVersion)))
	}

	disableDeliveryAddressUpdate, delUpdCheckErr := s.isDeliveryAddressUpdateDisabled(ctx, req.GetActorId(), kycLevel)
	if delUpdCheckErr != nil {
		logger.Error(ctx, "error in isDeliveryAddressUpdateDisabled()", zap.Error(delUpdCheckErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	if !disableDeliveryAddressUpdate {
		res.Status = rpc.StatusOk()
		res.NextAction = s.getForceUpdateScreenNextAction(ctx)
		return res, nil
	}

	// allow to order physical debit card only regular users
	if apo != accountsTypes.AccountProductOffering_APO_REGULAR {
		logger.Error(ctx, "ordering physical card is not allowed for apo", zap.String("accountProductOffering", apo.String()))
		res.Status = rpc.StatusFailedPrecondition()
		return res, nil
	}

	// if user is not onboarded yet explicitly update onSkipNextAction and postSuccessNextAction
	if !isUserOnboarded {
		postSuccessNextAction = helper.NewGetOnbStageNextActionDl()
		onSkipNextAction = helper.NewSkipOrderPhysicalCardOnbStageDl()
		req.EntryPoint = cardEnumsPb.OrderPhysicalCardUiEntryPoint_ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_ONBOARDING
	}

	nextAction, displayAmount, payableAmount, err := s.getTieringBasedNextAction(ctx, latestCard.GetId(), kycLevel, req.GetActorId(), apo, onSkipNextAction, postSuccessNextAction, isUserOnboarded, isTieringAllPlansV2Enabled, req.GetEntryPoint())
	if err != nil {
		logger.Error(ctx, "failed to set tiering based next action", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	res.NextAction = nextAction
	res.PayableAmount = payableAmount
	res.DisplayAmount = displayAmount
	res.Status = rpc.StatusOk()
	return res, nil
}

// get Next action for FetchPhysicalCardChargesForUser in case tiering is enabled
func (s *Service) getTieringBasedNextAction(ctx context.Context, cardId string, kycLevel kycPb.KYCLevel, actorId string, apo accountsTypes.AccountProductOffering, onSkipNextAction, postSuccessNextAction *deeplinkPb.Deeplink, isUserOnboarded, isTieringAllPlansV2Enabled bool, uiEntryPoint cardEnumsPb.OrderPhysicalCardUiEntryPoint) (*deeplinkPb.Deeplink, *types.Money, *types.Money, error) {
	tier, err := s.getTierOfUser(ctx, actorId)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("error in getting the tier for user %v %w", actorId, err)
	}
	amount, _, err := s.getPhysicalCardChargesAmount(ctx, actorId, tier, kycLevel, apo)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("error in getting tiering based amount for physical card %v %w", actorId, err)
	}

	gstAmount, err := s.getGstAmount(amount)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("error fetching gst amount for order %v %w", cardId, err)
	}

	inclusiveAmount, err := moneyPkg.Sum(amount.GetBeMoney(), gstAmount.GetBeMoney())
	if err != nil {
		return nil, nil, nil, fmt.Errorf("error in money addition %w", err)
	}
	totalPayableAmount := types.GetFromBeMoney(inclusiveAmount)

	logger.Info(ctx, "fetched physical card charges for actor",
		zap.String(logger.AMOUNT, moneyPkg.GetDisplayString(totalPayableAmount.GetBeMoney(), 2, false, false, moneyPkg.IndianNumberSystem)))

	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().EnableOrderPhysicalCardScreenV2().PlatformVersionConfig()) &&
		featureCfg.IsFeatureEnabledForUser(ctx, actorId, &cfg.FeatureReleaseConfig{
			IsFeatureRestricted: s.dynamicConf.Flags().EnableOrderPhysicalCardScreenV2().UserGroupCheckConfig().EnableUserGroupCheck(),
			AllowedUserGroups:   s.dynamicConf.Flags().EnableOrderPhysicalCardScreenV2().UserGroupCheckConfig().AllowedUserGrp(),
		}, s.userGroupClient, s.userClient, s.actorClient) {
		nextAction, nexActionErr := s.getOrderPhysicalCardScreenV2DlForUiEntryPoint(ctx, amount, totalPayableAmount, cardId, actorId, tier, uiEntryPoint, isTieringAllPlansV2Enabled)
		if nexActionErr != nil {
			return nil, nil, nil, fmt.Errorf("failed to get dl for order physical card screen v2 %v %w", actorId, nexActionErr)
		}
		return nextAction, amount, totalPayableAmount, nil
	}

	nextAction, err := s.getDeeplinkForOrderPhysicalCardNewLandingScreenWithTiering(ctx, actorId, tier, amount, cardId, kycLevel, onSkipNextAction, postSuccessNextAction, isUserOnboarded, isTieringAllPlansV2Enabled)
	if err != nil {
		return nil, nil, nil, fmt.Errorf("failed to fetch next action for order physical card screen %v %w", actorId, err)
	}
	return nextAction, amount, totalPayableAmount, nil
}

func (s *Service) getOrderPhysicalCardScreenV2DlForUiEntryPoint(ctx context.Context, amountWithoutGst *types.Money, totalAmountToBePaid *types.Money, cardId string, actorId string,
	tier externalPb.Tier, uiEntryPoint cardEnumsPb.OrderPhysicalCardUiEntryPoint, isTieringAllPlansV2Enabled bool) (*deeplinkPb.Deeplink, error) {

	screenOptionsWithCommonFields, welcomeOfferId, err := s.getOrderPhysicalCardScreenOptionsV2CommonFields(ctx, actorId, amountWithoutGst, totalAmountToBePaid, cardId, tier, isTieringAllPlansV2Enabled)
	if err != nil {
		return nil, fmt.Errorf("error while building dc order card screen options v2 with common fields: %w", err)
	}

	screenBuilderForUIEntryPoint, ok := uiEntryPointToOrderPhysicalCardScreenBuilderMap[uiEntryPoint]
	if !ok {
		logger.Error(ctx, "order physical screen builder mapping not found for uiEntryPoint, falling back to default handling", zap.String(logger.UI_ENTRY_POINT, uiEntryPoint.String()))
		screenBuilderForUIEntryPoint = uiEntryPointToOrderPhysicalCardScreenBuilderMap[cardEnumsPb.OrderPhysicalCardUiEntryPoint_ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_UNSPECIFIED]
	}

	return screenBuilderForUIEntryPoint(ctx, screenOptionsWithCommonFields, s.dynamicConf, amountWithoutGst, totalAmountToBePaid, cardId, tier, welcomeOfferId)
}

func getOrderPhysicalCardScreenV2DlForOnboarding(ctx context.Context, screenOptions *dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions,
	cardConf *gconf.Config, amountWithoutGst *types.Money, totalAmountToBePaid *types.Money, cardId string, tier externalPb.Tier, welcomeOfferId string) (*deeplinkPb.Deeplink, error) {

	screenOptions.ScreenLoadEventProperties[isAddressVisiblePropKey] = commontypes.BooleanEnum_FALSE.String()
	screenOptions.ScreenLoadEventProperties[isSkipButtonVisiblePropKey] = commontypes.BooleanEnum_TRUE.String()
	screenConf := cardConf.OrderPhysicalCardScreenV2Config()
	screenOptions.UiEntryPointStr = cardEnumsPb.OrderPhysicalCardUiEntryPoint_ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_ONBOARDING.String()
	benefitsSection, err := getPhysicalCardBenefitsSection(ctx, screenConf, tier, amountWithoutGst, welcomeOfferId)
	if err != nil {
		return nil, err
	}
	screenOptions.Sections = append(screenOptions.GetSections(), &dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_Section{Section: benefitsSection})

	screenOptions.SecondaryCta = &deeplinkPb.Cta{
		Type: deeplinkPb.Cta_CUSTOM,
		Text: orderCardSkipCtaText,
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_SKIP_ONBOARDING_STAGE_API,
			ScreenOptions: &deeplinkPb.Deeplink_SkipOnboardingStageApiOption{
				SkipOnboardingStageApiOption: &deeplinkPb.SkipOnboardingStageApiOption{
					Stage: onbPb.OnboardingStage_ORDER_PHYSICAL_CARD.String(),
				},
			},
		},
		DisplayTheme: deeplinkPb.Cta_SECONDARY,
	}
	screenOptions.PrimaryCta.Deeplink = deeplinkV3.GetDeeplinkV3WithoutError(
		deeplinkPb.Screen_DELIVERY_ADDRESS_SELECTION_BOTTOM_SHEET,
		&dcScreenOptionsPb.DeliveryAddressSelectionBottomSheetScreenOptions{
			Title: cardConf.AddressSelectionBottomSheetScreenConfig().Title().GetUiTextComponent(),
			Cta: &deeplinkPb.Cta{
				Type:         deeplinkPb.Cta_CUSTOM,
				Text:         cardConf.AddressSelectionBottomSheetScreenConfig().CtaText(),
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
			Amount:          totalAmountToBePaid,
			CardId:          cardId,
			UiEntryPointStr: cardEnumsPb.OrderPhysicalCardUiEntryPoint_ORDER_PHYSICAL_CARD_UI_ENTRY_POINT_ONBOARDING.String(),
		})
	screenOptions.BackToolBar = nil

	return deeplinkV3.GetDeeplinkV3(deeplinkPb.Screen_DC_ORDER_PHYSICAL_CARD_V2_SCREEN, screenOptions)
}

func getOrderPhysicalCardScreenV2DlDefault(_ context.Context, screenOptions *dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions,
	cardConf *gconf.Config, _ *types.Money, _ *types.Money, _ string, _ externalPb.Tier, _ string) (*deeplinkPb.Deeplink, error) {
	screenOptions.ScreenLoadEventProperties[isAddressVisiblePropKey] = commontypes.BooleanEnum_TRUE.String()
	screenOptions.ScreenLoadEventProperties[isSkipButtonVisiblePropKey] = commontypes.BooleanEnum_FALSE.String()

	screenOptions.Sections = append(screenOptions.GetSections(),
		&dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_Section{
			Section: &dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_Section_AddressSection{
				AddressSection: &dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_AddressSection{
					BgColor: cardConf.OrderPhysicalCardScreenV2Config().AddressSectionConfig().BgColor(),
				},
			},
		})
	return deeplinkV3.GetDeeplinkV3(deeplinkPb.Screen_DC_ORDER_PHYSICAL_CARD_V2_SCREEN, screenOptions)
}

func (s *Service) getOrderPhysicalCardScreenOptionsV2CommonFields(ctx context.Context, actorId string, amountWithoutGst *types.Money, totalAmountToBePaid *types.Money, cardId string, tier externalPb.Tier, isTieringAllPlansV2Enabled bool) (*dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions, string, error) {
	var (
		eventPropsMap = map[string]string{
			tierPropKey:  tier.String(),
			pricePropKey: totalAmountToBePaid.String(),
		}

		feeSectionRightIcon  *commontypes.VisualElement
		feeSectionBottomText *ui.IconTextComponent

		orderPhysicalCardScreenV2Conf = s.dynamicConf.OrderPhysicalCardScreenV2Config()
		welcomeOfferId                string
	)

	// fetch segment membership map
	var (
		segmentIdsList []string
		membershipResp *segmentPb.IsMemberResponse
	)
	orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().SegmentIdToOfferInfoMap().Iterate(func(segId string, offerConf *gconf.PhysicalCardWelcomeOfferConfig) (stop bool) {
		segmentIdsList = append(segmentIdsList, segId)
		return false
	})

	if len(segmentIdsList) > 0 {
		var membershipErr error
		membershipResp, membershipErr = s.segmentSrvClient.IsMember(ctx, &segmentPb.IsMemberRequest{
			ActorId:    actorId,
			SegmentIds: segmentIdsList,
			LatestBy:   timestampPb.Now(),
		})
		if te := epifigrpc.RPCError(membershipResp, membershipErr); te != nil {
			logger.Error(ctx, "error while getting membership status for physical card order segment for actor", zap.Error(te), zap.Strings("segmentIds", segmentIdsList))
		}
	}

	if membershipResp != nil && membershipResp.GetStatus().IsSuccess() {
		orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().SegmentIdToOfferInfoMap().Iterate(func(segId string, offerConf *gconf.PhysicalCardWelcomeOfferConfig) (stop bool) {
			membershipStatus, found := membershipResp.GetSegmentMembershipMap()[segId]
			if found && membershipStatus.GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND && membershipStatus.GetIsActorMember() {
				feeSectionRightIcon = offerConf.VisualElement().GetUiVisualElementImageComponent()
				feeSectionBottomText = offerConf.IconTextComponent().GetUiITCComponent()
				welcomeOfferId = offerConf.OfferId()
				return true
			}
			return false
		})
	}
	// default value for the card fee section for right icon
	if feeSectionRightIcon == nil {
		feeSectionRightIcon = orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().DefaultRightIcon().GetUiVisualElementImageComponent()
	}
	if feeSectionBottomText == nil {
		feeSectionBottomText = orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().DefaultOfferInfo().GetUiITCComponent()
	}

	primaryCtaText := orderPhysicalCardScreenV2Conf.OrderCardBtnText()
	if moneyPkg.IsZero(totalAmountToBePaid.GetBeMoney()) {
		primaryCtaText = orderPhysicalCardScreenV2Conf.OrderFreeCardBtnText()
	}

	// sufficient balance check for order card
	balCheckErr := s.checkSufficientBalance(ctx, actorId, moneyPkg.ParseInt(360, moneyPkg.RupeeCurrencyCode))
	switch {
	case balCheckErr != nil && !errors.Is(balCheckErr, epifierrors.ErrFailedPrecondition):
		// skip adding entry in event prop map, log error continue
		logger.Error(ctx, "error while checking sufficient balance to order physical card", zap.Error(balCheckErr))
	case errors.Is(balCheckErr, epifierrors.ErrFailedPrecondition):
		eventPropsMap[isBalanceAbove360] = commontypes.BooleanEnum_FALSE.String()
	default:
		eventPropsMap[isBalanceAbove360] = commontypes.BooleanEnum_TRUE.String()
	}

	primaryCta := &deeplinkPb.Cta{
		Type:         deeplinkPb.Cta_CUSTOM,
		Text:         primaryCtaText,
		DisplayTheme: deeplinkPb.Cta_PRIMARY,
	}

	return &dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions{
		TopVisualElement: orderPhysicalCardScreenV2Conf.TopVisualElement().GetUiVisualElementImageComponent(),
		Title:            orderPhysicalCardScreenV2Conf.ScreenTitle().GetUiTextComponent(),
		Subtitle:         orderPhysicalCardScreenV2Conf.ScreenSubtitle().GetUiTextComponent(),
		Sections: []*dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_Section{
			{
				Section: &dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_Section_CardFeeSection{
					CardFeeSection: &dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_CardFeeSection{
						Title:            orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().Title().GetUiTextComponent(),
						StrikedOffAmount: orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().StrikedOffAmount().GetUiTextComponent(),

						Amount: commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf(
							orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().ActualAmountFormat().DisplayText(),
							moneyPkg.GetDisplayString(totalAmountToBePaid.GetBeMoney(), 2, false, true, moneyPkg.IndianNumberSystem),
						),
							orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().ActualAmountFormat().FontColor(),
							commontypes.FontStyle(commontypes.FontStyle_value[orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().ActualAmountFormat().FontStyle()])),

						RightIcon: feeSectionRightIcon,
						BottomText: feeSectionBottomText.WithDeeplink(
							deeplinkV3.GetDeeplinkV3WithoutError(
								deeplinkPb.Screen_DC_BENEFITS_DETAILS_BOTTOM_SHEET,
								&dcScreenOptionsPb.DCBenfitsDetailsBottomSheetScreenOptions{
									BenefitTypeId:   orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().BottomTextDeeplink().BenefitTypeIdentifier(),
									CardIssuanceFee: amountWithoutGst,
									WelcomeOfferId:  welcomeOfferId,
								})),
						BgColor: orderPhysicalCardScreenV2Conf.CardFeesSectionConfig().BgColor(),
					},
				},
			},
		},
		PrimaryCta: primaryCta,
		Amount:     totalAmountToBePaid,
		CardId:     cardId,
		BackToolBar: &dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_BackToolBar{
			UpgradeTierScreenRedirectionCount: 2,
			IconTextToolBar: &ui.IconTextComponent{
				LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/back-arrow-gray.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{
					Width:  28,
					Height: 28,
				}),
				Deeplink: getUpgradePlanLandingScreen(tier, isTieringAllPlansV2Enabled),
			},
		},
		ScreenLoadEventProperties: eventPropsMap,
	}, welcomeOfferId, nil
}

func getPhysicalCardBenefitsSection(ctx context.Context, screenConf *gconf.OrderPhysicalCardScreenV2Config, tier externalPb.Tier, amountWithoutGst *types.Money, welcomeOfferId string) (*dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_Section_BenefitsListSection, error) {
	benefitsConf, ok := screenConf.UserTierToBenefitsListMap().Load(tier.String())
	if !ok {
		logger.Error(ctx, "benefits info not found in config for tier", zap.String(logger.TIER, tier.String()))
		benefitsConf, ok = screenConf.UserTierToBenefitsListMap().Load(defaultTierKey)
		if !ok {
			return nil, fmt.Errorf("default benefits info not found in config")
		}
	}

	benefitInfoItemsList := make([]*dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_CardBenefitItem, 0)
	for _, benefitConf := range benefitsConf.BenefitsInfoList() {
		benefitInfoItemsList = append(benefitInfoItemsList, &dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_CardBenefitItem{
			LeftIcon: commontypes.GetVisualElementImageFromUrl(benefitConf.LeftIcon.Url).
				WithImageType(commontypes.ImageType_PNG).
				WithProperties(&commontypes.VisualElementProperties{
					Width:  benefitConf.LeftIcon.Properties.Width,
					Height: benefitConf.LeftIcon.Properties.Height,
				}),

			Text: commontypes.GetTextFromStringFontColourFontStyle(
				benefitConf.Title.DisplayText, benefitConf.Title.FontColor,
				commontypes.FontStyle(commontypes.FontStyle_value[benefitConf.Title.FontStyle])),

			RightIcon: commontypes.GetVisualElementImageFromUrl(benefitConf.RightIcon.Url).
				WithImageType(commontypes.ImageType_PNG).
				WithProperties(&commontypes.VisualElementProperties{
					Width:  benefitConf.RightIcon.Properties.Width,
					Height: benefitConf.RightIcon.Properties.Height,
				}),

			Deeplink: deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_DC_BENEFITS_DETAILS_BOTTOM_SHEET,
				&dcScreenOptionsPb.DCBenfitsDetailsBottomSheetScreenOptions{
					BenefitTypeId:   benefitConf.Deeplink.BenefitTypeIdentifier,
					CardIssuanceFee: amountWithoutGst,
					WelcomeOfferId:  welcomeOfferId,
				}),
		})
	}

	return &dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_Section_BenefitsListSection{
		BenefitsListSection: &dcScreenOptionsPb.OrderPhysicalCardV2ScreenOptions_BenefitsSection{
			BenefitsList: benefitInfoItemsList,
			BgColor:      benefitsConf.BgColor(),
		},
	}, nil
}

// nolint: funlen,unparam
// getDeeplinkForOrderPhysicalCardNewLandingScreenWithTiering - returns the deeplink for order physical debit card info screen in case tiering is enabled
func (s *Service) getDeeplinkForOrderPhysicalCardNewLandingScreenWithTiering(ctx context.Context, actorId string, tier externalPb.Tier, amount *types.Money, cardId string, kycLevel kycPb.KYCLevel, onSkipNextAction, postSuccessNextAction *deeplinkPb.Deeplink, isUserOnboarded, isTieringAllPlansV2Enabled bool) (*deeplinkPb.Deeplink, error) {
	benefitsDetailsConfig, ok := s.config.OrderPhysicalDebitCardNewScreenInfo.Benefits[tier.String()]
	if !ok {
		return nil, fmt.Errorf("error in while fetching benefit details from config for tier %s", tier.String())
	}
	rewardDetailsConfig, err := s.getPhysicalCardRewardsInfo(ctx, actorId, tier)
	if err != nil {
		logger.Error(ctx, "error in fetching physical card rewards info", zap.Error(err))
		return nil, fmt.Errorf("error while fetching physical card rewards config for tier %s", tier.String())
	}
	cardUserName, err := s.getCardName(ctx, cardId)
	if err != nil {
		return nil, fmt.Errorf("error while fetching card user name %w", err)
	}

	// UI change
	var (
		rewardInfoTextFontColor = "#F6F9FD"
		rewardInfoIconImgUrl    = rewardDetailsConfig.Icon
		rewardInfoIconWidth     = int32(94)
		rewardInfoIconHeight    = int32(73)
		rewardInfoCornerRadius  = int32(20)
		rewardInfoBgColor       = "#28292B"
		addressTextFontColor    = "#929599"
		screenBgColor           = "#313234"
		showScreenWithUpdatedUi = pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().ShowUpdatedUiOnPhysicalCardOrderScreen())
	)
	if showScreenWithUpdatedUi {
		rewardInfoTextFontColor = "#004E2D"
		rewardInfoIconImgUrl = "https://epifi-icons.pointz.in/card-images/cash-asset.png"
		rewardInfoIconWidth = int32(60)
		rewardInfoIconHeight = int32(60)
		rewardInfoCornerRadius = int32(12)
		rewardInfoBgColor = "#D5E6CE"
		addressTextFontColor = "#313234"
		screenBgColor = "#FFFFFF"
	}

	// Giving quest the highest priority. rewardInfoBgColor is not rewritten ahead
	if s.dynamicConf.OrderPhysicalDCQuest().IsEnabled(ctx) {
		rewardInfoBgColor = s.dynamicConf.OrderPhysicalDCQuest().BannerBackgroundColour(ctx)
		rewardInfoTextFontColor = s.dynamicConf.OrderPhysicalDCQuest().BannerTextColour(ctx)
		rewardInfoIconImgUrl = s.dynamicConf.OrderPhysicalDCQuest().BannerIcon(ctx)
	}

	screen := deeplinkPb.Screen_DEBIT_CARD_ORDER_PHYSICAL_CARD_SCREEN
	screenOptions := &dcScreenOptionsPb.DebitCardOrderPhysicalCardScreenOptions{
		Title: &commontypes.Text{
			FontColor:    "#FFFFFF",
			DisplayValue: &commontypes.Text_PlainString{PlainString: s.config.OrderPhysicalDebitCardNewScreenInfo.ScreenTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_0},
		},
		SubTitle: &commontypes.Text{
			FontColor:    "#8D8D8D",
			DisplayValue: &commontypes.Text_PlainString{PlainString: s.config.OrderPhysicalDebitCardNewScreenInfo.ScreenSubTitle},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_3},
		},
		CardId: cardId,
		ScreenBgColor: &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#18191B",
			},
		},
		CheckBoxText: &dcScreenOptionsPb.DebitCardOrderPhysicalCardScreenOptions_CheckBoxText{
			IsChecked: s.config.OrderPhysicalDebitCardNewScreenInfo.CheckBoxInfo.IsChecked,
			Text: &ui.TextWithHyperlinks{
				Text: &commontypes.Text{
					FontColor:    "#929599",
					DisplayValue: &commontypes.Text_PlainString{PlainString: s.config.OrderPhysicalDebitCardNewScreenInfo.CheckBoxInfo.Text},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
				},
			},
		},
		Tnc: &commontypes.Text{
			FontColor:    "#929599",
			DisplayValue: &commontypes.Text_PlainString{PlainString: "*T&C apply"},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
		},
		// populate rewards info based on user's tier
		RewardInfo: &dcScreenOptionsPb.DebitCardOrderPhysicalCardScreenOptions_RewardInfo{
			Text: &commontypes.Text{
				FontColor:    rewardInfoTextFontColor,
				DisplayValue: &commontypes.Text_Html{Html: rewardDetailsConfig.Title},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Icon: commontypes.GetVisualElementImageFromUrl(rewardInfoIconImgUrl).
				WithProperties(&commontypes.VisualElementProperties{Width: rewardInfoIconWidth, Height: rewardInfoIconHeight}),
			BackgroundInfo: &firefly.DrawableProperties{
				CornerProperty: &firefly.CornerProperty{
					TopStartCornerRadius: rewardInfoCornerRadius,
					TopEndCornerRadius:   rewardInfoCornerRadius,
					BottomStartCorner:    rewardInfoCornerRadius,
					BottomEndCorner:      rewardInfoCornerRadius,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: rewardInfoBgColor},
				},
			},
		},
		KycLevel: types.KYCLevel(kycLevel),
		Flow:     dcScreenOptionsPb.DebitCardOrderPhysicalCardScreenOptions_FLOW_DEBIT_CARD,
		BackToolBar: &dcScreenOptionsPb.DebitCardOrderPhysicalCardScreenOptions_BackToolBar{
			UpgradeTierScreenRedirectionCount: 1,
			IconTextToolBar: &firefly.WrappedIconTextToolBar{
				BackArrowColor: "#646464",
				Properties: &firefly.LayoutProperties{
					Size: &firefly.Size{Width: -1, Height: -2},
				},
			},
		},
		OrderButton: &firefly.WrappedButtonInfo{
			Text: &commontypes.Text{
				FontColor:    "#FFFFFF",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "Order the card"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
			},
			DrawableProperties: &firefly.DrawableProperties{
				CornerProperty: &firefly.CornerProperty{
					TopStartCornerRadius: 19,
					TopEndCornerRadius:   19,
					BottomStartCorner:    19,
					BottomEndCorner:      19,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#00B899",
					},
				},
				Shadow: &widget.Shadow{
					Height: 3,
					Colour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{
							BlockColour: "#00866F",
						},
					},
				},
			},
			Properties: &firefly.LayoutProperties{
				Size:     &firefly.Size{Width: -1, Height: -2},
				Paddings: &firefly.PaddingProperty{Top: 15, Bottom: 15},
				Margins:  &firefly.MarginProperty{Start: 24, End: 24},
			},
			DisabledDrawableProperties: &firefly.DrawableProperties{
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{
						BlockColour: "#E6E9ED",
					},
				},
				CornerProperty: &firefly.CornerProperty{
					TopStartCornerRadius: 19,
					TopEndCornerRadius:   19,
					BottomStartCorner:    19,
					BottomEndCorner:      19,
				},
			},
		},
		ConfirmDetailsComponent: &dcScreenOptionsPb.DebitCardOrderPhysicalCardScreenOptions_ConfirmDetailsComponent{
			ConfirmDetailsComponent: []*deeplinkPb.InfoItemV2{
				{
					Title: &commontypes.Text{
						FontColor:    addressTextFontColor,
						DisplayValue: &commontypes.Text_PlainString{PlainString: s.config.OrderPhysicalDebitCardNewScreenInfo.ShippingAddressInfo.Title},
						FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
					},
					InfoType: deeplinkPb.InfoType_CARD_ACTIVATION_INFO_TYPE_ADDRESS,
				},
			},
			BackgroundInfo: &firefly.DrawableProperties{
				CornerProperty: &firefly.CornerProperty{
					TopStartCornerRadius: 12,
					TopEndCornerRadius:   12,
					BottomStartCorner:    12,
					BottomEndCorner:      12,
				},
				BgColor: &widget.BackgroundColour{
					Colour: &widget.BackgroundColour_BlockColour{BlockColour: screenBgColor},
				},
			},
		},
		UpgradeTierText:       s.getUpgradeTierFooterText(tier, showScreenWithUpdatedUi, isTieringAllPlansV2Enabled),
		CardComponentUserName: commontypes.GetTextFromStringFontColourFontStyle(cardUserName, "#FFFFFF", commontypes.FontStyle_OVERLINE_1),
		RedirectAction:        postSuccessNextAction,
	}

	if onSkipNextAction != nil {
		screenOptions.UpgradeTierText = &ui.TextWithHyperlinks{
			Text: &commontypes.Text{
				FontColor:    "#00B899",
				DisplayValue: &commontypes.Text_PlainString{PlainString: "I will order later"},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
			},
			HyperlinkMap: map[string]*ui.HyperLink{
				"I will order later": {
					Link: &ui.HyperLink_NextActionLink{
						NextActionLink: onSkipNextAction,
					},
				},
			},
		}
	}
	// disable backToolBar in when screen is shown in onboarding flow.
	if !isUserOnboarded {
		screenOptions.BackToolBar = nil
	}

	if showScreenWithUpdatedUi {
		screenOptions.BottomContainerBgColor = &widget.BackgroundColour{
			Colour: &widget.BackgroundColour_BlockColour{
				BlockColour: "#EFF2F6",
			},
		}
	}

	// populate benefits info based on user's tier
	benefitsInfo := screenOptions.GetBenefitsInfo()
	for _, detail := range benefitsDetailsConfig {
		benefitsInfo = append(benefitsInfo, &deeplinkPb.InfoItemV3{
			Title: &commontypes.Text{
				FontColor:    "#E7ECF0",
				DisplayValue: &commontypes.Text_Html{Html: accrual.ReplaceCoinWithPointIfApplicable(detail.Title)},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
			},
			Icon: commontypes.GetVisualElementImageFromUrl(detail.Icon).WithProperties(&commontypes.VisualElementProperties{Width: 40, Height: 40}),
		})
	}
	screenOptions.BenefitsInfo = benefitsInfo

	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().DisableOrderPhysicalCardBenefitsInfo()) {
		lottieRepeatCount := int32(0)

		appPlatForm := epificontext.AppPlatformFromContext(ctx)
		if appPlatForm == commontypes.Platform_ANDROID {
			lottieRepeatCount = 1
		}

		screenOptions.CardComponent = &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Lottie_{
				Lottie: &commontypes.VisualElement_Lottie{
					Source: &commontypes.VisualElement_Lottie_Url{
						Url: "https://epifi-icons.pointz.in/card-images/card-order-lottie.json",
					},
					RepeatCount: lottieRepeatCount,
					Properties: &commontypes.VisualElementProperties{
						Height: 150,
						Width:  231,
						PaddingProperty: &commontypes.PaddingProperty{
							Top:    36,
							Bottom: 30,
						},
					},
				},
			},
		}
	}

	// populate amount info if amount is payable
	amountToBePaid, amountInfo, err := s.getAmountDetails(ctx, amount, cardId, showScreenWithUpdatedUi, isTieringAllPlansV2Enabled)
	if err != nil {
		return nil, fmt.Errorf("error fetching amount details %w", err)
	}
	screenOptions.Amount = amountToBePaid

	// overwrite order button text base on card is paid or not
	if moneyPkg.IsZero(amountToBePaid.GetBeMoney()) && screenOptions.OrderButton != nil {
		screenOptions.OrderButton.Text = commontypes.GetTextFromStringFontColourFontStyle("Proceed", "#FFFFFF", commontypes.FontStyle_BUTTON_M)
	}

	screenOptions.GetConfirmDetailsComponent().ConfirmDetailsComponent = append(screenOptions.GetConfirmDetailsComponent().ConfirmDetailsComponent, amountInfo)

	if screenOptions.GetBackToolBar() != nil && screenOptions.GetBackToolBar().GetIconTextToolBar() != nil {
		screenOptions.GetBackToolBar().GetIconTextToolBar().BackAction = getUpgradePlanLandingScreen(tier, isTieringAllPlansV2Enabled)
	}

	return deeplinkV3.GetDeeplinkV3(screen, screenOptions)
}

// isDeliveryAddressUpdateDisabled - checks if delivery address update should be enabled for user based on some pre-defined conditions
func (s *Service) isDeliveryAddressUpdateDisabled(ctx context.Context, actorId string, kycLevel kycPb.KYCLevel) (bool, error) {
	disableDeliveryAddressUpdate, err := s.isFeatureEnabled(ctx, actorId, types.Feature_FEATURE_DISABLE_DC_DELIVERY_ADDRESS_UPDATE)
	if err != nil {
		return false, errors.Wrap(err, "error in checking feature status")
	}
	return disableDeliveryAddressUpdate, nil
}

//	getPhysicalCardRewardsInfo return reward details from config,
//
// it checks if segment based rewards are enabled, if yes then return segment based configs else return default reward configs
func (s *Service) getPhysicalCardRewardsInfo(ctx context.Context, actorId string, tier externalPb.Tier) (*config.RewardInfo, error) {
	var (
		segmentIds []string
	)

	// Giving quest the highest priority
	if s.dynamicConf.OrderPhysicalDCQuest().IsEnabled(ctx) {
		return &config.RewardInfo{
			Title: s.dynamicConf.OrderPhysicalDCQuest().BannerTitle(ctx),
			Icon:  s.dynamicConf.OrderPhysicalDCQuest().BannerIcon(ctx),
		}, nil
	}
	// Adding this separately as some segment IDs have to be excluded
	isUserPartOfPhysicalDCOfferSegment, err := s.checkPhysicalDCOfferSegment(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "Error in fetching from segmentation service", zap.Error(err))
	}
	if isUserPartOfPhysicalDCOfferSegment {
		return &config.RewardInfo{
			Title: s.dynamicConf.PhysicalDCOfferExperiment().Description(),
			Icon:  s.dynamicConf.PhysicalDCOfferExperiment().Icon(),
		}, nil
	}

	// return default config segment based reward are not enabled
	rewardDetailsConfigPointer, ok := s.config.OrderPhysicalDebitCardNewScreenInfo.Rewards[tier.String()]
	if !ok {
		return nil, fmt.Errorf("error in fetching reward details for tier %s", tier.String())
	}
	rewardDetailsConfig := deepcopy.Copy(rewardDetailsConfigPointer).(*config.RewardInfo)

	// if segment based benefits are not enabled return default
	if !s.dynamicConf.PhysicalCardOrderPageSegmentConfig().BenefitsSegmentConfig().IsEnabled() && !s.dynamicConf.F30IssuanceFeeRefundContent().IsEnabled() {
		return rewardDetailsConfig, nil
	}

	if s.dynamicConf.F30IssuanceFeeRefundContent().IsEnabled() {
		segmentIds = append(segmentIds, s.dynamicConf.F30IssuanceFeeRefundContent().SegmentIds()...)
	}
	if s.dynamicConf.PhysicalCardOrderPageSegmentConfig().BenefitsSegmentConfig().IsEnabled() {
		segments := s.dynamicConf.PhysicalCardOrderPageSegmentConfig().BenefitsSegmentConfig().Segments()
		for _, segmentConfig := range segments {
			segmentIds = append(segmentIds, segmentConfig.SegmentId)
		}
	}

	if len(segmentIds) == 0 {
		return rewardDetailsConfig, nil
	}

	isMemberResp, err := s.segmentSrvClient.IsMember(ctx, &segmentPb.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: segmentIds,
		LatestBy:   timestampPb.Now(),
	})
	if te := epifigrpc.RPCError(isMemberResp, err); te != nil {
		// TODO(CB): should we return default config from here? What is the failure ratio of this RPC?
		return nil, errors.Wrap(te, "error in fetching segment membership details")
	}

	// give segmentsDebitCardF30IssuanceFeeRefund higher priority as it is an offer
	if s.dynamicConf.F30IssuanceFeeRefundContent().IsEnabled() {
		for _, segmentId := range s.dynamicConf.F30IssuanceFeeRefundContent().SegmentIds() {
			if isMemberResp.GetSegmentMembershipMap()[segmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
				isMemberResp.GetSegmentMembershipMap()[segmentId].GetIsActorMember() {
				return &config.RewardInfo{
					Title: s.dynamicConf.F30IssuanceFeeRefundContent().Title(),
					Icon:  s.dynamicConf.PhysicalDCOfferExperiment().Icon(),
				}, nil
			}
		}
	}

	if s.dynamicConf.PhysicalCardOrderPageSegmentConfig().BenefitsSegmentConfig().IsEnabled() {
		segments := s.dynamicConf.PhysicalCardOrderPageSegmentConfig().BenefitsSegmentConfig().Segments()
		for _, segConfig := range segments {
			validFrom := &timestampPb.Timestamp{Seconds: segConfig.ValidFrom}
			validTill := &timestampPb.Timestamp{Seconds: segConfig.ValidTill}
			now := timestampPb.Now().AsTime()
			if isMemberResp.GetSegmentMembershipMap()[segConfig.SegmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND &&
				isMemberResp.GetSegmentMembershipMap()[segConfig.SegmentId].GetIsActorMember() &&
				now.After(validFrom.AsTime()) && now.Before(validTill.AsTime()) {
				return segConfig.RewardInfo, nil
			}
		}
	}

	return rewardDetailsConfig, nil
}

func (s *Service) isUserPartOfF30IssuanceSegment(ctx context.Context, actorId string) bool {
	if s.dynamicConf.PhysicalCardOrderPageSegmentConfig() == nil ||
		s.dynamicConf.PhysicalCardOrderPageSegmentConfig().BenefitsSegmentConfig() == nil ||
		!s.dynamicConf.PhysicalCardOrderPageSegmentConfig().BenefitsSegmentConfig().IsEnabled() {
		return false
	}
	f30IssuanceSegmentIdsList := make([]string, 0)
	for _, segConf := range s.dynamicConf.PhysicalCardOrderPageSegmentConfig().BenefitsSegmentConfig().Segments() {
		if segConf.SegmentId != "" {
			f30IssuanceSegmentIdsList = append(f30IssuanceSegmentIdsList, segConf.SegmentId)
		}
	}
	if len(f30IssuanceSegmentIdsList) == 0 {
		return false
	}

	isMemberResp, err := s.segmentSrvClient.IsMember(ctx, &segmentPb.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: f30IssuanceSegmentIdsList,
		LatestBy:   timestampPb.Now(),
	})
	if te := epifigrpc.RPCError(isMemberResp, err); te != nil {
		logger.Error(ctx, "error while fetching segment membership details", zap.Error(te), zap.Strings("segmentIds", f30IssuanceSegmentIdsList))
		return false
	}

	for _, segmentId := range f30IssuanceSegmentIdsList {
		membershipInfo, ok := isMemberResp.GetSegmentMembershipMap()[segmentId]
		if !ok || membershipInfo.GetSegmentStatus() != segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND || !membershipInfo.GetIsActorMember() {
			continue
		}
		return true
	}
	return false
}

func (s *Service) getAmountDetails(ctx context.Context, amount *types.Money, cardId string, showScreenWithUpdatedUi, isTieringAllPlansV2Enabled bool) (*types.Money, *deeplinkPb.InfoItemV2, error) {
	var (
		subTitleFontColor = "#F6F9FD"
	)
	if showScreenWithUpdatedUi {
		subTitleFontColor = "#313234"
	}

	gstAmount, err := s.getGstAmount(amount)
	if err != nil {
		return nil, nil, fmt.Errorf("error fetching gst amount for order %v %w", cardId, err)
	}

	amountStr := fmt.Sprintf(s.config.OrderPhysicalDebitCardNewScreenInfo.AmountInfo.AmountText,
		moneyPkg.GetDisplayString(amount.GetBeMoney(), 2, false, false, moneyPkg.IndianNumberSystem))
	gstAmountStr := fmt.Sprintf(s.config.OrderPhysicalDebitCardNewScreenInfo.AmountInfo.GstInfo,
		moneyPkg.GetDisplayString(gstAmount.GetBeMoney(), 2, false, false, moneyPkg.IndianNumberSystem), s.config.OrderPhysicalDebitCardGstPercent+"%")
	inclusiveAmount, err := moneyPkg.Sum(amount.GetBeMoney(), gstAmount.GetBeMoney())
	if err != nil {
		return nil, nil, fmt.Errorf("error in money addition %w", err)
	}
	totalAmount := types.GetFromBeMoney(inclusiveAmount)

	amountInfo := &deeplinkPb.InfoItemV2{
		Title: &commontypes.Text{
			FontColor:    "#929599",
			DisplayValue: &commontypes.Text_PlainString{PlainString: s.config.OrderPhysicalDebitCardNewScreenInfo.AmountInfo.Title},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_2XS_CAPS},
		},
		SubTitle: &commontypes.Text{
			FontColor:    subTitleFontColor,
			DisplayValue: &commontypes.Text_PlainString{PlainString: amountStr},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
		},
		Desc: &commontypes.Text{
			FontColor:    "#929599",
			DisplayValue: &commontypes.Text_PlainString{PlainString: gstAmountStr},
			FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
		},
	}

	if pkggenconf.IsFeatureEnabledOnPlatform(ctx, s.dynamicConf.Flags().DisableOrderPhysicalCardBenefitsInfo()) {
		discountPercentage, _ := moneyPkg.CalculateDiffPercentage(amount.GetBeMoney(), moneyPkg.ParseInt(499, moneyPkg.RupeeCurrencyCode))
		if discountPercentage.IsNegative() {
			discountPercentage = discountPercentage.Mul(decimal.NewFromInt32(-1))
		}
		amountInfo.Title.DisplayValue = &commontypes.Text_PlainString{PlainString: "CARD FEE"}
		amountStr = moneyPkg.GetDisplayString(amount.GetBeMoney(), 2, false, true, moneyPkg.IndianNumberSystem)
		amountInfo.AdditionalInfo = &deeplinkPb.InfoItemV2_AdditionalInfo{
			StrikedOffAmount: commontypes.GetTextFromStringFontColourFontStyle(moneyPkg.ToDisplayStringWithPrecision(cardPkg.ActualPhysicalDCCharges, 0), "#B2B5B9", commontypes.FontStyle_NUMBER_M),
			FinalAmount:      commontypes.GetTextFromStringFontColourFontStyle(amountStr, "#313234", commontypes.FontStyle_NUMBER_M),
			DiscountBadge:    commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%d%% OFF", discountPercentage.IntPart()), "#37522A", commontypes.FontStyle_SUBTITLE_XS).WithBgColor("#D5E6CE"),
		}
		amountInfo.SubTitle = nil
	}

	// update gst details in case of payable total amount is zero
	if moneyPkg.IsZero(totalAmount.GetBeMoney()) {
		amountInfo.Desc.DisplayValue = &commontypes.Text_PlainString{PlainString: "We’ve waived off the Card Fee for you!"}
	}

	return totalAmount, amountInfo, nil
}

func (s *Service) getUpgradeTierFooterText(tier externalPb.Tier, showScreenWithUpdatedUi, isTieringAllPlansV2Enabled bool) *ui.TextWithHyperlinks {
	var (
		footerTextFontColor = "#FFFFFF"
	)
	if showScreenWithUpdatedUi {
		footerTextFontColor = "#6A6D70"
	}
	switch tier {
	case externalPb.Tier_TIER_FI_BASIC, externalPb.Tier_TIER_FI_REGULAR:
		return &ui.TextWithHyperlinks{
			Text: &commontypes.Text{
				FontColor:    footerTextFontColor,
				DisplayValue: &commontypes.Text_PlainString{PlainString: s.config.OrderPhysicalDebitCardNewScreenInfo.FooterInfo.Text},
				FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_4},
			},
			HyperlinkMap: map[string]*ui.HyperLink{
				s.config.OrderPhysicalDebitCardNewScreenInfo.FooterInfo.UpgradePlanHyperLinkText: {
					Link: &ui.HyperLink_NextActionLink{
						NextActionLink: getAllPlansRedirectionDeeplink(isTieringAllPlansV2Enabled),
					},
				},
			},
		}
	default:
		return nil
	}
}

func getUpgradePlanLandingScreen(tier externalPb.Tier, isTieringAllPlansV2Enabled bool) *deeplinkPb.Deeplink {
	switch tier {
	case externalPb.Tier_TIER_FI_BASIC, externalPb.Tier_TIER_FI_REGULAR:
		screen := deeplinkPb.Screen_DEBIT_CARD_UPGRADE_PLAN_SCREEN
		screenOptions := &dcScreenOptionsPb.DebitCardUpgradePlanScreenOptions{
			UpgradePlanButton: &firefly.WrappedButtonInfo{
				Text: &commontypes.Text{
					FontColor:    "#00B899",
					DisplayValue: &commontypes.Text_PlainString{PlainString: "Upgrade plan"},
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_M},
				},
				DrawableProperties: &firefly.DrawableProperties{
					CornerProperty: &firefly.CornerProperty{
						TopStartCornerRadius: 19,
						TopEndCornerRadius:   19,
						BottomStartCorner:    19,
						BottomEndCorner:      19,
					},
					BgColor: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_BlockColour{
							BlockColour: "#FFFFFF",
						},
					},
					Shadow: &widget.Shadow{
						Colour: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: "#CED2D6",
							},
						},
						Height: 3,
					},
				},
				Properties: &firefly.LayoutProperties{
					Size:     &firefly.Size{Width: -1, Height: -2},
					Paddings: &firefly.PaddingProperty{Top: 12, Bottom: 12, Start: 24, End: 24},
				},
				Deeplink: getAllPlansRedirectionDeeplink(isTieringAllPlansV2Enabled),
			},
			VisualElementTitleSubtitleElement: &widget.VisualElementTitleSubtitleElement{
				TitleText: &commontypes.Text{
					FontColor:    "#333333",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_L},
					DisplayValue: &commontypes.Text_Html{Html: "Upgrade to get a free card"},
				},
				SubtitleText: &commontypes.Text{
					FontColor:    "#6A6D70",
					FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_S},
					DisplayValue: &commontypes.Text_Html{Html: "When you upgrade to the Plus, Infinite or<br>Salary plan you will get a slick, new VISA<br>debit completely for free!"},
				},
				VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/card-images/upgrade-tier-plan-top-icon.png").
					WithImageType(commontypes.ImageType_PNG).WithProperties(&commontypes.VisualElementProperties{Width: 117, Height: 107}),
			},
		}
		return deeplinkV3.GetDeeplinkV3WithoutError(screen, screenOptions)
	}
	return nil
}

func getAllPlansRedirectionDeeplink(isTieringPlansV2Enabled bool) *deeplinkPb.Deeplink {
	return tiering.AllPlansDeeplink(externalPb.Tier_TIER_UNSPECIFIED, isTieringPlansV2Enabled)
}

// This function checks if the user belongs to some included segments and does not belong to excluded segments
// This is currently specific to DC card offer segments
func (s *Service) checkPhysicalDCOfferSegment(ctx context.Context, actorId string) (bool, error) {
	if !s.dynamicConf.PhysicalDCOfferExperiment().IsExperimentEnabled() {
		return false, nil
	}
	// changing the physical card order amount for a segment of users as part of a campaign
	isUserPartOfPhysicalDCOfferIncludedSegments := false
	isUserPartOfPhysicalDCOfferExcludedSegments := false
	includedSegments := s.dynamicConf.PhysicalDCOfferExperiment().IncludedSegments()
	excludedSegments := s.dynamicConf.PhysicalDCOfferExperiment().ExcludedSegments()
	// Collect all segment IDs to check membership in a single slice.
	segmentIDsToCheck := make([]string, len(includedSegments)+len(excludedSegments))
	for i, segmentObject := range append(includedSegments, excludedSegments...) {
		segmentIDsToCheck[i] = segmentObject.SegmentId
	}
	if len(segmentIDsToCheck) == 0 {
		return false, nil
	}

	// Check user's membership in the collected segments.
	userSegmentRes, err := s.segmentSrvClient.IsMember(ctx, &segment.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: segmentIDsToCheck,
	})

	switch {
	case err != nil:
		return false, fmt.Errorf("error in calling IsMember(): %w", err)
	case epifigrpc.RPCError(userSegmentRes, err) != nil:
		return false, epifigrpc.RPCError(userSegmentRes, err)
	default:
		// Evaluate membership status for included and excluded segments.
		segmentMembershipMap := userSegmentRes.GetSegmentMembershipMap()
		for _, segmentObj := range includedSegments {
			if segmentMembershipMap[segmentObj.SegmentId].GetSegmentStatus() == segment.SegmentStatus_SEGMENT_INSTANCE_FOUND && segmentMembershipMap[segmentObj.SegmentId].GetIsActorMember() {
				isUserPartOfPhysicalDCOfferIncludedSegments = true
				break
			}
		}
		for _, segmentObj := range excludedSegments {
			if segmentMembershipMap[segmentObj.SegmentId].GetSegmentStatus() == segment.SegmentStatus_SEGMENT_INSTANCE_FOUND && segmentMembershipMap[segmentObj.SegmentId].GetIsActorMember() {
				isUserPartOfPhysicalDCOfferExcludedSegments = true
				break
			}
		}
		if isUserPartOfPhysicalDCOfferIncludedSegments && !isUserPartOfPhysicalDCOfferExcludedSegments {
			return true, nil
		}
		return false, nil
	}
}

// getGstAmount - returns the gst amount to be charged corresponding to the amount taken as input by using config specified gst percentage
func (s *Service) getGstAmount(amount *types.Money) (*types.Money, error) {
	gstPercent, err := strconv.ParseInt(s.config.OrderPhysicalDebitCardGstPercent, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("error in fetching gst percent %w", err)
	}

	gstUnits := float64(gstPercent*amount.GetUnits()) / 100
	return types.GetFromBeMoney(moneyPkg.ParseFloat(gstUnits, moneyPkg.RupeeCurrencyCode)), nil
}

// getLatestCardForUser - fetches the latest card for user
func (s *Service) getLatestCardForUser(ctx context.Context, actorId string) (*cardPb.Card, error) {
	fetchCardsResp, err := s.FetchCards(ctx, &pb.FetchCardsRequest{
		Actor:        &types.Actor{Id: actorId},
		IssuingBanks: []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
		CardStates: []cardPb.CardState{cardPb.CardState_CREATED, cardPb.CardState_ACTIVATED,
			cardPb.CardState_SUSPENDED},
		CardTypes:        []cardPb.CardType{},
		CardNetworkTypes: []cardPb.CardNetworkType{},
		CardForms:        []cardPb.CardForm{},
		SortedBy:         cardPb.CardFieldMask_CARD_UPDATED_AT,
	})

	te := epifigrpc.RPCError(fetchCardsResp, err)
	switch {
	case fetchCardsResp.GetStatus().IsRecordNotFound():
		return nil, epifierrors.ErrRecordNotFound
	case te != nil:
		return nil, fmt.Errorf("failed to fetch current cards for actor %v %w", actorId, te)
	}
	return fetchCardsResp.GetCards()[0], nil
}

// getCardName returns the card emboss name
func (s *Service) getCardName(ctx context.Context, cardId string) (string, error) {
	card, err := s.cardDao.GetByID(ctx, cardId)
	if err != nil {
		return "", fmt.Errorf("failed to fetch card by Id %w", err)
	}
	return card.GetBasicInfo().GetCustomerName(), nil
}

// getTierOfUser - fetches tier of user taking actorId as input
func (s *Service) getTierOfUser(ctx context.Context, actorId string) (externalPb.Tier, error) {
	tierResp, err := s.tieringClient.GetTieringPitchV2(ctx, &tieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})
	if err = epifigrpc.RPCError(tierResp, err); err != nil {
		return externalPb.Tier_TIER_UNSPECIFIED, fmt.Errorf("error while fetching the tier for user: %v %w", actorId, err)
	}
	return tierResp.GetCurrentTier(), nil
}

func (s *Service) getDeviceDetails(ctx context.Context, actorId string) (commontypes.Platform, uint32, error) {
	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	if appPlatform != commontypes.Platform_PLATFORM_UNSPECIFIED && appVersion != 0 {
		return appPlatform, uint32(appVersion), nil
	}

	getDeviceDetailsRes, err := s.userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId:       actorId,
		PropertyTypes: []types.DeviceProperty{types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO},
	})
	if te := epifigrpc.RPCError(getDeviceDetailsRes, err); te != nil {
		return commontypes.Platform_PLATFORM_UNSPECIFIED, 0, fmt.Errorf("error in fetching device details for the user %v %w", actorId, te)
	}
	appVersionInfo := getDeviceDetailsRes.GetPropValue(types.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO).GetAppVersionInfo()
	if appVersionInfo == nil {
		return commontypes.Platform_PLATFORM_UNSPECIFIED, 0, fmt.Errorf("empty device app version info %v", actorId)
	}
	return appVersionInfo.GetPlatform(), appVersionInfo.GetAppVersionCode(), nil
}

// fetchKYCLevelByActorId fetches the kyc level for the user
func (s *Service) fetchKYCLevelByActorId(ctx context.Context, actorId string) (kycPb.KYCLevel, error) {
	bcResp, errResp := s.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
	})
	if err := epifigrpc.RPCError(bcResp, errResp); err != nil {
		return kycPb.KYCLevel_UNSPECIFIED, err
	}
	return bcResp.GetBankCustomer().GetDedupeInfo().GetKycLevel(), nil
}

func (s *Service) getForceUpdateScreenNextAction(ctx context.Context) *deeplinkPb.Deeplink {
	var nextAction *deeplinkPb.Cta
	appPlatform, _ := epificontext.AppPlatformAndVersion(ctx)

	switch appPlatform {
	case commontypes.Platform_ANDROID:
		nextAction = &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CONTINUE,
			Text:         "Back to home",
			Deeplink:     &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_HOME},
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		}
	case commontypes.Platform_IOS:
		nextAction = &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CONTINUE,
			Text:         "Back to home",
			Deeplink:     &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_HOME},
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
		}
	default:
	}

	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplinkPb.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplinkPb.InformationPopupOptions{
				Ctas:         []*deeplinkPb.Cta{nextAction},
				TextTitle:    commontypes.GetTextFromStringFontColourFontStyle("Update your Fi app — to order a card", "313234", commontypes.FontStyle_SUBTITLE_2),
				TextSubTitle: commontypes.GetTextFromStringFontColourFontStyle("On the latest app, there's an easier way to order a physical debit card", "#6A6D70", commontypes.FontStyle_BODY_3_PARA),
				BgColor:      "#FFFFFF",
				Icon:         commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/credit_card_images/ufo_ladder_image.png", 80, 80),
			},
		},
	}
}
