package process_amc_eligible_users_test

import (
	"os"
	"testing"

	s3Mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"
	epifiTemporalLogging "github.com/epifi/be-common/pkg/epifitemporal/logging"
	epifitemporalTest "github.com/epifi/be-common/pkg/epifitemporal/test"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/golang/mock/gomock"

	operationalStatusMocks "github.com/epifi/gamma/api/accounts/operstatus/mocks"
	bankcustMock "github.com/epifi/gamma/api/bankcust/mocks"
	commsMocks "github.com/epifi/gamma/api/comms/mocks"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	"github.com/epifi/gamma/card/activity/process_amc_eligible_users"
	cardWorkerConf "github.com/epifi/gamma/card/config/worker"
	daoMocks "github.com/epifi/gamma/card/dao/mocks"
	"github.com/epifi/gamma/card/test"
	slackHelperMocks "github.com/epifi/gamma/casper/test/mocks/helper/slack_helper"
)

var (
	wts  epifitemporalTest.WorkflowTestSuite
	conf *cardWorkerConf.Config
)

type mockedDependencies struct {
	commsClient        *commsMocks.MockCommsClient
	cardDao            *daoMocks.MockCardDao
	userClient         *userMocks.MockUsersClient
	savingsClient      *savingsMocks.MockSavingsClient
	opsClient          *operationalStatusMocks.MockOperationalStatusServiceClient
	analyticsS3Client  *s3Mocks.MockS3Client
	dcDocsS3Client     *s3Mocks.MockS3Client
	slackClient        *slackHelperMocks.MockISlackHelperSvc
	bankCustomerClient *bankcustMock.MockBankCustomerServiceClient
	cardRequestDao     *daoMocks.MockCardRequestDao
}

func newAmcCardActivityProcessorWithMocks(t *testing.T) (*process_amc_eligible_users.Processor, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)
	analyticsS3Client := s3Mocks.NewMockS3Client(ctr)
	dcDocsS3Client := s3Mocks.NewMockS3Client(ctr)
	slackClient := slackHelperMocks.NewMockISlackHelperSvc(ctr)
	cardRequestDao := daoMocks.NewMockCardRequestDao(ctr)
	cardDao := daoMocks.NewMockCardDao(ctr)
	commsClient := commsMocks.NewMockCommsClient(ctr)
	userClient := userMocks.NewMockUsersClient(ctr)
	savingsClient := savingsMocks.NewMockSavingsClient(ctr)
	opsClient := operationalStatusMocks.NewMockOperationalStatusServiceClient(ctr)
	bankCustomerClient := bankcustMock.NewMockBankCustomerServiceClient(ctr)
	md := &mockedDependencies{
		commsClient:        commsClient,
		cardDao:            cardDao,
		userClient:         userClient,
		savingsClient:      savingsClient,
		opsClient:          opsClient,
		analyticsS3Client:  analyticsS3Client,
		dcDocsS3Client:     dcDocsS3Client,
		slackClient:        slackClient,
		bankCustomerClient: bankCustomerClient,
		cardRequestDao:     cardRequestDao,
	}
	processor := process_amc_eligible_users.NewAmcProcessor(commsClient, cardDao, userClient, savingsClient, conf, opsClient, analyticsS3Client, dcDocsS3Client, nil, bankCustomerClient, cardRequestDao)
	return processor, md, func() {
		ctr.Finish()
	}
}

func TestMain(m *testing.M) {
	// nolint: dogsled
	workerConf, _, teardown := test.InitTestWorker()
	conf = workerConf
	wts.SetLogger(epifiTemporalLogging.NewZapAdapter(logger.Log))
	exitCode := m.Run()
	teardown()
	os.Exit(exitCode)
}
