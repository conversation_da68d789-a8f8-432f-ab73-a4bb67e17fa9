package developer

import (
	"context"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/nps/developer"
)

type NpsDevService struct {
	fac *DevFactory
}

func NewNpsDevService(fac *DevFactory) *NpsDevService {
	return &NpsDevService{
		fac: fac,
	}
}

func (s *NpsDevService) GetEntityList(ctx context.Context, req *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	return &cxDsPb.GetEntityListResponse{
		Status: rpcPb.StatusOk(),
		EntityList: []string{
			developer.NpsEntity_NPS_NAV_DETAILS.String(),
			developer.NpsEntity_NPS_SCHEME_DETAILS.String(),
			developer.NpsEntity_NPS_PFM_DETAILS.String(),
		},
	}, nil
}

func (s *NpsDevService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := developer.NpsEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in NPS"),
		}, nil
	}

	paramFetcher, err := s.fac.getParameterListImpl(developer.NpsEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in NPS")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}

	paramList, err := paramFetcher.FetchParamList(ctx, developer.NpsEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list")
		return &cxDsPb.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}

	return &cxDsPb.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (s *NpsDevService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.NpsEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available in NPS"),
		}, nil
	}

	dataFetcher, err := s.fac.getDataImpl(developer.NpsEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available in NPS")
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}

	jsonResp, err := dataFetcher.FetchData(ctx, developer.NpsEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data")
		return &cxDsPb.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}

	return &cxDsPb.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
