package developer

import (
	"context"

	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/nps/developer"
)

type ParameterFetcher interface {
	FetchParamList(ctx context.Context, entity developer.NpsEntity) ([]*cxDsPb.ParameterMeta, error)
}

type DataFetcher interface {
	FetchData(ctx context.Context, entity developer.NpsEntity, filters []*cxDsPb.Filter) (string, error)
}
