package processor

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/nps/developer"

	"github.com/epifi/gamma/nps/catalog/dao"

	"github.com/epifi/gamma/api/typesv2"
)

const (
	errMarshallingResponse = "Error while marshalling response"
)

// NpsNavDetails provides methods to fetch NAV details for NPS schemes.
// It interacts with the NAV history DAO to retrieve NAV data based on scheme and date.
type NpsNavDetails struct {
	navHistoryDao dao.NpsNavHistoryDao
}

// NewNpsNavDetails creates a new NpsNavDetails processor.
// It requires a NpsNavHistoryDao for data access.
func NewNpsNavDetails(navHistoryDao dao.NpsNavHistoryDao,
) *NpsNavDetails {
	return &NpsNavDetails{
		navHistoryDao: navHistoryDao,
	}
}

// FetchParamList returns the list of parameters required to fetch NAV details.
// It specifies Scheme ID and NAV Date as mandatory parameters.
func (n *NpsNavDetails) FetchParamList(_ context.Context, _ developer.NpsEntity) ([]*db_state.ParameterMeta, error) {
	return []*db_state.ParameterMeta{
		{
			Name:            SchemeId,
			Label:           "Scheme ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            paramNavDate,
			Label:           "NAV Date",
			Type:            db_state.ParameterDataType_TIMESTAMP,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}, nil
}

// FetchData fetches NAV details for a given scheme and date.
// It validates input filters and returns the NAV data as a JSON string.
//
//nolint:gosec
func (n *NpsNavDetails) FetchData(ctx context.Context, _ developer.NpsEntity, filters []*db_state.Filter) (string, error) {
	var schemeId string
	var navDate *typesv2.Date

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case SchemeId:
			schemeId = filter.GetStringValue()
		case paramNavDate:
			ts := filter.GetTimestamp()
			if ts != nil {
				t := ts.AsTime()
				navDate = &typesv2.Date{
					Year:  int32(t.Year()),
					Month: int32(t.Month()),
					Day:   int32(t.Day()),
				}
			}
		}
	}

	if schemeId == "" || navDate == nil {
		return "", fmt.Errorf("scheme_id and nav_date are required")
	}

	navHistory, err := n.navHistoryDao.GetBySchemeIdDate(ctx, schemeId, navDate)
	if err != nil {
		return "", err
	}

	marshalledRes, err := json.Marshal(navHistory)
	if err != nil {
		return errMarshallingResponse, nil
	}
	return string(marshalledRes), nil
}
