// nolint:dupl
package processor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/nps/developer"
	"github.com/epifi/gamma/nps/catalog/dao"
)

// NpsSchemesDetails provides methods to fetch scheme details for NPS.
// It interacts with the scheme DAO to retrieve scheme data based on provided filters.
type NpsSchemesDetails struct {
	npsSchemeDao dao.NpsSchemeDao
}

// NewNpsSchemesDetails creates a new NpsSchemesDetails processor.
// It requires a NpsSchemeDao for data access.
func NewNpsSchemesDetails(npsSchemeDao dao.NpsSchemeDao) *NpsSchemesDetails {
	return &NpsSchemesDetails{
		npsSchemeDao: npsSchemeDao,
	}
}

// FetchParamList returns the list of parameters for fetching scheme details.
// It specifies Id and Scheme ID as optional parameters.
func (n *NpsSchemesDetails) FetchParamList(_ context.Context, _ developer.NpsEntity) ([]*db_state.ParameterMeta, error) {
	return []*db_state.ParameterMeta{
		{
			Name:            Id,
			Label:           "Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            SchemeId,
			Label:           "Scheme ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}, nil
}

// FetchData fetches scheme details based on provided filters.
// It validates input and returns the scheme data as a JSON string.
func (n *NpsSchemesDetails) FetchData(ctx context.Context, _ developer.NpsEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", fmt.Errorf("filter cannot be empty to fetch NPS schemes details")
	}

	var (
		id       string
		schemeId string
	)

	// Parse filters
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case Id:
			id = filter.GetStringValue()
		case SchemeId:
			schemeId = filter.GetStringValue()
		default:
			return "", fmt.Errorf("unknown filter applied to get db state")
		}
	}

	if schemeId != "" {
		return n.getEntityByID(ctx, schemeId)
	}

	if id == "" {
		return "", fmt.Errorf("either Scheme ID or ID is required")
	}

	return n.getEntityByExternalId(ctx, id)
}

// getEntityByID fetches a scheme entity by its ID.
// Returns the result as a JSON string or a not found message.
func (n *NpsSchemesDetails) getEntityByID(ctx context.Context, id string) (string, error) {
	scheme, err := n.npsSchemeDao.GetById(ctx, id)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			response := fmt.Sprintf("no NPS scheme found for ID: %s", id)
			logger.Info(ctx, response, zap.String("schemeId", id))
			return response, nil
		}
		logger.Error(ctx, "error fetching NPS scheme by ID", zap.String("schemeId", id), zap.Error(err))
		return "", fmt.Errorf("error fetching NPS scheme by ID: %w", err)
	}

	marshalledRes, err := json.Marshal(scheme)
	if err != nil {
		logger.Error(ctx, "error while marshalling response", zap.Error(err))
		return errMarshallingResponse, nil
	}
	return string(marshalledRes), nil
}

// getEntityByExternalId fetches scheme entities by their external ID.
// Returns the result as a JSON string or a not found message.
func (n *NpsSchemesDetails) getEntityByExternalId(ctx context.Context, id string) (string, error) {
	schemes, err := n.npsSchemeDao.GetByExternalId(ctx, id)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			response := fmt.Sprintf("no NPS schemes found for id: %s", id)
			logger.Info(ctx, response, zap.String("id", id))
			return response, nil
		}
		logger.Error(ctx, "error fetching NPS schemes", zap.String("id", id), zap.Error(err))
		return "", fmt.Errorf("error fetching NPS schemes: %w", err)
	}

	marshalledRes, err := json.Marshal(schemes)
	if err != nil {
		logger.Error(ctx, "error while marshalling response", zap.Error(err))
		return errMarshallingResponse, nil
	}
	return string(marshalledRes), nil
}
