// nolint:dupl
package processor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/nps/developer"
	"github.com/epifi/gamma/nps/catalog/dao"
)

// NpsPfmDetails provides methods to fetch Pension Fund Manager (PFM) details for NPS.
// It interacts with the PFM DAO to retrieve PFM data based on provided filters.
type NpsPfmDetails struct {
	npsPfmDao dao.NpsPensionFundManagerDao
}

// NewNpsPfmDetails creates a new NpsPfmDetails processor.
// It requires a NpsPensionFundManagerDao for data access.
func NewNpsPfmDetails(npsPfmDao dao.NpsPensionFundManagerDao) *NpsPfmDetails {
	return &NpsPfmDetails{
		npsPfmDao: npsPfmDao,
	}
}

// FetchParamList returns the list of parameters for fetching PFM details.
// It specifies Id and PFM ID as optional parameters.
func (n *NpsPfmDetails) FetchParamList(_ context.Context, _ developer.NpsEntity) ([]*db_state.ParameterMeta, error) {
	return []*db_state.ParameterMeta{
		{
			Name:            Id,
			Label:           "Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            PfmId,
			Label:           "PFM ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}, nil
}

// FetchData fetches PFM details based on provided filters.
// It validates input and returns the PFM data as a JSON string.
func (n *NpsPfmDetails) FetchData(ctx context.Context, _ developer.NpsEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", fmt.Errorf("filter cannot be empty to fetch NPS PFM details")
	}

	var (
		id    string
		pfmId string
	)

	// Parse filters
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case Id:
			id = filter.GetStringValue()
		case PfmId:
			pfmId = filter.GetStringValue()
		default:
			return "", fmt.Errorf("unknown filter applied to get db state")
		}
	}

	if pfmId != "" {
		return n.getEntityByID(ctx, pfmId)
	}

	if id == "" {
		return "", fmt.Errorf("either PFM ID or  ID is required")
	}

	return n.getEntityByExternalId(ctx, id)
}

// getEntityByID fetches a PFM entity by its ID.
// Returns the result as a JSON string or a not found message.
func (n *NpsPfmDetails) getEntityByID(ctx context.Context, id string) (string, error) {
	pfm, err := n.npsPfmDao.GetById(ctx, id)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			response := fmt.Sprintf("no NPS PFM found for ID: %s", id)
			logger.Info(ctx, response, zap.String("pfmId", id))
			return response, nil
		}
		logger.Error(ctx, "error fetching NPS PFM by ID", zap.String("pfmId", id), zap.Error(err))
		return "", fmt.Errorf("error fetching NPS PFM by ID: %w", err)
	}

	marshalledRes, err := json.Marshal(pfm)
	if err != nil {
		logger.Error(ctx, "error while marshalling response", zap.Error(err))
		return errMarshallingResponse, nil
	}
	return string(marshalledRes), nil
}

// getEntityByExternalId fetches PFM entities by their external ID.
// Returns the result as a JSON string or a not found message.
func (n *NpsPfmDetails) getEntityByExternalId(ctx context.Context, id string) (string, error) {
	pfms, err := n.npsPfmDao.GetByExternalId(ctx, id)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			response := fmt.Sprintf("no NPS PFMs found for id: %s", id)
			logger.Info(ctx, response, zap.String("id", id))
			return response, nil
		}
		logger.Error(ctx, "error fetching NPS PFMs", zap.String("id", id), zap.Error(err))
		return "", fmt.Errorf("error fetching NPS PFMs: %w", err)
	}

	marshalledRes, err := json.Marshal(pfms)
	if err != nil {
		logger.Error(ctx, "error while marshalling response", zap.Error(err))
		return errMarshallingResponse, nil
	}
	return string(marshalledRes), nil
}
