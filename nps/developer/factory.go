package developer

import (
	"fmt"

	"github.com/epifi/gamma/api/nps/developer"
	"github.com/epifi/gamma/nps/developer/processor"
)

type DevFactory struct {
	NpsNav    *processor.NpsNavDetails
	NpsScheme *processor.NpsSchemesDetails
	NpsPfm    *processor.NpsPfmDetails
}

func NewDevfactory(npsNav *processor.NpsNavDetails, npsScheme *processor.NpsSchemesDetails, npsPfm *processor.NpsPfmDetails) *DevFactory {
	return &DevFactory{
		NpsNav:    npsNav,
		NpsScheme: npsScheme,
		NpsPfm:    npsPfm,
	}
}

func (d *DevFactory) getParameterListImpl(entity developer.NpsEntity) (ParameterFetcher, error) {
	switch entity {
	case developer.NpsEntity_NPS_NAV_DETAILS:
		return d.NpsNav, nil
	case developer.NpsEntity_NPS_SCHEME_DETAILS:
		return d.NpsScheme, nil
	case developer.NpsEntity_NPS_PFM_DETAILS:
		return d.NpsPfm, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}

func (d *DevFactory) getDataImpl(entity developer.NpsEntity) (DataFetcher, error) {
	switch entity {
	case developer.NpsEntity_NPS_NAV_DETAILS:
		return d.NpsNav, nil
	case developer.NpsEntity_NPS_SCHEME_DETAILS:
		return d.NpsScheme, nil
	case developer.NpsEntity_NPS_PFM_DETAILS:
		return d.NpsPfm, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}
