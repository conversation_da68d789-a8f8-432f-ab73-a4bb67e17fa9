//go:build wireinject
// +build wireinject

package wire

import (
	"github.com/google/wire"
	"gorm.io/gorm"

	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/gamma/nps/developer"

	cmdTypes "github.com/epifi/be-common/pkg/cmd/types"
	npsPb "github.com/epifi/gamma/api/nps/developer"
	"github.com/epifi/gamma/api/vendorgateway/nps"
	"github.com/epifi/gamma/nps/catalog"
	"github.com/epifi/gamma/nps/catalog/dao"
	"github.com/epifi/gamma/nps/config"
	"github.com/epifi/gamma/nps/developer/processor"
)

func NpsPGDBGormDBProvider(db cmdTypes.NpsPGDB) *gorm.DB { return db }

func InitializeNPSService(
	db types.NpsPGDB,
	cfg *config.Config,
	vgClient nps.NPSClient,
) (*catalog.Service, error) {
	wire.Build(
		NpsPGDBGormDBProvider,
		catalog.NewService,
		dao.NewNpsPensionFundManagerPGDB,
		dao.NewNpsSchemePGDB,
		dao.NewNpsNavHistoryPGDB,
		wire.Bind(new(dao.NpsPensionFundManagerDao), new(*dao.NpsPensionFundManagerPGDB)),
		wire.Bind(new(dao.NpsSchemeDao), new(*dao.NpsSchemePGDB)),
		wire.Bind(new(dao.NpsNavHistoryDao), new(*dao.NpsNavHistoryPGDB)),
	)
	return &catalog.Service{}, nil
}

func InitializeDevNpsService(
	DeveloperClient npsPb.NpsDbStatesClient,
	db types.NpsPGDB,
) *developer.NpsDevService {
	wire.Build(
		NpsPGDBGormDBProvider,
		dao.NpsNavHistoryDaoWireSet,
		dao.NpsPensionFundManagerDaoWireSet,
		dao.NpsSchemeDaoWireSet,
		processor.NewNpsNavDetails,
		developer.NewDevfactory,
		developer.NewNpsDevService,
		processor.NewNpsSchemesDetails,
		processor.NewNpsPfmDetails,
	)
	return &developer.NpsDevService{}
}
