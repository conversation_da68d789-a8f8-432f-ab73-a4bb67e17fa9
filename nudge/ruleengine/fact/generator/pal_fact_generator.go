package generator

import (
	"context"
	"fmt"

	"github.com/epifi/gamma/nudge/helper"

	"github.com/epifi/gamma/nudge/ruleengine/fact/common"
	factHelper "github.com/epifi/gamma/nudge/ruleengine/fact/helper"
	"github.com/epifi/gamma/nudge/ruleengine/fact/preapprovedloan"

	nudgePb "github.com/epifi/gamma/api/nudge"
	nudgeDataCollectorPb "github.com/epifi/gamma/api/nudge/datacollector"
	palEventsPb "github.com/epifi/gamma/api/preapprovedloan"
)

// PALFactGenerator empty struct for now, might want to inject helper services later
type PALFactGenerator struct {
	factHelperSvc factHelper.IHelperService
	userHelperSvc helper.IUserHelperService
}

func NewPALFactGenerator(factHelperSvc factHelper.IHelperService, userHelperSvc helper.IUserHelperService) *PALFactGenerator {
	return &PALFactGenerator{
		factHelperSvc: factHelperSvc,
		userHelperSvc: userHelperSvc,
	}
}

var _ IFactGenerator = &PALFactGenerator{}

func (k *PALFactGenerator) GenerateFacts(ctx context.Context, collectedData *nudgeDataCollectorPb.CollectedData, nudge *nudgePb.Nudge) ([]common.IFact, error) {
	entryEventIds, err := k.factHelperSvc.GetEntryEventIds(ctx, nudge, collectedData)
	if err != nil {
		return nil, fmt.Errorf("error while fetching entry event ids: %w", err)
	}

	facts := make([]common.IFact, 0)
	switch nudge.GetExitEvent() {
	case nudgePb.NudgeEventDataType_PAL_EVENT:
		for _, entryEventId := range entryEventIds {
			facts = append(facts, &preapprovedloan.PreapprovedLoanFact{
				CommonFact: &common.CommonFact{
					Ctx:               ctx,
					RefId:             collectedData.GetId(),
					ActorId:           collectedData.GetActorId(),
					ActionTime:        collectedData.GetActionTime(),
					ActionType:        collectedData.GetEventDataType().String(),
					Nudge:             nudge,
					EntryEventId:      entryEventId,
					UserHelperService: k.userHelperSvc,
				},
				PALEvent: &palEventsPb.PALEvent{
					EventInfo: collectedData.GetPalEvent().GetEventInfo(),
				},
			})
		}
		return facts, nil
	default:
		return nil, fmt.Errorf("no fact supported for event :%s", nudge.ExitEvent.String())
	}
}
