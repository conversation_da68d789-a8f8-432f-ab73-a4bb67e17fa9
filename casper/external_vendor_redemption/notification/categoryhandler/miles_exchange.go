package categoryhandler

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/accrual"

	evrNotificationPb "github.com/epifi/gamma/api/casper/external_vendor_redemption/notification"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"

	"github.com/epifi/gamma/casper/config/genconf"
	"github.com/epifi/gamma/casper/external_vendor_redemption/dao"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type MilesExchangeHandler struct {
	gconf                       *genconf.Config
	externalVendorRedemptionDao dao.ExternalVendorRedemptionDao
	fiStoreRedemptionDao        dao.FiStoreRedemptionDao
	vendorMappingClient         vendormappingPb.VendorMappingServiceClient
	helper                      *HelperService
}

func NewMilesExchangeHandler(
	gconf *genconf.Config,
	externalVendorRedemptionDao dao.ExternalVendorRedemptionDao,
	fiStoreRedemptionDao dao.FiStoreRedemptionDao,
	vendorMappingClient vendormappingPb.VendorMappingServiceClient,
	helper *HelperService,
) *MilesExchangeHandler {
	return &MilesExchangeHandler{
		gconf:                       gconf,
		externalVendorRedemptionDao: externalVendorRedemptionDao,
		fiStoreRedemptionDao:        fiStoreRedemptionDao,
		vendorMappingClient:         vendorMappingClient,
		helper:                      helper,
	}
}

var _ ICategoryHandler = &MilesExchangeHandler{}

// nolint: funlen,dupl
func (e *MilesExchangeHandler) GetNotificationMessage(ctx context.Context, actorId, redemptionId string, medium evrNotificationPb.Medium, triggerType evrNotificationPb.NotificationTrigger) (*NotificationMediumMessage, error) {
	var (
		message = NotificationMediumMessage{
			CampaignName: commsPb.CampaignName_CAMPAIGN_NAME_FI_STORE_MILES_EXCHANGE,
		}
	)

	if !e.gconf.FiStoreNotificationParams().MilesExchangeNotificationParams().IsEnabled() {
		return nil, nil
	}

	milesExchangeNotificationParams := e.gconf.FiStoreNotificationParams().MilesExchangeNotificationParams()
	if milesExchangeNotificationParams == nil {
		return nil, fmt.Errorf("miles exchange notification params not present in config, err : %w", epifierrors.ErrFailedPrecondition)
	}
	milesExchangeNotificationParamsMap := milesExchangeNotificationParams.NotificationParamsMap()
	if milesExchangeNotificationParamsMap == nil {
		return nil, fmt.Errorf("miles exchange notification params map not present in config, err : %w", epifierrors.ErrFailedPrecondition)
	}

	switch medium {
	case evrNotificationPb.Medium_NOTIFICATION:
		switch triggerType {
		case evrNotificationPb.NotificationTrigger_NOTIFICATION_TRIGGER_ORDER_CONFIRMATION:
			triggerSpecificNotificationParams, isPresent := milesExchangeNotificationParamsMap.Load(triggerType.String())
			if !isPresent {
				return nil, fmt.Errorf("miles exchange notification params not present for notification type : %s, err : %w", triggerType.String(), epifierrors.ErrFailedPrecondition)
			}

			redemptionDetails, err := e.helper.getFiStoreRedemption(ctx, actorId, redemptionId)
			if err != nil {
				logger.Error(ctx, "error while fetching fi store redemption", zap.Error(err))
				return nil, err
			}

			brandName := redemptionDetails.GetRedemptionMetaData().GetBrandName()
			if brandName == "" {
				brandName = redemptionDetails.GetRedemptionMetaData().GetProductName()
			}

			message.NotificationMessage = &fcm.Notification{
				NotificationType: fcm.NotificationType_SYSTEM_TRAY,
				NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
					SystemTrayTemplate: &fcm.SystemTrayTemplate{
						CommonTemplateFields: &fcm.CommonTemplateFields{
							Title: fmt.Sprintf(triggerSpecificNotificationParams.Title(), strings.TrimSpace(brandName)),
							// nit : currently we are using ProductPrice as the number of converted miles,
							// as ProductPrice and GetSpentFiCoinUnitsEquivalentToCash are the same currently,
							// but in future if they are different, we can use according to the use case.
							Body: accrual.ReplaceCoinWithPointIfApplicable(fmt.Sprintf(triggerSpecificNotificationParams.Body(), strconv.Itoa(int(redemptionDetails.GetSpentFiCoinUnits())), strconv.Itoa(int(redemptionDetails.GetProductPrice().GetUnits())))),
							Deeplink: &deeplink.Deeplink{
								Screen: deeplink.Screen_REDEEMED_OFFERS_SCREEN,
							},
						},
					},
				},
			}
		case evrNotificationPb.NotificationTrigger_NOTIFICATION_TRIGGER_ORDER_REFUND:
			triggerSpecificNotificationParams, isPresent := milesExchangeNotificationParamsMap.Load(triggerType.String())
			if !isPresent {
				return nil, fmt.Errorf("miles exchange notification params not present for notification type : %s, err : %w", triggerType.String(), epifierrors.ErrFailedPrecondition)
			}

			redemptionDetails, err := e.helper.getExternalVendorRedemption(ctx, actorId, redemptionId)
			if err != nil {
				logger.Error(ctx, "error while fetching external vendor redemption", zap.Error(err))
				return nil, err
			}

			message.NotificationMessage = &fcm.Notification{
				NotificationType: fcm.NotificationType_SYSTEM_TRAY,
				NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
					SystemTrayTemplate: &fcm.SystemTrayTemplate{
						CommonTemplateFields: &fcm.CommonTemplateFields{
							Title: accrual.ReplaceCoinWithPointIfApplicable(fmt.Sprintf(triggerSpecificNotificationParams.Title(), strconv.Itoa(int(redemptionDetails.GetFiCoinUnits())))),
							Body:  accrual.ReplaceCoinWithPointIfApplicable(triggerSpecificNotificationParams.Body()),
							Deeplink: &deeplink.Deeplink{
								Screen: deeplink.Screen_REDEEMED_OFFERS_SCREEN,
							},
						},
					},
				},
			}
		case evrNotificationPb.NotificationTrigger_NOTIFICATION_TRIGGER_ORDER_PLACED:
			triggerSpecificNotificationParams, isPresent := milesExchangeNotificationParamsMap.Load(triggerType.String())
			if !isPresent {
				return nil, fmt.Errorf("miles exchange notification params not present for notification type : %s, err : %w", triggerType.String(), epifierrors.ErrFailedPrecondition)
			}

			redemptionDetails, err := e.helper.getExternalVendorRedemption(ctx, actorId, redemptionId)
			if err != nil {
				logger.Error(ctx, "error while fetching external vendor redemption", zap.Error(err))
				return nil, err
			}

			message.NotificationMessage = &fcm.Notification{
				NotificationType: fcm.NotificationType_IN_APP,
				NotificationTemplates: &fcm.Notification_InAppTemplate{
					InAppTemplate: &fcm.InAppTemplate{
						CommonTemplateFields: &fcm.CommonTemplateFields{
							Title: accrual.ReplaceCoinWithPointIfApplicable(fmt.Sprintf(triggerSpecificNotificationParams.Title(), strconv.Itoa(int(redemptionDetails.GetFiCoinUnits())))),
							Body:  accrual.ReplaceCoinWithPointIfApplicable(triggerSpecificNotificationParams.Body()),
							IconAttributes: &fcm.IconAttributes{
								IconUrl: triggerSpecificNotificationParams.ImageUrl(),
							},
							Deeplink: &deeplink.Deeplink{
								Screen: deeplink.Screen_WEB_PAGE_WITH_CARD_DETAILS_SCREEN,
								ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&pkgScreenOptionsPb.WebPageWithCardDetailsScreenOptions{
									WebpageTitle:             milesExchangeNotificationParams.WebpageTitle(),
									DisableHardwareBackPress: true,
									DisableDropDownAnimation: true,
									ShowUpiDetails:           true,
									ShowCreditCardDetails:    true,
									RequestMetadata:          fmt.Sprintf("vendor:POSHVINE,target_url:%s", milesExchangeNotificationParams.WebpageUrl()),
								}),
							},
						},
						NotificationPriority: fcm.InAppNotificationPriority_NOTIFICATION_PRIORITY_CRITICAL,
					},
				},
			}

		default:
			return nil, fmt.Errorf("unsupported Notification trigger type while getting push notification medium message, err : %w", epifierrors.ErrPermanent)
		}
	default:
		return nil, fmt.Errorf("unsupported medium while getting notification message, err : %w", epifierrors.ErrPermanent)
	}

	return &message, nil
}
