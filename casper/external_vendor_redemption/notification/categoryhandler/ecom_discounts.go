package categoryhandler

import (
	"context"
	"fmt"
	"strconv"

	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/fieldmaskpb"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/pkg/accrual"

	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	evrNotificationPb "github.com/epifi/gamma/api/casper/external_vendor_redemption/notification"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/fcm"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"

	"github.com/epifi/gamma/casper/config/genconf"
	"github.com/epifi/gamma/casper/external_vendor_redemption/dao"
	"github.com/epifi/gamma/casper/external_vendor_redemption/dao/model"
)

type EComDiscountsHandler struct {
	gconf                *genconf.Config
	fiStoreRedemptionDao dao.FiStoreRedemptionDao
	vendorMappingClient  vendormappingPb.VendorMappingServiceClient
}

func NewEComDiscountsHandler(
	gconf *genconf.Config,
	fiStoreRedemptionDao dao.FiStoreRedemptionDao,
	vendorMappingClient vendormappingPb.VendorMappingServiceClient,
) *EComDiscountsHandler {
	return &EComDiscountsHandler{
		gconf:                gconf,
		fiStoreRedemptionDao: fiStoreRedemptionDao,
		vendorMappingClient:  vendorMappingClient,
	}
}

var _ ICategoryHandler = &EComDiscountsHandler{}

// nolint: funlen
func (e *EComDiscountsHandler) GetNotificationMessage(ctx context.Context, actorId, redemptionId string, medium evrNotificationPb.Medium, triggerType evrNotificationPb.NotificationTrigger) (*NotificationMediumMessage, error) {
	var message NotificationMediumMessage

	if !e.gconf.FiStoreNotificationParams().EComDiscountNotificationParams().IsEnabled() {
		return nil, nil
	}

	fiStoreFmAllFields, err := fieldmaskpb.New(&evrPb.FiStoreRedemption{}, "id", "actor_id", "vendor", "vendor_ref_id", "product_id", "payment_instrument_identifier", "product_price", "spent_cash_units", "spent_fi_coin_units", "order_status", "category", "redemption_meta_data", "order_timestamp")
	if err != nil {
		return nil, err
	}
	redemptions, _, err := e.fiStoreRedemptionDao.GetByFilters(ctx, &model.FiStoreRedemptionsFilters{
		ActorId:      actorId,
		RedemptionId: redemptionId,
	}, fiStoreFmAllFields, nil, 30)
	if err != nil {
		return nil, err
	}

	if len(redemptions) == 0 {
		logger.Error(ctx, "no redemptions found in db with given id", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("redemptionId", redemptionId))
		// passing nil, nil to permanently fail the notification.
		return nil, nil
	}
	redemptionDetails := redemptions[0]

	message.CampaignName = commsPb.CampaignName_CAMPAIGN_NAME_FI_STORE_ECOM

	ecomDiscountNotificationParams := e.gconf.FiStoreNotificationParams().EComDiscountNotificationParams()
	if ecomDiscountNotificationParams == nil {
		return nil, fmt.Errorf("ecom discounts notification params is nil, %w", epifierrors.ErrFailedPrecondition)
	}
	ecomDiscountNotificationParamsMap := ecomDiscountNotificationParams.NotificationParamsMap()
	if ecomDiscountNotificationParamsMap == nil {
		return nil, fmt.Errorf("ecom discount notification parrams map is nil, %w", epifierrors.ErrFailedPrecondition)
	}

	switch medium {
	case evrNotificationPb.Medium_NOTIFICATION:
		switch triggerType {
		case evrNotificationPb.NotificationTrigger_NOTIFICATION_TRIGGER_ORDER_CONFIRMATION:
			triggerSpecificNotificationParams, isPresent := ecomDiscountNotificationParamsMap.Load(triggerType.String())
			if !isPresent {
				return nil, fmt.Errorf("ecom discounts notification params not present for notification type : %s, err : %w", triggerType.String(), epifierrors.ErrFailedPrecondition)
			}

			message.NotificationMessage = &fcm.Notification{
				NotificationType: fcm.NotificationType_SYSTEM_TRAY,
				NotificationTemplates: &fcm.Notification_SystemTrayTemplate{
					SystemTrayTemplate: &fcm.SystemTrayTemplate{
						CommonTemplateFields: &fcm.CommonTemplateFields{
							Title: triggerSpecificNotificationParams.Title(),
							Body:  accrual.ReplaceCoinWithPointIfApplicable(fmt.Sprintf(triggerSpecificNotificationParams.Body(), strconv.Itoa(int(redemptionDetails.GetSpentFiCoinUnits())))),
							Deeplink: &deeplink.Deeplink{
								Screen: deeplink.Screen_REDEEMED_OFFERS_SCREEN,
							},
						},
					},
				},
			}
		default:
			return nil, fmt.Errorf("unsupported Notification trigger type while getting push notification medium message, err : %w", epifierrors.ErrPermanent)
		}
	default:
		return nil, fmt.Errorf("unsupported medium while getting notification message, err : %w", epifierrors.ErrPermanent)
	}

	return &message, nil
}
