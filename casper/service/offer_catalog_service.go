package service

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/pkg/accrual"

	homeFePb "github.com/epifi/gamma/api/frontend/home"
	userGroupPb "github.com/epifi/gamma/api/user/group"

	gammanames "github.com/epifi/gamma/pkg/names"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"
	"fmt"
	"regexp"
	"strings"

	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	beFireflyPb "github.com/epifi/gamma/api/firefly"
	ffAccountsPb "github.com/epifi/gamma/api/firefly/accounting"
	beFireflyEnums "github.com/epifi/gamma/api/firefly/enums"
	types "github.com/epifi/gamma/api/typesv2"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	poshvineVgPb "github.com/epifi/gamma/api/vendorgateway/offers/poshvine"

	beUserPb "github.com/epifi/gamma/api/user"

	pkgScreenOptionsPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	beCardPb "github.com/epifi/gamma/api/card"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/gamma/api/card/provisioning"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/be-common/pkg/epifigrpc"

	dynamicElementsPb "github.com/epifi/gamma/api/dynamic_elements"
	"github.com/epifi/gamma/api/frontend/deeplink"
	segmentPb "github.com/epifi/gamma/api/segment"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/casper/config/genconf"

	"github.com/epifi/gamma/casper/helper/slack_helper"

	"github.com/epifi/be-common/pkg/epificontext"

	"github.com/epifi/gamma/casper/service/offervendor"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"

	casperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/casper/dao"

	"go.uber.org/zap"
)

// following vendors have static offer details
var (
	vendorsHavingStaticOfferDetails = []casperPb.OfferVendor{casperPb.OfferVendor_BLUE_TOKAI_OFFLINE, casperPb.OfferVendor_DHORA_OFFLINE, casperPb.OfferVendor_LIVING_FOOD_OFFLINE,
		casperPb.OfferVendor_SOCK_SOHO_OFFLINE, casperPb.OfferVendor_MONSOON_HARVEST_OFFLINE, casperPb.OfferVendor_TEA_TRUNK_OFFLINE, casperPb.OfferVendor_NAAGIN_SAUCE_OFFLINE,
		casperPb.OfferVendor_TRUE_ELEMENTS_OFFLINE, casperPb.OfferVendor_WE_WORK_OFFLINE, casperPb.OfferVendor_WHOLE_TRUTH_OFFLINE, casperPb.OfferVendor_BONOMI_OFFLINE,
		casperPb.OfferVendor_WHITE_WILLOW_OFFLINE, casperPb.OfferVendor_MAN_MATTERS_OFFLINE, casperPb.OfferVendor_CHAIKA_OFFLINE, casperPb.OfferVendor_BOLD_CARE_OFFLINE,
		casperPb.OfferVendor_BILI_HU_OFFLINE, casperPb.OfferVendor_HUMBLE_OFFLINE, casperPb.OfferVendor_BIGSMALL_OFFLINE, casperPb.OfferVendor_GENERIC_OFFLINE_VENDOR,
		casperPb.OfferVendor_QWIKCILVER, casperPb.OfferVendor_NO_OP_VENDOR, casperPb.OfferVendor_THRIWE, casperPb.OfferVendor_VISTARA, casperPb.OfferVendor_IN_HOUSE,
		casperPb.OfferVendor_DREAMFOLKS, casperPb.OfferVendor_DPANDA, casperPb.OfferVendor_POSHVINE, casperPb.OfferVendor_ITC}
)

type OfferCatalogService struct {
	catalogDao                      dao.OfferCatalogDao
	vendorFactory                   offervendor.IVendorFactory
	slackHelper                     slack_helper.ISlackHelperSvc
	segmentClient                   segmentPb.SegmentationServiceClient
	vendorMappingClient             vmPb.VendorMappingServiceClient
	cardClient                      provisioning.CardProvisioningClient
	usersClient                     beUserPb.UsersClient
	onboardingClient                onbPb.OnboardingClient
	poshvineVgClient                poshvineVgPb.PoshvineClient
	fireflyClient                   beFireflyPb.FireflyClient
	accountingClient                ffAccountsPb.AccountingClient
	externalVendorRedemptionsClient evrPb.ExternalVendorRedemptionServiceClient
	dyconf                          *genconf.Config
	userGroupClient                 userGroupPb.GroupClient
}

func NewOfferCatalogService(
	catalogDao dao.OfferCatalogDao,
	vendorFactory offervendor.IVendorFactory,
	slackHelper slack_helper.ISlackHelperSvc,
	segmentClient segmentPb.SegmentationServiceClient,
	vendorMappingClient vmPb.VendorMappingServiceClient,
	cardClient provisioning.CardProvisioningClient,
	usersClient beUserPb.UsersClient,
	onboardingClient onbPb.OnboardingClient,
	poshvineVgClient poshvineVgPb.PoshvineClient,
	fireflyClient beFireflyPb.FireflyClient,
	accountingClient ffAccountsPb.AccountingClient,
	externalVendorRedemptionsClient evrPb.ExternalVendorRedemptionServiceClient,
	dyconf *genconf.Config,
	userGroupClient userGroupPb.GroupClient,
) *OfferCatalogService {
	return &OfferCatalogService{
		catalogDao:                      catalogDao,
		vendorFactory:                   vendorFactory,
		slackHelper:                     slackHelper,
		segmentClient:                   segmentClient,
		vendorMappingClient:             vendorMappingClient,
		cardClient:                      cardClient,
		usersClient:                     usersClient,
		onboardingClient:                onboardingClient,
		fireflyClient:                   fireflyClient,
		accountingClient:                accountingClient,
		poshvineVgClient:                poshvineVgClient,
		externalVendorRedemptionsClient: externalVendorRedemptionsClient,
		dyconf:                          dyconf,
		userGroupClient:                 userGroupClient,
	}
}

var _ casperPb.OfferCatalogServiceServer = &OfferCatalogService{}

func (o OfferCatalogService) CreateOffer(ctx context.Context, req *casperPb.CreateOfferRequest) (*casperPb.CreateOfferResponse, error) {
	if ValidationFailureInfoList := o.validateCreateOfferRequest(ctx, req); ValidationFailureInfoList != nil {
		return &casperPb.CreateOfferResponse{
			Status:                 rpc.StatusInvalidArgument(),
			ValidationFailureInfos: ValidationFailureInfoList,
		}, nil
	}
	offerId, err := o.catalogDao.CreateOffer(ctx, req)
	if err != nil {
		logger.Error(ctx, "error while creating offer", zap.Error(err), zap.Any("req", req))
		return &casperPb.CreateOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	//nocustomlint:goroutine
	go func() {
		defer logger.RecoverPanicAndError(ctx)

		clonedCtx := epificontext.CloneCtx(ctx)

		offerDetails, fetchErr := o.catalogDao.GetOfferDetailsById(clonedCtx, offerId)
		if fetchErr != nil {
			logger.Error(clonedCtx, "error fetching offer details by id", zap.Error(fetchErr), zap.String(logger.OFFER_ID, offerId))
			return
		}

		if alertErr := o.slackHelper.SendMessage(clonedCtx, o.dyconf.SlackAlertingConfig().EntityUpdatesChannelId(), "New Offer(Non CBR) Created", offerDetails); alertErr != nil {
			logger.Error(clonedCtx, "error sending slack alert for offer creation", zap.Error(alertErr),
				zap.String(logger.OFFER_ID, offerId),
			)
		}
	}()

	return &casperPb.CreateOfferResponse{
		Status:  rpc.StatusOk(),
		OfferId: offerId,
	}, nil
}

func (o OfferCatalogService) UpdateOfferDisplay(ctx context.Context, req *casperPb.UpdateOfferDisplayRequest) (*casperPb.UpdateOfferDisplayResponse, error) {
	if validationFailureInfoList := o.validateUpdateOfferDisplayRequest(ctx, req); len(validationFailureInfoList) != 0 {
		logger.Error(ctx, "error in update offer display request", zap.Any("validationFailures", validationFailureInfoList))
		return &casperPb.UpdateOfferDisplayResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("validation failed: %s", validationFailureInfoList)),
		}, nil
	}
	err := o.catalogDao.UpdateOfferDisplay(ctx, req)
	if err != nil {
		logger.Error(ctx, "error while updating offer display", zap.Error(err), zap.Any("req", req))
		return &casperPb.UpdateOfferDisplayResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &casperPb.UpdateOfferDisplayResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// todo (utkarsh) : add filter based on client requirement to prevent doing extra processing.
func (o OfferCatalogService) GetOfferDetailsById(ctx context.Context, req *casperPb.GetOfferDetailsByIdRequest) (*casperPb.GetOfferDetailsByIdResponse, error) {
	offer, err := o.catalogDao.GetOfferDetailsById(ctx, req.OfferId)
	if err != nil {
		logger.Error(ctx, "error while getting offer by id", zap.Error(err), zap.Any("req", req))
		return &casperPb.GetOfferDetailsByIdResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	updatedOffers, err := o.withUpdatedVendorOfferDetails(ctx, []*casperPb.Offer{offer})
	if err != nil || len(updatedOffers) == 0 {
		logger.Error(ctx, "error getting updated offer details from vendor", zap.Error(err), zap.Any("req", req))
		return &casperPb.GetOfferDetailsByIdResponse{
			Status: rpc.StatusInternalWithDebugMsg("error getting updated offer details from vendor"),
		}, nil
	}

	return &casperPb.GetOfferDetailsByIdResponse{
		Status: rpc.StatusOk(),
		Offer:  updatedOffers[0],
	}, nil
}

func (o OfferCatalogService) GetBulkOfferDetailsByIds(ctx context.Context, req *casperPb.GetBulkOfferDetailsByIdsRequest) (*casperPb.GetBulkOfferDetailsByIdsResponse, error) {
	offers, err := o.catalogDao.GetBulkOfferDetailsByIds(ctx, req)
	if err != nil {
		logger.Error(ctx, "error while getting offers by ids", zap.Error(err), zap.Any("req", req))
		return &casperPb.GetBulkOfferDetailsByIdsResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	// if 'with_updated_vendor_offer_details' flag is set in request then update the vendor
	// offer details for the fetched offers otherwise directly return the fetched offers.
	updatedOffers := offers
	if req.GetWithUpdatedVendorOfferDetails() {
		updatedOffers, err = o.withUpdatedVendorOfferDetails(ctx, offers)
		if err != nil {
			logger.Error(ctx, "error getting updated offer details from vendor", zap.Error(err), zap.Any("req", req))
			return &casperPb.GetBulkOfferDetailsByIdsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	}

	return &casperPb.GetBulkOfferDetailsByIdsResponse{
		Status: rpc.StatusOk(),
		Offers: updatedOffers,
	}, nil
}

func (o OfferCatalogService) DeleteOfferById(ctx context.Context, req *casperPb.DeleteOfferByIdRequest) (*casperPb.DeleteOfferByIdResponse, error) {
	if err := o.catalogDao.DeleteOfferById(ctx, req.OfferId); err != nil {
		logger.Error(ctx, "error while deleting offer by id", zap.Error(err), zap.Any("req", req))
		return &casperPb.DeleteOfferByIdResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}
	return &casperPb.DeleteOfferByIdResponse{
		Status: rpc.StatusOk(),
	}, nil
}

// withUpdatedVendorOfferDetails populates the offer with updated vendor offer details.
// It fetches the updated details for the offers and populates them with those details.
// Offer details for some vendors can be dynamic (can change anytime) and as a result we
// may need to update them in realtime. It does not persists any updated details in DB.
// @param offersList : list of offers whose details need to be updated.
// nolint: gocritic
func (o OfferCatalogService) withUpdatedVendorOfferDetails(ctx context.Context, offersList []*casperPb.Offer) ([]*casperPb.Offer, error) {
	allUpdatedOffers := make([]*casperPb.Offer, 0)
	vendorToOffersMap := make(map[casperPb.OfferVendor][]*casperPb.Offer)

	// put each offer into its vendor bucket
	for _, offer := range offersList {
		vendor := offer.GetVendorName()
		updatedVendorOfferList := append(vendorToOffersMap[vendor], offer)
		vendorToOffersMap[vendor] = updatedVendorOfferList
	}

	// update offers for each vendor's bucket
	for vendorName, vendorOffers := range vendorToOffersMap {
		// if vendor is unspecified, then we don't need to update vendor offer details.
		// currently valid for FI_CARD offer use cases where offers are not linked to any vendor
		if vendorName == casperPb.OfferVendor_UNSPECIFIED {
			allUpdatedOffers = append(allUpdatedOffers, vendorOffers...)
			continue
		}
		// For some vendors, offers details are static, for them just add the existing offer details to allUpdatedOffers
		if IsVendorPresentInList(vendorName, vendorsHavingStaticOfferDetails) {
			allUpdatedOffers = append(allUpdatedOffers, vendorOffers...)
			continue
		}
		// if vendor does not has static offer details, then update it
		vendorClient, err := o.vendorFactory.Get(vendorName)
		if err != nil {
			return nil, err
		}
		updatedVendorOffers, err := vendorClient.GetOfferWithUpdatedVendorOfferDetails(ctx, vendorOffers)
		if err != nil {
			// todo (utkarsh, vikas) : send these errors to prometheus.
			// not throwing error as complete method shouldn't fail due to some offer vendors.
			logger.Error(ctx, "error getting updated offers for vendor", zap.Any("vendor", vendorName), zap.Error(err))
			continue
		}
		allUpdatedOffers = append(allUpdatedOffers, updatedVendorOffers...)
	}
	return allUpdatedOffers, nil
}

func IsVendorPresentInList(toBeSearched casperPb.OfferVendor, list []casperPb.OfferVendor) bool {
	for _, cur := range list {
		if toBeSearched == cur {
			return true
		}
	}
	return false
}

// FetchDynamicElements fetches dynamic elements for the given screen name.
func (o OfferCatalogService) FetchDynamicElements(ctx context.Context, request *dynamicElementsPb.FetchDynamicElementsRequest) (*dynamicElementsPb.FetchDynamicElementsResponse, error) {
	screenName := request.GetClientContext().GetScreenName()
	switch screenName {
	case deeplink.Screen_HOME:
		return o.getHomeScreenDynamicElements(ctx, request)
	case deeplink.Screen_MY_REWARDS_SCREEN:
		return o.getMyRewardsScreenDynamicElements(ctx, request)
	default:
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}
}

// This function fetches dynamic elements for the "My Rewards" screen.
// Note: Once this function's logic is implemented, add the service name in ScreenToServiceMapping in dynamic-elements-prod.yml file.
// nolint: unparam
func (o OfferCatalogService) getMyRewardsScreenDynamicElements(ctx context.Context, request *dynamicElementsPb.FetchDynamicElementsRequest) (*dynamicElementsPb.FetchDynamicElementsResponse, error) {
	return &dynamicElementsPb.FetchDynamicElementsResponse{
		Status: rpc.StatusRecordNotFound(),
	}, nil
}

// This function fetches dynamic elements for the home screen.
// It fetches the dynamic elements based on the section and version specified in the request.
// nolint: funlen
func (o OfferCatalogService) getHomeScreenDynamicElements(ctx context.Context, request *dynamicElementsPb.FetchDynamicElementsRequest) (*dynamicElementsPb.FetchDynamicElementsResponse, error) {
	screenAdditionalInfo, ok := request.GetClientContext().GetScreenAdditionalInfo().(*dynamicElementsPb.ClientContext_HomeInfo)
	if !ok {
		logger.Error(ctx, "failed to fetch home info from context")
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	var (
		dynamicElements []*dynamicElementsPb.DynamicElement
		err             error
	)
	homeInfo := screenAdditionalInfo.HomeInfo
	switch {
	case homeInfo.GetSection() == dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_BODY && homeInfo.GetVersion() == dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2:
		dynamicElements, err = o.getBannerV2Element(ctx, request.GetActorId())
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			return &dynamicElementsPb.FetchDynamicElementsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		case err != nil:
			logger.Error(ctx, "error while fetching banner v2 element", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
			return &dynamicElementsPb.FetchDynamicElementsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	case homeInfo.GetSection() == dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_BODY2 && homeInfo.GetVersion() == dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2:
		dynamicElements, err = o.getScrollableBannerElement(ctx, request.GetActorId())
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			return &dynamicElementsPb.FetchDynamicElementsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		case err != nil:
			logger.Error(ctx, "error while fetching scrollable banner element", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
			return &dynamicElementsPb.FetchDynamicElementsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	case homeInfo.GetSection() == dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY && homeInfo.GetVersion() == dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2:
		dynamicElements, err = o.getPrimaryFeatureElement(ctx, request.GetActorId())
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			return &dynamicElementsPb.FetchDynamicElementsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		case err != nil:
			logger.Error(ctx, "error while fetching primary feature element", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
			return &dynamicElementsPb.FetchDynamicElementsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	case homeInfo.GetSection() == dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_SECONDARY && homeInfo.GetVersion() == dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2:
		dynamicElements, err = o.getSecondaryFeatureElement(ctx, request.GetActorId())
		switch {
		case errors.Is(err, epifierrors.ErrRecordNotFound):
			return &dynamicElementsPb.FetchDynamicElementsResponse{
				Status: rpc.StatusRecordNotFound(),
			}, nil
		case err != nil:
			logger.Error(ctx, "error while fetching secondary feature element", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
			return &dynamicElementsPb.FetchDynamicElementsResponse{
				Status: rpc.StatusInternalWithDebugMsg(err.Error()),
			}, nil
		}
	default:
		return &dynamicElementsPb.FetchDynamicElementsResponse{
			Status: rpc.StatusRecordNotFound(),
		}, nil
	}

	return &dynamicElementsPb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: dynamicElements,
	}, nil
}

// nolint: funlen, dupl
func (o OfferCatalogService) getBannerV2Element(ctx context.Context, actorId string) ([]*dynamicElementsPb.DynamicElement, error) {
	// Banner for dpanda offer redirection.
	if !o.dyconf.DynamicElementConfig().IsEnabled() {
		logger.Debug(ctx, "home banner v2 not enabled", zap.String("actorId", actorId))
		return nil, epifierrors.ErrRecordNotFound
	}
	// check for segment condition
	segmentIds := o.dyconf.DynamicElementConfig().SegmentIds().ToStringArray()
	if len(segmentIds) == 0 {
		logger.Debug(ctx, "segment id is empty", zap.String("actorId", actorId))
		return nil, epifierrors.ErrRecordNotFound
	}
	segResp, err := o.segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: segmentIds,
	})
	if rpcErr := epifigrpc.RPCError(segResp, err); rpcErr != nil {
		return nil, rpcErr
	}

	if !isActorMemberOfAnySegment(segResp.GetSegmentMembershipMap(), segmentIds) {
		return nil, epifierrors.ErrRecordNotFound
	}

	// fetch dPanda external webpage url
	res, err := o.GetExternalVendorDynamicWebpageUrl(ctx, &casperPb.GetExternalVendorDynamicWebpageUrlRequest{
		ActorId:    actorId,
		VendorName: casperPb.OfferVendor_DPANDA,
		TargetUrl:  o.dyconf.DynamicElementConfig().DpandaContentConfig().Deeplink(),
	})
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		return nil, rpcErr
	}
	externalUrl := res.GetWebpageUrl()

	webPageDeeplink := &deeplink.Deeplink{
		Screen: deeplink.Screen_WEB_PAGE,
		ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
			WebPageScreenOptions: &deeplink.WebpageScreenOptions{
				WebpageTitle:             o.dyconf.DynamicElementConfig().DpandaContentConfig().DeeplinkTitle(),
				WebpageUrl:               externalUrl,
				DisableHardwareBackPress: true,
			},
		},
	}
	if o.dyconf.DynamicElementConfig().FiStoreWidgetConfig().IsWebPageWithCardDetailsScreenEnabled() {
		appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
		if (appPlatform == commontypes.Platform_ANDROID && appVersion >= int(o.dyconf.DynamicElementConfig().FiStoreWidgetConfig().MinAndroidVersionForWebPageWithCardDetailsScreen())) ||
			(appPlatform == commontypes.Platform_IOS && appVersion >= int(o.dyconf.DynamicElementConfig().FiStoreWidgetConfig().MinIosVersionForWebPageWithCardDetailsScreen())) {
			webPageDeeplink, _, err = o.getWebPageWithCardDetailsScreen(ctx, actorId, externalUrl, "")
			if err != nil {
				logger.Error(ctx, "error while fetching card details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				return nil, err
			}
		}
	}
	dynamicElement := &dynamicElementsPb.DynamicElement{
		OwnerService:  typesPb.ServiceName_CASPER_SERVICE,
		UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
		Content: &dynamicElementsPb.ElementContent{
			Content: &dynamicElementsPb.ElementContent_BannerV2{
				BannerV2: &dynamicElementsPb.BannerElementContentV2{
					Title: &commontypes.Text{
						FontColor:    o.dyconf.DynamicElementConfig().DpandaContentConfig().FontColor(),
						DisplayValue: &commontypes.Text_PlainString{PlainString: accrual.ReplaceCoinWithPointIfApplicable(o.dyconf.DynamicElementConfig().DpandaContentConfig().Title())},
					},
					BackgroundColor: ui.GetBlockColor(o.dyconf.DynamicElementConfig().DpandaContentConfig().BackgroundColor()),
					Deeplink:        webPageDeeplink,
					Image: &commontypes.Image{
						ImageType: commontypes.ImageType_PNG,
						ImageUrl:  o.dyconf.DynamicElementConfig().DpandaContentConfig().Image(),
					},
				},
			},
		},
	}

	logger.Debug(ctx, "dynamic elements response", zap.String("dynamicElement", dynamicElement.String()))
	return []*dynamicElementsPb.DynamicElement{dynamicElement}, nil
}

// getPrimaryFeatureElement returns the primary feature element for the home screen.
func (o OfferCatalogService) getPrimaryFeatureElement(ctx context.Context, actorId string) ([]*dynamicElementsPb.DynamicElement, error) {
	// give priority to gift card store feature widget
	dynamicElement, err := o.getGiftCardStoreThreePointsFeatureWidget(ctx, actorId)
	switch {
	case err != nil:
		return nil, err
	case dynamicElement != nil:
		return []*dynamicElementsPb.DynamicElement{dynamicElement}, nil
	default:
		return nil, epifierrors.ErrRecordNotFound
	}
}

// getSecondaryFeatureElement returns the secondary feature element for the home screen.
func (o OfferCatalogService) getSecondaryFeatureElement(ctx context.Context, actorId string) ([]*dynamicElementsPb.DynamicElement, error) {
	// give priority to gift card store feature widget
	dynamicElement, err := o.getGiftCardStoreThreePointsFeatureWidget(ctx, actorId)
	switch {
	case err != nil:
		return nil, err
	case dynamicElement != nil:
		// todo: add carousel variant when design is ready
		return []*dynamicElementsPb.DynamicElement{dynamicElement}, nil
	default:
		return nil, epifierrors.ErrRecordNotFound
	}
}

// nolint: funlen
func (o OfferCatalogService) getFiStoreFourPointsFeatureWidget(ctx context.Context, actorId string) (*dynamicElementsPb.DynamicElement, error) {
	featureWidgetConfig := o.dyconf.DynamicElementConfig().FeatureWidgetConfig()

	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	if appPlatform == commontypes.Platform_ANDROID && appVersion < int(featureWidgetConfig.MinAndroidVersionForWebPageWithCardDetailsScreen()) ||
		appPlatform == commontypes.Platform_IOS && appVersion < int(featureWidgetConfig.MinIosVersionForWebPageWithCardDetailsScreen()) {
		return nil, epifierrors.ErrRecordNotFound
	}

	// check for already redeemed condition
	fiStoreRedemptions, err := o.externalVendorRedemptionsClient.GetFiStoreRedemptions(ctx, &evrPb.GetFiStoreRedemptionsRequest{
		ActorId:        actorId,
		Filters:        &evrPb.GetFiStoreRedemptionsRequest_Filters{Categories: []evrPb.Category{evrPb.Category_CATEGORY_ECOM}},
		PageCtxRequest: &rpc.PageContextRequest{PageSize: 2},
	})
	if rpcErr := epifigrpc.RPCError(fiStoreRedemptions, err); rpcErr != nil {
		return nil, fmt.Errorf("error while checking if user has already redeemed a fi store product, err: %w", rpcErr)
	}
	if len(fiStoreRedemptions.GetRedemptions()) > 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	fiStoreDeeplink, _, err := o.getWebPageWithCardDetailsScreen(ctx, actorId, "", fmt.Sprintf("vendor:DPANDA,target_url:%s", featureWidgetConfig.FiStoreTargetUrl()))
	if err != nil {
		logger.Error(ctx, "error while web page with card details deeplink", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}

	return &dynamicElementsPb.DynamicElement{
		OwnerService:  typesPb.ServiceName_CASPER_SERVICE,
		UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
		Content: &dynamicElementsPb.ElementContent{
			Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
				FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
					Title:       commontypes.GetTextFromStringFontColourFontStyle("Shop the Fi-Store", "#313234", commontypes.FontStyle_HEADLINE_M),
					BorderColor: homeFePb.GetHomeWidgetBorderColor(),
					Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_{
						TextVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard{
							TopSection: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_TopSection{
								VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/casper/feature-widget/fi-store-4points-top-section-banner.png"),
							},
							MiddleSection: &dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection{
								HighlightedPoints: []*dynamicElementsPb.FeatureWidgetWithFourPoints_TextVisualElementCard_MiddleSection_HighlightedPoint{
									{
										LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/casper/feature-widget/fi-store-highlighted-point-product.png"),
										PreText:  commontypes.GetTextFromStringFontColourFontStyle("Products", "#929599", commontypes.FontStyle_HEADLINE_S),
										Text:     commontypes.GetTextFromStringFontColourFontStyle("30,000+", "#313234", commontypes.FontStyle_NUMBER_M),
									},
									{
										LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/casper/feature-widget/fi-store-highlighted-point-apple.png"),
										PreText:  commontypes.GetTextFromStringFontColourFontStyle("Brands", "#929599", commontypes.FontStyle_HEADLINE_S),
										Text:     commontypes.GetTextFromStringFontColourFontStyle("100+", "#313234", commontypes.FontStyle_NUMBER_M),
									},
									{
										LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/casper/feature-widget/fi-store-highlighted-point-truck.png"),
										PreText:  commontypes.GetTextFromStringFontColourFontStyle("Delivery", "#929599", commontypes.FontStyle_HEADLINE_S),
										Text:     commontypes.GetTextFromStringFontColourFontStyle("Free", "#313234", commontypes.FontStyle_NUMBER_M),
									},
									{
										LeftIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/casper/feature-widget/fi-store-highlighted-point-return.png"),
										PreText:  commontypes.GetTextFromStringFontColourFontStyle("Returns & Refunds", "#929599", commontypes.FontStyle_HEADLINE_S),
										Text:     commontypes.GetTextFromStringFontColourFontStyle("Easy", "#313234", commontypes.FontStyle_NUMBER_M),
									},
								},
							},
							Cta: &ui.IconTextComponent{
								Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Shop now >", "#4F71AB", commontypes.FontStyle_BUTTON_S)},
								Deeplink:            fiStoreDeeplink,
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#E7EBF2"},
							},
						},
					},
				},
			},
		},
		BizAnalyticsData: map[string]string{
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Fi Store",
		},
	}, nil
}

func (o OfferCatalogService) getGiftCardStoreFourPointsFeatureWidget(ctx context.Context, actorId string) (*dynamicElementsPb.DynamicElement, error) {
	featureWidgetConfig := o.dyconf.DynamicElementConfig().FeatureWidgetConfig()

	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	if appPlatform == commontypes.Platform_ANDROID && appVersion < int(featureWidgetConfig.MinAndroidVersionForWebPageWithCardDetailsScreen()) ||
		appPlatform == commontypes.Platform_IOS && appVersion < int(featureWidgetConfig.MinIosVersionForWebPageWithCardDetailsScreen()) {
		return nil, epifierrors.ErrRecordNotFound
	}

	// check for already redeemed condition
	fiStoreRedemptions, err := o.externalVendorRedemptionsClient.GetFiStoreRedemptions(ctx, &evrPb.GetFiStoreRedemptionsRequest{
		ActorId:        actorId,
		Filters:        &evrPb.GetFiStoreRedemptionsRequest_Filters{Categories: []evrPb.Category{evrPb.Category_CATEGORY_GIFT_CARDS}},
		PageCtxRequest: &rpc.PageContextRequest{PageSize: 2},
	})
	if rpcErr := epifigrpc.RPCError(fiStoreRedemptions, err); rpcErr != nil {
		return nil, fmt.Errorf("error while checking if user has already redeemed a gift card, err: %w", rpcErr)
	}
	if len(fiStoreRedemptions.GetRedemptions()) > 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	gcStoreDeeplink, _, err := o.getWebPageWithCardDetailsScreen(ctx, actorId, "", fmt.Sprintf("vendor:POSHVINE,target_url:%s", featureWidgetConfig.GiftCardStoreTargetUrl()))
	if err != nil {
		logger.Error(ctx, "error while web page with card details deeplink", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}

	return &dynamicElementsPb.DynamicElement{
		OwnerService:  typesPb.ServiceName_CASPER_SERVICE,
		UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
		Content: &dynamicElementsPb.ElementContent{
			Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
				FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
					Title:       commontypes.GetTextFromStringFontColourFontStyle("Shop the Fi-Store", "#313234", commontypes.FontStyle_HEADLINE_M),
					BorderColor: homeFePb.GetHomeWidgetBorderColor(),
					Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard_{
						FullVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard{
							VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/casper/feature-widget/gc-store-4points-full-visual-90-percent-store.png"),
							Cta: &ui.IconTextComponent{
								Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Explore Now >", "#FFFFFF", commontypes.FontStyle_BUTTON_S)},
								Deeplink:            gcStoreDeeplink,
								ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#313234"},
							},
						},
					},
				},
			},
		},
		BizAnalyticsData: map[string]string{
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Gift Card Store",
		},
	}, nil
}

// nolint: funlen
func (o OfferCatalogService) getGiftCardStoreThreePointsFeatureWidget(ctx context.Context, actorId string) (*dynamicElementsPb.DynamicElement, error) {
	featureWidgetConfig := o.dyconf.DynamicElementConfig().FeatureWidgetConfig()

	appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	if appPlatform == commontypes.Platform_ANDROID && appVersion < int(featureWidgetConfig.MinAndroidVersionForWebPageWithCardDetailsScreen()) ||
		appPlatform == commontypes.Platform_IOS && appVersion < int(featureWidgetConfig.MinIosVersionForWebPageWithCardDetailsScreen()) {
		return nil, epifierrors.ErrRecordNotFound
	}

	// check for already redeemed condition
	fiStoreRedemptions, err := o.externalVendorRedemptionsClient.GetFiStoreRedemptions(ctx, &evrPb.GetFiStoreRedemptionsRequest{
		ActorId:        actorId,
		Filters:        &evrPb.GetFiStoreRedemptionsRequest_Filters{Categories: []evrPb.Category{evrPb.Category_CATEGORY_GIFT_CARDS}},
		PageCtxRequest: &rpc.PageContextRequest{PageSize: 2},
	})
	if rpcErr := epifigrpc.RPCError(fiStoreRedemptions, err); rpcErr != nil {
		return nil, fmt.Errorf("error while checking if user has already redeemed a gift card, err: %w", rpcErr)
	}
	if len(fiStoreRedemptions.GetRedemptions()) > 0 {
		return nil, epifierrors.ErrRecordNotFound
	}

	gcStoreDeeplink, debitCardId, err := o.getWebPageWithCardDetailsScreen(ctx, actorId, "", fmt.Sprintf("vendor:POSHVINE,target_url:%s", featureWidgetConfig.GiftCardStoreTargetUrl()))
	if err != nil {
		logger.Error(ctx, "error while web page with card details deeplink", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return nil, err
	}

	return &dynamicElementsPb.DynamicElement{
		OwnerService:  typesPb.ServiceName_CASPER_SERVICE,
		UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS,
		Content: &dynamicElementsPb.ElementContent{
			Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithThreePoints{
				FeatureWidgetWithThreePoints: &dynamicElementsPb.FeatureWidgetWithThreePoints{
					Title:       commontypes.GetTextFromStringFontColourFontStyle("Shop the Fi-Store", "#313234", commontypes.FontStyle_HEADLINE_M),
					BorderColor: homeFePb.GetHomeWidgetBorderColor(),
					LeftVerticalFlyer: &dynamicElementsPb.FeatureWidgetWithThreePoints_LeftVerticalFlyer{
						VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/casper/feature-widget/gc-store-3points-left-vertical-flyer-new.png"),
						Cta: &ui.IconTextComponent{
							Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Check it out >", "#6294A6", commontypes.FontStyle_BUTTON_S)},
							Deeplink:            gcStoreDeeplink,
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#E4F1F5"},
						},
					},
					RightHorizontalFlyers: []*dynamicElementsPb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
						{
							PreText:   commontypes.GetTextFromStringFontColourFontStyle("50% off on Giftcard", "#929599", commontypes.FontStyle_HEADLINE_S),
							Text:      commontypes.GetTextFromStringFontColourFontStyle("Amazon", "#313234", commontypes.FontStyle_NUMBER_M),
							RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/casper/feature-widget/gc-store-3points-right-flyer-amazon.png"),
							BgColour:  ui.GetBlockColor("#FFFFFF"),
							Deeplink:  o.getWebPageWithCardDetailsScreenGivenDebitCardDetails("", "vendor:POSHVINE,target_url:fimoney.poshvine.com/gvms?merchantId=815ef837-2485-4eba-bc82-bd7f15423ead&merchantName=Amazon%20Pay%20E-Gift%20Card&isBenefitOffer=false&offerId=&isUnlockedBenefitApplicable=false", debitCardId),
						},
						{
							PreText:   commontypes.GetTextFromStringFontColourFontStyle("50% off on Giftcard", "#929599", commontypes.FontStyle_HEADLINE_S),
							Text:      commontypes.GetTextFromStringFontColourFontStyle("Myntra", "#313234", commontypes.FontStyle_NUMBER_M),
							RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/casper/feature-widget/gc-store-3points-right-flyer-myntra.png"),
							BgColour:  ui.GetBlockColor("#FFFFFF"),
							Deeplink:  o.getWebPageWithCardDetailsScreenGivenDebitCardDetails("", "vendor:POSHVINE,target_url:fimoney.poshvine.com/gvms?merchantId=036524c4-f92b-4594-9240-8474f6a4ef9a&merchantName=Myntra%20E%20Gift%20Card&isBenefitOffer=false&offerId=&isUnlockedBenefitApplicable=false", debitCardId),
						},
						{
							PreText:   commontypes.GetTextFromStringFontColourFontStyle("50% off on Giftcard", "#929599", commontypes.FontStyle_HEADLINE_S),
							Text:      commontypes.GetTextFromStringFontColourFontStyle("MakeMyTrip", "#313234", commontypes.FontStyle_NUMBER_M),
							RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/casper/feature-widget/gc-store-3points-right-flyer-makemytrip.png"),
							BgColour:  ui.GetBlockColor("#FFFFFF"),
							Deeplink:  o.getWebPageWithCardDetailsScreenGivenDebitCardDetails("", "vendor:POSHVINE,target_url:fimoney.poshvine.com/gvms?merchantId=1ac8c1ab-918f-4543-99dd-fcc04767620f&merchantName=MakeMyTrip%20&isBenefitOffer=false&isUnlockedBenefitApplicable=false", debitCardId),
						},
					},
				},
			},
		},
		BizAnalyticsData: map[string]string{
			dynamicElementsPb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Gift Card Store",
		},
	}, nil
}

func (o OfferCatalogService) isActorEligibleForFeatureWidget(ctx context.Context, actorId string) bool {
	segmentExp := o.dyconf.DynamicElementConfig().FeatureWidgetConfig().SegmentExpression()
	segmentResp, err := o.segmentClient.IsMemberOfExpressions(ctx, &segmentPb.IsMemberOfExpressionsRequest{
		ActorId:              actorId,
		SegmentIdExpressions: []string{segmentExp},
		LatestBy:             timestampPb.Now(),
	})
	if rpcErr := epifigrpc.RPCError(segmentResp, err); rpcErr != nil {
		logger.Error(ctx, "IsMemberOfExpressions call failed", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return false
	}

	return isActorMemberOfSegmentExpr(ctx, segmentResp.GetSegmentExpressionMembershipMap(), segmentExp)
}

func isActorMemberOfSegmentExpr(ctx context.Context, membershipMap map[string]*segmentPb.SegmentExpressionMembership, segmentExpr string) bool {
	segmentExprMembership, ok := membershipMap[segmentExpr]
	if !ok {
		logger.Error(ctx, "segment expression not found in response membership map", zap.String(logger.SEGMENT_ID, segmentExpr))
		return false
	}
	if segmentExprMembership.GetSegmentExpressionStatus() != segmentPb.SegmentExpressionStatus_OK {
		logger.Error(ctx, "error while checking if actor still belongs to this segment expression", zap.String(logger.SEGMENT_ID, segmentExpr))
		return false
	}
	return segmentExprMembership.GetIsActorMember()
}

// nolint: funlen, dupl, goconst
func (o OfferCatalogService) getScrollableBannerElement(ctx context.Context, actorId string) ([]*dynamicElementsPb.DynamicElement, error) {
	// Fi store home widget
	if !o.dyconf.DynamicElementConfig().FiStoreWidgetConfig().IsEnabled() {
		return nil, epifierrors.ErrRecordNotFound
	}

	fiStore := o.dyconf.DynamicElementConfig().FiStoreWidgetConfig()

	// check for segment condition
	segmentIds := fiStore.SegmentIds().ToStringArray()
	if len(segmentIds) == 0 {
		logger.Debug(ctx, "segment ids are empty", zap.String("actorId", actorId))
		return nil, epifierrors.ErrRecordNotFound
	}
	segResp, err := o.segmentClient.IsMember(ctx, &segmentPb.IsMemberRequest{
		ActorId:    actorId,
		SegmentIds: segmentIds,
	})
	if rpcErr := epifigrpc.RPCError(segResp, err); rpcErr != nil {
		return nil, rpcErr
	}
	if !isActorMemberOfAnySegment(segResp.GetSegmentMembershipMap(), segmentIds) {
		return nil, epifierrors.ErrRecordNotFound
	}
	// fetch be mapping value for given actor id
	vmResp, err := o.vendorMappingClient.GetBEMappingById(ctx, &vmPb.GetBEMappingByIdRequest{Id: actorId})
	if rpcErr := epifigrpc.RPCError(vmResp, err); rpcErr != nil {
		return nil, rpcErr
	}
	externalUrl := o.updateDPandaWebPageUrl(ctx, vmResp.GetDpandaId(), fiStore.RedirectionUrl())

	webPageDeeplink := &deeplink.Deeplink{
		Screen: deeplink.Screen_WEB_PAGE,
		ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
			WebPageScreenOptions: &deeplink.WebpageScreenOptions{
				WebpageTitle:             fiStore.RedirectionTitle(),
				WebpageUrl:               externalUrl,
				DisableHardwareBackPress: true,
			},
		},
	}
	var debitCardId string
	if o.dyconf.DynamicElementConfig().FiStoreWidgetConfig().IsWebPageWithCardDetailsScreenEnabled() {
		appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
		if (appPlatform == commontypes.Platform_ANDROID && appVersion >= int(o.dyconf.DynamicElementConfig().FiStoreWidgetConfig().MinAndroidVersionForWebPageWithCardDetailsScreen())) ||
			(appPlatform == commontypes.Platform_IOS && appVersion >= int(o.dyconf.DynamicElementConfig().FiStoreWidgetConfig().MinIosVersionForWebPageWithCardDetailsScreen())) {
			webPageDeeplink, debitCardId, err = o.getWebPageWithCardDetailsScreen(ctx, actorId, externalUrl, "")
			if err != nil {
				logger.Error(ctx, "error while fetching card details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
				return nil, err
			}
		}
	}

	dynamicElement := &dynamicElementsPb.DynamicElement{
		OwnerService:  typesPb.ServiceName_CASPER_SERVICE,
		UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE,
		Content: &dynamicElementsPb.ElementContent{
			Content: &dynamicElementsPb.ElementContent_ScrollableBanner{
				ScrollableBanner: &dynamicElementsPb.ScrollableBannerElementContent{
					Header: &dynamicElementsPb.BannerHeader{
						Title: []*commontypes.Text{
							{
								FontColor: fiStore.BannerHeader().FontColor(),
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: fiStore.BannerHeader().SubTitle(),
								},
								FontStyle:        &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
								FontColorOpacity: 60,
							},
							{
								FontColor: fiStore.BannerHeader().FontColor(),
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: fiStore.BannerHeader().Title(),
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_DISPLAY_XL},
							},
						},
						Cta: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								{
									FontColor: fiStore.BannerHeader().CtaFont(),
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: accrual.ReplaceCoinWithPointIfApplicable(fiStore.BannerHeader().CtaText()),
									},
									FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BODY_XS},
								},
							},
							RightIcon: &commontypes.Image{
								ImageUrl: fiStore.BannerHeader().CtaImageUrl(),
							},
							Deeplink: webPageDeeplink,
						},
					},
					ScrollingElements: []*dynamicElementsPb.BannerSingleShapeElement{
						{
							Shape: dynamicElementsPb.BannerSingleShapeElement_SHAPE_STAMP_4,
							Image: &commontypes.Image{
								ImageUrl: fiStore.Element1().ImageUrl(),
							},
							Title: &commontypes.Text{
								FontColor: fiStore.Element1().TitleFontColor(),
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: fiStore.Element1().TitleDisplayText(),
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: fiStore.Element1().BackgroundColor(),
								},
							},
							Shadow: []*widget.Shadow{
								{
									Height: fiStore.Element1().ShadowHeight(),
									Colour: widget.GetBlockBackgroundColour(fiStore.Element1().ShadowColor()),
								},
							},
							Deeplink: o.getWebPageWithScreenDetailsScreenForGivenUrl(ctx, vmResp.GetDpandaId(), debitCardId, fiStore.Element1().DeeplinkUrl()),
						},
						{
							Shape: dynamicElementsPb.BannerSingleShapeElement_SHAPE_STAMP_4,
							Image: &commontypes.Image{
								ImageUrl: fiStore.Element2().ImageUrl(),
							},
							Title: &commontypes.Text{
								FontColor: fiStore.Element2().TitleFontColor(),
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: fiStore.Element2().TitleDisplayText(),
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: fiStore.Element2().BackgroundColor(),
								},
							},
							Shadow: []*widget.Shadow{
								{
									Height: fiStore.Element2().ShadowHeight(),
									Colour: widget.GetBlockBackgroundColour(fiStore.Element2().ShadowColor()),
								},
							},
							Deeplink: o.getWebPageWithScreenDetailsScreenForGivenUrl(ctx, vmResp.GetDpandaId(), debitCardId, fiStore.Element2().DeeplinkUrl()),
						},
						{
							Shape: dynamicElementsPb.BannerSingleShapeElement_SHAPE_STAMP_4,
							Image: &commontypes.Image{
								ImageUrl: fiStore.Element3().ImageUrl(),
							},
							Title: &commontypes.Text{
								FontColor: fiStore.Element3().TitleFontColor(),
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: fiStore.Element3().TitleDisplayText(),
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: fiStore.Element3().BackgroundColor(),
								},
							},
							Shadow: []*widget.Shadow{
								{
									Height: fiStore.Element3().ShadowHeight(),
									Colour: widget.GetBlockBackgroundColour(fiStore.Element3().ShadowColor()),
								},
							},
							Deeplink: o.getWebPageWithScreenDetailsScreenForGivenUrl(ctx, vmResp.GetDpandaId(), debitCardId, fiStore.Element3().DeeplinkUrl()),
						},
						{
							Shape: dynamicElementsPb.BannerSingleShapeElement_SHAPE_STAMP_4,
							Image: &commontypes.Image{
								ImageUrl: fiStore.Element4().ImageUrl(),
							},
							Title: &commontypes.Text{
								FontColor: fiStore.Element4().TitleFontColor(),
								DisplayValue: &commontypes.Text_PlainString{
									PlainString: fiStore.Element4().TitleDisplayText(),
								},
								FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_XS},
							},
							BgColour: &widget.BackgroundColour{
								Colour: &widget.BackgroundColour_BlockColour{
									BlockColour: fiStore.Element4().BackgroundColor(),
								},
							},
							Shadow: []*widget.Shadow{
								{
									Height: fiStore.Element4().ShadowHeight(),
									Colour: widget.GetBlockBackgroundColour(fiStore.Element4().ShadowColor()),
								},
							},
							Deeplink: o.getWebPageWithScreenDetailsScreenForGivenUrl(ctx, vmResp.GetDpandaId(), debitCardId, fiStore.Element4().DeeplinkUrl()),
						},
					},
					BgColour: &widget.BackgroundColour{
						Colour: &widget.BackgroundColour_RadialGradient{
							RadialGradient: &widget.RadialGradient{
								Colours: fiStore.BackgroundColor().ToStringArray(),
							},
						},
					},
				},
			},
		},
	}

	return []*dynamicElementsPb.DynamicElement{dynamicElement}, nil
}

func (o OfferCatalogService) getWebPageWithScreenDetailsScreenForGivenUrl(ctx context.Context, dPandaId, debitCardId, url string) *deeplink.Deeplink {
	externalUrl := o.updateDPandaWebPageUrl(ctx, dPandaId, url)

	if o.dyconf.DynamicElementConfig().FiStoreWidgetConfig().IsWebPageWithCardDetailsScreenEnabled() {
		appPlatform, appVersion := epificontext.AppPlatformAndVersion(ctx)
		if (appPlatform == commontypes.Platform_ANDROID && appVersion >= int(o.dyconf.DynamicElementConfig().FiStoreWidgetConfig().MinAndroidVersionForWebPageWithCardDetailsScreen())) ||
			(appPlatform == commontypes.Platform_IOS && appVersion >= int(o.dyconf.DynamicElementConfig().FiStoreWidgetConfig().MinIosVersionForWebPageWithCardDetailsScreen())) {
			return &deeplink.Deeplink{
				Screen: deeplink.Screen_WEB_PAGE_WITH_CARD_DETAILS_SCREEN,
				ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&pkgScreenOptionsPb.WebPageWithCardDetailsScreenOptions{
					WebpageTitle:             o.dyconf.DynamicElementConfig().DpandaContentConfig().DeeplinkTitle(),
					WebpageUrl:               externalUrl,
					DisableHardwareBackPress: true,
					DisableDropDownAnimation: true,
					ShowUpiDetails:           true,
					ShowCreditCardDetails:    true,
					ShowDebitCardDetails:     debitCardId != "",
					DebitCardId:              debitCardId,
				}),
			}
		}
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_WEB_PAGE,
		ScreenOptions: &deeplink.Deeplink_WebPageScreenOptions{
			WebPageScreenOptions: &deeplink.WebpageScreenOptions{
				WebpageTitle:             o.dyconf.DynamicElementConfig().DpandaContentConfig().DeeplinkTitle(),
				WebpageUrl:               externalUrl,
				DisableHardwareBackPress: true,
			},
		},
	}
}

// only if actor present in any of the given segments, then we return true
func isActorMemberOfAnySegment(membership map[string]*segmentPb.SegmentMembership, segmentIds []string) bool {
	for _, segmentId := range segmentIds {
		if membership[segmentId].GetSegmentStatus() == segmentPb.SegmentStatus_SEGMENT_INSTANCE_FOUND && membership[segmentId].GetIsActorMember() {
			return true
		}
	}
	return false
}

func (o OfferCatalogService) DynamicElementCallback(ctx context.Context, request *dynamicElementsPb.DynamicElementCallbackRequest) (*dynamicElementsPb.DynamicElementCallbackResponse, error) {
	return &dynamicElementsPb.DynamicElementCallbackResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (o OfferCatalogService) getWebPageWithCardDetailsScreen(ctx context.Context, actorId string, externalUrl string, requestMetadata string) (*deeplink.Deeplink, string, error) {
	var (
		debitCardId          string
		showDebitCardDetails bool
	)
	// fetch debit card id to pass in deeplink to show debit card details in webpage.
	cards, cardFetchErr := o.cardClient.FetchCards(ctx, &provisioning.FetchCardsRequest{
		Actor:            &typesPb.Actor{Id: actorId},
		IssuingBanks:     []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
		CardStates:       []beCardPb.CardState{beCardPb.CardState_CREATED, beCardPb.CardState_ACTIVATED},
		CardTypes:        []beCardPb.CardType{beCardPb.CardType_DEBIT},
		CardNetworkTypes: []beCardPb.CardNetworkType{beCardPb.CardNetworkType_VISA},
		CardForms:        []beCardPb.CardForm{beCardPb.CardForm_PHYSICAL, beCardPb.CardForm_DIGITAL},
		SortedBy:         beCardPb.CardFieldMask_CARD_UPDATED_AT,
	})
	switch rpcErr := epifigrpc.RPCError(cards, cardFetchErr); {
	case rpc.StatusFromError(rpcErr).IsRecordNotFound():
		showDebitCardDetails = false
	case rpcErr != nil:
		logger.Error(ctx, "error while fetching debit card details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return nil, "", rpcErr
	case len(cards.GetCards()) == 0:
		showDebitCardDetails = false
	default:
		showDebitCardDetails = true
		debitCardId = cards.GetCards()[0].GetId()
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_WEB_PAGE_WITH_CARD_DETAILS_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&pkgScreenOptionsPb.WebPageWithCardDetailsScreenOptions{
			WebpageTitle:             o.dyconf.DynamicElementConfig().DpandaContentConfig().DeeplinkTitle(),
			WebpageUrl:               externalUrl,
			DisableHardwareBackPress: true,
			DisableDropDownAnimation: true,
			ShowUpiDetails:           true,
			ShowCreditCardDetails:    true,
			ShowDebitCardDetails:     showDebitCardDetails,
			DebitCardId:              debitCardId,
			RequestMetadata:          requestMetadata,
		}),
	}, debitCardId, nil
}

func (o OfferCatalogService) getWebPageWithCardDetailsScreenGivenDebitCardDetails(externalUrl string, requestMetadata string, debitCardId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_WEB_PAGE_WITH_CARD_DETAILS_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&pkgScreenOptionsPb.WebPageWithCardDetailsScreenOptions{
			WebpageTitle:             o.dyconf.DynamicElementConfig().DpandaContentConfig().DeeplinkTitle(),
			WebpageUrl:               externalUrl,
			DisableHardwareBackPress: true,
			DisableDropDownAnimation: true,
			ShowUpiDetails:           true,
			ShowCreditCardDetails:    true,
			ShowDebitCardDetails:     debitCardId != "",
			DebitCardId:              debitCardId,
			RequestMetadata:          requestMetadata,
		}),
	}
}

func (o OfferCatalogService) GetExternalVendorDynamicWebpageUrl(ctx context.Context, req *casperPb.GetExternalVendorDynamicWebpageUrlRequest) (*casperPb.GetExternalVendorDynamicWebpageUrlResponse, error) {
	if req.GetActorId() == "" {
		logger.Error(ctx, "actor id should not be empty")
		return &casperPb.GetExternalVendorDynamicWebpageUrlResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id should not be empty"),
		}, nil
	}
	var (
		webpageUrl string
		err        error
	)
	switch req.GetVendorName() {
	case casperPb.OfferVendor_POSHVINE:
		webpageUrl, err = o.getPoshVineWebPageUrl(ctx, req.GetActorId(), req.GetTargetUrl())
		if err != nil {
			logger.Error(ctx, "error while fetching poshVine's redirection url", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
			return &casperPb.GetExternalVendorDynamicWebpageUrlResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching poshVine's redirection url"),
			}, nil
		}
	case casperPb.OfferVendor_DPANDA:
		webpageUrl, err = o.getDPandaWebPageUrl(ctx, req.GetActorId(), req.GetTargetUrl())
		if err != nil {
			logger.Error(ctx, "error while fetching dPanda redirection url", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
			return &casperPb.GetExternalVendorDynamicWebpageUrlResponse{
				Status: rpc.StatusInternalWithDebugMsg("error while fetching dPanda redirection url"),
			}, nil
		}
	default:
		logger.Error(ctx, "unsupported vendor for external vendor dynamic url", zap.String("vendor", req.GetVendorName().String()))
		return &casperPb.GetExternalVendorDynamicWebpageUrlResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("unsupported vendor for external vendor dynamic url"),
		}, nil
	}
	logger.Info(ctx, "external vendor dynamic webpage url", zap.String(logger.VENDOR, req.GetVendorName().String()), zap.String("url", getMaskedString(req.GetVendorName(), webpageUrl)))
	return &casperPb.GetExternalVendorDynamicWebpageUrlResponse{
		Status:     rpc.StatusOk(),
		WebpageUrl: webpageUrl,
	}, nil
}

func (o OfferCatalogService) getDPandaWebPageUrl(ctx context.Context, actorId, targetUrl string) (string, error) {
	// fetch be mapping value for given actor id
	beMappingRes, err := o.vendorMappingClient.GetBEMappingById(ctx, &vmPb.GetBEMappingByIdRequest{Id: actorId})
	if rpcErr := epifigrpc.RPCError(beMappingRes, err); rpcErr != nil {
		return "", fmt.Errorf("error while fetching be mapping by actor id, err: %w", rpcErr)
	}

	return o.updateDPandaWebPageUrl(ctx, beMappingRes.GetDpandaId(), targetUrl), nil
}

func (o OfferCatalogService) updateDPandaWebPageUrl(ctx context.Context, dpandaId, targetUrl string) string {
	var externalUrl string

	// append "https://" if not present in url
	if !strings.HasPrefix(targetUrl, "https://") {
		targetUrl = "https://" + targetUrl
	}
	// check if target url already has any specific identifiers or query parameters
	switch {
	case strings.Contains(targetUrl, "user_identifier=%s"):
		externalUrl = fmt.Sprintf(targetUrl, dpandaId)
	case strings.Contains(targetUrl, "?"):
		externalUrl = fmt.Sprintf("%s&user_id=%s", targetUrl, dpandaId)
	default:
		externalUrl = fmt.Sprintf("%s?user_id=%s", targetUrl, dpandaId)
	}

	// check and add upi intent flag param
	platform, appVersion := epificontext.AppPlatformAndVersion(ctx)
	if platform == commontypes.Platform_ANDROID && appVersion >= int(o.dyconf.DPandaVendorConfig().MinAndroidVersionSupportingUpiIntent()) ||
		platform == commontypes.Platform_IOS && appVersion >= int(o.dyconf.DPandaVendorConfig().MinIosVersionSupportingUpiIntent()) {
		externalUrl += "&upi_intent_enabled=true"
	}

	return externalUrl
}

func (o OfferCatalogService) getPoshVineWebPageUrl(ctx context.Context, actorId, targetUrl string) (string, error) {
	// fetch be mapping value for given actor id
	beMappingRes, err := o.vendorMappingClient.GetBEMappingById(ctx, &vmPb.GetBEMappingByIdRequest{Id: actorId})
	if rpcErr := epifigrpc.RPCError(beMappingRes, err); rpcErr != nil {
		return "", fmt.Errorf("error while fetching be mapping by actor id, err: %w", rpcErr)
	}
	poshVineId := beMappingRes.GetPoshvineId()

	// get user details
	userResp, err := o.usersClient.GetUser(ctx, &beUserPb.GetUserRequest{
		Identifier: &beUserPb.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		return "", rpcErr
	}

	// get all eligible card attributes for given actor.
	cardAttributes, err := o.getPoshVineCardAttributes(ctx, actorId, poshVineId, userResp)
	if err != nil {
		return "", err
	}

	var defaultCardAttribute *poshvineVgPb.CardAttributes
	if len(cardAttributes) != 0 {
		// take first element from card attribute as default card attribute to pass in user registration flow.
		defaultCardAttribute = cardAttributes[0]
	}

	resp, err := o.poshvineVgClient.UserRegistration(ctx, &poshvineVgPb.UserRegistrationRequest{
		Header:         &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_POSHVINE},
		VendorUserId:   poshVineId,
		Name:           gammanames.BestNameFromProfile(ctx, userResp.GetUser().GetProfile()),
		PhoneNumber:    userResp.GetUser().GetProfile().GetPhoneNumber(),
		Email:          userResp.GetUser().GetProfile().GetEmail(),
		CardAttributes: defaultCardAttribute,
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		logger.Error(ctx, "received unexpected response from UpsertCustomer", zap.Error(rpcErr))
		return "", rpcErr
	}
	// Currently user registration API only supports one card to be pushed.
	// To Add multiple cards, we need to call Update API to add them.
	rpcErr := o.updatePoshVineUserDetails(ctx, poshVineId, userResp.GetUser(), cardAttributes)
	if rpcErr != nil {
		logger.Error(ctx, "error while updating user poshVine details", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		// no need to return error as this is to update user card details.
	}
	if !strings.HasPrefix(targetUrl, "https://") {
		targetUrl = "https://" + targetUrl
	}
	url := resp.GetRedirectionUrl() + "&targeturl=" + targetUrl
	logger.Debug(ctx, "redirection url for poshVine", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("targetUrl", url))
	return url, nil
}

// getPoshVineCardAttributes returns all user eligible card attributes.
func (o OfferCatalogService) getPoshVineCardAttributes(ctx context.Context, actorId, poshVineId string, userResp *beUserPb.GetUserResponse) ([]*poshvineVgPb.CardAttributes, error) {
	featureDetailsResp, err := o.onboardingClient.GetFeatureDetails(ctx, &onbPb.GetFeatureDetailsRequest{
		ActorId: actorId,
		Feature: onbPb.Feature_FEATURE_CC,
	})
	if rpcErr := epifigrpc.RPCError(featureDetailsResp, err); rpcErr != nil {
		return nil, rpcErr
	}

	var cardAttributes []*poshvineVgPb.CardAttributes
	if !featureDetailsResp.GetIsFiLiteUser() {
		cardAttributes = append(cardAttributes, getCardAttributeSegment(poshVineId, poshvineVgPb.CardSegment_DEBIT_CARD), getCardAttributeSegment(poshVineId, poshvineVgPb.CardSegment_UPI))
	}
	// get all user's CC card segments.
	cardSegments, err := o.getUserCreditCardSegments(ctx, actorId)
	if err != nil {
		return nil, err
	}
	for _, cardSegment := range cardSegments {
		cardAttributes = append(cardAttributes, getCardAttributeSegment(poshVineId, cardSegment))
	}

	userGroupsResp, err := o.userGroupClient.GetGroupsMappedToIdentifier(ctx, &userGroupPb.GetGroupsMappedToIdentifierRequest{
		IdentifierValue: &userGroupPb.IdentifierValue{
			Identifier: &userGroupPb.IdentifierValue_Email{
				Email: userResp.GetUser().GetProfile().GetEmail(),
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(userGroupsResp, err); rpcErr != nil {
		return nil, rpcErr
	}

	// TODO: remove this check once fi-lite card segment is enabled on poshVine end.
	if o.dyconf.IsFiLiteCardSegmentEnabledOnPoshVine() || commontypes.IsUserInGroup(userGroupsResp.GetGroups(), commontypes.UserGroup_INTERNAL) {
		// if user is fi-lite and has no card segments, then return FI_LITE card segment as default.
		if len(cardAttributes) == 0 && featureDetailsResp.GetIsFiLiteUser() {
			cardAttributes = append(cardAttributes, getCardAttributeSegment(poshVineId, poshvineVgPb.CardSegment_FI_LITE))
		}
	}

	return cardAttributes, nil
}

// getUserCreditCardSegment fetch user credit card details and return array of card segments.
func (o OfferCatalogService) getUserCreditCardSegments(ctx context.Context, actorId string) ([]poshvineVgPb.CardSegment, error) {
	creditCardRes, err := o.fireflyClient.GetCreditCard(ctx, &beFireflyPb.GetCreditCardRequest{
		GetBy:            &beFireflyPb.GetCreditCardRequest_ActorId{ActorId: actorId},
		SelectFieldMasks: []beFireflyEnums.CreditCardFieldMask{beFireflyEnums.CreditCardFieldMask_CREDIT_CARD_FIELD_MASK_STATE},
	})
	if rpcErr := epifigrpc.RPCError(creditCardRes, err); rpcErr != nil {
		if creditCardRes.GetStatus().IsRecordNotFound() {
			return nil, nil
		}
		return nil, fmt.Errorf("error fetching credit card status for the user, err : %w", rpcErr)
	}

	// get details for users who have an activated credit card and return array of card segments.
	if creditCardRes.GetCreditCard().GetCardState() == beFireflyEnums.CardState_CARD_STATE_ACTIVATED ||
		creditCardRes.GetCreditCard().GetCardState() == beFireflyEnums.CardState_CARD_STATE_DIGITALLY_ACTIVATED {
		ccAccountsRes, err := o.accountingClient.GetAccounts(ctx, &ffAccountsPb.GetAccountsRequest{
			GetBy: &ffAccountsPb.GetAccountsRequest_ActorId{ActorId: actorId},
		})
		if rpcErr := epifigrpc.RPCError(ccAccountsRes, err); rpcErr != nil {
			return nil, fmt.Errorf("error fetching credit card status for the user, : %w", rpcErr)
		}
		var cardSegments []poshvineVgPb.CardSegment
		for _, account := range ccAccountsRes.GetAccounts() {
			switch account.GetCardProgram().GetCardProgramType() {
			case types.CardProgramType_CARD_PROGRAM_TYPE_SECURED:
				cardSegments = append(cardSegments, poshvineVgPb.CardSegment_SIMPLIFI_CREDIT_CARD)
			case types.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED:
				cardSegments = append(cardSegments, poshvineVgPb.CardSegment_AMPLIFI_CREDIT_CARD)
			case types.CardProgramType_CARD_PROGRAM_TYPE_MASS_UNSECURED:
				cardSegments = append(cardSegments, poshvineVgPb.CardSegment_MAGNIFI_CREDIT_CARD)
			}
		}
		return cardSegments, nil
	}

	return nil, nil
}

// updatePoshVineUserDetails calls VG to update poshVine's card details.
func (o OfferCatalogService) updatePoshVineUserDetails(ctx context.Context, poshVineId string, user *beUserPb.User, cardAttributes []*poshvineVgPb.CardAttributes) error {
	// if no cards details are there to update, return nil.
	// we are ignoring the first card as, we already sent the first card in user registration API,
	// so we need to call update API only for other cards.
	if len(cardAttributes) <= 1 {
		return nil
	}

	resp, err := o.poshvineVgClient.UpdateUserDetails(ctx, &poshvineVgPb.UpdateUserDetailsRequest{
		Header:         &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_POSHVINE},
		VendorUserId:   poshVineId,
		Name:           gammanames.BestNameFromProfile(ctx, user.GetProfile()),
		PhoneNumber:    user.GetProfile().GetPhoneNumber(),
		Email:          user.GetProfile().GetEmail(),
		CardAttributes: cardAttributes[1:],
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		return rpcErr
	}

	return nil
}

// getCardAttributeSegment get card attributes details for given segment.
func getCardAttributeSegment(vendorUserId string, segment poshvineVgPb.CardSegment) *poshvineVgPb.CardAttributes {
	if segment == poshvineVgPb.CardSegment_CARD_SEGMENT_UNSPECIFIED {
		return nil
	}

	cardAttributes := &poshvineVgPb.CardAttributes{
		BinType:          "first8",
		UniqueExternalId: segment.String() + ":" + vendorUserId,
	}
	switch segment {
	case poshvineVgPb.CardSegment_UPI:
		cardAttributes.Bin = "12345678"
		cardAttributes.Segment = poshvineVgPb.CardSegment_UPI
	case poshvineVgPb.CardSegment_DEBIT_CARD:
		cardAttributes.Bin = "418730XX"
		cardAttributes.Segment = poshvineVgPb.CardSegment_DEBIT_CARD
	case poshvineVgPb.CardSegment_AMPLIFI_CREDIT_CARD:
		cardAttributes.Bin = "46673000"
		cardAttributes.Segment = poshvineVgPb.CardSegment_AMPLIFI_CREDIT_CARD
	case poshvineVgPb.CardSegment_SIMPLIFI_CREDIT_CARD:
		cardAttributes.Bin = "40339900"
		cardAttributes.Segment = poshvineVgPb.CardSegment_SIMPLIFI_CREDIT_CARD
	case poshvineVgPb.CardSegment_MAGNIFI_CREDIT_CARD:
		cardAttributes.Bin = "46673002"
		cardAttributes.Segment = poshvineVgPb.CardSegment_MAGNIFI_CREDIT_CARD
	case poshvineVgPb.CardSegment_FI_LITE:
		cardAttributes.Bin = "00000000"
		cardAttributes.Segment = poshvineVgPb.CardSegment_FI_LITE
	default:
		return nil
	}
	return cardAttributes
}

func getMaskedString(vendor casperPb.OfferVendor, webpageUrl string) string {
	switch vendor {
	case casperPb.OfferVendor_DPANDA:
		re := regexp.MustCompile(`user_id=([0-9a-fA-F-]+)`)
		maskedUserID := "user_id=XXXX"
		return re.ReplaceAllString(webpageUrl, maskedUserID)
	case casperPb.OfferVendor_POSHVINE:
		re := regexp.MustCompile(`data=([^&]+)`)
		maskedUserID := "data=XXXX"
		return re.ReplaceAllString(webpageUrl, maskedUserID)
	default:
		return webpageUrl
	}
}
