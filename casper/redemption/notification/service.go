package notification

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/wire"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/deeplink"
	fcmPb "github.com/epifi/gamma/api/frontend/fcm"
	"github.com/epifi/gamma/pkg/accrual"

	casperPb "github.com/epifi/gamma/api/casper"

	actorPb "github.com/epifi/gamma/api/actor"
	redemptionPb "github.com/epifi/gamma/api/casper/redemption"
	commsPb "github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/casper/config"
)

var WireSet = wire.NewSet(NewService, wire.Bind(new(INotificationService), new(*Service)))

type INotificationService interface {
	SendSuccessfulRedemptionNotification(ctx context.Context, redeemedOffer *redemptionPb.RedeemedOffer, catalogOffer *casperPb.Offer) error
	SendFailedRedemptionNotification(ctx context.Context, redeemedOffer *redemptionPb.RedeemedOffer, catalogOffer *casperPb.Offer) error
}

type Service struct {
	commsClient        commsPb.CommsClient
	actorClient        actorPb.ActorClient
	notificationParams *config.RedemptionNotificationParams
}

func NewService(commsClient commsPb.CommsClient, actorClient actorPb.ActorClient, notificationParams *config.RedemptionNotificationParams) *Service {
	return &Service{commsClient: commsClient, actorClient: actorClient, notificationParams: notificationParams}
}

func (s *Service) SendSuccessfulRedemptionNotification(ctx context.Context, redeemedOffer *redemptionPb.RedeemedOffer, catalogOffer *casperPb.Offer) error {
	actorEntityId, err := s.getActorEntityId(ctx, redeemedOffer.GetActorId())
	if err != nil {
		return err
	}
	if sendMessageRes, err := s.commsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_BEST_EFFORT,
		Medium:         commsPb.Medium_NOTIFICATION,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{UserId: actorEntityId},
		Message: &commsPb.SendMessageRequest_Notification{
			Notification: &commsPb.NotificationMessage{
				Priority: commsPb.NotificationPriority_NORMAL,
				Notification: &fcmPb.Notification{
					NotificationType: fcmPb.NotificationType_SYSTEM_TRAY,
					NotificationTemplates: &fcmPb.Notification_SystemTrayTemplate{
						SystemTrayTemplate: &fcmPb.SystemTrayTemplate{
							CommonTemplateFields: &fcmPb.CommonTemplateFields{
								Title: s.notificationParams.SuccessfulRedemption.Title,
								Body:  fmt.Sprintf(s.notificationParams.SuccessfulRedemption.Body, catalogOffer.GetAdditionalDetails().AfterRedemptionOfferName),
								Deeplink: &deeplink.Deeplink{
									Screen: deeplink.Screen_REDEEMED_OFFERS_SCREEN,
								},
							},
						}},
				},
			}},
	}); err != nil || !sendMessageRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "commsClient.SendMessage call failed", zap.Any(logger.RPC_STATUS, sendMessageRes.GetStatus()), zap.Error(err))
		return errors.New("commsClient.SendMessage call failed")
	}

	return nil
}

func (s *Service) SendFailedRedemptionNotification(ctx context.Context, redeemedOffer *redemptionPb.RedeemedOffer, catalogOffer *casperPb.Offer) error {
	actorEntityId, err := s.getActorEntityId(ctx, redeemedOffer.GetActorId())
	if err != nil {
		return err
	}
	if sendMessageRes, err := s.commsClient.SendMessage(ctx, &commsPb.SendMessageRequest{
		Type:           commsPb.QoS_BEST_EFFORT,
		Medium:         commsPb.Medium_NOTIFICATION,
		UserIdentifier: &commsPb.SendMessageRequest_UserId{UserId: actorEntityId},
		Message: &commsPb.SendMessageRequest_Notification{
			Notification: &commsPb.NotificationMessage{
				Priority: commsPb.NotificationPriority_NORMAL,
				Notification: &fcmPb.Notification{
					NotificationType: fcmPb.NotificationType_SYSTEM_TRAY,
					NotificationTemplates: &fcmPb.Notification_SystemTrayTemplate{
						SystemTrayTemplate: &fcmPb.SystemTrayTemplate{
							CommonTemplateFields: &fcmPb.CommonTemplateFields{
								Title: s.notificationParams.FailedRedemption.Title,
								Body:  accrual.ReplaceCoinWithPointIfApplicable(s.notificationParams.FailedRedemption.Body),
								Deeplink: &deeplink.Deeplink{
									Screen: deeplink.Screen_REDEEMED_OFFERS_SCREEN,
								},
							},
						}},
				},
			}},
	}); err != nil || !sendMessageRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "commsClient.SendMessage call failed", zap.Any(logger.RPC_STATUS, sendMessageRes.GetStatus()), zap.Error(err))
		return errors.New("commsClient.SendMessage call failed")
	}

	return nil
}

func (s *Service) getActorEntityId(ctx context.Context, actorId string) (string, error) {
	actorRes, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{Id: actorId})
	if err != nil || !actorRes.GetStatus().IsSuccess() {
		logger.Error(ctx, "actorClient.GetActorById call failed", zap.Any(logger.RPC_STATUS, actorRes.GetStatus()), zap.Error(err))
		return "", errors.New("actorClient.GetActorById call failed")
	}
	return actorRes.GetActor().GetEntityId(), nil
}
