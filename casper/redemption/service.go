package redemption

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	casperPb "github.com/epifi/gamma/api/casper"
	"github.com/epifi/gamma/api/casper/redemption"
	vistaraVgPb "github.com/epifi/gamma/api/vendorgateway/offers/vistara"

	"github.com/epifi/gamma/casper/config/genconf"
	casperDao "github.com/epifi/gamma/casper/dao"
	"github.com/epifi/gamma/casper/offervendor"
	"github.com/epifi/gamma/casper/redemption/cryptor"
	"github.com/epifi/gamma/casper/redemption/dao"
	"github.com/epifi/gamma/casper/wire/types"
	accrualPkg "github.com/epifi/gamma/pkg/accrual"
)

// todo : add proto validations

type Service struct {
	offerCatalogService  casperPb.OfferCatalogServiceServer
	redeemedOfferDao     dao.IRedeemedOfferDao
	redemptionReqDao     dao.IRedemptionRequestDao
	redemptionProcessor  *RedemptionProcessor
	offerVendorFactory   offervendor.IVendorFactory
	redeemedOfferCryptor cryptor.RedeemedOfferCryptor
	publisher            types.RetryRedemptionEventsSqsPublisher
	vistaraClient        vistaraVgPb.VistaraClient
	offerCatalogDao      casperDao.OfferCatalogDao
	dyconf               *genconf.Config
}

func NewService(
	offerCatalogService casperPb.OfferCatalogServiceServer,
	redeemedOfferDao dao.IRedeemedOfferDao,
	redemptionReqDao dao.IRedemptionRequestDao,
	redemptionProcessor *RedemptionProcessor,
	offerVendorFactory offervendor.IVendorFactory,
	redeemedOfferCryptor cryptor.RedeemedOfferCryptor,
	publisher types.RetryRedemptionEventsSqsPublisher,
	vistaraClient vistaraVgPb.VistaraClient,
	offerCatalogDao casperDao.OfferCatalogDao,
	dyconf *genconf.Config,
) *Service {

	return &Service{
		offerCatalogService:  offerCatalogService,
		redeemedOfferDao:     redeemedOfferDao,
		redemptionReqDao:     redemptionReqDao,
		redemptionProcessor:  redemptionProcessor,
		offerVendorFactory:   offerVendorFactory,
		redeemedOfferCryptor: redeemedOfferCryptor,
		publisher:            publisher,
		vistaraClient:        vistaraClient,
		offerCatalogDao:      offerCatalogDao,
		dyconf:               dyconf,
	}
}

var _ redemption.OfferRedemptionServiceServer = &Service{}

// InitiateRedemption rpc is useful to initiate a new offer redemption.
func (s *Service) InitiateRedemption(ctx context.Context, req *redemption.InitiateRedemptionRequest) (*redemption.InitiateRedemptionResponse, error) {
	// check if fi coins to fi points migration is active
	if accrualPkg.IsFiCoinsToFiPointsMigrationInProgress() {
		return &redemption.InitiateRedemptionResponse{Status: rpc.StatusUnavailableWithDebugMsg(accrualPkg.ErrFiCoinsToFiPointsMigrationInProgress.Error())}, nil
	}

	// validate initiate redemption request
	err, failureReason := s.validateInitiateRedemptionRequest(ctx, req)
	if err != nil {
		logger.Error(ctx, "invalid initiate redemption request", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.OFFER_ID, req.GetOfferId()), zap.String("failureReason", failureReason.String()), zap.Error(err))

		switch {
		case errors.Is(err, epifierrors.ErrInvalidArgument):
			return &redemption.InitiateRedemptionResponse{Status: rpc.StatusInvalidArgumentWithDebugMsg(err.Error()), FailureReason: failureReason}, nil
		default:
			return &redemption.InitiateRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error()), FailureReason: failureReason}, nil
		}
	}
	// create redemption request entry in db
	redemptionReq, err := s.redemptionReqDao.Create(ctx, req)
	if err != nil {
		logger.Error(ctx, "error creating redemption request", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.OFFER_ID, req.GetOfferId()), zap.Error(err))
		return &redemption.InitiateRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	return &redemption.InitiateRedemptionResponse{Status: rpc.StatusOk(), RedemptionRequestId: redemptionReq.Id}, nil
}

// validateInitiateRedemptionRequest validates if InitiateRedemptionRequest is valid or not.
// returns error if request is not valid.
// nolint:funlen
func (s *Service) validateInitiateRedemptionRequest(ctx context.Context, req *redemption.InitiateRedemptionRequest) (error, redemption.InitiateRedemptionResponse_FailureReason) {
	// fetch the offer details
	offerDetailRes, err := s.offerCatalogService.GetBulkOfferDetailsByIds(ctx, &casperPb.GetBulkOfferDetailsByIdsRequest{OfferIds: []string{req.GetOfferId()}, WithUpdatedVendorOfferDetails: false})
	if err != nil || !offerDetailRes.GetStatus().IsSuccess() || len(offerDetailRes.GetOffers()) == 0 {
		logger.Error(ctx, "offerCatalogService.GetOfferDetailsById call failed", zap.Any(logger.RPC_STATUS, offerDetailRes.GetStatus()), zap.Error(err))
		return fmt.Errorf("offerCatalogService.GetOfferDetailsById call failed"), redemption.InitiateRedemptionResponse_FAILURE_REASON_UNSPECIFIED
	}
	offer := offerDetailRes.GetOffers()[0]

	// check if offer is redeemable
	if offer.GetOfferType() == casperPb.OfferType_EXTERNAL_VENDOR {
		return fmt.Errorf("offer redemption is not valid, err: %w", epifierrors.ErrInvalidArgument), redemption.InitiateRedemptionResponse_FAILURE_REASON_NON_REDEEMABLE_OFFER
	}

	// shipping address is mandatory for physical merchandise offer redemption
	if offer.GetOfferType() == casperPb.OfferType_PHYSICAL_MERCHANDISE {
		if req.GetRequestMetadata().GetShippingAddress() == nil {
			return fmt.Errorf("shipping address is mandatory for physical merchandise offer redemption, err: %w", epifierrors.ErrInvalidArgument), redemption.InitiateRedemptionResponse_FAILURE_REASON_UNSPECIFIED
		}
	}

	// check for mandatory fields for vistara air miles offer redemption
	if offer.GetOfferType() == casperPb.OfferType_VISTARA_AIR_MILES {
		if len(req.GetRequestMetadata().GetAdditionalInputs()) != 2 {
			return fmt.Errorf("exactly two additional input fields required for vistara air miles offer redemption, err: %w", epifierrors.ErrInvalidArgument), redemption.InitiateRedemptionResponse_FAILURE_REASON_UNSPECIFIED
		}

		var emailId, cvId string
		for _, additionalInput := range req.GetRequestMetadata().GetAdditionalInputs() {
			if additionalInput.GetId() == redemption.InputFieldIdentifier_INPUT_FIELD_IDENTIFIER_EMAIL_ID.String() {
				emailId = additionalInput.GetValue()
			}
			if additionalInput.GetId() == redemption.InputFieldIdentifier_INPUT_FIELD_IDENTIFIER_CLUB_VISTARA_ID.String() {
				cvId = additionalInput.GetValue()
			}
		}
		if emailId == "" || cvId == "" || req.GetRequestMetadata().GetFiCoins() == 0 {
			return fmt.Errorf("mandatory fields emailId, cvId or fiCoins is missing for club vistara offer redemption, err: %w", epifierrors.ErrInvalidArgument), redemption.InitiateRedemptionResponse_FAILURE_REASON_UNSPECIFIED
		}

		if isCvIdValid(ctx, cvId) {
			// validate club vistara membership
			validateVistaraMembershipRes, err := s.vistaraClient.ValidateClubVistaraMembership(ctx, &vistaraVgPb.ValidateClubVistaraMembershipRequest{
				Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_VISTARA},
				EmailId:       emailId,
				ClubVistaraId: cvId,
			})
			// failure cases
			if err != nil {
				return fmt.Errorf("error while validating club vistara membership, err: %w", err), redemption.InitiateRedemptionResponse_FAILURE_REASON_UNSPECIFIED
			}
			if !validateVistaraMembershipRes.GetStatus().IsSuccess() {
				if validateVistaraMembershipRes.GetStatus().IsInvalidArgument() {
					switch validateVistaraMembershipRes.GetValidationStatus() {
					case vistaraVgPb.ValidateClubVistaraMembershipResponse_VALIDATION_STATUS_INVALID_CV_ID:
						logger.Error(ctx, "invalid CV ID provided", zap.String("cvId", cvId))
						return fmt.Errorf("invalid CV ID provided, CV ID: %s, err: %w", cvId, epifierrors.ErrInvalidArgument), redemption.InitiateRedemptionResponse_FAILURE_REASON_INVALID_CV_ID
					case vistaraVgPb.ValidateClubVistaraMembershipResponse_VALIDATION_STATUS_VALIDATION_FAILED:
						logger.Error(ctx, "club vistara membership validation failed", zap.String("cvId", cvId), zap.String("emailId", emailId))
						return fmt.Errorf("club vistara membership validation failed, err: %w", epifierrors.ErrInvalidArgument), redemption.InitiateRedemptionResponse_FAILURE_REASON_CV_MEMBERSHIP_VALIDATION_FAILURE
					case vistaraVgPb.ValidateClubVistaraMembershipResponse_VALIDATION_STATUS_USER_DOES_NOT_EXIST:
						logger.Error(ctx, "user with given email id doesn't exist on Vistara", zap.String("emailId", emailId))
						return fmt.Errorf("user with given email id doesn't exist on Vistara, err: %w", epifierrors.ErrInvalidArgument), redemption.InitiateRedemptionResponse_FAILURE_REASON_VISTARA_USER_DOES_NOT_EXIST
					default:
						// do nothing
					}
				}
				// for ISE cases, we'll return with vendor API failure response
				return fmt.Errorf("error while validating club vistara membership, rpcStatus: %s", validateVistaraMembershipRes.GetStatus().String()), redemption.InitiateRedemptionResponse_FAILURE_REASON_VENDOR_API_FAILURE
			}

			// success case
			if validateVistaraMembershipRes.GetStatus().IsSuccess() {
				switch validateVistaraMembershipRes.GetValidationStatus() {
				case vistaraVgPb.ValidateClubVistaraMembershipResponse_VALIDATION_STATUS_VALIDATED_SUCCESSFULLY:
					return nil, redemption.InitiateRedemptionResponse_FAILURE_REASON_UNSPECIFIED
				default:
					// unhandled status code
					return fmt.Errorf("unhandled validation status code encountered while validating club vistara membership, validationStatus: %s", validateVistaraMembershipRes.GetValidationStatus().String()), redemption.InitiateRedemptionResponse_FAILURE_REASON_UNSPECIFIED
				}
			}
		} else {
			return fmt.Errorf("CV ID provided is not valid (mod 7 algo check failed), err: %w", epifierrors.ErrInvalidArgument), redemption.InitiateRedemptionResponse_FAILURE_REASON_INVALID_CV_ID
		}
	}

	// check for mandatory fields for club itc green points offer redemption
	// todo (himanshu) add failure reasons if required
	if offer.GetOfferType() == casperPb.OfferType_CLUB_ITC_GREEN_POINTS {
		if len(req.GetRequestMetadata().GetAdditionalInputs()) != 2 {
			return fmt.Errorf("exactly two additional input fields required for club itc green points offer redemption, err: %w", epifierrors.ErrInvalidArgument), redemption.InitiateRedemptionResponse_FAILURE_REASON_UNSPECIFIED
		}

		var userName, clubItcId string
		for _, additionalInput := range req.GetRequestMetadata().GetAdditionalInputs() {
			if additionalInput.GetId() == redemption.InputFieldIdentifier_INPUT_FIELD_IDENTIFIER_USER_NAME.String() {
				userName = additionalInput.GetValue()
			}
			if additionalInput.GetId() == redemption.InputFieldIdentifier_INPUT_FIELD_IDENTIFIER_CLUB_ITC_ID.String() {
				clubItcId = additionalInput.GetValue()
			}
		}
		if userName == "" || clubItcId == "" || req.GetRequestMetadata().GetFiCoins() == 0 {
			return fmt.Errorf("mandatory fields userName, clubItcId or fiCoins is missing for club itc green points offer redemption, err: %w", epifierrors.ErrInvalidArgument), redemption.InitiateRedemptionResponse_FAILURE_REASON_UNSPECIFIED
		}
	}

	return nil, redemption.InitiateRedemptionResponse_FAILURE_REASON_UNSPECIFIED
}

// isCvIdValid checks whether the given cvId is valid or not. The algo provided
// by Vistara for the same is to divide the first 8 digits of the 9 digit CV ID
// by 7, and compare the remainder with the last digit of the CV ID. If they
// match, the given CV ID is valid, otherwise it isn't.
func isCvIdValid(ctx context.Context, cvIdString string) bool {
	if len(cvIdString) != 9 {
		return false
	}

	cvid, err := strconv.ParseInt(cvIdString, 10, 64)
	if err != nil {
		logger.Error(ctx, "error while parsing cvId to integer", zap.String("cvId", cvIdString))
		return false
	}
	checkDigit := cvid % 10
	serialNumber := cvid / 10

	return serialNumber%7 == checkDigit
}

// ConfirmRedemption rpc is useful to confirm an already initiated offer redemption.
func (s *Service) ConfirmRedemption(ctx context.Context, req *redemption.ConfirmRedemptionRequest) (*redemption.ConfirmRedemptionResponse, error) {
	// get redemption request entry
	redemptionReq, err := s.redemptionReqDao.GetById(ctx, req.GetRedemptionRequestId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "redemption request not found", zap.String(logger.REDEMPTION_REQUEST_ID, req.GetRedemptionRequestId()), zap.Error(err))
		return &redemption.ConfirmRedemptionResponse{Status: rpc.StatusRecordNotFoundWithDebugMsg("redemption request not found")}, nil
	case err != nil:
		logger.Error(ctx, "error fetching redemption request", zap.String(logger.REDEMPTION_REQUEST_ID, req.GetRedemptionRequestId()), zap.Error(err))
		return &redemption.ConfirmRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching redemption request : " + err.Error())}, nil
	}

	// todo (utkarsh) : use normal queue
	// push to queue for async processing
	redemptionEvent := &redemption.ProcessRetryRedemptionEventRequest{RedemptionRequestId: req.GetRedemptionRequestId()}
	messageId, err := s.publisher.PublishWithDelay(ctx, redemptionEvent, time.Second)
	if err != nil {
		logger.Error(ctx, "failed pushing redemption event to queue", zap.String(logger.REDEMPTION_REQUEST_ID, req.RedemptionRequestId), zap.Error(err))
		return &redemption.ConfirmRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg("failed pushing redemption event to queue")}, nil
	}
	logger.Info(ctx, "successfully pushed retry event to queue", zap.String(logger.QUEUE_MESSAGE_ID, messageId), zap.String(logger.REDEMPTION_REQUEST_ID, req.RedemptionRequestId))

	var updatedRedeemedOffer *redemption.RedeemedOffer
	// wait for sometime for redemption to complete
	pollingTimeDutationInSecs := uint32(15)
	if req.GetPollingTimeDurationInSecs() != 0 {
		pollingTimeDutationInSecs = req.GetPollingTimeDurationInSecs()
	}

	// todo: remove polling from this RPC and expose another RPC for fetching status of redemption that should be used by clients
	for i := uint32(0); i < pollingTimeDutationInSecs; i++ {
		// fetch updated redeemed offer
		updatedRedeemedOffer, err = s.redeemedOfferDao.GetByRequestId(ctx, redemptionReq.Id)
		if err != nil {
			logger.Error(ctx, "error fetching redeemed offer by redemption request id", zap.String(logger.REDEMPTION_REQUEST_ID, req.GetRedemptionRequestId()), zap.Error(err))
			return &redemption.ConfirmRedemptionResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offer by redemption request id : " + err.Error())}, nil
		}
		if s.redemptionProcessor.IsTerminalState(updatedRedeemedOffer.GetRedemptionState()) {
			logger.Info(ctx, "redemption reached terminal state", zap.String(logger.REDEMPTION_REQUEST_ID, req.RedemptionRequestId))
			return &redemption.ConfirmRedemptionResponse{Status: rpc.StatusOk(), RedeemedOffer: updatedRedeemedOffer}, nil
		}
		if s.redemptionProcessor.IsHaltedState(updatedRedeemedOffer.GetRedemptionState()) {
			logger.Info(ctx, "redemption reached halted state", zap.String(logger.REDEMPTION_REQUEST_ID, req.RedemptionRequestId))
			return &redemption.ConfirmRedemptionResponse{Status: rpc.StatusOk(), RedeemedOffer: updatedRedeemedOffer}, nil
		}
		<-time.After(1 * time.Second)
	}
	return &redemption.ConfirmRedemptionResponse{Status: rpc.StatusOk(), RedeemedOffer: updatedRedeemedOffer}, nil
}

// GetOfferRedemptionState rpc is useful for fetching the current state of a given redeemed offer.
func (s *Service) GetOfferRedemptionState(ctx context.Context, req *redemption.GetOfferRedemptionStateRequest) (*redemption.GetOfferRedemptionStateResponse, error) {
	redeemedOffer, err := s.redeemedOfferDao.GetByRequestId(ctx, req.GetRedemptionRequestId())
	if err != nil {
		logger.Error(ctx, "error fetching redeemed offer", zap.String(logger.REDEMPTION_REQUEST_ID, req.GetRedemptionRequestId()), zap.Error(err))
		return &redemption.GetOfferRedemptionStateResponse{Status: rpc.StatusInternalWithDebugMsg("error fetching redeemed offer : " + err.Error())}, nil
	}
	if redeemedOffer == nil {
		logger.Error(ctx, "redeemed offer not found", zap.String(logger.REDEMPTION_REQUEST_ID, req.GetRedemptionRequestId()), zap.Error(err))
		return &redemption.GetOfferRedemptionStateResponse{Status: rpc.StatusRecordNotFound()}, nil
	}
	return &redemption.GetOfferRedemptionStateResponse{
		Status:          rpc.StatusOk(),
		RedemptionState: redeemedOffer.GetRedemptionState(),
	}, nil
}

// GetRedeemedOffersForActor rpc is useful to fetch redeemed offers for actor with filters.
func (s *Service) GetRedeemedOffersForActor(ctx context.Context, request *redemption.GetRedeemedOffersForActorRequest) (*redemption.GetRedeemedOffersForActorResponse, error) {
	redeemedOffersList, pageCtxRes, err := s.redeemedOfferDao.GetByActorIdAndFilters(ctx, request)
	if err != nil {
		logger.Error(ctx, "error fetching redeemed offers by actor id and filters", zap.String(logger.ACTOR_ID, request.GetActorId()), zap.Error(err))
		return &redemption.GetRedeemedOffersForActorResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &redemption.GetRedeemedOffersForActorResponse{
		Status:         rpc.StatusOk(),
		RedeemedOffers: redeemedOffersList,
		PageContext:    pageCtxRes,
	}, nil
}

// GetRedeemedOfferById rpc is useful for fetching a redeemed offer by id.
func (s *Service) GetRedeemedOfferById(ctx context.Context, request *redemption.GetRedeemedOfferByIdRequest) (*redemption.GetRedeemedOfferByIdResponse, error) {
	redeemedOffer, err := s.redeemedOfferDao.GetById(ctx, request.GetRedeemedOfferId())
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "redeemed offer not found", zap.String(logger.REDEEMED_OFFER_ID, request.GetRedeemedOfferId()), zap.Error(err))
		return &redemption.GetRedeemedOfferByIdResponse{Status: rpc.StatusRecordNotFoundWithDebugMsg(err.Error())}, nil
	case err != nil:
		logger.Error(ctx, "error fetching redeemed offer by id", zap.String(logger.REDEEMED_OFFER_ID, request.GetRedeemedOfferId()), zap.Error(err))
		return &redemption.GetRedeemedOfferByIdResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &redemption.GetRedeemedOfferByIdResponse{
		Status:        rpc.StatusOk(),
		RedeemedOffer: redeemedOffer,
	}, nil
}

// DecryptRedeemedOffersDetails rpc is useful for decrypting encrypted offer details for given redeemed offers.
func (s *Service) DecryptRedeemedOffersDetails(ctx context.Context, req *redemption.DecryptRedeemedOffersDetailsRequest) (*redemption.DecryptRedeemedOffersDetailsResponse, error) {
	for _, redeemedOffer := range req.GetRedeemedOffers() {
		// decrypt encrypted fields of redeemed offer.
		if err := s.decryptRedeemedOfferDetails(ctx, redeemedOffer); err != nil {
			logger.Error(ctx, "error decrypting redeemed offer details", zap.String(logger.REDEEMED_OFFER_ID, redeemedOffer.GetId()), zap.Error(err))
			return &redemption.DecryptRedeemedOffersDetailsResponse{Status: rpc.StatusInternalWithDebugMsg("error decrypting redeemed offer details")}, nil
		}
	}
	return &redemption.DecryptRedeemedOffersDetailsResponse{
		Status:         rpc.StatusOk(),
		RedeemedOffers: req.GetRedeemedOffers(),
	}, nil
}

// GetRedeemedOfferVendorRedirectionUrl rpc is useful for getting a url where the user can be re-directed to view the redeemed offers which were fulfilled by the given vendor,
// useful for offers where the post the offer redemption on the Fi App, the voucher details are visible on vendor app only like thriwe benefits package offers.
func (s *Service) GetRedeemedOfferVendorRedirectionUrl(ctx context.Context, req *redemption.GetRedeemedOfferVendorRedirectionUrlRequest) (*redemption.GetRedeemedOfferVendorRedirectionUrlResponse, error) {
	offerVendor := s.offerVendorFactory.Get(req.GetOfferVendor())
	if offerVendor == nil {
		logger.Error(ctx, "no processor found for given offer vendor", zap.String(logger.VENDOR, req.GetOfferVendor().String()))
		return &redemption.GetRedeemedOfferVendorRedirectionUrlResponse{Status: rpc.StatusInternalWithDebugMsg("no processor found for given offer vendor")}, nil
	}

	redirectionUrl, err := offerVendor.GetRedeemedOffersDetailsRedirectionUrl(ctx, &offervendor.RedeemedOffersDetailsRedirectionUrlRequest{
		ActorId: req.GetActorId(),
	})
	if err != nil {
		logger.Error(ctx, "error fetching redirection url for the vendor", zap.String(logger.VENDOR, req.GetOfferVendor().String()), zap.Error(err))
		return &redemption.GetRedeemedOfferVendorRedirectionUrlResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &redemption.GetRedeemedOfferVendorRedirectionUrlResponse{
		Status:         rpc.StatusOk(),
		RedirectionUrl: redirectionUrl,
	}, nil
}

// decrypts sensitive fields of redeemed offer like voucher codes, pin etc.
func (s *Service) decryptRedeemedOfferDetails(ctx context.Context, redeemedOffer *redemption.RedeemedOffer) error {
	encryptionKeyId := redeemedOffer.GetRedeemedOfferDetails().GetEncryptionKeyId()

	var err error
	switch redeemedOffer.GetOfferType() {
	// unmask sensitive egv details
	case casperPb.OfferType_GIFT_CARD:
		egvDetails := redeemedOffer.GetRedeemedOfferDetails().GetEgiftCardDetails()
		if egvDetails == nil {
			logger.Info(ctx, "cannot decrypt egv details, details are nil", zap.String(logger.REDEEMED_OFFER_ID, redeemedOffer.GetId()))
			return nil
		}
		egvDetails.ActivationCode, err = s.redeemedOfferCryptor.Decrypt(ctx, egvDetails.ActivationCode, encryptionKeyId)
		if err != nil {
			return errors.Wrap(err, "error decrypting egv activation code")
		}
		egvDetails.Pin, err = s.redeemedOfferCryptor.Decrypt(ctx, egvDetails.Pin, encryptionKeyId)
		if err != nil {
			return errors.Wrap(err, "error decrypting egv pin")
		}
		egvDetails.CardNo, err = s.redeemedOfferCryptor.Decrypt(ctx, egvDetails.CardNo, encryptionKeyId)
		if err != nil {
			return errors.Wrap(err, "error decrypting egv card number")
		}
		egvDetails.ActivationUrl, err = s.redeemedOfferCryptor.Decrypt(ctx, egvDetails.ActivationUrl, encryptionKeyId)
		if err != nil {
			return errors.Wrap(err, "error decrypting egv activation url")
		}
		swapRedeemedOfferDetailsErr := s.swapRedeemedOfferDetails(ctx, redeemedOffer)
		if swapRedeemedOfferDetailsErr != nil {
			return swapRedeemedOfferDetailsErr
		}
	// unmask sensitive cms coupon details
	case casperPb.OfferType_CMS_COUPON:
		cmsCouponDetails := redeemedOffer.GetRedeemedOfferDetails().GetCmsCouponDetails()
		if cmsCouponDetails == nil {
			logger.Info(ctx, "cannot decrypt cms coupon details, details are nil", zap.String(logger.REDEEMED_OFFER_ID, redeemedOffer.GetId()))
			return nil
		}
		for idx, keyValuePair := range cmsCouponDetails.GetKeyValuePairs() {
			cmsCouponDetails.KeyValuePairs[idx].Value, err = s.redeemedOfferCryptor.Decrypt(ctx, keyValuePair.GetValue(), encryptionKeyId)
			if err != nil {
				return fmt.Errorf("error decrypting cms coupon value, err: %w", err)
			}
		}
	// unmask sensitive lounge access details
	case casperPb.OfferType_LOUNGE_ACCESS:
		loungeAccessDetails := redeemedOffer.GetRedeemedOfferDetails().GetLoungeAccessDetails()
		if loungeAccessDetails == nil {
			logger.Info(ctx, "cannot decrypt lounge access details, details are nil", zap.String(logger.REDEEMED_OFFER_ID, redeemedOffer.GetId()))
			return nil
		}
		loungeAccessDetails.VoucherCode, err = s.redeemedOfferCryptor.Decrypt(ctx, loungeAccessDetails.GetVoucherCode(), encryptionKeyId)
		if err != nil {
			return fmt.Errorf("error decrypting lounge access voucher code, err: %w", err)
		}
		loungeAccessDetails.VoucherQrUrl, err = s.redeemedOfferCryptor.Decrypt(ctx, loungeAccessDetails.GetVoucherQrUrl(), encryptionKeyId)
		if err != nil {
			return fmt.Errorf("error decrypting lounge access voucher qr url, err: %w", err)
		}
	// for other offer types decrypting is not needed as of now.
	default:

	}
	return nil
}

// in some cases we are getting cardNo and pin swapped from the vendor, so again swapping it before showing it to the user so that it is in the right format
func (s *Service) swapRedeemedOfferDetails(ctx context.Context, redeemedOffer *redemption.RedeemedOffer) error {

	offer, err := s.offerCatalogDao.GetOfferDetailsById(ctx, redeemedOffer.GetOfferId())
	if err != nil {
		return fmt.Errorf("error fetching offer by id, err: %w", err)
	}

	if lo.Contains(s.dyconf.SwapCardNoAndPinDetailsForSkuIds().ToStringArray(), offer.GetVendorOfferMetadata().GetQwikcilverVendorOfferMetadata().GetSkuId()) {
		tmpString := redeemedOffer.GetRedeemedOfferDetails().GetEgiftCardDetails().GetCardNo()
		redeemedOffer.GetRedeemedOfferDetails().GetEgiftCardDetails().CardNo = redeemedOffer.GetRedeemedOfferDetails().GetEgiftCardDetails().GetPin()
		redeemedOffer.GetRedeemedOfferDetails().GetEgiftCardDetails().Pin = tmpString
	}

	return nil

}
