Application:
  Environment: "test"
  Name: "inapphelp"

Server:
  Ports:
    GrpcPort: 8095
    GrpcSecurePort: 9508
    HttpPort: 9896
    HttpSecurePort: 9785

EpifiDb:
  AppName: "inapphelp"
  StatementTimeout: 5m
  Name: "inapphelp_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    dbusernamepassword: "{\"username\": \"root\", \"password\": \"\"}"
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Flags:
  TrimDebugMessageFromStatus: false
  FAQFetchSourceEnabled: true
  IsFetchFaqV2DaoEnabled: true

RelatedFaqCount: 2

FaqDocumentEventExternalQueuePublisher:
  QueueName: "inapphelp-faq-events-external-queue"

RelatedFaqConfig:
  BucketName: "stage-epifi-ds"
  ObjectKey: "CX/related_faqs/%s/related_faqs.csv"

FeedbackRuleEngineConfigMap:
  android:
    IsAppFlowBasedCoolOffEnabled: true
    CoolOffPeriodDuration: "240h"
    MaxNumberOfAttempts: 3
    AppFlowConfigsMap:
      app_flow_reward_claim_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 10
        PercentageOfAttemptsAllowed: 100
      app_flow_transaction_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 10
        PercentageOfAttemptsAllowed: 100
      app_flow_add_funds_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_fit_rules_creation_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_debit_card_activation_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_deposit_add_funds_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_connected_account_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_offer_redemption_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_cashback_reward_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_connected_net_worth_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_credit_score_generated_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
    AllRewardsRuleConfig:
      MinClaimCount: 3
      MinEarnedCount: 5
      LastClaimThresholdDuration: "10h"
      MinFiCoinsEarned: 1000
      MinCashbackEarned: 100
    AllTxnRuleConfig:
      DurationForTxnEvaluation: "720h"
      MaxFailedCountThreshold: 1
      MinSuccessCountThreshold: 2
      MinDebitAmount: 100
      MinSuccessfulDebitCountThreshold: 1
      PageSizeForOrders: 5
    ReferralRewardsRuleConfig:
      DurationForFetchingRewards: "24h"
    OnbAddFundsRuleConfig:
      IsRuleEnabled: true
      MaxTimeTakenToOnboard: "10m"
      MinAmountAdded: 100
      MaxTimeTakenToAddAmount: "4h"
      PageSizeForOrders: 30
    GenericAddFundsRuleConfig:
      IsRuleEnabled: true
      DurationForFailedAttemptEvaluation: "720h"
      DurationForSuccessfulAttemptEvaluation: "15m"
      MinAddFundsAmountAdded: 2000
      PageSizeForOrders: 10
    FITRuleConfig:
      IsRuleEnabled: true
      NumberOfActiveSubscriptions: 2
      MinAmountSaved: 2000
      ExcludeZenModeAndFootballSubscriber: true
    DebitCardActivationRuleConfig:
      IsRuleEnabled: true
      MaxTimeTakenToOnboard: "15m"
      MaxTimeTakenToActivateCard: "240h"
    DepositAddFundsRuleConfig:
      IsRuleEnabled: true
      Offset: 0
      PageSize: 1
      OrderFromDurationThreshold: "2h"
      MinDepositAddFundsAmount: 500
    ConnectedAccountRuleConfig:
      IsRuleEnabled: true
      AccountCreatedAtThresholdDuration: "1h"
      AccountLastSyncedAtThresholdDuration: "1h"
      PageSize: 10
    NetWorthRuleConfig:
      AccountLastSyncedTimeThreshold: "1h"
    AppFlowCoolOffs:
      APP_FLOW_TRANSACTION_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_REWARD_CLAIM_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_ADD_FUNDS_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_FIT_RULES_CREATION_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_CONNECTED_ACCOUNT_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_OFFER_REDEMPTION_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "720h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "720h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "720h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "720h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "1440h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "720h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "720h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "1440h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "1440h"
      APP_FLOW_CASHBACK_REWARD_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "720h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "720h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "720h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "720h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "1440h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "720h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "720h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "1440h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "1440h"
      APP_FLOW_CONNECTED_NET_WORTH_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "720h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "720h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "720h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "720h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "1440h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "720h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "720h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "1440h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "1440h"
      APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "720h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "720h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "720h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "720h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "1440h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "720h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "720h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "1440h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "1440h"
    AppFlowToNudgeDelayInSecondsMap:
      APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: 5
  ios:
    IsAppFlowBasedCoolOffEnabled: true
    CoolOffPeriodDuration: "240h"
    MaxNumberOfAttempts: 3
    AppFlowConfigsMap:
      app_flow_reward_claim_success:
        IsFlowEnabled: false
        MinSupportedAppVersion: 10
        PercentageOfAttemptsAllowed: 100
      app_flow_transaction_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 10
        PercentageOfAttemptsAllowed: 100
      app_flow_add_funds_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 92
        PercentageOfAttemptsAllowed: 100
      app_flow_fit_rules_creation_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 92
        PercentageOfAttemptsAllowed: 100
      app_flow_debit_card_activation_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 92
        PercentageOfAttemptsAllowed: 100
      app_flow_connected_account_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_deposit_add_funds_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_offer_redemption_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_cashback_reward_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_connected_net_worth_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
      app_flow_credit_score_generated_success:
        IsFlowEnabled: true
        MinSupportedAppVersion: 110
        PercentageOfAttemptsAllowed: 100
    AllRewardsRuleConfig:
      MinClaimCount: 3
      MinEarnedCount: 5
      LastClaimThresholdDuration: "10h"
      MinFiCoinsEarned: 1000
      MinCashbackEarned: 100
    AllTxnRuleConfig:
      DurationForTxnEvaluation: "720h"
      MaxFailedCountThreshold: 1
      MinSuccessCountThreshold: 2
      MinDebitAmount: 100
      MinSuccessfulDebitCountThreshold: 1
      PageSizeForOrders: 5
    ReferralRewardsRuleConfig:
      DurationForFetchingRewards: "24h"
    OnbAddFundsRuleConfig:
      IsRuleEnabled: true
      MaxTimeTakenToOnboard: "10m"
      MinAmountAdded: 100
      MaxTimeTakenToAddAmount: "4h"
      PageSizeForOrders: 30
    GenericAddFundsRuleConfig:
      IsRuleEnabled: true
      DurationForFailedAttemptEvaluation: "720h"
      DurationForSuccessfulAttemptEvaluation: "15m"
      MinAddFundsAmountAdded: 2000
      PageSizeForOrders: 10
    FITRuleConfig:
      IsRuleEnabled: true
      NumberOfActiveSubscriptions: 2
      MinAmountSaved: 2000
      ExcludeZenModeAndFootballSubscriber: true
    DebitCardActivationRuleConfig:
      IsRuleEnabled: true
      MaxTimeTakenToOnboard: "15m"
      MaxTimeTakenToActivateCard: "240h"
    DepositAddFundsRuleConfig:
      IsRuleEnabled: true
      Offset: 0
      PageSize: 1
      OrderFromDurationThreshold: "2h"
      MinDepositAddFundsAmount: 500
    ConnectedAccountRuleConfig:
      IsRuleEnabled: true
      AccountCreatedAtThresholdDuration: "1h"
      AccountLastSyncedAtThresholdDuration: "1h"
      PageSize: 10
    NetWorthRuleConfig:
      AccountLastSyncedTimeThreshold: "1h"
    AppFlowCoolOffs:
      APP_FLOW_TRANSACTION_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_REWARD_CLAIM_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_ADD_FUNDS_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_FIT_RULES_CREATION_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_CONNECTED_ACCOUNT_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "240h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "240h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "240h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "240h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "240h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "240h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "240h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "240h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "240h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "240h"
      APP_FLOW_OFFER_REDEMPTION_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "720h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "720h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "720h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "720h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "1440h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "720h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "720h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "1440h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "1440h"
      APP_FLOW_CASHBACK_REWARD_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "720h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "720h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "720h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "720h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "1440h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "720h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "720h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "1440h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "1440h"
      APP_FLOW_CONNECTED_NET_WORTH_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "720h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "720h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "720h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "720h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "1440h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "720h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "720h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "1440h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "1440h"
      APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS:
        APP_FLOW_TRANSACTION_SUCCESS: "720h"
        APP_FLOW_REWARD_CLAIM_SUCCESS: "720h"
        APP_FLOW_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_FIT_RULES_CREATION_SUCCESS: "720h"
        APP_FLOW_DEBIT_CARD_ACTIVATION_SUCCESS: "720h"
        APP_FLOW_DEPOSIT_ADD_FUNDS_SUCCESS: "720h"
        APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: "1440h"
        APP_FLOW_OFFER_REDEMPTION_SUCCESS: "720h"
        APP_FLOW_CASHBACK_REWARD_SUCCESS: "720h"
        APP_FLOW_CONNECTED_NET_WORTH_SUCCESS: "1440h"
        APP_FLOW_CREDIT_SCORE_GENERATED_SUCCESS: "1440h"
    AppFlowToNudgeDelayInSecondsMap:
      APP_FLOW_CONNECTED_ACCOUNT_SUCCESS: 5

RateLimitConfig:
  Namespace: "inapphelp"
  ResourceMap:
    rule_engine_flow_app_flow_transaction_success:
      Rate: 20
      Period: 1m
    rule_engine_flow_app_flow_reward_claim_success:
      Rate: 30
      Period: 1m
    rule_engine_flow_app_flow_add_funds_success:
      Rate: 10
      Period: 1m
    rule_engine_flow_app_flow_fit_rules_creation_success:
      Rate: 10
      Period: 1m
    rule_engine_flow_app_flow_debit_card_activation_success:
      Rate: 10
      Period: 1m
    rule_engine_flow_app_flow_deposit_add_funds_success:
      Rate: 10
      Period: 1m
    rule_engine_flow_app_flow_connected_account_success:
      Rate: 10
      Period: 1m
    rule_engine_flow_app_flow_offer_redemption_success:
      Rate: 10
      Period: 1m
    rule_engine_flow_app_flow_cashback_reward_success:
      Rate: 10
      Period: 1m
    rule_engine_flow_app_flow_connected_net_worth_success:
      Rate: 10
      Period: 1m
    rule_engine_flow_app_flow_credit_score_generated_success:
      Rate: 10
      Period: 1m

InAppFeedbackConfig:
  # MaxAttemptThresholdMap config will hold both app screen and feedback survey flow max feedback attempts
  MaxAttemptThresholdMap:
    app_screen_screener_terminal: 1
    app_screen_onboarding_dedupe_check_terminal: 1
    app_screen_onboarding_pan_name_mismatch_rejection: 1
    app_screen_sd_preclosure: 2
    feedback_survey_flow_insights: 1
    feedback_survey_flow_analyser: 100000


InAppHelpMediaConfig:
  IsContentEnabledForUIContext:
    ui_context_help_home_popular_stories: true
    ui_context_faq_category_stories: true
    ui_context_txn_receipt_stories: true
    ui_context_mutual_fund_stories: true
    ui_context_us_stocks_landing_page_stories: true
    ui_context_investment_landing_page_stories: true
  UIContextToSegmentEvaluationTypeMapping:
    ui_context_help_home_popular_stories: true
    ui_context_faq_category_stories: true
    ui_context_txn_receipt_stories: true
    ui_context_mutual_fund_stories: true
    ui_context_us_stocks_landing_page_stories: true
    ui_context_investment_landing_page_stories: true
  UIContextToFiLiteVisibilityMapping:
    ui_context_help_home_popular_stories: true
    ui_context_faq_category_stories: true
    ui_context_txn_receipt_stories: false
    ui_context_mutual_fund_stories: false
    ui_context_us_stocks_landing_page_stories: false
    ui_context_investment_landing_page_stories: false

PopularFAQConfig:
  IsRestrictedReleaseEnabled: false
  # if IsRestrictedReleaseEnabled is false then IsEnabledForPlatform and IsEnabledForUserGroup will not be evaluated
  IsEnabledForPlatform:
    android: true
    ios: true
  IsEnabledForUserGroup:
    internal: true
  NumOfFAQsToBeReturnedToClient: 3
  FAQBufferCount: -1

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

FeedbackInfoEventSnsPublisher:
  TopicName: "feedback-info-event-topic"

IssueReportingConfig:
  IsJsonFormatContextEnabled: true
  IsTrendingIssueRequired: false
  ContextTypeToConfigMap:
    USER_CONTEXT_TYPE_RECENT_ACTIVITY:
      ContextInitTemplate: "User has performed the following activities on Fi-App: \n"
      Template: "{#activity_type#} on Fi-App at {#timestamp#} additional details for activities are: {#activity_details#}"
      MaxNumberOfItemsToBeFetched: 3
      MaxNumberOfItemsInPrompt: 2
    USER_CONTEXT_TYPE_SUPPORT_TICKET:
      ContextInitTemplate: "User has following support tickets raised: \n"
      Template: "ID: {#ticket_id#} for issue related to {#issue_description#} created on {#timestamp#} and currently is in {#status#}"
      MaxNumberOfItemsToBeFetched: 3
      MaxNumberOfItemsInPrompt: 2
    USER_CONTEXT_TYPE_WATSON_INCIDENT:
      ContextInitTemplate: "User has faced following issues auto-detected by our system: \n"
      Template: "Issue related to {#issue_description#} at {#timestamp#}, an automatic support ticket has been created with ID: {#ticket_id#}"
      MaxNumberOfItemsToBeFetched: 3
      MaxNumberOfItemsInPrompt: 2

RedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0
