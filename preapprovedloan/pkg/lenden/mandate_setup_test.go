package lenden

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	userMocks "github.com/epifi/gamma/api/user/mocks"
	obfuscatorPb "github.com/epifi/gamma/api/user/obfuscator"
	obfuscatorMocks "github.com/epifi/gamma/api/user/obfuscator/mocks"
	lendenVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	lendenMocks "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden/mocks"
	palDaoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

type mockFields struct {
	loanApplicantDao     *palDaoMocks.MockLoanApplicantDao
	loanStepExecutionDao *palDaoMocks.MockLoanStepExecutionsDao
	loanRequestDao       *palDaoMocks.MockLoanRequestsDao
	rpcHelper            *helper.RpcHelper // can be nil if not used directly
	userClient           *userMocks.MockUsersClient
	lendenVgClient       *lendenMocks.MockLendenClient
	obfuscatorClient     *obfuscatorMocks.MockObfuscatorClient
}

func initMocks(ctrl *gomock.Controller) *mockFields {
	userClient := userMocks.NewMockUsersClient(ctrl)
	obfuscatorClient := obfuscatorMocks.NewMockObfuscatorClient(ctrl)
	return &mockFields{
		loanApplicantDao:     palDaoMocks.NewMockLoanApplicantDao(ctrl),
		loanStepExecutionDao: palDaoMocks.NewMockLoanStepExecutionsDao(ctrl),
		loanRequestDao:       palDaoMocks.NewMockLoanRequestsDao(ctrl),
		userClient:           userClient,
		lendenVgClient:       lendenMocks.NewMockLendenClient(ctrl),
		obfuscatorClient:     obfuscatorClient,
		rpcHelper: helper.NewRpcHelper(nil, nil, userClient, nil, nil,
			nil, nil, nil, nil, nil, nil, nil,
			nil, nil, nil, nil, nil, nil,
			nil, nil, nil, nil, nil, nil,
			nil, nil, nil, nil, nil, nil,
			nil, nil, obfuscatorClient, nil, nil, nil,
			nil, nil, nil, nil, nil, nil,
			nil, nil, nil,
		),
	}
}

func newProcessorWithMocks(f *mockFields) *MandateSetupProcessor {
	return &MandateSetupProcessor{
		loanApplicantDao:     f.loanApplicantDao,
		loanStepExecutionDao: f.loanStepExecutionDao,
		loanRequestDao:       f.loanRequestDao,
		rpcHelper:            f.rpcHelper,
		userClient:           f.userClient,
		lendenVgClient:       f.lendenVgClient,
	}
}

func TestMandateSetupProcessor_GetMandateInfo(t *testing.T) {
	t.Parallel()
	ctx := context.Background()

	type testCase struct {
		name      string
		setup     func(f *mockFields)
		lse       *palPb.LoanStepExecution
		want      *MandateInfo
		wantErr   bool
		requireFn func(t *testing.T, got *MandateInfo, err error)
	}

	trackingId := "track-123"
	mandateUrl := "https://mandate.url"
	urlGenAt := time.Now().Add(-10 * time.Minute)
	urlValidity := timestamppb.New(urlGenAt.Add(15 * time.Minute))

	applicant := &palPb.LoanApplicant{Id: "app-1", VendorApplicantId: "vend-app-1"}

	lseBase := &palPb.LoanStepExecution{
		Id:      "lse-1",
		ActorId: "actor-1",
		RefId:   "ref-1",
		Details: &palPb.LoanStepExecutionDetails{
			Details: &palPb.LoanStepExecutionDetails_MandateData{
				MandateData: &palPb.MandateData{},
			},
		},
	}

	tests := []testCase{
		{
			name: "Mandate not initiated, InitiateMandate returns AlreadyCompleted",
			lse:  proto.Clone(lseBase).(*palPb.LoanStepExecution),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil).AnyTimes()
				f.lendenVgClient.EXPECT().InitMandate(ctx, gomock.Any()).Return(&lendenVgPb.InitMandateResponse{
					Status:     &rpc.Status{Code: uint32(lendenVgPb.InitMandateResponse_ENACH_ALREADY_COMPLETED)},
					TrackingId: trackingId,
				}, nil)
				f.loanRequestDao.EXPECT().GetById(ctx, "ref-1").Return(&palPb.LoanRequest{VendorRequestId: "vend-req-1"}, nil)
				f.loanStepExecutionDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).
					Return(&userPb.GetUserDevicePropertiesResponse{Status: rpc.StatusOk()}, nil).AnyTimes()
				f.obfuscatorClient.EXPECT().GetPIIFromToken(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.GetPIIFromTokenResponse{
					Status: rpc.StatusOk(),
					Pii: &types.Identifier{
						IdType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{PropValue: &types.IdentifierValue_IpAddress{IpAddress: &types.IpAddress{IpAddress: "ip-1"}}},
					},
				}, nil).AnyTimes()
			},
			want:    &MandateInfo{IsCompleted: true},
			wantErr: false,
			requireFn: func(t *testing.T, got *MandateInfo, err error) {
				require.NoError(t, err)
				require.True(t, got.IsCompleted)
				require.NotNil(t, got.Lse)
				require.Equal(t, trackingId, got.Lse.GetDetails().GetMandateData().GetMerchantTxnId())
			},
		},
		{
			name: "Mandate not initiated, InitiateMandate returns PreRequisitesPending",
			lse:  proto.Clone(lseBase).(*palPb.LoanStepExecution),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil).AnyTimes()
				f.lendenVgClient.EXPECT().InitMandate(ctx, gomock.Any()).Return(&lendenVgPb.InitMandateResponse{
					Status: &rpc.Status{Code: uint32(lendenVgPb.InitMandateResponse_OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND)},
				}, nil)
				f.loanRequestDao.EXPECT().GetById(ctx, "ref-1").Return(&palPb.LoanRequest{VendorRequestId: "vend-req-1"}, nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).
					Return(&userPb.GetUserDevicePropertiesResponse{Status: rpc.StatusOk()}, nil).AnyTimes()
				f.obfuscatorClient.EXPECT().GetPIIFromToken(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.GetPIIFromTokenResponse{
					Status: rpc.StatusOk(),
					Pii: &types.Identifier{
						IdType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{PropValue: &types.IdentifierValue_IpAddress{IpAddress: &types.IpAddress{IpAddress: "ip-1"}}},
					},
				}, nil).AnyTimes()
			},
			want:    &MandateInfo{IsCompleted: false},
			wantErr: false,
			requireFn: func(t *testing.T, got *MandateInfo, err error) {
				require.NoError(t, err)
				require.False(t, got.IsCompleted)
			},
		},
		{
			name: "Mandate not initiated, InitiateMandate returns InProgress",
			lse:  proto.Clone(lseBase).(*palPb.LoanStepExecution),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil).AnyTimes()
				f.lendenVgClient.EXPECT().InitMandate(ctx, gomock.Any()).Return(&lendenVgPb.InitMandateResponse{
					Status:             rpc.StatusOk(),
					TrackingId:         trackingId,
					RedirectionUrl:     mandateUrl,
					MandateUrlValidity: urlValidity,
				}, nil)
				f.loanRequestDao.EXPECT().GetById(ctx, "ref-1").Return(&palPb.LoanRequest{VendorRequestId: "vend-req-1"}, nil)
				f.loanStepExecutionDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).
					Return(&userPb.GetUserDevicePropertiesResponse{Status: rpc.StatusOk()}, nil).AnyTimes()
				f.obfuscatorClient.EXPECT().GetPIIFromToken(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.GetPIIFromTokenResponse{
					Status: rpc.StatusOk(),
					Pii: &types.Identifier{
						IdType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{PropValue: &types.IdentifierValue_IpAddress{IpAddress: &types.IpAddress{IpAddress: "ip-1"}}},
					},
				}, nil).AnyTimes()
			},
			want:    &MandateInfo{IsCompleted: false},
			wantErr: false,
			requireFn: func(t *testing.T, got *MandateInfo, err error) {
				require.NoError(t, err)
				require.False(t, got.IsCompleted)
				require.NotNil(t, got.Lse)
				require.Equal(t, trackingId, got.Lse.GetDetails().GetMandateData().GetMerchantTxnId())
				require.Equal(t, mandateUrl, got.Lse.GetDetails().GetMandateData().GetUrl())
			},
		},
		{
			name: "Mandate tracking ID already present, status completed",
			lse: func() *palPb.LoanStepExecution {
				lse := proto.Clone(lseBase).(*palPb.LoanStepExecution)
				lse.GetDetails().GetMandateData().MerchantTxnId = trackingId
				return lse
			}(),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil).AnyTimes()
				f.lendenVgClient.EXPECT().CheckMandateStatus(ctx, gomock.Any()).Return(&lendenVgPb.CheckMandateStatusResponse{
					Status:        rpc.StatusOk(),
					MandateStatus: lendenVgPb.MandateStatus_MANDATE_STATUS_COMPLETED,
					TrackingId:    trackingId,
				}, nil)
				f.loanStepExecutionDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).
					Return(&userPb.GetUserDevicePropertiesResponse{Status: rpc.StatusOk()}, nil).AnyTimes()
				f.obfuscatorClient.EXPECT().GetPIIFromToken(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.GetPIIFromTokenResponse{
					Status: rpc.StatusOk(),
					Pii: &types.Identifier{
						IdType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{PropValue: &types.IdentifierValue_IpAddress{IpAddress: &types.IpAddress{IpAddress: "ip-1"}}},
					},
				}, nil).AnyTimes()
			},
			want:    &MandateInfo{IsCompleted: true},
			wantErr: false,
			requireFn: func(t *testing.T, got *MandateInfo, err error) {
				require.NoError(t, err)
				require.True(t, got.IsCompleted)
			},
		},
		{
			name: "Mandate tracking ID already present, status failed, re-initiate returns completed",
			lse: func() *palPb.LoanStepExecution {
				lse := proto.Clone(lseBase).(*palPb.LoanStepExecution)
				lse.GetDetails().GetMandateData().MerchantTxnId = trackingId
				return lse
			}(),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil).AnyTimes()
				f.lendenVgClient.EXPECT().CheckMandateStatus(ctx, gomock.Any()).Return(&lendenVgPb.CheckMandateStatusResponse{
					Status:        rpc.StatusOk(),
					MandateStatus: lendenVgPb.MandateStatus_MANDATE_STATUS_FAILED,
				}, nil)
				f.lendenVgClient.EXPECT().InitMandate(ctx, gomock.Any()).Return(&lendenVgPb.InitMandateResponse{
					Status:     &rpc.Status{Code: uint32(lendenVgPb.InitMandateResponse_ENACH_ALREADY_COMPLETED)},
					TrackingId: trackingId,
				}, nil)
				f.loanRequestDao.EXPECT().GetById(ctx, "ref-1").Return(&palPb.LoanRequest{VendorRequestId: "vend-req-1"}, nil)
				f.loanStepExecutionDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).
					Return(&userPb.GetUserDevicePropertiesResponse{Status: rpc.StatusOk()}, nil).AnyTimes()
				f.obfuscatorClient.EXPECT().GetPIIFromToken(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.GetPIIFromTokenResponse{
					Status: rpc.StatusOk(),
					Pii: &types.Identifier{
						IdType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{PropValue: &types.IdentifierValue_IpAddress{IpAddress: &types.IpAddress{IpAddress: "ip-1"}}},
					},
				}, nil).AnyTimes()
			},
			want:    &MandateInfo{IsCompleted: true},
			wantErr: false,
			requireFn: func(t *testing.T, got *MandateInfo, err error) {
				require.NoError(t, err)
				require.True(t, got.IsCompleted)
			},
		},
		{
			name: "Mandate tracking ID already present, status in progress, link expired, re-initiate returns in progress",
			lse: func() *palPb.LoanStepExecution {
				lse := proto.Clone(lseBase).(*palPb.LoanStepExecution)
				lse.GetDetails().GetMandateData().MerchantTxnId = trackingId
				lse.GetDetails().GetMandateData().UrlGeneratedAt = timestamppb.New(time.Now().Add(-20 * time.Minute))
				return lse
			}(),
			setup: func(f *mockFields) {
				f.loanApplicantDao.EXPECT().GetByActorId(ctx, "actor-1").Return(applicant, nil).AnyTimes()
				f.lendenVgClient.EXPECT().CheckMandateStatus(ctx, gomock.Any()).Return(&lendenVgPb.CheckMandateStatusResponse{
					Status:        rpc.StatusOk(),
					MandateStatus: lendenVgPb.MandateStatus_MANDATE_STATUS_IN_PROGRESS,
				}, nil)
				f.lendenVgClient.EXPECT().InitMandate(ctx, gomock.Any()).Return(&lendenVgPb.InitMandateResponse{
					Status:             rpc.StatusOk(),
					TrackingId:         trackingId,
					RedirectionUrl:     mandateUrl,
					MandateUrlValidity: urlValidity,
				}, nil)
				f.loanRequestDao.EXPECT().GetById(ctx, "ref-1").Return(&palPb.LoanRequest{VendorRequestId: "vend-req-1"}, nil)
				f.loanStepExecutionDao.EXPECT().Update(ctx, gomock.Any(), gomock.Any()).Return(nil)
				f.userClient.EXPECT().GetUserDeviceProperties(ctx, gomock.Any()).
					Return(&userPb.GetUserDevicePropertiesResponse{Status: rpc.StatusOk()}, nil).AnyTimes()
				f.obfuscatorClient.EXPECT().GetPIIFromToken(gomock.Any(), gomock.Any()).Return(&obfuscatorPb.GetPIIFromTokenResponse{
					Status: rpc.StatusOk(),
					Pii: &types.Identifier{
						IdType:  types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
						IdValue: &types.IdentifierValue{PropValue: &types.IdentifierValue_IpAddress{IpAddress: &types.IpAddress{IpAddress: "ip-1"}}},
					},
				}, nil).AnyTimes()
			},
			want:    &MandateInfo{IsCompleted: false},
			wantErr: false,
			requireFn: func(t *testing.T, got *MandateInfo, err error) {
				require.NoError(t, err)
				require.False(t, got.IsCompleted)
			},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initMocks(ctrl)
			if tc.setup != nil {
				tc.setup(f)
			}
			p := newProcessorWithMocks(f)
			got, err := p.FetchOrRefreshMandateStatus(ctx, tc.lse)
			if tc.requireFn != nil {
				tc.requireFn(t, got, err)
			} else {
				if tc.wantErr {
					require.Error(t, err)
				} else {
					require.NoError(t, err)
					require.Equal(t, tc.want.IsCompleted, got.IsCompleted)
				}
			}
		})
	}
}
