package processor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/developer"
	palEnums "github.com/epifi/gamma/api/preapprovedloan/enums"
	"github.com/epifi/gamma/preapprovedloan/dao"
)

type DevLoansMasterEntity struct {
	loanReqDao                      dao.LoanRequestsDao
	loanOfferEligibilityCriteriaDao dao.LoanOfferEligibilityCriteriaDao
	loanOffersDao                   dao.LoanOffersDao
	loanAccountsDao                 dao.LoanAccountsDao
	loanStepExecutionsDao           dao.LoanStepExecutionsDao
	loanApplicantDao                dao.LoanApplicantDao
}

func NewDevLoansMasterEntity(
	loanReqDao dao.LoanRequestsDao,
	loanOfferEligibilityCriteriaDao dao.LoanOfferEligibilityCriteriaDao,
	loanOffersDao dao.LoanOffersDao,
	loanAccountsDao dao.LoanAccountsDao,
	loanStepExecutionsDao dao.LoanStepExecutionsDao,
	loanApplicantDao dao.LoanApplicantDao,
) *DevLoansMasterEntity {
	return &DevLoansMasterEntity{
		loanReqDao:                      loanReqDao,
		loanOfferEligibilityCriteriaDao: loanOfferEligibilityCriteriaDao,
		loanOffersDao:                   loanOffersDao,
		loanAccountsDao:                 loanAccountsDao,
		loanStepExecutionsDao:           loanStepExecutionsDao,
		loanApplicantDao:                loanApplicantDao,
	}
}

func (d *DevLoansMasterEntity) FetchParamList(ctx context.Context, entity developer.PreApprovedLoanEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           ActorIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
		{
			Name:            Vendor,
			Label:           Vendor,
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         vendors,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevLoansMasterEntity) FetchData(ctx context.Context, entity developer.PreApprovedLoanEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}

	var actorId string
	var vendor preApprovedLoanPb.Vendor

	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			actorId = filter.GetStringValue()
		case Vendor:
			vendor = preApprovedLoanPb.Vendor(preApprovedLoanPb.Vendor_value[filter.GetDropdownValue()])
		default:
			return "", fmt.Errorf("unknown param type")
		}
	}

	if actorId == "" {
		return "", errors.New("actor_id is mandatory")
	}

	result := make(map[string]interface{})

	// Case 1: No vendor specified - fetch data for all vendors
	if vendor == preApprovedLoanPb.Vendor_VENDOR_UNSPECIFIED {
		for v := range preApprovedLoanPb.Vendor_value {
			if v == "VENDOR_UNSPECIFIED" {
				continue
			}
			vendorEnum := preApprovedLoanPb.Vendor(preApprovedLoanPb.Vendor_value[v])
			vendorData, err := d.fetchDataForVendor(ctx, actorId, vendorEnum)
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					continue // Skip vendors with no data
				}
				return "", err
			}

			// Only add vendor data if we found any records
			if len(vendorData) > 0 {
				result[v] = vendorData
			}
		}
	} else {
		// Case 2: Specific vendor provided
		vendorData, err := d.fetchDataForVendor(ctx, actorId, vendor)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return "{}", nil
			}
			return "", err
		}
		result[vendor.String()] = vendorData
	}

	if len(result) == 0 {
		return "{}", nil
	}

	jsonBytes, err := json.Marshal(result)
	if err != nil {
		return "", fmt.Errorf("error marshaling response: %w", err)
	}

	return string(jsonBytes), nil
}

func (d *DevLoansMasterEntity) fetchDataForVendor(ctx context.Context, actorId string, vendor preApprovedLoanPb.Vendor) (map[string]interface{}, error) {
	vendorData := make(map[string]interface{})
	hasData := false

	loanRequests, err := d.loanReqDao.GetByActorIdAndVendorAndStatus(ctx, actorId, vendor, nil)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("error getting loan requests: %w", err)
	}
	if len(loanRequests) > 0 {
		for _, lr := range loanRequests {
			lr.Details = getMaskedLoanRequestDetails(lr.GetDetails())
		}
		vendorData["lr"] = loanRequests
		hasData = true

		var redactedLoanStepExecutions []*preApprovedLoanPb.LoanStepExecution
		for _, lr := range loanRequests {
			loanStepExecutions, err := d.loanStepExecutionsDao.GetByRefIdAndStatuses(ctx, lr.GetId(), nil)
			if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
				return nil, fmt.Errorf("error getting loan step executions: %w", err)
			}
			if len(loanStepExecutions) > 0 {
				// Mask sensitive data in loan step executions
				for _, lse := range loanStepExecutions {
					redactPIIData(ctx, lse.GetDetails())
				}
				redactedLoanStepExecutions = append(redactedLoanStepExecutions, loanStepExecutions...)
			}
		}

		if len(redactedLoanStepExecutions) > 0 {
			vendorData["lse"] = redactedLoanStepExecutions
		} else {
			vendorData["lse"] = nil
		}
	} else {
		vendorData["lr"] = nil
		vendorData["lse"] = nil
	}

	var loanApplicants []*preApprovedLoanPb.LoanApplicant
	for loanProgram := range preApprovedLoanPb.LoanProgram_value {
		if loanProgram == "LOAN_PROGRAM_UNSPECIFIED" {
			continue
		}
		loanProgramEnum := preApprovedLoanPb.LoanProgram(preApprovedLoanPb.LoanProgram_value[loanProgram])

		for version := range palEnums.LoanProgramVersion_value {
			versionEnum := palEnums.LoanProgramVersion(palEnums.LoanProgramVersion_value[version])
			loanApplicant, err := d.loanApplicantDao.GetByActorIdAndVendorAndLoanProgramAndProgramVersion(ctx, actorId, vendor, loanProgramEnum, versionEnum)
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					continue
				}
				return nil, fmt.Errorf("error getting loan applicant: %w", err)
			}

			if loanApplicant != nil {
				loanApplicant = getMaskedLoanApplicant(ctx, loanApplicant)
				loanApplicants = append(loanApplicants, loanApplicant)
			}
		}
	}

	if len(loanApplicants) > 0 {
		vendorData["la"] = loanApplicants
	} else {
		vendorData["la"] = nil
	}

	loanOfferEligibilityCriteria, err := d.loanOfferEligibilityCriteriaDao.GetByActorIdAndVendorAndStatus(ctx, actorId, vendor, nil, true)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("error getting loan offer eligibility criteria: %w", err)
	}
	if len(loanOfferEligibilityCriteria) > 0 {
		for _, loec := range loanOfferEligibilityCriteria {
			maskLoec(loec)
		}
		vendorData["loec"] = loanOfferEligibilityCriteria
		hasData = true
	} else {
		vendorData["loec"] = nil
	}

	loanOffer, err := d.loanOffersDao.GetActiveOfferByActorIdAndVendor(ctx, actorId, vendor)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("error getting loan offers: %w", err)
	}
	if loanOffer != nil {
		vendorData["lo"] = loanOffer
		hasData = true
	} else {
		vendorData["lo"] = nil
	}

	loanAccounts, err := d.loanAccountsDao.GetByActorIdAndVendor(ctx, actorId, vendor)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("error getting loan accounts: %w", err)
	}
	if len(loanAccounts) > 0 {
		for _, acc := range loanAccounts {
			maskLoanAccount(acc)
		}
		vendorData["acc"] = loanAccounts
		hasData = true
	} else {
		vendorData["acc"] = nil
	}

	if !hasData {
		return nil, epifierrors.ErrRecordNotFound
	}

	return vendorData, nil
}
