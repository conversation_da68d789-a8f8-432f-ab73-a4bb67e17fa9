//nolint:dupl,funlen
package processor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/developer"
	vendorsRedactor "github.com/epifi/gamma/api/vendors/redactor"
	"github.com/epifi/gamma/preapprovedloan/dao"
)

type DevLoanStepExecutionEntity struct {
	loanStepExecutionDao dao.LoanStepExecutionsDao
}

func NewDevLoanStepExecutionEntity(loanStepExecutionDao dao.LoanStepExecutionsDao) *DevLoanStepExecutionEntity {
	return &DevLoanStepExecutionEntity{
		loanStepExecutionDao: loanStepExecutionDao,
	}
}

func (d *DevLoanStepExecutionEntity) FetchParamList(ctx context.Context, entity developer.PreApprovedLoanEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            ActorId,
			Label:           ActorIdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            LoanRequestId,
			Label:           "Loan Request Id / Loan Payment Request Id",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            Id,
			Label:           IdLabel,
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *DevLoanStepExecutionEntity) FetchData(ctx context.Context, entity developer.PreApprovedLoanEntity, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", errors.New("filter cannot be nil")
	}
	var actorId, loanReqId, lseId string
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case ActorId:
			actorId = filter.GetStringValue()
		case LoanRequestId:
			loanReqId = filter.GetStringValue()
		case Id:
			lseId = filter.GetStringValue()
		default:
			return "", fmt.Errorf("unknonw param type")
		}
	}
	if lseId != "" {
		loanStepExe, err := d.loanStepExecutionDao.GetById(ctx, lseId)
		if err != nil {
			logger.Error(ctx, "cannot fetch loan step execution using lseId", zap.Error(err))
			return err.Error(), nil
		}
		redactPIIData(ctx, loanStepExe.GetDetails())
		e, err := json.Marshal(loanStepExe)
		if err != nil {
			logger.Error(ctx, "cannot marshal struct to json", zap.Error(err))
			return marshalErr, nil
		}
		return string(e), nil
	}
	if actorId != "" {
		loanStepExe, err := d.loanStepExecutionDao.GetByActorId(ctx, actorId)
		if err != nil {
			logger.Error(ctx, "cannot fetch loan step execution using actorID", zap.Error(err))
			return "cannot fetch loan step execution using actorID", nil
		}
		for _, loanStep := range loanStepExe {
			if loanStep.GetDetails().GetESignStepData() != nil {
				loanStep.GetDetails().GetESignStepData().SignUrl = ""
			}
			redactPIIData(ctx, loanStep.GetDetails())
		}
		e, err := json.Marshal(loanStepExe)
		if err != nil {
			logger.Error(ctx, "cannot marshal struct to json")
			return marshalErr, nil
		}
		return string(e), nil
	}
	if loanReqId != "" {
		loanStepExe, err := d.loanStepExecutionDao.GetByRefIdAndStatuses(ctx, loanReqId, nil)
		if err != nil {
			logger.Error(ctx, "cannot fetch loan step execution using loan request ID")
			return "cannot fetch loan step execution using loan request ID", nil
		}
		for _, loanStep := range loanStepExe {
			redactPIIData(ctx, loanStep.GetDetails())
		}
		e, err := json.Marshal(loanStepExe)
		if err != nil {
			logger.Error(ctx, "cannot marshal struct to json")
			return marshalErr, nil
		}
		return string(e), nil
	}
	return "", nil
}

func redactPIIData(ctx context.Context, details *palpb.LoanStepExecutionDetails) {
	if details.GetVendorData() != "" {
		details.VendorData = ""
	}
	switch details.GetDetails().(type) {
	case *palpb.LoanStepExecutionDetails_ESignStepData:
		if details.GetESignStepData() != nil {
			details.GetESignStepData().SignUrl = mask.GetMaskedString(mask.MaskAllChars, details.GetESignStepData().GetSignUrl())
		}
	case *palpb.LoanStepExecutionDetails_ManualReviewStepData:
		if details.GetManualReviewStepData() != nil {
			if details.GetManualReviewStepData().GetReviewerDetails() != nil {
				details.GetManualReviewStepData().GetReviewerDetails().Email = mask.GetMaskedString(mask.MaskCharTillAtSign, details.GetManualReviewStepData().GetReviewerDetails().GetEmail())
			}
		}
	case *palpb.LoanStepExecutionDetails_OnboardingData:
		if details.GetOnboardingData() != nil {
			if details.GetOnboardingData().GetEmploymentDetails() != nil {
				details.GetOnboardingData().GetEmploymentDetails().MonthlyIncome = mask.GetMaskedMoney(details.GetOnboardingData().GetEmploymentDetails().GetMonthlyIncome())
				details.GetOnboardingData().GetEmploymentDetails().OrganizationName = mask.GetMaskedString(mask.MaskAllChars, details.GetOnboardingData().GetEmploymentDetails().GetOrganizationName())
				details.GetOnboardingData().GetEmploymentDetails().WorkEmail = mask.GetMaskedString(mask.MaskCharTillAtSign, details.GetOnboardingData().GetEmploymentDetails().GetWorkEmail())
				details.GetOnboardingData().GetEmploymentDetails().OfficeAddress = getMaskedAddress(details.GetOnboardingData().GetEmploymentDetails().GetOfficeAddress())
			}
			if details.GetOnboardingData().GetBankingDetails() != nil {
				details.GetOnboardingData().GetBankingDetails().AccountNumber = mask.GetMaskedString(mask.DontMaskLastFourChars, details.GetOnboardingData().GetBankingDetails().GetAccountNumber())
				details.GetOnboardingData().GetBankingDetails().AccountHolderName = mask.GetMaskedString(mask.DontMaskFirstFourChars, details.GetOnboardingData().GetBankingDetails().GetAccountHolderName())
			}
			if details.GetOnboardingData().GetAddressDetails() != nil {
				details.GetOnboardingData().GetAddressDetails().AddressDetails = getMaskedAddress(details.GetOnboardingData().GetAddressDetails().GetAddressDetails())
				details.GetOnboardingData().GetAddressDetails().LocationToken = mask.GetMaskedString(mask.MaskAllChars, details.GetOnboardingData().GetAddressDetails().GetLocationToken())
			}
		}
	case *palpb.LoanStepExecutionDetails_MandateData:
		if details.GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed() != nil {
			details.GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().AccountNumber = mask.GetMaskedString(mask.DontMaskLastFourChars, details.GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().GetAccountNumber())
			details.GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().AccountHolderName = mask.GetMaskedString(mask.DontMaskFirstFourChars, details.GetMandateData().GetBankingDetails().GetFinalAccDetailsUsed().GetAccountHolderName())

		}
	case *palpb.LoanStepExecutionDetails_CkycStepData:
		if details.GetCkycStepData() != nil {
			details.GetCkycStepData().PAN = mask.GetMaskedString(mask.DontMaskFirstFourChars, details.GetCkycStepData().GetPAN())
		}
	case *palpb.LoanStepExecutionDetails_ApplicantData:
		if details.GetApplicantData() != nil {
			details.GetApplicantData().Pan = mask.GetMaskedString(mask.DontMaskFirstFourChars, details.GetApplicantData().GetPan())
			details.GetApplicantData().PhoneNumber = mask.GetMaskedPhoneNumberV2(details.GetApplicantData().GetPhoneNumber())
			details.GetApplicantData().Email = mask.GetMaskedString(mask.MaskCharTillAtSign, details.GetApplicantData().GetEmail())
		}
	case *palpb.LoanStepExecutionDetails_OtpVerificationData:
		if details.GetOtpVerificationData() != nil {
			for i := range details.GetOtpVerificationData().GetOtpData() {
				details.GetOtpVerificationData().GetOtpData()[i].PhoneNumber = mask.GetMaskedPhoneNumberV2(details.GetOtpVerificationData().GetOtpData()[i].GetPhoneNumber())
				details.GetOtpVerificationData().GetOtpData()[i].Email = mask.GetMaskedString(mask.MaskCharTillAtSign, details.GetOtpVerificationData().GetOtpData()[i].GetEmail())
				details.GetOtpVerificationData().GetOtpData()[i].AssetDetails = nil
			}
		}
	case *palpb.LoanStepExecutionDetails_IncomeEstimateData:
		if details.GetIncomeEstimateData().GetPredictedIncome() != nil {
			details.GetIncomeEstimateData().GetPredictedIncome().Units = int64(mask.GetMaskedInt(nil))
			redactedRawResponse, err := httpcontentredactor.GetInstance().Redact(ctx, []byte(details.GetIncomeEstimateData().GetRawResponse()), httpcontentredactor.ContentTypeJSON, vendorsRedactor.Config)
			if err != nil {
				logger.Error(ctx, "error in redacting income estimator raw response", zap.Error(err))
			} else {
				details.GetIncomeEstimateData().RawResponse = string(redactedRawResponse)
			}
		}
	case *palpb.LoanStepExecutionDetails_CreateLeadStepData:
		if details.GetCreateLeadStepData() != nil {

			details.GetCreateLeadStepData().CurrentAddress = getMaskedAddress(details.GetCreateLeadStepData().GetCurrentAddress())
			details.GetCreateLeadStepData().Pan = mask.GetMaskedString(mask.DontMaskFirstFourChars, details.GetCreateLeadStepData().GetPan())
			details.GetCreateLeadStepData().DeclaredIncome = nil
			details.GetCreateLeadStepData().Email = mask.GetMaskedString(mask.MaskCharTillAtSign, details.GetCreateLeadStepData().GetEmail())
			details.GetCreateLeadStepData().PhoneNumber = mask.GetMaskedPhoneNumberV2(details.GetCreateLeadStepData().GetPhoneNumber())
		}
	case *palpb.LoanStepExecutionDetails_ContactabilityDetailsData:
		if details.GetContactabilityDetailsData() != nil {
			details.GetContactabilityDetailsData().PhoneNumber = mask.GetMaskedPhoneNumberV2(details.GetContactabilityDetailsData().GetPhoneNumber())
		}

	default:
		// do nothing

	}
}
