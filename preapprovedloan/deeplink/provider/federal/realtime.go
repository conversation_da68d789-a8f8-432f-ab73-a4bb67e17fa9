//nolint:dupl
package federal

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/api/typesv2/common/ui/widget"

	"github.com/epifi/gamma/api/frontend/analytics"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/preapprovedloan"
	typesPb "github.com/epifi/gamma/api/typesv2"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
)

type RealTimeFedProvider struct {
	*EtbFedProvider
}

var _ provider.IDeeplinkProvider = &RealTimeFedProvider{}

func NewRealTimeFedProvider(etbFedProvider *EtbFedProvider) *RealTimeFedProvider {
	return &RealTimeFedProvider{
		etbFedProvider,
	}
}

func (rtd *RealTimeFedProvider) GetLoanHeader() *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palFeEnumsPb.Vendor_FEDERAL_BANK,
	}
}

func (rtd *RealTimeFedProvider) GetLoanHeaderWithEventData(entryPoint palFeEnumsPb.EntryPoint) *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
		Vendor:      palFeEnumsPb.Vendor_FEDERAL_BANK,
		EventData:   &palFeEnumsPb.EventData{EntryPoint: entryPoint},
	}
}

//nolint:dupl,funlen
func (rtd *RealTimeFedProvider) GetEligibilitySuccessScreen(ctx context.Context, lh *palFeEnumsPb.LoanHeader, offerAmount *moneyPb.Money, interestRate float64, offerId string, lo *preapprovedloan.LoanOffer) *deeplinkPb.Deeplink {
	return deeplinkV3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_PL_ELIGIBILITY_OFFER_AVAILABLE_SCREEN, &palTypesPb.LoanEligibilityOfferAvailableScreenOptions{
		LoanHeader:        lh,
		TicketTitle:       helper.GetText("Congratulations!", "#313234", "", commontypes.FontStyle_DISPLAY_XL),
		TicketDescription: helper.GetText("You have got a loan offer.", "#929599", "", commontypes.FontStyle_SUBTITLE_S),
		TicketBox: &palTypesPb.LoanEligibilityOfferAvailableScreenOptions_TicketBox{
			BgColor:      "#FFFFFF",
			AmountTitle:  helper.GetText("Maximum loan amount", "#929599", "", commontypes.FontStyle_HEADLINE_XS),
			Money:        typesPb.GetFromBeMoney(offerAmount),
			InterestRate: helper.GetText(fmt.Sprintf("@ %.2f%% interest", interestRate), "#2D5E6E", "#E4F1F5", commontypes.FontStyle_SUBTITLE_S),
		},
		PartnershipTextWithIcon: &commontypes.TextWithIcon{
			Text:    helper.GetText("Powered by", "#929599", "", commontypes.FontStyle_HEADLINE_XS),
			IconUrl: "https://epifi-icons.pointz.in/loans/federalBankLogo",
		},
		BgColor: "#EFF2F6",
		NextNavigationCta: &deeplinkPb.Cta{
			Deeplink: rtd.GetLoanAmountSelectorScreen(ctx, lh, lo),
		},
		NextNavigationIntervalSeconds: 2,
		BackgroundImage: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: "https://epifi-icons.pointz.in/preapprovedloan/fl-eligibility-success-bg-card.png",
					},
					Properties: &commontypes.VisualElementProperties{
						Width:  394,
						Height: 436,
					},
				},
			},
		},
	})
}

//nolint:dupl,funlen
func (p *RealTimeFedProvider) GetIntroScreenForWebViewDeeplink(lh *palFeEnumsPb.LoanHeader, loanRequestId string, entryUrl string, retryBackoff uint32, platform commontypes.Platform) (*deeplinkPb.Deeplink, error) {
	webviewDl, webviewDlErr := p.GetWebViewScreenDeeplink(lh, loanRequestId, entryUrl, retryBackoff, platform)
	if webviewDlErr != nil {
		return nil, errors.Wrap(webviewDlErr, "error getting fed-aa setu webview screen deeplink")
	}

	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_GENERIC_INTRO_SCREEN, &palTypesPb.LoansGenericIntroScreen{
		LoanHeader: lh,
		TopComponent: &widget.VisualElementTitleSubtitleElement{
			VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/atl-pen-pad.png").WithProperties(&commontypes.VisualElementProperties{
				Width:  166,
				Height: 156,
			}),
			TitleText: commontypes.GetTextFromStringFontColourFontStyle(
				"Verify your income for a loan offer",
				"#313234",
				commontypes.FontStyle_HEADLINE_1,
			),
		},
		Consents: []*widget.CheckboxItem{
			{
				Id:          "consent_to_agree_sharing_banks_income_with_federal",
				DisplayText: helper.GetText("I hereby provide my consent for collection of my personal data and sharing and storing the data with Federal bank collected through FI. The consent will allow Federal Bank and epifi Tech to preserve audit trials and logs of entire onboarding journey for this process.", "8D8D8D", "", commontypes.FontStyle_BODY_3_PARA),
				IsChecked:   false,
			},
			{
				Id:          "consent_to_agree_sharing_credit_report_to_federal_for_fetch",
				DisplayText: helper.GetText("I hereby appoint Federal Bank as authorized representative to receive my credit information from CIBIL/Experian and agree to the privacy policy of Federal bank", "8D8D8D", "", commontypes.FontStyle_BODY_3_PARA),
				IsChecked:   false,
			},
		},
		Components: []*palTypesPb.LoansGenericIntroScreen_Component{
			{
				Component: &palTypesPb.LoansGenericIntroScreen_Component_VerticalList{
					VerticalList: &palTypesPb.KeyValueRowsComponent{
						BgColor: &widget.BackgroundColour{
							Colour: &widget.BackgroundColour_BlockColour{
								BlockColour: "#E4F1F5",
							},
						},
						Heading: &ui.IconTextComponent{
							Texts: []*commontypes.Text{
								helper.GetText("How to verify?", "#FFFFFF", "#2D5E6E", commontypes.FontStyle_SUBTITLE_XS),
							},
							ContainerProperties: &ui.IconTextComponent_ContainerProperties{
								CornerRadius:  20,
								BgColor:       "#2D5E6E",
								LeftPadding:   40,
								RightPadding:  40,
								TopPadding:    10,
								BottomPadding: 10,
							},
						},
						ItemList: &palTypesPb.KeyValueRowsComponent_ItemList{
							Items: []*palTypesPb.KeyValueRow{
								{
									Key: &ui.IconTextComponent{
										ContainerProperties: &ui.IconTextComponent_ContainerProperties{
											CornerRadius:  30,
											BgColor:       "#FFFFFF",
											LeftPadding:   8,
											RightPadding:  8,
											TopPadding:    8,
											BottomPadding: 8,
											Height:        40,
											Width:         40,
										},
										LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/loans/intro_password.png").WithProperties(&commontypes.VisualElementProperties{Width: 24, Height: 24}),
									},
									Value: &ui.IconTextComponent{
										Texts: []*commontypes.Text{
											helper.GetText("Login to Setu & select your salary account for verification", "313234", "", commontypes.FontStyle_HEADLINE_S),
										},
									},
									BgColor: "#E4F1F5B2",
								},
								{
									Key: &ui.IconTextComponent{
										ContainerProperties: &ui.IconTextComponent_ContainerProperties{
											CornerRadius:  30,
											BgColor:       "#FFFFFF",
											LeftPadding:   8,
											RightPadding:  8,
											TopPadding:    8,
											BottomPadding: 8,
											Height:        40,
											Width:         40,
										},
										LeftVisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/loans/intro_tickMark.png").WithProperties(&commontypes.VisualElementProperties{Width: 24, Height: 24}),
									},
									Value: &ui.IconTextComponent{
										Texts: []*commontypes.Text{
											helper.GetText("Approve data sharing to verify your eligibilty", "313234", "", commontypes.FontStyle_HEADLINE_S),
										},
									},
									BgColor: "#E4F1F5B2",
								},
							},
							ShowSeparator: true,
						},
					},
				},
			},
			{
				Component: &palTypesPb.LoansGenericIntroScreen_Component_PartnershipLogo{
					PartnershipLogo: &palTypesPb.VisualElementComponent{
						VisualElement: &commontypes.VisualElement{
							Asset: &commontypes.VisualElement_Image_{
								Image: &commontypes.VisualElement_Image{
									ImageType: commontypes.ImageType_PNG,
									Source: &commontypes.VisualElement_Image_Url{
										Url: "https://epifi-icons.pointz.in/loans/powered_by_setu.png",
									},
									Properties: &commontypes.VisualElementProperties{
										Height: 12,
										Width:  98,
									},
								},
							},
						},
						TopMargin: 16,
					},
				},
			},
		},
		Cta: &deeplinkPb.Button{
			Text:        helper.GetText("Verify income", "#FFFFFF", "#00BB98", commontypes.FontStyle_BUTTON_M),
			WrapContent: false,
			Padding: &deeplinkPb.Button_Padding{
				LeftPadding:   24,
				RightPadding:  24,
				TopPadding:    12,
				BottomPadding: 12,
			},
			Margin: &deeplinkPb.Button_Margin{
				LeftMargin:   24,
				RightMargin:  24,
				BottomMargin: 16,
			},
			Cta: &deeplinkPb.Cta{
				Text:         "Verify income",
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
				Deeplink:     webviewDl,
			},
		},
	})
	return dl, dlErr
}

// nolint: dupl
func (a *RealTimeFedProvider) GetWebViewScreenDeeplink(lh *palFeEnumsPb.LoanHeader, loanRequestId string, entryUrl string, backOff uint32, platform commontypes.Platform) (*deeplinkPb.Deeplink, error) {
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN, &palTypesPb.LoansWebviewWithStatusPollScreen{
		LoanHeader:          lh,
		EntryUrl:            entryUrl,
		RetryBackoff:        backOff,
		PageTitle:           commontypes.GetPlainStringText("consent").WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#313234"),
		LoanRequestId:       loanRequestId,
		AnalyticsScreenName: analytics.AnalyticsScreenName_LOANS_WEB_VIEW_WITH_POLL_SCREEN,
		ExitUrls:            []string{"https://fi.money/d2VhbHRoLWFhZGhhYXItZS1zaWdu"},
	})
}

func (p *RealTimeFedProvider) GetEmploymentDetailsDeeplink(lh *palFeEnumsPb.LoanHeader, loanRequestId string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_EMPLOYMENT_DETAILS_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedEmploymentDetailsScreenOptions{
			PreApprovedEmploymentDetailsScreenOptions: &deeplinkPb.PreApprovedEmploymentDetailsScreenOptions{
				HeaderInfoItem: &deeplinkPb.InfoItem{
					Title: "Tell us more about you",
					Desc:  "This will help us evaluate your scope to repay the loan.",
				},
				ContinueCta: &deeplinkPb.Cta{
					Type:     deeplinkPb.Cta_CUSTOM,
					Text:     "Continue",
					Deeplink: p.GetLoanApplicationStatusPollScreenDeepLink(lh, loanRequestId),
				},
				LoanReqId:                  loanRequestId,
				LoanHeader:                 lh,
				ToolbarTitle:               helper.GetText("Instant Loan", "#383838", "", commontypes.FontStyle_HEADLINE_2),
				AddEmploymentDetailsInSync: true,
			},
		},
	}
}

func (a *RealTimeFedProvider) GetAddressConfirmationScreen(lh *palFeEnumsPb.LoanHeader, loanReqId string, customerName string) *deeplinkPb.Deeplink {
	baseDl := a.Provider.GetAddressConfirmationScreen(lh, loanReqId, customerName)
	baseDl.GetPreApprovedAddressConfirmationScreenOptions().CustomerNameText = helper.GetText(customerName, "#313234", "#EFF2F6", commontypes.FontStyle_BODY_S)
	baseDl.GetPreApprovedAddressConfirmationScreenOptions().UseAddressCheckbox = nil
	baseDl.GetPreApprovedAddressConfirmationScreenOptions().ConfirmationText = ""
	baseDl.GetPreApprovedAddressConfirmationScreenOptions().AddressType = &commontypes.Text{
		FontColor:    "#B9B9B9",
		DisplayValue: &commontypes.Text_PlainString{PlainString: "COMMUNICATION ADDRESS"},
		FontStyle:    &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_OVERLINE_XS_CAPS},
	}
	baseDl.GetPreApprovedAddressConfirmationScreenOptions().ToolbarTitle = nil
	baseDl.GetPreApprovedAddressConfirmationScreenOptions().AddAddressDetailsInSync = true
	return baseDl
}

func (p *RealTimeFedProvider) GetApplicationInProgressScreen(ctx context.Context, lh *palFeEnumsPb.LoanHeader, actorId string) (*deeplinkPb.Deeplink, error) {
	screenOptions := &palTypesPb.LoansFailureScreen{
		LoanHeader: lh,
		BackCta: &deeplinkPb.Button{
			Text: helper.GetText("Back to home", "#FFFFFF", "#00B899", commontypes.FontStyle_BUTTON_S),
			Cta: &deeplinkPb.Cta{
				Deeplink:     p.GetLoanLandingInfo(p.GetLoanHeader()),
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_PRIMARY,
			},
		},
		AnalyticsScreenName: analytics.AnalyticsScreenName_LAMF_INSUFFICIENT_FOLIO_ERROR_SCREEN,
		Components: []*palTypesPb.LoansFailureScreenComponent{
			{
				Component: &palTypesPb.LoansFailureScreenComponent_TitleWithImage{
					TitleWithImage: &palTypesPb.VisualElementTitleSubtitleComponent{
						Component: &widget.VisualElementTitleSubtitleElement{
							VisualElement:   commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/close-circle-review-glasses.png").WithProperties(&commontypes.VisualElementProperties{Width: 120, Height: 120}),
							TitleText:       commontypes.GetPlainStringText("Reviewing your loan application").WithFontStyle(commontypes.FontStyle_HEADLINE_XL).WithFontColor("#313234"),
							SubtitleText:    commontypes.GetPlainStringText("This may take up to 2 hours. Once done,we'll let you know.").WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#6A6D70"),
							BackgroundColor: "#FFFFFF",
						},
						TopMargin: 72,
					},
				},
			},
		},
	}
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_FAILURE_SCREEN, screenOptions)
}

func (p *RealTimeFedProvider) GetNoOfferScreen(ctx context.Context, lh *palFeEnumsPb.LoanHeader, actorId string) (*deeplinkPb.Deeplink, error) {
	eligibilityScreen, err := p.getLoansCheckEligibilityScreen()
	if err != nil {
		return nil, fmt.Errorf("error while generating eligibility screen deeplink : %w", err)
	}
	screenOptions := &palTypesPb.LoansFailureScreen{
		LoanHeader: lh,
		BackCta: &deeplinkPb.Button{
			Text: helper.GetText("Back to home", "#00B899", "", commontypes.FontStyle_BUTTON_S),
			Cta: &deeplinkPb.Cta{
				Deeplink:     eligibilityScreen,
				Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
				Type:         deeplinkPb.Cta_DONE,
				DisplayTheme: deeplinkPb.Cta_TEXT,
			},
		},
		AnalyticsScreenName: analytics.AnalyticsScreenName_LAMF_INSUFFICIENT_FOLIO_ERROR_SCREEN,
		Components: []*palTypesPb.LoansFailureScreenComponent{
			{
				Component: &palTypesPb.LoansFailureScreenComponent_TitleWithImage{
					TitleWithImage: &palTypesPb.VisualElementTitleSubtitleComponent{
						Component: &widget.VisualElementTitleSubtitleElement{
							VisualElement:   commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/preapprovedloan/fl-eligibility-no-offer.png").WithProperties(&commontypes.VisualElementProperties{Width: 120, Height: 120}),
							TitleText:       commontypes.GetPlainStringText("You don't have an Instant Loan offer now").WithFontStyle(commontypes.FontStyle_HEADLINE_XL).WithFontColor("#313234"),
							SubtitleText:    commontypes.GetPlainStringText("We'll let you know once we have an offer for you").WithFontStyle(commontypes.FontStyle_BODY_S).WithFontColor("#6A6D70"),
							BackgroundColor: "#FFFFFF",
						},
						TopMargin: 72,
					},
				},
			},
		},
	}
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_FAILURE_SCREEN, screenOptions)
}

func (p *RealTimeFedProvider) getLoansCheckEligibilityScreen() (*deeplinkPb.Deeplink, error) {
	return deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_CHECK_ELIGIBILITY_SCREEN, &palTypesPb.LoansCheckEligibilityScreen{
		LoanHeader: p.GetLoanHeader(),
		BgColor:    widget.GetBlockBackgroundColour("#FFFFFF"),
	})
}

func (p *RealTimeFedProvider) GetLoansMandateInitiateScreenV2(ctx context.Context, lh *palFeEnumsPb.LoanHeader, loanRequestId string, lseId string, params *provider.LoansMandateInitiateScreenV2Params) (*deeplinkPb.Deeplink, error) {
	baseDl, baseDlErr := p.Provider.GetLoansMandateInitiateScreenV2(ctx, lh, loanRequestId, lseId, params)
	if baseDlErr != nil {
		return nil, baseDlErr
	}

	screenOptions := &palTypesPb.LoansMandateInitiateV2ScreenOptions{}

	if err := baseDl.GetScreenOptionsV2().UnmarshalTo(screenOptions); err != nil {
		return nil, err
	}

	screenOptions.PartnerLogo = ui.NewITC().WithTexts(helper.GetText("Powered By", "#9DA1A4", "", commontypes.FontStyle_HEADLINE_XS)).
		WithRightVisualElementUrlHeightAndWidth("https://epifi-icons.pointz.in/vkyc/federal-logo-3.png", 16, 64).WithRightImagePadding(6)
	screenOptions.TopComponent = &widget.VisualElementTitleSubtitleElement{
		VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.s3.ap-south-1.amazonaws.com/sg/AutomateCups.png").WithProperties(&commontypes.VisualElementProperties{
			Width:  348,
			Height: 224,
		}),
		TitleText: commontypes.GetTextFromStringFontColourFontStyle(
			"Set up AutoPay for your EMIs",
			"#313234",
			commontypes.FontStyle_HEADLINE_L,
		),
		SubtitleText: commontypes.GetTextFromStringFontColourFontStyle(
			"AutoPay saves you late fees by making your EMI payments on time",
			"#6A6D70",
			commontypes.FontStyle_BODY_S,
		),
	}

	dl, dlErr := deeplinkv3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_MANDATE_INITIATE_SCREEN_V2, screenOptions)
	if dlErr != nil {
		return nil, dlErr
	}

	return dl, nil
}
