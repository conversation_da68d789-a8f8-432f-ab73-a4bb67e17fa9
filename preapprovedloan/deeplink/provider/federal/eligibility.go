package federal

import (
	"fmt"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	feHeaderPb "github.com/epifi/gamma/api/frontend/header"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option"

	"github.com/epifi/gamma/api/consent"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	deeplinkV3 "github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
)

var fenEligibilityConsentPrivacyPolicyUrl = "https://www.federalbank.co.in/privacy-policy"

type EligibilityProvider struct {
	*EtbFedProvider
}

var _ provider.IDeeplinkProvider = &EligibilityProvider{}

func NewEligibilityProvider(etbFedProvider *EtbFedProvider) *EligibilityProvider {
	return &EligibilityProvider{
		etbFedProvider,
	}
}

func (s *EligibilityProvider) GetLoanHeader() *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY,
		Vendor:      palFeEnumsPb.Vendor_EPIFI_TECH,
		DataOwner:   palFeEnumsPb.Vendor_FEDERAL_BANK,
	}
}

func (s *EligibilityProvider) GetPreBreLoanConsentScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string) (*deeplinkPb.Deeplink, error) {
	lh.DataOwner = palFeEnumsPb.Vendor_FEDERAL_BANK
	return deeplinkV3.GetDeeplinkV3(deeplinkPb.Screen_LOANS_CONSENT_V2_SCREEN, &palTypesPb.LoansConsentV2ScreenOptions{
		Header: &deeplink_screen_option.ScreenOptionHeader{FeedbackEngineInfo: &feHeaderPb.FeedbackEngineInfo{
			FlowIdDetails: &feHeaderPb.FlowIdentifierDetails{
				FlowIdentifierType: typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     typesPb.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_CONSENT_V2_SCREEN.String(),
			},
		}},
		LoanHeader:    lh,
		Flow:          palFeEnumsPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY,
		LoanRequestId: loanRequestId,
		TopIcon:       commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/landing_federal_logo.png", 18, 72),
		Title:         helper.GetText("Confirm the following to apply for a loan", "#333333", "", commontypes.FontStyle_HEADLINE_L),
		Subtitle:      helper.GetText("Mandatory Step for Loan application evaluation", "#8D8D8D", "", commontypes.FontStyle_BODY_3),
		ConsentItems: []*widget.CheckboxItem{
			{
				Id:          consent.ConsentType_CONSENT_TYPE_CREDIT_REPORT_PULL_BY_FEDERAL_LENDER.String(),
				DisplayText: helper.GetTextWithHtml(fmt.Sprintf("I hereby appoint Federal Bank as authorised representative to receive my credit information from CIBIL/Experian and agree to the <a href=\"%s\" style=\"color: #00B899\">privacy policy</a>\n</font> of Federal bank.", fenEligibilityConsentPrivacyPolicyUrl), "#333333", commontypes.FontStyle_BODY_4),
			},
			{
				Id:          consent.ConsentType_CONSENT_TYPE_DATA_SHARING_WITH_FEDERAL.String(),
				DisplayText: helper.GetText("I hereby provide my consent for collection of my personal data and sharing and storing the data with Federal Bank collected through Fi Money Application. The consent will allow Federal Bank and Epifi Technologies Pvt Ltd to preserve the audit trails and logs of entire onboarding journey for this process.", "#333333", "", commontypes.FontStyle_BODY_4),
			},
		},
		Cta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CUSTOM,
			Text:         "Confirm",
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
		},
		BgColor:        "#FFFFFF",
		ConsentBgColor: colors.ColorOnDarkHighEmphasis,
	})
}
