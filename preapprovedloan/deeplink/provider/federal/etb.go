package federal

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/frontend/analytics"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	palTypesPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	dlGenConf "github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/helper"
)

type EtbFedProvider struct {
	*baseprovider.Provider
	dlGenConf *dlGenConf.DeeplinkConfig
}

var _ provider.IDeeplinkProvider = &EtbFedProvider{}

func NewEtbFedProvider(baseProvider *baseprovider.Provider, dlGenConf *dlGenConf.DeeplinkConfig) *EtbFedProvider {
	return &EtbFedProvider{
		Provider:  baseProvider,
		dlGenConf: dlGenConf,
	}
}

const esignWebViewPollRetryBackoffMs = 2000

func (etb *EtbFedProvider) GetLoanHeader() *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Vendor:      palFeEnumsPb.Vendor_FEDERAL_BANK,
	}
}

func (etb *EtbFedProvider) GetLoanHeaderWithEventData(entryPoint palFeEnumsPb.EntryPoint) *palFeEnumsPb.LoanHeader {
	return &palFeEnumsPb.LoanHeader{
		LoanProgram: palFeEnumsPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
		Vendor:      palFeEnumsPb.Vendor_FEDERAL_BANK,
		EventData:   &palFeEnumsPb.EventData{EntryPoint: entryPoint},
	}
}

func (etb *EtbFedProvider) GetInitiateESignScreen(lh *palFeEnumsPb.LoanHeader, loanRequestId string, documentUrl string) *deeplinkPb.Deeplink {
	return &deeplinkPb.Deeplink{
		Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_INITIATE_ESIGN_SCREEN,
		ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanInitiateEsignScreenOptions{
			PreApprovedLoanInitiateEsignScreenOptions: &deeplinkPb.PreApprovedLoanInitiateEsignScreenOptions{
				IconUrl:  "https://epifi-icons.pointz.in/preapprovedloan/esign-initiate.png",
				Title:    "To complete application,\ne-Sign with Aadhaar OTP",
				SubTitle: "Our partner regulated entity requires this for Instant Loan",
				BulletPoints: []string{
					"Review a document that has all your loan details",
					"Enter your Aadhaar number and OTP",
				},
				PartnerIconUrl: "https://epifi-icons.pointz.in/preapprovedloan/esign-initiate-partnership.png",
				Continue: &deeplinkPb.Cta{
					Type:         deeplinkPb.Cta_CUSTOM,
					Text:         "Continue",
					DisplayTheme: deeplinkPb.Cta_PRIMARY,
					Status:       deeplinkPb.Cta_CTA_STATUS_ENABLED,
					Deeplink: deeplinkv3.GetDeeplinkV3WithoutError(deeplinkPb.Screen_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN, &palTypesPb.LoansWebviewWithStatusPollScreen{
						LoanHeader:              lh,
						EntryUrl:                documentUrl,
						RetryBackoff:            esignWebViewPollRetryBackoffMs,
						LoanRequestId:           loanRequestId,
						PageTitle:               commontypes.GetPlainStringText("e-Sign").WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor("#313234"),
						ExitUrls:                []string{etb.dlGenConf.FedKfsExitUrl()},
						ExitDelayInMilliseconds: 2000,
						AnalyticsScreenName:     analytics.AnalyticsScreenName_LOANS_ESIGN_WEBVIEW,
						ShouldOpenInExternalTab: true,
					}),
				},
				LoanRequestId:   loanRequestId,
				BottomSheetText: "Next: You’ll see your loan agreement",
				LoanHeader:      lh,
				ToolbarTitle:    helper.GetText("Instant Loan", "#383838", "", commontypes.FontStyle_HEADLINE_2),
			}},
	}
}
