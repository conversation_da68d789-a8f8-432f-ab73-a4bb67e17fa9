//nolint:goimports
package epifitech

import (
	"fmt"
	"time"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palCommonNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	gammaPalNs "github.com/epifi/gamma/preapprovedloan/workflow/namespace"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"

	"github.com/google/uuid"
	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"
)

const IncomeEstimationMaxRetryCount = 3

type SyncIncomeEstimation struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

// Todo(Anupam): Move to new income estimationa and remove this
func NewSyncIncomeEstimation() *SyncIncomeEstimation {
	return &SyncIncomeEstimation{}
}

var _ stages.IStage = &SyncIncomeEstimation{}

func (si *SyncIncomeEstimation) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	lg := workflow.GetLogger(ctx)
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
		LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
			palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		},
	}
	lseStatus := palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
	retryCount := 0

	for lseStatus != palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS {
		if retryCount >= IncomeEstimationMaxRetryCount {
			lg.Error(fmt.Sprintf("income estimation retry exhausted. RetryCount: %v, MaxRetryCount: %v", retryCount, IncomeEstimationMaxRetryCount))
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
			res.GetLoanStep().SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_INCOME_CANNOT_BE_ESTIMATED
			res.LseFieldMasks = append(res.LseFieldMasks,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
			)
			break
		}
		retryCount += 1

		actReq := providers.GetActivityRequest(req, req.LoanProgram)
		actRes := &palActivityPb.PalActivityResponse{}
		// update orchid for each retry
		// with this approach we will lose the previous orchid against which we have done the salary estimation
		res.GetLoanStep().OrchId = uuid.New().String()
		if res.GetLoanStep().GetDetails().GetIncomeEstimateData() == nil {
			res.GetLoanStep().GetDetails().Details = &palPb.LoanStepExecutionDetails_IncomeEstimateData{IncomeEstimateData: &palPb.IncomeEstimateData{}}
		}
		res.GetLoanStep().GetDetails().GetIncomeEstimateData().AttemptOrchIdList =
			append(res.GetLoanStep().GetDetails().GetIncomeEstimateData().GetAttemptOrchIdList(), res.GetLoanStep().GetOrchId())
		res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
		actErr := activityPkg.Execute(ctx, palCommonNs.UpdateLoanStepExecution, actRes, &palActivityPb.UpdateLoanStepExecutionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanStep: res.GetLoanStep(),
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_ORCH_ID,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			},
		})
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), nil, res, actErr, false) {
			return res, actErr
		}
		res.LoanStep = actRes.GetLoanStep()

		// estimate salary against the updated orchid
		actRes, actErr = providers.PerformActionInSyncV2(ctx, &providers.SyncPolledStageRequestV2{
			ActivityName:           gammaPalNs.EstimateSalary,
			ActivityRequest:        actReq,
			ExecuteAsyncInParallel: true,
			StageRequest:           req,
			TimeoutDuration:        75 * time.Hour,
		})
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, true) {
			lg.Error("Failed to execute EstimateSalary", zap.Error(actErr), zap.String(logger.STATUS, actRes.GetLoanStep().GetStatus().String()), zap.String(logger.SUB_STATUS, actRes.GetLoanStep().GetSubStatus().String()))
		}
		lseStatus = actRes.GetLoanStep().GetStatus()
		if lseStatus == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED {
			lg.Info("salary estimation flow cancelled by user, workflow retry skipped", zap.String(logger.STATUS, actRes.GetLoanStep().GetStatus().String()), zap.String(logger.SUB_STATUS, actRes.GetLoanStep().GetSubStatus().String()))
			break
		}
	}
	return res, nil
}

func (si *SyncIncomeEstimation) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_INCOME_ESTIMATION
}

func (si *SyncIncomeEstimation) GetCelestialStage() epifitemporal.Stage {
	return palCommonNs.StageIncomeVerification
}
