package lenden

import (
	"fmt"

	"go.temporal.io/sdk/workflow"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

type LoanApplicationProvider struct{}

func NewLoanApplicationProvider() *LoanApplicationProvider {
	return &LoanApplicationProvider{}
}

var _ providers.IVendorProvider = &LoanApplicationProvider{}

func (p *LoanApplicationProvider) GetStages(ctx workflow.Context, req *providers.GetStagesRequest) (*providers.GetStagesResponse, error) {
	res := &providers.GetStagesResponse{}
	res.Stages = make([]stages.IStage, 0)

	switch req.GroupStage {
	case palPb.GroupStage_GROUP_STAGE_DATA_COLLECTION:
		res.Stages = append(res.Stages, NewPreBreLoanDataCollection(), providers.NewAddress())
	case palPb.GroupStage_GROUP_STAGE_OFFER_CREATION:
		res.Stages = append(res.Stages, providers.NewVendorHardOfferCreation(), providers.NewIncomeEstimation(), providers.NewVendorHardOfferCreation(), providers.NewUserOfferSelectionStage())
	case palPb.GroupStage_GROUP_STAGE_INIT_LOAN:
		res.Stages = append(res.Stages, NewInitLoan())
	case palPb.GroupStage_GROUP_STAGE_KYC:
		res.Stages = append(res.Stages, NewKyc())
	case palPb.GroupStage_GROUP_STAGE_MANDATE:
		res.Stages = append(res.Stages, NewMandate())
	case palPb.GroupStage_GROUP_STAGE_E_SIGN:
		res.Stages = append(res.Stages, NewESign())
	case palPb.GroupStage_GROUP_STAGE_DISBURSAL:
		res.Stages = append(res.Stages, NewModifyRoi(), NewReSign(), NewDisbursal())
	default:
		return nil, fmt.Errorf("group stage not implemented by LDC vendor provider, groupStage: %v", req.GroupStage)
	}
	return res, nil
}

func (p *LoanApplicationProvider) GetGroupStages(ctx workflow.Context, _ *providers.GetGroupStagesRequest) (*providers.GetGroupStagesResponse, error) {
	res := &providers.GetGroupStagesResponse{}
	if v := workflow.GetVersion(ctx, "lenden-application-movement", workflow.DefaultVersion, 1); v >= 1 {
		res.GroupStages = []palPb.GroupStage{
			palPb.GroupStage_GROUP_STAGE_DATA_COLLECTION,
			palPb.GroupStage_GROUP_STAGE_OFFER_CREATION,
			palPb.GroupStage_GROUP_STAGE_INIT_LOAN,
			palPb.GroupStage_GROUP_STAGE_KYC,
			palPb.GroupStage_GROUP_STAGE_MANDATE,
			palPb.GroupStage_GROUP_STAGE_E_SIGN,
			palPb.GroupStage_GROUP_STAGE_DISBURSAL,
		}
	} else {
		res.GroupStages = []palPb.GroupStage{
			palPb.GroupStage_GROUP_STAGE_INIT_LOAN,
			palPb.GroupStage_GROUP_STAGE_KYC,
			palPb.GroupStage_GROUP_STAGE_MANDATE,
			palPb.GroupStage_GROUP_STAGE_E_SIGN,
			palPb.GroupStage_GROUP_STAGE_DISBURSAL,
		}
	}

	return res, nil
}
