package federal

import (
	"fmt"
	"time"

	"go.temporal.io/sdk/workflow"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palNamespace "github.com/epifi/gamma/preapprovedloan/workflow/namespace"
	"github.com/epifi/gamma/preapprovedloan/workflow/providers"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

type FederalKfsStage struct {
	*stages.CommonPreProcessStage
	*stages.CommonPostProcessStage
}

var _ stages.IStage = &FederalKfsStage{}

func NewFederalKfsStage() *FederalKfsStage {
	return &FederalKfsStage{}
}

func (f *FederalKfsStage) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	checkKfsReq := &palActivityPb.PalActivityRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.Request.GetLoanStep().GetOrchId(),
		},
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	checkKfsRes := &palActivityPb.PalActivityResponse{}
	kfsInSyncWfVersion := workflow.GetVersion(ctx, "fed-kfs-sync", workflow.DefaultVersion, 1)
	if kfsInSyncWfVersion == 1 {
		actRes, actErr := providers.PerformActionInSyncV2(ctx, &providers.SyncPolledStageRequestV2{
			ActivityName:           palNamespace.FedKfsInSync,
			ActivityRequest:        checkKfsReq,
			ExecuteAsyncInParallel: true,
			StageRequest:           req,
			TimeoutDuration:        72 * time.Hour,
		})
		if providers.IsActivityError(ctx, actRes.GetLoanStep(), actRes.GetLseFieldMasks(), res, actErr, true) {
			return res, actErr
		}
	} else {
		checkKfsErr := activityPkg.Execute(ctx, palNs.CheckKfsStatusV2, checkKfsRes, checkKfsReq)
		expireLse := false
		wfVersion := workflow.GetVersion(ctx, "expire-lse-CheckKfsStatusV2", workflow.DefaultVersion, 1)
		if wfVersion == 1 {
			expireLse = true
		}
		if providers.IsActivityError(ctx, checkKfsRes.GetLoanStep(), nil, res, checkKfsErr, expireLse) {
			return res, fmt.Errorf("failed to CheckKfsStatus activity, %w", checkKfsErr)
		}
	}

	res.LoanStep = checkKfsRes.LoanStep
	res.LseFieldMasks = checkKfsRes.LseFieldMasks
	return res, nil
}

func (f *FederalKfsStage) GetName() palpb.LoanStepExecutionStepName {
	return palpb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS
}

func (f *FederalKfsStage) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageESign
}
