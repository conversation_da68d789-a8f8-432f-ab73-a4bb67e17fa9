//nolint:goimports
package providers

import (
	"go.temporal.io/sdk/workflow"

	"github.com/epifi/be-common/pkg/epifitemporal"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/namespace"
	"github.com/epifi/gamma/preapprovedloan/workflow/stages"
)

type IncomeEstimation struct {
	*stages.HardOfferSkipApplicationStagePreProcess
	*stages.CommonPostProcessStage
}

func NewIncomeEstimation() *IncomeEstimation {
	return &IncomeEstimation{}
}

var _ stages.IStage = &IncomeEstimation{}

func (aa *IncomeEstimation) Perform(ctx workflow.Context, req *stages.PerformRequest) (*stages.PerformResponse, error) {
	res := &stages.PerformResponse{
		LoanStep: req.Request.GetLoanStep(),
	}

	actReq := &palActivityPb.PalActivityRequest{
		LoanStep:    req.Request.GetLoanStep(),
		Vendor:      req.Vendor,
		LoanProgram: req.LoanProgram,
	}
	actRes := &palActivityPb.PalActivityResponse{}
	actErr := activityPkg.Execute(ctx, namespace.IncomeEstimation, actRes, actReq)
	if IsActivityError(ctx, actRes.GetLoanStep(), nil, res, actErr, true) {
		return res, actErr
	}
	// move this inside sync proxy.
	ver := workflow.GetVersion(ctx, "reset-actres-after-activity-execution", workflow.DefaultVersion, 1)
	if ver != workflow.DefaultVersion {
		actRes = &palActivityPb.PalActivityResponse{}
	}
	actErr = activityPkg.Execute(ctx, namespace.IncomeEstimationStatus, actRes, actReq)
	if IsActivityError(ctx, actRes.GetLoanStep(), nil, res, actErr, true) {
		return res, actErr
	}

	res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	return res, nil
}

func (ca *IncomeEstimation) GetName() palPb.LoanStepExecutionStepName {
	return palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_INCOME_ESTIMATION
}

func (ca *IncomeEstimation) GetCelestialStage() epifitemporal.Stage {
	return palNs.StageAaDataFetch
}
