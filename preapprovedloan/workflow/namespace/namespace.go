// nolint:gosec
package namespace

import "github.com/epifi/be-common/pkg/epifitemporal"

// All new activities and stages in loans will be registered here and all previous ones which are in be common should be moved
// here.
const (
	// stages
	StagePanVerification    epifitemporal.Stage = "PanVerification"
	StageReSign             epifitemporal.Stage = "ReSign"
	StageUserOfferSelection epifitemporal.Stage = "UserOfferSelection"
	StageProfileValidation  epifitemporal.Stage = "ProfileValidation"

	IncomeEstimation              epifitemporal.Activity = "IncomeEstimation"
	IncomeEstimationStatus        epifitemporal.Activity = "IncomeEstimationStatus"
	LdcDisbursal                  epifitemporal.Activity = "LdcDisbursal"
	AbflRegisterPwaUser           epifitemporal.Activity = "AbflRegisterPwaUser"
	AbflPushDataToVendor          epifitemporal.Activity = "AbflPushDataToVendor"
	FetchAndSetAbflPwaJourneyLink epifitemporal.Activity = "FetchAndSetAbflPwaJourneyLink"
	AbflPwaCreateLoanAccount      epifitemporal.Activity = "AbflPwaCreateLoanAccount"
	CreditReportFetch             epifitemporal.Activity = "CreditReportFetch"
	// LoanDataCollection workflow
	FiPreBreV2 epifitemporal.Activity = "FiPreBreV2"

	// SG activities
	UpdateBankingDetailsAtSg   epifitemporal.Activity = "UpdateBankingDetailsAtSg"
	SgGetOTPVerificationStatus epifitemporal.Activity = "SgGetOTPVerificationStatus"
	SgKycDocumentDownload      epifitemporal.Activity = "SgKycDocumentDownload"
	SgKycDataVerification      epifitemporal.Activity = "SgKycDataVerification"

	// Data collection
	WaitForDataCollection epifitemporal.Activity = "WaitForDataCollection"

	// Fed Activities
	FedBreConsentStatus          epifitemporal.Activity = "FedBreConsentStatus"
	FedInitiatePennyDrop         epifitemporal.Activity = "FedInitiatePennyDrop"
	FedPennyDropStatus           epifitemporal.Activity = "FedPennyDropStatus"
	FedSetupMandateIntroScreen   epifitemporal.Activity = "FedSetupMandateIntroScreen"
	FedSetupPennyDropIntroScreen epifitemporal.Activity = "FedSetupPennyDropIntroScreen"
	FedGetMandateStatus          epifitemporal.Activity = "FedGetMandateStatus"
	FedKfsInSync                 epifitemporal.Activity = "FedKfsInSync"

	// common activities
	IsFiCoreUser            epifitemporal.Activity = "IsFiCoreUser"
	ExecuteOnboardingStage  epifitemporal.Activity = "ExecuteOnboardingStage"
	UpdateSyncStatus        epifitemporal.Activity = "UpdateSyncStatus"
	VendorHardOfferCreation epifitemporal.Activity = "VendorHardOfferCreation"
	UserOfferSelection      epifitemporal.Activity = "UserOfferSelection"
	CheckHardOffer          epifitemporal.Activity = "CheckHardOffer"

	IsBasicAddressCollected epifitemporal.Activity = "IsBasicAddressCollected"
	IsFeatureEnabled        epifitemporal.Activity = "IsFeatureEnabled"
	// LDC activities

	LDCSignKFSLADocs        epifitemporal.Activity = "LDCSignKFSLADocs"
	LDCAllowROIModification epifitemporal.Activity = "LDCAllowROIModification"
	LDCInitiateReKfsLaEsign epifitemporal.Activity = "LDCInitiateReKfsLaEsign"
	LDCPaymentStatusCheck   epifitemporal.Activity = "LDCPaymentStatusCheck"

	// LDC Stages
	StageLDCAllowROIModification epifitemporal.Stage = "LDCAllowROIModification"

	// signals
	RoiModificationSignal epifitemporal.Signal = "RoiModificationSignal"

	// common activities
	EstimateSalary epifitemporal.Activity = "EstimateSalary"
)
