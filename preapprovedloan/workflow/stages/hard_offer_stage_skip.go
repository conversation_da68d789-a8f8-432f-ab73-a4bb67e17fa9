package stages

import (
	"fmt"

	"go.temporal.io/sdk/workflow"
	"go.uber.org/zap"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/workflow/namespace"
)

type HardOfferSkipApplicationStagePreProcess struct{}

func (hosas *HardOfferSkipApplicationStagePreProcess) PreProcess(ctx workflow.Context, req *PreProcessRequest) (*PreProcessResponse, error) {
	lg := workflow.GetLogger(ctx)
	commonPP := &CommonPreProcessStage{}
	res, err := commonPP.PreProcess(ctx, req)
	if err != nil {
		lg.Error("common PreProcess failed, err: ", err)
		return nil, fmt.Errorf("common PreProcess failed: %w", err)
	}
	wfVersionCheckHardOfferPreProcess := workflow.GetVersion(ctx, "new-check-hard-offer-pre-process", workflow.DefaultVersion, 1)
	// if old version, return after common implementation
	if wfVersionCheckHardOfferPreProcess == workflow.DefaultVersion {
		return res, nil
	}

	// Todo(Anupam): Remove after the lenden workflows moved to the new version
	if req.GetVendor() == palpb.Vendor_LENDEN.String() {
		wfVersionCheckHardOfferPreProcess = workflow.GetVersion(ctx, "lenden-new-check-hard-offer-pre-process", workflow.DefaultVersion, 1)
		// if old version, return after common implementation
		if wfVersionCheckHardOfferPreProcess == workflow.DefaultVersion {
			return res, nil
		}
	}

	hardOfferReq := &palActivityPb.CheckHardOffersRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		ActorId:    res.GetLoanStep().GetActorId(),
		LoanHeader: req.GetLoanHeader(),
	}

	hardOfferRes := &palActivityPb.CheckHardOffersResponse{}
	err = activityPkg.Execute(ctx, namespace.CheckHardOffer, hardOfferRes, hardOfferReq)
	if err != nil {
		lg.Error("CheckHardOffer activity failed", zap.Error(err))
		return nil, fmt.Errorf("CheckHardOffer activity failed: %w", err)
	}
	if hardOfferRes.GetHardOfferExists() {
		lse := res.GetLoanStep()
		lse.Status = palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		lse.SubStatus = palpb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
		updateReq := &palActivityPb.UpdateLoanStepExecutionRequest{
			RequestHeader: &activityPb.RequestHeader{
				ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
				Ownership:   epificontext.OwnershipFromContext(ctx),
			},
			LoanStep: lse,
			LseFieldMasks: []palpb.LoanStepExecutionFieldMask{
				palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
				palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
			},
		}
		updateRes := &palActivityPb.PalActivityResponse{}
		updateErr := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLoanStepExecution, updateRes, updateReq)
		if updateErr != nil {
			lg.Error("UpdateLoanStepExecution activity failed", zap.Error(updateErr))
			return nil, fmt.Errorf("UpdateLoanStepExecution activity failed: %w", updateErr)
		}
	}

	return res, nil
}
