//nolint:govet,ineffassign,goimports,staticcheck
package p2p

import (
	"context"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	emptyPb "google.golang.org/protobuf/types/known/emptypb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/datacollectors"
	rpcHelper "github.com/epifi/gamma/preapprovedloan/helper"
)

type PreBreLoanPreferencesSaver struct {
	loanRequestsDao dao.LoanRequestsDao
	loecDao         dao.LoanOfferEligibilityCriteriaDao
	rpcHelper       *rpcHelper.RpcHelper
}

func NewPreBreLoanPreferencesSaver(loanRequestsDao dao.LoanRequestsDao, loecDao dao.LoanOfferEligibilityCriteriaDao, rpcHelper *rpcHelper.RpcHelper) *PreBreLoanPreferencesSaver {
	return &PreBreLoanPreferencesSaver{
		loanRequestsDao: loanRequestsDao,
		loecDao:         loecDao,
		rpcHelper:       rpcHelper,
	}
}

var PreBreLoanPreferencesSaverWireSet = wire.NewSet(NewPreBreLoanPreferencesSaver, wire.Bind(new(datacollectors.LoanPreferencesSaver), new(*PreBreLoanPreferencesSaver)))

func (s *PreBreLoanPreferencesSaver) SaveLoanPreferences(ctx context.Context, req *datacollectors.SaveLoanPreferencesRequest) error {
	// context for loan requests with ownership epifi tech.
	ctxEpifiTech := epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH_V2)
	ctxVendor := epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
	reqData := palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_UNSPECIFIED
	switch req.Vendor {
	case palPb.Vendor_LENDEN:
		ctxVendor = epificontext.WithOwnership(ctx, commontypes.Ownership_LOANS_LENDEN)
		reqData = palPb.DataRequirementType_DATA_REQUIREMENT_TYPE_LENDEN_PRE_BRE_LOAN_DATA_COLLECTION
	default:
		return errors.Errorf("unsupported vendor %s", req.Vendor)
	}
	lr, err := s.loanRequestsDao.GetById(ctxEpifiTech, req.LoanRequestID)
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return s.SaveLoanPreferencesApplication(ctx, req)
		}
		return errors.Wrap(err, "failed to get loan requests")
	}
	// get loecs to update
	loecs, err := s.loecDao.GetByActorIdAndVendorAndStatus(ctxVendor, lr.GetActorId(), req.Vendor, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, false)
	if err != nil {
		return errors.Wrap(err, "failed to get loec")
	}
	// for all loecs that need the data update them with the data
	updatedLoecs := make([]*palPb.LoanOfferEligibilityCriteria, 0)
	for _, loec := range loecs {
		for _, data := range loec.GetDataRequirementDetails().GetDataRequirements() {
			if data.GetDataRequirementType() == reqData {
				data.Data = &palPb.DataRequirement_PreBreDataLoanPreferences_{
					PreBreDataLoanPreferences: &palPb.DataRequirement_PreBreDataLoanPreferences{
						LoanAmount: req.AmountRequested,
						// Note: For Lenden, this is expected to be an yearly interest rate percentage value
						Interest: int64(req.InterestRate),
					},
				}
				data.IsCollected = true
				updatedLoecs = append(updatedLoecs, loec)
			}
		}
	}
	// update loecs
	for _, loec := range updatedLoecs {
		if err := s.loecDao.Update(ctxVendor, loec, []palPb.LoanOfferEligibilityCriteriaFieldMask{
			palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_DATA_REQUIREMENT_DETAILS,
		}); err != nil {
			return errors.Wrap(err, "failed to update loec")
		}
	}

	// setting next action to poll screen for fixing race condition where we are seeing consent screen again after submitting the consent.
	lr.NextAction = req.NextAction
	if err = s.loanRequestsDao.Update(ctxEpifiTech, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION}); err != nil {
		return errors.Wrap(err, "failed to update loan request next action")
	}

	// signal workflow
	marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&emptyPb.Empty{})
	if err != nil {
		logger.Error(ctx, "failed to marshal signal with empty", zap.Error(err))
		return errors.Wrap(err, "failed to marshal signal with empty signal")
	}
	if signalErr := s.rpcHelper.SendSignalSync(ctxVendor, lr.GetOrchId(), string(palNs.PreBreLoanDataCollectionSignal), marshalledPayload); signalErr != nil {
		logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
		// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
	}
	return nil
}

// Todo(Anupam): refactor
func (s *PreBreLoanPreferencesSaver) SaveLoanPreferencesApplication(ctx context.Context, req *datacollectors.SaveLoanPreferencesRequest) error {
	ctxVendor := epificontext.WithOwnership(ctx, commontypes.Ownership_EPIFI_TECH)
	switch req.Vendor {
	case palPb.Vendor_LENDEN:
		ctxVendor = epificontext.WithOwnership(ctx, commontypes.Ownership_LOANS_LENDEN)
	default:
		return errors.Errorf("unsupported vendor %s", req.Vendor)
	}

	lr, err := s.loanRequestsDao.GetById(ctxVendor, req.LoanRequestID)
	if err != nil {
		return errors.Wrap(err, "failed to get loan requests")
	}

	if lr.GetDetails().GetLoanApplicationDetails() == nil {
		lr.GetDetails().Details = &palPb.LoanRequestDetails_LoanApplicationDetails{
			LoanApplicationDetails: &palPb.LoanApplicationDetails{
				Details: &palPb.LoanApplicationDetails_LdcLoanApplicationDetails{
					LdcLoanApplicationDetails: &palPb.LdcLoanApplicationDetails{},
				},
			},
		}
	}
	if lr.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().GetPreBreDataLoanPreferences() == nil {
		lr.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().PreBreDataLoanPreferences = &palPb.LdcLoanApplicationDetails_PreBreDataLoanPreferences{
			LoanAmount: req.AmountRequested,
			Interest:   int64(req.InterestRate),
		}
	}

	// setting next action to poll screen for fixing race condition where we are seeing consent screen again after submitting the consent.
	lr.NextAction = req.NextAction
	if err = s.loanRequestsDao.Update(ctxVendor, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS}); err != nil {
		return errors.Wrap(err, "failed to update loan request next action")
	}

	// signal workflow
	marshalledPayload, err := protojson.MarshalOptions{EmitUnpopulated: true}.Marshal(&emptyPb.Empty{})
	if err != nil {
		logger.Error(ctx, "failed to marshal signal with empty", zap.Error(err))
		return errors.Wrap(err, "failed to marshal signal with empty signal")
	}
	if signalErr := s.rpcHelper.SendSignalSync(ctxVendor, lr.GetOrchId(), string(palNs.PreBreLoanDataCollectionSignal), marshalledPayload); signalErr != nil {
		logger.Error(ctx, "failed to send signal to workflow", zap.Error(signalErr))
		// intentionally not returning internal status from here as we have both poll and push mechanism implemented in workflow
	}
	return nil
}
