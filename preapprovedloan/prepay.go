// nolint:funlen
package preapprovedloan

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/celestial/workflow"
	"github.com/epifi/be-common/api/rpc"
	rpcPb "github.com/epifi/be-common/api/rpc"
	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/pay/attribute"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palWorkflowPb "github.com/epifi/gamma/api/preapprovedloan/workflow"
	ldcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	loansPkg "github.com/epifi/gamma/pkg/loans"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	palErrors "github.com/epifi/gamma/preapprovedloan/errors"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	"github.com/epifi/gamma/preapprovedloan/metrics"
	prepayProviders "github.com/epifi/gamma/preapprovedloan/prepay/providers"
)

var (
	// all non-terminal status code applicable in loan closure flow
	nonTerminalLrStatusForLoanClosure = map[palPb.LoanRequestStatus]bool{
		palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED:             true,
		palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED:           true,
		palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING:             true,
		palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION: true,
	}
)

type prepayNotAllowedReason string

const (
	unspecified               prepayNotAllowedReason = "unspecified"
	existingPaymentInProgress prepayNotAllowedReason = "existingPaymentInProgress"
	prepayBlackoutPeriod      prepayNotAllowedReason = "prepayBlackoutPeriod"
	lmsNotUpdatedAtLender     prepayNotAllowedReason = "lmsNotUpdatedAtLender"
	preClosureBlackoutPeriod  prepayNotAllowedReason = "preClosureBlackoutPeriod"
	paymentRequestExpiryTime                         = 10 * time.Minute
)

// nolint:funlen
func (s *Service) PrePayLoanV2(ctx context.Context, req *palPb.PrePayLoanRequest) (*palPb.PrePayLoanResponse, error) {
	res := &palPb.PrePayLoanResponse{}
	metrics.RecordPrePaymentTxn(req.GetAccountType().String(), "STARTED", req.GetLoanHeader().GetVendor().String(), req.GetLoanHeader().GetLoanProgram().String())
	// fetch loan account from db which needs to be prepaid
	loanAccount, err := s.loanAccountsDao.GetById(ctx, req.GetLoanId())
	if err != nil {
		logger.Error(ctx, "error in fetching loan account from db", zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanId()), zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg("error in fetching loan account from db")
		return res, nil
	}

	// if prepay is done for pre closure of loan account
	// we need to check if pre closure is allowed or not
	if req.GetIsPreClose() && !s.isPreClosureAllowed(loanAccount) {
		res.Status = rpc.NewStatusWithoutDebug(uint32(palPb.PrePayLoanResponse_FAILED_PRECONDITION_PRE_CLOSURE_BLACKOUT_PERIOD), string(preClosureBlackoutPeriod))
		return res, nil
	}

	// check if prepayment is allowed or not.
	isPrepayAllowed, notAllowedReason, err := s.isPrepayAllowed(ctx, loanAccount)
	switch {
	case err != nil:
		logger.Error(ctx, "error checking if prepay is allowed", zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanId()), zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg("error checking if prepay is allowed")
		return res, nil
	case !isPrepayAllowed:
		switch notAllowedReason {
		case existingPaymentInProgress:
			res.Status = rpc.NewStatusWithoutDebug(uint32(palPb.PrePayLoanResponse_FAILED_PRECONDITION_EXISTING_PAYMENT_IN_PROGRESS), string(existingPaymentInProgress))
			return res, nil
		case prepayBlackoutPeriod:
			res.Status = rpc.NewStatusWithoutDebug(uint32(palPb.PrePayLoanResponse_FAILED_PRECONDITION_PREPAY_BLACKOUT_PERIOD), string(prepayBlackoutPeriod))
			return res, nil
		case lmsNotUpdatedAtLender:
			res.Status = rpc.NewStatusWithoutDebug(uint32(palPb.PrePayLoanResponse_EXPECTED_DELAY_IN_LMS_UPDATE_AT_VENDOR), string(lmsNotUpdatedAtLender))
			return res, nil
		}
		res.Status = rpc.StatusFailedPreconditionWithDebugMsg("prepayment is not allowed on the account")
		return res, nil
	}

	isPreClose := req.GetIsPreClose()
	loanPaymentRequestType := palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_LUMPSUM
	if isPreClose {
		loanPaymentRequestType = palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE
	}

	if !isPreClose {
		// fetch appropriate pre-closure decider based on loan header and partner lms.
		preClosureDecider, preClosureDeciderErr := s.prepayFactory.GetLoanPreClosureDecider(ctx, req.GetLoanHeader(), loanAccount.GetLmsPartner())
		if preClosureDeciderErr != nil {
			logger.Error(ctx, "failed to get loan pre-closure Decider", zap.Error(preClosureDeciderErr))
			res.Status = rpc.StatusInternalWithDebugMsg("failed to get loan pre-closure Decider")
			return res, nil
		}
		// check if loan should be pre-closed on payment.
		shouldPreClose, preCloseDecisionErr := preClosureDecider.ShouldPreCloseTheLoanOnPayment(ctx, req.GetLoanId(), req.GetAmount())
		switch {
		case preCloseDecisionErr != nil:
			logger.Error(ctx, "failed to check if loan should be pre-closed on payment", zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanId()), zap.Any("prepayAmount", req.GetAmount()), zap.Error(preCloseDecisionErr))
			res.Status = rpc.StatusInternalWithDebugMsg("failed to check if loan should be pre-closed on payment")
			return res, nil
		case shouldPreClose:
			if !s.isPreClosureAllowed(loanAccount) {
				res.Status = rpc.NewStatusWithoutDebug(uint32(palPb.PrePayLoanResponse_FAILED_PRECONDITION_PRE_CLOSURE_BLACKOUT_PERIOD), string(preClosureBlackoutPeriod))
				return res, nil
			}
			logger.Debug(ctx, "loan should be closed on this payment", zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanId()), zap.Any("prepayAmount", req.GetAmount()))
			loanPaymentRequestType = palPb.LoanPaymentRequestType_LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE
			isPreClose = true
		}
	}

	var requestExpiryTime *timestamp.Timestamp
	if req.GetUsePgForPrepay() {
		requestExpiryTime = timestamp.New(time.Now().Add(paymentRequestExpiryTime))
	}
	orchId := uuid.New().String()
	// there is no need to create order request for prepayment if the we are using pg for prepayment.

	if req.GetLoanHeader().GetVendor() != palPb.Vendor_LENDEN {
		lenderPrepayProvider, err := s.prepayFactory.GetLenderPrepayProvider(ctx, req.GetLoanHeader())
		if err != nil {
			logger.Error(ctx, "failed to get lender prepay provider", zap.Any(logger.LOAN_HEADER, req.GetLoanHeader()), zap.Error(err))
			res.Status = rpc.StatusInternalWithDebugMsg("failed to get lender prepay provider")
			return res, nil
		}
		if !req.GetUsePgForPrepay() {
			getOrderRequestRes, err2 := lenderPrepayProvider.GetOrderRequestForPrePayment(ctx,
				&prepayProviders.GetOrderRequestForPrePaymentRequest{
					Amount:                 req.GetAmount(),
					OrchId:                 orchId,
					ActorId:                req.GetActorId(),
					IsPreClose:             isPreClose,
					LoanAccountId:          req.GetLoanId(),
					LoanHeader:             req.GetLoanHeader(),
					PayerUserIdentifier:    req.GetPayerUserIdentifier(),
					LoanPaymentAccountType: req.GetAccountType(),
				})
			if err2 != nil {
				logger.Error(ctx, "failed to create order request for prepayment", zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanId()), zap.Error(err2))
				res.Status = rpc.StatusInternalWithDebugMsg("failed to create order request for prepayment")
				return res, nil
			}

			createFundRes, createFundErr := s.payClient.CreateFundTransferOrder(ctx, getOrderRequestRes.GetOrderRequest())
			if te := epifigrpc.RPCError(createFundRes, createFundErr); te != nil {
				logger.Error(ctx, "CreateFundTransferOrder failed", zap.Error(te))
				if createFundRes.GetPayErrorCodeForPayer() != "" {
					res.Status = rpc.NewStatus(uint32(palPb.PrePayLoanResponse_KNOWN_ERROR_FROM_PAY), "KNOWN_ERROR_FROM_PAY", "")
					res.PayErrorCodeForPayer = createFundRes.GetPayErrorCodeForPayer()
					return res, nil
				}
				res.Status = rpc.StatusInternalWithDebugMsg(fmt.Sprintf("pay CreateFundTransferOrder failed: %v", te.Error()))
				return res, nil
			}
			res.OrderId = createFundRes.GetOrder().GetId()
			res.TransactionAttribute = createFundRes.GetTxnAttributes()[0]

		} else {
			payeeInfo, err2 := lenderPrepayProvider.GetPayeeInfo()
			if err2 != nil {
				logger.Error(ctx, "error in getting payee info", zap.Error(err2), zap.Any(logger.LOAN_HEADER, req.GetLoanHeader()))
				res.Status = rpc.StatusInternalWithDebugMsg("error in getting payee info")
				return res, nil
			}
			res.TransactionAttribute = &attribute.TransactionAttribute{
				PayeePaymentInstrumentId: payeeInfo.PiId,
				PayeeActorName:           payeeInfo.ActorId,
			}
			res.RequestExpiryTime = requestExpiryTime
		}
	}

	// if an order is created on pay side and lpr creation fails, user will see an error and the order on pay side will eventually expire because of missing authorisation
	// that is why it is ok to create lpr after pay side order creation
	// new payments can be blocked if we are blocking new payments if there are existing lprs in created state
	metrics.RecordPrePaymentTxn(req.GetAccountType().String(), "WORKFLOW_STARTED", req.GetLoanHeader().GetVendor().String(), req.GetLoanHeader().GetLoanProgram().String())
	loanPaymentRequest, err := s.loanPaymentRequestDao.Create(ctx, &palPb.LoanPaymentRequest{
		ActorId:     req.GetActorId(),
		AccountId:   req.GetLoanId(),
		OrchId:      orchId,
		AccountType: req.GetAccountType(),
		Amount:      req.GetAmount(),
		Details: &palPb.LoanPaymentRequestDetails{
			RequestExpiryTime: requestExpiryTime,
		},
		Type:      loanPaymentRequestType,
		Status:    palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_CREATED,
		SubStatus: palPb.LoanPaymentRequestSubStatus_LOAN_PAYMENT_REQUEST_SUB_STATUS_UNSPECIFIED,
	})
	if err != nil {
		logger.Error(ctx, "failed to create loan payment request in db", zap.String(logger.LOAN_ACCOUNT_ID, req.GetLoanId()), zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg("failed to create loan payment request in db")
		return res, nil
	}

	payload := &palWorkflowPb.LoanPrePayPayload{
		Vendor:      req.GetLoanHeader().GetVendor(),
		LoanProgram: req.GetLoanHeader().GetLoanProgram(),
		RequestType: loanPaymentRequestType,
	}
	marPayload, err := protojson.Marshal(payload)
	if err != nil {
		logger.Error(ctx, "failed to marshal prepay workflow payload", zap.String(logger.PAYMENT_REQUEST_ID, loanPaymentRequest.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg("failed to marshal prepay workflow payload")
		return res, nil
	}

	if req.GetLoanHeader().GetVendor() == palPb.Vendor_LENDEN {
		paymentLinkProvider, providerErr := s.loanDataProvider.FetchPrePayPaymentLinkProvider(ctx, req.GetLoanHeader())
		if providerErr != nil {
			logger.Error(ctx, "failed to get payment link provider", zap.Error(providerErr), zap.Any(logger.REQUEST, req))
			res.Status = rpc.StatusInternalWithDebugMsg(providerErr.Error())
			return res, nil
		}

		paymentLinkResp, paymentLinkErr := paymentLinkProvider.GetPaymentLink(ctx, &providers.GetPaymentLinkReq{
			LoanId:    loanAccount.GetAccountNumber(),
			TxnAmount: req.GetAmount(),
		})
		if paymentLinkErr != nil {
			logger.Error(ctx, "failed to get redirection link", zap.Error(paymentLinkErr), zap.Any(logger.REQUEST, req))
			res.Status = rpc.StatusInternalWithDebugMsg(paymentLinkErr.Error())
			return res, nil
		}
		res.PaymentUrl = paymentLinkResp.PaymentLink
		res.LoanPaymentRequestId = loanPaymentRequest.GetId()
		loanPaymentRequest.GetDetails().PaymentLink = paymentLinkResp.PaymentLink
		loanPaymentRequest.GetDetails().VendorSpecificDetails = &palPb.LoanPaymentRequestDetails_LendenPaymentDetails{
			LendenPaymentDetails: &palPb.LendenPaymentDetails{
				OrderId: paymentLinkResp.OrderId,
			},
		}
		updateErr := s.loanPaymentRequestDao.Update(ctx, loanPaymentRequest, []palPb.LoanPaymentRequestFieldMask{palPb.LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_DETAILS})
		if updateErr != nil {
			logger.Error(ctx, "failed to update payment link in lpr", zap.Error(updateErr), zap.Any(logger.REQUEST, req))
			res.Status = rpc.StatusInternalWithDebugMsg(updateErr.Error())
			return res, nil
		}
	}

	err = s.initiateWorkflowV2(
		ctx,
		&workflow.ClientReqId{Id: loanPaymentRequest.GetOrchId(), Client: workflow.Client_PRE_APPROVED_LOAN},
		loanAccount.GetActorId(),
		marPayload, celestialPkg.GetTypeEnumFromWorkflowType(palNs.LoanPrePay),
		workflow.Version_V0,
	)
	if err != nil {
		logger.Error(ctx, "failed to initiate loan pre-pay workflow", zap.String(logger.PAYMENT_REQUEST_ID, loanPaymentRequest.GetId()), zap.Error(err))
		res.Status = rpc.StatusInternalWithDebugMsg("failed to initiate loan pre-pay workflow")
		return res, nil
	}

	// TODO: if server crashes here, pre-pay workflow will be initiated but loan closure workflow will not be initiated
	if isPreClose {
		// loan closure workflow is only supported for idfc lender as of now
		// todo (utkarsh) : evaluate if we really need a foreclosure workflow, can't the flow reside within prepay workflow (having payment request type as LOAN_PAYMENT_REQUEST_TYPE_PRE_CLOSURE) only.
		if loanAccount.GetVendor() == palPb.Vendor_IDFC {
			_, err = s.initiateLoanClosureFlow(ctx, loanAccount, loanPaymentRequest.GetId())
			if err != nil {
				logger.Error(ctx, "failed to initiate loan closure workflow", zap.String(logger.PAYMENT_REQUEST_ID, loanPaymentRequest.GetId()), zap.Error(err))
				res.Status = rpc.StatusInternalWithDebugMsg("failed to initiate loan closure workflow")
				return res, nil
			}
		}
	}
	metrics.RecordPrePaymentTxn(req.GetAccountType().String(), "SUCCESS", req.GetLoanHeader().GetVendor().String(), req.GetLoanHeader().GetLoanProgram().String())
	res.ReferenceId = loanPaymentRequest.GetOrchId()
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) isPreClosureAllowed(loanAccount *palPb.LoanAccount) bool {
	if s.DynConf.Prepay().LenderToPreClosureBlackOutConfig() == nil {
		return true
	}

	blackOutConfig := s.DynConf.Prepay().LenderToPreClosureBlackOutConfig().Get(loanAccount.GetVendor().String())
	if blackOutConfig == nil {
		return true
	}

	currentHour := time.Now().In(datetime.IST).Hour()
	start := blackOutConfig.BlockStartHour()
	end := blackOutConfig.BlockEndHour()

	// If start hour is greater than end hour, it means we're crossing midnight
	// Blocked from start hour till midnight OR from midnight till end hour
	if start > end && (currentHour >= start || currentHour < end) {
		return false
	}
	// Normal case: blocked between start and end hour on the same day
	if start <= end && currentHour >= start && currentHour < end {
		return false
	}
	return true
}

func (s *Service) isPrepayAllowed(ctx context.Context, loanAccount *palPb.LoanAccount) (bool, prepayNotAllowedReason, error) {

	// check if prepay is enabled for the given lender and loan program combination.
	if !loansPkg.CheckPrePayEnabled(&pal_enums.LoanHeader{
		LoanProgram: deeplink.GetFeLoanProgramFromBe(loanAccount.GetLoanProgram()),
		Vendor:      deeplink.GetPalFeVendorFromBe(loanAccount.GetVendor())}) {
		logger.Info(ctx, "prepay is not enabled for given lender and loan program combination", zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()))
		return false, unspecified, nil
	}

	// do not allow prepayments if loan is already closed or is overpaid.
	if loanAccount.GetStatus() == palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED {
		logger.Info(ctx, "prepay not allowed, loan account is already in a closed state", zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()))
		return false, unspecified, nil
	}

	// check if some earlier payment on the loan account is still in processing state, if it is then a new payment is not allowed.
	// **Note** : intentionally not considering 'CREATED' status as an in_progress state, as a payment request can remain in 'CREATED' status if the user attempted the payment but
	// dropped off from the flow before completing the payment authorization, we do not want to block new payments for such cases, similarly not blocking new payments on MANUAL_INTERVENTION state
	// as we intentionally do not want to block any new payments if some earlier payment got stuck in MANUAL_INTERVENTION state.
	// todo (utkarsh) : move this to a central place after cleaning up the redundant/unused payment request statuses.
	paymentInProgressStatuses := []palPb.LoanPaymentRequestStatus{
		palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_INITIATED,
		palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS,
	}
	_, getErr := s.loanPaymentRequestDao.GetByAccountIdAndStatuses(ctx, loanAccount.GetId(), paymentInProgressStatuses)
	switch {
	case errors.Is(getErr, epifierrors.ErrRecordNotFound):
		// no existing request in pending state
	case getErr != nil:
		return false, unspecified, fmt.Errorf("error fetching in_progress loan payment requests from the acount, err : %v", getErr)
	default:
		// some payment requests existing in processing state, so new payment is not allowed.
		logger.Info(ctx, "prepay not allowed, some existing payment requests are still in processing state", zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()))
		return false, existingPaymentInProgress, nil
	}

	// prepayments are not allowed in prepayment blackout period.
	isBlackOutPeriod, err := s.IsPrepayBlackOutPeriod(ctx, loanAccount)
	switch {
	case err != nil:
		return false, unspecified, fmt.Errorf("error checking if prepay is in black out period, err : %v", err)
	case isBlackOutPeriod:
		logger.Info(ctx, "prepay not allowed, prepay is in black out period", zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()))
		return false, prepayBlackoutPeriod, nil
	}

	foreclosureDetailsProvider, getProviderErr := s.loanDataProvider.FetchLoanForeClosureDetailsProvider(ctx, &palPb.LoanHeader{
		LoanProgram: loanAccount.GetLoanProgram(),
		Vendor:      loanAccount.GetVendor(),
	}, loanAccount.GetLmsPartner())
	if getProviderErr != nil || foreclosureDetailsProvider == nil {
		logger.Error(ctx, "error fetching foreclosure details provider", zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()), zap.Error(getProviderErr))
		return false, unspecified, fmt.Errorf("error fetching foreclosure details provider, err : %v", getProviderErr)
	}

	foreClosureDetails, getDetailsErr := foreclosureDetailsProvider.FetchLoanPreClosureDetailsFromVendor(ctx, loanAccount)
	if getDetailsErr != nil {
		if errors.Is(getDetailsErr, palErrors.ErrExpectedDelayInLmsUpdateAtLender) {
			return false, lmsNotUpdatedAtLender, nil
		}
		return false, unspecified, fmt.Errorf("error fetching foreclosure details from vendor, err : %v", getDetailsErr)
	}
	// negative foreclosure amount denotes that loan was overpaid, so not allowing prepayments in that case as well.
	if !moneyPb.IsPositive(foreClosureDetails.LoanPreCloseAmount) {
		logger.Info(ctx, "prepay not allowed, loan foreclosure amount is zero", zap.String(logger.LOAN_ACCOUNT_ID, loanAccount.GetId()))
		return false, unspecified, nil
	}

	return true, unspecified, nil
}

func (s *Service) IsPrepayBlackOutPeriod(ctx context.Context, loanAccount *palPb.LoanAccount) (bool, error) {
	if loanAccount.GetVendor() == palPb.Vendor_LENDEN {
		// Lenden doesn't define a fixed blackout period for EMI prepayment.
		// Instead, the flag returned by LoanDetails RPC indicates that Lenden is currently processing the e-NACH mandate
		// (set up during loan account creation) to auto-debit the EMI.
		//
		// Allowing prepayment while this flag is true can result in double debits,
		// requiring Lenden to manually refund the excess and respond to user complaints.
		// To avoid this, we block prepayment when the flag is true.
		res, err := s.ldcVgClient.GetLoanDetails(ctx, &ldcVgPb.GetLoanDetailsRequest{
			Header: &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
			LoanId: loanAccount.GetAccountNumber(),
		})
		if err = epifigrpc.RPCError(res, err); err != nil {
			return false, errors.Wrap(err, "error getting loan details")
		}
		if !res.GetLoanDetails().GetEnablePaymentGateway() {
			return true, nil
		}
		return false, nil
	}

	if s.DynConf.Prepay().LenderToPrepayBlackOutConfig() == nil {
		return false, nil
	}

	blackOutConfig := s.DynConf.Prepay().LenderToPrepayBlackOutConfig().Get(loanAccount.GetVendor().String())
	if blackOutConfig == nil {
		return false, nil
	}

	currentTime := time.Now()

	// get loan installment info
	loanInstallmentInfo, getErr := s.loanInstallmentInfoDao.GetByActiveAccountId(ctx, loanAccount.GetId())
	if getErr != nil {
		return false, fmt.Errorf("error fetching loan installment info from db, err : %v", getErr)
	}

	// get all the loan installments
	installments, getErr := s.loanInstallmentPayoutDao.GetByLoanInstallmentInfoId(ctx, loanInstallmentInfo.GetId())
	// Loan installment payout entries might not be present for loans created today because we fetch the installments from vendor as part of a scheduled job at the end of the day
	// we can ignore the blackout period if installments are not found
	if getErr != nil && !errors.Is(getErr, epifierrors.ErrRecordNotFound) {
		return false, fmt.Errorf("error fetching loan installment payouts from db, err : %v", getErr)
	}

	// todo (utkarsh) : optimize the following logic to directly compute this through a db query.
	// prepay is not allowed in [installment date - prepayBlockBeforeEmiDateInDays, installment date + gracePeriodInDays + prepayBlockAfterGracePeriodInDays]
	// duration to avoid possible race conditions in calculating mandate presentation amount due to in between payments.
	for _, installment := range installments {
		// if the installment is already paid then we do not need to block payments around that installment.
		if installment.GetStatus() == palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS {
			continue
		}
		installmentDate := datetime.DateToTimeV2(installment.GetDueDate(), datetime.IST)

		blackOutStartTime := datetime.StartOfDay(installmentDate.AddDate(0, 0, int(-blackOutConfig.BlockDurationBeforeEmiDueDateInDays())))
		blackOutEndTime := datetime.EndOfDay(installmentDate.AddDate(0, 0, int(blackOutConfig.BlockDurationAfterEmiDueDateInDays())))

		if currentTime.After(blackOutStartTime) && currentTime.Before(blackOutEndTime) {
			return true, nil
		}

		// 2nd block will start at the end of grace period
		blackOutStartTime = datetime.EndOfDay(installmentDate.AddDate(0, 0, int(loanInstallmentInfo.GetDetails().GetGracePeriod()-int64(blackOutConfig.BlockDurationBeforeEmiGraceEndDateInDays()))))
		blackOutEndTime = datetime.EndOfDay(installmentDate.AddDate(0, 0, int(loanInstallmentInfo.GetDetails().GetGracePeriod()+int64(blackOutConfig.BlockDurationAfterEmiGraceEndDateInDays()))))

		if currentTime.After(blackOutStartTime) && currentTime.Before(blackOutEndTime) {
			return true, nil
		}
	}

	return false, nil
}

func (s *Service) initiateLoanClosureFlow(ctx context.Context, account *palPb.LoanAccount, lprId string) (string, error) {
	loanReqs, loanReqErr := s.loanRequestsDao.GetByLoanAccountIdAndType(ctx, account.GetId(), palPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE)
	if loanReqErr != nil && !errors.Is(loanReqErr, epifierrors.ErrRecordNotFound) {
		return "", errors.Wrap(loanReqErr, "error while fetching existing loan closure requests from db")
	}

	var loanRequest *palPb.LoanRequest
	for _, lr := range loanReqs {
		// if status is success then return already exists error
		if lr.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
			return "", errors.Wrap(epifierrors.ErrAlreadyExists, "loan closure request found in success state")
		} else {
			if _, ok := nonTerminalLrStatusForLoanClosure[lr.GetStatus()]; ok {
				// if lr status is not success then set lr in loan request, this will ensure that non-terminal lr is set in loan request
				loanRequest = lr
				break
			}
		}
	}

	// creating a new request if one does not already exists.
	if loanRequest == nil {
		orchId := uuid.New().String()
		var err error
		// Create a new Loan request to orchestrate the loan closure flow
		loanRequest, err = s.loanRequestsDao.Create(ctx, &palPb.LoanRequest{
			ActorId:       account.GetActorId(),
			LoanAccountId: account.GetId(),
			OrchId:        orchId,
			Type:          palPb.LoanRequestType_LOAN_REQUEST_TYPE_CLOSURE,
			Status:        palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CREATED,
			SubStatus:     palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED,
			Vendor:        account.GetVendor(),
			LoanProgram:   account.GetLoanProgram(),
		})
		if err != nil {
			if errors.Is(err, epifierrors.ErrDuplicateEntry) {
				return "", errors.Wrap(epifierrors.ErrAlreadyExists, "request already exists for given actor id and vendor")
			}
			return "", errors.Wrap(err, "failed to create loan request for loan closure flow")
		}
	}
	var err error
	if s.usePreCloseV3Workflow(ctx, loanRequest) {
		err = s.initiatePreCloseV3Workflow(ctx, account, loanRequest)
	} else {
		err = s.initiatePreCloseV2Workflow(ctx, account, lprId, loanRequest)
	}
	if err != nil {
		return "", fmt.Errorf("error while initiaing loan closure workflow : %w", err)
	}
	return loanRequest.GetId(), nil
}

func (s *Service) usePreCloseV3Workflow(_ context.Context, lr *palPb.LoanRequest) bool {
	// pre-close v3 workflow is only active for LAMF right now.
	return lr.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_LAMF
}

func (s *Service) initiatePreCloseV2Workflow(ctx context.Context, account *palPb.LoanAccount, lprId string, loanRequest *palPb.LoanRequest) error {
	// Initiate the loan-closure workflow to mark the loan account closed at vendor's end in case payment of outstanding amount was successful
	preClosePayload := &palWorkflowPb.LoanPreClosePayload{
		Vendor:      account.GetVendor(),
		LoanProgram: account.GetLoanProgram(),
		PrePayLprId: lprId,
	}

	marPayload, err := protojson.Marshal(preClosePayload)
	if err != nil {
		return errors.New(fmt.Sprintf("failed to marshal workflow payload. err: %v", err.Error()))
	}

	err = s.initiateWorkflowV2(
		ctx,
		&workflow.ClientReqId{Id: loanRequest.GetOrchId(), Client: workflow.Client_PRE_APPROVED_LOAN},
		account.GetActorId(),
		marPayload, celestialPkg.GetTypeEnumFromWorkflowType(palNs.LoanPreClose),
		workflow.Version_V0,
	)

	if err != nil && !errors.Is(err, epifierrors.ErrAlreadyExists) {
		return errors.Wrap(err, "failed to initiate loan closure flow")
	}

	return nil
}

func (s *Service) initiatePreCloseV3Workflow(ctx context.Context, account *palPb.LoanAccount, loanRequest *palPb.LoanRequest) error {
	// Initiate the loan-closure workflow to mark the loan account closed at vendor's end in case payment of outstanding amount was successful
	preClosePayload := &palWorkflowPb.LoanPreCloseV3Payload{
		Vendor:      account.GetVendor(),
		LoanProgram: account.GetLoanProgram(),
	}

	marPayload, err := protojson.Marshal(preClosePayload)
	if err != nil {
		return errors.New(fmt.Sprintf("failed to marshal loan pre-close v3 workflow payload. err: %v", err.Error()))
	}

	err = s.initiateWorkflowV2(
		ctx,
		&workflow.ClientReqId{Id: loanRequest.GetOrchId(), Client: workflow.Client_PRE_APPROVED_LOAN},
		account.GetActorId(),
		marPayload, celestialPkg.GetTypeEnumFromWorkflowType(palNs.LoanPreCloseV3),
		workflow.Version_V0,
	)

	if err != nil && !errors.Is(err, epifierrors.ErrAlreadyExists) {
		return errors.Wrap(err, "failed to initiate loan pre-close v3 flow")
	}

	return nil
}

// nolint: funlen
func (s *Service) GetPrePayDetails(ctx context.Context, req *palPb.GetPrePayDetailsRequest) (*palPb.GetPrePayDetailsResponse, error) {
	ctx = epificontext.WithOwnership(ctx, helper.GetPalOwnership(req.GetLoanHeader().GetVendor()))
	res := &palPb.GetPrePayDetailsResponse{
		Status: rpc.StatusOk(),
	}
	loanAccount, err := s.loanAccountsDao.GetById(ctx, req.GetLoanId())
	if err != nil {
		logger.Error(ctx, "failed to get loan account by id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanInstallmentInfo, err := s.loanInstallmentInfoDao.GetByActiveAccountId(ctx, req.GetLoanId())
	if err != nil {
		logger.Error(ctx, "failed to get loan installment info by loan id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	loanForeClosureProvider, err := s.loanDataProvider.FetchLoanForeClosureDetailsProvider(ctx, req.GetLoanHeader(), loanAccount.GetLmsPartner())
	if err != nil {
		logger.Error(ctx, "failed to fetch loan data provider", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	latestLoanPaymentRequest, err := s.loanPaymentRequestDao.GetLatestByAccountIdAndStatuses(ctx, req.GetLoanId(), nil)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Error(ctx, "failed to get latest loan payment request by loan id", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if loanForeClosureProvider != nil {
		foreClosureDetails, foreClosureDetailsErr := loanForeClosureProvider.FetchLoanPreClosureDetailsFromVendor(ctx, loanAccount)
		if foreClosureDetailsErr != nil {
			logger.Error(ctx, "unable to get the foreclosure details", zap.Error(foreClosureDetailsErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}

		foreClosureDetail := &palPb.ForeclosureDetails{}

		if foreClosureDetails != nil && foreClosureDetails.LoanPrincipalOutstandingAmount != nil {
			foreClosureDetail.PrincipalOutstandingAmount = foreClosureDetails.LoanPrincipalOutstandingAmount
		}
		if foreClosureDetails != nil && foreClosureDetails.LoanPreCloseAmount != nil {
			foreClosureDetail.TotalOutstandingAmount = foreClosureDetails.LoanPreCloseAmount
		}
		if foreClosureDetails != nil && foreClosureDetails.LoanInterestOutstandingAmount != nil {
			foreClosureDetail.InterestOutstandingAmount = foreClosureDetails.LoanInterestOutstandingAmount
		}
		if foreClosureDetails != nil && foreClosureDetails.LoanPenaltyAmount != nil {
			foreClosureDetail.PenaltyAmt = foreClosureDetails.LoanPenaltyAmount
		}
		if foreClosureDetails != nil && foreClosureDetails.LoanFeesAmount != nil {
			foreClosureDetail.FeesAmt = foreClosureDetails.LoanFeesAmount
		}
		if foreClosureDetails != nil && foreClosureDetails.LoanOtherCharges != nil {
			foreClosureDetail.OtherCharges = foreClosureDetails.LoanOtherCharges
		}
		if foreClosureDetails != nil && foreClosureDetails.LoanPreCloseCharges != nil {
			foreClosureDetail.PreCloseCharges = foreClosureDetails.LoanPreCloseCharges
		}

		res.ForeclosureDetails = foreClosureDetail
	}

	loanCancellationDetailsProvider, err := s.loanDataProvider.FetchLoanCancellationDetailsProvider(ctx, req.GetLoanHeader())
	if err != nil {
		logger.Error(ctx, "failed to fetch loanCancellationDetailsProvider", zap.Error(err))
		res.Status = rpcPb.StatusInternal()
		return res, nil
	}

	if loanCancellationDetailsProvider != nil {
		cancellationDetails, cancelErr := loanCancellationDetailsProvider.FetchLoanCancellationDetailsFromVendor(ctx, loanAccount)
		if cancelErr != nil {
			logger.Error(ctx, "unable to get the cancellation details", zap.Error(cancelErr))
			res.Status = rpcPb.StatusInternal()
			return res, nil
		}
		res.LoanCancellationDetails = &palPb.LoanCancellationDetails{
			IsCancellationAllowed: cancellationDetails.IsCancellationAllowed,
			CancellationAmount:    cancellationDetails.LoanCancellationAmount,
		}
	}

	// setting this as lii next emi amount, can update this logic when we have logic to calculate overdue amount
	res.OverDueAmount = loanInstallmentInfo.GetDetails().GetNextEmiAmount()
	res.UpcomingEmi = loanInstallmentInfo.GetDetails().GetNextEmiAmount()
	res.LoanAccount = loanAccount
	res.LatestLoanPaymentRequest = latestLoanPaymentRequest
	return res, nil
}
