package common

type Flags struct {
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus             bool `dynamic:"true"`
	IsFldgLoanOverdueSherlockBannerEnabled bool `dynamic:"true"`
	IsOffAppPaymentV2Enabled               bool `dynamic:"true"`
	IsRecommendationEngineEnabled          bool `dynamic:"true"`
	HideIdfcOffer                          bool `dynamic:"true"`
	IsAbflKfsGenerationV2                  bool `dynamic:"true"`
	// when this flag is true we are moving SG, LDC's RTD program to creation of loec and applicant to realtime
	MovePreQualToRealTime bool `dynamic:"true"`
	// IsSgDigilockerEnabled is a flag to determine if the digilocker flow is enabled for SG
	IsSgDigilockerEnabled bool `dynamic:"true"`
	// Flag to control liveness image format - true for presigned URL, false for base64
	UseBase64Image                  bool `dynamic:"true"`
	IsLdcApplicationMovementEnabled bool `dynamic:"true"`
}
