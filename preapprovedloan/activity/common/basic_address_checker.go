package common

import (
	"context"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/userdata"

	"github.com/pkg/errors"
)

func (p *Processor) IsBasicAddressCollected(ctx context.Context, req *palActivityPb.IsBasicAddressCollectedRequest) (*palActivityPb.IsBasicAddressCollectedResponse, error) {
	userData, err := p.userDataProvider.GetDefaultUserData(ctx, &userdata.GetDefaultUserDataRequest{ActorId: req.GetActorId()})
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in GetDefaultUserData err: %v", err))
	}
	return &palActivityPb.IsBasicAddressCollectedResponse{
		IsBasicAddressCollected: userData.GetResidenceDetails().GetResidentialAddress().GetAddress().GetPostalCode() != "",
	}, nil
}
