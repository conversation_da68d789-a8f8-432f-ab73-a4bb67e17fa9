//nolint:goimports
package common

import (
	"context"
	"fmt"
	"time"

	"github.com/epifi/be-common/pkg/async/goroutine"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/logger"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/salaryestimation"
	typesV2 "github.com/epifi/gamma/api/typesv2"
	salaryestimationTypes "github.com/epifi/gamma/api/typesv2/salaryestimation"
	userPb "github.com/epifi/gamma/api/user"
	palAct "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

const SalaryStalenessThresholdInDays = 15

func (p *Processor) EstimateSalary(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palAct.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}

		lg := activity.GetLogger(ctx)

		salaryEstResp, salaryEstErr := p.salaryEstimationClient.GetSalary(ctx, &salaryestimation.GetSalaryRequest{
			ActorId: lse.GetActorId(),
		})
		if te := epifigrpc.RPCError(salaryEstResp, salaryEstErr); te != nil && !salaryEstResp.GetStatus().IsRecordNotFound() {
			lg.Error("failed to get salary estimation", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get salary estimation :%v", te))
		}

		// if salary is not present, it will be considered as stale
		lastSalaryComputedAt := salaryEstResp.GetSalary().GetComputedAt().AsTime()
		currentTime := time.Now()
		// check if salary is present and not stale
		if lastSalaryComputedAt.Add(SalaryStalenessThresholdInDays * 24 * time.Hour).After(currentTime) {
			// mark LSE success
			lg.Info(fmt.Sprintf("salary is not stale, last computed at: %v, current time: %v", lastSalaryComputedAt.String(), currentTime.String()))
			palAct.MarkLoanStepSuccess(lse)
			return res, nil
		}

		lg.Info(fmt.Sprintf("salary is stale or record not found, last computed at: %v, current time: %v", lastSalaryComputedAt.String(), currentTime.String()))

		user, err := p.rpcHelper.GetUserByActorId(ctx, lse.GetActorId())
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "error while fetching user").Error())
		}
		var employmentType typesV2.EmploymentType
		var orgName string
		dataVerificationDetails := user.GetDataVerificationDetails().GetDataVerificationDetails()
		for _, detail := range dataVerificationDetails {
			if detail.GetVerificationMethod() == userPb.VerificationMethod_VERIFICATION_METHOD_UNVERIFIED_LOAN_DATA &&
				detail.GetDataType() == userPb.DataType_DATA_TYPE_EMPLOYMENT_DETAIL {
				employmentType = detail.GetEmploymentDetail().GetEmploymentType()
				orgName = detail.GetEmploymentDetail().GetOrganizationName()
				break
			}
		}

		computeSalaryRes, computeSalaryErr := p.salaryEstimationClient.ComputeSalary(ctx, &salaryestimation.ComputeSalaryRequest{
			ActorId:     lse.GetActorId(),
			Client:      salaryestimation.Client_CLIENT_LOANS,
			ClientReqId: fmt.Sprintf("%s:%s", palPb.Vendor_EPIFI_TECH, lse.GetOrchId()),
			Source:      salaryestimationTypes.Source_SOURCE_AA,
			// Holding screen is required as it is later persisted and used to continue the flow
			RequireHoldingScreen: true,
			EmploymentType:       employmentType,
			OrganisationName:     orgName,
		})
		if err := epifigrpc.RPCError(computeSalaryRes, computeSalaryErr); err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "error computing salary").Error())
		}
		switch computeSalaryRes.GetAttemptStatus() {
		case salaryestimation.AttemptStatus_ATTEMPT_STATUS_PENDING:
			// just firing a call to compute salary for the service to update its status since Salary Estimation service has no orchestration by own
			// calling with RequireHoldingScreen: false which will update the state at salary estimation end as well
			goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
				_, _ = p.salaryEstimationClient.ComputeSalary(ctx, &salaryestimation.ComputeSalaryRequest{
					ActorId:     lse.GetActorId(),
					Client:      salaryestimation.Client_CLIENT_LOANS,
					ClientReqId: fmt.Sprintf("%s:%s", palPb.Vendor_EPIFI_TECH, lse.GetOrchId()),
					Source:      salaryestimationTypes.Source_SOURCE_AA,
				})
			})

			res.NextAction = computeSalaryRes.GetNextAction()
			return res, errors.Wrap(epifierrors.ErrTransient, "salary estimation flow initiated")
		case salaryestimation.AttemptStatus_ATTEMPT_STATUS_CANCELLED:
			cancelRes, err := p.palClient.CancelApplication(ctx, &palPb.CancelApplicationRequest{
				LoanRequestId: lse.GetRefId(),
				ActorId:       lse.GetActorId(),
				LoanHeader:    &palPb.LoanHeader{Vendor: palPb.Vendor_EPIFI_TECH, LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY},
			})
			if err = epifigrpc.RPCError(cancelRes, err); err != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "error cancelling salary attempt").Error())
			}
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED
			return res, nil
		case salaryestimation.AttemptStatus_ATTEMPT_STATUS_FAILED,
			salaryestimation.AttemptStatus_ATTEMPT_STATUS_EXPIRED:
			palAct.MarkLoanStepFail(lse)
			return res, nil
		case salaryestimation.AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL:
			palAct.MarkLoanStepSuccess(lse)
			return res, nil
		default:
			lg.Error("compute salary status not handled", zap.String(logger.STATUS, computeSalaryRes.GetStatus().String()))
			return nil, errors.Wrap(epifierrors.ErrTransient, "compute salary status not handled")
		}
	})
	return actRes, actErr
}

// IncomeEstimation Deprecated in favor of EstimateSalary
func (p *Processor) IncomeEstimation(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palAct.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}

		// updating the orch id in LSE so that it can be used to be passed in the salary estimation request as client req id
		lse.OrchId = uuid.NewString()
		err := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_ORCH_ID})
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LSE orch id, err: %v", err))
		}

		salaryEstResp, salaryEstErr := p.salaryEstimationClient.GetSalary(ctx, &salaryestimation.GetSalaryRequest{
			ActorId: lse.GetActorId(),
		})
		if te := epifigrpc.RPCError(salaryEstResp, salaryEstErr); te != nil && !salaryEstResp.GetStatus().IsRecordNotFound() {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get salary estimation :%v", te))
		}
		if salaryEstResp.GetStatus().IsRecordNotFound() {
			computeSalaryRes, computeSalaryErr := p.salaryEstimationClient.ComputeSalary(ctx, &salaryestimation.ComputeSalaryRequest{
				ActorId:     lse.GetActorId(),
				Client:      salaryestimation.Client_CLIENT_LOANS,
				ClientReqId: fmt.Sprintf("%s:%s", palPb.Vendor_EPIFI_TECH, lse.GetOrchId()),
				Source:      salaryestimationTypes.Source_SOURCE_AA,
				// Holding screen is required as it is later persisted and used to continue the flow
				RequireHoldingScreen: true,
			})
			if err = epifigrpc.RPCError(computeSalaryRes, computeSalaryErr); err != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, errors.Wrap(err, "error computing salary").Error())
			}
			txnExec, txnExecErr := helper.GetTxnExecutorByOwnership(ctx, p.txnExecutorProvider)
			if txnExecErr != nil {
				return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("failed to get txn executor by ownership, err: %v", txnExecErr))
			}
			txnErr := txnExec.RunTxn(ctx, func(txnCtx context.Context) error {
				loanRequest, err := p.loanRequestDao.GetById(txnCtx, lse.GetRefId())
				if err != nil {
					return errors.Wrap(err, "error in getting lr by id")
				}
				loanRequest.NextAction = computeSalaryRes.GetNextAction()
				updateErr := p.loanRequestDao.Update(txnCtx, loanRequest, []palPb.LoanRequestFieldMask{
					palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
				})
				if updateErr != nil {
					return errors.Wrap(updateErr, "failed to update lr next action")
				}
				// set the lse status to in_progress
				res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
				res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
				if err = p.loanStepExecutionDao.Update(txnCtx, res.GetLoanStep(), res.GetLseFieldMasks()); err != nil {
					return errors.Wrap(err, "failed to update LSE status in Txn")
				}
				return nil
			})
			if txnErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lse and lr in txn, err: %v", txnErr.Error()))
			}
			return res, nil
		}
		res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
		if err := p.loanStepExecutionDao.Update(ctx, res.GetLoanStep(), res.GetLseFieldMasks()); err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LSE status in txn, err: %v", err))
		}
		return res, nil
	})
	return actRes, actErr
}

// IncomeEstimationStatus Deprecated in favor of EstimateSalary
func (p *Processor) IncomeEstimationStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palAct.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}
		err := p.checkIfSalaryIdentified(ctx, lse)
		if err != nil {
			return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error checking if salary identified"))
		}
		res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
		res.LseFieldMasks = append(res.GetLseFieldMasks(), palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
		if err := p.loanStepExecutionDao.Update(ctx, res.GetLoanStep(), res.GetLseFieldMasks()); err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LSE status in txn, err: %v", err))
		}
		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) checkIfSalaryIdentified(ctx context.Context, lse *palPb.LoanStepExecution) error {
	logger := activity.GetLogger(ctx)
	res, err := p.salaryEstimationClient.GetSalary(ctx, &salaryestimation.GetSalaryRequest{
		ActorId: lse.GetActorId(),
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return errors.Wrap(err, "error getting estimated salary")
	}
	logger.Info("AA data analysis successful, lenders to verify if mandatory data available")
	return nil
}
