package activity

import (
	"context"
	"fmt"
	"math"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/dao"

	"github.com/pkg/errors"
)

// use when comparing interest rate and processing fee percentage
const loanOfferFloatingPointParamsPrecision = 1e-3

func MarkLoanStepSuccessV2(lse *preApprovedLoanPb.LoanStepExecution, res *palActivityPb.PalActivityResponse) {
	lse.Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED
	res.LseFieldMasks = append(res.GetLseFieldMasks(),
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	)
}

func MarkLoanStepSuccessWithSubStatus(lse *preApprovedLoanPb.LoanStepExecution, subStatus preApprovedLoanPb.LoanStepExecutionSubStatus, res *palActivityPb.PalActivityResponse) {
	lse.Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	lse.SubStatus = subStatus
	res.LseFieldMasks = append(res.GetLseFieldMasks(),
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
	)
}

func MarkLoanStepSuccess(lse *preApprovedLoanPb.LoanStepExecution) {
	lse.Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
}

// MarkSyncActivitySuccess updates the LSE as success and in synchronous activities(Sync proxy usage), mark sync task as done
func MarkSyncActivitySuccess(lse *preApprovedLoanPb.LoanStepExecution, res *palActivityPb.PalActivityResponse) {
	res.IsSyncTaskDone = true
	lse.Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
}

func SetLoanStepDetails(lse *preApprovedLoanPb.LoanStepExecution, details *preApprovedLoanPb.LoanStepExecutionDetails) {
	lse.Details = details
}

func MarkLoanStepFail(lse *preApprovedLoanPb.LoanStepExecution) {
	lse.Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
}

func MarkLoanStepFailWithSubStatus(
	res *palActivityPb.PalActivityResponse,
	subStatus preApprovedLoanPb.LoanStepExecutionSubStatus,
) {
	res.GetLoanStep().Status = preApprovedLoanPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
	res.GetLoanStep().SubStatus = subStatus
	res.LseFieldMasks = append(res.GetLseFieldMasks(), preApprovedLoanPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS)
}

type loanStepSetter interface {
	SetLoanStep(loanStep *palPb.LoanStepExecution)
}

// ExecuteWork can be used to perform the activity execution based on the Loan step status.
// It will execute the passed function only when loan step status is IN_PROGRESS, CREATED or UNSPECIFIED
func ExecuteWork[T any](ctx context.Context, loanStepExecutionsDao dao.LoanStepExecutionsDao, loanStep *palPb.LoanStepExecution, workExec func(ctx context.Context, lse *palPb.LoanStepExecution) (*T, error)) (*T, error) {
	var err error
	ctx = epificontext.CtxWithActorId(ctx, loanStep.GetActorId())

	loanStep, err = loanStepExecutionsDao.GetById(ctx, loanStep.GetId())
	if err != nil {
		return nil, GetActivityErrFromDaoError(err)
	}

	res := new(T)
	lsSetter, ok := interface{}(res).(loanStepSetter)
	if ok {
		lsSetter.SetLoanStep(loanStep)
	}
	switch loanStep.GetStatus() {
	case palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS:
		return res, nil
	case palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED:
		// We are returning both res and err here but celestial only allows one of them to be non-null.
		// If error in not nil, it overrides the response returned and sends nil instead to workflow.
		// We're using ExecuteWork in most of our workflows, so not updating the behaviour of this func for all calls.
		ow := epificontext.OwnershipFromContext[context.Context](ctx)
		if ow == commontypes.Ownership_FIFTYFIN_LAMF {
			return res, nil
		}
		return res, errors.Wrap(epifierrors.ErrPermanent, "activity execution cancelled")
	case palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
		palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION:
		return res, errors.Wrap(epifierrors.ErrPermanent, "failed to execute work")
	case palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
		palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS,
		palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_UNSPECIFIED:
		res, err = workExec(ctx, loanStep)
		return res, err
	default:
		return res, errors.Wrapf(epifierrors.ErrTransient, "unknown state: %s", loanStep.GetStatus())
	}
}

func UpdateNextActionInLoanRequest(ctx context.Context, loanRequestsDao dao.LoanRequestsDao, lrId string, dl *deeplink.Deeplink) error {
	loanRequest, err := loanRequestsDao.GetById(ctx, lrId)
	if err != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lse get by id, err: %v", err))
	}
	loanRequest.NextAction = dl
	updateErr := loanRequestsDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
	})
	if updateErr != nil {
		return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update lr next action, err: %v", updateErr))
	}
	return nil
}

func RunWithOwnershipAndReset(ctx context.Context, ownership commontypes.Ownership, function func(context.Context)) context.Context {
	oldOwnership := epificontext.OwnershipFromContext(ctx)
	ctx = epificontext.WithOwnership(ctx, ownership)
	function(ctx)
	return epificontext.WithOwnership(ctx, oldOwnership)
}

func IsLoanRequestValidWithNewOffer(lr *preApprovedLoanPb.LoanRequest, newOffer *preApprovedLoanPb.LoanOffer) (bool, error) {
	// federal bank offer has multiple ranges of interest rate and processing fee which is not considered in this evaluation
	if newOffer.GetVendor() == palPb.Vendor_FEDERAL {
		return false, errors.New("federal bank offers are not supported")
	}
	if len(newOffer.GetProcessingInfo().GetInterestRate()) == 0 {
		return false, errors.New("interest rate cannot be empty in new offer")
	}
	if len(newOffer.GetProcessingInfo().GetProcessingFee()) == 0 {
		return false, errors.New("processing fee cannot be empty in new offer")
	}

	if math.Abs(newOffer.GetProcessingInfo().GetInterestRate()[0].GetPercentage()-lr.GetDetails().GetLoanInfo().GetInterestRate()) > loanOfferFloatingPointParamsPrecision {
		return false, nil
	}
	pfPaise, err := moneyPkg.ToPaise(lr.GetDetails().GetLoanInfo().GetDeductions().GetProcessingFee())
	if err != nil {
		return false, errors.Wrap(err, "failed to convert processing fee to paise")
	}
	loanAmountPaise, err := moneyPkg.ToPaise(lr.GetDetails().GetLoanInfo().GetAmount())
	if err != nil {
		return false, errors.Wrap(err, "failed to convert processing fee to paise")
	}
	pfPercentage := float64(pfPaise) / float64(loanAmountPaise) * 100
	if math.Abs(newOffer.GetProcessingInfo().GetProcessingFee()[0].GetPercentage()-pfPercentage) > loanOfferFloatingPointParamsPrecision {
		return false, nil
	}
	if moneyPkg.Compare(lr.GetDetails().GetLoanInfo().GetAmount(), newOffer.GetOfferConstraints().GetMaxLoanAmount()) > 0 {
		return false, nil
	}
	if moneyPkg.Compare(lr.GetDetails().GetLoanInfo().GetAmount(), newOffer.GetOfferConstraints().GetMinLoanAmount()) < 0 {
		return false, nil
	}
	if lr.GetDetails().GetLoanInfo().GetTenureInMonths() > newOffer.GetOfferConstraints().GetMaxTenureMonths() {
		return false, nil
	}
	if lr.GetDetails().GetLoanInfo().GetTenureInMonths() < newOffer.GetOfferConstraints().GetMinTenureMonths() {
		return false, nil
	}
	if moneyPkg.Compare(lr.GetDetails().GetLoanInfo().GetEmiAmount(), newOffer.GetOfferConstraints().GetMaxEmiAmount()) > 0 {
		return false, nil
	}
	return true, nil
}

func AreOfferConstraintsSame(offer1 *preApprovedLoanPb.LoanOffer, offer2 *preApprovedLoanPb.LoanOffer) (bool, error) {
	// federal bank offer has multiple ranges of interest rate and processing fee which is not considered in this evaluation
	if offer1.GetVendor() == palPb.Vendor_FEDERAL || offer2.GetVendor() == palPb.Vendor_FEDERAL {
		return false, errors.New("federal bank offers are not supported")
	}
	if moneyPkg.Compare(offer1.GetOfferConstraints().GetMaxLoanAmount(), offer2.GetOfferConstraints().GetMaxLoanAmount()) != 0 {
		return false, nil
	}
	if moneyPkg.Compare(offer1.GetOfferConstraints().GetMinLoanAmount(), offer2.GetOfferConstraints().GetMinLoanAmount()) != 0 {
		return false, nil
	}
	if offer1.GetOfferConstraints().GetMaxTenureMonths() != offer2.GetOfferConstraints().GetMaxTenureMonths() {
		return false, nil
	}
	if offer1.GetOfferConstraints().GetMinTenureMonths() != offer2.GetOfferConstraints().GetMinTenureMonths() {
		return false, nil
	}
	if moneyPkg.Compare(offer1.GetOfferConstraints().GetMaxEmiAmount(), offer2.GetOfferConstraints().GetMaxEmiAmount()) != 0 {
		return false, nil
	}
	if len(offer1.GetProcessingInfo().GetInterestRate()) == 0 || len(offer2.GetProcessingInfo().GetInterestRate()) == 0 {
		return false, errors.New("interest rate cannot be empty offer")
	}
	if len(offer1.GetProcessingInfo().GetProcessingFee()) == 0 || len(offer2.GetProcessingInfo().GetProcessingFee()) == 0 {
		return false, errors.New("processing fee cannot be empty offer")
	}

	if math.Abs(offer1.GetProcessingInfo().GetInterestRate()[0].GetPercentage()-offer2.GetProcessingInfo().GetInterestRate()[0].GetPercentage()) > loanOfferFloatingPointParamsPrecision {
		return false, nil
	}
	if math.Abs(offer1.GetProcessingInfo().GetProcessingFee()[0].GetPercentage()-offer2.GetProcessingInfo().GetProcessingFee()[0].GetPercentage()) > loanOfferFloatingPointParamsPrecision {
		return false, nil
	}
	return true, nil
}
