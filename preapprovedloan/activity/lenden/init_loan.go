package lenden

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	ldcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
)

func (p *Processor) LdcUpdateUserSelectedOffer(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		applicant, err := p.loanApplicantDao.GetByActorId(ctx, req.GetLoanStep().GetActorId())
		if err != nil {
			lg.Error("error while getting loan applicant for the user", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}
		selectedOffer, err := p.loanOfferDao.GetById(ctx, lr.GetOfferId())
		if err != nil {
			return nil, errors.Wrap(err, "error getting selected offer")
		}
		vendorOfferIdParts := strings.Split(selectedOffer.GetVendorOfferId(), "_")
		if len(vendorOfferIdParts) != 2 {
			return nil, errors.New("invalid vendor offer id")
		}
		loanVendorId := vendorOfferIdParts[0]
		if strings.TrimSpace(loanVendorId) == "" {
			return nil, errors.New("empty vendor offer id")
		}
		vendorOfferCode := vendorOfferIdParts[1]
		if strings.TrimSpace(vendorOfferCode) == "" {
			return nil, errors.New("empty vendor offer code")
		}
		selectOfferRes, selectOfferErr := p.lendenVgClient.SelectOffer(ctx, &ldcVgPb.SelectOfferRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_LENDEN,
			},
			LoanId:          lr.GetVendorRequestId(),
			UserId:          applicant.GetVendorApplicantId(),
			SelectedAmount:  lr.GetDetails().GetLoanInfo().GetAmount(),
			Tenure:          lr.GetDetails().GetLoanInfo().GetTenureInMonths(),
			SelectedOfferId: vendorOfferCode,
		})
		if err = epifigrpc.RPCError(selectOfferRes, selectOfferErr); err != nil {
			lg.Error("error in lenden SelectOffer api", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, "error in lenden SelectOffer api call")
		}
		if selectOfferRes.GetOfferCode() == "" {
			return nil, errors.Wrap(epifierrors.ErrTransient, "no selected offer id returned by lenden")
		}
		// Assumption: All active offers provided by a lender are deactivated, as our system supports
		// providing only one active loan at a time for a user.
		activeOffers, err := p.loanOfferDao.GetActiveOffersByActorIdAndLoanPrograms(ctx, req.GetLoanStep().GetActorId(), nil)
		if err != nil {
			lg.Error("error getting active offers", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, "error getting active offers")
		}
		// We are using offer ID returned by LDC instead of the one we sent in the request as the user might
		// have already selected a different offer in the previous call which will be returned by LDC.
		selectedOfferVendorId := fmt.Sprintf("%s_%s", loanVendorId, selectOfferRes.GetOfferCode())
		lg.Info("deactivating non-selected offers", zap.String(logger.OFFER_ID, selectedOfferVendorId))

		var selectedOfferId string
		for _, activeOffer := range activeOffers {
			if activeOffer.GetVendorOfferId() == selectedOfferVendorId {
				selectedOfferId = activeOffer.GetId()
				break
			}
		}
		if selectedOfferId == "" {
			lg.Error("selected offer not found in active offers", zap.String(logger.OFFER_ID, selectedOfferVendorId))
			return nil, errors.Wrap(epifierrors.ErrTransient, "selected offer not found in active offers")
		}
		for _, activeOffer := range activeOffers {
			if activeOffer.GetId() != selectedOfferId {
				err = p.loanOfferDao.DeactivateLoanOffer(ctx, activeOffer.GetId())
				if err != nil {
					lg.Error("error deactivating non-selected offer", zap.Error(err))
					return nil, errors.Wrap(epifierrors.ErrTransient, "error deactivating non-selected offer")
				}
			}
		}
		return res, nil
	})
	return actRes, actErr
}
