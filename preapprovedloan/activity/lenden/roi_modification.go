// nolint: goimports
package lenden

import (
	"context"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"golang.org/x/exp/slices"
	decimalPb "google.golang.org/genproto/googleapis/type/decimal"
	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/typesv2/common"

	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	consentPb "github.com/epifi/gamma/api/consent"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
)

func (p *Processor) LDCAllowROIModification(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	execWorkRes, err := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(),
		func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
			lg := activity.GetLogger(ctx)

			// fetch esign step data
			esignStepExecutionData, err := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, lse.GetRefId(),
				palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
				palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_KFS)
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					lg.Error("kfs step not found", zap.Error(err))
					return nil, errors.Wrapf(epifierrors.ErrPermanent, "kfs step not found, err: %v", err)
				}
				lg.Error("error getting kfs step", zap.Error(err))
				return nil, errors.Wrapf(epifierrors.ErrTransient, "error getting kfs step")
			}

			loanRequest, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					lg.Error("loan request not found", zap.Error(err))
					return nil, errors.Wrapf(epifierrors.ErrPermanent, "loan request not found, err: %v", err)
				}
				lg.Error("error getting loan request", zap.Error(err))
				return nil, errors.Wrapf(epifierrors.ErrTransient, "error getting loan request,err: %v", err)
			}

			if lse.GetDetails().GetRoiModificationData().GetAllowedRoiForModification() == nil {
				loanOffer, loErr := p.loanOfferDao.GetById(ctx, loanRequest.GetOfferId())
				if loErr != nil {
					if errors.Is(err, epifierrors.ErrRecordNotFound) {
						lg.Error("loan offer not found", zap.Error(loErr))
						return nil, errors.Wrapf(epifierrors.ErrPermanent, "loan offer not found, err: %v", loErr)
					}
					lg.Error("error getting loan offer", zap.Error(loErr))
					return nil, errors.Wrapf(epifierrors.ErrTransient, "error getting loan offer,err: %v", loErr)
				}

				if lse.GetDetails().GetRoiModificationData() == nil {
					lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_RoiModificationData{
						RoiModificationData: &palPb.ROIModificationData{
							AllowedRoiForModification: loanOffer.GetOfferConstraints().GetLendenConstraintInfo().GetAllowedRoiValues(),
						},
					}
				} else {
					lse.GetDetails().GetRoiModificationData().AllowedRoiForModification = loanOffer.GetOfferConstraints().GetLendenConstraintInfo().GetAllowedRoiValues()
				}
				updateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS})
				if updateErr != nil {
					lg.Error("error updating loan step", zap.Error(updateErr))
					return nil, errors.Wrapf(epifierrors.ErrTransient, "error updating loan step,err: %v", updateErr)
				}
			}

			// If ROI mod deadline is not set, return transient error
			lrRoiModificationDetails := loanRequest.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().GetRoiModificationDetails()
			roiModificationDeadline := lrRoiModificationDetails.GetRoiModificationDeadline()
			if roiModificationDeadline == nil ||
				roiModificationDeadline.AsTime().Equal(time.Unix(0, 0)) {

				// If data not valid for LR then check lse
				roiModificationDeadline = esignStepExecutionData.GetDetails().GetESignStepData().GetRoiModificationDeadline()
				if roiModificationDeadline == nil ||
					roiModificationDeadline.AsTime().Equal(time.Unix(0, 0)) {
					lg.Error("no ROI modification deadline found in LSE")
					return nil, errors.Wrap(epifierrors.ErrPermanent, "no ROI modification deadline found in LSE")
				}
			}

			// If ROI mod deadline is in the past, return success
			if roiModificationDeadline.AsTime().Before(time.Now()) {
				lg.Info("ROI modification deadline is in the past, returning success", zap.Time(logger.END_TIME, lse.GetDetails().GetESignStepData().GetRoiModificationDeadline().AsTime()))
				palActivity.MarkLoanStepSuccess(lse)
				lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED
				if lseUpdateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
					palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
				}); lseUpdateErr != nil {
					lg.Error("error in updating lse", zap.Error(lseUpdateErr))
					return nil, errors.Wrapf(epifierrors.ErrTransient, "error in updating lse, err: %v", lseUpdateErr)
				}
				return &palActivityPb.PalActivityResponse{LoanStep: lse}, nil
			}

			if lse.GetDetails().GetRoiModificationData().GetChosenRoi() == 0 {
				// If chosen-modified-ROI value is empty, return disbursal in progress loan dashboard screen with a pencil in the dashboard,
				// which has a deeplink to choose the ROI from the list.
				// On link click, check the RPC that is called and store the chosen-modified-ROI in DB.
				deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetVendor(), LoanProgram: req.GetLoanProgram()})
				dl := deeplinkProvider.GetLoanLandingInfo(deeplinkProvider.GetLoanHeader())
				return &palActivityPb.PalActivityResponse{LoanStep: lse, NextAction: dl}, errors.Errorf("chosen-modified-ROI value is empty")
			}

			// Validate that chosen ROI is among acceptable valuesa
			isChosenRoiValid := slices.Contains(lse.GetDetails().GetRoiModificationData().GetAllowedRoiForModification(), lse.GetDetails().GetRoiModificationData().GetChosenRoi())
			if !isChosenRoiValid {
				lg.Error("chosen ROI is not among acceptable values", zap.Any("chosen_roi", lse.GetDetails().GetRoiModificationData().GetChosenRoi()), zap.Any("valid_rois", lse.GetDetails().GetRoiModificationData().GetAllowedRoiForModification()))
				return nil, errors.Wrapf(epifierrors.ErrPermanent, "chosen ROI is not among acceptable values")
			}

			// entering this flow means the user have selected the roi
			applicant, err := p.loanApplicantDao.GetByActorId(ctx, lse.GetActorId())
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					lg.Error("applicant not found", zap.Error(err))
					return nil, errors.Wrapf(epifierrors.ErrPermanent, "applicant not found,err: %v", err)
				}
				lg.Error("error getting applicant", zap.Error(err))
				return nil, errors.Wrapf(epifierrors.ErrTransient, "error getting applicant, err: %v", err)
			}
			ipAddress, err := p.rpcHelper.FetchIpAddress(ctx, lse.GetActorId())
			if err != nil {
				lg.Error("error fetching ip address", zap.Error(err))
				return nil, errors.Wrapf(epifierrors.ErrTransient, "error fetching ip address")
			}
			userDP, udpErr := p.userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
				ActorId: lse.GetActorId(),
				PropertyTypes: []typesv2.DeviceProperty{
					typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID,
				},
			})
			if te := epifigrpc.RPCError(userDP, udpErr); te != nil {
				lg.Error("error in fetching device details for the user", zap.Error(te))
				return nil, errors.Wrapf(epifierrors.ErrTransient, "error getting device id,err: %v", te)
			}
			deviceId := userDP.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID).GetDeviceId()

			modifyRoiConsent, err := p.consentClient.FetchConsent(ctx, &consentPb.FetchConsentRequest{
				ConsentType: consentPb.ConsentType_CONSENT_TYPE_LDC_MODIFY_ROI,
				ActorId:     lse.GetActorId(),
				Owner:       common.Owner_OWNER_EPIFI_TECH,
			})
			if err = epifigrpc.RPCError(modifyRoiConsent, err); err != nil {
				lg.Error("error in fetching consent", zap.Error(err))
				return nil, errors.Wrapf(epifierrors.ErrTransient, "error in fetching consent, err: %v", err)
			}

			locationRes, err := p.rpcHelper.GetUserDeviceLocation(ctx, lse.GetActorId())
			if err != nil {
				return nil, errors.Wrap(err, "error getting user device location")
			}

			roiModificationRes, err := p.lendenVgClient.ModifyRateOfInterest(ctx, &lenden.ModifyRateOfInterestRequest{
				Header:          &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
				LoanId:          loanRequest.GetVendorRequestId(),
				InterestRate:    float32(lse.GetDetails().GetRoiModificationData().GetChosenRoi()),
				ConsentCodeList: []lenden.ConsentType{lenden.ConsentType_CONSENT_TYPE_MODIFY_ROI},
				UserIp:          ipAddress,
				DeviceId:        deviceId,
				UserId:          applicant.GetVendorApplicantId(),
				ConsentedAt:     modifyRoiConsent.GetCreatedAt(),
				Latitude:        strconv.FormatFloat(locationRes.LatLng.GetLatitude(), 'f', 10, 64),
				Longitude:       strconv.FormatFloat(locationRes.LatLng.GetLongitude(), 'f', 10, 64),
			})
			if err = epifigrpc.RPCError(roiModificationRes, err); err != nil {
				lg.Error("error in roi modification", zap.Error(err))
				return nil, errors.Wrapf(epifierrors.ErrTransient, "error modifying ROI, err: %v", err)
			}

			// this will store the KFS and LA document in s3
			kfsLaS3PathResp, err := p.getKfsAndLaS3Path(ctx, &getS3KfsDocsFromRawDataRequest{
				vendorKfsDocUrl:  roiModificationRes.GetKfsDocUrl(),
				vendorLaDocUrl:   roiModificationRes.GetLoanAgreementDocUrl(),
				actorId:          lse.GetActorId(),
				kfsDocPathFormat: regeneratedKFSDocS3PathFormat,
				laDocPathFormat:  regeneratedLoanAgreementDocS3PathFormat,
			})
			if err != nil {
				lg.Error("error in getting s3 urls for kfs and la", zap.Error(err))
				return nil, errors.Wrapf(epifierrors.ErrTransient, "error in getting s3 urls for kfs and la, err: %v", err)
			}

			// Recalculate LR loan values again after ROI modification
			err = p.recalculateLoanValuesAfterRoiModificationAndUpdate(ctx, lse.GetDetails().GetRoiModificationData().GetChosenRoi(), lse)
			if err != nil {
				lg.Error("error in updating recalculated loan values in LR", zap.Error(err))
				return nil, errors.Wrapf(epifierrors.ErrTransient, "error in updating lr, err: %v", err)
			}

			// Update LSE data with the KFS Doc, LA Doc, and installment amount
			documentExpiryTime := timestamp.New(time.Now().Add(documentExpiryInSeconds * time.Second).Add(-documentExpiryBufferInSeconds * time.Second))
			lse.Details = &palPb.LoanStepExecutionDetails{
				Details: &palPb.LoanStepExecutionDetails_RoiModificationData{
					RoiModificationData: &palPb.ROIModificationData{
						ChosenRoi: lse.GetDetails().GetRoiModificationData().GetChosenRoi(),
						KfsDoc: &palPb.LoanDocument{
							SignUrl:            kfsLaS3PathResp.kfsS3Url,
							AwsDestinationPath: kfsLaS3PathResp.kfsAwsDestinationPath,
							ExpiryAt:           documentExpiryTime,
						},
						LoanAgreementDoc: &palPb.LoanDocument{
							SignUrl:            kfsLaS3PathResp.laS3Url,
							AwsDestinationPath: kfsLaS3PathResp.laAwsDestinationPath,
							ExpiryAt:           documentExpiryTime,
						},
						InstallmentAmount: roiModificationRes.GetInstallmentAmount(),
					},
				},
			}
			// mark success
			palActivity.MarkLoanStepSuccess(lse)
			if lseUpdateErr := p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
				palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
			}); lseUpdateErr != nil {
				lg.Error("error in updating lse", zap.Error(lseUpdateErr))
				return nil, errors.Wrapf(epifierrors.ErrTransient, "error in updating lse, err: %v", lseUpdateErr)
			}
			return &palActivityPb.PalActivityResponse{LoanStep: lse}, nil
		},
	)
	return execWorkRes, err
}

func (p *Processor) recalculateLoanValuesAfterRoiModificationAndUpdate(ctx context.Context, chosenRoi float64, lse *palPb.LoanStepExecution) error {
	lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
	if lrErr != nil {
		return errors.Wrapf(lrErr, "error in lr get by id")
	}

	vgRes, vgErr := p.lendenVgClient.GetPreDisbursementDetails(ctx, &lenden.GetPreDisbursementDetailsRequest{
		Header:         &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		Amount:         lr.GetDetails().GetLoanInfo().GetAmount(),
		Tenure:         lr.GetDetails().GetLoanInfo().GetTenureInMonths(),
		RateOfInterest: &decimalPb.Decimal{Value: decimal.NewFromFloat(chosenRoi).String()},
	})
	if te := epifigrpc.RPCError(vgRes, vgErr); te != nil {
		return errors.Wrapf(te, "error in vg res for pre disbursement details")
	}

	lr.GetDetails().GetLoanInfo().InterestRate = chosenRoi
	lr.GetDetails().GetLoanInfo().GetDeductions().ProcessingFee = vgRes.GetPreDisbursementDetails().GetProcessingFee()
	lr.GetDetails().GetLoanInfo().AprRate = vgRes.GetPreDisbursementDetails().GetApr()
	lr.GetDetails().GetLoanInfo().DisbursalAmount = vgRes.GetPreDisbursementDetails().GetDisbursalAmount()
	lr.GetDetails().GetLoanInfo().EmiAmount = vgRes.GetPreDisbursementDetails().GetInstallmentAmount()
	lr.GetDetails().GetLoanInfo().TotalPayable = vgRes.GetPreDisbursementDetails().GetTotalRepaymentAmount()

	updateErr := p.loanRequestDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS,
	})
	if updateErr != nil {
		return errors.Wrapf(updateErr, "error in updating lr")
	}
	return nil
}
