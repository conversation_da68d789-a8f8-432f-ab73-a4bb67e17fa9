package lenden

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	ldcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
)

const (
	documentExpiryInSeconds       = 2 * 60 * 60 // 10 hours
	documentExpiryBufferInSeconds = 5

	unsignedKFSDocS3PathFormat              = "LENDEN/documents/%v/%v/kfs.pdf"
	regeneratedKFSDocS3PathFormat           = "LENDEN/documents/%v/%v/kfs-regenerated.pdf"
	signedKFSDocS3PathFormat                = "LENDEN/documents/%v/%v/kfs-signed.pdf"
	resignedKFSDocS3PathFormat              = "LENDEN/documents/%v/%v/kfs-resigned.pdf"
	unsignedLoanAgreementDocS3PathFormat    = "LENDEN/documents/%v/%v/loan-agreement.pdf"
	regeneratedLoanAgreementDocS3PathFormat = "LENDEN/documents/%v/%v/loan-agreement-regenerated.pdf"
	signedLoanAgreementDocS3PathFormat      = "LENDEN/documents/%v/%v/loan-agreement-signed.pdf"
	resignedLoanAgreementDocS3PathFormat    = "LENDEN/documents/%v/%v/loan-agreement-resigned.pdf"
)

func (p *Processor) LdcGenerateKfsDocs(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		lg := activity.GetLogger(ctx)

		// fetch loan entities
		applicant, err := p.loanApplicantDao.GetByActorId(ctx, req.GetLoanStep().GetActorId())
		if err != nil {
			lg.Error("error while getting loan applicant for the user", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}

		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		// TODO(brijesh/anupam): add Error handling for retriable and non-retriable errors
		generateDocsRes, err := p.lendenVgClient.GenerateKfsLa(ctx, &ldcVgPb.GenerateKfsLaRequest{
			Header: &commonvgpb.RequestHeader{
				Vendor: commonvgpb.Vendor_LENDEN,
			},
			LoanId: lr.GetVendorRequestId(),
			UserId: applicant.GetVendorApplicantId(),
		})
		if err = epifigrpc.RPCError(generateDocsRes, err); err != nil {
			if generateDocsRes.GetStatus().IsAlreadyExists() {
				lse, err = p.signDocsAndUpdateLseLr(ctx, lse, req)
				if err != nil {
					lg.Error("error getting signed docs", zap.Error(err))
					return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error getting signed docs"))
				}
				return &palActivityPb.PalActivityResponse{LoanStep: lse}, nil
			}
			lg.Error("error generating unsigned KFS and LA docs", zap.Error(err))
			return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error generating unsigned KFS and LA docs"))
		}
		err = p.storeUnsignedDocsAndUpdateLseLr(ctx, generateDocsRes, lse, lr)
		if err != nil {
			lg.Error("error storing unsigned docs and updating loan step", zap.Error(err))
			return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error storing unsigned docs and updating loan step"))
		}
		return &palActivityPb.PalActivityResponse{LoanStep: lse}, nil
	})
	return actRes, actErr
}

func (p *Processor) storeUnsignedDocsAndUpdateLseLr(ctx context.Context, generateKfsRes *ldcVgPb.GenerateKfsLaResponse, lse *palPb.LoanStepExecution, lr *palPb.LoanRequest) error {
	if generateKfsRes.GetKfsDocUrl() == "" || generateKfsRes.GetLoanAgreementDocUrl() == "" {
		return errors.New("signed kfs doc url or LA doc url is empty")
	}
	kfsLaS3PathResp, err := p.getKfsAndLaS3Path(ctx, &getS3KfsDocsFromRawDataRequest{
		vendorKfsDocUrl:  generateKfsRes.GetKfsDocUrl(),
		vendorLaDocUrl:   generateKfsRes.GetLoanAgreementDocUrl(),
		actorId:          lse.GetActorId(),
		kfsDocPathFormat: unsignedKFSDocS3PathFormat,
		laDocPathFormat:  unsignedLoanAgreementDocS3PathFormat,
	})
	if err != nil {
		return errors.Wrap(err, "error getting S3 paths for KFS and LA")
	}
	documentExpiryTime := timestamppb.New(time.Now().Add(documentExpiryInSeconds * time.Second).Add(-documentExpiryBufferInSeconds * time.Second))
	lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
	lse.Details = &palPb.LoanStepExecutionDetails{
		Details: &palPb.LoanStepExecutionDetails_ESignStepData{
			ESignStepData: &palPb.ESignStepData{
				KfsDocument: &palPb.LoanDocument{
					SignUrl:            kfsLaS3PathResp.kfsS3Url,
					AwsDestinationPath: kfsLaS3PathResp.kfsAwsDestinationPath,
					ExpiryAt:           documentExpiryTime,
				},
				LoanAgreementDocument: &palPb.LoanDocument{
					SignUrl:            kfsLaS3PathResp.laS3Url,
					AwsDestinationPath: kfsLaS3PathResp.laAwsDestinationPath,
					ExpiryAt:           documentExpiryTime,
				},
			},
		},
	}
	err = p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
	})
	if err != nil {
		return errors.Wrap(err, "error updating lse")
	}
	deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
	nextAction := deeplinkProvider.GetInitiateESignScreen(deeplinkProvider.GetLoanHeader(), lr.GetId(), "")
	err = palActivity.UpdateNextActionInLoanRequest(ctx, p.loanRequestDao, lr.GetId(), nextAction)
	if err != nil {
		return errors.Wrap(err, "error updating next action in loan request")
	}
	return nil
}

func (p *Processor) LDCSignKFSLADocs(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	execWorkRes, err := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		lg := activity.GetLogger(ctx)
		lse, err := p.signDocsAndUpdateLseLr(ctx, lse, req)
		if err != nil {
			lg.Error("error signing loan documents", zap.Error(err))
			return nil, epifitemporal.NewTransientError(errors.Wrap(err, "error signing loan documents"))
		}
		return &palActivityPb.PalActivityResponse{LoanStep: lse}, nil
	})
	return execWorkRes, err
}

func (p *Processor) signDocsAndUpdateLseLr(ctx context.Context, lse *palPb.LoanStepExecution, req *palActivityPb.PalActivityRequest) (*palPb.LoanStepExecution, error) {
	res, err := p.signLoanDocuments(ctx, &signLoanDocumentsRequest{
		actorId:   lse.GetActorId(),
		loanReqId: lse.GetRefId(),
	})
	if err != nil {
		return nil, errors.Wrap(err, "error signing loan documents")
	}
	lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
	if lrErr != nil {
		return nil, errors.Wrap(err, "error in lr get by id")
	}

	// Update roi modification deadline in LR
	if lr.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().GetRoiModificationDetails() == nil {
		lr.GetDetails().Details = &palPb.LoanRequestDetails_LoanApplicationDetails{
			LoanApplicationDetails: &palPb.LoanApplicationDetails{
				Details: &palPb.LoanApplicationDetails_LdcLoanApplicationDetails{
					LdcLoanApplicationDetails: &palPb.LdcLoanApplicationDetails{
						RoiModificationDetails: &palPb.LdcLoanApplicationDetails_RoiModificationDetails{
							RoiModificationDeadline: res.roiModificationDeadline,
						},
					},
				},
			},
		}
	} else {
		lr.GetDetails().GetLoanApplicationDetails().GetLdcLoanApplicationDetails().GetRoiModificationDetails().RoiModificationDeadline = res.roiModificationDeadline
	}
	updateErr := p.loanRequestDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS})
	if updateErr != nil {
		return nil, errors.Wrap(updateErr, "error in updating lr")
	}

	// store the signed documents and roi modification deadline in the lse
	lse.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS

	// Setting the sub status allows the next activity (GetAgreementSignStatus),
	// which normally waits for user to sign documents, to be completed instantly.
	// Since documents are already signed, workflow will proceed directly
	// to LDCSignKFSLADocs activity to download the signed
	// KFS/LA documents and fetch the ROI modification deadline from Lenden.
	lse.SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_ESIGN_AGREEMENT_SIGNED
	if lse.GetDetails().GetESignStepData() == nil {
		lse.GetDetails().Details = &palPb.LoanStepExecutionDetails_ESignStepData{ESignStepData: &palPb.ESignStepData{}}
	}
	if lse.GetDetails().GetESignStepData().GetKfsDocument() == nil {
		lse.GetDetails().GetESignStepData().KfsDocument = &palPb.LoanDocument{}
	}
	if lse.GetDetails().GetESignStepData().GetLoanAgreementDocument() == nil {
		lse.GetDetails().GetESignStepData().LoanAgreementDocument = &palPb.LoanDocument{}
	}
	lse.GetDetails().GetESignStepData().GetKfsDocument().SignedDocS3Path = res.signedKeyFactStatementS3Path
	lse.GetDetails().GetESignStepData().GetLoanAgreementDocument().SignedDocS3Path = res.signedLoanAgreementS3Path
	lse.GetDetails().GetESignStepData().RoiModificationDeadline = res.roiModificationDeadline
	err = p.loanStepExecutionDao.Update(ctx, lse, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
	})
	if err != nil {
		return nil, errors.Wrap(err, "error updating lse")
	}
	deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: req.GetVendor(), LoanProgram: req.GetLoanProgram()})
	dl, err := deeplinkProvider.GetLoanApplicationStatusScreenDeepLink(ctx, deeplinkProvider.GetLoanHeader(), lr)
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan application status screen deeplink")
	}
	err = palActivity.UpdateNextActionInLoanRequest(ctx, p.loanRequestDao, lse.GetRefId(), dl)
	if err != nil {
		return nil, errors.Wrap(err, "error updating next action in loan request")
	}
	return lse, nil
}

type signLoanDocumentsRequest struct {
	actorId   string
	loanReqId string
}

type signLoanDocumentsResponse struct {
	signedKeyFactStatementS3Path string
	signedLoanAgreementS3Path    string
	roiModificationDeadline      *timestamppb.Timestamp
}

func (p *Processor) signLoanDocuments(ctx context.Context, req *signLoanDocumentsRequest) (*signLoanDocumentsResponse, error) {
	lg := activity.GetLogger(ctx)

	applicant, err := p.loanApplicantDao.GetByActorId(ctx, req.actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error in getting loan applicant")
	}
	loanRequest, err := p.loanRequestDao.GetById(ctx, req.loanReqId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan request by id")
	}
	ipAddress, err := p.rpcHelper.FetchIpAddress(ctx, req.actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error fetching ip address")
	}
	userDP, udpErr := p.userClient.GetUserDeviceProperties(ctx, &userPb.GetUserDevicePropertiesRequest{
		ActorId: req.actorId,
		PropertyTypes: []typesv2.DeviceProperty{
			typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID,
		},
	})
	if te := epifigrpc.RPCError(userDP, udpErr); te != nil {
		return nil, errors.Wrapf(te, "error getting device id")
	}
	deviceId := userDP.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID).GetDeviceId()

	locationRes, err := p.rpcHelper.GetUserDeviceLocation(ctx, req.actorId)
	if err != nil {
		return nil, errors.Wrap(err, "error getting user device location")
	}
	signKfsLaRes, err := p.lendenVgClient.SignKfsLa(ctx, &ldcVgPb.SignKfsLaRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_LENDEN},
		LoanId: loanRequest.GetVendorRequestId(),
		UserId: applicant.GetVendorApplicantId(),
		// TODO(Brijesh): Validate that the corresponding consents have been taken from user
		ConsentCodeList: []ldcVgPb.ConsentType{ldcVgPb.ConsentType_CONSENT_TYPE_GENERATE_KFS_LA},
		UserIp:          ipAddress,
		DeviceId:        deviceId,
		// A precision of 5 places is enough to pinpoint a device close to 1.1 m.
		// Ref: https://support.garmin.com/en-IN/?faq=hRMBoCTy5a7HqVkxukhHd8
		// This is more than enough for usual cases.
		Latitude:  strconv.FormatFloat(locationRes.LatLng.GetLatitude(), 'f', 10, 64),
		Longitude: strconv.FormatFloat(locationRes.LatLng.GetLongitude(), 'f', 10, 64),
		// TODO(Brijesh): Use the consent creation time instead of the current time.
		ConsentTime: timestamppb.Now(),
	})
	if err = epifigrpc.RPCError(signKfsLaRes, err); err != nil {
		return nil, errors.Wrap(err, "error signing kfs and la")
	}
	if signKfsLaRes.GetKfsDocUrl() == "" || signKfsLaRes.GetLoanAgreementDocUrl() == "" {
		return nil, errors.Errorf("signed kfs doc url or LA doc url is empty")
	}

	// this will store the KFS and LA document in s3
	kfsLaS3PathResp, err := p.getKfsAndLaS3Path(ctx, &getS3KfsDocsFromRawDataRequest{
		vendorKfsDocUrl:  signKfsLaRes.GetKfsDocUrl(),
		vendorLaDocUrl:   signKfsLaRes.GetLoanAgreementDocUrl(),
		actorId:          loanRequest.GetActorId(),
		kfsDocPathFormat: signedKFSDocS3PathFormat,
		laDocPathFormat:  signedLoanAgreementDocS3PathFormat,
	})
	if err != nil {
		lg.Error("error in getting s3 urls for kfs and la", zap.Error(err))
		return nil, errors.Wrapf(err, "error in getting s3 urls for kfs and la")
	}

	// if time deadline is nil or time unix is exactly zero, then its an issue via vendor
	if signKfsLaRes.GetModifyRoiExpirationTime().AsTime().Equal(time.Unix(0, 0)) {
		return nil, errors.Errorf("invalid roi modification deadline: %s", signKfsLaRes.GetModifyRoiExpirationTime().AsTime().String())
	}

	return &signLoanDocumentsResponse{
		signedKeyFactStatementS3Path: kfsLaS3PathResp.kfsAwsDestinationPath,
		signedLoanAgreementS3Path:    kfsLaS3PathResp.laAwsDestinationPath,
		roiModificationDeadline:      signKfsLaRes.GetModifyRoiExpirationTime(),
	}, nil
}
