package stock_guardian

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	gwSgApplicationPb "github.com/epifi/gamma/api/stockguardian/sgapigateway/application"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	deeplinkPal "github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
)

func (p *Processor) SgInitiateEmploymentCheck(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		// initiate employment check
		initEmploymentCheck, initEmploymentCheckErr := p.sgApplicationApiGateway.InitEmploymentCheck(ctx, &gwSgApplicationPb.InitEmploymentCheckRequest{
			LoanHeader: &gwSgApplicationPb.LoanHeader{
				ClientId: clientId,
			},
			ApplicationId: lr.GetVendorRequestId(),
		})
		if te := epifigrpc.RPCError(initEmploymentCheck, initEmploymentCheckErr); te != nil {
			if isEmploymentCheckFailed(initEmploymentCheck.GetStatus()) {
				lg.Info("employment check failed for the customer", zap.String("application_id", lr.GetVendorRequestId()))
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			} else if isEmploymentCheckPending(initEmploymentCheck.GetStatus()) {
				return res, nil
			} else if isEmploymentCheckSkipped(initEmploymentCheck.GetStatus()) {
				lg.Info("employment check skipped for the customer", zap.String("application_id", lr.GetVendorRequestId()))
				palActivity.MarkLoanStepSuccessWithSubStatus(lse, palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_SKIPPED_NOT_NEEDED, res)
				return res, nil
			}
			lg.Error("failed to initiate employment check", zap.Error(te))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to initiate employment check, err: %v", te))
		}

		return res, nil
	})
	return actRes, actErr
}

func (p *Processor) SgEmploymentCheckStatus(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	actRes, actErr := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep: lse,
		}
		lg := activity.GetLogger(ctx)

		// fetch loan request
		lr, lrErr := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if lrErr != nil {
			lg.Error("error in lr get by id", zap.Error(lrErr))
			if errors.Is(lrErr, epifierrors.ErrRecordNotFound) {
				palActivity.MarkLoanStepFail(lse)
				return res, nil
			}
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr get by id, err: %v", lrErr))
		}

		deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplinkPal.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
		res.NextAction = deeplinkProvider.GetManualReviewWaitScreen(ctx, &provider.GetManualReviewWaitScreenRequest{
			LoanHeader:     deeplinkProvider.GetLoanHeader(),
			LoanRequestId:  lr.GetId(),
			CenterImageUrl: "https://epifi-icons.s3.ap-south-1.amazonaws.com/preapprovedloan/vkycInReview.png",
			Title:          "Your KYC documents are in review",
			Subtitle:       "Once document verification is complete, we'll notify you. Typically, 90% of applications are reviewed within 8 hours.",
			BottomCard: &provider.ReviewScreenBottomCard{
				Title:    "In progress: Official’s approval",
				Subtitle: "90% of applications are reviewed within 8 hours",
			},
		})

		stageStatus, stageErr := p.getStageStatusFromVendor(ctx, lr.GetVendorRequestId(), gwSgApplicationPb.LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_EMPLOYMENT_CHECK)
		if stageErr != nil {
			lg.Error("got a failure stage status at employment check", zap.Error(stageErr))
			return nil, stageErr
		}

		if stageStatus == gwSgApplicationPb.LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_SUCCESS {
			// mark LSE success
			palActivity.MarkLoanStepSuccessV2(lse, res)
			return res, nil
		} else {
			lg.Info("employment check stage is not in success stage yet", zap.String("stage_status", stageStatus.String()))
			return res, errors.Wrap(epifierrors.ErrTransient, "employment check stage is not in success stage yet!")
		}
	})
	return actRes, actErr
}

// nolint:staticcheck
func isEmploymentCheckFailed(req *rpc.Status) bool {
	if req.GetCode() == uint32(gwSgApplicationPb.InitEmploymentCheckResponse_STATUS_FAILED) {
		return true
	}
	return false
}

// nolint:staticcheck
func isEmploymentCheckPending(req *rpc.Status) bool {
	if req.GetCode() == uint32(gwSgApplicationPb.InitEmploymentCheckResponse_STATUS_PENDING) {
		return true
	}
	return false
}

// nolint:staticcheck
func isEmploymentCheckSkipped(req *rpc.Status) bool {
	if req.GetCode() == uint32(gwSgApplicationPb.InitEmploymentCheckResponse_STATUS_NOT_REQUIRED) {
		return true
	}
	return false
}
