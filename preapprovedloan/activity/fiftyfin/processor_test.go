// nolint:goimports
package fiftyfin_test

import (
	"context"
	"testing"

	"github.com/epifi/be-common/api/rpc"

	dateTimeMocks "github.com/epifi/be-common/pkg/datetime/mocks"
	"github.com/epifi/be-common/pkg/epifierrors"
	mock_events "github.com/epifi/be-common/pkg/events/mocks"
	mockIdGen "github.com/epifi/be-common/pkg/idgen/mocks"
	"github.com/epifi/be-common/pkg/money"

	celestialMocks "github.com/epifi/be-common/api/celestial/mocks"
	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"

	mockActor "github.com/epifi/gamma/api/actor/mocks"
	catalogMock "github.com/epifi/gamma/api/investment/mutualfund/catalog/mocks"
	"github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	"github.com/epifi/gamma/api/preapprovedloan/enums"
	securedLoanaSrvcMock "github.com/epifi/gamma/api/preapprovedloan/secured_loans/mocks"
	mockUser "github.com/epifi/gamma/api/user/mocks"
	ffVgPb "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	vgMocks "github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin/mocks"
	ffVendorPb "github.com/epifi/gamma/api/vendors/fiftyfin"
	"github.com/epifi/gamma/preapprovedloan/activity/fiftyfin"
	plDaoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/mocks"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"
	ffDlProvider "github.com/epifi/gamma/preapprovedloan/deeplink/provider/fiftyfin"
	helper2 "github.com/epifi/gamma/preapprovedloan/helper"
	offer_manager_mock "github.com/epifi/gamma/preapprovedloan/offer_manager/mocks"
	slDaoMocks "github.com/epifi/gamma/preapprovedloan/secured_loans/dao/mocks"
	mocks2 "github.com/epifi/gamma/preapprovedloan/vendor_data_provider/fiftyfin/mocks"

	"github.com/golang/mock/gomock"
	"google.golang.org/protobuf/proto"
)

type processorMocks struct {
	loanApplicantDao       *plDaoMocks.MockLoanApplicantDao
	loanRequestDao         *plDaoMocks.MockLoanRequestsDao
	loanStepExecutionDao   *plDaoMocks.MockLoanStepExecutionsDao
	loanOfferDao           *plDaoMocks.MockLoanOffersDao
	loanAccountDao         *plDaoMocks.MockLoanAccountsDao
	loanInstallmentInfoDao *plDaoMocks.MockLoanInstallmentInfoDao
	loanActivityDao        *plDaoMocks.MockLoanActivityDao
	deeplinkFactory        *mocks.MockIDeeplinkProviderFactory
	ffVgClient             *vgMocks.MockFiftyFinClient
	fetchedAssetDao        *slDaoMocks.MockFetchedAssetDao
	securedLoansClient     *securedLoanaSrvcMock.MockSecuredLoansClient
	offerManagerFactory    *offer_manager_mock.MockOfferManagerFactory
	celestialClient        *celestialMocks.MockCelestialClient
	mfCatalogClient        *catalogMock.MockCatalogManagerClient
	actorClient            *mockActor.MockActorClient
	usersClient            *mockUser.MockUsersClient
	time                   *dateTimeMocks.MockTime
	uuidGenerator          *mockIdGen.MockIUuidGenerator
	eventsBroker           *mock_events.MockBroker
	vendorDataProvider     *mocks2.MockVendorDataProvider
}

func initProcessorMocks(ctrl *gomock.Controller) *processorMocks {
	return &processorMocks{
		loanApplicantDao:     plDaoMocks.NewMockLoanApplicantDao(ctrl),
		loanRequestDao:       plDaoMocks.NewMockLoanRequestsDao(ctrl),
		loanStepExecutionDao: plDaoMocks.NewMockLoanStepExecutionsDao(ctrl),
		loanOfferDao:         plDaoMocks.NewMockLoanOffersDao(ctrl),
		deeplinkFactory:      mocks.NewMockIDeeplinkProviderFactory(ctrl),
		ffVgClient:           vgMocks.NewMockFiftyFinClient(ctrl),
		fetchedAssetDao:      slDaoMocks.NewMockFetchedAssetDao(ctrl),
		securedLoansClient:   securedLoanaSrvcMock.NewMockSecuredLoansClient(ctrl),
		offerManagerFactory:  offer_manager_mock.NewMockOfferManagerFactory(ctrl),
		celestialClient:      celestialMocks.NewMockCelestialClient(ctrl),
		mfCatalogClient:      catalogMock.NewMockCatalogManagerClient(ctrl),
		actorClient:          mockActor.NewMockActorClient(ctrl),
		usersClient:          mockUser.NewMockUsersClient(ctrl),
		time:                 dateTimeMocks.NewMockTime(ctrl),
		uuidGenerator:        mockIdGen.NewMockIUuidGenerator(ctrl),
		eventsBroker:         mock_events.NewMockBroker(ctrl),
		vendorDataProvider:   mocks2.NewMockVendorDataProvider(ctrl),
	}
}
func newActivityProcessorWithMocks(md *processorMocks) *fiftyfin.Processor {
	baseProvider := baseprovider.NewProvider(nil, nil, nil, nil, nil, nil)
	ffProvider := ffDlProvider.NewProvider(baseProvider, md.mfCatalogClient, md.uuidGenerator)
	dlFactory := deeplink.NewDeeplinkProviderFactory(baseProvider, nil, nil, nil, nil, nil, nil, ffProvider, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	rpcHelper := helper2.NewRpcHelper(nil, md.actorClient, md.usersClient, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil)
	return fiftyfin.NewProcessor(md.loanApplicantDao, md.loanRequestDao, md.loanOfferDao, md.loanAccountDao, md.loanActivityDao, md.loanInstallmentInfoDao, nil, dlFactory, nil, md.ffVgClient, md.fetchedAssetDao, md.loanStepExecutionDao, rpcHelper, ffProvider, md.securedLoansClient, md.offerManagerFactory, nil, nil, nil, nil, md.celestialClient, nil, md.time, nil, md.eventsBroker, nil, md.uuidGenerator, nil, gconf, nil, md.vendorDataProvider)
}

var (
	loanOffer1 = &preapprovedloan.LoanOffer{
		Id: "loan-offer-id-1",
		OfferConstraints: &preapprovedloan.OfferConstraints{
			AdditionalConstraints: &preapprovedloan.OfferConstraints_FiftyfinLamfConstraintInfo{
				FiftyfinLamfConstraintInfo: &preapprovedloan.FiftyFinLamfConstraintInfo{
					MfPortfolioConstraint: &preapprovedloan.MfPortfolioConstraint{
						ApprovedHoldings: []*preapprovedloan.MfPortfolioConstraint_Holding{
							{
								MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_CAMS,
								Id:                    123,
								Isin:                  "********",
								Quantity:              100,
								Price:                 money.ParseFloat(12.9, "INR"),
								TotalAmount:           money.ParseFloat(1290.0, "INR"),
								LoanToValueRatio:      0.5,
								MaxLoanAmount:         money.ParseFloat(645.0, "INR"),
								FolioNumber:           "123456789",
								AmcCode:               "AXIS_SC_100",
								SchemeCode:            "Scheme_1",
								VendorId:              12,
								VendorLtv:             0.5,
								VendorMaxLoanAmount:   money.ParseFloat(645.0, "INR"),
								DiscountFactor:        0.05,
							},
							{
								MutualFundFacilitator: preapprovedloan.MutualFundFacilitator_MUTUAL_FUND_FACILITATOR_KARVY,
								Id:                    124,
								Isin:                  "90987679",
								Quantity:              1000,
								Price:                 money.ParseFloat(18.9, "INR"),
								TotalAmount:           money.ParseFloat(18900.0, "INR"),
								LoanToValueRatio:      0.5,
								MaxLoanAmount:         money.ParseFloat(9450.0, "INR"),
								FolioNumber:           "123452389",
								AmcCode:               "AXIS_SC_100",
								SchemeCode:            "Scheme_1",
								VendorId:              14,
								VendorLtv:             0.5,
								VendorMaxLoanAmount:   money.ParseFloat(645.0, "INR"),
								DiscountFactor:        0.05,
							},
						},
						UnapprovedHoldings: nil,
					},
				},
			},
		},
	}
	loanRequest1 = &preapprovedloan.LoanRequest{
		Id:      "loan-request-1",
		ActorId: "actor-1",
		OfferId: "loan-offer-id",
		Details: &preapprovedloan.LoanRequestDetails{
			LoanInfo: &preapprovedloan.LoanRequestDetails_LoanInfo{
				Amount:         money.ParseFloat(2000.0, "INR"),
				TenureInMonths: 12,
				InterestRate:   10.5,
				PledgeDetails: &preapprovedloan.PledgeDetails{
					MutualFunds: &preapprovedloan.PledgeDetails_MutualFunds{
						Schemes: []*preapprovedloan.PledgeDetails_MutualFunds_Scheme{
							{
								Isin:     "********",
								Quantity: 100,
							},
							{
								Isin:     "90987679",
								Quantity: 1000,
							},
						},
					},
				},
			},
		},
	}
)

// func TestProcessor_PortfolioFetch(t *testing.T) {
//	type args struct {
//		ctx context.Context
//		req *palActivityPb.PalActivityRequest
//	}
//	tests := []struct {
//		name      string
//		args      args
//		want      *palActivityPb.PalActivityResponse
//		mockFunc  func(md *processorMocks)
//		wantErr   bool
//		assertErr func(err error) bool
//	}{
//		{
//			name: "should fail with permanent error as lse in cancelled state",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					Vendor:        preapprovedloan.Vendor_FIFTYFIN,
//					LoanProgram:   preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED}, nil).AnyTimes()
//				md.securedLoansClient.EXPECT().CancelPortfolioFetch(gomock.Any(), &secured_loans.CancelPortfolioFetchRequest{LoanHeader: &preapprovedloan.LoanHeader{
//					LoanProgram: preapprovedloan.LoanProgram_LOAN_PROGRAM_LAMF,
//					Vendor:      preapprovedloan.Vendor_FIFTYFIN,
//				}}).Return(&secured_loans.CancelPortfolioFetchResponse{
//					Status: rpc.StatusOk(),
//				}, nil)
//			},
//			wantErr: true,
//			want:    nil,
//			assertErr: func(err error) bool {
//				return !epifitemporal.IsRetryableError(err)
//			},
//		},
//		{
//			name: "permanent failure, lse not found",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound).AnyTimes()
//			},
//			wantErr: true,
//			want:    nil,
//			assertErr: func(err error) bool {
//				return !epifitemporal.IsRetryableError(err)
//			},
//		},
//		{
//			name: "transient failure, fails to fetch lse",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrInvalidArgument).AnyTimes()
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, portfolioFetchStatus rpc errors",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.securedLoansClient.EXPECT().GetPortfolioFetchStatus(gomock.Any(), gomock.Any()).Return(&secured_loans.GetPortfolioFetchStatusResponse{Status: rpc.StatusInternal()}, nil)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, error while creating new pf fetch request",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.securedLoansClient.EXPECT().GetPortfolioFetchStatus(gomock.Any(), gomock.Any()).Return(&secured_loans.GetPortfolioFetchStatusResponse{Status: rpc.StatusRecordNotFound()}, nil)
//				md.securedLoansClient.EXPECT().InitiatePortfolioFetch(gomock.Any(), gomock.Any()).Return(&secured_loans.InitiatePortfolioFetchResponse{Status: rpc.StatusInternal()}, nil)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, error while updating lr next action",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.securedLoansClient.EXPECT().GetPortfolioFetchStatus(gomock.Any(), gomock.Any()).Return(&secured_loans.GetPortfolioFetchStatusResponse{Status: rpc.StatusRecordNotFound()}, nil)
//				md.securedLoansClient.EXPECT().InitiatePortfolioFetch(gomock.Any(), gomock.Any()).Return(&secured_loans.InitiatePortfolioFetchResponse{Status: rpc.StatusOk()}, nil)
//				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{}, nil)
//				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(epifierrors.ErrRecordNotFound).AnyTimes()
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, new pf fetch request successfully created",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.securedLoansClient.EXPECT().GetPortfolioFetchStatus(gomock.Any(), gomock.Any()).Return(&secured_loans.GetPortfolioFetchStatusResponse{Status: rpc.StatusRecordNotFound()}, nil)
//				md.securedLoansClient.EXPECT().InitiatePortfolioFetch(gomock.Any(), gomock.Any()).Return(&secured_loans.InitiatePortfolioFetchResponse{Status: rpc.StatusOk()}, nil)
//				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{}, nil)
//				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, pf fetch still in progress",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.securedLoansClient.EXPECT().GetPortfolioFetchStatus(gomock.Any(), gomock.Any()).Return(&secured_loans.GetPortfolioFetchStatusResponse{Status: rpc.StatusOk(), PortfolioFetchStatus: preapprovedloan.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING}, nil)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "permanent failure, pf fetch failed",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.securedLoansClient.EXPECT().GetPortfolioFetchStatus(gomock.Any(), gomock.Any()).Return(&secured_loans.GetPortfolioFetchStatusResponse{Status: rpc.StatusOk(), PortfolioFetchStatus: preapprovedloan.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED}, nil)
//				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{}, nil)
//				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//			},
//			wantErr: true,
//			want:    nil,
//			assertErr: func(err error) bool {
//				return !epifitemporal.IsRetryableError(err)
//			},
//		},
//		{
//			name: "success case, pf fetch gives success status",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.securedLoansClient.EXPECT().GetPortfolioFetchStatus(gomock.Any(), gomock.Any()).Return(&secured_loans.GetPortfolioFetchStatusResponse{Status: rpc.StatusOk(), PortfolioFetchStatus: preapprovedloan.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS}, nil)
//			},
//			wantErr:   false,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			ctrl := gomock.NewController(t)
//			defer ctrl.Finish()
//			mockFields := initProcessorMocks(ctrl)
//			tt.mockFunc(mockFields)
//			p := newActivityProcessorWithMocks(mockFields)
//			env := wts.NewTestActivityEnvironment()
//			env.RegisterActivity(p)
//			var result *palActivityPb.PalActivityResponse
//			got, err := env.ExecuteActivity(palNs.PortfolioFetch, tt.args.req)
//			if got != nil {
//				getErr := got.Get(&result)
//				if getErr != nil {
//					t.Errorf("PortfolioFetch() error = %v failed to fetch type value from convertible", err)
//					return
//				}
//			}
//
//			switch {
//			case (err != nil) != tt.wantErr:
//				t.Errorf("PortfolioFetch() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			case tt.wantErr && !tt.assertErr(err):
//				t.Errorf("PortfolioFetch() error = %v assertion failed", err)
//				return
//			case tt.want != nil && !proto.Equal(result, tt.want):
//				t.Errorf("PortfolioFetch() got = %v, want %v", result, tt.want)
//				return
//			}
//		})
//	}
// }

// func TestProcessor_SetOtpDataForLienMark(t *testing.T) {
//	type args struct {
//		ctx context.Context
//		req *palActivityPb.PalActivityRequest
//	}
//	tests := []struct {
//		name      string
//		args      args
//		want      *palActivityPb.PalActivityResponse
//		mockFunc  func(md *processorMocks)
//		wantErr   bool
//		assertErr func(err error) bool
//	}{
//		{
//			name: "transient failure, error in getting loan applicant",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{OfferId: "offerId"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId").Return(&preapprovedloan.LoanOffer{ValidTill: timestamp.New(timestamp.Now().AsTime().Add(1000 * time.Hour))}, nil)
//				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, error in getting loan request",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{OfferId: "offerId"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId").Return(&preapprovedloan.LoanOffer{ValidTill: timestamp.New(timestamp.Now().AsTime().Add(1000 * time.Hour))}, nil)
//				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant"}, nil)
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, error in getting loan offer",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{OfferId: "offerId"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId").Return(&preapprovedloan.LoanOffer{ValidTill: timestamp.New(timestamp.Now().AsTime().Add(1000 * time.Hour))}, nil)
//				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant"}, nil)
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{Id: "loan-request-id"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrContextCanceled)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, error in converting user id to int",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{OfferId: "offerId"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId").Return(&preapprovedloan.LoanOffer{ValidTill: timestamp.New(timestamp.Now().AsTime().Add(1000 * time.Hour))}, nil)
//				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "non-int-applicant-id"}, nil)
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{Id: "loan-request-id"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanOffer{Id: "loan-offer-id"}, nil)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, error in vg call to fetch user",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{OfferId: "offerId"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId").Return(&preapprovedloan.LoanOffer{ValidTill: timestamp.New(timestamp.Now().AsTime().Add(1000 * time.Hour))}, nil)
//				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{Id: "loan-request-id"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanOffer{Id: "loan-offer-id"}, nil)
//				md.ffVgClient.EXPECT().FetchUser(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchUserResponse{Status: rpc.StatusInternal()}, nil)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, error in getting fetched asset value",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{OfferId: "offerId"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId").Return(&preapprovedloan.LoanOffer{ValidTill: timestamp.New(timestamp.Now().AsTime().Add(1000 * time.Hour))}, nil)
//				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12335"}, nil)
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{Id: "loan-request-id"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanOffer{Id: "loan-offer-id"}, nil)
//				md.ffVgClient.EXPECT().FetchUser(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchUserResponse{Status: rpc.StatusOk()}, nil)
//				md.fetchedAssetDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(nil, epifierrors.ErrRecordNotFound)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, error in updating lse",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{OfferId: "offerId"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId").Return(&preapprovedloan.LoanOffer{ValidTill: timestamp.New(timestamp.Now().AsTime().Add(1000 * time.Hour))}, nil)
//				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12335"}, nil)
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(loanRequest1, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(loanOffer1, nil)
//				md.ffVgClient.EXPECT().FetchUser(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchUserResponse{Status: rpc.StatusOk()}, nil)
//				md.fetchedAssetDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&preapprovedloan.FetchedAsset{}, nil)
//				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(epifierrors.ErrContextCanceled)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "transient failure, error in updating LR next action",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS, StepName: preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MARK_LIEN}, nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{OfferId: "offerId"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId").Return(&preapprovedloan.LoanOffer{ValidTill: timestamp.New(timestamp.Now().AsTime().Add(1000 * time.Hour))}, nil)
//				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12335"}, nil)
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(loanRequest1, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(loanOffer1, nil)
//				md.ffVgClient.EXPECT().FetchUser(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchUserResponse{Status: rpc.StatusOk()}, nil)
//				md.fetchedAssetDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&preapprovedloan.FetchedAsset{}, nil)
//				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{}, nil)
//				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(epifierrors.ErrContextCanceled)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "success case",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS, StepName: preapprovedloan.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_MARK_LIEN}, nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{OfferId: "offerId"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId").Return(&preapprovedloan.LoanOffer{ValidTill: timestamp.New(timestamp.Now().AsTime().Add(1000 * time.Hour))}, nil)
//				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgram(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12335"}, nil)
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(loanRequest1, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(loanOffer1, nil)
//				md.ffVgClient.EXPECT().FetchUser(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchUserResponse{Status: rpc.StatusOk()}, nil)
//				md.fetchedAssetDao.EXPECT().GetByActorId(gomock.Any(), gomock.Any()).Return(&preapprovedloan.FetchedAsset{}, nil)
//				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{}, nil)
//				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			},
//			wantErr: false,
//			want:    nil,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			ctrl := gomock.NewController(t)
//			defer ctrl.Finish()
//			mockFields := initProcessorMocks(ctrl)
//			tt.mockFunc(mockFields)
//			p := newActivityProcessorWithMocks(mockFields)
//			env := wts.NewTestActivityEnvironment()
//			env.RegisterActivity(p)
//			var result *palActivityPb.PalActivityResponse
//			got, err := env.ExecuteActivity(palNs.SetOtpDataForLienMark, tt.args.req)
//			if got != nil {
//				getErr := got.Get(&result)
//				if getErr != nil {
//					t.Errorf("SetOtpDataForLienMark() error = %v failed to fetch type value from convertible", err)
//					return
//				}
//			}
//
//			switch {
//			case (err != nil) != tt.wantErr:
//				t.Errorf("SetOtpDataForLienMark() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			case tt.wantErr && !tt.assertErr(err):
//				t.Errorf("SetOtpDataForLienMark() error = %v assertion failed", err)
//				return
//			case tt.want != nil && !proto.Equal(result, tt.want):
//				t.Errorf("SetOtpDataForLienMark() got = %v, want %v", result, tt.want)
//				return
//			}
//		})
//	}
// }

// func TestProcessor_GetLienMarkOtpStatus(t *testing.T) {
//	type args struct {
//		ctx context.Context
//		req *palActivityPb.PalActivityRequest
//	}
//	tests := []struct {
//		name      string
//		args      args
//		want      *palActivityPb.PalActivityResponse
//		mockFunc  func(md *processorMocks)
//		wantErr   bool
//		assertErr func(err error) bool
//	}{
//		{
//			name: "transient error, otp not entered by the user",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
//				md.loanRequestDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanRequest{OfferId: "offerId"}, nil)
//				md.loanOfferDao.EXPECT().GetById(gomock.Any(), "offerId").Return(&preapprovedloan.LoanOffer{ValidTill: timestamp.New(timestamp.Now().AsTime().Add(1000 * time.Hour))}, nil)
//			},
//			wantErr:   true,
//			want:      nil,
//			assertErr: epifitemporal.IsRetryableError,
//		},
//		{
//			name: "success case, OTP verified by the user",
//			args: args{
//				ctx: context.Background(),
//				req: &palActivityPb.PalActivityRequest{
//					RequestHeader: nil,
//					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
//				},
//			},
//			mockFunc: func(md *processorMocks) {
//				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS, SubStatus: preapprovedloan.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_OTP_VERIFIED}, nil).AnyTimes()
//			},
//			wantErr: false,
//			want:    nil,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			ctrl := gomock.NewController(t)
//			defer ctrl.Finish()
//			mockFields := initProcessorMocks(ctrl)
//			tt.mockFunc(mockFields)
//			p := newActivityProcessorWithMocks(mockFields)
//			env := wts.NewTestActivityEnvironment()
//			env.RegisterActivity(p)
//			var result *palActivityPb.PalActivityResponse
//			got, err := env.ExecuteActivity(palNs.GetLienMarkOtpStatus, tt.args.req)
//			if got != nil {
//				getErr := got.Get(&result)
//				if getErr != nil {
//					t.Errorf("GetLienMarkOtpStatus() error = %v failed to fetch type value from convertible", err)
//					return
//				}
//			}
//
//			switch {
//			case (err != nil) != tt.wantErr:
//				t.Errorf("GetLienMarkOtpStatus() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			case tt.wantErr && !tt.assertErr(err):
//				t.Errorf("GetLienMarkOtpStatus() error = %v assertion failed", err)
//				return
//			case tt.want != nil && !proto.Equal(result, tt.want):
//				t.Errorf("GetLienMarkOtpStatus() got = %v, want %v", result, tt.want)
//				return
//			}
//		})
//	}
// }

func TestProcessor_ValidateMarkedLien(t *testing.T) {
	t.Parallel()
	type args struct {
		ctx context.Context
		req *palActivityPb.PalActivityRequest
	}
	tests := []struct {
		name      string
		args      args
		want      *palActivityPb.PalActivityResponse
		mockFunc  func(md *processorMocks)
		wantErr   bool
		assertErr func(err error) bool
	}{
		{
			name: "transient failure, error in getting loan applicant",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(nil, epifierrors.ErrRecordNotFound)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "transient failure, error in converting user id to int",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "non-int-applicant-id"}, nil)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "transient failure, error fetching loan portfolio from vendor",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
				md.ffVgClient.EXPECT().FetchLoanPortfolio(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchLoanPortfolioResponse{Status: rpc.StatusInternal()}, nil)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "success case, no asset in lien marked state",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
				md.ffVgClient.EXPECT().FetchLoanPortfolio(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchLoanPortfolioResponse{Status: rpc.StatusOk()}, nil)
			},
			wantErr: false,
			want:    nil,
		},
		{
			name: "transient failure, asset in lien marked state, OTP verified",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS, Details: &preapprovedloan.LoanStepExecutionDetails{
					Details: &preapprovedloan.LoanStepExecutionDetails_OtpVerificationData{
						OtpVerificationData: &preapprovedloan.OtpVerificationData{
							OtpData: []*preapprovedloan.OtpVerificationData_OtpData{
								{
									OtpType:   preapprovedloan.OtpType_OTP_TYPE_CAMS_LIEN_MARK,
									OtpStatus: preapprovedloan.OtpStatus_OTP_STATUS_SUCCESS,
								},
							},
						}},
				}}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), enums.LoanProgramVersion_LOAN_PROGRAM_VERSION_V1).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
				md.ffVgClient.EXPECT().FetchLoanPortfolio(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchLoanPortfolioResponse{Status: rpc.StatusOk(), Portfolio: &ffVendorPb.MfLoanPortfolio{
					LienMarked: []*ffVendorPb.MfHolding{
						{
							Id:       123,
							UserId:   12345,
							Platform: "Cams",
						},
					},
				}}, nil)
			},
			wantErr:   true,
			want:      nil,
			assertErr: epifitemporal.IsRetryableError,
		},
		{
			name: "success case, asset in lien marked state, OTP not verified",
			args: args{
				ctx: context.Background(),
				req: &palActivityPb.PalActivityRequest{
					RequestHeader: nil,
					LoanStep:      &preapprovedloan.LoanStepExecution{Id: "random-ls-id"},
				},
			},
			mockFunc: func(md *processorMocks) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanStepExecution{Status: preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS, Details: &preapprovedloan.LoanStepExecutionDetails{
					Details: &preapprovedloan.LoanStepExecutionDetails_OtpVerificationData{
						OtpVerificationData: &preapprovedloan.OtpVerificationData{
							OtpData: []*preapprovedloan.OtpVerificationData_OtpData{
								{
									OtpType:   preapprovedloan.OtpType_OTP_TYPE_KARVY_LIEN_MARK,
									OtpStatus: preapprovedloan.OtpStatus_OTP_STATUS_FAILED,
								},
							},
						}},
				}}, nil).AnyTimes()
				md.loanApplicantDao.EXPECT().GetByActorIdAndVendorAndLoanProgramAndProgramVersion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&preapprovedloan.LoanApplicant{Id: "random-applicant", VendorApplicantId: "12345"}, nil)
				md.ffVgClient.EXPECT().FetchLoanPortfolio(gomock.Any(), gomock.Any()).Return(&ffVgPb.FetchLoanPortfolioResponse{Status: rpc.StatusOk(), Portfolio: &ffVendorPb.MfLoanPortfolio{
					LienMarked: []*ffVendorPb.MfHolding{
						{
							Id:       123,
							UserId:   12345,
							Platform: "Karvy",
						},
					},
				}}, nil)
			},
			wantErr: false,
			want:    nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockFields := initProcessorMocks(ctrl)
			tt.mockFunc(mockFields)
			p := newActivityProcessorWithMocks(mockFields)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(p)
			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.ValidateMarkedLien, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("ValidateMarkedLien() error = %v failed to fetch type value from convertible", err)
					return
				}
			}

			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("ValidateMarkedLien() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("ValidateMarkedLien() error = %v assertion failed", err)
				return
			case tt.want != nil && !proto.Equal(result, tt.want):
				t.Errorf("ValidateMarkedLien() got = %v, want %v", result, tt.want)
				return
			}
		})
	}
}
