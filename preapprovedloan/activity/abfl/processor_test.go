// nolint: dogsled
package abfl_test

import (
	"context"

	"github.com/golang/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/money"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	storageV2Mocks "github.com/epifi/be-common/pkg/storage/v2/mocks"

	actormocks "github.com/epifi/gamma/api/actor/mocks"
	mockLocation "github.com/epifi/gamma/api/auth/location/mocks"
	"github.com/epifi/gamma/api/preapprovedloan"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	mockSavings "github.com/epifi/gamma/api/savings/mocks"
	userPb "github.com/epifi/gamma/api/user"
	obfuscatorMocks "github.com/epifi/gamma/api/user/obfuscator/mocks"
	mockOnb "github.com/epifi/gamma/api/user/onboarding/mocks"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl/mocks"
	defaultValueCalculator "github.com/epifi/gamma/preapprovedloan/calculator/defaultvalue"
	calculatorProvidersWrapper "github.com/epifi/gamma/preapprovedloan/calculator/providers/wrapper"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/config/worker"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/baseprovider"

	s3Mocks "github.com/epifi/be-common/pkg/aws/v2/s3/mocks"

	usersMocks "github.com/epifi/gamma/api/user/mocks"
	plDaoMocks "github.com/epifi/gamma/preapprovedloan/dao/mocks"

	myabfl "github.com/epifi/gamma/preapprovedloan/activity/abfl"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider/abfl"
	"github.com/epifi/gamma/preapprovedloan/helper"
)

var (
	loanOffer1 = &preapprovedloan.LoanOffer{
		Id:               "loan-offer-id-1",
		OfferConstraints: &preapprovedloan.OfferConstraints{},
	}
	loanOffer2 = &preapprovedloan.LoanOffer{
		Id: "loan-offer-id-2",
		OfferConstraints: &preapprovedloan.OfferConstraints{
			MinLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        50000,
			},
			MaxLoanAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        100000,
			},
			MinTenureMonths: 12,
			MaxEmiAmount: &moneyPb.Money{
				CurrencyCode: "INR",
				Units:        50000,
			},
			MaxTenureMonths: 48,
		},
		ProcessingInfo: &preapprovedloan.OfferProcessingInfo{
			InterestRate: []*preapprovedloan.RangeData{
				{
					Start: 50000,
					End:   100000,
					Value: &palPb.RangeData_Percentage{Percentage: 12},
				},
			},
			ProcessingFee: []*preapprovedloan.RangeData{
				{
					Start: 50000,
					End:   100000,
					Value: &palPb.RangeData_Percentage{Percentage: 2},
				},
			},
		},
	}
	loanRequest1 = &preapprovedloan.LoanRequest{
		Id:              "lr-id-1",
		ActorId:         "actor-id-1",
		OfferId:         "loan-offer-id-1",
		VendorRequestId: "123456",
		Details: &preapprovedloan.LoanRequestDetails{
			LoanInfo: &preapprovedloan.LoanRequestDetails_LoanInfo{
				Amount:         money.ParseFloat(2000.0, "INR"),
				TenureInMonths: 12,
				InterestRate:   10.5,
			},
		},
	}
	user1 = &userPb.User{
		Profile: &userPb.Profile{
			PAN:         "12344",
			DateOfBirth: &date.Date{Year: 2002, Month: 5, Day: 5},
		},
	}

	lse1 = &preapprovedloan.LoanStepExecution{
		Id:      "lse-id-1",
		RefId:   "lr-id-1",
		ActorId: "actor-id-1",
		Status:  preapprovedloan.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
		Details: &preapprovedloan.LoanStepExecutionDetails{
			Details: &preapprovedloan.LoanStepExecutionDetails_AadhaarData{
				AadhaarData: &preapprovedloan.AadhaarData{
					VendorSpecificDetails: &preapprovedloan.AadhaarData_Abfl{
						Abfl: &preapprovedloan.AbflAadhaarData{
							ProfileId: "23456",
						},
					},
				},
			},
		},
	}
)

type processorMocks struct {
	loanRequestDao         *plDaoMocks.MockLoanRequestsDao
	loanStepExecutionDao   *plDaoMocks.MockLoanStepExecutionsDao
	loanOfferDao           *plDaoMocks.MockLoanOffersDao
	deeplinkFactory        *deeplink.ProviderFactory
	abflVgClient           *mocks.MockAbflClient
	rpcHelper              *helper.RpcHelper
	loanAccountDao         *plDaoMocks.MockLoanAccountsDao
	loanActivityDao        *plDaoMocks.MockLoanActivityDao
	loanInstallmentInfoDao *plDaoMocks.MockLoanInstallmentInfoDao
	s3Client               *s3Mocks.MockS3Client
	txnExecutorProvider    *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	usersClient            *usersMocks.MockUsersClient
	locationClient         *mockLocation.MockLocationClient
	savingsClient          *mockSavings.MockSavingsClient
	abflDlProvider         *abfl.Provider
	actorClient            *actormocks.MockActorClient
	config                 *worker.Config
	obfuscatorClient       *obfuscatorMocks.MockObfuscatorClient
	txnExector             *storageV2Mocks.MockTxnExecutor
	onboardingClient       *mockOnb.MockOnboardingClient
}

type mockCalculatorProvider struct {
}

func (p *mockCalculatorProvider) GetDefaultValueCalculator(
	ctx context.Context,
	loanOffer *palPb.LoanOffer,
) calculatorTypes.DefaultValueCalculator {
	return defaultValueCalculator.NewCalculator(ctx, loanOffer)
}

func (p *mockCalculatorProvider) GetCalculator(
	ctx context.Context,
	req *calculatorTypes.Request,
) (calculatorTypes.Calculator, error) {
	return calculatorProvidersWrapper.NewProvider().GetCalculator(ctx, req)
}

func initProcessorMocks(ctrl *gomock.Controller) *processorMocks {
	savingsClient := mockSavings.NewMockSavingsClient(ctrl)
	mockLoanRequestsDao := plDaoMocks.NewMockLoanRequestsDao(ctrl)
	mockLoanStepExecutionDao := plDaoMocks.NewMockLoanStepExecutionsDao(ctrl)
	mockLoanOfferDao := plDaoMocks.NewMockLoanOffersDao(ctrl)
	abflVgClient := mocks.NewMockAbflClient(ctrl)
	mockLoanAccountDao := plDaoMocks.NewMockLoanAccountsDao(ctrl)
	mockLoanActivityDao := plDaoMocks.NewMockLoanActivityDao(ctrl)
	mockLoanInstallmentInfoDao := plDaoMocks.NewMockLoanInstallmentInfoDao(ctrl)
	S3Client := s3Mocks.NewMockS3Client(ctrl)
	txnExecutorProvider := storageV2Mocks.NewPassThroughMockIdempotentTxnExecutorProvider(conf.DbConfigMap.GetOwnershipToDbConfigMap())
	usersClient := usersMocks.NewMockUsersClient(ctrl)
	locationClient := mockLocation.NewMockLocationClient(ctrl)
	baseDlProvider := baseprovider.NewProvider(nil, genconf.DeeplinkConfig(), nil, nil, nil, nil)
	abflDlProvider := abfl.NewAbflProvider(baseDlProvider)
	obfuscatorClient := obfuscatorMocks.NewMockObfuscatorClient(ctrl)
	txnExecutor := storageV2Mocks.NewMockTxnExecutor(ctrl)
	actorClient := actormocks.NewMockActorClient(ctrl)
	onboardingClient := mockOnb.NewMockOnboardingClient(ctrl)
	rpcHelper := helper.NewRpcHelper(nil, actorClient, usersClient, savingsClient, nil, nil, nil, nil,
		nil, nil, nil, nil, mockLoanStepExecutionDao, conf.Notification, nil,
		nil, nil, nil, nil, nil, mockLoanActivityDao, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, obfuscatorClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	deeplinkFactory := deeplink.NewDeeplinkProviderFactory(
		baseDlProvider, nil, nil, nil,
		nil, nil, nil, nil, abflDlProvider, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil,
		nil, nil, nil, nil, nil, nil, nil, nil, nil)
	return &processorMocks{
		loanRequestDao:         mockLoanRequestsDao,
		loanStepExecutionDao:   mockLoanStepExecutionDao,
		loanOfferDao:           mockLoanOfferDao,
		deeplinkFactory:        deeplinkFactory,
		abflVgClient:           abflVgClient,
		rpcHelper:              rpcHelper,
		loanAccountDao:         mockLoanAccountDao,
		loanActivityDao:        mockLoanActivityDao,
		loanInstallmentInfoDao: mockLoanInstallmentInfoDao,
		s3Client:               S3Client,
		txnExecutorProvider:    txnExecutorProvider,
		usersClient:            usersClient,
		locationClient:         locationClient,
		abflDlProvider:         abflDlProvider,
		actorClient:            actorClient,
		savingsClient:          savingsClient,
		config:                 conf,
		obfuscatorClient:       obfuscatorClient,
		txnExector:             txnExecutor,
		onboardingClient:       onboardingClient,
	}
}
func newActivityProcessorWithMocks(d *processorMocks) *myabfl.Processor {
	mockCalculatorFactory := &mockCalculatorProvider{}
	return myabfl.NewProcessor(d.loanRequestDao, d.loanOfferDao, d.deeplinkFactory, d.loanStepExecutionDao, d.rpcHelper, d.abflVgClient,
		d.loanAccountDao, d.loanActivityDao, d.loanInstallmentInfoDao, d.s3Client, d.txnExecutorProvider, d.usersClient, d.locationClient,
		d.abflDlProvider, d.txnExector, mockCalculatorFactory, nil, nil, d.onboardingClient, d.savingsClient)
}
