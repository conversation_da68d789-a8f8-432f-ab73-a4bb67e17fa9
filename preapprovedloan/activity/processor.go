//nolint:funlen,dupl
package activity

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"
	googleDate "google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifitemporal"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/nulltypes"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	accountsPb "github.com/epifi/gamma/api/accounts"
	livenessPb "github.com/epifi/gamma/api/auth/liveness"
	authPb "github.com/epifi/gamma/api/auth/orchestrator"
	bankCustPb "github.com/epifi/gamma/api/bankcust"
	brePb "github.com/epifi/gamma/api/bre"
	limitEstimatorPb "github.com/epifi/gamma/api/credit_limit_estimator"
	"github.com/epifi/gamma/api/docs"
	esignPb "github.com/epifi/gamma/api/docs/esign"
	epfoPb "github.com/epifi/gamma/api/epfo"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/nudge"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	pQueuePb "github.com/epifi/gamma/api/persistentqueue"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	preApprovedActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palCommsPb "github.com/epifi/gamma/api/preapprovedloan/comms"
	"github.com/epifi/gamma/api/product"
	recurringPaymentPb "github.com/epifi/gamma/api/recurringpayment"
	riskPb "github.com/epifi/gamma/api/risk"
	"github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/preapprovedloans"
	userPb "github.com/epifi/gamma/api/user"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	palVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	idfcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	llVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	mvVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	profileValidationPb "github.com/epifi/gamma/api/vendorgateway/profilevalidation"
	"github.com/epifi/gamma/pkg/feature/release"
	httpPkg "github.com/epifi/gamma/pkg/http"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/preapprovedloan/activity/brecaller"
	"github.com/epifi/gamma/preapprovedloan/activity/datacompleteness"
	calculatorTypes "github.com/epifi/gamma/preapprovedloan/calculator/types"
	"github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
	"github.com/epifi/gamma/preapprovedloan/deeplink/provider"
	palEvents "github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/helper/agreement"
	"github.com/epifi/gamma/preapprovedloan/lms"
	"github.com/epifi/gamma/preapprovedloan/preclose"
	"github.com/epifi/gamma/preapprovedloan/prepay"
	reProvider "github.com/epifi/gamma/preapprovedloan/recommendation_engine/provider"
	"github.com/epifi/gamma/preapprovedloan/userdata"
	types2 "github.com/epifi/gamma/preapprovedloan/wire/types"
)

const layout = "02-01-2006T15:04:05"

type Processor struct {
	loanRequestDao                  dao.LoanRequestsDao
	loanOffersDao                   dao.LoanOffersDao
	loanStepExecutionDao            dao.LoanStepExecutionsDao
	loanAccountDao                  dao.LoanAccountsDao
	loanInstallmentInfoDao          dao.LoanInstallmentInfoDao
	loanPaymentRequestDao           dao.LoanPaymentRequestsDao
	loanActivityDao                 dao.LoanActivityDao
	loanOfferEligibilityCriteriaDao dao.LoanOfferEligibilityCriteriaDao
	loanApplicantDao                dao.LoanApplicantDao
	loanInstallmentPayoutDao        dao.LoanInstallmentPayoutDao
	rpcHelper                       *helper.RpcHelper
	livenessClient                  livenessPb.LivenessClient
	fedTxnExecutor                  storageV2.TxnExecutor
	palVgClient                     palVgPb.PreApprovedLoanClient
	persistentQueue                 persistentqueue.PersistentQueue
	esignClient                     esignPb.ESignClient
	profileValidationVgClient       profileValidationPb.ProfileValidationClient
	eventBroker                     events.Broker
	deeplinkFactory                 deeplink.IDeeplinkProviderFactory
	limitEstimatorClient            limitEstimatorPb.CreditLimitEstimatorClient
	authClient                      authPb.OrchestratorClient
	txnExecutorProvider             *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor]
	s3Client                        types2.PreApprovedLoanS3Client
	bankCustClient                  bankCustPb.BankCustomerServiceClient
	fac                             *Factory
	docsClient                      docs.DocsClient
	httpClient                      httpPkg.Client
	payClient                       payPb.PayClient
	time                            datetime.Time
	prePay                          prepay.IFactory
	llPalVgClient                   llVgPb.LiquiloansClient
	lms                             lms.ILms
	agreement                       *agreement.AgreementProvider
	preClosureFactory               preclose.IFactory
	riskClient                      riskPb.RiskClient
	epfoClient                      epfoPb.EpfoClient
	productClient                   product.ProductClient
	vkycClient                      vkyc.VKYCClient
	nudgeClient                     nudge.NudgeServiceClient
	config                          *genconf.Config
	palClient                       palPb.PreApprovedLoanClient
	piClient                        piPb.PiClient
	accountPiClient                 accountPiPb.AccountPIRelationClient
	orderClient                     orderPb.OrderServiceClient
	idfcVgClient                    idfcVgPb.IdfcClient
	partnerLmsUserDao               dao.PartnerLmsUserDao
	finFluxVgClient                 finflux.FinfluxClient
	recurringPayClient              recurringPaymentPb.RecurringPaymentServiceClient
	recommendationEngine            reProvider.ILoanRecommendationEngine
	calculatorFactory               calculatorTypes.FactoryProvider
	breClient                       brePb.BreClient
	dataDevS3Client                 types2.DataDevS3Client
	releaseEvaluator                release.IEvaluator
	dataCompletenessCheckerFactory  datacompleteness.IFactory
	lendenVgClient                  lenden.LendenClient
	breCallerFactory                brecaller.IFactory
	userDataPrpvider                userdata.IUserDataProvider
	userClient                      userPb.UsersClient
	mvVgClient                      mvVgPb.MoneyviewClient
	acqEventPublisher               palEvents.AcqEventPublisher
	onbClient                       onbPb.OnboardingClient
	savingsClient                   savings.SavingsClient
}

// nolint: funlen,dupl
func NewProcessor(
	loanRequestDao dao.LoanRequestsDao,
	loanOffersDao dao.LoanOffersDao,
	loanStepExecutionDao dao.LoanStepExecutionsDao,
	loanAccountDao dao.LoanAccountsDao,
	loanInstallmentInfoDao dao.LoanInstallmentInfoDao,
	loanPaymentRequestDao dao.LoanPaymentRequestsDao,
	loanActivityDao dao.LoanActivityDao,
	loanOfferEligibilityCriteriaDao dao.LoanOfferEligibilityCriteriaDao,
	loanApplicantDao dao.LoanApplicantDao,
	loanInstallmentPayoutDao dao.LoanInstallmentPayoutDao,
	rpcHelper *helper.RpcHelper,
	livenessClient livenessPb.LivenessClient,
	fedTxnExecutor storageV2.TxnExecutor,
	palVgClient palVgPb.PreApprovedLoanClient,
	persistentQueue persistentqueue.PersistentQueue,
	esignClient esignPb.ESignClient,
	profileValidationVgClient profileValidationPb.ProfileValidationClient,
	eventBroker events.Broker,
	deeplinkFactory deeplink.IDeeplinkProviderFactory,
	limitEstimatorClient limitEstimatorPb.CreditLimitEstimatorClient,
	authClient authPb.OrchestratorClient,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor],
	s3Client types2.PreApprovedLoanS3Client,
	bankCustClient bankCustPb.BankCustomerServiceClient,
	fac *Factory,
	docsClient docs.DocsClient,
	httpClient httpPkg.Client,
	payClient payPb.PayClient,
	time datetime.Time,
	llPalVgClient llVgPb.LiquiloansClient,
	prePay prepay.IFactory,
	lms lms.ILms,
	agreement *agreement.AgreementProvider,
	preClosureFactory preclose.IFactory,
	riskClient riskPb.RiskClient,
	epfoClient epfoPb.EpfoClient,
	productClient product.ProductClient,
	vkycClient vkyc.VKYCClient,
	nudgeClient nudge.NudgeServiceClient,
	config *genconf.Config,
	palClient palPb.PreApprovedLoanClient,
	piClient piPb.PiClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	orderClient orderPb.OrderServiceClient,
	idfcVgClient idfcVgPb.IdfcClient,
	partnerLmsUserDao dao.PartnerLmsUserDao,
	finFluxVgClient finflux.FinfluxClient,
	recurringPaymentClient recurringPaymentPb.RecurringPaymentServiceClient,
	recommendationEngine reProvider.ILoanRecommendationEngine,
	calculatorFactory calculatorTypes.FactoryProvider,
	breClient brePb.BreClient,
	dataDevS3Client types2.DataDevS3Client,
	releaseEvaluator release.IEvaluator,
	lendenVgClient lenden.LendenClient,
	dataCompletenessCheckerFactory datacompleteness.IFactory,
	breCallerFactory brecaller.IFactory,
	userDataProvider userdata.IUserDataProvider,
	userClient userPb.UsersClient,
	mvVgClient mvVgPb.MoneyviewClient,
	acqEventPublisher palEvents.AcqEventPublisher,
	onbClient onbPb.OnboardingClient,
	savingsClient savings.SavingsClient,
) *Processor {
	return &Processor{
		loanRequestDao:                  loanRequestDao,
		loanOffersDao:                   loanOffersDao,
		loanStepExecutionDao:            loanStepExecutionDao,
		loanInstallmentInfoDao:          loanInstallmentInfoDao,
		loanAccountDao:                  loanAccountDao,
		loanPaymentRequestDao:           loanPaymentRequestDao,
		loanActivityDao:                 loanActivityDao,
		loanOfferEligibilityCriteriaDao: loanOfferEligibilityCriteriaDao,
		loanApplicantDao:                loanApplicantDao,
		rpcHelper:                       rpcHelper,
		livenessClient:                  livenessClient,
		fedTxnExecutor:                  fedTxnExecutor,
		palVgClient:                     palVgClient,
		persistentQueue:                 persistentQueue,
		esignClient:                     esignClient,
		profileValidationVgClient:       profileValidationVgClient,
		eventBroker:                     eventBroker,
		deeplinkFactory:                 deeplinkFactory,
		limitEstimatorClient:            limitEstimatorClient,
		authClient:                      authClient,
		txnExecutorProvider:             txnExecutorProvider,
		s3Client:                        s3Client,
		bankCustClient:                  bankCustClient,
		fac:                             fac,
		docsClient:                      docsClient,
		httpClient:                      httpClient,
		payClient:                       payClient,
		time:                            time,
		prePay:                          prePay,
		llPalVgClient:                   llPalVgClient,
		lms:                             lms,
		loanInstallmentPayoutDao:        loanInstallmentPayoutDao,
		agreement:                       agreement,
		preClosureFactory:               preClosureFactory,
		riskClient:                      riskClient,
		epfoClient:                      epfoClient,
		productClient:                   productClient,
		vkycClient:                      vkycClient,
		nudgeClient:                     nudgeClient,
		config:                          config,
		palClient:                       palClient,
		piClient:                        piClient,
		accountPiClient:                 accountPiClient,
		orderClient:                     orderClient,
		idfcVgClient:                    idfcVgClient,
		partnerLmsUserDao:               partnerLmsUserDao,
		finFluxVgClient:                 finFluxVgClient,
		recurringPayClient:              recurringPaymentClient,
		recommendationEngine:            recommendationEngine,
		calculatorFactory:               calculatorFactory,
		breClient:                       breClient,
		dataDevS3Client:                 dataDevS3Client,
		releaseEvaluator:                releaseEvaluator,
		dataCompletenessCheckerFactory:  dataCompletenessCheckerFactory,
		lendenVgClient:                  lendenVgClient,
		breCallerFactory:                breCallerFactory,
		userDataPrpvider:                userDataProvider,
		userClient:                      userClient,
		mvVgClient:                      mvVgClient,
		acqEventPublisher:               acqEventPublisher,
		onbClient:                       onbClient,
		savingsClient:                   savingsClient,
	}
}

const (
	PrepayGracePeriodInDays            = 3
	EarlySalaryPrepayGracePeriodInDays = 2
)

// nolint: funlen
func (p *Processor) ProcessLoanApplication(ctx context.Context, req *activityPb.Request) (*activityPb.Response, error) {
	res := &activityPb.Response{}
	lg := activity.GetLogger(ctx)

	if p.isDownTime(ctx) {
		return nil, epifitemporal.NewTransientError(errors.New("vendor downtime"))
	}

	loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetClientReqId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("no such loan request exists", zap.Any(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(err))
			return nil, epifitemporal.NewPermanentError(err)
		}
		lg.Error("failed to fetch loan request id orch id", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	loanOffer, err := p.loanOffersDao.GetById(ctx, loanRequest.GetOfferId())
	if err != nil {
		lg.Error("failed to fetch loan offer by offer id", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	// loan application not raised with vendor, raise one
	if loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED {
		// Check if application has already been initiated at vendor
		// statusEnqRes, statusErr := p.palVgClient.GetInstantLoanStatusEnquiry(ctx, &palVgPb.GetInstantLoanStatusEnquiryRequest{
		//	Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		//	ApplicationId: loanRequest.GetVendorRequestId(),
		//	ApiType:       palVgPb.ApiType_API_TYPE_LOAN_PROCESS,
		// })
		// if te := epifigrpc.RPCError(statusEnqRes, statusErr); te != nil && !statusEnqRes.GetStatus().IsRecordNotFound() {
		//	if statusEnqRes.GetStatus().GetCode() == rpc.StatusFailedPrecondition().GetCode() {
		//		logger.Error(ctx, "failed pre-condition for loan status enquiry api", zap.Error(te))
		//		return nil, epifitemporal.NewPermanentError(te)
		//	}
		//	logger.Error(ctx, "failed to fetch status for loan application", zap.Error(te))
		//	return nil, epifitemporal.NewTransientError(te)
		// }
		//
		// if statusEnqRes.GetStatus().IsRecordNotFound() {
		err = p.initiateNewLoanApplication(ctx, loanRequest, loanOffer)
		if err != nil {
			return nil, err
		}
		// }
	}

	statusRes, err := p.palVgClient.GetInstantLoanInfo(ctx, &palVgPb.GetInstantLoanInfoRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		ApplicationId: loanRequest.GetVendorRequestId(),
	})
	if te := epifigrpc.RPCError(statusRes, err); te != nil {
		lg.Error("failed to fetch loan application status", zap.Error(te))
		return nil, epifitemporal.NewTransientError(te)
	}

	switch statusRes.GetLoanState() {
	case palVgPb.LoanState_LOAN_STATE_APPROVED_PENDING_DISBURSAL:
		// do nothing, loan state is in progress
		return nil, epifitemporal.NewTransientError(fmt.Errorf("no updates"))
	case palVgPb.LoanState_LOAN_STATE_DISBURSED:
		// loan amount disbursed to user's account, mark loan request state as disbursed
		loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_DISBURSED
		loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED
		loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
	case palVgPb.LoanState_LOAN_STATE_FAILED:
		// loan application failed at vendor, fail loan request
		loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
		// TODO(harish): add a new sub status for failed at vendor
		loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED
		loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
	default:
		return nil, epifitemporal.NewTransientError(fmt.Errorf("unknown loan state, %w", err))
	}
	if err = p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
	}); err != nil {
		return nil, epifitemporal.NewTransientError(fmt.Errorf("failed to update loan request, %w", err))
	}

	if loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED {
		return nil, epifitemporal.NewPermanentError(fmt.Errorf("loan request failed"))
	}
	return res, nil
}

// nolint: funlen
func (p *Processor) ProcessLivenessVendorReview(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	res := &palActivityPb.PalActivityResponse{}
	lg := activity.GetLogger(ctx)

	loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("no such loan request exists", zap.Any(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
		}
		lg.Error("failed to fetch loan request by orch id", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	vkycSummary, err := p.getVkycSummary(ctx, loanRequest.GetActorId())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		lg.Error("failed to fetch VKYC summary for actor", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}
	if vkycSummary == nil || vkycSummary.GetStatus() != vkyc.VKYCSummaryStatus_VKYC_SUMMARY_STATUS_APPROVED {
		if err = p.getReviewedByVendor(ctx, loanRequest); err != nil {
			lg.Error("failed in getReviewedByVendor", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
	}
	return res, nil
}

func (p *Processor) getReviewedByVendor(ctx context.Context, loanRequest *palPb.LoanRequest) error {
	livenessLoanStep, err := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, loanRequest.GetId(),
		palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_CHECK,
	)
	if err != nil {
		return fmt.Errorf("failed to get liveness loan step, %w", err)
	}

	lvSummary, err := p.getLivenessSummary(ctx, livenessLoanStep)
	if err != nil {
		return fmt.Errorf("failed to get liveness attemptId, %w", err)
	}

	livenessStatusRes, err := p.livenessClient.GetLivenessStatus(ctx, &livenessPb.GetLivenessStatusRequest{
		ActorId:   livenessLoanStep.GetActorId(),
		AttemptId: lvSummary.GetLivenessAttemptId(),
	})
	if te := epifigrpc.RPCError(livenessStatusRes, err); te != nil {
		return fmt.Errorf("failed to fetch liveness, %w", te)
	}
	txnErr := p.fedTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		loanStepExecution, err := p.createLoanStepExecution(txnCtx, loanRequest,
			palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
			palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_LIVENESS_VENDOR_REVIEW, nil)
		if err != nil {
			return fmt.Errorf("failed to create loan step execution, %w", err)
		}
		err = p.persistentQueue.InsertElement(txnCtx, &persistentqueue.QueueElement{
			ActorID:     livenessLoanStep.GetActorId(),
			PayloadType: pQueuePb.PayloadType_PAYLOAD_TYPE_PAL_APPLICATION_LIVENESS_FEDERAL_REVIEW,
			Payload: &pQueuePb.Payload{
				LivenessPayload: &pQueuePb.LivenessReview{
					ActorId:       livenessLoanStep.GetActorId(),
					RequestId:     lvSummary.GetLivenessAttemptId(),
					VideoLocation: livenessStatusRes.GetVideoLocation(),
					ReferenceId:   loanStepExecution.GetOrchId(),
				},
			}},
		)
		if err != nil {
			return fmt.Errorf("failed to insert liveness vendor review in PQ, %w", err)
		}
		return nil
	})
	if txnErr != nil {
		return fmt.Errorf("error in executing txn block, %w", txnErr)
	}
	return nil
}

// nolint: funlen
func (p *Processor) initiateNewLoanApplication(ctx context.Context, loanRequest *palPb.LoanRequest, loanOffer *palPb.LoanOffer) error {
	lg := activity.GetLogger(ctx)
	deviceId, _, _, err := p.rpcHelper.GetDeviceAuthDetails(ctx, loanRequest.GetActorId())
	if err != nil {
		return epifitemporal.NewTransientError(fmt.Errorf("failed to get device details, %w", err))
	}

	processingFeeAndGst, _ := money.Sum(loanRequest.GetDetails().GetLoanInfo().GetDeductions().GetProcessingFee(), loanRequest.GetDetails().GetLoanInfo().GetDeductions().GetGst())
	res, err := p.palVgClient.GetInstantLoanApplication(ctx, &palVgPb.GetInstantLoanApplicationRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		ApplicationId:       loanRequest.GetVendorRequestId(),
		OfferId:             loanOffer.GetVendorOfferId(),
		Otp:                 loanRequest.GetDetails().GetOtpInfo().GetLastEnteredOtp(),
		PhoneNumber:         loanRequest.GetDetails().GetPhoneNumber(),
		MaskedAccountNumber: loanRequest.GetDetails().GetMaskedAccountNumber(),
		ProcessingFee:       processingFeeAndGst,
		CustomerDeviceIp:    deviceId,
		LoanAmount:          loanRequest.GetDetails().GetLoanInfo().GetAmount(),
		EmiAmount:           loanRequest.GetDetails().GetLoanInfo().GetEmiAmount(),
		InterestRate:        loanRequest.GetDetails().GetLoanInfo().GetInterestRate(),
		TenureMonths:        loanRequest.GetDetails().GetLoanInfo().GetTenureInMonths(),
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		var isLastAttempt bool
		if res.GetStatus().GetCode() == rpc.StatusFailedPrecondition().GetCode() {
			if res.GetStatus().GetDebugMessage() == "ILE025" {
				isLastAttempt, err = p.handleOtpFailure(ctx, loanRequest)
				if err != nil {
					return fmt.Errorf("failed handling otp failure at vendor, %w", err)
				}
				if isLastAttempt {
					return epifitemporal.NewPermanentError(te)
				}
			}
			loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
			loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_PRE_CONDITION
			loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
			updateErr := p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
			})
			if updateErr != nil {
				lg.Error("failed to update loan request", zap.Error(updateErr))
				return epifitemporal.NewTransientError(updateErr)
			}
			return epifitemporal.NewPermanentError(te)
		}
		return epifitemporal.NewTransientError(te)
	}

	loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_INITIATED
	loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INITIATED_AT_VENDOR
	if err = p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
	}); err != nil {
		return epifitemporal.NewTransientError(fmt.Errorf("failed to update loan request, %w", err))
	}
	return nil
}

func (p *Processor) handleOtpFailure(ctx context.Context, loanRequest *palPb.LoanRequest) (bool, error) {
	isLastAttempt := false
	loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFICATION_FAILED
	loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFICATION_FAILED_OTP

	if loanRequest.GetDetails().GetOtpInfo().GetAttemptsCount() >= loanRequest.GetDetails().GetOtpInfo().GetMaxAttempts() {
		loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
		loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_OTP_MAX_ATTEMPTS_EXHAUSTED
		loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
		isLastAttempt = true
	}

	err := p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
	})
	if err != nil {
		return false, epifitemporal.NewTransientError(err)
	}
	return isLastAttempt, nil
}

func (p *Processor) UpdateLoanRequest(ctx context.Context, req *activityPb.Request) (*activityPb.Response, error) {
	lg := activity.GetLogger(ctx)
	res := &activityPb.Response{}
	var payload preApprovedActivityPb.UpdateLoanRequestActivityRequest
	err := protojson.Unmarshal(req.GetPayload(), &payload)
	if err != nil {
		lg.Error("failed to fetch loan request id orch id", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("failed to fetch loan request id orch id, %v", err))
	}

	loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetClientReqId())
	if err != nil {
		lg.Error("failed to get loan request by orch id", zap.Error(err))
		return nil, GetActivityErrFromDaoError(err)
	}
	// if loan request is already in cancelled state, don't update the status
	if loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED {
		return nil, errors.Wrap(epifierrors.ErrPermanent, "updating loan request failed as already in cancelled state")
	}
	loanRequest.Status = payload.GetLoanRequestStatus()
	loanRequest.SubStatus = payload.GetLoanRequestSubStatus()
	if loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED ||
		loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED ||
		loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS {
		loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
	}

	err = p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
	})
	if err != nil {
		lg.Error("failed to update loan request field masks", zap.Error(err))
		return nil, GetActivityErrFromDaoError(err)
	}

	return res, nil
}

func (p *Processor) UpdateLoanPaymentRequest(ctx context.Context, req *activityPb.Request) (*activityPb.Response, error) {
	lg := activity.GetLogger(ctx)
	res := &activityPb.Response{}
	var payload preApprovedActivityPb.UpdateLoanPaymentRequestActivityRequest
	err := protojson.Unmarshal(req.GetPayload(), &payload)
	if err != nil {
		lg.Error("failed to unmarshal request", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to unmarshal request, %v", err))
	}

	loanPaymentRequest, err := p.loanPaymentRequestDao.GetByOrchId(ctx, req.GetClientReqId())
	if err != nil {
		lg.Error("failed to get loan payment request by orch id", zap.Error(err))
		return nil, GetActivityErrFromDaoError(err)
	}

	payload.GetLoanPaymentRequest().Id = loanPaymentRequest.GetId()
	err = p.loanPaymentRequestDao.Update(ctx, payload.GetLoanPaymentRequest(), payload.GetFieldMasks())
	if err != nil {
		lg.Error("failed to update loan payment request", zap.Error(err))
		return nil, GetActivityErrFromDaoError(err)
	}
	return res, nil
}

// GetOtpVerificationStatus checks if OTP is verified for a loan application or not.
// It also checks if the max otp attempts for a loan application have been exhausted or not.
func (p *Processor) GetOtpVerificationStatus(ctx context.Context, req *activityPb.Request) (*activityPb.Response, error) {
	lg := activity.GetLogger(ctx)
	res := &activityPb.Response{}

	loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetClientReqId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("no such loan request exists", zap.Any(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(err))
			return nil, epifitemporal.NewPermanentError(err)
		}
		lg.Error("failed to fetch loan request id orch id", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	// If loan request verified, return nil
	if loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED &&
		loanRequest.GetSubStatus() == palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_OTP {
		return res, nil
	}

	if loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED {
		return nil, epifitemporal.NewPermanentError(fmt.Errorf("loan request failed"))
	}

	if !(loanRequest.GetDetails().GetOtpInfo().GetAttemptsCount() < loanRequest.GetDetails().GetOtpInfo().GetMaxAttempts()) &&
		loanRequest.GetStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED {
		// max number of retires exhausted for the OTP
		loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
		loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_OTP_MAX_ATTEMPTS_EXHAUSTED
		loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
		if err = p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
		}); err != nil {
			lg.Error("failed to update loan request status to FAILED", zap.Error(err))
			return nil, epifitemporal.NewTransientError(err)
		}
		// return permanent to mark workflow as failed
		return nil, epifitemporal.NewPermanentError(fmt.Errorf("otp limit exhausted"))
	}

	return nil, epifitemporal.NewTransientError(fmt.Errorf("otp verification pending"))
}

func (p *Processor) createPi(ctx context.Context, loanAccount *palPb.LoanAccount) (*piPb.PaymentInstrument, error) {
	res, err := p.piClient.CreatePi(ctx, &piPb.CreatePiRequest{
		Type: piPb.PaymentInstrumentType_BANK_ACCOUNT,
		Identifier: &piPb.CreatePiRequest_Account{
			Account: &piPb.Account{
				ActualAccountNumber: loanAccount.GetAccountNumber(),
				SecureAccountNumber: mask.GetMaskedAccountNumber(loanAccount.GetAccountNumber(), ""),
				IfscCode:            loanAccount.GetIfscCode(),
				AccountType:         accountsPb.Type_LOAN_ACCOUNT,
				Name:                "Loan Account",
			},
		},
		VerifiedName:         "Loan Account",
		IssuerClassification: piPb.PaymentInstrumentIssuer_INTERNAL,
		Capabilities: map[string]bool{
			piPb.Capability_INBOUND_TXN.String():  true,
			piPb.Capability_OUTBOUND_TXN.String(): false,
		},
		Ownership: piPb.Ownership_EPIFI_TECH,
		State:     piPb.PaymentInstrumentState_VERIFIED,
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return nil, fmt.Errorf("failed to create PI, %w", te)
	}
	return res.GetPaymentInstrument(), nil
}

func (p *Processor) createAccountPi(ctx context.Context, pi *piPb.PaymentInstrument, loanAccount *palPb.LoanAccount) error {
	res, err := p.accountPiClient.Create(ctx, &accountPiPb.CreateAccountPIRequest{
		ActorId:     loanAccount.GetActorId(),
		AccountId:   loanAccount.GetId(),
		AccountType: accountsPb.Type_LOAN_ACCOUNT,
		PiId:        pi.GetId(),
	})
	if te := epifigrpc.RPCError(res, err); te != nil {
		return errors.Wrap(te, "failed to create account PI")
	}
	return nil
}

func (p *Processor) getLoanDisbursalOrder(ctx context.Context, loanRequest *palPb.LoanRequest) (*orderPb.Order, *paymentPb.Transaction, error) {
	var (
		loanDisOrder *orderPb.Order
		offset       = 0
		pageSize     = 25
	)
	// TODO(harish): move to consumer based approach to consume order update events
	// The idea is to fetch orders for the actor of type Credit and ideally in the last 100-120 orders we should get
	// a loan disbursement order as the fromTime is set to the loan request creation time. Other filters such as workflow
	// and status are added.
	for i := 0; i < 8; i++ {
		orderRes, err := p.orderClient.GetOrdersForActor(ctx, &orderPb.GetOrdersForActorRequest{
			ActorId:   loanRequest.GetActorId(),
			FieldMask: []orderPb.OrderFieldMask{orderPb.OrderFieldMask_ID, orderPb.OrderFieldMask_TAGS},
			// As the disbursement transaction will be a credit transaction for the savings account
			TransactionType: orderPb.GetOrdersForActorRequest_CREDIT,
			StatusFilters:   []orderPb.GetOrdersForActorRequest_OrderStatusFilter{orderPb.GetOrdersForActorRequest_SUCCESS},
			SortBy:          orderPb.OrderFieldMask_CREATED_AT,
			FromTime:        loanRequest.GetCreatedAt(),
			ToTime:          timestampPb.Now(),
			PageSize:        int32(pageSize),
			Offset:          int32(offset),
			Workflows:       []orderPb.OrderWorkflow{orderPb.OrderWorkflow_NO_OP},
		})
		if te := epifigrpc.RPCError(orderRes, err); te != nil {
			return nil, nil, errors.Wrap(te, "failed to fetch orders for loan disbursement")
		}
		for _, order := range orderRes.GetOrders() {
			if isLoanOrder(order) {
				loanDisOrder = order
				break
			}
		}
		if loanDisOrder != nil {
			break
		}
		offset += pageSize
	}
	if loanDisOrder == nil {
		return nil, nil, fmt.Errorf("no disbursement order exists yet, %w", epifierrors.ErrRecordNotFound)
	}
	// fetch transaction and return
	orderTxnRes, orderTxnErr := p.orderClient.GetOrderWithTransactions(ctx, &orderPb.GetOrderWithTransactionsRequest{
		OrderId: loanDisOrder.GetId(),
	})
	if te := epifigrpc.RPCError(orderTxnRes, orderTxnErr); te != nil {
		return nil, nil, errors.Wrap(te, "failed to fetch transactions for loan disbursement")
	}
	if len(orderTxnRes.GetOrderWithTransactions().GetTransactions()) == 0 {
		return nil, nil, fmt.Errorf("no disbursement transaction exists yet, %w", epifierrors.ErrRecordNotFound)
	}

	return loanDisOrder, orderTxnRes.GetOrderWithTransactions().GetTransactions()[0], nil
}

func isLoanOrder(order *orderPb.Order) bool {
	for _, tag := range order.GetTags() {
		if tag == orderPb.OrderTag_LOAN {
			return true
		}
	}
	return false
}

// ProcessLoanAccountCreation creates a loan account after money is disbursed from the vendor.
//
//nolint:funlen
func (p *Processor) ProcessLoanAccountCreation(ctx context.Context, req *activityPb.Request) (*activityPb.Response, error) {
	lg := activity.GetLogger(ctx)
	res := &activityPb.Response{}

	loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetClientReqId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("no such loan request exists", zap.Any(logger.CLIENT_REQUEST_ID, req.GetClientReqId()), zap.Error(err))
			return nil, epifitemporal.NewPermanentError(err)
		}
		lg.Error("failed to fetch loan request id orch id", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	// Fetch customer info API to get the latest loan account for the actor and create an entry at our end
	accountsList, err := p.rpcHelper.GetLoanAccountListForActor(ctx, loanRequest.GetActorId())
	if err != nil {
		lg.Error("failed to fetch account list for customer from vendor", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	vgLoanAccount, err := getLoanAccountFromAccountsList(accountsList)
	if err != nil {
		lg.Error("loan account not created yet")
		return nil, epifitemporal.NewTransientError(err)
	}

	_, disTxn, err := p.getLoanDisbursalOrder(ctx, loanRequest)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		lg.Error("failed to fetch loan disbursement")
		return nil, epifitemporal.NewTransientError(err)
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		logger.Info(ctx, "GetLoanDisbursalOrder ended with record not found")
	} else {
		logger.Info(ctx, "GetLoanDisbursalOrder completed")
	}

	maturityDate := datetime.TimeToDateInLoc(time.Now().AddDate(0, int(loanRequest.GetDetails().GetLoanInfo().GetTenureInMonths()), 0), datetime.IST)
	// Override maturity daa=tw with vendor provided value if it's correct
	if time.Now().Before(datetime.DateToTimeV2(vgLoanAccount.GetEndDate(), datetime.IST)) {
		maturityDate = vgLoanAccount.GetEndDate()
	}

	loanAccounts, err := p.loanAccountDao.GetByActorIdAndVendor(ctx, loanRequest.GetActorId(), loanRequest.GetVendor())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		lg.Error("error in loanAccountsDao.GetByActorIdAndVendor", zap.Error(err))
		return nil, epifitemporal.NewTransientError(fmt.Errorf("error in loanAccountsDao.GetByActorIdAndVendor, %w", err))
	}

	// Default name given by BE. Can be edited so user can have option to change the loan name
	loanName := fmt.Sprintf("LOAN #%d", len(loanAccounts)+1)

	var loanAccount *palPb.LoanAccount
	txnErr := p.fedTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		loanAccount, err = p.loanAccountDao.GetOrCreate(txnCtx, &palPb.LoanAccount{
			ActorId:       loanRequest.GetActorId(),
			Vendor:        loanRequest.GetVendor(),
			AccountNumber: vgLoanAccount.GetAccountNumber(),
			LoanType:      palPb.LoanType_LOAN_TYPE_PERSONAL,
			IfscCode:      helper.FetchIFSCFromAccountNumber(vgLoanAccount.GetAccountNumber()),
			LoanAmountInfo: &palPb.LoanAmountInfo{
				LoanAmount:         loanRequest.GetDetails().GetLoanInfo().GetAmount(),
				DisbursedAmount:    loanRequest.GetDetails().GetLoanInfo().GetDisbursalAmount(),
				OutstandingAmount:  loanRequest.GetDetails().GetLoanInfo().GetTotalPayable(),
				TotalPayableAmount: loanRequest.GetDetails().GetLoanInfo().GetTotalPayable(),
			},
			MaturityDate: maturityDate,
			Details: &palPb.LoanAccountDetails{
				InterestRate:       vgLoanAccount.GetInterestRate(),
				TenureInMonths:     loanRequest.GetDetails().GetLoanInfo().GetTenureInMonths(),
				LoanName:           loanName,
				AprRate:            loanRequest.GetDetails().GetLoanInfo().GetAprRate(),
				LoanProgramVersion: loanRequest.GetDetails().GetProgramVersion(),
			},
			Status:      palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE,
			LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_PRE_APPROVED_LOAN,
			CreatedAt:   datetime.DateToTimestamp(vgLoanAccount.GetOpenDate(), datetime.IST),
		})
		logger.Info(ctx, "loan account created/fetched")

		loanRequest.LoanAccountId = loanAccount.GetId()
		loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS
		loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_SUCCESS
		err = p.loanRequestDao.Update(txnCtx, loanRequest, []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_LOAN_ACCOUNT_NUMBER,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS})
		if err != nil {
			return fmt.Errorf("failed to update loan request, %w", err)
		}

		// create loan activity for disbursement on best effort basis
		if disTxn != nil {
			_, actErr := p.loanActivityDao.Create(txnCtx, &palPb.LoanActivity{
				LoanAccountId: loanAccount.GetId(),
				Type:          palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LOAN_DISBURSEMENT,
				Details:       &palPb.LoanActivityDetails{TransactionId: disTxn.GetId(), Amount: disTxn.GetAmount()},
				ReferenceId:   disTxn.GetId(),
			})
			if actErr != nil {
				return fmt.Errorf("failed to create loan activity, %w", actErr)
			}
			logger.Info(ctx, "loan disbursement activity created")
		}

		_, createErr := p.loanInstallmentInfoDao.Create(txnCtx, getLoanInstallmentInfo(loanRequest, loanAccount, vgLoanAccount))
		if err != nil {
			return fmt.Errorf("failed to create loan installment info, %w", createErr)
		}
		return nil
	})
	if txnErr != nil {
		lg.Error("failed to create a loan account", zap.Error(txnErr))
		return nil, epifitemporal.NewTransientError(txnErr)
	}
	if loanAccount != nil {
		pi, piErr := p.createPi(ctx, loanAccount)
		if piErr != nil {
			lg.Error("failed to create a loan account PI", zap.Error(piErr))
			return nil, epifitemporal.NewTransientError(piErr)
		}
		logger.Info(ctx, "loan disbursement CreatePi")
		accountPiErr := p.createAccountPi(ctx, pi, loanAccount)
		if accountPiErr != nil {
			lg.Error("failed to create an account PI for loan account", zap.Error(piErr))
			return nil, epifitemporal.NewTransientError(piErr)
		}
		logger.Info(ctx, "loan disbursement CreateAccountPi")
		notifReq := helper.SendNotificationRequest{
			ActorId:           loanRequest.GetActorId(),
			NotificationType:  palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_LOAN_ACCOUNT_CREATION,
			LoanAccountNumber: loanAccount.GetAccountNumber(),
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_DETAILS_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanDetailsScreenOptions{
					PreApprovedLoanDetailsScreenOptions: &deeplinkPb.PreApprovedLoanDetailsScreenOptions{
						LoanId: loanAccount.GetId(),
					},
				},
			},
		}
		p.rpcHelper.SendNotificationWithGoRoutine(ctx, notifReq)
	}

	return res, nil
}

func getLoanAccountFromAccountsList(accountList []*palVgPb.FetchLoanDetailsResponse_LoanDetails) (*palVgPb.FetchLoanDetailsResponse_LoanDetails, error) {
	if len(accountList) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	for _, account := range accountList {
		if account.GetAccountType() == palVgPb.FetchLoanDetailsResponse_ACCOUNT_TYPE_LOANS {
			return account, nil
		}
	}
	return nil, fmt.Errorf("failed to fetch loan account from account list")
}

func getLoanInstallmentInfo(loanRequest *palPb.LoanRequest, loanAccount *palPb.LoanAccount, vgLoanAccount *palVgPb.FetchLoanDetailsResponse_LoanDetails) *palPb.LoanInstallmentInfo {
	year, month, _ := time.Now().In(datetime.IST).Date()
	fifthOfCurrMonth := time.Date(year, month, 5, 23, 59, 59, 0, datetime.IST)
	startDate := fifthOfCurrMonth.AddDate(0, 1, 0)
	if time.Now().In(datetime.IST).After(fifthOfCurrMonth) {
		startDate = fifthOfCurrMonth.AddDate(0, 2, 0)
	}
	// We are calculating loan start date + tenure as the end date. But we don't consider the start date in this case and hence the end date comes to one extra month. Have added -1 due to this
	endDate := startDate.AddDate(0, int(loanRequest.GetDetails().GetLoanInfo().GetTenureInMonths())-1, 0)
	return &palPb.LoanInstallmentInfo{
		AccountId:             loanAccount.GetId(),
		TotalAmount:           loanRequest.GetDetails().GetLoanInfo().GetTotalPayable(),
		StartDate:             datetime.TimeToDateInLoc(startDate, startDate.Location()),
		EndDate:               datetime.TimeToDateInLoc(endDate, endDate.Location()),
		TotalInstallmentCount: loanRequest.GetDetails().GetLoanInfo().GetTenureInMonths(),
		NextInstallmentDate:   vgLoanAccount.GetNextPayDate(),
		Details: &palPb.LoanInstallmentInfoDetails{
			NextEmiAmount: vgLoanAccount.GetNextPayAmount(),
			GracePeriod:   PrepayGracePeriodInDays,
		},
		Status: palPb.LoanInstallmentInfoStatus_LOAN_INSTALLMENT_INFO_STATUS_ACTIVE,
	}
}

// nolint: funlen
func (p *Processor) CheckProfileValidation(ctx context.Context, req *preApprovedActivityPb.PalActivityRequest) (*activityPb.Response, error) {
	res := &activityPb.Response{}
	lg := activity.GetLogger(ctx)

	loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("no such loan request exists", zap.Any(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
			return nil, epifitemporal.NewPermanentError(err)
		}
		lg.Error("failed to fetch loan request id orch id", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}
	loanStepExecution, err := p.createLoanStepExecution(ctx, loanRequest,
		palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PROFILE_VALIDATION, nil)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	startTime := time.Now()
	p.pushApiTriggeredEvent(ctx, loanRequest.GetActorId(), palEvents.ApiNameCheckProfileValidation, palEvents.LoanRequest{
		Id:      loanRequest.GetId(),
		OfferId: loanRequest.GetOfferId(),
	}, palEvents.LoanApplicationFlow)

	if loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED {
		return nil, epifitemporal.NewPermanentError(fmt.Errorf("profile validation failed"))
	}

	user, err := p.rpcHelper.GetUserByActorId(ctx, loanRequest.GetActorId())
	if err != nil {
		lg.Error("failed to fetch user by actor ID", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	customerDetails, err := p.rpcHelper.GetCustomerDetailsFromVG(ctx, loanRequest.GetVendor(), loanRequest.GetActorId())
	if err != nil {
		lg.Error("Failed to fetch customer details by actor Id and vendor", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}
	if customerDetails.GetGender() == types.Gender_GENDER_UNSPECIFIED {
		lg.Error("Got customer details by actor Id and vendor, gender as unspecified")
		return nil, epifitemporal.NewTransientError(fmt.Errorf("gender unspecified in customer details"))
	}

	vgRes, err := p.profileValidationVgClient.CheckProfileValidation(ctx, &profileValidationPb.CheckProfileValidationRequest{
		Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		CustomerDetails: &profileValidationPb.CheckProfileValidationRequest_CustomerDetails{
			Name:        user.GetProfile().GetPanName(),
			DateOfBirth: user.GetProfile().GetDateOfBirth(),
			Gender:      customerDetails.GetGender(),
			PanNumber:   user.GetProfile().GetPAN(),
		},
		ContactDetails: &profileValidationPb.CheckProfileValidationRequest_ContactDetails{
			PhoneNumber: user.GetProfile().GetPhoneNumber(),
			EmailId:     user.GetProfile().GetEmail(),
		},
		Client:            profileValidationPb.Client_CLIENT_PRE_APPROVED_LOAN,
		ApplicationType:   profileValidationPb.ApplicationType_APPLICATION_TYPE_CREDIT,
		ClientReferenceId: "FI-" + idgen.RandAlphaNumericString(9),
	})
	var errToReturn error
	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		lg.Error("failed to check profile validation for user", zap.Error(te))
		loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING
		loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_PROFILE_VALIDATION
		errToReturn = epifitemporal.NewTransientError(fmt.Errorf("failed to validate profile, %w", te))
	} else {
		switch vgRes.GetValidationStatus() {
		case profileValidationPb.ValidationStatus_VALIDATION_STATUS_VERIFIED:
			loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_VERIFIED
			loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_VERIFIED_PROFILE_VALIDATION
			loanStepExecution.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
			loanStepExecution.CompletedAt = timestampPb.Now()
			p.pushApiResponseEvent(ctx, loanRequest.GetActorId(), palEvents.ApiNameCheckProfileValidation, time.Since(startTime), palEvents.Success, "", palEvents.LoanRequest{
				Id:      loanRequest.GetId(),
				OfferId: loanRequest.GetOfferId(),
			}, palEvents.LoanApplicationFlow)
		case profileValidationPb.ValidationStatus_VALIDATION_STATUS_REJECTED:
			loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
			loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_PROFILE_VALIDATION
			loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
			loanStepExecution.Details = &palPb.LoanStepExecutionDetails{
				Details: &palPb.LoanStepExecutionDetails_HunterData{
					HunterData: &palPb.HunterData{
						Rule:  strings.Join(vgRes.GetRulesTriggered(), "::"),
						Score: vgRes.GetScore(),
					},
				}}
			loanStepExecution.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
			loanStepExecution.CompletedAt = timestampPb.Now()
			p.pushApiResponseEvent(ctx, loanRequest.GetActorId(), palEvents.ApiNameCheckProfileValidation, time.Since(startTime), palEvents.Failure, "", palEvents.LoanRequest{
				Id:      loanRequest.GetId(),
				OfferId: loanRequest.GetOfferId(),
			}, palEvents.LoanApplicationFlow)

			errToReturn = epifitemporal.NewPermanentError(fmt.Errorf("profile validation failed"))

		default:
			loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_PENDING
			loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_PENDING_PROFILE_VALIDATION
			loanStepExecution.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
			errToReturn = epifitemporal.NewTransientError(fmt.Errorf("got unkown status for profile validation"))
		}
	}

	updateErr := p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
	})
	if updateErr != nil {
		lg.Error("failed to update loan request", zap.Error(updateErr))
		return nil, epifitemporal.NewTransientError(updateErr)
	}

	updateLsErr := p.loanStepExecutionDao.Update(ctx, loanStepExecution, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT,
	})
	if updateLsErr != nil {
		lg.Error("failed to update loan step", zap.Error(updateLsErr))
		return nil, epifitemporal.NewTransientError(updateLsErr)
	}
	if errToReturn != nil {
		return nil, errToReturn
	}
	return res, nil
}

// ProcessPrePayPayment activity to process pre-payment of a loan account.
// The method fetches the order status for the fund transfer request and accordingly updated the loan payment request.
// nolint: funlen
// Deprecated: in favour of ProcessPayment
func (p *Processor) ProcessPrePayPayment(ctx context.Context, req *palActivityPb.PalActivityRequest) (*activityPb.Response, error) {
	res := &activityPb.Response{}
	lg := activity.GetLogger(ctx)

	paymentRequest, err := p.loanPaymentRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("no such loan payment request exists", zap.Any(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
			return nil, epifitemporal.NewPermanentError(err)
		}
		lg.Error("failed to fetch loan payment request id by orch id", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	loanAccount, err := p.loanAccountDao.GetById(ctx, paymentRequest.GetAccountId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("no such loan accounts found", zap.Any(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("no such loan accounts found, err: %v", err))
		}
		lg.Error("failed to fetch loan account by id", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan account by id, err: %v", err))
	}

	activityStatus, txn, err := p.rpcHelper.GetOrderAndTxn(ctx, paymentRequest.GetOrchId())
	if err != nil || activityStatus == palPb.GetLoanActivityStatusResponse_UNSPECIFIED {
		lg.Error("failed to fetch order by client request id", zap.Error(err))
		return nil, epifitemporal.NewTransientError(err)
	}

	var errToReturn error
	switch activityStatus {
	case palPb.GetLoanActivityStatusResponse_COMPLETED:
		paymentRequest.Status = palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_SUCCESS
		logger.Info(ctx, "loan pre payment success")
	case palPb.GetLoanActivityStatusResponse_FAILED:
		paymentRequest.Status = palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_FAILED
		errToReturn = epifitemporal.NewPermanentError(fmt.Errorf("payment failed"))
	default:
		paymentRequest.Status = palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_IN_PROGRESS
		errToReturn = epifitemporal.NewTransientError(fmt.Errorf("payment in progress"))
	}

	txnErr := p.fedTxnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		updateErr := p.loanPaymentRequestDao.Update(txnCtx, paymentRequest, []palPb.LoanPaymentRequestFieldMask{
			palPb.LoanPaymentRequestFieldMask_LOAN_PAYMENT_REQUEST_FIELD_MASK_STATUS,
		})
		if updateErr != nil {
			return fmt.Errorf("failed to update payment request, %w", updateErr)
		}

		// If payment request status is not successful, no need to create a loan activity for that transaction and hence we exit from the transaction block
		if paymentRequest.GetStatus() != palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_SUCCESS {
			logger.Info(ctx, "loan payment request status is not successful hence loan activity creation is omitted")
			return nil
		}
		_, createErr := p.loanActivityDao.Create(txnCtx, &palPb.LoanActivity{
			LoanAccountId: paymentRequest.GetAccountId(),
			Type:          palPb.LoanActivityType_LOAN_ACTIVITY_TYPE_LUMPSUM,
			ReferenceId:   txn.GetId(),
			Details: &palPb.LoanActivityDetails{
				TransactionId: txn.GetId(),
				Amount:        paymentRequest.GetAmount(),
				Utr:           txn.GetUtr(),
			},
		})
		if createErr != nil {
			return fmt.Errorf("failed to create loan activity for lumpsum payment, %w", createErr)
		}

		return nil
	})
	if txnErr != nil {
		lg.Error("failed to create a loan activity or update payment request", zap.Error(txnErr))
		return nil, epifitemporal.NewTransientError(txnErr)
	}

	if paymentRequest.GetStatus() == palPb.LoanPaymentRequestStatus_LOAN_PAYMENT_REQUEST_STATUS_SUCCESS {
		if fetchLoanErr := p.updateLiiWithFetchLoanDetails(ctx, loanAccount); fetchLoanErr != nil {
			logger.Error(ctx, "failed to fetch loan details from vendor", zap.Error(fetchLoanErr))
		}
		notifReq := helper.SendNotificationRequest{
			ActorId:           paymentRequest.GetActorId(),
			NotificationType:  palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_PRE_PAY,
			LoanAccountNumber: loanAccount.GetAccountNumber(),
			Deeplink: &deeplinkPb.Deeplink{
				Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_DETAILS_SCREEN,
				ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanDetailsScreenOptions{
					PreApprovedLoanDetailsScreenOptions: &deeplinkPb.PreApprovedLoanDetailsScreenOptions{
						LoanId: paymentRequest.GetAccountId(),
					},
				},
			},
			Amount: paymentRequest.GetAmount(),
		}
		p.rpcHelper.SendNotificationWithGoRoutine(ctx, notifReq)
	}
	return res, errToReturn
}

func (p *Processor) updateLiiWithFetchLoanDetails(ctx context.Context, la *palPb.LoanAccount) error {
	vgLoanDetails, ldErr := p.rpcHelper.GetVgLoanDetails(ctx, la.GetActorId(), la.GetAccountNumber())
	if ldErr != nil {
		return errors.Wrap(ldErr, "failed to fetch loan account list by actor")
	}

	lii, liiErr := p.loanInstallmentInfoDao.GetByActiveAccountId(ctx, la.GetId())
	if liiErr != nil {
		if errors.Is(liiErr, epifierrors.ErrRecordNotFound) {
			return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("no active loan installment info found by loan account id, err: %v", liiErr))
		}
		return errors.Wrap(liiErr, "failed to fetch active loan installment info by loan account id")
	}

	lii.NextInstallmentDate = vgLoanDetails.GetNextPayDate()
	lii.GetDetails().NextEmiAmount = vgLoanDetails.GetNextPayAmount()

	if updateErr := p.loanInstallmentInfoDao.Update(ctx, lii, []palPb.LoanInstallmentInfoFieldMask{
		palPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_NEXT_INSTALLMENT_DATE,
		palPb.LoanInstallmentInfoFieldMask_LOAN_INSTALLMENT_INFO_FIELD_MASK_DETAILS,
	}); updateErr != nil {
		return fmt.Errorf("failed to update loan installment info, %w", updateErr)
	}
	return nil
}

// ProcessPreClosure activity to process pre-closure of a loan account.
// The activity fetches the overdue payment ID and other details and pre-closes the loan account.
// Note that the overdue amount is to be fetched real time from the vendor before initialising the pre-closure request

// nolint: funlen
func (p *Processor) ProcessPreClosure(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	res := &palActivityPb.PalActivityResponse{
		ResponseHeader: &activityPb.ResponseHeader{},
	}
	lg := activity.GetLogger(ctx)

	loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("no such loan request exists", zap.Any(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
		}
		lg.Error("failed to fetch loan request id by orch id", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	loanAccount, err := p.loanAccountDao.GetById(ctx, loanRequest.GetLoanAccountId())
	if err != nil {
		lg.Error("failed to fetch loan account by id", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	savingsAccount, err := p.rpcHelper.GetSavingsAccountDetails(ctx, loanRequest.GetActorId(), commonvgpb.Vendor_FEDERAL_BANK, account.AccountProductOffering_APO_REGULAR)
	if err != nil {
		lg.Error("failed to fetch savings account by actorId", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	year, month, date := time.Now().Date()
	currDate := &googleDate.Date{Year: int32(year), Month: int32(month), Day: int32(date)}
	vgRes, err := p.palVgClient.GetInstantLoanClosureEnquiry(ctx, &palVgPb.GetInstantLoanClosureEnquiryRequest{
		Header:        &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		Date:          currDate,
		AccountNumber: loanAccount.GetAccountNumber(),
	})
	if te := epifigrpc.RPCError(vgRes, err); te != nil {
		lg.Error("failed to fetch loan closure enquiry details", zap.Error(te))
		if errors.Is(te, epifierrors.ErrResourceExhausted) {
			logger.Error(ctx, fmt.Sprintf("got resource exhausted in response for GetInstantLoanClosureEnquiry api,err: %v", te))
			return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
	}

	closureRes, err := p.palVgClient.CloseInstantLoanAccount(ctx, &palVgPb.CloseInstantLoanAccountRequest{
		Header:                &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_FEDERAL_BANK},
		RequestId:             loanRequest.GetVendorRequestId(),
		Date:                  currDate,
		CustomerAccountNumber: savingsAccount.GetAccountNo(),
		LoanAccountNumber:     loanAccount.GetAccountNumber(),
		NetPayAmount:          vgRes.GetClosureAmount(),
		PayAmountId:           "",
		Remarks:               "CLOSURE",
	})
	if te := epifigrpc.RPCError(closureRes, err); te != nil {
		lg.Error("failed to perform loan closure", zap.Error(te))
		return nil, errors.Wrap(epifierrors.ErrTransient, te.Error())
	}
	notifReq := helper.SendNotificationRequest{
		ActorId:          loanRequest.GetActorId(),
		NotificationType: palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_LOAN_ACCOUNT_CLOSURE,
		Deeplink: &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_PRE_APPROVED_LOAN_DETAILS_SCREEN,
			ScreenOptions: &deeplinkPb.Deeplink_PreApprovedLoanDetailsScreenOptions{
				PreApprovedLoanDetailsScreenOptions: &deeplinkPb.PreApprovedLoanDetailsScreenOptions{
					LoanId: loanAccount.GetId(),
				},
			},
		},
	}
	p.rpcHelper.SendNotificationWithGoRoutine(ctx, notifReq)
	return res, nil
}

func (p *Processor) CheckForLimitChange(ctx context.Context, req *palActivityPb.FetchAvailableLimitRequest) (*palActivityPb.FetchAvailableLimitResponse, error) {
	res := &palActivityPb.FetchAvailableLimitResponse{ResponseHeader: &activityPb.ResponseHeader{}}
	lg := activity.GetLogger(ctx)

	loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("no such loan request exists", zap.Any(logger.CLIENT_REQUEST_ID, req.GetRequestHeader().GetClientReqId()), zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
		}
		lg.Error("failed to fetch loan request id by orch id", zap.Error(err))
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	limitResp, rpcErr := p.limitEstimatorClient.GetLoanConservativeLimit(ctx, &limitEstimatorPb.GetLoanConservativeLimitRequest{
		ActorId:          loanRequest.GetActorId(),
		Vendor:           loanRequest.GetVendor(),
		OfferId:          loanRequest.GetOfferId(),
		ShouldCallVendor: true,
	})
	if err = epifigrpc.RPCError(limitResp, rpcErr); err != nil {
		lg.Error("error fetching available limit for pre approved loans", zap.Error(err))
		if limitResp.GetStatus().IsRecordNotFound() || limitResp.GetStatus().IsResourceExhausted() || limitResp.GetStatus().IsAlreadyExists() {
			loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
			loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INSUFFICIENT_LIMIT
			loanRequest.CompletedAt = timestampPb.Now()
			updateErr := p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
				palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
			})
			if updateErr != nil {
				lg.Error("error updating loan request", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, updateErr.Error())
			}
			return nil, errors.Wrap(epifierrors.ErrPermanent, err.Error())
		}
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	// Check if loan amount is still below conservative limit available, else cancel application
	if money.Compare(loanRequest.GetDetails().GetLoanInfo().GetAmount(), limitResp.GetConservativeLimit()) == 1 {
		lg.Info("available limit changed from the start of on-boarding")
		loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
		loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_INSUFFICIENT_LIMIT
		loanRequest.CompletedAt = timestampPb.Now()
		err = p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
		})
		if err != nil {
			lg.Error("error updating loan request", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		return nil, errors.Wrap(epifierrors.ErrPermanent, "limit changed mid on-boarding")
	}

	res.AvailableLimit = limitResp.GetConservativeLimit()
	return res, nil
}

func (p *Processor) pushApiTriggeredEvent(ctx context.Context, actorId string, apiName string, reqParams interface{}, flowName string) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewApiTriggeredEvent(actorId, apiName, reqParams, flowName))
	})
}

func (p *Processor) pushApiResponseEvent(ctx context.Context, actorId string, apiName string, timeTaken time.Duration, status string, substatus string, resParams interface{}, flowName string) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewApiResponseEvent(actorId, apiName, timeTaken, status, substatus, resParams, flowName))
	})
}

// CheckHardOffer checks if a hard offer exists for the given actor, vendor and loan program
func (p *Processor) CheckHardOffer(ctx context.Context, req *palActivityPb.CheckHardOffersRequest) (*palActivityPb.CheckHardOffersResponse, error) {
	lg := activity.GetLogger(ctx)

	offer, err := p.loanOffersDao.GetLatestByActorIdVendorAndLoanProgram(ctx, req.GetActorId(),
		req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram())
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		lg.Error("failed to get latest loan offer", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get latest loan offer, err: %v", err))
	}

	if offer.IsActiveNow() && offer.GetLoanOfferType() == palPb.LoanOfferType_LOAN_OFFER_TYPE_HARD {
		return &palActivityPb.CheckHardOffersResponse{
			HardOfferExists: true,
		}, nil
	}

	return &palActivityPb.CheckHardOffersResponse{
		HardOfferExists: false,
	}, nil
}

// TODO(harish): add UTs for the activity
func (p *Processor) CreateLoanStepExecution(ctx context.Context, req *palActivityPb.CreateLoanStepExecutionRequest) (*palActivityPb.CreateLoanStepExecutionResponse, error) {
	res := &palActivityPb.CreateLoanStepExecutionResponse{
		ResponseHeader: &activityPb.ResponseHeader{},
	}
	lg := activity.GetLogger(ctx)

	var actorId, refId string
	switch req.GetFlow() {
	case palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_COLLECTIONS,
		palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_PRE_PAY,
		palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_OFF_APP_PRE_PAY,
		palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_AUTO_PAY:
		loanPaymentRequest, err := p.loanPaymentRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			lg.Error("failed to fetch loan payment request by orch id", zap.Error(err))
			return nil, GetActivityErrFromDaoError(err)
		}
		actorId = loanPaymentRequest.GetActorId()
		refId = loanPaymentRequest.GetId()
	case palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION:
		loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil {
			lg.Error("failed to fetch loan request by orch id", zap.Error(err))
			return nil, GetActivityErrFromDaoError(err)
		}
		actorId = loanRequest.GetActorId()
		refId = loanRequest.GetId()
	default:
		// fallback, best effort
		loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
		if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
			loanPaymentRequest, loanPaymentRequestErr := p.loanPaymentRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
			if loanPaymentRequestErr != nil {
				lg.Error("failed to fetch loan payment request by orch id", zap.Error(loanPaymentRequestErr))
				return nil, GetActivityErrFromDaoError(loanPaymentRequestErr)
			}
			actorId = loanPaymentRequest.GetActorId()
			refId = loanPaymentRequest.GetId()
		}
		if err != nil {
			lg.Error("failed to fetch loan request by orch id", zap.Error(err))
			return nil, GetActivityErrFromDaoError(err)
		}
		if err == nil {
			actorId = loanRequest.GetActorId()
			refId = loanRequest.GetId()
		}
	}

	// check if any non-terminal loan step execution with the same loan request ID, step name and flow already exists in the system
	loanStepExecution, err := p.loanStepExecutionDao.GetActiveByRefIdFlowAndName(ctx, refId, req.GetFlow(), req.GetStepName())
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		// create lse if we got no record in the system
		loanStepExecution, err = p.loanStepExecutionDao.Create(ctx,
			&palPb.LoanStepExecution{
				ActorId:    actorId,
				RefId:      refId,
				Flow:       req.GetFlow(),
				OrchId:     uuid.New().String(),
				StepName:   req.GetStepName(),
				Details:    &palPb.LoanStepExecutionDetails{},
				Status:     palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED,
				GroupStage: req.GetGroupStage(),
			},
		)
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to create a loan step execution, %v", err))
		}
	} else if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch already existing lse, %v", err))
	}

	res.LoanStep = loanStepExecution
	return res, nil
}

func GetActivityErrFromDaoError(err error) error {
	if err == nil {
		return nil
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		return errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("no record found in db, err: %v", err))
	}
	return errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch record from Db, err: %v", err))
}

func (p *Processor) UpdateLoanStepExecution(ctx context.Context, req *palActivityPb.UpdateLoanStepExecutionRequest) (*palActivityPb.PalActivityResponse, error) {
	err := p.loanStepExecutionDao.Update(ctx, req.GetLoanStep(), req.GetLseFieldMasks())
	if err != nil && !errors.Is(err, epifierrors.ErrRowNotUpdated) {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loan step execution, %v", err))
	} else if err != nil {
		return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("lse record not found, err: %v", err))
	}
	return nil, nil
}

func (p *Processor) UpdateLRNextAction(ctx context.Context, req *palActivityPb.UpdateLRNextActionRequest) (*palActivityPb.PalActivityResponse, error) {
	loanRequest, err := p.loanRequestDao.GetById(ctx, req.GetLoanRequestId())
	if err != nil {
		return nil, GetActivityErrFromDaoError(err)
	}

	if req.GetNextAction() != nil && req.GetNextAction().GetScreenOptions() == nil && req.GetNextAction().GetScreenOptionsV2() == nil {
		req.NextAction, err = p.getDeeplinkFromScreen(ctx, loanRequest, req.GetLoanStep(), req.GetNextAction().GetScreen(), req.GetLoecOwner())
		if err != nil {
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get deeplink: %v", err))
		}
	}

	loanRequest.NextAction = req.GetNextAction()
	updateErr := p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
		palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION,
	})
	if updateErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in lr update, err: %v", updateErr))
	}
	return nil, nil
}

func (p *Processor) GetLoanRequest(ctx context.Context, req *palActivityPb.GetLoanRequestActivityRequest) (*palActivityPb.GetLoanRequestActivityResponse, error) {
	var loanRequest *palPb.LoanRequest
	switch {
	case req.GetLoanRequestId() != "":
		var err error
		loanRequest, err = p.loanRequestDao.GetById(ctx, req.GetLoanRequestId())
		if err != nil {
			return nil, GetActivityErrFromDaoError(err)
		}
	case req.GetOrchId() != "":
		var err error
		loanRequest, err = p.loanRequestDao.GetByOrchId(ctx, req.GetOrchId())
		if err != nil {
			return nil, GetActivityErrFromDaoError(err)
		}
	default:
		return nil, fmt.Errorf("loan request id and orch if both are empty: %w", epifierrors.ErrPermanent)
	}

	return &palActivityPb.GetLoanRequestActivityResponse{
		LoanRequest: loanRequest,
	}, nil
}

func (p *Processor) GetLoanOffer(ctx context.Context, req *palActivityPb.GetLoanOfferActivityRequest) (*palActivityPb.GetLoanOfferActivityResponse, error) {
	loanOffer, err := p.loanOffersDao.GetById(ctx, req.GetOfferId())
	if err != nil {
		return nil, GetActivityErrFromDaoError(err)
	}
	return &palActivityPb.GetLoanOfferActivityResponse{
		LoanOffer: loanOffer,
	}, nil
}

func (p *Processor) getDeeplinkFromScreen(ctx context.Context, lr *palPb.LoanRequest, lse *palPb.LoanStepExecution, screen deeplinkPb.Screen, loecOwner palPb.Vendor) (*deeplinkPb.Deeplink, error) {
	if lr.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_LAMF {
		return p.getLamfDeeplinkFromScreen(ctx, lr, lse, screen)
	}
	vendor := lr.GetVendor()
	// in case of eligibility lrs are always in epifi tech so we can not fetch vendor details from them
	// so instead we use loec ownership.
	if lr.GetLoanProgram() == palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY && loecOwner != palPb.Vendor_VENDOR_UNSPECIFIED {
		vendor = loecOwner
	}
	deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: vendor, LoanProgram: lr.GetLoanProgram()})
	// TODO(prasoon): move below mapping to some common place
	switch screen {
	case deeplinkPb.Screen_PRE_APPROVED_ADDRESS_CONFIRMATION_SCREEN,
		deeplinkPb.Screen_EARLY_SALARY_ADDRESS_CONFIRMATION_SCREEN:
		user, userErr := p.rpcHelper.GetUserByActorId(ctx, lr.GetActorId())
		if userErr != nil {
			return nil, errors.Wrap(userErr, "failed to get user")
		}
		return deeplinkProvider.GetAddressConfirmationScreen(deeplinkProvider.GetLoanHeader(), lr.GetId(), user.GetProfile().GetPanName().ToString()), nil
	case deeplinkPb.Screen_PRE_APPROVED_EMPLOYMENT_DETAILS_SCREEN:
		return deeplinkProvider.GetEmploymentDetailsDeeplink(deeplinkProvider.GetLoanHeader(), lr.GetId()), nil
	case deeplinkPb.Screen_PL_UPDATED_RATE_SCREEN:
		return deeplinkProvider.GetPlUpdatedRateScreenDeeplink(deeplinkProvider.GetLoanHeader(), lr), nil

	case deeplinkPb.Screen_AUTH_STATUS_POLL_SCREEN:
		return deeplinkProvider.GetAuthPollScreen(deeplinkProvider.GetLoanHeader(), lse.GetOrchId()), nil

	// status poll for both pre-approved loan and early salary
	case deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN:
		return deeplinkProvider.GetPollScreenDeepLink(deeplinkProvider.GetLoanHeader(), lse)
	case deeplinkPb.Screen_PRE_APPROVED_LOAN_ERROR_SCREEN:
		if lse.GetStepName() == palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_CKYC && lse.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED {
			return deeplinkProvider.GetFailedCkycCheckDeeplink(deeplinkProvider.GetLoanHeader()), nil
		}
		return &deeplinkPb.Deeplink{Screen: screen}, nil
	case deeplinkPb.Screen_PL_BANKING_DETAILS_SCREEN:
		return deeplinkProvider.GetBankingDetailsScreenDeeplink(deeplinkProvider.GetLoanHeader(), lr.GetId(), lse.GetId(), pal_enums.SearchIfscType_SEARCH_IFSC_TYPE_UNSPECIFIED, preapprovedloans.BankingDetailsBottomSheetType_BANKING_DETAILS_BOTTOM_SHEET_TYPE_UNSPECIFIED, "")
	case deeplinkPb.Screen_LOANS_FORM_DETAILS_SCREEN:
		return deeplinkProvider.GetReferenceScreenDeeplink(ctx, deeplinkProvider.GetLoanHeader(), lr.GetId())
	case deeplinkPb.Screen_LOAN_ADDRESS_VERIFICATION_INTRO_SCREEN:
		return deeplinkProvider.GetAddressVerificationIntroScreen(deeplinkProvider.GetLoanHeader(), lr.GetId()), nil
	case deeplinkPb.Screen_LOANS_CONSENT_SCREEN,
		deeplinkPb.Screen_LOANS_CONSENT_V2_SCREEN:
		preBreConsentDeeplink, err := deeplinkProvider.GetPreBreLoanConsentScreen(deeplinkProvider.GetLoanHeader(), lr.GetId())
		if err != nil {
			return nil, errors.Wrap(err, "error getting pre bre consent deeplink")
		}
		if preBreConsentDeeplink == nil {
			return nil, errors.Errorf("pre bre consent deeplink is nil for loan request id: %s, ", lr.GetId())
		}
		return preBreConsentDeeplink, nil
	case deeplinkPb.Screen_LOANS_OFFER_INTRO_SCREEN:
		switch lr.GetLoanProgram() {
		case palPb.LoanProgram_LOAN_PROGRAM_ELIGIBILITY, palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION:
			return deeplinkProvider.GetPreBreLoansOfferIntroScreen(deeplinkProvider.GetLoanHeader(), lr.GetId())
		default:
			return nil, errors.New("unimplemented screen")
		}
	default:
		// TODO(prasoon): return error instead of empty deeplink
		return &deeplinkPb.Deeplink{Screen: screen}, nil
	}
}

func (p *Processor) getLamfDeeplinkFromScreen(ctx context.Context, lr *palPb.LoanRequest, _ *palPb.LoanStepExecution, screen deeplinkPb.Screen) (*deeplinkPb.Deeplink, error) {
	deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: lr.GetVendor(), LoanProgram: lr.GetLoanProgram()})
	if screen == deeplinkPb.Screen_PRE_APPROVED_LOAN_APPLICATION_STATUS_POLL_SCREEN {
		return deeplinkProvider.GetLoansApplicationStatusPollDeeplink(ctx, deeplinkProvider.GetLoanHeader(), lr.GetActorId(), lr.GetId(), &provider.ApplicationStatusPollDeeplinkParams{
			Icon:     nulltypes.EmptyNullString(),
			Title:    nulltypes.NewNullString("Please wait"),
			SubTitle: nulltypes.EmptyNullString(),
		})
	}
	return &deeplinkPb.Deeplink{Screen: screen}, nil
}

func (p *Processor) isDownTime(ctx context.Context) bool {
	downTimeStart, err := time.ParseInLocation(layout, "01-04-2023T00:00:00", datetime.IST)
	if err != nil {
		logger.Error(ctx, "failed to parse string to time")
		return false
	}

	downTimeEnd, err := time.ParseInLocation(layout, "01-04-2023T23:59:59", datetime.IST)
	if err != nil {
		logger.Error(ctx, "failed to parse string to time")
		return false
	}
	if time.Now().After(downTimeStart) && time.Now().Before(downTimeEnd) {
		return true
	}
	return false
}

func (p *Processor) CheckForRisk(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	res := &palActivityPb.PalActivityResponse{}
	lg := activity.GetLogger(ctx)

	loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetRequestHeader().GetClientReqId())
	if err != nil {
		lg.Error("failed to fetch loan request by orch Id", zap.Error(err))
		return nil, GetActivityErrFromDaoError(err)
	}

	loanStepExecution, err := p.createLoanStepExecution(ctx, loanRequest,
		palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
		palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_RISK_CHECK, nil)
	if err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}

	var errToReturn error
	isRiskyUser, riskErr := p.rpcHelper.IsRiskyUser(ctx, loanRequest.GetActorId())
	if riskErr != nil {
		lg.Error("failed to check risk profile for PL", zap.Error(riskErr))
		return nil, errors.Wrap(epifierrors.ErrTransient, riskErr.Error())
	}

	if isRiskyUser {
		loanRequest.Status = palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED
		loanRequest.SubStatus = palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_FAILED_RISK_CHECK
		loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
		loanStepExecution.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED
		updateErr := p.loanRequestDao.Update(ctx, loanRequest, []palPb.LoanRequestFieldMask{
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS,
			palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT,
		})
		if updateErr != nil {
			lg.Error("failed to update loan request", zap.Error(updateErr))
			return nil, errors.Wrap(epifierrors.ErrTransient, err.Error())
		}
		errToReturn = epifitemporal.NewPermanentError(fmt.Errorf("application failed, risky user"))
	}
	if errToReturn == nil {
		loanStepExecution.Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS
	}
	loanStepExecution.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
	updateLsErr := p.loanStepExecutionDao.Update(ctx, loanStepExecution, []palPb.LoanStepExecutionFieldMask{
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS,
		palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT,
	})
	if updateLsErr != nil {
		lg.Error("failed to update loan step", zap.Error(updateLsErr))
		return nil, epifitemporal.NewTransientError(updateLsErr)
	}
	return res, errToReturn
}

func (p *Processor) getLivenessSummary(ctx context.Context, lse *palPb.LoanStepExecution) (*livenessPb.LivenessSummary, error) {
	lvSummaryReqId, err := p.authClient.GetAuthStageRefId(ctx, &authPb.GetAuthStageRefIdRequest{
		ClientRequestId: lse.GetOrchId(),
		Stage:           authPb.AuthStage_AUTH_STAGE_LIVENESS_SUMMARY,
	})
	if te := epifigrpc.RPCError(lvSummaryReqId, err); te != nil {
		return nil, fmt.Errorf("failed to get auth stage refId %w", te)
	}
	lvSummary, err := p.livenessClient.GetLivenessSummary(ctx, &livenessPb.GetLivenessSummaryRequest{
		ActorId:      lse.GetActorId(),
		RequestId:    lvSummaryReqId.GetAuthStageRefId(),
		LivenessFlow: livenessPb.LivenessFlow_PRE_APPROVED_LOANS,
	})
	if te := epifigrpc.RPCError(lvSummary, err); te != nil {
		return nil, fmt.Errorf("failed to get liveness summary %w", te)
	}

	return lvSummary.GetSummary(), nil
}

func (p *Processor) AsyncStepTerminalStatusCheck(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	res := &palActivityPb.PalActivityResponse{
		LoanStep:      req.GetLoanStep(),
		LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
	}
	// fetch the loan step to get the latest status
	loanStep, err := p.loanStepExecutionDao.GetById(ctx, req.GetLoanStep().GetId())
	if err != nil {
		logger.Error(ctx, "error in fetching loan step", zap.Error(err))
		return res, errors.Wrap(epifierrors.ErrTransient, err.Error())
	}
	res.LoanStep = loanStep

	// If loan step not in terminal state, return nil
	if loanStep.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED ||
		loanStep.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS {
		return res, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("%s pending", loanStep.GetStepName().String()))
	}
	if loanStep.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED ||
		loanStep.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION ||
		loanStep.GetStatus() == palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED {
		return res, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("%s pending permanent", loanStep.GetStepName().String()))
	}
	return res, nil
}

// AsyncStepTerminalStatusCheckV2 is same as AsyncStepTerminalStatusCheck but with an increased schedule to close timeout
func (p *Processor) AsyncStepTerminalStatusCheckV2(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	return p.AsyncStepTerminalStatusCheck(ctx, req)
}

func (p *Processor) UpdateLoanRequestV2(ctx context.Context, req *palActivityPb.UpdateLoanRequestActivityV2Request) (*palActivityPb.UpdateLoanRequestActivityV2Response, error) {
	lg := activity.GetLogger(ctx)
	res := &palActivityPb.UpdateLoanRequestActivityV2Response{}

	loanRequest, err := p.loanRequestDao.GetByOrchId(ctx, req.GetOrchId())
	if err != nil {
		lg.Error("failed to get loan request by orch id", zap.Error(err))
		return nil, GetActivityErrFromDaoError(err)
	}

	// if loan request is already in cancelled state, don't update the status
	if loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED {
		return nil, errors.Wrap(epifierrors.ErrPermanent, "updating loan request failed as already in cancelled state")
	}
	var fieldMasks []palPb.LoanRequestFieldMask

	if req.GetLoanRequestStatus() != palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_UNSPECIFIED {
		loanRequest.Status = req.GetLoanRequestStatus()
		fieldMasks = append(fieldMasks, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_STATUS)
	}
	if req.GetLoanRequestSubStatus() != palPb.LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED {
		loanRequest.SubStatus = req.GetLoanRequestSubStatus()
		fieldMasks = append(fieldMasks, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_SUB_STATUS)
	}

	if loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_CANCELLED ||
		loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_FAILED ||
		loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_SUCCESS ||
		loanRequest.GetStatus() == palPb.LoanRequestStatus_LOAN_REQUEST_STATUS_MANUAL_INTERVENTION {
		loanRequest.CompletedAt = timestampPb.New(time.Now().In(datetime.IST))
		fieldMasks = append(fieldMasks, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_COMPLETED_AT)
	}
	err = p.loanRequestDao.Update(ctx, loanRequest, fieldMasks)
	if err != nil {
		lg.Error("failed to update loan request field masks", zap.Error(err))
		return nil, GetActivityErrFromDaoError(err)
	}

	return res, nil
}

func IsTerminalStep(lse *palPb.LoanStepExecution) bool {
	switch lse.GetStatus() {
	case palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS,
		palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED,
		palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_MANUAL_INTERVENTION,
		palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CANCELLED:
		return true
	default:
		return false
	}
}

func (p *Processor) AsyncLoanStepStateCheck(ctx context.Context, req *palActivityPb.AsyncLoanStepStateCheckRequest) (*palActivityPb.AsyncLoanStepStateCheckResponse, error) {
	res := &palActivityPb.AsyncLoanStepStateCheckResponse{}
	// fetch the loan step to get the latest status
	loanStep, err := p.loanStepExecutionDao.GetById(ctx, req.GetLseId())
	if err != nil {
		logger.Error(ctx, "error in fetching loan step", zap.Error(err), zap.String(logger.LOAN_STEP_EXECUTION_ID, loanStep.GetId()))
		return res, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get lse by id, %v", err.Error()))
	}

	// If loan step status and sub-status  matches with required value returning
	if loanStep.GetSubStatus() == req.GetLseSubStatus() && loanStep.GetStatus() == req.GetLseStatus() {
		return res, nil
	}
	return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("%s pending", loanStep.GetStepName().String()))
}

// DeactivateLoanOffer evaluates the config to decide if loan offer needs to be deactivated or not.
// this activity will be executed for all stages, and inside activity decision is made whether to deactivate the loan offer or not
func (p *Processor) DeactivateLoanOffer(ctx context.Context, req *palActivityPb.DeactivateLoanOfferRequest) (*palActivityPb.DeactivateLoanOfferResponse, error) {
	res := &palActivityPb.DeactivateLoanOfferResponse{}
	lg := activity.GetLogger(ctx)
	// fetch the loan request to get the relevant data
	lr, lrErr := p.loanRequestDao.GetById(ctx, req.GetLoanStep().GetRefId())
	if lrErr != nil {
		lg.Error("error while fetching loan request", zap.Error(lrErr), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanStep().GetRefId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while fetching loan request, err: %v", lrErr.Error()))
	}

	key := lr.GetVendor().String() + ":" + lr.GetLoanProgram().String()
	val := p.config.OfferDeactivationConfig().Get(key)
	if val == nil {
		return res, nil
	}

	lseData := req.GetLoanStep().GetStepName().String() + ":" + req.GetLoanStep().GetStatus().String() + ":" + req.GetLoanStep().GetSubStatus().String()
	if !lo.Contains(val.LoanSteps().ToStringArray(), lseData) {
		return res, nil
	}

	loanOffer, loErr := p.loanOffersDao.GetById(ctx, lr.GetOfferId())
	if loErr != nil {
		lg.Error("error while fetching loan offer to mark it as deactivated", zap.Error(loErr), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanStep().GetRefId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while fetching loan offer to mark it as deactivated, err: %v", loErr.Error()))
	}
	var loec *palPb.LoanOfferEligibilityCriteria
	var err error
	if loanOffer.GetLoanOfferEligibilityCriteriaId() != "" {
		loec, err = p.loanOfferEligibilityCriteriaDao.GetById(ctx, loanOffer.GetLoanOfferEligibilityCriteriaId())
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("error in getting loec", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanStep().GetRefId()))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error while fetching loec, err: %v", err.Error()))
		}
	}

	txnExec, err := p.txnExecutorProvider.GetResourceForOwnership(epificontext.OwnershipFromContext(ctx))
	if err != nil {
		lg.Error("error in getting txn executor", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanStep().GetRefId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in getting txn executor, err: %v", err.Error()))
	}

	err = txnExec.RunTxn(ctx, func(ctx context.Context) error {
		updateErr := p.loanOffersDao.DeactivateLoanOffer(ctx, loanOffer.GetId())
		if updateErr != nil {
			return errors.Wrap(updateErr, "error in deactivating offer")
		}

		if loec != nil {
			loec.Status = palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED
			loec.SubStatus = palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR
			loecErr := p.loanOfferEligibilityCriteriaDao.Update(ctx, loec, []palPb.LoanOfferEligibilityCriteriaFieldMask{
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
				palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
			})
			if loecErr != nil {
				return errors.Wrap(loecErr, "error in updating loec")
			}
		}

		return nil
	})
	if err != nil {
		lg.Error("error in updating lr and loec", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLoanStep().GetRefId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating lr and loec, err: %v", err.Error()))
	}

	// Publish event for terminal rejection (when we actually deactivate the offer)
	p.publishLoanOfferDeactivatedEvent(ctx, lr)

	return res, nil
}

func (p *Processor) publishLoanOfferDeactivatedEvent(ctx context.Context, lr *palPb.LoanRequest) {
	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		p.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), palEvents.NewLoanOfferDeactivated(
			lr.GetActorId(),
			lr.GetOfferId(),
			lr.GetId(),
			lr.GetVendor().String(),
			lr.GetLoanProgram().String(),
			palEvents.PlLoanApplicationRejectedStatusTerminal,
		))
	})
}

func (p *Processor) GetExpiredPropertyForStage(ctx context.Context, req *palActivityPb.GetExpiredPropertyForStageRequest) (*palActivityPb.GetExpiredPropertyForStageResponse, error) {
	res := &palActivityPb.GetExpiredPropertyForStageResponse{}
	// fetch the loan step to get the latest status
	loanStep, err := p.loanStepExecutionDao.GetById(ctx, req.GetLseId())
	if err != nil {
		logger.Error(ctx, "error in fetching loan step", zap.Error(err), zap.String(logger.LOAN_STEP_EXECUTION_ID, loanStep.GetId()))
		return res, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get lse by id, %v", err.Error()))
	}

	// If loan step status and sub-status  matches with required value returning
	if lo.Contains(req.GetLseSubStatus(), loanStep.GetSubStatus()) && loanStep.GetStatus() == req.GetLseStatus() {
		res.MarkNonRetryableAsExpired = true
	}
	return res, nil
}

func (p *Processor) UpdateLoec(ctx context.Context, req *palActivityPb.UpdateLoecRequest) (*palActivityPb.PalActivityResponse, error) {
	res := &palActivityPb.PalActivityResponse{
		LoanStep: req.GetLoanStep(),
	}
	loecs, loecsErr := p.loanOfferEligibilityCriteriaDao.GetByActorIdLoanProgramsAndStatuses(ctx, req.GetLoanStep().GetActorId(), []palPb.LoanProgram{req.GetLoanProgram()},
		[]palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED},
		0, true)
	if loecsErr != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch latest loec, err: %v", loecsErr))
	}

	latestLoec := loecs[0]
	latestLoec.Status = req.GetLoanOfferEligibilityCriteriaStatus()
	latestLoec.SubStatus = req.GetLoanOfferEligibilityCriteriaSubStatus()
	if err := p.loanOfferEligibilityCriteriaDao.Update(ctx, latestLoec, []palPb.LoanOfferEligibilityCriteriaFieldMask{
		palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
		palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS,
	}); err != nil {
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loec, err: %v", loecsErr))
	}

	return res, nil
}

func (p *Processor) UpdateSyncStatus(ctx context.Context, req *palActivityPb.UpdateSyncStatusRequest) (*palActivityPb.UpdateSyncStatusResponse, error) {
	lg := activity.GetLogger(ctx)
	lr, err := p.loanRequestDao.GetById(ctx, req.GetLrId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			lg.Error("loan request not found", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLrId()))
			return nil, errors.Wrap(epifierrors.ErrPermanent, "loan request not found")
		}
		lg.Error("failed to fetch loan request by id", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLrId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to fetch loan request by id, err: %v", err))
	}
	if lr.GetDetails() == nil {
		lr.Details = &palPb.LoanRequestDetails{}
	}
	lr.GetDetails().IsInSyncMode = req.GetIsInSyncMode()
	err = p.loanRequestDao.Update(ctx, lr, []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_DETAILS})
	if err != nil {
		lg.Error("failed to update loan request", zap.Error(err), zap.String(logger.LOAN_REQUEST_ID, req.GetLrId()))
		return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update loan request, err: %v", err))
	}
	return &palActivityPb.UpdateSyncStatusResponse{}, nil
}
