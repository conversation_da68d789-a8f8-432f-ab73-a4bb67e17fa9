package brecaller

import (
	"context"

	"github.com/epifi/gamma/api/preapprovedloan"
)

// IBreCaller calls the bre and checks if there is  some offer for the given actor against the
// given loec.
type IBreCaller interface {
	CheckAndGetLoanOffer(ctx context.Context, req *CheckAndGetLoanOfferRequest) (*CheckAndGetLoanOfferResponse, error)
	FetchHardVendorLoanOffer(ctx context.Context, req *CheckAndGetLoanOfferRequest) (*CheckAndGetLoanOfferResponse, error)
}

type CheckAndGetLoanOfferRequest struct {
	Loec *preapprovedloan.LoanOfferEligibilityCriteria
	Lse  *preapprovedloan.LoanStepExecution
}

type CheckAndGetLoanOfferResponse struct {
	UpdatedLoec     *preapprovedloan.LoanOfferEligibilityCriteria
	UpdateFieldMask []preapprovedloan.LoanOfferEligibilityCriteriaFieldMask
	LoanOffer       *preapprovedloan.LoanOffer
}
