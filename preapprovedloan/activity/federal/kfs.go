//nolint:funlen
package federal

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/pkg/errors"
	"go.temporal.io/sdk/activity"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	eSignPb "github.com/epifi/gamma/api/docs/esign"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	palCommsPb "github.com/epifi/gamma/api/preapprovedloan/comms"
	palActivity "github.com/epifi/gamma/preapprovedloan/activity"
	"github.com/epifi/gamma/preapprovedloan/deeplink"
)

func (p *Processor) CheckKfsStatusV2(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	res, err := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}

		lg := activity.GetLogger(ctx)

		loanRequest, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				lg.Error("no such loan request exists", zap.Error(err))
				return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("lr record not found in CheckKfsStatusV2, err: %v", err))
			}
			lg.Error("failed to fetch loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get the lr in CheckKfsStatusV2, err: %v", err))
		}

		eSignRes, err := p.esignClient.CheckESignStatus(ctx, &eSignPb.CheckESignStatusRequest{
			ClientRequestId: loanRequest.GetOrchId(),
			Client:          eSignPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2,
		})

		if te := epifigrpc.RPCError(eSignRes, err); te != nil {
			lg.Error("failed to check e-sign status")
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to the status of e-sign in CheckKfsStatusV2, err: %v", te))
		}

		switch eSignRes.GetESignStatus() {
		case eSignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_PENDING_AT_BANK:
			res.GetLoanStep().SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KFS_PENDING_AT_BANK
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
			updateLseErr := p.loanStepExecutionDao.Update(ctx, res.GetLoanStep(), []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS})
			if updateLseErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating lse, err: %v", updateLseErr))
			}
			lg.Error("esign status is pending at bank in CheckKfsStatusV2")
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("esign status is pending at bank in CheckKfsStatusV2"))

		case eSignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_CREATED,
			eSignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_PENDING:
			p.rpcHelper.CheckAndSendDropOffComms(ctx, res.GetLoanStep(), palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_ESIGN_PENDING)
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
			updateErr := p.updateNextActionInLoanRequest(ctx, loanRequest.GetId(), p.deeplinkProvider.GetInitiateESignScreen(p.deeplinkProvider.GetLoanHeader(), loanRequest.GetId(), ""))
			if updateErr != nil {
				lg.Error("failed to update LR next action in CheckKfsStatusV2", zap.Error(updateErr))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to update LR next action in CheckKfsStatusV2, err: %v", updateErr))
			}
			updateLseErr := p.loanStepExecutionDao.Update(ctx, res.GetLoanStep(), []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS})
			if updateLseErr != nil {
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating lse, err: %v", updateLseErr))
			}

			// Fed PL users getting dropped of due to some client issue in KFS stage
			// TODO: Remove this once client location issue is fixed and all app versions are updated
			// Check if loanStep was created in the current 4-hour window (between X hours and X hours 40 minutes)
			// but only after the first 4-hour window has passed
			now := time.Now()
			createdAt := res.GetLoanStep().GetCreatedAt().AsTime()
			hoursSinceCreation := now.Sub(createdAt).Hours()
			// Only proceed if we're past the first 4-hour window
			if hoursSinceCreation >= 4 {
				windowStart := math.Floor(hoursSinceCreation/4) * 4
				windowEnd := windowStart + (40.0 / 60.0) // 40 minutes in hours
				// if the current time falls within this window (between X hours and X hours 40 minutes), we log the message
				if hoursSinceCreation >= windowStart && hoursSinceCreation <= windowEnd {
					preKfsLse, preKfsErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanStep().GetRefId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
						palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS)
					if preKfsErr != nil {
						lg.Error("failed to fetch pre kfs loan step execution by ref id and flow and name", zap.Error(preKfsErr), zap.String(logger.ACTOR_ID_V2, res.GetLoanStep().GetActorId()))
						return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get the pre kfs lse in CheckKfsStatusV2, err: %v", preKfsErr))
					}

					lg.Info("federal kfs status user drop off log in 4-hour window, ActorId: "+res.GetLoanStep().GetActorId()+", Sign URl: "+preKfsLse.GetDetails().GetESignStepData().GetSignUrl(),
						zap.String("actor_id", res.GetLoanStep().GetActorId()))
				}
			}

			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("esign status is in created or pending stage in CheckKfsStatusV2"))

		case eSignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_FAILED:
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED

		case eSignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_SUCCESS:
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS

		default:
			return nil, errors.Wrap(epifierrors.ErrTransient, "unknown esign status in CheckKfsStatusV2")
		}

		return res, nil
	})
	return res, err
}

//nolint:funlen
func (p *Processor) FedKfsInSync(ctx context.Context, req *palActivityPb.PalActivityRequest) (*palActivityPb.PalActivityResponse, error) {
	res, err := palActivity.ExecuteWork(ctx, p.loanStepExecutionDao, req.GetLoanStep(), func(ctx context.Context, lse *palPb.LoanStepExecution) (*palActivityPb.PalActivityResponse, error) {
		res := &palActivityPb.PalActivityResponse{
			LoanStep:      lse,
			LseFieldMasks: []palPb.LoanStepExecutionFieldMask{},
		}

		lg := activity.GetLogger(ctx)

		loanRequest, err := p.loanRequestDao.GetById(ctx, lse.GetRefId())
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				lg.Error("no such loan request exists", zap.Error(err))
				return nil, errors.Wrap(epifierrors.ErrPermanent, fmt.Sprintf("lr record not found in FedKfsInSync, err: %v", err))
			}
			lg.Error("failed to fetch loan request by orch id", zap.Error(err))
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get the lr in FedKfsInSync, err: %v", err))
		}

		eSignRes, err := p.esignClient.CheckESignStatus(ctx, &eSignPb.CheckESignStatusRequest{
			ClientRequestId: loanRequest.GetOrchId(),
			Client:          eSignPb.EsignRequestClient_ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2,
		})
		if te := epifigrpc.RPCError(eSignRes, err); te != nil {
			lg.Error("failed to check e-sign status")
			return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("unable to the status of e-sign in FedKfsInSync, err: %v", te))
		}

		switch eSignRes.GetESignStatus() {
		case eSignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_PENDING_AT_BANK:
			if res.GetLoanStep().GetStatus() != palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS ||
				res.GetLoanStep().GetSubStatus() != palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KFS_PENDING_AT_BANK {
				res.GetLoanStep().SubStatus = palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_KFS_PENDING_AT_BANK
				res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
				updateLseErr := p.loanStepExecutionDao.Update(ctx, res.GetLoanStep(), []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS})
				if updateLseErr != nil {
					return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating lse, err: %v", updateLseErr))
				}
			}
			lg.Error("esign status is pending at bank in FedKfsInSync")
			return res, errors.Wrap(epifierrors.ErrTransient, "esign status is pending at bank in FedKfsInSync")

		case eSignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_CREATED,
			eSignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_PENDING:
			p.rpcHelper.CheckAndSendDropOffComms(ctx, res.GetLoanStep(), palCommsPb.PreApprovedLoanNotificationType_PRE_APPROVED_LOAN_NOTIFICATION_TYPE_ESIGN_PENDING)

			if res.GetLoanStep().GetStatus() != palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS {
				res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS
				updateLseErr := p.loanStepExecutionDao.Update(ctx, res.GetLoanStep(), []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS})
				if updateLseErr != nil {
					return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("error in updating lse, err: %v", updateLseErr))
				}
			}

			preKfsLse, preKfsErr := p.loanStepExecutionDao.GetByRefIdAndFlowAndName(ctx, req.GetLoanStep().GetRefId(), palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION,
				palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_PRE_KFS)
			if preKfsErr != nil {
				lg.Error("failed to fetch pre kfs loan step execution by ref id and flow and name", zap.Error(preKfsErr), zap.String(logger.ACTOR_ID_V2, res.GetLoanStep().GetActorId()))
				return nil, errors.Wrap(epifierrors.ErrTransient, fmt.Sprintf("failed to get the pre kfs lse in FedKfsInSync, err: %v", preKfsErr))
			}

			// Fed PL users getting dropped of due to some client issue in KFS stage
			// TODO: Remove this once client location issue is fixed and all app versions are updated
			// Check if loanStep was created in the current 4-hour window (between X hours and X hours 40 minutes)
			// but only after the first 4-hour window has passed
			now := time.Now()
			createdAt := res.GetLoanStep().GetCreatedAt().AsTime()
			hoursSinceCreation := now.Sub(createdAt).Hours()
			// Only proceed if we're past the first 4-hour window
			if hoursSinceCreation >= 4 {
				windowStart := math.Floor(hoursSinceCreation/4) * 4
				windowEnd := windowStart + (40.0 / 60.0) // 40 minutes in hours
				// if the current time falls within this window (between X hours and X hours 40 minutes), we log the message
				if hoursSinceCreation >= windowStart && hoursSinceCreation <= windowEnd {
					lg.Info("federal kfs status user drop off log in 4-hour window, ActorId: "+res.GetLoanStep().GetActorId()+", Sign URl: "+preKfsLse.GetDetails().GetESignStepData().GetSignUrl(),
						zap.String("actor_id", res.GetLoanStep().GetActorId()))
				}
			}

			if preKfsLse.GetDetails().GetESignStepData().GetSignUrl() == "" {
				lg.Error("fed esign url is empty", zap.String(logger.ACTOR_ID_V2, res.GetLoanStep().GetActorId()))
				return nil, errors.Wrap(epifierrors.ErrPermanent, "fed esign url is empty")
			}

			deeplinkProvider := p.deeplinkFactory.GetDeeplinkGenerator(ctx, &deeplink.GetDeeplinkProviderRequest{Vendor: loanRequest.GetVendor(), LoanProgram: loanRequest.GetLoanProgram()})
			res.NextAction = deeplinkProvider.GetInitiateESignScreen(deeplinkProvider.GetLoanHeader(), loanRequest.GetId(), preKfsLse.GetDetails().GetESignStepData().GetSignUrl())
			return res, errors.Wrap(epifierrors.ErrTransient, "esign status is in created or pending stage in FedKfsInSync")

		case eSignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_FAILED:
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED

		case eSignPb.EsignRequestStatus_ESIGN_REQUEST_STATUS_SUCCESS:
			res.GetLoanStep().Status = palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS

		default:
			return nil, errors.Wrap(epifierrors.ErrTransient, "unknown esign status in FedKfsInSync")
		}

		return res, nil
	})
	return res, err
}
