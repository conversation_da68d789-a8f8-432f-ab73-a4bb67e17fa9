package federal_test

import (
	"context"
	"testing"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/epifitemporal"
	palNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	actorPb "github.com/epifi/gamma/api/actor"
	bcPb "github.com/epifi/gamma/api/bankcust"
	kycPb "github.com/epifi/gamma/api/kyc"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
	types "github.com/epifi/gamma/api/typesv2"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/obfuscator"
	setuVgPb "github.com/epifi/gamma/api/vendorgateway/lending/setu"
)

func TestProcessor_GetSetuAaConsentUrl(t *testing.T) {
	type args struct {
		req *palActivityPb.PalActivityRequest
	}
	t.Parallel()
	tests := []struct {
		name       string
		args       args
		want       *palActivityPb.PalActivityResponse
		wantErr    bool
		setupMocks func(md *mockedDependencies)
		assertErr  func(err error) bool
	}{
		{
			name: "#1 success case",
			args: args{
				req: &palActivityPb.PalActivityRequest{
					LoanStep:    getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					Vendor:      palPb.Vendor_FEDERAL,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
				},
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: getMockLseWithSubStatus(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_IN_PROGRESS, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH, palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_WEB_URL_FETCHED),
			},
			wantErr:   false,
			assertErr: epifitemporal.IsRetryableError,
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), dummyLseId).Return(
					getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					nil).Times(1)
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: dummyActorId}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: dummyActorEntityId,
					},
				}, nil).Times(1)
				md.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: dummyActorEntityId},
				}).Return(&userPb.GetUserResponse{
					User:   &userPb.User{},
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				md.bcClient.EXPECT().GetBankCustomer(gomock.Any(), &bcPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bcPb.GetBankCustomerRequest_ActorId{
						ActorId: dummyActorId,
					},
				}).Return(&bcPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bcPb.BankCustomer{
						DedupeInfo: &bcPb.DedupeInfo{
							KycLevel: kycPb.KYCLevel_MIN_KYC,
						},
					},
				}, nil).Times(1)
				md.userClient.EXPECT().GetUserDeviceProperties(gomock.Any(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: dummyActorId,
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN,
					},
				}).Return(&userPb.GetUserDevicePropertiesResponse{
					Status: rpc.StatusOk(),
					UserDevicePropertyList: []*userPb.UserDeviceProperty{
						{
							DeviceProperty: types.DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN,
							PropertyValue: &types.PropertyValue{
								PropValue: &types.PropertyValue_IpAddressToken{
									IpAddressToken: "ip-token-1",
								},
							},
						},
					},
				}, nil).Times(1)
				md.obfuscatorClient.EXPECT().GetPIIFromToken(gomock.Any(), &obfuscator.GetPIIFromTokenRequest{Token: "ip-token-1"}).
					Return(&obfuscator.GetPIIFromTokenResponse{
						Status: rpc.StatusOk(),
						Pii: &types.Identifier{
							IdType: types.IdentifierType_IDENTIFIER_TYPE_IP_ADDRESS,
							IdValue: &types.IdentifierValue{
								PropValue: &types.IdentifierValue_IpAddress{IpAddress: &types.IpAddress{IpAddress: "ip-1"}},
							},
						},
					}, nil).Times(1)

				md.loanStepExecutionDao.EXPECT().GetByRefIdAndFlowAndName(gomock.Any(), dummyRefId, palPb.LoanStepExecutionFlow_LOAN_STEP_EXECUTION_FLOW_LOAN_APPLICATION, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT).Return(
					getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS, dummyRefId, dummyOrchId2, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_EMPLOYMENT), nil,
				).Times(1)
				md.setuVgClient.EXPECT().CreateConsent(gomock.Any(), gomock.Any()).Return(&setuVgPb.CreateConsentResponse{
					Status:     rpc.StatusOk(),
					ConsentId:  "consent-id",
					WebviewUrl: "url-link",
				}, nil).Times(1)

				md.loanStepExecutionDao.EXPECT().Update(gomock.Any(), gomock.Any(), []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS, palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_DETAILS}).DoAndReturn(
					func(ctx context.Context, lse *palPb.LoanStepExecution, fm []palPb.LoanStepExecutionFieldMask) error {
						if lse.GetDetails().GetVendorData() == "" {
							t.Errorf("error in validating rpc request payload in lse details")
							return nil
						}
						return nil
					},
				)

				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), []palPb.LoanRequestFieldMask{palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_NEXT_ACTION, palPb.LoanRequestFieldMask_LOAN_REQUEST_FIELD_MASK_VENDOR_REQUEST_ID}).Return(nil)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), dummyRefId).Return(getMockLr(), nil).Times(1)
			},
		},
		{
			name: "#2 unable to get bank customer",
			args: args{
				req: &palActivityPb.PalActivityRequest{
					LoanStep:    getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					Vendor:      palPb.Vendor_FEDERAL,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), dummyLseId).Return(
					getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					nil).Times(1)
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: dummyActorId}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: dummyActorEntityId,
					},
				}, nil).Times(1)
				md.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: dummyActorEntityId},
				}).Return(&userPb.GetUserResponse{
					User:   &userPb.User{},
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				md.bcClient.EXPECT().GetBankCustomer(gomock.Any(), &bcPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bcPb.GetBankCustomerRequest_ActorId{
						ActorId: dummyActorId,
					},
				}).Return(&bcPb.GetBankCustomerResponse{
					Status: rpc.StatusInternal(),
				}, nil).Times(1)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), dummyRefId).Return(getMockLr(), nil).Times(1)
			},
		},
		{
			name: "#3 unable to fetch the IP address",
			args: args{
				req: &palActivityPb.PalActivityRequest{
					LoanStep:    getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					Vendor:      palPb.Vendor_FEDERAL,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), dummyLseId).Return(
					getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					nil).Times(1)
				md.actorClient.EXPECT().GetActorById(gomock.Any(), &actorPb.GetActorByIdRequest{Id: dummyActorId}).Return(&actorPb.GetActorByIdResponse{
					Status: rpc.StatusOk(),
					Actor: &types.Actor{
						EntityId: dummyActorEntityId,
					},
				}, nil).Times(1)
				md.userClient.EXPECT().GetUser(gomock.Any(), &userPb.GetUserRequest{
					Identifier: &userPb.GetUserRequest_Id{Id: dummyActorEntityId},
				}).Return(&userPb.GetUserResponse{
					User:   &userPb.User{},
					Status: rpc.StatusOk(),
				}, nil).Times(1)
				md.bcClient.EXPECT().GetBankCustomer(gomock.Any(), &bcPb.GetBankCustomerRequest{
					Vendor: commonvgpb.Vendor_FEDERAL_BANK,
					Identifier: &bcPb.GetBankCustomerRequest_ActorId{
						ActorId: dummyActorId,
					},
				}).Return(&bcPb.GetBankCustomerResponse{
					Status: rpc.StatusOk(),
					BankCustomer: &bcPb.BankCustomer{
						DedupeInfo: &bcPb.DedupeInfo{
							KycLevel: kycPb.KYCLevel_MIN_KYC,
						},
					},
				}, nil).Times(1)
				md.userClient.EXPECT().GetUserDeviceProperties(gomock.Any(), &userPb.GetUserDevicePropertiesRequest{
					ActorId: dummyActorId,
					PropertyTypes: []types.DeviceProperty{
						types.DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN,
					},
				}).Return(&userPb.GetUserDevicePropertiesResponse{
					Status: rpc.StatusOk(),
					UserDevicePropertyList: []*userPb.UserDeviceProperty{
						{
							DeviceProperty: types.DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN,
							PropertyValue: &types.PropertyValue{
								PropValue: &types.PropertyValue_IpAddressToken{
									IpAddressToken: "ip-token-1",
								},
							},
						},
					},
				}, nil).Times(1)
				md.obfuscatorClient.EXPECT().GetPIIFromToken(gomock.Any(), &obfuscator.GetPIIFromTokenRequest{Token: "ip-token-1"}).
					Return(&obfuscator.GetPIIFromTokenResponse{
						Status: rpc.StatusInternal(),
					}, nil).Times(1)
				md.loanRequestDao.EXPECT().GetById(gomock.Any(), dummyRefId).Return(getMockLr(), nil).Times(1)
			},
		}}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newFederalActProcessorWithMocks(t)
			defer assertTest()
			tt.setupMocks(md)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(s)
			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.GetSetuAaConsentUrl, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("GetSetuAaConsentUrl() error = %v failed to fetch type value from convertible", err)
					return
				}
				// set the details as nil as are storing vendor request payload in this case
				result.GetLoanStep().Details = nil
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetSetuAaConsentUrl() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("GetSetuAaConsentUrl() error = %v assertion failed", err)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
			}

			if diff := cmp.Diff(tt.want, result, opts...); diff != "" {
				t.Errorf("GetSetuAaConsentUrl() Mismatch in response got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
		})
	}
}

func TestProcessor_GetSetuJourneyStatus(t *testing.T) {
	type args struct {
		req *palActivityPb.PalActivityWithCallbackRequest
	}
	t.Parallel()
	tests := []struct {
		name       string
		args       args
		want       *palActivityPb.PalActivityResponse
		wantErr    bool
		setupMocks func(md *mockedDependencies)
		assertErr  func(err error) bool
	}{
		{
			name: "#1 success case",
			args: args{
				req: &palActivityPb.PalActivityWithCallbackRequest{
					LoanStep:    getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					Vendor:      palPb.Vendor_FEDERAL,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
					Data:        nil,
				},
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep: getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
			},
			wantErr:   false,
			assertErr: epifitemporal.IsRetryableError,
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), dummyLseId).Return(
					getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					nil).Times(1)

				md.loanRequestDao.EXPECT().GetById(gomock.Any(), dummyRefId).Return(getMockLr(), nil).Times(1)
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), dummyActorId, []palPb.LoanProgram{getMockLr().GetLoanProgram()}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, time.Duration(0), true).Return([]*palPb.LoanOfferEligibilityCriteria{
					{
						Id:      "loec-id",
						ActorId: dummyActorId,
						Vendor:  palPb.Vendor_FEDERAL,
					},
				}, nil).Times(1)
				md.setuVgClient.EXPECT().GetJourneyStatus(gomock.Any(), &setuVgPb.GetJourneyStatusRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_SETU},
				}).Return(&setuVgPb.GetJourneyStatusResponse{
					Status:                rpc.StatusOk(),
					LoanDecisioningStatus: setuVgPb.LoanDecisioningStatus_LOAN_DECISIONING_STATUS_ELIGIBLE,
					OfferDetails: &setuVgPb.GetJourneyStatusResponse_OfferDetails{
						ApprovedLimit: &money.Money{
							CurrencyCode: "INR",
							Units:        200000,
						},
						Roi: 14,
						MaxEmiAmount: &money.Money{
							CurrencyCode: "INR",
							Units:        18000,
						},
					},
				}, nil).Times(1)
				md.txnExecutor.EXPECT().RunTxn(gomock.Any(), gomock.Any()).Return(nil).Times(1)
			},
		},
		{
			name: "#2 permanent failure via vendor",
			args: args{
				req: &palActivityPb.PalActivityWithCallbackRequest{
					LoanStep:    getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					Vendor:      palPb.Vendor_FEDERAL,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
					Data:        nil,
				},
			},
			want: &palActivityPb.PalActivityResponse{
				LoanStep:      getMockLseWithSubStatus(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH, palPb.LoanStepExecutionSubStatus_LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED),
				LseFieldMasks: []palPb.LoanStepExecutionFieldMask{palPb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_SUB_STATUS},
			},
			wantErr:   false,
			assertErr: epifitemporal.IsRetryableError,
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), dummyLseId).Return(
					getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					nil).Times(1)

				md.loanRequestDao.EXPECT().GetById(gomock.Any(), dummyRefId).Return(getMockLr(), nil).Times(1)
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), dummyActorId, []palPb.LoanProgram{getMockLr().GetLoanProgram()}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, time.Duration(0), true).Return([]*palPb.LoanOfferEligibilityCriteria{
					{
						Id:      "loec-id",
						ActorId: dummyActorId,
						Vendor:  palPb.Vendor_FEDERAL,
					},
				}, nil).Times(1)
				md.setuVgClient.EXPECT().GetJourneyStatus(gomock.Any(), &setuVgPb.GetJourneyStatusRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_SETU},
				}).Return(&setuVgPb.GetJourneyStatusResponse{
					Status:                rpc.StatusOk(),
					LoanDecisioningStatus: setuVgPb.LoanDecisioningStatus_LOAN_DECISIONING_STATUS_NOT_ELIGIBLE,
				}, nil).Times(1)
				md.loanRequestDao.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(1)
				md.loecDao.EXPECT().Update(gomock.Any(), &palPb.LoanOfferEligibilityCriteria{
					Id:        "loec-id",
					ActorId:   dummyActorId,
					Vendor:    palPb.Vendor_FEDERAL,
					SubStatus: palPb.LoanOfferEligibilityCriteriaSubStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_SUB_STATUS_REJECTED_BY_VENDOR,
					Status:    palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_REJECTED,
				}, []palPb.LoanOfferEligibilityCriteriaFieldMask{
					palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_STATUS,
					palPb.LoanOfferEligibilityCriteriaFieldMask_LOAN_OFFER_ELIGIBILITY_CRITERIA_FIELD_MASK_SUB_STATUS}).Return(nil).Times(1)
			},
		},
		{
			name: "#3 retryable failure via vendor",
			args: args{
				req: &palActivityPb.PalActivityWithCallbackRequest{
					LoanStep:    getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					Vendor:      palPb.Vendor_FEDERAL,
					LoanProgram: palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION,
					Data:        nil,
				},
			},
			want:      nil,
			wantErr:   true,
			assertErr: epifitemporal.IsRetryableError,
			setupMocks: func(md *mockedDependencies) {
				md.loanStepExecutionDao.EXPECT().GetById(gomock.Any(), dummyLseId).Return(
					getMockLse(dummyLseId, dummyActorId, palPb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_CREATED, dummyRefId, dummyOrchId, palPb.LoanStepExecutionStepName_LOAN_STEP_EXECUTION_STEP_NAME_AA_DATA_FETCH),
					nil).Times(1)

				md.loanRequestDao.EXPECT().GetById(gomock.Any(), dummyRefId).Return(getMockLr(), nil).Times(1)
				md.loecDao.EXPECT().GetActiveLoecsByActorIdLoanProgramsAndStatuses(gomock.Any(), dummyActorId, []palPb.LoanProgram{getMockLr().GetLoanProgram()}, []palPb.LoanOfferEligibilityCriteriaStatus{palPb.LoanOfferEligibilityCriteriaStatus_LOAN_OFFER_ELIGIBILITY_CRITERIA_STATUS_CREATED}, time.Duration(0), true).Return([]*palPb.LoanOfferEligibilityCriteria{
					{
						Id:      "loec-id",
						ActorId: dummyActorId,
						Vendor:  palPb.Vendor_FEDERAL,
					},
				}, nil).Times(1)
				md.setuVgClient.EXPECT().GetJourneyStatus(gomock.Any(), &setuVgPb.GetJourneyStatusRequest{
					Header: &commonvgpb.RequestHeader{Vendor: commonvgpb.Vendor_SETU},
				}).Return(&setuVgPb.GetJourneyStatusResponse{
					Status:                rpc.StatusOk(),
					LoanDecisioningStatus: setuVgPb.LoanDecisioningStatus_LOAN_DECISIONING_STATUS_CONSENT_PENDING,
				}, nil).Times(1)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, md, assertTest := newFederalActProcessorWithMocks(t)
			defer assertTest()
			tt.setupMocks(md)
			env := wts.NewTestActivityEnvironment()
			env.RegisterActivity(s)
			var result *palActivityPb.PalActivityResponse
			got, err := env.ExecuteActivity(palNs.GetSetuJourneyStatus, tt.args.req)
			if got != nil {
				getErr := got.Get(&result)
				if getErr != nil {
					t.Errorf("GetSetuJourneyStatus() error = %v failed to fetch type value from convertible", err)
					return
				}
			}
			switch {
			case (err != nil) != tt.wantErr:
				t.Errorf("GetSetuJourneyStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			case tt.wantErr && !tt.assertErr(err):
				t.Errorf("GetSetuJourneyStatus() error = %v assertion failed", err)
				return
			}

			opts := []cmp.Option{
				protocmp.Transform(),
			}

			if diff := cmp.Diff(tt.want, result, opts...); diff != "" {
				t.Errorf("GetSetuJourneyStatus() Mismatch in response got = %v,\n want = %v \n diff = %v ", got, tt.want, diff)
			}
		})
	}
}
