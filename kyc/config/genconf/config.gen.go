// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"context"
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	pkgweb "github.com/epifi/be-common/api/pkg/web"
	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	genapp "github.com/epifi/be-common/pkg/frontend/app/genconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	genconfig2 "github.com/epifi/be-common/quest/sdk/config/genconf"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/kyc/config"
	genconfig "github.com/epifi/gamma/pkg/feature/release/config/genconf"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_FederalVKYCUpdateSubscriber                  *gencfg.SqsSubscriber
	_RefreshCallStatusSubscriber                  *gencfg.SqsSubscriber
	_KarzaVKYCCallEventSubscriber                 *gencfg.SqsSubscriber
	_KarzaVKYCAgentCallbackSubscriber             *gencfg.SqsSubscriber
	_KarzaVKYCAuditorCallbackSubscriber           *gencfg.SqsSubscriber
	_VKYCAgentUpdateDelaySqsSubscriber            *gencfg.SqsSubscriber
	_VKYCUserCommsSubscriber                      *gencfg.SqsSubscriber
	_QueueCKYCSearchSubscriber                    *gencfg.SqsSubscriber
	_QueueCKYCDownloadSubscriber                  *gencfg.SqsSubscriber
	_QueueAccountStateUpdateSubscriber            *gencfg.SqsSubscriber
	_Flags                                        *Flags
	_VKYC                                         *VKYC
	_EKYCDowntime                                 *EKYCDowntime
	_ProcessVKYCOnboardingCompleteEventSubscriber *gencfg.SqsSubscriber
	_ProcessVKYCTransactionEventSubscriber        *gencfg.SqsSubscriber
	_QuestSdk                                     *genconfig2.Config
	_UqudoPassportTamperingConfig                 *UqudoPassportTampering
	_FeatureReleaseConfig                         *genconfig.FeatureReleaseConfig
	_Application                                  *config.Application
	_Server                                       *config.Server
	_Logging                                      *cfg.Logging
	_EpifiDb                                      *cfg.DB
	_VkycAgentUpdatePublisher                     *cfg.SqsPublisher
	_RefreshCallStatusPublisher                   *cfg.SqsPublisher
	_RefreshCallStatusDelay                       time.Duration
	_VKYCUserCommsDelayPublisher                  *cfg.SqsPublisher
	_VKYCUserCommsPublisher                       *cfg.SqsPublisher
	_VkycCallCompletedEventPublisher              *cfg.SnsPublisher
	_QueueCKYCSearchPublisher                     *cfg.SqsPublisher
	_QueueCKYCDownloadPublisher                   *cfg.SqsPublisher
	_QueueVKYCUpdatePublisher                     *cfg.SqsPublisher
	_EKYCSuccessPublisher                         *cfg.SqsPublisher
	_BKYCUpdateEventPublisher                     *cfg.SnsPublisher
	_Delay                                        *config.Delay
	_Aws                                          *config.Aws
	_Secrets                                      *cfg.Secrets
	_Values                                       *config.Values
	_RudderStack                                  *config.RudderBroker
	_SecureLogging                                *config.Logging
	_VKYCNotification                             *config.VKYCNotification
	_Sentry                                       *cfg.Sentry
	_RateLimit                                    *config.RateLimit
	_RedisOptions                                 *cfg.RedisOptions
	_UserRedisOptions                             *cfg.RedisOptions
	_PANValidationRateLimitUpdatePublisher        *cfg.SqsPublisher
	_Tracing                                      *cfg.Tracing
	_Profiling                                    *cfg.Profiling
	_QuestRedisOptions                            *cfg.RedisOptions
	_KycDb                                        *cfg.DB
	_ExtractedDocumentsExpiryConfig               *config.ExtractedDocumentsExpiryConfig
	_NrBucketName                                 string
}

func (obj *Config) FederalVKYCUpdateSubscriber() *gencfg.SqsSubscriber {
	return obj._FederalVKYCUpdateSubscriber
}
func (obj *Config) RefreshCallStatusSubscriber() *gencfg.SqsSubscriber {
	return obj._RefreshCallStatusSubscriber
}
func (obj *Config) KarzaVKYCCallEventSubscriber() *gencfg.SqsSubscriber {
	return obj._KarzaVKYCCallEventSubscriber
}
func (obj *Config) KarzaVKYCAgentCallbackSubscriber() *gencfg.SqsSubscriber {
	return obj._KarzaVKYCAgentCallbackSubscriber
}
func (obj *Config) KarzaVKYCAuditorCallbackSubscriber() *gencfg.SqsSubscriber {
	return obj._KarzaVKYCAuditorCallbackSubscriber
}
func (obj *Config) VKYCAgentUpdateDelaySqsSubscriber() *gencfg.SqsSubscriber {
	return obj._VKYCAgentUpdateDelaySqsSubscriber
}
func (obj *Config) VKYCUserCommsSubscriber() *gencfg.SqsSubscriber {
	return obj._VKYCUserCommsSubscriber
}
func (obj *Config) QueueCKYCSearchSubscriber() *gencfg.SqsSubscriber {
	return obj._QueueCKYCSearchSubscriber
}
func (obj *Config) QueueCKYCDownloadSubscriber() *gencfg.SqsSubscriber {
	return obj._QueueCKYCDownloadSubscriber
}
func (obj *Config) QueueAccountStateUpdateSubscriber() *gencfg.SqsSubscriber {
	return obj._QueueAccountStateUpdateSubscriber
}
func (obj *Config) Flags() *Flags {
	return obj._Flags
}
func (obj *Config) VKYC() *VKYC {
	return obj._VKYC
}
func (obj *Config) EKYCDowntime() *EKYCDowntime {
	return obj._EKYCDowntime
}
func (obj *Config) ProcessVKYCOnboardingCompleteEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessVKYCOnboardingCompleteEventSubscriber
}
func (obj *Config) ProcessVKYCTransactionEventSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessVKYCTransactionEventSubscriber
}
func (obj *Config) QuestSdk() *genconfig2.Config {
	return obj._QuestSdk
}
func (obj *Config) UqudoPassportTamperingConfig() *UqudoPassportTampering {
	return obj._UqudoPassportTamperingConfig
}
func (obj *Config) FeatureReleaseConfig() *genconfig.FeatureReleaseConfig {
	return obj._FeatureReleaseConfig
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) EpifiDb() *cfg.DB {
	return obj._EpifiDb
}
func (obj *Config) VkycAgentUpdatePublisher() *cfg.SqsPublisher {
	return obj._VkycAgentUpdatePublisher
}
func (obj *Config) RefreshCallStatusPublisher() *cfg.SqsPublisher {
	return obj._RefreshCallStatusPublisher
}
func (obj *Config) RefreshCallStatusDelay() time.Duration {
	return obj._RefreshCallStatusDelay
}
func (obj *Config) VKYCUserCommsDelayPublisher() *cfg.SqsPublisher {
	return obj._VKYCUserCommsDelayPublisher
}
func (obj *Config) VKYCUserCommsPublisher() *cfg.SqsPublisher {
	return obj._VKYCUserCommsPublisher
}
func (obj *Config) VkycCallCompletedEventPublisher() *cfg.SnsPublisher {
	return obj._VkycCallCompletedEventPublisher
}
func (obj *Config) QueueCKYCSearchPublisher() *cfg.SqsPublisher {
	return obj._QueueCKYCSearchPublisher
}
func (obj *Config) QueueCKYCDownloadPublisher() *cfg.SqsPublisher {
	return obj._QueueCKYCDownloadPublisher
}
func (obj *Config) QueueVKYCUpdatePublisher() *cfg.SqsPublisher {
	return obj._QueueVKYCUpdatePublisher
}
func (obj *Config) EKYCSuccessPublisher() *cfg.SqsPublisher {
	return obj._EKYCSuccessPublisher
}
func (obj *Config) BKYCUpdateEventPublisher() *cfg.SnsPublisher {
	return obj._BKYCUpdateEventPublisher
}
func (obj *Config) Delay() *config.Delay {
	return obj._Delay
}
func (obj *Config) Aws() *config.Aws {
	return obj._Aws
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) Values() *config.Values {
	return obj._Values
}
func (obj *Config) RudderStack() *config.RudderBroker {
	return obj._RudderStack
}
func (obj *Config) SecureLogging() *config.Logging {
	return obj._SecureLogging
}
func (obj *Config) VKYCNotification() *config.VKYCNotification {
	return obj._VKYCNotification
}
func (obj *Config) Sentry() *cfg.Sentry {
	return obj._Sentry
}
func (obj *Config) RateLimit() *config.RateLimit {
	return obj._RateLimit
}
func (obj *Config) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}
func (obj *Config) UserRedisOptions() *cfg.RedisOptions {
	return obj._UserRedisOptions
}
func (obj *Config) PANValidationRateLimitUpdatePublisher() *cfg.SqsPublisher {
	return obj._PANValidationRateLimitUpdatePublisher
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) QuestRedisOptions() *cfg.RedisOptions {
	return obj._QuestRedisOptions
}
func (obj *Config) KycDb() *cfg.DB {
	return obj._KycDb
}
func (obj *Config) ExtractedDocumentsExpiryConfig() *config.ExtractedDocumentsExpiryConfig {
	return obj._ExtractedDocumentsExpiryConfig
}
func (obj *Config) NrBucketName() string {
	return obj._NrBucketName
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// EnableCKYCDataValidation controls if we want to validate
	// customer name and phone number in CKYC Download response
	_EnableCKYCDataValidation uint32
	// SkipCKYCDownload ends CKYC process after search and skips CKYC data download.
	_SkipCKYCDownload uint32
	// This flag controls how the passport back image is sent to Karza for OCR
	// By default, the image is sent as a PDF base64.
	// When set to true, the image will be sent in its original format (e.g., JPEG/PNG)
	_SendPassportBackAsImageToKarza uint32
	_TrimDebugMessageFromStatus     bool
}

// EnableCKYCDataValidation controls if we want to validate
// customer name and phone number in CKYC Download response
func (obj *Flags) EnableCKYCDataValidation() bool {
	if atomic.LoadUint32(&obj._EnableCKYCDataValidation) == 0 {
		return false
	} else {
		return true
	}
}

// SkipCKYCDownload ends CKYC process after search and skips CKYC data download.
func (obj *Flags) SkipCKYCDownload() bool {
	if atomic.LoadUint32(&obj._SkipCKYCDownload) == 0 {
		return false
	} else {
		return true
	}
}

// This flag controls how the passport back image is sent to Karza for OCR
// By default, the image is sent as a PDF base64.
// When set to true, the image will be sent in its original format (e.g., JPEG/PNG)
func (obj *Flags) SendPassportBackAsImageToKarza() bool {
	if atomic.LoadUint32(&obj._SendPassportBackAsImageToKarza) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	return obj._TrimDebugMessageFromStatus
}

type VKYC struct {
	callbacks               *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk                questsdk.Client
	questFieldPath          string
	_ReAttemptVkycStartHour int64
	_ReAttemptVkycEndHour   int64
	_VkycStartHour          int64
	// end hour is exclusive
	_VkycEndHour int64
	// max priority list size
	_MaxVKYCPriorityListSize int64
	// instruction page skip option timer in seconds
	_InstructionPageSkipOptionTime int32
	// landing page skip option timer in seconds
	_LandingPageSkipOptionTime int32
	_MaxRetryForAgentRejected  int64
	// reattempt vkyc roll out percentage
	_ReAttemptVKYCRolloutPercentage int64
	// NRVKYCStartHour specifies the start hour(24 hr format) in IST for NR VKYC agent availability.
	// If a user attempts calls before this hour, they should see an out of business hours message.
	_NRVKYCStartHour int64
	// NRVKYCEndHour specifies the end hour(24 hr format) in IST for NR VKYC agent availability.
	// If a user attempts calls at or after this hour, they should see an out of business hours message.
	_NRVKYCEndHour int64
	// LocationMatchThresholdInKM is maximum allowed distance (in kilometers) between the entered communication
	// address and the user's live location (latitude/longitude) for a successful match.
	_LocationMatchThresholdInKM         int64
	_EnableReAttemptVKYCNudge           uint32
	_EnableReAttemptVKYCHomeBottomSheet uint32
	_EnableVKYCPriortyFlow              uint32
	// enable call scheduling for VKYC V2 flow (https://monorail.pointz.in/p/fi-app/issues/detail?id=90797)
	_EnableCallScheduleForVKYCV2Flow uint32
	_EnableUploadEpanFile            uint32
	_EnablePanCapturePreOnboarding   uint32
	// Feature to enable VKYC benefit screen with comparison module
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=90797
	_EnableVKYCBenefitScreenV2  uint32
	_VKYCPriorityListElementTTL int64
	_RetryEPANDuration          int64
	// ExpectedUserWaitingTimeInNRVKYC is the expected waiting time for the user to get connected to an agent
	_ExpectedUserWaitingTimeInNRVKYC int64
	// contains time interval (start timestamp-end timestamp inclusive)YYYY-MM-DD HH:MM:SS/YYYY-MM-DD HH:MM:SS format and value as % users allowed
	_VKYCUserAllowedDateConstraint *syncmap.Map[string, int]
	// contains time interval in format HH:MM-HH:MM(start time-end time inclusive) and value as % users allowed
	_VKYCUserAllowedDailyTimeIntervalConstraint *syncmap.Map[string, int]
	_UnsupportedWebviewDeviceModels             roarray.ROArray[string]
	_UnsupportedWebviewDeviceModelsMutex        *sync.RWMutex
	_UnsupportedCustomerDeviceModels            roarray.ROArray[string]
	_UnsupportedCustomerDeviceModelsMutex       *sync.RWMutex
	_FederalSFTPEPANUploadPath                  string
	_FederalSFTPEPANUploadPathMutex             *sync.RWMutex
	_FederalSFTPEPANUploadPrefix                string
	_FederalSFTPEPANUploadPrefixMutex           *sync.RWMutex
	_VKYCFaqCategoryId                          string
	_VKYCFaqCategoryIdMutex                     *sync.RWMutex
	_VKYCPriorityUsersList                      *VKYCPriorityUsersList
	_AgentWaitingOverlay                        *AgentWaitingOverlay
	_EnableVKYCScheduleFlow                     *genapp.FeatureConfig
	_EnableVKYCDemandManagement                 *genapp.FeatureConfig
	_ABFeatureReleaseConfig                     *genconfig.ABFeatureReleaseConfig
	_EnableConfirmDetailsState                  *genapp.FeatureConfig
	_EnableNRConfirmDetailsState                *genapp.FeatureConfig
	_EnableEpanInstructionScreen                *genapp.FeatureConfig
	_EnableCallQualityCheckStage                *genapp.FeatureConfig
	_VKYCCallQuality                            *VKYCCallQuality
	_EnableApprovedNudgeDismissal               *genapp.FeatureConfig
	_EnableEPANInNRFlow                         *genapp.FeatureConfig
	_EnableVKYCFlowV2                           *genapp.FeatureConfig
	_EnableVKYCFlowV2ForHome                    *genapp.FeatureConfig
	_EnableCallInitiation                       *CallInitiation
	_PerformEkycAfter                           time.Duration
}

func (obj *VKYC) ReAttemptVkycStartHour() int {
	return int(atomic.LoadInt64(&obj._ReAttemptVkycStartHour))
}
func (obj *VKYC) ReAttemptVkycEndHour() int {
	return int(atomic.LoadInt64(&obj._ReAttemptVkycEndHour))
}
func (obj *VKYC) VkycStartHour() int {
	return int(atomic.LoadInt64(&obj._VkycStartHour))
}

// end hour is exclusive
func (obj *VKYC) VkycEndHour() int {
	return int(atomic.LoadInt64(&obj._VkycEndHour))
}

// max priority list size
func (obj *VKYC) MaxVKYCPriorityListSize() int {
	return int(atomic.LoadInt64(&obj._MaxVKYCPriorityListSize))
}

// instruction page skip option timer in seconds
func (obj *VKYC) InstructionPageSkipOptionTime() int32 {
	return int32(atomic.LoadInt32(&obj._InstructionPageSkipOptionTime))
}

// landing page skip option timer in seconds
func (obj *VKYC) LandingPageSkipOptionTime() int32 {
	return int32(atomic.LoadInt32(&obj._LandingPageSkipOptionTime))
}
func (obj *VKYC) MaxRetryForAgentRejected() int {
	return int(atomic.LoadInt64(&obj._MaxRetryForAgentRejected))
}

// reattempt vkyc roll out percentage
func (obj *VKYC) ReAttemptVKYCRolloutPercentage() int {
	return int(atomic.LoadInt64(&obj._ReAttemptVKYCRolloutPercentage))
}

// NRVKYCStartHour specifies the start hour(24 hr format) in IST for NR VKYC agent availability.
// If a user attempts calls before this hour, they should see an out of business hours message.
func (obj *VKYC) NRVKYCStartHour() int {
	return int(atomic.LoadInt64(&obj._NRVKYCStartHour))
}

// NRVKYCEndHour specifies the end hour(24 hr format) in IST for NR VKYC agent availability.
// If a user attempts calls at or after this hour, they should see an out of business hours message.
func (obj *VKYC) NRVKYCEndHour() int {
	return int(atomic.LoadInt64(&obj._NRVKYCEndHour))
}

// LocationMatchThresholdInKM is maximum allowed distance (in kilometers) between the entered communication
// address and the user's live location (latitude/longitude) for a successful match.
func (obj *VKYC) LocationMatchThresholdInKM() int {
	return int(atomic.LoadInt64(&obj._LocationMatchThresholdInKM))
}
func (obj *VKYC) EnableReAttemptVKYCNudge() bool {
	if atomic.LoadUint32(&obj._EnableReAttemptVKYCNudge) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *VKYC) EnableReAttemptVKYCHomeBottomSheet() bool {
	if atomic.LoadUint32(&obj._EnableReAttemptVKYCHomeBottomSheet) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *VKYC) EnableVKYCPriortyFlow() bool {
	if atomic.LoadUint32(&obj._EnableVKYCPriortyFlow) == 0 {
		return false
	} else {
		return true
	}
}

// enable call scheduling for VKYC V2 flow (https://monorail.pointz.in/p/fi-app/issues/detail?id=90797)
func (obj *VKYC) EnableCallScheduleForVKYCV2Flow() bool {
	if atomic.LoadUint32(&obj._EnableCallScheduleForVKYCV2Flow) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *VKYC) EnableUploadEpanFile() bool {
	if atomic.LoadUint32(&obj._EnableUploadEpanFile) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *VKYC) EnablePanCapturePreOnboarding() bool {
	if atomic.LoadUint32(&obj._EnablePanCapturePreOnboarding) == 0 {
		return false
	} else {
		return true
	}
}

// Feature to enable VKYC benefit screen with comparison module
// https://monorail.pointz.in/p/fi-app/issues/detail?id=90797
func (obj *VKYC) enableVKYCBenefitScreenV2() bool {
	if atomic.LoadUint32(&obj._EnableVKYCBenefitScreenV2) == 0 {
		return false
	} else {
		return true
	}
}

// Feature to enable VKYC benefit screen with comparison module
// https://monorail.pointz.in/p/fi-app/issues/detail?id=90797
func (obj *VKYC) EnableVKYCBenefitScreenV2(ctx context.Context) bool {
	defVal := obj.enableVKYCBenefitScreenV2()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "EnableVKYCBenefitScreenV2"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

func (obj *VKYC) VKYCPriorityListElementTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._VKYCPriorityListElementTTL))
}
func (obj *VKYC) RetryEPANDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._RetryEPANDuration))
}

// ExpectedUserWaitingTimeInNRVKYC is the expected waiting time for the user to get connected to an agent
func (obj *VKYC) ExpectedUserWaitingTimeInNRVKYC() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ExpectedUserWaitingTimeInNRVKYC))
}

// contains time interval (start timestamp-end timestamp inclusive)YYYY-MM-DD HH:MM:SS/YYYY-MM-DD HH:MM:SS format and value as % users allowed
func (obj *VKYC) VKYCUserAllowedDateConstraint() *syncmap.Map[string, int] {
	return obj._VKYCUserAllowedDateConstraint
}

// contains time interval in format HH:MM-HH:MM(start time-end time inclusive) and value as % users allowed
func (obj *VKYC) VKYCUserAllowedDailyTimeIntervalConstraint() *syncmap.Map[string, int] {
	return obj._VKYCUserAllowedDailyTimeIntervalConstraint
}
func (obj *VKYC) UnsupportedWebviewDeviceModels() roarray.ROArray[string] {
	obj._UnsupportedWebviewDeviceModelsMutex.RLock()
	defer obj._UnsupportedWebviewDeviceModelsMutex.RUnlock()
	return obj._UnsupportedWebviewDeviceModels
}
func (obj *VKYC) UnsupportedCustomerDeviceModels() roarray.ROArray[string] {
	obj._UnsupportedCustomerDeviceModelsMutex.RLock()
	defer obj._UnsupportedCustomerDeviceModelsMutex.RUnlock()
	return obj._UnsupportedCustomerDeviceModels
}
func (obj *VKYC) FederalSFTPEPANUploadPath() string {
	obj._FederalSFTPEPANUploadPathMutex.RLock()
	defer obj._FederalSFTPEPANUploadPathMutex.RUnlock()
	return obj._FederalSFTPEPANUploadPath
}
func (obj *VKYC) FederalSFTPEPANUploadPrefix() string {
	obj._FederalSFTPEPANUploadPrefixMutex.RLock()
	defer obj._FederalSFTPEPANUploadPrefixMutex.RUnlock()
	return obj._FederalSFTPEPANUploadPrefix
}
func (obj *VKYC) VKYCFaqCategoryId() string {
	obj._VKYCFaqCategoryIdMutex.RLock()
	defer obj._VKYCFaqCategoryIdMutex.RUnlock()
	return obj._VKYCFaqCategoryId
}
func (obj *VKYC) VKYCPriorityUsersList() *VKYCPriorityUsersList {
	return obj._VKYCPriorityUsersList
}
func (obj *VKYC) AgentWaitingOverlay() *AgentWaitingOverlay {
	return obj._AgentWaitingOverlay
}
func (obj *VKYC) EnableVKYCScheduleFlow() *genapp.FeatureConfig {
	return obj._EnableVKYCScheduleFlow
}
func (obj *VKYC) EnableVKYCDemandManagement() *genapp.FeatureConfig {
	return obj._EnableVKYCDemandManagement
}
func (obj *VKYC) ABFeatureReleaseConfig() *genconfig.ABFeatureReleaseConfig {
	return obj._ABFeatureReleaseConfig
}
func (obj *VKYC) EnableConfirmDetailsState() *genapp.FeatureConfig {
	return obj._EnableConfirmDetailsState
}
func (obj *VKYC) EnableNRConfirmDetailsState() *genapp.FeatureConfig {
	return obj._EnableNRConfirmDetailsState
}
func (obj *VKYC) EnableEpanInstructionScreen() *genapp.FeatureConfig {
	return obj._EnableEpanInstructionScreen
}
func (obj *VKYC) EnableCallQualityCheckStage() *genapp.FeatureConfig {
	return obj._EnableCallQualityCheckStage
}
func (obj *VKYC) VKYCCallQuality() *VKYCCallQuality {
	return obj._VKYCCallQuality
}
func (obj *VKYC) EnableApprovedNudgeDismissal() *genapp.FeatureConfig {
	return obj._EnableApprovedNudgeDismissal
}
func (obj *VKYC) EnableEPANInNRFlow() *genapp.FeatureConfig {
	return obj._EnableEPANInNRFlow
}
func (obj *VKYC) EnableVKYCFlowV2() *genapp.FeatureConfig {
	return obj._EnableVKYCFlowV2
}
func (obj *VKYC) EnableVKYCFlowV2ForHome() *genapp.FeatureConfig {
	return obj._EnableVKYCFlowV2ForHome
}
func (obj *VKYC) EnableCallInitiation() *CallInitiation {
	return obj._EnableCallInitiation
}
func (obj *VKYC) PerformEkycAfter() time.Duration {
	return obj._PerformEkycAfter
}

type VKYCPriorityUsersList struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// to enable priority queue for O type user
	_OTypeUser uint32
	// to enable priority queue for salaried user
	_SalariedUser uint32
	// to enable priority queue for user in their last N days
	_LastNDaysUser uint32
}

// to enable priority queue for O type user
func (obj *VKYCPriorityUsersList) OTypeUser() bool {
	if atomic.LoadUint32(&obj._OTypeUser) == 0 {
		return false
	} else {
		return true
	}
}

// to enable priority queue for salaried user
func (obj *VKYCPriorityUsersList) SalariedUser() bool {
	if atomic.LoadUint32(&obj._SalariedUser) == 0 {
		return false
	} else {
		return true
	}
}

// to enable priority queue for user in their last N days
func (obj *VKYCPriorityUsersList) LastNDaysUser() bool {
	if atomic.LoadUint32(&obj._LastNDaysUser) == 0 {
		return false
	} else {
		return true
	}
}

type AgentWaitingOverlay struct {
	callbacks      *syncmap.Map[string, helper.FieldChangeCallback]
	questSdk       questsdk.Client
	questFieldPath string
	_Disable       uint32
}

func (obj *AgentWaitingOverlay) disable() bool {
	if atomic.LoadUint32(&obj._Disable) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AgentWaitingOverlay) Disable(ctx context.Context) bool {
	defVal := obj.disable()
	if obj.questSdk == nil {
		return defVal
	}
	res := obj.questSdk.EvaluateWithDefaultValue(ctx, &questtypes.Variable{Path: obj.questFieldPath + "/" + "Disable"}, defVal)
	val, ok := res.(bool)
	if ok {
		return val
	}
	return defVal
}

type VKYCCallQuality struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_NoiseLevelThresholdAndroid     int64
	_ImageLuminanceThresholdAndroid int64
	_NoiseLevelThresholdIOS         int64
	_ImageLuminanceThresholdIOS     int64
	_CheckDurationInMillis          int64
	_SampleDurationInMillis         int64
}

func (obj *VKYCCallQuality) NoiseLevelThresholdAndroid() int {
	return int(atomic.LoadInt64(&obj._NoiseLevelThresholdAndroid))
}
func (obj *VKYCCallQuality) ImageLuminanceThresholdAndroid() int {
	return int(atomic.LoadInt64(&obj._ImageLuminanceThresholdAndroid))
}
func (obj *VKYCCallQuality) NoiseLevelThresholdIOS() int {
	return int(atomic.LoadInt64(&obj._NoiseLevelThresholdIOS))
}
func (obj *VKYCCallQuality) ImageLuminanceThresholdIOS() int {
	return int(atomic.LoadInt64(&obj._ImageLuminanceThresholdIOS))
}
func (obj *VKYCCallQuality) CheckDurationInMillis() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CheckDurationInMillis))
}
func (obj *VKYCCallQuality) SampleDurationInMillis() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._SampleDurationInMillis))
}

type CallInitiation struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_RolloutPercentage int64
	_Enable            *genapp.FeatureConfig
}

func (obj *CallInitiation) RolloutPercentage() int {
	return int(atomic.LoadInt64(&obj._RolloutPercentage))
}
func (obj *CallInitiation) Enable() *genapp.FeatureConfig {
	return obj._Enable
}

type EKYCDowntime struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// time ranges for ekyc downtime
	// time format "2006-01-02 15:04:05.000" (time zone IST)
	_EKYCDowntimeFrom         string
	_EKYCDowntimeFromMutex    *sync.RWMutex
	_EKYCDowntimeTo           string
	_EKYCDowntimeToMutex      *sync.RWMutex
	_EKYCDowntimeMessage      string
	_EKYCDowntimeMessageMutex *sync.RWMutex
}

// time ranges for ekyc downtime
// time format "2006-01-02 15:04:05.000" (time zone IST)
func (obj *EKYCDowntime) EKYCDowntimeFrom() string {
	obj._EKYCDowntimeFromMutex.RLock()
	defer obj._EKYCDowntimeFromMutex.RUnlock()
	return obj._EKYCDowntimeFrom
}
func (obj *EKYCDowntime) EKYCDowntimeTo() string {
	obj._EKYCDowntimeToMutex.RLock()
	defer obj._EKYCDowntimeToMutex.RUnlock()
	return obj._EKYCDowntimeTo
}
func (obj *EKYCDowntime) EKYCDowntimeMessage() string {
	obj._EKYCDowntimeMessageMutex.RLock()
	defer obj._EKYCDowntimeMessageMutex.RUnlock()
	return obj._EKYCDowntimeMessage
}

type UqudoPassportTampering struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ScreenDetectionThreshold int64
	_PrintDetectionThreshold  int64
	_PhotoTamperingThreshold  int64
	_EnableTamperCheck        uint32
}

func (obj *UqudoPassportTampering) ScreenDetectionThreshold() int {
	return int(atomic.LoadInt64(&obj._ScreenDetectionThreshold))
}
func (obj *UqudoPassportTampering) PrintDetectionThreshold() int {
	return int(atomic.LoadInt64(&obj._PrintDetectionThreshold))
}
func (obj *UqudoPassportTampering) PhotoTamperingThreshold() int {
	return int(atomic.LoadInt64(&obj._PhotoTamperingThreshold))
}
func (obj *UqudoPassportTampering) EnableTamperCheck() bool {
	if atomic.LoadUint32(&obj._EnableTamperCheck) == 0 {
		return false
	} else {
		return true
	}
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_FederalVKYCUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FederalVKYCUpdateSubscriber = _FederalVKYCUpdateSubscriber
	helper.AddFieldSetters("federalvkycupdatesubscriber", _fieldSetters, _setters)
	_RefreshCallStatusSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RefreshCallStatusSubscriber = _RefreshCallStatusSubscriber
	helper.AddFieldSetters("refreshcallstatussubscriber", _fieldSetters, _setters)
	_KarzaVKYCCallEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._KarzaVKYCCallEventSubscriber = _KarzaVKYCCallEventSubscriber
	helper.AddFieldSetters("karzavkyccalleventsubscriber", _fieldSetters, _setters)
	_KarzaVKYCAgentCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._KarzaVKYCAgentCallbackSubscriber = _KarzaVKYCAgentCallbackSubscriber
	helper.AddFieldSetters("karzavkycagentcallbacksubscriber", _fieldSetters, _setters)
	_KarzaVKYCAuditorCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._KarzaVKYCAuditorCallbackSubscriber = _KarzaVKYCAuditorCallbackSubscriber
	helper.AddFieldSetters("karzavkycauditorcallbacksubscriber", _fieldSetters, _setters)
	_VKYCAgentUpdateDelaySqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._VKYCAgentUpdateDelaySqsSubscriber = _VKYCAgentUpdateDelaySqsSubscriber
	helper.AddFieldSetters("vkycagentupdatedelaysqssubscriber", _fieldSetters, _setters)
	_VKYCUserCommsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._VKYCUserCommsSubscriber = _VKYCUserCommsSubscriber
	helper.AddFieldSetters("vkycusercommssubscriber", _fieldSetters, _setters)
	_QueueCKYCSearchSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._QueueCKYCSearchSubscriber = _QueueCKYCSearchSubscriber
	helper.AddFieldSetters("queueckycsearchsubscriber", _fieldSetters, _setters)
	_QueueCKYCDownloadSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._QueueCKYCDownloadSubscriber = _QueueCKYCDownloadSubscriber
	helper.AddFieldSetters("queueckycdownloadsubscriber", _fieldSetters, _setters)
	_QueueAccountStateUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._QueueAccountStateUpdateSubscriber = _QueueAccountStateUpdateSubscriber
	helper.AddFieldSetters("queueaccountstateupdatesubscriber", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_VKYC, _fieldSetters := NewVKYC()
	_obj._VKYC = _VKYC
	helper.AddFieldSetters("vkyc", _fieldSetters, _setters)
	_EKYCDowntime, _fieldSetters := NewEKYCDowntime()
	_obj._EKYCDowntime = _EKYCDowntime
	helper.AddFieldSetters("ekycdowntime", _fieldSetters, _setters)
	_ProcessVKYCOnboardingCompleteEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessVKYCOnboardingCompleteEventSubscriber = _ProcessVKYCOnboardingCompleteEventSubscriber
	helper.AddFieldSetters("processvkyconboardingcompleteeventsubscriber", _fieldSetters, _setters)
	_ProcessVKYCTransactionEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessVKYCTransactionEventSubscriber = _ProcessVKYCTransactionEventSubscriber
	helper.AddFieldSetters("processvkyctransactioneventsubscriber", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig2.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_UqudoPassportTamperingConfig, _fieldSetters := NewUqudoPassportTampering()
	_obj._UqudoPassportTamperingConfig = _UqudoPassportTamperingConfig
	helper.AddFieldSetters("uqudopassporttamperingconfig", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_FederalVKYCUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._FederalVKYCUpdateSubscriber = _FederalVKYCUpdateSubscriber
	helper.AddFieldSetters("federalvkycupdatesubscriber", _fieldSetters, _setters)
	_RefreshCallStatusSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RefreshCallStatusSubscriber = _RefreshCallStatusSubscriber
	helper.AddFieldSetters("refreshcallstatussubscriber", _fieldSetters, _setters)
	_KarzaVKYCCallEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._KarzaVKYCCallEventSubscriber = _KarzaVKYCCallEventSubscriber
	helper.AddFieldSetters("karzavkyccalleventsubscriber", _fieldSetters, _setters)
	_KarzaVKYCAgentCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._KarzaVKYCAgentCallbackSubscriber = _KarzaVKYCAgentCallbackSubscriber
	helper.AddFieldSetters("karzavkycagentcallbacksubscriber", _fieldSetters, _setters)
	_KarzaVKYCAuditorCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._KarzaVKYCAuditorCallbackSubscriber = _KarzaVKYCAuditorCallbackSubscriber
	helper.AddFieldSetters("karzavkycauditorcallbacksubscriber", _fieldSetters, _setters)
	_VKYCAgentUpdateDelaySqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._VKYCAgentUpdateDelaySqsSubscriber = _VKYCAgentUpdateDelaySqsSubscriber
	helper.AddFieldSetters("vkycagentupdatedelaysqssubscriber", _fieldSetters, _setters)
	_VKYCUserCommsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._VKYCUserCommsSubscriber = _VKYCUserCommsSubscriber
	helper.AddFieldSetters("vkycusercommssubscriber", _fieldSetters, _setters)
	_QueueCKYCSearchSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._QueueCKYCSearchSubscriber = _QueueCKYCSearchSubscriber
	helper.AddFieldSetters("queueckycsearchsubscriber", _fieldSetters, _setters)
	_QueueCKYCDownloadSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._QueueCKYCDownloadSubscriber = _QueueCKYCDownloadSubscriber
	helper.AddFieldSetters("queueckycdownloadsubscriber", _fieldSetters, _setters)
	_QueueAccountStateUpdateSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._QueueAccountStateUpdateSubscriber = _QueueAccountStateUpdateSubscriber
	helper.AddFieldSetters("queueaccountstateupdatesubscriber", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_VKYC, _fieldSetters := NewVKYCWithQuest(questFieldPath + "/" + "VKYC")
	_obj._VKYC = _VKYC
	helper.AddFieldSetters("vkyc", _fieldSetters, _setters)
	_EKYCDowntime, _fieldSetters := NewEKYCDowntime()
	_obj._EKYCDowntime = _EKYCDowntime
	helper.AddFieldSetters("ekycdowntime", _fieldSetters, _setters)
	_ProcessVKYCOnboardingCompleteEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessVKYCOnboardingCompleteEventSubscriber = _ProcessVKYCOnboardingCompleteEventSubscriber
	helper.AddFieldSetters("processvkyconboardingcompleteeventsubscriber", _fieldSetters, _setters)
	_ProcessVKYCTransactionEventSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessVKYCTransactionEventSubscriber = _ProcessVKYCTransactionEventSubscriber
	helper.AddFieldSetters("processvkyctransactioneventsubscriber", _fieldSetters, _setters)
	_QuestSdk, _fieldSetters := genconfig2.NewConfig()
	_obj._QuestSdk = _QuestSdk
	helper.AddFieldSetters("questsdk", _fieldSetters, _setters)
	_UqudoPassportTamperingConfig, _fieldSetters := NewUqudoPassportTampering()
	_obj._UqudoPassportTamperingConfig = _UqudoPassportTamperingConfig
	helper.AddFieldSetters("uqudopassporttamperingconfig", _fieldSetters, _setters)
	_FeatureReleaseConfig, _fieldSetters := genconfig.NewFeatureReleaseConfig()
	_obj._FeatureReleaseConfig = _FeatureReleaseConfig
	helper.AddFieldSetters("featurereleaseconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init(questFieldPath string) {
	newObj, _ := NewConfig()
	*obj = *newObj
}
func (obj *Config) InitWithQuest(questFieldPath string) {
	newObj, _ := NewConfigWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
	obj._VKYC.SetQuestSDK(questSdk)
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	childVars, childVarsErr = obj._VKYC.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}

	for _, v := range childVars {
		v.Area = "Onboarding" // from quest tag annotation for the component field
		vars = append(vars, v)
	}
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "federalvkycupdatesubscriber":
		return obj._FederalVKYCUpdateSubscriber.Set(v.FederalVKYCUpdateSubscriber, true, path)
	case "refreshcallstatussubscriber":
		return obj._RefreshCallStatusSubscriber.Set(v.RefreshCallStatusSubscriber, true, path)
	case "karzavkyccalleventsubscriber":
		return obj._KarzaVKYCCallEventSubscriber.Set(v.KarzaVKYCCallEventSubscriber, true, path)
	case "karzavkycagentcallbacksubscriber":
		return obj._KarzaVKYCAgentCallbackSubscriber.Set(v.KarzaVKYCAgentCallbackSubscriber, true, path)
	case "karzavkycauditorcallbacksubscriber":
		return obj._KarzaVKYCAuditorCallbackSubscriber.Set(v.KarzaVKYCAuditorCallbackSubscriber, true, path)
	case "vkycagentupdatedelaysqssubscriber":
		return obj._VKYCAgentUpdateDelaySqsSubscriber.Set(v.VKYCAgentUpdateDelaySqsSubscriber, true, path)
	case "vkycusercommssubscriber":
		return obj._VKYCUserCommsSubscriber.Set(v.VKYCUserCommsSubscriber, true, path)
	case "queueckycsearchsubscriber":
		return obj._QueueCKYCSearchSubscriber.Set(v.QueueCKYCSearchSubscriber, true, path)
	case "queueckycdownloadsubscriber":
		return obj._QueueCKYCDownloadSubscriber.Set(v.QueueCKYCDownloadSubscriber, true, path)
	case "queueaccountstateupdatesubscriber":
		return obj._QueueAccountStateUpdateSubscriber.Set(v.QueueAccountStateUpdateSubscriber, true, path)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "vkyc":
		return obj._VKYC.Set(v.VKYC, true, path)
	case "ekycdowntime":
		return obj._EKYCDowntime.Set(v.EKYCDowntime, true, path)
	case "processvkyconboardingcompleteeventsubscriber":
		return obj._ProcessVKYCOnboardingCompleteEventSubscriber.Set(v.ProcessVKYCOnboardingCompleteEventSubscriber, true, path)
	case "processvkyctransactioneventsubscriber":
		return obj._ProcessVKYCTransactionEventSubscriber.Set(v.ProcessVKYCTransactionEventSubscriber, true, path)
	case "questsdk":
		return obj._QuestSdk.Set(v.QuestSdk, true, path)
	case "uqudopassporttamperingconfig":
		return obj._UqudoPassportTamperingConfig.Set(v.UqudoPassportTamperingConfig, true, path)
	case "featurereleaseconfig":
		return obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj._FederalVKYCUpdateSubscriber.Set(v.FederalVKYCUpdateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RefreshCallStatusSubscriber.Set(v.RefreshCallStatusSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._KarzaVKYCCallEventSubscriber.Set(v.KarzaVKYCCallEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._KarzaVKYCAgentCallbackSubscriber.Set(v.KarzaVKYCAgentCallbackSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._KarzaVKYCAuditorCallbackSubscriber.Set(v.KarzaVKYCAuditorCallbackSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VKYCAgentUpdateDelaySqsSubscriber.Set(v.VKYCAgentUpdateDelaySqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VKYCUserCommsSubscriber.Set(v.VKYCUserCommsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QueueCKYCSearchSubscriber.Set(v.QueueCKYCSearchSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QueueCKYCDownloadSubscriber.Set(v.QueueCKYCDownloadSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QueueAccountStateUpdateSubscriber.Set(v.QueueAccountStateUpdateSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VKYC.Set(v.VKYC, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EKYCDowntime.Set(v.EKYCDowntime, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessVKYCOnboardingCompleteEventSubscriber.Set(v.ProcessVKYCOnboardingCompleteEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessVKYCTransactionEventSubscriber.Set(v.ProcessVKYCTransactionEventSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._QuestSdk.Set(v.QuestSdk, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UqudoPassportTamperingConfig.Set(v.UqudoPassportTamperingConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureReleaseConfig.Set(v.FeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._EpifiDb = v.EpifiDb
	obj._VkycAgentUpdatePublisher = v.VkycAgentUpdatePublisher
	obj._RefreshCallStatusPublisher = v.RefreshCallStatusPublisher
	obj._RefreshCallStatusDelay = v.RefreshCallStatusDelay
	obj._VKYCUserCommsDelayPublisher = v.VKYCUserCommsDelayPublisher
	obj._VKYCUserCommsPublisher = v.VKYCUserCommsPublisher
	obj._VkycCallCompletedEventPublisher = v.VkycCallCompletedEventPublisher
	obj._QueueCKYCSearchPublisher = v.QueueCKYCSearchPublisher
	obj._QueueCKYCDownloadPublisher = v.QueueCKYCDownloadPublisher
	obj._QueueVKYCUpdatePublisher = v.QueueVKYCUpdatePublisher
	obj._EKYCSuccessPublisher = v.EKYCSuccessPublisher
	obj._BKYCUpdateEventPublisher = v.BKYCUpdateEventPublisher
	obj._Delay = v.Delay
	obj._Aws = v.Aws
	obj._Secrets = v.Secrets
	obj._Values = v.Values
	obj._RudderStack = v.RudderStack
	obj._SecureLogging = v.SecureLogging
	obj._VKYCNotification = v.VKYCNotification
	obj._Sentry = v.Sentry
	obj._RateLimit = v.RateLimit
	obj._RedisOptions = v.RedisOptions
	obj._UserRedisOptions = v.UserRedisOptions
	obj._PANValidationRateLimitUpdatePublisher = v.PANValidationRateLimitUpdatePublisher
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._QuestRedisOptions = v.QuestRedisOptions
	obj._KycDb = v.KycDb
	obj._ExtractedDocumentsExpiryConfig = v.ExtractedDocumentsExpiryConfig
	obj._NrBucketName = v.NrBucketName
	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enableckycdatavalidation"] = _obj.SetEnableCKYCDataValidation
	_setters["skipckycdownload"] = _obj.SetSkipCKYCDownload
	_setters["sendpassportbackasimagetokarza"] = _obj.SetSendPassportBackAsImageToKarza
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *config.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enableckycdatavalidation":
		return obj.SetEnableCKYCDataValidation(v.EnableCKYCDataValidation, true, nil)
	case "skipckycdownload":
		return obj.SetSkipCKYCDownload(v.SkipCKYCDownload, true, nil)
	case "sendpassportbackasimagetokarza":
		return obj.SetSendPassportBackAsImageToKarza(v.SendPassportBackAsImageToKarza, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *config.Flags, dynamic bool, path []string) (err error) {

	err = obj.SetEnableCKYCDataValidation(v.EnableCKYCDataValidation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSkipCKYCDownload(v.SkipCKYCDownload, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSendPassportBackAsImageToKarza(v.SendPassportBackAsImageToKarza, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *config.Flags) error {

	obj._TrimDebugMessageFromStatus = v.TrimDebugMessageFromStatus
	return nil
}

func (obj *Flags) SetEnableCKYCDataValidation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableCKYCDataValidation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableCKYCDataValidation, 1)
	} else {
		atomic.StoreUint32(&obj._EnableCKYCDataValidation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableCKYCDataValidation")
	}
	return nil
}
func (obj *Flags) SetSkipCKYCDownload(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.SkipCKYCDownload", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SkipCKYCDownload, 1)
	} else {
		atomic.StoreUint32(&obj._SkipCKYCDownload, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SkipCKYCDownload")
	}
	return nil
}
func (obj *Flags) SetSendPassportBackAsImageToKarza(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.SendPassportBackAsImageToKarza", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SendPassportBackAsImageToKarza, 1)
	} else {
		atomic.StoreUint32(&obj._SendPassportBackAsImageToKarza, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SendPassportBackAsImageToKarza")
	}
	return nil
}

func NewVKYC() (_obj *VKYC, _setters map[string]dynconf.SetFunc) {
	_obj = &VKYC{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["reattemptvkycstarthour"] = _obj.SetReAttemptVkycStartHour
	_setters["reattemptvkycendhour"] = _obj.SetReAttemptVkycEndHour
	_setters["vkycstarthour"] = _obj.SetVkycStartHour
	_setters["vkycendhour"] = _obj.SetVkycEndHour
	_setters["maxvkycprioritylistsize"] = _obj.SetMaxVKYCPriorityListSize
	_setters["instructionpageskipoptiontime"] = _obj.SetInstructionPageSkipOptionTime
	_setters["landingpageskipoptiontime"] = _obj.SetLandingPageSkipOptionTime
	_setters["maxretryforagentrejected"] = _obj.SetMaxRetryForAgentRejected
	_setters["reattemptvkycrolloutpercentage"] = _obj.SetReAttemptVKYCRolloutPercentage
	_setters["nrvkycstarthour"] = _obj.SetNRVKYCStartHour
	_setters["nrvkycendhour"] = _obj.SetNRVKYCEndHour
	_setters["locationmatchthresholdinkm"] = _obj.SetLocationMatchThresholdInKM
	_setters["enablereattemptvkycnudge"] = _obj.SetEnableReAttemptVKYCNudge
	_setters["enablereattemptvkychomebottomsheet"] = _obj.SetEnableReAttemptVKYCHomeBottomSheet
	_setters["enablevkycpriortyflow"] = _obj.SetEnableVKYCPriortyFlow
	_setters["enablecallscheduleforvkycv2flow"] = _obj.SetEnableCallScheduleForVKYCV2Flow
	_setters["enableuploadepanfile"] = _obj.SetEnableUploadEpanFile
	_setters["enablepancapturepreonboarding"] = _obj.SetEnablePanCapturePreOnboarding
	_setters["enablevkycbenefitscreenv2"] = _obj.SetEnableVKYCBenefitScreenV2
	_setters["vkycprioritylistelementttl"] = _obj.SetVKYCPriorityListElementTTL
	_setters["retryepanduration"] = _obj.SetRetryEPANDuration
	_setters["expecteduserwaitingtimeinnrvkyc"] = _obj.SetExpectedUserWaitingTimeInNRVKYC

	_obj._VKYCUserAllowedDateConstraint = &syncmap.Map[string, int]{}
	_setters["vkycuseralloweddateconstraint"] = _obj.SetVKYCUserAllowedDateConstraint

	_obj._VKYCUserAllowedDailyTimeIntervalConstraint = &syncmap.Map[string, int]{}
	_setters["vkycuseralloweddailytimeintervalconstraint"] = _obj.SetVKYCUserAllowedDailyTimeIntervalConstraint
	_setters["unsupportedwebviewdevicemodels"] = _obj.SetUnsupportedWebviewDeviceModels
	_obj._UnsupportedWebviewDeviceModelsMutex = &sync.RWMutex{}
	_setters["unsupportedcustomerdevicemodels"] = _obj.SetUnsupportedCustomerDeviceModels
	_obj._UnsupportedCustomerDeviceModelsMutex = &sync.RWMutex{}
	_setters["federalsftpepanuploadpath"] = _obj.SetFederalSFTPEPANUploadPath
	_obj._FederalSFTPEPANUploadPathMutex = &sync.RWMutex{}
	_setters["federalsftpepanuploadprefix"] = _obj.SetFederalSFTPEPANUploadPrefix
	_obj._FederalSFTPEPANUploadPrefixMutex = &sync.RWMutex{}
	_setters["vkycfaqcategoryid"] = _obj.SetVKYCFaqCategoryId
	_obj._VKYCFaqCategoryIdMutex = &sync.RWMutex{}
	_VKYCPriorityUsersList, _fieldSetters := NewVKYCPriorityUsersList()
	_obj._VKYCPriorityUsersList = _VKYCPriorityUsersList
	helper.AddFieldSetters("vkycpriorityuserslist", _fieldSetters, _setters)
	_AgentWaitingOverlay, _fieldSetters := NewAgentWaitingOverlay()
	_obj._AgentWaitingOverlay = _AgentWaitingOverlay
	helper.AddFieldSetters("agentwaitingoverlay", _fieldSetters, _setters)
	_EnableVKYCScheduleFlow, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableVKYCScheduleFlow = _EnableVKYCScheduleFlow
	helper.AddFieldSetters("enablevkycscheduleflow", _fieldSetters, _setters)
	_EnableVKYCDemandManagement, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableVKYCDemandManagement = _EnableVKYCDemandManagement
	helper.AddFieldSetters("enablevkycdemandmanagement", _fieldSetters, _setters)
	_ABFeatureReleaseConfig, _fieldSetters := genconfig.NewABFeatureReleaseConfig()
	_obj._ABFeatureReleaseConfig = _ABFeatureReleaseConfig
	helper.AddFieldSetters("abfeaturereleaseconfig", _fieldSetters, _setters)
	_EnableConfirmDetailsState, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableConfirmDetailsState = _EnableConfirmDetailsState
	helper.AddFieldSetters("enableconfirmdetailsstate", _fieldSetters, _setters)
	_EnableNRConfirmDetailsState, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableNRConfirmDetailsState = _EnableNRConfirmDetailsState
	helper.AddFieldSetters("enablenrconfirmdetailsstate", _fieldSetters, _setters)
	_EnableEpanInstructionScreen, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableEpanInstructionScreen = _EnableEpanInstructionScreen
	helper.AddFieldSetters("enableepaninstructionscreen", _fieldSetters, _setters)
	_EnableCallQualityCheckStage, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableCallQualityCheckStage = _EnableCallQualityCheckStage
	helper.AddFieldSetters("enablecallqualitycheckstage", _fieldSetters, _setters)
	_VKYCCallQuality, _fieldSetters := NewVKYCCallQuality()
	_obj._VKYCCallQuality = _VKYCCallQuality
	helper.AddFieldSetters("vkyccallquality", _fieldSetters, _setters)
	_EnableApprovedNudgeDismissal, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableApprovedNudgeDismissal = _EnableApprovedNudgeDismissal
	helper.AddFieldSetters("enableapprovednudgedismissal", _fieldSetters, _setters)
	_EnableEPANInNRFlow, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableEPANInNRFlow = _EnableEPANInNRFlow
	helper.AddFieldSetters("enableepaninnrflow", _fieldSetters, _setters)
	_EnableVKYCFlowV2, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableVKYCFlowV2 = _EnableVKYCFlowV2
	helper.AddFieldSetters("enablevkycflowv2", _fieldSetters, _setters)
	_EnableVKYCFlowV2ForHome, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableVKYCFlowV2ForHome = _EnableVKYCFlowV2ForHome
	helper.AddFieldSetters("enablevkycflowv2forhome", _fieldSetters, _setters)
	_EnableCallInitiation, _fieldSetters := NewCallInitiation()
	_obj._EnableCallInitiation = _EnableCallInitiation
	helper.AddFieldSetters("enablecallinitiation", _fieldSetters, _setters)
	return _obj, _setters
}

func NewVKYCWithQuest(questFieldPath string) (_obj *VKYC, _setters map[string]dynconf.SetFunc) {
	_obj = &VKYC{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["reattemptvkycstarthour"] = _obj.SetReAttemptVkycStartHour
	_setters["reattemptvkycendhour"] = _obj.SetReAttemptVkycEndHour
	_setters["vkycstarthour"] = _obj.SetVkycStartHour
	_setters["vkycendhour"] = _obj.SetVkycEndHour
	_setters["maxvkycprioritylistsize"] = _obj.SetMaxVKYCPriorityListSize
	_setters["instructionpageskipoptiontime"] = _obj.SetInstructionPageSkipOptionTime
	_setters["landingpageskipoptiontime"] = _obj.SetLandingPageSkipOptionTime
	_setters["maxretryforagentrejected"] = _obj.SetMaxRetryForAgentRejected
	_setters["reattemptvkycrolloutpercentage"] = _obj.SetReAttemptVKYCRolloutPercentage
	_setters["nrvkycstarthour"] = _obj.SetNRVKYCStartHour
	_setters["nrvkycendhour"] = _obj.SetNRVKYCEndHour
	_setters["locationmatchthresholdinkm"] = _obj.SetLocationMatchThresholdInKM
	_setters["enablereattemptvkycnudge"] = _obj.SetEnableReAttemptVKYCNudge
	_setters["enablereattemptvkychomebottomsheet"] = _obj.SetEnableReAttemptVKYCHomeBottomSheet
	_setters["enablevkycpriortyflow"] = _obj.SetEnableVKYCPriortyFlow
	_setters["enablecallscheduleforvkycv2flow"] = _obj.SetEnableCallScheduleForVKYCV2Flow
	_setters["enableuploadepanfile"] = _obj.SetEnableUploadEpanFile
	_setters["enablepancapturepreonboarding"] = _obj.SetEnablePanCapturePreOnboarding
	_setters["enablevkycbenefitscreenv2"] = _obj.SetEnableVKYCBenefitScreenV2
	_setters["vkycprioritylistelementttl"] = _obj.SetVKYCPriorityListElementTTL
	_setters["retryepanduration"] = _obj.SetRetryEPANDuration
	_setters["expecteduserwaitingtimeinnrvkyc"] = _obj.SetExpectedUserWaitingTimeInNRVKYC

	_obj._VKYCUserAllowedDateConstraint = &syncmap.Map[string, int]{}
	_setters["vkycuseralloweddateconstraint"] = _obj.SetVKYCUserAllowedDateConstraint

	_obj._VKYCUserAllowedDailyTimeIntervalConstraint = &syncmap.Map[string, int]{}
	_setters["vkycuseralloweddailytimeintervalconstraint"] = _obj.SetVKYCUserAllowedDailyTimeIntervalConstraint
	_setters["unsupportedwebviewdevicemodels"] = _obj.SetUnsupportedWebviewDeviceModels
	_obj._UnsupportedWebviewDeviceModelsMutex = &sync.RWMutex{}
	_setters["unsupportedcustomerdevicemodels"] = _obj.SetUnsupportedCustomerDeviceModels
	_obj._UnsupportedCustomerDeviceModelsMutex = &sync.RWMutex{}
	_setters["federalsftpepanuploadpath"] = _obj.SetFederalSFTPEPANUploadPath
	_obj._FederalSFTPEPANUploadPathMutex = &sync.RWMutex{}
	_setters["federalsftpepanuploadprefix"] = _obj.SetFederalSFTPEPANUploadPrefix
	_obj._FederalSFTPEPANUploadPrefixMutex = &sync.RWMutex{}
	_setters["vkycfaqcategoryid"] = _obj.SetVKYCFaqCategoryId
	_obj._VKYCFaqCategoryIdMutex = &sync.RWMutex{}
	_VKYCPriorityUsersList, _fieldSetters := NewVKYCPriorityUsersList()
	_obj._VKYCPriorityUsersList = _VKYCPriorityUsersList
	helper.AddFieldSetters("vkycpriorityuserslist", _fieldSetters, _setters)
	_AgentWaitingOverlay, _fieldSetters := NewAgentWaitingOverlayWithQuest(questFieldPath + "/" + "AgentWaitingOverlay")
	_obj._AgentWaitingOverlay = _AgentWaitingOverlay
	helper.AddFieldSetters("agentwaitingoverlay", _fieldSetters, _setters)
	_EnableVKYCScheduleFlow, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableVKYCScheduleFlow = _EnableVKYCScheduleFlow
	helper.AddFieldSetters("enablevkycscheduleflow", _fieldSetters, _setters)
	_EnableVKYCDemandManagement, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableVKYCDemandManagement = _EnableVKYCDemandManagement
	helper.AddFieldSetters("enablevkycdemandmanagement", _fieldSetters, _setters)
	_ABFeatureReleaseConfig, _fieldSetters := genconfig.NewABFeatureReleaseConfig()
	_obj._ABFeatureReleaseConfig = _ABFeatureReleaseConfig
	helper.AddFieldSetters("abfeaturereleaseconfig", _fieldSetters, _setters)
	_EnableConfirmDetailsState, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableConfirmDetailsState = _EnableConfirmDetailsState
	helper.AddFieldSetters("enableconfirmdetailsstate", _fieldSetters, _setters)
	_EnableNRConfirmDetailsState, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableNRConfirmDetailsState = _EnableNRConfirmDetailsState
	helper.AddFieldSetters("enablenrconfirmdetailsstate", _fieldSetters, _setters)
	_EnableEpanInstructionScreen, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableEpanInstructionScreen = _EnableEpanInstructionScreen
	helper.AddFieldSetters("enableepaninstructionscreen", _fieldSetters, _setters)
	_EnableCallQualityCheckStage, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableCallQualityCheckStage = _EnableCallQualityCheckStage
	helper.AddFieldSetters("enablecallqualitycheckstage", _fieldSetters, _setters)
	_VKYCCallQuality, _fieldSetters := NewVKYCCallQuality()
	_obj._VKYCCallQuality = _VKYCCallQuality
	helper.AddFieldSetters("vkyccallquality", _fieldSetters, _setters)
	_EnableApprovedNudgeDismissal, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableApprovedNudgeDismissal = _EnableApprovedNudgeDismissal
	helper.AddFieldSetters("enableapprovednudgedismissal", _fieldSetters, _setters)
	_EnableEPANInNRFlow, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableEPANInNRFlow = _EnableEPANInNRFlow
	helper.AddFieldSetters("enableepaninnrflow", _fieldSetters, _setters)
	_EnableVKYCFlowV2, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableVKYCFlowV2 = _EnableVKYCFlowV2
	helper.AddFieldSetters("enablevkycflowv2", _fieldSetters, _setters)
	_EnableVKYCFlowV2ForHome, _fieldSetters := genapp.NewFeatureConfig()
	_obj._EnableVKYCFlowV2ForHome = _EnableVKYCFlowV2ForHome
	helper.AddFieldSetters("enablevkycflowv2forhome", _fieldSetters, _setters)
	_EnableCallInitiation, _fieldSetters := NewCallInitiation()
	_obj._EnableCallInitiation = _EnableCallInitiation
	helper.AddFieldSetters("enablecallinitiation", _fieldSetters, _setters)
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *VKYC) Init(questFieldPath string) {
	newObj, _ := NewVKYC()
	*obj = *newObj
}
func (obj *VKYC) InitWithQuest(questFieldPath string) {
	newObj, _ := NewVKYCWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *VKYC) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
	obj._AgentWaitingOverlay.SetQuestSDK(questSdk)
}

func (obj *VKYC) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *VKYC) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "EnableVKYCBenefitScreenV2",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	childVars, childVarsErr = obj._AgentWaitingOverlay.GetQuestVariables()
	if childVarsErr != nil {
		return nil, childVarsErr
	}
	vars = append(vars, childVars...)
	return vars, nil
}

func (obj *VKYC) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.VKYC)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *VKYC) setDynamicField(v *config.VKYC, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "reattemptvkycstarthour":
		return obj.SetReAttemptVkycStartHour(v.ReAttemptVkycStartHour, true, nil)
	case "reattemptvkycendhour":
		return obj.SetReAttemptVkycEndHour(v.ReAttemptVkycEndHour, true, nil)
	case "vkycstarthour":
		return obj.SetVkycStartHour(v.VkycStartHour, true, nil)
	case "vkycendhour":
		return obj.SetVkycEndHour(v.VkycEndHour, true, nil)
	case "maxvkycprioritylistsize":
		return obj.SetMaxVKYCPriorityListSize(v.MaxVKYCPriorityListSize, true, nil)
	case "instructionpageskipoptiontime":
		return obj.SetInstructionPageSkipOptionTime(v.InstructionPageSkipOptionTime, true, nil)
	case "landingpageskipoptiontime":
		return obj.SetLandingPageSkipOptionTime(v.LandingPageSkipOptionTime, true, nil)
	case "maxretryforagentrejected":
		return obj.SetMaxRetryForAgentRejected(v.MaxRetryForAgentRejected, true, nil)
	case "reattemptvkycrolloutpercentage":
		return obj.SetReAttemptVKYCRolloutPercentage(v.ReAttemptVKYCRolloutPercentage, true, nil)
	case "nrvkycstarthour":
		return obj.SetNRVKYCStartHour(v.NRVKYCStartHour, true, nil)
	case "nrvkycendhour":
		return obj.SetNRVKYCEndHour(v.NRVKYCEndHour, true, nil)
	case "locationmatchthresholdinkm":
		return obj.SetLocationMatchThresholdInKM(v.LocationMatchThresholdInKM, true, nil)
	case "enablereattemptvkycnudge":
		return obj.SetEnableReAttemptVKYCNudge(v.EnableReAttemptVKYCNudge, true, nil)
	case "enablereattemptvkychomebottomsheet":
		return obj.SetEnableReAttemptVKYCHomeBottomSheet(v.EnableReAttemptVKYCHomeBottomSheet, true, nil)
	case "enablevkycpriortyflow":
		return obj.SetEnableVKYCPriortyFlow(v.EnableVKYCPriortyFlow, true, nil)
	case "enablecallscheduleforvkycv2flow":
		return obj.SetEnableCallScheduleForVKYCV2Flow(v.EnableCallScheduleForVKYCV2Flow, true, nil)
	case "enableuploadepanfile":
		return obj.SetEnableUploadEpanFile(v.EnableUploadEpanFile, true, nil)
	case "enablepancapturepreonboarding":
		return obj.SetEnablePanCapturePreOnboarding(v.EnablePanCapturePreOnboarding, true, nil)
	case "enablevkycbenefitscreenv2":
		return obj.SetEnableVKYCBenefitScreenV2(v.EnableVKYCBenefitScreenV2, true, nil)
	case "vkycprioritylistelementttl":
		return obj.SetVKYCPriorityListElementTTL(v.VKYCPriorityListElementTTL, true, nil)
	case "retryepanduration":
		return obj.SetRetryEPANDuration(v.RetryEPANDuration, true, nil)
	case "expecteduserwaitingtimeinnrvkyc":
		return obj.SetExpectedUserWaitingTimeInNRVKYC(v.ExpectedUserWaitingTimeInNRVKYC, true, nil)
	case "vkycuseralloweddateconstraint":
		return obj.SetVKYCUserAllowedDateConstraint(v.VKYCUserAllowedDateConstraint, true, path)
	case "vkycuseralloweddailytimeintervalconstraint":
		return obj.SetVKYCUserAllowedDailyTimeIntervalConstraint(v.VKYCUserAllowedDailyTimeIntervalConstraint, true, path)
	case "unsupportedwebviewdevicemodels":
		return obj.SetUnsupportedWebviewDeviceModels(v.UnsupportedWebviewDeviceModels, true, path)
	case "unsupportedcustomerdevicemodels":
		return obj.SetUnsupportedCustomerDeviceModels(v.UnsupportedCustomerDeviceModels, true, path)
	case "federalsftpepanuploadpath":
		return obj.SetFederalSFTPEPANUploadPath(v.FederalSFTPEPANUploadPath, true, nil)
	case "federalsftpepanuploadprefix":
		return obj.SetFederalSFTPEPANUploadPrefix(v.FederalSFTPEPANUploadPrefix, true, nil)
	case "vkycfaqcategoryid":
		return obj.SetVKYCFaqCategoryId(v.VKYCFaqCategoryId, true, nil)
	case "vkycpriorityuserslist":
		return obj._VKYCPriorityUsersList.Set(v.VKYCPriorityUsersList, true, path)
	case "agentwaitingoverlay":
		return obj._AgentWaitingOverlay.Set(v.AgentWaitingOverlay, true, path)
	case "enablevkycscheduleflow":
		return obj._EnableVKYCScheduleFlow.Set(v.EnableVKYCScheduleFlow, true, path)
	case "enablevkycdemandmanagement":
		return obj._EnableVKYCDemandManagement.Set(v.EnableVKYCDemandManagement, true, path)
	case "abfeaturereleaseconfig":
		return obj._ABFeatureReleaseConfig.Set(v.ABFeatureReleaseConfig, true, path)
	case "enableconfirmdetailsstate":
		return obj._EnableConfirmDetailsState.Set(v.EnableConfirmDetailsState, true, path)
	case "enablenrconfirmdetailsstate":
		return obj._EnableNRConfirmDetailsState.Set(v.EnableNRConfirmDetailsState, true, path)
	case "enableepaninstructionscreen":
		return obj._EnableEpanInstructionScreen.Set(v.EnableEpanInstructionScreen, true, path)
	case "enablecallqualitycheckstage":
		return obj._EnableCallQualityCheckStage.Set(v.EnableCallQualityCheckStage, true, path)
	case "vkyccallquality":
		return obj._VKYCCallQuality.Set(v.VKYCCallQuality, true, path)
	case "enableapprovednudgedismissal":
		return obj._EnableApprovedNudgeDismissal.Set(v.EnableApprovedNudgeDismissal, true, path)
	case "enableepaninnrflow":
		return obj._EnableEPANInNRFlow.Set(v.EnableEPANInNRFlow, true, path)
	case "enablevkycflowv2":
		return obj._EnableVKYCFlowV2.Set(v.EnableVKYCFlowV2, true, path)
	case "enablevkycflowv2forhome":
		return obj._EnableVKYCFlowV2ForHome.Set(v.EnableVKYCFlowV2ForHome, true, path)
	case "enablecallinitiation":
		return obj._EnableCallInitiation.Set(v.EnableCallInitiation, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *VKYC) setDynamicFields(v *config.VKYC, dynamic bool, path []string) (err error) {

	err = obj.SetReAttemptVkycStartHour(v.ReAttemptVkycStartHour, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetReAttemptVkycEndHour(v.ReAttemptVkycEndHour, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetVkycStartHour(v.VkycStartHour, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetVkycEndHour(v.VkycEndHour, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxVKYCPriorityListSize(v.MaxVKYCPriorityListSize, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInstructionPageSkipOptionTime(v.InstructionPageSkipOptionTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLandingPageSkipOptionTime(v.LandingPageSkipOptionTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMaxRetryForAgentRejected(v.MaxRetryForAgentRejected, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetReAttemptVKYCRolloutPercentage(v.ReAttemptVKYCRolloutPercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNRVKYCStartHour(v.NRVKYCStartHour, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNRVKYCEndHour(v.NRVKYCEndHour, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLocationMatchThresholdInKM(v.LocationMatchThresholdInKM, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableReAttemptVKYCNudge(v.EnableReAttemptVKYCNudge, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableReAttemptVKYCHomeBottomSheet(v.EnableReAttemptVKYCHomeBottomSheet, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableVKYCPriortyFlow(v.EnableVKYCPriortyFlow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableCallScheduleForVKYCV2Flow(v.EnableCallScheduleForVKYCV2Flow, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableUploadEpanFile(v.EnableUploadEpanFile, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnablePanCapturePreOnboarding(v.EnablePanCapturePreOnboarding, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableVKYCBenefitScreenV2(v.EnableVKYCBenefitScreenV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetVKYCPriorityListElementTTL(v.VKYCPriorityListElementTTL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRetryEPANDuration(v.RetryEPANDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetExpectedUserWaitingTimeInNRVKYC(v.ExpectedUserWaitingTimeInNRVKYC, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetVKYCUserAllowedDateConstraint(v.VKYCUserAllowedDateConstraint, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetVKYCUserAllowedDailyTimeIntervalConstraint(v.VKYCUserAllowedDailyTimeIntervalConstraint, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetUnsupportedWebviewDeviceModels(v.UnsupportedWebviewDeviceModels, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetUnsupportedCustomerDeviceModels(v.UnsupportedCustomerDeviceModels, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetFederalSFTPEPANUploadPath(v.FederalSFTPEPANUploadPath, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFederalSFTPEPANUploadPrefix(v.FederalSFTPEPANUploadPrefix, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetVKYCFaqCategoryId(v.VKYCFaqCategoryId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._VKYCPriorityUsersList.Set(v.VKYCPriorityUsersList, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AgentWaitingOverlay.Set(v.AgentWaitingOverlay, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableVKYCScheduleFlow.Set(v.EnableVKYCScheduleFlow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableVKYCDemandManagement.Set(v.EnableVKYCDemandManagement, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ABFeatureReleaseConfig.Set(v.ABFeatureReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableConfirmDetailsState.Set(v.EnableConfirmDetailsState, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableNRConfirmDetailsState.Set(v.EnableNRConfirmDetailsState, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableEpanInstructionScreen.Set(v.EnableEpanInstructionScreen, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableCallQualityCheckStage.Set(v.EnableCallQualityCheckStage, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VKYCCallQuality.Set(v.VKYCCallQuality, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableApprovedNudgeDismissal.Set(v.EnableApprovedNudgeDismissal, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableEPANInNRFlow.Set(v.EnableEPANInNRFlow, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableVKYCFlowV2.Set(v.EnableVKYCFlowV2, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableVKYCFlowV2ForHome.Set(v.EnableVKYCFlowV2ForHome, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._EnableCallInitiation.Set(v.EnableCallInitiation, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *VKYC) setStaticFields(v *config.VKYC) error {

	obj._PerformEkycAfter = v.PerformEkycAfter
	return nil
}

func (obj *VKYC) SetReAttemptVkycStartHour(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.ReAttemptVkycStartHour", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ReAttemptVkycStartHour, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ReAttemptVkycStartHour")
	}
	return nil
}
func (obj *VKYC) SetReAttemptVkycEndHour(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.ReAttemptVkycEndHour", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ReAttemptVkycEndHour, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ReAttemptVkycEndHour")
	}
	return nil
}
func (obj *VKYC) SetVkycStartHour(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.VkycStartHour", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._VkycStartHour, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "VkycStartHour")
	}
	return nil
}
func (obj *VKYC) SetVkycEndHour(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.VkycEndHour", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._VkycEndHour, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "VkycEndHour")
	}
	return nil
}
func (obj *VKYC) SetMaxVKYCPriorityListSize(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.MaxVKYCPriorityListSize", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxVKYCPriorityListSize, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxVKYCPriorityListSize")
	}
	return nil
}
func (obj *VKYC) SetInstructionPageSkipOptionTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.InstructionPageSkipOptionTime", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._InstructionPageSkipOptionTime, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "InstructionPageSkipOptionTime")
	}
	return nil
}
func (obj *VKYC) SetLandingPageSkipOptionTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.LandingPageSkipOptionTime", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._LandingPageSkipOptionTime, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LandingPageSkipOptionTime")
	}
	return nil
}
func (obj *VKYC) SetMaxRetryForAgentRejected(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.MaxRetryForAgentRejected", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxRetryForAgentRejected, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxRetryForAgentRejected")
	}
	return nil
}
func (obj *VKYC) SetReAttemptVKYCRolloutPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.ReAttemptVKYCRolloutPercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ReAttemptVKYCRolloutPercentage, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ReAttemptVKYCRolloutPercentage")
	}
	return nil
}
func (obj *VKYC) SetNRVKYCStartHour(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.NRVKYCStartHour", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._NRVKYCStartHour, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NRVKYCStartHour")
	}
	return nil
}
func (obj *VKYC) SetNRVKYCEndHour(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.NRVKYCEndHour", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._NRVKYCEndHour, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NRVKYCEndHour")
	}
	return nil
}
func (obj *VKYC) SetLocationMatchThresholdInKM(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.LocationMatchThresholdInKM", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LocationMatchThresholdInKM, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LocationMatchThresholdInKM")
	}
	return nil
}
func (obj *VKYC) SetEnableReAttemptVKYCNudge(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.EnableReAttemptVKYCNudge", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableReAttemptVKYCNudge, 1)
	} else {
		atomic.StoreUint32(&obj._EnableReAttemptVKYCNudge, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableReAttemptVKYCNudge")
	}
	return nil
}
func (obj *VKYC) SetEnableReAttemptVKYCHomeBottomSheet(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.EnableReAttemptVKYCHomeBottomSheet", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableReAttemptVKYCHomeBottomSheet, 1)
	} else {
		atomic.StoreUint32(&obj._EnableReAttemptVKYCHomeBottomSheet, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableReAttemptVKYCHomeBottomSheet")
	}
	return nil
}
func (obj *VKYC) SetEnableVKYCPriortyFlow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.EnableVKYCPriortyFlow", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableVKYCPriortyFlow, 1)
	} else {
		atomic.StoreUint32(&obj._EnableVKYCPriortyFlow, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableVKYCPriortyFlow")
	}
	return nil
}
func (obj *VKYC) SetEnableCallScheduleForVKYCV2Flow(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.EnableCallScheduleForVKYCV2Flow", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableCallScheduleForVKYCV2Flow, 1)
	} else {
		atomic.StoreUint32(&obj._EnableCallScheduleForVKYCV2Flow, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableCallScheduleForVKYCV2Flow")
	}
	return nil
}
func (obj *VKYC) SetEnableUploadEpanFile(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.EnableUploadEpanFile", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableUploadEpanFile, 1)
	} else {
		atomic.StoreUint32(&obj._EnableUploadEpanFile, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableUploadEpanFile")
	}
	return nil
}
func (obj *VKYC) SetEnablePanCapturePreOnboarding(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.EnablePanCapturePreOnboarding", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnablePanCapturePreOnboarding, 1)
	} else {
		atomic.StoreUint32(&obj._EnablePanCapturePreOnboarding, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnablePanCapturePreOnboarding")
	}
	return nil
}
func (obj *VKYC) SetEnableVKYCBenefitScreenV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.EnableVKYCBenefitScreenV2", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableVKYCBenefitScreenV2, 1)
	} else {
		atomic.StoreUint32(&obj._EnableVKYCBenefitScreenV2, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableVKYCBenefitScreenV2")
	}
	return nil
}
func (obj *VKYC) SetVKYCPriorityListElementTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.VKYCPriorityListElementTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._VKYCPriorityListElementTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "VKYCPriorityListElementTTL")
	}
	return nil
}
func (obj *VKYC) SetRetryEPANDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.RetryEPANDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RetryEPANDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RetryEPANDuration")
	}
	return nil
}
func (obj *VKYC) SetExpectedUserWaitingTimeInNRVKYC(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.ExpectedUserWaitingTimeInNRVKYC", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ExpectedUserWaitingTimeInNRVKYC, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExpectedUserWaitingTimeInNRVKYC")
	}
	return nil
}
func (obj *VKYC) SetVKYCUserAllowedDateConstraint(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.VKYCUserAllowedDateConstraint", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._VKYCUserAllowedDateConstraint, v, path)
}
func (obj *VKYC) SetVKYCUserAllowedDailyTimeIntervalConstraint(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.VKYCUserAllowedDailyTimeIntervalConstraint", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._VKYCUserAllowedDailyTimeIntervalConstraint, v, path)
}
func (obj *VKYC) SetUnsupportedWebviewDeviceModels(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.UnsupportedWebviewDeviceModels", reflect.TypeOf(val))
	}
	obj._UnsupportedWebviewDeviceModelsMutex.Lock()
	defer obj._UnsupportedWebviewDeviceModelsMutex.Unlock()
	obj._UnsupportedWebviewDeviceModels = roarray.New[string](v)
	return nil
}
func (obj *VKYC) SetUnsupportedCustomerDeviceModels(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.UnsupportedCustomerDeviceModels", reflect.TypeOf(val))
	}
	obj._UnsupportedCustomerDeviceModelsMutex.Lock()
	defer obj._UnsupportedCustomerDeviceModelsMutex.Unlock()
	obj._UnsupportedCustomerDeviceModels = roarray.New[string](v)
	return nil
}
func (obj *VKYC) SetFederalSFTPEPANUploadPath(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.FederalSFTPEPANUploadPath", reflect.TypeOf(val))
	}
	obj._FederalSFTPEPANUploadPathMutex.Lock()
	defer obj._FederalSFTPEPANUploadPathMutex.Unlock()
	obj._FederalSFTPEPANUploadPath = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FederalSFTPEPANUploadPath")
	}
	return nil
}
func (obj *VKYC) SetFederalSFTPEPANUploadPrefix(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.FederalSFTPEPANUploadPrefix", reflect.TypeOf(val))
	}
	obj._FederalSFTPEPANUploadPrefixMutex.Lock()
	defer obj._FederalSFTPEPANUploadPrefixMutex.Unlock()
	obj._FederalSFTPEPANUploadPrefix = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FederalSFTPEPANUploadPrefix")
	}
	return nil
}
func (obj *VKYC) SetVKYCFaqCategoryId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYC.VKYCFaqCategoryId", reflect.TypeOf(val))
	}
	obj._VKYCFaqCategoryIdMutex.Lock()
	defer obj._VKYCFaqCategoryIdMutex.Unlock()
	obj._VKYCFaqCategoryId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "VKYCFaqCategoryId")
	}
	return nil
}

func NewVKYCPriorityUsersList() (_obj *VKYCPriorityUsersList, _setters map[string]dynconf.SetFunc) {
	_obj = &VKYCPriorityUsersList{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["otypeuser"] = _obj.SetOTypeUser
	_setters["salarieduser"] = _obj.SetSalariedUser
	_setters["lastndaysuser"] = _obj.SetLastNDaysUser
	return _obj, _setters
}

func (obj *VKYCPriorityUsersList) Init() {
	newObj, _ := NewVKYCPriorityUsersList()
	*obj = *newObj
}

func (obj *VKYCPriorityUsersList) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *VKYCPriorityUsersList) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.VKYCPriorityUsersList)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCPriorityUsersList", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *VKYCPriorityUsersList) setDynamicField(v *config.VKYCPriorityUsersList, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "otypeuser":
		return obj.SetOTypeUser(v.OTypeUser, true, nil)
	case "salarieduser":
		return obj.SetSalariedUser(v.SalariedUser, true, nil)
	case "lastndaysuser":
		return obj.SetLastNDaysUser(v.LastNDaysUser, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *VKYCPriorityUsersList) setDynamicFields(v *config.VKYCPriorityUsersList, dynamic bool, path []string) (err error) {

	err = obj.SetOTypeUser(v.OTypeUser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSalariedUser(v.SalariedUser, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLastNDaysUser(v.LastNDaysUser, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *VKYCPriorityUsersList) setStaticFields(v *config.VKYCPriorityUsersList) error {

	return nil
}

func (obj *VKYCPriorityUsersList) SetOTypeUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCPriorityUsersList.OTypeUser", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._OTypeUser, 1)
	} else {
		atomic.StoreUint32(&obj._OTypeUser, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "OTypeUser")
	}
	return nil
}
func (obj *VKYCPriorityUsersList) SetSalariedUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCPriorityUsersList.SalariedUser", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._SalariedUser, 1)
	} else {
		atomic.StoreUint32(&obj._SalariedUser, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "SalariedUser")
	}
	return nil
}
func (obj *VKYCPriorityUsersList) SetLastNDaysUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCPriorityUsersList.LastNDaysUser", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._LastNDaysUser, 1)
	} else {
		atomic.StoreUint32(&obj._LastNDaysUser, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "LastNDaysUser")
	}
	return nil
}

func NewAgentWaitingOverlay() (_obj *AgentWaitingOverlay, _setters map[string]dynconf.SetFunc) {
	_obj = &AgentWaitingOverlay{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disable"] = _obj.SetDisable
	return _obj, _setters
}

func NewAgentWaitingOverlayWithQuest(questFieldPath string) (_obj *AgentWaitingOverlay, _setters map[string]dynconf.SetFunc) {
	_obj = &AgentWaitingOverlay{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["disable"] = _obj.SetDisable
	_obj.questFieldPath = questFieldPath
	return _obj, _setters
}

func (obj *AgentWaitingOverlay) Init(questFieldPath string) {
	newObj, _ := NewAgentWaitingOverlay()
	*obj = *newObj
}
func (obj *AgentWaitingOverlay) InitWithQuest(questFieldPath string) {
	newObj, _ := NewAgentWaitingOverlayWithQuest(questFieldPath)
	*obj = *newObj
}

func (obj *AgentWaitingOverlay) SetQuestSDK(questSdk questsdk.Client) {
	obj.questSdk = questSdk
}

func (obj *AgentWaitingOverlay) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AgentWaitingOverlay) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	v = &questtypes.Variable{
		Path:     obj.questFieldPath + "/" + "Disable",
		Datatype: &pkgweb.Datatype{BaseType: pkgweb.PrimitiveType_PRIMITIVE_TYPE_BOOL},
		Area:     "", // from quest tag annotation
	}
	vars = append(vars, v)
	return vars, nil
}

func (obj *AgentWaitingOverlay) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AgentWaitingOverlay)
	if !ok {
		return fmt.Errorf("invalid data type %v *AgentWaitingOverlay", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AgentWaitingOverlay) setDynamicField(v *config.AgentWaitingOverlay, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "disable":
		return obj.SetDisable(v.Disable, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AgentWaitingOverlay) setDynamicFields(v *config.AgentWaitingOverlay, dynamic bool, path []string) (err error) {

	err = obj.SetDisable(v.Disable, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AgentWaitingOverlay) setStaticFields(v *config.AgentWaitingOverlay) error {

	return nil
}

func (obj *AgentWaitingOverlay) SetDisable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AgentWaitingOverlay.Disable", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Disable, 1)
	} else {
		atomic.StoreUint32(&obj._Disable, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Disable")
	}
	return nil
}

func NewVKYCCallQuality() (_obj *VKYCCallQuality, _setters map[string]dynconf.SetFunc) {
	_obj = &VKYCCallQuality{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["noiselevelthresholdandroid"] = _obj.SetNoiseLevelThresholdAndroid
	_setters["imageluminancethresholdandroid"] = _obj.SetImageLuminanceThresholdAndroid
	_setters["noiselevelthresholdios"] = _obj.SetNoiseLevelThresholdIOS
	_setters["imageluminancethresholdios"] = _obj.SetImageLuminanceThresholdIOS
	_setters["checkdurationinmillis"] = _obj.SetCheckDurationInMillis
	_setters["sampledurationinmillis"] = _obj.SetSampleDurationInMillis
	return _obj, _setters
}

func (obj *VKYCCallQuality) Init() {
	newObj, _ := NewVKYCCallQuality()
	*obj = *newObj
}

func (obj *VKYCCallQuality) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *VKYCCallQuality) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.VKYCCallQuality)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCCallQuality", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *VKYCCallQuality) setDynamicField(v *config.VKYCCallQuality, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "noiselevelthresholdandroid":
		return obj.SetNoiseLevelThresholdAndroid(v.NoiseLevelThresholdAndroid, true, nil)
	case "imageluminancethresholdandroid":
		return obj.SetImageLuminanceThresholdAndroid(v.ImageLuminanceThresholdAndroid, true, nil)
	case "noiselevelthresholdios":
		return obj.SetNoiseLevelThresholdIOS(v.NoiseLevelThresholdIOS, true, nil)
	case "imageluminancethresholdios":
		return obj.SetImageLuminanceThresholdIOS(v.ImageLuminanceThresholdIOS, true, nil)
	case "checkdurationinmillis":
		return obj.SetCheckDurationInMillis(v.CheckDurationInMillis, true, nil)
	case "sampledurationinmillis":
		return obj.SetSampleDurationInMillis(v.SampleDurationInMillis, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *VKYCCallQuality) setDynamicFields(v *config.VKYCCallQuality, dynamic bool, path []string) (err error) {

	err = obj.SetNoiseLevelThresholdAndroid(v.NoiseLevelThresholdAndroid, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetImageLuminanceThresholdAndroid(v.ImageLuminanceThresholdAndroid, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetNoiseLevelThresholdIOS(v.NoiseLevelThresholdIOS, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetImageLuminanceThresholdIOS(v.ImageLuminanceThresholdIOS, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCheckDurationInMillis(v.CheckDurationInMillis, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSampleDurationInMillis(v.SampleDurationInMillis, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *VKYCCallQuality) setStaticFields(v *config.VKYCCallQuality) error {

	return nil
}

func (obj *VKYCCallQuality) SetNoiseLevelThresholdAndroid(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCCallQuality.NoiseLevelThresholdAndroid", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._NoiseLevelThresholdAndroid, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NoiseLevelThresholdAndroid")
	}
	return nil
}
func (obj *VKYCCallQuality) SetImageLuminanceThresholdAndroid(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCCallQuality.ImageLuminanceThresholdAndroid", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ImageLuminanceThresholdAndroid, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ImageLuminanceThresholdAndroid")
	}
	return nil
}
func (obj *VKYCCallQuality) SetNoiseLevelThresholdIOS(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCCallQuality.NoiseLevelThresholdIOS", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._NoiseLevelThresholdIOS, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "NoiseLevelThresholdIOS")
	}
	return nil
}
func (obj *VKYCCallQuality) SetImageLuminanceThresholdIOS(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCCallQuality.ImageLuminanceThresholdIOS", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ImageLuminanceThresholdIOS, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ImageLuminanceThresholdIOS")
	}
	return nil
}
func (obj *VKYCCallQuality) SetCheckDurationInMillis(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCCallQuality.CheckDurationInMillis", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CheckDurationInMillis, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CheckDurationInMillis")
	}
	return nil
}
func (obj *VKYCCallQuality) SetSampleDurationInMillis(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *VKYCCallQuality.SampleDurationInMillis", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._SampleDurationInMillis, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "SampleDurationInMillis")
	}
	return nil
}

func NewCallInitiation() (_obj *CallInitiation, _setters map[string]dynconf.SetFunc) {
	_obj = &CallInitiation{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["rolloutpercentage"] = _obj.SetRolloutPercentage
	_Enable, _fieldSetters := genapp.NewFeatureConfig()
	_obj._Enable = _Enable
	helper.AddFieldSetters("enable", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *CallInitiation) Init() {
	newObj, _ := NewCallInitiation()
	*obj = *newObj
}

func (obj *CallInitiation) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CallInitiation) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CallInitiation)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallInitiation", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CallInitiation) setDynamicField(v *config.CallInitiation, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "rolloutpercentage":
		return obj.SetRolloutPercentage(v.RolloutPercentage, true, nil)
	case "enable":
		return obj._Enable.Set(v.Enable, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CallInitiation) setDynamicFields(v *config.CallInitiation, dynamic bool, path []string) (err error) {

	err = obj.SetRolloutPercentage(v.RolloutPercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._Enable.Set(v.Enable, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CallInitiation) setStaticFields(v *config.CallInitiation) error {

	return nil
}

func (obj *CallInitiation) SetRolloutPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *CallInitiation.RolloutPercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RolloutPercentage, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RolloutPercentage")
	}
	return nil
}

func NewEKYCDowntime() (_obj *EKYCDowntime, _setters map[string]dynconf.SetFunc) {
	_obj = &EKYCDowntime{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["ekycdowntimefrom"] = _obj.SetEKYCDowntimeFrom
	_obj._EKYCDowntimeFromMutex = &sync.RWMutex{}
	_setters["ekycdowntimeto"] = _obj.SetEKYCDowntimeTo
	_obj._EKYCDowntimeToMutex = &sync.RWMutex{}
	_setters["ekycdowntimemessage"] = _obj.SetEKYCDowntimeMessage
	_obj._EKYCDowntimeMessageMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *EKYCDowntime) Init() {
	newObj, _ := NewEKYCDowntime()
	*obj = *newObj
}

func (obj *EKYCDowntime) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *EKYCDowntime) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.EKYCDowntime)
	if !ok {
		return fmt.Errorf("invalid data type %v *EKYCDowntime", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *EKYCDowntime) setDynamicField(v *config.EKYCDowntime, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "ekycdowntimefrom":
		return obj.SetEKYCDowntimeFrom(v.EKYCDowntimeFrom, true, nil)
	case "ekycdowntimeto":
		return obj.SetEKYCDowntimeTo(v.EKYCDowntimeTo, true, nil)
	case "ekycdowntimemessage":
		return obj.SetEKYCDowntimeMessage(v.EKYCDowntimeMessage, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *EKYCDowntime) setDynamicFields(v *config.EKYCDowntime, dynamic bool, path []string) (err error) {

	err = obj.SetEKYCDowntimeFrom(v.EKYCDowntimeFrom, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEKYCDowntimeTo(v.EKYCDowntimeTo, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEKYCDowntimeMessage(v.EKYCDowntimeMessage, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *EKYCDowntime) setStaticFields(v *config.EKYCDowntime) error {

	return nil
}

func (obj *EKYCDowntime) SetEKYCDowntimeFrom(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EKYCDowntime.EKYCDowntimeFrom", reflect.TypeOf(val))
	}
	obj._EKYCDowntimeFromMutex.Lock()
	defer obj._EKYCDowntimeFromMutex.Unlock()
	obj._EKYCDowntimeFrom = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "EKYCDowntimeFrom")
	}
	return nil
}
func (obj *EKYCDowntime) SetEKYCDowntimeTo(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EKYCDowntime.EKYCDowntimeTo", reflect.TypeOf(val))
	}
	obj._EKYCDowntimeToMutex.Lock()
	defer obj._EKYCDowntimeToMutex.Unlock()
	obj._EKYCDowntimeTo = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "EKYCDowntimeTo")
	}
	return nil
}
func (obj *EKYCDowntime) SetEKYCDowntimeMessage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *EKYCDowntime.EKYCDowntimeMessage", reflect.TypeOf(val))
	}
	obj._EKYCDowntimeMessageMutex.Lock()
	defer obj._EKYCDowntimeMessageMutex.Unlock()
	obj._EKYCDowntimeMessage = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "EKYCDowntimeMessage")
	}
	return nil
}

func NewUqudoPassportTampering() (_obj *UqudoPassportTampering, _setters map[string]dynconf.SetFunc) {
	_obj = &UqudoPassportTampering{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["screendetectionthreshold"] = _obj.SetScreenDetectionThreshold
	_setters["printdetectionthreshold"] = _obj.SetPrintDetectionThreshold
	_setters["phototamperingthreshold"] = _obj.SetPhotoTamperingThreshold
	_setters["enabletampercheck"] = _obj.SetEnableTamperCheck
	return _obj, _setters
}

func (obj *UqudoPassportTampering) Init() {
	newObj, _ := NewUqudoPassportTampering()
	*obj = *newObj
}

func (obj *UqudoPassportTampering) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UqudoPassportTampering) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UqudoPassportTampering)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoPassportTampering", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UqudoPassportTampering) setDynamicField(v *config.UqudoPassportTampering, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "screendetectionthreshold":
		return obj.SetScreenDetectionThreshold(v.ScreenDetectionThreshold, true, nil)
	case "printdetectionthreshold":
		return obj.SetPrintDetectionThreshold(v.PrintDetectionThreshold, true, nil)
	case "phototamperingthreshold":
		return obj.SetPhotoTamperingThreshold(v.PhotoTamperingThreshold, true, nil)
	case "enabletampercheck":
		return obj.SetEnableTamperCheck(v.EnableTamperCheck, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UqudoPassportTampering) setDynamicFields(v *config.UqudoPassportTampering, dynamic bool, path []string) (err error) {

	err = obj.SetScreenDetectionThreshold(v.ScreenDetectionThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPrintDetectionThreshold(v.PrintDetectionThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPhotoTamperingThreshold(v.PhotoTamperingThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableTamperCheck(v.EnableTamperCheck, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UqudoPassportTampering) setStaticFields(v *config.UqudoPassportTampering) error {

	return nil
}

func (obj *UqudoPassportTampering) SetScreenDetectionThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoPassportTampering.ScreenDetectionThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ScreenDetectionThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ScreenDetectionThreshold")
	}
	return nil
}
func (obj *UqudoPassportTampering) SetPrintDetectionThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoPassportTampering.PrintDetectionThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._PrintDetectionThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PrintDetectionThreshold")
	}
	return nil
}
func (obj *UqudoPassportTampering) SetPhotoTamperingThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoPassportTampering.PhotoTamperingThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._PhotoTamperingThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "PhotoTamperingThreshold")
	}
	return nil
}
func (obj *UqudoPassportTampering) SetEnableTamperCheck(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *UqudoPassportTampering.EnableTamperCheck", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableTamperCheck, 1)
	} else {
		atomic.StoreUint32(&obj._EnableTamperCheck, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableTamperCheck")
	}
	return nil
}
