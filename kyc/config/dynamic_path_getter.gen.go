// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "federalvkycupdatesubscriber":
		return obj.FederalVKYCUpdateSubscriber.Get(dynamicFieldPath[1:])
	case "refreshcallstatussubscriber":
		return obj.RefreshCallStatusSubscriber.Get(dynamicFieldPath[1:])
	case "karzavkyccalleventsubscriber":
		return obj.KarzaVKYCCallEventSubscriber.Get(dynamicFieldPath[1:])
	case "karzavkycagentcallbacksubscriber":
		return obj.KarzaVKYCAgentCallbackSubscriber.Get(dynamicFieldPath[1:])
	case "karzavkycauditorcallbacksubscriber":
		return obj.KarzaVKYCAuditorCallbackSubscriber.Get(dynamicFieldPath[1:])
	case "vkycagentupdatedelaysqssubscriber":
		return obj.VKYCAgentUpdateDelaySqsSubscriber.Get(dynamicFieldPath[1:])
	case "vkycusercommssubscriber":
		return obj.VKYCUserCommsSubscriber.Get(dynamicFieldPath[1:])
	case "queueckycsearchsubscriber":
		return obj.QueueCKYCSearchSubscriber.Get(dynamicFieldPath[1:])
	case "queueckycdownloadsubscriber":
		return obj.QueueCKYCDownloadSubscriber.Get(dynamicFieldPath[1:])
	case "queueaccountstateupdatesubscriber":
		return obj.QueueAccountStateUpdateSubscriber.Get(dynamicFieldPath[1:])
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "vkyc":
		return obj.VKYC.Get(dynamicFieldPath[1:])
	case "ekycdowntime":
		return obj.EKYCDowntime.Get(dynamicFieldPath[1:])
	case "processvkyconboardingcompleteeventsubscriber":
		return obj.ProcessVKYCOnboardingCompleteEventSubscriber.Get(dynamicFieldPath[1:])
	case "processvkyctransactioneventsubscriber":
		return obj.ProcessVKYCTransactionEventSubscriber.Get(dynamicFieldPath[1:])
	case "questsdk":
		return obj.QuestSdk.Get(dynamicFieldPath[1:])
	case "uqudopassporttamperingconfig":
		return obj.UqudoPassportTamperingConfig.Get(dynamicFieldPath[1:])
	case "featurereleaseconfig":
		return obj.FeatureReleaseConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enableckycdatavalidation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableCKYCDataValidation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableCKYCDataValidation, nil
	case "skipckycdownload":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SkipCKYCDownload\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SkipCKYCDownload, nil
	case "sendpassportbackasimagetokarza":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SendPassportBackAsImageToKarza\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SendPassportBackAsImageToKarza, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VKYC) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "reattemptvkycstarthour":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ReAttemptVkycStartHour\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ReAttemptVkycStartHour, nil
	case "reattemptvkycendhour":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ReAttemptVkycEndHour\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ReAttemptVkycEndHour, nil
	case "vkycstarthour":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VkycStartHour\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VkycStartHour, nil
	case "vkycendhour":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VkycEndHour\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VkycEndHour, nil
	case "maxvkycprioritylistsize":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxVKYCPriorityListSize\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxVKYCPriorityListSize, nil
	case "instructionpageskipoptiontime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InstructionPageSkipOptionTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InstructionPageSkipOptionTime, nil
	case "landingpageskipoptiontime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LandingPageSkipOptionTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LandingPageSkipOptionTime, nil
	case "maxretryforagentrejected":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxRetryForAgentRejected\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxRetryForAgentRejected, nil
	case "reattemptvkycrolloutpercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ReAttemptVKYCRolloutPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ReAttemptVKYCRolloutPercentage, nil
	case "nrvkycstarthour":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NRVKYCStartHour\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NRVKYCStartHour, nil
	case "nrvkycendhour":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NRVKYCEndHour\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NRVKYCEndHour, nil
	case "locationmatchthresholdinkm":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LocationMatchThresholdInKM\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LocationMatchThresholdInKM, nil
	case "enablereattemptvkycnudge":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableReAttemptVKYCNudge\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableReAttemptVKYCNudge, nil
	case "enablereattemptvkychomebottomsheet":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableReAttemptVKYCHomeBottomSheet\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableReAttemptVKYCHomeBottomSheet, nil
	case "enablevkycpriortyflow":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableVKYCPriortyFlow\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableVKYCPriortyFlow, nil
	case "enablecallscheduleforvkycv2flow":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableCallScheduleForVKYCV2Flow\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableCallScheduleForVKYCV2Flow, nil
	case "enableuploadepanfile":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableUploadEpanFile\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableUploadEpanFile, nil
	case "enablepancapturepreonboarding":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnablePanCapturePreOnboarding\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnablePanCapturePreOnboarding, nil
	case "enablevkycbenefitscreenv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableVKYCBenefitScreenV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableVKYCBenefitScreenV2, nil
	case "vkycprioritylistelementttl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VKYCPriorityListElementTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VKYCPriorityListElementTTL, nil
	case "retryepanduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RetryEPANDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RetryEPANDuration, nil
	case "expecteduserwaitingtimeinnrvkyc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExpectedUserWaitingTimeInNRVKYC\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ExpectedUserWaitingTimeInNRVKYC, nil
	case "vkycuseralloweddateconstraint":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.VKYCUserAllowedDateConstraint, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"VKYCUserAllowedDateConstraint\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.VKYCUserAllowedDateConstraint[dynamicFieldPath[1]], nil

		}
		return obj.VKYCUserAllowedDateConstraint, nil
	case "vkycuseralloweddailytimeintervalconstraint":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.VKYCUserAllowedDailyTimeIntervalConstraint, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"VKYCUserAllowedDailyTimeIntervalConstraint\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.VKYCUserAllowedDailyTimeIntervalConstraint[dynamicFieldPath[1]], nil

		}
		return obj.VKYCUserAllowedDailyTimeIntervalConstraint, nil
	case "unsupportedwebviewdevicemodels":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UnsupportedWebviewDeviceModels\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UnsupportedWebviewDeviceModels, nil
	case "unsupportedcustomerdevicemodels":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UnsupportedCustomerDeviceModels\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UnsupportedCustomerDeviceModels, nil
	case "federalsftpepanuploadpath":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FederalSFTPEPANUploadPath\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FederalSFTPEPANUploadPath, nil
	case "federalsftpepanuploadprefix":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FederalSFTPEPANUploadPrefix\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FederalSFTPEPANUploadPrefix, nil
	case "vkycfaqcategoryid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"VKYCFaqCategoryId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.VKYCFaqCategoryId, nil
	case "vkycpriorityuserslist":
		return obj.VKYCPriorityUsersList.Get(dynamicFieldPath[1:])
	case "agentwaitingoverlay":
		return obj.AgentWaitingOverlay.Get(dynamicFieldPath[1:])
	case "enablevkycscheduleflow":
		return obj.EnableVKYCScheduleFlow.Get(dynamicFieldPath[1:])
	case "enablevkycdemandmanagement":
		return obj.EnableVKYCDemandManagement.Get(dynamicFieldPath[1:])
	case "abfeaturereleaseconfig":
		return obj.ABFeatureReleaseConfig.Get(dynamicFieldPath[1:])
	case "enableconfirmdetailsstate":
		return obj.EnableConfirmDetailsState.Get(dynamicFieldPath[1:])
	case "enablenrconfirmdetailsstate":
		return obj.EnableNRConfirmDetailsState.Get(dynamicFieldPath[1:])
	case "enableepaninstructionscreen":
		return obj.EnableEpanInstructionScreen.Get(dynamicFieldPath[1:])
	case "enablecallqualitycheckstage":
		return obj.EnableCallQualityCheckStage.Get(dynamicFieldPath[1:])
	case "vkyccallquality":
		return obj.VKYCCallQuality.Get(dynamicFieldPath[1:])
	case "enableapprovednudgedismissal":
		return obj.EnableApprovedNudgeDismissal.Get(dynamicFieldPath[1:])
	case "enableepaninnrflow":
		return obj.EnableEPANInNRFlow.Get(dynamicFieldPath[1:])
	case "enablevkycflowv2":
		return obj.EnableVKYCFlowV2.Get(dynamicFieldPath[1:])
	case "enablevkycflowv2forhome":
		return obj.EnableVKYCFlowV2ForHome.Get(dynamicFieldPath[1:])
	case "enablecallinitiation":
		return obj.EnableCallInitiation.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VKYC", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VKYCPriorityUsersList) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "otypeuser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OTypeUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OTypeUser, nil
	case "salarieduser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SalariedUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SalariedUser, nil
	case "lastndaysuser":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LastNDaysUser\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LastNDaysUser, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VKYCPriorityUsersList", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AgentWaitingOverlay) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "disable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Disable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Disable, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AgentWaitingOverlay", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VKYCCallQuality) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "noiselevelthresholdandroid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NoiseLevelThresholdAndroid\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NoiseLevelThresholdAndroid, nil
	case "imageluminancethresholdandroid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ImageLuminanceThresholdAndroid\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ImageLuminanceThresholdAndroid, nil
	case "noiselevelthresholdios":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"NoiseLevelThresholdIOS\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.NoiseLevelThresholdIOS, nil
	case "imageluminancethresholdios":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ImageLuminanceThresholdIOS\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ImageLuminanceThresholdIOS, nil
	case "checkdurationinmillis":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CheckDurationInMillis\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CheckDurationInMillis, nil
	case "sampledurationinmillis":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SampleDurationInMillis\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SampleDurationInMillis, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VKYCCallQuality", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CallInitiation) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "rolloutpercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RolloutPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RolloutPercentage, nil
	case "enable":
		return obj.Enable.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CallInitiation", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *EKYCDowntime) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "ekycdowntimefrom":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EKYCDowntimeFrom\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EKYCDowntimeFrom, nil
	case "ekycdowntimeto":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EKYCDowntimeTo\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EKYCDowntimeTo, nil
	case "ekycdowntimemessage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EKYCDowntimeMessage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EKYCDowntimeMessage, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for EKYCDowntime", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UqudoPassportTampering) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "screendetectionthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ScreenDetectionThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ScreenDetectionThreshold, nil
	case "printdetectionthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PrintDetectionThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PrintDetectionThreshold, nil
	case "phototamperingthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PhotoTamperingThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PhotoTamperingThreshold, nil
	case "enabletampercheck":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableTamperCheck\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableTamperCheck, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UqudoPassportTampering", strings.Join(dynamicFieldPath, "."))
	}
}
