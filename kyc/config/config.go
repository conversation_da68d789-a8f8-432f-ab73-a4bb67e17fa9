package config

import (
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/frontend/app"
	sdkconfig "github.com/epifi/be-common/quest/sdk/config"

	releaseConfig "github.com/epifi/gamma/pkg/feature/release/config"
)

const (
	QUEUE_CKYC_SEARCH          = "QueueCKYCSearch"
	QUEUE_CKYC_DOWNLOAD        = "QueueCKYCDownload"
	QUEUE_ACCOUNT_STATE_UPDATE = "QueueAccountStateUpdate"
	BUCKET_LIVENESS            = "BucketLiveness"
	RudderWriteKey             = "RudderWriteKey"
	QUEUE_VKYC_UPDATE          = "QueueVKYCUpdate"
	QUEUE_EKYC_SUCCESS         = "EKYCSuccess"
	KycDbCredentials           = "KycDbUserCredentials" // nolint: gosec
)

var (
	once   sync.Once
	config *Config
	err    error
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(false)
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

// LoadOnlyStaticConf loads configs only from static config files if not done already and returns it
func LoadOnlyStaticConf() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig(true)
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

func loadConfig(onlyStaticFiles bool) (*Config, error) {

	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.KYC_SERVICE)

	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, cfg.DefaultUnmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}
	if onlyStaticFiles {
		return conf, nil
	}
	conf.Secrets.Ids = cfg.AddCrdbSslCertSecretIds(conf.EpifiDb, conf.Secrets.Ids)
	conf.Secrets.Ids = cfg.AddPgdbSslCertSecretIds(conf.KycDb, conf.Secrets.Ids)
	keyToSecret, err := cfg.LoadSecrets(conf.Secrets.Ids, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return nil, err
	}

	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}
	return conf, nil
}

// update the DB endpoint and port
// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	dbServerEndpoint := cfg.GetDbEndpoint(cfg.PRIMARY_CRDB)
	cfg.UpdateDbEndpointInConfig(c.EpifiDb, dbServerEndpoint)

	pgdbServerEndpoint := cfg.GetDbEndpoint(cfg.MINERVA_RDS)
	cfg.UpdateDbEndpointInConfig(c.KycDb, pgdbServerEndpoint)

	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	cfg.UpdateSecretValues(c.EpifiDb, c.Secrets, keyToSecret)
	cfg.UpdatePGDBSecretValues(c.KycDb, c.Secrets, keyToSecret)

	if c.Application.Environment == cfg.TestEnv || c.Application.Environment == cfg.DevelopmentEnv {
		cfg.UpdateDbUsernamePasswordInConfig(c.KycDb, c.Secrets.Ids[KycDbCredentials])
		return nil
	}
	if _, ok := keyToSecret[KycDbCredentials]; !ok {
		return errors.New("db username password not fetched for kyc db from secrets manager")
	}
	cfg.UpdateDbUsernamePasswordInConfig(c.KycDb, keyToSecret[KycDbCredentials])

	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcPort = intVal
	}

	if val, ok := os.LookupEnv("DB_HOST"); ok {
		c.EpifiDb.Host = val
	}

	if val, ok := os.LookupEnv("PGDB_HOST"); ok {
		c.KycDb.Host = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

// TODO(kunal): Add validate tags later and verify config struct post unmarshalling
//
//go:generate conf_gen github.com/epifi/gamma/kyc/config Config
type Config struct {
	Application                        *Application
	Server                             *Server
	Logging                            *cfg.Logging
	EpifiDb                            *cfg.DB
	VkycAgentUpdatePublisher           *cfg.SqsPublisher
	FederalVKYCUpdateSubscriber        *cfg.SqsSubscriber `dynamic:"true"`
	RefreshCallStatusPublisher         *cfg.SqsPublisher
	RefreshCallStatusDelay             time.Duration
	RefreshCallStatusSubscriber        *cfg.SqsSubscriber `dynamic:"true"`
	KarzaVKYCCallEventSubscriber       *cfg.SqsSubscriber `dynamic:"true"`
	KarzaVKYCAgentCallbackSubscriber   *cfg.SqsSubscriber `dynamic:"true"`
	KarzaVKYCAuditorCallbackSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	VKYCAgentUpdateDelaySqsSubscriber  *cfg.SqsSubscriber `dynamic:"true"`
	VKYCUserCommsDelayPublisher        *cfg.SqsPublisher
	VKYCUserCommsPublisher             *cfg.SqsPublisher
	VKYCUserCommsSubscriber            *cfg.SqsSubscriber `dynamic:"true"`
	VkycCallCompletedEventPublisher    *cfg.SnsPublisher
	QueueCKYCSearchPublisher           *cfg.SqsPublisher
	QueueCKYCDownloadPublisher         *cfg.SqsPublisher
	QueueVKYCUpdatePublisher           *cfg.SqsPublisher
	EKYCSuccessPublisher               *cfg.SqsPublisher
	BKYCUpdateEventPublisher           *cfg.SnsPublisher
	QueueCKYCSearchSubscriber          *cfg.SqsSubscriber `dynamic:"true"`
	QueueCKYCDownloadSubscriber        *cfg.SqsSubscriber `dynamic:"true"`
	QueueAccountStateUpdateSubscriber  *cfg.SqsSubscriber `dynamic:"true"`
	Delay                              *Delay
	Aws                                *Aws
	Secrets                            *cfg.Secrets
	Values                             *Values
	Flags                              *Flags `dynamic:"true"`
	RudderStack                        *RudderBroker
	SecureLogging                      *Logging
	VKYCNotification                   *VKYCNotification
	Sentry                             *cfg.Sentry
	VKYC                               *VKYC `dynamic:"true" ,quest:"component,area:Onboarding"`
	RateLimit                          *RateLimit
	RedisOptions                       *cfg.RedisOptions
	UserRedisOptions                   *cfg.RedisOptions
	// publishes packet to update time till when the user is to blocked on exceeding pan validation rate limit
	PANValidationRateLimitUpdatePublisher *cfg.SqsPublisher
	EKYCDowntime                          *EKYCDowntime `dynamic:"true"`
	// subscriber on onboarding completion to remind user to do VKYC
	ProcessVKYCOnboardingCompleteEventSubscriber *cfg.SqsSubscriber `dynamic:"true"`
	ProcessVKYCTransactionEventSubscriber        *cfg.SqsSubscriber `dynamic:"true"`
	Tracing                                      *cfg.Tracing
	Profiling                                    *cfg.Profiling
	QuestRedisOptions                            *cfg.RedisOptions
	QuestSdk                                     *sdkconfig.Config `dynamic:"true"`
	KycDb                                        *cfg.DB
	UqudoPassportTamperingConfig                 *UqudoPassportTampering `dynamic:"true"`
	ExtractedDocumentsExpiryConfig               *ExtractedDocumentsExpiryConfig
	NrBucketName                                 string `iam:"s3-readwrite"`
	FeatureReleaseConfig                         *releaseConfig.FeatureReleaseConfig `dynamic:"true"`
}

type ExtractedDocumentsExpiryConfig struct {
	NRKYCDataValidity time.Duration
}

type Application struct {
	Environment   string
	Name          string
	IsSecureRedis bool
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Aws struct {
	Endpoint string
	Region   string
	Sqs      *Sqs
	S3       *S3
}

type Sqs struct {
	QueueNames map[string]string
}

type S3 struct {
	BucketNames BucketName
}

type BucketName struct {
	BucketLiveness string `iam:"s3-readwrite"`
	BucketUser     string `iam:"s3-readwrite"`
}

type Flags struct {
	// EnableCKYCDataValidation controls if we want to validate
	// customer name and phone number in CKYC Download response
	EnableCKYCDataValidation bool `dynamic:"true"`
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
	// SkipCKYCDownload ends CKYC process after search and skips CKYC data download.
	SkipCKYCDownload bool `dynamic:"true"`
	// This flag controls how the passport back image is sent to Karza for OCR
	// By default, the image is sent as a PDF base64.
	// When set to true, the image will be sent in its original format (e.g., JPEG/PNG)
	SendPassportBackAsImageToKarza bool `dynamic:"true"`
}

type Values struct {
	// Time we can keep CKYC Data for before purging the data.
	CKYCDataValidity time.Duration
	// Time we can keep EKYC Data for before purging the data.
	EKYCDataValidity time.Duration
	// Time we can keep EKYC Data for a CKYC L, S or O user before purging the data.
	EKYCDataValidityForLSO time.Duration
	// Time we can keep EKYC Data for a CKYC O type user before purging the data.
	EKYCDataValidityForOnboardingCKYCTypeO time.Duration
	// Batch size of KYC vendor data purge job
	PurgeJobBatchSize int
	// FM Threshold to be used for CKYC
	CKYCFMThreshold float32
	// days after which dl issue date is allowed
	MinDLIssueDays int
	// Time we can keep EKYC Data for before purging the data.
	BKYCDataValidity time.Duration
	// Validity for EKYC Data after account creation
	EKYCDataValidtyAfterAccountCreation time.Duration

	MinimumAgeForKYC int
}

type RudderBroker struct {
	Host          string
	Key           string
	IntervalInSec time.Duration
	BatchSize     int
	Verbose       bool
}

type Logging struct {
	EnablePartnerLog bool
	PartnerLogPath   string
	MaxSizeInMBs     int // megabytes
	MaxBackups       int // There will be MaxBackups + 1 total files
}

type Delay struct {
	BookSlot     time.Duration
	HolidayEvent time.Duration
}

type VKYCNotification struct {
	CallScheduled         *NotificationParams
	ReScheduleCall        *NotificationParams
	DocumentsUnderReview  *NotificationParams
	DocumentsApproved     *NotificationParams
	StartLiveCall         *NotificationParams
	BookCall              *NotificationParams
	ScheduledCallReminder *NotificationParams
	ScheduledCallMissed   *NotificationParams
	CallFailed            *NotificationParams
	CallFailedByAuditor   *NotificationParams
	VKYCAvailable         *NotificationParams
}

type NotificationParams struct {
	// to disable a notification if needed
	IsEnabled bool
	Title     string
	Body      string
	IconUrl   string
	Type      string
}

type VKYC struct {
	EnableReAttemptVKYCNudge           bool `dynamic:"true"`
	EnableReAttemptVKYCHomeBottomSheet bool `dynamic:"true"`
	ReAttemptVkycStartHour             int  `dynamic:"true"`
	ReAttemptVkycEndHour               int  `dynamic:"true"`
	VkycStartHour                      int  `dynamic:"true"`
	// end hour is exclusive
	VkycEndHour int `dynamic:"true"`
	// redo ekyc if last ekyc attempt was successful more than PerformEkycAfter duration ago
	PerformEkycAfter time.Duration
	// max priority list size
	MaxVKYCPriorityListSize    int                    `dynamic:"true"`
	VKYCPriorityListElementTTL time.Duration          `dynamic:"true"`
	EnableVKYCPriortyFlow      bool                   `dynamic:"true"`
	VKYCPriorityUsersList      *VKYCPriorityUsersList `dynamic:"true"`
	// contains time interval (start timestamp-end timestamp inclusive)YYYY-MM-DD HH:MM:SS/YYYY-MM-DD HH:MM:SS format and value as % users allowed
	VKYCUserAllowedDateConstraint map[string]int `dynamic:"true"`
	// contains time interval in format HH:MM-HH:MM(start time-end time inclusive) and value as % users allowed
	VKYCUserAllowedDailyTimeIntervalConstraint map[string]int `dynamic:"true"`
	// instruction page skip option timer in seconds
	InstructionPageSkipOptionTime int32 `dynamic:"true"`
	// landing page skip option timer in seconds
	LandingPageSkipOptionTime       int32    `dynamic:"true"`
	UnsupportedWebviewDeviceModels  []string `dynamic:"true"`
	UnsupportedCustomerDeviceModels []string `dynamic:"true"`
	// enable new vkyc error screen
	AgentWaitingOverlay    *AgentWaitingOverlay `dynamic:"true" ,quest:"component:AgentWaitingOverlay"`
	EnableVKYCScheduleFlow *app.FeatureConfig   `dynamic:"true"`
	// enable call scheduling for VKYC V2 flow (https://monorail.pointz.in/p/fi-app/issues/detail?id=90797)
	EnableCallScheduleForVKYCV2Flow bool                                  `dynamic:"true"`
	EnableVKYCDemandManagement      *app.FeatureConfig                    `dynamic:"true"`
	FederalSFTPEPANUploadPath       string                                `dynamic:"true"`
	EnableUploadEpanFile            bool                                  `dynamic:"true"`
	FederalSFTPEPANUploadPrefix     string                                `dynamic:"true"`
	RetryEPANDuration               time.Duration                         `dynamic:"true"`
	ABFeatureReleaseConfig          *releaseConfig.ABFeatureReleaseConfig `dynamic:"true"`
	MaxRetryForAgentRejected        int                                   `dynamic:"true"`
	EnablePanCapturePreOnboarding   bool                                  `dynamic:"true"`
	EnableConfirmDetailsState       *app.FeatureConfig                    `dynamic:"true"`
	EnableNRConfirmDetailsState     *app.FeatureConfig                    `dynamic:"true"`
	// reattempt vkyc roll out percentage
	ReAttemptVKYCRolloutPercentage int                `dynamic:"true"`
	EnableEpanInstructionScreen    *app.FeatureConfig `dynamic:"true"`
	EnableCallQualityCheckStage    *app.FeatureConfig `dynamic:"true"`
	VKYCCallQuality                *VKYCCallQuality   `dynamic:"true"`
	EnableApprovedNudgeDismissal   *app.FeatureConfig `dynamic:"true"`
	EnableEPANInNRFlow             *app.FeatureConfig `dynamic:"true"`
	// ExpectedUserWaitingTimeInNRVKYC is the expected waiting time for the user to get connected to an agent
	ExpectedUserWaitingTimeInNRVKYC time.Duration `dynamic:"true"`
	// NRVKYCStartHour specifies the start hour(24 hr format) in IST for NR VKYC agent availability.
	// If a user attempts calls before this hour, they should see an out of business hours message.
	NRVKYCStartHour int `dynamic:"true"`
	// NRVKYCEndHour specifies the end hour(24 hr format) in IST for NR VKYC agent availability.
	// If a user attempts calls at or after this hour, they should see an out of business hours message.
	NRVKYCEndHour int `dynamic:"true"`
	// Feature to enable revamped vkyc v2 flow
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=96997
	EnableVKYCFlowV2 *app.FeatureConfig `dynamic:"true"`
	// Feature to enable revamped vkyc v2 flow
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=90797
	EnableVKYCFlowV2ForHome *app.FeatureConfig `dynamic:"true"`
	EnableCallInitiation    *CallInitiation    `dynamic:"true"`
	// Feature to enable VKYC benefit screen with comparison module
	// https://monorail.pointz.in/p/fi-app/issues/detail?id=90797
	EnableVKYCBenefitScreenV2 bool   `dynamic:"true" ,quest:"variable"`
	VKYCFaqCategoryId         string `dynamic:"true"`
	// LocationMatchThresholdInKM is maximum allowed distance (in kilometers) between the entered communication
	// address and the user's live location (latitude/longitude) for a successful match.
	LocationMatchThresholdInKM int `dynamic:"true"`
}

type RateLimit struct {
	PANValidationRetryLimit      int
	PANValidationRedisExpiryMins int
}

type EKYCDowntime struct {
	// time ranges for ekyc downtime
	// time format "2006-01-02 15:04:05.000" (time zone IST)
	EKYCDowntimeFrom    string `dynamic:"true"`
	EKYCDowntimeTo      string `dynamic:"true"`
	EKYCDowntimeMessage string `dynamic:"true"`
}

type VKYCPriorityUsersList struct {
	// to enable priority queue for O type user
	OTypeUser bool `dynamic:"true"`
	// to enable priority queue for salaried user
	SalariedUser bool `dynamic:"true"`
	// to enable priority queue for user in their last N days
	LastNDaysUser bool `dynamic:"true"`
}

type AgentWaitingOverlay struct {
	Disable bool `dynamic:"true" ,quest:"variable"`
}

type CallInitiation struct {
	Enable            *app.FeatureConfig `dynamic:"true"`
	RolloutPercentage int                `dynamic:"true"`
}

type VKYCCallQuality struct {
	CheckDurationInMillis          time.Duration `dynamic:"true"`
	SampleDurationInMillis         time.Duration `dynamic:"true"`
	NoiseLevelThresholdAndroid     int           `dynamic:"true"`
	ImageLuminanceThresholdAndroid int           `dynamic:"true"`
	NoiseLevelThresholdIOS         int           `dynamic:"true"`
	ImageLuminanceThresholdIOS     int           `dynamic:"true"`
}

type UqudoPassportTampering struct {
	EnableTamperCheck        bool `dynamic:"true"`
	ScreenDetectionThreshold int  `dynamic:"true"` // Range : 0-100, higher the value higher the tampering
	PrintDetectionThreshold  int  `dynamic:"true"` // Range : 0-100, higher the value higher the tampering
	PhotoTamperingThreshold  int  `dynamic:"true"` // Range : 0-100, higher the value higher the tampering
}
