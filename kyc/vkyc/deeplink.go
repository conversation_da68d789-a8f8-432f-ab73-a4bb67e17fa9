package vkyc

import (
	"context"
	"strconv"

	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/anypb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	goutils "github.com/epifi/be-common/pkg/go_utils"
	vkycDlPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/vkyc"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/analytics"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	fecommon "github.com/epifi/gamma/frontend/pkg/common"
	"github.com/epifi/gamma/frontend/pkg/featureflags"
	"github.com/epifi/gamma/kyc/config/genconf"
	"github.com/epifi/gamma/kyc/vkyc/common"
	"github.com/epifi/gamma/kyc/vkyc/events"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	vkycPkg "github.com/epifi/gamma/pkg/vkyc"

	timestamp "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/gamma/api/frontend/deeplink"
	vkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/vkyc"
)

func getCallScheduleScreen(entryPoint vkycPb.EntryPoint, startTime *timestamp.Timestamp, endTime *timestamp.Timestamp) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_VKYC_INSTRUCTIONS,
		ScreenOptions: &deeplink.Deeplink_VkycInstructionsScreenOptions{
			VkycInstructionsScreenOptions: &deeplink.VKYCInstructionsScreenOptions{
				Icon: &commontypes.VisualElement{
					Asset: &commontypes.VisualElement_Image_{
						Image: &commontypes.VisualElement_Image{
							Source: &commontypes.VisualElement_Image_Url{
								Url: insPageIcon,
							},
							ImageType: commontypes.ImageType_PNG,
							Properties: &commontypes.VisualElementProperties{
								Width:  100,
								Height: 100,
							},
						},
					},
				},
				Title:    insPageTitle,
				Subtitle: insPageSubtitle,
				Blocks: []*deeplink.LandingPageBlock{
					{
						Icon:                 insPanBlockIcon,
						Text:                 insPanBlockTitle,
						BackgroundColorHex:   insBlockBackgroundColor,
						LandingPageBlockType: deeplink.LandingPageBlockType_LANDING_PAGE_BLOCK_TYPE_FULL,
					},
					{
						Icon:                 insNoOneAroundBlockIcon,
						Text:                 insNoOneAroundBlockTitle,
						BackgroundColorHex:   insBlockBackgroundColor,
						LandingPageBlockType: deeplink.LandingPageBlockType_LANDING_PAGE_BLOCK_TYPE_FULL,
					},
				},
				ScheduleBlock:  getScheduleBlock(startTime, endTime),
				Ctas:           getSchedulePageCta(entryPoint),
				BgColorHex:     insPageBgColor,
				IsScheduleFlow: true,
			},
		},
	}
}

func getTimeFormat(startTime *timestamp.Timestamp) string {
	return startTime.AsTime().In(datetime.IST).Format("January 02 at 3:04 PM")
}

func getSchedulePageCta(entryPoint vkycPb.EntryPoint) []*deeplink.Cta {
	if vkyc.IsDuringOnbEntryPoint(entryPoint) {
		return nil
	}
	if entryPoint == vkycPb.EntryPoint_ENTRY_POINT_ONBOARDING {
		return []*deeplink.Cta{
			{
				Type: deeplink.Cta_CUSTOM,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_SKIP_ONBOARDING_STAGE_API,
					ScreenOptions: &deeplink.Deeplink_SkipOnboardingStageApiOption{
						SkipOnboardingStageApiOption: &deeplink.SkipOnboardingStageApiOption{
							Stage: onboardingPb.OnboardingStage_OPTIONAL_VKYC.String(),
						},
					},
				},
				Text:         "Go to home",
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
		}
	}
	return []*deeplink.Cta{
		{
			Type: deeplink.Cta_CUSTOM,
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_GET_NEXT_ONBOARDING_ACTION_API,
			},
			Text:         "Go to home",
			DisplayTheme: deeplink.Cta_PRIMARY,
		},
	}
}

func getScheduleBlock(startTime *timestamp.Timestamp, endTime *timestamp.Timestamp) *deeplink.ScheduleBlock {
	return &deeplink.ScheduleBlock{
		BgColor: insScheduleBlockBgColor,
		Icon: &commontypes.VisualElement{
			Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{
						Url: insPageScheduleIcon,
					},
					ImageType: commontypes.ImageType_PNG,
					Properties: &commontypes.VisualElementProperties{
						Width:  28,
						Height: 28,
					},
				},
			},
		},
		Time: &commontypes.Text{
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: getTimeFormat(startTime),
			},
			FontColor: insScheduleBlockTextColor,
		},
		EventDescription: insScheduleEventDes,
		EventTitle:       insScheduleEventTitle,
		StartTime:        startTime,
		EndTime:          endTime,
	}
}

// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=1037-34490&t=ERJNldSFFmoZzJhT-4
func getVKYCIntroScreenV2Deeplink(ctx context.Context, entryPointStr string, conf *genconf.Config, vkycNextAvailableAt string, actorId string, releaseEvaluator release.IEvaluator) *deeplink.Deeplink {
	entryPoint := goutils.Enum(entryPointStr, vkycPb.EntryPoint_value, vkycPb.EntryPoint_ENTRY_POINT_UNSPECIFIED)
	isVkycFlowV2Enabled := common.IsVKYCFlowV2Enabled(ctx, entryPoint, conf)
	// Sending nil deeplink if error or VKYC flow V2 is not enabled
	// frontend client will handle nil check and return old deeplink as fallback
	if !isVkycFlowV2Enabled {
		return nil
	}

	mainContentSection := &sections.VerticalListSection{
		Components: []*components.Component{
			// header bar
			{
				Content: GetAnyWithoutError(&sections.HorizontalListSection{
					VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
					HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_SPACE_BETWEEN,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: properties.GetContainerProperty().
									WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
									WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
									WithPadding(0, 18, 0, 18),
							},
						},
					},
					Components: []*components.Component{
						// left image
						{
							Content: GetAnyWithoutError(ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/savingsAccountClosure/back-icon.png", 28, 28))),
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: GetAnyWithoutError(&deeplink.Cta{
												Type: deeplink.Cta_CANCEL,
											}),
										},
									},
									AnalyticsEvent: &analytics.AnalyticsEvent{
										EventName: events.EventVkycBenefitScreenClicked,
										Properties: map[string]string{
											events.PropertyEntryPoint:    entryPoint.String(),
											events.PropertyScreenVariant: getScreenVariant(ctx, entryPoint, conf),
											events.PropertyViewName:      "back_icon",
										},
									},
								},
							},
						},
						// right image
						{
							Content: GetAnyWithoutError(ui.NewITC().WithLeftVisualElement(commontypes.GetVisualElementFromUrlHeightAndWidth("https://epifi-icons.pointz.in/preapprovedloan/grey_question_info_icon.png", 28, 28))),
							InteractionBehaviors: []*behaviors.InteractionBehavior{
								{
									Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
										OnClickBehavior: &behaviors.OnClickBehavior{
											Action: GetAnyWithoutError(&deeplink.Deeplink{
												Screen: deeplink.Screen_FAQ_CATEGORY,
												ScreenOptions: &deeplink.Deeplink_FaqCategoryOptions{
													FaqCategoryOptions: &deeplink.FaqCategoryOptions{
														CategoryId: conf.VKYC().VKYCFaqCategoryId(),
													},
												},
											}),
										},
									},
									AnalyticsEvent: &analytics.AnalyticsEvent{
										EventName: events.EventVkycBenefitScreenClicked,
										Properties: map[string]string{
											events.PropertyEntryPoint:    entryPoint.String(),
											events.PropertyScreenVariant: getScreenVariant(ctx, entryPoint, conf),
											events.PropertyViewName:      "help_icon",
										},
									},
								},
							},
						},
					},
				}),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_L)),
			},
			{
				Content: GetAnyWithoutError(ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("VERIFICATION PENDING", "#C0723D", commontypes.FontStyle_SUBTITLE_S)).
					WithContainerBackgroundColor("#FBF3E6").WithContainerPadding(7, 12, 7, 12).WithContainerCornerRadius(12)),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_M)),
			},
			{
				Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Complete your Video KYC & unlock all benefits", "#313234", commontypes.FontStyle_HEADLINE_XL)),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_S)),
			},
			{
				Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Take a 3-min video call with our partner bank’s Relationship Manager", "#8D8D8D", commontypes.FontStyle_BODY_S)),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_L)),
			},
			{
				Content: GetAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(getVKYCBenefitScreenImageUrl(ctx, entryPoint, conf, actorId, releaseEvaluator), 380, 350)),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_XXXL)),
			},
		},
		HorizontalAlignment: sections.VerticalListSection_HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY,
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: properties.GetContainerProperty().
						WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
						WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
				},
			},
		},
		IsScrollable: true,
	}
	footorCtaSection := &sections.VerticalListSection{
		Components: []*components.Component{
			{
				Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("8 lakh+ Fi users finished their V-KYC", "#648E4D", commontypes.FontStyle_SUBTITLE_XS)),
			},
			{
				Content: GetAnyWithoutError(components.NewSpacer().WithSpacingValue(components.Spacing_SPACING_XS)),
			},
			// Button
			{
				Content: GetAnyWithoutError(&sections.HorizontalListSection{
					VerticalAlignment:     sections.HorizontalListSection_VERTICAL_ALIGNMENT_CENTER_VERTICALLY,
					HorizontalArrangement: sections.HorizontalListSection_HORIZONTAL_ARRANGEMENT_CENTER,
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: properties.GetContainerProperty().
									WithHeight(properties.Size_Dimension_DIMENSION_TYPE_WRAP, 0).
									WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
									WithBgColor(widget.GetBlockBackgroundColour("#00B899")).
									WithPadding(12, 12, 12, 12).
									WithAllCornerRadii(20, 20, 20, 20),
							},
						},
					},
					Components: []*components.Component{
						{
							Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle("Complete Video KYC", "#FFFFFF", commontypes.FontStyle_BUTTON_M)),
						},
					},
					InteractionBehaviors: []*behaviors.InteractionBehavior{
						{
							Behavior: &behaviors.InteractionBehavior_OnClickBehavior{
								OnClickBehavior: &behaviors.OnClickBehavior{
									Action: GetAnyWithoutError(vkycPkg.GetVKYCNextActionDeeplink(&deeplink.GetVKYCNextActionApiScreenOptions{
										EntryPoint:      entryPoint.String(),
										ClientLastState: vkycPb.VKYCClientState_VKYC_CLIENT_STATE_INITIATED.String(),
									})),
								},
							},
							AnalyticsEvent: &analytics.AnalyticsEvent{
								EventName: events.EventVkycBenefitScreenClicked,
								Properties: map[string]string{
									events.PropertyEntryPoint:    entryPoint.String(),
									events.PropertyScreenVariant: getScreenVariant(ctx, entryPoint, conf),
									events.PropertyViewName:      "primary_button",
								},
							},
						},
					},
				}),
			},
		},
		VisualProperties: []*properties.VisualProperty{
			{
				Properties: &properties.VisualProperty_ContainerProperty{
					ContainerProperty: properties.GetContainerProperty().WithBgColor(widget.GetBlockBackgroundColour("#EEF2F6")),
				},
			},
		},
	}

	if vkycNextAvailableAt != "" {
		footorCtaSection = &sections.VerticalListSection{
			Components: []*components.Component{
				{
					Content: GetAnyWithoutError(commontypes.GetTextFromStringFontColourFontStyle(vkycNextAvailableAt, "#EE4B2B", commontypes.FontStyle_SUBTITLE_S)),
				},
			},
			VisualProperties: []*properties.VisualProperty{
				{
					Properties: &properties.VisualProperty_ContainerProperty{
						ContainerProperty: properties.GetContainerProperty().WithBgColor(widget.GetBlockBackgroundColour("#EEF2F6")),
					},
				},
			},
		}
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_VKYC_INTRO_V2_SCREEN,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&vkycDlPb.VkycIntroV2ScreenOptions{
			ScreenDetails: &sections.Section{
				Content: &sections.Section_DepthWiseListSection{
					DepthWiseListSection: &sections.DepthWiseListSection{
						VisualProperties: []*properties.VisualProperty{
							{
								Properties: &properties.VisualProperty_ContainerProperty{
									ContainerProperty: properties.GetContainerProperty().
										WithBgColor(widget.GetBlockBackgroundColour("#EEF2F6")).
										WithPadding(16, 0, 16, 24).
										WithHeight(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0).
										WithWidth(properties.Size_Dimension_DIMENSION_TYPE_FILL, 0),
								},
							},
						},
						Alignment: sections.DepthWiseListSection_BOTTOM_CENTER,
						Components: []*components.Component{
							{
								Content: GetAnyWithoutError(mainContentSection),
							},
							{
								Content: GetAnyWithoutError(footorCtaSection),
							},
						},
						LoadBehavior: &behaviors.LifecycleBehavior{
							Behavior: &behaviors.LifecycleBehavior_LoadBehavior{},
							AnalyticsEvent: &analytics.AnalyticsEvent{
								EventName: events.EventVkycBenefitScreenLoaded,
								Properties: map[string]string{
									events.PropertyEntryPoint:    entryPoint.String(),
									events.PropertyScreenVariant: getScreenVariant(ctx, entryPoint, conf),
									"is_non_business_hour":       strconv.FormatBool(vkycNextAvailableAt != ""),
								},
							},
						},
					},
				},
			},
		}),
	}
}

func getVKYCBenefitScreenImageUrl(ctx context.Context, entryPoint vkycPb.EntryPoint, conf *genconf.Config, actorId string, releaseEvaluator release.IEvaluator) string {
	isComparisonBenefitScreenEnable := common.IsVKYCBenefitScreenV2Enabled(ctx, entryPoint, conf)
	benefitImg := "https://epifi-icons.pointz.in/vkyc/benefit_screen_2.png"
	if isComparisonBenefitScreenEnable {
		// flag to enable fi-coins to fi-points post migration
		isFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled := featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled(ctx, &featureflags.IsFeatureFiCoinsToFiPointsPostMigrationPhaseEnabledRequest{
			ActorId: actorId,
			ExternalDeps: &fecommon.ExternalDependencies{
				Evaluator: releaseEvaluator,
			},
		})

		if isFeatureFiCoinsToFiPointsPostMigrationPhaseEnabled {
			benefitImg = "https://epifi-icons.pointz.in/vkyc/benefit_screen_3.png"
		} else {
			benefitImg = "https://epifi-icons.pointz.in/vkyc/benefit_screen_1.png"
		}
	}
	return benefitImg
}

func getScreenVariant(ctx context.Context, entryPoint vkycPb.EntryPoint, conf *genconf.Config) string {
	isComparisonBenefitScreenEnable := common.IsVKYCBenefitScreenV2Enabled(ctx, entryPoint, conf)
	if isComparisonBenefitScreenEnable {
		return "comparison"
	}
	return "default"
}

func GetAnyWithoutError(msg proto.Message) *anypb.Any {
	res, _ := anypb.New(msg)
	return res
}
