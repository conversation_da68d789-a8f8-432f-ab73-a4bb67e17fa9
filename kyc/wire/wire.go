//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
package wire

import (
	"context"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	awsSqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/google/wire"

	awsConfig "github.com/epifi/be-common/pkg/aws/v2/config"

	"github.com/epifi/gamma/api/kyc/docs"
	kycUqudoPb "github.com/epifi/gamma/api/kyc/uqudo"
	"github.com/epifi/gamma/api/omegle"
	"github.com/epifi/gamma/api/omegle/matcher"
	"github.com/epifi/gamma/api/user/onboarding"
	vgUqudoPb "github.com/epifi/gamma/api/vendorgateway/kyc/uqudo"
	"github.com/epifi/gamma/api/vendorgateway/ocr"
	kycAgentDao "github.com/epifi/gamma/kyc/agent/dao"
	kycDocsDao "github.com/epifi/gamma/kyc/docs/dao"

	docExtFactory "github.com/epifi/gamma/kyc/docs/doc_extractor_factory"
	docExtProcessor "github.com/epifi/gamma/kyc/docs/doc_extractor_factory/processor"
	"github.com/epifi/gamma/kyc/vkyc/ack/factory"
	"github.com/epifi/gamma/kyc/vkyc/ack/factory/ack_manager"
	userPkg "github.com/epifi/gamma/pkg/user"

	"github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/queue"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/bankcust"
	casbinPb "github.com/epifi/gamma/api/casbin"
	tcPb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	"github.com/epifi/gamma/api/employment"
	kyc2 "github.com/epifi/gamma/api/kyc"
	vkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	panPb "github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/salaryprogram"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	location2 "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/ckyc"
	"github.com/epifi/gamma/api/vendorgateway/dl"
	ekycPb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	vgIdfcPb "github.com/epifi/gamma/api/vendorgateway/idfc"
	"github.com/epifi/gamma/api/vendorgateway/idvalidate"
	vgIdfcVkycPb "github.com/epifi/gamma/api/vendorgateway/kyc/vkyc/idfc"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/pan"
	pb "github.com/epifi/gamma/api/vendorgateway/vkyc"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	"github.com/epifi/gamma/kyc"
	"github.com/epifi/gamma/kyc/agent"
	agentDao "github.com/epifi/gamma/kyc/agent/dao"
	"github.com/epifi/gamma/kyc/config"
	"github.com/epifi/gamma/kyc/config/genconf"
	"github.com/epifi/gamma/kyc/dao"
	"github.com/epifi/gamma/kyc/data_validator"
	"github.com/epifi/gamma/kyc/developer"
	"github.com/epifi/gamma/kyc/developer/processor"
	kycDocs "github.com/epifi/gamma/kyc/docs"
	kycUqudo "github.com/epifi/gamma/kyc/uqudo"
	daoV2 "github.com/epifi/gamma/kyc/v2/dao"
	kycProc "github.com/epifi/gamma/kyc/v2/processor"
	"github.com/epifi/gamma/kyc/vkyc"
	"github.com/epifi/gamma/kyc/vkyc/consumers"
	vkycDao "github.com/epifi/gamma/kyc/vkyc/dao"
	"github.com/epifi/gamma/kyc/vkyc/notification"
	"github.com/epifi/gamma/kyc/vkyc/nudges"
	"github.com/epifi/gamma/kyc/vkyc/serviceprovider"
	"github.com/epifi/gamma/kyc/vkyc/serviceprovider/karza"
	"github.com/epifi/gamma/kyc/vkyc/statemachine"
	"github.com/epifi/gamma/kyc/vkyc/statemachine/actions"
	"github.com/epifi/gamma/kyc/vkyc/stateprocessor"
	vkycPriority "github.com/epifi/gamma/kyc/vkyc/vkyc_priority"
	wireTypes "github.com/epifi/gamma/kyc/wire/types"
	"github.com/epifi/gamma/pkg/changefeed"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	"github.com/epifi/gamma/pkg/persistentqueue"
)

// config: {"s3Client": "Aws().S3.BucketNames.BucketLiveness"}
func InitializeService(ctx context.Context, kycConf *config.Config, ckycClient ckyc.CKycClient, nameCheckClient namecheck.UNNameCheckClient,
	conf *genconf.Config, s3Client wireTypes.LivenessS3Client, broker events.Broker, dlClient dl.DLClient,
	idValidate idvalidate.IdValidateClient, vkycClient vkycpb.VKYCClient, ocrClient inhouseocr.OcrClient, dbV2 types.EpifiCRDB,
	bcClient bankcust.BankCustomerServiceClient, userClient user.UsersClient, db types.KycPGDB, vgIdfcClient vgIdfcPb.IdfcClient,
	vgIdfcVkycClient vgIdfcVkycPb.IdfcClient, panClient panPb.PanClient, bkycUpdateEventPublisher wireTypes.BKYCUpdateEventPublisher,
	vgEkycClient ekycPb.EKYCClient, authClient authPb.AuthClient, actorClient actorPb.ActorClient, userGroupClient userGroupPb.GroupClient, vgPanClient pan.PANClient) (*kyc.KYCService, error) {
	wire.Build(
		newSQSClient,
		getPublishersMap,
		wireTypes.LivenessS3ClientProvider,
		getEkycSuccessDelayPub,
		types.EpifiCRDBGormDBProvider,
		kyc.NewKYCService,
		datetime.WireDefaultTimeSet,
		dao.KYCDaoWireSet,
		kycProc.NewFederalEKYCStruct,
		kycProc.IdfcVkycWireSet,
		daoV2.KycAttemptsWireSet,
		daoV2.KYCDataWireSet,
		data_validator.WireSet,
		data_validator.NewEmptyAddressChecker,
		data_validator.NewDOBChecker,
		data_validator.NewEmptyUIDChecker,
		data_validator.NewInhouseNameChecker,
		data_validator.NewMinorAgeChecker,
		data_validator.NewEmptyPhotoChecker,
		data_validator.NewAadhaarMobileValidationChecker,
		data_validator.NewNameDobValidationChecker,
		data_validator.NewMinimumAgeValidationChecker,
		data_validator.NewPanAadhaarValidationChecker,
		data_validator.NewFederalPanValidationChecker,
	)
	return &kyc.KYCService{}, nil
}

func InitializeDevKycService(ctx context.Context, db types.EpifiCRDB, kycNonResidentPGDB wireTypes.KycNonResidentPGDB, conf *config.Config, redisClient types.KYCRedisStore,
	actorClient actorPb.ActorClient, userClient user.UsersClient, genConf *genconf.Config,
	kycClient kyc2.KycClient, spClient salaryprogram.SalaryProgramClient, vkycClient vkycpb.VKYCClient, bcClient bankcust.BankCustomerServiceClient,
	pgdb types.KycPGDB) *developer.DevKycService {
	wire.Build(
		dao.KYCDaoWireSet,
		kycDocsDao.ExtractedDocumentDaoWireSet,
		storageV2.TxnExecutorWireSet,
		changefeed.ChangefeedWireSet,
		kycAgentDao.KycAgentWireSet,
		developer.NewDevKycService,
		developer.NewDevFactory,
		processor.NewKycSummaryEntity,
		processor.NewVKYCSummaryEntity,
		processor.NewExtractedDocumentsEntity,
		processor.NewKycAgentEntity,
		vkycPriority.NewVKYCPriorityCacheImpl,
		vkycPriority.NewVKYCPriorityImpl,
		processor.NewVKYCPriorityUsersEntity,
		types.EpifiCRDBGormDBProvider,
		types.NewKYCRedisClient,
		vkycDao.WireSet,
		ExtractedDocumentsConfigProvider,
		wire.NewSet(vkycDao.NewVKYCCallScheduleCRDB, wire.Bind(new(vkycDao.VKYCCallScheduleDao), new(*vkycDao.VKYCCallScheduleCRDB))),
	)
	return &developer.DevKycService{}
}

func InitializeVKYCService(ctx context.Context, dbV2 types.EpifiCRDB, broker events.Broker, vkycAgentUpdateDelaySqsPublisher consumers.VkycAgentUpdateDelayPublisher, vkycCallCompletedPublisherType wireTypes.VkycCallCompletedEventPublisher,
	refreshCallStatusPublisher actions.RefreshCallStatusPublisher, conf *genconf.Config, delayPublisher wireTypes.VKYCUserCommsDelayPublisher, publisher wireTypes.VKYCUserCommsPublisher,
	actorClient actorPb.ActorClient, userClient user.UsersClient, kycClient kyc2.KycClient,
	redisClient types.KYCRedisStore, spClient salaryprogram.SalaryProgramClient, locationClient location.LocationClient, bcClient bankcust.BankCustomerServiceClient, empFeClient employment.EmploymentFeClient,
	savingClient savingsPb.SavingsClient, userLocationClient location2.LocationClient, inAppTargetedCommsClient tcPb.InAppTargetedCommsClient,
	kycConf *config.Config, vkycClient vkycpb.VKYCClient, panClient panPb.PanClient, employmentClient employment.EmploymentClient, userIntelClient userintel.UserIntelServiceClient, vkycVgClient pb.VkycClient, accountBalanceClient accountBalancePb.BalanceClient,
	ekycClient ekycPb.EKYCClient, onbClient onboarding.OnboardingClient, vgPanClient pan.PANClient, userGroupClient userGroupPb.GroupClient, vkycRueidisCacheStorage wireTypes.VKYCRueidisCacheStorage,
	omegleClient omegle.OmegleClient, docExtractionClient docs.DocExtractionClient, matcherClient matcher.MatcherClient) (*vkyc.Service, error) {
	wire.Build(
		wire.NewSet(vkycDao.NewVKYCKarzaCustomerInfo, wire.Bind(new(vkycDao.VKYCKarzaCustomerInfoDao), new(*vkycDao.VKYCKarzaCustomerInfoCRDB))),
		wire.NewSet(vkycDao.NewVKYCKarzaCallInfoDao, wire.Bind(new(vkycDao.VKYCKarzaCallInfoDao), new(*vkycDao.VKYCKarzaCallInfoCRDB))),
		wire.NewSet(vkycDao.NewVKYCSummaryDao, wire.Bind(new(vkycDao.VKYCSummaryDao), new(*vkycDao.VKYCSummaryCRDB))),
		wire.NewSet(vkycDao.NewVKYCKarzaCallHistoryDao, wire.Bind(new(vkycDao.VKYCKarzaCallHistoryDao), new(*vkycDao.VKYCKarzaCallHistoryCRDB))),
		wire.NewSet(vkycDao.NewVKYCAttemptDao, wire.Bind(new(vkycDao.VKYCAttemptDao), new(*vkycDao.VKYCAttemptCRDB))),
		wire.NewSet(vkycDao.NewVKYCCallScheduleCRDB, wire.Bind(new(vkycDao.VKYCCallScheduleDao), new(*vkycDao.VKYCCallScheduleCRDB))),
		stateprocessor.NewInitiatedState,
		stateprocessor.NewInstructionsState,
		stateprocessor.NewEPANSelectionState,
		stateprocessor.NewEPANEntryState,
		stateprocessor.NewPANEvaluationState,
		stateprocessor.NewEKYCState,
		stateprocessor.NewValidateAndStartCallState,
		stateprocessor.NewRegisterUserState,
		stateprocessor.NewCheckAgentAvailabilityState,
		stateprocessor.NewCallInitiationState,
		stateprocessor.NewEmploymentUpdateState,
		stateprocessor.NewPanEntryState,
		stateprocessor.NewConfirmDetailsState,
		stateprocessor.NewCallQualityCheck,
		stateprocessor.NewNRInstructionsState,
		stateprocessor.NewRegisterNRUserState,
		stateprocessor.NewNRValidateAndStartCallState,
		stateprocessor.NewNREPANEntryState,
		stateprocessor.NewNREPANSelectionState,
		stateprocessor.NewNRUpdateApplicantDetails,
		stateprocessor.NewSGInstructionsState,
		stateprocessor.NewSGValidateAndStartCallState,
		nudges.NewProfileMiddleWidget,
		nudges.NewHomePopup,
		nudges.NewHomeProfileExtension,
		nudges.NewHomeTopBanner,
		nudges.NewProfileTopBanner,
		stateprocessor.NewUpdateCallInfoState,
		actions.NewAttemptActions,
		actions.NewSummaryActions,
		actions.NewServiceProviderActions,
		statemachine.NewStateProcessor,
		idgen.NewClock,
		idgen.WireSet,
		vkyc.NewService,
		wire.NewSet(vkycDao.NewVKYCNotificationCRDB, wire.Bind(new(vkycDao.VKYCNotificationDao), new(*vkycDao.VKYCNotificationCRDB))),
		vkycPriority.NewVKYCPriorityCacheImpl,
		vkycPriority.NewVKYCPriorityImpl,
		types.NewKYCRedisClient,
		newSQSClient,
		getPublishersMap,
		types.EpifiCRDBGormDBProvider,
		getVendorKarza,
		getNewDoOnce,
		initializeVkycNotificationService,
		karza.NewVkycKarzaServiceProvider,
		vkycServiceProvider,
		initializeVkycCallScheduler,
		wireTypes.VkycCallCompletedEventPublisherProvider,
		consumers.VkycAgentUpdateDelayPublisherProvider,
		datetime.WireDefaultTimeSet,
		factory.AckFactoryWireSet,
		ack_manager.NewVKYCApprovedAckManager,
		userPkg.WireSet,
		release.EvaluatorWireSet,
		FeatureReleaseConfigProvider,
	)
	return &vkyc.Service{}, nil
}

func InitializeKarzaConsumerService(ctx context.Context, dbV2 types.EpifiCRDB, broker events.Broker, redisClient types.KYCRedisStore, kycClient kyc2.KycClient,
	spClient salaryprogram.SalaryProgramClient, vkycCallCompletedPublisherType wireTypes.VkycCallCompletedEventPublisher,
	vkycClient pb.VkycClient, refreshCallStatusPublisher actions.RefreshCallStatusPublisher,
	vkycAgentUpdateDelaySqsPublisher consumers.VkycAgentUpdateDelayPublisher, conf *genconf.Config, kycConf *config.Config,
	delayPublisher wireTypes.VKYCUserCommsDelayPublisher, publisher wireTypes.VKYCUserCommsPublisher, actorClient actorPb.ActorClient, userClient user.UsersClient,
	inAppTargetedCommsClient tcPb.InAppTargetedCommsClient, savingClient savingsPb.SavingsClient, bcClient bankcust.BankCustomerServiceClient, vClient vkycpb.VKYCClient,
	onbClient onboarding.OnboardingClient, vkycRueidisCacheStorage wireTypes.VKYCRueidisCacheStorage) (*consumers.ConsumerService, error) {
	wire.Build(
		newSQSClient,
		getPublishersMap,
		types.EpifiCRDBGormDBProvider,
		types.NewKYCRedisClient,
		initializeVkycCallScheduler,
		actions.NewAttemptActions,
		actions.NewSummaryActions,
		actions.NewServiceProviderActions,
		statemachine.NewStateProcessor,
		consumers.NewConsumerService,
		initializeVkycNotificationService,
		wire.NewSet(vkycDao.NewVKYCNotificationCRDB, wire.Bind(new(vkycDao.VKYCNotificationDao), new(*vkycDao.VKYCNotificationCRDB))),
		karza.NewVkycKarzaServiceProvider,
		vkycServiceProvider,
		wireTypes.VkycCallCompletedEventPublisherProvider,
		consumers.VkycAgentUpdateDelayPublisherProvider,
		datetime.WireDefaultTimeSet,
		vkycDao.WireSet,
		vkycPriority.NewVKYCPriorityCacheImpl,
		vkycPriority.NewVKYCPriorityImpl,
		getVendorKarza,
		dao.KYCDaoWireSet,
	)
	return &consumers.ConsumerService{}, nil
}

func InitializeVkycNotificationConsumer(ctx context.Context, conf *config.Config, dbV2 types.EpifiCRDB, actorClient actorPb.ActorClient, commsClient wireTypes.KycCommsClientWithInterceptors,
	vkycNotificationConfig *genconf.Config, refreshCallStatusPublisher actions.RefreshCallStatusPublisher,
	vkycClient pb.VkycClient, vkycAgentUpdateDelaySqsPublisher consumers.VkycAgentUpdateDelayPublisher, redisClient types.KYCRedisStore, spClient salaryprogram.SalaryProgramClient,
	vkycCallCompletedPublisherType wireTypes.VkycCallCompletedEventPublisher,
	broker events.Broker, userClient user.UsersClient, delayPublisher wireTypes.VKYCUserCommsDelayPublisher, publisher wireTypes.VKYCUserCommsPublisher,
	kycClient kyc2.KycClient, bcClient bankcust.BankCustomerServiceClient, onbClient onboarding.OnboardingClient, vkycRueidisCacheStorage wireTypes.VKYCRueidisCacheStorage) (*notification.ConsumerService, error) {
	wire.Build(
		wireTypes.CommsClientProvider,
		newSQSClient,
		getPublishersMap,
		types.EpifiCRDBGormDBProvider,
		types.NewKYCRedisClient,
		getVkycNotification,
		vkycDao.WireSet,
		actions.NewAttemptActions,
		actions.NewSummaryActions,
		actions.NewServiceProviderActions,
		statemachine.NewStateProcessor,
		notification.NewConsumerService,
		notification.NewVKYCNotificationService,
		karza.NewVkycKarzaServiceProvider,
		vkycServiceProvider,
		initializeVkycCallScheduler,
		wireTypes.VkycCallCompletedEventPublisherProvider,
		consumers.VkycAgentUpdateDelayPublisherProvider,
		datetime.WireDefaultTimeSet,
		getVendorKarza,
		vkycPriority.NewVKYCPriorityCacheImpl,
		vkycPriority.NewVKYCPriorityImpl,
	)
	return &notification.ConsumerService{}, nil
}

func InitialiseKycAgentService(conf *genconf.Config, db types.KycPGDB, actorClient actorPb.ActorClient, casbinClient casbinPb.CasbinClient) *agent.Service {
	wire.Build(
		types.KycPGDBGormDBProvider,
		storageV2.TxnExecutorWireSet,
		changefeed.ChangefeedWireSet,
		agentDao.KycAgentWireSet,
		idgen.NewClock,
		idgen.NewCryptoSeededSource,
		idgen.WireSet,
		agent.NewService,
	)
	return &agent.Service{}
}

func InitialiseKYCDocExtractionService(conf *genconf.Config, db wireTypes.KycNonResidentPGDB, kycUqudoClient kycUqudoPb.UqudoClient,
	vgUqudoClient vgUqudoPb.UqudoClient, ocrClient ocr.OCRClient, onbClient onboarding.OnboardingClient, nrS3Client wireTypes.NrS3Client) *kycDocs.DocExtractionService {
	wire.Build(
		wire.NewSet(docExtFactory.NewDocExtractorFactorySvc, wire.Bind(new(docExtFactory.DocExtractorFactory), new(*docExtFactory.DocExtractorFactorySvc))),
		wire.NewSet(kycDocsDao.NewExtractedDocumentsDB, wire.Bind(new(kycDocsDao.ExtractedDocumentDao), new(*kycDocsDao.ExtractedDocumentsDB))),
		ExtractedDocumentsConfigProvider,
		docExtProcessor.NewUqudoEmiratesIdProcessor,
		docExtProcessor.NewPassportProcessor,
		docExtProcessor.NewUqudoPassportFrontProcessor,
		docExtProcessor.NewKarzaPassportBackProcessor,
		docExtProcessor.NewUqudoQatarIdProcessorProcessor,
		kycDocs.NewDocExtractionService,
	)
	return &kycDocs.DocExtractionService{}
}

func ExtractedDocumentsConfigProvider(conf *genconf.Config) *config.ExtractedDocumentsExpiryConfig {
	return conf.ExtractedDocumentsExpiryConfig()
}

func InitialiseKYCUqudoService(vgUqudoClient vgUqudoPb.UqudoClient) *kycUqudo.Service {
	wire.Build(
		kycUqudo.NewService,
	)

	return &kycUqudo.Service{}
}

func initializeVkycCallScheduler(crdb types.EpifiCRDB) serviceprovider.CallScheduler {
	db := types.EpifiCRDBGormDBProvider(crdb)
	vkycCallScheduleCRDB := vkycDao.NewVKYCCallScheduleCRDB(db)
	vkycCallScheduler := serviceprovider.NewVKYCCallScheduler(vkycCallScheduleCRDB, getVendorKarza())
	return vkycCallScheduler
}

func initializeVkycNotificationService(conf *config.Config, delayPublisher wireTypes.VKYCUserCommsDelayPublisher, publisher wireTypes.VKYCUserCommsPublisher) *notification.VKYCNotificationService {
	dp := wireTypes.VKYCUserCommsDelayPublisherProvider(delayPublisher)
	pub := wireTypes.VKYCUserCommsPublisherProvider(publisher)
	vkycNotificationService := notification.NewVKYCNotificationService(dp, pub, conf.VKYCNotification)
	return vkycNotificationService
}

func getPublishersMap(ctx context.Context, conf *config.Config, sqsClient *awsSqs.Client) map[string]queue.Publisher {
	// Set up publishers
	publishers := map[string]queue.Publisher{
		config.QUEUE_CKYC_SEARCH:   newPublisher(ctx, sqsClient, conf.QueueCKYCSearchPublisher),
		config.QUEUE_CKYC_DOWNLOAD: newPublisher(ctx, sqsClient, conf.QueueCKYCDownloadPublisher),
		config.QUEUE_VKYC_UPDATE:   newPublisher(ctx, sqsClient, conf.QueueVKYCUpdatePublisher),
	}
	return publishers
}

func vkycServiceProvider(sp *karza.VkycKarzaServiceProvider) serviceprovider.ServiceProvider {
	return sp
}

func newPublisher(ctx context.Context, sqsClient *awsSqs.Client, pubConf *cfg.SqsPublisher) queue.Publisher {
	pub, _ := sqs.NewPublisherWithConfig(ctx, pubConf, sqsClient, nil)
	return pub
}

func newDelayPublisher(ctx context.Context, sqsClient *awsSqs.Client, pubConf *cfg.SqsPublisher) queue.DelayPublisher {
	pub, _ := sqs.NewPublisherWithConfig(ctx, pubConf, sqsClient, nil)
	return pub
}

func getEkycSuccessDelayPub(ctx context.Context, conf *config.Config, sqsClient *awsSqs.Client) queue.DelayPublisher {
	ekycSuccessDelayPub := newDelayPublisher(ctx, sqsClient, conf.EKYCSuccessPublisher)
	return ekycSuccessDelayPub
}

func getNewDoOnce(crdb types.EpifiCRDB) once.DoOnce {
	doOnceMgr := once.NewDoOnce(crdb)
	return doOnceMgr
}

func getPersistentQueue(crdb types.EpifiCRDB) persistentqueue.PersistentQueue {
	pQueue := persistentqueue.NewPersistentQueue(crdb)
	return pQueue
}

func getVendorKarza() commonvgpb.Vendor {
	return commonvgpb.Vendor_KARZA
}

func getVkycNotification(conf *config.Config) *config.VKYCNotification {
	return conf.VKYCNotification
}

func newSQSClient(ctx context.Context, conf *config.Config) (*awsSqs.Client, error) {
	awsSess, err := awsConfig.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		return nil, err
	}
	return sqs.InitSQSClient(awsSess), nil
}

func FeatureReleaseConfigProvider(conf *genconf.Config) *releaseGenConf.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}
