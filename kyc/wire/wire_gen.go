// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/epifi/be-common/api/vendorgateway"
	config2 "github.com/epifi/be-common/pkg/aws/v2/config"
	sqs2 "github.com/epifi/be-common/pkg/aws/v2/sqs"
	"github.com/epifi/be-common/pkg/cfg"
	types2 "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/casbin"
	"github.com/epifi/gamma/api/comms/inapptargetedcomms"
	"github.com/epifi/gamma/api/employment"
	kyc2 "github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/docs"
	"github.com/epifi/gamma/api/kyc/uqudo"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/omegle"
	"github.com/epifi/gamma/api/omegle/matcher"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	location2 "github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/userintel"
	"github.com/epifi/gamma/api/vendorgateway/ckyc"
	"github.com/epifi/gamma/api/vendorgateway/dl"
	"github.com/epifi/gamma/api/vendorgateway/ekyc"
	"github.com/epifi/gamma/api/vendorgateway/idfc"
	"github.com/epifi/gamma/api/vendorgateway/idvalidate"
	uqudo2 "github.com/epifi/gamma/api/vendorgateway/kyc/uqudo"
	idfc2 "github.com/epifi/gamma/api/vendorgateway/kyc/vkyc/idfc"
	"github.com/epifi/gamma/api/vendorgateway/namecheck"
	"github.com/epifi/gamma/api/vendorgateway/ocr"
	pan2 "github.com/epifi/gamma/api/vendorgateway/pan"
	vkyc2 "github.com/epifi/gamma/api/vendorgateway/vkyc"
	"github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	"github.com/epifi/gamma/kyc"
	"github.com/epifi/gamma/kyc/agent"
	dao5 "github.com/epifi/gamma/kyc/agent/dao"
	"github.com/epifi/gamma/kyc/config"
	"github.com/epifi/gamma/kyc/config/genconf"
	"github.com/epifi/gamma/kyc/dao"
	"github.com/epifi/gamma/kyc/data_validator"
	"github.com/epifi/gamma/kyc/developer"
	processor2 "github.com/epifi/gamma/kyc/developer/processor"
	docs2 "github.com/epifi/gamma/kyc/docs"
	dao4 "github.com/epifi/gamma/kyc/docs/dao"
	"github.com/epifi/gamma/kyc/docs/doc_extractor_factory"
	processor3 "github.com/epifi/gamma/kyc/docs/doc_extractor_factory/processor"
	uqudo3 "github.com/epifi/gamma/kyc/uqudo"
	dao2 "github.com/epifi/gamma/kyc/v2/dao"
	"github.com/epifi/gamma/kyc/v2/processor"
	vkyc3 "github.com/epifi/gamma/kyc/vkyc"
	"github.com/epifi/gamma/kyc/vkyc/ack/factory"
	"github.com/epifi/gamma/kyc/vkyc/ack/factory/ack_manager"
	"github.com/epifi/gamma/kyc/vkyc/consumers"
	dao3 "github.com/epifi/gamma/kyc/vkyc/dao"
	"github.com/epifi/gamma/kyc/vkyc/notification"
	"github.com/epifi/gamma/kyc/vkyc/nudges"
	"github.com/epifi/gamma/kyc/vkyc/serviceprovider"
	"github.com/epifi/gamma/kyc/vkyc/serviceprovider/karza"
	"github.com/epifi/gamma/kyc/vkyc/statemachine"
	"github.com/epifi/gamma/kyc/vkyc/statemachine/actions"
	"github.com/epifi/gamma/kyc/vkyc/stateprocessor"
	"github.com/epifi/gamma/kyc/vkyc/vkyc_priority"
	"github.com/epifi/gamma/kyc/wire/types"
	"github.com/epifi/gamma/pkg/changefeed"
	"github.com/epifi/gamma/pkg/feature/release"
	genconf2 "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	"github.com/epifi/gamma/pkg/persistentqueue"
	user2 "github.com/epifi/gamma/pkg/user"
)

// Injectors from wire.go:

// config: {"s3Client": "Aws().S3.BucketNames.BucketLiveness"}
func InitializeService(ctx context.Context, kycConf *config.Config, ckycClient ckyc.CKycClient, nameCheckClient namecheck.UNNameCheckClient, conf *genconf.Config, s3Client types.LivenessS3Client, broker events.Broker, dlClient dl.DLClient, idValidate idvalidate.IdValidateClient, vkycClient vkyc.VKYCClient, ocrClient inhouseocr.OcrClient, dbV2 types2.EpifiCRDB, bcClient bankcust.BankCustomerServiceClient, userClient user.UsersClient, db types2.KycPGDB, vgIdfcClient idfc.IdfcClient, vgIdfcVkycClient idfc2.IdfcClient, panClient pan.PanClient, bkycUpdateEventPublisher types.BKYCUpdateEventPublisher, vgEkycClient ekyc.EKYCClient, authClient auth.AuthClient, actorClient actor.ActorClient, userGroupClient group.GroupClient, vgPanClient pan2.PANClient) (*kyc.KYCService, error) {
	client, err := newSQSClient(ctx, kycConf)
	if err != nil {
		return nil, err
	}
	v := getPublishersMap(ctx, kycConf, client)
	gormDB := types2.EpifiCRDBGormDBProvider(dbV2)
	kycAttemptDB := dao.NewKYCAttemptDao(gormDB)
	kycSummaryCRDB := dao.NewKYCSummaryDao(gormDB)
	kycVendorDataDB := dao.NewKYCVendorDataDao(gormDB)
	s3S3Client := types.LivenessS3ClientProvider(s3Client)
	delayPublisher := getEkycSuccessDelayPub(ctx, kycConf, client)
	federalEKYCStruct := processor.NewFederalEKYCStruct()
	kycAttemptDaoPGDB := dao2.NewKycAttemptDaoPGDB(db)
	idfcVkyc := processor.NewIdfcVkyc(kycAttemptDaoPGDB, vgIdfcClient, vgIdfcVkycClient)
	kycDataDaoPGDB := dao2.NewKycDataDaoPGDB(db)
	defaultTime := datetime.NewDefaultTime()
	dobChecker := data_validator.NewDOBChecker(userClient, defaultTime)
	inhouseNameChecker := data_validator.NewInhouseNameChecker(nameCheckClient)
	emptyUIDChecker := data_validator.NewEmptyUIDChecker()
	emptyAddressChecker := data_validator.NewEmptyAddressChecker()
	emptyPhotoChecker := data_validator.NewEmptyPhotoChecker()
	minorAgeChecker := data_validator.NewMinorAgeChecker()
	aadhaarMobileValidationChecker := data_validator.NewAadhaarMobileValidationChecker(vgEkycClient, userClient, authClient)
	nameDobValidationChecker := data_validator.NewNameDobValidationChecker(vgEkycClient, kycAttemptDB, bcClient)
	minimumAgeValidationChecker := data_validator.NewMinimumAgeValidationChecker(userClient, conf, defaultTime)
	panAadhaarValidationChecker := data_validator.NewPanAadhaarValidationChecker(userClient, vgPanClient, vgEkycClient, kycAttemptDB)
	federalPanValidationChecker := data_validator.NewFederalPanValidationChecker(userClient, vgPanClient)
	kycDataValidator := data_validator.NewProc(dobChecker, inhouseNameChecker, emptyUIDChecker, emptyAddressChecker, emptyPhotoChecker, minorAgeChecker, aadhaarMobileValidationChecker, nameDobValidationChecker, minimumAgeValidationChecker, panAadhaarValidationChecker, federalPanValidationChecker)
	kycService := kyc.NewKYCService(v, ckycClient, nameCheckClient, kycAttemptDB, kycSummaryCRDB, kycVendorDataDB, s3S3Client, conf, broker, dlClient, idValidate, vkycClient, ocrClient, delayPublisher, bcClient, userClient, federalEKYCStruct, idfcVkyc, kycAttemptDaoPGDB, kycDataDaoPGDB, defaultTime, panClient, bkycUpdateEventPublisher, kycDataValidator, actorClient, userGroupClient)
	return kycService, nil
}

func InitializeDevKycService(ctx context.Context, db types2.EpifiCRDB, kycNonResidentPGDB types.KycNonResidentPGDB, conf *config.Config, redisClient types2.KYCRedisStore, actorClient actor.ActorClient, userClient user.UsersClient, genConf *genconf.Config, kycClient kyc2.KycClient, spClient salaryprogram.SalaryProgramClient, vkycClient vkyc.VKYCClient, bcClient bankcust.BankCustomerServiceClient, pgdb types2.KycPGDB) *developer.DevKycService {
	gormDB := types2.EpifiCRDBGormDBProvider(db)
	kycSummaryCRDB := dao.NewKYCSummaryDao(gormDB)
	kycAttemptDB := dao.NewKYCAttemptDao(gormDB)
	kycVendorDataDB := dao.NewKYCVendorDataDao(gormDB)
	kycSummaryEntity := processor2.NewKycSummaryEntity(kycSummaryCRDB, kycAttemptDB, kycVendorDataDB)
	vkycSummaryCRDB := dao3.NewVKYCSummaryDao(gormDB)
	vkycAttemptCRDB := dao3.NewVKYCAttemptDao(gormDB)
	vkycKarzaCallInfoCRDB := dao3.NewVKYCKarzaCallInfoDao(gormDB)
	vkycKarzaCallHistoryCRDB := dao3.NewVKYCKarzaCallHistoryDao(gormDB)
	vkycKarzaCustomerInfoCRDB := dao3.NewVKYCKarzaCustomerInfo(gormDB)
	vkycCallScheduleCRDB := dao3.NewVKYCCallScheduleCRDB(gormDB)
	vkycSummaryEntity := processor2.NewVKYCSummaryEntity(vkycSummaryCRDB, vkycAttemptCRDB, vkycKarzaCallInfoCRDB, vkycKarzaCallHistoryCRDB, vkycKarzaCustomerInfoCRDB, vkycCallScheduleCRDB)
	client := types2.NewKYCRedisClient(redisClient)
	ivkycPriorityStore := vkyc_priority.NewVKYCPriorityCacheImpl(genConf, client)
	ivkycPriority := vkyc_priority.NewVKYCPriorityImpl(genConf, ivkycPriorityStore, vkycSummaryCRDB, actorClient, userClient, kycClient, spClient, bcClient)
	vkycPriorityUsersEntity := processor2.NewVKYCPriorityUsersEntity(ivkycPriority)
	extractedDocumentsExpiryConfig := ExtractedDocumentsConfigProvider(genConf)
	extractedDocumentsDB := dao4.NewExtractedDocumentsDB(kycNonResidentPGDB, extractedDocumentsExpiryConfig)
	extractedDocumentsEntity := processor2.NewExtractedDocumentsEntity(extractedDocumentsDB)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	changeFeed := changefeed.NewChangefeed(gormDB)
	kycAgentDaoPGDB := dao5.NewKycAgentDaoPGDB(pgdb, gormTxnExecutor, changeFeed)
	kycAgentEntity := processor2.NewKycAgentEntity(kycAgentDaoPGDB)
	devFactory := developer.NewDevFactory(kycSummaryEntity, vkycSummaryEntity, vkycPriorityUsersEntity, extractedDocumentsEntity, kycAgentEntity)
	devKycService := developer.NewDevKycService(devFactory)
	return devKycService
}

func InitializeVKYCService(ctx context.Context, dbV2 types2.EpifiCRDB, broker events.Broker, vkycAgentUpdateDelaySqsPublisher consumers.VkycAgentUpdateDelayPublisher, vkycCallCompletedPublisherType types.VkycCallCompletedEventPublisher, refreshCallStatusPublisher actions.RefreshCallStatusPublisher, conf *genconf.Config, delayPublisher types.VKYCUserCommsDelayPublisher, publisher types.VKYCUserCommsPublisher, actorClient actor.ActorClient, userClient user.UsersClient, kycClient kyc2.KycClient, redisClient types2.KYCRedisStore, spClient salaryprogram.SalaryProgramClient, locationClient location.LocationClient, bcClient bankcust.BankCustomerServiceClient, empFeClient employment.EmploymentFeClient, savingClient savings.SavingsClient, userLocationClient location2.LocationClient, inAppTargetedCommsClient inapptargetedcomms.InAppTargetedCommsClient, kycConf *config.Config, vkycClient vkyc.VKYCClient, panClient pan.PanClient, employmentClient employment.EmploymentClient, userIntelClient userintel.UserIntelServiceClient, vkycVgClient vkyc2.VkycClient, accountBalanceClient balance.BalanceClient, ekycClient ekyc.EKYCClient, onbClient onboarding.OnboardingClient, vgPanClient pan2.PANClient, userGroupClient group.GroupClient, vkycRueidisCacheStorage types.VKYCRueidisCacheStorage, omegleClient omegle.OmegleClient, docExtractionClient docs.DocExtractionClient, matcherClient matcher.MatcherClient) (*vkyc3.Service, error) {
	db := types2.EpifiCRDBGormDBProvider(dbV2)
	vkycKarzaCustomerInfoCRDB := dao3.NewVKYCKarzaCustomerInfo(db)
	vkycKarzaCallInfoCRDB := dao3.NewVKYCKarzaCallInfoDao(db)
	callScheduler := initializeVkycCallScheduler(dbV2)
	vendor := getVendorKarza()
	queueDelayPublisher := consumers.VkycAgentUpdateDelayPublisherProvider(vkycAgentUpdateDelaySqsPublisher)
	vkycSummaryCRDB := dao3.NewVKYCSummaryDao(db)
	vkycKarzaCallHistoryCRDB := dao3.NewVKYCKarzaCallHistoryDao(db)
	vkycAttemptCRDB := dao3.NewVKYCAttemptDao(db)
	client := types2.NewKYCRedisClient(redisClient)
	ivkycPriorityStore := vkyc_priority.NewVKYCPriorityCacheImpl(conf, client)
	ivkycPriority := vkyc_priority.NewVKYCPriorityImpl(conf, ivkycPriorityStore, vkycSummaryCRDB, actorClient, userClient, kycClient, spClient, bcClient)
	defaultTime := datetime.NewDefaultTime()
	queuePublisher := types.VkycCallCompletedEventPublisherProvider(vkycCallCompletedPublisherType)
	vkycKarzaServiceProvider := karza.NewVkycKarzaServiceProvider(vkycVgClient, vkycKarzaCustomerInfoCRDB, vkycKarzaCallInfoCRDB, callScheduler, vendor, queueDelayPublisher, conf, vkycSummaryCRDB, vkycKarzaCallHistoryCRDB, vkycAttemptCRDB, broker, ivkycPriority, defaultTime, queuePublisher, onbClient)
	serviceProvider := vkycServiceProvider(vkycKarzaServiceProvider)
	attemptActions := actions.NewAttemptActions(vkycAttemptCRDB, serviceProvider, conf, actorClient, userClient)
	sqsClient, err := newSQSClient(ctx, kycConf)
	if err != nil {
		return nil, err
	}
	v := getPublishersMap(ctx, kycConf, sqsClient)
	summaryActions := actions.NewSummaryActions(vkycSummaryCRDB, vkycAttemptCRDB, serviceProvider, v, broker, conf, actorClient, userClient, vkycRueidisCacheStorage)
	serviceProviderActions := actions.NewServiceProviderActions(serviceProvider, refreshCallStatusPublisher, conf, broker, actorClient, userClient)
	stateProcessor := statemachine.NewStateProcessor(attemptActions, summaryActions, serviceProviderActions, broker, conf)
	vkycCallScheduleCRDB := dao3.NewVKYCCallScheduleCRDB(db)
	vkycNotificationService := initializeVkycNotificationService(kycConf, delayPublisher, publisher)
	vkycNotificationCRDB := dao3.NewVKYCNotificationCRDB(db)
	doOnce := getNewDoOnce(dbV2)
	initiatedState := stateprocessor.NewInitiatedState(kycConf)
	instructionsState := stateprocessor.NewInstructionsState(kycConf, vkycClient, conf, vkycSummaryCRDB, panClient, userClient, v, bcClient, vendor)
	ekycState := stateprocessor.NewEKYCState(conf, vkycClient, kycClient)
	validateAndStartCallState := stateprocessor.NewValidateAndStartCallState(serviceProvider, vkycSummaryCRDB, vkycAttemptCRDB, vkycKarzaCallInfoCRDB, conf, userClient)
	profileMiddleWidget := nudges.NewProfileMiddleWidget(conf, vkycClient)
	homePopup := nudges.NewHomePopup(inAppTargetedCommsClient, conf)
	profileTopBanner := nudges.NewProfileTopBanner(actorClient, savingClient, accountBalanceClient, conf)
	homeTopBanner := nudges.NewHomeTopBanner(panClient, actorClient, savingClient, accountBalanceClient, conf)
	homeProfileExtension := nudges.NewHomeProfileExtension(actorClient, userClient, userGroupClient, conf)
	registerUserState := stateprocessor.NewRegisterUserState(vkycClient)
	updateCallInfoState := stateprocessor.NewUpdateCallInfoState()
	epanSelectionState := stateprocessor.NewEPANSelectionState(kycConf, vkycSummaryCRDB, vendor, panClient, conf)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	epanEntryState := stateprocessor.NewEPANEntryState(kycConf, vkycSummaryCRDB, vendor, domainIdGenerator, panClient, conf, vkycClient)
	panEvaluationState := stateprocessor.NewPANEvaluationState(userClient, conf, bcClient, panClient, vkycSummaryCRDB, vendor, actorClient, userGroupClient)
	checkAgentAvailabilityState := stateprocessor.NewCheckAgentAvailabilityState(vkycClient, conf)
	callInitiationState := stateprocessor.NewCallInitiationState()
	employmentUpdateState := stateprocessor.NewEmploymentUpdateState(employmentClient)
	panEntry := stateprocessor.NewPanEntryState(conf, bcClient, domainIdGenerator, vkycSummaryCRDB, vendor, actorClient, userClient, userGroupClient, panClient)
	userAttributesFetcherImpl := user2.NewUserAttributesFetcherImpl(userClient)
	confirmDetailsState := stateprocessor.NewConfirmDetailsState(conf, vkycClient, empFeClient, onbClient, userAttributesFetcherImpl, kycClient, employmentClient, userClient)
	callQualityCheck := stateprocessor.NewCallQualityCheck(conf, actorClient, userClient, userGroupClient)
	nrInstructionsState := stateprocessor.NewNRInstructionsState(conf, defaultTime, userClient)
	vkycApprovedAckManager := ack_manager.NewVKYCApprovedAckManager(vkycRueidisCacheStorage)
	ackFactoryService := factory.NewAckFactoryService(vkycApprovedAckManager)
	registerNRUserState := stateprocessor.NewRegisterNRUserState(omegleClient, onbClient, employmentClient, userClient)
	nrValidateAndStartCallState := stateprocessor.NewNRValidateAndStartCallState(onbClient, conf)
	nrepanEntryState := stateprocessor.NewNREPANEntryState(domainIdGenerator, panClient, conf)
	nrepanSelectionState := stateprocessor.NewNREPANSelectionState(kycConf, panClient, conf)
	nrUpdateApplicantDetails := stateprocessor.NewNRUpdateApplicantDetails(omegleClient, panClient, onbClient)
	sgInstructionsState := stateprocessor.NewSGInstructionsState(conf, defaultTime)
	sgValidateAndStartCallState := stateprocessor.NewSGValidateAndStartCallState(onbClient, conf)
	featureReleaseConfig := FeatureReleaseConfigProvider(conf)
	appVersionConstraint := release.NewAppVersionConstraint()
	stickinessConstraint := release.NewStickinessConstraint()
	userGroupConstraint := release.NewUserGroupConstraint(actorClient, userClient, userGroupClient)
	constraintFactoryImpl := release.NewConstraintFactoryImpl(appVersionConstraint, stickinessConstraint, userGroupConstraint)
	evaluator := release.NewEvaluator(featureReleaseConfig, constraintFactoryImpl)
	service := vkyc3.NewService(conf, serviceProvider, vkycSummaryCRDB, vkycAttemptCRDB, vendor, stateProcessor, broker, vkycKarzaCallInfoCRDB, vkycKarzaCallHistoryCRDB, vkycKarzaCustomerInfoCRDB, vkycCallScheduleCRDB, vkycNotificationService, vkycNotificationCRDB, doOnce, kycClient, ivkycPriority, locationClient, userClient, actorClient, bcClient, empFeClient, savingClient, userLocationClient, inAppTargetedCommsClient, initiatedState, instructionsState, ekycState, validateAndStartCallState, profileMiddleWidget, homePopup, profileTopBanner, homeTopBanner, homeProfileExtension, registerUserState, updateCallInfoState, panClient, employmentClient, epanSelectionState, epanEntryState, panEvaluationState, vkycRueidisCacheStorage, checkAgentAvailabilityState, userIntelClient, callInitiationState, employmentUpdateState, panEntry, vgPanClient, onbClient, confirmDetailsState, callQualityCheck, nrInstructionsState, ackFactoryService, registerNRUserState, nrValidateAndStartCallState, nrepanEntryState, userAttributesFetcherImpl, nrepanSelectionState, nrUpdateApplicantDetails, sgInstructionsState, sgValidateAndStartCallState, userGroupClient, matcherClient, evaluator)
	return service, nil
}

func InitializeKarzaConsumerService(ctx context.Context, dbV2 types2.EpifiCRDB, broker events.Broker, redisClient types2.KYCRedisStore, kycClient kyc2.KycClient, spClient salaryprogram.SalaryProgramClient, vkycCallCompletedPublisherType types.VkycCallCompletedEventPublisher, vkycClient vkyc2.VkycClient, refreshCallStatusPublisher actions.RefreshCallStatusPublisher, vkycAgentUpdateDelaySqsPublisher consumers.VkycAgentUpdateDelayPublisher, conf *genconf.Config, kycConf *config.Config, delayPublisher types.VKYCUserCommsDelayPublisher, publisher types.VKYCUserCommsPublisher, actorClient actor.ActorClient, userClient user.UsersClient, inAppTargetedCommsClient inapptargetedcomms.InAppTargetedCommsClient, savingClient savings.SavingsClient, bcClient bankcust.BankCustomerServiceClient, vClient vkyc.VKYCClient, onbClient onboarding.OnboardingClient, vkycRueidisCacheStorage types.VKYCRueidisCacheStorage) (*consumers.ConsumerService, error) {
	db := types2.EpifiCRDBGormDBProvider(dbV2)
	vkycAttemptCRDB := dao3.NewVKYCAttemptDao(db)
	vkycKarzaCustomerInfoCRDB := dao3.NewVKYCKarzaCustomerInfo(db)
	vkycKarzaCallInfoCRDB := dao3.NewVKYCKarzaCallInfoDao(db)
	callScheduler := initializeVkycCallScheduler(dbV2)
	vendor := getVendorKarza()
	queueDelayPublisher := consumers.VkycAgentUpdateDelayPublisherProvider(vkycAgentUpdateDelaySqsPublisher)
	vkycSummaryCRDB := dao3.NewVKYCSummaryDao(db)
	vkycKarzaCallHistoryCRDB := dao3.NewVKYCKarzaCallHistoryDao(db)
	client := types2.NewKYCRedisClient(redisClient)
	ivkycPriorityStore := vkyc_priority.NewVKYCPriorityCacheImpl(conf, client)
	ivkycPriority := vkyc_priority.NewVKYCPriorityImpl(conf, ivkycPriorityStore, vkycSummaryCRDB, actorClient, userClient, kycClient, spClient, bcClient)
	defaultTime := datetime.NewDefaultTime()
	queuePublisher := types.VkycCallCompletedEventPublisherProvider(vkycCallCompletedPublisherType)
	vkycKarzaServiceProvider := karza.NewVkycKarzaServiceProvider(vkycClient, vkycKarzaCustomerInfoCRDB, vkycKarzaCallInfoCRDB, callScheduler, vendor, queueDelayPublisher, conf, vkycSummaryCRDB, vkycKarzaCallHistoryCRDB, vkycAttemptCRDB, broker, ivkycPriority, defaultTime, queuePublisher, onbClient)
	serviceProvider := vkycServiceProvider(vkycKarzaServiceProvider)
	attemptActions := actions.NewAttemptActions(vkycAttemptCRDB, serviceProvider, conf, actorClient, userClient)
	sqsClient, err := newSQSClient(ctx, kycConf)
	if err != nil {
		return nil, err
	}
	v := getPublishersMap(ctx, kycConf, sqsClient)
	summaryActions := actions.NewSummaryActions(vkycSummaryCRDB, vkycAttemptCRDB, serviceProvider, v, broker, conf, actorClient, userClient, vkycRueidisCacheStorage)
	serviceProviderActions := actions.NewServiceProviderActions(serviceProvider, refreshCallStatusPublisher, conf, broker, actorClient, userClient)
	stateProcessor := statemachine.NewStateProcessor(attemptActions, summaryActions, serviceProviderActions, broker, conf)
	vkycNotificationCRDB := dao3.NewVKYCNotificationCRDB(db)
	vkycNotificationService := initializeVkycNotificationService(kycConf, delayPublisher, publisher)
	kycAttemptDB := dao.NewKYCAttemptDao(db)
	consumerService := consumers.NewConsumerService(stateProcessor, serviceProvider, vkycSummaryCRDB, callScheduler, vkycAgentUpdateDelaySqsPublisher, conf, vkycAttemptCRDB, broker, vkycNotificationCRDB, vkycNotificationService, inAppTargetedCommsClient, savingClient, bcClient, vClient, kycAttemptDB, v, actorClient, vkycKarzaCallHistoryCRDB)
	return consumerService, nil
}

func InitializeVkycNotificationConsumer(ctx context.Context, conf *config.Config, dbV2 types2.EpifiCRDB, actorClient actor.ActorClient, commsClient types.KycCommsClientWithInterceptors, vkycNotificationConfig *genconf.Config, refreshCallStatusPublisher actions.RefreshCallStatusPublisher, vkycClient vkyc2.VkycClient, vkycAgentUpdateDelaySqsPublisher consumers.VkycAgentUpdateDelayPublisher, redisClient types2.KYCRedisStore, spClient salaryprogram.SalaryProgramClient, vkycCallCompletedPublisherType types.VkycCallCompletedEventPublisher, broker events.Broker, userClient user.UsersClient, delayPublisher types.VKYCUserCommsDelayPublisher, publisher types.VKYCUserCommsPublisher, kycClient kyc2.KycClient, bcClient bankcust.BankCustomerServiceClient, onbClient onboarding.OnboardingClient, vkycRueidisCacheStorage types.VKYCRueidisCacheStorage) (*notification.ConsumerService, error) {
	db := types2.EpifiCRDBGormDBProvider(dbV2)
	vkycSummaryCRDB := dao3.NewVKYCSummaryDao(db)
	vkycAttemptCRDB := dao3.NewVKYCAttemptDao(db)
	commsCommsClient := types.CommsClientProvider(commsClient)
	vkycKarzaCustomerInfoCRDB := dao3.NewVKYCKarzaCustomerInfo(db)
	vkycKarzaCallInfoCRDB := dao3.NewVKYCKarzaCallInfoDao(db)
	callScheduler := initializeVkycCallScheduler(dbV2)
	vendor := getVendorKarza()
	queueDelayPublisher := consumers.VkycAgentUpdateDelayPublisherProvider(vkycAgentUpdateDelaySqsPublisher)
	vkycKarzaCallHistoryCRDB := dao3.NewVKYCKarzaCallHistoryDao(db)
	client := types2.NewKYCRedisClient(redisClient)
	ivkycPriorityStore := vkyc_priority.NewVKYCPriorityCacheImpl(vkycNotificationConfig, client)
	ivkycPriority := vkyc_priority.NewVKYCPriorityImpl(vkycNotificationConfig, ivkycPriorityStore, vkycSummaryCRDB, actorClient, userClient, kycClient, spClient, bcClient)
	defaultTime := datetime.NewDefaultTime()
	queuePublisher := types.VkycCallCompletedEventPublisherProvider(vkycCallCompletedPublisherType)
	vkycKarzaServiceProvider := karza.NewVkycKarzaServiceProvider(vkycClient, vkycKarzaCustomerInfoCRDB, vkycKarzaCallInfoCRDB, callScheduler, vendor, queueDelayPublisher, vkycNotificationConfig, vkycSummaryCRDB, vkycKarzaCallHistoryCRDB, vkycAttemptCRDB, broker, ivkycPriority, defaultTime, queuePublisher, onbClient)
	serviceProvider := vkycServiceProvider(vkycKarzaServiceProvider)
	attemptActions := actions.NewAttemptActions(vkycAttemptCRDB, serviceProvider, vkycNotificationConfig, actorClient, userClient)
	sqsClient, err := newSQSClient(ctx, conf)
	if err != nil {
		return nil, err
	}
	v := getPublishersMap(ctx, conf, sqsClient)
	summaryActions := actions.NewSummaryActions(vkycSummaryCRDB, vkycAttemptCRDB, serviceProvider, v, broker, vkycNotificationConfig, actorClient, userClient, vkycRueidisCacheStorage)
	serviceProviderActions := actions.NewServiceProviderActions(serviceProvider, refreshCallStatusPublisher, vkycNotificationConfig, broker, actorClient, userClient)
	stateProcessor := statemachine.NewStateProcessor(attemptActions, summaryActions, serviceProviderActions, broker, vkycNotificationConfig)
	vkycNotification := getVkycNotification(conf)
	vkycNotificationService := notification.NewVKYCNotificationService(queueDelayPublisher, queuePublisher, vkycNotification)
	consumerService := notification.NewConsumerService(vkycSummaryCRDB, vkycAttemptCRDB, commsCommsClient, vkycNotificationConfig, actorClient, serviceProvider, stateProcessor, broker, vkycNotificationService, kycClient, bcClient)
	return consumerService, nil
}

func InitialiseKycAgentService(conf *genconf.Config, db types2.KycPGDB, actorClient actor.ActorClient, casbinClient casbin.CasbinClient) *agent.Service {
	gormDB := types2.KycPGDBGormDBProvider(db)
	gormTxnExecutor := storagev2.NewGormTxnExecutor(gormDB)
	changeFeed := changefeed.NewChangefeed(gormDB)
	kycAgentDaoPGDB := dao5.NewKycAgentDaoPGDB(db, gormTxnExecutor, changeFeed)
	source := idgen.NewCryptoSeededSource()
	runeNumberIdGenerator := idgen.NewNumberIdGenerator(source)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	service := agent.NewService(conf, kycAgentDaoPGDB, actorClient, casbinClient, runeNumberIdGenerator, domainIdGenerator)
	return service
}

func InitialiseKYCDocExtractionService(conf *genconf.Config, db types.KycNonResidentPGDB, kycUqudoClient uqudo.UqudoClient, vgUqudoClient uqudo2.UqudoClient, ocrClient ocr.OCRClient, onbClient onboarding.OnboardingClient, nrS3Client types.NrS3Client) *docs2.DocExtractionService {
	extractedDocumentsExpiryConfig := ExtractedDocumentsConfigProvider(conf)
	extractedDocumentsDB := dao4.NewExtractedDocumentsDB(db, extractedDocumentsExpiryConfig)
	uqudoEmiratesIdProcessor := processor3.NewUqudoEmiratesIdProcessor(conf, kycUqudoClient, vgUqudoClient, extractedDocumentsDB, nrS3Client)
	passportProcessor := processor3.NewPassportProcessor(extractedDocumentsDB, ocrClient)
	uqudoPassportFrontProcessor := processor3.NewUqudoPassportFrontProcessor(kycUqudoClient, vgUqudoClient, extractedDocumentsDB, conf, onbClient, nrS3Client)
	karzaPassportBackProcessor := processor3.NewKarzaPassportBackProcessor(extractedDocumentsDB, ocrClient, onbClient, nrS3Client, conf)
	uqudoQatarIdProcessor := processor3.NewUqudoQatarIdProcessorProcessor(kycUqudoClient, vgUqudoClient, extractedDocumentsDB, conf, onbClient, nrS3Client)
	docExtractorFactorySvc := docExtFactory.NewDocExtractorFactorySvc(uqudoEmiratesIdProcessor, passportProcessor, uqudoPassportFrontProcessor, karzaPassportBackProcessor, uqudoQatarIdProcessor)
	docExtractionService := docs2.NewDocExtractionService(conf, docExtractorFactorySvc, extractedDocumentsDB)
	return docExtractionService
}

func InitialiseKYCUqudoService(vgUqudoClient uqudo2.UqudoClient) *uqudo3.Service {
	service := uqudo3.NewService(vgUqudoClient)
	return service
}

// wire.go:

func ExtractedDocumentsConfigProvider(conf *genconf.Config) *config.ExtractedDocumentsExpiryConfig {
	return conf.ExtractedDocumentsExpiryConfig()
}

func initializeVkycCallScheduler(crdb types2.EpifiCRDB) serviceprovider.CallScheduler {
	db := types2.EpifiCRDBGormDBProvider(crdb)
	vkycCallScheduleCRDB := dao3.NewVKYCCallScheduleCRDB(db)
	vkycCallScheduler := serviceprovider.NewVKYCCallScheduler(vkycCallScheduleCRDB, getVendorKarza())
	return vkycCallScheduler
}

func initializeVkycNotificationService(conf *config.Config, delayPublisher types.VKYCUserCommsDelayPublisher, publisher types.VKYCUserCommsPublisher) *notification.VKYCNotificationService {
	dp := types.VKYCUserCommsDelayPublisherProvider(delayPublisher)
	pub := types.VKYCUserCommsPublisherProvider(publisher)
	vkycNotificationService := notification.NewVKYCNotificationService(dp, pub, conf.VKYCNotification)
	return vkycNotificationService
}

func getPublishersMap(ctx context.Context, conf *config.Config, sqsClient *sqs.Client) map[string]queue.Publisher {

	publishers := map[string]queue.Publisher{config.QUEUE_CKYC_SEARCH: newPublisher(ctx, sqsClient, conf.QueueCKYCSearchPublisher), config.QUEUE_CKYC_DOWNLOAD: newPublisher(ctx, sqsClient, conf.QueueCKYCDownloadPublisher), config.QUEUE_VKYC_UPDATE: newPublisher(ctx, sqsClient, conf.QueueVKYCUpdatePublisher)}
	return publishers
}

func vkycServiceProvider(sp *karza.VkycKarzaServiceProvider) serviceprovider.ServiceProvider {
	return sp
}

func newPublisher(ctx context.Context, sqsClient *sqs.Client, pubConf *cfg.SqsPublisher) queue.Publisher {
	pub, _ := sqs2.NewPublisherWithConfig(ctx, pubConf, sqsClient, nil)
	return pub
}

func newDelayPublisher(ctx context.Context, sqsClient *sqs.Client, pubConf *cfg.SqsPublisher) queue.DelayPublisher {
	pub, _ := sqs2.NewPublisherWithConfig(ctx, pubConf, sqsClient, nil)
	return pub
}

func getEkycSuccessDelayPub(ctx context.Context, conf *config.Config, sqsClient *sqs.Client) queue.DelayPublisher {
	ekycSuccessDelayPub := newDelayPublisher(ctx, sqsClient, conf.EKYCSuccessPublisher)
	return ekycSuccessDelayPub
}

func getNewDoOnce(crdb types2.EpifiCRDB) once.DoOnce {
	doOnceMgr := once.NewDoOnce(crdb)
	return doOnceMgr
}

func getPersistentQueue(crdb types2.EpifiCRDB) persistentqueue.PersistentQueue {
	pQueue := persistentqueue.NewPersistentQueue(crdb)
	return pQueue
}

func getVendorKarza() vendorgateway.Vendor {
	return vendorgateway.Vendor_KARZA
}

func getVkycNotification(conf *config.Config) *config.VKYCNotification {
	return conf.VKYCNotification
}

func newSQSClient(ctx context.Context, conf *config.Config) (*sqs.Client, error) {
	awsSess, err := config2.NewAWSConfig(ctx, conf.Aws.Region, true)
	if err != nil {
		return nil, err
	}
	return sqs2.InitSQSClient(awsSess), nil
}

func FeatureReleaseConfigProvider(conf *genconf.Config) *genconf2.FeatureReleaseConfig {
	return conf.FeatureReleaseConfig()
}
