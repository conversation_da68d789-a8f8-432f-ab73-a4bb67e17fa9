Application:
  Environment: "development"
  Name: "p2pinvestment"

Server:
  Ports:
    GrpcPort: 8110
    GrpcSecurePort: 9526
    HttpPort: 9999

EpifiDb:
  AppName: "p2pinvestment"
  StatementTimeout: 5s
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "p2pinvestment_liquiloans"
  EnableDebug: true
  SSLMode: "disable"

RedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0

AWS:
  Region: "ap-south-1"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"

Profiling:
  StackDriverProfiling:
    ProjectId: "development"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

InvestmentInstrumentEventPublisher:
  QueueName: "investment-event-queue"

DBOperationsPublisher:
  QueueName: "p2investment-db-operations-queue"

ProcrastinatorWorkflowPublisher:
  QueueName: "celestial-initiate-procrastinator-workflow-queue"

DBOperationsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  QueueName: "p2investment-db-operations-queue"
  # Exponential backoff till 17 Minute
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"
