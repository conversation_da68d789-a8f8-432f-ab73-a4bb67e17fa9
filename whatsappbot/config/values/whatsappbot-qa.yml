Application:
  Environment: "qa"
  Name: "whatsappbot"

Server:
  Ports:
    GrpcPort: 8085
    GrpcSecurePort: 9506
    HttpPort: 9999
    HttpPProfPort: 9990

AWS:
  Region: "ap-south-1"

Flags:
  TrimDebugMessageFromStatus: true

WaitlistAccessGrantPublisher:
  QueueName: "qa-waitlist-access-grant-queue"

WhatsappBotSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "qa-whatsapp-bot-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 5
      TimeUnit: "Second"

WaitlistAccessGrantDelay: "30s"

Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true
