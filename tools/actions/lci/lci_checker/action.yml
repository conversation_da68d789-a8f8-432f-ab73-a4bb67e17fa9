name: "LCI stamp checker"
description: "Action to check if LCI stamp is present or not"
inputs:
  services:
    required: true
    description: "Space separated list of service groups for which to run the workflow"
  paths_to_trigger:
    required: true
    description: "Paths for which to trigger the actual workflow run"
  run_test_parallel:
    required: false
    description: "Specify whether to execute the tests in parallel at package level using gotestsum. This will significantly speed up test execution. Make sure that your tests can run parallely without any failures. Not used in this action, added for lci to scrape metadata"
    default: "true"
  lci_stamp_key:
    required: true
    description: "Key in lci stamp to check for skipping the workflow"
  workflow_token:
    required: true
    description: "workflow token generated by peter-murray/workflow-application-token-action"
  checker_type:
    required: true
    description: "type of checker. Valid options: service_test,other"
  enabled:
    required: true
    description: "should action be enabled"
    default: "true"
runs:
  using: composite
  steps:
    - name: Check local ci run - ${{ inputs.services }}
      if: ${{ inputs.enabled == 'true' }}
      id: local_ci_run
      shell: bash
      run: |
        echo_in_green() {
          echo -e "\x1b[32m${1}\x1b[0m"
        }

        echo_in_red() {
          echo -e "\x1b[31m${1}\x1b[0m"
        }

        # Fetch the stamp info for the latest commit
        git_note=$(git notes show "$latest_commit" 2>/dev/null || true)

        # check if git note exist
        if [ -z "$git_note" ]; then
          echo_in_red "[$services]: Could not find lci run stamp. Failing action."
          exit 1
        fi
        stamp_info=$(echo "$git_note" | base64 --decode)
        echo "Stamp info: $stamp_info"
        lci_stamp_key="${{ inputs.lci_stamp_key }}"

        commit_sha=$(echo "$stamp_info" | jq -r .commitSha)
        check_status=$(echo "$stamp_info" | jq -r ".checksStatus.$lci_stamp_key")
        base_branch=$(echo "$stamp_info" | jq -r ".baseBranch")

        if [ "$commit_sha" == "$latest_commit" ]; then
          echo "[$services]: Commit sha matches"
          if [ "$check_status" == "true" ]; then
            if [ "$base_branch" != "$base_ref" ]; then
              echo_in_red "[$services]: base branch of pr and mentioned in stamp do not match. Failing action."
              exit 1
            fi
            echo_in_green "[$services]: Successful check run on local. Passing action."
            exit 0
          else
            echo_in_red "[$services]: Could not find successful check run on local. Failing action"
            exit 1
          fi
        else
          echo_in_red "[$services]: Hashes do not match. Failing action"
          exit 1
        fi
      working-directory: /runner/_work/gamma/gamma/go/src/github.com/epifi/gamma
      env:
        latest_commit: ${{ github.event.pull_request.head.sha }}
        base_ref: ${{ github.event.pull_request.base.ref }}
        services: ${{ inputs.services }}
