---
description: To implement a Dev Action
globs:
alwaysApply: false
---

This rule is used to implement a Dev Action. A Dev Action is internal terminology used at Epifi that allows engineers and operations teams to trigger privileged or diagnostic actions (such as data extraction, log queries, or operational fixes) via <PERSON> (UI tool).

---

## 1. Definition
- Each Dev Action corresponds to a specific operational or debugging task.
- Each action is defined as an enum in the proto enum file (e.g., `DeveloperActions_FETCH_UTRS_FROM_LOG` in `api/cx/developer/actions/enums.pb.go`).
- This file is generated; do **NOT** edit it manually!
- The enum is used to register and route the action in the backend.
- Dev Actions may interact with databases, external APIs, or log stores.

---

## 2. Code Structure

```
cx/
  ├── developer/
  │    ├── actions/
  │    │    ├── processor/
  │    │    │    ├── <domain>/
  │    │    │    │    ├── dev_action_1.go
  │    │    │    │    ├── dev_action_2.go
  │    |    ├── factory.go         # Returns appropriate processor based on Dev Action enum
  │    ├── constants.go       # Stores all constants
  ├── wire/
  │    ├── wire.go            # Dependency injection setup for processors/services
```

- **Processor logic** for each Dev Action is placed in a domain-specific subfolder under `actions/processor/`.
- **Factory** registers and routes actions to the correct processor.
- **Wire** sets up dependencies (e.g., log clients, DAOs).

---

## 3. Code Changes

    1. **Implement the Processor**
        - Select the appropriate Dev Action entity from the proto enum.
        - Examine the corresponding database model and DAO interface to understand its structure and available methods.
        - Create a new processor in the appropriate domain folder under `actions/processor/`.
        - The processor must implement:
        - `FetchParamList(ctx, action, meta) ([]*db_state.ParameterMeta, error)`
            - Returns the input fields required for the action.
        - `ExecuteAction(ctx, action, filters, meta) (string, error)`
            - Executes the action logic (e.g., queries logs, updates DB, calls external API).
            - Returns the result as a JSON string.

    2. **Register the Processor in the Factory**
        - Add the processor as a new field in the `DevActionFactory` struct.
        - Add the processor as a parameter and assignment in the `NewDevActionFactory` constructor.
        - Add a case for the new Dev Action enum in both `getActionParamListImpl` and `getExecuteActionImpl` to return the new processor.
        - Do not modify, reorder, or refactor unrelated fields, parameters, or logic in the factory. Only add what is necessary for the new Dev Action.


    3. **Wire Up Dependencies**
        - If the processor needs new dependencies, add providers in `wire.go`.
        - Add the processor’s constructor to the `wire.Build(...)` list for Dev Actions in `wire.go`. This is required and sufficient for Wire to generate the dependency.
        - Regenerate `wire_gen.go` using:
        ```
        cd service && wire ./wire/wire.go
        ```

4. Build and Iterate -
    1. Find the correct server which runs this gRPC service in `cmd/servers/qa/*/run.gen.go`.
    2. Run the build command from the repo root (not from inside the service directory):
       ```
       make build target={server} env=development
       ```
       This will create the binary in output/{target}/{target}_bin.
    2. Fix any issues and re-iterate the process until the build succeeds.
