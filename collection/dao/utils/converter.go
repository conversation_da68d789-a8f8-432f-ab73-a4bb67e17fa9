package utils

import (
	"fmt"

	"google.golang.org/protobuf/proto"
)

type ProtoConvertable[P proto.Message] interface {
	ToProto() (P, error)
}

// ConvertModelsToProtos converts a list of models to a list of protos using the ToProto method of the model
// e.g: ConvertModelsToProtos[*collectionPb.Allocation](allocationModels) will convert a list of allocation models to
// a list of allocation protos
func ConvertModelsToProtos[P proto.Message, M ProtoConvertable[P]](models []M) ([]P, error) {
	var protos []P
	for _, model := range models {
		p, err := model.ToProto()
		if err != nil {
			return nil, fmt.Errorf("error converting model to proto: %w", err)
		}
		protos = append(protos, p)
	}
	return protos, nil
}
