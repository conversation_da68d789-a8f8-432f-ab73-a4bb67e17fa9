package developer

import (
	"context"

	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/securities/developer"
)

type ParameterFetcher interface {
	FetchParamList(ctx context.Context, entity developer.SecuritiesEntitiy) ([]*cxDsPb.ParameterMeta, error)
}

type DataFetcher interface {
	FetchData(ctx context.Context, entity developer.SecuritiesEntitiy, filters []*cxDsPb.Filter) (string, error)
}
