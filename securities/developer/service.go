package developer

import (
	"context"
	"errors"

	"go.uber.org/zap"
	"gorm.io/gorm"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/api/securities/developer"
)

type SecuritiesDevService struct {
	fac *DevFactory
}

func NewSecuritiesDevService(fac *DevFactory) *SecuritiesDevService {
	return &SecuritiesDevService{
		fac: fac,
	}
}
func (c *SecuritiesDevService) GetEntityList(ctx context.Context, req *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	return &db_state.GetEntityListResponse{
		Status: rpcPb.StatusOk(),
		EntityList: []string{
			developer.SecuritiesEntitiy_SECURITIES_DETAILS.String(),
			developer.SecuritiesEntitiy_SECURITIES_LISTINGS_DETAILS.String(),
		},
	}, nil
}

func (s *SecuritiesDevService) GetParameterList(ctx context.Context, req *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	ent, ok := developer.SecuritiesEntitiy_value[req.GetEntity()]
	if !ok {
		return &db_state.GetParameterListResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	paramFetcher, err := s.fac.getParameterListImpl(developer.SecuritiesEntitiy(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available", zap.Error(err))
		return &db_state.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.SecuritiesEntitiy(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list", zap.Error(err))
		return &db_state.GetParameterListResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &db_state.GetParameterListResponse{
		Status:        rpcPb.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (s *SecuritiesDevService) GetData(ctx context.Context, req *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	ent, ok := developer.SecuritiesEntitiy_value[req.GetEntity()]
	if !ok {
		return &db_state.GetDataResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	dataFetcher, err := s.fac.getDataImpl(developer.SecuritiesEntitiy(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available", zap.Error(err))
		return &db_state.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.SecuritiesEntitiy(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data", zap.Error(err))
		if errors.Is(err, gorm.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &db_state.GetDataResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &db_state.GetDataResponse{Status: rpcPb.StatusInternal()}, nil
	}
	return &db_state.GetDataResponse{
		Status:       rpcPb.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
