package processor

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/securities/developer"

	"github.com/epifi/gamma/api/cx/developer/db_state"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	"github.com/epifi/gamma/securities/catalog/dao"
)

type SecuritiesDetails struct {
	securitiesDao dao.SecuritiesDao
}

func NewSecuritiesDetails(securitiesDao dao.SecuritiesDao) *SecuritiesDetails {
	return &SecuritiesDetails{
		securitiesDao: securitiesDao,
	}
}

func (d *SecuritiesDetails) FetchParamList(ctx context.Context, entity developer.SecuritiesEntitiy) ([]*db_state.ParameterMeta, error) {
	return []*db_state.ParameterMeta{
		{
			Name:            securityIdKey,
			Label:           "Security ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_MANDATORY,
		},
	}, nil
}

func (d *SecuritiesDetails) FetchData(ctx context.Context, entity developer.SecuritiesEntitiy, filters []*db_state.Filter) (string, error) {
	if len(filters) == 0 {
		return "", epifierrors.ErrInvalidArgument
	}

	var securityId string
	for _, filter := range filters {
		switch filter.GetParameterName() {
		case securityIdKey:
			securityId = filter.GetStringValue()
		default:
			return "", fmt.Errorf("invalid filter: %s", filter.GetParameterName())
		}
	}

	if securityId == "" {
		return "", fmt.Errorf("security id is required")
	}

	// Get all fields for the security
	fieldMasks := []catalogPb.SecurityFieldMask{
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_SECURITY_TYPE,
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_NAME,
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR,
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID,
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_LOGO_URL,
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_DETAILS,
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_CREATED_AT,
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_UPDATED_AT,
		catalogPb.SecurityFieldMask_SECURITY_FIELD_MASK_DELETED_AT,
	}

	security, err := d.securitiesDao.GetById(ctx, securityId, fieldMasks)
	if err != nil {
		logger.Error(ctx, "failed to get security details", zap.Error(err), zap.String("security_id", securityId))
		return "", fmt.Errorf("failed to get security details: %w", err)
	}

	// Marshal to JSON
	marshalOptions := protojson.MarshalOptions{
		UseEnumNumbers: false,
		Multiline:      true,
		Indent:         "  ",
	}

	jsonBytes, err := marshalOptions.Marshal(security)
	if err != nil {
		logger.Error(ctx, "failed to marshal security details", zap.Error(err))
		return "", fmt.Errorf("failed to marshal security details: %w", err)
	}

	return string(jsonBytes), nil
}
