package developer

import (
	"fmt"

	"github.com/epifi/gamma/api/securities/developer"
	"github.com/epifi/gamma/securities/developer/processor"
)

type DevFactory struct {
	SecuritiesDetails         *processor.SecuritiesDetails
	SecuritiesListingsDetails *processor.SecuritiesListingsDetails
}

func NewDevFactory(
	securitiesDetails *processor.SecuritiesDetails,
	securitiesListingsDetails *processor.SecuritiesListingsDetails,
) *DevFactory {
	return &DevFactory{
		SecuritiesDetails:         securitiesDetails,
		SecuritiesListingsDetails: securitiesListingsDetails,
	}
}

func (d *DevFactory) getParameterListImpl(entity developer.SecuritiesEntitiy) (ParameterFetcher, error) {
	switch entity {
	case developer.SecuritiesEntitiy_SECURITIES_DETAILS:
		return d.SecuritiesDetails, nil
	case developer.SecuritiesEntitiy_SECURITIES_LISTINGS_DETAILS:
		return d.SecuritiesListingsDetails, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}

func (d *DevFactory) getDataImpl(entity developer.SecuritiesEntitiy) (DataFetcher, error) {
	switch entity {
	case developer.SecuritiesEntitiy_SECURITIES_DETAILS:
		return d.SecuritiesDetails, nil
	case developer.SecuritiesEntitiy_SECURITIES_LISTINGS_DETAILS:
		return d.SecuritiesListingsDetails, nil
	default:
		return nil, fmt.Errorf("no valid implementation found")
	}
}
