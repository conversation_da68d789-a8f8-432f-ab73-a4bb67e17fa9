# This config stores the mapping of the business units Fi has and the service groups owned by them.
# Refer https://github.com/epiFi/gamma/blob/master/pkg/owners/config/config.go for the allowed values in this config.
BusinessUnits:
  growth-and-experience:
    EnggLead: "<EMAIL>"
    OncallUserHandle: "growth-and-exp-oncall"
    OnCallGroupId: "S08LF8GUTQC"
    TeamSlackChannel:
      Name: "growth-and-experience-team"
      Id: "C088X2SD40N"
    AlertSlackChannel:
      Name: "onboarding-alerts"
      Id: "C01NTC58Z35"
    GithubProjectName: "Growth-Experience"
    ServiceGroupsOwned:
      "auth":
        Owners:
          - <EMAIL>
      "alfred":
        Owners:
          - <EMAIL>
      "bankcust":
        Owners:
          - <EMAIL>
      "employment":
        Owners:
          - <EMAIL>
      "inappreferral":
        Owners:
          - <EMAIL>
      "referral":
        Owners:
          - <EMAIL>
      "product":
        Owners:
          - <EMAIL>
      "screener":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "user":
        Owners:
          - <EMAIL>
      "useractions":
        Owners:
          - <EMAIL>
      "userintel":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "kyc":
        Owners:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
      "pan":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "vendordata":
        Owners:
          - <EMAIL>
      "shipment":
        Owners:
          - <EMAIL>
      "accrual":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "casper":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "comms":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "customdelayqueue":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "nudge":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "quest":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
        SlackChannel: "ab-exp-engg"
      "rewards":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "segment":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "dynamicelements":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "cms":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "whatsappbot":
        Owners:
          - "<EMAIL>"
      "actor_activity":
        Owners:
          - "<EMAIL>"
      "cx":
        Owners:
          - "<EMAIL>"
      "inapphelp":
        Owners:
          - "<EMAIL>"

  "lending":
    EnggLead: <EMAIL>
    OncallUserHandle: lendingoncall
    OnCallGroupId: "S05S77W3V2R"
    TeamSlackChannel:
      Name: "personal-loans-tech"
      Id: "C03LMRC4RH9"
    AlertSlackChannel:
      Name: "lending-oncall"
      Id: "C04UQSH435Z"
    GithubProjectName: "Lending"
    ServiceGroupsOwned:
      "collateralmgrtsp":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "bre":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "inhousebre":
        Owners:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
      "preapprovedloan":
        Owners:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
      "creditreportv2":
        Owners:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
      "collection":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "epfo":
        Owners:
          - <EMAIL>
          - <EMAIL>

  "banking":
    EnggLead: <EMAIL>
    OncallUserHandle: banking-on-call
    OnCallGroupId: "S05SBGP6GMC"
    TeamSlackChannel:
      Name: "banking-xfn"
      Id: "C0101Q3TXFF"
    AlertSlackChannel:
      Name: "banking-oncall"
      Id: "C08A6MDH1E3"
    GithubProjectName: "Banking"
    ServiceGroupsOwned:
      "actor":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "merchant":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "docs":
        Owners:
          - <EMAIL>
      "order":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "pay":
        Owners:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
      "recurringpayment":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "upi":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "health_engine":
        Owners:
          - <EMAIL>
      "paymentinstrument":
        Owners:
          - <EMAIL>
          - <EMAIL>
          - <EMAIL>
      "timeline":
        Owners:
          - <EMAIL>
          - <EMAIL>
      "accounts":
        Owners:
          - <EMAIL>
      "parser":
        Owners:
          - <EMAIL>
      "connectedaccount":
        Owners:
          - "<EMAIL>"
      "connectedaccountorder":
        Owners:
          - "<EMAIL>"
      "tiering":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "heartbeat":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "savings":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "salaryprogram":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "card":
        Owners:
          - "<EMAIL>"
      "firefly":
        Owners:
          - "<EMAIL>"
      "tokenizer":
        Owners:
          - "<EMAIL>"


  "wealth":
    EnggLead: "<EMAIL>"
    OncallUserHandle: "wealthdmfoncall"
    OnCallGroupId: "S05713C487K"
    TeamSlackChannel:
      Name: "ana-team"
      Id: "C0623V4V3RS"
    AlertSlackChannel:
      Name: "wealth-oncall"
      Id: "C0303BMGPCY"
    GithubProjectName: "Assets and Analysis"
    ServiceGroupsOwned:
      "goals":
        Owners:
          - "<EMAIL>"
      "deposit":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "investment":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "p2pinvestment":
        Owners:
          - "<EMAIL>"
      "usstocks":
        Owners:
          - "<EMAIL>"
      "fittt":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "rms":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "aml":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "analyser":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "categorizer":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "insights":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "upcomingtransactions":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "search":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "budgeting":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "pinot":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
      "gplace":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"

  "platform":
    EnggLead: "<EMAIL>"
    OncallUserHandle: "platform-oncall"
    OnCallGroupId: "S05SZTGBWCQ"
    TeamSlackChannel:
      Name: "infra-engg"
      Id: "C035BNW5UFL"
    GithubProjectName: "Platform&CX"
    ServiceGroupsOwned:
      "celestial":
        Owners:
          - "<EMAIL>"
        SlackChannel: "celestial"
      "slack_bot":
        Owners:
          - "<EMAIL>"


  "user-risk":
    EnggLead: "<EMAIL>"
    OncallUserHandle: "user-risk-oncall"
    OnCallGroupId: "S05TTN4RA69"
    TeamSlackChannel:
      Name: "risk_engg"
      Id: "C04K68T9GJJ"
    AlertSlackChannel:
      Name: "risk_alerts"
      Id: "C05CHN127SN"
    GithubProjectName: "User Risk"
    ServiceGroupsOwned:
      "risk":
        Owners:
          - "<EMAIL>"
          - "<EMAIL>"
          - "<EMAIL>"

  "infra":
    EnggLead: "<EMAIL>"
    OncallUserHandle: "devops-oncall"
    OnCallGroupId: "S071AKGT6MN"
    GithubProjectName: "Infra"

  "web":
    EnggLead: "<EMAIL>"
    OncallUserHandle: "web-oncall"
    OnCallGroupId: "S05T57LPN82"
    GithubProjectName: "Web"


  # These servers have collective ownership. Hence, no particular email is assigned in this config.
  # Though the empty array is needed so that these entries are parsed & loaded in the config struct.

  "all":
    ServiceGroupsOwned:
      "atlas":
        Owners:
          -
      "frontend":
        Owners:
          -
      "simulator":
        Owners:
          -
      "vendorgateway":
        Owners:
          -
      "vendorgateway-pci":
        Owners:
          -
      "vendormapping":
        Owners:
          -
      "vendornotification":
        Owners:
          -
      "vnotificationgw":
        Owners:
          -
OperationUnits:
  "customer-operations-team":
    "Lead": "<EMAIL>"
    "OncallUserHandle": "cxops"
    "OnCallGroupId": ""
