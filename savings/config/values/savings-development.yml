Application:
  Environment: "development"
  Name: "savings"

RedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 13
  ClientName: savings
  HystrixCommand:
    CommandName: "savings_redis_circuit_breaker_command"
    TemplateName: "Q20"
    OverrideTemplateConfig:
      ExecutionMaxConcurrency: 2500
      ExecutionTimeout: 1s
      RequiredConsecutiveSuccessful: 6
      HalfOpenAttemptsAllowedPerSleepWindow: 10
      ErrorThresholdPercentage: 80

BalanceHistoryRedisOptions:
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 13
  ClientName: savings

SavingsCacheConfig:
  IsCachingEnabled: false
  SavingsIdPrefix: "savings_id_"
  SecondaryIdPrefix: "cache_savings_secondary_id_"
  CacheTTl: "2m"
  EssentialsCacheTTLForNonActiveAccounts: 1h
  EssentialsCacheTTLForActiveAccounts: 168h # 7 days

Server:
  Ports:
    GrpcPort: 8084
    GrpcSecurePort: 9516
    HttpPort: 9883
    HttpPProfPort: 9990
  EnablePoller: true

EpifiDb:
  AppName: "savings"
  StatementTimeout: 5s
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

AWS:
  Region: "ap-south-1"

SavingsCreationPublisher:
  QueueName: "savings-creation-queue"

ThirdPartyAccountSharingPublisher:
  QueueName: "third-party-account-sharing-event-queue"

ThirdPartyAccountSharingSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "third-party-account-sharing-event-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 10
      TimeUnit: "Second"

SavingsCreationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "savings-creation-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

Secrets:
  Ids:
    TestPgpPublicKey: "test/second-secret"
    TestPgpPrivateKey: "test/top-test-secret"
    TestMostSecret: "test/second-secret"
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"


CreateVPASubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "savings-create-vpa-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

CreateVPAPublisher:
  QueueName: "savings-create-vpa-queue"

AccountStmtSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "account-statement-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

AccountStmtPublisher:
  QueueName: "account-statement-queue"

AccountStatePublisher:
  TopicName: "savings-account-state-update"

BalanceChangeEventPublisher:
  TopicName: "balance-update-events-topic"

SavingsAccountPICreationSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "savings-account-pi-creation-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff is followed for first ~25 min post that regular interval is followed for next 24 hours. ~ same strategy used in vpa creation
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 3
          MaxAttempts: 9
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 48
          TimeUnit: "Minute"
      MaxAttempts: 57
      CutOff: 9

SavingsAccountPICreationPublisher:
  QueueName: "savings-account-pi-creation-queue"


Flags:
  TrimDebugMessageFromStatus: false

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: true

SavingsCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "savings-creation-callback-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

EventAfPurchasePublisher:
  QueueName: "event-af-purchase-queue"

AccountCreationEnquiryDelay: 90s

Profiling:
  StackDriverProfiling:
    ProjectId: "development"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

BalanceUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "process-balance-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

BalanceUpdateEventPublisher:
  TopicName: "balance-update-topic"

OperationalStatusUpdateSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "savings-account-operational-status-update-consumer-queue"
  RetryStrategy:
    RegularInterval:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ProcessTierUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "savings-tier-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"

ProcessKycUpdateEventSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "savings-kyc-update-event-consumer-queue"
  RetryStrategy:
    RegularInterval:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1
        Period: 10s
    Namespace: "savings"

ProcessBalanceUpdateEventSubscriber:
  StartOnServerStart: true
  Disable: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "savings-account-closure-balance-update-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 26
      MaxAttempts: 9
      TimeUnit: "Second" ## backoff for 12 hours
