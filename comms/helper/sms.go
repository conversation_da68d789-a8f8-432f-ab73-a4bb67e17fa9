package helper

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/mohae/deepcopy"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/gamma/pkg/accrual"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	pb "github.com/epifi/gamma/api/comms"
	userPb "github.com/epifi/gamma/api/user"
	smsPb "github.com/epifi/gamma/api/vendorgateway/sms"
	"github.com/epifi/gamma/comms/config"
	"github.com/epifi/gamma/comms/metrics"
	"github.com/epifi/gamma/comms/vendor_selector/strategies"
)

func (h *HelperService) ConstructSendSMSRequest(ctx context.Context, smsMessage *pb.SMSMessage, phone string, user *userPb.User, commsMsgId string) (*smsPb.SendSMSRequest, error) {
	smsReq := &smsPb.SendSMSRequest{
		PhoneNumber: phone,
	}
	// supporting old clients which still send this sms message
	// This will be deprecated and removed from protos when all clients migrate to new templates
	if smsMessage.GetMessage() != "" {
		smsReq.Message = smsMessage.GetMessage()
		return smsReq, nil
	} else if smsMessage.GetSmsOption() != nil {
		return h.GetSmsTemplateRequest(ctx, smsMessage.GetSmsOption(), smsReq, user, commsMsgId)
	}
	return nil, fmt.Errorf("invalid request both message and sms options not present")
}

// nolint: funlen
func (h *HelperService) GetSmsTemplateRequest(ctx context.Context, smsOption *pb.SmsOption, smsReq *smsPb.SendSMSRequest,
	user *userPb.User, commsMsgId string) (*smsPb.SendSMSRequest, error) {
	smsOptions, ok := smsOption.GetOption().(pb.ISmsOption)
	if !ok {
		return nil, errors.New(fmt.Sprintf("%T request does not implement sms options interface", smsOption.GetOption()))
	}
	tempType := smsOptions.GetType()
	tempVersion := smsOptions.GetTemplateVersion()
	smsTemp, err := GetSmsTemplateFromConfig(tempType, tempVersion, h.conf)
	if err != nil {
		logger.Error(ctx, "cannot fetch sms template from config", zap.Error(err),
			zap.Any(logger.TEMPLATE_NAME, tempType))

		return nil, errors.Wrap(err, "cannot fetch sms template from config")
	}
	logger.Debug(ctx, "sms template from config", zap.String("templateType", tempType.String()), zap.String("template", smsTemp.Template))
	msgValue, err := smsOptions.GetActualMessage(smsTemp.Template)
	if err != nil {
		logger.Error(ctx, "unable to replace template values with actual values", zap.Error(err),
			zap.Any(logger.TEMPLATE_NAME, smsTemp.Template), zap.Any(logger.VENDOR, smsTemp.Vendor))

		return nil, errors.Wrap(err, "unable to replace template values with actual values")
	}
	logger.Debug(ctx, "actual message value from template", zap.String("msgValue", msgValue))
	smsReq.Message = msgValue
	// Replace vendor used and account in request here for template based sms
	var vendor commonvgpb.Vendor
	var accountType smsPb.SmsVendorAccountType
	if smsTemp.EnableDynamicVendorSwitching {
		vendorInfoList, err := GetVendorInfoList(smsTemp)
		if err != nil {
			logger.Error(ctx, "error while fetching vendor info list", zap.Error(err))
			return nil, errors.Wrap(err, "error while fetching vendor info list")
		}

		// should call the vendor selector function only in the flag is set to true
		// passing commsMsgId as parameter to vendor switching algorithm to use number of retries, client, etc. parameters in decision-making
		// in which vendor should be selected for given msg
		vendor, err = h.vendorSelector.SelectVendorForMessage(ctx, smsReq.GetPhoneNumber(), pb.Medium_SMS, vendorInfoList, smsTemp.Strategy,
			smsOptions.GetType().String(), commsMsgId)
		// if any error is encountered while identifying vendor for the msg
		// than select default vendor for that template
		if err != nil {
			logger.Error(ctx, "unable to get vendor from vendor selector", zap.Error(err),
				zap.Any(logger.TEMPLATE_NAME, smsTemp.Template), zap.Any(logger.VENDOR, smsTemp.Vendor))
			vendorValue, isSmsVendorValueValid := commonvgpb.Vendor_value[smsTemp.Vendor]
			if !isSmsVendorValueValid {
				return nil, errors.New("valid vendor not configured for template in config")
			}
			vendor = commonvgpb.Vendor(vendorValue)
		}

		account, err := strategies.GetAccountForVendor(vendorInfoList, vendor)
		if err != nil {
			logger.Error(ctx, "error while identifying account for vendor", zap.Error(err))
			return nil, errors.Wrap(err, "error while identifying account for vendor")
		}
		accountType = account
		logger.Debug(ctx, fmt.Sprintf("selected vendor %s with account %s for template %s", vendor.String(), accountType.String(), smsOptions.GetType().String()))

	} else {
		vendorValue, ok1 := commonvgpb.Vendor_value[smsTemp.Vendor]
		if !ok1 {
			// use default vendor passed in request so as to not fail sms being sent
			logger.Error(ctx, "vendor not configured for template in config", zap.Any("smsVendor", smsTemp.Vendor))
			return nil, errors.New("vendor not configured for template in config")
		}
		vendor = commonvgpb.Vendor(vendorValue)
		accountTypeValue, ok2 := smsPb.SmsVendorAccountType_value[smsTemp.Account]
		if !ok2 {
			// use default vendor passed in request so as to not fail sms being sent
			logger.Error(ctx, "invalid account type in config", zap.Any("smsAccountType", smsTemp.Account))
			return nil, errors.New("invalid account type in config")
		}
		accountType = smsPb.SmsVendorAccountType(accountTypeValue)
	}

	// currently NR account support is setup only for Kaleyra vendor.
	if vendor == commonvgpb.Vendor_KALEYRA && smsTemp.IsTemplateWhitelistedForNR {
		accountType = h.getBestSmsVendorAccountType(ctx, user.GetActorId(), accountType)
	}

	smsReq.Header = &commonvgpb.RequestHeader{Vendor: vendor}
	smsReq.SmsVendorAccountType = accountType
	if smsTemp.EnableOnlyForInternalAndFnfUsers {
		ok, err := h.IsInternalOrFnfUser(ctx, user)
		if err != nil {
			logger.Error(ctx, "error while checking if user is internal or fnf", zap.Error(err))
			return nil, errors.Wrap(err, "error while checking if user is internal or fnf")
		}
		if !ok {
			return nil, ErrSmsTurnedOff
		}
	}
	return smsReq, nil
}

func (h *HelperService) getBestSmsVendorAccountType(ctx context.Context, actorId string, accountType smsPb.SmsVendorAccountType) smsPb.SmsVendorAccountType {
	nrResp, nrErr := h.userClient.IsNonResidentUser(ctx, &userPb.IsNonResidentUserRequest{
		Identifier: &userPb.IsNonResidentUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(nrResp, nrErr); rpcErr != nil {
		logger.Error(ctx, "error in non resident user check rpc", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return accountType
	}

	// if user is NR user, then send messages via EPIFI_NR account.
	if nrResp.GetIsNonResidentUser().ToBool() {
		logger.Debug(ctx, "sending sms using EPIFI_NR account", zap.String(logger.ACTOR_ID_V2, actorId))
		return smsPb.SmsVendorAccountType_EPIFI_NR
	}

	return accountType
}

func GetSmsTemplateFromConfig(smsType pb.SmsType, templateVersion pb.TemplateVersion, conf *config.Config) (*config.SmsTemplate, error) {
	var smsTypeMapKey, templateVersionMapKey string
	smsTypeMapKey = smsType.String()
	templateVersionMapKey = templateVersion.String()
	smsOptionsVersionMap, ok := conf.SmsTemplates[smsTypeMapKey]
	if !ok {
		return nil, errors.New("cannot get template from config")
	}
	smsTemplate, ok := smsOptionsVersionMap[templateVersionMapKey]
	if !ok {
		return nil, errors.New("cannot get template from config")
	}
	smsTemplateCopy := deepcopy.Copy(smsTemplate).(*config.SmsTemplate)
	// Apply fi-coin to fi-point replacement for templates
	smsTemplateCopy.Template = accrual.ReplaceCoinWithPointIfApplicable(smsTemplateCopy.Template)
	return smsTemplateCopy, nil
}

func GetMessageStatusFromSmsStatus(status smsPb.SMSStatus, defaultMessageState pb.MessageState) pb.MessageState {
	switch status {
	case smsPb.SMSStatus_SENT_TO_VENDOR:
		return pb.MessageState_SENT_TO_VENDOR
	case smsPb.SMSStatus_FAILED:
		return pb.MessageState_FAILED
	case smsPb.SMSStatus_DELIVERED:
		return pb.MessageState_DELIVERED
	default:
		return defaultMessageState
	}
}

func (h *HelperService) SendSMSWithFallback(ctx context.Context, smsReq *smsPb.SendSMSRequest) (*smsPb.SendSMSResponse, error) {
	isFallbackAvl := false
	if smsReq.GetHeader().GetVendor() == commonvgpb.Vendor_ACL {
		isFallbackAvl = true
	}
	sendSMSResponse, err := h.vgSmsClient.SendSMS(ctx, smsReq)
	if err = epifigrpc.RPCError(sendSMSResponse, err); err != nil {
		if sendSMSResponse.GetStatus().IsUnavailable() && isFallbackAvl {
			smsReq.UseFallbackEndpoint = true
			sendSMSResponse, err = h.vgSmsClient.SendSMS(ctx, smsReq)
			if err = epifigrpc.RPCError(sendSMSResponse, err); err != nil {
				return sendSMSResponse, errors.Wrap(err, "error sending sms via both primary and fallback endpoint")
			}
			return sendSMSResponse, nil
		}
		return sendSMSResponse, errors.Wrap(err, "error sending sms via primary endpoint")
	}
	return sendSMSResponse, nil
}

func IsSmsStatusCheckRequired(vendor commonvgpb.Vendor) bool {
	switch vendor {
	case commonvgpb.Vendor_ACL, commonvgpb.Vendor_KALEYRA, commonvgpb.Vendor_NETCORE, commonvgpb.Vendor_AIRTEL:
		return false
	default:
		return true
	}
}

func GetSMSTemplateString(smsMessage *pb.SMSMessage) string {
	smsOption, ok := smsMessage.GetSmsOption().GetOption().(pb.ISmsOption)
	if !ok {
		return metrics.UnknownSmsTemplate
	}
	return smsOption.GetType().String()
}
