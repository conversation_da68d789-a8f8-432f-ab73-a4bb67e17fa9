package dao_test

import (
	"os"
	"testing"

	gormv2 "gorm.io/gorm"

	"github.com/epifi/gamma/fittt/config"
	"github.com/epifi/gamma/fittt/config/genconf"
	rtdao "github.com/epifi/gamma/fittt/devconsole/dao"
	"github.com/epifi/gamma/fittt/test"
)

var (
	db    *gormv2.DB
	dao   rtdao.RuleTemplatesDao
	conf  *config.Config
	gConf *genconf.Config
)

// TestMain initializes test components, runs tests and exits
// os.Exit() does not respect deferred functions, so teardown has to be called without defer
func TestMain(m *testing.M) {
	var teardown func()
	conf, gConf, db, teardown = test.InitTestServerWithGenConf(true)
	defer teardown()
	dao = rtdao.NewRuleTemplatesDaoImpl(db)

	exitCode := m.Run()
	os.Exit(exitCode)
}
