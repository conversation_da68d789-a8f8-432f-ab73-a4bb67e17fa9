Application:
  Environment: "qa"
  Name: "casbin"

Server:
  Ports:
    GrpcPort: 8099
    GrpcSecurePort: 9505
    HttpPort: 9999
    HttpPProfPort: 9990

EpifiDb:
  AppName: "casbin"
  StatementTimeout: 1s
  Name: "sherlock"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    DbUsernamePassword: "qa/rds/postgres/sherlock"

PolicyModel: "policy_model.conf"

Flags:
  TrimDebugMessageFromStatus: false

RoleToRpcMap:
  ADMIN: [
      "/cx.issue_config.IssueConfigManagement/GetIssueConfig",
      "/cx.issue_config.IssueConfigManagement/UpdateIssueConfig",
      "/cx.issue_config.IssueConfigManagement/GetIssueConfigHistory",
      "/cx.issue_config.IssueConfigManagement/GetIssueConfigFilterOptions",
      "/cx.agent.AgentService/CreateAgent",
      "/cx.agent.AgentService/UpdateAgent",
      "/cx.agent.AgentService/GetAgentInfo",
      "/cx.agent.AgentService/GetAllAgents",
      "/cx.agent.AgentService/RemoveAgent",
      "/cx.monitoring.ActivityLogging/GetActivityLogs",
      "/cx.monitoring.ActivityLogging/GetActivityInfo",
      "/cx.audit_log.AuditLogs/GetAuditLogs",
      "/cx.audit_log.AuditLogs/GetAuditLogDetails",
      "/cx.payout.Payout/RaisePayout",
      "/cx.payout.Payout/GetPayoutListRaisedByCurrentUser",
      "/cx.payout.Payout/GetAllowedFiCoinsPayoutValues",
      "/cx.landing_page.LandingPage/CallbackUserAdmin",
      "/cx.data_collector.kyc.CustomerKYC/AllowLivenessRetry",
      "/cx.landing_page.LandingPage/GetWaitlistUserDetailsWithoutTicket",
      "/cx.admin_actions.AdminActons/GetAvailableAdminActions",
      "/cx.admin_actions.AdminActons/GetParameterListForAdminAction",
      "/cx.admin_actions.AdminActons/ExecuteAction",
      "/cx.data_collector.profile.CustomerProfile/GetBulkUserInfoByTicketIds",
      "ADMIN_ACTION_REVOKE_USER_ACCESS",
      "ADMIN_ACTION_NAME_DOB_RESET",
      "ADMIN_ACTION_LIVENESS_FM_RESET",
      "ADMIN_ACTION_DATA_RESET",
      "ADMIN_ACTION_RESET_DEBIT_CARD_NAME_RETRY",
      "ADMIN_ACTION_REFRESH_USER_INFO_FROM_PARTNER_BANK",
      "ADMIN_ACTION_MANUAL_SCREENING_UPDATE",
      "ADMIN_ACTION_PULL_APP_LOGS",
      "/cx.sherlock_user.SherlockUserService/CreateSherlockUser",
      "/cx.sherlock_user.SherlockUserService/UpdateSherlockUserInfo",
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.sherlock_user.SherlockUserService/GetAllSherlockUsers",
      "/cx.sherlock_banners.SherlockBanners/CreateBanner",
      "/cx.sherlock_banners.SherlockBanners/BulkCreateBanners",
      "/cx.sherlock_banners.SherlockBanners/UpdateBanner",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "/cx.sherlock_banners.SherlockBanners/DeleteBanners",
      "/cx.ticket.ticket/BulkUpdateTickets",
      "/cx.ticket.ticket/GetAllBulkTicketJobs",
      "/cx.ticket.ticket/GetJobFailureLogs",
      "/cx.ticket.ticket/KillJobProcessing",
      "ADMIN_ACTION_UPDATE_USER_PARENTS_NAME",
      "ADMIN_ACTION_USER_COMMS_CONTROL",
      "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetLoanUserDetails",
      "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetLoanDetails",
      "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetForeClosureDetails",
      "/cx.data_collector.firefly.Firefly/GetCreditLimit",
      "/cx.data_collector.firefly.Firefly/GetOutstandingDues",
      "/cx.data_collector.firefly.Firefly/InitiateCardAction",
      "/cx.data_collector.firefly.Firefly/GetPaginatedTransactionDetails",
      "/cx.data_collector.firefly.Firefly/GetAllActiveLoanAccounts",
      "/cx.data_collector.firefly.Firefly/GetAllClosedLoanAccounts",
      "/cx.data_collector.firefly.Firefly/GetAllEligibleTransactions",
      "/cx.data_collector.firefly.Firefly/GetLoanAccountDetails",
      "/cx.data_collector.firefly.Firefly/GetTransactionLoanOffers",
      "/cx.data_collector.firefly.Firefly/GetCardActionStatus",
      "/cx.data_collector.firefly.Firefly/GetCurrentCard",
      "/cx.data_collector.firefly.Firefly/GetAllCards",
      "/cx.data_collector.firefly.Firefly/GetCardBillingInfo",
      "/cx.data_collector.firefly.Firefly/GetCardControlDetails",
      "/cx.data_collector.firefly.Firefly/GetDisputes",
      "/cx.data_collector.firefly.Firefly/GetCardTrackingInfo",
      "/cx.data_collector.firefly.Firefly/GetTransactions",
      "/cx.data_collector.firefly.Firefly/GetLimitUsageDetails",
      "/cx.data_collector.firefly.Firefly/GetCardUsageDetails",
      "/cx.data_collector.firefly.Firefly/ChangeControlDetails",
      "/cx.data_collector.firefly.Firefly/UpdateFreeCardReplacement",
      "/cx.data_collector.firefly.Firefly/BlockCard",
      "/cx.data_collector.firefly.Firefly/SuspendCard",
      "/cx.data_collector.firefly.Firefly/GetCardOnboardingDetails",
      "/cx.data_collector.firefly.Firefly/GetNextDisputeQuestion",
      "/cx.data_collector.firefly.Firefly/GetNextStageDetails",
      "/cx.data_collector.firefly.Firefly/GetStageDetails",
      "/cx.data_collector.firefly.Firefly/GetCardEligibilityDetails",
      "/cx.data_collector.firefly.Firefly/GetCardProgramDetails",
      "/cx.data_collector.firefly.Firefly/GetCardOnboardingDetailsV2",
      "/cx.data_collector.firefly.Firefly/GetMilestoneRewardsDetails",
      "/cx.sherlock_feedback.SherlockFeedback/GetFeedback",
      "/cx.data_collector.user_requests.UserRequests/GetAccountStatementFormDetails",
      "/cx.data_collector.user_requests.UserRequests/SendAccountStatement",
      "/cx.data_collector.rewards.Rewards/GetRewardUnitsUtilisationForActorAndOfferInMonth",
      "/cx.data_collector.firefly.Firefly/GetFdDetails",
      "/cx.data_collector.rewards.Rewards/GetRewardOfferTypesOptions",
      "/cx.data_collector.rewards.Rewards/GetRewardDetails",
      "/cx.data_collector.user_requests.UserRequests/GetAgentPrompts",
      "/cx.data_collector.user_requests.UserRequests/SendPrompt",
      "/cx.data_collector.rewards.Rewards/GetFiStoreRedemptionsDetails",
      "/cx.data_collector.rewards.Rewards/GetFiStoreRedemptionAdditionalDetails",
      "/cx.data_collector.card.Cards/FetchAmcInfo",
      "/cx.data_collector.card.Cards/FetchPhysicalCardDispatchStatus",
  ]
  ADMINRESTRICTED: [
      "/cx.landing_page.LandingPage/CallbackUserAdmin",
      "/cx.landing_page.LandingPage/GetWaitlistUserDetailsWithoutTicket",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
  ]
  AGENT: [
      "/cx.agent.AgentService/GetAgentInfo",
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.payout.Payout/RaisePayout",
      "/cx.payout.Payout/GetPayoutListRaisedByCurrentUser",
      "/cx.payout.Payout/GetAllowedFiCoinsPayoutValues",
      "/cx.landing_page.LandingPage/GetTicketAndUserDetails",
      "/cx.landing_page.LandingPage/GetRecentUserQueries",
      "/cx.landing_page.LandingPage/CallbackUser",
      "/cx.landing_page.LandingPage/GetUserDetails",
      "/cx.landing_page.LandingPage/GetWaitlistUserDetails",
      "/cx.landing_page.LandingPage/GetRecentOTPAttempts",
      "/cx.landing_page.LandingPage/GetAvailableTabs",
      "/cx.landing_page.LandingPage/GetTabDetails",
      "/cx.customer_auth.CustomerAuthentication/GetNextAction",
      "/cx.customer_auth.CustomerAuthentication/VerifyCustomer",
      "/cx.customer_auth.CustomerAuthentication/ResetAuthFactor",
      "/cx.data_collector.account.Account/GetDepositsForCustomer",
      "/cx.data_collector.account.Account/GetDepositDetails",
      "/cx.data_collector.account.Account/GetDepositTransactions",
      "/cx.data_collector.account.Account/GetDepositRequestsForCustomer",
      "/cx.data_collector.account.Account/IsSavingsAccountClosureAllowed",
      "/cx.data_collector.account.Account/GetAccountFreezeInfo",
      "/cx.data_collector.card.Cards/GetCardsForCustomer",
      "/cx.data_collector.card.Cards/BlockCustomerCard",
      "/cx.data_collector.card.Cards/SuspendCustomerCard",
      "/cx.data_collector.card.Cards/GetChannelAndLimitSettings",
      "/cx.data_collector.card.Cards/GetCardDeliveryTrackingData",
      "/cx.data_collector.card.Cards/GetAllCardsForCustomer",
      "/cx.data_collector.card.Cards/GetCardDeliveryTrackingDataV2",
      "/cx.data_collector.card.Cards/UpdateFreeCardReplacement",
      "/cx.data_collector.card.Cards/FetchForexRefundInfoForDcTxn",
      "/cx.data_collector.card.Cards/FetchAmcInfo",
      "/cx.data_collector.card.Cards/FetchPhysicalCardDispatchStatus",
      "/cx.data_collector.kyc.CustomerKYC/GetCustomerKYCData",
      "/cx.data_collector.kyc.CustomerKYC/GetCustomerVKYCData",
      "/cx.data_collector.kyc.CustomerKYC/GetCustomerEPANAttempts",
      "/cx.data_collector.fittt.Fittt/GetActiveRules",
      "/cx.data_collector.fittt.Fittt/GetSubscriptionsForActor",
      "/cx.data_collector.fittt.Fittt/GetSubscriptionExecutionInfo",
      "/cx.data_collector.onboarding_status.Onboarding/GetOnboardingDetails",
      "/cx.data_collector.onboarding_status.Onboarding/GetOnboardingScores",
      "/cx.data_collector.onboarding_status.Onboarding/GetReOOBEDetails",
      "/cx.data_collector.profile.CustomerProfile/GetCustomerProfile",
      "/cx.data_collector.payment_instruments.CustomerPI/GetUPIDetails",
      "/cx.data_collector.payment_instruments.CustomerPI/DisableOrEnableVPA",
      "/cx.data_collector.payment_instruments.CustomerPI/GetUpiInfo",
      "/cx.data_collector.transaction.CustomerTransactions/GetCustomerTransactions",
      "/cx.data_collector.transaction.CustomerTransactions/GetFirstOrLastNTransactions",
      "/cx.data_collector.transaction.CustomerTransactions/GetMandates",
      "/cx.data_collector.transaction.CustomerTransactions/GetUpcomingTransactions",
      "/cx.data_collector.rewards.Rewards/GetRewardOffers",
      "/cx.data_collector.rewards.Rewards/GetOffers",
      "/cx.data_collector.rewards.Rewards/GetUserRewards",
      "/cx.data_collector.rewards.Rewards/GetRedeemedOffers",
      "/cx.data_collector.rewards.Rewards/GetRewardOfferById",
      "/cx.data_collector.rewards.Rewards/GetExchangerOffers",
      "/cx.data_collector.rewards.Rewards/GetExchangerOfferOrders",
      "/cx.data_collector.transaction.CustomerTransactions/GetAllTransactionsDetails",
      "/cx.data_collector.communications.CustomerCommunication/GetCustomerMessages",
      "/cx.data_collector.communications.CustomerCommunication/GetMessageDetails",
      "/cx.data_collector.wealth_onboarding.WealthOnboarding/GetOnboardingDetails",
      "/cx.data_collector.wealth_onboarding.WealthOnboarding/GetIsInvestmentReady",
      "/cx.data_collector.investment.mutualfund.Investment/GetInvestedMutualFunds",
      "/cx.data_collector.investment.mutualfund.Investment/GetInvestedMutualFundActivities",
      "/cx.data_collector.investment.mutualfund.Investment/GetOrderDetails",
      "/cx.data_collector.sherlock_actor_activity.SherlockActorActivity/GetActivities",
      "/cx.data_collector.sherlock_actor_activity.SherlockActorActivity/GetActivityDetailsForSherlock",
      "/cx.dispute.Dispute/GetChannelList",
      "/cx.dispute.Dispute/GetNextQuestions",
      "/cx.dispute.Dispute/RaiseDisputeSherlock",
      "/cx.dispute.Dispute/GetDisputeDetails",
      "/cx.dispute.Dispute/GetAllDisputes",
      "/cx.dispute.Dispute/UpdateDispute",
      "/cx.dispute.Dispute/GetAllDisputesForActor",
      "/cx.dispute.Dispute/MarkDisputeAgainstTicket",
      "/cx.ticket.ticket/AttachEntity",
      "/cx.ticket.ticket/GetRelatedTickets",
      "/cx.ticket.ticket/MergeTickets",
      "/cx.ticket.ticket/GetFreshdeskTicketCategories",
      "/cx.ticket.ticket/GetTicketInfo",
      "/cx.ticket.ticket/UpdateTicketInfo",
      "/cx.app_log.AppLog/PullAppLogs",
      "/cx.app_log.AppLog/GetExportedLogsListForAgents",
      "/cx.data_collector.referrals.Referrals/GetReferralDetailsForActor",
      "/cx.data_collector.referrals.Referrals/GetReferrerDetails",
      "/cx.data_collector.referrals.Referrals/GetRefereesForActor",
      "/cx.data_collector.referrals.Referrals/GetRefereeDetails",
      "/cx.admin_actions.AdminActons/GetAvailableAdminActions",
      "/cx.admin_actions.AdminActons/GetParameterListForAdminAction",
      "/cx.admin_actions.AdminActons/ExecuteAction",
      "ADMIN_ACTION_NAME_DOB_RESET",
      "ADMIN_ACTION_LIVENESS_FM_RESET",
      "ADMIN_ACTION_RESET_DEBIT_CARD_NAME_RETRY",
      "ADMIN_ACTION_PULL_APP_LOGS",
      "/cx.connected_account.ConnectedAccount/GetConnectedAccountDetails",
      "/cx.ticket.ticket/GetSupportTicketsForSherlock",
      "/cx.call.Call/GetCallDetails",
      "/cx.call.Call/UpdateCallDetails",
      "/cx.call.Call/CreateTicketForCall",
      "/cx.call.Call/GetCallRecordings",
      "/cx.data_collector.p2pinvestment.P2PInvestment/GetInvestmentSummary",
      "/cx.data_collector.p2pinvestment.P2PInvestment/GetInvestor",
      "/cx.data_collector.p2pinvestment.P2PInvestment/GetFilterValuesForJumpInvestmentSummary",
      "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetLoanUserDetails",
      "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetLoanDetails",
      "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetForeClosureDetails",
      "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetLoanAdditionalDetails",
      "/cx.data_collector.firefly.Firefly/GetCreditLimit",
      "/cx.data_collector.firefly.Firefly/GetOutstandingDues",
      "/cx.data_collector.firefly.Firefly/InitiateCardAction",
      "/cx.data_collector.firefly.Firefly/GetPaginatedTransactionDetails",
      "/cx.data_collector.firefly.Firefly/GetAllActiveLoanAccounts",
      "/cx.data_collector.firefly.Firefly/GetAllClosedLoanAccounts",
      "/cx.data_collector.firefly.Firefly/GetAllEligibleTransactions",
      "/cx.data_collector.firefly.Firefly/GetLoanAccountDetails",
      "/cx.data_collector.firefly.Firefly/GetTransactionLoanOffers",
      "/cx.data_collector.firefly.Firefly/GetCardActionStatus",
      "/cx.data_collector.firefly.Firefly/GetCurrentCard",
      "/cx.data_collector.firefly.Firefly/GetAllCards",
      "/cx.data_collector.firefly.Firefly/GetCardBillingInfo",
      "/cx.data_collector.firefly.Firefly/GetCardControlDetails",
      "/cx.data_collector.firefly.Firefly/GetDisputes",
      "/cx.data_collector.firefly.Firefly/GetCardTrackingInfo",
      "/cx.data_collector.firefly.Firefly/GetTransactions",
      "/cx.data_collector.firefly.Firefly/GetLimitUsageDetails",
      "/cx.data_collector.firefly.Firefly/GetCardUsageDetails",
      "/cx.data_collector.firefly.Firefly/ChangeControlDetails",
      "/cx.data_collector.firefly.Firefly/UpdateFreeCardReplacement",
      "/cx.data_collector.firefly.Firefly/BlockCard",
      "/cx.data_collector.firefly.Firefly/SuspendCard",
      "/cx.data_collector.firefly.Firefly/GetCardOnboardingDetails",
      "/cx.data_collector.firefly.Firefly/GetNextDisputeQuestion",
      "/cx.data_collector.firefly.Firefly/GetNextStageDetails",
      "/cx.data_collector.firefly.Firefly/GetStageDetails",
      "/cx.data_collector.firefly.Firefly/GetCardEligibilityDetails",
      "/cx.data_collector.firefly.Firefly/GetCardProgramDetails",
      "/cx.data_collector.firefly.Firefly/GetCardOnboardingDetailsV2",
      "/cx.data_collector.firefly.Firefly/GetMilestoneRewardsDetails",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "/cx.sherlock_banners.SherlockBanners/GetBannersForCustomer",
      "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryAccountDetailsForAgents",
      "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryTxnVerificationRequestsForUser",
      "/cx.data_collector.salarydataops.SalaryDataOps/GetUserDetailsNeededForVerification",
      "/cx.user_issue_info.UserIssueInfoService/GetUserIssueInfo",
      "/cx.sherlock_feedback.SherlockFeedback/SubmitFeedback",
      "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetPortfolioDetails",
      "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetUsStockActivities",
      "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetActivityDetails",
      "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetRemittanceOrderDetails",
      "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetRemittances",
      "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetAccountBasicDetails",
      "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetAccountStagesDetails",
      "/cx.data_collector.tiering.Tiering/GetTieringDetails",
      "/cx.data_collector.tiering.Tiering/GetTieringSensitiveDetails",
      "/cx.data_collector.tiering.Tiering/OverrideGracePeriod",
      "/cx.data_collector.tiering.Tiering/OverrideCoolOffPeriod",
      "/cx.data_collector.tiering.Tiering/IsUserEligibleForRewards",
      "/cx.dispute.Dispute/GetDocument",
      "/cx.dispute.Dispute/SendDocument",
      "/cx.dispute.Dispute/AttachCorrespondenceForSherlock",
      "/cx.dispute.Dispute/GetAllCorrespondence",
      "/cx.data_collector.alfred.Alfred/GetRequestDetails",
      "/cx.data_collector.salarydataops.SalaryDataOps/GetHealthInsuranceUserPolicyDetailsForAgents",
      "/cx.data_collector.salarydataops.SalaryDataOps/GetHealthInsuranceGeneralPolicyInfo",
      "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryLiteMandateDetailsForAgents",
      "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryLiteMandateExecutionDetailsForAgents",
      "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryProgramActivationStatusAtTimeForAgents",
      "/cx.data_collector.user_requests.UserRequests/GetAccountStatementFormDetails",
      "/cx.data_collector.user_requests.UserRequests/SendAccountStatement",
      "/cx.data_collector.rewards.Rewards/GetRewardUnitsUtilisationForActorAndOfferInMonth",
      "/cx.data_collector.firefly.Firefly/GetFdDetails",
      "/cx.data_collector.compliance.Compliance/GetPeriodicKYCDetails",
      "/cx.data_collector.rewards.Rewards/GetRewardOfferTypesOptions",
      "/cx.data_collector.rewards.Rewards/GetRewardDetails",
      "/cx.data_collector.user_requests.UserRequests/GetAgentPrompts",
      "/cx.data_collector.user_requests.UserRequests/SendPrompt",
      "/cx.data_collector.rewards.Rewards/GetFiStoreRedemptionsDetails",
      "/cx.data_collector.rewards.Rewards/GetFiStoreRedemptionAdditionalDetails",
      "/cx.issue_category.IssueCategoryService/GetIssueCategoryDetails",
  ]
  DEVELOPER: [
      "/cx.agent.AgentService/CreateAgent",
      "/cx.agent.AgentService/UpdateAgent",
      "/cx.agent.AgentService/GetAgentInfo",
      "/cx.agent.AgentService/GetAllAgents",
      "/cx.agent.AgentService/RemoveAgent",
      "/cx.sherlock_user.SherlockUserService/CreateSherlockUser",
      "/cx.sherlock_user.SherlockUserService/UpdateSherlockUserInfo",
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.sherlock_user.SherlockUserService/GetAllSherlockUsers",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "/cx.app_log.AppLog/GetExportedLogsList",
      "/cx.app_log.AppLog/GetLogsData",
      "/cx.developer.ticket_summary.TicketSummary/GetTicketDetails",
      "/cx.developer.ticket_summary.TicketSummary/GetTicketConversations",
      "/cx.developer.db_state.DBState/GetEntityListForService",
      "/cx.developer.db_state.DBState/GetServiceList",
      "/cx.developer.db_state.DBState/GetParameterListForEntity",
      "/cx.developer.db_state.DBState/GetDataForEntity",
      "/cx.developer.actions.DevActions/GetAvailableActions",
      "/cx.developer.actions.DevActions/GetParameterListForAction",
      "/cx.developer.actions.DevActions/ExecuteAction",
      "/cx.call.Call/GetCallRecordings",
      "DEV_ACTION_MARK_USERS_BY_ACQUISITION_INFO",
      "DEV_ACTION_CREATE_ACTIVITY_METADATA",
      "DEV_ACTION_RETRY_LIVENESS",
      "DEV_ACTION_RETRY_CREATE_BANK_CUSTOMER",
      "DEV_ACTION_RETRY_CREATE_ACCOUNT",
      "DEV_ACTION_RETRY_EKYC",
      "DEV_ACTION_RETRY_CKYC",
      "DEV_ACTION_CREATE_CARD",
      "DEV_ACTION_START_USER_ACTION",
      "DEV_ACTION_CREATE_REWARD_OFFER",
      "DEV_ACTION_ADD_RED_LIST_ENTRY",
      "DEV_ACTION_UPDATE_REWARD_OFFER_STATUS",
      "DEV_ACTION_CREATE_LUCKY_DRAW_CAMPAIGN",
      "DEV_ACTION_CREATE_LUCKY_DRAW",
      "DEV_ACTION_PULL_APP_LOGS",
      "DEV_ACTION_REMOVE_RED_LIST_ENTRY",
      "DEV_ACTION_ADD_USER_GROUP_MAPPING",
      "DEV_ACTION_DELETE_USER",
      "DEV_ACTION_RECOVER_AUTH_FACTOR_UPDATE",
      "DEV_ACTION_REALLOW_USER_TO_TRY_WAITLIST",
      "DEV_ACTION_UPDATE_REWARD_OFFER_DISPLAY",
      "DEV_ACTION_TRIGGER_FAILED_WAITLIST_EMAIL",
      "DEV_ACTION_TRIGGER_FAILED_PIS_INDEXING",
      "DEV_ACTION_ONBOARDING_TROUBLESHOOTING_DETAILS",
      "DEV_ACTION_TRIGGER_KYC_NAME_DOB_VALIDATION",
      "DEV_ACTION_CREATE_OFFER",
      "DEV_ACTION_CREATE_OFFERS_IN_BULK",
      "DEV_ACTION_CREATE_OFFER_LISTING",
      "DEV_ACTION_UPDATE_OFFER_LISTING",
      "DEV_ACTION_DELETE_OFFER_LISTING",
      "DEV_ACTION_CREATE_OR_UPDATE_SEGMENT_MAPPING",
      "DEV_ACTION_CREATE_OFFER_INVENTORY",
      "DEV_ACTION_ADD_OFFER_TO_INVENTORY",
      "DEV_ACTION_DELETE_OFFER_INVENTORY",
      "DEV_ACTION_FORCE_TRIGGER_RECON",
      "DEV_ACTION_UPDATE_OFFER_DISPLAY_RANK",
      "DEV_ACTION_UPDATE_REWARD_OFFER_DISPLAY_RANK",
      "DEV_ACTION_TRIGGER_GMAIL_ACTOR_SYNC",
      "DEV_ACTION_UNLINK_GMAIL_ACCOUNT",
      "DEV_ACTION_DELETE_WAITLIST_USER_FROM_USER_ID",
      "DEV_ACTION_MARK_WAITLIST_USERS_EARLY_ACCESS",
      "DEV_ACTION_SEND_EMAIL_TO_EARLY_ACCESS",
      "DEV_ACTION_SYNC_ONBOARDING",
      "DEV_ACTION_FITTT_CREATE_NEW_RULE",
      "DEV_ACTION_FITTT_PUBLISH_SHARK_TANK_EVENT",
      "DEV_ACTION_FITTT_UPDATE_RULE",
      "DEV_ACTION_FITTT_BULK_UPDATE_SUBSCRIPTIONS_STATE",
      "DEV_ACTION_FITTT_ARCHIVE_RULES_AND_SUBSCRIPTIONS",
      "DEV_ACTION_FITTT_GET_RULES_FOR_CLIENT",
      "DEV_ACTION_FITTT_CREATE_HOME_CARD",
      "DEV_ACTION_FITTT_UPDATE_HOME_CARD",
      "DEV_ACTION_FITTT_CREATE_SCHEDULE",
      "DEV_ACTION_FITTT_STOP_SCHEDULES_JOBS",
      "DEV_ACTION_FITTT_CREATE_COLLECTION",
      "DEV_ACTION_FITTT_UPDATE_COLLECTION",
      "DEV_ACTION_UPDATE_ONBOARDING_STAGE",
      "DEV_ACTION_PROCESS_NON_RESIDENT_CROSS_VALIDATION_MANUAL_REVIEW",
      "DEV_ACTION_SEND_COMMS_TO_WAITLIST_USER",
      "DEV_ACTION_UPDATE_OFFER_DISPLAY",
      "DEV_ACTION_SEED_FINITE_CODES",
      "DEV_ACTION_FITTT_INITIATE_MATCH_UPDATE",
      "DEV_ACTION_INITIATE_CARD_NOTIFICATIONS",
      "DEV_ACTION_MARK_LIVENESS_PASSED",
      "DEV_ACTION_MARK_FACEMATCH_PASSED",
      "DEV_ACTION_CREATE_PREFERENCE",
      "DEV_ACTION_UPDATE_SHIPPING_ADDRESS_AT_VENDOR",
      "DEV_ACTION_CREATE_SHIPPING_PREFERENCE",
      "DEV_ACTION_RETRY_PAY_ORDER_RMS_EVENT",
      "DEV_ACTION_SEND_WELCOME_WHATSAPP",
      "DEV_ACTION_SEND_REWARDS_CAMPAIGN_COMM",
      "DEV_ACTION_UNREDACTED_USER",
      "DEV_ACTION_FORCE_PROCESS_ORDER",
      "DEV_ACTION_RETRY_REWARD_PROCESSING",
      "DEV_ACTION_GET_FIT_TXN_AGGREGATE_SEARCH",
      "DEV_ACTION_UPDATE_USER_PROFILE_NAME",
      "DEV_ACTION_GENERATE_ACCOUNT_STATEMENT",
      "DEV_ACTION_ONBOARDING_SNAPSHOT",
      "DEV_ACTION_BACKFILL_FRESHDESK_TICKET_CONTACTS",
      "DEV_ACTION_CREATE_REWARD_OFFER_GROUP",
      "DEV_ACTION_RESET_KYC_NAME_DOB_RETRY",
      "DEV_ACTION_RETRY_OFFER_REDEMPTION",
      "DEV_ACTION_MANUAL_SCREENING_UPDATE",
      "DEV_ACTION_UN_NAME_CHECK_UNBLOCK",
      "DEV_ACTION_RESET_DEBIT_CARD_NAME_RETRY",
      "DEV_ACTION_REFRESH_VKYC_STATUS",
      "DEV_ACTION_UPDATE_CARD_PIN_SET",
      "DEV_ACTION_RAISE_AA_CONSENT",
      "DEV_ACTION_FORCE_CARD_CREATION_ENQUIRY",
      "DEV_ACTION_TRIGGER_REWARDS_MANUAL_GIVEAWAY_EVENT",
      "DEV_ACTION_DELETE_USER_GROUP_MAPPING",
      "DEV_ACTION_SYNC_WEALTH_ONBOARDING",
      "DEV_ACTION_UNLOCK_IN_APP_REFERRAL",
      "DEV_ACTION_TRIGGER_VKYC_CALLBACK",
      "DEV_ACTION_UPDATE_GMAIL_INSIGHTS_MERCHANTS",
      "DEV_ACTION_UPDATE_GMAIL_INSIGHTS_MERCHANT_QUERIES",
      "DEV_ACTION_UPDATE_INAPPHELP_FAQ",
      "DEV_ACTION_HANDLE_SAVINGS_ACCOUNT_CLOSURE",
      "DEV_ACTION_AA_CONSENT_STATUS_UPDATE",
      "DEV_ACTION_AA_ACCOUNT_DELINK",
      "DEV_ACTION_REACTIVATE_DEVICE",
      "DEV_ACTION_REOPEN_SAVINGS_ACCOUNT_IN_DB",
      "DEV_ACTION_UPDATE_WEALTH_ONBOARDING_STATUS",
      "DEV_ACTION_ADD_MEDIA_PLAYLIST",
      "DEV_ACTION_UPDATE_MEDIA_PLAYLIST",
      "DEV_ACTION_ADD_MEDIA_CONTENT_STORY",
      "DEV_ACTION_UPDATE_MEDIA_CONTENT_STORY",
      "DEV_ACTION_ADD_UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING",
      "DEV_ACTION_DELETE_UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING",
      "DEV_ACTION_ADD_MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING",
      "DEV_ACTION_DELETE_MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING",
      "DEV_ACTION_UPDATE_USER_NAME",
      "DEV_ACTION_REFRESH_USER_INFO_FROM_PARTNER_BANK",
      "DEV_ACTION_CREATE_MUTUAL_FUND",
      "DEV_ACTION_UPDATE_MUTUAL_FUND",
      "DEV_ACTION_ADD_CREDIT_MIS_FILE_META_DATA",
      "DEV_ACTION_MUTUAL_FUND_UPLOAD_REVERSE_FEED_FILE",
      "DEV_ACTION_REPLAY_AA_ACCOUNT_EVENTS",
      "DEV_ACTION_CREATE_EXCHANGER_OFFER",
      "DEV_ACTION_CREATE_EXCHANGER_OFFER_GROUP",
      "DEV_ACTION_CREATE_EXCHANGER_OFFER_LISTING",
      "DEV_ACTION_UPDATE_EXCHANGER_OFFER_DISPLAY",
      "DEV_ACTION_UPDATE_EXCHANGER_OFFER_STATUS",
      "DEV_ACTION_UPDATE_EXCHANGER_OFFER_LISTING",
      "DEV_ACTION_DELETE_EXCHANGER_OFFER_LISTING",
      "DEV_ACTION_CREATE_EXCHANGER_OFFER_INVENTORY",
      "DEV_ACTION_INCREMENT_EXCHANGER_OFFER_INVENTORY",
      "DEV_ACTION_REPLAY_AA_TXN_EVENTS",
      "DEV_ACTION_FORCE_PROCESS_DEPOSIT_REQUEST",
      "DEV_ACTION_SEARCH_PARSE_QUERY_BASE",
      "DEV_ACTION_MUTUAL_FUND_DEACTIVATE_ENTITY_FROM_FILE",
      "DEV_ACTION_UPDATE_MUTUAL_FUND_ORDER_STATUS",
      "DEV_ACTION_UPLOAD_MARKETING_CAMPAIGN_USERS_LIST",
      "DEV_ACTION_MUTUAL_FUND_DOWNLOAD_CREDIT_MIS_REPORT",
      "DEV_ACTION_ADD_IN_APP_TARGETED_COMMS_ELEMENT",
      "DEV_ACTION_UPDATE_IN_APP_TARGETED_COMMS_ELEMENT",
      "DEV_ACTION_MF_DOWNLOAD_OPS_FILE",
      "DEV_ACTION_MF_RE_TRIGGER_PRE_REQUISITES",
      "DEV_ACTION_UPDATE_INSIGHT_FRAMEWORK",
      "DEV_ACTION_UPDATE_INSIGHT_SEGMENT",
      "DEV_ACTION_UPDATE_INSIGHT_CONTENT_TEMPLATE",
      "DEV_ACTION_MF_UPLOAD_CATALOG_UPDATE",
      "DEV_ACTION_MF_UPLOAD_CREDIT_MIS_NON_PROD",
      "DEV_ACTION_ADD_USER_TO_VKYC_PRIORITY",
      "DEV_ACTION_UPDATE_USER_DOB",
      "DEV_ACTION_MANUAL_CARD_UNSUSPEND",
      "DEV_ACTION_UPDATE_USER_PHOTO",
      "DEV_ACTION_MF_PROCESS_REVERSE_FEED_FILE",
      "DEV_ACTION_MF_CREATE_COLLECTION",
      "DEV_ACTION_MF_ADD_FUND_TO_COLLECTION",
      "DEV_ACTION_MF_REMOVE_FUNDS_FROM_COLLECTION",
      "DEV_ACTION_MF_UPDATE_FUND_IN_COLLECTION",
      "DEV_ACTION_MF_UPDATE_COLLECTION",
      "DEV_ACTION_UPDATE_P2P_VENDOR_RESPONSE_APPROVAL_STATUS",
      "DEV_ACTION_PHYSICAL_CARD_REQUEST",
      "DEV_ACTION_CREATE_REFERRALS_SEASON",
      "DEV_ACTION_UPDATE_REFERRALS_SEASON",
      "DEV_ACTION_DELETE_SEGMENT",
      "DEV_ACTION_MARK_STEP_STALE_WEALTH_ONBOARDING",
      "DEV_ACTION_REQUEST_NEW_CARD",
      "DEV_ACTION_TRIGGER_VPA_CREATION",
      "DEV_ACTION_ADD_MANUAL_CALL_ROUTING_MAPPINGS",
      "DEV_ACTION_TRIGGER_SEGMENT_EXPORT",
      "DEV_ACTION_UPDATE_SEGMENT",
      "DEV_ACTION_CREATE_TICKET_DETAILS_TRANSFORMATION",
      "DEV_ACTION_UPDATE_TICKET_DETAILS_TRANSFORMATION",
      "DEV_ACTION_DELETE_TICKET_DETAILS_TRANSFORMATION",
      "DEV_ACTION_MF_RECONCILIATION",
      "DEV_ACTION_DEEPLINK_BASE64_ENCODER",
      "DEV_ACTION_CREDIT_CARD_UPDATE_CARD_REQUEST_STATUS",
      "DEV_ACTION_PRE_APPROVED_LOAN_MANUAL_REVIEW",
      "DEV_ACTION_PRE_APPROVED_LOAN_CREATE_OFFER",
      "DEV_ACTION_PRE_APPROVED_LOAN_UPDATE_LOAN_STATUS",
      "DEV_ACTION_CATEGORISE_SCREENER_DOMAINS",
      "DEV_ACTION_CREATE_SALARY_PROGRAM_REFERRALS_SEASON",
      "DEV_ACTION_SAVINGS_RISK_BANK_ACTIONS",
      "DEV_ACTION_UPDATE_USER_FATHER_NAME",
      "DEV_ACTION_CREATE_DISCOUNT",
      "DEV_ACTION_DELETE_DISCOUNT",
      "DEV_ACTION_UPDATE_ACCOUNT_FREEZE_STATUS_IN_SIMULATOR",
      "DEV_ACTION_INTERNATIONAL_FUND_TRANSFER_UPLOAD_LRS_CHECK_FILE",
      "DEV_ACTION_INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_SWIFT_TRANSFER",
      "DEV_ACTION_INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_INWARD_FUND_TRANSFER",
      "DEV_ACTION_UPDATE_ACCOUNT_FREEZE_STATUS",
      "DEV_ACTION_UPLOAD_USER_ISSUE_INFO_FOR_AGENTS",
      "DEV_ACTION_UPDATE_USER_EMPLOYMENT",
      "DEV_ACTION_CREATE_NUDGE",
      "DEV_ACTION_CREATE_NUDGES_IN_BULK",
      "DEV_ACTION_EDIT_NUDGE",
      "DEV_ACTION_UPDATE_NUDGE_STATUS",
      "DEV_ACTION_GET_DOCKET_URL_WEALTH_ONBOARDING",
      "DEV_ACTION_DEPOSIT_ADD_INTEREST_RATE",
      "DEV_ACTION_DEPOSIT_UPDATE_INTEREST_RATE",
      "DEV_ACTION_DEPOSIT_DELETE_INTEREST_RATE",
      "DEV_ACTION_PROFILE_EVALUATOR_ACTIONS",
      "DEV_ACTION_INCOME_OCCUPATION_DISCREPANCY_VERIFICATION",
      "DEV_ACTION_CREATE_REFERRALS_SEGMENTED_COMPONENT",
      "DEV_ACTION_UPDATE_REFERRALS_SEGMENTED_COMPONENT",
      "DEV_ACTION_DELETE_REFERRALS_SEGMENTED_COMPONENT",
      "DEV_ACTION_UPLOAD_RISK_CASES",
      "DEV_ACTION_GET_SIGNED_URL_WEALTH",
      "DEV_ACTION_USSTOCKS_REFRESH_STOCK_DETAILS",
      "DEV_ACTION_UPLOAD_AML_REPORT",
      "DEV_ACTION_DEACTIVATE_DEVICE",
      "DEV_ACTION_FEDERAL_LOAN_LIVENESS_REVIEW",
      "DEV_ACTION_CREATE_REWARD_OFFER_IN_BULK",
      "DEV_ACTION_CREATE_FOREX_RATE",
      "DEV_ACTION_UPDATE_FOREX_RATE",
      "DEV_ACTION_REPRIEVE_VKYC",
      "DEV_ACTION_AML_UPDATE_FILE_GEN_STATUS",
      "DEV_ACTION_MF_DOWNLOAD_FILE_FROM_BUCKET",
      "DEV_ACTION_FOREX_RATE_REPORT",
      "DEV_ACTION_CURRENT_FOREX_RATE",
      "DEV_ACTION_UPLOAD_WEALTH_DOCUMENT",
      "DEV_ACTION_UPDATE_P2P_VENDOR_RESPONSE_MATURITY_TRANSACTION_DAYS_TO_EXPIRE",
      "DEV_ACTION_PERFORM_RISK_REVIEW_ACTION",
      "DEV_ACTION_CREATE_TEMPORAL_SCHEDULE",
      "DEV_ACTION_GET_FOREX_RATES",
      "DEV_ACTION_PRE_APPROVED_LOAN_LL_UPDATE_ENTITY_STATUS",
      "DEV_ACTION_ADD_HOME_PROMO_BANNER",
      "DEV_ACTION_USSTOCKS_ACCOUNT_STATEMENT",
      "DEV_ACTION_RETRY_MUTUAL_FUND_ORDER",
      "DEV_ACTION_RISK_CREATE_ALLOWED_ANNOTATION",
      "DEV_ACTION_VENDOR_ACCOUNT_PENNY_DROP",
      "DEV_ACTION_RESET_CELESTIAL_WORKFLOW_EXECUTION",
      "DEV_ACTION_CREATE_REFERRAL_NOTIFICATION_CONFIG",
      "DEV_ACTION_UPDATE_REFERRAL_NOTIFICATION_CONFIG_STATUS",
      "DEV_ACTION_UPDATE_REFERRAL_NOTIFICATION_CONFIG_CONTENT",
      "DEV_ACTION_DELETE_REFERRAL_NOTIFICATION_CONFIG",
      "DEV_ACTION_ADD_HOME_POP_UP_BANNER",
      "DEV_ACTION_DEPOSIT_LIST_ACCOUNTS_VENDOR",
      "DEV_ACTION_MARK_LOAN_REQUEST_CANCEL",
      "DEV_ACTION_IFT_GENERATE_AGGREGATED_TAX_REPORT",
      "DEV_ACTION_CREATE_CMS_PRODUCT",
      "DEV_ACTION_CREATE_CMS_SKU",
      "DEV_ACTION_CREATE_CMS_COUPON_IN_BULK",
      "DEV_ACTION_CREATE_HOME_LAYOUT",
      "DEV_ACTION_CREATE_HOME_LAYOUT_SEGMENT_MAPPING",
      "DEV_ACTION_GET_HOME_LAYOUT",
      "DEV_ACTION_GET_HOME_LAYOUT_SEGMENT_MAPPING",
      "DEV_ACTION_DELETE_HOME_LAYOUT",
      "DEV_ACTION_DELETE_HOME_LAYOUT_SEGMENT_MAPPING",
      "DEV_ACTION_CREATE_OR_UPDATE_DYNAMIC_UI_ELEMENT_VARIANT",
      "DEV_ACTION_UPDATE_DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG",
      "DEV_ACTION_MARK_CARD_DELIVERY_TRACKING_STATE_RECEIVED",
      "DEV_ACTION_LOAN_UPDATE_LOAN_STEP_STATUS",
      "DEV_ACTION_USSTOCKS_CREATE_COLLECTION",
      "DEV_ACTION_USSTOCKS_UPDATE_COLLECTION",
      "DEV_ACTION_USSTOCKS_ADD_STOCK_TO_COLLECTION",
      "DEV_ACTION_USSTOCKS_UPDATE_STOCK_IN_COLLECTION",
      "DEV_ACTION_USSTOCKS_REMOVE_STOCK_FROM_COLLECTION",
      "DEV_ACTION_ADD_EMPLOYERS",
      "DEV_ACTION_IFT_REJECT_TRANSACTIONS_IN_OUTWARD_SWIFT_FILE",
      "DEV_ACTION_WHITELIST_EMAIL_ID",
      "DEV_ACTION_CREATE_ISSUE_CONFIG",
      "DEV_ACTION_UPDATE_REWARD_OFFER",
      "DEV_ACTION_GENERATE_CHATBOT_ACCESS_TOKEN",
      "DEV_ACTION_BULK_SETUP_REFERRAL_SEGMENTED_COMPONENTS",
      "DEV_ACTION_USER_COMMS_CONTROL",
      "DEV_ACTION_CREDIT_CARD_UPDATE_CARD_REQUEST_STAGE_STATUS",
      "DEV_ACTION_SIMULATE_USSTOCKS_OUTWARD_FUND_TRANSFER_NON_PROD",
      "DEV_ACTION_P2P_UPDATE_INVESTMENT_TRANSACTION",
      "DEV_ACTION_CREATE_FAQ_CONTEXT_MAPPING",
      "DEV_ACTION_CREATE_HOME_SIMULATOR_LAYOUT",
      "DEV_ACTION_GET_WEALTH_ONBOARDING_DETAILS_BY_PAN",
      "DEV_ACTION_CREATE_WATSON_TICKET_DETAILS",
      "DEV_ACTION_UPDATE_WATSON_TICKET_DETAILS",
      "DEV_ACTION_LOANS_ABFL_ACTIONS",
      "DEV_ACTION_CREATE_EVENT_CONFIG",
      "DEV_ACTION_UPDATE_EVENT_CONFIG",
      "DEV_ACTION_GET_MF_FOLIO_BALANCE",
      "DEV_ACTION_IFT_INITIATE_REFUND",
      "DEV_ACTION_SIMULATE_REWARD_GENERATION",
      "DEV_ACTION_UPDATE_SALARY_PROGRAM_REFERRALS_SEASON",
      "DEV_ACTION_PROCESS_ITC_POINTS_HANDBACK_FILE",
      "DEV_ACTION_PASS_RISK_SCREENER_ATTEMPT",
      "DEV_ACTION_EXTRACT_PAN_FROM_KRA_DOCKET",
      "DEV_ACTION_USSTOCKS_ADD_INVESTOR_ADDRESS",
      "DEV_ACTION_TRIGGER_CATEGORISATION",
      "DEV_ACTION_TRIGGER_TXN_CATEGORISATION",
      "DEV_ACTION_TRIGGER_UNSECURED_CC_RENEWAL_FEE_REVERSAL",
      "DEV_ACTION_USSTOCKS_GENERATE_ADHOC_INWARD_REMITTANCE_FILES",
      "DEV_ACTION_MAP_DEBIT_CARD_FOREX_TXN",
      "DEV_ACTION_CREATE_JOURNEY",
      "DEV_ACTION_UPDATE_JOURNEY",
      "DEV_ACTION_HANDLE_VKYC_CALL_STATE",
      "DEV_ACTION_USSTOCKS_REGENERATE_INWARD_FILE",
      "DEV_ACTION_UPDATE_ACTIVITY_METADATA",
      "DEV_ACTION_UPLOAD_UNIFIED_LEA_COMPLAINTS",
      "DEV_ACTION_DELETE_SERVICE_REQUEST",
      "DEV_ACTION_FAIL_DEBIT_CARD_CREATION",
      "DEV_ACTION_USSTOCKS_TRADING_ACCOUNT_SUMMARY",
      "DEV_ACTION_CACHE_CONTACT_US_MODEL_RESPONSE",
      "DEV_ACTION_B2B_USERS_ONBOARDING_STATUS_TRACKING",
      "DEV_ACTION_B2B_USERS_ONBOARDING_STATUS_LIMITED_DETAILS",
      "DEV_ACTION_USSTOCKS_UPDATE_DETAILS",
      "DEV_ACTION_MARK_WEALTH_ONBOARDING_LIVENESS_PASSED",
      "DEV_ACTION_DEPOSIT_UPDATE_STATE",
      "DEV_ACTION_MARK_WEALTH_ONBOARDING_REDACTION_PASSED",
      "DEV_ACTION_SALARY_PROGRAM_DYNAMIC_UI_ELEMENT_UPDATE_OR_CREATE_VARIANT",
      "DEV_ACTION_SALARY_PROGRAM_DYNAMIC_UI_ELEMENT_UPDATE_OR_CREATE_EVALUATOR_CONFIG",
      "DEV_ACTION_ADD_STOCK_WITH_ISINS",
      "DEV_ACTION_UPLOAD_RISK_REDLIST",
      "DEV_ACTION_SEND_ENACH_MANDATE_NOTIFICATION_CALLBACK",
      "DEV_ACTION_UPLOAD_IMAGE_TO_EPIFI_ICONS_S3_BUCKET",
      "DEV_ACTION_EXPIRE_LOEC",
      "DEV_ACTION_FETCH_UTRS_FROM_LOG",
      "DEV_ACTION_RESET_LOAN_REQUEST"
  ]
  VIEWLIVENESSVIDEO: [
      "/cx.liveness_video.LivenessVideo/GetLivenessVideo",
      "/cx.liveness_video.LivenessVideo/GetFMImage",
      "/cx.liveness_video.LivenessVideo/GetLivenessVideoURL",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
  ]
  SUPERADMIN: [
      "/cx.payout.Payout/GetAllPayoutsList",
      "/cx.payout.Payout/UpdateApprovalStatus",
      "/cx.data_collector.profile.CustomerProfile/GetUserByBankInfo",
      "ADMIN_ACTION_CREATE_CARD",
      "/cx.ticket.ticket/BulkUpdateTickets",
      "/cx.ticket.ticket/GetAllBulkTicketJobs",
      "/cx.ticket.ticket/GetJobFailureLogs",
      "/cx.ticket.ticket/KillJobProcessing",
      "/cx.sherlock_banners.SherlockBanners/CreateBanner",
      "/cx.sherlock_banners.SherlockBanners/BulkCreateBanners",
      "/cx.sherlock_banners.SherlockBanners/UpdateBanner",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "/cx.sherlock_banners.SherlockBanners/DeleteBanners",
  ]
  WAITLISTAPPROVER: [
      "/cx.agent.AgentService/CreateAgent",
      "/cx.agent.AgentService/UpdateAgent",
      "/cx.agent.AgentService/GetAgentInfo",
      "/cx.agent.AgentService/GetAllAgents",
      "/cx.agent.AgentService/RemoveAgent",
      "/cx.waitlist.CxWaitlist/GetFreelanceUserData",
      "/cx.waitlist.CxWaitlist/UpdateApprovalStatus",
      "/cx.sherlock_user.SherlockUserService/CreateSherlockUser",
      "/cx.sherlock_user.SherlockUserService/UpdateSherlockUserInfo",
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.sherlock_user.SherlockUserService/GetAllSherlockUsers",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
  ]
  WAITLISTADMINAPPROVER: [
      "/cx.waitlist.CxWaitlist/GetFreelancerUnmarked",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
  ]
  QALEAD: [
      "/cx.ticket.ticket/GetCallRecording",
      "/cx.ticket.ticket/GetCallTranscript",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
  ]
  FEDERALAGENT: [
      "/cx.agent.AgentService/GetAgentInfo",
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.federal.Federal/GetFederalEscalationTickets",
      "/cx.federal.Federal/GetTicketConversations",
      "/cx.federal.Federal/GetAllTicketGroups",
      "/cx.federal.Federal/UpdateTicketGroup",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
  ]
  RISKOPS: [
      "DEV_ACTION_PROCESS_NON_RESIDENT_CROSS_VALIDATION_MANUAL_REVIEW",
      "/cx.risk_ops.RiskOps/GetDataForCrossValidationManualReview",
      "/cx.liveness_video.LivenessVideo/GetLivenessVideo",
      "/cx.liveness_video.LivenessVideo/GetFMImage",
      "/cx.liveness_video.LivenessVideo/GetLivenessVideoURL",
      "/cx.agent.AgentService/GetAgentInfo",
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.data_collector.kyc.CustomerKYC/GetLivenessSummary",
      "/cx.data_collector.kyc.CustomerKYC/MarkLivenessPassed",
      "/cx.developer.ticket_summary.TicketSummary/GetTicketDetails",
      "/cx.developer.actions.DevActions/GetAvailableActions",
      "/cx.risk_ops.RiskOps/ListAssignedCases",
      "/cx.risk_ops.RiskOps/GetCaseFilters",
      "/cx.risk_ops.RiskOps/GetTransactionsTable",
      "/cx.risk_ops.RiskOps/GetTransactionFilters",
      "/cx.risk_ops.RiskOps/GetCaseDetails",
      "/cx.risk_ops.RiskOps/ListComments",
      "/cx.risk_ops.RiskOps/ListAnnotations",
      "/cx.risk_ops.RiskOps/GetAllowedAnnotations",
      "/cx.risk_ops.RiskOps/CreateComment",
      "/cx.risk_ops.RiskOps/CreateAnnotation",
      "/cx.risk_ops.RiskOps/GetAvailableReviewActions",
      "/cx.risk_ops.RiskOps/GetReviewActionFormElements",
      "/cx.risk_ops.RiskOps/PerformReviewAction",
      "/cx.risk_ops.RiskOps/GetUserProducts",
      "/cx.risk_ops.RiskOps/GetProductUserInfo",
      "/cx.developer.actions.DevActions/GetParameterListForAction",
      "/cx.developer.actions.DevActions/ExecuteAction",
      "/cx.risk_ops.RiskOps/GetPrioritizedCase",
      "/cx.risk_ops.RiskOps/GetRelatedCases",
      "/cx.risk_ops.RiskOps/GetActorActivities",
      "/cx.risk_ops.RiskOps/GetActorActivityAreas",
      "DEV_ACTION_NR_ONBOARDING_PASSPORT_MANUAL_REVIEW",
      "DEV_ACTION_MARK_LIVENESS_PASSED",
      "DEV_ACTION_MARK_FACEMATCH_PASSED",
      "DEV_ACTION_UPLOAD_LEA_COMPLAINTS",
      "DEV_ACTION_START_USER_ACTION",
      "DEV_ACTION_RETRY_LIVENESS",
      "DEV_ACTION_PASS_ONBOARDING_STAGE",
      "DEV_ACTION_ADD_RED_LIST_ENTRY",
      "DEV_ACTION_RESET_KYC_NAME_DOB_RETRY",
      "DEV_ACTION_REMOVE_RED_LIST_ENTRY",
      "DEV_ACTION_UN_NAME_CHECK_UNBLOCK",
      "DEV_ACTION_UPDATE_PAN_NAME_REVIEW",
      "DEV_ACTION_ONBOARDING_TROUBLESHOOTING_DETAILS",
      "/cx.risk_ops.RiskOps/GetLivenessAndFacematchQueue",
      "/cx.risk_ops.RiskOps/DeleteLivenessAndFacematchQueueElement",
      "/cx.risk_ops.RiskOps/GetPendingReviewCount",
      "/cx.data_collector.risk_ops_wealth.RiskOpsWealth/GetWealthDataQueue",
      "/cx.data_collector.risk_ops_wealth.RiskOpsWealth/DeleteWealthDataQueueElement",
      "/cx.risk_ops.RiskOps/AuditLogMediaDownload",
      "/cx.risk_ops.RiskOps/GetUserInfo",
      "/cx.risk_ops.RiskOps/GetL2Details",
      "/cx.risk_ops.RiskOps/ReReview",
      "/cx.risk_ops.RiskOps/GetUserRiskData",
      "/cx.risk_ops.RiskOps/GetAFUData",
      "/cx.data_collector.risk_ops_wealth.RiskOpsWealth/CountWealthDataQueueElements",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "/cx.ticket.ticket/GetSupportTicketsForSherlock",
      "/cx.risk_ops.RiskOps/GetReviewElementsByActorId",
      "/cx.risk_ops.RiskOps/GetTransactionReviewDetails",
      "/cx.risk_ops.RiskOps/ListForms",
      "DEV_ACTION_PRE_APPROVED_LOAN_MANUAL_REVIEW",
      "DEV_ACTION_UPDATE_USER_EMPLOYMENT",
      "DEV_ACTION_INCOME_OCCUPATION_DISCREPANCY_VERIFICATION",
      "DEV_ACTION_UPLOAD_AML_REPORT",
      "DEV_ACTION_RISK_CREATE_ALLOWED_ANNOTATION",
      "DEV_ACTION_REPRIEVE_VKYC",
      "/cx.risk_ops.RiskOps/GetScreenerCheckDetails",
      "/cx.risk_ops.chart.ChartService/GetCharts",
      "/cx.risk_ops.RiskOps/CreateAnnotations",
      "/cx.risk_ops.RiskOps/ListAllowedAnnotations",
  ]
  RISKOPSADMIN: [
      "/cx.risk_ops.RiskOps/CreateAllowedAnnotation",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "DEV_ACTION_UPLOAD_MARKETING_CAMPAIGN_USERS_LIST",
      "DEV_ACTION_SAVINGS_RISK_BANK_ACTIONS",
      "/cx.risk_ops.RiskOps/UpdateRule",
      "/cx.risk_ops.RiskOps/CreateRule",
      "/cx.risk_ops.RiskOps/ListForms",
      "DEV_ACTION_VENDOR_ACCOUNT_PENNY_DROP",
      "/cx.risk_ops.RiskOps/ListRules",
      "/cx.risk_ops.RiskOps/GetAllTags",
      "DEV_ACTION_UPLOAD_LEA_COMPLAINT_NARRATIONS",
      "DEV_ACTION_UPLOAD_LEA_COMPLAINT_SOURCE_DETAILS",
      "DEV_ACTION_RISK_UPLOAD_DISPUTES",
      "DEV_ACTION_RISK_MANAGE_WHITELIST",
      "DEV_ACTION_USSTOCKS_TRADING_ACCOUNT_SUMMARY",
      "DEV_ACTION_GET_SAVINGS_ACCOUNT_CLOSURE_ELIGIBILITY_IN_BULK",
      "DEV_ACTION_UPLOAD_RISK_REDLIST",
      "DEV_ACTION_CREATE_SUGGESTED_ACTION_FOR_RULE",
      "DEV_ACTION_CREATE_RULE_REVIEW_TYPE_MAPPING",
      "/cx.risk_ops.RiskOps/GetTransactionBlocks",
  ]
  FINANCEADMIN: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_VENDOR_ACCOUNT_PENNY_DROP",
  ]
  KYCANALYST: [
      "/cx.risk_ops.RiskOps/ListAssignedCases",
      "/cx.risk_ops.RiskOps/GetCaseFilters",
      "/cx.risk_ops.RiskOps/GetTransactionsTable",
      "/cx.risk_ops.RiskOps/GetTransactionFilters",
      "/cx.risk_ops.RiskOps/GetCaseDetails",
      "/cx.risk_ops.RiskOps/ListComments",
      "/cx.risk_ops.RiskOps/ListAnnotations",
      "/cx.risk_ops.RiskOps/GetAllowedAnnotations",
      "/cx.risk_ops.RiskOps/CreateComment",
      "/cx.risk_ops.RiskOps/CreateAnnotation",
      "/cx.risk_ops.RiskOps/GetAvailableReviewActions",
      "/cx.risk_ops.RiskOps/GetReviewActionFormElements",
      "/cx.risk_ops.RiskOps/PerformReviewAction",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "/cx.risk_ops.RiskOps/GetUserRiskData",
      "/cx.agent.AgentService/GetAgentInfo",
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.risk_ops.RiskOps/GetUserInfo",
      "/cx.risk_ops.RiskOps/GetPrioritizedCase",
  ]
  FITADMIN: [
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
  ]
  BIZADMIN: [
      "/cx.agent.AgentService/CreateAgent",
      "/cx.agent.AgentService/UpdateAgent",
      "/cx.agent.AgentService/GetAgentInfo",
      "/cx.agent.AgentService/GetAllAgents",
      "/cx.agent.AgentService/RemoveAgent",
      "/cx.data_collector.profile.CustomerProfile/GetUserByBankInfo",
      "/cx.data_collector.profile.CustomerProfile/GetBulkUserInfo",
      "/cx.sherlock_user.SherlockUserService/CreateSherlockUser",
      "/cx.sherlock_user.SherlockUserService/UpdateSherlockUserInfo",
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.sherlock_user.SherlockUserService/GetAllSherlockUsers",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
  ]
  BIZADMINRESTRICTED: [
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "/cx.data_collector.profile.CustomerProfile/GetBulkUserInfo",
  ]
  WEALTHDEVELOPER: [
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
  ]
  WEALTHOPS: [
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "DEV_ACTION_MARK_WEALTH_ONBOARDING_LIVENESS_PASSED",
      "DEV_ACTION_MARK_WEALTH_ONBOARDING_REDACTION_PASSED",
      "DEV_ACTION_MARK_WEALTH_ONBOARDING_EXPIRY_PASSED",
      "DEV_ACTION_MUTUAL_FUND_UPLOAD_REVERSE_FEED_FILE",
      "DEV_ACTION_ADD_CREDIT_MIS_FILE_META_DATA",
      "DEV_ACTION_MUTUAL_FUND_DOWNLOAD_CREDIT_MIS_REPORT",
      "DEV_ACTION_MF_DOWNLOAD_OPS_FILE",
      "DEV_ACTION_GET_DOCKET_URL_WEALTH_ONBOARDING",
      "DEV_ACTION_UPDATE_WEALTH_ONBOARDING_STATUS",
      "DEV_ACTION_SYNC_WEALTH_ONBOARDING",
      "DEV_ACTION_MF_RECONCILIATION",
      "DEV_ACTION_UPLOAD_WEALTH_DOCUMENT",
      "DEV_ACTION_MF_RE_TRIGGER_PRE_REQUISITES",
      "DEV_ACTION_UPDATE_WEALTH_USER_INPUT_PENDING_DATA",
      "DEV_ACTION_GET_WEALTH_ONBOARDING_DETAILS_BY_PAN",
      "DEV_ACTION_GET_MF_FOLIO_BALANCE",
      "DEV_ACTION_UPDATE_MUTUAL_FUND",
      "DEV_ACTION_UPDATE_MUTUAL_FUND_ORDER_STATUS",
      "DEV_ACTION_MF_DOWNLOAD_FILE_BY_VENDOR_ORDER_IDS",
      "DEV_ACTION_FETCH_UTRS_FROM_LOG",
  ]
  RECURRINGPAYMENTDEV: [
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "DEV_ACTION_TRIGGER_RECURRING_PAYMENT_EXECUTION",
  ]
  SALARYWHITELISTB2B: [
    "/cx.data_collector.profile.CustomerProfile/GetCustomerProfile",
    "/cx.data_collector.onboarding_status.Onboarding/GetReOOBEDetails",
    "/cx.sherlock_banners.SherlockBanners/GetBannersForCustomer",
    "/cx.data_collector.profile.CustomerProfile/GetCustomerProfile",
    "/cx.data_collector.onboarding_status.Onboarding/WhitelistUsersForOnboarding",
    "/cx.data_collector.onboarding_status.Onboarding/GetOnboardingDetails",
    "/cx.data_collector.kyc.CustomerKYC/GetCustomerKYCData",
    "/cx.data_collector.kyc.CustomerKYC/GetCustomerVKYCData",
    "/cx.data_collector.onboarding_status.Onboarding/GetReOOBEDetails",
    "/cx.sherlock_banners.SherlockBanners/GetBannersForCustomer",
    "/cx.user_issue_info.UserIssueInfoService/GetUserIssueInfo",
    "DEV_ACTION_ADD_EMPLOYERS",
    "DEV_ACTION_ONBOARDING_TROUBLESHOOTING_DETAILS",
    "DEV_ACTION_B2B_USERS_ONBOARDING_STATUS_TRACKING",
    "DEV_ACTION_B2B_USERS_ONBOARDING_STATUS_LIMITED_DETAILS",
    "DEV_ACTION_B2B_UN_NAME_CHECK_FAILURE_EMAIL",
  ]
  WEBDEVELOPER: [
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
  ]
  REWARDSADMIN: [
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "DEV_ACTION_CREATE_REWARD_OFFER",
    "DEV_ACTION_UPDATE_REWARD_OFFER_STATUS",
    "DEV_ACTION_UPDATE_REWARD_OFFER_DISPLAY",
    "DEV_ACTION_UPDATE_REWARD_OFFER_DISPLAY_RANK",
    "DEV_ACTION_CREATE_LUCKY_DRAW_CAMPAIGN",
    "DEV_ACTION_CREATE_LUCKY_DRAW",
    "DEV_ACTION_CREATE_EXCHANGER_OFFER",
    "DEV_ACTION_CREATE_EXCHANGER_OFFER_GROUP",
    "DEV_ACTION_CREATE_EXCHANGER_OFFER_LISTING",
    "DEV_ACTION_UPDATE_EXCHANGER_OFFER_DISPLAY",
    "DEV_ACTION_UPDATE_EXCHANGER_OFFER_STATUS",
    "DEV_ACTION_UPDATE_EXCHANGER_OFFER_LISTING",
    "DEV_ACTION_DELETE_EXCHANGER_OFFER_LISTING",
    "DEV_ACTION_CREATE_EXCHANGER_OFFER_INVENTORY",
    "DEV_ACTION_INCREMENT_EXCHANGER_OFFER_INVENTORY",
    "DEV_ACTION_CREATE_OFFER",
    "DEV_ACTION_CREATE_OFFERS_IN_BULK",
    "DEV_ACTION_CREATE_OFFER_LISTING",
    "DEV_ACTION_UPDATE_OFFER_LISTING",
    "DEV_ACTION_DELETE_OFFER_LISTING",
    "DEV_ACTION_CREATE_OFFER_INVENTORY",
    "DEV_ACTION_ADD_OFFER_TO_INVENTORY",
    "DEV_ACTION_DELETE_OFFER_INVENTORY",
    "DEV_ACTION_UPDATE_OFFER_DISPLAY",
    "DEV_ACTION_UPDATE_OFFER_DISPLAY_RANK",
    "DEV_ACTION_SEND_REWARDS_CAMPAIGN_COMM",
    "DEV_ACTION_FORCE_PROCESS_ORDER",
    "DEV_ACTION_RETRY_REWARD_PROCESSING",
    "DEV_ACTION_CREATE_REWARD_OFFER_GROUP",
    "DEV_ACTION_RETRY_OFFER_REDEMPTION",
    "DEV_ACTION_TRIGGER_REWARDS_MANUAL_GIVEAWAY_EVENT",
    "DEV_ACTION_UNLOCK_IN_APP_REFERRAL",
    "DEV_ACTION_CREATE_REFERRALS_SEASON",
    "DEV_ACTION_UPDATE_REFERRALS_SEASON",
    "DEV_ACTION_DELETE_SEGMENT",
    "DEV_ACTION_TRIGGER_SEGMENT_EXPORT",
    "DEV_ACTION_CREATE_SEGMENT",
    "DEV_ACTION_UPDATE_SEGMENT",
    "DEV_ACTION_CREATE_SALARY_PROGRAM_REFERRALS_SEASON",
    "DEV_ACTION_CREATE_DISCOUNT",
    "DEV_ACTION_DELETE_DISCOUNT",
    "DEV_ACTION_CREATE_REFERRALS_SEGMENTED_COMPONENT",
    "DEV_ACTION_UPDATE_REFERRALS_SEGMENTED_COMPONENT",
    "DEV_ACTION_DELETE_REFERRALS_SEGMENTED_COMPONENT",
    "DEV_ACTION_CREATE_REWARD_OFFER_IN_BULK",
    "DEV_ACTION_CREATE_CMS_PRODUCT",
    "DEV_ACTION_CREATE_CMS_SKU",
    "DEV_ACTION_CREATE_CMS_COUPON_IN_BULK",
    "DEV_ACTION_UPDATE_REWARD_OFFER",
    "DEV_ACTION_UPDATE_EMPLOYER_DETAILS",
    "DEV_ACTION_ONBOARDING_TROUBLESHOOTING_DETAILS",
  ]
  ACCOUNTOPS: [
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.data_collector.account.Account/BulkAccountValidations",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.data_collector.account.Account/StoreClosedAccountBalance",
    "/cx.data_collector.account.Account/StoreClosedAccBalTransferUtr",
  ]
  SALARYDATAOPS: [
    "/cx.data_collector.profile.CustomerProfile/GetCustomerProfile",
    "/cx.data_collector.onboarding_status.Onboarding/GetOnboardingDetails",
    "/cx.data_collector.kyc.CustomerKYC/GetCustomerKYCData",
    "/cx.data_collector.kyc.CustomerKYC/GetCustomerVKYCData",
    "/cx.data_collector.onboarding_status.Onboarding/GetReOOBEDetails",
    "/cx.sherlock_banners.SherlockBanners/GetBannersForCustomer",
    "/cx.user_issue_info.UserIssueInfoService/GetUserIssueInfo",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.data_collector.salarydataops.SalaryDataOps/UpdateUsersEmploymentDetails",
    "/cx.data_collector.salarydataops.SalaryDataOps/SearchUsingGSTINOrName",
    "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryTxnVerificationRequestsByStatus",
    "/cx.data_collector.salarydataops.SalaryDataOps/UpdateVerificationRequestStatus",
    "/cx.data_collector.salarydataops.SalaryDataOps/GetUserDetailsNeededForVerification",
    "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryAccountDetailsForAgents",
    "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryAccountDetailsForSalaryDataOps",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.data_collector.salarydataops.SalaryDataOps/GetPossibleSalaryTxnsForActor",
    "/cx.data_collector.salarydataops.SalaryDataOps/GetUserDetailsNeededForRaisingSalaryVerification",
    "/cx.data_collector.salarydataops.SalaryDataOps/GetPossibleActorsForSalaryVerification",
    "/cx.data_collector.salarydataops.SalaryDataOps/UpdateSalaryVerificationEligibilityStatus",
    "/cx.data_collector.salarydataops.SalaryDataOps/RaiseAndVerifySalaryTxnVerificationReq",
    "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryTxnVerificationRequestsCount",
    "/cx.data_collector.salarydataops.SalaryDataOps/GetDetailedSalaryTxnVerificationRequestsByFilter",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_ADD_EMPLOYERS",
    "/cx.data_collector.salarydataops.SalaryDataOps/GetSalaryTxnVerificationRequestsForSalaryDataOps",
    "DEV_ACTION_ONBOARDING_TROUBLESHOOTING_DETAILS",
    "DEV_ACTION_RAISE_MANUAL_SALARY_VERIFICATION_REQUESTS_IN_BULK",
    "DEV_ACTION_B2B_USERS_ONBOARDING_STATUS_TRACKING",
    "DEV_ACTION_B2B_USERS_ONBOARDING_STATUS_LIMITED_DETAILS",
    "DEV_ACTION_LEAD_MGMT_DOWNLOAD_FILE",
    "DEV_ACTION_B2B_UN_NAME_CHECK_FAILURE_EMAIL",
  ]
  SALARYADMIN: [
    "/cx.data_collector.profile.CustomerProfile/GetCustomerProfile",
    "/cx.data_collector.onboarding_status.Onboarding/GetOnboardingDetails",
    "/cx.data_collector.kyc.CustomerKYC/GetCustomerKYCData",
    "/cx.data_collector.kyc.CustomerKYC/GetCustomerVKYCData",
    "/cx.data_collector.onboarding_status.Onboarding/GetReOOBEDetails",
    "/cx.sherlock_banners.SherlockBanners/GetBannersForCustomer",
    "/cx.user_issue_info.UserIssueInfoService/GetUserIssueInfo",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_UPDATE_EMPLOYER_DETAILS",
    "DEV_ACTION_B2B_USERS_ONBOARDING_STATUS_TRACKING",
    "DEV_ACTION_B2B_USERS_ONBOARDING_STATUS_LIMITED_DETAILS",
    "DEV_ACTION_B2B_UN_NAME_CHECK_FAILURE_EMAIL",
  ]
  DATAOPS: [
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_CATEGORISE_SCREENER_DOMAINS",
  ]
  JUMPSENSITIVEVENDORRESPONSE: [
    "/cx.agent.AgentService/GetAgentInfo",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.app_log.AppLog/GetExportedLogsList",
    "/cx.app_log.AppLog/GetLogsData",
    "/cx.developer.ticket_summary.TicketSummary/GetTicketDetails",
    "/cx.developer.ticket_summary.TicketSummary/GetTicketConversations",
    "/cx.developer.db_state.DBState/GetEntityListForService",
    "/cx.developer.db_state.DBState/GetServiceList",
    "/cx.developer.db_state.DBState/GetParameterListForEntity",
    "/cx.developer.db_state.DBState/GetDataForEntity",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "DEV_ACTION_P2P_GET_INVESTOR_DASHBOARD",
    "DEV_ACTION_P2P_GET_INVESTMENT_SUMMARY",
    "DEV_ACTION_P2P_DOWNLOAD_RECON_FILE",
    "DEV_ACTION_P2P_REGISTER_BANKING_DETAILS",
    "DEV_ACTION_P2P_GET_CASH_LEDGER",
  ]
  JUMPOPS: [
    "/cx.agent.AgentService/GetAgentInfo",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.app_log.AppLog/GetExportedLogsList",
    "/cx.app_log.AppLog/GetLogsData",
    "/cx.developer.ticket_summary.TicketSummary/GetTicketDetails",
    "/cx.developer.ticket_summary.TicketSummary/GetTicketConversations",
    "/cx.developer.db_state.DBState/GetEntityListForService",
    "/cx.developer.db_state.DBState/GetServiceList",
    "/cx.developer.db_state.DBState/GetParameterListForEntity",
    "/cx.developer.db_state.DBState/GetDataForEntity",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "DEV_ACTION_P2P_DOWNLOAD_RECON_FILE",
    "DEV_ACTION_P2P_REGISTER_BANKING_DETAILS",
    "DEV_ACTION_P2P_UPDATE_INVESTMENT_TRANSACTION",
    "DEV_ACTION_P2P_GET_CASH_LEDGER",
    "DEV_ACTION_P2P_GET_INVESTMENT_SUMMARY",
  ]
  RISKENG: [
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "DEV_ACTION_UPLOAD_RISK_CASES",
  ]
  FEDERALLOANLIVENESSREVIEWER: [
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.risk_ops.RiskOps/GetLivenessAndFacematchQueue",
    "DEV_ACTION_FEDERAL_LOAN_LIVENESS_REVIEW",
    "/cx.liveness_video.LivenessVideo/GetLivenessVideoURL",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.risk_ops.RiskOps/GetPendingReviewCount",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "/cx.risk_ops.RiskOps/DeleteLivenessAndFacematchQueueElement",
    "/cx.risk_ops.RiskOps/GetUserInfo",
    "/cx.data_collector.kyc.CustomerKYC/GetLivenessSummary",
    "/cx.risk_ops.RiskOps/DownloadFile",
  ]
  FEDERALINWARDREMITTER: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetNavigationBarEntities",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetFiltersForFileType",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetFileEntries",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetAdditionalActions",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GenerateFile",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/AcknowledgeFileEntry",
    "DEV_ACTION_INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_INWARD_FUND_TRANSFER",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/DownloadFile",
  ]
  FEDERALOUTWARDREMITTER: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetNavigationBarEntities",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetFiltersForFileType",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetFileEntries",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetAdditionalActions",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GenerateFile",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/AcknowledgeFileEntry",
    "DEV_ACTION_INTERNATIONAL_FUND_TRANSFER_UPLOAD_LRS_CHECK_FILE",
    "DEV_ACTION_INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_SWIFT_TRANSFER",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/DownloadFile",
    "DEV_ACTION_IFT_REJECT_TRANSACTIONS_IN_OUTWARD_SWIFT_FILE",
  ]
  FEDERALMASTERREMITTER: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetNavigationBarEntities",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetFiltersForFileType",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetFileEntries",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetAdditionalActions",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GenerateFile",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/AcknowledgeFileEntry",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetLRSCheckEssentials",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/ProcessLRSCheckResult",
    "DEV_ACTION_INTERNATIONAL_FUND_TRANSFER_UPLOAD_LRS_CHECK_FILE",
    "DEV_ACTION_INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_SWIFT_TRANSFER",
    "DEV_ACTION_INTERNATIONAL_FUND_TRANSFER_ACKNOWLEDGE_INWARD_FUND_TRANSFER",
    "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/DownloadFile",
    "DEV_ACTION_IFT_REJECT_TRANSACTIONS_IN_OUTWARD_SWIFT_FILE",
  ]
  FEDERALMASTERREMITTANCEOPS: [
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetNavigationBarEntities",
      "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetFiltersForFileType",
      "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetFileEntries",
      "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GetAdditionalActions",
      "/cx.data_collector.pay.internationalfundtransfer.InternationalFundTransfer/GenerateFile",
  ]
  USSTOCKSOPS: [
      "/cx.sherlock_banners.SherlockBanners/GetBanners",
      "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
      "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
      "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetReviewItems",
      "/cx.data_collector.investment.usstocks.UsStocksInvestment/GetReviewItemDetails",
      "/cx.data_collector.investment.usstocks.UsStocksInvestment/MarkReviewAsDone",
      "/cx.developer.actions.DevActions/GetAvailableActions",
      "/cx.developer.actions.DevActions/GetParameterListForAction",
      "/cx.developer.actions.DevActions/ExecuteAction",
      "DEV_ACTION_CREATE_FOREX_RATE",
      "DEV_ACTION_UPDATE_FOREX_RATE",
      "DEV_ACTION_FOREX_RATE_REPORT",
      "DEV_ACTION_CURRENT_FOREX_RATE",
      "DEV_ACTION_USSTOCKS_DOWNLOAD_FILE_FROM_BUCKET",
      "DEV_ACTION_IFT_GENERATE_AGGREGATED_TAX_REPORT",
      "DEV_ACTION_USSTOCKS_CREATE_BANK_RELATIONSHIP_WITH_BROKER",
      "DEV_ACTION_SOF_LIMIT_MANUAL_OVERRIDE",
      "DEV_ACTION_ADD_PIN_CODE_ENTRY",
      "DEV_ACTION_USSTOCKS_RESET_ONBOARDING_DATA",
      "DEV_ACTION_USSTOCKS_REGENERATE_INWARD_FILE",
      "DEV_ACTION_DELETE_USER_ASSETS",
  ]
  USSTOCKSSENSITIVEDATA: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_USSTOCKS_DOWNLOAD_FILE_FROM_BUCKET",
    "DEV_ACTION_USSTOCKS_ACCOUNT_STATEMENT",
    "DEV_ACTION_GET_US_STOCK_ACCOUNT_ACTIVITIES_CSV",
  ]
  MUTUALFUNDSSENSITIVEDATA: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_MF_DOWNLOAD_FILE_FROM_BUCKET",
  ]
  WEALTHONBOARDINGSENSITIVEDATA: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_GET_SIGNED_URL_WEALTH",
    "DEV_ACTION_GET_DOCKET_URL_WEALTH_ONBOARDING",
    "DEV_ACTION_GET_WEALTH_ONBOARDING_DETAILS_BY_PAN",
  ]
  USEROUTCALL: [
    "/cx.risk_ops.RiskOps/ListAssignedCases",
    "/cx.risk_ops.RiskOps/GetCaseFilters",
    "/cx.risk_ops.RiskOps/GetCaseDetails",
    "/cx.risk_ops.RiskOps/ListComments",
    "/cx.risk_ops.RiskOps/GetAvailableReviewActions",
    "/cx.risk_ops.RiskOps/GetReviewActionFormElements",
    "/cx.risk_ops.RiskOps/PerformReviewAction",
    "/cx.risk_ops.RiskOps/GetUserRiskData",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
  ]
  KYCAGENTADMIN: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_CREATE_KYC_AGENT",
    "DEV_ACTION_DELETE_KYC_AGENT",
  ]
  DATARETRIEVER: [
    "/cx.data_collector.profile.CustomerProfile/GetCustomerProfile",
    "/cx.data_collector.onboarding_status.Onboarding/GetOnboardingDetails",
    "/cx.data_collector.kyc.CustomerKYC/GetCustomerKYCData",
    "/cx.data_collector.kyc.CustomerKYC/GetCustomerVKYCData",
    "/cx.data_collector.onboarding_status.Onboarding/GetReOOBEDetails",
    "/cx.sherlock_banners.SherlockBanners/GetBannersForCustomer",
    "/cx.user_issue_info.UserIssueInfoService/GetUserIssueInfo",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_DATA_EXTRACTION",
    "DEV_ACTION_SEGMENT_METADATA_ACTION",
  ]
  SEGMENTATIONADMIN: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_SEGMENT_METADATA_ACTION",
    "DEV_ACTION_SET_SEGMENT_METADATA_APPROVAL_STATUS",
  ]
  CXSUPPORTLLMTESTER: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_GENERATE_MODEL_RESPONSE_FOR_USERS_CX_QUERY",
  ]
  VKYC_CALL_AGENT: [
    "/cx.data_collector.vkyccall.VkycCall/GetAvailableCalls",
    "/cx.data_collector.vkyccall.VkycCall/GetAvailableCallsCount",
    "/cx.data_collector.vkyccall.VkycCall/InitiateAgentCall",
    "/cx.data_collector.vkyccall.VkycCall/ConcludeVkycCall",
    "/cx.data_collector.vkyccall.VkycCall/GetUserLocation",
    "/cx.data_collector.vkyccall.VkycCall/CaptureScreenshot",
    "/cx.data_collector.vkyccall.VkycCall/GetOnboardingStages",
    "/cx.data_collector.vkyccall.VkycCall/PerformClientAction",
    "/cx.data_collector.vkyccall.VkycCall/EndVkycCall",
    "/cx.data_collector.vkyccall.VkycCall/ExtractDataFromDocumentImage",
    "/cx.data_collector.vkyccall.VkycCall/GetMatchScore",
    "/cx.data_collector.vkyccall.VkycCall/GenerateVKYCCallReport",
    "/cx.data_collector.vkyccall.VkycCall/MeetingHealthCheck",
  ]
  VKYC_CALL_AUDITOR: [
    "/cx.data_collector.vkyccall.VkycCall/GetAvailableReports",
    "/cx.data_collector.vkyccall.VkycCall/GetAvailableReportsCount",
    "/cx.data_collector.vkyccall.VkycCall/InitiateAuditorReview",
    "/cx.data_collector.vkyccall.VkycCall/ConcludeAuditorReview",
  ]
  STOCKGUARDIANVKYCCALLAGENT: [
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.risk_ops.RiskOps/GetLivenessAndFacematchQueue",
    "/cx.risk_ops.RiskOps/DeleteLivenessAndFacematchQueueElement",
    "/cx.risk_ops.RiskOps/UploadManuallyReviewedDocuments",
    "/cx.stockguardian.kyc.KYC/UploadManuallyReviewedDocuments",
    "/cx.stockguardian.kyc.KYC/GetS3PreSignedURL",
    "DEV_ACTION_STOCK_GUARDIAN_REDACT_CKYC_DOCUMENT",
    "DEV_ACTION_STOCK_GUARDIAN_UPDATE_EMPLOYMENT_DETAILS"
  ]
  STOCKGUARDIANVKYCCALLAUDITOR: [
  ]
  STOCKGUARDIANLENDINGOPS: [
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_STOCK_GUARDIAN_EXECUTE_EMI_MANDATE"
  ]
  STOCKGUARDIANDEVELOPER: [
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.db_state.DBState/GetEntityListForService",
    "/cx.developer.db_state.DBState/GetServiceList",
    "/cx.developer.db_state.DBState/GetParameterListForEntity",
    "/cx.developer.db_state.DBState/GetDataForEntity",
  ]
  LOANSUSEROUTCALL: [
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetLoanUserDetails",
    "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetLoanDetails",
    "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetForeClosureDetails",
    "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetLoanAdditionalDetails",
    "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetLoanOutCallAgentAssignedTickets",
    "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetLoanOutCallTicketHistory",
    "/cx.data_collector.preapprovedloan.PreApprovedLoan/GetUserProfileInfo",
    "DEV_ACTION_UPDATE_LOANS_OUTCALL_TICKET"
  ]
  ESCALATIONS: [
    "/cx.sherlock_banners.SherlockBanners/GetBanners",
    "/cx.sherlock_user.SherlockUserService/GetSherlockUser",
    "/cx.sherlock_user.SherlockUserService/FetchLoggedInUserInfo",
    "/cx.developer.actions.DevActions/GetAvailableActions",
    "/cx.developer.actions.DevActions/GetParameterListForAction",
    "/cx.developer.actions.DevActions/ExecuteAction",
    "DEV_ACTION_FEDERAL_ESCALATION_CREATION"
  ]

GroupedRolesMap:
  ADMIN: [
      "AGENT",
  ]
  VIEWLIVENESSVIDEO: [
      "DEVELOPER",
  ]
  QA: [
      "ADMIN",
  ]
  SUPERADMIN: [
      "ADMIN",
  ]
  WAITLISTADMINAPPROVER: [
      "WAITLIST_APPROVER",
  ]
  QALEAD: [
      "AGENT",
  ]
  FITADMIN: [
      "DEVELOPER",
  ]
  ADMINRESTRICTED: [
      "AGENT",
  ]
  WEALTHDEVELOPER: [
      "DEVELOPER",
  ]
  WEALTHOPS: [
    "RISK_OPS",
  ]
  RECURRINGPAYMENTDEV: [
    "DEVELOPER",
  ]
  RISKOPSADMIN: [
    "RISK_OPS",
  ]
  BIZADMIN: [
      "BIZ_ADMIN_RESTRICTED",
  ]
  FINANCEADMIN: [
    "FINANCE_ADMIN",
  ]
  STOCKGUARDIANVKYCCALLAGENT: [
    "VKYC_CALL_AGENT",
  ]
  STOCKGUARDIANVKYCCALLAUDITOR: [
    "VKYC_CALL_AUDITOR",
  ]


Tracing:
  Enable: true

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true
