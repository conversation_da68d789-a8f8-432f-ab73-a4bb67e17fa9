// nolint:gosec,gocritic
package utils

import (
	"context"
	"fmt"
	"math/rand"
	"regexp"

	"github.com/epifi/be-common/pkg/epifigrpc"
	githubpkg "github.com/epifi/be-common/pkg/github"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/owners"
	"github.com/epifi/be-common/pkg/owners/config"
	"go.uber.org/zap"

	slackPb "github.com/epifi/gamma/api/vendorgateway/slack_bot"
	"github.com/epifi/gamma/api/vendorgateway/slack_bot/types"
	"github.com/epifi/gamma/slack_bot/config/genconf"
)

func FetchTicketLabel(ctx context.Context, gconf *genconf.Config, channelName string) string {
	var ticketLabel string
	if gconf.ChannelsDetail().Get(channelName) != nil {
		ticketLabel = gconf.ChannelsDetail().Get(channelName).Label()
	}
	if ticketLabel != "" {
		logger.Info(ctx, "ticket label found", zap.String("ticketLabel", ticketLabel))
	}
	return ticketLabel
}

func GetAllBusinessUnits(businessUnitDetail map[owners.BusinessUnit]*config.BusinessUnit, channelName string) []string {
	var businessUnit []string
	for key, _ := range businessUnitDetail {
		if key != "all" {
			businessUnit = append(businessUnit, string(key))
		}
	}
	if channelName == "user-feedback" {
		businessUnit = append(businessUnit, "unknown")
	}
	return businessUnit
}

// GetIssueTitle return issue title by truncating description to 100 char
func GetIssueTitle(ticketDesc string) string {
	re := regexp.MustCompile(`<@.*?>`)
	ticketDesc = re.ReplaceAllString(ticketDesc, "")
	issueTitle := ticketDesc
	if len(ticketDesc) > 100 {
		issueTitle = ticketDesc[:100] + " ..."
	}
	return issueTitle
}

// GetOwnerAndAttachmentText determines the appropriate ticket owner and constructs a message text for tagging in the channel.
// If an owner is specified in the issue form, it uses that owner directly for tagging.
// If the config includes onCall or owner, it sets them as the ticket owner and tags them directly.
// If neither onCall nor owner is present in the config, it assigns the engineering lead as the ticket owner and tags the onCall group.
func GetOwnerAndAttachmentText(ctx context.Context, conf *genconf.Config, slackVgClient slackPb.SlackBotClient, businessUnitDetail map[owners.BusinessUnit]*config.BusinessUnit, operationsUnitDetail map[owners.OperationUnit]*config.OperationUnit, owner, channelName, businessUnit, requesterID string) (string, string, error) {
	var (
		ticketOwner string
		text        string
	)

	switch {
	case businessUnit == "unknown":
		lead := operationsUnitDetail["customer-operations-team"].Lead
		onCallUserHandle := string(operationsUnitDetail["customer-operations-team"].OncallUserHandle)
		onCallGroupId := operationsUnitDetail["customer-operations-team"].OnCallGroupId
		text = fmt.Sprintf("<!subteam^%s|%s> Issue Raised by <@%s>", onCallGroupId, onCallUserHandle, requesterID)
		return string(lead), text, nil

	case owner != "":
		user, err := GetUserDetail(ctx, slackVgClient, owner)
		if err != nil {
			logger.Error(ctx, "Error in getting user detail for owner", zap.Error(err))
			return "", "", fmt.Errorf("error in getting user detail for owner: %w", err)
		}
		ticketOwner = user[0].GetProfile().GetEmail()
		userToTag := fmt.Sprintf("<@%s>", user[0].GetId())
		text = fmt.Sprintf("%s Issue Raised by <@%s>", userToTag, requesterID)
		return ticketOwner, text, nil

	case conf.ChannelsDetail().Get(channelName) != nil:
		channelDetails := conf.ChannelsDetail().Get(channelName)
		if buToTicketOwner := channelDetails.BUToTicketOwnerMap(); buToTicketOwner != nil {
			ticketOwnerId := buToTicketOwner.Get(businessUnit)
			if ticketOwnerId != "" {
				user, err := GetUserDetail(ctx, slackVgClient, ticketOwnerId)
				if err != nil {
					logger.Error(ctx, "Error in getting user detail for owner", zap.Error(err))
					return "", "", fmt.Errorf("error in getting user detail for owner: %w", err)
				}
				email := user[0].GetProfile().GetEmail()
				tag := fmt.Sprintf("<@%s>", user[0].GetId())
				text = fmt.Sprintf("%s Issue Raised by <@%s>", tag, requesterID)
				return email, text, nil
			}
		}
		if buToOnCall := channelDetails.BUToOnCallMap(); buToOnCall != nil {
			ticketOwnerId := buToOnCall.Get(businessUnit)
			if ticketOwnerId != "" {
				user, err := GetUserDetail(ctx, slackVgClient, ticketOwnerId)
				if err != nil {
					logger.Error(ctx, "Error in getting user detail for owner", zap.Error(err))
					return "", "", fmt.Errorf("error in getting user detail for owner: %w", err)
				}
				email := user[0].GetProfile().GetEmail()
				tag := fmt.Sprintf("<@%s>", user[0].GetId())
				text = fmt.Sprintf("%s Issue Raised by <@%s>", tag, requesterID)
				return email, text, nil
			}
		}
		fallthrough // If not found in channel config, fallback to business unit config

	default:
		// --- Ticket Owner Selection Logic ---
		// 1. If a user group is configured for the business unit (OnCallGroupId), fetch its members from Slack.
		// 2. If the group is empty, fallback to the engineering lead (enggLead) as the ticket owner and tag the whole group in Slack.
		// 3. If the group has exactly one user, assign that user as the ticket owner and tag them.
		// 4. If the group has multiple users, filter out the engineering lead (if present), then randomly select one user from the remaining group as the ticket owner and tag them.
		// -------------------------------------
		buKey := owners.BusinessUnit(businessUnit)
		buDetail, ok := businessUnitDetail[buKey]
		if !ok {
			return "", "", fmt.Errorf("business unit %s not found", businessUnit)
		}
		userGroupId := buDetail.OnCallGroupId
		enggLead := string(buDetail.EnggLead)
		var groupUsers []*types.User
		if userGroupId != "" {
			resp, respErr := slackVgClient.GetUserGroupMembers(ctx, &slackPb.GetUserGroupMembersRequest{UserGroupId: userGroupId})
			if err := epifigrpc.RPCError(resp, respErr); err != nil {
				logger.Error(ctx, "error in fetching usergroup members", zap.Error(err))
				return "", "", fmt.Errorf("error in fetching usergroup members: %w", err)
			}
			groupUsers = resp.GetUsers()
		}
		if len(groupUsers) == 0 {
			user, err := GetUserDetail(ctx, slackVgClient, enggLead)
			if err != nil {
				logger.Error(ctx, "Error in getting user detail for owner", zap.Error(err))
				return "", "", fmt.Errorf("error in getting user detail for owner: %w", err)
			}
			text = fmt.Sprintf("<@%s> Issue Raised by <@%s>", user[0].GetId(), requesterID)
			return enggLead, text, nil
		}
		if len(groupUsers) == 1 {
			selectedUser := groupUsers[0]
			email := selectedUser.GetProfile().GetEmail()
			text = fmt.Sprintf("<@%s> Issue Raised by <@%s>", selectedUser.GetId(), requesterID)
			return email, text, nil
		}
		var filtered []*types.User
		for _, u := range groupUsers {
			if u.GetProfile().GetEmail() != enggLead {
				filtered = append(filtered, u)
			}
		}
		var selectedUser *types.User
		if len(filtered) > 0 {
			idx := rand.Intn(len(filtered))
			selectedUser = filtered[idx]
		} else {
			selectedUser = groupUsers[0]
		}
		email := selectedUser.GetProfile().GetEmail()
		text = fmt.Sprintf("<@%s> Issue Raised by <@%s>", selectedUser.GetId(), requesterID)
		return email, text, nil
	}
}

func GetConfigTicketLabels(gconf *genconf.Config) []string {
	ticketLabelsArray := gconf.TicketLabels()
	var labels []string
	for i := 0; i < ticketLabelsArray.Len(); i++ {
		labels = append(labels, ticketLabelsArray.At(i))
	}
	return labels
}

func LoadIssueFormFieldsOptions(ctx context.Context, githubApiWrapper githubpkg.IGithubApiWrapper) (map[string][]*types.OptionBlockObject, error) {
	optionsMap := make(map[string][]*types.OptionBlockObject)
	templateOptions, optionErr := GetIssueTemplateOptions(ctx, githubApiWrapper, GITHUB_REPO_OWNER, GITHUB_TICKETS_REPO_NAME)
	if optionErr != nil {
		logger.Error(ctx, "error in fetching templates from GitHub", zap.Error(optionErr))
		return nil, fmt.Errorf("error in fetching templates from GitHub: %w", optionErr)
	}
	projectOptions, err := GetProjectOptions(ctx, githubApiWrapper)
	if err != nil {
		logger.Error(ctx, "Error fetching projects from GitHub", zap.Error(err))
		return nil, fmt.Errorf("error fetching projects from GitHub: %w", err)
	}
	optionsMap[ISSUE_TEMPLATE_FIELD_NAME] = templateOptions
	optionsMap[PROJECT_FIELD_NAME] = projectOptions
	return optionsMap, nil
}
