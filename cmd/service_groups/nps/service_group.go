// nolint:unused
package nps

import (
	"github.com/epifi/be-common/tools/servergen/meta"

	npspb "github.com/epifi/gamma/api/nps/catalog"
	npsdev "github.com/epifi/gamma/api/nps/developer"
	"github.com/epifi/gamma/nps/wire"
)

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer:     wire.InitializeNPSService,
		GRPCRegisterMethods: []any{npspb.RegisterNpsCatalogServer},
	},
	{
		WireInitializer:     wire.InitializeDevNpsService,
		GRPCRegisterMethods: []any{npsdev.RegisterNpsDbStatesServer},
	},
}
