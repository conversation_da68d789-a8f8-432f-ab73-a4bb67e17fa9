package rules

import (
	"context"
	"fmt"

	"buf.build/go/bufplugin/check"
	"buf.build/go/bufplugin/descriptor"
	"google.golang.org/protobuf/types/descriptorpb"
)

// CheckJavaPackageConvention checks that the java package option in typesv2 commons package is correct.
// while specifying options java_package, we must adhere to the specifications necessary for the code to be generated in
// the right path in Android codebase
func CheckJavaPackageConvention(
	_ context.Context,
	responseWriter check.ResponseWriter,
	_ check.Request,
	fileDescriptor descriptor.FileDescriptor,
) error {
	protoFileDescriptor := fileDescriptor.ProtoreflectFileDescriptor()

	pack := protoFileDescriptor.Package()
	fileOptions := protoFileDescriptor.Options().(*descriptorpb.FileOptions)

	javaPackageFromFile := fileOptions.GetJavaPackage()
	if javaPackageFromFile != fmt.Sprintf("com.github.epifi.gamma.%s", pack) {
		responseWriter.AddAnnotation(
			check.WithDescriptor(protoFileDescriptor),
			check.WithMessagef(
					" Invalid Java package name, should be: com.github.epifi.gamma.%s, but found: %s",
					pack,
					javaPackageFromFile,
				),
			)
		return nil
	}
	return nil
}
