ServerPorts:
  Port: 8150
  GrpcPort: 9090
  GrpcHttpPort: 9091
  HealthCheckPort: 9889
  UnsecureHttpPort: 8079

Databases:
  JarvisPGDB:
    DbType: "PGDB"
    DbServerAlias: "JARVIS_RDS"
    SecretName: "{\"username\": \"root\", \"password\": \"\"}"
    AppName: "jarvis"
    StatementTimeout: 5s
    Name: "jarvis_test"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 10
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

RedisRateLimiterName: "JarvisRedisStore"
RedisClusters:
  JarvisRedisStore:
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 0

GrpcRateLimiterParams:
  Disable: true
