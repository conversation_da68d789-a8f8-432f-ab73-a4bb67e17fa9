Application:
  Environment: "uat"
  Namespace: "uat-recurring-payment"
  TaskQueue: "uat-recurring-payment-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: ""
      DB: 0

Secrets:
  Ids:
    EnachSecrets: "uat/recurringpayment/enach/secrets"
    PayDbUsernamePassword: "uat/rds/postgres/pay_dev_user"
    TemporalCodecAesKey: "uat/temporal/codec-encryption-key"

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "recurring-payment-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DbType: "CRDB"
    AppName: "recurring-payment-worker"
    StatementTimeout: 1s
    Username: "liquiloans_epifi_dev_user"
    Password: ""
    Name: "liquiloans_epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.liquiloans_epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.liquiloans_epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH_V2:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "recurring-payment-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  STOCK_GUARDIAN_TSP:
    DBType: "CRDB"
    Username: "stockguardian_tsp_crdb_dev_user"
    Password: ""
    Name: "stockguardian_tsp_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.stockguardian_tsp_crdb_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.stockguardian_tsp_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "recurring-payment-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "recurring-payment-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DbType: "CRDB"
    AppName: "recurring-payment-worker"
    StatementTimeout: 1s
    Username: "liquiloans_epifi_dev_user"
    Password: ""
    Name: "liquiloans_epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.liquiloans_epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.liquiloans_epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH_V2:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "recurring-payment-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  STOCK_GUARDIAN_TSP:
    DBType: "CRDB"
    Username: "stockguardian_tsp_crdb_dev_user"
    Password: ""
    Name: "stockguardian_tsp_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.stockguardian_tsp_crdb_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.stockguardian_tsp_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "uat/cockroach/ca.crt"
    SSLClientCert: "uat/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "uat/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "recurring-payment-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH:USE_CASE_ENACH_MANDATE:
    Name: "recurring_payment"
    DbType: "PGDB"
    DbServerAlias: "PLUTUS_RDS"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "recurring_payment_worker"
    SecretName: "uat/rds/epifiplutus/recurring_payment_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  STOCK_GUARDIAN_TSP:USE_CASE_ENACH_MANDATE:
    Name: "stock_guardian_tsp_recurring_payment"
    DbType: "PGDB"
    DbServerAlias: "PLUTUS_RDS"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "recurring_payment_worker"
    SecretName: "uat/rds/epifiplutus/stock_guardian_tsp_recurring_payment_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH:USE_CASE_RECURRING_PAYMENT:
    Name: "recurring_payment"
    DbType: "PGDB"
    DbServerAlias: "PLUTUS_RDS"
    EnableDebug: true
    SSLMode: "disable"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "recurring_payment_worker"
    SecretName: "uat/rds/epifiplutus/recurring_payment_dev_user"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

RecurringPaymentDb:
  Name: "recurring_payment"
  DbType: "PGDB"
  DbServerAlias: "PLUTUS_RDS"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  AppName: "recurring_payment_worker"
  SecretName: "uat/rds/epifiplutus/recurring_payment_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

OrderUpdatePublisher:
  TopicName: "uat-order-update-topic"

WorkflowUpdatePublisher:
  TopicName: "uat-celestial-workflow-update-topic"

RecurringPaymentActionUpdatePublisher:
  TopicName: "uat-recurring-payment-action-update-topic"

EnachConfig:
  FileRecordsBucket:
    AwsBucket: "epifi-uat-recurringpayment-enach-files"
    PresentationFilesS3Directory: "presentation"
  PresentationFileConfig:
    EpifiFederalPoolAccountNumber: ""
    EpifiFederalIFSCCode: ""
    UtilityName: "FEDERALBANK"


OrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    - OrderCache:
        IsCachingEnabled: false
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.uat-common-cache-redis.ud7kyq.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14
      ClientName: order
