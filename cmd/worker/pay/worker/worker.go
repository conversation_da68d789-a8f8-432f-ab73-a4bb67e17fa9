// nolint: unparam
package worker

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	errorspkg "github.com/pkg/errors"
	"go.uber.org/fx"
	"go.uber.org/zap"
	gormV2 "gorm.io/gorm"

	celestialPb "github.com/epifi/be-common/api/celestial"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/aws/v2/sns"
	"github.com/epifi/be-common/pkg/cfg"
	types2 "github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/epifigrpc"
	commonfx "github.com/epifi/be-common/pkg/epifitemporal/fx"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/storage"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"
	upiPb "github.com/epifi/gamma/api/upi"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"

	"github.com/epifi/gamma/pay/wire/types"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	accountsStatmentPb "github.com/epifi/gamma/api/accounts/statement"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/parser"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer"
	fileGenPb "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator"
	"github.com/epifi/gamma/api/pay/internationalfundtransfer/forex"
	piPb "github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/recurringpayment"
	savingsPb "github.com/epifi/gamma/api/savings"
	timelinePb "github.com/epifi/gamma/api/timeline"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	usersPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	ussOrderManagerPb "github.com/epifi/gamma/api/usstocks/order"
	vgAccountsPb "github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	iftVgPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/internationalfundtransfer"
	vgUpiPb "github.com/epifi/gamma/api/vendorgateway/openbanking/upi"
	pgPb "github.com/epifi/gamma/api/vendorgateway/pg"
	orderWire "github.com/epifi/gamma/order/wire"
	types3 "github.com/epifi/gamma/order/wire/types"
	payServerConfig "github.com/epifi/gamma/pay/config/server"
	payworkerconfig "github.com/epifi/gamma/pay/config/worker"
	payworkergenconfig "github.com/epifi/gamma/pay/config/worker/genconf"
	payWire "github.com/epifi/gamma/pay/wire"
	payWorkflow "github.com/epifi/gamma/pay/workflow"
	epifitemporalfx "github.com/epifi/gamma/pkg/epifitemporal/fx"
	"github.com/epifi/gamma/pkg/pay/pgauthkeys"
)

type domainClients struct {
	businessClient                      paymentPb.BusinessClient
	orderClient                         orderPb.OrderServiceClient
	savingsClient                       savingsPb.SavingsClient
	savingsAccountClient                accountsStatmentPb.AccountStatementClient
	payClient                           payPb.PayClient
	iftFileGenClient                    fileGenPb.FileGeneratorClient
	iftClient                           internationalfundtransfer.InternationalFundTransferClient
	forexClient                         forex.ForexServiceClient
	s3Client                            *s3.Client
	actorClient                         actorPb.ActorClient
	celestialClient                     celestialPb.CelestialClient
	timelineClient                      timelinePb.TimelineServiceClient
	accountPiClient                     account_pi.AccountPIRelationClient
	piClient                            piPb.PiClient
	ugClient                            userGroupPb.GroupClient
	userClient                          usersPb.UsersClient
	bcClient                            bankcust.BankCustomerServiceClient
	iftVgClient                         iftVgPb.InternationalFundTransferClient
	vgAccountClient                     vgAccountsPb.AccountsClient
	pgVgClient                          pgPb.PaymentGatewayClient
	accountBalanceClient                accountBalancePb.BalanceClient
	parserClient                        parser.ParserClient
	merchantClient                      merchant.MerchantServiceClient
	vgUpiClient                         vgUpiPb.UPIClient
	orderUpdatePublisher                queue.Publisher
	payRedisStore                       types.PayRedisStore
	lockRedisStore                      types.LockRedisStore
	orderRedisStore                     types2.OrderRedisStore
	recurringPaymentClient              recurringpayment.RecurringPaymentServiceClient
	connectedAccountClient              connectedAccountPb.ConnectedAccountClient
	ussOrderManagerClient               ussOrderManagerPb.OrderManagerClient
	txnDetailedStatusUpdateSnsPublisher queue.Publisher
	upiOnboardingClient                 upiOnboardingPb.UpiOnboardingClient
	upiClient                           upiPb.UPIClient
}

func initializeDomainClients(ctx context.Context, conf *payworkerconfig.Config, awsConf aws.Config) (*domainClients, func(), error) {
	clients := &domainClients{}

	// Initialize all connections
	orderConn := epifigrpc.NewConnByService(cfg.ORDER_SERVICE)
	clients.businessClient = paymentPb.NewBusinessClient(orderConn)
	clients.orderClient = orderPb.NewOrderServiceClient(orderConn)

	savingsConn := epifigrpc.NewConnByService(cfg.SAVINGS_SERVICE)
	clients.savingsClient = savingsPb.NewSavingsClient(savingsConn)

	accountsConn := epifigrpc.NewConnByService(cfg.ACCOUNTS_SERVICE)
	clients.savingsAccountClient = accountsStatmentPb.NewAccountStatementClient(accountsConn)
	clients.accountBalanceClient = accountBalancePb.NewBalanceClient(accountsConn)

	payConn := epifigrpc.NewConnByService(cfg.PAY_SERVICE)
	clients.payClient = payPb.NewPayClient(payConn)
	clients.iftFileGenClient = fileGenPb.NewFileGeneratorClient(payConn)
	clients.iftClient = internationalfundtransfer.NewInternationalFundTransferClient(payConn)
	clients.forexClient = forex.NewForexServiceClient(payConn)

	clients.s3Client = s3.NewClient(awsConf, conf.InternationalFundTransfer.DocumentsBucketName)

	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	clients.actorClient = actorPb.NewActorClient(actorConn)

	celestialConn := epifigrpc.NewConnByService(cfg.CELESTIAL_SERVICE)
	clients.celestialClient = celestialPb.NewCelestialClient(celestialConn)

	timelineConn := epifigrpc.NewConnByService(cfg.TIMELINE_SERVICE)
	clients.timelineClient = timelinePb.NewTimelineServiceClient(timelineConn)

	piConn := epifigrpc.NewConnByService(cfg.PAYMENT_INSTRUMENT_SERVICE)
	clients.accountPiClient = account_pi.NewAccountPIRelationClient(piConn)
	clients.piClient = piPb.NewPiClient(piConn)

	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	clients.ugClient = userGroupPb.NewGroupClient(userConn)
	clients.userClient = usersPb.NewUsersClient(userConn)

	bcConn := epifigrpc.NewConnByService(cfg.BANK_CUSTOMER_SERVICE)
	clients.bcClient = bankcust.NewBankCustomerServiceClient(bcConn)

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	clients.iftVgClient = iftVgPb.NewInternationalFundTransferClient(vgConn)
	clients.vgAccountClient = vgAccountsPb.NewAccountsClient(vgConn)
	clients.pgVgClient = pgPb.NewPaymentGatewayClient(vgConn)
	clients.vgUpiClient = vgUpiPb.NewUPIClient(vgConn)

	parserConn := epifigrpc.NewConnByService(cfg.PARSER_SERVICE)
	clients.parserClient = parser.NewParserClient(parserConn)

	merchantConn := epifigrpc.NewConnByService(cfg.MERCHANT_SERVICE)
	clients.merchantClient = merchant.NewMerchantServiceClient(merchantConn)

	recurringPaymentConn := epifigrpc.NewConnByService(cfg.RECURRING_PAYMENT_SERVICE)
	clients.recurringPaymentClient = recurringpayment.NewRecurringPaymentServiceClient(recurringPaymentConn)

	connectedAccountConn := epifigrpc.NewConnByService(cfg.CONNECTED_ACC_SERVICE)
	clients.connectedAccountClient = connectedAccountPb.NewConnectedAccountClient(connectedAccountConn)

	ussOrderManagerConn := epifigrpc.NewConnByService(cfg.US_STOCKS_SERVICE)
	clients.ussOrderManagerClient = ussOrderManagerPb.NewOrderManagerClient(ussOrderManagerConn)

	clients.orderUpdatePublisher = initSNSPublisher(ctx, conf.OrderUpdatePublisher, awsConf)
	clients.txnDetailedStatusUpdateSnsPublisher = initSNSPublisher(ctx, conf.TxnDetailedStatusUpdatePublisher, awsConf)

	// Initialize Redis clients
	payRedisClient := storage.NewRedisClientFromConfig(conf.PayOrderCacheConfig.RedisOptions, conf.Tracing.Enable)
	clients.payRedisStore = payRedisClient

	orderRedisClient := storage.NewRedisClientFromConfig(conf.OrderCacheConfig.RedisOptions, conf.Tracing.Enable)
	clients.orderRedisStore = orderRedisClient

	lockRedisClient := storage.NewRedisClientFromConfig(conf.LockRedisStore, conf.Tracing.Enable)
	clients.lockRedisStore = lockRedisClient

	cleanup := func() {
		// Close all connections
		epifigrpc.CloseConn(orderConn)
		epifigrpc.CloseConn(savingsConn)
		epifigrpc.CloseConn(accountsConn)
		epifigrpc.CloseConn(payConn)
		epifigrpc.CloseConn(actorConn)
		epifigrpc.CloseConn(celestialConn)
		epifigrpc.CloseConn(timelineConn)
		epifigrpc.CloseConn(piConn)
		epifigrpc.CloseConn(userConn)
		epifigrpc.CloseConn(bcConn)
		epifigrpc.CloseConn(vgConn)
		epifigrpc.CloseConn(parserConn)
		epifigrpc.CloseConn(merchantConn)
		epifigrpc.CloseConn(recurringPaymentConn)
		epifigrpc.CloseConn(connectedAccountConn)
		epifigrpc.CloseConn(ussOrderManagerConn)

		// Close Redis clients
		_ = payRedisClient.Close()
		_ = orderRedisClient.Close()
		_ = lockRedisClient.Close()
	}

	return clients, cleanup, nil
}

func PayWorkerDefinitions(genConf *payworkergenconfig.Config) ([]*commonfx.WorkerDefinition, error) {
	return []*commonfx.WorkerDefinition{
		{
			Name:                  "pay-primary-worker",
			WorkerApplicationConf: genConf.Application(),
		},
		{
			Name:                  "pay-off-app-upi-worker",
			WorkerApplicationConf: genConf.OffAppUpiApplication(),
		},
		{
			Name:                  "pay-billpay-worker",
			WorkerApplicationConf: genConf.BillpayApplication(),
		},
	}, nil
}

func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	app := fx.New(
		fx.Supply(initNotifier),
		fx.Supply(cfg.PAY_WORKER_SERVER),
		fx.Supply(cfg.PAY_WORKER_SERVICE),
		fx.Supply(payworkerconfig.Load),
		fx.Supply(payworkergenconfig.NewConfig),
		commonfx.GetConfigProviderOption[*payworkergenconfig.Config, *payworkerconfig.Config](),
		epifitemporalfx.FxWorkersModule,
		fx.Decorate(registerDomainActivitiesAndWorkflows),
		fx.Provide(commonfx.AsWorkerDefinitions(PayWorkerDefinitions)),
	)

	app.Run()
	return nil
}

type registerDomainActivitiesAndWorkflowsParams struct {
	fx.In

	Lc                  fx.Lifecycle
	Workers             []*commonfx.WorkerResult                                       `name:"Workers"`
	Conf                *payworkerconfig.Config                                        `name:"Conf"`
	DynConf             *payworkergenconfig.Config                                     `name:"GenConf"`
	DbConProvider       *storageV2.DBResourceProvider[*gormV2.DB]                      `name:"DbConnProvider"`
	TxnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor] `name:"TxnExecutorProvider"`
	AwsConfig           aws.Config                                                     `name:"AwsConfig"`
}

type registerDomainActivitiesAndWorkflowsResult struct {
	fx.Out

	Workers []*commonfx.WorkerResult `name:"Workers"`
}

func registerDomainActivitiesAndWorkflows(p registerDomainActivitiesAndWorkflowsParams) (registerDomainActivitiesAndWorkflowsResult, error) {
	db, err := p.DbConProvider.GetResourceForOwnership(commontypes.Ownership_EPIFI_TECH)
	if err != nil {
		return registerDomainActivitiesAndWorkflowsResult{}, errorspkg.Wrap(err, "failed to get db resource for ownership")
	}

	storageV2.InitDefaultCRDBTransactionExecutor(db)

	if err = pgauthkeys.InitialisePgProgramToAuthParams(p.Conf.PgProgramToAuthSecretMap); err != nil {
		return registerDomainActivitiesAndWorkflowsResult{}, errorspkg.Wrap(err, "initialise auth params failed")
	}

	// Initialize clients once outside the loop
	clients, cleanup, err := initializeDomainClients(context.Background(), p.Conf, p.AwsConfig)
	if err != nil {
		return registerDomainActivitiesAndWorkflowsResult{}, errorspkg.Wrap(err, "failed to initialize domain clients")
	}

	// Add cleanup to lifecycle hooks
	p.Lc.Append(fx.Hook{
		OnStop: func(context.Context) error {
			cleanup()
			return nil
		},
	})

	// Initialize activity processors
	activityProcessors := initActivityProcessors(db, p.Conf, clients, p.TxnExecutorProvider, p.DbConProvider, p.DynConf)

	for _, wr := range p.Workers {
		// Register activities
		for _, ap := range activityProcessors {
			wr.Worker.RegisterActivity(ap)
		}

		// Register workflows
		wr.Worker.RegisterWorkflow(payWorkflow.B2CFundTransfer)
		wr.Worker.RegisterWorkflow(payWorkflow.ExecutionReportGenerator)
		wr.Worker.RegisterWorkflow(payWorkflow.GenerateSwiftReports)
		wr.Worker.RegisterWorkflow(payWorkflow.InternationalFundTransfer)
		wr.Worker.RegisterWorkflow(payWorkflow.FundTransfer)
		wr.Worker.RegisterWorkflow(payWorkflow.FundTransferV1)
		wr.Worker.RegisterWorkflow(payWorkflow.MonitorForexRate)
		wr.Worker.RegisterWorkflow(payWorkflow.UpdateLrsLimitsWorkflow)
		wr.Worker.RegisterWorkflow(payWorkflow.CheckAndUpdatePgFundTransferStatus)
		wr.Worker.RegisterWorkflow(payWorkflow.OffAppUpiV2Flow)
		wr.Worker.RegisterWorkflow(payWorkflow.ProcessRecurringOutwardRemittance)
		wr.Worker.RegisterWorkflow(payWorkflow.ReconcilePgPayments)
		wr.Worker.RegisterWorkflow(payWorkflow.InitiatePgPaymentsRecon)
	}

	return registerDomainActivitiesAndWorkflowsResult{
		Workers: p.Workers,
	}, nil
}

// initActivityProcessors - Initialize all activity processors to be registered with temporal worker(s).
func initActivityProcessors(db *gormV2.DB, conf *payworkerconfig.Config, clients *domainClients,
	txnExecutorProvider *storageV2.DBResourceProvider[storageV2.IdempotentTxnExecutor], dbProvider *storageV2.DBResourceProvider[*gormV2.DB],
	dynConf *payworkergenconfig.Config) []interface{} {

	// use fund transfer worker params to map to server params
	paymentNotificationParams := convertFundTransferWorkerToServerParams(conf)

	payActProcessor := payWire.InitializeActivityProcessor(
		db,
		clients.businessClient,
		clients.savingsClient,
		conf.ExecutionReportGenerationParams.ReportStalenessDuration,
		clients.payClient,
		clients.parserClient,
		clients.iftFileGenClient,
		clients.s3Client,
		clients.actorClient,
		clients.celestialClient,
		clients.savingsAccountClient,
		clients.iftClient,
		clients.timelineClient,
		clients.accountPiClient,
		clients.piClient,
		clients.ugClient,
		clients.userClient,
		&payServerConfig.FundTransferParams{
			SMSTypeToOptionVersionMap: conf.FundTransferParams.SMSTypeToOptionVersionMap,
			PaymentNotificationParams: paymentNotificationParams,
		},
		clients.bcClient,
		conf.PayOrderCacheConfig,
		conf.PayTransactionCacheConfig,
		conf,
		clients.iftVgClient,
		clients.accountBalanceClient,
		clients.vgUpiClient,
		clients.orderClient,
		clients.pgVgClient,
		clients.merchantClient,
		clients.orderUpdatePublisher,
		clients.payRedisStore,
		clients.vgAccountClient,
		dbProvider,
		txnExecutorProvider,
		clients.forexClient,
		clients.connectedAccountClient,
		clients.recurringPaymentClient,
		clients.lockRedisStore,
		dynConf,
		clients.ussOrderManagerClient,
		clients.txnDetailedStatusUpdateSnsPublisher,
		clients.upiOnboardingClient,
		clients.upiClient,
	)

	orderActProcessor := orderWire.InitializeActivityProcessor(
		db,
		dynConf.OrderCacheConfig(),
		clients.orderUpdatePublisher,
		clients.piClient,
		clients.orderRedisStore,
		dbProvider,
		types3.EnableResourceProvider(conf.EnableEntitySegregation),
	)

	return []interface{}{payActProcessor, orderActProcessor}
}

func initSNSPublisher(ctx context.Context, conf *cfg.SnsPublisher, awsConf aws.Config) queue.Publisher {
	publisher, err := sns.NewSnsPublisherWithConfig(ctx, conf, awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Panic("failed to initialise SNS publisher", zap.Error(err))
	}

	return publisher
}

// convertFundTransferWorkerToServerParams - use fund transfer worker params to map to server params
func convertFundTransferWorkerToServerParams(conf *payworkerconfig.Config) *payServerConfig.PaymentNotificationParams {
	paymentNotifParamsWorker := conf.FundTransferParams.PaymentNotificationParams
	paymentNotifParamsServer := &payServerConfig.PaymentNotificationParams{
		HistoricPaymentSupportedTill: paymentNotifParamsWorker.HistoricPaymentSupportedTill,
		TransactionDebitSuccess:      conf.FundTransferParams.PaymentNotificationParams.TransactionDebitSuccess,
		TransactionFailed:            conf.FundTransferParams.PaymentNotificationParams.TransactionFailed,
		TransactionReversed:          conf.FundTransferParams.PaymentNotificationParams.TransactionReversed,
	}
	return paymentNotifParamsServer
}
