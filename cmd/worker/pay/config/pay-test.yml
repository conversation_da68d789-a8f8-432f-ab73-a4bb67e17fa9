Application:
  Environment: "test"
  Namespace: "pay"
  TaskQueue: "pay-task-queue"
  RedisConfig:
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    IsSecureRedis: false

OffAppUpiApplication:
  Environment: "test"
  Namespace: "pay"
  TaskQueue: "pay-off-app-upi-task-queue"
  RedisConfig:
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    IsSecureRedis: false

BillpayApplication:
  Environment: "test"
  Namespace: "pay"
  TaskQueue: "pay-billpay-task-queue"
  RedisConfig:
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    IsSecureRedis: false

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi_test"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "ERROR"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

OrderUpdatePublisher:
  TopicName: "order-update-topic"

TxnDetailedStatusUpdatePublisher:
  TopicName: "txn-detailed-status-update-topic"

WorkflowUpdatePublisher:
  TopicName: "celestial-workflow-update-topic"

Secrets:
  Ids:
    GoogleCloudProfilingServiceAccountKey: "test"
    IFTReportsSlackBotOauthToken: "dummy-slack-bot-oauth-token"
    TemporalCodecAesKey: "853bbce933313713af7f43bb8fcc1d84"

Server:
  HttpPort: 9552

InternationalFundTransfer:
  DocumentsBucketName: "epifi-pay-international-fund-transfer"
  IsGstReportingViaApiFlowActive: true

OrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 5
      ClientName: order

PayOrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    # CacheConfig for OrderCache used in GetById fpr storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 3
    ClientName: pay

LockRedisStore:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
  ClientName: pay

PayTransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
  RedisOptions:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 3
    ClientName: pay

PgProgramToAuthSecretMap:
  "RAZORPAY:FEDERAL_BANK:paymentinstrument-creditcard-federal-pool-account-1":
    AuthParam: "{\"KeyId\":\"rzp_test_J8bVSzs01Aaq7V\",\"KeySecret\":\"fjdeWi73ZX71TyZOjYzeB0jh\"}"
  "RAZORPAY:EPIFI_TECH:paymentinstrument-creditcard-federal-pool-account-1":
    AuthParam: "{\"KeyId\":\"rzp_test_J8bVSzs01Aaq7V\",\"KeySecret\":\"fjdeWi73ZX71TyZOjYzeB0jh\"}"
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "{\"KeyId\":\"rzp_test_J8bVSzs01Aaq7V\",\"KeySecret\":\"fjdeWi73ZX71TyZOjYzeB0jh\"}"
