Application:
  Environment: "development"
  Namespace: "pay"
  TaskQueue: "pay-task-queue"
  RedisConfig:
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    IsSecureRedis: false

OffAppUpiApplication:
  Environment: "development"
  Namespace: "pay"
  TaskQueue: "pay-off-app-upi-task-queue"
  RedisConfig:
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    IsSecureRedis: false

BillpayApplication:
  Environment: "development"
  Namespace: "pay"
  TaskQueue: "pay-billpay-task-queue"
  RedisConfig:
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    IsSecureRedis: false

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

OrderUpdatePublisher:
  TopicName: "order-update-topic"

WorkflowUpdatePublisher:
  TopicName: "celestial-workflow-update-topic"

Secrets:
  Ids:
    GoogleCloudProfilingServiceAccountKey: "development"
    IFTReportsSlackBotOauthToken: "dummy-slack-bot-oauth-token"
    TemporalCodecAesKey: "853bbce933313713af7f43bb8fcc1d84"

InternationalFundTransfer:
  DocumentsBucketName: "epifi-development-pay-international-fund-transfer"

OrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    - OrderCache:
        IsCachingEnabled: false
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 5
      ClientName: order

PayOrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    # CacheConfig for OrderCache used in GetById fpr storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: false
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 3
    ClientName: pay

LockRedisStore:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 3
  ClientName: pay

PayTransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: false
        CacheTTL: "5m"
  RedisOptions:
    IsSecureRedis: false
    Options:
      Addr: "localhost:6379"
      Password: "" ## empty string for no password
      DB: 3
    ClientName: pay
