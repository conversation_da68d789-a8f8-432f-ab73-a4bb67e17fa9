Application:
  WorkerOptions:
    Options:
      # This is computed based on the activity task throughput expected from the workflows in the worst case
      MaxConcurrentActivityTaskPollers: 10
      MaxConcurrentActivityExecutionSize: 500
      WorkerActivitiesPerSecond: 50.0
      MaxConcurrentLocalActivityExecutionSize: 500
      WorkerLocalActivitiesPerSecond: 100.0
      TaskQueueActivitiesPerSecond: 50
      # This is computed based on the workflow task throughput expected from the workflows in the worst case
      MaxConcurrentWorkflowTaskPollers: 15
      # 50 concurrency / 10ms = 5000 qps
      MaxConcurrentWorkflowTaskExecutionSize: 50
  Flags:
    SkipDeprecatedCelestialActivitiesRegistration: false

OffAppUpiApplication:
  WorkerOptions:
    Options:
      MaxConcurrentActivityTaskPollers: 7
      MaxConcurrentActivityExecutionSize: 500
      WorkerActivitiesPerSecond: 70
      MaxConcurrentLocalActivityExecutionSize: 500
      WorkerLocalActivitiesPerSecond: 100.0
      TaskQueueActivitiesPerSecond: 75
      MaxConcurrentWorkflowTaskPollers: 10
      MaxConcurrentWorkflowTaskExecutionSize: 50
  Flags:
    SkipDeprecatedCelestialActivitiesRegistration: false

BillpayApplication:
  WorkerOptions:
    Options:
      MaxConcurrentActivityTaskPollers: 7
      MaxConcurrentActivityExecutionSize: 500
      WorkerActivitiesPerSecond: 70
      MaxConcurrentLocalActivityExecutionSize: 500
      WorkerLocalActivitiesPerSecond: 100.0
      TaskQueueActivitiesPerSecond: 75
      MaxConcurrentWorkflowTaskPollers: 10
      MaxConcurrentWorkflowTaskExecutionSize: 50
  Flags:
    SkipDeprecatedCelestialActivitiesRegistration: true

RazorPayResponseCodesJson: "pkg/pay/pgerrorcodes/razorpayErrorResponseCodes.json"

Server:
  HttpPort: 9090
  GrpcPort: 9000

AWS:
  Region: "ap-south-1"

DefaultActivityParamsList:
  - ActivityName: "GetWorkflowProcessingParams"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "InitiateWorkflowStage"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "UpdateWorkflowStageStatus"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "PublishWorkflowUpdateEvent"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "SendNotification"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "CreateOrderV1"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "UpdateOrder"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "PublishOrderUpdate"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "UpdateOrderWorkflowRefID"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "PublishToSQS"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "CheckAndUpdateBalance"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 30s 90s 210s 450s 930s 1890s 3810s 7650s 15330s
      ExponentialBackOff:
        BaseInterval: "30s"
        MaxInterval: "3h"
        BackoffCoefficient: 2.0
        MaxAttempts: 9
  - ActivityName: "GetWorkflowProcessingParamsV2"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "InitiateWorkflowStageV2"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "UpdateWorkflowStage"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "PublishWorkflowUpdateEventV2"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "SendSignalToWorkflowsV2"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "GetWorkflowProcessingParamsByWorkflowTypeV2"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "SendNotification"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "UpdateLRSLimits"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "30s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "SendInternationalFundTransferStatus"
    ScheduleToCloseTimeout: "10m"
    StartToCloseTimeout: "60s"
    RetryParams:
      RegularInterval:
        Interval: "20s"
        MaxAttempts: 5
  - ActivityName: "RevertForexRateAmountInUse"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 10min
      # Retry interval - 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 10m40s 10m40s
      ExponentialBackOff:
        BaseInterval: "5s"
        MaxInterval: "11m"
        BackoffCoefficient: 2.0
        MaxAttempts: 10

WorkflowParamsList:
  - WorkflowName: "B2CFundTransfer"
    ActivityParamsList:
      - ActivityName: "ProcessB2CPayment"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
  - WorkflowName: "ExecutionReportGenerator"
    ActivityParamsList:
      - ActivityName: "GetWorkflowCount"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
  - WorkflowName: "InternationalFundTransfer"
    ActivityParamsList:
      - ActivityName: "ProcessFundTransfer"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GenerateA2Form"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GenerateSOFDocument"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "UploadFileToS3"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GetOrderAndTransactionDetailsForTxn"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "UploadSOFDocument"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GetStatement"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "ProcessRefundNotification"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "CreateIFTSecondLegTransaction"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "InitiateLRSCheck"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "10s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 10min
          # Retry interval - 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 10m40s 10m40s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "11m"
            BackoffCoefficient: 2.0
            MaxAttempts: 10
      - ActivityName: "ReportTcsCharges"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 10min
          # Retry interval - 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 10m40s 10m40s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "11m"
            BackoffCoefficient: 2.0
            MaxAttempts: 10
      - ActivityName: "GetRemittanceEligibilityBySof"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for ~17 mins with max cap between retries at 10min
          # Retry interval - 2s 4s 8s 16s 32s ... 10 attempts
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 10
      - ActivityName: "GetGstCollectedFromUserStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 10min
          # Retry interval - 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 10m40s 10m40s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "11m"
            BackoffCoefficient: 2.0
            MaxAttempts: 10
      - ActivityName: "ReportGstCollectedFromUser"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 10min
          # Retry interval - 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 10m40s 10m40s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "11m"
            BackoffCoefficient: 2.0
            MaxAttempts: 10
      - ActivityName: "GenerateGstReportingVendorRequestId"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 10min
          # Retry interval - 5s 10s 20s 40s 1m20s 2m40s 5m20s 10m40s 10m40s 10m40s
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "11m"
            BackoffCoefficient: 2.0
            MaxAttempts: 10
  - WorkflowName: "FundTransfer"
    ActivityParamsList:
      - ActivityName: "ProcessFundTransfer"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
  - WorkflowName: "GenerateSwiftReports"
    ActivityParamsList:
      - ActivityName: "VerifyWFStatusUpdate"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GenerateMt199MessageAttachment"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GenerateLRSFile"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GenerateTTMFile"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GenerateGSTFile"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GenerateTCSFile"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GetOutwardBulkTransactionInfo"
        ScheduleToCloseTimeout: "36h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "30m"
            MaxAttempts: 73
  - WorkflowName: "FundTransferV1"
    ActivityParamsList:
      - ActivityName: "ProcessFundTransfer"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GetFundTransferNotificationTemplate"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
      - ActivityName: "GetTxnStatus"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "20s"
            MaxAttempts: 5
  - WorkflowName: "MonitorForexRate"
    ActivityParamsList:
        - ActivityName: "GetForexRateExpiryWaitTime"
          # since there is no db or network call, keeping the timeout and retries very low
          ScheduleToCloseTimeout: "2m"
          StartToCloseTimeout: "5s"
          RetryParams:
            RegularInterval:
              Interval: "1s"
              MaxAttempts: 30
        - ActivityName: "UpdateForexRate"
          ScheduleToCloseTimeout: "3h"
          StartToCloseTimeout: "60s"
          RetryParams:
            # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
            # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
            ExponentialBackOff:
              BaseInterval: "2s"
              MaxInterval: "20m"
              BackoffCoefficient: 2.0
              MaxAttempts: 15
        - ActivityName: "SendForexRateReport"
          ScheduleToCloseTimeout: "3h"
          StartToCloseTimeout: "60s"
          RetryParams:
            # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
            # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
            ExponentialBackOff:
              BaseInterval: "2s"
              MaxInterval: "20m"
              BackoffCoefficient: 2.0
              MaxAttempts: 15
        - ActivityName: "PublishForexRateMetricToPrometheus"
          ScheduleToCloseTimeout: "3h"
          StartToCloseTimeout: "60s"
          RetryParams:
            # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
            # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
            ExponentialBackOff:
              BaseInterval: "2s"
              MaxInterval: "20m"
              BackoffCoefficient: 2.0
              MaxAttempts: 15
        - ActivityName: "GetForexRate"
          ScheduleToCloseTimeout: "3h"
          StartToCloseTimeout: "60s"
          RetryParams:
            # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
            # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
            ExponentialBackOff:
              BaseInterval: "2s"
              MaxInterval: "20m"
              BackoffCoefficient: 2.0
              MaxAttempts: 15
  - WorkflowName: "OffAppUpiV2Flow"
    # the retry strategy added here isn't from production view point
    ActivityParamsList:
      - ActivityName: "EnquireUpiTxnStatus"
        # Keeping activity timeout and retries very low since we need to create order and transaction in near real time.
        ScheduleToCloseTimeout: "72s"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 6
      - ActivityName: "EnquireUpiTxnStatusLastAttempt"
        # Keeping activity timeout and retries very low since we need to create order and transaction in near real time.
        ScheduleToCloseTimeout: "62s"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 5
      - ActivityName: "AcquireStatelessLock"
        ScheduleToCloseTimeout: "4m"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "3s"
            MaxAttempts: 15
      - ActivityName: "ReleaseStatelessLock"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "3s"
            MaxAttempts: 5
      - ActivityName: "DedupeTxn"
        ScheduleToCloseTimeout: "1m"
        StartToCloseTimeout: "15s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 15
      - ActivityName: "RecordOffAppPayment"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "3s"
            MaxAttempts: 20
      - ActivityName: "RecordOffAppPaymentLastAttempt"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 10
      - ActivityName: "TriggerRemitterBackfill"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 15
      - ActivityName: "EnquireAndUpdateUpiTxn"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "1m"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 30
      - ActivityName: "ResolveOtherActorEntities"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 15
      - ActivityName: "UpdateOrderAndTxn"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 30
      - ActivityName: "PublishOrderUpdateEventWithTxn"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 30
  - WorkflowName: "UpdateLrsLimitsWorkflow"
    ActivityParamsList:
      - ActivityName: "UpdateLRSLimits"
        HeartbeatTimeout: "30s"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
  - WorkflowName: "CheckAndUpdatePgFundTransferStatus"
    ActivityParamsList:
      - ActivityName: "GetPgFundTransferOrderDetails"
        HeartbeatTimeout: "10s"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "20s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "UpdateOrderAndTxnForPG"
        HeartbeatTimeout: "10s"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "20s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
          # Retry interval - 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
  - WorkflowName: "ProcessRecurringOutwardRemittance"
    ActivityParamsList:
      - ActivityName: "UpdateForexDealUsage"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20m"
            BackoffCoefficient: 2.0
            MaxAttempts: 15
      - ActivityName: "ValidateLRSLimit"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20m"
            BackoffCoefficient: 2.0
            MaxAttempts: 15
      - ActivityName: "ExecuteRecurringPoolTransfer"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20m"
            BackoffCoefficient: 2.0
            MaxAttempts: 15
      - ActivityName: "InitiateIFTWorkflow"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # exponential retry strategy that runs for ~2 hours with max cap between retries at 20m
          # First exponential retry interval 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "20m"
            BackoffCoefficient: 2.0
            MaxAttempts: 15
  - WorkflowName: "ReconcilePgPayments"
    ActivityParamsList:
      - ActivityName: "FetchPgReconPaymentsFromVendor"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 3 mins with max cap between retries at 10min
          # Retry wait intervals - 10s 20s 40s 80s 160s 320s 10m 10m
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 8
      - ActivityName: "ReconcilePgPaymentEntities"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "10m"
        RetryParams:
          # Exponential retry strategy that runs for 30 mins with max cap between retries at 10min
          # Retry wait intervals - 10s 20s 40s 80s 160s 320s 10m 10m
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 8
  - WorkflowName: "InitiatePgPaymentsRecon"
    ChildWorkflowParamsList:
      - WorkflowName: "ReconcilePgPayments"
        WorkflowExecutionTimeout: "12h"
        WorkflowRunTimeout: "2h"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 1
  - WorkflowName: "RechargePayment"
    ActivityParamsList:
      - ActivityName: "UpdateRechargeOrderStatus"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "CreateRechargeOrderStage"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "UpdateRechargeOrderStage"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "EnquireRechargePaymentStatus"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "4s"
                MaxAttempts: 5
            RetryStrategy2:
              # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
              # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "10m"
                BackoffCoefficient: 2.0
                MaxAttempts: 14
            MaxAttempts: 14
            CutOff: 5
      - ActivityName: "ShouldInitiateRechargeFulfilment"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "InitiateRechargeWithVendor"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 3 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "EnquireRechargeStatusWithVendor"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "4s"
                MaxAttempts: 5
            RetryStrategy2:
              # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
              # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "10m"
                BackoffCoefficient: 2.0
                MaxAttempts: 14
            MaxAttempts: 14
            CutOff: 5
      - ActivityName: "ShouldInitiateRechargeRefundOrder"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "30s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "InitiateRechargeRefundOrder"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "10m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "EnquireRechargeRefundOrderStatus"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "4s"
                MaxAttempts: 5
            RetryStrategy2:
              # Exponential retry strategy that runs for 1.5 hours with max cap between retries at 10min
              # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 10m0s 10m0s 10m0s 10m0s 10m0s 10m0s
              ExponentialBackOff:
                BaseInterval: "4s"
                MaxInterval: "10m"
                BackoffCoefficient: 2.0
                MaxAttempts: 14
            MaxAttempts: 14
            CutOff: 5

Tracing:
  Enable: true

ExecutionReportGenerationParams:
  ReportStalenessDuration: "0.1s"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

FundTransferParams:
  SMSTypeToOptionVersionMap:
    GenericPiDebit:
      Default: "V1"
    NeftDebit:
      Default: "V1"
    RtgsDebit:
      Default: "V1"
    TransactionReversed:
      Default: "V1"
  PaymentNotificationParams:
    HistoricPaymentSupportedTill: "2h"
    TransactionDebitSuccess:
      Title: "Money sent"
      Body: "Your transaction for %s has gone through! Tap to view."
      IconAttr:
        IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
      NotificationExpiry: "1h"
      NotificationType: "SystemTray"
      TriggerAfter: "30s"
    TransactionFailed:
      Title: "Transaction failed"
      Body: "Sorry, we couldn't send %s to %s. Tap to retry."
      IconAttr:
        IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
      NotificationExpiry: "1h"
      NotificationType: "SystemTray"
      TriggerAfter: "30s"
    TransactionReversed:
      Title: "Payment reversed"
      Body: "Heads up! The %s transferred to %s is now back in your account."
      IconAttr:
        IconURL: "https://epifi-icons.pointz.in/fibank/icon/24-color.png"
      NotificationExpiry: "1h"
      NotificationType: "SystemTray"

InternationalFundTransfer:
  EnableFederalSherlock: true
  ForexRateReportSlackChannelId: "C058MNMBMMK" # save-reports-testing
  ReportTcsChargesFromApi: true
  IsSofAnalysisFlowActive: true
  IsGstReportingViaApiFlowActive: true
  SherlockHost: "https://federal.epifi.in"

IsGSTReportedWithNewInvoiceNumber: true

PaymentEnquiryParams:
  InProgressToSuccessMap:
    "FEDERAL_BANK":
      PaymentProtocolToDeemedEnquiryDurationMap:
        UPI:
          P2P: 5m
          P2M: 5m
      OffAppPaymentProtocolToDeadlineExceededDuration:
        UPI: 5m
      OffAppPaymentProtocolToNotFoundDuration:
        UPI: 5m

EnableEntitySegregation: true

# This config is also duplicated in the pay service group's config. Do update there as well when updating here
PgParams:
  # TODO(Sundeep): Increase the values once we determine the reliability of webhooks.
  # For one time payments, we expect the payments to be complete within 12m, hence setting the signal wait timeout to
  # this. If the webhook event is not received from paymentgateway within this timeout, then the workflow polls the
  # payment gateway for a while to check if payment is completed.
  OneTimeFundTransferStatusWaitSignalTimeout: "1m"
  # For recurring payment executions (using mandates), for the payments to be authorised it takes T + 1 days. Hence,
  # for recurring payment executions we set the signal wait timeout to 3 days, as we expect the payments to be
  # completed within that duration. However in non-prod the mandate execution is immediately captured, hence setting it
  # to a small value
  # https://razorpay.com/docs/payments/recurring-payments/emandate/faqs/#4-for-emandates-how-long-does-it-take
  RpExecutionFundTransferStatusWaitSignalTimeout: "1m"

  OneTimePaymentOrderExpirationDuration:
    # Fail the internal order corresponding to razorpay order if no payment is attempted within 20 min.
    "RAZORPAY": "20m"

  # Config values for creating order and transaction for adjustment payments from payment gateway.
  PaymentGatewayAdjustmentActorId: "pg-adjustment-generic-vendor-actor-id"
  DummyPaymentGatewayAdjustmentActorToName: "PG Pool Account"

  PGAdjustmentOwnershipToPiMapping:
    "STOCK_GUARDIAN_TSP": "pg-adjustment-generic-vendor-pi-id_MbZRPgHhafW"
    "EPIFI_TECH": "pg-adjustment-generic-vendor-pi-id"
    "LIQUILOANS_PL": "pg-adjustment-generic-vendor-pi-id_5feFb8ziEp"

  PaymentGatewayRefundGenericActorId: "pg-refund-no-forward-payment-actor-id"
  DummyPaymentGatewayRefundGenericActorFromName: "PG Pool account"
  # Make 5 concurrent vendor calls in every 1 second, so for page size of 100, it will take ~20s to process.
  # Razorpay has a ratelimit of 10 QPS configured per account, hence starting with 5 requests per second, so that
  # we have enough headroom for normal flows as well.
  VendorDetailsEnrichmentNumWorkers: 5
  VendorDetailsEnrichmentApiRatelimit: 5
