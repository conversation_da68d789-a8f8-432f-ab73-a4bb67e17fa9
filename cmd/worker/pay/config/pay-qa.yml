Application:
  Environment: "qa"
  Namespace: "qa-pay"
  TaskQueue: "qa-pay-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: ""
      DB: 12

OffAppUpiApplication:
  Environment: "qa"
  Namespace: "qa-pay"
  TaskQueue: "qa-pay-off-app-upi-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: ""
      DB: 12

BillpayApplication:
  Environment: "qa"
  Namespace: "qa-pay"
  TaskQueue: "qa-pay-billpay-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: ""
      DB: 12

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DbType: "CRDB"
    AppName: "pay-worker"
    StatementTimeout: 1s
    Username: "liquiloans_epifi_dev_user"
    Password: ""
    Name: "liquiloans_epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.liquiloans_epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.liquiloans_epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH_V2:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  STOCK_GUARDIAN_TSP:
    DBType: "CRDB"
    Username: "stockguardian_tsp_crdb_dev_user"
    Password: ""
    Name: "stockguardian_tsp_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.stockguardian_tsp_crdb_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.stockguardian_tsp_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:
    DbType: "CRDB"
    AppName: "pay-worker"
    StatementTimeout: 1s
    Username: "liquiloans_epifi_dev_user"
    Password: ""
    Name: "liquiloans_epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.liquiloans_epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.liquiloans_epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  EPIFI_TECH_V2:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  STOCK_GUARDIAN_TSP:
    DBType: "CRDB"
    Username: "stockguardian_tsp_crdb_dev_user"
    Password: ""
    Name: "stockguardian_tsp_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.stockguardian_tsp_crdb_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.stockguardian_tsp_crdb_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  FEDERAL_BANK:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

OrderUpdatePublisher:
  TopicName: "qa-order-update-topic"

TxnDetailedStatusUpdatePublisher:
  TopicName: "qa-txn-detailed-status-update-topic"

WorkflowUpdatePublisher:
  TopicName: "qa-celestial-workflow-update-topic"

Secrets:
  Ids:
    GoogleCloudProfilingServiceAccountKey: "qa/gcloud/profiling-service-account-key"
    IFTReportsSlackBotOauthToken: "qa/ift/slack-bot-oauth-token"
    TemporalCodecAesKey: "qa/temporal/codec-encryption-key"

InternationalFundTransfer:
  EnableFederalSherlock: false
  DocumentsBucketName: "epifi-qa-pay-international-fund-transfer"
  SherlockHost: "https://sherlock.qa.pointz.in"
  FederalSherlockHost: "https://federal-sherlock.qa.pointz.in"

OrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14
      ClientName: order

PgProgramToAuthSecretMap:
  "RAZORPAY:EPIFI_TECH:paymentinstrument-creditcard-federal-pool-account-2":
    AuthParam: "qa/vendorgateway/razorpay-federal-secured-cards-api-key"
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "qa/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-stockguardian_MbZRPgHhafW":
    AuthParam: "qa/vendorgateway/razorpay-stock-guardian-loans-api-key"

PayOrderCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for RecentActivityCache used in GetRecentActivityOrdersFromCache for storing recent pay activity
    # (RecentPayActivityFromActorIdPrefix, RecentPayActivityToActorIdPrefix are cache prefixes used here).
    - RecentActivityCache:
        IsCachingEnabled: true
        CacheTTL: "5m"
    # CacheConfig for OrderCache used in GetById fpr storing order
    # (OrderKeyPrefix is cache prefix used here).
    - OrderCache:
        IsCachingEnabled: true
        CacheTTL: "2m"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14
    ClientName: pay

LockRedisStore:
  IsSecureRedis: true
  Options:
    Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
    Password: "" ## empty string for no password
  ClientName: pay

PayTransactionCacheConfig:
  IsCachingEnabled: true
  UseCaseToCacheConfigMap:
    # CacheConfig for TransactionCache used in GetById for storing transaction
    # (TransactionKeyPrefix is cache prefix used here).
    - TransactionCache:
        IsCachingEnabled: false
        CacheTTL: "5m"
  RedisOptions:
    IsSecureRedis: true
    Options:
      Addr: "master.qa-common-cache-redis.rxzivf.aps1.cache.amazonaws.com:6379"
      Password: "" ## empty string for no password
      DB: 14
    ClientName: pay
