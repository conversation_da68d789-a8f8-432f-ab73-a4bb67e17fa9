Application:
  Environment: "demo"
  Namespace: "demo-pay"
  TaskQueue: "demo-pay-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.demo-common-cache-redis.wqltco.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 0

OffAppUpiApplication:
  Environment: "demo"
  Namespace: "demo-pay"
  TaskQueue: "demo-pay-off-app-upi-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.demo-common-cache-redis.wqltco.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 0

BillpayApplication:
  Environment: "demo"
  Namespace: "demo-pay"
  TaskQueue: "demo-pay-billpay-task-queue"
  RedisConfig:
    IsSecureRedis: true
    Options:
      Addr: "master.demo-common-cache-redis.wqltco.aps1.cache.amazonaws.com:6379"
    Password: ""
    DB: 0

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "demo/cockroach/ca.crt"
    SSLClientCert: "demo/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "demo/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "demo/cockroach/ca.crt"
    SSLClientCert: "demo/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "demo/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    AppName: "pay-worker"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

OrderUpdatePublisher:
  TopicName: "demo-order-update-topic"

WorkflowUpdatePublisher:
  TopicName: "demo-celestial-workflow-update-topic"

Secrets:
  Ids:
    GoogleCloudProfilingServiceAccountKey: "demo/gcloud/profiling-service-account-key"
    IFTReportsSlackBotOauthToken: "demo/ift/slack-bot-oauth-token"
    TemporalCodecAesKey: "deploy/temporal/codec-encryption-key"

InternationalFundTransfer:
  DocumentsBucketName: "epifi-demo-pay-international-fund-transfer"
