Application:
  Environment: "development"
  Namespace: "credit-report"
  TaskQueue: "credit-report-task-queue"
  RedisConfig:
    Options:
      Addr: "localhost:6379"
      Password: ""
      DB: 0
    IsSecureRedis: false

DbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:
    DBType: "CRDB"
    Host: "localhost"
    Port: 26257
    Username: "root"
    Password: ""
    Name: "epifi"
    EnableDebug: false
    SSLMode: "disable"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

FeatureEngineeringPdgb:
  DbType: "PGDB"
  AppName: "credit-report-worker"
  StatementTimeout: 5s
  Name: "feature_engineering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FeatureReleaseConfig:
  FeatureConstraints:
    - NSDL_PAN_FLOW_V2_CREDIT_REPORT_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 343
          MinIOSVersion: 484
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL

WorkflowUpdatePublisher:
  TopicName: "celestial-workflow-update-topic"

CreditReportDownloadEventPublisher:
  TopicName: "credit-report-download-event-topic"

RudderStack:
  IntervalInSec: 10
  BatchSize: 100
  Verbose: true

Secrets:
  Ids:
    GoogleCloudProfilingServiceAccountKey: "development"
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    TemporalCodecAesKey: "853bbce933313713af7f43bb8fcc1d84"

CreditReportFlattenPublisher:
  QueueName: "lending-credit-report-flattening-queue"
