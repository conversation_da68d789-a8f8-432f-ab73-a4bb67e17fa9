package worker

import (
	"context"
	"fmt"
	"time"

	// nolint: depguard
	"github.com/rudderlabs/analytics-go"
	"go.uber.org/zap"

	commonTypesPb "github.com/epifi/be-common/api/typesv2/common"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	confpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/config"
	"github.com/epifi/be-common/pkg/epifigrpc"
	epifigrpcresolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	"github.com/epifi/be-common/pkg/epifiserver"
	epifiTemporalClient "github.com/epifi/be-common/pkg/epifitemporal/client"
	epifiTemporalWorker "github.com/epifi/be-common/pkg/epifitemporal/worker"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	rudderLogger "github.com/epifi/be-common/pkg/logger/rudder_logger"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	caPb "github.com/epifi/gamma/api/connected_account"
	caAnalytics "github.com/epifi/gamma/api/connected_account/analytics"
	ignosisVgPb "github.com/epifi/gamma/api/vendorgateway/aa/analytics/ignosis"
	vmPb "github.com/epifi/gamma/api/vendormapping"
	caWorkerConf "github.com/epifi/gamma/connectedaccount/config/worker"
	"github.com/epifi/gamma/connectedaccount/wire"
	caWorkerflow "github.com/epifi/gamma/connectedaccount/workflow"
	"github.com/epifi/gamma/pkg/epifitemporal/interceptor/quest"
)

const (
	httpPrefix = "http://"
)

func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	epifiserver.HandlePanic()
	epifigrpcresolver.RegisterBuilder(epifigrpcresolver.ConsulSchemeName)

	env, err := cfg.GetEnvironment()
	if err != nil {
		panic(err)
	}

	conf, err := caWorkerConf.LoadConfig()
	if err != nil {
		panic(fmt.Errorf("failed to load config: %w", err))
	}

	logger.InitV2(env, conf.Application.Logging)

	c, err := epifiTemporalClient.NewWorkflowClient(conf.Application.GetNamespace(), true, conf.Secrets.Ids[config.TemporalCodecAesKey])
	if err != nil {
		logger.Panic("unable to create client", zap.Error(err))
	}

	defer c.Close()

	dbConnProvider, _, dbConnTeardown, err := storageV2.NewDBResourceProviderV2(conf.DbConfigMap.GetOwnershipToDbConfigMap(), conf.Tracing.Enable, nil)
	defer dbConnTeardown()
	if err != nil {
		logger.Panic("failed to get db resource provider", zap.Error(err))
	}

	caPgDb, err := storageV2.NewGormDB(conf.DbConfigMap.GetOwnershipToDbConfigMap()[commonTypesPb.Ownership_CONNECTED_ACCOUNT_WEALTH])
	if err != nil {
		logger.Panic("failed to get connected account db", zap.Error(err))
	}
	featureEngineeringPgDb, err := storageV2.NewGormDB(conf.DbConfigMap.GetOwnershipToDbConfigMap()[(commontypes.Ownership_FEATURE_ENGINEERING_TECH)])
	if err != nil {
		logger.Panic("failed to get feature engineering db", zap.Error(err))
	}

	if err = epifiTemporalWorker.InitWorkerPrerequisites(conf.WorkflowParamsList, conf.DefaultActivityParamsList); err != nil {
		logger.Panic("failed to init worker prerequisites", zap.Error(err))
	}

	wo := conf.Application.GetWorkerOptions()
	w, err := epifiTemporalWorker.GetWorker(
		c,
		conf.Application.TaskQueue,
		conf.Application.GetNamespace(),
		&wo,
		conf.Application.RedisConfig,
		conf.PausedWorkflowList,
		quest.NewQuestContextInterceptor(),
	)

	if err != nil {
		logger.Panic("failed to get worker", zap.Error(err))
	}

	awsConf, err := confpkg.NewAWSConfig(ctx, conf.AWS.Region, true)
	if err != nil {
		logger.Panic("failed to initialise aws session", zap.Error(err))
	}
	caAnalyticsS3Client := s3.NewClient(awsConf, conf.S3.BucketNames[caWorkerConf.CaAnalyticsBucketName])

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		conf.Secrets.Ids[caWorkerConf.RudderWriteKey], httpPrefix+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  conf.RudderStack.IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: conf.RudderStack.BatchSize,
			Verbose:   conf.RudderStack.Verbose,
			Logger:    rudderLogger.NewRudderLogger(),
		})
	if err != nil {
		panic(err)
	}
	broker := events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)
	ignosisVgClient := ignosisVgPb.NewIgnosisAaAnalyticsServiceClient(vgConn)

	vmConn := epifigrpc.NewConnByService(cfg.VENDORMAPPING_SERVICE)
	defer epifigrpc.CloseConn(vmConn)
	vmClient := vmPb.NewVendorMappingServiceClient(vmConn)

	caServiceConn := epifigrpc.NewConnByService(cfg.CONNECTED_ACC_SERVICE)
	caAnalyticsClient := caAnalytics.NewAnalyticsClient(caServiceConn)
	caClient := caPb.NewConnectedAccountClient(caServiceConn)
	defer epifigrpc.CloseConn(caServiceConn)

	wealthAnalysisActivities := wire.InitiateAnalysisIgnosisActivityProcessor(conf, caPgDb, dbConnProvider, ignosisVgClient,
		vmClient, caClient, caAnalyticsS3Client)
	w.RegisterActivity(wealthAnalysisActivities)
	activities := wire.InitialiseActivities(caPgDb, featureEngineeringPgDb, dbConnProvider, caAnalyticsClient, broker)
	w.RegisterActivity(activities)
	w.RegisterWorkflow(caWorkerflow.RefreshWealthAnalysis)
	w.RegisterWorkflow(caWorkerflow.ProcessTechAnalysisAttempt)

	initNotifier <- cfg.CONNECTED_ACCOUNT_WORKER_SERVER
	epifiTemporalWorker.StartWorker(c, w, conf.Server, conf.Application.TaskQueue)
	return nil
}
