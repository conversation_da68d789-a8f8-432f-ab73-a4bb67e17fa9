package integration

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"

	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/owners"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	authPb "github.com/epifi/gamma/api/auth"
	ffBeEnumPb "github.com/epifi/gamma/api/firefly/enums"
	accDao "github.com/epifi/gamma/firefly/accounting/dao/impl"
	"github.com/epifi/gamma/firefly/dao/impl"
	"github.com/epifi/gamma/testing/integration/app"
	"github.com/epifi/gamma/testing/integration/config"
)

func CreditCardOnboarding(t *testing.T) {
	defer Recover(t)
	a := require.New(t)
	ctx := context.Background()

	user, releaseUserFn := GetPooledUser(ctx, a)
	defer releaseUserFn()
	authHeader := user.RequestHeader

	awsConf, _ := awsconfpkg.NewAWSConfig(ctx, conf.AWS.Region, false)
	usersBuckets3Client := s3.NewClient(awsConf, conf.S3.UsersBucketName)
	authConn = epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	authClient = authPb.NewAuthClient(authConn)
	ccDb, err := storageV2.NewPostgresDBWithConfig(conf.Databases[config.CreditCardPgDb], true)
	a.NoError(err)

	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	ccOffersDao := impl.NewCrdbCreditCardOfferDao(ccDb, domainIdGenerator)
	cardRequestStageDao := impl.NewCrdbCardRequestStage(ccDb, domainIdGenerator)
	cardRequestDao := impl.NewCrdbCardRequest(ccDb, domainIdGenerator)
	creditCardDao := impl.NewCrdbCreditCard(ccDb, domainIdGenerator)
	creditAccountDao := accDao.NewCreditAccountDaoCRDB(ccDb, domainIdGenerator)
	ts := app.NewCreditCardTestSuite(feFireflyClient, authHeader, authOrchClient, feSignupClient, usersBuckets3Client,
		ccOffersDao, authClient, cardRequestStageDao, creditAccountDao, creditCardDao, cardRequestDao, conf.RedisCluster[config.CardRedis])

	actorId := ts.GetActorFromToken(ctx, user.RequestHeader, a)
	defer ts.DeleteCardDataForActorIfExists(ctx, actorId, ffBeEnumPb.CardRequestWorkFlow_CARD_REQUEST_WORKFLOW_TYPE_CARD_ONBOARDING, a)

	ts.StartPreapprovedUnsecuredCardOnboarding(ctx, a, user.Data.Video)
	ts.StartRealtimeBreApprovedOnboarding(ctx, a, user.Data.Video)
	ts.StartPreaprovedMassUnsecuredCardOnboarding(ctx, a, user.Data.Video)
	// TODO(priyansh) : Enable this once tested
	/*
		ts.OnboardingFailureDueToOfferDeactivated(ctx, a, user.Data.Video)
		ts.RealtimeBreFailureDueToNoLimitFromVendor(ctx, a, user.Data, t)
	*/

}

func CreditCardsTestCases() []*TestCase {
	return []*TestCase{
		{
			TestProperty: &TestProperty{
				Description: "This is test for credit card onboarding",
				BU:          owners.BUSINESS_UNIT_BANKING,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: CreditCardOnboarding,
		},
	}
}
