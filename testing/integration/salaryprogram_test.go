package integration

import (
	"context"
	"testing"

	"github.com/epifi/gamma/testing/integration/app"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/pkg/owners"

	"github.com/epifi/gamma/api/frontend/header"
)

func SalaryProgramFlows(t *testing.T) {
	defer Recover(t)
	a := require.New(t)
	ctx := context.Background()

	// get user from pool
	pooledUser, release := GetPooledUser(ctx, a)
	reqH := proto.Clone(pooledUser.RequestHeader).(*header.RequestHeader)
	defer release()

	// init salaryprogram test suite
	sts := app.NewSalaryProgramTestSuite(feSalaryProgramClient, beSalaryProgramClient, authClient, beBankCustClient, beSavingsClient)

	t.Run("salaryprogram tests", func(t *testing.T) {
		defer Recover(t)
		t.Run("running registration_flow", func(t *testing.T) {
			defer Recover(t)
			sts.TestRegistrationFlow_NonFullKycUser(t, reqH)
		})
		t.Run("running activation_flow", func(t *testing.T) {
			defer Recover(t)
			sts.TestActivationFlow_AutoVerifier(t, reqH, a)
		})
	})
}

func SalaryProgramTestCases() []*TestCase {
	return []*TestCase{
		{
			TestProperty: &TestProperty{
				Description: "This is the test for salary program flows",
				BU:          owners.BUSINESS_UNIT_BANKING,
				Scope:       []Scope{Smoke, Acceptance},
			},
			Test: SalaryProgramFlows,
		},
	}
}
