syntax = "proto3";

package vendornotification.whatsapp.airtel;

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "api/vendors/airtel/airtel.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/whatsapp/airtel";
option java_package = "com.github.epifi.gamma.api.vendornotification.whatsapp.airtel";

// Service to receive WhatsApp callbacks from Airtel
service AirtelCallback {
  rpc AirtelWhatsappDLR (vendors.airtel.AirtelWhatsappDLRPayload) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/whatsapp/callback/airtel/RealTimeDLR/requestListener"
      body: "*"
    };
  }
}
