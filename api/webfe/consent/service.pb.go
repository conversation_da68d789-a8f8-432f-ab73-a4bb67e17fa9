// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/webfe/consent/service.proto

package consent

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	header "github.com/epifi/gamma/api/frontend/header"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RecordConsentsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req         *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	ConsentList []string              `protobuf:"bytes,1,rep,name=consent_list,json=consentList,proto3" json:"consent_list,omitempty"`
	// The owner associated with this request. This field is important for compliance purposes.
	// string maps to api/typesv2/common/ownership.proto:Owner
	Owner string `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner,omitempty"`
	// Request ID sent by client
	ClientReqId           string             `protobuf:"bytes,3,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	RecordWhatsappConsent common.BooleanEnum `protobuf:"varint,4,opt,name=record_whatsapp_consent,json=recordWhatsappConsent,proto3,enum=api.typesv2.common.BooleanEnum" json:"record_whatsapp_consent,omitempty"`
}

func (x *RecordConsentsRequest) Reset() {
	*x = RecordConsentsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_consent_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordConsentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordConsentsRequest) ProtoMessage() {}

func (x *RecordConsentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_consent_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordConsentsRequest.ProtoReflect.Descriptor instead.
func (*RecordConsentsRequest) Descriptor() ([]byte, []int) {
	return file_api_webfe_consent_service_proto_rawDescGZIP(), []int{0}
}

func (x *RecordConsentsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *RecordConsentsRequest) GetConsentList() []string {
	if x != nil {
		return x.ConsentList
	}
	return nil
}

func (x *RecordConsentsRequest) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *RecordConsentsRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *RecordConsentsRequest) GetRecordWhatsappConsent() common.BooleanEnum {
	if x != nil {
		return x.RecordWhatsappConsent
	}
	return common.BooleanEnum(0)
}

type RecordConsentsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Status     *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RecordConsentsResponse) Reset() {
	*x = RecordConsentsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_webfe_consent_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordConsentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordConsentsResponse) ProtoMessage() {}

func (x *RecordConsentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_webfe_consent_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordConsentsResponse.ProtoReflect.Descriptor instead.
func (*RecordConsentsResponse) Descriptor() ([]byte, []int) {
	return file_api_webfe_consent_service_proto_rawDescGZIP(), []int{1}
}

func (x *RecordConsentsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *RecordConsentsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_webfe_consent_service_proto protoreflect.FileDescriptor

var file_api_webfe_consent_service_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xff, 0x01,
	0x0a, 0x15, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e,
	0x65, 0x72, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x57, 0x0a, 0x17, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x5f, 0x77, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f,
	0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x15, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x57, 0x68, 0x61, 0x74, 0x73, 0x61, 0x70, 0x70, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x22,
	0x7f, 0x0a, 0x16, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73,
	0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x32, 0x79, 0x0a, 0x07, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x6e, 0x0a, 0x0e, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x24, 0x2e,
	0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65,
	0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x0f, 0x80, 0x9e, 0xd7, 0x0a,
	0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x42, 0x54, 0x0a, 0x28, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2e,
	0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x77, 0x65, 0x62, 0x66, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_webfe_consent_service_proto_rawDescOnce sync.Once
	file_api_webfe_consent_service_proto_rawDescData = file_api_webfe_consent_service_proto_rawDesc
)

func file_api_webfe_consent_service_proto_rawDescGZIP() []byte {
	file_api_webfe_consent_service_proto_rawDescOnce.Do(func() {
		file_api_webfe_consent_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_webfe_consent_service_proto_rawDescData)
	})
	return file_api_webfe_consent_service_proto_rawDescData
}

var file_api_webfe_consent_service_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_webfe_consent_service_proto_goTypes = []interface{}{
	(*RecordConsentsRequest)(nil),  // 0: webfe.consent.RecordConsentsRequest
	(*RecordConsentsResponse)(nil), // 1: webfe.consent.RecordConsentsResponse
	(*header.RequestHeader)(nil),   // 2: frontend.header.RequestHeader
	(common.BooleanEnum)(0),        // 3: api.typesv2.common.BooleanEnum
	(*header.ResponseHeader)(nil),  // 4: frontend.header.ResponseHeader
	(*rpc.Status)(nil),             // 5: rpc.Status
}
var file_api_webfe_consent_service_proto_depIdxs = []int32{
	2, // 0: webfe.consent.RecordConsentsRequest.req:type_name -> frontend.header.RequestHeader
	3, // 1: webfe.consent.RecordConsentsRequest.record_whatsapp_consent:type_name -> api.typesv2.common.BooleanEnum
	4, // 2: webfe.consent.RecordConsentsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	5, // 3: webfe.consent.RecordConsentsResponse.status:type_name -> rpc.Status
	0, // 4: webfe.consent.Consent.RecordConsents:input_type -> webfe.consent.RecordConsentsRequest
	1, // 5: webfe.consent.Consent.RecordConsents:output_type -> webfe.consent.RecordConsentsResponse
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_api_webfe_consent_service_proto_init() }
func file_api_webfe_consent_service_proto_init() {
	if File_api_webfe_consent_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_webfe_consent_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordConsentsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_webfe_consent_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordConsentsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_webfe_consent_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_webfe_consent_service_proto_goTypes,
		DependencyIndexes: file_api_webfe_consent_service_proto_depIdxs,
		MessageInfos:      file_api_webfe_consent_service_proto_msgTypes,
	}.Build()
	File_api_webfe_consent_service_proto = out.File
	file_api_webfe_consent_service_proto_rawDesc = nil
	file_api_webfe_consent_service_proto_goTypes = nil
	file_api_webfe_consent_service_proto_depIdxs = nil
}
