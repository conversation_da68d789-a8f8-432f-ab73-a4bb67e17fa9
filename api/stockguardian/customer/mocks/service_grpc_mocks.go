// Code generated by MockGen. DO NOT EDIT.
// Source: api/stockguardian/customer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	customer "github.com/epifi/gamma/api/stockguardian/customer"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCustomerServiceClient is a mock of CustomerServiceClient interface.
type MockCustomerServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockCustomerServiceClientMockRecorder
}

// MockCustomerServiceClientMockRecorder is the mock recorder for MockCustomerServiceClient.
type MockCustomerServiceClientMockRecorder struct {
	mock *MockCustomerServiceClient
}

// NewMockCustomerServiceClient creates a new mock instance.
func NewMockCustomerServiceClient(ctrl *gomock.Controller) *MockCustomerServiceClient {
	mock := &MockCustomerServiceClient{ctrl: ctrl}
	mock.recorder = &MockCustomerServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCustomerServiceClient) EXPECT() *MockCustomerServiceClientMockRecorder {
	return m.recorder
}

// CreateCustomer mocks base method.
func (m *MockCustomerServiceClient) CreateCustomer(ctx context.Context, in *customer.CreateCustomerRequest, opts ...grpc.CallOption) (*customer.CreateCustomerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateCustomer", varargs...)
	ret0, _ := ret[0].(*customer.CreateCustomerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCustomer indicates an expected call of CreateCustomer.
func (mr *MockCustomerServiceClientMockRecorder) CreateCustomer(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCustomer", reflect.TypeOf((*MockCustomerServiceClient)(nil).CreateCustomer), varargs...)
}

// DedupeCheck mocks base method.
func (m *MockCustomerServiceClient) DedupeCheck(ctx context.Context, in *customer.DedupeCheckRequest, opts ...grpc.CallOption) (*customer.DedupeCheckResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DedupeCheck", varargs...)
	ret0, _ := ret[0].(*customer.DedupeCheckResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DedupeCheck indicates an expected call of DedupeCheck.
func (mr *MockCustomerServiceClientMockRecorder) DedupeCheck(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DedupeCheck", reflect.TypeOf((*MockCustomerServiceClient)(nil).DedupeCheck), varargs...)
}

// GetCustomerDetails mocks base method.
func (m *MockCustomerServiceClient) GetCustomerDetails(ctx context.Context, in *customer.GetCustomerDetailsRequest, opts ...grpc.CallOption) (*customer.GetCustomerDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCustomerDetails", varargs...)
	ret0, _ := ret[0].(*customer.GetCustomerDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerDetails indicates an expected call of GetCustomerDetails.
func (mr *MockCustomerServiceClientMockRecorder) GetCustomerDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerDetails", reflect.TypeOf((*MockCustomerServiceClient)(nil).GetCustomerDetails), varargs...)
}

// MockCustomerServiceServer is a mock of CustomerServiceServer interface.
type MockCustomerServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockCustomerServiceServerMockRecorder
}

// MockCustomerServiceServerMockRecorder is the mock recorder for MockCustomerServiceServer.
type MockCustomerServiceServerMockRecorder struct {
	mock *MockCustomerServiceServer
}

// NewMockCustomerServiceServer creates a new mock instance.
func NewMockCustomerServiceServer(ctrl *gomock.Controller) *MockCustomerServiceServer {
	mock := &MockCustomerServiceServer{ctrl: ctrl}
	mock.recorder = &MockCustomerServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCustomerServiceServer) EXPECT() *MockCustomerServiceServerMockRecorder {
	return m.recorder
}

// CreateCustomer mocks base method.
func (m *MockCustomerServiceServer) CreateCustomer(arg0 context.Context, arg1 *customer.CreateCustomerRequest) (*customer.CreateCustomerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCustomer", arg0, arg1)
	ret0, _ := ret[0].(*customer.CreateCustomerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCustomer indicates an expected call of CreateCustomer.
func (mr *MockCustomerServiceServerMockRecorder) CreateCustomer(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCustomer", reflect.TypeOf((*MockCustomerServiceServer)(nil).CreateCustomer), arg0, arg1)
}

// DedupeCheck mocks base method.
func (m *MockCustomerServiceServer) DedupeCheck(arg0 context.Context, arg1 *customer.DedupeCheckRequest) (*customer.DedupeCheckResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DedupeCheck", arg0, arg1)
	ret0, _ := ret[0].(*customer.DedupeCheckResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DedupeCheck indicates an expected call of DedupeCheck.
func (mr *MockCustomerServiceServerMockRecorder) DedupeCheck(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DedupeCheck", reflect.TypeOf((*MockCustomerServiceServer)(nil).DedupeCheck), arg0, arg1)
}

// GetCustomerDetails mocks base method.
func (m *MockCustomerServiceServer) GetCustomerDetails(arg0 context.Context, arg1 *customer.GetCustomerDetailsRequest) (*customer.GetCustomerDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerDetails", arg0, arg1)
	ret0, _ := ret[0].(*customer.GetCustomerDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerDetails indicates an expected call of GetCustomerDetails.
func (mr *MockCustomerServiceServerMockRecorder) GetCustomerDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerDetails", reflect.TypeOf((*MockCustomerServiceServer)(nil).GetCustomerDetails), arg0, arg1)
}

// MockUnsafeCustomerServiceServer is a mock of UnsafeCustomerServiceServer interface.
type MockUnsafeCustomerServiceServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCustomerServiceServerMockRecorder
}

// MockUnsafeCustomerServiceServerMockRecorder is the mock recorder for MockUnsafeCustomerServiceServer.
type MockUnsafeCustomerServiceServerMockRecorder struct {
	mock *MockUnsafeCustomerServiceServer
}

// NewMockUnsafeCustomerServiceServer creates a new mock instance.
func NewMockUnsafeCustomerServiceServer(ctrl *gomock.Controller) *MockUnsafeCustomerServiceServer {
	mock := &MockUnsafeCustomerServiceServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCustomerServiceServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCustomerServiceServer) EXPECT() *MockUnsafeCustomerServiceServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCustomerServiceServer mocks base method.
func (m *MockUnsafeCustomerServiceServer) mustEmbedUnimplementedCustomerServiceServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCustomerServiceServer")
}

// mustEmbedUnimplementedCustomerServiceServer indicates an expected call of mustEmbedUnimplementedCustomerServiceServer.
func (mr *MockUnsafeCustomerServiceServerMockRecorder) mustEmbedUnimplementedCustomerServiceServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCustomerServiceServer", reflect.TypeOf((*MockUnsafeCustomerServiceServer)(nil).mustEmbedUnimplementedCustomerServiceServer))
}
