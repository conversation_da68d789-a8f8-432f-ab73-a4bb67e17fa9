syntax = "proto3";

package stockguardian.sgsimulator.creditreport.cibil;

import "api/stockguardian/vendors/cibil/customer_risk_and_income_assessment.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgsimulator/creditreport/cibil";

service CreditReportService {
  rpc GetConsumerCreditRiskAndIncomeAssessment (vendors.cibil.ConsumerCreditRiskAndIncomeAssessmentRequest) returns (vendors.cibil.ConsumerCreditRiskAndIncomeAssessmentResponse) {
    option (google.api.http) = {
      post: "/creditreport/consumer-credit-risk-and-income-assessment"
      body: "*"
    };
  };
}