// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/stockguardian/sgapigateway/application/service.proto

package application

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	bre "github.com/epifi/gamma/api/stockguardian/vendors/inhouse/bre"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StartApplicationResponse_Status int32

const (
	StartApplicationResponse_OK StartApplicationResponse_Status = 0
	// If application already exist
	StartApplicationResponse_ALREADY_EXISTS StartApplicationResponse_Status = 6
	// internal error while processing the request
	StartApplicationResponse_INTERNAL                                     StartApplicationResponse_Status = 13
	StartApplicationResponse_FAILED_PRECONDITION_APPLICANT_DOES_NOT_EXIST StartApplicationResponse_Status = 100
)

// Enum value maps for StartApplicationResponse_Status.
var (
	StartApplicationResponse_Status_name = map[int32]string{
		0:   "OK",
		6:   "ALREADY_EXISTS",
		13:  "INTERNAL",
		100: "FAILED_PRECONDITION_APPLICANT_DOES_NOT_EXIST",
	}
	StartApplicationResponse_Status_value = map[string]int32{
		"OK":             0,
		"ALREADY_EXISTS": 6,
		"INTERNAL":       13,
		"FAILED_PRECONDITION_APPLICANT_DOES_NOT_EXIST": 100,
	}
)

func (x StartApplicationResponse_Status) Enum() *StartApplicationResponse_Status {
	p := new(StartApplicationResponse_Status)
	*p = x
	return p
}

func (x StartApplicationResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StartApplicationResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapigateway_application_service_proto_enumTypes[0].Descriptor()
}

func (StartApplicationResponse_Status) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapigateway_application_service_proto_enumTypes[0]
}

func (x StartApplicationResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StartApplicationResponse_Status.Descriptor instead.
func (StartApplicationResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{4, 0}
}

type GetLoanOfferResponse_Status int32

const (
	GetLoanOfferResponse_OK                                  GetLoanOfferResponse_Status = 0
	GetLoanOfferResponse_INTERNAL                            GetLoanOfferResponse_Status = 13
	GetLoanOfferResponse_OFFER_GENERATION_IN_PROGRESS        GetLoanOfferResponse_Status = 102
	GetLoanOfferResponse_OFFER_REJECTED                      GetLoanOfferResponse_Status = 103
	GetLoanOfferResponse_OFFER_EXPIRED                       GetLoanOfferResponse_Status = 104
	GetLoanOfferResponse_OFFER_LOCKED_BY_ANOTHER_APPLICATION GetLoanOfferResponse_Status = 105
)

// Enum value maps for GetLoanOfferResponse_Status.
var (
	GetLoanOfferResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		102: "OFFER_GENERATION_IN_PROGRESS",
		103: "OFFER_REJECTED",
		104: "OFFER_EXPIRED",
		105: "OFFER_LOCKED_BY_ANOTHER_APPLICATION",
	}
	GetLoanOfferResponse_Status_value = map[string]int32{
		"OK":                                  0,
		"INTERNAL":                            13,
		"OFFER_GENERATION_IN_PROGRESS":        102,
		"OFFER_REJECTED":                      103,
		"OFFER_EXPIRED":                       104,
		"OFFER_LOCKED_BY_ANOTHER_APPLICATION": 105,
	}
)

func (x GetLoanOfferResponse_Status) Enum() *GetLoanOfferResponse_Status {
	p := new(GetLoanOfferResponse_Status)
	*p = x
	return p
}

func (x GetLoanOfferResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetLoanOfferResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapigateway_application_service_proto_enumTypes[1].Descriptor()
}

func (GetLoanOfferResponse_Status) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapigateway_application_service_proto_enumTypes[1]
}

func (x GetLoanOfferResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetLoanOfferResponse_Status.Descriptor instead.
func (GetLoanOfferResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{10, 0}
}

type InitiateKycResponse_Status int32

const (
	InitiateKycResponse_OK                    InitiateKycResponse_Status = 0
	InitiateKycResponse_INTERNAL              InitiateKycResponse_Status = 13
	InitiateKycResponse_KYC_ALREADY_COMPLETED InitiateKycResponse_Status = 100
)

// Enum value maps for InitiateKycResponse_Status.
var (
	InitiateKycResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		100: "KYC_ALREADY_COMPLETED",
	}
	InitiateKycResponse_Status_value = map[string]int32{
		"OK":                    0,
		"INTERNAL":              13,
		"KYC_ALREADY_COMPLETED": 100,
	}
)

func (x InitiateKycResponse_Status) Enum() *InitiateKycResponse_Status {
	p := new(InitiateKycResponse_Status)
	*p = x
	return p
}

func (x InitiateKycResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitiateKycResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapigateway_application_service_proto_enumTypes[2].Descriptor()
}

func (InitiateKycResponse_Status) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapigateway_application_service_proto_enumTypes[2]
}

func (x InitiateKycResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitiateKycResponse_Status.Descriptor instead.
func (InitiateKycResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{13, 0}
}

type InitDrawDownResponse_Status int32

const (
	InitDrawDownResponse_STATUS_OK                InitDrawDownResponse_Status = 0
	InitDrawDownResponse_STATUS_VALIDATION_FAILED InitDrawDownResponse_Status = 100
	InitDrawDownResponse_PERMANANT_FAILURE        InitDrawDownResponse_Status = 101
)

// Enum value maps for InitDrawDownResponse_Status.
var (
	InitDrawDownResponse_Status_name = map[int32]string{
		0:   "STATUS_OK",
		100: "STATUS_VALIDATION_FAILED",
		101: "PERMANANT_FAILURE",
	}
	InitDrawDownResponse_Status_value = map[string]int32{
		"STATUS_OK":                0,
		"STATUS_VALIDATION_FAILED": 100,
		"PERMANANT_FAILURE":        101,
	}
)

func (x InitDrawDownResponse_Status) Enum() *InitDrawDownResponse_Status {
	p := new(InitDrawDownResponse_Status)
	*p = x
	return p
}

func (x InitDrawDownResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitDrawDownResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapigateway_application_service_proto_enumTypes[3].Descriptor()
}

func (InitDrawDownResponse_Status) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapigateway_application_service_proto_enumTypes[3]
}

func (x InitDrawDownResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitDrawDownResponse_Status.Descriptor instead.
func (InitDrawDownResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{18, 0}
}

type InitEmploymentCheckResponse_Status int32

const (
	InitEmploymentCheckResponse_STATUS_OK           InitEmploymentCheckResponse_Status = 0
	InitEmploymentCheckResponse_STATUS_PENDING      InitEmploymentCheckResponse_Status = 100
	InitEmploymentCheckResponse_STATUS_FAILED       InitEmploymentCheckResponse_Status = 101
	InitEmploymentCheckResponse_STATUS_NOT_REQUIRED InitEmploymentCheckResponse_Status = 102
)

// Enum value maps for InitEmploymentCheckResponse_Status.
var (
	InitEmploymentCheckResponse_Status_name = map[int32]string{
		0:   "STATUS_OK",
		100: "STATUS_PENDING",
		101: "STATUS_FAILED",
		102: "STATUS_NOT_REQUIRED",
	}
	InitEmploymentCheckResponse_Status_value = map[string]int32{
		"STATUS_OK":           0,
		"STATUS_PENDING":      100,
		"STATUS_FAILED":       101,
		"STATUS_NOT_REQUIRED": 102,
	}
)

func (x InitEmploymentCheckResponse_Status) Enum() *InitEmploymentCheckResponse_Status {
	p := new(InitEmploymentCheckResponse_Status)
	*p = x
	return p
}

func (x InitEmploymentCheckResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitEmploymentCheckResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_stockguardian_sgapigateway_application_service_proto_enumTypes[4].Descriptor()
}

func (InitEmploymentCheckResponse_Status) Type() protoreflect.EnumType {
	return &file_api_stockguardian_sgapigateway_application_service_proto_enumTypes[4]
}

func (x InitEmploymentCheckResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitEmploymentCheckResponse_Status.Descriptor instead.
func (InitEmploymentCheckResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{38, 0}
}

type InitDisbursalRetryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	// loan application ids
	ApplicationIds []string `protobuf:"bytes,2,rep,name=application_ids,json=applicationIds,proto3" json:"application_ids,omitempty"`
}

func (x *InitDisbursalRetryRequest) Reset() {
	*x = InitDisbursalRetryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitDisbursalRetryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitDisbursalRetryRequest) ProtoMessage() {}

func (x *InitDisbursalRetryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitDisbursalRetryRequest.ProtoReflect.Descriptor instead.
func (*InitDisbursalRetryRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{0}
}

func (x *InitDisbursalRetryRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *InitDisbursalRetryRequest) GetApplicationIds() []string {
	if x != nil {
		return x.ApplicationIds
	}
	return nil
}

type InitDisbursalRetryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                    *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SuccessLoanApplicationIds []string    `protobuf:"bytes,2,rep,name=success_loan_application_ids,json=successLoanApplicationIds,proto3" json:"success_loan_application_ids,omitempty"`
	FailureLoanApplicationIds []string    `protobuf:"bytes,3,rep,name=failure_loan_application_ids,json=failureLoanApplicationIds,proto3" json:"failure_loan_application_ids,omitempty"`
	InvalidLoanApplicationIds []string    `protobuf:"bytes,4,rep,name=invalid_loan_application_ids,json=invalidLoanApplicationIds,proto3" json:"invalid_loan_application_ids,omitempty"`
}

func (x *InitDisbursalRetryResponse) Reset() {
	*x = InitDisbursalRetryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitDisbursalRetryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitDisbursalRetryResponse) ProtoMessage() {}

func (x *InitDisbursalRetryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitDisbursalRetryResponse.ProtoReflect.Descriptor instead.
func (*InitDisbursalRetryResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{1}
}

func (x *InitDisbursalRetryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitDisbursalRetryResponse) GetSuccessLoanApplicationIds() []string {
	if x != nil {
		return x.SuccessLoanApplicationIds
	}
	return nil
}

func (x *InitDisbursalRetryResponse) GetFailureLoanApplicationIds() []string {
	if x != nil {
		return x.FailureLoanApplicationIds
	}
	return nil
}

func (x *InitDisbursalRetryResponse) GetInvalidLoanApplicationIds() []string {
	if x != nil {
		return x.InvalidLoanApplicationIds
	}
	return nil
}

type LoanHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClientId string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
}

func (x *LoanHeader) Reset() {
	*x = LoanHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanHeader) ProtoMessage() {}

func (x *LoanHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanHeader.ProtoReflect.Descriptor instead.
func (*LoanHeader) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{2}
}

func (x *LoanHeader) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

type StartApplicationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader  *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ProductId   string      `protobuf:"bytes,2,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
	ApplicantId string      `protobuf:"bytes,3,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
}

func (x *StartApplicationRequest) Reset() {
	*x = StartApplicationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartApplicationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartApplicationRequest) ProtoMessage() {}

func (x *StartApplicationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartApplicationRequest.ProtoReflect.Descriptor instead.
func (*StartApplicationRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{3}
}

func (x *StartApplicationRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *StartApplicationRequest) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *StartApplicationRequest) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

type StartApplicationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *StartApplicationResponse) Reset() {
	*x = StartApplicationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartApplicationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartApplicationResponse) ProtoMessage() {}

func (x *StartApplicationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartApplicationResponse.ProtoReflect.Descriptor instead.
func (*StartApplicationResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{4}
}

func (x *StartApplicationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *StartApplicationResponse) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type CancelApplicationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *CancelApplicationRequest) Reset() {
	*x = CancelApplicationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelApplicationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelApplicationRequest) ProtoMessage() {}

func (x *CancelApplicationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelApplicationRequest.ProtoReflect.Descriptor instead.
func (*CancelApplicationRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{5}
}

func (x *CancelApplicationRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *CancelApplicationRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type CancelApplicationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CancelApplicationResponse) Reset() {
	*x = CancelApplicationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelApplicationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelApplicationResponse) ProtoMessage() {}

func (x *CancelApplicationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelApplicationResponse.ProtoReflect.Descriptor instead.
func (*CancelApplicationResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{6}
}

func (x *CancelApplicationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetApplicationStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *GetApplicationStatusRequest) Reset() {
	*x = GetApplicationStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicationStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicationStatusRequest) ProtoMessage() {}

func (x *GetApplicationStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicationStatusRequest.ProtoReflect.Descriptor instead.
func (*GetApplicationStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetApplicationStatusRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *GetApplicationStatusRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type GetApplicationStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status                                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	StageDetails []*GetApplicationStatusResponse_StageStatus `protobuf:"bytes,2,rep,name=stage_details,json=stageDetails,proto3" json:"stage_details,omitempty"`
}

func (x *GetApplicationStatusResponse) Reset() {
	*x = GetApplicationStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicationStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicationStatusResponse) ProtoMessage() {}

func (x *GetApplicationStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicationStatusResponse.ProtoReflect.Descriptor instead.
func (*GetApplicationStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetApplicationStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetApplicationStatusResponse) GetStageDetails() []*GetApplicationStatusResponse_StageStatus {
	if x != nil {
		return x.StageDetails
	}
	return nil
}

type GetLoanOfferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	// open market user is someone who was not initially evaluated offline by LSP and all evaluation is happening real time
	IsOpenMarketUser bool `protobuf:"varint,3,opt,name=is_open_market_user,json=isOpenMarketUser,proto3" json:"is_open_market_user,omitempty"`
	// this is metadata sent by LSP to be sent to BRE for evaluation of offer generation
	BreParams *GetLoanOfferRequest_BreParams `protobuf:"bytes,4,opt,name=bre_params,json=breParams,proto3" json:"bre_params,omitempty"`
}

func (x *GetLoanOfferRequest) Reset() {
	*x = GetLoanOfferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanOfferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanOfferRequest) ProtoMessage() {}

func (x *GetLoanOfferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanOfferRequest.ProtoReflect.Descriptor instead.
func (*GetLoanOfferRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetLoanOfferRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *GetLoanOfferRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *GetLoanOfferRequest) GetIsOpenMarketUser() bool {
	if x != nil {
		return x.IsOpenMarketUser
	}
	return false
}

func (x *GetLoanOfferRequest) GetBreParams() *GetLoanOfferRequest_BreParams {
	if x != nil {
		return x.BreParams
	}
	return nil
}

type GetLoanOfferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status               `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Details *LoanOfferDetailsResponse `protobuf:"bytes,2,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *GetLoanOfferResponse) Reset() {
	*x = GetLoanOfferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanOfferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanOfferResponse) ProtoMessage() {}

func (x *GetLoanOfferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanOfferResponse.ProtoReflect.Descriptor instead.
func (*GetLoanOfferResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetLoanOfferResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanOfferResponse) GetDetails() *LoanOfferDetailsResponse {
	if x != nil {
		return x.Details
	}
	return nil
}

type LoanOfferDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinAmount               *money.Money `protobuf:"bytes,2,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	MaxAmount               *money.Money `protobuf:"bytes,3,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	MaxEmiAmount            *money.Money `protobuf:"bytes,4,opt,name=max_emi_amount,json=maxEmiAmount,proto3" json:"max_emi_amount,omitempty"`
	InterestPercentage      float64      `protobuf:"fixed64,5,opt,name=interest_percentage,json=interestPercentage,proto3" json:"interest_percentage,omitempty"`
	ProcessingFeePercentage float64      `protobuf:"fixed64,6,opt,name=processing_fee_percentage,json=processingFeePercentage,proto3" json:"processing_fee_percentage,omitempty"`
	GstPercentage           float64      `protobuf:"fixed64,7,opt,name=gst_percentage,json=gstPercentage,proto3" json:"gst_percentage,omitempty"`
	MinTenureInMonths       int32        `protobuf:"varint,8,opt,name=min_tenure_in_months,json=minTenureInMonths,proto3" json:"min_tenure_in_months,omitempty"`
	MaxTenureInMonths       int32        `protobuf:"varint,9,opt,name=max_tenure_in_months,json=maxTenureInMonths,proto3" json:"max_tenure_in_months,omitempty"`
}

func (x *LoanOfferDetailsResponse) Reset() {
	*x = LoanOfferDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanOfferDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanOfferDetailsResponse) ProtoMessage() {}

func (x *LoanOfferDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanOfferDetailsResponse.ProtoReflect.Descriptor instead.
func (*LoanOfferDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{11}
}

func (x *LoanOfferDetailsResponse) GetMinAmount() *money.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

func (x *LoanOfferDetailsResponse) GetMaxAmount() *money.Money {
	if x != nil {
		return x.MaxAmount
	}
	return nil
}

func (x *LoanOfferDetailsResponse) GetMaxEmiAmount() *money.Money {
	if x != nil {
		return x.MaxEmiAmount
	}
	return nil
}

func (x *LoanOfferDetailsResponse) GetInterestPercentage() float64 {
	if x != nil {
		return x.InterestPercentage
	}
	return 0
}

func (x *LoanOfferDetailsResponse) GetProcessingFeePercentage() float64 {
	if x != nil {
		return x.ProcessingFeePercentage
	}
	return 0
}

func (x *LoanOfferDetailsResponse) GetGstPercentage() float64 {
	if x != nil {
		return x.GstPercentage
	}
	return 0
}

func (x *LoanOfferDetailsResponse) GetMinTenureInMonths() int32 {
	if x != nil {
		return x.MinTenureInMonths
	}
	return 0
}

func (x *LoanOfferDetailsResponse) GetMaxTenureInMonths() int32 {
	if x != nil {
		return x.MaxTenureInMonths
	}
	return 0
}

type InitiateKycRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *InitiateKycRequest) Reset() {
	*x = InitiateKycRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateKycRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateKycRequest) ProtoMessage() {}

func (x *InitiateKycRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateKycRequest.ProtoReflect.Descriptor instead.
func (*InitiateKycRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{12}
}

func (x *InitiateKycRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *InitiateKycRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type InitiateKycResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ApplicantId string      `protobuf:"bytes,3,opt,name=applicant_id,json=applicantId,proto3" json:"applicant_id,omitempty"`
	// External id for KYC stage in loan application stages.
	ClientRefId      string `protobuf:"bytes,4,opt,name=client_ref_id,json=clientRefId,proto3" json:"client_ref_id,omitempty"`
	KycApplicationId string `protobuf:"bytes,5,opt,name=kyc_application_id,json=kycApplicationId,proto3" json:"kyc_application_id,omitempty"`
}

func (x *InitiateKycResponse) Reset() {
	*x = InitiateKycResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateKycResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateKycResponse) ProtoMessage() {}

func (x *InitiateKycResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateKycResponse.ProtoReflect.Descriptor instead.
func (*InitiateKycResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{13}
}

func (x *InitiateKycResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitiateKycResponse) GetApplicantId() string {
	if x != nil {
		return x.ApplicantId
	}
	return ""
}

func (x *InitiateKycResponse) GetClientRefId() string {
	if x != nil {
		return x.ClientRefId
	}
	return ""
}

func (x *InitiateKycResponse) GetKycApplicationId() string {
	if x != nil {
		return x.KycApplicationId
	}
	return ""
}

type UpdateApplicationDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader          `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string               `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	Details       *UserSelectedDetails `protobuf:"bytes,3,opt,name=details,proto3" json:"details,omitempty"`
}

func (x *UpdateApplicationDetailsRequest) Reset() {
	*x = UpdateApplicationDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateApplicationDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApplicationDetailsRequest) ProtoMessage() {}

func (x *UpdateApplicationDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApplicationDetailsRequest.ProtoReflect.Descriptor instead.
func (*UpdateApplicationDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateApplicationDetailsRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *UpdateApplicationDetailsRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *UpdateApplicationDetailsRequest) GetDetails() *UserSelectedDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

type UserSelectedDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanAmount     *money.Money `protobuf:"bytes,1,opt,name=loan_amount,json=loanAmount,proto3" json:"loan_amount,omitempty"`
	TenureInMonths int32        `protobuf:"varint,2,opt,name=tenure_in_months,json=tenureInMonths,proto3" json:"tenure_in_months,omitempty"`
	InterestRate   float64      `protobuf:"fixed64,3,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
}

func (x *UserSelectedDetails) Reset() {
	*x = UserSelectedDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSelectedDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSelectedDetails) ProtoMessage() {}

func (x *UserSelectedDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSelectedDetails.ProtoReflect.Descriptor instead.
func (*UserSelectedDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{15}
}

func (x *UserSelectedDetails) GetLoanAmount() *money.Money {
	if x != nil {
		return x.LoanAmount
	}
	return nil
}

func (x *UserSelectedDetails) GetTenureInMonths() int32 {
	if x != nil {
		return x.TenureInMonths
	}
	return 0
}

func (x *UserSelectedDetails) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

type UpdateApplicationDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateApplicationDetailsResponse) Reset() {
	*x = UpdateApplicationDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateApplicationDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateApplicationDetailsResponse) ProtoMessage() {}

func (x *UpdateApplicationDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateApplicationDetailsResponse.ProtoReflect.Descriptor instead.
func (*UpdateApplicationDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateApplicationDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type InitDrawDownRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *InitDrawDownRequest) Reset() {
	*x = InitDrawDownRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitDrawDownRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitDrawDownRequest) ProtoMessage() {}

func (x *InitDrawDownRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitDrawDownRequest.ProtoReflect.Descriptor instead.
func (*InitDrawDownRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{17}
}

func (x *InitDrawDownRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *InitDrawDownRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type InitDrawDownResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *InitDrawDownResponse) Reset() {
	*x = InitDrawDownResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitDrawDownResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitDrawDownResponse) ProtoMessage() {}

func (x *InitDrawDownResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitDrawDownResponse.ProtoReflect.Descriptor instead.
func (*InitDrawDownResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{18}
}

func (x *InitDrawDownResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type InitMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	RedirectLink  []byte      `protobuf:"bytes,3,opt,name=redirect_link,json=redirectLink,proto3" json:"redirect_link,omitempty"`
}

func (x *InitMandateRequest) Reset() {
	*x = InitMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitMandateRequest) ProtoMessage() {}

func (x *InitMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitMandateRequest.ProtoReflect.Descriptor instead.
func (*InitMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{19}
}

func (x *InitMandateRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *InitMandateRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *InitMandateRequest) GetRedirectLink() []byte {
	if x != nil {
		return x.RedirectLink
	}
	return nil
}

type InitMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	OrchId string      `protobuf:"bytes,2,opt,name=orch_id,json=orchId,proto3" json:"orch_id,omitempty"`
}

func (x *InitMandateResponse) Reset() {
	*x = InitMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitMandateResponse) ProtoMessage() {}

func (x *InitMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitMandateResponse.ProtoReflect.Descriptor instead.
func (*InitMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{20}
}

func (x *InitMandateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitMandateResponse) GetOrchId() string {
	if x != nil {
		return x.OrchId
	}
	return ""
}

type InitEsignRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *InitEsignRequest) Reset() {
	*x = InitEsignRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitEsignRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitEsignRequest) ProtoMessage() {}

func (x *InitEsignRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitEsignRequest.ProtoReflect.Descriptor instead.
func (*InitEsignRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{21}
}

func (x *InitEsignRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *InitEsignRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type InitEsignResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	OrchId string      `protobuf:"bytes,2,opt,name=orch_id,json=orchId,proto3" json:"orch_id,omitempty"`
}

func (x *InitEsignResponse) Reset() {
	*x = InitEsignResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitEsignResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitEsignResponse) ProtoMessage() {}

func (x *InitEsignResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitEsignResponse.ProtoReflect.Descriptor instead.
func (*InitEsignResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{22}
}

func (x *InitEsignResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitEsignResponse) GetOrchId() string {
	if x != nil {
		return x.OrchId
	}
	return ""
}

type UpdateUserDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader      *LoanHeader                `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId   string                     `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	UpdateFieldMask UpdateUserDetailsFieldMask `protobuf:"varint,3,opt,name=update_field_mask,json=updateFieldMask,proto3,enum=stockguardian.sgapigateway.application.UpdateUserDetailsFieldMask" json:"update_field_mask,omitempty"`
	// Types that are assignable to Details:
	//
	//	*UpdateUserDetailsRequest_AddressDetails
	//	*UpdateUserDetailsRequest_EmploymentDetails
	//	*UpdateUserDetailsRequest_BankAccountDetails
	//	*UpdateUserDetailsRequest_PersonalDetails
	//	*UpdateUserDetailsRequest_VerificationDetails
	Details isUpdateUserDetailsRequest_Details `protobuf_oneof:"details"`
}

func (x *UpdateUserDetailsRequest) Reset() {
	*x = UpdateUserDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserDetailsRequest) ProtoMessage() {}

func (x *UpdateUserDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserDetailsRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{23}
}

func (x *UpdateUserDetailsRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *UpdateUserDetailsRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *UpdateUserDetailsRequest) GetUpdateFieldMask() UpdateUserDetailsFieldMask {
	if x != nil {
		return x.UpdateFieldMask
	}
	return UpdateUserDetailsFieldMask_UPDATE_USER_DETAILS_FIELD_MASK_UNSPECIFIED
}

func (m *UpdateUserDetailsRequest) GetDetails() isUpdateUserDetailsRequest_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *UpdateUserDetailsRequest) GetAddressDetails() *AddressDetails {
	if x, ok := x.GetDetails().(*UpdateUserDetailsRequest_AddressDetails); ok {
		return x.AddressDetails
	}
	return nil
}

func (x *UpdateUserDetailsRequest) GetEmploymentDetails() *EmploymentDetails {
	if x, ok := x.GetDetails().(*UpdateUserDetailsRequest_EmploymentDetails); ok {
		return x.EmploymentDetails
	}
	return nil
}

func (x *UpdateUserDetailsRequest) GetBankAccountDetails() *BankAccountDetails {
	if x, ok := x.GetDetails().(*UpdateUserDetailsRequest_BankAccountDetails); ok {
		return x.BankAccountDetails
	}
	return nil
}

func (x *UpdateUserDetailsRequest) GetPersonalDetails() *PersonalDetails {
	if x, ok := x.GetDetails().(*UpdateUserDetailsRequest_PersonalDetails); ok {
		return x.PersonalDetails
	}
	return nil
}

func (x *UpdateUserDetailsRequest) GetVerificationDetails() *VerificationDetails {
	if x, ok := x.GetDetails().(*UpdateUserDetailsRequest_VerificationDetails); ok {
		return x.VerificationDetails
	}
	return nil
}

type isUpdateUserDetailsRequest_Details interface {
	isUpdateUserDetailsRequest_Details()
}

type UpdateUserDetailsRequest_AddressDetails struct {
	AddressDetails *AddressDetails `protobuf:"bytes,4,opt,name=address_details,json=addressDetails,proto3,oneof"`
}

type UpdateUserDetailsRequest_EmploymentDetails struct {
	EmploymentDetails *EmploymentDetails `protobuf:"bytes,5,opt,name=employment_details,json=employmentDetails,proto3,oneof"`
}

type UpdateUserDetailsRequest_BankAccountDetails struct {
	BankAccountDetails *BankAccountDetails `protobuf:"bytes,6,opt,name=bank_account_details,json=bankAccountDetails,proto3,oneof"`
}

type UpdateUserDetailsRequest_PersonalDetails struct {
	PersonalDetails *PersonalDetails `protobuf:"bytes,7,opt,name=personal_details,json=personalDetails,proto3,oneof"`
}

type UpdateUserDetailsRequest_VerificationDetails struct {
	VerificationDetails *VerificationDetails `protobuf:"bytes,8,opt,name=verification_details,json=verificationDetails,proto3,oneof"`
}

func (*UpdateUserDetailsRequest_AddressDetails) isUpdateUserDetailsRequest_Details() {}

func (*UpdateUserDetailsRequest_EmploymentDetails) isUpdateUserDetailsRequest_Details() {}

func (*UpdateUserDetailsRequest_BankAccountDetails) isUpdateUserDetailsRequest_Details() {}

func (*UpdateUserDetailsRequest_PersonalDetails) isUpdateUserDetailsRequest_Details() {}

func (*UpdateUserDetailsRequest_VerificationDetails) isUpdateUserDetailsRequest_Details() {}

type AddressDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AddressDetails *common.PostalAddress `protobuf:"bytes,1,opt,name=address_details,json=addressDetails,proto3" json:"address_details,omitempty"`
	AddressType    common.AddressType    `protobuf:"varint,2,opt,name=address_type,json=addressType,proto3,enum=api.typesv2.common.AddressType" json:"address_type,omitempty"`
}

func (x *AddressDetails) Reset() {
	*x = AddressDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddressDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddressDetails) ProtoMessage() {}

func (x *AddressDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddressDetails.ProtoReflect.Descriptor instead.
func (*AddressDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{24}
}

func (x *AddressDetails) GetAddressDetails() *common.PostalAddress {
	if x != nil {
		return x.AddressDetails
	}
	return nil
}

func (x *AddressDetails) GetAddressType() common.AddressType {
	if x != nil {
		return x.AddressType
	}
	return common.AddressType(0)
}

type EmploymentDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Occupation       common.EmploymentType `protobuf:"varint,1,opt,name=occupation,proto3,enum=api.typesv2.common.EmploymentType" json:"occupation,omitempty"`
	OrganizationName string                `protobuf:"bytes,2,opt,name=organization_name,json=organizationName,proto3" json:"organization_name,omitempty"`
	MonthlyIncome    *money.Money          `protobuf:"bytes,3,opt,name=monthly_income,json=monthlyIncome,proto3" json:"monthly_income,omitempty"`
	WorkEmail        string                `protobuf:"bytes,4,opt,name=work_email,json=workEmail,proto3" json:"work_email,omitempty"`
	OfficeAddress    *common.PostalAddress `protobuf:"bytes,5,opt,name=office_address,json=officeAddress,proto3" json:"office_address,omitempty"`
}

func (x *EmploymentDetails) Reset() {
	*x = EmploymentDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentDetails) ProtoMessage() {}

func (x *EmploymentDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentDetails.ProtoReflect.Descriptor instead.
func (*EmploymentDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{25}
}

func (x *EmploymentDetails) GetOccupation() common.EmploymentType {
	if x != nil {
		return x.Occupation
	}
	return common.EmploymentType(0)
}

func (x *EmploymentDetails) GetOrganizationName() string {
	if x != nil {
		return x.OrganizationName
	}
	return ""
}

func (x *EmploymentDetails) GetMonthlyIncome() *money.Money {
	if x != nil {
		return x.MonthlyIncome
	}
	return nil
}

func (x *EmploymentDetails) GetWorkEmail() string {
	if x != nil {
		return x.WorkEmail
	}
	return ""
}

func (x *EmploymentDetails) GetOfficeAddress() *common.PostalAddress {
	if x != nil {
		return x.OfficeAddress
	}
	return nil
}

type BankAccountDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BankAccountDetails *common.BankAccountDetails `protobuf:"bytes,1,opt,name=bank_account_details,json=bankAccountDetails,proto3" json:"bank_account_details,omitempty"`
}

func (x *BankAccountDetails) Reset() {
	*x = BankAccountDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankAccountDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankAccountDetails) ProtoMessage() {}

func (x *BankAccountDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankAccountDetails.ProtoReflect.Descriptor instead.
func (*BankAccountDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{26}
}

func (x *BankAccountDetails) GetBankAccountDetails() *common.BankAccountDetails {
	if x != nil {
		return x.BankAccountDetails
	}
	return nil
}

type PersonalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Dob     *date.Date    `protobuf:"bytes,1,opt,name=dob,proto3" json:"dob,omitempty"`
	Email   string        `protobuf:"bytes,2,opt,name=email,proto3" json:"email,omitempty"`
	PanName *common.Name  `protobuf:"bytes,3,opt,name=pan_name,json=panName,proto3" json:"pan_name,omitempty"`
	Gender  common.Gender `protobuf:"varint,4,opt,name=gender,proto3,enum=api.typesv2.common.Gender" json:"gender,omitempty"`
}

func (x *PersonalDetails) Reset() {
	*x = PersonalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonalDetails) ProtoMessage() {}

func (x *PersonalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonalDetails.ProtoReflect.Descriptor instead.
func (*PersonalDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{27}
}

func (x *PersonalDetails) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *PersonalDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PersonalDetails) GetPanName() *common.Name {
	if x != nil {
		return x.PanName
	}
	return nil
}

func (x *PersonalDetails) GetGender() common.Gender {
	if x != nil {
		return x.Gender
	}
	return common.Gender(0)
}

type EmploymentVerificationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	EmploymentData string `protobuf:"bytes,1,opt,name=employment_data,json=employmentData,proto3" json:"employment_data,omitempty"`
	IsSalaried     bool   `protobuf:"varint,2,opt,name=is_salaried,json=isSalaried,proto3" json:"is_salaried,omitempty"`
}

func (x *EmploymentVerificationDetails) Reset() {
	*x = EmploymentVerificationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmploymentVerificationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmploymentVerificationDetails) ProtoMessage() {}

func (x *EmploymentVerificationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmploymentVerificationDetails.ProtoReflect.Descriptor instead.
func (*EmploymentVerificationDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{28}
}

func (x *EmploymentVerificationDetails) GetEmploymentData() string {
	if x != nil {
		return x.EmploymentData
	}
	return ""
}

func (x *EmploymentVerificationDetails) GetIsSalaried() bool {
	if x != nil {
		return x.IsSalaried
	}
	return false
}

type VerificationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Details of employment verification checks performed.
	EmploymentVerificationDetails *EmploymentVerificationDetails `protobuf:"bytes,1,opt,name=employment_verification_details,json=employmentVerificationDetails,proto3" json:"employment_verification_details,omitempty"`
}

func (x *VerificationDetails) Reset() {
	*x = VerificationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerificationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerificationDetails) ProtoMessage() {}

func (x *VerificationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerificationDetails.ProtoReflect.Descriptor instead.
func (*VerificationDetails) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{29}
}

func (x *VerificationDetails) GetEmploymentVerificationDetails() *EmploymentVerificationDetails {
	if x != nil {
		return x.EmploymentVerificationDetails
	}
	return nil
}

type UpdateUserDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateUserDetailsResponse) Reset() {
	*x = UpdateUserDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserDetailsResponse) ProtoMessage() {}

func (x *UpdateUserDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserDetailsResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateUserDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type InitDisbursementRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	// external application id
	ApplicationId string `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *InitDisbursementRequest) Reset() {
	*x = InitDisbursementRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitDisbursementRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitDisbursementRequest) ProtoMessage() {}

func (x *InitDisbursementRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitDisbursementRequest.ProtoReflect.Descriptor instead.
func (*InitDisbursementRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{31}
}

func (x *InitDisbursementRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *InitDisbursementRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type InitDisbursementResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *InitDisbursementResponse) Reset() {
	*x = InitDisbursementResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitDisbursementResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitDisbursementResponse) ProtoMessage() {}

func (x *InitDisbursementResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitDisbursementResponse.ProtoReflect.Descriptor instead.
func (*InitDisbursementResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{32}
}

func (x *InitDisbursementResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type InitPennyDropRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *InitPennyDropRequest) Reset() {
	*x = InitPennyDropRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitPennyDropRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitPennyDropRequest) ProtoMessage() {}

func (x *InitPennyDropRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitPennyDropRequest.ProtoReflect.Descriptor instead.
func (*InitPennyDropRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{33}
}

func (x *InitPennyDropRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *InitPennyDropRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type InitPennyDropResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *InitPennyDropResponse) Reset() {
	*x = InitPennyDropResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitPennyDropResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitPennyDropResponse) ProtoMessage() {}

func (x *InitPennyDropResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitPennyDropResponse.ProtoReflect.Descriptor instead.
func (*InitPennyDropResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{34}
}

func (x *InitPennyDropResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type RecordConsentRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader    `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string         `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
	ConsentTypes  []ConsentType  `protobuf:"varint,3,rep,packed,name=consent_types,json=consentTypes,proto3,enum=stockguardian.sgapigateway.application.ConsentType" json:"consent_types,omitempty"`
	Device        *common.Device `protobuf:"bytes,4,opt,name=device,proto3" json:"device,omitempty"`
	IpAddress     string         `protobuf:"bytes,5,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
}

func (x *RecordConsentRequest) Reset() {
	*x = RecordConsentRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordConsentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordConsentRequest) ProtoMessage() {}

func (x *RecordConsentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordConsentRequest.ProtoReflect.Descriptor instead.
func (*RecordConsentRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{35}
}

func (x *RecordConsentRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *RecordConsentRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

func (x *RecordConsentRequest) GetConsentTypes() []ConsentType {
	if x != nil {
		return x.ConsentTypes
	}
	return nil
}

func (x *RecordConsentRequest) GetDevice() *common.Device {
	if x != nil {
		return x.Device
	}
	return nil
}

func (x *RecordConsentRequest) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

type RecordConsentResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *RecordConsentResponse) Reset() {
	*x = RecordConsentResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RecordConsentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordConsentResponse) ProtoMessage() {}

func (x *RecordConsentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordConsentResponse.ProtoReflect.Descriptor instead.
func (*RecordConsentResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{36}
}

func (x *RecordConsentResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type InitEmploymentCheckRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanHeader    *LoanHeader `protobuf:"bytes,1,opt,name=loan_header,json=loanHeader,proto3" json:"loan_header,omitempty"`
	ApplicationId string      `protobuf:"bytes,2,opt,name=application_id,json=applicationId,proto3" json:"application_id,omitempty"`
}

func (x *InitEmploymentCheckRequest) Reset() {
	*x = InitEmploymentCheckRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitEmploymentCheckRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitEmploymentCheckRequest) ProtoMessage() {}

func (x *InitEmploymentCheckRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitEmploymentCheckRequest.ProtoReflect.Descriptor instead.
func (*InitEmploymentCheckRequest) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{37}
}

func (x *InitEmploymentCheckRequest) GetLoanHeader() *LoanHeader {
	if x != nil {
		return x.LoanHeader
	}
	return nil
}

func (x *InitEmploymentCheckRequest) GetApplicationId() string {
	if x != nil {
		return x.ApplicationId
	}
	return ""
}

type InitEmploymentCheckResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *InitEmploymentCheckResponse) Reset() {
	*x = InitEmploymentCheckResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitEmploymentCheckResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitEmploymentCheckResponse) ProtoMessage() {}

func (x *InitEmploymentCheckResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitEmploymentCheckResponse.ProtoReflect.Descriptor instead.
func (*InitEmploymentCheckResponse) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{38}
}

func (x *InitEmploymentCheckResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetApplicationStatusResponse_StageStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StageName   LoanApplicationStageName   `protobuf:"varint,1,opt,name=stage_name,json=stageName,proto3,enum=stockguardian.sgapigateway.application.LoanApplicationStageName" json:"stage_name,omitempty"`
	StageStatus LoanApplicationStageStatus `protobuf:"varint,2,opt,name=stage_status,json=stageStatus,proto3,enum=stockguardian.sgapigateway.application.LoanApplicationStageStatus" json:"stage_status,omitempty"`
}

func (x *GetApplicationStatusResponse_StageStatus) Reset() {
	*x = GetApplicationStatusResponse_StageStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetApplicationStatusResponse_StageStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetApplicationStatusResponse_StageStatus) ProtoMessage() {}

func (x *GetApplicationStatusResponse_StageStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetApplicationStatusResponse_StageStatus.ProtoReflect.Descriptor instead.
func (*GetApplicationStatusResponse_StageStatus) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *GetApplicationStatusResponse_StageStatus) GetStageName() LoanApplicationStageName {
	if x != nil {
		return x.StageName
	}
	return LoanApplicationStageName_LOAN_APPLICATION_STAGE_NAME_UNSPECIFIED
}

func (x *GetApplicationStatusResponse_StageStatus) GetStageStatus() LoanApplicationStageStatus {
	if x != nil {
		return x.StageStatus
	}
	return LoanApplicationStageStatus_LOAN_APPLICATION_STAGE_STATUS_UNSPECIFIED
}

type GetLoanOfferRequest_BreParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// policy params used to generate offer from BRE
	PolicyParams      *bre.PolicyParams `protobuf:"bytes,1,opt,name=policy_params,json=policyParams,proto3" json:"policy_params,omitempty"`
	DesiredLoanAmount *money.Money      `protobuf:"bytes,2,opt,name=desired_loan_amount,json=desiredLoanAmount,proto3" json:"desired_loan_amount,omitempty"`
}

func (x *GetLoanOfferRequest_BreParams) Reset() {
	*x = GetLoanOfferRequest_BreParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanOfferRequest_BreParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanOfferRequest_BreParams) ProtoMessage() {}

func (x *GetLoanOfferRequest_BreParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanOfferRequest_BreParams.ProtoReflect.Descriptor instead.
func (*GetLoanOfferRequest_BreParams) Descriptor() ([]byte, []int) {
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *GetLoanOfferRequest_BreParams) GetPolicyParams() *bre.PolicyParams {
	if x != nil {
		return x.PolicyParams
	}
	return nil
}

func (x *GetLoanOfferRequest_BreParams) GetDesiredLoanAmount() *money.Money {
	if x != nil {
		return x.DesiredLoanAmount
	}
	return nil
}

var File_api_stockguardian_sgapigateway_application_service_proto protoreflect.FileDescriptor

var file_api_stockguardian_sgapigateway_application_service_proto_rawDesc = []byte{
	0x0a, 0x38, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x26, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x61, 0x70,
	0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x69, 0x6e, 0x68, 0x6f,
	0x75, 0x73, 0x65, 0x2f, 0x62, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e,
	0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x99, 0x01, 0x0a, 0x19, 0x49,
	0x6e, 0x69, 0x74, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x74, 0x72,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67,
	0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x27, 0x0a,
	0x0f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x84, 0x02, 0x0a, 0x1a, 0x49, 0x6e, 0x69, 0x74, 0x44,
	0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x1c, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x19, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x1c, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x19, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x1c,
	0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x19, 0x69, 0x6e, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x4c, 0x6f, 0x61, 0x6e, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x29, 0x0a,
	0x0a, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xb0, 0x01, 0x0a, 0x17, 0x53, 0x74, 0x61,
	0x72, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c,
	0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0xcc, 0x01, 0x0a, 0x18,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x22, 0x64, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06,
	0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44,
	0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x06, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x30, 0x0a, 0x2c, 0x46, 0x41, 0x49, 0x4c,
	0x45, 0x44, 0x5f, 0x50, 0x52, 0x45, 0x43, 0x4f, 0x4e, 0x44, 0x49, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x5f, 0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e,
	0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x64, 0x22, 0x96, 0x01, 0x0a, 0x18, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61,
	0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x22, 0x40, 0x0a, 0x19, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x99, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0x92, 0x03, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x75, 0x0a, 0x0d, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50,
	0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73,
	0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xd5,
	0x01, 0x0a, 0x0b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5f,
	0x0a, 0x0a, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x40, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e,
	0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x65, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x67, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xce, 0x03, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53,
	0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x2d, 0x0a, 0x13, 0x69, 0x73,
	0x5f, 0x6f, 0x70, 0x65, 0x6e, 0x5f, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x75, 0x73, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x69, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x4d,
	0x61, 0x72, 0x6b, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x12, 0x64, 0x0a, 0x0a, 0x62, 0x72, 0x65,
	0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x45, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67,
	0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x72, 0x65, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x52, 0x09, 0x62, 0x72, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x1a,
	0xa5, 0x01, 0x0a, 0x09, 0x42, 0x72, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x54, 0x0a,
	0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x69, 0x6e, 0x68,
	0x6f, 0x75, 0x73, 0x65, 0x2e, 0x62, 0x72, 0x65, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x42, 0x0a, 0x13, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x64, 0x65, 0x73, 0x69, 0x72, 0x65, 0x64, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xaa, 0x02, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4c,
	0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5a, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0x90, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x47, 0x45, 0x4e, 0x45,
	0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45,
	0x53, 0x53, 0x10, 0x66, 0x12, 0x12, 0x0a, 0x0e, 0x4f, 0x46, 0x46, 0x45, 0x52, 0x5f, 0x52, 0x45,
	0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x67, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x46, 0x46, 0x45,
	0x52, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x68, 0x12, 0x27, 0x0a, 0x23, 0x4f,
	0x46, 0x46, 0x45, 0x52, 0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x41,
	0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x69, 0x22, 0xb0, 0x03, 0x0a, 0x18, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x61,
	0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x0e, 0x6d, 0x61, 0x78, 0x5f, 0x65,
	0x6d, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0c, 0x6d, 0x61, 0x78, 0x45, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2f, 0x0a, 0x13, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x12,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61,
	0x67, 0x65, 0x12, 0x3a, 0x0a, 0x19, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67,
	0x5f, 0x66, 0x65, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e,
	0x67, 0x46, 0x65, 0x65, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x25,
	0x0a, 0x0e, 0x67, 0x73, 0x74, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x67, 0x73, 0x74, 0x50, 0x65, 0x72, 0x63, 0x65,
	0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x14, 0x6d, 0x69, 0x6e, 0x5f, 0x74, 0x65, 0x6e,
	0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x69, 0x6e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49, 0x6e,
	0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x2f, 0x0a, 0x14, 0x6d, 0x61, 0x78, 0x5f, 0x74, 0x65,
	0x6e, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x6d, 0x61, 0x78, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49,
	0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x22, 0x90, 0x01, 0x0a, 0x12, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x4b, 0x79, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53,
	0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0xea, 0x01, 0x0a, 0x13, 0x49,
	0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x4b, 0x79, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x2c,
	0x0a, 0x12, 0x6b, 0x79, 0x63, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6b, 0x79, 0x63, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x39, 0x0a, 0x06,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x19, 0x0a, 0x15,
	0x4b, 0x59, 0x43, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x50,
	0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x64, 0x22, 0xf4, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x55, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x99,
	0x01, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x74,
	0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x22, 0x47, 0x0a, 0x20, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x91, 0x01, 0x0a, 0x13, 0x49, 0x6e, 0x69, 0x74, 0x44, 0x72, 0x61, 0x77,
	0x44, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c,
	0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x89, 0x01, 0x0a, 0x14, 0x49, 0x6e, 0x69, 0x74,
	0x44, 0x72, 0x61, 0x77, 0x44, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4c, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x0d, 0x0a, 0x09, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x1c,
	0x0a, 0x18, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x64, 0x12, 0x15, 0x0a, 0x11,
	0x50, 0x45, 0x52, 0x4d, 0x41, 0x4e, 0x41, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x10, 0x65, 0x22, 0xb5, 0x01, 0x0a, 0x12, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c, 0x6f,
	0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x72,
	0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x22, 0x53, 0x0a, 0x13, 0x49,
	0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x63, 0x68, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x63, 0x68, 0x49, 0x64,
	0x22, 0x8e, 0x01, 0x0a, 0x10, 0x49, 0x6e, 0x69, 0x74, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0x51, 0x0a, 0x11, 0x49, 0x6e, 0x69, 0x74, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6f,
	0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72,
	0x63, 0x68, 0x49, 0x64, 0x22, 0xa8, 0x06, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x6e, 0x0a,
	0x11, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61,
	0x73, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x42, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0f, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x61, 0x0a,
	0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00,
	0x52, 0x0e, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x6a, 0x0a, 0x12, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61,
	0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x11, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6e, 0x0a, 0x14,
	0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x12, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x64, 0x0a, 0x10,
	0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48,
	0x00, 0x52, 0x0f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x70, 0x0a, 0x14, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x3b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52,
	0x13, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22,
	0xa0, 0x01, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x4a, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x64, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0e,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x42,
	0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79,
	0x70, 0x65, 0x22, 0xa8, 0x02, 0x0a, 0x11, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x42, 0x0a, 0x0a, 0x6f, 0x63, 0x63, 0x75,
	0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x6f, 0x63, 0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x11,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6d, 0x6f, 0x6e,
	0x74, 0x68, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x49, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x77, 0x6f, 0x72, 0x6b, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x48, 0x0a, 0x0e, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0d,
	0x6f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x6e, 0x0a,
	0x12, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x58, 0x0a, 0x14, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x62, 0x61, 0x6e, 0x6b, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xb5, 0x01,
	0x0a, 0x0f, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x23, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x33, 0x0a, 0x08,
	0x70, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x07, 0x70, 0x61, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x32, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x22, 0x69, 0x0a, 0x1d, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x69, 0x65, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x69, 0x65, 0x64,
	0x22, 0xa5, 0x01, 0x0a, 0x13, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x8d, 0x01, 0x0a, 0x1f, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x45, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x1d, 0x65, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x56, 0x65, 0x72, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x40, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x95, 0x01, 0x0a, 0x17, 0x49,
	0x6e, 0x69, 0x74, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70,
	0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x22, 0x3f, 0x0a, 0x18, 0x49, 0x6e, 0x69, 0x74, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72,
	0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x92, 0x01, 0x0a, 0x14, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x6e, 0x6e,
	0x79, 0x44, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x3c, 0x0a, 0x15, 0x49, 0x6e, 0x69, 0x74,
	0x50, 0x65, 0x6e, 0x6e, 0x79, 0x44, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xbf, 0x02, 0x0a, 0x14, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x53, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f,
	0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x0d, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0e, 0x32, 0x33, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x32, 0x0a, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x06, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x70, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x69,
	0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x3c, 0x0a, 0x15, 0x52, 0x65, 0x63, 0x6f,
	0x72, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x98, 0x01, 0x0a, 0x1a, 0x49, 0x6e, 0x69, 0x74, 0x45,
	0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x53, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x6c, 0x6f, 0x61, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x22, 0x9b, 0x01, 0x0a, 0x1b, 0x49, 0x6e, 0x69, 0x74, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x57, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x0d, 0x0a, 0x09, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x12, 0x0a, 0x0e, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x64, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x66, 0x32,
	0xd1, 0x11, 0x0a, 0x0b, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x95, 0x01, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x72, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61,
	0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x98, 0x01, 0x0a, 0x11, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x2e,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67,
	0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x41, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x41,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x2e, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70,
	0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x44, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e,
	0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xad, 0x01, 0x0a, 0x18, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x47, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x48, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61,
	0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x98, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x40, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61,
	0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41,
	0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73,
	0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x8c, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x12, 0x3c, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x3d, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x89, 0x01, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x12, 0x3b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c,
	0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73,
	0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a,
	0x0b, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x4b, 0x79, 0x63, 0x12, 0x3a, 0x2e, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61,
	0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x4b, 0x79,
	0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b,
	0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x4b, 0x79, 0x63, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x0c, 0x49, 0x6e, 0x69, 0x74, 0x44, 0x72,
	0x61, 0x77, 0x44, 0x6f, 0x77, 0x6e, 0x12, 0x3b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x49, 0x6e, 0x69, 0x74, 0x44, 0x72, 0x61, 0x77, 0x44, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69,
	0x74, 0x44, 0x72, 0x61, 0x77, 0x44, 0x6f, 0x77, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x80, 0x01, 0x0a, 0x09, 0x49, 0x6e, 0x69, 0x74, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x12,
	0x38, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x45, 0x73, 0x69,
	0x67, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x45, 0x73, 0x69, 0x67, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x0d, 0x49, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x6e,
	0x6e, 0x79, 0x44, 0x72, 0x6f, 0x70, 0x12, 0x3c, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x49, 0x6e, 0x69, 0x74, 0x50, 0x65, 0x6e, 0x6e, 0x79, 0x44, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72,
	0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e,
	0x69, 0x74, 0x50, 0x65, 0x6e, 0x6e, 0x79, 0x44, 0x72, 0x6f, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x0b, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x3a, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69,
	0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3b, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e,
	0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x95, 0x01, 0x0a,
	0x10, 0x49, 0x6e, 0x69, 0x74, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x3f, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61,
	0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61,
	0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x44,
	0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x40, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69,
	0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74,
	0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9b, 0x01, 0x0a, 0x12, 0x49, 0x6e, 0x69, 0x74, 0x44, 0x69, 0x73,
	0x62, 0x75, 0x73, 0x72, 0x61, 0x6c, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x41, 0x2e, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70,
	0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73,
	0x61, 0x6c, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42,
	0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73,
	0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x44, 0x69, 0x73, 0x62,
	0x75, 0x72, 0x73, 0x61, 0x6c, 0x52, 0x65, 0x74, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x9e, 0x01, 0x0a, 0x13, 0x49, 0x6e, 0x69, 0x74, 0x45, 0x6d, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x42, 0x2e, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43,
	0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2e, 0x73,
	0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x61, 0x70, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x89, 0x01, 0x0a, 0x44, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x72, 0x69, 0x6e, 0x67, 0x6f, 0x74,
	0x74, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75, 0x61, 0x72, 0x64,
	0x69, 0x61, 0x6e, 0x2e, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x41, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x67, 0x75,
	0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x2f, 0x73, 0x67, 0x61, 0x70, 0x69, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_stockguardian_sgapigateway_application_service_proto_rawDescOnce sync.Once
	file_api_stockguardian_sgapigateway_application_service_proto_rawDescData = file_api_stockguardian_sgapigateway_application_service_proto_rawDesc
)

func file_api_stockguardian_sgapigateway_application_service_proto_rawDescGZIP() []byte {
	file_api_stockguardian_sgapigateway_application_service_proto_rawDescOnce.Do(func() {
		file_api_stockguardian_sgapigateway_application_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_stockguardian_sgapigateway_application_service_proto_rawDescData)
	})
	return file_api_stockguardian_sgapigateway_application_service_proto_rawDescData
}

var file_api_stockguardian_sgapigateway_application_service_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_stockguardian_sgapigateway_application_service_proto_msgTypes = make([]protoimpl.MessageInfo, 41)
var file_api_stockguardian_sgapigateway_application_service_proto_goTypes = []interface{}{
	(StartApplicationResponse_Status)(0),             // 0: stockguardian.sgapigateway.application.StartApplicationResponse.Status
	(GetLoanOfferResponse_Status)(0),                 // 1: stockguardian.sgapigateway.application.GetLoanOfferResponse.Status
	(InitiateKycResponse_Status)(0),                  // 2: stockguardian.sgapigateway.application.InitiateKycResponse.Status
	(InitDrawDownResponse_Status)(0),                 // 3: stockguardian.sgapigateway.application.InitDrawDownResponse.Status
	(InitEmploymentCheckResponse_Status)(0),          // 4: stockguardian.sgapigateway.application.InitEmploymentCheckResponse.Status
	(*InitDisbursalRetryRequest)(nil),                // 5: stockguardian.sgapigateway.application.InitDisbursalRetryRequest
	(*InitDisbursalRetryResponse)(nil),               // 6: stockguardian.sgapigateway.application.InitDisbursalRetryResponse
	(*LoanHeader)(nil),                               // 7: stockguardian.sgapigateway.application.LoanHeader
	(*StartApplicationRequest)(nil),                  // 8: stockguardian.sgapigateway.application.StartApplicationRequest
	(*StartApplicationResponse)(nil),                 // 9: stockguardian.sgapigateway.application.StartApplicationResponse
	(*CancelApplicationRequest)(nil),                 // 10: stockguardian.sgapigateway.application.CancelApplicationRequest
	(*CancelApplicationResponse)(nil),                // 11: stockguardian.sgapigateway.application.CancelApplicationResponse
	(*GetApplicationStatusRequest)(nil),              // 12: stockguardian.sgapigateway.application.GetApplicationStatusRequest
	(*GetApplicationStatusResponse)(nil),             // 13: stockguardian.sgapigateway.application.GetApplicationStatusResponse
	(*GetLoanOfferRequest)(nil),                      // 14: stockguardian.sgapigateway.application.GetLoanOfferRequest
	(*GetLoanOfferResponse)(nil),                     // 15: stockguardian.sgapigateway.application.GetLoanOfferResponse
	(*LoanOfferDetailsResponse)(nil),                 // 16: stockguardian.sgapigateway.application.LoanOfferDetailsResponse
	(*InitiateKycRequest)(nil),                       // 17: stockguardian.sgapigateway.application.InitiateKycRequest
	(*InitiateKycResponse)(nil),                      // 18: stockguardian.sgapigateway.application.InitiateKycResponse
	(*UpdateApplicationDetailsRequest)(nil),          // 19: stockguardian.sgapigateway.application.UpdateApplicationDetailsRequest
	(*UserSelectedDetails)(nil),                      // 20: stockguardian.sgapigateway.application.UserSelectedDetails
	(*UpdateApplicationDetailsResponse)(nil),         // 21: stockguardian.sgapigateway.application.UpdateApplicationDetailsResponse
	(*InitDrawDownRequest)(nil),                      // 22: stockguardian.sgapigateway.application.InitDrawDownRequest
	(*InitDrawDownResponse)(nil),                     // 23: stockguardian.sgapigateway.application.InitDrawDownResponse
	(*InitMandateRequest)(nil),                       // 24: stockguardian.sgapigateway.application.InitMandateRequest
	(*InitMandateResponse)(nil),                      // 25: stockguardian.sgapigateway.application.InitMandateResponse
	(*InitEsignRequest)(nil),                         // 26: stockguardian.sgapigateway.application.InitEsignRequest
	(*InitEsignResponse)(nil),                        // 27: stockguardian.sgapigateway.application.InitEsignResponse
	(*UpdateUserDetailsRequest)(nil),                 // 28: stockguardian.sgapigateway.application.UpdateUserDetailsRequest
	(*AddressDetails)(nil),                           // 29: stockguardian.sgapigateway.application.AddressDetails
	(*EmploymentDetails)(nil),                        // 30: stockguardian.sgapigateway.application.EmploymentDetails
	(*BankAccountDetails)(nil),                       // 31: stockguardian.sgapigateway.application.BankAccountDetails
	(*PersonalDetails)(nil),                          // 32: stockguardian.sgapigateway.application.PersonalDetails
	(*EmploymentVerificationDetails)(nil),            // 33: stockguardian.sgapigateway.application.EmploymentVerificationDetails
	(*VerificationDetails)(nil),                      // 34: stockguardian.sgapigateway.application.VerificationDetails
	(*UpdateUserDetailsResponse)(nil),                // 35: stockguardian.sgapigateway.application.UpdateUserDetailsResponse
	(*InitDisbursementRequest)(nil),                  // 36: stockguardian.sgapigateway.application.InitDisbursementRequest
	(*InitDisbursementResponse)(nil),                 // 37: stockguardian.sgapigateway.application.InitDisbursementResponse
	(*InitPennyDropRequest)(nil),                     // 38: stockguardian.sgapigateway.application.InitPennyDropRequest
	(*InitPennyDropResponse)(nil),                    // 39: stockguardian.sgapigateway.application.InitPennyDropResponse
	(*RecordConsentRequest)(nil),                     // 40: stockguardian.sgapigateway.application.RecordConsentRequest
	(*RecordConsentResponse)(nil),                    // 41: stockguardian.sgapigateway.application.RecordConsentResponse
	(*InitEmploymentCheckRequest)(nil),               // 42: stockguardian.sgapigateway.application.InitEmploymentCheckRequest
	(*InitEmploymentCheckResponse)(nil),              // 43: stockguardian.sgapigateway.application.InitEmploymentCheckResponse
	(*GetApplicationStatusResponse_StageStatus)(nil), // 44: stockguardian.sgapigateway.application.GetApplicationStatusResponse.StageStatus
	(*GetLoanOfferRequest_BreParams)(nil),            // 45: stockguardian.sgapigateway.application.GetLoanOfferRequest.BreParams
	(*rpc.Status)(nil),                               // 46: rpc.Status
	(*money.Money)(nil),                              // 47: google.type.Money
	(UpdateUserDetailsFieldMask)(0),                  // 48: stockguardian.sgapigateway.application.UpdateUserDetailsFieldMask
	(*common.PostalAddress)(nil),                     // 49: api.typesv2.common.PostalAddress
	(common.AddressType)(0),                          // 50: api.typesv2.common.AddressType
	(common.EmploymentType)(0),                       // 51: api.typesv2.common.EmploymentType
	(*common.BankAccountDetails)(nil),                // 52: api.typesv2.common.BankAccountDetails
	(*date.Date)(nil),                                // 53: google.type.Date
	(*common.Name)(nil),                              // 54: api.typesv2.common.Name
	(common.Gender)(0),                               // 55: api.typesv2.common.Gender
	(ConsentType)(0),                                 // 56: stockguardian.sgapigateway.application.ConsentType
	(*common.Device)(nil),                            // 57: api.typesv2.common.Device
	(LoanApplicationStageName)(0),                    // 58: stockguardian.sgapigateway.application.LoanApplicationStageName
	(LoanApplicationStageStatus)(0),                  // 59: stockguardian.sgapigateway.application.LoanApplicationStageStatus
	(*bre.PolicyParams)(nil),                         // 60: stockguardian.vendors.inhouse.bre.PolicyParams
}
var file_api_stockguardian_sgapigateway_application_service_proto_depIdxs = []int32{
	7,  // 0: stockguardian.sgapigateway.application.InitDisbursalRetryRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 1: stockguardian.sgapigateway.application.InitDisbursalRetryResponse.status:type_name -> rpc.Status
	7,  // 2: stockguardian.sgapigateway.application.StartApplicationRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 3: stockguardian.sgapigateway.application.StartApplicationResponse.status:type_name -> rpc.Status
	7,  // 4: stockguardian.sgapigateway.application.CancelApplicationRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 5: stockguardian.sgapigateway.application.CancelApplicationResponse.status:type_name -> rpc.Status
	7,  // 6: stockguardian.sgapigateway.application.GetApplicationStatusRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 7: stockguardian.sgapigateway.application.GetApplicationStatusResponse.status:type_name -> rpc.Status
	44, // 8: stockguardian.sgapigateway.application.GetApplicationStatusResponse.stage_details:type_name -> stockguardian.sgapigateway.application.GetApplicationStatusResponse.StageStatus
	7,  // 9: stockguardian.sgapigateway.application.GetLoanOfferRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	45, // 10: stockguardian.sgapigateway.application.GetLoanOfferRequest.bre_params:type_name -> stockguardian.sgapigateway.application.GetLoanOfferRequest.BreParams
	46, // 11: stockguardian.sgapigateway.application.GetLoanOfferResponse.status:type_name -> rpc.Status
	16, // 12: stockguardian.sgapigateway.application.GetLoanOfferResponse.details:type_name -> stockguardian.sgapigateway.application.LoanOfferDetailsResponse
	47, // 13: stockguardian.sgapigateway.application.LoanOfferDetailsResponse.min_amount:type_name -> google.type.Money
	47, // 14: stockguardian.sgapigateway.application.LoanOfferDetailsResponse.max_amount:type_name -> google.type.Money
	47, // 15: stockguardian.sgapigateway.application.LoanOfferDetailsResponse.max_emi_amount:type_name -> google.type.Money
	7,  // 16: stockguardian.sgapigateway.application.InitiateKycRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 17: stockguardian.sgapigateway.application.InitiateKycResponse.status:type_name -> rpc.Status
	7,  // 18: stockguardian.sgapigateway.application.UpdateApplicationDetailsRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	20, // 19: stockguardian.sgapigateway.application.UpdateApplicationDetailsRequest.details:type_name -> stockguardian.sgapigateway.application.UserSelectedDetails
	47, // 20: stockguardian.sgapigateway.application.UserSelectedDetails.loan_amount:type_name -> google.type.Money
	46, // 21: stockguardian.sgapigateway.application.UpdateApplicationDetailsResponse.status:type_name -> rpc.Status
	7,  // 22: stockguardian.sgapigateway.application.InitDrawDownRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 23: stockguardian.sgapigateway.application.InitDrawDownResponse.status:type_name -> rpc.Status
	7,  // 24: stockguardian.sgapigateway.application.InitMandateRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 25: stockguardian.sgapigateway.application.InitMandateResponse.status:type_name -> rpc.Status
	7,  // 26: stockguardian.sgapigateway.application.InitEsignRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 27: stockguardian.sgapigateway.application.InitEsignResponse.status:type_name -> rpc.Status
	7,  // 28: stockguardian.sgapigateway.application.UpdateUserDetailsRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	48, // 29: stockguardian.sgapigateway.application.UpdateUserDetailsRequest.update_field_mask:type_name -> stockguardian.sgapigateway.application.UpdateUserDetailsFieldMask
	29, // 30: stockguardian.sgapigateway.application.UpdateUserDetailsRequest.address_details:type_name -> stockguardian.sgapigateway.application.AddressDetails
	30, // 31: stockguardian.sgapigateway.application.UpdateUserDetailsRequest.employment_details:type_name -> stockguardian.sgapigateway.application.EmploymentDetails
	31, // 32: stockguardian.sgapigateway.application.UpdateUserDetailsRequest.bank_account_details:type_name -> stockguardian.sgapigateway.application.BankAccountDetails
	32, // 33: stockguardian.sgapigateway.application.UpdateUserDetailsRequest.personal_details:type_name -> stockguardian.sgapigateway.application.PersonalDetails
	34, // 34: stockguardian.sgapigateway.application.UpdateUserDetailsRequest.verification_details:type_name -> stockguardian.sgapigateway.application.VerificationDetails
	49, // 35: stockguardian.sgapigateway.application.AddressDetails.address_details:type_name -> api.typesv2.common.PostalAddress
	50, // 36: stockguardian.sgapigateway.application.AddressDetails.address_type:type_name -> api.typesv2.common.AddressType
	51, // 37: stockguardian.sgapigateway.application.EmploymentDetails.occupation:type_name -> api.typesv2.common.EmploymentType
	47, // 38: stockguardian.sgapigateway.application.EmploymentDetails.monthly_income:type_name -> google.type.Money
	49, // 39: stockguardian.sgapigateway.application.EmploymentDetails.office_address:type_name -> api.typesv2.common.PostalAddress
	52, // 40: stockguardian.sgapigateway.application.BankAccountDetails.bank_account_details:type_name -> api.typesv2.common.BankAccountDetails
	53, // 41: stockguardian.sgapigateway.application.PersonalDetails.dob:type_name -> google.type.Date
	54, // 42: stockguardian.sgapigateway.application.PersonalDetails.pan_name:type_name -> api.typesv2.common.Name
	55, // 43: stockguardian.sgapigateway.application.PersonalDetails.gender:type_name -> api.typesv2.common.Gender
	33, // 44: stockguardian.sgapigateway.application.VerificationDetails.employment_verification_details:type_name -> stockguardian.sgapigateway.application.EmploymentVerificationDetails
	46, // 45: stockguardian.sgapigateway.application.UpdateUserDetailsResponse.status:type_name -> rpc.Status
	7,  // 46: stockguardian.sgapigateway.application.InitDisbursementRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 47: stockguardian.sgapigateway.application.InitDisbursementResponse.status:type_name -> rpc.Status
	7,  // 48: stockguardian.sgapigateway.application.InitPennyDropRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 49: stockguardian.sgapigateway.application.InitPennyDropResponse.status:type_name -> rpc.Status
	7,  // 50: stockguardian.sgapigateway.application.RecordConsentRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	56, // 51: stockguardian.sgapigateway.application.RecordConsentRequest.consent_types:type_name -> stockguardian.sgapigateway.application.ConsentType
	57, // 52: stockguardian.sgapigateway.application.RecordConsentRequest.device:type_name -> api.typesv2.common.Device
	46, // 53: stockguardian.sgapigateway.application.RecordConsentResponse.status:type_name -> rpc.Status
	7,  // 54: stockguardian.sgapigateway.application.InitEmploymentCheckRequest.loan_header:type_name -> stockguardian.sgapigateway.application.LoanHeader
	46, // 55: stockguardian.sgapigateway.application.InitEmploymentCheckResponse.status:type_name -> rpc.Status
	58, // 56: stockguardian.sgapigateway.application.GetApplicationStatusResponse.StageStatus.stage_name:type_name -> stockguardian.sgapigateway.application.LoanApplicationStageName
	59, // 57: stockguardian.sgapigateway.application.GetApplicationStatusResponse.StageStatus.stage_status:type_name -> stockguardian.sgapigateway.application.LoanApplicationStageStatus
	60, // 58: stockguardian.sgapigateway.application.GetLoanOfferRequest.BreParams.policy_params:type_name -> stockguardian.vendors.inhouse.bre.PolicyParams
	47, // 59: stockguardian.sgapigateway.application.GetLoanOfferRequest.BreParams.desired_loan_amount:type_name -> google.type.Money
	8,  // 60: stockguardian.sgapigateway.application.Application.StartApplication:input_type -> stockguardian.sgapigateway.application.StartApplicationRequest
	10, // 61: stockguardian.sgapigateway.application.Application.CancelApplication:input_type -> stockguardian.sgapigateway.application.CancelApplicationRequest
	12, // 62: stockguardian.sgapigateway.application.Application.GetApplicationStatus:input_type -> stockguardian.sgapigateway.application.GetApplicationStatusRequest
	19, // 63: stockguardian.sgapigateway.application.Application.UpdateApplicationDetails:input_type -> stockguardian.sgapigateway.application.UpdateApplicationDetailsRequest
	28, // 64: stockguardian.sgapigateway.application.Application.UpdateUserDetails:input_type -> stockguardian.sgapigateway.application.UpdateUserDetailsRequest
	40, // 65: stockguardian.sgapigateway.application.Application.RecordConsent:input_type -> stockguardian.sgapigateway.application.RecordConsentRequest
	14, // 66: stockguardian.sgapigateway.application.Application.GetLoanOffer:input_type -> stockguardian.sgapigateway.application.GetLoanOfferRequest
	17, // 67: stockguardian.sgapigateway.application.Application.InitiateKyc:input_type -> stockguardian.sgapigateway.application.InitiateKycRequest
	22, // 68: stockguardian.sgapigateway.application.Application.InitDrawDown:input_type -> stockguardian.sgapigateway.application.InitDrawDownRequest
	26, // 69: stockguardian.sgapigateway.application.Application.InitEsign:input_type -> stockguardian.sgapigateway.application.InitEsignRequest
	38, // 70: stockguardian.sgapigateway.application.Application.InitPennyDrop:input_type -> stockguardian.sgapigateway.application.InitPennyDropRequest
	24, // 71: stockguardian.sgapigateway.application.Application.InitMandate:input_type -> stockguardian.sgapigateway.application.InitMandateRequest
	36, // 72: stockguardian.sgapigateway.application.Application.InitDisbursement:input_type -> stockguardian.sgapigateway.application.InitDisbursementRequest
	5,  // 73: stockguardian.sgapigateway.application.Application.InitDisbusralRetry:input_type -> stockguardian.sgapigateway.application.InitDisbursalRetryRequest
	42, // 74: stockguardian.sgapigateway.application.Application.InitEmploymentCheck:input_type -> stockguardian.sgapigateway.application.InitEmploymentCheckRequest
	9,  // 75: stockguardian.sgapigateway.application.Application.StartApplication:output_type -> stockguardian.sgapigateway.application.StartApplicationResponse
	11, // 76: stockguardian.sgapigateway.application.Application.CancelApplication:output_type -> stockguardian.sgapigateway.application.CancelApplicationResponse
	13, // 77: stockguardian.sgapigateway.application.Application.GetApplicationStatus:output_type -> stockguardian.sgapigateway.application.GetApplicationStatusResponse
	21, // 78: stockguardian.sgapigateway.application.Application.UpdateApplicationDetails:output_type -> stockguardian.sgapigateway.application.UpdateApplicationDetailsResponse
	35, // 79: stockguardian.sgapigateway.application.Application.UpdateUserDetails:output_type -> stockguardian.sgapigateway.application.UpdateUserDetailsResponse
	41, // 80: stockguardian.sgapigateway.application.Application.RecordConsent:output_type -> stockguardian.sgapigateway.application.RecordConsentResponse
	15, // 81: stockguardian.sgapigateway.application.Application.GetLoanOffer:output_type -> stockguardian.sgapigateway.application.GetLoanOfferResponse
	18, // 82: stockguardian.sgapigateway.application.Application.InitiateKyc:output_type -> stockguardian.sgapigateway.application.InitiateKycResponse
	23, // 83: stockguardian.sgapigateway.application.Application.InitDrawDown:output_type -> stockguardian.sgapigateway.application.InitDrawDownResponse
	27, // 84: stockguardian.sgapigateway.application.Application.InitEsign:output_type -> stockguardian.sgapigateway.application.InitEsignResponse
	39, // 85: stockguardian.sgapigateway.application.Application.InitPennyDrop:output_type -> stockguardian.sgapigateway.application.InitPennyDropResponse
	25, // 86: stockguardian.sgapigateway.application.Application.InitMandate:output_type -> stockguardian.sgapigateway.application.InitMandateResponse
	37, // 87: stockguardian.sgapigateway.application.Application.InitDisbursement:output_type -> stockguardian.sgapigateway.application.InitDisbursementResponse
	6,  // 88: stockguardian.sgapigateway.application.Application.InitDisbusralRetry:output_type -> stockguardian.sgapigateway.application.InitDisbursalRetryResponse
	43, // 89: stockguardian.sgapigateway.application.Application.InitEmploymentCheck:output_type -> stockguardian.sgapigateway.application.InitEmploymentCheckResponse
	75, // [75:90] is the sub-list for method output_type
	60, // [60:75] is the sub-list for method input_type
	60, // [60:60] is the sub-list for extension type_name
	60, // [60:60] is the sub-list for extension extendee
	0,  // [0:60] is the sub-list for field type_name
}

func init() { file_api_stockguardian_sgapigateway_application_service_proto_init() }
func file_api_stockguardian_sgapigateway_application_service_proto_init() {
	if File_api_stockguardian_sgapigateway_application_service_proto != nil {
		return
	}
	file_api_stockguardian_sgapigateway_application_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitDisbursalRetryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitDisbursalRetryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartApplicationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartApplicationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelApplicationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelApplicationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicationStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicationStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanOfferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanOfferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanOfferDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateKycRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateKycResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateApplicationDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSelectedDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateApplicationDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitDrawDownRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitDrawDownResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitEsignRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitEsignResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddressDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankAccountDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmploymentVerificationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerificationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitDisbursementRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitDisbursementResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitPennyDropRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitPennyDropResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordConsentRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RecordConsentResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitEmploymentCheckRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitEmploymentCheckResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetApplicationStatusResponse_StageStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanOfferRequest_BreParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_stockguardian_sgapigateway_application_service_proto_msgTypes[23].OneofWrappers = []interface{}{
		(*UpdateUserDetailsRequest_AddressDetails)(nil),
		(*UpdateUserDetailsRequest_EmploymentDetails)(nil),
		(*UpdateUserDetailsRequest_BankAccountDetails)(nil),
		(*UpdateUserDetailsRequest_PersonalDetails)(nil),
		(*UpdateUserDetailsRequest_VerificationDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_stockguardian_sgapigateway_application_service_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   41,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_stockguardian_sgapigateway_application_service_proto_goTypes,
		DependencyIndexes: file_api_stockguardian_sgapigateway_application_service_proto_depIdxs,
		EnumInfos:         file_api_stockguardian_sgapigateway_application_service_proto_enumTypes,
		MessageInfos:      file_api_stockguardian_sgapigateway_application_service_proto_msgTypes,
	}.Build()
	File_api_stockguardian_sgapigateway_application_service_proto = out.File
	file_api_stockguardian_sgapigateway_application_service_proto_rawDesc = nil
	file_api_stockguardian_sgapigateway_application_service_proto_goTypes = nil
	file_api_stockguardian_sgapigateway_application_service_proto_depIdxs = nil
}
