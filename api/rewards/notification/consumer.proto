//go:generate gen_queue_pb

syntax = "proto3";

package rewards.notification;

import "api/queue/consumer_headers.proto";
import "api/rewards/notification/notification.proto";
import "api/rewards/rewardoffers/reward_offer.proto";

option go_package = "github.com/epifi/gamma/api/rewards/notification";
option java_package = "com.github.epifi.gamma.api.rewards.notification";

service Consumer {
  // ProcessRewardNotificationEvent consumer is useful for triggering comms for rewards depending on the trigger points
  // e.g. triggering a reward unlock notification when a reward gets unlocked for the user
  rpc ProcessRewardNotificationEvent (RewardNotificationDataEvent) returns (ConsumerResponse);
}

message ConsumerResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message RewardNotificationDataEvent {
  // actor to whom the notification is supposed to be sent
  string actor_id = 1;
  // medium via which the notification is to be sent
  // deprecated in favour of rewardNotificationType
  // do not use this field as no handling is done based on this field
  notification.NotificationMedium medium = 2 [deprecated = true];
  // trigger refers to the action for which the notification is supposed to be sent, for e.g. rewards being unlocked
  notification.NotificationTrigger trigger = 3;
  // data required for validating triggers
  notification.TriggerMetadata trigger_metadata = 4;
  // type of the notification to be sent eg SYSTEM_TRAY, IN_APP, IN_APP_CRITICAL
  rewardoffers.RewardNotificationType rewardNotificationType = 5;
  // contains the content of the notification, like title, body
  Content content = 6;
  // notification type specific metadata
  notification.NotificationTypeMetadata notification_type_metadata = 7;

  queue.ConsumerRequestHeader request_header = 15;
}
