//go:generate gen_queue_pb
syntax = "proto3";

package rewards.pinot;

import "api/queue/consumer_headers.proto";
import "api/rewards/events/reward_status_update_event.proto";
import "api/rewards/events/projection_event.proto";

option go_package = "github.com/epifi/gamma/api/rewards/pinot";
option java_package = "com.github.epifi.gamma.api.rewards.pinot";


service Consumer {
  // rewards pinot consumer which consumes the terminal state reward (PROCESSED, EXPIRED)
  // which are pushed to RewardStatusUpdateEvent sns topic,stitches other information
  // and publishes them to the kafka stream for uploading to realtime table for terminal rewards (rewards_terminal)
  // Also publishes to kafka stream of realtime table for non_terminal rewards with delete_entry set to true,
  // so that any reward which may be present in non-terminal realtime table is soft-deleted for querying
  rpc ProcessTerminalRewardEventsForPinot (events.RewardStatusUpdateEvent) returns (ConsumerResponse);

  // projections pinot consumer which consumes projection generation/update events,
  // which are pushed to ProjectionEvent sns topic,stitches other information
  // and publishes them to kafka topic for uploading to realtime table for projections (reward_projections)
  rpc ProcessProjectionEventForPinot (events.ProjectionEvent)returns (ConsumerResponse);
}

message ConsumerResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

