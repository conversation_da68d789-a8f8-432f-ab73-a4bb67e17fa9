// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/rewards/pinot/service.proto

package pinot

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	categorizer "github.com/epifi/gamma/api/categorizer"

	external "github.com/epifi/gamma/api/tiering/external"

	order "github.com/epifi/gamma/api/order"

	payment "github.com/epifi/gamma/api/order/payment"

	rewards "github.com/epifi/gamma/api/rewards"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = categorizer.L0(0)

	_ = external.Tier(0)

	_ = order.OrderWorkflow(0)

	_ = payment.PaymentProtocol(0)

	_ = rewards.RewardType(0)
)

// Validate checks the field values on TimeRangeFilter with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TimeRangeFilter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeRangeFilter with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TimeRangeFilterMultiError, or nil if none found.
func (m *TimeRangeFilter) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeRangeFilter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeFilterValidationError{
					field:  "Range",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeFilterValidationError{
					field:  "Range",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeFilterValidationError{
				field:  "Range",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TimeRangeFilterMultiError(errors)
	}

	return nil
}

// TimeRangeFilterMultiError is an error wrapping multiple validation errors
// returned by TimeRangeFilter.ValidateAll() if the designated constraints
// aren't met.
type TimeRangeFilterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeRangeFilterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeRangeFilterMultiError) AllErrors() []error { return m }

// TimeRangeFilterValidationError is the validation error returned by
// TimeRangeFilter.Validate if the designated constraints aren't met.
type TimeRangeFilterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeRangeFilterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeRangeFilterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeRangeFilterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeRangeFilterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeRangeFilterValidationError) ErrorName() string { return "TimeRangeFilterValidationError" }

// Error satisfies the builtin error interface
func (e TimeRangeFilterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeRangeFilter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeRangeFilterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeRangeFilterValidationError{}

// Validate checks the field values on TimeRange with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TimeRange) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TimeRange with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TimeRangeMultiError, or nil
// if none found.
func (m *TimeRange) ValidateAll() error {
	return m.validate(true)
}

func (m *TimeRange) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFrom()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "From",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFrom()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeValidationError{
				field:  "From",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TimeRangeValidationError{
					field:  "To",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TimeRangeValidationError{
				field:  "To",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TimeRangeMultiError(errors)
	}

	return nil
}

// TimeRangeMultiError is an error wrapping multiple validation errors returned
// by TimeRange.ValidateAll() if the designated constraints aren't met.
type TimeRangeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TimeRangeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TimeRangeMultiError) AllErrors() []error { return m }

// TimeRangeValidationError is the validation error returned by
// TimeRange.Validate if the designated constraints aren't met.
type TimeRangeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TimeRangeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TimeRangeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TimeRangeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TimeRangeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TimeRangeValidationError) ErrorName() string { return "TimeRangeValidationError" }

// Error satisfies the builtin error interface
func (e TimeRangeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTimeRange.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TimeRangeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TimeRangeValidationError{}

// Validate checks the field values on GetRewardsAggregatesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardsAggregatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardsAggregatesRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardsAggregatesRequestMultiError, or nil if none found.
func (m *GetRewardsAggregatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardsAggregatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardsAggregatesRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardsAggregatesRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardsAggregatesRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardsAggregatesRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardsAggregatesRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardsAggregatesRequestValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRewardsAggregatesRequestMultiError(errors)
	}

	return nil
}

// GetRewardsAggregatesRequestMultiError is an error wrapping multiple
// validation errors returned by GetRewardsAggregatesRequest.ValidateAll() if
// the designated constraints aren't met.
type GetRewardsAggregatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardsAggregatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardsAggregatesRequestMultiError) AllErrors() []error { return m }

// GetRewardsAggregatesRequestValidationError is the validation error returned
// by GetRewardsAggregatesRequest.Validate if the designated constraints
// aren't met.
type GetRewardsAggregatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardsAggregatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardsAggregatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardsAggregatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardsAggregatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardsAggregatesRequestValidationError) ErrorName() string {
	return "GetRewardsAggregatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardsAggregatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardsAggregatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardsAggregatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardsAggregatesRequestValidationError{}

// Validate checks the field values on Filters with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Filters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Filters with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in FiltersMultiError, or nil if none found.
func (m *Filters) ValidateAll() error {
	return m.validate(true)
}

func (m *Filters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardType

	// no validation rules for OfferId

	// no validation rules for ClaimType

	// no validation rules for AccountTier

	// no validation rules for PaymentProtocol

	// no validation rules for OrderWorkflow

	if all {
		switch v := interface{}(m.GetMiscellaneousFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiltersValidationError{
					field:  "MiscellaneousFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiltersValidationError{
					field:  "MiscellaneousFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMiscellaneousFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiltersValidationError{
				field:  "MiscellaneousFilters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FiltersMultiError(errors)
	}

	return nil
}

// FiltersMultiError is an error wrapping multiple validation errors returned
// by Filters.ValidateAll() if the designated constraints aren't met.
type FiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiltersMultiError) AllErrors() []error { return m }

// FiltersValidationError is the validation error returned by Filters.Validate
// if the designated constraints aren't met.
type FiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiltersValidationError) ErrorName() string { return "FiltersValidationError" }

// Error satisfies the builtin error interface
func (e FiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiltersValidationError{}

// Validate checks the field values on MiscellaneousFilters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MiscellaneousFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MiscellaneousFilters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MiscellaneousFiltersMultiError, or nil if none found.
func (m *MiscellaneousFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *MiscellaneousFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for MergeFiCoinsToFiPoints

	if len(errors) > 0 {
		return MiscellaneousFiltersMultiError(errors)
	}

	return nil
}

// MiscellaneousFiltersMultiError is an error wrapping multiple validation
// errors returned by MiscellaneousFilters.ValidateAll() if the designated
// constraints aren't met.
type MiscellaneousFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MiscellaneousFiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MiscellaneousFiltersMultiError) AllErrors() []error { return m }

// MiscellaneousFiltersValidationError is the validation error returned by
// MiscellaneousFilters.Validate if the designated constraints aren't met.
type MiscellaneousFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MiscellaneousFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MiscellaneousFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MiscellaneousFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MiscellaneousFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MiscellaneousFiltersValidationError) ErrorName() string {
	return "MiscellaneousFiltersValidationError"
}

// Error satisfies the builtin error interface
func (e MiscellaneousFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMiscellaneousFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MiscellaneousFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MiscellaneousFiltersValidationError{}

// Validate checks the field values on GetRewardsAggregatesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRewardsAggregatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRewardsAggregatesResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetRewardsAggregatesResponseMultiError, or nil if none found.
func (m *GetRewardsAggregatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRewardsAggregatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRewardsAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRewardsAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRewardsAggregatesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRewardOptionAggregates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetRewardsAggregatesResponseValidationError{
						field:  fmt.Sprintf("RewardOptionAggregates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetRewardsAggregatesResponseValidationError{
						field:  fmt.Sprintf("RewardOptionAggregates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetRewardsAggregatesResponseValidationError{
					field:  fmt.Sprintf("RewardOptionAggregates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetRewardsAggregatesResponseMultiError(errors)
	}

	return nil
}

// GetRewardsAggregatesResponseMultiError is an error wrapping multiple
// validation errors returned by GetRewardsAggregatesResponse.ValidateAll() if
// the designated constraints aren't met.
type GetRewardsAggregatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRewardsAggregatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRewardsAggregatesResponseMultiError) AllErrors() []error { return m }

// GetRewardsAggregatesResponseValidationError is the validation error returned
// by GetRewardsAggregatesResponse.Validate if the designated constraints
// aren't met.
type GetRewardsAggregatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRewardsAggregatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRewardsAggregatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRewardsAggregatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRewardsAggregatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRewardsAggregatesResponseValidationError) ErrorName() string {
	return "GetRewardsAggregatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRewardsAggregatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRewardsAggregatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRewardsAggregatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRewardsAggregatesResponseValidationError{}

// Validate checks the field values on RewardOptionAggregate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardOptionAggregate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardOptionAggregate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardOptionAggregateMultiError, or nil if none found.
func (m *RewardOptionAggregate) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardOptionAggregate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardType

	// no validation rules for RewardUnits

	// no validation rules for RewardCount

	if len(errors) > 0 {
		return RewardOptionAggregateMultiError(errors)
	}

	return nil
}

// RewardOptionAggregateMultiError is an error wrapping multiple validation
// errors returned by RewardOptionAggregate.ValidateAll() if the designated
// constraints aren't met.
type RewardOptionAggregateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardOptionAggregateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardOptionAggregateMultiError) AllErrors() []error { return m }

// RewardOptionAggregateValidationError is the validation error returned by
// RewardOptionAggregate.Validate if the designated constraints aren't met.
type RewardOptionAggregateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardOptionAggregateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardOptionAggregateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardOptionAggregateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardOptionAggregateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardOptionAggregateValidationError) ErrorName() string {
	return "RewardOptionAggregateValidationError"
}

// Error satisfies the builtin error interface
func (e RewardOptionAggregateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardOptionAggregate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardOptionAggregateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardOptionAggregateValidationError{}

// Validate checks the field values on GetMerchantRewardsAggregatesRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetMerchantRewardsAggregatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMerchantRewardsAggregatesRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetMerchantRewardsAggregatesRequestMultiError, or nil if none found.
func (m *GetMerchantRewardsAggregatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantRewardsAggregatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetFromCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMerchantRewardsAggregatesRequestValidationError{
					field:  "FromCreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMerchantRewardsAggregatesRequestValidationError{
					field:  "FromCreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMerchantRewardsAggregatesRequestValidationError{
				field:  "FromCreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMerchantRewardsAggregatesRequestValidationError{
					field:  "ToCreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMerchantRewardsAggregatesRequestValidationError{
					field:  "ToCreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMerchantRewardsAggregatesRequestValidationError{
				field:  "ToCreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActionType

	// no validation rules for RewardType

	if len(errors) > 0 {
		return GetMerchantRewardsAggregatesRequestMultiError(errors)
	}

	return nil
}

// GetMerchantRewardsAggregatesRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetMerchantRewardsAggregatesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetMerchantRewardsAggregatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantRewardsAggregatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantRewardsAggregatesRequestMultiError) AllErrors() []error { return m }

// GetMerchantRewardsAggregatesRequestValidationError is the validation error
// returned by GetMerchantRewardsAggregatesRequest.Validate if the designated
// constraints aren't met.
type GetMerchantRewardsAggregatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantRewardsAggregatesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMerchantRewardsAggregatesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMerchantRewardsAggregatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMerchantRewardsAggregatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMerchantRewardsAggregatesRequestValidationError) ErrorName() string {
	return "GetMerchantRewardsAggregatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantRewardsAggregatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantRewardsAggregatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantRewardsAggregatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantRewardsAggregatesRequestValidationError{}

// Validate checks the field values on GetMerchantRewardsAggregatesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetMerchantRewardsAggregatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetMerchantRewardsAggregatesResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetMerchantRewardsAggregatesResponseMultiError, or nil if none found.
func (m *GetMerchantRewardsAggregatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetMerchantRewardsAggregatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetMerchantRewardsAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetMerchantRewardsAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetMerchantRewardsAggregatesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetMerchantRewardAggregates() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetMerchantRewardsAggregatesResponseValidationError{
						field:  fmt.Sprintf("MerchantRewardAggregates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetMerchantRewardsAggregatesResponseValidationError{
						field:  fmt.Sprintf("MerchantRewardAggregates[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetMerchantRewardsAggregatesResponseValidationError{
					field:  fmt.Sprintf("MerchantRewardAggregates[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetMerchantRewardsAggregatesResponseMultiError(errors)
	}

	return nil
}

// GetMerchantRewardsAggregatesResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetMerchantRewardsAggregatesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetMerchantRewardsAggregatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetMerchantRewardsAggregatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetMerchantRewardsAggregatesResponseMultiError) AllErrors() []error { return m }

// GetMerchantRewardsAggregatesResponseValidationError is the validation error
// returned by GetMerchantRewardsAggregatesResponse.Validate if the designated
// constraints aren't met.
type GetMerchantRewardsAggregatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetMerchantRewardsAggregatesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetMerchantRewardsAggregatesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetMerchantRewardsAggregatesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetMerchantRewardsAggregatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetMerchantRewardsAggregatesResponseValidationError) ErrorName() string {
	return "GetMerchantRewardsAggregatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetMerchantRewardsAggregatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetMerchantRewardsAggregatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetMerchantRewardsAggregatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetMerchantRewardsAggregatesResponseValidationError{}

// Validate checks the field values on MerchantRewardAggregate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *MerchantRewardAggregate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on MerchantRewardAggregate with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// MerchantRewardAggregateMultiError, or nil if none found.
func (m *MerchantRewardAggregate) ValidateAll() error {
	return m.validate(true)
}

func (m *MerchantRewardAggregate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRewardAggregate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, MerchantRewardAggregateValidationError{
					field:  "RewardAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, MerchantRewardAggregateValidationError{
					field:  "RewardAggregate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardAggregate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return MerchantRewardAggregateValidationError{
				field:  "RewardAggregate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MerchantId

	// no validation rules for MerchantName

	if len(errors) > 0 {
		return MerchantRewardAggregateMultiError(errors)
	}

	return nil
}

// MerchantRewardAggregateMultiError is an error wrapping multiple validation
// errors returned by MerchantRewardAggregate.ValidateAll() if the designated
// constraints aren't met.
type MerchantRewardAggregateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m MerchantRewardAggregateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m MerchantRewardAggregateMultiError) AllErrors() []error { return m }

// MerchantRewardAggregateValidationError is the validation error returned by
// MerchantRewardAggregate.Validate if the designated constraints aren't met.
type MerchantRewardAggregateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e MerchantRewardAggregateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e MerchantRewardAggregateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e MerchantRewardAggregateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e MerchantRewardAggregateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e MerchantRewardAggregateValidationError) ErrorName() string {
	return "MerchantRewardAggregateValidationError"
}

// Error satisfies the builtin error interface
func (e MerchantRewardAggregateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sMerchantRewardAggregate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = MerchantRewardAggregateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = MerchantRewardAggregateValidationError{}

// Validate checks the field values on
// GetActualizedRewardProjectionsAggregatesRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetActualizedRewardProjectionsAggregatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetActualizedRewardProjectionsAggregatesRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetActualizedRewardProjectionsAggregatesRequestMultiError, or nil if none found.
func (m *GetActualizedRewardProjectionsAggregatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActualizedRewardProjectionsAggregatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActualizedRewardProjectionsAggregatesRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActualizedRewardProjectionsAggregatesRequestValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetActualizedRewardProjectionsAggregatesRequestMultiError(errors)
	}

	return nil
}

// GetActualizedRewardProjectionsAggregatesRequestMultiError is an error
// wrapping multiple validation errors returned by
// GetActualizedRewardProjectionsAggregatesRequest.ValidateAll() if the
// designated constraints aren't met.
type GetActualizedRewardProjectionsAggregatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActualizedRewardProjectionsAggregatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActualizedRewardProjectionsAggregatesRequestMultiError) AllErrors() []error { return m }

// GetActualizedRewardProjectionsAggregatesRequestValidationError is the
// validation error returned by
// GetActualizedRewardProjectionsAggregatesRequest.Validate if the designated
// constraints aren't met.
type GetActualizedRewardProjectionsAggregatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActualizedRewardProjectionsAggregatesRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetActualizedRewardProjectionsAggregatesRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetActualizedRewardProjectionsAggregatesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetActualizedRewardProjectionsAggregatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActualizedRewardProjectionsAggregatesRequestValidationError) ErrorName() string {
	return "GetActualizedRewardProjectionsAggregatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetActualizedRewardProjectionsAggregatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActualizedRewardProjectionsAggregatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActualizedRewardProjectionsAggregatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActualizedRewardProjectionsAggregatesRequestValidationError{}

// Validate checks the field values on ProjectionFilters with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ProjectionFilters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectionFilters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProjectionFiltersMultiError, or nil if none found.
func (m *ProjectionFilters) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectionFilters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferId

	// no validation rules for AccountId

	// no validation rules for AccountTier

	// no validation rules for PaymentProtocol

	// no validation rules for OrderWorkflow

	if len(errors) > 0 {
		return ProjectionFiltersMultiError(errors)
	}

	return nil
}

// ProjectionFiltersMultiError is an error wrapping multiple validation errors
// returned by ProjectionFilters.ValidateAll() if the designated constraints
// aren't met.
type ProjectionFiltersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectionFiltersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectionFiltersMultiError) AllErrors() []error { return m }

// ProjectionFiltersValidationError is the validation error returned by
// ProjectionFilters.Validate if the designated constraints aren't met.
type ProjectionFiltersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectionFiltersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectionFiltersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectionFiltersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectionFiltersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectionFiltersValidationError) ErrorName() string {
	return "ProjectionFiltersValidationError"
}

// Error satisfies the builtin error interface
func (e ProjectionFiltersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectionFilters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectionFiltersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectionFiltersValidationError{}

// Validate checks the field values on
// GetActualizedRewardProjectionsAggregatesResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetActualizedRewardProjectionsAggregatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetActualizedRewardProjectionsAggregatesResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetActualizedRewardProjectionsAggregatesResponseMultiError, or nil if none found.
func (m *GetActualizedRewardProjectionsAggregatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetActualizedRewardProjectionsAggregatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActualizedRewardProjectionsAggregatesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardProjectionsAggregates()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "RewardProjectionsAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "RewardProjectionsAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardProjectionsAggregates()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetActualizedRewardProjectionsAggregatesResponseValidationError{
				field:  "RewardProjectionsAggregates",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetActualizedRewardProjectionsAggregatesResponseMultiError(errors)
	}

	return nil
}

// GetActualizedRewardProjectionsAggregatesResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetActualizedRewardProjectionsAggregatesResponse.ValidateAll() if the
// designated constraints aren't met.
type GetActualizedRewardProjectionsAggregatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetActualizedRewardProjectionsAggregatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetActualizedRewardProjectionsAggregatesResponseMultiError) AllErrors() []error { return m }

// GetActualizedRewardProjectionsAggregatesResponseValidationError is the
// validation error returned by
// GetActualizedRewardProjectionsAggregatesResponse.Validate if the designated
// constraints aren't met.
type GetActualizedRewardProjectionsAggregatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetActualizedRewardProjectionsAggregatesResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetActualizedRewardProjectionsAggregatesResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetActualizedRewardProjectionsAggregatesResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetActualizedRewardProjectionsAggregatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetActualizedRewardProjectionsAggregatesResponseValidationError) ErrorName() string {
	return "GetActualizedRewardProjectionsAggregatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetActualizedRewardProjectionsAggregatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetActualizedRewardProjectionsAggregatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetActualizedRewardProjectionsAggregatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetActualizedRewardProjectionsAggregatesResponseValidationError{}

// Validate checks the field values on ActualizedRewardProjectionsAggregate
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ActualizedRewardProjectionsAggregate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActualizedRewardProjectionsAggregate
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ActualizedRewardProjectionsAggregateMultiError, or nil if none found.
func (m *ActualizedRewardProjectionsAggregate) ValidateAll() error {
	return m.validate(true)
}

func (m *ActualizedRewardProjectionsAggregate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClaimedRewardProjections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActualizedRewardProjectionsAggregateValidationError{
						field:  fmt.Sprintf("ClaimedRewardProjections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActualizedRewardProjectionsAggregateValidationError{
						field:  fmt.Sprintf("ClaimedRewardProjections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActualizedRewardProjectionsAggregateValidationError{
					field:  fmt.Sprintf("ClaimedRewardProjections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetUnclaimedRewardProjections() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ActualizedRewardProjectionsAggregateValidationError{
						field:  fmt.Sprintf("UnclaimedRewardProjections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ActualizedRewardProjectionsAggregateValidationError{
						field:  fmt.Sprintf("UnclaimedRewardProjections[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ActualizedRewardProjectionsAggregateValidationError{
					field:  fmt.Sprintf("UnclaimedRewardProjections[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ActualizedRewardProjectionsAggregateMultiError(errors)
	}

	return nil
}

// ActualizedRewardProjectionsAggregateMultiError is an error wrapping multiple
// validation errors returned by
// ActualizedRewardProjectionsAggregate.ValidateAll() if the designated
// constraints aren't met.
type ActualizedRewardProjectionsAggregateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActualizedRewardProjectionsAggregateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActualizedRewardProjectionsAggregateMultiError) AllErrors() []error { return m }

// ActualizedRewardProjectionsAggregateValidationError is the validation error
// returned by ActualizedRewardProjectionsAggregate.Validate if the designated
// constraints aren't met.
type ActualizedRewardProjectionsAggregateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActualizedRewardProjectionsAggregateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActualizedRewardProjectionsAggregateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActualizedRewardProjectionsAggregateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActualizedRewardProjectionsAggregateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActualizedRewardProjectionsAggregateValidationError) ErrorName() string {
	return "ActualizedRewardProjectionsAggregateValidationError"
}

// Error satisfies the builtin error interface
func (e ActualizedRewardProjectionsAggregateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActualizedRewardProjectionsAggregate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActualizedRewardProjectionsAggregateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActualizedRewardProjectionsAggregateValidationError{}

// Validate checks the field values on RewardProjectionOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RewardProjectionOption) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RewardProjectionOption with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RewardProjectionOptionMultiError, or nil if none found.
func (m *RewardProjectionOption) ValidateAll() error {
	return m.validate(true)
}

func (m *RewardProjectionOption) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RewardType

	// no validation rules for RewardUnits

	if len(errors) > 0 {
		return RewardProjectionOptionMultiError(errors)
	}

	return nil
}

// RewardProjectionOptionMultiError is an error wrapping multiple validation
// errors returned by RewardProjectionOption.ValidateAll() if the designated
// constraints aren't met.
type RewardProjectionOptionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RewardProjectionOptionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RewardProjectionOptionMultiError) AllErrors() []error { return m }

// RewardProjectionOptionValidationError is the validation error returned by
// RewardProjectionOption.Validate if the designated constraints aren't met.
type RewardProjectionOptionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RewardProjectionOptionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RewardProjectionOptionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RewardProjectionOptionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RewardProjectionOptionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RewardProjectionOptionValidationError) ErrorName() string {
	return "RewardProjectionOptionValidationError"
}

// Error satisfies the builtin error interface
func (e RewardProjectionOptionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRewardProjectionOption.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RewardProjectionOptionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RewardProjectionOptionValidationError{}

// Validate checks the field values on
// GetUnActualizedRewardProjectionsAggregatesRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetUnActualizedRewardProjectionsAggregatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUnActualizedRewardProjectionsAggregatesRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetUnActualizedRewardProjectionsAggregatesRequestMultiError, or nil if none found.
func (m *GetUnActualizedRewardProjectionsAggregatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUnActualizedRewardProjectionsAggregatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RewardType

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUnActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUnActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUnActualizedRewardProjectionsAggregatesRequestValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUnActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUnActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUnActualizedRewardProjectionsAggregatesRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUnActualizedRewardProjectionsAggregatesRequestMultiError(errors)
	}

	return nil
}

// GetUnActualizedRewardProjectionsAggregatesRequestMultiError is an error
// wrapping multiple validation errors returned by
// GetUnActualizedRewardProjectionsAggregatesRequest.ValidateAll() if the
// designated constraints aren't met.
type GetUnActualizedRewardProjectionsAggregatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUnActualizedRewardProjectionsAggregatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUnActualizedRewardProjectionsAggregatesRequestMultiError) AllErrors() []error { return m }

// GetUnActualizedRewardProjectionsAggregatesRequestValidationError is the
// validation error returned by
// GetUnActualizedRewardProjectionsAggregatesRequest.Validate if the
// designated constraints aren't met.
type GetUnActualizedRewardProjectionsAggregatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUnActualizedRewardProjectionsAggregatesRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetUnActualizedRewardProjectionsAggregatesRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetUnActualizedRewardProjectionsAggregatesRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetUnActualizedRewardProjectionsAggregatesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUnActualizedRewardProjectionsAggregatesRequestValidationError) ErrorName() string {
	return "GetUnActualizedRewardProjectionsAggregatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUnActualizedRewardProjectionsAggregatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUnActualizedRewardProjectionsAggregatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUnActualizedRewardProjectionsAggregatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUnActualizedRewardProjectionsAggregatesRequestValidationError{}

// Validate checks the field values on
// GetUnActualizedRewardProjectionsAggregatesResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUnActualizedRewardProjectionsAggregatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetUnActualizedRewardProjectionsAggregatesResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetUnActualizedRewardProjectionsAggregatesResponseMultiError, or nil if
// none found.
func (m *GetUnActualizedRewardProjectionsAggregatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUnActualizedRewardProjectionsAggregatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUnActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUnActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUnActualizedRewardProjectionsAggregatesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardProjectionsAggregates()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetUnActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "RewardProjectionsAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetUnActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "RewardProjectionsAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardProjectionsAggregates()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetUnActualizedRewardProjectionsAggregatesResponseValidationError{
				field:  "RewardProjectionsAggregates",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetUnActualizedRewardProjectionsAggregatesResponseMultiError(errors)
	}

	return nil
}

// GetUnActualizedRewardProjectionsAggregatesResponseMultiError is an error
// wrapping multiple validation errors returned by
// GetUnActualizedRewardProjectionsAggregatesResponse.ValidateAll() if the
// designated constraints aren't met.
type GetUnActualizedRewardProjectionsAggregatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUnActualizedRewardProjectionsAggregatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUnActualizedRewardProjectionsAggregatesResponseMultiError) AllErrors() []error { return m }

// GetUnActualizedRewardProjectionsAggregatesResponseValidationError is the
// validation error returned by
// GetUnActualizedRewardProjectionsAggregatesResponse.Validate if the
// designated constraints aren't met.
type GetUnActualizedRewardProjectionsAggregatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUnActualizedRewardProjectionsAggregatesResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetUnActualizedRewardProjectionsAggregatesResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetUnActualizedRewardProjectionsAggregatesResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetUnActualizedRewardProjectionsAggregatesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUnActualizedRewardProjectionsAggregatesResponseValidationError) ErrorName() string {
	return "GetUnActualizedRewardProjectionsAggregatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetUnActualizedRewardProjectionsAggregatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUnActualizedRewardProjectionsAggregatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUnActualizedRewardProjectionsAggregatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUnActualizedRewardProjectionsAggregatesResponseValidationError{}

// Validate checks the field values on
// GetCustomUnActualizedRewardProjectionsAggregatesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomUnActualizedRewardProjectionsAggregatesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCustomUnActualizedRewardProjectionsAggregatesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomUnActualizedRewardProjectionsAggregatesRequestMultiError, or nil
// if none found.
func (m *GetCustomUnActualizedRewardProjectionsAggregatesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomUnActualizedRewardProjectionsAggregatesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RewardType

	if all {
		switch v := interface{}(m.GetTimeRange()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "TimeRange",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTimeRange()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError{
				field:  "TimeRange",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProjectionCustomQuery()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "ProjectionCustomQuery",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError{
					field:  "ProjectionCustomQuery",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProjectionCustomQuery()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError{
				field:  "ProjectionCustomQuery",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCustomUnActualizedRewardProjectionsAggregatesRequestMultiError(errors)
	}

	return nil
}

// GetCustomUnActualizedRewardProjectionsAggregatesRequestMultiError is an
// error wrapping multiple validation errors returned by
// GetCustomUnActualizedRewardProjectionsAggregatesRequest.ValidateAll() if
// the designated constraints aren't met.
type GetCustomUnActualizedRewardProjectionsAggregatesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomUnActualizedRewardProjectionsAggregatesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomUnActualizedRewardProjectionsAggregatesRequestMultiError) AllErrors() []error {
	return m
}

// GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError is
// the validation error returned by
// GetCustomUnActualizedRewardProjectionsAggregatesRequest.Validate if the
// designated constraints aren't met.
type GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError) ErrorName() string {
	return "GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomUnActualizedRewardProjectionsAggregatesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomUnActualizedRewardProjectionsAggregatesRequestValidationError{}

// Validate checks the field values on
// GetCustomUnActualizedRewardProjectionsAggregatesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomUnActualizedRewardProjectionsAggregatesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCustomUnActualizedRewardProjectionsAggregatesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomUnActualizedRewardProjectionsAggregatesResponseMultiError, or nil
// if none found.
func (m *GetCustomUnActualizedRewardProjectionsAggregatesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomUnActualizedRewardProjectionsAggregatesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRewardProjectionsAggregates()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "RewardProjectionsAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError{
					field:  "RewardProjectionsAggregates",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRewardProjectionsAggregates()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError{
				field:  "RewardProjectionsAggregates",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCustomUnActualizedRewardProjectionsAggregatesResponseMultiError(errors)
	}

	return nil
}

// GetCustomUnActualizedRewardProjectionsAggregatesResponseMultiError is an
// error wrapping multiple validation errors returned by
// GetCustomUnActualizedRewardProjectionsAggregatesResponse.ValidateAll() if
// the designated constraints aren't met.
type GetCustomUnActualizedRewardProjectionsAggregatesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomUnActualizedRewardProjectionsAggregatesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomUnActualizedRewardProjectionsAggregatesResponseMultiError) AllErrors() []error {
	return m
}

// GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError is
// the validation error returned by
// GetCustomUnActualizedRewardProjectionsAggregatesResponse.Validate if the
// designated constraints aren't met.
type GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError) ErrorName() string {
	return "GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomUnActualizedRewardProjectionsAggregatesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomUnActualizedRewardProjectionsAggregatesResponseValidationError{}

// Validate checks the field values on ProjectionCustomQuery with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProjectionCustomQuery) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProjectionCustomQuery with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProjectionCustomQueryMultiError, or nil if none found.
func (m *ProjectionCustomQuery) ValidateAll() error {
	return m.validate(true)
}

func (m *ProjectionCustomQuery) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	switch v := m.Metadata.(type) {
	case *ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata:
		if v == nil {
			err := ProjectionCustomQueryValidationError{
				field:  "Metadata",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTieringMonthlyProjectionsAggregateMetadata()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ProjectionCustomQueryValidationError{
						field:  "TieringMonthlyProjectionsAggregateMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ProjectionCustomQueryValidationError{
						field:  "TieringMonthlyProjectionsAggregateMetadata",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTieringMonthlyProjectionsAggregateMetadata()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ProjectionCustomQueryValidationError{
					field:  "TieringMonthlyProjectionsAggregateMetadata",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ProjectionCustomQueryMultiError(errors)
	}

	return nil
}

// ProjectionCustomQueryMultiError is an error wrapping multiple validation
// errors returned by ProjectionCustomQuery.ValidateAll() if the designated
// constraints aren't met.
type ProjectionCustomQueryMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProjectionCustomQueryMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProjectionCustomQueryMultiError) AllErrors() []error { return m }

// ProjectionCustomQueryValidationError is the validation error returned by
// ProjectionCustomQuery.Validate if the designated constraints aren't met.
type ProjectionCustomQueryValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProjectionCustomQueryValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProjectionCustomQueryValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProjectionCustomQueryValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProjectionCustomQueryValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProjectionCustomQueryValidationError) ErrorName() string {
	return "ProjectionCustomQueryValidationError"
}

// Error satisfies the builtin error interface
func (e ProjectionCustomQueryValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProjectionCustomQuery.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProjectionCustomQueryValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProjectionCustomQueryValidationError{}

// Validate checks the field values on
// TieringMonthlyProjectionsAggregateMetadata with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TieringMonthlyProjectionsAggregateMetadata) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// TieringMonthlyProjectionsAggregateMetadata with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// TieringMonthlyProjectionsAggregateMetadataMultiError, or nil if none found.
func (m *TieringMonthlyProjectionsAggregateMetadata) ValidateAll() error {
	return m.validate(true)
}

func (m *TieringMonthlyProjectionsAggregateMetadata) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActionType

	// no validation rules for DailyLimit

	// no validation rules for MonthlyLimit

	if len(errors) > 0 {
		return TieringMonthlyProjectionsAggregateMetadataMultiError(errors)
	}

	return nil
}

// TieringMonthlyProjectionsAggregateMetadataMultiError is an error wrapping
// multiple validation errors returned by
// TieringMonthlyProjectionsAggregateMetadata.ValidateAll() if the designated
// constraints aren't met.
type TieringMonthlyProjectionsAggregateMetadataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TieringMonthlyProjectionsAggregateMetadataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TieringMonthlyProjectionsAggregateMetadataMultiError) AllErrors() []error { return m }

// TieringMonthlyProjectionsAggregateMetadataValidationError is the validation
// error returned by TieringMonthlyProjectionsAggregateMetadata.Validate if
// the designated constraints aren't met.
type TieringMonthlyProjectionsAggregateMetadataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TieringMonthlyProjectionsAggregateMetadataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TieringMonthlyProjectionsAggregateMetadataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TieringMonthlyProjectionsAggregateMetadataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TieringMonthlyProjectionsAggregateMetadataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TieringMonthlyProjectionsAggregateMetadataValidationError) ErrorName() string {
	return "TieringMonthlyProjectionsAggregateMetadataValidationError"
}

// Error satisfies the builtin error interface
func (e TieringMonthlyProjectionsAggregateMetadataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTieringMonthlyProjectionsAggregateMetadata.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TieringMonthlyProjectionsAggregateMetadataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TieringMonthlyProjectionsAggregateMetadataValidationError{}
