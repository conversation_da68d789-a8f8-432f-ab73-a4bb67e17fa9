// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/rewards/pinot/service.proto

package pinot

import (
	rpc "github.com/epifi/be-common/api/rpc"
	categorizer "github.com/epifi/gamma/api/categorizer"
	order "github.com/epifi/gamma/api/order"
	payment "github.com/epifi/gamma/api/order/payment"
	rewards "github.com/epifi/gamma/api/rewards"
	external "github.com/epifi/gamma/api/tiering/external"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// TimeRangeType defines the type of time range to filter on.
type TimeRangeType int32

const (
	TimeRangeType_TIME_RANGE_TYPE_UNSPECIFIED TimeRangeType = 0
	TimeRangeType_TIME_RANGE_TYPE_CREATED_AT  TimeRangeType = 1 // Time when the record was created.
	TimeRangeType_TIME_RANGE_TYPE_ACTION_TIME TimeRangeType = 2 // Time when the action associated with the record occurred.
)

// Enum value maps for TimeRangeType.
var (
	TimeRangeType_name = map[int32]string{
		0: "TIME_RANGE_TYPE_UNSPECIFIED",
		1: "TIME_RANGE_TYPE_CREATED_AT",
		2: "TIME_RANGE_TYPE_ACTION_TIME",
	}
	TimeRangeType_value = map[string]int32{
		"TIME_RANGE_TYPE_UNSPECIFIED": 0,
		"TIME_RANGE_TYPE_CREATED_AT":  1,
		"TIME_RANGE_TYPE_ACTION_TIME": 2,
	}
)

func (x TimeRangeType) Enum() *TimeRangeType {
	p := new(TimeRangeType)
	*p = x
	return p
}

func (x TimeRangeType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TimeRangeType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_pinot_service_proto_enumTypes[0].Descriptor()
}

func (TimeRangeType) Type() protoreflect.EnumType {
	return &file_api_rewards_pinot_service_proto_enumTypes[0]
}

func (x TimeRangeType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TimeRangeType.Descriptor instead.
func (TimeRangeType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{0}
}

// ProjectionCustomQueryType enumerates the supported custom aggregation queries.
type ProjectionCustomQueryType int32

const (
	// Default, indicates no custom query is specified.
	ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_UNSPECIFIED ProjectionCustomQueryType = 0
	// Indicates a request to aggregate tiering projections with daily/monthly caps.
	ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE ProjectionCustomQueryType = 1
)

// Enum value maps for ProjectionCustomQueryType.
var (
	ProjectionCustomQueryType_name = map[int32]string{
		0: "PROJECTION_CUSTOM_QUERY_TYPE_UNSPECIFIED",
		1: "PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE",
	}
	ProjectionCustomQueryType_value = map[string]int32{
		"PROJECTION_CUSTOM_QUERY_TYPE_UNSPECIFIED":                           0,
		"PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE": 1,
	}
)

func (x ProjectionCustomQueryType) Enum() *ProjectionCustomQueryType {
	p := new(ProjectionCustomQueryType)
	*p = x
	return p
}

func (x ProjectionCustomQueryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProjectionCustomQueryType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_rewards_pinot_service_proto_enumTypes[1].Descriptor()
}

func (ProjectionCustomQueryType) Type() protoreflect.EnumType {
	return &file_api_rewards_pinot_service_proto_enumTypes[1]
}

func (x ProjectionCustomQueryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProjectionCustomQueryType.Descriptor instead.
func (ProjectionCustomQueryType) EnumDescriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{1}
}

// TimeRangeFilter combines a TimeRangeType with its corresponding TimeRange.
type TimeRangeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type  TimeRangeType `protobuf:"varint,1,opt,name=type,proto3,enum=rewards.pinot.TimeRangeType" json:"type,omitempty"`
	Range *TimeRange    `protobuf:"bytes,2,opt,name=range,proto3" json:"range,omitempty"`
}

func (x *TimeRangeFilter) Reset() {
	*x = TimeRangeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRangeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRangeFilter) ProtoMessage() {}

func (x *TimeRangeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRangeFilter.ProtoReflect.Descriptor instead.
func (*TimeRangeFilter) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{0}
}

func (x *TimeRangeFilter) GetType() TimeRangeType {
	if x != nil {
		return x.Type
	}
	return TimeRangeType_TIME_RANGE_TYPE_UNSPECIFIED
}

func (x *TimeRangeFilter) GetRange() *TimeRange {
	if x != nil {
		return x.Range
	}
	return nil
}

// TimeRange defines a time interval with start and end timestamps.
// Example for March 2024:
// from: "2024-03-01T00:00:00Z" (inclusive)
// to:   "2024-04-01T00:00:00Z" (exclusive)
// This covers all events for the month of March 2024.
type TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	From *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"` // Start time of the range (inclusive).
	To   *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`     // End time of the range (exclusive).
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{1}
}

func (x *TimeRange) GetFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *TimeRange) GetTo() *timestamppb.Timestamp {
	if x != nil {
		return x.To
	}
	return nil
}

type GetRewardsAggregatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory: actorId of the user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Filters to be applied on rewards.
	// See the 'Filters' message definition for details on mandatory fields and how filters are combined.
	Filters *Filters `protobuf:"bytes,5,opt,name=filters,proto3" json:"filters,omitempty"`
	// Mandatory: Time range filter for the aggregation.
	TimeRange *TimeRangeFilter `protobuf:"bytes,6,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *GetRewardsAggregatesRequest) Reset() {
	*x = GetRewardsAggregatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsAggregatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsAggregatesRequest) ProtoMessage() {}

func (x *GetRewardsAggregatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsAggregatesRequest.ProtoReflect.Descriptor instead.
func (*GetRewardsAggregatesRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetRewardsAggregatesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetRewardsAggregatesRequest) GetFilters() *Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *GetRewardsAggregatesRequest) GetTimeRange() *TimeRangeFilter {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

// Filters to be applied on rewards.
// Notes:
// 1. Individual filter fields (e.g., reward_type, offer_id) work in an AND fashion. If multiple are set, all conditions must match.
// 2. Fields that are arrays (e.g., reward_statuses, include_l0_ontologies) work in an OR fashion within themselves. If multiple values are provided for a single array field, any one of those values matching is sufficient for that specific filter condition.
type Filters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType           rewards.RewardType          `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	OfferId              string                      `protobuf:"bytes,2,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	ClaimType            rewards.ClaimType           `protobuf:"varint,3,opt,name=claim_type,json=claimType,proto3,enum=rewards.ClaimType" json:"claim_type,omitempty"`
	AccountTier          external.Tier               `protobuf:"varint,4,opt,name=account_tier,json=accountTier,proto3,enum=tiering.external.Tier" json:"account_tier,omitempty"`
	PaymentProtocol      payment.PaymentProtocol     `protobuf:"varint,5,opt,name=payment_protocol,json=paymentProtocol,proto3,enum=order.payment.PaymentProtocol" json:"payment_protocol,omitempty"`
	OrderWorkflow        order.OrderWorkflow         `protobuf:"varint,6,opt,name=order_workflow,json=orderWorkflow,proto3,enum=order.OrderWorkflow" json:"order_workflow,omitempty"`
	RewardStatuses       []rewards.RewardStatus      `protobuf:"varint,7,rep,packed,name=reward_statuses,json=rewardStatuses,proto3,enum=rewards.RewardStatus" json:"reward_statuses,omitempty"`
	RewardOfferTypes     []rewards.RewardOfferType   `protobuf:"varint,8,rep,packed,name=reward_offer_types,json=rewardOfferTypes,proto3,enum=rewards.RewardOfferType" json:"reward_offer_types,omitempty"`
	IncludeL0Ontologies  []categorizer.L0            `protobuf:"varint,10,rep,packed,name=include_l0_ontologies,json=includeL0Ontologies,proto3,enum=categorizer.L0" json:"include_l0_ontologies,omitempty"`
	ExcludeL0Ontologies  []categorizer.L0            `protobuf:"varint,11,rep,packed,name=exclude_l0_ontologies,json=excludeL0Ontologies,proto3,enum=categorizer.L0" json:"exclude_l0_ontologies,omitempty"`
	IncludeOntologyIds   []string                    `protobuf:"bytes,12,rep,name=include_ontology_ids,json=includeOntologyIds,proto3" json:"include_ontology_ids,omitempty"`
	ExcludeOntologyIds   []string                    `protobuf:"bytes,13,rep,name=exclude_ontology_ids,json=excludeOntologyIds,proto3" json:"exclude_ontology_ids,omitempty"`
	MerchantIds          []string                    `protobuf:"bytes,14,rep,name=merchant_ids,json=merchantIds,proto3" json:"merchant_ids,omitempty"`
	MerchantNames        []string                    `protobuf:"bytes,15,rep,name=merchant_names,json=merchantNames,proto3" json:"merchant_names,omitempty"`
	IncludeActionTypes   []rewards.CollectedDataType `protobuf:"varint,16,rep,packed,name=include_action_types,json=includeActionTypes,proto3,enum=rewards.CollectedDataType" json:"include_action_types,omitempty"`
	ExcludeActionTypes   []rewards.CollectedDataType `protobuf:"varint,17,rep,packed,name=exclude_action_types,json=excludeActionTypes,proto3,enum=rewards.CollectedDataType" json:"exclude_action_types,omitempty"`
	MiscellaneousFilters *MiscellaneousFilters       `protobuf:"bytes,18,opt,name=miscellaneous_filters,json=miscellaneousFilters,proto3" json:"miscellaneous_filters,omitempty"`
}

func (x *Filters) Reset() {
	*x = Filters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Filters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Filters) ProtoMessage() {}

func (x *Filters) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Filters.ProtoReflect.Descriptor instead.
func (*Filters) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{3}
}

func (x *Filters) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *Filters) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *Filters) GetClaimType() rewards.ClaimType {
	if x != nil {
		return x.ClaimType
	}
	return rewards.ClaimType(0)
}

func (x *Filters) GetAccountTier() external.Tier {
	if x != nil {
		return x.AccountTier
	}
	return external.Tier(0)
}

func (x *Filters) GetPaymentProtocol() payment.PaymentProtocol {
	if x != nil {
		return x.PaymentProtocol
	}
	return payment.PaymentProtocol(0)
}

func (x *Filters) GetOrderWorkflow() order.OrderWorkflow {
	if x != nil {
		return x.OrderWorkflow
	}
	return order.OrderWorkflow(0)
}

func (x *Filters) GetRewardStatuses() []rewards.RewardStatus {
	if x != nil {
		return x.RewardStatuses
	}
	return nil
}

func (x *Filters) GetRewardOfferTypes() []rewards.RewardOfferType {
	if x != nil {
		return x.RewardOfferTypes
	}
	return nil
}

func (x *Filters) GetIncludeL0Ontologies() []categorizer.L0 {
	if x != nil {
		return x.IncludeL0Ontologies
	}
	return nil
}

func (x *Filters) GetExcludeL0Ontologies() []categorizer.L0 {
	if x != nil {
		return x.ExcludeL0Ontologies
	}
	return nil
}

func (x *Filters) GetIncludeOntologyIds() []string {
	if x != nil {
		return x.IncludeOntologyIds
	}
	return nil
}

func (x *Filters) GetExcludeOntologyIds() []string {
	if x != nil {
		return x.ExcludeOntologyIds
	}
	return nil
}

func (x *Filters) GetMerchantIds() []string {
	if x != nil {
		return x.MerchantIds
	}
	return nil
}

func (x *Filters) GetMerchantNames() []string {
	if x != nil {
		return x.MerchantNames
	}
	return nil
}

func (x *Filters) GetIncludeActionTypes() []rewards.CollectedDataType {
	if x != nil {
		return x.IncludeActionTypes
	}
	return nil
}

func (x *Filters) GetExcludeActionTypes() []rewards.CollectedDataType {
	if x != nil {
		return x.ExcludeActionTypes
	}
	return nil
}

func (x *Filters) GetMiscellaneousFilters() *MiscellaneousFilters {
	if x != nil {
		return x.MiscellaneousFilters
	}
	return nil
}

// MiscellaneousFilters contains additional filters that are useful for very specific use cases
type MiscellaneousFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// filter to indicate whether to merge fi coins to fi points in the following fashion:
	// migration time: fi coins to fi points migration time
	//  1. If current time is before migration time:
	//     i. We will just directly return the aggregates as earlier.
	//  2. If current time is after migration time:
	//     i. Fetch pre-migration and post-migration aggregates:
	//     - Pre-migration aggregates:
	//     - Fetch the aggregates for the base filter but with the toActionTime set to migrationTime.
	//     - Post-migration aggregates:
	//     - Fetch the aggregates for the base filter but with the fromActionTime set to migrationTime.
	//     ii. Update the aggregates:
	//     - Pre-migration aggregates:
	//     - If the reward type is FI_COINS, we need to convert it to FI_POINTS valuation.
	//     - If the reward type is not FI_COINS, we can just add the reward units on reward type.
	//     - Post-migration aggregates:
	//     - We can just add the reward units together.
	//     iii. Merge the pre-migration and post-migration aggregates together.
	//     iv. Return the merged aggregates.
	MergeFiCoinsToFiPoints bool `protobuf:"varint,1,opt,name=merge_fi_coins_to_fi_points,json=mergeFiCoinsToFiPoints,proto3" json:"merge_fi_coins_to_fi_points,omitempty"`
}

func (x *MiscellaneousFilters) Reset() {
	*x = MiscellaneousFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MiscellaneousFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MiscellaneousFilters) ProtoMessage() {}

func (x *MiscellaneousFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MiscellaneousFilters.ProtoReflect.Descriptor instead.
func (*MiscellaneousFilters) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{4}
}

func (x *MiscellaneousFilters) GetMergeFiCoinsToFiPoints() bool {
	if x != nil {
		return x.MergeFiCoinsToFiPoints
	}
	return false
}

type GetRewardsAggregatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 *rpc.Status              `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RewardOptionAggregates []*RewardOptionAggregate `protobuf:"bytes,2,rep,name=reward_option_aggregates,json=rewardOptionAggregates,proto3" json:"reward_option_aggregates,omitempty"`
}

func (x *GetRewardsAggregatesResponse) Reset() {
	*x = GetRewardsAggregatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRewardsAggregatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRewardsAggregatesResponse) ProtoMessage() {}

func (x *GetRewardsAggregatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRewardsAggregatesResponse.ProtoReflect.Descriptor instead.
func (*GetRewardsAggregatesResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetRewardsAggregatesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRewardsAggregatesResponse) GetRewardOptionAggregates() []*RewardOptionAggregate {
	if x != nil {
		return x.RewardOptionAggregates
	}
	return nil
}

type RewardOptionAggregate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType  rewards.RewardType `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	RewardUnits float64            `protobuf:"fixed64,2,opt,name=reward_units,json=rewardUnits,proto3" json:"reward_units,omitempty"`
	RewardCount int64              `protobuf:"varint,3,opt,name=reward_count,json=rewardCount,proto3" json:"reward_count,omitempty"`
}

func (x *RewardOptionAggregate) Reset() {
	*x = RewardOptionAggregate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardOptionAggregate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardOptionAggregate) ProtoMessage() {}

func (x *RewardOptionAggregate) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardOptionAggregate.ProtoReflect.Descriptor instead.
func (*RewardOptionAggregate) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{6}
}

func (x *RewardOptionAggregate) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *RewardOptionAggregate) GetRewardUnits() float64 {
	if x != nil {
		return x.RewardUnits
	}
	return 0
}

func (x *RewardOptionAggregate) GetRewardCount() int64 {
	if x != nil {
		return x.RewardCount
	}
	return 0
}

type GetMerchantRewardsAggregatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory: actorId of the user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// mandatory: start time of the time range
	FromCreatedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=from_created_at,json=fromCreatedAt,proto3" json:"from_created_at,omitempty"`
	// mandatory: end time of the time range
	ToCreatedAt *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=to_created_at,json=toCreatedAt,proto3" json:"to_created_at,omitempty"`
	// optional: collected data type for which reward aggregates are needed
	ActionType rewards.CollectedDataType `protobuf:"varint,4,opt,name=action_type,json=actionType,proto3,enum=rewards.CollectedDataType" json:"action_type,omitempty"`
	// optional: reward type
	RewardType rewards.RewardType `protobuf:"varint,5,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
}

func (x *GetMerchantRewardsAggregatesRequest) Reset() {
	*x = GetMerchantRewardsAggregatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMerchantRewardsAggregatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMerchantRewardsAggregatesRequest) ProtoMessage() {}

func (x *GetMerchantRewardsAggregatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMerchantRewardsAggregatesRequest.ProtoReflect.Descriptor instead.
func (*GetMerchantRewardsAggregatesRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetMerchantRewardsAggregatesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetMerchantRewardsAggregatesRequest) GetFromCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FromCreatedAt
	}
	return nil
}

func (x *GetMerchantRewardsAggregatesRequest) GetToCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ToCreatedAt
	}
	return nil
}

func (x *GetMerchantRewardsAggregatesRequest) GetActionType() rewards.CollectedDataType {
	if x != nil {
		return x.ActionType
	}
	return rewards.CollectedDataType(0)
}

func (x *GetMerchantRewardsAggregatesRequest) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

type GetMerchantRewardsAggregatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                   *rpc.Status                `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	MerchantRewardAggregates []*MerchantRewardAggregate `protobuf:"bytes,2,rep,name=merchant_reward_aggregates,json=merchantRewardAggregates,proto3" json:"merchant_reward_aggregates,omitempty"`
}

func (x *GetMerchantRewardsAggregatesResponse) Reset() {
	*x = GetMerchantRewardsAggregatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetMerchantRewardsAggregatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMerchantRewardsAggregatesResponse) ProtoMessage() {}

func (x *GetMerchantRewardsAggregatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMerchantRewardsAggregatesResponse.ProtoReflect.Descriptor instead.
func (*GetMerchantRewardsAggregatesResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetMerchantRewardsAggregatesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetMerchantRewardsAggregatesResponse) GetMerchantRewardAggregates() []*MerchantRewardAggregate {
	if x != nil {
		return x.MerchantRewardAggregates
	}
	return nil
}

type MerchantRewardAggregate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardAggregate *RewardOptionAggregate `protobuf:"bytes,1,opt,name=reward_aggregate,json=rewardAggregate,proto3" json:"reward_aggregate,omitempty"`
	MerchantId      string                 `protobuf:"bytes,2,opt,name=merchant_id,json=merchantId,proto3" json:"merchant_id,omitempty"`
	MerchantName    string                 `protobuf:"bytes,3,opt,name=merchant_name,json=merchantName,proto3" json:"merchant_name,omitempty"`
}

func (x *MerchantRewardAggregate) Reset() {
	*x = MerchantRewardAggregate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MerchantRewardAggregate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MerchantRewardAggregate) ProtoMessage() {}

func (x *MerchantRewardAggregate) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MerchantRewardAggregate.ProtoReflect.Descriptor instead.
func (*MerchantRewardAggregate) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{9}
}

func (x *MerchantRewardAggregate) GetRewardAggregate() *RewardOptionAggregate {
	if x != nil {
		return x.RewardAggregate
	}
	return nil
}

func (x *MerchantRewardAggregate) GetMerchantId() string {
	if x != nil {
		return x.MerchantId
	}
	return ""
}

func (x *MerchantRewardAggregate) GetMerchantName() string {
	if x != nil {
		return x.MerchantName
	}
	return ""
}

type GetActualizedRewardProjectionsAggregatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory: actorId of the user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Filters to be applied on projections.
	// See the 'ProjectionFilters' message definition for details on mandatory fields and how filters are combined.
	Filters *ProjectionFilters `protobuf:"bytes,4,opt,name=filters,proto3" json:"filters,omitempty"`
	// Mandatory: Time range filter for the aggregation.
	TimeRange *TimeRangeFilter `protobuf:"bytes,5,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *GetActualizedRewardProjectionsAggregatesRequest) Reset() {
	*x = GetActualizedRewardProjectionsAggregatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActualizedRewardProjectionsAggregatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActualizedRewardProjectionsAggregatesRequest) ProtoMessage() {}

func (x *GetActualizedRewardProjectionsAggregatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActualizedRewardProjectionsAggregatesRequest.ProtoReflect.Descriptor instead.
func (*GetActualizedRewardProjectionsAggregatesRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{10}
}

func (x *GetActualizedRewardProjectionsAggregatesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetActualizedRewardProjectionsAggregatesRequest) GetFilters() *ProjectionFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *GetActualizedRewardProjectionsAggregatesRequest) GetTimeRange() *TimeRangeFilter {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

// Filters to be applied on projections.
// Notes:
// 1. Individual filter fields (e.g., offer_id, account_id) work in an AND fashion. If multiple are set, all conditions must match.
// 2. Fields that are arrays (e.g., reward_offer_types, include_l0_ontologies) work in an OR fashion within themselves. If multiple values are provided for a single array field, any one of those values matching is sufficient for that specific filter condition.
type ProjectionFilters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OfferId             string                      `protobuf:"bytes,1,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	AccountId           string                      `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	AccountTier         external.Tier               `protobuf:"varint,3,opt,name=account_tier,json=accountTier,proto3,enum=tiering.external.Tier" json:"account_tier,omitempty"`
	PaymentProtocol     payment.PaymentProtocol     `protobuf:"varint,4,opt,name=payment_protocol,json=paymentProtocol,proto3,enum=order.payment.PaymentProtocol" json:"payment_protocol,omitempty"`
	OrderWorkflow       order.OrderWorkflow         `protobuf:"varint,5,opt,name=order_workflow,json=orderWorkflow,proto3,enum=order.OrderWorkflow" json:"order_workflow,omitempty"`
	RewardOfferTypes    []rewards.RewardOfferType   `protobuf:"varint,6,rep,packed,name=reward_offer_types,json=rewardOfferTypes,proto3,enum=rewards.RewardOfferType" json:"reward_offer_types,omitempty"`
	ActionTypes         []rewards.CollectedDataType `protobuf:"varint,7,rep,packed,name=action_types,json=actionTypes,proto3,enum=rewards.CollectedDataType" json:"action_types,omitempty"`
	IncludeL0Ontologies []categorizer.L0            `protobuf:"varint,8,rep,packed,name=include_l0_ontologies,json=includeL0Ontologies,proto3,enum=categorizer.L0" json:"include_l0_ontologies,omitempty"`
	ExcludeL0Ontologies []categorizer.L0            `protobuf:"varint,9,rep,packed,name=exclude_l0_ontologies,json=excludeL0Ontologies,proto3,enum=categorizer.L0" json:"exclude_l0_ontologies,omitempty"`
	IncludeOntologyIds  []string                    `protobuf:"bytes,10,rep,name=include_ontology_ids,json=includeOntologyIds,proto3" json:"include_ontology_ids,omitempty"`
	ExcludeOntologyIds  []string                    `protobuf:"bytes,11,rep,name=exclude_ontology_ids,json=excludeOntologyIds,proto3" json:"exclude_ontology_ids,omitempty"`
	MerchantIds         []string                    `protobuf:"bytes,12,rep,name=merchant_ids,json=merchantIds,proto3" json:"merchant_ids,omitempty"`
	MerchantNames       []string                    `protobuf:"bytes,13,rep,name=merchant_names,json=merchantNames,proto3" json:"merchant_names,omitempty"`
}

func (x *ProjectionFilters) Reset() {
	*x = ProjectionFilters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectionFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectionFilters) ProtoMessage() {}

func (x *ProjectionFilters) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectionFilters.ProtoReflect.Descriptor instead.
func (*ProjectionFilters) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{11}
}

func (x *ProjectionFilters) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *ProjectionFilters) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *ProjectionFilters) GetAccountTier() external.Tier {
	if x != nil {
		return x.AccountTier
	}
	return external.Tier(0)
}

func (x *ProjectionFilters) GetPaymentProtocol() payment.PaymentProtocol {
	if x != nil {
		return x.PaymentProtocol
	}
	return payment.PaymentProtocol(0)
}

func (x *ProjectionFilters) GetOrderWorkflow() order.OrderWorkflow {
	if x != nil {
		return x.OrderWorkflow
	}
	return order.OrderWorkflow(0)
}

func (x *ProjectionFilters) GetRewardOfferTypes() []rewards.RewardOfferType {
	if x != nil {
		return x.RewardOfferTypes
	}
	return nil
}

func (x *ProjectionFilters) GetActionTypes() []rewards.CollectedDataType {
	if x != nil {
		return x.ActionTypes
	}
	return nil
}

func (x *ProjectionFilters) GetIncludeL0Ontologies() []categorizer.L0 {
	if x != nil {
		return x.IncludeL0Ontologies
	}
	return nil
}

func (x *ProjectionFilters) GetExcludeL0Ontologies() []categorizer.L0 {
	if x != nil {
		return x.ExcludeL0Ontologies
	}
	return nil
}

func (x *ProjectionFilters) GetIncludeOntologyIds() []string {
	if x != nil {
		return x.IncludeOntologyIds
	}
	return nil
}

func (x *ProjectionFilters) GetExcludeOntologyIds() []string {
	if x != nil {
		return x.ExcludeOntologyIds
	}
	return nil
}

func (x *ProjectionFilters) GetMerchantIds() []string {
	if x != nil {
		return x.MerchantIds
	}
	return nil
}

func (x *ProjectionFilters) GetMerchantNames() []string {
	if x != nil {
		return x.MerchantNames
	}
	return nil
}

type GetActualizedRewardProjectionsAggregatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                      *rpc.Status                           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RewardProjectionsAggregates *ActualizedRewardProjectionsAggregate `protobuf:"bytes,2,opt,name=reward_projections_aggregates,json=rewardProjectionsAggregates,proto3" json:"reward_projections_aggregates,omitempty"`
}

func (x *GetActualizedRewardProjectionsAggregatesResponse) Reset() {
	*x = GetActualizedRewardProjectionsAggregatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetActualizedRewardProjectionsAggregatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActualizedRewardProjectionsAggregatesResponse) ProtoMessage() {}

func (x *GetActualizedRewardProjectionsAggregatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActualizedRewardProjectionsAggregatesResponse.ProtoReflect.Descriptor instead.
func (*GetActualizedRewardProjectionsAggregatesResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{12}
}

func (x *GetActualizedRewardProjectionsAggregatesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetActualizedRewardProjectionsAggregatesResponse) GetRewardProjectionsAggregates() *ActualizedRewardProjectionsAggregate {
	if x != nil {
		return x.RewardProjectionsAggregates
	}
	return nil
}

type ActualizedRewardProjectionsAggregate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ClaimedRewardProjections   []*RewardProjectionOption `protobuf:"bytes,1,rep,name=claimed_reward_projections,json=claimedRewardProjections,proto3" json:"claimed_reward_projections,omitempty"`
	UnclaimedRewardProjections []*RewardProjectionOption `protobuf:"bytes,2,rep,name=unclaimed_reward_projections,json=unclaimedRewardProjections,proto3" json:"unclaimed_reward_projections,omitempty"`
}

func (x *ActualizedRewardProjectionsAggregate) Reset() {
	*x = ActualizedRewardProjectionsAggregate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActualizedRewardProjectionsAggregate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActualizedRewardProjectionsAggregate) ProtoMessage() {}

func (x *ActualizedRewardProjectionsAggregate) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActualizedRewardProjectionsAggregate.ProtoReflect.Descriptor instead.
func (*ActualizedRewardProjectionsAggregate) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{13}
}

func (x *ActualizedRewardProjectionsAggregate) GetClaimedRewardProjections() []*RewardProjectionOption {
	if x != nil {
		return x.ClaimedRewardProjections
	}
	return nil
}

func (x *ActualizedRewardProjectionsAggregate) GetUnclaimedRewardProjections() []*RewardProjectionOption {
	if x != nil {
		return x.UnclaimedRewardProjections
	}
	return nil
}

type RewardProjectionOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RewardType  rewards.RewardType `protobuf:"varint,1,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	RewardUnits float64            `protobuf:"fixed64,2,opt,name=reward_units,json=rewardUnits,proto3" json:"reward_units,omitempty"`
}

func (x *RewardProjectionOption) Reset() {
	*x = RewardProjectionOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RewardProjectionOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RewardProjectionOption) ProtoMessage() {}

func (x *RewardProjectionOption) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RewardProjectionOption.ProtoReflect.Descriptor instead.
func (*RewardProjectionOption) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{14}
}

func (x *RewardProjectionOption) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *RewardProjectionOption) GetRewardUnits() float64 {
	if x != nil {
		return x.RewardUnits
	}
	return 0
}

type GetUnActualizedRewardProjectionsAggregatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory: actorId of the user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// mandatory: reward type
	RewardType rewards.RewardType `protobuf:"varint,2,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	// Mandatory: Time range filter for the aggregation.
	TimeRange *TimeRangeFilter `protobuf:"bytes,3,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	// Filters to be applied on projections.
	// See the 'ProjectionFilters' message definition for details.
	Filters *ProjectionFilters `protobuf:"bytes,4,opt,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetUnActualizedRewardProjectionsAggregatesRequest) Reset() {
	*x = GetUnActualizedRewardProjectionsAggregatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUnActualizedRewardProjectionsAggregatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUnActualizedRewardProjectionsAggregatesRequest) ProtoMessage() {}

func (x *GetUnActualizedRewardProjectionsAggregatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUnActualizedRewardProjectionsAggregatesRequest.ProtoReflect.Descriptor instead.
func (*GetUnActualizedRewardProjectionsAggregatesRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{15}
}

func (x *GetUnActualizedRewardProjectionsAggregatesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetUnActualizedRewardProjectionsAggregatesRequest) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *GetUnActualizedRewardProjectionsAggregatesRequest) GetTimeRange() *TimeRangeFilter {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *GetUnActualizedRewardProjectionsAggregatesRequest) GetFilters() *ProjectionFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

type GetUnActualizedRewardProjectionsAggregatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// aggregates for un-actualized reward projections
	// i.e. aggregates for all reward projections for which reward has not yet been generated for the given reward type
	RewardProjectionsAggregates *RewardProjectionOption `protobuf:"bytes,2,opt,name=reward_projections_aggregates,json=rewardProjectionsAggregates,proto3" json:"reward_projections_aggregates,omitempty"`
}

func (x *GetUnActualizedRewardProjectionsAggregatesResponse) Reset() {
	*x = GetUnActualizedRewardProjectionsAggregatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUnActualizedRewardProjectionsAggregatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUnActualizedRewardProjectionsAggregatesResponse) ProtoMessage() {}

func (x *GetUnActualizedRewardProjectionsAggregatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUnActualizedRewardProjectionsAggregatesResponse.ProtoReflect.Descriptor instead.
func (*GetUnActualizedRewardProjectionsAggregatesResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{16}
}

func (x *GetUnActualizedRewardProjectionsAggregatesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUnActualizedRewardProjectionsAggregatesResponse) GetRewardProjectionsAggregates() *RewardProjectionOption {
	if x != nil {
		return x.RewardProjectionsAggregates
	}
	return nil
}

// Request message for custom un-actualized reward projection aggregations.
type GetCustomUnActualizedRewardProjectionsAggregatesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// mandatory: actorId of the user
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// mandatory: reward type to aggregate on
	RewardType rewards.RewardType `protobuf:"varint,2,opt,name=reward_type,json=rewardType,proto3,enum=rewards.RewardType" json:"reward_type,omitempty"`
	// Mandatory: Time range filter for the aggregation.
	TimeRange *TimeRangeFilter `protobuf:"bytes,3,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
	// mandatory: Specifies the custom query type and its required metadata.
	ProjectionCustomQuery *ProjectionCustomQuery `protobuf:"bytes,4,opt,name=projection_custom_query,json=projectionCustomQuery,proto3" json:"projection_custom_query,omitempty"`
}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesRequest) Reset() {
	*x = GetCustomUnActualizedRewardProjectionsAggregatesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomUnActualizedRewardProjectionsAggregatesRequest) ProtoMessage() {}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomUnActualizedRewardProjectionsAggregatesRequest.ProtoReflect.Descriptor instead.
func (*GetCustomUnActualizedRewardProjectionsAggregatesRequest) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesRequest) GetRewardType() rewards.RewardType {
	if x != nil {
		return x.RewardType
	}
	return rewards.RewardType(0)
}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesRequest) GetTimeRange() *TimeRangeFilter {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesRequest) GetProjectionCustomQuery() *ProjectionCustomQuery {
	if x != nil {
		return x.ProjectionCustomQuery
	}
	return nil
}

// Response message for custom un-actualized reward projection aggregations.
type GetCustomUnActualizedRewardProjectionsAggregatesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// The single aggregated result based on the custom query logic.
	RewardProjectionsAggregates *RewardProjectionOption `protobuf:"bytes,2,opt,name=reward_projections_aggregates,json=rewardProjectionsAggregates,proto3" json:"reward_projections_aggregates,omitempty"`
}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesResponse) Reset() {
	*x = GetCustomUnActualizedRewardProjectionsAggregatesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomUnActualizedRewardProjectionsAggregatesResponse) ProtoMessage() {}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomUnActualizedRewardProjectionsAggregatesResponse.ProtoReflect.Descriptor instead.
func (*GetCustomUnActualizedRewardProjectionsAggregatesResponse) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{18}
}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCustomUnActualizedRewardProjectionsAggregatesResponse) GetRewardProjectionsAggregates() *RewardProjectionOption {
	if x != nil {
		return x.RewardProjectionsAggregates
	}
	return nil
}

// ProjectionCustomQuery defines the specific custom aggregation to perform.
// It includes the type of query and the necessary metadata for that query.
type ProjectionCustomQuery struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The type of custom query to execute.
	Type ProjectionCustomQueryType `protobuf:"varint,1,opt,name=type,proto3,enum=rewards.pinot.ProjectionCustomQueryType" json:"type,omitempty"`
	// Metadata required for the specific custom query type.
	//
	// Types that are assignable to Metadata:
	//
	//	*ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata
	Metadata isProjectionCustomQuery_Metadata `protobuf_oneof:"metadata"`
}

func (x *ProjectionCustomQuery) Reset() {
	*x = ProjectionCustomQuery{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProjectionCustomQuery) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProjectionCustomQuery) ProtoMessage() {}

func (x *ProjectionCustomQuery) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProjectionCustomQuery.ProtoReflect.Descriptor instead.
func (*ProjectionCustomQuery) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{19}
}

func (x *ProjectionCustomQuery) GetType() ProjectionCustomQueryType {
	if x != nil {
		return x.Type
	}
	return ProjectionCustomQueryType_PROJECTION_CUSTOM_QUERY_TYPE_UNSPECIFIED
}

func (m *ProjectionCustomQuery) GetMetadata() isProjectionCustomQuery_Metadata {
	if m != nil {
		return m.Metadata
	}
	return nil
}

func (x *ProjectionCustomQuery) GetTieringMonthlyProjectionsAggregateMetadata() *TieringMonthlyProjectionsAggregateMetadata {
	if x, ok := x.GetMetadata().(*ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata); ok {
		return x.TieringMonthlyProjectionsAggregateMetadata
	}
	return nil
}

type isProjectionCustomQuery_Metadata interface {
	isProjectionCustomQuery_Metadata()
}

type ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata struct {
	// Metadata specific to tiering monthly projection aggregates.
	TieringMonthlyProjectionsAggregateMetadata *TieringMonthlyProjectionsAggregateMetadata `protobuf:"bytes,2,opt,name=tiering_monthly_projections_aggregate_metadata,json=tieringMonthlyProjectionsAggregateMetadata,proto3,oneof"`
}

func (*ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata) isProjectionCustomQuery_Metadata() {
}

// TieringMonthlyProjectionsAggregateMetadata contains parameters needed for tiering aggregation.
type TieringMonthlyProjectionsAggregateMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Mandatory: The specific action type to filter projections for (e.g., ORDER).
	// Ensure this corresponds to the actual action type enum/string used in your data.
	ActionType rewards.CollectedDataType `protobuf:"varint,1,opt,name=action_type,json=actionType,proto3,enum=rewards.CollectedDataType" json:"action_type,omitempty"`
	// Mandatory: The offer types to filter projections for (e.g., PLUS_TIER_1_PERCENT_CASHBACK_OFFER).
	// note: the offer types should be in accordance to the user tier
	OfferTypes []rewards.RewardOfferType `protobuf:"varint,2,rep,packed,name=offer_types,json=offerTypes,proto3,enum=rewards.RewardOfferType" json:"offer_types,omitempty"`
	// The daily cap to apply
	// note : the units here should be in accordance to the reward type and offer_types(which will be in accordance with the user tier)
	DailyLimit int64 `protobuf:"varint,3,opt,name=daily_limit,json=dailyLimit,proto3" json:"daily_limit,omitempty"`
	// The overall monthly cap to apply
	// note : the units here should be in accordance to the reward type and offer_types(which will be in accordance with the user tier)
	MonthlyLimit int64 `protobuf:"varint,4,opt,name=monthly_limit,json=monthlyLimit,proto3" json:"monthly_limit,omitempty"`
}

func (x *TieringMonthlyProjectionsAggregateMetadata) Reset() {
	*x = TieringMonthlyProjectionsAggregateMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_rewards_pinot_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TieringMonthlyProjectionsAggregateMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TieringMonthlyProjectionsAggregateMetadata) ProtoMessage() {}

func (x *TieringMonthlyProjectionsAggregateMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_rewards_pinot_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TieringMonthlyProjectionsAggregateMetadata.ProtoReflect.Descriptor instead.
func (*TieringMonthlyProjectionsAggregateMetadata) Descriptor() ([]byte, []int) {
	return file_api_rewards_pinot_service_proto_rawDescGZIP(), []int{20}
}

func (x *TieringMonthlyProjectionsAggregateMetadata) GetActionType() rewards.CollectedDataType {
	if x != nil {
		return x.ActionType
	}
	return rewards.CollectedDataType(0)
}

func (x *TieringMonthlyProjectionsAggregateMetadata) GetOfferTypes() []rewards.RewardOfferType {
	if x != nil {
		return x.OfferTypes
	}
	return nil
}

func (x *TieringMonthlyProjectionsAggregateMetadata) GetDailyLimit() int64 {
	if x != nil {
		return x.DailyLimit
	}
	return 0
}

func (x *TieringMonthlyProjectionsAggregateMetadata) GetMonthlyLimit() int64 {
	if x != nil {
		return x.MonthlyLimit
	}
	return 0
}

var File_api_rewards_pinot_service_proto protoreflect.FileDescriptor

var file_api_rewards_pinot_service_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x70, 0x69,
	0x6e, 0x6f, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74,
	0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65,
	0x72, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61,
	0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x63,
	0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63,
	0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x2f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x73, 0x0a, 0x0f, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70,
	0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x72, 0x61, 0x6e, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x52, 0x05, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x22, 0x67, 0x0a, 0x09, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x2e, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x2a, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x02, 0x74,
	0x6f, 0x22, 0xbb, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3d,
	0x0a, 0x0a, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e,
	0x6f, 0x74, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4a, 0x04, 0x08,
	0x02, 0x10, 0x03, 0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x4a, 0x04, 0x08, 0x04, 0x10, 0x05, 0x22,
	0x8c, 0x08, 0x0a, 0x07, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x34, 0x0a, 0x0b, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x31, 0x0a, 0x0a,
	0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x12, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x39, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x52, 0x0b, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x69, 0x65, 0x72, 0x12, 0x49, 0x0a, 0x10, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x0f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x3b, 0x0a, 0x0e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x66,
	0x6c, 0x6f, 0x77, 0x52, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x12, 0x3e, 0x0a, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x0e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x65, 0x73, 0x12, 0x46, 0x0a, 0x12, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66,
	0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x15, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6c, 0x30, 0x5f, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67,
	0x69, 0x65, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x4c, 0x30, 0x52, 0x13, 0x69, 0x6e, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x4c, 0x30, 0x4f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x12,
	0x43, 0x0a, 0x15, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6c, 0x30, 0x5f, 0x6f, 0x6e,
	0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x0f,
	0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x4c, 0x30, 0x52,
	0x13, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4c, 0x30, 0x4f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f,
	0x67, 0x69, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f,
	0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4f, 0x6e, 0x74, 0x6f, 0x6c,
	0x6f, 0x67, 0x79, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x5f, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4f, 0x6e, 0x74,
	0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x49, 0x64, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b,
	0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x0f, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x12, 0x4c, 0x0a, 0x14, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x4c, 0x0a, 0x14, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x11, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a,
	0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x58,
	0x0a, 0x15, 0x6d, 0x69, 0x73, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x6e, 0x65, 0x6f, 0x75, 0x73, 0x5f,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x4d, 0x69,
	0x73, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x6e, 0x65, 0x6f, 0x75, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x52, 0x14, 0x6d, 0x69, 0x73, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x6e, 0x65, 0x6f, 0x75,
	0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x4a, 0x04, 0x08, 0x09, 0x10, 0x0a, 0x22, 0x53,
	0x0a, 0x14, 0x4d, 0x69, 0x73, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x6e, 0x65, 0x6f, 0x75, 0x73, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3b, 0x0a, 0x1b, 0x6d, 0x65, 0x72, 0x67, 0x65, 0x5f,
	0x66, 0x69, 0x5f, 0x63, 0x6f, 0x69, 0x6e, 0x73, 0x5f, 0x74, 0x6f, 0x5f, 0x66, 0x69, 0x5f, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x16, 0x6d, 0x65, 0x72,
	0x67, 0x65, 0x46, 0x69, 0x43, 0x6f, 0x69, 0x6e, 0x73, 0x54, 0x6f, 0x46, 0x69, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x22, 0xa3, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5e, 0x0a, 0x18, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x52, 0x16, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x41,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x15, 0x52, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x55, 0x6e, 0x69, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0xb7, 0x02, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x42, 0x0a, 0x0f, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3e, 0x0a, 0x0d, 0x74, 0x6f, 0x5f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x74, 0x6f, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44,
	0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x22, 0xb1, 0x01, 0x0a, 0x24, 0x47, 0x65,
	0x74, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x64, 0x0a, 0x1a, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x4d, 0x65, 0x72, 0x63,
	0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x52, 0x18, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x22, 0xb0, 0x01,
	0x0a, 0x17, 0x4d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x4f, 0x0a, 0x10, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69,
	0x6e, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x52, 0x0f, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x6d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0xd3, 0x01, 0x0a, 0x2f, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x3a, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74,
	0x2e, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3d, 0x0a, 0x0a, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x4a, 0x04, 0x08, 0x02, 0x10, 0x03,
	0x4a, 0x04, 0x08, 0x03, 0x10, 0x04, 0x22, 0xcf, 0x05, 0x0a, 0x11, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x74, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x74,
	0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2e,
	0x54, 0x69, 0x65, 0x72, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x69, 0x65,
	0x72, 0x12, 0x49, 0x0a, 0x10, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x0f, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x3b, 0x0a, 0x0e,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x0d, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x46, 0x0a, 0x12, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x10, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x73, 0x12, 0x3d, 0x0a, 0x0c, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x73,
	0x12, 0x43, 0x0a, 0x15, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6c, 0x30, 0x5f, 0x6f,
	0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x0f, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2e, 0x4c, 0x30,
	0x52, 0x13, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4c, 0x30, 0x4f, 0x6e, 0x74, 0x6f, 0x6c,
	0x6f, 0x67, 0x69, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x15, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x5f, 0x6c, 0x30, 0x5f, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a,
	0x65, 0x72, 0x2e, 0x4c, 0x30, 0x52, 0x13, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x4c, 0x30,
	0x4f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x69, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x6e,
	0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64,
	0x65, 0x4f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x49, 0x64, 0x73, 0x12, 0x30, 0x0a, 0x14,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x6f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x79,
	0x5f, 0x69, 0x64, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x4f, 0x6e, 0x74, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x49, 0x64, 0x73, 0x12, 0x21,
	0x0a, 0x0c, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0c,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x49, 0x64,
	0x73, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0xd0, 0x01, 0x0a, 0x30, 0x47, 0x65, 0x74,
	0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x77, 0x0a, 0x1d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c,
	0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x52, 0x1b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x22, 0xf4, 0x01, 0x0a, 0x24,
	0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x12, 0x63, 0x0a, 0x1a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x65, 0x64, 0x5f,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x18, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x67, 0x0a, 0x1c, 0x75, 0x6e, 0x63,
	0x6c, 0x61, 0x69, 0x6d, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x25, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e,
	0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1a, 0x75, 0x6e, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x65,
	0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x22, 0x71, 0x0a, 0x16, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x34, 0x0a, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x55, 0x6e, 0x69, 0x74, 0x73, 0x22, 0xff, 0x01, 0x0a, 0x31, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x41,
	0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0a,
	0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x07, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x50, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x22, 0xc4, 0x01, 0x0a, 0x32, 0x47, 0x65, 0x74, 0x55,
	0x6e, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x69, 0x0a, 0x1d, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x1b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x22, 0xa7,
	0x02, 0x0a, 0x37, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x55, 0x6e, 0x41, 0x63,
	0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0a, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x5c, 0x0a, 0x17, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f,
	0x71, 0x75, 0x65, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x52, 0x15, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x22, 0xca, 0x01, 0x0a, 0x38, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x69, 0x0a, 0x1d, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x5f, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f,
	0x74, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1b, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x73, 0x22, 0x83, 0x02, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x3c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x9f, 0x01,
	0x0a, 0x2e, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c,
	0x79, 0x5f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x48, 0x00, 0x52, 0x2a, 0x74, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x42,
	0x0a, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x22, 0xea, 0x01, 0x0a, 0x2a,
	0x54, 0x69, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x50, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61,
	0x74, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x3b, 0x0a, 0x0b, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x72,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x5f, 0x6c, 0x69, 0x6d, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x64, 0x61, 0x69, 0x6c, 0x79, 0x4c, 0x69,
	0x6d, 0x69, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x6d, 0x6f, 0x6e, 0x74,
	0x68, 0x6c, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x2a, 0x71, 0x0a, 0x0d, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x49, 0x4d,
	0x45, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x49,
	0x4d, 0x45, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x49,
	0x4d, 0x45, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x02, 0x2a, 0x91, 0x01, 0x0a, 0x19,
	0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x28, 0x50, 0x52, 0x4f,
	0x4a, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x51,
	0x55, 0x45, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x46, 0x0a, 0x42, 0x50, 0x52, 0x4f, 0x4a, 0x45,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x5f, 0x51, 0x55, 0x45,
	0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f,
	0x4d, 0x4f, 0x4e, 0x54, 0x48, 0x4c, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x4a, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x53, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x10, 0x01, 0x32,
	0xb6, 0x06, 0x0a, 0x11, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0x6f, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0x2a, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x72, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x87, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x4d, 0x65,
	0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0x32, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x65, 0x72, 0x63, 0x68,
	0x61, 0x6e, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67,
	0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x4d,
	0x65, 0x72, 0x63, 0x68, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0xab, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0x3e, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72,
	0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72,
	0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb1,
	0x01, 0x0a, 0x2a, 0x47, 0x65, 0x74, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0x40, 0x2e,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65,
	0x74, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x41, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0xc3, 0x01, 0x0a, 0x30, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x55, 0x6e, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61,
	0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67,
	0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x12, 0x46, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64,
	0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x47, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x55, 0x6e, 0x41, 0x63, 0x74, 0x75, 0x61,
	0x6c, 0x69, 0x7a, 0x65, 0x64, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x41, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2e, 0x70,
	0x69, 0x6e, 0x6f, 0x74, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x2f, 0x70, 0x69, 0x6e, 0x6f, 0x74, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_rewards_pinot_service_proto_rawDescOnce sync.Once
	file_api_rewards_pinot_service_proto_rawDescData = file_api_rewards_pinot_service_proto_rawDesc
)

func file_api_rewards_pinot_service_proto_rawDescGZIP() []byte {
	file_api_rewards_pinot_service_proto_rawDescOnce.Do(func() {
		file_api_rewards_pinot_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_rewards_pinot_service_proto_rawDescData)
	})
	return file_api_rewards_pinot_service_proto_rawDescData
}

var file_api_rewards_pinot_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_rewards_pinot_service_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_api_rewards_pinot_service_proto_goTypes = []interface{}{
	(TimeRangeType)(0),                                               // 0: rewards.pinot.TimeRangeType
	(ProjectionCustomQueryType)(0),                                   // 1: rewards.pinot.ProjectionCustomQueryType
	(*TimeRangeFilter)(nil),                                          // 2: rewards.pinot.TimeRangeFilter
	(*TimeRange)(nil),                                                // 3: rewards.pinot.TimeRange
	(*GetRewardsAggregatesRequest)(nil),                              // 4: rewards.pinot.GetRewardsAggregatesRequest
	(*Filters)(nil),                                                  // 5: rewards.pinot.Filters
	(*MiscellaneousFilters)(nil),                                     // 6: rewards.pinot.MiscellaneousFilters
	(*GetRewardsAggregatesResponse)(nil),                             // 7: rewards.pinot.GetRewardsAggregatesResponse
	(*RewardOptionAggregate)(nil),                                    // 8: rewards.pinot.RewardOptionAggregate
	(*GetMerchantRewardsAggregatesRequest)(nil),                      // 9: rewards.pinot.GetMerchantRewardsAggregatesRequest
	(*GetMerchantRewardsAggregatesResponse)(nil),                     // 10: rewards.pinot.GetMerchantRewardsAggregatesResponse
	(*MerchantRewardAggregate)(nil),                                  // 11: rewards.pinot.MerchantRewardAggregate
	(*GetActualizedRewardProjectionsAggregatesRequest)(nil),          // 12: rewards.pinot.GetActualizedRewardProjectionsAggregatesRequest
	(*ProjectionFilters)(nil),                                        // 13: rewards.pinot.ProjectionFilters
	(*GetActualizedRewardProjectionsAggregatesResponse)(nil),         // 14: rewards.pinot.GetActualizedRewardProjectionsAggregatesResponse
	(*ActualizedRewardProjectionsAggregate)(nil),                     // 15: rewards.pinot.ActualizedRewardProjectionsAggregate
	(*RewardProjectionOption)(nil),                                   // 16: rewards.pinot.RewardProjectionOption
	(*GetUnActualizedRewardProjectionsAggregatesRequest)(nil),        // 17: rewards.pinot.GetUnActualizedRewardProjectionsAggregatesRequest
	(*GetUnActualizedRewardProjectionsAggregatesResponse)(nil),       // 18: rewards.pinot.GetUnActualizedRewardProjectionsAggregatesResponse
	(*GetCustomUnActualizedRewardProjectionsAggregatesRequest)(nil),  // 19: rewards.pinot.GetCustomUnActualizedRewardProjectionsAggregatesRequest
	(*GetCustomUnActualizedRewardProjectionsAggregatesResponse)(nil), // 20: rewards.pinot.GetCustomUnActualizedRewardProjectionsAggregatesResponse
	(*ProjectionCustomQuery)(nil),                                    // 21: rewards.pinot.ProjectionCustomQuery
	(*TieringMonthlyProjectionsAggregateMetadata)(nil),               // 22: rewards.pinot.TieringMonthlyProjectionsAggregateMetadata
	(*timestamppb.Timestamp)(nil),                                    // 23: google.protobuf.Timestamp
	(rewards.RewardType)(0),                                          // 24: rewards.RewardType
	(rewards.ClaimType)(0),                                           // 25: rewards.ClaimType
	(external.Tier)(0),                                               // 26: tiering.external.Tier
	(payment.PaymentProtocol)(0),                                     // 27: order.payment.PaymentProtocol
	(order.OrderWorkflow)(0),                                         // 28: order.OrderWorkflow
	(rewards.RewardStatus)(0),                                        // 29: rewards.RewardStatus
	(rewards.RewardOfferType)(0),                                     // 30: rewards.RewardOfferType
	(categorizer.L0)(0),                                              // 31: categorizer.L0
	(rewards.CollectedDataType)(0),                                   // 32: rewards.CollectedDataType
	(*rpc.Status)(nil),                                               // 33: rpc.Status
}
var file_api_rewards_pinot_service_proto_depIdxs = []int32{
	0,  // 0: rewards.pinot.TimeRangeFilter.type:type_name -> rewards.pinot.TimeRangeType
	3,  // 1: rewards.pinot.TimeRangeFilter.range:type_name -> rewards.pinot.TimeRange
	23, // 2: rewards.pinot.TimeRange.from:type_name -> google.protobuf.Timestamp
	23, // 3: rewards.pinot.TimeRange.to:type_name -> google.protobuf.Timestamp
	5,  // 4: rewards.pinot.GetRewardsAggregatesRequest.filters:type_name -> rewards.pinot.Filters
	2,  // 5: rewards.pinot.GetRewardsAggregatesRequest.time_range:type_name -> rewards.pinot.TimeRangeFilter
	24, // 6: rewards.pinot.Filters.reward_type:type_name -> rewards.RewardType
	25, // 7: rewards.pinot.Filters.claim_type:type_name -> rewards.ClaimType
	26, // 8: rewards.pinot.Filters.account_tier:type_name -> tiering.external.Tier
	27, // 9: rewards.pinot.Filters.payment_protocol:type_name -> order.payment.PaymentProtocol
	28, // 10: rewards.pinot.Filters.order_workflow:type_name -> order.OrderWorkflow
	29, // 11: rewards.pinot.Filters.reward_statuses:type_name -> rewards.RewardStatus
	30, // 12: rewards.pinot.Filters.reward_offer_types:type_name -> rewards.RewardOfferType
	31, // 13: rewards.pinot.Filters.include_l0_ontologies:type_name -> categorizer.L0
	31, // 14: rewards.pinot.Filters.exclude_l0_ontologies:type_name -> categorizer.L0
	32, // 15: rewards.pinot.Filters.include_action_types:type_name -> rewards.CollectedDataType
	32, // 16: rewards.pinot.Filters.exclude_action_types:type_name -> rewards.CollectedDataType
	6,  // 17: rewards.pinot.Filters.miscellaneous_filters:type_name -> rewards.pinot.MiscellaneousFilters
	33, // 18: rewards.pinot.GetRewardsAggregatesResponse.status:type_name -> rpc.Status
	8,  // 19: rewards.pinot.GetRewardsAggregatesResponse.reward_option_aggregates:type_name -> rewards.pinot.RewardOptionAggregate
	24, // 20: rewards.pinot.RewardOptionAggregate.reward_type:type_name -> rewards.RewardType
	23, // 21: rewards.pinot.GetMerchantRewardsAggregatesRequest.from_created_at:type_name -> google.protobuf.Timestamp
	23, // 22: rewards.pinot.GetMerchantRewardsAggregatesRequest.to_created_at:type_name -> google.protobuf.Timestamp
	32, // 23: rewards.pinot.GetMerchantRewardsAggregatesRequest.action_type:type_name -> rewards.CollectedDataType
	24, // 24: rewards.pinot.GetMerchantRewardsAggregatesRequest.reward_type:type_name -> rewards.RewardType
	33, // 25: rewards.pinot.GetMerchantRewardsAggregatesResponse.status:type_name -> rpc.Status
	11, // 26: rewards.pinot.GetMerchantRewardsAggregatesResponse.merchant_reward_aggregates:type_name -> rewards.pinot.MerchantRewardAggregate
	8,  // 27: rewards.pinot.MerchantRewardAggregate.reward_aggregate:type_name -> rewards.pinot.RewardOptionAggregate
	13, // 28: rewards.pinot.GetActualizedRewardProjectionsAggregatesRequest.filters:type_name -> rewards.pinot.ProjectionFilters
	2,  // 29: rewards.pinot.GetActualizedRewardProjectionsAggregatesRequest.time_range:type_name -> rewards.pinot.TimeRangeFilter
	26, // 30: rewards.pinot.ProjectionFilters.account_tier:type_name -> tiering.external.Tier
	27, // 31: rewards.pinot.ProjectionFilters.payment_protocol:type_name -> order.payment.PaymentProtocol
	28, // 32: rewards.pinot.ProjectionFilters.order_workflow:type_name -> order.OrderWorkflow
	30, // 33: rewards.pinot.ProjectionFilters.reward_offer_types:type_name -> rewards.RewardOfferType
	32, // 34: rewards.pinot.ProjectionFilters.action_types:type_name -> rewards.CollectedDataType
	31, // 35: rewards.pinot.ProjectionFilters.include_l0_ontologies:type_name -> categorizer.L0
	31, // 36: rewards.pinot.ProjectionFilters.exclude_l0_ontologies:type_name -> categorizer.L0
	33, // 37: rewards.pinot.GetActualizedRewardProjectionsAggregatesResponse.status:type_name -> rpc.Status
	15, // 38: rewards.pinot.GetActualizedRewardProjectionsAggregatesResponse.reward_projections_aggregates:type_name -> rewards.pinot.ActualizedRewardProjectionsAggregate
	16, // 39: rewards.pinot.ActualizedRewardProjectionsAggregate.claimed_reward_projections:type_name -> rewards.pinot.RewardProjectionOption
	16, // 40: rewards.pinot.ActualizedRewardProjectionsAggregate.unclaimed_reward_projections:type_name -> rewards.pinot.RewardProjectionOption
	24, // 41: rewards.pinot.RewardProjectionOption.reward_type:type_name -> rewards.RewardType
	24, // 42: rewards.pinot.GetUnActualizedRewardProjectionsAggregatesRequest.reward_type:type_name -> rewards.RewardType
	2,  // 43: rewards.pinot.GetUnActualizedRewardProjectionsAggregatesRequest.time_range:type_name -> rewards.pinot.TimeRangeFilter
	13, // 44: rewards.pinot.GetUnActualizedRewardProjectionsAggregatesRequest.filters:type_name -> rewards.pinot.ProjectionFilters
	33, // 45: rewards.pinot.GetUnActualizedRewardProjectionsAggregatesResponse.status:type_name -> rpc.Status
	16, // 46: rewards.pinot.GetUnActualizedRewardProjectionsAggregatesResponse.reward_projections_aggregates:type_name -> rewards.pinot.RewardProjectionOption
	24, // 47: rewards.pinot.GetCustomUnActualizedRewardProjectionsAggregatesRequest.reward_type:type_name -> rewards.RewardType
	2,  // 48: rewards.pinot.GetCustomUnActualizedRewardProjectionsAggregatesRequest.time_range:type_name -> rewards.pinot.TimeRangeFilter
	21, // 49: rewards.pinot.GetCustomUnActualizedRewardProjectionsAggregatesRequest.projection_custom_query:type_name -> rewards.pinot.ProjectionCustomQuery
	33, // 50: rewards.pinot.GetCustomUnActualizedRewardProjectionsAggregatesResponse.status:type_name -> rpc.Status
	16, // 51: rewards.pinot.GetCustomUnActualizedRewardProjectionsAggregatesResponse.reward_projections_aggregates:type_name -> rewards.pinot.RewardProjectionOption
	1,  // 52: rewards.pinot.ProjectionCustomQuery.type:type_name -> rewards.pinot.ProjectionCustomQueryType
	22, // 53: rewards.pinot.ProjectionCustomQuery.tiering_monthly_projections_aggregate_metadata:type_name -> rewards.pinot.TieringMonthlyProjectionsAggregateMetadata
	32, // 54: rewards.pinot.TieringMonthlyProjectionsAggregateMetadata.action_type:type_name -> rewards.CollectedDataType
	30, // 55: rewards.pinot.TieringMonthlyProjectionsAggregateMetadata.offer_types:type_name -> rewards.RewardOfferType
	4,  // 56: rewards.pinot.RewardsAggregates.GetRewardsAggregates:input_type -> rewards.pinot.GetRewardsAggregatesRequest
	9,  // 57: rewards.pinot.RewardsAggregates.GetMerchantRewardsAggregates:input_type -> rewards.pinot.GetMerchantRewardsAggregatesRequest
	12, // 58: rewards.pinot.RewardsAggregates.GetActualizedRewardProjectionsAggregates:input_type -> rewards.pinot.GetActualizedRewardProjectionsAggregatesRequest
	17, // 59: rewards.pinot.RewardsAggregates.GetUnActualizedRewardProjectionsAggregates:input_type -> rewards.pinot.GetUnActualizedRewardProjectionsAggregatesRequest
	19, // 60: rewards.pinot.RewardsAggregates.GetCustomUnActualizedRewardProjectionsAggregates:input_type -> rewards.pinot.GetCustomUnActualizedRewardProjectionsAggregatesRequest
	7,  // 61: rewards.pinot.RewardsAggregates.GetRewardsAggregates:output_type -> rewards.pinot.GetRewardsAggregatesResponse
	10, // 62: rewards.pinot.RewardsAggregates.GetMerchantRewardsAggregates:output_type -> rewards.pinot.GetMerchantRewardsAggregatesResponse
	14, // 63: rewards.pinot.RewardsAggregates.GetActualizedRewardProjectionsAggregates:output_type -> rewards.pinot.GetActualizedRewardProjectionsAggregatesResponse
	18, // 64: rewards.pinot.RewardsAggregates.GetUnActualizedRewardProjectionsAggregates:output_type -> rewards.pinot.GetUnActualizedRewardProjectionsAggregatesResponse
	20, // 65: rewards.pinot.RewardsAggregates.GetCustomUnActualizedRewardProjectionsAggregates:output_type -> rewards.pinot.GetCustomUnActualizedRewardProjectionsAggregatesResponse
	61, // [61:66] is the sub-list for method output_type
	56, // [56:61] is the sub-list for method input_type
	56, // [56:56] is the sub-list for extension type_name
	56, // [56:56] is the sub-list for extension extendee
	0,  // [0:56] is the sub-list for field type_name
}

func init() { file_api_rewards_pinot_service_proto_init() }
func file_api_rewards_pinot_service_proto_init() {
	if File_api_rewards_pinot_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_rewards_pinot_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRangeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsAggregatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Filters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MiscellaneousFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRewardsAggregatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardOptionAggregate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMerchantRewardsAggregatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetMerchantRewardsAggregatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MerchantRewardAggregate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActualizedRewardProjectionsAggregatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectionFilters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetActualizedRewardProjectionsAggregatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActualizedRewardProjectionsAggregate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RewardProjectionOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUnActualizedRewardProjectionsAggregatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUnActualizedRewardProjectionsAggregatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomUnActualizedRewardProjectionsAggregatesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCustomUnActualizedRewardProjectionsAggregatesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProjectionCustomQuery); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_rewards_pinot_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TieringMonthlyProjectionsAggregateMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_rewards_pinot_service_proto_msgTypes[19].OneofWrappers = []interface{}{
		(*ProjectionCustomQuery_TieringMonthlyProjectionsAggregateMetadata)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_rewards_pinot_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_rewards_pinot_service_proto_goTypes,
		DependencyIndexes: file_api_rewards_pinot_service_proto_depIdxs,
		EnumInfos:         file_api_rewards_pinot_service_proto_enumTypes,
		MessageInfos:      file_api_rewards_pinot_service_proto_msgTypes,
	}.Build()
	File_api_rewards_pinot_service_proto = out.File
	file_api_rewards_pinot_service_proto_rawDesc = nil
	file_api_rewards_pinot_service_proto_goTypes = nil
	file_api_rewards_pinot_service_proto_depIdxs = nil
}
