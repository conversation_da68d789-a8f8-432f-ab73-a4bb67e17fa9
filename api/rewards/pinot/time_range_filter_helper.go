package pinot

import (
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"
)

// NewTimeRangeFilter builds a TimeRangeFilter protobuf message.
func NewTimeRangeFilter(timeRangeType TimeRangeType, fromTime, toTime *timestampPb.Timestamp) *TimeRangeFilter {
	return &TimeRangeFilter{
		Type: timeRangeType,
		Range: &TimeRange{
			From: fromTime,
			To:   toTime,
		},
	}
}

// ValidateAndFlatten validates and extracts the flattened out time range from TimeRangeFilter
func (trf *TimeRangeFilter) ValidateAndFlatten() (TimeRangeType, time.Time, time.Time, error) {
	// 1.Validate that the time range filter is not nil
	if trf == nil {
		return 0, time.Time{}, time.Time{}, status.Error(codes.InvalidArgument, "time range filter cannot be nil")
	}

	// 2. Determine the source of timestamps and the time type
	var (
		timeRangeType = trf.GetType()
		fromTsProto   = trf.GetRange().GetFrom()
		toTsProto     = trf.GetRange().GetTo()
	)

	// 3. Validate that the time range type is not UNSPECIFIED
	if timeRangeType == TimeRangeType_TIME_RANGE_TYPE_UNSPECIFIED {
		return 0, time.Time{}, time.Time{}, status.Error(codes.InvalidArgument, "time range type must not be UNSPECIFIED")
	}

	// 4. Consolidated validation for timestamps (regardless of source)
	if !fromTsProto.IsValid() || !toTsProto.IsValid() {
		return 0, time.Time{}, time.Time{}, status.Error(codes.InvalidArgument, "a valid time range with 'from' and 'to' timestamps is required")
	}

	// 5. Validate that the time range is not invalid
	if fromTsProto.AsTime().After(toTsProto.AsTime()) {
		return 0, time.Time{}, time.Time{}, status.Error(codes.InvalidArgument, "time range 'from' cannot be after 'to'")
	}

	// 6. Success - return the determined type and validated times
	return timeRangeType, fromTsProto.AsTime(), toTsProto.AsTime(), nil
}
