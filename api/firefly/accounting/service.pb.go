// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/firefly/accounting/service.proto

package accounting

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	categorizer "github.com/epifi/gamma/api/categorizer"
	dispute "github.com/epifi/gamma/api/firefly/accounting/dispute"
	enums "github.com/epifi/gamma/api/firefly/accounting/enums"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetNextDisputeQuestionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	QuestionCode string `protobuf:"bytes,1,opt,name=question_code,json=questionCode,proto3" json:"question_code,omitempty"`
	Answer       string `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer,omitempty"`
}

func (x *GetNextDisputeQuestionRequest) Reset() {
	*x = GetNextDisputeQuestionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextDisputeQuestionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextDisputeQuestionRequest) ProtoMessage() {}

func (x *GetNextDisputeQuestionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextDisputeQuestionRequest.ProtoReflect.Descriptor instead.
func (*GetNextDisputeQuestionRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetNextDisputeQuestionRequest) GetQuestionCode() string {
	if x != nil {
		return x.QuestionCode
	}
	return ""
}

func (x *GetNextDisputeQuestionRequest) GetAnswer() string {
	if x != nil {
		return x.Answer
	}
	return ""
}

type GetNextDisputeQuestionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	QuestionMeta *dispute.QuestionMeta `protobuf:"bytes,2,opt,name=question_meta,json=questionMeta,proto3" json:"question_meta,omitempty"`
}

func (x *GetNextDisputeQuestionResponse) Reset() {
	*x = GetNextDisputeQuestionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetNextDisputeQuestionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNextDisputeQuestionResponse) ProtoMessage() {}

func (x *GetNextDisputeQuestionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNextDisputeQuestionResponse.ProtoReflect.Descriptor instead.
func (*GetNextDisputeQuestionResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetNextDisputeQuestionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetNextDisputeQuestionResponse) GetQuestionMeta() *dispute.QuestionMeta {
	if x != nil {
		return x.QuestionMeta
	}
	return nil
}

type GetAllDisputesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetAllDisputesRequest) Reset() {
	*x = GetAllDisputesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllDisputesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllDisputesRequest) ProtoMessage() {}

func (x *GetAllDisputesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllDisputesRequest.ProtoReflect.Descriptor instead.
func (*GetAllDisputesRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetAllDisputesRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetAllDisputesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status               *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DisputedTransactions []*DisputedTransaction `protobuf:"bytes,2,rep,name=disputed_transactions,json=disputedTransactions,proto3" json:"disputed_transactions,omitempty"`
}

func (x *GetAllDisputesResponse) Reset() {
	*x = GetAllDisputesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllDisputesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllDisputesResponse) ProtoMessage() {}

func (x *GetAllDisputesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllDisputesResponse.ProtoReflect.Descriptor instead.
func (*GetAllDisputesResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetAllDisputesResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAllDisputesResponse) GetDisputedTransactions() []*DisputedTransaction {
	if x != nil {
		return x.DisputedTransactions
	}
	return nil
}

type GetTransactionsForATimeIntervalRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId  string                 `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RefId    string                 `protobuf:"bytes,2,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	FromTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=from_time,json=fromTime,proto3" json:"from_time,omitempty"`
	ToTime   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=to_time,json=toTime,proto3" json:"to_time,omitempty"`
	// flag to enable or disable frm decline txns to be returned
	IncludeFrmDeclineTxns bool `protobuf:"varint,5,opt,name=include_frm_decline_txns,json=includeFrmDeclineTxns,proto3" json:"include_frm_decline_txns,omitempty"`
}

func (x *GetTransactionsForATimeIntervalRequest) Reset() {
	*x = GetTransactionsForATimeIntervalRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionsForATimeIntervalRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionsForATimeIntervalRequest) ProtoMessage() {}

func (x *GetTransactionsForATimeIntervalRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionsForATimeIntervalRequest.ProtoReflect.Descriptor instead.
func (*GetTransactionsForATimeIntervalRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetTransactionsForATimeIntervalRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetTransactionsForATimeIntervalRequest) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *GetTransactionsForATimeIntervalRequest) GetFromTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromTime
	}
	return nil
}

func (x *GetTransactionsForATimeIntervalRequest) GetToTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ToTime
	}
	return nil
}

func (x *GetTransactionsForATimeIntervalRequest) GetIncludeFrmDeclineTxns() bool {
	if x != nil {
		return x.IncludeFrmDeclineTxns
	}
	return false
}

type GetTransactionsForATimeIntervalResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Transactions []*CardTransaction `protobuf:"bytes,2,rep,name=transactions,proto3" json:"transactions,omitempty"`
}

func (x *GetTransactionsForATimeIntervalResponse) Reset() {
	*x = GetTransactionsForATimeIntervalResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionsForATimeIntervalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionsForATimeIntervalResponse) ProtoMessage() {}

func (x *GetTransactionsForATimeIntervalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionsForATimeIntervalResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionsForATimeIntervalResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetTransactionsForATimeIntervalResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTransactionsForATimeIntervalResponse) GetTransactions() []*CardTransaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type CreateAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// this is a unique ref number passed by client to create the account
	ReferenceId string `protobuf:"bytes,2,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// total credit limit
	TotalLimit *money.Money `protobuf:"bytes,3,opt,name=total_limit,json=totalLimit,proto3" json:"total_limit,omitempty"`
	// cardProgram of the account : optional field
	CardProgram *typesv2.CardProgram `protobuf:"bytes,4,opt,name=card_program,json=cardProgram,proto3" json:"card_program,omitempty"`
	// collateral details of the account: optional field
	CollateralDetails *CollateralDetails `protobuf:"bytes,5,opt,name=collateral_details,json=collateralDetails,proto3" json:"collateral_details,omitempty"`
}

func (x *CreateAccountRequest) Reset() {
	*x = CreateAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountRequest) ProtoMessage() {}

func (x *CreateAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{6}
}

func (x *CreateAccountRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateAccountRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *CreateAccountRequest) GetTotalLimit() *money.Money {
	if x != nil {
		return x.TotalLimit
	}
	return nil
}

func (x *CreateAccountRequest) GetCardProgram() *typesv2.CardProgram {
	if x != nil {
		return x.CardProgram
	}
	return nil
}

func (x *CreateAccountRequest) GetCollateralDetails() *CollateralDetails {
	if x != nil {
		return x.CollateralDetails
	}
	return nil
}

type CreateAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Account *CreditAccount `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *CreateAccountResponse) Reset() {
	*x = CreateAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountResponse) ProtoMessage() {}

func (x *CreateAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountResponse.ProtoReflect.Descriptor instead.
func (*CreateAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{7}
}

func (x *CreateAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateAccountResponse) GetAccount() *CreditAccount {
	if x != nil {
		return x.Account
	}
	return nil
}

type GetAccountsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to GetBy:
	//
	//	*GetAccountsRequest_ActorId
	GetBy isGetAccountsRequest_GetBy `protobuf_oneof:"GetBy"`
}

func (x *GetAccountsRequest) Reset() {
	*x = GetAccountsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountsRequest) ProtoMessage() {}

func (x *GetAccountsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountsRequest.ProtoReflect.Descriptor instead.
func (*GetAccountsRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{8}
}

func (m *GetAccountsRequest) GetGetBy() isGetAccountsRequest_GetBy {
	if m != nil {
		return m.GetBy
	}
	return nil
}

func (x *GetAccountsRequest) GetActorId() string {
	if x, ok := x.GetGetBy().(*GetAccountsRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

type isGetAccountsRequest_GetBy interface {
	isGetAccountsRequest_GetBy()
}

type GetAccountsRequest_ActorId struct {
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*GetAccountsRequest_ActorId) isGetAccountsRequest_GetBy() {}

type GetAccountsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status      `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Accounts []*CreditAccount `protobuf:"bytes,2,rep,name=accounts,proto3" json:"accounts,omitempty"`
}

func (x *GetAccountsResponse) Reset() {
	*x = GetAccountsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountsResponse) ProtoMessage() {}

func (x *GetAccountsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountsResponse.ProtoReflect.Descriptor instead.
func (*GetAccountsResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetAccountsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAccountsResponse) GetAccounts() []*CreditAccount {
	if x != nil {
		return x.Accounts
	}
	return nil
}

type GetAccountRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to GetBy:
	//
	//	*GetAccountRequest_ByActorIdAndRefId
	//	*GetAccountRequest_AccountId
	//	*GetAccountRequest_ReferenceId
	GetBy isGetAccountRequest_GetBy `protobuf_oneof:"GetBy"`
}

func (x *GetAccountRequest) Reset() {
	*x = GetAccountRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountRequest) ProtoMessage() {}

func (x *GetAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountRequest.ProtoReflect.Descriptor instead.
func (*GetAccountRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{10}
}

func (m *GetAccountRequest) GetGetBy() isGetAccountRequest_GetBy {
	if m != nil {
		return m.GetBy
	}
	return nil
}

func (x *GetAccountRequest) GetByActorIdAndRefId() *GetAccountRequest_ActorIdAndRefId {
	if x, ok := x.GetGetBy().(*GetAccountRequest_ByActorIdAndRefId); ok {
		return x.ByActorIdAndRefId
	}
	return nil
}

func (x *GetAccountRequest) GetAccountId() string {
	if x, ok := x.GetGetBy().(*GetAccountRequest_AccountId); ok {
		return x.AccountId
	}
	return ""
}

func (x *GetAccountRequest) GetReferenceId() string {
	if x, ok := x.GetGetBy().(*GetAccountRequest_ReferenceId); ok {
		return x.ReferenceId
	}
	return ""
}

type isGetAccountRequest_GetBy interface {
	isGetAccountRequest_GetBy()
}

type GetAccountRequest_ByActorIdAndRefId struct {
	ByActorIdAndRefId *GetAccountRequest_ActorIdAndRefId `protobuf:"bytes,1,opt,name=by_actor_id_and_ref_id,json=byActorIdAndRefId,proto3,oneof"`
}

type GetAccountRequest_AccountId struct {
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3,oneof"`
}

type GetAccountRequest_ReferenceId struct {
	ReferenceId string `protobuf:"bytes,3,opt,name=reference_id,json=referenceId,proto3,oneof"`
}

func (*GetAccountRequest_ByActorIdAndRefId) isGetAccountRequest_GetBy() {}

func (*GetAccountRequest_AccountId) isGetAccountRequest_GetBy() {}

func (*GetAccountRequest_ReferenceId) isGetAccountRequest_GetBy() {}

type GetAccountResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Account *CreditAccount `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"`
}

func (x *GetAccountResponse) Reset() {
	*x = GetAccountResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountResponse) ProtoMessage() {}

func (x *GetAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountResponse.ProtoReflect.Descriptor instead.
func (*GetAccountResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{11}
}

func (x *GetAccountResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAccountResponse) GetAccount() *CreditAccount {
	if x != nil {
		return x.Account
	}
	return nil
}

type GetTransactionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to GetBy:
	//
	//	*GetTransactionsRequest_TransactionId
	//	*GetTransactionsRequest_AccountId
	//	*GetTransactionsRequest_DedupeId
	//	*GetTransactionsRequest_BatchExternalTxnIds
	//	*GetTransactionsRequest_BatchTxnIds
	//	*GetTransactionsRequest_VendorExternalTransactionId
	//	*GetTransactionsRequest_BatchDedupeIdGenerationParameter
	//	*GetTransactionsRequest_AccountIdAndCategories
	GetBy isGetTransactionsRequest_GetBy `protobuf_oneof:"GetBy"`
}

func (x *GetTransactionsRequest) Reset() {
	*x = GetTransactionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionsRequest) ProtoMessage() {}

func (x *GetTransactionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionsRequest.ProtoReflect.Descriptor instead.
func (*GetTransactionsRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{12}
}

func (m *GetTransactionsRequest) GetGetBy() isGetTransactionsRequest_GetBy {
	if m != nil {
		return m.GetBy
	}
	return nil
}

func (x *GetTransactionsRequest) GetTransactionId() string {
	if x, ok := x.GetGetBy().(*GetTransactionsRequest_TransactionId); ok {
		return x.TransactionId
	}
	return ""
}

func (x *GetTransactionsRequest) GetAccountId() string {
	if x, ok := x.GetGetBy().(*GetTransactionsRequest_AccountId); ok {
		return x.AccountId
	}
	return ""
}

func (x *GetTransactionsRequest) GetDedupeId() string {
	if x, ok := x.GetGetBy().(*GetTransactionsRequest_DedupeId); ok {
		return x.DedupeId
	}
	return ""
}

func (x *GetTransactionsRequest) GetBatchExternalTxnIds() *BatchExternalTxnIds {
	if x, ok := x.GetGetBy().(*GetTransactionsRequest_BatchExternalTxnIds); ok {
		return x.BatchExternalTxnIds
	}
	return nil
}

func (x *GetTransactionsRequest) GetBatchTxnIds() *BatchTxnIds {
	if x, ok := x.GetGetBy().(*GetTransactionsRequest_BatchTxnIds); ok {
		return x.BatchTxnIds
	}
	return nil
}

func (x *GetTransactionsRequest) GetVendorExternalTransactionId() string {
	if x, ok := x.GetGetBy().(*GetTransactionsRequest_VendorExternalTransactionId); ok {
		return x.VendorExternalTransactionId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/firefly/accounting/service.proto.
func (x *GetTransactionsRequest) GetBatchDedupeIdGenerationParameter() *BatchDedupeIdGenerationParameter {
	if x, ok := x.GetGetBy().(*GetTransactionsRequest_BatchDedupeIdGenerationParameter); ok {
		return x.BatchDedupeIdGenerationParameter
	}
	return nil
}

func (x *GetTransactionsRequest) GetAccountIdAndCategories() *AccountIdAndCategories {
	if x, ok := x.GetGetBy().(*GetTransactionsRequest_AccountIdAndCategories); ok {
		return x.AccountIdAndCategories
	}
	return nil
}

type isGetTransactionsRequest_GetBy interface {
	isGetTransactionsRequest_GetBy()
}

type GetTransactionsRequest_TransactionId struct {
	TransactionId string `protobuf:"bytes,1,opt,name=transaction_id,json=transactionId,proto3,oneof"`
}

type GetTransactionsRequest_AccountId struct {
	AccountId string `protobuf:"bytes,2,opt,name=account_id,json=accountId,proto3,oneof"`
}

type GetTransactionsRequest_DedupeId struct {
	DedupeId string `protobuf:"bytes,3,opt,name=dedupe_id,json=dedupeId,proto3,oneof"`
}

type GetTransactionsRequest_BatchExternalTxnIds struct {
	BatchExternalTxnIds *BatchExternalTxnIds `protobuf:"bytes,4,opt,name=batch_external_txn_ids,json=batchExternalTxnIds,proto3,oneof"`
}

type GetTransactionsRequest_BatchTxnIds struct {
	BatchTxnIds *BatchTxnIds `protobuf:"bytes,5,opt,name=batch_txn_ids,json=batchTxnIds,proto3,oneof"`
}

type GetTransactionsRequest_VendorExternalTransactionId struct {
	VendorExternalTransactionId string `protobuf:"bytes,6,opt,name=vendor_external_transaction_id,json=vendorExternalTransactionId,proto3,oneof"`
}

type GetTransactionsRequest_BatchDedupeIdGenerationParameter struct {
	// Deprecated: Marked as deprecated in api/firefly/accounting/service.proto.
	BatchDedupeIdGenerationParameter *BatchDedupeIdGenerationParameter `protobuf:"bytes,7,opt,name=batch_dedupe_id_generation_parameter,json=batchDedupeIdGenerationParameter,proto3,oneof"`
}

type GetTransactionsRequest_AccountIdAndCategories struct {
	AccountIdAndCategories *AccountIdAndCategories `protobuf:"bytes,8,opt,name=account_id_and_categories,json=accountIdAndCategories,proto3,oneof"`
}

func (*GetTransactionsRequest_TransactionId) isGetTransactionsRequest_GetBy() {}

func (*GetTransactionsRequest_AccountId) isGetTransactionsRequest_GetBy() {}

func (*GetTransactionsRequest_DedupeId) isGetTransactionsRequest_GetBy() {}

func (*GetTransactionsRequest_BatchExternalTxnIds) isGetTransactionsRequest_GetBy() {}

func (*GetTransactionsRequest_BatchTxnIds) isGetTransactionsRequest_GetBy() {}

func (*GetTransactionsRequest_VendorExternalTransactionId) isGetTransactionsRequest_GetBy() {}

func (*GetTransactionsRequest_BatchDedupeIdGenerationParameter) isGetTransactionsRequest_GetBy() {}

func (*GetTransactionsRequest_AccountIdAndCategories) isGetTransactionsRequest_GetBy() {}

// message for getting transactions by account ID and category.
type AccountIdAndCategories struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// account ID for filtering transactions.
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// Transaction categories for filtering transactions.
	TransactionCategories []enums.TransactionCategory `protobuf:"varint,2,rep,packed,name=transaction_categories,json=transactionCategories,proto3,enum=firefly.accounting.enums.TransactionCategory" json:"transaction_categories,omitempty"`
}

func (x *AccountIdAndCategories) Reset() {
	*x = AccountIdAndCategories{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AccountIdAndCategories) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountIdAndCategories) ProtoMessage() {}

func (x *AccountIdAndCategories) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountIdAndCategories.ProtoReflect.Descriptor instead.
func (*AccountIdAndCategories) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{13}
}

func (x *AccountIdAndCategories) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *AccountIdAndCategories) GetTransactionCategories() []enums.TransactionCategory {
	if x != nil {
		return x.TransactionCategories
	}
	return nil
}

type BatchExternalTxnIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExternalTxnIds []string `protobuf:"bytes,1,rep,name=external_txn_ids,json=externalTxnIds,proto3" json:"external_txn_ids,omitempty"`
}

func (x *BatchExternalTxnIds) Reset() {
	*x = BatchExternalTxnIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchExternalTxnIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchExternalTxnIds) ProtoMessage() {}

func (x *BatchExternalTxnIds) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchExternalTxnIds.ProtoReflect.Descriptor instead.
func (*BatchExternalTxnIds) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{14}
}

func (x *BatchExternalTxnIds) GetExternalTxnIds() []string {
	if x != nil {
		return x.ExternalTxnIds
	}
	return nil
}

type BatchTxnIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnIds []string `protobuf:"bytes,1,rep,name=txn_ids,json=txnIds,proto3" json:"txn_ids,omitempty"`
}

func (x *BatchTxnIds) Reset() {
	*x = BatchTxnIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchTxnIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchTxnIds) ProtoMessage() {}

func (x *BatchTxnIds) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchTxnIds.ProtoReflect.Descriptor instead.
func (*BatchTxnIds) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{15}
}

func (x *BatchTxnIds) GetTxnIds() []string {
	if x != nil {
		return x.TxnIds
	}
	return nil
}

type BatchDedupeIdGenerationParameter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DedupeIdGenerationParameters []*DedupeId `protobuf:"bytes,1,rep,name=dedupe_id_generation_parameters,json=dedupeIdGenerationParameters,proto3" json:"dedupe_id_generation_parameters,omitempty"`
}

func (x *BatchDedupeIdGenerationParameter) Reset() {
	*x = BatchDedupeIdGenerationParameter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDedupeIdGenerationParameter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDedupeIdGenerationParameter) ProtoMessage() {}

func (x *BatchDedupeIdGenerationParameter) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDedupeIdGenerationParameter.ProtoReflect.Descriptor instead.
func (*BatchDedupeIdGenerationParameter) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{16}
}

func (x *BatchDedupeIdGenerationParameter) GetDedupeIdGenerationParameters() []*DedupeId {
	if x != nil {
		return x.DedupeIdGenerationParameters
	}
	return nil
}

type GetTransactionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Transactions []*CardTransaction `protobuf:"bytes,2,rep,name=transactions,proto3" json:"transactions,omitempty"`
}

func (x *GetTransactionsResponse) Reset() {
	*x = GetTransactionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionsResponse) ProtoMessage() {}

func (x *GetTransactionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionsResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionsResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{17}
}

func (x *GetTransactionsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTransactionsResponse) GetTransactions() []*CardTransaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

type PostTransactionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountId   string           `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	Transaction *CardTransaction `protobuf:"bytes,2,opt,name=transaction,proto3" json:"transaction,omitempty"`
}

func (x *PostTransactionRequest) Reset() {
	*x = PostTransactionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostTransactionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostTransactionRequest) ProtoMessage() {}

func (x *PostTransactionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostTransactionRequest.ProtoReflect.Descriptor instead.
func (*PostTransactionRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{18}
}

func (x *PostTransactionRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *PostTransactionRequest) GetTransaction() *CardTransaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

type PostTransactionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *PostTransactionResponse) Reset() {
	*x = PostTransactionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostTransactionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostTransactionResponse) ProtoMessage() {}

func (x *PostTransactionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostTransactionResponse.ProtoReflect.Descriptor instead.
func (*PostTransactionResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{19}
}

func (x *PostTransactionResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type FetchTxnReceiptRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnId string `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
}

func (x *FetchTxnReceiptRequest) Reset() {
	*x = FetchTxnReceiptRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchTxnReceiptRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchTxnReceiptRequest) ProtoMessage() {}

func (x *FetchTxnReceiptRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchTxnReceiptRequest.ProtoReflect.Descriptor instead.
func (*FetchTxnReceiptRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{20}
}

func (x *FetchTxnReceiptRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

type FetchTxnReceiptResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// txn id generated on Fi's side as a parallel to the external id
	// generated on the vendor's side
	ExternalTxnId string `protobuf:"bytes,2,opt,name=external_txn_id,json=externalTxnId,proto3" json:"external_txn_id,omitempty"`
	// url for the image of other party in the txn
	IconUrl string `protobuf:"bytes,3,opt,name=icon_url,json=iconUrl,proto3" json:"icon_url,omitempty"`
	// description of the transaction.
	// could be something automatically generated or user entered
	Remarks string `protobuf:"bytes,4,opt,name=remarks,proto3" json:"remarks,omitempty"`
	// amount being transacted in the txn
	Amount *money.Money `protobuf:"bytes,5,opt,name=amount,proto3" json:"amount,omitempty"`
	// formatted string containing the name and the PI display id
	// of the from party
	FromName string `protobuf:"bytes,6,opt,name=from_name,json=fromName,proto3" json:"from_name,omitempty"`
	// formatted string containing the name and the PI display id
	// of the to party
	ToName string `protobuf:"bytes,7,opt,name=to_name,json=toName,proto3" json:"to_name,omitempty"`
	// terminal status of the txn
	TxnStatus enums.TransactionStatus `protobuf:"varint,8,opt,name=txn_status,json=txnStatus,proto3,enum=firefly.accounting.enums.TransactionStatus" json:"txn_status,omitempty"`
	// direction of transfer of the amount .
	// could be Credit or Debit
	TxnType enums.TransactionType `protobuf:"varint,9,opt,name=txn_type,json=txnType,proto3,enum=firefly.accounting.enums.TransactionType" json:"txn_type,omitempty"`
	// mode of the txn, could be IMPS, ECOM, etc.
	TxnCategory enums.TransactionCategory `protobuf:"varint,10,opt,name=txn_category,json=txnCategory,proto3,enum=firefly.accounting.enums.TransactionCategory" json:"txn_category,omitempty"`
	// time of the txn
	TxnTime    *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=txn_time,json=txnTime,proto3" json:"txn_time,omitempty"`
	PartnerUrl string                 `protobuf:"bytes,12,opt,name=partner_url,json=partnerUrl,proto3" json:"partner_url,omitempty"`
	// category defining for what the txn has been done
	// can be food, entertainment, etc.
	TransactionDisplayCategories []categorizer.DisplayCategory `protobuf:"varint,13,rep,packed,name=transaction_display_categories,json=transactionDisplayCategories,proto3,enum=categorizer.DisplayCategory" json:"transaction_display_categories,omitempty"`
	OtherPartyName               *common.Name                  `protobuf:"bytes,14,opt,name=other_party_name,json=otherPartyName,proto3" json:"other_party_name,omitempty"`
	ReceiptAdditionalDetails     *ReceiptAdditionalDetails     `protobuf:"bytes,15,opt,name=receipt_additional_details,json=receiptAdditionalDetails,proto3" json:"receipt_additional_details,omitempty"`
	VendorExternalTxnId          string                        `protobuf:"bytes,16,opt,name=vendor_external_txn_id,json=vendorExternalTxnId,proto3" json:"vendor_external_txn_id,omitempty"`
}

func (x *FetchTxnReceiptResponse) Reset() {
	*x = FetchTxnReceiptResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchTxnReceiptResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchTxnReceiptResponse) ProtoMessage() {}

func (x *FetchTxnReceiptResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchTxnReceiptResponse.ProtoReflect.Descriptor instead.
func (*FetchTxnReceiptResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{21}
}

func (x *FetchTxnReceiptResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *FetchTxnReceiptResponse) GetExternalTxnId() string {
	if x != nil {
		return x.ExternalTxnId
	}
	return ""
}

func (x *FetchTxnReceiptResponse) GetIconUrl() string {
	if x != nil {
		return x.IconUrl
	}
	return ""
}

func (x *FetchTxnReceiptResponse) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *FetchTxnReceiptResponse) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *FetchTxnReceiptResponse) GetFromName() string {
	if x != nil {
		return x.FromName
	}
	return ""
}

func (x *FetchTxnReceiptResponse) GetToName() string {
	if x != nil {
		return x.ToName
	}
	return ""
}

func (x *FetchTxnReceiptResponse) GetTxnStatus() enums.TransactionStatus {
	if x != nil {
		return x.TxnStatus
	}
	return enums.TransactionStatus(0)
}

func (x *FetchTxnReceiptResponse) GetTxnType() enums.TransactionType {
	if x != nil {
		return x.TxnType
	}
	return enums.TransactionType(0)
}

func (x *FetchTxnReceiptResponse) GetTxnCategory() enums.TransactionCategory {
	if x != nil {
		return x.TxnCategory
	}
	return enums.TransactionCategory(0)
}

func (x *FetchTxnReceiptResponse) GetTxnTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TxnTime
	}
	return nil
}

func (x *FetchTxnReceiptResponse) GetPartnerUrl() string {
	if x != nil {
		return x.PartnerUrl
	}
	return ""
}

func (x *FetchTxnReceiptResponse) GetTransactionDisplayCategories() []categorizer.DisplayCategory {
	if x != nil {
		return x.TransactionDisplayCategories
	}
	return nil
}

func (x *FetchTxnReceiptResponse) GetOtherPartyName() *common.Name {
	if x != nil {
		return x.OtherPartyName
	}
	return nil
}

func (x *FetchTxnReceiptResponse) GetReceiptAdditionalDetails() *ReceiptAdditionalDetails {
	if x != nil {
		return x.ReceiptAdditionalDetails
	}
	return nil
}

func (x *FetchTxnReceiptResponse) GetVendorExternalTxnId() string {
	if x != nil {
		return x.VendorExternalTxnId
	}
	return ""
}

// Additional details to shown for a txn on receipt
// For example in case of forex fee or a service tax fee we need to add information regarding the original/parent txn
// for which the fee is applied
type ReceiptAdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*ReceiptAdditionalDetails_FeesAdditionalDetails
	Details isReceiptAdditionalDetails_Details `protobuf_oneof:"Details"`
}

func (x *ReceiptAdditionalDetails) Reset() {
	*x = ReceiptAdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReceiptAdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReceiptAdditionalDetails) ProtoMessage() {}

func (x *ReceiptAdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReceiptAdditionalDetails.ProtoReflect.Descriptor instead.
func (*ReceiptAdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{22}
}

func (m *ReceiptAdditionalDetails) GetDetails() isReceiptAdditionalDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *ReceiptAdditionalDetails) GetFeesAdditionalDetails() *FeesAdditionalDetails {
	if x, ok := x.GetDetails().(*ReceiptAdditionalDetails_FeesAdditionalDetails); ok {
		return x.FeesAdditionalDetails
	}
	return nil
}

type isReceiptAdditionalDetails_Details interface {
	isReceiptAdditionalDetails_Details()
}

type ReceiptAdditionalDetails_FeesAdditionalDetails struct {
	FeesAdditionalDetails *FeesAdditionalDetails `protobuf:"bytes,1,opt,name=fees_additional_details,json=feesAdditionalDetails,proto3,oneof"`
}

func (*ReceiptAdditionalDetails_FeesAdditionalDetails) isReceiptAdditionalDetails_Details() {}

type FeesAdditionalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Description string `protobuf:"bytes,1,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *FeesAdditionalDetails) Reset() {
	*x = FeesAdditionalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FeesAdditionalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FeesAdditionalDetails) ProtoMessage() {}

func (x *FeesAdditionalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FeesAdditionalDetails.ProtoReflect.Descriptor instead.
func (*FeesAdditionalDetails) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{23}
}

func (x *FeesAdditionalDetails) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type GetTransactionWithAdditionalInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to GetBy:
	//
	//	*GetTransactionWithAdditionalInfoRequest_TransactionId
	//	*GetTransactionWithAdditionalInfoRequest_VendorExternalTransactionId
	GetBy isGetTransactionWithAdditionalInfoRequest_GetBy `protobuf_oneof:"GetBy"`
}

func (x *GetTransactionWithAdditionalInfoRequest) Reset() {
	*x = GetTransactionWithAdditionalInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionWithAdditionalInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionWithAdditionalInfoRequest) ProtoMessage() {}

func (x *GetTransactionWithAdditionalInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionWithAdditionalInfoRequest.ProtoReflect.Descriptor instead.
func (*GetTransactionWithAdditionalInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{24}
}

func (m *GetTransactionWithAdditionalInfoRequest) GetGetBy() isGetTransactionWithAdditionalInfoRequest_GetBy {
	if m != nil {
		return m.GetBy
	}
	return nil
}

func (x *GetTransactionWithAdditionalInfoRequest) GetTransactionId() string {
	if x, ok := x.GetGetBy().(*GetTransactionWithAdditionalInfoRequest_TransactionId); ok {
		return x.TransactionId
	}
	return ""
}

func (x *GetTransactionWithAdditionalInfoRequest) GetVendorExternalTransactionId() string {
	if x, ok := x.GetGetBy().(*GetTransactionWithAdditionalInfoRequest_VendorExternalTransactionId); ok {
		return x.VendorExternalTransactionId
	}
	return ""
}

type isGetTransactionWithAdditionalInfoRequest_GetBy interface {
	isGetTransactionWithAdditionalInfoRequest_GetBy()
}

type GetTransactionWithAdditionalInfoRequest_TransactionId struct {
	TransactionId string `protobuf:"bytes,1,opt,name=transaction_id,json=transactionId,proto3,oneof"`
}

type GetTransactionWithAdditionalInfoRequest_VendorExternalTransactionId struct {
	VendorExternalTransactionId string `protobuf:"bytes,2,opt,name=vendor_external_transaction_id,json=vendorExternalTransactionId,proto3,oneof"`
}

func (*GetTransactionWithAdditionalInfoRequest_TransactionId) isGetTransactionWithAdditionalInfoRequest_GetBy() {
}

func (*GetTransactionWithAdditionalInfoRequest_VendorExternalTransactionId) isGetTransactionWithAdditionalInfoRequest_GetBy() {
}

type GetTransactionWithAdditionalInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                            *rpc.Status                        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CardTransactionWithAdditionalInfo *CardTransactionWithAdditionalInfo `protobuf:"bytes,2,opt,name=card_transaction_with_additional_info,json=cardTransactionWithAdditionalInfo,proto3" json:"card_transaction_with_additional_info,omitempty"`
}

func (x *GetTransactionWithAdditionalInfoResponse) Reset() {
	*x = GetTransactionWithAdditionalInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionWithAdditionalInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionWithAdditionalInfoResponse) ProtoMessage() {}

func (x *GetTransactionWithAdditionalInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionWithAdditionalInfoResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionWithAdditionalInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{25}
}

func (x *GetTransactionWithAdditionalInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTransactionWithAdditionalInfoResponse) GetCardTransactionWithAdditionalInfo() *CardTransactionWithAdditionalInfo {
	if x != nil {
		return x.CardTransactionWithAdditionalInfo
	}
	return nil
}

type GetPaginatedTxnsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetPaginatedTxnsRequest_AccountId
	//	*GetPaginatedTxnsRequest_ActorId
	Identifier isGetPaginatedTxnsRequest_Identifier `protobuf_oneof:"Identifier"`
	// timestamp starting from which transaction records are scanned.
	// NOTE- the records are returned INCLUSIVE of the timestamp.
	StartTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	// Page size determines the upper bound on the number of records
	// returned in a particular response.
	// Page size must be in the range [10, 40]
	// minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// An offset lets the caller control the number of records that needs to be skipped
	// starting from start timestamp.
	// e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
	// set to 5 then first 5 records from the qualifying set are removed.
	// deprecated. use order offset and aaTxnOffset
	Offset int32 `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	// The sequence of the result returned be based on the boolean flag descending.
	// i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
	// If marked false ASCENDING ordered results are returned from the given start timestamp.
	Descending bool `protobuf:"varint,6,opt,name=descending,proto3" json:"descending,omitempty"`
	// transaction status filter
	// if not set we will return txns for all states
	Statuses []enums.TransactionStatus `protobuf:"varint,7,rep,packed,name=statuses,proto3,enum=firefly.accounting.enums.TransactionStatus" json:"statuses,omitempty"`
	// field masks for fetching particular fields
	// we will return the complete object if this is empty
	CardTransactionFieldMasks []enums.CardTransactionFieldMask `protobuf:"varint,8,rep,packed,name=card_transaction_field_masks,json=cardTransactionFieldMasks,proto3,enum=firefly.accounting.enums.CardTransactionFieldMask" json:"card_transaction_field_masks,omitempty"`
	// field masks for fetching particular fields
	// we will return the complete object if this is empty
	TransactionAdditionalInfoFieldMasks []enums.TransactionAdditionalInfoFieldMask `protobuf:"varint,9,rep,packed,name=transaction_additional_info_field_masks,json=transactionAdditionalInfoFieldMasks,proto3,enum=firefly.accounting.enums.TransactionAdditionalInfoFieldMask" json:"transaction_additional_info_field_masks,omitempty"`
	// flag to enable or disable frm decline txns to be returned
	IncludeFrmDeclineTxns bool `protobuf:"varint,10,opt,name=include_frm_decline_txns,json=includeFrmDeclineTxns,proto3" json:"include_frm_decline_txns,omitempty"`
}

func (x *GetPaginatedTxnsRequest) Reset() {
	*x = GetPaginatedTxnsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaginatedTxnsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaginatedTxnsRequest) ProtoMessage() {}

func (x *GetPaginatedTxnsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaginatedTxnsRequest.ProtoReflect.Descriptor instead.
func (*GetPaginatedTxnsRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{26}
}

func (m *GetPaginatedTxnsRequest) GetIdentifier() isGetPaginatedTxnsRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetPaginatedTxnsRequest) GetAccountId() string {
	if x, ok := x.GetIdentifier().(*GetPaginatedTxnsRequest_AccountId); ok {
		return x.AccountId
	}
	return ""
}

func (x *GetPaginatedTxnsRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetPaginatedTxnsRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *GetPaginatedTxnsRequest) GetStartTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTimestamp
	}
	return nil
}

func (x *GetPaginatedTxnsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetPaginatedTxnsRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *GetPaginatedTxnsRequest) GetDescending() bool {
	if x != nil {
		return x.Descending
	}
	return false
}

func (x *GetPaginatedTxnsRequest) GetStatuses() []enums.TransactionStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *GetPaginatedTxnsRequest) GetCardTransactionFieldMasks() []enums.CardTransactionFieldMask {
	if x != nil {
		return x.CardTransactionFieldMasks
	}
	return nil
}

func (x *GetPaginatedTxnsRequest) GetTransactionAdditionalInfoFieldMasks() []enums.TransactionAdditionalInfoFieldMask {
	if x != nil {
		return x.TransactionAdditionalInfoFieldMasks
	}
	return nil
}

func (x *GetPaginatedTxnsRequest) GetIncludeFrmDeclineTxns() bool {
	if x != nil {
		return x.IncludeFrmDeclineTxns
	}
	return false
}

type isGetPaginatedTxnsRequest_Identifier interface {
	isGetPaginatedTxnsRequest_Identifier()
}

type GetPaginatedTxnsRequest_AccountId struct {
	// account for which transactions need to be fetched
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3,oneof"`
}

type GetPaginatedTxnsRequest_ActorId struct {
	// actor for which cc transactions need to be fetched
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*GetPaginatedTxnsRequest_AccountId) isGetPaginatedTxnsRequest_Identifier() {}

func (*GetPaginatedTxnsRequest_ActorId) isGetPaginatedTxnsRequest_Identifier() {}

type GetPaginatedTxnsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                            *rpc.Status                          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TransactionWithAdditionalInfoList []*CardTransactionWithAdditionalInfo `protobuf:"bytes,2,rep,name=transaction_with_additional_info_list,json=transactionWithAdditionalInfoList,proto3" json:"transaction_with_additional_info_list,omitempty"`
}

func (x *GetPaginatedTxnsResponse) Reset() {
	*x = GetPaginatedTxnsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaginatedTxnsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaginatedTxnsResponse) ProtoMessage() {}

func (x *GetPaginatedTxnsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaginatedTxnsResponse.ProtoReflect.Descriptor instead.
func (*GetPaginatedTxnsResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetPaginatedTxnsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPaginatedTxnsResponse) GetTransactionWithAdditionalInfoList() []*CardTransactionWithAdditionalInfo {
	if x != nil {
		return x.TransactionWithAdditionalInfoList
	}
	return nil
}

// Card Transaction with additional info, this will have the raw transaction details along with the enriched
// transaction data
type CardTransactionWithAdditionalInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Transaction    *CardTransaction           `protobuf:"bytes,1,opt,name=transaction,proto3" json:"transaction,omitempty"`
	AdditionalInfo *TransactionAdditionalInfo `protobuf:"bytes,2,opt,name=additional_info,json=additionalInfo,proto3" json:"additional_info,omitempty"`
}

func (x *CardTransactionWithAdditionalInfo) Reset() {
	*x = CardTransactionWithAdditionalInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CardTransactionWithAdditionalInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CardTransactionWithAdditionalInfo) ProtoMessage() {}

func (x *CardTransactionWithAdditionalInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CardTransactionWithAdditionalInfo.ProtoReflect.Descriptor instead.
func (*CardTransactionWithAdditionalInfo) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{28}
}

func (x *CardTransactionWithAdditionalInfo) GetTransaction() *CardTransaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *CardTransactionWithAdditionalInfo) GetAdditionalInfo() *TransactionAdditionalInfo {
	if x != nil {
		return x.AdditionalInfo
	}
	return nil
}

type GetPaginatedCreditCardTxnViewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetPaginatedCreditCardTxnViewRequest_AccountId
	//	*GetPaginatedCreditCardTxnViewRequest_ActorId
	Identifier isGetPaginatedCreditCardTxnViewRequest_Identifier `protobuf_oneof:"Identifier"`
	// timestamp starting from which transaction records are scanned.
	// NOTE- the records are returned INCLUSIVE of the timestamp.
	StartTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	// Page size determines the upper bound on the number of records
	// returned in a particular response.
	// Page size must be in the range [10, 40]
	// minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// An offset lets the caller control the number of records that needs to be skipped
	// starting from start timestamp.
	// e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
	// set to 5 then first 5 records from the qualifying set are removed.
	// deprecated. use order offset and aaTxnOffset
	Offset int32 `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	// The sequence of the result returned be based on the boolean flag descending.
	// i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
	// If marked false ASCENDING ordered results are returned from the given start timestamp.
	Descending bool `protobuf:"varint,6,opt,name=descending,proto3" json:"descending,omitempty"`
	// transaction status filter
	// if not set we will return txns for all states
	Statuses []enums.TransactionStatus `protobuf:"varint,7,rep,packed,name=statuses,proto3,enum=firefly.accounting.enums.TransactionStatus" json:"statuses,omitempty"`
}

func (x *GetPaginatedCreditCardTxnViewRequest) Reset() {
	*x = GetPaginatedCreditCardTxnViewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaginatedCreditCardTxnViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaginatedCreditCardTxnViewRequest) ProtoMessage() {}

func (x *GetPaginatedCreditCardTxnViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaginatedCreditCardTxnViewRequest.ProtoReflect.Descriptor instead.
func (*GetPaginatedCreditCardTxnViewRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{29}
}

func (m *GetPaginatedCreditCardTxnViewRequest) GetIdentifier() isGetPaginatedCreditCardTxnViewRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetPaginatedCreditCardTxnViewRequest) GetAccountId() string {
	if x, ok := x.GetIdentifier().(*GetPaginatedCreditCardTxnViewRequest_AccountId); ok {
		return x.AccountId
	}
	return ""
}

func (x *GetPaginatedCreditCardTxnViewRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetPaginatedCreditCardTxnViewRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *GetPaginatedCreditCardTxnViewRequest) GetStartTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTimestamp
	}
	return nil
}

func (x *GetPaginatedCreditCardTxnViewRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetPaginatedCreditCardTxnViewRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *GetPaginatedCreditCardTxnViewRequest) GetDescending() bool {
	if x != nil {
		return x.Descending
	}
	return false
}

func (x *GetPaginatedCreditCardTxnViewRequest) GetStatuses() []enums.TransactionStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

type isGetPaginatedCreditCardTxnViewRequest_Identifier interface {
	isGetPaginatedCreditCardTxnViewRequest_Identifier()
}

type GetPaginatedCreditCardTxnViewRequest_AccountId struct {
	// account for which transactions need to be fetched
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3,oneof"`
}

type GetPaginatedCreditCardTxnViewRequest_ActorId struct {
	// actor for which cc transactions need to be fetched
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*GetPaginatedCreditCardTxnViewRequest_AccountId) isGetPaginatedCreditCardTxnViewRequest_Identifier() {
}

func (*GetPaginatedCreditCardTxnViewRequest_ActorId) isGetPaginatedCreditCardTxnViewRequest_Identifier() {
}

type GetPaginatedCreditCardTxnViewResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status            *rpc.Status                       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TxnsViewModelList []*CreditCardTransactionViewModel `protobuf:"bytes,2,rep,name=txns_view_model_list,json=txnsViewModelList,proto3" json:"txns_view_model_list,omitempty"`
}

func (x *GetPaginatedCreditCardTxnViewResponse) Reset() {
	*x = GetPaginatedCreditCardTxnViewResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaginatedCreditCardTxnViewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaginatedCreditCardTxnViewResponse) ProtoMessage() {}

func (x *GetPaginatedCreditCardTxnViewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaginatedCreditCardTxnViewResponse.ProtoReflect.Descriptor instead.
func (*GetPaginatedCreditCardTxnViewResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{30}
}

func (x *GetPaginatedCreditCardTxnViewResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPaginatedCreditCardTxnViewResponse) GetTxnsViewModelList() []*CreditCardTransactionViewModel {
	if x != nil {
		return x.TxnsViewModelList
	}
	return nil
}

type CreditCardTransactionViewModel struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for the transaction
	TransactionId string `protobuf:"bytes,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	// transaction timestamp
	TransactionTimestamp *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=transaction_timestamp,json=transactionTimestamp,proto3" json:"transaction_timestamp,omitempty"`
	// transaction amount
	Amount *money.Money `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	// status of the transaction
	TransactionStatus enums.TransactionStatus `protobuf:"varint,4,opt,name=transaction_status,json=transactionStatus,proto3,enum=firefly.accounting.enums.TransactionStatus" json:"transaction_status,omitempty"`
	// type of transaction
	// credit/debit
	TransactionType enums.TransactionType `protobuf:"varint,5,opt,name=transaction_type,json=transactionType,proto3,enum=firefly.accounting.enums.TransactionType" json:"transaction_type,omitempty"`
	// credit card actor
	PrimaryActorId string `protobuf:"bytes,6,opt,name=primary_actor_id,json=primaryActorId,proto3" json:"primary_actor_id,omitempty"`
	// actor id of the other party involved in the txn
	OtherActorId string `protobuf:"bytes,7,opt,name=other_actor_id,json=otherActorId,proto3" json:"other_actor_id,omitempty"`
	// credit card PI id
	CardPi string `protobuf:"bytes,8,opt,name=card_pi,json=cardPi,proto3" json:"card_pi,omitempty"`
	// other actor PI id
	OtherActorPi string `protobuf:"bytes,9,opt,name=other_actor_pi,json=otherActorPi,proto3" json:"other_actor_pi,omitempty"`
	// category of the transaction
	TransactionCategory enums.TransactionCategory `protobuf:"varint,10,opt,name=transaction_category,json=transactionCategory,proto3,enum=firefly.accounting.enums.TransactionCategory" json:"transaction_category,omitempty"`
	TxnDescription      string                    `protobuf:"bytes,11,opt,name=txn_description,json=txnDescription,proto3" json:"txn_description,omitempty"`
	// external txn id for a txn
	ExternalTxnId string `protobuf:"bytes,12,opt,name=external_txn_id,json=externalTxnId,proto3" json:"external_txn_id,omitempty"`
	// bill ref id for a cc txn
	BillRefId string `protobuf:"bytes,13,opt,name=bill_ref_id,json=billRefId,proto3" json:"bill_ref_id,omitempty"`
}

func (x *CreditCardTransactionViewModel) Reset() {
	*x = CreditCardTransactionViewModel{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreditCardTransactionViewModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreditCardTransactionViewModel) ProtoMessage() {}

func (x *CreditCardTransactionViewModel) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreditCardTransactionViewModel.ProtoReflect.Descriptor instead.
func (*CreditCardTransactionViewModel) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{31}
}

func (x *CreditCardTransactionViewModel) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *CreditCardTransactionViewModel) GetTransactionTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.TransactionTimestamp
	}
	return nil
}

func (x *CreditCardTransactionViewModel) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *CreditCardTransactionViewModel) GetTransactionStatus() enums.TransactionStatus {
	if x != nil {
		return x.TransactionStatus
	}
	return enums.TransactionStatus(0)
}

func (x *CreditCardTransactionViewModel) GetTransactionType() enums.TransactionType {
	if x != nil {
		return x.TransactionType
	}
	return enums.TransactionType(0)
}

func (x *CreditCardTransactionViewModel) GetPrimaryActorId() string {
	if x != nil {
		return x.PrimaryActorId
	}
	return ""
}

func (x *CreditCardTransactionViewModel) GetOtherActorId() string {
	if x != nil {
		return x.OtherActorId
	}
	return ""
}

func (x *CreditCardTransactionViewModel) GetCardPi() string {
	if x != nil {
		return x.CardPi
	}
	return ""
}

func (x *CreditCardTransactionViewModel) GetOtherActorPi() string {
	if x != nil {
		return x.OtherActorPi
	}
	return ""
}

func (x *CreditCardTransactionViewModel) GetTransactionCategory() enums.TransactionCategory {
	if x != nil {
		return x.TransactionCategory
	}
	return enums.TransactionCategory(0)
}

func (x *CreditCardTransactionViewModel) GetTxnDescription() string {
	if x != nil {
		return x.TxnDescription
	}
	return ""
}

func (x *CreditCardTransactionViewModel) GetExternalTxnId() string {
	if x != nil {
		return x.ExternalTxnId
	}
	return ""
}

func (x *CreditCardTransactionViewModel) GetBillRefId() string {
	if x != nil {
		return x.BillRefId
	}
	return ""
}

type GetTxnsAndAdditionalInfoBetweenActorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrimaryActorId   string `protobuf:"bytes,1,opt,name=primary_actor_id,json=primaryActorId,proto3" json:"primary_actor_id,omitempty"`
	SecondaryActorId string `protobuf:"bytes,2,opt,name=secondary_actor_id,json=secondaryActorId,proto3" json:"secondary_actor_id,omitempty"`
	// timestamp starting from which transaction records are scanned.
	// NOTE- the records are returned INCLUSIVE of the timestamp.
	StartTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=start_timestamp,json=startTimestamp,proto3" json:"start_timestamp,omitempty"`
	// Page size determines the upper bound on the number of records
	// returned in a particular response.
	// Page size must be in the range [10, 40]
	// minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// An offset lets the caller control the number of records that needs to be skipped
	// starting from start timestamp.
	// e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
	// set to 5 then first 5 records from the qualifying set are removed.
	// deprecated. use order offset and aaTxnOffset
	Offset int32 `protobuf:"varint,5,opt,name=offset,proto3" json:"offset,omitempty"`
	// The sequence of the result returned be based on the boolean flag descending.
	// i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
	// If marked false ASCENDING ordered results are returned from the given start timestamp.
	Descending bool `protobuf:"varint,6,opt,name=descending,proto3" json:"descending,omitempty"`
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsRequest) Reset() {
	*x = GetTxnsAndAdditionalInfoBetweenActorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTxnsAndAdditionalInfoBetweenActorsRequest) ProtoMessage() {}

func (x *GetTxnsAndAdditionalInfoBetweenActorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTxnsAndAdditionalInfoBetweenActorsRequest.ProtoReflect.Descriptor instead.
func (*GetTxnsAndAdditionalInfoBetweenActorsRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{32}
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsRequest) GetPrimaryActorId() string {
	if x != nil {
		return x.PrimaryActorId
	}
	return ""
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsRequest) GetSecondaryActorId() string {
	if x != nil {
		return x.SecondaryActorId
	}
	return ""
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsRequest) GetStartTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTimestamp
	}
	return nil
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsRequest) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsRequest) GetDescending() bool {
	if x != nil {
		return x.Descending
	}
	return false
}

type GetTxnsAndAdditionalInfoBetweenActorsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                            *rpc.Status                          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TransactionWithAdditionalInfoList []*CardTransactionWithAdditionalInfo `protobuf:"bytes,2,rep,name=transaction_with_additional_info_list,json=transactionWithAdditionalInfoList,proto3" json:"transaction_with_additional_info_list,omitempty"`
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsResponse) Reset() {
	*x = GetTxnsAndAdditionalInfoBetweenActorsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTxnsAndAdditionalInfoBetweenActorsResponse) ProtoMessage() {}

func (x *GetTxnsAndAdditionalInfoBetweenActorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTxnsAndAdditionalInfoBetweenActorsResponse.ProtoReflect.Descriptor instead.
func (*GetTxnsAndAdditionalInfoBetweenActorsResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{33}
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTxnsAndAdditionalInfoBetweenActorsResponse) GetTransactionWithAdditionalInfoList() []*CardTransactionWithAdditionalInfo {
	if x != nil {
		return x.TransactionWithAdditionalInfoList
	}
	return nil
}

type CreateDisputeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnId          string                 `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	ActorId        string                 `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	AccountId      string                 `protobuf:"bytes,3,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	DisputeRef     string                 `protobuf:"bytes,4,opt,name=dispute_ref,json=disputeRef,proto3" json:"dispute_ref,omitempty"`
	DisputedAmount *money.Money           `protobuf:"bytes,5,opt,name=disputed_amount,json=disputedAmount,proto3" json:"disputed_amount,omitempty"`
	Reason         string                 `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`
	Description    string                 `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`
	DisputeState   enums.DisputeState     `protobuf:"varint,8,opt,name=dispute_state,json=disputeState,proto3,enum=firefly.accounting.enums.DisputeState" json:"dispute_state,omitempty"`
	DisputedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=disputed_at,json=disputedAt,proto3" json:"disputed_at,omitempty"`
}

func (x *CreateDisputeRequest) Reset() {
	*x = CreateDisputeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDisputeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDisputeRequest) ProtoMessage() {}

func (x *CreateDisputeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDisputeRequest.ProtoReflect.Descriptor instead.
func (*CreateDisputeRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{34}
}

func (x *CreateDisputeRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *CreateDisputeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateDisputeRequest) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *CreateDisputeRequest) GetDisputeRef() string {
	if x != nil {
		return x.DisputeRef
	}
	return ""
}

func (x *CreateDisputeRequest) GetDisputedAmount() *money.Money {
	if x != nil {
		return x.DisputedAmount
	}
	return nil
}

func (x *CreateDisputeRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *CreateDisputeRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateDisputeRequest) GetDisputeState() enums.DisputeState {
	if x != nil {
		return x.DisputeState
	}
	return enums.DisputeState(0)
}

func (x *CreateDisputeRequest) GetDisputedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DisputedAt
	}
	return nil
}

type CreateDisputeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *CreateDisputeResponse) Reset() {
	*x = CreateDisputeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDisputeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDisputeResponse) ProtoMessage() {}

func (x *CreateDisputeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDisputeResponse.ProtoReflect.Descriptor instead.
func (*CreateDisputeResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{35}
}

func (x *CreateDisputeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetDisputeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnId   string `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetDisputeRequest) Reset() {
	*x = GetDisputeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDisputeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDisputeRequest) ProtoMessage() {}

func (x *GetDisputeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDisputeRequest.ProtoReflect.Descriptor instead.
func (*GetDisputeRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{36}
}

func (x *GetDisputeRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *GetDisputeRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetDisputeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Dispute *DisputedTransaction `protobuf:"bytes,2,opt,name=dispute,proto3" json:"dispute,omitempty"`
}

func (x *GetDisputeResponse) Reset() {
	*x = GetDisputeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDisputeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDisputeResponse) ProtoMessage() {}

func (x *GetDisputeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDisputeResponse.ProtoReflect.Descriptor instead.
func (*GetDisputeResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetDisputeResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDisputeResponse) GetDispute() *DisputedTransaction {
	if x != nil {
		return x.Dispute
	}
	return nil
}

type UpdateDisputeStateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnId        string             `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	ActorId      string             `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	DisputeState enums.DisputeState `protobuf:"varint,3,opt,name=dispute_state,json=disputeState,proto3,enum=firefly.accounting.enums.DisputeState" json:"dispute_state,omitempty"`
}

func (x *UpdateDisputeStateRequest) Reset() {
	*x = UpdateDisputeStateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDisputeStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDisputeStateRequest) ProtoMessage() {}

func (x *UpdateDisputeStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDisputeStateRequest.ProtoReflect.Descriptor instead.
func (*UpdateDisputeStateRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{38}
}

func (x *UpdateDisputeStateRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *UpdateDisputeStateRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *UpdateDisputeStateRequest) GetDisputeState() enums.DisputeState {
	if x != nil {
		return x.DisputeState
	}
	return enums.DisputeState(0)
}

type UpdateDisputeStateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateDisputeStateResponse) Reset() {
	*x = UpdateDisputeStateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDisputeStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDisputeStateResponse) ProtoMessage() {}

func (x *UpdateDisputeStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDisputeStateResponse.ProtoReflect.Descriptor instead.
func (*UpdateDisputeStateResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{39}
}

func (x *UpdateDisputeStateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type IsDisputeAllowedRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnId   string `protobuf:"bytes,1,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *IsDisputeAllowedRequest) Reset() {
	*x = IsDisputeAllowedRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsDisputeAllowedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsDisputeAllowedRequest) ProtoMessage() {}

func (x *IsDisputeAllowedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsDisputeAllowedRequest.ProtoReflect.Descriptor instead.
func (*IsDisputeAllowedRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{40}
}

func (x *IsDisputeAllowedRequest) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *IsDisputeAllowedRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type IsDisputeAllowedResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *IsDisputeAllowedResponse) Reset() {
	*x = IsDisputeAllowedResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsDisputeAllowedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsDisputeAllowedResponse) ProtoMessage() {}

func (x *IsDisputeAllowedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsDisputeAllowedResponse.ProtoReflect.Descriptor instead.
func (*IsDisputeAllowedResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{41}
}

func (x *IsDisputeAllowedResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsDisputeAllowedResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type GetTransactionIdsByBillIdRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BillId string `protobuf:"bytes,1,opt,name=bill_id,json=billId,proto3" json:"bill_id,omitempty"`
	// flag to fetch child transactions as well
	// NOTE : This might contain child txns which are not be part of the bill for the given Id
	FetchChildTransactions bool `protobuf:"varint,2,opt,name=fetch_child_transactions,json=fetchChildTransactions,proto3" json:"fetch_child_transactions,omitempty"`
	// flag to fetch future reversal/refund child transactions
	// NOTE :
	// 1. If fetch_child_transactions is already true then we won't honour this flag and send all the child txns
	// 2. This might contain child txns which are not be part of the bill for the given Id
	FetchFutureRefundReversalChildTransactions bool `protobuf:"varint,3,opt,name=fetch_future_refund_reversal_child_transactions,json=fetchFutureRefundReversalChildTransactions,proto3" json:"fetch_future_refund_reversal_child_transactions,omitempty"`
}

func (x *GetTransactionIdsByBillIdRequest) Reset() {
	*x = GetTransactionIdsByBillIdRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionIdsByBillIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionIdsByBillIdRequest) ProtoMessage() {}

func (x *GetTransactionIdsByBillIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionIdsByBillIdRequest.ProtoReflect.Descriptor instead.
func (*GetTransactionIdsByBillIdRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{42}
}

func (x *GetTransactionIdsByBillIdRequest) GetBillId() string {
	if x != nil {
		return x.BillId
	}
	return ""
}

func (x *GetTransactionIdsByBillIdRequest) GetFetchChildTransactions() bool {
	if x != nil {
		return x.FetchChildTransactions
	}
	return false
}

func (x *GetTransactionIdsByBillIdRequest) GetFetchFutureRefundReversalChildTransactions() bool {
	if x != nil {
		return x.FetchFutureRefundReversalChildTransactions
	}
	return false
}

type GetTransactionIdsByBillIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 *rpc.Status                                                `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	TransactionIdResponses []*GetTransactionIdsByBillIdResponse_TransactionIdResponse `protobuf:"bytes,2,rep,name=transaction_id_responses,json=transactionIdResponses,proto3" json:"transaction_id_responses,omitempty"`
}

func (x *GetTransactionIdsByBillIdResponse) Reset() {
	*x = GetTransactionIdsByBillIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionIdsByBillIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionIdsByBillIdResponse) ProtoMessage() {}

func (x *GetTransactionIdsByBillIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionIdsByBillIdResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionIdsByBillIdResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{43}
}

func (x *GetTransactionIdsByBillIdResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTransactionIdsByBillIdResponse) GetTransactionIdResponses() []*GetTransactionIdsByBillIdResponse_TransactionIdResponse {
	if x != nil {
		return x.TransactionIdResponses
	}
	return nil
}

type GetCreditAccountLimitUtilisationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to GetBy:
	//
	//	*GetCreditAccountLimitUtilisationRequest_ReferenceId
	//	*GetCreditAccountLimitUtilisationRequest_CreditAccountId
	//	*GetCreditAccountLimitUtilisationRequest_ActorId
	GetBy isGetCreditAccountLimitUtilisationRequest_GetBy `protobuf_oneof:"GetBy"`
}

func (x *GetCreditAccountLimitUtilisationRequest) Reset() {
	*x = GetCreditAccountLimitUtilisationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditAccountLimitUtilisationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditAccountLimitUtilisationRequest) ProtoMessage() {}

func (x *GetCreditAccountLimitUtilisationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditAccountLimitUtilisationRequest.ProtoReflect.Descriptor instead.
func (*GetCreditAccountLimitUtilisationRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{44}
}

func (m *GetCreditAccountLimitUtilisationRequest) GetGetBy() isGetCreditAccountLimitUtilisationRequest_GetBy {
	if m != nil {
		return m.GetBy
	}
	return nil
}

func (x *GetCreditAccountLimitUtilisationRequest) GetReferenceId() string {
	if x, ok := x.GetGetBy().(*GetCreditAccountLimitUtilisationRequest_ReferenceId); ok {
		return x.ReferenceId
	}
	return ""
}

func (x *GetCreditAccountLimitUtilisationRequest) GetCreditAccountId() string {
	if x, ok := x.GetGetBy().(*GetCreditAccountLimitUtilisationRequest_CreditAccountId); ok {
		return x.CreditAccountId
	}
	return ""
}

func (x *GetCreditAccountLimitUtilisationRequest) GetActorId() string {
	if x, ok := x.GetGetBy().(*GetCreditAccountLimitUtilisationRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

type isGetCreditAccountLimitUtilisationRequest_GetBy interface {
	isGetCreditAccountLimitUtilisationRequest_GetBy()
}

type GetCreditAccountLimitUtilisationRequest_ReferenceId struct {
	// Reference id is the id passed as an identifier to the vendor.
	// It is the bank customer id stored in the user
	ReferenceId string `protobuf:"bytes,1,opt,name=reference_id,json=referenceId,proto3,oneof"`
}

type GetCreditAccountLimitUtilisationRequest_CreditAccountId struct {
	CreditAccountId string `protobuf:"bytes,2,opt,name=credit_account_id,json=creditAccountId,proto3,oneof"`
}

type GetCreditAccountLimitUtilisationRequest_ActorId struct {
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*GetCreditAccountLimitUtilisationRequest_ReferenceId) isGetCreditAccountLimitUtilisationRequest_GetBy() {
}

func (*GetCreditAccountLimitUtilisationRequest_CreditAccountId) isGetCreditAccountLimitUtilisationRequest_GetBy() {
}

func (*GetCreditAccountLimitUtilisationRequest_ActorId) isGetCreditAccountLimitUtilisationRequest_GetBy() {
}

type GetCreditAccountLimitUtilisationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LimitActual    *money.Money `protobuf:"bytes,2,opt,name=limit_actual,json=limitActual,proto3" json:"limit_actual,omitempty"`
	LimitAvailable *money.Money `protobuf:"bytes,3,opt,name=limit_available,json=limitAvailable,proto3" json:"limit_available,omitempty"`
	LimitUtilized  *money.Money `protobuf:"bytes,4,opt,name=limit_utilized,json=limitUtilized,proto3" json:"limit_utilized,omitempty"`
}

func (x *GetCreditAccountLimitUtilisationResponse) Reset() {
	*x = GetCreditAccountLimitUtilisationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditAccountLimitUtilisationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditAccountLimitUtilisationResponse) ProtoMessage() {}

func (x *GetCreditAccountLimitUtilisationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditAccountLimitUtilisationResponse.ProtoReflect.Descriptor instead.
func (*GetCreditAccountLimitUtilisationResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{45}
}

func (x *GetCreditAccountLimitUtilisationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCreditAccountLimitUtilisationResponse) GetLimitActual() *money.Money {
	if x != nil {
		return x.LimitActual
	}
	return nil
}

func (x *GetCreditAccountLimitUtilisationResponse) GetLimitAvailable() *money.Money {
	if x != nil {
		return x.LimitAvailable
	}
	return nil
}

func (x *GetCreditAccountLimitUtilisationResponse) GetLimitUtilized() *money.Money {
	if x != nil {
		return x.LimitUtilized
	}
	return nil
}

type GetCreditAccountDueInformationRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to GetBy:
	//
	//	*GetCreditAccountDueInformationRequest_ReferenceId
	//	*GetCreditAccountDueInformationRequest_CreditAccountId
	//	*GetCreditAccountDueInformationRequest_ActorId
	GetBy isGetCreditAccountDueInformationRequest_GetBy `protobuf_oneof:"GetBy"`
}

func (x *GetCreditAccountDueInformationRequest) Reset() {
	*x = GetCreditAccountDueInformationRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditAccountDueInformationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditAccountDueInformationRequest) ProtoMessage() {}

func (x *GetCreditAccountDueInformationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditAccountDueInformationRequest.ProtoReflect.Descriptor instead.
func (*GetCreditAccountDueInformationRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{46}
}

func (m *GetCreditAccountDueInformationRequest) GetGetBy() isGetCreditAccountDueInformationRequest_GetBy {
	if m != nil {
		return m.GetBy
	}
	return nil
}

func (x *GetCreditAccountDueInformationRequest) GetReferenceId() string {
	if x, ok := x.GetGetBy().(*GetCreditAccountDueInformationRequest_ReferenceId); ok {
		return x.ReferenceId
	}
	return ""
}

func (x *GetCreditAccountDueInformationRequest) GetCreditAccountId() string {
	if x, ok := x.GetGetBy().(*GetCreditAccountDueInformationRequest_CreditAccountId); ok {
		return x.CreditAccountId
	}
	return ""
}

func (x *GetCreditAccountDueInformationRequest) GetActorId() string {
	if x, ok := x.GetGetBy().(*GetCreditAccountDueInformationRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

type isGetCreditAccountDueInformationRequest_GetBy interface {
	isGetCreditAccountDueInformationRequest_GetBy()
}

type GetCreditAccountDueInformationRequest_ReferenceId struct {
	// Reference id is the id passed as an identifier to the vendor.
	// It is the bank customer id stored in the user
	ReferenceId string `protobuf:"bytes,1,opt,name=reference_id,json=referenceId,proto3,oneof"`
}

type GetCreditAccountDueInformationRequest_CreditAccountId struct {
	CreditAccountId string `protobuf:"bytes,2,opt,name=credit_account_id,json=creditAccountId,proto3,oneof"`
}

type GetCreditAccountDueInformationRequest_ActorId struct {
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*GetCreditAccountDueInformationRequest_ReferenceId) isGetCreditAccountDueInformationRequest_GetBy() {
}

func (*GetCreditAccountDueInformationRequest_CreditAccountId) isGetCreditAccountDueInformationRequest_GetBy() {
}

func (*GetCreditAccountDueInformationRequest_ActorId) isGetCreditAccountDueInformationRequest_GetBy() {
}

type GetCreditAccountDueInformationResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status           *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	MinimumDueAmount *money.Money `protobuf:"bytes,2,opt,name=minimum_due_amount,json=minimumDueAmount,proto3" json:"minimum_due_amount,omitempty"`
	TotalDueAmount   *money.Money `protobuf:"bytes,3,opt,name=total_due_amount,json=totalDueAmount,proto3" json:"total_due_amount,omitempty"`
	PaymentMade      *money.Money `protobuf:"bytes,4,opt,name=payment_made,json=paymentMade,proto3" json:"payment_made,omitempty"`
	// non billed amount is the outstanding amount for which a bill is not generated yet.
	NonBilledAmount     *money.Money  `protobuf:"bytes,5,opt,name=non_billed_amount,json=nonBilledAmount,proto3" json:"non_billed_amount,omitempty"`
	InterestAccumulated *money.Money  `protobuf:"bytes,6,opt,name=interest_accumulated,json=interestAccumulated,proto3" json:"interest_accumulated,omitempty"`
	DueDate             *typesv2.Date `protobuf:"bytes,7,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
	// for a billing cycle, unpaid total due is the remaining min due amount to be paid.
	UnpaidMinDue *money.Money `protobuf:"bytes,9,opt,name=unpaid_min_due,json=unpaidMinDue,proto3" json:"unpaid_min_due,omitempty"`
	// for a billing cycle, unpaid total due is the remaining due amount to be paid.
	UnpaidTotalDue         *money.Money `protobuf:"bytes,10,opt,name=unpaid_total_due,json=unpaidTotalDue,proto3" json:"unpaid_total_due,omitempty"`
	TotalOutstandingAmount *money.Money `protobuf:"bytes,11,opt,name=total_outstanding_amount,json=totalOutstandingAmount,proto3" json:"total_outstanding_amount,omitempty"`
}

func (x *GetCreditAccountDueInformationResponse) Reset() {
	*x = GetCreditAccountDueInformationResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCreditAccountDueInformationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCreditAccountDueInformationResponse) ProtoMessage() {}

func (x *GetCreditAccountDueInformationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCreditAccountDueInformationResponse.ProtoReflect.Descriptor instead.
func (*GetCreditAccountDueInformationResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{47}
}

func (x *GetCreditAccountDueInformationResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCreditAccountDueInformationResponse) GetMinimumDueAmount() *money.Money {
	if x != nil {
		return x.MinimumDueAmount
	}
	return nil
}

func (x *GetCreditAccountDueInformationResponse) GetTotalDueAmount() *money.Money {
	if x != nil {
		return x.TotalDueAmount
	}
	return nil
}

func (x *GetCreditAccountDueInformationResponse) GetPaymentMade() *money.Money {
	if x != nil {
		return x.PaymentMade
	}
	return nil
}

func (x *GetCreditAccountDueInformationResponse) GetNonBilledAmount() *money.Money {
	if x != nil {
		return x.NonBilledAmount
	}
	return nil
}

func (x *GetCreditAccountDueInformationResponse) GetInterestAccumulated() *money.Money {
	if x != nil {
		return x.InterestAccumulated
	}
	return nil
}

func (x *GetCreditAccountDueInformationResponse) GetDueDate() *typesv2.Date {
	if x != nil {
		return x.DueDate
	}
	return nil
}

func (x *GetCreditAccountDueInformationResponse) GetUnpaidMinDue() *money.Money {
	if x != nil {
		return x.UnpaidMinDue
	}
	return nil
}

func (x *GetCreditAccountDueInformationResponse) GetUnpaidTotalDue() *money.Money {
	if x != nil {
		return x.UnpaidTotalDue
	}
	return nil
}

func (x *GetCreditAccountDueInformationResponse) GetTotalOutstandingAmount() *money.Money {
	if x != nil {
		return x.TotalOutstandingAmount
	}
	return nil
}

type GetTxnsWithAdditionalInfosRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to GetBy:
	//
	//	*GetTxnsWithAdditionalInfosRequest_BatchDedupeIdGenerationParameter
	//	*GetTxnsWithAdditionalInfosRequest_BatchDedupeIds
	//	*GetTxnsWithAdditionalInfosRequest_BatchTxnIds
	//	*GetTxnsWithAdditionalInfosRequest_BatchExternalTxnIds
	GetBy isGetTxnsWithAdditionalInfosRequest_GetBy `protobuf_oneof:"GetBy"`
}

func (x *GetTxnsWithAdditionalInfosRequest) Reset() {
	*x = GetTxnsWithAdditionalInfosRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTxnsWithAdditionalInfosRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTxnsWithAdditionalInfosRequest) ProtoMessage() {}

func (x *GetTxnsWithAdditionalInfosRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTxnsWithAdditionalInfosRequest.ProtoReflect.Descriptor instead.
func (*GetTxnsWithAdditionalInfosRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{48}
}

func (m *GetTxnsWithAdditionalInfosRequest) GetGetBy() isGetTxnsWithAdditionalInfosRequest_GetBy {
	if m != nil {
		return m.GetBy
	}
	return nil
}

// Deprecated: Marked as deprecated in api/firefly/accounting/service.proto.
func (x *GetTxnsWithAdditionalInfosRequest) GetBatchDedupeIdGenerationParameter() *BatchDedupeIdGenerationParameter {
	if x, ok := x.GetGetBy().(*GetTxnsWithAdditionalInfosRequest_BatchDedupeIdGenerationParameter); ok {
		return x.BatchDedupeIdGenerationParameter
	}
	return nil
}

func (x *GetTxnsWithAdditionalInfosRequest) GetBatchDedupeIds() *BatchDedupeIds {
	if x, ok := x.GetGetBy().(*GetTxnsWithAdditionalInfosRequest_BatchDedupeIds); ok {
		return x.BatchDedupeIds
	}
	return nil
}

func (x *GetTxnsWithAdditionalInfosRequest) GetBatchTxnIds() *BatchTxnIds {
	if x, ok := x.GetGetBy().(*GetTxnsWithAdditionalInfosRequest_BatchTxnIds); ok {
		return x.BatchTxnIds
	}
	return nil
}

func (x *GetTxnsWithAdditionalInfosRequest) GetBatchExternalTxnIds() *BatchExternalTxnIds {
	if x, ok := x.GetGetBy().(*GetTxnsWithAdditionalInfosRequest_BatchExternalTxnIds); ok {
		return x.BatchExternalTxnIds
	}
	return nil
}

type isGetTxnsWithAdditionalInfosRequest_GetBy interface {
	isGetTxnsWithAdditionalInfosRequest_GetBy()
}

type GetTxnsWithAdditionalInfosRequest_BatchDedupeIdGenerationParameter struct {
	// Deprecated: Marked as deprecated in api/firefly/accounting/service.proto.
	BatchDedupeIdGenerationParameter *BatchDedupeIdGenerationParameter `protobuf:"bytes,1,opt,name=batch_dedupe_id_generation_parameter,json=batchDedupeIdGenerationParameter,proto3,oneof"`
}

type GetTxnsWithAdditionalInfosRequest_BatchDedupeIds struct {
	BatchDedupeIds *BatchDedupeIds `protobuf:"bytes,2,opt,name=batch_dedupe_ids,json=batchDedupeIds,proto3,oneof"`
}

type GetTxnsWithAdditionalInfosRequest_BatchTxnIds struct {
	BatchTxnIds *BatchTxnIds `protobuf:"bytes,3,opt,name=batch_txn_ids,json=batchTxnIds,proto3,oneof"`
}

type GetTxnsWithAdditionalInfosRequest_BatchExternalTxnIds struct {
	BatchExternalTxnIds *BatchExternalTxnIds `protobuf:"bytes,4,opt,name=batch_external_txn_ids,json=batchExternalTxnIds,proto3,oneof"`
}

func (*GetTxnsWithAdditionalInfosRequest_BatchDedupeIdGenerationParameter) isGetTxnsWithAdditionalInfosRequest_GetBy() {
}

func (*GetTxnsWithAdditionalInfosRequest_BatchDedupeIds) isGetTxnsWithAdditionalInfosRequest_GetBy() {
}

func (*GetTxnsWithAdditionalInfosRequest_BatchTxnIds) isGetTxnsWithAdditionalInfosRequest_GetBy() {}

func (*GetTxnsWithAdditionalInfosRequest_BatchExternalTxnIds) isGetTxnsWithAdditionalInfosRequest_GetBy() {
}

type GetTxnsWithAdditionalInfosResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                             *rpc.Status                          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CardTransactionWithAdditionalInfos []*CardTransactionWithAdditionalInfo `protobuf:"bytes,2,rep,name=card_transaction_with_additional_infos,json=cardTransactionWithAdditionalInfos,proto3" json:"card_transaction_with_additional_infos,omitempty"`
}

func (x *GetTxnsWithAdditionalInfosResponse) Reset() {
	*x = GetTxnsWithAdditionalInfosResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTxnsWithAdditionalInfosResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTxnsWithAdditionalInfosResponse) ProtoMessage() {}

func (x *GetTxnsWithAdditionalInfosResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTxnsWithAdditionalInfosResponse.ProtoReflect.Descriptor instead.
func (*GetTxnsWithAdditionalInfosResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{49}
}

func (x *GetTxnsWithAdditionalInfosResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetTxnsWithAdditionalInfosResponse) GetCardTransactionWithAdditionalInfos() []*CardTransactionWithAdditionalInfo {
	if x != nil {
		return x.CardTransactionWithAdditionalInfos
	}
	return nil
}

type GetPaginatedTxnWithAdditionalInfoRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Identifier:
	//
	//	*GetPaginatedTxnWithAdditionalInfoRequest_AccountId
	//	*GetPaginatedTxnWithAdditionalInfoRequest_ActorId
	Identifier isGetPaginatedTxnWithAdditionalInfoRequest_Identifier `protobuf_oneof:"Identifier"`
	// transaction status filter
	// if not set we will return txns for all states
	Statuses []enums.TransactionStatus `protobuf:"varint,3,rep,packed,name=statuses,proto3,enum=firefly.accounting.enums.TransactionStatus" json:"statuses,omitempty"`
	// field masks for fetching particular fields
	// we will return the complete object if this is empty
	CardTransactionFieldMasks []enums.CardTransactionFieldMask `protobuf:"varint,4,rep,packed,name=card_transaction_field_masks,json=cardTransactionFieldMasks,proto3,enum=firefly.accounting.enums.CardTransactionFieldMask" json:"card_transaction_field_masks,omitempty"`
	// for pagination
	PageContext *rpc.PageContextRequest `protobuf:"bytes,5,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetPaginatedTxnWithAdditionalInfoRequest) Reset() {
	*x = GetPaginatedTxnWithAdditionalInfoRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaginatedTxnWithAdditionalInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaginatedTxnWithAdditionalInfoRequest) ProtoMessage() {}

func (x *GetPaginatedTxnWithAdditionalInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaginatedTxnWithAdditionalInfoRequest.ProtoReflect.Descriptor instead.
func (*GetPaginatedTxnWithAdditionalInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{50}
}

func (m *GetPaginatedTxnWithAdditionalInfoRequest) GetIdentifier() isGetPaginatedTxnWithAdditionalInfoRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *GetPaginatedTxnWithAdditionalInfoRequest) GetAccountId() string {
	if x, ok := x.GetIdentifier().(*GetPaginatedTxnWithAdditionalInfoRequest_AccountId); ok {
		return x.AccountId
	}
	return ""
}

func (x *GetPaginatedTxnWithAdditionalInfoRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*GetPaginatedTxnWithAdditionalInfoRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

func (x *GetPaginatedTxnWithAdditionalInfoRequest) GetStatuses() []enums.TransactionStatus {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *GetPaginatedTxnWithAdditionalInfoRequest) GetCardTransactionFieldMasks() []enums.CardTransactionFieldMask {
	if x != nil {
		return x.CardTransactionFieldMasks
	}
	return nil
}

func (x *GetPaginatedTxnWithAdditionalInfoRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type isGetPaginatedTxnWithAdditionalInfoRequest_Identifier interface {
	isGetPaginatedTxnWithAdditionalInfoRequest_Identifier()
}

type GetPaginatedTxnWithAdditionalInfoRequest_AccountId struct {
	// account for which transactions need to be fetched
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3,oneof"`
}

type GetPaginatedTxnWithAdditionalInfoRequest_ActorId struct {
	// actor for which cc transactions need to be fetched
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*GetPaginatedTxnWithAdditionalInfoRequest_AccountId) isGetPaginatedTxnWithAdditionalInfoRequest_Identifier() {
}

func (*GetPaginatedTxnWithAdditionalInfoRequest_ActorId) isGetPaginatedTxnWithAdditionalInfoRequest_Identifier() {
}

type GetPaginatedTxnWithAdditionalInfoResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                             *rpc.Status                          `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CardTransactionWithAdditionalInfos []*CardTransactionWithAdditionalInfo `protobuf:"bytes,2,rep,name=card_transaction_with_additional_infos,json=cardTransactionWithAdditionalInfos,proto3" json:"card_transaction_with_additional_infos,omitempty"`
	// for pagination
	PageContext *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetPaginatedTxnWithAdditionalInfoResponse) Reset() {
	*x = GetPaginatedTxnWithAdditionalInfoResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaginatedTxnWithAdditionalInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaginatedTxnWithAdditionalInfoResponse) ProtoMessage() {}

func (x *GetPaginatedTxnWithAdditionalInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaginatedTxnWithAdditionalInfoResponse.ProtoReflect.Descriptor instead.
func (*GetPaginatedTxnWithAdditionalInfoResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{51}
}

func (x *GetPaginatedTxnWithAdditionalInfoResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPaginatedTxnWithAdditionalInfoResponse) GetCardTransactionWithAdditionalInfos() []*CardTransactionWithAdditionalInfo {
	if x != nil {
		return x.CardTransactionWithAdditionalInfos
	}
	return nil
}

func (x *GetPaginatedTxnWithAdditionalInfoResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type UpdateBillToTxnMappingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TxnIds    []string `protobuf:"bytes,1,rep,name=txn_ids,json=txnIds,proto3" json:"txn_ids,omitempty"`
	BillRefId string   `protobuf:"bytes,2,opt,name=bill_ref_id,json=billRefId,proto3" json:"bill_ref_id,omitempty"`
}

func (x *UpdateBillToTxnMappingRequest) Reset() {
	*x = UpdateBillToTxnMappingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBillToTxnMappingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBillToTxnMappingRequest) ProtoMessage() {}

func (x *UpdateBillToTxnMappingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBillToTxnMappingRequest.ProtoReflect.Descriptor instead.
func (*UpdateBillToTxnMappingRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{52}
}

func (x *UpdateBillToTxnMappingRequest) GetTxnIds() []string {
	if x != nil {
		return x.TxnIds
	}
	return nil
}

func (x *UpdateBillToTxnMappingRequest) GetBillRefId() string {
	if x != nil {
		return x.BillRefId
	}
	return ""
}

type UpdateBillToTxnMappingResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateBillToTxnMappingResponse) Reset() {
	*x = UpdateBillToTxnMappingResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBillToTxnMappingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBillToTxnMappingResponse) ProtoMessage() {}

func (x *UpdateBillToTxnMappingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBillToTxnMappingResponse.ProtoReflect.Descriptor instead.
func (*UpdateBillToTxnMappingResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{53}
}

func (x *UpdateBillToTxnMappingResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type BatchDedupeIds struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DedupeIds []string `protobuf:"bytes,1,rep,name=dedupe_ids,json=dedupeIds,proto3" json:"dedupe_ids,omitempty"`
}

func (x *BatchDedupeIds) Reset() {
	*x = BatchDedupeIds{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchDedupeIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchDedupeIds) ProtoMessage() {}

func (x *BatchDedupeIds) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchDedupeIds.ProtoReflect.Descriptor instead.
func (*BatchDedupeIds) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{54}
}

func (x *BatchDedupeIds) GetDedupeIds() []string {
	if x != nil {
		return x.DedupeIds
	}
	return nil
}

type GetCollateralLienStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorAndCollateralIdentifier *ActorAndCollateralIdentifier `protobuf:"bytes,1,opt,name=actor_and_collateral_identifier,json=actorAndCollateralIdentifier,proto3" json:"actor_and_collateral_identifier,omitempty"`
}

func (x *GetCollateralLienStatusRequest) Reset() {
	*x = GetCollateralLienStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCollateralLienStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCollateralLienStatusRequest) ProtoMessage() {}

func (x *GetCollateralLienStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCollateralLienStatusRequest.ProtoReflect.Descriptor instead.
func (*GetCollateralLienStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{55}
}

func (x *GetCollateralLienStatusRequest) GetActorAndCollateralIdentifier() *ActorAndCollateralIdentifier {
	if x != nil {
		return x.ActorAndCollateralIdentifier
	}
	return nil
}

type GetCollateralLienStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status               *rpc.Status                  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	CollateralLienStatus typesv2.CollateralLienStatus `protobuf:"varint,2,opt,name=collateral_lien_status,json=collateralLienStatus,proto3,enum=api.typesv2.CollateralLienStatus" json:"collateral_lien_status,omitempty"`
}

func (x *GetCollateralLienStatusResponse) Reset() {
	*x = GetCollateralLienStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCollateralLienStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCollateralLienStatusResponse) ProtoMessage() {}

func (x *GetCollateralLienStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCollateralLienStatusResponse.ProtoReflect.Descriptor instead.
func (*GetCollateralLienStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{56}
}

func (x *GetCollateralLienStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetCollateralLienStatusResponse) GetCollateralLienStatus() typesv2.CollateralLienStatus {
	if x != nil {
		return x.CollateralLienStatus
	}
	return typesv2.CollateralLienStatus(0)
}

// Identifier to fetch card post verification of collateral details stored in credit account
type ActorAndCollateralIdentifier struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// id of the collateral stored in account -> collateral details.
	CollateralId string `protobuf:"bytes,2,opt,name=collateral_id,json=collateralId,proto3" json:"collateral_id,omitempty"`
	// type of collateral against which a credit line is issued.
	CollateralType typesv2.CardProgramCollateral `protobuf:"varint,3,opt,name=collateral_type,json=collateralType,proto3,enum=api.typesv2.CardProgramCollateral" json:"collateral_type,omitempty"`
}

func (x *ActorAndCollateralIdentifier) Reset() {
	*x = ActorAndCollateralIdentifier{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ActorAndCollateralIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActorAndCollateralIdentifier) ProtoMessage() {}

func (x *ActorAndCollateralIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActorAndCollateralIdentifier.ProtoReflect.Descriptor instead.
func (*ActorAndCollateralIdentifier) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{57}
}

func (x *ActorAndCollateralIdentifier) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ActorAndCollateralIdentifier) GetCollateralId() string {
	if x != nil {
		return x.CollateralId
	}
	return ""
}

func (x *ActorAndCollateralIdentifier) GetCollateralType() typesv2.CardProgramCollateral {
	if x != nil {
		return x.CollateralType
	}
	return typesv2.CardProgramCollateral(0)
}

type GetAccountRequest_ActorIdAndRefId struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ReferenceId string `protobuf:"bytes,2,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
}

func (x *GetAccountRequest_ActorIdAndRefId) Reset() {
	*x = GetAccountRequest_ActorIdAndRefId{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAccountRequest_ActorIdAndRefId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountRequest_ActorIdAndRefId) ProtoMessage() {}

func (x *GetAccountRequest_ActorIdAndRefId) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountRequest_ActorIdAndRefId.ProtoReflect.Descriptor instead.
func (*GetAccountRequest_ActorIdAndRefId) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{10, 0}
}

func (x *GetAccountRequest_ActorIdAndRefId) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAccountRequest_ActorIdAndRefId) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

type GetTransactionIdsByBillIdResponse_TransactionIdResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExternalTxnId       string                    `protobuf:"bytes,1,opt,name=external_txn_id,json=externalTxnId,proto3" json:"external_txn_id,omitempty"`
	TxnId               string                    `protobuf:"bytes,2,opt,name=txn_id,json=txnId,proto3" json:"txn_id,omitempty"`
	TransactionCategory enums.TransactionCategory `protobuf:"varint,3,opt,name=transaction_category,json=transactionCategory,proto3,enum=firefly.accounting.enums.TransactionCategory" json:"transaction_category,omitempty"`
}

func (x *GetTransactionIdsByBillIdResponse_TransactionIdResponse) Reset() {
	*x = GetTransactionIdsByBillIdResponse_TransactionIdResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_firefly_accounting_service_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTransactionIdsByBillIdResponse_TransactionIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTransactionIdsByBillIdResponse_TransactionIdResponse) ProtoMessage() {}

func (x *GetTransactionIdsByBillIdResponse_TransactionIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_firefly_accounting_service_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTransactionIdsByBillIdResponse_TransactionIdResponse.ProtoReflect.Descriptor instead.
func (*GetTransactionIdsByBillIdResponse_TransactionIdResponse) Descriptor() ([]byte, []int) {
	return file_api_firefly_accounting_service_proto_rawDescGZIP(), []int{43, 0}
}

func (x *GetTransactionIdsByBillIdResponse_TransactionIdResponse) GetExternalTxnId() string {
	if x != nil {
		return x.ExternalTxnId
	}
	return ""
}

func (x *GetTransactionIdsByBillIdResponse_TransactionIdResponse) GetTxnId() string {
	if x != nil {
		return x.TxnId
	}
	return ""
}

func (x *GetTransactionIdsByBillIdResponse_TransactionIdResponse) GetTransactionCategory() enums.TransactionCategory {
	if x != nil {
		return x.TransactionCategory
	}
	return enums.TransactionCategory(0)
}

var File_api_firefly_accounting_service_proto protoreflect.FileDescriptor

var file_api_firefly_accounting_service_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x7a, 0x65, 0x72, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2f,
	0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x2f, 0x74, 0x72, 0x65, 0x65, 0x5f, 0x6e, 0x6f, 0x64,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x2f, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x63, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x3a, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x2f, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2f, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61,
	0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0x5c, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x69,
	0x73, 0x70, 0x75, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6e, 0x73,
	0x77, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6e, 0x73, 0x77, 0x65,
	0x72, 0x22, 0x94, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x69, 0x73,
	0x70, 0x75, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4d, 0x0a, 0x0d, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x2e, 0x51, 0x75,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x32, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x41,
	0x6c, 0x6c, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0x9b, 0x01, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5c, 0x0a, 0x15,
	0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x81, 0x02, 0x0a, 0x26, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x6f,
	0x72, 0x41, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x09, 0x66, 0x72, 0x6f, 0x6d, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x33, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x06, 0x74,
	0x6f, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x5f, 0x66, 0x72, 0x6d, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x78, 0x6e,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65,
	0x46, 0x72, 0x6d, 0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x78, 0x6e, 0x73, 0x22, 0x97,
	0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x47, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x9c, 0x02, 0x0a, 0x14, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x12, 0x3b, 0x0a, 0x0c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x63, 0x61, 0x72, 0x64, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61,
	0x6d, 0x12, 0x54, 0x0a, 0x12, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x79, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x22, 0x3a, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x47, 0x65, 0x74, 0x42, 0x79, 0x22, 0x79,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x08, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x9f, 0x02, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x6a, 0x0a, 0x16, 0x62, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x5f, 0x61,
	0x6e, 0x64, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x41, 0x6e,
	0x64, 0x52, 0x65, 0x66, 0x49, 0x64, 0x48, 0x00, 0x52, 0x11, 0x62, 0x79, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x52, 0x65, 0x66, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x1a, 0x4f, 0x0a, 0x0f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x52,
	0x65, 0x66, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x47, 0x65, 0x74, 0x42, 0x79, 0x22, 0x76, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3b, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x22, 0xee, 0x04, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27,
	0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x09, 0x64, 0x65, 0x64, 0x75,
	0x70, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x64,
	0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x12, 0x5e, 0x0a, 0x16, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x74,
	0x63, 0x68, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x73,
	0x48, 0x00, 0x52, 0x13, 0x62, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x73, 0x48,
	0x00, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x63, 0x68, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x45,
	0x0a, 0x1e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x1b, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x8a, 0x01, 0x0a, 0x24, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44,
	0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00,
	0x52, 0x20, 0x62, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x12, 0x67, 0x0a, 0x19, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x5f, 0x61, 0x6e, 0x64, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65,
	0x73, 0x48, 0x00, 0x52, 0x16, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x41, 0x6e,
	0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x42, 0x07, 0x0a, 0x05, 0x47,
	0x65, 0x74, 0x42, 0x79, 0x22, 0x9d, 0x01, 0x0a, 0x16, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x49, 0x64, 0x41, 0x6e, 0x64, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x64,
	0x0a, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2d,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x15, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x22, 0x3f, 0x0a, 0x13, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x28, 0x0a, 0x10, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54,
	0x78, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x26, 0x0a, 0x0b, 0x42, 0x61, 0x74, 0x63, 0x68, 0x54, 0x78,
	0x6e, 0x49, 0x64, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x73, 0x22, 0x87, 0x01,
	0x0a, 0x20, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x12, 0x63, 0x0a, 0x1f, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x5f,
	0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x52, 0x1c, 0x64, 0x65, 0x64, 0x75, 0x70,
	0x65, 0x49, 0x64, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x22, 0x87, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0x7e, 0x0a, 0x16, 0x50, 0x6f, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0b, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x23, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0x3e, 0x0a, 0x17, 0x50, 0x6f, 0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x2f, 0x0a, 0x16, 0x46, 0x65, 0x74, 0x63, 0x68, 0x54, 0x78, 0x6e, 0x52, 0x65, 0x63,
	0x65, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x74,
	0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e,
	0x49, 0x64, 0x22, 0x82, 0x07, 0x0a, 0x17, 0x46, 0x65, 0x74, 0x63, 0x68, 0x54, 0x78, 0x6e, 0x52,
	0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x69,
	0x63, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69,
	0x63, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09,
	0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x66, 0x72, 0x6f, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x6f, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x74, 0x78, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x09, 0x74, 0x78, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44,
	0x0a, 0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x74, 0x78, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x50, 0x0a, 0x0c, 0x74, 0x78, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0b, 0x74, 0x78, 0x6e, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x35, 0x0a, 0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x74, 0x78, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x62,
	0x0a, 0x1e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x7a, 0x65, 0x72, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x79, 0x52, 0x1c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x65, 0x73, 0x12, 0x42, 0x0a, 0x10, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x72, 0x74,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0e, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x50, 0x61, 0x72,
	0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x6a, 0x0a, 0x1a, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70,
	0x74, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x18, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70,
	0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x22, 0x8a, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x63, 0x65,
	0x69, 0x70, 0x74, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x63, 0x0a, 0x17, 0x66, 0x65, 0x65, 0x73, 0x5f, 0x61, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x65, 0x73, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x48, 0x00, 0x52, 0x15, 0x66, 0x65, 0x65, 0x73, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x22, 0x39, 0x0a, 0x15, 0x46, 0x65, 0x65, 0x73, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22,
	0xa2, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x27, 0x0a, 0x0e, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x1e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x1b,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x47,
	0x65, 0x74, 0x42, 0x79, 0x22, 0xd9, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x87, 0x01, 0x0a, 0x25, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x21, 0x63,
	0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69,
	0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x22, 0x96, 0x05, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x78, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0a,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x26, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x28, 0x28, 0x0a, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12,
	0x47, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x73, 0x0a, 0x1c, 0x63, 0x61, 0x72, 0x64,
	0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x32,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x52, 0x19, 0x63, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x92, 0x01,
	0x0a, 0x27, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x3c, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x23, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x73, 0x12, 0x37, 0x0a, 0x18, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x66, 0x72,
	0x6d, 0x5f, 0x64, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x74, 0x78, 0x6e, 0x73, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x46, 0x72, 0x6d,
	0x44, 0x65, 0x63, 0x6c, 0x69, 0x6e, 0x65, 0x54, 0x78, 0x6e, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xc9, 0x01, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x78, 0x6e, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x87, 0x01, 0x0a, 0x25,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x21, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57,
	0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc2, 0x01, 0x0a, 0x21, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45, 0x0a, 0x0b, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x56, 0x0a, 0x0f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0e, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xd5, 0x02, 0x0a, 0x24, 0x47,
	0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x78, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x43, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12, 0x1e, 0x0a, 0x0a, 0x64,
	0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x47, 0x0a, 0x08, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x65, 0x73, 0x42, 0x0c, 0x0a, 0x0a, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x22, 0xb1, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x78, 0x6e,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72,
	0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x63, 0x0a, 0x14, 0x74, 0x78, 0x6e, 0x73, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x32, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f,
	0x64, 0x65, 0x6c, 0x52, 0x11, 0x74, 0x78, 0x6e, 0x73, 0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64,
	0x65, 0x6c, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xd8, 0x05, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x64, 0x69,
	0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x56, 0x69, 0x65, 0x77, 0x4d, 0x6f, 0x64, 0x65, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x4f, 0x0a, 0x15, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x5a, 0x0a,
	0x12, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x54, 0x0a, 0x10, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x28, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72, 0x69, 0x6d, 0x61,
	0x72, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x07, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x63, 0x61, 0x72, 0x64, 0x50, 0x69, 0x12, 0x24, 0x0a, 0x0e, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x70, 0x69, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x50, 0x69, 0x12, 0x60,
	0x0a, 0x14, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x13, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79,
	0x12, 0x27, 0x0a, 0x0f, 0x74, 0x78, 0x6e, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x78, 0x6e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x0f, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x49,
	0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x66, 0x49,
	0x64, 0x22, 0xab, 0x02, 0x0a, 0x2c, 0x47, 0x65, 0x74, 0x54, 0x78, 0x6e, 0x73, 0x41, 0x6e, 0x64,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x65,
	0x74, 0x77, 0x65, 0x65, 0x6e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x6d, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x70, 0x72,
	0x69, 0x6d, 0x61, 0x72, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x12,
	0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x61, 0x72, 0x79, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64,
	0x61, 0x72, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x0f, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0e, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12,
	0x26, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x28, 0x28, 0x0a, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x12,
	0x1e, 0x0a, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0a, 0x64, 0x65, 0x73, 0x63, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22,
	0xde, 0x01, 0x0a, 0x2d, 0x47, 0x65, 0x74, 0x54, 0x78, 0x6e, 0x73, 0x41, 0x6e, 0x64, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x65, 0x74, 0x77,
	0x65, 0x65, 0x6e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x87, 0x01, 0x0a, 0x25, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68, 0x5f, 0x61, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72, 0x64,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x21, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x22, 0x89, 0x03, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x70, 0x75,
	0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x69,
	0x73, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x52, 0x65, 0x66, 0x12, 0x3b, 0x0a, 0x0f, 0x64,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74,
	0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x65,
	0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x3b, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x3c, 0x0a, 0x15,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x57, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1e, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x22, 0x7c, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41,
	0x0a, 0x07, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x27, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74,
	0x65, 0x22, 0xac, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1e, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12,
	0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0d, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x22, 0x41, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x5d, 0x0a, 0x17, 0x49, 0x73, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65,
	0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e,
	0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x22,
	0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x22, 0x7d, 0x0a, 0x18, 0x49, 0x73, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x41,
	0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xda, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x42, 0x79, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x62, 0x69, 0x6c, 0x6c, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x12,
	0x38, 0x0a, 0x18, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x16, 0x66, 0x65, 0x74, 0x63, 0x68, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x63, 0x0a, 0x2f, 0x66, 0x65, 0x74,
	0x63, 0x68, 0x5f, 0x66, 0x75, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x66, 0x75, 0x6e, 0x64,
	0x5f, 0x72, 0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x5f, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x5f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x2a, 0x66, 0x65, 0x74, 0x63, 0x68, 0x46, 0x75, 0x74, 0x75, 0x72, 0x65, 0x52,
	0x65, 0x66, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x76, 0x65, 0x72, 0x73, 0x61, 0x6c, 0x43, 0x68, 0x69,
	0x6c, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x8b,
	0x03, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x73, 0x42, 0x79, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x85, 0x01, 0x0a, 0x18, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x5f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x73, 0x42, 0x79, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x16, 0x74, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x73, 0x1a, 0xb8, 0x01, 0x0a, 0x15, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78,
	0x6e, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x60, 0x0a, 0x14, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43,
	0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x13, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x22, 0xa2, 0x01, 0x0a,
	0x27, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x2c, 0x0a,
	0x11, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0f, 0x63, 0x72, 0x65, 0x64,
	0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x07, 0x0a, 0x05, 0x47, 0x65, 0x74, 0x42,
	0x79, 0x22, 0xfe, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x55, 0x74, 0x69, 0x6c, 0x69,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x0c, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x61, 0x63, 0x74,
	0x75, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x41, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x12, 0x3b, 0x0a, 0x0f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x5f, 0x75, 0x74, 0x69, 0x6c, 0x69, 0x7a, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0d, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x7a,
	0x65, 0x64, 0x22, 0xa0, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0c,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x2c, 0x0a, 0x11, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0f,
	0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x42, 0x07, 0x0a, 0x05,
	0x47, 0x65, 0x74, 0x42, 0x79, 0x22, 0xff, 0x04, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x75, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x12, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d,
	0x5f, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x10, 0x6d, 0x69, 0x6e, 0x69, 0x6d, 0x75, 0x6d, 0x44, 0x75,
	0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3c, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x64, 0x75, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x75, 0x65, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x35, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x6d, 0x61, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0b, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4d, 0x61, 0x64, 0x65, 0x12, 0x3e, 0x0a, 0x11,
	0x6e, 0x6f, 0x6e, 0x5f, 0x62, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x6e, 0x6f, 0x6e,
	0x42, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x45, 0x0a, 0x14,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x61, 0x63, 0x63, 0x75, 0x6d, 0x75, 0x6c,
	0x61, 0x74, 0x65, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13,
	0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x41, 0x63, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61,
	0x74, 0x65, 0x64, 0x12, 0x2c, 0x0a, 0x08, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x38, 0x0a, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x6d, 0x69, 0x6e, 0x5f,
	0x64, 0x75, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x75,
	0x6e, 0x70, 0x61, 0x69, 0x64, 0x4d, 0x69, 0x6e, 0x44, 0x75, 0x65, 0x12, 0x3c, 0x0a, 0x10, 0x75,
	0x6e, 0x70, 0x61, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x75, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x75, 0x6e, 0x70, 0x61, 0x69,
	0x64, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x75, 0x65, 0x12, 0x4c, 0x0a, 0x18, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x16, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xb0, 0x03, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x54,
	0x78, 0x6e, 0x73, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x8a, 0x01,
	0x0a, 0x24, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x69,
	0x64, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x47,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x20, 0x62, 0x61, 0x74, 0x63, 0x68, 0x44,
	0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x10, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44,
	0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x62, 0x61, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x64, 0x75, 0x70, 0x65, 0x49, 0x64, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x54, 0x78, 0x6e, 0x49,
	0x64, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x62, 0x61, 0x74, 0x63, 0x68, 0x54, 0x78, 0x6e, 0x49, 0x64,
	0x73, 0x12, 0x5e, 0x0a, 0x16, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x73, 0x48, 0x00, 0x52, 0x13, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x78, 0x6e, 0x49, 0x64,
	0x73, 0x42, 0x07, 0x0a, 0x05, 0x47, 0x65, 0x74, 0x42, 0x79, 0x22, 0xd5, 0x01, 0x0a, 0x22, 0x47,
	0x65, 0x74, 0x54, 0x78, 0x6e, 0x73, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x89, 0x01, 0x0a, 0x26, 0x63, 0x61, 0x72, 0x64, 0x5f,
	0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x69, 0x74, 0x68,
	0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x61, 0x72,
	0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x22,
	0x63, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57,
	0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66,
	0x6f, 0x73, 0x22, 0xf0, 0x02, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x78, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1f, 0x0a, 0x0a, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x47, 0x0a,
	0x08, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x65, 0x73, 0x12, 0x73, 0x0a, 0x1c, 0x63, 0x61, 0x72, 0x64, 0x5f, 0x74,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x52, 0x19, 0x63, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74,
	0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0x99, 0x02, 0x0a, 0x29, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x78, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x89, 0x01, 0x0a, 0x26, 0x63, 0x61, 0x72,
	0x64, 0x5f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x77, 0x69,
	0x74, 0x68, 0x5f, 0x61, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69,
	0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x22, 0x63, 0x61, 0x72, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x22, 0x58, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6c, 0x6c, 0x54,
	0x6f, 0x54, 0x78, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x78, 0x6e, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x78, 0x6e, 0x49, 0x64, 0x73, 0x12, 0x1e, 0x0a, 0x0b, 0x62,
	0x69, 0x6c, 0x6c, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x62, 0x69, 0x6c, 0x6c, 0x52, 0x65, 0x66, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x1e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6c, 0x6c, 0x54, 0x6f, 0x54, 0x78, 0x6e, 0x4d, 0x61,
	0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x2f, 0x0a, 0x0e, 0x42, 0x61, 0x74, 0x63, 0x68, 0x44, 0x65, 0x64, 0x75, 0x70,
	0x65, 0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x64, 0x65, 0x64, 0x75, 0x70, 0x65,
	0x49, 0x64, 0x73, 0x22, 0x99, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x61,
	0x74, 0x65, 0x72, 0x61, 0x6c, 0x4c, 0x69, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x77, 0x0a, 0x1f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x61, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x43, 0x6f, 0x6c,
	0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x52, 0x1c, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6c, 0x61,
	0x74, 0x65, 0x72, 0x61, 0x6c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0x9f, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61,
	0x6c, 0x4c, 0x69, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x57, 0x0a, 0x16, 0x63, 0x6f, 0x6c, 0x6c,
	0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x65, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61,
	0x6c, 0x4c, 0x69, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x14, 0x63, 0x6f, 0x6c,
	0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x4c, 0x69, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xab, 0x01, 0x0a, 0x1c, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x41, 0x6e, 0x64, 0x43, 0x6f,
	0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c,
	0x49, 0x64, 0x12, 0x4b, 0x0a, 0x0f, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x61, 0x72, 0x64, 0x50, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x52,
	0x0e, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x32,
	0xfa, 0x17, 0x0a, 0x0a, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x66,
	0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x28, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x25, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5e, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x12, 0x26, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6c, 0x0a, 0x0f, 0x50, 0x6f, 0x73, 0x74, 0x54, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6f,
	0x73, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2a, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x6a, 0x0a, 0x0f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x54, 0x78, 0x6e, 0x52, 0x65, 0x63,
	0x65, 0x69, 0x70, 0x74, 0x12, 0x2a, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x54,
	0x78, 0x6e, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x54, 0x78, 0x6e, 0x52, 0x65,
	0x63, 0x65, 0x69, 0x70, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x78, 0x6e,
	0x73, 0x12, 0x2b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61,
	0x74, 0x65, 0x64, 0x54, 0x78, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64,
	0x54, 0x78, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x94, 0x01, 0x0a,
	0x1d, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x43, 0x72, 0x65,
	0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x78, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x12, 0x38,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64,
	0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x43, 0x61, 0x72, 0x64, 0x54, 0x78, 0x6e, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74,
	0x43, 0x61, 0x72, 0x64, 0x54, 0x78, 0x6e, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xac, 0x01, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x54, 0x78, 0x6e, 0x73, 0x41,
	0x6e, 0x64, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x42, 0x65, 0x74, 0x77, 0x65, 0x65, 0x6e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x40, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x78, 0x6e, 0x73, 0x41, 0x6e, 0x64, 0x41, 0x64, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x65, 0x74, 0x77, 0x65,
	0x65, 0x6e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x41, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x78, 0x6e, 0x73, 0x41, 0x6e, 0x64, 0x41,
	0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x65, 0x74,
	0x77, 0x65, 0x65, 0x6e, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x64, 0x0a, 0x0d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x70,
	0x75, 0x74, 0x65, 0x12, 0x28, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x12, 0x25, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2d, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x66, 0x69, 0x72,
	0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x20, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74,
	0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x3b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6d, 0x0a, 0x10, 0x49, 0x73,
	0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65, 0x64, 0x12, 0x2b,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x49, 0x73, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x41, 0x6c, 0x6c,
	0x6f, 0x77, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x49, 0x73, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x6f, 0x77, 0x65,
	0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9a, 0x01, 0x0a, 0x1f, 0x47, 0x65,
	0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x6f, 0x72,
	0x41, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x3a, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76,
	0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x46, 0x6f,
	0x72, 0x41, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x67, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c,
	0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x73, 0x12, 0x29, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x44, 0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x88, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x73, 0x42, 0x79, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x12, 0x34, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x73, 0x42, 0x79, 0x42, 0x69, 0x6c, 0x6c, 0x49, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x73, 0x42, 0x79, 0x42, 0x69, 0x6c, 0x6c,
	0x49, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x20, 0x47,
	0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x3b, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x66,
	0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e,
	0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x55, 0x74, 0x69, 0x6c, 0x69, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x97, 0x01, 0x0a, 0x1e, 0x47,
	0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44,
	0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x39, 0x2e,
	0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69,
	0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x75, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x75,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44,
	0x69, 0x73, 0x70, 0x75, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31,
	0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x69, 0x73, 0x70, 0x75,
	0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x32, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x65, 0x78, 0x74, 0x44, 0x69,
	0x73, 0x70, 0x75, 0x74, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x54, 0x78, 0x6e,
	0x73, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x12, 0x35, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x78, 0x6e,
	0x73, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49,
	0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x66, 0x69,
	0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67,
	0x2e, 0x47, 0x65, 0x74, 0x54, 0x78, 0x6e, 0x73, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69,
	0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xa0, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e,
	0x61, 0x74, 0x65, 0x64, 0x54, 0x78, 0x6e, 0x57, 0x69, 0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3c, 0x2e, 0x66, 0x69, 0x72, 0x65,
	0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x78, 0x6e, 0x57, 0x69,
	0x74, 0x68, 0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x61, 0x74, 0x65, 0x64, 0x54, 0x78, 0x6e, 0x57, 0x69, 0x74, 0x68,
	0x41, 0x64, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7f, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x42, 0x69, 0x6c, 0x6c, 0x54, 0x6f, 0x54, 0x78, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x12, 0x31, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x69, 0x6c, 0x6c,
	0x54, 0x6f, 0x54, 0x78, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42,
	0x69, 0x6c, 0x6c, 0x54, 0x6f, 0x54, 0x78, 0x6e, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x43,
	0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x4c, 0x69, 0x65, 0x6e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x32, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c, 0x79, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6c, 0x6c,
	0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x4c, 0x69, 0x65, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x74, 0x65, 0x72, 0x61, 0x6c, 0x4c, 0x69, 0x65, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x5e, 0x0a, 0x2d,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x69, 0x72, 0x65, 0x66,
	0x6c, 0x79, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x5a, 0x2d, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x72, 0x65, 0x66, 0x6c,
	0x79, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_firefly_accounting_service_proto_rawDescOnce sync.Once
	file_api_firefly_accounting_service_proto_rawDescData = file_api_firefly_accounting_service_proto_rawDesc
)

func file_api_firefly_accounting_service_proto_rawDescGZIP() []byte {
	file_api_firefly_accounting_service_proto_rawDescOnce.Do(func() {
		file_api_firefly_accounting_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_firefly_accounting_service_proto_rawDescData)
	})
	return file_api_firefly_accounting_service_proto_rawDescData
}

var file_api_firefly_accounting_service_proto_msgTypes = make([]protoimpl.MessageInfo, 60)
var file_api_firefly_accounting_service_proto_goTypes = []interface{}{
	(*GetNextDisputeQuestionRequest)(nil),                           // 0: firefly.accounting.GetNextDisputeQuestionRequest
	(*GetNextDisputeQuestionResponse)(nil),                          // 1: firefly.accounting.GetNextDisputeQuestionResponse
	(*GetAllDisputesRequest)(nil),                                   // 2: firefly.accounting.GetAllDisputesRequest
	(*GetAllDisputesResponse)(nil),                                  // 3: firefly.accounting.GetAllDisputesResponse
	(*GetTransactionsForATimeIntervalRequest)(nil),                  // 4: firefly.accounting.GetTransactionsForATimeIntervalRequest
	(*GetTransactionsForATimeIntervalResponse)(nil),                 // 5: firefly.accounting.GetTransactionsForATimeIntervalResponse
	(*CreateAccountRequest)(nil),                                    // 6: firefly.accounting.CreateAccountRequest
	(*CreateAccountResponse)(nil),                                   // 7: firefly.accounting.CreateAccountResponse
	(*GetAccountsRequest)(nil),                                      // 8: firefly.accounting.GetAccountsRequest
	(*GetAccountsResponse)(nil),                                     // 9: firefly.accounting.GetAccountsResponse
	(*GetAccountRequest)(nil),                                       // 10: firefly.accounting.GetAccountRequest
	(*GetAccountResponse)(nil),                                      // 11: firefly.accounting.GetAccountResponse
	(*GetTransactionsRequest)(nil),                                  // 12: firefly.accounting.GetTransactionsRequest
	(*AccountIdAndCategories)(nil),                                  // 13: firefly.accounting.AccountIdAndCategories
	(*BatchExternalTxnIds)(nil),                                     // 14: firefly.accounting.BatchExternalTxnIds
	(*BatchTxnIds)(nil),                                             // 15: firefly.accounting.BatchTxnIds
	(*BatchDedupeIdGenerationParameter)(nil),                        // 16: firefly.accounting.BatchDedupeIdGenerationParameter
	(*GetTransactionsResponse)(nil),                                 // 17: firefly.accounting.GetTransactionsResponse
	(*PostTransactionRequest)(nil),                                  // 18: firefly.accounting.PostTransactionRequest
	(*PostTransactionResponse)(nil),                                 // 19: firefly.accounting.PostTransactionResponse
	(*FetchTxnReceiptRequest)(nil),                                  // 20: firefly.accounting.FetchTxnReceiptRequest
	(*FetchTxnReceiptResponse)(nil),                                 // 21: firefly.accounting.FetchTxnReceiptResponse
	(*ReceiptAdditionalDetails)(nil),                                // 22: firefly.accounting.ReceiptAdditionalDetails
	(*FeesAdditionalDetails)(nil),                                   // 23: firefly.accounting.FeesAdditionalDetails
	(*GetTransactionWithAdditionalInfoRequest)(nil),                 // 24: firefly.accounting.GetTransactionWithAdditionalInfoRequest
	(*GetTransactionWithAdditionalInfoResponse)(nil),                // 25: firefly.accounting.GetTransactionWithAdditionalInfoResponse
	(*GetPaginatedTxnsRequest)(nil),                                 // 26: firefly.accounting.GetPaginatedTxnsRequest
	(*GetPaginatedTxnsResponse)(nil),                                // 27: firefly.accounting.GetPaginatedTxnsResponse
	(*CardTransactionWithAdditionalInfo)(nil),                       // 28: firefly.accounting.CardTransactionWithAdditionalInfo
	(*GetPaginatedCreditCardTxnViewRequest)(nil),                    // 29: firefly.accounting.GetPaginatedCreditCardTxnViewRequest
	(*GetPaginatedCreditCardTxnViewResponse)(nil),                   // 30: firefly.accounting.GetPaginatedCreditCardTxnViewResponse
	(*CreditCardTransactionViewModel)(nil),                          // 31: firefly.accounting.CreditCardTransactionViewModel
	(*GetTxnsAndAdditionalInfoBetweenActorsRequest)(nil),            // 32: firefly.accounting.GetTxnsAndAdditionalInfoBetweenActorsRequest
	(*GetTxnsAndAdditionalInfoBetweenActorsResponse)(nil),           // 33: firefly.accounting.GetTxnsAndAdditionalInfoBetweenActorsResponse
	(*CreateDisputeRequest)(nil),                                    // 34: firefly.accounting.CreateDisputeRequest
	(*CreateDisputeResponse)(nil),                                   // 35: firefly.accounting.CreateDisputeResponse
	(*GetDisputeRequest)(nil),                                       // 36: firefly.accounting.GetDisputeRequest
	(*GetDisputeResponse)(nil),                                      // 37: firefly.accounting.GetDisputeResponse
	(*UpdateDisputeStateRequest)(nil),                               // 38: firefly.accounting.UpdateDisputeStateRequest
	(*UpdateDisputeStateResponse)(nil),                              // 39: firefly.accounting.UpdateDisputeStateResponse
	(*IsDisputeAllowedRequest)(nil),                                 // 40: firefly.accounting.IsDisputeAllowedRequest
	(*IsDisputeAllowedResponse)(nil),                                // 41: firefly.accounting.IsDisputeAllowedResponse
	(*GetTransactionIdsByBillIdRequest)(nil),                        // 42: firefly.accounting.GetTransactionIdsByBillIdRequest
	(*GetTransactionIdsByBillIdResponse)(nil),                       // 43: firefly.accounting.GetTransactionIdsByBillIdResponse
	(*GetCreditAccountLimitUtilisationRequest)(nil),                 // 44: firefly.accounting.GetCreditAccountLimitUtilisationRequest
	(*GetCreditAccountLimitUtilisationResponse)(nil),                // 45: firefly.accounting.GetCreditAccountLimitUtilisationResponse
	(*GetCreditAccountDueInformationRequest)(nil),                   // 46: firefly.accounting.GetCreditAccountDueInformationRequest
	(*GetCreditAccountDueInformationResponse)(nil),                  // 47: firefly.accounting.GetCreditAccountDueInformationResponse
	(*GetTxnsWithAdditionalInfosRequest)(nil),                       // 48: firefly.accounting.GetTxnsWithAdditionalInfosRequest
	(*GetTxnsWithAdditionalInfosResponse)(nil),                      // 49: firefly.accounting.GetTxnsWithAdditionalInfosResponse
	(*GetPaginatedTxnWithAdditionalInfoRequest)(nil),                // 50: firefly.accounting.GetPaginatedTxnWithAdditionalInfoRequest
	(*GetPaginatedTxnWithAdditionalInfoResponse)(nil),               // 51: firefly.accounting.GetPaginatedTxnWithAdditionalInfoResponse
	(*UpdateBillToTxnMappingRequest)(nil),                           // 52: firefly.accounting.UpdateBillToTxnMappingRequest
	(*UpdateBillToTxnMappingResponse)(nil),                          // 53: firefly.accounting.UpdateBillToTxnMappingResponse
	(*BatchDedupeIds)(nil),                                          // 54: firefly.accounting.BatchDedupeIds
	(*GetCollateralLienStatusRequest)(nil),                          // 55: firefly.accounting.GetCollateralLienStatusRequest
	(*GetCollateralLienStatusResponse)(nil),                         // 56: firefly.accounting.GetCollateralLienStatusResponse
	(*ActorAndCollateralIdentifier)(nil),                            // 57: firefly.accounting.ActorAndCollateralIdentifier
	(*GetAccountRequest_ActorIdAndRefId)(nil),                       // 58: firefly.accounting.GetAccountRequest.ActorIdAndRefId
	(*GetTransactionIdsByBillIdResponse_TransactionIdResponse)(nil), // 59: firefly.accounting.GetTransactionIdsByBillIdResponse.TransactionIdResponse
	(*rpc.Status)(nil),                                              // 60: rpc.Status
	(*dispute.QuestionMeta)(nil),                                    // 61: firefly.accounting.dispute.QuestionMeta
	(*DisputedTransaction)(nil),                                     // 62: firefly.accounting.DisputedTransaction
	(*timestamppb.Timestamp)(nil),                                   // 63: google.protobuf.Timestamp
	(*CardTransaction)(nil),                                         // 64: firefly.accounting.CardTransaction
	(*money.Money)(nil),                                             // 65: google.type.Money
	(*typesv2.CardProgram)(nil),                                     // 66: api.typesv2.CardProgram
	(*CollateralDetails)(nil),                                       // 67: firefly.accounting.CollateralDetails
	(*CreditAccount)(nil),                                           // 68: firefly.accounting.CreditAccount
	(enums.TransactionCategory)(0),                                  // 69: firefly.accounting.enums.TransactionCategory
	(*DedupeId)(nil),                                                // 70: firefly.accounting.DedupeId
	(enums.TransactionStatus)(0),                                    // 71: firefly.accounting.enums.TransactionStatus
	(enums.TransactionType)(0),                                      // 72: firefly.accounting.enums.TransactionType
	(categorizer.DisplayCategory)(0),                                // 73: categorizer.DisplayCategory
	(*common.Name)(nil),                                             // 74: api.typesv2.common.Name
	(enums.CardTransactionFieldMask)(0),                             // 75: firefly.accounting.enums.CardTransactionFieldMask
	(enums.TransactionAdditionalInfoFieldMask)(0),                   // 76: firefly.accounting.enums.TransactionAdditionalInfoFieldMask
	(*TransactionAdditionalInfo)(nil),                               // 77: firefly.accounting.TransactionAdditionalInfo
	(enums.DisputeState)(0),                                         // 78: firefly.accounting.enums.DisputeState
	(*deeplink.Deeplink)(nil),                                       // 79: frontend.deeplink.Deeplink
	(*typesv2.Date)(nil),                                            // 80: api.typesv2.Date
	(*rpc.PageContextRequest)(nil),                                  // 81: rpc.PageContextRequest
	(*rpc.PageContextResponse)(nil),                                 // 82: rpc.PageContextResponse
	(typesv2.CollateralLienStatus)(0),                               // 83: api.typesv2.CollateralLienStatus
	(typesv2.CardProgramCollateral)(0),                              // 84: api.typesv2.CardProgramCollateral
}
var file_api_firefly_accounting_service_proto_depIdxs = []int32{
	60,  // 0: firefly.accounting.GetNextDisputeQuestionResponse.status:type_name -> rpc.Status
	61,  // 1: firefly.accounting.GetNextDisputeQuestionResponse.question_meta:type_name -> firefly.accounting.dispute.QuestionMeta
	60,  // 2: firefly.accounting.GetAllDisputesResponse.status:type_name -> rpc.Status
	62,  // 3: firefly.accounting.GetAllDisputesResponse.disputed_transactions:type_name -> firefly.accounting.DisputedTransaction
	63,  // 4: firefly.accounting.GetTransactionsForATimeIntervalRequest.from_time:type_name -> google.protobuf.Timestamp
	63,  // 5: firefly.accounting.GetTransactionsForATimeIntervalRequest.to_time:type_name -> google.protobuf.Timestamp
	60,  // 6: firefly.accounting.GetTransactionsForATimeIntervalResponse.status:type_name -> rpc.Status
	64,  // 7: firefly.accounting.GetTransactionsForATimeIntervalResponse.transactions:type_name -> firefly.accounting.CardTransaction
	65,  // 8: firefly.accounting.CreateAccountRequest.total_limit:type_name -> google.type.Money
	66,  // 9: firefly.accounting.CreateAccountRequest.card_program:type_name -> api.typesv2.CardProgram
	67,  // 10: firefly.accounting.CreateAccountRequest.collateral_details:type_name -> firefly.accounting.CollateralDetails
	60,  // 11: firefly.accounting.CreateAccountResponse.status:type_name -> rpc.Status
	68,  // 12: firefly.accounting.CreateAccountResponse.account:type_name -> firefly.accounting.CreditAccount
	60,  // 13: firefly.accounting.GetAccountsResponse.status:type_name -> rpc.Status
	68,  // 14: firefly.accounting.GetAccountsResponse.accounts:type_name -> firefly.accounting.CreditAccount
	58,  // 15: firefly.accounting.GetAccountRequest.by_actor_id_and_ref_id:type_name -> firefly.accounting.GetAccountRequest.ActorIdAndRefId
	60,  // 16: firefly.accounting.GetAccountResponse.status:type_name -> rpc.Status
	68,  // 17: firefly.accounting.GetAccountResponse.account:type_name -> firefly.accounting.CreditAccount
	14,  // 18: firefly.accounting.GetTransactionsRequest.batch_external_txn_ids:type_name -> firefly.accounting.BatchExternalTxnIds
	15,  // 19: firefly.accounting.GetTransactionsRequest.batch_txn_ids:type_name -> firefly.accounting.BatchTxnIds
	16,  // 20: firefly.accounting.GetTransactionsRequest.batch_dedupe_id_generation_parameter:type_name -> firefly.accounting.BatchDedupeIdGenerationParameter
	13,  // 21: firefly.accounting.GetTransactionsRequest.account_id_and_categories:type_name -> firefly.accounting.AccountIdAndCategories
	69,  // 22: firefly.accounting.AccountIdAndCategories.transaction_categories:type_name -> firefly.accounting.enums.TransactionCategory
	70,  // 23: firefly.accounting.BatchDedupeIdGenerationParameter.dedupe_id_generation_parameters:type_name -> firefly.accounting.DedupeId
	60,  // 24: firefly.accounting.GetTransactionsResponse.status:type_name -> rpc.Status
	64,  // 25: firefly.accounting.GetTransactionsResponse.transactions:type_name -> firefly.accounting.CardTransaction
	64,  // 26: firefly.accounting.PostTransactionRequest.transaction:type_name -> firefly.accounting.CardTransaction
	60,  // 27: firefly.accounting.PostTransactionResponse.status:type_name -> rpc.Status
	60,  // 28: firefly.accounting.FetchTxnReceiptResponse.status:type_name -> rpc.Status
	65,  // 29: firefly.accounting.FetchTxnReceiptResponse.amount:type_name -> google.type.Money
	71,  // 30: firefly.accounting.FetchTxnReceiptResponse.txn_status:type_name -> firefly.accounting.enums.TransactionStatus
	72,  // 31: firefly.accounting.FetchTxnReceiptResponse.txn_type:type_name -> firefly.accounting.enums.TransactionType
	69,  // 32: firefly.accounting.FetchTxnReceiptResponse.txn_category:type_name -> firefly.accounting.enums.TransactionCategory
	63,  // 33: firefly.accounting.FetchTxnReceiptResponse.txn_time:type_name -> google.protobuf.Timestamp
	73,  // 34: firefly.accounting.FetchTxnReceiptResponse.transaction_display_categories:type_name -> categorizer.DisplayCategory
	74,  // 35: firefly.accounting.FetchTxnReceiptResponse.other_party_name:type_name -> api.typesv2.common.Name
	22,  // 36: firefly.accounting.FetchTxnReceiptResponse.receipt_additional_details:type_name -> firefly.accounting.ReceiptAdditionalDetails
	23,  // 37: firefly.accounting.ReceiptAdditionalDetails.fees_additional_details:type_name -> firefly.accounting.FeesAdditionalDetails
	60,  // 38: firefly.accounting.GetTransactionWithAdditionalInfoResponse.status:type_name -> rpc.Status
	28,  // 39: firefly.accounting.GetTransactionWithAdditionalInfoResponse.card_transaction_with_additional_info:type_name -> firefly.accounting.CardTransactionWithAdditionalInfo
	63,  // 40: firefly.accounting.GetPaginatedTxnsRequest.start_timestamp:type_name -> google.protobuf.Timestamp
	71,  // 41: firefly.accounting.GetPaginatedTxnsRequest.statuses:type_name -> firefly.accounting.enums.TransactionStatus
	75,  // 42: firefly.accounting.GetPaginatedTxnsRequest.card_transaction_field_masks:type_name -> firefly.accounting.enums.CardTransactionFieldMask
	76,  // 43: firefly.accounting.GetPaginatedTxnsRequest.transaction_additional_info_field_masks:type_name -> firefly.accounting.enums.TransactionAdditionalInfoFieldMask
	60,  // 44: firefly.accounting.GetPaginatedTxnsResponse.status:type_name -> rpc.Status
	28,  // 45: firefly.accounting.GetPaginatedTxnsResponse.transaction_with_additional_info_list:type_name -> firefly.accounting.CardTransactionWithAdditionalInfo
	64,  // 46: firefly.accounting.CardTransactionWithAdditionalInfo.transaction:type_name -> firefly.accounting.CardTransaction
	77,  // 47: firefly.accounting.CardTransactionWithAdditionalInfo.additional_info:type_name -> firefly.accounting.TransactionAdditionalInfo
	63,  // 48: firefly.accounting.GetPaginatedCreditCardTxnViewRequest.start_timestamp:type_name -> google.protobuf.Timestamp
	71,  // 49: firefly.accounting.GetPaginatedCreditCardTxnViewRequest.statuses:type_name -> firefly.accounting.enums.TransactionStatus
	60,  // 50: firefly.accounting.GetPaginatedCreditCardTxnViewResponse.status:type_name -> rpc.Status
	31,  // 51: firefly.accounting.GetPaginatedCreditCardTxnViewResponse.txns_view_model_list:type_name -> firefly.accounting.CreditCardTransactionViewModel
	63,  // 52: firefly.accounting.CreditCardTransactionViewModel.transaction_timestamp:type_name -> google.protobuf.Timestamp
	65,  // 53: firefly.accounting.CreditCardTransactionViewModel.amount:type_name -> google.type.Money
	71,  // 54: firefly.accounting.CreditCardTransactionViewModel.transaction_status:type_name -> firefly.accounting.enums.TransactionStatus
	72,  // 55: firefly.accounting.CreditCardTransactionViewModel.transaction_type:type_name -> firefly.accounting.enums.TransactionType
	69,  // 56: firefly.accounting.CreditCardTransactionViewModel.transaction_category:type_name -> firefly.accounting.enums.TransactionCategory
	63,  // 57: firefly.accounting.GetTxnsAndAdditionalInfoBetweenActorsRequest.start_timestamp:type_name -> google.protobuf.Timestamp
	60,  // 58: firefly.accounting.GetTxnsAndAdditionalInfoBetweenActorsResponse.status:type_name -> rpc.Status
	28,  // 59: firefly.accounting.GetTxnsAndAdditionalInfoBetweenActorsResponse.transaction_with_additional_info_list:type_name -> firefly.accounting.CardTransactionWithAdditionalInfo
	65,  // 60: firefly.accounting.CreateDisputeRequest.disputed_amount:type_name -> google.type.Money
	78,  // 61: firefly.accounting.CreateDisputeRequest.dispute_state:type_name -> firefly.accounting.enums.DisputeState
	63,  // 62: firefly.accounting.CreateDisputeRequest.disputed_at:type_name -> google.protobuf.Timestamp
	60,  // 63: firefly.accounting.CreateDisputeResponse.status:type_name -> rpc.Status
	60,  // 64: firefly.accounting.GetDisputeResponse.status:type_name -> rpc.Status
	62,  // 65: firefly.accounting.GetDisputeResponse.dispute:type_name -> firefly.accounting.DisputedTransaction
	78,  // 66: firefly.accounting.UpdateDisputeStateRequest.dispute_state:type_name -> firefly.accounting.enums.DisputeState
	60,  // 67: firefly.accounting.UpdateDisputeStateResponse.status:type_name -> rpc.Status
	60,  // 68: firefly.accounting.IsDisputeAllowedResponse.status:type_name -> rpc.Status
	79,  // 69: firefly.accounting.IsDisputeAllowedResponse.next_action:type_name -> frontend.deeplink.Deeplink
	60,  // 70: firefly.accounting.GetTransactionIdsByBillIdResponse.status:type_name -> rpc.Status
	59,  // 71: firefly.accounting.GetTransactionIdsByBillIdResponse.transaction_id_responses:type_name -> firefly.accounting.GetTransactionIdsByBillIdResponse.TransactionIdResponse
	60,  // 72: firefly.accounting.GetCreditAccountLimitUtilisationResponse.status:type_name -> rpc.Status
	65,  // 73: firefly.accounting.GetCreditAccountLimitUtilisationResponse.limit_actual:type_name -> google.type.Money
	65,  // 74: firefly.accounting.GetCreditAccountLimitUtilisationResponse.limit_available:type_name -> google.type.Money
	65,  // 75: firefly.accounting.GetCreditAccountLimitUtilisationResponse.limit_utilized:type_name -> google.type.Money
	60,  // 76: firefly.accounting.GetCreditAccountDueInformationResponse.status:type_name -> rpc.Status
	65,  // 77: firefly.accounting.GetCreditAccountDueInformationResponse.minimum_due_amount:type_name -> google.type.Money
	65,  // 78: firefly.accounting.GetCreditAccountDueInformationResponse.total_due_amount:type_name -> google.type.Money
	65,  // 79: firefly.accounting.GetCreditAccountDueInformationResponse.payment_made:type_name -> google.type.Money
	65,  // 80: firefly.accounting.GetCreditAccountDueInformationResponse.non_billed_amount:type_name -> google.type.Money
	65,  // 81: firefly.accounting.GetCreditAccountDueInformationResponse.interest_accumulated:type_name -> google.type.Money
	80,  // 82: firefly.accounting.GetCreditAccountDueInformationResponse.due_date:type_name -> api.typesv2.Date
	65,  // 83: firefly.accounting.GetCreditAccountDueInformationResponse.unpaid_min_due:type_name -> google.type.Money
	65,  // 84: firefly.accounting.GetCreditAccountDueInformationResponse.unpaid_total_due:type_name -> google.type.Money
	65,  // 85: firefly.accounting.GetCreditAccountDueInformationResponse.total_outstanding_amount:type_name -> google.type.Money
	16,  // 86: firefly.accounting.GetTxnsWithAdditionalInfosRequest.batch_dedupe_id_generation_parameter:type_name -> firefly.accounting.BatchDedupeIdGenerationParameter
	54,  // 87: firefly.accounting.GetTxnsWithAdditionalInfosRequest.batch_dedupe_ids:type_name -> firefly.accounting.BatchDedupeIds
	15,  // 88: firefly.accounting.GetTxnsWithAdditionalInfosRequest.batch_txn_ids:type_name -> firefly.accounting.BatchTxnIds
	14,  // 89: firefly.accounting.GetTxnsWithAdditionalInfosRequest.batch_external_txn_ids:type_name -> firefly.accounting.BatchExternalTxnIds
	60,  // 90: firefly.accounting.GetTxnsWithAdditionalInfosResponse.status:type_name -> rpc.Status
	28,  // 91: firefly.accounting.GetTxnsWithAdditionalInfosResponse.card_transaction_with_additional_infos:type_name -> firefly.accounting.CardTransactionWithAdditionalInfo
	71,  // 92: firefly.accounting.GetPaginatedTxnWithAdditionalInfoRequest.statuses:type_name -> firefly.accounting.enums.TransactionStatus
	75,  // 93: firefly.accounting.GetPaginatedTxnWithAdditionalInfoRequest.card_transaction_field_masks:type_name -> firefly.accounting.enums.CardTransactionFieldMask
	81,  // 94: firefly.accounting.GetPaginatedTxnWithAdditionalInfoRequest.page_context:type_name -> rpc.PageContextRequest
	60,  // 95: firefly.accounting.GetPaginatedTxnWithAdditionalInfoResponse.status:type_name -> rpc.Status
	28,  // 96: firefly.accounting.GetPaginatedTxnWithAdditionalInfoResponse.card_transaction_with_additional_infos:type_name -> firefly.accounting.CardTransactionWithAdditionalInfo
	82,  // 97: firefly.accounting.GetPaginatedTxnWithAdditionalInfoResponse.page_context:type_name -> rpc.PageContextResponse
	60,  // 98: firefly.accounting.UpdateBillToTxnMappingResponse.status:type_name -> rpc.Status
	57,  // 99: firefly.accounting.GetCollateralLienStatusRequest.actor_and_collateral_identifier:type_name -> firefly.accounting.ActorAndCollateralIdentifier
	60,  // 100: firefly.accounting.GetCollateralLienStatusResponse.status:type_name -> rpc.Status
	83,  // 101: firefly.accounting.GetCollateralLienStatusResponse.collateral_lien_status:type_name -> api.typesv2.CollateralLienStatus
	84,  // 102: firefly.accounting.ActorAndCollateralIdentifier.collateral_type:type_name -> api.typesv2.CardProgramCollateral
	69,  // 103: firefly.accounting.GetTransactionIdsByBillIdResponse.TransactionIdResponse.transaction_category:type_name -> firefly.accounting.enums.TransactionCategory
	6,   // 104: firefly.accounting.Accounting.CreateAccount:input_type -> firefly.accounting.CreateAccountRequest
	10,  // 105: firefly.accounting.Accounting.GetAccount:input_type -> firefly.accounting.GetAccountRequest
	8,   // 106: firefly.accounting.Accounting.GetAccounts:input_type -> firefly.accounting.GetAccountsRequest
	18,  // 107: firefly.accounting.Accounting.PostTransaction:input_type -> firefly.accounting.PostTransactionRequest
	12,  // 108: firefly.accounting.Accounting.GetTransactions:input_type -> firefly.accounting.GetTransactionsRequest
	20,  // 109: firefly.accounting.Accounting.FetchTxnReceipt:input_type -> firefly.accounting.FetchTxnReceiptRequest
	26,  // 110: firefly.accounting.Accounting.GetPaginatedTxns:input_type -> firefly.accounting.GetPaginatedTxnsRequest
	29,  // 111: firefly.accounting.Accounting.GetPaginatedCreditCardTxnView:input_type -> firefly.accounting.GetPaginatedCreditCardTxnViewRequest
	32,  // 112: firefly.accounting.Accounting.GetTxnsAndAdditionalInfoBetweenActors:input_type -> firefly.accounting.GetTxnsAndAdditionalInfoBetweenActorsRequest
	34,  // 113: firefly.accounting.Accounting.CreateDispute:input_type -> firefly.accounting.CreateDisputeRequest
	36,  // 114: firefly.accounting.Accounting.GetDispute:input_type -> firefly.accounting.GetDisputeRequest
	38,  // 115: firefly.accounting.Accounting.UpdateDisputeState:input_type -> firefly.accounting.UpdateDisputeStateRequest
	24,  // 116: firefly.accounting.Accounting.GetTransactionWithAdditionalInfo:input_type -> firefly.accounting.GetTransactionWithAdditionalInfoRequest
	40,  // 117: firefly.accounting.Accounting.IsDisputeAllowed:input_type -> firefly.accounting.IsDisputeAllowedRequest
	4,   // 118: firefly.accounting.Accounting.GetTransactionsForATimeInterval:input_type -> firefly.accounting.GetTransactionsForATimeIntervalRequest
	2,   // 119: firefly.accounting.Accounting.GetAllDisputes:input_type -> firefly.accounting.GetAllDisputesRequest
	42,  // 120: firefly.accounting.Accounting.GetTransactionIdsByBillId:input_type -> firefly.accounting.GetTransactionIdsByBillIdRequest
	44,  // 121: firefly.accounting.Accounting.GetCreditAccountLimitUtilisation:input_type -> firefly.accounting.GetCreditAccountLimitUtilisationRequest
	46,  // 122: firefly.accounting.Accounting.GetCreditAccountDueInformation:input_type -> firefly.accounting.GetCreditAccountDueInformationRequest
	0,   // 123: firefly.accounting.Accounting.GetNextDisputeQuestion:input_type -> firefly.accounting.GetNextDisputeQuestionRequest
	48,  // 124: firefly.accounting.Accounting.GetTxnsWithAdditionalInfos:input_type -> firefly.accounting.GetTxnsWithAdditionalInfosRequest
	50,  // 125: firefly.accounting.Accounting.GetPaginatedTxnWithAdditionalInfo:input_type -> firefly.accounting.GetPaginatedTxnWithAdditionalInfoRequest
	52,  // 126: firefly.accounting.Accounting.UpdateBillToTxnMapping:input_type -> firefly.accounting.UpdateBillToTxnMappingRequest
	55,  // 127: firefly.accounting.Accounting.GetCollateralLienStatus:input_type -> firefly.accounting.GetCollateralLienStatusRequest
	7,   // 128: firefly.accounting.Accounting.CreateAccount:output_type -> firefly.accounting.CreateAccountResponse
	11,  // 129: firefly.accounting.Accounting.GetAccount:output_type -> firefly.accounting.GetAccountResponse
	9,   // 130: firefly.accounting.Accounting.GetAccounts:output_type -> firefly.accounting.GetAccountsResponse
	19,  // 131: firefly.accounting.Accounting.PostTransaction:output_type -> firefly.accounting.PostTransactionResponse
	17,  // 132: firefly.accounting.Accounting.GetTransactions:output_type -> firefly.accounting.GetTransactionsResponse
	21,  // 133: firefly.accounting.Accounting.FetchTxnReceipt:output_type -> firefly.accounting.FetchTxnReceiptResponse
	27,  // 134: firefly.accounting.Accounting.GetPaginatedTxns:output_type -> firefly.accounting.GetPaginatedTxnsResponse
	30,  // 135: firefly.accounting.Accounting.GetPaginatedCreditCardTxnView:output_type -> firefly.accounting.GetPaginatedCreditCardTxnViewResponse
	33,  // 136: firefly.accounting.Accounting.GetTxnsAndAdditionalInfoBetweenActors:output_type -> firefly.accounting.GetTxnsAndAdditionalInfoBetweenActorsResponse
	35,  // 137: firefly.accounting.Accounting.CreateDispute:output_type -> firefly.accounting.CreateDisputeResponse
	37,  // 138: firefly.accounting.Accounting.GetDispute:output_type -> firefly.accounting.GetDisputeResponse
	39,  // 139: firefly.accounting.Accounting.UpdateDisputeState:output_type -> firefly.accounting.UpdateDisputeStateResponse
	25,  // 140: firefly.accounting.Accounting.GetTransactionWithAdditionalInfo:output_type -> firefly.accounting.GetTransactionWithAdditionalInfoResponse
	41,  // 141: firefly.accounting.Accounting.IsDisputeAllowed:output_type -> firefly.accounting.IsDisputeAllowedResponse
	5,   // 142: firefly.accounting.Accounting.GetTransactionsForATimeInterval:output_type -> firefly.accounting.GetTransactionsForATimeIntervalResponse
	3,   // 143: firefly.accounting.Accounting.GetAllDisputes:output_type -> firefly.accounting.GetAllDisputesResponse
	43,  // 144: firefly.accounting.Accounting.GetTransactionIdsByBillId:output_type -> firefly.accounting.GetTransactionIdsByBillIdResponse
	45,  // 145: firefly.accounting.Accounting.GetCreditAccountLimitUtilisation:output_type -> firefly.accounting.GetCreditAccountLimitUtilisationResponse
	47,  // 146: firefly.accounting.Accounting.GetCreditAccountDueInformation:output_type -> firefly.accounting.GetCreditAccountDueInformationResponse
	1,   // 147: firefly.accounting.Accounting.GetNextDisputeQuestion:output_type -> firefly.accounting.GetNextDisputeQuestionResponse
	49,  // 148: firefly.accounting.Accounting.GetTxnsWithAdditionalInfos:output_type -> firefly.accounting.GetTxnsWithAdditionalInfosResponse
	51,  // 149: firefly.accounting.Accounting.GetPaginatedTxnWithAdditionalInfo:output_type -> firefly.accounting.GetPaginatedTxnWithAdditionalInfoResponse
	53,  // 150: firefly.accounting.Accounting.UpdateBillToTxnMapping:output_type -> firefly.accounting.UpdateBillToTxnMappingResponse
	56,  // 151: firefly.accounting.Accounting.GetCollateralLienStatus:output_type -> firefly.accounting.GetCollateralLienStatusResponse
	128, // [128:152] is the sub-list for method output_type
	104, // [104:128] is the sub-list for method input_type
	104, // [104:104] is the sub-list for extension type_name
	104, // [104:104] is the sub-list for extension extendee
	0,   // [0:104] is the sub-list for field type_name
}

func init() { file_api_firefly_accounting_service_proto_init() }
func file_api_firefly_accounting_service_proto_init() {
	if File_api_firefly_accounting_service_proto != nil {
		return
	}
	file_api_firefly_accounting_internal_card_transaction_proto_init()
	file_api_firefly_accounting_internal_credit_account_proto_init()
	file_api_firefly_accounting_internal_disputed_transaction_proto_init()
	file_api_firefly_accounting_internal_transaction_additional_info_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_firefly_accounting_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextDisputeQuestionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetNextDisputeQuestionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllDisputesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllDisputesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionsForATimeIntervalRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionsForATimeIntervalResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AccountIdAndCategories); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchExternalTxnIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchTxnIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDedupeIdGenerationParameter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostTransactionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostTransactionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchTxnReceiptRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchTxnReceiptResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReceiptAdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FeesAdditionalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionWithAdditionalInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionWithAdditionalInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaginatedTxnsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaginatedTxnsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CardTransactionWithAdditionalInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaginatedCreditCardTxnViewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaginatedCreditCardTxnViewResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreditCardTransactionViewModel); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTxnsAndAdditionalInfoBetweenActorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTxnsAndAdditionalInfoBetweenActorsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDisputeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDisputeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDisputeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDisputeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDisputeStateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDisputeStateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsDisputeAllowedRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsDisputeAllowedResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionIdsByBillIdRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionIdsByBillIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditAccountLimitUtilisationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditAccountLimitUtilisationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditAccountDueInformationRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCreditAccountDueInformationResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTxnsWithAdditionalInfosRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTxnsWithAdditionalInfosResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaginatedTxnWithAdditionalInfoRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaginatedTxnWithAdditionalInfoResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBillToTxnMappingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBillToTxnMappingResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchDedupeIds); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCollateralLienStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCollateralLienStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ActorAndCollateralIdentifier); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAccountRequest_ActorIdAndRefId); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_firefly_accounting_service_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTransactionIdsByBillIdResponse_TransactionIdResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_firefly_accounting_service_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*GetAccountsRequest_ActorId)(nil),
	}
	file_api_firefly_accounting_service_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*GetAccountRequest_ByActorIdAndRefId)(nil),
		(*GetAccountRequest_AccountId)(nil),
		(*GetAccountRequest_ReferenceId)(nil),
	}
	file_api_firefly_accounting_service_proto_msgTypes[12].OneofWrappers = []interface{}{
		(*GetTransactionsRequest_TransactionId)(nil),
		(*GetTransactionsRequest_AccountId)(nil),
		(*GetTransactionsRequest_DedupeId)(nil),
		(*GetTransactionsRequest_BatchExternalTxnIds)(nil),
		(*GetTransactionsRequest_BatchTxnIds)(nil),
		(*GetTransactionsRequest_VendorExternalTransactionId)(nil),
		(*GetTransactionsRequest_BatchDedupeIdGenerationParameter)(nil),
		(*GetTransactionsRequest_AccountIdAndCategories)(nil),
	}
	file_api_firefly_accounting_service_proto_msgTypes[22].OneofWrappers = []interface{}{
		(*ReceiptAdditionalDetails_FeesAdditionalDetails)(nil),
	}
	file_api_firefly_accounting_service_proto_msgTypes[24].OneofWrappers = []interface{}{
		(*GetTransactionWithAdditionalInfoRequest_TransactionId)(nil),
		(*GetTransactionWithAdditionalInfoRequest_VendorExternalTransactionId)(nil),
	}
	file_api_firefly_accounting_service_proto_msgTypes[26].OneofWrappers = []interface{}{
		(*GetPaginatedTxnsRequest_AccountId)(nil),
		(*GetPaginatedTxnsRequest_ActorId)(nil),
	}
	file_api_firefly_accounting_service_proto_msgTypes[29].OneofWrappers = []interface{}{
		(*GetPaginatedCreditCardTxnViewRequest_AccountId)(nil),
		(*GetPaginatedCreditCardTxnViewRequest_ActorId)(nil),
	}
	file_api_firefly_accounting_service_proto_msgTypes[44].OneofWrappers = []interface{}{
		(*GetCreditAccountLimitUtilisationRequest_ReferenceId)(nil),
		(*GetCreditAccountLimitUtilisationRequest_CreditAccountId)(nil),
		(*GetCreditAccountLimitUtilisationRequest_ActorId)(nil),
	}
	file_api_firefly_accounting_service_proto_msgTypes[46].OneofWrappers = []interface{}{
		(*GetCreditAccountDueInformationRequest_ReferenceId)(nil),
		(*GetCreditAccountDueInformationRequest_CreditAccountId)(nil),
		(*GetCreditAccountDueInformationRequest_ActorId)(nil),
	}
	file_api_firefly_accounting_service_proto_msgTypes[48].OneofWrappers = []interface{}{
		(*GetTxnsWithAdditionalInfosRequest_BatchDedupeIdGenerationParameter)(nil),
		(*GetTxnsWithAdditionalInfosRequest_BatchDedupeIds)(nil),
		(*GetTxnsWithAdditionalInfosRequest_BatchTxnIds)(nil),
		(*GetTxnsWithAdditionalInfosRequest_BatchExternalTxnIds)(nil),
	}
	file_api_firefly_accounting_service_proto_msgTypes[50].OneofWrappers = []interface{}{
		(*GetPaginatedTxnWithAdditionalInfoRequest_AccountId)(nil),
		(*GetPaginatedTxnWithAdditionalInfoRequest_ActorId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_firefly_accounting_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   60,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_firefly_accounting_service_proto_goTypes,
		DependencyIndexes: file_api_firefly_accounting_service_proto_depIdxs,
		MessageInfos:      file_api_firefly_accounting_service_proto_msgTypes,
	}.Build()
	File_api_firefly_accounting_service_proto = out.File
	file_api_firefly_accounting_service_proto_rawDesc = nil
	file_api_firefly_accounting_service_proto_goTypes = nil
	file_api_firefly_accounting_service_proto_depIdxs = nil
}
