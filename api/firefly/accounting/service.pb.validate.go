// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/firefly/accounting/service.proto

package accounting

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	categorizer "github.com/epifi/gamma/api/categorizer"

	enums "github.com/epifi/gamma/api/firefly/accounting/enums"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = categorizer.DisplayCategory(0)

	_ = enums.TransactionCategory(0)

	_ = typesv2.CollateralLienStatus(0)
)

// Validate checks the field values on GetNextDisputeQuestionRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNextDisputeQuestionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNextDisputeQuestionRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetNextDisputeQuestionRequestMultiError, or nil if none found.
func (m *GetNextDisputeQuestionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNextDisputeQuestionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for QuestionCode

	// no validation rules for Answer

	if len(errors) > 0 {
		return GetNextDisputeQuestionRequestMultiError(errors)
	}

	return nil
}

// GetNextDisputeQuestionRequestMultiError is an error wrapping multiple
// validation errors returned by GetNextDisputeQuestionRequest.ValidateAll()
// if the designated constraints aren't met.
type GetNextDisputeQuestionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNextDisputeQuestionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNextDisputeQuestionRequestMultiError) AllErrors() []error { return m }

// GetNextDisputeQuestionRequestValidationError is the validation error
// returned by GetNextDisputeQuestionRequest.Validate if the designated
// constraints aren't met.
type GetNextDisputeQuestionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNextDisputeQuestionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNextDisputeQuestionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNextDisputeQuestionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNextDisputeQuestionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNextDisputeQuestionRequestValidationError) ErrorName() string {
	return "GetNextDisputeQuestionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNextDisputeQuestionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNextDisputeQuestionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNextDisputeQuestionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNextDisputeQuestionRequestValidationError{}

// Validate checks the field values on GetNextDisputeQuestionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNextDisputeQuestionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNextDisputeQuestionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetNextDisputeQuestionResponseMultiError, or nil if none found.
func (m *GetNextDisputeQuestionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNextDisputeQuestionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextDisputeQuestionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextDisputeQuestionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextDisputeQuestionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetQuestionMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetNextDisputeQuestionResponseValidationError{
					field:  "QuestionMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetNextDisputeQuestionResponseValidationError{
					field:  "QuestionMeta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuestionMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetNextDisputeQuestionResponseValidationError{
				field:  "QuestionMeta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetNextDisputeQuestionResponseMultiError(errors)
	}

	return nil
}

// GetNextDisputeQuestionResponseMultiError is an error wrapping multiple
// validation errors returned by GetNextDisputeQuestionResponse.ValidateAll()
// if the designated constraints aren't met.
type GetNextDisputeQuestionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNextDisputeQuestionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNextDisputeQuestionResponseMultiError) AllErrors() []error { return m }

// GetNextDisputeQuestionResponseValidationError is the validation error
// returned by GetNextDisputeQuestionResponse.Validate if the designated
// constraints aren't met.
type GetNextDisputeQuestionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNextDisputeQuestionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNextDisputeQuestionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNextDisputeQuestionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNextDisputeQuestionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNextDisputeQuestionResponseValidationError) ErrorName() string {
	return "GetNextDisputeQuestionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetNextDisputeQuestionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNextDisputeQuestionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNextDisputeQuestionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNextDisputeQuestionResponseValidationError{}

// Validate checks the field values on GetAllDisputesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllDisputesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllDisputesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllDisputesRequestMultiError, or nil if none found.
func (m *GetAllDisputesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllDisputesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if len(errors) > 0 {
		return GetAllDisputesRequestMultiError(errors)
	}

	return nil
}

// GetAllDisputesRequestMultiError is an error wrapping multiple validation
// errors returned by GetAllDisputesRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAllDisputesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllDisputesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllDisputesRequestMultiError) AllErrors() []error { return m }

// GetAllDisputesRequestValidationError is the validation error returned by
// GetAllDisputesRequest.Validate if the designated constraints aren't met.
type GetAllDisputesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllDisputesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllDisputesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllDisputesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllDisputesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllDisputesRequestValidationError) ErrorName() string {
	return "GetAllDisputesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllDisputesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllDisputesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllDisputesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllDisputesRequestValidationError{}

// Validate checks the field values on GetAllDisputesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAllDisputesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllDisputesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAllDisputesResponseMultiError, or nil if none found.
func (m *GetAllDisputesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllDisputesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllDisputesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllDisputesResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllDisputesResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetDisputedTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllDisputesResponseValidationError{
						field:  fmt.Sprintf("DisputedTransactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllDisputesResponseValidationError{
						field:  fmt.Sprintf("DisputedTransactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllDisputesResponseValidationError{
					field:  fmt.Sprintf("DisputedTransactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAllDisputesResponseMultiError(errors)
	}

	return nil
}

// GetAllDisputesResponseMultiError is an error wrapping multiple validation
// errors returned by GetAllDisputesResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAllDisputesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllDisputesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllDisputesResponseMultiError) AllErrors() []error { return m }

// GetAllDisputesResponseValidationError is the validation error returned by
// GetAllDisputesResponse.Validate if the designated constraints aren't met.
type GetAllDisputesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllDisputesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllDisputesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllDisputesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllDisputesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllDisputesResponseValidationError) ErrorName() string {
	return "GetAllDisputesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllDisputesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllDisputesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllDisputesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllDisputesResponseValidationError{}

// Validate checks the field values on GetTransactionsForATimeIntervalRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetTransactionsForATimeIntervalRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTransactionsForATimeIntervalRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetTransactionsForATimeIntervalRequestMultiError, or nil if none found.
func (m *GetTransactionsForATimeIntervalRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionsForATimeIntervalRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RefId

	if all {
		switch v := interface{}(m.GetFromTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionsForATimeIntervalRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionsForATimeIntervalRequestValidationError{
					field:  "FromTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFromTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionsForATimeIntervalRequestValidationError{
				field:  "FromTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetToTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionsForATimeIntervalRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionsForATimeIntervalRequestValidationError{
					field:  "ToTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetToTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionsForATimeIntervalRequestValidationError{
				field:  "ToTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IncludeFrmDeclineTxns

	if len(errors) > 0 {
		return GetTransactionsForATimeIntervalRequestMultiError(errors)
	}

	return nil
}

// GetTransactionsForATimeIntervalRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetTransactionsForATimeIntervalRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionsForATimeIntervalRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionsForATimeIntervalRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionsForATimeIntervalRequestMultiError) AllErrors() []error { return m }

// GetTransactionsForATimeIntervalRequestValidationError is the validation
// error returned by GetTransactionsForATimeIntervalRequest.Validate if the
// designated constraints aren't met.
type GetTransactionsForATimeIntervalRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionsForATimeIntervalRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionsForATimeIntervalRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionsForATimeIntervalRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionsForATimeIntervalRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionsForATimeIntervalRequestValidationError) ErrorName() string {
	return "GetTransactionsForATimeIntervalRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionsForATimeIntervalRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionsForATimeIntervalRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionsForATimeIntervalRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionsForATimeIntervalRequestValidationError{}

// Validate checks the field values on GetTransactionsForATimeIntervalResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetTransactionsForATimeIntervalResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTransactionsForATimeIntervalResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetTransactionsForATimeIntervalResponseMultiError, or nil if none found.
func (m *GetTransactionsForATimeIntervalResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionsForATimeIntervalResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionsForATimeIntervalResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionsForATimeIntervalResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionsForATimeIntervalResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionsForATimeIntervalResponseValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionsForATimeIntervalResponseValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionsForATimeIntervalResponseValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTransactionsForATimeIntervalResponseMultiError(errors)
	}

	return nil
}

// GetTransactionsForATimeIntervalResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetTransactionsForATimeIntervalResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionsForATimeIntervalResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionsForATimeIntervalResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionsForATimeIntervalResponseMultiError) AllErrors() []error { return m }

// GetTransactionsForATimeIntervalResponseValidationError is the validation
// error returned by GetTransactionsForATimeIntervalResponse.Validate if the
// designated constraints aren't met.
type GetTransactionsForATimeIntervalResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionsForATimeIntervalResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionsForATimeIntervalResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionsForATimeIntervalResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionsForATimeIntervalResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionsForATimeIntervalResponseValidationError) ErrorName() string {
	return "GetTransactionsForATimeIntervalResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionsForATimeIntervalResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionsForATimeIntervalResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionsForATimeIntervalResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionsForATimeIntervalResponseValidationError{}

// Validate checks the field values on CreateAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAccountRequestMultiError, or nil if none found.
func (m *CreateAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ReferenceId

	if all {
		switch v := interface{}(m.GetTotalLimit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "TotalLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "TotalLimit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalLimit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountRequestValidationError{
				field:  "TotalLimit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardProgram()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "CardProgram",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardProgram()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountRequestValidationError{
				field:  "CardProgram",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCollateralDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "CollateralDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountRequestValidationError{
					field:  "CollateralDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCollateralDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountRequestValidationError{
				field:  "CollateralDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAccountRequestMultiError(errors)
	}

	return nil
}

// CreateAccountRequestMultiError is an error wrapping multiple validation
// errors returned by CreateAccountRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAccountRequestMultiError) AllErrors() []error { return m }

// CreateAccountRequestValidationError is the validation error returned by
// CreateAccountRequest.Validate if the designated constraints aren't met.
type CreateAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAccountRequestValidationError) ErrorName() string {
	return "CreateAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAccountRequestValidationError{}

// Validate checks the field values on CreateAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateAccountResponseMultiError, or nil if none found.
func (m *CreateAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateAccountResponseMultiError(errors)
	}

	return nil
}

// CreateAccountResponseMultiError is an error wrapping multiple validation
// errors returned by CreateAccountResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateAccountResponseMultiError) AllErrors() []error { return m }

// CreateAccountResponseValidationError is the validation error returned by
// CreateAccountResponse.Validate if the designated constraints aren't met.
type CreateAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateAccountResponseValidationError) ErrorName() string {
	return "CreateAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateAccountResponseValidationError{}

// Validate checks the field values on GetAccountsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountsRequestMultiError, or nil if none found.
func (m *GetAccountsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.GetBy.(type) {
	case *GetAccountsRequest_ActorId:
		if v == nil {
			err := GetAccountsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAccountsRequestMultiError(errors)
	}

	return nil
}

// GetAccountsRequestMultiError is an error wrapping multiple validation errors
// returned by GetAccountsRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAccountsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountsRequestMultiError) AllErrors() []error { return m }

// GetAccountsRequestValidationError is the validation error returned by
// GetAccountsRequest.Validate if the designated constraints aren't met.
type GetAccountsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountsRequestValidationError) ErrorName() string {
	return "GetAccountsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountsRequestValidationError{}

// Validate checks the field values on GetAccountsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountsResponseMultiError, or nil if none found.
func (m *GetAccountsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAccounts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountsResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountsResponseValidationError{
						field:  fmt.Sprintf("Accounts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountsResponseValidationError{
					field:  fmt.Sprintf("Accounts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAccountsResponseMultiError(errors)
	}

	return nil
}

// GetAccountsResponseMultiError is an error wrapping multiple validation
// errors returned by GetAccountsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAccountsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountsResponseMultiError) AllErrors() []error { return m }

// GetAccountsResponseValidationError is the validation error returned by
// GetAccountsResponse.Validate if the designated constraints aren't met.
type GetAccountsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountsResponseValidationError) ErrorName() string {
	return "GetAccountsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountsResponseValidationError{}

// Validate checks the field values on GetAccountRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetAccountRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountRequestMultiError, or nil if none found.
func (m *GetAccountRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.GetBy.(type) {
	case *GetAccountRequest_ByActorIdAndRefId:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetByActorIdAndRefId()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAccountRequestValidationError{
						field:  "ByActorIdAndRefId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAccountRequestValidationError{
						field:  "ByActorIdAndRefId",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetByActorIdAndRefId()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAccountRequestValidationError{
					field:  "ByActorIdAndRefId",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetAccountRequest_AccountId:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AccountId
	case *GetAccountRequest_ReferenceId:
		if v == nil {
			err := GetAccountRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ReferenceId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetAccountRequestMultiError(errors)
	}

	return nil
}

// GetAccountRequestMultiError is an error wrapping multiple validation errors
// returned by GetAccountRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAccountRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountRequestMultiError) AllErrors() []error { return m }

// GetAccountRequestValidationError is the validation error returned by
// GetAccountRequest.Validate if the designated constraints aren't met.
type GetAccountRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountRequestValidationError) ErrorName() string {
	return "GetAccountRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountRequestValidationError{}

// Validate checks the field values on GetAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAccountResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAccountResponseMultiError, or nil if none found.
func (m *GetAccountResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAccount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAccountResponseValidationError{
					field:  "Account",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAccount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAccountResponseValidationError{
				field:  "Account",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAccountResponseMultiError(errors)
	}

	return nil
}

// GetAccountResponseMultiError is an error wrapping multiple validation errors
// returned by GetAccountResponse.ValidateAll() if the designated constraints
// aren't met.
type GetAccountResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountResponseMultiError) AllErrors() []error { return m }

// GetAccountResponseValidationError is the validation error returned by
// GetAccountResponse.Validate if the designated constraints aren't met.
type GetAccountResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountResponseValidationError) ErrorName() string {
	return "GetAccountResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountResponseValidationError{}

// Validate checks the field values on GetTransactionsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionsRequestMultiError, or nil if none found.
func (m *GetTransactionsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.GetBy.(type) {
	case *GetTransactionsRequest_TransactionId:
		if v == nil {
			err := GetTransactionsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for TransactionId
	case *GetTransactionsRequest_AccountId:
		if v == nil {
			err := GetTransactionsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AccountId
	case *GetTransactionsRequest_DedupeId:
		if v == nil {
			err := GetTransactionsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for DedupeId
	case *GetTransactionsRequest_BatchExternalTxnIds:
		if v == nil {
			err := GetTransactionsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBatchExternalTxnIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionsRequestValidationError{
						field:  "BatchExternalTxnIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionsRequestValidationError{
						field:  "BatchExternalTxnIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBatchExternalTxnIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionsRequestValidationError{
					field:  "BatchExternalTxnIds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetTransactionsRequest_BatchTxnIds:
		if v == nil {
			err := GetTransactionsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBatchTxnIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionsRequestValidationError{
						field:  "BatchTxnIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionsRequestValidationError{
						field:  "BatchTxnIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBatchTxnIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionsRequestValidationError{
					field:  "BatchTxnIds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetTransactionsRequest_VendorExternalTransactionId:
		if v == nil {
			err := GetTransactionsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for VendorExternalTransactionId
	case *GetTransactionsRequest_BatchDedupeIdGenerationParameter:
		if v == nil {
			err := GetTransactionsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBatchDedupeIdGenerationParameter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionsRequestValidationError{
						field:  "BatchDedupeIdGenerationParameter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionsRequestValidationError{
						field:  "BatchDedupeIdGenerationParameter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBatchDedupeIdGenerationParameter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionsRequestValidationError{
					field:  "BatchDedupeIdGenerationParameter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetTransactionsRequest_AccountIdAndCategories:
		if v == nil {
			err := GetTransactionsRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetAccountIdAndCategories()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionsRequestValidationError{
						field:  "AccountIdAndCategories",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionsRequestValidationError{
						field:  "AccountIdAndCategories",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetAccountIdAndCategories()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionsRequestValidationError{
					field:  "AccountIdAndCategories",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetTransactionsRequestMultiError(errors)
	}

	return nil
}

// GetTransactionsRequestMultiError is an error wrapping multiple validation
// errors returned by GetTransactionsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionsRequestMultiError) AllErrors() []error { return m }

// GetTransactionsRequestValidationError is the validation error returned by
// GetTransactionsRequest.Validate if the designated constraints aren't met.
type GetTransactionsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionsRequestValidationError) ErrorName() string {
	return "GetTransactionsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionsRequestValidationError{}

// Validate checks the field values on AccountIdAndCategories with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AccountIdAndCategories) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AccountIdAndCategories with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AccountIdAndCategoriesMultiError, or nil if none found.
func (m *AccountIdAndCategories) ValidateAll() error {
	return m.validate(true)
}

func (m *AccountIdAndCategories) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if len(errors) > 0 {
		return AccountIdAndCategoriesMultiError(errors)
	}

	return nil
}

// AccountIdAndCategoriesMultiError is an error wrapping multiple validation
// errors returned by AccountIdAndCategories.ValidateAll() if the designated
// constraints aren't met.
type AccountIdAndCategoriesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AccountIdAndCategoriesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AccountIdAndCategoriesMultiError) AllErrors() []error { return m }

// AccountIdAndCategoriesValidationError is the validation error returned by
// AccountIdAndCategories.Validate if the designated constraints aren't met.
type AccountIdAndCategoriesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AccountIdAndCategoriesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AccountIdAndCategoriesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AccountIdAndCategoriesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AccountIdAndCategoriesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AccountIdAndCategoriesValidationError) ErrorName() string {
	return "AccountIdAndCategoriesValidationError"
}

// Error satisfies the builtin error interface
func (e AccountIdAndCategoriesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAccountIdAndCategories.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AccountIdAndCategoriesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AccountIdAndCategoriesValidationError{}

// Validate checks the field values on BatchExternalTxnIds with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchExternalTxnIds) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchExternalTxnIds with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchExternalTxnIdsMultiError, or nil if none found.
func (m *BatchExternalTxnIds) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchExternalTxnIds) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchExternalTxnIdsMultiError(errors)
	}

	return nil
}

// BatchExternalTxnIdsMultiError is an error wrapping multiple validation
// errors returned by BatchExternalTxnIds.ValidateAll() if the designated
// constraints aren't met.
type BatchExternalTxnIdsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchExternalTxnIdsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchExternalTxnIdsMultiError) AllErrors() []error { return m }

// BatchExternalTxnIdsValidationError is the validation error returned by
// BatchExternalTxnIds.Validate if the designated constraints aren't met.
type BatchExternalTxnIdsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchExternalTxnIdsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchExternalTxnIdsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchExternalTxnIdsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchExternalTxnIdsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchExternalTxnIdsValidationError) ErrorName() string {
	return "BatchExternalTxnIdsValidationError"
}

// Error satisfies the builtin error interface
func (e BatchExternalTxnIdsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchExternalTxnIds.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchExternalTxnIdsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchExternalTxnIdsValidationError{}

// Validate checks the field values on BatchTxnIds with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BatchTxnIds) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchTxnIds with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BatchTxnIdsMultiError, or
// nil if none found.
func (m *BatchTxnIds) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchTxnIds) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchTxnIdsMultiError(errors)
	}

	return nil
}

// BatchTxnIdsMultiError is an error wrapping multiple validation errors
// returned by BatchTxnIds.ValidateAll() if the designated constraints aren't met.
type BatchTxnIdsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchTxnIdsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchTxnIdsMultiError) AllErrors() []error { return m }

// BatchTxnIdsValidationError is the validation error returned by
// BatchTxnIds.Validate if the designated constraints aren't met.
type BatchTxnIdsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchTxnIdsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchTxnIdsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchTxnIdsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchTxnIdsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchTxnIdsValidationError) ErrorName() string { return "BatchTxnIdsValidationError" }

// Error satisfies the builtin error interface
func (e BatchTxnIdsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchTxnIds.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchTxnIdsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchTxnIdsValidationError{}

// Validate checks the field values on BatchDedupeIdGenerationParameter with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *BatchDedupeIdGenerationParameter) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDedupeIdGenerationParameter with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// BatchDedupeIdGenerationParameterMultiError, or nil if none found.
func (m *BatchDedupeIdGenerationParameter) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDedupeIdGenerationParameter) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDedupeIdGenerationParameters() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchDedupeIdGenerationParameterValidationError{
						field:  fmt.Sprintf("DedupeIdGenerationParameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchDedupeIdGenerationParameterValidationError{
						field:  fmt.Sprintf("DedupeIdGenerationParameters[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchDedupeIdGenerationParameterValidationError{
					field:  fmt.Sprintf("DedupeIdGenerationParameters[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchDedupeIdGenerationParameterMultiError(errors)
	}

	return nil
}

// BatchDedupeIdGenerationParameterMultiError is an error wrapping multiple
// validation errors returned by
// BatchDedupeIdGenerationParameter.ValidateAll() if the designated
// constraints aren't met.
type BatchDedupeIdGenerationParameterMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDedupeIdGenerationParameterMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDedupeIdGenerationParameterMultiError) AllErrors() []error { return m }

// BatchDedupeIdGenerationParameterValidationError is the validation error
// returned by BatchDedupeIdGenerationParameter.Validate if the designated
// constraints aren't met.
type BatchDedupeIdGenerationParameterValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDedupeIdGenerationParameterValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDedupeIdGenerationParameterValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDedupeIdGenerationParameterValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDedupeIdGenerationParameterValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDedupeIdGenerationParameterValidationError) ErrorName() string {
	return "BatchDedupeIdGenerationParameterValidationError"
}

// Error satisfies the builtin error interface
func (e BatchDedupeIdGenerationParameterValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDedupeIdGenerationParameter.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDedupeIdGenerationParameterValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDedupeIdGenerationParameterValidationError{}

// Validate checks the field values on GetTransactionsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionsResponseMultiError, or nil if none found.
func (m *GetTransactionsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransactions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionsResponseValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionsResponseValidationError{
						field:  fmt.Sprintf("Transactions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionsResponseValidationError{
					field:  fmt.Sprintf("Transactions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTransactionsResponseMultiError(errors)
	}

	return nil
}

// GetTransactionsResponseMultiError is an error wrapping multiple validation
// errors returned by GetTransactionsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionsResponseMultiError) AllErrors() []error { return m }

// GetTransactionsResponseValidationError is the validation error returned by
// GetTransactionsResponse.Validate if the designated constraints aren't met.
type GetTransactionsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionsResponseValidationError) ErrorName() string {
	return "GetTransactionsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionsResponseValidationError{}

// Validate checks the field values on PostTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostTransactionRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostTransactionRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostTransactionRequestMultiError, or nil if none found.
func (m *PostTransactionRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PostTransactionRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountId

	if all {
		switch v := interface{}(m.GetTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostTransactionRequestValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostTransactionRequestValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostTransactionRequestValidationError{
				field:  "Transaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostTransactionRequestMultiError(errors)
	}

	return nil
}

// PostTransactionRequestMultiError is an error wrapping multiple validation
// errors returned by PostTransactionRequest.ValidateAll() if the designated
// constraints aren't met.
type PostTransactionRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostTransactionRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostTransactionRequestMultiError) AllErrors() []error { return m }

// PostTransactionRequestValidationError is the validation error returned by
// PostTransactionRequest.Validate if the designated constraints aren't met.
type PostTransactionRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostTransactionRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostTransactionRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostTransactionRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostTransactionRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostTransactionRequestValidationError) ErrorName() string {
	return "PostTransactionRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PostTransactionRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostTransactionRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostTransactionRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostTransactionRequestValidationError{}

// Validate checks the field values on PostTransactionResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostTransactionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostTransactionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostTransactionResponseMultiError, or nil if none found.
func (m *PostTransactionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PostTransactionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostTransactionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostTransactionResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostTransactionResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostTransactionResponseMultiError(errors)
	}

	return nil
}

// PostTransactionResponseMultiError is an error wrapping multiple validation
// errors returned by PostTransactionResponse.ValidateAll() if the designated
// constraints aren't met.
type PostTransactionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostTransactionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostTransactionResponseMultiError) AllErrors() []error { return m }

// PostTransactionResponseValidationError is the validation error returned by
// PostTransactionResponse.Validate if the designated constraints aren't met.
type PostTransactionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostTransactionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostTransactionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostTransactionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostTransactionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostTransactionResponseValidationError) ErrorName() string {
	return "PostTransactionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PostTransactionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostTransactionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostTransactionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostTransactionResponseValidationError{}

// Validate checks the field values on FetchTxnReceiptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchTxnReceiptRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchTxnReceiptRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchTxnReceiptRequestMultiError, or nil if none found.
func (m *FetchTxnReceiptRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchTxnReceiptRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnId

	if len(errors) > 0 {
		return FetchTxnReceiptRequestMultiError(errors)
	}

	return nil
}

// FetchTxnReceiptRequestMultiError is an error wrapping multiple validation
// errors returned by FetchTxnReceiptRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchTxnReceiptRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchTxnReceiptRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchTxnReceiptRequestMultiError) AllErrors() []error { return m }

// FetchTxnReceiptRequestValidationError is the validation error returned by
// FetchTxnReceiptRequest.Validate if the designated constraints aren't met.
type FetchTxnReceiptRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchTxnReceiptRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchTxnReceiptRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchTxnReceiptRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchTxnReceiptRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchTxnReceiptRequestValidationError) ErrorName() string {
	return "FetchTxnReceiptRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchTxnReceiptRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchTxnReceiptRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchTxnReceiptRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchTxnReceiptRequestValidationError{}

// Validate checks the field values on FetchTxnReceiptResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchTxnReceiptResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchTxnReceiptResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchTxnReceiptResponseMultiError, or nil if none found.
func (m *FetchTxnReceiptResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchTxnReceiptResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchTxnReceiptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchTxnReceiptResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchTxnReceiptResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExternalTxnId

	// no validation rules for IconUrl

	// no validation rules for Remarks

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchTxnReceiptResponseValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchTxnReceiptResponseValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchTxnReceiptResponseValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FromName

	// no validation rules for ToName

	// no validation rules for TxnStatus

	// no validation rules for TxnType

	// no validation rules for TxnCategory

	if all {
		switch v := interface{}(m.GetTxnTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchTxnReceiptResponseValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchTxnReceiptResponseValidationError{
					field:  "TxnTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTxnTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchTxnReceiptResponseValidationError{
				field:  "TxnTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PartnerUrl

	if all {
		switch v := interface{}(m.GetOtherPartyName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchTxnReceiptResponseValidationError{
					field:  "OtherPartyName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchTxnReceiptResponseValidationError{
					field:  "OtherPartyName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtherPartyName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchTxnReceiptResponseValidationError{
				field:  "OtherPartyName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReceiptAdditionalDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchTxnReceiptResponseValidationError{
					field:  "ReceiptAdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchTxnReceiptResponseValidationError{
					field:  "ReceiptAdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReceiptAdditionalDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchTxnReceiptResponseValidationError{
				field:  "ReceiptAdditionalDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VendorExternalTxnId

	if len(errors) > 0 {
		return FetchTxnReceiptResponseMultiError(errors)
	}

	return nil
}

// FetchTxnReceiptResponseMultiError is an error wrapping multiple validation
// errors returned by FetchTxnReceiptResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchTxnReceiptResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchTxnReceiptResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchTxnReceiptResponseMultiError) AllErrors() []error { return m }

// FetchTxnReceiptResponseValidationError is the validation error returned by
// FetchTxnReceiptResponse.Validate if the designated constraints aren't met.
type FetchTxnReceiptResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchTxnReceiptResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchTxnReceiptResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchTxnReceiptResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchTxnReceiptResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchTxnReceiptResponseValidationError) ErrorName() string {
	return "FetchTxnReceiptResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchTxnReceiptResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchTxnReceiptResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchTxnReceiptResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchTxnReceiptResponseValidationError{}

// Validate checks the field values on ReceiptAdditionalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReceiptAdditionalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReceiptAdditionalDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReceiptAdditionalDetailsMultiError, or nil if none found.
func (m *ReceiptAdditionalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ReceiptAdditionalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Details.(type) {
	case *ReceiptAdditionalDetails_FeesAdditionalDetails:
		if v == nil {
			err := ReceiptAdditionalDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFeesAdditionalDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ReceiptAdditionalDetailsValidationError{
						field:  "FeesAdditionalDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ReceiptAdditionalDetailsValidationError{
						field:  "FeesAdditionalDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFeesAdditionalDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ReceiptAdditionalDetailsValidationError{
					field:  "FeesAdditionalDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return ReceiptAdditionalDetailsMultiError(errors)
	}

	return nil
}

// ReceiptAdditionalDetailsMultiError is an error wrapping multiple validation
// errors returned by ReceiptAdditionalDetails.ValidateAll() if the designated
// constraints aren't met.
type ReceiptAdditionalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReceiptAdditionalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReceiptAdditionalDetailsMultiError) AllErrors() []error { return m }

// ReceiptAdditionalDetailsValidationError is the validation error returned by
// ReceiptAdditionalDetails.Validate if the designated constraints aren't met.
type ReceiptAdditionalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReceiptAdditionalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReceiptAdditionalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReceiptAdditionalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReceiptAdditionalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReceiptAdditionalDetailsValidationError) ErrorName() string {
	return "ReceiptAdditionalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ReceiptAdditionalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReceiptAdditionalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReceiptAdditionalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReceiptAdditionalDetailsValidationError{}

// Validate checks the field values on FeesAdditionalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FeesAdditionalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FeesAdditionalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FeesAdditionalDetailsMultiError, or nil if none found.
func (m *FeesAdditionalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FeesAdditionalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	if len(errors) > 0 {
		return FeesAdditionalDetailsMultiError(errors)
	}

	return nil
}

// FeesAdditionalDetailsMultiError is an error wrapping multiple validation
// errors returned by FeesAdditionalDetails.ValidateAll() if the designated
// constraints aren't met.
type FeesAdditionalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FeesAdditionalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FeesAdditionalDetailsMultiError) AllErrors() []error { return m }

// FeesAdditionalDetailsValidationError is the validation error returned by
// FeesAdditionalDetails.Validate if the designated constraints aren't met.
type FeesAdditionalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FeesAdditionalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FeesAdditionalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FeesAdditionalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FeesAdditionalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FeesAdditionalDetailsValidationError) ErrorName() string {
	return "FeesAdditionalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FeesAdditionalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFeesAdditionalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FeesAdditionalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FeesAdditionalDetailsValidationError{}

// Validate checks the field values on GetTransactionWithAdditionalInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetTransactionWithAdditionalInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTransactionWithAdditionalInfoRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetTransactionWithAdditionalInfoRequestMultiError, or nil if none found.
func (m *GetTransactionWithAdditionalInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionWithAdditionalInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.GetBy.(type) {
	case *GetTransactionWithAdditionalInfoRequest_TransactionId:
		if v == nil {
			err := GetTransactionWithAdditionalInfoRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for TransactionId
	case *GetTransactionWithAdditionalInfoRequest_VendorExternalTransactionId:
		if v == nil {
			err := GetTransactionWithAdditionalInfoRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for VendorExternalTransactionId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetTransactionWithAdditionalInfoRequestMultiError(errors)
	}

	return nil
}

// GetTransactionWithAdditionalInfoRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetTransactionWithAdditionalInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionWithAdditionalInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionWithAdditionalInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionWithAdditionalInfoRequestMultiError) AllErrors() []error { return m }

// GetTransactionWithAdditionalInfoRequestValidationError is the validation
// error returned by GetTransactionWithAdditionalInfoRequest.Validate if the
// designated constraints aren't met.
type GetTransactionWithAdditionalInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionWithAdditionalInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionWithAdditionalInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionWithAdditionalInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionWithAdditionalInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionWithAdditionalInfoRequestValidationError) ErrorName() string {
	return "GetTransactionWithAdditionalInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionWithAdditionalInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionWithAdditionalInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionWithAdditionalInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionWithAdditionalInfoRequestValidationError{}

// Validate checks the field values on GetTransactionWithAdditionalInfoResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetTransactionWithAdditionalInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTransactionWithAdditionalInfoResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetTransactionWithAdditionalInfoResponseMultiError, or nil if none found.
func (m *GetTransactionWithAdditionalInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionWithAdditionalInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionWithAdditionalInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionWithAdditionalInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionWithAdditionalInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCardTransactionWithAdditionalInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionWithAdditionalInfoResponseValidationError{
					field:  "CardTransactionWithAdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionWithAdditionalInfoResponseValidationError{
					field:  "CardTransactionWithAdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCardTransactionWithAdditionalInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionWithAdditionalInfoResponseValidationError{
				field:  "CardTransactionWithAdditionalInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetTransactionWithAdditionalInfoResponseMultiError(errors)
	}

	return nil
}

// GetTransactionWithAdditionalInfoResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetTransactionWithAdditionalInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionWithAdditionalInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionWithAdditionalInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionWithAdditionalInfoResponseMultiError) AllErrors() []error { return m }

// GetTransactionWithAdditionalInfoResponseValidationError is the validation
// error returned by GetTransactionWithAdditionalInfoResponse.Validate if the
// designated constraints aren't met.
type GetTransactionWithAdditionalInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionWithAdditionalInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionWithAdditionalInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionWithAdditionalInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionWithAdditionalInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionWithAdditionalInfoResponseValidationError) ErrorName() string {
	return "GetTransactionWithAdditionalInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionWithAdditionalInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionWithAdditionalInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionWithAdditionalInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionWithAdditionalInfoResponseValidationError{}

// Validate checks the field values on GetPaginatedTxnsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPaginatedTxnsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaginatedTxnsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPaginatedTxnsRequestMultiError, or nil if none found.
func (m *GetPaginatedTxnsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaginatedTxnsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStartTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedTxnsRequestValidationError{
					field:  "StartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedTxnsRequestValidationError{
					field:  "StartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedTxnsRequestValidationError{
				field:  "StartTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 10 || val > 40 {
		err := GetPaginatedTxnsRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [10, 40]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Offset

	// no validation rules for Descending

	// no validation rules for IncludeFrmDeclineTxns

	switch v := m.Identifier.(type) {
	case *GetPaginatedTxnsRequest_AccountId:
		if v == nil {
			err := GetPaginatedTxnsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AccountId
	case *GetPaginatedTxnsRequest_ActorId:
		if v == nil {
			err := GetPaginatedTxnsRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetPaginatedTxnsRequestMultiError(errors)
	}

	return nil
}

// GetPaginatedTxnsRequestMultiError is an error wrapping multiple validation
// errors returned by GetPaginatedTxnsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPaginatedTxnsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaginatedTxnsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaginatedTxnsRequestMultiError) AllErrors() []error { return m }

// GetPaginatedTxnsRequestValidationError is the validation error returned by
// GetPaginatedTxnsRequest.Validate if the designated constraints aren't met.
type GetPaginatedTxnsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaginatedTxnsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaginatedTxnsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaginatedTxnsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaginatedTxnsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaginatedTxnsRequestValidationError) ErrorName() string {
	return "GetPaginatedTxnsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaginatedTxnsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaginatedTxnsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaginatedTxnsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaginatedTxnsRequestValidationError{}

// Validate checks the field values on GetPaginatedTxnsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPaginatedTxnsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaginatedTxnsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPaginatedTxnsResponseMultiError, or nil if none found.
func (m *GetPaginatedTxnsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaginatedTxnsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedTxnsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedTxnsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedTxnsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransactionWithAdditionalInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaginatedTxnsResponseValidationError{
						field:  fmt.Sprintf("TransactionWithAdditionalInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaginatedTxnsResponseValidationError{
						field:  fmt.Sprintf("TransactionWithAdditionalInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaginatedTxnsResponseValidationError{
					field:  fmt.Sprintf("TransactionWithAdditionalInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetPaginatedTxnsResponseMultiError(errors)
	}

	return nil
}

// GetPaginatedTxnsResponseMultiError is an error wrapping multiple validation
// errors returned by GetPaginatedTxnsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPaginatedTxnsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaginatedTxnsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaginatedTxnsResponseMultiError) AllErrors() []error { return m }

// GetPaginatedTxnsResponseValidationError is the validation error returned by
// GetPaginatedTxnsResponse.Validate if the designated constraints aren't met.
type GetPaginatedTxnsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaginatedTxnsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaginatedTxnsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaginatedTxnsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaginatedTxnsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaginatedTxnsResponseValidationError) ErrorName() string {
	return "GetPaginatedTxnsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaginatedTxnsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaginatedTxnsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaginatedTxnsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaginatedTxnsResponseValidationError{}

// Validate checks the field values on CardTransactionWithAdditionalInfo with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *CardTransactionWithAdditionalInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CardTransactionWithAdditionalInfo
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// CardTransactionWithAdditionalInfoMultiError, or nil if none found.
func (m *CardTransactionWithAdditionalInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CardTransactionWithAdditionalInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTransaction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardTransactionWithAdditionalInfoValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardTransactionWithAdditionalInfoValidationError{
					field:  "Transaction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransaction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardTransactionWithAdditionalInfoValidationError{
				field:  "Transaction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAdditionalInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CardTransactionWithAdditionalInfoValidationError{
					field:  "AdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CardTransactionWithAdditionalInfoValidationError{
					field:  "AdditionalInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdditionalInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CardTransactionWithAdditionalInfoValidationError{
				field:  "AdditionalInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CardTransactionWithAdditionalInfoMultiError(errors)
	}

	return nil
}

// CardTransactionWithAdditionalInfoMultiError is an error wrapping multiple
// validation errors returned by
// CardTransactionWithAdditionalInfo.ValidateAll() if the designated
// constraints aren't met.
type CardTransactionWithAdditionalInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CardTransactionWithAdditionalInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CardTransactionWithAdditionalInfoMultiError) AllErrors() []error { return m }

// CardTransactionWithAdditionalInfoValidationError is the validation error
// returned by CardTransactionWithAdditionalInfo.Validate if the designated
// constraints aren't met.
type CardTransactionWithAdditionalInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CardTransactionWithAdditionalInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CardTransactionWithAdditionalInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CardTransactionWithAdditionalInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CardTransactionWithAdditionalInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CardTransactionWithAdditionalInfoValidationError) ErrorName() string {
	return "CardTransactionWithAdditionalInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CardTransactionWithAdditionalInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCardTransactionWithAdditionalInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CardTransactionWithAdditionalInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CardTransactionWithAdditionalInfoValidationError{}

// Validate checks the field values on GetPaginatedCreditCardTxnViewRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPaginatedCreditCardTxnViewRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaginatedCreditCardTxnViewRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPaginatedCreditCardTxnViewRequestMultiError, or nil if none found.
func (m *GetPaginatedCreditCardTxnViewRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaginatedCreditCardTxnViewRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStartTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedCreditCardTxnViewRequestValidationError{
					field:  "StartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedCreditCardTxnViewRequestValidationError{
					field:  "StartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedCreditCardTxnViewRequestValidationError{
				field:  "StartTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PageSize

	// no validation rules for Offset

	// no validation rules for Descending

	switch v := m.Identifier.(type) {
	case *GetPaginatedCreditCardTxnViewRequest_AccountId:
		if v == nil {
			err := GetPaginatedCreditCardTxnViewRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AccountId
	case *GetPaginatedCreditCardTxnViewRequest_ActorId:
		if v == nil {
			err := GetPaginatedCreditCardTxnViewRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetPaginatedCreditCardTxnViewRequestMultiError(errors)
	}

	return nil
}

// GetPaginatedCreditCardTxnViewRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetPaginatedCreditCardTxnViewRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPaginatedCreditCardTxnViewRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaginatedCreditCardTxnViewRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaginatedCreditCardTxnViewRequestMultiError) AllErrors() []error { return m }

// GetPaginatedCreditCardTxnViewRequestValidationError is the validation error
// returned by GetPaginatedCreditCardTxnViewRequest.Validate if the designated
// constraints aren't met.
type GetPaginatedCreditCardTxnViewRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaginatedCreditCardTxnViewRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaginatedCreditCardTxnViewRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaginatedCreditCardTxnViewRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaginatedCreditCardTxnViewRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaginatedCreditCardTxnViewRequestValidationError) ErrorName() string {
	return "GetPaginatedCreditCardTxnViewRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaginatedCreditCardTxnViewRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaginatedCreditCardTxnViewRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaginatedCreditCardTxnViewRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaginatedCreditCardTxnViewRequestValidationError{}

// Validate checks the field values on GetPaginatedCreditCardTxnViewResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPaginatedCreditCardTxnViewResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaginatedCreditCardTxnViewResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPaginatedCreditCardTxnViewResponseMultiError, or nil if none found.
func (m *GetPaginatedCreditCardTxnViewResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaginatedCreditCardTxnViewResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedCreditCardTxnViewResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedCreditCardTxnViewResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedCreditCardTxnViewResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTxnsViewModelList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaginatedCreditCardTxnViewResponseValidationError{
						field:  fmt.Sprintf("TxnsViewModelList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaginatedCreditCardTxnViewResponseValidationError{
						field:  fmt.Sprintf("TxnsViewModelList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaginatedCreditCardTxnViewResponseValidationError{
					field:  fmt.Sprintf("TxnsViewModelList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetPaginatedCreditCardTxnViewResponseMultiError(errors)
	}

	return nil
}

// GetPaginatedCreditCardTxnViewResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetPaginatedCreditCardTxnViewResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPaginatedCreditCardTxnViewResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaginatedCreditCardTxnViewResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaginatedCreditCardTxnViewResponseMultiError) AllErrors() []error { return m }

// GetPaginatedCreditCardTxnViewResponseValidationError is the validation error
// returned by GetPaginatedCreditCardTxnViewResponse.Validate if the
// designated constraints aren't met.
type GetPaginatedCreditCardTxnViewResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaginatedCreditCardTxnViewResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaginatedCreditCardTxnViewResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaginatedCreditCardTxnViewResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaginatedCreditCardTxnViewResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaginatedCreditCardTxnViewResponseValidationError) ErrorName() string {
	return "GetPaginatedCreditCardTxnViewResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaginatedCreditCardTxnViewResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaginatedCreditCardTxnViewResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaginatedCreditCardTxnViewResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaginatedCreditCardTxnViewResponseValidationError{}

// Validate checks the field values on CreditCardTransactionViewModel with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreditCardTransactionViewModel) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditCardTransactionViewModel with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// CreditCardTransactionViewModelMultiError, or nil if none found.
func (m *CreditCardTransactionViewModel) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditCardTransactionViewModel) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TransactionId

	if all {
		switch v := interface{}(m.GetTransactionTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardTransactionViewModelValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardTransactionViewModelValidationError{
					field:  "TransactionTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTransactionTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardTransactionViewModelValidationError{
				field:  "TransactionTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditCardTransactionViewModelValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditCardTransactionViewModelValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditCardTransactionViewModelValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TransactionStatus

	// no validation rules for TransactionType

	// no validation rules for PrimaryActorId

	// no validation rules for OtherActorId

	// no validation rules for CardPi

	// no validation rules for OtherActorPi

	// no validation rules for TransactionCategory

	// no validation rules for TxnDescription

	// no validation rules for ExternalTxnId

	// no validation rules for BillRefId

	if len(errors) > 0 {
		return CreditCardTransactionViewModelMultiError(errors)
	}

	return nil
}

// CreditCardTransactionViewModelMultiError is an error wrapping multiple
// validation errors returned by CreditCardTransactionViewModel.ValidateAll()
// if the designated constraints aren't met.
type CreditCardTransactionViewModelMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditCardTransactionViewModelMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditCardTransactionViewModelMultiError) AllErrors() []error { return m }

// CreditCardTransactionViewModelValidationError is the validation error
// returned by CreditCardTransactionViewModel.Validate if the designated
// constraints aren't met.
type CreditCardTransactionViewModelValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditCardTransactionViewModelValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditCardTransactionViewModelValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditCardTransactionViewModelValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditCardTransactionViewModelValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditCardTransactionViewModelValidationError) ErrorName() string {
	return "CreditCardTransactionViewModelValidationError"
}

// Error satisfies the builtin error interface
func (e CreditCardTransactionViewModelValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditCardTransactionViewModel.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditCardTransactionViewModelValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditCardTransactionViewModelValidationError{}

// Validate checks the field values on
// GetTxnsAndAdditionalInfoBetweenActorsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetTxnsAndAdditionalInfoBetweenActorsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTxnsAndAdditionalInfoBetweenActorsRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetTxnsAndAdditionalInfoBetweenActorsRequestMultiError, or nil if none found.
func (m *GetTxnsAndAdditionalInfoBetweenActorsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTxnsAndAdditionalInfoBetweenActorsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PrimaryActorId

	// no validation rules for SecondaryActorId

	if all {
		switch v := interface{}(m.GetStartTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError{
					field:  "StartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError{
					field:  "StartTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStartTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError{
				field:  "StartTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if val := m.GetPageSize(); val < 10 || val > 40 {
		err := GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError{
			field:  "PageSize",
			reason: "value must be inside range [10, 40]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Offset

	// no validation rules for Descending

	if len(errors) > 0 {
		return GetTxnsAndAdditionalInfoBetweenActorsRequestMultiError(errors)
	}

	return nil
}

// GetTxnsAndAdditionalInfoBetweenActorsRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetTxnsAndAdditionalInfoBetweenActorsRequest.ValidateAll() if the
// designated constraints aren't met.
type GetTxnsAndAdditionalInfoBetweenActorsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTxnsAndAdditionalInfoBetweenActorsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTxnsAndAdditionalInfoBetweenActorsRequestMultiError) AllErrors() []error { return m }

// GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError is the
// validation error returned by
// GetTxnsAndAdditionalInfoBetweenActorsRequest.Validate if the designated
// constraints aren't met.
type GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError) ErrorName() string {
	return "GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTxnsAndAdditionalInfoBetweenActorsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTxnsAndAdditionalInfoBetweenActorsRequestValidationError{}

// Validate checks the field values on
// GetTxnsAndAdditionalInfoBetweenActorsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetTxnsAndAdditionalInfoBetweenActorsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTxnsAndAdditionalInfoBetweenActorsResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetTxnsAndAdditionalInfoBetweenActorsResponseMultiError, or nil if none found.
func (m *GetTxnsAndAdditionalInfoBetweenActorsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTxnsAndAdditionalInfoBetweenActorsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransactionWithAdditionalInfoList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError{
						field:  fmt.Sprintf("TransactionWithAdditionalInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError{
						field:  fmt.Sprintf("TransactionWithAdditionalInfoList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError{
					field:  fmt.Sprintf("TransactionWithAdditionalInfoList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTxnsAndAdditionalInfoBetweenActorsResponseMultiError(errors)
	}

	return nil
}

// GetTxnsAndAdditionalInfoBetweenActorsResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetTxnsAndAdditionalInfoBetweenActorsResponse.ValidateAll() if the
// designated constraints aren't met.
type GetTxnsAndAdditionalInfoBetweenActorsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTxnsAndAdditionalInfoBetweenActorsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTxnsAndAdditionalInfoBetweenActorsResponseMultiError) AllErrors() []error { return m }

// GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError is the
// validation error returned by
// GetTxnsAndAdditionalInfoBetweenActorsResponse.Validate if the designated
// constraints aren't met.
type GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError) ErrorName() string {
	return "GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTxnsAndAdditionalInfoBetweenActorsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTxnsAndAdditionalInfoBetweenActorsResponseValidationError{}

// Validate checks the field values on CreateDisputeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateDisputeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDisputeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDisputeRequestMultiError, or nil if none found.
func (m *CreateDisputeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDisputeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TxnId

	// no validation rules for ActorId

	// no validation rules for AccountId

	// no validation rules for DisputeRef

	if all {
		switch v := interface{}(m.GetDisputedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDisputeRequestValidationError{
					field:  "DisputedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDisputeRequestValidationError{
					field:  "DisputedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisputedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDisputeRequestValidationError{
				field:  "DisputedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Reason

	// no validation rules for Description

	// no validation rules for DisputeState

	if all {
		switch v := interface{}(m.GetDisputedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDisputeRequestValidationError{
					field:  "DisputedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDisputeRequestValidationError{
					field:  "DisputedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisputedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDisputeRequestValidationError{
				field:  "DisputedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateDisputeRequestMultiError(errors)
	}

	return nil
}

// CreateDisputeRequestMultiError is an error wrapping multiple validation
// errors returned by CreateDisputeRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateDisputeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDisputeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDisputeRequestMultiError) AllErrors() []error { return m }

// CreateDisputeRequestValidationError is the validation error returned by
// CreateDisputeRequest.Validate if the designated constraints aren't met.
type CreateDisputeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDisputeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDisputeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDisputeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDisputeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDisputeRequestValidationError) ErrorName() string {
	return "CreateDisputeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDisputeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDisputeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDisputeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDisputeRequestValidationError{}

// Validate checks the field values on CreateDisputeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateDisputeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDisputeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDisputeResponseMultiError, or nil if none found.
func (m *CreateDisputeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDisputeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDisputeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDisputeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDisputeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateDisputeResponseMultiError(errors)
	}

	return nil
}

// CreateDisputeResponseMultiError is an error wrapping multiple validation
// errors returned by CreateDisputeResponse.ValidateAll() if the designated
// constraints aren't met.
type CreateDisputeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDisputeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDisputeResponseMultiError) AllErrors() []error { return m }

// CreateDisputeResponseValidationError is the validation error returned by
// CreateDisputeResponse.Validate if the designated constraints aren't met.
type CreateDisputeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDisputeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDisputeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDisputeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDisputeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDisputeResponseValidationError) ErrorName() string {
	return "CreateDisputeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDisputeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDisputeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDisputeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDisputeResponseValidationError{}

// Validate checks the field values on GetDisputeRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetDisputeRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDisputeRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDisputeRequestMultiError, or nil if none found.
func (m *GetDisputeRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDisputeRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTxnId()) < 1 {
		err := GetDisputeRequestValidationError{
			field:  "TxnId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetDisputeRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetDisputeRequestMultiError(errors)
	}

	return nil
}

// GetDisputeRequestMultiError is an error wrapping multiple validation errors
// returned by GetDisputeRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDisputeRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDisputeRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDisputeRequestMultiError) AllErrors() []error { return m }

// GetDisputeRequestValidationError is the validation error returned by
// GetDisputeRequest.Validate if the designated constraints aren't met.
type GetDisputeRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDisputeRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDisputeRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDisputeRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDisputeRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDisputeRequestValidationError) ErrorName() string {
	return "GetDisputeRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDisputeRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDisputeRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDisputeRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDisputeRequestValidationError{}

// Validate checks the field values on GetDisputeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDisputeResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDisputeResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDisputeResponseMultiError, or nil if none found.
func (m *GetDisputeResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDisputeResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDispute()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDisputeResponseValidationError{
					field:  "Dispute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDisputeResponseValidationError{
					field:  "Dispute",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDispute()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDisputeResponseValidationError{
				field:  "Dispute",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetDisputeResponseMultiError(errors)
	}

	return nil
}

// GetDisputeResponseMultiError is an error wrapping multiple validation errors
// returned by GetDisputeResponse.ValidateAll() if the designated constraints
// aren't met.
type GetDisputeResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDisputeResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDisputeResponseMultiError) AllErrors() []error { return m }

// GetDisputeResponseValidationError is the validation error returned by
// GetDisputeResponse.Validate if the designated constraints aren't met.
type GetDisputeResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDisputeResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDisputeResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDisputeResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDisputeResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDisputeResponseValidationError) ErrorName() string {
	return "GetDisputeResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetDisputeResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDisputeResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDisputeResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDisputeResponseValidationError{}

// Validate checks the field values on UpdateDisputeStateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateDisputeStateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateDisputeStateRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateDisputeStateRequestMultiError, or nil if none found.
func (m *UpdateDisputeStateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateDisputeStateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTxnId()) < 1 {
		err := UpdateDisputeStateRequestValidationError{
			field:  "TxnId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := UpdateDisputeStateRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for DisputeState

	if len(errors) > 0 {
		return UpdateDisputeStateRequestMultiError(errors)
	}

	return nil
}

// UpdateDisputeStateRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateDisputeStateRequest.ValidateAll() if the
// designated constraints aren't met.
type UpdateDisputeStateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateDisputeStateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateDisputeStateRequestMultiError) AllErrors() []error { return m }

// UpdateDisputeStateRequestValidationError is the validation error returned by
// UpdateDisputeStateRequest.Validate if the designated constraints aren't met.
type UpdateDisputeStateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateDisputeStateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateDisputeStateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateDisputeStateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateDisputeStateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateDisputeStateRequestValidationError) ErrorName() string {
	return "UpdateDisputeStateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateDisputeStateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateDisputeStateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateDisputeStateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateDisputeStateRequestValidationError{}

// Validate checks the field values on UpdateDisputeStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateDisputeStateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateDisputeStateResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateDisputeStateResponseMultiError, or nil if none found.
func (m *UpdateDisputeStateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateDisputeStateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDisputeStateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDisputeStateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDisputeStateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateDisputeStateResponseMultiError(errors)
	}

	return nil
}

// UpdateDisputeStateResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateDisputeStateResponse.ValidateAll() if
// the designated constraints aren't met.
type UpdateDisputeStateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateDisputeStateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateDisputeStateResponseMultiError) AllErrors() []error { return m }

// UpdateDisputeStateResponseValidationError is the validation error returned
// by UpdateDisputeStateResponse.Validate if the designated constraints aren't met.
type UpdateDisputeStateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateDisputeStateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateDisputeStateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateDisputeStateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateDisputeStateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateDisputeStateResponseValidationError) ErrorName() string {
	return "UpdateDisputeStateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateDisputeStateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateDisputeStateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateDisputeStateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateDisputeStateResponseValidationError{}

// Validate checks the field values on IsDisputeAllowedRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsDisputeAllowedRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsDisputeAllowedRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsDisputeAllowedRequestMultiError, or nil if none found.
func (m *IsDisputeAllowedRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsDisputeAllowedRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetTxnId()) < 1 {
		err := IsDisputeAllowedRequestValidationError{
			field:  "TxnId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := IsDisputeAllowedRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return IsDisputeAllowedRequestMultiError(errors)
	}

	return nil
}

// IsDisputeAllowedRequestMultiError is an error wrapping multiple validation
// errors returned by IsDisputeAllowedRequest.ValidateAll() if the designated
// constraints aren't met.
type IsDisputeAllowedRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsDisputeAllowedRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsDisputeAllowedRequestMultiError) AllErrors() []error { return m }

// IsDisputeAllowedRequestValidationError is the validation error returned by
// IsDisputeAllowedRequest.Validate if the designated constraints aren't met.
type IsDisputeAllowedRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsDisputeAllowedRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsDisputeAllowedRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsDisputeAllowedRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsDisputeAllowedRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsDisputeAllowedRequestValidationError) ErrorName() string {
	return "IsDisputeAllowedRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsDisputeAllowedRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsDisputeAllowedRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsDisputeAllowedRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsDisputeAllowedRequestValidationError{}

// Validate checks the field values on IsDisputeAllowedResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsDisputeAllowedResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsDisputeAllowedResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsDisputeAllowedResponseMultiError, or nil if none found.
func (m *IsDisputeAllowedResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsDisputeAllowedResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsDisputeAllowedResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsDisputeAllowedResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsDisputeAllowedResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsDisputeAllowedResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsDisputeAllowedResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsDisputeAllowedResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IsDisputeAllowedResponseMultiError(errors)
	}

	return nil
}

// IsDisputeAllowedResponseMultiError is an error wrapping multiple validation
// errors returned by IsDisputeAllowedResponse.ValidateAll() if the designated
// constraints aren't met.
type IsDisputeAllowedResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsDisputeAllowedResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsDisputeAllowedResponseMultiError) AllErrors() []error { return m }

// IsDisputeAllowedResponseValidationError is the validation error returned by
// IsDisputeAllowedResponse.Validate if the designated constraints aren't met.
type IsDisputeAllowedResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsDisputeAllowedResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsDisputeAllowedResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsDisputeAllowedResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsDisputeAllowedResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsDisputeAllowedResponseValidationError) ErrorName() string {
	return "IsDisputeAllowedResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsDisputeAllowedResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsDisputeAllowedResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsDisputeAllowedResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsDisputeAllowedResponseValidationError{}

// Validate checks the field values on GetTransactionIdsByBillIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTransactionIdsByBillIdRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionIdsByBillIdRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTransactionIdsByBillIdRequestMultiError, or nil if none found.
func (m *GetTransactionIdsByBillIdRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionIdsByBillIdRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BillId

	// no validation rules for FetchChildTransactions

	// no validation rules for FetchFutureRefundReversalChildTransactions

	if len(errors) > 0 {
		return GetTransactionIdsByBillIdRequestMultiError(errors)
	}

	return nil
}

// GetTransactionIdsByBillIdRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetTransactionIdsByBillIdRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionIdsByBillIdRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionIdsByBillIdRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionIdsByBillIdRequestMultiError) AllErrors() []error { return m }

// GetTransactionIdsByBillIdRequestValidationError is the validation error
// returned by GetTransactionIdsByBillIdRequest.Validate if the designated
// constraints aren't met.
type GetTransactionIdsByBillIdRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionIdsByBillIdRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionIdsByBillIdRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionIdsByBillIdRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionIdsByBillIdRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionIdsByBillIdRequestValidationError) ErrorName() string {
	return "GetTransactionIdsByBillIdRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionIdsByBillIdRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionIdsByBillIdRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionIdsByBillIdRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionIdsByBillIdRequestValidationError{}

// Validate checks the field values on GetTransactionIdsByBillIdResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTransactionIdsByBillIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTransactionIdsByBillIdResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTransactionIdsByBillIdResponseMultiError, or nil if none found.
func (m *GetTransactionIdsByBillIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionIdsByBillIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTransactionIdsByBillIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTransactionIdsByBillIdResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTransactionIdsByBillIdResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTransactionIdResponses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTransactionIdsByBillIdResponseValidationError{
						field:  fmt.Sprintf("TransactionIdResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTransactionIdsByBillIdResponseValidationError{
						field:  fmt.Sprintf("TransactionIdResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTransactionIdsByBillIdResponseValidationError{
					field:  fmt.Sprintf("TransactionIdResponses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTransactionIdsByBillIdResponseMultiError(errors)
	}

	return nil
}

// GetTransactionIdsByBillIdResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTransactionIdsByBillIdResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTransactionIdsByBillIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionIdsByBillIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionIdsByBillIdResponseMultiError) AllErrors() []error { return m }

// GetTransactionIdsByBillIdResponseValidationError is the validation error
// returned by GetTransactionIdsByBillIdResponse.Validate if the designated
// constraints aren't met.
type GetTransactionIdsByBillIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionIdsByBillIdResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTransactionIdsByBillIdResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTransactionIdsByBillIdResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTransactionIdsByBillIdResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTransactionIdsByBillIdResponseValidationError) ErrorName() string {
	return "GetTransactionIdsByBillIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionIdsByBillIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionIdsByBillIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionIdsByBillIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionIdsByBillIdResponseValidationError{}

// Validate checks the field values on GetCreditAccountLimitUtilisationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCreditAccountLimitUtilisationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCreditAccountLimitUtilisationRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetCreditAccountLimitUtilisationRequestMultiError, or nil if none found.
func (m *GetCreditAccountLimitUtilisationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditAccountLimitUtilisationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.GetBy.(type) {
	case *GetCreditAccountLimitUtilisationRequest_ReferenceId:
		if v == nil {
			err := GetCreditAccountLimitUtilisationRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ReferenceId
	case *GetCreditAccountLimitUtilisationRequest_CreditAccountId:
		if v == nil {
			err := GetCreditAccountLimitUtilisationRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for CreditAccountId
	case *GetCreditAccountLimitUtilisationRequest_ActorId:
		if v == nil {
			err := GetCreditAccountLimitUtilisationRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetCreditAccountLimitUtilisationRequestMultiError(errors)
	}

	return nil
}

// GetCreditAccountLimitUtilisationRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetCreditAccountLimitUtilisationRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCreditAccountLimitUtilisationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditAccountLimitUtilisationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditAccountLimitUtilisationRequestMultiError) AllErrors() []error { return m }

// GetCreditAccountLimitUtilisationRequestValidationError is the validation
// error returned by GetCreditAccountLimitUtilisationRequest.Validate if the
// designated constraints aren't met.
type GetCreditAccountLimitUtilisationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditAccountLimitUtilisationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditAccountLimitUtilisationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditAccountLimitUtilisationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditAccountLimitUtilisationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditAccountLimitUtilisationRequestValidationError) ErrorName() string {
	return "GetCreditAccountLimitUtilisationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditAccountLimitUtilisationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditAccountLimitUtilisationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditAccountLimitUtilisationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditAccountLimitUtilisationRequestValidationError{}

// Validate checks the field values on GetCreditAccountLimitUtilisationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCreditAccountLimitUtilisationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCreditAccountLimitUtilisationResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetCreditAccountLimitUtilisationResponseMultiError, or nil if none found.
func (m *GetCreditAccountLimitUtilisationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditAccountLimitUtilisationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountLimitUtilisationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountLimitUtilisationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountLimitUtilisationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLimitActual()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountLimitUtilisationResponseValidationError{
					field:  "LimitActual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountLimitUtilisationResponseValidationError{
					field:  "LimitActual",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLimitActual()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountLimitUtilisationResponseValidationError{
				field:  "LimitActual",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLimitAvailable()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountLimitUtilisationResponseValidationError{
					field:  "LimitAvailable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountLimitUtilisationResponseValidationError{
					field:  "LimitAvailable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLimitAvailable()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountLimitUtilisationResponseValidationError{
				field:  "LimitAvailable",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLimitUtilized()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountLimitUtilisationResponseValidationError{
					field:  "LimitUtilized",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountLimitUtilisationResponseValidationError{
					field:  "LimitUtilized",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLimitUtilized()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountLimitUtilisationResponseValidationError{
				field:  "LimitUtilized",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditAccountLimitUtilisationResponseMultiError(errors)
	}

	return nil
}

// GetCreditAccountLimitUtilisationResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetCreditAccountLimitUtilisationResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCreditAccountLimitUtilisationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditAccountLimitUtilisationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditAccountLimitUtilisationResponseMultiError) AllErrors() []error { return m }

// GetCreditAccountLimitUtilisationResponseValidationError is the validation
// error returned by GetCreditAccountLimitUtilisationResponse.Validate if the
// designated constraints aren't met.
type GetCreditAccountLimitUtilisationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditAccountLimitUtilisationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditAccountLimitUtilisationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditAccountLimitUtilisationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditAccountLimitUtilisationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditAccountLimitUtilisationResponseValidationError) ErrorName() string {
	return "GetCreditAccountLimitUtilisationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditAccountLimitUtilisationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditAccountLimitUtilisationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditAccountLimitUtilisationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditAccountLimitUtilisationResponseValidationError{}

// Validate checks the field values on GetCreditAccountDueInformationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCreditAccountDueInformationRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCreditAccountDueInformationRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetCreditAccountDueInformationRequestMultiError, or nil if none found.
func (m *GetCreditAccountDueInformationRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditAccountDueInformationRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.GetBy.(type) {
	case *GetCreditAccountDueInformationRequest_ReferenceId:
		if v == nil {
			err := GetCreditAccountDueInformationRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ReferenceId
	case *GetCreditAccountDueInformationRequest_CreditAccountId:
		if v == nil {
			err := GetCreditAccountDueInformationRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for CreditAccountId
	case *GetCreditAccountDueInformationRequest_ActorId:
		if v == nil {
			err := GetCreditAccountDueInformationRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetCreditAccountDueInformationRequestMultiError(errors)
	}

	return nil
}

// GetCreditAccountDueInformationRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetCreditAccountDueInformationRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCreditAccountDueInformationRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditAccountDueInformationRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditAccountDueInformationRequestMultiError) AllErrors() []error { return m }

// GetCreditAccountDueInformationRequestValidationError is the validation error
// returned by GetCreditAccountDueInformationRequest.Validate if the
// designated constraints aren't met.
type GetCreditAccountDueInformationRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditAccountDueInformationRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditAccountDueInformationRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditAccountDueInformationRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditAccountDueInformationRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditAccountDueInformationRequestValidationError) ErrorName() string {
	return "GetCreditAccountDueInformationRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditAccountDueInformationRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditAccountDueInformationRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditAccountDueInformationRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditAccountDueInformationRequestValidationError{}

// Validate checks the field values on GetCreditAccountDueInformationResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetCreditAccountDueInformationResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCreditAccountDueInformationResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetCreditAccountDueInformationResponseMultiError, or nil if none found.
func (m *GetCreditAccountDueInformationResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCreditAccountDueInformationResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountDueInformationResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMinimumDueAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "MinimumDueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "MinimumDueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinimumDueAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountDueInformationResponseValidationError{
				field:  "MinimumDueAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalDueAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "TotalDueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "TotalDueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalDueAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountDueInformationResponseValidationError{
				field:  "TotalDueAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPaymentMade()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "PaymentMade",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "PaymentMade",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentMade()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountDueInformationResponseValidationError{
				field:  "PaymentMade",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNonBilledAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "NonBilledAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "NonBilledAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNonBilledAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountDueInformationResponseValidationError{
				field:  "NonBilledAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestAccumulated()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "InterestAccumulated",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "InterestAccumulated",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestAccumulated()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountDueInformationResponseValidationError{
				field:  "InterestAccumulated",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountDueInformationResponseValidationError{
				field:  "DueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUnpaidMinDue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "UnpaidMinDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "UnpaidMinDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnpaidMinDue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountDueInformationResponseValidationError{
				field:  "UnpaidMinDue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUnpaidTotalDue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "UnpaidTotalDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "UnpaidTotalDue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUnpaidTotalDue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountDueInformationResponseValidationError{
				field:  "UnpaidTotalDue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalOutstandingAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "TotalOutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCreditAccountDueInformationResponseValidationError{
					field:  "TotalOutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalOutstandingAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCreditAccountDueInformationResponseValidationError{
				field:  "TotalOutstandingAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCreditAccountDueInformationResponseMultiError(errors)
	}

	return nil
}

// GetCreditAccountDueInformationResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetCreditAccountDueInformationResponse.ValidateAll() if the designated
// constraints aren't met.
type GetCreditAccountDueInformationResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCreditAccountDueInformationResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCreditAccountDueInformationResponseMultiError) AllErrors() []error { return m }

// GetCreditAccountDueInformationResponseValidationError is the validation
// error returned by GetCreditAccountDueInformationResponse.Validate if the
// designated constraints aren't met.
type GetCreditAccountDueInformationResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCreditAccountDueInformationResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCreditAccountDueInformationResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCreditAccountDueInformationResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCreditAccountDueInformationResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCreditAccountDueInformationResponseValidationError) ErrorName() string {
	return "GetCreditAccountDueInformationResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCreditAccountDueInformationResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCreditAccountDueInformationResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCreditAccountDueInformationResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCreditAccountDueInformationResponseValidationError{}

// Validate checks the field values on GetTxnsWithAdditionalInfosRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTxnsWithAdditionalInfosRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTxnsWithAdditionalInfosRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTxnsWithAdditionalInfosRequestMultiError, or nil if none found.
func (m *GetTxnsWithAdditionalInfosRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTxnsWithAdditionalInfosRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.GetBy.(type) {
	case *GetTxnsWithAdditionalInfosRequest_BatchDedupeIdGenerationParameter:
		if v == nil {
			err := GetTxnsWithAdditionalInfosRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBatchDedupeIdGenerationParameter()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTxnsWithAdditionalInfosRequestValidationError{
						field:  "BatchDedupeIdGenerationParameter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTxnsWithAdditionalInfosRequestValidationError{
						field:  "BatchDedupeIdGenerationParameter",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBatchDedupeIdGenerationParameter()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTxnsWithAdditionalInfosRequestValidationError{
					field:  "BatchDedupeIdGenerationParameter",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetTxnsWithAdditionalInfosRequest_BatchDedupeIds:
		if v == nil {
			err := GetTxnsWithAdditionalInfosRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBatchDedupeIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTxnsWithAdditionalInfosRequestValidationError{
						field:  "BatchDedupeIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTxnsWithAdditionalInfosRequestValidationError{
						field:  "BatchDedupeIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBatchDedupeIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTxnsWithAdditionalInfosRequestValidationError{
					field:  "BatchDedupeIds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetTxnsWithAdditionalInfosRequest_BatchTxnIds:
		if v == nil {
			err := GetTxnsWithAdditionalInfosRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBatchTxnIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTxnsWithAdditionalInfosRequestValidationError{
						field:  "BatchTxnIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTxnsWithAdditionalInfosRequestValidationError{
						field:  "BatchTxnIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBatchTxnIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTxnsWithAdditionalInfosRequestValidationError{
					field:  "BatchTxnIds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetTxnsWithAdditionalInfosRequest_BatchExternalTxnIds:
		if v == nil {
			err := GetTxnsWithAdditionalInfosRequestValidationError{
				field:  "GetBy",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBatchExternalTxnIds()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTxnsWithAdditionalInfosRequestValidationError{
						field:  "BatchExternalTxnIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTxnsWithAdditionalInfosRequestValidationError{
						field:  "BatchExternalTxnIds",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBatchExternalTxnIds()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTxnsWithAdditionalInfosRequestValidationError{
					field:  "BatchExternalTxnIds",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetTxnsWithAdditionalInfosRequestMultiError(errors)
	}

	return nil
}

// GetTxnsWithAdditionalInfosRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetTxnsWithAdditionalInfosRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTxnsWithAdditionalInfosRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTxnsWithAdditionalInfosRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTxnsWithAdditionalInfosRequestMultiError) AllErrors() []error { return m }

// GetTxnsWithAdditionalInfosRequestValidationError is the validation error
// returned by GetTxnsWithAdditionalInfosRequest.Validate if the designated
// constraints aren't met.
type GetTxnsWithAdditionalInfosRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTxnsWithAdditionalInfosRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTxnsWithAdditionalInfosRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTxnsWithAdditionalInfosRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTxnsWithAdditionalInfosRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTxnsWithAdditionalInfosRequestValidationError) ErrorName() string {
	return "GetTxnsWithAdditionalInfosRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTxnsWithAdditionalInfosRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTxnsWithAdditionalInfosRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTxnsWithAdditionalInfosRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTxnsWithAdditionalInfosRequestValidationError{}

// Validate checks the field values on GetTxnsWithAdditionalInfosResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetTxnsWithAdditionalInfosResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTxnsWithAdditionalInfosResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetTxnsWithAdditionalInfosResponseMultiError, or nil if none found.
func (m *GetTxnsWithAdditionalInfosResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTxnsWithAdditionalInfosResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTxnsWithAdditionalInfosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTxnsWithAdditionalInfosResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTxnsWithAdditionalInfosResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCardTransactionWithAdditionalInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTxnsWithAdditionalInfosResponseValidationError{
						field:  fmt.Sprintf("CardTransactionWithAdditionalInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTxnsWithAdditionalInfosResponseValidationError{
						field:  fmt.Sprintf("CardTransactionWithAdditionalInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTxnsWithAdditionalInfosResponseValidationError{
					field:  fmt.Sprintf("CardTransactionWithAdditionalInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetTxnsWithAdditionalInfosResponseMultiError(errors)
	}

	return nil
}

// GetTxnsWithAdditionalInfosResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetTxnsWithAdditionalInfosResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTxnsWithAdditionalInfosResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTxnsWithAdditionalInfosResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTxnsWithAdditionalInfosResponseMultiError) AllErrors() []error { return m }

// GetTxnsWithAdditionalInfosResponseValidationError is the validation error
// returned by GetTxnsWithAdditionalInfosResponse.Validate if the designated
// constraints aren't met.
type GetTxnsWithAdditionalInfosResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTxnsWithAdditionalInfosResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTxnsWithAdditionalInfosResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTxnsWithAdditionalInfosResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTxnsWithAdditionalInfosResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTxnsWithAdditionalInfosResponseValidationError) ErrorName() string {
	return "GetTxnsWithAdditionalInfosResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTxnsWithAdditionalInfosResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTxnsWithAdditionalInfosResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTxnsWithAdditionalInfosResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTxnsWithAdditionalInfosResponseValidationError{}

// Validate checks the field values on GetPaginatedTxnWithAdditionalInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetPaginatedTxnWithAdditionalInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPaginatedTxnWithAdditionalInfoRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetPaginatedTxnWithAdditionalInfoRequestMultiError, or nil if none found.
func (m *GetPaginatedTxnWithAdditionalInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaginatedTxnWithAdditionalInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedTxnWithAdditionalInfoRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedTxnWithAdditionalInfoRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedTxnWithAdditionalInfoRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Identifier.(type) {
	case *GetPaginatedTxnWithAdditionalInfoRequest_AccountId:
		if v == nil {
			err := GetPaginatedTxnWithAdditionalInfoRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for AccountId
	case *GetPaginatedTxnWithAdditionalInfoRequest_ActorId:
		if v == nil {
			err := GetPaginatedTxnWithAdditionalInfoRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetPaginatedTxnWithAdditionalInfoRequestMultiError(errors)
	}

	return nil
}

// GetPaginatedTxnWithAdditionalInfoRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetPaginatedTxnWithAdditionalInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPaginatedTxnWithAdditionalInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaginatedTxnWithAdditionalInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaginatedTxnWithAdditionalInfoRequestMultiError) AllErrors() []error { return m }

// GetPaginatedTxnWithAdditionalInfoRequestValidationError is the validation
// error returned by GetPaginatedTxnWithAdditionalInfoRequest.Validate if the
// designated constraints aren't met.
type GetPaginatedTxnWithAdditionalInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaginatedTxnWithAdditionalInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaginatedTxnWithAdditionalInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaginatedTxnWithAdditionalInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaginatedTxnWithAdditionalInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaginatedTxnWithAdditionalInfoRequestValidationError) ErrorName() string {
	return "GetPaginatedTxnWithAdditionalInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaginatedTxnWithAdditionalInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaginatedTxnWithAdditionalInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaginatedTxnWithAdditionalInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaginatedTxnWithAdditionalInfoRequestValidationError{}

// Validate checks the field values on
// GetPaginatedTxnWithAdditionalInfoResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetPaginatedTxnWithAdditionalInfoResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetPaginatedTxnWithAdditionalInfoResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetPaginatedTxnWithAdditionalInfoResponseMultiError, or nil if none found.
func (m *GetPaginatedTxnWithAdditionalInfoResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaginatedTxnWithAdditionalInfoResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedTxnWithAdditionalInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedTxnWithAdditionalInfoResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedTxnWithAdditionalInfoResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCardTransactionWithAdditionalInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetPaginatedTxnWithAdditionalInfoResponseValidationError{
						field:  fmt.Sprintf("CardTransactionWithAdditionalInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetPaginatedTxnWithAdditionalInfoResponseValidationError{
						field:  fmt.Sprintf("CardTransactionWithAdditionalInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetPaginatedTxnWithAdditionalInfoResponseValidationError{
					field:  fmt.Sprintf("CardTransactionWithAdditionalInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaginatedTxnWithAdditionalInfoResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaginatedTxnWithAdditionalInfoResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaginatedTxnWithAdditionalInfoResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPaginatedTxnWithAdditionalInfoResponseMultiError(errors)
	}

	return nil
}

// GetPaginatedTxnWithAdditionalInfoResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetPaginatedTxnWithAdditionalInfoResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPaginatedTxnWithAdditionalInfoResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaginatedTxnWithAdditionalInfoResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaginatedTxnWithAdditionalInfoResponseMultiError) AllErrors() []error { return m }

// GetPaginatedTxnWithAdditionalInfoResponseValidationError is the validation
// error returned by GetPaginatedTxnWithAdditionalInfoResponse.Validate if the
// designated constraints aren't met.
type GetPaginatedTxnWithAdditionalInfoResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaginatedTxnWithAdditionalInfoResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaginatedTxnWithAdditionalInfoResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaginatedTxnWithAdditionalInfoResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaginatedTxnWithAdditionalInfoResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaginatedTxnWithAdditionalInfoResponseValidationError) ErrorName() string {
	return "GetPaginatedTxnWithAdditionalInfoResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaginatedTxnWithAdditionalInfoResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaginatedTxnWithAdditionalInfoResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaginatedTxnWithAdditionalInfoResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaginatedTxnWithAdditionalInfoResponseValidationError{}

// Validate checks the field values on UpdateBillToTxnMappingRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBillToTxnMappingRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBillToTxnMappingRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateBillToTxnMappingRequestMultiError, or nil if none found.
func (m *UpdateBillToTxnMappingRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBillToTxnMappingRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BillRefId

	if len(errors) > 0 {
		return UpdateBillToTxnMappingRequestMultiError(errors)
	}

	return nil
}

// UpdateBillToTxnMappingRequestMultiError is an error wrapping multiple
// validation errors returned by UpdateBillToTxnMappingRequest.ValidateAll()
// if the designated constraints aren't met.
type UpdateBillToTxnMappingRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBillToTxnMappingRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBillToTxnMappingRequestMultiError) AllErrors() []error { return m }

// UpdateBillToTxnMappingRequestValidationError is the validation error
// returned by UpdateBillToTxnMappingRequest.Validate if the designated
// constraints aren't met.
type UpdateBillToTxnMappingRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBillToTxnMappingRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBillToTxnMappingRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBillToTxnMappingRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBillToTxnMappingRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBillToTxnMappingRequestValidationError) ErrorName() string {
	return "UpdateBillToTxnMappingRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBillToTxnMappingRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBillToTxnMappingRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBillToTxnMappingRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBillToTxnMappingRequestValidationError{}

// Validate checks the field values on UpdateBillToTxnMappingResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateBillToTxnMappingResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateBillToTxnMappingResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateBillToTxnMappingResponseMultiError, or nil if none found.
func (m *UpdateBillToTxnMappingResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateBillToTxnMappingResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateBillToTxnMappingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateBillToTxnMappingResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateBillToTxnMappingResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateBillToTxnMappingResponseMultiError(errors)
	}

	return nil
}

// UpdateBillToTxnMappingResponseMultiError is an error wrapping multiple
// validation errors returned by UpdateBillToTxnMappingResponse.ValidateAll()
// if the designated constraints aren't met.
type UpdateBillToTxnMappingResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateBillToTxnMappingResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateBillToTxnMappingResponseMultiError) AllErrors() []error { return m }

// UpdateBillToTxnMappingResponseValidationError is the validation error
// returned by UpdateBillToTxnMappingResponse.Validate if the designated
// constraints aren't met.
type UpdateBillToTxnMappingResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateBillToTxnMappingResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateBillToTxnMappingResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateBillToTxnMappingResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateBillToTxnMappingResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateBillToTxnMappingResponseValidationError) ErrorName() string {
	return "UpdateBillToTxnMappingResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateBillToTxnMappingResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateBillToTxnMappingResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateBillToTxnMappingResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateBillToTxnMappingResponseValidationError{}

// Validate checks the field values on BatchDedupeIds with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BatchDedupeIds) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchDedupeIds with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BatchDedupeIdsMultiError,
// or nil if none found.
func (m *BatchDedupeIds) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchDedupeIds) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BatchDedupeIdsMultiError(errors)
	}

	return nil
}

// BatchDedupeIdsMultiError is an error wrapping multiple validation errors
// returned by BatchDedupeIds.ValidateAll() if the designated constraints
// aren't met.
type BatchDedupeIdsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchDedupeIdsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchDedupeIdsMultiError) AllErrors() []error { return m }

// BatchDedupeIdsValidationError is the validation error returned by
// BatchDedupeIds.Validate if the designated constraints aren't met.
type BatchDedupeIdsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchDedupeIdsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchDedupeIdsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchDedupeIdsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchDedupeIdsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchDedupeIdsValidationError) ErrorName() string { return "BatchDedupeIdsValidationError" }

// Error satisfies the builtin error interface
func (e BatchDedupeIdsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchDedupeIds.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchDedupeIdsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchDedupeIdsValidationError{}

// Validate checks the field values on GetCollateralLienStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCollateralLienStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCollateralLienStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCollateralLienStatusRequestMultiError, or nil if none found.
func (m *GetCollateralLienStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCollateralLienStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetActorAndCollateralIdentifier()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCollateralLienStatusRequestValidationError{
					field:  "ActorAndCollateralIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCollateralLienStatusRequestValidationError{
					field:  "ActorAndCollateralIdentifier",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActorAndCollateralIdentifier()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCollateralLienStatusRequestValidationError{
				field:  "ActorAndCollateralIdentifier",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetCollateralLienStatusRequestMultiError(errors)
	}

	return nil
}

// GetCollateralLienStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetCollateralLienStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetCollateralLienStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCollateralLienStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCollateralLienStatusRequestMultiError) AllErrors() []error { return m }

// GetCollateralLienStatusRequestValidationError is the validation error
// returned by GetCollateralLienStatusRequest.Validate if the designated
// constraints aren't met.
type GetCollateralLienStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCollateralLienStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCollateralLienStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCollateralLienStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCollateralLienStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCollateralLienStatusRequestValidationError) ErrorName() string {
	return "GetCollateralLienStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCollateralLienStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCollateralLienStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCollateralLienStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCollateralLienStatusRequestValidationError{}

// Validate checks the field values on GetCollateralLienStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCollateralLienStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCollateralLienStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetCollateralLienStatusResponseMultiError, or nil if none found.
func (m *GetCollateralLienStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCollateralLienStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetCollateralLienStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetCollateralLienStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetCollateralLienStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CollateralLienStatus

	if len(errors) > 0 {
		return GetCollateralLienStatusResponseMultiError(errors)
	}

	return nil
}

// GetCollateralLienStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetCollateralLienStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type GetCollateralLienStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCollateralLienStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCollateralLienStatusResponseMultiError) AllErrors() []error { return m }

// GetCollateralLienStatusResponseValidationError is the validation error
// returned by GetCollateralLienStatusResponse.Validate if the designated
// constraints aren't met.
type GetCollateralLienStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCollateralLienStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCollateralLienStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCollateralLienStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCollateralLienStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCollateralLienStatusResponseValidationError) ErrorName() string {
	return "GetCollateralLienStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetCollateralLienStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCollateralLienStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCollateralLienStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCollateralLienStatusResponseValidationError{}

// Validate checks the field values on ActorAndCollateralIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ActorAndCollateralIdentifier) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ActorAndCollateralIdentifier with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ActorAndCollateralIdentifierMultiError, or nil if none found.
func (m *ActorAndCollateralIdentifier) ValidateAll() error {
	return m.validate(true)
}

func (m *ActorAndCollateralIdentifier) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for CollateralId

	// no validation rules for CollateralType

	if len(errors) > 0 {
		return ActorAndCollateralIdentifierMultiError(errors)
	}

	return nil
}

// ActorAndCollateralIdentifierMultiError is an error wrapping multiple
// validation errors returned by ActorAndCollateralIdentifier.ValidateAll() if
// the designated constraints aren't met.
type ActorAndCollateralIdentifierMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ActorAndCollateralIdentifierMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ActorAndCollateralIdentifierMultiError) AllErrors() []error { return m }

// ActorAndCollateralIdentifierValidationError is the validation error returned
// by ActorAndCollateralIdentifier.Validate if the designated constraints
// aren't met.
type ActorAndCollateralIdentifierValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ActorAndCollateralIdentifierValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ActorAndCollateralIdentifierValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ActorAndCollateralIdentifierValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ActorAndCollateralIdentifierValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ActorAndCollateralIdentifierValidationError) ErrorName() string {
	return "ActorAndCollateralIdentifierValidationError"
}

// Error satisfies the builtin error interface
func (e ActorAndCollateralIdentifierValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sActorAndCollateralIdentifier.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ActorAndCollateralIdentifierValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ActorAndCollateralIdentifierValidationError{}

// Validate checks the field values on GetAccountRequest_ActorIdAndRefId with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAccountRequest_ActorIdAndRefId) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAccountRequest_ActorIdAndRefId
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAccountRequest_ActorIdAndRefIdMultiError, or nil if none found.
func (m *GetAccountRequest_ActorIdAndRefId) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAccountRequest_ActorIdAndRefId) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for ReferenceId

	if len(errors) > 0 {
		return GetAccountRequest_ActorIdAndRefIdMultiError(errors)
	}

	return nil
}

// GetAccountRequest_ActorIdAndRefIdMultiError is an error wrapping multiple
// validation errors returned by
// GetAccountRequest_ActorIdAndRefId.ValidateAll() if the designated
// constraints aren't met.
type GetAccountRequest_ActorIdAndRefIdMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAccountRequest_ActorIdAndRefIdMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAccountRequest_ActorIdAndRefIdMultiError) AllErrors() []error { return m }

// GetAccountRequest_ActorIdAndRefIdValidationError is the validation error
// returned by GetAccountRequest_ActorIdAndRefId.Validate if the designated
// constraints aren't met.
type GetAccountRequest_ActorIdAndRefIdValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAccountRequest_ActorIdAndRefIdValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAccountRequest_ActorIdAndRefIdValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAccountRequest_ActorIdAndRefIdValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAccountRequest_ActorIdAndRefIdValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAccountRequest_ActorIdAndRefIdValidationError) ErrorName() string {
	return "GetAccountRequest_ActorIdAndRefIdValidationError"
}

// Error satisfies the builtin error interface
func (e GetAccountRequest_ActorIdAndRefIdValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAccountRequest_ActorIdAndRefId.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAccountRequest_ActorIdAndRefIdValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAccountRequest_ActorIdAndRefIdValidationError{}

// Validate checks the field values on
// GetTransactionIdsByBillIdResponse_TransactionIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTransactionIdsByBillIdResponse_TransactionIdResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetTransactionIdsByBillIdResponse_TransactionIdResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTransactionIdsByBillIdResponse_TransactionIdResponseMultiError, or nil
// if none found.
func (m *GetTransactionIdsByBillIdResponse_TransactionIdResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTransactionIdsByBillIdResponse_TransactionIdResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExternalTxnId

	// no validation rules for TxnId

	// no validation rules for TransactionCategory

	if len(errors) > 0 {
		return GetTransactionIdsByBillIdResponse_TransactionIdResponseMultiError(errors)
	}

	return nil
}

// GetTransactionIdsByBillIdResponse_TransactionIdResponseMultiError is an
// error wrapping multiple validation errors returned by
// GetTransactionIdsByBillIdResponse_TransactionIdResponse.ValidateAll() if
// the designated constraints aren't met.
type GetTransactionIdsByBillIdResponse_TransactionIdResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTransactionIdsByBillIdResponse_TransactionIdResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTransactionIdsByBillIdResponse_TransactionIdResponseMultiError) AllErrors() []error {
	return m
}

// GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError is
// the validation error returned by
// GetTransactionIdsByBillIdResponse_TransactionIdResponse.Validate if the
// designated constraints aren't met.
type GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError) Key() bool {
	return e.key
}

// ErrorName returns error name.
func (e GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError) ErrorName() string {
	return "GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTransactionIdsByBillIdResponse_TransactionIdResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTransactionIdsByBillIdResponse_TransactionIdResponseValidationError{}
