syntax = "proto3";

package insights.model;

import "api/insights/model/enums.proto";
import "api/typesv2/common/user_group.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/insights/model";
option java_package = "com.github.epifi.gamma.api.insights.model";

// Absolute date and time for expiry of insight values
message AbsoluteDateTimeParams {
  google.protobuf.Timestamp date_time = 1;
}

// EndOf params defines end of certain time when the insight values expires
// Unit can be week, month, year, etc
// value represent the value for that unit
// ex. unit - Week, count - 2 => Insight value will expire at end of second week from time of generation
// i.e. if generated on friday, will expire next week's sunday
// ex. Unit - Month, count - 2 => Insight value will expire at end of next month from time of generation
message EndOfCalenderUnitParams {
  insights.model.TimeUnit unit = 1;
  int32 count = 2;
}

// Defines the Expiry config for each insight framework
// When values for any framework are generated, this config is used to determine their expiry
message ExpiryConfig {
  insights.model.ExpiryMethod method = 1;
  oneof ExpiryValue {
    AbsoluteDateTimeParams absolute_date_time_params = 2;
    // if relative_duration is 3 then insight will expire at exactly 3 days from time of generation
    uint32 relative_duration_from_generation_in_days = 3;
    EndOfCalenderUnitParams end_of_calender_unit_params = 4;
  }
}

message InsightFramework {
  // Primary key to uniquely identify all records
  string id = 1;

  // Defines the insight's content type (i.e. Personal/Generic)
  ContentType content_type = 2;

  // All variables that can vary in the framework
  repeated InsightVariable framework_variables = 3;

  // Segment variables are basically a subset of framework_variables that can define one segment
  // e.g. merchant name in Merchant transaction insights. "Last month spent at <merchant-name> was Rs <amount>"
  // In above example merchant-name is the only segment variable. While both merchant name and amount are framework_variables
  repeated InsightVariable segment_variables = 4;

  // Deeplink info (json converted to string) - can also contain variables e.x. merchant name to be used in screen params in deeplink
  string deeplink = 5;

  // Defines the state of insight framework i.e. active/inactive/deprecated
  InsightState insight_state = 6;

  // Config used to determine the validity of any insight value (ActorInsight) generated from this framework
  ExpiryConfig expiry_config = 7;

  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;

  // Human understandable identifier (name) for the framework
  string framework_name = 11;

  // Defines the insight's target audience level (i.e. Actor/Group)
  // Here group can be a peer group or group of all users (special case)
  TargetEntityType target_entity_type = 12;

  // defines the user group who is eligible to see this insight e.g. Internal / fnf / All users
  api.typesv2.common.UserGroup release_group = 13;

  // expression that will be evaluated before an insight of this framework is selected to be shown to the user
  string validation_expression = 14;
}

// InsightFrameworkFieldMask is the enum representation of all the InsightFramework fields.
// Meant to be used as field mask to help with database updates
enum InsightFrameworkFieldMask {
  INSIGHT_FRAMEWORK_FIELD_MASK_UNSPECIFIED = 0;
  INSIGHT_FRAMEWORK_FIELD_MASK_ID = 1;
  INSIGHT_FRAMEWORK_FIELD_MASK_CONTENT_TYPE = 2;
  INSIGHT_FRAMEWORK_FIELD_MASK_FRAMEWORK_VARIABLES = 3;
  INSIGHT_FRAMEWORK_FIELD_MASK_SEGMENT_VARIABLES = 4;
  INSIGHT_FRAMEWORK_FIELD_MASK_DEEPLINK = 5;
  INSIGHT_FRAMEWORK_FIELD_MASK_INSIGHT_STATE = 6;
  INSIGHT_FRAMEWORK_FIELD_MASK_EXPIRY_CONFIG = 7;
  INSIGHT_FRAMEWORK_FIELD_MASK_CREATED_AT = 8;
  INSIGHT_FRAMEWORK_FIELD_MASK_UPDATED_AT = 9;
  INSIGHT_FRAMEWORK_FIELD_MASK_DELETED_AT = 10;
  INSIGHT_FRAMEWORK_FIELD_MASK_FRAMEWORK_NAME = 11;
  INSIGHT_FRAMEWORK_FIELD_MASK_TARGET_ENTITY_TYPE = 12;
  INSIGHT_FRAMEWORK_FIELD_MASK_RELEASE_GROUP = 13;
  INSIGHT_FRAMEWORK_FIELD_MASK_VALIDATION_EXPRESSION = 14;
}
