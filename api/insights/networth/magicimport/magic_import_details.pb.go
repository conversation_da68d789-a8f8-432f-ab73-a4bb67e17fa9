// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/insights/networth/magicimport/magic_import_details.proto

package magicimport

import (
	model "github.com/epifi/gamma/api/insights/networth/model"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type MagicImportAssetType int32

const (
	MagicImportAssetType_MAGIC_IMPORT_ASSET_TYPE_UNSPECIFIED MagicImportAssetType = 0
	// Asset which can be added to networth e.g. fixed deposits, bonds, gadgets, etc.
	MagicImportAssetType_MAGIC_IMPORT_ASSET_TYPE_CONVENTIONAL MagicImportAssetType = 1
	// Asset which cannot be added to networth
	MagicImportAssetType_MAGIC_IMPORT_ASSET_TYPE_UNCONVENTIONAL MagicImportAssetType = 2
)

// Enum value maps for MagicImportAssetType.
var (
	MagicImportAssetType_name = map[int32]string{
		0: "MAGIC_IMPORT_ASSET_TYPE_UNSPECIFIED",
		1: "MAGIC_IMPORT_ASSET_TYPE_CONVENTIONAL",
		2: "MAGIC_IMPORT_ASSET_TYPE_UNCONVENTIONAL",
	}
	MagicImportAssetType_value = map[string]int32{
		"MAGIC_IMPORT_ASSET_TYPE_UNSPECIFIED":    0,
		"MAGIC_IMPORT_ASSET_TYPE_CONVENTIONAL":   1,
		"MAGIC_IMPORT_ASSET_TYPE_UNCONVENTIONAL": 2,
	}
)

func (x MagicImportAssetType) Enum() *MagicImportAssetType {
	p := new(MagicImportAssetType)
	*p = x
	return p
}

func (x MagicImportAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MagicImportAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_insights_networth_magicimport_magic_import_details_proto_enumTypes[0].Descriptor()
}

func (MagicImportAssetType) Type() protoreflect.EnumType {
	return &file_api_insights_networth_magicimport_magic_import_details_proto_enumTypes[0]
}

func (x MagicImportAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MagicImportAssetType.Descriptor instead.
func (MagicImportAssetType) EnumDescriptor() ([]byte, []int) {
	return file_api_insights_networth_magicimport_magic_import_details_proto_rawDescGZIP(), []int{0}
}

// MagicImportDetails contains asset details via magic import
// It also contains abstracted derived details of each asset
type MagicImportDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetDetailsList []*MagicImportAssetDetails `protobuf:"bytes,1,rep,name=asset_details_list,json=assetDetailsList,proto3" json:"asset_details_list,omitempty"`
	AiCommentary     string                     `protobuf:"bytes,2,opt,name=ai_commentary,json=aiCommentary,proto3" json:"ai_commentary,omitempty"`
}

func (x *MagicImportDetails) Reset() {
	*x = MagicImportDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MagicImportDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagicImportDetails) ProtoMessage() {}

func (x *MagicImportDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagicImportDetails.ProtoReflect.Descriptor instead.
func (*MagicImportDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_magicimport_magic_import_details_proto_rawDescGZIP(), []int{0}
}

func (x *MagicImportDetails) GetAssetDetailsList() []*MagicImportAssetDetails {
	if x != nil {
		return x.AssetDetailsList
	}
	return nil
}

func (x *MagicImportDetails) GetAiCommentary() string {
	if x != nil {
		return x.AiCommentary
	}
	return ""
}

type MagicImportAssetDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetType MagicImportAssetType `protobuf:"varint,1,opt,name=asset_type,json=assetType,proto3,enum=insights.networth.magicimport.MagicImportAssetType" json:"asset_type,omitempty"`
	// Types that are assignable to Details:
	//
	//	*MagicImportAssetDetails_ConventionalAssetDetails
	//	*MagicImportAssetDetails_UnconventionalAssetDetails
	Details isMagicImportAssetDetails_Details `protobuf_oneof:"details"`
}

func (x *MagicImportAssetDetails) Reset() {
	*x = MagicImportAssetDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MagicImportAssetDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MagicImportAssetDetails) ProtoMessage() {}

func (x *MagicImportAssetDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MagicImportAssetDetails.ProtoReflect.Descriptor instead.
func (*MagicImportAssetDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_magicimport_magic_import_details_proto_rawDescGZIP(), []int{1}
}

func (x *MagicImportAssetDetails) GetAssetType() MagicImportAssetType {
	if x != nil {
		return x.AssetType
	}
	return MagicImportAssetType_MAGIC_IMPORT_ASSET_TYPE_UNSPECIFIED
}

func (m *MagicImportAssetDetails) GetDetails() isMagicImportAssetDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *MagicImportAssetDetails) GetConventionalAssetDetails() *ConventionalAssetDetails {
	if x, ok := x.GetDetails().(*MagicImportAssetDetails_ConventionalAssetDetails); ok {
		return x.ConventionalAssetDetails
	}
	return nil
}

func (x *MagicImportAssetDetails) GetUnconventionalAssetDetails() *UnconventionalAssetDetails {
	if x, ok := x.GetDetails().(*MagicImportAssetDetails_UnconventionalAssetDetails); ok {
		return x.UnconventionalAssetDetails
	}
	return nil
}

type isMagicImportAssetDetails_Details interface {
	isMagicImportAssetDetails_Details()
}

type MagicImportAssetDetails_ConventionalAssetDetails struct {
	// Asset which can be added to networth e.g. stocks, mutual funds, bonds
	ConventionalAssetDetails *ConventionalAssetDetails `protobuf:"bytes,2,opt,name=conventional_asset_details,json=conventionalAssetDetails,proto3,oneof"`
}

type MagicImportAssetDetails_UnconventionalAssetDetails struct {
	// Asset which cannot be added to networth
	UnconventionalAssetDetails *UnconventionalAssetDetails `protobuf:"bytes,3,opt,name=unconventional_asset_details,json=unconventionalAssetDetails,proto3,oneof"`
}

func (*MagicImportAssetDetails_ConventionalAssetDetails) isMagicImportAssetDetails_Details() {}

func (*MagicImportAssetDetails_UnconventionalAssetDetails) isMagicImportAssetDetails_Details() {}

type ConventionalAssetDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	InvestmentDeclaration *model.InvestmentDeclaration `protobuf:"bytes,1,opt,name=investment_declaration,json=investmentDeclaration,proto3" json:"investment_declaration,omitempty"`
	EstimatedValue        *money.Money                 `protobuf:"bytes,2,opt,name=estimated_value,json=estimatedValue,proto3" json:"estimated_value,omitempty"`
	InvestmentName        string                       `protobuf:"bytes,3,opt,name=investment_name,json=investmentName,proto3" json:"investment_name,omitempty"`
	// File name from which the asset was imported
	FileName string `protobuf:"bytes,4,opt,name=file_name,json=fileName,proto3" json:"file_name,omitempty"`
}

func (x *ConventionalAssetDetails) Reset() {
	*x = ConventionalAssetDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConventionalAssetDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConventionalAssetDetails) ProtoMessage() {}

func (x *ConventionalAssetDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConventionalAssetDetails.ProtoReflect.Descriptor instead.
func (*ConventionalAssetDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_magicimport_magic_import_details_proto_rawDescGZIP(), []int{2}
}

func (x *ConventionalAssetDetails) GetInvestmentDeclaration() *model.InvestmentDeclaration {
	if x != nil {
		return x.InvestmentDeclaration
	}
	return nil
}

func (x *ConventionalAssetDetails) GetEstimatedValue() *money.Money {
	if x != nil {
		return x.EstimatedValue
	}
	return nil
}

func (x *ConventionalAssetDetails) GetInvestmentName() string {
	if x != nil {
		return x.InvestmentName
	}
	return ""
}

func (x *ConventionalAssetDetails) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type UnconventionalAssetDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AssetName    string `protobuf:"bytes,1,opt,name=asset_name,json=assetName,proto3" json:"asset_name,omitempty"`
	AssetType    string `protobuf:"bytes,2,opt,name=asset_type,json=assetType,proto3" json:"asset_type,omitempty"`
	AiCommentary string `protobuf:"bytes,3,opt,name=ai_commentary,json=aiCommentary,proto3" json:"ai_commentary,omitempty"`
}

func (x *UnconventionalAssetDetails) Reset() {
	*x = UnconventionalAssetDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnconventionalAssetDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnconventionalAssetDetails) ProtoMessage() {}

func (x *UnconventionalAssetDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnconventionalAssetDetails.ProtoReflect.Descriptor instead.
func (*UnconventionalAssetDetails) Descriptor() ([]byte, []int) {
	return file_api_insights_networth_magicimport_magic_import_details_proto_rawDescGZIP(), []int{3}
}

func (x *UnconventionalAssetDetails) GetAssetName() string {
	if x != nil {
		return x.AssetName
	}
	return ""
}

func (x *UnconventionalAssetDetails) GetAssetType() string {
	if x != nil {
		return x.AssetType
	}
	return ""
}

func (x *UnconventionalAssetDetails) GetAiCommentary() string {
	if x != nil {
		return x.AiCommentary
	}
	return ""
}

var File_api_insights_networth_magicimport_magic_import_details_proto protoreflect.FileDescriptor

var file_api_insights_networth_magicimport_magic_import_details_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70,
	0x6f, 0x72, 0x74, 0x2f, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x5f, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x1a, 0x37, 0x61,
	0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73,
	0x74, 0x6d, 0x65, 0x6e, 0x74, 0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x9f, 0x01, 0x0a, 0x12, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x64, 0x0a, 0x12, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f,
	0x72, 0x74, 0x2e, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x10, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x23, 0x0a, 0x0d,
	0x61, 0x69, 0x5f, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x69, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72,
	0x79, 0x22, 0xf0, 0x02, 0x0a, 0x17, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x52, 0x0a,
	0x0a, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x33, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72,
	0x74, 0x2e, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x61, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x77, 0x0a, 0x1a, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69,
	0x6d, 0x70, 0x6f, 0x72, 0x74, 0x2e, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00,
	0x52, 0x18, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x73,
	0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x7d, 0x0a, 0x1c, 0x75, 0x6e,
	0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x39, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x2e, 0x55, 0x6e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41,
	0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x1a, 0x75,
	0x6e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x22, 0x84, 0x02, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x73, 0x73, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x65, 0x0a, 0x16, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2e, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2e, 0x49, 0x6e, 0x76, 0x65,
	0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63, 0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x15, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x63,
	0x6c, 0x61, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x0f, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0e, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x65, 0x64,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x7f, 0x0a, 0x1a, 0x55,
	0x6e, 0x63, 0x6f, 0x6e, 0x76, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73,
	0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x73, 0x73, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x73, 0x73, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x69, 0x5f, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x61, 0x69, 0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x2a, 0x95, 0x01, 0x0a,
	0x14, 0x4d, 0x61, 0x67, 0x69, 0x63, 0x49, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x41, 0x73, 0x73, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x49,
	0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x28,
	0x0a, 0x24, 0x4d, 0x41, 0x47, 0x49, 0x43, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41,
	0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x4e,
	0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x4d, 0x41, 0x47, 0x49,
	0x43, 0x5f, 0x49, 0x4d, 0x50, 0x4f, 0x52, 0x54, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x43, 0x4f, 0x4e, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e,
	0x41, 0x4c, 0x10, 0x02, 0x42, 0x74, 0x0a, 0x38, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x74, 0x68, 0x2e, 0x6d, 0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74,
	0x5a, 0x38, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x6d,
	0x61, 0x67, 0x69, 0x63, 0x69, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_insights_networth_magicimport_magic_import_details_proto_rawDescOnce sync.Once
	file_api_insights_networth_magicimport_magic_import_details_proto_rawDescData = file_api_insights_networth_magicimport_magic_import_details_proto_rawDesc
)

func file_api_insights_networth_magicimport_magic_import_details_proto_rawDescGZIP() []byte {
	file_api_insights_networth_magicimport_magic_import_details_proto_rawDescOnce.Do(func() {
		file_api_insights_networth_magicimport_magic_import_details_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_insights_networth_magicimport_magic_import_details_proto_rawDescData)
	})
	return file_api_insights_networth_magicimport_magic_import_details_proto_rawDescData
}

var file_api_insights_networth_magicimport_magic_import_details_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_insights_networth_magicimport_magic_import_details_proto_goTypes = []interface{}{
	(MagicImportAssetType)(0),           // 0: insights.networth.magicimport.MagicImportAssetType
	(*MagicImportDetails)(nil),          // 1: insights.networth.magicimport.MagicImportDetails
	(*MagicImportAssetDetails)(nil),     // 2: insights.networth.magicimport.MagicImportAssetDetails
	(*ConventionalAssetDetails)(nil),    // 3: insights.networth.magicimport.ConventionalAssetDetails
	(*UnconventionalAssetDetails)(nil),  // 4: insights.networth.magicimport.UnconventionalAssetDetails
	(*model.InvestmentDeclaration)(nil), // 5: insights.networth.model.InvestmentDeclaration
	(*money.Money)(nil),                 // 6: google.type.Money
}
var file_api_insights_networth_magicimport_magic_import_details_proto_depIdxs = []int32{
	2, // 0: insights.networth.magicimport.MagicImportDetails.asset_details_list:type_name -> insights.networth.magicimport.MagicImportAssetDetails
	0, // 1: insights.networth.magicimport.MagicImportAssetDetails.asset_type:type_name -> insights.networth.magicimport.MagicImportAssetType
	3, // 2: insights.networth.magicimport.MagicImportAssetDetails.conventional_asset_details:type_name -> insights.networth.magicimport.ConventionalAssetDetails
	4, // 3: insights.networth.magicimport.MagicImportAssetDetails.unconventional_asset_details:type_name -> insights.networth.magicimport.UnconventionalAssetDetails
	5, // 4: insights.networth.magicimport.ConventionalAssetDetails.investment_declaration:type_name -> insights.networth.model.InvestmentDeclaration
	6, // 5: insights.networth.magicimport.ConventionalAssetDetails.estimated_value:type_name -> google.type.Money
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_insights_networth_magicimport_magic_import_details_proto_init() }
func file_api_insights_networth_magicimport_magic_import_details_proto_init() {
	if File_api_insights_networth_magicimport_magic_import_details_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MagicImportDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MagicImportAssetDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConventionalAssetDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnconventionalAssetDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*MagicImportAssetDetails_ConventionalAssetDetails)(nil),
		(*MagicImportAssetDetails_UnconventionalAssetDetails)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_insights_networth_magicimport_magic_import_details_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_insights_networth_magicimport_magic_import_details_proto_goTypes,
		DependencyIndexes: file_api_insights_networth_magicimport_magic_import_details_proto_depIdxs,
		EnumInfos:         file_api_insights_networth_magicimport_magic_import_details_proto_enumTypes,
		MessageInfos:      file_api_insights_networth_magicimport_magic_import_details_proto_msgTypes,
	}.Build()
	File_api_insights_networth_magicimport_magic_import_details_proto = out.File
	file_api_insights_networth_magicimport_magic_import_details_proto_rawDesc = nil
	file_api_insights_networth_magicimport_magic_import_details_proto_goTypes = nil
	file_api_insights_networth_magicimport_magic_import_details_proto_depIdxs = nil
}
