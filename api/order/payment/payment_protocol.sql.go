package payment

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"

	"github.com/golang/protobuf/proto"
)

// Marshaller interface implementation, to marshal enum to string
func (p PaymentProtocol) MarshalJSON() ([]byte, error) {
	return json.Marshal(p.String())
}

// UnMarshaller interface implementation, to unmarshal string to enum
func (p *PaymentProtocol) UnmarshalJSON(inp []byte) error {
	valInt, err := proto.UnmarshalJSONEnum(PaymentProtocol_value, inp, "order.payment.PaymentProtocol")
	if err != nil {
		return fmt.Errorf("unable to unmarshal to PaymentProtocol value: %w", err)
	}

	*p = PaymentProtocol(valInt)
	return nil
}

// Valuer interface implementation for storing the data in string format in DB
func (p PaymentProtocol) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing data while reading from DB
func (p *PaymentProtocol) Scan(src interface{}) error {
	val, ok := src.(string)
	if !ok {
		return fmt.Errorf("expected string got %T", src)
	}

	valInt, ok := PaymentProtocol_value[val]
	if !ok {
		return fmt.Errorf("unexpected PaymentProtocol value: %s", val)
	}

	*p = PaymentProtocol(valInt)
	return nil
}
