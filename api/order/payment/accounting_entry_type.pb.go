// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/order/payment/accounting_entry_type.proto

package payment

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AccountingEntryType represents the type of transaction that was requested/completed.
// It can be of type Debit or Credit.
type AccountingEntryType int32

const (
	AccountingEntryType_ACCOUNTING_ENTRY_TYPE_UNSPECIFIED AccountingEntryType = 0
	// DEBIT implies that given amount was deducted from the account in the transaction.
	AccountingEntryType_DEBIT AccountingEntryType = 1
	// CREDIT denotes given amount was added to the account in the transaction.
	AccountingEntryType_CREDIT AccountingEntryType = 2
)

// Enum value maps for AccountingEntryType.
var (
	AccountingEntryType_name = map[int32]string{
		0: "ACCOUNTING_ENTRY_TYPE_UNSPECIFIED",
		1: "DEBIT",
		2: "CREDIT",
	}
	AccountingEntryType_value = map[string]int32{
		"ACCOUNTING_ENTRY_TYPE_UNSPECIFIED": 0,
		"DEBIT":                             1,
		"CREDIT":                            2,
	}
)

func (x AccountingEntryType) Enum() *AccountingEntryType {
	p := new(AccountingEntryType)
	*p = x
	return p
}

func (x AccountingEntryType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AccountingEntryType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_payment_accounting_entry_type_proto_enumTypes[0].Descriptor()
}

func (AccountingEntryType) Type() protoreflect.EnumType {
	return &file_api_order_payment_accounting_entry_type_proto_enumTypes[0]
}

func (x AccountingEntryType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AccountingEntryType.Descriptor instead.
func (AccountingEntryType) EnumDescriptor() ([]byte, []int) {
	return file_api_order_payment_accounting_entry_type_proto_rawDescGZIP(), []int{0}
}

var File_api_order_payment_accounting_entry_type_proto protoreflect.FileDescriptor

var file_api_order_payment_accounting_entry_type_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65,
	0x6e, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2a, 0x53,
	0x0a, 0x13, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x21, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x52, 0x45, 0x44, 0x49,
	0x54, 0x10, 0x02, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5a,
	0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_order_payment_accounting_entry_type_proto_rawDescOnce sync.Once
	file_api_order_payment_accounting_entry_type_proto_rawDescData = file_api_order_payment_accounting_entry_type_proto_rawDesc
)

func file_api_order_payment_accounting_entry_type_proto_rawDescGZIP() []byte {
	file_api_order_payment_accounting_entry_type_proto_rawDescOnce.Do(func() {
		file_api_order_payment_accounting_entry_type_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_order_payment_accounting_entry_type_proto_rawDescData)
	})
	return file_api_order_payment_accounting_entry_type_proto_rawDescData
}

var file_api_order_payment_accounting_entry_type_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_order_payment_accounting_entry_type_proto_goTypes = []interface{}{
	(AccountingEntryType)(0), // 0: order.payment.AccountingEntryType
}
var file_api_order_payment_accounting_entry_type_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_order_payment_accounting_entry_type_proto_init() }
func file_api_order_payment_accounting_entry_type_proto_init() {
	if File_api_order_payment_accounting_entry_type_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_order_payment_accounting_entry_type_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_order_payment_accounting_entry_type_proto_goTypes,
		DependencyIndexes: file_api_order_payment_accounting_entry_type_proto_depIdxs,
		EnumInfos:         file_api_order_payment_accounting_entry_type_proto_enumTypes,
	}.Build()
	File_api_order_payment_accounting_entry_type_proto = out.File
	file_api_order_payment_accounting_entry_type_proto_rawDesc = nil
	file_api_order_payment_accounting_entry_type_proto_goTypes = nil
	file_api_order_payment_accounting_entry_type_proto_depIdxs = nil
}
