// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/user/onboarding/service.proto

package onboarding

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Onboarding_CheckAccountSetupStatus_FullMethodName               = "/user.onboarding.Onboarding/CheckAccountSetupStatus"
	Onboarding_GetPinCodeDetails_FullMethodName                     = "/user.onboarding.Onboarding/GetPinCodeDetails"
	Onboarding_AddPinCodeEntry_FullMethodName                       = "/user.onboarding.Onboarding/AddPinCodeEntry"
	Onboarding_GetNextAction_FullMethodName                         = "/user.onboarding.Onboarding/GetNextAction"
	Onboarding_GetDetails_FullMethodName                            = "/user.onboarding.Onboarding/GetDetails"
	Onboarding_GetTroubleshootingDetails_FullMethodName             = "/user.onboarding.Onboarding/GetTroubleshootingDetails"
	Onboarding_ResetUser_FullMethodName                             = "/user.onboarding.Onboarding/ResetUser"
	Onboarding_ResetStage_FullMethodName                            = "/user.onboarding.Onboarding/ResetStage"
	Onboarding_RegisterUserForVKYC_FullMethodName                   = "/user.onboarding.Onboarding/RegisterUserForVKYC"
	Onboarding_GetOnboardedUser_FullMethodName                      = "/user.onboarding.Onboarding/GetOnboardedUser"
	Onboarding_UpdateStage_FullMethodName                           = "/user.onboarding.Onboarding/UpdateStage"
	Onboarding_GetUnonboardedUsers_FullMethodName                   = "/user.onboarding.Onboarding/GetUnonboardedUsers"
	Onboarding_DebitCardNameCheck_FullMethodName                    = "/user.onboarding.Onboarding/DebitCardNameCheck"
	Onboarding_ResetDebitCardNameRetries_FullMethodName             = "/user.onboarding.Onboarding/ResetDebitCardNameRetries"
	Onboarding_GetOnboardedUsers_FullMethodName                     = "/user.onboarding.Onboarding/GetOnboardedUsers"
	Onboarding_StartOnboarding_FullMethodName                       = "/user.onboarding.Onboarding/StartOnboarding"
	Onboarding_GetQueueElements_FullMethodName                      = "/user.onboarding.Onboarding/GetQueueElements"
	Onboarding_DeleteQueueElement_FullMethodName                    = "/user.onboarding.Onboarding/DeleteQueueElement"
	Onboarding_CountQueueElements_FullMethodName                    = "/user.onboarding.Onboarding/CountQueueElements"
	Onboarding_UpdatePanNameVerdict_FullMethodName                  = "/user.onboarding.Onboarding/UpdatePanNameVerdict"
	Onboarding_GetOnboardingDetailsMin_FullMethodName               = "/user.onboarding.Onboarding/GetOnboardingDetailsMin"
	Onboarding_ProcessRiskVerdict_FullMethodName                    = "/user.onboarding.Onboarding/ProcessRiskVerdict"
	Onboarding_UpdateFiLiteAccessibility_FullMethodName             = "/user.onboarding.Onboarding/UpdateFiLiteAccessibility"
	Onboarding_GetFeatureDetails_FullMethodName                     = "/user.onboarding.Onboarding/GetFeatureDetails"
	Onboarding_SetOnboardingIntent_FullMethodName                   = "/user.onboarding.Onboarding/SetOnboardingIntent"
	Onboarding_GetOnboardingSoftIntentOptions_FullMethodName        = "/user.onboarding.Onboarding/GetOnboardingSoftIntentOptions"
	Onboarding_SetOnboardingSoftIntent_FullMethodName               = "/user.onboarding.Onboarding/SetOnboardingSoftIntent"
	Onboarding_PerformStage_FullMethodName                          = "/user.onboarding.Onboarding/PerformStage"
	Onboarding_GetIntentSelectionDeeplink_FullMethodName            = "/user.onboarding.Onboarding/GetIntentSelectionDeeplink"
	Onboarding_UploadPassport_FullMethodName                        = "/user.onboarding.Onboarding/UploadPassport"
	Onboarding_GetFeatureLifecycle_FullMethodName                   = "/user.onboarding.Onboarding/GetFeatureLifecycle"
	Onboarding_FetchPassport_FullMethodName                         = "/user.onboarding.Onboarding/FetchPassport"
	Onboarding_ProcessUserAck_FullMethodName                        = "/user.onboarding.Onboarding/ProcessUserAck"
	Onboarding_AddQueueElement_FullMethodName                       = "/user.onboarding.Onboarding/AddQueueElement"
	Onboarding_VerifyGlobalIssuedPassport_FullMethodName            = "/user.onboarding.Onboarding/VerifyGlobalIssuedPassport"
	Onboarding_ProcessCrossValidationManualReview_FullMethodName    = "/user.onboarding.Onboarding/ProcessCrossValidationManualReview"
	Onboarding_GetDataForCrossValidationManualReview_FullMethodName = "/user.onboarding.Onboarding/GetDataForCrossValidationManualReview"
	Onboarding_ProcessPassportManualReview_FullMethodName           = "/user.onboarding.Onboarding/ProcessPassportManualReview"
)

// OnboardingClient is the client API for Onboarding service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OnboardingClient interface {
	// Terminology:
	// 1. Stage: Represents a step in onboarding process - Customer Creation, Account creation etc.
	// 2. State: Current state of a stage - Initiated, Inprogress, Succuss etc.
	//
	// CheckAccountSetupStatus returns the current account setup status for given actor and vendor.
	// The api currently populates status in db on demand, i.e. the db update for the service is done when the
	// api is triggered. However we optimise the service calls by avoiding querying respective services for stages
	// which has reached terminal states.
	CheckAccountSetupStatus(ctx context.Context, in *CheckAccountSetupStatusRequest, opts ...grpc.CallOption) (*CheckAccountSetupStatusResponse, error)
	// Given the PIN code of an area, this API returns a list of cities and a state to which this PIN code belongs to.
	// List of cities as a PIN code can belong to more than 1 city (though such occurrences are rare)
	// In case the PIN code belongs to only 1 city (which is the most common scenario), the list wll contain only 1 city name
	GetPinCodeDetails(ctx context.Context, in *GetPinCodeDetailsRequest, opts ...grpc.CallOption) (*GetPinCodeDetailsResponse, error)
	// this rpc is used for storing pincode details
	AddPinCodeEntry(ctx context.Context, in *AddPinCodeEntryRequest, opts ...grpc.CallOption) (*AddPinCodeEntryResponse, error)
	// GetNextAction evaluates and returns the next onboarding
	// action for the user. onboarding starts once email is verified and
	// carries on till user is landed on home page.
	GetNextAction(ctx context.Context, in *GetNextActionRequest, opts ...grpc.CallOption) (*GetNextActionResponse, error)
	// GetDetails returns current onboarding status of the stages.
	// If the onboarding is not complete, fetches the latest states
	// of the stage next in line and returns the updated status.
	GetDetails(ctx context.Context, in *GetDetailsRequest, opts ...grpc.CallOption) (*GetDetailsResponse, error)
	// GetTroubleshootingDetails returns all info required to trouble shoot stuck users.
	// The idea is to get the whole picture at one place. It's a batched API.
	// For a given stage, the API can return 3 levels of in-depth root cause analysis.
	GetTroubleshootingDetails(ctx context.Context, in *GetTroubleshootingDetailsRequest, opts ...grpc.CallOption) (*GetTroubleshootingDetailsResponse, error)
	// RPC to allow a user to restart onboarding from scratch again. This RPC
	// after validating if it’s possible to safely reset a user, calls
	// necessary backend services to delete corresponding user entries.
	// E.g. user, actor & auth token entries are deleted. This way,
	// the user when trying to onboard again, will be considered a new user.
	ResetUser(ctx context.Context, in *ResetUserRequest, opts ...grpc.CallOption) (*ResetUserResponse, error)
	// RPC to "reset" a stage. "Reset" will allow "re-performing" a partially/fully completed stage.
	//
	//	For e.g. a user entered the wrong parents
	//
	// name and would like to add them again after completing the stage.
	// The RPC first reset the dependent stages before resetting the input stage.
	// If reset for a stage is not possible PERMISSION DENIED status is returned.
	ResetStage(ctx context.Context, in *ResetStageRequest, opts ...grpc.CallOption) (*ResetStageResponse, error)
	// RPC to register a user for VKYC
	// This will collect data required for registration and call vkyc backend service
	RegisterUserForVKYC(ctx context.Context, in *RegisterUserForVKYCRequest, opts ...grpc.CallOption) (*RegisterUserForVKYCResponse, error)
	// GetOnboardedUser returns user details along with onboarding completion timestamp, for a given user
	// identifier
	GetOnboardedUser(ctx context.Context, in *GetOnboardedUserRequest, opts ...grpc.CallOption) (*GetOnboardedUserResponse, error)
	// UpdateStage forcefully updates the status of a stage. Use with caution.
	UpdateStage(ctx context.Context, in *UpdateStageRequest, opts ...grpc.CallOption) (*UpdateStageResponse, error)
	// GetUnonboardedUsers fetches all the currently unonboarded users
	GetUnonboardedUsers(ctx context.Context, in *GetUnonboardedUsersRequest, opts ...grpc.CallOption) (*GetUnonboardedUsersResponse, error)
	// rpc to check user entered debit card name with KYC and PAN name using the name match algorithm
	// and if name match passes then updates debit card name in user
	DebitCardNameCheck(ctx context.Context, in *DebitCardNameCheckRequest, opts ...grpc.CallOption) (*DebitCardNameCheckResponse, error)
	// rpc to reset retries for debit card name match.
	// This would allow users to retry debit card name match after the initial retries are exhausted and they reach
	// out via CX team.
	ResetDebitCardNameRetries(ctx context.Context, in *ResetDebitCardNameRetriesRequest, opts ...grpc.CallOption) (*ResetDebitCardNameRetriesResponse, error)
	// rpc to fetch onboarded users after/before a time
	// this is a paginated rpc
	// If fromTime and ToTime both are passed, all the onboarded users
	// between this time are returned
	// if only from time is passed onboarded users after the from time are returned
	// if only toTime is passed onboarded users after the toTime are returned
	// if both from and to time are not passed then returns invalid argument
	// NOTE - max page size supported is 50
	GetOnboardedUsers(ctx context.Context, in *GetOnboardedUsersRequest, opts ...grpc.CallOption) (*GetOnboardedUsersResponse, error)
	// rpc to create new onboarding entry for a user
	StartOnboarding(ctx context.Context, in *StartOnboardingRequest, opts ...grpc.CallOption) (*StartOnboardingResponse, error)
	// rpc to fetch the elements in the persistent queue based on a specific payload type
	// The max limit supported is 100, default limit is 20 if no limit is passed
	GetQueueElements(ctx context.Context, in *GetQueueElementsRequest, opts ...grpc.CallOption) (*GetQueueElementsResponse, error)
	// rpc to delete elements from the persistent queue
	DeleteQueueElement(ctx context.Context, in *DeleteQueueElementRequest, opts ...grpc.CallOption) (*DeleteQueueElementResponse, error)
	CountQueueElements(ctx context.Context, in *CountQueueElementsRequest, opts ...grpc.CallOption) (*CountQueueElementsResponse, error)
	// rpc to update the verdict on pan name mis-match during onboarding
	UpdatePanNameVerdict(ctx context.Context, in *UpdatePanNameVerdictRequest, opts ...grpc.CallOption) (*UpdatePanNameVerdictResponse, error)
	// rpc to get minimal onboarding details
	// this rpc fetches only minimal onboarding details from the cache. (Populates in cache if not found)
	GetOnboardingDetailsMin(ctx context.Context, in *GetOnboardingDetailsMinRequest, opts ...grpc.CallOption) (*GetOnboardingDetailsMinResponse, error)
	// ProcessRiskVerdict is an API to block/unblock onboarding journey of a user based on liveness/risk review
	ProcessRiskVerdict(ctx context.Context, in *ProcessRiskVerdictRequest, opts ...grpc.CallOption) (*ProcessRiskVerdictResponse, error)
	// UpdateFiLiteAccessibility rpc is used to updates the status of Fi lite accessibility of the user
	// If this rpc is called then user will be redirected to home and will always land on home if user reopens the app
	UpdateFiLiteAccessibility(ctx context.Context, in *UpdateFiLiteAccessibilityRequest, opts ...grpc.CallOption) (*UpdateFiLiteAccessibilityResponse, error)
	// GetFeatureDetails rpc will be used to fetch details regarding the fi lite status of an actor and other relevant fi lite details
	GetFeatureDetails(ctx context.Context, in *GetFeatureDetailsRequest, opts ...grpc.CallOption) (*GetFeatureDetailsResponse, error)
	// SetOnboardingIntent rpc will be used to set the onboarding journey for a user based
	// on the intent selected upon prompt.
	SetOnboardingIntent(ctx context.Context, in *SetOnboardingIntentRequest, opts ...grpc.CallOption) (*SetOnboardingIntentResponse, error)
	// GetOnboardingSoftIntentOptions rpc will be used to get the soft intent options for a user
	GetOnboardingSoftIntentOptions(ctx context.Context, in *GetOnboardingSoftIntentOptionsRequest, opts ...grpc.CallOption) (*GetOnboardingSoftIntentOptionsResponse, error)
	// SetOnboardingSoftIntent rpc will be used to set the soft intent choices for a user
	// These soft intent choices will further be used to personalise their in-app experience
	SetOnboardingSoftIntent(ctx context.Context, in *SetOnboardingSoftIntentRequest, opts ...grpc.CallOption) (*SetOnboardingSoftIntentResponse, error)
	// PerformStage rpc will be used by the callers to run a particular stage order for a user based on feature up until the
	// stage which is passed in the request
	// The rpc will return a deeplink if needed and will return the stage status and the current stage
	// NextAction should be preferred over StageState, since a deeplink needs to be shown
	// If NextAction is nil then status needs to be checked, SKIPPED/SUCCESS/FAILURE essentially are terminal states
	PerformStage(ctx context.Context, in *PerformStageRequest, opts ...grpc.CallOption) (*PerformStageResponse, error)
	// rpc to serve the intent selection deeplink as this deeplink is attached to the onboarding config
	GetIntentSelectionDeeplink(ctx context.Context, in *GetIntentSelectionDeeplinkRequest, opts ...grpc.CallOption) (*GetIntentSelectionDeeplinkResponse, error)
	// UploadPassport stores passed passport image in s3. It also updates the passport verification onboarding stage
	// metadata depending on what side of the passport was uploaded.
	// This is only to be triggered from passport verification onboarding stage.
	UploadPassport(ctx context.Context, in *UploadPassportRequest, opts ...grpc.CallOption) (*UploadPassportResponse, error)
	// GetFeatureLifecycle returns all the details pertaining to the lifecycle of a feature for a user
	// It returns the following details for each feature for a user -
	// 1. intent selection info - what is the intent selection info of the feature
	// 2. eligibility status - what is the eligibility status of the feature
	// 3. activation status - what is the activation status of the feature
	// Use case - Home, comms and other in-app properties require intent, eligibility and activation to determine the best cross-selling options
	GetFeatureLifecycle(ctx context.Context, in *GetFeatureLifecycleRequest, opts ...grpc.CallOption) (*GetFeatureLifecycleResponse, error)
	// FetchPassport fetches the passport data for a user
	FetchPassport(ctx context.Context, in *FetchPassportRequest, opts ...grpc.CallOption) (*FetchPassportResponse, error)
	// ProcessUserAck: Listens for acknowledgements from the client
	// For example, when a user interacts with a CTA or banner, the client can send an acknowledgement to the backend
	ProcessUserAck(ctx context.Context, in *ProcessUserAckRequest, opts ...grpc.CallOption) (*ProcessUserAckResponse, error)
	// RPC to add queue elements to persistent queue, it takes in the payload to be added to the queue
	AddQueueElement(ctx context.Context, in *AddQueueElementRequest, opts ...grpc.CallOption) (*AddQueueElementResponse, error)
	// RPC to verify the passport which is issued outside india
	VerifyGlobalIssuedPassport(ctx context.Context, in *VerifyGlobalIssuedPassportRequest, opts ...grpc.CallOption) (*VerifyGlobalIssuedPassportResponse, error)
	// RPC to update cross validation manual review info for NR users
	ProcessCrossValidationManualReview(ctx context.Context, in *ProcessCrossValidationManualReviewRequest, opts ...grpc.CallOption) (*ProcessCrossValidationManualReviewResponse, error)
	// RPC to get cross validation data for manual review
	GetDataForCrossValidationManualReview(ctx context.Context, in *GetDataForCrossValidationManualReviewRequest, opts ...grpc.CallOption) (*GetDataForCrossValidationManualReviewResponse, error)
	// RPC to update passport manual review status for the passport issued outside india for NR users
	ProcessPassportManualReview(ctx context.Context, in *ProcessPassportManualReviewRequest, opts ...grpc.CallOption) (*ProcessPassportManualReviewResponse, error)
}

type onboardingClient struct {
	cc grpc.ClientConnInterface
}

func NewOnboardingClient(cc grpc.ClientConnInterface) OnboardingClient {
	return &onboardingClient{cc}
}

func (c *onboardingClient) CheckAccountSetupStatus(ctx context.Context, in *CheckAccountSetupStatusRequest, opts ...grpc.CallOption) (*CheckAccountSetupStatusResponse, error) {
	out := new(CheckAccountSetupStatusResponse)
	err := c.cc.Invoke(ctx, Onboarding_CheckAccountSetupStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetPinCodeDetails(ctx context.Context, in *GetPinCodeDetailsRequest, opts ...grpc.CallOption) (*GetPinCodeDetailsResponse, error) {
	out := new(GetPinCodeDetailsResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetPinCodeDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) AddPinCodeEntry(ctx context.Context, in *AddPinCodeEntryRequest, opts ...grpc.CallOption) (*AddPinCodeEntryResponse, error) {
	out := new(AddPinCodeEntryResponse)
	err := c.cc.Invoke(ctx, Onboarding_AddPinCodeEntry_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetNextAction(ctx context.Context, in *GetNextActionRequest, opts ...grpc.CallOption) (*GetNextActionResponse, error) {
	out := new(GetNextActionResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetNextAction_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetDetails(ctx context.Context, in *GetDetailsRequest, opts ...grpc.CallOption) (*GetDetailsResponse, error) {
	out := new(GetDetailsResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetTroubleshootingDetails(ctx context.Context, in *GetTroubleshootingDetailsRequest, opts ...grpc.CallOption) (*GetTroubleshootingDetailsResponse, error) {
	out := new(GetTroubleshootingDetailsResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetTroubleshootingDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ResetUser(ctx context.Context, in *ResetUserRequest, opts ...grpc.CallOption) (*ResetUserResponse, error) {
	out := new(ResetUserResponse)
	err := c.cc.Invoke(ctx, Onboarding_ResetUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ResetStage(ctx context.Context, in *ResetStageRequest, opts ...grpc.CallOption) (*ResetStageResponse, error) {
	out := new(ResetStageResponse)
	err := c.cc.Invoke(ctx, Onboarding_ResetStage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) RegisterUserForVKYC(ctx context.Context, in *RegisterUserForVKYCRequest, opts ...grpc.CallOption) (*RegisterUserForVKYCResponse, error) {
	out := new(RegisterUserForVKYCResponse)
	err := c.cc.Invoke(ctx, Onboarding_RegisterUserForVKYC_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetOnboardedUser(ctx context.Context, in *GetOnboardedUserRequest, opts ...grpc.CallOption) (*GetOnboardedUserResponse, error) {
	out := new(GetOnboardedUserResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetOnboardedUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) UpdateStage(ctx context.Context, in *UpdateStageRequest, opts ...grpc.CallOption) (*UpdateStageResponse, error) {
	out := new(UpdateStageResponse)
	err := c.cc.Invoke(ctx, Onboarding_UpdateStage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetUnonboardedUsers(ctx context.Context, in *GetUnonboardedUsersRequest, opts ...grpc.CallOption) (*GetUnonboardedUsersResponse, error) {
	out := new(GetUnonboardedUsersResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetUnonboardedUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) DebitCardNameCheck(ctx context.Context, in *DebitCardNameCheckRequest, opts ...grpc.CallOption) (*DebitCardNameCheckResponse, error) {
	out := new(DebitCardNameCheckResponse)
	err := c.cc.Invoke(ctx, Onboarding_DebitCardNameCheck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ResetDebitCardNameRetries(ctx context.Context, in *ResetDebitCardNameRetriesRequest, opts ...grpc.CallOption) (*ResetDebitCardNameRetriesResponse, error) {
	out := new(ResetDebitCardNameRetriesResponse)
	err := c.cc.Invoke(ctx, Onboarding_ResetDebitCardNameRetries_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetOnboardedUsers(ctx context.Context, in *GetOnboardedUsersRequest, opts ...grpc.CallOption) (*GetOnboardedUsersResponse, error) {
	out := new(GetOnboardedUsersResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetOnboardedUsers_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) StartOnboarding(ctx context.Context, in *StartOnboardingRequest, opts ...grpc.CallOption) (*StartOnboardingResponse, error) {
	out := new(StartOnboardingResponse)
	err := c.cc.Invoke(ctx, Onboarding_StartOnboarding_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetQueueElements(ctx context.Context, in *GetQueueElementsRequest, opts ...grpc.CallOption) (*GetQueueElementsResponse, error) {
	out := new(GetQueueElementsResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetQueueElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) DeleteQueueElement(ctx context.Context, in *DeleteQueueElementRequest, opts ...grpc.CallOption) (*DeleteQueueElementResponse, error) {
	out := new(DeleteQueueElementResponse)
	err := c.cc.Invoke(ctx, Onboarding_DeleteQueueElement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) CountQueueElements(ctx context.Context, in *CountQueueElementsRequest, opts ...grpc.CallOption) (*CountQueueElementsResponse, error) {
	out := new(CountQueueElementsResponse)
	err := c.cc.Invoke(ctx, Onboarding_CountQueueElements_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) UpdatePanNameVerdict(ctx context.Context, in *UpdatePanNameVerdictRequest, opts ...grpc.CallOption) (*UpdatePanNameVerdictResponse, error) {
	out := new(UpdatePanNameVerdictResponse)
	err := c.cc.Invoke(ctx, Onboarding_UpdatePanNameVerdict_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetOnboardingDetailsMin(ctx context.Context, in *GetOnboardingDetailsMinRequest, opts ...grpc.CallOption) (*GetOnboardingDetailsMinResponse, error) {
	out := new(GetOnboardingDetailsMinResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetOnboardingDetailsMin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ProcessRiskVerdict(ctx context.Context, in *ProcessRiskVerdictRequest, opts ...grpc.CallOption) (*ProcessRiskVerdictResponse, error) {
	out := new(ProcessRiskVerdictResponse)
	err := c.cc.Invoke(ctx, Onboarding_ProcessRiskVerdict_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) UpdateFiLiteAccessibility(ctx context.Context, in *UpdateFiLiteAccessibilityRequest, opts ...grpc.CallOption) (*UpdateFiLiteAccessibilityResponse, error) {
	out := new(UpdateFiLiteAccessibilityResponse)
	err := c.cc.Invoke(ctx, Onboarding_UpdateFiLiteAccessibility_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetFeatureDetails(ctx context.Context, in *GetFeatureDetailsRequest, opts ...grpc.CallOption) (*GetFeatureDetailsResponse, error) {
	out := new(GetFeatureDetailsResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetFeatureDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) SetOnboardingIntent(ctx context.Context, in *SetOnboardingIntentRequest, opts ...grpc.CallOption) (*SetOnboardingIntentResponse, error) {
	out := new(SetOnboardingIntentResponse)
	err := c.cc.Invoke(ctx, Onboarding_SetOnboardingIntent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetOnboardingSoftIntentOptions(ctx context.Context, in *GetOnboardingSoftIntentOptionsRequest, opts ...grpc.CallOption) (*GetOnboardingSoftIntentOptionsResponse, error) {
	out := new(GetOnboardingSoftIntentOptionsResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetOnboardingSoftIntentOptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) SetOnboardingSoftIntent(ctx context.Context, in *SetOnboardingSoftIntentRequest, opts ...grpc.CallOption) (*SetOnboardingSoftIntentResponse, error) {
	out := new(SetOnboardingSoftIntentResponse)
	err := c.cc.Invoke(ctx, Onboarding_SetOnboardingSoftIntent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) PerformStage(ctx context.Context, in *PerformStageRequest, opts ...grpc.CallOption) (*PerformStageResponse, error) {
	out := new(PerformStageResponse)
	err := c.cc.Invoke(ctx, Onboarding_PerformStage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetIntentSelectionDeeplink(ctx context.Context, in *GetIntentSelectionDeeplinkRequest, opts ...grpc.CallOption) (*GetIntentSelectionDeeplinkResponse, error) {
	out := new(GetIntentSelectionDeeplinkResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetIntentSelectionDeeplink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) UploadPassport(ctx context.Context, in *UploadPassportRequest, opts ...grpc.CallOption) (*UploadPassportResponse, error) {
	out := new(UploadPassportResponse)
	err := c.cc.Invoke(ctx, Onboarding_UploadPassport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetFeatureLifecycle(ctx context.Context, in *GetFeatureLifecycleRequest, opts ...grpc.CallOption) (*GetFeatureLifecycleResponse, error) {
	out := new(GetFeatureLifecycleResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetFeatureLifecycle_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) FetchPassport(ctx context.Context, in *FetchPassportRequest, opts ...grpc.CallOption) (*FetchPassportResponse, error) {
	out := new(FetchPassportResponse)
	err := c.cc.Invoke(ctx, Onboarding_FetchPassport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ProcessUserAck(ctx context.Context, in *ProcessUserAckRequest, opts ...grpc.CallOption) (*ProcessUserAckResponse, error) {
	out := new(ProcessUserAckResponse)
	err := c.cc.Invoke(ctx, Onboarding_ProcessUserAck_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) AddQueueElement(ctx context.Context, in *AddQueueElementRequest, opts ...grpc.CallOption) (*AddQueueElementResponse, error) {
	out := new(AddQueueElementResponse)
	err := c.cc.Invoke(ctx, Onboarding_AddQueueElement_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) VerifyGlobalIssuedPassport(ctx context.Context, in *VerifyGlobalIssuedPassportRequest, opts ...grpc.CallOption) (*VerifyGlobalIssuedPassportResponse, error) {
	out := new(VerifyGlobalIssuedPassportResponse)
	err := c.cc.Invoke(ctx, Onboarding_VerifyGlobalIssuedPassport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ProcessCrossValidationManualReview(ctx context.Context, in *ProcessCrossValidationManualReviewRequest, opts ...grpc.CallOption) (*ProcessCrossValidationManualReviewResponse, error) {
	out := new(ProcessCrossValidationManualReviewResponse)
	err := c.cc.Invoke(ctx, Onboarding_ProcessCrossValidationManualReview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) GetDataForCrossValidationManualReview(ctx context.Context, in *GetDataForCrossValidationManualReviewRequest, opts ...grpc.CallOption) (*GetDataForCrossValidationManualReviewResponse, error) {
	out := new(GetDataForCrossValidationManualReviewResponse)
	err := c.cc.Invoke(ctx, Onboarding_GetDataForCrossValidationManualReview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *onboardingClient) ProcessPassportManualReview(ctx context.Context, in *ProcessPassportManualReviewRequest, opts ...grpc.CallOption) (*ProcessPassportManualReviewResponse, error) {
	out := new(ProcessPassportManualReviewResponse)
	err := c.cc.Invoke(ctx, Onboarding_ProcessPassportManualReview_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OnboardingServer is the server API for Onboarding service.
// All implementations should embed UnimplementedOnboardingServer
// for forward compatibility
type OnboardingServer interface {
	// Terminology:
	// 1. Stage: Represents a step in onboarding process - Customer Creation, Account creation etc.
	// 2. State: Current state of a stage - Initiated, Inprogress, Succuss etc.
	//
	// CheckAccountSetupStatus returns the current account setup status for given actor and vendor.
	// The api currently populates status in db on demand, i.e. the db update for the service is done when the
	// api is triggered. However we optimise the service calls by avoiding querying respective services for stages
	// which has reached terminal states.
	CheckAccountSetupStatus(context.Context, *CheckAccountSetupStatusRequest) (*CheckAccountSetupStatusResponse, error)
	// Given the PIN code of an area, this API returns a list of cities and a state to which this PIN code belongs to.
	// List of cities as a PIN code can belong to more than 1 city (though such occurrences are rare)
	// In case the PIN code belongs to only 1 city (which is the most common scenario), the list wll contain only 1 city name
	GetPinCodeDetails(context.Context, *GetPinCodeDetailsRequest) (*GetPinCodeDetailsResponse, error)
	// this rpc is used for storing pincode details
	AddPinCodeEntry(context.Context, *AddPinCodeEntryRequest) (*AddPinCodeEntryResponse, error)
	// GetNextAction evaluates and returns the next onboarding
	// action for the user. onboarding starts once email is verified and
	// carries on till user is landed on home page.
	GetNextAction(context.Context, *GetNextActionRequest) (*GetNextActionResponse, error)
	// GetDetails returns current onboarding status of the stages.
	// If the onboarding is not complete, fetches the latest states
	// of the stage next in line and returns the updated status.
	GetDetails(context.Context, *GetDetailsRequest) (*GetDetailsResponse, error)
	// GetTroubleshootingDetails returns all info required to trouble shoot stuck users.
	// The idea is to get the whole picture at one place. It's a batched API.
	// For a given stage, the API can return 3 levels of in-depth root cause analysis.
	GetTroubleshootingDetails(context.Context, *GetTroubleshootingDetailsRequest) (*GetTroubleshootingDetailsResponse, error)
	// RPC to allow a user to restart onboarding from scratch again. This RPC
	// after validating if it’s possible to safely reset a user, calls
	// necessary backend services to delete corresponding user entries.
	// E.g. user, actor & auth token entries are deleted. This way,
	// the user when trying to onboard again, will be considered a new user.
	ResetUser(context.Context, *ResetUserRequest) (*ResetUserResponse, error)
	// RPC to "reset" a stage. "Reset" will allow "re-performing" a partially/fully completed stage.
	//
	//	For e.g. a user entered the wrong parents
	//
	// name and would like to add them again after completing the stage.
	// The RPC first reset the dependent stages before resetting the input stage.
	// If reset for a stage is not possible PERMISSION DENIED status is returned.
	ResetStage(context.Context, *ResetStageRequest) (*ResetStageResponse, error)
	// RPC to register a user for VKYC
	// This will collect data required for registration and call vkyc backend service
	RegisterUserForVKYC(context.Context, *RegisterUserForVKYCRequest) (*RegisterUserForVKYCResponse, error)
	// GetOnboardedUser returns user details along with onboarding completion timestamp, for a given user
	// identifier
	GetOnboardedUser(context.Context, *GetOnboardedUserRequest) (*GetOnboardedUserResponse, error)
	// UpdateStage forcefully updates the status of a stage. Use with caution.
	UpdateStage(context.Context, *UpdateStageRequest) (*UpdateStageResponse, error)
	// GetUnonboardedUsers fetches all the currently unonboarded users
	GetUnonboardedUsers(context.Context, *GetUnonboardedUsersRequest) (*GetUnonboardedUsersResponse, error)
	// rpc to check user entered debit card name with KYC and PAN name using the name match algorithm
	// and if name match passes then updates debit card name in user
	DebitCardNameCheck(context.Context, *DebitCardNameCheckRequest) (*DebitCardNameCheckResponse, error)
	// rpc to reset retries for debit card name match.
	// This would allow users to retry debit card name match after the initial retries are exhausted and they reach
	// out via CX team.
	ResetDebitCardNameRetries(context.Context, *ResetDebitCardNameRetriesRequest) (*ResetDebitCardNameRetriesResponse, error)
	// rpc to fetch onboarded users after/before a time
	// this is a paginated rpc
	// If fromTime and ToTime both are passed, all the onboarded users
	// between this time are returned
	// if only from time is passed onboarded users after the from time are returned
	// if only toTime is passed onboarded users after the toTime are returned
	// if both from and to time are not passed then returns invalid argument
	// NOTE - max page size supported is 50
	GetOnboardedUsers(context.Context, *GetOnboardedUsersRequest) (*GetOnboardedUsersResponse, error)
	// rpc to create new onboarding entry for a user
	StartOnboarding(context.Context, *StartOnboardingRequest) (*StartOnboardingResponse, error)
	// rpc to fetch the elements in the persistent queue based on a specific payload type
	// The max limit supported is 100, default limit is 20 if no limit is passed
	GetQueueElements(context.Context, *GetQueueElementsRequest) (*GetQueueElementsResponse, error)
	// rpc to delete elements from the persistent queue
	DeleteQueueElement(context.Context, *DeleteQueueElementRequest) (*DeleteQueueElementResponse, error)
	CountQueueElements(context.Context, *CountQueueElementsRequest) (*CountQueueElementsResponse, error)
	// rpc to update the verdict on pan name mis-match during onboarding
	UpdatePanNameVerdict(context.Context, *UpdatePanNameVerdictRequest) (*UpdatePanNameVerdictResponse, error)
	// rpc to get minimal onboarding details
	// this rpc fetches only minimal onboarding details from the cache. (Populates in cache if not found)
	GetOnboardingDetailsMin(context.Context, *GetOnboardingDetailsMinRequest) (*GetOnboardingDetailsMinResponse, error)
	// ProcessRiskVerdict is an API to block/unblock onboarding journey of a user based on liveness/risk review
	ProcessRiskVerdict(context.Context, *ProcessRiskVerdictRequest) (*ProcessRiskVerdictResponse, error)
	// UpdateFiLiteAccessibility rpc is used to updates the status of Fi lite accessibility of the user
	// If this rpc is called then user will be redirected to home and will always land on home if user reopens the app
	UpdateFiLiteAccessibility(context.Context, *UpdateFiLiteAccessibilityRequest) (*UpdateFiLiteAccessibilityResponse, error)
	// GetFeatureDetails rpc will be used to fetch details regarding the fi lite status of an actor and other relevant fi lite details
	GetFeatureDetails(context.Context, *GetFeatureDetailsRequest) (*GetFeatureDetailsResponse, error)
	// SetOnboardingIntent rpc will be used to set the onboarding journey for a user based
	// on the intent selected upon prompt.
	SetOnboardingIntent(context.Context, *SetOnboardingIntentRequest) (*SetOnboardingIntentResponse, error)
	// GetOnboardingSoftIntentOptions rpc will be used to get the soft intent options for a user
	GetOnboardingSoftIntentOptions(context.Context, *GetOnboardingSoftIntentOptionsRequest) (*GetOnboardingSoftIntentOptionsResponse, error)
	// SetOnboardingSoftIntent rpc will be used to set the soft intent choices for a user
	// These soft intent choices will further be used to personalise their in-app experience
	SetOnboardingSoftIntent(context.Context, *SetOnboardingSoftIntentRequest) (*SetOnboardingSoftIntentResponse, error)
	// PerformStage rpc will be used by the callers to run a particular stage order for a user based on feature up until the
	// stage which is passed in the request
	// The rpc will return a deeplink if needed and will return the stage status and the current stage
	// NextAction should be preferred over StageState, since a deeplink needs to be shown
	// If NextAction is nil then status needs to be checked, SKIPPED/SUCCESS/FAILURE essentially are terminal states
	PerformStage(context.Context, *PerformStageRequest) (*PerformStageResponse, error)
	// rpc to serve the intent selection deeplink as this deeplink is attached to the onboarding config
	GetIntentSelectionDeeplink(context.Context, *GetIntentSelectionDeeplinkRequest) (*GetIntentSelectionDeeplinkResponse, error)
	// UploadPassport stores passed passport image in s3. It also updates the passport verification onboarding stage
	// metadata depending on what side of the passport was uploaded.
	// This is only to be triggered from passport verification onboarding stage.
	UploadPassport(context.Context, *UploadPassportRequest) (*UploadPassportResponse, error)
	// GetFeatureLifecycle returns all the details pertaining to the lifecycle of a feature for a user
	// It returns the following details for each feature for a user -
	// 1. intent selection info - what is the intent selection info of the feature
	// 2. eligibility status - what is the eligibility status of the feature
	// 3. activation status - what is the activation status of the feature
	// Use case - Home, comms and other in-app properties require intent, eligibility and activation to determine the best cross-selling options
	GetFeatureLifecycle(context.Context, *GetFeatureLifecycleRequest) (*GetFeatureLifecycleResponse, error)
	// FetchPassport fetches the passport data for a user
	FetchPassport(context.Context, *FetchPassportRequest) (*FetchPassportResponse, error)
	// ProcessUserAck: Listens for acknowledgements from the client
	// For example, when a user interacts with a CTA or banner, the client can send an acknowledgement to the backend
	ProcessUserAck(context.Context, *ProcessUserAckRequest) (*ProcessUserAckResponse, error)
	// RPC to add queue elements to persistent queue, it takes in the payload to be added to the queue
	AddQueueElement(context.Context, *AddQueueElementRequest) (*AddQueueElementResponse, error)
	// RPC to verify the passport which is issued outside india
	VerifyGlobalIssuedPassport(context.Context, *VerifyGlobalIssuedPassportRequest) (*VerifyGlobalIssuedPassportResponse, error)
	// RPC to update cross validation manual review info for NR users
	ProcessCrossValidationManualReview(context.Context, *ProcessCrossValidationManualReviewRequest) (*ProcessCrossValidationManualReviewResponse, error)
	// RPC to get cross validation data for manual review
	GetDataForCrossValidationManualReview(context.Context, *GetDataForCrossValidationManualReviewRequest) (*GetDataForCrossValidationManualReviewResponse, error)
	// RPC to update passport manual review status for the passport issued outside india for NR users
	ProcessPassportManualReview(context.Context, *ProcessPassportManualReviewRequest) (*ProcessPassportManualReviewResponse, error)
}

// UnimplementedOnboardingServer should be embedded to have forward compatible implementations.
type UnimplementedOnboardingServer struct {
}

func (UnimplementedOnboardingServer) CheckAccountSetupStatus(context.Context, *CheckAccountSetupStatusRequest) (*CheckAccountSetupStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAccountSetupStatus not implemented")
}
func (UnimplementedOnboardingServer) GetPinCodeDetails(context.Context, *GetPinCodeDetailsRequest) (*GetPinCodeDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPinCodeDetails not implemented")
}
func (UnimplementedOnboardingServer) AddPinCodeEntry(context.Context, *AddPinCodeEntryRequest) (*AddPinCodeEntryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddPinCodeEntry not implemented")
}
func (UnimplementedOnboardingServer) GetNextAction(context.Context, *GetNextActionRequest) (*GetNextActionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNextAction not implemented")
}
func (UnimplementedOnboardingServer) GetDetails(context.Context, *GetDetailsRequest) (*GetDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDetails not implemented")
}
func (UnimplementedOnboardingServer) GetTroubleshootingDetails(context.Context, *GetTroubleshootingDetailsRequest) (*GetTroubleshootingDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTroubleshootingDetails not implemented")
}
func (UnimplementedOnboardingServer) ResetUser(context.Context, *ResetUserRequest) (*ResetUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetUser not implemented")
}
func (UnimplementedOnboardingServer) ResetStage(context.Context, *ResetStageRequest) (*ResetStageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetStage not implemented")
}
func (UnimplementedOnboardingServer) RegisterUserForVKYC(context.Context, *RegisterUserForVKYCRequest) (*RegisterUserForVKYCResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterUserForVKYC not implemented")
}
func (UnimplementedOnboardingServer) GetOnboardedUser(context.Context, *GetOnboardedUserRequest) (*GetOnboardedUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnboardedUser not implemented")
}
func (UnimplementedOnboardingServer) UpdateStage(context.Context, *UpdateStageRequest) (*UpdateStageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateStage not implemented")
}
func (UnimplementedOnboardingServer) GetUnonboardedUsers(context.Context, *GetUnonboardedUsersRequest) (*GetUnonboardedUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUnonboardedUsers not implemented")
}
func (UnimplementedOnboardingServer) DebitCardNameCheck(context.Context, *DebitCardNameCheckRequest) (*DebitCardNameCheckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DebitCardNameCheck not implemented")
}
func (UnimplementedOnboardingServer) ResetDebitCardNameRetries(context.Context, *ResetDebitCardNameRetriesRequest) (*ResetDebitCardNameRetriesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetDebitCardNameRetries not implemented")
}
func (UnimplementedOnboardingServer) GetOnboardedUsers(context.Context, *GetOnboardedUsersRequest) (*GetOnboardedUsersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnboardedUsers not implemented")
}
func (UnimplementedOnboardingServer) StartOnboarding(context.Context, *StartOnboardingRequest) (*StartOnboardingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartOnboarding not implemented")
}
func (UnimplementedOnboardingServer) GetQueueElements(context.Context, *GetQueueElementsRequest) (*GetQueueElementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQueueElements not implemented")
}
func (UnimplementedOnboardingServer) DeleteQueueElement(context.Context, *DeleteQueueElementRequest) (*DeleteQueueElementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteQueueElement not implemented")
}
func (UnimplementedOnboardingServer) CountQueueElements(context.Context, *CountQueueElementsRequest) (*CountQueueElementsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountQueueElements not implemented")
}
func (UnimplementedOnboardingServer) UpdatePanNameVerdict(context.Context, *UpdatePanNameVerdictRequest) (*UpdatePanNameVerdictResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePanNameVerdict not implemented")
}
func (UnimplementedOnboardingServer) GetOnboardingDetailsMin(context.Context, *GetOnboardingDetailsMinRequest) (*GetOnboardingDetailsMinResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnboardingDetailsMin not implemented")
}
func (UnimplementedOnboardingServer) ProcessRiskVerdict(context.Context, *ProcessRiskVerdictRequest) (*ProcessRiskVerdictResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessRiskVerdict not implemented")
}
func (UnimplementedOnboardingServer) UpdateFiLiteAccessibility(context.Context, *UpdateFiLiteAccessibilityRequest) (*UpdateFiLiteAccessibilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFiLiteAccessibility not implemented")
}
func (UnimplementedOnboardingServer) GetFeatureDetails(context.Context, *GetFeatureDetailsRequest) (*GetFeatureDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFeatureDetails not implemented")
}
func (UnimplementedOnboardingServer) SetOnboardingIntent(context.Context, *SetOnboardingIntentRequest) (*SetOnboardingIntentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetOnboardingIntent not implemented")
}
func (UnimplementedOnboardingServer) GetOnboardingSoftIntentOptions(context.Context, *GetOnboardingSoftIntentOptionsRequest) (*GetOnboardingSoftIntentOptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnboardingSoftIntentOptions not implemented")
}
func (UnimplementedOnboardingServer) SetOnboardingSoftIntent(context.Context, *SetOnboardingSoftIntentRequest) (*SetOnboardingSoftIntentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetOnboardingSoftIntent not implemented")
}
func (UnimplementedOnboardingServer) PerformStage(context.Context, *PerformStageRequest) (*PerformStageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PerformStage not implemented")
}
func (UnimplementedOnboardingServer) GetIntentSelectionDeeplink(context.Context, *GetIntentSelectionDeeplinkRequest) (*GetIntentSelectionDeeplinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIntentSelectionDeeplink not implemented")
}
func (UnimplementedOnboardingServer) UploadPassport(context.Context, *UploadPassportRequest) (*UploadPassportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadPassport not implemented")
}
func (UnimplementedOnboardingServer) GetFeatureLifecycle(context.Context, *GetFeatureLifecycleRequest) (*GetFeatureLifecycleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFeatureLifecycle not implemented")
}
func (UnimplementedOnboardingServer) FetchPassport(context.Context, *FetchPassportRequest) (*FetchPassportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchPassport not implemented")
}
func (UnimplementedOnboardingServer) ProcessUserAck(context.Context, *ProcessUserAckRequest) (*ProcessUserAckResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessUserAck not implemented")
}
func (UnimplementedOnboardingServer) AddQueueElement(context.Context, *AddQueueElementRequest) (*AddQueueElementResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddQueueElement not implemented")
}
func (UnimplementedOnboardingServer) VerifyGlobalIssuedPassport(context.Context, *VerifyGlobalIssuedPassportRequest) (*VerifyGlobalIssuedPassportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyGlobalIssuedPassport not implemented")
}
func (UnimplementedOnboardingServer) ProcessCrossValidationManualReview(context.Context, *ProcessCrossValidationManualReviewRequest) (*ProcessCrossValidationManualReviewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCrossValidationManualReview not implemented")
}
func (UnimplementedOnboardingServer) GetDataForCrossValidationManualReview(context.Context, *GetDataForCrossValidationManualReviewRequest) (*GetDataForCrossValidationManualReviewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDataForCrossValidationManualReview not implemented")
}
func (UnimplementedOnboardingServer) ProcessPassportManualReview(context.Context, *ProcessPassportManualReviewRequest) (*ProcessPassportManualReviewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessPassportManualReview not implemented")
}

// UnsafeOnboardingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OnboardingServer will
// result in compilation errors.
type UnsafeOnboardingServer interface {
	mustEmbedUnimplementedOnboardingServer()
}

func RegisterOnboardingServer(s grpc.ServiceRegistrar, srv OnboardingServer) {
	s.RegisterService(&Onboarding_ServiceDesc, srv)
}

func _Onboarding_CheckAccountSetupStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAccountSetupStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).CheckAccountSetupStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_CheckAccountSetupStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).CheckAccountSetupStatus(ctx, req.(*CheckAccountSetupStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetPinCodeDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPinCodeDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetPinCodeDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetPinCodeDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetPinCodeDetails(ctx, req.(*GetPinCodeDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_AddPinCodeEntry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddPinCodeEntryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).AddPinCodeEntry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_AddPinCodeEntry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).AddPinCodeEntry(ctx, req.(*AddPinCodeEntryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetNextAction_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNextActionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetNextAction(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetNextAction_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetNextAction(ctx, req.(*GetNextActionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetDetails(ctx, req.(*GetDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetTroubleshootingDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTroubleshootingDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetTroubleshootingDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetTroubleshootingDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetTroubleshootingDetails(ctx, req.(*GetTroubleshootingDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ResetUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ResetUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ResetUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ResetUser(ctx, req.(*ResetUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ResetStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetStageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ResetStage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ResetStage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ResetStage(ctx, req.(*ResetStageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_RegisterUserForVKYC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterUserForVKYCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).RegisterUserForVKYC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_RegisterUserForVKYC_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).RegisterUserForVKYC(ctx, req.(*RegisterUserForVKYCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetOnboardedUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnboardedUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetOnboardedUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetOnboardedUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetOnboardedUser(ctx, req.(*GetOnboardedUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_UpdateStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateStageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).UpdateStage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_UpdateStage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).UpdateStage(ctx, req.(*UpdateStageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetUnonboardedUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUnonboardedUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetUnonboardedUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetUnonboardedUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetUnonboardedUsers(ctx, req.(*GetUnonboardedUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_DebitCardNameCheck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DebitCardNameCheckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).DebitCardNameCheck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_DebitCardNameCheck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).DebitCardNameCheck(ctx, req.(*DebitCardNameCheckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ResetDebitCardNameRetries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetDebitCardNameRetriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ResetDebitCardNameRetries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ResetDebitCardNameRetries_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ResetDebitCardNameRetries(ctx, req.(*ResetDebitCardNameRetriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetOnboardedUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnboardedUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetOnboardedUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetOnboardedUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetOnboardedUsers(ctx, req.(*GetOnboardedUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_StartOnboarding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartOnboardingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).StartOnboarding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_StartOnboarding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).StartOnboarding(ctx, req.(*StartOnboardingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetQueueElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQueueElementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetQueueElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetQueueElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetQueueElements(ctx, req.(*GetQueueElementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_DeleteQueueElement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteQueueElementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).DeleteQueueElement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_DeleteQueueElement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).DeleteQueueElement(ctx, req.(*DeleteQueueElementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_CountQueueElements_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountQueueElementsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).CountQueueElements(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_CountQueueElements_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).CountQueueElements(ctx, req.(*CountQueueElementsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_UpdatePanNameVerdict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePanNameVerdictRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).UpdatePanNameVerdict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_UpdatePanNameVerdict_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).UpdatePanNameVerdict(ctx, req.(*UpdatePanNameVerdictRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetOnboardingDetailsMin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnboardingDetailsMinRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetOnboardingDetailsMin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetOnboardingDetailsMin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetOnboardingDetailsMin(ctx, req.(*GetOnboardingDetailsMinRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ProcessRiskVerdict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessRiskVerdictRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ProcessRiskVerdict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ProcessRiskVerdict_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ProcessRiskVerdict(ctx, req.(*ProcessRiskVerdictRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_UpdateFiLiteAccessibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateFiLiteAccessibilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).UpdateFiLiteAccessibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_UpdateFiLiteAccessibility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).UpdateFiLiteAccessibility(ctx, req.(*UpdateFiLiteAccessibilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetFeatureDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFeatureDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetFeatureDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetFeatureDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetFeatureDetails(ctx, req.(*GetFeatureDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_SetOnboardingIntent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetOnboardingIntentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).SetOnboardingIntent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_SetOnboardingIntent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).SetOnboardingIntent(ctx, req.(*SetOnboardingIntentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetOnboardingSoftIntentOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnboardingSoftIntentOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetOnboardingSoftIntentOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetOnboardingSoftIntentOptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetOnboardingSoftIntentOptions(ctx, req.(*GetOnboardingSoftIntentOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_SetOnboardingSoftIntent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetOnboardingSoftIntentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).SetOnboardingSoftIntent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_SetOnboardingSoftIntent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).SetOnboardingSoftIntent(ctx, req.(*SetOnboardingSoftIntentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_PerformStage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PerformStageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).PerformStage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_PerformStage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).PerformStage(ctx, req.(*PerformStageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetIntentSelectionDeeplink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIntentSelectionDeeplinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetIntentSelectionDeeplink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetIntentSelectionDeeplink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetIntentSelectionDeeplink(ctx, req.(*GetIntentSelectionDeeplinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_UploadPassport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UploadPassportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).UploadPassport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_UploadPassport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).UploadPassport(ctx, req.(*UploadPassportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetFeatureLifecycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFeatureLifecycleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetFeatureLifecycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetFeatureLifecycle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetFeatureLifecycle(ctx, req.(*GetFeatureLifecycleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_FetchPassport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchPassportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).FetchPassport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_FetchPassport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).FetchPassport(ctx, req.(*FetchPassportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ProcessUserAck_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessUserAckRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ProcessUserAck(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ProcessUserAck_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ProcessUserAck(ctx, req.(*ProcessUserAckRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_AddQueueElement_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddQueueElementRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).AddQueueElement(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_AddQueueElement_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).AddQueueElement(ctx, req.(*AddQueueElementRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_VerifyGlobalIssuedPassport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyGlobalIssuedPassportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).VerifyGlobalIssuedPassport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_VerifyGlobalIssuedPassport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).VerifyGlobalIssuedPassport(ctx, req.(*VerifyGlobalIssuedPassportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ProcessCrossValidationManualReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessCrossValidationManualReviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ProcessCrossValidationManualReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ProcessCrossValidationManualReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ProcessCrossValidationManualReview(ctx, req.(*ProcessCrossValidationManualReviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_GetDataForCrossValidationManualReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataForCrossValidationManualReviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).GetDataForCrossValidationManualReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_GetDataForCrossValidationManualReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).GetDataForCrossValidationManualReview(ctx, req.(*GetDataForCrossValidationManualReviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Onboarding_ProcessPassportManualReview_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessPassportManualReviewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OnboardingServer).ProcessPassportManualReview(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Onboarding_ProcessPassportManualReview_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OnboardingServer).ProcessPassportManualReview(ctx, req.(*ProcessPassportManualReviewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Onboarding_ServiceDesc is the grpc.ServiceDesc for Onboarding service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Onboarding_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "user.onboarding.Onboarding",
	HandlerType: (*OnboardingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckAccountSetupStatus",
			Handler:    _Onboarding_CheckAccountSetupStatus_Handler,
		},
		{
			MethodName: "GetPinCodeDetails",
			Handler:    _Onboarding_GetPinCodeDetails_Handler,
		},
		{
			MethodName: "AddPinCodeEntry",
			Handler:    _Onboarding_AddPinCodeEntry_Handler,
		},
		{
			MethodName: "GetNextAction",
			Handler:    _Onboarding_GetNextAction_Handler,
		},
		{
			MethodName: "GetDetails",
			Handler:    _Onboarding_GetDetails_Handler,
		},
		{
			MethodName: "GetTroubleshootingDetails",
			Handler:    _Onboarding_GetTroubleshootingDetails_Handler,
		},
		{
			MethodName: "ResetUser",
			Handler:    _Onboarding_ResetUser_Handler,
		},
		{
			MethodName: "ResetStage",
			Handler:    _Onboarding_ResetStage_Handler,
		},
		{
			MethodName: "RegisterUserForVKYC",
			Handler:    _Onboarding_RegisterUserForVKYC_Handler,
		},
		{
			MethodName: "GetOnboardedUser",
			Handler:    _Onboarding_GetOnboardedUser_Handler,
		},
		{
			MethodName: "UpdateStage",
			Handler:    _Onboarding_UpdateStage_Handler,
		},
		{
			MethodName: "GetUnonboardedUsers",
			Handler:    _Onboarding_GetUnonboardedUsers_Handler,
		},
		{
			MethodName: "DebitCardNameCheck",
			Handler:    _Onboarding_DebitCardNameCheck_Handler,
		},
		{
			MethodName: "ResetDebitCardNameRetries",
			Handler:    _Onboarding_ResetDebitCardNameRetries_Handler,
		},
		{
			MethodName: "GetOnboardedUsers",
			Handler:    _Onboarding_GetOnboardedUsers_Handler,
		},
		{
			MethodName: "StartOnboarding",
			Handler:    _Onboarding_StartOnboarding_Handler,
		},
		{
			MethodName: "GetQueueElements",
			Handler:    _Onboarding_GetQueueElements_Handler,
		},
		{
			MethodName: "DeleteQueueElement",
			Handler:    _Onboarding_DeleteQueueElement_Handler,
		},
		{
			MethodName: "CountQueueElements",
			Handler:    _Onboarding_CountQueueElements_Handler,
		},
		{
			MethodName: "UpdatePanNameVerdict",
			Handler:    _Onboarding_UpdatePanNameVerdict_Handler,
		},
		{
			MethodName: "GetOnboardingDetailsMin",
			Handler:    _Onboarding_GetOnboardingDetailsMin_Handler,
		},
		{
			MethodName: "ProcessRiskVerdict",
			Handler:    _Onboarding_ProcessRiskVerdict_Handler,
		},
		{
			MethodName: "UpdateFiLiteAccessibility",
			Handler:    _Onboarding_UpdateFiLiteAccessibility_Handler,
		},
		{
			MethodName: "GetFeatureDetails",
			Handler:    _Onboarding_GetFeatureDetails_Handler,
		},
		{
			MethodName: "SetOnboardingIntent",
			Handler:    _Onboarding_SetOnboardingIntent_Handler,
		},
		{
			MethodName: "GetOnboardingSoftIntentOptions",
			Handler:    _Onboarding_GetOnboardingSoftIntentOptions_Handler,
		},
		{
			MethodName: "SetOnboardingSoftIntent",
			Handler:    _Onboarding_SetOnboardingSoftIntent_Handler,
		},
		{
			MethodName: "PerformStage",
			Handler:    _Onboarding_PerformStage_Handler,
		},
		{
			MethodName: "GetIntentSelectionDeeplink",
			Handler:    _Onboarding_GetIntentSelectionDeeplink_Handler,
		},
		{
			MethodName: "UploadPassport",
			Handler:    _Onboarding_UploadPassport_Handler,
		},
		{
			MethodName: "GetFeatureLifecycle",
			Handler:    _Onboarding_GetFeatureLifecycle_Handler,
		},
		{
			MethodName: "FetchPassport",
			Handler:    _Onboarding_FetchPassport_Handler,
		},
		{
			MethodName: "ProcessUserAck",
			Handler:    _Onboarding_ProcessUserAck_Handler,
		},
		{
			MethodName: "AddQueueElement",
			Handler:    _Onboarding_AddQueueElement_Handler,
		},
		{
			MethodName: "VerifyGlobalIssuedPassport",
			Handler:    _Onboarding_VerifyGlobalIssuedPassport_Handler,
		},
		{
			MethodName: "ProcessCrossValidationManualReview",
			Handler:    _Onboarding_ProcessCrossValidationManualReview_Handler,
		},
		{
			MethodName: "GetDataForCrossValidationManualReview",
			Handler:    _Onboarding_GetDataForCrossValidationManualReview_Handler,
		},
		{
			MethodName: "ProcessPassportManualReview",
			Handler:    _Onboarding_ProcessPassportManualReview_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/user/onboarding/service.proto",
}
