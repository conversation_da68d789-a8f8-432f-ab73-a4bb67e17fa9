// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/user/onboarding/pin_code_details.proto

package onboarding

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetPinCodeDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// PIN code for which city/cities and state name is desired
	// Indian PIN codes are 6 digits long
	PinCode   string         `protobuf:"bytes,1,opt,name=pinCode,proto3" json:"pinCode,omitempty"`
	FieldMask []PinCodeField `protobuf:"varint,2,rep,packed,name=field_mask,json=fieldMask,proto3,enum=user.onboarding.PinCodeField" json:"field_mask,omitempty"`
}

func (x *GetPinCodeDetailsRequest) Reset() {
	*x = GetPinCodeDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_pin_code_details_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinCodeDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinCodeDetailsRequest) ProtoMessage() {}

func (x *GetPinCodeDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_pin_code_details_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinCodeDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetPinCodeDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_pin_code_details_proto_rawDescGZIP(), []int{0}
}

func (x *GetPinCodeDetailsRequest) GetPinCode() string {
	if x != nil {
		return x.PinCode
	}
	return ""
}

func (x *GetPinCodeDetailsRequest) GetFieldMask() []PinCodeField {
	if x != nil {
		return x.FieldMask
	}
	return nil
}

// Geographical area details for a PIN code
type GetPinCodeDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Details []*PinCodeDetails `protobuf:"bytes,2,rep,name=details,proto3" json:"details,omitempty"`
}

func (x *GetPinCodeDetailsResponse) Reset() {
	*x = GetPinCodeDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_pin_code_details_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPinCodeDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPinCodeDetailsResponse) ProtoMessage() {}

func (x *GetPinCodeDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_pin_code_details_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPinCodeDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetPinCodeDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_pin_code_details_proto_rawDescGZIP(), []int{1}
}

func (x *GetPinCodeDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPinCodeDetailsResponse) GetDetails() []*PinCodeDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

// Geographical area details for a PIN code
type AddPinCodeEntryRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PinCodeDetails *PinCodeDetails `protobuf:"bytes,1,opt,name=pin_code_details,json=pinCodeDetails,proto3" json:"pin_code_details,omitempty"`
}

func (x *AddPinCodeEntryRequest) Reset() {
	*x = AddPinCodeEntryRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_pin_code_details_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPinCodeEntryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPinCodeEntryRequest) ProtoMessage() {}

func (x *AddPinCodeEntryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_pin_code_details_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPinCodeEntryRequest.ProtoReflect.Descriptor instead.
func (*AddPinCodeEntryRequest) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_pin_code_details_proto_rawDescGZIP(), []int{2}
}

func (x *AddPinCodeEntryRequest) GetPinCodeDetails() *PinCodeDetails {
	if x != nil {
		return x.PinCodeDetails
	}
	return nil
}

type AddPinCodeEntryResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *AddPinCodeEntryResponse) Reset() {
	*x = AddPinCodeEntryResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_pin_code_details_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddPinCodeEntryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPinCodeEntryResponse) ProtoMessage() {}

func (x *AddPinCodeEntryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_pin_code_details_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPinCodeEntryResponse.ProtoReflect.Descriptor instead.
func (*AddPinCodeEntryResponse) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_pin_code_details_proto_rawDescGZIP(), []int{3}
}

func (x *AddPinCodeEntryResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type PinCodeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PinCode   string `protobuf:"bytes,1,opt,name=pinCode,proto3" json:"pinCode,omitempty"`
	Division  string `protobuf:"bytes,2,opt,name=division,proto3" json:"division,omitempty"`
	Region    string `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`
	Circle    string `protobuf:"bytes,4,opt,name=circle,proto3" json:"circle,omitempty"`
	Taluk     string `protobuf:"bytes,5,opt,name=taluk,proto3" json:"taluk,omitempty"`
	District  string `protobuf:"bytes,6,opt,name=district,proto3" json:"district,omitempty"`
	State     string `protobuf:"bytes,7,opt,name=state,proto3" json:"state,omitempty"`
	Longitude string `protobuf:"bytes,8,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Latitude  string `protobuf:"bytes,9,opt,name=latitude,proto3" json:"latitude,omitempty"`
	// PostOffice denotes the name of the post office for the given pin code detail entry
	PostOffice string `protobuf:"bytes,10,opt,name=post_office,json=postOffice,proto3" json:"post_office,omitempty"`
}

func (x *PinCodeDetails) Reset() {
	*x = PinCodeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_user_onboarding_pin_code_details_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PinCodeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PinCodeDetails) ProtoMessage() {}

func (x *PinCodeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_user_onboarding_pin_code_details_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PinCodeDetails.ProtoReflect.Descriptor instead.
func (*PinCodeDetails) Descriptor() ([]byte, []int) {
	return file_api_user_onboarding_pin_code_details_proto_rawDescGZIP(), []int{4}
}

func (x *PinCodeDetails) GetPinCode() string {
	if x != nil {
		return x.PinCode
	}
	return ""
}

func (x *PinCodeDetails) GetDivision() string {
	if x != nil {
		return x.Division
	}
	return ""
}

func (x *PinCodeDetails) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *PinCodeDetails) GetCircle() string {
	if x != nil {
		return x.Circle
	}
	return ""
}

func (x *PinCodeDetails) GetTaluk() string {
	if x != nil {
		return x.Taluk
	}
	return ""
}

func (x *PinCodeDetails) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

func (x *PinCodeDetails) GetState() string {
	if x != nil {
		return x.State
	}
	return ""
}

func (x *PinCodeDetails) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *PinCodeDetails) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *PinCodeDetails) GetPostOffice() string {
	if x != nil {
		return x.PostOffice
	}
	return ""
}

var File_api_user_onboarding_pin_code_details_proto protoreflect.FileDescriptor

var file_api_user_onboarding_pin_code_details_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x14, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6f, 0x6e,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7c, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x22, 0x0a, 0x07, 0x70, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x98, 0x01, 0x06, 0x52, 0x07, 0x70,
	0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x3c, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x6d, 0x61, 0x73, 0x6b, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x69, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x22, 0x7b, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x69, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0x63, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x10, 0x70,
	0x69, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x50, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x70, 0x69, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x3e, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x50, 0x69, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x99, 0x02, 0x0a, 0x0e, 0x50, 0x69, 0x6e, 0x43, 0x6f,
	0x64, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x69, 0x6e,
	0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x69, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x69, 0x72, 0x63, 0x6c,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x69, 0x72, 0x63, 0x6c, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x61, 0x6c, 0x75, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x74, 0x61, 0x6c, 0x75, 0x6b, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63,
	0x74, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69,
	0x74, 0x75, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x4f, 0x66, 0x66, 0x69,
	0x63, 0x65, 0x42, 0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65,
	0x72, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_user_onboarding_pin_code_details_proto_rawDescOnce sync.Once
	file_api_user_onboarding_pin_code_details_proto_rawDescData = file_api_user_onboarding_pin_code_details_proto_rawDesc
)

func file_api_user_onboarding_pin_code_details_proto_rawDescGZIP() []byte {
	file_api_user_onboarding_pin_code_details_proto_rawDescOnce.Do(func() {
		file_api_user_onboarding_pin_code_details_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_user_onboarding_pin_code_details_proto_rawDescData)
	})
	return file_api_user_onboarding_pin_code_details_proto_rawDescData
}

var file_api_user_onboarding_pin_code_details_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_api_user_onboarding_pin_code_details_proto_goTypes = []interface{}{
	(*GetPinCodeDetailsRequest)(nil),  // 0: user.onboarding.GetPinCodeDetailsRequest
	(*GetPinCodeDetailsResponse)(nil), // 1: user.onboarding.GetPinCodeDetailsResponse
	(*AddPinCodeEntryRequest)(nil),    // 2: user.onboarding.AddPinCodeEntryRequest
	(*AddPinCodeEntryResponse)(nil),   // 3: user.onboarding.AddPinCodeEntryResponse
	(*PinCodeDetails)(nil),            // 4: user.onboarding.PinCodeDetails
	(PinCodeField)(0),                 // 5: user.onboarding.PinCodeField
	(*rpc.Status)(nil),                // 6: rpc.Status
}
var file_api_user_onboarding_pin_code_details_proto_depIdxs = []int32{
	5, // 0: user.onboarding.GetPinCodeDetailsRequest.field_mask:type_name -> user.onboarding.PinCodeField
	6, // 1: user.onboarding.GetPinCodeDetailsResponse.status:type_name -> rpc.Status
	4, // 2: user.onboarding.GetPinCodeDetailsResponse.details:type_name -> user.onboarding.PinCodeDetails
	4, // 3: user.onboarding.AddPinCodeEntryRequest.pin_code_details:type_name -> user.onboarding.PinCodeDetails
	6, // 4: user.onboarding.AddPinCodeEntryResponse.status:type_name -> rpc.Status
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_user_onboarding_pin_code_details_proto_init() }
func file_api_user_onboarding_pin_code_details_proto_init() {
	if File_api_user_onboarding_pin_code_details_proto != nil {
		return
	}
	file_api_user_onboarding_internal_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_user_onboarding_pin_code_details_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinCodeDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_pin_code_details_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPinCodeDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_pin_code_details_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPinCodeEntryRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_pin_code_details_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddPinCodeEntryResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_user_onboarding_pin_code_details_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PinCodeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_user_onboarding_pin_code_details_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_user_onboarding_pin_code_details_proto_goTypes,
		DependencyIndexes: file_api_user_onboarding_pin_code_details_proto_depIdxs,
		MessageInfos:      file_api_user_onboarding_pin_code_details_proto_msgTypes,
	}.Build()
	File_api_user_onboarding_pin_code_details_proto = out.File
	file_api_user_onboarding_pin_code_details_proto_rawDesc = nil
	file_api_user_onboarding_pin_code_details_proto_goTypes = nil
	file_api_user_onboarding_pin_code_details_proto_depIdxs = nil
}
