// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/user/onboarding/watson/service.proto

package watson

import (
	watson "github.com/epifi/gamma/api/cx/watson"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_user_onboarding_watson_service_proto protoreflect.FileDescriptor

var file_api_user_onboarding_watson_service_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x6f, 0x6e, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x1a, 0x21, 0x61,
	0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2f, 0x77, 0x61, 0x74,
	0x73, 0x6f, 0x6e, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0xbf, 0x01, 0x0a, 0x06, 0x57, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x12, 0x58, 0x0a, 0x0f, 0x49,
	0x73, 0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x12, 0x21,
	0x2e, 0x63, 0x78, 0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2e, 0x49, 0x73, 0x49, 0x6e, 0x63,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2e, 0x49, 0x73,
	0x49, 0x6e, 0x63, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x22, 0x2e, 0x63, 0x78, 0x2e, 0x77,
	0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e,
	0x63, 0x78, 0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x66, 0x0a, 0x31, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x5a, 0x31, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x2f, 0x77, 0x61, 0x74, 0x73, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var file_api_user_onboarding_watson_service_proto_goTypes = []interface{}{
	(*watson.IsIncidentValidRequest)(nil),   // 0: cx.watson.IsIncidentValidRequest
	(*watson.GetTicketDetailsRequest)(nil),  // 1: cx.watson.GetTicketDetailsRequest
	(*watson.IsIncidentValidResponse)(nil),  // 2: cx.watson.IsIncidentValidResponse
	(*watson.GetTicketDetailsResponse)(nil), // 3: cx.watson.GetTicketDetailsResponse
}
var file_api_user_onboarding_watson_service_proto_depIdxs = []int32{
	0, // 0: onboarding.watson.Watson.IsIncidentValid:input_type -> cx.watson.IsIncidentValidRequest
	1, // 1: onboarding.watson.Watson.GetTicketDetails:input_type -> cx.watson.GetTicketDetailsRequest
	2, // 2: onboarding.watson.Watson.IsIncidentValid:output_type -> cx.watson.IsIncidentValidResponse
	3, // 3: onboarding.watson.Watson.GetTicketDetails:output_type -> cx.watson.GetTicketDetailsResponse
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_user_onboarding_watson_service_proto_init() }
func file_api_user_onboarding_watson_service_proto_init() {
	if File_api_user_onboarding_watson_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_user_onboarding_watson_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_user_onboarding_watson_service_proto_goTypes,
		DependencyIndexes: file_api_user_onboarding_watson_service_proto_depIdxs,
	}.Build()
	File_api_user_onboarding_watson_service_proto = out.File
	file_api_user_onboarding_watson_service_proto_rawDesc = nil
	file_api_user_onboarding_watson_service_proto_goTypes = nil
	file_api_user_onboarding_watson_service_proto_depIdxs = nil
}
