// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package derivedattributes;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/user/credit_report/derivedattributes";
option java_package = "com.github.epifi.gamma.api.user.credit_report.derivedattributes";

message CreditReportDerivedAttributes {
  // Primary identifier to credit_report table.
  string id = 1;

  // Actor id mapped to credit_report
  string actor_id = 2;

  // Credit report id
  string credit_report_id = 3;

  // derived attribute key
  string attribute_key = 4;

  // derived attribute value
  string attribute_value = 5;

  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp deleted_at = 8;
}
