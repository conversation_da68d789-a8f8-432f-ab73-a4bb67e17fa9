// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/user/credit_report/internal/credit_report_download.proto

package credit_report

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on CreditReportDownload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreditReportDownload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreditReportDownload with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreditReportDownloadMultiError, or nil if none found.
func (m *CreditReportDownload) ValidateAll() error {
	return m.validate(true)
}

func (m *CreditReportDownload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for RequestId

	// no validation rules for ActorId

	// no validation rules for Vendor

	// no validation rules for FetchType

	if all {
		switch v := interface{}(m.GetOtpInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "OtpInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "OtpInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtpInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadValidationError{
				field:  "OtpInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetConsentInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "ConsentInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "ConsentInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadValidationError{
				field:  "ConsentInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProcessStatus

	// no validation rules for ProcessSubStatus

	if all {
		switch v := interface{}(m.GetRedirectDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "RedirectDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "RedirectDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadValidationError{
				field:  "RedirectDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDownloadedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "DownloadedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "DownloadedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDownloadedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadValidationError{
				field:  "DownloadedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Provenance

	// no validation rules for CreditReportId

	// no validation rules for OrchId

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreditReportDownloadValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreditReportDownloadValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VendorError

	if len(errors) > 0 {
		return CreditReportDownloadMultiError(errors)
	}

	return nil
}

// CreditReportDownloadMultiError is an error wrapping multiple validation
// errors returned by CreditReportDownload.ValidateAll() if the designated
// constraints aren't met.
type CreditReportDownloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreditReportDownloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreditReportDownloadMultiError) AllErrors() []error { return m }

// CreditReportDownloadValidationError is the validation error returned by
// CreditReportDownload.Validate if the designated constraints aren't met.
type CreditReportDownloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreditReportDownloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreditReportDownloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreditReportDownloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreditReportDownloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreditReportDownloadValidationError) ErrorName() string {
	return "CreditReportDownloadValidationError"
}

// Error satisfies the builtin error interface
func (e CreditReportDownloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreditReportDownload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreditReportDownloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreditReportDownloadValidationError{}

// Validate checks the field values on OtpInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OtpInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OtpInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in OtpInfoMultiError, or nil if none found.
func (m *OtpInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *OtpInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	// no validation rules for OtpAlreadyVerified

	if len(errors) > 0 {
		return OtpInfoMultiError(errors)
	}

	return nil
}

// OtpInfoMultiError is an error wrapping multiple validation errors returned
// by OtpInfo.ValidateAll() if the designated constraints aren't met.
type OtpInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OtpInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OtpInfoMultiError) AllErrors() []error { return m }

// OtpInfoValidationError is the validation error returned by OtpInfo.Validate
// if the designated constraints aren't met.
type OtpInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OtpInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OtpInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OtpInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OtpInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OtpInfoValidationError) ErrorName() string { return "OtpInfoValidationError" }

// Error satisfies the builtin error interface
func (e OtpInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOtpInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OtpInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OtpInfoValidationError{}

// Validate checks the field values on ConsentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ConsentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ConsentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ConsentInfoMultiError, or
// nil if none found.
func (m *ConsentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ConsentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ConsentStatus

	// no validation rules for ConsentId

	// no validation rules for ConsentReqId

	if len(errors) > 0 {
		return ConsentInfoMultiError(errors)
	}

	return nil
}

// ConsentInfoMultiError is an error wrapping multiple validation errors
// returned by ConsentInfo.ValidateAll() if the designated constraints aren't met.
type ConsentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ConsentInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ConsentInfoMultiError) AllErrors() []error { return m }

// ConsentInfoValidationError is the validation error returned by
// ConsentInfo.Validate if the designated constraints aren't met.
type ConsentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ConsentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ConsentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ConsentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ConsentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ConsentInfoValidationError) ErrorName() string { return "ConsentInfoValidationError" }

// Error satisfies the builtin error interface
func (e ConsentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sConsentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ConsentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ConsentInfoValidationError{}
