//go:generate gen_sql -types=SalaryEstimationAttempt,ClientParams,AttemptInfo

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/salaryestimation/salary_estimation_attempt.proto

package salaryestimation

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AttemptInfo contains metadata about the salary estimation attempt
type AttemptInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ComputedAt *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=computed_at,json=computedAt,proto3" json:"computed_at,omitempty"`
}

func (x *AttemptInfo) Reset() {
	*x = AttemptInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AttemptInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AttemptInfo) ProtoMessage() {}

func (x *AttemptInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AttemptInfo.ProtoReflect.Descriptor instead.
func (*AttemptInfo) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_salary_estimation_attempt_proto_rawDescGZIP(), []int{0}
}

func (x *AttemptInfo) GetComputedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ComputedAt
	}
	return nil
}

type ClientParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Client Client `protobuf:"varint,1,opt,name=client,proto3,enum=api.salaryestimation.Client" json:"client,omitempty"`
	// user's employment type details as sent by client
	EmploymentType typesv2.EmploymentType `protobuf:"varint,2,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.EmploymentType" json:"employment_type,omitempty"` // optional field
	// Name of user's organisation / employer
	OrganisationName string `protobuf:"bytes,3,opt,name=organisation_name,json=organisationName,proto3" json:"organisation_name,omitempty"` // optional field
}

func (x *ClientParams) Reset() {
	*x = ClientParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientParams) ProtoMessage() {}

func (x *ClientParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientParams.ProtoReflect.Descriptor instead.
func (*ClientParams) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_salary_estimation_attempt_proto_rawDescGZIP(), []int{1}
}

func (x *ClientParams) GetClient() Client {
	if x != nil {
		return x.Client
	}
	return Client_CLIENT_UNSPECIFIED
}

func (x *ClientParams) GetEmploymentType() typesv2.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return typesv2.EmploymentType(0)
}

func (x *ClientParams) GetOrganisationName() string {
	if x != nil {
		return x.OrganisationName
	}
	return ""
}

// SalaryEstimationAttempt represents a salary estimation attempt record
type SalaryEstimationAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the salary estimation attempt
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// Client request ID - unique identifier provided by the client
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// Source of the salary estimation attempt
	Source Source `protobuf:"varint,3,opt,name=source,proto3,enum=api.salaryestimation.Source" json:"source,omitempty"`
	// Actor ID associated with the attempt
	ActorId string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Client parameters for the attempt
	ClientParams *ClientParams `protobuf:"bytes,5,opt,name=client_params,json=clientParams,proto3" json:"client_params,omitempty"`
	// Current step of the attempt
	Step AttemptStep `protobuf:"varint,6,opt,name=step,proto3,enum=api.salaryestimation.AttemptStep" json:"step,omitempty"`
	// Current status of the attempt
	Status AttemptStatus `protobuf:"varint,7,opt,name=status,proto3,enum=api.salaryestimation.AttemptStatus" json:"status,omitempty"`
	// Current sub status of the attempt
	SubStatus AttemptSubStatus `protobuf:"varint,8,opt,name=sub_status,json=subStatus,proto3,enum=api.salaryestimation.AttemptSubStatus" json:"sub_status,omitempty"`
	// Attempt metadata and information
	AttemptInfo *AttemptInfo `protobuf:"bytes,9,opt,name=attempt_info,json=attemptInfo,proto3" json:"attempt_info,omitempty"`
	// Timestamp when the record was created
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// Timestamp when the record was last updated
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// Timestamp when the record is set to expire
	ExpiryAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=expiry_at,json=expiryAt,proto3" json:"expiry_at,omitempty"`
	// Timestamp when the record was deleted (soft delete)
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *SalaryEstimationAttempt) Reset() {
	*x = SalaryEstimationAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SalaryEstimationAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SalaryEstimationAttempt) ProtoMessage() {}

func (x *SalaryEstimationAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SalaryEstimationAttempt.ProtoReflect.Descriptor instead.
func (*SalaryEstimationAttempt) Descriptor() ([]byte, []int) {
	return file_api_salaryestimation_salary_estimation_attempt_proto_rawDescGZIP(), []int{2}
}

func (x *SalaryEstimationAttempt) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SalaryEstimationAttempt) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *SalaryEstimationAttempt) GetSource() Source {
	if x != nil {
		return x.Source
	}
	return Source_SOURCE_UNSPECIFIED
}

func (x *SalaryEstimationAttempt) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *SalaryEstimationAttempt) GetClientParams() *ClientParams {
	if x != nil {
		return x.ClientParams
	}
	return nil
}

func (x *SalaryEstimationAttempt) GetStep() AttemptStep {
	if x != nil {
		return x.Step
	}
	return AttemptStep_ATTEMPT_STEP_UNSPECIFIED
}

func (x *SalaryEstimationAttempt) GetStatus() AttemptStatus {
	if x != nil {
		return x.Status
	}
	return AttemptStatus_ATTEMPT_STATUS_UNSPECIFIED
}

func (x *SalaryEstimationAttempt) GetSubStatus() AttemptSubStatus {
	if x != nil {
		return x.SubStatus
	}
	return AttemptSubStatus_ATTEMPT_SUB_STATUS_UNSPECIFIED
}

func (x *SalaryEstimationAttempt) GetAttemptInfo() *AttemptInfo {
	if x != nil {
		return x.AttemptInfo
	}
	return nil
}

func (x *SalaryEstimationAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *SalaryEstimationAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *SalaryEstimationAttempt) GetExpiryAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryAt
	}
	return nil
}

func (x *SalaryEstimationAttempt) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

var File_api_salaryestimation_salary_estimation_attempt_proto protoreflect.FileDescriptor

var file_api_salaryestimation_salary_estimation_attempt_proto_rawDesc = []byte{
	0x0a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x5f, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x20, 0x61, 0x70,
	0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x4a, 0x0a, 0x0b, 0x41,
	0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x6f, 0x6d,
	0x70, 0x75, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xb7, 0x01, 0x0a, 0x0c, 0x43, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x34, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x44,
	0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xe4, 0x05, 0x0a, 0x17, 0x53, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x45, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x2b, 0x0a,
	0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52,
	0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x35, 0x0a,
	0x04, 0x73, 0x74, 0x65, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74, 0x65, 0x70, 0x52, 0x04,
	0x73, 0x74, 0x65, 0x70, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72,
	0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x45, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x73,
	0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x44, 0x0a, 0x0c, 0x61, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x41, 0x74, 0x12, 0x37, 0x0a, 0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x5f, 0x61,
	0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74,
	0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_salaryestimation_salary_estimation_attempt_proto_rawDescOnce sync.Once
	file_api_salaryestimation_salary_estimation_attempt_proto_rawDescData = file_api_salaryestimation_salary_estimation_attempt_proto_rawDesc
)

func file_api_salaryestimation_salary_estimation_attempt_proto_rawDescGZIP() []byte {
	file_api_salaryestimation_salary_estimation_attempt_proto_rawDescOnce.Do(func() {
		file_api_salaryestimation_salary_estimation_attempt_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_salaryestimation_salary_estimation_attempt_proto_rawDescData)
	})
	return file_api_salaryestimation_salary_estimation_attempt_proto_rawDescData
}

var file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_api_salaryestimation_salary_estimation_attempt_proto_goTypes = []interface{}{
	(*AttemptInfo)(nil),             // 0: api.salaryestimation.AttemptInfo
	(*ClientParams)(nil),            // 1: api.salaryestimation.ClientParams
	(*SalaryEstimationAttempt)(nil), // 2: api.salaryestimation.SalaryEstimationAttempt
	(*timestamppb.Timestamp)(nil),   // 3: google.protobuf.Timestamp
	(Client)(0),                     // 4: api.salaryestimation.Client
	(typesv2.EmploymentType)(0),     // 5: api.typesv2.EmploymentType
	(Source)(0),                     // 6: api.salaryestimation.Source
	(AttemptStep)(0),                // 7: api.salaryestimation.AttemptStep
	(AttemptStatus)(0),              // 8: api.salaryestimation.AttemptStatus
	(AttemptSubStatus)(0),           // 9: api.salaryestimation.AttemptSubStatus
}
var file_api_salaryestimation_salary_estimation_attempt_proto_depIdxs = []int32{
	3,  // 0: api.salaryestimation.AttemptInfo.computed_at:type_name -> google.protobuf.Timestamp
	4,  // 1: api.salaryestimation.ClientParams.client:type_name -> api.salaryestimation.Client
	5,  // 2: api.salaryestimation.ClientParams.employment_type:type_name -> api.typesv2.EmploymentType
	6,  // 3: api.salaryestimation.SalaryEstimationAttempt.source:type_name -> api.salaryestimation.Source
	1,  // 4: api.salaryestimation.SalaryEstimationAttempt.client_params:type_name -> api.salaryestimation.ClientParams
	7,  // 5: api.salaryestimation.SalaryEstimationAttempt.step:type_name -> api.salaryestimation.AttemptStep
	8,  // 6: api.salaryestimation.SalaryEstimationAttempt.status:type_name -> api.salaryestimation.AttemptStatus
	9,  // 7: api.salaryestimation.SalaryEstimationAttempt.sub_status:type_name -> api.salaryestimation.AttemptSubStatus
	0,  // 8: api.salaryestimation.SalaryEstimationAttempt.attempt_info:type_name -> api.salaryestimation.AttemptInfo
	3,  // 9: api.salaryestimation.SalaryEstimationAttempt.created_at:type_name -> google.protobuf.Timestamp
	3,  // 10: api.salaryestimation.SalaryEstimationAttempt.updated_at:type_name -> google.protobuf.Timestamp
	3,  // 11: api.salaryestimation.SalaryEstimationAttempt.expiry_at:type_name -> google.protobuf.Timestamp
	3,  // 12: api.salaryestimation.SalaryEstimationAttempt.deleted_at:type_name -> google.protobuf.Timestamp
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_api_salaryestimation_salary_estimation_attempt_proto_init() }
func file_api_salaryestimation_salary_estimation_attempt_proto_init() {
	if File_api_salaryestimation_salary_estimation_attempt_proto != nil {
		return
	}
	file_api_salaryestimation_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AttemptInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SalaryEstimationAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_salaryestimation_salary_estimation_attempt_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_salaryestimation_salary_estimation_attempt_proto_goTypes,
		DependencyIndexes: file_api_salaryestimation_salary_estimation_attempt_proto_depIdxs,
		MessageInfos:      file_api_salaryestimation_salary_estimation_attempt_proto_msgTypes,
	}.Build()
	File_api_salaryestimation_salary_estimation_attempt_proto = out.File
	file_api_salaryestimation_salary_estimation_attempt_proto_rawDesc = nil
	file_api_salaryestimation_salary_estimation_attempt_proto_goTypes = nil
	file_api_salaryestimation_salary_estimation_attempt_proto_depIdxs = nil
}
