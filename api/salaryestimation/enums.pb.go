//go:generate gen_sql -types=Source,Client,AttemptStep,AttemptStatus,AttemptSubStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/salaryestimation/enums.proto

package salaryestimation

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Source int32

const (
	Source_SOURCE_UNSPECIFIED Source = 0
	Source_SOURCE_AA          Source = 1
)

// Enum value maps for Source.
var (
	Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "SOURCE_AA",
	}
	Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"SOURCE_AA":          1,
	}
)

func (x Source) Enum() *Source {
	p := new(Source)
	*p = x
	return p
}

func (x Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Source) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_enums_proto_enumTypes[0].Descriptor()
}

func (Source) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_enums_proto_enumTypes[0]
}

func (x Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Source.Descriptor instead.
func (Source) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_enums_proto_rawDescGZIP(), []int{0}
}

// Client is used to determine the service to notify or callback on exit after salary estimation
type Client int32

const (
	Client_CLIENT_UNSPECIFIED Client = 0
	Client_CLIENT_LOANS       Client = 1
)

// Enum value maps for Client.
var (
	Client_name = map[int32]string{
		0: "CLIENT_UNSPECIFIED",
		1: "CLIENT_LOANS",
	}
	Client_value = map[string]int32{
		"CLIENT_UNSPECIFIED": 0,
		"CLIENT_LOANS":       1,
	}
)

func (x Client) Enum() *Client {
	p := new(Client)
	*p = x
	return p
}

func (x Client) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Client) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_enums_proto_enumTypes[1].Descriptor()
}

func (Client) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_enums_proto_enumTypes[1]
}

func (x Client) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Client.Descriptor instead.
func (Client) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_enums_proto_rawDescGZIP(), []int{1}
}

// Step enum for salary estimation attempts
type AttemptStep int32

const (
	AttemptStep_ATTEMPT_STEP_UNSPECIFIED        AttemptStep = 0
	AttemptStep_ATTEMPT_STEP_ACCOUNT_CONNECTION AttemptStep = 1
	AttemptStep_ATTEMPT_STEP_ACCOUNT_SELECTION  AttemptStep = 2
	AttemptStep_ATTEMPT_STEP_DATA_SHARING       AttemptStep = 3
	AttemptStep_ATTEMPT_STEP_ANALYSIS           AttemptStep = 4
)

// Enum value maps for AttemptStep.
var (
	AttemptStep_name = map[int32]string{
		0: "ATTEMPT_STEP_UNSPECIFIED",
		1: "ATTEMPT_STEP_ACCOUNT_CONNECTION",
		2: "ATTEMPT_STEP_ACCOUNT_SELECTION",
		3: "ATTEMPT_STEP_DATA_SHARING",
		4: "ATTEMPT_STEP_ANALYSIS",
	}
	AttemptStep_value = map[string]int32{
		"ATTEMPT_STEP_UNSPECIFIED":        0,
		"ATTEMPT_STEP_ACCOUNT_CONNECTION": 1,
		"ATTEMPT_STEP_ACCOUNT_SELECTION":  2,
		"ATTEMPT_STEP_DATA_SHARING":       3,
		"ATTEMPT_STEP_ANALYSIS":           4,
	}
)

func (x AttemptStep) Enum() *AttemptStep {
	p := new(AttemptStep)
	*p = x
	return p
}

func (x AttemptStep) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttemptStep) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_enums_proto_enumTypes[2].Descriptor()
}

func (AttemptStep) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_enums_proto_enumTypes[2]
}

func (x AttemptStep) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttemptStep.Descriptor instead.
func (AttemptStep) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_enums_proto_rawDescGZIP(), []int{2}
}

// Status enum for salary estimation attempts
type AttemptStatus int32

const (
	AttemptStatus_ATTEMPT_STATUS_UNSPECIFIED AttemptStatus = 0
	AttemptStatus_ATTEMPT_STATUS_PENDING     AttemptStatus = 1
	AttemptStatus_ATTEMPT_STATUS_SUCCESSFUL  AttemptStatus = 2
	AttemptStatus_ATTEMPT_STATUS_FAILED      AttemptStatus = 3
	AttemptStatus_ATTEMPT_STATUS_CANCELLED   AttemptStatus = 4
	AttemptStatus_ATTEMPT_STATUS_EXPIRED     AttemptStatus = 5
)

// Enum value maps for AttemptStatus.
var (
	AttemptStatus_name = map[int32]string{
		0: "ATTEMPT_STATUS_UNSPECIFIED",
		1: "ATTEMPT_STATUS_PENDING",
		2: "ATTEMPT_STATUS_SUCCESSFUL",
		3: "ATTEMPT_STATUS_FAILED",
		4: "ATTEMPT_STATUS_CANCELLED",
		5: "ATTEMPT_STATUS_EXPIRED",
	}
	AttemptStatus_value = map[string]int32{
		"ATTEMPT_STATUS_UNSPECIFIED": 0,
		"ATTEMPT_STATUS_PENDING":     1,
		"ATTEMPT_STATUS_SUCCESSFUL":  2,
		"ATTEMPT_STATUS_FAILED":      3,
		"ATTEMPT_STATUS_CANCELLED":   4,
		"ATTEMPT_STATUS_EXPIRED":     5,
	}
)

func (x AttemptStatus) Enum() *AttemptStatus {
	p := new(AttemptStatus)
	*p = x
	return p
}

func (x AttemptStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttemptStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_enums_proto_enumTypes[3].Descriptor()
}

func (AttemptStatus) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_enums_proto_enumTypes[3]
}

func (x AttemptStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttemptStatus.Descriptor instead.
func (AttemptStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_enums_proto_rawDescGZIP(), []int{3}
}

type AttemptSubStatus int32

const (
	AttemptSubStatus_ATTEMPT_SUB_STATUS_UNSPECIFIED AttemptSubStatus = 0
)

// Enum value maps for AttemptSubStatus.
var (
	AttemptSubStatus_name = map[int32]string{
		0: "ATTEMPT_SUB_STATUS_UNSPECIFIED",
	}
	AttemptSubStatus_value = map[string]int32{
		"ATTEMPT_SUB_STATUS_UNSPECIFIED": 0,
	}
)

func (x AttemptSubStatus) Enum() *AttemptSubStatus {
	p := new(AttemptSubStatus)
	*p = x
	return p
}

func (x AttemptSubStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttemptSubStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_enums_proto_enumTypes[4].Descriptor()
}

func (AttemptSubStatus) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_enums_proto_enumTypes[4]
}

func (x AttemptSubStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttemptSubStatus.Descriptor instead.
func (AttemptSubStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_enums_proto_rawDescGZIP(), []int{4}
}

// Field mask for SalaryEstimationAttempt updates
type SalaryEstimationAttemptFieldMask int32

const (
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UNSPECIFIED   SalaryEstimationAttemptFieldMask = 0
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SOURCE        SalaryEstimationAttemptFieldMask = 1
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_CLIENT_PARAMS SalaryEstimationAttemptFieldMask = 2
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP          SalaryEstimationAttemptFieldMask = 3
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS        SalaryEstimationAttemptFieldMask = 4
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SUB_STATUS    SalaryEstimationAttemptFieldMask = 5
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_ATTEMPT_INFO  SalaryEstimationAttemptFieldMask = 6
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UPDATED_AT    SalaryEstimationAttemptFieldMask = 7
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_EXPIRY_AT     SalaryEstimationAttemptFieldMask = 8
	SalaryEstimationAttemptFieldMask_SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_DELETED_AT    SalaryEstimationAttemptFieldMask = 9
)

// Enum value maps for SalaryEstimationAttemptFieldMask.
var (
	SalaryEstimationAttemptFieldMask_name = map[int32]string{
		0: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UNSPECIFIED",
		1: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SOURCE",
		2: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_CLIENT_PARAMS",
		3: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP",
		4: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS",
		5: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SUB_STATUS",
		6: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_ATTEMPT_INFO",
		7: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UPDATED_AT",
		8: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_EXPIRY_AT",
		9: "SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_DELETED_AT",
	}
	SalaryEstimationAttemptFieldMask_value = map[string]int32{
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UNSPECIFIED":   0,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SOURCE":        1,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_CLIENT_PARAMS": 2,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STEP":          3,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_STATUS":        4,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_SUB_STATUS":    5,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_ATTEMPT_INFO":  6,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_UPDATED_AT":    7,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_EXPIRY_AT":     8,
		"SALARY_ESTIMATION_ATTEMPT_FIELD_MASK_DELETED_AT":    9,
	}
)

func (x SalaryEstimationAttemptFieldMask) Enum() *SalaryEstimationAttemptFieldMask {
	p := new(SalaryEstimationAttemptFieldMask)
	*p = x
	return p
}

func (x SalaryEstimationAttemptFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SalaryEstimationAttemptFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryestimation_enums_proto_enumTypes[5].Descriptor()
}

func (SalaryEstimationAttemptFieldMask) Type() protoreflect.EnumType {
	return &file_api_salaryestimation_enums_proto_enumTypes[5]
}

func (x SalaryEstimationAttemptFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SalaryEstimationAttemptFieldMask.Descriptor instead.
func (SalaryEstimationAttemptFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryestimation_enums_proto_rawDescGZIP(), []int{5}
}

var File_api_salaryestimation_enums_proto protoreflect.FileDescriptor

var file_api_salaryestimation_enums_proto_rawDesc = []byte{
	0x0a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x14, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73,
	0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0x2f, 0x0a, 0x06, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x4f, 0x55, 0x52, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x4f,
	0x55, 0x52, 0x43, 0x45, 0x5f, 0x41, 0x41, 0x10, 0x01, 0x2a, 0x32, 0x0a, 0x06, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x43,
	0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10, 0x01, 0x2a, 0xae, 0x01,
	0x0a, 0x0b, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74, 0x65, 0x70, 0x12, 0x1c, 0x0a,
	0x18, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01,
	0x12, 0x22, 0x0a, 0x1e, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x45, 0x50,
	0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f,
	0x53, 0x54, 0x45, 0x50, 0x5f, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x49, 0x4e,
	0x47, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53,
	0x54, 0x45, 0x50, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x10, 0x04, 0x2a, 0xbf,
	0x01, 0x0a, 0x0d, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1e, 0x0a, 0x1a, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1a, 0x0a, 0x16, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19,
	0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x41,
	0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1c, 0x0a, 0x18, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c,
	0x45, 0x44, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x05,
	0x2a, 0x36, 0x0a, 0x10, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x53, 0x75, 0x62, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x1e, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f,
	0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x2a, 0xab, 0x04, 0x0a, 0x20, 0x53, 0x61, 0x6c,
	0x61, 0x72, 0x79, 0x45, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x74, 0x74,
	0x65, 0x6d, 0x70, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x34, 0x0a,
	0x30, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53,
	0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x4f, 0x55, 0x52,
	0x43, 0x45, 0x10, 0x01, 0x12, 0x36, 0x0a, 0x32, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45,
	0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50,
	0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x53, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x45, 0x50, 0x10, 0x03, 0x12, 0x2f, 0x0a, 0x2b, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x04, 0x12, 0x33, 0x0a, 0x2f,
	0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x42, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10,
	0x05, 0x12, 0x35, 0x0a, 0x31, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49,
	0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50,
	0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x06, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54,
	0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x07, 0x12, 0x32, 0x0a,
	0x2e, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x59, 0x5f, 0x41, 0x54, 0x10,
	0x08, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x45, 0x53, 0x54, 0x49,
	0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x65, 0x73, 0x74, 0x69, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_salaryestimation_enums_proto_rawDescOnce sync.Once
	file_api_salaryestimation_enums_proto_rawDescData = file_api_salaryestimation_enums_proto_rawDesc
)

func file_api_salaryestimation_enums_proto_rawDescGZIP() []byte {
	file_api_salaryestimation_enums_proto_rawDescOnce.Do(func() {
		file_api_salaryestimation_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_salaryestimation_enums_proto_rawDescData)
	})
	return file_api_salaryestimation_enums_proto_rawDescData
}

var file_api_salaryestimation_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_salaryestimation_enums_proto_goTypes = []interface{}{
	(Source)(0),                           // 0: api.salaryestimation.Source
	(Client)(0),                           // 1: api.salaryestimation.Client
	(AttemptStep)(0),                      // 2: api.salaryestimation.AttemptStep
	(AttemptStatus)(0),                    // 3: api.salaryestimation.AttemptStatus
	(AttemptSubStatus)(0),                 // 4: api.salaryestimation.AttemptSubStatus
	(SalaryEstimationAttemptFieldMask)(0), // 5: api.salaryestimation.SalaryEstimationAttemptFieldMask
}
var file_api_salaryestimation_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_salaryestimation_enums_proto_init() }
func file_api_salaryestimation_enums_proto_init() {
	if File_api_salaryestimation_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_salaryestimation_enums_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_salaryestimation_enums_proto_goTypes,
		DependencyIndexes: file_api_salaryestimation_enums_proto_depIdxs,
		EnumInfos:         file_api_salaryestimation_enums_proto_enumTypes,
	}.Build()
	File_api_salaryestimation_enums_proto = out.File
	file_api_salaryestimation_enums_proto_rawDesc = nil
	file_api_salaryestimation_enums_proto_goTypes = nil
	file_api_salaryestimation_enums_proto_depIdxs = nil
}
