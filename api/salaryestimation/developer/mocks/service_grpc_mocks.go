// Code generated by MockGen. DO NOT EDIT.
// Source: api/salaryestimation/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockDevSalaryEstimationClient is a mock of DevSalaryEstimationClient interface.
type MockDevSalaryEstimationClient struct {
	ctrl     *gomock.Controller
	recorder *MockDevSalaryEstimationClientMockRecorder
}

// MockDevSalaryEstimationClientMockRecorder is the mock recorder for MockDevSalaryEstimationClient.
type MockDevSalaryEstimationClientMockRecorder struct {
	mock *MockDevSalaryEstimationClient
}

// NewMockDevSalaryEstimationClient creates a new mock instance.
func NewMockDevSalaryEstimationClient(ctrl *gomock.Controller) *MockDevSalaryEstimationClient {
	mock := &MockDevSalaryEstimationClient{ctrl: ctrl}
	mock.recorder = &MockDevSalaryEstimationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevSalaryEstimationClient) EXPECT() *MockDevSalaryEstimationClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevSalaryEstimationClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevSalaryEstimationClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevSalaryEstimationClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockDevSalaryEstimationClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevSalaryEstimationClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevSalaryEstimationClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockDevSalaryEstimationClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevSalaryEstimationClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevSalaryEstimationClient)(nil).GetParameterList), varargs...)
}

// MockDevSalaryEstimationServer is a mock of DevSalaryEstimationServer interface.
type MockDevSalaryEstimationServer struct {
	ctrl     *gomock.Controller
	recorder *MockDevSalaryEstimationServerMockRecorder
}

// MockDevSalaryEstimationServerMockRecorder is the mock recorder for MockDevSalaryEstimationServer.
type MockDevSalaryEstimationServerMockRecorder struct {
	mock *MockDevSalaryEstimationServer
}

// NewMockDevSalaryEstimationServer creates a new mock instance.
func NewMockDevSalaryEstimationServer(ctrl *gomock.Controller) *MockDevSalaryEstimationServer {
	mock := &MockDevSalaryEstimationServer{ctrl: ctrl}
	mock.recorder = &MockDevSalaryEstimationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDevSalaryEstimationServer) EXPECT() *MockDevSalaryEstimationServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockDevSalaryEstimationServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockDevSalaryEstimationServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockDevSalaryEstimationServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockDevSalaryEstimationServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockDevSalaryEstimationServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockDevSalaryEstimationServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockDevSalaryEstimationServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockDevSalaryEstimationServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockDevSalaryEstimationServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeDevSalaryEstimationServer is a mock of UnsafeDevSalaryEstimationServer interface.
type MockUnsafeDevSalaryEstimationServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeDevSalaryEstimationServerMockRecorder
}

// MockUnsafeDevSalaryEstimationServerMockRecorder is the mock recorder for MockUnsafeDevSalaryEstimationServer.
type MockUnsafeDevSalaryEstimationServerMockRecorder struct {
	mock *MockUnsafeDevSalaryEstimationServer
}

// NewMockUnsafeDevSalaryEstimationServer creates a new mock instance.
func NewMockUnsafeDevSalaryEstimationServer(ctrl *gomock.Controller) *MockUnsafeDevSalaryEstimationServer {
	mock := &MockUnsafeDevSalaryEstimationServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeDevSalaryEstimationServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeDevSalaryEstimationServer) EXPECT() *MockUnsafeDevSalaryEstimationServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedDevSalaryEstimationServer mocks base method.
func (m *MockUnsafeDevSalaryEstimationServer) mustEmbedUnimplementedDevSalaryEstimationServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedDevSalaryEstimationServer")
}

// mustEmbedUnimplementedDevSalaryEstimationServer indicates an expected call of mustEmbedUnimplementedDevSalaryEstimationServer.
func (mr *MockUnsafeDevSalaryEstimationServerMockRecorder) mustEmbedUnimplementedDevSalaryEstimationServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedDevSalaryEstimationServer", reflect.TypeOf((*MockUnsafeDevSalaryEstimationServer)(nil).mustEmbedUnimplementedDevSalaryEstimationServer))
}
