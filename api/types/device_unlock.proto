// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=DeviceUnlockMechanism,DeviceUnlockMechanismStrength
syntax = "proto3";

package types;

option go_package = "github.com/epifi/gamma/api/types";
option java_package = "com.github.epifi.gamma.api.types";

// this will be used to get info from app what type of unlock mechanism is used to unlock the device
// in future more mechanisms can be added for more granularity
enum DeviceUnlockMechanism {
  DEVICE_UNLOCK_MECHANISM_UNSPECIFIED = 0;
  // this will be for if auth mechanism is not identified
  DEVICE_UNLOCK_MECHANISM_NORMAL = 1;
  DEVICE_UNLOCK_MECHANISM_BIOMETRIC = 2;
}

// this will be used get device unlock mechanism strength info from app
// strength will be collected by app using os(IOS/Android) interfaces
enum DeviceUnlockMechanismStrength {
  DEVICE_UNLOCK_MECHANISM_STRENGTH_UNSPECIFIED = 0;
  DEVICE_UNLOCK_MECHANISM_STRENGTH_HIGH = 1;
  DEVICE_UNLOCK_MECHANISM_STRENGTH_MEDIUM = 2;
  DEVICE_UNLOCK_MECHANISM_STRENGTH_LOW = 3;
}



