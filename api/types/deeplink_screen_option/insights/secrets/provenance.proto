syntax = "proto3";

package types.deeplink_screen_option.insights.secrets;

option go_package = "github.com/epifi/gamma/api/types/deeplink_screen_option/insights/secrets";
option java_package = "com.github.epifi.gamma.api.types.deeplink_screen_option.insights.secrets";

option java_multiple_files = true;

// Provenance used for the identifying the flow
enum Provenance {
  PROVENANCE_UNSPECIFIED = 0;
  // Provenance for credit report analyser flow
  // where user has already provided consent for fetching credit report during login.
  PROVENANCE_WEB_CREDIT_REPORT_ANALYSER = 1;
}
