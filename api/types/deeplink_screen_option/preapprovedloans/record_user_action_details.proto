syntax = "proto3";

package types.deeplink_screen_option.preapprovedloans;

option go_package = "github.com/epifi/gamma/api/types/deeplink_screen_option/preapprovedloans";
option java_package = "com.github.epifi.gamma.api.types.deeplink_screen_option.preapprovedloans";

message RecordUserActionDetails {
  oneof details {
    LamfLinkMfDetails lamf_link_mf_details = 1;
  }
}

message LamfLinkMfDetails {
  // link mf screen can have multiple actions.
  // this field will help BE in evaluating which action was taken by the user.
  string action_identifier = 1;
}

