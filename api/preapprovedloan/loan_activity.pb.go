// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/loan_activity.proto

package preapprovedloan

import (
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

//go:generate gen_sql -types=LoanActivity,LoanActivityDetails
type LoanActivity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	LoanAccountId string                 `protobuf:"bytes,2,opt,name=loan_account_id,json=loanAccountId,proto3" json:"loan_account_id,omitempty"`
	Type          LoanActivityType       `protobuf:"varint,3,opt,name=type,proto3,enum=preapprovedloan.LoanActivityType" json:"type,omitempty"`
	Details       *LoanActivityDetails   `protobuf:"bytes,4,opt,name=details,proto3" json:"details,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	ReferenceId   string                 `protobuf:"bytes,8,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
}

func (x *LoanActivity) Reset() {
	*x = LoanActivity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_activity_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanActivity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanActivity) ProtoMessage() {}

func (x *LoanActivity) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_activity_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanActivity.ProtoReflect.Descriptor instead.
func (*LoanActivity) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_activity_proto_rawDescGZIP(), []int{0}
}

func (x *LoanActivity) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LoanActivity) GetLoanAccountId() string {
	if x != nil {
		return x.LoanAccountId
	}
	return ""
}

func (x *LoanActivity) GetType() LoanActivityType {
	if x != nil {
		return x.Type
	}
	return LoanActivityType_LOAN_ACTIVITY_TYPE_UNSPECIFIED
}

func (x *LoanActivity) GetDetails() *LoanActivityDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *LoanActivity) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LoanActivity) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LoanActivity) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *LoanActivity) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

type LoanActivityDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TransactionId  string                 `protobuf:"bytes,1,opt,name=transaction_id,json=transactionId,proto3" json:"transaction_id,omitempty"`
	Amount         *money.Money           `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	TxnParticulars string                 `protobuf:"bytes,4,opt,name=txn_particulars,json=txnParticulars,proto3" json:"txn_particulars,omitempty"`
	TxnTime        *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=txn_time,json=txnTime,proto3" json:"txn_time,omitempty"`
	Utr            string                 `protobuf:"bytes,6,opt,name=utr,proto3" json:"utr,omitempty"`
	// Types that are assignable to ActivityTypeDetails:
	//
	//	*LoanActivityDetails_InstallmentPayoutDetails_
	//	*LoanActivityDetails_EmiActivityDetails_
	ActivityTypeDetails isLoanActivityDetails_ActivityTypeDetails `protobuf_oneof:"activity_type_details"`
}

func (x *LoanActivityDetails) Reset() {
	*x = LoanActivityDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_activity_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanActivityDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanActivityDetails) ProtoMessage() {}

func (x *LoanActivityDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_activity_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanActivityDetails.ProtoReflect.Descriptor instead.
func (*LoanActivityDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_activity_proto_rawDescGZIP(), []int{1}
}

func (x *LoanActivityDetails) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *LoanActivityDetails) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *LoanActivityDetails) GetTxnParticulars() string {
	if x != nil {
		return x.TxnParticulars
	}
	return ""
}

func (x *LoanActivityDetails) GetTxnTime() *timestamppb.Timestamp {
	if x != nil {
		return x.TxnTime
	}
	return nil
}

func (x *LoanActivityDetails) GetUtr() string {
	if x != nil {
		return x.Utr
	}
	return ""
}

func (m *LoanActivityDetails) GetActivityTypeDetails() isLoanActivityDetails_ActivityTypeDetails {
	if m != nil {
		return m.ActivityTypeDetails
	}
	return nil
}

// Deprecated: Marked as deprecated in api/preapprovedloan/loan_activity.proto.
func (x *LoanActivityDetails) GetInstallmentPayoutDetails() *LoanActivityDetails_InstallmentPayoutDetails {
	if x, ok := x.GetActivityTypeDetails().(*LoanActivityDetails_InstallmentPayoutDetails_); ok {
		return x.InstallmentPayoutDetails
	}
	return nil
}

func (x *LoanActivityDetails) GetEmiActivityDetails() *LoanActivityDetails_EmiActivityDetails {
	if x, ok := x.GetActivityTypeDetails().(*LoanActivityDetails_EmiActivityDetails_); ok {
		return x.EmiActivityDetails
	}
	return nil
}

type isLoanActivityDetails_ActivityTypeDetails interface {
	isLoanActivityDetails_ActivityTypeDetails()
}

type LoanActivityDetails_InstallmentPayoutDetails_ struct {
	// Deprecated: Marked as deprecated in api/preapprovedloan/loan_activity.proto.
	InstallmentPayoutDetails *LoanActivityDetails_InstallmentPayoutDetails `protobuf:"bytes,3,opt,name=installment_payout_details,json=installmentPayoutDetails,proto3,oneof"`
}

type LoanActivityDetails_EmiActivityDetails_ struct {
	EmiActivityDetails *LoanActivityDetails_EmiActivityDetails `protobuf:"bytes,7,opt,name=emi_activity_details,json=emiActivityDetails,proto3,oneof"`
}

func (*LoanActivityDetails_InstallmentPayoutDetails_) isLoanActivityDetails_ActivityTypeDetails() {}

func (*LoanActivityDetails_EmiActivityDetails_) isLoanActivityDetails_ActivityTypeDetails() {}

type LoanActivityDetails_InstallmentPayoutDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *LoanActivityDetails_InstallmentPayoutDetails) Reset() {
	*x = LoanActivityDetails_InstallmentPayoutDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_activity_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanActivityDetails_InstallmentPayoutDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanActivityDetails_InstallmentPayoutDetails) ProtoMessage() {}

func (x *LoanActivityDetails_InstallmentPayoutDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_activity_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanActivityDetails_InstallmentPayoutDetails.ProtoReflect.Descriptor instead.
func (*LoanActivityDetails_InstallmentPayoutDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_activity_proto_rawDescGZIP(), []int{1, 0}
}

func (x *LoanActivityDetails_InstallmentPayoutDetails) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type LoanActivityDetails_EmiActivityDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// denotes the due date of the emi for which the activity is created.
	EmiDueDate *date.Date `protobuf:"bytes,1,opt,name=emi_due_date,json=emiDueDate,proto3" json:"emi_due_date,omitempty"`
}

func (x *LoanActivityDetails_EmiActivityDetails) Reset() {
	*x = LoanActivityDetails_EmiActivityDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_activity_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanActivityDetails_EmiActivityDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanActivityDetails_EmiActivityDetails) ProtoMessage() {}

func (x *LoanActivityDetails_EmiActivityDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_activity_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanActivityDetails_EmiActivityDetails.ProtoReflect.Descriptor instead.
func (*LoanActivityDetails_EmiActivityDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_activity_proto_rawDescGZIP(), []int{1, 1}
}

func (x *LoanActivityDetails_EmiActivityDetails) GetEmiDueDate() *date.Date {
	if x != nil {
		return x.EmiDueDate
	}
	return nil
}

var File_api_preapprovedloan_loan_activity_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_loan_activity_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x1a, 0x1f, 0x61, 0x70, 0x69, 0x2f,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x91, 0x03,
	0x0a, 0x0c, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26,
	0x0a, 0x0f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x35, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76,
	0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3e, 0x0a,
	0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49,
	0x64, 0x22, 0xdb, 0x04, 0x0a, 0x13, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x27, 0x0a, 0x0f,
	0x74, 0x78, 0x6e, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x63, 0x75, 0x6c, 0x61, 0x72, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x74, 0x78, 0x6e, 0x50, 0x61, 0x72, 0x74, 0x69, 0x63,
	0x75, 0x6c, 0x61, 0x72, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x74, 0x78, 0x6e, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x07, 0x74, 0x78, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x74, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x74, 0x72, 0x12, 0x81,
	0x01, 0x0a, 0x1a, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70,
	0x61, 0x79, 0x6f, 0x75, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x3d, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x18, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x6b, 0x0a, 0x14, 0x65, 0x6d, 0x69, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x37, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x45, 0x6d, 0x69, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x12, 0x65, 0x6d, 0x69,
	0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a,
	0x2a, 0x0a, 0x18, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x50, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x1a, 0x49, 0x0a, 0x12, 0x45,
	0x6d, 0x69, 0x41, 0x63, 0x74, 0x69, 0x76, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x33, 0x0a, 0x0c, 0x65, 0x6d, 0x69, 0x5f, 0x64, 0x75, 0x65, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x65, 0x6d, 0x69, 0x44,
	0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x61, 0x63, 0x74, 0x69, 0x76, 0x69,
	0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42,
	0x58, 0x0a, 0x2a, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x5a, 0x2a, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_preapprovedloan_loan_activity_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_loan_activity_proto_rawDescData = file_api_preapprovedloan_loan_activity_proto_rawDesc
)

func file_api_preapprovedloan_loan_activity_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_loan_activity_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_loan_activity_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_loan_activity_proto_rawDescData)
	})
	return file_api_preapprovedloan_loan_activity_proto_rawDescData
}

var file_api_preapprovedloan_loan_activity_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_preapprovedloan_loan_activity_proto_goTypes = []interface{}{
	(*LoanActivity)(nil),                                 // 0: preapprovedloan.LoanActivity
	(*LoanActivityDetails)(nil),                          // 1: preapprovedloan.LoanActivityDetails
	(*LoanActivityDetails_InstallmentPayoutDetails)(nil), // 2: preapprovedloan.LoanActivityDetails.InstallmentPayoutDetails
	(*LoanActivityDetails_EmiActivityDetails)(nil),       // 3: preapprovedloan.LoanActivityDetails.EmiActivityDetails
	(LoanActivityType)(0),                                // 4: preapprovedloan.LoanActivityType
	(*timestamppb.Timestamp)(nil),                        // 5: google.protobuf.Timestamp
	(*money.Money)(nil),                                  // 6: google.type.Money
	(*date.Date)(nil),                                    // 7: google.type.Date
}
var file_api_preapprovedloan_loan_activity_proto_depIdxs = []int32{
	4,  // 0: preapprovedloan.LoanActivity.type:type_name -> preapprovedloan.LoanActivityType
	1,  // 1: preapprovedloan.LoanActivity.details:type_name -> preapprovedloan.LoanActivityDetails
	5,  // 2: preapprovedloan.LoanActivity.created_at:type_name -> google.protobuf.Timestamp
	5,  // 3: preapprovedloan.LoanActivity.updated_at:type_name -> google.protobuf.Timestamp
	5,  // 4: preapprovedloan.LoanActivity.deleted_at:type_name -> google.protobuf.Timestamp
	6,  // 5: preapprovedloan.LoanActivityDetails.amount:type_name -> google.type.Money
	5,  // 6: preapprovedloan.LoanActivityDetails.txn_time:type_name -> google.protobuf.Timestamp
	2,  // 7: preapprovedloan.LoanActivityDetails.installment_payout_details:type_name -> preapprovedloan.LoanActivityDetails.InstallmentPayoutDetails
	3,  // 8: preapprovedloan.LoanActivityDetails.emi_activity_details:type_name -> preapprovedloan.LoanActivityDetails.EmiActivityDetails
	7,  // 9: preapprovedloan.LoanActivityDetails.EmiActivityDetails.emi_due_date:type_name -> google.type.Date
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_loan_activity_proto_init() }
func file_api_preapprovedloan_loan_activity_proto_init() {
	if File_api_preapprovedloan_loan_activity_proto != nil {
		return
	}
	file_api_preapprovedloan_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_preapprovedloan_loan_activity_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanActivity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_activity_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanActivityDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_activity_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanActivityDetails_InstallmentPayoutDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_activity_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanActivityDetails_EmiActivityDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_preapprovedloan_loan_activity_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*LoanActivityDetails_InstallmentPayoutDetails_)(nil),
		(*LoanActivityDetails_EmiActivityDetails_)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_loan_activity_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_preapprovedloan_loan_activity_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_loan_activity_proto_depIdxs,
		MessageInfos:      file_api_preapprovedloan_loan_activity_proto_msgTypes,
	}.Build()
	File_api_preapprovedloan_loan_activity_proto = out.File
	file_api_preapprovedloan_loan_activity_proto_rawDesc = nil
	file_api_preapprovedloan_loan_activity_proto_goTypes = nil
	file_api_preapprovedloan_loan_activity_proto_depIdxs = nil
}
