// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/preapprovedloan/loan_request.proto

package preapprovedloan

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	enums "github.com/epifi/gamma/api/preapprovedloan/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Gender(0)

	_ = enums.LoanProgramVersion(0)
)

// Validate checks the field values on LoanRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LoanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LoanRequestMultiError, or
// nil if none found.
func (m *LoanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ActorId

	// no validation rules for OfferId

	// no validation rules for OrchId

	// no validation rules for LoanAccountId

	// no validation rules for VendorRequestId

	// no validation rules for Vendor

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	// no validation rules for Status

	// no validation rules for SubStatus

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanProgram

	if all {
		switch v := interface{}(m.GetRedirectLink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "RedirectLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestValidationError{
					field:  "RedirectLink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRedirectLink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestValidationError{
				field:  "RedirectLink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	if len(errors) > 0 {
		return LoanRequestMultiError(errors)
	}

	return nil
}

// LoanRequestMultiError is an error wrapping multiple validation errors
// returned by LoanRequest.ValidateAll() if the designated constraints aren't met.
type LoanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanRequestMultiError) AllErrors() []error { return m }

// LoanRequestValidationError is the validation error returned by
// LoanRequest.Validate if the designated constraints aren't met.
type LoanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanRequestValidationError) ErrorName() string { return "LoanRequestValidationError" }

// Error satisfies the builtin error interface
func (e LoanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanRequestValidationError{}

// Validate checks the field values on LoanRequestDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanRequestDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanRequestDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanRequestDetailsMultiError, or nil if none found.
func (m *LoanRequestDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanRequestDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetOtpInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetailsValidationError{
					field:  "OtpInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetailsValidationError{
					field:  "OtpInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOtpInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetailsValidationError{
				field:  "OtpInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MaskedAccountNumber

	if all {
		switch v := interface{}(m.GetLoanInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetailsValidationError{
					field:  "LoanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetailsValidationError{
					field:  "LoanInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetailsValidationError{
				field:  "LoanInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetailsValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetailsValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CustomerDeviceId

	// no validation rules for EmailId

	if all {
		switch v := interface{}(m.GetLoanOfferExpiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetailsValidationError{
					field:  "LoanOfferExpiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetailsValidationError{
					field:  "LoanOfferExpiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanOfferExpiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetailsValidationError{
				field:  "LoanOfferExpiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ProgramVersion

	// no validation rules for LocationToken

	// no validation rules for OldOfferId

	// no validation rules for IsInSyncMode

	// no validation rules for IsHardPullDone

	if all {
		switch v := interface{}(m.GetApplicationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetailsValidationError{
					field:  "ApplicationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetailsValidationError{
					field:  "ApplicationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApplicationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetailsValidationError{
				field:  "ApplicationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Details.(type) {
	case *LoanRequestDetails_PortfolioFetchDetails:
		if v == nil {
			err := LoanRequestDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetPortfolioFetchDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanRequestDetailsValidationError{
						field:  "PortfolioFetchDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanRequestDetailsValidationError{
						field:  "PortfolioFetchDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetPortfolioFetchDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanRequestDetailsValidationError{
					field:  "PortfolioFetchDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanRequestDetails_LoanApplicationDetails:
		if v == nil {
			err := LoanRequestDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLoanApplicationDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanRequestDetailsValidationError{
						field:  "LoanApplicationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanRequestDetailsValidationError{
						field:  "LoanApplicationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLoanApplicationDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanRequestDetailsValidationError{
					field:  "LoanApplicationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanRequestDetails_NftDetails:
		if v == nil {
			err := LoanRequestDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetNftDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanRequestDetailsValidationError{
						field:  "NftDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanRequestDetailsValidationError{
						field:  "NftDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetNftDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanRequestDetailsValidationError{
					field:  "NftDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return LoanRequestDetailsMultiError(errors)
	}

	return nil
}

// LoanRequestDetailsMultiError is an error wrapping multiple validation errors
// returned by LoanRequestDetails.ValidateAll() if the designated constraints
// aren't met.
type LoanRequestDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanRequestDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanRequestDetailsMultiError) AllErrors() []error { return m }

// LoanRequestDetailsValidationError is the validation error returned by
// LoanRequestDetails.Validate if the designated constraints aren't met.
type LoanRequestDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanRequestDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanRequestDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanRequestDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanRequestDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanRequestDetailsValidationError) ErrorName() string {
	return "LoanRequestDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanRequestDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanRequestDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanRequestDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanRequestDetailsValidationError{}

// Validate checks the field values on ApplicationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplicationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplicationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplicationDetailsMultiError, or nil if none found.
func (m *ApplicationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplicationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetKycDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplicationDetailsValidationError{
					field:  "KycDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplicationDetailsValidationError{
					field:  "KycDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplicationDetailsValidationError{
				field:  "KycDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApplicationDetailsMultiError(errors)
	}

	return nil
}

// ApplicationDetailsMultiError is an error wrapping multiple validation errors
// returned by ApplicationDetails.ValidateAll() if the designated constraints
// aren't met.
type ApplicationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplicationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplicationDetailsMultiError) AllErrors() []error { return m }

// ApplicationDetailsValidationError is the validation error returned by
// ApplicationDetails.Validate if the designated constraints aren't met.
type ApplicationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplicationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplicationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplicationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplicationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplicationDetailsValidationError) ErrorName() string {
	return "ApplicationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ApplicationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplicationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplicationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplicationDetailsValidationError{}

// Validate checks the field values on KycDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KycDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KycDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KycDetailsMultiError, or
// nil if none found.
func (m *KycDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *KycDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsKycAlreadyDonePreviously

	// no validation rules for KycType

	if all {
		switch v := interface{}(m.GetPersonalData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycDetailsValidationError{
					field:  "PersonalData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycDetailsValidationError{
					field:  "PersonalData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycDetailsValidationError{
				field:  "PersonalData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RawResponse

	// no validation rules for KycDocumentNumber

	// no validation rules for UserImagePath

	// no validation rules for VendorKycRequestId

	if len(errors) > 0 {
		return KycDetailsMultiError(errors)
	}

	return nil
}

// KycDetailsMultiError is an error wrapping multiple validation errors
// returned by KycDetails.ValidateAll() if the designated constraints aren't met.
type KycDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KycDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KycDetailsMultiError) AllErrors() []error { return m }

// KycDetailsValidationError is the validation error returned by
// KycDetails.Validate if the designated constraints aren't met.
type KycDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KycDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KycDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KycDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KycDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KycDetailsValidationError) ErrorName() string { return "KycDetailsValidationError" }

// Error satisfies the builtin error interface
func (e KycDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKycDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KycDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KycDetailsValidationError{}

// Validate checks the field values on PortfolioFetchDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PortfolioFetchDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PortfolioFetchDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PortfolioFetchDetailsMultiError, or nil if none found.
func (m *PortfolioFetchDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PortfolioFetchDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Details.(type) {
	case *PortfolioFetchDetails_FiftyfinLamfDetails:
		if v == nil {
			err := PortfolioFetchDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFiftyfinLamfDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, PortfolioFetchDetailsValidationError{
						field:  "FiftyfinLamfDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, PortfolioFetchDetailsValidationError{
						field:  "FiftyfinLamfDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFiftyfinLamfDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return PortfolioFetchDetailsValidationError{
					field:  "FiftyfinLamfDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return PortfolioFetchDetailsMultiError(errors)
	}

	return nil
}

// PortfolioFetchDetailsMultiError is an error wrapping multiple validation
// errors returned by PortfolioFetchDetails.ValidateAll() if the designated
// constraints aren't met.
type PortfolioFetchDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PortfolioFetchDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PortfolioFetchDetailsMultiError) AllErrors() []error { return m }

// PortfolioFetchDetailsValidationError is the validation error returned by
// PortfolioFetchDetails.Validate if the designated constraints aren't met.
type PortfolioFetchDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PortfolioFetchDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PortfolioFetchDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PortfolioFetchDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PortfolioFetchDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PortfolioFetchDetailsValidationError) ErrorName() string {
	return "PortfolioFetchDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PortfolioFetchDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPortfolioFetchDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PortfolioFetchDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PortfolioFetchDetailsValidationError{}

// Validate checks the field values on FiftyfinLamfPortfolioFetchDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FiftyfinLamfPortfolioFetchDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinLamfPortfolioFetchDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FiftyfinLamfPortfolioFetchDetailsMultiError, or nil if none found.
func (m *FiftyfinLamfPortfolioFetchDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinLamfPortfolioFetchDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SkipAccountDetailUpdate

	// no validation rules for FetchMfcPortfolio

	// no validation rules for FetchFiftyfinPortfolio

	// no validation rules for FetchMfcCasSummaryPortfolio

	// no validation rules for UserEmailInputInferred

	if len(errors) > 0 {
		return FiftyfinLamfPortfolioFetchDetailsMultiError(errors)
	}

	return nil
}

// FiftyfinLamfPortfolioFetchDetailsMultiError is an error wrapping multiple
// validation errors returned by
// FiftyfinLamfPortfolioFetchDetails.ValidateAll() if the designated
// constraints aren't met.
type FiftyfinLamfPortfolioFetchDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinLamfPortfolioFetchDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinLamfPortfolioFetchDetailsMultiError) AllErrors() []error { return m }

// FiftyfinLamfPortfolioFetchDetailsValidationError is the validation error
// returned by FiftyfinLamfPortfolioFetchDetails.Validate if the designated
// constraints aren't met.
type FiftyfinLamfPortfolioFetchDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinLamfPortfolioFetchDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinLamfPortfolioFetchDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinLamfPortfolioFetchDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinLamfPortfolioFetchDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinLamfPortfolioFetchDetailsValidationError) ErrorName() string {
	return "FiftyfinLamfPortfolioFetchDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinLamfPortfolioFetchDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinLamfPortfolioFetchDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinLamfPortfolioFetchDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinLamfPortfolioFetchDetailsValidationError{}

// Validate checks the field values on LoanApplicationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanApplicationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanApplicationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanApplicationDetailsMultiError, or nil if none found.
func (m *LoanApplicationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanApplicationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Details.(type) {
	case *LoanApplicationDetails_FiftyfinLamfDetails:
		if v == nil {
			err := LoanApplicationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFiftyfinLamfDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanApplicationDetailsValidationError{
						field:  "FiftyfinLamfDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanApplicationDetailsValidationError{
						field:  "FiftyfinLamfDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFiftyfinLamfDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanApplicationDetailsValidationError{
					field:  "FiftyfinLamfDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *LoanApplicationDetails_LdcLoanApplicationDetails:
		if v == nil {
			err := LoanApplicationDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetLdcLoanApplicationDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, LoanApplicationDetailsValidationError{
						field:  "LdcLoanApplicationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, LoanApplicationDetailsValidationError{
						field:  "LdcLoanApplicationDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetLdcLoanApplicationDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return LoanApplicationDetailsValidationError{
					field:  "LdcLoanApplicationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return LoanApplicationDetailsMultiError(errors)
	}

	return nil
}

// LoanApplicationDetailsMultiError is an error wrapping multiple validation
// errors returned by LoanApplicationDetails.ValidateAll() if the designated
// constraints aren't met.
type LoanApplicationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanApplicationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanApplicationDetailsMultiError) AllErrors() []error { return m }

// LoanApplicationDetailsValidationError is the validation error returned by
// LoanApplicationDetails.Validate if the designated constraints aren't met.
type LoanApplicationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanApplicationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanApplicationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanApplicationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanApplicationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanApplicationDetailsValidationError) ErrorName() string {
	return "LoanApplicationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanApplicationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanApplicationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanApplicationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanApplicationDetailsValidationError{}

// Validate checks the field values on LdcLoanApplicationDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LdcLoanApplicationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LdcLoanApplicationDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LdcLoanApplicationDetailsMultiError, or nil if none found.
func (m *LdcLoanApplicationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LdcLoanApplicationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPreBreDataLoanPreferences()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LdcLoanApplicationDetailsValidationError{
					field:  "PreBreDataLoanPreferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LdcLoanApplicationDetailsValidationError{
					field:  "PreBreDataLoanPreferences",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreBreDataLoanPreferences()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LdcLoanApplicationDetailsValidationError{
				field:  "PreBreDataLoanPreferences",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAaData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LdcLoanApplicationDetailsValidationError{
					field:  "AaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LdcLoanApplicationDetailsValidationError{
					field:  "AaData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAaData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LdcLoanApplicationDetailsValidationError{
				field:  "AaData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRoiModificationDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LdcLoanApplicationDetailsValidationError{
					field:  "RoiModificationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LdcLoanApplicationDetailsValidationError{
					field:  "RoiModificationDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRoiModificationDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LdcLoanApplicationDetailsValidationError{
				field:  "RoiModificationDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LdcLoanApplicationDetailsMultiError(errors)
	}

	return nil
}

// LdcLoanApplicationDetailsMultiError is an error wrapping multiple validation
// errors returned by LdcLoanApplicationDetails.ValidateAll() if the
// designated constraints aren't met.
type LdcLoanApplicationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LdcLoanApplicationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LdcLoanApplicationDetailsMultiError) AllErrors() []error { return m }

// LdcLoanApplicationDetailsValidationError is the validation error returned by
// LdcLoanApplicationDetails.Validate if the designated constraints aren't met.
type LdcLoanApplicationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LdcLoanApplicationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LdcLoanApplicationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LdcLoanApplicationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LdcLoanApplicationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LdcLoanApplicationDetailsValidationError) ErrorName() string {
	return "LdcLoanApplicationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LdcLoanApplicationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLdcLoanApplicationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LdcLoanApplicationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LdcLoanApplicationDetailsValidationError{}

// Validate checks the field values on FiftyfinLamfLoanApplicationDetails with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FiftyfinLamfLoanApplicationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinLamfLoanApplicationDetails
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FiftyfinLamfLoanApplicationDetailsMultiError, or nil if none found.
func (m *FiftyfinLamfLoanApplicationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinLamfLoanApplicationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCreateLoanAttempts() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FiftyfinLamfLoanApplicationDetailsValidationError{
						field:  fmt.Sprintf("CreateLoanAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FiftyfinLamfLoanApplicationDetailsValidationError{
						field:  fmt.Sprintf("CreateLoanAttempts[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FiftyfinLamfLoanApplicationDetailsValidationError{
					field:  fmt.Sprintf("CreateLoanAttempts[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FiftyfinLamfLoanApplicationDetailsMultiError(errors)
	}

	return nil
}

// FiftyfinLamfLoanApplicationDetailsMultiError is an error wrapping multiple
// validation errors returned by
// FiftyfinLamfLoanApplicationDetails.ValidateAll() if the designated
// constraints aren't met.
type FiftyfinLamfLoanApplicationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinLamfLoanApplicationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinLamfLoanApplicationDetailsMultiError) AllErrors() []error { return m }

// FiftyfinLamfLoanApplicationDetailsValidationError is the validation error
// returned by FiftyfinLamfLoanApplicationDetails.Validate if the designated
// constraints aren't met.
type FiftyfinLamfLoanApplicationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinLamfLoanApplicationDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinLamfLoanApplicationDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinLamfLoanApplicationDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinLamfLoanApplicationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinLamfLoanApplicationDetailsValidationError) ErrorName() string {
	return "FiftyfinLamfLoanApplicationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinLamfLoanApplicationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinLamfLoanApplicationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinLamfLoanApplicationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinLamfLoanApplicationDetailsValidationError{}

// Validate checks the field values on FiftyfinLamfCreateLoanAttempt with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiftyfinLamfCreateLoanAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinLamfCreateLoanAttempt with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// FiftyfinLamfCreateLoanAttemptMultiError, or nil if none found.
func (m *FiftyfinLamfCreateLoanAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinLamfCreateLoanAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanId

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FiftyfinLamfCreateLoanAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FiftyfinLamfCreateLoanAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FiftyfinLamfCreateLoanAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FiftyfinLamfCreateLoanAttemptMultiError(errors)
	}

	return nil
}

// FiftyfinLamfCreateLoanAttemptMultiError is an error wrapping multiple
// validation errors returned by FiftyfinLamfCreateLoanAttempt.ValidateAll()
// if the designated constraints aren't met.
type FiftyfinLamfCreateLoanAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinLamfCreateLoanAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinLamfCreateLoanAttemptMultiError) AllErrors() []error { return m }

// FiftyfinLamfCreateLoanAttemptValidationError is the validation error
// returned by FiftyfinLamfCreateLoanAttempt.Validate if the designated
// constraints aren't met.
type FiftyfinLamfCreateLoanAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinLamfCreateLoanAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinLamfCreateLoanAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinLamfCreateLoanAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinLamfCreateLoanAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinLamfCreateLoanAttemptValidationError) ErrorName() string {
	return "FiftyfinLamfCreateLoanAttemptValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinLamfCreateLoanAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinLamfCreateLoanAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinLamfCreateLoanAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinLamfCreateLoanAttemptValidationError{}

// Validate checks the field values on NFTDetails with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NFTDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NFTDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NFTDetailsMultiError, or
// nil if none found.
func (m *NFTDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *NFTDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Details.(type) {
	case *NFTDetails_Fiftyfin:
		if v == nil {
			err := NFTDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFiftyfin()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NFTDetailsValidationError{
						field:  "Fiftyfin",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NFTDetailsValidationError{
						field:  "Fiftyfin",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFiftyfin()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NFTDetailsValidationError{
					field:  "Fiftyfin",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return NFTDetailsMultiError(errors)
	}

	return nil
}

// NFTDetailsMultiError is an error wrapping multiple validation errors
// returned by NFTDetails.ValidateAll() if the designated constraints aren't met.
type NFTDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NFTDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NFTDetailsMultiError) AllErrors() []error { return m }

// NFTDetailsValidationError is the validation error returned by
// NFTDetails.Validate if the designated constraints aren't met.
type NFTDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NFTDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NFTDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NFTDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NFTDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NFTDetailsValidationError) ErrorName() string { return "NFTDetailsValidationError" }

// Error satisfies the builtin error interface
func (e NFTDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNFTDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NFTDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NFTDetailsValidationError{}

// Validate checks the field values on FiftyfinNftDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FiftyfinNftDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FiftyfinNftDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FiftyfinNftDetailsMultiError, or nil if none found.
func (m *FiftyfinNftDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FiftyfinNftDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExternalId

	// no validation rules for Type

	// no validation rules for CallerWorkflowId

	switch v := m.UserAuth.(type) {
	case *FiftyfinNftDetails_Email:
		if v == nil {
			err := FiftyfinNftDetailsValidationError{
				field:  "UserAuth",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for Email
	case *FiftyfinNftDetails_Mobile:
		if v == nil {
			err := FiftyfinNftDetailsValidationError{
				field:  "UserAuth",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMobile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FiftyfinNftDetailsValidationError{
						field:  "Mobile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FiftyfinNftDetailsValidationError{
						field:  "Mobile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMobile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FiftyfinNftDetailsValidationError{
					field:  "Mobile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}
	switch v := m.Details.(type) {
	case *FiftyfinNftDetails_UpdateEmail:
		if v == nil {
			err := FiftyfinNftDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpdateEmail()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FiftyfinNftDetailsValidationError{
						field:  "UpdateEmail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FiftyfinNftDetailsValidationError{
						field:  "UpdateEmail",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateEmail()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FiftyfinNftDetailsValidationError{
					field:  "UpdateEmail",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *FiftyfinNftDetails_UpdateMobile:
		if v == nil {
			err := FiftyfinNftDetailsValidationError{
				field:  "Details",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetUpdateMobile()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FiftyfinNftDetailsValidationError{
						field:  "UpdateMobile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FiftyfinNftDetailsValidationError{
						field:  "UpdateMobile",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetUpdateMobile()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FiftyfinNftDetailsValidationError{
					field:  "UpdateMobile",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return FiftyfinNftDetailsMultiError(errors)
	}

	return nil
}

// FiftyfinNftDetailsMultiError is an error wrapping multiple validation errors
// returned by FiftyfinNftDetails.ValidateAll() if the designated constraints
// aren't met.
type FiftyfinNftDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FiftyfinNftDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FiftyfinNftDetailsMultiError) AllErrors() []error { return m }

// FiftyfinNftDetailsValidationError is the validation error returned by
// FiftyfinNftDetails.Validate if the designated constraints aren't met.
type FiftyfinNftDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FiftyfinNftDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FiftyfinNftDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FiftyfinNftDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FiftyfinNftDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FiftyfinNftDetailsValidationError) ErrorName() string {
	return "FiftyfinNftDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FiftyfinNftDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFiftyfinNftDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FiftyfinNftDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FiftyfinNftDetailsValidationError{}

// Validate checks the field values on NFTUpdateEmailDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NFTUpdateEmailDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NFTUpdateEmailDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NFTUpdateEmailDetailsMultiError, or nil if none found.
func (m *NFTUpdateEmailDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *NFTUpdateEmailDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TargetEmail

	for idx, item := range m.GetFoliosUpdateList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NFTUpdateEmailDetailsValidationError{
						field:  fmt.Sprintf("FoliosUpdateList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NFTUpdateEmailDetailsValidationError{
						field:  fmt.Sprintf("FoliosUpdateList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NFTUpdateEmailDetailsValidationError{
					field:  fmt.Sprintf("FoliosUpdateList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NFTUpdateEmailDetailsMultiError(errors)
	}

	return nil
}

// NFTUpdateEmailDetailsMultiError is an error wrapping multiple validation
// errors returned by NFTUpdateEmailDetails.ValidateAll() if the designated
// constraints aren't met.
type NFTUpdateEmailDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NFTUpdateEmailDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NFTUpdateEmailDetailsMultiError) AllErrors() []error { return m }

// NFTUpdateEmailDetailsValidationError is the validation error returned by
// NFTUpdateEmailDetails.Validate if the designated constraints aren't met.
type NFTUpdateEmailDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NFTUpdateEmailDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NFTUpdateEmailDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NFTUpdateEmailDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NFTUpdateEmailDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NFTUpdateEmailDetailsValidationError) ErrorName() string {
	return "NFTUpdateEmailDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e NFTUpdateEmailDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNFTUpdateEmailDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NFTUpdateEmailDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NFTUpdateEmailDetailsValidationError{}

// Validate checks the field values on NftUpdateMobileDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NftUpdateMobileDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NftUpdateMobileDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NftUpdateMobileDetailsMultiError, or nil if none found.
func (m *NftUpdateMobileDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *NftUpdateMobileDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTargetMobile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NftUpdateMobileDetailsValidationError{
					field:  "TargetMobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NftUpdateMobileDetailsValidationError{
					field:  "TargetMobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetMobile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NftUpdateMobileDetailsValidationError{
				field:  "TargetMobile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetFoliosUpdateList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, NftUpdateMobileDetailsValidationError{
						field:  fmt.Sprintf("FoliosUpdateList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, NftUpdateMobileDetailsValidationError{
						field:  fmt.Sprintf("FoliosUpdateList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return NftUpdateMobileDetailsValidationError{
					field:  fmt.Sprintf("FoliosUpdateList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return NftUpdateMobileDetailsMultiError(errors)
	}

	return nil
}

// NftUpdateMobileDetailsMultiError is an error wrapping multiple validation
// errors returned by NftUpdateMobileDetails.ValidateAll() if the designated
// constraints aren't met.
type NftUpdateMobileDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NftUpdateMobileDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NftUpdateMobileDetailsMultiError) AllErrors() []error { return m }

// NftUpdateMobileDetailsValidationError is the validation error returned by
// NftUpdateMobileDetails.Validate if the designated constraints aren't met.
type NftUpdateMobileDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NftUpdateMobileDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NftUpdateMobileDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NftUpdateMobileDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NftUpdateMobileDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NftUpdateMobileDetailsValidationError) ErrorName() string {
	return "NftUpdateMobileDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e NftUpdateMobileDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNftUpdateMobileDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NftUpdateMobileDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NftUpdateMobileDetailsValidationError{}

// Validate checks the field values on FolioEmailUpdateDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FolioEmailUpdateDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FolioEmailUpdateDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FolioEmailUpdateDetailsMultiError, or nil if none found.
func (m *FolioEmailUpdateDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FolioEmailUpdateDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AmcCode

	// no validation rules for FolioNumber

	// no validation rules for Relationship

	// no validation rules for TargetEmail

	if len(errors) > 0 {
		return FolioEmailUpdateDetailsMultiError(errors)
	}

	return nil
}

// FolioEmailUpdateDetailsMultiError is an error wrapping multiple validation
// errors returned by FolioEmailUpdateDetails.ValidateAll() if the designated
// constraints aren't met.
type FolioEmailUpdateDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FolioEmailUpdateDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FolioEmailUpdateDetailsMultiError) AllErrors() []error { return m }

// FolioEmailUpdateDetailsValidationError is the validation error returned by
// FolioEmailUpdateDetails.Validate if the designated constraints aren't met.
type FolioEmailUpdateDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FolioEmailUpdateDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FolioEmailUpdateDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FolioEmailUpdateDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FolioEmailUpdateDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FolioEmailUpdateDetailsValidationError) ErrorName() string {
	return "FolioEmailUpdateDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FolioEmailUpdateDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFolioEmailUpdateDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FolioEmailUpdateDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FolioEmailUpdateDetailsValidationError{}

// Validate checks the field values on FolioMobileUpdateDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FolioMobileUpdateDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FolioMobileUpdateDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FolioMobileUpdateDetailsMultiError, or nil if none found.
func (m *FolioMobileUpdateDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FolioMobileUpdateDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AmcCode

	// no validation rules for FolioNumber

	// no validation rules for Relationship

	if all {
		switch v := interface{}(m.GetTargetMobile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FolioMobileUpdateDetailsValidationError{
					field:  "TargetMobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FolioMobileUpdateDetailsValidationError{
					field:  "TargetMobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetMobile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FolioMobileUpdateDetailsValidationError{
				field:  "TargetMobile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FolioMobileUpdateDetailsMultiError(errors)
	}

	return nil
}

// FolioMobileUpdateDetailsMultiError is an error wrapping multiple validation
// errors returned by FolioMobileUpdateDetails.ValidateAll() if the designated
// constraints aren't met.
type FolioMobileUpdateDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FolioMobileUpdateDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FolioMobileUpdateDetailsMultiError) AllErrors() []error { return m }

// FolioMobileUpdateDetailsValidationError is the validation error returned by
// FolioMobileUpdateDetails.Validate if the designated constraints aren't met.
type FolioMobileUpdateDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FolioMobileUpdateDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FolioMobileUpdateDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FolioMobileUpdateDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FolioMobileUpdateDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FolioMobileUpdateDetailsValidationError) ErrorName() string {
	return "FolioMobileUpdateDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e FolioMobileUpdateDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFolioMobileUpdateDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FolioMobileUpdateDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FolioMobileUpdateDetailsValidationError{}

// Validate checks the field values on LoanRequestDetails_OtpInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanRequestDetails_OtpInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanRequestDetails_OtpInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanRequestDetails_OtpInfoMultiError, or nil if none found.
func (m *LoanRequestDetails_OtpInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanRequestDetails_OtpInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Otp

	// no validation rules for MaxAttempts

	// no validation rules for AttemptsCount

	// no validation rules for LastEnteredOtp

	// no validation rules for OtpType

	// no validation rules for TotalOtps

	// no validation rules for OtpSerialNumber

	// no validation rules for Token

	if len(errors) > 0 {
		return LoanRequestDetails_OtpInfoMultiError(errors)
	}

	return nil
}

// LoanRequestDetails_OtpInfoMultiError is an error wrapping multiple
// validation errors returned by LoanRequestDetails_OtpInfo.ValidateAll() if
// the designated constraints aren't met.
type LoanRequestDetails_OtpInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanRequestDetails_OtpInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanRequestDetails_OtpInfoMultiError) AllErrors() []error { return m }

// LoanRequestDetails_OtpInfoValidationError is the validation error returned
// by LoanRequestDetails_OtpInfo.Validate if the designated constraints aren't met.
type LoanRequestDetails_OtpInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanRequestDetails_OtpInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanRequestDetails_OtpInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanRequestDetails_OtpInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanRequestDetails_OtpInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanRequestDetails_OtpInfoValidationError) ErrorName() string {
	return "LoanRequestDetails_OtpInfoValidationError"
}

// Error satisfies the builtin error interface
func (e LoanRequestDetails_OtpInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanRequestDetails_OtpInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanRequestDetails_OtpInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanRequestDetails_OtpInfoValidationError{}

// Validate checks the field values on LoanRequestDetails_LoanInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *LoanRequestDetails_LoanInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LoanRequestDetails_LoanInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// LoanRequestDetails_LoanInfoMultiError, or nil if none found.
func (m *LoanRequestDetails_LoanInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanRequestDetails_LoanInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfoValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TenureInMonths

	if all {
		switch v := interface{}(m.GetDisbursalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "DisbursalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "DisbursalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisbursalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfoValidationError{
				field:  "DisbursalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InterestRate

	if all {
		switch v := interface{}(m.GetEmiAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "EmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "EmiAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmiAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfoValidationError{
				field:  "EmiAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeductions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "Deductions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "Deductions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeductions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfoValidationError{
				field:  "Deductions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalPayable()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "TotalPayable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "TotalPayable",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalPayable()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfoValidationError{
				field:  "TotalPayable",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AprRate

	if all {
		switch v := interface{}(m.GetPledgeDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "PledgeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "PledgeDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPledgeDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfoValidationError{
				field:  "PledgeDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpectedDisbursmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "ExpectedDisbursmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "ExpectedDisbursmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedDisbursmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfoValidationError{
				field:  "ExpectedDisbursmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEmiStartDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "EmiStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "EmiStartDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEmiStartDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfoValidationError{
				field:  "EmiStartDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DisbursementUtr

	if all {
		switch v := interface{}(m.GetActualDisbursalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "ActualDisbursalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfoValidationError{
					field:  "ActualDisbursalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetActualDisbursalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfoValidationError{
				field:  "ActualDisbursalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsDiscountedOffer

	if len(errors) > 0 {
		return LoanRequestDetails_LoanInfoMultiError(errors)
	}

	return nil
}

// LoanRequestDetails_LoanInfoMultiError is an error wrapping multiple
// validation errors returned by LoanRequestDetails_LoanInfo.ValidateAll() if
// the designated constraints aren't met.
type LoanRequestDetails_LoanInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanRequestDetails_LoanInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanRequestDetails_LoanInfoMultiError) AllErrors() []error { return m }

// LoanRequestDetails_LoanInfoValidationError is the validation error returned
// by LoanRequestDetails_LoanInfo.Validate if the designated constraints
// aren't met.
type LoanRequestDetails_LoanInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanRequestDetails_LoanInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanRequestDetails_LoanInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanRequestDetails_LoanInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanRequestDetails_LoanInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanRequestDetails_LoanInfoValidationError) ErrorName() string {
	return "LoanRequestDetails_LoanInfoValidationError"
}

// Error satisfies the builtin error interface
func (e LoanRequestDetails_LoanInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanRequestDetails_LoanInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanRequestDetails_LoanInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanRequestDetails_LoanInfoValidationError{}

// Validate checks the field values on LoanRequestDetails_LoanInfo_Deductions
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *LoanRequestDetails_LoanInfo_Deductions) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LoanRequestDetails_LoanInfo_Deductions with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// LoanRequestDetails_LoanInfo_DeductionsMultiError, or nil if none found.
func (m *LoanRequestDetails_LoanInfo_Deductions) ValidateAll() error {
	return m.validate(true)
}

func (m *LoanRequestDetails_LoanInfo_Deductions) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTotalDeductions()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfo_DeductionsValidationError{
					field:  "TotalDeductions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfo_DeductionsValidationError{
					field:  "TotalDeductions",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalDeductions()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfo_DeductionsValidationError{
				field:  "TotalDeductions",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGst()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfo_DeductionsValidationError{
					field:  "Gst",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfo_DeductionsValidationError{
					field:  "Gst",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGst()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfo_DeductionsValidationError{
				field:  "Gst",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetProcessingFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfo_DeductionsValidationError{
					field:  "ProcessingFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfo_DeductionsValidationError{
					field:  "ProcessingFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessingFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfo_DeductionsValidationError{
				field:  "ProcessingFee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAdvanceInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfo_DeductionsValidationError{
					field:  "AdvanceInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LoanRequestDetails_LoanInfo_DeductionsValidationError{
					field:  "AdvanceInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAdvanceInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LoanRequestDetails_LoanInfo_DeductionsValidationError{
				field:  "AdvanceInterest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LoanRequestDetails_LoanInfo_DeductionsMultiError(errors)
	}

	return nil
}

// LoanRequestDetails_LoanInfo_DeductionsMultiError is an error wrapping
// multiple validation errors returned by
// LoanRequestDetails_LoanInfo_Deductions.ValidateAll() if the designated
// constraints aren't met.
type LoanRequestDetails_LoanInfo_DeductionsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LoanRequestDetails_LoanInfo_DeductionsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LoanRequestDetails_LoanInfo_DeductionsMultiError) AllErrors() []error { return m }

// LoanRequestDetails_LoanInfo_DeductionsValidationError is the validation
// error returned by LoanRequestDetails_LoanInfo_Deductions.Validate if the
// designated constraints aren't met.
type LoanRequestDetails_LoanInfo_DeductionsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LoanRequestDetails_LoanInfo_DeductionsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LoanRequestDetails_LoanInfo_DeductionsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LoanRequestDetails_LoanInfo_DeductionsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LoanRequestDetails_LoanInfo_DeductionsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LoanRequestDetails_LoanInfo_DeductionsValidationError) ErrorName() string {
	return "LoanRequestDetails_LoanInfo_DeductionsValidationError"
}

// Error satisfies the builtin error interface
func (e LoanRequestDetails_LoanInfo_DeductionsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLoanRequestDetails_LoanInfo_Deductions.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LoanRequestDetails_LoanInfo_DeductionsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LoanRequestDetails_LoanInfo_DeductionsValidationError{}

// Validate checks the field values on KycDetails_PersonalDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *KycDetails_PersonalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KycDetails_PersonalDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KycDetails_PersonalDetailsMultiError, or nil if none found.
func (m *KycDetails_PersonalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *KycDetails_PersonalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycDetails_PersonalDetailsValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycDetails_PersonalDetailsValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Gender

	if all {
		switch v := interface{}(m.GetCorrespondenceAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "CorrespondenceAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "CorrespondenceAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCorrespondenceAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycDetails_PersonalDetailsValidationError{
				field:  "CorrespondenceAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPermanentAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "PermanentAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPermanentAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycDetails_PersonalDetailsValidationError{
				field:  "PermanentAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetKycAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "KycAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "KycAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetKycAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycDetails_PersonalDetailsValidationError{
				field:  "KycAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMobile()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "Mobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "Mobile",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMobile()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycDetails_PersonalDetailsValidationError{
				field:  "Mobile",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetFathersName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "FathersName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "FathersName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFathersName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycDetails_PersonalDetailsValidationError{
				field:  "FathersName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMothersName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "MothersName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycDetails_PersonalDetailsValidationError{
					field:  "MothersName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMothersName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycDetails_PersonalDetailsValidationError{
				field:  "MothersName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return KycDetails_PersonalDetailsMultiError(errors)
	}

	return nil
}

// KycDetails_PersonalDetailsMultiError is an error wrapping multiple
// validation errors returned by KycDetails_PersonalDetails.ValidateAll() if
// the designated constraints aren't met.
type KycDetails_PersonalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KycDetails_PersonalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KycDetails_PersonalDetailsMultiError) AllErrors() []error { return m }

// KycDetails_PersonalDetailsValidationError is the validation error returned
// by KycDetails_PersonalDetails.Validate if the designated constraints aren't met.
type KycDetails_PersonalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KycDetails_PersonalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KycDetails_PersonalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KycDetails_PersonalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KycDetails_PersonalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KycDetails_PersonalDetailsValidationError) ErrorName() string {
	return "KycDetails_PersonalDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e KycDetails_PersonalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKycDetails_PersonalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KycDetails_PersonalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KycDetails_PersonalDetailsValidationError{}

// Validate checks the field values on
// LdcLoanApplicationDetails_RoiModificationDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LdcLoanApplicationDetails_RoiModificationDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LdcLoanApplicationDetails_RoiModificationDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LdcLoanApplicationDetails_RoiModificationDetailsMultiError, or nil if none found.
func (m *LdcLoanApplicationDetails_RoiModificationDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LdcLoanApplicationDetails_RoiModificationDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRoiModificationDeadline()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, LdcLoanApplicationDetails_RoiModificationDetailsValidationError{
					field:  "RoiModificationDeadline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, LdcLoanApplicationDetails_RoiModificationDetailsValidationError{
					field:  "RoiModificationDeadline",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRoiModificationDeadline()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return LdcLoanApplicationDetails_RoiModificationDetailsValidationError{
				field:  "RoiModificationDeadline",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return LdcLoanApplicationDetails_RoiModificationDetailsMultiError(errors)
	}

	return nil
}

// LdcLoanApplicationDetails_RoiModificationDetailsMultiError is an error
// wrapping multiple validation errors returned by
// LdcLoanApplicationDetails_RoiModificationDetails.ValidateAll() if the
// designated constraints aren't met.
type LdcLoanApplicationDetails_RoiModificationDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LdcLoanApplicationDetails_RoiModificationDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LdcLoanApplicationDetails_RoiModificationDetailsMultiError) AllErrors() []error { return m }

// LdcLoanApplicationDetails_RoiModificationDetailsValidationError is the
// validation error returned by
// LdcLoanApplicationDetails_RoiModificationDetails.Validate if the designated
// constraints aren't met.
type LdcLoanApplicationDetails_RoiModificationDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LdcLoanApplicationDetails_RoiModificationDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LdcLoanApplicationDetails_RoiModificationDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LdcLoanApplicationDetails_RoiModificationDetailsValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LdcLoanApplicationDetails_RoiModificationDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LdcLoanApplicationDetails_RoiModificationDetailsValidationError) ErrorName() string {
	return "LdcLoanApplicationDetails_RoiModificationDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LdcLoanApplicationDetails_RoiModificationDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLdcLoanApplicationDetails_RoiModificationDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LdcLoanApplicationDetails_RoiModificationDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LdcLoanApplicationDetails_RoiModificationDetailsValidationError{}

// Validate checks the field values on
// LdcLoanApplicationDetails_PreBreDataLoanPreferences with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *LdcLoanApplicationDetails_PreBreDataLoanPreferences) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LdcLoanApplicationDetails_PreBreDataLoanPreferences with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LdcLoanApplicationDetails_PreBreDataLoanPreferencesMultiError, or nil if
// none found.
func (m *LdcLoanApplicationDetails_PreBreDataLoanPreferences) ValidateAll() error {
	return m.validate(true)
}

func (m *LdcLoanApplicationDetails_PreBreDataLoanPreferences) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LoanAmount

	// no validation rules for Interest

	if len(errors) > 0 {
		return LdcLoanApplicationDetails_PreBreDataLoanPreferencesMultiError(errors)
	}

	return nil
}

// LdcLoanApplicationDetails_PreBreDataLoanPreferencesMultiError is an error
// wrapping multiple validation errors returned by
// LdcLoanApplicationDetails_PreBreDataLoanPreferences.ValidateAll() if the
// designated constraints aren't met.
type LdcLoanApplicationDetails_PreBreDataLoanPreferencesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LdcLoanApplicationDetails_PreBreDataLoanPreferencesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LdcLoanApplicationDetails_PreBreDataLoanPreferencesMultiError) AllErrors() []error { return m }

// LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError is the
// validation error returned by
// LdcLoanApplicationDetails_PreBreDataLoanPreferences.Validate if the
// designated constraints aren't met.
type LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError) ErrorName() string {
	return "LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError"
}

// Error satisfies the builtin error interface
func (e LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLdcLoanApplicationDetails_PreBreDataLoanPreferences.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LdcLoanApplicationDetails_PreBreDataLoanPreferencesValidationError{}

// Validate checks the field values on
// LdcLoanApplicationDetails_AaAnalysisBankDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LdcLoanApplicationDetails_AaAnalysisBankDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// LdcLoanApplicationDetails_AaAnalysisBankDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// LdcLoanApplicationDetails_AaAnalysisBankDetailsMultiError, or nil if none found.
func (m *LdcLoanApplicationDetails_AaAnalysisBankDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *LdcLoanApplicationDetails_AaAnalysisBankDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccountHolderName

	// no validation rules for AccountNumber

	// no validation rules for Ifsc

	// no validation rules for Type

	// no validation rules for BankName

	// no validation rules for IsAaDataNeeded

	if len(errors) > 0 {
		return LdcLoanApplicationDetails_AaAnalysisBankDetailsMultiError(errors)
	}

	return nil
}

// LdcLoanApplicationDetails_AaAnalysisBankDetailsMultiError is an error
// wrapping multiple validation errors returned by
// LdcLoanApplicationDetails_AaAnalysisBankDetails.ValidateAll() if the
// designated constraints aren't met.
type LdcLoanApplicationDetails_AaAnalysisBankDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LdcLoanApplicationDetails_AaAnalysisBankDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LdcLoanApplicationDetails_AaAnalysisBankDetailsMultiError) AllErrors() []error { return m }

// LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError is the
// validation error returned by
// LdcLoanApplicationDetails_AaAnalysisBankDetails.Validate if the designated
// constraints aren't met.
type LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError) ErrorName() string {
	return "LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLdcLoanApplicationDetails_AaAnalysisBankDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LdcLoanApplicationDetails_AaAnalysisBankDetailsValidationError{}
