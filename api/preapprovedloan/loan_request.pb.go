// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/preapprovedloan/loan_request.proto

package preapprovedloan

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	enums "github.com/epifi/gamma/api/preapprovedloan/enums"
	date "google.golang.org/genproto/googleapis/type/date"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NftType int32

const (
	NftType_NFT_TYPE_UNSPECIFIED   NftType = 0
	NftType_NFT_TYPE_UPDATE_EMAIL  NftType = 1
	NftType_NFT_TYPE_UPDATE_MOBILE NftType = 2
)

// Enum value maps for NftType.
var (
	NftType_name = map[int32]string{
		0: "NFT_TYPE_UNSPECIFIED",
		1: "NFT_TYPE_UPDATE_EMAIL",
		2: "NFT_TYPE_UPDATE_MOBILE",
	}
	NftType_value = map[string]int32{
		"NFT_TYPE_UNSPECIFIED":   0,
		"NFT_TYPE_UPDATE_EMAIL":  1,
		"NFT_TYPE_UPDATE_MOBILE": 2,
	}
)

func (x NftType) Enum() *NftType {
	p := new(NftType)
	*p = x
	return p
}

func (x NftType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NftType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_loan_request_proto_enumTypes[0].Descriptor()
}

func (NftType) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_loan_request_proto_enumTypes[0]
}

func (x NftType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NftType.Descriptor instead.
func (NftType) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{0}
}

type MutualFundNftNewDetailsRelationship int32

const (
	MutualFundNftNewDetailsRelationship_MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_UNSPECIFIED MutualFundNftNewDetailsRelationship = 0
	MutualFundNftNewDetailsRelationship_MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_SELF        MutualFundNftNewDetailsRelationship = 1
)

// Enum value maps for MutualFundNftNewDetailsRelationship.
var (
	MutualFundNftNewDetailsRelationship_name = map[int32]string{
		0: "MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_UNSPECIFIED",
		1: "MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_SELF",
	}
	MutualFundNftNewDetailsRelationship_value = map[string]int32{
		"MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_UNSPECIFIED": 0,
		"MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_SELF":        1,
	}
)

func (x MutualFundNftNewDetailsRelationship) Enum() *MutualFundNftNewDetailsRelationship {
	p := new(MutualFundNftNewDetailsRelationship)
	*p = x
	return p
}

func (x MutualFundNftNewDetailsRelationship) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MutualFundNftNewDetailsRelationship) Descriptor() protoreflect.EnumDescriptor {
	return file_api_preapprovedloan_loan_request_proto_enumTypes[1].Descriptor()
}

func (MutualFundNftNewDetailsRelationship) Type() protoreflect.EnumType {
	return &file_api_preapprovedloan_loan_request_proto_enumTypes[1]
}

func (x MutualFundNftNewDetailsRelationship) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MutualFundNftNewDetailsRelationship.Descriptor instead.
func (MutualFundNftNewDetailsRelationship) EnumDescriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{1}
}

//go:generate gen_sql -types=LoanRequest,LoanRequestDetails,LoanInfo,Deductions,OtpInfo
type LoanRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ActorId         string                 `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	OfferId         string                 `protobuf:"bytes,3,opt,name=offer_id,json=offerId,proto3" json:"offer_id,omitempty"`
	OrchId          string                 `protobuf:"bytes,4,opt,name=orch_id,json=orchId,proto3" json:"orch_id,omitempty"`
	LoanAccountId   string                 `protobuf:"bytes,5,opt,name=loan_account_id,json=loanAccountId,proto3" json:"loan_account_id,omitempty"`
	VendorRequestId string                 `protobuf:"bytes,6,opt,name=vendor_request_id,json=vendorRequestId,proto3" json:"vendor_request_id,omitempty"`
	Vendor          Vendor                 `protobuf:"varint,7,opt,name=vendor,proto3,enum=preapprovedloan.Vendor" json:"vendor,omitempty"`
	Details         *LoanRequestDetails    `protobuf:"bytes,8,opt,name=details,proto3" json:"details,omitempty"`
	Type            LoanRequestType        `protobuf:"varint,9,opt,name=type,proto3,enum=preapprovedloan.LoanRequestType" json:"type,omitempty"`
	Status          LoanRequestStatus      `protobuf:"varint,10,opt,name=status,proto3,enum=preapprovedloan.LoanRequestStatus" json:"status,omitempty"`
	SubStatus       LoanRequestSubStatus   `protobuf:"varint,11,opt,name=sub_status,json=subStatus,proto3,enum=preapprovedloan.LoanRequestSubStatus" json:"sub_status,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt       *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt       *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	CompletedAt     *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
	// Deeplink to redirect to the next screen
	NextAction   *deeplink.Deeplink `protobuf:"bytes,16,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
	LoanProgram  LoanProgram        `protobuf:"varint,17,opt,name=loan_program,json=loanProgram,proto3,enum=preapprovedloan.LoanProgram" json:"loan_program,omitempty"`
	RedirectLink *deeplink.Deeplink `protobuf:"bytes,18,opt,name=redirect_link,json=redirectLink,proto3" json:"redirect_link,omitempty"`
	ClientReqId  string             `protobuf:"bytes,19,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *LoanRequest) Reset() {
	*x = LoanRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanRequest) ProtoMessage() {}

func (x *LoanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanRequest.ProtoReflect.Descriptor instead.
func (*LoanRequest) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{0}
}

func (x *LoanRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LoanRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *LoanRequest) GetOfferId() string {
	if x != nil {
		return x.OfferId
	}
	return ""
}

func (x *LoanRequest) GetOrchId() string {
	if x != nil {
		return x.OrchId
	}
	return ""
}

func (x *LoanRequest) GetLoanAccountId() string {
	if x != nil {
		return x.LoanAccountId
	}
	return ""
}

func (x *LoanRequest) GetVendorRequestId() string {
	if x != nil {
		return x.VendorRequestId
	}
	return ""
}

func (x *LoanRequest) GetVendor() Vendor {
	if x != nil {
		return x.Vendor
	}
	return Vendor_VENDOR_UNSPECIFIED
}

func (x *LoanRequest) GetDetails() *LoanRequestDetails {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *LoanRequest) GetType() LoanRequestType {
	if x != nil {
		return x.Type
	}
	return LoanRequestType_LOAN_REQUEST_TYPE_UNSPECIFIED
}

func (x *LoanRequest) GetStatus() LoanRequestStatus {
	if x != nil {
		return x.Status
	}
	return LoanRequestStatus_LOAN_REQUEST_STATUS_UNSPECIFIED
}

func (x *LoanRequest) GetSubStatus() LoanRequestSubStatus {
	if x != nil {
		return x.SubStatus
	}
	return LoanRequestSubStatus_LOAN_REQUEST_SUB_STATUS_UNSPECIFIED
}

func (x *LoanRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *LoanRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *LoanRequest) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *LoanRequest) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *LoanRequest) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

func (x *LoanRequest) GetLoanProgram() LoanProgram {
	if x != nil {
		return x.LoanProgram
	}
	return LoanProgram_LOAN_PROGRAM_UNSPECIFIED
}

func (x *LoanRequest) GetRedirectLink() *deeplink.Deeplink {
	if x != nil {
		return x.RedirectLink
	}
	return nil
}

func (x *LoanRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type LoanRequestDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OtpInfo             *LoanRequestDetails_OtpInfo  `protobuf:"bytes,1,opt,name=otp_info,json=otpInfo,proto3" json:"otp_info,omitempty"`
	MaskedAccountNumber string                       `protobuf:"bytes,2,opt,name=masked_account_number,json=maskedAccountNumber,proto3" json:"masked_account_number,omitempty"`
	LoanInfo            *LoanRequestDetails_LoanInfo `protobuf:"bytes,3,opt,name=loan_info,json=loanInfo,proto3" json:"loan_info,omitempty"`
	PhoneNumber         *common.PhoneNumber          `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	CustomerDeviceId    string                       `protobuf:"bytes,5,opt,name=customer_device_id,json=customerDeviceId,proto3" json:"customer_device_id,omitempty"`
	EmailId             string                       `protobuf:"bytes,6,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
	// this is used only for view purpose(to populate data on screen)
	// NOTE: this should not be used to derive any business logic, for that use case we should always fetch loan offer from db
	LoanOfferExpiry *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=loan_offer_expiry,json=loanOfferExpiry,proto3" json:"loan_offer_expiry,omitempty"`
	// request type specific details
	//
	// Types that are assignable to Details:
	//
	//	*LoanRequestDetails_PortfolioFetchDetails
	//	*LoanRequestDetails_LoanApplicationDetails
	//	*LoanRequestDetails_NftDetails
	Details        isLoanRequestDetails_Details `protobuf_oneof:"details"`
	ProgramVersion enums.LoanProgramVersion     `protobuf:"varint,11,opt,name=program_version,json=programVersion,proto3,enum=preapprovedloan.enums.LoanProgramVersion" json:"program_version,omitempty"`
	// location token we get from client side when we apply for loan.
	LocationToken string `protobuf:"bytes,12,opt,name=location_token,json=locationToken,proto3" json:"location_token,omitempty"`
	// old_offer_id is used to store the old offer id in case of offer change during the application journey
	// as of now, offer can be changed only once for a loan request, that is why we are storing only one old offer id
	OldOfferId string `protobuf:"bytes,13,opt,name=old_offer_id,json=oldOfferId,proto3" json:"old_offer_id,omitempty"`
	// if true, the workflow that is processing this loan request will be waiting for SyncProxy signal
	IsInSyncMode bool `protobuf:"varint,14,opt,name=is_in_sync_mode,json=isInSyncMode,proto3" json:"is_in_sync_mode,omitempty"`
	// true if the lender has done an enquiry with the credit bureau
	IsHardPullDone bool `protobuf:"varint,15,opt,name=is_hard_pull_done,json=isHardPullDone,proto3" json:"is_hard_pull_done,omitempty"`
	// application_details is used to store the whole application common details
	ApplicationDetails *ApplicationDetails `protobuf:"bytes,16,opt,name=application_details,json=applicationDetails,proto3" json:"application_details,omitempty"`
}

func (x *LoanRequestDetails) Reset() {
	*x = LoanRequestDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanRequestDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanRequestDetails) ProtoMessage() {}

func (x *LoanRequestDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanRequestDetails.ProtoReflect.Descriptor instead.
func (*LoanRequestDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{1}
}

func (x *LoanRequestDetails) GetOtpInfo() *LoanRequestDetails_OtpInfo {
	if x != nil {
		return x.OtpInfo
	}
	return nil
}

func (x *LoanRequestDetails) GetMaskedAccountNumber() string {
	if x != nil {
		return x.MaskedAccountNumber
	}
	return ""
}

func (x *LoanRequestDetails) GetLoanInfo() *LoanRequestDetails_LoanInfo {
	if x != nil {
		return x.LoanInfo
	}
	return nil
}

func (x *LoanRequestDetails) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *LoanRequestDetails) GetCustomerDeviceId() string {
	if x != nil {
		return x.CustomerDeviceId
	}
	return ""
}

func (x *LoanRequestDetails) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

func (x *LoanRequestDetails) GetLoanOfferExpiry() *timestamppb.Timestamp {
	if x != nil {
		return x.LoanOfferExpiry
	}
	return nil
}

func (m *LoanRequestDetails) GetDetails() isLoanRequestDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *LoanRequestDetails) GetPortfolioFetchDetails() *PortfolioFetchDetails {
	if x, ok := x.GetDetails().(*LoanRequestDetails_PortfolioFetchDetails); ok {
		return x.PortfolioFetchDetails
	}
	return nil
}

func (x *LoanRequestDetails) GetLoanApplicationDetails() *LoanApplicationDetails {
	if x, ok := x.GetDetails().(*LoanRequestDetails_LoanApplicationDetails); ok {
		return x.LoanApplicationDetails
	}
	return nil
}

func (x *LoanRequestDetails) GetNftDetails() *NFTDetails {
	if x, ok := x.GetDetails().(*LoanRequestDetails_NftDetails); ok {
		return x.NftDetails
	}
	return nil
}

func (x *LoanRequestDetails) GetProgramVersion() enums.LoanProgramVersion {
	if x != nil {
		return x.ProgramVersion
	}
	return enums.LoanProgramVersion(0)
}

func (x *LoanRequestDetails) GetLocationToken() string {
	if x != nil {
		return x.LocationToken
	}
	return ""
}

func (x *LoanRequestDetails) GetOldOfferId() string {
	if x != nil {
		return x.OldOfferId
	}
	return ""
}

func (x *LoanRequestDetails) GetIsInSyncMode() bool {
	if x != nil {
		return x.IsInSyncMode
	}
	return false
}

func (x *LoanRequestDetails) GetIsHardPullDone() bool {
	if x != nil {
		return x.IsHardPullDone
	}
	return false
}

func (x *LoanRequestDetails) GetApplicationDetails() *ApplicationDetails {
	if x != nil {
		return x.ApplicationDetails
	}
	return nil
}

type isLoanRequestDetails_Details interface {
	isLoanRequestDetails_Details()
}

type LoanRequestDetails_PortfolioFetchDetails struct {
	PortfolioFetchDetails *PortfolioFetchDetails `protobuf:"bytes,8,opt,name=portfolio_fetch_details,json=portfolioFetchDetails,proto3,oneof"`
}

type LoanRequestDetails_LoanApplicationDetails struct {
	LoanApplicationDetails *LoanApplicationDetails `protobuf:"bytes,9,opt,name=loan_application_details,json=loanApplicationDetails,proto3,oneof"`
}

type LoanRequestDetails_NftDetails struct {
	NftDetails *NFTDetails `protobuf:"bytes,10,opt,name=nft_details,json=nftDetails,proto3,oneof"`
}

func (*LoanRequestDetails_PortfolioFetchDetails) isLoanRequestDetails_Details() {}

func (*LoanRequestDetails_LoanApplicationDetails) isLoanRequestDetails_Details() {}

func (*LoanRequestDetails_NftDetails) isLoanRequestDetails_Details() {}

// ApplicationDetails is used to store the common details of the application like kyc, mandate details, etc.
type ApplicationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	KycDetails *KycDetails `protobuf:"bytes,1,opt,name=kyc_details,json=kycDetails,proto3" json:"kyc_details,omitempty"`
}

func (x *ApplicationDetails) Reset() {
	*x = ApplicationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplicationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicationDetails) ProtoMessage() {}

func (x *ApplicationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicationDetails.ProtoReflect.Descriptor instead.
func (*ApplicationDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{2}
}

func (x *ApplicationDetails) GetKycDetails() *KycDetails {
	if x != nil {
		return x.KycDetails
	}
	return nil
}

type KycDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This bool will define if the KYC is already completed at vendor end, and needs to be skipped
	IsKycAlreadyDonePreviously bool                        `protobuf:"varint,1,opt,name=is_kyc_already_done_previously,json=isKycAlreadyDonePreviously,proto3" json:"is_kyc_already_done_previously,omitempty"`
	KycType                    KycType                     `protobuf:"varint,2,opt,name=kyc_type,json=kycType,proto3,enum=preapprovedloan.KycType" json:"kyc_type,omitempty"`
	PersonalData               *KycDetails_PersonalDetails `protobuf:"bytes,3,opt,name=personal_data,json=personalData,proto3" json:"personal_data,omitempty"`
	RawResponse                string                      `protobuf:"bytes,4,opt,name=raw_response,json=rawResponse,proto3" json:"raw_response,omitempty"`
	KycDocumentNumber          string                      `protobuf:"bytes,5,opt,name=kyc_document_number,json=kycDocumentNumber,proto3" json:"kyc_document_number,omitempty"`
	UserImagePath              string                      `protobuf:"bytes,6,opt,name=user_image_path,json=userImagePath,proto3" json:"user_image_path,omitempty"`
	VendorKycRequestId         string                      `protobuf:"bytes,7,opt,name=vendor_kyc_request_id,json=vendorKycRequestId,proto3" json:"vendor_kyc_request_id,omitempty"`
}

func (x *KycDetails) Reset() {
	*x = KycDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KycDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KycDetails) ProtoMessage() {}

func (x *KycDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KycDetails.ProtoReflect.Descriptor instead.
func (*KycDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{3}
}

func (x *KycDetails) GetIsKycAlreadyDonePreviously() bool {
	if x != nil {
		return x.IsKycAlreadyDonePreviously
	}
	return false
}

func (x *KycDetails) GetKycType() KycType {
	if x != nil {
		return x.KycType
	}
	return KycType_KYC_TYPE_UNSPECIFIED
}

func (x *KycDetails) GetPersonalData() *KycDetails_PersonalDetails {
	if x != nil {
		return x.PersonalData
	}
	return nil
}

func (x *KycDetails) GetRawResponse() string {
	if x != nil {
		return x.RawResponse
	}
	return ""
}

func (x *KycDetails) GetKycDocumentNumber() string {
	if x != nil {
		return x.KycDocumentNumber
	}
	return ""
}

func (x *KycDetails) GetUserImagePath() string {
	if x != nil {
		return x.UserImagePath
	}
	return ""
}

func (x *KycDetails) GetVendorKycRequestId() string {
	if x != nil {
		return x.VendorKycRequestId
	}
	return ""
}

type PortfolioFetchDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*PortfolioFetchDetails_FiftyfinLamfDetails
	Details isPortfolioFetchDetails_Details `protobuf_oneof:"details"`
}

func (x *PortfolioFetchDetails) Reset() {
	*x = PortfolioFetchDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PortfolioFetchDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PortfolioFetchDetails) ProtoMessage() {}

func (x *PortfolioFetchDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PortfolioFetchDetails.ProtoReflect.Descriptor instead.
func (*PortfolioFetchDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{4}
}

func (m *PortfolioFetchDetails) GetDetails() isPortfolioFetchDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *PortfolioFetchDetails) GetFiftyfinLamfDetails() *FiftyfinLamfPortfolioFetchDetails {
	if x, ok := x.GetDetails().(*PortfolioFetchDetails_FiftyfinLamfDetails); ok {
		return x.FiftyfinLamfDetails
	}
	return nil
}

type isPortfolioFetchDetails_Details interface {
	isPortfolioFetchDetails_Details()
}

type PortfolioFetchDetails_FiftyfinLamfDetails struct {
	FiftyfinLamfDetails *FiftyfinLamfPortfolioFetchDetails `protobuf:"bytes,1,opt,name=fiftyfin_lamf_details,json=fiftyfinLamfDetails,proto3,oneof"`
}

func (*PortfolioFetchDetails_FiftyfinLamfDetails) isPortfolioFetchDetails_Details() {}

type FiftyfinLamfPortfolioFetchDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// skip account detail update during portfolio fetch.
	SkipAccountDetailUpdate bool `protobuf:"varint,1,opt,name=skip_account_detail_update,json=skipAccountDetailUpdate,proto3" json:"skip_account_detail_update,omitempty"`
	FetchMfcPortfolio       bool `protobuf:"varint,2,opt,name=fetch_mfc_portfolio,json=fetchMfcPortfolio,proto3" json:"fetch_mfc_portfolio,omitempty"`
	FetchFiftyfinPortfolio  bool `protobuf:"varint,3,opt,name=fetch_fiftyfin_portfolio,json=fetchFiftyfinPortfolio,proto3" json:"fetch_fiftyfin_portfolio,omitempty"`
	// This flag should be set to true only if fetch_mfc_portfolio is set to true.
	FetchMfcCasSummaryPortfolio bool `protobuf:"varint,4,opt,name=fetch_mfc_cas_summary_portfolio,json=fetchMfcCasSummaryPortfolio,proto3" json:"fetch_mfc_cas_summary_portfolio,omitempty"`
	// This flag is set to true if the user email should be inferred from mfc portfolio. If set to false then the value is taken as input from user.
	// Also this will be used only if skip_account_details_update is set to false
	UserEmailInputInferred bool `protobuf:"varint,5,opt,name=user_email_input_inferred,json=userEmailInputInferred,proto3" json:"user_email_input_inferred,omitempty"`
}

func (x *FiftyfinLamfPortfolioFetchDetails) Reset() {
	*x = FiftyfinLamfPortfolioFetchDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiftyfinLamfPortfolioFetchDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiftyfinLamfPortfolioFetchDetails) ProtoMessage() {}

func (x *FiftyfinLamfPortfolioFetchDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiftyfinLamfPortfolioFetchDetails.ProtoReflect.Descriptor instead.
func (*FiftyfinLamfPortfolioFetchDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{5}
}

func (x *FiftyfinLamfPortfolioFetchDetails) GetSkipAccountDetailUpdate() bool {
	if x != nil {
		return x.SkipAccountDetailUpdate
	}
	return false
}

func (x *FiftyfinLamfPortfolioFetchDetails) GetFetchMfcPortfolio() bool {
	if x != nil {
		return x.FetchMfcPortfolio
	}
	return false
}

func (x *FiftyfinLamfPortfolioFetchDetails) GetFetchFiftyfinPortfolio() bool {
	if x != nil {
		return x.FetchFiftyfinPortfolio
	}
	return false
}

func (x *FiftyfinLamfPortfolioFetchDetails) GetFetchMfcCasSummaryPortfolio() bool {
	if x != nil {
		return x.FetchMfcCasSummaryPortfolio
	}
	return false
}

func (x *FiftyfinLamfPortfolioFetchDetails) GetUserEmailInputInferred() bool {
	if x != nil {
		return x.UserEmailInputInferred
	}
	return false
}

type LoanApplicationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*LoanApplicationDetails_FiftyfinLamfDetails
	//	*LoanApplicationDetails_LdcLoanApplicationDetails
	Details isLoanApplicationDetails_Details `protobuf_oneof:"details"`
}

func (x *LoanApplicationDetails) Reset() {
	*x = LoanApplicationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanApplicationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanApplicationDetails) ProtoMessage() {}

func (x *LoanApplicationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanApplicationDetails.ProtoReflect.Descriptor instead.
func (*LoanApplicationDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{6}
}

func (m *LoanApplicationDetails) GetDetails() isLoanApplicationDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *LoanApplicationDetails) GetFiftyfinLamfDetails() *FiftyfinLamfLoanApplicationDetails {
	if x, ok := x.GetDetails().(*LoanApplicationDetails_FiftyfinLamfDetails); ok {
		return x.FiftyfinLamfDetails
	}
	return nil
}

func (x *LoanApplicationDetails) GetLdcLoanApplicationDetails() *LdcLoanApplicationDetails {
	if x, ok := x.GetDetails().(*LoanApplicationDetails_LdcLoanApplicationDetails); ok {
		return x.LdcLoanApplicationDetails
	}
	return nil
}

type isLoanApplicationDetails_Details interface {
	isLoanApplicationDetails_Details()
}

type LoanApplicationDetails_FiftyfinLamfDetails struct {
	FiftyfinLamfDetails *FiftyfinLamfLoanApplicationDetails `protobuf:"bytes,1,opt,name=fiftyfin_lamf_details,json=fiftyfinLamfDetails,proto3,oneof"`
}

type LoanApplicationDetails_LdcLoanApplicationDetails struct {
	LdcLoanApplicationDetails *LdcLoanApplicationDetails `protobuf:"bytes,2,opt,name=ldc_loan_application_details,json=ldcLoanApplicationDetails,proto3,oneof"`
}

func (*LoanApplicationDetails_FiftyfinLamfDetails) isLoanApplicationDetails_Details() {}

func (*LoanApplicationDetails_LdcLoanApplicationDetails) isLoanApplicationDetails_Details() {}

type LdcLoanApplicationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PreBreDataLoanPreferences *LdcLoanApplicationDetails_PreBreDataLoanPreferences `protobuf:"bytes,1,opt,name=pre_bre_data_loan_preferences,json=preBreDataLoanPreferences,proto3" json:"pre_bre_data_loan_preferences,omitempty"`
	AaData                    *LdcLoanApplicationDetails_AaAnalysisBankDetails     `protobuf:"bytes,2,opt,name=aa_data,json=aaData,proto3" json:"aa_data,omitempty"`
	RoiModificationDetails    *LdcLoanApplicationDetails_RoiModificationDetails    `protobuf:"bytes,3,opt,name=roi_modification_details,json=roiModificationDetails,proto3" json:"roi_modification_details,omitempty"`
}

func (x *LdcLoanApplicationDetails) Reset() {
	*x = LdcLoanApplicationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LdcLoanApplicationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LdcLoanApplicationDetails) ProtoMessage() {}

func (x *LdcLoanApplicationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LdcLoanApplicationDetails.ProtoReflect.Descriptor instead.
func (*LdcLoanApplicationDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{7}
}

func (x *LdcLoanApplicationDetails) GetPreBreDataLoanPreferences() *LdcLoanApplicationDetails_PreBreDataLoanPreferences {
	if x != nil {
		return x.PreBreDataLoanPreferences
	}
	return nil
}

func (x *LdcLoanApplicationDetails) GetAaData() *LdcLoanApplicationDetails_AaAnalysisBankDetails {
	if x != nil {
		return x.AaData
	}
	return nil
}

func (x *LdcLoanApplicationDetails) GetRoiModificationDetails() *LdcLoanApplicationDetails_RoiModificationDetails {
	if x != nil {
		return x.RoiModificationDetails
	}
	return nil
}

type FiftyfinLamfLoanApplicationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreateLoanAttempts []*FiftyfinLamfCreateLoanAttempt `protobuf:"bytes,1,rep,name=create_loan_attempts,json=createLoanAttempts,proto3" json:"create_loan_attempts,omitempty"`
}

func (x *FiftyfinLamfLoanApplicationDetails) Reset() {
	*x = FiftyfinLamfLoanApplicationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiftyfinLamfLoanApplicationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiftyfinLamfLoanApplicationDetails) ProtoMessage() {}

func (x *FiftyfinLamfLoanApplicationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiftyfinLamfLoanApplicationDetails.ProtoReflect.Descriptor instead.
func (*FiftyfinLamfLoanApplicationDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{8}
}

func (x *FiftyfinLamfLoanApplicationDetails) GetCreateLoanAttempts() []*FiftyfinLamfCreateLoanAttempt {
	if x != nil {
		return x.CreateLoanAttempts
	}
	return nil
}

type FiftyfinLamfCreateLoanAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanId    string                 `protobuf:"bytes,1,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
}

func (x *FiftyfinLamfCreateLoanAttempt) Reset() {
	*x = FiftyfinLamfCreateLoanAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiftyfinLamfCreateLoanAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiftyfinLamfCreateLoanAttempt) ProtoMessage() {}

func (x *FiftyfinLamfCreateLoanAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiftyfinLamfCreateLoanAttempt.ProtoReflect.Descriptor instead.
func (*FiftyfinLamfCreateLoanAttempt) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{9}
}

func (x *FiftyfinLamfCreateLoanAttempt) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *FiftyfinLamfCreateLoanAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type NFTDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Details:
	//
	//	*NFTDetails_Fiftyfin
	Details isNFTDetails_Details `protobuf_oneof:"details"`
}

func (x *NFTDetails) Reset() {
	*x = NFTDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFTDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFTDetails) ProtoMessage() {}

func (x *NFTDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFTDetails.ProtoReflect.Descriptor instead.
func (*NFTDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{10}
}

func (m *NFTDetails) GetDetails() isNFTDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *NFTDetails) GetFiftyfin() *FiftyfinNftDetails {
	if x, ok := x.GetDetails().(*NFTDetails_Fiftyfin); ok {
		return x.Fiftyfin
	}
	return nil
}

type isNFTDetails_Details interface {
	isNFTDetails_Details()
}

type NFTDetails_Fiftyfin struct {
	Fiftyfin *FiftyfinNftDetails `protobuf:"bytes,1,opt,name=fiftyfin,proto3,oneof"`
}

func (*NFTDetails_Fiftyfin) isNFTDetails_Details() {}

type FiftyfinNftDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This will be populated once the request is created.
	ExternalId string  `protobuf:"bytes,1,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	Type       NftType `protobuf:"varint,2,opt,name=type,proto3,enum=preapprovedloan.NftType" json:"type,omitempty"`
	// Types that are assignable to UserAuth:
	//
	//	*FiftyfinNftDetails_Email
	//	*FiftyfinNftDetails_Mobile
	UserAuth isFiftyfinNftDetails_UserAuth `protobuf_oneof:"user_auth"`
	// Types that are assignable to Details:
	//
	//	*FiftyfinNftDetails_UpdateEmail
	//	*FiftyfinNftDetails_UpdateMobile
	Details isFiftyfinNftDetails_Details `protobuf_oneof:"details"`
	// sync signal will be sent to this workflow execution if not empty
	CallerWorkflowId string `protobuf:"bytes,7,opt,name=caller_workflow_id,json=callerWorkflowId,proto3" json:"caller_workflow_id,omitempty"`
}

func (x *FiftyfinNftDetails) Reset() {
	*x = FiftyfinNftDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FiftyfinNftDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FiftyfinNftDetails) ProtoMessage() {}

func (x *FiftyfinNftDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FiftyfinNftDetails.ProtoReflect.Descriptor instead.
func (*FiftyfinNftDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{11}
}

func (x *FiftyfinNftDetails) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *FiftyfinNftDetails) GetType() NftType {
	if x != nil {
		return x.Type
	}
	return NftType_NFT_TYPE_UNSPECIFIED
}

func (m *FiftyfinNftDetails) GetUserAuth() isFiftyfinNftDetails_UserAuth {
	if m != nil {
		return m.UserAuth
	}
	return nil
}

func (x *FiftyfinNftDetails) GetEmail() string {
	if x, ok := x.GetUserAuth().(*FiftyfinNftDetails_Email); ok {
		return x.Email
	}
	return ""
}

func (x *FiftyfinNftDetails) GetMobile() *common.PhoneNumber {
	if x, ok := x.GetUserAuth().(*FiftyfinNftDetails_Mobile); ok {
		return x.Mobile
	}
	return nil
}

func (m *FiftyfinNftDetails) GetDetails() isFiftyfinNftDetails_Details {
	if m != nil {
		return m.Details
	}
	return nil
}

func (x *FiftyfinNftDetails) GetUpdateEmail() *NFTUpdateEmailDetails {
	if x, ok := x.GetDetails().(*FiftyfinNftDetails_UpdateEmail); ok {
		return x.UpdateEmail
	}
	return nil
}

func (x *FiftyfinNftDetails) GetUpdateMobile() *NftUpdateMobileDetails {
	if x, ok := x.GetDetails().(*FiftyfinNftDetails_UpdateMobile); ok {
		return x.UpdateMobile
	}
	return nil
}

func (x *FiftyfinNftDetails) GetCallerWorkflowId() string {
	if x != nil {
		return x.CallerWorkflowId
	}
	return ""
}

type isFiftyfinNftDetails_UserAuth interface {
	isFiftyfinNftDetails_UserAuth()
}

type FiftyfinNftDetails_Email struct {
	Email string `protobuf:"bytes,3,opt,name=email,proto3,oneof"`
}

type FiftyfinNftDetails_Mobile struct {
	Mobile *common.PhoneNumber `protobuf:"bytes,4,opt,name=mobile,proto3,oneof"`
}

func (*FiftyfinNftDetails_Email) isFiftyfinNftDetails_UserAuth() {}

func (*FiftyfinNftDetails_Mobile) isFiftyfinNftDetails_UserAuth() {}

type isFiftyfinNftDetails_Details interface {
	isFiftyfinNftDetails_Details()
}

type FiftyfinNftDetails_UpdateEmail struct {
	UpdateEmail *NFTUpdateEmailDetails `protobuf:"bytes,5,opt,name=update_email,json=updateEmail,proto3,oneof"`
}

type FiftyfinNftDetails_UpdateMobile struct {
	UpdateMobile *NftUpdateMobileDetails `protobuf:"bytes,6,opt,name=update_mobile,json=updateMobile,proto3,oneof"`
}

func (*FiftyfinNftDetails_UpdateEmail) isFiftyfinNftDetails_Details() {}

func (*FiftyfinNftDetails_UpdateMobile) isFiftyfinNftDetails_Details() {}

type NFTUpdateEmailDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/preapprovedloan/loan_request.proto.
	FoliosList []string `protobuf:"bytes,1,rep,name=foliosList,proto3" json:"foliosList,omitempty"`
	// Deprecated: Marked as deprecated in api/preapprovedloan/loan_request.proto.
	TargetEmail      string                     `protobuf:"bytes,2,opt,name=target_email,json=targetEmail,proto3" json:"target_email,omitempty"`
	FoliosUpdateList []*FolioEmailUpdateDetails `protobuf:"bytes,3,rep,name=folios_update_list,json=foliosUpdateList,proto3" json:"folios_update_list,omitempty"`
}

func (x *NFTUpdateEmailDetails) Reset() {
	*x = NFTUpdateEmailDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NFTUpdateEmailDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NFTUpdateEmailDetails) ProtoMessage() {}

func (x *NFTUpdateEmailDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NFTUpdateEmailDetails.ProtoReflect.Descriptor instead.
func (*NFTUpdateEmailDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{12}
}

// Deprecated: Marked as deprecated in api/preapprovedloan/loan_request.proto.
func (x *NFTUpdateEmailDetails) GetFoliosList() []string {
	if x != nil {
		return x.FoliosList
	}
	return nil
}

// Deprecated: Marked as deprecated in api/preapprovedloan/loan_request.proto.
func (x *NFTUpdateEmailDetails) GetTargetEmail() string {
	if x != nil {
		return x.TargetEmail
	}
	return ""
}

func (x *NFTUpdateEmailDetails) GetFoliosUpdateList() []*FolioEmailUpdateDetails {
	if x != nil {
		return x.FoliosUpdateList
	}
	return nil
}

type NftUpdateMobileDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Deprecated: Marked as deprecated in api/preapprovedloan/loan_request.proto.
	FoliosList []string `protobuf:"bytes,1,rep,name=foliosList,proto3" json:"foliosList,omitempty"`
	// Deprecated: Marked as deprecated in api/preapprovedloan/loan_request.proto.
	TargetMobile     *common.PhoneNumber         `protobuf:"bytes,2,opt,name=target_mobile,json=targetMobile,proto3" json:"target_mobile,omitempty"`
	FoliosUpdateList []*FolioMobileUpdateDetails `protobuf:"bytes,3,rep,name=folios_update_list,json=foliosUpdateList,proto3" json:"folios_update_list,omitempty"`
}

func (x *NftUpdateMobileDetails) Reset() {
	*x = NftUpdateMobileDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NftUpdateMobileDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NftUpdateMobileDetails) ProtoMessage() {}

func (x *NftUpdateMobileDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NftUpdateMobileDetails.ProtoReflect.Descriptor instead.
func (*NftUpdateMobileDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{13}
}

// Deprecated: Marked as deprecated in api/preapprovedloan/loan_request.proto.
func (x *NftUpdateMobileDetails) GetFoliosList() []string {
	if x != nil {
		return x.FoliosList
	}
	return nil
}

// Deprecated: Marked as deprecated in api/preapprovedloan/loan_request.proto.
func (x *NftUpdateMobileDetails) GetTargetMobile() *common.PhoneNumber {
	if x != nil {
		return x.TargetMobile
	}
	return nil
}

func (x *NftUpdateMobileDetails) GetFoliosUpdateList() []*FolioMobileUpdateDetails {
	if x != nil {
		return x.FoliosUpdateList
	}
	return nil
}

type FolioEmailUpdateDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AmcCode      string                              `protobuf:"bytes,1,opt,name=amc_code,json=amcCode,proto3" json:"amc_code,omitempty"`
	FolioNumber  string                              `protobuf:"bytes,2,opt,name=folio_number,json=folioNumber,proto3" json:"folio_number,omitempty"`
	Relationship MutualFundNftNewDetailsRelationship `protobuf:"varint,3,opt,name=relationship,proto3,enum=preapprovedloan.MutualFundNftNewDetailsRelationship" json:"relationship,omitempty"`
	TargetEmail  string                              `protobuf:"bytes,4,opt,name=target_email,json=targetEmail,proto3" json:"target_email,omitempty"`
}

func (x *FolioEmailUpdateDetails) Reset() {
	*x = FolioEmailUpdateDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FolioEmailUpdateDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FolioEmailUpdateDetails) ProtoMessage() {}

func (x *FolioEmailUpdateDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FolioEmailUpdateDetails.ProtoReflect.Descriptor instead.
func (*FolioEmailUpdateDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{14}
}

func (x *FolioEmailUpdateDetails) GetAmcCode() string {
	if x != nil {
		return x.AmcCode
	}
	return ""
}

func (x *FolioEmailUpdateDetails) GetFolioNumber() string {
	if x != nil {
		return x.FolioNumber
	}
	return ""
}

func (x *FolioEmailUpdateDetails) GetRelationship() MutualFundNftNewDetailsRelationship {
	if x != nil {
		return x.Relationship
	}
	return MutualFundNftNewDetailsRelationship_MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_UNSPECIFIED
}

func (x *FolioEmailUpdateDetails) GetTargetEmail() string {
	if x != nil {
		return x.TargetEmail
	}
	return ""
}

type FolioMobileUpdateDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AmcCode      string                              `protobuf:"bytes,1,opt,name=amc_code,json=amcCode,proto3" json:"amc_code,omitempty"`
	FolioNumber  string                              `protobuf:"bytes,2,opt,name=folio_number,json=folioNumber,proto3" json:"folio_number,omitempty"`
	Relationship MutualFundNftNewDetailsRelationship `protobuf:"varint,3,opt,name=relationship,proto3,enum=preapprovedloan.MutualFundNftNewDetailsRelationship" json:"relationship,omitempty"`
	TargetMobile *common.PhoneNumber                 `protobuf:"bytes,4,opt,name=target_mobile,json=targetMobile,proto3" json:"target_mobile,omitempty"`
}

func (x *FolioMobileUpdateDetails) Reset() {
	*x = FolioMobileUpdateDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FolioMobileUpdateDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FolioMobileUpdateDetails) ProtoMessage() {}

func (x *FolioMobileUpdateDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FolioMobileUpdateDetails.ProtoReflect.Descriptor instead.
func (*FolioMobileUpdateDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{15}
}

func (x *FolioMobileUpdateDetails) GetAmcCode() string {
	if x != nil {
		return x.AmcCode
	}
	return ""
}

func (x *FolioMobileUpdateDetails) GetFolioNumber() string {
	if x != nil {
		return x.FolioNumber
	}
	return ""
}

func (x *FolioMobileUpdateDetails) GetRelationship() MutualFundNftNewDetailsRelationship {
	if x != nil {
		return x.Relationship
	}
	return MutualFundNftNewDetailsRelationship_MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_UNSPECIFIED
}

func (x *FolioMobileUpdateDetails) GetTargetMobile() *common.PhoneNumber {
	if x != nil {
		return x.TargetMobile
	}
	return nil
}

type LoanRequestDetails_OtpInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Otp           string `protobuf:"bytes,1,opt,name=otp,proto3" json:"otp,omitempty"`
	MaxAttempts   int32  `protobuf:"varint,2,opt,name=max_attempts,json=maxAttempts,proto3" json:"max_attempts,omitempty"`
	AttemptsCount int32  `protobuf:"varint,3,opt,name=attempts_count,json=attemptsCount,proto3" json:"attempts_count,omitempty"`
	// In case of failure, last entered otp becomes null
	// In case of success, update last entered otp
	LastEnteredOtp string  `protobuf:"bytes,4,opt,name=last_entered_otp,json=lastEnteredOtp,proto3" json:"last_entered_otp,omitempty"`
	OtpType        OtpType `protobuf:"varint,5,opt,name=otp_type,json=otpType,proto3,enum=preapprovedloan.OtpType" json:"otp_type,omitempty"`
	// these fields are used in case multiple OTPs are needed for verification
	// to show the user how many more steps user needs to do
	TotalOtps       int32  `protobuf:"varint,6,opt,name=total_otps,json=totalOtps,proto3" json:"total_otps,omitempty"`
	OtpSerialNumber int32  `protobuf:"varint,7,opt,name=otp_serial_number,json=otpSerialNumber,proto3" json:"otp_serial_number,omitempty"`
	Token           string `protobuf:"bytes,8,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *LoanRequestDetails_OtpInfo) Reset() {
	*x = LoanRequestDetails_OtpInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanRequestDetails_OtpInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanRequestDetails_OtpInfo) ProtoMessage() {}

func (x *LoanRequestDetails_OtpInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanRequestDetails_OtpInfo.ProtoReflect.Descriptor instead.
func (*LoanRequestDetails_OtpInfo) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{1, 0}
}

func (x *LoanRequestDetails_OtpInfo) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *LoanRequestDetails_OtpInfo) GetMaxAttempts() int32 {
	if x != nil {
		return x.MaxAttempts
	}
	return 0
}

func (x *LoanRequestDetails_OtpInfo) GetAttemptsCount() int32 {
	if x != nil {
		return x.AttemptsCount
	}
	return 0
}

func (x *LoanRequestDetails_OtpInfo) GetLastEnteredOtp() string {
	if x != nil {
		return x.LastEnteredOtp
	}
	return ""
}

func (x *LoanRequestDetails_OtpInfo) GetOtpType() OtpType {
	if x != nil {
		return x.OtpType
	}
	return OtpType_OTP_TYPE_UNSPECIFIED
}

func (x *LoanRequestDetails_OtpInfo) GetTotalOtps() int32 {
	if x != nil {
		return x.TotalOtps
	}
	return 0
}

func (x *LoanRequestDetails_OtpInfo) GetOtpSerialNumber() int32 {
	if x != nil {
		return x.OtpSerialNumber
	}
	return 0
}

func (x *LoanRequestDetails_OtpInfo) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type LoanRequestDetails_LoanInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Amount         *money.Money `protobuf:"bytes,1,opt,name=amount,proto3" json:"amount,omitempty"`
	TenureInMonths int32        `protobuf:"varint,2,opt,name=tenure_in_months,json=tenureInMonths,proto3" json:"tenure_in_months,omitempty"`
	// disbursal_amount is calculated amount before actual disbursal
	DisbursalAmount *money.Money                            `protobuf:"bytes,3,opt,name=disbursal_amount,json=disbursalAmount,proto3" json:"disbursal_amount,omitempty"`
	InterestRate    float64                                 `protobuf:"fixed64,4,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	EmiAmount       *money.Money                            `protobuf:"bytes,5,opt,name=emi_amount,json=emiAmount,proto3" json:"emi_amount,omitempty"`
	Deductions      *LoanRequestDetails_LoanInfo_Deductions `protobuf:"bytes,6,opt,name=deductions,proto3" json:"deductions,omitempty"`
	TotalPayable    *money.Money                            `protobuf:"bytes,7,opt,name=total_payable,json=totalPayable,proto3" json:"total_payable,omitempty"`
	AprRate         float64                                 `protobuf:"fixed64,8,opt,name=apr_rate,json=aprRate,proto3" json:"apr_rate,omitempty"`
	PledgeDetails   *PledgeDetails                          `protobuf:"bytes,9,opt,name=pledge_details,json=pledgeDetails,proto3" json:"pledge_details,omitempty"`
	// expected_disbursment_date is passed to the partner LMS system for creation and disbursal of loan in partner LMS system.
	ExpectedDisbursmentDate *date.Date `protobuf:"bytes,10,opt,name=expected_disbursment_date,json=expectedDisbursmentDate,proto3" json:"expected_disbursment_date,omitempty"`
	EmiStartDate            *date.Date `protobuf:"bytes,11,opt,name=emi_start_date,json=emiStartDate,proto3" json:"emi_start_date,omitempty"`
	DisbursementUtr         string     `protobuf:"bytes,12,opt,name=disbursement_utr,json=disbursementUtr,proto3" json:"disbursement_utr,omitempty"`
	// actual_disbursal_amount is the actual amount credited to user's bank account
	// this is needed currently to validate the actual disbursal amount against the disbursal amount in the partner LMS system
	ActualDisbursalAmount *money.Money `protobuf:"bytes,13,opt,name=actual_disbursal_amount,json=actualDisbursalAmount,proto3" json:"actual_disbursal_amount,omitempty"`
	// true if the loan application is started with a discounted offer
	// discount details are present in the offer
	IsDiscountedOffer bool `protobuf:"varint,14,opt,name=is_discounted_offer,json=isDiscountedOffer,proto3" json:"is_discounted_offer,omitempty"`
}

func (x *LoanRequestDetails_LoanInfo) Reset() {
	*x = LoanRequestDetails_LoanInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanRequestDetails_LoanInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanRequestDetails_LoanInfo) ProtoMessage() {}

func (x *LoanRequestDetails_LoanInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanRequestDetails_LoanInfo.ProtoReflect.Descriptor instead.
func (*LoanRequestDetails_LoanInfo) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{1, 1}
}

func (x *LoanRequestDetails_LoanInfo) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo) GetTenureInMonths() int32 {
	if x != nil {
		return x.TenureInMonths
	}
	return 0
}

func (x *LoanRequestDetails_LoanInfo) GetDisbursalAmount() *money.Money {
	if x != nil {
		return x.DisbursalAmount
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo) GetInterestRate() float64 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *LoanRequestDetails_LoanInfo) GetEmiAmount() *money.Money {
	if x != nil {
		return x.EmiAmount
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo) GetDeductions() *LoanRequestDetails_LoanInfo_Deductions {
	if x != nil {
		return x.Deductions
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo) GetTotalPayable() *money.Money {
	if x != nil {
		return x.TotalPayable
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo) GetAprRate() float64 {
	if x != nil {
		return x.AprRate
	}
	return 0
}

func (x *LoanRequestDetails_LoanInfo) GetPledgeDetails() *PledgeDetails {
	if x != nil {
		return x.PledgeDetails
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo) GetExpectedDisbursmentDate() *date.Date {
	if x != nil {
		return x.ExpectedDisbursmentDate
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo) GetEmiStartDate() *date.Date {
	if x != nil {
		return x.EmiStartDate
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo) GetDisbursementUtr() string {
	if x != nil {
		return x.DisbursementUtr
	}
	return ""
}

func (x *LoanRequestDetails_LoanInfo) GetActualDisbursalAmount() *money.Money {
	if x != nil {
		return x.ActualDisbursalAmount
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo) GetIsDiscountedOffer() bool {
	if x != nil {
		return x.IsDiscountedOffer
	}
	return false
}

type LoanRequestDetails_LoanInfo_Deductions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TotalDeductions *money.Money `protobuf:"bytes,1,opt,name=total_deductions,json=totalDeductions,proto3" json:"total_deductions,omitempty"`
	Gst             *money.Money `protobuf:"bytes,2,opt,name=gst,proto3" json:"gst,omitempty"`
	ProcessingFee   *money.Money `protobuf:"bytes,3,opt,name=processing_fee,json=processingFee,proto3" json:"processing_fee,omitempty"`
	AdvanceInterest *money.Money `protobuf:"bytes,4,opt,name=advance_interest,json=advanceInterest,proto3" json:"advance_interest,omitempty"`
}

func (x *LoanRequestDetails_LoanInfo_Deductions) Reset() {
	*x = LoanRequestDetails_LoanInfo_Deductions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoanRequestDetails_LoanInfo_Deductions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoanRequestDetails_LoanInfo_Deductions) ProtoMessage() {}

func (x *LoanRequestDetails_LoanInfo_Deductions) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoanRequestDetails_LoanInfo_Deductions.ProtoReflect.Descriptor instead.
func (*LoanRequestDetails_LoanInfo_Deductions) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{1, 1, 0}
}

func (x *LoanRequestDetails_LoanInfo_Deductions) GetTotalDeductions() *money.Money {
	if x != nil {
		return x.TotalDeductions
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo_Deductions) GetGst() *money.Money {
	if x != nil {
		return x.Gst
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo_Deductions) GetProcessingFee() *money.Money {
	if x != nil {
		return x.ProcessingFee
	}
	return nil
}

func (x *LoanRequestDetails_LoanInfo_Deductions) GetAdvanceInterest() *money.Money {
	if x != nil {
		return x.AdvanceInterest
	}
	return nil
}

type KycDetails_PersonalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name                  *common.Name          `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Dob                   *common.Date          `protobuf:"bytes,2,opt,name=dob,proto3" json:"dob,omitempty"`
	Gender                common.Gender         `protobuf:"varint,3,opt,name=gender,proto3,enum=api.typesv2.common.Gender" json:"gender,omitempty"`
	CorrespondenceAddress *common.PostalAddress `protobuf:"bytes,4,opt,name=correspondence_address,json=correspondenceAddress,proto3" json:"correspondence_address,omitempty"`
	PermanentAddress      *common.PostalAddress `protobuf:"bytes,5,opt,name=permanent_address,json=permanentAddress,proto3" json:"permanent_address,omitempty"`
	KycAddress            *common.PostalAddress `protobuf:"bytes,6,opt,name=kyc_address,json=kycAddress,proto3" json:"kyc_address,omitempty"`
	Mobile                *common.PhoneNumber   `protobuf:"bytes,7,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Pan                   string                `protobuf:"bytes,8,opt,name=pan,proto3" json:"pan,omitempty"`
	FathersName           *common.Name          `protobuf:"bytes,9,opt,name=fathers_name,json=fathersName,proto3" json:"fathers_name,omitempty"`
	MothersName           *common.Name          `protobuf:"bytes,10,opt,name=mothers_name,json=mothersName,proto3" json:"mothers_name,omitempty"`
}

func (x *KycDetails_PersonalDetails) Reset() {
	*x = KycDetails_PersonalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KycDetails_PersonalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KycDetails_PersonalDetails) ProtoMessage() {}

func (x *KycDetails_PersonalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KycDetails_PersonalDetails.ProtoReflect.Descriptor instead.
func (*KycDetails_PersonalDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{3, 0}
}

func (x *KycDetails_PersonalDetails) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *KycDetails_PersonalDetails) GetDob() *common.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *KycDetails_PersonalDetails) GetGender() common.Gender {
	if x != nil {
		return x.Gender
	}
	return common.Gender(0)
}

func (x *KycDetails_PersonalDetails) GetCorrespondenceAddress() *common.PostalAddress {
	if x != nil {
		return x.CorrespondenceAddress
	}
	return nil
}

func (x *KycDetails_PersonalDetails) GetPermanentAddress() *common.PostalAddress {
	if x != nil {
		return x.PermanentAddress
	}
	return nil
}

func (x *KycDetails_PersonalDetails) GetKycAddress() *common.PostalAddress {
	if x != nil {
		return x.KycAddress
	}
	return nil
}

func (x *KycDetails_PersonalDetails) GetMobile() *common.PhoneNumber {
	if x != nil {
		return x.Mobile
	}
	return nil
}

func (x *KycDetails_PersonalDetails) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *KycDetails_PersonalDetails) GetFathersName() *common.Name {
	if x != nil {
		return x.FathersName
	}
	return nil
}

func (x *KycDetails_PersonalDetails) GetMothersName() *common.Name {
	if x != nil {
		return x.MothersName
	}
	return nil
}

type LdcLoanApplicationDetails_RoiModificationDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RoiModificationDeadline *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=roi_modification_deadline,json=roiModificationDeadline,proto3" json:"roi_modification_deadline,omitempty"`
}

func (x *LdcLoanApplicationDetails_RoiModificationDetails) Reset() {
	*x = LdcLoanApplicationDetails_RoiModificationDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LdcLoanApplicationDetails_RoiModificationDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LdcLoanApplicationDetails_RoiModificationDetails) ProtoMessage() {}

func (x *LdcLoanApplicationDetails_RoiModificationDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LdcLoanApplicationDetails_RoiModificationDetails.ProtoReflect.Descriptor instead.
func (*LdcLoanApplicationDetails_RoiModificationDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{7, 0}
}

func (x *LdcLoanApplicationDetails_RoiModificationDetails) GetRoiModificationDeadline() *timestamppb.Timestamp {
	if x != nil {
		return x.RoiModificationDeadline
	}
	return nil
}

type LdcLoanApplicationDetails_PreBreDataLoanPreferences struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoanAmount int64 `protobuf:"varint,1,opt,name=loan_amount,json=loanAmount,proto3" json:"loan_amount,omitempty"`
	Interest   int64 `protobuf:"varint,2,opt,name=interest,proto3" json:"interest,omitempty"`
}

func (x *LdcLoanApplicationDetails_PreBreDataLoanPreferences) Reset() {
	*x = LdcLoanApplicationDetails_PreBreDataLoanPreferences{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LdcLoanApplicationDetails_PreBreDataLoanPreferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LdcLoanApplicationDetails_PreBreDataLoanPreferences) ProtoMessage() {}

func (x *LdcLoanApplicationDetails_PreBreDataLoanPreferences) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LdcLoanApplicationDetails_PreBreDataLoanPreferences.ProtoReflect.Descriptor instead.
func (*LdcLoanApplicationDetails_PreBreDataLoanPreferences) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{7, 1}
}

func (x *LdcLoanApplicationDetails_PreBreDataLoanPreferences) GetLoanAmount() int64 {
	if x != nil {
		return x.LoanAmount
	}
	return 0
}

func (x *LdcLoanApplicationDetails_PreBreDataLoanPreferences) GetInterest() int64 {
	if x != nil {
		return x.Interest
	}
	return 0
}

type LdcLoanApplicationDetails_AaAnalysisBankDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountHolderName string `protobuf:"bytes,1,opt,name=account_holder_name,json=accountHolderName,proto3" json:"account_holder_name,omitempty"`
	AccountNumber     string `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Ifsc              string `protobuf:"bytes,3,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	Type              string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	BankName          string `protobuf:"bytes,5,opt,name=bank_name,json=bankName,proto3" json:"bank_name,omitempty"`
	IsAaDataNeeded    bool   `protobuf:"varint,6,opt,name=is_aa_data_needed,json=isAaDataNeeded,proto3" json:"is_aa_data_needed,omitempty"`
}

func (x *LdcLoanApplicationDetails_AaAnalysisBankDetails) Reset() {
	*x = LdcLoanApplicationDetails_AaAnalysisBankDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LdcLoanApplicationDetails_AaAnalysisBankDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LdcLoanApplicationDetails_AaAnalysisBankDetails) ProtoMessage() {}

func (x *LdcLoanApplicationDetails_AaAnalysisBankDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_preapprovedloan_loan_request_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LdcLoanApplicationDetails_AaAnalysisBankDetails.ProtoReflect.Descriptor instead.
func (*LdcLoanApplicationDetails_AaAnalysisBankDetails) Descriptor() ([]byte, []int) {
	return file_api_preapprovedloan_loan_request_proto_rawDescGZIP(), []int{7, 2}
}

func (x *LdcLoanApplicationDetails_AaAnalysisBankDetails) GetAccountHolderName() string {
	if x != nil {
		return x.AccountHolderName
	}
	return ""
}

func (x *LdcLoanApplicationDetails_AaAnalysisBankDetails) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *LdcLoanApplicationDetails_AaAnalysisBankDetails) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *LdcLoanApplicationDetails_AaAnalysisBankDetails) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *LdcLoanApplicationDetails_AaAnalysisBankDetails) GetBankName() string {
	if x != nil {
		return x.BankName
	}
	return ""
}

func (x *LdcLoanApplicationDetails_AaAnalysisBankDetails) GetIsAaDataNeeded() bool {
	if x != nil {
		return x.IsAaDataNeeded
	}
	return false
}

var File_api_preapprovedloan_loan_request_proto protoreflect.FileDescriptor

var file_api_preapprovedloan_loan_request_proto_rawDesc = []byte{
	0x0a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x70,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbd, 0x07,
	0x0a, 0x0b, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x66, 0x66, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x6f, 0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x72, 0x63, 0x68, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64,
	0x12, 0x2f, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x17, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x12, 0x3d, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x34, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x3a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x44, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x53, 0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x73,
	0x75, 0x62, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39,
	0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6d,
	0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3f, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c,
	0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e,
	0x50, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x12, 0x40, 0x0a, 0x0d, 0x72, 0x65, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0c, 0x72, 0x65, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x22, 0xba, 0x12,
	0x0a, 0x12, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x46, 0x0a, 0x08, 0x6f, 0x74, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x4f, 0x74, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x07, 0x6f, 0x74, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x32, 0x0a, 0x15,
	0x6d, 0x61, 0x73, 0x6b, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x6d, 0x61, 0x73,
	0x6b, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x49, 0x0a, 0x09, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x42, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x2c, 0x0a, 0x12, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x11, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0f, 0x6c, 0x6f, 0x61, 0x6e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x45, 0x78, 0x70, 0x69, 0x72, 0x79,
	0x12, 0x60, 0x0a, 0x17, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x66, 0x65,
	0x74, 0x63, 0x68, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x15, 0x70, 0x6f, 0x72,
	0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x63, 0x0a, 0x18, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52,
	0x16, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x0b, 0x6e, 0x66, 0x74, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4e,
	0x46, 0x54, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0a, 0x6e, 0x66, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x52, 0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x29, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0e, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x0a, 0x0e, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x6c, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x6c, 0x64, 0x4f, 0x66, 0x66,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x5f, 0x73, 0x79,
	0x6e, 0x63, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69,
	0x73, 0x49, 0x6e, 0x53, 0x79, 0x6e, 0x63, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x29, 0x0a, 0x11, 0x69,
	0x73, 0x5f, 0x68, 0x61, 0x72, 0x64, 0x5f, 0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x64, 0x6f, 0x6e, 0x65,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x48, 0x61, 0x72, 0x64, 0x50, 0x75,
	0x6c, 0x6c, 0x44, 0x6f, 0x6e, 0x65, 0x12, 0x54, 0x0a, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0xa5, 0x02, 0x0a,
	0x07, 0x4f, 0x74, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61,
	0x78, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x6d, 0x61, 0x78, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x65, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x64, 0x5f, 0x6f, 0x74, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x6c, 0x61, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x64, 0x4f, 0x74, 0x70, 0x12, 0x33,
	0x0a, 0x08, 0x6f, 0x74, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x18, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4f, 0x74, 0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6f, 0x74, 0x70, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6f, 0x74, 0x70,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4f, 0x74,
	0x70, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x6f, 0x74, 0x70, 0x5f, 0x73, 0x65, 0x72, 0x69, 0x61, 0x6c,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x6f,
	0x74, 0x70, 0x53, 0x65, 0x72, 0x69, 0x61, 0x6c, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x14,
	0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x1a, 0x88, 0x08, 0x0a, 0x08, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x28, 0x0a,
	0x10, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x6d, 0x6f, 0x6e, 0x74, 0x68,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x49,
	0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x73, 0x12, 0x3d, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x62, 0x75,
	0x72, 0x73, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x61, 0x6c,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65,
	0x73, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x65,
	0x6d, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x09, 0x65, 0x6d, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x57,
	0x0a, 0x0a, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x37, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0a, 0x64, 0x65, 0x64,
	0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x37, 0x0a, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x70, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x79, 0x61, 0x62, 0x6c, 0x65,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x72, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x07, 0x61, 0x70, 0x72, 0x52, 0x61, 0x74, 0x65, 0x12, 0x45, 0x0a, 0x0e, 0x70,
	0x6c, 0x65, 0x64, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x50, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x0d, 0x70, 0x6c, 0x65, 0x64, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x4d, 0x0a, 0x19, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x64,
	0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x17, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x37, 0x0a, 0x0e, 0x65, 0x6d, 0x69, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0c, 0x65, 0x6d,
	0x69, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x69,
	0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x75, 0x74, 0x72, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x55, 0x74, 0x72, 0x12, 0x4a, 0x0a, 0x17, 0x61, 0x63, 0x74, 0x75, 0x61, 0x6c, 0x5f,
	0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x61, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x15, 0x61, 0x63, 0x74, 0x75,
	0x61, 0x6c, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x64, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11,
	0x69, 0x73, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65,
	0x72, 0x1a, 0xeb, 0x01, 0x0a, 0x0a, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x3d, 0x0a, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x64, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x24, 0x0a, 0x03, 0x67, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x03, 0x67, 0x73, 0x74, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46, 0x65, 0x65,
	0x12, 0x3d, 0x0a, 0x10, 0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f,
	0x61, 0x64, 0x76, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x42,
	0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x52, 0x0a, 0x12, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x3c, 0x0a, 0x0b, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4b, 0x79, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x0a, 0x6b, 0x79, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xda,
	0x07, 0x0a, 0x0a, 0x4b, 0x79, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x42, 0x0a,
	0x1e, 0x69, 0x73, 0x5f, 0x6b, 0x79, 0x63, 0x5f, 0x61, 0x6c, 0x72, 0x65, 0x61, 0x64, 0x79, 0x5f,
	0x64, 0x6f, 0x6e, 0x65, 0x5f, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x6c, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1a, 0x69, 0x73, 0x4b, 0x79, 0x63, 0x41, 0x6c, 0x72, 0x65,
	0x61, 0x64, 0x79, 0x44, 0x6f, 0x6e, 0x65, 0x50, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x6c,
	0x79, 0x12, 0x33, 0x0a, 0x08, 0x6b, 0x79, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4b, 0x79, 0x63, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6b,
	0x79, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x50, 0x0a, 0x0d, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e,
	0x61, 0x6c, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x4b, 0x79, 0x63, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x70, 0x65, 0x72, 0x73,
	0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x61, 0x77, 0x5f,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x61, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x6b,
	0x79, 0x63, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x6b, 0x79, 0x63, 0x44, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x26, 0x0a, 0x0f, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x31, 0x0a, 0x15, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6b, 0x79,
	0x63, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4b, 0x79, 0x63, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x1a, 0xd2, 0x04, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x73, 0x6f,
	0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x03, 0x64, 0x6f, 0x62, 0x12, 0x32, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x58, 0x0a, 0x16, 0x63, 0x6f, 0x72, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f,
	0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x15, 0x63, 0x6f, 0x72,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x64, 0x65, 0x6e, 0x63, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x4e, 0x0a, 0x11, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x10, 0x70, 0x65, 0x72, 0x6d, 0x61, 0x6e, 0x65, 0x6e, 0x74, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x42, 0x0a, 0x0b, 0x6b, 0x79, 0x63, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73,
	0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0a, 0x6b, 0x79, 0x63, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x37, 0x0a, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e,
	0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61,
	0x6e, 0x12, 0x3b, 0x0a, 0x0c, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x73, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x52, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b,
	0x0a, 0x0c, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x0b,
	0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x8c, 0x01, 0x0a, 0x15,
	0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x68, 0x0a, 0x15, 0x66, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69,
	0x6e, 0x5f, 0x6c, 0x61, 0x6d, 0x66, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x4c,
	0x61, 0x6d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x13, 0x66, 0x69, 0x66, 0x74,
	0x79, 0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42,
	0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xcb, 0x02, 0x0a, 0x21, 0x46,
	0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x50, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x46, 0x65, 0x74, 0x63, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x3b, 0x0a, 0x1a, 0x73, 0x6b, 0x69, 0x70, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x17, 0x73, 0x6b, 0x69, 0x70, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x6d, 0x66, 0x63, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x66,
	0x6f, 0x6c, 0x69, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x4d, 0x66, 0x63, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x12, 0x38, 0x0a,
	0x18, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x5f,
	0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x16, 0x66, 0x65, 0x74, 0x63, 0x68, 0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x50, 0x6f,
	0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x12, 0x44, 0x0a, 0x1f, 0x66, 0x65, 0x74, 0x63, 0x68,
	0x5f, 0x6d, 0x66, 0x63, 0x5f, 0x63, 0x61, 0x73, 0x5f, 0x73, 0x75, 0x6d, 0x6d, 0x61, 0x72, 0x79,
	0x5f, 0x70, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x1b, 0x66, 0x65, 0x74, 0x63, 0x68, 0x4d, 0x66, 0x63, 0x43, 0x61, 0x73, 0x53, 0x75, 0x6d,
	0x6d, 0x61, 0x72, 0x79, 0x50, 0x6f, 0x72, 0x74, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x12, 0x39, 0x0a,
	0x19, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x5f, 0x69, 0x6e, 0x70, 0x75,
	0x74, 0x5f, 0x69, 0x6e, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x16, 0x75, 0x73, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x70, 0x75, 0x74,
	0x49, 0x6e, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x22, 0xfd, 0x01, 0x0a, 0x16, 0x4c, 0x6f, 0x61,
	0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x69, 0x0a, 0x15, 0x66, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x5f,
	0x6c, 0x61, 0x6d, 0x66, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d,
	0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x13, 0x66, 0x69, 0x66, 0x74, 0x79,
	0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x6d,
	0x0a, 0x1c, 0x6c, 0x64, 0x63, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c, 0x64, 0x63, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x48, 0x00, 0x52, 0x19, 0x6c, 0x64, 0x63, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x09, 0x0a,
	0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xa9, 0x06, 0x0a, 0x19, 0x4c, 0x64, 0x63,
	0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x86, 0x01, 0x0a, 0x1d, 0x70, 0x72, 0x65, 0x5f, 0x62,
	0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x4c, 0x64, 0x63, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x50, 0x72, 0x65, 0x42, 0x72,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x73, 0x52, 0x19, 0x70, 0x72, 0x65, 0x42, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12,
	0x59, 0x0a, 0x07, 0x61, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x40, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x4c, 0x64, 0x63, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x41, 0x61, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x06, 0x61, 0x61, 0x44, 0x61, 0x74, 0x61, 0x12, 0x7b, 0x0a, 0x18, 0x72, 0x6f,
	0x69, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x41, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4c,
	0x64, 0x63, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x52, 0x6f, 0x69, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x16, 0x72, 0x6f, 0x69, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x1a, 0x70, 0x0a, 0x16, 0x52, 0x6f, 0x69, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x56, 0x0a, 0x19, 0x72, 0x6f, 0x69, 0x5f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x17, 0x72, 0x6f, 0x69, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x1a, 0x58, 0x0a, 0x19, 0x50, 0x72, 0x65,
	0x42, 0x72, 0x65, 0x44, 0x61, 0x74, 0x61, 0x4c, 0x6f, 0x61, 0x6e, 0x50, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x6c, 0x6f, 0x61,
	0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x1a, 0xde, 0x01, 0x0a, 0x15, 0x41, 0x61, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2e, 0x0a,
	0x13, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x48, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a,
	0x0e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75,
	0x6d, 0x62, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x66, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x69, 0x66, 0x73, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x62, 0x61, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x11, 0x69, 0x73, 0x5f,
	0x61, 0x61, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x65, 0x65, 0x64, 0x65, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x69, 0x73, 0x41, 0x61, 0x44, 0x61, 0x74, 0x61, 0x4e, 0x65,
	0x65, 0x64, 0x65, 0x64, 0x22, 0x86, 0x01, 0x0a, 0x22, 0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69,
	0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x60, 0x0a, 0x14, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x66, 0x74,
	0x79, 0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x6f,
	0x61, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x52, 0x12, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x73, 0x22, 0x73, 0x0a,
	0x1d, 0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x4c, 0x61, 0x6d, 0x66, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4c, 0x6f, 0x61, 0x6e, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x12, 0x17,
	0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x22, 0x5a, 0x0a, 0x0a, 0x4e, 0x46, 0x54, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x41, 0x0a, 0x08, 0x66, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x4e, 0x66, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x08, 0x66, 0x69, 0x66, 0x74, 0x79,
	0x66, 0x69, 0x6e, 0x42, 0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x99,
	0x03, 0x0a, 0x12, 0x46, 0x69, 0x66, 0x74, 0x79, 0x66, 0x69, 0x6e, 0x4e, 0x66, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x2c, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4e, 0x66, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x39, 0x0a, 0x06,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x48, 0x00, 0x52,
	0x06, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x4e, 0x46, 0x54, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x01, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x4e, 0x0a, 0x0d, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4e, 0x66,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x48, 0x01, 0x52, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x5f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x63, 0x61, 0x6c, 0x6c, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x49, 0x64, 0x42, 0x0b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x42,
	0x09, 0x0a, 0x07, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xba, 0x01, 0x0a, 0x15, 0x4e,
	0x46, 0x54, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x73, 0x4c, 0x69,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x66, 0x6f,
	0x6c, 0x69, 0x6f, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x25, 0x0a, 0x0c, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x56, 0x0a, 0x12, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x46, 0x6f,
	0x6c, 0x69, 0x6f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x10, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x73, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xdf, 0x01, 0x0a, 0x16, 0x4e, 0x66, 0x74, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x73, 0x4c, 0x69, 0x73, 0x74,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x12, 0x57, 0x0a, 0x12, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x73, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x46,
	0x6f, 0x6c, 0x69, 0x6f, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x10, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x73, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xd4, 0x01, 0x0a, 0x17, 0x46, 0x6f,
	0x6c, 0x69, 0x6f, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x6d, 0x63, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x6d, 0x63, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x12, 0x58, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x68, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x4d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x4e, 0x66, 0x74, 0x4e, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x52,
	0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x12, 0x21, 0x0a,
	0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x22, 0xf8, 0x01, 0x0a, 0x18, 0x46, 0x6f, 0x6c, 0x69, 0x6f, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x6d, 0x63, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x6d, 0x63, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x6f, 0x6c, 0x69,
	0x6f, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x66, 0x6f, 0x6c, 0x69, 0x6f, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x58, 0x0a, 0x0c, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x34, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x4d, 0x75, 0x74, 0x75, 0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x4e, 0x66,
	0x74, 0x4e, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x52, 0x0c, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x68, 0x69, 0x70, 0x12, 0x44, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x2a, 0x5a, 0x0a, 0x07, 0x4e,
	0x66, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x4e, 0x46, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x19, 0x0a, 0x15, 0x4e, 0x46, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4e,
	0x46, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4d,
	0x4f, 0x42, 0x49, 0x4c, 0x45, 0x10, 0x02, 0x2a, 0x92, 0x01, 0x0a, 0x23, 0x4d, 0x75, 0x74, 0x75,
	0x61, 0x6c, 0x46, 0x75, 0x6e, 0x64, 0x4e, 0x66, 0x74, 0x4e, 0x65, 0x77, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x68, 0x69, 0x70, 0x12,
	0x38, 0x0a, 0x34, 0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4e,
	0x46, 0x54, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x52,
	0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x31, 0x0a, 0x2d, 0x4d, 0x55, 0x54,
	0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x5f, 0x4e, 0x46, 0x54, 0x5f, 0x4e, 0x45, 0x57,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x53, 0x48, 0x49, 0x50, 0x5f, 0x53, 0x45, 0x4c, 0x46, 0x10, 0x01, 0x42, 0x58, 0x0a, 0x2a,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x5a, 0x2a, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_preapprovedloan_loan_request_proto_rawDescOnce sync.Once
	file_api_preapprovedloan_loan_request_proto_rawDescData = file_api_preapprovedloan_loan_request_proto_rawDesc
)

func file_api_preapprovedloan_loan_request_proto_rawDescGZIP() []byte {
	file_api_preapprovedloan_loan_request_proto_rawDescOnce.Do(func() {
		file_api_preapprovedloan_loan_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_preapprovedloan_loan_request_proto_rawDescData)
	})
	return file_api_preapprovedloan_loan_request_proto_rawDescData
}

var file_api_preapprovedloan_loan_request_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_preapprovedloan_loan_request_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_api_preapprovedloan_loan_request_proto_goTypes = []interface{}{
	(NftType)(0),                                                // 0: preapprovedloan.NftType
	(MutualFundNftNewDetailsRelationship)(0),                    // 1: preapprovedloan.MutualFundNftNewDetailsRelationship
	(*LoanRequest)(nil),                                         // 2: preapprovedloan.LoanRequest
	(*LoanRequestDetails)(nil),                                  // 3: preapprovedloan.LoanRequestDetails
	(*ApplicationDetails)(nil),                                  // 4: preapprovedloan.ApplicationDetails
	(*KycDetails)(nil),                                          // 5: preapprovedloan.KycDetails
	(*PortfolioFetchDetails)(nil),                               // 6: preapprovedloan.PortfolioFetchDetails
	(*FiftyfinLamfPortfolioFetchDetails)(nil),                   // 7: preapprovedloan.FiftyfinLamfPortfolioFetchDetails
	(*LoanApplicationDetails)(nil),                              // 8: preapprovedloan.LoanApplicationDetails
	(*LdcLoanApplicationDetails)(nil),                           // 9: preapprovedloan.LdcLoanApplicationDetails
	(*FiftyfinLamfLoanApplicationDetails)(nil),                  // 10: preapprovedloan.FiftyfinLamfLoanApplicationDetails
	(*FiftyfinLamfCreateLoanAttempt)(nil),                       // 11: preapprovedloan.FiftyfinLamfCreateLoanAttempt
	(*NFTDetails)(nil),                                          // 12: preapprovedloan.NFTDetails
	(*FiftyfinNftDetails)(nil),                                  // 13: preapprovedloan.FiftyfinNftDetails
	(*NFTUpdateEmailDetails)(nil),                               // 14: preapprovedloan.NFTUpdateEmailDetails
	(*NftUpdateMobileDetails)(nil),                              // 15: preapprovedloan.NftUpdateMobileDetails
	(*FolioEmailUpdateDetails)(nil),                             // 16: preapprovedloan.FolioEmailUpdateDetails
	(*FolioMobileUpdateDetails)(nil),                            // 17: preapprovedloan.FolioMobileUpdateDetails
	(*LoanRequestDetails_OtpInfo)(nil),                          // 18: preapprovedloan.LoanRequestDetails.OtpInfo
	(*LoanRequestDetails_LoanInfo)(nil),                         // 19: preapprovedloan.LoanRequestDetails.LoanInfo
	(*LoanRequestDetails_LoanInfo_Deductions)(nil),              // 20: preapprovedloan.LoanRequestDetails.LoanInfo.Deductions
	(*KycDetails_PersonalDetails)(nil),                          // 21: preapprovedloan.KycDetails.PersonalDetails
	(*LdcLoanApplicationDetails_RoiModificationDetails)(nil),    // 22: preapprovedloan.LdcLoanApplicationDetails.RoiModificationDetails
	(*LdcLoanApplicationDetails_PreBreDataLoanPreferences)(nil), // 23: preapprovedloan.LdcLoanApplicationDetails.PreBreDataLoanPreferences
	(*LdcLoanApplicationDetails_AaAnalysisBankDetails)(nil),     // 24: preapprovedloan.LdcLoanApplicationDetails.AaAnalysisBankDetails
	(Vendor)(0),                   // 25: preapprovedloan.Vendor
	(LoanRequestType)(0),          // 26: preapprovedloan.LoanRequestType
	(LoanRequestStatus)(0),        // 27: preapprovedloan.LoanRequestStatus
	(LoanRequestSubStatus)(0),     // 28: preapprovedloan.LoanRequestSubStatus
	(*timestamppb.Timestamp)(nil), // 29: google.protobuf.Timestamp
	(*deeplink.Deeplink)(nil),     // 30: frontend.deeplink.Deeplink
	(LoanProgram)(0),              // 31: preapprovedloan.LoanProgram
	(*common.PhoneNumber)(nil),    // 32: api.typesv2.common.PhoneNumber
	(enums.LoanProgramVersion)(0), // 33: preapprovedloan.enums.LoanProgramVersion
	(KycType)(0),                  // 34: preapprovedloan.KycType
	(OtpType)(0),                  // 35: preapprovedloan.OtpType
	(*money.Money)(nil),           // 36: google.type.Money
	(*PledgeDetails)(nil),         // 37: preapprovedloan.PledgeDetails
	(*date.Date)(nil),             // 38: google.type.Date
	(*common.Name)(nil),           // 39: api.typesv2.common.Name
	(*common.Date)(nil),           // 40: api.typesv2.common.Date
	(common.Gender)(0),            // 41: api.typesv2.common.Gender
	(*common.PostalAddress)(nil),  // 42: api.typesv2.common.PostalAddress
}
var file_api_preapprovedloan_loan_request_proto_depIdxs = []int32{
	25, // 0: preapprovedloan.LoanRequest.vendor:type_name -> preapprovedloan.Vendor
	3,  // 1: preapprovedloan.LoanRequest.details:type_name -> preapprovedloan.LoanRequestDetails
	26, // 2: preapprovedloan.LoanRequest.type:type_name -> preapprovedloan.LoanRequestType
	27, // 3: preapprovedloan.LoanRequest.status:type_name -> preapprovedloan.LoanRequestStatus
	28, // 4: preapprovedloan.LoanRequest.sub_status:type_name -> preapprovedloan.LoanRequestSubStatus
	29, // 5: preapprovedloan.LoanRequest.created_at:type_name -> google.protobuf.Timestamp
	29, // 6: preapprovedloan.LoanRequest.updated_at:type_name -> google.protobuf.Timestamp
	29, // 7: preapprovedloan.LoanRequest.deleted_at:type_name -> google.protobuf.Timestamp
	29, // 8: preapprovedloan.LoanRequest.completed_at:type_name -> google.protobuf.Timestamp
	30, // 9: preapprovedloan.LoanRequest.next_action:type_name -> frontend.deeplink.Deeplink
	31, // 10: preapprovedloan.LoanRequest.loan_program:type_name -> preapprovedloan.LoanProgram
	30, // 11: preapprovedloan.LoanRequest.redirect_link:type_name -> frontend.deeplink.Deeplink
	18, // 12: preapprovedloan.LoanRequestDetails.otp_info:type_name -> preapprovedloan.LoanRequestDetails.OtpInfo
	19, // 13: preapprovedloan.LoanRequestDetails.loan_info:type_name -> preapprovedloan.LoanRequestDetails.LoanInfo
	32, // 14: preapprovedloan.LoanRequestDetails.phone_number:type_name -> api.typesv2.common.PhoneNumber
	29, // 15: preapprovedloan.LoanRequestDetails.loan_offer_expiry:type_name -> google.protobuf.Timestamp
	6,  // 16: preapprovedloan.LoanRequestDetails.portfolio_fetch_details:type_name -> preapprovedloan.PortfolioFetchDetails
	8,  // 17: preapprovedloan.LoanRequestDetails.loan_application_details:type_name -> preapprovedloan.LoanApplicationDetails
	12, // 18: preapprovedloan.LoanRequestDetails.nft_details:type_name -> preapprovedloan.NFTDetails
	33, // 19: preapprovedloan.LoanRequestDetails.program_version:type_name -> preapprovedloan.enums.LoanProgramVersion
	4,  // 20: preapprovedloan.LoanRequestDetails.application_details:type_name -> preapprovedloan.ApplicationDetails
	5,  // 21: preapprovedloan.ApplicationDetails.kyc_details:type_name -> preapprovedloan.KycDetails
	34, // 22: preapprovedloan.KycDetails.kyc_type:type_name -> preapprovedloan.KycType
	21, // 23: preapprovedloan.KycDetails.personal_data:type_name -> preapprovedloan.KycDetails.PersonalDetails
	7,  // 24: preapprovedloan.PortfolioFetchDetails.fiftyfin_lamf_details:type_name -> preapprovedloan.FiftyfinLamfPortfolioFetchDetails
	10, // 25: preapprovedloan.LoanApplicationDetails.fiftyfin_lamf_details:type_name -> preapprovedloan.FiftyfinLamfLoanApplicationDetails
	9,  // 26: preapprovedloan.LoanApplicationDetails.ldc_loan_application_details:type_name -> preapprovedloan.LdcLoanApplicationDetails
	23, // 27: preapprovedloan.LdcLoanApplicationDetails.pre_bre_data_loan_preferences:type_name -> preapprovedloan.LdcLoanApplicationDetails.PreBreDataLoanPreferences
	24, // 28: preapprovedloan.LdcLoanApplicationDetails.aa_data:type_name -> preapprovedloan.LdcLoanApplicationDetails.AaAnalysisBankDetails
	22, // 29: preapprovedloan.LdcLoanApplicationDetails.roi_modification_details:type_name -> preapprovedloan.LdcLoanApplicationDetails.RoiModificationDetails
	11, // 30: preapprovedloan.FiftyfinLamfLoanApplicationDetails.create_loan_attempts:type_name -> preapprovedloan.FiftyfinLamfCreateLoanAttempt
	29, // 31: preapprovedloan.FiftyfinLamfCreateLoanAttempt.created_at:type_name -> google.protobuf.Timestamp
	13, // 32: preapprovedloan.NFTDetails.fiftyfin:type_name -> preapprovedloan.FiftyfinNftDetails
	0,  // 33: preapprovedloan.FiftyfinNftDetails.type:type_name -> preapprovedloan.NftType
	32, // 34: preapprovedloan.FiftyfinNftDetails.mobile:type_name -> api.typesv2.common.PhoneNumber
	14, // 35: preapprovedloan.FiftyfinNftDetails.update_email:type_name -> preapprovedloan.NFTUpdateEmailDetails
	15, // 36: preapprovedloan.FiftyfinNftDetails.update_mobile:type_name -> preapprovedloan.NftUpdateMobileDetails
	16, // 37: preapprovedloan.NFTUpdateEmailDetails.folios_update_list:type_name -> preapprovedloan.FolioEmailUpdateDetails
	32, // 38: preapprovedloan.NftUpdateMobileDetails.target_mobile:type_name -> api.typesv2.common.PhoneNumber
	17, // 39: preapprovedloan.NftUpdateMobileDetails.folios_update_list:type_name -> preapprovedloan.FolioMobileUpdateDetails
	1,  // 40: preapprovedloan.FolioEmailUpdateDetails.relationship:type_name -> preapprovedloan.MutualFundNftNewDetailsRelationship
	1,  // 41: preapprovedloan.FolioMobileUpdateDetails.relationship:type_name -> preapprovedloan.MutualFundNftNewDetailsRelationship
	32, // 42: preapprovedloan.FolioMobileUpdateDetails.target_mobile:type_name -> api.typesv2.common.PhoneNumber
	35, // 43: preapprovedloan.LoanRequestDetails.OtpInfo.otp_type:type_name -> preapprovedloan.OtpType
	36, // 44: preapprovedloan.LoanRequestDetails.LoanInfo.amount:type_name -> google.type.Money
	36, // 45: preapprovedloan.LoanRequestDetails.LoanInfo.disbursal_amount:type_name -> google.type.Money
	36, // 46: preapprovedloan.LoanRequestDetails.LoanInfo.emi_amount:type_name -> google.type.Money
	20, // 47: preapprovedloan.LoanRequestDetails.LoanInfo.deductions:type_name -> preapprovedloan.LoanRequestDetails.LoanInfo.Deductions
	36, // 48: preapprovedloan.LoanRequestDetails.LoanInfo.total_payable:type_name -> google.type.Money
	37, // 49: preapprovedloan.LoanRequestDetails.LoanInfo.pledge_details:type_name -> preapprovedloan.PledgeDetails
	38, // 50: preapprovedloan.LoanRequestDetails.LoanInfo.expected_disbursment_date:type_name -> google.type.Date
	38, // 51: preapprovedloan.LoanRequestDetails.LoanInfo.emi_start_date:type_name -> google.type.Date
	36, // 52: preapprovedloan.LoanRequestDetails.LoanInfo.actual_disbursal_amount:type_name -> google.type.Money
	36, // 53: preapprovedloan.LoanRequestDetails.LoanInfo.Deductions.total_deductions:type_name -> google.type.Money
	36, // 54: preapprovedloan.LoanRequestDetails.LoanInfo.Deductions.gst:type_name -> google.type.Money
	36, // 55: preapprovedloan.LoanRequestDetails.LoanInfo.Deductions.processing_fee:type_name -> google.type.Money
	36, // 56: preapprovedloan.LoanRequestDetails.LoanInfo.Deductions.advance_interest:type_name -> google.type.Money
	39, // 57: preapprovedloan.KycDetails.PersonalDetails.name:type_name -> api.typesv2.common.Name
	40, // 58: preapprovedloan.KycDetails.PersonalDetails.dob:type_name -> api.typesv2.common.Date
	41, // 59: preapprovedloan.KycDetails.PersonalDetails.gender:type_name -> api.typesv2.common.Gender
	42, // 60: preapprovedloan.KycDetails.PersonalDetails.correspondence_address:type_name -> api.typesv2.common.PostalAddress
	42, // 61: preapprovedloan.KycDetails.PersonalDetails.permanent_address:type_name -> api.typesv2.common.PostalAddress
	42, // 62: preapprovedloan.KycDetails.PersonalDetails.kyc_address:type_name -> api.typesv2.common.PostalAddress
	32, // 63: preapprovedloan.KycDetails.PersonalDetails.mobile:type_name -> api.typesv2.common.PhoneNumber
	39, // 64: preapprovedloan.KycDetails.PersonalDetails.fathers_name:type_name -> api.typesv2.common.Name
	39, // 65: preapprovedloan.KycDetails.PersonalDetails.mothers_name:type_name -> api.typesv2.common.Name
	29, // 66: preapprovedloan.LdcLoanApplicationDetails.RoiModificationDetails.roi_modification_deadline:type_name -> google.protobuf.Timestamp
	67, // [67:67] is the sub-list for method output_type
	67, // [67:67] is the sub-list for method input_type
	67, // [67:67] is the sub-list for extension type_name
	67, // [67:67] is the sub-list for extension extendee
	0,  // [0:67] is the sub-list for field type_name
}

func init() { file_api_preapprovedloan_loan_request_proto_init() }
func file_api_preapprovedloan_loan_request_proto_init() {
	if File_api_preapprovedloan_loan_request_proto != nil {
		return
	}
	file_api_preapprovedloan_enums_proto_init()
	file_api_preapprovedloan_pledge_details_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_preapprovedloan_loan_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanRequestDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplicationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KycDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PortfolioFetchDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiftyfinLamfPortfolioFetchDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanApplicationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LdcLoanApplicationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiftyfinLamfLoanApplicationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiftyfinLamfCreateLoanAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFTDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FiftyfinNftDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NFTUpdateEmailDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NftUpdateMobileDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FolioEmailUpdateDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FolioMobileUpdateDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanRequestDetails_OtpInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanRequestDetails_LoanInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoanRequestDetails_LoanInfo_Deductions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KycDetails_PersonalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LdcLoanApplicationDetails_RoiModificationDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LdcLoanApplicationDetails_PreBreDataLoanPreferences); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_preapprovedloan_loan_request_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LdcLoanApplicationDetails_AaAnalysisBankDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_preapprovedloan_loan_request_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*LoanRequestDetails_PortfolioFetchDetails)(nil),
		(*LoanRequestDetails_LoanApplicationDetails)(nil),
		(*LoanRequestDetails_NftDetails)(nil),
	}
	file_api_preapprovedloan_loan_request_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*PortfolioFetchDetails_FiftyfinLamfDetails)(nil),
	}
	file_api_preapprovedloan_loan_request_proto_msgTypes[6].OneofWrappers = []interface{}{
		(*LoanApplicationDetails_FiftyfinLamfDetails)(nil),
		(*LoanApplicationDetails_LdcLoanApplicationDetails)(nil),
	}
	file_api_preapprovedloan_loan_request_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*NFTDetails_Fiftyfin)(nil),
	}
	file_api_preapprovedloan_loan_request_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*FiftyfinNftDetails_Email)(nil),
		(*FiftyfinNftDetails_Mobile)(nil),
		(*FiftyfinNftDetails_UpdateEmail)(nil),
		(*FiftyfinNftDetails_UpdateMobile)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_preapprovedloan_loan_request_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_preapprovedloan_loan_request_proto_goTypes,
		DependencyIndexes: file_api_preapprovedloan_loan_request_proto_depIdxs,
		EnumInfos:         file_api_preapprovedloan_loan_request_proto_enumTypes,
		MessageInfos:      file_api_preapprovedloan_loan_request_proto_msgTypes,
	}.Build()
	File_api_preapprovedloan_loan_request_proto = out.File
	file_api_preapprovedloan_loan_request_proto_rawDesc = nil
	file_api_preapprovedloan_loan_request_proto_goTypes = nil
	file_api_preapprovedloan_loan_request_proto_depIdxs = nil
}
