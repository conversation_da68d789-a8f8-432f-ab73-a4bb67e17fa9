// protolint:disable MAX_LINE_LENGTH

// A service to send messages to a user through a specified medium and with a provided Quality of Service
syntax = "proto3";

package comms;

import "api/comms/email.proto";
import "api/comms/enums.proto";
import "api/comms/notification.proto";
import "api/comms/sms.proto";
import "api/comms/whatsapp.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/comms";
option java_package = "com.github.epifi.gamma.api.comms";

// Consolidated fields which are to be shown for all the messages
message DetailedMessage {
  string comms_message_id = 1;
  string qos = 2;
  string medium = 3;
  string sqs_message_id = 4;
  string vendor = 5;
  string vendor_message_id = 6;
  int64 retries = 7;
  string message_state = 8;
  CommsMessageSubStatus message_sub_status = 17;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  UserIdentifierType user_identifier_type = 11;
  string user_identifier_value = 12;
  string actor_id = 13;
  string client_id = 14;
  MessageMetadata message_metadata = 15;
  string external_reference_id = 16;
}

// sms vendor specific details if any, currently we only have for sms
message SmsVendorData {
  string vendor_date_created = 1;
  string vendor_date_updated = 2;
  string vendor_date_sent = 3;
  string vendor_detailed_status = 4;
  string vendor_status_code = 5;
  string vendor_sms_count = 6;
  string template_id = 7;
  SmsOrigin sms_origin = 8;
}

message ActorIdentifier {
  oneof identifier {
    string user_id = 1;
    string phone = 2;
    string email = 5;
  }
}

message Communication {
  Medium medium = 1;
  // Message can be either an SMS or an Email; specified in the medium field
  oneof message {
    // Has fields that are required for an SMS
    SMSMessage sms = 2;

    // Has fields required for an Email
    EmailMessage email = 3;

    NotificationMessage notification = 4;

    WhatsappMessage whatsapp = 5;
  }

  // Reference Id used by caller services. Comms doesn't enforce uniqueness on this Id
  string external_reference_id = 6;
}

message MessageMetadata {
  string message_type = 1;
  // business use case field for better analytics
  string biz_usecase = 2;

  // field to indicate the campaign name for any communication sent out from comms
  // this could be used to group and analyse different communication sent out for a particular use case
  // for ex. onboarding reminders, card tracking updates etc.
  CampaignName campaign_name = 3;

  // field to indicate treatment id i.e to differentiate notifications within a campaign
  // we can have use case where we want to AB test different strategy in a campaign, this could be used for it
  // for ex. sending onboarding reminders after 4hrs can be one treatment and other could be sending it after 24 hrs etc
  TreatmentId treatment_id = 4;
  // account type
  string account_type = 5;
  // Campaign Id or Journey Id from Amazon pinpoint
  string pinpoint_id = 6;
  // count of the message characters.
  int32 msg_characters_count = 7;
}

// proto msg for comms retry log
message CommsRetryLog {
  // row id
  string id = 1;
  // id from comms msg table
  string comms_msg_id = 2;
  // vendor: ACL, Kaleyra, etc
  vendorgateway.Vendor vendor = 3;
  // vendor account: EPIFI, FEDERAL, etc
  // keeping this as string since this can extend to multiple medium in future
  string vendor_account = 4;
  // msg id given by vendor
  string vendor_msg_id = 5;
  // status of the msg: delivered, failed, etc
  MessageState status = 6;
  // timestamp at which this row was created in DB
  google.protobuf.Timestamp created_at = 7;
  // timestamp at which this row was last updated
  google.protobuf.Timestamp updated_at = 8;
}
