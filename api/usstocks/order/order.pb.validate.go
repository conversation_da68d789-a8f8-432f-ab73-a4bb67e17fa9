// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/usstocks/order/order.proto

package order

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	stage "github.com/epifi/be-common/api/celestial/workflow/stage"

	usstocks "github.com/epifi/gamma/api/usstocks"

	workflow "github.com/epifi/be-common/api/celestial/workflow"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = stage.Status(0)

	_ = usstocks.OrderSide(0)

	_ = workflow.Stage(0)
)

// Validate checks the field values on Order with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Order) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Order with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in OrderMultiError, or nil if none found.
func (m *Order) ValidateAll() error {
	return m.validate(true)
}

func (m *Order) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for VendorOrderId

	// no validation rules for PoolTxnId

	// no validation rules for SwiftTxnId

	// no validation rules for WfReqId

	// no validation rules for ActorId

	// no validation rules for Symbol

	// no validation rules for CatalogRefId

	// no validation rules for Side

	// no validation rules for QtyRequested

	if all {
		switch v := interface{}(m.GetAmountRequested()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "AmountRequested",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "AmountRequested",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountRequested()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "AmountRequested",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for QtyConfirmed

	if all {
		switch v := interface{}(m.GetAmountConfirmed()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "AmountConfirmed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "AmountConfirmed",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmountConfirmed()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "AmountConfirmed",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for State

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvoiceDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "InvoiceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "InvoiceDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvoiceDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "InvoiceDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ExternalOrderId

	// no validation rules for ClientOrderId

	// no validation rules for VendorAccountId

	if all {
		switch v := interface{}(m.GetPaymentInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "PaymentInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "PaymentInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPaymentInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "PaymentInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTradeInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "TradeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "TradeInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTradeInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "TradeInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FailureReason

	if all {
		switch v := interface{}(m.GetFulfilledAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "FulfilledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "FulfilledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFulfilledAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "FulfilledAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FundingType

	// no validation rules for TimeInForce

	if all {
		switch v := interface{}(m.GetLimitPrice()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "LimitPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderValidationError{
					field:  "LimitPrice",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLimitPrice()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderValidationError{
				field:  "LimitPrice",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OrderMultiError(errors)
	}

	return nil
}

// OrderMultiError is an error wrapping multiple validation errors returned by
// Order.ValidateAll() if the designated constraints aren't met.
type OrderMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderMultiError) AllErrors() []error { return m }

// OrderValidationError is the validation error returned by Order.Validate if
// the designated constraints aren't met.
type OrderValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderValidationError) ErrorName() string { return "OrderValidationError" }

// Error satisfies the builtin error interface
func (e OrderValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrder.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderValidationError{}

// Validate checks the field values on Brokerage with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Brokerage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Brokerage with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BrokerageMultiError, or nil
// if none found.
func (m *Brokerage) ValidateAll() error {
	return m.validate(true)
}

func (m *Brokerage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Brokerage.(type) {
	case *Brokerage_BrokerageAmount:
		if v == nil {
			err := BrokerageValidationError{
				field:  "Brokerage",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetBrokerageAmount()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BrokerageValidationError{
						field:  "BrokerageAmount",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BrokerageValidationError{
						field:  "BrokerageAmount",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetBrokerageAmount()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BrokerageValidationError{
					field:  "BrokerageAmount",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *Brokerage_BrokerageInPercentage:
		if v == nil {
			err := BrokerageValidationError{
				field:  "Brokerage",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for BrokerageInPercentage
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return BrokerageMultiError(errors)
	}

	return nil
}

// BrokerageMultiError is an error wrapping multiple validation errors returned
// by Brokerage.ValidateAll() if the designated constraints aren't met.
type BrokerageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BrokerageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BrokerageMultiError) AllErrors() []error { return m }

// BrokerageValidationError is the validation error returned by
// Brokerage.Validate if the designated constraints aren't met.
type BrokerageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BrokerageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BrokerageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BrokerageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BrokerageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BrokerageValidationError) ErrorName() string { return "BrokerageValidationError" }

// Error satisfies the builtin error interface
func (e BrokerageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBrokerage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BrokerageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BrokerageValidationError{}

// Validate checks the field values on PaymentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *PaymentInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PaymentInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in PaymentInfoMultiError, or
// nil if none found.
func (m *PaymentInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *PaymentInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UtrNumber

	if len(errors) > 0 {
		return PaymentInfoMultiError(errors)
	}

	return nil
}

// PaymentInfoMultiError is an error wrapping multiple validation errors
// returned by PaymentInfo.ValidateAll() if the designated constraints aren't met.
type PaymentInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PaymentInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PaymentInfoMultiError) AllErrors() []error { return m }

// PaymentInfoValidationError is the validation error returned by
// PaymentInfo.Validate if the designated constraints aren't met.
type PaymentInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PaymentInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PaymentInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PaymentInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PaymentInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PaymentInfoValidationError) ErrorName() string { return "PaymentInfoValidationError" }

// Error satisfies the builtin error interface
func (e PaymentInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPaymentInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PaymentInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PaymentInfoValidationError{}

// Validate checks the field values on TradeInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TradeInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TradeInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TradeInfoMultiError, or nil
// if none found.
func (m *TradeInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TradeInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFulfilledAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TradeInfoValidationError{
					field:  "FulfilledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TradeInfoValidationError{
					field:  "FulfilledAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFulfilledAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TradeInfoValidationError{
				field:  "FulfilledAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderType

	if len(errors) > 0 {
		return TradeInfoMultiError(errors)
	}

	return nil
}

// TradeInfoMultiError is an error wrapping multiple validation errors returned
// by TradeInfo.ValidateAll() if the designated constraints aren't met.
type TradeInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TradeInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TradeInfoMultiError) AllErrors() []error { return m }

// TradeInfoValidationError is the validation error returned by
// TradeInfo.Validate if the designated constraints aren't met.
type TradeInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TradeInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TradeInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TradeInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TradeInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TradeInfoValidationError) ErrorName() string { return "TradeInfoValidationError" }

// Error satisfies the builtin error interface
func (e TradeInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTradeInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TradeInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TradeInfoValidationError{}

// Validate checks the field values on OrderProcessingStageDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OrderProcessingStageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OrderProcessingStageDetails with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OrderProcessingStageDetailsMultiError, or nil if none found.
func (m *OrderProcessingStageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *OrderProcessingStageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Stage

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetLastUpdatedTimestamp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderProcessingStageDetailsValidationError{
					field:  "LastUpdatedTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderProcessingStageDetailsValidationError{
					field:  "LastUpdatedTimestamp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastUpdatedTimestamp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderProcessingStageDetailsValidationError{
				field:  "LastUpdatedTimestamp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OrderProcessingStageDetailsValidationError{
					field:  "Eta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OrderProcessingStageDetailsValidationError{
					field:  "Eta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OrderProcessingStageDetailsValidationError{
				field:  "Eta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return OrderProcessingStageDetailsMultiError(errors)
	}

	return nil
}

// OrderProcessingStageDetailsMultiError is an error wrapping multiple
// validation errors returned by OrderProcessingStageDetails.ValidateAll() if
// the designated constraints aren't met.
type OrderProcessingStageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OrderProcessingStageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OrderProcessingStageDetailsMultiError) AllErrors() []error { return m }

// OrderProcessingStageDetailsValidationError is the validation error returned
// by OrderProcessingStageDetails.Validate if the designated constraints
// aren't met.
type OrderProcessingStageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OrderProcessingStageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OrderProcessingStageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OrderProcessingStageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OrderProcessingStageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OrderProcessingStageDetailsValidationError) ErrorName() string {
	return "OrderProcessingStageDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e OrderProcessingStageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOrderProcessingStageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OrderProcessingStageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OrderProcessingStageDetailsValidationError{}

// Validate checks the field values on InvoiceDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *InvoiceDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InvoiceDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in InvoiceDetailsMultiError,
// or nil if none found.
func (m *InvoiceDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *InvoiceDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPartnerExchangeRate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "PartnerExchangeRate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "PartnerExchangeRate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPartnerExchangeRate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "PartnerExchangeRate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPurchaseAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "PurchaseAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "PurchaseAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPurchaseAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "PurchaseAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPurchaseAmountIn_USD()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "PurchaseAmountIn_USD",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "PurchaseAmountIn_USD",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPurchaseAmountIn_USD()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "PurchaseAmountIn_USD",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGST()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "GST",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "GST",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGST()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "GST",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTCS()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "TCS",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "TCS",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTCS()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "TCS",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalDebitAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "TotalDebitAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "TotalDebitAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalDebitAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "TotalDebitAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "Fee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "Fee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "Fee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ForexRateId

	// no validation rules for ForexRateProvenance

	if all {
		switch v := interface{}(m.GetInstantPurchaseFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "InstantPurchaseFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "InstantPurchaseFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstantPurchaseFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "InstantPurchaseFee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSellAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "SellAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "SellAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSellAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "SellAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetSellAmountIn_USD()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "SellAmountIn_USD",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "SellAmountIn_USD",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSellAmountIn_USD()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "SellAmountIn_USD",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalCreditAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "TotalCreditAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "TotalCreditAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalCreditAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "TotalCreditAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBrokerage()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "Brokerage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "Brokerage",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBrokerage()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "Brokerage",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetExpectedTradeAmountAfterChargesDeduction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "ExpectedTradeAmountAfterChargesDeduction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InvoiceDetailsValidationError{
					field:  "ExpectedTradeAmountAfterChargesDeduction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetExpectedTradeAmountAfterChargesDeduction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InvoiceDetailsValidationError{
				field:  "ExpectedTradeAmountAfterChargesDeduction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return InvoiceDetailsMultiError(errors)
	}

	return nil
}

// InvoiceDetailsMultiError is an error wrapping multiple validation errors
// returned by InvoiceDetails.ValidateAll() if the designated constraints
// aren't met.
type InvoiceDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InvoiceDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InvoiceDetailsMultiError) AllErrors() []error { return m }

// InvoiceDetailsValidationError is the validation error returned by
// InvoiceDetails.Validate if the designated constraints aren't met.
type InvoiceDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InvoiceDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InvoiceDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InvoiceDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InvoiceDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InvoiceDetailsValidationError) ErrorName() string { return "InvoiceDetailsValidationError" }

// Error satisfies the builtin error interface
func (e InvoiceDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInvoiceDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InvoiceDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InvoiceDetailsValidationError{}
