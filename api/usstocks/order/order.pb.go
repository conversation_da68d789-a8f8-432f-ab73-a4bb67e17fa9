//go:generate gen_sql -types=OrderState,InvoiceDetails,PaymentInfo,TradeInfo,FailureReason

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/usstocks/order/order.proto

package order

import (
	workflow "github.com/epifi/be-common/api/celestial/workflow"
	stage "github.com/epifi/be-common/api/celestial/workflow/stage"
	usstocks "github.com/epifi/gamma/api/usstocks"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FailureReason int32

const (
	FailureReason_FAILURE_REASON_UNSPECIFIED FailureReason = 0
	// A buy-order was not fulfilled due to amount not being transferred to Fi Federal pool account
	// This could be for several reasons like user not entering UPI PIN, wrong PIN, etc.
	FailureReason_FAILURE_REASON_ERROR_TRANSFERRING_BUY_AMOUNT_TO_POOL_ACCOUNT FailureReason = 1
	// International fund transfer of buy-order is not allowed as the order amount  will breach the
	// maximum amount allowed by RBI for international remittance for a financial year for the user
	FailureReason_FAILURE_REASON_LRS_LIMIT_BREACHED FailureReason = 2
	// Source of funds that are being transferred could not be determined
	// This could be because user has no bank account connected with Fi that is at least 6 months old, etc.
	FailureReason_FAILURE_REASON_SOF_DETERMINATION_FAILED FailureReason = 3
	// Generic error when placing order with broker
	FailureReason_FAILURE_REASON_ERROR_PLACING_ORDER_WITH_VENDOR FailureReason = 4
	// Generic error while fulfilling payment
	FailureReason_FAILURE_REASON_ERROR_FULFILLING_ORDER FailureReason = 5
	// Generic error due to unexpected IFT status
	FailureReason_FAILURE_REASON_UNEXPECTED_IFT_STATUS FailureReason = 6
	// Generic error while releasing sell lock
	FailureReason_FAILURE_REASON_ERROR_RELEASING_SELL_LOCK FailureReason = 7
	// Generic error while sending sell order to broker
	FailureReason_FAILURE_REASON_ERROR_SENDING_SELL_ORDER_TO_VENDOR FailureReason = 8
	// Generic error while tracking order status with broker
	FailureReason_FAILURE_REASON_ERROR_TRACKING_ORDER_STATUS_WITH_VENDOR FailureReason = 9
	// Signifies that signal is not received during defined wait time, possible reason could be payment not credited to user or
	// bug in signalling flow
	FailureReason_FAILURE_REASON_TIMED_OUT_WAITING_FOR_PAYMENT_RECEIVED_SIGNAL_FOR_SELL_ORDER FailureReason = 10
	// generic error encountered while waiting for payment received signal
	FailureReason_FAILURE_REASON_ERROR_WAITING_FOR_PAYMENT_RECEIVED_SIGNAL_FOR_SELL_ORDER FailureReason = 11
	// generic error encountered while waiting for payment initiated signal
	FailureReason_FAILURE_REASON_ERROR_WAITING_FOR_PAYMENT_INITIATED_SIGNAL_FOR_SELL_ORDER FailureReason = 12
	// Signifies that payment initiated signal is not received during defined wait time
	FailureReason_FAILURE_REASON_TIMED_OUT_WAITING_FOR_PAYMENT_INITIATED_SIGNAL_FOR_SELL_ORDER FailureReason = 13
	// order creation failed due to max allowed purchase limit for the day already consumed
	FailureReason_FAILURE_REASON_BREACHED_MAX_ALLOWED_PURCHASE_LIMIT_FOR_DAY FailureReason = 14
	// order creation failed due to max allowed purchase limit for the financial year already consumed
	FailureReason_FAILURE_REASON_BREACHED_MAX_ALLOWED_PURCHASE_LIMIT_FOR_FINANCIAL_YEAR FailureReason = 15
	// order creation failed as user is not a vintage Fi user
	// vintage is config driven. eg: 6 Months
	FailureReason_FAILURE_REASON_INSUFFICIENT_SAVINGS_ACCOUNT_VINTAGE FailureReason = 16
	// order creation failed as user have not performed min number of required transactions through Fi
	FailureReason_FAILURE_REASON_INSUFFICIENT_NO_OF_TRANSACTIONS FailureReason = 17
	// order creation failed as partner bank does not allow foreign remittance for the actor
	// reason could be - not a full KYC user, NRI user etc
	FailureReason_FAILURE_REASON_FOREIGN_REMITTANCE_NOT_ALLOWED FailureReason = 18
	// user blacklisted for foreign fund transfer with Fi
	FailureReason_FAILURE_REASON_USER_BLACKLISTED FailureReason = 19
	// order amount is suspected only if all the following conditions are met:
	// 1. International Transaction amount is >2 L
	// 2. International Transaction amount/ Account Balance > 80%
	// 3. International Transaction Amount/ Max(Last 5 Credit transaction Amount) > 90%
	// https://docs.google.com/document/d/1OYrGhaNFnJDY8CcDq6_XgtmRAXFoLeOBzGW22PQ4nWM/edit#bookmark=id.kqm7896u5dv
	FailureReason_FAILURE_REASON_ORDER_AMOUNT_SUSPECTED FailureReason = 20
	// Can be because of partner bank rejecting a user's order
	FailureReason_FAILURE_REASON_OUTWARD_SWIFT_TRANSFER_FAILED FailureReason = 21
	// if symbol is non fractionable but fractional order is placed
	FailureReason_FAILURE_REASON_FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL FailureReason = 22
	// if symbol is inactive for trading, so order cant be placed at exchange
	FailureReason_FAILURE_REASON_SYMBOL_INACTIVE_FOR_TRADE FailureReason = 23
	// if qty requested is more than qty has in portfolio, so sell order cant be placed at exchange
	FailureReason_FAILURE_REASON_INSUFFICIENT_SELL_QTY_REQUESTED FailureReason = 24
	// primarily impacts non-US Persons who invest in US PTP Securities
	// With effect from 1 January 2023, non-US Persons will incur a 10% withholding tax on gross proceeds from sales or trading of US PTP securities.
	// PTP symbols with no exceptions are by default blocked from being purchased.
	// but it is a configuration that can be modified as well
	// ref: https://www.irs.gov/individuals/international-taxpayers/partnership-withholding
	// PTP list: https://ibkr.info/node/4706
	FailureReason_FAILURE_REASON_SYMBOL_IS_PTP_AND_ACCOUNT_IS_NOT_CONFIGURED FailureReason = 25
	// if symbol is not found at vendor, so order cant be placed
	FailureReason_FAILURE_REASON_SYMBOL_NOT_FOUND_AT_VENDOR FailureReason = 26
	// given symbol is not active for trade
	FailureReason_FAILURE_REASON_SYMBOL_NON_TRADABLE_AT_VENDOR FailureReason = 27
	// if the user is trying to short selling and we dont support short sell
	// https://www.investopedia.com/terms/s/shortselling.asp
	FailureReason_FAILURE_REASON_SHORT_SELLING_IS_NOT_ALLOWED FailureReason = 28
	// user doesn't have enough buying power
	// example scenario: user has placed an order to withdraw all funds from their wallet, but then tries to buy stocks.
	FailureReason_FAILURE_REASON_INSUFFICIENT_BUYING_POWER FailureReason = 29
	// user has recently placed an opposite side order
	// example scenario: an opposite side order might get flagged as a potential wash trade.
	FailureReason_FAILURE_REASON_OPPOSITE_SIDE_ORDER_EXISTS FailureReason = 30
	// if order is rejected by vendor due to 'trade denied due to pattern day trading protection'
	// max limit on number of intraday trades imposed by US government
	FailureReason_FAILURE_REASON_DAY_TRADING_PROTECTION FailureReason = 31
)

// Enum value maps for FailureReason.
var (
	FailureReason_name = map[int32]string{
		0:  "FAILURE_REASON_UNSPECIFIED",
		1:  "FAILURE_REASON_ERROR_TRANSFERRING_BUY_AMOUNT_TO_POOL_ACCOUNT",
		2:  "FAILURE_REASON_LRS_LIMIT_BREACHED",
		3:  "FAILURE_REASON_SOF_DETERMINATION_FAILED",
		4:  "FAILURE_REASON_ERROR_PLACING_ORDER_WITH_VENDOR",
		5:  "FAILURE_REASON_ERROR_FULFILLING_ORDER",
		6:  "FAILURE_REASON_UNEXPECTED_IFT_STATUS",
		7:  "FAILURE_REASON_ERROR_RELEASING_SELL_LOCK",
		8:  "FAILURE_REASON_ERROR_SENDING_SELL_ORDER_TO_VENDOR",
		9:  "FAILURE_REASON_ERROR_TRACKING_ORDER_STATUS_WITH_VENDOR",
		10: "FAILURE_REASON_TIMED_OUT_WAITING_FOR_PAYMENT_RECEIVED_SIGNAL_FOR_SELL_ORDER",
		11: "FAILURE_REASON_ERROR_WAITING_FOR_PAYMENT_RECEIVED_SIGNAL_FOR_SELL_ORDER",
		12: "FAILURE_REASON_ERROR_WAITING_FOR_PAYMENT_INITIATED_SIGNAL_FOR_SELL_ORDER",
		13: "FAILURE_REASON_TIMED_OUT_WAITING_FOR_PAYMENT_INITIATED_SIGNAL_FOR_SELL_ORDER",
		14: "FAILURE_REASON_BREACHED_MAX_ALLOWED_PURCHASE_LIMIT_FOR_DAY",
		15: "FAILURE_REASON_BREACHED_MAX_ALLOWED_PURCHASE_LIMIT_FOR_FINANCIAL_YEAR",
		16: "FAILURE_REASON_INSUFFICIENT_SAVINGS_ACCOUNT_VINTAGE",
		17: "FAILURE_REASON_INSUFFICIENT_NO_OF_TRANSACTIONS",
		18: "FAILURE_REASON_FOREIGN_REMITTANCE_NOT_ALLOWED",
		19: "FAILURE_REASON_USER_BLACKLISTED",
		20: "FAILURE_REASON_ORDER_AMOUNT_SUSPECTED",
		21: "FAILURE_REASON_OUTWARD_SWIFT_TRANSFER_FAILED",
		22: "FAILURE_REASON_FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL",
		23: "FAILURE_REASON_SYMBOL_INACTIVE_FOR_TRADE",
		24: "FAILURE_REASON_INSUFFICIENT_SELL_QTY_REQUESTED",
		25: "FAILURE_REASON_SYMBOL_IS_PTP_AND_ACCOUNT_IS_NOT_CONFIGURED",
		26: "FAILURE_REASON_SYMBOL_NOT_FOUND_AT_VENDOR",
		27: "FAILURE_REASON_SYMBOL_NON_TRADABLE_AT_VENDOR",
		28: "FAILURE_REASON_SHORT_SELLING_IS_NOT_ALLOWED",
		29: "FAILURE_REASON_INSUFFICIENT_BUYING_POWER",
		30: "FAILURE_REASON_OPPOSITE_SIDE_ORDER_EXISTS",
		31: "FAILURE_REASON_DAY_TRADING_PROTECTION",
	}
	FailureReason_value = map[string]int32{
		"FAILURE_REASON_UNSPECIFIED":                                                   0,
		"FAILURE_REASON_ERROR_TRANSFERRING_BUY_AMOUNT_TO_POOL_ACCOUNT":                 1,
		"FAILURE_REASON_LRS_LIMIT_BREACHED":                                            2,
		"FAILURE_REASON_SOF_DETERMINATION_FAILED":                                      3,
		"FAILURE_REASON_ERROR_PLACING_ORDER_WITH_VENDOR":                               4,
		"FAILURE_REASON_ERROR_FULFILLING_ORDER":                                        5,
		"FAILURE_REASON_UNEXPECTED_IFT_STATUS":                                         6,
		"FAILURE_REASON_ERROR_RELEASING_SELL_LOCK":                                     7,
		"FAILURE_REASON_ERROR_SENDING_SELL_ORDER_TO_VENDOR":                            8,
		"FAILURE_REASON_ERROR_TRACKING_ORDER_STATUS_WITH_VENDOR":                       9,
		"FAILURE_REASON_TIMED_OUT_WAITING_FOR_PAYMENT_RECEIVED_SIGNAL_FOR_SELL_ORDER":  10,
		"FAILURE_REASON_ERROR_WAITING_FOR_PAYMENT_RECEIVED_SIGNAL_FOR_SELL_ORDER":      11,
		"FAILURE_REASON_ERROR_WAITING_FOR_PAYMENT_INITIATED_SIGNAL_FOR_SELL_ORDER":     12,
		"FAILURE_REASON_TIMED_OUT_WAITING_FOR_PAYMENT_INITIATED_SIGNAL_FOR_SELL_ORDER": 13,
		"FAILURE_REASON_BREACHED_MAX_ALLOWED_PURCHASE_LIMIT_FOR_DAY":                   14,
		"FAILURE_REASON_BREACHED_MAX_ALLOWED_PURCHASE_LIMIT_FOR_FINANCIAL_YEAR":        15,
		"FAILURE_REASON_INSUFFICIENT_SAVINGS_ACCOUNT_VINTAGE":                          16,
		"FAILURE_REASON_INSUFFICIENT_NO_OF_TRANSACTIONS":                               17,
		"FAILURE_REASON_FOREIGN_REMITTANCE_NOT_ALLOWED":                                18,
		"FAILURE_REASON_USER_BLACKLISTED":                                              19,
		"FAILURE_REASON_ORDER_AMOUNT_SUSPECTED":                                        20,
		"FAILURE_REASON_OUTWARD_SWIFT_TRANSFER_FAILED":                                 21,
		"FAILURE_REASON_FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL":           22,
		"FAILURE_REASON_SYMBOL_INACTIVE_FOR_TRADE":                                     23,
		"FAILURE_REASON_INSUFFICIENT_SELL_QTY_REQUESTED":                               24,
		"FAILURE_REASON_SYMBOL_IS_PTP_AND_ACCOUNT_IS_NOT_CONFIGURED":                   25,
		"FAILURE_REASON_SYMBOL_NOT_FOUND_AT_VENDOR":                                    26,
		"FAILURE_REASON_SYMBOL_NON_TRADABLE_AT_VENDOR":                                 27,
		"FAILURE_REASON_SHORT_SELLING_IS_NOT_ALLOWED":                                  28,
		"FAILURE_REASON_INSUFFICIENT_BUYING_POWER":                                     29,
		"FAILURE_REASON_OPPOSITE_SIDE_ORDER_EXISTS":                                    30,
		"FAILURE_REASON_DAY_TRADING_PROTECTION":                                        31,
	}
)

func (x FailureReason) Enum() *FailureReason {
	p := new(FailureReason)
	*p = x
	return p
}

func (x FailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_order_order_proto_enumTypes[0].Descriptor()
}

func (FailureReason) Type() protoreflect.EnumType {
	return &file_api_usstocks_order_order_proto_enumTypes[0]
}

func (x FailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FailureReason.Descriptor instead.
func (FailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{0}
}

// Note: OrderFieldMask should have same name as order field
// eg: vendor_order_id -> VENDOR_ORDER_ID
type OrderFieldMask int32

const (
	OrderFieldMask_ORDER_FIELD_MASK_UNSPECIFIED      OrderFieldMask = 0
	OrderFieldMask_ORDER_FIELD_MASK_VENDOR_ORDER_ID  OrderFieldMask = 1
	OrderFieldMask_ORDER_FIELD_MASK_POOL_TXN_ID      OrderFieldMask = 2
	OrderFieldMask_ORDER_FIELD_MASK_SWIFT_TXN_ID     OrderFieldMask = 3
	OrderFieldMask_ORDER_FIELD_MASK_QTY_CONFIRMED    OrderFieldMask = 4
	OrderFieldMask_ORDER_FIELD_MASK_AMOUNT_CONFIRMED OrderFieldMask = 5
	OrderFieldMask_ORDER_FIELD_MASK_ORDER_STATE      OrderFieldMask = 6
	OrderFieldMask_ORDER_FIELD_MASK_WF_REQ_ID        OrderFieldMask = 7
	OrderFieldMask_ORDER_FIELD_MASK_PAYMENT_INFO     OrderFieldMask = 8
	OrderFieldMask_ORDER_FIELD_MASK_TRADE_INFO       OrderFieldMask = 9
	OrderFieldMask_ORDER_FIELD_MASK_FAILURE_REASON   OrderFieldMask = 10
	// Invoice details is a JSON column, below enums corresponds to fields in the JSON
	// update query is responsible for updating/inserting the mentioned field in JSON. i.e. other fields in json is not affected
	OrderFieldMask_ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE            OrderFieldMask = 11
	OrderFieldMask_ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE_ID         OrderFieldMask = 12
	OrderFieldMask_ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE_PROVENANCE OrderFieldMask = 13
	OrderFieldMask_ORDER_FIELD_MASK_INVOICE_DETAILS_GST                   OrderFieldMask = 14
	// Payment info is a JSON column, utr_number is a field in the JSON
	OrderFieldMask_ORDER_FIELD_MASK_PAYMENT_INFO_UTR_NUMBER OrderFieldMask = 15
	// Trade info is a JSON column
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	OrderFieldMask_ORDER_FIELD_MASK_TRADE_INFO_FULFILLED_AT OrderFieldMask = 16
	// fulfilled at is the timestamp at which order was fulfilled by vendor
	OrderFieldMask_ORDER_FIELD_MASK_FULFILLED_AT OrderFieldMask = 17
	// amount (in INR) remitted from pool account of Fi to user's account
	OrderFieldMask_ORDER_FIELD_MASK_INVOICE_DETAILS_SELL_AMOUNT OrderFieldMask = 18
	// field mask for funding type column
	OrderFieldMask_ORDER_FIELD_MASK_FUNDING_TYPE OrderFieldMask = 19
	// field mask for time in force column
	OrderFieldMask_ORDER_FIELD_MASK_TIME_IN_FORCE OrderFieldMask = 20
	// field mask for limit price column
	OrderFieldMask_ORDER_FIELD_MASK_LIMIT_PRICE OrderFieldMask = 21
)

// Enum value maps for OrderFieldMask.
var (
	OrderFieldMask_name = map[int32]string{
		0:  "ORDER_FIELD_MASK_UNSPECIFIED",
		1:  "ORDER_FIELD_MASK_VENDOR_ORDER_ID",
		2:  "ORDER_FIELD_MASK_POOL_TXN_ID",
		3:  "ORDER_FIELD_MASK_SWIFT_TXN_ID",
		4:  "ORDER_FIELD_MASK_QTY_CONFIRMED",
		5:  "ORDER_FIELD_MASK_AMOUNT_CONFIRMED",
		6:  "ORDER_FIELD_MASK_ORDER_STATE",
		7:  "ORDER_FIELD_MASK_WF_REQ_ID",
		8:  "ORDER_FIELD_MASK_PAYMENT_INFO",
		9:  "ORDER_FIELD_MASK_TRADE_INFO",
		10: "ORDER_FIELD_MASK_FAILURE_REASON",
		11: "ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE",
		12: "ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE_ID",
		13: "ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE_PROVENANCE",
		14: "ORDER_FIELD_MASK_INVOICE_DETAILS_GST",
		15: "ORDER_FIELD_MASK_PAYMENT_INFO_UTR_NUMBER",
		16: "ORDER_FIELD_MASK_TRADE_INFO_FULFILLED_AT",
		17: "ORDER_FIELD_MASK_FULFILLED_AT",
		18: "ORDER_FIELD_MASK_INVOICE_DETAILS_SELL_AMOUNT",
		19: "ORDER_FIELD_MASK_FUNDING_TYPE",
		20: "ORDER_FIELD_MASK_TIME_IN_FORCE",
		21: "ORDER_FIELD_MASK_LIMIT_PRICE",
	}
	OrderFieldMask_value = map[string]int32{
		"ORDER_FIELD_MASK_UNSPECIFIED":                           0,
		"ORDER_FIELD_MASK_VENDOR_ORDER_ID":                       1,
		"ORDER_FIELD_MASK_POOL_TXN_ID":                           2,
		"ORDER_FIELD_MASK_SWIFT_TXN_ID":                          3,
		"ORDER_FIELD_MASK_QTY_CONFIRMED":                         4,
		"ORDER_FIELD_MASK_AMOUNT_CONFIRMED":                      5,
		"ORDER_FIELD_MASK_ORDER_STATE":                           6,
		"ORDER_FIELD_MASK_WF_REQ_ID":                             7,
		"ORDER_FIELD_MASK_PAYMENT_INFO":                          8,
		"ORDER_FIELD_MASK_TRADE_INFO":                            9,
		"ORDER_FIELD_MASK_FAILURE_REASON":                        10,
		"ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE":            11,
		"ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE_ID":         12,
		"ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE_PROVENANCE": 13,
		"ORDER_FIELD_MASK_INVOICE_DETAILS_GST":                   14,
		"ORDER_FIELD_MASK_PAYMENT_INFO_UTR_NUMBER":               15,
		"ORDER_FIELD_MASK_TRADE_INFO_FULFILLED_AT":               16,
		"ORDER_FIELD_MASK_FULFILLED_AT":                          17,
		"ORDER_FIELD_MASK_INVOICE_DETAILS_SELL_AMOUNT":           18,
		"ORDER_FIELD_MASK_FUNDING_TYPE":                          19,
		"ORDER_FIELD_MASK_TIME_IN_FORCE":                         20,
		"ORDER_FIELD_MASK_LIMIT_PRICE":                           21,
	}
)

func (x OrderFieldMask) Enum() *OrderFieldMask {
	p := new(OrderFieldMask)
	*p = x
	return p
}

func (x OrderFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_order_order_proto_enumTypes[1].Descriptor()
}

func (OrderFieldMask) Type() protoreflect.EnumType {
	return &file_api_usstocks_order_order_proto_enumTypes[1]
}

func (x OrderFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderFieldMask.Descriptor instead.
func (OrderFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{1}
}

type StatsFieldMask int32

const (
	StatsFieldMask_STATS_FIELD_MASK_UNSPECIFIED StatsFieldMask = 0
	// field mask for getting count of orders in INITIATED, CREATED state
	StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_ORDERS_COUNT StatsFieldMask = 1
	// field mask for getting total amount of orders in INITIATED, CREATED state
	StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_ORDERS_TOTAL_AMOUNT StatsFieldMask = 2
	// field mask for getting total count of orders for which order is not fulfillment
	StatsFieldMask_STATS_FIELD_MASK_UNFULFILLED_ORDERS_COUNT StatsFieldMask = 3
	// field mask for getting total amount of orders for which order is not fulfillment
	StatsFieldMask_STATS_FIELD_MASK_UNFULFILLED_ORDERS_TOTAL_AMOUNT StatsFieldMask = 4
	// field mask for getting available transaction limit for inward remittances
	// the available limit is calculated using max limit imposed on Inward Remittance by banking partner and already consumed transaction limit by user
	//
	// Note: multiple wallet withdraw orders may be aggregated and remitted as part of single swift transfer.
	// The limit here is applicable for a single swift transfer and NOT on a single withdraw order
	//
	// eg: Federal has imposed a limit of 4L on a single swift transfer for a user
	// assuming if a user places multiple withdraw fund orders, the sum of withdraw amount across all orders should not be more than 4L
	StatsFieldMask_STATS_FIELD_MASK_AVAILABLE_TRANSACTION_LIMIT_IN_INR_FOR_INWARD_REMITTANCE StatsFieldMask = 5
	// field mask for getting count of wallet orders in INITIATED, CREATED state
	StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_WALLET_ORDERS_COUNT StatsFieldMask = 6
	// field mask for getting amount of wallet orders in INITIATED, CREATED state
	StatsFieldMask_STATS_FIELD_MASK_IN_PROGRESS_WALLET_ORDERS_AMOUNT StatsFieldMask = 7
	// field mask for getting count of trade orders in SUCCESSFUL state
	StatsFieldMask_STATS_FIELD_MASK_SUCCESS_ORDERS_COUNT StatsFieldMask = 8
	// field mask for getting count of wallet orders in SUCCESSFUL state
	StatsFieldMask_STATS_FIELD_MASK_SUCCESS_WALLET_ORDERS_COUNT StatsFieldMask = 9
)

// Enum value maps for StatsFieldMask.
var (
	StatsFieldMask_name = map[int32]string{
		0: "STATS_FIELD_MASK_UNSPECIFIED",
		1: "STATS_FIELD_MASK_IN_PROGRESS_ORDERS_COUNT",
		2: "STATS_FIELD_MASK_IN_PROGRESS_ORDERS_TOTAL_AMOUNT",
		3: "STATS_FIELD_MASK_UNFULFILLED_ORDERS_COUNT",
		4: "STATS_FIELD_MASK_UNFULFILLED_ORDERS_TOTAL_AMOUNT",
		5: "STATS_FIELD_MASK_AVAILABLE_TRANSACTION_LIMIT_IN_INR_FOR_INWARD_REMITTANCE",
		6: "STATS_FIELD_MASK_IN_PROGRESS_WALLET_ORDERS_COUNT",
		7: "STATS_FIELD_MASK_IN_PROGRESS_WALLET_ORDERS_AMOUNT",
		8: "STATS_FIELD_MASK_SUCCESS_ORDERS_COUNT",
		9: "STATS_FIELD_MASK_SUCCESS_WALLET_ORDERS_COUNT",
	}
	StatsFieldMask_value = map[string]int32{
		"STATS_FIELD_MASK_UNSPECIFIED":                                              0,
		"STATS_FIELD_MASK_IN_PROGRESS_ORDERS_COUNT":                                 1,
		"STATS_FIELD_MASK_IN_PROGRESS_ORDERS_TOTAL_AMOUNT":                          2,
		"STATS_FIELD_MASK_UNFULFILLED_ORDERS_COUNT":                                 3,
		"STATS_FIELD_MASK_UNFULFILLED_ORDERS_TOTAL_AMOUNT":                          4,
		"STATS_FIELD_MASK_AVAILABLE_TRANSACTION_LIMIT_IN_INR_FOR_INWARD_REMITTANCE": 5,
		"STATS_FIELD_MASK_IN_PROGRESS_WALLET_ORDERS_COUNT":                          6,
		"STATS_FIELD_MASK_IN_PROGRESS_WALLET_ORDERS_AMOUNT":                         7,
		"STATS_FIELD_MASK_SUCCESS_ORDERS_COUNT":                                     8,
		"STATS_FIELD_MASK_SUCCESS_WALLET_ORDERS_COUNT":                              9,
	}
)

func (x StatsFieldMask) Enum() *StatsFieldMask {
	p := new(StatsFieldMask)
	*p = x
	return p
}

func (x StatsFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatsFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_order_order_proto_enumTypes[2].Descriptor()
}

func (StatsFieldMask) Type() protoreflect.EnumType {
	return &file_api_usstocks_order_order_proto_enumTypes[2]
}

func (x StatsFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatsFieldMask.Descriptor instead.
func (StatsFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{2}
}

type ForexRateProvenance int32

const (
	ForexRateProvenance_FOREX_RATE_PROVENANCE_UNSPECIFIED ForexRateProvenance = 0
	// forex rate is fetched from the forex_rates table which is manually uploaded by the ops team
	ForexRateProvenance_FOREX_RATE_PROVENANCE_MANUAL_PURCHASE ForexRateProvenance = 1
	// forex rate is fetched from vendor api
	ForexRateProvenance_FOREX_RATE_PROVENANCE_VENDOR_API ForexRateProvenance = 2
	// forex rate from config
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	ForexRateProvenance_FOREX_RATE_PROVENANCE_CONFIG ForexRateProvenance = 3
)

// Enum value maps for ForexRateProvenance.
var (
	ForexRateProvenance_name = map[int32]string{
		0: "FOREX_RATE_PROVENANCE_UNSPECIFIED",
		1: "FOREX_RATE_PROVENANCE_MANUAL_PURCHASE",
		2: "FOREX_RATE_PROVENANCE_VENDOR_API",
		3: "FOREX_RATE_PROVENANCE_CONFIG",
	}
	ForexRateProvenance_value = map[string]int32{
		"FOREX_RATE_PROVENANCE_UNSPECIFIED":     0,
		"FOREX_RATE_PROVENANCE_MANUAL_PURCHASE": 1,
		"FOREX_RATE_PROVENANCE_VENDOR_API":      2,
		"FOREX_RATE_PROVENANCE_CONFIG":          3,
	}
)

func (x ForexRateProvenance) Enum() *ForexRateProvenance {
	p := new(ForexRateProvenance)
	*p = x
	return p
}

func (x ForexRateProvenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ForexRateProvenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_order_order_proto_enumTypes[3].Descriptor()
}

func (ForexRateProvenance) Type() protoreflect.EnumType {
	return &file_api_usstocks_order_order_proto_enumTypes[3]
}

func (x ForexRateProvenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ForexRateProvenance.Descriptor instead.
func (ForexRateProvenance) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{3}
}

// pre-requisite checks status for us stocks order
type OrderPreReqChecksStatus int32

const (
	OrderPreReqChecksStatus_ORDER_PRE_REQ_CHECKS_STATUS_UNSPECIFIED OrderPreReqChecksStatus = 0
	// pre req checks for order is passed
	OrderPreReqChecksStatus_ORDER_PRE_REQ_CHECKS_PASSED OrderPreReqChecksStatus = 1
	// incorrect requested amount for order
	OrderPreReqChecksStatus_INCORRECT_REQUESTED_AMOUNT_FOR_ORDER OrderPreReqChecksStatus = 9
	// defined day trade limit is breached by user
	// eg: if the limit is 4 orders in 5 days, user is trying to place 5th order
	OrderPreReqChecksStatus_DAY_TRADE_LIMIT_BREACHED OrderPreReqChecksStatus = 10
	// user doesn't have enough buying power
	// example scenario: user has placed an order to withdraw all funds from their wallet, but then tries to buy stocks.
	OrderPreReqChecksStatus_INSUFFICIENT_BUYING_POWER OrderPreReqChecksStatus = 11
	// Trade amount requested by client did not match the trade amount calculated at BE
	// eg:
	// Assuming, amount entered by user: $100, Brokerage(0.25%): $0.25, Trade amount: $99.75
	// In parallel, If the config for brokerage percentage is changed at BE to 0.50%, Trade amount becomes $99.50($100-$0.50)
	//
	// Order in above scenario should be blocked by BE with relevant error code, so that the situation of disparity in charges presented
	// to user and actual charges at BE will be failed by PreReqChecksStatus TRADE_AMOUNT_MISMATCH_AFTER_CHARGES_DEDUCTION
	OrderPreReqChecksStatus_TRADE_AMOUNT_MISMATCH_AFTER_CHARGES_DEDUCTION OrderPreReqChecksStatus = 12
	// incorrect limit order amount for order
	// eg : if limit price is in INR or units are 0
	OrderPreReqChecksStatus_INVALID_LIMIT_ORDER_PRICE OrderPreReqChecksStatus = 13
	// insufficient quantity to sell
	// eg : if user has less units in portfolio than requested quantity
	OrderPreReqChecksStatus_INSUFFICIENT_QUANTITY_FOR_SELL OrderPreReqChecksStatus = 14
	// limit order can only be placed on non fractionable stock
	// and for quantity orders fractional quantity is allowed only when stock is fractional
	// status for fractional orders placed for non fractional stock
	OrderPreReqChecksStatus_FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL OrderPreReqChecksStatus = 15
	// quantity is not valid
	// ex : quantity is less than or equal to zero
	OrderPreReqChecksStatus_INVALID_QUANTITY_REQUESTED OrderPreReqChecksStatus = 16
)

// Enum value maps for OrderPreReqChecksStatus.
var (
	OrderPreReqChecksStatus_name = map[int32]string{
		0:  "ORDER_PRE_REQ_CHECKS_STATUS_UNSPECIFIED",
		1:  "ORDER_PRE_REQ_CHECKS_PASSED",
		9:  "INCORRECT_REQUESTED_AMOUNT_FOR_ORDER",
		10: "DAY_TRADE_LIMIT_BREACHED",
		11: "INSUFFICIENT_BUYING_POWER",
		12: "TRADE_AMOUNT_MISMATCH_AFTER_CHARGES_DEDUCTION",
		13: "INVALID_LIMIT_ORDER_PRICE",
		14: "INSUFFICIENT_QUANTITY_FOR_SELL",
		15: "FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL",
		16: "INVALID_QUANTITY_REQUESTED",
	}
	OrderPreReqChecksStatus_value = map[string]int32{
		"ORDER_PRE_REQ_CHECKS_STATUS_UNSPECIFIED":             0,
		"ORDER_PRE_REQ_CHECKS_PASSED":                         1,
		"INCORRECT_REQUESTED_AMOUNT_FOR_ORDER":                9,
		"DAY_TRADE_LIMIT_BREACHED":                            10,
		"INSUFFICIENT_BUYING_POWER":                           11,
		"TRADE_AMOUNT_MISMATCH_AFTER_CHARGES_DEDUCTION":       12,
		"INVALID_LIMIT_ORDER_PRICE":                           13,
		"INSUFFICIENT_QUANTITY_FOR_SELL":                      14,
		"FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL": 15,
		"INVALID_QUANTITY_REQUESTED":                          16,
	}
)

func (x OrderPreReqChecksStatus) Enum() *OrderPreReqChecksStatus {
	p := new(OrderPreReqChecksStatus)
	*p = x
	return p
}

func (x OrderPreReqChecksStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderPreReqChecksStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_order_order_proto_enumTypes[4].Descriptor()
}

func (OrderPreReqChecksStatus) Type() protoreflect.EnumType {
	return &file_api_usstocks_order_order_proto_enumTypes[4]
}

func (x OrderPreReqChecksStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderPreReqChecksStatus.Descriptor instead.
func (OrderPreReqChecksStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{4}
}

// IncomingPaymentReason defines reasons for which payment is received for user
// eg: user placed a sell order, company offered dividend to share holders
type IncomingPaymentReason int32

const (
	IncomingPaymentReason_REASON_UNSPECIFIED                       IncomingPaymentReason = 0
	IncomingPaymentReason_SELL_ORDER                               IncomingPaymentReason = 1
	IncomingPaymentReason_DIVIDEND_RECEIVED                        IncomingPaymentReason = 2
	IncomingPaymentReason_AGGREGATED_INWARD_REMITTANCE_TRANSACTION IncomingPaymentReason = 3
)

// Enum value maps for IncomingPaymentReason.
var (
	IncomingPaymentReason_name = map[int32]string{
		0: "REASON_UNSPECIFIED",
		1: "SELL_ORDER",
		2: "DIVIDEND_RECEIVED",
		3: "AGGREGATED_INWARD_REMITTANCE_TRANSACTION",
	}
	IncomingPaymentReason_value = map[string]int32{
		"REASON_UNSPECIFIED": 0,
		"SELL_ORDER":         1,
		"DIVIDEND_RECEIVED":  2,
		"AGGREGATED_INWARD_REMITTANCE_TRANSACTION": 3,
	}
)

func (x IncomingPaymentReason) Enum() *IncomingPaymentReason {
	p := new(IncomingPaymentReason)
	*p = x
	return p
}

func (x IncomingPaymentReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IncomingPaymentReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_usstocks_order_order_proto_enumTypes[5].Descriptor()
}

func (IncomingPaymentReason) Type() protoreflect.EnumType {
	return &file_api_usstocks_order_order_proto_enumTypes[5]
}

func (x IncomingPaymentReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IncomingPaymentReason.Descriptor instead.
func (IncomingPaymentReason) EnumDescriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{5}
}

// Order denotes a buy/sell order placed by user
type Order struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for order
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// OrderId of corresponding order at vendor
	VendorOrderId string `protobuf:"bytes,2,opt,name=vendor_order_id,json=vendorOrderId,proto3" json:"vendor_order_id,omitempty"`
	// transaction Id of the pool transfer for corresponding buy order
	PoolTxnId string `protobuf:"bytes,3,opt,name=pool_txn_id,json=poolTxnId,proto3" json:"pool_txn_id,omitempty"`
	// transaction Id of the swift transfer for corresponding buy order
	SwiftTxnId string `protobuf:"bytes,4,opt,name=swift_txn_id,json=swiftTxnId,proto3" json:"swift_txn_id,omitempty"`
	// corresponding workflow request Id for the order
	WfReqId string `protobuf:"bytes,5,opt,name=wf_req_id,json=wfReqId,proto3" json:"wf_req_id,omitempty"`
	// actor_id of the user
	ActorId string `protobuf:"bytes,6,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// order specific details
	// symbol/ticker for which order has been placed
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	Symbol string `protobuf:"bytes,7,opt,name=symbol,proto3" json:"symbol,omitempty"`
	// foreign key to us_stocks_catalog table for mapping symbol order is placed for
	CatalogRefId string `protobuf:"bytes,8,opt,name=catalog_ref_id,json=catalogRefId,proto3" json:"catalog_ref_id,omitempty"`
	// defines type of order placed by user
	Side usstocks.OrderSide `protobuf:"varint,9,opt,name=side,proto3,enum=usstocks.OrderSide" json:"side,omitempty"`
	// quantity of units user has requested to buy/sell
	// eg: 2.5
	QtyRequested float64 `protobuf:"fixed64,10,opt,name=qty_requested,json=qtyRequested,proto3" json:"qty_requested,omitempty"`
	// amount value that user has requested to buy/sell
	// eg: 1000 Rs
	AmountRequested *money.Money `protobuf:"bytes,11,opt,name=amount_requested,json=amountRequested,proto3" json:"amount_requested,omitempty"`
	// qty of units confirmed by vendor
	QtyConfirmed float64 `protobuf:"fixed64,12,opt,name=qty_confirmed,json=qtyConfirmed,proto3" json:"qty_confirmed,omitempty"`
	// amount value that vendor has confirmed
	// eg: 1000 Rs
	AmountConfirmed *money.Money `protobuf:"bytes,13,opt,name=amount_confirmed,json=amountConfirmed,proto3" json:"amount_confirmed,omitempty"`
	// current state of the order
	State     usstocks.OrderState    `protobuf:"varint,14,opt,name=state,proto3,enum=usstocks.OrderState" json:"state,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// InvoiceDetails holds the component wise amount breakdown
	// eg: GST, TCS, Forex rate etc
	InvoiceDetails *InvoiceDetails `protobuf:"bytes,18,opt,name=invoice_details,json=invoiceDetails,proto3" json:"invoice_details,omitempty"`
	// External order Id is an alternative which can be shared with external users.
	ExternalOrderId string `protobuf:"bytes,19,opt,name=external_order_id,json=externalOrderId,proto3" json:"external_order_id,omitempty"`
	// order Id generated by client eg: FIT
	ClientOrderId string `protobuf:"bytes,20,opt,name=client_order_id,json=clientOrderId,proto3" json:"client_order_id,omitempty"`
	// reference identifier to map an order to accounts table
	VendorAccountId string `protobuf:"bytes,21,opt,name=vendor_account_id,json=vendorAccountId,proto3" json:"vendor_account_id,omitempty"`
	// represent payment info for order
	// eg: it contains utr number for successful pool transaction
	PaymentInfo *PaymentInfo `protobuf:"bytes,22,opt,name=payment_info,json=paymentInfo,proto3" json:"payment_info,omitempty"`
	// represent trade info for order
	TradeInfo *TradeInfo `protobuf:"bytes,23,opt,name=trade_info,json=tradeInfo,proto3" json:"trade_info,omitempty"`
	// reason for a order that fails, shouldn't be populated for successful or orders that are still being processed
	FailureReason FailureReason `protobuf:"varint,24,opt,name=failure_reason,json=failureReason,proto3,enum=usstocks.order.FailureReason" json:"failure_reason,omitempty"`
	// fulfilled_at is the time at which order was processed successfully by vendor
	FulfilledAt *timestamppb.Timestamp `protobuf:"bytes,25,opt,name=fulfilled_at,json=fulfilledAt,proto3" json:"fulfilled_at,omitempty"`
	// type of funding applicable for the corresponding trade order
	FundingType usstocks.OrderFundingType `protobuf:"varint,26,opt,name=funding_type,json=fundingType,proto3,enum=usstocks.OrderFundingType" json:"funding_type,omitempty"`
	// time_in_force used when placing a trade to indicate how long an order will remain active before it is executed or expires.
	TimeInForce usstocks.TimeInForce `protobuf:"varint,27,opt,name=time_in_force,json=timeInForce,proto3,enum=usstocks.TimeInForce" json:"time_in_force,omitempty"`
	// price at which limit order to be placed at vendor
	LimitPrice *money.Money `protobuf:"bytes,28,opt,name=limit_price,json=limitPrice,proto3" json:"limit_price,omitempty"`
}

func (x *Order) Reset() {
	*x = Order{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_usstocks_order_order_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Order) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order) ProtoMessage() {}

func (x *Order) ProtoReflect() protoreflect.Message {
	mi := &file_api_usstocks_order_order_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order.ProtoReflect.Descriptor instead.
func (*Order) Descriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{0}
}

func (x *Order) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Order) GetVendorOrderId() string {
	if x != nil {
		return x.VendorOrderId
	}
	return ""
}

func (x *Order) GetPoolTxnId() string {
	if x != nil {
		return x.PoolTxnId
	}
	return ""
}

func (x *Order) GetSwiftTxnId() string {
	if x != nil {
		return x.SwiftTxnId
	}
	return ""
}

func (x *Order) GetWfReqId() string {
	if x != nil {
		return x.WfReqId
	}
	return ""
}

func (x *Order) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *Order) GetSymbol() string {
	if x != nil {
		return x.Symbol
	}
	return ""
}

func (x *Order) GetCatalogRefId() string {
	if x != nil {
		return x.CatalogRefId
	}
	return ""
}

func (x *Order) GetSide() usstocks.OrderSide {
	if x != nil {
		return x.Side
	}
	return usstocks.OrderSide(0)
}

func (x *Order) GetQtyRequested() float64 {
	if x != nil {
		return x.QtyRequested
	}
	return 0
}

func (x *Order) GetAmountRequested() *money.Money {
	if x != nil {
		return x.AmountRequested
	}
	return nil
}

func (x *Order) GetQtyConfirmed() float64 {
	if x != nil {
		return x.QtyConfirmed
	}
	return 0
}

func (x *Order) GetAmountConfirmed() *money.Money {
	if x != nil {
		return x.AmountConfirmed
	}
	return nil
}

func (x *Order) GetState() usstocks.OrderState {
	if x != nil {
		return x.State
	}
	return usstocks.OrderState(0)
}

func (x *Order) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Order) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Order) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *Order) GetInvoiceDetails() *InvoiceDetails {
	if x != nil {
		return x.InvoiceDetails
	}
	return nil
}

func (x *Order) GetExternalOrderId() string {
	if x != nil {
		return x.ExternalOrderId
	}
	return ""
}

func (x *Order) GetClientOrderId() string {
	if x != nil {
		return x.ClientOrderId
	}
	return ""
}

func (x *Order) GetVendorAccountId() string {
	if x != nil {
		return x.VendorAccountId
	}
	return ""
}

func (x *Order) GetPaymentInfo() *PaymentInfo {
	if x != nil {
		return x.PaymentInfo
	}
	return nil
}

func (x *Order) GetTradeInfo() *TradeInfo {
	if x != nil {
		return x.TradeInfo
	}
	return nil
}

func (x *Order) GetFailureReason() FailureReason {
	if x != nil {
		return x.FailureReason
	}
	return FailureReason_FAILURE_REASON_UNSPECIFIED
}

func (x *Order) GetFulfilledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FulfilledAt
	}
	return nil
}

func (x *Order) GetFundingType() usstocks.OrderFundingType {
	if x != nil {
		return x.FundingType
	}
	return usstocks.OrderFundingType(0)
}

func (x *Order) GetTimeInForce() usstocks.TimeInForce {
	if x != nil {
		return x.TimeInForce
	}
	return usstocks.TimeInForce(0)
}

func (x *Order) GetLimitPrice() *money.Money {
	if x != nil {
		return x.LimitPrice
	}
	return nil
}

type Brokerage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// brokerage could be either a flat amount or percentage of notional amount
	//
	// Types that are assignable to Brokerage:
	//
	//	*Brokerage_BrokerageAmount
	//	*Brokerage_BrokerageInPercentage
	Brokerage isBrokerage_Brokerage `protobuf_oneof:"brokerage"`
}

func (x *Brokerage) Reset() {
	*x = Brokerage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_usstocks_order_order_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Brokerage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Brokerage) ProtoMessage() {}

func (x *Brokerage) ProtoReflect() protoreflect.Message {
	mi := &file_api_usstocks_order_order_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Brokerage.ProtoReflect.Descriptor instead.
func (*Brokerage) Descriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{1}
}

func (m *Brokerage) GetBrokerage() isBrokerage_Brokerage {
	if m != nil {
		return m.Brokerage
	}
	return nil
}

func (x *Brokerage) GetBrokerageAmount() *money.Money {
	if x, ok := x.GetBrokerage().(*Brokerage_BrokerageAmount); ok {
		return x.BrokerageAmount
	}
	return nil
}

func (x *Brokerage) GetBrokerageInPercentage() float64 {
	if x, ok := x.GetBrokerage().(*Brokerage_BrokerageInPercentage); ok {
		return x.BrokerageInPercentage
	}
	return 0
}

type isBrokerage_Brokerage interface {
	isBrokerage_Brokerage()
}

type Brokerage_BrokerageAmount struct {
	// flat amount to be charged as brokerage
	BrokerageAmount *money.Money `protobuf:"bytes,27,opt,name=brokerage_amount,json=brokerageAmount,proto3,oneof"`
}

type Brokerage_BrokerageInPercentage struct {
	// percentage of the trade amount that should be charged as brokerage
	BrokerageInPercentage float64 `protobuf:"fixed64,28,opt,name=brokerage_in_percentage,json=brokerageInPercentage,proto3,oneof"`
}

func (*Brokerage_BrokerageAmount) isBrokerage_Brokerage() {}

func (*Brokerage_BrokerageInPercentage) isBrokerage_Brokerage() {}

type PaymentInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// represent utr number of successful pool transfer
	UtrNumber string `protobuf:"bytes,1,opt,name=utr_number,json=utrNumber,proto3" json:"utr_number,omitempty"`
}

func (x *PaymentInfo) Reset() {
	*x = PaymentInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_usstocks_order_order_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PaymentInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentInfo) ProtoMessage() {}

func (x *PaymentInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_usstocks_order_order_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentInfo.ProtoReflect.Descriptor instead.
func (*PaymentInfo) Descriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{2}
}

func (x *PaymentInfo) GetUtrNumber() string {
	if x != nil {
		return x.UtrNumber
	}
	return ""
}

type TradeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// fulfilled_at is the time at which order was processed successfully by vendor
	// [Deprecated] in favour of moving fulfilled_at field as part of Order object
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	FulfilledAt *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=fulfilled_at,json=fulfilledAt,proto3" json:"fulfilled_at,omitempty"`
	// type of order to be placed with vendor
	// this helps in identifying if market order to be placed should be notional/quantity/limit order
	OrderType usstocks.OrderType `protobuf:"varint,2,opt,name=order_type,json=orderType,proto3,enum=usstocks.OrderType" json:"order_type,omitempty"`
}

func (x *TradeInfo) Reset() {
	*x = TradeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_usstocks_order_order_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TradeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TradeInfo) ProtoMessage() {}

func (x *TradeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_usstocks_order_order_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TradeInfo.ProtoReflect.Descriptor instead.
func (*TradeInfo) Descriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{3}
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *TradeInfo) GetFulfilledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.FulfilledAt
	}
	return nil
}

func (x *TradeInfo) GetOrderType() usstocks.OrderType {
	if x != nil {
		return x.OrderType
	}
	return usstocks.OrderType(0)
}

type OrderProcessingStageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Stage                workflow.Stage         `protobuf:"varint,1,opt,name=stage,proto3,enum=celestial.workflow.Stage" json:"stage,omitempty"`
	Status               stage.Status           `protobuf:"varint,2,opt,name=status,proto3,enum=celestial.workflow.stage.Status" json:"status,omitempty"`
	LastUpdatedTimestamp *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=last_updated_timestamp,json=lastUpdatedTimestamp,proto3" json:"last_updated_timestamp,omitempty"`
	// eta for stage completion
	// nil if stage has already transitioned to terminal state
	// Note: not all stages require ETAs, only stages which are expected to take longer duration to complete are expected to show ETA
	// eta will be only populated for few eligible stages
	Eta *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=eta,proto3" json:"eta,omitempty"`
}

func (x *OrderProcessingStageDetails) Reset() {
	*x = OrderProcessingStageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_usstocks_order_order_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderProcessingStageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderProcessingStageDetails) ProtoMessage() {}

func (x *OrderProcessingStageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_usstocks_order_order_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderProcessingStageDetails.ProtoReflect.Descriptor instead.
func (*OrderProcessingStageDetails) Descriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{4}
}

func (x *OrderProcessingStageDetails) GetStage() workflow.Stage {
	if x != nil {
		return x.Stage
	}
	return workflow.Stage(0)
}

func (x *OrderProcessingStageDetails) GetStatus() stage.Status {
	if x != nil {
		return x.Status
	}
	return stage.Status(0)
}

func (x *OrderProcessingStageDetails) GetLastUpdatedTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUpdatedTimestamp
	}
	return nil
}

func (x *OrderProcessingStageDetails) GetEta() *timestamppb.Timestamp {
	if x != nil {
		return x.Eta
	}
	return nil
}

type InvoiceDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// exchange rate decided for the transaction by partner bank
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	PartnerExchangeRate *money.Money `protobuf:"bytes,1,opt,name=partner_exchange_rate,json=partnerExchangeRate,proto3" json:"partner_exchange_rate,omitempty"`
	// Purchase amount for stock in INR
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	PurchaseAmount *money.Money `protobuf:"bytes,3,opt,name=purchase_amount,json=purchaseAmount,proto3" json:"purchase_amount,omitempty"`
	// Purchase amount for stock in USD
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	PurchaseAmountIn_USD *money.Money `protobuf:"bytes,4,opt,name=purchase_amount_in_USD,json=purchaseAmountInUSD,proto3" json:"purchase_amount_in_USD,omitempty"`
	// GST(tax) for the foreign fund transfer
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	GST *money.Money `protobuf:"bytes,5,opt,name=GST,proto3" json:"GST,omitempty"`
	// TCS(tax) for the foreign fund transfer, this is the amount charged by govt
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	TCS *money.Money `protobuf:"bytes,6,opt,name=TCS,proto3" json:"TCS,omitempty"`
	// total amount is taxes plus amount to be transferred amount
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	TotalDebitAmount *money.Money `protobuf:"bytes,7,opt,name=total_debit_amount,json=totalDebitAmount,proto3" json:"total_debit_amount,omitempty"`
	// fee that is charged for creating order
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	Fee *money.Money `protobuf:"bytes,8,opt,name=fee,proto3" json:"fee,omitempty"`
	// if forex rate is from DB, then it'll be populated
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	ForexRateId string `protobuf:"bytes,9,opt,name=forex_rate_id,json=forexRateId,proto3" json:"forex_rate_id,omitempty"`
	// source from where forex rate is fetched
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	ForexRateProvenance ForexRateProvenance `protobuf:"varint,10,opt,name=forex_rate_provenance,json=forexRateProvenance,proto3,enum=usstocks.order.ForexRateProvenance" json:"forex_rate_provenance,omitempty"`
	// represent instant purchase fee
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	InstantPurchaseFee *money.Money `protobuf:"bytes,11,opt,name=instant_purchase_fee,json=instantPurchaseFee,proto3" json:"instant_purchase_fee,omitempty"`
	// represent sell amount user has opted in inr shown to user
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	SellAmount *money.Money `protobuf:"bytes,12,opt,name=sell_amount,json=sellAmount,proto3" json:"sell_amount,omitempty"`
	// represent sell amount user has opted by user
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	SellAmountIn_USD *money.Money `protobuf:"bytes,13,opt,name=sell_amount_in_USD,json=sellAmountInUSD,proto3" json:"sell_amount_in_USD,omitempty"`
	// represent total credit amount show to user
	// total_credit_amount = sell_amount - GST [or any other charges]
	//
	// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
	TotalCreditAmount *money.Money `protobuf:"bytes,14,opt,name=total_credit_amount,json=totalCreditAmount,proto3" json:"total_credit_amount,omitempty"`
	// Brokerage commission to be deducted for the order
	Brokerage *Brokerage `protobuf:"bytes,15,opt,name=brokerage,proto3" json:"brokerage,omitempty"`
	// expected_trade_amount_after_charges_deduction is the amount for which the actual trade is expected to happen
	// consider user entered $100, charges applicable includes 0.25% brokerage i.e. $0.25
	// so the expected_trade_amount_after_charges_deduction = $99.75
	// expected_trade_amount_after_charges_deduction = amount_entered_by_user - charges(eg: brokerage)
	//
	// value for expected_trade_amount_after_charges_deduction is received from client and validated at BE as part of pre-req check
	ExpectedTradeAmountAfterChargesDeduction *money.Money `protobuf:"bytes,16,opt,name=expected_trade_amount_after_charges_deduction,json=expectedTradeAmountAfterChargesDeduction,proto3" json:"expected_trade_amount_after_charges_deduction,omitempty"`
}

func (x *InvoiceDetails) Reset() {
	*x = InvoiceDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_usstocks_order_order_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InvoiceDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InvoiceDetails) ProtoMessage() {}

func (x *InvoiceDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_usstocks_order_order_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InvoiceDetails.ProtoReflect.Descriptor instead.
func (*InvoiceDetails) Descriptor() ([]byte, []int) {
	return file_api_usstocks_order_order_proto_rawDescGZIP(), []int{5}
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetPartnerExchangeRate() *money.Money {
	if x != nil {
		return x.PartnerExchangeRate
	}
	return nil
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetPurchaseAmount() *money.Money {
	if x != nil {
		return x.PurchaseAmount
	}
	return nil
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetPurchaseAmountIn_USD() *money.Money {
	if x != nil {
		return x.PurchaseAmountIn_USD
	}
	return nil
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetGST() *money.Money {
	if x != nil {
		return x.GST
	}
	return nil
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetTCS() *money.Money {
	if x != nil {
		return x.TCS
	}
	return nil
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetTotalDebitAmount() *money.Money {
	if x != nil {
		return x.TotalDebitAmount
	}
	return nil
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetFee() *money.Money {
	if x != nil {
		return x.Fee
	}
	return nil
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetForexRateId() string {
	if x != nil {
		return x.ForexRateId
	}
	return ""
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetForexRateProvenance() ForexRateProvenance {
	if x != nil {
		return x.ForexRateProvenance
	}
	return ForexRateProvenance_FOREX_RATE_PROVENANCE_UNSPECIFIED
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetInstantPurchaseFee() *money.Money {
	if x != nil {
		return x.InstantPurchaseFee
	}
	return nil
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetSellAmount() *money.Money {
	if x != nil {
		return x.SellAmount
	}
	return nil
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetSellAmountIn_USD() *money.Money {
	if x != nil {
		return x.SellAmountIn_USD
	}
	return nil
}

// Deprecated: Marked as deprecated in api/usstocks/order/order.proto.
func (x *InvoiceDetails) GetTotalCreditAmount() *money.Money {
	if x != nil {
		return x.TotalCreditAmount
	}
	return nil
}

func (x *InvoiceDetails) GetBrokerage() *Brokerage {
	if x != nil {
		return x.Brokerage
	}
	return nil
}

func (x *InvoiceDetails) GetExpectedTradeAmountAfterChargesDeduction() *money.Money {
	if x != nil {
		return x.ExpectedTradeAmountAfterChargesDeduction
	}
	return nil
}

var File_api_usstocks_order_order_proto protoreflect.FileDescriptor

var file_api_usstocks_order_order_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x0e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2f,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74,
	0x69, 0x61, 0x6c, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2f, 0x73, 0x74, 0x61,
	0x67, 0x65, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xbf, 0x0a, 0x0a, 0x05, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x26, 0x0a,
	0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x74, 0x78,
	0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6f, 0x6f, 0x6c,
	0x54, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x0c, 0x73, 0x77, 0x69, 0x66, 0x74, 0x5f, 0x74,
	0x78, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x77, 0x69,
	0x66, 0x74, 0x54, 0x78, 0x6e, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x09, 0x77, 0x66, 0x5f, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x77, 0x66, 0x52, 0x65,
	0x71, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x06, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x52, 0x65, 0x66, 0x49, 0x64,
	0x12, 0x27, 0x0a, 0x04, 0x73, 0x69, 0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x69, 0x64, 0x65, 0x52, 0x04, 0x73, 0x69, 0x64, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x71, 0x74, 0x79,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x0c, 0x71, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x3d,
	0x0a, 0x10, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x65, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x71, 0x74, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x71, 0x74, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x65, 0x64, 0x12, 0x3d, 0x0a, 0x10, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65,
	0x64, 0x12, 0x2a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x14, 0x2e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x47,
	0x0a, 0x0f, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x49, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0e, 0x69, 0x6e, 0x76, 0x6f, 0x69, 0x63, 0x65,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x0c, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0b, 0x70, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a, 0x0a, 0x74, 0x72, 0x61, 0x64, 0x65,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x75, 0x73,
	0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x74, 0x72, 0x61, 0x64, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x44, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x75, 0x73, 0x73, 0x74,
	0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x46, 0x61, 0x69, 0x6c, 0x75,
	0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0c, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3d, 0x0a, 0x0c, 0x66, 0x75, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x75,
	0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x75, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x66, 0x75, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0d, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e,
	0x5f, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x75,
	0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x46, 0x6f,
	0x72, 0x63, 0x65, 0x52, 0x0b, 0x74, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x46, 0x6f, 0x72, 0x63, 0x65,
	0x12, 0x33, 0x0a, 0x0b, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x09, 0x42, 0x72, 0x6f, 0x6b, 0x65, 0x72,
	0x61, 0x67, 0x65, 0x12, 0x3f, 0x0a, 0x10, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x61, 0x67, 0x65,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x48, 0x00, 0x52, 0x0f, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x61, 0x67, 0x65, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x17, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x61, 0x67,
	0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x01, 0x48, 0x00, 0x52, 0x15, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x42, 0x0b,
	0x0a, 0x09, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x61, 0x67, 0x65, 0x22, 0x2c, 0x0a, 0x0b, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x74,
	0x72, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x75, 0x74, 0x72, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0x82, 0x01, 0x0a, 0x09, 0x54, 0x72,
	0x61, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x41, 0x0a, 0x0c, 0x66, 0x75, 0x6c, 0x66, 0x69,
	0x6c, 0x6c, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x66,
	0x75, 0x6c, 0x66, 0x69, 0x6c, 0x6c, 0x65, 0x64, 0x41, 0x74, 0x12, 0x32, 0x0a, 0x0a, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x22, 0x88,
	0x02, 0x0a, 0x1b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69,
	0x6e, 0x67, 0x53, 0x74, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x2f,
	0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e,
	0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c,
	0x6f, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x67, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x20, 0x2e, 0x63, 0x65, 0x6c, 0x65, 0x73, 0x74, 0x69, 0x61, 0x6c, 0x2e, 0x77, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x73, 0x74, 0x61, 0x67, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x50, 0x0a, 0x16, 0x6c, 0x61, 0x73,
	0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x14, 0x6c, 0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x2c, 0x0a, 0x03, 0x65,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x65, 0x74, 0x61, 0x22, 0xf1, 0x07, 0x0a, 0x0e, 0x49, 0x6e,
	0x76, 0x6f, 0x69, 0x63, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4a, 0x0a, 0x15,
	0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x72, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42,
	0x02, 0x18, 0x01, 0x52, 0x13, 0x70, 0x61, 0x72, 0x74, 0x6e, 0x65, 0x72, 0x45, 0x78, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x52, 0x61, 0x74, 0x65, 0x12, 0x3f, 0x0a, 0x0f, 0x70, 0x75, 0x72, 0x63,
	0x68, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0e, 0x70, 0x75, 0x72, 0x63, 0x68,
	0x61, 0x73, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x4b, 0x0a, 0x16, 0x70, 0x75, 0x72,
	0x63, 0x68, 0x61, 0x73, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x5f,
	0x55, 0x53, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x13, 0x70, 0x75, 0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x49, 0x6e, 0x55, 0x53, 0x44, 0x12, 0x28, 0x0a, 0x03, 0x47, 0x53, 0x54, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x03, 0x47, 0x53, 0x54,
	0x12, 0x28, 0x0a, 0x03, 0x54, 0x43, 0x53, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x03, 0x54, 0x43, 0x53, 0x12, 0x44, 0x0a, 0x12, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x62, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x65, 0x62, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x28, 0x0a, 0x03, 0x66, 0x65, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x26, 0x0a, 0x0d, 0x66, 0x6f,
	0x72, 0x65, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0b, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x52, 0x61, 0x74, 0x65,
	0x49, 0x64, 0x12, 0x5b, 0x0a, 0x15, 0x66, 0x6f, 0x72, 0x65, 0x78, 0x5f, 0x72, 0x61, 0x74, 0x65,
	0x5f, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x23, 0x2e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x2e, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x52, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x76,
	0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x42, 0x02, 0x18, 0x01, 0x52, 0x13, 0x66, 0x6f, 0x72, 0x65,
	0x78, 0x52, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12,
	0x48, 0x0a, 0x14, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x70, 0x75, 0x72, 0x63, 0x68,
	0x61, 0x73, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x50, 0x75,
	0x72, 0x63, 0x68, 0x61, 0x73, 0x65, 0x46, 0x65, 0x65, 0x12, 0x37, 0x0a, 0x0b, 0x73, 0x65, 0x6c,
	0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x73, 0x65, 0x6c, 0x6c, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x43, 0x0a, 0x12, 0x73, 0x65, 0x6c, 0x6c, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x69, 0x6e, 0x5f, 0x55, 0x53, 0x44, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0f, 0x73, 0x65, 0x6c, 0x6c, 0x41, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x49, 0x6e, 0x55, 0x53, 0x44, 0x12, 0x46, 0x0a, 0x13, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x63, 0x72, 0x65, 0x64, 0x69, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x43, 0x72, 0x65, 0x64, 0x69, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x37, 0x0a, 0x09, 0x62, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x61, 0x67, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x2e, 0x42, 0x72, 0x6f, 0x6b, 0x65, 0x72, 0x61, 0x67, 0x65, 0x52, 0x09, 0x62,
	0x72, 0x6f, 0x6b, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x73, 0x0a, 0x2d, 0x65, 0x78, 0x70, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x61, 0x66, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f,
	0x64, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x28, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x72, 0x61,
	0x64, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x41, 0x66, 0x74, 0x65, 0x72, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x73, 0x44, 0x65, 0x64, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2a, 0xde, 0x0d,
	0x0a, 0x0d, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x1e, 0x0a, 0x1a, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x40, 0x0a, 0x3c, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52,
	0x52, 0x49, 0x4e, 0x47, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f,
	0x54, 0x4f, 0x5f, 0x50, 0x4f, 0x4f, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x01, 0x12, 0x25, 0x0a, 0x21, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x4c, 0x52, 0x53, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x42, 0x52,
	0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x10, 0x02, 0x12, 0x2b, 0x0a, 0x27, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x4f, 0x46, 0x5f, 0x44,
	0x45, 0x54, 0x45, 0x52, 0x4d, 0x49, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x32, 0x0a, 0x2e, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x50, 0x4c,
	0x41, 0x43, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x57, 0x49, 0x54, 0x48,
	0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x29, 0x0a, 0x25, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x5f, 0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x10, 0x05, 0x12, 0x28, 0x0a, 0x24, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x45, 0x58, 0x50, 0x45, 0x43, 0x54, 0x45,
	0x44, 0x5f, 0x49, 0x46, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x06, 0x12, 0x2c,
	0x0a, 0x28, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x52, 0x45, 0x4c, 0x45, 0x41, 0x53, 0x49, 0x4e, 0x47,
	0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x07, 0x12, 0x35, 0x0a, 0x31,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x45,
	0x52, 0x52, 0x4f, 0x52, 0x5f, 0x53, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x45, 0x4c,
	0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f,
	0x52, 0x10, 0x08, 0x12, 0x3a, 0x0a, 0x36, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x43,
	0x4b, 0x49, 0x4e, 0x47, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x57, 0x49, 0x54, 0x48, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x09, 0x12,
	0x4f, 0x0a, 0x4b, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f,
	0x4e, 0x5f, 0x54, 0x49, 0x4d, 0x45, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x57, 0x41, 0x49, 0x54,
	0x49, 0x4e, 0x47, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f,
	0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x0a,
	0x12, 0x4b, 0x0a, 0x47, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x43,
	0x45, 0x49, 0x56, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x0b, 0x12, 0x4c, 0x0a,
	0x48, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41,
	0x54, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x53,
	0x45, 0x4c, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x0c, 0x12, 0x50, 0x0a, 0x4c, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x44, 0x5f, 0x4f, 0x55, 0x54, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f,
	0x46, 0x4f, 0x52, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x49, 0x54,
	0x49, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x4c, 0x5f, 0x46, 0x4f, 0x52,
	0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x0d, 0x12, 0x3e, 0x0a,
	0x3a, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x42, 0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x41, 0x4c, 0x4c,
	0x4f, 0x57, 0x45, 0x44, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x4c, 0x49,
	0x4d, 0x49, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x44, 0x41, 0x59, 0x10, 0x0e, 0x12, 0x49, 0x0a,
	0x45, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x42, 0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x4d, 0x41, 0x58, 0x5f, 0x41, 0x4c, 0x4c,
	0x4f, 0x57, 0x45, 0x44, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x4c, 0x49,
	0x4d, 0x49, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x49, 0x41,
	0x4c, 0x5f, 0x59, 0x45, 0x41, 0x52, 0x10, 0x0f, 0x12, 0x37, 0x0a, 0x33, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x46,
	0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f,
	0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x56, 0x49, 0x4e, 0x54, 0x41, 0x47, 0x45, 0x10,
	0x10, 0x12, 0x32, 0x0a, 0x2e, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x53, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54,
	0x5f, 0x4e, 0x4f, 0x5f, 0x4f, 0x46, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49,
	0x4f, 0x4e, 0x53, 0x10, 0x11, 0x12, 0x31, 0x0a, 0x2d, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x49, 0x47, 0x4e, 0x5f,
	0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41,
	0x4c, 0x4c, 0x4f, 0x57, 0x45, 0x44, 0x10, 0x12, 0x12, 0x23, 0x0a, 0x1f, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x42, 0x4c, 0x41, 0x43, 0x4b, 0x4c, 0x49, 0x53, 0x54, 0x45, 0x44, 0x10, 0x13, 0x12, 0x29, 0x0a,
	0x25, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x53,
	0x50, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x14, 0x12, 0x30, 0x0a, 0x2c, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4f, 0x55, 0x54, 0x57, 0x41,
	0x52, 0x44, 0x5f, 0x53, 0x57, 0x49, 0x46, 0x54, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45,
	0x52, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x15, 0x12, 0x46, 0x0a, 0x42, 0x46, 0x41,
	0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x46, 0x52, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x4c,
	0x41, 0x43, 0x45, 0x44, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x46, 0x52, 0x41,
	0x43, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x53, 0x59, 0x4d, 0x42, 0x4f, 0x4c,
	0x10, 0x16, 0x12, 0x2c, 0x0a, 0x28, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45,
	0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x4d, 0x42, 0x4f, 0x4c, 0x5f, 0x49, 0x4e, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x45, 0x10, 0x17,
	0x12, 0x32, 0x0a, 0x2e, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f,
	0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x51, 0x54, 0x59, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54,
	0x45, 0x44, 0x10, 0x18, 0x12, 0x3e, 0x0a, 0x3a, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x4d, 0x42, 0x4f, 0x4c, 0x5f, 0x49, 0x53,
	0x5f, 0x50, 0x54, 0x50, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x5f, 0x49, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x47, 0x55, 0x52,
	0x45, 0x44, 0x10, 0x19, 0x12, 0x2d, 0x0a, 0x29, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x4d, 0x42, 0x4f, 0x4c, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f,
	0x52, 0x10, 0x1a, 0x12, 0x30, 0x0a, 0x2c, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x59, 0x4d, 0x42, 0x4f, 0x4c, 0x5f, 0x4e, 0x4f, 0x4e,
	0x5f, 0x54, 0x52, 0x41, 0x44, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x41, 0x54, 0x5f, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x10, 0x1b, 0x12, 0x2f, 0x0a, 0x2b, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45,
	0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x53, 0x48, 0x4f, 0x52, 0x54, 0x5f, 0x53, 0x45,
	0x4c, 0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x41, 0x4c, 0x4c,
	0x4f, 0x57, 0x45, 0x44, 0x10, 0x1c, 0x12, 0x2c, 0x0a, 0x28, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x46, 0x46, 0x49,
	0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x55, 0x59, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x4f, 0x57,
	0x45, 0x52, 0x10, 0x1d, 0x12, 0x2d, 0x0a, 0x29, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f,
	0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x4f, 0x50, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x45, 0x5f,
	0x53, 0x49, 0x44, 0x45, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54,
	0x53, 0x10, 0x1e, 0x12, 0x29, 0x0a, 0x25, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x44, 0x41, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x1f, 0x2a, 0xfc,
	0x06, 0x0a, 0x0e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x24, 0x0a, 0x20, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x50, 0x4f,
	0x4f, 0x4c, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x21, 0x0a, 0x1d, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x53, 0x57, 0x49, 0x46, 0x54, 0x5f, 0x54, 0x58, 0x4e, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x22,
	0x0a, 0x1e, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x51, 0x54, 0x59, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45, 0x44,
	0x10, 0x04, 0x12, 0x25, 0x0a, 0x21, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4f,
	0x4e, 0x46, 0x49, 0x52, 0x4d, 0x45, 0x44, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x52,
	0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x10, 0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x57, 0x46, 0x5f, 0x52, 0x45, 0x51, 0x5f, 0x49, 0x44, 0x10, 0x07, 0x12, 0x21, 0x0a, 0x1d, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x08, 0x12, 0x1f,
	0x0a, 0x1b, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x09, 0x12,
	0x23, 0x0a, 0x1f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53,
	0x4f, 0x4e, 0x10, 0x0a, 0x12, 0x2f, 0x0a, 0x2b, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43, 0x45,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x52,
	0x41, 0x54, 0x45, 0x10, 0x0b, 0x12, 0x32, 0x0a, 0x2e, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43,
	0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f,
	0x52, 0x41, 0x54, 0x45, 0x5f, 0x49, 0x44, 0x10, 0x0c, 0x12, 0x3a, 0x0a, 0x36, 0x4f, 0x52, 0x44,
	0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e,
	0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x46, 0x4f,
	0x52, 0x45, 0x58, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41,
	0x4e, 0x43, 0x45, 0x10, 0x0d, 0x12, 0x28, 0x0a, 0x24, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43,
	0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x47, 0x53, 0x54, 0x10, 0x0e, 0x12,
	0x2c, 0x0a, 0x28, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x46, 0x4f,
	0x5f, 0x55, 0x54, 0x52, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x0f, 0x12, 0x30, 0x0a,
	0x28, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x5f, 0x46, 0x55, 0x4c,
	0x46, 0x49, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x10, 0x1a, 0x02, 0x08, 0x01, 0x12,
	0x21, 0x0a, 0x1d, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x41, 0x54,
	0x10, 0x11, 0x12, 0x30, 0x0a, 0x2c, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x56, 0x4f, 0x49, 0x43, 0x45, 0x5f, 0x44,
	0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x41, 0x4d, 0x4f, 0x55,
	0x4e, 0x54, 0x10, 0x12, 0x12, 0x21, 0x0a, 0x1d, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x49, 0x4e, 0x47,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x13, 0x12, 0x22, 0x0a, 0x1e, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x54, 0x49, 0x4d, 0x45,
	0x5f, 0x49, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x43, 0x45, 0x10, 0x14, 0x12, 0x20, 0x0a, 0x1c, 0x4f,
	0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x15, 0x2a, 0x95, 0x04,
	0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b,
	0x12, 0x20, 0x0a, 0x1c, 0x53, 0x54, 0x41, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x54, 0x41, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45,
	0x53, 0x53, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x01, 0x12, 0x34, 0x0a, 0x30, 0x53, 0x54, 0x41, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53,
	0x53, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x53, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x41,
	0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x54, 0x41, 0x54, 0x53,
	0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x46, 0x55,
	0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x53, 0x5f, 0x43,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0x03, 0x12, 0x34, 0x0a, 0x30, 0x53, 0x54, 0x41, 0x54, 0x53, 0x5f,
	0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x46, 0x55, 0x4c,
	0x46, 0x49, 0x4c, 0x4c, 0x45, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x53, 0x5f, 0x54, 0x4f,
	0x54, 0x41, 0x4c, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x4d, 0x0a, 0x49,
	0x53, 0x54, 0x41, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f, 0x49, 0x4e, 0x5f,
	0x49, 0x4e, 0x52, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52,
	0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x05, 0x12, 0x34, 0x0a, 0x30, 0x53,
	0x54, 0x41, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f,
	0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x57, 0x41, 0x4c, 0x4c,
	0x45, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10,
	0x06, 0x12, 0x35, 0x0a, 0x31, 0x53, 0x54, 0x41, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53,
	0x53, 0x5f, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x53, 0x5f,
	0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x07, 0x12, 0x29, 0x0a, 0x25, 0x53, 0x54, 0x41, 0x54,
	0x53, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x43,
	0x43, 0x45, 0x53, 0x53, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x08, 0x12, 0x30, 0x0a, 0x2c, 0x53, 0x54, 0x41, 0x54, 0x53, 0x5f, 0x46, 0x49, 0x45,
	0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f,
	0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x53, 0x5f, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x10, 0x09, 0x2a, 0xb3, 0x01, 0x0a, 0x13, 0x46, 0x6f, 0x72, 0x65, 0x78, 0x52,
	0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x25, 0x0a,
	0x21, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x56,
	0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x52, 0x41,
	0x54, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x4d, 0x41,
	0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x10, 0x01, 0x12,
	0x24, 0x0a, 0x20, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x52,
	0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f,
	0x41, 0x50, 0x49, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x1c, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x52,
	0x41, 0x54, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x43,
	0x4f, 0x4e, 0x46, 0x49, 0x47, 0x10, 0x03, 0x1a, 0x02, 0x08, 0x01, 0x2a, 0xc7, 0x03, 0x0a, 0x17,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x65, 0x52, 0x65, 0x71, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x27, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x50, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x53, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x52,
	0x45, 0x5f, 0x52, 0x45, 0x51, 0x5f, 0x43, 0x48, 0x45, 0x43, 0x4b, 0x53, 0x5f, 0x50, 0x41, 0x53,
	0x53, 0x45, 0x44, 0x10, 0x01, 0x12, 0x28, 0x0a, 0x24, 0x49, 0x4e, 0x43, 0x4f, 0x52, 0x52, 0x45,
	0x43, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x4d, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x09, 0x12,
	0x1c, 0x0a, 0x18, 0x44, 0x41, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x4c, 0x49, 0x4d,
	0x49, 0x54, 0x5f, 0x42, 0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x1d, 0x0a,
	0x19, 0x49, 0x4e, 0x53, 0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x55,
	0x59, 0x49, 0x4e, 0x47, 0x5f, 0x50, 0x4f, 0x57, 0x45, 0x52, 0x10, 0x0b, 0x12, 0x31, 0x0a, 0x2d,
	0x54, 0x52, 0x41, 0x44, 0x45, 0x5f, 0x41, 0x4d, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4d, 0x49, 0x53,
	0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x41, 0x46, 0x54, 0x45, 0x52, 0x5f, 0x43, 0x48, 0x41, 0x52,
	0x47, 0x45, 0x53, 0x5f, 0x44, 0x45, 0x44, 0x55, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0c, 0x12,
	0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x52, 0x49, 0x43, 0x45, 0x10, 0x0d, 0x12, 0x22,
	0x0a, 0x1e, 0x49, 0x4e, 0x53, 0x55, 0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x51,
	0x55, 0x41, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x53, 0x45, 0x4c, 0x4c,
	0x10, 0x0e, 0x12, 0x37, 0x0a, 0x33, 0x46, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c,
	0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x44, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x4e, 0x4f, 0x4e, 0x5f, 0x46, 0x52, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x42,
	0x4c, 0x45, 0x5f, 0x53, 0x59, 0x4d, 0x42, 0x4f, 0x4c, 0x10, 0x0f, 0x12, 0x1e, 0x0a, 0x1a, 0x49,
	0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x51, 0x55, 0x41, 0x4e, 0x54, 0x49, 0x54, 0x59, 0x5f,
	0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x45, 0x44, 0x10, 0x10, 0x22, 0x04, 0x08, 0x02, 0x10,
	0x02, 0x22, 0x04, 0x08, 0x03, 0x10, 0x03, 0x22, 0x04, 0x08, 0x04, 0x10, 0x04, 0x22, 0x04, 0x08,
	0x05, 0x10, 0x05, 0x22, 0x04, 0x08, 0x06, 0x10, 0x06, 0x22, 0x04, 0x08, 0x07, 0x10, 0x07, 0x22,
	0x04, 0x08, 0x08, 0x10, 0x08, 0x2a, 0x84, 0x01, 0x0a, 0x15, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x69,
	0x6e, 0x67, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x16, 0x0a, 0x12, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x45, 0x4c, 0x4c, 0x5f,
	0x4f, 0x52, 0x44, 0x45, 0x52, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x49, 0x56, 0x49, 0x44,
	0x45, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x43, 0x45, 0x49, 0x56, 0x45, 0x44, 0x10, 0x02, 0x12, 0x2c,
	0x0a, 0x28, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x57,
	0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x54,
	0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x42, 0x56, 0x0a, 0x29,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x73, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5a, 0x29, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_usstocks_order_order_proto_rawDescOnce sync.Once
	file_api_usstocks_order_order_proto_rawDescData = file_api_usstocks_order_order_proto_rawDesc
)

func file_api_usstocks_order_order_proto_rawDescGZIP() []byte {
	file_api_usstocks_order_order_proto_rawDescOnce.Do(func() {
		file_api_usstocks_order_order_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_usstocks_order_order_proto_rawDescData)
	})
	return file_api_usstocks_order_order_proto_rawDescData
}

var file_api_usstocks_order_order_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_api_usstocks_order_order_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_usstocks_order_order_proto_goTypes = []interface{}{
	(FailureReason)(0),                  // 0: usstocks.order.FailureReason
	(OrderFieldMask)(0),                 // 1: usstocks.order.OrderFieldMask
	(StatsFieldMask)(0),                 // 2: usstocks.order.StatsFieldMask
	(ForexRateProvenance)(0),            // 3: usstocks.order.ForexRateProvenance
	(OrderPreReqChecksStatus)(0),        // 4: usstocks.order.OrderPreReqChecksStatus
	(IncomingPaymentReason)(0),          // 5: usstocks.order.IncomingPaymentReason
	(*Order)(nil),                       // 6: usstocks.order.Order
	(*Brokerage)(nil),                   // 7: usstocks.order.Brokerage
	(*PaymentInfo)(nil),                 // 8: usstocks.order.PaymentInfo
	(*TradeInfo)(nil),                   // 9: usstocks.order.TradeInfo
	(*OrderProcessingStageDetails)(nil), // 10: usstocks.order.OrderProcessingStageDetails
	(*InvoiceDetails)(nil),              // 11: usstocks.order.InvoiceDetails
	(usstocks.OrderSide)(0),             // 12: usstocks.OrderSide
	(*money.Money)(nil),                 // 13: google.type.Money
	(usstocks.OrderState)(0),            // 14: usstocks.OrderState
	(*timestamppb.Timestamp)(nil),       // 15: google.protobuf.Timestamp
	(usstocks.OrderFundingType)(0),      // 16: usstocks.OrderFundingType
	(usstocks.TimeInForce)(0),           // 17: usstocks.TimeInForce
	(usstocks.OrderType)(0),             // 18: usstocks.OrderType
	(workflow.Stage)(0),                 // 19: celestial.workflow.Stage
	(stage.Status)(0),                   // 20: celestial.workflow.stage.Status
}
var file_api_usstocks_order_order_proto_depIdxs = []int32{
	12, // 0: usstocks.order.Order.side:type_name -> usstocks.OrderSide
	13, // 1: usstocks.order.Order.amount_requested:type_name -> google.type.Money
	13, // 2: usstocks.order.Order.amount_confirmed:type_name -> google.type.Money
	14, // 3: usstocks.order.Order.state:type_name -> usstocks.OrderState
	15, // 4: usstocks.order.Order.created_at:type_name -> google.protobuf.Timestamp
	15, // 5: usstocks.order.Order.updated_at:type_name -> google.protobuf.Timestamp
	15, // 6: usstocks.order.Order.deleted_at:type_name -> google.protobuf.Timestamp
	11, // 7: usstocks.order.Order.invoice_details:type_name -> usstocks.order.InvoiceDetails
	8,  // 8: usstocks.order.Order.payment_info:type_name -> usstocks.order.PaymentInfo
	9,  // 9: usstocks.order.Order.trade_info:type_name -> usstocks.order.TradeInfo
	0,  // 10: usstocks.order.Order.failure_reason:type_name -> usstocks.order.FailureReason
	15, // 11: usstocks.order.Order.fulfilled_at:type_name -> google.protobuf.Timestamp
	16, // 12: usstocks.order.Order.funding_type:type_name -> usstocks.OrderFundingType
	17, // 13: usstocks.order.Order.time_in_force:type_name -> usstocks.TimeInForce
	13, // 14: usstocks.order.Order.limit_price:type_name -> google.type.Money
	13, // 15: usstocks.order.Brokerage.brokerage_amount:type_name -> google.type.Money
	15, // 16: usstocks.order.TradeInfo.fulfilled_at:type_name -> google.protobuf.Timestamp
	18, // 17: usstocks.order.TradeInfo.order_type:type_name -> usstocks.OrderType
	19, // 18: usstocks.order.OrderProcessingStageDetails.stage:type_name -> celestial.workflow.Stage
	20, // 19: usstocks.order.OrderProcessingStageDetails.status:type_name -> celestial.workflow.stage.Status
	15, // 20: usstocks.order.OrderProcessingStageDetails.last_updated_timestamp:type_name -> google.protobuf.Timestamp
	15, // 21: usstocks.order.OrderProcessingStageDetails.eta:type_name -> google.protobuf.Timestamp
	13, // 22: usstocks.order.InvoiceDetails.partner_exchange_rate:type_name -> google.type.Money
	13, // 23: usstocks.order.InvoiceDetails.purchase_amount:type_name -> google.type.Money
	13, // 24: usstocks.order.InvoiceDetails.purchase_amount_in_USD:type_name -> google.type.Money
	13, // 25: usstocks.order.InvoiceDetails.GST:type_name -> google.type.Money
	13, // 26: usstocks.order.InvoiceDetails.TCS:type_name -> google.type.Money
	13, // 27: usstocks.order.InvoiceDetails.total_debit_amount:type_name -> google.type.Money
	13, // 28: usstocks.order.InvoiceDetails.fee:type_name -> google.type.Money
	3,  // 29: usstocks.order.InvoiceDetails.forex_rate_provenance:type_name -> usstocks.order.ForexRateProvenance
	13, // 30: usstocks.order.InvoiceDetails.instant_purchase_fee:type_name -> google.type.Money
	13, // 31: usstocks.order.InvoiceDetails.sell_amount:type_name -> google.type.Money
	13, // 32: usstocks.order.InvoiceDetails.sell_amount_in_USD:type_name -> google.type.Money
	13, // 33: usstocks.order.InvoiceDetails.total_credit_amount:type_name -> google.type.Money
	7,  // 34: usstocks.order.InvoiceDetails.brokerage:type_name -> usstocks.order.Brokerage
	13, // 35: usstocks.order.InvoiceDetails.expected_trade_amount_after_charges_deduction:type_name -> google.type.Money
	36, // [36:36] is the sub-list for method output_type
	36, // [36:36] is the sub-list for method input_type
	36, // [36:36] is the sub-list for extension type_name
	36, // [36:36] is the sub-list for extension extendee
	0,  // [0:36] is the sub-list for field type_name
}

func init() { file_api_usstocks_order_order_proto_init() }
func file_api_usstocks_order_order_proto_init() {
	if File_api_usstocks_order_order_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_usstocks_order_order_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Order); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_usstocks_order_order_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Brokerage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_usstocks_order_order_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PaymentInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_usstocks_order_order_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TradeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_usstocks_order_order_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderProcessingStageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_usstocks_order_order_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InvoiceDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_usstocks_order_order_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Brokerage_BrokerageAmount)(nil),
		(*Brokerage_BrokerageInPercentage)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_usstocks_order_order_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_usstocks_order_order_proto_goTypes,
		DependencyIndexes: file_api_usstocks_order_order_proto_depIdxs,
		EnumInfos:         file_api_usstocks_order_order_proto_enumTypes,
		MessageInfos:      file_api_usstocks_order_order_proto_msgTypes,
	}.Build()
	File_api_usstocks_order_order_proto = out.File
	file_api_usstocks_order_order_proto_rawDesc = nil
	file_api_usstocks_order_order_proto_goTypes = nil
	file_api_usstocks_order_order_proto_depIdxs = nil
}
