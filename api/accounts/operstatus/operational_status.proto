//go:generate gen_sql -types=OperationalStatusVendorResponse,KYCCompliance
syntax = "proto3";

package accounts.operstatus;

import "api/accounts/account_type.proto";
import "api/accounts/enums/freeze_status.proto";
import "api/accounts/enums/operational_status.proto";
import "api/vendorgateway/openbanking/accounts/status/enquiry.proto";
import "api/vendorgateway/vendor.proto";
import "api/vendornotification/openbanking/accounts/federal/events.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/accounts/operstatus";
option java_package = "com.github.epifi.gamma.api.accounts.operstatus";

// OperationalStatusInfo is the data model that stores attributes that can affect the normal functioning of a bank account.
message OperationalStatusInfo {
  // bank account type ex. savings, fixed deposit
  accounts.Type account_type = 1 [(validate.rules).enum = {not_in: [0]}];

  // id of given account, will have different pattern for different account type
  // ex. savings - SV123..., deposit - DP123... etc. Foreign key to entity of a particular account type.
  string account_id = 2 [(validate.rules).string.min_len = 1];

  // partner bank with whom account belongs
  vendorgateway.Vendor partner_bank = 3 [(validate.rules).enum = {in: [1]}];

  // account open date in indian time zone
  google.type.Date account_opened_at = 4;
  // account close date in indian time zone
  google.type.Date account_closed_at = 5;

  // signifies if account is active, inactive etc.
  enums.OperationalStatus operational_status = 6;

  // tells what type of freeze is imposed on account if any
  enums.FreezeStatus freeze_status = 7;

  // total_lien_marking is the amount of money that is blocked by the bank on this account,
  // which can not be access by the account right now
  google.type.Money total_lien_marking = 9;

  // KYCCompliance stores the dates associated with Re KYC.
  KYCCompliance k_y_c_compliance_info = 14;

  // vendor response stores the response object that was used to decide the states
  OperationalStatusVendorResponse vendor_response = 10;

  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  int64 deleted_at_unix = 13;
}

message OperationalStatusVendorResponse {
  oneof vendor_response {
    vendorgateway.openbanking.accounts.status.AccountStatusEnquiryResponse federal_account_status_enquiry_response = 1;
    vendornotification.openbanking.accounts.federal.AccountStatusCallBackData federal_account_status_call_back_data = 2;
  }
}

message KYCCompliance {
  // KYCDueDate is when the KYC was reviewed/done.
  google.type.Date k_y_c_review_date = 1 [json_name = "kycReviewDate"];
  // KYCDueDate is when the KYC process is due next.
  google.type.Date k_y_c_due_date = 2 [json_name = "kycDueDate"];
  // KYCGracePeriodDate is when KYC is already due but no action is taken until grace period.
  google.type.Date k_y_c_grace_period_date = 3 [json_name = "kycGracePeriodDate"];
}
