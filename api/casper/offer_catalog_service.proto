syntax = "proto3";

package casper;

import "api/casper/offer_catalog.proto";
import "api/casper/tag.proto";
import "api/dynamic_elements/dynamic_elements.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/casper";
option java_package = "com.github.epifi.gamma.api.casper";

// contains methods for offers
service OfferCatalogService {
  // will be used to create the offers
  rpc createOffer (CreateOfferRequest) returns (CreateOfferResponse) {}
  // will be used to update the offer display
  rpc updateOfferDisplay (UpdateOfferDisplayRequest) returns (UpdateOfferDisplayResponse) {}
  // todo (utkarsh) : remove this api, use getBulkOfferDetailsByIds instead
  // for the details of an offer by id
  rpc getOfferDetailsById (GetOfferDetailsByIdRequest) returns (GetOfferDetailsByIdResponse) {}
  // bulk api of offer details by id
  rpc getBulkOfferDetailsByIds (GetBulkOfferDetailsByIdsRequest) returns (GetBulkOfferDetailsByIdsResponse) {}
  // to delete an offer by id
  rpc deleteOfferById (DeleteOfferByIdRequest) returns (DeleteOfferByIdResponse) {}
  // FetchDynamicElements will be invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse) {};
  // DynamicElementCallback will be processes callback received on user action on any of the dynamic elements
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse) {};
  // GetExternalVendorDynamicWebpageUrl will return webpage url specific to vendor with any custom logic for the given user.
  rpc GetExternalVendorDynamicWebpageUrl(GetExternalVendorDynamicWebpageUrlRequest) returns (GetExternalVendorDynamicWebpageUrlResponse) {};
}

message CreateOfferRequest {
  // name of the offer shown on the main offer page
  string name = 1;
  // description of the offer shown on the main offer page
  string desc = 2;
  // price of the offer in decimals
  // not attaching any units: like currency  to it as it will vary based on OfferRedemptionMode
  // for: FI_COINS: it will represent fi coins needed to redeem the offer
  // for: FI_CARD: it will represent INR needed to redeem the offer
  float price = 3;
  // type of offer namely gift card, subscription etc
  OfferType offer_type = 4;
  // list of imaged attached to this offer
  repeated OfferImage images = 6;
  // offer tnc
  OfferTnc tnc = 7;
  // mode though which offer is redeemable FI_COINS/FI_CARD etc
  OfferRedemptionMode redemption_mode = 8;
  // name of the vendor though which offer is redeemable
  OfferVendor vendor_name = 9;
  // stores vendor specific metadata of offer
  VendorOfferMetadata vendor_offer_metadata = 11;
  // stores metadata for offer.
  // for eg for EGV offers it stores the gift card value.
  OfferMetadata offer_metadata = 14;
  // additional details
  OfferAdditionalDetails offer_additional_details = 15;
  // manually applied tags
  repeated TagName manual_tags = 16;
  // category tag of the offer
  CategoryTag category_tag = 17;
  // sub category tag of the offer
  SubCategoryTag sub_category_tag = 18;
}

message CreateOfferResponse {
  // rpc response status
  rpc.Status status = 1;
  // generated offer id
  string offer_id = 2;
  repeated ValidationFailureInfo validation_failure_infos = 3;
  message ValidationFailureInfo {
    // failure message for validation failure for create Offer request
    string failure_message = 1;
  }
}

message UpdateOfferDisplayRequest {
  // id of offer which is to be updated
  string offer_id = 1;
  // updated offer name
  string name = 2;
  // updated offer description
  string desc = 3;
  // updated offer images
  repeated OfferImage images = 4;
  // updated offer tnc
  OfferTnc tnc = 5;
  // updated additional details
  OfferAdditionalDetails offer_additional_details = 6;
  // updated list of tags applied to the offer
  repeated TagName new_manual_tags = 7;
  // updated category tag of the offer
  CategoryTag category_tag = 8;
  // updated sub category tag of the offer
  SubCategoryTag sub_category_tag = 9;
}

message UpdateOfferDisplayResponse {
  // rpc response status
  rpc.Status status = 1;
}

message GetOfferDetailsByIdRequest {
  // offer id which was generated by backed
  string offer_id = 1;
}

message GetOfferDetailsByIdResponse {
  // rpc response status
  rpc.Status status = 1;
  // offer details
  Offer offer = 2;
}

message GetBulkOfferDetailsByIdsRequest {
  //todo(utkarsh) : restrict max number of offerIds
  // ids of offers which need to be fetched
  repeated string offer_ids = 1;
  // filter on mode though which offer is redeemable FI_COINS/FI_CARD etc
  // UNSPECIFIED implies that redemption_mode filter shouldn't be applied.
  OfferRedemptionMode redemption_mode = 8;
  // for some offers we need to pull data from vendor in realtime like for getting vendor_product_id in case of offer redemption
  // or for showing only those products in our catalog which are available at vendor end. Alternately there can be cases where we
  // don't require updated vendor offer details like if the offer was already redeemed, then for fetching the offer for display
  // on the redeemed offers section we need not fetch realtime vendor offer details.
  bool with_updated_vendor_offer_details = 9;
}

message GetBulkOfferDetailsByIdsResponse {
  // rpc response status
  rpc.Status status = 1;
  // list of offers
  repeated Offer offers = 2;
}

message DeleteOfferByIdRequest {
  // offer id which was generated by backed
  string offer_id = 1;
}

message DeleteOfferByIdResponse {
  // rpc response status
  rpc.Status status = 1;
}

message GetExternalVendorDynamicWebpageUrlRequest {
  string actor_id = 1;
  // name of the external vendor though which dynamic webpage url should be returned.
  OfferVendor vendor_name = 2;
  // if exists, target url of the redirection page
  string target_url = 3;
}

message GetExternalVendorDynamicWebpageUrlResponse {
  // rpc response status
  rpc.Status status = 1;
  // Url for the page content
  string webpage_url = 2;
}
