syntax = "proto3";

package casper.exchanger;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/casper/exchanger";
option java_package = "com.github.epifi.gamma.api.casper.exchanger";

// ExchangerOfferOrderFulfillmentRequest is created each time an ExchangerOfferOrder needs to be fulfilled.
// Multiple ExchangerOfferOrderFulfillmentRequest entries can be created given the previous ones led to terminal failure.
message ExchangerOfferOrderFulfillmentRequest {
  string id = 1;

  // actor who created an exchangerOfferOrder
  string actor_id = 2;

  // id of exchangerOfferOrder
  string exchanger_offer_order_id = 3;

  // processing_ref_id is same as id in cases where we are generating
  // the unique id and passing to the third party,
  // whereas in case of third party, this belongs unique id
  // generated in the 1st API call which is then passed to 2nd API call.
  string processing_ref_id = 4;

  // current state of orderFulfillment
  ExchangerOfferOrderFulfillmentRequestState state = 5;

  // standard timestamp fields
  // intentional gap in field number to accommodate new fields
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
  google.protobuf.Timestamp deleted_at = 12;
}

// ExchangerOfferOrderFulfillmentRequestState denotes the state of fulfillment request
enum ExchangerOfferOrderFulfillmentRequestState {
  FULFILLMENT_REQUEST_STATE_UNSPECIFIED = 0;
  FULFILLMENT_REQUEST_STATE_SUCCESS = 1;
  FULFILLMENT_REQUEST_STATE_IN_PROGRESS = 2;
  FULFILLMENT_REQUEST_STATE_FAILURE = 3;
}

enum ExchangerOfferOrderFulfillmentRequestFieldMask {
  FULFILLMENT_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  FULFILLMENT_REQUEST_FIELD_MASK_STATE = 1;
  FULFILLMENT_REQUEST_FIELD_MASK_PROCESSING_REF_ID = 2;
}
