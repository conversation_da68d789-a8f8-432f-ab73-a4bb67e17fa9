// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/casper/redemption/offline_redemption.proto

package redemption

import (
	casper "github.com/epifi/gamma/api/casper"
	postaladdress "google.golang.org/genproto/googleapis/type/postaladdress"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateOfflineRedemptionReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RefId string `protobuf:"bytes,1,opt,name=ref_id,json=refId,proto3" json:"ref_id,omitempty"`
	// actor who requested the redemption
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// vendor from whom some product is to be redeemed offline.
	VendorName casper.OfferVendor `protobuf:"varint,3,opt,name=vendor_name,json=vendorName,proto3,enum=casper.OfferVendor" json:"vendor_name,omitempty"`
	// type of offer redeemed
	OfferType casper.OfferType           `protobuf:"varint,4,opt,name=offer_type,json=offerType,proto3,enum=casper.OfferType" json:"offer_type,omitempty"`
	Metadata  *OfflineRedemptionMetadata `protobuf:"bytes,6,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// source of request
	RequestSource casper.RequestSource `protobuf:"varint,7,opt,name=request_source,json=requestSource,proto3,enum=casper.RequestSource" json:"request_source,omitempty"`
}

func (x *CreateOfflineRedemptionReq) Reset() {
	*x = CreateOfflineRedemptionReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_redemption_offline_redemption_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateOfflineRedemptionReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOfflineRedemptionReq) ProtoMessage() {}

func (x *CreateOfflineRedemptionReq) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_redemption_offline_redemption_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOfflineRedemptionReq.ProtoReflect.Descriptor instead.
func (*CreateOfflineRedemptionReq) Descriptor() ([]byte, []int) {
	return file_api_casper_redemption_offline_redemption_proto_rawDescGZIP(), []int{0}
}

func (x *CreateOfflineRedemptionReq) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *CreateOfflineRedemptionReq) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *CreateOfflineRedemptionReq) GetVendorName() casper.OfferVendor {
	if x != nil {
		return x.VendorName
	}
	return casper.OfferVendor(0)
}

func (x *CreateOfflineRedemptionReq) GetOfferType() casper.OfferType {
	if x != nil {
		return x.OfferType
	}
	return casper.OfferType(0)
}

func (x *CreateOfflineRedemptionReq) GetMetadata() *OfflineRedemptionMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateOfflineRedemptionReq) GetRequestSource() casper.RequestSource {
	if x != nil {
		return x.RequestSource
	}
	return casper.RequestSource(0)
}

type OfflineRedemption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id    string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	RefId string `protobuf:"bytes,2,opt,name=refId,proto3" json:"refId,omitempty"`
	// actor who performed the redemption
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// vendor from offer who be redeemed offline.
	VendorName casper.OfferVendor `protobuf:"varint,4,opt,name=vendor_name,json=vendorName,proto3,enum=casper.OfferVendor" json:"vendor_name,omitempty"`
	// type of offer redeemed
	OfferType casper.OfferType           `protobuf:"varint,5,opt,name=offer_type,json=offerType,proto3,enum=casper.OfferType" json:"offer_type,omitempty"`
	Metadata  *OfflineRedemptionMetadata `protobuf:"bytes,6,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// time at offline redemption entry was created in system.
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// source of request
	RequestSource casper.RequestSource `protobuf:"varint,7,opt,name=request_source,json=requestSource,proto3,enum=casper.RequestSource" json:"request_source,omitempty"`
}

func (x *OfflineRedemption) Reset() {
	*x = OfflineRedemption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_redemption_offline_redemption_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfflineRedemption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineRedemption) ProtoMessage() {}

func (x *OfflineRedemption) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_redemption_offline_redemption_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineRedemption.ProtoReflect.Descriptor instead.
func (*OfflineRedemption) Descriptor() ([]byte, []int) {
	return file_api_casper_redemption_offline_redemption_proto_rawDescGZIP(), []int{1}
}

func (x *OfflineRedemption) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *OfflineRedemption) GetRefId() string {
	if x != nil {
		return x.RefId
	}
	return ""
}

func (x *OfflineRedemption) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *OfflineRedemption) GetVendorName() casper.OfferVendor {
	if x != nil {
		return x.VendorName
	}
	return casper.OfferVendor(0)
}

func (x *OfflineRedemption) GetOfferType() casper.OfferType {
	if x != nil {
		return x.OfferType
	}
	return casper.OfferType(0)
}

func (x *OfflineRedemption) GetMetadata() *OfflineRedemptionMetadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *OfflineRedemption) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *OfflineRedemption) GetRequestSource() casper.RequestSource {
	if x != nil {
		return x.RequestSource
	}
	return casper.RequestSource(0)
}

type OfflineRedemptionMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// includes product identifiers used to identify a vendor product.
	VendorProductMetadata *OfflineRedemptionMetadata_VendorProductMetadata `protobuf:"bytes,1,opt,name=vendor_product_metadata,json=vendorProductMetadata,proto3" json:"vendor_product_metadata,omitempty"`
	// shipping address of actor
	ShippingAddress *postaladdress.PostalAddress `protobuf:"bytes,2,opt,name=shipping_address,json=shippingAddress,proto3" json:"shipping_address,omitempty"`
}

func (x *OfflineRedemptionMetadata) Reset() {
	*x = OfflineRedemptionMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_redemption_offline_redemption_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfflineRedemptionMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineRedemptionMetadata) ProtoMessage() {}

func (x *OfflineRedemptionMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_redemption_offline_redemption_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineRedemptionMetadata.ProtoReflect.Descriptor instead.
func (*OfflineRedemptionMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_redemption_offline_redemption_proto_rawDescGZIP(), []int{2}
}

func (x *OfflineRedemptionMetadata) GetVendorProductMetadata() *OfflineRedemptionMetadata_VendorProductMetadata {
	if x != nil {
		return x.VendorProductMetadata
	}
	return nil
}

func (x *OfflineRedemptionMetadata) GetShippingAddress() *postaladdress.PostalAddress {
	if x != nil {
		return x.ShippingAddress
	}
	return nil
}

type OfflineRedemptionMetadata_VendorProductMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProductId string `protobuf:"bytes,1,opt,name=product_id,json=productId,proto3" json:"product_id,omitempty"`
}

func (x *OfflineRedemptionMetadata_VendorProductMetadata) Reset() {
	*x = OfflineRedemptionMetadata_VendorProductMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_casper_redemption_offline_redemption_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfflineRedemptionMetadata_VendorProductMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfflineRedemptionMetadata_VendorProductMetadata) ProtoMessage() {}

func (x *OfflineRedemptionMetadata_VendorProductMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_api_casper_redemption_offline_redemption_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfflineRedemptionMetadata_VendorProductMetadata.ProtoReflect.Descriptor instead.
func (*OfflineRedemptionMetadata_VendorProductMetadata) Descriptor() ([]byte, []int) {
	return file_api_casper_redemption_offline_redemption_proto_rawDescGZIP(), []int{2, 0}
}

func (x *OfflineRedemptionMetadata_VendorProductMetadata) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

var File_api_casper_redemption_offline_redemption_proto protoreflect.FileDescriptor

var file_api_casper_redemption_offline_redemption_proto_rawDesc = []byte{
	0x0a, 0x2e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x64,
	0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x6f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x06, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x74, 0x61, 0x6c,
	0x6f, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x61,
	0x73, 0x70, 0x65, 0x72, 0x2f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb3, 0x02, 0x0a, 0x1a, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x15, 0x0a, 0x06, 0x72, 0x65, 0x66,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0b, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x13, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x56,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x0a, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x30, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f,
	0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f,
	0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x12, 0x3c, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x73,
	0x70, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0xf4, 0x02, 0x0a, 0x11, 0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x64, 0x65,
	0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x66, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x0b, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x63,
	0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x52, 0x0a, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a,
	0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x11, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x3d, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x4f, 0x66, 0x66, 0x6c, 0x69,
	0x6e, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x3c, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x8b, 0x02, 0x0a, 0x19, 0x4f, 0x66, 0x66, 0x6c,
	0x69, 0x6e, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x65, 0x74,
	0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x6f, 0x0a, 0x17, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e,
	0x4f, 0x66, 0x66, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x52,
	0x15, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x10, 0x73, 0x68, 0x69, 0x70, 0x70, 0x69,
	0x6e, 0x67, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x50,
	0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x0f, 0x73, 0x68,
	0x69, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x1a, 0x36, 0x0a,
	0x15, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x64,
	0x75, 0x63, 0x74, 0x49, 0x64, 0x42, 0x5c, 0x0a, 0x2c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2e, 0x72, 0x65, 0x64, 0x65, 0x6d,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5a, 0x2c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x61, 0x73, 0x70, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x6d, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_casper_redemption_offline_redemption_proto_rawDescOnce sync.Once
	file_api_casper_redemption_offline_redemption_proto_rawDescData = file_api_casper_redemption_offline_redemption_proto_rawDesc
)

func file_api_casper_redemption_offline_redemption_proto_rawDescGZIP() []byte {
	file_api_casper_redemption_offline_redemption_proto_rawDescOnce.Do(func() {
		file_api_casper_redemption_offline_redemption_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_casper_redemption_offline_redemption_proto_rawDescData)
	})
	return file_api_casper_redemption_offline_redemption_proto_rawDescData
}

var file_api_casper_redemption_offline_redemption_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_casper_redemption_offline_redemption_proto_goTypes = []interface{}{
	(*CreateOfflineRedemptionReq)(nil),                      // 0: casper.CreateOfflineRedemptionReq
	(*OfflineRedemption)(nil),                               // 1: casper.OfflineRedemption
	(*OfflineRedemptionMetadata)(nil),                       // 2: casper.OfflineRedemptionMetadata
	(*OfflineRedemptionMetadata_VendorProductMetadata)(nil), // 3: casper.OfflineRedemptionMetadata.VendorProductMetadata
	(casper.OfferVendor)(0),                                 // 4: casper.OfferVendor
	(casper.OfferType)(0),                                   // 5: casper.OfferType
	(casper.RequestSource)(0),                               // 6: casper.RequestSource
	(*timestamppb.Timestamp)(nil),                           // 7: google.protobuf.Timestamp
	(*postaladdress.PostalAddress)(nil),                     // 8: google.type.PostalAddress
}
var file_api_casper_redemption_offline_redemption_proto_depIdxs = []int32{
	4,  // 0: casper.CreateOfflineRedemptionReq.vendor_name:type_name -> casper.OfferVendor
	5,  // 1: casper.CreateOfflineRedemptionReq.offer_type:type_name -> casper.OfferType
	2,  // 2: casper.CreateOfflineRedemptionReq.metadata:type_name -> casper.OfflineRedemptionMetadata
	6,  // 3: casper.CreateOfflineRedemptionReq.request_source:type_name -> casper.RequestSource
	4,  // 4: casper.OfflineRedemption.vendor_name:type_name -> casper.OfferVendor
	5,  // 5: casper.OfflineRedemption.offer_type:type_name -> casper.OfferType
	2,  // 6: casper.OfflineRedemption.metadata:type_name -> casper.OfflineRedemptionMetadata
	7,  // 7: casper.OfflineRedemption.created_at:type_name -> google.protobuf.Timestamp
	6,  // 8: casper.OfflineRedemption.request_source:type_name -> casper.RequestSource
	3,  // 9: casper.OfflineRedemptionMetadata.vendor_product_metadata:type_name -> casper.OfflineRedemptionMetadata.VendorProductMetadata
	8,  // 10: casper.OfflineRedemptionMetadata.shipping_address:type_name -> google.type.PostalAddress
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_casper_redemption_offline_redemption_proto_init() }
func file_api_casper_redemption_offline_redemption_proto_init() {
	if File_api_casper_redemption_offline_redemption_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_casper_redemption_offline_redemption_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateOfflineRedemptionReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_redemption_offline_redemption_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfflineRedemption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_redemption_offline_redemption_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfflineRedemptionMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_casper_redemption_offline_redemption_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfflineRedemptionMetadata_VendorProductMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_casper_redemption_offline_redemption_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_casper_redemption_offline_redemption_proto_goTypes,
		DependencyIndexes: file_api_casper_redemption_offline_redemption_proto_depIdxs,
		MessageInfos:      file_api_casper_redemption_offline_redemption_proto_msgTypes,
	}.Build()
	File_api_casper_redemption_offline_redemption_proto = out.File
	file_api_casper_redemption_offline_redemption_proto_rawDesc = nil
	file_api_casper_redemption_offline_redemption_proto_goTypes = nil
	file_api_casper_redemption_offline_redemption_proto_depIdxs = nil
}
