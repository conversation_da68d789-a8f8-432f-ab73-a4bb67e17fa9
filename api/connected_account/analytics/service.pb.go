// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/connected_account/analytics/service.proto

package analytics

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InitiateAnalysisResponse_Status int32

const (
	InitiateAnalysisResponse_OK                                                 InitiateAnalysisResponse_Status = 0
	InitiateAnalysisResponse_ANOTHER_ANALYSIS_FOR_ACTOR_ALREADY_IN_PROGRESS     InitiateAnalysisResponse_Status = 101
	InitiateAnalysisResponse_AA_DATA_PULL_IN_PROGRESS_FOR_ACTOR                 InitiateAnalysisResponse_Status = 102
	InitiateAnalysisResponse_NO_NEW_DATA_FOR_ANALYSIS_FOR_ANY_ACCOUNT_FOR_ACTOR InitiateAnalysisResponse_Status = 103
)

// Enum value maps for InitiateAnalysisResponse_Status.
var (
	InitiateAnalysisResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "ANOTHER_ANALYSIS_FOR_ACTOR_ALREADY_IN_PROGRESS",
		102: "AA_DATA_PULL_IN_PROGRESS_FOR_ACTOR",
		103: "NO_NEW_DATA_FOR_ANALYSIS_FOR_ANY_ACCOUNT_FOR_ACTOR",
	}
	InitiateAnalysisResponse_Status_value = map[string]int32{
		"OK": 0,
		"ANOTHER_ANALYSIS_FOR_ACTOR_ALREADY_IN_PROGRESS":     101,
		"AA_DATA_PULL_IN_PROGRESS_FOR_ACTOR":                 102,
		"NO_NEW_DATA_FOR_ANALYSIS_FOR_ANY_ACCOUNT_FOR_ACTOR": 103,
	}
)

func (x InitiateAnalysisResponse_Status) Enum() *InitiateAnalysisResponse_Status {
	p := new(InitiateAnalysisResponse_Status)
	*p = x
	return p
}

func (x InitiateAnalysisResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitiateAnalysisResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_connected_account_analytics_service_proto_enumTypes[0].Descriptor()
}

func (InitiateAnalysisResponse_Status) Type() protoreflect.EnumType {
	return &file_api_connected_account_analytics_service_proto_enumTypes[0]
}

func (x InitiateAnalysisResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitiateAnalysisResponse_Status.Descriptor instead.
func (InitiateAnalysisResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{1, 0}
}

// Enum for the status of the analysis process
type GetAnalysisStatusResponse_AnalysisStatus int32

const (
	GetAnalysisStatusResponse_ANALYSIS_STATUS_UNSPECIFIED GetAnalysisStatusResponse_AnalysisStatus = 0
	// Analysis is pending or in progress
	GetAnalysisStatusResponse_ANALYSIS_STATUS_PENDING GetAnalysisStatusResponse_AnalysisStatus = 1
	// Analysis is completed successfully
	GetAnalysisStatusResponse_ANALYSIS_STATUS_SUCCESS GetAnalysisStatusResponse_AnalysisStatus = 2
	// Analysis failed
	GetAnalysisStatusResponse_ANALYSIS_STATUS_FAILED GetAnalysisStatusResponse_AnalysisStatus = 3
)

// Enum value maps for GetAnalysisStatusResponse_AnalysisStatus.
var (
	GetAnalysisStatusResponse_AnalysisStatus_name = map[int32]string{
		0: "ANALYSIS_STATUS_UNSPECIFIED",
		1: "ANALYSIS_STATUS_PENDING",
		2: "ANALYSIS_STATUS_SUCCESS",
		3: "ANALYSIS_STATUS_FAILED",
	}
	GetAnalysisStatusResponse_AnalysisStatus_value = map[string]int32{
		"ANALYSIS_STATUS_UNSPECIFIED": 0,
		"ANALYSIS_STATUS_PENDING":     1,
		"ANALYSIS_STATUS_SUCCESS":     2,
		"ANALYSIS_STATUS_FAILED":      3,
	}
)

func (x GetAnalysisStatusResponse_AnalysisStatus) Enum() *GetAnalysisStatusResponse_AnalysisStatus {
	p := new(GetAnalysisStatusResponse_AnalysisStatus)
	*p = x
	return p
}

func (x GetAnalysisStatusResponse_AnalysisStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetAnalysisStatusResponse_AnalysisStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_connected_account_analytics_service_proto_enumTypes[1].Descriptor()
}

func (GetAnalysisStatusResponse_AnalysisStatus) Type() protoreflect.EnumType {
	return &file_api_connected_account_analytics_service_proto_enumTypes[1]
}

func (x GetAnalysisStatusResponse_AnalysisStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetAnalysisStatusResponse_AnalysisStatus.Descriptor instead.
func (GetAnalysisStatusResponse_AnalysisStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{3, 0}
}

type InitiateAnalysisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier of the actor whose analysis is being requested
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// unique request id sent for orchestration
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// Client enum for specified by the caller
	Client common.Owner `protobuf:"varint,3,opt,name=client,proto3,enum=api.typesv2.common.Owner" json:"client,omitempty"`
	// Data exchange record containing the AA data to be analyzed, needed to be sent by external (non-wealth) caller
	DataExchangeRecord *DataExchangeRecord `protobuf:"bytes,4,opt,name=data_exchange_record,json=dataExchangeRecord,proto3" json:"data_exchange_record,omitempty"`
	// user's employment type details as sent by client
	EmploymentType typesv2.EmploymentType `protobuf:"varint,5,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.EmploymentType" json:"employment_type,omitempty"` // optional field
	// Name of user's organisation / employer
	OrganisationName string `protobuf:"bytes,6,opt,name=organisation_name,json=organisationName,proto3" json:"organisation_name,omitempty"` // optional field
}

func (x *InitiateAnalysisRequest) Reset() {
	*x = InitiateAnalysisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_analytics_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateAnalysisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateAnalysisRequest) ProtoMessage() {}

func (x *InitiateAnalysisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_analytics_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateAnalysisRequest.ProtoReflect.Descriptor instead.
func (*InitiateAnalysisRequest) Descriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{0}
}

func (x *InitiateAnalysisRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InitiateAnalysisRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *InitiateAnalysisRequest) GetClient() common.Owner {
	if x != nil {
		return x.Client
	}
	return common.Owner(0)
}

func (x *InitiateAnalysisRequest) GetDataExchangeRecord() *DataExchangeRecord {
	if x != nil {
		return x.DataExchangeRecord
	}
	return nil
}

func (x *InitiateAnalysisRequest) GetEmploymentType() typesv2.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return typesv2.EmploymentType(0)
}

func (x *InitiateAnalysisRequest) GetOrganisationName() string {
	if x != nil {
		return x.OrganisationName
	}
	return ""
}

type InitiateAnalysisResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// unique request id sent for orchestration
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *InitiateAnalysisResponse) Reset() {
	*x = InitiateAnalysisResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_analytics_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateAnalysisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateAnalysisResponse) ProtoMessage() {}

func (x *InitiateAnalysisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_analytics_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateAnalysisResponse.ProtoReflect.Descriptor instead.
func (*InitiateAnalysisResponse) Descriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{1}
}

func (x *InitiateAnalysisResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitiateAnalysisResponse) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type GetAnalysisStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// unique request id sent for initiating the analysis
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *GetAnalysisStatusRequest) Reset() {
	*x = GetAnalysisStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_analytics_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisStatusRequest) ProtoMessage() {}

func (x *GetAnalysisStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_analytics_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisStatusRequest.ProtoReflect.Descriptor instead.
func (*GetAnalysisStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetAnalysisStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAnalysisStatusRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type GetAnalysisStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// unique request id sent for initiating the analysis
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// Current status of analysis
	AnalysisStatus GetAnalysisStatusResponse_AnalysisStatus `protobuf:"varint,3,opt,name=analysis_status,json=analysisStatus,proto3,enum=connected_account.analytics.GetAnalysisStatusResponse_AnalysisStatus" json:"analysis_status,omitempty"`
}

func (x *GetAnalysisStatusResponse) Reset() {
	*x = GetAnalysisStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_analytics_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisStatusResponse) ProtoMessage() {}

func (x *GetAnalysisStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_analytics_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisStatusResponse.ProtoReflect.Descriptor instead.
func (*GetAnalysisStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetAnalysisStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAnalysisStatusResponse) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *GetAnalysisStatusResponse) GetAnalysisStatus() GetAnalysisStatusResponse_AnalysisStatus {
	if x != nil {
		return x.AnalysisStatus
	}
	return GetAnalysisStatusResponse_ANALYSIS_STATUS_UNSPECIFIED
}

type GetAnalysisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *GetAnalysisRequest) Reset() {
	*x = GetAnalysisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_analytics_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisRequest) ProtoMessage() {}

func (x *GetAnalysisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_analytics_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisRequest.ProtoReflect.Descriptor instead.
func (*GetAnalysisRequest) Descriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetAnalysisRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAnalysisRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type GetAnalysisResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Analysis results
	Analysis *Analysis `protobuf:"bytes,2,opt,name=analysis,proto3" json:"analysis,omitempty"`
}

func (x *GetAnalysisResponse) Reset() {
	*x = GetAnalysisResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_analytics_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisResponse) ProtoMessage() {}

func (x *GetAnalysisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_analytics_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisResponse.ProtoReflect.Descriptor instead.
func (*GetAnalysisResponse) Descriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetAnalysisResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAnalysisResponse) GetAnalysis() *Analysis {
	if x != nil {
		return x.Analysis
	}
	return nil
}

type GetAnalysisByActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string       `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	Client  common.Owner `protobuf:"varint,2,opt,name=client,proto3,enum=api.typesv2.common.Owner" json:"client,omitempty"`
}

func (x *GetAnalysisByActorRequest) Reset() {
	*x = GetAnalysisByActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_analytics_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisByActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisByActorRequest) ProtoMessage() {}

func (x *GetAnalysisByActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_analytics_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisByActorRequest.ProtoReflect.Descriptor instead.
func (*GetAnalysisByActorRequest) Descriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetAnalysisByActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAnalysisByActorRequest) GetClient() common.Owner {
	if x != nil {
		return x.Client
	}
	return common.Owner(0)
}

type GetAnalysisByActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Analysis results
	Analysis *Analysis `protobuf:"bytes,2,opt,name=analysis,proto3" json:"analysis,omitempty"`
	// presigned url for the L1 level of analysis
	L1AnalysisSignedUrl string `protobuf:"bytes,3,opt,name=l1_analysis_signed_url,json=l1AnalysisSignedUrl,proto3" json:"l1_analysis_signed_url,omitempty"`
}

func (x *GetAnalysisByActorResponse) Reset() {
	*x = GetAnalysisByActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_analytics_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisByActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisByActorResponse) ProtoMessage() {}

func (x *GetAnalysisByActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_analytics_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisByActorResponse.ProtoReflect.Descriptor instead.
func (*GetAnalysisByActorResponse) Descriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetAnalysisByActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAnalysisByActorResponse) GetAnalysis() *Analysis {
	if x != nil {
		return x.Analysis
	}
	return nil
}

func (x *GetAnalysisByActorResponse) GetL1AnalysisSignedUrl() string {
	if x != nil {
		return x.L1AnalysisSignedUrl
	}
	return ""
}

type ProcessAnalysisStatusCallbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vendor-side identifier of a user
	VendorActorId string `protobuf:"bytes,1,opt,name=vendor_actor_id,json=vendorActorId,proto3" json:"vendor_actor_id,omitempty"`
	// id which belongs maps to client request id sent to vendor
	VendorRequestId string `protobuf:"bytes,2,opt,name=vendor_request_id,json=vendorRequestId,proto3" json:"vendor_request_id,omitempty"`
	AnalysisStatus  string `protobuf:"bytes,3,opt,name=analysis_status,json=analysisStatus,proto3" json:"analysis_status,omitempty"`
}

func (x *ProcessAnalysisStatusCallbackRequest) Reset() {
	*x = ProcessAnalysisStatusCallbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_analytics_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAnalysisStatusCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAnalysisStatusCallbackRequest) ProtoMessage() {}

func (x *ProcessAnalysisStatusCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_analytics_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAnalysisStatusCallbackRequest.ProtoReflect.Descriptor instead.
func (*ProcessAnalysisStatusCallbackRequest) Descriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{8}
}

func (x *ProcessAnalysisStatusCallbackRequest) GetVendorActorId() string {
	if x != nil {
		return x.VendorActorId
	}
	return ""
}

func (x *ProcessAnalysisStatusCallbackRequest) GetVendorRequestId() string {
	if x != nil {
		return x.VendorRequestId
	}
	return ""
}

func (x *ProcessAnalysisStatusCallbackRequest) GetAnalysisStatus() string {
	if x != nil {
		return x.AnalysisStatus
	}
	return ""
}

type ProcessAnalysisStatusCallbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ProcessAnalysisStatusCallbackResponse) Reset() {
	*x = ProcessAnalysisStatusCallbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_analytics_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessAnalysisStatusCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessAnalysisStatusCallbackResponse) ProtoMessage() {}

func (x *ProcessAnalysisStatusCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_analytics_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessAnalysisStatusCallbackResponse.ProtoReflect.Descriptor instead.
func (*ProcessAnalysisStatusCallbackResponse) Descriptor() ([]byte, []int) {
	return file_api_connected_account_analytics_service_proto_rawDescGZIP(), []int{9}
}

func (x *ProcessAnalysisStatusCallbackResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_api_connected_account_analytics_service_proto protoreflect.FileDescriptor

var file_api_connected_account_analytics_service_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1b, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x1a, 0x33, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x65, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x3a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xfd, 0x02, 0x0a, 0x17, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64,
	0x12, 0x3b, 0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x19, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x61, 0x0a,
	0x14, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x12, 0x64, 0x61,
	0x74, 0x61, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x12, 0x44, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x93, 0x02, 0x0a, 0x18, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x49, 0x64, 0x22, 0xa4, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a,
	0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x4e, 0x4f, 0x54, 0x48, 0x45, 0x52,
	0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x43,
	0x54, 0x4f, 0x52, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x49, 0x4e, 0x5f, 0x50,
	0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x41, 0x5f,
	0x44, 0x41, 0x54, 0x41, 0x5f, 0x50, 0x55, 0x4c, 0x4c, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10,
	0x66, 0x12, 0x36, 0x0a, 0x32, 0x4e, 0x4f, 0x5f, 0x4e, 0x45, 0x57, 0x5f, 0x44, 0x41, 0x54, 0x41,
	0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x41, 0x4e, 0x59, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x46, 0x4f,
	0x52, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10, 0x67, 0x22, 0x6b, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x22, 0xde, 0x02, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x6e, 0x0a,
	0x0f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x45, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x87, 0x01,
	0x0a, 0x0e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1f, 0x0a, 0x1b, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x1b, 0x0a, 0x17, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x41,
	0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x22, 0x65, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x22, 0x7d,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x08, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x52, 0x08, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x22, 0x7c, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x42, 0x79, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3b,
	0x0a, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x06, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x22, 0xb9, 0x01, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x41, 0x0a, 0x08, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x08, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x6c, 0x31, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x6c, 0x31, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x22, 0xa3, 0x01, 0x0a, 0x24, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x26, 0x0a, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x4c, 0x0a,
	0x25, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xb4, 0x05, 0x0a, 0x09,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x7f, 0x0a, 0x10, 0x49, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x34, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x11, 0x47,
	0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x35, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x70, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x2f,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x30, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x85, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x36, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65,
	0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x37, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa6, 0x01, 0x0a, 0x1d, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x41, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x50, 0x72, 0x6f,
	0x63, 0x65, 0x73, 0x73, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x42, 0x70, 0x0a, 0x36, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5a, 0x36, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67,
	0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79,
	0x74, 0x69, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_connected_account_analytics_service_proto_rawDescOnce sync.Once
	file_api_connected_account_analytics_service_proto_rawDescData = file_api_connected_account_analytics_service_proto_rawDesc
)

func file_api_connected_account_analytics_service_proto_rawDescGZIP() []byte {
	file_api_connected_account_analytics_service_proto_rawDescOnce.Do(func() {
		file_api_connected_account_analytics_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_connected_account_analytics_service_proto_rawDescData)
	})
	return file_api_connected_account_analytics_service_proto_rawDescData
}

var file_api_connected_account_analytics_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_connected_account_analytics_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_connected_account_analytics_service_proto_goTypes = []interface{}{
	(InitiateAnalysisResponse_Status)(0),          // 0: connected_account.analytics.InitiateAnalysisResponse.Status
	(GetAnalysisStatusResponse_AnalysisStatus)(0), // 1: connected_account.analytics.GetAnalysisStatusResponse.AnalysisStatus
	(*InitiateAnalysisRequest)(nil),               // 2: connected_account.analytics.InitiateAnalysisRequest
	(*InitiateAnalysisResponse)(nil),              // 3: connected_account.analytics.InitiateAnalysisResponse
	(*GetAnalysisStatusRequest)(nil),              // 4: connected_account.analytics.GetAnalysisStatusRequest
	(*GetAnalysisStatusResponse)(nil),             // 5: connected_account.analytics.GetAnalysisStatusResponse
	(*GetAnalysisRequest)(nil),                    // 6: connected_account.analytics.GetAnalysisRequest
	(*GetAnalysisResponse)(nil),                   // 7: connected_account.analytics.GetAnalysisResponse
	(*GetAnalysisByActorRequest)(nil),             // 8: connected_account.analytics.GetAnalysisByActorRequest
	(*GetAnalysisByActorResponse)(nil),            // 9: connected_account.analytics.GetAnalysisByActorResponse
	(*ProcessAnalysisStatusCallbackRequest)(nil),  // 10: connected_account.analytics.ProcessAnalysisStatusCallbackRequest
	(*ProcessAnalysisStatusCallbackResponse)(nil), // 11: connected_account.analytics.ProcessAnalysisStatusCallbackResponse
	(common.Owner)(0),                             // 12: api.typesv2.common.Owner
	(*DataExchangeRecord)(nil),                    // 13: connected_account.analytics.DataExchangeRecord
	(typesv2.EmploymentType)(0),                   // 14: api.typesv2.EmploymentType
	(*rpc.Status)(nil),                            // 15: rpc.Status
	(*Analysis)(nil),                              // 16: connected_account.analytics.Analysis
}
var file_api_connected_account_analytics_service_proto_depIdxs = []int32{
	12, // 0: connected_account.analytics.InitiateAnalysisRequest.client:type_name -> api.typesv2.common.Owner
	13, // 1: connected_account.analytics.InitiateAnalysisRequest.data_exchange_record:type_name -> connected_account.analytics.DataExchangeRecord
	14, // 2: connected_account.analytics.InitiateAnalysisRequest.employment_type:type_name -> api.typesv2.EmploymentType
	15, // 3: connected_account.analytics.InitiateAnalysisResponse.status:type_name -> rpc.Status
	15, // 4: connected_account.analytics.GetAnalysisStatusResponse.status:type_name -> rpc.Status
	1,  // 5: connected_account.analytics.GetAnalysisStatusResponse.analysis_status:type_name -> connected_account.analytics.GetAnalysisStatusResponse.AnalysisStatus
	15, // 6: connected_account.analytics.GetAnalysisResponse.status:type_name -> rpc.Status
	16, // 7: connected_account.analytics.GetAnalysisResponse.analysis:type_name -> connected_account.analytics.Analysis
	12, // 8: connected_account.analytics.GetAnalysisByActorRequest.client:type_name -> api.typesv2.common.Owner
	15, // 9: connected_account.analytics.GetAnalysisByActorResponse.status:type_name -> rpc.Status
	16, // 10: connected_account.analytics.GetAnalysisByActorResponse.analysis:type_name -> connected_account.analytics.Analysis
	15, // 11: connected_account.analytics.ProcessAnalysisStatusCallbackResponse.status:type_name -> rpc.Status
	2,  // 12: connected_account.analytics.Analytics.InitiateAnalysis:input_type -> connected_account.analytics.InitiateAnalysisRequest
	4,  // 13: connected_account.analytics.Analytics.GetAnalysisStatus:input_type -> connected_account.analytics.GetAnalysisStatusRequest
	6,  // 14: connected_account.analytics.Analytics.GetAnalysis:input_type -> connected_account.analytics.GetAnalysisRequest
	8,  // 15: connected_account.analytics.Analytics.GetAnalysisByActor:input_type -> connected_account.analytics.GetAnalysisByActorRequest
	10, // 16: connected_account.analytics.Analytics.ProcessAnalysisStatusCallback:input_type -> connected_account.analytics.ProcessAnalysisStatusCallbackRequest
	3,  // 17: connected_account.analytics.Analytics.InitiateAnalysis:output_type -> connected_account.analytics.InitiateAnalysisResponse
	5,  // 18: connected_account.analytics.Analytics.GetAnalysisStatus:output_type -> connected_account.analytics.GetAnalysisStatusResponse
	7,  // 19: connected_account.analytics.Analytics.GetAnalysis:output_type -> connected_account.analytics.GetAnalysisResponse
	9,  // 20: connected_account.analytics.Analytics.GetAnalysisByActor:output_type -> connected_account.analytics.GetAnalysisByActorResponse
	11, // 21: connected_account.analytics.Analytics.ProcessAnalysisStatusCallback:output_type -> connected_account.analytics.ProcessAnalysisStatusCallbackResponse
	17, // [17:22] is the sub-list for method output_type
	12, // [12:17] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_api_connected_account_analytics_service_proto_init() }
func file_api_connected_account_analytics_service_proto_init() {
	if File_api_connected_account_analytics_service_proto != nil {
		return
	}
	file_api_connected_account_analytics_analysed_user_proto_init()
	file_api_connected_account_analytics_data_exchange_record_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_connected_account_analytics_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateAnalysisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_analytics_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateAnalysisResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_analytics_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_analytics_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_analytics_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_analytics_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_analytics_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisByActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_analytics_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisByActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_analytics_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAnalysisStatusCallbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_analytics_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessAnalysisStatusCallbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_connected_account_analytics_service_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_connected_account_analytics_service_proto_goTypes,
		DependencyIndexes: file_api_connected_account_analytics_service_proto_depIdxs,
		EnumInfos:         file_api_connected_account_analytics_service_proto_enumTypes,
		MessageInfos:      file_api_connected_account_analytics_service_proto_msgTypes,
	}.Build()
	File_api_connected_account_analytics_service_proto = out.File
	file_api_connected_account_analytics_service_proto_rawDesc = nil
	file_api_connected_account_analytics_service_proto_goTypes = nil
	file_api_connected_account_analytics_service_proto_depIdxs = nil
}
