// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/connected_account/analytics/analysis_request.proto

package analytics

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Owner(0)

	_ = typesv2.EmploymentType(0)
)

// Validate checks the field values on AnalysisRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AnalysisRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnalysisRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnalysisRequestMultiError, or nil if none found.
func (m *AnalysisRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AnalysisRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for OrchId

	// no validation rules for ActorId

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisRequestValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisRequestValidationError{
					field:  "Details",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisRequestValidationError{
				field:  "Details",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisRequestValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisRequestValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisRequestValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisRequestValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisRequestValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisRequestValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisRequestValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisRequestValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisRequestValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AnalysisRequestMultiError(errors)
	}

	return nil
}

// AnalysisRequestMultiError is an error wrapping multiple validation errors
// returned by AnalysisRequest.ValidateAll() if the designated constraints
// aren't met.
type AnalysisRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalysisRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalysisRequestMultiError) AllErrors() []error { return m }

// AnalysisRequestValidationError is the validation error returned by
// AnalysisRequest.Validate if the designated constraints aren't met.
type AnalysisRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalysisRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalysisRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalysisRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalysisRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalysisRequestValidationError) ErrorName() string { return "AnalysisRequestValidationError" }

// Error satisfies the builtin error interface
func (e AnalysisRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalysisRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalysisRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalysisRequestValidationError{}

// Validate checks the field values on Details with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Details) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Details with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DetailsMultiError, or nil if none found.
func (m *Details) ValidateAll() error {
	return m.validate(true)
}

func (m *Details) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Client

	// no validation rules for ReferenceId

	if all {
		switch v := interface{}(m.GetL1CompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetailsValidationError{
					field:  "L1CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetailsValidationError{
					field:  "L1CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetL1CompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetailsValidationError{
				field:  "L1CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetL2CompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DetailsValidationError{
					field:  "L2CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DetailsValidationError{
					field:  "L2CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetL2CompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DetailsValidationError{
				field:  "L2CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsInitiatedWithData

	// no validation rules for EmploymentType

	// no validation rules for OrganisationName

	if len(errors) > 0 {
		return DetailsMultiError(errors)
	}

	return nil
}

// DetailsMultiError is an error wrapping multiple validation errors returned
// by Details.ValidateAll() if the designated constraints aren't met.
type DetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DetailsMultiError) AllErrors() []error { return m }

// DetailsValidationError is the validation error returned by Details.Validate
// if the designated constraints aren't met.
type DetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DetailsValidationError) ErrorName() string { return "DetailsValidationError" }

// Error satisfies the builtin error interface
func (e DetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DetailsValidationError{}
