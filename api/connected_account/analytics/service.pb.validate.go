// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/connected_account/analytics/service.proto

package analytics

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.Owner(0)

	_ = typesv2.EmploymentType(0)
)

// Validate checks the field values on InitiateAnalysisRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateAnalysisRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateAnalysisRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateAnalysisRequestMultiError, or nil if none found.
func (m *InitiateAnalysisRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateAnalysisRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := InitiateAnalysisRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetClientReqId()) < 1 {
		err := InitiateAnalysisRequestValidationError{
			field:  "ClientReqId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := common.Owner_name[int32(m.GetClient())]; !ok {
		err := InitiateAnalysisRequestValidationError{
			field:  "Client",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDataExchangeRecord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAnalysisRequestValidationError{
					field:  "DataExchangeRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAnalysisRequestValidationError{
					field:  "DataExchangeRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDataExchangeRecord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAnalysisRequestValidationError{
				field:  "DataExchangeRecord",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmploymentType

	// no validation rules for OrganisationName

	if len(errors) > 0 {
		return InitiateAnalysisRequestMultiError(errors)
	}

	return nil
}

// InitiateAnalysisRequestMultiError is an error wrapping multiple validation
// errors returned by InitiateAnalysisRequest.ValidateAll() if the designated
// constraints aren't met.
type InitiateAnalysisRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateAnalysisRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateAnalysisRequestMultiError) AllErrors() []error { return m }

// InitiateAnalysisRequestValidationError is the validation error returned by
// InitiateAnalysisRequest.Validate if the designated constraints aren't met.
type InitiateAnalysisRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateAnalysisRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateAnalysisRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateAnalysisRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateAnalysisRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateAnalysisRequestValidationError) ErrorName() string {
	return "InitiateAnalysisRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateAnalysisRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateAnalysisRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateAnalysisRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateAnalysisRequestValidationError{}

// Validate checks the field values on InitiateAnalysisResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitiateAnalysisResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitiateAnalysisResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitiateAnalysisResponseMultiError, or nil if none found.
func (m *InitiateAnalysisResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitiateAnalysisResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitiateAnalysisResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitiateAnalysisResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitiateAnalysisResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetClientReqId()) < 1 {
		err := InitiateAnalysisResponseValidationError{
			field:  "ClientReqId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return InitiateAnalysisResponseMultiError(errors)
	}

	return nil
}

// InitiateAnalysisResponseMultiError is an error wrapping multiple validation
// errors returned by InitiateAnalysisResponse.ValidateAll() if the designated
// constraints aren't met.
type InitiateAnalysisResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitiateAnalysisResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitiateAnalysisResponseMultiError) AllErrors() []error { return m }

// InitiateAnalysisResponseValidationError is the validation error returned by
// InitiateAnalysisResponse.Validate if the designated constraints aren't met.
type InitiateAnalysisResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitiateAnalysisResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitiateAnalysisResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitiateAnalysisResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitiateAnalysisResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitiateAnalysisResponseValidationError) ErrorName() string {
	return "InitiateAnalysisResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitiateAnalysisResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitiateAnalysisResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitiateAnalysisResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitiateAnalysisResponseValidationError{}

// Validate checks the field values on GetAnalysisStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAnalysisStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAnalysisStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAnalysisStatusRequestMultiError, or nil if none found.
func (m *GetAnalysisStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAnalysisStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetAnalysisStatusRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetClientReqId()) < 1 {
		err := GetAnalysisStatusRequestValidationError{
			field:  "ClientReqId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAnalysisStatusRequestMultiError(errors)
	}

	return nil
}

// GetAnalysisStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetAnalysisStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAnalysisStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAnalysisStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAnalysisStatusRequestMultiError) AllErrors() []error { return m }

// GetAnalysisStatusRequestValidationError is the validation error returned by
// GetAnalysisStatusRequest.Validate if the designated constraints aren't met.
type GetAnalysisStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAnalysisStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAnalysisStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAnalysisStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAnalysisStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAnalysisStatusRequestValidationError) ErrorName() string {
	return "GetAnalysisStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAnalysisStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAnalysisStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAnalysisStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAnalysisStatusRequestValidationError{}

// Validate checks the field values on GetAnalysisStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAnalysisStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAnalysisStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAnalysisStatusResponseMultiError, or nil if none found.
func (m *GetAnalysisStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAnalysisStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAnalysisStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAnalysisStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAnalysisStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientReqId

	// no validation rules for AnalysisStatus

	if len(errors) > 0 {
		return GetAnalysisStatusResponseMultiError(errors)
	}

	return nil
}

// GetAnalysisStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetAnalysisStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type GetAnalysisStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAnalysisStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAnalysisStatusResponseMultiError) AllErrors() []error { return m }

// GetAnalysisStatusResponseValidationError is the validation error returned by
// GetAnalysisStatusResponse.Validate if the designated constraints aren't met.
type GetAnalysisStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAnalysisStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAnalysisStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAnalysisStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAnalysisStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAnalysisStatusResponseValidationError) ErrorName() string {
	return "GetAnalysisStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAnalysisStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAnalysisStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAnalysisStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAnalysisStatusResponseValidationError{}

// Validate checks the field values on GetAnalysisRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAnalysisRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAnalysisRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAnalysisRequestMultiError, or nil if none found.
func (m *GetAnalysisRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAnalysisRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetAnalysisRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetClientReqId()) < 1 {
		err := GetAnalysisRequestValidationError{
			field:  "ClientReqId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAnalysisRequestMultiError(errors)
	}

	return nil
}

// GetAnalysisRequestMultiError is an error wrapping multiple validation errors
// returned by GetAnalysisRequest.ValidateAll() if the designated constraints
// aren't met.
type GetAnalysisRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAnalysisRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAnalysisRequestMultiError) AllErrors() []error { return m }

// GetAnalysisRequestValidationError is the validation error returned by
// GetAnalysisRequest.Validate if the designated constraints aren't met.
type GetAnalysisRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAnalysisRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAnalysisRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAnalysisRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAnalysisRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAnalysisRequestValidationError) ErrorName() string {
	return "GetAnalysisRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAnalysisRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAnalysisRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAnalysisRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAnalysisRequestValidationError{}

// Validate checks the field values on GetAnalysisResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAnalysisResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAnalysisResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAnalysisResponseMultiError, or nil if none found.
func (m *GetAnalysisResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAnalysisResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAnalysisResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAnalysisResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAnalysisResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnalysis()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAnalysisResponseValidationError{
					field:  "Analysis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAnalysisResponseValidationError{
					field:  "Analysis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalysis()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAnalysisResponseValidationError{
				field:  "Analysis",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAnalysisResponseMultiError(errors)
	}

	return nil
}

// GetAnalysisResponseMultiError is an error wrapping multiple validation
// errors returned by GetAnalysisResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAnalysisResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAnalysisResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAnalysisResponseMultiError) AllErrors() []error { return m }

// GetAnalysisResponseValidationError is the validation error returned by
// GetAnalysisResponse.Validate if the designated constraints aren't met.
type GetAnalysisResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAnalysisResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAnalysisResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAnalysisResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAnalysisResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAnalysisResponseValidationError) ErrorName() string {
	return "GetAnalysisResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAnalysisResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAnalysisResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAnalysisResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAnalysisResponseValidationError{}

// Validate checks the field values on GetAnalysisByActorRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAnalysisByActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAnalysisByActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAnalysisByActorRequestMultiError, or nil if none found.
func (m *GetAnalysisByActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAnalysisByActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetActorId()) < 1 {
		err := GetAnalysisByActorRequestValidationError{
			field:  "ActorId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := common.Owner_name[int32(m.GetClient())]; !ok {
		err := GetAnalysisByActorRequestValidationError{
			field:  "Client",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetAnalysisByActorRequestMultiError(errors)
	}

	return nil
}

// GetAnalysisByActorRequestMultiError is an error wrapping multiple validation
// errors returned by GetAnalysisByActorRequest.ValidateAll() if the
// designated constraints aren't met.
type GetAnalysisByActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAnalysisByActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAnalysisByActorRequestMultiError) AllErrors() []error { return m }

// GetAnalysisByActorRequestValidationError is the validation error returned by
// GetAnalysisByActorRequest.Validate if the designated constraints aren't met.
type GetAnalysisByActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAnalysisByActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAnalysisByActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAnalysisByActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAnalysisByActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAnalysisByActorRequestValidationError) ErrorName() string {
	return "GetAnalysisByActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAnalysisByActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAnalysisByActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAnalysisByActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAnalysisByActorRequestValidationError{}

// Validate checks the field values on GetAnalysisByActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAnalysisByActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAnalysisByActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetAnalysisByActorResponseMultiError, or nil if none found.
func (m *GetAnalysisByActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAnalysisByActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAnalysisByActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAnalysisByActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAnalysisByActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAnalysis()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAnalysisByActorResponseValidationError{
					field:  "Analysis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAnalysisByActorResponseValidationError{
					field:  "Analysis",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAnalysis()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAnalysisByActorResponseValidationError{
				field:  "Analysis",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for L1AnalysisSignedUrl

	if len(errors) > 0 {
		return GetAnalysisByActorResponseMultiError(errors)
	}

	return nil
}

// GetAnalysisByActorResponseMultiError is an error wrapping multiple
// validation errors returned by GetAnalysisByActorResponse.ValidateAll() if
// the designated constraints aren't met.
type GetAnalysisByActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAnalysisByActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAnalysisByActorResponseMultiError) AllErrors() []error { return m }

// GetAnalysisByActorResponseValidationError is the validation error returned
// by GetAnalysisByActorResponse.Validate if the designated constraints aren't met.
type GetAnalysisByActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAnalysisByActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAnalysisByActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAnalysisByActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAnalysisByActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAnalysisByActorResponseValidationError) ErrorName() string {
	return "GetAnalysisByActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAnalysisByActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAnalysisByActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAnalysisByActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAnalysisByActorResponseValidationError{}

// Validate checks the field values on ProcessAnalysisStatusCallbackRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessAnalysisStatusCallbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAnalysisStatusCallbackRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessAnalysisStatusCallbackRequestMultiError, or nil if none found.
func (m *ProcessAnalysisStatusCallbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAnalysisStatusCallbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VendorActorId

	// no validation rules for VendorRequestId

	// no validation rules for AnalysisStatus

	if len(errors) > 0 {
		return ProcessAnalysisStatusCallbackRequestMultiError(errors)
	}

	return nil
}

// ProcessAnalysisStatusCallbackRequestMultiError is an error wrapping multiple
// validation errors returned by
// ProcessAnalysisStatusCallbackRequest.ValidateAll() if the designated
// constraints aren't met.
type ProcessAnalysisStatusCallbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAnalysisStatusCallbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAnalysisStatusCallbackRequestMultiError) AllErrors() []error { return m }

// ProcessAnalysisStatusCallbackRequestValidationError is the validation error
// returned by ProcessAnalysisStatusCallbackRequest.Validate if the designated
// constraints aren't met.
type ProcessAnalysisStatusCallbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAnalysisStatusCallbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAnalysisStatusCallbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAnalysisStatusCallbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAnalysisStatusCallbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAnalysisStatusCallbackRequestValidationError) ErrorName() string {
	return "ProcessAnalysisStatusCallbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAnalysisStatusCallbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAnalysisStatusCallbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAnalysisStatusCallbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAnalysisStatusCallbackRequestValidationError{}

// Validate checks the field values on ProcessAnalysisStatusCallbackResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *ProcessAnalysisStatusCallbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessAnalysisStatusCallbackResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessAnalysisStatusCallbackResponseMultiError, or nil if none found.
func (m *ProcessAnalysisStatusCallbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessAnalysisStatusCallbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessAnalysisStatusCallbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessAnalysisStatusCallbackResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessAnalysisStatusCallbackResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessAnalysisStatusCallbackResponseMultiError(errors)
	}

	return nil
}

// ProcessAnalysisStatusCallbackResponseMultiError is an error wrapping
// multiple validation errors returned by
// ProcessAnalysisStatusCallbackResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessAnalysisStatusCallbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessAnalysisStatusCallbackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessAnalysisStatusCallbackResponseMultiError) AllErrors() []error { return m }

// ProcessAnalysisStatusCallbackResponseValidationError is the validation error
// returned by ProcessAnalysisStatusCallbackResponse.Validate if the
// designated constraints aren't met.
type ProcessAnalysisStatusCallbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessAnalysisStatusCallbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessAnalysisStatusCallbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessAnalysisStatusCallbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessAnalysisStatusCallbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessAnalysisStatusCallbackResponseValidationError) ErrorName() string {
	return "ProcessAnalysisStatusCallbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessAnalysisStatusCallbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessAnalysisStatusCallbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessAnalysisStatusCallbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessAnalysisStatusCallbackResponseValidationError{}
