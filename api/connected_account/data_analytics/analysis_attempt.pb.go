//go:generate gen_sql -types=AnalysisRequestParams,AnalysisStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/connected_account/data_analytics/analysis_attempt.proto

package data_analytics

import (
	analytics "github.com/epifi/gamma/api/connected_account/analytics"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AnalysisStatus represents the status of the analysis process.
type AnalysisStatus int32

const (
	AnalysisStatus_ANALYSIS_STATUS_UNSPECIFIED      AnalysisStatus = 0
	AnalysisStatus_ANALYSIS_STATUS_CREATED          AnalysisStatus = 1
	AnalysisStatus_ANALYSIS_STATUS_INITIATED        AnalysisStatus = 2
	AnalysisStatus_ANALYSIS_STATUS_ANALYSIS_PENDING AnalysisStatus = 3
	// A successful completion of the analysis process resulting in the analysis results.
	AnalysisStatus_ANALYSIS_STATUS_SUCCESSFUL AnalysisStatus = 4
	// A permanent failure in the analysis process, like no analysis results being found due to insufficient data.
	AnalysisStatus_ANALYSIS_STATUS_FAILED AnalysisStatus = 5
)

// Enum value maps for AnalysisStatus.
var (
	AnalysisStatus_name = map[int32]string{
		0: "ANALYSIS_STATUS_UNSPECIFIED",
		1: "ANALYSIS_STATUS_CREATED",
		2: "ANALYSIS_STATUS_INITIATED",
		3: "ANALYSIS_STATUS_ANALYSIS_PENDING",
		4: "ANALYSIS_STATUS_SUCCESSFUL",
		5: "ANALYSIS_STATUS_FAILED",
	}
	AnalysisStatus_value = map[string]int32{
		"ANALYSIS_STATUS_UNSPECIFIED":      0,
		"ANALYSIS_STATUS_CREATED":          1,
		"ANALYSIS_STATUS_INITIATED":        2,
		"ANALYSIS_STATUS_ANALYSIS_PENDING": 3,
		"ANALYSIS_STATUS_SUCCESSFUL":       4,
		"ANALYSIS_STATUS_FAILED":           5,
	}
)

func (x AnalysisStatus) Enum() *AnalysisStatus {
	p := new(AnalysisStatus)
	*p = x
	return p
}

func (x AnalysisStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnalysisStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_connected_account_data_analytics_analysis_attempt_proto_enumTypes[0].Descriptor()
}

func (AnalysisStatus) Type() protoreflect.EnumType {
	return &file_api_connected_account_data_analytics_analysis_attempt_proto_enumTypes[0]
}

func (x AnalysisStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnalysisStatus.Descriptor instead.
func (AnalysisStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescGZIP(), []int{0}
}

type AnalysisAttemptFieldMask int32

const (
	AnalysisAttemptFieldMask_ANALYSIS_ATTEMPT_FIELD_MASK_UNSPECIFIED      AnalysisAttemptFieldMask = 0
	AnalysisAttemptFieldMask_ANALYSIS_ATTEMPT_FIELD_MASK_ORCHESTRATION_ID AnalysisAttemptFieldMask = 1
	AnalysisAttemptFieldMask_ANALYSIS_ATTEMPT_FIELD_MASK_STATUS           AnalysisAttemptFieldMask = 2
	AnalysisAttemptFieldMask_ANALYSIS_ATTEMPT_FIELD_MASK_RESULT           AnalysisAttemptFieldMask = 3
)

// Enum value maps for AnalysisAttemptFieldMask.
var (
	AnalysisAttemptFieldMask_name = map[int32]string{
		0: "ANALYSIS_ATTEMPT_FIELD_MASK_UNSPECIFIED",
		1: "ANALYSIS_ATTEMPT_FIELD_MASK_ORCHESTRATION_ID",
		2: "ANALYSIS_ATTEMPT_FIELD_MASK_STATUS",
		3: "ANALYSIS_ATTEMPT_FIELD_MASK_RESULT",
	}
	AnalysisAttemptFieldMask_value = map[string]int32{
		"ANALYSIS_ATTEMPT_FIELD_MASK_UNSPECIFIED":      0,
		"ANALYSIS_ATTEMPT_FIELD_MASK_ORCHESTRATION_ID": 1,
		"ANALYSIS_ATTEMPT_FIELD_MASK_STATUS":           2,
		"ANALYSIS_ATTEMPT_FIELD_MASK_RESULT":           3,
	}
)

func (x AnalysisAttemptFieldMask) Enum() *AnalysisAttemptFieldMask {
	p := new(AnalysisAttemptFieldMask)
	*p = x
	return p
}

func (x AnalysisAttemptFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AnalysisAttemptFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_connected_account_data_analytics_analysis_attempt_proto_enumTypes[1].Descriptor()
}

func (AnalysisAttemptFieldMask) Type() protoreflect.EnumType {
	return &file_api_connected_account_data_analytics_analysis_attempt_proto_enumTypes[1]
}

func (x AnalysisAttemptFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AnalysisAttemptFieldMask.Descriptor instead.
func (AnalysisAttemptFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescGZIP(), []int{1}
}

// AnalysisAttempt represents an individual attempt to analyze a user's data.
// This message captures the details of the analysis request and its current status.
type AnalysisAttempt struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique identifier for the analysis attempt
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// A client set identifier to track the analysis
	ClientReqId     string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	OrchestrationId string `protobuf:"bytes,3,opt,name=orchestration_id,json=orchestrationId,proto3" json:"orchestration_id,omitempty"`
	// Identifier of the user whose analysis is being requested
	ActorId string `protobuf:"bytes,4,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Parameters to be used for processing analysis request
	RequestParams *AnalysisRequestParams `protobuf:"bytes,5,opt,name=request_params,json=requestParams,proto3" json:"request_params,omitempty"`
	// Latest status of the analysis
	Status    AnalysisStatus         `protobuf:"varint,6,opt,name=status,proto3,enum=connected_account.data_analytics.AnalysisStatus" json:"status,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
}

func (x *AnalysisAttempt) Reset() {
	*x = AnalysisAttempt{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_data_analytics_analysis_attempt_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalysisAttempt) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalysisAttempt) ProtoMessage() {}

func (x *AnalysisAttempt) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_data_analytics_analysis_attempt_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalysisAttempt.ProtoReflect.Descriptor instead.
func (*AnalysisAttempt) Descriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescGZIP(), []int{0}
}

func (x *AnalysisAttempt) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AnalysisAttempt) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *AnalysisAttempt) GetOrchestrationId() string {
	if x != nil {
		return x.OrchestrationId
	}
	return ""
}

func (x *AnalysisAttempt) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *AnalysisAttempt) GetRequestParams() *AnalysisRequestParams {
	if x != nil {
		return x.RequestParams
	}
	return nil
}

func (x *AnalysisAttempt) GetStatus() AnalysisStatus {
	if x != nil {
		return x.Status
	}
	return AnalysisStatus_ANALYSIS_STATUS_UNSPECIFIED
}

func (x *AnalysisAttempt) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AnalysisAttempt) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *AnalysisAttempt) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

type AnalysisRequestParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataExchangeRecord *analytics.DataExchangeRecord `protobuf:"bytes,2,opt,name=data_exchange_record,json=dataExchangeRecord,proto3" json:"data_exchange_record,omitempty"`
	// user's employment type details as sent by client
	EmploymentType typesv2.EmploymentType `protobuf:"varint,1,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.EmploymentType" json:"employment_type,omitempty"` // optional field
	// Name of user's organisation / employer
	OrganisationName string `protobuf:"bytes,3,opt,name=organisation_name,json=organisationName,proto3" json:"organisation_name,omitempty"` // optional field
}

func (x *AnalysisRequestParams) Reset() {
	*x = AnalysisRequestParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_data_analytics_analysis_attempt_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AnalysisRequestParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnalysisRequestParams) ProtoMessage() {}

func (x *AnalysisRequestParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_data_analytics_analysis_attempt_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnalysisRequestParams.ProtoReflect.Descriptor instead.
func (*AnalysisRequestParams) Descriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescGZIP(), []int{1}
}

func (x *AnalysisRequestParams) GetDataExchangeRecord() *analytics.DataExchangeRecord {
	if x != nil {
		return x.DataExchangeRecord
	}
	return nil
}

func (x *AnalysisRequestParams) GetEmploymentType() typesv2.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return typesv2.EmploymentType(0)
}

func (x *AnalysisRequestParams) GetOrganisationName() string {
	if x != nil {
		return x.OrganisationName
	}
	return ""
}

var File_api_connected_account_data_analytics_analysis_attempt_proto protoreflect.FileDescriptor

var file_api_connected_account_data_analytics_analysis_attempt_proto_rawDesc = []byte{
	0x0a, 0x3b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x5f,
	0x61, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x1a,
	0x3a, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73,
	0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x72,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xe6, 0x03, 0x0a, 0x0f, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x41, 0x74, 0x74, 0x65,
	0x6d, 0x70, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x72, 0x63, 0x68, 0x65,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x6f, 0x72, 0x63, 0x68, 0x65, 0x73, 0x74, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x5e, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61,
	0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x48, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73,
	0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a,
	0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xed, 0x01, 0x0a, 0x15, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x73, 0x12, 0x61, 0x0a, 0x14, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x52, 0x12, 0x64, 0x61, 0x74, 0x61, 0x45, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x52,
	0x65, 0x63, 0x6f, 0x72, 0x64, 0x12, 0x44, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x45, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x73, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x2a, 0xcf, 0x01, 0x0a, 0x0e, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x1b, 0x41,
	0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17,
	0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x41, 0x4e, 0x41,
	0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49,
	0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x41, 0x4e, 0x41, 0x4c,
	0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x4e, 0x41, 0x4c,
	0x59, 0x53, 0x49, 0x53, 0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x1e,
	0x0a, 0x1a, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46, 0x55, 0x4c, 0x10, 0x04, 0x12, 0x1a,
	0x0a, 0x16, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x2a, 0xc9, 0x01, 0x0a, 0x18, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x41, 0x74, 0x74, 0x65, 0x6d, 0x70, 0x74, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x2b, 0x0a, 0x27, 0x41, 0x4e, 0x41, 0x4c, 0x59,
	0x53, 0x49, 0x53, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c,
	0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a, 0x2c, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53,
	0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x4f, 0x52, 0x43, 0x48, 0x45, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x26, 0x0a, 0x22, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53,
	0x49, 0x53, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d, 0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x02, 0x12, 0x26,
	0x0a, 0x22, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x49, 0x53, 0x5f, 0x41, 0x54, 0x54, 0x45, 0x4d,
	0x50, 0x54, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x52, 0x45,
	0x53, 0x55, 0x4c, 0x54, 0x10, 0x03, 0x42, 0x7a, 0x0a, 0x3b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x73, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescOnce sync.Once
	file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescData = file_api_connected_account_data_analytics_analysis_attempt_proto_rawDesc
)

func file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescGZIP() []byte {
	file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescOnce.Do(func() {
		file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescData)
	})
	return file_api_connected_account_data_analytics_analysis_attempt_proto_rawDescData
}

var file_api_connected_account_data_analytics_analysis_attempt_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_connected_account_data_analytics_analysis_attempt_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_api_connected_account_data_analytics_analysis_attempt_proto_goTypes = []interface{}{
	(AnalysisStatus)(0),                  // 0: connected_account.data_analytics.AnalysisStatus
	(AnalysisAttemptFieldMask)(0),        // 1: connected_account.data_analytics.AnalysisAttemptFieldMask
	(*AnalysisAttempt)(nil),              // 2: connected_account.data_analytics.AnalysisAttempt
	(*AnalysisRequestParams)(nil),        // 3: connected_account.data_analytics.AnalysisRequestParams
	(*timestamppb.Timestamp)(nil),        // 4: google.protobuf.Timestamp
	(*analytics.DataExchangeRecord)(nil), // 5: connected_account.analytics.DataExchangeRecord
	(typesv2.EmploymentType)(0),          // 6: api.typesv2.EmploymentType
}
var file_api_connected_account_data_analytics_analysis_attempt_proto_depIdxs = []int32{
	3, // 0: connected_account.data_analytics.AnalysisAttempt.request_params:type_name -> connected_account.data_analytics.AnalysisRequestParams
	0, // 1: connected_account.data_analytics.AnalysisAttempt.status:type_name -> connected_account.data_analytics.AnalysisStatus
	4, // 2: connected_account.data_analytics.AnalysisAttempt.created_at:type_name -> google.protobuf.Timestamp
	4, // 3: connected_account.data_analytics.AnalysisAttempt.updated_at:type_name -> google.protobuf.Timestamp
	4, // 4: connected_account.data_analytics.AnalysisAttempt.deleted_at:type_name -> google.protobuf.Timestamp
	5, // 5: connected_account.data_analytics.AnalysisRequestParams.data_exchange_record:type_name -> connected_account.analytics.DataExchangeRecord
	6, // 6: connected_account.data_analytics.AnalysisRequestParams.employment_type:type_name -> api.typesv2.EmploymentType
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_api_connected_account_data_analytics_analysis_attempt_proto_init() }
func file_api_connected_account_data_analytics_analysis_attempt_proto_init() {
	if File_api_connected_account_data_analytics_analysis_attempt_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_connected_account_data_analytics_analysis_attempt_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalysisAttempt); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_data_analytics_analysis_attempt_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AnalysisRequestParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_connected_account_data_analytics_analysis_attempt_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_connected_account_data_analytics_analysis_attempt_proto_goTypes,
		DependencyIndexes: file_api_connected_account_data_analytics_analysis_attempt_proto_depIdxs,
		EnumInfos:         file_api_connected_account_data_analytics_analysis_attempt_proto_enumTypes,
		MessageInfos:      file_api_connected_account_data_analytics_analysis_attempt_proto_msgTypes,
	}.Build()
	File_api_connected_account_data_analytics_analysis_attempt_proto = out.File
	file_api_connected_account_data_analytics_analysis_attempt_proto_rawDesc = nil
	file_api_connected_account_data_analytics_analysis_attempt_proto_goTypes = nil
	file_api_connected_account_data_analytics_analysis_attempt_proto_depIdxs = nil
}
