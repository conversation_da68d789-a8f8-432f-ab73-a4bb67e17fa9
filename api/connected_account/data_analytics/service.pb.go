// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/connected_account/data_analytics/service.proto

package data_analytics

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	analytics "github.com/epifi/gamma/api/connected_account/analytics"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetAnalysisByActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier of the actor whose analysis attempts are being requested.
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *GetAnalysisByActorRequest) Reset() {
	*x = GetAnalysisByActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisByActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisByActorRequest) ProtoMessage() {}

func (x *GetAnalysisByActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisByActorRequest.ProtoReflect.Descriptor instead.
func (*GetAnalysisByActorRequest) Descriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetAnalysisByActorRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

type GetAnalysisByActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Analysis *analytics.Analysis `protobuf:"bytes,2,opt,name=analysis,proto3" json:"analysis,omitempty"`
	// presigned url for the L1 level of analysis
	L1AnalysisSignedUrl string `protobuf:"bytes,3,opt,name=l1_analysis_signed_url,json=l1AnalysisSignedUrl,proto3" json:"l1_analysis_signed_url,omitempty"`
}

func (x *GetAnalysisByActorResponse) Reset() {
	*x = GetAnalysisByActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisByActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisByActorResponse) ProtoMessage() {}

func (x *GetAnalysisByActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisByActorResponse.ProtoReflect.Descriptor instead.
func (*GetAnalysisByActorResponse) Descriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetAnalysisByActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAnalysisByActorResponse) GetAnalysis() *analytics.Analysis {
	if x != nil {
		return x.Analysis
	}
	return nil
}

func (x *GetAnalysisByActorResponse) GetL1AnalysisSignedUrl() string {
	if x != nil {
		return x.L1AnalysisSignedUrl
	}
	return ""
}

type InitiateAnalysisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Unique client-set identifier to track the analysis and retrieve results later.
	// Only one analysis is initiated per client request id.
	ClientReqId string `protobuf:"bytes,1,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// Identifier of the actor whose analysis is being requested.
	ActorId       string                 `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestParams *AnalysisRequestParams `protobuf:"bytes,3,opt,name=request_params,json=requestParams,proto3" json:"request_params,omitempty"`
	// user's employment type details as sent by client
	EmploymentType typesv2.EmploymentType `protobuf:"varint,4,opt,name=employment_type,json=employmentType,proto3,enum=api.typesv2.EmploymentType" json:"employment_type,omitempty"` // optional field
	// Name of user's organisation / employer
	OrganisationName string `protobuf:"bytes,5,opt,name=organisation_name,json=organisationName,proto3" json:"organisation_name,omitempty"` // optional field
}

func (x *InitiateAnalysisRequest) Reset() {
	*x = InitiateAnalysisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateAnalysisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateAnalysisRequest) ProtoMessage() {}

func (x *InitiateAnalysisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateAnalysisRequest.ProtoReflect.Descriptor instead.
func (*InitiateAnalysisRequest) Descriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_service_proto_rawDescGZIP(), []int{2}
}

func (x *InitiateAnalysisRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *InitiateAnalysisRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *InitiateAnalysisRequest) GetRequestParams() *AnalysisRequestParams {
	if x != nil {
		return x.RequestParams
	}
	return nil
}

func (x *InitiateAnalysisRequest) GetEmploymentType() typesv2.EmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return typesv2.EmploymentType(0)
}

func (x *InitiateAnalysisRequest) GetOrganisationName() string {
	if x != nil {
		return x.OrganisationName
	}
	return ""
}

type InitiateAnalysisResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Status of the analysis attempt
	AnalysisStatus AnalysisStatus `protobuf:"varint,2,opt,name=analysis_status,json=analysisStatus,proto3,enum=connected_account.data_analytics.AnalysisStatus" json:"analysis_status,omitempty"`
}

func (x *InitiateAnalysisResponse) Reset() {
	*x = InitiateAnalysisResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitiateAnalysisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitiateAnalysisResponse) ProtoMessage() {}

func (x *InitiateAnalysisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitiateAnalysisResponse.ProtoReflect.Descriptor instead.
func (*InitiateAnalysisResponse) Descriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_service_proto_rawDescGZIP(), []int{3}
}

func (x *InitiateAnalysisResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitiateAnalysisResponse) GetAnalysisStatus() AnalysisStatus {
	if x != nil {
		return x.AnalysisStatus
	}
	return AnalysisStatus_ANALYSIS_STATUS_UNSPECIFIED
}

type GetAnalysisStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier of the actor whose analysis is being requested.
	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Same as the client request ID used to initiate the analysis
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *GetAnalysisStatusRequest) Reset() {
	*x = GetAnalysisStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisStatusRequest) ProtoMessage() {}

func (x *GetAnalysisStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisStatusRequest.ProtoReflect.Descriptor instead.
func (*GetAnalysisStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetAnalysisStatusRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAnalysisStatusRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type GetAnalysisStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// Status of the analysis attempt
	AnalysisStatus AnalysisStatus `protobuf:"varint,2,opt,name=analysis_status,json=analysisStatus,proto3,enum=connected_account.data_analytics.AnalysisStatus" json:"analysis_status,omitempty"`
}

func (x *GetAnalysisStatusResponse) Reset() {
	*x = GetAnalysisStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisStatusResponse) ProtoMessage() {}

func (x *GetAnalysisStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisStatusResponse.ProtoReflect.Descriptor instead.
func (*GetAnalysisStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetAnalysisStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAnalysisStatusResponse) GetAnalysisStatus() AnalysisStatus {
	if x != nil {
		return x.AnalysisStatus
	}
	return AnalysisStatus_ANALYSIS_STATUS_UNSPECIFIED
}

type GetAnalysisRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId string `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// Same as the client request ID used to initiate the analysis
	ClientReqId string `protobuf:"bytes,2,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
}

func (x *GetAnalysisRequest) Reset() {
	*x = GetAnalysisRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisRequest) ProtoMessage() {}

func (x *GetAnalysisRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisRequest.ProtoReflect.Descriptor instead.
func (*GetAnalysisRequest) Descriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetAnalysisRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetAnalysisRequest) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

type GetAnalysisResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status   *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Analysis *analytics.Analysis `protobuf:"bytes,2,opt,name=analysis,proto3" json:"analysis,omitempty"`
}

func (x *GetAnalysisResponse) Reset() {
	*x = GetAnalysisResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAnalysisResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnalysisResponse) ProtoMessage() {}

func (x *GetAnalysisResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_connected_account_data_analytics_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnalysisResponse.ProtoReflect.Descriptor instead.
func (*GetAnalysisResponse) Descriptor() ([]byte, []int) {
	return file_api_connected_account_data_analytics_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetAnalysisResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAnalysisResponse) GetAnalysis() *analytics.Analysis {
	if x != nil {
		return x.Analysis
	}
	return nil
}

var File_api_connected_account_data_analytics_service_proto protoreflect.FileDescriptor

var file_api_connected_account_data_analytics_service_proto_rawDesc = []byte{
	0x0a, 0x32, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x20, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x1a, 0x33, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x61, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x65, 0x64,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3b, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x5f, 0x61, 0x74, 0x74, 0x65, 0x6d,
	0x70, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x3f, 0x0a, 0x19, 0x47, 0x65,
	0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xb9, 0x01, 0x0a, 0x1a,
	0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x41, 0x0a, 0x08, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x08, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x12, 0x33, 0x0a, 0x16, 0x6c, 0x31, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x13, 0x6c, 0x31, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x69,
	0x67, 0x6e, 0x65, 0x64, 0x55, 0x72, 0x6c, 0x22, 0xbd, 0x02, 0x0a, 0x17, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65,
	0x71, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x10, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x5e, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x44, 0x0a, 0x0f, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x45, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c,
	0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9a, 0x01, 0x0a, 0x18, 0x49, 0x6e, 0x69, 0x74,
	0x69, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x0f, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c,
	0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x6b, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72,
	0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49,
	0x64, 0x22, 0x9b, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x0f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e,
	0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73,
	0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x65, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x0d, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x22, 0x7d, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x41, 0x0a, 0x08, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69,
	0x63, 0x73, 0x2e, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x08, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x32, 0xb8, 0x04, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x12, 0x8f, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x41,
	0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x3b,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x42, 0x79, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x42, 0x79, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x89, 0x01, 0x0a, 0x10, 0x49, 0x6e,
	0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x12, 0x39,
	0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63,
	0x73, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x49, 0x6e, 0x69,
	0x74, 0x69, 0x61, 0x74, 0x65, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61,
	0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x2e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e,
	0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7a, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79,
	0x73, 0x69, 0x73, 0x12, 0x34, 0x2e, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61,
	0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73,
	0x69, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x73, 0x69, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x7a, 0x0a, 0x3b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63,
	0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5a,
	0x3b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6f, 0x6e, 0x6e,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_connected_account_data_analytics_service_proto_rawDescOnce sync.Once
	file_api_connected_account_data_analytics_service_proto_rawDescData = file_api_connected_account_data_analytics_service_proto_rawDesc
)

func file_api_connected_account_data_analytics_service_proto_rawDescGZIP() []byte {
	file_api_connected_account_data_analytics_service_proto_rawDescOnce.Do(func() {
		file_api_connected_account_data_analytics_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_connected_account_data_analytics_service_proto_rawDescData)
	})
	return file_api_connected_account_data_analytics_service_proto_rawDescData
}

var file_api_connected_account_data_analytics_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_connected_account_data_analytics_service_proto_goTypes = []interface{}{
	(*GetAnalysisByActorRequest)(nil),  // 0: connected_account.data_analytics.GetAnalysisByActorRequest
	(*GetAnalysisByActorResponse)(nil), // 1: connected_account.data_analytics.GetAnalysisByActorResponse
	(*InitiateAnalysisRequest)(nil),    // 2: connected_account.data_analytics.InitiateAnalysisRequest
	(*InitiateAnalysisResponse)(nil),   // 3: connected_account.data_analytics.InitiateAnalysisResponse
	(*GetAnalysisStatusRequest)(nil),   // 4: connected_account.data_analytics.GetAnalysisStatusRequest
	(*GetAnalysisStatusResponse)(nil),  // 5: connected_account.data_analytics.GetAnalysisStatusResponse
	(*GetAnalysisRequest)(nil),         // 6: connected_account.data_analytics.GetAnalysisRequest
	(*GetAnalysisResponse)(nil),        // 7: connected_account.data_analytics.GetAnalysisResponse
	(*rpc.Status)(nil),                 // 8: rpc.Status
	(*analytics.Analysis)(nil),         // 9: connected_account.analytics.Analysis
	(*AnalysisRequestParams)(nil),      // 10: connected_account.data_analytics.AnalysisRequestParams
	(typesv2.EmploymentType)(0),        // 11: api.typesv2.EmploymentType
	(AnalysisStatus)(0),                // 12: connected_account.data_analytics.AnalysisStatus
}
var file_api_connected_account_data_analytics_service_proto_depIdxs = []int32{
	8,  // 0: connected_account.data_analytics.GetAnalysisByActorResponse.status:type_name -> rpc.Status
	9,  // 1: connected_account.data_analytics.GetAnalysisByActorResponse.analysis:type_name -> connected_account.analytics.Analysis
	10, // 2: connected_account.data_analytics.InitiateAnalysisRequest.request_params:type_name -> connected_account.data_analytics.AnalysisRequestParams
	11, // 3: connected_account.data_analytics.InitiateAnalysisRequest.employment_type:type_name -> api.typesv2.EmploymentType
	8,  // 4: connected_account.data_analytics.InitiateAnalysisResponse.status:type_name -> rpc.Status
	12, // 5: connected_account.data_analytics.InitiateAnalysisResponse.analysis_status:type_name -> connected_account.data_analytics.AnalysisStatus
	8,  // 6: connected_account.data_analytics.GetAnalysisStatusResponse.status:type_name -> rpc.Status
	12, // 7: connected_account.data_analytics.GetAnalysisStatusResponse.analysis_status:type_name -> connected_account.data_analytics.AnalysisStatus
	8,  // 8: connected_account.data_analytics.GetAnalysisResponse.status:type_name -> rpc.Status
	9,  // 9: connected_account.data_analytics.GetAnalysisResponse.analysis:type_name -> connected_account.analytics.Analysis
	0,  // 10: connected_account.data_analytics.DataAnalytics.GetAnalysisByActor:input_type -> connected_account.data_analytics.GetAnalysisByActorRequest
	2,  // 11: connected_account.data_analytics.DataAnalytics.InitiateAnalysis:input_type -> connected_account.data_analytics.InitiateAnalysisRequest
	4,  // 12: connected_account.data_analytics.DataAnalytics.GetAnalysisStatus:input_type -> connected_account.data_analytics.GetAnalysisStatusRequest
	6,  // 13: connected_account.data_analytics.DataAnalytics.GetAnalysis:input_type -> connected_account.data_analytics.GetAnalysisRequest
	1,  // 14: connected_account.data_analytics.DataAnalytics.GetAnalysisByActor:output_type -> connected_account.data_analytics.GetAnalysisByActorResponse
	3,  // 15: connected_account.data_analytics.DataAnalytics.InitiateAnalysis:output_type -> connected_account.data_analytics.InitiateAnalysisResponse
	5,  // 16: connected_account.data_analytics.DataAnalytics.GetAnalysisStatus:output_type -> connected_account.data_analytics.GetAnalysisStatusResponse
	7,  // 17: connected_account.data_analytics.DataAnalytics.GetAnalysis:output_type -> connected_account.data_analytics.GetAnalysisResponse
	14, // [14:18] is the sub-list for method output_type
	10, // [10:14] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_api_connected_account_data_analytics_service_proto_init() }
func file_api_connected_account_data_analytics_service_proto_init() {
	if File_api_connected_account_data_analytics_service_proto != nil {
		return
	}
	file_api_connected_account_data_analytics_analysis_attempt_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_connected_account_data_analytics_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisByActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_data_analytics_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisByActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_data_analytics_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateAnalysisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_data_analytics_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitiateAnalysisResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_data_analytics_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_data_analytics_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_data_analytics_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_connected_account_data_analytics_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAnalysisResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_connected_account_data_analytics_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_connected_account_data_analytics_service_proto_goTypes,
		DependencyIndexes: file_api_connected_account_data_analytics_service_proto_depIdxs,
		MessageInfos:      file_api_connected_account_data_analytics_service_proto_msgTypes,
	}.Build()
	File_api_connected_account_data_analytics_service_proto = out.File
	file_api_connected_account_data_analytics_service_proto_rawDesc = nil
	file_api_connected_account_data_analytics_service_proto_goTypes = nil
	file_api_connected_account_data_analytics_service_proto_depIdxs = nil
}
