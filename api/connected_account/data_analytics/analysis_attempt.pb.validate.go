// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/connected_account/data_analytics/analysis_attempt.proto

package data_analytics

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = typesv2.EmploymentType(0)
)

// Validate checks the field values on AnalysisAttempt with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AnalysisAttempt) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnalysisAttempt with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnalysisAttemptMultiError, or nil if none found.
func (m *AnalysisAttempt) ValidateAll() error {
	return m.validate(true)
}

func (m *AnalysisAttempt) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for ClientReqId

	// no validation rules for OrchestrationId

	// no validation rules for ActorId

	if all {
		switch v := interface{}(m.GetRequestParams()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisAttemptValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisAttemptValidationError{
					field:  "RequestParams",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestParams()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisAttemptValidationError{
				field:  "RequestParams",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisAttemptValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisAttemptValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisAttemptValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisAttemptValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisAttemptValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisAttemptValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisAttemptValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AnalysisAttemptMultiError(errors)
	}

	return nil
}

// AnalysisAttemptMultiError is an error wrapping multiple validation errors
// returned by AnalysisAttempt.ValidateAll() if the designated constraints
// aren't met.
type AnalysisAttemptMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalysisAttemptMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalysisAttemptMultiError) AllErrors() []error { return m }

// AnalysisAttemptValidationError is the validation error returned by
// AnalysisAttempt.Validate if the designated constraints aren't met.
type AnalysisAttemptValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalysisAttemptValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalysisAttemptValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalysisAttemptValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalysisAttemptValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalysisAttemptValidationError) ErrorName() string { return "AnalysisAttemptValidationError" }

// Error satisfies the builtin error interface
func (e AnalysisAttemptValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalysisAttempt.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalysisAttemptValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalysisAttemptValidationError{}

// Validate checks the field values on AnalysisRequestParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AnalysisRequestParams) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AnalysisRequestParams with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AnalysisRequestParamsMultiError, or nil if none found.
func (m *AnalysisRequestParams) ValidateAll() error {
	return m.validate(true)
}

func (m *AnalysisRequestParams) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDataExchangeRecord()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AnalysisRequestParamsValidationError{
					field:  "DataExchangeRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AnalysisRequestParamsValidationError{
					field:  "DataExchangeRecord",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDataExchangeRecord()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AnalysisRequestParamsValidationError{
				field:  "DataExchangeRecord",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EmploymentType

	// no validation rules for OrganisationName

	if len(errors) > 0 {
		return AnalysisRequestParamsMultiError(errors)
	}

	return nil
}

// AnalysisRequestParamsMultiError is an error wrapping multiple validation
// errors returned by AnalysisRequestParams.ValidateAll() if the designated
// constraints aren't met.
type AnalysisRequestParamsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AnalysisRequestParamsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AnalysisRequestParamsMultiError) AllErrors() []error { return m }

// AnalysisRequestParamsValidationError is the validation error returned by
// AnalysisRequestParams.Validate if the designated constraints aren't met.
type AnalysisRequestParamsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AnalysisRequestParamsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AnalysisRequestParamsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AnalysisRequestParamsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AnalysisRequestParamsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AnalysisRequestParamsValidationError) ErrorName() string {
	return "AnalysisRequestParamsValidationError"
}

// Error satisfies the builtin error interface
func (e AnalysisRequestParamsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAnalysisRequestParams.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AnalysisRequestParamsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AnalysisRequestParamsValidationError{}
