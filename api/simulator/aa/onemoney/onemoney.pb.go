// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/simulator/aa/onemoney/onemoney.proto

package onemoney

import (
	onemoney "github.com/epifi/gamma/api/vendors/aa/onemoney"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_simulator_aa_onemoney_onemoney_proto protoreflect.FileDescriptor

var file_api_simulator_aa_onemoney_onemoney_proto_rawDesc = []byte{
	0x0a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f,
	0x61, 0x61, 0x2f, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2f, 0x6f, 0x6e, 0x65, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x61, 0x61, 0x2e, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x61,
	0x61, 0x2f, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x61, 0x61, 0x2f, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65,
	0x79, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xa6, 0x05, 0x0a, 0x08, 0x4f,
	0x6e, 0x65, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x12, 0x78, 0x0a, 0x0b, 0x50, 0x6f, 0x73, 0x74, 0x43,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x61, 0x61, 0x2e, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x50, 0x6f, 0x73,
	0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x28, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61, 0x2e, 0x6f, 0x6e, 0x65,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x16, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x10, 0x3a, 0x01, 0x2a, 0x22, 0x0b, 0x2f, 0x61, 0x61, 0x2f, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x12, 0x9c, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x61, 0x61, 0x2e, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61,
	0x61, 0x2e, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x2f, 0x61, 0x61,
	0x2f, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x2f,
	0x7b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x68, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x7d,
	0x12, 0x97, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x41,
	0x72, 0x74, 0x65, 0x66, 0x61, 0x63, 0x74, 0x12, 0x2e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x61, 0x61, 0x2e, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x72, 0x74, 0x65, 0x66, 0x61, 0x63, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x61, 0x61, 0x2e, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x41, 0x72, 0x74, 0x65, 0x66, 0x61, 0x63, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x20, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1a,
	0x12, 0x18, 0x2f, 0x61, 0x61, 0x2f, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x2f, 0x7b, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x7d, 0x12, 0x6b, 0x0a, 0x06, 0x46, 0x49,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61,
	0x61, 0x2e, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x46, 0x49, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x73, 0x2e, 0x61, 0x61, 0x2e, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x46,
	0x49, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x19, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x13, 0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x61, 0x61, 0x2f, 0x46, 0x49, 0x2f,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x7a, 0x0a, 0x07, 0x46, 0x49, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x12, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x61, 0x61, 0x2e,
	0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x46, 0x49, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x61, 0x61, 0x2e, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x46, 0x49,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x3a, 0x01, 0x2a, 0x22, 0x19, 0x2f, 0x61, 0x61, 0x2f, 0x46, 0x49,
	0x2f, 0x66, 0x65, 0x74, 0x63, 0x68, 0x2f, 0x7b, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f,
	0x69, 0x64, 0x7d, 0x42, 0x64, 0x0a, 0x30, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x61, 0x61, 0x2e, 0x6f,
	0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x61, 0x61,
	0x2f, 0x6f, 0x6e, 0x65, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var file_api_simulator_aa_onemoney_onemoney_proto_goTypes = []interface{}{
	(*onemoney.PostConsentRequest)(nil),         // 0: vendors.aa.onemoney.PostConsentRequest
	(*onemoney.GetConsentStatusRequest)(nil),    // 1: vendors.aa.onemoney.GetConsentStatusRequest
	(*onemoney.GetConsentArtefactRequest)(nil),  // 2: vendors.aa.onemoney.GetConsentArtefactRequest
	(*onemoney.FIDataRequest)(nil),              // 3: vendors.aa.onemoney.FIDataRequest
	(*onemoney.FIFetchRequest)(nil),             // 4: vendors.aa.onemoney.FIFetchRequest
	(*onemoney.PostConsentResponse)(nil),        // 5: vendors.aa.onemoney.PostConsentResponse
	(*onemoney.GetConsentStatusResponse)(nil),   // 6: vendors.aa.onemoney.GetConsentStatusResponse
	(*onemoney.GetConsentArtefactResponse)(nil), // 7: vendors.aa.onemoney.GetConsentArtefactResponse
	(*onemoney.FIFetchResponse)(nil),            // 8: vendors.aa.onemoney.FIFetchResponse
}
var file_api_simulator_aa_onemoney_onemoney_proto_depIdxs = []int32{
	0, // 0: simulator.aa.onemoney.OneMoney.PostConsent:input_type -> vendors.aa.onemoney.PostConsentRequest
	1, // 1: simulator.aa.onemoney.OneMoney.GetConsentStatus:input_type -> vendors.aa.onemoney.GetConsentStatusRequest
	2, // 2: simulator.aa.onemoney.OneMoney.GetConsentArtefact:input_type -> vendors.aa.onemoney.GetConsentArtefactRequest
	3, // 3: simulator.aa.onemoney.OneMoney.FIData:input_type -> vendors.aa.onemoney.FIDataRequest
	4, // 4: simulator.aa.onemoney.OneMoney.FIFetch:input_type -> vendors.aa.onemoney.FIFetchRequest
	5, // 5: simulator.aa.onemoney.OneMoney.PostConsent:output_type -> vendors.aa.onemoney.PostConsentResponse
	6, // 6: simulator.aa.onemoney.OneMoney.GetConsentStatus:output_type -> vendors.aa.onemoney.GetConsentStatusResponse
	7, // 7: simulator.aa.onemoney.OneMoney.GetConsentArtefact:output_type -> vendors.aa.onemoney.GetConsentArtefactResponse
	3, // 8: simulator.aa.onemoney.OneMoney.FIData:output_type -> vendors.aa.onemoney.FIDataRequest
	8, // 9: simulator.aa.onemoney.OneMoney.FIFetch:output_type -> vendors.aa.onemoney.FIFetchResponse
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_simulator_aa_onemoney_onemoney_proto_init() }
func file_api_simulator_aa_onemoney_onemoney_proto_init() {
	if File_api_simulator_aa_onemoney_onemoney_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_simulator_aa_onemoney_onemoney_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_simulator_aa_onemoney_onemoney_proto_goTypes,
		DependencyIndexes: file_api_simulator_aa_onemoney_onemoney_proto_depIdxs,
	}.Build()
	File_api_simulator_aa_onemoney_onemoney_proto = out.File
	file_api_simulator_aa_onemoney_onemoney_proto_rawDesc = nil
	file_api_simulator_aa_onemoney_onemoney_proto_goTypes = nil
	file_api_simulator_aa_onemoney_onemoney_proto_depIdxs = nil
}
