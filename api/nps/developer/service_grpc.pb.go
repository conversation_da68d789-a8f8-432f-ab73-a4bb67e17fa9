// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/nps/developer/service.proto

package developer

import (
	context "context"
	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	NpsDbStates_GetEntityList_FullMethodName    = "/nps.developer.NpsDbStates/GetEntityList"
	NpsDbStates_GetParameterList_FullMethodName = "/nps.developer.NpsDbStates/GetParameterList"
	NpsDbStates_GetData_FullMethodName          = "/nps.developer.NpsDbStates/GetData"
)

// NpsDbStatesClient is the client API for NpsDbStates service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NpsDbStatesClient interface {
	// GetEntityList returns the list of available NPS entities that can be queried.
	// Used to populate entity options in the UI or client applications.
	GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error)
	// GetParameterList returns the list of parameters required for a specific NPS entity.
	// Used to determine input fields needed to fetch data for the selected entity.
	GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error)
	// GetData fetches data for a specific NPS entity using provided parameters.
	// Returns the entity data as a JSON string for debugging or analysis.
	GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error)
}

type npsDbStatesClient struct {
	cc grpc.ClientConnInterface
}

func NewNpsDbStatesClient(cc grpc.ClientConnInterface) NpsDbStatesClient {
	return &npsDbStatesClient{cc}
}

func (c *npsDbStatesClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	out := new(db_state.GetEntityListResponse)
	err := c.cc.Invoke(ctx, NpsDbStates_GetEntityList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *npsDbStatesClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	out := new(db_state.GetParameterListResponse)
	err := c.cc.Invoke(ctx, NpsDbStates_GetParameterList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *npsDbStatesClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	out := new(db_state.GetDataResponse)
	err := c.cc.Invoke(ctx, NpsDbStates_GetData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NpsDbStatesServer is the server API for NpsDbStates service.
// All implementations should embed UnimplementedNpsDbStatesServer
// for forward compatibility
type NpsDbStatesServer interface {
	// GetEntityList returns the list of available NPS entities that can be queried.
	// Used to populate entity options in the UI or client applications.
	GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error)
	// GetParameterList returns the list of parameters required for a specific NPS entity.
	// Used to determine input fields needed to fetch data for the selected entity.
	GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error)
	// GetData fetches data for a specific NPS entity using provided parameters.
	// Returns the entity data as a JSON string for debugging or analysis.
	GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error)
}

// UnimplementedNpsDbStatesServer should be embedded to have forward compatible implementations.
type UnimplementedNpsDbStatesServer struct {
}

func (UnimplementedNpsDbStatesServer) GetEntityList(context.Context, *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEntityList not implemented")
}
func (UnimplementedNpsDbStatesServer) GetParameterList(context.Context, *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetParameterList not implemented")
}
func (UnimplementedNpsDbStatesServer) GetData(context.Context, *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetData not implemented")
}

// UnsafeNpsDbStatesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NpsDbStatesServer will
// result in compilation errors.
type UnsafeNpsDbStatesServer interface {
	mustEmbedUnimplementedNpsDbStatesServer()
}

func RegisterNpsDbStatesServer(s grpc.ServiceRegistrar, srv NpsDbStatesServer) {
	s.RegisterService(&NpsDbStates_ServiceDesc, srv)
}

func _NpsDbStates_GetEntityList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetEntityListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NpsDbStatesServer).GetEntityList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NpsDbStates_GetEntityList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NpsDbStatesServer).GetEntityList(ctx, req.(*db_state.GetEntityListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NpsDbStates_GetParameterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetParameterListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NpsDbStatesServer).GetParameterList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NpsDbStates_GetParameterList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NpsDbStatesServer).GetParameterList(ctx, req.(*db_state.GetParameterListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NpsDbStates_GetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(db_state.GetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NpsDbStatesServer).GetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: NpsDbStates_GetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NpsDbStatesServer).GetData(ctx, req.(*db_state.GetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NpsDbStates_ServiceDesc is the grpc.ServiceDesc for NpsDbStates service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NpsDbStates_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "nps.developer.NpsDbStates",
	HandlerType: (*NpsDbStatesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetEntityList",
			Handler:    _NpsDbStates_GetEntityList_Handler,
		},
		{
			MethodName: "GetParameterList",
			Handler:    _NpsDbStates_GetParameterList_Handler,
		},
		{
			MethodName: "GetData",
			Handler:    _NpsDbStates_GetData_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/nps/developer/service.proto",
}
