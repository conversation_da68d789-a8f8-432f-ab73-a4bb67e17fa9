// Code generated by MockGen. DO NOT EDIT.
// Source: api/nps/developer/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	db_state "github.com/epifi/gamma/api/cx/developer/db_state"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockNpsDbStatesClient is a mock of NpsDbStatesClient interface.
type MockNpsDbStatesClient struct {
	ctrl     *gomock.Controller
	recorder *MockNpsDbStatesClientMockRecorder
}

// MockNpsDbStatesClientMockRecorder is the mock recorder for MockNpsDbStatesClient.
type MockNpsDbStatesClientMockRecorder struct {
	mock *MockNpsDbStatesClient
}

// NewMockNpsDbStatesClient creates a new mock instance.
func NewMockNpsDbStatesClient(ctrl *gomock.Controller) *MockNpsDbStatesClient {
	mock := &MockNpsDbStatesClient{ctrl: ctrl}
	mock.recorder = &MockNpsDbStatesClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNpsDbStatesClient) EXPECT() *MockNpsDbStatesClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockNpsDbStatesClient) GetData(ctx context.Context, in *db_state.GetDataRequest, opts ...grpc.CallOption) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockNpsDbStatesClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockNpsDbStatesClient)(nil).GetData), varargs...)
}

// GetEntityList mocks base method.
func (m *MockNpsDbStatesClient) GetEntityList(ctx context.Context, in *db_state.GetEntityListRequest, opts ...grpc.CallOption) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEntityList", varargs...)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockNpsDbStatesClientMockRecorder) GetEntityList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockNpsDbStatesClient)(nil).GetEntityList), varargs...)
}

// GetParameterList mocks base method.
func (m *MockNpsDbStatesClient) GetParameterList(ctx context.Context, in *db_state.GetParameterListRequest, opts ...grpc.CallOption) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetParameterList", varargs...)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockNpsDbStatesClientMockRecorder) GetParameterList(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockNpsDbStatesClient)(nil).GetParameterList), varargs...)
}

// MockNpsDbStatesServer is a mock of NpsDbStatesServer interface.
type MockNpsDbStatesServer struct {
	ctrl     *gomock.Controller
	recorder *MockNpsDbStatesServerMockRecorder
}

// MockNpsDbStatesServerMockRecorder is the mock recorder for MockNpsDbStatesServer.
type MockNpsDbStatesServerMockRecorder struct {
	mock *MockNpsDbStatesServer
}

// NewMockNpsDbStatesServer creates a new mock instance.
func NewMockNpsDbStatesServer(ctrl *gomock.Controller) *MockNpsDbStatesServer {
	mock := &MockNpsDbStatesServer{ctrl: ctrl}
	mock.recorder = &MockNpsDbStatesServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockNpsDbStatesServer) EXPECT() *MockNpsDbStatesServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockNpsDbStatesServer) GetData(arg0 context.Context, arg1 *db_state.GetDataRequest) (*db_state.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockNpsDbStatesServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockNpsDbStatesServer)(nil).GetData), arg0, arg1)
}

// GetEntityList mocks base method.
func (m *MockNpsDbStatesServer) GetEntityList(arg0 context.Context, arg1 *db_state.GetEntityListRequest) (*db_state.GetEntityListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEntityList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetEntityListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEntityList indicates an expected call of GetEntityList.
func (mr *MockNpsDbStatesServerMockRecorder) GetEntityList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEntityList", reflect.TypeOf((*MockNpsDbStatesServer)(nil).GetEntityList), arg0, arg1)
}

// GetParameterList mocks base method.
func (m *MockNpsDbStatesServer) GetParameterList(arg0 context.Context, arg1 *db_state.GetParameterListRequest) (*db_state.GetParameterListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParameterList", arg0, arg1)
	ret0, _ := ret[0].(*db_state.GetParameterListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParameterList indicates an expected call of GetParameterList.
func (mr *MockNpsDbStatesServerMockRecorder) GetParameterList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParameterList", reflect.TypeOf((*MockNpsDbStatesServer)(nil).GetParameterList), arg0, arg1)
}

// MockUnsafeNpsDbStatesServer is a mock of UnsafeNpsDbStatesServer interface.
type MockUnsafeNpsDbStatesServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeNpsDbStatesServerMockRecorder
}

// MockUnsafeNpsDbStatesServerMockRecorder is the mock recorder for MockUnsafeNpsDbStatesServer.
type MockUnsafeNpsDbStatesServerMockRecorder struct {
	mock *MockUnsafeNpsDbStatesServer
}

// NewMockUnsafeNpsDbStatesServer creates a new mock instance.
func NewMockUnsafeNpsDbStatesServer(ctrl *gomock.Controller) *MockUnsafeNpsDbStatesServer {
	mock := &MockUnsafeNpsDbStatesServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeNpsDbStatesServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeNpsDbStatesServer) EXPECT() *MockUnsafeNpsDbStatesServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedNpsDbStatesServer mocks base method.
func (m *MockUnsafeNpsDbStatesServer) mustEmbedUnimplementedNpsDbStatesServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedNpsDbStatesServer")
}

// mustEmbedUnimplementedNpsDbStatesServer indicates an expected call of mustEmbedUnimplementedNpsDbStatesServer.
func (mr *MockUnsafeNpsDbStatesServerMockRecorder) mustEmbedUnimplementedNpsDbStatesServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedNpsDbStatesServer", reflect.TypeOf((*MockUnsafeNpsDbStatesServer)(nil).mustEmbedUnimplementedNpsDbStatesServer))
}
