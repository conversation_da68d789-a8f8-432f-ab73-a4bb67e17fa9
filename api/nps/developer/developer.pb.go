// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/nps/developer/developer.proto

package developer

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// NpsEntity represents the different types of NPS (National Pension System) entities.
// These entities can be queried or managed in the NPS developer APIs.
type NpsEntity int32

const (
	// Default value for NpsEntity. Represents an unspecified or unknown entity.
	NpsEntity_NPS_UNSPECIFIED NpsEntity = 0
	// NAV (Net Asset Value) details entity in NPS.
	// Used to fetch NAV history for a scheme on a specific date.
	NpsEntity_NPS_NAV_DETAILS NpsEntity = 1
	// Scheme details entity in NPS.
	// Used to fetch information about NPS schemes.
	NpsEntity_NPS_SCHEME_DETAILS NpsEntity = 2
	// PFM (Pension Fund Manager) details entity in NPS.
	// Used to fetch information about pension fund managers.
	NpsEntity_NPS_PFM_DETAILS NpsEntity = 3
)

// Enum value maps for NpsEntity.
var (
	NpsEntity_name = map[int32]string{
		0: "NPS_UNSPECIFIED",
		1: "NPS_NAV_DETAILS",
		2: "NPS_SCHEME_DETAILS",
		3: "NPS_PFM_DETAILS",
	}
	NpsEntity_value = map[string]int32{
		"NPS_UNSPECIFIED":    0,
		"NPS_NAV_DETAILS":    1,
		"NPS_SCHEME_DETAILS": 2,
		"NPS_PFM_DETAILS":    3,
	}
)

func (x NpsEntity) Enum() *NpsEntity {
	p := new(NpsEntity)
	*p = x
	return p
}

func (x NpsEntity) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NpsEntity) Descriptor() protoreflect.EnumDescriptor {
	return file_api_nps_developer_developer_proto_enumTypes[0].Descriptor()
}

func (NpsEntity) Type() protoreflect.EnumType {
	return &file_api_nps_developer_developer_proto_enumTypes[0]
}

func (x NpsEntity) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NpsEntity.Descriptor instead.
func (NpsEntity) EnumDescriptor() ([]byte, []int) {
	return file_api_nps_developer_developer_proto_rawDescGZIP(), []int{0}
}

var File_api_nps_developer_developer_proto protoreflect.FileDescriptor

var file_api_nps_developer_developer_proto_rawDesc = []byte{
	0x0a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x70, 0x73, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x72, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x6e, 0x70, 0x73, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x2a, 0x62, 0x0a, 0x09, 0x4e, 0x70, 0x73, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12,
	0x13, 0x0a, 0x0f, 0x4e, 0x50, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x4e, 0x50, 0x53, 0x5f, 0x4e, 0x41, 0x56, 0x5f,
	0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x4e, 0x50, 0x53,
	0x5f, 0x53, 0x43, 0x48, 0x45, 0x4d, 0x45, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x02, 0x12, 0x13, 0x0a, 0x0f, 0x4e, 0x50, 0x53, 0x5f, 0x50, 0x46, 0x4d, 0x5f, 0x44, 0x45, 0x54,
	0x41, 0x49, 0x4c, 0x53, 0x10, 0x03, 0x42, 0x54, 0x0a, 0x28, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x6e, 0x70, 0x73, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x5a, 0x28, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e,
	0x70, 0x73, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_nps_developer_developer_proto_rawDescOnce sync.Once
	file_api_nps_developer_developer_proto_rawDescData = file_api_nps_developer_developer_proto_rawDesc
)

func file_api_nps_developer_developer_proto_rawDescGZIP() []byte {
	file_api_nps_developer_developer_proto_rawDescOnce.Do(func() {
		file_api_nps_developer_developer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_nps_developer_developer_proto_rawDescData)
	})
	return file_api_nps_developer_developer_proto_rawDescData
}

var file_api_nps_developer_developer_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_nps_developer_developer_proto_goTypes = []interface{}{
	(NpsEntity)(0), // 0: nps.developer.NpsEntity
}
var file_api_nps_developer_developer_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_nps_developer_developer_proto_init() }
func file_api_nps_developer_developer_proto_init() {
	if File_api_nps_developer_developer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_nps_developer_developer_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_nps_developer_developer_proto_goTypes,
		DependencyIndexes: file_api_nps_developer_developer_proto_depIdxs,
		EnumInfos:         file_api_nps_developer_developer_proto_enumTypes,
	}.Build()
	File_api_nps_developer_developer_proto = out.File
	file_api_nps_developer_developer_proto_rawDesc = nil
	file_api_nps_developer_developer_proto_goTypes = nil
	file_api_nps_developer_developer_proto_depIdxs = nil
}
