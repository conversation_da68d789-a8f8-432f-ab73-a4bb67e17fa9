//go:generate gen_sql -types=ProductType,ProductStatus

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/product/enums.proto

package product

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ProductType int32

const (
	ProductType_PRODUCT_TYPE_UNSPECIFIED     ProductType = 0
	ProductType_PRODUCT_TYPE_SAVINGS_ACCOUNT ProductType = 1
	ProductType_PRODUCT_TYPE_CREDIT_CARD     ProductType = 2
	ProductType_PRODUCT_TYPE_PERSONAL_LOANS  ProductType = 3
	ProductType_PRODUCT_TYPE_TPAP            ProductType = 4
	ProductType_PRODUCT_TYPE_USSTOCKS        ProductType = 5
	// Wealth Analyser product would be active if user onboards for wealth analyser product and has completed
	// its onboarding steps which includes collection of certain data, eg: MF, PF etc
	// Once active this would never become inactive and relevant consumers might need to check status of other
	// products if they want to customise their experience for SA user or SA+WA user
	ProductType_PRODUCT_TYPE_WEALTH_ANALYSER ProductType = 6
	// Fixed Deposit product would be active if user onboards for Fixed Deposit product
	ProductType_PRODUCT_TYPE_FIXED_DEPOSIT ProductType = 7
	// Smart Deposit product would be active if user onboards for Smart Deposit product
	ProductType_PRODUCT_TYPE_SMART_DEPOSIT ProductType = 8
)

// Enum value maps for ProductType.
var (
	ProductType_name = map[int32]string{
		0: "PRODUCT_TYPE_UNSPECIFIED",
		1: "PRODUCT_TYPE_SAVINGS_ACCOUNT",
		2: "PRODUCT_TYPE_CREDIT_CARD",
		3: "PRODUCT_TYPE_PERSONAL_LOANS",
		4: "PRODUCT_TYPE_TPAP",
		5: "PRODUCT_TYPE_USSTOCKS",
		6: "PRODUCT_TYPE_WEALTH_ANALYSER",
		7: "PRODUCT_TYPE_FIXED_DEPOSIT",
		8: "PRODUCT_TYPE_SMART_DEPOSIT",
	}
	ProductType_value = map[string]int32{
		"PRODUCT_TYPE_UNSPECIFIED":     0,
		"PRODUCT_TYPE_SAVINGS_ACCOUNT": 1,
		"PRODUCT_TYPE_CREDIT_CARD":     2,
		"PRODUCT_TYPE_PERSONAL_LOANS":  3,
		"PRODUCT_TYPE_TPAP":            4,
		"PRODUCT_TYPE_USSTOCKS":        5,
		"PRODUCT_TYPE_WEALTH_ANALYSER": 6,
		"PRODUCT_TYPE_FIXED_DEPOSIT":   7,
		"PRODUCT_TYPE_SMART_DEPOSIT":   8,
	}
)

func (x ProductType) Enum() *ProductType {
	p := new(ProductType)
	*p = x
	return p
}

func (x ProductType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProductType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_product_enums_proto_enumTypes[0].Descriptor()
}

func (ProductType) Type() protoreflect.EnumType {
	return &file_api_product_enums_proto_enumTypes[0]
}

func (x ProductType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProductType.Descriptor instead.
func (ProductType) EnumDescriptor() ([]byte, []int) {
	return file_api_product_enums_proto_rawDescGZIP(), []int{0}
}

// ProductStatus enum is used to denote the current status of the product
type ProductStatus int32

const (
	ProductStatus_PRODUCT_STATUS_UNSPECIFIED ProductStatus = 0
	// Onboarding is in progress for the feature, this shall be used when no user input is needed, and it is in progress
	ProductStatus_PRODUCT_STATUS_ONBOARDING_IN_PROGRESS ProductStatus = 1
	// Failure is used when onboarding has failed in between
	ProductStatus_PRODUCT_STATUS_ONBOARDING_FAILURE ProductStatus = 2
	// Active is used when onboarding is completed for a feature, and it is ready to use
	ProductStatus_PRODUCT_STATUS_ACTIVE ProductStatus = 3
	// Inactive is used when onboarding is successfully complete, but the feature is unavailable for use.
	// e.g. SA got closed
	ProductStatus_PRODUCT_STATUS_INACTIVE ProductStatus = 4
	// Rejected is used when the user has been rejected for the product
	ProductStatus_PRODUCT_STATUS_REJECTED ProductStatus = 5
)

// Enum value maps for ProductStatus.
var (
	ProductStatus_name = map[int32]string{
		0: "PRODUCT_STATUS_UNSPECIFIED",
		1: "PRODUCT_STATUS_ONBOARDING_IN_PROGRESS",
		2: "PRODUCT_STATUS_ONBOARDING_FAILURE",
		3: "PRODUCT_STATUS_ACTIVE",
		4: "PRODUCT_STATUS_INACTIVE",
		5: "PRODUCT_STATUS_REJECTED",
	}
	ProductStatus_value = map[string]int32{
		"PRODUCT_STATUS_UNSPECIFIED":            0,
		"PRODUCT_STATUS_ONBOARDING_IN_PROGRESS": 1,
		"PRODUCT_STATUS_ONBOARDING_FAILURE":     2,
		"PRODUCT_STATUS_ACTIVE":                 3,
		"PRODUCT_STATUS_INACTIVE":               4,
		"PRODUCT_STATUS_REJECTED":               5,
	}
)

func (x ProductStatus) Enum() *ProductStatus {
	p := new(ProductStatus)
	*p = x
	return p
}

func (x ProductStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ProductStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_product_enums_proto_enumTypes[1].Descriptor()
}

func (ProductStatus) Type() protoreflect.EnumType {
	return &file_api_product_enums_proto_enumTypes[1]
}

func (x ProductStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ProductStatus.Descriptor instead.
func (ProductStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_product_enums_proto_rawDescGZIP(), []int{1}
}

var File_api_product_enums_proto protoreflect.FileDescriptor

var file_api_product_enums_proto_rawDesc = []byte{
	0x0a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75,
	0x63, 0x74, 0x2a, 0xa0, 0x02, 0x0a, 0x0b, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x20, 0x0a, 0x1c, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x10, 0x02,
	0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x53, 0x10,
	0x03, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x54, 0x50, 0x41, 0x50, 0x10, 0x04, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x52, 0x4f, 0x44,
	0x55, 0x43, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b,
	0x53, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x41, 0x4e, 0x41, 0x4c, 0x59,
	0x53, 0x45, 0x52, 0x10, 0x06, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x58, 0x45, 0x44, 0x5f, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x10, 0x07, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x4d, 0x41, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x50, 0x4f,
	0x53, 0x49, 0x54, 0x10, 0x08, 0x2a, 0xd6, 0x01, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x29, 0x0a, 0x25, 0x50, 0x52, 0x4f, 0x44, 0x55,
	0x43, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x52, 0x4f,
	0x44, 0x55, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10,
	0x04, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x52, 0x4f, 0x44, 0x55, 0x43, 0x54, 0x5f, 0x53, 0x54, 0x41,
	0x54, 0x55, 0x53, 0x5f, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x05, 0x42, 0x48,
	0x0a, 0x22, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5a, 0x22, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_product_enums_proto_rawDescOnce sync.Once
	file_api_product_enums_proto_rawDescData = file_api_product_enums_proto_rawDesc
)

func file_api_product_enums_proto_rawDescGZIP() []byte {
	file_api_product_enums_proto_rawDescOnce.Do(func() {
		file_api_product_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_product_enums_proto_rawDescData)
	})
	return file_api_product_enums_proto_rawDescData
}

var file_api_product_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_api_product_enums_proto_goTypes = []interface{}{
	(ProductType)(0),   // 0: product.ProductType
	(ProductStatus)(0), // 1: product.ProductStatus
}
var file_api_product_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_product_enums_proto_init() }
func file_api_product_enums_proto_init() {
	if File_api_product_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_product_enums_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_product_enums_proto_goTypes,
		DependencyIndexes: file_api_product_enums_proto_depIdxs,
		EnumInfos:         file_api_product_enums_proto_enumTypes,
	}.Build()
	File_api_product_enums_proto = out.File
	file_api_product_enums_proto_rawDesc = nil
	file_api_product_enums_proto_goTypes = nil
	file_api_product_enums_proto_depIdxs = nil
}
