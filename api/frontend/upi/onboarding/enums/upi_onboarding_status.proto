syntax = "proto3";

package frontend.upi.onboarding.enums;

option go_package = "github.com/epifi/gamma/api/frontend/upi/onboarding/enums";
option java_package = "com.github.epifi.gamma.api.frontend.upi.onboarding.enums";

// Current status of the account onboarding.
// CREATED -> INITIATED -> LINKED/FAILED -> DELINKED_INITIATED -> DELINKED/MANUAL_INTERVNETION
// If the user drops in between entry will be marked as INVALID
enum UpiOnboardingStatus {
  UPI_ONBOARDING_STATUS_UNSPECIFIED = 0;
  // successfully linked or delinked account
  UPI_ONBOARDING_STATUS_SUCCESSFUL = 1;
  // failed to link or delink account
  UPI_ONBOARDING_STATUS_FAILED = 2;
  // account link/delink in progress
  UPI_ONBOARDING_STATUS_IN_PROGRESS = 3;
}
