syntax = "proto3";

package frontend.insights.epf;

import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights/epf";
option java_package = "com.github.epifi.gamma.api.frontend.insights.epf";

// LandingPageComponent represents different components in the EPF Insights landing screen.
message LandingPageComponent {
  oneof component_type {
    UanAccountComponent uan_account_component = 2; // EPF UAN Account component.
    api.typesv2.ui.IconTextComponent message = 3;
  }
}

// EpfSummary represents the EPF summary component.
message EpfSummary {
  api.typesv2.ui.IconTextComponent title = 1; // Title of the EPF summary.
  api.typesv2.ui.IconTextComponent total_value = 2; // IconTextComponent representing total EPF value.
  api.typesv2.ui.IconTextComponent status_info = 3; // Status text associated with the summary.
  api.typesv2.ui.IconTextComponent primary_cta = 4; // Primary call-to-action button.
}

// UanAccountComponent represents a component with a title, account tiles and a primary CTA.
// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=1-5604&mode=design&t=LKyami7VmDYgfjXF-0
message UanAccountComponent {
  // Title of the UAN Account component.
  api.typesv2.ui.IconTextComponent title = 1;
  repeated AccountTile account_tiles = 2;
}

// AccountTile represents a tile with primary icon, heading, title, sub-title, and tag.
message AccountTile {
  api.typesv2.common.VisualElement primary_icon = 1;
  // heading represents the total value in EPF dashboard
  // Ex; 2,00,000
  api.typesv2.ui.IconTextComponent heading = 2;
  // title represents the company name
  // Ex; Epifi Technologies Private Limited
  api.typesv2.ui.IconTextComponent title = 3;
  // Example of sub_title; Mar 2020 - Dec 2022
  api.typesv2.ui.IconTextComponent sub_title = 4;
  // Example of tags; ACTIVE, SYNC IN PROGRESS
  api.typesv2.ui.IconTextComponent tag = 5;
}

