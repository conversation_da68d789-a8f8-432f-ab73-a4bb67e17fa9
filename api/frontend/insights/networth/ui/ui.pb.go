// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/insights/networth/ui/ui.proto

package ui

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	enums "github.com/epifi/gamma/api/frontend/insights/networth/enums"
	ui1 "github.com/epifi/gamma/api/frontend/investment/ui"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	properties "github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	sections "github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type WidgetTypeV2 int32

const (
	WidgetTypeV2_WIDGET_TYPE_V2_UNSPECIFIED    WidgetTypeV2 = 0
	WidgetTypeV2_WIDGET_TYPE_V2_WEALTH_BUILDER WidgetTypeV2 = 1
)

// Enum value maps for WidgetTypeV2.
var (
	WidgetTypeV2_name = map[int32]string{
		0: "WIDGET_TYPE_V2_UNSPECIFIED",
		1: "WIDGET_TYPE_V2_WEALTH_BUILDER",
	}
	WidgetTypeV2_value = map[string]int32{
		"WIDGET_TYPE_V2_UNSPECIFIED":    0,
		"WIDGET_TYPE_V2_WEALTH_BUILDER": 1,
	}
)

func (x WidgetTypeV2) Enum() *WidgetTypeV2 {
	p := new(WidgetTypeV2)
	*p = x
	return p
}

func (x WidgetTypeV2) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WidgetTypeV2) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_ui_ui_proto_enumTypes[0].Descriptor()
}

func (WidgetTypeV2) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_ui_ui_proto_enumTypes[0]
}

func (x WidgetTypeV2) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WidgetTypeV2.Descriptor instead.
func (WidgetTypeV2) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{0}
}

type CacheControlV2 int32

const (
	// // If cache control is unspecified then client should not use cache
	CacheControlV2_CACHE_CONTROL_V2_UNSPECIFIED CacheControlV2 = 0
	// // Client is supposed to read from cache for overall computation.
	CacheControlV2_CACHE_CONTROL_V2_USE_CACHE CacheControlV2 = 1
)

// Enum value maps for CacheControlV2.
var (
	CacheControlV2_name = map[int32]string{
		0: "CACHE_CONTROL_V2_UNSPECIFIED",
		1: "CACHE_CONTROL_V2_USE_CACHE",
	}
	CacheControlV2_value = map[string]int32{
		"CACHE_CONTROL_V2_UNSPECIFIED": 0,
		"CACHE_CONTROL_V2_USE_CACHE":   1,
	}
)

func (x CacheControlV2) Enum() *CacheControlV2 {
	p := new(CacheControlV2)
	*p = x
	return p
}

func (x CacheControlV2) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CacheControlV2) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_ui_ui_proto_enumTypes[1].Descriptor()
}

func (CacheControlV2) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_ui_ui_proto_enumTypes[1]
}

func (x CacheControlV2) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CacheControlV2.Descriptor instead.
func (CacheControlV2) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{1}
}

type WidgetStateV2 int32

const (
	WidgetStateV2_WIDGET_STATE_V2_UNSPECIFIED WidgetStateV2 = 0
	// // This refers to the state where user had not successfully completed the category process.
	WidgetStateV2_WIDGET_STATE_V2_UNINITIALIZED WidgetStateV2 = 1
	// // This refers to the state where the user has completed the category process successfully. The data can be zero or contain some value.
	// // If a widget is in initialised state and client has to use cache, then it should count this widget in category added.
	WidgetStateV2_WIDGET_STATE_V2_INITIALIZED WidgetStateV2 = 2
)

// Enum value maps for WidgetStateV2.
var (
	WidgetStateV2_name = map[int32]string{
		0: "WIDGET_STATE_V2_UNSPECIFIED",
		1: "WIDGET_STATE_V2_UNINITIALIZED",
		2: "WIDGET_STATE_V2_INITIALIZED",
	}
	WidgetStateV2_value = map[string]int32{
		"WIDGET_STATE_V2_UNSPECIFIED":   0,
		"WIDGET_STATE_V2_UNINITIALIZED": 1,
		"WIDGET_STATE_V2_INITIALIZED":   2,
	}
)

func (x WidgetStateV2) Enum() *WidgetStateV2 {
	p := new(WidgetStateV2)
	*p = x
	return p
}

func (x WidgetStateV2) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (WidgetStateV2) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_insights_networth_ui_ui_proto_enumTypes[2].Descriptor()
}

func (WidgetStateV2) Type() protoreflect.EnumType {
	return &file_api_frontend_insights_networth_ui_ui_proto_enumTypes[2]
}

func (x WidgetStateV2) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use WidgetStateV2.Descriptor instead.
func (WidgetStateV2) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{2}
}

// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46664&t=Zhxqm8mShovbvbVH-4
// Ref: https://drive.google.com/file/d/1mIzDVbBr8T3oEBdhyqhxWcA2wUXjdw7D/view?usp=drive_link
type WealthBuilderLandingComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WealthBuilderLandingDashboard *WealthBuilderLandingDashboard `protobuf:"bytes,1,opt,name=wealth_builder_landing_dashboard,json=wealthBuilderLandingDashboard,proto3" json:"wealth_builder_landing_dashboard,omitempty"`
	// primary cta at the footer of component, e.g. "Track bank balances", "Get Mutual Funds summary"
	// DEPRECATED : In support of  wealth_builder_landing_dashboard.footer_ctas [WealthBuilderLandingCta]
	//
	// Deprecated: Marked as deprecated in api/frontend/insights/networth/ui/ui.proto.
	FooterCta *ui.IconTextComponent `protobuf:"bytes,2,opt,name=footer_cta,json=footerCta,proto3" json:"footer_cta,omitempty"`
	// bg color of the dashboard
	BgColor *widget.BackgroundColour `protobuf:"bytes,3,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *WealthBuilderLandingComponent) Reset() {
	*x = WealthBuilderLandingComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthBuilderLandingComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthBuilderLandingComponent) ProtoMessage() {}

func (x *WealthBuilderLandingComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthBuilderLandingComponent.ProtoReflect.Descriptor instead.
func (*WealthBuilderLandingComponent) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{0}
}

func (x *WealthBuilderLandingComponent) GetWealthBuilderLandingDashboard() *WealthBuilderLandingDashboard {
	if x != nil {
		return x.WealthBuilderLandingDashboard
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/insights/networth/ui/ui.proto.
func (x *WealthBuilderLandingComponent) GetFooterCta() *ui.IconTextComponent {
	if x != nil {
		return x.FooterCta
	}
	return nil
}

func (x *WealthBuilderLandingComponent) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/%F0%9F%9B%A0%EF%B8%8F-Wealth-builder-2.0?node-id=8122-14943&t=syacNv3WDBOjOMCS-4
// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/%F0%9F%9B%A0%EF%B8%8F-Wealth-builder-2.0?node-id=8067-33174&t=syacNv3WDBOjOMCS-4
// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/%F0%9F%9B%A0%EF%B8%8F-Wealth-builder-2.0?node-id=8122-15146&t=syacNv3WDBOjOMCS-4
type WealthBuilderLandingCta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BgColor        *widget.BackgroundColour   `protobuf:"bytes,1,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	CornerRadius   int32                      `protobuf:"varint,2,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	BorderProperty *properties.BorderProperty `protobuf:"bytes,3,opt,name=border_property,json=borderProperty,proto3" json:"border_property,omitempty"`
	Icon           *common.VisualElement      `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	Title          *common.Text               `protobuf:"bytes,5,opt,name=title,proto3" json:"title,omitempty"`
	Tag            *ui.IconTextComponent      `protobuf:"bytes,6,opt,name=tag,proto3" json:"tag,omitempty"`
	IsPrimary      bool                       `protobuf:"varint,7,opt,name=is_primary,json=isPrimary,proto3" json:"is_primary,omitempty"` // If the cta is primary cta, then cta will take remaining space otherwise will wrap
	Deeplink       *deeplink.Deeplink         `protobuf:"bytes,8,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// Identifier based on this client will decide whether the user has already seen the banner for this cta or not
	Identifier string `protobuf:"bytes,9,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// If nil, client won't draw any arrow.
	// Note: There should be one arrow color and arrow color should be present if footer_banner is present
	ArrowColor *widget.BackgroundColour `protobuf:"bytes,10,opt,name=arrow_color,json=arrowColor,proto3" json:"arrow_color,omitempty"`
}

func (x *WealthBuilderLandingCta) Reset() {
	*x = WealthBuilderLandingCta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthBuilderLandingCta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthBuilderLandingCta) ProtoMessage() {}

func (x *WealthBuilderLandingCta) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthBuilderLandingCta.ProtoReflect.Descriptor instead.
func (*WealthBuilderLandingCta) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{1}
}

func (x *WealthBuilderLandingCta) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *WealthBuilderLandingCta) GetCornerRadius() int32 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *WealthBuilderLandingCta) GetBorderProperty() *properties.BorderProperty {
	if x != nil {
		return x.BorderProperty
	}
	return nil
}

func (x *WealthBuilderLandingCta) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *WealthBuilderLandingCta) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *WealthBuilderLandingCta) GetTag() *ui.IconTextComponent {
	if x != nil {
		return x.Tag
	}
	return nil
}

func (x *WealthBuilderLandingCta) GetIsPrimary() bool {
	if x != nil {
		return x.IsPrimary
	}
	return false
}

func (x *WealthBuilderLandingCta) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *WealthBuilderLandingCta) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

func (x *WealthBuilderLandingCta) GetArrowColor() *widget.BackgroundColour {
	if x != nil {
		return x.ArrowColor
	}
	return nil
}

// Ref: https://drive.google.com/file/d/1RGgTZd-dwqLGa-kdyhWtP7INQGbDNvYr/view?usp=drive_link
type WealthBuilderLandingDashboard struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// dashboard header,
	// contains either zero state when no assets are connected
	// or contains section title, money value and other details when any single asset is connected
	DashboardHeader              *DashboardHeader               `protobuf:"bytes,1,opt,name=dashboard_header,json=dashboardHeader,proto3" json:"dashboard_header,omitempty"`
	WealthBuilderLandingSections []*WealthBuilderLandingSection `protobuf:"bytes,2,rep,name=wealth_builder_landing_sections,json=wealthBuilderLandingSections,proto3" json:"wealth_builder_landing_sections,omitempty"`
	// to show at bottom of the section, used to expand and collapse the dashboard
	// this will only be shown when user has more than 5 assets connected
	CollapsibleDetails *CollapsibleDetails `protobuf:"bytes,3,opt,name=collapsible_details,json=collapsibleDetails,proto3" json:"collapsible_details,omitempty"`
	// more cta to show at bottom of dashboard e.g. add more cta
	ActionCta []*ui.IconTextComponent `protobuf:"bytes,4,rep,name=action_cta,json=actionCta,proto3" json:"action_cta,omitempty"`
	// bg color of the component
	BgColor *widget.BackgroundColour `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// corner radius of the component
	// DEPRECATED : As going forward we will be not showing any rounded corner.
	//
	// Deprecated: Marked as deprecated in api/frontend/insights/networth/ui/ui.proto.
	CornerRadius float64 `protobuf:"fixed64,6,opt,name=corner_radius,json=cornerRadius,proto3" json:"corner_radius,omitempty"`
	// Footer ctas
	FooterCtas []*WealthBuilderLandingCta `protobuf:"bytes,7,rep,name=footer_ctas,json=footerCtas,proto3" json:"footer_ctas,omitempty"`
	// Footer banner
	FooterBanner *sections.Section `protobuf:"bytes,8,opt,name=footer_banner,json=footerBanner,proto3" json:"footer_banner,omitempty"`
}

func (x *WealthBuilderLandingDashboard) Reset() {
	*x = WealthBuilderLandingDashboard{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthBuilderLandingDashboard) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthBuilderLandingDashboard) ProtoMessage() {}

func (x *WealthBuilderLandingDashboard) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthBuilderLandingDashboard.ProtoReflect.Descriptor instead.
func (*WealthBuilderLandingDashboard) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{2}
}

func (x *WealthBuilderLandingDashboard) GetDashboardHeader() *DashboardHeader {
	if x != nil {
		return x.DashboardHeader
	}
	return nil
}

func (x *WealthBuilderLandingDashboard) GetWealthBuilderLandingSections() []*WealthBuilderLandingSection {
	if x != nil {
		return x.WealthBuilderLandingSections
	}
	return nil
}

func (x *WealthBuilderLandingDashboard) GetCollapsibleDetails() *CollapsibleDetails {
	if x != nil {
		return x.CollapsibleDetails
	}
	return nil
}

func (x *WealthBuilderLandingDashboard) GetActionCta() []*ui.IconTextComponent {
	if x != nil {
		return x.ActionCta
	}
	return nil
}

func (x *WealthBuilderLandingDashboard) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/insights/networth/ui/ui.proto.
func (x *WealthBuilderLandingDashboard) GetCornerRadius() float64 {
	if x != nil {
		return x.CornerRadius
	}
	return 0
}

func (x *WealthBuilderLandingDashboard) GetFooterCtas() []*WealthBuilderLandingCta {
	if x != nil {
		return x.FooterCtas
	}
	return nil
}

func (x *WealthBuilderLandingDashboard) GetFooterBanner() *sections.Section {
	if x != nil {
		return x.FooterBanner
	}
	return nil
}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-47472&t=Zhxqm8mShovbvbVH-4
type WealthBuilderLandingSection struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// This will be the unique identifier for the section. Each widget will be specific to a section. Caching of a widget should be done with the section.
	// If section id is updated then all the widgets that are cached in the section should be invalidated.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// networth section type, asset or liability
	SectionType enums.NetworthSectionType `protobuf:"varint,2,opt,name=section_type,json=sectionType,proto3,enum=frontend.insights.networth.enums.NetworthSectionType" json:"section_type,omitempty"`
	// header of the section, contains title, money value and other details
	SectionHeaderGenericState *SectionHeaderGenericState `protobuf:"bytes,3,opt,name=section_header_generic_state,json=sectionHeaderGenericState,proto3" json:"section_header_generic_state,omitempty"`
	// list of all widgets in the section
	Widgets []*WidgetV2 `protobuf:"bytes,4,rep,name=widgets,proto3" json:"widgets,omitempty"`
}

func (x *WealthBuilderLandingSection) Reset() {
	*x = WealthBuilderLandingSection{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthBuilderLandingSection) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthBuilderLandingSection) ProtoMessage() {}

func (x *WealthBuilderLandingSection) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthBuilderLandingSection.ProtoReflect.Descriptor instead.
func (*WealthBuilderLandingSection) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{3}
}

func (x *WealthBuilderLandingSection) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WealthBuilderLandingSection) GetSectionType() enums.NetworthSectionType {
	if x != nil {
		return x.SectionType
	}
	return enums.NetworthSectionType(0)
}

func (x *WealthBuilderLandingSection) GetSectionHeaderGenericState() *SectionHeaderGenericState {
	if x != nil {
		return x.SectionHeaderGenericState
	}
	return nil
}

func (x *WealthBuilderLandingSection) GetWidgets() []*WidgetV2 {
	if x != nil {
		return x.Widgets
	}
	return nil
}

type DashboardHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to DashboardHeader:
	//
	//	*DashboardHeader_SectionHeaderZeroState
	//	*DashboardHeader_SectionHeaderConnectedState
	DashboardHeader isDashboardHeader_DashboardHeader `protobuf_oneof:"dashboard_header"`
}

func (x *DashboardHeader) Reset() {
	*x = DashboardHeader{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardHeader) ProtoMessage() {}

func (x *DashboardHeader) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardHeader.ProtoReflect.Descriptor instead.
func (*DashboardHeader) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{4}
}

func (m *DashboardHeader) GetDashboardHeader() isDashboardHeader_DashboardHeader {
	if m != nil {
		return m.DashboardHeader
	}
	return nil
}

func (x *DashboardHeader) GetSectionHeaderZeroState() *DashboardHeaderZeroState {
	if x, ok := x.GetDashboardHeader().(*DashboardHeader_SectionHeaderZeroState); ok {
		return x.SectionHeaderZeroState
	}
	return nil
}

func (x *DashboardHeader) GetSectionHeaderConnectedState() *DashboardHeaderConnectedState {
	if x, ok := x.GetDashboardHeader().(*DashboardHeader_SectionHeaderConnectedState); ok {
		return x.SectionHeaderConnectedState
	}
	return nil
}

type isDashboardHeader_DashboardHeader interface {
	isDashboardHeader_DashboardHeader()
}

type DashboardHeader_SectionHeaderZeroState struct {
	SectionHeaderZeroState *DashboardHeaderZeroState `protobuf:"bytes,1,opt,name=section_header_zero_state,json=sectionHeaderZeroState,proto3,oneof"`
}

type DashboardHeader_SectionHeaderConnectedState struct {
	SectionHeaderConnectedState *DashboardHeaderConnectedState `protobuf:"bytes,2,opt,name=section_header_connected_state,json=sectionHeaderConnectedState,proto3,oneof"`
}

func (*DashboardHeader_SectionHeaderZeroState) isDashboardHeader_DashboardHeader() {}

func (*DashboardHeader_SectionHeaderConnectedState) isDashboardHeader_DashboardHeader() {}

// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-45905&t=sJQwPE7SVHainEDH-4
// Ref: https://drive.google.com/file/d/1nocV-YhLQb7DQ9bQNct50pMKn67LGg4Z/view?usp=drive_link
type DashboardHeaderZeroState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Title *common.Text `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// cta for video to play stating "How this works"
	ActionCta *ui.IconTextComponent `protobuf:"bytes,2,opt,name=action_cta,json=actionCta,proto3" json:"action_cta,omitempty"`
}

func (x *DashboardHeaderZeroState) Reset() {
	*x = DashboardHeaderZeroState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardHeaderZeroState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardHeaderZeroState) ProtoMessage() {}

func (x *DashboardHeaderZeroState) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardHeaderZeroState.ProtoReflect.Descriptor instead.
func (*DashboardHeaderZeroState) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{5}
}

func (x *DashboardHeaderZeroState) GetTitle() *common.Text {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *DashboardHeaderZeroState) GetActionCta() *ui.IconTextComponent {
	if x != nil {
		return x.ActionCta
	}
	return nil
}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46669&t=sJQwPE7SVHainEDH-4
// Ref: https://drive.google.com/file/d/1cko0WMZs8hSQIqdOhOKkWEZ-pN0-8Esn/view?usp=drive_link
type DashboardHeaderConnectedState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of the section including info icon (info icon is optional)
	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// money details, contains money value and rupee symbol for display along with raw money value
	WealthDisplayDetails *WealthDisplayDetails `protobuf:"bytes,2,opt,name=wealth_display_details,json=wealthDisplayDetails,proto3" json:"wealth_display_details,omitempty"`
	// tags to show at the right of money value, contains daily percentage increase and refresh tags etc.
	Tags []*ui.IconTextComponent `protobuf:"bytes,3,rep,name=tags,proto3" json:"tags,omitempty"`
	// show/hide details of the component, includes show/hide icon
	VisibilityDetails *VisibilityDetails `protobuf:"bytes,4,opt,name=visibility_details,json=visibilityDetails,proto3" json:"visibility_details,omitempty"`
}

func (x *DashboardHeaderConnectedState) Reset() {
	*x = DashboardHeaderConnectedState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardHeaderConnectedState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardHeaderConnectedState) ProtoMessage() {}

func (x *DashboardHeaderConnectedState) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardHeaderConnectedState.ProtoReflect.Descriptor instead.
func (*DashboardHeaderConnectedState) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{6}
}

func (x *DashboardHeaderConnectedState) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *DashboardHeaderConnectedState) GetWealthDisplayDetails() *WealthDisplayDetails {
	if x != nil {
		return x.WealthDisplayDetails
	}
	return nil
}

func (x *DashboardHeaderConnectedState) GetTags() []*ui.IconTextComponent {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *DashboardHeaderConnectedState) GetVisibilityDetails() *VisibilityDetails {
	if x != nil {
		return x.VisibilityDetails
	}
	return nil
}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46117&t=qVfYTMo2zXmi7a8g-4
// Ref: https://drive.google.com/file/d/1p4tpp-05ylud2s7hH_3wXpzXwh7LN3zx/view?usp=drive_link
type SectionHeaderGenericState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title of the section including info icon (info icon is optional)
	Title *ui.IconTextComponent `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// money details, contains money value and rupee symbol for display along with raw money value
	WealthDisplayDetails *WealthDisplayDetails `protobuf:"bytes,2,opt,name=wealth_display_details,json=wealthDisplayDetails,proto3" json:"wealth_display_details,omitempty"`
	// toggle for liabilities
	ToggleComponent *ui1.AssetLandingToggleComponent `protobuf:"bytes,3,opt,name=toggle_component,json=toggleComponent,proto3" json:"toggle_component,omitempty"`
}

func (x *SectionHeaderGenericState) Reset() {
	*x = SectionHeaderGenericState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SectionHeaderGenericState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SectionHeaderGenericState) ProtoMessage() {}

func (x *SectionHeaderGenericState) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SectionHeaderGenericState.ProtoReflect.Descriptor instead.
func (*SectionHeaderGenericState) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{7}
}

func (x *SectionHeaderGenericState) GetTitle() *ui.IconTextComponent {
	if x != nil {
		return x.Title
	}
	return nil
}

func (x *SectionHeaderGenericState) GetWealthDisplayDetails() *WealthDisplayDetails {
	if x != nil {
		return x.WealthDisplayDetails
	}
	return nil
}

func (x *SectionHeaderGenericState) GetToggleComponent() *ui1.AssetLandingToggleComponent {
	if x != nil {
		return x.ToggleComponent
	}
	return nil
}

type WidgetV2 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique identifier for the widget. This is to be used by client for caching.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// type of widget that should be rendered.
	Type WidgetTypeV2 `protobuf:"varint,2,opt,name=type,proto3,enum=frontend.insights.networth.ui.WidgetTypeV2" json:"type,omitempty"`
	// Types that are assignable to Params:
	//
	//	*WidgetV2_WealthLandingWidgetParams
	//	*WidgetV2_WbFeatureEntryPointWidgetParams
	Params isWidgetV2_Params `protobuf_oneof:"params"`
	// This enum will tell if client should use the widget from cache or not.
	CacheControl CacheControlV2 `protobuf:"varint,4,opt,name=cache_control,json=cacheControl,proto3,enum=frontend.insights.networth.ui.CacheControlV2" json:"cache_control,omitempty"`
	// State of widget i.e if it is uninitialized, initialized etc.
	State WidgetStateV2 `protobuf:"varint,5,opt,name=state,proto3,enum=frontend.insights.networth.ui.WidgetStateV2" json:"state,omitempty"`
	// payload to be sent in analytics event from client
	// if cache_control is set to use cache, use the payload as well from cache
	WidgetAnalyticsPayload string `protobuf:"bytes,6,opt,name=widget_analytics_payload,json=widgetAnalyticsPayload,proto3" json:"widget_analytics_payload,omitempty"`
	// Value to be used for calculating the total section value and net worth. This can be negative in case of liabilities.
	TotalValue *typesv2.Money `protobuf:"bytes,7,opt,name=total_value,json=totalValue,proto3" json:"total_value,omitempty"`
}

func (x *WidgetV2) Reset() {
	*x = WidgetV2{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WidgetV2) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WidgetV2) ProtoMessage() {}

func (x *WidgetV2) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WidgetV2.ProtoReflect.Descriptor instead.
func (*WidgetV2) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{8}
}

func (x *WidgetV2) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *WidgetV2) GetType() WidgetTypeV2 {
	if x != nil {
		return x.Type
	}
	return WidgetTypeV2_WIDGET_TYPE_V2_UNSPECIFIED
}

func (m *WidgetV2) GetParams() isWidgetV2_Params {
	if m != nil {
		return m.Params
	}
	return nil
}

func (x *WidgetV2) GetWealthLandingWidgetParams() *WealthBuilderLandingWidgetParams {
	if x, ok := x.GetParams().(*WidgetV2_WealthLandingWidgetParams); ok {
		return x.WealthLandingWidgetParams
	}
	return nil
}

func (x *WidgetV2) GetWbFeatureEntryPointWidgetParams() *WbFeatureEntryPointWidgetParams {
	if x, ok := x.GetParams().(*WidgetV2_WbFeatureEntryPointWidgetParams); ok {
		return x.WbFeatureEntryPointWidgetParams
	}
	return nil
}

func (x *WidgetV2) GetCacheControl() CacheControlV2 {
	if x != nil {
		return x.CacheControl
	}
	return CacheControlV2_CACHE_CONTROL_V2_UNSPECIFIED
}

func (x *WidgetV2) GetState() WidgetStateV2 {
	if x != nil {
		return x.State
	}
	return WidgetStateV2_WIDGET_STATE_V2_UNSPECIFIED
}

func (x *WidgetV2) GetWidgetAnalyticsPayload() string {
	if x != nil {
		return x.WidgetAnalyticsPayload
	}
	return ""
}

func (x *WidgetV2) GetTotalValue() *typesv2.Money {
	if x != nil {
		return x.TotalValue
	}
	return nil
}

type isWidgetV2_Params interface {
	isWidgetV2_Params()
}

type WidgetV2_WealthLandingWidgetParams struct {
	// parameters for wealth builder landing widget
	// used only in case of WIDGET_TYPE_WEALTH_BUILDER type
	WealthLandingWidgetParams *WealthBuilderLandingWidgetParams `protobuf:"bytes,3,opt,name=wealth_landing_widget_params,json=wealthLandingWidgetParams,proto3,oneof"`
}

type WidgetV2_WbFeatureEntryPointWidgetParams struct {
	// used as entry-point for features like Magic Import
	WbFeatureEntryPointWidgetParams *WbFeatureEntryPointWidgetParams `protobuf:"bytes,8,opt,name=wb_feature_entry_point_widget_params,json=wbFeatureEntryPointWidgetParams,proto3,oneof"`
}

func (*WidgetV2_WealthLandingWidgetParams) isWidgetV2_Params() {}

func (*WidgetV2_WbFeatureEntryPointWidgetParams) isWidgetV2_Params() {}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46684&t=sJQwPE7SVHainEDH-4
// Ref: https://drive.google.com/file/d/1HXnf1NZ5yNRIwxT0vsRUNx3WPLQzx2Jd/view?usp=sharing
type WealthBuilderLandingWidgetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// weight of card specifies the width of the card in percentage
	Weight float32 `protobuf:"fixed32,1,opt,name=weight,proto3" json:"weight,omitempty"`
	// card title
	WidgetTitle *common.Text `protobuf:"bytes,2,opt,name=widget_title,json=widgetTitle,proto3" json:"widget_title,omitempty"`
	// money details, contains money value and rupee symbol for display along with raw money value
	WealthDisplayDetails *WealthDisplayDetails `protobuf:"bytes,3,opt,name=wealth_display_details,json=wealthDisplayDetails,proto3" json:"wealth_display_details,omitempty"`
	// tag to show at the right of money value
	RightTag *ui.IconTextComponent `protobuf:"bytes,4,opt,name=right_tag,json=rightTag,proto3" json:"right_tag,omitempty"`
	// tag to show at the bottom of the widget
	BottomTag *ui.IconTextComponent `protobuf:"bytes,5,opt,name=bottom_tag,json=bottomTag,proto3" json:"bottom_tag,omitempty"`
	// Icon to show at bottom right corner of card
	BottomRightIcon *common.VisualElement `protobuf:"bytes,6,opt,name=bottom_right_icon,json=bottomRightIcon,proto3" json:"bottom_right_icon,omitempty"`
	// deeplink to open when user clicks on the widget, this will be for the entire card
	Deeplink *deeplink.Deeplink `protobuf:"bytes,7,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// bg color of the widget
	BgColor *widget.BackgroundColour `protobuf:"bytes,8,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// border properties including border style
	BorderProperty *ui.BorderProperty `protobuf:"bytes,9,opt,name=border_property,json=borderProperty,proto3" json:"border_property,omitempty"`
	// top right nudge to show on widget
	TopRightNudge *common.VisualElement `protobuf:"bytes,10,opt,name=top_right_nudge,json=topRightNudge,proto3" json:"top_right_nudge,omitempty"`
	// sdui border property to be used for the widget
	SduiBorderProperty *properties.BorderProperty `protobuf:"bytes,11,opt,name=sdui_border_property,json=sduiBorderProperty,proto3" json:"sdui_border_property,omitempty"`
}

func (x *WealthBuilderLandingWidgetParams) Reset() {
	*x = WealthBuilderLandingWidgetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthBuilderLandingWidgetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthBuilderLandingWidgetParams) ProtoMessage() {}

func (x *WealthBuilderLandingWidgetParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthBuilderLandingWidgetParams.ProtoReflect.Descriptor instead.
func (*WealthBuilderLandingWidgetParams) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{9}
}

func (x *WealthBuilderLandingWidgetParams) GetWeight() float32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *WealthBuilderLandingWidgetParams) GetWidgetTitle() *common.Text {
	if x != nil {
		return x.WidgetTitle
	}
	return nil
}

func (x *WealthBuilderLandingWidgetParams) GetWealthDisplayDetails() *WealthDisplayDetails {
	if x != nil {
		return x.WealthDisplayDetails
	}
	return nil
}

func (x *WealthBuilderLandingWidgetParams) GetRightTag() *ui.IconTextComponent {
	if x != nil {
		return x.RightTag
	}
	return nil
}

func (x *WealthBuilderLandingWidgetParams) GetBottomTag() *ui.IconTextComponent {
	if x != nil {
		return x.BottomTag
	}
	return nil
}

func (x *WealthBuilderLandingWidgetParams) GetBottomRightIcon() *common.VisualElement {
	if x != nil {
		return x.BottomRightIcon
	}
	return nil
}

func (x *WealthBuilderLandingWidgetParams) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *WealthBuilderLandingWidgetParams) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

func (x *WealthBuilderLandingWidgetParams) GetBorderProperty() *ui.BorderProperty {
	if x != nil {
		return x.BorderProperty
	}
	return nil
}

func (x *WealthBuilderLandingWidgetParams) GetTopRightNudge() *common.VisualElement {
	if x != nil {
		return x.TopRightNudge
	}
	return nil
}

func (x *WealthBuilderLandingWidgetParams) GetSduiBorderProperty() *properties.BorderProperty {
	if x != nil {
		return x.SduiBorderProperty
	}
	return nil
}

type WbFeatureEntryPointWidgetParams struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// weight of card specifies the width of the card in percentage
	Weight float32 `protobuf:"fixed32,1,opt,name=weight,proto3" json:"weight,omitempty"`
	// visual element to fill the widget card
	CenterImage *common.VisualElement `protobuf:"bytes,2,opt,name=center_image,json=centerImage,proto3" json:"center_image,omitempty"`
	// deeplink to open the feature when user clicks on the widget, this will be for the entire card
	Deeplink *deeplink.Deeplink `protobuf:"bytes,3,opt,name=deeplink,proto3" json:"deeplink,omitempty"`
	// sdui border property to be used for the widget
	SduiBorderProperty *properties.BorderProperty `protobuf:"bytes,4,opt,name=sdui_border_property,json=sduiBorderProperty,proto3" json:"sdui_border_property,omitempty"`
	// bg color of the widget
	BgColor *widget.BackgroundColour `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
}

func (x *WbFeatureEntryPointWidgetParams) Reset() {
	*x = WbFeatureEntryPointWidgetParams{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WbFeatureEntryPointWidgetParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WbFeatureEntryPointWidgetParams) ProtoMessage() {}

func (x *WbFeatureEntryPointWidgetParams) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WbFeatureEntryPointWidgetParams.ProtoReflect.Descriptor instead.
func (*WbFeatureEntryPointWidgetParams) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{10}
}

func (x *WbFeatureEntryPointWidgetParams) GetWeight() float32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *WbFeatureEntryPointWidgetParams) GetCenterImage() *common.VisualElement {
	if x != nil {
		return x.CenterImage
	}
	return nil
}

func (x *WbFeatureEntryPointWidgetParams) GetDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.Deeplink
	}
	return nil
}

func (x *WbFeatureEntryPointWidgetParams) GetSduiBorderProperty() *properties.BorderProperty {
	if x != nil {
		return x.SduiBorderProperty
	}
	return nil
}

func (x *WbFeatureEntryPointWidgetParams) GetBgColor() *widget.BackgroundColour {
	if x != nil {
		return x.BgColor
	}
	return nil
}

type CollapsibleDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// show more cta to show at the bottom of the section, this will only be shown when user has more than 5 assets connected
	// tapping on this will expand the section to show all asets
	ShowMoreCta *ui.IconTextComponent `protobuf:"bytes,1,opt,name=show_more_cta,json=showMoreCta,proto3" json:"show_more_cta,omitempty"`
	// tapping on this will collapse the section to show only 5 assets
	ShowLessCta *ui.IconTextComponent `protobuf:"bytes,2,opt,name=show_less_cta,json=showLessCta,proto3" json:"show_less_cta,omitempty"`
}

func (x *CollapsibleDetails) Reset() {
	*x = CollapsibleDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollapsibleDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollapsibleDetails) ProtoMessage() {}

func (x *CollapsibleDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollapsibleDetails.ProtoReflect.Descriptor instead.
func (*CollapsibleDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{11}
}

func (x *CollapsibleDetails) GetShowMoreCta() *ui.IconTextComponent {
	if x != nil {
		return x.ShowMoreCta
	}
	return nil
}

func (x *CollapsibleDetails) GetShowLessCta() *ui.IconTextComponent {
	if x != nil {
		return x.ShowLessCta
	}
	return nil
}

type WealthDisplayDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// money symbol in rupee (₹)
	CurrencySymbol *common.Text `protobuf:"bytes,1,opt,name=currency_symbol,json=currencySymbol,proto3" json:"currency_symbol,omitempty"`
	// total asset value display text
	// Total value. The value should be overridden by client and formatting should be used as it is.
	TotalDisplayValue *common.Text `protobuf:"bytes,2,opt,name=total_display_value,json=totalDisplayValue,proto3" json:"total_display_value,omitempty"`
	// Value to be used for calculating the total section value and net worth. This can be negative in case of liabilities.
	TotalValue *typesv2.Money `protobuf:"bytes,3,opt,name=total_value,json=totalValue,proto3" json:"total_value,omitempty"`
}

func (x *WealthDisplayDetails) Reset() {
	*x = WealthDisplayDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WealthDisplayDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WealthDisplayDetails) ProtoMessage() {}

func (x *WealthDisplayDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WealthDisplayDetails.ProtoReflect.Descriptor instead.
func (*WealthDisplayDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{12}
}

func (x *WealthDisplayDetails) GetCurrencySymbol() *common.Text {
	if x != nil {
		return x.CurrencySymbol
	}
	return nil
}

func (x *WealthDisplayDetails) GetTotalDisplayValue() *common.Text {
	if x != nil {
		return x.TotalDisplayValue
	}
	return nil
}

func (x *WealthDisplayDetails) GetTotalValue() *typesv2.Money {
	if x != nil {
		return x.TotalValue
	}
	return nil
}

type VisibilityDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Hide *common.VisualElement `protobuf:"bytes,1,opt,name=hide,proto3" json:"hide,omitempty"`
	Show *common.VisualElement `protobuf:"bytes,2,opt,name=show,proto3" json:"show,omitempty"`
}

func (x *VisibilityDetails) Reset() {
	*x = VisibilityDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VisibilityDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VisibilityDetails) ProtoMessage() {}

func (x *VisibilityDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_insights_networth_ui_ui_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VisibilityDetails.ProtoReflect.Descriptor instead.
func (*VisibilityDetails) Descriptor() ([]byte, []int) {
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP(), []int{13}
}

func (x *VisibilityDetails) GetHide() *common.VisualElement {
	if x != nil {
		return x.Hide
	}
	return nil
}

func (x *VisibilityDetails) GetShow() *common.VisualElement {
	if x != nil {
		return x.Show
	}
	return nil
}

var File_api_frontend_insights_networth_ui_ui_proto protoreflect.FileDescriptor

var file_api_frontend_insights_networth_ui_ui_proto_rawDesc = []byte{
	0x0a, 0x2a, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69,
	0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x2f, 0x75, 0x69, 0x2f, 0x75, 0x69, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x1a, 0x24, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x75, 0x69, 0x2f,
	0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x30, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2f,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x68, 0x65, 0x6d, 0x65, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65,
	0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x36, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x73, 0x64, 0x75, 0x69, 0x2f, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2a, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x73, 0x64,
	0x75, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8, 0x02, 0x0a, 0x1d, 0x57, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x85, 0x01, 0x0a, 0x20,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6c,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69,
	0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x52, 0x1d, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x12, 0x44, 0x0a, 0x0a, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x74,
	0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09,
	0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f,
	0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67,
	0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43,
	0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xc7, 0x04, 0x0a, 0x17, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42,
	0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x74, 0x61,
	0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73,
	0x12, 0x57, 0x0a, 0x0f, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x42, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x0e, 0x62, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x35, 0x0a, 0x04, 0x69, 0x63, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73,
	0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63, 0x6f, 0x6e,
	0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x12, 0x33, 0x0a, 0x03, 0x74, 0x61, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x03, 0x74, 0x61, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x70, 0x72, 0x69, 0x6d,
	0x61, 0x72, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x50, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x1e, 0x0a,
	0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x4f, 0x0a,
	0x0b, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f,
	0x75, 0x72, 0x52, 0x0a, 0x61, 0x72, 0x72, 0x6f, 0x77, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xbd,
	0x05, 0x0a, 0x1d, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72,
	0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x12, 0x59, 0x0a, 0x10, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0f, 0x64, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x81, 0x01, 0x0a, 0x1f,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x62, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x5f, 0x6c,
	0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c,
	0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x1c, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72,
	0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x62, 0x0a, 0x13, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x70, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x43, 0x6f, 0x6c,
	0x6c, 0x61, 0x70, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x12, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x70, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x40, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x74,
	0x61, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x43, 0x74, 0x61, 0x12, 0x49, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e,
	0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72,
	0x12, 0x27, 0x0a, 0x0d, 0x63, 0x6f, 0x72, 0x6e, 0x65, 0x72, 0x5f, 0x72, 0x61, 0x64, 0x69, 0x75,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0c, 0x63, 0x6f, 0x72,
	0x6e, 0x65, 0x72, 0x52, 0x61, 0x64, 0x69, 0x75, 0x73, 0x12, 0x57, 0x0a, 0x0b, 0x66, 0x6f, 0x6f,
	0x74, 0x65, 0x72, 0x5f, 0x63, 0x74, 0x61, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x57,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x43, 0x74, 0x61, 0x52, 0x0a, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x43, 0x74,
	0x61, 0x73, 0x12, 0x4a, 0x0a, 0x0d, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x5f, 0x62, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e,
	0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0c, 0x66, 0x6f, 0x6f, 0x74, 0x65, 0x72, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x22, 0xc5,
	0x02, 0x0a, 0x1b, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72,
	0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x58,
	0x0a, 0x0c, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68,
	0x53, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x79, 0x0a, 0x1c, 0x73, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x67, 0x65, 0x6e, 0x65, 0x72,
	0x69, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x53,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x47, 0x65, 0x6e, 0x65,
	0x72, 0x69, 0x63, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x19, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x41, 0x0a, 0x07, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x75, 0x69, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x56, 0x32, 0x52, 0x07, 0x77,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x73, 0x22, 0xa1, 0x02, 0x0a, 0x0f, 0x44, 0x61, 0x73, 0x68, 0x62,
	0x6f, 0x61, 0x72, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x74, 0x0a, 0x19, 0x73, 0x65,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5f, 0x7a, 0x65, 0x72,
	0x6f, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74,
	0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5a, 0x65, 0x72,
	0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x16, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5a, 0x65, 0x72, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x83, 0x01, 0x0a, 0x1e, 0x73, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x1b, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x12, 0x0a, 0x10, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f,
	0x61, 0x72, 0x64, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x8c, 0x01, 0x0a, 0x18, 0x44,
	0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x5a, 0x65,
	0x72, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x40, 0x0a, 0x0a, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x74, 0x61, 0x22, 0xdb, 0x02, 0x0a, 0x1d, 0x44, 0x61,
	0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x65, 0x64, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74,
	0x69, 0x74, 0x6c, 0x65, 0x12, 0x69, 0x0a, 0x16, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x75, 0x69, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14, 0x77, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12,
	0x35, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49,
	0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x5f, 0x0a, 0x12, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e,
	0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e,
	0x75, 0x69, 0x2e, 0x56, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x11, 0x76, 0x69, 0x73, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x9f, 0x02, 0x0a, 0x19, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x05, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x69,
	0x0a, 0x16, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x57,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x14, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x44, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x5e, 0x0a, 0x10, 0x74, 0x6f, 0x67,
	0x67, 0x6c, 0x65, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69,
	0x6e, 0x76, 0x65, 0x73, 0x74, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x75, 0x69, 0x2e, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x54, 0x6f, 0x67, 0x67, 0x6c, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x74, 0x6f, 0x67, 0x67, 0x6c, 0x65,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0x83, 0x05, 0x0a, 0x08, 0x57, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x56, 0x32, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3f, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x75, 0x69, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x56,
	0x32, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x82, 0x01, 0x0a, 0x1c, 0x77, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x5f, 0x6c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3f,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x57,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65, 0x72, 0x4c, 0x61, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48,
	0x00, 0x52, 0x19, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x8f, 0x01, 0x0a,
	0x24, 0x77, 0x62, 0x5f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x5f, 0x65, 0x6e, 0x74, 0x72,
	0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x3e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x57, 0x62, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x57,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x1f, 0x77,
	0x62, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x52,
	0x0a, 0x0d, 0x63, 0x61, 0x63, 0x68, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x56, 0x32, 0x52, 0x0c, 0x63, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72,
	0x6f, 0x6c, 0x12, 0x42, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73,
	0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75,
	0x69, 0x2e, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x56, 0x32, 0x52,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x38, 0x0a, 0x18, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x5f, 0x61, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74,
	0x41, 0x6e, 0x61, 0x6c, 0x79, 0x74, 0x69, 0x63, 0x73, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x08, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22,
	0xad, 0x06, 0x0a, 0x20, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x42, 0x75, 0x69, 0x6c, 0x64, 0x65,
	0x72, 0x4c, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x3b, 0x0a, 0x0c,
	0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0b, 0x77, 0x69,
	0x64, 0x67, 0x65, 0x74, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x69, 0x0a, 0x16, 0x77, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2e, 0x75, 0x69, 0x2e, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x14,
	0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x3e, 0x0a, 0x09, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x74, 0x61,
	0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78,
	0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x72, 0x69, 0x67, 0x68,
	0x74, 0x54, 0x61, 0x67, 0x12, 0x40, 0x0a, 0x0a, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x5f, 0x74,
	0x61, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x09, 0x62, 0x6f, 0x74,
	0x74, 0x6f, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x4d, 0x0a, 0x11, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d,
	0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f, 0x69, 0x63, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0f, 0x62, 0x6f, 0x74, 0x74, 0x6f, 0x6d, 0x52, 0x69, 0x67, 0x68,
	0x74, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x49,
	0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x42, 0x61, 0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72,
	0x52, 0x07, 0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x47, 0x0a, 0x0f, 0x62, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x52, 0x0e, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x12, 0x49, 0x0a, 0x0f, 0x74, 0x6f, 0x70, 0x5f, 0x72, 0x69, 0x67, 0x68, 0x74, 0x5f,
	0x6e, 0x75, 0x64, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0d,
	0x74, 0x6f, 0x70, 0x52, 0x69, 0x67, 0x68, 0x74, 0x4e, 0x75, 0x64, 0x67, 0x65, 0x12, 0x60, 0x0a,
	0x14, 0x73, 0x64, 0x75, 0x69, 0x5f, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x73, 0x64, 0x75,
	0x69, 0x2e, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x42, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x12, 0x73, 0x64, 0x75,
	0x69, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22,
	0xe5, 0x02, 0x0a, 0x1f, 0x57, 0x62, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x44, 0x0a, 0x0c, 0x63,
	0x65, 0x6e, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x63, 0x65, 0x6e, 0x74, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67,
	0x65, 0x12, 0x37, 0x0a, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x52, 0x08, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x60, 0x0a, 0x14, 0x73, 0x64,
	0x75, 0x69, 0x5f, 0x62, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x73, 0x64, 0x75, 0x69, 0x2e, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x42, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x52, 0x12, 0x73, 0x64, 0x75, 0x69, 0x42, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x12, 0x49, 0x0a, 0x08,
	0x62, 0x67, 0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e, 0x42, 0x61,
	0x63, 0x6b, 0x67, 0x72, 0x6f, 0x75, 0x6e, 0x64, 0x43, 0x6f, 0x6c, 0x6f, 0x75, 0x72, 0x52, 0x07,
	0x62, 0x67, 0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0xa2, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6c, 0x6c,
	0x61, 0x70, 0x73, 0x69, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x45,
	0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x6d, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x73, 0x68, 0x6f, 0x77, 0x4d, 0x6f,
	0x72, 0x65, 0x43, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x0d, 0x73, 0x68, 0x6f, 0x77, 0x5f, 0x6c, 0x65,
	0x73, 0x73, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63,
	0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x0b, 0x73, 0x68, 0x6f, 0x77, 0x4c, 0x65, 0x73, 0x73, 0x43, 0x74, 0x61, 0x22, 0xd8, 0x01, 0x0a,
	0x14, 0x57, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x41, 0x0a, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x63,
	0x79, 0x5f, 0x73, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0e, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e,
	0x63, 0x79, 0x53, 0x79, 0x6d, 0x62, 0x6f, 0x6c, 0x12, 0x48, 0x0a, 0x13, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x33, 0x0a, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0a, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x81, 0x01, 0x0a, 0x11, 0x56, 0x69, 0x73, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x35, 0x0a,
	0x04, 0x68, 0x69, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04,
	0x68, 0x69, 0x64, 0x65, 0x12, 0x35, 0x0a, 0x04, 0x73, 0x68, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x73, 0x68, 0x6f, 0x77, 0x2a, 0x51, 0x0a, 0x0c, 0x57,
	0x69, 0x64, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x56, 0x32, 0x12, 0x1e, 0x0a, 0x1a, 0x57,
	0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x32, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x57,
	0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x56, 0x32, 0x5f, 0x57, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x5f, 0x42, 0x55, 0x49, 0x4c, 0x44, 0x45, 0x52, 0x10, 0x01, 0x2a, 0x52,
	0x0a, 0x0e, 0x43, 0x61, 0x63, 0x68, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x56, 0x32,
	0x12, 0x20, 0x0a, 0x1c, 0x43, 0x41, 0x43, 0x48, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x52, 0x4f,
	0x4c, 0x5f, 0x56, 0x32, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x43, 0x48, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54,
	0x52, 0x4f, 0x4c, 0x5f, 0x56, 0x32, 0x5f, 0x55, 0x53, 0x45, 0x5f, 0x43, 0x41, 0x43, 0x48, 0x45,
	0x10, 0x01, 0x2a, 0x74, 0x0a, 0x0d, 0x57, 0x69, 0x64, 0x67, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x56, 0x32, 0x12, 0x1f, 0x0a, 0x1b, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x45, 0x5f, 0x56, 0x32, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x21, 0x0a, 0x1d, 0x57, 0x49, 0x44, 0x47, 0x45, 0x54, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x32, 0x5f, 0x55, 0x4e, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41,
	0x4c, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x57, 0x49, 0x44, 0x47, 0x45,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x56, 0x32, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x4c, 0x49, 0x5a, 0x45, 0x44, 0x10, 0x02, 0x42, 0x74, 0x0a, 0x38, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x69, 0x6e, 0x73, 0x69, 0x67, 0x68, 0x74, 0x73, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74,
	0x68, 0x2e, 0x75, 0x69, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x69, 0x6e, 0x73, 0x69, 0x67, 0x68,
	0x74, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x74, 0x68, 0x2f, 0x75, 0x69, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_insights_networth_ui_ui_proto_rawDescOnce sync.Once
	file_api_frontend_insights_networth_ui_ui_proto_rawDescData = file_api_frontend_insights_networth_ui_ui_proto_rawDesc
)

func file_api_frontend_insights_networth_ui_ui_proto_rawDescGZIP() []byte {
	file_api_frontend_insights_networth_ui_ui_proto_rawDescOnce.Do(func() {
		file_api_frontend_insights_networth_ui_ui_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_insights_networth_ui_ui_proto_rawDescData)
	})
	return file_api_frontend_insights_networth_ui_ui_proto_rawDescData
}

var file_api_frontend_insights_networth_ui_ui_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_frontend_insights_networth_ui_ui_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_frontend_insights_networth_ui_ui_proto_goTypes = []interface{}{
	(WidgetTypeV2)(0),                        // 0: frontend.insights.networth.ui.WidgetTypeV2
	(CacheControlV2)(0),                      // 1: frontend.insights.networth.ui.CacheControlV2
	(WidgetStateV2)(0),                       // 2: frontend.insights.networth.ui.WidgetStateV2
	(*WealthBuilderLandingComponent)(nil),    // 3: frontend.insights.networth.ui.WealthBuilderLandingComponent
	(*WealthBuilderLandingCta)(nil),          // 4: frontend.insights.networth.ui.WealthBuilderLandingCta
	(*WealthBuilderLandingDashboard)(nil),    // 5: frontend.insights.networth.ui.WealthBuilderLandingDashboard
	(*WealthBuilderLandingSection)(nil),      // 6: frontend.insights.networth.ui.WealthBuilderLandingSection
	(*DashboardHeader)(nil),                  // 7: frontend.insights.networth.ui.DashboardHeader
	(*DashboardHeaderZeroState)(nil),         // 8: frontend.insights.networth.ui.DashboardHeaderZeroState
	(*DashboardHeaderConnectedState)(nil),    // 9: frontend.insights.networth.ui.DashboardHeaderConnectedState
	(*SectionHeaderGenericState)(nil),        // 10: frontend.insights.networth.ui.SectionHeaderGenericState
	(*WidgetV2)(nil),                         // 11: frontend.insights.networth.ui.WidgetV2
	(*WealthBuilderLandingWidgetParams)(nil), // 12: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams
	(*WbFeatureEntryPointWidgetParams)(nil),  // 13: frontend.insights.networth.ui.WbFeatureEntryPointWidgetParams
	(*CollapsibleDetails)(nil),               // 14: frontend.insights.networth.ui.CollapsibleDetails
	(*WealthDisplayDetails)(nil),             // 15: frontend.insights.networth.ui.WealthDisplayDetails
	(*VisibilityDetails)(nil),                // 16: frontend.insights.networth.ui.VisibilityDetails
	(*ui.IconTextComponent)(nil),             // 17: api.typesv2.ui.IconTextComponent
	(*widget.BackgroundColour)(nil),          // 18: api.typesv2.common.ui.widget.BackgroundColour
	(*properties.BorderProperty)(nil),        // 19: api.typesv2.ui.sdui.properties.BorderProperty
	(*common.VisualElement)(nil),             // 20: api.typesv2.common.VisualElement
	(*common.Text)(nil),                      // 21: api.typesv2.common.Text
	(*deeplink.Deeplink)(nil),                // 22: frontend.deeplink.Deeplink
	(*sections.Section)(nil),                 // 23: api.typesv2.ui.sdui.sections.Section
	(enums.NetworthSectionType)(0),           // 24: frontend.insights.networth.enums.NetworthSectionType
	(*ui1.AssetLandingToggleComponent)(nil),  // 25: frontend.investment.ui.AssetLandingToggleComponent
	(*typesv2.Money)(nil),                    // 26: api.typesv2.Money
	(*ui.BorderProperty)(nil),                // 27: api.typesv2.ui.BorderProperty
}
var file_api_frontend_insights_networth_ui_ui_proto_depIdxs = []int32{
	5,  // 0: frontend.insights.networth.ui.WealthBuilderLandingComponent.wealth_builder_landing_dashboard:type_name -> frontend.insights.networth.ui.WealthBuilderLandingDashboard
	17, // 1: frontend.insights.networth.ui.WealthBuilderLandingComponent.footer_cta:type_name -> api.typesv2.ui.IconTextComponent
	18, // 2: frontend.insights.networth.ui.WealthBuilderLandingComponent.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	18, // 3: frontend.insights.networth.ui.WealthBuilderLandingCta.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	19, // 4: frontend.insights.networth.ui.WealthBuilderLandingCta.border_property:type_name -> api.typesv2.ui.sdui.properties.BorderProperty
	20, // 5: frontend.insights.networth.ui.WealthBuilderLandingCta.icon:type_name -> api.typesv2.common.VisualElement
	21, // 6: frontend.insights.networth.ui.WealthBuilderLandingCta.title:type_name -> api.typesv2.common.Text
	17, // 7: frontend.insights.networth.ui.WealthBuilderLandingCta.tag:type_name -> api.typesv2.ui.IconTextComponent
	22, // 8: frontend.insights.networth.ui.WealthBuilderLandingCta.deeplink:type_name -> frontend.deeplink.Deeplink
	18, // 9: frontend.insights.networth.ui.WealthBuilderLandingCta.arrow_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	7,  // 10: frontend.insights.networth.ui.WealthBuilderLandingDashboard.dashboard_header:type_name -> frontend.insights.networth.ui.DashboardHeader
	6,  // 11: frontend.insights.networth.ui.WealthBuilderLandingDashboard.wealth_builder_landing_sections:type_name -> frontend.insights.networth.ui.WealthBuilderLandingSection
	14, // 12: frontend.insights.networth.ui.WealthBuilderLandingDashboard.collapsible_details:type_name -> frontend.insights.networth.ui.CollapsibleDetails
	17, // 13: frontend.insights.networth.ui.WealthBuilderLandingDashboard.action_cta:type_name -> api.typesv2.ui.IconTextComponent
	18, // 14: frontend.insights.networth.ui.WealthBuilderLandingDashboard.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	4,  // 15: frontend.insights.networth.ui.WealthBuilderLandingDashboard.footer_ctas:type_name -> frontend.insights.networth.ui.WealthBuilderLandingCta
	23, // 16: frontend.insights.networth.ui.WealthBuilderLandingDashboard.footer_banner:type_name -> api.typesv2.ui.sdui.sections.Section
	24, // 17: frontend.insights.networth.ui.WealthBuilderLandingSection.section_type:type_name -> frontend.insights.networth.enums.NetworthSectionType
	10, // 18: frontend.insights.networth.ui.WealthBuilderLandingSection.section_header_generic_state:type_name -> frontend.insights.networth.ui.SectionHeaderGenericState
	11, // 19: frontend.insights.networth.ui.WealthBuilderLandingSection.widgets:type_name -> frontend.insights.networth.ui.WidgetV2
	8,  // 20: frontend.insights.networth.ui.DashboardHeader.section_header_zero_state:type_name -> frontend.insights.networth.ui.DashboardHeaderZeroState
	9,  // 21: frontend.insights.networth.ui.DashboardHeader.section_header_connected_state:type_name -> frontend.insights.networth.ui.DashboardHeaderConnectedState
	21, // 22: frontend.insights.networth.ui.DashboardHeaderZeroState.title:type_name -> api.typesv2.common.Text
	17, // 23: frontend.insights.networth.ui.DashboardHeaderZeroState.action_cta:type_name -> api.typesv2.ui.IconTextComponent
	17, // 24: frontend.insights.networth.ui.DashboardHeaderConnectedState.title:type_name -> api.typesv2.ui.IconTextComponent
	15, // 25: frontend.insights.networth.ui.DashboardHeaderConnectedState.wealth_display_details:type_name -> frontend.insights.networth.ui.WealthDisplayDetails
	17, // 26: frontend.insights.networth.ui.DashboardHeaderConnectedState.tags:type_name -> api.typesv2.ui.IconTextComponent
	16, // 27: frontend.insights.networth.ui.DashboardHeaderConnectedState.visibility_details:type_name -> frontend.insights.networth.ui.VisibilityDetails
	17, // 28: frontend.insights.networth.ui.SectionHeaderGenericState.title:type_name -> api.typesv2.ui.IconTextComponent
	15, // 29: frontend.insights.networth.ui.SectionHeaderGenericState.wealth_display_details:type_name -> frontend.insights.networth.ui.WealthDisplayDetails
	25, // 30: frontend.insights.networth.ui.SectionHeaderGenericState.toggle_component:type_name -> frontend.investment.ui.AssetLandingToggleComponent
	0,  // 31: frontend.insights.networth.ui.WidgetV2.type:type_name -> frontend.insights.networth.ui.WidgetTypeV2
	12, // 32: frontend.insights.networth.ui.WidgetV2.wealth_landing_widget_params:type_name -> frontend.insights.networth.ui.WealthBuilderLandingWidgetParams
	13, // 33: frontend.insights.networth.ui.WidgetV2.wb_feature_entry_point_widget_params:type_name -> frontend.insights.networth.ui.WbFeatureEntryPointWidgetParams
	1,  // 34: frontend.insights.networth.ui.WidgetV2.cache_control:type_name -> frontend.insights.networth.ui.CacheControlV2
	2,  // 35: frontend.insights.networth.ui.WidgetV2.state:type_name -> frontend.insights.networth.ui.WidgetStateV2
	26, // 36: frontend.insights.networth.ui.WidgetV2.total_value:type_name -> api.typesv2.Money
	21, // 37: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams.widget_title:type_name -> api.typesv2.common.Text
	15, // 38: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams.wealth_display_details:type_name -> frontend.insights.networth.ui.WealthDisplayDetails
	17, // 39: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams.right_tag:type_name -> api.typesv2.ui.IconTextComponent
	17, // 40: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams.bottom_tag:type_name -> api.typesv2.ui.IconTextComponent
	20, // 41: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams.bottom_right_icon:type_name -> api.typesv2.common.VisualElement
	22, // 42: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams.deeplink:type_name -> frontend.deeplink.Deeplink
	18, // 43: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	27, // 44: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams.border_property:type_name -> api.typesv2.ui.BorderProperty
	20, // 45: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams.top_right_nudge:type_name -> api.typesv2.common.VisualElement
	19, // 46: frontend.insights.networth.ui.WealthBuilderLandingWidgetParams.sdui_border_property:type_name -> api.typesv2.ui.sdui.properties.BorderProperty
	20, // 47: frontend.insights.networth.ui.WbFeatureEntryPointWidgetParams.center_image:type_name -> api.typesv2.common.VisualElement
	22, // 48: frontend.insights.networth.ui.WbFeatureEntryPointWidgetParams.deeplink:type_name -> frontend.deeplink.Deeplink
	19, // 49: frontend.insights.networth.ui.WbFeatureEntryPointWidgetParams.sdui_border_property:type_name -> api.typesv2.ui.sdui.properties.BorderProperty
	18, // 50: frontend.insights.networth.ui.WbFeatureEntryPointWidgetParams.bg_color:type_name -> api.typesv2.common.ui.widget.BackgroundColour
	17, // 51: frontend.insights.networth.ui.CollapsibleDetails.show_more_cta:type_name -> api.typesv2.ui.IconTextComponent
	17, // 52: frontend.insights.networth.ui.CollapsibleDetails.show_less_cta:type_name -> api.typesv2.ui.IconTextComponent
	21, // 53: frontend.insights.networth.ui.WealthDisplayDetails.currency_symbol:type_name -> api.typesv2.common.Text
	21, // 54: frontend.insights.networth.ui.WealthDisplayDetails.total_display_value:type_name -> api.typesv2.common.Text
	26, // 55: frontend.insights.networth.ui.WealthDisplayDetails.total_value:type_name -> api.typesv2.Money
	20, // 56: frontend.insights.networth.ui.VisibilityDetails.hide:type_name -> api.typesv2.common.VisualElement
	20, // 57: frontend.insights.networth.ui.VisibilityDetails.show:type_name -> api.typesv2.common.VisualElement
	58, // [58:58] is the sub-list for method output_type
	58, // [58:58] is the sub-list for method input_type
	58, // [58:58] is the sub-list for extension type_name
	58, // [58:58] is the sub-list for extension extendee
	0,  // [0:58] is the sub-list for field type_name
}

func init() { file_api_frontend_insights_networth_ui_ui_proto_init() }
func file_api_frontend_insights_networth_ui_ui_proto_init() {
	if File_api_frontend_insights_networth_ui_ui_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthBuilderLandingComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthBuilderLandingCta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthBuilderLandingDashboard); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthBuilderLandingSection); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardHeader); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardHeaderZeroState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardHeaderConnectedState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SectionHeaderGenericState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WidgetV2); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthBuilderLandingWidgetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WbFeatureEntryPointWidgetParams); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollapsibleDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WealthDisplayDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_insights_networth_ui_ui_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VisibilityDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_insights_networth_ui_ui_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*DashboardHeader_SectionHeaderZeroState)(nil),
		(*DashboardHeader_SectionHeaderConnectedState)(nil),
	}
	file_api_frontend_insights_networth_ui_ui_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*WidgetV2_WealthLandingWidgetParams)(nil),
		(*WidgetV2_WbFeatureEntryPointWidgetParams)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_insights_networth_ui_ui_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_frontend_insights_networth_ui_ui_proto_goTypes,
		DependencyIndexes: file_api_frontend_insights_networth_ui_ui_proto_depIdxs,
		EnumInfos:         file_api_frontend_insights_networth_ui_ui_proto_enumTypes,
		MessageInfos:      file_api_frontend_insights_networth_ui_ui_proto_msgTypes,
	}.Build()
	File_api_frontend_insights_networth_ui_ui_proto = out.File
	file_api_frontend_insights_networth_ui_ui_proto_rawDesc = nil
	file_api_frontend_insights_networth_ui_ui_proto_goTypes = nil
	file_api_frontend_insights_networth_ui_ui_proto_depIdxs = nil
}
