syntax = "proto3";
package frontend.analyser;

import "api/frontend/analyser/details_modal.proto";
import "api/frontend/analyser/icon.proto";
import "api/frontend/analyser/text.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";

option go_package = "github.com/epifi/gamma/api/frontend/analyser";
option java_package = "com.github.epifi.gamma.api.frontend.analyser";

// VisualisationType contains all different visualisation types that can go into an analysis lens.
enum VisualisationType {
  VISUALISATION_TYPE_UNSPECIFIED = 0;
  VISUALISATION_TYPE_PIE_CHART = 1;
  VISUALISATION_HORIZONTAL_BARS = 2;
  VISUALISATION_VERTICAL_BARS = 3;
  VISUALISATION_VERTICAL_BARS_WITH_SLIDER = 4;
  VISUALISATION_SPIDER_WEB = 5;
  VISUALISATION_LINE_CHART = 6;
}

// Visualisation contains data required by client to generate the visualisation.
message Visualisation {
  VisualisationType type = 1;
  // Data required by each visualisations is different, so keeping a oneof field
  // to accommodate requirements of different visualisations
  oneof params {
    PieChartVisualisationParams pie_chart = 2;
    BarParams bars = 3;
    SpiderWebParams spider_web = 5;
    LineChartParams line_chart_params = 7;
  }
  // insight based on the visualisation
  string insight_text = 4;
  Footer footer = 6;
}

enum FooterType {
  FOOTER_TYPE_UNSPECIFIED = 0;
  FOOTER_TYPE_TEXT_ENDING_WITH_IMAGE = 1;
}

message Footer {
  FooterType footer_type = 1;
  oneof footer {
    FooterEndingWithImage footer_ending_with_image = 2;
  }
}

message FooterEndingWithImage {
  string text = 1;
  repeated string image_urls = 2;
}

message PieChartVisualisationParams {
  repeated PieChartDivision items = 1;
}

message PieChartDivision {
  // percentage of pie occupied by the item.
  // sum of percent of all divisions in pie should be 100.
  double percent = 1;
  // color of the pie chart division in hexcode
  string color = 2;
  // display icon to be shown along with the division.
  Icon icon = 3 [deprecated = true];
  deeplink.Deeplink deeplink = 4;
  IconV2 iconV2 = 5;
}

message BarParams {
  repeated Bar bars = 1;
  repeated Axis axes = 2;
  // horizontal lines
  repeated ChartLine horizontal_lines = 3;
  // slider is a hovering line with clickable text box
  // when the slider is moved its value is changed relative to the component in that position
  // for slider perpendicular to the bars, number of slider points = number of bars
  repeated Slider sliders = 4;
}

// AxisPosition is defined relative the the bars
// The base of bar is considered bottom and its opposite direction is top
// Right and Left are relative to the direction of growth of the bars
//             TOP
//       ________________
//      |
// LEFT |     ██          RIGHT
//      | ██  ██
//      | ██  ██      ██
//      | ██  ██  ██  ██
//      | ██  ██  ██  ██
//            BOTTOM
//
//               LEFT
//          ██████████████
//   BOTTOM ████████████████████  TOP
//          ████
//          ███████████
//               RIGHT
enum AxisPosition {
  AXIS_POSITION_UNSPECIFIED = 0;
  AXIS_POSITION_RIGHT = 1;
  AXIS_POSITION_LEFT = 2;
  AXIS_POSITION_BOTTOM = 3;
  AXIS_POSITION_TOP = 4;
}

message Axis {
  // position of axis
  AxisPosition position = 1;
  // labels to be shown on axis
  repeated Label values = 2;
}

// Label is shown for a reference position/point in visualisation
message Label {
  // text to be show at the label position
  string display_text = 1;
  // value to be used for label positioning and relative comparison
  double value = 2;
}

message Bar {
  // to be used to calculate the length of the bar
  double value = 1;
  // display text to be shown along with the bar
  string display_text = 2;
  // color of the bar in #RRGGBB format
  string color = 3;
  // display icon to be shown for the bar
  Icon icon = 4 [deprecated = true];
  deeplink.Deeplink deeplink = 5;
  // horizontal axis value used to position the bar relative to the bottom axis labels
  double horizontal_value = 6;
  // horizontal axis display text to be shown on bottom x axis or top x axis depending on the bar chart implementation
  string horizontal_display_text = 7;
  // display icon to be shown for the bar
  IconV2 iconV2 = 8;
}

// SliderPosition on the chart
enum SliderPosition {
  SLIDER_POSITION_UNSPECIFIED = 0;
  SLIDER_POSITION_RIGHT = 1;
  SLIDER_POSITION_LEFT = 2;
  SLIDER_POSITION_BOTTOM = 3;
  SLIDER_POSITION_TOP = 4;
}

// Slider is a hovering clickable text box bounded to the underlying bar
// When the slider is moved its value is change the bar relative to the position
// https://www.figma.com/file/gdUq5d1eLP5uz6hEeKUvjK/Analyser-%E2%80%A2-Workfile?node-id=5333%3A104151
message Slider {
  SliderPosition position = 1;
  // Points on the slider bar
  repeated SliderPoint slider_points = 2 [deprecated = true];

  SliderType slider_type = 3;
  oneof slider_params {
    SliderBasicPillWithSeekbarParams basic_pill_with_seekbar = 4;
    SliderBasicPillWithNoSeekbarParams basic_pill_with_no_seekbar = 5;
  }
}

message SliderBasicPillWithSeekbarParams {
  repeated BasicPill slider_points = 1;
}

message SliderBasicPillWithNoSeekbarParams {
  repeated BasicPill slider_points = 1;
}

message SliderPoint {
  // cta displaying some info and containing deeplink to next screen
  // https://www.figma.com/file/gdUq5d1eLP5uz6hEeKUvjK/Analyser-%E2%80%A2-Workfile?node-id=5396%3A101550
  Cta cta = 1;
  // text to be shown corresponding to this point on the slider bar
  // https://www.figma.com/file/gdUq5d1eLP5uz6hEeKUvjK/Analyser-%E2%80%A2-Workfile?node-id=5333%3A104203
  TextElement slider_text = 2;
}

message BasicPill {
  // Index corresponding to a point on graph which over which the values will be shown
  int64 index = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text body = 3;
  // cta displaying some info and containing deeplink to next screen
  Cta cta = 4;
}



// ChartLine is a line perpendicular to the value axis
//             TOP
//       ________________
//      |
// LEFT |     ██          RIGHT
//      |-██--██---------- <- ChartLine
//      | ██  ██      ██
//      | ██  ██  ██  ██
//      | ██  ██  ██  ██
//            BOTTOM
// https://www.figma.com/file/gdUq5d1eLP5uz6hEeKUvjK/Analyser-%E2%80%A2-Workfile?node-id=5396%3A101624
message ChartLine {
  // value is used to determine the position of line in the chart
  double value = 1;
  // display text to be shown along with the line
  string display_text = 2;
  // color of line and the corresponding label
  string color = 3;
  // label for the line shown at the bottom  of the chart
  string label_text = 4;
  // optional info icon to be shown alongside the label
  InfoIcon label_info = 5;
}

message SpiderWebParams {
  // The maximum number of levels that spider web will show
  int32 max_levels = 1;
  // The details of the center of web
  SpiderWebCenter center = 2;
  // The shape of the spider web will depend on the number of sections. If the number of sections is 6 then the shape of spider web will be Hexagon.
  repeated SpiderWebSection sections = 3;
  // Spider Web Color (the skeleton)
  // It can be different for different Analysers
  string web_border_color = 4;
}

message SpiderWebCenter {
  // text to show in the center
  TextElement text = 1;
  // Center background color
  string background_color = 2;
  // The number of levels that the center will consume
  int32 levels = 3;
  // details to be shown when user clicks on the center
  DetailsModal details = 4;
}

message SpiderWebSection {
  // name to show for the section
  TextElement name = 1;
  // The number of levels to fill in the section. Should be less than or equal to max_levels
  int32 level = 2;
  // background color for the section
  string background_color = 3;
  // details to be shown when user clicks on the spider web section.
  DetailsModal details = 4;
}

// Figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=4581%3A12725&t=3qJNR2JNL7I5PruX-4
message LineChartParams {
  reserved 1, 2;
  // There can be multiple lines on a line chart.
  repeated Line lines = 3;
  repeated Point points = 4;
  repeated Axis axes = 5;
  // slider is a hovering line with clickable text box
  // when the slider is moved its value is changed relative to the component in that position
  // for slider perpendicular to the line chart, number of slider points = number of points on line
  repeated Slider sliders = 6;
}

message Line {
  // These points will be sorted by value of x
  repeated Point points = 1;
  LineType line_type = 2;
}

message Point {
  double x_axis_value = 1;
  double y_axis_value = 2;
}

enum SliderType {
  SLIDER_TYPE_UNSPECIFIED = 0;
  // It is a type of slider with no seekbar. Title and body are present in the same pill.
  // reference: mf growth analyser line chart interaction: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=7330-18264&t=rLU5lZgqDhf7LpVH-0
  SLIDER_TYPE_BASIC_PILL_WITH_NO_SEEKBAR = 1;
  // It is a type of slider with a seekbar, where title runs over seekbar and body is in a pill below it.
  // reference: time analyser bar chart interaction: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=45-2775&t=oAjUuxsUXSNDPYJ6-0
  SLIDER_TYPE_BASIC_PILL_WITH_SEEKBAR = 2;
}

enum LineType {
  LINE_TYPE_UNSPECIFIED = 0;
  LINE_TYPE_SOLID = 1;
  LINE_TYPE_DASHED = 2;
}
