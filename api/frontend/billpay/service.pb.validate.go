// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/billpay/service.proto

package billpay

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/frontend/billpay/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.RechargeOrderStatus(0)
)

// Validate checks the field values on CreateRechargeOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRechargeOrderRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRechargeOrderRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRechargeOrderRequestMultiError, or nil if none found.
func (m *CreateRechargeOrderRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRechargeOrderRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRechargeOrderRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRechargeOrderRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRechargeOrderRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountType

	// no validation rules for AccountIdentifier

	// no validation rules for OperatorId

	// no validation rules for PlanId

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return CreateRechargeOrderRequestMultiError(errors)
	}

	return nil
}

// CreateRechargeOrderRequestMultiError is an error wrapping multiple
// validation errors returned by CreateRechargeOrderRequest.ValidateAll() if
// the designated constraints aren't met.
type CreateRechargeOrderRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRechargeOrderRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRechargeOrderRequestMultiError) AllErrors() []error { return m }

// CreateRechargeOrderRequestValidationError is the validation error returned
// by CreateRechargeOrderRequest.Validate if the designated constraints aren't met.
type CreateRechargeOrderRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRechargeOrderRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRechargeOrderRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRechargeOrderRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRechargeOrderRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRechargeOrderRequestValidationError) ErrorName() string {
	return "CreateRechargeOrderRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRechargeOrderRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRechargeOrderRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRechargeOrderRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRechargeOrderRequestValidationError{}

// Validate checks the field values on CreateRechargeOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateRechargeOrderResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateRechargeOrderResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateRechargeOrderResponseMultiError, or nil if none found.
func (m *CreateRechargeOrderResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateRechargeOrderResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRechargeOrderResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRechargeOrderResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRechargeOrderResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateRechargeOrderResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateRechargeOrderResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateRechargeOrderResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateRechargeOrderResponseMultiError(errors)
	}

	return nil
}

// CreateRechargeOrderResponseMultiError is an error wrapping multiple
// validation errors returned by CreateRechargeOrderResponse.ValidateAll() if
// the designated constraints aren't met.
type CreateRechargeOrderResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateRechargeOrderResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateRechargeOrderResponseMultiError) AllErrors() []error { return m }

// CreateRechargeOrderResponseValidationError is the validation error returned
// by CreateRechargeOrderResponse.Validate if the designated constraints
// aren't met.
type CreateRechargeOrderResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateRechargeOrderResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateRechargeOrderResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateRechargeOrderResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateRechargeOrderResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateRechargeOrderResponseValidationError) ErrorName() string {
	return "CreateRechargeOrderResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateRechargeOrderResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateRechargeOrderResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateRechargeOrderResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateRechargeOrderResponseValidationError{}

// Validate checks the field values on GetRechargeOrderStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRechargeOrderStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRechargeOrderStatusRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRechargeOrderStatusRequestMultiError, or nil if none found.
func (m *GetRechargeOrderStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRechargeOrderStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargeOrderStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargeOrderStatusRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargeOrderStatusRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	if len(errors) > 0 {
		return GetRechargeOrderStatusRequestMultiError(errors)
	}

	return nil
}

// GetRechargeOrderStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetRechargeOrderStatusRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRechargeOrderStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRechargeOrderStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRechargeOrderStatusRequestMultiError) AllErrors() []error { return m }

// GetRechargeOrderStatusRequestValidationError is the validation error
// returned by GetRechargeOrderStatusRequest.Validate if the designated
// constraints aren't met.
type GetRechargeOrderStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRechargeOrderStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRechargeOrderStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRechargeOrderStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRechargeOrderStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRechargeOrderStatusRequestValidationError) ErrorName() string {
	return "GetRechargeOrderStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRechargeOrderStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRechargeOrderStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRechargeOrderStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRechargeOrderStatusRequestValidationError{}

// Validate checks the field values on GetRechargeOrderStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRechargeOrderStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRechargeOrderStatusResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRechargeOrderStatusResponseMultiError, or nil if none found.
func (m *GetRechargeOrderStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRechargeOrderStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargeOrderStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargeOrderStatusResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargeOrderStatusResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OrderStatus

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargeOrderStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargeOrderStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargeOrderStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRechargeOrderStatusResponseMultiError(errors)
	}

	return nil
}

// GetRechargeOrderStatusResponseMultiError is an error wrapping multiple
// validation errors returned by GetRechargeOrderStatusResponse.ValidateAll()
// if the designated constraints aren't met.
type GetRechargeOrderStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRechargeOrderStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRechargeOrderStatusResponseMultiError) AllErrors() []error { return m }

// GetRechargeOrderStatusResponseValidationError is the validation error
// returned by GetRechargeOrderStatusResponse.Validate if the designated
// constraints aren't met.
type GetRechargeOrderStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRechargeOrderStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRechargeOrderStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRechargeOrderStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRechargeOrderStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRechargeOrderStatusResponseValidationError) ErrorName() string {
	return "GetRechargeOrderStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRechargeOrderStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRechargeOrderStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRechargeOrderStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRechargeOrderStatusResponseValidationError{}

// Validate checks the field values on GetRechargeIntroScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRechargeIntroScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRechargeIntroScreenRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRechargeIntroScreenRequestMultiError, or nil if none found.
func (m *GetRechargeIntroScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRechargeIntroScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargeIntroScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargeIntroScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargeIntroScreenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountType

	// no validation rules for AccountIdentifier

	if len(errors) > 0 {
		return GetRechargeIntroScreenRequestMultiError(errors)
	}

	return nil
}

// GetRechargeIntroScreenRequestMultiError is an error wrapping multiple
// validation errors returned by GetRechargeIntroScreenRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRechargeIntroScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRechargeIntroScreenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRechargeIntroScreenRequestMultiError) AllErrors() []error { return m }

// GetRechargeIntroScreenRequestValidationError is the validation error
// returned by GetRechargeIntroScreenRequest.Validate if the designated
// constraints aren't met.
type GetRechargeIntroScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRechargeIntroScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRechargeIntroScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRechargeIntroScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRechargeIntroScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRechargeIntroScreenRequestValidationError) ErrorName() string {
	return "GetRechargeIntroScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRechargeIntroScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRechargeIntroScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRechargeIntroScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRechargeIntroScreenRequestValidationError{}

// Validate checks the field values on GetRechargeIntroScreenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRechargeIntroScreenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRechargeIntroScreenResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRechargeIntroScreenResponseMultiError, or nil if none found.
func (m *GetRechargeIntroScreenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRechargeIntroScreenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargeIntroScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargeIntroScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargeIntroScreenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLayout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargeIntroScreenResponseValidationError{
					field:  "Layout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargeIntroScreenResponseValidationError{
					field:  "Layout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLayout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargeIntroScreenResponseValidationError{
				field:  "Layout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRechargeIntroScreenResponseMultiError(errors)
	}

	return nil
}

// GetRechargeIntroScreenResponseMultiError is an error wrapping multiple
// validation errors returned by GetRechargeIntroScreenResponse.ValidateAll()
// if the designated constraints aren't met.
type GetRechargeIntroScreenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRechargeIntroScreenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRechargeIntroScreenResponseMultiError) AllErrors() []error { return m }

// GetRechargeIntroScreenResponseValidationError is the validation error
// returned by GetRechargeIntroScreenResponse.Validate if the designated
// constraints aren't met.
type GetRechargeIntroScreenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRechargeIntroScreenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRechargeIntroScreenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRechargeIntroScreenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRechargeIntroScreenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRechargeIntroScreenResponseValidationError) ErrorName() string {
	return "GetRechargeIntroScreenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRechargeIntroScreenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRechargeIntroScreenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRechargeIntroScreenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRechargeIntroScreenResponseValidationError{}

// Validate checks the field values on GetRechargePlansScreenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRechargePlansScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRechargePlansScreenRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRechargePlansScreenRequestMultiError, or nil if none found.
func (m *GetRechargePlansScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRechargePlansScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargePlansScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargePlansScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargePlansScreenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccountType

	// no validation rules for AccountIdentifier

	// no validation rules for OperatorId

	if len(errors) > 0 {
		return GetRechargePlansScreenRequestMultiError(errors)
	}

	return nil
}

// GetRechargePlansScreenRequestMultiError is an error wrapping multiple
// validation errors returned by GetRechargePlansScreenRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRechargePlansScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRechargePlansScreenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRechargePlansScreenRequestMultiError) AllErrors() []error { return m }

// GetRechargePlansScreenRequestValidationError is the validation error
// returned by GetRechargePlansScreenRequest.Validate if the designated
// constraints aren't met.
type GetRechargePlansScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRechargePlansScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRechargePlansScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRechargePlansScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRechargePlansScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRechargePlansScreenRequestValidationError) ErrorName() string {
	return "GetRechargePlansScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRechargePlansScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRechargePlansScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRechargePlansScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRechargePlansScreenRequestValidationError{}

// Validate checks the field values on GetRechargePlansScreenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRechargePlansScreenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRechargePlansScreenResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRechargePlansScreenResponseMultiError, or nil if none found.
func (m *GetRechargePlansScreenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRechargePlansScreenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargePlansScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargePlansScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargePlansScreenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLayout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRechargePlansScreenResponseValidationError{
					field:  "Layout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRechargePlansScreenResponseValidationError{
					field:  "Layout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLayout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRechargePlansScreenResponseValidationError{
				field:  "Layout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OperatorId

	if len(errors) > 0 {
		return GetRechargePlansScreenResponseMultiError(errors)
	}

	return nil
}

// GetRechargePlansScreenResponseMultiError is an error wrapping multiple
// validation errors returned by GetRechargePlansScreenResponse.ValidateAll()
// if the designated constraints aren't met.
type GetRechargePlansScreenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRechargePlansScreenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRechargePlansScreenResponseMultiError) AllErrors() []error { return m }

// GetRechargePlansScreenResponseValidationError is the validation error
// returned by GetRechargePlansScreenResponse.Validate if the designated
// constraints aren't met.
type GetRechargePlansScreenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRechargePlansScreenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRechargePlansScreenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRechargePlansScreenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRechargePlansScreenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRechargePlansScreenResponseValidationError) ErrorName() string {
	return "GetRechargePlansScreenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRechargePlansScreenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRechargePlansScreenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRechargePlansScreenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRechargePlansScreenResponseValidationError{}

// Validate checks the field values on GetBillDetailsConfirmationScreenRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetBillDetailsConfirmationScreenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetBillDetailsConfirmationScreenRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetBillDetailsConfirmationScreenRequestMultiError, or nil if none found.
func (m *GetBillDetailsConfirmationScreenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBillDetailsConfirmationScreenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBillDetailsConfirmationScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBillDetailsConfirmationScreenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBillDetailsConfirmationScreenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	switch v := m.Params.(type) {
	case *GetBillDetailsConfirmationScreenRequest_RechargeParams:
		if v == nil {
			err := GetBillDetailsConfirmationScreenRequestValidationError{
				field:  "Params",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRechargeParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBillDetailsConfirmationScreenRequestValidationError{
						field:  "RechargeParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBillDetailsConfirmationScreenRequestValidationError{
						field:  "RechargeParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRechargeParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBillDetailsConfirmationScreenRequestValidationError{
					field:  "RechargeParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetBillDetailsConfirmationScreenRequestMultiError(errors)
	}

	return nil
}

// GetBillDetailsConfirmationScreenRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetBillDetailsConfirmationScreenRequest.ValidateAll() if the designated
// constraints aren't met.
type GetBillDetailsConfirmationScreenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBillDetailsConfirmationScreenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBillDetailsConfirmationScreenRequestMultiError) AllErrors() []error { return m }

// GetBillDetailsConfirmationScreenRequestValidationError is the validation
// error returned by GetBillDetailsConfirmationScreenRequest.Validate if the
// designated constraints aren't met.
type GetBillDetailsConfirmationScreenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBillDetailsConfirmationScreenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBillDetailsConfirmationScreenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBillDetailsConfirmationScreenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBillDetailsConfirmationScreenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBillDetailsConfirmationScreenRequestValidationError) ErrorName() string {
	return "GetBillDetailsConfirmationScreenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBillDetailsConfirmationScreenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBillDetailsConfirmationScreenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBillDetailsConfirmationScreenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBillDetailsConfirmationScreenRequestValidationError{}

// Validate checks the field values on GetBillDetailsConfirmationScreenResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetBillDetailsConfirmationScreenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetBillDetailsConfirmationScreenResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// GetBillDetailsConfirmationScreenResponseMultiError, or nil if none found.
func (m *GetBillDetailsConfirmationScreenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBillDetailsConfirmationScreenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBillDetailsConfirmationScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBillDetailsConfirmationScreenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBillDetailsConfirmationScreenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLayout()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetBillDetailsConfirmationScreenResponseValidationError{
					field:  "Layout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetBillDetailsConfirmationScreenResponseValidationError{
					field:  "Layout",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLayout()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetBillDetailsConfirmationScreenResponseValidationError{
				field:  "Layout",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ClientRequestId

	switch v := m.PaymentParams.(type) {
	case *GetBillDetailsConfirmationScreenResponse_RechargeParams:
		if v == nil {
			err := GetBillDetailsConfirmationScreenResponseValidationError{
				field:  "PaymentParams",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetRechargeParams()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBillDetailsConfirmationScreenResponseValidationError{
						field:  "RechargeParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBillDetailsConfirmationScreenResponseValidationError{
						field:  "RechargeParams",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetRechargeParams()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBillDetailsConfirmationScreenResponseValidationError{
					field:  "RechargeParams",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetBillDetailsConfirmationScreenResponseMultiError(errors)
	}

	return nil
}

// GetBillDetailsConfirmationScreenResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetBillDetailsConfirmationScreenResponse.ValidateAll() if the designated
// constraints aren't met.
type GetBillDetailsConfirmationScreenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBillDetailsConfirmationScreenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBillDetailsConfirmationScreenResponseMultiError) AllErrors() []error { return m }

// GetBillDetailsConfirmationScreenResponseValidationError is the validation
// error returned by GetBillDetailsConfirmationScreenResponse.Validate if the
// designated constraints aren't met.
type GetBillDetailsConfirmationScreenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBillDetailsConfirmationScreenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBillDetailsConfirmationScreenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBillDetailsConfirmationScreenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBillDetailsConfirmationScreenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBillDetailsConfirmationScreenResponseValidationError) ErrorName() string {
	return "GetBillDetailsConfirmationScreenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetBillDetailsConfirmationScreenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBillDetailsConfirmationScreenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBillDetailsConfirmationScreenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBillDetailsConfirmationScreenResponseValidationError{}
