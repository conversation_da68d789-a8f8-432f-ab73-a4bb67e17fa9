// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/billpay/service.proto

package billpay

import (
	enums "github.com/epifi/gamma/api/frontend/billpay/enums"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	billpay "github.com/epifi/gamma/api/typesv2/billpay"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Request/Response for CreateRechargeOrder
type CreateRechargeOrderRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// expected to be of RechargeAccountType enum
	AccountType string `protobuf:"bytes,2,opt,name=account_type,json=accountType,proto3" json:"account_type,omitempty"`
	// account identifier of corresponding account_type e.g. mobile number for MOBILE account
	AccountIdentifier string `protobuf:"bytes,3,opt,name=account_identifier,json=accountIdentifier,proto3" json:"account_identifier,omitempty"`
	// operator for the recharge e.g. Jio/Airtel/Vi for mobile recharges
	OperatorId string `protobuf:"bytes,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// unique identifier for plans provided by an operator
	PlanId          string `protobuf:"bytes,5,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`
	ClientRequestId string `protobuf:"bytes,6,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *CreateRechargeOrderRequest) Reset() {
	*x = CreateRechargeOrderRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_billpay_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRechargeOrderRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRechargeOrderRequest) ProtoMessage() {}

func (x *CreateRechargeOrderRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_billpay_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRechargeOrderRequest.ProtoReflect.Descriptor instead.
func (*CreateRechargeOrderRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_billpay_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateRechargeOrderRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *CreateRechargeOrderRequest) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetAccountIdentifier() string {
	if x != nil {
		return x.AccountIdentifier
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetPlanId() string {
	if x != nil {
		return x.PlanId
	}
	return ""
}

func (x *CreateRechargeOrderRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

type CreateRechargeOrderResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Deeplink that client should honour when success response is received
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *CreateRechargeOrderResponse) Reset() {
	*x = CreateRechargeOrderResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_billpay_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateRechargeOrderResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRechargeOrderResponse) ProtoMessage() {}

func (x *CreateRechargeOrderResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_billpay_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRechargeOrderResponse.ProtoReflect.Descriptor instead.
func (*CreateRechargeOrderResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_billpay_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateRechargeOrderResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *CreateRechargeOrderResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

// Request/Response for GetRechargeOrderStatus
type GetRechargeOrderStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req             *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	ClientRequestId string                `protobuf:"bytes,2,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
}

func (x *GetRechargeOrderStatusRequest) Reset() {
	*x = GetRechargeOrderStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_billpay_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRechargeOrderStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRechargeOrderStatusRequest) ProtoMessage() {}

func (x *GetRechargeOrderStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_billpay_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRechargeOrderStatusRequest.ProtoReflect.Descriptor instead.
func (*GetRechargeOrderStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_billpay_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetRechargeOrderStatusRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetRechargeOrderStatusRequest) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

type GetRechargeOrderStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// Status of the recharge order
	OrderStatus enums.RechargeOrderStatus `protobuf:"varint,2,opt,name=order_status,json=orderStatus,proto3,enum=frontend.billpay.enums.RechargeOrderStatus" json:"order_status,omitempty"`
	// Deeplink that client should honour. If RECHARGE_POLLING_SCREEN is received, client will keep polling
	NextAction *deeplink.Deeplink `protobuf:"bytes,3,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *GetRechargeOrderStatusResponse) Reset() {
	*x = GetRechargeOrderStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_billpay_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRechargeOrderStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRechargeOrderStatusResponse) ProtoMessage() {}

func (x *GetRechargeOrderStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_billpay_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRechargeOrderStatusResponse.ProtoReflect.Descriptor instead.
func (*GetRechargeOrderStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_billpay_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetRechargeOrderStatusResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetRechargeOrderStatusResponse) GetOrderStatus() enums.RechargeOrderStatus {
	if x != nil {
		return x.OrderStatus
	}
	return enums.RechargeOrderStatus(0)
}

func (x *GetRechargeOrderStatusResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type GetRechargeIntroScreenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// expected to be of RechargeAccountType enum
	AccountType string `protobuf:"bytes,2,opt,name=account_type,json=accountType,proto3" json:"account_type,omitempty"`
	// account identifier of corresponding account_type e.g. mobile number for MOBILE account
	//
	// Deprecated: Marked as deprecated in api/frontend/billpay/service.proto.
	AccountIdentifier string `protobuf:"bytes,3,opt,name=account_identifier,json=accountIdentifier,proto3" json:"account_identifier,omitempty"`
}

func (x *GetRechargeIntroScreenRequest) Reset() {
	*x = GetRechargeIntroScreenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_billpay_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRechargeIntroScreenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRechargeIntroScreenRequest) ProtoMessage() {}

func (x *GetRechargeIntroScreenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_billpay_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRechargeIntroScreenRequest.ProtoReflect.Descriptor instead.
func (*GetRechargeIntroScreenRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_billpay_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetRechargeIntroScreenRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetRechargeIntroScreenRequest) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

// Deprecated: Marked as deprecated in api/frontend/billpay/service.proto.
func (x *GetRechargeIntroScreenRequest) GetAccountIdentifier() string {
	if x != nil {
		return x.AccountIdentifier
	}
	return ""
}

type GetRechargeIntroScreenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader       `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Layout     *billpay.RechargeIntroScreen `protobuf:"bytes,2,opt,name=layout,proto3" json:"layout,omitempty"`
}

func (x *GetRechargeIntroScreenResponse) Reset() {
	*x = GetRechargeIntroScreenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_billpay_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRechargeIntroScreenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRechargeIntroScreenResponse) ProtoMessage() {}

func (x *GetRechargeIntroScreenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_billpay_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRechargeIntroScreenResponse.ProtoReflect.Descriptor instead.
func (*GetRechargeIntroScreenResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_billpay_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetRechargeIntroScreenResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetRechargeIntroScreenResponse) GetLayout() *billpay.RechargeIntroScreen {
	if x != nil {
		return x.Layout
	}
	return nil
}

// Request/Response for GetRechargePlans
type GetRechargePlansScreenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// expected to be of RechargeAccountType enum
	AccountType string `protobuf:"bytes,2,opt,name=account_type,json=accountType,proto3" json:"account_type,omitempty"`
	// account identifier of corresponding account_type e.g. mobile number for MOBILE account
	AccountIdentifier string `protobuf:"bytes,3,opt,name=account_identifier,json=accountIdentifier,proto3" json:"account_identifier,omitempty"`
	// operator for the recharge e.g. Jio/Airtel/Vi for mobile recharges
	//
	// Deprecated: Marked as deprecated in api/frontend/billpay/service.proto.
	OperatorId string `protobuf:"bytes,4,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
}

func (x *GetRechargePlansScreenRequest) Reset() {
	*x = GetRechargePlansScreenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_billpay_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRechargePlansScreenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRechargePlansScreenRequest) ProtoMessage() {}

func (x *GetRechargePlansScreenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_billpay_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRechargePlansScreenRequest.ProtoReflect.Descriptor instead.
func (*GetRechargePlansScreenRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_billpay_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetRechargePlansScreenRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetRechargePlansScreenRequest) GetAccountType() string {
	if x != nil {
		return x.AccountType
	}
	return ""
}

func (x *GetRechargePlansScreenRequest) GetAccountIdentifier() string {
	if x != nil {
		return x.AccountIdentifier
	}
	return ""
}

// Deprecated: Marked as deprecated in api/frontend/billpay/service.proto.
func (x *GetRechargePlansScreenRequest) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

type GetRechargePlansScreenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader       `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Layout     *billpay.RechargePlansScreen `protobuf:"bytes,2,opt,name=layout,proto3" json:"layout,omitempty"`
	// operator for the recharge e.g. Jio/Airtel/Vi for mobile recharges
	OperatorId string `protobuf:"bytes,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
}

func (x *GetRechargePlansScreenResponse) Reset() {
	*x = GetRechargePlansScreenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_billpay_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRechargePlansScreenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRechargePlansScreenResponse) ProtoMessage() {}

func (x *GetRechargePlansScreenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_billpay_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRechargePlansScreenResponse.ProtoReflect.Descriptor instead.
func (*GetRechargePlansScreenResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_billpay_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetRechargePlansScreenResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetRechargePlansScreenResponse) GetLayout() *billpay.RechargePlansScreen {
	if x != nil {
		return x.Layout
	}
	return nil
}

func (x *GetRechargePlansScreenResponse) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

type GetBillDetailsConfirmationScreenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// Types that are assignable to Params:
	//
	//	*GetBillDetailsConfirmationScreenRequest_RechargeParams
	Params isGetBillDetailsConfirmationScreenRequest_Params `protobuf_oneof:"params"`
}

func (x *GetBillDetailsConfirmationScreenRequest) Reset() {
	*x = GetBillDetailsConfirmationScreenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_billpay_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBillDetailsConfirmationScreenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBillDetailsConfirmationScreenRequest) ProtoMessage() {}

func (x *GetBillDetailsConfirmationScreenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_billpay_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBillDetailsConfirmationScreenRequest.ProtoReflect.Descriptor instead.
func (*GetBillDetailsConfirmationScreenRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_billpay_service_proto_rawDescGZIP(), []int{8}
}

func (x *GetBillDetailsConfirmationScreenRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (m *GetBillDetailsConfirmationScreenRequest) GetParams() isGetBillDetailsConfirmationScreenRequest_Params {
	if m != nil {
		return m.Params
	}
	return nil
}

func (x *GetBillDetailsConfirmationScreenRequest) GetRechargeParams() *billpay.RechargeParams {
	if x, ok := x.GetParams().(*GetBillDetailsConfirmationScreenRequest_RechargeParams); ok {
		return x.RechargeParams
	}
	return nil
}

type isGetBillDetailsConfirmationScreenRequest_Params interface {
	isGetBillDetailsConfirmationScreenRequest_Params()
}

type GetBillDetailsConfirmationScreenRequest_RechargeParams struct {
	RechargeParams *billpay.RechargeParams `protobuf:"bytes,2,opt,name=recharge_params,json=rechargeParams,proto3,oneof"`
}

func (*GetBillDetailsConfirmationScreenRequest_RechargeParams) isGetBillDetailsConfirmationScreenRequest_Params() {
}

type GetBillDetailsConfirmationScreenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader                 `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	Layout     *billpay.BillDetailsConfirmationScreen `protobuf:"bytes,2,opt,name=layout,proto3" json:"layout,omitempty"`
	// unique id to be used for the payment flow
	ClientRequestId string `protobuf:"bytes,3,opt,name=client_request_id,json=clientRequestId,proto3" json:"client_request_id,omitempty"`
	// one-of field indicating which payment flow should be initiated by the client.
	// Contains necessary parameters for the selected flow.
	// e.g., if `recharge_params` is received, start the `CreateRechargeOrder` flow.
	//
	// Types that are assignable to PaymentParams:
	//
	//	*GetBillDetailsConfirmationScreenResponse_RechargeParams
	PaymentParams isGetBillDetailsConfirmationScreenResponse_PaymentParams `protobuf_oneof:"payment_params"`
}

func (x *GetBillDetailsConfirmationScreenResponse) Reset() {
	*x = GetBillDetailsConfirmationScreenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_billpay_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBillDetailsConfirmationScreenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBillDetailsConfirmationScreenResponse) ProtoMessage() {}

func (x *GetBillDetailsConfirmationScreenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_billpay_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBillDetailsConfirmationScreenResponse.ProtoReflect.Descriptor instead.
func (*GetBillDetailsConfirmationScreenResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_billpay_service_proto_rawDescGZIP(), []int{9}
}

func (x *GetBillDetailsConfirmationScreenResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetBillDetailsConfirmationScreenResponse) GetLayout() *billpay.BillDetailsConfirmationScreen {
	if x != nil {
		return x.Layout
	}
	return nil
}

func (x *GetBillDetailsConfirmationScreenResponse) GetClientRequestId() string {
	if x != nil {
		return x.ClientRequestId
	}
	return ""
}

func (m *GetBillDetailsConfirmationScreenResponse) GetPaymentParams() isGetBillDetailsConfirmationScreenResponse_PaymentParams {
	if m != nil {
		return m.PaymentParams
	}
	return nil
}

func (x *GetBillDetailsConfirmationScreenResponse) GetRechargeParams() *billpay.RechargeParams {
	if x, ok := x.GetPaymentParams().(*GetBillDetailsConfirmationScreenResponse_RechargeParams); ok {
		return x.RechargeParams
	}
	return nil
}

type isGetBillDetailsConfirmationScreenResponse_PaymentParams interface {
	isGetBillDetailsConfirmationScreenResponse_PaymentParams()
}

type GetBillDetailsConfirmationScreenResponse_RechargeParams struct {
	RechargeParams *billpay.RechargeParams `protobuf:"bytes,4,opt,name=recharge_params,json=rechargeParams,proto3,oneof"`
}

func (*GetBillDetailsConfirmationScreenResponse_RechargeParams) isGetBillDetailsConfirmationScreenResponse_PaymentParams() {
}

var File_api_frontend_billpay_service_proto protoreflect.FileDescriptor

var file_api_frontend_billpay_service_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2f, 0x65, 0x6e, 0x75,
	0x6d, 0x73, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69,
	0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79,
	0x2f, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61,
	0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70,
	0x61, 0x79, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x86, 0x02, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65,
	0x71, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66,
	0x69, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x70, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x2a, 0x0a,
	0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x9d, 0x01, 0x0a, 0x1b, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73,
	0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e,
	0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e,
	0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x7d, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65,
	0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x2a, 0x0a, 0x11,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xf0, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72,
	0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4e, 0x0a,
	0x0c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x52, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x0b, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a,
	0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52,
	0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xa7, 0x01, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x6e, 0x74, 0x72, 0x6f,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12,
	0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x31, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02,
	0x18, 0x01, 0x52, 0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x66, 0x69, 0x65, 0x72, 0x22, 0xa4, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x06, 0x6c, 0x61,
	0x79, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79,
	0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x52, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x22, 0xc8, 0x01, 0x0a,
	0x1d, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e,
	0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30,
	0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71,
	0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x12, 0x23, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22, 0xc5, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x06,
	0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70,
	0x61, 0x79, 0x2e, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74, 0x12, 0x1f,
	0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x22,
	0xb5, 0x01, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72,
	0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x4e, 0x0a,
	0x0f, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x72,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x08, 0x0a,
	0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xc6, 0x02, 0x0a, 0x28, 0x47, 0x65, 0x74, 0x42,
	0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x4a, 0x0a, 0x06, 0x6c, 0x61, 0x79, 0x6f, 0x75, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x42, 0x69, 0x6c,
	0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x06, 0x6c, 0x61, 0x79, 0x6f,
	0x75, 0x74, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x4e,
	0x0a, 0x0f, 0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x52, 0x65,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x0e,
	0x72, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42, 0x10,
	0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x32, 0x90, 0x05, 0x0a, 0x07, 0x42, 0x69, 0x6c, 0x6c, 0x50, 0x61, 0x79, 0x12, 0x72, 0x0a, 0x13,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x12, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x69, 0x6c,
	0x6c, 0x70, 0x61, 0x79, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x7b, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x66, 0x72,
	0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x47,
	0x65, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a,
	0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x6e, 0x74, 0x72,
	0x6f, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x52,
	0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x49, 0x6e, 0x74, 0x72, 0x6f, 0x53, 0x63, 0x72, 0x65,
	0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x7b, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x53, 0x63,
	0x72, 0x65, 0x65, 0x6e, 0x12, 0x2f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x63, 0x68,
	0x61, 0x72, 0x67, 0x65, 0x50, 0x6c, 0x61, 0x6e, 0x73, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x99, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x42,
	0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x12, 0x39, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x69, 0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61, 0x79, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69,
	0x6c, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x5c, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x70,
	0x61, 0x79, 0x50, 0x01, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x62, 0x69, 0x6c, 0x6c, 0x70, 0x61,
	0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_billpay_service_proto_rawDescOnce sync.Once
	file_api_frontend_billpay_service_proto_rawDescData = file_api_frontend_billpay_service_proto_rawDesc
)

func file_api_frontend_billpay_service_proto_rawDescGZIP() []byte {
	file_api_frontend_billpay_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_billpay_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_billpay_service_proto_rawDescData)
	})
	return file_api_frontend_billpay_service_proto_rawDescData
}

var file_api_frontend_billpay_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_frontend_billpay_service_proto_goTypes = []interface{}{
	(*CreateRechargeOrderRequest)(nil),               // 0: frontend.billpay.CreateRechargeOrderRequest
	(*CreateRechargeOrderResponse)(nil),              // 1: frontend.billpay.CreateRechargeOrderResponse
	(*GetRechargeOrderStatusRequest)(nil),            // 2: frontend.billpay.GetRechargeOrderStatusRequest
	(*GetRechargeOrderStatusResponse)(nil),           // 3: frontend.billpay.GetRechargeOrderStatusResponse
	(*GetRechargeIntroScreenRequest)(nil),            // 4: frontend.billpay.GetRechargeIntroScreenRequest
	(*GetRechargeIntroScreenResponse)(nil),           // 5: frontend.billpay.GetRechargeIntroScreenResponse
	(*GetRechargePlansScreenRequest)(nil),            // 6: frontend.billpay.GetRechargePlansScreenRequest
	(*GetRechargePlansScreenResponse)(nil),           // 7: frontend.billpay.GetRechargePlansScreenResponse
	(*GetBillDetailsConfirmationScreenRequest)(nil),  // 8: frontend.billpay.GetBillDetailsConfirmationScreenRequest
	(*GetBillDetailsConfirmationScreenResponse)(nil), // 9: frontend.billpay.GetBillDetailsConfirmationScreenResponse
	(*header.RequestHeader)(nil),                     // 10: frontend.header.RequestHeader
	(*header.ResponseHeader)(nil),                    // 11: frontend.header.ResponseHeader
	(*deeplink.Deeplink)(nil),                        // 12: frontend.deeplink.Deeplink
	(enums.RechargeOrderStatus)(0),                   // 13: frontend.billpay.enums.RechargeOrderStatus
	(*billpay.RechargeIntroScreen)(nil),              // 14: api.typesv2.billpay.RechargeIntroScreen
	(*billpay.RechargePlansScreen)(nil),              // 15: api.typesv2.billpay.RechargePlansScreen
	(*billpay.RechargeParams)(nil),                   // 16: api.typesv2.billpay.RechargeParams
	(*billpay.BillDetailsConfirmationScreen)(nil),    // 17: api.typesv2.billpay.BillDetailsConfirmationScreen
}
var file_api_frontend_billpay_service_proto_depIdxs = []int32{
	10, // 0: frontend.billpay.CreateRechargeOrderRequest.req:type_name -> frontend.header.RequestHeader
	11, // 1: frontend.billpay.CreateRechargeOrderResponse.resp_header:type_name -> frontend.header.ResponseHeader
	12, // 2: frontend.billpay.CreateRechargeOrderResponse.next_action:type_name -> frontend.deeplink.Deeplink
	10, // 3: frontend.billpay.GetRechargeOrderStatusRequest.req:type_name -> frontend.header.RequestHeader
	11, // 4: frontend.billpay.GetRechargeOrderStatusResponse.resp_header:type_name -> frontend.header.ResponseHeader
	13, // 5: frontend.billpay.GetRechargeOrderStatusResponse.order_status:type_name -> frontend.billpay.enums.RechargeOrderStatus
	12, // 6: frontend.billpay.GetRechargeOrderStatusResponse.next_action:type_name -> frontend.deeplink.Deeplink
	10, // 7: frontend.billpay.GetRechargeIntroScreenRequest.req:type_name -> frontend.header.RequestHeader
	11, // 8: frontend.billpay.GetRechargeIntroScreenResponse.resp_header:type_name -> frontend.header.ResponseHeader
	14, // 9: frontend.billpay.GetRechargeIntroScreenResponse.layout:type_name -> api.typesv2.billpay.RechargeIntroScreen
	10, // 10: frontend.billpay.GetRechargePlansScreenRequest.req:type_name -> frontend.header.RequestHeader
	11, // 11: frontend.billpay.GetRechargePlansScreenResponse.resp_header:type_name -> frontend.header.ResponseHeader
	15, // 12: frontend.billpay.GetRechargePlansScreenResponse.layout:type_name -> api.typesv2.billpay.RechargePlansScreen
	10, // 13: frontend.billpay.GetBillDetailsConfirmationScreenRequest.req:type_name -> frontend.header.RequestHeader
	16, // 14: frontend.billpay.GetBillDetailsConfirmationScreenRequest.recharge_params:type_name -> api.typesv2.billpay.RechargeParams
	11, // 15: frontend.billpay.GetBillDetailsConfirmationScreenResponse.resp_header:type_name -> frontend.header.ResponseHeader
	17, // 16: frontend.billpay.GetBillDetailsConfirmationScreenResponse.layout:type_name -> api.typesv2.billpay.BillDetailsConfirmationScreen
	16, // 17: frontend.billpay.GetBillDetailsConfirmationScreenResponse.recharge_params:type_name -> api.typesv2.billpay.RechargeParams
	0,  // 18: frontend.billpay.BillPay.CreateRechargeOrder:input_type -> frontend.billpay.CreateRechargeOrderRequest
	2,  // 19: frontend.billpay.BillPay.GetRechargeOrderStatus:input_type -> frontend.billpay.GetRechargeOrderStatusRequest
	4,  // 20: frontend.billpay.BillPay.GetRechargeIntroScreen:input_type -> frontend.billpay.GetRechargeIntroScreenRequest
	6,  // 21: frontend.billpay.BillPay.GetRechargePlansScreen:input_type -> frontend.billpay.GetRechargePlansScreenRequest
	8,  // 22: frontend.billpay.BillPay.GetBillDetailsConfirmationScreen:input_type -> frontend.billpay.GetBillDetailsConfirmationScreenRequest
	1,  // 23: frontend.billpay.BillPay.CreateRechargeOrder:output_type -> frontend.billpay.CreateRechargeOrderResponse
	3,  // 24: frontend.billpay.BillPay.GetRechargeOrderStatus:output_type -> frontend.billpay.GetRechargeOrderStatusResponse
	5,  // 25: frontend.billpay.BillPay.GetRechargeIntroScreen:output_type -> frontend.billpay.GetRechargeIntroScreenResponse
	7,  // 26: frontend.billpay.BillPay.GetRechargePlansScreen:output_type -> frontend.billpay.GetRechargePlansScreenResponse
	9,  // 27: frontend.billpay.BillPay.GetBillDetailsConfirmationScreen:output_type -> frontend.billpay.GetBillDetailsConfirmationScreenResponse
	23, // [23:28] is the sub-list for method output_type
	18, // [18:23] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_api_frontend_billpay_service_proto_init() }
func file_api_frontend_billpay_service_proto_init() {
	if File_api_frontend_billpay_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_billpay_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRechargeOrderRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_billpay_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateRechargeOrderResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_billpay_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRechargeOrderStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_billpay_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRechargeOrderStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_billpay_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRechargeIntroScreenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_billpay_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRechargeIntroScreenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_billpay_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRechargePlansScreenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_billpay_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRechargePlansScreenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_billpay_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBillDetailsConfirmationScreenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_billpay_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBillDetailsConfirmationScreenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_billpay_service_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*GetBillDetailsConfirmationScreenRequest_RechargeParams)(nil),
	}
	file_api_frontend_billpay_service_proto_msgTypes[9].OneofWrappers = []interface{}{
		(*GetBillDetailsConfirmationScreenResponse_RechargeParams)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_billpay_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_billpay_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_billpay_service_proto_depIdxs,
		MessageInfos:      file_api_frontend_billpay_service_proto_msgTypes,
	}.Build()
	File_api_frontend_billpay_service_proto = out.File
	file_api_frontend_billpay_service_proto_rawDesc = nil
	file_api_frontend_billpay_service_proto_goTypes = nil
	file_api_frontend_billpay_service_proto_depIdxs = nil
}
