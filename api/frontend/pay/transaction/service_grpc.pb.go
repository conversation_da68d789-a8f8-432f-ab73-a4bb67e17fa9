// protolint:disable MAX_LINE_LENGTH

// Frontend RPC for operations involving payment transactions

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/pay/transaction/service.proto

package transaction

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Transaction_CreateFundTransferOrder_FullMethodName              = "/frontend.pay.transaction.Transaction/CreateFundTransferOrder"
	Transaction_CreateP2PCollectOrder_FullMethodName                = "/frontend.pay.transaction.Transaction/CreateP2PCollectOrder"
	Transaction_InitiatePayment_FullMethodName                      = "/frontend.pay.transaction.Transaction/InitiatePayment"
	Transaction_GetOrderStatus_FullMethodName                       = "/frontend.pay.transaction.Transaction/GetOrderStatus"
	Transaction_GetCollectOrderTransactionDetails_FullMethodName    = "/frontend.pay.transaction.Transaction/GetCollectOrderTransactionDetails"
	Transaction_GetEligibleAccountsForPayment_FullMethodName        = "/frontend.pay.transaction.Transaction/GetEligibleAccountsForPayment"
	Transaction_InitiateCollect_FullMethodName                      = "/frontend.pay.transaction.Transaction/InitiateCollect"
	Transaction_DismissCollect_FullMethodName                       = "/frontend.pay.transaction.Transaction/DismissCollect"
	Transaction_CreateURNOrder_FullMethodName                       = "/frontend.pay.transaction.Transaction/CreateURNOrder"
	Transaction_GetOrderReceipt_FullMethodName                      = "/frontend.pay.transaction.Transaction/GetOrderReceipt"
	Transaction_RaiseDispute_FullMethodName                         = "/frontend.pay.transaction.Transaction/RaiseDispute"
	Transaction_GetAddFundParams_FullMethodName                     = "/frontend.pay.transaction.Transaction/GetAddFundParams"
	Transaction_AuthoriseFundTransfer_FullMethodName                = "/frontend.pay.transaction.Transaction/AuthoriseFundTransfer"
	Transaction_CheckIfAddFundsAllowed_FullMethodName               = "/frontend.pay.transaction.Transaction/CheckIfAddFundsAllowed"
	Transaction_GetEligibleAccountsForPaymentV1_FullMethodName      = "/frontend.pay.transaction.Transaction/GetEligibleAccountsForPaymentV1"
	Transaction_GetChatHeadsForPaymentViaNumber_FullMethodName      = "/frontend.pay.transaction.Transaction/GetChatHeadsForPaymentViaNumber"
	Transaction_GetAddFundsScreenDetailsV2_FullMethodName           = "/frontend.pay.transaction.Transaction/GetAddFundsScreenDetailsV2"
	Transaction_ManualBalanceRefreshForOnbAddFundsV2_FullMethodName = "/frontend.pay.transaction.Transaction/ManualBalanceRefreshForOnbAddFundsV2"
	Transaction_GetPaymentOptions_FullMethodName                    = "/frontend.pay.transaction.Transaction/GetPaymentOptions"
	Transaction_CreateFundTransferOrderV1_FullMethodName            = "/frontend.pay.transaction.Transaction/CreateFundTransferOrderV1"
	Transaction_MarkPostPayment_FullMethodName                      = "/frontend.pay.transaction.Transaction/MarkPostPayment"
)

// TransactionClient is the client API for Transaction service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TransactionClient interface {
	// RPC method to create an order with fund transfer workflow.
	//
	// An Order is a concept between client and Epifi and is abstracted
	// from the partner banks. It helps to manage transaction requests and their workflow.
	//
	// It returns decision engine's preferred payment protocol, payment instrument and other transaction
	// related info back to the Client.
	// This information will be used by client-
	// 1. To invoke `GetCredentials` in common library.
	// 2. To invoke `InitiatePayment` with the cred block which executes the transaction
	CreateFundTransferOrder(ctx context.Context, in *CreateFundTransferOrderRequest, opts ...grpc.CallOption) (*CreateFundTransferOrderResponse, error)
	// RPC method to create an order for P2P Collect workflow.
	//
	// It returns order id which client can use as an identifier of the request.
	// This information will be used by client to invoke `InitiateCollect` which registers collect request.
	// The RPC only accepts collect request between two individuals.
	// Hence, Any Collect request to merchant VPA will be rejected
	CreateP2PCollectOrder(ctx context.Context, in *CreateP2PCollectOrderRequest, opts ...grpc.CallOption) (*CreateP2PCollectOrderResponse, error)
	// RPC method to create transaction and initiate the payment.
	//
	// This is an asynchronous API such that, payment may not be completed immediately.
	// This API, hence, will respond with an Acknowledgement message immediately.
	//
	// Any updates on the payment has to be polled with `GetOrderStatus` RPC method
	InitiatePayment(ctx context.Context, in *InitiatePaymentRequest, opts ...grpc.CallOption) (*InitiatePaymentResponse, error)
	// RPC facilitates enquiry of order status - identified by a unique `order_id`
	// This can also give the status of a recurring payment registration.
	GetOrderStatus(ctx context.Context, in *GetOrderStatusRequest, opts ...grpc.CallOption) (*GetOrderStatusResponse, error)
	// RPC method to return necessary transaction related information for a given collect order.
	// UPI CL requires transaction parameters to capture the credentials
	// that are required to process the transaction
	//
	// This method is to be used by client to fetch transaction parameters
	// when user wishes to make a payment against a collect request.
	//
	// RPC returns a list of eligible accounts through which user can initiate the payment
	// It also returns parameters such as payment instrument, txn-id, merchant_ref_id
	// that are required to complete the transaction
	//
	// Client can use this information for the following:
	// 1. To invoke `GetCredentials` in common library.
	// 2. To invoke `InitiatePayment` with the cred block which executes the transaction
	GetCollectOrderTransactionDetails(ctx context.Context, in *GetCollectOrderTransactionDetailsRequest, opts ...grpc.CallOption) (*GetCollectOrderTransactionDetailsResponse, error)
	// Deprecated: Do not use.
	// RPC to fetch the accounts eligible for a user to send and receive a payment
	// contains information related to eligible accounts for making a payment. Client can surface this information to the user while he/she is trying to make a payment.
	// Since this information will be static mostly client can choose to cache this information
	// This RPC has been deprecated and is merged with frontend.user.GetUserSessionDetails.
	GetEligibleAccountsForPayment(ctx context.Context, in *GetEligibleAccountsForPaymentRequest, opts ...grpc.CallOption) (*GetEligibleAccountsForPaymentResponse, error)
	// RPC to initiate a collect request with the system.
	//
	// This is an asynchronous API. So, collect registration may not be completed immediately.
	// This API, hence, will respond with an Acknowledgement message immediately.
	//
	// Any updates on the collect status has to be polled with `GetOrderStatus` RPC method
	InitiateCollect(ctx context.Context, in *InitiateCollectRequest, opts ...grpc.CallOption) (*InitiateCollectResponse, error)
	// RPC to dismiss a collect request
	// A client can use this when a user wants to dismiss a collect request. A user can dismiss a collect request in the following scenarios-
	//  1. On receiving a collect request from another actor be it internal or external.
	//  2. When a user sends a collect request to an internal epiFi user.
	//     NOTE: a collect order whose payment is in progress can't be dismissed
	DismissCollect(ctx context.Context, in *DismissCollectRequest, opts ...grpc.CallOption) (*DismissCollectResponse, error)
	// RPC method to create an order for outgoing intents/ dynamic QR
	// eg. Adding funds to epifi account using 3rd party apps
	//
	// An Order is a concept between client and Epifi and is abstracted
	// from the partner banks. It helps to manage transaction requests and their workflow.
	//
	// returns the orderId and URN to the client
	// Client can use it to create dynamic QR or Intent to the 3rd party app based on user's preference,
	// Urn will contain parameters such as payeeVPA, amount, txnId, refId etc and will be signed by the PSP's key.
	// 3rd party app is expected to use txnId and refId from URN to initiate the payment with NPCI
	CreateURNOrder(ctx context.Context, in *CreateURNOrderRequest, opts ...grpc.CallOption) (*CreateURNOrderResponse, error)
	// RPC method to fetch an order receipt
	// Client is expected to use this method in order to fetch granular details
	// regarding the state of a payment and display it to the user.
	//
	// It returns necessary information such as transaction id, RRN/UTR, etc. A
	// A user can refer to the information for various purposes, but not limited to:
	// 1. User can share this information as a proof of payment with the other actor.
	// 2. User can raise dispute against an suspicious transaction and claim refunds.
	GetOrderReceipt(ctx context.Context, in *GetOrderReceiptRequest, opts ...grpc.CallOption) (*GetOrderReceiptResponse, error)
	// Deprecated: Do not use.
	// RPC method to raise dispute for a transaction
	// A client can use this to raise dispute for any transaction. Dispute can be raised for a transaction only after the
	// cooldown period for the transaction which is 24 hours for UPI transaction and 3 hours for NEFT/IMPS/IntraBank/RTGS
	// transaction. Dispute can be raised only if the transaction state is not expired or cancelled. Once dispute is raised
	// for a transaction client cannot re raise a dispute for that transaction
	RaiseDispute(ctx context.Context, in *RaiseDisputeRequest, opts ...grpc.CallOption) (*RaiseDisputeResponse, error)
	// RPC method to return parameters required for the add funds screen
	GetAddFundParams(ctx context.Context, in *GetAddFundParamsRequest, opts ...grpc.CallOption) (*GetAddFundParamsResponse, error)
	// AuthoriseFundTransfer will initiate fund transfer payment with auth. It will make required cred block(encrypted pin/secure pin)
	// of user, validate the authorise request and payment details. After that it will initiate payment with vendor.
	//
	// Order should be created for such payment.
	AuthoriseFundTransfer(ctx context.Context, in *AuthoriseFundTransferRequest, opts ...grpc.CallOption) (*AuthoriseFundTransferResponse, error)
	// RPC method to check whether we want to allow the user to add funds
	// we show them a warning if they are nearing the min kyc usage limit and also do not allow them to add funds if not applicable
	CheckIfAddFundsAllowed(ctx context.Context, in *CheckIfAddFundsAllowedRequest, opts ...grpc.CallOption) (*CheckIfAddFundsAllowedResponse, error)
	// GetEligibleAccountsForPaymentV1 calls the backend rpc to fetch all active accounts using actorId.
	// It fetches the active tpap accounts and also checks if it includes internal account
	// If internal account is not present in the list of active tpaps accounts, it adds the internal account in the given list.
	GetEligibleAccountsForPaymentV1(ctx context.Context, in *GetEligibleAccountsForPaymentV1Request, opts ...grpc.CallOption) (*GetEligibleAccountsForPaymentV1Response, error)
	// GetChatHeadsForPaymentViaNumber returns a list of chat heads for the given phone number
	// rpc checks internally if number belongs to an existing user
	// if yes then also returns that with other chat heads for vpas linked with npci
	GetChatHeadsForPaymentViaNumber(ctx context.Context, in *GetChatHeadsForPaymentViaNumberRequest, opts ...grpc.CallOption) (*GetChatHeadsForPaymentViaNumberResponse, error)
	// GetAddFundsScreenDetailsV2 returns add funds screen details based on different entry points
	// Takes ui entry point in request
	// Returns a one of in response which contains parameter details specific to the entry point
	GetAddFundsScreenDetailsV2(ctx context.Context, in *GetAddFundsScreenDetailsV2Request, opts ...grpc.CallOption) (*GetAddFundsScreenDetailsV2Response, error)
	// ManualBalanceRefreshForOnbAddFundsV2 to be used via user-action for manual balance update during onboarding add-funds
	// It returns success if the updated balance passes the minimum required amount for onb-add-funds.
	// Non-success response if the updated balance is less than the minimum required amount for onb-add-funds
	ManualBalanceRefreshForOnbAddFundsV2(ctx context.Context, in *ManualBalanceRefreshForOnbAddFundsV2Request, opts ...grpc.CallOption) (*ManualBalanceRefreshForOnbAddFundsV2Response, error)
	// GetPaymentOptions provides a mechanism to perform a payment by providing various methods/options for the payment.
	// It looks into the availability and applicability/possibility for using a payment option and generates the response accordingly.
	// todo: add more details regarding the sections, ordering etc
	GetPaymentOptions(ctx context.Context, in *GetPaymentOptionsRequest, opts ...grpc.CallOption) (*GetPaymentOptionsResponse, error)
	// CreateFundTransferOrderV1 RPC method is responsible to initiate order creation for the transactions
	//
	// Order is an important step of the payment life cycle and required to be created before a transaction can be processed.
	// An Order is a concept between client and Epifi and is typically abstracted from the partner banks or payment gateways. It helps to manage transaction requests and their workflow transitions from payment initiation to settlement.
	//
	// Payment details such as preferred payment protocol, payment instrument and other transaction
	// related info are typically determined at this step and passed back to the client.
	//
	// Additionally, RPC abstracts the order creation step at vendor's side, if required, such as when using payment gateways like Razorpay.
	// After execution, it provides the vendor_order_id in the response to satisfy the client's requirements for further actions.
	//
	// This information is to be used by client to
	// 1. To invoke `GetCredentials` in common library and `InitiatePayment` with the cred block which executes the transaction
	// 2. Invoke SDK or corresponding flow of payment gateway to execute a transaction
	CreateFundTransferOrderV1(ctx context.Context, in *CreateFundTransferOrderV1Request, opts ...grpc.CallOption) (*CreateFundTransferOrderV1Response, error)
	// MarkPostPayment RPC can be used by client to inform/update backend regarding the payment done by the user via
	// app SDKs. These payments can be the ones done via external vendor integration such as RazorPay where the payment
	// creation doesn't go via Fi backend servers.
	// The logic in here can include steps to be performed depending on the type of payment + vendor. For e.g.
	// 1. Signalling the backend workflows upon authorisation, i.e. payment for mandate setup.
	// 2. todo: add more if extend support tomorrow.
	//
	// Note: relying only client invocation to update backend servers regarding action performed by user is subject to
	// failure. Please make sure to have fallback logics which are backend driven to maintain consistency.
	MarkPostPayment(ctx context.Context, in *MarkPostPaymentRequest, opts ...grpc.CallOption) (*MarkPostPaymentResponse, error)
}

type transactionClient struct {
	cc grpc.ClientConnInterface
}

func NewTransactionClient(cc grpc.ClientConnInterface) TransactionClient {
	return &transactionClient{cc}
}

func (c *transactionClient) CreateFundTransferOrder(ctx context.Context, in *CreateFundTransferOrderRequest, opts ...grpc.CallOption) (*CreateFundTransferOrderResponse, error) {
	out := new(CreateFundTransferOrderResponse)
	err := c.cc.Invoke(ctx, Transaction_CreateFundTransferOrder_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) CreateP2PCollectOrder(ctx context.Context, in *CreateP2PCollectOrderRequest, opts ...grpc.CallOption) (*CreateP2PCollectOrderResponse, error) {
	out := new(CreateP2PCollectOrderResponse)
	err := c.cc.Invoke(ctx, Transaction_CreateP2PCollectOrder_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) InitiatePayment(ctx context.Context, in *InitiatePaymentRequest, opts ...grpc.CallOption) (*InitiatePaymentResponse, error) {
	out := new(InitiatePaymentResponse)
	err := c.cc.Invoke(ctx, Transaction_InitiatePayment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetOrderStatus(ctx context.Context, in *GetOrderStatusRequest, opts ...grpc.CallOption) (*GetOrderStatusResponse, error) {
	out := new(GetOrderStatusResponse)
	err := c.cc.Invoke(ctx, Transaction_GetOrderStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetCollectOrderTransactionDetails(ctx context.Context, in *GetCollectOrderTransactionDetailsRequest, opts ...grpc.CallOption) (*GetCollectOrderTransactionDetailsResponse, error) {
	out := new(GetCollectOrderTransactionDetailsResponse)
	err := c.cc.Invoke(ctx, Transaction_GetCollectOrderTransactionDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *transactionClient) GetEligibleAccountsForPayment(ctx context.Context, in *GetEligibleAccountsForPaymentRequest, opts ...grpc.CallOption) (*GetEligibleAccountsForPaymentResponse, error) {
	out := new(GetEligibleAccountsForPaymentResponse)
	err := c.cc.Invoke(ctx, Transaction_GetEligibleAccountsForPayment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) InitiateCollect(ctx context.Context, in *InitiateCollectRequest, opts ...grpc.CallOption) (*InitiateCollectResponse, error) {
	out := new(InitiateCollectResponse)
	err := c.cc.Invoke(ctx, Transaction_InitiateCollect_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) DismissCollect(ctx context.Context, in *DismissCollectRequest, opts ...grpc.CallOption) (*DismissCollectResponse, error) {
	out := new(DismissCollectResponse)
	err := c.cc.Invoke(ctx, Transaction_DismissCollect_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) CreateURNOrder(ctx context.Context, in *CreateURNOrderRequest, opts ...grpc.CallOption) (*CreateURNOrderResponse, error) {
	out := new(CreateURNOrderResponse)
	err := c.cc.Invoke(ctx, Transaction_CreateURNOrder_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetOrderReceipt(ctx context.Context, in *GetOrderReceiptRequest, opts ...grpc.CallOption) (*GetOrderReceiptResponse, error) {
	out := new(GetOrderReceiptResponse)
	err := c.cc.Invoke(ctx, Transaction_GetOrderReceipt_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *transactionClient) RaiseDispute(ctx context.Context, in *RaiseDisputeRequest, opts ...grpc.CallOption) (*RaiseDisputeResponse, error) {
	out := new(RaiseDisputeResponse)
	err := c.cc.Invoke(ctx, Transaction_RaiseDispute_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetAddFundParams(ctx context.Context, in *GetAddFundParamsRequest, opts ...grpc.CallOption) (*GetAddFundParamsResponse, error) {
	out := new(GetAddFundParamsResponse)
	err := c.cc.Invoke(ctx, Transaction_GetAddFundParams_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) AuthoriseFundTransfer(ctx context.Context, in *AuthoriseFundTransferRequest, opts ...grpc.CallOption) (*AuthoriseFundTransferResponse, error) {
	out := new(AuthoriseFundTransferResponse)
	err := c.cc.Invoke(ctx, Transaction_AuthoriseFundTransfer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) CheckIfAddFundsAllowed(ctx context.Context, in *CheckIfAddFundsAllowedRequest, opts ...grpc.CallOption) (*CheckIfAddFundsAllowedResponse, error) {
	out := new(CheckIfAddFundsAllowedResponse)
	err := c.cc.Invoke(ctx, Transaction_CheckIfAddFundsAllowed_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetEligibleAccountsForPaymentV1(ctx context.Context, in *GetEligibleAccountsForPaymentV1Request, opts ...grpc.CallOption) (*GetEligibleAccountsForPaymentV1Response, error) {
	out := new(GetEligibleAccountsForPaymentV1Response)
	err := c.cc.Invoke(ctx, Transaction_GetEligibleAccountsForPaymentV1_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetChatHeadsForPaymentViaNumber(ctx context.Context, in *GetChatHeadsForPaymentViaNumberRequest, opts ...grpc.CallOption) (*GetChatHeadsForPaymentViaNumberResponse, error) {
	out := new(GetChatHeadsForPaymentViaNumberResponse)
	err := c.cc.Invoke(ctx, Transaction_GetChatHeadsForPaymentViaNumber_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetAddFundsScreenDetailsV2(ctx context.Context, in *GetAddFundsScreenDetailsV2Request, opts ...grpc.CallOption) (*GetAddFundsScreenDetailsV2Response, error) {
	out := new(GetAddFundsScreenDetailsV2Response)
	err := c.cc.Invoke(ctx, Transaction_GetAddFundsScreenDetailsV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) ManualBalanceRefreshForOnbAddFundsV2(ctx context.Context, in *ManualBalanceRefreshForOnbAddFundsV2Request, opts ...grpc.CallOption) (*ManualBalanceRefreshForOnbAddFundsV2Response, error) {
	out := new(ManualBalanceRefreshForOnbAddFundsV2Response)
	err := c.cc.Invoke(ctx, Transaction_ManualBalanceRefreshForOnbAddFundsV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) GetPaymentOptions(ctx context.Context, in *GetPaymentOptionsRequest, opts ...grpc.CallOption) (*GetPaymentOptionsResponse, error) {
	out := new(GetPaymentOptionsResponse)
	err := c.cc.Invoke(ctx, Transaction_GetPaymentOptions_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) CreateFundTransferOrderV1(ctx context.Context, in *CreateFundTransferOrderV1Request, opts ...grpc.CallOption) (*CreateFundTransferOrderV1Response, error) {
	out := new(CreateFundTransferOrderV1Response)
	err := c.cc.Invoke(ctx, Transaction_CreateFundTransferOrderV1_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *transactionClient) MarkPostPayment(ctx context.Context, in *MarkPostPaymentRequest, opts ...grpc.CallOption) (*MarkPostPaymentResponse, error) {
	out := new(MarkPostPaymentResponse)
	err := c.cc.Invoke(ctx, Transaction_MarkPostPayment_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TransactionServer is the server API for Transaction service.
// All implementations should embed UnimplementedTransactionServer
// for forward compatibility
type TransactionServer interface {
	// RPC method to create an order with fund transfer workflow.
	//
	// An Order is a concept between client and Epifi and is abstracted
	// from the partner banks. It helps to manage transaction requests and their workflow.
	//
	// It returns decision engine's preferred payment protocol, payment instrument and other transaction
	// related info back to the Client.
	// This information will be used by client-
	// 1. To invoke `GetCredentials` in common library.
	// 2. To invoke `InitiatePayment` with the cred block which executes the transaction
	CreateFundTransferOrder(context.Context, *CreateFundTransferOrderRequest) (*CreateFundTransferOrderResponse, error)
	// RPC method to create an order for P2P Collect workflow.
	//
	// It returns order id which client can use as an identifier of the request.
	// This information will be used by client to invoke `InitiateCollect` which registers collect request.
	// The RPC only accepts collect request between two individuals.
	// Hence, Any Collect request to merchant VPA will be rejected
	CreateP2PCollectOrder(context.Context, *CreateP2PCollectOrderRequest) (*CreateP2PCollectOrderResponse, error)
	// RPC method to create transaction and initiate the payment.
	//
	// This is an asynchronous API such that, payment may not be completed immediately.
	// This API, hence, will respond with an Acknowledgement message immediately.
	//
	// Any updates on the payment has to be polled with `GetOrderStatus` RPC method
	InitiatePayment(context.Context, *InitiatePaymentRequest) (*InitiatePaymentResponse, error)
	// RPC facilitates enquiry of order status - identified by a unique `order_id`
	// This can also give the status of a recurring payment registration.
	GetOrderStatus(context.Context, *GetOrderStatusRequest) (*GetOrderStatusResponse, error)
	// RPC method to return necessary transaction related information for a given collect order.
	// UPI CL requires transaction parameters to capture the credentials
	// that are required to process the transaction
	//
	// This method is to be used by client to fetch transaction parameters
	// when user wishes to make a payment against a collect request.
	//
	// RPC returns a list of eligible accounts through which user can initiate the payment
	// It also returns parameters such as payment instrument, txn-id, merchant_ref_id
	// that are required to complete the transaction
	//
	// Client can use this information for the following:
	// 1. To invoke `GetCredentials` in common library.
	// 2. To invoke `InitiatePayment` with the cred block which executes the transaction
	GetCollectOrderTransactionDetails(context.Context, *GetCollectOrderTransactionDetailsRequest) (*GetCollectOrderTransactionDetailsResponse, error)
	// Deprecated: Do not use.
	// RPC to fetch the accounts eligible for a user to send and receive a payment
	// contains information related to eligible accounts for making a payment. Client can surface this information to the user while he/she is trying to make a payment.
	// Since this information will be static mostly client can choose to cache this information
	// This RPC has been deprecated and is merged with frontend.user.GetUserSessionDetails.
	GetEligibleAccountsForPayment(context.Context, *GetEligibleAccountsForPaymentRequest) (*GetEligibleAccountsForPaymentResponse, error)
	// RPC to initiate a collect request with the system.
	//
	// This is an asynchronous API. So, collect registration may not be completed immediately.
	// This API, hence, will respond with an Acknowledgement message immediately.
	//
	// Any updates on the collect status has to be polled with `GetOrderStatus` RPC method
	InitiateCollect(context.Context, *InitiateCollectRequest) (*InitiateCollectResponse, error)
	// RPC to dismiss a collect request
	// A client can use this when a user wants to dismiss a collect request. A user can dismiss a collect request in the following scenarios-
	//  1. On receiving a collect request from another actor be it internal or external.
	//  2. When a user sends a collect request to an internal epiFi user.
	//     NOTE: a collect order whose payment is in progress can't be dismissed
	DismissCollect(context.Context, *DismissCollectRequest) (*DismissCollectResponse, error)
	// RPC method to create an order for outgoing intents/ dynamic QR
	// eg. Adding funds to epifi account using 3rd party apps
	//
	// An Order is a concept between client and Epifi and is abstracted
	// from the partner banks. It helps to manage transaction requests and their workflow.
	//
	// returns the orderId and URN to the client
	// Client can use it to create dynamic QR or Intent to the 3rd party app based on user's preference,
	// Urn will contain parameters such as payeeVPA, amount, txnId, refId etc and will be signed by the PSP's key.
	// 3rd party app is expected to use txnId and refId from URN to initiate the payment with NPCI
	CreateURNOrder(context.Context, *CreateURNOrderRequest) (*CreateURNOrderResponse, error)
	// RPC method to fetch an order receipt
	// Client is expected to use this method in order to fetch granular details
	// regarding the state of a payment and display it to the user.
	//
	// It returns necessary information such as transaction id, RRN/UTR, etc. A
	// A user can refer to the information for various purposes, but not limited to:
	// 1. User can share this information as a proof of payment with the other actor.
	// 2. User can raise dispute against an suspicious transaction and claim refunds.
	GetOrderReceipt(context.Context, *GetOrderReceiptRequest) (*GetOrderReceiptResponse, error)
	// Deprecated: Do not use.
	// RPC method to raise dispute for a transaction
	// A client can use this to raise dispute for any transaction. Dispute can be raised for a transaction only after the
	// cooldown period for the transaction which is 24 hours for UPI transaction and 3 hours for NEFT/IMPS/IntraBank/RTGS
	// transaction. Dispute can be raised only if the transaction state is not expired or cancelled. Once dispute is raised
	// for a transaction client cannot re raise a dispute for that transaction
	RaiseDispute(context.Context, *RaiseDisputeRequest) (*RaiseDisputeResponse, error)
	// RPC method to return parameters required for the add funds screen
	GetAddFundParams(context.Context, *GetAddFundParamsRequest) (*GetAddFundParamsResponse, error)
	// AuthoriseFundTransfer will initiate fund transfer payment with auth. It will make required cred block(encrypted pin/secure pin)
	// of user, validate the authorise request and payment details. After that it will initiate payment with vendor.
	//
	// Order should be created for such payment.
	AuthoriseFundTransfer(context.Context, *AuthoriseFundTransferRequest) (*AuthoriseFundTransferResponse, error)
	// RPC method to check whether we want to allow the user to add funds
	// we show them a warning if they are nearing the min kyc usage limit and also do not allow them to add funds if not applicable
	CheckIfAddFundsAllowed(context.Context, *CheckIfAddFundsAllowedRequest) (*CheckIfAddFundsAllowedResponse, error)
	// GetEligibleAccountsForPaymentV1 calls the backend rpc to fetch all active accounts using actorId.
	// It fetches the active tpap accounts and also checks if it includes internal account
	// If internal account is not present in the list of active tpaps accounts, it adds the internal account in the given list.
	GetEligibleAccountsForPaymentV1(context.Context, *GetEligibleAccountsForPaymentV1Request) (*GetEligibleAccountsForPaymentV1Response, error)
	// GetChatHeadsForPaymentViaNumber returns a list of chat heads for the given phone number
	// rpc checks internally if number belongs to an existing user
	// if yes then also returns that with other chat heads for vpas linked with npci
	GetChatHeadsForPaymentViaNumber(context.Context, *GetChatHeadsForPaymentViaNumberRequest) (*GetChatHeadsForPaymentViaNumberResponse, error)
	// GetAddFundsScreenDetailsV2 returns add funds screen details based on different entry points
	// Takes ui entry point in request
	// Returns a one of in response which contains parameter details specific to the entry point
	GetAddFundsScreenDetailsV2(context.Context, *GetAddFundsScreenDetailsV2Request) (*GetAddFundsScreenDetailsV2Response, error)
	// ManualBalanceRefreshForOnbAddFundsV2 to be used via user-action for manual balance update during onboarding add-funds
	// It returns success if the updated balance passes the minimum required amount for onb-add-funds.
	// Non-success response if the updated balance is less than the minimum required amount for onb-add-funds
	ManualBalanceRefreshForOnbAddFundsV2(context.Context, *ManualBalanceRefreshForOnbAddFundsV2Request) (*ManualBalanceRefreshForOnbAddFundsV2Response, error)
	// GetPaymentOptions provides a mechanism to perform a payment by providing various methods/options for the payment.
	// It looks into the availability and applicability/possibility for using a payment option and generates the response accordingly.
	// todo: add more details regarding the sections, ordering etc
	GetPaymentOptions(context.Context, *GetPaymentOptionsRequest) (*GetPaymentOptionsResponse, error)
	// CreateFundTransferOrderV1 RPC method is responsible to initiate order creation for the transactions
	//
	// Order is an important step of the payment life cycle and required to be created before a transaction can be processed.
	// An Order is a concept between client and Epifi and is typically abstracted from the partner banks or payment gateways. It helps to manage transaction requests and their workflow transitions from payment initiation to settlement.
	//
	// Payment details such as preferred payment protocol, payment instrument and other transaction
	// related info are typically determined at this step and passed back to the client.
	//
	// Additionally, RPC abstracts the order creation step at vendor's side, if required, such as when using payment gateways like Razorpay.
	// After execution, it provides the vendor_order_id in the response to satisfy the client's requirements for further actions.
	//
	// This information is to be used by client to
	// 1. To invoke `GetCredentials` in common library and `InitiatePayment` with the cred block which executes the transaction
	// 2. Invoke SDK or corresponding flow of payment gateway to execute a transaction
	CreateFundTransferOrderV1(context.Context, *CreateFundTransferOrderV1Request) (*CreateFundTransferOrderV1Response, error)
	// MarkPostPayment RPC can be used by client to inform/update backend regarding the payment done by the user via
	// app SDKs. These payments can be the ones done via external vendor integration such as RazorPay where the payment
	// creation doesn't go via Fi backend servers.
	// The logic in here can include steps to be performed depending on the type of payment + vendor. For e.g.
	// 1. Signalling the backend workflows upon authorisation, i.e. payment for mandate setup.
	// 2. todo: add more if extend support tomorrow.
	//
	// Note: relying only client invocation to update backend servers regarding action performed by user is subject to
	// failure. Please make sure to have fallback logics which are backend driven to maintain consistency.
	MarkPostPayment(context.Context, *MarkPostPaymentRequest) (*MarkPostPaymentResponse, error)
}

// UnimplementedTransactionServer should be embedded to have forward compatible implementations.
type UnimplementedTransactionServer struct {
}

func (UnimplementedTransactionServer) CreateFundTransferOrder(context.Context, *CreateFundTransferOrderRequest) (*CreateFundTransferOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFundTransferOrder not implemented")
}
func (UnimplementedTransactionServer) CreateP2PCollectOrder(context.Context, *CreateP2PCollectOrderRequest) (*CreateP2PCollectOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateP2PCollectOrder not implemented")
}
func (UnimplementedTransactionServer) InitiatePayment(context.Context, *InitiatePaymentRequest) (*InitiatePaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiatePayment not implemented")
}
func (UnimplementedTransactionServer) GetOrderStatus(context.Context, *GetOrderStatusRequest) (*GetOrderStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderStatus not implemented")
}
func (UnimplementedTransactionServer) GetCollectOrderTransactionDetails(context.Context, *GetCollectOrderTransactionDetailsRequest) (*GetCollectOrderTransactionDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCollectOrderTransactionDetails not implemented")
}
func (UnimplementedTransactionServer) GetEligibleAccountsForPayment(context.Context, *GetEligibleAccountsForPaymentRequest) (*GetEligibleAccountsForPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEligibleAccountsForPayment not implemented")
}
func (UnimplementedTransactionServer) InitiateCollect(context.Context, *InitiateCollectRequest) (*InitiateCollectResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitiateCollect not implemented")
}
func (UnimplementedTransactionServer) DismissCollect(context.Context, *DismissCollectRequest) (*DismissCollectResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DismissCollect not implemented")
}
func (UnimplementedTransactionServer) CreateURNOrder(context.Context, *CreateURNOrderRequest) (*CreateURNOrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateURNOrder not implemented")
}
func (UnimplementedTransactionServer) GetOrderReceipt(context.Context, *GetOrderReceiptRequest) (*GetOrderReceiptResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOrderReceipt not implemented")
}
func (UnimplementedTransactionServer) RaiseDispute(context.Context, *RaiseDisputeRequest) (*RaiseDisputeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RaiseDispute not implemented")
}
func (UnimplementedTransactionServer) GetAddFundParams(context.Context, *GetAddFundParamsRequest) (*GetAddFundParamsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddFundParams not implemented")
}
func (UnimplementedTransactionServer) AuthoriseFundTransfer(context.Context, *AuthoriseFundTransferRequest) (*AuthoriseFundTransferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AuthoriseFundTransfer not implemented")
}
func (UnimplementedTransactionServer) CheckIfAddFundsAllowed(context.Context, *CheckIfAddFundsAllowedRequest) (*CheckIfAddFundsAllowedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckIfAddFundsAllowed not implemented")
}
func (UnimplementedTransactionServer) GetEligibleAccountsForPaymentV1(context.Context, *GetEligibleAccountsForPaymentV1Request) (*GetEligibleAccountsForPaymentV1Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEligibleAccountsForPaymentV1 not implemented")
}
func (UnimplementedTransactionServer) GetChatHeadsForPaymentViaNumber(context.Context, *GetChatHeadsForPaymentViaNumberRequest) (*GetChatHeadsForPaymentViaNumberResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChatHeadsForPaymentViaNumber not implemented")
}
func (UnimplementedTransactionServer) GetAddFundsScreenDetailsV2(context.Context, *GetAddFundsScreenDetailsV2Request) (*GetAddFundsScreenDetailsV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAddFundsScreenDetailsV2 not implemented")
}
func (UnimplementedTransactionServer) ManualBalanceRefreshForOnbAddFundsV2(context.Context, *ManualBalanceRefreshForOnbAddFundsV2Request) (*ManualBalanceRefreshForOnbAddFundsV2Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ManualBalanceRefreshForOnbAddFundsV2 not implemented")
}
func (UnimplementedTransactionServer) GetPaymentOptions(context.Context, *GetPaymentOptionsRequest) (*GetPaymentOptionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentOptions not implemented")
}
func (UnimplementedTransactionServer) CreateFundTransferOrderV1(context.Context, *CreateFundTransferOrderV1Request) (*CreateFundTransferOrderV1Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFundTransferOrderV1 not implemented")
}
func (UnimplementedTransactionServer) MarkPostPayment(context.Context, *MarkPostPaymentRequest) (*MarkPostPaymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkPostPayment not implemented")
}

// UnsafeTransactionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TransactionServer will
// result in compilation errors.
type UnsafeTransactionServer interface {
	mustEmbedUnimplementedTransactionServer()
}

func RegisterTransactionServer(s grpc.ServiceRegistrar, srv TransactionServer) {
	s.RegisterService(&Transaction_ServiceDesc, srv)
}

func _Transaction_CreateFundTransferOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFundTransferOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).CreateFundTransferOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_CreateFundTransferOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).CreateFundTransferOrder(ctx, req.(*CreateFundTransferOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_CreateP2PCollectOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateP2PCollectOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).CreateP2PCollectOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_CreateP2PCollectOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).CreateP2PCollectOrder(ctx, req.(*CreateP2PCollectOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_InitiatePayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiatePaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).InitiatePayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_InitiatePayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).InitiatePayment(ctx, req.(*InitiatePaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetOrderStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetOrderStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetOrderStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetOrderStatus(ctx, req.(*GetOrderStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetCollectOrderTransactionDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCollectOrderTransactionDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetCollectOrderTransactionDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetCollectOrderTransactionDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetCollectOrderTransactionDetails(ctx, req.(*GetCollectOrderTransactionDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetEligibleAccountsForPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEligibleAccountsForPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetEligibleAccountsForPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetEligibleAccountsForPayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetEligibleAccountsForPayment(ctx, req.(*GetEligibleAccountsForPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_InitiateCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitiateCollectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).InitiateCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_InitiateCollect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).InitiateCollect(ctx, req.(*InitiateCollectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_DismissCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DismissCollectRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).DismissCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_DismissCollect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).DismissCollect(ctx, req.(*DismissCollectRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_CreateURNOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateURNOrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).CreateURNOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_CreateURNOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).CreateURNOrder(ctx, req.(*CreateURNOrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetOrderReceipt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOrderReceiptRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetOrderReceipt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetOrderReceipt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetOrderReceipt(ctx, req.(*GetOrderReceiptRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_RaiseDispute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RaiseDisputeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).RaiseDispute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_RaiseDispute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).RaiseDispute(ctx, req.(*RaiseDisputeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetAddFundParams_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddFundParamsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetAddFundParams(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetAddFundParams_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetAddFundParams(ctx, req.(*GetAddFundParamsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_AuthoriseFundTransfer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AuthoriseFundTransferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).AuthoriseFundTransfer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_AuthoriseFundTransfer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).AuthoriseFundTransfer(ctx, req.(*AuthoriseFundTransferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_CheckIfAddFundsAllowed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckIfAddFundsAllowedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).CheckIfAddFundsAllowed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_CheckIfAddFundsAllowed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).CheckIfAddFundsAllowed(ctx, req.(*CheckIfAddFundsAllowedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetEligibleAccountsForPaymentV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEligibleAccountsForPaymentV1Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetEligibleAccountsForPaymentV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetEligibleAccountsForPaymentV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetEligibleAccountsForPaymentV1(ctx, req.(*GetEligibleAccountsForPaymentV1Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetChatHeadsForPaymentViaNumber_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChatHeadsForPaymentViaNumberRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetChatHeadsForPaymentViaNumber(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetChatHeadsForPaymentViaNumber_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetChatHeadsForPaymentViaNumber(ctx, req.(*GetChatHeadsForPaymentViaNumberRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetAddFundsScreenDetailsV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAddFundsScreenDetailsV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetAddFundsScreenDetailsV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetAddFundsScreenDetailsV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetAddFundsScreenDetailsV2(ctx, req.(*GetAddFundsScreenDetailsV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_ManualBalanceRefreshForOnbAddFundsV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ManualBalanceRefreshForOnbAddFundsV2Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).ManualBalanceRefreshForOnbAddFundsV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_ManualBalanceRefreshForOnbAddFundsV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).ManualBalanceRefreshForOnbAddFundsV2(ctx, req.(*ManualBalanceRefreshForOnbAddFundsV2Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_GetPaymentOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentOptionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).GetPaymentOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_GetPaymentOptions_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).GetPaymentOptions(ctx, req.(*GetPaymentOptionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_CreateFundTransferOrderV1_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFundTransferOrderV1Request)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).CreateFundTransferOrderV1(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_CreateFundTransferOrderV1_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).CreateFundTransferOrderV1(ctx, req.(*CreateFundTransferOrderV1Request))
	}
	return interceptor(ctx, in, info, handler)
}

func _Transaction_MarkPostPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkPostPaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TransactionServer).MarkPostPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Transaction_MarkPostPayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TransactionServer).MarkPostPayment(ctx, req.(*MarkPostPaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Transaction_ServiceDesc is the grpc.ServiceDesc for Transaction service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Transaction_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.pay.transaction.Transaction",
	HandlerType: (*TransactionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateFundTransferOrder",
			Handler:    _Transaction_CreateFundTransferOrder_Handler,
		},
		{
			MethodName: "CreateP2PCollectOrder",
			Handler:    _Transaction_CreateP2PCollectOrder_Handler,
		},
		{
			MethodName: "InitiatePayment",
			Handler:    _Transaction_InitiatePayment_Handler,
		},
		{
			MethodName: "GetOrderStatus",
			Handler:    _Transaction_GetOrderStatus_Handler,
		},
		{
			MethodName: "GetCollectOrderTransactionDetails",
			Handler:    _Transaction_GetCollectOrderTransactionDetails_Handler,
		},
		{
			MethodName: "GetEligibleAccountsForPayment",
			Handler:    _Transaction_GetEligibleAccountsForPayment_Handler,
		},
		{
			MethodName: "InitiateCollect",
			Handler:    _Transaction_InitiateCollect_Handler,
		},
		{
			MethodName: "DismissCollect",
			Handler:    _Transaction_DismissCollect_Handler,
		},
		{
			MethodName: "CreateURNOrder",
			Handler:    _Transaction_CreateURNOrder_Handler,
		},
		{
			MethodName: "GetOrderReceipt",
			Handler:    _Transaction_GetOrderReceipt_Handler,
		},
		{
			MethodName: "RaiseDispute",
			Handler:    _Transaction_RaiseDispute_Handler,
		},
		{
			MethodName: "GetAddFundParams",
			Handler:    _Transaction_GetAddFundParams_Handler,
		},
		{
			MethodName: "AuthoriseFundTransfer",
			Handler:    _Transaction_AuthoriseFundTransfer_Handler,
		},
		{
			MethodName: "CheckIfAddFundsAllowed",
			Handler:    _Transaction_CheckIfAddFundsAllowed_Handler,
		},
		{
			MethodName: "GetEligibleAccountsForPaymentV1",
			Handler:    _Transaction_GetEligibleAccountsForPaymentV1_Handler,
		},
		{
			MethodName: "GetChatHeadsForPaymentViaNumber",
			Handler:    _Transaction_GetChatHeadsForPaymentViaNumber_Handler,
		},
		{
			MethodName: "GetAddFundsScreenDetailsV2",
			Handler:    _Transaction_GetAddFundsScreenDetailsV2_Handler,
		},
		{
			MethodName: "ManualBalanceRefreshForOnbAddFundsV2",
			Handler:    _Transaction_ManualBalanceRefreshForOnbAddFundsV2_Handler,
		},
		{
			MethodName: "GetPaymentOptions",
			Handler:    _Transaction_GetPaymentOptions_Handler,
		},
		{
			MethodName: "CreateFundTransferOrderV1",
			Handler:    _Transaction_CreateFundTransferOrderV1_Handler,
		},
		{
			MethodName: "MarkPostPayment",
			Handler:    _Transaction_MarkPostPayment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/pay/transaction/service.proto",
}
