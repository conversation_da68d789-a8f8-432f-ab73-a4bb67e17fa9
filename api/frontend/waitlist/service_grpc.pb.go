// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/waitlist/service.proto

package waitlist

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Waitlist_AddToWaitlist_FullMethodName = "/frontend.waitlist.Waitlist/AddToWaitlist"
)

// WaitlistClient is the client API for Waitlist service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WaitlistClient interface {
	AddToWaitlist(ctx context.Context, in *AddToWaitlistRequest, opts ...grpc.CallOption) (*AddToWaitlistResponse, error)
}

type waitlistClient struct {
	cc grpc.ClientConnInterface
}

func NewWaitlistClient(cc grpc.ClientConnInterface) WaitlistClient {
	return &waitlistClient{cc}
}

func (c *waitlistClient) AddToWaitlist(ctx context.Context, in *AddToWaitlistRequest, opts ...grpc.CallOption) (*AddToWaitlistResponse, error) {
	out := new(AddToWaitlistResponse)
	err := c.cc.Invoke(ctx, Waitlist_AddToWaitlist_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WaitlistServer is the server API for Waitlist service.
// All implementations should embed UnimplementedWaitlistServer
// for forward compatibility
type WaitlistServer interface {
	AddToWaitlist(context.Context, *AddToWaitlistRequest) (*AddToWaitlistResponse, error)
}

// UnimplementedWaitlistServer should be embedded to have forward compatible implementations.
type UnimplementedWaitlistServer struct {
}

func (UnimplementedWaitlistServer) AddToWaitlist(context.Context, *AddToWaitlistRequest) (*AddToWaitlistResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddToWaitlist not implemented")
}

// UnsafeWaitlistServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WaitlistServer will
// result in compilation errors.
type UnsafeWaitlistServer interface {
	mustEmbedUnimplementedWaitlistServer()
}

func RegisterWaitlistServer(s grpc.ServiceRegistrar, srv WaitlistServer) {
	s.RegisterService(&Waitlist_ServiceDesc, srv)
}

func _Waitlist_AddToWaitlist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddToWaitlistRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WaitlistServer).AddToWaitlist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Waitlist_AddToWaitlist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WaitlistServer).AddToWaitlist(ctx, req.(*AddToWaitlistRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Waitlist_ServiceDesc is the grpc.ServiceDesc for Waitlist service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Waitlist_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.waitlist.Waitlist",
	HandlerType: (*WaitlistServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddToWaitlist",
			Handler:    _Waitlist_AddToWaitlist_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/waitlist/service.proto",
}
