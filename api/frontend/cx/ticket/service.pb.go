// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/cx/ticket/service.proto

package ticket

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSupportTicketsForAppRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// header is mandatory
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// list of ticket filters that needs to be applied
	TicketFilters *TicketFiltersForUser `protobuf:"bytes,2,opt,name=ticket_filters,json=ticketFilters,proto3" json:"ticket_filters,omitempty"`
	// max page size allowed is 20
	// if page size is not passed default page size of 10 will be used
	PageContextRequest *rpc.PageContextRequest `protobuf:"bytes,3,opt,name=page_context_request,json=pageContextRequest,proto3" json:"page_context_request,omitempty"`
}

func (x *GetSupportTicketsForAppRequest) Reset() {
	*x = GetSupportTicketsForAppRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketsForAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketsForAppRequest) ProtoMessage() {}

func (x *GetSupportTicketsForAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketsForAppRequest.ProtoReflect.Descriptor instead.
func (*GetSupportTicketsForAppRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_ticket_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetSupportTicketsForAppRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetSupportTicketsForAppRequest) GetTicketFilters() *TicketFiltersForUser {
	if x != nil {
		return x.TicketFilters
	}
	return nil
}

func (x *GetSupportTicketsForAppRequest) GetPageContextRequest() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContextRequest
	}
	return nil
}

type GetSupportTicketsForAppResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// list of tickets
	Tickets []*TicketDetailsForUser `protobuf:"bytes,2,rep,name=tickets,proto3" json:"tickets,omitempty"`
	// isLatestTicketDetailsStillUpdating flag is used to decide whether to inform user that the latest ticket details still being updated
	// This is decided based on list of mandatory params to be populated before a ticket can be shown
	// If this flag is set to TRUE, latest-ticket-still-updating card must be shown to user
	// clients are not using it anymore, hence deprecating it
	//
	// Deprecated: Marked as deprecated in api/frontend/cx/ticket/service.proto.
	IsLatestTicketDetailsStillUpdating common.BooleanEnum `protobuf:"varint,3,opt,name=isLatestTicketDetailsStillUpdating,proto3,enum=api.typesv2.common.BooleanEnum" json:"isLatestTicketDetailsStillUpdating,omitempty"`
	// pagination request field
	PageContextResponse *rpc.PageContextResponse `protobuf:"bytes,4,opt,name=page_context_response,json=pageContextResponse,proto3" json:"page_context_response,omitempty"`
}

func (x *GetSupportTicketsForAppResponse) Reset() {
	*x = GetSupportTicketsForAppResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketsForAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketsForAppResponse) ProtoMessage() {}

func (x *GetSupportTicketsForAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketsForAppResponse.ProtoReflect.Descriptor instead.
func (*GetSupportTicketsForAppResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_ticket_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetSupportTicketsForAppResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSupportTicketsForAppResponse) GetTickets() []*TicketDetailsForUser {
	if x != nil {
		return x.Tickets
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/cx/ticket/service.proto.
func (x *GetSupportTicketsForAppResponse) GetIsLatestTicketDetailsStillUpdating() common.BooleanEnum {
	if x != nil {
		return x.IsLatestTicketDetailsStillUpdating
	}
	return common.BooleanEnum(0)
}

func (x *GetSupportTicketsForAppResponse) GetPageContextResponse() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContextResponse
	}
	return nil
}

type GetSupportTicketByIdForAppRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// header is mandatory
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// id of the ticket whose details are required, mandatory
	TicketId int64 `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id,omitempty"`
}

func (x *GetSupportTicketByIdForAppRequest) Reset() {
	*x = GetSupportTicketByIdForAppRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketByIdForAppRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketByIdForAppRequest) ProtoMessage() {}

func (x *GetSupportTicketByIdForAppRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketByIdForAppRequest.ProtoReflect.Descriptor instead.
func (*GetSupportTicketByIdForAppRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_ticket_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetSupportTicketByIdForAppRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetSupportTicketByIdForAppRequest) GetTicketId() int64 {
	if x != nil {
		return x.TicketId
	}
	return 0
}

type GetSupportTicketByIdForAppResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// ticket with the given id
	Ticket *TicketDetailsForUser `protobuf:"bytes,2,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *GetSupportTicketByIdForAppResponse) Reset() {
	*x = GetSupportTicketByIdForAppResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSupportTicketByIdForAppResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSupportTicketByIdForAppResponse) ProtoMessage() {}

func (x *GetSupportTicketByIdForAppResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSupportTicketByIdForAppResponse.ProtoReflect.Descriptor instead.
func (*GetSupportTicketByIdForAppResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_ticket_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetSupportTicketByIdForAppResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSupportTicketByIdForAppResponse) GetTicket() *TicketDetailsForUser {
	if x != nil {
		return x.Ticket
	}
	return nil
}

type GetTicketBannerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// header is mandatory
	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// this will be used in future if we want to show resolved tickets using the same banner
	// as of now only open tickets are being displayed hence nothing is required in filter
	// response will contain latest ticket for the status provided in filter
	StatusFilter TicketStatusForUser `protobuf:"varint,2,opt,name=status_filter,json=statusFilter,proto3,enum=frontend.cx.ticket.TicketStatusForUser" json:"status_filter,omitempty"`
}

func (x *GetTicketBannerRequest) Reset() {
	*x = GetTicketBannerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketBannerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketBannerRequest) ProtoMessage() {}

func (x *GetTicketBannerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketBannerRequest.ProtoReflect.Descriptor instead.
func (*GetTicketBannerRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_ticket_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetTicketBannerRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetTicketBannerRequest) GetStatusFilter() TicketStatusForUser {
	if x != nil {
		return x.StatusFilter
	}
	return TicketStatusForUser_TICKET_STATUS_FOR_USER_UNSPECIFIED
}

type GetTicketBannerResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// state of the banner
	BannerType TicketBannerType `protobuf:"varint,2,opt,name=banner_type,json=bannerType,proto3,enum=frontend.cx.ticket.TicketBannerType" json:"banner_type,omitempty"`
	// banner_type will be determined based on state of the banner
	// we are using one-of here because based on type of banner we only want to show on info
	//
	// Types that are assignable to TicketBannerDetails:
	//
	//	*GetTicketBannerResponse_TicketDetails
	//	*GetTicketBannerResponse_MessageView
	TicketBannerDetails isGetTicketBannerResponse_TicketBannerDetails `protobuf_oneof:"ticket_banner_details"`
}

func (x *GetTicketBannerResponse) Reset() {
	*x = GetTicketBannerResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTicketBannerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTicketBannerResponse) ProtoMessage() {}

func (x *GetTicketBannerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTicketBannerResponse.ProtoReflect.Descriptor instead.
func (*GetTicketBannerResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_ticket_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetTicketBannerResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetTicketBannerResponse) GetBannerType() TicketBannerType {
	if x != nil {
		return x.BannerType
	}
	return TicketBannerType_TICKET_BANNER_TYPE_UNSPECIFIED
}

func (m *GetTicketBannerResponse) GetTicketBannerDetails() isGetTicketBannerResponse_TicketBannerDetails {
	if m != nil {
		return m.TicketBannerDetails
	}
	return nil
}

func (x *GetTicketBannerResponse) GetTicketDetails() *TicketDetailsForBanner {
	if x, ok := x.GetTicketBannerDetails().(*GetTicketBannerResponse_TicketDetails); ok {
		return x.TicketDetails
	}
	return nil
}

func (x *GetTicketBannerResponse) GetMessageView() *DisplayMessageForBanner {
	if x, ok := x.GetTicketBannerDetails().(*GetTicketBannerResponse_MessageView); ok {
		return x.MessageView
	}
	return nil
}

type isGetTicketBannerResponse_TicketBannerDetails interface {
	isGetTicketBannerResponse_TicketBannerDetails()
}

type GetTicketBannerResponse_TicketDetails struct {
	// display ticket inside banner
	TicketDetails *TicketDetailsForBanner `protobuf:"bytes,3,opt,name=ticket_details,json=ticketDetails,proto3,oneof"`
}

type GetTicketBannerResponse_MessageView struct {
	// display icon and message provided in banner
	MessageView *DisplayMessageForBanner `protobuf:"bytes,4,opt,name=message_view,json=messageView,proto3,oneof"`
}

func (*GetTicketBannerResponse_TicketDetails) isGetTicketBannerResponse_TicketBannerDetails() {}

func (*GetTicketBannerResponse_MessageView) isGetTicketBannerResponse_TicketBannerDetails() {}

type CollectCsatSurveyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *CollectCsatSurveyRequest) Reset() {
	*x = CollectCsatSurveyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectCsatSurveyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectCsatSurveyRequest) ProtoMessage() {}

func (x *CollectCsatSurveyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectCsatSurveyRequest.ProtoReflect.Descriptor instead.
func (*CollectCsatSurveyRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_ticket_service_proto_rawDescGZIP(), []int{6}
}

func (x *CollectCsatSurveyRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type CollectCsatSurveyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// screen to land the user on from where csat survey is collected
	TicketScreenDeeplink *deeplink.Deeplink `protobuf:"bytes,2,opt,name=ticket_screen_deeplink,json=ticketScreenDeeplink,proto3" json:"ticket_screen_deeplink,omitempty"`
}

func (x *CollectCsatSurveyResponse) Reset() {
	*x = CollectCsatSurveyResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CollectCsatSurveyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CollectCsatSurveyResponse) ProtoMessage() {}

func (x *CollectCsatSurveyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_ticket_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CollectCsatSurveyResponse.ProtoReflect.Descriptor instead.
func (*CollectCsatSurveyResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_ticket_service_proto_rawDescGZIP(), []int{7}
}

func (x *CollectCsatSurveyResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *CollectCsatSurveyResponse) GetTicketScreenDeeplink() *deeplink.Deeplink {
	if x != nil {
		return x.TicketScreenDeeplink
	}
	return nil
}

var File_api_frontend_cx_ticket_service_proto protoreflect.FileDescriptor

var file_api_frontend_cx_ticket_service_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63,
	0x78, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x78, 0x2f, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x78, 0x2f,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70,
	0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x62, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xee, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70,
	0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x4f, 0x0a, 0x0e, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0d, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x49, 0x0a, 0x14, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x12, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xea, 0x02, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70,
	0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x70,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73,
	0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x42, 0x0a, 0x07, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46,
	0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x07, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12,
	0x73, 0x0a, 0x22, 0x69, 0x73, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53, 0x74, 0x69, 0x6c, 0x6c, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x22, 0x69, 0x73, 0x4c, 0x61, 0x74, 0x65, 0x73, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x53, 0x74, 0x69, 0x6c, 0x6c, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x69, 0x6e, 0x67, 0x12, 0x4c, 0x0a, 0x15, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e,
	0x74, 0x65, 0x78, 0x74, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x13, 0x70,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x72, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x79, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x22, 0xa8, 0x01, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x53, 0x75,
	0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x79, 0x49, 0x64, 0x46,
	0x6f, 0x72, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x40, 0x0a, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x06, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x22, 0x98, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03,
	0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x4c,
	0x0a, 0x0d, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x0c,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x22, 0xe2, 0x02, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a,
	0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x45, 0x0a, 0x0b, 0x62, 0x61,
	0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x24, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x53, 0x0a, 0x0e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x46, 0x6f, 0x72, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0d, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x44,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x50, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x2e, 0x44, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x46, 0x6f, 0x72, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x48, 0x00, 0x52, 0x0b, 0x6d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x56, 0x69, 0x65, 0x77, 0x42, 0x17, 0x0a, 0x15, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x5f, 0x62, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0x4c, 0x0a, 0x18, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x73, 0x61, 0x74,
	0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22,
	0xb0, 0x01, 0x0a, 0x19, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x73, 0x61, 0x74, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x51, 0x0a, 0x16, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e,
	0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c,
	0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x14, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x32, 0xd3, 0x04, 0x0a, 0x06, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x98, 0x01,
	0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x12, 0x32, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x73,
	0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e,
	0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0xa1, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74,
	0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x79, 0x49,
	0x64, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x12, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x79, 0x49,
	0x64, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x42, 0x79, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x70, 0x70, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7,
	0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x80, 0x01, 0x0a,
	0x0f, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72,
	0x12, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42,
	0x61, 0x6e, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01,
	0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12,
	0x86, 0x01, 0x0a, 0x11, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74, 0x43, 0x73, 0x61, 0x74, 0x53,
	0x75, 0x72, 0x76, 0x65, 0x79, 0x12, 0x2c, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65,
	0x63, 0x74, 0x43, 0x73, 0x61, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x43, 0x6f, 0x6c, 0x6c, 0x65, 0x63, 0x74,
	0x43, 0x73, 0x61, 0x74, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e,
	0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d,
	0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x63, 0x78, 0x2e, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63,
	0x78, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_cx_ticket_service_proto_rawDescOnce sync.Once
	file_api_frontend_cx_ticket_service_proto_rawDescData = file_api_frontend_cx_ticket_service_proto_rawDesc
)

func file_api_frontend_cx_ticket_service_proto_rawDescGZIP() []byte {
	file_api_frontend_cx_ticket_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_cx_ticket_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_cx_ticket_service_proto_rawDescData)
	})
	return file_api_frontend_cx_ticket_service_proto_rawDescData
}

var file_api_frontend_cx_ticket_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_frontend_cx_ticket_service_proto_goTypes = []interface{}{
	(*GetSupportTicketsForAppRequest)(nil),     // 0: frontend.cx.ticket.GetSupportTicketsForAppRequest
	(*GetSupportTicketsForAppResponse)(nil),    // 1: frontend.cx.ticket.GetSupportTicketsForAppResponse
	(*GetSupportTicketByIdForAppRequest)(nil),  // 2: frontend.cx.ticket.GetSupportTicketByIdForAppRequest
	(*GetSupportTicketByIdForAppResponse)(nil), // 3: frontend.cx.ticket.GetSupportTicketByIdForAppResponse
	(*GetTicketBannerRequest)(nil),             // 4: frontend.cx.ticket.GetTicketBannerRequest
	(*GetTicketBannerResponse)(nil),            // 5: frontend.cx.ticket.GetTicketBannerResponse
	(*CollectCsatSurveyRequest)(nil),           // 6: frontend.cx.ticket.CollectCsatSurveyRequest
	(*CollectCsatSurveyResponse)(nil),          // 7: frontend.cx.ticket.CollectCsatSurveyResponse
	(*header.RequestHeader)(nil),               // 8: frontend.header.RequestHeader
	(*TicketFiltersForUser)(nil),               // 9: frontend.cx.ticket.TicketFiltersForUser
	(*rpc.PageContextRequest)(nil),             // 10: rpc.PageContextRequest
	(*header.ResponseHeader)(nil),              // 11: frontend.header.ResponseHeader
	(*TicketDetailsForUser)(nil),               // 12: frontend.cx.ticket.TicketDetailsForUser
	(common.BooleanEnum)(0),                    // 13: api.typesv2.common.BooleanEnum
	(*rpc.PageContextResponse)(nil),            // 14: rpc.PageContextResponse
	(TicketStatusForUser)(0),                   // 15: frontend.cx.ticket.TicketStatusForUser
	(TicketBannerType)(0),                      // 16: frontend.cx.ticket.TicketBannerType
	(*TicketDetailsForBanner)(nil),             // 17: frontend.cx.ticket.TicketDetailsForBanner
	(*DisplayMessageForBanner)(nil),            // 18: frontend.cx.ticket.DisplayMessageForBanner
	(*deeplink.Deeplink)(nil),                  // 19: frontend.deeplink.Deeplink
}
var file_api_frontend_cx_ticket_service_proto_depIdxs = []int32{
	8,  // 0: frontend.cx.ticket.GetSupportTicketsForAppRequest.req:type_name -> frontend.header.RequestHeader
	9,  // 1: frontend.cx.ticket.GetSupportTicketsForAppRequest.ticket_filters:type_name -> frontend.cx.ticket.TicketFiltersForUser
	10, // 2: frontend.cx.ticket.GetSupportTicketsForAppRequest.page_context_request:type_name -> rpc.PageContextRequest
	11, // 3: frontend.cx.ticket.GetSupportTicketsForAppResponse.resp_header:type_name -> frontend.header.ResponseHeader
	12, // 4: frontend.cx.ticket.GetSupportTicketsForAppResponse.tickets:type_name -> frontend.cx.ticket.TicketDetailsForUser
	13, // 5: frontend.cx.ticket.GetSupportTicketsForAppResponse.isLatestTicketDetailsStillUpdating:type_name -> api.typesv2.common.BooleanEnum
	14, // 6: frontend.cx.ticket.GetSupportTicketsForAppResponse.page_context_response:type_name -> rpc.PageContextResponse
	8,  // 7: frontend.cx.ticket.GetSupportTicketByIdForAppRequest.req:type_name -> frontend.header.RequestHeader
	11, // 8: frontend.cx.ticket.GetSupportTicketByIdForAppResponse.resp_header:type_name -> frontend.header.ResponseHeader
	12, // 9: frontend.cx.ticket.GetSupportTicketByIdForAppResponse.ticket:type_name -> frontend.cx.ticket.TicketDetailsForUser
	8,  // 10: frontend.cx.ticket.GetTicketBannerRequest.req:type_name -> frontend.header.RequestHeader
	15, // 11: frontend.cx.ticket.GetTicketBannerRequest.status_filter:type_name -> frontend.cx.ticket.TicketStatusForUser
	11, // 12: frontend.cx.ticket.GetTicketBannerResponse.resp_header:type_name -> frontend.header.ResponseHeader
	16, // 13: frontend.cx.ticket.GetTicketBannerResponse.banner_type:type_name -> frontend.cx.ticket.TicketBannerType
	17, // 14: frontend.cx.ticket.GetTicketBannerResponse.ticket_details:type_name -> frontend.cx.ticket.TicketDetailsForBanner
	18, // 15: frontend.cx.ticket.GetTicketBannerResponse.message_view:type_name -> frontend.cx.ticket.DisplayMessageForBanner
	8,  // 16: frontend.cx.ticket.CollectCsatSurveyRequest.req:type_name -> frontend.header.RequestHeader
	11, // 17: frontend.cx.ticket.CollectCsatSurveyResponse.resp_header:type_name -> frontend.header.ResponseHeader
	19, // 18: frontend.cx.ticket.CollectCsatSurveyResponse.ticket_screen_deeplink:type_name -> frontend.deeplink.Deeplink
	0,  // 19: frontend.cx.ticket.Ticket.GetSupportTicketsForApp:input_type -> frontend.cx.ticket.GetSupportTicketsForAppRequest
	2,  // 20: frontend.cx.ticket.Ticket.GetSupportTicketByIdForApp:input_type -> frontend.cx.ticket.GetSupportTicketByIdForAppRequest
	4,  // 21: frontend.cx.ticket.Ticket.GetTicketBanner:input_type -> frontend.cx.ticket.GetTicketBannerRequest
	6,  // 22: frontend.cx.ticket.Ticket.CollectCsatSurvey:input_type -> frontend.cx.ticket.CollectCsatSurveyRequest
	1,  // 23: frontend.cx.ticket.Ticket.GetSupportTicketsForApp:output_type -> frontend.cx.ticket.GetSupportTicketsForAppResponse
	3,  // 24: frontend.cx.ticket.Ticket.GetSupportTicketByIdForApp:output_type -> frontend.cx.ticket.GetSupportTicketByIdForAppResponse
	5,  // 25: frontend.cx.ticket.Ticket.GetTicketBanner:output_type -> frontend.cx.ticket.GetTicketBannerResponse
	7,  // 26: frontend.cx.ticket.Ticket.CollectCsatSurvey:output_type -> frontend.cx.ticket.CollectCsatSurveyResponse
	23, // [23:27] is the sub-list for method output_type
	19, // [19:23] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_api_frontend_cx_ticket_service_proto_init() }
func file_api_frontend_cx_ticket_service_proto_init() {
	if File_api_frontend_cx_ticket_service_proto != nil {
		return
	}
	file_api_frontend_cx_ticket_enums_proto_init()
	file_api_frontend_cx_ticket_ticket_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_cx_ticket_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketsForAppRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_ticket_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketsForAppResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_ticket_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketByIdForAppRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_ticket_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSupportTicketByIdForAppResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_ticket_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketBannerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_ticket_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTicketBannerResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_ticket_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectCsatSurveyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_ticket_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CollectCsatSurveyResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_cx_ticket_service_proto_msgTypes[5].OneofWrappers = []interface{}{
		(*GetTicketBannerResponse_TicketDetails)(nil),
		(*GetTicketBannerResponse_MessageView)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_cx_ticket_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_cx_ticket_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_cx_ticket_service_proto_depIdxs,
		MessageInfos:      file_api_frontend_cx_ticket_service_proto_msgTypes,
	}.Build()
	File_api_frontend_cx_ticket_service_proto = out.File
	file_api_frontend_cx_ticket_service_proto_rawDesc = nil
	file_api_frontend_cx_ticket_service_proto_goTypes = nil
	file_api_frontend_cx_ticket_service_proto_depIdxs = nil
}
