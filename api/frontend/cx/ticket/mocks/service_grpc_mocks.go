// Code generated by MockGen. DO NOT EDIT.
// Source: api/frontend/cx/ticket/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	ticket "github.com/epifi/gamma/api/frontend/cx/ticket"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockTicketClient is a mock of TicketClient interface.
type MockTicketClient struct {
	ctrl     *gomock.Controller
	recorder *MockTicketClientMockRecorder
}

// MockTicketClientMockRecorder is the mock recorder for MockTicketClient.
type MockTicketClientMockRecorder struct {
	mock *MockTicketClient
}

// NewMockTicketClient creates a new mock instance.
func NewMockTicketClient(ctrl *gomock.Controller) *MockTicketClient {
	mock := &MockTicketClient{ctrl: ctrl}
	mock.recorder = &MockTicketClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTicketClient) EXPECT() *MockTicketClientMockRecorder {
	return m.recorder
}

// CollectCsatSurvey mocks base method.
func (m *MockTicketClient) CollectCsatSurvey(ctx context.Context, in *ticket.CollectCsatSurveyRequest, opts ...grpc.CallOption) (*ticket.CollectCsatSurveyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CollectCsatSurvey", varargs...)
	ret0, _ := ret[0].(*ticket.CollectCsatSurveyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectCsatSurvey indicates an expected call of CollectCsatSurvey.
func (mr *MockTicketClientMockRecorder) CollectCsatSurvey(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectCsatSurvey", reflect.TypeOf((*MockTicketClient)(nil).CollectCsatSurvey), varargs...)
}

// GetSupportTicketByIdForApp mocks base method.
func (m *MockTicketClient) GetSupportTicketByIdForApp(ctx context.Context, in *ticket.GetSupportTicketByIdForAppRequest, opts ...grpc.CallOption) (*ticket.GetSupportTicketByIdForAppResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSupportTicketByIdForApp", varargs...)
	ret0, _ := ret[0].(*ticket.GetSupportTicketByIdForAppResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTicketByIdForApp indicates an expected call of GetSupportTicketByIdForApp.
func (mr *MockTicketClientMockRecorder) GetSupportTicketByIdForApp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTicketByIdForApp", reflect.TypeOf((*MockTicketClient)(nil).GetSupportTicketByIdForApp), varargs...)
}

// GetSupportTicketsForApp mocks base method.
func (m *MockTicketClient) GetSupportTicketsForApp(ctx context.Context, in *ticket.GetSupportTicketsForAppRequest, opts ...grpc.CallOption) (*ticket.GetSupportTicketsForAppResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSupportTicketsForApp", varargs...)
	ret0, _ := ret[0].(*ticket.GetSupportTicketsForAppResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTicketsForApp indicates an expected call of GetSupportTicketsForApp.
func (mr *MockTicketClientMockRecorder) GetSupportTicketsForApp(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTicketsForApp", reflect.TypeOf((*MockTicketClient)(nil).GetSupportTicketsForApp), varargs...)
}

// GetTicketBanner mocks base method.
func (m *MockTicketClient) GetTicketBanner(ctx context.Context, in *ticket.GetTicketBannerRequest, opts ...grpc.CallOption) (*ticket.GetTicketBannerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTicketBanner", varargs...)
	ret0, _ := ret[0].(*ticket.GetTicketBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketBanner indicates an expected call of GetTicketBanner.
func (mr *MockTicketClientMockRecorder) GetTicketBanner(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketBanner", reflect.TypeOf((*MockTicketClient)(nil).GetTicketBanner), varargs...)
}

// MockTicketServer is a mock of TicketServer interface.
type MockTicketServer struct {
	ctrl     *gomock.Controller
	recorder *MockTicketServerMockRecorder
}

// MockTicketServerMockRecorder is the mock recorder for MockTicketServer.
type MockTicketServerMockRecorder struct {
	mock *MockTicketServer
}

// NewMockTicketServer creates a new mock instance.
func NewMockTicketServer(ctrl *gomock.Controller) *MockTicketServer {
	mock := &MockTicketServer{ctrl: ctrl}
	mock.recorder = &MockTicketServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTicketServer) EXPECT() *MockTicketServerMockRecorder {
	return m.recorder
}

// CollectCsatSurvey mocks base method.
func (m *MockTicketServer) CollectCsatSurvey(arg0 context.Context, arg1 *ticket.CollectCsatSurveyRequest) (*ticket.CollectCsatSurveyResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CollectCsatSurvey", arg0, arg1)
	ret0, _ := ret[0].(*ticket.CollectCsatSurveyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CollectCsatSurvey indicates an expected call of CollectCsatSurvey.
func (mr *MockTicketServerMockRecorder) CollectCsatSurvey(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CollectCsatSurvey", reflect.TypeOf((*MockTicketServer)(nil).CollectCsatSurvey), arg0, arg1)
}

// GetSupportTicketByIdForApp mocks base method.
func (m *MockTicketServer) GetSupportTicketByIdForApp(arg0 context.Context, arg1 *ticket.GetSupportTicketByIdForAppRequest) (*ticket.GetSupportTicketByIdForAppResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSupportTicketByIdForApp", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetSupportTicketByIdForAppResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTicketByIdForApp indicates an expected call of GetSupportTicketByIdForApp.
func (mr *MockTicketServerMockRecorder) GetSupportTicketByIdForApp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTicketByIdForApp", reflect.TypeOf((*MockTicketServer)(nil).GetSupportTicketByIdForApp), arg0, arg1)
}

// GetSupportTicketsForApp mocks base method.
func (m *MockTicketServer) GetSupportTicketsForApp(arg0 context.Context, arg1 *ticket.GetSupportTicketsForAppRequest) (*ticket.GetSupportTicketsForAppResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSupportTicketsForApp", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetSupportTicketsForAppResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSupportTicketsForApp indicates an expected call of GetSupportTicketsForApp.
func (mr *MockTicketServerMockRecorder) GetSupportTicketsForApp(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSupportTicketsForApp", reflect.TypeOf((*MockTicketServer)(nil).GetSupportTicketsForApp), arg0, arg1)
}

// GetTicketBanner mocks base method.
func (m *MockTicketServer) GetTicketBanner(arg0 context.Context, arg1 *ticket.GetTicketBannerRequest) (*ticket.GetTicketBannerResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTicketBanner", arg0, arg1)
	ret0, _ := ret[0].(*ticket.GetTicketBannerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTicketBanner indicates an expected call of GetTicketBanner.
func (mr *MockTicketServerMockRecorder) GetTicketBanner(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTicketBanner", reflect.TypeOf((*MockTicketServer)(nil).GetTicketBanner), arg0, arg1)
}

// MockUnsafeTicketServer is a mock of UnsafeTicketServer interface.
type MockUnsafeTicketServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeTicketServerMockRecorder
}

// MockUnsafeTicketServerMockRecorder is the mock recorder for MockUnsafeTicketServer.
type MockUnsafeTicketServerMockRecorder struct {
	mock *MockUnsafeTicketServer
}

// NewMockUnsafeTicketServer creates a new mock instance.
func NewMockUnsafeTicketServer(ctrl *gomock.Controller) *MockUnsafeTicketServer {
	mock := &MockUnsafeTicketServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeTicketServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeTicketServer) EXPECT() *MockUnsafeTicketServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedTicketServer mocks base method.
func (m *MockUnsafeTicketServer) mustEmbedUnimplementedTicketServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedTicketServer")
}

// mustEmbedUnimplementedTicketServer indicates an expected call of mustEmbedUnimplementedTicketServer.
func (mr *MockUnsafeTicketServerMockRecorder) mustEmbedUnimplementedTicketServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedTicketServer", reflect.TypeOf((*MockUnsafeTicketServer)(nil).mustEmbedUnimplementedTicketServer))
}
