// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/frontend/cx/ticket/service.proto

package ticket

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Ticket_GetSupportTicketsForApp_FullMethodName    = "/frontend.cx.ticket.Ticket/GetSupportTicketsForApp"
	Ticket_GetSupportTicketByIdForApp_FullMethodName = "/frontend.cx.ticket.Ticket/GetSupportTicketByIdForApp"
	Ticket_GetTicketBanner_FullMethodName            = "/frontend.cx.ticket.Ticket/GetTicketBanner"
	Ticket_CollectCsatSurvey_FullMethodName          = "/frontend.cx.ticket.Ticket/CollectCsatSurvey"
)

// TicketClient is the client API for Ticket service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TicketClient interface {
	// rpc to retrieve support tickets from db to be shown to the users in app
	// request accepts actor id, ticket filters and page context request
	// response contains rpc status code, list of tickets and page context response
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	GetSupportTicketsForApp(ctx context.Context, in *GetSupportTicketsForAppRequest, opts ...grpc.CallOption) (*GetSupportTicketsForAppResponse, error)
	// rpc to retrieve support ticket with the given id from db to be shown to the users in app
	// request accepts ticket_id which is mandatory field
	// response contains rpc status code, ticket details
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	GetSupportTicketByIdForApp(ctx context.Context, in *GetSupportTicketByIdForAppRequest, opts ...grpc.CallOption) (*GetSupportTicketByIdForAppResponse, error)
	// GetTicketBanner RPC determines what details to display on open ticket banner of Help and Support page
	// response contains response header, state of the banner, and open ticket if any
	// Possible response Status Code (present in response header):
	// 1. OK for success
	// 2. InternalServerError for any unknown error
	// 3. InvalidArgument for invalid argument, passed by client
	GetTicketBanner(ctx context.Context, in *GetTicketBannerRequest, opts ...grpc.CallOption) (*GetTicketBannerResponse, error)
	CollectCsatSurvey(ctx context.Context, in *CollectCsatSurveyRequest, opts ...grpc.CallOption) (*CollectCsatSurveyResponse, error)
}

type ticketClient struct {
	cc grpc.ClientConnInterface
}

func NewTicketClient(cc grpc.ClientConnInterface) TicketClient {
	return &ticketClient{cc}
}

func (c *ticketClient) GetSupportTicketsForApp(ctx context.Context, in *GetSupportTicketsForAppRequest, opts ...grpc.CallOption) (*GetSupportTicketsForAppResponse, error) {
	out := new(GetSupportTicketsForAppResponse)
	err := c.cc.Invoke(ctx, Ticket_GetSupportTicketsForApp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetSupportTicketByIdForApp(ctx context.Context, in *GetSupportTicketByIdForAppRequest, opts ...grpc.CallOption) (*GetSupportTicketByIdForAppResponse, error) {
	out := new(GetSupportTicketByIdForAppResponse)
	err := c.cc.Invoke(ctx, Ticket_GetSupportTicketByIdForApp_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) GetTicketBanner(ctx context.Context, in *GetTicketBannerRequest, opts ...grpc.CallOption) (*GetTicketBannerResponse, error) {
	out := new(GetTicketBannerResponse)
	err := c.cc.Invoke(ctx, Ticket_GetTicketBanner_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketClient) CollectCsatSurvey(ctx context.Context, in *CollectCsatSurveyRequest, opts ...grpc.CallOption) (*CollectCsatSurveyResponse, error) {
	out := new(CollectCsatSurveyResponse)
	err := c.cc.Invoke(ctx, Ticket_CollectCsatSurvey_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TicketServer is the server API for Ticket service.
// All implementations should embed UnimplementedTicketServer
// for forward compatibility
type TicketServer interface {
	// rpc to retrieve support tickets from db to be shown to the users in app
	// request accepts actor id, ticket filters and page context request
	// response contains rpc status code, list of tickets and page context response
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	GetSupportTicketsForApp(context.Context, *GetSupportTicketsForAppRequest) (*GetSupportTicketsForAppResponse, error)
	// rpc to retrieve support ticket with the given id from db to be shown to the users in app
	// request accepts ticket_id which is mandatory field
	// response contains rpc status code, ticket details
	// OK if successful
	// InvalidArg if mandatory params are missing
	// NotFound if data not found
	// ISE for any other errors
	GetSupportTicketByIdForApp(context.Context, *GetSupportTicketByIdForAppRequest) (*GetSupportTicketByIdForAppResponse, error)
	// GetTicketBanner RPC determines what details to display on open ticket banner of Help and Support page
	// response contains response header, state of the banner, and open ticket if any
	// Possible response Status Code (present in response header):
	// 1. OK for success
	// 2. InternalServerError for any unknown error
	// 3. InvalidArgument for invalid argument, passed by client
	GetTicketBanner(context.Context, *GetTicketBannerRequest) (*GetTicketBannerResponse, error)
	CollectCsatSurvey(context.Context, *CollectCsatSurveyRequest) (*CollectCsatSurveyResponse, error)
}

// UnimplementedTicketServer should be embedded to have forward compatible implementations.
type UnimplementedTicketServer struct {
}

func (UnimplementedTicketServer) GetSupportTicketsForApp(context.Context, *GetSupportTicketsForAppRequest) (*GetSupportTicketsForAppResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportTicketsForApp not implemented")
}
func (UnimplementedTicketServer) GetSupportTicketByIdForApp(context.Context, *GetSupportTicketByIdForAppRequest) (*GetSupportTicketByIdForAppResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSupportTicketByIdForApp not implemented")
}
func (UnimplementedTicketServer) GetTicketBanner(context.Context, *GetTicketBannerRequest) (*GetTicketBannerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTicketBanner not implemented")
}
func (UnimplementedTicketServer) CollectCsatSurvey(context.Context, *CollectCsatSurveyRequest) (*CollectCsatSurveyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CollectCsatSurvey not implemented")
}

// UnsafeTicketServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TicketServer will
// result in compilation errors.
type UnsafeTicketServer interface {
	mustEmbedUnimplementedTicketServer()
}

func RegisterTicketServer(s grpc.ServiceRegistrar, srv TicketServer) {
	s.RegisterService(&Ticket_ServiceDesc, srv)
}

func _Ticket_GetSupportTicketsForApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSupportTicketsForAppRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetSupportTicketsForApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetSupportTicketsForApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetSupportTicketsForApp(ctx, req.(*GetSupportTicketsForAppRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetSupportTicketByIdForApp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSupportTicketByIdForAppRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetSupportTicketByIdForApp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetSupportTicketByIdForApp_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetSupportTicketByIdForApp(ctx, req.(*GetSupportTicketByIdForAppRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_GetTicketBanner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTicketBannerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).GetTicketBanner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_GetTicketBanner_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).GetTicketBanner(ctx, req.(*GetTicketBannerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Ticket_CollectCsatSurvey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CollectCsatSurveyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketServer).CollectCsatSurvey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Ticket_CollectCsatSurvey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketServer).CollectCsatSurvey(ctx, req.(*CollectCsatSurveyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Ticket_ServiceDesc is the grpc.ServiceDesc for Ticket service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Ticket_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "frontend.cx.ticket.Ticket",
	HandlerType: (*TicketServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSupportTicketsForApp",
			Handler:    _Ticket_GetSupportTicketsForApp_Handler,
		},
		{
			MethodName: "GetSupportTicketByIdForApp",
			Handler:    _Ticket_GetSupportTicketByIdForApp_Handler,
		},
		{
			MethodName: "GetTicketBanner",
			Handler:    _Ticket_GetTicketBanner_Handler,
		},
		{
			MethodName: "CollectCsatSurvey",
			Handler:    _Ticket_CollectCsatSurvey_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/frontend/cx/ticket/service.proto",
}
