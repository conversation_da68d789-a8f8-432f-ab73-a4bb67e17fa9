// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/cx/ticket/service.proto

package ticket

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)
)

// Validate checks the field values on GetSupportTicketsForAppRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSupportTicketsForAppRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketsForAppRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSupportTicketsForAppRequestMultiError, or nil if none found.
func (m *GetSupportTicketsForAppRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketsForAppRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForAppRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForAppRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForAppRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicketFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForAppRequestValidationError{
					field:  "TicketFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForAppRequestValidationError{
					field:  "TicketFilters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForAppRequestValidationError{
				field:  "TicketFilters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContextRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForAppRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForAppRequestValidationError{
					field:  "PageContextRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForAppRequestValidationError{
				field:  "PageContextRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSupportTicketsForAppRequestMultiError(errors)
	}

	return nil
}

// GetSupportTicketsForAppRequestMultiError is an error wrapping multiple
// validation errors returned by GetSupportTicketsForAppRequest.ValidateAll()
// if the designated constraints aren't met.
type GetSupportTicketsForAppRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketsForAppRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketsForAppRequestMultiError) AllErrors() []error { return m }

// GetSupportTicketsForAppRequestValidationError is the validation error
// returned by GetSupportTicketsForAppRequest.Validate if the designated
// constraints aren't met.
type GetSupportTicketsForAppRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketsForAppRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketsForAppRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketsForAppRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketsForAppRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketsForAppRequestValidationError) ErrorName() string {
	return "GetSupportTicketsForAppRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketsForAppRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketsForAppRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketsForAppRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketsForAppRequestValidationError{}

// Validate checks the field values on GetSupportTicketsForAppResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSupportTicketsForAppResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketsForAppResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetSupportTicketsForAppResponseMultiError, or nil if none found.
func (m *GetSupportTicketsForAppResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketsForAppResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForAppResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForAppResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForAppResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetTickets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSupportTicketsForAppResponseValidationError{
						field:  fmt.Sprintf("Tickets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSupportTicketsForAppResponseValidationError{
						field:  fmt.Sprintf("Tickets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSupportTicketsForAppResponseValidationError{
					field:  fmt.Sprintf("Tickets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsLatestTicketDetailsStillUpdating

	if all {
		switch v := interface{}(m.GetPageContextResponse()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketsForAppResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketsForAppResponseValidationError{
					field:  "PageContextResponse",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContextResponse()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketsForAppResponseValidationError{
				field:  "PageContextResponse",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSupportTicketsForAppResponseMultiError(errors)
	}

	return nil
}

// GetSupportTicketsForAppResponseMultiError is an error wrapping multiple
// validation errors returned by GetSupportTicketsForAppResponse.ValidateAll()
// if the designated constraints aren't met.
type GetSupportTicketsForAppResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketsForAppResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketsForAppResponseMultiError) AllErrors() []error { return m }

// GetSupportTicketsForAppResponseValidationError is the validation error
// returned by GetSupportTicketsForAppResponse.Validate if the designated
// constraints aren't met.
type GetSupportTicketsForAppResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketsForAppResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketsForAppResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketsForAppResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketsForAppResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketsForAppResponseValidationError) ErrorName() string {
	return "GetSupportTicketsForAppResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketsForAppResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketsForAppResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketsForAppResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketsForAppResponseValidationError{}

// Validate checks the field values on GetSupportTicketByIdForAppRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSupportTicketByIdForAppRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketByIdForAppRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSupportTicketByIdForAppRequestMultiError, or nil if none found.
func (m *GetSupportTicketByIdForAppRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketByIdForAppRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketByIdForAppRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketByIdForAppRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketByIdForAppRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for TicketId

	if len(errors) > 0 {
		return GetSupportTicketByIdForAppRequestMultiError(errors)
	}

	return nil
}

// GetSupportTicketByIdForAppRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetSupportTicketByIdForAppRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSupportTicketByIdForAppRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketByIdForAppRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketByIdForAppRequestMultiError) AllErrors() []error { return m }

// GetSupportTicketByIdForAppRequestValidationError is the validation error
// returned by GetSupportTicketByIdForAppRequest.Validate if the designated
// constraints aren't met.
type GetSupportTicketByIdForAppRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketByIdForAppRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketByIdForAppRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketByIdForAppRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketByIdForAppRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketByIdForAppRequestValidationError) ErrorName() string {
	return "GetSupportTicketByIdForAppRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketByIdForAppRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketByIdForAppRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketByIdForAppRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketByIdForAppRequestValidationError{}

// Validate checks the field values on GetSupportTicketByIdForAppResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetSupportTicketByIdForAppResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSupportTicketByIdForAppResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetSupportTicketByIdForAppResponseMultiError, or nil if none found.
func (m *GetSupportTicketByIdForAppResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSupportTicketByIdForAppResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketByIdForAppResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketByIdForAppResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketByIdForAppResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSupportTicketByIdForAppResponseValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSupportTicketByIdForAppResponseValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSupportTicketByIdForAppResponseValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSupportTicketByIdForAppResponseMultiError(errors)
	}

	return nil
}

// GetSupportTicketByIdForAppResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetSupportTicketByIdForAppResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSupportTicketByIdForAppResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSupportTicketByIdForAppResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSupportTicketByIdForAppResponseMultiError) AllErrors() []error { return m }

// GetSupportTicketByIdForAppResponseValidationError is the validation error
// returned by GetSupportTicketByIdForAppResponse.Validate if the designated
// constraints aren't met.
type GetSupportTicketByIdForAppResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSupportTicketByIdForAppResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSupportTicketByIdForAppResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSupportTicketByIdForAppResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSupportTicketByIdForAppResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSupportTicketByIdForAppResponseValidationError) ErrorName() string {
	return "GetSupportTicketByIdForAppResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSupportTicketByIdForAppResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSupportTicketByIdForAppResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSupportTicketByIdForAppResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSupportTicketByIdForAppResponseValidationError{}

// Validate checks the field values on GetTicketBannerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTicketBannerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketBannerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTicketBannerRequestMultiError, or nil if none found.
func (m *GetTicketBannerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketBannerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketBannerRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketBannerRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketBannerRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for StatusFilter

	if len(errors) > 0 {
		return GetTicketBannerRequestMultiError(errors)
	}

	return nil
}

// GetTicketBannerRequestMultiError is an error wrapping multiple validation
// errors returned by GetTicketBannerRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTicketBannerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketBannerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketBannerRequestMultiError) AllErrors() []error { return m }

// GetTicketBannerRequestValidationError is the validation error returned by
// GetTicketBannerRequest.Validate if the designated constraints aren't met.
type GetTicketBannerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketBannerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketBannerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketBannerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketBannerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketBannerRequestValidationError) ErrorName() string {
	return "GetTicketBannerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketBannerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketBannerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketBannerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketBannerRequestValidationError{}

// Validate checks the field values on GetTicketBannerResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTicketBannerResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTicketBannerResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTicketBannerResponseMultiError, or nil if none found.
func (m *GetTicketBannerResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTicketBannerResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetTicketBannerResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetTicketBannerResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetTicketBannerResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BannerType

	switch v := m.TicketBannerDetails.(type) {
	case *GetTicketBannerResponse_TicketDetails:
		if v == nil {
			err := GetTicketBannerResponseValidationError{
				field:  "TicketBannerDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetTicketDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTicketBannerResponseValidationError{
						field:  "TicketDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTicketBannerResponseValidationError{
						field:  "TicketDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetTicketDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTicketBannerResponseValidationError{
					field:  "TicketDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *GetTicketBannerResponse_MessageView:
		if v == nil {
			err := GetTicketBannerResponseValidationError{
				field:  "TicketBannerDetails",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMessageView()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetTicketBannerResponseValidationError{
						field:  "MessageView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetTicketBannerResponseValidationError{
						field:  "MessageView",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMessageView()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetTicketBannerResponseValidationError{
					field:  "MessageView",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return GetTicketBannerResponseMultiError(errors)
	}

	return nil
}

// GetTicketBannerResponseMultiError is an error wrapping multiple validation
// errors returned by GetTicketBannerResponse.ValidateAll() if the designated
// constraints aren't met.
type GetTicketBannerResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTicketBannerResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTicketBannerResponseMultiError) AllErrors() []error { return m }

// GetTicketBannerResponseValidationError is the validation error returned by
// GetTicketBannerResponse.Validate if the designated constraints aren't met.
type GetTicketBannerResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTicketBannerResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTicketBannerResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTicketBannerResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTicketBannerResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTicketBannerResponseValidationError) ErrorName() string {
	return "GetTicketBannerResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetTicketBannerResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTicketBannerResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTicketBannerResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTicketBannerResponseValidationError{}

// Validate checks the field values on CollectCsatSurveyRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectCsatSurveyRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectCsatSurveyRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectCsatSurveyRequestMultiError, or nil if none found.
func (m *CollectCsatSurveyRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectCsatSurveyRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectCsatSurveyRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectCsatSurveyRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectCsatSurveyRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollectCsatSurveyRequestMultiError(errors)
	}

	return nil
}

// CollectCsatSurveyRequestMultiError is an error wrapping multiple validation
// errors returned by CollectCsatSurveyRequest.ValidateAll() if the designated
// constraints aren't met.
type CollectCsatSurveyRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectCsatSurveyRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectCsatSurveyRequestMultiError) AllErrors() []error { return m }

// CollectCsatSurveyRequestValidationError is the validation error returned by
// CollectCsatSurveyRequest.Validate if the designated constraints aren't met.
type CollectCsatSurveyRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectCsatSurveyRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectCsatSurveyRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectCsatSurveyRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectCsatSurveyRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectCsatSurveyRequestValidationError) ErrorName() string {
	return "CollectCsatSurveyRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CollectCsatSurveyRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectCsatSurveyRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectCsatSurveyRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectCsatSurveyRequestValidationError{}

// Validate checks the field values on CollectCsatSurveyResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CollectCsatSurveyResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CollectCsatSurveyResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CollectCsatSurveyResponseMultiError, or nil if none found.
func (m *CollectCsatSurveyResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CollectCsatSurveyResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectCsatSurveyResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectCsatSurveyResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectCsatSurveyResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicketScreenDeeplink()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CollectCsatSurveyResponseValidationError{
					field:  "TicketScreenDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CollectCsatSurveyResponseValidationError{
					field:  "TicketScreenDeeplink",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicketScreenDeeplink()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CollectCsatSurveyResponseValidationError{
				field:  "TicketScreenDeeplink",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CollectCsatSurveyResponseMultiError(errors)
	}

	return nil
}

// CollectCsatSurveyResponseMultiError is an error wrapping multiple validation
// errors returned by CollectCsatSurveyResponse.ValidateAll() if the
// designated constraints aren't met.
type CollectCsatSurveyResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CollectCsatSurveyResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CollectCsatSurveyResponseMultiError) AllErrors() []error { return m }

// CollectCsatSurveyResponseValidationError is the validation error returned by
// CollectCsatSurveyResponse.Validate if the designated constraints aren't met.
type CollectCsatSurveyResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CollectCsatSurveyResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CollectCsatSurveyResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CollectCsatSurveyResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CollectCsatSurveyResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CollectCsatSurveyResponseValidationError) ErrorName() string {
	return "CollectCsatSurveyResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CollectCsatSurveyResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCollectCsatSurveyResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CollectCsatSurveyResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CollectCsatSurveyResponseValidationError{}
