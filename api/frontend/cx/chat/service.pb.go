// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/cx/chat/service.proto

package chat

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	header "github.com/epifi/gamma/api/frontend/header"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// SenseforthChatInitInformation provides information
// to initialize senseforth webview on client side
type SenseforthChatInitInformation struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// url which client has to load for chatbot webview
	WebViewUrl string `protobuf:"bytes,1,opt,name=web_view_url,json=webViewUrl,proto3" json:"web_view_url,omitempty"`
	// short token which client need to pass to senseforth
	ShortToken string `protobuf:"bytes,2,opt,name=short_token,json=shortToken,proto3" json:"short_token,omitempty"`
	// If this flag is set to TRUE , the client should reuse the short token & url stored in the cache
	ReuseCacheData common.BooleanEnum `protobuf:"varint,3,opt,name=reuse_cache_data,json=reuseCacheData,proto3,enum=api.typesv2.common.BooleanEnum" json:"reuse_cache_data,omitempty"`
	// The context to invoke the bot. Ref: https://docs.google.com/document/d/15A9XIZRPD0omXeBTIm36WXU3GuQU46zt32zagqB2wrs/edit
	// This context will be used by client only if the deeplink(which triggered this RPC) doesn't contain context_code
	BotContextCode string `protobuf:"bytes,4,opt,name=bot_context_code,json=botContextCode,proto3" json:"bot_context_code,omitempty"`
}

func (x *SenseforthChatInitInformation) Reset() {
	*x = SenseforthChatInitInformation{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_chat_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SenseforthChatInitInformation) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SenseforthChatInitInformation) ProtoMessage() {}

func (x *SenseforthChatInitInformation) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_chat_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SenseforthChatInitInformation.ProtoReflect.Descriptor instead.
func (*SenseforthChatInitInformation) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_chat_service_proto_rawDescGZIP(), []int{0}
}

func (x *SenseforthChatInitInformation) GetWebViewUrl() string {
	if x != nil {
		return x.WebViewUrl
	}
	return ""
}

func (x *SenseforthChatInitInformation) GetShortToken() string {
	if x != nil {
		return x.ShortToken
	}
	return ""
}

func (x *SenseforthChatInitInformation) GetReuseCacheData() common.BooleanEnum {
	if x != nil {
		return x.ReuseCacheData
	}
	return common.BooleanEnum(0)
}

func (x *SenseforthChatInitInformation) GetBotContextCode() string {
	if x != nil {
		return x.BotContextCode
	}
	return ""
}

type GetReferenceIdForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// has actor information
	//
	// Deprecated: Marked as deprecated in api/frontend/cx/chat/service.proto.
	Auth *header.AuthHeader    `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	Req  *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetReferenceIdForActorRequest) Reset() {
	*x = GetReferenceIdForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_chat_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReferenceIdForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReferenceIdForActorRequest) ProtoMessage() {}

func (x *GetReferenceIdForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_chat_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReferenceIdForActorRequest.ProtoReflect.Descriptor instead.
func (*GetReferenceIdForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_chat_service_proto_rawDescGZIP(), []int{1}
}

// Deprecated: Marked as deprecated in api/frontend/cx/chat/service.proto.
func (x *GetReferenceIdForActorRequest) GetAuth() *header.AuthHeader {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *GetReferenceIdForActorRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetReferenceIdForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ActorId     string      `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	ReferenceId string      `protobuf:"bytes,3,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// App id of the freshchat mobile sdk
	AppId string `protobuf:"bytes,4,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// App key of the freshchat mobile sdk
	AppKey string `protobuf:"bytes,5,opt,name=app_key,json=appKey,proto3" json:"app_key,omitempty"`
	// Domain name of freshchat mobile sdk
	Domain     string                 `protobuf:"bytes,6,opt,name=domain,proto3" json:"domain,omitempty"`
	RespHeader *header.ResponseHeader `protobuf:"bytes,15,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
}

func (x *GetReferenceIdForActorResponse) Reset() {
	*x = GetReferenceIdForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_chat_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetReferenceIdForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetReferenceIdForActorResponse) ProtoMessage() {}

func (x *GetReferenceIdForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_chat_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetReferenceIdForActorResponse.ProtoReflect.Descriptor instead.
func (*GetReferenceIdForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_chat_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetReferenceIdForActorResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetReferenceIdForActorResponse) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *GetReferenceIdForActorResponse) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *GetReferenceIdForActorResponse) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GetReferenceIdForActorResponse) GetAppKey() string {
	if x != nil {
		return x.AppKey
	}
	return ""
}

func (x *GetReferenceIdForActorResponse) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *GetReferenceIdForActorResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

// failure object to specify failure info from client side in chat view
type ClientSideChatFailureInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// chat view which client was trying to load but failed
	LastTriedChatView typesv2.InAppChatViewType `protobuf:"varint,1,opt,name=last_tried_chat_view,json=lastTriedChatView,proto3,enum=api.typesv2.InAppChatViewType" json:"last_tried_chat_view,omitempty"`
	// count of client side failures like failed to load web view
	FailureCount int64 `protobuf:"varint,2,opt,name=failure_count,json=failureCount,proto3" json:"failure_count,omitempty"`
	// error reason at client eg: sent by the senseforth webview to the app client
	FailureReason string `protobuf:"bytes,3,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *ClientSideChatFailureInfo) Reset() {
	*x = ClientSideChatFailureInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_chat_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientSideChatFailureInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientSideChatFailureInfo) ProtoMessage() {}

func (x *ClientSideChatFailureInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_chat_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientSideChatFailureInfo.ProtoReflect.Descriptor instead.
func (*ClientSideChatFailureInfo) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_chat_service_proto_rawDescGZIP(), []int{3}
}

func (x *ClientSideChatFailureInfo) GetLastTriedChatView() typesv2.InAppChatViewType {
	if x != nil {
		return x.LastTriedChatView
	}
	return typesv2.InAppChatViewType(0)
}

func (x *ClientSideChatFailureInfo) GetFailureCount() int64 {
	if x != nil {
		return x.FailureCount
	}
	return 0
}

func (x *ClientSideChatFailureInfo) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type GetChatInitInformationForActorRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// has actor information
	Req *header.RequestHeader `protobuf:"bytes,15,opt,name=req,proto3" json:"req,omitempty"`
	// chat view which client loaded in last session
	LastSuccessfullyLoadedChatView typesv2.InAppChatViewType `protobuf:"varint,2,opt,name=last_successfully_loaded_chat_view,json=lastSuccessfullyLoadedChatView,proto3,enum=api.typesv2.InAppChatViewType" json:"last_successfully_loaded_chat_view,omitempty"`
	// time at which user last interacted with fi chat option
	LastSuccessfulSessionTime *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=last_successful_session_time,json=lastSuccessfulSessionTime,proto3" json:"last_successful_session_time,omitempty"`
	// client side failure information
	ClientSideChatFailureInfo *ClientSideChatFailureInfo `protobuf:"bytes,4,opt,name=client_side_chat_failure_info,json=clientSideChatFailureInfo,proto3" json:"client_side_chat_failure_info,omitempty"`
	// client will use this if the user explicitly wants to start a new session
	// if this flag is set a new short token would be generated for the Senseforth webview
	// This flag is ignored in case the chat view to be loaded is freshchat
	ForceNewSession common.BooleanEnum `protobuf:"varint,5,opt,name=force_new_session,json=forceNewSession,proto3,enum=api.typesv2.common.BooleanEnum" json:"force_new_session,omitempty"`
	// client can pass a context code to load a particular starting menu on senseforth chatbot
	// the context code setup needs to be done on the a.ware platform
	SenseforthBotContextCode string `protobuf:"bytes,6,opt,name=senseforth_bot_context_code,json=senseforthBotContextCode,proto3" json:"senseforth_bot_context_code,omitempty"`
	// metadata stores information required for chat init
	Metadata map[string]string `protobuf:"bytes,7,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// screen-level metadata stores information related to screen required for chat init
	ScreenMetadata map[string]string `protobuf:"bytes,8,rep,name=screen_metadata,json=screenMetadata,proto3" json:"screen_metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetChatInitInformationForActorRequest) Reset() {
	*x = GetChatInitInformationForActorRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_chat_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChatInitInformationForActorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatInitInformationForActorRequest) ProtoMessage() {}

func (x *GetChatInitInformationForActorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_chat_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatInitInformationForActorRequest.ProtoReflect.Descriptor instead.
func (*GetChatInitInformationForActorRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_chat_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetChatInitInformationForActorRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetChatInitInformationForActorRequest) GetLastSuccessfullyLoadedChatView() typesv2.InAppChatViewType {
	if x != nil {
		return x.LastSuccessfullyLoadedChatView
	}
	return typesv2.InAppChatViewType(0)
}

func (x *GetChatInitInformationForActorRequest) GetLastSuccessfulSessionTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastSuccessfulSessionTime
	}
	return nil
}

func (x *GetChatInitInformationForActorRequest) GetClientSideChatFailureInfo() *ClientSideChatFailureInfo {
	if x != nil {
		return x.ClientSideChatFailureInfo
	}
	return nil
}

func (x *GetChatInitInformationForActorRequest) GetForceNewSession() common.BooleanEnum {
	if x != nil {
		return x.ForceNewSession
	}
	return common.BooleanEnum(0)
}

func (x *GetChatInitInformationForActorRequest) GetSenseforthBotContextCode() string {
	if x != nil {
		return x.SenseforthBotContextCode
	}
	return ""
}

func (x *GetChatInitInformationForActorRequest) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *GetChatInitInformationForActorRequest) GetScreenMetadata() map[string]string {
	if x != nil {
		return x.ScreenMetadata
	}
	return nil
}

type GetChatInitInformationForActorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// mapped freshdesk_id received from vendor-mapping service for current actor
	ReferenceId string `protobuf:"bytes,2,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// App id of the freshchat mobile sdk
	AppId string `protobuf:"bytes,3,opt,name=app_id,json=appId,proto3" json:"app_id,omitempty"`
	// App key of the freshchat mobile sdk
	AppKey string `protobuf:"bytes,4,opt,name=app_key,json=appKey,proto3" json:"app_key,omitempty"`
	// Domain name of freshchat mobile sdk
	Domain string `protobuf:"bytes,5,opt,name=domain,proto3" json:"domain,omitempty"`
	// Custom user properties to be passed to freschat via android/ios client
	CustomUserProperties map[string]string `protobuf:"bytes,6,rep,name=custom_user_properties,json=customUserProperties,proto3" json:"custom_user_properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// backend generated email to be passed by mobile client to Freshchat sdk
	// it is not part of custom property because client sdk explicitly expects
	// email to passed separately as an identifier
	Email string `protobuf:"bytes,7,opt,name=email,proto3" json:"email,omitempty"`
	// enum specifying which chat view has to be loaded on client side
	ChatViewToBeLoaded typesv2.InAppChatViewType `protobuf:"varint,8,opt,name=chat_view_to_be_loaded,json=chatViewToBeLoaded,proto3,enum=api.typesv2.InAppChatViewType" json:"chat_view_to_be_loaded,omitempty"`
	// info require to use senseforth webview on client side
	SenseforthChatInitInformation *SenseforthChatInitInformation `protobuf:"bytes,9,opt,name=senseforth_chat_init_information,json=senseforthChatInitInformation,proto3" json:"senseforth_chat_init_information,omitempty"`
	// client should auto retry in case of failures if this flag is set to true
	ShouldAutoRetry common.BooleanEnum `protobuf:"varint,10,opt,name=should_auto_retry,json=shouldAutoRetry,proto3,enum=api.typesv2.common.BooleanEnum" json:"should_auto_retry,omitempty"`
	// To filter and display only Topics tagged with a specific term, use the filterByTags API in ConversationOptions instance passed to showConversations() API as below.
	// Eg: To link and display only specific Topics from say orders page in your app, those specific Topics can be tagged with the term "order_queries".
	TopicTags []string `protobuf:"bytes,11,rep,name=topic_tags,json=topicTags,proto3" json:"topic_tags,omitempty"`
	// All chatbot init information specific to SDK is to be populated in this field
	// TODO(sayan): once migration is done, deprecate all senseforth and freshchat specific fields
	ChatbotInitInformation *typesv2.ChatbotInitInformation `protobuf:"bytes,12,opt,name=chatbot_init_information,json=chatbotInitInformation,proto3" json:"chatbot_init_information,omitempty"`
}

func (x *GetChatInitInformationForActorResponse) Reset() {
	*x = GetChatInitInformationForActorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_chat_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetChatInitInformationForActorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChatInitInformationForActorResponse) ProtoMessage() {}

func (x *GetChatInitInformationForActorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_chat_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChatInitInformationForActorResponse.ProtoReflect.Descriptor instead.
func (*GetChatInitInformationForActorResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_chat_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetChatInitInformationForActorResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetChatInitInformationForActorResponse) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *GetChatInitInformationForActorResponse) GetAppId() string {
	if x != nil {
		return x.AppId
	}
	return ""
}

func (x *GetChatInitInformationForActorResponse) GetAppKey() string {
	if x != nil {
		return x.AppKey
	}
	return ""
}

func (x *GetChatInitInformationForActorResponse) GetDomain() string {
	if x != nil {
		return x.Domain
	}
	return ""
}

func (x *GetChatInitInformationForActorResponse) GetCustomUserProperties() map[string]string {
	if x != nil {
		return x.CustomUserProperties
	}
	return nil
}

func (x *GetChatInitInformationForActorResponse) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *GetChatInitInformationForActorResponse) GetChatViewToBeLoaded() typesv2.InAppChatViewType {
	if x != nil {
		return x.ChatViewToBeLoaded
	}
	return typesv2.InAppChatViewType(0)
}

func (x *GetChatInitInformationForActorResponse) GetSenseforthChatInitInformation() *SenseforthChatInitInformation {
	if x != nil {
		return x.SenseforthChatInitInformation
	}
	return nil
}

func (x *GetChatInitInformationForActorResponse) GetShouldAutoRetry() common.BooleanEnum {
	if x != nil {
		return x.ShouldAutoRetry
	}
	return common.BooleanEnum(0)
}

func (x *GetChatInitInformationForActorResponse) GetTopicTags() []string {
	if x != nil {
		return x.TopicTags
	}
	return nil
}

func (x *GetChatInitInformationForActorResponse) GetChatbotInitInformation() *typesv2.ChatbotInitInformation {
	if x != nil {
		return x.ChatbotInitInformation
	}
	return nil
}

type FetchAccessTokenRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *FetchAccessTokenRequest) Reset() {
	*x = FetchAccessTokenRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_chat_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAccessTokenRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAccessTokenRequest) ProtoMessage() {}

func (x *FetchAccessTokenRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_chat_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAccessTokenRequest.ProtoReflect.Descriptor instead.
func (*FetchAccessTokenRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_chat_service_proto_rawDescGZIP(), []int{6}
}

func (x *FetchAccessTokenRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type FetchAccessTokenResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// access token to be used by client to make api calls to nugget
	AccessToken string `protobuf:"bytes,2,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
}

func (x *FetchAccessTokenResponse) Reset() {
	*x = FetchAccessTokenResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_cx_chat_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchAccessTokenResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchAccessTokenResponse) ProtoMessage() {}

func (x *FetchAccessTokenResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_cx_chat_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchAccessTokenResponse.ProtoReflect.Descriptor instead.
func (*FetchAccessTokenResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_cx_chat_service_proto_rawDescGZIP(), []int{7}
}

func (x *FetchAccessTokenResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *FetchAccessTokenResponse) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

var File_api_frontend_cx_chat_service_proto protoreflect.FileDescriptor

var file_api_frontend_cx_chat_service_proto_rawDesc = []byte{
	0x0a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63,
	0x78, 0x2f, 0x63, 0x68, 0x61, 0x74, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61,
	0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63,
	0x68, 0x61, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x6f,
	0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd7, 0x01, 0x0a,
	0x1d, 0x53, 0x65, 0x6e, 0x73, 0x65, 0x66, 0x6f, 0x72, 0x74, 0x68, 0x43, 0x68, 0x61, 0x74, 0x49,
	0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20,
	0x0a, 0x0c, 0x77, 0x65, 0x62, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x62, 0x56, 0x69, 0x65, 0x77, 0x55, 0x72, 0x6c,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x49, 0x0a, 0x10, 0x72, 0x65, 0x75, 0x73, 0x65, 0x5f, 0x63, 0x61, 0x63, 0x68, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0e, 0x72, 0x65,
	0x75, 0x73, 0x65, 0x43, 0x61, 0x63, 0x68, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x10,
	0x62, 0x6f, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x86, 0x01, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x42, 0x02, 0x18, 0x01, 0x52, 0x04, 0x61, 0x75, 0x74, 0x68, 0x12, 0x30, 0x0a,
	0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22,
	0x8d, 0x02, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07,
	0x61, 0x70, 0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x70, 0x70, 0x4b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x40, 0x0a,
	0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22,
	0xb8, 0x01, 0x0a, 0x19, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x64, 0x65, 0x43, 0x68,
	0x61, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4f, 0x0a,
	0x14, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x74, 0x72, 0x69, 0x65, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x74,
	0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x41, 0x70, 0x70, 0x43,
	0x68, 0x61, 0x74, 0x56, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52, 0x11, 0x6c, 0x61, 0x73,
	0x74, 0x54, 0x72, 0x69, 0x65, 0x64, 0x43, 0x68, 0x61, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x23,
	0x0a, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x25, 0x0a, 0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69,
	0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0xf6, 0x06, 0x0a, 0x25, 0x47,
	0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x6a, 0x0a, 0x22, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66, 0x75, 0x6c, 0x6c, 0x79, 0x5f, 0x6c, 0x6f, 0x61, 0x64,
	0x65, 0x64, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x49, 0x6e, 0x41, 0x70, 0x70, 0x43, 0x68, 0x61, 0x74, 0x56, 0x69, 0x65, 0x77, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x1e, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x66,
	0x75, 0x6c, 0x6c, 0x79, 0x4c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x43, 0x68, 0x61, 0x74, 0x56, 0x69,
	0x65, 0x77, 0x12, 0x5b, 0x0a, 0x1c, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x73, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x66, 0x75, 0x6c, 0x5f, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x19, 0x6c, 0x61, 0x73, 0x74, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x6d, 0x0a, 0x1d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x69, 0x64, 0x65, 0x5f, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x53, 0x69, 0x64, 0x65, 0x43, 0x68, 0x61, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x19, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x69, 0x64, 0x65, 0x43,
	0x68, 0x61, 0x74, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4b,
	0x0a, 0x11, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x65, 0x77, 0x5f, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42,
	0x6f, 0x6f, 0x6c, 0x65, 0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0f, 0x66, 0x6f, 0x72, 0x63,
	0x65, 0x4e, 0x65, 0x77, 0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x1b, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x66, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x62, 0x6f, 0x74, 0x5f, 0x63, 0x6f,
	0x6e, 0x74, 0x65, 0x78, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x18, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x66, 0x6f, 0x72, 0x74, 0x68, 0x42, 0x6f, 0x74, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x61, 0x0a, 0x08, 0x6d, 0x65,
	0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x74, 0x0a,
	0x0f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61,
	0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0e, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64,
	0x61, 0x74, 0x61, 0x1a, 0x3b, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x41, 0x0a, 0x13, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61,
	0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0xd8, 0x06, 0x0a, 0x26, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x49,
	0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f,
	0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x70, 0x70, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x61, 0x70,
	0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x70, 0x70,
	0x4b, 0x65, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x12, 0x88, 0x01, 0x0a, 0x16,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x52, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e,
	0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x55, 0x73, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x14, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x55, 0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x52, 0x0a, 0x16,
	0x63, 0x68, 0x61, 0x74, 0x5f, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x74, 0x6f, 0x5f, 0x62, 0x65, 0x5f,
	0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x49, 0x6e, 0x41, 0x70, 0x70,
	0x43, 0x68, 0x61, 0x74, 0x56, 0x69, 0x65, 0x77, 0x54, 0x79, 0x70, 0x65, 0x52, 0x12, 0x63, 0x68,
	0x61, 0x74, 0x56, 0x69, 0x65, 0x77, 0x54, 0x6f, 0x42, 0x65, 0x4c, 0x6f, 0x61, 0x64, 0x65, 0x64,
	0x12, 0x78, 0x0a, 0x20, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x66, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x63,
	0x68, 0x61, 0x74, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x53, 0x65,
	0x6e, 0x73, 0x65, 0x66, 0x6f, 0x72, 0x74, 0x68, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x69, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x1d, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x66, 0x6f, 0x72, 0x74, 0x68, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4b, 0x0a, 0x11, 0x73, 0x68,
	0x6f, 0x75, 0x6c, 0x64, 0x5f, 0x61, 0x75, 0x74, 0x6f, 0x5f, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x6f, 0x6f, 0x6c, 0x65,
	0x61, 0x6e, 0x45, 0x6e, 0x75, 0x6d, 0x52, 0x0f, 0x73, 0x68, 0x6f, 0x75, 0x6c, 0x64, 0x41, 0x75,
	0x74, 0x6f, 0x52, 0x65, 0x74, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x70,
	0x69, 0x63, 0x54, 0x61, 0x67, 0x73, 0x12, 0x5d, 0x0a, 0x18, 0x63, 0x68, 0x61, 0x74, 0x62, 0x6f,
	0x74, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x43, 0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e,
	0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x16, 0x63,
	0x68, 0x61, 0x74, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x47, 0x0a, 0x19, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x55,
	0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4b,
	0x0a, 0x17, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0x7f, 0x0a, 0x18, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72,
	0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0xc8, 0x03, 0x0a,
	0x05, 0x43, 0x68, 0x61, 0x74, 0x73, 0x12, 0xa9, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x43, 0x68,
	0x61, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x37, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x69, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78,
	0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x69,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x41,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x14, 0x80, 0x9e,
	0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7,
	0x0a, 0x00, 0x12, 0x91, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x49, 0x64, 0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2f, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74,
	0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x46,
	0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61,
	0x74, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64,
	0x46, 0x6f, 0x72, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7, 0x0a,
	0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x12, 0x7f, 0x0a, 0x10, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x29, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x63, 0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x14, 0x80, 0x9e, 0xd7, 0x0a, 0x01, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0xb0, 0x9e, 0xd7,
	0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x42, 0x5a, 0x0a, 0x2b, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x63,
	0x78, 0x2e, 0x63, 0x68, 0x61, 0x74, 0x5a, 0x2b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x63, 0x78, 0x2f, 0x63,
	0x68, 0x61, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_cx_chat_service_proto_rawDescOnce sync.Once
	file_api_frontend_cx_chat_service_proto_rawDescData = file_api_frontend_cx_chat_service_proto_rawDesc
)

func file_api_frontend_cx_chat_service_proto_rawDescGZIP() []byte {
	file_api_frontend_cx_chat_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_cx_chat_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_cx_chat_service_proto_rawDescData)
	})
	return file_api_frontend_cx_chat_service_proto_rawDescData
}

var file_api_frontend_cx_chat_service_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_frontend_cx_chat_service_proto_goTypes = []interface{}{
	(*SenseforthChatInitInformation)(nil),          // 0: frontend.cx.chat.SenseforthChatInitInformation
	(*GetReferenceIdForActorRequest)(nil),          // 1: frontend.cx.chat.GetReferenceIdForActorRequest
	(*GetReferenceIdForActorResponse)(nil),         // 2: frontend.cx.chat.GetReferenceIdForActorResponse
	(*ClientSideChatFailureInfo)(nil),              // 3: frontend.cx.chat.ClientSideChatFailureInfo
	(*GetChatInitInformationForActorRequest)(nil),  // 4: frontend.cx.chat.GetChatInitInformationForActorRequest
	(*GetChatInitInformationForActorResponse)(nil), // 5: frontend.cx.chat.GetChatInitInformationForActorResponse
	(*FetchAccessTokenRequest)(nil),                // 6: frontend.cx.chat.FetchAccessTokenRequest
	(*FetchAccessTokenResponse)(nil),               // 7: frontend.cx.chat.FetchAccessTokenResponse
	nil,                                            // 8: frontend.cx.chat.GetChatInitInformationForActorRequest.MetadataEntry
	nil,                                            // 9: frontend.cx.chat.GetChatInitInformationForActorRequest.ScreenMetadataEntry
	nil,                                            // 10: frontend.cx.chat.GetChatInitInformationForActorResponse.CustomUserPropertiesEntry
	(common.BooleanEnum)(0),                        // 11: api.typesv2.common.BooleanEnum
	(*header.AuthHeader)(nil),                      // 12: frontend.header.AuthHeader
	(*header.RequestHeader)(nil),                   // 13: frontend.header.RequestHeader
	(*rpc.Status)(nil),                             // 14: rpc.Status
	(*header.ResponseHeader)(nil),                  // 15: frontend.header.ResponseHeader
	(typesv2.InAppChatViewType)(0),                 // 16: api.typesv2.InAppChatViewType
	(*timestamppb.Timestamp)(nil),                  // 17: google.protobuf.Timestamp
	(*typesv2.ChatbotInitInformation)(nil),         // 18: api.typesv2.ChatbotInitInformation
}
var file_api_frontend_cx_chat_service_proto_depIdxs = []int32{
	11, // 0: frontend.cx.chat.SenseforthChatInitInformation.reuse_cache_data:type_name -> api.typesv2.common.BooleanEnum
	12, // 1: frontend.cx.chat.GetReferenceIdForActorRequest.auth:type_name -> frontend.header.AuthHeader
	13, // 2: frontend.cx.chat.GetReferenceIdForActorRequest.req:type_name -> frontend.header.RequestHeader
	14, // 3: frontend.cx.chat.GetReferenceIdForActorResponse.status:type_name -> rpc.Status
	15, // 4: frontend.cx.chat.GetReferenceIdForActorResponse.resp_header:type_name -> frontend.header.ResponseHeader
	16, // 5: frontend.cx.chat.ClientSideChatFailureInfo.last_tried_chat_view:type_name -> api.typesv2.InAppChatViewType
	13, // 6: frontend.cx.chat.GetChatInitInformationForActorRequest.req:type_name -> frontend.header.RequestHeader
	16, // 7: frontend.cx.chat.GetChatInitInformationForActorRequest.last_successfully_loaded_chat_view:type_name -> api.typesv2.InAppChatViewType
	17, // 8: frontend.cx.chat.GetChatInitInformationForActorRequest.last_successful_session_time:type_name -> google.protobuf.Timestamp
	3,  // 9: frontend.cx.chat.GetChatInitInformationForActorRequest.client_side_chat_failure_info:type_name -> frontend.cx.chat.ClientSideChatFailureInfo
	11, // 10: frontend.cx.chat.GetChatInitInformationForActorRequest.force_new_session:type_name -> api.typesv2.common.BooleanEnum
	8,  // 11: frontend.cx.chat.GetChatInitInformationForActorRequest.metadata:type_name -> frontend.cx.chat.GetChatInitInformationForActorRequest.MetadataEntry
	9,  // 12: frontend.cx.chat.GetChatInitInformationForActorRequest.screen_metadata:type_name -> frontend.cx.chat.GetChatInitInformationForActorRequest.ScreenMetadataEntry
	15, // 13: frontend.cx.chat.GetChatInitInformationForActorResponse.resp_header:type_name -> frontend.header.ResponseHeader
	10, // 14: frontend.cx.chat.GetChatInitInformationForActorResponse.custom_user_properties:type_name -> frontend.cx.chat.GetChatInitInformationForActorResponse.CustomUserPropertiesEntry
	16, // 15: frontend.cx.chat.GetChatInitInformationForActorResponse.chat_view_to_be_loaded:type_name -> api.typesv2.InAppChatViewType
	0,  // 16: frontend.cx.chat.GetChatInitInformationForActorResponse.senseforth_chat_init_information:type_name -> frontend.cx.chat.SenseforthChatInitInformation
	11, // 17: frontend.cx.chat.GetChatInitInformationForActorResponse.should_auto_retry:type_name -> api.typesv2.common.BooleanEnum
	18, // 18: frontend.cx.chat.GetChatInitInformationForActorResponse.chatbot_init_information:type_name -> api.typesv2.ChatbotInitInformation
	13, // 19: frontend.cx.chat.FetchAccessTokenRequest.req:type_name -> frontend.header.RequestHeader
	15, // 20: frontend.cx.chat.FetchAccessTokenResponse.resp_header:type_name -> frontend.header.ResponseHeader
	4,  // 21: frontend.cx.chat.Chats.GetChatInitInformationForActor:input_type -> frontend.cx.chat.GetChatInitInformationForActorRequest
	1,  // 22: frontend.cx.chat.Chats.GetReferenceIdForActor:input_type -> frontend.cx.chat.GetReferenceIdForActorRequest
	6,  // 23: frontend.cx.chat.Chats.FetchAccessToken:input_type -> frontend.cx.chat.FetchAccessTokenRequest
	5,  // 24: frontend.cx.chat.Chats.GetChatInitInformationForActor:output_type -> frontend.cx.chat.GetChatInitInformationForActorResponse
	2,  // 25: frontend.cx.chat.Chats.GetReferenceIdForActor:output_type -> frontend.cx.chat.GetReferenceIdForActorResponse
	7,  // 26: frontend.cx.chat.Chats.FetchAccessToken:output_type -> frontend.cx.chat.FetchAccessTokenResponse
	24, // [24:27] is the sub-list for method output_type
	21, // [21:24] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_api_frontend_cx_chat_service_proto_init() }
func file_api_frontend_cx_chat_service_proto_init() {
	if File_api_frontend_cx_chat_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_cx_chat_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SenseforthChatInitInformation); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_chat_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReferenceIdForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_chat_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetReferenceIdForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_chat_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientSideChatFailureInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_chat_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChatInitInformationForActorRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_chat_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetChatInitInformationForActorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_chat_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAccessTokenRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_cx_chat_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchAccessTokenResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_cx_chat_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_cx_chat_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_cx_chat_service_proto_depIdxs,
		MessageInfos:      file_api_frontend_cx_chat_service_proto_msgTypes,
	}.Build()
	File_api_frontend_cx_chat_service_proto = out.File
	file_api_frontend_cx_chat_service_proto_rawDesc = nil
	file_api_frontend_cx_chat_service_proto_goTypes = nil
	file_api_frontend_cx_chat_service_proto_depIdxs = nil
}
