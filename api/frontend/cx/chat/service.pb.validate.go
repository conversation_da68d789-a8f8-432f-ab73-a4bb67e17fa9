// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/cx/chat/service.proto

package chat

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	common "github.com/epifi/be-common/api/typesv2/common"

	typesv2 "github.com/epifi/gamma/api/typesv2"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = common.BooleanEnum(0)

	_ = typesv2.InAppChatViewType(0)
)

// Validate checks the field values on SenseforthChatInitInformation with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SenseforthChatInitInformation) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SenseforthChatInitInformation with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SenseforthChatInitInformationMultiError, or nil if none found.
func (m *SenseforthChatInitInformation) ValidateAll() error {
	return m.validate(true)
}

func (m *SenseforthChatInitInformation) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for WebViewUrl

	// no validation rules for ShortToken

	// no validation rules for ReuseCacheData

	// no validation rules for BotContextCode

	if len(errors) > 0 {
		return SenseforthChatInitInformationMultiError(errors)
	}

	return nil
}

// SenseforthChatInitInformationMultiError is an error wrapping multiple
// validation errors returned by SenseforthChatInitInformation.ValidateAll()
// if the designated constraints aren't met.
type SenseforthChatInitInformationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SenseforthChatInitInformationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SenseforthChatInitInformationMultiError) AllErrors() []error { return m }

// SenseforthChatInitInformationValidationError is the validation error
// returned by SenseforthChatInitInformation.Validate if the designated
// constraints aren't met.
type SenseforthChatInitInformationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SenseforthChatInitInformationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SenseforthChatInitInformationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SenseforthChatInitInformationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SenseforthChatInitInformationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SenseforthChatInitInformationValidationError) ErrorName() string {
	return "SenseforthChatInitInformationValidationError"
}

// Error satisfies the builtin error interface
func (e SenseforthChatInitInformationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSenseforthChatInitInformation.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SenseforthChatInitInformationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SenseforthChatInitInformationValidationError{}

// Validate checks the field values on GetReferenceIdForActorRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReferenceIdForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReferenceIdForActorRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetReferenceIdForActorRequestMultiError, or nil if none found.
func (m *GetReferenceIdForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReferenceIdForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetAuth()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReferenceIdForActorRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReferenceIdForActorRequestValidationError{
					field:  "Auth",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAuth()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReferenceIdForActorRequestValidationError{
				field:  "Auth",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReferenceIdForActorRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReferenceIdForActorRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReferenceIdForActorRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetReferenceIdForActorRequestMultiError(errors)
	}

	return nil
}

// GetReferenceIdForActorRequestMultiError is an error wrapping multiple
// validation errors returned by GetReferenceIdForActorRequest.ValidateAll()
// if the designated constraints aren't met.
type GetReferenceIdForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReferenceIdForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReferenceIdForActorRequestMultiError) AllErrors() []error { return m }

// GetReferenceIdForActorRequestValidationError is the validation error
// returned by GetReferenceIdForActorRequest.Validate if the designated
// constraints aren't met.
type GetReferenceIdForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReferenceIdForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReferenceIdForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReferenceIdForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReferenceIdForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReferenceIdForActorRequestValidationError) ErrorName() string {
	return "GetReferenceIdForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetReferenceIdForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReferenceIdForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReferenceIdForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReferenceIdForActorRequestValidationError{}

// Validate checks the field values on GetReferenceIdForActorResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetReferenceIdForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetReferenceIdForActorResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetReferenceIdForActorResponseMultiError, or nil if none found.
func (m *GetReferenceIdForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetReferenceIdForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReferenceIdForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReferenceIdForActorResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReferenceIdForActorResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	// no validation rules for ReferenceId

	// no validation rules for AppId

	// no validation rules for AppKey

	// no validation rules for Domain

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetReferenceIdForActorResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetReferenceIdForActorResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetReferenceIdForActorResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetReferenceIdForActorResponseMultiError(errors)
	}

	return nil
}

// GetReferenceIdForActorResponseMultiError is an error wrapping multiple
// validation errors returned by GetReferenceIdForActorResponse.ValidateAll()
// if the designated constraints aren't met.
type GetReferenceIdForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetReferenceIdForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetReferenceIdForActorResponseMultiError) AllErrors() []error { return m }

// GetReferenceIdForActorResponseValidationError is the validation error
// returned by GetReferenceIdForActorResponse.Validate if the designated
// constraints aren't met.
type GetReferenceIdForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetReferenceIdForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetReferenceIdForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetReferenceIdForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetReferenceIdForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetReferenceIdForActorResponseValidationError) ErrorName() string {
	return "GetReferenceIdForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetReferenceIdForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetReferenceIdForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetReferenceIdForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetReferenceIdForActorResponseValidationError{}

// Validate checks the field values on ClientSideChatFailureInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ClientSideChatFailureInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClientSideChatFailureInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ClientSideChatFailureInfoMultiError, or nil if none found.
func (m *ClientSideChatFailureInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ClientSideChatFailureInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for LastTriedChatView

	// no validation rules for FailureCount

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return ClientSideChatFailureInfoMultiError(errors)
	}

	return nil
}

// ClientSideChatFailureInfoMultiError is an error wrapping multiple validation
// errors returned by ClientSideChatFailureInfo.ValidateAll() if the
// designated constraints aren't met.
type ClientSideChatFailureInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClientSideChatFailureInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClientSideChatFailureInfoMultiError) AllErrors() []error { return m }

// ClientSideChatFailureInfoValidationError is the validation error returned by
// ClientSideChatFailureInfo.Validate if the designated constraints aren't met.
type ClientSideChatFailureInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClientSideChatFailureInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClientSideChatFailureInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClientSideChatFailureInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClientSideChatFailureInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClientSideChatFailureInfoValidationError) ErrorName() string {
	return "ClientSideChatFailureInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ClientSideChatFailureInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClientSideChatFailureInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClientSideChatFailureInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClientSideChatFailureInfoValidationError{}

// Validate checks the field values on GetChatInitInformationForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetChatInitInformationForActorRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetChatInitInformationForActorRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetChatInitInformationForActorRequestMultiError, or nil if none found.
func (m *GetChatInitInformationForActorRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatInitInformationForActorRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LastSuccessfullyLoadedChatView

	if all {
		switch v := interface{}(m.GetLastSuccessfulSessionTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "LastSuccessfulSessionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "LastSuccessfulSessionTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLastSuccessfulSessionTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorRequestValidationError{
				field:  "LastSuccessfulSessionTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetClientSideChatFailureInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "ClientSideChatFailureInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorRequestValidationError{
					field:  "ClientSideChatFailureInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetClientSideChatFailureInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorRequestValidationError{
				field:  "ClientSideChatFailureInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ForceNewSession

	// no validation rules for SenseforthBotContextCode

	// no validation rules for Metadata

	// no validation rules for ScreenMetadata

	if len(errors) > 0 {
		return GetChatInitInformationForActorRequestMultiError(errors)
	}

	return nil
}

// GetChatInitInformationForActorRequestMultiError is an error wrapping
// multiple validation errors returned by
// GetChatInitInformationForActorRequest.ValidateAll() if the designated
// constraints aren't met.
type GetChatInitInformationForActorRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatInitInformationForActorRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatInitInformationForActorRequestMultiError) AllErrors() []error { return m }

// GetChatInitInformationForActorRequestValidationError is the validation error
// returned by GetChatInitInformationForActorRequest.Validate if the
// designated constraints aren't met.
type GetChatInitInformationForActorRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatInitInformationForActorRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatInitInformationForActorRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatInitInformationForActorRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatInitInformationForActorRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatInitInformationForActorRequestValidationError) ErrorName() string {
	return "GetChatInitInformationForActorRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatInitInformationForActorRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatInitInformationForActorRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatInitInformationForActorRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatInitInformationForActorRequestValidationError{}

// Validate checks the field values on GetChatInitInformationForActorResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *GetChatInitInformationForActorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetChatInitInformationForActorResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// GetChatInitInformationForActorResponseMultiError, or nil if none found.
func (m *GetChatInitInformationForActorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetChatInitInformationForActorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ReferenceId

	// no validation rules for AppId

	// no validation rules for AppKey

	// no validation rules for Domain

	// no validation rules for CustomUserProperties

	// no validation rules for Email

	// no validation rules for ChatViewToBeLoaded

	if all {
		switch v := interface{}(m.GetSenseforthChatInitInformation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "SenseforthChatInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "SenseforthChatInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSenseforthChatInitInformation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorResponseValidationError{
				field:  "SenseforthChatInitInformation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ShouldAutoRetry

	if all {
		switch v := interface{}(m.GetChatbotInitInformation()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "ChatbotInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetChatInitInformationForActorResponseValidationError{
					field:  "ChatbotInitInformation",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetChatbotInitInformation()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetChatInitInformationForActorResponseValidationError{
				field:  "ChatbotInitInformation",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetChatInitInformationForActorResponseMultiError(errors)
	}

	return nil
}

// GetChatInitInformationForActorResponseMultiError is an error wrapping
// multiple validation errors returned by
// GetChatInitInformationForActorResponse.ValidateAll() if the designated
// constraints aren't met.
type GetChatInitInformationForActorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetChatInitInformationForActorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetChatInitInformationForActorResponseMultiError) AllErrors() []error { return m }

// GetChatInitInformationForActorResponseValidationError is the validation
// error returned by GetChatInitInformationForActorResponse.Validate if the
// designated constraints aren't met.
type GetChatInitInformationForActorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetChatInitInformationForActorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetChatInitInformationForActorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetChatInitInformationForActorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetChatInitInformationForActorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetChatInitInformationForActorResponseValidationError) ErrorName() string {
	return "GetChatInitInformationForActorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetChatInitInformationForActorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetChatInitInformationForActorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetChatInitInformationForActorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetChatInitInformationForActorResponseValidationError{}

// Validate checks the field values on FetchAccessTokenRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchAccessTokenRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchAccessTokenRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchAccessTokenRequestMultiError, or nil if none found.
func (m *FetchAccessTokenRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAccessTokenRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAccessTokenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAccessTokenRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAccessTokenRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchAccessTokenRequestMultiError(errors)
	}

	return nil
}

// FetchAccessTokenRequestMultiError is an error wrapping multiple validation
// errors returned by FetchAccessTokenRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchAccessTokenRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAccessTokenRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAccessTokenRequestMultiError) AllErrors() []error { return m }

// FetchAccessTokenRequestValidationError is the validation error returned by
// FetchAccessTokenRequest.Validate if the designated constraints aren't met.
type FetchAccessTokenRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAccessTokenRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAccessTokenRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAccessTokenRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAccessTokenRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAccessTokenRequestValidationError) ErrorName() string {
	return "FetchAccessTokenRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAccessTokenRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAccessTokenRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAccessTokenRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAccessTokenRequestValidationError{}

// Validate checks the field values on FetchAccessTokenResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FetchAccessTokenResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchAccessTokenResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FetchAccessTokenResponseMultiError, or nil if none found.
func (m *FetchAccessTokenResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchAccessTokenResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchAccessTokenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchAccessTokenResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchAccessTokenResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AccessToken

	if len(errors) > 0 {
		return FetchAccessTokenResponseMultiError(errors)
	}

	return nil
}

// FetchAccessTokenResponseMultiError is an error wrapping multiple validation
// errors returned by FetchAccessTokenResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchAccessTokenResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchAccessTokenResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchAccessTokenResponseMultiError) AllErrors() []error { return m }

// FetchAccessTokenResponseValidationError is the validation error returned by
// FetchAccessTokenResponse.Validate if the designated constraints aren't met.
type FetchAccessTokenResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchAccessTokenResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchAccessTokenResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchAccessTokenResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchAccessTokenResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchAccessTokenResponseValidationError) ErrorName() string {
	return "FetchAccessTokenResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchAccessTokenResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchAccessTokenResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchAccessTokenResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchAccessTokenResponseValidationError{}
