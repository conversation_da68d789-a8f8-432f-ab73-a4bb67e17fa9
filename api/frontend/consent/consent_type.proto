syntax = "proto3";

package frontend.consent;

option go_package = "github.com/epifi/gamma/api/frontend/consent";
option java_package = "com.github.epifi.gamma.api.frontend.consent";
option java_outer_classname = "ConsentTypeProtos";

// Represents the type of consent that an actor can provide
enum ConsentType {
  ConsentType_UNSPECIFIED = 0;
  TnC = 1;
  cKYC = 2;
  eKYC = 3;
  UPI = 5;
  FI_TNC = 6;
  FED_TNC = 7;
  FI_PRIVACY_POLICY = 8;
  FI_WEALTH_TNC = 9;
  CREDIT_REPORT_TNC = 10;
  HIGH_RISK_DEVICE = 11;
  // consent to use aadhaar number E.g. Use aadhaar number to
  // set / reset upi pin
  AADHAAR_NUMBER_BASED_PIN_SET = 24;

  // consent taken from agent while performing customer due diligence during Biometric KYC.
  BKYC_CUSTOMER_DUE_DILIGENCE = 25;
}
