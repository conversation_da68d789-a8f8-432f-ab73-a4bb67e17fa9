syntax = "proto3";

package frontend.fcm;

option go_package = "github.com/epifi/gamma/api/frontend/fcm";
option java_package = "com.github.epifi.gamma.api.frontend.fcm";

// Notification can contain custom key value pairs sent
// For example ; you can send key = "debit_card" and value = "anything"
// and client takes action based on this
// All these keys need to be defined at one place
// to maintain the contract properly
// All these enums will be sent in map as their string values
// client can parse the string to enum from the map
enum NotificationCustomKeys {
  UNSPECIFIED = 0;

  // map key will have string value of this enum since FCM
  // only has a map of string to string
  // client can parse the string to this enum
  DEEPLINK = 1;
}

// enum which defines the actions which are possible on an in app notification
enum NotificationAction {
  NOTIFICATION_ACTION_UNSPECIFIED = 0;
  // notification was clicked
  NOTIFICATION_ACTION_CLICK = 1;
  // notification was dismissed
  NOTIFICATION_ACTION_DISMISS = 2;
  // action associated with notification is completed
  // for example set upi pin is completed in case of set upi Pin notification
  NOTIFICATION_ACTION_COMPLETED = 3;
  // event to be used when user clicks on the notification shown on home as pop-up
  NOTIFICATION_ACTION_CLICKED_ON_HOME = 4;
  // event to be used when user dismisses a notification on home
  NOTIFICATION_ACTION_DISMISSED_ON_HOME = 5;
  // event to be used when we render a notification on home page
  NOTIFICATION_ACTION_VIEWED_ON_HOME = 6;
  // action to be used when user opens up notification center to mark all the notification till that point as read
  NOTIFICATION_ACTION_VIEW_ALL = 7;
  // action to be used for dismiss all behaviour, where we want to dismiss all the current notifications in the notification center
  NOTIFICATION_ACTION_DISMISS_ALL = 8;
}

// enum which defines if and how the notification timer should be shown on UI for an in app notification
enum ExpiryTimerType {
  // we will show the default timer if the enum is not passed
  EXPIRY_TIMER_TYPE_UNSPECIFIED = 0;
  // no timer will be shown on UI
  EXPIRY_TIMER_TYPE_HIDDEN = 1;
}

// enum to indicate the section in notification center a given notification belongs to
// there can be different sections like important, general etc based on criticality of the notifications
enum InAppNotificationSection {
  NOTIFICATION_SECTION_UNSPECIFIED = 0;

  NOTIFICATION_SECTION_IMPORTANT = 1;

  NOTIFICATION_SECTION_GENERAL = 2;
}

enum InAppNotificationPriority {
  NOTIFICATION_PRIORITY_UNSPECIFIED = 0;
  // Critical priority should be used for cases where the notification is actionable and timebound hence should be shown to user on top
  // ex: collect request notifications, vky expiry etc
  NOTIFICATION_PRIORITY_CRITICAL = 1;
  // High priority should also be used for cases where the notification is actionable and timebound 
  // but action is less critical and doesn't require immediate attention
  // ex: Add funds, card pin set
  // or informative notfication but important to show
  // ex: debit card delivery, sd created/closed etc 
  NOTIFICATION_PRIORITY_HIGH = 2;
  // Medium and low priority can be used for showing information where action is already done or for promotional notifications
  // ex: transaction notifications, referral, rewards and other promotional notifications
  NOTIFICATION_PRIORITY_MEDIUM = 3;
  NOTIFICATION_PRIORITY_LOW = 4;
}
