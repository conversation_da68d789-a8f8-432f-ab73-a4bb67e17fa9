// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/openbanking/deposit/customer.proto

package deposit

import (
	common "github.com/epifi/be-common/api/typesv2/common"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CustomerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// title before name like Mr/Mrs etc.
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// First name of customer.
	FirstName string `protobuf:"bytes,2,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	// Last name of customer
	LastName string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	// Phone number of customer
	PhoneNo *common.PhoneNumber `protobuf:"bytes,4,opt,name=phone_no,json=phoneNo,proto3" json:"phone_no,omitempty"`
	// email id of customer
	EmailId string `protobuf:"bytes,5,opt,name=email_id,json=emailId,proto3" json:"email_id,omitempty"`
}

func (x *CustomerInfo) Reset() {
	*x = CustomerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerInfo) ProtoMessage() {}

func (x *CustomerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerInfo.ProtoReflect.Descriptor instead.
func (*CustomerInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescGZIP(), []int{0}
}

func (x *CustomerInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CustomerInfo) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *CustomerInfo) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *CustomerInfo) GetPhoneNo() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNo
	}
	return nil
}

func (x *CustomerInfo) GetEmailId() string {
	if x != nil {
		return x.EmailId
	}
	return ""
}

// CustomerAccountInfo holds information related to customer account.
type CustomerAccountInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customer bank account number from which money will be deducted to create deposit account.
	OperativeAccountNumber string `protobuf:"bytes,1,opt,name=operative_account_number,json=operativeAccountNumber,proto3" json:"operative_account_number,omitempty"`
	// Customer bank account number where amount will be credited after maturity
	RepayAccountNumber string `protobuf:"bytes,2,opt,name=repay_account_number,json=repayAccountNumber,proto3" json:"repay_account_number,omitempty"`
	// Unique identifier for customer.
	CustomerId string `protobuf:"bytes,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
}

func (x *CustomerAccountInfo) Reset() {
	*x = CustomerAccountInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CustomerAccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CustomerAccountInfo) ProtoMessage() {}

func (x *CustomerAccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CustomerAccountInfo.ProtoReflect.Descriptor instead.
func (*CustomerAccountInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescGZIP(), []int{1}
}

func (x *CustomerAccountInfo) GetOperativeAccountNumber() string {
	if x != nil {
		return x.OperativeAccountNumber
	}
	return ""
}

func (x *CustomerAccountInfo) GetRepayAccountNumber() string {
	if x != nil {
		return x.RepayAccountNumber
	}
	return ""
}

func (x *CustomerAccountInfo) GetCustomerId() string {
	if x != nil {
		return x.CustomerId
	}
	return ""
}

// Set of attributes to reflect taxation info
type TaxInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Customer Tax assessed or not
	IsTaxAssessed bool `protobuf:"varint,1,opt,name=is_tax_assessed,json=isTaxAssessed,proto3" json:"is_tax_assessed,omitempty"`
	// Year of tax assessed
	AssessmentYear string `protobuf:"bytes,2,opt,name=assessment_year,json=assessmentYear,proto3" json:"assessment_year,omitempty"`
	// total income of customer
	TotalIncome string `protobuf:"bytes,3,opt,name=total_income,json=totalIncome,proto3" json:"total_income,omitempty"`
}

func (x *TaxInfo) Reset() {
	*x = TaxInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaxInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaxInfo) ProtoMessage() {}

func (x *TaxInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaxInfo.ProtoReflect.Descriptor instead.
func (*TaxInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescGZIP(), []int{2}
}

func (x *TaxInfo) GetIsTaxAssessed() bool {
	if x != nil {
		return x.IsTaxAssessed
	}
	return false
}

func (x *TaxInfo) GetAssessmentYear() string {
	if x != nil {
		return x.AssessmentYear
	}
	return ""
}

func (x *TaxInfo) GetTotalIncome() string {
	if x != nil {
		return x.TotalIncome
	}
	return ""
}

// Holds the nominee information for a deposit account along with its percentage
type DepositNomineeDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NomineeInfoList []*DepositNomineeDetails_DepositNomineeInfo `protobuf:"bytes,1,rep,name=nominee_info_list,json=nomineeInfoList,proto3" json:"nominee_info_list,omitempty"`
}

func (x *DepositNomineeDetails) Reset() {
	*x = DepositNomineeDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositNomineeDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositNomineeDetails) ProtoMessage() {}

func (x *DepositNomineeDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositNomineeDetails.ProtoReflect.Descriptor instead.
func (*DepositNomineeDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescGZIP(), []int{3}
}

func (x *DepositNomineeDetails) GetNomineeInfoList() []*DepositNomineeDetails_DepositNomineeInfo {
	if x != nil {
		return x.NomineeInfoList
	}
	return nil
}

type DepositNomineeDetails_DepositNomineeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Nominee *typesv2.Nominee `protobuf:"bytes,1,opt,name=nominee,proto3" json:"nominee,omitempty"`
	// percentage share of deposit allocated to this nominee
	PercentageShare string `protobuf:"bytes,2,opt,name=percentage_share,json=percentageShare,proto3" json:"percentage_share,omitempty"`
	// vendor specific city state code for nominee
	NomineeCityStateCode *DepositNomineeDetails_DepositNomineeInfo_CityStateCode `protobuf:"bytes,3,opt,name=nominee_city_state_code,json=nomineeCityStateCode,proto3" json:"nominee_city_state_code,omitempty"`
	// vendor specific city state code for guardian in case the nominee is a minor
	GuardianCityStateCode *DepositNomineeDetails_DepositNomineeInfo_CityStateCode `protobuf:"bytes,4,opt,name=guardian_city_state_code,json=guardianCityStateCode,proto3" json:"guardian_city_state_code,omitempty"`
}

func (x *DepositNomineeDetails_DepositNomineeInfo) Reset() {
	*x = DepositNomineeDetails_DepositNomineeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositNomineeDetails_DepositNomineeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositNomineeDetails_DepositNomineeInfo) ProtoMessage() {}

func (x *DepositNomineeDetails_DepositNomineeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositNomineeDetails_DepositNomineeInfo.ProtoReflect.Descriptor instead.
func (*DepositNomineeDetails_DepositNomineeInfo) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescGZIP(), []int{3, 0}
}

func (x *DepositNomineeDetails_DepositNomineeInfo) GetNominee() *typesv2.Nominee {
	if x != nil {
		return x.Nominee
	}
	return nil
}

func (x *DepositNomineeDetails_DepositNomineeInfo) GetPercentageShare() string {
	if x != nil {
		return x.PercentageShare
	}
	return ""
}

func (x *DepositNomineeDetails_DepositNomineeInfo) GetNomineeCityStateCode() *DepositNomineeDetails_DepositNomineeInfo_CityStateCode {
	if x != nil {
		return x.NomineeCityStateCode
	}
	return nil
}

func (x *DepositNomineeDetails_DepositNomineeInfo) GetGuardianCityStateCode() *DepositNomineeDetails_DepositNomineeInfo_CityStateCode {
	if x != nil {
		return x.GuardianCityStateCode
	}
	return nil
}

type DepositNomineeDetails_DepositNomineeInfo_CityStateCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vendor city code
	CityCode string `protobuf:"bytes,1,opt,name=city_code,json=cityCode,proto3" json:"city_code,omitempty"`
	// vendor state code
	StateCode string `protobuf:"bytes,2,opt,name=state_code,json=stateCode,proto3" json:"state_code,omitempty"`
}

func (x *DepositNomineeDetails_DepositNomineeInfo_CityStateCode) Reset() {
	*x = DepositNomineeDetails_DepositNomineeInfo_CityStateCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DepositNomineeDetails_DepositNomineeInfo_CityStateCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DepositNomineeDetails_DepositNomineeInfo_CityStateCode) ProtoMessage() {}

func (x *DepositNomineeDetails_DepositNomineeInfo_CityStateCode) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DepositNomineeDetails_DepositNomineeInfo_CityStateCode.ProtoReflect.Descriptor instead.
func (*DepositNomineeDetails_DepositNomineeInfo_CityStateCode) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescGZIP(), []int{3, 0, 0}
}

func (x *DepositNomineeDetails_DepositNomineeInfo_CityStateCode) GetCityCode() string {
	if x != nil {
		return x.CityCode
	}
	return ""
}

func (x *DepositNomineeDetails_DepositNomineeInfo_CityStateCode) GetStateCode() string {
	if x != nil {
		return x.StateCode
	}
	return ""
}

var File_api_vendorgateway_openbanking_deposit_customer_proto protoreflect.FileDescriptor

var file_api_vendorgateway_openbanking_deposit_customer_proto_rawDesc = []byte{
	0x0a, 0x34, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f,
	0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x21, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x19, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6e, 0x6f,
	0x6d, 0x69, 0x6e, 0x65, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb7, 0x01, 0x0a, 0x0c,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a,
	0x0a, 0x08, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x52, 0x07, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x49, 0x64, 0x22, 0xa2, 0x01, 0x0a, 0x13, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x38, 0x0a,
	0x18, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x16, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x76, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x70, 0x61, 0x79,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x72, 0x65, 0x70, 0x61, 0x79, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x22, 0x7d, 0x0a, 0x07, 0x54, 0x61,
	0x78, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x69, 0x73, 0x5f, 0x74, 0x61, 0x78, 0x5f,
	0x61, 0x73, 0x73, 0x65, 0x73, 0x73, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0d,
	0x69, 0x73, 0x54, 0x61, 0x78, 0x41, 0x73, 0x73, 0x65, 0x73, 0x73, 0x65, 0x64, 0x12, 0x27, 0x0a,
	0x0f, 0x61, 0x73, 0x73, 0x65, 0x73, 0x73, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x79, 0x65, 0x61, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61, 0x73, 0x73, 0x65, 0x73, 0x73, 0x6d, 0x65,
	0x6e, 0x74, 0x59, 0x65, 0x61, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x22, 0xf7, 0x04, 0x0a, 0x15, 0x44, 0x65,
	0x70, 0x6f, 0x73, 0x69, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x77, 0x0a, 0x11, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x69,
	0x6e, 0x66, 0x6f, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f,
	0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x64, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65,
	0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x6e, 0x6f, 0x6d,
	0x69, 0x6e, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74, 0x1a, 0xe4, 0x03, 0x0a,
	0x12, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x2e, 0x0a, 0x07, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x52, 0x07, 0x6e, 0x6f, 0x6d, 0x69,
	0x6e, 0x65, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x90,
	0x01, 0x0a, 0x17, 0x6e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x59, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x64, 0x65, 0x70,
	0x6f, 0x73, 0x69, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4e, 0x6f, 0x6d, 0x69,
	0x6e, 0x65, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x43, 0x69,
	0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x14, 0x6e, 0x6f, 0x6d,
	0x69, 0x6e, 0x65, 0x65, 0x43, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x92, 0x01, 0x0a, 0x18, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x5f, 0x63,
	0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x59, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x2e, 0x44, 0x65, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x44,
	0x65, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x4e, 0x6f, 0x6d, 0x69, 0x6e, 0x65, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x2e, 0x43, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x52,
	0x15, 0x67, 0x75, 0x61, 0x72, 0x64, 0x69, 0x61, 0x6e, 0x43, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x1a, 0x4b, 0x0a, 0x0d, 0x43, 0x69, 0x74, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x69, 0x74, 0x79, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x69, 0x74, 0x79,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x64, 0x65, 0x42, 0x7c, 0x0a, 0x3c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6f, 0x70, 0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x64, 0x65, 0x70, 0x6f,
	0x73, 0x69, 0x74, 0x5a, 0x3c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6f, 0x70,
	0x65, 0x6e, 0x62, 0x61, 0x6e, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x64, 0x65, 0x70, 0x6f, 0x73, 0x69,
	0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescOnce sync.Once
	file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescData = file_api_vendorgateway_openbanking_deposit_customer_proto_rawDesc
)

func file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescData)
	})
	return file_api_vendorgateway_openbanking_deposit_customer_proto_rawDescData
}

var file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_api_vendorgateway_openbanking_deposit_customer_proto_goTypes = []interface{}{
	(*CustomerInfo)(nil),                                           // 0: vendorgateway.openbanking.deposit.CustomerInfo
	(*CustomerAccountInfo)(nil),                                    // 1: vendorgateway.openbanking.deposit.CustomerAccountInfo
	(*TaxInfo)(nil),                                                // 2: vendorgateway.openbanking.deposit.TaxInfo
	(*DepositNomineeDetails)(nil),                                  // 3: vendorgateway.openbanking.deposit.DepositNomineeDetails
	(*DepositNomineeDetails_DepositNomineeInfo)(nil),               // 4: vendorgateway.openbanking.deposit.DepositNomineeDetails.DepositNomineeInfo
	(*DepositNomineeDetails_DepositNomineeInfo_CityStateCode)(nil), // 5: vendorgateway.openbanking.deposit.DepositNomineeDetails.DepositNomineeInfo.CityStateCode
	(*common.PhoneNumber)(nil),                                     // 6: api.typesv2.common.PhoneNumber
	(*typesv2.Nominee)(nil),                                        // 7: api.typesv2.Nominee
}
var file_api_vendorgateway_openbanking_deposit_customer_proto_depIdxs = []int32{
	6, // 0: vendorgateway.openbanking.deposit.CustomerInfo.phone_no:type_name -> api.typesv2.common.PhoneNumber
	4, // 1: vendorgateway.openbanking.deposit.DepositNomineeDetails.nominee_info_list:type_name -> vendorgateway.openbanking.deposit.DepositNomineeDetails.DepositNomineeInfo
	7, // 2: vendorgateway.openbanking.deposit.DepositNomineeDetails.DepositNomineeInfo.nominee:type_name -> api.typesv2.Nominee
	5, // 3: vendorgateway.openbanking.deposit.DepositNomineeDetails.DepositNomineeInfo.nominee_city_state_code:type_name -> vendorgateway.openbanking.deposit.DepositNomineeDetails.DepositNomineeInfo.CityStateCode
	5, // 4: vendorgateway.openbanking.deposit.DepositNomineeDetails.DepositNomineeInfo.guardian_city_state_code:type_name -> vendorgateway.openbanking.deposit.DepositNomineeDetails.DepositNomineeInfo.CityStateCode
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_openbanking_deposit_customer_proto_init() }
func file_api_vendorgateway_openbanking_deposit_customer_proto_init() {
	if File_api_vendorgateway_openbanking_deposit_customer_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CustomerAccountInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaxInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositNomineeDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositNomineeDetails_DepositNomineeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DepositNomineeDetails_DepositNomineeInfo_CityStateCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_openbanking_deposit_customer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_openbanking_deposit_customer_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_openbanking_deposit_customer_proto_depIdxs,
		MessageInfos:      file_api_vendorgateway_openbanking_deposit_customer_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_openbanking_deposit_customer_proto = out.File
	file_api_vendorgateway_openbanking_deposit_customer_proto_rawDesc = nil
	file_api_vendorgateway_openbanking_deposit_customer_proto_goTypes = nil
	file_api_vendorgateway_openbanking_deposit_customer_proto_depIdxs = nil
}
