// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/openbanking/payment/b2c/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	b2c "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPaymentClient is a mock of PaymentClient interface.
type MockPaymentClient struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentClientMockRecorder
}

// MockPaymentClientMockRecorder is the mock recorder for MockPaymentClient.
type MockPaymentClientMockRecorder struct {
	mock *MockPaymentClient
}

// NewMockPaymentClient creates a new mock instance.
func NewMockPaymentClient(ctrl *gomock.Controller) *MockPaymentClient {
	mock := &MockPaymentClient{ctrl: ctrl}
	mock.recorder = &MockPaymentClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentClient) EXPECT() *MockPaymentClientMockRecorder {
	return m.recorder
}

// GetB2CAccountBalance mocks base method.
func (m *MockPaymentClient) GetB2CAccountBalance(ctx context.Context, in *b2c.GetB2CAccountBalanceRequest, opts ...grpc.CallOption) (*b2c.GetB2CAccountBalanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetB2CAccountBalance", varargs...)
	ret0, _ := ret[0].(*b2c.GetB2CAccountBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetB2CAccountBalance indicates an expected call of GetB2CAccountBalance.
func (mr *MockPaymentClientMockRecorder) GetB2CAccountBalance(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetB2CAccountBalance", reflect.TypeOf((*MockPaymentClient)(nil).GetB2CAccountBalance), varargs...)
}

// GetB2CTransactionStatus mocks base method.
func (m *MockPaymentClient) GetB2CTransactionStatus(ctx context.Context, in *b2c.GetB2CTransactionStatusRequest, opts ...grpc.CallOption) (*b2c.GetB2CTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetB2CTransactionStatus", varargs...)
	ret0, _ := ret[0].(*b2c.GetB2CTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetB2CTransactionStatus indicates an expected call of GetB2CTransactionStatus.
func (mr *MockPaymentClientMockRecorder) GetB2CTransactionStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetB2CTransactionStatus", reflect.TypeOf((*MockPaymentClient)(nil).GetB2CTransactionStatus), varargs...)
}

// InitiateB2CPay mocks base method.
func (m *MockPaymentClient) InitiateB2CPay(ctx context.Context, in *b2c.InitiateB2CPayRequest, opts ...grpc.CallOption) (*b2c.InitiateB2CPayResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InitiateB2CPay", varargs...)
	ret0, _ := ret[0].(*b2c.InitiateB2CPayResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateB2CPay indicates an expected call of InitiateB2CPay.
func (mr *MockPaymentClientMockRecorder) InitiateB2CPay(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateB2CPay", reflect.TypeOf((*MockPaymentClient)(nil).InitiateB2CPay), varargs...)
}

// MockPaymentServer is a mock of PaymentServer interface.
type MockPaymentServer struct {
	ctrl     *gomock.Controller
	recorder *MockPaymentServerMockRecorder
}

// MockPaymentServerMockRecorder is the mock recorder for MockPaymentServer.
type MockPaymentServerMockRecorder struct {
	mock *MockPaymentServer
}

// NewMockPaymentServer creates a new mock instance.
func NewMockPaymentServer(ctrl *gomock.Controller) *MockPaymentServer {
	mock := &MockPaymentServer{ctrl: ctrl}
	mock.recorder = &MockPaymentServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPaymentServer) EXPECT() *MockPaymentServerMockRecorder {
	return m.recorder
}

// GetB2CAccountBalance mocks base method.
func (m *MockPaymentServer) GetB2CAccountBalance(arg0 context.Context, arg1 *b2c.GetB2CAccountBalanceRequest) (*b2c.GetB2CAccountBalanceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetB2CAccountBalance", arg0, arg1)
	ret0, _ := ret[0].(*b2c.GetB2CAccountBalanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetB2CAccountBalance indicates an expected call of GetB2CAccountBalance.
func (mr *MockPaymentServerMockRecorder) GetB2CAccountBalance(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetB2CAccountBalance", reflect.TypeOf((*MockPaymentServer)(nil).GetB2CAccountBalance), arg0, arg1)
}

// GetB2CTransactionStatus mocks base method.
func (m *MockPaymentServer) GetB2CTransactionStatus(arg0 context.Context, arg1 *b2c.GetB2CTransactionStatusRequest) (*b2c.GetB2CTransactionStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetB2CTransactionStatus", arg0, arg1)
	ret0, _ := ret[0].(*b2c.GetB2CTransactionStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetB2CTransactionStatus indicates an expected call of GetB2CTransactionStatus.
func (mr *MockPaymentServerMockRecorder) GetB2CTransactionStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetB2CTransactionStatus", reflect.TypeOf((*MockPaymentServer)(nil).GetB2CTransactionStatus), arg0, arg1)
}

// InitiateB2CPay mocks base method.
func (m *MockPaymentServer) InitiateB2CPay(arg0 context.Context, arg1 *b2c.InitiateB2CPayRequest) (*b2c.InitiateB2CPayResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InitiateB2CPay", arg0, arg1)
	ret0, _ := ret[0].(*b2c.InitiateB2CPayResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InitiateB2CPay indicates an expected call of InitiateB2CPay.
func (mr *MockPaymentServerMockRecorder) InitiateB2CPay(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InitiateB2CPay", reflect.TypeOf((*MockPaymentServer)(nil).InitiateB2CPay), arg0, arg1)
}

// MockUnsafePaymentServer is a mock of UnsafePaymentServer interface.
type MockUnsafePaymentServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafePaymentServerMockRecorder
}

// MockUnsafePaymentServerMockRecorder is the mock recorder for MockUnsafePaymentServer.
type MockUnsafePaymentServerMockRecorder struct {
	mock *MockUnsafePaymentServer
}

// NewMockUnsafePaymentServer creates a new mock instance.
func NewMockUnsafePaymentServer(ctrl *gomock.Controller) *MockUnsafePaymentServer {
	mock := &MockUnsafePaymentServer{ctrl: ctrl}
	mock.recorder = &MockUnsafePaymentServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafePaymentServer) EXPECT() *MockUnsafePaymentServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedPaymentServer mocks base method.
func (m *MockUnsafePaymentServer) mustEmbedUnimplementedPaymentServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedPaymentServer")
}

// mustEmbedUnimplementedPaymentServer indicates an expected call of mustEmbedUnimplementedPaymentServer.
func (mr *MockUnsafePaymentServerMockRecorder) mustEmbedUnimplementedPaymentServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedPaymentServer", reflect.TypeOf((*MockUnsafePaymentServer)(nil).mustEmbedUnimplementedPaymentServer))
}
