// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendorgateway/lending/preapprovedloan/lenden/service.proto

package lenden

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateUserRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserRequestMultiError, or nil if none found.
func (m *CreateUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUserRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUserRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUserRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetName() == nil {
		err := CreateUserRequestValidationError{
			field:  "Name",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUserRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUserRequestValidationError{
					field:  "Name",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUserRequestValidationError{
				field:  "Name",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetPan()) < 1 {
		err := CreateUserRequestValidationError{
			field:  "Pan",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetPhoneNumber() == nil {
		err := CreateUserRequestValidationError{
			field:  "PhoneNumber",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetPhoneNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUserRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUserRequestValidationError{
					field:  "PhoneNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPhoneNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUserRequestValidationError{
				field:  "PhoneNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if err := m._validateEmail(m.GetEmail()); err != nil {
		err = CreateUserRequestValidationError{
			field:  "Email",
			reason: "value must be a valid email address",
			cause:  err,
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDob() == nil {
		err := CreateUserRequestValidationError{
			field:  "Dob",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUserRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUserRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUserRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetReferenceId()) < 1 {
		err := CreateUserRequestValidationError{
			field:  "ReferenceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CreateUserRequest_EmploymentType_NotInLookup[m.GetEmploymentType()]; ok {
		err := CreateUserRequestValidationError{
			field:  "EmploymentType",
			reason: "value must not be in list [LENDEN_EMPLOYMENT_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EmploymentOrganizationName

	if ip := net.ParseIP(m.GetUserIp()); ip == nil {
		err := CreateUserRequestValidationError{
			field:  "UserIp",
			reason: "value must be a valid IP address",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDeviceId()) < 1 {
		err := CreateUserRequestValidationError{
			field:  "DeviceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Latitude

	// no validation rules for Longitude

	if m.GetConsentTime() == nil {
		err := CreateUserRequestValidationError{
			field:  "ConsentTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CreateUserRequestMultiError(errors)
	}

	return nil
}

func (m *CreateUserRequest) _validateHostname(host string) error {
	s := strings.ToLower(strings.TrimSuffix(host, "."))

	if len(host) > 253 {
		return errors.New("hostname cannot exceed 253 characters")
	}

	for _, part := range strings.Split(s, ".") {
		if l := len(part); l == 0 || l > 63 {
			return errors.New("hostname part must be non-empty and cannot exceed 63 characters")
		}

		if part[0] == '-' {
			return errors.New("hostname parts cannot begin with hyphens")
		}

		if part[len(part)-1] == '-' {
			return errors.New("hostname parts cannot end with hyphens")
		}

		for _, r := range part {
			if (r < 'a' || r > 'z') && (r < '0' || r > '9') && r != '-' {
				return fmt.Errorf("hostname parts can only contain alphanumeric characters or hyphens, got %q", string(r))
			}
		}
	}

	return nil
}

func (m *CreateUserRequest) _validateEmail(addr string) error {
	a, err := mail.ParseAddress(addr)
	if err != nil {
		return err
	}
	addr = a.Address

	if len(addr) > 254 {
		return errors.New("email addresses cannot exceed 254 characters")
	}

	parts := strings.SplitN(addr, "@", 2)

	if len(parts[0]) > 64 {
		return errors.New("email address local phrase cannot exceed 64 characters")
	}

	return m._validateHostname(parts[1])
}

// CreateUserRequestMultiError is an error wrapping multiple validation errors
// returned by CreateUserRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserRequestMultiError) AllErrors() []error { return m }

// CreateUserRequestValidationError is the validation error returned by
// CreateUserRequest.Validate if the designated constraints aren't met.
type CreateUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserRequestValidationError) ErrorName() string {
	return "CreateUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserRequestValidationError{}

var _CreateUserRequest_EmploymentType_NotInLookup = map[LendenEmploymentType]struct{}{
	0: {},
}

// Validate checks the field values on CreateUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateUserResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateUserResponseMultiError, or nil if none found.
func (m *CreateUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateUserResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateUserResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for UserId

	if len(errors) > 0 {
		return CreateUserResponseMultiError(errors)
	}

	return nil
}

// CreateUserResponseMultiError is an error wrapping multiple validation errors
// returned by CreateUserResponse.ValidateAll() if the designated constraints
// aren't met.
type CreateUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateUserResponseMultiError) AllErrors() []error { return m }

// CreateUserResponseValidationError is the validation error returned by
// CreateUserResponse.Validate if the designated constraints aren't met.
type CreateUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateUserResponseValidationError) ErrorName() string {
	return "CreateUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CreateUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateUserResponseValidationError{}

// Validate checks the field values on ApplyForLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplyForLoanRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplyForLoanRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplyForLoanRequestMultiError, or nil if none found.
func (m *ApplyForLoanRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplyForLoanRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyForLoanRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyForLoanRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyForLoanRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := ApplyForLoanRequestValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetReferenceId()) < 1 {
		err := ApplyForLoanRequestValidationError{
			field:  "ReferenceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRequestedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyForLoanRequestValidationError{
					field:  "RequestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyForLoanRequestValidationError{
					field:  "RequestedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyForLoanRequestValidationError{
				field:  "RequestedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetInterest() == nil {
		err := ApplyForLoanRequestValidationError{
			field:  "Interest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyForLoanRequestValidationError{
					field:  "Interest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyForLoanRequestValidationError{
					field:  "Interest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyForLoanRequestValidationError{
				field:  "Interest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if _, ok := _ApplyForLoanRequest_AddressType_NotInLookup[m.GetAddressType()]; ok {
		err := ApplyForLoanRequestValidationError{
			field:  "AddressType",
			reason: "value must not be in list [ADDRESS_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAddress() == nil {
		err := ApplyForLoanRequestValidationError{
			field:  "Address",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyForLoanRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyForLoanRequestValidationError{
					field:  "Address",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyForLoanRequestValidationError{
				field:  "Address",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ApplyForLoanRequestMultiError(errors)
	}

	return nil
}

// ApplyForLoanRequestMultiError is an error wrapping multiple validation
// errors returned by ApplyForLoanRequest.ValidateAll() if the designated
// constraints aren't met.
type ApplyForLoanRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplyForLoanRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplyForLoanRequestMultiError) AllErrors() []error { return m }

// ApplyForLoanRequestValidationError is the validation error returned by
// ApplyForLoanRequest.Validate if the designated constraints aren't met.
type ApplyForLoanRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplyForLoanRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplyForLoanRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplyForLoanRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplyForLoanRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplyForLoanRequestValidationError) ErrorName() string {
	return "ApplyForLoanRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ApplyForLoanRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplyForLoanRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplyForLoanRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplyForLoanRequestValidationError{}

var _ApplyForLoanRequest_AddressType_NotInLookup = map[AddressType]struct{}{
	0: {},
}

// Validate checks the field values on ApplyForLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplyForLoanResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplyForLoanResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplyForLoanResponseMultiError, or nil if none found.
func (m *ApplyForLoanResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplyForLoanResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ApplyForLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ApplyForLoanResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ApplyForLoanResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	if len(errors) > 0 {
		return ApplyForLoanResponseMultiError(errors)
	}

	return nil
}

// ApplyForLoanResponseMultiError is an error wrapping multiple validation
// errors returned by ApplyForLoanResponse.ValidateAll() if the designated
// constraints aren't met.
type ApplyForLoanResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplyForLoanResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplyForLoanResponseMultiError) AllErrors() []error { return m }

// ApplyForLoanResponseValidationError is the validation error returned by
// ApplyForLoanResponse.Validate if the designated constraints aren't met.
type ApplyForLoanResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplyForLoanResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplyForLoanResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplyForLoanResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplyForLoanResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplyForLoanResponseValidationError) ErrorName() string {
	return "ApplyForLoanResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ApplyForLoanResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplyForLoanResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplyForLoanResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplyForLoanResponseValidationError{}

// Validate checks the field values on CheckHardEligibilityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckHardEligibilityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckHardEligibilityRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckHardEligibilityRequestMultiError, or nil if none found.
func (m *CheckHardEligibilityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckHardEligibilityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckHardEligibilityRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckHardEligibilityRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckHardEligibilityRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := CheckHardEligibilityRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := CheckHardEligibilityRequestValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckHardEligibilityRequestMultiError(errors)
	}

	return nil
}

// CheckHardEligibilityRequestMultiError is an error wrapping multiple
// validation errors returned by CheckHardEligibilityRequest.ValidateAll() if
// the designated constraints aren't met.
type CheckHardEligibilityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckHardEligibilityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckHardEligibilityRequestMultiError) AllErrors() []error { return m }

// CheckHardEligibilityRequestValidationError is the validation error returned
// by CheckHardEligibilityRequest.Validate if the designated constraints
// aren't met.
type CheckHardEligibilityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckHardEligibilityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckHardEligibilityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckHardEligibilityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckHardEligibilityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckHardEligibilityRequestValidationError) ErrorName() string {
	return "CheckHardEligibilityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckHardEligibilityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckHardEligibilityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckHardEligibilityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckHardEligibilityRequestValidationError{}

// Validate checks the field values on CheckHardEligibilityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckHardEligibilityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckHardEligibilityResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckHardEligibilityResponseMultiError, or nil if none found.
func (m *CheckHardEligibilityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckHardEligibilityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckHardEligibilityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckHardEligibilityResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckHardEligibilityResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EligibilityStatus

	if all {
		switch v := interface{}(m.GetOfferData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckHardEligibilityResponseValidationError{
					field:  "OfferData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckHardEligibilityResponseValidationError{
					field:  "OfferData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOfferData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckHardEligibilityResponseValidationError{
				field:  "OfferData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckHardEligibilityResponseMultiError(errors)
	}

	return nil
}

// CheckHardEligibilityResponseMultiError is an error wrapping multiple
// validation errors returned by CheckHardEligibilityResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckHardEligibilityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckHardEligibilityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckHardEligibilityResponseMultiError) AllErrors() []error { return m }

// CheckHardEligibilityResponseValidationError is the validation error returned
// by CheckHardEligibilityResponse.Validate if the designated constraints
// aren't met.
type CheckHardEligibilityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckHardEligibilityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckHardEligibilityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckHardEligibilityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckHardEligibilityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckHardEligibilityResponseValidationError) ErrorName() string {
	return "CheckHardEligibilityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckHardEligibilityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckHardEligibilityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckHardEligibilityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckHardEligibilityResponseValidationError{}

// Validate checks the field values on OfferData with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *OfferData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferData with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in OfferDataMultiError, or nil
// if none found.
func (m *OfferData) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetOffers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferDataValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferDataValidationError{
						field:  fmt.Sprintf("Offers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferDataValidationError{
					field:  fmt.Sprintf("Offers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for OfferSelectionMultiple

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDataValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDataValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDataValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferDataValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferDataValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferDataValidationError{
				field:  "MaxAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetApplicableTenures() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, OfferDataValidationError{
						field:  fmt.Sprintf("ApplicableTenures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, OfferDataValidationError{
						field:  fmt.Sprintf("ApplicableTenures[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return OfferDataValidationError{
					field:  fmt.Sprintf("ApplicableTenures[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return OfferDataMultiError(errors)
	}

	return nil
}

// OfferDataMultiError is an error wrapping multiple validation errors returned
// by OfferData.ValidateAll() if the designated constraints aren't met.
type OfferDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferDataMultiError) AllErrors() []error { return m }

// OfferDataValidationError is the validation error returned by
// OfferData.Validate if the designated constraints aren't met.
type OfferDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferDataValidationError) ErrorName() string { return "OfferDataValidationError" }

// Error satisfies the builtin error interface
func (e OfferDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferDataValidationError{}

// Validate checks the field values on P2POfferDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *P2POfferDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on P2POfferDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// P2POfferDetailsMultiError, or nil if none found.
func (m *P2POfferDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *P2POfferDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OfferCode

	// no validation rules for Roi

	// no validation rules for FundingProbability

	// no validation rules for ExpectedTimeToGetFunding

	// no validation rules for IsRecommended

	if len(errors) > 0 {
		return P2POfferDetailsMultiError(errors)
	}

	return nil
}

// P2POfferDetailsMultiError is an error wrapping multiple validation errors
// returned by P2POfferDetails.ValidateAll() if the designated constraints
// aren't met.
type P2POfferDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m P2POfferDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m P2POfferDetailsMultiError) AllErrors() []error { return m }

// P2POfferDetailsValidationError is the validation error returned by
// P2POfferDetails.Validate if the designated constraints aren't met.
type P2POfferDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e P2POfferDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e P2POfferDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e P2POfferDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e P2POfferDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e P2POfferDetailsValidationError) ErrorName() string { return "P2POfferDetailsValidationError" }

// Error satisfies the builtin error interface
func (e P2POfferDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sP2POfferDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = P2POfferDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = P2POfferDetailsValidationError{}

// Validate checks the field values on SelectOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SelectOfferRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SelectOfferRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SelectOfferRequestMultiError, or nil if none found.
func (m *SelectOfferRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SelectOfferRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SelectOfferRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := SelectOfferRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := SelectOfferRequestValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSelectedAmount() == nil {
		err := SelectOfferRequestValidationError{
			field:  "SelectedAmount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSelectedAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "SelectedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SelectOfferRequestValidationError{
					field:  "SelectedAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSelectedAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SelectOfferRequestValidationError{
				field:  "SelectedAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Tenure

	if utf8.RuneCountInString(m.GetSelectedOfferId()) < 1 {
		err := SelectOfferRequestValidationError{
			field:  "SelectedOfferId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SelectOfferRequestMultiError(errors)
	}

	return nil
}

// SelectOfferRequestMultiError is an error wrapping multiple validation errors
// returned by SelectOfferRequest.ValidateAll() if the designated constraints
// aren't met.
type SelectOfferRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelectOfferRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelectOfferRequestMultiError) AllErrors() []error { return m }

// SelectOfferRequestValidationError is the validation error returned by
// SelectOfferRequest.Validate if the designated constraints aren't met.
type SelectOfferRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelectOfferRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelectOfferRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SelectOfferRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelectOfferRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelectOfferRequestValidationError) ErrorName() string {
	return "SelectOfferRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SelectOfferRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelectOfferRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelectOfferRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelectOfferRequestValidationError{}

// Validate checks the field values on SelectOfferResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SelectOfferResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SelectOfferResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SelectOfferResponseMultiError, or nil if none found.
func (m *SelectOfferResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SelectOfferResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SelectOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SelectOfferResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SelectOfferResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for OfferCode

	if len(errors) > 0 {
		return SelectOfferResponseMultiError(errors)
	}

	return nil
}

// SelectOfferResponseMultiError is an error wrapping multiple validation
// errors returned by SelectOfferResponse.ValidateAll() if the designated
// constraints aren't met.
type SelectOfferResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SelectOfferResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SelectOfferResponseMultiError) AllErrors() []error { return m }

// SelectOfferResponseValidationError is the validation error returned by
// SelectOfferResponse.Validate if the designated constraints aren't met.
type SelectOfferResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SelectOfferResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SelectOfferResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SelectOfferResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SelectOfferResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SelectOfferResponseValidationError) ErrorName() string {
	return "SelectOfferResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SelectOfferResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSelectOfferResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SelectOfferResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SelectOfferResponseValidationError{}

// Validate checks the field values on ModifyRateOfInterestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyRateOfInterestRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyRateOfInterestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyRateOfInterestRequestMultiError, or nil if none found.
func (m *ModifyRateOfInterestRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyRateOfInterestRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRateOfInterestRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRateOfInterestRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRateOfInterestRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := ModifyRateOfInterestRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetInterestRate() <= 0 {
		err := ModifyRateOfInterestRequestValidationError{
			field:  "InterestRate",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if ip := net.ParseIP(m.GetUserIp()); ip == nil {
		err := ModifyRateOfInterestRequestValidationError{
			field:  "UserIp",
			reason: "value must be a valid IP address",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDeviceId()) < 1 {
		err := ModifyRateOfInterestRequestValidationError{
			field:  "DeviceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Latitude

	// no validation rules for Longitude

	// no validation rules for UserId

	if all {
		switch v := interface{}(m.GetConsentedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRateOfInterestRequestValidationError{
					field:  "ConsentedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRateOfInterestRequestValidationError{
					field:  "ConsentedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetConsentedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRateOfInterestRequestValidationError{
				field:  "ConsentedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ModifyRateOfInterestRequestMultiError(errors)
	}

	return nil
}

// ModifyRateOfInterestRequestMultiError is an error wrapping multiple
// validation errors returned by ModifyRateOfInterestRequest.ValidateAll() if
// the designated constraints aren't met.
type ModifyRateOfInterestRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyRateOfInterestRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyRateOfInterestRequestMultiError) AllErrors() []error { return m }

// ModifyRateOfInterestRequestValidationError is the validation error returned
// by ModifyRateOfInterestRequest.Validate if the designated constraints
// aren't met.
type ModifyRateOfInterestRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyRateOfInterestRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyRateOfInterestRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyRateOfInterestRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyRateOfInterestRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyRateOfInterestRequestValidationError) ErrorName() string {
	return "ModifyRateOfInterestRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyRateOfInterestRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyRateOfInterestRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyRateOfInterestRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyRateOfInterestRequestValidationError{}

// Validate checks the field values on ModifyRateOfInterestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModifyRateOfInterestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModifyRateOfInterestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModifyRateOfInterestResponseMultiError, or nil if none found.
func (m *ModifyRateOfInterestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ModifyRateOfInterestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRateOfInterestResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRateOfInterestResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRateOfInterestResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInstallmentAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ModifyRateOfInterestResponseValidationError{
					field:  "InstallmentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ModifyRateOfInterestResponseValidationError{
					field:  "InstallmentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstallmentAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ModifyRateOfInterestResponseValidationError{
				field:  "InstallmentAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KfsDocUrl

	// no validation rules for LoanAgreementDocUrl

	if len(errors) > 0 {
		return ModifyRateOfInterestResponseMultiError(errors)
	}

	return nil
}

// ModifyRateOfInterestResponseMultiError is an error wrapping multiple
// validation errors returned by ModifyRateOfInterestResponse.ValidateAll() if
// the designated constraints aren't met.
type ModifyRateOfInterestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModifyRateOfInterestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModifyRateOfInterestResponseMultiError) AllErrors() []error { return m }

// ModifyRateOfInterestResponseValidationError is the validation error returned
// by ModifyRateOfInterestResponse.Validate if the designated constraints
// aren't met.
type ModifyRateOfInterestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModifyRateOfInterestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModifyRateOfInterestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModifyRateOfInterestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModifyRateOfInterestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModifyRateOfInterestResponseValidationError) ErrorName() string {
	return "ModifyRateOfInterestResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ModifyRateOfInterestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModifyRateOfInterestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModifyRateOfInterestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModifyRateOfInterestResponseValidationError{}

// Validate checks the field values on KycInitRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *KycInitRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KycInitRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in KycInitRequestMultiError,
// or nil if none found.
func (m *KycInitRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *KycInitRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycInitRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycInitRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycInitRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := KycInitRequestValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := KycInitRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if ip := net.ParseIP(m.GetUserIp()); ip == nil {
		err := KycInitRequestValidationError{
			field:  "UserIp",
			reason: "value must be a valid IP address",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDeviceId()) < 1 {
		err := KycInitRequestValidationError{
			field:  "DeviceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Latitude

	// no validation rules for Longitude

	if m.GetConsentTime() == nil {
		err := KycInitRequestValidationError{
			field:  "ConsentTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return KycInitRequestMultiError(errors)
	}

	return nil
}

// KycInitRequestMultiError is an error wrapping multiple validation errors
// returned by KycInitRequest.ValidateAll() if the designated constraints
// aren't met.
type KycInitRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KycInitRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KycInitRequestMultiError) AllErrors() []error { return m }

// KycInitRequestValidationError is the validation error returned by
// KycInitRequest.Validate if the designated constraints aren't met.
type KycInitRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KycInitRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KycInitRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KycInitRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KycInitRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KycInitRequestValidationError) ErrorName() string { return "KycInitRequestValidationError" }

// Error satisfies the builtin error interface
func (e KycInitRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKycInitRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KycInitRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KycInitRequestValidationError{}

// Validate checks the field values on KycInitResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *KycInitResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on KycInitResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// KycInitResponseMultiError, or nil if none found.
func (m *KycInitResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *KycInitResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, KycInitResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, KycInitResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return KycInitResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycStatus

	// no validation rules for RedirectionUrl

	// no validation rules for TrackingId

	// no validation rules for KycMessage

	if len(errors) > 0 {
		return KycInitResponseMultiError(errors)
	}

	return nil
}

// KycInitResponseMultiError is an error wrapping multiple validation errors
// returned by KycInitResponse.ValidateAll() if the designated constraints
// aren't met.
type KycInitResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m KycInitResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m KycInitResponseMultiError) AllErrors() []error { return m }

// KycInitResponseValidationError is the validation error returned by
// KycInitResponse.Validate if the designated constraints aren't met.
type KycInitResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e KycInitResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e KycInitResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e KycInitResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e KycInitResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e KycInitResponseValidationError) ErrorName() string { return "KycInitResponseValidationError" }

// Error satisfies the builtin error interface
func (e KycInitResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sKycInitResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = KycInitResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = KycInitResponseValidationError{}

// Validate checks the field values on CheckKycStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckKycStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckKycStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckKycStatusRequestMultiError, or nil if none found.
func (m *CheckKycStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckKycStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckKycStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckKycStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckKycStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := CheckKycStatusRequestValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTrackingId()) < 1 {
		err := CheckKycStatusRequestValidationError{
			field:  "TrackingId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckKycStatusRequestMultiError(errors)
	}

	return nil
}

// CheckKycStatusRequestMultiError is an error wrapping multiple validation
// errors returned by CheckKycStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type CheckKycStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckKycStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckKycStatusRequestMultiError) AllErrors() []error { return m }

// CheckKycStatusRequestValidationError is the validation error returned by
// CheckKycStatusRequest.Validate if the designated constraints aren't met.
type CheckKycStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckKycStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckKycStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckKycStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckKycStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckKycStatusRequestValidationError) ErrorName() string {
	return "CheckKycStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckKycStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckKycStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckKycStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckKycStatusRequestValidationError{}

// Validate checks the field values on CheckKycStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckKycStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckKycStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckKycStatusResponseMultiError, or nil if none found.
func (m *CheckKycStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckKycStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckKycStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckKycStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckKycStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KycStatus

	if len(errors) > 0 {
		return CheckKycStatusResponseMultiError(errors)
	}

	return nil
}

// CheckKycStatusResponseMultiError is an error wrapping multiple validation
// errors returned by CheckKycStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type CheckKycStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckKycStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckKycStatusResponseMultiError) AllErrors() []error { return m }

// CheckKycStatusResponseValidationError is the validation error returned by
// CheckKycStatusResponse.Validate if the designated constraints aren't met.
type CheckKycStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckKycStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckKycStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckKycStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckKycStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckKycStatusResponseValidationError) ErrorName() string {
	return "CheckKycStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckKycStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckKycStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckKycStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckKycStatusResponseValidationError{}

// Validate checks the field values on AddBankDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddBankDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddBankDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddBankDetailsRequestMultiError, or nil if none found.
func (m *AddBankDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *AddBankDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddBankDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddBankDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddBankDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := AddBankDetailsRequestValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := AddBankDetailsRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetBankAccountDetails() == nil {
		err := AddBankDetailsRequestValidationError{
			field:  "BankAccountDetails",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetBankAccountDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddBankDetailsRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddBankDetailsRequestValidationError{
					field:  "BankAccountDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankAccountDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddBankDetailsRequestValidationError{
				field:  "BankAccountDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddBankDetailsRequestMultiError(errors)
	}

	return nil
}

// AddBankDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by AddBankDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type AddBankDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddBankDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddBankDetailsRequestMultiError) AllErrors() []error { return m }

// AddBankDetailsRequestValidationError is the validation error returned by
// AddBankDetailsRequest.Validate if the designated constraints aren't met.
type AddBankDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddBankDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddBankDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddBankDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddBankDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddBankDetailsRequestValidationError) ErrorName() string {
	return "AddBankDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e AddBankDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddBankDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddBankDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddBankDetailsRequestValidationError{}

// Validate checks the field values on AddBankDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AddBankDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddBankDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddBankDetailsResponseMultiError, or nil if none found.
func (m *AddBankDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddBankDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddBankDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddBankDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddBankDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddBankDetailsResponseMultiError(errors)
	}

	return nil
}

// AddBankDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by AddBankDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type AddBankDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddBankDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddBankDetailsResponseMultiError) AllErrors() []error { return m }

// AddBankDetailsResponseValidationError is the validation error returned by
// AddBankDetailsResponse.Validate if the designated constraints aren't met.
type AddBankDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddBankDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddBankDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddBankDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddBankDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddBankDetailsResponseValidationError) ErrorName() string {
	return "AddBankDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddBankDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddBankDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddBankDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddBankDetailsResponseValidationError{}

// Validate checks the field values on InitMandateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitMandateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitMandateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitMandateRequestMultiError, or nil if none found.
func (m *InitMandateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *InitMandateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := InitMandateRequestValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := InitMandateRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _InitMandateRequest_MandateType_NotInLookup[m.GetMandateType()]; ok {
		err := InitMandateRequestValidationError{
			field:  "MandateType",
			reason: "value must not be in list [MANDATE_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if ip := net.ParseIP(m.GetUserIp()); ip == nil {
		err := InitMandateRequestValidationError{
			field:  "UserIp",
			reason: "value must be a valid IP address",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDeviceId()) < 1 {
		err := InitMandateRequestValidationError{
			field:  "DeviceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Latitude

	// no validation rules for Longitude

	if m.GetConsentTime() == nil {
		err := InitMandateRequestValidationError{
			field:  "ConsentTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return InitMandateRequestMultiError(errors)
	}

	return nil
}

// InitMandateRequestMultiError is an error wrapping multiple validation errors
// returned by InitMandateRequest.ValidateAll() if the designated constraints
// aren't met.
type InitMandateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitMandateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitMandateRequestMultiError) AllErrors() []error { return m }

// InitMandateRequestValidationError is the validation error returned by
// InitMandateRequest.Validate if the designated constraints aren't met.
type InitMandateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitMandateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitMandateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitMandateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitMandateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitMandateRequestValidationError) ErrorName() string {
	return "InitMandateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e InitMandateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitMandateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitMandateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitMandateRequestValidationError{}

var _InitMandateRequest_MandateType_NotInLookup = map[MandateType]struct{}{
	0: {},
}

// Validate checks the field values on InitMandateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *InitMandateResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on InitMandateResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// InitMandateResponseMultiError, or nil if none found.
func (m *InitMandateResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *InitMandateResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RedirectionUrl

	// no validation rules for TrackingId

	if all {
		switch v := interface{}(m.GetMandateUrlValidity()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, InitMandateResponseValidationError{
					field:  "MandateUrlValidity",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, InitMandateResponseValidationError{
					field:  "MandateUrlValidity",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMandateUrlValidity()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return InitMandateResponseValidationError{
				field:  "MandateUrlValidity",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MandateAmount

	// no validation rules for Umrn

	if len(errors) > 0 {
		return InitMandateResponseMultiError(errors)
	}

	return nil
}

// InitMandateResponseMultiError is an error wrapping multiple validation
// errors returned by InitMandateResponse.ValidateAll() if the designated
// constraints aren't met.
type InitMandateResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m InitMandateResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m InitMandateResponseMultiError) AllErrors() []error { return m }

// InitMandateResponseValidationError is the validation error returned by
// InitMandateResponse.Validate if the designated constraints aren't met.
type InitMandateResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e InitMandateResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e InitMandateResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e InitMandateResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e InitMandateResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e InitMandateResponseValidationError) ErrorName() string {
	return "InitMandateResponseValidationError"
}

// Error satisfies the builtin error interface
func (e InitMandateResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sInitMandateResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = InitMandateResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = InitMandateResponseValidationError{}

// Validate checks the field values on CheckMandateStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckMandateStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckMandateStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckMandateStatusRequestMultiError, or nil if none found.
func (m *CheckMandateStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMandateStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMandateStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMandateStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMandateStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := CheckMandateStatusRequestValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _CheckMandateStatusRequest_MandateType_NotInLookup[m.GetMandateType()]; ok {
		err := CheckMandateStatusRequestValidationError{
			field:  "MandateType",
			reason: "value must not be in list [MANDATE_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTrackingId()) < 1 {
		err := CheckMandateStatusRequestValidationError{
			field:  "TrackingId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return CheckMandateStatusRequestMultiError(errors)
	}

	return nil
}

// CheckMandateStatusRequestMultiError is an error wrapping multiple validation
// errors returned by CheckMandateStatusRequest.ValidateAll() if the
// designated constraints aren't met.
type CheckMandateStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMandateStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMandateStatusRequestMultiError) AllErrors() []error { return m }

// CheckMandateStatusRequestValidationError is the validation error returned by
// CheckMandateStatusRequest.Validate if the designated constraints aren't met.
type CheckMandateStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMandateStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckMandateStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckMandateStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckMandateStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckMandateStatusRequestValidationError) ErrorName() string {
	return "CheckMandateStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMandateStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMandateStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMandateStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMandateStatusRequestValidationError{}

var _CheckMandateStatusRequest_MandateType_NotInLookup = map[MandateType]struct{}{
	0: {},
}

// Validate checks the field values on CheckMandateStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CheckMandateStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CheckMandateStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CheckMandateStatusResponseMultiError, or nil if none found.
func (m *CheckMandateStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CheckMandateStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMandateStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMandateStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMandateStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MandateStatus

	// no validation rules for TrackingId

	if all {
		switch v := interface{}(m.GetCompletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CheckMandateStatusResponseValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CheckMandateStatusResponseValidationError{
					field:  "CompletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CheckMandateStatusResponseValidationError{
				field:  "CompletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CheckMandateStatusResponseMultiError(errors)
	}

	return nil
}

// CheckMandateStatusResponseMultiError is an error wrapping multiple
// validation errors returned by CheckMandateStatusResponse.ValidateAll() if
// the designated constraints aren't met.
type CheckMandateStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CheckMandateStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CheckMandateStatusResponseMultiError) AllErrors() []error { return m }

// CheckMandateStatusResponseValidationError is the validation error returned
// by CheckMandateStatusResponse.Validate if the designated constraints aren't met.
type CheckMandateStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CheckMandateStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CheckMandateStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CheckMandateStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CheckMandateStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CheckMandateStatusResponseValidationError) ErrorName() string {
	return "CheckMandateStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CheckMandateStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCheckMandateStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CheckMandateStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CheckMandateStatusResponseValidationError{}

// Validate checks the field values on GenerateKfsLaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateKfsLaRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateKfsLaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateKfsLaRequestMultiError, or nil if none found.
func (m *GenerateKfsLaRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateKfsLaRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateKfsLaRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateKfsLaRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateKfsLaRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := GenerateKfsLaRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := GenerateKfsLaRequestValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GenerateKfsLaRequestMultiError(errors)
	}

	return nil
}

// GenerateKfsLaRequestMultiError is an error wrapping multiple validation
// errors returned by GenerateKfsLaRequest.ValidateAll() if the designated
// constraints aren't met.
type GenerateKfsLaRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateKfsLaRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateKfsLaRequestMultiError) AllErrors() []error { return m }

// GenerateKfsLaRequestValidationError is the validation error returned by
// GenerateKfsLaRequest.Validate if the designated constraints aren't met.
type GenerateKfsLaRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateKfsLaRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateKfsLaRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateKfsLaRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateKfsLaRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateKfsLaRequestValidationError) ErrorName() string {
	return "GenerateKfsLaRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateKfsLaRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateKfsLaRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateKfsLaRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateKfsLaRequestValidationError{}

// Validate checks the field values on GenerateKfsLaResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GenerateKfsLaResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GenerateKfsLaResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GenerateKfsLaResponseMultiError, or nil if none found.
func (m *GenerateKfsLaResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GenerateKfsLaResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GenerateKfsLaResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GenerateKfsLaResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GenerateKfsLaResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KfsDocUrl

	// no validation rules for LoanAgreementDocUrl

	if len(errors) > 0 {
		return GenerateKfsLaResponseMultiError(errors)
	}

	return nil
}

// GenerateKfsLaResponseMultiError is an error wrapping multiple validation
// errors returned by GenerateKfsLaResponse.ValidateAll() if the designated
// constraints aren't met.
type GenerateKfsLaResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GenerateKfsLaResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GenerateKfsLaResponseMultiError) AllErrors() []error { return m }

// GenerateKfsLaResponseValidationError is the validation error returned by
// GenerateKfsLaResponse.Validate if the designated constraints aren't met.
type GenerateKfsLaResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GenerateKfsLaResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GenerateKfsLaResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GenerateKfsLaResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GenerateKfsLaResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GenerateKfsLaResponseValidationError) ErrorName() string {
	return "GenerateKfsLaResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GenerateKfsLaResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGenerateKfsLaResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GenerateKfsLaResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GenerateKfsLaResponseValidationError{}

// Validate checks the field values on SignKfsLaRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SignKfsLaRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignKfsLaRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SignKfsLaRequestMultiError, or nil if none found.
func (m *SignKfsLaRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SignKfsLaRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SignKfsLaRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SignKfsLaRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SignKfsLaRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := SignKfsLaRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetUserId()) < 1 {
		err := SignKfsLaRequestValidationError{
			field:  "UserId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if ip := net.ParseIP(m.GetUserIp()); ip == nil {
		err := SignKfsLaRequestValidationError{
			field:  "UserIp",
			reason: "value must be a valid IP address",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDeviceId()) < 1 {
		err := SignKfsLaRequestValidationError{
			field:  "DeviceId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Latitude

	// no validation rules for Longitude

	if m.GetConsentTime() == nil {
		err := SignKfsLaRequestValidationError{
			field:  "ConsentTime",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return SignKfsLaRequestMultiError(errors)
	}

	return nil
}

// SignKfsLaRequestMultiError is an error wrapping multiple validation errors
// returned by SignKfsLaRequest.ValidateAll() if the designated constraints
// aren't met.
type SignKfsLaRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignKfsLaRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignKfsLaRequestMultiError) AllErrors() []error { return m }

// SignKfsLaRequestValidationError is the validation error returned by
// SignKfsLaRequest.Validate if the designated constraints aren't met.
type SignKfsLaRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignKfsLaRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignKfsLaRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignKfsLaRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignKfsLaRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignKfsLaRequestValidationError) ErrorName() string { return "SignKfsLaRequestValidationError" }

// Error satisfies the builtin error interface
func (e SignKfsLaRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignKfsLaRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignKfsLaRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignKfsLaRequestValidationError{}

// Validate checks the field values on SignKfsLaResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SignKfsLaResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SignKfsLaResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SignKfsLaResponseMultiError, or nil if none found.
func (m *SignKfsLaResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SignKfsLaResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SignKfsLaResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SignKfsLaResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SignKfsLaResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for KfsDocUrl

	// no validation rules for LoanAgreementDocUrl

	if all {
		switch v := interface{}(m.GetModifyRoiExpirationTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SignKfsLaResponseValidationError{
					field:  "ModifyRoiExpirationTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SignKfsLaResponseValidationError{
					field:  "ModifyRoiExpirationTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetModifyRoiExpirationTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SignKfsLaResponseValidationError{
				field:  "ModifyRoiExpirationTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SignKfsLaResponseMultiError(errors)
	}

	return nil
}

// SignKfsLaResponseMultiError is an error wrapping multiple validation errors
// returned by SignKfsLaResponse.ValidateAll() if the designated constraints
// aren't met.
type SignKfsLaResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SignKfsLaResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SignKfsLaResponseMultiError) AllErrors() []error { return m }

// SignKfsLaResponseValidationError is the validation error returned by
// SignKfsLaResponse.Validate if the designated constraints aren't met.
type SignKfsLaResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SignKfsLaResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SignKfsLaResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SignKfsLaResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SignKfsLaResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SignKfsLaResponseValidationError) ErrorName() string {
	return "SignKfsLaResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SignKfsLaResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSignKfsLaResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SignKfsLaResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SignKfsLaResponseValidationError{}

// Validate checks the field values on GetLoanDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDetailsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDetailsRequestMultiError, or nil if none found.
func (m *GetLoanDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	if len(errors) > 0 {
		return GetLoanDetailsRequestMultiError(errors)
	}

	return nil
}

// GetLoanDetailsRequestMultiError is an error wrapping multiple validation
// errors returned by GetLoanDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsRequestMultiError) AllErrors() []error { return m }

// GetLoanDetailsRequestValidationError is the validation error returned by
// GetLoanDetailsRequest.Validate if the designated constraints aren't met.
type GetLoanDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDetailsRequestValidationError) ErrorName() string {
	return "GetLoanDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsRequestValidationError{}

// Validate checks the field values on GetLoanDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetLoanDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetLoanDetailsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetLoanDetailsResponseMultiError, or nil if none found.
func (m *GetLoanDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetLoanDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanStatus

	if all {
		switch v := interface{}(m.GetLoanDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetLoanDetailsResponseValidationError{
					field:  "LoanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetLoanDetailsResponseValidationError{
					field:  "LoanDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLoanDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetLoanDetailsResponseValidationError{
				field:  "LoanDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetLoanDetailsResponseMultiError(errors)
	}

	return nil
}

// GetLoanDetailsResponseMultiError is an error wrapping multiple validation
// errors returned by GetLoanDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetLoanDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetLoanDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetLoanDetailsResponseMultiError) AllErrors() []error { return m }

// GetLoanDetailsResponseValidationError is the validation error returned by
// GetLoanDetailsResponse.Validate if the designated constraints aren't met.
type GetLoanDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetLoanDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetLoanDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetLoanDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetLoanDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetLoanDetailsResponseValidationError) ErrorName() string {
	return "GetLoanDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetLoanDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetLoanDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetLoanDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetLoanDetailsResponseValidationError{}

// Validate checks the field values on GetPreDisbursementDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPreDisbursementDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPreDisbursementDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetPreDisbursementDetailsRequestMultiError, or nil if none found.
func (m *GetPreDisbursementDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPreDisbursementDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPreDisbursementDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPreDisbursementDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPreDisbursementDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetAmount() == nil {
		err := GetPreDisbursementDetailsRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPreDisbursementDetailsRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPreDisbursementDetailsRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPreDisbursementDetailsRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.GetTenure() <= 0 {
		err := GetPreDisbursementDetailsRequestValidationError{
			field:  "Tenure",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetRateOfInterest() == nil {
		err := GetPreDisbursementDetailsRequestValidationError{
			field:  "RateOfInterest",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetRateOfInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPreDisbursementDetailsRequestValidationError{
					field:  "RateOfInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPreDisbursementDetailsRequestValidationError{
					field:  "RateOfInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRateOfInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPreDisbursementDetailsRequestValidationError{
				field:  "RateOfInterest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPreDisbursementDetailsRequestMultiError(errors)
	}

	return nil
}

// GetPreDisbursementDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetPreDisbursementDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPreDisbursementDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPreDisbursementDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPreDisbursementDetailsRequestMultiError) AllErrors() []error { return m }

// GetPreDisbursementDetailsRequestValidationError is the validation error
// returned by GetPreDisbursementDetailsRequest.Validate if the designated
// constraints aren't met.
type GetPreDisbursementDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPreDisbursementDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPreDisbursementDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPreDisbursementDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPreDisbursementDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPreDisbursementDetailsRequestValidationError) ErrorName() string {
	return "GetPreDisbursementDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPreDisbursementDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPreDisbursementDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPreDisbursementDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPreDisbursementDetailsRequestValidationError{}

// Validate checks the field values on GetPreDisbursementDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetPreDisbursementDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPreDisbursementDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetPreDisbursementDetailsResponseMultiError, or nil if none found.
func (m *GetPreDisbursementDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPreDisbursementDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPreDisbursementDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPreDisbursementDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPreDisbursementDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPreDisbursementDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPreDisbursementDetailsResponseValidationError{
					field:  "PreDisbursementDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPreDisbursementDetailsResponseValidationError{
					field:  "PreDisbursementDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPreDisbursementDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPreDisbursementDetailsResponseValidationError{
				field:  "PreDisbursementDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetPreDisbursementDetailsResponseMultiError(errors)
	}

	return nil
}

// GetPreDisbursementDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetPreDisbursementDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPreDisbursementDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPreDisbursementDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPreDisbursementDetailsResponseMultiError) AllErrors() []error { return m }

// GetPreDisbursementDetailsResponseValidationError is the validation error
// returned by GetPreDisbursementDetailsResponse.Validate if the designated
// constraints aren't met.
type GetPreDisbursementDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPreDisbursementDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPreDisbursementDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPreDisbursementDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPreDisbursementDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPreDisbursementDetailsResponseValidationError) ErrorName() string {
	return "GetPreDisbursementDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPreDisbursementDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPreDisbursementDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPreDisbursementDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPreDisbursementDetailsResponseValidationError{}

// Validate checks the field values on PreDisbursementDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PreDisbursementDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PreDisbursementDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PreDisbursementDetailsMultiError, or nil if none found.
func (m *PreDisbursementDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PreDisbursementDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetProcessingFee()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "ProcessingFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "ProcessingFee",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProcessingFee()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreDisbursementDetailsValidationError{
				field:  "ProcessingFee",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisbursalAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "DisbursalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "DisbursalAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisbursalAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreDisbursementDetailsValidationError{
				field:  "DisbursalAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "TotalInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "TotalInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreDisbursementDetailsValidationError{
				field:  "TotalInterest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalRepaymentAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "TotalRepaymentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "TotalRepaymentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalRepaymentAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreDisbursementDetailsValidationError{
				field:  "TotalRepaymentAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInstallmentAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "InstallmentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "InstallmentAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInstallmentAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreDisbursementDetailsValidationError{
				field:  "InstallmentAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFirstInstallmentDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "FirstInstallmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "FirstInstallmentDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFirstInstallmentDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreDisbursementDetailsValidationError{
				field:  "FirstInstallmentDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Apr

	if all {
		switch v := interface{}(m.GetGapInterest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "GapInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PreDisbursementDetailsValidationError{
					field:  "GapInterest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGapInterest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PreDisbursementDetailsValidationError{
				field:  "GapInterest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PreDisbursementDetailsMultiError(errors)
	}

	return nil
}

// PreDisbursementDetailsMultiError is an error wrapping multiple validation
// errors returned by PreDisbursementDetails.ValidateAll() if the designated
// constraints aren't met.
type PreDisbursementDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PreDisbursementDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PreDisbursementDetailsMultiError) AllErrors() []error { return m }

// PreDisbursementDetailsValidationError is the validation error returned by
// PreDisbursementDetails.Validate if the designated constraints aren't met.
type PreDisbursementDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PreDisbursementDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PreDisbursementDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PreDisbursementDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PreDisbursementDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PreDisbursementDetailsValidationError) ErrorName() string {
	return "PreDisbursementDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e PreDisbursementDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPreDisbursementDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PreDisbursementDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PreDisbursementDetailsValidationError{}

// Validate checks the field values on PostExternalDataRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostExternalDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostExternalDataRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostExternalDataRequestMultiError, or nil if none found.
func (m *PostExternalDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PostExternalDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostExternalDataRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostExternalDataRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostExternalDataRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	// no validation rules for UserId

	// no validation rules for Data

	if all {
		switch v := interface{}(m.GetBankDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostExternalDataRequestValidationError{
					field:  "BankDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostExternalDataRequestValidationError{
					field:  "BankDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBankDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostExternalDataRequestValidationError{
				field:  "BankDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	if len(errors) > 0 {
		return PostExternalDataRequestMultiError(errors)
	}

	return nil
}

// PostExternalDataRequestMultiError is an error wrapping multiple validation
// errors returned by PostExternalDataRequest.ValidateAll() if the designated
// constraints aren't met.
type PostExternalDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostExternalDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostExternalDataRequestMultiError) AllErrors() []error { return m }

// PostExternalDataRequestValidationError is the validation error returned by
// PostExternalDataRequest.Validate if the designated constraints aren't met.
type PostExternalDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostExternalDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostExternalDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostExternalDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostExternalDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostExternalDataRequestValidationError) ErrorName() string {
	return "PostExternalDataRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PostExternalDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostExternalDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostExternalDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostExternalDataRequestValidationError{}

// Validate checks the field values on BankDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BankDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BankDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BankDetailsMultiError, or
// nil if none found.
func (m *BankDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *BankDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for HolderName

	// no validation rules for AccountNumber

	// no validation rules for Ifsc

	// no validation rules for Type

	if len(errors) > 0 {
		return BankDetailsMultiError(errors)
	}

	return nil
}

// BankDetailsMultiError is an error wrapping multiple validation errors
// returned by BankDetails.ValidateAll() if the designated constraints aren't met.
type BankDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BankDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BankDetailsMultiError) AllErrors() []error { return m }

// BankDetailsValidationError is the validation error returned by
// BankDetails.Validate if the designated constraints aren't met.
type BankDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BankDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BankDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BankDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BankDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BankDetailsValidationError) ErrorName() string { return "BankDetailsValidationError" }

// Error satisfies the builtin error interface
func (e BankDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBankDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BankDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BankDetailsValidationError{}

// Validate checks the field values on PostExternalDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PostExternalDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PostExternalDataResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PostExternalDataResponseMultiError, or nil if none found.
func (m *PostExternalDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PostExternalDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PostExternalDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PostExternalDataResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PostExternalDataResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PostExternalDataResponseMultiError(errors)
	}

	return nil
}

// PostExternalDataResponseMultiError is an error wrapping multiple validation
// errors returned by PostExternalDataResponse.ValidateAll() if the designated
// constraints aren't met.
type PostExternalDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PostExternalDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PostExternalDataResponseMultiError) AllErrors() []error { return m }

// PostExternalDataResponseValidationError is the validation error returned by
// PostExternalDataResponse.Validate if the designated constraints aren't met.
type PostExternalDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PostExternalDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PostExternalDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PostExternalDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PostExternalDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PostExternalDataResponseValidationError) ErrorName() string {
	return "PostExternalDataResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PostExternalDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPostExternalDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PostExternalDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PostExternalDataResponseValidationError{}

// Validate checks the field values on GetAmortizationScheduleRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAmortizationScheduleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmortizationScheduleRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAmortizationScheduleRequestMultiError, or nil if none found.
func (m *GetAmortizationScheduleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmortizationScheduleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmortizationScheduleRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmortizationScheduleRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmortizationScheduleRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	if len(errors) > 0 {
		return GetAmortizationScheduleRequestMultiError(errors)
	}

	return nil
}

// GetAmortizationScheduleRequestMultiError is an error wrapping multiple
// validation errors returned by GetAmortizationScheduleRequest.ValidateAll()
// if the designated constraints aren't met.
type GetAmortizationScheduleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmortizationScheduleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmortizationScheduleRequestMultiError) AllErrors() []error { return m }

// GetAmortizationScheduleRequestValidationError is the validation error
// returned by GetAmortizationScheduleRequest.Validate if the designated
// constraints aren't met.
type GetAmortizationScheduleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmortizationScheduleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmortizationScheduleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmortizationScheduleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmortizationScheduleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmortizationScheduleRequestValidationError) ErrorName() string {
	return "GetAmortizationScheduleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmortizationScheduleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmortizationScheduleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmortizationScheduleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmortizationScheduleRequestValidationError{}

// Validate checks the field values on AmortizationAmountBreakupComponent with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *AmortizationAmountBreakupComponent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AmortizationAmountBreakupComponent
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// AmortizationAmountBreakupComponentMultiError, or nil if none found.
func (m *AmortizationAmountBreakupComponent) ValidateAll() error {
	return m.validate(true)
}

func (m *AmortizationAmountBreakupComponent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Purpose

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AmortizationAmountBreakupComponentValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AmortizationAmountBreakupComponentValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AmortizationAmountBreakupComponentValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AmortizationAmountBreakupComponentMultiError(errors)
	}

	return nil
}

// AmortizationAmountBreakupComponentMultiError is an error wrapping multiple
// validation errors returned by
// AmortizationAmountBreakupComponent.ValidateAll() if the designated
// constraints aren't met.
type AmortizationAmountBreakupComponentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AmortizationAmountBreakupComponentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AmortizationAmountBreakupComponentMultiError) AllErrors() []error { return m }

// AmortizationAmountBreakupComponentValidationError is the validation error
// returned by AmortizationAmountBreakupComponent.Validate if the designated
// constraints aren't met.
type AmortizationAmountBreakupComponentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AmortizationAmountBreakupComponentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AmortizationAmountBreakupComponentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AmortizationAmountBreakupComponentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AmortizationAmountBreakupComponentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AmortizationAmountBreakupComponentValidationError) ErrorName() string {
	return "AmortizationAmountBreakupComponentValidationError"
}

// Error satisfies the builtin error interface
func (e AmortizationAmountBreakupComponentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAmortizationAmountBreakupComponent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AmortizationAmountBreakupComponentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AmortizationAmountBreakupComponentValidationError{}

// Validate checks the field values on AmortizationScheduleItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AmortizationScheduleItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AmortizationScheduleItem with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AmortizationScheduleItemMultiError, or nil if none found.
func (m *AmortizationScheduleItem) ValidateAll() error {
	return m.validate(true)
}

func (m *AmortizationScheduleItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetDueDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AmortizationScheduleItemValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AmortizationScheduleItemValidationError{
					field:  "DueDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AmortizationScheduleItemValidationError{
				field:  "DueDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDueAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AmortizationScheduleItemValidationError{
					field:  "DueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AmortizationScheduleItemValidationError{
					field:  "DueAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDueAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AmortizationScheduleItemValidationError{
				field:  "DueAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBreakup() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AmortizationScheduleItemValidationError{
						field:  fmt.Sprintf("Breakup[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AmortizationScheduleItemValidationError{
						field:  fmt.Sprintf("Breakup[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AmortizationScheduleItemValidationError{
					field:  fmt.Sprintf("Breakup[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Status

	if all {
		switch v := interface{}(m.GetOutstandingAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AmortizationScheduleItemValidationError{
					field:  "OutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AmortizationScheduleItemValidationError{
					field:  "OutstandingAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOutstandingAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AmortizationScheduleItemValidationError{
				field:  "OutstandingAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AmortizationScheduleItemMultiError(errors)
	}

	return nil
}

// AmortizationScheduleItemMultiError is an error wrapping multiple validation
// errors returned by AmortizationScheduleItem.ValidateAll() if the designated
// constraints aren't met.
type AmortizationScheduleItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AmortizationScheduleItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AmortizationScheduleItemMultiError) AllErrors() []error { return m }

// AmortizationScheduleItemValidationError is the validation error returned by
// AmortizationScheduleItem.Validate if the designated constraints aren't met.
type AmortizationScheduleItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AmortizationScheduleItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AmortizationScheduleItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AmortizationScheduleItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AmortizationScheduleItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AmortizationScheduleItemValidationError) ErrorName() string {
	return "AmortizationScheduleItemValidationError"
}

// Error satisfies the builtin error interface
func (e AmortizationScheduleItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAmortizationScheduleItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AmortizationScheduleItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AmortizationScheduleItemValidationError{}

// Validate checks the field values on GetAmortizationScheduleResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetAmortizationScheduleResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAmortizationScheduleResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetAmortizationScheduleResponseMultiError, or nil if none found.
func (m *GetAmortizationScheduleResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAmortizationScheduleResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAmortizationScheduleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAmortizationScheduleResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAmortizationScheduleResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetAmortizationSchedule() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAmortizationScheduleResponseValidationError{
						field:  fmt.Sprintf("AmortizationSchedule[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAmortizationScheduleResponseValidationError{
						field:  fmt.Sprintf("AmortizationSchedule[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAmortizationScheduleResponseValidationError{
					field:  fmt.Sprintf("AmortizationSchedule[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetAmortizationScheduleResponseMultiError(errors)
	}

	return nil
}

// GetAmortizationScheduleResponseMultiError is an error wrapping multiple
// validation errors returned by GetAmortizationScheduleResponse.ValidateAll()
// if the designated constraints aren't met.
type GetAmortizationScheduleResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAmortizationScheduleResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAmortizationScheduleResponseMultiError) AllErrors() []error { return m }

// GetAmortizationScheduleResponseValidationError is the validation error
// returned by GetAmortizationScheduleResponse.Validate if the designated
// constraints aren't met.
type GetAmortizationScheduleResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAmortizationScheduleResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAmortizationScheduleResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAmortizationScheduleResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAmortizationScheduleResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAmortizationScheduleResponseValidationError) ErrorName() string {
	return "GetAmortizationScheduleResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAmortizationScheduleResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAmortizationScheduleResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAmortizationScheduleResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAmortizationScheduleResponseValidationError{}

// Validate checks the field values on GetForeclosureDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForeclosureDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForeclosureDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetForeclosureDetailsRequestMultiError, or nil if none found.
func (m *GetForeclosureDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForeclosureDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for LoanId

	// no validation rules for Purpose

	if len(errors) > 0 {
		return GetForeclosureDetailsRequestMultiError(errors)
	}

	return nil
}

// GetForeclosureDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by GetForeclosureDetailsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetForeclosureDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForeclosureDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForeclosureDetailsRequestMultiError) AllErrors() []error { return m }

// GetForeclosureDetailsRequestValidationError is the validation error returned
// by GetForeclosureDetailsRequest.Validate if the designated constraints
// aren't met.
type GetForeclosureDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForeclosureDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForeclosureDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForeclosureDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForeclosureDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForeclosureDetailsRequestValidationError) ErrorName() string {
	return "GetForeclosureDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetForeclosureDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForeclosureDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForeclosureDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForeclosureDetailsRequestValidationError{}

// Validate checks the field values on GetForeclosureDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetForeclosureDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetForeclosureDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetForeclosureDetailsResponseMultiError, or nil if none found.
func (m *GetForeclosureDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetForeclosureDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetForeclosureDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "ForeclosureDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetForeclosureDetailsResponseValidationError{
					field:  "ForeclosureDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForeclosureDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetForeclosureDetailsResponseValidationError{
				field:  "ForeclosureDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetForeclosureDetailsResponseMultiError(errors)
	}

	return nil
}

// GetForeclosureDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetForeclosureDetailsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetForeclosureDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetForeclosureDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetForeclosureDetailsResponseMultiError) AllErrors() []error { return m }

// GetForeclosureDetailsResponseValidationError is the validation error
// returned by GetForeclosureDetailsResponse.Validate if the designated
// constraints aren't met.
type GetForeclosureDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetForeclosureDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetForeclosureDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetForeclosureDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetForeclosureDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetForeclosureDetailsResponseValidationError) ErrorName() string {
	return "GetForeclosureDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetForeclosureDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetForeclosureDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetForeclosureDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetForeclosureDetailsResponseValidationError{}

// Validate checks the field values on ForeclosureDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForeclosureDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForeclosureDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForeclosureDetailsMultiError, or nil if none found.
func (m *ForeclosureDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ForeclosureDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPrincipalOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "PrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "PrincipalOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPrincipalOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "PrincipalOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInterestOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "InterestOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "InterestOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInterestOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "InterestOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDelayChargesOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "DelayChargesOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "DelayChargesOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDelayChargesOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "DelayChargesOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLateFeeOutstanding()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "LateFeeOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "LateFeeOutstanding",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLateFeeOutstanding()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "LateFeeOutstanding",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetForeclosureAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "ForeclosureAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "ForeclosureAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForeclosureAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "ForeclosureAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetForeclosureCharges()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "ForeclosureCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "ForeclosureCharges",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForeclosureCharges()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "ForeclosureCharges",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRepaymentReceived()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "RepaymentReceived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForeclosureDetailsValidationError{
					field:  "RepaymentReceived",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRepaymentReceived()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForeclosureDetailsValidationError{
				field:  "RepaymentReceived",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ForeclosureDetailsMultiError(errors)
	}

	return nil
}

// ForeclosureDetailsMultiError is an error wrapping multiple validation errors
// returned by ForeclosureDetails.ValidateAll() if the designated constraints
// aren't met.
type ForeclosureDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForeclosureDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForeclosureDetailsMultiError) AllErrors() []error { return m }

// ForeclosureDetailsValidationError is the validation error returned by
// ForeclosureDetails.Validate if the designated constraints aren't met.
type ForeclosureDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForeclosureDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForeclosureDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForeclosureDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForeclosureDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForeclosureDetailsValidationError) ErrorName() string {
	return "ForeclosureDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e ForeclosureDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForeclosureDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForeclosureDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForeclosureDetailsValidationError{}

// Validate checks the field values on GeneratePaymentLinkRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GeneratePaymentLinkRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GeneratePaymentLinkRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GeneratePaymentLinkRequestMultiError, or nil if none found.
func (m *GeneratePaymentLinkRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GeneratePaymentLinkRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GeneratePaymentLinkRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GeneratePaymentLinkRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GeneratePaymentLinkRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetLoanId()) < 1 {
		err := GeneratePaymentLinkRequestValidationError{
			field:  "LoanId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetAmount() == nil {
		err := GeneratePaymentLinkRequestValidationError{
			field:  "Amount",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GeneratePaymentLinkRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GeneratePaymentLinkRequestValidationError{
					field:  "Amount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GeneratePaymentLinkRequestValidationError{
				field:  "Amount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GeneratePaymentLinkRequestMultiError(errors)
	}

	return nil
}

// GeneratePaymentLinkRequestMultiError is an error wrapping multiple
// validation errors returned by GeneratePaymentLinkRequest.ValidateAll() if
// the designated constraints aren't met.
type GeneratePaymentLinkRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GeneratePaymentLinkRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GeneratePaymentLinkRequestMultiError) AllErrors() []error { return m }

// GeneratePaymentLinkRequestValidationError is the validation error returned
// by GeneratePaymentLinkRequest.Validate if the designated constraints aren't met.
type GeneratePaymentLinkRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GeneratePaymentLinkRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GeneratePaymentLinkRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GeneratePaymentLinkRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GeneratePaymentLinkRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GeneratePaymentLinkRequestValidationError) ErrorName() string {
	return "GeneratePaymentLinkRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GeneratePaymentLinkRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGeneratePaymentLinkRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GeneratePaymentLinkRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GeneratePaymentLinkRequestValidationError{}

// Validate checks the field values on GeneratePaymentLinkResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GeneratePaymentLinkResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GeneratePaymentLinkResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GeneratePaymentLinkResponseMultiError, or nil if none found.
func (m *GeneratePaymentLinkResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GeneratePaymentLinkResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GeneratePaymentLinkResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GeneratePaymentLinkResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GeneratePaymentLinkResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Url

	// no validation rules for OrderId

	if len(errors) > 0 {
		return GeneratePaymentLinkResponseMultiError(errors)
	}

	return nil
}

// GeneratePaymentLinkResponseMultiError is an error wrapping multiple
// validation errors returned by GeneratePaymentLinkResponse.ValidateAll() if
// the designated constraints aren't met.
type GeneratePaymentLinkResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GeneratePaymentLinkResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GeneratePaymentLinkResponseMultiError) AllErrors() []error { return m }

// GeneratePaymentLinkResponseValidationError is the validation error returned
// by GeneratePaymentLinkResponse.Validate if the designated constraints
// aren't met.
type GeneratePaymentLinkResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GeneratePaymentLinkResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GeneratePaymentLinkResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GeneratePaymentLinkResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GeneratePaymentLinkResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GeneratePaymentLinkResponseValidationError) ErrorName() string {
	return "GeneratePaymentLinkResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GeneratePaymentLinkResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGeneratePaymentLinkResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GeneratePaymentLinkResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GeneratePaymentLinkResponseValidationError{}

// Validate checks the field values on GetPaymentStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPaymentStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPaymentStatusRequestMultiError, or nil if none found.
func (m *GetPaymentStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentStatusRequestValidationError{
					field:  "Header",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentStatusRequestValidationError{
				field:  "Header",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetOrderId()) < 1 {
		err := GetPaymentStatusRequestValidationError{
			field:  "OrderId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetPaymentStatusRequestMultiError(errors)
	}

	return nil
}

// GetPaymentStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetPaymentStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetPaymentStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentStatusRequestMultiError) AllErrors() []error { return m }

// GetPaymentStatusRequestValidationError is the validation error returned by
// GetPaymentStatusRequest.Validate if the designated constraints aren't met.
type GetPaymentStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentStatusRequestValidationError) ErrorName() string {
	return "GetPaymentStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentStatusRequestValidationError{}

// Validate checks the field values on GetPaymentStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetPaymentStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetPaymentStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetPaymentStatusResponseMultiError, or nil if none found.
func (m *GetPaymentStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetPaymentStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetPaymentStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetPaymentStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetPaymentStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PaymentStatus

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return GetPaymentStatusResponseMultiError(errors)
	}

	return nil
}

// GetPaymentStatusResponseMultiError is an error wrapping multiple validation
// errors returned by GetPaymentStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type GetPaymentStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetPaymentStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetPaymentStatusResponseMultiError) AllErrors() []error { return m }

// GetPaymentStatusResponseValidationError is the validation error returned by
// GetPaymentStatusResponse.Validate if the designated constraints aren't met.
type GetPaymentStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetPaymentStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetPaymentStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetPaymentStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetPaymentStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetPaymentStatusResponseValidationError) ErrorName() string {
	return "GetPaymentStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetPaymentStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetPaymentStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetPaymentStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetPaymentStatusResponseValidationError{}

// Validate checks the field values on ApplyForLoanRequest_Interest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ApplyForLoanRequest_Interest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ApplyForLoanRequest_Interest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ApplyForLoanRequest_InterestMultiError, or nil if none found.
func (m *ApplyForLoanRequest_Interest) ValidateAll() error {
	return m.validate(true)
}

func (m *ApplyForLoanRequest_Interest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := _ApplyForLoanRequest_Interest_Type_NotInLookup[m.GetType()]; ok {
		err := ApplyForLoanRequest_InterestValidationError{
			field:  "Type",
			reason: "value must not be in list [INTEREST_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := _ApplyForLoanRequest_Interest_Frequency_NotInLookup[m.GetFrequency()]; ok {
		err := ApplyForLoanRequest_InterestValidationError{
			field:  "Frequency",
			reason: "value must not be in list [INTEREST_FREQUENCY_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetValue() <= 0 {
		err := ApplyForLoanRequest_InterestValidationError{
			field:  "Value",
			reason: "value must be greater than 0",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ApplyForLoanRequest_InterestMultiError(errors)
	}

	return nil
}

// ApplyForLoanRequest_InterestMultiError is an error wrapping multiple
// validation errors returned by ApplyForLoanRequest_Interest.ValidateAll() if
// the designated constraints aren't met.
type ApplyForLoanRequest_InterestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ApplyForLoanRequest_InterestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ApplyForLoanRequest_InterestMultiError) AllErrors() []error { return m }

// ApplyForLoanRequest_InterestValidationError is the validation error returned
// by ApplyForLoanRequest_Interest.Validate if the designated constraints
// aren't met.
type ApplyForLoanRequest_InterestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ApplyForLoanRequest_InterestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ApplyForLoanRequest_InterestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ApplyForLoanRequest_InterestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ApplyForLoanRequest_InterestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ApplyForLoanRequest_InterestValidationError) ErrorName() string {
	return "ApplyForLoanRequest_InterestValidationError"
}

// Error satisfies the builtin error interface
func (e ApplyForLoanRequest_InterestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sApplyForLoanRequest_Interest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ApplyForLoanRequest_InterestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ApplyForLoanRequest_InterestValidationError{}

var _ApplyForLoanRequest_Interest_Type_NotInLookup = map[InterestType]struct{}{
	0: {},
}

var _ApplyForLoanRequest_Interest_Frequency_NotInLookup = map[InterestFrequency]struct{}{
	0: {},
}

// Validate checks the field values on OfferData_ApplicableAmountToTenure with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *OfferData_ApplicableAmountToTenure) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OfferData_ApplicableAmountToTenure
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// OfferData_ApplicableAmountToTenureMultiError, or nil if none found.
func (m *OfferData_ApplicableAmountToTenure) ValidateAll() error {
	return m.validate(true)
}

func (m *OfferData_ApplicableAmountToTenure) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMinAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferData_ApplicableAmountToTenureValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferData_ApplicableAmountToTenureValidationError{
					field:  "MinAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMinAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferData_ApplicableAmountToTenureValidationError{
				field:  "MinAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetMaxAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, OfferData_ApplicableAmountToTenureValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, OfferData_ApplicableAmountToTenureValidationError{
					field:  "MaxAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMaxAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return OfferData_ApplicableAmountToTenureValidationError{
				field:  "MaxAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MinTenure

	// no validation rules for MaxTenure

	if len(errors) > 0 {
		return OfferData_ApplicableAmountToTenureMultiError(errors)
	}

	return nil
}

// OfferData_ApplicableAmountToTenureMultiError is an error wrapping multiple
// validation errors returned by
// OfferData_ApplicableAmountToTenure.ValidateAll() if the designated
// constraints aren't met.
type OfferData_ApplicableAmountToTenureMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OfferData_ApplicableAmountToTenureMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OfferData_ApplicableAmountToTenureMultiError) AllErrors() []error { return m }

// OfferData_ApplicableAmountToTenureValidationError is the validation error
// returned by OfferData_ApplicableAmountToTenure.Validate if the designated
// constraints aren't met.
type OfferData_ApplicableAmountToTenureValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OfferData_ApplicableAmountToTenureValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OfferData_ApplicableAmountToTenureValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OfferData_ApplicableAmountToTenureValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OfferData_ApplicableAmountToTenureValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OfferData_ApplicableAmountToTenureValidationError) ErrorName() string {
	return "OfferData_ApplicableAmountToTenureValidationError"
}

// Error satisfies the builtin error interface
func (e OfferData_ApplicableAmountToTenureValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOfferData_ApplicableAmountToTenure.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OfferData_ApplicableAmountToTenureValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OfferData_ApplicableAmountToTenureValidationError{}
