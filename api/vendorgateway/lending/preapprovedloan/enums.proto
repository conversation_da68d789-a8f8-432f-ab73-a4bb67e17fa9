// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=LoanState,ApiType,KfsOptions_Field_Name
syntax = "proto3";

package vendorgateway.lending.preapprovedloan;

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.preapprovedloan";

// ENUM for defining loan state types
enum LoanState {
  LOAN_STATE_TYPE_UNSPECIFIED = 0;
  LOAN_STATE_APPROVED_PENDING_DISBURSAL = 1;
  LOAN_STATE_DISBURSED = 2;
  LOAN_STATE_REPAYMENT = 3;
  LOAN_STATE_CLOSED = 4;
  LOAN_STATE_FAILED = 5;
  LOAN_STATE_SUSPECT = 6;
}

enum ApiType {
  API_TYPE_UNSPECIFIED = 0;
  API_TYPE_LOAN_PROCESS = 1;
  API_TYPE_LOAN_UNBLOCK = 2;
}

enum KfsOptions_Field_Name {
  FIELD_NAME = 0;
  FIELD_ADDRESS = 1;
  FIELD_EMAIL = 2;
  FIELD_MOBILE = 3;
  FIELD_AMOUNT = 4;
  FIELD_AMOUNT_IN_WORDS = 5;
  FIELD_PERIOD = 6;
  FIELD_ROI = 7;
  FIELD_EMI = 8;
  FIELD_DATE = 9;
  FIELD_PF_AMOUNT = 10;
  FIELD_BPI = 11;
  FIELD_AGE = 12;
  FIELD_FATHER_HUSBAND_NAME = 13;
  FIELD_FIRST_EMI_DATE = 14;
  FIELD_ACCOUNT_NUMBER = 15;
  FIELD_IFSC = 16;
  FIELD_APR = 17;
  FIELD_PF_RATE = 18;
  FIELD_EXPIRY = 19;
  TOTAL_AMOUNT = 20;
  NET_LOAN_AMOUNT = 21;
  INTEREST_AMOUNT = 22;
  FIELD_LENTRA_REF_ID = 23;
  FIELD_SANCTION_AMOUNT = 24;
  FIELD_TENURE = 25;
  FIELD_EMI_NUMBER = 26;
  FIELD_EMI_AMOUNT = 27;
  FIELD_FIRST_EMI = 28;
  FIELD_PROCESSING_FEE = 29;
  FIELD_GST = 30;
  FIELD_RATE_OF_INTEREST = 31;
  FIELD_NET_LOAN_AMOUNT = 32;
  FIELD_LOAN_AMOUNT_TO_BE_PAID_BY_BOROWWER = 33;
  FIELD_RATE_OF_INTEREST_CAPS = 34;
}

enum Qualification {
  QUALIFICATION_UNSPECIFIED = 0;
  QUALIFICATION_UNDER_GRADUATE = 1;
  QUALIFICATION_GRADUATE = 2;
}

enum Application_Type {
  APPLICATION_TYPE_UNSPECIFIED = 0;
  APPLICATION_TYPE_ETB = 1;
  APPLICATION_TYPE_NTB = 2;
}

enum IncomeDataSource {
  INCOME_DATA_SOURCE_UNSPECIFIED = 0;
  // account aggregator txns data
  INCOME_DATA_SOURCE_AA = 1;
  INCOME_DATA_SOURCE_ITR_INTIMATION = 2;
}

enum CredentialCategory {
  CREDENTIAL_CATEGORY_UNSPECIFIED = 0;
  // this is the identifier for Fed NTB flow, If we get this value in request we need to use Fed NTB flow credentials
  CREDENTIAL_CATEGORY_NTB = 1;
}
