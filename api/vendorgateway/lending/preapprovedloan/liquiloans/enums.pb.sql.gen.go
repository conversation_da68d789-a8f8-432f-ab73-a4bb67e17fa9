// Code generated by gen_sql.go. DO NOT EDIT.
// File generated using : go generate /src/github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans/enums.pb.go

package liquiloans

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
)

// Valuer interface implementation for storing the Status in string format in DB
func (p Status) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing Status while reading from DB
func (p *Status) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := Status_value[val]
	if !ok {
		return fmt.Errorf("unexpected Status value: %s", val)
	}
	*p = Status(valInt)
	return nil
}

// Marshaler interface implementation for Status
func (x Status) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for Status
func (x *Status) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = Status(Status_value[val])
	return nil
}

// Valuer interface implementation for storing the PaymentStatus in string format in DB
func (p PaymentStatus) Value() (driver.Value, error) {
	return p.String(), nil
}

// Scanner interface implementation for parsing PaymentStatus while reading from DB
func (p *PaymentStatus) Scan(value interface{}) error {
	val, ok := value.(string)
	if !ok {
		return fmt.Errorf("type conversion to string failed, src: %T", val)
	}

	valInt, ok := PaymentStatus_value[val]
	if !ok {
		return fmt.Errorf("unexpected PaymentStatus value: %s", val)
	}
	*p = PaymentStatus(valInt)
	return nil
}

// Marshaler interface implementation for PaymentStatus
func (x PaymentStatus) MarshalJSON() ([]byte, error) {
	return json.Marshal(x.String())
}

// Unmarshaler interface implementation for PaymentStatus
func (x *PaymentStatus) UnmarshalJSON(b []byte) error {
	var val string
	err := json.Unmarshal(b, &val)
	if err != nil {
		return err
	}
	*x = PaymentStatus(PaymentStatus_value[val])
	return nil
}
